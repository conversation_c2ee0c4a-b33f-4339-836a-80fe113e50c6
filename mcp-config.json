{"mcpServers": {"postgres-v5-client": {"command": "npx", "args": ["@modelcontextprotocol/server-postgres", "postgresql://crmroot:<EMAIL>:5432/v5_client"]}, "postgres-pg-admin": {"command": "npx", "args": ["@modelcontextprotocol/server-postgres", "postgresql://crmroot:<EMAIL>:5432/pg_admin"]}, "postgres-common-vector": {"command": "npx", "args": ["@modelcontextprotocol/server-postgres", "postgresql://crmphp:<EMAIL>:5432/common_vector"]}, "mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "timeout": 600, "autoApprove": ["interactive_feedback"]}}}