kind: ConfigMap
apiVersion: v1
metadata:
  name: sidecar-grey-configmap
  namespace: app-crm

data:
  config.yaml: |-
    service:
      name: pod_agent
      env: prod
    server:
      rpc:
        network: unix
        addr: /var/run/sidecar/agent.sock
        timeout: 30s
    log:                                # 日志配置
      level: info                       # 日志级别
      output:
        - stdout
        - ./logs/pod_agent.log
    plugins:
      sentinel:                         # sentinel-熔断限流
        ahas_namespace: "grey"
        ahas_license: "049549e09a4141c5bb4a87f207533bf4"
      nacos:
        namespace: "prod"
        group_id: "grey"
      collector:                       # 采集PHP主动上报的日志
        enable_collect_fpm: true       # 是否开启fpm状态采集
        fpm_collect_interval: 60s       # fpm状态采集周期
        tdengine_db:
          driver: taosSql
          source: root:taosdata/tcp(172.21.125.160:6030)/php_request_info
          max_idle: 4
          max_open: 8
          conn_life_time: 3600s
        tdengine_v3_db:
          driver: taosSql
          source: root:taosdata/tcp(172.23.2.191:6030)/php_request_info
          max_idle: 4
          max_open: 8
          conn_life_time: 3600s
        prometheus_db:
          driver: mysql
          source: crmphp:ou5pxhJTRxvqWcyr8cYBMXKqGMZ6eg@tcp(crm-prometheus.mysql.rds.aliyuncs.com:3306)/prometheus
          max_idle: 4
          max_open: 16
          conn_life_time: 3600s
        trace_config:
          trace_exporter_endpoint: opentelemetry-collector.vpcslb.com:4317
          trace_exporter_endpoint_insecure: true
          service_name: php-crm
          service_namespace: prod
          service_version: v1.0
