apiVersion: apps/v1
kind: Deployment
metadata:
  name: ads-server-grey
  namespace: app-crm
  labels:
    app: ads-server-grey
    developer: PHP-CRM
    lang: php
spec:
  selector:
    matchLabels:
      app: ads-server-grey
  replicas: 1
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        service: ads-server
        app_env: grey
        sidecar-injection: enabled
        app: ads-server-grey
      annotations:
        sidecarConfigName: crm-fpm-grey
    spec:
      nodeSelector:
        xiaoman.cn/server-type: php-server
      initContainers:
        - command:
            - /bin/sh
            - '-c'
            - |
              mount -o remount rw /proc/sys
              sysctl -w net.ipv4.ip_local_port_range="10240 65535"
              sysctl -w net.ipv4.tcp_max_tw_buckets=100000 
              sysctl -w fs.inotify.max_user_watches=8192000
              sysctl -w net.ipv4.tcp_fin_timeout=10
              sysctl -w net.ipv4.tcp_tw_reuse=1
          image: 'registry-vpc.cn-hangzhou.aliyuncs.com/acs/busybox:v1.29.2'
          imagePullPolicy: IfNotPresent
          name: init-sysctl
          resources: { }
          securityContext:
            capabilities:
              add:
                - SYS_ADMIN
              drop:
                - ALL
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      containers:
        - name: ads-server-grey
          #imagePullPolicy: IfNotPresent
          imagePullPolicy: Always
          image: 'xiaoman-registry-vpc.cn-hangzhou.cr.aliyuncs.com/xiaoman-php/crm:k8s-prod-hotfix-20240506-v2'
          command: [ 'sh', '-c', '/usr/local/php/sbin/php-fpm' ]
          lifecycle:
            preStop:
              exec:
                command: ['/bin/sh','-c','/bin/sleep 8; kill -QUIT 1 ;/bin/sleep 5']
          env:
            - name: RUNTIME_ENV
              value: 'k8s'
            - name: aliyun_logs_crm-service
              value: stdout
            - name: aliyun_logs_crm-service_tags
              value: app=crm
            - name: aliyun_logs_crm-service_tags
              value: env=grey
            - name: aliyun_logs_php-error
              value: /data/logs/php_errors.log
            - name: aliyun_logs_php-error_tags
              value: app=crm
            - name: aliyun_logs_fpm-slowlog
              value: /data/logs/php-fpm_slow.log
            - name: aliyun_logs_fpm-slowlog_tags
              value: app=crm
            - name: aliyun_logs_fpm-log
              value: /data/logs/php-fpm.log
            - name: aliyun_logs_fpm-log_tags
              value: app=crm
            - name: aliyun_logs_opcahce-error-log
              value: /data/logs/php_opcache_error.log
            - name: aliyun_logs_opcahce-error-log_tags
              value: app=crm
            # 需要跟着容器配置来, 具体看README
            - name: FPM.pm.max_children
              value: '64'
            - name: FPM.pm.start_servers
              value: '16'
            - name: FPM.pm.min_spare_servers
              value: '16'
            - name: FPM.pm.max_spare_servers
              value: '32'
            - name: PHP.opcache.huge_code_pages
              value: '0'
            - name: CLI.opcache.huge_code_pages
              value: '0'
            - name: enable_pod_agent
              value: '1'
            - name: SIDECAR_ENABLE
              value: '1'
            - name: RECORD_SINGLE_DB
              value: '1'
            - name: DISABLE_SINGLE_DB_CACHE
              value: '0'
          volumeMounts:
            - name: volume-log
              mountPath: /data/logs
          resources:
            requests:
              cpu: "2"
              memory: "4Gi"
            limits:
              cpu: "4"
              memory: "8Gi"
          ports:
            - name: fastcgi
              containerPort: 9000
          readinessProbe:
            exec:
              command: [ 'sh', '-c', 'ps -ef |grep php-fpm' ]
            initialDelaySeconds: 5
            periodSeconds: 10
          livenessProbe:
            initialDelaySeconds: 10
            periodSeconds: 60
            exec:
              command: [ 'sh', '-c', 'ps -ef |grep php-fpm' ]
          securityContext:
            privileged: true
            capabilities:
              add: [ "SYS_ADMIN" ,"SYS_PTRACE"]
            allowPrivilegeEscalation: true
        - name: ads-server-nginx-grey
          image: "xiaoman-registry-vpc.cn-hangzhou.cr.aliyuncs.com/xiaoman-php/nginx:latest"
          lifecycle:
            postStart:
              exec:
                command: ['/bin/sh','-c','for i in {1..50}; do [ $(curl -s -o  /dev/null -w "%{http_code}" http://127.0.0.1/api/internal/preload) -eq 200 ] && break || echo "Retry preload $i..." >>/data/logs/error.log && sleep 0.01 ; done']
            preStop:
              exec:
                command: [ '/bin/sh', '-c', 'sleep 5; /usr/local/nginx/sbin/nginx -s quit; while pgrep -x nginx; do sleep 1; done' ]
          env:
            - name: RUNTIME_ENV
              value: 'k8s'
            - name: aliyun_logs_nginx-access
              value: stdout
            - name: aliyun_logs_nginx-access_tags
              value: app=crm
            - name: aliyun_logs_nginx-access_tags
              value: env=grey
            - name: aliyun_logs_nginx-error
              value: /data/logs/error.log
            - name: aliyun_logs_nginx-error_tags
              value: app=crm
          volumeMounts:
            - name: volume-log
              mountPath: /data/logs
            - name: volume-nginx
              mountPath: /usr/local/nginx/conf/nginx.conf
              subPath: crm.nginx.grey.conf
              readOnly: false
            - name: volume-zipkin
              mountPath: /usr/local/nginx/nginx-opentracing/zipkin-config.json
              subPath: zipkin-nginx-config.json
              readOnly: false
          resources:
            requests:
              cpu: "0.25"
              memory: "0.5Gi"
            limits:
              cpu: "1"
              memory: "2Gi"
          ports:
            - name: http
              containerPort: 80
          livenessProbe:
            httpGet:
              path: /healthz
              port: http
            initialDelaySeconds: 3
            periodSeconds: 60
            timeoutSeconds: 5
          readinessProbe:
            httpGet:
              path: /healthz
              port: http
            initialDelaySeconds: 5
            periodSeconds: 60
            timeoutSeconds: 1
          securityContext:
            privileged: true
            capabilities:
              add: [ "SYS_ADMIN" ,"SYS_PTRACE"]
            allowPrivilegeEscalation: true
      dnsPolicy: None
      dnsConfig:
        nameservers:
          - *************
          - *************
        searches:
          - app-crm.svc.cluster.local
          - svc.cluster.local
          - cluster.local
        options:
          - name: ndots
            value: '3'
          - name: timeout
            value: '1'
          - name: attempts
            value: '2'
          - name: single-request-reopen
      volumes:
        - name: volume-log
          emptyDir: { }
        - name: volume-nginx
          configMap:
            name: nginx-grey-configmap
            items:
              - key: crm.nginx.grey.conf
                path: crm.nginx.grey.conf
        - name: volume-zipkin
          configMap:
            name: zipkin-grey-configmap
            items:
              - key: zipkin-nginx-config.json
                path: zipkin-nginx-config.json