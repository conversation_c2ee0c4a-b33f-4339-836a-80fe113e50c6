apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-beta-configmap
  labels:
    app: nginx
    developer: PHP-CRM
  namespace: app-crm
data:
  nginx.conf: |-
    #user  nobody;
    worker_processes  2;
    #error_log  logs/error.log;
    #error_log  logs/error.log  notice;
    #error_log  logs/error.log  info;

    load_module  /usr/local/nginx/nginx-opentracing/ngx_http_opentracing_module.so;
    #pid        logs/nginx.pid;
    events {
        worker_connections  2048;
    }

    # load modules compiled as Dynamic Shared Object (DSO)    
    #dso {
    #    load ngx_http_fastcgi_module.so;
    #    load ngx_http_rewrite_module.so;
    #}

    http {
        include       mime.types;
        default_type  application/octet-stream;
        server_names_hash_bucket_size 128;
        log_format access_log '$remote_addr|$remote_user|$time_local|$http_host|$request|$status|$body_bytes_sent|$http_referer|$http_user_agent|$request_time|$proxy_add_x_forwarded_for|$upstream_addr|$cookie_clientId|$cookie_userId|$cookie_pskey|$cookie_fingerprint|$http_x_forwarded_for|$request_length|$http_xiaoman_client_id|$http_xiaoman_user_id|$http_xiaoman_pskey|$http_xiaoman_device_id|$http_xiaoman_os|$http_xiaoman_os_version|$http_x_xiaoman_env|$http_x_xiaoman_platform_type|$opentracing_context_x_b3_spanid|$opentracing_context_x_b3_traceid';
        access_log  /dev/stdout  access_log;
        error_log  /data/logs/error.log;

        opentracing on;
        opentracing_load_tracer  /usr/local/nginx/nginx-opentracing/linux-amd64-libzipkin_opentracing_plugin.so /usr/local/nginx/nginx-opentracing/zipkin-config.json;
        opentracing_location_operation_name "HTTP $request_method $host";
        opentracing_operation_name "HTTP $request_method $host";
        opentracing_trace_locations off;
        opentracing_propagate_context;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always;
        sendfile        on;
        #tcp_nopush     on;

        #keepalive_timeout  0;
        keepalive_timeout  65;

        gzip  on;
        gzip_min_length 10k;
        gzip_buffers 4 16k;
        gzip_http_version 1.1;
        gzip_comp_level 2;
        gzip_types application/javascript application/x-javascript text/css text/javascript application/json text/plain text/xml application/x-protobuf;
        gzip_vary on;

        server {
            listen       80;
            server_name  localhost;
            
            root        /data/codebase/crm_root/beta/master/php-crm/;
            index       index-beta.php;
            
            location / {
              root    /usr/local/nginx/html;
              index   index.html index.htm;
            }
            #error_page   500 502 503 504  /50x.html;
            location = /50x.html {
              root /usr/local/nginx/html;
            }
    
            location /healthz {
              access_log off;
              return 200;
            }
    
            location  /fpm_status {
              access_log off;

              include fastcgi_params;
              fastcgi_param SCRIPT_FILENAME "";
              fastcgi_pass 127.0.0.1:9000;
            }
    
            location  /ping {
              access_log off;
              include fastcgi_params;
              fastcgi_param SCRIPT_FILENAME "/ping";
              fastcgi_param SCRIPT_NAME "/ping";
              fastcgi_pass 127.0.0.1:9000;
            }
    
            location ~ \.php$ {
              add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
              add_header 'Access-Control-Allow-Origin' "$http_origin";
              add_header 'Access-Control-Allow-Credentials' "true";
              add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
    
              include fastcgi_params;
              fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
              fastcgi_param REQUEST_URI $php_url?$args;
              fastcgi_param REAL_REQUEST_URI $request_uri;
              fastcgi_pass   127.0.0.1:9000;
              opentracing_fastcgi_propagate_context;
            }
    
            location ~* ^/api(/.*)$ {
              set $php_url $1;
              try_files $uri $uri/ /index-beta.php?$args;
            }
        }

        include /usr/local/nginx/conf/vhost.d/*.conf;
    }
  prometheus.conf: |-
    #user  nobody;
    worker_processes  1;
    worker_rlimit_nofile 65535;
    #error_log  logs/error.log;
    #error_log  logs/error.log  notice;
    #error_log  logs/error.log  info;
    load_module  /usr/local/nginx/nginx-opentracing/ngx_http_opentracing_module.so;
    #pid        logs/nginx.pid;
    events {
        worker_connections   4096;
    }

    # load modules compiled as Dynamic Shared Object (DSO)    
    #dso {
    #    load ngx_http_fastcgi_module.so;
    #    load ngx_http_rewrite_module.so;
    #}

    http {
        include       mime.types;
        default_type  application/octet-stream;
        server_names_hash_bucket_size 128;
        log_format access_log '$remote_addr|$remote_user|$time_local|$http_host|$request|$status|$body_bytes_sent|$http_referer|$http_user_agent|$request_time|$proxy_add_x_forwarded_for|$upstream_addr|$cookie_clientId|$cookie_userId|$cookie_pskey|$cookie_fingerprint|$http_x_forwarded_for|$request_length|$http_xiaoman_client_id|$http_xiaoman_user_id|$http_xiaoman_pskey|$http_xiaoman_device_id|$http_xiaoman_os|$http_xiaoman_os_version|$http_x_xiaoman_env|$http_x_xiaoman_platform_type|$opentracing_context_x_b3_spanid|$opentracing_context_x_b3_traceid';

        access_log  /dev/stdout  access_log;
        error_log  /data/logs/error.log;

        opentracing on;
        opentracing_load_tracer  /usr/local/nginx/nginx-opentracing/linux-amd64-libzipkin_opentracing_plugin.so /usr/local/nginx/nginx-opentracing/zipkin-config.json;
        opentracing_location_operation_name "HTTP $request_method $host";
        opentracing_operation_name "HTTP $request_method $host";
        opentracing_trace_locations off;
        opentracing_propagate_context;

        sendfile        on;
        #tcp_nopush     on;

        #keepalive_timeout  0;
        keepalive_timeout  65;

        gzip  on;
        gzip_min_length 10k;
        gzip_buffers 4 16k;
        gzip_http_version 1.1;
        gzip_comp_level 2;
        gzip_types application/javascript application/x-javascript text/css text/javascript application/json text/plain text/xml application/x-protobuf;
        gzip_vary on;
  
        server {
          charset utf-8;
          client_max_body_size 128M;
          listen 80; ## listen for ipv4
          server_name  _;
          
          root        /data/codebase/crm_root/production/php-crm/;
          index       index-beta.php;
          location / {
    
            if ($uri = '/') {
              rewrite ^/ /prometheus/$uri redirect;
            }    
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Cache-Control' 'no-cache';
            add_header 'Access-Control-Max-Age' '86400';
            if ($request_method = 'OPTIONS') {
                return 204;
            }    
            #先服务nginx返回, 后续再考虑交接给网关处理
            add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
            add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always;    
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
          }
          location = /50x.html {
              root /usr/local/nginx/html;
          }
          location /healthz {
              access_log off;
              return 200;
          }
          location /fpm_status {
            access_log off;
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME "";
            fastcgi_pass 127.0.0.1:9000;
          }
          location /ping {
            access_log off;
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME "/ping";
            fastcgi_param SCRIPT_NAME "/ping";
            fastcgi_pass 127.0.0.1:9000;
          }    
          location ~ \/(protected|k8s)\/ {
            deny all;
          }
          location ~ /\.(ht|svn|git) {
            deny all;
          }  
          location ~ \.php$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';    
            #先服务nginx返回, 后续再考虑交接给网关处理
            add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
            add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always;
            add_header 'Access-Control-Max-Age' '86400';
            if ($request_method = 'OPTIONS') {
                return 204;
            }    
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
            fastcgi_param REQUEST_URI $php_url?$args;
            fastcgi_param REAL_REQUEST_URI $request_uri;
            fastcgi_pass   127.0.0.1:9000;
            opentracing_fastcgi_propagate_context;
          }
    
          location ~* ^/api(/.*)$ {
            set $php_url $1;
            try_files $uri $uri/ /index-beta.php?$args;
          }
          location /interface {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
          }
          location /track {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
          }
          location /app {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
          }
          location /stormsFury {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
          }     
          location ^~ /prometheus {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
          }
    
          location ^~ /prometheusOversea {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
          }  
    
          location ^~ /prometheusPublish {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
          }  
    
          location ^~ /prometheusConfigCenter {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
          }
        }
    }
  vhost.conf: |-
    log_format web_access_log '|$remote_addr|$remote_user|$time_local|$http_host|$request|$status|$body_bytes_sent|$http_referer|$http_user_agent|$request_time|$proxy_add_x_forwarded_for|$upstream_addr|$cookie_clientId|$cookie_userId|$cookie_pskey|$cookie_fingerprint|$http_x_forwarded_for|';
    upstream crm-fpm {
        server crm-beta-fpm-service:9000 weight=8 max_fails=0 fail_timeout=30s;
    }
    upstream crm-xhprof-php7-fpm {
        server crm-xhporf-fpm-beta:9000 weight=8 max_fails=0 fail_timeout=30s;
    }
    server {
        charset utf-8;
        client_max_body_size 128M;
        listen 80; ## listen for ipv4
        server_name omgk.xiaoman.cn;
        root        /data/codebase/crm_root/beta/v4_client/;
        index       web/crm_main_shell_dist/index.html;
        access_log  /data/v4_logs/nginx/omgk_access.log web_access_log;
        error_log   /data/v4_logs/nginx/error.log;
        location / {
            try_files /web/$uri /web/crm_main_shell_dist/$uri /web/crm_main_shell_dist/index.html 404;
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
    		add_header 'Cache-Control' 'no-cache';
        }
        location ~ \/(protected|k8s)\/ {
          deny all;
        }
        location ~ /\.(ht|svn|git) {
            deny all;
        }
        location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Cache-Control' 'no-cache';
            try_files /web/$uri /web/crm_main_shell_dist/$uri 404;
        }
        location ~ ^\/web\/im-portal\/(.*) {
            index     /web/im-portal/index.html;
            try_files /web/im-portal/$1 /web/im-portal/index.html 404;
        }    
        location ~ ^\/(.*)_subapp\/(.*) {
            index     /web/$1_subapp/index.html;
            try_files /web/$1_subapp/$2 /web/$1_subapp/index.html 404;
        }    
        location ~ \.php$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
            fastcgi_param REQUEST_URI $php_url?$args;
            fastcgi_param REAL_REQUEST_URI $request_uri;
            fastcgi_pass   crm-fpm;
            try_files $uri =404;
            fastcgi_param PHP_VALUE "auto_prepend_file=/data/codebase/crm_root/beta/yii/xhprof/index.php";
    	 }
        location ~* ^/api(/.*)$ {
            set $php_url $1;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location /stormsFury {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }    
        location /interface {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location /track {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }

        location /app {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location ^~ /prometheus {
            #rewrite ^/ https://prometheus.xiaoman.cn/;
            set $php_url $request_uri;
            try_files /web/$uri /index-beta.php?$args;
        }
        location /internal {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
            try_files /web/$uri /web/crm_main_shell_dist/$uri  404;
            expires 7d;
        }
        location ~ \.(jpg|jpeg|gif|png|ico|svg|otf)$ {
            try_files /web/$uri /web/crm_main_shell_dist/$uri 404;
            expires 30m;
        }
    }

    server {
        charset utf-8;
        client_max_body_size 128M;
        listen 80; ## listen for ipv4
        server_name omgdev.xiaoman.cn;
        root        /data/codebase/crm_root/beta/v4_client_dev/;
        index       web/crm_main_shell_dist/index.html;
        access_log  /data/v4_logs/nginx/omgdev_access.log web_access_log;
        error_log   /data/v4_logs/nginx/error.log;
        location / {
            try_files /web/$uri /web/crm_main_shell_dist/$uri /web/crm_main_shell_dist/index.html 404;
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
    		add_header 'Cache-Control' 'no-cache';
        }
        location ~ \/(protected|k8s)\/ {
          deny all;
        }
        location ~ /\.(ht|svn|git) {
            deny all;
        }
        location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Cache-Control' 'no-cache';
            try_files /web/$uri /web/crm_main_shell_dist/$uri 404;
        }
        location ~ ^\/web\/im-portal\/(.*) {
            index     /web/im-portal/index.html;
            try_files /web/im-portal/$1 /web/im-portal/index.html 404;
        }    
        location ~ ^\/(.*)_subapp\/(.*) {
            index     /web/$1_subapp/index.html;
            try_files /web/$1_subapp/$2 /web/$1_subapp/index.html 404;
        }    
        location ~ \.php$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
            fastcgi_param REQUEST_URI $php_url?$args;
            fastcgi_param REAL_REQUEST_URI $request_uri;
            fastcgi_pass   crm-fpm;
            try_files $uri =404;
    	    fastcgi_param PHP_VALUE "auto_prepend_file=/data/codebase/crm_root/beta/yii/xhprof/index.php";
    	 }
        location ~* ^/api(/.*)$ {
            set $php_url $1;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location /interface {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location /track {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }

        location /app {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location ^~ /prometheus {
            set $php_url $request_uri;
            try_files /web/$uri /index-beta.php?$args;
        }
        location /internal {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
            try_files /web/$uri /web/crm_main_shell_dist/$uri  404;
            expires 7d;
        }
        location ~ \.(jpg|jpeg|gif|png|ico|svg|otf)$ {
            try_files /web/$uri /web/crm_main_shell_dist/$uri 404;
            expires 30m;
        }
    }
    
    server {
        charset utf-8;
        client_max_body_size 128M;
        listen 80; ## listen for ipv4
        server_name omg-bugfix.xiaoman.cn;
        root        /data/codebase/crm_root/beta/bugfix/;
        index       web/crm_main_shell_dist/index.html;
        access_log  /data/v4_logs/nginx/omgbugfix_access.log web_access_log;
        error_log   /data/v4_logs/nginx/error.log;
        location / {
            try_files /web/$uri /web/crm_main_shell_dist/$uri /web/crm_main_shell_dist/index.html 404;
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
    		add_header 'Cache-Control' 'no-cache';
        }
        location ~ \/(protected|k8s)\/ {
          deny all;
        }
        location ~ /\.(ht|svn|git) {
            deny all;
        }
        location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Cache-Control' 'no-cache';
            try_files /web/$uri /web/crm_main_shell_dist/$uri 404;
        }
        location ~ ^\/web\/im-portal\/(.*) {
            index     /web/im-portal/index.html;
            try_files /web/im-portal/$1 /web/im-portal/index.html 404;
        }    
        location ~ ^\/(.*)_subapp\/(.*) {
            index     /web/$1_subapp/index.html;
            try_files /web/$1_subapp/$2 /web/$1_subapp/index.html 404;
        }    
        location ~ \.php$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
            fastcgi_param REQUEST_URI $php_url?$args;
            fastcgi_param REAL_REQUEST_URI $request_uri;
            fastcgi_pass   crm-fpm;
            try_files $uri =404;
    	    fastcgi_param PHP_VALUE "auto_prepend_file=/data/codebase/crm_root/beta/yii/xhprof/index.php";
    	 }
        location ~* ^/api(/.*)$ {
            set $php_url $1;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location /stormsFury {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }    
        location /interface {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location /track {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }

        location /app {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location ^~ /prometheus {
            set $php_url $request_uri;
            try_files /web/$uri /index-beta.php?$args;
        }
        location /internal {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
            try_files /web/$uri /web/crm_main_shell_dist/$uri  404;
            expires 7d;
        }
        location ~ \.(jpg|jpeg|gif|png|ico|svg|otf)$ {
            try_files /web/$uri /web/crm_main_shell_dist/$uri 404;
            expires 30m;
        }
    }    
    
    server {
        charset utf-8;
        client_max_body_size 128M;
        listen 80; ## listen for ipv4
        server_name omgb.xiaoman.cn;
        root        /data/codebase/crm_root/beta/php-lighthouse/;
        index       web/crm_main_shell_dist/index.html;
        access_log  /data/v4_logs/nginx/omgb_access.log web_access_log;
        error_log   /data/v4_logs/nginx/error.log;
        location / {
            try_files /web/$uri /web/crm_main_shell_dist/$uri /web/crm_main_shell_dist/index.html 404;
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
    		add_header 'Cache-Control' 'no-cache';
        }
        location ~ \/(protected|k8s)\/ {
          deny all;
        }
        location ~ /\.(ht|svn|git) {
            deny all;
        }
        location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Cache-Control' 'no-cache';
            try_files /web/$uri /web/crm_main_shell_dist/$uri 404;
        }
        location ~ ^\/web\/im-portal\/(.*) {
            index     /web/im-portal/index.html;
            try_files /web/im-portal/$1 /web/im-portal/index.html 404;
        }    
        location ~ ^\/(.*)_subapp\/(.*) {
            index     /web/$1_subapp/index.html;
            try_files /web/$1_subapp/$2 /web/$1_subapp/index.html 404;
        }    
        location ~ \.php$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
            fastcgi_param REQUEST_URI $php_url?$args;
            fastcgi_param REAL_REQUEST_URI $request_uri;
            fastcgi_pass   crm-fpm;
            try_files $uri =404;
            fastcgi_param PHP_VALUE "auto_prepend_file=/data/codebase/crm_root/beta/yii/xhprof/index.php";
    	 }
        location ~* ^/api(/.*)$ {
            set $php_url $1;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location /stormsFury {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }    
        location /interface {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location /track {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }

        location /app {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location ^~ /prometheus {
            #rewrite ^/ https://prometheus.xiaoman.cn/;
            set $php_url $request_uri;
            try_files /web/$uri /index-beta.php?$args;
        }
        location /internal {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
            try_files /web/$uri /web/crm_main_shell_dist/$uri  404;
            expires 7d;
        }
        location ~ \.(jpg|jpeg|gif|png|ico|svg|otf)$ {
            try_files /web/$uri /web/crm_main_shell_dist/$uri 404;
            expires 30m;
        }
    }

    server {
        charset utf-8;
        client_max_body_size 128M;
        listen 80; ## listen for ipv4
        server_name ~^(?<home>[\w-]+)\.beta\.xiaoman\.cn$;
        root        /data/codebase/crm_root/beta/$home;
        index       web/dist/index.html;
        access_log  /data/v4_logs/nginx/beta_access.log web_access_log;
        error_log   /data/v4_logs/nginx/beta_error.log;
        location / {
             try_files /web/$uri /web/crm_main_shell_dist/$uri /web/crm_main_shell_dist/index.html 404;
        }
        location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin" always;
            add_header 'Access-Control-Allow-Credentials' "true" always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
            add_header 'Cache-Control' 'no-store';

            try_files /web/$uri /web/crm_main_shell_dist/$uri 404;
        }
        location ~ \.(jpg|jpeg|gif|png|ico|svg|otf)$ {
            try_files /web/$uri /web/crm_main_shell_dist/$uri 404;
            expires 30m;
        }
        location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
            try_files /web/$uri /web/crm_main_shell_dist/$uri  404;
            expires 7d;
        }
        location ~ ^\/web\/im-portal\/(.*) {
            index     /web/im-portal/index.html;
            try_files /web/im-portal/$1 /web/im-portal/index.html 404;
        }
        location ~ ^\/(.*)_subapp\/(.*) {
            index     /web/$1_subapp/index.html;
            try_files /web/$1_subapp/$2 /web/$1_subapp/index.html 404;
        }    
        location ~ \/(protected|k8s)\/ {
          deny all;
        }
        location ~ /\.(ht|svn|git) {
            deny all;
        }
        location ~ \.php$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin" always;
            add_header 'Access-Control-Allow-Credentials' "true" always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
            fastcgi_param REQUEST_URI $php_url?$args;
            fastcgi_param REAL_REQUEST_URI $request_uri;
            fastcgi_pass   crm-fpm;
            try_files $uri =404;
            fastcgi_param PHP_VALUE "auto_prepend_file=/data/codebase/crm_root/beta/yii/xhprof/index.php";
        }
        location ~* ^/api(/.*)$ {
            set $php_url $1;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location /stormsFury {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location /interface {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location /track {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location /app {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location ^~ /prometheus {
            set $php_url $request_uri;
            try_files /web/$uri /index-beta.php?$args;
        }
        location /internal {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location /assistant {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
    }

    server {
        charset utf-8;
        client_max_body_size 128M;
        listen 80; ## listen for ipv4
        server_name app.beta.xiaoman.cn;
        root        /data/codebase/crm_root/beta/app;
        index       web/dist/index.html;
        access_log  /data/v4_logs/nginx/app_beta_access.log web_access_log;
        error_log   /data/v4_logs/nginx/app_beta_error.log;
        location / {
             try_files /web/$uri /web/crm_main_shell_dist/$uri /web/crm_main_shell_dist/index.html 404;
        }
        location ~ \/(protected|k8s)\/ {
          deny all;
        } 
        location ~ /\.(ht|svn|git) {
            deny all;
        }
        location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin" always;
            add_header 'Access-Control-Allow-Credentials' "true" always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
            add_header 'Cache-Control' 'no-store';
            try_files /web/$uri /web/crm_main_shell_dist/$uri 404;
        }
        location ~ \.(jpg|jpeg|gif|png|ico|svg|otf)$ {
            try_files /web/$uri /web/crm_main_shell_dist/$uri 404;
            expires 30m;
        }
        location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
            try_files /web/$uri /web/crm_main_shell_dist/$uri  404;
            expires 7d;
        }
        location ~ ^\/web\/im-portal\/(.*) {
            index     /web/im-portal/index.html;
            try_files /web/im-portal/$1 /web/im-portal/index.html 404;
        }
        location ~ ^\/(.*)_subapp\/(.*) {
            index     /web/$1_subapp/index.html;
            try_files /web/$1_subapp/$2 /web/$1_subapp/index.html 404;
        }    
        location ~ \.php$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,x-xiaoman-platform-type' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin" always;
            add_header 'Access-Control-Allow-Credentials' "true" always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
            fastcgi_param REQUEST_URI $php_url?$args;
            fastcgi_param REAL_REQUEST_URI $request_uri;
            fastcgi_pass   crm-fpm;
            try_files $uri =404;
            fastcgi_param PHP_VALUE "auto_prepend_file=/data/codebase/crm_root/beta/yii/xhprof/index.php";
        }
        location ~* ^/api(/.*)$ {
    	    set $php_url "/app$1";
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location /stormsFury {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location /interface {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location /track {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location /app {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location ^~ /prometheus {
            set $php_url $request_uri;
            try_files /web/$uri /index-beta.php?$args;
        }
        location /internal {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location /assistant {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
    }

    server {
        charset utf-8;
        client_max_body_size 128M;
        listen 8000; ## listen for ipv4
        server_name *.sandbox.xiaoman.cn;
        root        /data/codebase/crm_root/beta/k7;
        index       web/dist/index.html;
        access_log  /data/v4_logs/nginx/sandbox_access.log web_access_log;
        error_log   /data/v4_logs/nginx/sandbox_error.log;
        location / {
             try_files /web/$uri /web/crm_main_shell_dist/$uri /web/crm_main_shell_dist/index.html 404;
        }
        location ~ \/(protected|k8s)\/ {
          deny all;
        }
        location ~ /\.(ht|svn|git) {
            deny all;
        }
        location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin" always;
            add_header 'Access-Control-Allow-Credentials' "true" always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
            add_header 'Cache-Control' 'no-store';
            try_files /web/$uri /web/crm_main_shell_dist/$uri 404;
        }
        location ~ \.(jpg|jpeg|gif|png|ico|svg|otf)$ {
            try_files /web/$uri /web/crm_main_shell_dist/$uri 404;
            expires 30m;
        }
        location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
            try_files /web/$uri /web/crm_main_shell_dist/$uri  404;
            expires 7d;
        }
        location ~ ^\/web\/im-portal\/(.*) {
            index     /web/im-portal/index.html;
            try_files /web/im-portal/$1 /web/im-portal/index.html 404;
        }
        location ~ ^\/(.*)_subapp\/(.*) {
            index     /web/$1_subapp/index.html;
            try_files /web/$1_subapp/$2 /web/$1_subapp/index.html 404;
        }
        location ~ \.php$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin" always;
            add_header 'Access-Control-Allow-Credentials' "true" always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
            fastcgi_param REQUEST_URI $php_url?$args;
            fastcgi_param REAL_REQUEST_URI $request_uri;
            fastcgi_pass   crm-fpm;
            try_files $uri =404;
            fastcgi_param PHP_VALUE "auto_prepend_file=/data/codebase/crm_root/beta/yii/xhprof/index.php";
        }
        location ~* ^/api(/.*)$ {
            set $php_url $1;
            try_files $uri $uri/ /index-sandbox.php?$args;
        }
        location /stormsFury {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-sandbox.php?$args;
        }
        location /interface {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-sandbox.php?$args;
        }
        location /track {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-sandbox.php?$args;
        }
        location /app {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-sandbox.php?$args;
        }
        location ^~ /prometheus {
            set $php_url $request_uri;
            try_files /web/$uri /index-sandbox.php?$args;
        }
        location /internal {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-sandbox.php?$args;
        }
        location /assistant {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-sandbox.php?$args;
        }
    }

    server {
        charset utf-8;
        client_max_body_size 128M;
        listen 80; ## listen for ipv4
        server_name omgm.xiaoman.cn;
        root        /data/codebase/crm_root/beta/php-mkt/;
        index       web/crm_main_shell_dist/index.html;
        access_log  /data/v4_logs/nginx/omgm_access.log;
        error_log   /data/v4_logs/nginx/omgm_error.log;
        location / {
            try_files /web/$uri /web/crm_main_shell_dist/$uri /web/crm_main_shell_dist/index.html 404;
            expires 0;
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        }
        location ~ \/(protected|k8s)\/ {
            deny all;
        } 
        location ~ /\.(ht|svn|git) {
            deny all;
        }
        location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
    		add_header 'Cache-Control' 'no-cache';
            try_files /web/$uri /web/crm_main_shell_dist/$uri 404;
        }
        location ~ \.php$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
            fastcgi_param REQUEST_URI $php_url?$args;
            fastcgi_param REAL_REQUEST_URI $request_uri;
            fastcgi_pass   crm-fpm;
            try_files $uri =404;
            fastcgi_param PHP_VALUE "auto_prepend_file=/data/codebase/crm_root/beta/yii/xhprof/index.php";
        }
        location ~ /\.(ht|svn|git) {
            deny all;
        }

        location ~* ^/api/internal(/.*)$ {
            set $php_url "/internal$1";
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location ~* ^/api/marketing(/.*)$ {
            set $php_url "/marketing$1";
            try_files $uri $uri/ /index-beta.php?$args;
        }

        location ~* ^/api/stormsFury(/.*)$ {
            set $php_url "/stormsFury$1";
            try_files $uri $uri/ /index-beta.php?$args;
        }

        location ~* ^/api/prometheus(/.*)$ {
           set $php_url "/prometheus$1";
           try_files $uri $uri/ /index-beta.php?$args;
       }
       location ^~ /prometheus {
          set $php_url $request_uri;
          try_files /web/$uri /index-beta.php?$args;
       }
      location ~* ^/api(/.*)$ {
        set $php_url "/marketing$1";
        try_files $uri $uri/ /index-beta.php?$args;
      }
       location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
         try_files /web/$uri /web/crm_main_shell_dist/$uri  404;
         expires 7d;
      }

      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf)$ {
        try_files /web/$uri /web/crm_main_shell_dist/$uri 404;
        expires 30m;
      }
    }

    server {
        charset utf-8;
        client_max_body_size 128M;
        listen 80; ## listen for ipv4
        server_name crm-exp-callback.xiaoman.cn;
        root        /data/codebase/crm_root/beta/v4_client/;
        index       index-exp.php;
        access_log  /data/v4_logs/nginx/crm_exp_callback_access.log;
        error_log   /data/v4_logs/nginx/crm_exp_callback_error.log;
        location / {
            try_files $uri $uri/ /index-exp.php?$args;
        }
        location ~ \/(protected|k8s)\/ {
          deny all;
        }
        location ~ /\.(ht|svn|git) {
            deny all;
        }
        location ~ \.php$ {
    	  add_header   Cache-Control "no-cache";
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
            fastcgi_pass   crm-fpm;
            try_files $uri =404;
        }

        location ~* \.(txt|xml|js|css)$ {
    	    expires 365d;
        }
        location ~* \.(jpg|jpeg|gif|png|ico|svg|otf)$ {
    	    expires 30m;
        }
        location ~* \.(html)$ {
    	    expires 30m;
        }
    }
    
    server {
        charset utf-8;
        client_max_body_size 128M;
        listen 80; ## listen for ipv4
        server_name xhprof-php7.beta.xiaoman.cn;
        root        /data/codebase/crm_root/beta/v4_client_dev/;
        index       web/crm_main_shell_dist/index.html;
        access_log  /data/v4_logs/nginx/omgdev_access.log web_access_log;
        error_log   /data/v4_logs/nginx/error.log;
        location / {
            try_files /web/$uri /web/crm_main_shell_dist/$uri /web/crm_main_shell_dist/index.html 404;
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
    		add_header 'Cache-Control' 'no-cache';
        }
        location ~ \/(protected|k8s)\/ {
          deny all;
        }
        location ~ /\.(ht|svn|git) {
            deny all;
        }
        location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Cache-Control' 'no-cache';
            try_files /web/$uri /web/crm_main_shell_dist/$uri 404;
        }
        location ~ ^\/web\/im-portal\/(.*) {
            index     /web/im-portal/index.html;
            try_files /web/im-portal/$1 /web/im-portal/index.html 404;
        }    
        location ~ ^\/(.*)_subapp\/(.*) {
            index     /web/$1_subapp/index.html;
            try_files /web/$1_subapp/$2 /web/$1_subapp/index.html 404;
        }    
        location ~ \.php$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
            fastcgi_param REQUEST_URI $php_url?$args;
            fastcgi_param REAL_REQUEST_URI $request_uri;
            fastcgi_pass   crm-xhprof-php7-fpm;
            try_files $uri =404;
    	    fastcgi_param PHP_VALUE "auto_prepend_file=/data/codebase/crm_root/beta/yii/xhprof/index.php";
    	 }
        location ~* ^/api(/.*)$ {
            set $php_url $1;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location /stormsFury {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }    
        location /interface {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location /track {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }

        location /app {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location ^~ /prometheus {
            set $php_url $request_uri;
            try_files /web/$uri /index-beta.php?$args;
        }
        location /internal {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
        }
        location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
            try_files /web/$uri /web/crm_main_shell_dist/$uri  404;
            expires 7d;
        }
        location ~ \.(jpg|jpeg|gif|png|ico|svg|otf)$ {
            try_files /web/$uri /web/crm_main_shell_dist/$uri 404;
            expires 30m;
        }
    }    



  beta.nginx.conf: |-
    #user  nobody;
    worker_processes  4;
    worker_rlimit_nofile 65535;
    #error_log  logs/error.log;
    #error_log  logs/error.log  notice;
    #error_log  logs/error.log  info;
    load_module  /usr/local/nginx/nginx-opentracing/ngx_http_opentracing_module.so;
    #pid        logs/nginx.pid;
    events {
        worker_connections   8192;
    }

    # load modules compiled as Dynamic Shared Object (DSO)    
    #dso {
    #    load ngx_http_fastcgi_module.so;
    #    load ngx_http_rewrite_module.so;
    #}

    http {
        include       mime.types;
        default_type  application/octet-stream;
        server_names_hash_bucket_size 128;
        log_format access_log '$remote_addr|$remote_user|$time_local|$http_host|$request|$status|$body_bytes_sent|$http_referer|$http_user_agent|$request_time|$proxy_add_x_forwarded_for|$upstream_addr|$cookie_clientId|$cookie_userId|$cookie_pskey|$cookie_fingerprint|$http_x_forwarded_for|$request_length|$http_xiaoman_client_id|$http_xiaoman_user_id|$http_xiaoman_pskey|$http_xiaoman_device_id|$http_xiaoman_os|$http_xiaoman_os_version|$http_x_xiaoman_env|$http_x_xiaoman_platform_type|$opentracing_context_x_b3_spanid|$opentracing_context_x_b3_traceid';

        access_log  /dev/stdout  access_log;
        error_log  /data/logs/error.log;
    
        opentracing on;
        opentracing_load_tracer  /usr/local/nginx/nginx-opentracing/linux-amd64-libzipkin_opentracing_plugin.so /usr/local/nginx/nginx-opentracing/zipkin-config.json;
        opentracing_location_operation_name "HTTP $request_method $host";
        opentracing_operation_name "HTTP $request_method $host";
        opentracing_trace_locations off;
        opentracing_propagate_context;        

        sendfile        on;
        #tcp_nopush     on;

        #keepalive_timeout  0;
        keepalive_timeout  65;

        gzip  on;
        gzip_min_length 10k;
        gzip_buffers 4 16k;
        gzip_http_version 1.1;
        gzip_comp_level 2;
        gzip_types application/javascript application/x-javascript text/css text/javascript application/json text/plain text/xml application/x-protobuf;
        gzip_vary on;

        server {
          charset utf-8;
          client_max_body_size 128M;
          listen 80; ## listen for ipv4
          server_name  _;
          root        /data/codebase/crm_root/production/php-crm/;
          index       index-beta.php;
          location / {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Cache-Control' 'no-cache';
            #先服务nginx返回, 后续再考虑交接给网关处理
            add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
            add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always;
            add_header 'Access-Control-Max-Age' '86400';
            if ($request_method = 'OPTIONS') {
                return 204;
            }    
          }
          location = /50x.html {
              root /usr/local/nginx/html;
          }
          location /healthz {
              access_log off;
              return 200;
          }
          location ~ \/(protected|k8s)\/ {
            deny all;
          }
          location ~ /\.(ht|svn|git) {
            deny all;
          }
          location ~ \.php$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            #先服务nginx返回, 后续再考虑交接给网关处理
            add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
            add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always;
            add_header 'Access-Control-Max-Age' '86400';
            if ($request_method = 'OPTIONS') {
                return 204;
            }    
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
            fastcgi_param REQUEST_URI $php_url?$args;
            fastcgi_param REAL_REQUEST_URI $request_uri;
            fastcgi_pass   127.0.0.1:9000;
            opentracing_fastcgi_propagate_context;
          }
  
          location ~* ^/api(/.*)$ {
            set $php_url $1;
            try_files $uri $uri/ /index-beta.php?$args;
          }
          location /interface {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
          }
          location /track {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
          }
          location /app {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
          }
          location /stormsFury {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
          }    
          location ^~ /prometheus {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
          }
        }
    }

  mkt.nginx.beta.conf: |-
    #user  nobody;
    worker_processes  2;
    worker_rlimit_nofile 65535;
    #error_log  logs/error.log;
    #error_log  logs/error.log  notice;
    #error_log  logs/error.log  info;
    load_module  /usr/local/nginx/nginx-opentracing/ngx_http_opentracing_module.so;
    #pid        logs/nginx.pid;
    events {
        worker_connections   2048;
    }

    # load modules compiled as Dynamic Shared Object (DSO)    
    #dso {
    #    load ngx_http_fastcgi_module.so;
    #    load ngx_http_rewrite_module.so;
    #}

    http {
        include       mime.types;
        default_type  application/octet-stream;
        server_names_hash_bucket_size 128;
        log_format access_log '$remote_addr|$remote_user|$time_local|$http_host|$request|$status|$body_bytes_sent|$http_referer|$http_user_agent|$request_time|$proxy_add_x_forwarded_for|$upstream_addr|$cookie_clientId|$cookie_userId|$cookie_pskey|$cookie_fingerprint|$http_x_forwarded_for|$request_length|$http_xiaoman_client_id|$http_xiaoman_user_id|$http_xiaoman_pskey|$http_xiaoman_device_id|$http_xiaoman_os|$http_xiaoman_os_version|$http_x_xiaoman_env|$http_x_xiaoman_platform_type|$opentracing_context_x_b3_spanid|$opentracing_context_x_b3_traceid';

        access_log  /dev/stdout  access_log;
        error_log  /data/logs/error.log;

        opentracing on;
        opentracing_load_tracer  /usr/local/nginx/nginx-opentracing/linux-amd64-libzipkin_opentracing_plugin.so /usr/local/nginx/nginx-opentracing/zipkin-config.json;
        opentracing_location_operation_name "HTTP $request_method $host";
        opentracing_operation_name "HTTP $request_method $host";
        opentracing_trace_locations off;
        opentracing_propagate_context;

        sendfile        on;
        #tcp_nopush     on;

        #keepalive_timeout  0;
        keepalive_timeout  65;

        gzip  on;
        gzip_min_length 10k;
        gzip_buffers 4 16k;
        gzip_http_version 1.1;
        gzip_comp_level 2;
        gzip_types application/javascript application/x-javascript text/css text/javascript application/json text/plain text/xml application/x-protobuf;
        gzip_vary on;

        server {
          charset utf-8;
          client_max_body_size 128M;
          listen 80; ## listen for ipv4
          server_name  _;
          root        /data/codebase/crm_root/production/php-crm/;
          index       index-beta.php;
          location / {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Cache-Control' 'no-cache';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always;    
            add_header 'Access-Control-Max-Age' '86400';
            if ($request_method = 'OPTIONS') {
                return 204;
            }    
          }
          location = /50x.html {
              root /usr/local/nginx/html;
          }
          location /healthz {
              access_log off;
              return 200;
          }
          location ~ \/(protected|k8s)\/ {
            deny all;
          }
          location ~ /\.(ht|svn|git) {
            deny all;
          }
          location ~ \.php$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always;
            add_header 'Access-Control-Max-Age' '86400';
            if ($request_method = 'OPTIONS') {
                return 204;
            }    
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
            fastcgi_param REQUEST_URI $php_url?$args;
            fastcgi_param REAL_REQUEST_URI $request_uri;
            fastcgi_pass   127.0.0.1:9000;
            opentracing_fastcgi_propagate_context;
          }
    
          location /api(/prometheus/siteTrack/.*)$ {
            set $php_url $1;
            try_files $uri $uri/ /index-beta.php?$args;
          }

         location ~* ^/api/marketing(/.*)$ {
           set $php_url "/marketing$1";
            try_files $uri $uri/ /index-beta.php?$args;
          }
          location ~* ^/api(/.*)$ {
            set $php_url "/marketing$1";
            try_files $uri $uri/ /index-beta.php?$args;
          }    
    
          location /interface {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
          }
          location /track {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
          }
          location /app {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
          }
          location ^~ /prometheus {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
          }
            location  /fpm_status {
              access_log off;

              include fastcgi_params;
              fastcgi_param SCRIPT_FILENAME "";
              fastcgi_pass 127.0.0.1:9000;
            }
    
            location  /ping {
              access_log off;
              include fastcgi_params;
              fastcgi_param SCRIPT_FILENAME "/ping";
              fastcgi_param SCRIPT_NAME "/ping";
              fastcgi_pass 127.0.0.1:9000;
            }    
        }
        include /usr/local/nginx/conf/vhost.d/*.conf;
    }    
  mkt.feature.beta.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
    
      listen 80; ## listen for ipv4
      server_name mkt.feature.beta.xiaoman.cn;
    
      set $curPath "feature/mkt";
      set $fePath /data/codebase/crm_root/beta/$curPath;
      set $defaultFePath /data/codebase/crm_root/beta/master;
      set $feTryFirst /$uri;
      set $feTrySecond /crm_main_shell_dist/$uri;
      set $feTryThird "";

      root       /data/codebase/crm_root/beta/$curPath/php-crm;
      index      crm_main_shell_dist/index.html;

      location / {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS'; 
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always;    
            add_header 'Access-Control-Max-Age' '86400';
            if ($request_method = 'OPTIONS') {
                return 204;
            }
    
        root $fePath;
        try_files /$uri /crm_main_shell_dist/$uri /crm_main_shell_dist/index.html 404;
      }

      location ~ \/(protected|k8s)\/ {
        deny all;
      }

      location ~ /\.(ht|svn|git) {
         deny all;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
         add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always;
            add_header 'Access-Control-Max-Age' '86400';
            if ($request_method = 'OPTIONS') {
                return 204;
            }    
         root    $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
      }

      location ~ ^\/web\/im-portal\/(.*) {
        index /im-portal/index.html;
        root $fePath;
        try_files /im-portal/$1 /im-portal/index.html 404;
      }

      location ~ ^\/tm\/(.*) {
        root    $fePath;
        index     /tm_subapp/index.html;
        try_files /tm_subapp/$1 /tm_subapp/index.html 404;
      }

      location ~ ^\/(.*)_subapp\/$ {
        add_header 'Cache-Control' 'no-store';
        root $fePath;
        try_files /$1_subapp/index.html 404;
      }
      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
         expires 30m;
      }
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri  404;
         expires 7d;
      }
      location ~ \.php$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always;  
            add_header 'Access-Control-Max-Age' '86400';
            if ($request_method = 'OPTIONS') {
                return 204;
            }    
         include fastcgi_params;
         fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
         fastcgi_param REQUEST_URI $php_url?$args;
         fastcgi_param REAL_REQUEST_URI $request_uri;
         fastcgi_pass   127.0.0.1:9000;
         opentracing_fastcgi_propagate_context;
      }
      location ~* ^/api/marketing(/.*)$ {
          set $php_url "/marketing$1";
          try_files $uri $uri/ /index-beta.php?$args;
      }
      location ~* ^/api(/.*)$ {
            set $php_url "/marketing$1";
            try_files $uri $uri/ /index-beta.php?$args;
      }    
      location /stormsFury {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /interface {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /track {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /app {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location ^~ /prometheus {
         set $php_url $request_uri;
         try_files /$uri /index-beta.php?$args;
      }
      location /internal {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /assistant {
       set $php_url $request_uri;
       try_files $uri $uri/ /index-beta.php?$args;
      }
    }
  nginx-lua.conf: |-
    #user  nobody;
    worker_processes  1;
    #error_log  logs/error.log;
    #error_log  logs/error.log  notice;
    #error_log  logs/error.log  info;

    load_module /usr/local/openresty/nginx/nginx-opentracing/ngx_http_opentracing_module.so;

    #pid        logs/nginx.pid;
    events {
        worker_connections  2048;
    }

    # load modules compiled as Dynamic Shared Object (DSO)
    #
    #dso {
    #    load ngx_http_fastcgi_module.so;
    #    load ngx_http_rewrite_module.so;
    #}


    http {
        include       mime.types;
        default_type  application/octet-stream;
        server_names_hash_bucket_size 128;
        log_format access_log '$remote_addr|$remote_user|$time_local|$http_host|$request|$status|$body_bytes_sent|$http_referer|$http_user_agent|$request_time|$proxy_add_x_forwarded_for|$upstream_addr|$cookie_clientId|$cookie_userId|$cookie_pskey|$cookie_fingerprint|$http_x_forwarded_for|$request_length|$http_xiaoman_client_id|$http_xiaoman_user_id|$http_xiaoman_pskey|$http_xiaoman_device_id|$http_xiaoman_os|$http_xiaoman_os_version|$http_x_xiaoman_env|$http_x_xiaoman_platform_type|$opentracing_context_x_b3_spanid|$opentracing_context_x_b3_traceid';

        access_log  /dev/stdout  access_log;
        error_log  /data/logs/error.log info;

        sendfile        on;
        #tcp_nopush     on;

        #keepalive_timeout  0;
        keepalive_timeout  65;

        gzip  on;
        gzip_min_length 10k;
        gzip_buffers 4 16k;
        gzip_http_version 1.1;
        gzip_comp_level 2;
        gzip_types application/javascript application/x-javascript text/css text/javascript application/json text/plain text/xml application/x-protobuf;
        gzip_vary on;
    
        #lua config
        lua_code_cache off;
        rewrite_by_lua_no_postpone off;
        lua_package_path '/usr/local/openresty/nginx/conf/resty_modules/lualib/?.lua;/usr/local/openresty/nginx/conf/library/?.lua;/usr/local/openresty/nginx/conf/lua/?.lua;;';
        lua_package_cpath "/usr/local/openresty/nginx/conf/resty_modules/lualib/?.so;;";
        lua_shared_dict site_cache 64m;
       
        resolver *********;    

        server {
            listen       80;
            server_name  localhost;
            location / {
              root    /usr/local/openresty/nginx/html;
              index   index.html index.htm;
            }
            #error_page   500 502 503 504  /50x.html;
            location = /50x.html {
              root /usr/local/nginx/html;
            }
    
            location /healthz {
              access_log off;
              return 200;
            }
    
            location  /fpm_status {
              access_log off;

              include fastcgi_params;
              fastcgi_param SCRIPT_FILENAME "";
              fastcgi_pass 127.0.0.1:9000;
            }
    
            location  /ping {
              access_log off;
              include fastcgi_params;
              fastcgi_param SCRIPT_FILENAME "/ping";
              fastcgi_param SCRIPT_NAME "/ping";
              fastcgi_pass 127.0.0.1:9000;
            }
        }

        include /usr/local/openresty/nginx/conf/vhost.d/*.conf;
    }
  hotfix.beta.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
    
      listen 80; ## listen for ipv4
      server_name *.hotfix.beta.xiaoman.cn;
    
      if ($host !~* ^(.*)\.hotfix\.beta\.xiaoman\.cn$) {
          return 404;
      }
    
      set $curPath $1;
      set $fePath /data/codebase/crm_root/beta/hotfix/$curPath;
      set $defaultFePath /data/codebase/crm_root/beta/master;
      set $feTryFirst /$uri;
      set $feTrySecond /crm_main_shell_dist/$uri;
      set $feTryThird "";
    
      root       /data/codebase/crm_root/beta/hotfix/$curPath/php-crm;
      index      crm_main_shell_dist/index.html;

      ##前端资源不存在，默认拉取master分支代码
      location @fe_default {
        root    $defaultFePath;
        try_files $feTryFirst $feTrySecond $feTryThird 404;
      }
    
      location / {
        root    $fePath;
        set $feTryThird /crm_main_shell_dist/index.html;
        try_files $feTryFirst $feTrySecond $feTryThird @fe_default;
      }

      location = /50x.html {
         root /usr/local/nginx/html;
      }

      location /healthz {
         access_log off;
         return 200;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,x-xiaoman-platform-type' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always;    
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
      }
    
      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf)$ {
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
        expires 30m;
      }
    
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;
            add_header 'Access-Control-Max-Age' '86400';
            if ($request_method = 'OPTIONS') {
                return 204;
            }    
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
        expires 7d;
      }
    
      location ~ ^\/web\/im-portal\/(.*) {
        root    $fePath;
        index     /im-portal/index.html;
        set $feTryFirst /im-portal/$1;
        set $feTrySecond /im-portal/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }

      location ~ ^\/tm\/(.*) {
        root    $fePath;
        index     /tm_subapp/index.html;
        set $feTryFirst /tm_subapp/$1;
        set $feTrySecond /tm_subapp/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }

      location ~ ^\/(.*)_subapp\/$ {
          add_header 'Cache-Control' 'no-store';
          root    $fePath;
          set $feTryFirst /$1_subapp/index.html;
          set $feTrySecond "";
          try_files $feTryFirst $feTrySecond @fe_default;
      }
    
      location /protected {
        deny  all;
      }
    
      location ~ \.php$ {

        if (!-f $document_root$fastcgi_script_name) {
            root  /data/codebase/crm_root/beta/master/php-crm;
        }

        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always;
            add_header 'Access-Control-Max-Age' '86400';
            if ($request_method = 'OPTIONS') {
                return 204;
            }    
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
        fastcgi_param REQUEST_URI $php_url?$args;
        fastcgi_param REAL_REQUEST_URI $request_uri;
        fastcgi_pass   127.0.0.1:9000;
        opentracing_fastcgi_propagate_context;
      }
    
      location ~ /\.(ht|svn|git) {
        deny all;
      }
    
      location ~* ^/api(/.*)$ {
        set $php_url $1;
        try_files $uri $uri/ /index-beta.php?$args;
      }
      location /stormsFury {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /interface {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /track {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /app {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
      location ^~ /prometheus {
        set $php_url $request_uri;
        try_files /$uri /index-beta.php?$args;
      }
      location ^~ /prometheusOversea {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }  

      location ^~ /prometheusPublish {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }      
      location ^~ /gii {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /internal {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /assistant {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    }
    
    
  feature.beta.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
    
      listen 80; ## listen for ipv4
      server_name *.feature.beta.xiaoman.cn;
    
      if ($host !~* ^(.*)\.feature\.beta\.xiaoman\.cn$) {
          return 404;
      }
    
      set $curPath $1;
      set $fePath /data/codebase/crm_root/beta/feature/$curPath;
      set $defaultFePath /data/codebase/crm_root/beta/master;
      set $feTryFirst /$uri;
      set $feTrySecond /crm_main_shell_dist/$uri;
      set $feTryThird "";

      root       /data/codebase/crm_root/beta/feature/$curPath/php-crm;
      index      crm_main_shell_dist/index.html;

      ##前端资源不存在，默认拉取master分支代码
      location @fe_default {
        root    $defaultFePath;
        try_files $feTryFirst $feTrySecond $feTryThird 404;
      }
    
      location / {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always;    
            add_header 'Access-Control-Max-Age' '86400';
            if ($request_method = 'OPTIONS') {
                return 204;
            }    
        root    $fePath;
        set $feTryThird /crm_main_shell_dist/index.html;
        try_files $feTryFirst $feTrySecond $feTryThird @fe_default;
      }
    
      location = /50x.html {
        root /usr/local/nginx/html;
      }

      location /healthz {
       access_log off;
       return 200;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always;    
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
      }

      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf)$ {
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
        expires 30m;
      }
    
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;    
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
        expires 7d;
      }
    
      location ~ ^\/web\/im-portal\/(.*) {
        root    $fePath;
        index     /im-portal/index.html;
        set $feTryFirst /im-portal/$1;
        set $feTrySecond /im-portal/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }

      location ~ ^\/tm\/(.*) {
        root    $fePath;
        index     /tm_subapp/index.html;
        set $feTryFirst /tm_subapp/$1;
        set $feTrySecond /tm_subapp/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }

      location ~ ^\/(.*)_subapp\/$ {
          add_header 'Cache-Control' 'no-store';
          root    $fePath;
          set $feTryFirst /$1_subapp/index.html;
          set $feTrySecond "";
          try_files $feTryFirst $feTrySecond @fe_default;
      }

      location /protected {
        deny  all;
      }
    
      location ~ \.php$ {
        if (!-f $document_root$fastcgi_script_name) {
            root  /data/codebase/crm_root/beta/master/php-crm;
        }

        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always; 
            add_header 'Access-Control-Max-Age' '86400';
            if ($request_method = 'OPTIONS') {
                return 204;
            }    
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
        fastcgi_param REQUEST_URI $php_url?$args;
        fastcgi_param REAL_REQUEST_URI $request_uri;
        fastcgi_pass   127.0.0.1:9000;
        opentracing_fastcgi_propagate_context;
      }
    
      location ~ /\.(ht|svn|git) {
        deny all;
      }
    
      location ~* ^/api(/.*)$ {
        set $php_url $1;
        try_files $uri $uri/ /index-beta.php?$args;
      }
      location /stormsFury {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /interface {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /track {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /app {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
      location ^~ /prometheus {
        set $php_url $request_uri;
        try_files /$uri /index-beta.php?$args;
      }
      location ^~ /prometheusOversea {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }  

      location ^~ /prometheusPublish {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }      
      location ^~ /gii {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /internal {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /assistant {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
      location /tapdPlugin {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }    
    }

  bugfix.beta.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
      listen 80; ## listen for ipv4
      #server_name ~^(?<home>[\w-]+)\.bugfix\.beta\.xiaoman\.cn$;
      server_name *.bugfix.beta.xiaoman.cn;
      if ($host !~* ^(.*)\.bugfix\.beta\.xiaoman\.cn$) {
         return 404;
      }
    
      set $curPath $1;
      set $fePath /data/codebase/crm_root/beta/bugfix/$curPath;
      set $defaultFePath /data/codebase/crm_root/beta/master;
      set $feTryFirst /$uri;
      set $feTrySecond /crm_main_shell_dist/$uri;
      set $feTryThird "";

      root        /data/codebase/crm_root/beta/bugfix/$curPath/php-crm;
      index       crm_main_shell_dist/index.html;
    
      ##前端资源不存在，默认拉取master分支代码
      location @fe_default {
        root    $defaultFePath;
        try_files $feTryFirst $feTrySecond $feTryThird 404;
      }

      location / {
          root $fePath;
          set $feTryThird /crm_main_shell_dist/index.html;
          try_files $feTryFirst $feTrySecond $feTryThird @fe_default;
      }

      location ~ \/(protected|k8s)\/ {
        deny all;
      }

      location ~ /\.(ht|svn|git) {
         deny all;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
         add_header 'Cache-Control' 'no-store';
         #先服务nginx返回, 后续再考虑交接给网关处理
         add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
         add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always;
         root    $fePath;
         try_files $feTryFirst $feTrySecond @fe_default;
      }

      location ~ ^\/web\/im-portal\/(.*) {
        index /im-portal/index.html;
        root $fePath;
        set $feTryFirst /im-portal/$1;
        set $feTrySecond /im-portal/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }

      location ~ ^\/tm\/(.*) {
        root    $fePath;
        index     /tm_subapp/index.html;
        set $feTryFirst /tm_subapp/$1;
        set $feTrySecond /tm_subapp/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }

      location ~ ^\/(.*)_subapp\/$ {
        add_header 'Cache-Control' 'no-store';
        root $fePath;
        set $feTryFirst /$1_subapp/index.html;
        set $feTrySecond "";
        try_files $feTryFirst $feTrySecond @fe_default;
      }

      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf)$ {
         root $fePath;
         try_files $feTryFirst $feTrySecond @fe_default;
         expires 30m;
      }

      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;     
        root $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
        expires 7d;
      }

      location ~ \.php$ {
        if (!-f $document_root$fastcgi_script_name) {
            root  /data/codebase/crm_root/beta/master/php-crm;
        }
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always;  
        add_header 'Access-Control-Max-Age' '86400';
        if ($request_method = 'OPTIONS') {
            return 204;
        }    
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
        fastcgi_param REQUEST_URI $php_url?$args;
        fastcgi_param REAL_REQUEST_URI $request_uri;
        fastcgi_pass   127.0.0.1:9000;
        opentracing_fastcgi_propagate_context;
      }
      location ~* ^/api(/.*)$ {
         set $php_url $1;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /stormsFury {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /interface {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /track {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /app {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location ^~ /prometheus {
         set $php_url $request_uri;
         try_files /$uri /index-beta.php?$args;
      }
      location ^~ /prometheusOversea {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }  

      location ^~ /prometheusPublish {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }  
      location /internal {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /assistant {
       set $php_url $request_uri;
       try_files $uri $uri/ /index-beta.php?$args;
      }
    }
  master.beta.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
      listen 80; ## listen for ipv4

      server_name master.beta.xiaoman.cn;

      set $fePath /data/codebase/crm_root/beta/master;

      root        /data/codebase/crm_root/beta/master/php-crm;
      index       /dist/index.html;

      location / {
          root $fePath;
          try_files /$uri /crm_main_shell_dist/$uri /crm_main_shell_dist/index.html 404;
      }

      location ~ \/(protected|k8s)\/ {
        deny all;
      }

      location ~ /\.(ht|svn|git) {
         deny all;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
         add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always;
         root    $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
      }

      location ~ ^\/web\/im-portal\/(.*) {
        index /im-portal/index.html;
        root $fePath;
        try_files /im-portal/$1 /im-portal/index.html 404;
      }

      location ~ ^\/tm\/(.*) {
        root    $fePath;
        index     /tm_subapp/index.html;
        try_files /tm_subapp/$1 /tm_subapp/index.html 404;
      }

      location ~ ^\/(.*)_subapp\/$ {
        add_header 'Cache-Control' 'no-store';
        root $fePath;
        try_files /$1_subapp/index.html 404;
      }
      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
         expires 30m;
      }
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri  404;
         expires 7d;
      }
      location ~ \.php$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
            add_header 'Access-Control-Max-Age' '86400';
            if ($request_method = 'OPTIONS') {
                return 204;
            }    
         include fastcgi_params;
         fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
         fastcgi_param REQUEST_URI $php_url?$args;
         fastcgi_param REAL_REQUEST_URI $request_uri;
         fastcgi_pass   127.0.0.1:9000;
         opentracing_fastcgi_propagate_context;
      }
      location ~* ^/api(/.*)$ {
         set $php_url $1;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /stormsFury {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /interface {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /track {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /app {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location ^~ /prometheus {
         set $php_url $request_uri;
         try_files /$uri /index-beta.php?$args;
      }
      location ^~ /prometheusOversea {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }  

      location ^~ /prometheusPublish {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }  
      location /internal {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /assistant {
       set $php_url $request_uri;
       try_files $uri $uri/ /index-beta.php?$args;
      }
    }

  release.beta.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
    
      listen 80; ## listen for ipv4
      server_name *.release.beta.xiaoman.cn;
    
      if ($host !~* ^(.*)\.release\.beta\.xiaoman\.cn$) {
          return 404;
      }
    
      set $curPath $1;
      set $fePath /data/codebase/crm_root/beta/release/$curPath;
      set $defaultFePath /data/codebase/crm_root/beta/master;
      set $feTryFirst /$uri;
      set $feTrySecond /crm_main_shell_dist/$uri;
      set $feTryThird "";

      root       /data/codebase/crm_root/beta/release/$curPath/php-crm;
      index      crm_main_shell_dist/index.html;

      ##前端资源不存在，默认拉取master分支代码
      location @fe_default {
        root    $defaultFePath;
        try_files $feTryFirst $feTrySecond $feTryThird 404;
      }
    
      location / {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always;    
            add_header 'Access-Control-Max-Age' '86400';
            if ($request_method = 'OPTIONS') {
                return 204;
            }    
        root    $fePath;
        set $feTryThird /crm_main_shell_dist/index.html;
        try_files $feTryFirst $feTrySecond $feTryThird @fe_default;
      }
    
      location = /50x.html {
        root /usr/local/nginx/html;
      }

      location /healthz {
       access_log off;
       return 200;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always;    
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
      }

      location ~ \.(jpg|jpeg|gif|png|ico|svg)$ {
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
        expires 30m;
      }
    
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;    
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
        expires 7d;
      }
    
      location ~ ^\/web\/im-portal\/(.*) {
        root    $fePath;
        index     /im-portal/index.html;
        set $feTryFirst /im-portal/$1;
        set $feTrySecond /im-portal/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }

      location ~ ^\/tm\/(.*) {
        root    $fePath;
        index     /tm_subapp/index.html;
        set $feTryFirst /tm_subapp/$1;
        set $feTrySecond /tm_subapp/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }

      location ~ ^\/(.*)_subapp\/$ {
          add_header 'Cache-Control' 'no-store';
          root    $fePath;
          set $feTryFirst /$1_subapp/index.html;
          set $feTrySecond "";
          try_files $feTryFirst $feTrySecond @fe_default;
      }
      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
         expires 30m;

      location /protected {
        deny  all;
      }
    
      location ~ \.php$ {
        if (!-f $document_root$fastcgi_script_name) {
            root  /data/codebase/crm_root/beta/master/php-crm;
        }

        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always; 
            add_header 'Access-Control-Max-Age' '86400';
            if ($request_method = 'OPTIONS') {
                return 204;
            }    
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
        fastcgi_param REQUEST_URI $php_url?$args;
        fastcgi_param REAL_REQUEST_URI $request_uri;
        fastcgi_pass   127.0.0.1:9000;
        opentracing_fastcgi_propagate_context;
      }
    
      location ~ /\.(ht|svn|git) {
        deny all;
      }
    
      location ~* ^/api(/.*)$ {
        set $php_url $1;
        try_files $uri $uri/ /index-beta.php?$args;
      }
      location /stormsFury {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /interface {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /track {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /app {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
      location ^~ /prometheus {
        set $php_url $request_uri;
        try_files /$uri /index-beta.php?$args;
      }
      location ^~ /prometheusOversea {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }  

      location ^~ /prometheusPublish {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }      
      location ^~ /gii {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /internal {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /assistant {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
      location /tapdPlugin {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }    
    }

  hotfix-ames.beta.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
    
      listen 80; ## listen for ipv4
      server_name *.hotfix-ames.beta.xiaoman.cn;
    
      if ($host !~* ^(.*)\.hotfix-ames\.beta\.xiaoman\.cn$) {
          return 404;
      }
    
      set $curPath $1;
      set $fePath /data/codebase/ames_root/beta/hotfix/$curPath;
      set $defaultFePath /data/codebase/ames_root/beta/master;
      set $feTryFirst /$uri;
      set $feTrySecond /crm_main_shell_dist/$uri;
      set $feTryThird "";
    
      root       /data/codebase/ames_root/beta/hotfix/$curPath/php-crm;
      index      crm_main_shell_dist/index.html;

      ##前端资源不存在，默认拉取master分支代码
      location @fe_default {
        root    $defaultFePath;
        try_files $feTryFirst $feTrySecond $feTryThird 404;
      }
    
      location / {
        root    $fePath;
        set $feTryThird /crm_main_shell_dist/index.html;
        try_files $feTryFirst $feTrySecond $feTryThird @fe_default;
      }

      location = /50x.html {
         root /usr/local/nginx/html;
      }

      location /healthz {
         access_log off;
         return 200;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,x-xiaoman-platform-type' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Cache-Control' 'no-store';
    
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
      }

      location  =/ames-subapps.json {
        js_content main.extract_ames_entry;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }

      location  =/ames-v1-subapps.json {
        js_content main_v1.extract_ames_entry;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }

      location ~ (xm\.rt\.env\.js)$ {
        content_by_lua_file  /usr/local/openresty/nginx/conf/lua/xm_runtime_env.lua; 
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }
      location ~ (ames\.rt\.env\.js)$ {
        content_by_lua_file  /usr/local/openresty/nginx/conf/lua/ames_runtime_env.lua;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }
      location ~ (ames\.rt\.v1\.env\.js)$ {
        content_by_lua_file  /usr/local/openresty/nginx/conf/lua/ames_runtime_v1_env.lua;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }    
    
      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf)$ {
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
        expires 30m;
      }
    
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
        expires 7d;
      }
    
      location ~ ^\/web\/im-portal\/(.*) {
        root    $fePath;
        index     /im-portal/index.html;
        set $feTryFirst /im-portal/$1;
        set $feTrySecond /im-portal/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }

      location ~ ^\/tm\/(.*) {
        root    $fePath;
        index     /tm_subapp/index.html;
        set $feTryFirst /tm_subapp/$1;
        set $feTrySecond /tm_subapp/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }

      location ~ ^\/(.*)_subapp\/$ {
          add_header 'Cache-Control' 'no-store';
          root    $fePath;
          set $feTryFirst /$1_subapp/index.html;
          set $feTrySecond "";
          try_files $feTryFirst $feTrySecond @fe_default;
      }
    
      location /protected {
        deny  all;
      }
    
      location ~ \.php$ {

        if (!-f $document_root$fastcgi_script_name) {
            root  /data/codebase/crm_root/beta/master/php-crm;
        }

        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
    
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
        fastcgi_param REQUEST_URI $php_url?$args;
        fastcgi_param REAL_REQUEST_URI $request_uri;
        fastcgi_pass   127.0.0.1:9000;
      }
    
      location ~ /\.(ht|svn|git) {
        deny all;
      }
    
      location ~* ^/api(/.*)$ {
        set $php_url $1;
        try_files $uri $uri/ /index-beta.php?$args;
      }
      location /stormsFury {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /interface {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /track {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /app {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
      location ^~ /prometheus {
        set $php_url $request_uri;
        try_files /$uri /index-beta.php?$args;
      }
    
      location ^~ /gii {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /internal {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /assistant {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    }


  feature-ames.beta.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
      js_import main from /usr/local/openresty/nginx/conf/js/extract_ames_entry.js;
      js_import main_v1 from /usr/local/openresty/nginx/conf/js/extract_ames_entry_v1.js;
      # js_import main_v2 from extract_ames_entry_v2.js;
    
      listen 80; ## listen for ipv4
      server_name *.feature-ames.beta.xiaoman.cn;
    
      if ($host !~* ^(.*)\.feature-ames\.beta\.xiaoman\.cn$) {
          return 404;
      }
    
      set $curPath $1;
      set $fePath /data/codebase/ames_root/beta/feature/$curPath;
      set $defaultFePath /data/codebase/ames_root/beta/master;
      set $feTryFirst /$uri;
      set $feTrySecond /crm_main_shell_dist/$uri;
      set $feTryThird "";

      root       /data/codebase/ames_root/beta/feature/$curPath/php-crm;
      index      crm_main_shell_dist/index.html;

      ##前端资源不存在，默认拉取master分支代码
      location @fe_default {
        root    $defaultFePath;
        try_files $feTryFirst $feTrySecond $feTryThird 404;
      }
    
      location / {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
    
        root    $fePath;
        set $feTryThird /crm_main_shell_dist/index.html;
        try_files $feTryFirst $feTrySecond $feTryThird @fe_default;
      }
    
      location = /50x.html {
        root /usr/local/nginx/html;
      }

      location /healthz {
       access_log off;
       return 200;
      }
    
      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Cache-Control' 'no-store';
    
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
      }
    
      location  =/ames-subapps.json {
        js_content main.extract_ames_entry;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }

      location  =/ames-v1-subapps.json {
        js_content main_v1.extract_ames_entry;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }

      location ~ (xm\.rt\.env\.js)$ {
        content_by_lua_file  /usr/local/openresty/nginx/conf/lua/xm_runtime_env.lua; 
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }
      location ~ (ames\.rt\.env\.js)$ {
        content_by_lua_file  /usr/local/openresty/nginx/conf/lua/ames_runtime_env.lua;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }
      location ~ (ames\.rt\.v1\.env\.js)$ {
        content_by_lua_file  /usr/local/openresty/nginx/conf/lua/ames_runtime_v1_env.lua;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }                                       

      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf)$ {
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
        expires 30m;
      }
    
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;    
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
        expires 7d;
      }
    
      location ~ ^\/web\/im-portal\/(.*) {
        root    $fePath;
        index     /im-portal/index.html;
        set $feTryFirst /im-portal/$1;
        set $feTrySecond /im-portal/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }

      location ~ ^\/tm\/(.*) {
        root    $fePath;
        index     /tm_subapp/index.html;
        set $feTryFirst /tm_subapp/$1;
        set $feTrySecond /tm_subapp/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }

      location ~ ^\/(.*)_subapp\/$ {
          add_header 'Cache-Control' 'no-store';
          root    $fePath;
          set $feTryFirst /$1_subapp/index.html;
          set $feTrySecond "";
          try_files $feTryFirst $feTrySecond @fe_default;
      }

      location /protected {
        deny  all;
      }
    
      location ~ \.php$ {
        if (!-f $document_root$fastcgi_script_name) {
            root  /data/codebase/crm_root/beta/master/php-crm;
        }

        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
    
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
        fastcgi_param REQUEST_URI $php_url?$args;
        fastcgi_param REAL_REQUEST_URI $request_uri;
        fastcgi_pass   127.0.0.1:9000;
      }
    
      location ~ /\.(ht|svn|git) {
        deny all;
      }
    
      location ~* ^/api(/.*)$ {
        set $php_url $1;
        try_files $uri $uri/ /index-beta.php?$args;
      }
      location /stormsFury {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /interface {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /track {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /app {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
      location ^~ /prometheus {
        set $php_url $request_uri;
        try_files /$uri /index-beta.php?$args;
      }
    
      location ^~ /gii {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /internal {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /assistant {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    }

  bugfix-ames.beta.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
      listen 80; ## listen for ipv4
      #server_name ~^(?<home>[\w-]+)\.bugfix-ames\.beta\.xiaoman\.cn$;
      server_name *.bugfix-ames.beta.xiaoman.cn;
      if ($host !~* ^(.*)\.bugfix-ames\.beta\.xiaoman\.cn$) {
         return 404;
      }

      set $home $1;
      set $fePath /data/codebase/ames_root/beta/bugfix/$home;

      root        /data/codebase/ames_root/beta/bugfix/$home/php-crm;
      index       crm_main_shell_dist/index.html;

      location / {
          root $fePath;
          try_files /$uri /crm_main_shell_dist/$uri /crm_main_shell_dist/index.html 404;
      }

      location ~ \/(protected|k8s)\/ {
        deny all;
      }

      location ~ /\.(ht|svn|git) {
         deny all;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
         add_header 'Cache-Control' 'no-store';

         root    $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
      }

      location  =/ames-subapps.json {
        js_content main.extract_ames_entry;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }

      location  =/ames-v1-subapps.json {
        js_content main_v1.extract_ames_entry;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }

      location ~ (xm\.rt\.env\.js)$ {
        content_by_lua_file  /usr/local/openresty/nginx/conf/lua/xm_runtime_env.lua; 
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }
      location ~ (ames\.rt\.env\.js)$ {
        content_by_lua_file  /usr/local/openresty/nginx/conf/lua/ames_runtime_env.lua;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }
      location ~ (ames\.rt\.v1\.env\.js)$ {
        content_by_lua_file  /usr/local/openresty/nginx/conf/lua/ames_runtime_v1_env.lua;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }    

      location ~ ^\/web\/im-portal\/(.*) {
        index /im-portal/index.html;
        root $fePath;
        try_files /im-portal/$1 /im-portal/index.html 404;
      }

      location ~ ^\/tm\/(.*) {
        root    $fePath;
        index     /tm_subapp/index.html;
        try_files /tm_subapp/$1 /tm_subapp/index.html 404;
      }

      location ~ ^\/(.*)_subapp\/$ {
        add_header 'Cache-Control' 'no-store';
        root $fePath;
        try_files /$1_subapp/index.html 404;
      }

      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
         expires 30m;
      }

      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri  404;
         expires 7d;
      }

      location ~ \.php$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
         include fastcgi_params;
         fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
         fastcgi_param REQUEST_URI $php_url?$args;
         fastcgi_param REAL_REQUEST_URI $request_uri;
         fastcgi_pass   127.0.0.1:9000;
      }
      location ~* ^/api(/.*)$ {
         set $php_url $1;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /stormsFury {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /interface {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /track {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /app {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location ^~ /prometheus {
         set $php_url $request_uri;
         try_files /$uri /index-beta.php?$args;
      }
      location /internal {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /assistant {
       set $php_url $request_uri;
       try_files $uri $uri/ /index-beta.php?$args;
      }
    }
  master-ames.beta.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
      listen 80; ## listen for ipv4

      server_name master-ames.beta.xiaoman.cn;

      set $fePath /data/codebase/crm_root/beta/master;

      root        /data/codebase/ames_root/beta/master/php-crm;
      index       /dist/index.html;

      location / {
          root $fePath;
          try_files /$uri /crm_main_shell_dist/$uri /crm_main_shell_dist/index.html 404;
      }

      location ~ \/(protected|k8s)\/ {
        deny all;
      }

      location ~ /\.(ht|svn|git) {
         deny all;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
         add_header 'Cache-Control' 'no-store';

         root    $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
      }
    
      location  =/ames-subapps.json {
        js_content main.extract_ames_entry;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }

      location  =/ames-v1-subapps.json {
        js_content main_v1.extract_ames_entry;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }

      location ~ (xm\.rt\.env\.js)$ {
        content_by_lua_file  /usr/local/openresty/nginx/conf/lua/xm_runtime_env.lua; 
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }
      location ~ (ames\.rt\.env\.js)$ {
        content_by_lua_file  /usr/local/openresty/nginx/conf/lua/ames_runtime_env.lua;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }
      location ~ (ames\.rt\.v1\.env\.js)$ {
        content_by_lua_file  /usr/local/openresty/nginx/conf/lua/ames_runtime_v1_env.lua;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }    

      location ~ ^\/web\/im-portal\/(.*) {
        index /im-portal/index.html;
        root $fePath;
        try_files /im-portal/$1 /im-portal/index.html 404;
      }

      location ~ ^\/tm\/(.*) {
        root    $fePath;
        index     /tm_subapp/index.html;
        try_files /tm_subapp/$1 /tm_subapp/index.html 404;
      }

      location ~ ^\/(.*)_subapp\/$ {
        add_header 'Cache-Control' 'no-store';
        root $fePath;
        try_files /$1_subapp/index.html 404;
      }
      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
         expires 30m;
      }
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri  404;
         expires 7d;
      }
      location ~ \.php$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
         include fastcgi_params;
         fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
         fastcgi_param REQUEST_URI $php_url?$args;
         fastcgi_param REAL_REQUEST_URI $request_uri;
         fastcgi_pass   127.0.0.1:9000;
      }
      location ~* ^/api(/.*)$ {
         set $php_url $1;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /stormsFury {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /interface {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /track {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /app {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location ^~ /prometheus {
         set $php_url $request_uri;
         try_files /$uri /index-beta.php?$args;
      }
      location /internal {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /assistant {
       set $php_url $request_uri;
       try_files $uri $uri/ /index-beta.php?$args;
      }
    }

  release-ames.beta.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
      listen 80; ## listen for ipv4

      server_name release-ames.beta.xiaoman.cn;

      set $fePath /data/codebase/crm_root/beta/release/dev;

      root       /data/codebase/crm_root/beta/release/dev/php-crm;
      index       /dist/index.html;

      location / {
          root $fePath;
          try_files /$uri /crm_main_shell_dist/$uri /crm_main_shell_dist/index.html 404;
      }

      location ~ \/(protected|k8s)\/ {
        deny all;
      }

      location ~ /\.(ht|svn|git) {
         deny all;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
         add_header 'Cache-Control' 'no-store';

         root    $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
      }
    
      location  =/ames-subapps.json {
        js_content main.extract_ames_entry;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }

      location  =/ames-v1-subapps.json {
        js_content main_v1.extract_ames_entry;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }

      location ~ (xm\.rt\.env\.js)$ {
        content_by_lua_file  /usr/local/openresty/nginx/conf/lua/xm_runtime_env.lua; 
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }
      location ~ (ames\.rt\.env\.js)$ {
        content_by_lua_file  /usr/local/openresty/nginx/conf/lua/ames_runtime_env.lua;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }
      location ~ (ames\.rt\.v1\.env\.js)$ {
        content_by_lua_file  /usr/local/openresty/nginx/conf/lua/ames_runtime_v1_env.lua;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
        add_header 'Access-Control-Allow-Origin' "$http_origin";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Cache-Control' 'no-cache';
      }    

      location ~ ^\/web\/im-portal\/(.*) {
        index /im-portal/index.html;
        root $fePath;
        try_files /im-portal/$1 /im-portal/index.html 404;
      }

      location ~ ^\/tm\/(.*) {
        root    $fePath;
        index     /tm_subapp/index.html;
        try_files /tm_subapp/$1 /tm_subapp/index.html 404;
      }

      location ~ ^\/(.*)_subapp\/$ {
        add_header 'Cache-Control' 'no-store';
        root $fePath;
        try_files /$1_subapp/index.html 404;
      }
      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
         expires 30m;
      }
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri  404;
         expires 7d;
      }
      location ~ \.php$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
         include fastcgi_params;
         fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
         fastcgi_param REQUEST_URI $php_url?$args;
         fastcgi_param REAL_REQUEST_URI $request_uri;
         fastcgi_pass   127.0.0.1:9000;
      }
      location ~* ^/api(/.*)$ {
         set $php_url $1;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /stormsFury {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /interface {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /track {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /app {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location ^~ /prometheus {
         set $php_url $request_uri;
         try_files /$uri /index-beta.php?$args;
      }
      location /internal {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-beta.php?$args;
      }
      location /assistant {
       set $php_url $request_uri;
       try_files $uri $uri/ /index-beta.php?$args;
      }
    }

  tapd-plugin.conf: |-
    #user  nobody;
    worker_processes  1;
    worker_rlimit_nofile 65535;
    #error_log  logs/error.log;
    #error_log  logs/error.log  notice;
    #error_log  logs/error.log  info;

    #pid        logs/nginx.pid;
    events {
        worker_connections   4096;
    }

    # load modules compiled as Dynamic Shared Object (DSO)
    #
    #dso {
    #    load ngx_http_fastcgi_module.so;
    #    load ngx_http_rewrite_module.so;
    #}

    http {
        include       mime.types;
        default_type  application/octet-stream;
        server_names_hash_bucket_size 128;
        log_format access_log '$remote_addr|$remote_user|$time_local|$http_host|$request|$status|$body_bytes_sent|$http_referer|$http_user_agent|$request_time|$proxy_add_x_forwarded_for|$upstream_addr|$cookie_clientId|$cookie_userId|$cookie_pskey|$cookie_fingerprint|$http_x_forwarded_for|$request_length|$http_xiaoman_client_id|$http_xiaoman_user_id|$http_xiaoman_pskey|$http_xiaoman_device_id|$http_xiaoman_os|$http_xiaoman_os_version|$http_x-b3_traceid|$http_x_b3_spanid';

        access_log  /dev/stdout  access_log;
        error_log  /data/logs/error.log;

        sendfile        on;
        #tcp_nopush     on;

        #keepalive_timeout  0;
        keepalive_timeout  65;

        gzip  on;
        gzip_min_length 10k;
        gzip_buffers 4 16k;
        gzip_http_version 1.1;
        gzip_comp_level 2;
        gzip_types application/javascript application/x-javascript text/css text/javascript application/json text/plain text/xml application/x-protobuf;
        gzip_vary on;
    
        server {
          charset utf-8;
          client_max_body_size 128M;
          listen 80; ## listen for ipv4
          server_name  _;
    
          root        /data/codebase/crm_root/production/php-crm/;
          index       index-beta.php;
          location / {  
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Cache-Control' 'no-cache';
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
          }
          location = /50x.html {
              root /usr/local/nginx/html;
          }
          location /healthz {
              access_log off;
              return 200;
          }
          location /fpm_status {
            access_log off;
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME "";
            fastcgi_pass 127.0.0.1:9000;
          }
          location /ping {
            access_log off;
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME "/ping";
            fastcgi_param SCRIPT_NAME "/ping";
            fastcgi_pass 127.0.0.1:9000;
          }    
          location ~ \/(protected|k8s)\/ {
            deny all;
          }
          location ~ /\.(ht|svn|git) {
            deny all;
          }  
          location ~ \.php$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;

            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';

            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
            fastcgi_param REQUEST_URI $php_url?$args;
            fastcgi_param REAL_REQUEST_URI $request_uri;
            fastcgi_pass   127.0.0.1:9000;
          }
    
          location ~* ^/api(/.*)$ {
            set $php_url $1;
            try_files $uri $uri/ /index-beta.php?$args;
          }
          location /tapdPlugin {
            set $php_url $request_uri;
            try_files $uri $uri/ /index-beta.php?$args;
          }
        }
    }
  story.beta.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
    
      listen 80; ## listen for ipv4
      server_name *.story.beta.xiaoman.cn;
    
      if ($host !~* ^(.*)\.story\.beta\.xiaoman\.cn$) {
      return 404;
      }
      
      set $curPath $1;
      set $fePath /data/codebase/crm_root/beta/story/$curPath;
      set $defaultFePath /data/codebase/crm_root/beta/master;
      
      if ($host !~* ^([a-zA-z0-9]+-[a-zA-z0-9]+-[0-9]+-[0-9]+)-([0-9]+)\.story\.beta\.xiaoman\.cn$) {
      return 404;
      }
      
      set $iterationName $1;
      set $storyId $2;
      set $feIterationPath /data/codebase/crm_root/beta/release/$iterationName;
      
      set $feTryFirst /$uri;
      set $feTrySecond /crm_main_shell_dist/$uri;
      set $feTryThird "";
      
      root /data/codebase/crm_root/beta/story/$curPath/php-crm;
      index web/dist/index.html;
    
      ##story和release前端资源不存在，默认拉取master分支代码
      location @fe_default {
        root    $defaultFePath;
        try_files $feTryFirst $feTrySecond $feTryThird 404;
      }
    
      ##story前端资源不存在，拉取release分支代码
      location @iteration_fe {
        root    $feIterationPath;
        try_files $feTryFirst $feTrySecond $feTryThird @fe_default;
      }
    
      location / {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always;    
            add_header 'Access-Control-Max-Age' '86400';
            if ($request_method = 'OPTIONS') {
                return 204;
            }    
        root    $fePath;
        set $feTryThird /crm_main_shell_dist/index.html;
        try_files $feTryFirst $feTrySecond $feTryThird @iteration_fe;
      }
    
      location = /50x.html {
        root /usr/local/nginx/html;
      }

      location /healthz {
       access_log off;
       return 200;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always;    
        root    $fePath;
        try_files $feTryFirst $feTrySecond @iteration_fe;
      }

      location ~ \.(jpg|jpeg|gif|png|ico|svg)$ {
        root    $fePath;
        try_files $feTryFirst $feTrySecond @iteration_fe;
        expires 30m;
      }
    
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;    
        root    $fePath;
        try_files $feTryFirst $feTrySecond @iteration_fe;
        expires 7d;
      }
    
      location ~ ^\/web\/im-portal\/(.*) {
        root    $fePath;
        index     /im-portal/index.html;
        set $feTryFirst /im-portal/$1;
        set $feTrySecond /im-portal/index.html;
        try_files $feTryFirst $feTrySecond @iteration_fe;
      }

      location ~ ^\/tm\/(.*) {
        root    $fePath;
        index     /tm_subapp/index.html;
        set $feTryFirst /tm_subapp/$1;
        set $feTrySecond /tm_subapp/index.html;
        try_files $feTryFirst $feTrySecond @iteration_fe;
      }

      location ~ ^\/(.*)_subapp\/$ {
          add_header 'Cache-Control' 'no-store';
          root    $fePath;
          set $feTryFirst /$1_subapp/index.html;
          set $feTrySecond "";
          try_files $feTryFirst $feTrySecond @iteration_fe;
      }

      location /protected {
        deny  all;
      }
    
      location ~ \.php$ {    
        if (!-f $document_root$fastcgi_script_name) {
          root /data/codebase/crm_root/beta/release/$iterationName/php-crm;
        }
    
        if (!-f $document_root$fastcgi_script_name) {
          root  /data/codebase/crm_root/beta/master/php-crm;
        }

        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always; 
            add_header 'Access-Control-Max-Age' '86400';
            if ($request_method = 'OPTIONS') {
                return 204;
            }    
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
        fastcgi_param REQUEST_URI $php_url?$args;
        fastcgi_param REAL_REQUEST_URI $request_uri;
        fastcgi_pass   127.0.0.1:9000;
        opentracing_fastcgi_propagate_context;
      }
    
      location ~ /\.(ht|svn|git) {
        deny all;
      }
    
      location ~* ^/api(/.*)$ {
        set $php_url $1;
        try_files $uri $uri/ /index-beta.php?$args;
      }
      location /stormsFury {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /interface {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /track {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /app {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
      location ^~ /prometheus {
        set $php_url $request_uri;
        try_files /$uri /index-beta.php?$args;
      }
      location ^~ /prometheusOversea {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }  

      location ^~ /prometheusPublish {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }      
      location ^~ /gii {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /internal {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
    
      location /assistant {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }
      location /tapdPlugin {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-beta.php?$args;
      }    
    }
