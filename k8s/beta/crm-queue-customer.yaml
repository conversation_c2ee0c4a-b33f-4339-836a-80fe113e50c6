apiVersion: apps/v1
kind: Deployment
metadata:
  name: crm-queue-customer-beta
  namespace: app-crm
  labels:
    app: crm-queue-customer-beta
    developer: PHP-CRM
    lang: php
spec:
  selector:
    matchLabels:
      app: crm-queue-customer-beta
  replicas: 1
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        service: crm-queue-customer
        app_env: beta
        sidecar-injection: enabled
        app: crm-queue-customer-beta
      annotations:
        sidecarConfigName: crm-queue-beta
    spec:
      nodeSelector:
        xiaoman.cn/server-type: beta-php-server
      tolerations:
        - key: "xiaoman.cn/server-type"
          operator: "Equal"
          value: "beta-php-server"
          effect: "NoSchedule"
      containers:
        - name: crm-queue-customer-beta
          #imagePullPolicy: IfNotPresent
          imagePullPolicy: Always
          image: 'xiaoman-registry-vpc.cn-hangzhou.cr.aliyuncs.com/xiaoman-php/crm:k8s-grey-whatsapp315-20230324-v6'
          command: [ '/usr/local/php/bin/php', '-c/usr/local/php/etc/php-cli.ini', '/data/codebase/crm_root/beta/php-crm/protected/vendor/bin/queue', 'worker:listen', '--env=beta', '--channel=customer_job', '--workers=4']
          lifecycle:
            preStop:
              exec:
                command: [ '/usr/local/php/bin/php', '-c/usr/local/php/etc/php-cli.ini', '/data/codebase/crm_root/beta/php-crm/protected/vendor/bin/queue', 'worker:stop', '--env=beta', '--channel=customer_job', '--force=1', '--wait=300' ]
          env:
            - name: RUNTIME_ENV
              value: 'k8s'
            - name: aliyun_logs_queue-service
              value: stdout
            - name: aliyun_logs_queue-service_tags
              value: app=queue
            - name: aliyun_logs_php-error
              value: /data/logs/php_errors.log
            - name: aliyun_logs_php-error_tags
              value: app=queue
            - name: CLI.grpc.enable_fork_support
              value: '0'
            - name: PHP.opcache.huge_code_pages
              value: '0'
            - name: CLI.opcache.huge_code_pages
              value: '0'
            - name: SIDECAR_ENABLE
              value: '1'
          volumeMounts:
          resources:
            requests:
              cpu: "0.02"
              memory: "0.064Gi"
            limits:
              cpu: "0.5"
              memory: "2Gi"
          readinessProbe:
            exec:
              command: [ 'sh', '-c', 'pgrep -f channel=customer_job' ]
            initialDelaySeconds: 10
            periodSeconds: 10
          livenessProbe:
            initialDelaySeconds: 10
            periodSeconds: 60
            exec:
              command: [ 'sh', '-c', 'pgrep -f channel=customer_job' ]
          securityContext:
            privileged: true
            capabilities:
              add: [ "SYS_ADMIN" ,"SYS_PTRACE"]
            allowPrivilegeEscalation: true
      dnsPolicy: None
      dnsConfig:
        nameservers:
          - 169.254.20.10
          - 192.168.96.10
        searches:
          - app-crm.svc.cluster.local
          - svc.cluster.local
          - cluster.local
        options:
          - name: ndots
            value: '3'
          - name: timeout
            value: '1'
          - name: attempts
            value: '2'
          - name: single-request-reopen
      volumes: