apiVersion: apps/v1
kind: Deployment
metadata:
  name: crm-queue-slow-dev
  namespace: app-crm
  labels:
    app: crm-queue-slow-dev
    developer: PHP-CRM
    lang: php
spec:
  selector:
    matchLabels:
      app: crm-queue-slow-dev
  replicas: 1
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app_env: dev
        sidecar-injection: enabled
        app: crm-queue-slow-dev
      annotations:
        sidecarConfigName: crm-queue-dev
    spec:
      nodeSelector:
        xiaoman.cn/server-type: php-server
      tolerations:
        - key: "xiaoman.cn/server-type"
          operator: "Equal"
          value: "dev-php-server"
          effect: "NoSchedule"
      initContainers:
        - command:
            - /bin/sh
            - '-c'
            - |
              cp /usr/bin/ossutil64 /var/bin
          image: 'xiaoman-registry-vpc.cn-hangzhou.cr.aliyuncs.com/xiaoman-php/ossutil:base'
          imagePullPolicy: IfNotPresent
          name: init-ossutil
          volumeMounts:
            - name: volume-ossutil
              mountPath: /var/bin
      containers:
        - name: crm-queue-slow-dev
          #imagePullPolicy: IfNotPresent
          imagePullPolicy: Always
          image: 'xiaoman-registry-vpc.cn-hangzhou.cr.aliyuncs.com/xiaoman-php/php8:rockylinux'
          command: ['/usr/local/php/bin/php', '-c/usr/local/php/etc/php-cli.ini', '/data/codebase/crm_root/dev/master/php-crm/protected/vendor/bin/queue', 'worker:listen', '--env=test', '--channel=slow_job', '--workers=4']
          lifecycle:
            preStop:
              exec:
                command: [ '/usr/local/php/bin/php', '-c/usr/local/php/etc/php-cli.ini', '/data/codebase/crm_root/dev/master/php-crm/protected/vendor/bin/queue', 'worker:stop', '--env=test', '--channel=slow_job', '--force=1', '--wait=300' ]
          env:
            - name: RUNTIME_ENV
              value: 'k8s'
            - name: aliyun_logs_queue-service
              value: stdout
            - name: aliyun_logs_queue-service_tags
              value: app=queue
            - name: aliyun_logs_php-error
              value: /data/logs/php_errors.log
            - name: aliyun_logs_php-error_tags
              value: app=queue
            - name: CLI.grpc.enable_fork_support
              value: '0'
            - name: PHP.opcache.huge_code_pages
              value: '0'
            - name: CLI.opcache.huge_code_pages
              value: '0'
            - name: SIDECAR_ENABLE
              value: '1'
          volumeMounts:
            - name: volume-data
              mountPropagation: "Bidirectional"
              mountPath: /data/codebase/crm_root/dev
            - name: volume-ossutil
              mountPath: /data/bin
          resources:
            requests:
              cpu: "0.02"
              memory: "0.064Gi"
              ephemeral-storage: "100Mi"
            limits:
              cpu: "0.5"
              memory: "2Gi"
              ephemeral-storage: "20Gi"
          readinessProbe:
            exec:
              command: [ 'sh', '-c', 'pgrep -f channel=slow_job' ]
            initialDelaySeconds: 10
            periodSeconds: 10
          livenessProbe:
            initialDelaySeconds: 10
            periodSeconds: 60
            exec:
              command: [ 'sh', '-c', 'pgrep -f channel=slow_job' ]
          securityContext:
            privileged: true
            capabilities:
              add: [ "SYS_ADMIN" ,"SYS_PTRACE"]
            allowPrivilegeEscalation: true
      terminationGracePeriodSeconds: 30
      dnsPolicy: None
      dnsConfig:
        nameservers:
          - *************
          - *************
        searches:
          - app-crm.svc.cluster.local
          - svc.cluster.local
          - cluster.local
        options:
          - name: ndots
            value: '3'
          - name: timeout
            value: '1'
          - name: attempts
            value: '2'
          - name: single-request-reopen
      volumes:
        - name: volume-uds
          emptyDir: { }
        - name: volume-data
          hostPath:
            path: /data/codebase/crm_root/dev
            type: DirectoryOrCreate
        - name: volume-ossutil
          emptyDir: { }