apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-configmap
  namespace: app-crm
  labels:
    app: nginx
    developer: PHP-CRM
data:
  nginx.conf: |-
    #user  nobody;
    worker_processes  1;
    #error_log  logs/error.log;
    #error_log  logs/error.log  notice;
    #error_log  logs/error.log  info;

    #pid        logs/nginx.pid;
    load_module /usr/local/nginx/nginx-opentracing/ngx_http_opentracing_module.so;
    events {
        worker_connections  2048;
    }

    # load modules compiled as Dynamic Shared Object (DSO)
    #
    #dso {
    #    load ngx_http_fastcgi_module.so;
    #    load ngx_http_rewrite_module.so;
    #}
    
    http {
        include       mime.types;
        default_type  application/octet-stream;
        server_names_hash_bucket_size 128;
        log_format access_log '$remote_addr|$remote_user|$time_local|$http_host|$request|$status|$body_bytes_sent|$http_referer|$http_user_agent|$request_time|$proxy_add_x_forwarded_for|$upstream_addr|$cookie_clientId|$cookie_userId|$cookie_pskey|$cookie_fingerprint|$http_x_forwarded_for|$request_length|$http_xiaoman_client_id|$http_xiaoman_user_id|$http_xiaoman_pskey|$http_xiaoman_device_id|$http_xiaoman_os|$http_xiaoman_os_version|$http_x_xiaoman_env|$http_x_xiaoman_platform_type|$opentracing_context_x_b3_spanid|$opentracing_context_x_b3_traceid';
        access_log  /dev/stdout  access_log;
        error_log  /data/logs/error.log;
    
    
        opentracing on;
        opentracing_load_tracer  /usr/local/nginx/nginx-opentracing/linux-amd64-libzipkin_opentracing_plugin.so /usr/local/nginx/nginx-opentracing/zipkin-config.json;
        opentracing_location_operation_name "HTTP $request_method $host";
        opentracing_operation_name "HTTP $request_method $host";
        opentracing_trace_locations off;
        opentracing_propagate_context; 
    
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid always;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid always;    
        
        sendfile        on;
        #tcp_nopush     on;

        #keepalive_timeout  0;
        keepalive_timeout  65;

        gzip  on;
        gzip_min_length 10k;
        gzip_buffers 4 16k;
        gzip_http_version 1.1;
        gzip_comp_level 2;
        gzip_types application/javascript application/x-javascript text/css text/javascript application/json text/plain text/xml application/x-protobuf;
        gzip_vary on;
    
        server {
          charset utf-8;
          client_max_body_size 128M;
          listen 80; ## listen for ipv4
          set $php_index index-test.php;
          server_name _;
          root        /data/codebase/crm_root/dev/release/dev/php-crm;
          index       $php_index;
          location / {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,x-xiaoman-platform-type';
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Cache-Control' 'no-cache';
            #先服务nginx返回, 后续再考虑交接给网关处理
            add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
            add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;
            set $php_url $request_uri;
            try_files $uri $uri/ /$php_index?$args;
          }

          location /healthz {
              access_log off;
              return 200;
          }
          location ~ \/(protected|k8s)\/ {
            deny all;
          }
          location ~ /\.(ht|svn|git) {
            deny all;
          }  
          location ~ \.php$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';

            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
            fastcgi_param REQUEST_URI $php_url?$args;
            fastcgi_param REAL_REQUEST_URI $request_uri;
            fastcgi_pass   127.0.0.1:9000;
            opentracing_fastcgi_propagate_context;
          }
    
          location ~* ^/api(/.*)$ {
            set $php_url $1;
            try_files $uri $uri/ /$php_index?$args;
          }

          location  /fpm_status {
            access_log off;
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME "";
            fastcgi_pass 127.0.0.1:9000;
          }
    
          location  /ping {
            access_log off;
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME "/ping";
            fastcgi_param SCRIPT_NAME "/ping";
            fastcgi_pass 127.0.0.1:9000;
          }
        }

        include /usr/local/nginx/conf/vhost.d/*.conf;
    }

  hotfix.dev.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
    
      listen 80; ## listen for ipv4
      server_name *.hotfix.dev.xiaoman.cn;
    
      if ($host !~* ^(.*)\.hotfix\.dev\.xiaoman\.cn$) {
          return 404;
      }
    
      set $curPath $1;
      set $fePath /data/codebase/crm_root/dev/hotfix/$curPath;
      set $defaultFePath /data/codebase/crm_root/dev/master;
      set $feTryFirst /$uri;
      set $feTrySecond /crm_main_shell_dist/$uri;
      set $feTryThird "";
    
      root       /data/codebase/crm_root/dev/hotfix/$curPath/php-crm;
      index      web/dist/index.html;

      ##前端资源不存在，默认拉取master分支代码
      location @fe_default {
        root    $defaultFePath;
        try_files $feTryFirst $feTrySecond $feTryThird 404;
      }
    
      location / {
        root    $fePath;
        set $feTryThird /crm_main_shell_dist/index.html;
        try_files $feTryFirst $feTrySecond $feTryThird @fe_default;
      }

      location = /50x.html {
         root /usr/local/nginx/html;
      }

      location /healthz {
         access_log off;
         return 200;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,x-xiaoman-platform-type' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
      }
    
      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf|bin)$ {
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
        expires 30m;
      }
    
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
        expires 7d;
      }
    
      location ~ ^\/web\/im-portal\/(.*) {
        root    $fePath;
        index     /im-portal/index.html;
        set $feTryFirst /im-portal/$1;
        set $feTrySecond /im-portal/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }
    
      location ~ ^\/web\/mobile\/(.*) {
        root    $fePath;
        index     /mobile_web/index.html;
        set $feTryFirst /mobile_web/$1;
        set $feTrySecond /mobile_web/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }    
    
      location ~ ^\/tm\/(.*) {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;    
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
        root    $fePath;
        index     /tm_subapp/index.html;
        set $feTryFirst /tm_subapp/$1;
        set $feTrySecond /tm_subapp/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }

      location ~ ^\/(.*)_subapp\/(.*){
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;    
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
          root    $fePath;
          index   /$1_subapp/index.html;
          set $feTryFirst /$1_subapp/$2;
          set $feTrySecond /$1_subapp/index.html;
          try_files $feTryFirst $feTrySecond @fe_default;
      }
    
      location /protected {
        deny  all;
      }
    
      location ~ \.php$ {

        if (!-f $document_root$fastcgi_script_name) {
            root  /data/codebase/crm_root/dev/master/php-crm;
        }

        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;
    
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
        fastcgi_param REQUEST_URI $php_url?$args;
        fastcgi_param REAL_REQUEST_URI $request_uri;
        fastcgi_pass   127.0.0.1:9000;
        opentracing_fastcgi_propagate_context;
      }
    
      location ~ /\.(ht|svn|git) {
        deny all;
      }
    
      location ~* ^/api(/.*)$ {
        set $php_url $1;
        try_files $uri $uri/ /index-test.php?$args;
      }
      location /stormsFury {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /interface {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /track {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /app {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
      location ^~ /prometheus {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }
    
      location ^~ /prometheusOversea {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }  

      location ^~ /prometheusPublish {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }  
    
      location ^~ /gii {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /internal {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /assistant {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
      
      location /tapdPlugin {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }    
    }
    

  feature.dev.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
    
      listen 80; ## listen for ipv4
      server_name *.feature.dev.xiaoman.cn;
    
      if ($host !~* ^(.*)\.feature\.dev\.xiaoman\.cn$) {
          return 404;
      }
    
      set $curPath $1;
      set $fePath /data/codebase/crm_root/dev/feature/$curPath;
      set $defaultFePath /data/codebase/crm_root/dev/master;
      set $feTryFirst /$uri;
      set $feTrySecond /crm_main_shell_dist/$uri;
      set $feTryThird "";

      root       /data/codebase/crm_root/dev/feature/$curPath/php-crm;
      index      web/dist/index.html;
      
      ##前端资源不存在，默认拉取master分支代码
      location @fe_default {
        root    $defaultFePath;
        try_files $feTryFirst $feTrySecond $feTryThird 404;
      }
    
      location / {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;        
        root    $fePath;
        set $feTryThird /crm_main_shell_dist/index.html;
        try_files $feTryFirst $feTrySecond $feTryThird @fe_default;
      }
    
      location = /50x.html {
        root /usr/local/nginx/html;
      }

      location /healthz {
       access_log off;
       return 200;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
      }

      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf|bin)$ {
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
        expires 30m;
      }
    
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
        expires 7d;
      }
    
      location ~ ^\/web\/im-portal\/(.*) {
        root    $fePath;
        index     /im-portal/index.html;
        set $feTryFirst /im-portal/$1;
        set $feTrySecond /im-portal/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }
    
      location ~ ^\/web\/mobile\/(.*) {
        root    $fePath;
        index     /mobile_web/index.html;
        set $feTryFirst /mobile_web/$1;
        set $feTrySecond /mobile_web/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }    

      location ~ ^\/tm\/(.*) {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
         #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;   
        root    $fePath;
        index     /tm_subapp/index.html;
        set $feTryFirst /tm_subapp/$1;
        set $feTrySecond /tm_subapp/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }

      location ~ ^\/(.*)_subapp\/(.*){
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;    
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
          root    $fePath;
          index   /$1_subapp/index.html;
          set $feTryFirst /$1_subapp/$2;
          set $feTrySecond /$1_subapp/index.html;
          try_files $feTryFirst $feTrySecond @fe_default;
      }

      location /protected {
        deny  all;
      }
    
      location ~ \.php$ {
        if (!-f $document_root$fastcgi_script_name) {
            root  /data/codebase/crm_root/dev/master/php-crm;
        }

        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
         #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;   
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
        fastcgi_param REQUEST_URI $php_url?$args;
        fastcgi_param REAL_REQUEST_URI $request_uri;
        fastcgi_pass   127.0.0.1:9000;
        opentracing_fastcgi_propagate_context;
      }
    
      location ~ /\.(ht|svn|git) {
        deny all;
      }
    
      location ~* ^/api(/.*)$ {
        set $php_url $1;
        try_files $uri $uri/ /index-test.php?$args;
      }
      location /stormsFury {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /interface {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /track {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /app {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
      location ^~ /prometheus {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }
    
      location ^~ /prometheusOversea {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }  

      location ^~ /prometheusPublish {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }  
    
      location ^~ /gii {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /internal {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /assistant {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
      location /tapdPlugin {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }    
    }

  bugfix.dev.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
      listen 80; ## listen for ipv4
      #server_name ~^(?<home>[\w-]+)\.bugfix\.dev\.xiaoman\.cn$;
      server_name *.bugfix.dev.xiaoman.cn;
      if ($host !~* ^(.*)\.bugfix\.dev\.xiaoman\.cn$) {
         return 404;
      }

      set $curPath $1;
      set $fePath /data/codebase/crm_root/dev/bugfix/$curPath;
      set $defaultFePath /data/codebase/crm_root/dev/master;
      set $feTryFirst /$uri;
      set $feTrySecond /crm_main_shell_dist/$uri;
      set $feTryThird "";

      root       /data/codebase/crm_root/dev/bugfix/$curPath/php-crm;
      index      web/dist/index.html;

      ##前端资源不存在，默认拉取master分支代码
      location @fe_default {
        root    $defaultFePath;
        try_files $feTryFirst $feTrySecond $feTryThird 404;
      }    
    
      location / {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
        root    $fePath;
        set $feTryThird /crm_main_shell_dist/index.html;
        try_files $feTryFirst $feTrySecond $feTryThird @fe_default;
      }
    
      location = /50x.html {
        root /usr/local/nginx/html;
      }

      location /healthz {
       access_log off;
       return 200;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
      }

      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf|bin)$ {
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
        expires 30m;
      }
    
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
        expires 7d;
      }
    
      location ~ ^\/web\/im-portal\/(.*) {
        root    $fePath;
        index     /im-portal/index.html;
        set $feTryFirst /im-portal/$1;
        set $feTrySecond /im-portal/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }
    
      location ~ ^\/web\/mobile\/(.*) {
        root    $fePath;
        index     /mobile_web/index.html;
        set $feTryFirst /mobile_web/$1;
        set $feTrySecond /mobile_web/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }    

      location ~ ^\/tm\/(.*) {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
        root    $fePath;
        index     /tm_subapp/index.html;
        set $feTryFirst /tm_subapp/$1;
        set $feTrySecond /tm_subapp/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }

      location ~ ^\/(.*)_subapp\/(.*){
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;    
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
          root    $fePath;
          index   /$1_subapp/index.html;
          set $feTryFirst /$1_subapp/$2;
          set $feTrySecond /$1_subapp/index.html;
          try_files $feTryFirst $feTrySecond @fe_default;
      }

      location /protected {
        deny  all;
      }
    
      location ~ \.php$ {
        if (!-f $document_root$fastcgi_script_name) {
            root  /data/codebase/crm_root/dev/master/php-crm;
        }

        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
        fastcgi_param REQUEST_URI $php_url?$args;
        fastcgi_param REAL_REQUEST_URI $request_uri;
        fastcgi_pass   127.0.0.1:9000;
        opentracing_fastcgi_propagate_context;
      }
    
      location ~ /\.(ht|svn|git) {
        deny all;
      }
    
      location ~* ^/api(/.*)$ {
        set $php_url $1;
        try_files $uri $uri/ /index-test.php?$args;
      }
      location /stormsFury {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /interface {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /track {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /app {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
      location ^~ /prometheus {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }
      location ^~ /prometheusOversea {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }  

      location ^~ /prometheusPublish {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }      
      location ^~ /gii {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /internal {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /assistant {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
      location /tapdPlugin {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }    
    }

  master.dev.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
      listen 80; ## listen for ipv4

      server_name master.dev.xiaoman.cn;

      set $fePath /data/codebase/crm_root/dev/master;

      root        /data/codebase/crm_root/dev/master/php-crm;
      index       /dist/index.html;

      location / {
          root $fePath;
          try_files /$uri /crm_main_shell_dist/$uri /crm_main_shell_dist/index.html 404;
      }

      location ~ \/(protected|k8s)\/ {
        deny all;
      }

      location ~ /\.(ht|svn|git) {
         deny all;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
         add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;
         root    $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
      }

      location ~ ^\/web\/im-portal\/(.*) {
        index /im-portal/index.html;
        root $fePath;
        try_files /im-portal/$1 /im-portal/index.html 404;
      }
    
      location ~ ^\/web\/mobile\/(.*) {
        root    $fePath;
        index     /mobile_web/index.html;
        set $feTryFirst /mobile_web/$1;
        set $feTrySecond /mobile_web/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }    

      location ~ ^\/tm\/(.*) {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
        root    $fePath;
        index     /tm_subapp/index.html;
        try_files /tm_subapp/$1 /tm_subapp/index.html 404;
      }

      location ~ ^\/(.*)_subapp\/(.*){
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;    
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
          root    $fePath;
          index   /$1_subapp/index.html;
          set $feTryFirst /$1_subapp/$2;
          set $feTrySecond /$1_subapp/index.html;
          try_files $feTryFirst $feTrySecond @fe_default;
      }
      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf|bin)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
         expires 30m;
      }
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri  404;
         expires 7d;
      }
      location ~ \.php$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
         include fastcgi_params;
         fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
         fastcgi_param REQUEST_URI $php_url?$args;
         fastcgi_param REAL_REQUEST_URI $request_uri;
         fastcgi_pass   127.0.0.1:9000;
         opentracing_fastcgi_propagate_context;
      }
      location ~* ^/api(/.*)$ {
         set $php_url $1;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /stormsFury {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /interface {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /track {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /app {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location ^~ /prometheus {
         set $php_url $request_uri;
         try_files /$uri /index-test.php?$args;
      }
      location ^~ /prometheusOversea {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }  

      location ^~ /prometheusPublish {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }  
      location /internal {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /assistant {
       set $php_url $request_uri;
       try_files $uri $uri/ /index-test.php?$args;
      }
      location /tapdPlugin {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }    
    }

  k.dev.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
      listen 80; ## listen for ipv4

      server_name k.dev.xiaoman.cn;

      set $fePath /data/codebase/crm_root/dev/release/dev;

      root       /data/codebase/crm_root/dev/release/dev/php-crm;
      index       /dist/index.html;

      location / {
          root $fePath;
          try_files /$uri /crm_main_shell_dist/$uri /crm_main_shell_dist/index.html 404;
      }

      location ~ \/(protected|k8s)\/ {
        deny all;
      }

      location ~ /\.(ht|svn|git) {
         deny all;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
         add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;
         root    $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
      }

      location ~ ^\/web\/im-portal\/(.*) {
        index /im-portal/index.html;
        root $fePath;
        try_files /im-portal/$1 /im-portal/index.html 404;
      }

      location ~ ^\/web\/mobile\/(.*) {
        root    $fePath;
        index     /mobile_web/index.html;
        set $feTryFirst /mobile_web/$1;
        set $feTrySecond /mobile_web/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }
    
      location ~ ^\/tm\/(.*) {
        root    $fePath;
        index     /tm_subapp/index.html;
        try_files /tm_subapp/$1 /tm_subapp/index.html 404;
      }

      location ~ ^\/(.*)_subapp\/$ {
        add_header 'Cache-Control' 'no-store';
        root $fePath;
        try_files /$1_subapp/index.html 404;
      }
      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf|bin)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
         expires 30m;
      }
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri  404;
         expires 7d;
      }
      location ~ \.php$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
         include fastcgi_params;
         fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
         fastcgi_param REQUEST_URI $php_url?$args;
         fastcgi_param REAL_REQUEST_URI $request_uri;
         fastcgi_pass   127.0.0.1:9000;
         opentracing_fastcgi_propagate_context;
      }
      location ~* ^/api(/.*)$ {
         set $php_url $1;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /stormsFury {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /interface {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /track {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /app {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location ^~ /prometheus {
         set $php_url $request_uri;
         try_files /$uri /index-test.php?$args;
      }
      location ^~ /prometheusOversea {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }  

      location ^~ /prometheusPublish {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }  
      location /internal {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /assistant {
       set $php_url $request_uri;
       try_files $uri $uri/ /index-test.php?$args;
      }
      location /tapdPlugin {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }    
    }
  app.dev.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
      listen 80; ## listen for ipv4

      server_name app.dev.xiaoman.cn;

      set $fePath /data/codebase/crm_root/dev/master;

      root        /data/codebase/crm_root/dev/master/php-crm;
      index       /dist/index.html;

      location / {
          root $fePath;
          try_files /$uri /crm_main_shell_dist/$uri /crm_main_shell_dist/index.html 404;
      }

      location ~ \/(protected|k8s)\/ {
        deny all;
      }

      location ~ /\.(ht|svn|git) {
         deny all;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
         add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;
         root    $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
      }

      location ~ ^\/web\/im-portal\/(.*) {
        index /im-portal/index.html;
        root $fePath;
        try_files /im-portal/$1 /im-portal/index.html 404;
      }
    
      location ~ ^\/web\/mobile\/(.*) {
        root    $fePath;
        index     /mobile_web/index.html;
        set $feTryFirst /mobile_web/$1;
        set $feTrySecond /mobile_web/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }    

      location ~ ^\/tm\/(.*) {
        root    $fePath;
        index     /tm_subapp/index.html;
        try_files /tm_subapp/$1 /tm_subapp/index.html 404;
      }

      location ~ ^\/(.*)_subapp\/$ {
        add_header 'Cache-Control' 'no-store';
        root $fePath;
        try_files /$1_subapp/index.html 404;
      }
      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf|bin)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
         expires 30m;
      }
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri  404;
         expires 7d;
      }
      location ~ \.php$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
         include fastcgi_params;
         fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
         fastcgi_param REQUEST_URI $php_url?$args;
         fastcgi_param REAL_REQUEST_URI $request_uri;
         fastcgi_pass   127.0.0.1:9000;
         opentracing_fastcgi_propagate_context;
      }
      location ~* ^/api(/.*)$ {
         set $php_url $1;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /stormsFury {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /interface {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /track {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /app {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location ^~ /prometheus {
         set $php_url $request_uri;
         try_files /$uri /index-test.php?$args;
      }
      location /internal {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /assistant {
       set $php_url $request_uri;
       try_files $uri $uri/ /index-test.php?$args;
      }
      location /tapdPlugin {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }    
    }

  desktop.dev.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
      listen 80; ## listen for ipv4

      server_name desktop.dev.xiaoman.cn;

      set $fePath /data/codebase/crm_root/dev/master;

      root        /data/codebase/crm_root/dev/master/php-crm;
      index       /dist/index.html;

      location / {
          root $fePath;
          try_files /$uri /crm_main_shell_dist/$uri /crm_main_shell_dist/index.html 404;
      }

      location ~ \/(protected|k8s)\/ {
        deny all;
      }

      location ~ /\.(ht|svn|git) {
         deny all;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
         add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;
         root    $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
      }

      location ~ ^\/web\/im-portal\/(.*) {
        index /im-portal/index.html;
        root $fePath;
        try_files /im-portal/$1 /im-portal/index.html 404;
      }
    
      location ~ ^\/web\/mobile\/(.*) {
        root    $fePath;
        index     /mobile_web/index.html;
        set $feTryFirst /mobile_web/$1;
        set $feTrySecond /mobile_web/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }    

      location ~ ^\/tm\/(.*) {
        root    $fePath;
        index     /tm_subapp/index.html;
        try_files /tm_subapp/$1 /tm_subapp/index.html 404;
      }

      location ~ ^\/(.*)_subapp\/$ {
        add_header 'Cache-Control' 'no-store';
        root $fePath;
        try_files /$1_subapp/index.html 404;
      }
      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf|bin)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
         expires 30m;
      }
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri  404;
         expires 7d;
      }
      location ~ \.php$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
         include fastcgi_params;
         fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
         fastcgi_param REQUEST_URI $php_url?$args;
         fastcgi_param REAL_REQUEST_URI $request_uri;
         fastcgi_pass   127.0.0.1:9000;
         opentracing_fastcgi_propagate_context;
      }
      location ~* ^/api(/.*)$ {
         set $php_url $1;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /stormsFury {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /interface {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /track {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /app {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location ^~ /prometheus {
         set $php_url $request_uri;
         try_files /$uri /index-test.php?$args;
      }
      location /internal {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /assistant {
       set $php_url $request_uri;
       try_files $uri $uri/ /index-test.php?$args;
      }
      location /tapdPlugin {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }    
    }
  kcallback.dev.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
      listen 80; ## listen for ipv4

      server_name kcallback.dev.xiaoman.cn exciting-spence.com  holedevops.com.cn boomdevops.com.cn;

      set $fePath /data/codebase/crm_root/dev/release/dev;

      root       /data/codebase/crm_root/dev/release/dev/php-crm;
      index       /dist/index.html;

      location / {
          root $fePath;
          try_files /$uri /crm_main_shell_dist/$uri /crm_main_shell_dist/index.html 404;
      }

      location ~ \/(protected|k8s)\/ {
        deny all;
      }

      location ~ /\.(ht|svn|git) {
         deny all;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
         add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;
         root    $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
      }

      location ~ ^\/web\/im-portal\/(.*) {
        index /im-portal/index.html;
        root $fePath;
        try_files /im-portal/$1 /im-portal/index.html 404;
      }

      location ~ ^\/tm\/(.*) {
        root    $fePath;
        index     /tm_subapp/index.html;
        try_files /tm_subapp/$1 /tm_subapp/index.html 404;
      }

      location ~ ^\/(.*)_subapp\/$ {
        add_header 'Cache-Control' 'no-store';
        root $fePath;
        try_files /$1_subapp/index.html 404;
      }
      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf|bin)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
         expires 30m;
      }
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri  404;
         expires 7d;
      }
      location ~ \.php$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
         include fastcgi_params;
         fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
         fastcgi_param REQUEST_URI $php_url?$args;
         fastcgi_param REAL_REQUEST_URI $request_uri;
         fastcgi_pass   127.0.0.1:9000;
         opentracing_fastcgi_propagate_context;
      }
      location ~* ^/api(/.*)$ {
         set $php_url $1;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /stormsFury {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /interface {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /track {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /app {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location ^~ /prometheus {
         set $php_url $request_uri;
         try_files /$uri /index-test.php?$args;
      }
      location /internal {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /assistant {
       set $php_url $request_uri;
       try_files $uri $uri/ /index-test.php?$args;
      }
    }
  inner-api.dev.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
      listen 80; ## listen for ipv4

      server_name inner-api.dev.xiaoman.cn;

      set $fePath /data/codebase/crm_root/dev/master;

      root       /data/codebase/crm_root/dev/master/php-crm;
      index       /dist/index.html;

      location / {
          root $fePath;
          try_files /$uri /crm_main_shell_dist/$uri /crm_main_shell_dist/index.html 404;
      }

      location ~ \/(protected|k8s)\/ {
        deny all;
      }

      location ~ /\.(ht|svn|git) {
         deny all;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
         add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;
         root    $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
      }

      location ~ ^\/web\/im-portal\/(.*) {
        index /im-portal/index.html;
        root $fePath;
        try_files /im-portal/$1 /im-portal/index.html 404;
      }

      location ~ ^\/tm\/(.*) {
        root    $fePath;
        index     /tm_subapp/index.html;
        try_files /tm_subapp/$1 /tm_subapp/index.html 404;
      }

      location ~ ^\/(.*)_subapp\/$ {
        add_header 'Cache-Control' 'no-store';
        root $fePath;
        try_files /$1_subapp/index.html 404;
      }
      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf|bin)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
         expires 30m;
      }
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri  404;
         expires 7d;
      }
      location ~ \.php$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
         include fastcgi_params;
         fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
         fastcgi_param REQUEST_URI $php_url?$args;
         fastcgi_param REAL_REQUEST_URI $request_uri;
         fastcgi_pass   127.0.0.1:9000;
         opentracing_fastcgi_propagate_context;
      }
      location ~* ^/api(/.*)$ {
         set $php_url $1;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /stormsFury {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /interface {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /track {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /app {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location ^~ /prometheus {
         set $php_url $request_uri;
         try_files /$uri /index-test.php?$args;
      }
      location /internal {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /assistant {
       set $php_url $request_uri;
       try_files $uri $uri/ /index-test.php?$args;
      }
    }

  sandbox.dev.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
      listen 80; ## listen for ipv4

      server_name *.sandbox.dev.xiaoman.cn;

      set $fePath /data/codebase/crm_root/dev/release/dev;

      root       /data/codebase/crm_root/dev/release/dev/php-crm;
      index       /dist/index.html;

      location / {
          root $fePath;
          try_files /$uri /crm_main_shell_dist/$uri /crm_main_shell_dist/index.html 404;
      }

      location ~ \/(protected|k8s)\/ {
        deny all;
      }

      location ~ /\.(ht|svn|git) {
         deny all;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
         add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;
         root    $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
      }

      location ~ ^\/web\/im-portal\/(.*) {
        index /im-portal/index.html;
        root $fePath;
        try_files /im-portal/$1 /im-portal/index.html 404;
      }

      location ~ ^\/tm\/(.*) {
        root    $fePath;
        index     /tm_subapp/index.html;
        try_files /tm_subapp/$1 /tm_subapp/index.html 404;
      }

      location ~ ^\/(.*)_subapp\/$ {
        add_header 'Cache-Control' 'no-store';
        root $fePath;
        try_files /$1_subapp/index.html 404;
      }
      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf|bin)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
         expires 30m;
      }
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri  404;
         expires 7d;
      }
      location ~ \.php$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
         include fastcgi_params;
         fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
         fastcgi_param REQUEST_URI $php_url?$args;
         fastcgi_param REAL_REQUEST_URI $request_uri;
         fastcgi_pass   127.0.0.1:9000;
         opentracing_fastcgi_propagate_context;
      }
      location ~* ^/api(/.*)$ {
         set $php_url $1;
         try_files $uri $uri/ /index-sandbox-test.php?$args;
      }
      location /stormsFury {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-sandbox-test.php?$args;
      }
      location /interface {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-sandbox-test.php?$args;
      }
      location /track {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-sandbox-test.php?$args;
      }
      location /app {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-sandbox-test.php?$args;
      }
      location ^~ /prometheus {
         set $php_url $request_uri;
         try_files /$uri /index-sandbox-test.php?$args;
      }
      location /internal {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-sandbox-test.php?$args;
      }
      location /assistant {
       set $php_url $request_uri;
       try_files $uri $uri/ /index-sandbox-test.php?$args;
      }
    }
  mkt.dev.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
      listen 80; ## listen for ipv4

      server_name mkt.dev.xiaoman.cn mkt.dev.okki.com ad.dev.xiaoman.cn mkt.feature.dev.xiaoman.cn;

      set $fePath /data/codebase/crm_root/dev/feature/mkt;

      root       /data/codebase/crm_root/dev/feature/mkt/php-crm;
      index       /dist/index.html;

      location / {
          root $fePath;
          try_files /$uri /crm_main_shell_dist/$uri /crm_main_shell_dist/index.html 404;
      }

      location ~ \/(protected|k8s)\/ {
        deny all;
      }

      location ~ /\.(ht|svn|git) {
         deny all;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
         add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;
         root    $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
      }

      location ~ ^\/web\/im-portal\/(.*) {
        index /im-portal/index.html;
        root $fePath;
        try_files /im-portal/$1 /im-portal/index.html 404;
      }

      location ~ ^\/tm\/(.*) {
        root    $fePath;
        index     /tm_subapp/index.html;
        try_files /tm_subapp/$1 /tm_subapp/index.html 404;
      }

      location ~ ^\/(.*)_subapp\/$ {
        add_header 'Cache-Control' 'no-store';
        root $fePath;
        try_files /$1_subapp/index.html 404;
      }
      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf|bin)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
         expires 30m;
      }
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri  404;
         expires 7d;
      }
      location ~ \.php$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
         include fastcgi_params;
         fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
         fastcgi_param REQUEST_URI $php_url?$args;
         fastcgi_param REAL_REQUEST_URI $request_uri;
         fastcgi_pass   127.0.0.1:9000;
         opentracing_fastcgi_propagate_context;
      }
      location ~* ^/api/marketing(/.*)$ {
          set $php_url "/marketing$1";
          try_files $uri $uri/ /index-test.php?$args;
      }
      location ~* ^/api(/.*)$ {
            set $php_url "/marketing$1";
            try_files $uri $uri/ /index-test.php?$args;
      }    
      location /stormsFury {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /interface {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /track {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /app {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location ^~ /prometheus {
         set $php_url $request_uri;
         try_files /$uri /index-test.php?$args;
      }
      location /internal {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /assistant {
       set $php_url $request_uri;
       try_files $uri $uri/ /index-test.php?$args;
      }
    }
  login.dev.xiaoman.cn.conf: |-
    server {
        charset utf-8;
        client_max_body_size 128M;
        listen 80; ## listen for ipv4
        server_name login-api.dev.xiaoman.cn;
        root        /data/codebase/crm_root/dev/login;
        index       index-test.php;
       location / {
            # Redirect everything that isn't a real file to index.php
            try_files $uri $uri/ /index-test.php?$args;
        }
        location ~ \.(txt|xml|js|vue|css|ttf)$ {
            try_files $uri =404;
            expires 7d;
        }
        location ~ \.(jpg|jpeg|gif|png|ico|svg|otf|bin)$ {
            try_files $uri =404;
            expires 30m;
        }
        location /protected {
            deny  all;
        }
        location ~ \.php$ {
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id';
            add_header 'Access-Control-Allow-Origin' "$http_origin";
            add_header 'Access-Control-Allow-Credentials' "true";
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            #先服务nginx返回, 后续再考虑交接给网关处理
            add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
            add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
            fastcgi_param DEFAULT_DC     "cn";
            fastcgi_pass   127.0.0.1:9000;
        }
        location ~ /\.(ht|svn|git|k8s) {
            deny all;
        }
    }
        server {
        charset utf-8;
        client_max_body_size 128M;
        listen 80; ## listen for ipv4
        server_name  login.dev.xiaoman.cn ;
        root        /data/codebase/crm_root/dev/login;
        index       web/dist/index.html;

        location / {
             try_files /web/$uri /web/dist/index.html 404;
        }
        location ~ \.(txt|xml|js|vue|css|ttf)$ {
            try_files /web/$uri /web/dist/index.html 404;
            expires 7d;
        }
        location ~ \.(jpg|jpeg|gif|png|ico|svg|otf|bin)$ {
            try_files /web/$uri /web/dist/index.html 404;
            expires 30m;
        }
        location /protected {
            deny  all;
        }
        location ~ \.php$ {
           add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id';
           add_header 'Access-Control-Allow-Origin' "$http_origin";
           add_header 'Access-Control-Allow-Credentials' "true";
           add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            #先服务nginx返回, 后续再考虑交接给网关处理
            add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
            add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
            fastcgi_param REQUEST_URI $php_url?$args;
            fastcgi_param REAL_REQUEST_URI $request_uri;
            fastcgi_pass   127.0.0.1:9000;
            opentracing_fastcgi_propagate_context;
        }
        location ~ /\.(ht|svn|git|k8s) {
            deny all;
        }
        location ~* ^/api(/.*)$ {
            fastcgi_pass   127.0.0.1:9000;
          }
    
          location ~* ^/api(/.*)$ {
            set $php_url $1;
            try_files $uri $uri/ /index-test.php?$args;
        }
        location ^~ /gii {
          set $php_url $request_uri;
          try_files $uri $uri/ /index-test.php?$args;
        }
    }

  light.dev.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
      listen 80; ## listen for ipv4

      server_name light.dev.xiaoman.cn;

      set $fePath /data/codebase/crm_root/dev/release/dev;

      root       /data/codebase/crm_root/dev/release/dev/php-crm;
      index       /dist/index.html;

      location / {
          root $fePath;
          try_files /$uri /crm_main_shell_dist/$uri /crm_main_shell_dist/index.html 404;
      }

      location ~ \/(protected|k8s)\/ {
        deny all;
      }

      location ~ /\.(ht|svn|git) {
         deny all;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
         add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;
         root    $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
      }

      location ~ ^\/web\/im-portal\/(.*) {
        index /im-portal/index.html;
        root $fePath;
        try_files /im-portal/$1 /im-portal/index.html 404;
      }

      location ~ ^\/tm\/(.*) {
        root    $fePath;
        index     /tm_subapp/index.html;
        try_files /tm_subapp/$1 /tm_subapp/index.html 404;
      }

      location ~ ^\/(.*)_subapp\/$ {
        add_header 'Cache-Control' 'no-store';
        root $fePath;
        try_files /$1_subapp/index.html 404;
      }
      location ~ \.(jpg|jpeg|gif|png|ico|svg|otf|bin)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri 404;
         expires 30m;
      }
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
         root $fePath;
         try_files /$uri /crm_main_shell_dist/$uri  404;
         expires 7d;
      }
      location ~ \.php$ {
         add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
         add_header 'Access-Control-Allow-Origin' "$http_origin" always;
         add_header 'Access-Control-Allow-Credentials' "true" always;
         add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
         include fastcgi_params;
         fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
         fastcgi_param REQUEST_URI $php_url?$args;
         fastcgi_param REAL_REQUEST_URI $request_uri;
         fastcgi_pass   127.0.0.1:9000;
         opentracing_fastcgi_propagate_context;
      }
      location ~* ^/api(/.*)$ {
         set $php_url $1;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /stormsFury {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /interface {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /track {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /app {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location ^~ /prometheus {
         set $php_url $request_uri;
         try_files /$uri /index-test.php?$args;
      }
      location /internal {
         set $php_url $request_uri;
         try_files $uri $uri/ /index-test.php?$args;
      }
      location /assistant {
       set $php_url $request_uri;
       try_files $uri $uri/ /index-test.php?$args;
      }
    }
  release.dev.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
    
      listen 80; ## listen for ipv4
      server_name *.release.dev.xiaoman.cn;
    
      if ($host !~* ^(.*)\.release\.dev\.xiaoman\.cn$) {
          return 404;
      }
    
      set $curPath $1;
      set $fePath /data/codebase/crm_root/dev/release/$curPath;
      set $defaultFePath /data/codebase/crm_root/dev/master;
      set $feTryFirst /$uri;
      set $feTrySecond /crm_main_shell_dist/$uri;
      set $feTryThird "";
    
      root       /data/codebase/crm_root/dev/release/$curPath/php-crm;
      index      web/dist/index.html;

      ##前端资源不存在，默认拉取master分支代码
      location @fe_default {
        root    $defaultFePath;
        try_files $feTryFirst $feTrySecond $feTryThird 404;
      }
    
      location / {
        root    $fePath;
        set $feTryThird /crm_main_shell_dist/index.html;
        try_files $feTryFirst $feTrySecond $feTryThird @fe_default;
      }

      location = /50x.html {
         root /usr/local/nginx/html;
      }

      location /healthz {
         access_log off;
         return 200;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,x-xiaoman-platform-type' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
      }
    
      location ~ \.(jpg|jpeg|gif|png|ico|svg)$ {
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
        expires 30m;
      }
    
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
        root    $fePath;
        try_files $feTryFirst $feTrySecond @fe_default;
        expires 7d;
      }
    
      location ~ ^\/web\/im-portal\/(.*) {
        root    $fePath;
        index     /im-portal/index.html;
        set $feTryFirst /im-portal/$1;
        set $feTrySecond /im-portal/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }
      location ~ ^\/web\/mobile\/(.*) {
        root    $fePath;
        index     /mobile_web/index.html;
        set $feTryFirst /mobile_web/$1;
        set $feTrySecond /mobile_web/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }
      location ~ ^\/tm\/(.*) {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;    
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
        root    $fePath;
        index     /tm_subapp/index.html;
        set $feTryFirst /tm_subapp/$1;
        set $feTrySecond /tm_subapp/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }

      location ~ ^\/(.*)_subapp\/(.*){
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;    
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
          root    $fePath;
          index   /$1_subapp/index.html;
          set $feTryFirst /$1_subapp/$2;
          set $feTrySecond /$1_subapp/index.html;
          try_files $feTryFirst $feTrySecond @fe_default;
      }
    
      location /protected {
        deny  all;
      }
    
      location ~ \.php$ {

        if (!-f $document_root$fastcgi_script_name) {
            root  /data/codebase/crm_root/dev/master/php-crm;
        }

        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;
    
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
        fastcgi_param REQUEST_URI $php_url?$args;
        fastcgi_param REAL_REQUEST_URI $request_uri;
        fastcgi_pass   127.0.0.1:9000;
        opentracing_fastcgi_propagate_context;
      }
    
      location ~ /\.(ht|svn|git) {
        deny all;
      }
    
      location ~* ^/api(/.*)$ {
        set $php_url $1;
        try_files $uri $uri/ /index-test.php?$args;
      }
      location /stormsFury {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /interface {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /track {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /app {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
      location ^~ /prometheus {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }
      location ^~ /prometheusOversea {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }  

      location ^~ /prometheusPublish {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }      
      location ^~ /gii {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /internal {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /assistant {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /tapdPlugin {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }    
    }
    

  story.dev.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
    
      listen 80; ## listen for ipv4
      server_name *.story.dev.xiaoman.cn;
    
      if ($host !~* ^(.*)\.story\.dev\.xiaoman\.cn$) {
        return 404;
      }
    
      set $curPath $1;
      set $fePath /data/codebase/crm_root/dev/story/$curPath;
      set $defaultFePath /data/codebase/crm_root/dev/master;
    
      if ($host !~* ^([a-zA-z0-9]+-[a-zA-z0-9]+-[0-9]+-[0-9]+)-([0-9]+)\.story\.dev\.xiaoman\.cn$) {
        return 404;    
      }
  
      set $iterationName $1;
      set $storyId $2;
      set $feIterationPath /data/codebase/crm_root/dev/release/$iterationName;
        
      set $feTryFirst /$uri;
      set $feTrySecond /crm_main_shell_dist/$uri;
      set $feTryThird "";
    
      root /data/codebase/crm_root/dev/story/$curPath/php-crm;
      index      web/dist/index.html;
        
      ##story和release前端资源不存在，默认拉取master分支代码
      location @fe_default {
        root    $defaultFePath;
        try_files $feTryFirst $feTrySecond $feTryThird 404;
      }
    
      ##story前端资源不存在，拉取release分支代码
      location @iteration_fe {
        root    $feIterationPath;
        try_files $feTryFirst $feTrySecond $feTryThird @fe_default;
      }
    
      location / {
        root    $fePath;
        set $feTryThird /crm_main_shell_dist/index.html;
        try_files $feTryFirst $feTrySecond $feTryThird @iteration_fe;
      }

      location = /50x.html {
         root /usr/local/nginx/html;
      }

      location /healthz {
         access_log off;
         return 200;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,x-xiaoman-platform-type' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
        root    $fePath;
        try_files $feTryFirst $feTrySecond @iteration_fe;
      }
    
      location ~ \.(jpg|jpeg|gif|png|ico|svg)$ {
        root    $fePath;
        try_files $feTryFirst $feTrySecond @iteration_fe;
        expires 30m;
      }
    
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
        root    $fePath;
        try_files $feTryFirst $feTrySecond @iteration_fe;
        expires 7d;
      }
    
      location ~ ^\/web\/im-portal\/(.*) {
        root    $fePath;
        index     /im-portal/index.html;
        set $feTryFirst /im-portal/$1;
        set $feTrySecond /im-portal/index.html;
        try_files $feTryFirst $feTrySecond @iteration_fe;
      }
    
      location ~ ^\/web\/mobile\/(.*) {
        root    $fePath;
        index     /mobile_web/index.html;
        set $feTryFirst /mobile_web/$1;
        set $feTrySecond /mobile_web/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }    

      location ~ ^\/tm\/(.*) {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;    
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
        root    $fePath;
        index     /tm_subapp/index.html;
        set $feTryFirst /tm_subapp/$1;
        set $feTrySecond /tm_subapp/index.html;
        try_files $feTryFirst $feTrySecond @iteration_fe;
      }

      location ~ ^\/(.*)_subapp\/(.*){
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;    
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
          root    $fePath;
          index   /$1_subapp/index.html;
          set $feTryFirst /$1_subapp/$2;
          set $feTrySecond /$1_subapp/index.html;
          try_files $feTryFirst $feTrySecond @iteration_fe;
      }
    
      location /protected {
        deny  all;
      }
    
      location ~ \.php$ {
    
        if (!-f $document_root$fastcgi_script_name) {
          root /data/codebase/crm_root/dev/release/$iterationName/php-crm;
        }
    
        if (!-f $document_root$fastcgi_script_name) {
          root  /data/codebase/crm_root/dev/master/php-crm;
        }

        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid,Tapd-Email,Tapd-Refer-Url,Tapd-Monkey-Version' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;
    
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
        fastcgi_param REQUEST_URI $php_url?$args;
        fastcgi_param REAL_REQUEST_URI $request_uri;
        fastcgi_pass   127.0.0.1:9000;
        opentracing_fastcgi_propagate_context;
      }
    
      location ~ /\.(ht|svn|git) {
        deny all;
      }
    
      location ~* ^/api(/.*)$ {
        set $php_url $1;
        try_files $uri $uri/ /index-test.php?$args;
      }
      location /stormsFury {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /interface {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /track {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /app {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
      location ^~ /prometheus {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }
      location ^~ /prometheusOversea {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }  

      location ^~ /prometheusPublish {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }      
      location ^~ /gii {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /internal {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /assistant {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /tapdPlugin {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }    
    }
    
  ding.dev.xiaoman.cn.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
    
      listen 80; ## listen for ipv4
      server_name ding.dev.xiaoman.cn;
    
    
      set $curPath "master";
      set $iterationPath 'release/crm-iteration-3-2';
    
      set $fePath /data/codebase/crm_root/dev/$curPath;
      set $defaultFePath /data/codebase/crm_root/dev/master;
    
      set $feIterationPath /data/codebase/crm_root/dev/$iterationPath;
    
      set $feTryFirst /$uri;
      set $feTrySecond /crm_main_shell_dist/$uri;
      set $feTryThird "";
    
      root /data/codebase/crm_root/dev/story/$curPath/php-crm;
      index      web/dist/index.html;
    
      ##story和release前端资源不存在，默认拉取master分支代码
      location @fe_default {
        root    $defaultFePath;
        try_files $feTryFirst $feTrySecond $feTryThird 404;
      }
    
      ##story前端资源不存在，拉取release分支代码
      location @iteration_fe {
        root    $feIterationPath;
        try_files $feTryFirst $feTrySecond $feTryThird @fe_default;
      }
    
      location / {
        root    $fePath;
        set $feTryThird /crm_main_shell_dist/index.html;
        try_files $feTryFirst $feTrySecond $feTryThird @iteration_fe;
      }

      location = /50x.html {
         root /usr/local/nginx/html;
      }

      location /healthz {
         access_log off;
         return 200;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,x-xiaoman-platform-type' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
        root    $fePath;
        try_files $feTryFirst $feTrySecond @iteration_fe;
      }
    
      location ~ \.(jpg|jpeg|gif|png|ico|svg)$ {
        root    $fePath;
        try_files $feTryFirst $feTrySecond @iteration_fe;
        expires 30m;
      }
    
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
        root    $fePath;
        try_files $feTryFirst $feTrySecond @iteration_fe;
        expires 7d;
      }
    
      location ~ ^\/web\/im-portal\/(.*) {
        root    $fePath;
        index     /im-portal/index.html;
        set $feTryFirst /im-portal/$1;
        set $feTrySecond /im-portal/index.html;
        try_files $feTryFirst $feTrySecond @iteration_fe;
      }
    
      location ~ ^\/web\/mobile\/(.*) {
        root    $fePath;
        index     /mobile_web/index.html;
        set $feTryFirst /mobile_web/$1;
        set $feTrySecond /mobile_web/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }    

      location ~ ^\/tm\/(.*) {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;    
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
        root    $fePath;
        index     /tm_subapp/index.html;
        set $feTryFirst /tm_subapp/$1;
        set $feTrySecond /tm_subapp/index.html;
        try_files $feTryFirst $feTrySecond @iteration_fe;
      }

      location ~ ^\/(.*)_subapp\/(.*){
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;    
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
          root    $fePath;
          index   /$1_subapp/index.html;
          set $feTryFirst /$1_subapp/$2;
          set $feTrySecond /$1_subapp/index.html;
          try_files $feTryFirst $feTrySecond @iteration_fe;
      }
    
      location /protected {
        deny  all;
      }
    
      location ~ \.php$ {
    
        if (!-f $document_root$fastcgi_script_name) {
          root /data/codebase/crm_root/dev/release/$iterationName/php-crm;
        }
    
        if (!-f $document_root$fastcgi_script_name) {
          root  /data/codebase/crm_root/dev/master/php-crm;
        }

        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;
    
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
        fastcgi_param REQUEST_URI $php_url?$args;
        fastcgi_param REAL_REQUEST_URI $request_uri;
        fastcgi_pass   127.0.0.1:9000;
        opentracing_fastcgi_propagate_context;
      }
    
      location ~ /\.(ht|svn|git) {
        deny all;
      }
    
      location ~* ^/api(/.*)$ {
        set $php_url $1;
        try_files $uri $uri/ /index-test.php?$args;
      }
      location /stormsFury {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /interface {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /track {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /app {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
      location ^~ /prometheus {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }
    
      location ^~ /gii {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /internal {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /assistant {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /tapdPlugin {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }    
    }

  assistant.dev.okki.com.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
    
      listen 80; ## listen for ipv4
      server_name assistant.dev.okki.com;
    
    
      set $curPath "story/crm-iteration-3-4-1067266";
      set $iterationPath 'release/crm-iteration-3-4';
    
      set $fePath /data/codebase/crm_root/dev/$curPath;
      set $defaultFePath /data/codebase/crm_root/dev/master;
    
      set $feIterationPath /data/codebase/crm_root/dev/$iterationPath;
    
      set $feTryFirst /$uri;
      set $feTrySecond /crm_main_shell_dist/$uri;
      set $feTryThird "";
    
      root /data/codebase/crm_root/dev/story/$curPath/php-crm;
      index      web/dist/index.html;
    
      ##story和release前端资源不存在，默认拉取master分支代码
      location @fe_default {
        root    $defaultFePath;
        try_files $feTryFirst $feTrySecond $feTryThird 404;
      }
    
      ##story前端资源不存在，拉取release分支代码
      location @iteration_fe {
        root    $feIterationPath;
        try_files $feTryFirst $feTrySecond $feTryThird @fe_default;
      }
    
      location / {
        root    $fePath;
        set $feTryThird /crm_main_shell_dist/index.html;
        try_files $feTryFirst $feTrySecond $feTryThird @iteration_fe;
      }

      location = /50x.html {
         root /usr/local/nginx/html;
      }

      location /healthz {
         access_log off;
         return 200;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,x-xiaoman-platform-type' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
        root    $fePath;
        try_files $feTryFirst $feTrySecond @iteration_fe;
      }
    
      location ~ \.(jpg|jpeg|gif|png|ico|svg)$ {
        root    $fePath;
        try_files $feTryFirst $feTrySecond @iteration_fe;
        expires 30m;
      }
    
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
        root    $fePath;
        try_files $feTryFirst $feTrySecond @iteration_fe;
        expires 7d;
      }
    
      location ~ ^\/web\/im-portal\/(.*) {
        root    $fePath;
        index     /im-portal/index.html;
        set $feTryFirst /im-portal/$1;
        set $feTrySecond /im-portal/index.html;
        try_files $feTryFirst $feTrySecond @iteration_fe;
      }
    
      location ~ ^\/web\/mobile\/(.*) {
        root    $fePath;
        index     /mobile_web/index.html;
        set $feTryFirst /mobile_web/$1;
        set $feTrySecond /mobile_web/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }    

      location ~ ^\/tm\/(.*) {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;    
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
        root    $fePath;
        index     /tm_subapp/index.html;
        set $feTryFirst /tm_subapp/$1;
        set $feTrySecond /tm_subapp/index.html;
        try_files $feTryFirst $feTrySecond @iteration_fe;
      }

      location ~ ^\/(.*)_subapp\/(.*){
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;    
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
          root    $fePath;
          index   /$1_subapp/index.html;
          set $feTryFirst /$1_subapp/$2;
          set $feTrySecond /$1_subapp/index.html;
          try_files $feTryFirst $feTrySecond @iteration_fe;
      }
    
      location /protected {
        deny  all;
      }
    
      location ~ \.php$ {
    
        if (!-f $document_root$fastcgi_script_name) {
          root /data/codebase/crm_root/dev/release/$iterationName/php-crm;
        }
    
        if (!-f $document_root$fastcgi_script_name) {
          root  /data/codebase/crm_root/dev/master/php-crm;
        }

        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;
    
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
        fastcgi_param REQUEST_URI $php_url?$args;
        fastcgi_param REAL_REQUEST_URI $request_uri;
        fastcgi_pass   127.0.0.1:9000;
        opentracing_fastcgi_propagate_context;
      }
    
      location ~ /\.(ht|svn|git) {
        deny all;
      }
    
      location ~* ^/api(/.*)$ {
        set $php_url $1;
        try_files $uri $uri/ /index-test.php?$args;
      }
      location /stormsFury {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /interface {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /track {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /app {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
      location ^~ /prometheus {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }
    
      location ^~ /gii {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /internal {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /assistant {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /tapdPlugin {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }    
    }
  leads.dev.okki.com.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
    
      listen 80; ## listen for ipv4
      server_name app.dev.okki.com;
    
    
      set $curPath "story/crm/iteration-4-6/1098452";
      set $iterationPath 'release/crm-iteration-4-6';
    
      set $fePath /data/codebase/crm_root/dev/$curPath;
      set $defaultFePath /data/codebase/crm_root/dev/master;
    
      set $feIterationPath /data/codebase/crm_root/dev/$iterationPath;
    
      set $feTryFirst /$uri;
      set $feTrySecond /crm_main_shell_dist/$uri;
      set $feTryThird "";
    
      root /data/codebase/crm_root/dev/story/$curPath/php-crm;
      index      web/dist/index.html;
    
      ##story和release前端资源不存在，默认拉取master分支代码
      location @fe_default {
        root    $defaultFePath;
        try_files $feTryFirst $feTrySecond $feTryThird 404;
      }
    
      ##story前端资源不存在，拉取release分支代码
      location @iteration_fe {
        root    $feIterationPath;
        try_files $feTryFirst $feTrySecond $feTryThird @fe_default;
      }
    
      location / {
        root    $fePath;
        set $feTryThird /crm_main_shell_dist/index.html;
        try_files $feTryFirst $feTrySecond $feTryThird @iteration_fe;
      }

      location = /50x.html {
         root /usr/local/nginx/html;
      }

      location /healthz {
         access_log off;
         return 200;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,x-xiaoman-platform-type' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
        root    $fePath;
        try_files $feTryFirst $feTrySecond @iteration_fe;
      }
    
      location ~ \.(jpg|jpeg|gif|png|ico|svg)$ {
        root    $fePath;
        try_files $feTryFirst $feTrySecond @iteration_fe;
        expires 30m;
      }
    
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
        root    $fePath;
        try_files $feTryFirst $feTrySecond @iteration_fe;
        expires 7d;
      }
    
      location ~ ^\/web\/im-portal\/(.*) {
        root    $fePath;
        index     /im-portal/index.html;
        set $feTryFirst /im-portal/$1;
        set $feTrySecond /im-portal/index.html;
        try_files $feTryFirst $feTrySecond @iteration_fe;
      }

      location ~ ^\/web\/mobile\/(.*) {
        root    $fePath;
        index     /web/mobile/index.html;
        set $feTryFirst /web/mobile/$1;
        set $feTrySecond /web/mobile/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }
    
      location ~ ^\/tm\/(.*) {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;    
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
        root    $fePath;
        index     /tm_subapp/index.html;
        set $feTryFirst /tm_subapp/$1;
        set $feTrySecond /tm_subapp/index.html;
        try_files $feTryFirst $feTrySecond @iteration_fe;
      }

      location ~ ^\/(.*)_subapp\/(.*){
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;    
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
          root    $fePath;
          index   /$1_subapp/index.html;
          set $feTryFirst /$1_subapp/$2;
          set $feTrySecond /$1_subapp/index.html;
          try_files $feTryFirst $feTrySecond @iteration_fe;
      }
    
      location /protected {
        deny  all;
      }
    
      location ~ \.php$ {
    
        if (!-f $document_root$fastcgi_script_name) {
          root /data/codebase/crm_root/dev/release/$iterationName/php-crm;
        }
    
        if (!-f $document_root$fastcgi_script_name) {
          root  /data/codebase/crm_root/dev/master/php-crm;
        }

        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;
    
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
        fastcgi_param REQUEST_URI $php_url?$args;
        fastcgi_param REAL_REQUEST_URI $request_uri;
        fastcgi_pass   127.0.0.1:9000;
        opentracing_fastcgi_propagate_context;
      }
    
      location ~ /\.(ht|svn|git) {
        deny all;
      }
    
      location ~* ^/api(/.*)$ {
        set $php_url $1;
        try_files $uri $uri/ /index-test.php?$args;
      }
      location /stormsFury {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /interface {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /track {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /app {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
      location ^~ /prometheus {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }
    
      location ^~ /gii {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /internal {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /assistant {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /tapdPlugin {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }    
    }
  crm.dev.okki.com.conf: |-
    server {
      charset utf-8;
      client_max_body_size 128M;
    
      listen 80; ## listen for ipv4
      server_name crm.dev.okki.com;
    
    
      set $curPath "story/crm/iteration-3-6/1086726";
      set $iterationPath 'release/crm-iteration-3-6';
    
      set $fePath /data/codebase/crm_root/dev/$curPath;
      set $defaultFePath /data/codebase/crm_root/dev/master;
    
      set $feIterationPath /data/codebase/crm_root/dev/$iterationPath;
    
      set $feTryFirst /$uri;
      set $feTrySecond /crm_main_shell_dist/$uri;
      set $feTryThird "";
    
      root /data/codebase/crm_root/dev/story/$curPath/php-crm;
      index      web/dist/index.html;
    
      ##story和release前端资源不存在，默认拉取master分支代码
      location @fe_default {
        root    $defaultFePath;
        try_files $feTryFirst $feTrySecond $feTryThird 404;
      }
    
      ##story前端资源不存在，拉取release分支代码
      location @iteration_fe {
        root    $feIterationPath;
        try_files $feTryFirst $feTrySecond $feTryThird @fe_default;
      }
    
      location / {
        root    $fePath;
        set $feTryThird /crm_main_shell_dist/index.html;
        try_files $feTryFirst $feTrySecond $feTryThird @iteration_fe;
      }

      location = /50x.html {
         root /usr/local/nginx/html;
      }

      location /healthz {
         access_log off;
         return 200;
      }

      location ~ (webpack-assets\.json|vite-assets\.json|sw\.js|(.*)-entry\.js)$ {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,x-xiaoman-platform-type' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Cache-Control' 'no-store';
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
        root    $fePath;
        try_files $feTryFirst $feTrySecond @iteration_fe;
      }
    
      location ~ \.(jpg|jpeg|gif|png|ico|svg)$ {
        root    $fePath;
        try_files $feTryFirst $feTrySecond @iteration_fe;
        expires 30m;
      }
    
      location ~ \.(txt|xml|js|vue|css|ttf|json)$ {
        root    $fePath;
        try_files $feTryFirst $feTrySecond @iteration_fe;
        expires 7d;
      }
    
      location ~ ^\/web\/im-portal\/(.*) {
        root    $fePath;
        index     /im-portal/index.html;
        set $feTryFirst /im-portal/$1;
        set $feTrySecond /im-portal/index.html;
        try_files $feTryFirst $feTrySecond @iteration_fe;
      }

      location ~ ^\/web\/mobile\/(.*) {
        root    $fePath;
        index     /web/mobile/index.html;
        set $feTryFirst /web/mobile/$1;
        set $feTrySecond /web/mobile/index.html;
        try_files $feTryFirst $feTrySecond @fe_default;
      }
    
      location ~ ^\/tm\/(.*) {
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;    
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
        root    $fePath;
        index     /tm_subapp/index.html;
        set $feTryFirst /tm_subapp/$1;
        set $feTrySecond /tm_subapp/index.html;
        try_files $feTryFirst $feTrySecond @iteration_fe;
      }

      location ~ ^\/(.*)_subapp\/(.*){
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;    
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;    
          root    $fePath;
          index   /$1_subapp/index.html;
          set $feTryFirst /$1_subapp/$2;
          set $feTrySecond /$1_subapp/index.html;
          try_files $feTryFirst $feTrySecond @iteration_fe;
      }
    
      location /protected {
        deny  all;
      }
    
      location ~ \.php$ {
    
        if (!-f $document_root$fastcgi_script_name) {
          root /data/codebase/crm_root/dev/release/$iterationName/php-crm;
        }
    
        if (!-f $document_root$fastcgi_script_name) {
          root  /data/codebase/crm_root/dev/master/php-crm;
        }

        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Accept-Language,xiaoman-os,xiaoman-version,xiaoman-pskey,xiaoman-user-id,xiaoman-client-id,x-xiaoman-platform-type,x-xsrf-token,x-xiaoman-app-version,b3,traceparent,uber-trace-id,x-b3-spanid,x-b3-traceid' always;
        add_header 'Access-Control-Allow-Origin' "$http_origin" always;
        add_header 'Access-Control-Allow-Credentials' "true" always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        #先服务nginx返回, 后续再考虑交接给网关处理
        add_header   X-Xiaoman-TraceId $opentracing_context_x_b3_traceid;
        add_header   X-Xiaoman-SpanId $opentracing_context_x_b3_spanid;
    
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
        fastcgi_param REQUEST_URI $php_url?$args;
        fastcgi_param REAL_REQUEST_URI $request_uri;
        fastcgi_pass   127.0.0.1:9000;
        opentracing_fastcgi_propagate_context;
      }
    
      location ~ /\.(ht|svn|git) {
        deny all;
      }
    
      location ~* ^/api(/.*)$ {
        set $php_url $1;
        try_files $uri $uri/ /index-test.php?$args;
      }
      location /stormsFury {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /interface {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /track {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /app {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
      location ^~ /prometheus {
        set $php_url $request_uri;
        try_files /$uri /index-test.php?$args;
      }
    
      location ^~ /gii {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /internal {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /assistant {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }
    
      location /tapdPlugin {
        set $php_url $request_uri;
        try_files $uri $uri/ /index-test.php?$args;
      }    
    }        
