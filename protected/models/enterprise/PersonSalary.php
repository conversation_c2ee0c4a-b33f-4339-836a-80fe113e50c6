<?php

namespace common\models\enterprise;


/**
 * This is the model class for table "tbl_person_salary".
 *
 * The followings are the available columns in table 'tbl_person_salary':
 * @property integer $person_salary_id
 * @property string $client_id
 * @property string $user_id
 * @property integer $salary_id
 * @property string $salary_name
 * @property string $head
 * @property string $body
 * @property integer $is_distributed
 * @property string $create_user
 * @property string $create_time
 * @property string $update_time
 */
class PersonSalary extends \CActiveRecord
{
    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'tbl_person_salary';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array();
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'person_salary_id' => 'Person Salary',
            'client_id' => 'Client',
            'user_id' => 'User',
            'salary_id' => 'Salary',
            'salary_name' => 'Salary Name',
            'head' => 'Head',
            'body' => 'Body',
            'is_distributed' => 'Is Distributed',
            'create_user' => 'Create User',
            'create_time' => 'Create Time',
            'update_time' => 'Update Time',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical usecase:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass data provider to CGridView, CListView or any similar widget.
     *
     * @return \CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function search()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria=new \CDbCriteria;

        $criteria->compare('person_salary_id',$this->person_salary_id);
        $criteria->compare('client_id',$this->client_id,true);
        $criteria->compare('user_id',$this->user_id,true);
        $criteria->compare('salary_id',$this->salary_id);
        $criteria->compare('salary_name',$this->salary_name,true);
        $criteria->compare('head',$this->head,true);
        $criteria->compare('body',$this->body,true);
        $criteria->compare('is_distributed',$this->is_distributed);
        $criteria->compare('create_user',$this->create_user,true);
        $criteria->compare('create_time',$this->create_time,true);
        $criteria->compare('update_time',$this->update_time,true);

        return new \CActiveDataProvider($this, array(
            'criteria'=>$criteria,
        ));
    }

    /**
     * @return \CDbConnection the database connection used for this class
     */
    public function getDbConnection()
    {
        return \Yii::app()->enterprise_db;
    }

    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return PersonSalary the static model class
     */
    public static function model($className=__CLASS__)
    {
        return parent::model($className);
    }
}