<?php

/**
 * This is the model class for table "tbl_user_statistics_external_day".
 *
 * The followings are the available columns in table 'tbl_user_statistics_external_day':
 * @property string $user_id
 * @property string $date
 * @property string $type
 * @property string $value
 * @property string $count
 */
class UserStatisticsExternalDay extends ProjectActiveRecord
{


    /**
     * 往来联系
     */
    const TYPE_LINKMAN = 1 ;

    /**
     * 往来邮件(发)
     */
    const TYPE_LINKMAN_SEND =11;

    /**
     * 往来邮件(收)
     */
    const TYPE_LINKMAN_RECEIVE = 111;


    /**
     * 邮箱后缀
     */
    const TYPE_EMAIL_EXTERNAL = 2 ;
    /**
     * 邮箱后缀(发)
     */
    const TYPE_EMAIL_EXTERNAL_SEND = 22;

    /**
     * 邮箱后缀(收)
     */
    const TYPE_EMAIL_EXTERNAL_RECEIVE = 222;


    /**
     * 抄送最多
     */
    const TYPE_EMAIL_CC = 3 ;

    /**
     * 抄送(发)
     */
    const TYPE_EMAIL_CC_SEND = 33;

    /**
     * 抄送(收)
     */
    const TYPE_EMAIL_CC_RECEIVE =333;



    public function primaryKey(){
        return array('user_id','date','type','value');
    }
    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'tbl_user_statistics_external_day';
    }
    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return UserStatisticsExternalDay the static model class
     */
    public static function model($className=__CLASS__)
    {
        return parent::model($className);
    }

    /**
     * @param $pk
     * @return array|CActiveRecord|mixed|null
     */
    public static function getModelByPk($pk){
       return self::model()->find('client_id=:client_id and user_id=:user_id and date=:date and type=:type and value=:value',
           array(
               ':client_id' => $pk['client_id'],
           ':user_id'=>$pk['user_id'],
           ':date'=>$pk['date'],
           ':type'=>$pk['type'],
           ':value'=>$pk['value']
       ));
    }


    public static function getInstance($params){

        $model = self::getModelByPk(array(
            'client_id' => $params['client_id'],
            'user_id'=>$params['user_id'],
            'date'=>$params['date'],
            'type'=>$params['type'],
            'value'=>$params['value']
        ));
        if(empty($model)){
            $model = new UserStatisticsExternalDay();
            $model->client_id = $params['client_id'];
            $model->user_id = $params['user_id'];
            $model->date = $params['date'];
            $model->type = $params['type'];
            $model->value = $params['value'] ;
            $model->save();
        }

        return $model;
    }

    /**
     * @param $user_id
     */
    public static function resetUserStatisticsExternal($user_id){

        UserStatisticsExternal::clearByUserId($user_id);
        $tableName = UserStatisticsExternal::model()->tableName();
        $sql = "insert into  $tableName (user_id,type,value,count)
         select user_id,type,value,sum(count) from tbl_user_statistics_external_day
         where user_id = $user_id  group by type, value";
        $command = self::model()->getDbConnection()->createCommand($sql);
        $command->execute();
    }

    /**
     * @param $user_id
     * @param null $begin_day
     * @param null $end_day
     */
    public static function clearByUserId($user_id,$begin_day = null,$end_day = null){
        //只重置邮件相关
        self::setConnection(self::getDbByUserId($user_id));
        $tableName = self::model()->tableName();
        $deleteType = array(
            UserStatisticsExternalDay::TYPE_LINKMAN,
            UserStatisticsExternalDay::TYPE_LINKMAN_RECEIVE,
            UserStatisticsExternalDay::TYPE_EMAIL_EXTERNAL,
            UserStatisticsExternalDay::TYPE_EMAIL_EXTERNAL_RECEIVE,
            UserStatisticsExternalDay::TYPE_LINKMAN,
            UserStatisticsExternalDay::TYPE_LINKMAN_SEND,
            UserStatisticsExternalDay::TYPE_EMAIL_EXTERNAL,
            UserStatisticsExternalDay::TYPE_EMAIL_EXTERNAL_SEND,
            UserStatisticsExternalDay::TYPE_EMAIL_CC,
            UserStatisticsExternalDay::TYPE_EMAIL_CC_SEND,
        );
        $sql = "delete from $tableName where user_id = $user_id
            and date_format(date,'%Y-%m-%d') between '$begin_day' and '$end_day' and type in ( ".implode(',',$deleteType).");";
        self::model()->getDbConnection()->createCommand($sql)->execute();
    }
}