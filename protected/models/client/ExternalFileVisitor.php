<?php

/**
 * This is the model class for table "tbl_external_file_visitor".
 *
 * The followings are the available columns in table 'tbl_external_file_visitor':
 * @property string $visitor_id
 * @property integer $client_id
 * @property integer $user_id
 * @property string $file_id
 * @property string $share_id
 * @property string $cookie_id
 * @property string $email
 * @property string $description
 * @property string $total_page
 * @property string $total_read_time
 * @property string $preview_count
 * @property string $download_count
 * @property string $last_read_page
 * @property string $last_read_ip
 * @property string $devices
 * @property string $last_read_time
 * @property string $create_time
 * @property string $update_time
 */
class ExternalFileVisitor extends ProjectActiveRecord
{
    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'tbl_external_file_visitor';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'visitor_id' => 'Visitor',
            'client_id' => 'Client',
            'user_id' => 'User',
            'file_id' => 'File',
            'share_id' => 'Share',
            'cookie_id' => 'Cookie',
            'email' => 'Email',
            'description' => 'Description',
            'total_page' => 'Total Page',
            'total_read_time' => 'Total Read Time',
            'preview_count' => 'Preview Count',
            'download_count' => 'Download Count',
            'last_read_page' => 'Last Read Page',
            'last_read_ip' => 'Last Read Ip',
            'devices' => 'Devices',
            'last_read_time' => 'Last Read Time',
            'create_time' => 'Create Time',
            'update_time' => 'Update Time',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical usecase:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass data provider to CGridView, CListView or any similar widget.
     *
     * @return CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function search()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria=new CDbCriteria;

        $criteria->compare('visitor_id',$this->visitor_id,true);
        $criteria->compare('client_id',$this->client_id);
        $criteria->compare('user_id',$this->user_id);
        $criteria->compare('file_id',$this->file_id,true);
        $criteria->compare('share_id',$this->share_id,true);
        $criteria->compare('cookie_id',$this->cookie_id,true);
        $criteria->compare('email',$this->email,true);
        $criteria->compare('description',$this->description,true);
        $criteria->compare('total_page',$this->total_page,true);
        $criteria->compare('total_read_time',$this->total_read_time,true);
        $criteria->compare('preview_count',$this->preview_count,true);
        $criteria->compare('download_count',$this->download_count,true);
        $criteria->compare('last_read_page',$this->last_read_page,true);
        $criteria->compare('last_read_ip',$this->last_read_ip,true);
        $criteria->compare('devices',$this->devices,true);
        $criteria->compare('last_read_time',$this->last_read_time,true);
        $criteria->compare('create_time',$this->create_time,true);
        $criteria->compare('update_time',$this->update_time,true);

        return new CActiveDataProvider($this, array(
            'criteria'=>$criteria,
        ));
    }

    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return ExternalFileVisitor the static model class
     */
    public static function model($className=__CLASS__)
    {
        return parent::model($className);
    }

    public function autoIncrementColumn()
    {
        return 'visitor_id';
    }
}