<?php

/**
 * This is the model class for table "tbl_mail_expose".
 *
 * The followings are the available columns in table 'tbl_mail_expose':
 * @property string $mail_id
 * @property string $user_id
 * @property integer $client_id
 * @property string $sub_mail_set
 * @property string $variable
 * @property string $create_time
 * @property string $update_time
 */
class MailExpose extends ProjectActiveRecord
{
    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'tbl_mail_expose';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('mail_id, user_id', 'required'),
            array('client_id', 'numerical', 'integerOnly'=>true),
            array('mail_id', 'length', 'max'=>20),
            array('user_id', 'length', 'max'=>10),
            array('sub_mail_set', 'length', 'max'=>256),
            // The following rule is used by search().
            // @todo Please remove those attributes that should not be searched.
            array('mail_id, user_id, client_id, sub_mail_set, variable, create_time, update_time', 'safe', 'on'=>'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'mail_id' => 'Mail',
            'user_id' => 'User',
            'client_id' => 'Client',
            'sub_mail_set' => 'Sub Mail Set',
            'variable' => 'Variable',
            'create_time' => 'Create Time',
            'update_time' => 'Update Time',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical usecase:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass data provider to CGridView, CListView or any similar widget.
     *
     * @return CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function search()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria=new CDbCriteria;

        $criteria->compare('mail_id',$this->mail_id,true);
        $criteria->compare('user_id',$this->user_id,true);
        $criteria->compare('client_id',$this->client_id);
        $criteria->compare('sub_mail_set',$this->sub_mail_set,true);
        $criteria->compare('variable',$this->variable,true);
        $criteria->compare('create_time',$this->create_time,true);
        $criteria->compare('update_time',$this->update_time,true);

        return new CActiveDataProvider($this, array(
            'criteria'=>$criteria,
        ));
    }

    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return MailExpose the static model class
     */
    public static function model($className=__CLASS__)
    {
        return parent::model($className);
    }

    public static function findByMailIds(array $mailIds)
    {
        $sql = 'select * from tbl_mail_expose where mail_id in('.implode(',',$mailIds).')';
        $result = self::model()->getDbConnection()->createCommand($sql)->queryAll(true);
        if (!$result) {
            return [];
        } else {
            $data = [];
            foreach ($result as $item) {
                $data[$item['mail_id']] = $item;
            }
            return $data;
        }
    }
}