<?php

/**
 * This is the model class for table "tbl_edm_email_track".
 *
 * The followings are the available columns in table 'tbl_edm_email_track':
 * @property string $user_id
 * @property string $email
 * @property string $client_id
 * @property string $track_id
 * @property string $customer_user
 * @property string $lead_user
 * @property string $open_count
 * @property string $open_url_count
 * @property string $reply_count
 * @property string $last_track_ip
 * @property string $last_track_time
 * @property string $last_track_country
 * @property string $last_reply_mail
 * @property string $create_time
 * @property string $update_time
 */
class EdmEmailTrackModel extends PgActiveRecord
{
    public function autoIncrementColumn()
    {
        return 'track_id';
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'tbl_edm_email_track';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return [];
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'user_id' => 'User',
            'email' => 'Email',
            'client_id' => 'Client Id',
            'tack_id' => 'Track Id',
            'open_count' => 'Open Count',
            'open_url_count' => 'Open Url Count',
            'reply_count' => 'Reply Count',
            'last_track_ip' => 'Last Track Ip',
            'last_track_time' => 'Last Track Time',
            'last_track_country' => 'Last Track Country',
            'last_reply_mail' => 'Last Reply Mail',
            'create_time' => 'Create Time',
            'update_time' => 'Update Time',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical usecase:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass data provider to CGridView, CListView or any similar widget.
     *
     * @return CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function search()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria=new CDbCriteria;

        $criteria->compare('user_id',$this->user_id,true);
        $criteria->compare('email',$this->email,true);
        $criteria->compare('customer_user',$this->customer_user,true);
        $criteria->compare('lead_user',$this->lead_user,true);
        $criteria->compare('open_count',$this->open_count,true);
        $criteria->compare('open_url_count',$this->open_url_count,true);
        $criteria->compare('reply_count',$this->reply_count,true);
        $criteria->compare('last_track_ip',$this->last_track_ip,true);
        $criteria->compare('last_track_time',$this->last_track_time,true);
        $criteria->compare('last_track_country',$this->last_track_country,true);
        $criteria->compare('last_reply_mail',$this->last_reply_mail,true);
        $criteria->compare('create_time',$this->create_time,true);
        $criteria->compare('update_time',$this->update_time,true);

        return new CActiveDataProvider($this, array(
            'criteria'=>$criteria,
        ));
    }

    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return EdmEmailTrackModel the static model class
     */
    public static function model($className=__CLASS__)
    {
        return parent::model($className);
    }
}