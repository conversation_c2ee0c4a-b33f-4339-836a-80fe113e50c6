<?php

/**
 * This is the model class for table "tbl_mail".
 *
 * The followings are the available columns in table 'tbl_mail':
 * @property string $mail_id
 * @property integer $mail_type
 * @property string $folder_id
 * @property string $sender
 * @property string $receiver
 * @property string $reply_to
 * @property string $cc
 * @property string $bcc
 * @property string $subject
 * @property integer $attach_flag
 * @property integer $urgent_flag
 * @property integer $receipt_flag
 * @property string $mail_server_uuid
 * @property integer $email_size
 * @property string $user_id
 * @property string $create_time
 * @property string $update_time
 * @property string $receive_time
 * @property integer $star_flag
 * @property integer $read_flag
 * @property integer $open_flag
 * @property integer $forward_flag
 * @property integer $reply_flag
 * @property integer $delete_flag
 * @property integer $time_flag
 * @property integer $send_status
 * @property string $reply_to_mail_id
 * @property integer $client_id
 * @property integer $distribute_flag
 * @property integer $user_mail_id
 * @property integer $track_flag
 * @property integer $bounce_flag
 * @property integer $relate_company_flag
 * @property integer $conversation_id
 * @property integer $status_bitmap_flag
 */
class Mail extends ProjectActiveRecord
{

    //0=未知, 1=发送失败 2=发送成功 3. 异步发送中
    const SEND_STATUS_SEND_UNKNOWN = 0;
    const SEND_STATUS_SEND_FAIL = 1;
    const SEND_STATUS_SEND_SUCCESS = 2;
    const SEND_STATUS_SENDING = 3;

    const MAIL_TYPE_UNKNOWN = 0;
    const MAIL_TYPE_RECEIVE = 1;
    const MAIL_TYPE_SEND = 2;


    const FOLDER_DRAFT_ID = 0;   //草稿
    const FOLDER_INBOX_ID = 1;  //收件
    const FOLDER_SEND_ID = 2; //发件
    const FOLDER_TRASH_ID = 5; //已删除
    const FOLDER_JUNK_ID = 6;  //垃圾邮件
    const FOLDER_DELETE_ID = 9; //永久删除  boss 端可恢复
    const FOLDER_IMPORT_ID = 10;    //JAVA可见，PHP不可见
    const FOLDER_HIDDEN_DRAFT_ID = 11;   //隐藏的草稿
    const FOLDER_EXPOSE_ID = 12; //群发子邮件

    const DELETE_FLAG_NONE = 0;
    /**
     * 已删除
     */
    const DELETE_FLAG_TRASH = 1;
    /**
     * 彻底删除
     */
    const DELETE_FLAG_DELETE = 2;

    /**
     * 已分发
     */
    const DISTRIBUTE_FLAG_NONE = 0;
    /**
     * 未分发
     */
    const DISTRIBUTE_FLAG_DONE = 1;

    const READ_FLAG_TRUE = 1;
    const READ_FLAG_FALSE = 0;

    const ARCHIVE_FLAG_TRUE = 1;
    const ARCHIVE_FLAG_FALSE = 0;


    const ATTACH_FLAG_TRUE = 1;
    const ATTACH_FLAG_FALSE = 0;

    const TIME_FLAG_TRUE = 1;
    const TIME_FLAG_FALSE = 0;

    const TRACK_FLAG_FALSE = 0;
    const TRACK_FLAG_TRUE = 1;

    const SUBJECT_LIMIT = 300;

    const CC_LIMIT = 20;
    const BCC_LIMIT = 20;

    const URGENT_FLAG_TRUE = 1;
    const URGENT_FLAG_FALSE = 0;

    //我方已回复
    const REPLY_TO = 1;
    //对方已回复
    const REPLAYED = 2;

    //离线回复
    const OFFLINE_REPLY = 1;
    //离线转发
    const OFFLINE_FORWARD = 2;

    protected $backup_conversation_id;

	//优质邮箱
	public const MAIL_LEVEL_GOOD = 1;

	//一般邮箱
	public const MAIL_LEVEL_GENERAL = 2;

	//未知邮箱
	public const MAIL_LEVEL_UNKNOWN = 3;

	//风险邮箱
	public const MAIL_LEVEL_RISK = 4;

	//不存在邮箱
	public const MAIL_LEVEL_ABSENT = 5;

    public static function getSySFolderList()
    {
        return array(
            self::FOLDER_DRAFT_ID  => \Yii::t('mail', 'FOLDER_DRAFT'),
            self::FOLDER_INBOX_ID  => \Yii::t('mail', 'FOLDER_INBOX'),
            self::FOLDER_SEND_ID   => \Yii::t('mail', 'FOLDER_SEND'),
            self::FOLDER_TRASH_ID  => \Yii::t('mail', 'FOLDER_TRASH'),
            self::FOLDER_JUNK_ID   => \Yii::t('mail', 'FOLDER_JUNK'),
            self::FOLDER_DELETE_ID => \Yii::t('mail', 'FOLDER_DELETE'),
        );
    }


    public static function getAllSysFolderList()
    {
        return [
            self::FOLDER_DRAFT_ID,
            self::FOLDER_INBOX_ID,
            self::FOLDER_SEND_ID,
            self::FOLDER_TRASH_ID,
            self::FOLDER_JUNK_ID,
            self::FOLDER_DELETE_ID,
            self::FOLDER_IMPORT_ID,
            self::FOLDER_HIDDEN_DRAFT_ID,
        ];
    }

    /**
     * 默认不包含已删除，垃圾箱和彻底删除的邮件
     * @return array
     */
    public static function getSearchExcludeFolderIds()
    {
        return [
            self::FOLDER_DRAFT_ID,
            self::FOLDER_TRASH_ID,
            self::FOLDER_JUNK_ID,
            self::FOLDER_DELETE_ID,
            self::FOLDER_HIDDEN_DRAFT_ID,
        ];
    }

    public static function getSearchExcludeFolderIdsForSubordinate(): array
    {
        return [
            self::FOLDER_DRAFT_ID,
            self::FOLDER_TRASH_ID,
            self::FOLDER_JUNK_ID,
            self::FOLDER_DELETE_ID,
            self::FOLDER_HIDDEN_DRAFT_ID,
        ];
    }

    public static function getWebSearchExcludeFolderIds()
    {
        return [
            self::FOLDER_DELETE_ID,
            self::FOLDER_HIDDEN_DRAFT_ID,
        ];
    }


	public function autoIncrementColumn()
    {
        return 'mail_id';
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'tbl_mail';
    }

    public function primaryKey()
    {
        return 'mail_id';
    }


    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        return array(
            array('mail_id','required'),
            array('mail_type, attach_flag, urgent_flag, receipt_flag, email_size, star_flag, read_flag, open_flag, reply_flag, forward_flag, delete_flag, time_flag, send_status, client_id, distribute_flag, track_flag', 'numerical', 'integerOnly'=>true),
            array('mail_id, folder_id, reply_to_mail_id', 'length', 'max'=>21),
            array('mail_server_uuid', 'length', 'max'=>128),
            array('user_id, imap_folder_id', 'length', 'max'=>11),
            array('user_mail_id', 'length', 'max'=>10),
            array('subject, create_time, receive_time', 'safe'),
            // The following rule is used by search().
            // @todo Please remove those attributes that should not be searched.
            array('mail_id, mail_type, folder_id, sender, receiver, reply_to, cc, bcc, subject, attach_flag, urgent_flag, receipt_flag, mail_server_uuid, email_size, user_id, user_mail_id, create_time, update_time, receive_time, star_flag, read_flag, open_flag, reply_flag, forward_flag, delete_flag, time_flag, send_status, reply_to_mail_id, client_id, imap_folder_id, distribute_flag, track_flag', 'safe', 'on'=>'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'mail_id' => 'Mail',
            'mail_type' => 'Mail Type',
            'folder_id' => 'Folder',
            'sender' => 'Sender',
            'receiver' => 'Receiver',
            'reply_to' => 'Reply To',
            'cc' => 'Cc',
            'bcc' => 'Bcc',
            'subject' => 'Subject',
            'attach_flag' => 'Attach Flag',
            'urgent_flag' => 'Urgent Flag',
            'receipt_flag' => 'Receipt Flag',
            'mail_server_uuid' => 'Mail Server Uuid',
            'email_size' => 'Email Size',
            'user_id' => 'User',
            'create_time' => 'Create Time',
            'update_time' => 'Update Time',
            'receive_time' => 'Receive Time',
            'star_flag' => 'Star Flag',
            'read_flag' => 'Read Flag',
            'open_flag' => 'Open Flag',
            'reply_flag' => 'Reply Flag',
            'delete_flag' => 'Delete Flag',
            'time_flag' => 'Time Flag',
            'send_status' => 'Send Status',
            'reply_to_mail_id' => 'Reply To Mail',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical usecase:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass data provider to CGridView, CListView or any similar widget.
     *
     * @return CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function search()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria=new CDbCriteria;

        $criteria->compare('mail_id',$this->mail_id,true);
        $criteria->compare('mail_type',$this->mail_type);
        $criteria->compare('folder_id',$this->folder_id,true);
        $criteria->compare('sender',$this->sender,true);
        $criteria->compare('receiver',$this->receiver,true);
        $criteria->compare('reply_to',$this->reply_to,true);
        $criteria->compare('cc',$this->cc,true);
        $criteria->compare('bcc',$this->bcc,true);
        $criteria->compare('subject',$this->subject,true);
        $criteria->compare('attach_flag',$this->attach_flag);
        $criteria->compare('urgent_flag',$this->urgent_flag);
        $criteria->compare('receipt_flag',$this->receipt_flag);
        $criteria->compare('mail_server_uuid',$this->mail_server_uuid,true);
        $criteria->compare('email_size',$this->email_size);
        $criteria->compare('user_id',$this->user_id,true);
        $criteria->compare('create_time',$this->create_time,true);
        $criteria->compare('update_time',$this->update_time,true);
        $criteria->compare('receive_time',$this->receive_time,true);
        $criteria->compare('star_flag',$this->star_flag);
        $criteria->compare('read_flag',$this->read_flag);
        $criteria->compare('open_flag',$this->open_flag);
        $criteria->compare('reply_flag',$this->reply_flag);
        $criteria->compare('delete_flag',$this->delete_flag);
        $criteria->compare('time_flag',$this->time_flag);
        $criteria->compare('send_status',$this->send_status);
        $criteria->compare('reply_to_mail_id',$this->reply_to_mail_id,true);
        return new CActiveDataProvider($this, array(
            'criteria'=>$criteria,
        ));
    }

    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return Mail the static model class
     */
    public static function model($className=__CLASS__)
    {
        return parent::model($className);
    }


    public static function getUnusualFolderIds(){
        return array(
            self::FOLDER_DRAFT_ID,
            self::FOLDER_TRASH_ID,
            self::FOLDER_JUNK_ID,
            self::FOLDER_DELETE_ID
        );
    }

    public function beforeSave()
    {
        $this->backup_conversation_id = $this->conversation_id;
        unset($this->conversation_id);
        return parent::beforeSave();
    }

    public function afterSave()
    {
        $this->conversation_id = $this->backup_conversation_id;
        return parent::afterSave();
    }
}
