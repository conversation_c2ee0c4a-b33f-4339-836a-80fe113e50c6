<?php
/**
 * Created by PhpStorm.
 * User: andy
 * Date: 2018/1/31
 * Time: 18:09
 */


/**
 * This is the model class for table "tbl_mail_draft_version".
 *
 * The followings are the available columns in table 'tbl_mail_draft_version':
 * @property string $user_id
 * @property string $version
 * @property string $mail_id
 * @property string $type
 * @property string $client_id
 * @property string $create_time
 */
class MailDraftVersion extends ProjectActiveRecord
{
    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'tbl_mail_draft_version';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('user_id, version, mail_id, type, client_id, create_time', 'required'),
            array('user_id, version, type, client_id', 'length', 'max'=>10),
            array('mail_id', 'length', 'max'=>20),
            // The following rule is used by search().
            // @todo Please remove those attributes that should not be searched.
            array('user_id, version, mail_id, type, client_id, create_time', 'safe', 'on'=>'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'user_id' => 'User',
            'version' => 'Version',
            'mail_id' => 'Mail',
            'type' => 'Type',
            'client_id' => 'Client',
            'create_time' => 'Create Time',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical usecase:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass data provider to CGridView, CListView or any similar widget.
     *
     * @return CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function search()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria=new CDbCriteria;

        $criteria->compare('user_id',$this->user_id,true);
        $criteria->compare('version',$this->version,true);
        $criteria->compare('mail_id',$this->mail_id,true);
        $criteria->compare('type',$this->type,true);
        $criteria->compare('client_id',$this->client_id,true);
        $criteria->compare('create_time',$this->create_time,true);

        return new CActiveDataProvider($this, array(
            'criteria'=>$criteria,
        ));
    }

    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return MailDraftVersion the static model class
     */
    public static function model($className=__CLASS__)
    {
        return parent::model($className);
    }
}