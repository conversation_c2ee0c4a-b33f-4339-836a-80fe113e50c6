<?php

/**
 * This is the model class for table "tbl_async_mail_task".
 *
 * The followings are the available columns in table 'tbl_async_mail_task':
 * @property string $mail_id
 * @property string $user_id
 * @property integer $status
 * @property integer $type
 * @property string $send_time
 * @property integer $fail_num
 * @property string $fail_desc
 * @property string $create_time
 * @property string $update_time
 * @property integer $track_flag
 * @property integer $group_flag
 * @property string $fail_code
 */
class AsyncMailTask extends ProjectActiveRecord
{
    //0:待发送 1:发送失败  2：已发送
    const STATUS_WAITING = 0;
    const STATUS_SEND_FAIL = 1;
    const STATUS_SEND_SUCCESS = 2;

    const GROUP_FLAG_NOTHING = 0;
    const GROUP_FLAG_ROOT_MAIL = 1; //模版邮件
    const GROUP_FLAG_CHILD_MAIL = 2; //子邮件，未发送
    const GROUP_FLAG_FINISH = 3; //子邮件，发送完成(可能失败)
    const GROUP_FLAG_CHILD_RESENT = 4; //子邮件被重新发送了
    const GROUP_FLAG_ROOT_TERMINATED = 5; //模版邮件被终止发送
    const GROUP_FLAG_CLIENT_ROOT_MAIL = 6;//客户端(桌面端群发单显多个发件人)邮件

    const TYPE_TIMING = 1;
    const TYPE_NOT_TIMING = 0;

    const TASK_ALL = 0;
    const TASK_SENDING = 1;
    const TASK_PLAN_SEND = 2;

    const SEND_FAIL_CODE_SEND_NUM_OVER_RESTRICT = 'SendNumOverRestrict';
    const SEND_FAIL_CODE_INVALID_RECEIVER_ADDRESS = 'InvalidReceiverAddress';
    const SEND_FAIL_CODE_SPAM_MAIL = 'SpamMail';
    const SEND_FAIL_CODE_SEND_MAIL_FREQUENT = 'SendMailFrequent';
    const SEND_FAIL_CODE_INVALID_AUTH_CODE = 'AuthCodeOrPassWordInvalid';
    const SEND_FAIL_CODE_ABNORMAL_NETWORK_CONNECTION = 'AbnormalNetworkConnection';
    const SEND_FAIL_CODE_UNKNOWN = 'UnknownError';
    const SEND_FAIL_CODE_OUTLOOK_SPAM_MAIL = 'OutlookSpamMail';
    const SEND_FAIL_CODE_ABNORMAL_SERVER = 'AbnormalServer';
    const SEND_FAIL_CODE_ACCESS_DENIED = 'AccessDenied';
    const SEND_FAILED_MSG = [
        self::SEND_FAIL_CODE_SEND_NUM_OVER_RESTRICT  => [
            'expose' => [
                'transfer_err_msg' => '该邮箱今日发送邮件数超过邮件服务商限制，建议改天再试（可设置定时发送，避免遗忘）',
                'replace_list' => ['定时发送']
            ],
            'normal' => [
                'transfer_err_msg' => '该邮箱今日发送邮件数超过邮件服务商限制，建议改天再试（可设置定时发送，避免遗忘）',
                'replace_list' => ['定时发送']
            ],
        ],
        self::SEND_FAIL_CODE_INVALID_RECEIVER_ADDRESS  => [
            'expose' => [
                'transfer_err_msg' => '收件人可能不存在，请检查后再试',
                'replace_list' => []
            ],
            'normal' => [
                'transfer_err_msg' => '收件人可能不存在，请检查后再试',
                'replace_list' => []
            ],
        ],
        self::SEND_FAIL_CODE_SPAM_MAIL  => [
            'expose' => [
                'transfer_err_msg' => '该邮件已被标为垃圾邮件，建议：1. 修改邮件主题和正文后再试；2. 延长发件间隔后再试',
                'replace_list' => ['延长发件间隔']
            ],
            'normal' => [
                'transfer_err_msg' => '该邮件已被标为垃圾邮件，建议修改邮件主题和正文后再试',
                'replace_list' => []
            ],
        ],
        self::SEND_FAIL_CODE_SEND_MAIL_FREQUENT  => [
            'expose' => [
                'transfer_err_msg' => '该邮箱发件过于频繁，请至少等待1小时后重试（可设置定时发送，避免遗忘）',
                'replace_list' => ['定时发送']
            ],
            'normal' => [
                'transfer_err_msg' => '该邮箱发件过于频繁，请至少等待1小时后重试（可设置定时发送，避免遗忘）',
                'replace_list' => ['定时发送']
            ],
        ],
        self::SEND_FAIL_CODE_INVALID_AUTH_CODE  => [
            'expose' => [
                'transfer_err_msg' => '邮箱绑定存在异常，请检测邮箱状态',
                'replace_list' => ['检测邮箱']
            ],
            'normal' => [
                'transfer_err_msg' => '邮箱绑定存在异常，请检测邮箱状态',
                'replace_list' => ['检测邮箱']
            ],
        ],
        self::SEND_FAIL_CODE_ABNORMAL_NETWORK_CONNECTION  => [
            'expose' => [
                'transfer_err_msg' => '本地网络不稳定，无法连接你的企业邮箱服务器，请确保网络连接正常后再试',
                'replace_list' => []
            ],
            'normal' => [
                'transfer_err_msg' => '本地网络不稳定，无法连接你的企业邮箱服务器，请确保网络连接正常后再试',
                'replace_list' => []
            ],
        ],
        self::SEND_FAIL_CODE_UNKNOWN  => [
            'expose' => [
                'transfer_err_msg' => '未知错误，请联系小满在线客服',
                'replace_list' => ['小满在线客服']
            ],
            'normal' => [
                'transfer_err_msg' => '未知错误，请联系小满在线客服',
                'replace_list' => ['小满在线客服']
            ],
        ],
        self::SEND_FAIL_CODE_OUTLOOK_SPAM_MAIL => [
            'expose' => [
                'transfer_err_msg' => '该邮件已被标为垃圾邮件，且邮箱可能已被服务商禁用，请登录Outlook解封邮箱之后，建议：1. 修改邮件主题和正文后再试；2. 延长发件间隔后再试',
                'replace_list' => ['Outlook', '延长发件间隔']
            ],
            'normal' => [
                'transfer_err_msg' => '该邮件已被标为垃圾邮件，且邮箱可能已被服务商禁用，请登录Outlook解封邮箱之后，建议修改邮件主题和正文后再试',
                'replace_list' => ['Outlook']
            ],
        ],
        self::SEND_FAIL_CODE_ABNORMAL_SERVER => [
            'expose' => [
                'transfer_err_msg' => '邮箱服务器不稳定，请10分钟后再试',
                'replace_list' => []
            ],
            'normal' => [
                'transfer_err_msg' => '邮箱服务器不稳定，请10分钟后再试',
                'replace_list' => []
            ],
        ],
        self::SEND_FAIL_CODE_ACCESS_DENIED => [
            'expose' => [
                'transfer_err_msg' => '邮箱服务商拒绝小满IP的发件，请联系35邮箱客服添加小满IP:xiaoManIp到白名单',
                'replace_list' => ['联系35邮箱客服']
            ],
            'normal' => [
                'transfer_err_msg' => '邮箱服务商拒绝小满IP的发件，请联系35邮箱客服添加小满IP:xiaoManIp到白名单',
                'replace_list' => ['联系35邮箱客服']
            ],
        ],
    ];

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'tbl_async_mail_task';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		return array();
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'mail_id' => 'Mail',
			'user_id' => 'User',
			'status' => 'Status',
			'type' => 'Type',
			'send_time' => 'Send Time',
			'fail_num' => 'Fail Num',
			'fail_desc' => 'Fail Desc',
			'create_time' => 'Create Time',
			'update_time' => 'Update Time',
			'track_flag' => 'Track Flag'
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('mail_id',$this->mail_id,true);
		$criteria->compare('user_id',$this->user_id,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('type',$this->type);
		$criteria->compare('send_time',$this->send_time,true);
		$criteria->compare('fail_num',$this->fail_num);
		$criteria->compare('fail_desc',$this->fail_desc,true);
		$criteria->compare('create_time',$this->create_time,true);
		$criteria->compare('update_time',$this->update_time,true);
		$criteria->compare('track_flag',$this->track_flag);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsyncMailTask the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}


    /**
     * @param $mailId
     * @return AsyncMailTask
     */
    public static function findByMailId($mailId){
        return self::model()->find('mail_id=:mail_id', array(':mail_id' => $mailId));
    }

    public static function findByMailIds(array $mailIds)
    {
        $sql = 'select * from tbl_async_mail_task where mail_id in('.implode(',',$mailIds).')';
        return self::model()->getDbConnection()->createCommand($sql)->queryAll(true);
    }
    /**
     * 删除定时邮件任务
     * @param $mailIds
     */
    public static function deleteUnsendTimeMails($mailIds){
        $c = new CDbCriteria();
        $c->addInCondition('mail_id', $mailIds);
        $c->addCondition('type=1 and status=0');
        return self::model()->deleteAll($c);
    }

	public static function deleteMailTaskByMailId($mail_id) {

		$mail_ids = is_array($mail_id) ? $mail_id : [$mail_id];

		$mail_ids = implode(',', $mail_ids);

		$sql = "delete from tbl_async_mail_task where mail_id IN ( {$mail_ids}) ";

		return self::model()->getDbConnection()->createCommand($sql)->execute();
	}

    //停止群发单显发送任务
    public static function stopMailTasks($mail_id)
    {
        if (is_array($mail_id)) {
            $mailIds = implode(',', $mail_id);
            $mailIdSql = " mail_id in ($mailIds)";
        } else {
            $mailIdSql = " mail_id = $mail_id";
        }

        $sql = "update tbl_async_mail_task set group_flag = ". self::GROUP_FLAG_ROOT_TERMINATED .  " where". $mailIdSql;
        return self::model()->getDbConnection()->createCommand($sql)->execute();
    }
}
