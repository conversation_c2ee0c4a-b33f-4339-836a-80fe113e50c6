<?php

/**
 * This is the model class for table "tbl_google_ads_gclid".
 *
 * The followings are the available columns in table 'tbl_google_ads_gclid':
 * @property string $id
 * @property string $client_id
 * @property string $ads_account_id
 * @property integer $site_id
 * @property string $gclid
 * @property string $site_session_id
 * @property string $campaign_id
 * @property string $ad_group_id
 * @property string $criteria_id
 * @property string $criteria_parameters
 * @property string $clicks
 * @property string $click_date
 * @property string $click_type
 * @property string $attribution_model
 * @property integer $report_flag
 * @property string $ads_conversion_id
 * @property string $conversion_date
 * @property string $create_time
 * @property string $update_time
 */
class GoogleAdsGclid extends ProjectActiveRecord
{
    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'tbl_google_ads_gclid';
    }

    public function autoIncrementColumn()
    {
        return 'id';
    }


    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('id, click_date, report_flag, create_time, update_time', 'required'),
            array('site_id, report_flag', 'numerical', 'integerOnly'=>true),
            array('id', 'length', 'max'=>11),
            array('client_id', 'length', 'max'=>10),
            array('ads_account_id, campaign_id, ad_group_id, criteria_id, clicks, ads_conversion_id', 'length', 'max'=>20),
            array('gclid', 'length', 'max'=>1024),
            array('site_session_id', 'length', 'max'=>128),
            array('criteria_parameters, click_type', 'length', 'max'=>256),
            array('attribution_model', 'length', 'max'=>25),
            array('conversion_date', 'safe'),
            // The following rule is used by search().
            // @todo Please remove those attributes that should not be searched.
            array('id, client_id, ads_account_id, site_id, gclid, site_session_id, campaign_id, ad_group_id, criteria_id, criteria_parameters, clicks, click_date, click_type, attribution_model, report_flag, ads_conversion_id, conversion_date, create_time, update_time', 'safe', 'on'=>'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'id' => 'ID',
            'client_id' => 'Client',
            'ads_account_id' => 'ads账号id',
            'site_id' => '网站id',
            'gclid' => 'googe click id',
            'site_session_id' => '网站会话id',
            'campaign_id' => '广告系列id',
            'ad_group_id' => '广告组id',
            'criteria_id' => '搜索关键字id',
            'criteria_parameters' => '搜索关键字',
            'clicks' => '点击数',
            'click_date' => '点击日期',
            'click_type' => '点击类型',
            'attribution_model' => '归因模型 form  表单',
            'report_flag' => '是否上报google  0 未上报 1 上报失败 2 上报一次',
            'ads_conversion_id' => '转化id',
            'conversion_date' => '转化日期',
            'create_time' => 'Create Time',
            'update_time' => 'Update Time',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical usecase:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass data provider to CGridView, CListView or any similar widget.
     *
     * @return CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function search()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria=new CDbCriteria;

        $criteria->compare('id',$this->id,true);
        $criteria->compare('client_id',$this->client_id,true);
        $criteria->compare('ads_account_id',$this->ads_account_id,true);
        $criteria->compare('site_id',$this->site_id);
        $criteria->compare('gclid',$this->gclid,true);
        $criteria->compare('site_session_id',$this->site_session_id,true);
        $criteria->compare('campaign_id',$this->campaign_id,true);
        $criteria->compare('ad_group_id',$this->ad_group_id,true);
        $criteria->compare('criteria_id',$this->criteria_id,true);
        $criteria->compare('criteria_parameters',$this->criteria_parameters,true);
        $criteria->compare('clicks',$this->clicks,true);
        $criteria->compare('click_date',$this->click_date,true);
        $criteria->compare('click_type',$this->click_type,true);
        $criteria->compare('attribution_model',$this->attribution_model,true);
        $criteria->compare('report_flag',$this->report_flag);
        $criteria->compare('ads_conversion_id',$this->ads_conversion_id,true);
        $criteria->compare('conversion_date',$this->conversion_date,true);
        $criteria->compare('create_time',$this->create_time,true);
        $criteria->compare('update_time',$this->update_time,true);

        return new CActiveDataProvider($this, array(
            'criteria'=>$criteria,
        ));
    }

    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return GoogleAdsGclid the static model class
     */
    public static function model($className=__CLASS__)
    {
        return parent::model($className);
    }
}
