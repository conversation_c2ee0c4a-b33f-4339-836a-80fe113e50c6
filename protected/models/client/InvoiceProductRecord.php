<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 17/4/24
 * Time: 上午10:23
 */

namespace common\models\client;
use common\components\BaseObject;
use common\library\invoice\Invoice;
use common\library\invoice\InvoiceProductRecordList;
use common\library\product_v2\Product;
use common\library\product_v2\ProductConstant;
use common\library\product_v2\ProductFilter;
use common\library\util\PgsqlUtil;

/**
 * This is the model class for table "tbl_invoice_product_record".
 *
 * The followings are the available columns in table 'tbl_invoice_product_record':
 * @property string $id
 * @property string $product_id
 * @property string $client_id
 * @property array  $user_id
 * @property string $company_id
 * @property string $opportunity_id
 * @property string $customer_id
 * @property string $record_name
 * @property integer $type
 * @property string $refer_id
 * @property string $unit
 * @property string $unit_price
 * @property string $currency
 * @property string $price_contract
 * @property string $count
 * @property string $amount_rmb
 * @property string $amount
 * @property integer $enable_flag
 * @property string $create_time
 * @property string $update_time
 * @property double $invoice_amount
 * @property double $invoice_amount_rmb
 * @property double other_cost
 * @property double cost_amount
 * @property string offer_data
 * @property string description
 * @property string package_unit
 * @property string cost_with_tax
 * @property string product_model
 * @property string product_remark
 * @property string count_per_package
 * @property string gross_profit_margin
 * @property double package_gross_weight_subtotal
 * @property double package_gross_weight
 * @property double package_volume_subtotal
 * @property double package_volume
 * @property string product_name
 * @property string ali_product_id
 * @property string product_image
 * @property string product_images
 * @property string platform_product_id
 * @property string platform_sku_id
 * @property string sku_attributes
 * @property string product_cn_name
 *
 */
class InvoiceProductRecord extends \PgActiveRecord
{

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'tbl_invoice_product_record';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('id, product_id, client_id, company_id, update_time', 'required'),
            array('type', 'numerical', 'integerOnly'=>true),
            array('amount,amount_rmb,invoice_amount, invoice_amount_rmb', 'numerical'),
            array('id, product_id, company_id, opportunity_id, customer_id, refer_id', 'length', 'max'=>20),
            array('client_id', 'length', 'max'=>11),
            array('record_name,count', 'length', 'max'=>255),
            array('unit, unit_price, currency, price_contract', 'length', 'max'=>50),
            array('create_time', 'safe'),
            // The following rule is used by search().
            // @todo Please remove those attributes that should not be searched.
            array('id, product_id, client_id, company_id, customer_id, record_name, type, refer_id, unit, unit_price, currency, price_contract, count, amount_rmb, amount, create_time, update_time, invoice_amount, invoice_amount_rmb', 'safe', 'on'=>'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'id' => 'ID',
            'product_id' => 'Product',
            'client_id' => 'Client',
            'company_id' => 'Company',
            'opportunity_id' => 'Opportunity',
            'customer_id' => 'Customer',
            'record_name' => 'Record Name',
            'type' => 'Type',
            'refer_id' => 'Refer',
            'unit' => 'Unit',
            'unit_price' => 'Unit Price',
            'currency' => 'Currency',
            'price_contract' => 'Price Contract',
            'count' => 'Count',
            'amount_rmb' => 'Amount Rmb',
            'amount' => 'Amount',
            'create_time' => 'Create Time',
            'update_time' => 'Update Time',
            'invoice_amount' => 'Invoice Amount',
            'invoice_amount_rmb' => 'Invoice Amount Rmb',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical usecase:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass data provider to CGridView, CListView or any similar widget.
     *
     * @return \CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function search()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria = new \CDbCriteria;

        $criteria->compare('id', $this->id, true);
        $criteria->compare('product_id', $this->product_id, true);
        $criteria->compare('client_id', $this->client_id, true);
        $criteria->compare('company_id', $this->company_id, true);
        $criteria->compare('opportunity_id', $this->opportunity_id, true);
        $criteria->compare('customer_id', $this->customer_id, true);
        $criteria->compare('record_name', $this->record_name, true);
        $criteria->compare('type', $this->type);
        $criteria->compare('refer_id', $this->refer_id, true);
        $criteria->compare('unit', $this->unit, true);
        $criteria->compare('unit_price', $this->unit_price, true);
        $criteria->compare('currency', $this->currency, true);
        $criteria->compare('price_contract', $this->price_contract, true);
        $criteria->compare('count', $this->count, true);
        $criteria->compare('amount_rmb', $this->amount_rmb);
        $criteria->compare('amount', $this->amount);
        $criteria->compare('create_time', $this->create_time, true);
        $criteria->compare('update_time', $this->update_time, true);
        $criteria->compare('invoice_amount', $this->invoice_amount);
        $criteria->compare('invoice_amount_rmb', $this->invoice_amount_rmb);

        return new \CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }

    public function primaryKey()
    {
        return 'id';
    }

    public function autoIncrementColumn()
    {
        return 'id';
    }

    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return InvoiceProductRecord the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    public static function clearRecord($clientId, $referId, $type, $enableFlag = 0, $ids = [])
    {
        $condition = 'client_id=:client_id and type=:type and refer_id=:refer_id';
        $params = [
            ':client_id' => $clientId,
            ':type' => $type,
            ':refer_id' => $referId,
        ];

        $attributes = [
            'enable_flag' => $enableFlag
        ];

        if (!empty($ids)) {
            $idsString = implode(',', $ids);
            $condition .= " and id in ($idsString)";
        }

        //return self::model()->deleteAll($condition,$params);
        return self::model()->updateAll($attributes, $condition, $params);
    }

    public static function clearSubRecord($clientId, $referId, $combineRecordIds, $type, $enableFlag = 0, $ids = [])
    {
        if(empty($combineRecordIds)){
            return ;
        }

        if(!is_array($referId)){
            $referId = array_filter([$referId]);
        }

        if(empty($referId)){
            return ;
        }

        $referIdsStr = implode(",", $referId);
        $combineRecordIdsStr = implode(",", $combineRecordIds);
        $condition = "client_id=:client_id and type=:type and refer_id in ({$referIdsStr}) and combine_record_id in ({$combineRecordIdsStr})";

        $params = [
            ':client_id' => $clientId,
            ':type' => $type,
        ];

        if (!empty($ids)) {
            $idsString = implode(',', $ids);
            $condition .= " and id in ($idsString)";
        }

        $attributes = [
            'enable_flag' => $enableFlag
        ];

        return self::model()->updateAll($attributes, $condition, $params);
    }

    public static function clearRecords($clientId, $referIds, $type)
    {
        $condition = 'client_id=:client_id and type=:type and refer_id IN (:refer_id)';
        $params = [
            ':client_id' => $clientId,
            ':type' => $type,
            ':refer_id' => implode(',', $referIds),
        ];

        return self::model()->deleteAll($condition, $params);
    }


    public static function updateRecord($clientId,$referId,$type,$data){

        $condition = 'client_id=:client_id and type=:type and refer_id=:refer_id';
        $params = [
            ':client_id' =>$clientId,
            ':type' =>$type,
            ':refer_id' =>$referId,
        ];

        if(isset($data['user_id'])){
            $data['user_id'] = PgsqlUtil::formatArray($data['user_id']);
        }

        if (isset($data['record_name'])) {
            $data['record_name'] = mb_substr($data['record_name'], 0, 100);
        }

        return self::model()->updateAll($data,$condition,$params);

    }

    public static function batchInsert($clientId,array $list){

        if(empty($list)) return ;
        $db = self::getDbByClientId($clientId);

        $sqlField = 'insert into tbl_invoice_product_record(id,product_id,sku_id, platform_sku_id,client_id,user_id,company_id,opportunity_id,
customer_id,record_name,type,refer_id,unit,unit_price,currency,price_contract,count,amount,amount_rmb,invoice_amount,
invoice_amount_rmb,create_time,update_time,status,amount_usd,invoice_amount_usd,product_name,other_cost,cost_amount,offer_data,
description,package_unit,cost_with_tax,product_model,product_remark,ali_product_id,count_per_package,gross_profit_margin,
package_gross_weight_subtotal,package_gross_weight,package_volume_subtotal,package_volume,external_field_data,enable_flag,
product_image,product_images,platform_product_id,sku_attributes,package_remark,product_type,combine_product_config,combine_record_id,
product_cn_name,sort,gross_margin,gross_margin_cny,gross_margin_usd,purchase_cost_unit,purchase_cost_unit_rmb,purchase_cost_unit_usd,
tax_refund_rate,vat_rate,package_count,is_master_product,master_id,ratio) values ';
        $onDuplicate = 'ON CONFLICT (id) DO UPDATE SET 
product_id=excluded.product_id,
sku_id=excluded.sku_id,
platform_sku_id = excluded.platform_sku_id,
client_id=excluded.client_id,
user_id=excluded.user_id,
company_id=excluded.company_id,
opportunity_id=excluded.opportunity_id,
customer_id=excluded.customer_id,
record_name=excluded.record_name,
type=excluded.type,
refer_id=excluded.refer_id,
unit=excluded.unit,
unit_price=excluded.unit_price,
currency=excluded.currency,
price_contract=excluded.price_contract,
count=excluded.count,
amount=excluded.amount,
amount_rmb=excluded.amount_rmb,
invoice_amount=excluded.invoice_amount,
invoice_amount_rmb=excluded.invoice_amount_rmb,
update_time=excluded.update_time,
status=excluded.status,
amount_usd=excluded.amount_usd,
invoice_amount_usd=excluded.invoice_amount_usd,
product_name=excluded.product_name,
other_cost=excluded.other_cost,
cost_amount=excluded.cost_amount,
offer_data=excluded.offer_data,
description=excluded.description,
package_unit=excluded.package_unit,
cost_with_tax=excluded.cost_with_tax,
product_model=excluded.product_model,
product_remark=excluded.product_remark,
ali_product_id=excluded.ali_product_id,
count_per_package=excluded.count_per_package,
gross_profit_margin=excluded.gross_profit_margin,
package_gross_weight_subtotal=excluded.package_gross_weight_subtotal,
package_gross_weight=excluded.package_gross_weight,
package_volume_subtotal=excluded.package_volume_subtotal,
package_volume=excluded.package_volume,
external_field_data=excluded.external_field_data,
enable_flag=excluded.enable_flag,
product_image=excluded.product_image,
product_images=excluded.product_images,
platform_product_id=excluded.platform_product_id,
sku_attributes=excluded.sku_attributes,
package_remark=excluded.package_remark,
product_type=excluded.product_type,
combine_record_id=excluded.combine_record_id,
product_cn_name=excluded.product_cn_name,
sort=excluded.sort,
gross_margin=excluded.gross_margin,
gross_margin_cny=excluded.gross_margin_cny,
gross_margin_usd=excluded.gross_margin_usd,
purchase_cost_unit=excluded.purchase_cost_unit,
purchase_cost_unit_rmb=excluded.purchase_cost_unit_rmb,
purchase_cost_unit_usd=excluded.purchase_cost_unit_usd,
combine_product_config=excluded.combine_product_config,
tax_refund_rate=excluded.tax_refund_rate,
vat_rate=excluded.vat_rate,
package_count=excluded.package_count,
is_master_product=excluded.is_master_product,
master_id=excluded.master_id,
ratio=excluded.ratio
';
        $sqlArr = [];

        $subStrFields = [
            'offer_data' => 240,
            'package_unit' => 240,
            'product_model' => 240,
            'product_name' => 240,
        ];

        foreach ($list as $elem){

            if (is_array($elem['user_id'])) {
                $elem['user_id'] = PgsqlUtil::formatArray($elem['user_id']);
            }

            if (!is_numeric($elem['product_id']))
                continue;

            //$id = self::produceAutoIncrementId();
            $id = $elem['id'];
            $elem['record_name'] = \Util::escapeDoubleQuoteSql(mb_substr($elem['record_name'] ?? '', 0, 100));
            $elem['unit'] = \Util::escapeDoubleQuoteSql($elem['unit'] ?? '');
            $elem['user_id'] = \Util::escapeDoubleQuoteSql($elem['user_id']);
            $elem['count'] = \Util::escapeDoubleQuoteSql(substr($elem['count'] ?? '', 0, 50));


            $elem['unit_price'] = floatval($elem['unit_price'] ?? 0);
            $elem['currency'] = $elem['currency'] ?? '';
            $elem['price_contract'] = $elem['price_contract'] ?? '';
            $elem['company_id'] = intval($elem['company_id'] ?? 0);
            $elem['opportunity_id'] = intval($elem['opportunity_id'] ?? 0);
            $elem['customer_id'] = intval($elem['customer_id'] ?? 0);
            $elem['amount'] = empty($elem['amount'])?'null':$elem['amount'];
            $elem['amount_rmb'] = empty($elem['amount_rmb'])?'null':$elem['amount_rmb'];
            $elem['amount_usd'] = empty($elem['amount_usd'])?'0':$elem['amount_usd'];
            $elem['invoice_amount'] = empty($elem['invoice_amount'])?'0':$elem['invoice_amount'];
            $elem['invoice_amount_rmb'] = empty($elem['invoice_amount_rmb'])?'0':$elem['invoice_amount_rmb'];
            $elem['invoice_amount_usd'] = empty($elem['invoice_amount_usd'])?'0':$elem['invoice_amount_usd'];
            $elem['status'] = $elem['status'] ?? 0;

            $elem['other_cost'] = empty($elem['other_cost'])?'0':(is_numeric($elem['other_cost'])?$elem['other_cost']:'0');
            $elem['cost_amount'] = empty($elem['cost_amount'])?'0':(is_numeric($elem['cost_amount'])?$elem['cost_amount']:'0');
            $elem['cost_with_tax'] = empty($elem['cost_with_tax'])?'0':(is_numeric($elem['cost_with_tax'])?$elem['cost_with_tax']:'0');
            $elem['count_per_package'] = empty($elem['count_per_package'])?'0':(is_numeric($elem['count_per_package'])?$elem['count_per_package']:'0');
            $elem['gross_profit_margin'] = empty($elem['gross_profit_margin'])?'0':(is_numeric($elem['gross_profit_margin'])?$elem['gross_profit_margin']:'0');//存在非数字类型的值例如"--"
            $elem['package_gross_weight_subtotal'] = empty($elem['package_gross_weight_subtotal'])?'0':$elem['package_gross_weight_subtotal'];
            $elem['package_gross_weight'] = empty($elem['package_gross_weight'])?'0':(is_numeric($elem['package_gross_weight'])?$elem['package_gross_weight']:'0');
            $elem['package_volume_subtotal'] = empty($elem['package_volume_subtotal'])?'0':(is_numeric($elem['package_volume_subtotal'])?$elem['package_volume_subtotal']:'0');
            $elem['package_volume'] = empty($elem['package_volume'])?'0':(is_numeric($elem['package_volume'])?$elem['package_volume']:'0');
            $elem['package_remark'] = \Util::escapeDoubleQuoteSql($elem['package_remark']??'');
            $elem['enable_flag'] = isset($elem['enable_flag']) ? intval($elem['enable_flag']) : 1;
            $elem['product_type'] = isset($elem['product_type']) ? intval($elem['product_type']) : 0;
            $elem['combine_product_config'] = \Util::escapeDoubleQuoteSql($elem['combine_product_config']??'');
            $elem['combine_record_id'] = isset($elem['combine_record_id']) ? intval($elem['combine_record_id']) : 0;
            $elem['sort'] = isset($elem['sort']) ? intval($elem['sort']) : 0;

            //字符串截断
            foreach ($subStrFields as $subStrField => $mbStrLen)
            {
                $elem[$subStrField] = $elem[$subStrField] ?? '';
                if (!isset($elem[$subStrField]) || (mb_strlen($elem[$subStrField]) <= $mbStrLen))
                    continue;

                $elem[$subStrField] = mb_substr($elem[$subStrField], 0, $mbStrLen,'UTF-8');
            }

            $elem['description'] = \Util::escapeDoubleQuoteSql($elem['description'] ?? '');
            $elem['product_remark'] = \Util::escapeDoubleQuoteSql($elem['product_remark'] ?? '');
            $elem['ali_product_id'] = $elem['ali_product_id'] ?? 0;
            $elem['offer_data'] = \Util::escapeDoubleQuoteSql($elem['offer_data'] ?? '');
            $elem['product_model'] = \Util::escapeDoubleQuoteSql($elem['product_model'] ?? '');
            $elem['product_name'] = \Util::escapeDoubleQuoteSql($elem['product_name'] ?? '');
            $elem['product_cn_name'] = \Util::escapeDoubleQuoteSql($elem['product_cn_name'] ?? '');
            $elem['product_image'] = \Util::escapeDoubleQuoteSql($elem['product_image'] ?? '');
            $elem['product_images'] = !empty($elem['product_images']) && is_array($elem['product_images']) ? json_encode($elem['product_images']) : '{}';
            $elem['sku_id'] = intval($elem['sku_id']);
            $elem['platform_sku_id'] = intval($elem['platform_sku_id'] ?? 0);
            $elem['platform_product_id'] = intval($elem['platform_product_id'] ?? 0);
            $elem['sku_attributes'] = empty($elem['sku_attributes']) ? '{}' : json_encode($elem['sku_attributes']);
            $elem['sku_attributes'] = \Util::escapeDoubleQuoteSql($elem['sku_attributes']??'');

            $elem['external_field_data'] = empty($elem['external_field_data']) ? '{}' : json_encode($elem['external_field_data'], JSON_UNESCAPED_UNICODE);
            $elem['external_field_data'] = str_replace("'", "''", $elem['external_field_data']);
            $elem['create_time'] = $elem['create_time'] ?? date('Y-m-d H:i:s');
            $elem['update_time'] = $elem['update_time'] ?? date('Y-m-d H:i:s');

            $elem['gross_margin'] = $elem['gross_margin'] ?? 0;
            $elem['gross_margin_cny'] = $elem['gross_margin_cny'] ?? 0;
            $elem['gross_margin_usd'] = $elem['gross_margin_usd'] ?? 0;
            $elem['purchase_cost_unit'] = $elem['purchase_cost_unit'] ?? 'null';
            $elem['purchase_cost_unit_rmb'] = $elem['purchase_cost_unit_rmb'] ?? 'null';
            $elem['purchase_cost_unit_usd'] = $elem['purchase_cost_unit_usd'] ?? 'null';
            $elem['tax_refund_rate'] = (!empty($elem['tax_refund_rate']) || (isset($elem['tax_refund_rate']) && $elem['tax_refund_rate'] == 0)) ? $elem['tax_refund_rate'] : 'null';
            $elem['vat_rate'] = (!empty($elem['vat_rate']) || (isset($elem['vat_rate']) && $elem['vat_rate'] == 0)) ? $elem['vat_rate'] : 'null';
            $elem['package_count'] = ($elem['count_per_package'] != 0 && is_numeric($elem['count'])) ? ceil(floatval($elem['count']) / $elem['count_per_package']) : 0;
            $elem['is_master_product'] = intval($elem['is_master_product'] ?? 0);
            $elem['master_id'] = intval($elem['master_id'] ?? 0);
            $elem['ratio'] = floatval($elem['ratio'] ?? 0);
            //https://www.postgresql.org/docs/9.0/sql-syntax-lexical.html
            $sqlArr[]= '('.
                "'{$id}','{$elem['product_id']}','{$elem['sku_id']}','{$elem['platform_sku_id']}','{$elem['client_id']}','{$elem['user_id']}','{$elem['company_id']}','{$elem['opportunity_id']}','{$elem['customer_id']}','{$elem['record_name']}','{$elem['type']}',".
                "{$elem['refer_id']},'{$elem['unit']}','{$elem['unit_price']}','{$elem['currency']}','{$elem['price_contract']}','{$elem['count']}',".
                "{$elem['amount']},{$elem['amount_rmb']},'{$elem['invoice_amount']}','{$elem['invoice_amount_rmb']}','{$elem['create_time']}','{$elem['update_time']}',{$elem['status']},{$elem['amount_usd']},{$elem['invoice_amount_usd']}"
                .",'{$elem['product_name']}',{$elem['other_cost']},{$elem['cost_amount']},'{$elem['offer_data']}',
                E'{$elem['description']}','{$elem['package_unit']}',{$elem['cost_with_tax']},'{$elem['product_model']}',
                E'{$elem['product_remark']}','{$elem['ali_product_id']}',{$elem['count_per_package']},{$elem['gross_profit_margin']},
                {$elem['package_gross_weight_subtotal']},{$elem['package_gross_weight']},
                {$elem['package_volume_subtotal']},{$elem['package_volume']},'{$elem['external_field_data']}',{$elem['enable_flag']},'{$elem['product_image']}','{$elem['product_images']}','{$elem['platform_product_id']}',E'{$elem['sku_attributes']}',E'{$elem['package_remark']}',
                {$elem['product_type']},E'{$elem['combine_product_config']}',{$elem['combine_record_id']},'{$elem['product_cn_name']}',{$elem['sort']},{$elem['gross_margin']},{$elem['gross_margin_cny']},{$elem['gross_margin_usd']},{$elem['purchase_cost_unit']},{$elem['purchase_cost_unit_rmb']},{$elem['purchase_cost_unit_usd']}
                ,{$elem['tax_refund_rate']},{$elem['vat_rate']},{$elem['package_count']},{$elem['is_master_product']},{$elem['master_id']},{$elem['ratio']}"
            .')';
        }

        if(!empty($sqlArr)){
            $sql = $sqlField.implode(',',$sqlArr).$onDuplicate;
            $db->createCommand($sql)->execute();
        }

    }
    
    public static function newBatchInsert($clientId,array $list){
    
            if(empty($list)) return ;
            $db = self::getDbByClientId($clientId);
    
            $sqlField = 'insert into tbl_invoice_product_record(id,product_id,sku_id, platform_sku_id,client_id,user_id,company_id,opportunity_id,
    customer_id,record_name,type,refer_id,unit,unit_price,currency,price_contract,count,amount,amount_rmb,invoice_amount,
    invoice_amount_rmb,create_time,update_time,status,amount_usd,invoice_amount_usd,product_name,other_cost,cost_amount,offer_data,
    description,package_unit,cost_with_tax,product_model,product_remark,ali_product_id,count_per_package,gross_profit_margin,
    package_gross_weight_subtotal,package_gross_weight,package_volume_subtotal,package_volume,external_field_data,enable_flag,
    product_image,product_images,platform_product_id,sku_attributes,package_remark,product_type,combine_product_config,combine_record_id,
    product_cn_name,sort,gross_margin,gross_margin_cny,gross_margin_usd,purchase_cost_unit,purchase_cost_unit_rmb,purchase_cost_unit_usd,
    tax_refund_rate,vat_rate,package_count,is_master_product,master_id,ratio) values ';
            $onDuplicate = 'ON CONFLICT (id) DO UPDATE SET 
    product_id=excluded.product_id,
    sku_id=excluded.sku_id,
    platform_sku_id = excluded.platform_sku_id,
    client_id=excluded.client_id,
    user_id=excluded.user_id,
    company_id=excluded.company_id,
    opportunity_id=excluded.opportunity_id,
    customer_id=excluded.customer_id,
    record_name=excluded.record_name,
    type=excluded.type,
    refer_id=excluded.refer_id,
    unit=excluded.unit,
    unit_price=excluded.unit_price,
    currency=excluded.currency,
    price_contract=excluded.price_contract,
    count=excluded.count,
    amount=excluded.amount,
    amount_rmb=excluded.amount_rmb,
    invoice_amount=excluded.invoice_amount,
    invoice_amount_rmb=excluded.invoice_amount_rmb,
    update_time=excluded.update_time,
    status=excluded.status,
    amount_usd=excluded.amount_usd,
    invoice_amount_usd=excluded.invoice_amount_usd,
    product_name=excluded.product_name,
    other_cost=excluded.other_cost,
    cost_amount=excluded.cost_amount,
    offer_data=excluded.offer_data,
    description=excluded.description,
    package_unit=excluded.package_unit,
    cost_with_tax=excluded.cost_with_tax,
    product_model=excluded.product_model,
    product_remark=excluded.product_remark,
    ali_product_id=excluded.ali_product_id,
    count_per_package=excluded.count_per_package,
    gross_profit_margin=excluded.gross_profit_margin,
    package_gross_weight_subtotal=excluded.package_gross_weight_subtotal,
    package_gross_weight=excluded.package_gross_weight,
    package_volume_subtotal=excluded.package_volume_subtotal,
    package_volume=excluded.package_volume,
    external_field_data=excluded.external_field_data,
    enable_flag=excluded.enable_flag,
    product_image=excluded.product_image,
    product_images=excluded.product_images,
    platform_product_id=excluded.platform_product_id,
    sku_attributes=excluded.sku_attributes,
    package_remark=excluded.package_remark,
    product_type=excluded.product_type,
    combine_record_id=excluded.combine_record_id,
    product_cn_name=excluded.product_cn_name,
    sort=excluded.sort,
    gross_margin=excluded.gross_margin,
    gross_margin_cny=excluded.gross_margin_cny,
    gross_margin_usd=excluded.gross_margin_usd,
    purchase_cost_unit=excluded.purchase_cost_unit,
    purchase_cost_unit_rmb=excluded.purchase_cost_unit_rmb,
    purchase_cost_unit_usd=excluded.purchase_cost_unit_usd,
    combine_product_config=excluded.combine_product_config,
    tax_refund_rate=excluded.tax_refund_rate,
    vat_rate=excluded.vat_rate,
    package_count=excluded.package_count,
    is_master_product=excluded.is_master_product,
    master_id=excluded.master_id,
    ratio=excluded.ratio
    ';
            $sqlData = [];
    
            $subStrFields = [
                'offer_data' => 240,
                'package_unit' => 240,
                'product_model' => 240,
                'product_name' => 240,
            ];
    
            foreach ($list as $bindIndex => $elem){
    
                if (is_array($elem['user_id'])) {
                    $elem['user_id'] = PgsqlUtil::formatArray($elem['user_id']);
                }
    
                if (!is_numeric($elem['product_id']))
                    continue;
    
                $id = $elem['id'];
                $elem['record_name'] = mb_substr($elem['record_name'] ?? '', 0, 100);
                $elem['unit'] = $elem['unit'] ?? '';
                $elem['user_id'] = $elem['user_id'];
                $elem['count'] = substr($elem['count'] ?? '', 0, 50);
    
    
                $elem['unit_price'] = floatval($elem['unit_price'] ?? 0);
                $elem['currency'] = $elem['currency'] ?? '';
                $elem['price_contract'] = $elem['price_contract'] ?? '';
                $elem['company_id'] = intval($elem['company_id'] ?? 0);
                $elem['opportunity_id'] = intval($elem['opportunity_id'] ?? 0);
                $elem['customer_id'] = intval($elem['customer_id'] ?? 0);
                $elem['amount'] = empty($elem['amount']) ? null : $elem['amount'];
                $elem['amount_rmb'] = empty($elem['amount_rmb']) ? null:$elem['amount_rmb'];
                $elem['amount_usd'] = empty($elem['amount_usd']) ?0 : $elem['amount_usd'];
                $elem['invoice_amount'] = empty($elem['invoice_amount'])?0:$elem['invoice_amount'];
                $elem['invoice_amount_rmb'] = empty($elem['invoice_amount_rmb'])?0:$elem['invoice_amount_rmb'];
                $elem['invoice_amount_usd'] = empty($elem['invoice_amount_usd'])?0:$elem['invoice_amount_usd'];
                $elem['status'] = $elem['status'] ?? 0;
    
                $elem['other_cost'] = empty($elem['other_cost'])?0:(is_numeric($elem['other_cost'])?$elem['other_cost']:0);
                $elem['cost_amount'] = empty($elem['cost_amount'])?0:(is_numeric($elem['cost_amount'])?$elem['cost_amount']:0);
                $elem['cost_with_tax'] = empty($elem['cost_with_tax'])?0:(is_numeric($elem['cost_with_tax'])?$elem['cost_with_tax']:0);
                $elem['count_per_package'] = empty($elem['count_per_package'])?0:(is_numeric($elem['count_per_package'])?$elem['count_per_package']:0);
                $elem['gross_profit_margin'] = empty($elem['gross_profit_margin'])?0:(is_numeric($elem['gross_profit_margin'])?$elem['gross_profit_margin']:0);//存在非数字类型的值例如"--"
                $elem['package_gross_weight_subtotal'] = empty($elem['package_gross_weight_subtotal'])?'0':$elem['package_gross_weight_subtotal'];
                $elem['package_gross_weight'] = empty($elem['package_gross_weight'])?0:(is_numeric($elem['package_gross_weight'])?$elem['package_gross_weight']:0);
                $elem['package_volume_subtotal'] = empty($elem['package_volume_subtotal'])?0:(is_numeric($elem['package_volume_subtotal'])?$elem['package_volume_subtotal']:0);
                $elem['package_volume'] = empty($elem['package_volume'])?0:(is_numeric($elem['package_volume'])?$elem['package_volume']:'0');
                $elem['package_remark'] = $elem['package_remark']??'';
                $elem['enable_flag'] = isset($elem['enable_flag']) ? intval($elem['enable_flag']) : 1;
                $elem['product_type'] = isset($elem['product_type']) ? intval($elem['product_type']) : 0;
                $elem['combine_product_config'] = $elem['combine_product_config']??'';
                $elem['combine_record_id'] = isset($elem['combine_record_id']) ? intval($elem['combine_record_id']) : 0;
                $elem['sort'] = isset($elem['sort']) ? intval($elem['sort']) : 0;
                //字符串截断
                foreach ($subStrFields as $subStrField => $mbStrLen)
                {
                    $elem[$subStrField] = $elem[$subStrField] ?? '';
                    if (!isset($elem[$subStrField]) || (mb_strlen($elem[$subStrField]) <= $mbStrLen))
                        continue;
    
                    $elem[$subStrField] = mb_substr($elem[$subStrField], 0, $mbStrLen,'UTF-8');
                }
    
                $elem['description'] = $elem['description'] ?? '';
                $elem['product_remark'] = $elem['product_remark'] ?? '';
                $elem['ali_product_id'] = $elem['ali_product_id'] ?? 0;
                $elem['offer_data'] = $elem['offer_data'] ?? '';
                $elem['product_model'] = $elem['product_model'] ?? '';
                $elem['product_name'] = $elem['product_name'] ?? '';
                $elem['product_cn_name'] = $elem['product_cn_name'] ?? '';
                $elem['product_image'] = $elem['product_image'] ?? '';
                $elem['product_images'] = !empty($elem['product_images']) && is_array($elem['product_images']) ? json_encode($elem['product_images']) : '{}';
                $elem['sku_id'] = intval($elem['sku_id']);
                $elem['platform_sku_id'] = intval($elem['platform_sku_id'] ?? 0);
                $elem['platform_product_id'] = intval($elem['platform_product_id'] ?? 0);
                $elem['sku_attributes'] = empty($elem['sku_attributes']) ? '{}' : json_encode($elem['sku_attributes']);
                $elem['sku_attributes'] = $elem['sku_attributes']??'';
    
                $elem['external_field_data'] = empty($elem['external_field_data']) ? '{}' : json_encode($elem['external_field_data'], JSON_UNESCAPED_UNICODE);
                $elem['external_field_data'] = str_replace("'", "''", $elem['external_field_data']);
                $elem['create_time'] = $elem['create_time'] ?? date('Y-m-d H:i:s');
                $elem['update_time'] = $elem['update_time'] ?? date('Y-m-d H:i:s');
    
                $elem['gross_margin'] = $elem['gross_margin'] ?? 0;
                $elem['gross_margin_cny'] = $elem['gross_margin_cny'] ?? 0;
                $elem['gross_margin_usd'] = $elem['gross_margin_usd'] ?? 0;
                $elem['purchase_cost_unit'] = $elem['purchase_cost_unit'] ?? null;
                $elem['purchase_cost_unit_rmb'] = $elem['purchase_cost_unit_rmb'] ?? null;
                $elem['purchase_cost_unit_usd'] = $elem['purchase_cost_unit_usd'] ?? null;
                $elem['tax_refund_rate'] = (!empty($elem['tax_refund_rate']) || (isset($elem['tax_refund_rate']) && $elem['tax_refund_rate'] == 0)) ? $elem['tax_refund_rate'] : null;
                $elem['vat_rate'] = (!empty($elem['vat_rate']) || (isset($elem['vat_rate']) && $elem['vat_rate'] == 0)) ? $elem['vat_rate'] : null;
                $elem['package_count'] = ($elem['count_per_package'] != 0 && is_numeric($elem['count'])) ? ceil(floatval($elem['count']) / $elem['count_per_package']) : 0;
                $elem['is_master_product'] = intval($elem['is_master_product'] ?? 0);
                $elem['master_id'] = intval($elem['master_id'] ?? 0);
                $elem['ratio'] = floatval($elem['ratio'] ?? 0);
                
                $sqlData[] = [
                    ":id{$bindIndex}" => $id,
                    ":product_id{$bindIndex}" => $elem['product_id'],
                    ":sku_id{$bindIndex}" => $elem['sku_id'],
                    ":platform_sku_id{$bindIndex}" => $elem['platform_sku_id'],
                    ":client_id{$bindIndex}" => $elem['client_id'],
                    ":user_id{$bindIndex}" => $elem['user_id'],
                    ":company_id{$bindIndex}" => $elem['company_id'],
                    ":opportunity_id{$bindIndex}" => $elem['opportunity_id'],
                    ":customer_id{$bindIndex}" => $elem['customer_id'],
                    ":record_name{$bindIndex}" => $elem['record_name'],
                    ":type{$bindIndex}" => $elem['type'],
                    ":refer_id{$bindIndex}" => $elem['refer_id'],
                    ":unit{$bindIndex}" => $elem['unit'],
                    ":unit_price{$bindIndex}" => $elem['unit_price'],
                    ":currency{$bindIndex}" => $elem['currency'],
                    ":price_contract{$bindIndex}" => $elem['price_contract'],
                    ":count{$bindIndex}" => $elem['count'],
                    ":amount{$bindIndex}" => $elem['amount'],
                    ":amount_rmb{$bindIndex}" => $elem['amount_rmb'],
                    ":invoice_amount{$bindIndex}" => $elem['invoice_amount'],
                    ":invoice_amount_rmb{$bindIndex}" => $elem['invoice_amount_rmb'],
                    ":create_time{$bindIndex}" => $elem['create_time'],
                    ":update_time{$bindIndex}" => $elem['update_time'],
                    ":status{$bindIndex}" => $elem['status'],
                    ":amount_usd{$bindIndex}" => $elem['amount_usd'],
                    ":invoice_amount_usd{$bindIndex}" => $elem['invoice_amount_usd'],
                    ":product_name{$bindIndex}" => $elem['product_name'],
                    ":other_cost{$bindIndex}" => $elem['other_cost'],
                    ":cost_amount{$bindIndex}" => $elem['cost_amount'],
                    ":offer_data{$bindIndex}" => $elem['offer_data'],
                    ":description{$bindIndex}" => $elem['description'],
                    ":package_unit{$bindIndex}" => $elem['package_unit'],
                    ":cost_with_tax{$bindIndex}" => $elem['cost_with_tax'],
                    ":product_model{$bindIndex}" => $elem['product_model'],
                    ":product_remark{$bindIndex}" => $elem['product_remark'],
                    ":ali_product_id{$bindIndex}" => $elem['ali_product_id'],
                    ":count_per_package{$bindIndex}" => $elem['count_per_package'],
                    ":gross_profit_margin{$bindIndex}" => $elem['gross_profit_margin'],
                    ":package_gross_weight_subtotal{$bindIndex}" => $elem['package_gross_weight_subtotal'],
                    ":package_gross_weight{$bindIndex}" => $elem['package_gross_weight'],
                    ":package_volume_subtotal{$bindIndex}" => $elem['package_volume_subtotal'],
                    ":package_volume{$bindIndex}" => $elem['package_volume'],
                    ":external_field_data{$bindIndex}" => $elem['external_field_data'],
                    ":enable_flag{$bindIndex}" => $elem['enable_flag'],
                    ":product_image{$bindIndex}" => $elem['product_image'],
                    ":product_images{$bindIndex}" => $elem['product_images'],
                    ":platform_product_id{$bindIndex}" => $elem['platform_product_id'],
                    ":sku_attributes{$bindIndex}" => $elem['sku_attributes'],
                    ":package_remark{$bindIndex}" => $elem['package_remark'],
                    ":product_type{$bindIndex}" => $elem['product_type'],
                    ":combine_product_config{$bindIndex}" => $elem['combine_product_config'],
                    ":combine_record_id{$bindIndex}" => $elem['combine_record_id'],
                    ":product_cn_name{$bindIndex}" => $elem['product_cn_name'],
                    ":sort{$bindIndex}" => $elem['sort'],
                    ":gross_margin{$bindIndex}" => $elem['gross_margin'],
                    ":gross_margin_cny{$bindIndex}" => $elem['gross_margin_cny'],
                    ":gross_margin_usd{$bindIndex}" => $elem['gross_margin_usd'],
                    ":purchase_cost_unit{$bindIndex}" => $elem['purchase_cost_unit'],
                    ":purchase_cost_unit_rmb{$bindIndex}" => $elem['purchase_cost_unit_rmb'],
                    ":purchase_cost_unit_usd{$bindIndex}" => $elem['purchase_cost_unit_usd'],
                    ":tax_refund_rate{$bindIndex}" => $elem['tax_refund_rate'],
                    ":vat_rate{$bindIndex}" => $elem['vat_rate'],
                    ":package_count{$bindIndex}" => $elem['package_count'],
                    ":is_master_product{$bindIndex}" => $elem['is_master_product'],
                    ":master_id{$bindIndex}" => $elem['master_id'],
                    ":ratio{$bindIndex}" => $elem['ratio'],
                ];
                //https://www.postgresql.org/docs/9.0/sql-syntax-lexical.html
                /*
                $sqlArr[]= '('.
                    "'{$id}','{$elem['product_id']}','{$elem['sku_id']}','{$elem['platform_sku_id']}','{$elem['client_id']}','{$elem['user_id']}','{$elem['company_id']}','{$elem['opportunity_id']}','{$elem['customer_id']}','{$elem['record_name']}','{$elem['type']}',".
                    "{$elem['refer_id']},'{$elem['unit']}','{$elem['unit_price']}','{$elem['currency']}','{$elem['price_contract']}','{$elem['count']}',".
                    "{$elem['amount']},{$elem['amount_rmb']},'{$elem['invoice_amount']}','{$elem['invoice_amount_rmb']}','{$elem['create_time']}','{$elem['update_time']}',{$elem['status']},{$elem['amount_usd']},{$elem['invoice_amount_usd']}"
                    .",'{$elem['product_name']}',{$elem['other_cost']},{$elem['cost_amount']},'{$elem['offer_data']}',
                    E'{$elem['description']}','{$elem['package_unit']}',{$elem['cost_with_tax']},'{$elem['product_model']}',
                    E'{$elem['product_remark']}','{$elem['ali_product_id']}',{$elem['count_per_package']},{$elem['gross_profit_margin']},
                    {$elem['package_gross_weight_subtotal']},{$elem['package_gross_weight']},
                    {$elem['package_volume_subtotal']},{$elem['package_volume']},'{$elem['external_field_data']}',{$elem['enable_flag']},'{$elem['product_image']}','{$elem['product_images']}','{$elem['platform_product_id']}',E'{$elem['sku_attributes']}',E'{$elem['package_remark']}',
                    {$elem['product_type']},E'{$elem['combine_product_config']}',{$elem['combine_record_id']},'{$elem['product_cn_name']}',{$elem['sort']},{$elem['gross_margin']},{$elem['gross_margin_cny']},{$elem['gross_margin_usd']},{$elem['purchase_cost_unit']},{$elem['purchase_cost_unit_rmb']},{$elem['purchase_cost_unit_usd']}
                    ,{$elem['tax_refund_rate']},{$elem['vat_rate']},{$elem['package_count']},{$elem['is_master_product']},{$elem['master_id']},{$elem['ratio']}"
                .')';
                */
            }

            if (!empty($sqlData)) {
                $sqlDataStr = $sqlDataBind = [];
                foreach ($sqlData as $sqlKeyValue) {
                    $sqlDataBind = array_merge($sqlDataBind, $sqlKeyValue);
                    $sqlDataStr[] = '(' . implode(',', array_keys($sqlKeyValue)) . ')';
                }
                $sql = $sqlField.implode(',',$sqlDataStr).$onDuplicate;
                $stmt = $db->getPdoInstance()->prepare($sql);
                $stmt->execute($sqlDataBind);
            }
        }

    public static function changeCompanyId($clientId, $type, $fromCompanyId, $toCompanyId)
    {
        $condition = 'client_id=:client_id and type=:type and company_id=:company_id';
        $params = [
            ':client_id' =>$clientId,
            ':type' =>$type,
            ':company_id' =>$fromCompanyId,
        ];

        $data = [
            'company_id' => $toCompanyId
        ];

        return self::model()->updateAll($data,$condition,$params);
    }

    public static function changeCustomerId($clientId, $type, $companyId, $fromCustomerId, $toCustomerId)
    {
        $condition = 'client_id=:client_id and type=:type and company_id=:company_id and customer_id=:customer_id';
        $params = [
            ':client_id' =>$clientId,
            ':type' =>$type,
            ':company_id' =>$companyId,
            ':customer_id' =>$fromCustomerId
        ];

        $data = [
            'customer_id' => $toCustomerId
        ];

        return self::model()->updateAll($data,$condition,$params);
    }

    //查询出来然后在新增或更新,因为保存下来组合产品的子产品关系不能再变更，所以通过eventType来做判断
    public static function batchCombineInsert($eventType,$clientId,array $list)
    {
        $subProduct = [];
        foreach ($list as $product) {
            if ($product['product_type'] == ProductConstant::PRODUCT_TYPE_COMBINE) {
                if ($eventType == BaseObject::EVENT_AFTER_INSERT) {
                    //新建场景，新增子产品record_list信息
                    $combineProductConfig = json_decode($product['combine_product_config'], true);
                    $subProductInvoice = self::generateSubProductInvoice($clientId, $combineProductConfig, $product);
                    $subProduct = array_merge($subProduct, $subProductInvoice);

                } else {
                    $subProductConds = ['combine_record_id' => $product['id']];
                    // 阿里产品匹配组合产品场景
                    if(!empty($product['update_combine_product_config'])){
                        $subProductConds['enable_flag'] = \Constants::ENABLE_FLAG_TRUE;
                    }else{
                        if(isset($product['old_sub_product_record_id'])){
                            $subProductConds['id'] = $product['old_sub_product_record_id'];
                        }
                    }

                    //更新场景，修改子产品record_list信息 combine_product_config 拿取以前的
                    //获取组合产品关系信息
                    $combineProductInfo = self::model()->findByAttributes([
                        'id' => $product['id'],
                        'enable_flag' => \Constants::ENABLE_FLAG_TRUE
                    ]);
                    //组合产品查询不到不插入子产品，避免下面语法报错
                    if (empty($combineProductInfo)) {
                        continue;
                    }
                    $combineProductInfo = $combineProductInfo->attributes;
                    //获取子产品信息
                    $subProductList = self::model()->findAllByAttributes($subProductConds);

                    //编辑时 新增一行组合产品 需要新增子产品
                    if (empty($subProductList)) {
                        $combineProductConfig = json_decode($product['combine_product_config'], true);
                        $subProductInvoice = self::generateSubProductInvoice($clientId, $combineProductConfig, $product);
                        $subProduct = array_merge($subProduct, $subProductInvoice);
                        continue;
                    }

                    $oldCombineProductConfig = json_decode($combineProductInfo['combine_product_config'], true);
                    $oldCombineProductConfig = array_column($oldCombineProductConfig,null,'sub_sku_id');
                    //报价单中，有历史数据为字符串，若是字符串则让该值为0；
                    $productCount = is_numeric($product['count']) ? $product['count'] : 0;

                    foreach ($subProductList as $subProductItem) {
                        $subProductItem = $subProductItem->attributes;
                        $subProduct[] = [
                            'count' => isset($oldCombineProductConfig[$subProductItem['sku_id']]) ? $productCount * $oldCombineProductConfig[$subProductItem['sku_id']]['count'] : 0,
                            'id' => $subProductItem['id'],
                            'product_id' => $subProductItem['product_id'] ?? 0,
                            'sku_id' => $subProductItem['sku_id'] ?? 0,
                            'client_id' => $subProductItem['client_id'],
                            'user_id' => $product['user_id'],
                            'status' => $product['status'], //状态更新
                            'type' => $subProductItem['type'],
                            'refer_id' => $subProductItem['refer_id'],
                            'combine_record_id' => $subProductItem['combine_record_id'],

                            //子产品信息组装 不能被刷新掉
                            'product_image' => $subProductItem['product_image'],
                            'product_images' => $subProductItem['product_images'],
                            'product_name' => mb_substr($subProductItem['product_name'], 0, 200),
                            'product_cn_name' => mb_substr($subProductItem['product_cn_name'], 0, 200),
                            'product_model' => $subProductItem['product_model'],
                            'product_remark' => $subProductItem['product_remark'],
                            'unit' => $subProductItem['unit'],
                            'unit_price' => $subProductItem['unit_price'],
                            'sku_attributes' => json_decode($subProductItem['sku_attributes'], true),
                            'sort' => $product['sort'] ?? 0,
                        ];
                    }
                }
            }
        }
        self::newBatchInsert($clientId, $subProduct);
    }

    /**
     * 生成子产品记录、保存子产品的信息
     * @param array $combineProductConfig 组合产品关系
     * @param array $product 交易产品当前行 order->product_list
     * @return array
     */
    public static function generateSubProductInvoice($clientId,array $combineProductConfig, $product)
    {
        $subProductIds = array_column($combineProductConfig, 'sub_product_id');
        $subProductMap = [];
        $subProductInvoice = [];

        //获取子产品信息
        if ($subProductIds) {
            $productObj = new ProductFilter($clientId);
            $productObj->product_id = $subProductIds;
            $productObj->enable_flag = 1;
            $subProductMap = $productObj->rawData();
            $subProductMap = array_column($subProductMap, null, 'product_id');
        }

        foreach ($combineProductConfig as $subProductItem) {
            $subProductId = $subProductItem['sub_product_id'] ?? 0;

            //报价单中，有历史数据为字符串，若是字符串则让该值为0；
            $productCount = is_numeric($product['count']) ? $product['count'] : 0;
            $userId       = !empty($product['user_id']) && is_array($product['user_id']) ? (string)current($product['user_id']) : '0';
            $productImages = '{}';
            if (!empty($subProductMap[$subProductId]['images'][0]['src'])) {
                $productImages = [[
                    'file_id'     => (string)($subProductMap[$subProductId]['images'][0]['id'] ?? 0),
                    'user_id'     => $userId,
                    'create_time' => xm_function_now(),
                ]];
            }

            $subProductInvoice[] = [
                'id' => \ProjectActiveRecord::produceAutoIncrementId(),
                'product_id' => $subProductId,
                'sku_id' => $subProductItem['sub_sku_id'] ?? 0,
                'client_id' => $product['client_id'],
                'user_id' => $product['user_id'],
                'status' => $product['status'],
                'type' => $product['type'],
                'refer_id' => !empty($product['refer_id']) ? $product['refer_id'] : $product['order_id'] ,
                'count' => $productCount * $subProductItem['count'],//子配件数量
                'combine_record_id' => $product['id'],

                //子产品信息组装  不会用组合产品输入的信息
                'product_image' => $subProductMap[$subProductId]['images'][0]['src'] ?? '', //获取第一张图片
                'product_images' => $productImages,
                'product_name' => $subProductMap[$subProductId]['name'] ?? '',
                'product_cn_name' => $subProductMap[$subProductId]['cn_name'] ?? '',
                'product_model' => $subProductMap[$subProductId]['model'] ?? '',
                'product_remark' => $subProductMap[$subProductId]['product_remark'] ?? '',
                'unit' => $subProductMap[$subProductId]['unit'] ?? '',
                'unit_price' => $subProductMap[$subProductId]['unit_price'] ?? '',
                'sku_attributes' => $subProductMap[$subProductId]['sku_attributes'] ?? '',
                'sort' => $product['sort'] ?? 0,
            ];
        }
//        echo \GuzzleHttp\json_encode($subProductInvoice);die;
        return $subProductInvoice;
    }
}
