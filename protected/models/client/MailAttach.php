<?php

use common\library\util\SqlBuilder;

/**
 * This is the model class for table "tbl_mail_attach".
 *
 * The followings are the available columns in table 'tbl_mail_attach':
 * @property string $attach_id
 * @property string $file_id
 * @property string $user_id
 * @property string $mail_id
 * @property string $create_time
 * @property string $update_time
 * @property integer $delete_flag
 * @property integer $inline_flag
 */
class MailAttach extends ProjectActiveRecord
{
    const TYPE_ATTACHMENT = 0;
    const TYPE_INLINE_IMAGE = 1;
    const MAX_FILE_LIMIT = 20;
    const MAX_FILE_SIZE_LIMIT = 50;         //50M 附件累计的大小
    public function autoIncrementColumn()
    {
        return 'attach_id';
    }

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'tbl_mail_attach';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(array('attach_id, file_id, mail_id, create_time, update_time', 'required'), array('delete_flag, inline_flag', 'numerical', 'integerOnly' => true), array('attach_id, file_id, mail_id', 'length', 'max' => 21), array('user_id', 'length', 'max' => 11), // The following rule is used by search().
            // @todo Please remove those attributes that should not be searched.
            array('attach_id, file_id, user_id, mail_id, create_time, update_time, delete_flag, inline_flag', 'safe', 'on' => 'search'),);
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array();
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array('attach_id' => 'Attach', 'file_id' => 'File', 'user_id' => 'User', 'mail_id' => 'Mail', 'create_time' => 'Create Time', 'update_time' => 'Update Time', 'delete_flag' => 'Delete Flag', 'inline_flag' => 'Inline Flag',);
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical usecase:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass data provider to CGridView, CListView or any similar widget.
     *
     * @return CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function search()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('attach_id', $this->attach_id, true);
        $criteria->compare('file_id', $this->file_id, true);
        $criteria->compare('user_id', $this->user_id, true);
        $criteria->compare('mail_id', $this->mail_id, true);
        $criteria->compare('create_time', $this->create_time, true);
        $criteria->compare('update_time', $this->update_time, true);
        $criteria->compare('delete_flag', $this->delete_flag);
        $criteria->compare('inline_flag', $this->inline_flag);

        return new CActiveDataProvider($this, array('criteria' => $criteria,));
    }

    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return MailAttach the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    public static function getFormatMailAttach(array $mailIds)
    {
        if (empty($mailIds))
        {
            return array();
        }

        $mailIdSql = implode(',', $mailIds);

        $db = Mail::model()->getDbConnection();

        $sql = "SELECT A.mail_id, A.inline_flag, B.* FROM tbl_mail_attach AS A LEFT JOIN tbl_upload_file AS B ON A.`file_id`=B.`file_id` WHERE A.mail_id in ($mailIdSql)";

        $list = $db->createCommand($sql)->queryAll(true);

        $result = array();

        $obj = new AliyunUpload();
        $obj->setHttps(true);
        foreach ($list as $elem)
        {
            $obj->loadByArray($elem);

            $item = array();
            $fileName = $obj->getFileName();
            $item['md5_file'] = $obj->getMd5File();
            $item['file_id'] = $obj->getFileId();
            $item['file_name'] = $obj->getFileName();
            $expireTime = 864000;
            $item['file_url'] = $elem['inline_flag'] ? $obj->getFileUrl() : $obj->generatePresignedUrl(true, null, $expireTime);
            if ($elem['inline_flag']) {
                $item['expired_time'] = 0;
            } else {
                $item['expired_time'] = $expireTime;
            }

            $item['file_size'] = $obj->getFileSize();

            if(DocumentpreviewUtil::isPreviewable($fileName, $obj->getFileSize())
                && DocumentpreviewUtil::getFileType($fileName) === 'office'
                && DocumentpreviewUtil::getExtName($fileName) !== '.pdf') {
                $item['preview_url'] = DocumentpreviewUtil::getPreviewUrl($obj->getFileKey(), $fileName, $obj->getBucket());
            }
            else {
                $item['preview_url'] = $obj->generatePresignedUrl(false);
//                $item['preview_url'] = $item['file_url'];
            }

            $item['file_status'] = 1;

            if (!isset($result[$elem['mail_id']]))
            {
                $result[$elem['mail_id']] = array();
            }

            if (!isset($result[$elem['mail_id']] [$elem['inline_flag']]))
            {
                $result[$elem['mail_id']] [$elem['inline_flag']] = array();
            }

            $result[$elem['mail_id']] [$elem['inline_flag']][] = $item;
        }
        if(!$result){
            return $result;
        }
        $return = [];
        //附件列表，根据入参排序
        foreach ($mailIds as $mailId){
            if(isset($result[$mailId]) && $result[$mailId]){
                $return[$mailId] = $result[$mailId];
            }
        }
        return $return;
    }


    /**
     * 批量插入邮件附件信息
     * @param array $file_ids
     * @param $user_id
     * @param $mail_id
     * @param $type
     * @param $client_id
     * @return int
     */
    public static function batchInsert(array $file_ids, $user_id,$mail_id,$type, $client_id)
    {

        if(empty($file_ids)) {
            return false;
        }

        $count = count($file_ids);
        $maxId = intval(ProjectActiveRecord::produceAutoIncrementId($count));
        $id = $maxId - $count;

        $create_time = date('Y-m-d H:i:s',time());

        //获取这一批file_id的md5_file和file_size
        $fileObjs = \UploadFile::findByIds($file_ids);
        $uploadFileInfoMap = array_column($fileObjs, null, 'file_id');

        foreach ($file_ids as $file_id){
            $id++;

            $md5File = '';
            $fileSize = 0;

            if (isset($uploadFileInfoMap[$file_id])) {
                $md5File = $uploadFileInfoMap[$file_id]->md5_file ?:'';
                $fileSize = $uploadFileInfoMap[$file_id]->file_size ?:0;
            }

            $insertValues[] = "($id,{$file_id},{$user_id},{$mail_id},'{$create_time}',0,{$type},{$client_id},'{$md5File}',{$fileSize})";
        }
        $sql = 'INSERT INTO tbl_mail_attach (attach_id,file_id, user_id, mail_id,create_time,delete_flag,inline_flag,client_id,md5_file,file_size) VALUES '.implode(',',$insertValues);

        return self::model()->getDbConnection()->createCommand($sql)->execute();

    }

    public static function batchDeleteByMailId($mail_id)
    {
        return self::model()->deleteAll('mail_id=:mail_id', [':mail_id' => $mail_id]);
    }

    public static function findByMailId($mail_ids)
    {
        if (empty($mail_ids)) {
            return array();
        }
        $mailIdSql = implode(',', $mail_ids);

        $sql = "SELECT * FROM tbl_mail_attach where mail_id in ($mailIdSql)";

        $list =self::model()->getDbConnection()->createCommand($sql)->queryAll(true);
        return $list;
    }

    /**
     * @param $mail_ids
     * @return int
     */
    public static function batchDelete($mail_ids)
    {
        if(empty($mail_ids)) {
            return 0;
        }
        $mail_id_string = implode(',',$mail_ids);
        $sql = "delete from tbl_mail_attach where mail_id in($mail_id_string)";
        return self::model()->getDbConnection()->createCommand($sql)->execute();
    }

    public static function batchSetDeleteFlagByMailId($mail_ids)
    {
        if(empty($mail_ids)) {
            return 0;
        }

        $mailIds = implode(',',$mail_ids);
        if (empty($mailIds)) {
            return 0;
        }

        //更新tbl_mail_attach表的delete_flag字段为1
        $sql = "update tbl_mail_attach set delete_flag=1 where mail_id in($mailIds)";
        return self::model()->getDbConnection()->createCommand($sql)->execute();
    }
}
