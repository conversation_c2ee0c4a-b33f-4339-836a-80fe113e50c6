<?php

/**
 * This is the model class for table "tbl_trade_module".
 *
 * The followings are the available columns in table 'tbl_trade_module':
 * @property string $module_id
 * @property string $module_key
 * @property string $categories
 * @property string $data
 * @property string $name 模块名称
 * @property string $remark 备注/推荐语
 * @property string $preview_image
 * @property integer $enable_flag
 * @property string $create_time
 * @property string $update_time
 * @property int $update_user
 * @property int $grey_enable_flag
 */
class TradeModule extends CActiveRecord
{
    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'tbl_trade_module';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('module_key, categories, data, name, remark, enable_flag', 'required'),

            array('module_key, name, remark, preview_image', 'length', 'max' => 255),
            // The following rule is used by search().
            // @todo Please remove those attributes that should not be searched.
            array('grey_enable_flag', 'numerical', 'integerOnly' => true),
            array('module_id, module_key, categories, data, remark, preview_image, enable_flag, create_time, update_time', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'module_id' => 'Module',
            'module_key' => 'Module Key',
            'categories' => 'Categories',
            'data' => 'Data',
            'name' => 'Name',
            'remark' => 'Remark',
            'preview_image' => 'Preview Image',
            'enable_flag' => 'Enable Flag',
            'create_time' => 'Create Time',
            'update_time' => 'Update Time',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical usecase:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass data provider to CGridView, CListView or any similar widget.
     *
     * @return CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function search()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria=new CDbCriteria;

        $criteria->compare('module_id',$this->module_id,true);
        $criteria->compare('module_key',$this->module_key,true);
        $criteria->compare('categories',$this->categories,true);
        $criteria->compare('data',$this->data,true);
        $criteria->compare('name',$this->name,true);
        $criteria->compare('remark',$this->remark,true);
        $criteria->compare('preview_image',$this->preview_image,true);
        $criteria->compare('enable_flag',$this->enable_flag);
        $criteria->compare('create_time',$this->create_time,true);
        $criteria->compare('update_time',$this->update_time,true);

        return new CActiveDataProvider($this, array(
            'criteria'=>$criteria,
        ));
    }

    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return TradeModule the static model class
     */
    public static function model($className=__CLASS__)
    {
        return parent::model($className);
    }
}