<?php

/*
 * @property string $id
 * @property string $user_id
 * @property string $refer_uid
 * @property integer $refer_type
 * @property string $refer_id
 * @property integer $open_flag
 * @property string $create_time
 * @property string $update_time
 *
 * <AUTHOR> <<EMAIL>> Create On Nov 4, 2015
 */

class UserSecuritySetting extends CActiveRecord {

    const REFER_TYPE_FACE = 3;
    const REFER_TYPE_VOICE = 4;

    /**
     * @return string the associated database table name
     */
    public function tableName() {
        return 'tbl_user_security_setting';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules() {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('user_id', 'required'),
            array('refer_type, open_flag', 'numerical', 'integerOnly' => true),
            array('user_id', 'length', 'max' => 10),
            array('refer_uid, refer_id', 'length', 'max' => 100),
            array('create_time', 'safe'),
            // The following rule is used by search().
            // @todo Please remove those attributes that should not be searched.
            array('id, user_id, refer_uid, refer_type, refer_id, open_flag, create_time, update_time', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels() {
        return array(
            'id' => 'ID',
            'user_id' => 'User',
            'refer_uid' => 'Refer Uid',
            'refer_type' => 'Refer Type',
            'refer_id' => 'Refer',
            'open_flag' => 'Open Flag',
            'create_time' => 'Create Time',
            'update_time' => 'Update Time',
        );
    }

    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return UserSecuritySetting the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    public static function getReferTypeList() {
        return [
            self::REFER_TYPE_FACE => '人脸',
            self::REFER_TYPE_VOICE => '声纹'
        ];
    }

    /**
     * 
     * @param type $type
     * @return type
     */
    public static function getReferTypeName($type) {
        $list = self::getReferTypeList();
        return isset($list[$type]) ? $list[$type] : '';
    }

    /**
     * 
     * @return array
     */
    public static function getEmptySettingList() {
        $types = array_values(array_flip(self::getReferTypeList()));
        $res = [];
        foreach ($types as $type) {
            $newModel = new self();
            $newModel->open_flag = 0;
            $newModel->refer_type = $type;

            $res[$type] = $newModel->getApiAttr();
        }
        return array_values($res);
    }

    /**
     * 
     * @param type $userId
     */
    public static function getSettingList($userId) {
        $types = array_values(array_flip(self::getReferTypeList()));
        $c = new CDbCriteria();
        $c->addColumnCondition([
            'user_id' => $userId
        ]);
        $c->addInCondition('refer_type', $types);

        $list = self::model()->findAll($c);

        $res = [];
        foreach ($list as $model) {
            $res[$model->refer_type] = $model->getApiAttr();
        }
        $now = date('Y-m-d H:i:s');
        foreach ($types as $type) {
            if (!isset($res[$type])) {
                $newModel = new self();
                $newModel->open_flag = 0;
                $newModel->refer_type = $type;
                $newModel->create_time = $now;
                $newModel->user_id = $userId;
                $newModel->save();
                $res[$type] = $newModel->getApiAttr();
            }
        }
        return array_values($res);
    }

    /**
     * 
     * @return type
     */
    public function getApiAttr() {
        return [
            'open_flag' => $this->open_flag,
            'refer_id' => is_null($this->refer_id) ? '' : $this->refer_id,
            'refer_type' => intval($this->refer_type)
        ];
    }

    /**
     * 
     * @param type $id
     * @return static
     */
    public static function findById($id) {
        return self::model()->findByPk($id);
    }

    /**
     * 
     * @param type $userId
     * @param type $referType
     * @return static
     */
    public static function findByType($userId, $referType) {
        $c = new CDbCriteria();
        $c->addColumnCondition([
            'user_id' => $userId,
            'refer_type' => $referType
        ]);
        $model = self::model()->find($c);
        if (!$model) {
            $model = new self();
            $model->user_id = $userId;
            $model->refer_type = $referType;
            $model->create_time = date('Y-m-d H:i:s');
            $model->save();
        }
        return $model;
    }

    /**
     * 
     * @param int $userId
     * @return boolean
     */
    public static function isOpen($userId) {
        $list = self::getSettingList($userId);
        $openCount = 0;
        foreach ($list as $k => $v) {
            $openCount += $v['open_flag'];
        }
        return $openCount > 0;
    }

    /**
     * 
     * @param type $userId
     */
    public static function close($userId) {
        $list = self::getSettingList($userId);
        foreach ($list as $v) {
            if (!$v['open_flag']) {
                continue;
            }
            $model = self::findByType($userId, $v['refer_type']);
            $model->open_flag = 0;
            $model->update(['open_flag']);
        }
    }

}
