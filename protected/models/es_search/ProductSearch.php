<?php
namespace common\models\es_search;

use common\components\ElasticSearchActiveRecordV7;
use common\library\search\ESProtocol;

class ProductSearch extends ElasticSearchActiveRecordV7
{
    public static function index()
    {
        //体验系统特殊处理
        if( \Yii::app()->params['env'] =='exp' )
            return 'product_'.\Yii::app()->params['exp_elastic_search_index_postfix'];

        if (\Yii::app()->params['env'] == 'unittest') {
            return 'product_debug';
        }

        if (\Yii::app()->params['env'] == 'test') {
            return 'product_test_0307';
        }

        return 'product';
    }

    /**
     * @param string $className
     * @return ProductSearch
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    public function mapping()
    {
        return [
            '_source' => [
                'enabled' => true
            ],
            '_routing' =>[
                'required' => true,
            ],
            'properties' => [
                'name' => [
                    'type' => 'text',
                    'analyzer' => 'matching_standard',
                    'fields'=>[
                        'keyword' =>[
                            'type' =>'keyword',
                            'normalizer' =>'keyword_normalizer',
                        ],
                        'number_split' => [
                            'type' => 'text',
                            'analyzer' => ESProtocol::NUMBER_MATH_ANALYZER,
                        ],
                        'char_split' => [    // 模糊搜索(名字改为char_split，别叫fuzzy)
                            'type' => 'text',
                            "analyzer"=> ESProtocol::CHAR_SPLIT_ANALYZER,
                        ],
                    ],
                ],
                'cn_name' => [
                    'type' => 'text',
                    'analyzer' => 'matching_standard',
                    'fields'=>[
                        'keyword' =>[
                            'type' =>'keyword',
                            'normalizer' =>'keyword_normalizer',
                        ],
                        'number_split' => [
                            'type' => 'text',
                            'analyzer' => ESProtocol::NUMBER_MATH_ANALYZER,
                        ],
                        'char_split' => [    // 模糊搜索
                            'type' => 'text',
                            "analyzer"=> ESProtocol::CHAR_SPLIT_ANALYZER,
                        ],
                    ],
                ],
                'product_no' => [
                    'type' => 'text',
                    'analyzer' => 'serial_id',
                    'fields'=>[
                        'keyword' =>[
                            'type' =>'keyword',
                            'normalizer' =>'keyword_normalizer',
                        ],
                        'number_split' => [
                            'type' => 'text',
                            'analyzer' => ESProtocol::NUMBER_MATH_ANALYZER,
                        ],
                        'char_split' => [    // 模糊搜索
                            'type' => 'text',
                            "analyzer"=> ESProtocol::CHAR_SPLIT_ANALYZER,
                        ],
                    ],
                ],
                'model' => [
                    'type' => 'text',
                    'analyzer' => 'matching_standard',
                    'fields'=>[
                        'keyword' =>[
                            'type' =>'keyword',
                            'normalizer' =>'keyword_normalizer',
                        ],
                        'number_split' => [
                            'type' => 'text',
                            'analyzer' => ESProtocol::NUMBER_MATH_ANALYZER,
                        ],
                        'char_split' => [    // 模糊搜索
                            'type' => 'text',
                            "analyzer"=> ESProtocol::CHAR_SPLIT_ANALYZER,
                        ],
                    ],
                ],
                'sku_code' => [
                    'type' => 'text',
                    'analyzer' => 'serial_id',
                    'fields'=>[
                        'keyword' =>[
                            'type' =>'keyword',
                            'normalizer' =>'keyword_normalizer',
                        ],
                        'number_split' => [
                            'type' => 'text',
                            'analyzer' => ESProtocol::NUMBER_MATH_ANALYZER,
                        ],
//                        'char_split' => [    // 模糊搜索
//                            'type' => 'text',
//                            "analyzer"=> ESProtocol::CHAR_SPLIT_ANALYZER,
//                        ],
                    ],
                ],
                'client_id' =>[
                    'type' => 'integer',
                ],
                'group_id' =>[
                    'type' => 'long',
                ],
                'create_time' =>[
                    'type' =>'date',
                    'ignore_malformed'=>true,
                    'format'=> 'yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis',
                ],
                'update_time' =>[
                    'type' =>'date',
                    'ignore_malformed'=>true,
                    'format'=> 'yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis',
                ],
                'external_field' => [
                    'type' => 'nested',
                    'properties' => [
                        'field' => [
                            'type' => 'long',
                        ],
                        'value' =>[
                            'type' => 'text',
                            'analyzer' => 'standard',
                            'fields' => [
                                'numeric' => [
                                    'ignore_malformed' => true,
                                    'type' => 'double',
                                ],
                                'keyword' => [
                                    'type' => 'keyword',
                                    'normalizer' => 'keyword_normalizer'
                                ],
                                'char_split' => [    // 模糊搜索
                                    'type' => 'text',
                                    "analyzer"=> ESProtocol::CHAR_SPLIT_ANALYZER,
                                ],
                                'datetime' => [
                                    'type' => 'date',
                                    'ignore_malformed' => true,
                                    'format'=> 'yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis',
                                ],
                            ]
                        ]
                    ]
                ],
            ],
        ];
    }

    public function setting()
    {
        return [
            'number_of_shards'   => 10,
            'number_of_replicas' => 1,
            'analysis'           => \CMap::mergeArray(
                [
                    'filter' => [
                        'serial_id_delimiter_filter' =>[
                            'type' =>'word_delimiter',
                            'preserve_original' =>true,
                            'catenate_numbers' =>true,
                        ],
                    ],
                    'normalizer' =>[
                        'keyword_normalizer' =>[
                            'type' => 'custom',
                            'char_filter' =>[],
                            'filter' =>[
                                'lowercase'
                            ]
                        ],
                    ],
                    'char_filter' => [
                        'to_mapping' => [
                            'type' => 'mapping',
                            "mappings" => ["_=>|"]
                        ]
                    ],
                    'analyzer' =>[
                        'serial_id' =>[
                            'type' => 'standard',
                            'tokenizer' =>'keyword',
                            'filter' => [
                                "serial_id_delimiter_filter",
                                "lowercase",
                                "trim"
                            ]
                        ],
                        'matching_standard' => [
                            'type' => 'custom',
                            'tokenizer' =>'standard',
                            'char_filter'=> ['to_mapping'],
                            'filter' => [
                                "lowercase",
                                "trim"
                            ]
                        ]
                    ],
            ], ESProtocol::productSearchMatchAnalyzer()),
        ];
    }
}
