<?php
/**
 * Created by PhpStorm.
 * User: troy.li
 * Date: 2018-03-21
 * Time: 4:51 PM
 */

namespace common\models\es_search;

/**
 * Class LeadSearch
 * @package common\models\es_search
 *
 * @property integer client_id
 * @property integer user_id
 * @property string  name
 * @property string  homepage
 * @property string  tel
 * @property string  customer
 */
class LeadSearch extends CompanySearch
{
    public static function index()
    {
        //体验系统特殊处理
        if( \Yii::app()->params['env'] =='exp' )
            return 'lead_'.\Yii::app()->params['exp_elastic_search_index_postfix'];

        return 'lead';
    }

    /**
     * @param string $className
     *
     * @return LeadSearch
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    public function mapping()
    {
        return [
            '_source' => [
                'enabled' => true
            ],
            '_routing' =>[
                'required' => true,
            ],
            'properties' => [
                'name' => [
                    'type' => 'text',
                    'analyzer' => 'standard'
                ],
                'company_name' => [
                    'type' => 'text',
                    'fields' => [
                        'keyword' => [
                            'type' => 'keyword'
                        ]
                    ],
                ],
                'serial_id' => [
                    'type' => 'text',
                    'analyzer' => 'serial_id',
                ],
                'homepage' => [
                    'type' => 'text',
                    'analyzer' => 'domain',
                    'fields' => [
                        'keyword' => [
                            'type' => 'keyword',
                            'normalizer' => 'keyword_normalizer'
                        ],
                    ],
                ],
                'tel' => [
                    'type' => 'text',
                    'analyzer' => 'tel',
                    'fields' => [
                        'tel_full' => [
                            'type' => 'text',
                            'analyzer' => 'tel_full'
                        ]
                    ],
                ],
                'fax' => [
                    'type' => 'text',
                    'analyzer' => 'tel'
                ],
                'client_id' =>[
                    'type' => 'integer',
                ],
                'user_id' =>[
                    'type' => 'integer',
                ],
                'private' =>[
                    'type' => 'boolean',
                ],
                'star' =>[
                    'type' =>'integer',
                ],
                'trail_status' =>[
                    'type' =>'long',
                ],
                'group_id' =>[
                    'type' =>'long',
                ],
                'country' =>[
                    'type' => 'text',
                ],
                'address' => [
                    'fields'=>[
                        'keyword' =>[
                            'type' =>'keyword',
                            'normalizer' =>'keyword_normalizer'
                        ],
                    ],
                    'type' => 'text',
                    'analyzer' => 'standard'
                ],
                'origin' =>[
                    'type' =>'long',
                ],
                'score' =>[
                    'type' =>'integer',
                ],
                'order_time' =>[
                    'type' =>'date',
                    'ignore_malformed'=>true,
                    'format'=> 'yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis',
                ],
                'external_field' =>[
                    'type' => 'nested',
                    'properties' =>[
                        'field' =>[
                            'type' =>'long',
                        ],
                        'value' =>[
                            'type' =>'text',
                            'analyzer' => 'standard',
                            'fields'=>[
                                'keyword' =>[
                                    'type' =>'keyword',
                                    'normalizer' =>'keyword_normalizer'
                                ],
                                'datetime' =>[
                                    'type' =>'date',
                                    'ignore_malformed'=>true,
                                    'format'=> 'yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis',
                                ]
                            ]
                        ]
                    ]
                ],
                'customer_list' =>[
                    'type' => 'nested',
                    'properties' =>[
                        'customer_id' =>[
                            'type' => 'long',
                        ],
                        'email' =>[
                            'type' => 'text',
                            'analyzer' => 'email',
                            'fields' => [
                                'keyword' => [
                                    'type' => 'keyword'
                                ],
                                'domain' =>[
                                    'type' =>'text',
                                    'analyzer' => 'email_domain',
                                ],
                            ],
                        ],
                        'name' => [
                            'type' => 'text',
                            'analyzer' => 'standard'
                        ],
                        'tel' =>[
                            'type' =>'text',
                            'analyzer' => 'tel',
                            'fields' => [
                                'tel_full' => [
                                    'type' => 'text',
                                    'analyzer' => 'tel_full'
                                ]
                            ],
                        ],
                        'contact' =>[
                            'type' => 'nested',
                            'properties' =>[
                                'type' =>[
                                    'type' => 'text',
                                    'fields' => [
                                        'keyword' => [
                                            'type' => 'keyword',
                                            'normalizer' => 'keyword_normalizer'
                                        ]
                                    ]
                                ],
                                'value' =>[
                                    'type' =>'text',
                                    'analyzer' => 'contact',
                                    'fields' => [
                                        'tel' => [
                                            'type' => 'text',
                                            'analyzer'  => 'tel',
                                        ],
                                        'keyword' =>[
                                            'type' =>'keyword',
                                            'normalizer' =>'keyword_normalizer'
                                        ],
                                    ]
                                ]
                            ]
                        ],
                        'external_field' =>[
                            'type' => 'nested',
                            'properties' =>[
                                'field' =>[
                                    'type' =>'long',
                                ],
                                'value' =>[
                                    'type' =>'text',
                                    'analyzer' => 'standard',
                                    'fields'=>[
                                        'keyword' =>[
                                            'type' =>'keyword',
                                            'normalizer' =>'keyword_normalizer'
                                        ],
                                        'datetime' =>[
                                            'type' =>'date',
                                            'ignore_malformed'=>true,
                                            'format'=> 'yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis',
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }

}