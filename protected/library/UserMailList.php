<?php
/**
 * Created by PhpStorm.
 * User: beenzhang
 * Date: 16/8/26
 * Time: 上午11:19
 */

use common\library\mail\public_mail_rule\PublicMailRuleList;
use common\library\mail\setting\signature\Signature;
use common\library\mail\setting\signature\Setting;

class UserMailList extends MysqlList
{
//    protected $userId;
//    protected $onlyBind = false;
//    protected $clientId;
//    protected $userMailId;
//    protected $emailAddress;
//    protected $validFlag;
//    protected $enableFlag = 1;
//    protected $keyword;
    protected static $mapUserInfo;
    protected static $mapDefaultUserMailInfo;

//    protected $showMailSignInfo;
//    protected $showMobileMailSignInfo;
//    protected $showMailSettingInfo;
//    protected $showUserInfo;
//    protected $showBindMailInfo; //显示绑定参数
//    protected $showDefaultMailFlag;  //显示默认邮箱标志
//    protected $showUnreadCount;
//    protected $showPassword=false;
//    protected $isKeywordMatchUser =true;
//    protected $showMailAliases = false; // 显示邮箱别名
//    protected $hideSubUserMails = false; // 隐藏公共邮箱 - 子邮箱
//    protected $showSubUserIds = false;
//    protected $showOnlySubUserMails = null; // 只显示公共子邮箱
//    protected $showSendServerValidFlag = false; // 判断是否可用邮箱（非网易腾讯邮箱）
//    protected $showExposeTimes = false; //判断是否展示邮箱群发单显次数

    protected $showEmailSize = false;

    protected $map = [];

    //拓展排序字段
    const SORT_FILED_USER_NICKNAME = 'nickname';
    /**
     * @var bool
     */
    protected $showProxy = false; //展示代理邮箱配置

    protected $cacheableRepo;

    public function __construct($clientId = null)
    {
        $this->cacheableRepo = new \common\library\email\UserMailCacheableRepo();
        $this->setClientId($clientId);
    }

    /**
     * @param mixed $userId
     * @throws \ProcessException
     */
    public function setUserId($userId)
    {
        if(empty($userId) ) {
            throw new \ProcessException('user_mail_list: 不能设置user_id为空');
        }
        $this->cacheableRepo->userId = $userId;
    }

    /**
     * @param mixed $clientId
     */
    public function setClientId($clientId)
    {
        $this->cacheableRepo->clientId = $clientId;
    }

    /**
     * @param mixed $userMailId
     */
    public function setUserMailId($userMailId)
    {
        $this->cacheableRepo->userMailId = $userMailId;
    }

    /**
     * @param mixed $emailAddress
     */
    public function setEmailAddress($emailAddress)
    {
        $this->cacheableRepo->emailAddress = $emailAddress;
    }

    /**
     * @param mixed $validFlag
     */
    public function setValidFlag($validFlag)
    {
        $this->cacheableRepo->validFlag = $validFlag;
    }

    /**
     * @param mixed $enableFlag
     */
    public function setEnableFlag($enableFlag)
    {
        $this->cacheableRepo->enableFlag = $enableFlag;
    }

    /**
     * @param mixed $keyword
     */
    public function setKeyword($keyword)
    {
        $this->cacheableRepo->keyword = $keyword;
    }

    public function setOnlyBind($flag)
    {
        $this->cacheableRepo->onlyBind = $flag;
    }

    public function setOnlyUnbind($flag)
    {
        $this->cacheableRepo->onlyUnbind = $flag;
    }

    /**
     * @param mixed $showMailSignInfo
     */
    public function setShowMailSignInfo($showMailSignInfo)
    {
        $this->cacheableRepo->showMailSignInfo = $showMailSignInfo;
    }

    /**
     * @param mixed $showMailSettingInfo
     */
    public function setShowMailSettingInfo($showMailSettingInfo)
    {
        $this->cacheableRepo->showMailSettingInfo = $showMailSettingInfo;
    }

    /**
     * @param mixed $showUserInfo
     */
    public function setShowUserInfo($showUserInfo)
    {
        $this->cacheableRepo->showUserInfo = $showUserInfo;
    }

    /**
     * @param mixed $showBindMailInfo
     */
    public function setShowBindMailInfo($showBindMailInfo)
    {
        $this->cacheableRepo->showBindMailInfo = $showBindMailInfo;
    }

    /**
     * @param mixed $showDefaultMailFlag
     */
    public function setShowDefaultMailFlag($showDefaultMailFlag)
    {
        $this->cacheableRepo->showDefaultMailFlag = $showDefaultMailFlag;
    }

    /**
     * @param mixed $showDefaultMailFlag
     */
    public function setShowSendServerValidFlag($showSendServerValidFlag)
    {
        $this->cacheableRepo->showSendServerValidFlag = $showSendServerValidFlag;
    }

    /**
     * @param mixed $mapUserInfo
     */
    public static function setMapUserInfo($mapUserInfo)
    {
        self::$mapUserInfo = $mapUserInfo;
    }

    /**
     * @param mixed $showMobileMailSignInfo
     */
    public function setShowMobileMailSignInfo($showMobileMailSignInfo)
    {
        $this->cacheableRepo->showMobileMailSignInfo = $showMobileMailSignInfo;
    }

    /**
     * @param mixed $showUnreadCount
     */
    public function setShowUnreadCount($showUnreadCount)
    {
        $this->cacheableRepo->showUnreadCount = $showUnreadCount;
    }

    /**
     * @param bool $showPassword
     */
    public function setShowPassword(bool $showPassword)
    {
        $this->cacheableRepo->showPassword = $showPassword;
    }

    /**
     * 设置keyword是否匹配email对应联系人之下所有的邮箱
     * @param
     * @return array
     */
    public function setIsKeywordMatchUser($flag)
    {
        $this->cacheableRepo->isKeywordMatchUser = $flag;
    }

    public function setShowMailAliases(bool $showMailAliases)
    {
        $this->cacheableRepo->showMailAliases = $showMailAliases;
    }

    public function setHideSubUserMails(bool $hideSubUserMails)
    {
        $this->cacheableRepo->hideSubUserMails = $hideSubUserMails;
    }

    public function setShowSubUserIds(bool $showSubUserIds)
    {
        $this->cacheableRepo->showSubUserIds = $showSubUserIds;
    }

    public function setShowOnlySubUserMails(bool $showOnlySubUserMails)
    {
        $this->cacheableRepo->showOnlySubUserMails = $showOnlySubUserMails;
    }

    public function setShowExposeTimes(bool $showExposeTimes){
        $this->cacheableRepo->showExposeTimes = $showExposeTimes;
    }

    public function setShowRemark(bool $showRemark){
        $this->cacheableRepo->showRemark = $showRemark;
    }

    /**
     * @param bool $showEmailSize
     */
    public function setShowEmailSize(bool $showEmailSize): void
    {
        $this->showEmailSize = $showEmailSize;
    }

    public function getUserInfo($userId)
    {

        if (!$userId) return [];

        if (!isset(self::$mapUserInfo[$userId]))
        {
            $userInfo = new \common\library\account\UserInfo($userId, $this->cacheableRepo->clientId);
            $result = [];

            if( !$userInfo->isNew() )
            {
                $userInfo->getFormatter();
                $result = $userInfo->getAttributes();
            }

            self::$mapUserInfo[$userId] = $result;
        }

        return self::$mapUserInfo[$userId];
    }

    public function getDefaultUserMailInfo($userId)
    {
        if (!isset(self::$mapDefaultUserMailInfo[$userId]))
        {
            $userMailId = 0;
            $aliasId = 0;
            $setting = new \common\library\mail\setting\general\MailSetting($userId);
            if (!$setting->isNew()) {
                $userMailId =  $setting->default_user_mail;
                $aliasId = $setting->default_alias_id;
            }
            self::$mapDefaultUserMailInfo[$userId] =
                [
                    'default_user_mail_id' => $userMailId,
                    'default_alias_id' => $aliasId
                ];
        }
        return self::$mapDefaultUserMailInfo[$userId];
    }

    public function getDefaultUserMailInfoList($clientId , array $userIds)
    {
        if (empty($clientId) || empty($userIds)) {
            return [];
        }
        $db = ProjectActiveRecord::getDbByClientId($clientId);
        $sql = "select * from tbl_mail_settings where client_id = :client_id and user_id in (".implode(',',$userIds).")";
        $list = $db->createCommand($sql)->queryAll(true,[':client_id' => $clientId]);
        $listData = array_column($list,null,'user_id');
        foreach ($userIds as $userId){
            self::$mapDefaultUserMailInfo[$userId] =
                [
                    'default_user_mail_id' => $listData[$userId]['default_user_mail'] ?? 0,
                    'default_alias_id' => $listData[$userId]['default_alias_id'] ?? 0
                ];
        }
        return self::$mapDefaultUserMailInfo;
    }

    public function getUserMailUnreadCount($userId,$userMailId)
    {
        $user = empty($userId) ? \User::getLoginUser() : \User::getUserObject($userId);
        $mailList = new \common\library\mail\MailList($user->getClientId(),$user->getUserId());
        $mailList->setUserMailId($userMailId);
        $mailList->unreadSettingByFolderIdIn();
        $count = $mailList->count();
        return intval($count);
    }

    protected function buildOrderBy()
    {
        $orderBy = '';

        if (!empty($this->orderBy)) {
            $field = implode(',', $this->orderBy);
            //拓展排序只支持一列排序&&字段名要一致
            if ($field == self::SORT_FILED_USER_NICKNAME) {
                $listObj = new \common\library\account\UserList();
                //查询所有(在快发||不在快发)
                $listObj->setEnableFlag(null);
                if ($this->cacheableRepo->clientId) {
                    $listObj->setClientId($this->cacheableRepo->clientId);
                } else {
                    throw new \RuntimeException(\Yii::t('common', 'ClientId is not null'));
                }

                if (is_array($this->cacheableRepo->userId)) {
                    $listObj->setUserIds($this->cacheableRepo->userId);
                }

                $listObj->setOrderBy($field);
                if($this->order){
                    $listObj->setOrder($this->order);
                }
                $result = $listObj->find();

                if (empty($result)) return '';

                $userIds = array_column($result, 'user_id');


                if($this->order == 'asc'){
                    array_unshift($userIds,0);
                }else{
                    array_push($userIds,0);
                }

                $andUserSql = '';
//                if(empty($this->$this->cacheableList->userId)){
//                    $andUserSql = ' and user_id in ('.implode(',',$userIds).')' ;
//                }

                $orderBy = $andUserSql.' order by field(user_id,' . implode(',', $userIds) . ')';

                //缓存
                if($this->cacheableRepo->showUserInfo){
                    $this->setMapUserInfo(array_combine(array_column($result, 'user_id'),$result));
                }


            } else {
                if ($this->order !== null) {
                    $orderBy = " order by {$field} {$this->order}";
                } else {
                    $orderBy = " order by {$field}";
                }
            }
        }


        return $orderBy;
    }


    public function buildParams()
    {
        if (!$this->cacheableRepo->clientId && $this->cacheableRepo->userId) {
            $this->setClientId(User::getUserObject($this->cacheableRepo->userId)->getClientId());
        }

        return $this->cacheableRepo->getSql();

//        $sql ='';
//        $params =[];
//
//        if ($this->cacheableRepo->clientId)
//        {
//            $sql .= ' and client_id=:client_id';
//            $params[':client_id'] = $this->cacheableRepo->clientId;
//        }
//
//        if ($this->cacheableRepo->userId)
//        {
//            if (is_array($this->cacheableRepo->userId)) {
//                $userId = array_filter($this->cacheableRepo->userId);
//                $sql .= ' and user_id in ('.implode(',',$userId).')';
//            } else {
//                $sql .= ' and user_id=:user_id';
//                $params[':user_id'] = $this->cacheableRepo->userId;
//            }
//        }
//
//        if ($this->cacheableRepo->onlyBind)
//        {
//            $sql .= ' and user_id!=0';
//        }
//
//        if ($this->cacheableRepo->userMailId)
//        {
//            if (is_array($this->cacheableRepo->userMailId)) {
//                $sql .= ' and user_mail_id in ('.implode(',',$this->cacheableRepo->userMailId).')';
//            } else {
//                $sql .= ' and user_mail_id=:user_mail_id';
//                $params[':user_mail_id'] = $this->cacheableRepo->userMailId;
//            }
//        }
//
//        if ($this->cacheableRepo->emailAddress)
//        {
//            if (is_array($this->cacheableRepo->emailAddress)) {
//                $emails = array_map(function ($eamil) {
//                    return \Util::escapeDoubleQuoteSql($eamil);
//                }, $this->cacheableRepo->emailAddress);
//                $emails = '\'' . implode("','", $emails) . '\'';
//                $sql .= " and email_address in ({$emails})";
//            } else {
//                $sql .= ' and email_address=:email_address';
//                $params[':email_address'] = $this->cacheableRepo->emailAddress;
//            }
//        }
//
//        if ($this->cacheableRepo->validFlag !==null)
//        {
//            $sql .= ' and valid_flag=:valid_flag';
//            $params[':valid_flag'] = $this->cacheableRepo->validFlag;
//        }
//
//        if ($this->cacheableRepo->enableFlag !==null)
//        {
//            $sql .= ' and enable_flag=:enable_flag';
//            $params[':enable_flag'] = $this->cacheableRepo->enableFlag;
//        }
//
//        if ($this->cacheableRepo->keyword)
//        {
//            $keyword = Util::escapeDoubleQuoteSql($this->cacheableRepo->keyword);
//            $result = [];
//            if ($this->cacheableRepo->isKeywordMatchUser)
//            {
//                $userListObj = new \common\library\account\UserList();
//                $userListObj->setClientId($this->cacheableRepo->clientId);
//                $userListObj->setEnableFlag(1);
//                $userListObj->setKeyWord($this->cacheableRepo->keyword);
//                $result = $userListObj->find();
//            }
//            if (!empty($result)) {
//                $sql .= " and (email_address like '%{$keyword}%' or user_id in (".implode(',',array_column($result,'user_id')).'))';
//            } else {
//                $sql .=" and email_address like '%{$keyword}%' ";
//            }
//        }
//
//        if ($this->cacheableRepo->hideSubUserMails) {
//            $sql .= ' and (source_user_mail_id = 0 or source_user_mail_id = user_mail_id)';
//        }
//
//        if ($this->cacheableRepo->showOnlySubUserMails) {
//            $sql .= ' and source_user_mail_id > 0 and source_user_mail_id != user_mail_id';
//        }
//
//        return [$sql, $params];
    }

    public function find()
    {

        list($sql, $params) = $this->buildParams();
        $limit = $this->buildLimit();
        $order = $this->buildOrderBy();
        $sql = "select * from tbl_user_mail where $sql $order $limit ";

        $data = $this->cacheableRepo->find($sql, $params, $order);

//        $data = UserMail::model()->getDbConnection()->createCommand($sql)->queryAll(true, $params);
        $this->map = $this->buildMapData($data);
        $list = [];
        foreach ($data as $item) {
            $list[] = $this->format($item);
        }

        return $list;
    }

    public function count()
    {
        list($sql, $params) = $this->buildParams();

        $count = $this->cacheableRepo->count("select count(1) from tbl_user_mail where {$sql}", $params);

        return intval($count);
    }

    protected function buildMapData($data){
        $userMailIds = array_column($data, 'user_mail_id');
        $userIds = array_column($data,'user_id');
        $allMainUserMailIds = array_filter(array_column($data, 'source_user_mail_id'));
        $mailAliasesMap = [];
        $subUserIdsMap = [];
        $sourceUserMailIdUserScopeMap = [];
        $defaultAliasId =0;

        $defaultUserMailInfoMap = [];
        if ($this->cacheableRepo->showMailAliases || $this->cacheableRepo->showDefaultMailFlag){
            $defaultUserMailInfoMap = $this->getDefaultUserMailInfoList($this->cacheableRepo->clientId,$userIds);
	        $defaultAliasId = $defaultUserMailInfoMap['default_alias_id'] ?? 0;
        }

        if ($this->cacheableRepo->showMailAliases)
        {

            $mailAliasesList = UserMailAlias::findByUserMailIds($userMailIds, $this->cacheableRepo->clientId);
            foreach ($mailAliasesList as $mailAliasInfo)
            {
                $userMailId = $mailAliasInfo['user_mail_id'];
                $aliasAddress = $mailAliasInfo['alias_address'];
                $aliasId = $mailAliasInfo['alias_id'];
                if (!isset($mailAliasesMap[$userMailId])) $mailAliasesMap[$userMailId] = [];

                $mailAliasesMap[$userMailId][] = [
                    'alias_id' => $aliasId,
                    'alias_address' => $aliasAddress,
                    'default_mail_flag' => ($aliasId == $defaultAliasId)
                ];
            }
        }

        if ($this->cacheableRepo->showSubUserIds)
        {
            $allSubUserMails = UserMail::findAllSubUserMailsBySourceUserMailId($this->cacheableRepo->clientId, $userMailIds);
            foreach ($allSubUserMails as $item) {
                /**
                 * @var UserMail $item
                 */
                if ($item->user_id == 0 ) {
                    continue;
                }
                $subUserIdsMap[$item->source_user_mail_id][] = $item->user_id;
            }


            //获取所有的source_user_mail_id
            $allSourceUserIds = array_keys($subUserIdsMap);

            if (!empty($allSourceUserIds)) {
                $publicMailRuleList = new PublicMailRuleList($this->cacheableRepo->clientId);
                $publicMailRuleList->setSourceUserMailId($allSourceUserIds);
                $ruleList = $publicMailRuleList->find();
                foreach ($ruleList as $rule) {
                    $sourceUserMailIdUserScopeMap[$rule['source_user_mail_id']][$rule['receive_scope']][] = $rule['user_id'];
                }
            }


        }
	    $allUserMailExposeTimesMap = [];
	    $allUserMailExposeTotalMap = [];

        if ($this->cacheableRepo->showExposeTimes)
        {
            $allUserMailExposeTimes = UserMailService::getUserMailExposeTimes($userMailIds);
            $allUserMailExposeTimesMap = array_column($allUserMailExposeTimes,'exposeTimes','user_mail_id' );
            $allUserMailExposeTotalMap = array_column($allUserMailExposeTimes,'total','user_mail_id' );
        }
        $mainUserMailInfoMap = [];
        if (!empty($allMainUserMailIds)) {
            $allMainUserMails = \common\library\email\UserMailCacheableRepo::instance($this->cacheableRepo->clientId)->findByIds($allMainUserMailIds);
//            $allMainUserMails = UserMail::findAllByCondition($this->cacheableList->clientId, "user_mail_id in (" . implode(',', $allMainUserMailIds) . ")", []);
            foreach ($allMainUserMails as $userMailItem) {
                $mainUserMailInfoMap[$userMailItem['user_mail_id']] = $userMailItem;
            }
        }


        $proxyMap = [];
        if ($this->showProxy && !empty($userMailIds)) {
//            $allUserProxys = UserMailOverseasProxy::findAllByCondition(" user_mail_id in (" . implode(',', $userMailIds) . ")", []);
            $allUserProxies = \common\library\email\UserMailOverseasProxyCacheableRepo::instance($this->cacheableRepo->clientId)->findAllByConditions([
                'user_mail_id' => $userMailIds
            ]);
            foreach ($allUserProxies as $userProxy) {
                $proxyMap[$userProxy['user_mail_id']] = [
                    'overseasProxyType' =>$userProxy['proxy_type'],
                    'overseasProxyHost' =>$userProxy['host'],
                    'overseasProxyPort' => $userProxy['port'],
                    'overseasProxyUser' => $userProxy['user'],
                    'overseasProxyPassword' => $userProxy['password'],
                ];
            }
        }

        $userMailRemarkMap = [];
        if ($this->cacheableRepo->showRemark) {
            $user = User::getLoginUser();
            $clientId = $user->getClientId();
            $userId = $this->cacheableRepo->userId;
            $key = \common\library\setting\user\UserSetting::MAIL_USER_MAIL_REMARK;
            $setting = new \common\library\setting\user\UserSetting($clientId, $userId, $key);

            $settingValue = $setting->getValue();
            $userMailRemarkMap = $settingValue;
        }

        $emailSizeMap = [];
        if ($this->showEmailSize)
        {
            $userMailExternalInfoList = UserMailExternalInfo::getUserMailExternalInfoList($userMailIds);
            $emailSizeMap = array_column($userMailExternalInfoList, 'email_size', 'user_mail_id');
        }

        return [
            'mail_aliases_map' => $mailAliasesMap,
            'sub_user_ids_map' => $subUserIdsMap,
            'source_user_mailId_user_scope_map' => $sourceUserMailIdUserScopeMap,
            'main_user_mail_info_map' => $mainUserMailInfoMap,
            'default_user_mail_info_map' => $defaultUserMailInfoMap,
            'all_user_mail_expose_times_map' => $allUserMailExposeTimesMap,
            'all_user_mail_expose_total_map' => $allUserMailExposeTotalMap,
            'proxy_map' => $proxyMap,
            'user_mail_remark_map' => $userMailRemarkMap,
            'email_size_map' => $emailSizeMap
        ];
    }

    protected function format($userMail)
    {
        $data = [
            'user_mail_id' => $userMail['user_mail_id'],
            'user_id' => $userMail['user_id'],
            'client_id' => $userMail['client_id'],
            'email_address' => $userMail['email_address'],
            'valid_flag' => $userMail['valid_flag'],
            'attach_max_size' => $userMail['attach_max_size'],
            'attach_total_max_size' => $userMail['attach_total_max_size'],
            'mail_max_size' => $userMail['mail_max_size'],
            'enable_flag' => $userMail['enable_flag'],
            'create_time' => $userMail['create_time'],
            'source_user_mail_id' => $userMail['source_user_mail_id'],
            'old_user_id' => $userMail['old_user_id'],
            'order_rank' => $userMail['order_rank'] ?? 0,
            'alimail_flag' => $userMail['alimail_flag'] ?? 0,
            'mail_type' => $userMail['mail_type'] ?? 'mail_type_other',
        ];

        // 公共邮箱 - 子邮箱 的valid_flag 取主邮箱的valid_flag值
        if ($data['source_user_mail_id'] && $data['source_user_mail_id'] != $data['user_mail_id']) {
            $sourceUserMail = $this->map['main_user_mail_info_map'][$data['source_user_mail_id']] ?? null;
            $data['valid_flag'] = $sourceUserMail ? $sourceUserMail['valid_flag'] : 1;
        }

        if ($this->cacheableRepo->showBindMailInfo)
        {
            $data['siteUrl'] ='';
            $data['smtpPort'] = $userMail['send_port'];
            $data['smtpSSL'] = intval($userMail['send_ssl']);
            $data['keywordList'] = '';
            $data['smtpHost'] = $userMail['send_server'];
            $data['exchange_domain'] = $userMail['exchange_domain'];
            $data['receiveHost'] = $userMail['receive_server'];
            $data['receiveSSL'] = intval($userMail['receive_ssl']);
            $data['receivePort'] = $userMail['receive_port'];
            $data['receiveProtocol'] = \UserMail::RECEIVE_PROTOCOL_LIST[$userMail['email_server_type']];
            $data['receive_server_user_name'] = $userMail['receive_server_user_name'];
            $data['smtp_user_name'] = $userMail['smtp_user_name'];
            if ($userMail['multi_user_name_flag']) {
                $data['receive_server_pwd_enc'] = $this->cacheableRepo->showPassword ? SecurityUtil::tripleDESDecrypt($userMail['receive_server_pwd_enc'], $userMail['email_pwd_salt']) :'';
                $data['smtp_pwd_enc'] =   $this->cacheableRepo->showPassword? SecurityUtil::tripleDESDecrypt($userMail['smtp_pwd_enc'], $userMail['email_pwd_salt']) : '';
            } else {
                $data['receive_server_pwd_enc'] = '';
                $data['smtp_pwd_enc'] = '';
            }

            $data['multi_user_name_flag'] = $userMail['multi_user_name_flag'];
            $data['can_editaccount'] = 'no';
            $data['email_server_type'] = $userMail['email_server_type'];
            $data['receive_server'] = $userMail['receive_server'];
            $data['send_server'] = $userMail['send_server'];
            $data['send_port'] = $userMail['send_port'];
            $data['send_ssl'] = $userMail['send_ssl'];
            $data['proxyFlag'] = $userMail['proxy_flag'];
            $data['bisync_flag'] = $userMail['bisync_flag'];
            $data['attach_max_size'] = $userMail['attach_max_size'];
            $data['attach_total_max_size'] = $userMail['attach_total_max_size'];
            $data['mail_max_size'] = $userMail['mail_max_size'];
        }

        if ($this->cacheableRepo->showUserInfo)
        {
            $data['user'] = $this->getUserInfo($userMail['user_id']);
        }

        if ($this->cacheableRepo->showMailSettingInfo)
        {
            $setting = new \common\library\mail\setting\general\UserMailSetting($userMail['user_id'], $userMail['user_mail_id']);
            $userMailSetting = $setting->getAttributes();

            $attributeDefaultMailTemplate = $userMailSetting['default_mail_template'] ?? '';

            if ($attributeDefaultMailTemplate != '') {
                $_tmp = json_decode($attributeDefaultMailTemplate,true);
                if ($_tmp != null) {
                    $userMailSetting['default_mail_template'] = $_tmp;
                } else {
                    $userMailSetting['default_mail_template'] = [
                        'scene_normal' => 0,
                        'scene_reply' => 0,
                        'scene_forward' => 0,
                    ];
                }
            }

            $data['user_mail_setting'] = $userMailSetting;
        }

        if ($this->cacheableRepo->showMailSignInfo || $this->cacheableRepo->showMobileMailSignInfo)
        {
            $setting = new Setting($userMail['user_mail_id'], $userMail['user_id']);

            $userMailSignInfo = [
                'user_id' => $userMail['user_id'],
                'client_id' => $userMail['client_id'],
                'user_mail_id' => $userMail['user_mail_id'],
                'default_sign_id' => $setting->default_sign_id,
                'default_sign_info' => null,
                'reply_sign_id' => $setting->reply_sign_id,
                'reply_sign_info' => null,
            ];

            if ($setting->default_sign_id && !($signature = new Signature($userMail['user_id'], $setting->default_sign_id))->isNew())
                $userMailSignInfo['default_sign_info'] = [
                    'sign_id'=> $signature->sign_id,
                    'sign_name'=> $signature->sign_name,
                    'sign_content'=> $signature->sign_content,
                ];

            if ($setting->reply_sign_id && !($signature = new Signature($userMail['user_id'], $setting->reply_sign_id))->isNew())
                $userMailSignInfo['reply_sign_info'] = [
                    'sign_id'=> $signature->sign_id,
                    'sign_name'=> $signature->sign_name,
                    'sign_content'=> $signature->sign_content,
                ];

            $data['user_mail_sign'] = $userMailSignInfo;
        }

        if ($this->cacheableRepo->showDefaultMailFlag) {
            $defaultUserMailInfo = $this->getDefaultUserMailInfo($userMail['user_id']);
            $defaultUserMailId = $defaultUserMailInfo['default_user_mail_id'] ?? 0;
            $data['default_mail_flag'] = ($userMail['user_mail_id'] == $defaultUserMailId);
        }

        if ($this->cacheableRepo->showUnreadCount) {
            $data['unread_count'] = $this->getUserMailUnreadCount($data['user_id'],$data['user_mail_id']);
        }

        if ($this->cacheableRepo->showPassword) {
            $data['email_pwd_enc'] = SecurityUtil::tripleDESDecrypt($userMail['email_pwd_enc'], $userMail['email_pwd_salt']);
        }

        if ($this->cacheableRepo->showMailAliases) {
            $defaultUserMailInfoMap = $this->map['default_user_mail_info_map'];
            foreach ($this->map['mail_aliases_map'] as &$mails){
                foreach ($mails as &$mail){
                    $defaultAliasId = $defaultUserMailInfoMap[$data['user_id']]['default_alias_id'] ?? 0;
                    $mail['default_mail_flag'] = ($mail['alias_id'] == $defaultAliasId);
                }
            }
            $data['mail_aliases'] = $this->map['mail_aliases_map'][$data['user_mail_id']] ?? [];

            // 给别名邮箱加显示名称
            if ($this->cacheableRepo->showRemark) {
                $newAliases = [];
                foreach ($data['mail_aliases'] as $alias) {
                    $userMailRemarkMap = $this->map['user_mail_remark_map'];
                    if (!empty($userMailRemarkMap)) {
                        $email_address = $alias['alias_address'];
                        if (key_exists($email_address, $userMailRemarkMap)) {
                            $alias['display_name'] = $userMailRemarkMap[$email_address];
                        } else {
                            $alias['display_name'] = '';
                        }
                    } else {
                        $alias['display_name'] = '';
                    }

                    $newAliases[] = $alias;
                }
                $data['mail_aliases'] = $newAliases;
            }
        }

        if ($this->cacheableRepo->showSubUserIds) {
            $data['sub_user_ids'] = $this->map['sub_user_ids_map'][$data['user_mail_id']] ?? [];

            //去掉主归属人user_id
            $excludeMainSubUserIds = array_diff($data['sub_user_ids'],[$data['user_id']]);
            if (!empty($excludeMainSubUserIds)) {

                $allScopeUserIds = $this->map['source_user_mailId_user_scope_map'][$data['user_mail_id']][PublicMailRule::PUBLIC_MAIL_RECEIVE_MAIL_RULE_ALL]??[];
                $companyScopeUserIds = $this->map['source_user_mailId_user_scope_map'][$data['user_mail_id']][PublicMailRule::PUBLIC_MAIL_RECEIVE_MAIL_RULE_COMPANY]??[];

                //两个配置都没有，走默认配置，全部可见
                if (empty($allScopeUserIds) && empty($companyScopeUserIds)) {
                    $data['user_scopes'] = [
                        [
                            'scope' => PublicMailRule::PUBLIC_MAIL_RECEIVE_MAIL_RULE_ALL,
                            'sub_user_ids' => array_values($excludeMainSubUserIds)
                        ],
                    ];
                } else {

                    if (!empty($allScopeUserIds)) {
                        $data['user_scopes'][] = [
                            'scope' => PublicMailRule::PUBLIC_MAIL_RECEIVE_MAIL_RULE_ALL,
                            'sub_user_ids' => $allScopeUserIds
                        ];
                    }

                    if (!empty($companyScopeUserIds)) {
                        $data['user_scopes'][] = [
                            'scope' => PublicMailRule::PUBLIC_MAIL_RECEIVE_MAIL_RULE_COMPANY,
                            'sub_user_ids' => $companyScopeUserIds
                        ];
                    }
                }
            }
        }

        if ($this->cacheableRepo->showSendServerValidFlag){
            $email = new \common\library\email\Email($data['user_mail_id'],$data['user_id'],$this->cacheableRepo->clientId);
            $data['send_server_valid_flag'] = !(($email->checkIsTecentSendServer() || $email->checkIsNetEaseSendServer()));
        }
        if ($this->cacheableRepo->showExposeTimes) {
            $data['exposeTimes'] = $this->map['all_user_mail_expose_times_map'][$data['user_mail_id']] ?? \common\library\mail\service\ExposeService::$client_limit_sender_day_num;
            $data['expose_total'] = $this->map['all_user_mail_expose_total_map'][$data['user_mail_id']] ?? \common\library\mail\service\ExposeService::$client_limit_sender_day_num;
        }

        if ($this->cacheableRepo->showRemark) {
            $userMailRemarkMap = $this->map['user_mail_remark_map'];
            if (!empty($userMailRemarkMap)) {
                $email_address = $userMail['email_address'];
                if (key_exists($email_address, $userMailRemarkMap)) {
                    $data['display_name'] = $userMailRemarkMap[$email_address];
                } else {
                    $data['display_name'] = '';
                }
            } else {
                $data['display_name'] = '';
            }
        }

        if ($this->showProxy) {
            $userProxy = $this->map['proxy_map'][$data['user_mail_id']??0]??[];
            if (!empty($userProxy)) {
                $data = array_merge($data,$userProxy);
            }
        }

        $data['is_oauth'] = 0;
        if (isset($userMail['send_server']) && \common\library\mail\Helper::checkOutlookMailBySendServer($userMail['send_server'])) {
            $data['oauth_flag'] = $userMail['oauth_flag'] ?? 0;
            $data['is_oauth'] = 1;
        }

        if ($this->showEmailSize)
        {
            $data['email_size'] = intval($this->map['email_size_map'][$data['user_mail_id']] ?? 0);
        }

        return $data;
    }

    public function setShowProxy(bool $showProxy)
    {
        $this->showProxy = $showProxy;
    }
}
