<?php

namespace common\library\oms\command;

use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldExportService;
use common\library\object\field\FieldConstant;
use common\library\util\sort\Sorter;
use Constants;

trait QuotationFieldSetting
{
    public function quotationFieldSetting()
    {

        $fieldTemplate = [[
            'id' => 'product_cn_name',
            'base' => 1,
            'name' => '中文产品名称',
            'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            'require' => 0,
            'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
            'default' => '',
            'edit_default' => 0,
            'hint' => '',
            'is_list' => 1,
            'columns' => 'product_cn_name',
            'relation_type' => 0,
            'relation_field' => '',
            'relation_field_type' => 0,
            'relation_field_name' => '',
            'readonly' => 0,
            'ext_info' => '',
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s'),
            'after' => 'product_name',
            'type' => Constants::TYPE_QUOTATION,
            'is_editable' => 1,
            'edit_required' => 1,
            'edit_hint' => 1,
            'edit_hide' => 0,
            'group_id' => CustomFieldService::QUOTATION_GROUP_PRODUCT,
            'is_exportable' => 1,
            'export_scenario' => Constants::TYPE_QUOTATION,
            'export_group' => FieldExportService::QUOTATION_GROUP_EXCHANGE_PRODUCT,
        ]];


        return $fieldTemplate;
    }

    public  function customFieldSetting()
    {
        $settings = [
            [
                'id' => 'vat_rate',
                'name' => '增值税率',
                'type' => Constants::TYPE_QUOTATION,
                'require' => 0,
                'base' => 1,
                'hint' => '',
                'edit_hint' => 1,//是否可编辑字段提示：0-否，1-是
                'group_id' => CustomFieldService::QUOTATION_GROUP_PRODUCT,
                'edit_required' => 1,
                'is_exportable' => 1,
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'edit_default' => 1,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'export_scenario' => \Constants::TYPE_QUOTATION,
                'export_group' => FieldExportService::QUOTATION_GROUP_EXCHANGE_PRODUCT,
                'is_list' => 1,
                'default' => '',
                'columns' => 'vat_rate',
                'readonly' => 0,
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'order' => 0,
            ],
            [
                'id' => 'tax_refund_rate',
                'name' => '退税率',
                'type' => Constants::TYPE_QUOTATION,
                'require' => 0,
                'base' => 1,
                'hint' => '',
                'edit_hint' => 1,//是否可编辑字段提示：0-否，1-是
                'group_id' => CustomFieldService::QUOTATION_GROUP_PRODUCT,
                'edit_required' => 1,
                'is_exportable' => 1,
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'edit_default' => 1,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'export_scenario' => \Constants::TYPE_QUOTATION,
                'export_group' => FieldExportService::QUOTATION_GROUP_EXCHANGE_PRODUCT,
                'is_list' => 1,
                'default' => '',
                'columns' => 'tax_refund_rate',
                'readonly' => 0,
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'order' => 0,
            ],
        ];
        return $settings;
    }

    public function orderCustomFieldConfig()
    {
        // 订单字段
        $orderSettings = [
            [
                'id' => 'refer_quotation_no',
                'name' => '关联报价单编号',
                'require' => 0,
                'hint' => '',
                'edit_hint' => 0, //是否可编辑字段提示：0-否，1-是
                'group_id' => CustomFieldService::ORDER_GROUP_BASIC,
                'extra_info' => '[]',
                'edit_required' => 0,
                'is_exportable' => 1,
                'disable_flag' => 0, //是否列表展示，0展示，1不展示
                'edit_hide' => 1, //是否可编辑启用，1可以，0否
                'edit_default' => 0, //是否可编辑默认值，1可以，0否
                'is_editable' => 1, //是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                'is_list' => 0,
                'base' => 1,
                'default' => '',
                'columns' => '',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => \Constants::TYPE_ORDER,
                'export_group' => FieldExportService::ORDER_GROUP_BASIC,
                'order' => Sorter::MIN_ORDER,
                'app_order' => Sorter::MIN_ORDER,
                'readonly' => 0,
                'type' => \Constants::TYPE_ORDER,
            ],
        ];

        foreach ($orderSettings as &$setting) {
            $setting['type'] = \Constants::TYPE_ORDER;
            $setting['base'] = 1;
            $setting['default'] = $setting['default'] ?? '';
//            $setting['columns'] = $setting['columns'] ?? $setting['id'];
            $setting['relation_type'] = 0;
            $setting['relation_field'] = '';
            $setting['relation_field_type'] = 0;
            $setting['relation_field_name'] = '';
            $setting['readonly'] = $setting['readonly'] ?? 0;
            $setting['after'] = $setting['after'] ?? '';
        }

        return $orderSettings;
    }
    public function fieldNumericExtInfo(){
        $quotationFieldNumericExtInfo = [
            'amount' => [
                'decimal' => 4,         //小数位数
            ],
            'amount_rmb' => [
                'decimal' => 4,
            ],
            'amount_usd' => [
                'decimal' => 4,
            ],
            'product_total_amount' => [
                'decimal' => 4,
            ],
            'addition_cost_amount' => [
                'decimal' => 4,
            ],
            'parts_total_count' => [
                'decimal' => 4,
            ],
            'product_total_count' => [
                'decimal' => 4,
            ],
            'product_total_count_no_parts' => [
                'decimal' => 4,
            ],
            'package_gross_weight_amount' => [
                'decimal' => 4,
            ],
            'package_volume_amount' => [
                'decimal' => 4,
            ],

            'product_total_amount_rmb' => [
                'decimal' => 4,
            ],
            'product_total_amount_usd' => [
                'decimal' => 4,
            ],
            'addition_cost_amount_rmb' => [
                'decimal' => 4,
            ],
            'addition_cost_amount_usd' => [
                'decimal' => 4,
            ],
            'count' => [
                'decimal' => 4,
            ],
            'unit_price' => [
                'decimal' => 4,
            ],

            'exchange_rate' => [
                'allow_setting_decimal' => FieldConstant::NOT_ALLOW_SETTING_DECIMAL,   //系统字段添加允许设置小数位
                'decimal' => 5,
            ],
            'exchange_rate_usd' => [
                'allow_setting_decimal' => FieldConstant::NOT_ALLOW_SETTING_DECIMAL,   //系统字段添加允许设置小数位
                'decimal' => 5,
            ],
            'cost_amount' => [
                'decimal' => 4,
            ],
            'other_cost' => [
                'decimal' => 4,
            ],
            'cost_with_tax' => [
                'decimal' => 4,
            ],
            'package_volume' => [
                'decimal' => 4,
            ],
            'count_per_package' => [
                'decimal' => 4,
            ],
            'gross_profit_margin' => [
                'decimal' => 4,
            ],
            'package_gross_weight' => [
                'decimal' => 4,
            ],
            'package_volume_subtotal' => [
                'decimal' => 4,
            ],
            'package_gross_weight_subtotal' => [
                'decimal' => 4,
            ],
            'standard_price' => [
                'decimal' => 4,
            ],
            'minimum_order_quantity'=>[
                'decimal' => 6,
            ],
            'vat_rate' => [
                'decimal' => 4,
                'allow_setting_decimal' => FieldConstant::NOT_ALLOW_SETTING_DECIMAL,   //系统字段添加允许设置小数位
            ],
            'package_count'=>[
                'decimal' => 0,
            ],
            'tax_refund_rate' => [
                'decimal' => 4,
                'allow_setting_decimal' => FieldConstant::NOT_ALLOW_SETTING_DECIMAL,   //系统字段添加允许设置小数位
            ],
        ];

        $extra = [
            'allow_setting_decimal' => FieldConstant::ALLOW_SETTING_DECIMAL,   //系统字段添加允许设置小数位
            'decimal_logic' => FieldConstant::DECIMAL_LOGIC_ROUND,     //保留小数位逻辑,round四舍五入， ceil向上截取，floor向下截取
        ];

        return [$quotationFieldNumericExtInfo,$extra];
    }


    public function InquiryFieldSetting()
    {
        // 报价单字段
        $quotationSettings = [
            [
                'id' => 'refer_inquiry_collaboration_no',
                'name' => '关联询价单编号',
                'require' => 0,
                'hint' => '',
                'edit_hint' => 0, //是否可编辑字段提示：0-否，1-是
                'group_id' => CustomFieldService::ORDER_GROUP_BASIC,
                'extra_info' => '[]',
                'edit_required' => 0,
                'is_exportable' => 1,
                'disable_flag' => 0, //是否列表展示，0展示，1不展示
                'edit_hide' => 1, //是否可编辑启用，1可以，0否
                'edit_default' => 0, //是否可编辑默认值，1可以，0否
                'is_editable' => 0, //是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                'is_list' => 0,
                'base' => 1,
                'default' => '',
                'columns' => '',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => \Constants::TYPE_QUOTATION,
                'export_group' => FieldExportService::QUOTATION_GROUP_BASIC,
                'order' => Sorter::MIN_ORDER,
                'app_order' => Sorter::MIN_ORDER,
                'readonly' => 1,
                'type' => \Constants::TYPE_ORDER,
            ],
            [
                "id" => "purchase_quote_cny",
                "group_id" => 2,
                "base" => 1,
                "name" => "采购报价CNY",
                "field_type" => 5,
                "require" => 0,
                "edit_required" => 1,
                "disable_flag" => 0,
                "edit_hide" => 1,
                "default" => "",
                "edit_default" => 1,
                "hint" => "",
                "edit_hint" => 1,
                "is_exportable" => 1,
                "is_editable" => 1,
                "is_list" => 1,
                "columns" => "purchase_quote_cny",
                "readonly" => 0,
                'export_scenario' => \Constants::TYPE_QUOTATION,
                "export_group" => FieldExportService::QUOTATION_GROUP_EXCHANGE_PRODUCT,
                "relation_type" => 0,
                "relation_field" => "",
                "relation_field_type" => 0,
                "relation_field_name" => "",
                "ext_info" => json_encode(
                    [
                        "decimal" => 4,
                        "allow_setting_decimal" => 1,
                        "decimal_logic" => "round",
                        "is_fill_zero" => 0
                    ]),
            ],
            [
                "id" => "purchase_quote",
                "group_id" => 2,
                "base" => 1,
                "name" => "采购报价",
                "field_type" => 5,
                "require" => 0,
                "edit_required" => 1,
                "disable_flag" => 0,
                "edit_hide" => 1,
                "default" => json_encode(["type" => "3", "value" => ""]),
                "edit_default" => 1,
                "hint" => "",
                "edit_hint" => 1,
                "is_exportable" => 1,
                "is_editable" => 1,
                "is_list" => 1,
                "columns" => "purchase_quote",
                "readonly" => 0,
                'export_scenario' => \Constants::TYPE_QUOTATION,
                "export_group" => FieldExportService::QUOTATION_GROUP_EXCHANGE_PRODUCT,
                "relation_type" => 0,
                "relation_field" => "",
                "relation_field_type" => 0,
                "relation_field_name" => "",
                "ext_info" => json_encode([
                    'empty' => 'null',
                    'decimal' => 4,
                    'allow_setting_decimal' => FieldConstant::ALLOW_SETTING_DECIMAL,
                    'allow_setting_default_value_formula' => FieldConstant::ALLOW_SETTING_DEFAULT_VALUE_FORMULA,
                    'token' => [
                        [
                            "type" => "field",
                            "value" => "quotation_product_id.objQuotationProduct.purchase_quote_cny",
                            "value_type" => "number",
                            "name" => "采购报价CNY",
                            "full_name" => "采购报价CNY"
                        ],
                        [
                            "type" => "operator",
                            "value" => "/",
                            "name" => "除"
                        ],
                        [
                            "type"=> "operator",
                            "value"=> "(",
                            "name"=> ""
                        ],
                        [
                            "type" => "field",
                            "value" => "quotation_id.objQuotation.exchange_rate",
                            "value_type" => "number",
                            "name" => "汇率(兑CNY)",
                            "full_name" => "汇率(兑CNY)"
                        ],
                        [
                            "type"=> "operator",
                            "value"=> "/",
                            "name"=> "除"
                        ],
                        [
                            "type"=> "input",
                            "value"=> "100",
                            "value_type"=> "number"
                        ],
                        [
                            "type"=> "operator",
                            "value"=> ")",
                            "name"=> ""
                        ]
                    ],
                    'expression' => "{quotation_product_id.objQuotationProduct.purchase_quote_cny}/({quotation_id.objQuotation.exchange_rate}/100)",
                ])
            ],
        ];

        foreach ($quotationSettings as &$setting) {
            $setting['type'] = \Constants::TYPE_QUOTATION;
            $setting['base'] = 1;
            $setting['default'] = $setting['default'] ?? '';
//            $setting['columns'] = $setting['columns'] ?? $setting['id'];
            $setting['relation_type'] = 0;
            $setting['relation_field'] = '';
            $setting['relation_field_type'] = 0;
            $setting['relation_field_name'] = '';
            $setting['order'] = Sorter::MIN_ORDER;
            $setting['app_order'] = Sorter::MIN_ORDER;
            $setting['readonly'] = $setting['readonly'] ?? 0;
            $setting['after'] = $setting['after'] ?? '';
        }

        return $quotationSettings;
    }
}
