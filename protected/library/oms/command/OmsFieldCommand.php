<?php


namespace common\library\oms\command;

use common\commands\iteration\erp_v6_4\AddTransferInvoiceCustomFieldTask;
use common\commands\iteration\erp_v6_4\AddTransferInvoiceSystemFieldTask;
use common\commands\iteration\erp_v6_4\TransferIgnoreClient;
use common\library\custom_field\FieldExportService;
use common\library\custom_field\Helper;
use common\library\invoice\export\template\InvoiceExportTemplateHelper;
use \common\library\privilege_v3\PrivilegeService;
use common\library\custom_field\CustomFieldService;
use common\library\util\sort\CustomFieldSortRepository;
use Constants;

trait OmsFieldCommand
{
    use PurchaseInboundFieldSetting;
    use OtherInboundFieldSetting;
    Use SalesOutboundFieldSetting;
    use OtherOutboundFieldSetting;
    use PurchaseReturnFieldSetting;
    use ProductTransferInboundFieldSetting;
    use ProductTransferPurchaseFieldSetting;
    use ProductTransferOutboundFieldSetting;
    use OtherTransferFieldSetting;
    use PaymentInvoiceFieldSetting;
    use CostInvoiceFieldSetting;
    use CashCollectionInvoiceFieldSetting;
    use CashCollectionFieldSetting;
    use OrderProfitFieldSetting;
    use OrderFieldSetting;
    use ShippingFieldSetting;
    use ProductFieldSetting;

    public function actionInitOmsField($client_id, $grey = 0, $greyNum = null)
    {
        if ($client_id) {
            $clientIds = explode(',', $client_id);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            try {
                \User::setLoginUserById(PrivilegeService::getInstance($clientId)->getAdminUserId());
                self::info("[$clientId]: doing...");

                $this->actionSyncByClient($clientId);

            } catch (\Exception $e) {
                self::info("error: {$e->getMessage()}");
            }
        }

        self::info("all done!!!");
    }

    public function actionInitOmsField4Admin()
    {
//        $modules = [28,29,30,31,33];
        $modules = [44];
        $map= [
            \Constants::TYPE_PURCHASE_INBOUND_INVOICE => $this->purchaseInboundFieldsSetting(),
            \Constants::TYPE_OTHER_INBOUND_INVOICE    => $this->otherInboundFieldsSetting(),
            \Constants::TYPE_SALE_OUTBOUND_INVOICE    => $this->salesOutboundFieldSetting(),
            \Constants::TYPE_OTHER_OUTBOUND_INVOICE   => $this->otherOutboundFieldSetting(),
            \Constants::TYPE_PURCHASE_RETURN_INVOICE  => $this->purchaseReturnFieldSetting(),
            \Constants::TYPE_PAYMENT_INVOICE          => $this->paymentInvoiceFieldsSetting(),
        ];

        foreach ($modules  as $module) {
            $this->syncSystemFields($module,$map[$module]);
        }
    }

    public function actionInitProductTransferField4Admin()
    {
        //oms闭环优化迭代新增 入库、发货
        $modules = [39, 40];
        $map = [
            \Constants::TYPE_PRODUCT_TRANSFER_PURCHASE => $this->productTransferPurchaseFieldsSetting(),
            \Constants::TYPE_PRODUCT_TRANSFER_INBOUND  => $this->productTransferInboundFieldsSetting(),
            \Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND => $this->productTransferOutboundFieldsSetting(),
        ];
        foreach ($modules  as $module) {
            $this->syncSystemFields($module,$map[$module]);
        }
    }

    public function actionInitOmsFieldTest($module = null)
    {
        if (\Yii::app()->params['env'] != 'test') {
            throw  new \RuntimeException('非测试环境');
        }

        $clientIds = [
            14787
        ];

        $this->actionClean($clientIds);

        if(is_null($module)) {
            $modules = [44];
        }else{
            $modules = [$module];
        }

        $map= [
            \Constants::TYPE_PURCHASE_INBOUND_INVOICE => $this->purchaseInboundFieldsSetting(),
            \Constants::TYPE_OTHER_INBOUND_INVOICE    => $this->otherInboundFieldsSetting(),
            \Constants::TYPE_SALE_OUTBOUND_INVOICE    => $this->salesOutboundFieldSetting(),
            \Constants::TYPE_OTHER_OUTBOUND_INVOICE   => $this->otherOutboundFieldSetting(),
            \Constants::TYPE_PURCHASE_RETURN_INVOICE  => $this->purchaseReturnFieldSetting(),
            \Constants::TYPE_PAYMENT_INVOICE          => $this->paymentInvoiceFieldsSetting(),
        ];

        foreach ($modules  as $module) {
            $this->syncSystemFields($module,$map[$module]);
        }

        foreach ( $clientIds as  $clientId) {
            $this->actionSyncByClient($clientId);
            $this->actionInitOmsDefaultSNRule($clientId);
        }

    }

    public function actionClean($clientIds,$module = null)
    {
        if (\Yii::app()->params['env'] != 'test') {
            throw  new \RuntimeException('非测试环境');
        }

        if(is_null($module)) {
            $module = '(44)';
        }else{
            $module = '('.$module.')';
        }

        $sql = "DELETE FROM tbl_custom_field WHERE type in {$module} and base=1;";

        foreach ($clientIds as  $clientId) {
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            $db->createCommand($sql)->execute();
        }

        $sql = "DELETE FROM tbl_system_field WHERE type in {$module};";

        $db =  \Yii::app()->getDb();
        $db->createCommand($sql)->execute();

    }

    public function actionInitOmsDefaultSNRule($client_id, $grey = 0, $greyNum = null)
    {
        if ($client_id) {
            $clientIds = explode(',', $client_id);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            try {
                \User::setLoginUserById(PrivilegeService::getInstance($clientId)->getAdminUserId());
                self::info("[$clientId]: doing...");

                \CustomerOptionService::initOmsDefaultSNRule($clientId);

            } catch (\Exception $e) {
                self::info("error: {$e->getMessage()}");
            }
        }

        self::info("all done!!!");
    }

    /**
     * 添加产品模型到库存模块 系统字段 （全量后执行一次）
     */
    public function actionAddProductModelSystemField(){
        $typeArr = [
            \Constants::TYPE_PURCHASE_INBOUND_INVOICE,
            \Constants::TYPE_OTHER_INBOUND_INVOICE,
            \Constants::TYPE_SALE_OUTBOUND_INVOICE,
            \Constants::TYPE_OTHER_OUTBOUND_INVOICE,
            \Constants::TYPE_PURCHASE_RETURN_INVOICE,
        ];

        //获取系统字段
        $intoFields = [
            [
                'id'         => 'product_model',
                'name'       => '产品型号',
                'require'    => 0,
                'hint'       => '',
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => 3, // 商品分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => 1,
            ],
        ];

        //同步到系统表
        foreach ($typeArr as $type) {
            $this->syncSystemFields($type, $intoFields);
        }
    }

    /**
     * 添加产品模型到库存模块 客户表字段
     */
    public function actionAddProductModelCustomField($clientId = 14367, $grey = 0, $greyNum = 0){
        $typeArr = [
            \Constants::TYPE_PURCHASE_INBOUND_INVOICE,
            \Constants::TYPE_OTHER_INBOUND_INVOICE,
            \Constants::TYPE_SALE_OUTBOUND_INVOICE,
            \Constants::TYPE_OTHER_OUTBOUND_INVOICE,
            \Constants::TYPE_PURCHASE_RETURN_INVOICE,
        ];
        $system_fields = [
            'id' => 'product_model',
            'type' => 28,//循环写入
            'name' => '产品型号',
            'base' => 1,
            'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            'require' => 0,
            'hint' => '',
            'edit_hint' => 0,//是否可编辑字段提示：0-否，1-是
            'group_id' => 3, // 商品分组
            'edit_required' => 0,
            'is_exportable' => 0,
            'is_editable' => 0,//是否可编辑：0-否，1-是
            'default' => '',
            'edit_default' => 0,// 是否可编辑默认值，1可以，0否
            'edit_hide' => 1,// 是否可隐藏，1可以，0否
            'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
            'is_list' => 1,
            'columns' => 'product_model',
            'relation_type' => 0,
            'relation_field' => '',
            'relation_field_type' => 0,
            'relation_field_name' => '',
            'export_scenario' => '',
            'export_group' => '',
            'readonly' => 0,
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ];

        $newField = array_combine($typeArr,array_fill(0,count($typeArr),$system_fields));

        array_walk($newField, function (&$v, $k) {
            $v['type'] = $k;
        });
//        echo json_encode($newField);die;

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            // 字段排在product_model之后
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }

            $this->syncSystem2Customer($newField, $clientId);
            $this->syncSystem2ExportSetting($newField, $clientId);
            $this->syncSystem2FieldGroup($newField, $clientId);
            echo '成功处理client_id：', $clientId, PHP_EOL;
        }
    }

    /**
     * 采购订单新增采购任务相关字段 系统字段 （全量后执行一次）
     */
    public function actionAddTransferInvoiceToPurchaseOrderSystemField(){
        //获取系统字段
        $systemFields = \Yii::app()->db->createCommand('SELECT * FROM tbl_system_field WHERE `type`=22 ORDER BY `order` DESC')->queryAll(true);
        $intoFields = [
            [
                'id' => 'transfer_invoice_serial_id',
                'name' => '关联采购任务',
                'require' => 0,
                'hint' => '',
                'edit_hint' => 0,//是否可编辑字段提示：0-否，1-是
                'group_id' => 2, // 商品分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'readonly' => 0, //只读字段
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ],
            [
                'id' => 'transfer_invoice_id',
                'name' => '产品流转ID',
                'require' => 0,
                'hint' => '',
                'edit_hint' => 0,//是否可编辑字段提示：0-否，1-是
                'group_id' => 2, // 商品分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'readonly' => 1, //只读字段
                'disable_flag' => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
            ],
            [
                'id' => 'transfer_invoice_record_id',
                'name' => '产品流转明细ID',
                'require' => 0,
                'hint' => '',
                'edit_hint' => 0,//是否可编辑字段提示：0-否，1-是
                'group_id' => 2, // 商品分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'readonly' => 1, //只读字段
                'disable_flag' => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
            ],
        ];

        $newSystemFields = [];

        foreach ($systemFields as $field) {
            $newSystemFields[] = $field;
            if ($field['id'] == 'order_id') {
                foreach ($intoFields as $intoField) {
                    $newSystemFields[] = $intoField;
                }
            }
        }

        $conut = count($newSystemFields);
        foreach ($newSystemFields as &$newSystemField) {
            $newSystemField['order'] = $conut;
            $newSystemField['app_order'] = $conut;
            $conut--;
        }
        unset($newSystemField);
        //同步到系统表
        $this->syncSystemFields(Constants::TYPE_PURCHASE_ORDER, $newSystemFields);
    }

    /**
     * 采购订单新增采购任务相关字段 客户表字段
     */
    public function actionAddTransferInvoiceToPurchaseOrderCustomField($clientId = 0, $grey = 0, $greyNum = 0){
        $system_fields = [
            [
                'id'         => 'transfer_invoice_serial_id',
                'type' => Constants::TYPE_PURCHASE_ORDER,//采购订单
                'group_id'   => CustomFieldService::PURCHASE_ORDER_GROUP_PRODUCT,
                'base' => 1,
                'name'       => '关联采购任务',
                'order' => 0,
                'app_order' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                'require' => 0,
                'edit_required' => 0,
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 0,
                'is_exportable' => 0,
                'is_editable' => 0,
                'is_list' => 1,
                'columns' => 'transfer_invoice_serial_id',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '',
                'readonly' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
            [
                'id'         => 'transfer_invoice_id',
                'type' => Constants::TYPE_PURCHASE_ORDER,//采购订单
                'group_id'   => CustomFieldService::PURCHASE_ORDER_GROUP_PRODUCT,
                'base' => 1,
                'name'       => '产品流转ID',
                'order' => 0,
                'app_order' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'require' => 0,
                'edit_required' => 0,
                'disable_flag' => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 0,
                'is_exportable' => 0,
                'is_editable' => 0,
                'is_list' => 1,
                'columns' => 'transfer_invoice_id',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '',
                'readonly' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
            [
                'id'         => 'transfer_invoice_record_id',
                'type' => Constants::TYPE_PURCHASE_ORDER,//采购订单
                'group_id'   => CustomFieldService::PURCHASE_ORDER_GROUP_PRODUCT,
                'base' => 1,
                'name'       => '产品流转明细ID',
                'order' => 0,
                'app_order' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'require' => 0,
                'edit_required' => 0,
                'disable_flag' => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 0,
                'is_exportable' => 0,
                'is_editable' => 0,
                'is_list' => 1,
                'columns' => 'transfer_invoice_record_id',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '',
                'readonly' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
        ];

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $beforeFields = [
            'transfer_invoice_serial_id' => 'order_id',
            'transfer_invoice_id' => 'transfer_invoice_serial_id',
            'transfer_invoice_record_id' => 'transfer_invoice_id',
        ];

        foreach ($clientIds as $clientId) {
            // 字段排在product_model之后
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }

            $this->syncSystem2Customer($system_fields, $clientId);
            //处理字段排序
            echo '--开始处理新字段顺序'.PHP_EOL;
            foreach ($system_fields as $k => $field) {
                $id = $field['id'];
                $type = $field['type'];
                $beforeId = $beforeFields[$id] ?? false;
                if (!$beforeId) {
                    continue;
                }
                $sorter = new \common\library\util\sort\Sorter(new \common\library\util\sort\CustomFieldSortRepository($clientId, $type));
                $sorter->setResort(true);
                $sorter->setId($id, true);
                $sorter->setSortField('order');
                $orderNum = $sorter->after($beforeId);
                $sorter->setSortField('app_order');
                $appOrderNum = $sorter->after($beforeId);
                $system_fields[$k]['order'] = $orderNum;
                $system_fields[$k]['app_order'] = $appOrderNum;
            }
            $this->syncSystem2ExportSetting($system_fields, $clientId);
            $this->syncSystem2FieldGroup($system_fields, $clientId);
            echo '成功处理client_id：', $clientId, PHP_EOL;
        }

    }

    /**
     * 更新采购订单 【费用名称】 字段类型
     */
    public function actionUpdatePurchaseOrderCostNameCustomField($clientId = 0, $grey = 0, $greyNum = 0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }


        foreach ($clientIds as $clientId) {
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }
            $updateSql = "update tbl_custom_field set field_type=1 where client_id={$clientId} and id='cost_name' and type=22";
            $result = $db->createCommand($updateSql)->execute();
            if ($result) {
                echo '成功处理client_id：', $clientId, PHP_EOL;
            } else {
                echo '～～～失败处理client_id：', $clientId, PHP_EOL;
            }
        }

    }

    /**
     * 更新采购订单 【计量单位】 字段类型  --全量
     */
    public function actionFixPurchaseOrderUnitField($skipSetId=0)
    {
        //修复admin表
        echo  'fix admin  system ...',PHP_EOL;
        $sql = "update tbl_system_field set `field_type`=1 where id='unit' and type=22";
        \Yii::app()->getDb()->createCommand($sql)->execute();
        echo  'admin fixed ..',PHP_EOL;

        $dbList = $this->dbSetList(1, null);

        $dbNum = 0;
        $dbCount = count($dbList);
        echo  'to do ',$dbCount,'...',PHP_EOL;
        foreach ($dbList as $dbItem)
        {
            $dbNum++;
            $setId = $dbItem['set_id'];
            if ($setId < $skipSetId) {
                continue;
            }
            echo 'doing  set id ',$setId,PHP_EOL;
            $db = \ProjectActiveRecord::getDbByDbSetId($setId);
            $sql = "update tbl_custom_field set `field_type`=1 where id='unit' and type=22";
            $rows = $db->createCommand($sql)->execute();
            echo  'affected rows ',$rows,PHP_EOL;
        }
        echo 'all '.$dbNum.'done ! ';
    }

    public function actionProductTransferRun($clientId = 0, $grey = 0, $greyNum = 0)
    {

        echo "*****开始执行：actionAddTransferInvoiceToPurchaseOrderCustomField" . PHP_EOL;
        $this->actionAddTransferInvoiceToPurchaseOrderCustomField($clientId, $grey, $greyNum);

        echo "*****开始执行：actionInitOmsField" . PHP_EOL;
        //fix
        $this->actionInitOmsField($clientId, $grey, $greyNum);


        echo "*****开始执行：actionInitOmsDefaultSNRule" . PHP_EOL;
        $this->actionInitOmsDefaultSNRule($clientId, $grey, $greyNum);

        echo "*****开始执行：actionAddProductModelCustomField" . PHP_EOL;
        $this->actionAddProductModelCustomField($clientId, $grey, $greyNum);
    }

    /**
     * 采购入库单、销售出库单 新增任务相关字段 系统字段 （全量后执行一次）
     */
    public function actionAddTransferInvoiceToInvoiceSystemField(){
        $typeArr = [
            Constants::TYPE_PURCHASE_INBOUND_INVOICE,
            Constants::TYPE_SALE_OUTBOUND_INVOICE
        ];

        $serialNameMap = [
            Constants::TYPE_PURCHASE_INBOUND_INVOICE => '关联入库任务',
            Constants::TYPE_SALE_OUTBOUND_INVOICE => '关联出库任务'
        ];
        $beforeFieldMap = [
            Constants::TYPE_PURCHASE_INBOUND_INVOICE => 'purchase_order_no',
            Constants::TYPE_SALE_OUTBOUND_INVOICE => 'order_no'
        ];

        foreach ($typeArr as $type) {
            //获取系统字段
            $systemFields = \Yii::app()->db->createCommand('SELECT * FROM tbl_system_field WHERE `type`=' . $type . ' ORDER BY `order` DESC')->queryAll(true);
            $intoFields = [
                [
                    'id' => 'transfer_invoice_serial_id',
                    'name' => $serialNameMap[$type],
                    'require' => 0,
                    'hint' => '',
                    'edit_hint' => 1,//是否可编辑字段提示：0-否，1-是
                    'group_id' => 3, // 商品分组
                    'edit_required' => 1,
                    'is_exportable' => 0,
                    'is_list' => 1,
                    'readonly' => 0, //只读字段
                    'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                    'edit_hide' => 1,// 是否可隐藏，1可以，0否
                    'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                    'is_editable' => 0,//是否可编辑：0-否，1-是
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                ],
                [
                    'id' => 'transfer_invoice_id',
                    'name' => '产品流转ID',
                    'require' => 0,
                    'hint' => '',
                    'edit_hint' => 0,//是否可编辑字段提示：0-否，1-是
                    'group_id' => 3, // 商品分组
                    'edit_required' => 0,
                    'is_exportable' => 0,
                    'is_list' => 1,
                    'readonly' => 1, //只读字段
                    'disable_flag' => 1,//前端是否隐藏，1隐藏，0不隐藏
                    'edit_hide' => 0,// 是否可隐藏，1可以，0否
                    'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                    'is_editable' => 0,//是否可编辑：0-否，1-是
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                ],
                [
                    'id' => 'transfer_invoice_record_id',
                    'name' => '产品流转明细ID',
                    'require' => 0,
                    'hint' => '',
                    'edit_hint' => 0,//是否可编辑字段提示：0-否，1-是
                    'group_id' => 3, // 商品分组
                    'edit_required' => 0,
                    'is_exportable' => 0,
                    'is_list' => 1,
                    'readonly' => 1, //只读字段
                    'disable_flag' => 1,//前端是否隐藏，1隐藏，0不隐藏
                    'edit_hide' => 0,// 是否可隐藏，1可以，0否
                    'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                    'is_editable' => 0,//是否可编辑：0-否，1-是
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                ],
            ];

            $newSystemFields = [];

            foreach ($systemFields as $field) {
                $newSystemFields[] = $field;
                if ($field['id'] == $beforeFieldMap[$type]) {
                    foreach ($intoFields as $intoField) {
                        $newSystemFields[] = $intoField;
                    }
                }
            }

            $conut = count($newSystemFields);
            foreach ($newSystemFields as &$newSystemField) {
                $newSystemField['order'] = $conut;
                $newSystemField['app_order'] = $conut;
                $conut--;
            }
            unset($newSystemField);
            //同步到系统表
            $this->syncSystemFields($type, $newSystemFields);
        }
    }

    /**
     * 采购入库单、销售出库单 新增任务相关字段 客户表字段
     */
    public function actionAddTransferInvoiceToInvoiceCustomField($clientId = 0, $grey = 0, $greyNum = 0, $skipClientId=0){
        $typeArr = [
            Constants::TYPE_PURCHASE_INBOUND_INVOICE,
            Constants::TYPE_SALE_OUTBOUND_INVOICE
        ];
        $serialNameMap = [
            Constants::TYPE_PURCHASE_INBOUND_INVOICE => '关联入库任务',
            Constants::TYPE_SALE_OUTBOUND_INVOICE => '关联出库任务'
        ];
        $beforeFieldMap = [
            Constants::TYPE_PURCHASE_INBOUND_INVOICE => 'purchase_order_no',
            Constants::TYPE_SALE_OUTBOUND_INVOICE => 'order_no'
        ];

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($typeArr as $type) {
            $system_fields = [
                [
                    'id'         => 'transfer_invoice_serial_id',
                    'type' => $type,//相关单据
                    'group_id'   => 3,
                    'base' => 1,
                    'name'       => $serialNameMap[$type],
                    'order' => 0,
                    'app_order' => 0,
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'require' => 0,
                    'edit_required' => 1,
                    'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                    'edit_hide' => 1,// 是否可编辑，1可以，0否
                    'default' => '',
                    'edit_default' => 0,
                    'hint' => '',
                    'edit_hint' => 1,
                    'is_exportable' => 0,
                    'is_editable' => 0,
                    'is_list' => 1,
                    'columns' => 'transfer_invoice_serial_id',
                    'relation_type' => 0,
                    'relation_field' => '',
                    'relation_field_type' => 0,
                    'relation_field_name' => '',
                    'export_scenario' => '',
                    'export_group' => '',
                    'readonly' => 0,
                    'create_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s')
                ],
                [
                    'id'         => 'transfer_invoice_id',
                    'type' => $type,//相关单据
                    'group_id'   => 3,
                    'base' => 1,
                    'name'       => '产品流转ID',
                    'order' => 0,
                    'app_order' => 0,
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'require' => 0,
                    'edit_required' => 0,
                    'disable_flag' => 1,//前端是否隐藏，1隐藏，0不隐藏
                    'edit_hide' => 0,// 是否可编辑，1可以，0否
                    'default' => '',
                    'edit_default' => 0,
                    'hint' => '',
                    'edit_hint' => 0,
                    'is_exportable' => 0,
                    'is_editable' => 0,
                    'is_list' => 1,
                    'columns' => 'transfer_invoice_id',
                    'relation_type' => 0,
                    'relation_field' => '',
                    'relation_field_type' => 0,
                    'relation_field_name' => '',
                    'export_scenario' => '',
                    'export_group' => '',
                    'readonly' => 1,
                    'create_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s')
                ],
                [
                    'id'         => 'transfer_invoice_record_id',
                    'type' => $type,//相关单据
                    'group_id'   => 3,
                    'base' => 1,
                    'name'       => '产品流转明细ID',
                    'order' => 0,
                    'app_order' => 0,
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'require' => 0,
                    'edit_required' => 0,
                    'disable_flag' => 1,//前端是否隐藏，1隐藏，0不隐藏
                    'edit_hide' => 0,// 是否可编辑，1可以，0否
                    'default' => '',
                    'edit_default' => 0,
                    'hint' => '',
                    'edit_hint' => 0,
                    'is_exportable' => 0,
                    'is_editable' => 0,
                    'is_list' => 1,
                    'columns' => 'transfer_invoice_record_id',
                    'relation_type' => 0,
                    'relation_field' => '',
                    'relation_field_type' => 0,
                    'relation_field_name' => '',
                    'export_scenario' => '',
                    'export_group' => '',
                    'readonly' => 1,
                    'create_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s')
                ],
            ];

            $beforeFields = [
                'transfer_invoice_serial_id' => $beforeFieldMap[$type],
                'transfer_invoice_id' => 'transfer_invoice_serial_id',
                'transfer_invoice_record_id' => 'transfer_invoice_id',
            ];

            foreach ($clientIds as $clientId) {
                if($clientId < $skipClientId){
                    continue;
                }
                // 字段排在product_model之后
                echo '---开始处理client_id：', $clientId, PHP_EOL;
                $db = \ProjectActiveRecord::getDbByClientId($clientId);
                if (!$db) {
                    echo '--此client无效..'.PHP_EOL;
                    continue;
                }

                $this->syncSystem2Customer($system_fields, $clientId);
                //处理字段排序
                echo '--开始处理新字段顺序'.PHP_EOL;
                foreach ($system_fields as $k => $field) {
                    $id = $field['id'];
                    $type = $field['type'];
                    $beforeId = $beforeFields[$id] ?? false;
                    if (!$beforeId) {
                        continue;
                    }
                    $sorter = new \common\library\util\sort\Sorter(new \common\library\util\sort\CustomFieldSortRepository($clientId, $type));
                    $sorter->setResort(true);
                    $sorter->setId($id, true);
                    $sorter->setSortField('order');
                    $orderNum = $sorter->after($beforeId);
                    $sorter->setSortField('app_order');
                    $appOrderNum = $sorter->after($beforeId);
                    $system_fields[$k]['order'] = $orderNum;
                    $system_fields[$k]['app_order'] = $appOrderNum;
                }
                $this->syncSystem2ExportSetting($system_fields, $clientId);
                $this->syncSystem2FieldGroup($system_fields, $clientId);
                echo '成功处理client_id：', $clientId, PHP_EOL;
            }
        }
    }

    //oms闭环优化执行脚本
    public function actionOmsCloseLoopOptimizeRun($clientId = 0, $grey = 0, $greyNum = 0)
    {
        echo "*****开始执行：actionAddTransferInvoiceToInvoiceCustomField" . PHP_EOL;
        $this->actionAddTransferInvoiceToInvoiceCustomField($clientId, $grey, $greyNum);

        echo "*****开始执行：actionInitOmsField" . PHP_EOL;
        $this->actionInitOmsField($clientId, $grey, $greyNum);

        echo "*****开始执行：actionInitOmsDefaultSNRule" . PHP_EOL;
        $this->actionInitOmsDefaultSNRule($clientId, $grey, $greyNum);

        echo "*****开始执行：用户清除缓存" . PHP_EOL;
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        //清除缓存
        foreach ($clientIds as $clientId){
            (new \common\library\privilege_v3\PrivilegeField($clientId))->flushReferFieldPrivilege(\Constants::TYPE_PURCHASE_ORDER);
        }

    }

    /**
     * 全量更新
     * 更新销售出库单 `出库单价` -> `销售单价` 字段名字
     * 更新oms `当前处理人` -> `处理人` 字段名字
     * 更新产品流转 `实际完成时间` -> `任务完成时间` 字段名字
     * 采购订单字段 `关联销售订单`显示 ->需要刷新缓存
     * 采购订单字段 `产品编号` 修改
     * 更新采购订单 【入库单价】 名字改为 【采购单价】
     */
    public function actionFixOmsCloseLoopOptimizeField()
    {
        //修复admin表
        echo  'fix admin  system ...',PHP_EOL;
        $sql = "update tbl_system_field set `name`='销售单价' where id='sale_price' and type=30";
        \Yii::app()->getDb()->createCommand($sql)->execute();

        $sql = "update tbl_system_field set `name`='处理人' where id='handler' and type in (28,29,30,31,33,38,39,40)";
        \Yii::app()->getDb()->createCommand($sql)->execute();

        $sql = "update tbl_system_field set `name`='任务完成时间' where id='finish_time' and type in (38,39,40)";
        \Yii::app()->getDb()->createCommand($sql)->execute();

        $sql = "update tbl_system_field set `disable_flag`=0 where id='order_no' and type=22";
        \Yii::app()->getDb()->createCommand($sql)->execute();

        $sql = "update tbl_system_field set `edit_hide`=0 where id='product_no' and type=22";
        \Yii::app()->getDb()->createCommand($sql)->execute();

        $sql = "update tbl_system_field set `name`='采购单价' where id='purchase_price' and type=28";
        \Yii::app()->getDb()->createCommand($sql)->execute();
        echo  'admin fixed ..',PHP_EOL;

        $dbList = $this->dbSetList(1);

        $dbNum = 0;
        $dbCount = count($dbList);
        echo  'to do ',$dbCount,'...',PHP_EOL;
        foreach ($dbList as $dbItem)
        {
            $dbNum++;
            $setId = $dbItem['set_id'];
            echo 'doing  set id ',$setId,PHP_EOL;
            $db = \ProjectActiveRecord::getDbByDbSetId($setId);
            $sql = "update tbl_custom_field set `name`='销售单价' where id='sale_price' and type=30";
            $rows = $db->createCommand($sql)->execute();
            echo  'sale_price field affected rows ',$rows,PHP_EOL;

            $sql = "update tbl_custom_field set `name`='处理人' where id='handler' and type in (28,29,30,31,33,38,39,40)";
            $rows = $db->createCommand($sql)->execute();
            echo  'handler field affected rows ',$rows,PHP_EOL;

            $sql = "update tbl_custom_field set `name`='任务完成时间' where id='finish_time' and type in (38,39,40)";
            $rows = $db->createCommand($sql)->execute();
            echo  'finish_time field affected rows ',$rows,PHP_EOL;

            $sql = "update tbl_custom_field set `disable_flag`=0 where id='order_no' and type=22";
            $rows = $db->createCommand($sql)->execute();
            echo  'order_no field affected rows ',$rows,PHP_EOL;

            $sql = "update tbl_custom_field set `edit_hide`=0 where id='product_no' and type=22";
            $rows = $db->createCommand($sql)->execute();
            echo  'product_no field affected rows ',$rows,PHP_EOL;

            $sql = "update tbl_custom_field set `name`='采购单价' where id='purchase_price' and type=28";
            $rows = $db->createCommand($sql)->execute();
            echo  'purchase_price field affected rows ',$rows,PHP_EOL;
        }
        echo 'all '.$dbNum.'done ! ';
    }

    /**
     * 采购入库单 新增 待入库数量 系统字段 （全量后执行一次）
     */
    public function actionAddWaitInboundCountToPurchaseInboundSystemField(){
        //获取系统字段
        $systemFields = \Yii::app()->db->createCommand('SELECT * FROM tbl_system_field WHERE `type`=28 ORDER BY `order` DESC')->queryAll(true);
        $intoFields = [
            [
                'id' => 'wait_inbound_count',
                'name' => '待入库数量',
                'require' => 0,
                'hint' => '',
                'edit_hint' => 0,//是否可编辑字段提示：0-否，1-是
                'group_id' => CustomFieldService::PURCHASE_INBOUND_GROUP_PRODUCT, // 字段分组
                'edit_required' => 0,
                'is_exportable' => 0,
                'is_list' => 1,
                'readonly' => 1, //只读字段
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
            ]
        ];

        $newSystemFields = [];

        foreach ($systemFields as $field) {
            $newSystemFields[] = $field;
            if ($field['id'] == 'purchase_price') {
                foreach ($intoFields as $intoField) {
                    $newSystemFields[] = $intoField;
                }
            }
        }

        $conut = count($newSystemFields);
        foreach ($newSystemFields as &$newSystemField) {
            $newSystemField['order'] = $conut;
            $newSystemField['app_order'] = $conut;
            $conut--;
        }
        unset($newSystemField);
        //同步到系统表
        $this->syncSystemFields(Constants::TYPE_PURCHASE_INBOUND_INVOICE, $newSystemFields);
    }

    /**
     * 采购入库单 新增 待入库数量 字段 客户表字段
     */
    public function actionAddWaitInboundCountToPurchaseInboundCustomField($clientId = 0, $grey = 0, $greyNum = 0){
        $system_fields = [
            [
                'id'         => 'wait_inbound_count',
                'type' => Constants::TYPE_PURCHASE_INBOUND_INVOICE,//采购入库单
                'group_id'   => CustomFieldService::PURCHASE_INBOUND_GROUP_PRODUCT,
                'base' => 1,
                'name'       => '待入库数量',
                'order' => 0,
                'app_order' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'require' => 0,
                'edit_required' => 0,
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可编辑，1可以，0否
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 0,
                'is_exportable' => 0,
                'is_editable' => 0,
                'is_list' => 1,
                'columns' => 'wait_inbound_count',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '',
                'readonly' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]
        ];

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $beforeFields = [
            'wait_inbound_count' => 'purchase_price',
        ];

        foreach ($clientIds as $clientId) {
            // 字段排在product_model之后
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }

            $this->syncSystem2Customer($system_fields, $clientId);
            //处理字段排序
            echo '--开始处理新字段顺序'.PHP_EOL;
            foreach ($system_fields as $k => $field) {
                $id = $field['id'];
                $type = $field['type'];
                $beforeId = $beforeFields[$id] ?? false;
                if (!$beforeId) {
                    continue;
                }
                $sorter = new \common\library\util\sort\Sorter(new \common\library\util\sort\CustomFieldSortRepository($clientId, $type));
                $sorter->setResort(true);
                $sorter->setId($id, true);
                $sorter->setSortField('order');
                $orderNum = $sorter->after($beforeId);
                $sorter->setSortField('app_order');
                $appOrderNum = $sorter->after($beforeId);
                $system_fields[$k]['order'] = $orderNum;
                $system_fields[$k]['app_order'] = $appOrderNum;
            }
            $this->syncSystem2ExportSetting($system_fields, $clientId);
            $this->syncSystem2FieldGroup($system_fields, $clientId);
            echo '成功处理client_id：', $clientId, PHP_EOL;
        }

    }


    /**
     * 平台产品 新增 产品型号 系统字段 （全量后执行一次）
     */
    public function actionAddModelToPlatformProductSystemField(){
        //获取系统字段
        $systemFields = \Yii::app()->db->createCommand('SELECT * FROM tbl_system_field WHERE `type`=27 ORDER BY `order` DESC')->queryAll(true);
        $intoFields = [
            [
                'id' => 'model',
                'name' => '产品型号',
                'require' => 0,
                'hint' => '',
                'edit_hint' => 0,//是否可编辑字段提示：0-否，1-是
                'group_id' => 1, // 字段分组
                'edit_required' => 1,
                'is_exportable' => 0,
                'is_list' => 0,
                'readonly' => 0, //只读字段
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可隐藏，1可以，0否
                'edit_default' => 1,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
            ]
        ];

        $newSystemFields = [];

        foreach ($systemFields as $field) {
            $newSystemFields[] = $field;
            if ($field['id'] == 'store_name') {
                foreach ($intoFields as $intoField) {
                    $newSystemFields[] = $intoField;
                }
            }
        }

        $conut = count($newSystemFields);
        foreach ($newSystemFields as &$newSystemField) {
            $newSystemField['order'] = $conut;
            $newSystemField['app_order'] = $conut;
            $conut--;
        }
        unset($newSystemField);
        //同步到系统表
        $this->syncSystemFields(Constants::TYPE_PLATFORM_PRODUCT_SKU, $newSystemFields);
    }

    /**
     * 平台产品 新增 产品型号 字段 客户表字段
     */
    public function actionAddModelToPlatformProductCustomField($clientId = 0, $grey = 0, $greyNum = 0){
        $system_fields = [
            [
                'id'         => 'model',
                'type' => Constants::TYPE_PLATFORM_PRODUCT_SKU,
                'group_id'   => 1,
                'base' => 1,
                'name'       => '产品型号',
                'order' => 0,
                'app_order' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                'require' => 0,
                'edit_required' => 1,
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'default' => '',
                'edit_default' => 1,
                'hint' => '',
                'edit_hint' => 1,
                'is_exportable' => 0,
                'is_editable' => 1,
                'is_list' => 0,
                'columns' => 'model',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '',
                'readonly' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]
        ];

        $beforeFields = [
            'model' => 'attributes_info',
        ];

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            // 字段排在product_model之后
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }

            $this->syncSystem2Customer($system_fields, $clientId);
            //处理字段排序
            echo '--开始处理新字段顺序'.PHP_EOL;
            foreach ($system_fields as $k => $field) {
                $id = $field['id'];
                $type = $field['type'];
                $beforeId = $beforeFields[$id] ?? false;
                if (!$beforeId) {
                    continue;
                }
                $sorter = new \common\library\util\sort\Sorter(new \common\library\util\sort\CustomFieldSortRepository($clientId, $type));
                $sorter->setResort(true);
                $sorter->setId($id, true);
                $sorter->setSortField('order');
                $orderNum = $sorter->before($beforeId);
                $sorter->setSortField('app_order');
                $appOrderNum = $sorter->before($beforeId);
                $system_fields[$k]['order'] = $orderNum;
                $system_fields[$k]['app_order'] = $appOrderNum;
            }

            $this->syncSystem2ExportSetting($system_fields, $clientId);
            $this->syncSystem2FieldGroup($system_fields, $clientId);
            echo '成功处理client_id：', $clientId, PHP_EOL;
        }

    }

    //【平台产品】增加显示字段「产品型号」字段默认勾选脚本
    public function actionAddModelToPlatformProductUserSetting($clientId = 0, $grey = 0, $greyNum = 0)
    {
        $addModelField = [
            "width" => 200,
            "id" => "model",
            "fixed" => 0
        ];
        $beforeFields = [
            'model' => 'attributes_info',
        ];

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            // 字段排在product_model之后
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }

            $userSettingSql = "select * from tbl_user_setting where client_id={$clientId} and `key`='platform.product.list.field'";
            $userSettingList = $db->createCommand($userSettingSql)->queryAll();

            foreach ($userSettingList as $userSettingItem) {
                $value = json_decode($userSettingItem['value'], true);
                $newValue = [];
                //判断有没有model ，防止重复跑导致数据错乱
                if (in_array('model', array_column($value, 'id'))) {
                    echo $userSettingItem['user_id']."：已存在【model】字段".PHP_EOL;
                    continue;
                }
                if (in_array($beforeFields['model'], array_column($value, 'id'))) {
                    foreach ($value as $item) {
                        if ($item['id'] == $beforeFields['model']) {
                            $newValue[] = $addModelField;
                            $newValue[] = $item;
                        }else{
                            $newValue[] = $item;
                        }
                    }
                }else{
                    $newValue = array_push($value,$addModelField);
                }
                $newValue = json_encode($newValue);

                $updateUserSettingSql = "update tbl_user_setting set value=:newValue  where user_id=:user_id and `key`=:keyword";
                $updateUserSettingArr = [
                    ':newValue' => $newValue,
                    ':user_id' => $userSettingItem['user_id'],
                    ':keyword' => 'platform.product.list.field',
                ];
                $db->createCommand($updateUserSettingSql)->execute($updateUserSettingArr);

                echo '成功处理user_id：', $userSettingItem['user_id'], PHP_EOL;
            }

            echo '成功处理client_id：', $clientId, PHP_EOL;
        }
    }

    /**
     * 进销存任务明细 新增 【产品描述】  系统字段 （全量后执行一次）
     */
    public function actionAddDescriptionToProductTransferSystemField(){
        $addTypes = [
            Constants::TYPE_PRODUCT_TRANSFER_PURCHASE,
            Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND
        ];

        foreach ($addTypes as $addType) {
            //获取系统字段
            $systemFields = \Yii::app()->db->createCommand('SELECT * FROM tbl_system_field WHERE `type`=' . $addType . ' ORDER BY `order` DESC')->queryAll(true);
            $intoFields = [
                [
                    'id' => 'description',
                    'name' => '产品描述',
                    'require' => 0,
                    'hint' => '',
                    'edit_hint' => 0,//是否可编辑字段提示：0-否，1-是
                    'group_id' => CustomFieldService::PRODUCT_TRANSFER_PURCHASE_GROUP_PRODUCT, // 字段分组
                    'edit_required' => 1,
                    'is_exportable' => 0,
                    'is_list' => 1,
                    'readonly' => 0, //只读字段
                    'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                    'edit_hide' => 1,// 是否可隐藏，1可以，0否
                    'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                    'is_editable' => 1,//是否可编辑：0-否，1-是
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXTAREA,
                ]
            ];

            $newSystemFields = [];

            foreach ($systemFields as $field) {
                $newSystemFields[] = $field;
                if ($field['id'] == 'record_remark') {
                    foreach ($intoFields as $intoField) {
                        $newSystemFields[] = $intoField;
                    }
                }
            }

            $conut = count($newSystemFields);
            foreach ($newSystemFields as &$newSystemField) {
                $newSystemField['order'] = $conut;
                $newSystemField['app_order'] = $conut;
                $conut--;
            }
            unset($newSystemField);
            //同步到系统表
            $this->syncSystemFields($addType, $newSystemFields);
        }
    }

    /**
     * 进销存任务明细 新增 【产品描述】 客户表字段
     */
    public function actionAddDescriptionToProductTransferCustomField($clientId = 0, $grey = 0, $greyNum = 0){
        $addTypes = [
            Constants::TYPE_PRODUCT_TRANSFER_PURCHASE,
            Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND
        ];
        $system_fields = [];
        foreach ($addTypes as $addType) {
            $addField = [
                'id' => 'description',
                'type' => $addType,//类型
                'group_id' => 3,
                'base' => 1,
                'name' => '产品描述',
                'order' => 0,
                'app_order' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_TEXTAREA,
                'require' => 0,
                'edit_required' => 1,
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可编辑，1可以，0否
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 0,
                'is_exportable' => 0,
                'is_editable' => 1,
                'is_list' => 1,
                'columns' => 'description',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '',
                'readonly' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ];
            array_push($system_fields, $addField);
        }

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            // 字段排在product_model之后
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }

            $this->syncSystem2Customer($system_fields, $clientId);
            $this->syncSystem2ExportSetting($system_fields, $clientId);
            $this->syncSystem2FieldGroup($system_fields, $clientId);
            echo '成功处理client_id：', $clientId, PHP_EOL;
        }

    }

    /**
     * 更新采购单 产品总数量 字段 信息
     * 然后更新权限字段缓存
     */
    public function actionUpdatePurchaseOrderField($clientId = 0, $grey = 0, $greyNum = 0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            // 字段排在product_model之后
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }

            $params = [
                ':id' => 'product_total_count',
                ':type' => 22,
                ':client_id' => $clientId
            ];
            $updateCustomFieldSql = "UPDATE tbl_custom_field SET `disable_flag`=:disable_flag WHERE `id`=:id AND `type`=:type AND `client_id`=:client_id";
            $updateCustomFieldParams = array_merge([':disable_flag' => 0], $params);
            $db->createCommand($updateCustomFieldSql)->execute($updateCustomFieldParams);

            $privilegeField = new \common\library\privilege_v3\PrivilegeField($clientId);
            $privilegeField->flushCache();
            echo '成功处理client_id：', $clientId, PHP_EOL;
        }
    }

    /**
     * 单据 新增 【产品总数量】  系统字段 （全量后执行一次）
     */
    public function actionAddProductCountToInvoiceSystemField(){
        $addTypes = [
            Constants::TYPE_PURCHASE_INBOUND_INVOICE,
            Constants::TYPE_OTHER_INBOUND_INVOICE,
            Constants::TYPE_SALE_OUTBOUND_INVOICE,
            Constants::TYPE_OTHER_OUTBOUND_INVOICE,
            Constants::TYPE_PURCHASE_RETURN_INVOICE,
            Constants::TYPE_PRODUCT_TRANSFER_PURCHASE,
            Constants::TYPE_PRODUCT_TRANSFER_INBOUND,
            Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND
        ];

        foreach ($addTypes as $addType) {
            //获取系统字段
            $systemFields = \Yii::app()->db->createCommand('SELECT * FROM tbl_system_field WHERE `type`=' . $addType . ' ORDER BY `order` DESC')->queryAll(true);
            $intoFields = [
                [
                    'id' => 'product_total_count',
                    'name' => '产品总数量',
                    'require' => 0,
                    'hint' => '',
                    'edit_hint' => 1,//是否可编辑字段提示：0-否，1-是
                    'group_id' => 2, // 字段分组
                    'edit_required' => 0,
                    'is_exportable' => 0,
                    'is_list' => 0,
                    'readonly' => 1, //只读字段
                    'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                    'edit_hide' => 0,// 是否可隐藏，1可以，0否
                    'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                    'is_editable' => 0,//是否可编辑：0-否，1-是
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                ]
            ];

            $newSystemFields = array($systemFields,$intoFields);

            $conut = count($newSystemFields);
            foreach ($newSystemFields as &$newSystemField) {
                $newSystemField['order'] = $conut;
                $newSystemField['app_order'] = $conut;
                $conut--;
            }
            unset($newSystemField);
            //同步到系统表
            $this->syncSystemFields($addType, $newSystemFields);
        }
    }

    /**
     * 单据 新增 【产品总数量】 客户表字段
     */
    public function actionAddProductCountToInvoiceCustomField($clientId = 0, $grey = 0, $greyNum = 0){
        $addTypes = [
            Constants::TYPE_PURCHASE_INBOUND_INVOICE,
            Constants::TYPE_OTHER_INBOUND_INVOICE,
            Constants::TYPE_SALE_OUTBOUND_INVOICE,
            Constants::TYPE_OTHER_OUTBOUND_INVOICE,
            Constants::TYPE_PURCHASE_RETURN_INVOICE,
            Constants::TYPE_PRODUCT_TRANSFER_PURCHASE,
            Constants::TYPE_PRODUCT_TRANSFER_INBOUND,
            Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND
        ];

        $system_fields = [];
        foreach ($addTypes as $addType) {
            $addField = [
                'id' => 'product_total_count',
                'type' => $addType,//类型
                'group_id' => 3,
                'base' => 1,
                'name' => '产品总数量',
                'order' => 0,
                'app_order' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'require' => 0,
                'edit_required' => 0,
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 1,
                'is_exportable' => 0,
                'is_editable' => 0,
                'is_list' => 0,
                'columns' => 'product_total_count',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '',
                'readonly' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ];
            array_push($system_fields, $addField);
        }

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            // 字段排在product_model之后
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }

            $this->syncSystem2Customer($system_fields, $clientId);
            $this->syncSystem2ExportSetting($system_fields, $clientId);
            $this->syncSystem2FieldGroup($system_fields, $clientId);
            echo '成功处理client_id：', $clientId, PHP_EOL;
        }

    }


    public function getCosWithTasTotalFieldConfig()
    {
        return [
            [
                'id' => 'cost_with_tax_total',
                'name' => '含税成本价总金额',
                'require' => 0,
                'hint' => '',
                'edit_hint' => 1,
                'group_id' => CustomFieldService::ORDER_GROUP_FEE,
                'edit_required' => 0,
                'is_exportable' => 0,
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可编辑，1可以，0否
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'default' => '',
                'columns' => 'cost_with_tax_total',
                'readonly' => '0',
                'type' => '2',
                'base' => '1',
                'is_editable' => 1,
                'ext_info' => '',
                'edit_default' => '0',
                'is_list' => 0,
                'export_scenario' => '2',
                'export_group' => FieldExportService::ORDER_GROUP_ADDITION_FEE,
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
            ]
        ];
    }

    /**
     * 销售订单 含税成本价总金额 cost_with_tax_total 系统字段 （全量后执行一次）
     */
    public function actionAddCostWithTaxTotalToOrderSystemField(){
        //同步到系统表
        $this->syncSystemFields(Constants::TYPE_ORDER, $this->getCosWithTasTotalFieldConfig());
    }

    /**
     * 销售订单 含税成本价总金额 客户表字段
     */
    public function actionAddCostWithTaxTotalToOrderCustomField($clientId = 0, $grey = 0, $greyNum = 0){
        $system_fields = $this->getCosWithTasTotalFieldConfig();

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            // 字段排在product_model之后
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }

            $this->syncSystem2Customer($system_fields, $clientId);
            $this->syncSystem2ExportSetting($system_fields, $clientId);
            $this->syncSystem2FieldGroup($system_fields, $clientId);
            echo '成功处理client_id：', $clientId, PHP_EOL;
        }

    }


    //【库存流水】增加显示字段「产品型号」字段 脚本
    public function actionAddModelToInventoryReportUserSetting($clientId = 0, $grey = 0, $greyNum = 0, $skipClientId = 0)
    {
        $addModelField = [
            "width" => 160,
            "id" => "model",
            "fixed" => 0
        ];
        $afterFields = [
            'model' => 'name',
        ];

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }
            // 字段排在product_model之后
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }

            $userSettingSql = "select * from tbl_user_setting where client_id={$clientId} and `key`='inventory.report.list.field'";
            $userSettingList = $db->createCommand($userSettingSql)->queryAll();

            foreach ($userSettingList as $userSettingItem) {
                $value = json_decode($userSettingItem['value'], true);
                $newValue = [];
                //判断有没有model ，防止重复跑导致数据错乱
                if (in_array('model', array_column($value, 'id'))) {
                    echo $userSettingItem['user_id']."：已存在【model】字段".PHP_EOL;
                    continue;
                }

                //排序
                foreach ($value as $item) {
                    if ($item['id'] == $afterFields['model']) {
                        $newValue[] = $item;
                        $newValue[] = $addModelField;
                    }else{
                        $newValue[] = $item;
                    }
                }

                $newValue = json_encode($newValue);

                $updateUserSettingSql = "update tbl_user_setting set value=:newValue  where user_id=:user_id and `key`=:keyword";
                $updateUserSettingArr = [
                    ':newValue' => $newValue,
                    ':user_id' => $userSettingItem['user_id'],
                    ':keyword' => 'inventory.report.list.field',
                ];
                $db->createCommand($updateUserSettingSql)->execute($updateUserSettingArr);

                echo '成功处理user_id：', $userSettingItem['user_id'], PHP_EOL;
            }

            echo '成功处理client_id：', $clientId, PHP_EOL;
        }
    }

    protected function getPackageRemarkFieldConfig()
    {
        return [
            [
                'id'         => 'product_package_remark',
                'type' => Constants::TYPE_ORDER,
                'group_id'   => '2',
                'base' => 1,
                'name'       => '产品包装说明',
                'order' => 0,
                'app_order' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_TEXTAREA,
                'require' => 0,
                'edit_required' => 1,
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可编辑，1可以，0否
                'default' => '',
                'edit_default' => 1,
                'hint' => '',
                'edit_hint' => 1,
                'is_exportable' => 1,
                'is_editable' => 1,
                'is_list' => 1,
                'columns' => 'product_package_remark',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '2',
                'export_group' => '6',
                'readonly' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]
        ];
    }

    /**
     * 销售订单 新增 【包装说明】 系统字段 （全量后执行一次）
     */
    public function actionAddPackageRemarkToOrderSystemField(){
        //同步到系统表
        $this->syncSystemFields(Constants::TYPE_ORDER, $this->getPackageRemarkFieldConfig());
    }

    /**
     *  销售订单 新增 【包装说明】 字段 客户表字段
     */
    public function actionAddPackageRemarkToOrderCustomField($clientId = 0, $grey = 0, $greyNum = 0,$skipClientId=0){
        $system_fields = $this->getPackageRemarkFieldConfig();

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId<$skipClientId) {
                continue;
            }
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }

            $this->syncSystem2Customer($system_fields, $clientId);
            $this->syncSystem2ExportSetting($system_fields, $clientId);
            $this->syncSystem2FieldGroup($system_fields, $clientId);
            echo '成功处理client_id：', $clientId, PHP_EOL;
        }

    }

    /**
     * 供应商 修改 【跟进人】user_id => user_ids 系统字段 （全量后执行一次）
     */
    public function actionUpdateUserIdsToSupplierSystemField(){
        $updateSql = "update tbl_system_field set id='user_ids',columns='user_ids' where type=23 and id='user_id'";
        $db = \Yii::app()->db;
        $result = $db->createCommand($updateSql)->execute();
        if ($result) {
            echo "更新系统表成功～";
        } else {
            echo "更新系统表失败！";
        }
    }

    /**
     *  供应商 新增 【跟进人】user_id => user_ids 字段 客户表字段
     */
    public function actionUpdateUserIdsToSupplierCustomField($skipSetId=0){
        $setList = $this->dbSetList(1, null);
        foreach ($setList as $item) {
            if ($item['set_id'] < $skipSetId) {
                continue;
            }
            echo "set_id:{$item['set_id']} 开始执行".PHP_EOL;
            $selectCustomFieldSql = "select count(1) from tbl_custom_field  where type=23 and id='user_id'";
            $updateCustomFieldSql = "update tbl_custom_field set id='user_ids',columns='[\"user_ids\"]' where type=23 and id='user_id'";
            $selectFieldGroupSql = "select count(1) from tbl_field_group where type=23 and id='user_id'";
            $updateFieldGroupSql = "update tbl_field_group set id='user_ids' where type=23 and id='user_id'";

            $db = \ProjectActiveRecord::getDbByDbSetId($item['set_id']);
            $selectCustomCount = $db->createCommand($selectCustomFieldSql)->queryScalar();
            self::info("CustomField需要更新的数据量:$selectCustomCount");
            $updateCustomFieldCount = $db->createCommand($updateCustomFieldSql)->execute();
            self::info("CustomField更新数量:$updateCustomFieldCount/$selectCustomCount");

            $selectFieldGroupCount = $db->createCommand($selectFieldGroupSql)->queryScalar();
            self::info("FieldGroup需要更新的数据量:$selectFieldGroupCount");
            $updateFieldGroupCount = $db->createCommand($updateFieldGroupSql)->execute();
            self::info("FieldGroup更新数量:$selectFieldGroupCount/$updateFieldGroupCount");

            //usersetting 修改
            $userSettingSql = "SELECT * FROM tbl_user_setting WHERE `key`='supplier.private.list.field'";
            $userSettingList = $db->createCommand($userSettingSql)->queryAll();
            foreach ($userSettingList as $settingItem) {
                //直接替换字符串
                if (strpos($settingItem['value'],'user_id') !== false) {
                    $valueStr = str_replace('user_id', 'user_ids', $settingItem['value']);
                    $updateUserSettingSql = "update tbl_user_setting set `value`='$valueStr' where user_id={$settingItem['user_id']} and client_id={$settingItem['client_id']} and `key`='supplier.private.list.field' ";
                    $updateUserSettingResult = $db->createCommand($updateUserSettingSql)->execute();
                    if ($updateUserSettingResult) {
                        self::info("{$settingItem['user_id']}user_setting更新成功");
                    }else{
                        self::info("{$settingItem['user_id']}user_setting更新失败");
                    }
                }

            }

            \ProjectActiveRecord::releaseDbBySetId($item['set_id']);
        }
    }

    protected function getProductTypeFieldConfig()
    {
        return [
            Constants::TYPE_ORDER => [
                'id'         => 'product_type',
                'type' => Constants::TYPE_ORDER,
                'group_id'   => CustomFieldService::ORDER_GROUP_PRODUCT,
                'base' => 1,
                'name'       => '产品类型',
                'order' => 0,
                'app_order' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'require' => 0,
                'edit_required' => 0,
                'disable_flag' => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 0,
                'is_exportable' => 0,
                'is_editable' => 0,
                'is_list' => 1,
                'columns' => 'product_type',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '',
                'readonly' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
            Constants::TYPE_QUOTATION => [
                'id'         => 'product_type',
                'type' => Constants::TYPE_QUOTATION,
                'group_id'   => CustomFieldService::QUOTATION_GROUP_PRODUCT,
                'base' => 1,
                'name'       => '产品类型',
                'order' => 0,
                'app_order' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'require' => 0,
                'edit_required' => 0,
                'disable_flag' => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 0,
                'is_exportable' => 0,
                'is_editable' => 0,
                'is_list' => 1,
                'columns' => 'product_type',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '',
                'readonly' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]
        ];
    }

    /**
     * 销售订单\报价单 新增 【产品类型】 系统字段 （全量后执行一次）
     */
    public function actionAddProductTypeToOrderSystemField(){
        //同步到系统表
        $fieldConfig = $this->getProductTypeFieldConfig();
        foreach ($fieldConfig as $type => $fieldInfo) {
            $this->syncSystemFields($type, [$fieldInfo]);
        }
    }

    /**
     *  销售订单\报价单 新增 【产品类型】 字段 客户表字段
     */
    public function actionAddProductTypeToOrderCustomField($clientId = 0, $grey = 0, $greyNum = 0,$skipClientId=0){
        $system_fields = $this->getProductTypeFieldConfig();

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId<$skipClientId) {
                continue;
            }
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }
            $this->syncSystem2Customer($system_fields, $clientId);
            $this->syncSystem2ExportSetting($system_fields, $clientId);
            $this->syncSystem2FieldGroup($system_fields, $clientId);
            echo '成功处理client_id：', $clientId, PHP_EOL;
        }
    }

    protected function getSubProductFieldConfig($type)
    {
        return [
            [
                'id' => 'is_sub_product',
                'type' =>$type,
                'group_id' => 2,
                'base' => 1,
                'name' => '是否为子产品',
                'order' => 0,
                'app_order' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'require' => 0,
                'edit_required' => 0,
                'disable_flag' => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 0,
                'is_exportable' => 0,
                'is_editable' => 0,
                'is_list' => 1,
                'columns' => 'is_sub_product',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '',
                'readonly' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'combine_product_id',
                'type' => $type,
                'group_id' => 2,
                'base' => 1,
                'name' => '组合产品product_id',
                'order' => 0,
                'app_order' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'require' => 0,
                'edit_required' => 0,
                'disable_flag' => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 0,
                'is_exportable' => 0,
                'is_editable' => 0,
                'is_list' => 1,
                'columns' => 'combine_product_id',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '',
                'readonly' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'combine_product_no',
                'type' => $type,
                'group_id' => 2,
                'base' => 1,
                'name' => '组合产品编号',
                'order' => 0,
                'app_order' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                'require' => 0,
                'edit_required' => 0,
                'disable_flag' => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 0,
                'is_exportable' => 0,
                'is_editable' => 0,
                'is_list' => 1,
                'columns' => 'combine_product_no',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '',
                'readonly' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]
        ];
    }

    /**
     * 采购订单 新增 【是否子产品、组合产品id、组合产品编号】 系统字段 （全量后执行一次）
     */
    public function actionAddSubProductToPurchaseOrderSystemField(){
        //同步到系统表
        $this->syncSystemFields(Constants::TYPE_PURCHASE_ORDER, $this->getSubProductFieldConfig(Constants::TYPE_PURCHASE_ORDER));
        $this->syncSystemFields(Constants::TYPE_ORDER, $this->getSubProductFieldConfig(Constants::TYPE_ORDER));
    }

    /**
     *  采购订单 新增 【是否子产品、组合产品id、组合产品编号】 字段 客户表字段
     */
    public function actionAddSubProductToPurchaseOrderCustomField($clientId = 0, $grey = 0, $greyNum = 0,$skipClientId=0){
        $order_system_fields = $this->getSubProductFieldConfig(Constants::TYPE_PURCHASE_ORDER);
        $purchase_system_fields = $this->getSubProductFieldConfig(Constants::TYPE_ORDER);
        $system_fields = array_merge($order_system_fields, $purchase_system_fields);

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId<$skipClientId) {
                continue;
            }
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }
            $this->syncSystem2Customer($system_fields, $clientId);
            $this->syncSystem2ExportSetting($system_fields, $clientId);
            $this->syncSystem2FieldGroup($system_fields, $clientId);
            echo '成功处理client_id：', $clientId, PHP_EOL;
        }
    }

    protected function getProductDisableFlagFieldConfig()   // $cfg包含type、group_id
    {
        $cfgs = [
            ['type'=>2, 'group_id'=>2],
            ['type'=>3, 'group_id'=>2],
            ['type'=>9, 'group_id'=>2],
            ['type'=>22, 'group_id'=>2],
            ['type'=>28, 'group_id'=>3],
            ['type'=>29, 'group_id'=>3],
            ['type'=>30, 'group_id'=>3],
            ['type'=>31, 'group_id'=>3],
            ['type'=>33, 'group_id'=>3],
        ];
        $template = [
                'id'         => 'product_disable_flag',
                'type' => 0,
                'group_id'   => 0,
                'base' => 1,
                'name'       => '产品是否停售',
                'order' => 0,
                'app_order' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'require' => 0,
                'edit_required' => 0,
                'disable_flag' => 1,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 0,
                'is_exportable' => 0,
                'is_editable' => 0,
                'is_list' => 1,
                'columns' => 'product_disable_flag',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '',
                'readonly' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ];

        $configs = [];
        foreach($cfgs as $cfg){
            $config = $template;
            $config['type'] = $cfg['type'];
            $config['group_id'] = $cfg['group_id'];
            $configs[$cfg['type']] = $config;
        }
        return $configs;
    }

    /**
     * 销售订单\报价单\采购单\销售出库单\采购入库单\其他出库单\商机\采购退货单\其他入库单 新增 【产品停售】 系统字段 （全量后执行一次）
     */
    public function actionAddProductDisableFlagSystemField(){
        //同步到系统表
        $fieldConfig = $this->getProductDisableFlagFieldConfig();
        foreach ($fieldConfig as $type => $fieldInfo) {
            $this->syncSystemFields($type, [$fieldInfo]);
        }
    }

    /**
     *  销售订单\报价单\采购单\销售出库单\采购入库单\其他出库单\商机\采购退货单\其他入库单 新增 【产品停售】 字段 客户表字段
     */
    public function actionAddProductDisableFlagCustomField($clientId = 0, $grey = 0, $greyNum = 0,$skipClientId=0){
        $system_fields = $this->getProductDisableFlagFieldConfig();

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId<$skipClientId) {
                continue;
            }
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }
            $this->syncSystem2Customer($system_fields, $clientId);
            $this->syncSystem2FieldGroup($system_fields, $clientId);
            echo '成功处理client_id：', $clientId, PHP_EOL;
        }
    }


    protected function getProductCombineInfoFieldConfig()
    {
        return [
            Constants::TYPE_PRODUCT => [
                'id'         => 'combine_info',
                'type' => Constants::TYPE_PRODUCT,
                'group_id'   => 1,
                'base' => 1,
                'name'       => '组合情况',
                'order' => 0,
                'app_order' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_OTHER,
                'require' => 0,
                'edit_required' => 1,
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可编辑，1可以，0否
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 1,
                'is_exportable' => 0,
                'is_editable' => 1,
                'is_list' => 0,
                'columns' => "combine_product_count,sub_product_count",
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '',
                'readonly' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]
        ];
    }

    /**
     *  产品 新增 【组合情况】 字段 系统字段 （全量后执行一次）
     */
    public function actionAddProductCombineInfoSystemField(){
        //同步到系统表
        $fieldConfig = $this->getProductCombineInfoFieldConfig();
        foreach ($fieldConfig as $type => $fieldInfo) {
            $this->syncSystemFields($type, [$fieldInfo]);
        }
    }

    /**
     *  产品 新增 【组合情况】 字段 客户表字段
     */
    public function actionAddProductCombineInfoCustomField($clientId = 0, $grey = 0, $greyNum = 0,$skipClientId=0){
        $system_fields = $this->getProductCombineInfoFieldConfig();

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId<$skipClientId) {
                continue;
            }
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }
            $this->syncSystem2Customer($system_fields, $clientId);
            $this->syncSystem2FieldGroup($system_fields, $clientId);
            echo '成功处理client_id：', $clientId, PHP_EOL;
        }
    }

    public function actionAddProductCombineInfoUserSetting($clientId = 0, $grey = 0, $greyNum = 0,$skipClientId=0){
        $productUserSettings = [['id'=>'combine_info','width'=>200,'fixed'=>0]];

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId<$skipClientId) {
                continue;
            }
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }
            $this->actionAddProductListFieldUserSetting($clientId, $productUserSettings);
            echo '成功处理client_id：', $clientId, PHP_EOL;
        }
    }

    /**
     *  产品 更改 【含税成本价】 字段（添加关联子产品成本字段） 客户表字段
     */
    public function actionEditCostWithTaxCustomField($clientId = 0, $grey = 0, $greyNum = 0,$skipClientId=0){
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId<$skipClientId) {
                continue;
            }
            $sql = "select client_id,id,type,columns from tbl_custom_field where `type`=1 and client_id={$clientId} and id='cost_with_tax' and enable_flag=1";

            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }

            $data = $db->createCommand($sql)->queryRow();
            if(!$data){
                echo "用户【{$clientId}】不存在字段【cost_with_tax】".PHP_EOL;
                continue;
            }
            $columns = json_decode($data['columns'], true);
            $update = ["cost_currency", "cost", "associate_cost_flag", "cost_type"];
            if(!$columns || !is_array($columns) || !empty(array_diff($update, $columns))){
                $columns = $update;
            }else{
                echo "用户【{$clientId}】的字段【cost_with_tax】已存在含税成本价字段完整，略过更新".PHP_EOL;
                continue;
            }

            $columnsStr = json_encode($columns);
            $editSql = "update tbl_custom_field set columns='{$columnsStr}' where  `type`=1 and client_id={$clientId} and id='cost_with_tax' and enable_flag=1";
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $rows = $db->createCommand($editSql)->execute();
            echo "成功处理client_id({$rows}行)：", $clientId, PHP_EOL;
        }
    }

    /**
     *  产品 更改 【含税成本价】 字段（添加关联子产品成本字段） 系统表字段 （全量后执行一次）
     */
    public function actionEditCostWithTaxSystemField(){
        //同步到系统表
        $db = \Yii::app()->db;
        $sql = "update tbl_system_field set columns='cost_currency,cost,associate_cost_flag,cost_type' where `type`=1 and id='cost_with_tax'";
        $rows = $db->createCommand($sql)->execute();
        if($rows){
            echo "更改系统表字段【cost_with_tax】成功\n";
        }else{
            echo "更改系统表字段【cost_with_tax】失败\n";
        }
    }

    //组合产品脚本聚合
    public function actionCombineProductCustomRun($clientId = 0, $grey = 0, $greyNum = 0,$skipClientId=0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $client_id) {
            if ($client_id < $skipClientId) {
                continue;
            }
            $this->actionAddProductTypeToOrderCustomField($client_id);
            $this->actionAddSubProductToPurchaseOrderCustomField($client_id);
            $this->actionAddProductCombineInfoCustomField($client_id);
            $this->actionAddProductDisableFlagCustomField($client_id);
            $this->actionEditCostWithTaxCustomField($client_id);
        }
    }

    //组合产品脚本聚合 -- 系统
    public function actionCombineProductSystemRun()
    {
        $this->actionAddProductTypeToOrderSystemField();
        $this->actionAddSubProductToPurchaseOrderSystemField();
        $this->actionAddProductCombineInfoSystemField();
        $this->actionAddProductDisableFlagSystemField();
        $this->actionEditCostWithTaxSystemField();
    }
    protected function getProductCNNameFieldConfig()
    {
        return [
            [
                'id'         => 'cn_name',
                'type' => Constants::TYPE_PRODUCT,
                'group_id'   => '1',
                'base' => 1,
                'name'       => '中文产品名称',
                'order' => 0,
                'app_order' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                'require' => 0,
                'edit_required' => 1,
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 1,// 是否可编辑，1可以，0否
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 1,
                'is_exportable' => 1,
                'is_editable' => 1,
                'is_list' => 0,
                'columns' => 'cn_name',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '2,3',
                'export_group' => '0',
                'readonly' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]
        ];
    }

    /**
     * 新增 产品中文名 字段
     */
    public function actionAddProductCNNameToProductCustomField($clientId = 0, $grey = 0, $greyNum = 0)
    {
        $system_fields = $this->getProductCNNameFieldConfig();

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $afterFields = [
            'cn_name' => 'name',
        ];

        foreach ($clientIds as $client_id) {
            // 字段排在product_model之后
            echo '---开始处理client_id：', $client_id, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($client_id);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }

            $this->syncSystem2Customer($system_fields, $client_id);
            //处理字段排序
            echo '--开始处理新字段顺序'.PHP_EOL;
            foreach ($system_fields as $k => $field) {
                $id = $field['id'];
                $type = $field['type'];
                $afterId = $afterFields[$id] ?? false;
                if (!$afterId) {
                    continue;
                }
                $sorter = new \common\library\util\sort\Sorter(new \common\library\util\sort\CustomFieldSortRepository($client_id, $type));
                $sorter->setResort(true);
                $sorter->setId($id, true);
                $sorter->setSortField('order');
                $orderNum = $sorter->after($afterId);
                $sorter->setSortField('app_order');
                $appOrderNum = $sorter->after($afterId);
                $system_fields[$k]['order'] = $orderNum;
                $system_fields[$k]['app_order'] = $appOrderNum;
            }
            $this->syncSystem2ExportSetting($system_fields, $client_id);
            $this->syncSystem2FieldGroup($system_fields, $client_id);
            echo '成功处理client_id：', $client_id, PHP_EOL;
        }


    }

    /**
     * 新增 产品中文名 系统字段 （全量后执行一次）
     */
    public function actionAddProductCNNameToProductSystemField(){
        //获取系统字段
        $systemFields = \Yii::app()->db->createCommand('SELECT * FROM tbl_system_field WHERE `type`=1 ORDER BY `order` DESC')->queryAll(true);

        $intoFields = $this->getProductCNNameFieldConfig();

        $newSystemFields = [];

        foreach ($systemFields as $field) {
            $newSystemFields[] = $field;
            if ($field['id'] == 'name') {
                foreach ($intoFields as $intoField) {
                    $newSystemFields[] = $intoField;
                }
            }
        }

        $conut = count($newSystemFields);
        foreach ($newSystemFields as &$newSystemField) {
            $newSystemField['order'] = $conut;
            $newSystemField['app_order'] = $conut;
            $conut--;
        }
        unset($newSystemField);
        //同步到系统表
        $this->syncSystemFields(Constants::TYPE_PRODUCT, $newSystemFields);
    }

    //产品显示列表 删除字段「最小起订数量」脚本
    public function actionRemoveMinToProductUserSetting($clientId = 0,$skipClientId=0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId < $skipClientId) {
                continue;
            }
            // 字段排在product_model之后
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }

            $userSettingSql = "select * from tbl_user_setting where client_id={$clientId} and `key`='product.list.field'";
            $userSettingList = $db->createCommand($userSettingSql)->queryAll();

            if (!$userSettingList) {
                continue;
            }
            foreach ($userSettingList as $userSettingItem) {
                $value = json_decode($userSettingItem['value'], true);
                $newValue = [];
                //判断有没有minimum_order_quantity ，防止重复跑
                if (!in_array('minimum_order_quantity', array_column($value, 'id'))) {
                    continue;
                }

                foreach ($value as $item) {
                    if ($item['id'] == 'minimum_order_quantity') {
                        continue;
                    } else {
                        $newValue[] = $item;
                    }
                }

                $newValue = json_encode($newValue);
                $updateUserSettingSql = "update tbl_user_setting set value=:newValue  where user_id=:user_id and `key`=:keyword";
                $updateUserSettingArr = [
                    ':newValue' => $newValue,
                    ':user_id' => $userSettingItem['user_id'],
                    ':keyword' => 'product.list.field',
                ];
                $db->createCommand($updateUserSettingSql)->execute($updateUserSettingArr);

                echo '成功处理user_id：', $userSettingItem['user_id'], PHP_EOL;
            }

            echo '***** 成功处理client_id：', $clientId, PHP_EOL;
        }
    }

    // 跟单协同迭代 本脚本用于新增入库、出库、采购、单据任务字段，并调整字段的展示顺序
    public function actionAddTransferInvoiceColumns($clientId=null, $grey = 0, $greyNum = null,$skipClientId=0){
        self::info("AddTransferInvoiceColumns start".PHP_EOL);
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $greyNum = explode(",", $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum, true, $skipClientId);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach($clientIds as $clientId){
//            if(isset(TransferIgnoreClient::$ignoreClientIds[$clientId])){
//                self::info('Ignore client：'.$clientId);
//                continue;
//            }
            try{
                $mysqlDb = \ProjectActiveRecord::getDbByClientId($clientId);
                $modules = [38,39,40];

// 移除本次迭代原本要新增，但后来产品决定弃用的字段
$dropClauseStr = '';
foreach(AddTransferInvoiceCustomFieldTask::dropNotUseNewField() as $dropType => $dropFieldIds){
    $idStr = "'".implode('\',\'', $dropFieldIds) . "'";
    $dropClauseStr .= " (type = {$dropType} and id in ({$idStr})) or";
}
$dropClauseStr = rtrim($dropClauseStr, 'or');
$deleteSql = "update tbl_custom_field set enable_flag=0,disable_flag=1 where client_id = {$clientId} and ({$dropClauseStr})";
$mysqlDb->createCommand($deleteSql)->execute();

                foreach($modules as $module){   //入库、出库、采购
                    $system_fields = AddTransferInvoiceCustomFieldTask::getAddTransferInvoiceConfig($module);
                    Helper::addField($clientId,$system_fields);

                    // 调整字段顺序
                    $orderSetting = AddTransferInvoiceCustomFieldTask::fieldOrderSetting($module);
                    Helper::batchUpdateFieldOrder($clientId, $module, $orderSetting);
                }

                // 订单、采购订单添加字段
                $referModules = [2,22];
                foreach($referModules as $module){
                    $system_fields = AddTransferInvoiceCustomFieldTask::getAddReferInvoiceFieldsConfig($module);
                    Helper::addField($clientId,$system_fields);
                }

                // 单据任务
                Helper::addField($clientId,AddTransferInvoiceCustomFieldTask::productTransferOtherFieldsSetting());
$orderSetting = AddTransferInvoiceCustomFieldTask::fieldOrderSetting(\Constants::TYPE_PRODUCT_TRANSFER_OTHER);
Helper::batchUpdateFieldOrder($clientId, \Constants::TYPE_PRODUCT_TRANSFER_OTHER, $orderSetting);

                // 修改任务数量名称/隐藏采购数量
                $editReachCountSql = "update tbl_custom_field set name=case when type=38 and id='reach_count' then '本次需采购数' when type=39 and id='reach_count' then '本次需入库数' when type=40 and id='reach_count' then '本次需出库数' when type=38 and id='expect_time' then '期望货好时间' else name end where client_id = {$clientId} and type in (38,39,40)  and id in ('reach_count','expect_time')";
                $hidePruchaseCountSql = "update tbl_custom_field set disable_flag = 1 where ((id = 'purchase_count' and type = 38) or (id = 'status' and type in (38,39,40))) and client_id = {$clientId}";
                $mysqlDb->createCommand($editReachCountSql)->execute();
                $mysqlDb->createCommand($hidePruchaseCountSql)->execute();
                self::info("AddTransferInvoiceColumns client_id 【{$clientId}】 success ".PHP_EOL);
            }catch(\Throwable $e){
                self::info('Error : '.$e->getMessage().' | File : '.$e->getFile(). ' | Line : '.$e->getLine() . ' | client_id : '.$clientId);
            }
        }

        self::info("AddTransferInvoiceColumns All Finished".PHP_EOL);
    }

    // 跟单协同迭代 本脚本用于新增全局入库、出库、采购、单据任务字段，并调整字段的展示顺序（全量时调用）
    public function actionAddTransferInvoiceSystemColumns(){
        try{
            self::info("AddTransferInvoiceSystemFieldTask start ".PHP_EOL);
            $modules = [38,39,40];
            foreach($modules as $module) {   //入库、出库、采购
                $settings = AddTransferInvoiceCustomFieldTask::getAddTransferInvoiceConfig($module);
                Helper::syncSystemFields($module, $settings);

                // 变更字段顺序
                $sorter = new \common\library\util\sort\Sorter(new \common\library\util\sort\SystemFieldSortRepository($module,
                    'order'));
                $sorter->setResort(true);
                $orderSettings = AddTransferInvoiceSystemFieldTask::fieldOrderSetting($module);
                foreach($orderSettings as $after=>$before){
                    $sorter->setId($after, true);
                    $sorter->setSortField('order');
                    $orderNum = $sorter->after($before);
                    $sorter->setSortField('app_order');
                    $appOrderNum = $sorter->after($before);
                }
            }

            $referModules = [2,22];
            foreach($referModules as $module){
                $settings = AddTransferInvoiceCustomFieldTask::getAddReferInvoiceFieldsConfig($module);
                Helper::syncSystemFields($module,$settings);
            }

            $otherTransferSettings = AddTransferInvoiceCustomFieldTask::productTransferOtherFieldsSetting();
            Helper::syncSystemFields(\Constants::TYPE_PRODUCT_TRANSFER_OTHER, $otherTransferSettings);

            // 修改任务数量名称/隐藏采购数量
            $editReachCountSql = "update tbl_system_field set name=case when type=38 and id='reach_count' then '本次需采购数' when type=39 and id='reach_count' then '本次需入库数' when type=40 and id='reach_count' then '本次需出库数' when type=38 and id='expect_time' then '期望货好时间' else name end where type in (38,39,40)  and id in ('reach_count','expect_time')";
            $hidePruchaseCountSql = "update tbl_system_field set disable_flag = 1 where (id = 'purchase_count' and type = 38) or (id = 'status' and type in (38,39,40))";
            $db =  \Yii::app()->getDb();
            $db->createCommand($editReachCountSql)->execute();
            $db->createCommand($hidePruchaseCountSql)->execute();
            self::info("AddTransferInvoiceSystemFieldTask success ".PHP_EOL);
        }catch(\Throwable $e){
            throw new \RuntimeException('Error : '.$e->getMessage().' | File : '.$e->getFile(). ' | Line : '.$e->getLine());
        }
    }

    /**
     * 获取 资金账户 字段配置
     * @return array[]
     */
    protected function getCapitalAccountFieldConfig()
    {
        return [
            [
                'id' => 'capital_account_id',
                'type' => Constants::TYPE_CASH_COLLECTION,
                'group_id'   => CustomFieldService::CASH_COLLECTION_GROUP_BASIC,
                'base' => 1,
                'name'       => '资金账户',
                'order' => 0,
                'app_order' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'edit_required' => 0,
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'require' => 0,
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 0,
                'is_exportable' => 1,
                'is_editable' => 1,
                'is_list' => 0,
                'columns' => 'capital_account_id',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group' => '0',
                'readonly' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]
        ];
    }

    /**
     * 新增 资金账户 字段
     * @param $clientId
     * @param $grey
     * @param $greyNum
     * @return void
     * @throws \ProcessException
     */
    public function actionAddCapitalAccountToProductCustomField($clientId = 0, $grey = 0, $greyNum = 0)
    {
        $system_fields = $this->getCapitalAccountFieldConfig();

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $client_id) {
            // 字段排在product_model之后
            echo '---开始处理client_id：', $client_id, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($client_id);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }

            $this->syncSystem2Customer($system_fields, $client_id);
            //处理字段排序
            $this->syncSystem2ExportSetting($system_fields, $client_id);
            $this->syncSystem2FieldGroup($system_fields, $client_id);
            echo '成功处理client_id：', $client_id, PHP_EOL;
        }
    }

    /**
     * 新增 资金账户 系统字段 （全量后执行一次）
     */
    public function actionAddCapitalAccountToSystemField(){
        //获取系统字段
        $systemFields = \Yii::app()->db->createCommand('SELECT * FROM tbl_system_field WHERE `type`=10 ORDER BY `order` DESC')->queryAll(true);

        $intoFields = $this->getCapitalAccountFieldConfig();

        $newSystemFields = $systemFields;

        foreach ($intoFields as $intoField) {
            $newSystemFields[] = $intoField;
        }

        $conut = count($newSystemFields);
        foreach ($newSystemFields as &$newSystemField) {
            $newSystemField['order'] = $conut;
            $newSystemField['app_order'] = $conut;
            $conut--;
        }
        unset($newSystemField);
        //同步到系统表
        $this->syncSystemFields(Constants::TYPE_CASH_COLLECTION, $newSystemFields);
    }

    /**
     * 获取 付款状态&付款金额 字段配置
     * @return array[]
     */
    protected function getPaymentFieldConfig()
    {
        return [
            [
                'id' => 'payment_status',
                'type' => Constants::TYPE_PURCHASE_ORDER,
                'group_id'   => CustomFieldService::PURCHASE_ORDER_GROUP_CHARGE,
                'base' => 1,
                'name'       => '付款状态',
                'field_type' => CustomFieldService::FIELD_TYPE_STATUS,
                'require' => 1,
                'edit_required' => 0,
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 0,
                'is_exportable' => 1,
                'is_editable' => 1,
                'is_list' => 0,
                'columns' => 'payment_status',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_BASIC,
                'order' => 0,
                'app_order' => 0,
                'readonly' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'payment_amount',
                'type' => Constants::TYPE_PURCHASE_ORDER,
                'group_id'   => CustomFieldService::PURCHASE_ORDER_GROUP_CHARGE,
                'base' => 1,
                'name'       => '已付款金额',
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'require' => 1,
                'edit_required' => 0,
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 0,
                'is_exportable' => 1,
                'is_editable' => 1,
                'is_list' => 0,
                'columns' => 'payment_amount',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_BASIC,
                'order' => 0,
                'app_order' => 0,
                'readonly' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'payment_wait_amount',
                'type' => Constants::TYPE_PURCHASE_ORDER,
                'group_id'   => CustomFieldService::PURCHASE_ORDER_GROUP_CHARGE,
                'base' => 1,
                'name'       => '待付款金额',
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'require' => 1,
                'edit_required' => 0,
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 0,
                'is_exportable' => 1,
                'is_editable' => 1,
                'is_list' => 0,
                'columns' => 'payment_wait_amount',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => Constants::TYPE_PURCHASE_ORDER,
                'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_BASIC,
                'order' => 0,
                'app_order' => 0,
                'readonly' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]
        ];
    }

    /**
     * 新增 付款状态&付款金额 字段配置 字段
     * @param $clientId
     * @param $grey
     * @param $greyNum
     * @return void
     * @throws \ProcessException
     */
    public function actionAddPaymentFieldToPurchaseOrderCustomField($clientId = 0, $grey = 0, $greyNum = 0)
    {
        $system_fields = $this->getPaymentFieldConfig();


        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $client_id) {
            // 字段排在product_model之后
            echo '---开始处理client_id：', $client_id, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($client_id);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }

            $this->syncSystem2Customer($system_fields, $client_id);
            //处理字段排序
            $this->syncSystem2ExportSetting($system_fields, $client_id);
            $this->syncSystem2FieldGroup($system_fields, $client_id);
            echo '成功处理client_id：', $client_id, PHP_EOL;
        }
    }

    /**
     * 新增 付款状态&付款金额 字段配置 系统字段 （全量后执行一次）
     */
    public function actionAddPaymentFieldToSystemField(){
        //获取系统字段
        $systemFields = \Yii::app()->db->createCommand('SELECT * FROM tbl_system_field WHERE `type`=22 ORDER BY `order` DESC')->queryAll(true);

        $intoFields = $this->getPaymentFieldConfig();

        $newSystemFields = $systemFields;

        foreach ($intoFields as $intoField) {
            $newSystemFields[] = $intoField;
        }

        $conut = count($newSystemFields);
        foreach ($newSystemFields as &$newSystemField) {
            $newSystemField['order'] = $conut;
            $newSystemField['app_order'] = $conut;
            $conut--;
        }
        unset($newSystemField);
        //同步到系统表
        $this->syncSystemFields(Constants::TYPE_PURCHASE_ORDER, $newSystemFields);
    }

    /**
     * 获取 供应商-付款信息 字段配置
     * @return array[]
     */
    protected function getSupplierBankInfoFieldConfig()
    {
        return [
            [
                'id'             => 'bank_name',
                'type'           => Constants::TYPE_SUPPLIER,
                'group_id'       => CustomFieldService::SUPPLIER_GROUP_PAYMENT,
                'base'           => 1,
                'name'           => '收款银行',
                'order'          => 1,
                'app_order'      => 1,
                'field_type'      => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required'  => 0,
                'edit_hide'      => 0,// 是否可编辑，1可以，0否
                'require'        => 0,
                'disable_flag'    => 0,//前端是否隐藏，1隐藏，0不隐藏
                'default'        => '',
                'edit_default'   => 0,
                'hint'           => '',
                'edit_hint'      => 0,
                'is_exportable'  => 1,
                'is_editable'    => 1,
                'is_list'        => 0,
                'columns'        => 'bank_name',
                'relation_type'  => 0,
                'relation_field'  => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group'    => '0',
                'readonly'        => 0,
                'create_time'     => date('Y-m-d H:i:s'),
                'update_time'     => date('Y-m-d H:i:s')
            ],
            [
                'id'            => 'account_name',
                'type'          => Constants::TYPE_SUPPLIER,
                'group_id'      => CustomFieldService::SUPPLIER_GROUP_PAYMENT,
                'base'          => 1,
                'name'          => '收款户名',
                'order'         => 2,
                'app_order'     => 2,
                'field_type'     => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required' => 0,
                'edit_hide'     => 0,// 是否可编辑，1可以，0否
                'require'       => 0,
                'disable_flag'   => 0,//前端是否隐藏，1隐藏，0不隐藏
                'default'       => '',
                'edit_default'  => 0,
                'hint'          => '',
                'edit_hint'     => 0,
                'is_exportable' => 1,
                'is_editable'   => 1,
                'is_list'       => 0,
                'columns'       => 'account_name',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group'    => '0',
                'readonly'        => 0,
                'create_time'     => date('Y-m-d H:i:s'),
                'update_time'     => date('Y-m-d H:i:s')
            ],
            [
                'id'            => 'bank_account',
                'type'          => Constants::TYPE_SUPPLIER,
                'group_id'      => CustomFieldService::SUPPLIER_GROUP_PAYMENT,
                'base'          => 1,
                'name'          => '收款账号',
                'order'         => 3,
                'app_order'     => 3,
                'field_type'     => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required' => 0,
                'edit_hide'     => 0,// 是否可编辑，1可以，0否
                'require'       => 0,
                'disable_flag'   => 0,//前端是否隐藏，1隐藏，0不隐藏
                'default'       => '',
                'edit_default'  => 0,
                'hint'          => '',
                'edit_hint'     => 0,
                'is_exportable' => 1,
                'is_editable'   => 1,
                'is_list'       => 0,
                'columns'       => 'bank_account',
                'relation_type' => 0,
                'relation_field'      => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group'    => '0',
                'readonly'        => 0,
                'create_time'     => date('Y-m-d H:i:s'),
                'update_time'     => date('Y-m-d H:i:s')
            ],
            [
                'id'            => 'is_main_flag',
                'type'          => Constants::TYPE_SUPPLIER,
                'group_id'      => CustomFieldService::SUPPLIER_GROUP_PAYMENT,
                'base'          => 1,
                'name'          => '主资金账户标识',
                'order'         => 0,
                'app_order'     => 0,
                'field_type'     => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required' => 0,
                'edit_hide'     => 0,// 是否可编辑，1可以，0否
                'require'       => 0,
                'disable_flag'   => 1,//前端是否隐藏，1隐藏，0不隐藏
                'default'       => '',
                'edit_default'  => 0,
                'hint'          => '',
                'edit_hint'     => 0,
                'is_exportable' => 0,
                'is_editable'   => 1,
                'is_list'       => 0,
                'columns'       => 'is_main_flag',
                'relation_type' => 0,
                'relation_field'      => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',
                'export_group'    => '0',
                'readonly'        => 0,
                'create_time'     => date('Y-m-d H:i:s'),
                'update_time'     => date('Y-m-d H:i:s')
            ]
        ];
    }

    /**
     * 新增 供应商-付款信息 字段
     * @param $clientId
     * @param $grey
     * @param $greyNum
     * @return void
     * @throws \ProcessException
     */
    public function actionAddBankInfoFieldToSupplierCustomField($clientId = 0, $grey = 0, $greyNum = 0)
    {
        $system_fields = $this->getSupplierBankInfoFieldConfig();


        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $client_id) {
            // 字段排在product_model之后
            echo '---开始处理client_id：', $client_id, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($client_id);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }

            $this->syncSystem2Customer($system_fields, $client_id);
            //处理字段排序
            $this->syncSystem2ExportSetting($system_fields, $client_id);
            $this->syncSystem2FieldGroup($system_fields, $client_id);
            echo '成功处理client_id：', $client_id, PHP_EOL;
        }
    }

    /**
     * 新增 供应商-付款信息 系统字段（全量后执行一次）
     */
    public function actionAddSupplierBankInfoToSystemField(){
        //获取系统字段
        $systemFields = \Yii::app()->db->createCommand('SELECT * FROM tbl_system_field WHERE `type`= ' . Constants::TYPE_SUPPLIER . ' ORDER BY `order` DESC')->queryAll(true);

        $intoFields = $this->getSupplierBankInfoFieldConfig();

        $newSystemFields = $systemFields;

        foreach ($intoFields as $intoField) {
            $newSystemFields[] = $intoField;
        }

        $conut = count($newSystemFields);
        foreach ($newSystemFields as &$newSystemField) {
            $newSystemField['order'] = $conut;
            $newSystemField['app_order'] = $conut;
            $conut--;
        }
        unset($newSystemField);
        //同步到系统表
        $this->syncSystemFields(Constants::TYPE_SUPPLIER, $newSystemFields);
    }

    /**
     * 隐藏指定字段
     * @param $field_id
     * @param $type
     * @param $clientId
     * @param $grey
     * @param $greyNum
     * @return void
     * @throws \ProcessException
     */
    public function actionHideField($field_id, $type, $clientId = 0, $grey = 0, $greyNum = 0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            echo "start $clientId \n";
            \ProjectActiveRecord::setConnection(\ProjectActiveRecord::getDbByClientId($clientId));
            \common\library\custom_field\CustomFieldService::hideField($clientId, $type, $field_id);
            $privilegeField = new \common\library\privilege_v3\PrivilegeField($clientId);
            $privilegeField->flushCache();
            \ProjectActiveRecord::releaseDbByClientId($clientId);
            echo  "affected client: [$clientId] hide field: [$field_id]" . PHP_EOL;
        }

        self::info("all done!!!");
    }

    public function actionPaymentInvoiceCustomField($clientId = 0, $grey = 0, $greyNum = 0)
    {
        self::info("开始执行付款单字段到客户表！！！");

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $greyNum = explode(",", $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $system_fields = $this->paymentInvoiceFieldsSetting();

        foreach ($clientIds as $client_id) {
            echo "开始执行client_id:" . $client_id . PHP_EOL;

            $this->syncSystem2Customer($system_fields, $client_id);
            $this->syncSystem2ExportSetting($system_fields, $client_id);
            $this->syncSystem2FieldGroup($system_fields, $client_id);

            $privilegeField = new \common\library\privilege_v3\PrivilegeField($client_id);
            $privilegeField->flushCache();
        }
        self::info("新增付款单字段到客户表执行完成！！！");
    }

    //采购付款-灰度回退，执行脚本 $recover 灰度正常recover=1
    public function actionPurchasePaymentFieldRollback($clientId = 0, $grey = 0, $greyNum = 0,$recover = 0)
    {
//        10-capital_account_id
//        22-payment_status
//        22-payment_amount
//        22-payment_wait_amount
//        23-bank_name
//        23-account_name
//        23-bank_account
//        23-is_main_flag
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $client_id) {

            echo "开始执行client_id:".$client_id.PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($client_id);

            $sql = "update tbl_custom_field set enable_flag=:enable_flag 
   where client_id =:client_id and ((type = 10 and id = 'capital_account_id')
   or (type = 22 and id in ('payment_status', 'payment_amount', 'payment_wait_amount'))
   or (type = 23 and id in ('bank_name', 'account_name', 'bank_account', 'is_main_flag')))";

            $parmas = [
                ':enable_flag' => $recover,
                ':client_id' => $client_id
            ];

            $updateCount = $db->createCommand($sql)->execute($parmas);
            echo "影响条数:".$updateCount.PHP_EOL;
            \ProjectActiveRecord::releaseDbByClientId($client_id);
        }

        echo "success".PHP_EOL;

    }

    /**
     * 修改系统字段，目的生成单据模版导出字典字段
     * @return void
     * @throws \ProcessException
     */
    public function actionUpdateInvoiceSystemField()
    {
        $updateTime = "'" . date('Y-m-d H:i:s') . "'";
        try {
            $types = [
                Constants::TYPE_PURCHASE_INBOUND_INVOICE,
                Constants::TYPE_OTHER_INBOUND_INVOICE,
                Constants::TYPE_SALE_OUTBOUND_INVOICE,
                Constants::TYPE_OTHER_OUTBOUND_INVOICE,
                Constants::TYPE_PURCHASE_RETURN_INVOICE,
            ];
            foreach ($types as $type) {
                //基本信息
                $updBaseFieldSql = "update tbl_system_field set export_scenario='" . $type . "' , export_group=1, is_exportable=1, update_time={$updateTime}
   where type =" . $type . " and group_id=1";
                $baseFieldQueryCount = \Yii::app()->db->createCommand($updBaseFieldSql)->execute();
                \LogUtil::info('修改系统字段单据导出字段字典, [type=' . $type . '] 基本信息字段，成功条数：' . $baseFieldQueryCount);

                //出库/入库明细
                $updDetailFieldSql = "update tbl_system_field set export_scenario='" . $type . "' , export_group=6,  is_exportable=1, update_time={$updateTime}
   where type =" . $type . " and group_id=3  and disable_flag=0 and id not in ('have_outbound_count', 'for_outbound_count', 'wait_inbound_count')";
                $detailFieldQueryCount = \Yii::app()->db->createCommand($updDetailFieldSql)->execute();
                \LogUtil::info('修改系统字段单据导出字段字典, [type=' . $type . '] 明细字段，成功条数：' . $detailFieldQueryCount);
            }
        }catch (\Exception $e) {
            \LogUtil::error('修改系统字段单据导出字段字典出错 error:'.$e->getMessage());
            throw  new \RuntimeException('修改系统字段单据导出字段字典出错 error:'.$e->getMessage());
        }


        //供应商信息
        try {
            $supplierSystemFields = \Yii::app()->db->createCommand('SELECT * FROM tbl_system_field WHERE `type`= ' . Constants::TYPE_SUPPLIER . ' and export_scenario like "%22%"')->queryAll(true);
            foreach($supplierSystemFields as $supplierSystemField){
                $exportScenarioArr = explode(',', $supplierSystemField['export_scenario']);
                $needAddExportScenario = array_diff([Constants::TYPE_PURCHASE_INBOUND_INVOICE, Constants::TYPE_PURCHASE_RETURN_INVOICE], $exportScenarioArr);
                if(empty($needAddExportScenario)){
                    continue;
                }
                $updExportScenarioStr = implode(',', array_merge($exportScenarioArr, $needAddExportScenario));

                $updSupplierFieldSql = "update tbl_system_field set export_scenario='".$updExportScenarioStr."', update_time={$updateTime}
   where type =".$supplierSystemField['type']. " and id='". $supplierSystemField['id'] ."' and export_scenario like '%22%'";
                \Yii::app()->db->createCommand($updSupplierFieldSql)->execute();
                \LogUtil::info('修改系统字段单据导出字段字典, [type='.$supplierSystemField['type'].']类型'.$supplierSystemField['id'] .' 字段export_scenario='. $updExportScenarioStr);
            }
        } catch (\Exception $e) {
            \LogUtil::error('修改系统字段单据导出字段字典,供应商类型字段出错 error:'.$e->getMessage());
            throw  new \RuntimeException('修改系统字段单据导出字段字典,供应商类型字段出错 error:'.$e->getMessage());
        }

        //客户信息
        try {
            $companyTypeStr = implode(',', [Constants::TYPE_COMPANY, Constants::TYPE_CUSTOMER]);
            $companySystemFields = \Yii::app()->db->createCommand('SELECT * FROM tbl_system_field WHERE `type` in (' . $companyTypeStr . ') and export_scenario like "%2%"')->queryAll(true);
            foreach($companySystemFields as $companySystemField){
                $companyExportScenarioArr = explode(',', $companySystemField['export_scenario']);
                $companyNeedAddExportScenario = array_diff([Constants::TYPE_SALE_OUTBOUND_INVOICE], $companyExportScenarioArr);
                if(empty($companyNeedAddExportScenario)){
                    continue;
                }
                $updCompanyExportScenarioStr = implode(',', array_merge($companyExportScenarioArr, $companyNeedAddExportScenario));

                $updCompanyFieldSql = "update tbl_system_field set export_scenario='".$updCompanyExportScenarioStr."', update_time={$updateTime}
   where type =".$companySystemField['type']. " and id='". $companySystemField['id'] ."' and export_scenario like '%2%'";
                \Yii::app()->db->createCommand($updCompanyFieldSql)->execute();
                \LogUtil::info('修改系统字段单据导出字段字典, [type='.$companySystemField['type'].']类型'.$companySystemField['id'] .' 字段export_scenario='. $updCompanyExportScenarioStr);
            }
        } catch (\Exception $e) {
            \LogUtil::error('修改系统字段单据导出字段字典,客户类型字段出错 error:'.$e->getMessage());
            throw  new \RuntimeException('修改系统字段单据导出字段字典,客户类型字段出错 error:'.$e->getMessage());
        }

        //产品单页
        try {
            //销售出库、其他出库的产品单页字段 与 销售订单 相同
            $productSystemFields = \Yii::app()->db->createCommand('SELECT * FROM tbl_system_field WHERE `type`= ' . Constants::TYPE_PRODUCT . ' and export_scenario like "%2%"')->queryAll(true);
            foreach($productSystemFields as $productSystemField){
                $productExportScenarioArr = explode(',', $productSystemField['export_scenario']);
                $productNeedAddExportScenario = array_diff([Constants::TYPE_SALE_OUTBOUND_INVOICE, Constants::TYPE_OTHER_OUTBOUND_INVOICE], $productExportScenarioArr);
                if(empty($productNeedAddExportScenario)){
                    continue;
                }
                $updProductExportScenarioStr = implode(',', array_merge($productExportScenarioArr, $productNeedAddExportScenario));

                $updProductFieldSql = "update tbl_system_field set export_scenario='".$updProductExportScenarioStr."', update_time={$updateTime}
   where type =".$productSystemField['type']. " and id='". $productSystemField['id'] ."' and export_scenario like '%2%'";
                \Yii::app()->db->createCommand($updProductFieldSql)->execute();
                \LogUtil::info('修改系统字段单据导出字段字典, [type='.$productSystemField['type'].']类型'.$productSystemField['id'] .' 字段export_scenario='. $updProductExportScenarioStr);
            }

            //采购入库、其他入库、采购退款的产品单页字段 与 采购订单 相同
            $purchaseProductSystemFields = \Yii::app()->db->createCommand('SELECT * FROM tbl_system_field WHERE `type`= ' . Constants::TYPE_PRODUCT . ' and export_scenario like "%22%"')->queryAll(true);
            foreach($purchaseProductSystemFields as $purchaseProductSystemField){
                $purchaseProductExportScenarioArr = explode(',', $purchaseProductSystemField['export_scenario']);
                $purchaseProductNeedAddExportScenario = array_diff([Constants::TYPE_PURCHASE_INBOUND_INVOICE, Constants::TYPE_OTHER_INBOUND_INVOICE, Constants::TYPE_PURCHASE_RETURN_INVOICE], $purchaseProductExportScenarioArr);
                if(empty($purchaseProductNeedAddExportScenario)){
                    continue;
                }
                $updPurchaseProductExportScenarioStr = implode(',', array_merge($purchaseProductExportScenarioArr, $purchaseProductNeedAddExportScenario));

                $updPurchaseProductFieldSql = "update tbl_system_field set export_scenario='".$updPurchaseProductExportScenarioStr."', update_time={$updateTime}
   where type =".$purchaseProductSystemField['type']. " and id='". $purchaseProductSystemField['id'] ."' and export_scenario like '%22%'";
                \Yii::app()->db->createCommand($updPurchaseProductFieldSql)->execute();
                \LogUtil::info('修改系统字段单据导出字段字典, [type='.$purchaseProductSystemField['type'].']类型'.$purchaseProductSystemField['id'] .' 字段export_scenario='. $updPurchaseProductExportScenarioStr);
            }

        } catch (\Exception $e) {
            \LogUtil::error('修改系统字段单据导出字段字典,产品单页类型字段出错 error:'.$e->getMessage());
            throw  new \RuntimeException('修改系统字段单据导出字段字典,产品单页类型字段出错 error:'.$e->getMessage());
        }
        echo "success";
    }

    /**
     * 生成单据模版导出字典字段
     */
    public function actionSyncInvoiceTemplateExportField($clientId, $grey = 1, $greyNum = 0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey && !is_null($greyNum)) {
            $greyNum = explode(',', $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        foreach ($clientIds as $clientId) {
            try {
                (new InvoiceExportTemplateHelper)->syncInvoiceTemplateExportField($clientId);
            } catch (\Throwable $throwable) {
                echo 'client_id' . $clientId . 'message' . $throwable->getMessage();
                \LogUtil::error('client_id' . $clientId . 'message' . $throwable->getMessage());
            }
        }
    }

    /**
     * 获取 销售订单-银行信息 字段配置
     * @return array[]
     */
    protected function getOrderBankInfoFieldConfig()
    {
        return [
            [
                'id'             => 'capital_account_id',
                'type'           => Constants::TYPE_ORDER,
                'group_id'       => CustomFieldService::ORDER_GROUP_BANK,
                'base'           => 1,
                'name'           => '资金账户',
                'order'          => 6,
                'app_order'      => 6,
                'field_type'      => CustomFieldService::FIELD_TYPE_NUMBER,
                'edit_required'  => 1,
                'edit_hide'      => 1,// 是否可编辑，1可以，0否
                'require'        => 0,
                'disable_flag'    => 0,//前端是否隐藏，1隐藏，0不隐藏
                'default'        => '',
                'edit_default'   => 0,
                'hint'           => '',
                'edit_hint'      => 0,
                'is_exportable'  => 1,
                'is_editable'    => 1,
                'is_list'        => 0,
                'columns'        => 'capital_account_id',
                'relation_type'  => 0,
                'relation_field'  => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '2',
                'export_group'    => FieldExportService::ORDER_GROUP_BANK_INFO,
                'readonly'        => 0,
                'create_time'     => date('Y-m-d H:i:s'),
                'update_time'     => date('Y-m-d H:i:s')
            ],
            [
                'id'            => 'capital_name',
                'type'           => Constants::TYPE_ORDER,
                'group_id'       => CustomFieldService::ORDER_GROUP_BANK,
                'base'          => 1,
                'name'          => '开户名',
                'order'         => 5,
                'app_order'     => 5,
                'field_type'     => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required' => 0,
                'edit_hide'     => 1,// 是否可编辑，1可以，0否
                'require'       => 0,
                'disable_flag'   => 0,//前端是否隐藏，1隐藏，0不隐藏
                'default'       => '',
                'edit_default'  => 0,
                'hint'          => '',
                'edit_hint'     => 0,
                'is_exportable' => 1,
                'is_editable'   => 1,
                'is_list'       => 0,
                'columns'       => 'capital_name',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '2',
                'export_group'    => FieldExportService::ORDER_GROUP_BANK_INFO,
                'readonly'        => 0,
                'create_time'     => date('Y-m-d H:i:s'),
                'update_time'     => date('Y-m-d H:i:s')
            ],
            [
                'id'            => 'capital_bank',
                'type'           => Constants::TYPE_ORDER,
                'group_id'       => CustomFieldService::ORDER_GROUP_BANK,
                'base'          => 1,
                'name'          => '开户行',
                'order'         => 4,
                'app_order'     => 4,
                'field_type'     => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required' => 0,
                'edit_hide'     => 1,// 是否可编辑，1可以，0否
                'require'       => 0,
                'disable_flag'   => 0,//前端是否隐藏，1隐藏，0不隐藏
                'default'       => '',
                'edit_default'  => 0,
                'hint'          => '',
                'edit_hint'     => 0,
                'is_exportable' => 1,
                'is_editable'   => 1,
                'is_list'       => 0,
                'columns'       => 'capital_bank',
                'relation_type' => 0,
                'relation_field'      => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '2',
                'export_group'    => FieldExportService::ORDER_GROUP_BANK_INFO,
                'readonly'        => 0,
                'create_time'     => date('Y-m-d H:i:s'),
                'update_time'     => date('Y-m-d H:i:s')
            ],
            [
                'id'            => 'bank_account',
                'type'           => Constants::TYPE_ORDER,
                'group_id'       => CustomFieldService::ORDER_GROUP_BANK,
                'base'          => 1,
                'name'          => '银行账号',
                'order'         => 3,
                'app_order'     => 3,
                'field_type'     => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required' => 0,
                'edit_hide'     => 1,// 是否可编辑，1可以，0否
                'require'       => 0,
                'disable_flag'   => 0,//前端是否隐藏，1隐藏，0不隐藏
                'default'       => '',
                'edit_default'  => 0,
                'hint'          => '',
                'edit_hint'     => 0,
                'is_exportable' => 1,
                'is_editable'   => 1,
                'is_list'       => 0,
                'columns'       => 'bank_account',
                'relation_type' => 0,
                'relation_field'      => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '2',
                'export_group'    => FieldExportService::ORDER_GROUP_BANK_INFO,
                'readonly'        => 0,
                'create_time'     => date('Y-m-d H:i:s'),
                'update_time'     => date('Y-m-d H:i:s')
            ],
            [
                'id'            => 'capital_account_address',
                'type'           => Constants::TYPE_ORDER,
                'group_id'       => CustomFieldService::ORDER_GROUP_BANK,
                'base'          => 1,
                'name'          => '开户行地址',
                'order'         => 2,
                'app_order'     => 2,
                'field_type'     => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required' => 0,
                'edit_hide'     => 1,// 是否可编辑，1可以，0否
                'require'       => 0,
                'disable_flag'   => 0,//前端是否隐藏，1隐藏，0不隐藏
                'default'       => '',
                'edit_default'  => 0,
                'hint'          => '',
                'edit_hint'     => 0,
                'is_exportable' => 1,
                'is_editable'   => 1,
                'is_list'       => 0,
                'columns'       => 'capital_account_address',
                'relation_type' => 0,
                'relation_field'      => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '2',
                'export_group'    => FieldExportService::ORDER_GROUP_BANK_INFO,
                'readonly'        => 0,
                'create_time'     => date('Y-m-d H:i:s'),
                'update_time'     => date('Y-m-d H:i:s')
            ],
            [
                'id'            => 'capital_account_remark',
                'type'           => Constants::TYPE_ORDER,
                'group_id'       => CustomFieldService::ORDER_GROUP_BANK,
                'base'          => 1,
                'name'          => '更多信息',
                'order'         => 1,
                'app_order'     => 1,
                'field_type'     => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required' => 0,
                'edit_hide'     => 1,// 是否可编辑，1可以，0否
                'require'       => 0,
                'disable_flag'   => 0,//前端是否隐藏，1隐藏，0不隐藏
                'default'       => '',
                'edit_default'  => 0,
                'hint'          => '',
                'edit_hint'     => 0,
                'is_exportable' => 1,
                'is_editable'   => 1,
                'is_list'       => 0,
                'columns'       => 'capital_account_remark',
                'relation_type' => 0,
                'relation_field'      => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '2',
                'export_group'    => FieldExportService::ORDER_GROUP_BANK_INFO,
                'readonly'        => 0,
                'create_time'     => date('Y-m-d H:i:s'),
                'update_time'     => date('Y-m-d H:i:s')
            ]

        ];
    }

    /**
     * 销售订单 新增 【银行信息】 系统字段 （全量后执行一次）
     */
    public function actionAddBankInfoToOrderSystemField(){
        //同步到系统表
        $fieldConfig = $this->getOrderBankInfoFieldConfig();
        $this->syncSystemFields(\Constants::TYPE_ORDER, $fieldConfig);
    }

    /**
     *  销售订单 新增 【银行信息】 字段 客户表字段
     */
    public function actionAddBankInfoToOrderCustomField($clientId = 0, $grey = 0, $greyNum = 0,$skipClientId=0){
        $system_fields = $this->getOrderBankInfoFieldConfig();

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId<$skipClientId) {
                continue;
            }
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }
            $this->syncSystem2Customer($system_fields, $clientId);
            $this->syncSystem2ExportSetting($system_fields, $clientId);
            $this->syncSystem2FieldGroup($system_fields, $clientId);
            echo '成功处理client_id：', $clientId, PHP_EOL;
        }
    }

    /**
     * 销售订单 `银行信息`字段 关闭 系统字段 （全量后执行一次）
     */
    public function actionUpdateBankInfoToOrderSystemField() {
        $updateSql = "update tbl_system_field set disable_flag=1 where type=2 and id='bank_info'";
        $db = \Yii::app()->db;
        $result = $db->createCommand($updateSql)->execute();
        if ($result) {
            echo "更新系统表成功～";
        } else {
            echo "更新系统表失败！";
        }
    }

   /**
    * 获取 款项id 字段配置
    * @return array[]
    */
   protected function getCostItemRelationIdFieldConfig()
   {
       $addTypes = [
           Constants::TYPE_ORDER,
           Constants::TYPE_QUOTATION,
           Constants::TYPE_PURCHASE_ORDER,
       ];



       $template = [
           'id'             => 'cost_item_relation_id',
//           'type'           => $addType,
           'group_id'       => 3, //添加的单据费用都是3
           'base'           => 1,
           'name'           => '费用',
           'order'          => 6,
           'app_order'      => 6,
           'field_type'      => CustomFieldService::FIELD_TYPE_NUMBER,
           'edit_required'  => 1,
           'edit_hide'      => 0,// 是否可编辑，1可以，0否
           'require'        => 0,
           'disable_flag'    => 0,//前端是否隐藏，1隐藏，0不隐藏
           'default'        => '',
           'edit_default'   => 0,
           'hint'           => '',
           'edit_hint'      => 1,
           'is_exportable'  => 1,
           'is_editable'    => 0,
           'is_list'        => 1,
           'columns'        => 'cost_item_relation_id',
           'relation_type'  => 0,
           'relation_field'  => '',
           'relation_field_type' => 0,
           'relation_field_name' => '',
//           'export_scenario' => $addType,
           'export_group'    => 8, //添加的单据费用导出分组都是8
           'readonly'        => 0,
           'create_time'     => date('Y-m-d H:i:s'),
           'update_time'     => date('Y-m-d H:i:s')
       ];

       $configs = [];
       foreach($addTypes as $addType){
           $config = $template;
           $config['type'] = $addType;
           $config['export_scenario'] = $addType;
           $configs[$addType] = $config;
       }
       return $configs;
   }

   /**
    * 销售订单\报价单\采购订单 新增 【款项id】 系统字段 （全量后执行一次）
    */
   public function actionAddCostItemRelationIdSystemField(){
       //同步到系统表
       $fieldConfig = $this->getCostItemRelationIdFieldConfig();
       foreach ($fieldConfig as $type => $fieldInfo) {
           $this->syncSystemFields($type, [$fieldInfo]);
       }
   }

   /**
    *  销售订单\报价单\采购订单 新增 【款项id】  客户表字段
    */
   public function actionAddCostItemRelationIdCustomField($clientId = 0, $grey = 0, $greyNum = 0,$skipClientId=0){
       $system_fields = $this->getCostItemRelationIdFieldConfig();

       if ($clientId) {
           $clientIds = explode(',', $clientId);
       } elseif ($grey) {
           $clientIds = $this->getGreyClientIds($greyNum);
       } else {
           $clientIds = array_column($this->getClientList(), 'client_id');
       }

       foreach ($clientIds as $clientId) {
           if ($clientId<$skipClientId) {
               continue;
           }
           echo '---开始处理client_id：', $clientId, PHP_EOL;
           $db = \ProjectActiveRecord::getDbByClientId($clientId);
           if (!$db) {
               echo '--此client无效..'.PHP_EOL;
               continue;
           }
           $this->syncSystem2Customer($system_fields, $clientId);
           $this->syncSystem2ExportSetting($system_fields, $clientId);
           $this->syncSystem2FieldGroup($system_fields, $clientId);
           echo '成功处理client_id：', $clientId, PHP_EOL;
       }
   }

    /**
     * 获取 费用备注 字段配置
     * @return array[]
     */
    protected function getCostRemarkFieldConfig()
    {
        $addTypes = [
            Constants::TYPE_ORDER,
            Constants::TYPE_QUOTATION,
            Constants::TYPE_PURCHASE_ORDER,
        ];



        $template = [
            'id'             => 'cost_remark',
//           'type'           => $addType,
            'group_id'       => 3, //添加的单据费用都是3
            'base'           => 1,
            'name'           => '费用备注',
            'order'          => 0,
            'app_order'      => 0,
            'field_type'      => CustomFieldService::FIELD_TYPE_TEXT,
            'edit_required'  => 1,
            'edit_hide'      => 0,// 是否可编辑，1可以，0否
            'require'        => 0,
            'disable_flag'    => 0,//前端是否隐藏，1隐藏，0不隐藏
            'default'        => '',
            'edit_default'   => 0,
            'hint'           => '',
            'edit_hint'      => 1,
            'is_exportable'  => 1,
            'is_editable'    => 0,
            'is_list'        => 1,
            'columns'        => 'cost_remark',
            'relation_type'  => 0,
            'relation_field'  => '',
            'relation_field_type' => 0,
            'relation_field_name' => '',
            'export_scenario' => 0,
            'export_group'    => 8, //添加的单据费用导出分组都是8
            'readonly'        => 0,
            'create_time'     => date('Y-m-d H:i:s'),
            'update_time'     => date('Y-m-d H:i:s')
        ];

        $configs = [];
        foreach($addTypes as $addType){
            $config = $template;
            $config['type'] = $addType;
            $config['export_scenario'] = $addType;
            $configs[$addType] = $config;
        }
        return $configs;
    }

    /**
     * 销售订单\报价单\采购订单 新增 【费用备注】 系统字段 （全量后执行一次）
     */
    public function actionAddCostRemarkSystemField(){
        //同步到系统表
        $fieldConfig = $this->getCostRemarkFieldConfig();
        foreach ($fieldConfig as $type => $fieldInfo) {
            $this->syncSystemFields($type, [$fieldInfo]);
        }
    }

    /**
     *  销售订单\报价单\采购订单 新增 【费用备注】  客户表字段
     */
    public function actionAddCostRemarkCustomField($clientId = 0, $grey = 0, $greyNum = 0,$skipClientId=0){
        $system_fields = $this->getCostRemarkFieldConfig();

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId<$skipClientId) {
                continue;
            }
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }
            $this->syncSystem2Customer($system_fields, $clientId);
            $this->syncSystem2ExportSetting($system_fields, $clientId);
            $this->syncSystem2FieldGroup($system_fields, $clientId);
            echo '成功处理client_id：', $clientId, PHP_EOL;
        }
    }

    /**
     * 采购订单【销售订单编号】order_no 字段配置
     * @return array[]
     */
    public function getOrderNoFieldConfig()
    {
        return [
            Constants::TYPE_PURCHASE_ORDER => [
                'id' => 'order_no',
                'type' => Constants::TYPE_PURCHASE_ORDER,
                'group_id' => CustomFieldService::ORDER_GROUP_PRODUCT,
                'base' => 1,
                'name' => '销售订单编号',
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                'require' => 0,
                'edit_required' => 0,
                'disable_flag' => 0,
                'edit_hide' => 0,
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 1,
                'is_exportable' => 1,
                'is_editable' => 0,
                'is_list' => 1,
                'columns' => 'order_no',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => 22,
                'export_group' => 5,
                'readonly' => 0,
                'ext_info' => '',
                'unique_check' => 0,
                'unique_prevent' => 1,
                'unique_message' => '',
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
            ]
        ];
    }

    /**
     * 获取各种单据模块【中文产品名称】字段配置
     * @return array
     */
    public function getCnNameFieldConfig()
    {
        $fieldTemplate = [
            'id'                 => 'product_cn_name',
            'base'               => 1,
            'name'               => '中文产品名称',
            'field_type'          => CustomFieldService::FIELD_TYPE_TEXT,
            'require'            => 0,
            'edit_required'      => 0,
            'disable_flag'        => 0,//前端是否隐藏，1隐藏，0不隐藏
            'edit_hide'          => 0,// 是否可编辑，1可以，0否
            'default'            => '',
            'edit_default'       => 0,
            'hint'               => '',
            'edit_hint'          => 0,
            'is_exportable'      => 0,
            'is_editable'        => 0,
            'is_list'            => 1,
            'columns'            => 'product_cn_name',
            'relation_type'      => 0,
            'relation_field'      => '',
            'relation_field_type' => 0,
            'relation_field_name' => '',
            'readonly'           => 0,
            'ext_info'           => '',
            'create_time'        => date('Y-m-d H:i:s'),
            'update_time'        => date('Y-m-d H:i:s'),
            'after'              => 'product_name',
        ];

        $groupMap = [
            Constants::TYPE_ORDER                     => CustomFieldService::ORDER_GROUP_PRODUCT,             //2-销售订单
            Constants::TYPE_PURCHASE_ORDER            => CustomFieldService::PURCHASE_ORDER_GROUP_PRODUCT,    //22-采购订单
            Constants::TYPE_PURCHASE_INBOUND_INVOICE  => CustomFieldService::PURCHASE_INBOUND_GROUP_PRODUCT,  //28-采购入库
            Constants::TYPE_OTHER_INBOUND_INVOICE     => CustomFieldService::OTHER_INBOUND_GROUP_PRODUCT,     //29-其他入库
            Constants::TYPE_SALE_OUTBOUND_INVOICE     => CustomFieldService::SALE_OUTBOUND_GROUP_PRODUCT,     //30-销售出库单
            Constants::TYPE_OTHER_OUTBOUND_INVOICE    => CustomFieldService::OTHER_OUTBOUND_GROUP_PRODUCT,    //31-其他出库
            Constants::TYPE_PURCHASE_RETURN_INVOICE   => CustomFieldService::PURCHASE_RETURN_GROUP_PRODUCT,   //33-采购退货
            Constants::TYPE_PRODUCT_TRANSFER_PURCHASE => CustomFieldService::PURCHASE_INBOUND_GROUP_PRODUCT,  //38-采购产品流转
            Constants::TYPE_PRODUCT_TRANSFER_INBOUND  => CustomFieldService::PURCHASE_INBOUND_GROUP_PRODUCT,  //39-入库产品流转
            Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND => CustomFieldService::PURCHASE_INBOUND_GROUP_PRODUCT,  //40-出库产品流转
        ];
        $isEditable = [
            Constants::TYPE_ORDER,
        ];
        $editRequired = [
            Constants::TYPE_ORDER,
        ];
        $editHint = [
            Constants::TYPE_ORDER,
            Constants::TYPE_PURCHASE_ORDER,
        ];
        $editHide = [
            Constants::TYPE_PURCHASE_INBOUND_INVOICE,
            Constants::TYPE_OTHER_INBOUND_INVOICE,
            Constants::TYPE_SALE_OUTBOUND_INVOICE,
            Constants::TYPE_OTHER_OUTBOUND_INVOICE,
            Constants::TYPE_PURCHASE_RETURN_INVOICE,
        ];
        $exportGroup = [
            Constants::TYPE_ORDER => FieldExportService::ORDER_GROUP_EXCHANGE_PRODUCT,
            Constants::TYPE_PURCHASE_ORDER => FieldExportService::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT,
        ];
        $fieldConfig = [];
        foreach ($groupMap as $type => $groupId) {
            $config = $fieldTemplate;
            $config['type'] = $type;
            $config['is_editable'] = in_array($type, $isEditable) ? 1 : 0;
            $config['edit_required'] = in_array($type, $editRequired) ? 1 : 0;
            $config['edit_hint'] = in_array($type, $editHint) ? 1 : 0;
            $config['edit_hide'] = in_array($type, $editHide) ? 1 : 0;
            $config['group_id'] = $groupId;

            $config['is_exportable'] = isset($exportGroup[$type]) ? 1 : 0;
            $config['export_scenario'] = isset($exportGroup[$type]) ? $type : '';
            $config['export_group'] = isset($exportGroup[$type]) ? $exportGroup[$type] : 0;

            $fieldConfig[$type] = $config;
        }

        return $fieldConfig;
    }

    /**
     *  回款登记 新增客户表字段
     */
    public function actionAddCashCollectionInvoiceCustomField($clientId = 0, $grey = 0, $greyNum = 0)
    {
        self::info("开始执行回款登记字段到客户表！！！");
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $greyNum = explode(",", $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $system_fields = $this->cashCollectionInvoiceFieldsSetting();

        foreach ($clientIds as $client_id) {
            echo "开始执行client_id:" . $client_id . PHP_EOL;

            $this->syncSystem2Customer($system_fields, $client_id);
            $this->syncSystem2ExportSetting($system_fields, $client_id);
            $this->syncSystem2FieldGroup($system_fields, $client_id);

            $privilegeField = new \common\library\privilege_v3\PrivilegeField($client_id);
            $privilegeField->flushCache();
        }

        self::info("新增回款登记字段到客户表执行完成！！！");
    }

    /**
     *  回款单新增字段 新增客户表字段
     */
    public function actionAddCashCollectionCustomField($clientId = 0, $grey = 0, $greyNum = 0)
    {
        self::info("开始执行回款单字段到客户表！！！");

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $greyNum = explode(",", $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        $fieldIds = $this->fieldIdsByOrder();

        $system_fields = $this->cashCollectionFieldsSetting();

        foreach ($clientIds as $client_id) {
            echo "开始执行client_id:" . $client_id . PHP_EOL;
            $this->syncSystem2Customer($system_fields, $client_id);
            $this->syncSystem2ExportSetting($system_fields, $client_id);
            $this->syncSystem2FieldGroup($system_fields, $client_id);

            //处理字段排序
            echo '--开始处理新字段顺序'.PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($client_id);
            $customFieldSql = "SELECT id FROM  tbl_custom_field where client_id ={$client_id} and type = " .  \Constants::TYPE_CASH_COLLECTION . " and base=1 ORDER BY `order` DESC";
            $customFields = $db->createCommand($customFieldSql)->queryAll(true);
            $customFieldIds = array_column($customFields, 'id');
            $intersectCustomFieldIds = array_intersect($fieldIds, $customFieldIds);
            if(!empty($intersectCustomFieldIds)){
                $batchUpdateFieldSql = '';
                $intersectCustomFieldIdStr = '';
                foreach ($intersectCustomFieldIds as  $updateField) {
                    $batchUpdateFieldSql .=  " when id = '" . $updateField . "' then " . array_search($updateField, $fieldIds) + 1;
                    $intersectCustomFieldIdStr .= "'" . $updateField . "',";
                }
                $intersectCustomFieldIdStr = rtrim($intersectCustomFieldIdStr, ',');
                $updateOrderSql = "update tbl_custom_field set `order` = case " . $batchUpdateFieldSql . " end, app_order = case " . $batchUpdateFieldSql . " end, update_time = '" .xm_function_now(). "' where  client_id ={$client_id} and type =" . \Constants::TYPE_CASH_COLLECTION . " and id in (" . $intersectCustomFieldIdStr. ")";
                $updateOrderCount = $db->createCommand($updateOrderSql)->execute();
                echo "修改回款单字段顺序:".$updateOrderCount.PHP_EOL;
            }

            //修改回款单 「资金账户」字段为可控制必填
            $sql = "update tbl_custom_field set edit_required=:edit_required
   where client_id =:client_id and type =:type and id =:id and enable_flag=:enable_flag";
            $parmas = [
                ':edit_required' => 1,
                ':client_id' => $client_id,
                ':type' =>  \Constants::TYPE_CASH_COLLECTION,
                ':id' => 'capital_account_id',
                ':enable_flag' => \Constants::ENABLE_FLAG_TRUE,
            ];
            $updateCount = $db->createCommand($sql)->execute($parmas);
            echo "修改回款单 「资金账户」字段为可控制必填影响条数:".$updateCount.PHP_EOL;

            //修改回款单字段collecting_bank「资金账户」名称为「资金账户(旧)」
            $updateCollectingBankSql = "update tbl_custom_field set name=:name
   where client_id =:client_id and type =:type and id =:id and enable_flag=:enable_flag";
            $collectingBankParmas = [
                ':name' => '资金账号',
                ':client_id' => $client_id,
                ':type' =>  \Constants::TYPE_CASH_COLLECTION,
                ':id' => 'collecting_bank',
                ':enable_flag' => \Constants::ENABLE_FLAG_TRUE,
            ];
            $updateCollectingBankCount = $db->createCommand($updateCollectingBankSql)->execute($collectingBankParmas);
            echo "修改回款单 collecting_bank「资金账户」字段名称为「资金账户号」:".$updateCollectingBankCount.PHP_EOL;

            $privilegeField = new \common\library\privilege_v3\PrivilegeField($client_id);
            $privilegeField->flushCache();
        }
        self::info("新增回款单字段到客户表执行完成！！！");
    }

    //回款登记-灰度回退，执行脚本 $recover 灰度正常recover=1
    public function actionCashCollectionInvoiceFieldRollback($clientId = 0, $grey = 0, $greyNum = 0,$recover = 0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $client_id) {

            echo "开始执行client_id:".$client_id.PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($client_id);

            $sql = "update tbl_custom_field set enable_flag=:enable_flag 
   where client_id =:client_id and type = " . \Constants::TYPE_CASH_COLLECTION_INVOICE;

            $parmas = [
                ':enable_flag' => $recover,
                ':client_id' => $client_id
            ];
            $updateCount = $db->createCommand($sql)->execute($parmas);
            echo "影响条数:".$updateCount.PHP_EOL;
            $cashCollectionFields = [
                'exchange_rate_usd',
                'exchange_loss_amount',
                'exchange_loss_amount_usd',
                'cash_collection_invoice_id',
                'cash_collection_invoice_amount',
                'cash_collection_invoice_currency',
                'cash_collection_invoice_bank_charge',
                'cash_collection_invoice_exchange_rate',
                'cash_collection_invoice_exchange_rate_usd',
            ];
            $idStr = '';
            foreach ($cashCollectionFields as $field){
                $idStr .=  "'" . $field."',";
            }
            $idStr = rtrim($idStr, ',');
            $cashCollectionSql = "update tbl_custom_field set enable_flag=:enable_flag 
   where client_id =:client_id and type =:type and id in (" . $idStr. ")";
            $cashCollectionParmas = [
                ':enable_flag' => $recover,
                ':client_id' => $client_id,
                ':type' => \Constants::TYPE_CASH_COLLECTION
            ];
            $updateCount = $db->createCommand($cashCollectionSql)->execute($cashCollectionParmas);
            echo "回款单字段变更影响条数:".$updateCount.PHP_EOL;
            \ProjectActiveRecord::releaseDbByClientId($client_id);
        }

        echo "success".PHP_EOL;

    }

    /**
     * 新增 回款登记 系统字段 （全量后执行一次）
     */
    public function actionAddCashCollectionInvoiceFieldToSystemField(){
        //获取系统字段
        $systemFields = \Yii::app()->db->createCommand('SELECT * FROM tbl_system_field WHERE `type`= ' . Constants::TYPE_CASH_COLLECTION_INVOICE . ' ORDER BY `order` DESC')->queryAll(true);

        $intoFields = $this->cashCollectionInvoiceFieldsSetting();

        $newSystemFields = $systemFields;

        foreach ($intoFields as $intoField) {
            $newSystemFields[] = $intoField;
        }

        $conut = count($newSystemFields);
        foreach ($newSystemFields as &$newSystemField) {
            $newSystemField['order'] = $conut;
            $newSystemField['app_order'] = $conut;
            $conut--;
        }
        unset($newSystemField);
        //同步到系统表
        $this->syncSystemFields(Constants::TYPE_CASH_COLLECTION_INVOICE, $newSystemFields);
    }

    /**
     * 新增 回款单 系统字段 （全量后执行一次）
     */
    public function actionAddCashCollectionFieldToSystemField(){
        //获取系统字段
        $systemFields = \Yii::app()->db->createCommand('SELECT * FROM tbl_system_field WHERE `type`= ' . Constants::TYPE_CASH_COLLECTION . ' ORDER BY `order` DESC')->queryAll(true);

        $intoFields = array_column($this->cashCollectionFieldsSetting(), null, 'id');

        $newSystemFields = [];
        foreach ($systemFields as  $field) {
            if($field['id'] == 'capital_account_id'){
                $field['edit_required'] = 1;
                $newSystemFields[] = $field;
            }
            if ($field['id'] == 'exchange_rate') {
                $newSystemFields[] = $intoFields['exchange_rate_usd'];
            }
            if($field['id'] == 'amount') {
                $newSystemFields[] = $intoFields['cash_collection_invoice_id'];
                $newSystemFields[] = $intoFields['cash_collection_invoice_currency'];
                $newSystemFields[] = $intoFields['cash_collection_invoice_amount'];
                $newSystemFields[] = $intoFields['cash_collection_invoice_bank_charge'];
                $newSystemFields[] = $intoFields['cash_collection_invoice_exchange_rate'];
                $newSystemFields[] = $intoFields['cash_collection_invoice_exchange_rate_usd'];
                $newSystemFields[] = $intoFields['exchange_loss_amount'];
                $newSystemFields[] = $intoFields['exchange_loss_amount_usd'];
            }
        }

        $fieldIds = $this->fieldIdsByOrder();

        foreach ($newSystemFields as &$newSystemField) {
            $order = array_search($newSystemField['id'], $fieldIds) ?? 0;
            $newSystemField['order'] = $order + 1 ;
            $newSystemField['app_order'] = $order + 1;
        }
        //同步到系统表
        $this->syncSystemFields(Constants::TYPE_CASH_COLLECTION, $newSystemFields);
    }

    public function getMinmumOrderQuantityFieldConfig()
    {
        return [[
            'id' => 'minimum_order_quantity',
            'type' => \Constants::TYPE_QUOTATION,
            'group_id' => CustomFieldService::QUOTATION_GROUP_PRODUCT,
            'base' => 1,
            'name' => '最小起订量',
            'order' => 0,
            'app_order' => 0,
            'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
            'edit_required' => 1,
            'edit_hide' => 1,       // 是否可隐藏，✔
            'require' => 0,         //是否可选填，✔
            'disable_flag' => 0,    //前端是否隐藏，1隐藏，0不隐藏
            'default' => '',
            'edit_default' => 0,    //是否可编辑默认值，✖
            'hint' => '',           //提示内容，空
            'edit_hint' => 1,       //是否可编辑提示字段，✔
            'is_exportable' => 1,
            'is_editable' => 1,     //是否可编辑，✔
            'is_list' => 1,
            'columns' => 'minimum_order_quantity',
            'relation_type' => 0,
            'relation_field' => '',
            'relation_field_type' => 0,
            'relation_field_name' => '',
            'export_scenario' => \Constants::TYPE_QUOTATION,
            'export_group' => 6,
            'readonly' => 0,        //是否只读字段，✖
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ]];


    }

    /**
     * 新增 最小起订量 字段
     * @param $clientId
     * @param $grey
     * @param $greyNum
     * @return void
     * @throws \ProcessException
     */
    public function actionAddMinMumOrderQuantityField($clientId, $grey = 0, $greyNum = 0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $greyNum = explode(",", $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        //新增系统字段 最小起订量
        $system_fields = $this->getMinmumOrderQuantityFieldConfig();
        $this->syncSystemFields(\Constants::TYPE_QUOTATION, $system_fields);

        foreach ($clientIds as $client_id) {
            // 字段排在product_model之后
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($client_id);
            if (!$db) {
                echo '--此client无效..' . PHP_EOL;
                continue;
            }

            $this->syncSystem2Customer($system_fields, $client_id);
            //处理字段排序
            $this->syncSystem2ExportSetting($system_fields, $client_id);
            $this->syncSystem2FieldGroup($system_fields, $client_id);
            echo '成功处理client_id：', $client_id, PHP_EOL;
        }
    }


    /****
     * 撤回 最小起订量 字段
     * @param $clientId
     * @param $grey
     * @param $greyNum
     * @return void
     */
    public function actionRollbackMinMumOrderQuantityField($clientId, $grey = 0, $greyNum = 0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $greyNum = explode(",", $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $client_id) {
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($client_id);
            if (!$db) {
                echo '--此client无效..' . PHP_EOL;
                continue;
            }
            $sql = "delete from tbl_custom_field  where client_id ='$clientId' and id ='minimum_order_quantity' and type = 3";
            $db->createCommand($sql)->execute();
            $sql = "delete from tbl_field_export_setting  where client_id ='$clientId' and id ='minimum_order_quantity' and type = 3 and export_type = 3";
            $db->createCommand($sql)->execute();
            echo '成功处理client_id：', $client_id, PHP_EOL;
        }
        $db = \Yii::app()->db;
        //修改系统字段
        $sql = "delete from tbl_system_field  where  id ='minimum_order_quantity' and type = 3 ";
        $db->createCommand($sql)->execute();
        echo '删除成功';
    }

    /**
     * 获取 采购订单-入库 字段配置
     * @return array[]
     */
    protected function getPurchaseOrderInboundConfig()
    {
        return [
            [
                'id' => 'inbound_status',
                'type' => Constants::TYPE_PURCHASE_ORDER,
                'group_id' => CustomFieldService::PURCHASE_ORDER_GROUP_BASIC,
                'base' => 1,
                'name' => '入库状态',
                'order' => 0,
                'app_order' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                'edit_required' => 0,
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'require' => 0,
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 0,
                'is_exportable' => 1,
                'is_editable' => 0,
                'is_list' => 0,
                'columns' => 'inbound_status',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '22',
                'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_BASIC,
                'readonly' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'have_inbound_count',
                'type' => Constants::TYPE_PURCHASE_ORDER,
                'group_id' => CustomFieldService::PURCHASE_ORDER_GROUP_PRODUCT,
                'base' => 1,
                'name' => '已入库数量',
                'order' => 0,
                'app_order' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'edit_required' => 0,
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'require' => 0,
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 0,
                'is_exportable' => 1,
                'is_editable' => 0,
                'is_list' => 1,
                'columns' => 'have_inbound_count',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '22',
                'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT,
                'readonly' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'wait_inbound_count',
                'type' => Constants::TYPE_PURCHASE_ORDER,
                'group_id' => CustomFieldService::PURCHASE_ORDER_GROUP_PRODUCT,
                'base' => 1,
                'name' => '未入库数量',
                'order' => 0,
                'app_order' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'edit_required' => 0,
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'require' => 0,
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 0,
                'is_exportable' => 1,
                'is_editable' => 0,
                'is_list' => 1,
                'columns' => 'wait_inbound_count',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '22',
                'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT,
                'readonly' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'have_return_count',
                'type' => Constants::TYPE_PURCHASE_ORDER,
                'group_id' => CustomFieldService::PURCHASE_ORDER_GROUP_PRODUCT,
                'base' => 1,
                'name' => '已退货数量',
                'order' => 0,
                'app_order' => 0,
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'edit_required' => 0,
                'edit_hide' => 0,// 是否可编辑，1可以，0否
                'require' => 0,
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 0,
                'is_exportable' => 1,
                'is_editable' => 0,
                'is_list' => 1,
                'columns' => 'have_return_count',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '22',
                'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT,
                'readonly' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
        ];
    }

    public function actionUpdateDeliveryDateField($clientId, $grey = 0, $greyNum = 0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $greyNum = explode(",", $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        foreach ($clientIds as $client_id) {
            // 字段排在product_model之后
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($client_id);
            $sql = "update tbl_custom_field set ext_info = '".addslashes(json_encode(['1-2天',"3-5天","6-10天","11-15天",'>15天']))."' WHERE client_id = $clientId and id = 'delivery_date' and type = 23;";
            $db->createCommand($sql)->execute();
            echo '成功处理client_id：', $client_id, PHP_EOL;
        }

    }


    /**
     *  费用单 新增客户表字段
     */
    public function actionAddCostInvoiceCustomField($clientId = 0, $grey = 0, $greyNum = 0)
    {
        self::info("开始执行费用单字段到客户表！！！");
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $greyNum = explode(",", $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }


        $system_fields = $this->costInvoiceFieldsSetting();

        foreach ($clientIds as $client_id) {
            echo "开始执行client_id:" . $client_id . PHP_EOL;

            $this->syncSystem2Customer($system_fields, $client_id);
            $this->syncSystem2ExportSetting($system_fields, $client_id);
            $this->syncSystem2FieldGroup($system_fields, $client_id);

            $privilegeField = new \common\library\privilege_v3\PrivilegeField($client_id);
            $privilegeField->flushCache();
        }
        self::info("新增费用单字段到客户表执行完成！！！");
    }


    /**
     * 费用单-灰度回退，执行脚本 $recover 灰度正常recover=1
     */
    public function actionCostInvoiceFieldRollback($clientId = 0, $grey = 0, $greyNum = 0,$recover = 0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        foreach ($clientIds as $client_id) {

            echo "开始执行client_id:" . $client_id . PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($client_id);

            $sql = "update tbl_custom_field set enable_flag=:enable_flag 
   where client_id =:client_id and type = " . \Constants::TYPE_COST_INVOICE;

            $parmas = [
                ':enable_flag' => $recover,
                ':client_id' => $client_id
            ];
            $updateCount = $db->createCommand($sql)->execute($parmas);
            echo "影响条数:" . $updateCount . PHP_EOL;

            \ProjectActiveRecord::releaseDbByClientId($client_id);
        }

        echo "success".PHP_EOL;
    }


    /**
     * 新增 费用单 系统字段 （全量后执行一次）
     */
    public function actionAddCostInvoiceFieldToSystemField()
    {
        //获取系统字段
        $systemFields = \Yii::app()->db->createCommand('SELECT * FROM tbl_system_field WHERE `type`= ' . Constants::TYPE_COST_INVOICE . ' ORDER BY `order` DESC')->queryAll(true);

        $intoFields = $this->costInvoiceFieldsSetting();

        $newSystemFields = $systemFields;

        foreach ($intoFields as $intoField) {
            $newSystemFields[] = $intoField;
        }

        $conut = count($newSystemFields);
        foreach ($newSystemFields as &$newSystemField) {
            $newSystemField['order'] = $conut;
            $newSystemField['app_order'] = $conut;
            $conut--;
        }
        unset($newSystemField);
        //同步到系统表
        $this->syncSystemFields(Constants::TYPE_COST_INVOICE, $newSystemFields);
    }

    public function actionAddOPSysField()
    {
        $fields_setting = $this->orderProfitListFieldsSetting();
        Helper::addField(14787, $fields_setting);
        echo "执行完毕";
    }



    public function actionAddAmountPercentField($clientId = 333319, $grey = 0, $greyNum = 0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $greyNum = explode(",", $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        $fields_setting = $this->paymentAmountPercentFieldSetting();
        foreach ($clientIds as $clientId) {
            // 字段排在product_model之后
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $afterFields[\Constants::TYPE_PAYMENT_INVOICE] = [
                'payment_amount_percent' => 'refer_amount',
            ];
            Helper::addField($clientId, $fields_setting, $afterFields);
            echo '成功处理client_id：', $clientId, PHP_EOL;
        }

    }

    public function actionUpdateCostInvoiceCurrencyCustomField($clientId, $grey = 0, $greyNum = 0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $greyNum = explode(",", $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        foreach ($clientIds as $client_id) {
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($client_id);
            $type = \Constants::TYPE_COST_INVOICE;
            $sql = "update tbl_custom_field set edit_default = 1 WHERE client_id = $clientId and id = 'currency' and type = $type;";
            $db->createCommand($sql)->execute();
            echo '成功处理client_id：', $client_id, PHP_EOL;
        }
    }

    public function actionUpdateCostInvoiceCurrencySystemField(){
        $updateSql = "update tbl_system_field set edit_default = 1  WHERE  id = 'currency' and type = 47 and name = '币种';";
        $db = \Yii::app()->db;
        $result = $db->createCommand($updateSql)->execute();
        if ($result) {
            echo "更新系统表成功～";
        } else {
            echo "更新系统表失败！";
        }
    }

    public function actionUpdateCashCollectionStatusCustomField($clientId, $grey = 0, $greyNum = 0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $greyNum = explode(",", $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        foreach ($clientIds as $client_id) {
            echo '---开始处理client_id：', $client_id, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($client_id);
            $type = \Constants::TYPE_CASH_COLLECTION;
            $sql = "update tbl_custom_field set `default` = '' WHERE client_id = $client_id and id = 'collect_status' and `default` = 0 and type = $type; 
                    update tbl_custom_field set `hint` = '' WHERE client_id = $client_id and id = 'collect_status' and `hint` = 0 and type = $type;";
            $db->createCommand($sql)->execute();
            echo '成功处理client_id：', $client_id, PHP_EOL;
        }
    }

    public function actionUpdateCashCollectionStatusSystemField()
    {
        $db = \Yii::app()->db;
        $sql = "update tbl_system_field set `default` = ''  WHERE  id = 'collect_status' and type = 10 and `default` = 0;";
        $result = $db->createCommand($sql)->execute();
        if ($result) {
            echo "更新系统表成功～";
        } else {
            echo "更新系统表失败！";
        }
    }



    /**
     * 获取出入库单据 仓库ID 字段配置
     * @return array[]
     */
    protected function getWarehouseIdFieldConfig()
    {
        $settings = [
            \Constants::TYPE_PURCHASE_INBOUND_INVOICE => [
                'group_id' => \common\library\custom_field\CustomFieldService::PURCHASE_INBOUND_GROUP_BASIC,
                'export_group' => \common\library\custom_field\FieldExportService::PURCHASE_INBOUND_INVOICE_GROUP_BASIC,
            ],
            \Constants::TYPE_OTHER_INBOUND_INVOICE    => [
                'group_id' => \common\library\custom_field\CustomFieldService::OTHER_INBOUND_GROUP_BASIC,
                'export_group' => \common\library\custom_field\FieldExportService::OTHER_INBOUND_INVOICE_GROUP_BASIC,
            ],
            \Constants::TYPE_SALE_OUTBOUND_INVOICE    => [
                'group_id' => \common\library\custom_field\CustomFieldService::SALE_OUTBOUND_GROUP_BASIC,
                'export_group' => \common\library\custom_field\FieldExportService::SALE_OUTBOUND_INVOICE_GROUP_BASIC,
            ],
            \Constants::TYPE_OTHER_OUTBOUND_INVOICE   => [
                'group_id' => \common\library\custom_field\CustomFieldService::OTHER_OUTBOUND_GROUP_BASIC,
                'export_group' => \common\library\custom_field\FieldExportService::OTHER_OUTBOUND_INVOICE_GROUP_BASIC,
            ],
            \Constants::TYPE_PURCHASE_RETURN_INVOICE  => [
                'group_id' => \common\library\custom_field\CustomFieldService::PURCHASE_RETURN_GROUP_BASIC,
                'export_group' => \common\library\custom_field\FieldExportService::PURCHASE_RETURN_INVOICE_GROUP_BASIC,
            ],
        ];

        $systemFields = [];
        foreach ($settings as $type => $setting) {
            $field = [
                'id'                  => 'invoice_warehouse_id',
                'base'                => 1,      //是否系统字段：1-是，0-否
                'name'                => '仓库', //字段名称
//                'order'               => 0,      //字段排序
//                'app_order'           => 0,      //app字段排序
                'field_type'          => CustomFieldService::FIELD_TYPE_SELECT, //字段类型
                'edit_required'       => 0,      //是否可编辑必填：1-是，0-否
                'edit_hide'           => 1,      //是否可编辑隐藏：1-是，0-否
                'require'             => 1,      //是否必填：1-是，0-否
                'disable_flag'        => 0,      //是否隐藏：1-是，0-否
                'default'             => '',     //字段默认值
                'edit_default'        => 0,      //是否可编辑默认值：1-是，0-否
                'hint'                => '',     //字段提示
                'edit_hint'           => 0,      //是否可编辑字段提示：1-是，0-否
                'is_editable'         => 0,      //是否可编辑：1-是，0-否
                'is_list'             => 0,      //是否为子列表：1-是，0-否
                'columns'             => 'invoice_warehouse_id',
                'relation_type'       => 0,      //关联字段模块
                'relation_field'      => '',     //关联字段ID
                'relation_field_type' => 0,      //关联字段类型
                'relation_field_name' => '',     //关联字段名称
                'is_exportable'       => 1,      //是否可导出
                'readonly'            => 0,
                'create_time'         => date('Y-m-d H:i:s'),
                'update_time'         => date('Y-m-d H:i:s'),
            ];

            $field['type']            = $type;
            $field['group_id']        = $setting['group_id'];      //字段分组
            $field['export_scenario'] = $type;                     //导出字段所属模块
            $field['export_group']    = $setting['export_group'];  //导出字段分组

            $systemFields[$type] = $field;
        }

        return $systemFields;
    }

    public function actionUpdateProductUnitField($clientId=14119, $grey = 0, $greyNum = 0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $greyNum = explode(",", $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        foreach ($clientIds as $client_id) {
            echo '---开始处理client_id：', $client_id, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($client_id);
            //采购退货单 修改非必填
            //采购入库单 修改不可编辑
            $sql = "update tbl_custom_field set edit_required = 1 WHERE client_id = $client_id and id = 'product_unit' and type = 33;
";
            $db->createCommand($sql)->execute();
            echo '成功处理client_id：', $client_id, PHP_EOL;
        }
    }

    public function actionUpdateProductUnitSystemFields()
    {
        $db = \Yii::app()->db;
        //正常是 0 1
        $sql = "update tbl_system_field  set edit_required = 1 WHERE  id = 'product_unit' and type = 33";
        $result = $db->createCommand($sql)->execute();
        if ($result) {
            echo "更新系统表成功～";
        } else {
            echo "更新系统表失败！";
        }
    }

    //多币种需求 名字修改、新增字段 todo 未排序 产品还未提供  回款单的exchange_rate_usd 需要改为is_editable=1
    public function actionExchangeRateField($clientId, $returnField = false)
    {

        //可以导出的模版
        $isExportModule = [
            Constants::TYPE_ORDER,
            Constants::TYPE_QUOTATION,
            Constants::TYPE_PURCHASE_ORDER,
            Constants::TYPE_PURCHASE_INBOUND_INVOICE,
            Constants::TYPE_OTHER_INBOUND_INVOICE,
            Constants::TYPE_PURCHASE_RETURN_INVOICE,
        ];

        $fieldGroupMap = [
            \Constants::TYPE_PURCHASE_ORDER => [
                'exchange_rate_usd'        => \common\library\custom_field\CustomFieldService::PURCHASE_ORDER_GROUP_BASIC,
                'amount_rmb' => \common\library\custom_field\CustomFieldService::PURCHASE_ORDER_GROUP_CHARGE,
                'amount_usd' => \common\library\custom_field\CustomFieldService::PURCHASE_ORDER_GROUP_CHARGE,
            ],
        ];

        //模块需要新增的字段
        $module2addFieldMap = [
//            Constants::TYPE_ORDER => ['exchange_rate_usd'],
//            Constants::TYPE_QUOTATION => ['exchange_rate_usd'],
//            Constants::TYPE_CASH_COLLECTION => ['real_amount_rmb', 'real_amount_usd', 'bank_charge_rmb', 'bank_charge_usd', 'amount_rmb', 'amount_usd'],
//            Constants::TYPE_PURCHASE_ORDER => ['exchange_rate_usd', 'amount_rmb', 'amount_usd', 'product_total_amount_rmb', 'product_total_amount_usd', 'payment_amount_rmb', 'payment_amount_usd', 'payment_wait_amount_rmb', 'payment_wait_amount_usd'],
            Constants::TYPE_PURCHASE_ORDER => ['exchange_rate_usd', 'amount_rmb', 'amount_usd'],
            Constants::TYPE_PURCHASE_INBOUND_INVOICE => ['currency', 'exchange_rate', 'exchange_rate_usd', 'product_total_amount_rmb', 'product_total_amount_usd'],
//            Constants::TYPE_SALE_OUTBOUND_INVOICE => ['exchange_rate_usd', 'product_total_amount_rmb', 'product_total_amount_usd'],
            Constants::TYPE_PURCHASE_RETURN_INVOICE => ['currency', 'exchange_rate', 'exchange_rate_usd', 'product_total_amount_rmb', 'product_total_amount_usd'],
//            Constants::TYPE_PAYMENT_INVOICE => ['exchange_rate_usd', 'amount_rmb', 'amount_usd'],
//            Constants::TYPE_CASH_COLLECTION_INVOICE => ['amount_rmb', 'amount_usd', 'bank_charge_rmb', 'bank_charge_usd', 'real_amount_rmb', 'real_amount_usd', 'allocated_amount_rmb', 'allocated_amount_usd', 'current_amount_rmb', 'current_amount_usd'],
//            Constants::TYPE_COST_INVOICE => ['amount_rmb', 'amount_usd', 'payment_amount_rmb', 'payment_amount_usd', 'payment_wait_amount_rmb', 'payment_wait_amount_usd'],
        ];

        //模块新增字段对应的名字
        $moduleFieldNameMap = [
            Constants::TYPE_ORDER => ['exchange_rate_usd' => '汇率(兑USD)'],
            Constants::TYPE_QUOTATION => ['exchange_rate_usd' => '汇率(兑USD)'],
            Constants::TYPE_CASH_COLLECTION => [
                'real_amount_rmb' => '实到账金额(CNY)',
                'real_amount_usd' => '实到账金额(USD)',
                'bank_charge_rmb' => '手续费(CNY)',
                'bank_charge_usd' => '手续费(USD)',
                'amount_rmb' => '回款金额(CNY)',
                'amount_usd' => '回款金额(USD)'
            ],
            Constants::TYPE_PURCHASE_ORDER => [
                'exchange_rate_usd' => '汇率(兑USD)',
                'amount_rmb' => '订单金额(CNY)',
                'amount_usd' => '订单金额(USD)',
                'product_total_amount_rmb' => '产品总金额(CNY)',
                'product_total_amount_usd' => '产品总金额(USD)',
                'payment_amount_rmb' => '已付款金额(CNY)',
                'payment_amount_usd' => '已付款金额(USD)',
                'payment_wait_amount_rmb' => '待付款金额(CNY)',
                'payment_wait_amount_usd' => '待付款金额(USD)'
            ],
            Constants::TYPE_PURCHASE_INBOUND_INVOICE => [
                'currency' => '币种',
                'exchange_rate'=>'汇率(兑CNY)',
                'exchange_rate_usd'=>'汇率(兑USD)',
                'product_total_amount_rmb'=>'产品总金额(CNY)',
                'product_total_amount_usd'=>'产品总金额(USD)'
            ],
            Constants::TYPE_SALE_OUTBOUND_INVOICE => [
                'exchange_rate_usd'=>'汇率(兑USD)',
                'product_total_amount_rmb'=>'产品总金额(CNY)',
                'product_total_amount_usd'=>'产品总金额(USD)'
            ],
            Constants::TYPE_PURCHASE_RETURN_INVOICE => [
                'currency' => '币种',
                'exchange_rate'=>'汇率(兑CNY)',
                'exchange_rate_usd'=>'汇率(兑USD)',
                'product_total_amount_rmb'=>'产品总金额(CNY)',
                'product_total_amount_usd'=>'产品总金额(USD)'
            ],
            Constants::TYPE_PAYMENT_INVOICE => [
                'exchange_rate_usd'=>'汇率(兑USD)',
                'amount_rmb'=>'付款金额(CNY)',
                'amount_usd'=>'付款金额(USD)'
            ],
            Constants::TYPE_CASH_COLLECTION_INVOICE => [
                'amount_rmb'=>'回款金额(CNY)',
                'amount_usd'=>'回款金额(USD)',
                'bank_charge_rmb'=>'回款手续费(CNY)',
                'bank_charge_usd'=>'回款手续费(USD)',
                'real_amount_rmb'=>'实到账金额(CNY)',
                'real_amount_usd'=>'实到账金额(USD)',
                'allocated_amount_rmb'=>'已核销金额(CNY)',
                'allocated_amount_usd'=>'已核销金额(USD)',
                'current_amount_rmb'=>'待核销金额(CNY)',
                'current_amount_usd'=>'待核销金额(USD)'
            ],
            Constants::TYPE_COST_INVOICE => [
                'amount_rmb'=>'费用金额(CNY)',
                'amount_usd'=>'费用金额(USD)',
                'payment_amount_rmb'=>'已付款金额(CNY)',
                'payment_amount_usd'=>'已付款金额(USD)',
                'payment_wait_amount_rmb'=>'待付款金额(CNY)',
                'payment_wait_amount_usd'=>'待付款金额(USD)'
            ],
        ];

        $currencyAndExchangeRateFieldSettingMap = [
            'edit_required' => 0,
            'require' => 1,
            'edit_hint' => 0,
            'disable_flag' => 0,
            'is_editable'=>1,
        ];


        $cnyUsdField = ["real_amount_rmb", "real_amount_usd", "bank_charge_rmb", "bank_charge_usd", "amount_rmb", "amount_usd",
            "product_total_amount_rmb", "product_total_amount_usd", "payment_amount_rmb", "payment_amount_usd",
            "payment_wait_amount_rmb", "payment_wait_amount_usd", "allocated_amount_rmb", "allocated_amount_usd", "current_amount_rmb",
            "current_amount_usd"];

        $cnyUsdFieldSettingMap = [
            'is_editable'=>0,
            'edit_hide'=>1,
            'edit_required'=>0,
            'require'=>0,
        ];


        $baseField = [
                'id' => 'exchange_rate',
                'type' => Constants::TYPE_PURCHASE_ORDER,   //外部给
                'group_id' => 1,  //外部给
                'base' => 1,
                'name' => '汇率(兑CNY)',
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'require' => 0,
                'edit_required' => 0,
                'disable_flag' => 0,
                'edit_hide' => 0,
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 1,
                'is_exportable' => 0,   //外部给，看是否可导出的模版
                'is_editable' => 1,
                'is_list' => 0,
                'columns' => 'exchange_rate',//外部给
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => '',//外部给
                'export_group' => '',//外部给
                'readonly' => 0,
                'ext_info' => '',
                'unique_check' => 0,
                'unique_prevent' => 1,
                'unique_message' => '',
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
            ];

        $systemField=[];


        foreach ($module2addFieldMap as $module => $fieldIds) {
            foreach ($fieldIds as $fieldId) {
                $field = $baseField;
                $field['id'] = $field['columns'] = $fieldId;
                $field['type'] = $module;
                $field['name'] = $moduleFieldNameMap[$module][$fieldId]??'';
                $field['group_id'] = isset($fieldGroupMap[$module]) ? $fieldGroupMap[$module][$field['id']] : 1;

                if (empty($field['name'])) {
                    echo "{$module}-{$fieldId} 没有设置名称" . PHP_EOL;
                    continue;
                }

                //币种 field_type 不一样
                if ($fieldId == 'currency') {
                    $field['field_type'] = CustomFieldService::FIELD_TYPE_SELECT;
                }

                if (in_array($fieldId, ['currency','exchange_rate', 'exchange_rate_usd'])) {
                    foreach ($currencyAndExchangeRateFieldSettingMap as $k => $v) {
                        $field[$k]=$v;
                    }
                }

                //多币种字段设置
                if (in_array($fieldId, $cnyUsdField)) {
                    foreach ($cnyUsdFieldSettingMap as $k => $v) {
                        $field[$k]=$v;
                    }
                }

                //是否有导出,对导出字段操作
                if (in_array($module,$isExportModule)) {
                    $field['is_exportable'] = 1;
                    $field['export_scenario'] = $module;
                    $field['export_group'] = 1;
                }

                $systemField[] =$field;
            }

        }

        if ($returnField) {
            return $systemField;
        }

        $client_id = $clientId;
        $this->syncSystem2Customer($systemField, $client_id);
        $this->syncSystem2ExportSetting($systemField, $client_id);
        $this->syncSystem2FieldGroup($systemField, $client_id);
    }


    public function actionAddCashCollectionPercentageCustomField($clientId = 1, $grey = 0, $greyNum = 0)
    {
        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $greyNum = explode(",", $greyNum);
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }
        $fields_setting = $this->getOrderCashCollectionPercentage();
        $afterFields[\Constants::TYPE_ORDER] = [
            'cash_collection_percentage' => 'cash_collection_info.collect_amount_usd',
        ];
        foreach ($clientIds as $clientId) {
            // 字段排在product_model之后
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            Helper::addField($clientId, $fields_setting, $afterFields);
            echo '成功处理client_id：', $clientId, PHP_EOL;
        }
    }

    public function actionAddCashCollectionPercentageSystemField()
    {
        $fields_setting = $this->getOrderCashCollectionPercentage();
        Helper::syncSystemFields(Constants::TYPE_ORDER, $fields_setting);
        $this->resortSystemFields($fields_setting,Constants::TYPE_ORDER);
        echo '成功处理client_id：';
    }

    public static function resortSystemFields($fields, $module){
        $sorter = new \common\library\util\sort\Sorter(new \common\library\util\sort\SystemFieldSortRepository($module,
            'order'));
        $sorter->setResort(true);

        foreach($fields as $field){
            $sorter->setId($field['id'], true);
            $sorter->setSortField('order');
            $orderNum = $sorter->after($field['after']);
            $sorter->setSortField('app_order');
            $appOrderNum = $sorter->after($field['after']);
        }
    }

    /**
     * 获取 采购订单-关联销售订单编号 字段配置
     * @return array[]
     */
    protected function getPurchaseOrderReferOrderIdFieldConfig()
    {
        $template = [
            [
                'id' => 'refer_order_id',
                'type' => 22,
                'group_id' => CustomFieldService::PURCHASE_ORDER_GROUP_BASIC,
                'base' => 1,
                'name' => '关联销售订单编号',
                'order' => 6,
                'app_order' => 6,
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                'edit_required' => 0,
                'edit_hide' => 1,// 是否可编辑，1可以，0否
                'require' => 0,
                'disable_flag' => 0,//前端是否隐藏，1隐藏，0不隐藏
                'default' => '',
                'edit_default' => 0,
                'hint' => '',
                'edit_hint' => 1,
                'is_exportable' => 1,
                'is_editable' => 1,
                'is_list' => 0,
                'columns' => 'refer_order_id',
                'relation_type' => 0,
                'relation_field' => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario' => 22,
                'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_BASIC,
                'readonly' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]
        ];
        return $template;
    }

    /**
     * 采购订单 新增 【关联销售订单编号】 系统字段 （全量后执行一次）
     */
    public function actionAddPurchaseOrderReferOrderIdSystemField(){
        //同步到系统表
        $fieldConfig = $this->getPurchaseOrderReferOrderIdFieldConfig();
        $this->syncSystemFields(22, [$fieldConfig]);
    }

    /**
     *  采购订单 新增 【关联销售订单编号】  客户表字段
     */
    public function actionAddPurchaseOrderReferOrderIdCustomField($clientId = 0, $grey = 0, $greyNum = 0,$skipClientId=0){
        $system_fields = $this->getPurchaseOrderReferOrderIdFieldConfig();

        if ($clientId) {
            $clientIds = explode(',', $clientId);
        } elseif ($grey) {
            $clientIds = $this->getGreyClientIds($greyNum);
        } else {
            $clientIds = array_column($this->getClientList(), 'client_id');
        }

        foreach ($clientIds as $clientId) {
            if ($clientId<$skipClientId) {
                continue;
            }
            echo '---开始处理client_id：', $clientId, PHP_EOL;
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if (!$db) {
                echo '--此client无效..'.PHP_EOL;
                continue;
            }
            $this->syncSystem2Customer($system_fields, $clientId);
            $this->syncSystem2ExportSetting($system_fields, $clientId);
            $this->syncSystem2FieldGroup($system_fields, $clientId);
            echo '成功处理client_id：', $clientId, PHP_EOL;
        }
    }

    public function actionUpdatePurchaseOrderOrderNoCustomField($clientId = 14119)
    {
        $sql = "update  tbl_custom_field set readonly = 0 WHERE type=22 and base=1 and id='order_no' and client_id = $clientId;";
        $db = \ProjectActiveRecord::getDbByClientId($clientId);
        $db->createCommand($sql)->execute();
        echo '成功';
    }

    public function actionHideTaskProgress($clientId){
        $sql = "update  tbl_custom_field set disable_flag = 1 WHERE is_list = 0 and id = 'task_progress' and type in (38,39,40) and client_id = {$clientId};";
        $db = \ProjectActiveRecord::getDbByClientId($clientId);
        $db->createCommand($sql)->execute();
    }

    public function actionAddProductTransferFields($clientId = 333319)
    {
        $db = \ProjectActiveRecord::getDbByClientId($clientId);

        //隐藏不需要的字段 明细&&任务进度
        $updateSql = "update  tbl_custom_field set disable_flag = 1 WHERE  type in (38,39,40) and client_id = {$clientId} and base = 1
            and id  in ('order_record_id','purchase_order_product_id','have_purchase_count','task_inbound_count','check_outbound_count',
                        'task_purchase_count','to_inbound_count','to_purchase_count','reach_count','to_outbound_count','task_outbound_count'
                       'check_purchase_count','reach_count','check_inbound_count','task_progress','notice_config','product_total_count');
update  tbl_custom_field set is_editable = 0 WHERE is_list = 1 and id  in('record_remark','description') and type in (38,39,40) and client_id = {$clientId};
update  tbl_custom_field set name = '期望货好时间'  WHERE type = 38 and name='期望到货时间' and id ='expect_time' and client_id = {$clientId};
update  tbl_custom_field set field_type = 4 WHERE field_type = 10  and id = 'expect_time' and type in (38,39,40) and client_id = {$clientId};
update  tbl_custom_field set name = '单位'  WHERE   type in (38,39,40) and name='计量单位' and id ='product_unit' and client_id = {$clientId};
update  tbl_custom_field set name = '产品备注'  WHERE   type in (38,39,40) and name='产品行备注' and id ='record_remark' and client_id = {$clientId};
update  tbl_custom_field set  name = '出库数量'  WHERE   type =40 and  id = 'have_outbound_count' and client_id = {$clientId};
update  tbl_custom_field set  disable_flag = 0  WHERE   type =38 and  id = 'purchase_count' and client_id = {$clientId} and disable_flag=1;
";

        $db->createCommand($updateSql)->execute();

        $fieldsGroup = [
            Constants::TYPE_PRODUCT_TRANSFER_PURCHASE => $this->addProductTransferPurchaseFieldsSetting(),
            Constants::TYPE_PRODUCT_TRANSFER_INBOUND => $this->addProductTransferIntboundFieldsSetting(),
            Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND => $this->addProductTransferOutboundFieldsSetting()
        ];
        foreach ($fieldsGroup as $moduleType => $fields) {
            \common\library\custom_field\Helper::addField($clientId, $fields);
            // 调整字段顺序
            $sorter = new \common\library\util\sort\Sorter(new CustomFieldSortRepository($clientId, $moduleType));
            $sorter->setResort(true);
            foreach ($fields as $field) {
                $sorter->setId($field['id'], true);
                $sorter->setSortField('order');
                $orderNum = $sorter->after($field['after']);
                $sorter->setSortField('app_order');
                $appOrderNum = $sorter->after($field['after']);
            }
        }
    }

    public function actionUpdateSystemField(){

        $fieldsGroup = [
            Constants::TYPE_PRODUCT_TRANSFER_PURCHASE => $this->addProductTransferPurchaseFieldsSetting(),
            Constants::TYPE_PRODUCT_TRANSFER_INBOUND => $this->addProductTransferIntboundFieldsSetting(),
            Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND => $this->addProductTransferOutboundFieldsSetting()
        ];
        foreach ($fieldsGroup as $moduleType => $fields) {
            Helper::syncSystemFields($moduleType, $fields);
            // 调整字段顺序
            $sorter = new \common\library\util\sort\Sorter(new \common\library\util\sort\SystemFieldSortRepository($moduleType, 'order'));
            $sorter->setResort(true);
            foreach ($fields as $field) {
                $sorter->setId($field['id'], true);
                $sorter->setSortField('order');
                $orderNum = $sorter->after($field['after']);
                $sorter->setSortField('app_order');
                $appOrderNum = $sorter->after($field['after']);
            }
        }
        $db = \Yii::app()->db;
        $updateSql = "update  tbl_system_field set disable_flag = 1 WHERE  type in (38,39,40)  and base = 1
            and id  in ('order_record_id','purchase_order_product_id','have_purchase_count','task_inbound_count','product_total_count'
                        'task_purchase_count','to_inbound_count','to_purchase_count','reach_count','check_outbound_count'
                       'check_purchase_count','reach_count','check_inbound_count','task_progress','notice_config');
update  tbl_system_field set is_editable = 0 WHERE is_list = 1 and id  in('record_remark','description') and type in (38,39,40) ;
update  tbl_system_field set name = '期望货好时间'  WHERE type = 38 and name='期望到货时间' and id ='expect_time';
update  tbl_system_field set field_type = 4 WHERE field_type = 10  and id = 'expect_time' and type in (38,39,40) ;
update  tbl_system_field set name = '单位'  WHERE   type in (38,39,40) and name='计量单位' and id ='product_unit';
update  tbl_system_field set name = '产品备注'  WHERE   type in (38,39,40) and name='产品行备注' and id ='record_remark' ;
update  tbl_system_field set  name = '出库数量'  WHERE   type =40 and  id = 'have_outbound_count';";
        $db->createCommand($updateSql)->execute();
    }

    public function actionRevertProductTransferField($clientId){
        $db = \ProjectActiveRecord::getDbByClientId($clientId);

        $deletePurchaseSql = "
delete  from tbl_custom_field where client_id = {$clientId} and id in ('status','last_comment','name',
    'purchase_order_no','purchase_product_count','need_purchase_count','todo_purchase_count','refer_purchase_order_id','delivery_date','have_inbound_count','todo_purchase_sum','product_total_sum','need_purchase_sum','have_purchase_sum','from_stock_sum') and type = 38;
update  tbl_custom_field set disable_flag = 0 WHERE  id  in ('task_progress','to_purchase_count','task_purchase_count','have_purchase_count','reach_count') and type = 38 and client_id = {$clientId};";
        $deleteInboundSql = "delete  from tbl_custom_field where client_id = {$clientId}  and type = 39  and id in ('status','last_comment','name','purchase_sum','todo_inbound_sum','have_inbound_sum','inbound_no','inbound_product_count','inbound_count','inbound_date',
    'need_inbound_count','todo_inbound_count','inbound_no','inbound_product_count','refer_purchase_inbound_id','inbound_count','inbound_date');
update  tbl_custom_field set disable_flag = 0 WHERE  id  in ('task_progress','to_inbound_count','task_inbound_count','reach_count') and type = 39 and  client_id = {$clientId};    
    ";
        $deleteOutboundSql = "delete  from tbl_custom_field where client_id = {$clientId}  and type = 40 and id in ('name','status','last_comment',
    'outbound_no','outbound_product_count','need_outbound_count','todo_outbound_count','refer_outbound_id','outbound_date','product_total_sum','todo_outbound_sum','have_outbound_sum','todo_outbound_sum','product_total_sum','have_outbound_sum');
update  tbl_custom_field set disable_flag = 0 WHERE  id  in ('task_progress','to_outbound_count','task_outbound_count','reach_count') and type = 40 and  client_id = {$clientId};    
update  tbl_custom_field set is_editable = 1 WHERE is_list = 1 and id  in('record_remark','description') and type in (38,39,40) and client_id = {$clientId};
update  tbl_custom_field set name = '期望到货时间'  WHERE type = 38 and name='期望货好时间' and id ='expect_time' and client_id = {$clientId};
update  tbl_custom_field set field_type = 10 WHERE field_type = 4  and id = 'expect_time' and type in (38,39,40) and client_id = {$clientId};
update  tbl_custom_field set name = '计量单位'  WHERE   type in (38,39,40) and name='单位' and id ='product_unit' and client_id = {$clientId};
update  tbl_custom_field set name = '产品行备注'  WHERE   type in (38,39,40) and name='产品备注' and id ='record_remark' and client_id = {$clientId};
update  tbl_custom_field set  name = '已出库数量'  WHERE   type =40 and  id = 'have_outbound_count' and client_id = {$clientId};
update  tbl_custom_field set  disable_flag = 1  WHERE   type =38 and  id = 'purchase_count' and client_id = {$clientId} and disable_flag=0;";
        $db->createCommand($deletePurchaseSql)->execute();
        $db->createCommand($deleteInboundSql)->execute();
        $db->createCommand($deleteOutboundSql)->execute();
    }

    //跟单协同需求 新增字段
    public function actionProductTransferField()
    {
        //模块需要新增的字段
        $module2addFieldMap = [
            Constants::TYPE_PRODUCT_TRANSFER_PURCHASE => ['name', 'status', 'last_comment'],
            Constants::TYPE_PRODUCT_TRANSFER_INBOUND => ['name', 'status', 'last_comment'],
            Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND => ['name', 'status', 'last_comment'],
            Constants::TYPE_PRODUCT_TRANSFER_OTHER => ['name', 'last_comment'],
        ];

        //模块新增字段对应的名字
        $fieldNameMap = [
           'name' => '任务名称',
           'status' => '任务状态',
           'last_comment' => '最新跟进',
        ];

        $baseField = [
            'id' => 'name',//外部给
            'type' => Constants::TYPE_PURCHASE_ORDER,   //外部给
            'group_id' => 1,
            'base' => 1,
            'name' => '任务名称',//外部给
            'field_type' => CustomFieldService::FIELD_TYPE_TEXT,//外部给
            'require' => 0,
            'edit_required' => 0,
            'disable_flag' => 0,
            'edit_hide' => 0,
            'default' => '',
            'edit_default' => 0,
            'hint' => '',
            'edit_hint' => 0,
            'is_exportable' => 0,
            'is_editable' => 1,
            'is_list' => 0,
            'columns' => 'name',//外部给
            'relation_type' => 0,
            'relation_field' => '',
            'relation_field_type' => 0,
            'relation_field_name' => '',
            'export_scenario' => '',
            'export_group' => '',
            'readonly' => 0,
            'ext_info' => '',
            'unique_check' => 0,
            'unique_prevent' => 1,
            'unique_message' => '',
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s'),
        ];

        $systemField=[];


        foreach ($module2addFieldMap as $module => $fieldIds) {
            foreach ($fieldIds as $fieldId) {
                $field = $baseField;
                $field['id'] = $field['columns'] = $fieldId;
                $field['type'] = $module;
                $field['name'] = $fieldNameMap[$fieldId]??'';

                if (empty($field['name'])) {
                    echo "{$module}-{$fieldId} 没有设置名称" . PHP_EOL;
                    continue;
                }

                //last_comment field_type 不一样
                if ($fieldId == 'last_comment') {
                    $field['field_type'] = CustomFieldService::FIELD_TYPE_RICH_TEXT;
                    $field['is_editable'] = 0;
                }

                $systemField[] =$field;
            }

        }

        return $systemField;
    }

    public function actionAddShippingInvoiceField($clientId = 14119)
    {
        $fieldsGroup = [
            Constants::TYPE_SHIPPING_INVOICE => array_values($this->shippingSetting()),
        ];
        foreach ($fieldsGroup as $moduleType => $fields) {
            \common\library\custom_field\Helper::addField($clientId, $fields);
            // 调整字段顺序
            $sorter = new \common\library\util\sort\Sorter(new CustomFieldSortRepository($clientId, $moduleType));
            $sorter->setResort(true);
            foreach ($fields as $field) {
                $sorter->setId($field['id'], true);
                $sorter->setSortField('order');
                $orderNum = $sorter->after($field['after']);
                $sorter->setSortField('app_order');
                $appOrderNum = $sorter->after($field['after']);
            }
        }
    }

    public function actionDelOPSysField()
    {
        $client_id = 14787;
        $type = 57;
        $db = \ProjectActiveRecord::getDbByClientId($client_id);
        $sql = "delete from tbl_custom_field  where client_id ='$client_id' and type = {$type}";
        $db->createCommand($sql)->execute();
        $sql = "delete from tbl_field_group  where client_id ='$client_id' and type = {$type}";
        $db->createCommand($sql)->execute();
        $sql = "delete from tbl_field_export_setting  where client_id ='$client_id' and type = {$type}";
        $db->createCommand($sql)->execute();
        $db = \Yii::app()->db;
        $sql = "delete from tbl_system_field  where  type = {$type}";
        $db->createCommand($sql)->execute();
        echo "执行删除完毕";
    }
    

    public function actionAddOrderOutbound($clientId=14119)
    {
        $fields_setting = $this->referShippingInvoice();
        $afterFields[\Constants::TYPE_SALE_OUTBOUND_INVOICE] = [
            'refer_shipping_invoice_id' => 'order_no',
            'refer_shipping_invoice_no' => 'refer_shipping_invoice_id',
            'shipping_count' => 'sale_count'
        ];
        echo '---开始处理client_id：', $clientId, PHP_EOL;
        \common\library\custom_field\Helper::addField($clientId, $fields_setting, $afterFields);
        echo '成功处理client_id：', $clientId, PHP_EOL;
    }

    public function actionAddProductField($clientId = 14119)
    {
        $fields_setting = $this->productSizeFieldSetting();
        $afterFields[\Constants::TYPE_PRODUCT] = [
            'category_ids' => 'hs_code',
            'hs_code' => 'customs_name',
            'customs_name' => 'customs_cn_name'
        ];
        \common\library\custom_field\Helper::addField($clientId, $fields_setting, $afterFields);
        $sql = "update  tbl_field_group set group_id = 8 WHERE type=1  and id in ('category_ids','hs_code') and client_id = {$clientId};";
        $db = \ProjectActiveRecord::getDbByClientId($clientId);
        $db->createCommand($sql)->execute();
    }

    public function actionAddProductTransfer($clientId = 14119)
    {
        $field_setting = $this->shippingTransferFieldSetting();
        $afterFields[\Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND] = [
            'order_id' => 'shipping_invoice_id',
            'refer_shipping_record_id'=> 'shipping_invoice_id',
            'shipping_count' => 'product_unit',
        ];
        \common\library\custom_field\Helper::addField($clientId, $field_setting, $afterFields);
    }

    //供应商、货代、费用单、付款单 字段新增
    public function actionAddMultiCapitalField()
    {
        //模块需要新增的字段
        $addFieldTypeMap = [
            Constants::TYPE_SUPPLIER => [
                'partner_account_name' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'name' => '收款账户名',
                    'require' => 0,
                    'hint' => '收款账户别名，如对公账户、微信、支付宝...',
                    'group_id' => CustomFieldService::SUPPLIER_GROUP_PAYMENT,
                    'before' => 'bank_account',
                ],
                'partner_account_id' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '收款账户id',
                    'disable_flag' => 1,
                    'group_id' => CustomFieldService::SUPPLIER_GROUP_PAYMENT,
                    'before' => 'partner_account_name',
                ],
                'account_image' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_IMAGE,
                    'name' => '账户图片',
                    'group_id' => CustomFieldService::SUPPLIER_GROUP_PAYMENT,
                    'after' => 'bank_name',
                ]
            ],

            Constants::TYPE_FORWARDER => [
                'partner_account_name' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'name' => '收款账户名',
                    'require' => 0,
                    'hint' => '收款账户别名，如对公账户、微信、支付宝...',
                    'group_id' => CustomFieldService::FORWARDER_GROUP_PAYMENT,
                    'before' => 'bank_account',
                ],
                'partner_account_id' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '收款账户id',
                    'disable_flag' => 1,
                    'group_id' => CustomFieldService::FORWARDER_GROUP_PAYMENT,
                    'before' => 'partner_account_name',
                ],
                'account_image' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_IMAGE,
                    'name' => '账户图片',
                    'group_id' => CustomFieldService::FORWARDER_GROUP_PAYMENT,
                    'after' => 'bank_name',
                ],
                'is_main_flag' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_OTHER,
                    'name' => '是否主收款账号',
                    'disable_flag' => 1,
                    'group_id' => CustomFieldService::FORWARDER_GROUP_PAYMENT,
                ]
            ],

            Constants::TYPE_COST_INVOICE => [
                'partner_account_name' => [
                    'name' => '收款账户名',
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'require' => 0,
                    'edit_hide' => 0,
                    'edit_hint' => 0,
                    'edit_default' => 0,
                    'hint' => '收款账户别名，如对公账户、微信、支付宝...',
                    'group_id' => CustomFieldService::COST_INVOICE_GROUP_PAYMENT,
                    'before' => 'bank_account',
                ],
                'partner_account_id' => [
                    'name' => '收款账户id',
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'require' => 0,
                    'edit_hide' => 0,
                    'edit_hint' => 0,
                    'edit_default' => 0,
                    'group_id' => CustomFieldService::COST_INVOICE_GROUP_PAYMENT,
                    'before' => 'partner_account_name',
                ],
                'account_image' => [
                    'name' => '账户图片',
                    'field_type' => CustomFieldService::FIELD_TYPE_IMAGE,
                    'require' => 0,
                    'edit_required' => 1,
                    'edit_hide' => 1,
                    'edit_hint' => 1,
                    'edit_default' => 0,
                    'group_id' => CustomFieldService::COST_INVOICE_GROUP_PAYMENT,
                    'after' => 'bank_name',
                ],
            ],
            Constants::TYPE_PAYMENT_INVOICE => [
                'partner_account_name' => [
                    'name' => '收款账户名',
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'require' => 0,
                    'edit_hide' => 0,
                    'edit_hint' => 0,
                    'edit_default' => 0,
                    'hint' => '收款账户别名，如对公账户、微信、支付宝...',
                    'group_id' => CustomFieldService::PAYMENT_INVOICE_GROUP_PAYMENT,
                    'before' => 'bank_account',
                ],
                'partner_account_id' => [
                    'name' => '收款账户id',
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'require' => 0,
                    'edit_hide' => 0,
                    'edit_hint' => 0,
                    'edit_default' => 0,
                    'group_id' => CustomFieldService::PAYMENT_INVOICE_GROUP_PAYMENT,
                    'before' => 'partner_account_name',
                ],
                'account_image' => [
                    'name' => '账户图片',
                    'field_type' => CustomFieldService::FIELD_TYPE_IMAGE,
                    'require' => 0,
                    'edit_required' => 1,
                    'edit_hide' => 1,
                    'edit_hint' => 1,
                    'edit_default' => 0,
                    'group_id' => CustomFieldService::PAYMENT_INVOICE_GROUP_PAYMENT,
                    'after' => 'bank_name',
                ],
            ],
        ];

        $baseField = [
            'id' => 'name',//外部给
            'type' => Constants::TYPE_PURCHASE_ORDER,   //外部给
            'group_id' => 1,
            'base' => 1,
            'name' => '任务名称',//外部给
            'field_type' => CustomFieldService::FIELD_TYPE_TEXT,//外部给
            'require' => 0,
            'edit_required' => 0,
            'disable_flag' => 0,
            'edit_hide' => 0,
            'default' => '',
            'edit_default' => 0,
            'hint' => '',
            'edit_hint' => 0,
            'is_exportable' => 0,
            'is_editable' => 1,
            'is_list' => 0,
            'columns' => 'name',//外部给
            'relation_type' => 0,
            'relation_field' => '',
            'relation_field_type' => 0,
            'relation_field_name' => '',
            'export_scenario' => '',
            'export_group' => '',
            'readonly' => 0,
            'ext_info' => '',
            'unique_check' => 0,
            'unique_prevent' => 1,
            'unique_message' => '',
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s'),
        ];

        $systemField=[];

        foreach ($addFieldTypeMap as $module => $fieldIds) {
            foreach ($fieldIds as $fieldId => $fieldConfig) {
                $field = $baseField;
                $field['id'] = $field['columns'] = $fieldId;
                $field['type'] = $module;

                foreach ($fieldConfig as $key => $value) {
                    $field[$key] = $value;
                }

                if (empty($field['name'])) {
                    echo "{$module}-{$fieldId} 没有设置名称" . PHP_EOL;
                    continue;
                }

                $systemField[] =$field;
            }
        }

        return $systemField;
    }


    //采购订单、费用单 字段新增
    public function getPurchaseAndCostCurrencyField()
    {
        //模块需要新增的字段
        $addFieldTypeMap = [
            Constants::TYPE_PURCHASE_ORDER => [
                'product_total_amount_rmb' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '产品总金额(CNY)',
                    'require' => 0,
                    'disable_flag' => 0,
                    'group_id' => CustomFieldService::PURCHASE_ORDER_GROUP_CHARGE,
                    'is_editable' => 0,
                    'is_exportable' => 1,
                    'export_scenario' => '22',
                    'export_group' => '1',
                    'before' => 'product_total_amount',
                ],
                'product_total_amount_usd' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '产品总金额(USD)',
                    'disable_flag' => 0,
                    'group_id' => CustomFieldService::PURCHASE_ORDER_GROUP_CHARGE,
                    'is_editable' => 0,
                    'is_exportable' => 1,
                    'export_scenario' => '22',
                    'export_group' => '1',
                    'before' => 'product_total_amount_rmb',
                ],
                'payment_amount_rmb' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '已付款金额(CNY)',
                    'disable_flag' => 0,
                    'group_id' => CustomFieldService::PURCHASE_ORDER_GROUP_CHARGE,
                    'is_editable' => 0,
                    'is_exportable' => 1,
                    'export_scenario' => '22',
                    'export_group' => '1',
                    'before' => 'payment_amount',
                ],
                'payment_amount_usd' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '已付款金额(USD)',
                    'disable_flag' => 0,
                    'group_id' => CustomFieldService::PURCHASE_ORDER_GROUP_CHARGE,
                    'is_editable' => 0,
                    'is_exportable' => 1,
                    'export_scenario' => '22',
                    'export_group' => '1',
                    'before' => 'payment_amount_rmb',
                ],
                'payment_wait_amount_rmb' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '待付款金额(CNY)',
                    'disable_flag' => 0,
                    'group_id' => CustomFieldService::PURCHASE_ORDER_GROUP_CHARGE,
                    'is_editable' => 0,
                    'is_exportable' => 1,
                    'export_scenario' => '22',
                    'export_group' => '1',
                    'before' => 'payment_wait_amount',
                ],
                'payment_wait_amount_usd' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '待付款金额(USD)',
                    'disable_flag' => 0,
                    'group_id' => CustomFieldService::PURCHASE_ORDER_GROUP_CHARGE,
                    'is_editable' => 0,
                    'is_exportable' => 1,
                    'export_scenario' => '22',
                    'export_group' => '1',
                    'before' => 'payment_wait_amount_rmb',
                ]
            ],
            Constants::TYPE_COST_INVOICE => [
                'amount_rmb' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '费用金额(CNY)',
                    'require' => 0,
                    'disable_flag' => 0,
                    'group_id' => CustomFieldService::COST_INVOICE_GROUP_BASIC,
                    'is_editable' => 0,
                    'before' => 'amount',
                ],
                'amount_usd' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '费用金额(USD)',
                    'disable_flag' => 0,
                    'group_id' => CustomFieldService::COST_INVOICE_GROUP_BASIC,
                    'is_editable' => 0,
                    'before' => 'amount_rmb',
                ],
                'payment_amount_rmb' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '已付款金额(CNY)',
                    'disable_flag' => 0,
                    'group_id' => CustomFieldService::COST_INVOICE_GROUP_BASIC,
                    'is_editable' => 0,
                    'before' => 'payment_amount',
                ],
                'payment_amount_usd' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '已付款金额(USD)',
                    'disable_flag' => 0,
                    'group_id' => CustomFieldService::COST_INVOICE_GROUP_BASIC,
                    'is_editable' => 0,
                    'before' => 'payment_amount_rmb',
                ],
                'payment_wait_amount_rmb' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '待付款金额(CNY)',
                    'disable_flag' => 0,
                    'group_id' => CustomFieldService::COST_INVOICE_GROUP_BASIC,
                    'is_editable' => 0,
                    'before' => 'payment_wait_amount',
                ],
                'payment_wait_amount_usd' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '待付款金额(USD)',
                    'disable_flag' => 0,
                    'group_id' => CustomFieldService::COST_INVOICE_GROUP_BASIC,
                    'is_editable' => 0,
                    'before' => 'payment_wait_amount_rmb',
                ]
            ],
        ];

        $baseField = [
            'id' => 'name',//外部给
            'type' => Constants::TYPE_PURCHASE_ORDER,   //外部给
            'group_id' => 1,
            'base' => 1,
            'name' => '任务名称',//外部给
            'field_type' => CustomFieldService::FIELD_TYPE_TEXT,//外部给
            'require' => 0,
            'edit_required' => 0,
            'disable_flag' => 0,
            'edit_hide' => 0,
            'default' => '',
            'edit_default' => 0,
            'hint' => '',
            'edit_hint' => 0,
            'is_exportable' => 0,
            'is_editable' => 1,
            'is_list' => 0,
            'columns' => 'name',//外部给
            'relation_type' => 0,
            'relation_field' => '',
            'relation_field_type' => 0,
            'relation_field_name' => '',
            'export_scenario' => '',
            'export_group' => '',
            'readonly' => 0,
            'ext_info' => '',
            'unique_check' => 0,
            'unique_prevent' => 1,
            'unique_message' => '',
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s'),
        ];

        $systemField=[];

        foreach ($addFieldTypeMap as $module => $fieldIds) {
            foreach ($fieldIds as $fieldId => $fieldConfig) {
                $field = $baseField;
                $field['id'] = $field['columns'] = $fieldId;
                $field['type'] = $module;

                foreach ($fieldConfig as $key => $value) {
                    $field[$key] = $value;
                }

                if (empty($field['name'])) {
                    echo "{$module}-{$fieldId} 没有设置名称" . PHP_EOL;
                    continue;
                }

                $systemField[] =$field;
            }
        }

        return $systemField;
    }

    //大宽表迁移，新增字段 @link https://xmkm.yuque.com/armee3/dzbqgm/lndubg9u3r7rgt7e#RVu2J
    public function getAddBigTblField()
    {
        //模块需要新增的字段
        $addFieldTypeMap = [
            Constants::TYPE_ORDER => [
                'exchange_rate_usd' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '汇率（兑USD）',
                    'require' => 1,
                    'group_id' => CustomFieldService::ORDER_GROUP_BASIC,
                    'edit_hint' => 1,
                    'is_exportable' => 1,
                    'export_scenario' => 2,
                    'export_group' => 1,
                    'after' => 'exchange_rate',
                ],
                'order_gross_margin_cny' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '产品毛利总计（CNY）',
                    'disable_flag' => 1,
                    'is_exportable' => 1,
                    'export_scenario' => 2,
                    'export_group' => 1,
                    'group_id' => CustomFieldService::ORDER_GROUP_FEE,
                    'after' => 'order_gross_margin',
                ],
                'order_gross_margin_usd' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '产品毛利总计（USD）',
                    'disable_flag' => 1,
                    'is_exportable' => 1,
                    'export_scenario' => 2,
                    'export_group' => 1,
                    'group_id' => CustomFieldService::ORDER_GROUP_FEE,
                    'after' => 'order_gross_margin_cny',
                ],

                'addition_cost_amount_rmb' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '附加费用总金额（CNY）',
                    'disable_flag' => 1,
                    'is_exportable' => 1,
                    'export_scenario' => 2,
                    'export_group' => 1,
                    'group_id' => CustomFieldService::ORDER_GROUP_FEE,
                    'after' => 'addition_cost_amount',
                ],
                'addition_cost_amount_usd' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '附加费用总金额（USD）',
                    'disable_flag' => 1,
                    'is_exportable' => 1,
                    'export_scenario' => 2,
                    'export_group' => 1,
                    'group_id' => CustomFieldService::ORDER_GROUP_FEE,
                    'after' => 'addition_cost_amount_rmb',
                ],

            ],
        ];

        $baseField = [
            'id' => 'name',//外部给
            'type' => Constants::TYPE_PURCHASE_ORDER,   //外部给
            'group_id' => 1,
            'base' => 1,
            'name' => '任务名称',//外部给
            'field_type' => CustomFieldService::FIELD_TYPE_TEXT,//外部给
            'require' => 0,
            'edit_required' => 0,
            'disable_flag' => 0,
            'edit_hide' => 0,
            'default' => '',
            'edit_default' => 0,
            'hint' => '',
            'edit_hint' => 0,
            'is_exportable' => 0,
            'is_editable' => 1,
            'is_list' => 0,
            'columns' => 'name',//外部给
            'relation_type' => 0,
            'relation_field' => '',
            'relation_field_type' => 0,
            'relation_field_name' => '',
            'export_scenario' => '',
            'export_group' => '',
            'readonly' => 0,
            'ext_info' => '',
            'unique_check' => 0,
            'unique_prevent' => 1,
            'unique_message' => '',
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s'),
        ];

        $systemField=[];

        foreach ($addFieldTypeMap as $module => $fieldIds) {
            foreach ($fieldIds as $fieldId => $fieldConfig) {
                $field = $baseField;
                $field['id'] = $field['columns'] = $fieldId;
                $field['type'] = $module;

                foreach ($fieldConfig as $key => $value) {
                    $field[$key] = $value;
                }

                if (empty($field['name'])) {
                    echo "{$module}-{$fieldId} 没有设置名称" . PHP_EOL;
                    continue;
                }

                $systemField[] =$field;
            }
        }

        return $systemField;
    }


    public function AddInvoiceProductPartField()
    {
        //模块需要新增的字段
        $addFieldTypeMap = [
            Constants::TYPE_QUOTATION => [
                'parts_total_count' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '配件总数量',
                    'is_list' => 0,
                    'is_editable' => 0,
                    'is_exportable' => 1,
                    'export_scenario' => '3',
                    'export_group' => FieldExportService::QUOTATION_GROUP_BASIC,
                    'group_id' => CustomFieldService::QUOTATION_GROUP_PRODUCT,
                ],
                'product_total_count_no_parts' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '产品总数量（不含配件）',
                    'is_list' => 0,
                    'is_editable' => 0,
                    'is_exportable' => 1,
                    'export_scenario' => '3',
                    'export_group' => FieldExportService::QUOTATION_GROUP_BASIC,
                    'group_id' => CustomFieldService::QUOTATION_GROUP_PRODUCT,
                ],
                'is_master_product' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '是否主产品',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::QUOTATION_GROUP_PRODUCT,
                ],
                'master_id' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '归属产品id',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::QUOTATION_GROUP_PRODUCT,
                ],
                'ratio' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '配比',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::QUOTATION_GROUP_PRODUCT,
                ],
                'master_group_id' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '主配产品分组id',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::QUOTATION_GROUP_PRODUCT,
                ],
            ],
            Constants::TYPE_ORDER => [
                'parts_total_count' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '配件总数量',
                    'is_list' => 0,
                    'is_editable' => 0,
                    'is_exportable' => 1,
                    'export_scenario' => '2',
                    'export_group' => FieldExportService::ORDER_GROUP_BASIC,
                    'group_id' => CustomFieldService::ORDER_GROUP_PRODUCT,
                ],
                'product_total_count_no_parts' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '产品总数量（不含配件）',
                    'is_list' => 0,
                    'is_editable' => 0,
                    'is_exportable' => 1,
                    'export_scenario' => '2',
                    'export_group' => FieldExportService::ORDER_GROUP_BASIC,
                    'group_id' => CustomFieldService::ORDER_GROUP_PRODUCT,
                ],
                'is_master_product' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '是否主产品',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::ORDER_GROUP_PRODUCT,
                ],
                'master_id' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '归属产品id',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::ORDER_GROUP_PRODUCT,
                ],
                'ratio' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '配比',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::ORDER_GROUP_PRODUCT,
                ],
                'master_group_id' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '主配产品分组id',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::ORDER_GROUP_PRODUCT,
                ],
            ],

            Constants::TYPE_PURCHASE_ORDER => [
                'purchase_type' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '采购类型',
                    'is_editable' => 0,
                    'disable_flag' => 1,
                    'group_id' => CustomFieldService::PURCHASE_ORDER_GROUP_BASIC,
                ],
                'parts_total_count' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '配件产品总数量',
                    'is_list' => 0,
                    'is_editable' => 0,
                    'is_exportable' => 1,
                    'export_scenario' => '22',
                    'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_BASIC,
                    'group_id' => CustomFieldService::PURCHASE_ORDER_GROUP_PRODUCT,
                ],
                'product_total_count_no_parts' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '产品总数量（不含配件）',
                    'is_list' => 0,
                    'is_editable' => 0,
                    'is_exportable' => 1,
                    'export_scenario' => '22',
                    'export_group' => FieldExportService::PURCHASE_ORDER_GROUP_BASIC,
                    'group_id' => CustomFieldService::PURCHASE_ORDER_GROUP_PRODUCT,
                ],
                'is_master_product' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '是否主产品',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::PURCHASE_ORDER_GROUP_PRODUCT,
                ],
                'master_id' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '归属产品id',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::PURCHASE_ORDER_GROUP_PRODUCT,
                ],
                'master_group_id' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '主配产品分组id',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::PURCHASE_ORDER_GROUP_PRODUCT,
                ],
            ],

            Constants::TYPE_PRODUCT_TRANSFER_PURCHASE => [
                'purchase_type' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '采购类型',
                    'is_editable' => 0,
                    'disable_flag' => 1,
                    'group_id' => CustomFieldService::PRODUCT_TRANSFER_PURCHASE_GROUP_BASIC,
                ],
                'parts_total_count' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '配件产品总数量',
                    'is_list' => 0,
                    'is_editable' => 0,
                    'group_id' => CustomFieldService::PRODUCT_TRANSFER_PURCHASE_GROUP_PRODUCT,
                ],
                'product_total_count_no_parts' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '产品总数量（不含配件）',
                    'is_list' => 0,
                    'is_editable' => 0,
                    'group_id' => CustomFieldService::PRODUCT_TRANSFER_PURCHASE_GROUP_PRODUCT,
                ],
                'is_master_product' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '是否主产品',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::PRODUCT_TRANSFER_PURCHASE_GROUP_PRODUCT,
                ],
                'master_id' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '归属产品id',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::PRODUCT_TRANSFER_PURCHASE_GROUP_PRODUCT,
                ],
                'master_group_id' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '主配产品分组id',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::PRODUCT_TRANSFER_PURCHASE_GROUP_PRODUCT,
                ],
            ],

            Constants::TYPE_PRODUCT_TRANSFER_INBOUND => [
                'is_master_product' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '是否主产品',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::PRODUCT_TRANSFER_INBOUND_GROUP_PRODUCT,
                ],
                'master_id' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '归属产品id',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::PRODUCT_TRANSFER_INBOUND_GROUP_PRODUCT,
                ],
                'master_group_id' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '主配产品分组id',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::PRODUCT_TRANSFER_INBOUND_GROUP_PRODUCT,
                ],
            ],

            Constants::TYPE_PURCHASE_INBOUND_INVOICE => [
                'is_master_product' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '是否主产品',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::PURCHASE_INBOUND_GROUP_PRODUCT,
                ],
                'master_id' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '归属产品id',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::PURCHASE_INBOUND_GROUP_PRODUCT,
                ],
                'master_group_id' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '主配产品分组id',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::PURCHASE_INBOUND_GROUP_PRODUCT,
                ],
            ],

            Constants::TYPE_SHIPPING_INVOICE => [
                'parts_total_count' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '配件产品总数量',
                    'is_list' => 0,
                    'is_editable' => 0,
                    'is_exportable' => 1,
                    'export_scenario' => Constants::TYPE_SHIPPING_INVOICE,
                    'export_group' => FieldExportService::SHIPPING_INVOICE_GROUP_BASIC,
                    'group_id' => CustomFieldService::SHIPPING_INVOICE_GROUP_PRODUCT_BASE,
                ],
                'product_total_count_no_parts' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '产品总数量（不含配件）',
                    'is_list' => 0,
                    'is_editable' => 0,
                    'is_exportable' => 1,
                    'export_scenario' => Constants::TYPE_SHIPPING_INVOICE,
                    'export_group' => FieldExportService::SHIPPING_INVOICE_GROUP_BASIC,
                    'group_id' => CustomFieldService::SHIPPING_INVOICE_GROUP_PRODUCT_BASE,
                ],
                'is_master_product' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '是否主产品',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::SHIPPING_INVOICE_GROUP_PRODUCT_BASE,
                ],
                'master_id' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '归属产品id',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::SHIPPING_INVOICE_GROUP_PRODUCT_BASE,
                ],
                'master_group_id' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '主配产品分组id',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::SHIPPING_INVOICE_GROUP_PRODUCT_BASE,
                ],
            ],

            Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND => [
                'parts_total_count' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '配件产品总数量',
                    'is_list' => 0,
                    'is_editable' => 0,
                    'group_id' => CustomFieldService::PRODUCT_TRANSFER_OUTBOUND_GROUP_PRODUCT,
                ],
                'product_total_count_no_parts' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '产品总数量（不含配件）',
                    'is_list' => 0,
                    'is_editable' => 0,
                    'group_id' => CustomFieldService::PRODUCT_TRANSFER_OUTBOUND_GROUP_PRODUCT,
                ],
                'is_master_product' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '是否主产品',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::PRODUCT_TRANSFER_OUTBOUND_GROUP_PRODUCT,
                ],
                'master_id' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '归属产品id',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::PRODUCT_TRANSFER_OUTBOUND_GROUP_PRODUCT,
                ],
                'master_group_id' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '主配产品分组id',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::PRODUCT_TRANSFER_OUTBOUND_GROUP_PRODUCT,
                ],
            ],

            Constants::TYPE_SALE_OUTBOUND_INVOICE => [
                'product_total_count' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '产品总数量',
                    'is_list' => 0,
                    'is_editable' => 0,
                    'is_exportable' => 1,
                    'export_scenario' => Constants::TYPE_SALE_OUTBOUND_INVOICE,
                    'export_group' => FieldExportService::SALE_OUTBOUND_INVOICE_GROUP_BASIC,
                    'group_id' => CustomFieldService::SALE_OUTBOUND_GROUP_PRODUCT,
                ],
                'parts_total_count' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '配件产品总数量',
                    'is_list' => 0,
                    'is_editable' => 0,
                    'is_exportable' => 1,
                    'export_scenario' => Constants::TYPE_SALE_OUTBOUND_INVOICE,
                    'export_group' => FieldExportService::SALE_OUTBOUND_INVOICE_GROUP_BASIC,
                    'group_id' => CustomFieldService::SALE_OUTBOUND_GROUP_PRODUCT,
                ],
                'product_total_count_no_parts' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '产品总数量（不含配件）',
                    'is_list' => 0,
                    'is_editable' => 0,
                    'is_exportable' => 1,
                    'export_scenario' => Constants::TYPE_SALE_OUTBOUND_INVOICE,
                    'export_group' => FieldExportService::SALE_OUTBOUND_INVOICE_GROUP_BASIC,
                    'group_id' => CustomFieldService::SALE_OUTBOUND_GROUP_PRODUCT,
                ],
                'is_master_product' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '是否主产品',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::SALE_OUTBOUND_GROUP_PRODUCT,
                ],
                'master_id' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '归属产品id',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::SALE_OUTBOUND_GROUP_PRODUCT,
                ],
                'master_group_id' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '主配产品分组id',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::SALE_OUTBOUND_GROUP_PRODUCT,
                ],
            ],

            Constants::TYPE_OTHER_OUTBOUND_INVOICE => [
                'parts_total_count' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '配件产品总数量',
                    'is_list' => 0,
                    'is_editable' => 0,
                    'is_exportable' => 1,
                    'export_scenario' => Constants::TYPE_OTHER_OUTBOUND_INVOICE,
                    'export_group' => FieldExportService::OTHER_OUTBOUND_INVOICE_GROUP_BASIC,
                    'group_id' => CustomFieldService::OTHER_OUTBOUND_GROUP_PRODUCT,
                ],
                'product_total_count_no_parts' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '产品总数量（不含配件）',
                    'is_list' => 0,
                    'is_editable' => 0,
                    'is_exportable' => 1,
                    'export_scenario' => Constants::TYPE_OTHER_OUTBOUND_INVOICE,
                    'export_group' => FieldExportService::OTHER_OUTBOUND_INVOICE_GROUP_BASIC,
                    'group_id' => CustomFieldService::OTHER_OUTBOUND_GROUP_PRODUCT,
                ],
                'is_master_product' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '是否主产品',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::OTHER_OUTBOUND_GROUP_PRODUCT,
                ],
                'master_id' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '归属产品id',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::OTHER_OUTBOUND_GROUP_PRODUCT,
                ],
                'master_group_id' => [
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'name' => '主配产品分组id',
                    'disable_flag' => 1,
                    'is_list' => 1,
                    'group_id' => CustomFieldService::OTHER_OUTBOUND_GROUP_PRODUCT,
                ],
            ],
        ];

        $baseField = [
            'id' => 'name',//外部给
            'type' => Constants::TYPE_PURCHASE_ORDER,   //外部给
            'group_id' => 1,
            'base' => 1,
            'name' => '任务名称',//外部给
            'field_type' => CustomFieldService::FIELD_TYPE_TEXT,//外部给
            'require' => 0,
            'edit_required' => 0,
            'disable_flag' => 0,
            'edit_hide' => 0,
            'default' => '',
            'edit_default' => 0,
            'hint' => '',
            'edit_hint' => 0,
            'is_exportable' => 0,
            'is_editable' => 1,
            'is_list' => 0,
            'columns' => 'name',//外部给
            'relation_type' => 0,
            'relation_field' => '',
            'relation_field_type' => 0,
            'relation_field_name' => '',
            'export_scenario' => '',
            'export_group' => '',
            'readonly' => 0,
            'ext_info' => '',
            'unique_check' => 0,
            'unique_prevent' => 1,
            'unique_message' => '',
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s'),
        ];

        $systemField=[];

        foreach ($addFieldTypeMap as $module => $fieldIds) {
            foreach ($fieldIds as $fieldId => $fieldConfig) {
                $field = $baseField;
                $field['id'] = $field['columns'] = $fieldId;
                $field['type'] = $module;

                foreach ($fieldConfig as $key => $value) {
                    $field[$key] = $value;
                }

                if (empty($field['name'])) {
                    echo "{$module}-{$fieldId} 没有设置名称" . PHP_EOL;
                    continue;
                }

                $systemField[] =$field;
            }
        }

        return $systemField;
    }

}