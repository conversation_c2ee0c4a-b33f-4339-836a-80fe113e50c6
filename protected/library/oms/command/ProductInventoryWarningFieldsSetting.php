<?php


namespace common\library\oms\command;
use common\library\custom_field\CustomFieldService;
use Constants;

trait ProductInventoryWarningFieldsSetting
{
    public function productInventoryWarningFieldsSetting()
    {
        //库存预警模块需要新增的字段
        $settings = [
            /********************* 产品信息 *************************/
            [
                'id'         => 'images',
                'name'       => '产品图片',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_IMAGE, //图片类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'product_no',
                'name'       => '产品编号',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'name',
                'name'       => '产品名称',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'cn_name',
                'name'       => '中文产品名称',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'model',
                'name'       => '产品型号',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'sku_attributes',
                'name'       => '产品规格',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏 
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'unit',
                'name'       => '计量单位',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'group_id',
                'name'       => '产品分组',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'description',
                'name'       => '产品描述',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'package_size',
                'name'       => '包装尺寸',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'package_volume',
                'name'       => '包装体积',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'package_gross_weight',
                'name'       => '包装毛重',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'count_per_package',
                'name'       => '每包装产品数',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'package_remark',
                'name'       => '包装说明',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'category_ids',
                'name'       => '产品类目',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'hs_code',
                'name'       => '海关编码',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'info_json',
                'name'       => '产品属性',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'product_remark',
                'name'       => '产品备注',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'form_url',
                'name'       => '产品链接',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'create_type',
                'name'       => '创建方式',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'create_user',
                'name'       => '创建人',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'create_time',
                'name'       => '创建时间',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_DATE, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'update_user',
                'name'       => '修改人',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'update_time',
                'name'       => '更新时间',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_DATE, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'product_size',
                'name'       => '产品尺寸（长*宽*高cm）',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'product_volume',
                'name'       => '产品体积',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'customs_cn_name',
                'name'       => '报关中文名',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'customs_name',
                'name'       => '报关英文名',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'package_unit',
                'name'       => '包装单位',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'product_net_weight',
                'name'       => '产品净重',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'tax_refund_rate',
                'name'       => '退税率',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'carton_size',
                'name'       => '单箱尺寸(长*宽*高cm)',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'vat_rate',
                'name'       => '增值税率',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'carton_volume',
                'name'       => '单箱体积',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'carton_net_weight',
                'name'       => '单箱净重',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'product_type',
                'name'       => '产品类型',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'carton_gross_weight',
                'name'       => '单箱毛重',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'count_per_carton',
                'name'       => '每箱产品数',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'package_type',
                'name'       => '包装方式',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                 'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'warning_status',
                'name'       => '预警状态',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'limit_count',
                'name'       => '最低库存',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 1,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'warehouse_id',
                'name'       => '仓库',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'enable_count',
                'name'       => '可用库存',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER, //字段类型
                'is_list' => 0,  //是否明细
            ],
            [
                'id'         => 'real_count',
                'name'       => '实际库存',
                'require'    => 0,   //是否必填
                'hint'       => '',  //字段提示
                'edit_hint'  => 0,//是否可编辑字段提示：0-否，1-是
                'group_id'   => CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC,
                'edit_required' => 0, //是否可以编辑必填
                'is_exportable' => 1,  //是否可以导出
                'disable_flag'  => 0,//前端是否隐藏，1隐藏，0不隐藏
                'edit_hide' => 0,// 是否可隐藏，1可以，0否
                'edit_default' => 0,// 是否可编辑默认值，1可以，0否
                'is_editable' => 0,//是否可编辑：0-否，1-是
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER, //字段类型
                'is_list' => 0,  //是否明细
            ]
        ];
        $beforeFields='';
        $order = count($settings);
        foreach ($settings as &$setting) {
            $setting['type'] = \Constants::TYPE_PRODUCT_INVENTORY_WARNING;
            $setting['base'] = 1;
            $setting['default'] = $setting['default'] ?? '';
            $setting['columns'] = $setting['columns']??$setting['id'];
            $setting['relation_type'] = 0;
            $setting['relation_field'] = '';
            $setting['relation_field_type'] = 0;
            $setting['relation_field_name'] = '';
            $setting['export_scenario'] = 66;
            $setting['export_group'] = $setting['group_id'];
            $setting['order'] = $order;
            $setting['app_order'] = $order;
            $setting['readonly']=$setting['readonly']??0;
            $setting['after'] = $beforeFields;
            $beforeFields = $setting['id'];
            $order--;
        }

        return $settings;
    }
}