<?php
namespace common\library\oms\inbound_invoice\purchase;

use common\library\exchange_rate\ExchangeRateService;
use common\library\oms\common\UniqueSerialAdapter;
use common\library\oms\inbound_invoice\InboundInvoice;
use common\library\oms\inbound_invoice\purchase\record\BatchPurchaseInboundRecord;
use common\library\oms\common\OmsConstant;
use common\library\privilege_v3\field\BaseObjectPrivilegeField;
use common\library\privilege_v3\PrivilegeConstants;

/**
 * Class PurchaseInboundInvoice
 * @package common\library\oms\inbound_invoice\purchase;
 * @property mixed parts_total_count
 * @property mixed product_total_count_no_parts
 * @method  PurchaseInboundInvoiceFormatter getFormatter()
 * @method  PurchaseInboundInvoiceOperator getOperator()
 */
class PurchaseInboundInvoice extends InboundInvoice
{
    use InitPurchaseInboundInvoiceMetadata;
    use BaseObjectPrivilegeField;

    public function initDefaultAttributes()
    {
        $this->type = \Constants::TYPE_PURCHASE_INBOUND_INVOICE;
        parent::initDefaultAttributes();
    }

    /**
     * 采购入库单  referId 关联供应商
     * @param $supplier_id
     */
    public function setSupplierId($supplier_id)
    {
        $this->refer_id = $supplier_id;
        $this->refer_type = \Constants::TYPE_SUPPLIER;
    }

    /**
     * 新建入库明细
     * @return bool
     * @throws \xiaoman\orm\exception\OrmException
     */
    public function createRecord()
    {
        $partsProductMap = [];
        $recordList = array_reduce($this->recordList,function ($carry,$value) use (&$partsProductMap){
            //同步主对象属性到子对象
            $recordId = $value['inbound_record_id'] = \ProjectActiveRecord::produceAutoIncrementId();
            $value['status'] = $this->status;
            $value['type'] = $this->type;
            $value['warehouse_invoice_time'] = $this->warehouse_invoice_time;
            $value['inbound_invoice_id'] = $this->getObjectId();
            $value['refer_id'] = $value['purchase_order_id'] ?? 0;
            $value['refer_type'] = !empty($value['purchase_order_id']) ? \Constants::TYPE_PURCHASE_ORDER : 0;
            $value['sub_refer_id'] = $value['purchase_order_product_id'] ?? 0;
            //避免该字段为空时前端不传该字段导致报错，所以明细db字段需要防一下空值
            $value['product_name']    = $value['product_name'] ?? '';
            $value['product_cn_name'] = $value['product_cn_name'] ?? '';
            $value['product_model']   = $value['product_model'] ?? '';
            $value['product_unit']    = $value['product_unit'] ?? '';
            $value['product_image']   = $value['product_image'] ?? [];

            //主配信息处理
            $value['is_master_product'] = intval($value['is_master_product'] ?? 0);
            $value['master_group_id'] = intval($value['master_group_id'] ?? 0);
            if (!empty($value['is_master_product']) && !empty($value['master_group_id'])) {
                $partsProductMap[$value['master_group_id']] = $recordId;
            }
            $value['master_id'] = ((empty($value['is_master_product'])) && !empty($value['master_group_id'])) ? ($partsProductMap[$value['master_group_id']] ?? 0) : 0;

            $carry[] = $value;
            return $carry;
        },[]);

        $batchInboundProduct = new  BatchPurchaseInboundRecord($this->_clientId);
        $batchInboundProduct->getOperator()->create($recordList);

        return true;
    }



    public function beforeCreate()
    {
        $this->handlePrivilegeFieldsV2();

        $this->status = OmsConstant::INBOUND_INVOICE_STATUS_WAIT;
        //编号查重
        if ($this->serial_id) {
            $this->checkNoLogic();
        } else {
            $this->serial_id = UniqueSerialAdapter::make(\Constants::TYPE_PURCHASE_INBOUND_INVOICE, \Constants::TYPE_PURCHASE_INBOUND_INVOICE, $this)->getUniqueSerialId();
        }
        //配件数据计算 特殊处理
        \common\library\oms\common\Helper::computePartCount($this->recordList, 'inbound_count', $this);
        //必须关联至少一个采购订单
        $this->checkHasRelationInvoice();

        //检查字段权限和和汇率币种，以关联单据为准
        if (!$this->checkExchangeRate($this->type)) {
            throw new \RuntimeException(\Yii::t('invoice', 'Currency and exchange rate of invoice or associated purchase order is not same.'));
        }
        //刷新产品总金额rmb usd
        $this->refreshAmountExchange();

        return parent::beforeCreate();
    }

    /**
     * 检查是否有关联单据
     * @return void
     */
    public function checkHasRelationInvoice()
    {
        if (empty(array_filter(array_column($this->recordList, 'purchase_order_id')))) {
            throw new \RuntimeException(\Yii::t('invoice', 'Please associate at least one purchase order for this purchase inbound invoice.'));
        }
    }

    /**
     * 检查入库单状态是否已经入库
     * @param $return
     * @return false|void
     */
    public function checkInboundStatus($return = OmsConstant::RETURN_EXCEPTION)
    {
        if ($this->status == OmsConstant::INBOUND_INVOICE_STATUS_FINISH) {
            ($return == OmsConstant::RETURN_EXCEPTION) && throw new \RuntimeException(\Yii::t('inbound', '采购入库单已经确认入库，无需再次确认入库'));
            return false;
        }
        return true;
    }

    /**
     * @param int $return
     * @return bool
     * @throws \xiaoman\orm\exception\SingleObjectNotFoundException
     */
    public function canInWarehouse($return = OmsConstant::RETURN_EXCEPTION)
    {
        $this->checkObjectIsExist(true);
        $this->isDelete($return);
//        $this->hasAccessPrivilege(PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_INBOUND_CONFIRM,$return);
        $this->checkInboundStatus($return);

        if (!\common\library\privilege_v3\Helper::canOperateRecord($this->getClientId(), $this->getDomainHandler()->getUserId(), $this->scope_user_ids, PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_INBOUND_CONFIRM)) {
           if ($return == OmsConstant::RETURN_EXCEPTION) {
               throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'));
           }

           return false;
        }

        //todo 是否已入库
        //todo 不在审批中

        return true;
    }

    public function canRead()
    {
        $this->checkObjectIsExist(true);
        $this->isDelete(OmsConstant::RETURN_EXCEPTION);
//        $this->hasAccessPrivilege(PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_INBOUND_VIEW,OmsConstant::RETURN_EXCEPTION);

        if (!\common\library\privilege_v3\Helper::canOperateRecord($this->getClientId(), $this->getDomainHandler()->getUserId(), $this->scope_user_ids, PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_INBOUND_VIEW)) {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'));
        }

        return true;
    }


    /**
     * @param int $return
     * @return bool
     * @throws \xiaoman\orm\exception\SingleObjectNotFoundException
     */
    public function canChangeHandler()
    {
        $this->checkObjectIsExist(true);
        $this->isDelete(OmsConstant::RETURN_EXCEPTION);
//        $this->hasAccessPrivilege(PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_INBOUND_EDIT,OmsConstant::RETURN_EXCEPTION);

        if (!\common\library\privilege_v3\Helper::canOperateRecord($this->getClientId(), $this->getDomainHandler()->getUserId(), $this->scope_user_ids, PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_INBOUND_EDIT)) {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'));
        }

        //todo 不在审批中

        return true;
    }

    public function canEdit()
    {
        $this->checkObjectIsExist(true);
        $this->isDelete(OmsConstant::RETURN_EXCEPTION);
//        $this->hasAccessPrivilege(PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_INBOUND_EDIT,OmsConstant::RETURN_EXCEPTION);
        if (!\common\library\privilege_v3\Helper::canOperateRecord($this->getClientId(), $this->getDomainHandler()->getUserId(), $this->scope_user_ids, PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_INBOUND_EDIT)) {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'));
        }

        return true;
    }

    public function afterCreateForTransaction()
    {
        parent::afterCreateForTransaction();
        //新建采购入库单 不触发订单环节刷新
        //$this->getOperator()->triggerReferOrderLink();

        //新建采购入库单 刷新上游单据相关任务状态 放在后置是为了避免报错导致单据创建失败，最重要是需要用到子表数据，需要明细执行完后才可查询出数据
        $this->getOperator()->triggerReferInboundProductTransferInvoice($this->inbound_invoice_id);

    }

    public function callback4Success()
    {
       $result =  parent::callback4Success();


       $this->getOperator()->triggerReferOrderLink();
       $this->getOperator()->triggerReferInboundProductTransferInvoice([$this->getObjectId()]);
       $this->getOperator()->triggerReferPurchaseOrderInboundStatus([$this->getObjectId()]);

       return $result;
    }


    public function getPrivilegeFieldFunctionalId()
    {
        return PrivilegeConstants::FUNCTIONAL_PURCHASE_INBOUND;
    }

    public function getClientId()
    {
        return $this->getDomainHandler()->getClientId();
    }

    public function getPrivilegeFieldUserId()
    {
        return $this->getDomainHandler()->getUserId();
    }

    public function canDel()
    {
        $this->checkObjectIsExist(true);
        $this->hasAccessPrivilege(PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_INBOUND_REMOVE,OmsConstant::RETURN_EXCEPTION);
    }
}


