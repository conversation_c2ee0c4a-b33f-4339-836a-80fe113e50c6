<?php

namespace common\library\oms\inquiry_feedback;

use common\library\history\base\Builder;
use common\library\history\inquiry_feedback\InquiryFeedBackSetting;
use common\library\inquiry_collaboration\InquiryCollaboration;
use common\library\inquiry_collaboration\InquiryCollaborationFilter;
use common\library\inquiry_collaboration\InquiryHelper;
use common\library\inquiry_collaboration_product\InquiryCollaborationProductFilter;
use common\library\invoice\batch\Constant;
use common\library\oms\common\OmsConstant;
use common\library\oms\product_transfer\dynamic\TransferInvoiceDynamicConstant;
use common\library\oms\traits\BatchOperateTrait;
use common\library\privilege_v3\field\PrivilegeFieldService;
use common\library\privilege_v3\Helper as PrivilegeHelper;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\util\Arr;
use common\library\util\PgsqlUtil;
use User;
use xiaoman\orm\common\DomainObject;
use xiaoman\orm\common\ObjectField;
use xiaoman\orm\common\OperatorV2;
use xiaoman\orm\common\SingleObjectV2;
use xiaoman\orm\database\data\In;
use xiaoman\orm\database\DBConstants;


/**
 * class InquiryFeedBackOperator
 * @package common\library\oms\inquiry_feedback
 */
class InquiryFeedBackOperator extends OperatorV2
{
    use BatchOperateTrait;
    const TASK_LIST = [];

    /**
     * @var InquiryFeedBack|BatchInquiryFeedBack $object
     */
    protected $object;

    protected $dataMap = [];
    protected $failInquiryFeedbackIds = [];
    protected $failInquiry = [];


    public function batchCreate()
    {
        $batchData = $this->object->getRawAttributes();
        foreach ($batchData as &$feedBack) {
            if (empty($feedBack['inquiry_feedback_id'])) {
                $feedBack['inquiry_feedback_id'] = \PgActiveRecord::produceAutoIncrementId();
            }
        }
        unset($feedBack);

        $ret = $this->batchInsert($batchData);

        $historyBuilder = new Builder(new InquiryFeedBackSetting());
        $historyBuilder->setType(InquiryFeedBackSetting::TYPE_CREATE)
            ->setClientId($this->object->getDomainHandler()->getClientId())
            ->setUpdateUser($this->object->getDomainHandler()->getUserId())
            ->initFromRawData($batchData, true)
            ->build();

        $this->triggerInquiryDynamic($batchData, TransferInvoiceDynamicConstant::TYPE_FEEDBACK_CREATE);

    }

    public function batchUpdate()
    {
        $newListAttributes = $this->object->getRawAttributes();
        $oldListAttributes = $this->object->getOldAttributes();
        $this->batchInsert($newListAttributes, DBConstants::INSERT_MODE_CONFLICT);

        $historyBuilder = new Builder(new InquiryFeedBackSetting());
        $historyBuilder->setClientId($this->object->getClientId())->setType(InquiryFeedBackSetting::TYPE_EDIT)
            ->setUpdateUser($this->object->getDomainHandler()->getUserId())
            ->initFromBatchData($newListAttributes)
            ->setOldAttributes($oldListAttributes)
            ->buildBatch();

        $this->triggerInquiryDynamic($newListAttributes, TransferInvoiceDynamicConstant::TYPE_FEEDBACK_EDIT);

    }

    public function batchDelete()
    {
        $updateData = [
            'enable_flag' => \Constants::DELETE_FLAG_FALSE,
            'update_time' => xm_function_now()
        ];
        $this->execute($updateData);
    }

    public function batchRecover()
    {
        $updateData = [
            'enable_flag' => \Constants::ENABLE_FLAG_TRUE,
            'update_time' => xm_function_now()
        ];
        $this->execute($updateData);
    }

    public function batchRemove()
    {
        $oldList = $this->get([
            'client_id',
            'inquiry_feedback_id',
            'inquiry_collaboration_id',
            'inquiry_product_id',
            'tax_included',
            'purchase_quote_cny',
            'purchase_quote',
            'supplier_id',
            'price_validity_date',
            'market_information',
            'external_field_data',
            'adoption_status',
            'adoption_time',
            'scope_user_ids',
            'create_user',
            'update_user']);
        $this->dataMap = array_column($oldList, null, 'inquiry_feedback_id');

        // 权限过滤
        $userId = $this->object->getDomainHandler()->getUserId();
        $permissionScopeUser = \common\library\privilege_v3\Helper::getPermissionScopeUser($this->clientId, $userId, PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_FEEDBACK_DELETE, true);
        foreach ($oldList as $item) {
            // 权限校验
            if ($permissionScopeUser != \common\library\privilege_v3\Helper::CAN_MANAGE_ALL_USER && !array_intersect($item['scope_user_ids'], $permissionScopeUser)) {
                $this->setFailOrderMessage(\common\library\invoice\batch\Constant::MISSING_PERMISSION_TYPE, $item['inquiry_feedback_id'], 'Missing permission');
            }
        }

        if (empty($this->dataMap)){
            return 0;
        }

        $time = xm_function_now();
        $params = [
            'enable_flag' => \Constants::ENABLE_FLAG_REMOVE,
            'update_time' => xm_function_now()
        ];

        $inquiryFeedbackIds = implode(',', array_keys($this->dataMap));
        $sql = "update tbl_inquiry_feedback set enable_flag=:enable_flag, update_time=:update_time where client_id={$this->clientId} and inquiry_feedback_id IN ({$inquiryFeedbackIds})";
        $db = \PgActiveRecord::getDbByClientId($this->clientId);
        $count = $db->createCommand($sql)->execute($params);

        if ($count > 0) {
            $historyBuilder = new Builder(new InquiryFeedBackSetting());
            $historyBuilder->setType(InquiryFeedBackSetting::TYPE_REMOVE)
                ->setClientId($this->object->getDomainHandler()->getClientId())
                ->setUpdateUser($this->object->getDomainHandler()->getUserId())
                ->initFromSetValue([
                    'update_user' => $this->object->getDomainHandler()->getUserId(),
                    'update_time' => $time,
                    'enable_flag' => \Constants::ENABLE_FLAG_FALSE,
                ])
                ->setOldAttributes(array_values($oldList))
                ->build();
        }

        $newListAttributes = $this->get([
            'client_id',
            'inquiry_feedback_id',
            'inquiry_collaboration_id',
            'inquiry_product_id',
            'tax_included',
            'purchase_quote_cny',
            'purchase_quote',
            'supplier_id',
            'price_validity_date',
            'market_information',
            'external_field_data',
            'adoption_status',
            'adoption_time',
            'scope_user_ids',
            'create_user',
            'update_user']);

        $this->triggerInquiryDynamic($newListAttributes, TransferInvoiceDynamicConstant::TYPE_FEEDBACK_DEL);

    }

    public function batchRemove2()
    {
        if ($this->object instanceof SingleObjectV2) {
            $old_list = [$this->object->getRawAttributes()];
        } else {
            $old_list = $this->object->getRawAttributes() ?: $this->object->getAllAttributes();
        }

        $time = xm_function_now();
        $updateData = [
            'enable_flag' => \Constants::ENABLE_FLAG_REMOVE,
            'update_time' => xm_function_now()
        ];
        $this->execute($updateData);

        if ($this->object instanceof SingleObjectV2) {
            $newListAttributes = [$this->object->getRawAttributes()];
        } else {
            $newListAttributes = $this->object->getRawAttributes();
        }

        $historyBuilder = new Builder(new InquiryFeedBackSetting());
        $historyBuilder->setType(InquiryFeedBackSetting::TYPE_REMOVE)
            ->setClientId($this->object->getDomainHandler()->getClientId())
            ->setUpdateUser($this->object->getDomainHandler()->getUserId())
            ->initFromSetValue([
                'update_user' => $this->object->getDomainHandler()->getUserId(),
                'update_time' => $time,
                'enable_flag' => \Constants::ENABLE_FLAG_FALSE,
            ])
            ->setOldAttributes(array_values($old_list))
            ->build();

        $this->triggerInquiryDynamic($newListAttributes, TransferInvoiceDynamicConstant::TYPE_FEEDBACK_DEL);
    }

    public function editByInfo($data): InquiryFeedBack
    {
        $this->onlySingle();

        $clientId = $this->clientId;

        $data = is_array($data) ? $data : json_decode($data, true);
        $feedBackField = ObjectField::make($clientId, new InquiryFeedBackMetadata($clientId));
        $attributes = $feedBackField->unpackFormData($data);
        $oldAttributes = $this->object->getOldAttributes();

        $privilegeFieldService = new PrivilegeFieldService($this->object->getDomainHandler()->getUserId(), PrivilegeConstants::FUNCTIONAL_INQUIRY_FEEDBACK);
        $attributes = $privilegeFieldService->handlePrivilegeFieldsForUpdateWriteBack($attributes, $oldAttributes);

        $this->object->bindAttrbuties($attributes);
        $this->object->update();
        $newAttributes = $this->object->getRawAttributes();

        $historyBuilder = new Builder(new InquiryFeedBackSetting());
        $historyBuilder->setClientId($this->object->getDomainHandler()->getClientId())->setType(InquiryFeedBackSetting::TYPE_EDIT)
            ->setUpdateUser($this->object->getDomainHandler()->getUserId())
            ->initFromRawData($newAttributes)
            ->setOldAttributes($oldAttributes)
            ->build();

        $this->triggerInquiryDynamic([$attributes], TransferInvoiceDynamicConstant::TYPE_FEEDBACK_EDIT);
        return $this->object;
    }

    public function batchAdoption()
    {
        $old_list = $this->object->getRawAttributes() ?: $this->object->getAllAttributes();
        $time = xm_function_now();
        $user = \User::getLoginUser();

        $updateData = [
            'top_flag' => 1,
            'adoption_status' => OmsConstant::IS_ADOPTION,
            'adoption_time' => $time,
            'adoption_user' => $user->getUserId(),
            'update_user' => $user->getUserId(),
        ];
        $this->execute($updateData);

        $newListAttributes = $this->object->getRawAttributes();
        $this->triggerInquiryDynamic($newListAttributes, TransferInvoiceDynamicConstant::TYPE_FEEDBACK_ADOPTION);


        $historyBuilder = new Builder(new InquiryFeedBackSetting());
        $historyBuilder->setType(InquiryFeedBackSetting::TYPE_ADOPTION)
            ->setClientId($this->object->getDomainHandler()->getClientId())
            ->setUpdateUser($this->object->getDomainHandler()->getUserId())
            ->initFromSetValue($updateData)
            ->setOldAttributes($old_list)
            ->build();
    }

    public function batchNotAdoption()
    {
        $old_list = $this->object->getRawAttributes() ?: $this->object->getAllAttributes();

        $updateData = [
            'top_flag' => 0,
            'adoption_status' => OmsConstant::IS_NOT_ADOPTION,
            'adoption_time' => DomainObject::XM_NULL,
            'adoption_user' => 0,
        ];
        $this->execute($updateData);

        $historyBuilder = new Builder(new InquiryFeedBackSetting());
        $historyBuilder->setType(InquiryFeedBackSetting::TYPE_NOT_ADOPTION)
            ->setClientId($this->object->getDomainHandler()->getClientId())
            ->setUpdateUser($this->object->getDomainHandler()->getUserId())
            ->initFromSetValue($updateData)
            ->setOldAttributes($old_list)
            ->build();
    }

    public function batchRefreshUsers($handler)
    {
        $list = $this->get(['inquiry_feedback_id', 'create_user']);
        $client_id = $this->clientId;
        $feedback_map = array_column($list, null, 'inquiry_feedback_id');
        $sql = "update tbl_inquiry_feedback set user_ids = case inquiry_feedback_id  ";
        $feedback_ids = [];
        foreach ($list as $feedback) {
            $id = $feedback['inquiry_feedback_id'];
            if (!empty($feedback_map[$id])) {
                $users = Arr::uniqueFilterValues(array_merge([$feedback_map[$id]['create_user']], $handler));

                $sql .= sprintf("when %d then '%s'::bigint[] ", $id, PgsqlUtil::formatArray($users));
                $feedback_ids[] = $id;
            }
        }
        $feedback_ids = Arr::uniqueFilterValues($feedback_ids);
        if (empty($feedback_ids)) {
            return true;
        }

        $db = \PgActiveRecord::getDbByClientId($client_id);
        $sql .= "end where client_id = $client_id and inquiry_feedback_id in (" . implode(',', $feedback_ids) . ")";
        $db->createCommand($sql)->execute();

    }

    private function triggerInquiryDynamic($data, $type)
    {
        $map = [];
        foreach ($data as $feedBack) {
            if (!empty($map[$feedBack['inquiry_collaboration_id']])) {
                $map[$feedBack['inquiry_collaboration_id']][] = $feedBack['inquiry_feedback_id'];
            } else {
                $map[$feedBack['inquiry_collaboration_id']] = [$feedBack['inquiry_feedback_id']];
            }
        }

        $inquiry_collaboration_ids = Arr::uniqueFilterValues(array_column($data, 'inquiry_collaboration_id'));
        if (empty($inquiry_collaboration_ids)) {
            return true;
        }
        $transferMap = InquiryHelper::getTransferIdByInquiryCollaborationId($this->object->getDomainHandler()->getClientId(), $inquiry_collaboration_ids);

        if (empty($transferMap)){
            return true;
        }
        $dynamicParams = [];
        foreach ($map as $inquiry_collaboration_id => $feedback_ids) {
            $dynamicParams[] = [
                'transfer_invoice_id' => $transferMap[$inquiry_collaboration_id] ?? 0,
                'transfer_invoice_type' => \Constants::TYPE_INQUIRY_COLLABORATION,
                "update_user" => $this->object->getDomainHandler()->getUserId(),
                "handler" => $this->object->getDomainHandler()->getUserId(),
                "inquiry_feedback_nos" => implode(",", $feedback_ids),
            ];
        }


        switch ($type) {
            case TransferInvoiceDynamicConstant::TYPE_FEEDBACK_CREATE:
            {
                Helper::dynamicByCreateFeedBack(
                    $dynamicParams,
                    $this->object->getDomainHandler()->getClientId(),
                    $this->object->getDomainHandler()->getUserId()
                );
                break;
            }
            case TransferInvoiceDynamicConstant::TYPE_FEEDBACK_EDIT:
            {
                Helper::dynamicByEditFeedBack(
                    $dynamicParams,
                    $this->object->getDomainHandler()->getClientId(),
                    $this->object->getDomainHandler()->getUserId()
                );
                break;
            }
            case TransferInvoiceDynamicConstant::TYPE_FEEDBACK_DEL:
            {
                Helper::dynamicByDelFeedBack(
                    $dynamicParams,
                    $this->object->getDomainHandler()->getClientId(),
                    $this->object->getDomainHandler()->getUserId()
                );
                break;
            }
            case TransferInvoiceDynamicConstant::TYPE_FEEDBACK_ADOPTION:
            {
                Helper::dynamicByAdoptFeedBack(
                    $dynamicParams,
                    $this->object->getDomainHandler()->getClientId(),
                    $this->object->getDomainHandler()->getUserId()
                );
                break;
            }
        }
        return true;
    }

    //反馈采纳 实际上是询价任务Operator类职责
    public function batchFeedback()
    {
        $user = User::getLoginUser();

        $data = $this->object->getAllAttributes();
        $inquiry_feedback_ids = Arr::uniqueFilterValues(array_column($data, 'inquiry_feedback_id'));
        $inquiry_collaboration_ids = Arr::uniqueFilterValues(array_column($data, 'inquiry_collaboration_id'));
        $inquiry_product_ids = Arr::uniqueFilterValues(array_column($data, 'inquiry_product_id'));

        if (empty($data) || empty($inquiry_collaboration_ids) || empty($inquiry_feedback_ids)) {
            $this->failIds[] = $inquiry_feedback_ids;
        }

        $inquiryCollaborationList = [];
        if (!empty($inquiry_collaboration_ids)) {
            $filter = new InquiryCollaborationFilter($this->clientId);
            $filter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
            $filter->inquiry_collaboration_id = new In($inquiry_collaboration_ids);
            $filter->select(['inquiry_collaboration_id', 'status', 'handler', 'create_user']);
            $inquiryCollaborationList = $filter->rawData();
        }

        $inquiryProductList = [];
        if (!empty($inquiry_product_ids)) {
            $filter = new InquiryCollaborationProductFilter($this->clientId);
            $filter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
            $filter->inquiry_product_id = new In($inquiry_product_ids);
            $filter->select(['inquiry_product_id']);
            $inquiryProductList = $filter->rawData();
        }

        $inquiryCollaborationMap = array_column($inquiryCollaborationList, null, 'inquiry_collaboration_id');
        $inquiryProductMap = array_column($inquiryProductList, null, 'inquiry_product_id');

        $adoption_scope_user_ids = PrivilegeHelper::getPermissionScopeUser($this->clientId, $this->object->getDomainHandler()->getUserId(),
            PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_FEEDBACK_ADOPTION);

        $hasCollaborationPerm = \common\library\privilege_v3\Helper::hasPermission(
            $user->getClientId(),
            $user->getUserId(),
            PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_ADOPTION
        );

        $inquiry_view_user_ids = PrivilegeHelper::getPermissionScopeUser($user->getClientId(), $user->getUserId(),
            PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_VIEW);

        foreach ($data as $k => &$listDatum) {
            try {
                //状态校验
                if ($listDatum['adoption_status'] == OmsConstant::IS_ADOPTION) {
                    $this->setFailOrderMessage(\common\library\invoice\batch\Constant::FEEDBACK_STATUS_NO_CHANGE_TYPE, $listDatum['inquiry_feedback_id'], \Yii::t('inquiry_collaboration', '该反馈已采纳,不支持重复采纳'));
                }

                //反馈不存在
                if ($listDatum['enable_flag'] == \Constants::ENABLE_FLAG_FALSE) {
                    $this->setFailOrderMessage(\common\library\invoice\batch\Constant::FEEDBACK_HAS_DEL_TYPE, $listDatum['inquiry_feedback_id'], \Yii::t('inquiry_collaboration', '采纳失败,该产品数据已被删除'));
                }

                //任务删除
                if (!isset($inquiryCollaborationMap[$listDatum['inquiry_collaboration_id']])) {
                    $this->setFailOrderMessage(\common\library\invoice\batch\Constant::FEEDBACK_COLLABORATION_HAS_DEL_TYPE, $listDatum['inquiry_feedback_id'], \Yii::t('inquiry_collaboration', '采纳失败,该任务数据已被删除'));
                }

                if (!isset($inquiryCollaborationMap[$listDatum['inquiry_collaboration_id']]) && $inquiryCollaborationMap[$listDatum['inquiry_collaboration_id']]['status'] == OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH) {
                    $this->setFailOrderMessage(\common\library\invoice\batch\Constant::FEEDBACK_COLLABORATION_STATUS_COMPLETE_TYPE, $listDatum['inquiry_feedback_id'], \Yii::t('inquiry_collaboration', '采纳失败,该任务数据已完成'));
                }

                //询价产品删除
                if (!isset($inquiryProductMap[$listDatum['inquiry_product_id']])) {
                    $this->setFailOrderMessage(\common\library\invoice\batch\Constant::FEEDBACK_COLLABORATION_PRODUCT_HAS_DEL_TYPE, $listDatum['inquiry_feedback_id'], \Yii::t('inquiry_collaboration', '采纳失败,该产品数据已被删除'));
                }


                $user_ids = [$listDatum['create_user']];

                if (empty($user_ids)) {
                    //超管
                    if ($adoption_scope_user_ids == 0) {
                        $userId = null;
                    } else {
                        $userId = array_values(array_unique($adoption_scope_user_ids));
                    }
                } else {
                    if ($adoption_scope_user_ids != 0) {
                        $userId = array_intersect($user_ids, $adoption_scope_user_ids);
                    } else {
                        $userId = $user_ids;
                    }
                }

                $hasFeedbackPerm = !(is_array($userId) && empty($userId));
                $canView = Helper::checkCanView($hasCollaborationPerm, $inquiry_view_user_ids,
                    $inquiryCollaborationMap[$listDatum['inquiry_collaboration_id']]['create_user'],
                    $inquiryCollaborationMap[$listDatum['inquiry_collaboration_id']]['handler']
                );
                $shouldAllowAccess = ($hasFeedbackPerm) || ($canView);

                if (!$shouldAllowAccess) {
                    $this->setFailOrderMessage(\common\library\invoice\batch\Constant::FEEDBACK_COLLABORATION_PRODUCT_HAS_DEL_TYPE, $listDatum['inquiry_feedback_id'], \Yii::t('inquiry_collaboration', '无法编辑,该反馈属于其他人'));
                }

            } catch (\Exception $e) {
                unset($data[$k]);
                $this->failIds[] = $listDatum['inquiry_feedback_id'];
            }
        }

        unset($listDatum);
        if (empty($data)) {
            return;
        }

//        $inquiry_collaboration_info = [];
//        foreach ($data as $datum){
//            if (isset($inquiry_collaboration_info[$datum['inquiry_collaboration_id']])){
//                $inquiry_collaboration_info[$datum['inquiry_collaboration_id']][] = $datum;
//
//            }else{
//                $inquiry_collaboration_info[$datum['inquiry_collaboration_id']] = [$datum];
//            }
//        }

        //这里仅支持单个询价任务 后续有列表跨任务审批 这里需要知道交互才能改造
        $inquiryCollaboration = new InquiryCollaboration($this->clientId, $inquiry_collaboration_ids[0]);
        $inquiryCollaboration->canFeedback();
        $inquiryCollaboration->getOperator()->feedback($data);

        $this->successIds = array_column($data, 'inquiry_feedback_id');

    }

    public function setFailOrderMessage($failType, $objectId, $message = '', $throw = true)
    {
        $moduleType = $this->object->getMetadata()->getModuleType();

        if (!array_key_exists($failType, $this->failInfo)) {
            $message = \common\library\invoice\batch\Constant::BATCH_FAIL_MESSAGE_MAP[$moduleType][$failType];
            $this->failInfo[$failType] = [
                'message' => \Yii::t('inquiry_collaboration', $message),
                'ids' => [$objectId],
                'failType' => $failType
            ];
        } else {
            $this->failInfo[$failType]['ids'][] = $objectId;
        }
        \LogUtil::info('client_id[' . $this->clientId . 'module_type:' . $moduleType . 'refer_id' . $objectId . ']批量修改失败，失败原因：' . $message);
        $throw && throw new \RuntimeException($message);
    }
}