<?php

namespace common\library\oms\common;

use common\library\account\Client;
use common\library\oms\task\formatter\ShippingInvoiceDownStreamInvoiceTask;
use common\library\oms\task\privilege\ApprovalIsLockTask;
use common\library\oms\task\privilege\ApprovalPendingTask;
use common\library\oms\task\privilege\CustomizeTask;
use common\library\oms\task\privilege\FieldsPrivilegesTask;
use common\library\oms\task\privilege\MultiplePrivilegeTask;
use common\library\oms\task\privilege\OpportunitySecondPrivilegeTask;
use common\library\oms\task\privilege\PaymentAmountTask;
use common\library\oms\task\privilege\ApprovalUnlockTask;
use common\library\oms\task\privilege\ClientSettingTask;
use common\library\oms\task\privilege\CustomStatusTask;
use common\library\oms\task\privilege\EmptyFieldTask;
use common\library\oms\task\privilege\FunctionalTask;
use common\library\oms\task\privilege\HasAiAgentTask;
use common\library\oms\task\privilege\precondition\Preconditions;
use common\library\oms\task\privilege\PrivilegeTask;
use common\library\oms\task\privilege\ScopeUserTask;
use common\library\oms\task\privilege\StatusTask;
use common\library\oms\task\privilege\TransferScopeUserTask;
use common\library\opportunity\Opportunity;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\setting\library\stage\Stage;
use common\library\setting\library\status\Status;
use Yii;

class OmsPrivilegeConstant
{

    const OPERATE_PRIVILEGE_MAP = [

        \Constants::TYPE_PURCHASE_INBOUND_INVOICE => [
            OmsConstant::DELETE_OPERATE       => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_INBOUND_REMOVE,
                        'user' => ['handler']
                    ]
                ],
                //状态
//                [
//                    'task_class' => StatusTask::class,
//                    'setting'    => [
//                        'disable_status' => [OmsConstant::INBOUND_INVOICE_STATUS_FINISH],
//                    ]
//                ]
            ],
            OmsConstant::EDIT_OPERATE         => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_INBOUND_EDIT,
                        'user' => ['handler']
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [OmsConstant::INBOUND_INVOICE_STATUS_FINISH],
                    ]
                ]
            ],
            OmsConstant::PRINT_OPERATE        => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_INBOUND_PRINT,
                        'user' => ['handler']
                    ],
                ]
            ],
            OmsConstant::TO_ORDER_OUTBOUND    => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_SALES_OUTBOUND_CREATE,
                        'user' => ['handler'],
                    ]
                ],
            ],
            OmsConstant::IN_WAREHOUSE_OPERATE => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_INBOUND_CONFIRM,
                        'user' => ['handler']
                    ],
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [OmsConstant::INBOUND_INVOICE_STATUS_FINISH],
                    ]
                ]
            ],
            OmsConstant::TO_RETURN_OPERATE => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_RETURN_CREATE,
                        'user' => ['handler']
                    ],
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [OmsConstant::INBOUND_INVOICE_STATUS_WAIT],
                        'message'    => [
                            OmsConstant::INBOUND_INVOICE_STATUS_WAIT => '草稿状态下不支持生成采购退货单',
                        ]
                    ]
                ]
            ],
        ],

        \Constants::TYPE_OTHER_INBOUND_INVOICE => [
            OmsConstant::DELETE_OPERATE       => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_INBOUND_REMOVE,
                        'user' => ['handler']
                    ]
                ],
                //状态
//                [
//                    'task_class' => StatusTask::class,
//                    'setting'    => [
//                        'disable_status' => [OmsConstant::INBOUND_INVOICE_STATUS_FINISH],
//                    ]
//                ]
            ],
            OmsConstant::EDIT_OPERATE         => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_INBOUND_EDIT,
                        'user' => ['handler']
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [OmsConstant::INBOUND_INVOICE_STATUS_FINISH],
                    ]
                ]
            ],
            OmsConstant::PRINT_OPERATE        => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_INBOUND_PRINT,
                        'user' => ['handler']
                    ],
                ]
            ],
            OmsConstant::IN_WAREHOUSE_OPERATE => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_INBOUND_CONFIRM,
                        'user' => ['handler']
                    ],

                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [OmsConstant::INBOUND_INVOICE_STATUS_FINISH],
                    ]
                ]

            ],
            OmsConstant::EDIT_ATTACHMENT => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_INBOUND_EDIT,
                        'user' => ['handler']
                    ]
                ],
            ],
        ],
        \Constants::TYPE_SALE_OUTBOUND_INVOICE => [
            OmsConstant::DELETE_OPERATE       => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_SALES_OUTBOUND_REMOVE,
                        'user' => ['handler']
                    ]
                ],
                //审批校验
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SALE_OUTBOUND_INVOICE,
                        'message' => '审批中，不支持删除',
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SALE_OUTBOUND_INVOICE,
                    ]
                ],
            ],
            OmsConstant::CHANGE_HANDLER_OPERATE => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_SALES_OUTBOUND_EDIT,
                        'user' => ['handler']
                    ]
                ],
                //审批校验
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SALE_OUTBOUND_INVOICE,
                        'message' => '审批中，不支持变更处理人',
                    ]
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::OUTBOUND_INVOICE_STATUS_FINISH,
                        ],
                        'message'    => [
                            OmsConstant::OUTBOUND_INVOICE_STATUS_FINISH => '单据已出库，不支持变更处理人'
                        ]
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SALE_OUTBOUND_INVOICE,
                    ]
                ],
                //字段权限校验
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SALE_OUTBOUND_INVOICE,
                        'fields' => ['handler']
                    ]
                ],
            ],
            OmsConstant::EDIT_OPERATE         => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_SALES_OUTBOUND_EDIT,
                        'user' => ['handler']
                    ]
                ],
                //审批校验
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SALE_OUTBOUND_INVOICE,
                        'message' => '审批中，不支持编辑',
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [OmsConstant::OUTBOUND_INVOICE_STATUS_FINISH],
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SALE_OUTBOUND_INVOICE,
                    ]
                ],
            ],
            OmsConstant::PRINT_OPERATE        => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_SALES_OUTBOUND_PRINT,
                        'user' => ['handler']
                    ],
                ]
            ],
            OmsConstant::OUT_WAREHOUSE_OPERATE => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_SALES_OUTBOUND_CONFIRM,
                        'user' => ['handler']
                    ],
                ],
                //审批校验
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SALE_OUTBOUND_INVOICE,
                        'message' => '审批中，不支持当前操作',
                    ]
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [OmsConstant::OUTBOUND_INVOICE_STATUS_FINISH],
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SALE_OUTBOUND_INVOICE,
                    ]
                ],
            ],
            OmsConstant::APPROVAL_UNLOCK => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_SALE_OUTBOUND_APPROVAL_UNLOCK,
                        ],
                    ]
                ],
                //审批校验
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SALE_OUTBOUND_INVOICE,
                        'message' => '审批中，不支持当前操作',
                    ]
                ],
                [
                    'task_class' => ApprovalUnlockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SALE_OUTBOUND_INVOICE,
                    ]
                ],
            ],
            OmsConstant::EDIT_ATTACHMENT         => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_SALES_OUTBOUND_EDIT,
                        'user' => ['handler']
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SALE_OUTBOUND_INVOICE,
                    ]
                ],
                //字段权限校验
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SALE_OUTBOUND_INVOICE,
                        'fields' => ['attachment']
                    ]
                ],
            ],
        ],
        \Constants::TYPE_OTHER_OUTBOUND_INVOICE => [
            OmsConstant::DELETE_OPERATE       => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_OUTBOUND_REMOVE,
                        'user' => ['handler']
                    ]
                ],
                //状态
//                [
//                    'task_class' => StatusTask::class,
//                    'setting'    => [
//                        'disable_status' => [OmsConstant::OUTBOUND_INVOICE_STATUS_FINISH],
//                    ]
//                ]
            ],
            OmsConstant::EDIT_OPERATE         => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_OUTBOUND_EDIT,
                        'user' => ['handler']
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [OmsConstant::OUTBOUND_INVOICE_STATUS_FINISH],
                    ]
                ]
            ],
            OmsConstant::PRINT_OPERATE        => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_OUTBOUND_PRINT,
                        'user' => ['handler']
                    ],
                ]
            ],
            OmsConstant::OUT_WAREHOUSE_OPERATE => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_OUTBOUND_CONFIRM,
                        'user' => ['handler']
                    ],
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [OmsConstant::OUTBOUND_INVOICE_STATUS_FINISH],
                    ]
                ]

            ],
            OmsConstant::EDIT_ATTACHMENT => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_OTHER_OUTBOUND_EDIT,
                        'user' => ['handler']
                    ]
                ],
            ],
        ],
        \Constants::TYPE_PURCHASE_RETURN_INVOICE => [
            OmsConstant::DELETE_OPERATE       => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_RETURN_REMOVE,
                        'user' => ['handler']
                    ]
                ],
                //状态
//                [
//                    'task_class' => StatusTask::class,
//                    'setting'    => [
//                        'disable_status' => [OmsConstant::RETURN_INVOICE_STATUS_FINISH],
//                    ]
//                ]
            ],
            OmsConstant::EDIT_OPERATE         => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_RETURN_EDIT,
                        'user' => ['handler']
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [OmsConstant::RETURN_INVOICE_STATUS_FINISH],
                    ]
                ]
            ],
            OmsConstant::PRINT_OPERATE        => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_RETURN_PRINT,
                        'user' => ['handler']
                    ],
                ]
            ],
            OmsConstant::TO_RETURN_OPERATE => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_RETURN_CONFIRM,
                        'user' => ['handler']
                    ],
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [OmsConstant::RETURN_INVOICE_STATUS_FINISH],
                    ]
                ]
            ],
            OmsConstant::EDIT_ATTACHMENT => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_RETURN_EDIT,
                        'user' => ['handler']
                    ]
                ],
            ],
        ],

        \Constants::TYPE_PRODUCT_TRANSFER_PURCHASE => [
            OmsConstant::PURCHASE_FOR_SALE => [
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting' => [
                        'user' => ['handler', 'create_user']
                    ]
                ],
                // 校验状态
                [
                    'task_class' => StatusTask::class,
                    'setting' => [
                        'disable_status' => [
                            OmsConstant::PRODUCT_TRANSFER_STATUS_DRAFT,
                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
                            OmsConstant::PRODUCT_TRANSFER_STATUS_REJECTED,
                        ],
                    ]
                ]
            ],
            OmsConstant::EDIT_OPERATE         => [
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting'    => [
//                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_TRANSFER_PURCHASE_EDIT,
                        'user'=>['create_user']
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
                        ],
                    ]
                ]
            ],
            OmsConstant::CHANGE_STATUS_OPERATE => [
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting' => [
//                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_TRANSFER_PURCHASE_EDIT,
                        'user' => ['handler']
                    ]
                ],
            ],
//            OmsConstant::EFFECT_OPERATE        => [
//                //权限校验
//                [
//                    'task_class' => TransferScopeUserTask::class,
//                    'setting'    => [
////                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_TRANSFER_PURCHASE_EFFECT,
//                        'user' => ['handler', 'create_user']
//                    ],
//                ],
//                //状态
//                [
//                    'task_class' => StatusTask::class,
//                    'setting'    => [
//                        'disable_status' => [
////                            OmsConstant::PRODUCT_TRANSFER_STATUS_WAIT,
////                            OmsConstant::PRODUCT_TRANSFER_STATUS_PROCESSING,
//                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
//                        ],
//                    ]
//                ]
//            ],
            OmsConstant::CHANGE_HANDLER_OPERATE => [
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting' => [
//                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_TRANSFER_PURCHASE_EDIT,
                        'user' => ['handler', 'create_user']
                    ],
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH],
                    ]
                ]
            ],
            OmsConstant::GENERATE_OUTBOUND_INVOICE_OPERATE => [
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting' => [
//                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_TRANSFER_PURCHASE_EDIT,
                        'user' => ['handler']
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_SALES_OUTBOUND_CREATE,
                        'user' => ['create_user', 'handler']
                    ],

                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting' => [
                        'disable_status' => [
//                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
//                            OmsConstant::PRODUCT_TRANSFER_STATUS_REJECTED,
                        ],
                    ]
                ]
            ],
            OmsConstant::GENERATE_PURCHASE_ORDER_OPERATE       => [
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting'    => [
//                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_TRANSFER_PURCHASE_GENERATE,
                        'user' => ['handler']
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_CREATE,
                        'user' => ['create_user','handler']
                    ],

                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting' => [
                        'disable_status' => [
//                            OmsConstant::PRODUCT_TRANSFER_STATUS_INVALID,
                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
                            OmsConstant::PRODUCT_TRANSFER_STATUS_REJECTED,
                        ],
                    ]
                ]
            ],
            OmsConstant::SET_NOTICE =>[
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting'    => [
                        'user' => ['handler', 'create_user']
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
                        ],
                    ]
                ]
            ],
            OmsConstant::DELETE_OPERATE => [
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting' => [
//                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_TRANSFER_PURCHASE_REMOVE,
                        'user' => ['create_user']
                    ]
                ],
                //状态
//                [
//                    'task_class' => StatusTask::class,
//                    'setting'    => [
//                        'disable_status' => [
//                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
//                        ],
//                    ]
//                ]
            ],
//            OmsConstant::FINISH_OPERATE        => [
//                //权限校验
//                [
//                    'task_class' => TransferScopeUserTask::class,
//                    'setting'    => [
//                        'user' => ['handler']
//                    ],
//                ],
//                //状态
//                [
//                    'task_class' => StatusTask::class,
//                    'setting'    => [
//                        'disable_status' => [
//                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
//                        ],
//                    ]
//                ]
//            ],
//            OmsConstant::TO_HANDLE_OPERATE => [
//                //权限校验
//                [
//                    'task_class' => TransferScopeUserTask::class,
//                    'setting'    => [
//                        'user' => ['handler']
//                    ],
//                ],
//                [
//                    'task_class' => StatusTask::class,
//                    'setting'    => [
//                        'disable_status' => [
//                            OmsConstant::PRODUCT_TRANSFER_STATUS_TO_HANDLE,
//                        ],
//                    ]
//                ]
//            ],
        ],
        \Constants::TYPE_PRODUCT_TRANSFER_INBOUND => [
//            OmsConstant::FINISH_OPERATE        => [
//                //权限校验
//                [
//                    'task_class' => TransferScopeUserTask::class,
//                    'setting'    => [
//                        'user' => ['handler']
//                    ],
//                ],
//                //状态
//                [
//                    'task_class' => StatusTask::class,
//                    'setting'    => [
//                        'disable_status' => [
//                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
//                        ],
//                    ]
//                ]
//            ],
//            OmsConstant::TO_HANDLE_OPERATE => [
//                //权限校验
//                [
//                    'task_class' => TransferScopeUserTask::class,
//                    'setting'    => [
//                        'user' => ['handler']
//                    ],
//                ],
//                [
//                    'task_class' => StatusTask::class,
//                    'setting'    => [
//                        'disable_status' => [
//                            OmsConstant::PRODUCT_TRANSFER_STATUS_TO_HANDLE,
//                        ],
//                    ]
//                ]
//            ],
            OmsConstant::SET_NOTICE =>[
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting'    => [
                        'user' => ['handler', 'create_user']
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
                        ],
                    ]
                ]
            ],
            OmsConstant::EDIT_OPERATE => [
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting' => [
//                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_TRANSFER_INBOUND_EDIT,
                        'user' => ['create_user']
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
//                            OmsConstant::PRODUCT_TRANSFER_STATUS_WAIT,
//                            OmsConstant::PRODUCT_TRANSFER_STATUS_PROCESSING,
                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
                        ],
                    ]
                ]
            ],
            OmsConstant::CHANGE_STATUS_OPERATE => [
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting' => [
//                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_TRANSFER_PURCHASE_EDIT,
                        'user' => ['handler']
                    ]
                ],
            ],
//            OmsConstant::EFFECT_OPERATE        => [
//                //权限校验
//                [
//                    'task_class' => TransferScopeUserTask::class,
//                    'setting'    => [
////                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_TRANSFER_INBOUND_EFFECT,
//                        'user' => ['handler', 'create_user']
//                    ],
//                ],
//                //状态
//                [
//                    'task_class' => StatusTask::class,
//                    'setting'    => [
//                        'disable_status' => [
////                            OmsConstant::PRODUCT_TRANSFER_STATUS_WAIT,
////                            OmsConstant::PRODUCT_TRANSFER_STATUS_PROCESSING,
//                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
//                        ],
//                    ]
//                ]
//            ],
            OmsConstant::CHANGE_HANDLER_OPERATE => [
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting' => [
//                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_TRANSFER_INBOUND_EDIT,
                        'user' => ['handler', 'create_user']
                    ],
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH],
                    ]
                ]
            ],
            OmsConstant::GENERATE_PURCHASE_INBOUND_OPERATE       => [
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting'    => [
//                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_TRANSFER_INBOUND_GENERATE,
                        'user' => ['handler']
                    ]
                ],
                [
                    'task_class' => PrivilegeTask::class,
                    'setting'    => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_INBOUND_CREATE,
                        ],
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
//                            OmsConstant::PRODUCT_TRANSFER_STATUS_INVALID,
                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
                            OmsConstant::PRODUCT_TRANSFER_STATUS_REJECTED,
                        ],
                    ]
                ]
            ],
            OmsConstant::DELETE_OPERATE       => [
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting'    => [
//                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_TRANSFER_INBOUND_REMOVE,
                        'user' => ['create_user']
                    ]
                ],
            ],
        ],
        \Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND => [
            OmsConstant::SET_NOTICE =>[
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting'    => [
                        'user' => ['handler', 'create_user']
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
                        ],
                    ]
                ]
            ],
            OmsConstant::EDIT_OPERATE => [
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting' => [
//                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_TRANSFER_OUTBOUND_EDIT,
                        'user' => ['create_user']
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
//                            OmsConstant::PRODUCT_TRANSFER_STATUS_WAIT,
//                            OmsConstant::PRODUCT_TRANSFER_STATUS_PROCESSING,
                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
                        ],
                    ]
                ]
            ],
            OmsConstant::CHANGE_STATUS_OPERATE => [
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting' => [
//                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_TRANSFER_PURCHASE_EDIT,
                        'user' => ['handler']
                    ]
                ],
            ],
//            OmsConstant::EFFECT_OPERATE        => [
//                //权限校验
//                [
//                    'task_class' => TransferScopeUserTask::class,
//                    'setting'    => [
////                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_TRANSFER_OUTBOUND_EFFECT,
//                        'user' => ['handler', 'create_user']
//                    ],
//                ],
//                //状态
//                [
//                    'task_class' => StatusTask::class,
//                    'setting'    => [
//                        'disable_status' => [
////                            OmsConstant::PRODUCT_TRANSFER_STATUS_WAIT,
////                            OmsConstant::PRODUCT_TRANSFER_STATUS_PROCESSING,
//                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
//                        ],
//                    ]
//                ]
//            ],
            OmsConstant::CHANGE_HANDLER_OPERATE => [
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting' => [
//                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_TRANSFER_OUTBOUND_EDIT,
                        'user' => ['handler', 'create_user']
                    ],
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH],
                    ]
                ]
            ],
            OmsConstant::GENERATE_ORDER_OUTBOUND_OPERATE       => [
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting'    => [
//                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_TRANSFER_OUTBOUND_GENERATE,
                        'user' => ['handler']
                    ]
                ],
                [
                    'task_class' => PrivilegeTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_SALES_OUTBOUND_CREATE,
                        'user' => ['create_user','handler']
                    ],

                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
//                            OmsConstant::PRODUCT_TRANSFER_STATUS_INVALID,
                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
                            OmsConstant::PRODUCT_TRANSFER_STATUS_REJECTED,
                        ],
                    ]
                ]
            ],
            OmsConstant::DELETE_OPERATE => [
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting' => [
//                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_TRANSFER_OUTBOUND_REMOVE,
                        'user' => ['create_user']
                    ]
                ],
            ],
//            OmsConstant::FINISH_OPERATE        => [
//                //权限校验
//                [
//                    'task_class' => TransferScopeUserTask::class,
//                    'setting'    => [
//                        'user' => ['handler']
//                    ],
//                ],
//                //状态
//                [
//                    'task_class' => StatusTask::class,
//                    'setting'    => [
//                        'disable_status' => [
//                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
//                        ],
//                    ]
//                ]
//            ],
//            OmsConstant::TO_HANDLE_OPERATE => [
//                //权限校验
//                [
//                    'task_class' => TransferScopeUserTask::class,
//                    'setting'    => [
//                        'user' => ['handler']
//                    ],
//                ],
//                [
//                    'task_class' => StatusTask::class,
//                    'setting'    => [
//                        'disable_status' => [
//                            OmsConstant::PRODUCT_TRANSFER_STATUS_TO_HANDLE,
//                        ],
//                    ]
//                ]
//            ],
        ],
        \Constants::TYPE_PRODUCT_TRANSFER_OTHER => [
            OmsConstant::CHANGE_STATUS_OPERATE => [
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting' => [
                        'user' => ['handler']
                    ]
                ],
            ],
            OmsConstant::SET_NOTICE =>[
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting'    => [
                        'user' => ['handler', 'create_user']
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
                        ],
                    ]
                ]
            ],
            OmsConstant::EDIT_OPERATE         => [
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting'    => [
//                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_TRANSFER_OUTBOUND_EDIT,
                        'user'=>['create_user']
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
//                            OmsConstant::PRODUCT_TRANSFER_STATUS_WAIT,
//                            OmsConstant::PRODUCT_TRANSFER_STATUS_PROCESSING,
                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
                        ],
                    ]
                ]
            ],
//            OmsConstant::FINISH_OPERATE        => [
//                //权限校验
//                [
//                    'task_class' => TransferScopeUserTask::class,
//                    'setting'    => [
//                        'user' => ['handler']
//                    ],
//                ],
//                //状态
//                [
//                    'task_class' => StatusTask::class,
//                    'setting'    => [
//                        'disable_status' => [
//                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
//                        ],
//                    ]
//                ]
//            ],
//            OmsConstant::TO_HANDLE_OPERATE => [
//                //权限校验
//                [
//                    'task_class' => TransferScopeUserTask::class,
//                    'setting'    => [
//                        'user' => ['handler']
//                    ],
//                ],
//                [
//                    'task_class' => StatusTask::class,
//                    'setting'    => [
//                        'disable_status' => [
//                            OmsConstant::PRODUCT_TRANSFER_STATUS_TO_HANDLE,
//                        ],
//                    ]
//                ]
//            ],
            OmsConstant::CHANGE_HANDLER_OPERATE => [
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting'    => [
                        'user' => ['handler', 'create_user']
                    ],
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH],
                    ]
                ]
            ],
            OmsConstant::DELETE_OPERATE       => [
                //权限校验
                [
                    'task_class' => TransferScopeUserTask::class,
                    'setting'    => [
                        'user' => ['create_user']
                    ]
                ],
            ],
        ],

        \Constants::TYPE_PAYMENT_INVOICE => [
            OmsConstant::EDIT_OPERATE         => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PAYMENT_INVOICE_EDIT,
                        'user'=>['handler','create_user']
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::PAYMENT_INVOICE_STATUS_FINISH,
                            OmsConstant::PAYMENT_INVOICE_STATUS_DISCARD,
                        ],
                    ]
                ]
            ],
            OmsConstant::DELETE_OPERATE        => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PAYMENT_INVOICE_DELETE,
                        'user' => ['handler', 'create_user']
                    ],
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::PAYMENT_INVOICE_STATUS_WAIT,
                            OmsConstant::PAYMENT_INVOICE_STATUS_FINISH,
//                            OmsConstant::PAYMENT_INVOICE_STATUS_DISCARD,
                        ],
                    ]
                ]
            ],
            OmsConstant::DISCARD_OPERATE        => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PAYMENT_INVOICE_DISABLE,
                        'user' => ['handler', 'create_user']
                    ],
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::PAYMENT_INVOICE_STATUS_DRAFT,
                            OmsConstant::PAYMENT_INVOICE_STATUS_DISCARD,
                        ],
                    ]
                ]
            ],
            OmsConstant::CHANGE_HANDLER_OPERATE => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PAYMENT_INVOICE_MANAGE_USER,
                        'user' => ['handler', 'create_user']
                    ],
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::PAYMENT_INVOICE_STATUS_FINISH,
                            OmsConstant::PAYMENT_INVOICE_STATUS_DISCARD
                        ],
                    ]
                ]
            ],
            OmsConstant::APPLY_APPROVAL_OPERATE       => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PAYMENT_INVOICE_CHANGE_STATUS,
                        'user' => ['handler', 'create_user']
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::PAYMENT_INVOICE_STATUS_WAIT,
                            OmsConstant::PAYMENT_INVOICE_STATUS_FINISH,
                            OmsConstant::PAYMENT_INVOICE_STATUS_DISCARD,
                        ],
                    ]
                ]
            ],
            OmsConstant::PAYMENT_OPERATE       => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PAYMENT_INVOICE_CONFIRM,
                        'user' => ['handler', 'create_user']
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::PAYMENT_INVOICE_STATUS_DRAFT,
                            OmsConstant::PAYMENT_INVOICE_STATUS_FINISH,
                            OmsConstant::PAYMENT_INVOICE_STATUS_DISCARD,
                        ],
                    ]
                ]
            ],
            OmsConstant::APPROVAL_UNLOCK => [
                [
                    'task_class' => PrivilegeTask::class,
                    'setting'    => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_PAYMENT_INVOICE_APPROVAL_UNLOCK,
                        ],
                    ]
                ],
//                [
//                    'task_class' => ApprovalUnlockTask::class,
//                    'setting'    => [
//                        'refer_type' => \Constants::TYPE_PAYMENT_INVOICE,
//                    ]
//                ],
            ],
            OmsConstant::EDIT_ATTACHMENT         => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_PAYMENT_INVOICE_EDIT,
                        'user'=>['handler','create_user']
                    ]
                ],
            ],
            OmsConstant::EXPORT_OPERATE => [
                [
                    'task_class' => PrivilegeTask::class,
                    'setting'    => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_PAYMENT_INVOICE_EXPORT_INVOICE,
                        ],
                    ]
                ],

            ],
        ],
        \Constants::TYPE_CASH_COLLECTION_INVOICE => [
            OmsConstant::EDIT_OPERATE         => [
//                //权限校验
//                [
//                    'task_class' => ScopeUserTask::class,
//                    'setting'    => [
//                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_CASH_COLLECTION_INVOICE_EDIT,
//                        'user'=>['handler','create_user']
//                    ]
//                ],
                [
                    'task_class' => PrivilegeTask::class,
                    'setting'    => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_CASH_COLLECTION_INVOICE_EDIT,
                        ],
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::CASH_COLLECTION_INVOICE_STATUS_FINISH,
                        ],
                    ]
                ],
                //审批校验
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION_INVOICE,
                        'message' => '审批中，不支持编辑',
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION_INVOICE,
                    ]
                ],
            ],
            OmsConstant::DELETE_OPERATE        => [
//                //权限校验
//                [
//                    'task_class' => ScopeUserTask::class,
//                    'setting'    => [
//                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_CASH_COLLECTION_INVOICE_REMOVE,
//                        'user' => ['handler', 'create_user']
//                    ],
//                ],
                [
                    'task_class' => PrivilegeTask::class,
                    'setting'    => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_CASH_COLLECTION_INVOICE_REMOVE,
                        ],
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
//                            OmsConstant::CASH_COLLECTION_INVOICE_STATUS_NOT_HANDLE,
                            OmsConstant::CASH_COLLECTION_INVOICE_STATUS_NOT_ALLOCATED,
                            OmsConstant::CASH_COLLECTION_INVOICE_STATUS_PARTIAL_ALLOCATED,
                            OmsConstant::CASH_COLLECTION_INVOICE_STATUS_FINISH,
                        ],
                    ]
                ],
                //审批校验
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION_INVOICE,
                        'message' => '审批中，不支持删除',
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION_INVOICE,
                    ]
                ],
            ],
            OmsConstant::CONFIRM_RECEIVE_OPERATE => [
//                //权限校验
//                [
//                    'task_class' => ScopeUserTask::class,
//                    'setting'    => [
//                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_CASH_COLLECTION_INVOICE_CONFIRM_RECEIVE,
//                        'user' => ['handler', 'create_user']
//                    ],
//                ],
                [
                    'task_class' => PrivilegeTask::class,
                    'setting'    => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_CASH_COLLECTION_INVOICE_CONFIRM_RECEIVE,
                        ],
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::CASH_COLLECTION_INVOICE_STATUS_FINISH,
                        ],
                    ]
                ],
                //审批校验
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION_INVOICE,
                        'message' => '审批中，不支持当前操作',
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION_INVOICE,
                    ]
                ],
            ],
            OmsConstant::CANCEL_RECEIVE_OPERATE => [
//                //权限校验
//                [
//                    'task_class' => ScopeUserTask::class,
//                    'setting'    => [
//                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_CASH_COLLECTION_INVOICE_CANCEL_RECEIVE,
//                        'user' => ['handler', 'create_user']
//                    ],
//                ],
                [
                    'task_class' => PrivilegeTask::class,
                    'setting'    => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_CASH_COLLECTION_INVOICE_CANCEL_RECEIVE,
                        ],
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::CASH_COLLECTION_INVOICE_STATUS_FINISH,
                        ],
                    ]
                ],
                //审批校验
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION_INVOICE,
                        'message' => '审批中，不支持当前操作',
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION_INVOICE,
                    ]
                ],
            ],
            OmsConstant::APPORTION_OPERATE => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_CASH_COLLECTION_INVOICE_APPORTION,
                        'user' => ['handler']
                    ],
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::CASH_COLLECTION_INVOICE_STATUS_FINISH,
                        ],
                    ]
                ],
                //审批校验
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION_INVOICE,
                        'message' => '审批中，不支持当前操作',
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION_INVOICE,
                    ]
                ],
            ],
            //提交
            OmsConstant::CHANGE_STATUS_OPERATE       => [
//                //权限校验
//                [
//                    'task_class' => ScopeUserTask::class,
//                    'setting'    => [
//                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_CASH_COLLECTION_INVOICE_CHANGE_STATUS,
//                        'user' => ['handler', 'create_user']
//                    ]
//                ],
                [
                    'task_class' => PrivilegeTask::class,
                    'setting'    => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_CASH_COLLECTION_INVOICE_CHANGE_STATUS,
                        ],
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::CASH_COLLECTION_INVOICE_STATUS_NOT_HANDLE,
                            OmsConstant::CASH_COLLECTION_INVOICE_STATUS_NOT_ALLOCATED,
                            OmsConstant::CASH_COLLECTION_INVOICE_STATUS_PARTIAL_ALLOCATED,
                            OmsConstant::CASH_COLLECTION_INVOICE_STATUS_FINISH,
                        ],
                    ]
                ],
                //审批校验
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION_INVOICE,
                        'message' => '审批中，不支持当前操作',
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION_INVOICE,
                    ]
                ],
            ],
            OmsConstant::APPROVAL_UNLOCK => [
                [
                    'task_class' => PrivilegeTask::class,
                    'setting'    => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_CASH_COLLECTION_INVOICE_APPROVAL_UNLOCK,
                        ],
                    ]
                ],
                //审批校验
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION_INVOICE,
                        'message' => '审批中，不支持当前操作',
                    ]
                ],
                [
                    'task_class' => ApprovalUnlockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION_INVOICE,
                    ]
                ],
            ],
            OmsConstant::EDIT_ATTACHMENT         => [
                [
                    'task_class' => PrivilegeTask::class,
                    'setting'    => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_CASH_COLLECTION_INVOICE_EDIT,
                        ],
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION_INVOICE,
                    ]
                ],
            ],
        ],
        \Constants::TYPE_COST_INVOICE => [
            OmsConstant::EDIT_OPERATE         => [
                [
                    'task_class' => PrivilegeTask::class,
                    'setting'    => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COST_INVOICE_EDIT,
                        ],
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::COST_INVOICE_STATUS_PARTIAL_PAYMENT,
                            OmsConstant::COST_INVOICE_STATUS_FINISH,
                        ],
                    ]
                ]
            ],
            OmsConstant::DELETE_OPERATE        => [
                [
                    'task_class' => PrivilegeTask::class,
                    'setting'    => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COST_INVOICE_REMOVE,
                        ],
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::COST_INVOICE_STATUS_PARTIAL_PAYMENT,
                            OmsConstant::COST_INVOICE_STATUS_FINISH,
                        ],
                    ]
                ]
            ],
            OmsConstant::CHANGE_HANDLER_OPERATE       => [
                [
                    'task_class' => PrivilegeTask::class,
                    'setting'    => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COST_INVOICE_MANAGE_USER,
                        ],
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::COST_INVOICE_STATUS_PARTIAL_PAYMENT,
                            OmsConstant::COST_INVOICE_STATUS_FINISH,
                        ],
                    ]
                ]
            ],
            OmsConstant::APPROVAL_UNLOCK => [
                [
                    'task_class' => PrivilegeTask::class,
                    'setting'    => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COST_INVOICE_APPROVAL_UNLOCK,
                        ],
                    ]
                ],
//                [
//                    'task_class' => ApprovalUnlockTask::class,
//                    'setting'    => [
//                        'refer_type' => \Constants::TYPE_PAYMENT_INVOICE,
//                    ]
//                ],
            ],
            OmsConstant::EDIT_ATTACHMENT         => [
                [
                    'task_class' => PrivilegeTask::class,
                    'setting'    => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COST_INVOICE_EDIT,
                        ],
                    ]
                ],
            ],
            OmsConstant::APPLY_APPROVAL_OPERATE       => [
                [
                    'task_class' => PrivilegeTask::class,
                    'setting'    => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COST_INVOICE_CHANGE_STATUS,
                        ],
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::COST_INVOICE_STATUS_NOT_PAYMENT,
                            OmsConstant::COST_INVOICE_STATUS_PARTIAL_PAYMENT,
                            OmsConstant::COST_INVOICE_STATUS_FINISH,
                        ],
                    ]
                ]
            ],
            OmsConstant::APPLY_PAYMENT_OPERATE => [
                //权限校验
                [
                    'task_class' => PrivilegeTask::class,
                    'setting' => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COST_INVOICE_VIEW,
                            PrivilegeConstants::PRIVILEGE_CRM_PAYMENT_INVOICE_CREATE,
                        ],
                    ]
                ],
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::COST_INVOICE_STATUS_DRAFT,
                            OmsConstant::COST_INVOICE_STATUS_FINISH,
                        ],
                    ]
                ]
            ],
            OmsConstant::CREATE_AND_CHANGE_STATUS_OPERATE =>[
                [
                    'task_class' => PrivilegeTask::class,
                    'setting'    => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COST_INVOICE_CREATE,
                            PrivilegeConstants::PRIVILEGE_CRM_COST_INVOICE_CHANGE_STATUS,
                        ],
                    ]
                ],
            ],
            OmsConstant::CREATE_AND_APPLY_PAYMENT_OPERATE =>[
                [
                    'task_class' => PrivilegeTask::class,
                    'setting'    => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COST_INVOICE_CREATE,
                            PrivilegeConstants::PRIVILEGE_CRM_COST_INVOICE_CHANGE_STATUS,
                            PrivilegeConstants::PRIVILEGE_CRM_PAYMENT_INVOICE_CREATE,
                        ],
                    ]
                ],
            ],
        ],
        \Constants::TYPE_FORWARDER => [
            OmsConstant::EDIT_OPERATE         => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_FORWARDER_EDIT,
                        'user' => ['handler']
                    ]
                ],
            ],
            OmsConstant::DELETE_OPERATE        => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_FORWARDER_REMOVE,
                        'user' => ['handler']
                    ]
                ],
            ],
            OmsConstant::EDIT_ATTACHMENT        => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_FORWARDER_EDIT,
                        'user' => ['handler']
                    ]
                ],
            ],
            OmsConstant::CHANGE_HANDLER_OPERATE       => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_FORWARDER_EDIT,
                        'user' => ['handler']
                    ]
                ],
                //字段权限校验
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_FORWARDER,
                        'fields' => ['handler']
                    ]
                ],
            ],
        ],
        \Constants::TYPE_SHIPPING_INVOICE => [
            OmsConstant::EDIT_OPERATE => [
                //已完成 不支持编辑
                [
                    'task_class' => StatusTask::class,
                    'setting' => [
                        'disable_status' => [
                            OmsConstant::SHIPPING_INVOICE_STATUS_FINISH,
                        ],
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_SHIPPING_INVOICE_EDIT,
                        'user' => ['handler']
                    ]
                ],
                //审批校验
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SHIPPING_INVOICE,
                        'message' => '审批中，不支持当前操作',
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SHIPPING_INVOICE,
                    ]
                ],
            ],
            OmsConstant::CHANGE_STATUS_OPERATE => [
                //占位
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::SHIPPING_INVOICE_STATUS_DISCARD
                        ],
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_SHIPPING_INVOICE_SET_STATUS,
                        'user' => ['handler']
                    ]
                ],
                //审批校验
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SHIPPING_INVOICE,
                        'message' => '审批中，不支持当前操作',
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SHIPPING_INVOICE,
                    ]
                ],
            ],
            OmsConstant::GENERATE_COST_INVOICE_OPERATE => [
                //草稿 不支持生成费用单
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::SHIPPING_INVOICE_STATUS_DRAFT,
                        ],
                        'message'    => [
                            OmsConstant::SHIPPING_INVOICE_STATUS_DRAFT => '草稿不支持生成'
                        ]
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_COST_INVOICE_CREATE,
                        'user' => ['handler']
                    ]
                ],
            ],
            OmsConstant::SET_NOTICE => [
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [],
                    ]
                ]
            ],
            OmsConstant::GENERATE_OUTBOUND_INVOICE_OPERATE => [
                //草稿 不支持生成出库单
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::SHIPPING_INVOICE_STATUS_DRAFT,
                        ],
                        'message'    => [
                            OmsConstant::SHIPPING_INVOICE_STATUS_DRAFT => '草稿不支持生成'
                        ]
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_SHIPPING_INVOICE_OUTBOUND_GENERATE,
                        'user' => ['handler']
                    ]
                ],
            ],
            OmsConstant::CHANGE_HANDLER_OPERATE => [
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::SHIPPING_INVOICE_STATUS_FINISH,
                            OmsConstant::SHIPPING_INVOICE_STATUS_DISCARD
                        ],
                        'message'    => [
                            OmsConstant::SHIPPING_INVOICE_STATUS_FINISH => '已完成不支持变更处理人'
                        ]
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_SHIPPING_INVOICE_CHANGE_HANDLER,
                        'user' => ['handler']
                    ]
                ],
                //审批校验
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SHIPPING_INVOICE,
                        'message' => '审批中，不支持当前操作',
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SHIPPING_INVOICE,
                    ]
                ],
            ],
            OmsConstant::DELETE_OPERATE => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_SHIPPING_INVOICE_DELETE,
                        'user' => ['handler']
                    ]
                ],
                [
                    'task_class' => ShippingInvoiceDownStreamInvoiceTask::class,
                    'setting' => [
                        'message' => '已关联下游单据或任务，不支持删除',
                    ],
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::SHIPPING_INVOICE_STATUS_WAIT_SHIPMENT,
                            OmsConstant::SHIPPING_INVOICE_STATUS_SHIPMENT,
                            OmsConstant::SHIPPING_INVOICE_STATUS_FINISH,
                        ],
                    ]
                ],
                //审批校验
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SHIPPING_INVOICE,
                        'message' => '审批中，不支持当前操作',
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SHIPPING_INVOICE,
                    ]
                ],
            ],
            OmsConstant::DISCARD_OPERATE => [
                [
                    'task_class' => ShippingInvoiceDownStreamInvoiceTask::class,
                    'setting' => [
                        'message' => '已关联下游单据或任务，不支持作废',
                    ],
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::SHIPPING_INVOICE_STATUS_DRAFT,
                            OmsConstant::SHIPPING_INVOICE_STATUS_DISCARD,
                        ],
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_SHIPPING_INVOICE_DISABLE,
                        'user' => ['handler']
                    ]
                ],
                //审批校验
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SHIPPING_INVOICE,
                        'message' => '审批中，不支持当前操作',
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SHIPPING_INVOICE,
                    ]
                ],

            ],
            OmsConstant::EXPORT_OPERATE => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_SHIPPING_INVOICE_EXPORT_TEMPLATE,
                        'user' => ['handler']
                    ]
                ],
            ],
            OmsConstant::CREATE_OUTBOUND_PRODUCT_TRANSFER => [
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::SHIPPING_INVOICE_STATUS_DRAFT,
                            OmsConstant::SHIPPING_INVOICE_STATUS_DISCARD,
                        ],
                        'message'    => [
                            OmsConstant::SHIPPING_INVOICE_STATUS_DRAFT => '草稿不支持生成'
                        ]
                    ]
                ],
                //审批校验
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SHIPPING_INVOICE,
                        'message' => '审批中，不支持当前操作',
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SHIPPING_INVOICE,
                    ]
                ],
            ],
            OmsConstant::CREATE_OTHER_PRODUCT_TRANSFER => [
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::SHIPPING_INVOICE_STATUS_DRAFT,
                            OmsConstant::SHIPPING_INVOICE_STATUS_DISCARD,
                        ],
                        'message'    => [
                            OmsConstant::SHIPPING_INVOICE_STATUS_DRAFT => '草稿不支持生成'
                        ]
                    ]
                ]
            ],
            OmsConstant::APPROVAL_UNLOCK => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_SHIPPING_INVOICE_APPROVAL_UNLOCK,
                        'user' => ['handler']
                    ]
                ],
//                [
//                    'task_class' => ApprovalUnlockTask::class,
//                    'setting'    => [
//                        'refer_type' => \Constants::TYPE_SHIPPING_INVOICE,
//                    ]
//                ],
            ],
            OmsConstant::EDIT_ATTACHMENT => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_SHIPPING_INVOICE_EDIT,
                        'user' => ['handler']
                    ]
                ],
                //审批校验
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SHIPPING_INVOICE,
                        'message' => '审批中，不支持当前操作',
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_SHIPPING_INVOICE,
                    ]
                ],
            ],
        ],
        \Constants::TYPE_QUOTATION => [
            OmsConstant::DELETE_OPERATE => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_QUOTATION_REMOVE,
                        'user' => ['user_id']
                    ]
                ],
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_QUOTATION,
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_QUOTATION,
                    ]
                ],
            ],
            OmsConstant::EXPORT_OPERATE => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_QUOTATION_EXPORT,
                        'user' => ['user_id']
                    ]
                ],
            ],
            OmsConstant::EDIT_OPERATE => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_QUOTATION_EDIT,
                        'user' => ['user_id']
                    ]
                ],
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_QUOTATION,
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_QUOTATION,
                    ]
                ],
                [
                    'task_class' => CustomStatusTask::class,
                    'setting'    => [
                        'not_end' => true,
                        'field' => 'status',
                        'message' => [
                            'not_end' => '已结束状态，不支持编辑',
                        ]
                    ]
                ],
            ],
            OmsConstant::EDIT_ATTACHMENT => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_QUOTATION_EDIT,
                        'user' => ['user_id']
                    ]
                ],
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_QUOTATION,
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_QUOTATION,
                    ]
                ],
                [
                    'task_class' => CustomStatusTask::class,
                    'setting'    => [
                        'not_end' => true,
                        'field' => 'status',
                        'message' => [
                            'not_end' => '已结束状态，不支持编辑',
                        ]
                    ]
                ],
            ],
            OmsConstant::COPY_OPERATE => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_QUOTATION_CREATE,
                        'user' => ['user_id']
                    ]
                ],
            ],
            OmsConstant::APPROVAL_UNLOCK => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_QUOTATION_APPROVAL_UNLOCK,
                        'user' => ['user_id']
                    ]
                ],
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_QUOTATION,
                    ]
                ],
            ],
            OmsConstant::CHANGE_STATUS_OPERATE => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_QUOTATION_EDIT,
                        'user' => ['user_id']
                    ]
                ],
                //字段权限校验
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_QUOTATION,
                        'fields' => ['status']
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_QUOTATION,
                    ]
                ],
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_QUOTATION,
                    ]
                ],
            ],
            OmsConstant::GENERATE_ORDER_OPERATE => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_ORDER_CREATE,
                        'user' => ['user_id']
                    ]
                ],
            ],
            OmsConstant::SET_NOTICE => [
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [],
                    ]
                ]
            ],
            OmsConstant::CREATE_OPPORTUNITY => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_CREATE,
                        'user' => ['user_id']
                    ]
                ],
                [
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'opportunity_id',
                        'message' => [
                            'module' => 'quotation',
                            'raw' => '已关联商机, 不支持该操作',
                        ],
                    ],
                    [
                        'task_class' => ApprovalPendingTask::class,
                        'setting'    => [
                            'refer_type' => \Constants::TYPE_QUOTATION,
                        ]
                    ],
                ]
            ],
        ],
        \Constants::TYPE_INQUIRY_COLLABORATION => [
            OmsConstant::VIEW_FEEDBACK => [
                [
                    'task_class' => PrivilegeTask::class,
                    'setting' => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_FEEDBACK_VIEW,
                        ],
                    ]
                ],
            ],
            OmsConstant::OWNER_VIEW => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_VIEW,
                        'user' => ['handler']
                    ],
                ],
            ],
            OmsConstant::EXPORT_OPERATE => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_EXPORT,
                        'user' => ['handler']
                    ]
                ],
            ],
            OmsConstant::SET_NOTICE =>[
                //状态
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
                        ],
                    ]
                ]
            ],
            OmsConstant::EDIT_OPERATE => [
                [
                    'task_class' => StatusTask::class,
                    'setting' => [
                        'disable_status' => [
                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
                        ],
                        'message'    => [
                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH => '任务已完成,不支持编辑'
                        ]
                    ]
                ],
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_EDIT,
                        'user' => ['handler']
                    ]
                ],
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_INQUIRY_COLLABORATION,
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_INQUIRY_COLLABORATION,
                    ]
                ],
            ],
            OmsConstant::EDIT_ATTACHMENT => [
                [
                    'task_class' => StatusTask::class,
                    'setting' => [
                        'disable_status' => [
                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
                        ],
                        'message'    => [
                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH => '任务已完成,不支持编辑'
                        ]
                    ]
                ],
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_EDIT,
                        'user' => ['handler']
                    ]
                ],
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_INQUIRY_COLLABORATION,
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_INQUIRY_COLLABORATION,
                    ]
                ],
            ],
            OmsConstant::CHANGE_STATUS_OPERATE => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_CHANGE_STATUS,
                        'user' => ['handler']
                    ]
                ],
//                //字段权限校验
//                [
//                    'task_class' => FieldsPrivilegesTask::class,
//                    'setting'    => [
//                        'refer_type' => \Constants::TYPE_INQUIRY_COLLABORATION,
//                        'fields' => ['status']
//                    ]
//                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_INQUIRY_COLLABORATION,
                    ]
                ],
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_INQUIRY_COLLABORATION,
                    ]
                ],
            ],
            OmsConstant::DELETE_OPERATE => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_DELETE,
                        'user' => ['handler']
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_INQUIRY_COLLABORATION,
                    ]
                ],
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_INQUIRY_COLLABORATION,
                    ]
                ],
            ],
            OmsConstant::CHANGE_HANDLER_OPERATE => [
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_CHANGE_HANDLER,
                        'user' => ['handler']
                    ]
                ],
                //字段权限校验
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_INQUIRY_COLLABORATION,
                        'fields' => ['handler']
                    ]
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
                        ],
                        'message'    => [
                            OmsConstant::SHIPPING_INVOICE_STATUS_FINISH => '已完成不支持变更处理人'
                        ]
                    ]
                ],
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_INQUIRY_COLLABORATION,
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_INQUIRY_COLLABORATION,
                    ]
                ],
            ],

            OmsConstant::FEEDBACK => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_FEEDBACK_VIEW,
                        'user' => ['handler']
                    ]
                ],
            ],
            OmsConstant::BATCH_CREATE_FEEDBACK=>[
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_FEEDBACK_CREATE,
                        'user' => ['handler']
                    ]
                ],
            ],
            OmsConstant::BATCH_ADOPT_FEEDBACK=>[
                [
                    'task_class' => MultiplePrivilegeTask::class,
                    'setting'    => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_FEEDBACK_ADOPTION,
                            PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_ADOPTION,
                        ],
                        'operate' => 'or'
                    ]
                ],
            ],
            OmsConstant::CREATE  => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_CREATE,
                        'user' => ['handler']
                    ]
                ],
            ],
            OmsConstant::TO_QUOTATION=>[
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_QUOTATION_CREATE,
                        'user' => ['handler']
                    ]
                ],
            ],
            OmsConstant::GENERATE_ORDER_OPERATE=>[
                //权限校验
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_ORDER_CREATE,
                        'user' => ['handler']
                    ]
                ],
            ],
            OmsConstant::APPROVAL_UNLOCK => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_APPROVAL_UNLOCK,
                        'user' => ['handler']
                    ]
                ],
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_INQUIRY_COLLABORATION,
                    ]
                ],
                [
                    'task_class' => ApprovalUnlockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_INQUIRY_COLLABORATION,
                    ]
                ],
            ],
        ],
        \Constants::TYPE_INQUIRY_FEEDBACK => [
            OmsConstant::OWNER_VIEW => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_FEEDBACK_VIEW,
                        'user' => ['user_ids']
                    ],
                ],
            ],
            OmsConstant::CREATE => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_FEEDBACK_CREATE,
                        'user' => ['user_ids']
                    ]
                ],
            ],
            OmsConstant::EDIT_OPERATE => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_FEEDBACK_EDIT,
                        'user' => ['user_ids']
                    ]
                ],
                [
                    'task_class' => CustomizeTask::class,
                    'setting'    => [
                    ]
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::IS_ADOPTION,
                        ],
                        'message'    => [
                            OmsConstant::IS_ADOPTION => '已采纳,不支持编辑'
                        ]
                    ]
                ],
            ],
            OmsConstant::DELETE_OPERATE => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting'    => [
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_FEEDBACK_DELETE,
                        'user' => ['user_ids']
                    ]
                ],
                [
                    'task_class' => CustomizeTask::class,
                    'setting' => [
                    ]
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::IS_ADOPTION,
                        ],
                        'message'    => [
                            OmsConstant::IS_ADOPTION => '已采纳,不支持删除'
                        ]
                    ]
                ],
            ],
            OmsConstant::ADOPTION => [
                [
                    'task_class' => MultiplePrivilegeTask::class,
                    'setting' => [
                    'privilege' => [
                        PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_FEEDBACK_ADOPTION,
                        PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_ADOPTION,
                        ],
                        'operate' => 'or'
                    ]
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'disable_status' => [
                            OmsConstant::IS_ADOPTION,
                        ],
                        'message'    => [
                            OmsConstant::IS_ADOPTION => '已采纳,不支持重复采纳'
                        ]
                    ]
                ],
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_INQUIRY_COLLABORATION,
                        'message'    => '无法采纳,审批单在待审批状态'
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting'    => [
                        'refer_type' => \Constants::TYPE_INQUIRY_COLLABORATION,
                    ]
                ],
                [
                    'task_class' => CustomizeTask::class,
                    'setting'    => [
                    ]
                ]
            ],
        ],

        \Constants::TYPE_COMPANY => [
            OmsConstant::WRITE_FOLLOWUP => [
                [
                    // 私海: user_id非空
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'user_id',
                        'negative' => true,
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW,
                    ],
                ],
            ],
            OmsConstant::WRITE_MAIL => [
                [
                    'task_class' => ClientSettingTask::class,
                    'setting'    => [
                        'precondition' => [Preconditions::class, 'bePublicOrPass'], // 公海
                        'external_key' => [
                            Client::EXTERNAL_KEY_CUSTOMER_SHOW_PUBLIC_CONTACT => 1,
                        ],
                    ]
                ],
                //字段权限校验
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'precondition' => [Preconditions::class, 'bePublicOrPass'], // 公海
                        'allow_empty_user' => true,
                        'functional_id' => PrivilegeConstants::FUNCTIONAL_COMPANY_POOL,
                        'privilege_scope' => 'readable',
                        'refer_type' => \Constants::TYPE_CUSTOMER,
                        'fields' => ['email'],
                    ]
                ],
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'precondition' => [Preconditions::class, 'bePrivateOrPass'], // 私海
                        'allow_empty_user' => false,
                        'functional_id' => PrivilegeConstants::FUNCTIONAL_CUSTOMER,
                        'privilege_scope' => 'readable',
                        'refer_type' => \Constants::TYPE_CUSTOMER,
                        'fields' => ['email'],
                    ]
                ],
            ],
            OmsConstant::WRITE_EDM => [
                [
                    'task_class' => ClientSettingTask::class,
                    'setting'    => [
                        'precondition' => [Preconditions::class, 'bePublicOrPass'],  // 公海
                        'external_key' => [
                            Client::EXTERNAL_KEY_CUSTOMER_SHOW_PUBLIC_CONTACT => 1,
                        ],
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => true,
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW,
                    ],
                ],
                //字段权限校验
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'allow_empty_user' => true,
                        'privilege_scope' => 'readable',
                        'refer_type' => \Constants::TYPE_CUSTOMER,
                        'fields' => ['email'],
                    ]
                ],
                [
                    'task_class' => PrivilegeTask::class,
                    'setting' => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_EDM_VIEW,
                        ],
                    ]
                ],
            ],
            OmsConstant::CREATE_SCHEDULE => [
                [
                    // 私海: user_id非空
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'user_id',
                        'negative' => true,
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW,
                    ],
                ],
            ],
            OmsConstant::MOVE_TO_GROUP => [
                [
                    'task_class' => ClientSettingTask::class,
                    'setting'    => [
                        'external_key' => [
                            Client::EXTERNAL_KEY_GROUP_DISABLED => 0,
                        ],
                    ]
                ],
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'allow_empty_user' => false,
                        'privilege_scope' => 'editable',
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'fields' => ['group_id'],
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'precondition' => [Preconditions::class, 'bePrivateOrPass'],  // 私海
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_EDIT,
                        ],
                    ],
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'precondition' => [Preconditions::class, 'bePrivateOrPass'],  // 公海
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_EDIT,
                        ],
                    ],
                ],
            ],
            OmsConstant::MERGE_COMPANY => [
                [
                    // 私海: user_id非空
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'user_id',
                        'negative' => true,
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_EDIT,
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_MERGE,
                        ],
                    ],
                ],
            ],
            OmsConstant::JOIN_AI_QC => [
                [
                    // 私海: user_id非空
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'user_id',
                        'negative' => true,
                    ]
                ],
                [
                    // 未加入谈单监测
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'ai_qc_flag',
                        'negative' => false,
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW,
                        ],
                    ],
                ],
                [
                    'task_class' => PrivilegeTask::class,
                    'setting' => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_OKKI_AI_QC_INSERT,
                        ],
                    ]
                ],
                [
                    'task_class' => HasAiAgentTask::class,
                    'setting' => [
                        'ai_agent_scene' => \AiAgent::AI_AGENT_SCENE_TYPE_COMPANY_QUALITY_CHECK,
                    ],
                ]
            ],
            OmsConstant::READ_AI_QC => [
                [
                    // 已加入谈单监测
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'ai_qc_flag',
                        'negative' => true,
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => true,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW,
                        ],
                    ],
                ],
                [
                    'task_class' => HasAiAgentTask::class,
                    'setting' => [
                        'ai_agent_scene' => \AiAgent::AI_AGENT_SCENE_TYPE_COMPANY_QUALITY_CHECK,
                    ],
                ]
            ],
            OmsConstant::TRANSFER => [
                [
                    // 私海: user_id非空
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'user_id',
                        'negative' => true,
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'precondition' => [Preconditions::class, 'ownersMustContainMe'], // 本人跟进
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_TRANSFER,
                        ],
                    ],
                ],
            ],
            OmsConstant::SHARE => [
                [
                    // 私海: user_id非空
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'user_id',
                        'negative' => true,
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_SHARE,
                        ],
                    ],
                ],
            ],
            OmsConstant::MOVE_TO_PUBLIC => [
                [
                    // 私海: user_id非空
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'user_id',
                        'negative' => true,
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_MOVE_POOL,
                        ],
                    ],
                ],
                [
                    'task_class' => FunctionalTask::class,
                    'setting' => [
                        'functional' => [
                            PrivilegeConstants::FUNCTIONAL_COMPANY_POOL,
                        ],
                    ]
                ],
            ],
            OmsConstant::RE_ASSIGN => [
                [
                    // 私海: user_id非空
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'user_id',
                        'negative' => true,
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_ASSIGN,
                        ],
                    ],
                ],
            ],
            OmsConstant::REMOVE_FOLLOWER => [
                [
                    // 私海: user_id非空
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'user_id',
                        'negative' => true,
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_RELEASE_SPECIFY_USER,
                        ],
                    ],
                ],
            ],
            OmsConstant::CHANGE_PUBLIC_GROUP => [
                [
                    // 公海
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => true,
                        'precondition' => [Preconditions::class, 'bePublicOrPass'],
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_EDIT,
                        ],
                    ],
                ],
                [
                    // 私海
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'precondition' => [Preconditions::class, 'bePrivateOrPass'],
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_EDIT,
                        ],
                    ],
                ],
            ],
            OmsConstant::MOVE_TO_PRIVATE => [
                [
                    // 公海: user_id为空
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'user_id',
                        'negative' => false,
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => true,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_HOLD,
                        ],
                    ],
                ],
            ],
            OmsConstant::ASSIGN => [
                [
                    // 公海: user_id为空
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'user_id',
                        'negative' => false,
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => true,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_HOLD,
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_ASSIGN,
                        ],
                    ],
                ],
            ],
            OmsConstant::ASSESS => [
                [
                    // 私海: user_id不为空
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'user_id',
                        'negative' => true,
                    ]
                ],
                //[
                //    'task_class' => ClientSettingTask::class,
                //    'setting'    => [
                //        'external_key' => [
                //            Client::EXTERNAL_KEY_ASSESS_DIMENSION_SWITCH => 1,
                //        ],
                //    ]
                //],
                [
                    // 首次评分
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'precondition' => [Preconditions::class, 'neverAssessOrPass'],
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_EDIT,
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_ASSESS_FIRST,
                        ],
                    ],
                ],
                [
                    // 二次评分
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'precondition' => [Preconditions::class, 'secondAssessCheck'],
                        'precondition_msg' => ['customer', '所选客户已经二次评分'],
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_EDIT,
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_ASSESS_SECOND,
                        ],
                    ],
                ],
            ],
            OmsConstant::DELETE_OPERATE => [
                [
                    // 公海
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'precondition' => [Preconditions::class, 'bePublicOrPass'],
                        'allow_empty_user' => true,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_REMOVE,
                        ],
                    ],
                ],
                [
                    // 私海
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'precondition' => [Preconditions::class, 'bePrivateOrPass'],
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_REMOVE,
                        ],
                    ],
                ]
            ],
            OmsConstant::EDIT_OPERATE => [
                [
                    // 公海
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'precondition' => [Preconditions::class, 'bePublicOrPass'],
                        'allow_empty_user' => true,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_EDIT,
                        ],
                    ],
                ],
                [
                    'task_class' => ClientSettingTask::class,
                    'setting'    => [
                        'precondition' => [Preconditions::class, 'bePublicOrPass'],
                        'external_key' => [
                            Client::EXTERNAL_KEY_CUSTOMER_SHOW_PUBLIC_CONTACT => 1,
                        ],
                    ]
                ],
                [
                    // 私海
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'precondition' => [Preconditions::class, 'bePrivateOrPass'],
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_EDIT,
                        ],
                    ],
                ]
            ],
            OmsConstant::CANCEL_FOLLOW => [
                [
                    // 私海: user_id不为空
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'user_id',
                        'negative' => true,
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'precondition' => [Preconditions::class, 'ownersMustContainMe'], // 本人跟进
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_RELEASE,
                        ],
                    ],
                ],
            ],
            OmsConstant::CREATE_QUOTATION => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => true,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW,
                        ],
                    ],
                ],
                [
                    'task_class' => ClientSettingTask::class,
                    'setting'    => [
                        'external_key' => [
                            Client::EXTERNAL_KEY_QUOTATION_SWITCH => 1,
                        ],
                    ]
                ],
                [
                    'task_class' => PrivilegeTask::class,
                    'setting' => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_QUOTATION_CREATE,
                        ],
                    ]
                ],
                [
                    'task_class' => FunctionalTask::class,
                    'setting' => [
                        'functional' => [
                            PrivilegeConstants::FUNCTIONAL_QUOTATION,
                        ],
                    ]
                ],
            ],
            OmsConstant::EDIT_TAG => [
                //字段权限校验
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'precondition' => [Preconditions::class, 'bePublicOrPass'], // 公海
                        'allow_empty_user' => true,
                        'functional_id' => PrivilegeConstants::FUNCTIONAL_COMPANY_POOL,
                        'privilege_scope' => 'editable',
                        'refer_type' => \Constants::TYPE_COMPANY_POOL,
                        'fields' => ['cus_tag'],
                    ]
                ],
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'precondition' => [Preconditions::class, 'bePrivateOrPass'], // 私海
                        'allow_empty_user' => false,
                        'functional_id' => PrivilegeConstants::FUNCTIONAL_CUSTOMER,
                        'privilege_scope' => 'editable',
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'fields' => ['cus_tag'],
                    ]
                ],
            ],
        ],
        \Constants::TYPE_LEAD => [
            OmsConstant::WRITE_FOLLOWUP => [
                [
                    // 私海: user_id非空
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'user_id',
                        'negative' => true,
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_LEAD_VIEW,
                    ],
                ],
            ],
            OmsConstant::WRITE_MAIL => [
                [
                    // 私海: user_id非空
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'user_id',
                        'negative' => true,
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_LEAD_VIEW,
                    ],
                ],
                //字段权限校验
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'allow_empty_user' => false,
                        'privilege_scope' => 'readable',
                        'refer_type' => \Constants::TYPE_LEAD_CUSTOMER,
                        'fields' => ['email'],
                    ]
                ],
            ],
            OmsConstant::WRITE_EDM => [
                [
                    // 私海: user_id非空
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'user_id',
                        'negative' => true,
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_LEAD_VIEW,
                    ],
                ],
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'allow_empty_user' => false,
                        'privilege_scope' => 'readable',
                        'refer_type' => \Constants::TYPE_LEAD_CUSTOMER,
                        'fields' => ['email'],
                    ]
                ],
                [
                    'task_class' => PrivilegeTask::class,
                    'setting' => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_EDM_VIEW,
                        ],
                    ]
                ],
            ],
            OmsConstant::CREATE_SCHEDULE => [
                [
                    // 私海: user_id非空
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'user_id',
                        'negative' => true,
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_LEAD_VIEW,
                    ],
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'field' => 'status_id',
                        'disable_status' => [
                            Status::SYS_STATUS_INVALID,
                            Status::SYS_STATUS_CONVERSION,
                        ],
                        'message' => [
                            Status::SYS_STATUS_INVALID => '无效线索不支持该操作',
                            Status::SYS_STATUS_CONVERSION => '已转化线索不支持该操作',
                        ],
                    ]
                ],
            ],
            OmsConstant::CHANGE_STATUS_OPERATE => [
                [
                    // 私海: user_id非空
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'user_id',
                        'negative' => true,
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_LEAD_EDIT,
                    ],
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'field' => 'status_id',
                        'disable_status' => [Status::SYS_STATUS_CONVERSION],
                        'message' => [
                            Status::SYS_STATUS_CONVERSION => '已转化线索不支持该操作',
                        ],
                    ]
                ],
            ],
            OmsConstant::TRANSFER => [
                [
                    'task_class' => ScopeUserTask::class, // 公海
                    'setting' => [
                        'allow_empty_user' => true,
                        'precondition' => [Preconditions::class, 'bePublicOrPass'],
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_LEAD_POOL_TRANSFER,
                    ],
                ],
                [
                    'task_class' => ScopeUserTask::class, // 私海
                    'setting' => [
                        'allow_empty_user' => false,
                        'precondition' => [
                            [Preconditions::class, 'bePrivateOrPass'],
                            [Preconditions::class, 'ownersMustContainMe'], // 本人跟进
                        ],
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_LEAD_NEW_TRANSFER,
                    ],
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'field' => 'status_id',
                        'disable_status' => [
                            Status::SYS_STATUS_INVALID,
                            Status::SYS_STATUS_CONVERSION,
                        ],
                        'message' => [
                            Status::SYS_STATUS_INVALID => '无效线索不支持该操作',
                            Status::SYS_STATUS_CONVERSION => '已转化线索不支持该操作',
                        ],
                    ]
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'field' => 'transform_task_status',
                        'disable_status' => [
                            \LeadTransformTask::STATUS_INIT,
                        ],
                        'message' => [
                            \LeadTransformTask::STATUS_INIT => '线索转化中，不支持此操作',
                        ],
                    ]
                ],
            ],
            OmsConstant::MERGE_LEAD => [
                [
                    // 私海: user_id非空
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'user_id',
                        'negative' => true,
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_LEAD_EDIT,
                            PrivilegeConstants::PRIVILEGE_CRM_LEAD_MERGE,
                        ],
                    ],
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'field' => 'status_id',
                        'disable_status' => [
                            Status::SYS_STATUS_INVALID,
                            Status::SYS_STATUS_CONVERSION,
                        ],
                        'message' => [
                            Status::SYS_STATUS_INVALID => '无效线索不支持该操作',
                            Status::SYS_STATUS_CONVERSION => '已转化线索不支持该操作',
                        ],
                    ]
                ],
            ],
            OmsConstant::MOVE_TO_PUBLIC => [
                [
                    // 私海: user_id非空
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'user_id',
                        'negative' => true,
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_LEAD_MOVE_POOL,
                        ],
                    ],
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'field' => 'status_id',
                        'disable_status' => [
                            Status::SYS_STATUS_CONVERSION,
                        ],
                        'message' => [
                            Status::SYS_STATUS_CONVERSION => '已转化线索不支持该操作',
                        ],
                    ]
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'field' => 'transform_task_status',
                        'disable_status' => [
                            \LeadTransformTask::STATUS_INIT,
                        ],
                        'message' => [
                            \LeadTransformTask::STATUS_INIT => '线索转化中，不支持此操作',
                        ],
                    ]
                ],
            ],
            OmsConstant::ASSESS => [
                [
                    // 私海: user_id不为空
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'user_id',
                        'negative' => true,
                    ],
                ],
                [
                    'task_class' => ClientSettingTask::class,
                    'setting'    => [
                        'external_key' => [
                            Client::EXTERNAL_KEY_ASSESS_DIMENSION_SWITCH => 1,
                        ],
                    ]
                ],
                [
                    // 首次评分
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'precondition' => [Preconditions::class, 'neverAssessOrPass'],
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_LEAD_EDIT,
                            PrivilegeConstants::PRIVILEGE_CRM_LEAD_ASSESS_FIRST,
                        ],
                    ],
                ],
                [
                    // 二次评分
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'precondition' => [Preconditions::class, 'secondAssessCheck'],
                        'precondition_msg' => ['lead', '所选线索已经二次评分'],
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_LEAD_EDIT,
                            PrivilegeConstants::PRIVILEGE_CRM_LEAD_ASSESS_SECOND,
                        ],
                    ],
                ],
            ],
            OmsConstant::RE_ASSIGN => [
                [
                    'task_class' => ScopeUserTask::class, // 公海
                    'setting' => [
                        'allow_empty_user' => true,
                        'precondition' => [Preconditions::class, 'bePublicOrPass'],
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_LEAD_POOL_TRANSFER,
                        ],
                    ],
                ],
                [
                    'task_class' => ScopeUserTask::class, // 私海
                    'setting' => [
                        'allow_empty_user' => false,
                        'precondition' => [Preconditions::class, 'bePrivateOrPass'],
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_LEAD_TRANSFER,
                        ],
                    ],
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'field' => 'status_id',
                        'disable_status' => [
                            Status::SYS_STATUS_INVALID,
                            Status::SYS_STATUS_CONVERSION,
                        ],
                        'message' => [
                            Status::SYS_STATUS_INVALID => '无效线索不支持该操作',
                            Status::SYS_STATUS_CONVERSION => '已转化线索不支持该操作',
                        ],
                    ]
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'field' => 'transform_task_status',
                        'disable_status' => [
                            \LeadTransformTask::STATUS_INIT,
                        ],
                        'message' => [
                            \LeadTransformTask::STATUS_INIT => '线索转化中，不支持此操作',
                        ],
                    ]
                ],
            ],
            OmsConstant::DELETE_OPERATE => [
                [
                    'task_class' => ScopeUserTask::class, // 公海
                    'setting' => [
                        'allow_empty_user' => true,
                        'precondition' => [Preconditions::class, 'bePublicOrPass'],
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_LEAD_POOL_REMOVE,
                        ],
                    ],
                ],
                [
                    'task_class' => ScopeUserTask::class, // 私海
                    'setting' => [
                        'allow_empty_user' => false,
                        'precondition' => [Preconditions::class, 'bePrivateOrPass'],
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_LEAD_REMOVE,
                        ],
                    ],
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'field' => 'status_id',
                        'disable_status' => [
                            Status::SYS_STATUS_CONVERSION,
                        ],
                        'message' => [
                            Status::SYS_STATUS_CONVERSION => '已转化线索不支持该操作',
                        ],
                    ]
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'field' => 'transform_task_status',
                        'disable_status' => [
                            \LeadTransformTask::STATUS_INIT,
                        ],
                        'message' => [
                            \LeadTransformTask::STATUS_INIT => '线索转化中，不支持此操作',
                        ],
                    ]
                ],
            ],
            OmsConstant::EDIT_OPERATE => [
                [
                    'task_class' => ScopeUserTask::class, // 公海
                    'setting' => [
                        'allow_empty_user' => true,
                        'precondition' => [Preconditions::class, 'bePublicOrPass'],
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_LEAD_POOL_EDIT,
                        ],
                    ],
                ],
                [
                    'task_class' => ScopeUserTask::class, // 私海
                    'setting' => [
                        'allow_empty_user' => false,
                        'precondition' => [Preconditions::class, 'bePrivateOrPass'],
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_LEAD_EDIT,
                        ],
                    ],
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'field' => 'status_id',
                        'disable_status' => [
                            Status::SYS_STATUS_INVALID,
                            Status::SYS_STATUS_CONVERSION,
                        ],
                        'message' => [
                            Status::SYS_STATUS_INVALID => '无效线索不支持该操作',
                            Status::SYS_STATUS_CONVERSION => '已转化线索不支持该操作',
                        ],
                    ]
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'field' => 'transform_task_status',
                        'disable_status' => [
                            \LeadTransformTask::STATUS_INIT,
                        ],
                        'message' => [
                            \LeadTransformTask::STATUS_INIT => '线索转化中，不支持此操作',
                        ],
                    ]
                ],
            ],
            OmsConstant::VIEW_CUSTOMER => [
                [
                    // 私海: user_id不为空
                    'task_class' => EmptyFieldTask::class,
                    'setting' => [
                        'field' => 'user_id',
                        'negative' => true,
                    ],
                ],
                [
                    'task_class' => PrivilegeTask::class,
                    'setting' => [
                        'precondition' => [Preconditions::class, 'leadStatusMustBeConversion'], // 线索状态为「已转化」
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW,
                        ],
                        'message' => '无查看权限',
                    ],
                ],
            ],
            OmsConstant::TRANSFORM_TO_CUSTOMER => [
//                [
//                    'task_class' => PrivilegeTask::class,
//                    'setting' => [
//                        'privilege' => [
//                            PrivilegeConstants::PRIVILEGE_CRM_COMPANY_CREATE,
//                        ],
//                    ],
//                ],
                [
                    'task_class' => ScopeUserTask::class, // 公海
                    'setting' => [
                        'allow_empty_user' => true,
                        'precondition' => [Preconditions::class, 'bePublicOrPass'],
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_LEAD_POOL_TRANSFORM,
                        ],
                    ],
                ],
                [
                    'task_class' => ScopeUserTask::class, // 私海
                    'setting' => [
                        'allow_empty_user' => false,
                        'precondition' => [Preconditions::class, 'bePrivateOrPass'],
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_LEAD_TRANSFORM,
                        ],
                    ],
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'field' => 'status_id',
                        'disable_status' => [
                            Status::SYS_STATUS_INVALID,
                            Status::SYS_STATUS_CONVERSION,
                        ],
                        'message' => [
                            Status::SYS_STATUS_INVALID => '无效线索不支持该操作',
                            Status::SYS_STATUS_CONVERSION => '已转化线索不支持该操作',
                        ],
                    ]
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'field' => 'transform_task_status',
                        'disable_status' => [
                            \LeadTransformTask::STATUS_INIT,
                        ],
                        'message' => [
                            \LeadTransformTask::STATUS_INIT => '线索转化中，不支持此操作',
                        ],
                    ]
                ],
            ],
            OmsConstant::TRANSFORM_TO_EXIST_CUSTOMER => [
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'field' => 'status_id',
                        'disable_status' => [
                            Status::SYS_STATUS_INVALID,
                            Status::SYS_STATUS_CONVERSION,
                        ],
                        'message' => [
                            Status::SYS_STATUS_INVALID => '无效线索不支持该操作',
                            Status::SYS_STATUS_CONVERSION => '已转化线索不支持该操作',
                        ],
                    ]
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'field' => 'transform_task_status',
                        'disable_status' => [
                            \LeadTransformTask::STATUS_INIT,
                        ],
                        'message' => [
                            \LeadTransformTask::STATUS_INIT => '线索转化中，不支持此操作',
                        ],
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class, // 公海
                    'setting' => [
                        'allow_empty_user' => true,
                        'precondition' => [Preconditions::class, 'bePublicOrPass'],
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_LEAD_POOL_TRANSFORM,
                        ],
                    ],
                ],
                [
                    'task_class' => ScopeUserTask::class, // 私海
                    'setting' => [
                        'allow_empty_user' => false,
                        'precondition' => [Preconditions::class, 'bePrivateOrPass'],
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_LEAD_TRANSFORM,
                        ],
                    ],
                ],
            ],
            OmsConstant::MARK_INVALID => [
                [
                    'task_class' => ScopeUserTask::class, // 公海
                    'setting' => [
                        'allow_empty_user' => true,
                        'precondition' => [Preconditions::class, 'bePublicOrPass'],
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_LEAD_POOL_EDIT,
                        ],
                    ],
                ],
                [
                    'task_class' => ScopeUserTask::class, // 私海
                    'setting' => [
                        'allow_empty_user' => false,
                        'precondition' => [Preconditions::class, 'bePrivateOrPass'],
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_LEAD_EDIT,
                        ],
                    ],
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'field' => 'status_id',
                        'disable_status' => [
                            Status::SYS_STATUS_INVALID,
                            Status::SYS_STATUS_CONVERSION,
                        ],
                        'message' => [
                            Status::SYS_STATUS_INVALID => '暂无权限', // 隐藏按钮
                            Status::SYS_STATUS_CONVERSION => '已转化线索不支持该操作',
                        ],
                    ]
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'field' => 'transform_task_status',
                        'disable_status' => [
                            \LeadTransformTask::STATUS_INIT,
                        ],
                        'message' => [
                            \LeadTransformTask::STATUS_INIT => '线索转化中，不支持此操作',
                        ],
                    ]
                ],
            ],
            OmsConstant::ADD_INQUIRY_TASK => [
                [
                    'task_class' => FunctionalTask::class,
                    'setting' => [
                        'functional' => [
                            PrivilegeConstants::FUNCTIONAL_PURCHASE_ORDER,
                        ],
                    ],
                ],
                [
                    'task_class' => PrivilegeTask::class,
                    'setting' => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_CREATE,
                        ],
                        'message' => '无操作权限',
                    ],
                ],
            ],
            OmsConstant::EDIT_TAG => [
                //字段权限校验
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'precondition' => [Preconditions::class, 'bePublicOrPass'], // 公海
                        'allow_empty_user' => true,
                        'functional_id' => PrivilegeConstants::FUNCTIONAL_LEAD_POOL,
                        'privilege_scope' => 'editable',
                        'refer_type' => \Constants::TYPE_LEAD_POOL,
                        'fields' => ['cus_tag'],
                    ]
                ],
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'precondition' => [Preconditions::class, 'bePrivateOrPass'], // 私海
                        'allow_empty_user' => false,
                        'functional_id' => PrivilegeConstants::FUNCTIONAL_LEAD,
                        'privilege_scope' => 'editable',
                        'refer_type' => \Constants::TYPE_LEAD,
                        'fields' => ['cus_tag'],
                    ]
                ],
            ],
        ],
        \Constants::TYPE_OPPORTUNITY => [
            OmsConstant::TRANSFER => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_TRANSFER,
                    ],
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting' => [
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'message' => '数据锁定，不支持此操作',
                    ]
                ],
                [
                    'task_class' => OpportunitySecondPrivilegeTask::class,
                    'setting' => [
                        'permission' => PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_TRANSFER,
                        'with_subordinate' => true,
                    ]
                ],
            ],
            OmsConstant::WRITE_MAIL => [
                [
                    'task_class' => ClientSettingTask::class,
                    'setting' => [
                        'external_key' => [
                            Client::EXTERNAL_KEY_OPPORTUNITY_V2_SWITCH => 1,
                        ],
                    ]
                ],
                //字段权限校验
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'allow_empty_user' => false,
                        'privilege_scope' => 'readable',
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'fields' => ['customer_id'],
                    ]
                ],
            ],
            OmsConstant::WRITE_EDM => [
                [
                    'task_class' => ClientSettingTask::class,
                    'setting' => [
                        'external_key' => [
                            Client::EXTERNAL_KEY_OPPORTUNITY_V2_SWITCH => 1,
                        ],
                    ]
                ],
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'allow_empty_user' => false,
                        'privilege_scope' => 'readable',
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'fields' => ['customer_id'],
                    ]
                ],
                [
                    'task_class' => PrivilegeTask::class,
                    'setting' => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_EDM_VIEW,
                        ],
                    ]
                ],
            ],
            OmsConstant::EDIT_OPERATE => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_EDIT,
                        ],
                    ],
                ],
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting' => [
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting' => [
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'message' => '数据锁定，不支持此操作',
                    ]
                ],
                [
                    'task_class' => OpportunitySecondPrivilegeTask::class,
                    'setting' => [
                        'permission' => PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_EDIT,
                        'with_subordinate' => true,
                    ]
                ],
            ],
            OmsConstant::CREATE_SCHEDULE => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_VIEW,
                        ],
                    ],
                ],
            ],
            OmsConstant::ARCHIVE => [
                [
                    'task_class' => ClientSettingTask::class,
                    'setting' => [
                        'external_key' => [
                            Client::EXTERNAL_KEY_OPPORTUNITY_V2_SWITCH => 1,
                        ],
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_ARCHIVE,
                        ],
                    ],
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'field' => 'disable_flag',
                        'disable_status' => [
                            Opportunity::DISABLE_FLAG_TRUE,
                        ],
                        'message' => [
                            Opportunity::DISABLE_FLAG_TRUE => '暂无权限'
                        ],
                    ]
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'field' => 'stage_type',
                        'disable_status' => [
                            Stage::STAGE_ON_GOING_STATUS,
                        ],
                        'message' => [
                            Stage::STAGE_ON_GOING_STATUS => '进行中商机不支持归档'
                        ],
                    ]
                ],
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting' => [
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                    ]
                ],
            ],
            OmsConstant::CANCEL_ARCHIVE => [
                [
                    'task_class' => ClientSettingTask::class,
                    'setting' => [
                        'external_key' => [
                            Client::EXTERNAL_KEY_OPPORTUNITY_V2_SWITCH => 1,
                        ],
                    ]
                ],
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_ARCHIVE,
                        ],
                    ],
                ],
                [
                    'task_class' => StatusTask::class,
                    'setting'    => [
                        'field' => 'disable_flag',
                        'disable_status' => [
                            Opportunity::DISABLE_FLAG_FALSE,
                        ],
                        'message' => [
                            Opportunity::DISABLE_FLAG_FALSE => '暂无权限'
                        ],
                    ]
                ],
            ],
            OmsConstant::COPY_OPERATE => [
                [
                    'task_class' => PrivilegeTask::class,
                    'setting' => [
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_CREATE,
                        ],
                    ]
                ],
            ],
            OmsConstant::DELETE_OPERATE => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_REMOVE,
                        ],
                    ],
                ],
                [
                    'task_class' => ApprovalPendingTask::class,
                    'setting' => [
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                    ]
                ],
                [
                    'task_class' => ApprovalIsLockTask::class,
                    'setting' => [
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'message' => '数据锁定，不支持此操作',
                    ]
                ],
                [
                    'task_class' => OpportunitySecondPrivilegeTask::class,
                    'setting' => [
                        'permission' => PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_REMOVE,
                        'with_subordinate' => true,
                    ]
                ],
            ],
            OmsConstant::OPPORTUNITY_UNLOCK => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_APPROVAL_UNLOCK,
                        ],
                    ],
                ],
                [
                    'task_class' => ApprovalUnlockTask::class,
                    'setting' => [
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'null_message' => true,
                    ]
                ],
                [
                    'task_class' => OpportunitySecondPrivilegeTask::class,
                    'setting' => [
                        'permission' => PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_APPROVAL_UNLOCK,
                        'with_subordinate' => true,
                    ]
                ],
            ],
            OmsConstant::WRITE_FOLLOWUP => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => [
                            PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_TRAIL_CREATE,
                        ],
                    ],
                ],
            ],
            OmsConstant::EDIT_TAG => [
                //字段权限校验
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'allow_empty_user' => true,
                        'functional_id' => PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
                        'privilege_scope' => 'editable',
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'fields' => ['cus_tag'],
                    ]
                ],
            ],
        ],
        \Constants::TYPE_TMS_SUGGESTION => [
            OmsConstant::SUGGESTION_EDIT => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_SUGGESTION_EDIT,
                    ],
                ]
            ],
        ],
        \Constants::TYPE_TMS_SPEECHCRAFT => [
            OmsConstant::SPEECH_CRAFT_EDIT => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_SPEECHCRAFT_EDIT,
                    ],
                ]
            ],
            OmsConstant::SPEECH_CRAFT_DELETE => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_SPEECHCRAFT_REMOVE,
                    ],
                ]
            ],
        ],
        \Constants::TYPE_TRADE_DOCUMENT => [
            OmsConstant::EDIT_TRADE_DOCUMENT_TITLE => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_DOCUMENT_EDIT
                    ]
                ],
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'allow_empty_user' => false,
                        'privilege_scope' => 'readable',
                        'refer_type' => \Constants::TYPE_TRADE_DOCUMENT,
                        'fields' => ['name'],
                    ]
                ],
            ],
            OmsConstant::MODIFY_TRADE_DOCUMENT_PURPOSE => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_DOCUMENT_EDIT
                    ]
                ],
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'allow_empty_user' => false,
                        'privilege_scope' => 'readable',
                        'refer_type' => \Constants::TYPE_TRADE_DOCUMENT,
                        'fields' => ['category'],
                    ]
                ],
            ],
            OmsConstant::TOGGLE_TRADE_DOCUMENT_ACTIVE => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_DOCUMENT_EDIT
                    ]
                ],
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'allow_empty_user' => false,
                        'privilege_scope' => 'readable',
                        'refer_type' => \Constants::TYPE_TRADE_DOCUMENT,
                        'fields' => ['is_accessible'],
                    ]
                ],
            ],
            OmsConstant::PUBLISH_TRADE_DOCUMENT => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_DOCUMENT_EDIT
                    ]
                ],
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'allow_empty_user' => false,
                        'privilege_scope' => 'readable',
                        'refer_type' => \Constants::TYPE_TRADE_DOCUMENT,
                        'fields' => ['status'],
                    ]
                ],
            ],
            OmsConstant::SAVE_TRADE_DOCUMENT => [[
                'task_class' => ScopeUserTask::class,
                'setting' => [
                    'allow_empty_user' => false,
                    'privilege' => PrivilegeConstants::PRIVILEGE_CRM_DOCUMENT_EDIT
                ]
            ]],
            OmsConstant::EDIT_TRADE_DOCUMENT => [[
                'task_class' => ScopeUserTask::class,
                'setting' => [
                    'allow_empty_user' => false,
                    'privilege' => PrivilegeConstants::PRIVILEGE_CRM_DOCUMENT_EDIT
                ]
            ]],
            OmsConstant::EDIT_TRADE_DOCUMENT_SUMMARY => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_DOCUMENT_EDIT
                    ]
                ],
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'allow_empty_user' => false,
                        'privilege_scope' => 'readable',
                        'refer_type' => \Constants::TYPE_TRADE_DOCUMENT,
                        'fields' => ['custom_description'],
                    ]
                ],
            ],
            OmsConstant::EDIT_TRADE_DOCUMENT_SALES_STAGE => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_DOCUMENT_EDIT
                    ]
                ],
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'allow_empty_user' => false,
                        'privilege_scope' => 'readable',
                        'refer_type' => \Constants::TYPE_TRADE_DOCUMENT,
                        'fields' => ['links'],
                    ]
                ],
            ],
            OmsConstant::ASSOCIATE_TRADE_DOCUMENT_CUSTOMER => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_DOCUMENT_EDIT
                    ]
                ],
                [
                    'task_class' => FieldsPrivilegesTask::class,
                    'setting'    => [
                        'allow_empty_user' => false,
                        'privilege_scope' => 'readable',
                        'refer_type' => \Constants::TYPE_TRADE_DOCUMENT,
                        'fields' => ['customer'],
                    ]
                ],
            ],
            OmsConstant::RECEIVE_SETTINGS_ACTIVE => [[
                'task_class' => ScopeUserTask::class,
                'setting' => [
                    'allow_empty_user' => false,
                    'privilege' => PrivilegeConstants::PRIVILEGE_CRM_DOCUMENT_EDIT
                ]
            ]],

            OmsConstant::DELETE_TRADE_DOCUMENT => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_DOCUMENT_REMOVE
                    ]
                ]
            ],

            OmsConstant::COPY_TRADE_DOCUMENT_TO => [
                [
                    'task_class' => ScopeUserTask::class,
                    'setting' => [
                        'allow_empty_user' => false,
                        'privilege' => PrivilegeConstants::PRIVILEGE_CRM_DOCUMENT_CREATE
                    ]
                ]
            ],
        ],
    ];
}
