<?php

namespace common\library\oms\quotation\export;

use common\library\custom_field\CustomFieldService;
use common\library\invoice\Helper;
use common\library\oms\common\OmsConstant;
use common\library\oms\invoice_export\InvoiceExportFormatter;
use common\library\product\AlibabaCategoryAttr;
use common\library\product_v2\ProductHelper;
use common\library\product_v2\sku\SkuAPI;
use common\library\setting\library\fund\relation\CostItemInvoiceRelationApi;
use common\library\util\PgsqlUtil;
use common\models\client\ClientProduct;
use Constants;

class QuotationExportFormatter extends InvoiceExportFormatter
{
    public function __construct($clientId, $userId)
    {
        parent::__construct($clientId, $userId);
        $this->setModuleType(\Constants::TYPE_QUOTATION);
        $this->setPrimaryKey('quotation_id');
        $this->init();
    }

    public function buildMapData()
    {
        parent::buildMapData();
        $data = $this->data;

        if (empty($data))
            return;

        $status = [];
        if (!empty($data['status_info'])) {
            $status = [
                $data['status_info']['id'] => $data['status_info']['name'] ?? '',
            ];
        }

        $this->setMapData(array_merge($this->mapData, [
            'status' => $status,
        ]));
    }

    public function format($data)
    {
//        if(is_array($data['product_list'])){
//            foreach ($data['product_list'] as &$product){
//                $product['gross_profit_margin_1'] = $product['gross_profit_margin'];
//                $product['gross_profit_margin_2'] = trim($product['gross_profit_margin'],'%');
////                $product['unique_id'] = $product['quotation_product_id'];
//            }
//        }

        foreach ($data as $field => $value) {
            switch ($field) {
                case 'update_user':
                    $data[$field] = $data['update_user_info']['nickname'] ?? '';
                    break;
                case 'approval_status':
                    $data[$field] = \common\library\approval_flow\Helper::approvalStatusMap()[$value] ?? '';
                    break;
                case 'opportunity_id':
                    $data[$field] = $data['opportunity_info']['name'] ?? '';
                    break;
                case 'has_transfer_order':
                    $data[$field] = !empty($value) ? '是' : '否';
                    break;
                case 'refer_order_info':
                    $refer_orders = array_column($data['refer_order_info'] ?? [], 'order_no');
                    $refer_orders_str = implode(',', $refer_orders);
                    $data['refer_order_ids'] = $refer_orders_str;
                    break;
                case "create_type":
                    $data[$field] = OmsConstant::CREATE_TYPE_MAP[$value] ?? '';
                    break;
            }
        }

        $data = parent::format($data);

        if (!empty($data['record_list'])){
            $skuIds = array_column($data['record_list'], 'sku_id');
            $skuApi = new SkuAPI($this->clientId, $this->opUserId);
            $productSkuMap = array_column($skuApi->items($skuIds), null, 'sku_id');

            //$productIds = array_column($data['record_list'],'product_id');
            //$productPageResult = ClientProduct::findAllByProductIds($this->clientId,$productIds);
//            $productPageMap = [];
//            $productGroupNameMap = [];
//            if( !empty($productPageResult) ){
//                $productPageMap = array_combine(array_column($productPageResult,'product_id'),$productPageResult);
//                $productGroupIds = array_column($productPageResult,'group_id');
//                $productGroupNameMap = \common\library\group\Helper::getGroupNameMap($this->clientId,Constants::TYPE_PRODUCT,$productGroupIds);
//            }

            $productTotal = [
                'parts_total_amount' => 0,//产品金额（全部主配）
                'parts_total_all_gross_profit' => 0,//产品毛利（全部主配）
                'parts_total_gross_profit' => 0,//产品毛利（单个）
                'parts_total_cost' => 0,//产品成本（单个）
                'parts_total_unit_price' => 0,//产品单价（单个）
            ];

            //获取主产品、普通产品的id
            $productIds = $productCountMap = [];
            foreach ($data['record_list'] as $product ) {
                if (empty($product['master_id'])) {//主产品、普通产品 的master_id 就是为空
                    $productIds[]=$product['unique_id'];
                    $productCountMap[$product['unique_id']]= floatval($product['count'] ?? 0);
                }
            }
            $productTotalMap = array_combine($productIds, array_fill(0, count($productIds), $productTotal));

            //统计全部主配的信息
            foreach ( $data['record_list'] as $product ){
                if (!empty($productTotalMap[$product['unique_id'] ?? 0]) ) {
                    $productTotalMap[$product['unique_id']]['parts_total_amount'] += (floatval(Helper::getMustNumberic($product['cost_amount'] ?? 0)) - floatval(Helper::getMustNumberic($product['other_cost'] ?? 0)));
                    $productTotalMap[$product['unique_id']]['parts_total_all_gross_profit'] += floatval(Helper::getMustNumberic($product['gross_margin'] ?? 0)) * floatval(Helper::getMustNumberic($product['count'] ?? 0));
                    $productTotalMap[$product['unique_id']]['parts_total_cost'] += Helper::getMustNumberic($product['cost_with_tax'] ?? 0);
                }
                if (!empty($productTotalMap[$product['master_id'] ?? 0])) {
                    $productTotalMap[$product['master_id']]['parts_total_amount'] += (floatval(Helper::getMustNumberic($product['cost_amount'] ?? 0)) - floatval(Helper::getMustNumberic($product['other_cost'] ?? 0)));
                    $productTotalMap[$product['master_id']]['parts_total_all_gross_profit'] += floatval(Helper::getMustNumberic($product['gross_margin'] ?? 0)) * floatval(Helper::getMustNumberic($product['count'] ?? 0));
                    $productTotalMap[$product['master_id']]['parts_total_cost'] += Helper::getMustNumberic($product['cost_with_tax'] ?? 0) * (floatval(Helper::getMustNumberic($product['ratio'] ?? 0)));
                }
            }

            foreach ( $data['record_list'] as &$product ){
                //产品规格信息
                $skuStr = [];
                $skuAttributesValue = [];
                if (!empty($skuAttrInfo = $productSkuMap[$product['sku_id']]['attributes_info'] ?? [])) {
                    foreach ($skuAttrInfo as $attr) {
                        $skuStr[] = ($attr['item_name'] ?? '').': '.($attr['value']['item_name'] ?? '');
                        $skuAttributesValue[] = ($attr['value']['item_name'] ?? '');
                    }
                }

                // 获取sku的编号
                if (!empty($productSkuMap[$product['sku_id']]['sku_code'])) {
                    $product['product_no'] = $productSkuMap[$product['sku_id']]['sku_code'];
                }

                $product['sku_attributes'] = implode(";\n", $skuStr);
                $product['sku_attributes_value'] = implode(";\n", $skuAttributesValue);;
                //特殊处理交易产品毛利率
                $product['gross_profit_margin_1'] = $product['gross_profit_margin'];
                $product['gross_profit_margin_2'] = trim($product['gross_profit_margin'],'%');
//                unset($product['gross_profit_margin']);

                // NOTE: 包装毛重小计、包装体积小计格式兼容
                !isset($product['["package_volume_subtotal"]']) && $product['["package_volume_subtotal"]'] = $product['package_volume_subtotal'] ?? 0;
                !isset($product['["package_gross_weight_subtotal"]']) && $product['["package_gross_weight_subtotal"]'] = $product['package_gross_weight_subtotal'] ?? 0;

                $product['package_count'] = 0;
                if (!empty($product['count']) && !empty($product['count_per_package'])) {
                    $product['package_count'] = ($product['count_per_package'] != 0) ? ceil($product['count'] / $product['count_per_package']) : 0;
                }

                //配件产品计算配比
                if (!empty($product['master_id']) && !empty($productCountMap[$product['master_id']] ?? 0)) {
                    $product['ratio'] = round(($product['count'] ?? 0) / $productCountMap[$product['master_id']], 4);
                }

                //主产品,处理主配小计金额
                $productTotalData = $productTotalMap[$product['unique_id']] ?? [];
                if (!empty($productTotalData)) {
                    $productTotalData['parts_total_gross_profit'] = round(empty($product['count']) ? 0 : ($productTotalData['parts_total_all_gross_profit'] / $product['count']), 4);
                    $productTotalData['parts_total_unit_price']=round(empty($product['count']) ? 0 : ($productTotalData['parts_total_amount'] / $product['count']), 4);
                }
                $product = array_replace($product, $productTotalData);

            }
            unset($product);
        }


        if (!empty($data['cost_list'])) {
            $costItemRelationList = CostItemInvoiceRelationApi::getCostItemRelationByInvoice(\Constants::TYPE_QUOTATION);
            $costItemRelationMap = array_column($costItemRelationList, null, 'relation_id');

            foreach ($data['cost_list'] as &$cost){
                if (!empty($cost['cost_item_relation_id'])) {
                    $cost['cost_name'] = $costItemRelationMap[$cost['cost_item_relation_id']]['item_name'] ?? '';
//                    if (isset($cost['cost_remark']) && !empty($cost['cost_remark'])) {
//                        $cost['cost_name'] .= "({$cost['cost_remark']})";
//                    } // 费用名称和费用备注分开展示
                }
                //特殊处理
                if ($cost['percent_type'] == CustomFieldService::ADDITION_FEE_TYPE_PLUS_PERCENTAGE || $cost['percent_type'] == CustomFieldService::ADDITION_FEE_TYPE_SUBTRACT_PERCENTAGE) {
                    $cost['percent_of_total_amount_1'] = $cost['percent_amount'].'%';
                    $cost['percent_of_total_amount_2'] = $cost['percent_amount'];
                }else{
                    $cost['percent_of_total_amount_1'] = '';
                    $cost['percent_of_total_amount_2'] = '';
                }
            }
            unset($cost);
        }

        return $data;
    }

}
