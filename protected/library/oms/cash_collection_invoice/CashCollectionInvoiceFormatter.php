<?php

/**
 * This file is part of php-crm.
 *
 * <AUTHOR> <<EMAIL>>
 * @created_at  2022/11/08 15:47:24
 */

namespace common\library\oms\cash_collection_invoice;

use common\library\account\UserInfo;
use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldList;
use common\library\oms\common\OmsConstant;
use common\library\oms\task\formatter\CapitalAccountInfoTask;
use common\library\oms\task\formatter\CashCollectionInvoiceInfoTask;
use common\library\oms\task\formatter\ExternalFieldDataTask;
use common\library\oms\task\formatter\FormatNullDateFieldTask;
use common\library\oms\task\formatter\OperatePrivilegeTask;
use common\library\oms\task\formatter\RichFieldTask;
use common\library\orm\pipeline\formatter\ApprovalLockFlagTask;
use common\library\orm\pipeline\formatter\PrivilegeFormatTask;
use xiaoman\orm\common\Formatter;

/**
 * Class CashCollectionInvoiceFormatter
 * @package common\library\oms\cash_collection_invoice
 * @method displayCapitalAccountInfo(bool $flag)
 * @method displayAttachmentListSetting(bool $flag)
 * @method displayHasApprovalConfig(bool $flag)
 * @method displayApprovalFlowInfo(bool $flag)
 * @method displayCalInvoiceAmount(bool $flag)
 * @method displayFloatVal(bool $flag)
 * @method displayFormatExternalFieldInfo(bool $flag)
 * @method displayApprovalLockInfo(bool $flag);
 */
class CashCollectionInvoiceFormatter extends Formatter
{
    protected $showFileOffset = 0;
    protected $showFileLimit = 10;

    private $showApprovalFlowEventInfo = false;
    protected $formatDateFields = ['collection_date'];

    protected $approvalProductList = [];    //审批流显示的采购产品数据

    protected $showApprovalFlag = false;

    /**
     * @param array $approvalProductList
     */
    public function setApprovalProductList(array $approvalProductList): void
    {
        $this->approvalProductList = $approvalProductList;
    }

    /**
     * @param bool $showApprovalFlag
     */
    public function setShowApprovalFlag(bool $showApprovalFlag): void
    {
        $this->showApprovalFlag = $showApprovalFlag;
    }

    const MAPPING_SETTING = [
        'privilege_formatter' => [
            'task_class' => PrivilegeFormatTask::class
        ],
        'rich_field'  => [
            'task_class'  => RichFieldTask::class,
        ],
        'record_list' => [
            'task_class'  => CashCollectionInvoiceInfoTask::class,
        ],
        'external_field_data' => [
            'task_class'  => ExternalFieldDataTask::class,
        ],
        'capital_account_info' => [
            'task_class' => CapitalAccountInfoTask::class
        ],
        'attachment_list_setting' => [
            'build_function' => 'buildAttachmentsListInfo',
            'mapping_function' => 'mapAttachmentsListInfo',
        ],
        'has_approval_config' => [
            'build_function' => 'buildHasApprovalConfig',
            'mapping_function' => 'mapHasApprovalConfig',
        ],
        'approval_flow_info' => [
            'build_function' => 'buildApprovalFlowInfo',
            'mapping_function' => 'mapApprovalFlowInfo',
        ],
        'approval_lock_info' => [
            'task_class' => ApprovalLockFlagTask::class,
            'id_key' => 'cash_collection_invoice_id',
            'refer_type' => \Constants::TYPE_CASH_COLLECTION_INVOICE
        ],
        'operate_privilege' => [
            'task_class' => OperatePrivilegeTask::class
        ],
        'cal_invoice_amount' => [
            'build_function'   => 'buildCalInvoiceAmount',
            'mapping_function' => 'mapCalInvoiceAmount',
        ],
        'float_val' => [
            'mapping_function' => 'mapFloatValData',
        ],
        'format_external_field_info' => [
            'mapping_function' => 'mapFormatExternalFieldInfo',
        ],
        'format_null_date_val' => [
            'task_class' => FormatNullDateFieldTask::class
        ]
    ];

    public function mapFormatExternalFieldInfo(&$result, $key, $data)
    {
        $fieldList = new FieldList($this->clientId);
        $fieldList->setType($this->getModuleType());
        $fieldList->setFields(['id', 'base', 'name', 'require', 'hint', 'field_type', 'disable_flag', 'type', 'group_id', 'is_editable', 'group_name', 'value']);
        $fieldList->setEnableFlag(true);
        $fieldList->setBase(0);
        $externalFieldMap = array_column($fieldList->find(), null, 'id');

        $externalFieldData = $result['external_field_data'] ?? [];
        $externalFieldData = is_string($externalFieldData) ? json_decode($externalFieldData, true) : $externalFieldData;

        foreach ($externalFieldData as $fieldId => $item) {
            $data = $externalFieldMap[$fieldId];
            if ($data['field_type'] == CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT)
                $item = is_array($item) ? implode(',', $item) : $item;
            $data['value'] = $item;
            $externalFieldData[$fieldId] = $data;
        }

        $result['external_field_data'] = array_values($externalFieldData);
    }

    public function getModuleType()
    {
        return \Constants::TYPE_CASH_COLLECTION_INVOICE;
    }

    public function webListSetting()
    {
        $this->displayFields([
            'cash_collection_invoice_id',
            'cash_collection_invoice_no',
            'collection_date',
            'type',
            'trade_no',
            'account_name',
            'bank_name',
            'bank_account',
            'company_id',
            'currency',
            'exchange_rate',
            'exchange_rate_usd',
            'amount',
            'amount_rmb',
            'amount_usd',
            'real_amount',
            'real_amount_rmb',
            'real_amount_usd',
            'bank_charge',
            'bank_charge_rmb',
            'bank_charge_usd',
            'capital_account_id',
            'remark',
            'status',
            'create_type',
            'handler',
            'create_user',
            'update_user',
            'create_time',
            'update_time',
        ]);
        $this->displayRichField();
        $this->displayExternalFieldData(true);
        $this->displayCapitalAccountInfo(true);
        $this->displayCalInvoiceAmount(true);
        $this->displayHasApprovalConfig(true);
        $this->displayApprovalFlowInfo(true);
        $this->displayApprovalLockInfo(true);
        $this->displayOperatePrivilege([
            OmsConstant::DELETE_OPERATE,
            OmsConstant::EDIT_OPERATE,
            OmsConstant::CONFIRM_RECEIVE_OPERATE,
            OmsConstant::CANCEL_RECEIVE_OPERATE,
            OmsConstant::APPORTION_OPERATE,
            OmsConstant::APPLY_APPROVAL_OPERATE,
            OmsConstant::CHANGE_STATUS_OPERATE,
            OmsConstant::APPROVAL_UNLOCK,
            OmsConstant::EDIT_ATTACHMENT,
        ]);
        $this->setFormatterPrivilege(true, false);
    }

    public function webInfoSetting()
    {
        $this->displayFields([
            'cash_collection_invoice_id',
            'cash_collection_invoice_no',
            'collection_date',
            'type',
            'trade_no',
            'account_name',
            'bank_name',
            'bank_account',
            'company_id',
            'currency',
            'exchange_rate',
            'exchange_rate_usd',
            'amount',
            'amount_rmb',
            'amount_usd',
            'real_amount',
            'real_amount_rmb',
            'real_amount_usd',
            'bank_charge',
            'bank_charge_rmb',
            'bank_charge_usd',
            'capital_account_id',
            'remark',
            'attachment',
            'status',
            'create_type',
            'handler',
            'create_user',
            'update_user',
            'create_time',
            'update_time',
        ]);
        $this->displayRichField();
        $this->displayRecordList();
        $this->displayExternalFieldData(true);
        $this->displayCapitalAccountInfo(true);
        $this->displayCalInvoiceAmount(true);
        $this->displayHasApprovalConfig(true);
        $this->showApprovalFlowEventInfo = true;
        $this->displayApprovalFlowInfo(true);
        $this->displayApprovalLockInfo(true);
        $this->displayOperatePrivilege([
            OmsConstant::DELETE_OPERATE,
            OmsConstant::EDIT_OPERATE,
            OmsConstant::CONFIRM_RECEIVE_OPERATE,
            OmsConstant::CANCEL_RECEIVE_OPERATE,
            OmsConstant::APPORTION_OPERATE,
            OmsConstant::CHANGE_STATUS_OPERATE,
            OmsConstant::APPLY_APPROVAL_OPERATE,
            OmsConstant::APPROVAL_UNLOCK,
            OmsConstant::EDIT_ATTACHMENT,
        ]);
        $this->setFormatterPrivilege(true, false);
    }

    public function appInfoSetting()
    {
        $this->displayFields([
            'cash_collection_invoice_id',
            'cash_collection_invoice_no',
            'collection_date',
            'type',
            'trade_no',
            'account_name',
            'bank_name',
            'bank_account',
            'company_id',
            'currency',
            'exchange_rate',
            'exchange_rate_usd',
            'amount',
            'amount_rmb',
            'amount_usd',
            'real_amount',
            'real_amount_rmb',
            'real_amount_usd',
            'bank_charge',
            'bank_charge_rmb',
            'bank_charge_usd',
            'capital_account_id',
            'remark',
            'status',
            'handler',
            'create_type',
            'create_user',
            'update_user',
            'create_time',
            'update_time',
        ]);
        $this->displayRichField();
//        $this->displayRecordList();
        $this->displayExternalFieldData(true);
        $this->displayCalInvoiceAmount(true);
        $this->displayCapitalAccountInfo(true);
        $this->displayHasApprovalConfig(true);
        $this->showApprovalFlowEventInfo = true;
        $this->displayApprovalFlowInfo(true);
        $this->displayOperatePrivilege([
            OmsConstant::APPLY_APPROVAL_OPERATE
        ]);
        $this->setFormatterV2Privilege(true, false);
    }

    public function exportSetting()
    {
        $this->displayFields($this->metadata->columnFields());
        $this->displayRichField();
        $this->displayRecordList();
        $this->displayExternalFieldData(true);
        $this->displayCapitalAccountInfo(true);
        $this->displayCalInvoiceAmount(true);
        $this->displayHasApprovalConfig(true);
        $this->displayApprovalFlowInfo(true);
    }

    public function openListSetting()
    {
        $this->displayFields($this->metadata->columnFields());
        $this->displayRichField();
//        $this->displayExternalFieldData(true);
        $this->displayCapitalAccountInfo(true);
//        $this->displayCalInvoiceAmount(true);
    }

    public function openInfoSetting()
    {
        $this->displayFields($this->metadata->columnFields());
        $this->displayRichField();
        $this->displayRecordList();
        $this->displayExternalFieldData(true);
        $this->displayCapitalAccountInfo(true);
        $this->displayCalInvoiceAmount(true);
        $this->displayApprovalFlowInfo(true);
    }

    public function workFlowSetting($displayFields = [])
    {
        $displayFields = empty($displayFields) ? $this->metadata->columnFields() : $displayFields;
        $this->displayFields($displayFields);
        $this->displayRichField();
        $this->displayFormatExternalFieldInfo(true);
        $this->displayCapitalAccountInfo(true);
        $this->displayFormatNullDateVal(true, $this->formatDateFields, FormatNullDateFieldTask::FORMAT_TYPE_EMPTY_STRING);
    }

    public function amountListSetting()
    {
        $this->displayFields([
            'cash_collection_invoice_id',
            'cash_collection_invoice_no',
            'collection_date',
            'currency',
            'exchange_rate',
            'exchange_rate_usd',
            'amount',
            'amount_rmb',
            'amount_usd',
            'bank_charge',
            'bank_charge_rmb',
            'bank_charge_usd',
        ]);
        $this->displayCalInvoiceAmount(true);
    }

    public function attachmentListSetting($offset = 0, $limit = 10)
    {
        $this->showFileOffset = $offset;
        $this->showFileLimit = $limit;
        $this->displayAttachmentListSetting(true);
        $this->displayFields(['cash_collection_invoice_id']);
    }

    public function  displayRichField($flag = true)
    {
        $this->setting['rich_field'] = [
            'argument' => $flag,
            'display_fields' => $this->displayFields
        ];
    }

    public function  displayRecordList($flag = true)
    {
        $this->setting['record_list'] = [
            'argument' => $flag,
            'module_type' => $this->getModuleType(),
            'record_info' => $this->approvalProductList,
            'show_approval_flag' => $this->showApprovalFlag
        ];
    }

    public function  displayExternalFieldData($flag = true)
    {
        $this->setting['external_field_data'] = [
            'argument' => $flag,
            'module_type' => $this->getModuleType()
        ];
    }

    public function displayOperatePrivilege(array $operates)
    {
        $this->setting['operate_privilege'] = [
            'argument' => true,
            'module_type' => $this->getModuleType(),
            'operates' => $operates
        ];
    }

    protected function buildAttachmentsListInfo($key, $data)
    {
        $result = [];
        foreach ($data as $item)
        {
            $item['attachment'] = is_array($item['attachment']) ? $item['attachment'] : json_decode($item['attachment'],true);
            $result['attachment'][$item['cash_collection_invoice_id']] = [
                'attachment' => $this->formatFileList($item['attachment'], $this->showFileOffset, $this->showFileLimit),
                'count' => count($item['attachment'])
            ];
        }
        return $result;
    }

    protected function mapAttachmentsListInfo(&$result, $key, $data)
    {
        $result += $this->getPipelinePrepareData($key)['attachment'][$data['cash_collection_invoice_id']]??[];
    }

    /**
     * 格式化文件列表
     *
     * @param array $list
     * @param int $offset
     * @param int $limit
     *
     * @return array
     */
    protected function formatFileList(array $list, $offset = 0, $limit = 0)
    {
        \ArrayUtil::multisort($list, 'create_time', SORT_DESC);

        $fileList = [];
        if ($limit) {
            $list = array_slice($list, $offset, $limit);
        }

        foreach ($list as $elem) {
            if (!isset($elem['file_id'])) {
                continue;
            }
            $file = new \AliyunUpload();
            $file->loadByFileId($elem['file_id']);
            $file_obj = $file->getFileObject();

            $elem['file_name'] = $file->getFileName();
            $elem['file_size'] = $file->getFileSize();
            $elem['file_url'] = $file->generatePresignedUrl();
            $elem['preview_url'] = $file->getPreview();
            $elem['bucket'] = $file->getBucket();
            $elem['file_key'] = $file->getFileKey();
            $elem['ETag'] = '';//已废弃，ios还有这个字段兼容处理返回空（后续版本让ios删掉，Been 2018-10-26）
            $elem['mime_type'] = $file_obj->file_mime;
            $elem['target_type'] = '2';

            $userInfo = new UserInfo($elem['create_user'] ?? $elem['user_id'], $this->clientId);
            $user = [
                'user_id' => $userInfo['user_id'],
                'nickname' => $userInfo['nickname'],
                'avatar' => $userInfo['avatar']
            ];

            $elem['create_user'] = $user;

            $fileList[] = $elem;
        }

        return $fileList;
    }

    public function buildHasApprovalConfig($key, array $data)
    {
        $hasApprovalConfig = \common\library\approval_flow\Helper::hasEnableApprovalConfig($this->clientId,\Constants::TYPE_CASH_COLLECTION_INVOICE );

        return $hasApprovalConfig;
    }

    public function mapHasApprovalConfig(&$result,$key,$data)
    {
        $hasApprovalConfig = $this->getPipelinePrepareData($key);
        $result['has_approval_config'] = empty($hasApprovalConfig) ? 0 : 1;
    }

    public function buildApprovalFlowInfo($key, array $data)
    {
        $approvalFlowInfoMap = \common\library\approval_flow\Helper::getApprovalProgressInfo(\Constants::TYPE_CASH_COLLECTION_INVOICE, array_column($data, 'cash_collection_invoice_id'), $this->showApprovalFlowEventInfo);

        return $approvalFlowInfoMap;
    }

    public function mapApprovalFlowInfo(&$result,$key,$data)
    {
        $map = $this->getPipelinePrepareData($key);
        $result['approval_flow_info'] = $map[$data['cash_collection_invoice_id']] ?? new \stdClass();

        $approvalFlowInfo = is_a($result['approval_flow_info'],\stdClass::class) ? [] : $result['approval_flow_info'];
        $result['last_approval_info'] = \common\library\approval_flow\Helper::lastApproval($data['cash_collection_invoice_id'], $approvalFlowInfo);

        // app approval_flow_info 中需要 approval_type
        if (!empty($result['last_approval_info']) && !empty($approvalFlowInfo)) {
            $result['approval_flow_info']['approval_type'] = $result['last_approval_info']['approval_type'] ?? 0;
        }
    }

    public function mapFloatValData(&$result, $key, $data)
    {
        if (isset($result['amount']))
            $result['amount'] = round(floatval($result['amount']), 4);
    }

    public function buildCalInvoiceAmount($key, array $data)
    {
        $referData = (new CashCollectionInvoiceInfoTask($this->clientId))->prepare($data);
        $dataMap = [];
        foreach ($referData['cash_collection'] as $collection_id => $item) {

            $dataMap[$item['cash_collection_invoice_id']] = $dataMap[$item['cash_collection_invoice_id']] ?? [
                'allocated_amount'      => 0,
                'allocated_amount_rmb'  => 0,
                'allocated_amount_usd'  => 0,
                'allocated_bank_charge' => 0,
            ];

            $dataMap[$item['cash_collection_invoice_id']]['allocated_amount']      += $item['cash_collection_invoice_amount'];
            $dataMap[$item['cash_collection_invoice_id']]['allocated_amount_rmb']  += $item['cash_collection_invoice_amount_rmb'];
            $dataMap[$item['cash_collection_invoice_id']]['allocated_amount_usd']  += $item['cash_collection_invoice_amount_usd'];
            $dataMap[$item['cash_collection_invoice_id']]['allocated_bank_charge'] += $item['cash_collection_invoice_bank_charge'];
        }

        return $dataMap;
    }

    public function mapCalInvoiceAmount(&$result, $key, $data)
    {
        $dataMap = $this->getPipelinePrepareData($key);
        $result['allocated_amount']      = round($dataMap[$result['cash_collection_invoice_id']]['allocated_amount'] ?? 0, 2);           //已核销金额（回款登记币种）
        $result['allocated_amount_rmb']  = round($dataMap[$result['cash_collection_invoice_id']]['allocated_amount_rmb'] ?? 0, 2);       //已核销金额（CNY）
        $result['allocated_amount_usd']  = round($dataMap[$result['cash_collection_invoice_id']]['allocated_amount_usd'] ?? 0, 2);       //已核销金额（USD）
        $result['current_amount']        = round(floatval($result['amount'] - $result['allocated_amount']), 2); //待核销金额（回款登记币种）
        $result['current_amount_rmb']    = round(floatval($result['amount_rmb'] - $result['allocated_amount_rmb']), 2); //待核销金额（CNY）
        $result['current_amount_usd']    = round(floatval($result['amount_usd'] - $result['allocated_amount_usd']), 2); //待核销金额（USD）
        $result['allocated_bank_charge'] = round($dataMap[$result['cash_collection_invoice_id']]['allocated_bank_charge'] ?? 0, 2);      //已核销手续费（回款登记币种）
        $result['current_bank_charge']   = round(floatval($result['bank_charge'] - $result['allocated_bank_charge']), 2); //待核销手续费（回款登记币种）
    }

    public function displayFormatNullDateVal($flag = true, $dateFields = [], $formatType = '')
    {
        if(empty($dateFields)){
            $dateFields = empty($this->formatDateFields) ? [] : $this->formatDateFields;
        }
        $this->setting['format_null_date_val'] = [
            'argument' => $flag,
            'date_field' => $dateFields,
            'format_type' => $formatType,
        ];
    }
}
