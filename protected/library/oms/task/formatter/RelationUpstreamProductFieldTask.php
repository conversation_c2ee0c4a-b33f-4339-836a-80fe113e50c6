<?php

namespace common\library\oms\task\formatter;

use common\library\oms\common\OmsConstant;
use common\library\oms\common\RecordFilterFactory;
use common\library\product_v2\sku\SkuAPI;
use common\library\util\Arr;
use xiaoman\orm\common\PipelineTask;

/**
 * 单据任务 获取 上游单据产品信息
 */
class RelationUpstreamProductFieldTask extends PipelineTask
{
    private $dataMap = [];

    public function prepare(array $params)
    {
        $imgId2InfoMap = $images = [];
        $skuIds = Arr::uniqueFilterValues(array_column($params, 'sku_id'));
        if (!empty($skuIds)) {
            $skuApi = new SkuAPI($this->clientId);
            $this->dataMap['sku_info'] = array_column($skuApi->items($skuIds), null, 'sku_id');
        }
        //获取上有单据明细表的数据，目前任务明细都是指向一个单据，不会指向多个单据
        $upstreamType = $params[0]['refer_type'];
        $upstreamRecordIds = Arr::uniqueFilterValues(array_column($params, 'sub_refer_id'));
        if (!empty($upstreamRecordIds) && !empty($upstreamType)) {
            if ($upstreamType == \Constants::TYPE_ORDER) {
                $productList = $this->orderProductRecord($upstreamRecordIds);
                $images      = $this->getOrderSourceTypeAndProductImages($productList);
            } else {
                $relationKey = OmsConstant::RELATION_INVOICE_RECORD_KEY_MAP[$upstreamType];
                $recordFiler = RecordFilterFactory::make($upstreamType, $this->clientId);
                $recordFiler->$relationKey = $upstreamRecordIds;
                $recordFiler->enable_flag = \Constants::ENABLE_FLAG_TRUE;
                $recordFiler->skipPrivilegeCheck(true);

                $selectFiled = [
                    $relationKey => function () use ($relationKey) {
                        return "{$relationKey} as sub_refer_id";
                    },
                    'product_name', 'product_cn_name', 'product_model', 'product_image',
                    'unit' => function () {
                        return "unit as product_unit";
                    }
                ];

                //如果是出库任务关联出运单，需要找出子产品，该子产品的信息获取销售订单子产品信息
                if ($upstreamType == \Constants::TYPE_SHIPPING_INVOICE) {
                    $recordFiler->removeWhere(['combine_record_id']);
                    $selectFiled = array_merge($selectFiled, ['invoice_product_id', 'combine_record_id']);
                }

                $recordFiler->select($selectFiled);
                $productList = $recordFiler->rawData();
                foreach ($productList as $productItem) {
                    $fileList = $productItem['product_image'];
                    foreach ($fileList as $item) {
                        $images[] = $item;
                    }
                }
//                if (!empty($fileMap)) {
//                    $this->dataMap += (new FileTask($this->clientId))->prepare($fileMap);
//                }

                //如果是出库任务关联出运单，需要找出子产品，该子产品的信息获取销售订单子产品信息
                if ($upstreamType == \Constants::TYPE_SHIPPING_INVOICE) {
                    $subProduct2InvoiceIdMap =$subProduct2InvoiceInfoMap= [];
                    foreach ($productList as $product) {
                        if (!empty($product['combine_record_id'])) $subProduct2InvoiceIdMap[$product['sub_refer_id']] = $product['invoice_product_id'];
                    }

                    if (!empty($subProduct2InvoiceIdMap)) {
                        $invoiceIds    = array_values($subProduct2InvoiceIdMap);
                        $subProductMap = array_column($this->orderProductRecord($invoiceIds), null, 'sub_refer_id');
                        $images        = array_merge($images, $this->getOrderSourceTypeAndProductImages($productList));
                        foreach ($subProduct2InvoiceIdMap as $subProductRecordId => $invoiceId) {
                            $subProduct2InvoiceInfoMap[$subProductRecordId] = $subProductMap[$invoiceId];
                        }
                        $this->dataMap['sub_product_info'] = $subProduct2InvoiceInfoMap;
                    }
                }
            }

            if (!empty($images)) {
                $imgId2InfoMap = $this->getImageFileInfos($images);
            }
            $this->dataMap['imgId2InfoMap']         = $imgId2InfoMap;
            $this->dataMap['upstream_product_info'] = array_column($productList, null, 'sub_refer_id');
        }

        return $this->dataMap;
    }

    public function run(array $params)
    {
        $sku = $this->dataMap['sku_info'][$params['sku_id']] ?? [];
        $this->referenceData['product_no'] = $sku['product_no'] ?? '';


        //如果是子产品，用sub_product_info获取
        $subProductFlag = false;
        if (!empty($this->dataMap['sub_product_info'][$params['sub_refer_id']])) {
            $subProductFlag = true;
            $upstreamProduct = $this->dataMap['sub_product_info'][$params['sub_refer_id']];
        }else{
            $upstreamProduct = $this->dataMap['upstream_product_info'][$params['sub_refer_id']] ?? [];
        }

        //获取 上有单据明细表的数据
        $this->referenceData['product_name'] = $upstreamProduct['product_name'] ?? '';
        $this->referenceData['product_cn_name'] = $upstreamProduct['product_cn_name'] ?? '';
        $this->referenceData['product_model'] = $upstreamProduct['product_model'] ?? '';
        $this->referenceData['product_unit'] = $upstreamProduct['product_unit'] ?? '';

        if ($params['refer_type'] == \Constants::TYPE_ORDER || $subProductFlag) {
            // todo @young 暂时使用产品库图片，需要改造为订单明细图片
            $this->referenceData['product_image'] = [];
            if (!empty($this->dataMap['imgId2InfoMap']) && !empty($upstreamProduct['product_images'][0]['file_id'])) {
                $imageInfo = $this->dataMap['imgId2InfoMap'][$upstreamProduct['product_images'][0]['file_id']] ?? [];
                if (!empty($imageInfo)) {
                    $this->referenceData['product_image'] = [$imageInfo];
                }
            }
            if (empty($this->referenceData['product_image']) && !empty($sku['image_info'])) {
                $this->referenceData['product_image'] = [array_merge($sku['image_info'], ['user_id' => '0', 'create_time' => xm_function_now()])];
            }
        } else {
            $list = $upstreamProduct['product_image'] ?? [];
            \ArrayUtil::multisort($list, 'create_time', SORT_DESC);
            $fileList = [];
            foreach ($list as $item) {
                if (!empty($this->dataMap['imgId2InfoMap']) && !empty($item['file_id'])) {
                    $file = $this->dataMap['imgId2InfoMap'][$item['file_id']] ?? [];
                    if (empty($file)) {
                        continue;
                    }
                    $userId     = strval($item['user_id'] ?? 0);
                    $fileList[] = $file + ['create_time' => ($item['create_time'] ?? xm_function_now())] + ['create_user' => $userId, 'user_id' => $userId];
                }
            }
            $this->referenceData['product_image'] = $fileList;
        }

        $this->referenceData['sku_attributes'] = $sku['attributes_info'] ?? [];
        $this->referenceData['sku_code'] = $sku['sku_code'] ?? '';
        $this->referenceData['product_type'] = $sku['product_type'] ?? 0;
        $this->referenceData['product_disable_flag'] = $sku['product_disable_flag'] ?? 0;
    }

    protected function getImageFileInfos(array $images)
    {
        $imageIds = Arr::uniqueFilterValues(array_column($images, 'file_id'));
        if (!empty($imageIds)) {
            $imgId2InfoMap = \common\library\file\Helper::fileUrlMap($imageIds);
            foreach ($images as $k => $image) {
                if (!empty($image['file_id']) && isset($imgId2InfoMap[$image['file_id']])) {
                    $images[$k] = array_merge($image, $imgId2InfoMap[$image['file_id']]);
                }
            }
        }
        return array_column($images, null, 'file_id');
    }

    protected function getOrderSourceTypeAndProductImages(array $products)
    {
        $images = [];
        foreach ($products as $product) {
            $productImages = $product['product_images'] ?? [];
            foreach ($productImages as $image) {
                if (!empty($image['file_id'])) {
                    $images[] = $image;
                }
            }
        }
        return $images;
    }


    /**
     * 销售订单产品信息返回
     * @param array $invoiceIds
     * @return array
     */
    protected function orderProductRecord(array $invoiceIds)
    {
        $db = \PgActiveRecord::getDbByClientId($this->clientId);
        $sql = "select r.id as sub_refer_id,r.user_id,        r.product_name, r.product_cn_name, r.product_model, r.product_image,r.product_images, r.unit as product_unit,r.refer_id,o.source_type from tbl_invoice_product_record r left join tbl_order o on r.refer_id=o.order_id where r.client_id={$this->clientId} and r.id in (" . implode(',', $invoiceIds) . ")";
        $products = $db->createCommand($sql)->queryAll();
        foreach ($products as &$product) {
            $product['product_images'] = !empty($product['product_images']) ? json_decode($product['product_images'], true) : [];
        }
        unset($product);
        return $products;
    }
}