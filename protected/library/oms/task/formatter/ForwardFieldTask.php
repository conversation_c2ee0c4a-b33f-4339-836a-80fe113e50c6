<?php

namespace common\library\oms\task\formatter;

use common\library\setting\library\forwarderRate\ForwarderRateApi;
use common\library\util\Arr;

class ForwardFieldTask extends FormatFieldTask
{
    protected $dataMap = [];
    protected $externalFields;

    function getModuleType()
    {
        return \Constants::TYPE_FORWARDER;
    }

    public function getDataMap($params, $needFormatList)
    {
        $forwarderList = (new ForwarderRateApi($this->clientId))->list(0);
        $this->dataMap['forwarder:rate'] = array_column($forwarderList, null, 'rate_id');
        $userIds = [];
        $external_field_data_list = array_column($params, 'external_field_data');
        foreach ($needFormatList as $field => $fieldInfo) {
            switch ($fieldInfo['relation_origin_field']) {
                case 'create_user':
                    $userIds = array_merge($userIds, array_column($external_field_data_list, $field));
                    break;
                case 'handler':
                    foreach (array_column($external_field_data_list,$field) as $elem) {
                        $userIds = array_merge($userIds, $elem);
                    }
                    break;
            }
        }
        $userIds = Arr::uniqueFilterValues($userIds);
        if (!empty($userIds)) {
            $userIds = Arr::uniqueFilterValues($userIds);
            $userList = \common\library\account\Helper::getBatchUserInfo($this->clientId, $userIds);
            $userList = array_map(function ($elem) {
                return ['user_id' => $elem->user_id, 'nickname' => $elem->nickname, 'avatar' => $elem->avatar];
            }, $userList);
            $userMap = array_combine(array_column($userList, 'user_id'), $userList);
            $this->dataMap['forwarder:user'] = $userMap;
        }
    }

    function formatRelationField($relation_origin_field, $value)
    {
        switch ($relation_origin_field) {
            case 'handler':
                if (is_array($value)){
                    $arr = [];
                    foreach ($value as $item) {
                        $arr[] = $this->dataMap['forwarder:user'][$item]['nickname']??"";
                    }
                    $value = implode(';', $arr);
                }
                break;
            case 'rate_id':
                $value = $this->dataMap['forwarder:rate'][$value]['name'] ?? "";
                break;
            case 'social_platform':
                if (is_array($value)){
                    $arr = [];
                    foreach ($value as $item) {
                        $arr[] = $item['account'] . '-' . $item['platform'];
                    }
                    $value = implode(';', $arr);
                }

                break;
        }
        return $value;
    }
}