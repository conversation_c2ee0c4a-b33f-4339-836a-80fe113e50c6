<?php

namespace common\library\oms\task\formatter;

use common\library\invoice\InvoiceProductRecordList;
use common\library\oms\product_transfer\outbound\OutboundProductTransferAPI;
use common\library\oms\product_transfer\ProductTransferHelper;
use common\library\oms\shipping_invoice\record\ShippingRecordFilter;
use common\library\util\Arr;
use xiaoman\orm\common\PipelineTask;

/**
 * 单据出库任务查询 - 维度（出库任务、出运单）
 * 通过module去判断
 */
class InvoiceOutboundCountTask extends PipelineTask
{
    protected $dataMap = [];
    protected $module;

    protected $invoice_key_map = [
        \Constants::TYPE_SHIPPING_INVOICE => 'shipping_invoice_id',
    ];
    protected $record_key_map = [
        \Constants::TYPE_SHIPPING_INVOICE => 'shipping_record_id',
    ];

    public function prepare(array $params)
    {
        $this->module = $this->setting['module'] ?? 0;
        $record_key = $this->record_key_map[$this->setting['module'] ?? -1] ?? 'sub_refer_id';
        $invoice_key = $this->invoice_key_map[$this->setting['module'] ?? -1] ?? 'refer_id';

        $invoiceIds = array_unique(array_column($params, $invoice_key));
        $recordIds = array_unique(array_column($params, $record_key));

        //1、$this->dataMap['order_sale_count'] 需要做区分，是出运单还是出库任务
        //2、出库任务需要看对应的refer_type是什么,去对应的查询相关单据
        $referType = Arr::uniqueFilterValues(array_column($params, 'refer_type'))[0] ?? 0;

        $recordCountMap = [];
        if ($this->module == \Constants::TYPE_SHIPPING_INVOICE || $referType == \Constants::TYPE_SHIPPING_INVOICE) {
            $recordCountMap = $this->getShippingRecordCountMap($recordIds);
        }else if($referType == \Constants::TYPE_ORDER){
            $recordCountMap = $this->getOrderProductCountMap($recordIds);
        }

        $this->dataMap['order_sale_count'] = $recordCountMap;

        $outboundTransferApi = new OutboundProductTransferAPI($this->clientId);
        // 获取订单直接/间接关联的已确认出库单明细（即已下单出库数）
        $checkPurchaseCountList = $outboundTransferApi->getOrderRelateOutboundInvoiceRecordCount($invoiceIds);
        $this->dataMap['check_outbound_count'] = array_column($checkPurchaseCountList, 'check_outbound_count', 'sub_refer_id');

        // 订单关联的出库任务（非草稿状态）明细数（即已安排出库数）
        list($taskOutboundCountList, $taskOutboundTransferList) = $outboundTransferApi->getOrderRelateOutboundTransferRecordCount($invoiceIds);
        $this->dataMap['task_outbound_count'] = $taskOutboundCountList;
        $orderRelateTaskRecordIds = array_unique(array_column($taskOutboundTransferList, 'transfer_invoice_record_id')); // 与本页订单相关联的非草稿采购任务id

        // 订单关联的出库任务关联的已确定出库单明细数
        $checkTaskOutboundCountList = $outboundTransferApi->getOutboundTransferRelateOutboundInvoiceRecordCount($orderRelateTaskRecordIds);
        $this->dataMap['check_task_outbound_count'] = array_column($checkTaskOutboundCountList, 'check_task_outbound_count', 'sub_refer_id');

        return $this->dataMap;
    }

    public function run(array $params)
    {
        $record_key = $this->record_key_map[$this->setting['module']??-1] ?? 'sub_refer_id';

        $this->referenceData['check_outbound_count'] = intval($this->dataMap['check_outbound_count'][$params[$record_key]] ?? 0); //已下单出库数
//        $this->referenceData['task_outbound_count'] = $this->dataMap['task_outbound_count'][$params['sub_refer_id']] ?? 0;   // 已安排出库数
        $this->referenceData['task_outbound_count'] = isset($this->dataMap['task_outbound_count'][$params[$record_key]]) ? ($this->dataMap['task_outbound_count'][$params[$record_key]] - ($params['reach_count']??0)) : 0;   // 已安排出库数(需要剔除本任务的本次需出库数）
        $checkTaskOutboundCount = intval($this->dataMap['check_task_outbound_count'][$params[$record_key]] ?? 0);        // 订单关联任务所关联的出库明细
        $this->referenceData['check_task_outbound_count'] = $checkTaskOutboundCount;
        $this->referenceData['to_outbound_count'] = ($this->dataMap['order_sale_count'][$params[$record_key]] ?? 0) - $this->referenceData['task_outbound_count'];   //待出库数量
        $this->referenceData['to_outbound_count'] = ProductTransferHelper::formatTransferCount($this->referenceData['to_outbound_count']);
        if ($this->referenceData['to_outbound_count'] < 0) {
            $this->referenceData['to_outbound_count'] = 0;
        }
    }

    //获取销售产品数量的map
    public function getOrderProductCountMap($invoiceProductIds)
    {
        if (empty($invoiceProductIds)) {
            return [];
        }
        // 销售数量
        $invoiceProductList = new InvoiceProductRecordList(\User::getLoginUser()->getUserId());
        $invoiceProductList->setType(\Constants::TYPE_ORDER);
        $invoiceProductList->setInvoiceProductIds($invoiceProductIds);
        $invoiceProductList->setFields([
            'id as order_record_id',
            'count  as sale_count',
        ]);
        return array_column($invoiceProductList->find(), 'sale_count', 'order_record_id');
    }

    //获取出运明细数量的map
    public function getShippingRecordCountMap($shippingRecordIds)
    {
        if (empty($shippingRecordIds)) {
            return [];
        }

        $shippingRecordFilter = new ShippingRecordFilter($this->clientId);
        $shippingRecordFilter->shipping_record_id = $shippingRecordIds;
        $shippingRecordFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $shippingRecordFilter->select([
            'shipping_record_id',
            'shipping_count',
        ]);

        return array_column($shippingRecordFilter->rawData(), 'shipping_count', 'shipping_record_id');
    }

}