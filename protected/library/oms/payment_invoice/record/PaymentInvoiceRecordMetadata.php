<?php

namespace common\library\oms\payment_invoice\record;

use xiaoman\orm\metadata\Metadata;

class PaymentInvoiceRecordMetadata extends Metadata
{
    protected $columns = [
        'payment_record_id' => [
            'type' => 'bigint',
            'name' => 'payment_record_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => ['enable' => true, 'batch' => true,]
        ],
        'payment_invoice_id' => [
            'type' => 'bigint',
            'name' => 'payment_invoice_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => ['enable' => true,]
        ],
        'client_id' => [
            'type' => 'bigint',
            'name' => 'client_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => ['enable' => true,]
        ],
        'enable_flag' => [
            'type' => 'smallint',
            'name' => 'enable_flag',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => ['enable' => true, 'batch' => true,]
        ],
        'refer_id' => [
            'type' => 'bigint',
            'name' => 'refer_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => ['enable' => true,]
        ],
        'refer_type' => [
            'type' => 'smallint',
            'name' => 'refer_type',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => ['enable' => true, 'batch' => true,]
        ],
        'payable_invoice_id' => [
            'type' => 'bigint',
            'name' => 'payable_invoice_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => ['enable' => true,]
        ],
        'amount' => [
            'type' => 'numeric',
            'name' => 'amount',
            'nullable' => 0,
            'php_type' => 'float',
            'filter' => ['enable' => true,]
        ],
        'bank_charge' => [
            'type' => 'numeric',
            'name' => 'bank_charge',
            'nullable' => 0,
            'php_type' => 'float',
            'filter' => ['enable' => true,]
        ],
        'real_amount' => [
            'type' => 'numeric',
            'name' => 'real_amount',
            'nullable' => 0,
            'php_type' => 'float',
            'filter' => ['enable' => true,]
        ],
        'create_time' => [
            'type' => 'datetime',
            'php_type' => 'string',
            'filter' => ['enable' => true, 'range' => true],
        ],
        'create_user' => [
            'type' => 'bigint',
            'name' => 'create_user',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => ['enable' => true,]
        ],
        'update_time' => [
            'type' => 'datetime',
            'php_type' => 'string',
            'filter' => ['enable' => true, 'range' => true],
        ],
        'update_user' => [
            'type' => 'bigint',
            'name' => 'update_user',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => ['enable' => true,]
        ],
        'external_field_data' => [
            'type' => 'jsonb',
            'name' => 'external_field_data',
            'nullable' => 0,
            'php_type' => 'array',
            'filter' => ['enable' => true, 'json' => true,]
        ]
    ];

    public static function table()
    {
        return 'tbl_payment_record';
    }

    public static function dataSource()
    {
        return Metadata::DATA_SOURCE_POSTGRESQL_CLIENT;
    }

    public static function singeObject()
    {
        return PaymentInvoiceRecord::class;
    }

    public static function batchObject()
    {
        return BatchPaymentInvoiceRecord::class;
    }


    public static function objectIdKey()
    {
        return 'payment_record_id';
    }

    public static function formatter()
    {
        return PaymentInvoiceRecordFormatter::class;
    }

    public static function operator()
    {
        return PaymentInvoiceRecordOperator::class;
    }

    public static function filter()
    {
        return PaymentInvoiceRecordFilter::class;
    }


}