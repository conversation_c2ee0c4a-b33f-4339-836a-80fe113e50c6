<?php

namespace common\library\oms\order;

use common\library\account\Client;
use common\library\alibaba\order\AlibabaOrderRelationList;
use common\library\alibaba\store\AlibabaStoreService;
use common\library\button\order\OrderButtonFactory;
use common\library\cash_collection\CashCollection;
use common\library\customer_v3\company\list\CompanyList;
use common\library\customer_v3\customer\CustomerList;
use common\library\department\DepartmentService;
use common\library\inquiry_collaboration\InquiryCollaborationFilter;
use common\library\invoice\export\InvoiceExportFileList;
use common\library\invoice\Helper;
use common\library\invoice\order_erp_external\OrderErpExternalApi;
use common\library\invoice\status\InvoiceStatusService;
use common\library\lead\LeadList;
use common\library\object\field\formatter\Factory as FormatterFactory;
use common\library\object\object_define\Constant;
use common\library\oms\capital_account\CapitalAccountFilter;
use common\library\oms\cost_invoice\CostInvoiceApi;
use common\library\oms\cost_invoice\CostInvoiceFilter;
use common\library\oms\order_link\OrderLink;
use common\library\oms\order_product\BatchOrderProduct;
use common\library\oms\order_product\OrderProductMetadata;
use common\library\oms\product_transfer\outbound\OutboundProductTransferAPI;
use common\library\oms\product_transfer\ProductTransferAPI;
use common\library\oms\product_transfer\ProductTransferHelper;
use common\library\oms\product_transfer\purchase\PurchaseProductTransferAPI;
use common\library\oms\quotation\QuotationFilter;
use common\library\oms\shipping_invoice\ShippingInvoiceApi;
use common\library\oms\shipping_invoice\ShippingInvoiceFilter;
use common\library\oms\task\formatter\OrderProductListTask;
use common\library\opportunity\OpportunityList;
use common\library\orm\pipeline\formatter\FieldV2FormatTask;
use common\library\orm\pipeline\formatter\order\ExportFormatterTask;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeFieldV2;
use common\library\product_v2\ProductConstant;
use common\library\product_v2\relation\CombineProductRelationAPI;
use common\library\purchase\purchase_order\PurchaseOrderFilter;
use common\library\purchase\purchase_order_product\PurchaseOrderProductFilter;
use common\library\setting\library\fund\relation\CostItemInvoiceRelationApi;
use common\library\util\Arr;
use xiaoman\orm\common\FormatterV2;
use xiaoman\orm\database\data\In;
use xiaoman\orm\database\data\InArray;
use common\library\oms\order_link\Constant as OrderLinkConstant;
use common\library\alibaba\Constant as AlibabaConstant;
use function DeepCopy\deep_copy;
use common\library\oms\common\OmsConstant;
use common\library\inquiry_collaboration\InquiryCollaboration;

/**
 * class OrderFormatter
 * @package common\library\oms\order
 * @method displayPin(bool $flag)
 * @method displayStatusInfo(bool $flag)
 * @method displayUsersInfo(bool $flag)
 * @method displayUpdateUserInfo(bool $flag)
 * @method displayDepartmentsInfo(bool $flag)
 * @method displayReferLockInfo(bool $flag)
 * @method displayApprovalFlowInfo(bool $flag)
 * @method displayApprovalProductList(bool $flag)
 * @method displayProductList(bool|OrderProductListTask $flag)
 * @method displayAllowExport(bool $flag)
 * @method displayCompanyInfo(bool $flag)
 * @method displayOpportunityInfo(bool $flag)
 * @method displayLeadInfo(bool $flag)
 * @method displayCustomerInfo(bool $flag)
 * @method displayAlibabaStatusInfo(bool $flag)
 * @method displayAlibabaOrderInfo(bool $flag)
 * @method displayStoreInfo(bool $flag)
 * @method displayCapitalAccountInfo(bool $flag)
 * @method displayLinkInfo(bool $flag)
 * @method displayLinkStatus(bool $flag)
 * // * @method displayAmountInfo(bool $flag) // 即displayCostList
 * @method displayShippingCostList(bool $flag)
 * @method displayCashCollectionStatus(bool $flag)
 * @method displayCashCollectionInfo(bool $flag)
 * @method displayCanChangeCurrency(bool $flag) //这个仅用于单个订单，批量的情况就需要改造
 * @method displayCostInvoiceInfo(bool $flag)
 * @method displayCostList(bool $flag)
 * @method displayCostItems(bool $flag)
 * @method displayStatusAction(bool $flag)
 * @method displayQuotationInfo(bool $flag)
 * @method displayProgressInfo(bool $flag)
 * @method displaySystemInfo(bool $flag)
 * @method displayReferInvoiceProductCount(bool $flag)
 * @method displayOperatePrivilege(bool $flag)
 * @method displayOperateButtonAccess(bool $flag)
 * @method displayTransferInfo(bool $flag)
 * @method displayErpStatusInfo(bool $flag)
 * @method displayExportFileCount(bool $flag)
 * @method displayAlibabaAccountInfo(bool $flag)
 * @method displayFieldFormatter(bool|FieldV2FormatTask $flag)
 * @method displayStatistics(bool $true)
 * @method displayInquiryCollaborationInfo(bool $flag)
 */
class OrderFormatter extends FormatterV2
{

    private $showApprovalFlowEventInfo = false;
    private $showAllowExport = false;
    private $allowExportDraft = false;
    protected $showCashCollectionDetail = true;
    protected $showOperateButtonAccess = [];
    protected $showTransferInfo = false;
    protected $showTransferType;
    protected $ignorePurchaseCountDone = 0;
    protected array $showLink = [];
    protected $approvalProductList;
    protected $scene;

    protected $productImageFormatter = false; //下推产品图片改造为数组格式，与其他单据的product_image结构保持一致

    const MAPPING_SETTING = [
        'approval_flow_info' => [
            'build_function' => 'buildApprovalFlowInfo',
            'mapping_function' => 'mapApprovalFlowInfo',
        ],
        'refer_lock_info' => [
            'build_function' => 'buildReferLockInfo',
            'mapping_function' => 'mapReferLockInfo',
        ],
        'pin' => [
            'build_function' => 'buildPinMap',
            'mapping_function' => 'mapPin',
            'mapping_fields' => ['order_id'],
        ],
        'quotation_info' => [
            'build_function' => 'buildQuotationMap',
            'mapping_function' => 'mapQuotation',
        ],
        'status_info' => [
            'build_function' => 'buildStatusInfoMap',
            'mapping_function' => 'mapStatusInfo',
        ],
        'product_list' => [
            'task_class' => OrderProductListTask::class,
        ],
        'approval_product_list' => [
            'build_function' => 'buildApprovalProductList',
            'mapping_function' => 'mapApprovalProductList',
        ],
        'field_formatter' => [
            'task_class' => FieldV2FormatTask::class
        ],
        'users_info' => [
            'build_function' => 'buildUsersMap',
            'mapping_function' => 'mapUsers',
            'mapping_fields' => ['users'],
        ],
        // 没有update_user字段，只能硬编码出数据
        'update_user_info' => [
            'build_function' => 'buildUpdateUserInfoMap',
            'mapping_function' => 'mapUpdateUserInfo',
        ],
        'departments_info' => [
            'build_function' => 'buildDepartmentsMap',
            'mapping_function' => 'mapDepartments',
            'mapping_fields' => ['departments'],
        ],
        'company_info' => [
            'build_function' => 'buildCompanyInfoMap',
            'mapping_function' => 'mapCompanyInfo',
        ],
        'opportunity_info' => [
            'build_function' => 'buildOpportunityInfoMap',
            'mapping_function' => 'mapOpportunityInfo',
        ],
        'lead_info' => [
            'build_function' => 'buildLeadInfoMap',
            'mapping_function' => 'mapLeadInfo',
        ],
        'customer_info' => [
            'build_function' => 'buildCustomerInfoMap',
            'mapping_function' => 'mapCustomerInfo',
        ],
        'alibaba_status_info' => [
            'mapping_function' => 'mapAlibabaStatusInfo',
        ],
        'alibaba_order_info' => [
            'build_function' => 'buildAlibabaOrderInfo',
            'mapping_function' => 'mapAlibabaOrderInfo',
        ],
        'store_info' => [
            'build_function' => 'buildStoreInfo',
            'mapping_function' => 'mapStoreInfo',
        ],
        'capital_account_info' => [
            'build_function' => 'buildCapitalAccountInfo',
            'mapping_function' => 'mapCapitalAccountInfo',
        ],
        'link_info' => [
            'build_function' => 'buildLinkInfo',
            'mapping_function' => 'mapLinkInfo',
        ],
        'link_status' => [
            'mapping_function' => 'mapLinkStatus',
        ],
        'shipping_cost_list' => [
            'build_function' => 'buildShippingCostList',
            'mapping_function' => 'mapShippingCostList',
        ],
        'cash_collection_status' => [
            'build_function' => 'buildCashCollectionStatus',
            'mapping_function' => 'mapCashCollectionStatus',
        ],
        'cash_collection_info' => [
            'build_function' => 'buildCashCollectionInfo',
            'mapping_function' => 'mapCashCollectionInfo',
        ],
        'cost_invoice_info' => [
            'build_function' => 'buildCostInvoiceInfo',
            'mapping_function' => 'mapCostInvoiceInfo',
        ],
        'cost_list' => [
            'build_function' => 'buildCostListInfo',
            'mapping_function' => 'mapCostListInfo',
        ],
        'cost_items' => [
            'build_function' => 'buildCostListInfo',
            'mapping_function' => 'mapCostItems',
        ],
        'status_action' => [
            'mapping_function' => 'mapStatusAction',
        ],
        'can_change_currency' => [
            'mapping_function' => 'mapCanChangeCurrency',
        ],
        'progress_info' => [
            'build_function' => 'buildProgressInfo',
            'mapping_function' => 'mapProgressInfo',
        ],
        'system_info' => [
            'mapping_function' => 'mapSystemInfo',
        ],
        'refer_invoice_product_count' => [
            'mapping_function' => 'mapReferInvoiceProductCount',
        ],
        'operate_privilege' => [
            'mapping_function' => 'mapOperatePrivilege',
        ],
        'operate_button_access' => [
            'mapping_function' => 'mapOperateButtonAccess',
        ],
        'transfer_info' => [
            'build_function' => 'buildTransferInfo',
            'mapping_function' => 'mapTransferInfo',
        ],
        'erp_status_info' => [
            'build_function' => 'buildErpStatusInfo',
            'mapping_function' => 'mapErpStatusInfo',
        ],
        'export_file_count' => [
            'build_function' => 'buildExportFileCount',
            'mapping_function' => 'mapExportFileCount',
        ],
        'alibaba_account_info' => [
            'build_function' => 'buildAlibabaAccountInfo',
            'mapping_function' => 'mapAlibabaAccountInfo',
        ],
        'statistics' => [
            'mapping_function' => 'mapStatistics',
        ],
        'inquiry_collaboration_info' => [
            'build_function' => 'buildInquiryCollaborationInfoMap',
            'mapping_function' => 'mapInquiryCollaborationInfo',
        ],
        'export_formatter' => [
            'task_class' => ExportFormatterTask::class,
        ],
    ];

    public function setScene($scene){
        $this->scene = $scene;
    }

    public function displayExportFormatter($argument, array $params = [])
    {
        $this->setting['export_formatter']=[
            'argument' => $argument,
            'params' => $params
        ];
    }

    public function formatExportInfoSetting()
    {
        $this->displayFields($this->metadata->getMappedAttributeKeys());
        $productListTask = new OrderProductListTask($this->clientId);
        $productListTask->setSetting([
            'showShippingRecordInfo'      => true,
            'displayAssocPlatformProduct' => true,
            'displayProductInfo'          => true,
            'displayCombineProduct'       => true,
            'displayProductImageInfoList' => $this->productImageFormatter,
        ]);
        $this->displayProductList($productListTask);
    }

    public function functionFieldInfoSetting($params = [])
    {
        extract($params);
        $this->fieldSetting();
        $fieldFormatterTask = new  FieldV2FormatTask($this->clientId);
        $fieldFormatterTask->setFieldInfo($this->fieldSetting);
        $fieldFormatterTask->setObjName(Constant::OBJ_ORDER);
        $fieldFormatterTask->setDisplayFields($this->displayFields);
        if (!empty($valueNeedString)) {
            $fieldFormatterTask->setValueNeedString($valueNeedString);
        }
        $this->displayFieldFormatter($fieldFormatterTask);
    }

    public function setOperateButtonAccess(array $button): void
    {
        $this->showOperateButtonAccess = $button;
        $this->displayOperateButtonAccess(true);
    }

    public function setApprovalProductList($approvalProductList)
    {
        $this->approvalProductList = $approvalProductList;
    }

    //todo 待实现
    public function setShowTransferInfo($showTransferInfo, $showTransferType, $ignorePurchaseCountDone = false)
    {
        $this->showTransferInfo = $showTransferInfo;
        $this->showTransferType = $showTransferType;
        $this->ignorePurchaseCountDone = $ignorePurchaseCountDone;
        $this->displayTransferInfo(true);
    }

    public function profitFormulaComputationSetting(){
        $this->displayFields($this->metadata->getMappedAttributeKeys());
        $fieldFormatterTask = new FieldV2FormatTask($this->clientId);
        $fieldFormatterTask->setObjName(Constant::OBJ_ORDER);
        $fieldFormatterTask->setDisplayFields([]);
        $this->displayFieldFormatter($fieldFormatterTask);
    }

    /**
     * 临时方法，open还没重构但是统计字段等无法返回
     * @param $groups
     * @return void
     */
    public function  openInfoSetting()
    {
        $this->displayFields($this->metadata->getMappedAttributeKeys());
        $this->functionFieldInfoSetting();
        $productListTask = new OrderProductListTask($this->clientId);
        $this->displayProductList($productListTask);
    }

    public function  webInfoSetting($groups = [])
    {
        $this->displayFields($this->metadata->getMappedAttributeKeys());
        $this->functionFieldInfoSetting();

        if (in_array(\common\library\custom_field\CustomFieldService::ORDER_GROUP_PRODUCT, $groups)) {
            $productListTask = new OrderProductListTask($this->clientId);
            $productListTask->setSetting([
                'showShippingRecordInfo' => true,
                'displayAssocPlatformProduct' => true,
                'displayProductInfo' => true,
                'displayCombineProduct' => true,
                'displayProductImageInfoList' => $this->productImageFormatter,
                'displayRecycleProduct' => $this->scene == OrderConstants::ORDER_INFO_SCENE_RECYCLE,
            ]);
            $this->displayProductList($productListTask);
            $this->displayReferInvoiceProductCount(true);
        }

        $this->displayReferLockInfo(true);
        $this->displayApprovalFlowInfo(true);
        $this->showApprovalFlowEventInfo = true;
        $this->displayPin(true);

        $this->displayStatusInfo(true);
        $this->showAllowExport = true;

        $this->displayUpdateUserInfo(true);
        $this->displayUsersInfo(true);
        $this->displayDepartmentsInfo(true);
        $this->displayCompanyInfo(true);
        $this->displayOpportunityInfo(true);
        $this->displayLeadInfo(true);
        $this->displayCustomerInfo(true);
        $this->displayAlibabaStatusInfo(true);
        $this->displayAlibabaOrderInfo(true);
        $this->displayCapitalAccountInfo(true);
        $this->displayLinkInfo(true);
        $this->displayCashCollectionStatus(true);
        $this->displayShippingCostList(true);
        $this->displayCostList(true);   //setShowAmountInfo
        $this->displayStatusAction(true);
        $this->displayQuotationInfo(true);
        $this->displayAlibabaAccountInfo(true);

        $this->displayCanChangeCurrency(true);
    }

    public function orderPushDownSetting($groups = [])
    {
        $this->displayFields([
            'order_id',
            'order_no',
            'update_user',
            'name',
            'currency',
            'amount_rmb',
            'amount_usd',
            'exchange_rate',
            'exchange_rate_usd',
            'company_id',
            'account_date',
            'create_time',
            'update_time',
            'archive_type',
            'amount',
            'amount_rmb',
            'amount_usd',
            'users',
            'source_type',
            'status_action',
            'ali_status_name',
            'fulfillment_channel',
            'product_total_amount_rmb',
            'product_total_amount_usd',
            'user_id',
            'handler',
            'has_partial_purchase_order',
        ]);
        $this->functionFieldInfoSetting();

        if (in_array(\common\library\custom_field\CustomFieldService::ORDER_GROUP_PRODUCT, $groups)) {
            $productListTask = new OrderProductListTask($this->clientId);
            $productListTask->setSetting([
                'showShippingRecordInfo' => true,
                'displayExtendField' => true,
                'displayProductInfo' => true,
                'pushDownShowSubProduct' => true,
                'displayToBePurchaseCount' => true,
                'displayCombineProduct' => true,
                'displayProductImageInfoList' => $this->productImageFormatter,
                'displayProductToPurchaseByOrderProductInfo' => $this->scene == OrderConstants::ORDER_INFO_SCENE_PUSH_DOWN_ORDER_SPLIT_PURCHASE,
            ]);
            $this->displayProductList($productListTask);
        }

        $this->displayCompanyInfo(true);
        $this->displayApprovalFlowInfo(true);

    }

    public function orderApprovalSetting($groups = [])
    {
        $this->displayFields($this->metadata->getMappedAttributeKeys());

        $this->functionFieldInfoSetting();

        if (in_array(\common\library\custom_field\CustomFieldService::ORDER_GROUP_PRODUCT, $groups)) {
            $this->displayApprovalProductList(true);
        }
        $this->displayUsersInfo(true);
        $this->displayUpdateUserInfo(true);
        $this->displayDepartmentsInfo(true);
        $this->displayCompanyInfo(true);
        $this->displayOpportunityInfo(true);
        $this->displayLeadInfo(true);
        $this->displayCustomerInfo(true);
        $this->displayCapitalAccountInfo(true);
        $this->displayLinkInfo(true);
        $this->displayCostList(true);
        $this->displayStatusInfo(true);
        $this->displayAlibabaStatusInfo(true);
        $this->displayAlibabaOrderInfo(true);
        $this->displayStatusAction(true);
        $this->displaySystemInfo(true);
        $this->displayOperatePrivilege(true);
    }

    public function selectListInfoSetting()
    {
        $this->displayFields(
            [
                'name',
                'currency',
                'create_time',
                'update_time',
                'enable_flag',
                'status',
                'company_id',
                'order_id',
            ]
        );
        $this->functionFieldInfoSetting();
        $this->displayStatusInfo(true);
        $this->displayCompanyInfo(true);
        $this->displayExportFileCount(true);
    }


    public function opportunityDetailListInfoSetting()
    {
        $this->displayFields([
            'order_id', 'status', 'name', 'currency', 'amount', 'amount_rmb', 'amount_usd', 'create_time', 'update_time', 'enable_flag', 'opportunity_id'
        ]);
        $this->functionFieldInfoSetting();
        $this->displayStatusInfo(true);
        $this->displayApprovalFlowInfo(true);
    }

    public function aliOrderListInfoSetting()
    {
        $this->displayFields($this->metadata->getMappedAttributeKeys());
        $this->functionFieldInfoSetting();
        $this->displayUsersInfo(true);
        $this->displayDepartmentsInfo(true);
        $this->displayStatusInfo(true);
        $this->displayCompanyInfo(true);
        $this->displayPin(true);
        $this->displayOpportunityInfo(true);
//        $this->displayAllowExport(true);
        $this->displayCashCollectionStatus(true);
        $this->displayUpdateUserInfo(true);
        $this->displayCustomerInfo(true);
        $this->displayLeadInfo(true);
        $this->displayAlibabaAccountInfo(true);
        $this->displayStoreInfo(true);
        $this->displayAlibabaStatusInfo(true);
        $this->displayStatusAction(true);
        $this->displayQuotationInfo(true);
        $this->displayAlibabaOrderInfo(true);
        $this->showLink = [
            OrderLinkConstant::ORDER_LINK_CASH_COLLECTION,
            OrderLinkConstant::ORDER_LINK_STOCK_UP,
            OrderLinkConstant::ORDER_LINK_OUTBOUND,
            OrderLinkConstant::ORDER_LINK_END
        ];
        $this->displayLinkStatus(true);
        $this->setFormatterV2Privilege(true, true, PrivilegeFieldV2::SCENE_OF_VIEW);
    }

    public function orderProfitListInfoSetting($fields)
    {
        $this->displayFields($fields);
        $this->functionFieldInfoSetting();
        $this->displayDepartmentsInfo(true);
        $this->displayUsersInfo(true);
        $this->displayStatusInfo(true);
        $this->displayStoreInfo(true);
        $this->displayCustomerInfo(true);
        $this->displayCompanyInfo(true);
        $this->displayCostInvoiceInfo(true);
        $this->displayCostList(true);
        $this->displayCostItems(true);
    }

    public function showWebListAccess()
    {
        $this->setOperateButtonAccess([
            'change_status',
            'batch_change_status',
            'member_manage',
            'batch_member_manage',
            'unlock',
            'edit',
            'copy',
            'delete',
            'create_schedule',
            'export',
            'associate_business',
            'create_cash_collection',
            'create_cash_collection_invoice',
            'create_logistics',
            'generate_purchase_order',
            'assurance',
            'eProceed',
            'generate_order_outbound',
            'create_outbound_product_transfer',
            'create_stock_up_invoice',
            'create_transfer_invoice',
            'confirm_stock_up_done',
            'confirm_shipping_done',
            'confirm_end_done',
            'cancel_end_done',
            'generate_cost_invoice',
            'generate_shipping_invoice',
            'edit_attachment',
            'transfer_ali_order'
        ]);
    }

    public function exportInfoSetting($showProduct)
    {
        $this->displayFields($this->metadata->getMappedAttributeKeys());
        $this->functionFieldInfoSetting(['valueNeedString' => true]);
        if ($showProduct) {
            $productListTask = new OrderProductListTask($this->clientId);
            $productListTask->setSetting([
                //'showShippingRecordInfo' => true,
                'displayAssocPlatformProduct' => true,
                'displayProductInfo' => true,
                'displayCombineProduct' => true,
                'displayProductImageInfoList' => $this->productImageFormatter,
            ]);
            $this->displayProductList($productListTask);
        }

        $this->displayUsersInfo(true);
        $this->displayDepartmentsInfo(true);
        $this->displayStatusInfo(true);
        $this->displayCompanyInfo(true);
        $this->displayOpportunityInfo(true);
        $this->displayCashCollectionStatus(true);
        $this->displayUpdateUserInfo(true);
        $this->displayCustomerInfo(true);
        $this->displayStoreInfo(true);
        $this->displayAlibabaStatusInfo(true);
        $this->displayCapitalAccountInfo(true);
        $this->displayAlibabaOrderInfo(true);
        $this->displayCostList(true);
        $this->displayQuotationInfo(true);
        $this->setFormatterV2Privilege(false, true, PrivilegeFieldV2::SCENE_OF_EXPORT);
        $this->showLink = [
            OrderLinkConstant::ORDER_LINK_CASH_COLLECTION,
            OrderLinkConstant::ORDER_LINK_STOCK_UP,
            OrderLinkConstant::ORDER_LINK_OUTBOUND,
            OrderLinkConstant::ORDER_LINK_END
        ];
        $this->displayLinkStatus(true);
    }

    public function listInfoSetting()
    {
        $this->displayFields($this->metadata->getMappedAttributeKeys());
        $this->functionFieldInfoSetting();
        $this->displayUsersInfo(true);
        $this->displayDepartmentsInfo(true);
        $this->displayStatusInfo(true);
        $this->displayCompanyInfo(true);
        $this->displayPin(true);
        $this->displayOpportunityInfo(true);
        $this->displayQuotationInfo(true);
//        $this->displayAllowExport(true);
        $this->displayCashCollectionStatus(true);
//        $this->displayTransport(true);
        $this->displayUpdateUserInfo(true);
        $this->displayCustomerInfo(true);
        $this->displayAlibabaAccountInfo(true);
        $this->displayStoreInfo(true);
        $this->displayAlibabaStatusInfo(true);
        $this->displayStatusAction(true);
        $this->displayCapitalAccountInfo(true);
//        $this->displayHasReferDownStreamInvoice(true);
        $this->displayAlibabaOrderInfo(true);
        $this->displayShippingCostList(true);
        $this->displayCostList(true);
        $this->showLink = [
            OrderLinkConstant::ORDER_LINK_CASH_COLLECTION,
            OrderLinkConstant::ORDER_LINK_STOCK_UP,
            OrderLinkConstant::ORDER_LINK_OUTBOUND,
            OrderLinkConstant::ORDER_LINK_END
        ];
        $this->displayLinkStatus(true);
        $this->setFormatterV2Privilege(true, true, PrivilegeFieldV2::SCENE_OF_VIEW);
    }

    public function listStatistics()
    {
        $this->displayFields([
            'amount_rmb',
            'amount_usd',
            'product_total_amount_rmb',
            'product_total_amount_usd',
            'addition_cost_amount_rmb',
            'addition_cost_amount_usd',
            'collect_amount_rmb',
            'collect_amount_usd',
            'not_collect_amount_rmb',
            'not_collect_amount_usd',
        ]);
        $this->fieldSetting();
        $this->displayStatistics(true);
    }

    public function OrderProfitListStatistics()
    {
        $this->displayFields([
            'amount_rmb',
            'amount_usd',
            'product_total_amount_rmb',
            'product_total_amount_usd',
            'addition_cost_amount_rmb',
            'addition_cost_amount_usd',
            'order_cost_with_tax_total_rmb_sum',
            'order_cost_with_tax_total_usd_sum',
            'real_profit_rmb_sum',
            'real_profit_usd_sum',
            'predict_profit_rmb_sum',
            'cash_collection_collect_amount_rmb_sum',
            'cash_collection_collect_amount_usd_sum',
            'cash_collection_not_collect_amount_rmb_sum',
            'cash_collection_not_collect_amount_usd_sum',
            'cash_collection_real_amount_rmb_sum',
            'cash_collection_real_amount_usd_sum',
            'purchase_order_product_amount_rmb_sum',
            'purchase_order_amount_rmb_sum',
            'purchase_order_product_payment_amount_rmb_sum',
            'outbound_product_completed_inventory_amount_rmb_sum',
            'outbound_product_inventory_amount_rmb_sum',
            'cost_invoice_rmb_sum',
            'payment_cost_invoice_rmb_sum',
            'not_payment_cost_invoice_rmb_sum',
            'tax_refund_rmb_sum',
            'purchase_order_product_not_payment_amount_rmb_sum',
            'defined_1_rmb_sum',
            'defined_2_rmb_sum',
            'defined_3_rmb_sum',
            'purchase_order_addition_cost_amount_rmb_sum',
            'exchange_loss_amount_rmb_sum',
            'exchange_loss_amount_usd_sum',
        ]);
        $this->fieldSetting();
        $this->displayStatistics(true);
    }


    public function mapStatistics(&$result)
    {
        foreach ($result as $field => &$value) {
            is_null($value) && $value = 0;
            $value = $this->specialFieldFormat($field, $value);
        }
    }

    public function specialFieldFormat($field, $value)
    {
        if (!isset($this->fieldSetting[$field])) {
            return $value;
        }
        $formatter = FormatterFactory::MakeForSpecialField($this->clientId, $this->fieldSetting[$field]);

        if (!($formatter instanceof \common\library\object\field\formatter\FieldValueFormatter)) {
            return $value;
        }

        if ($formatter->filter($value)) {
            return $value;
        }

        return $formatter->format($value);
    }

    public function buildApprovalFlowInfo($key, array $data)
    {
        $approvalFlowInfoMap = \common\library\approval_flow\Helper::getApprovalProgressInfo(\Constants::TYPE_ORDER, array_column($data, 'order_id'), $this->showApprovalFlowEventInfo);

        return $approvalFlowInfoMap;
    }

    public function mapApprovalFlowInfo(&$result, $key, $data)
    {
        $map = $this->getPipelinePrepareData($key);
        $result['approval_flow_info'] = $map[$data['order_id']] ?? new \stdClass();

        $approvalFlowInfo = is_a($result['approval_flow_info'], \stdClass::class) ? [] : $result['approval_flow_info'];
        $result['last_approval_info'] = \common\library\approval_flow\Helper::lastApproval($data['order_id'], $approvalFlowInfo);

        // app approval_flow_info 中需要 approval_type
        if (!empty($result['last_approval_info']) && !empty($approvalFlowInfo)) {
            $result['approval_flow_info']['approval_type'] = $result['last_approval_info']['approval_type'] ?? 0;
        }
    }

    public function buildReferLockInfo($key, array $data)
    {
        $approvalLockList = \common\library\approval_flow\Helper::getReferLockList($this->getViewUserId(), \Constants::TYPE_ORDER, array_column($data, 'order_id'), ['refer_id', 'lock_flag']);
        return array_column($approvalLockList, 'lock_flag', 'refer_id');
    }

    public function mapReferLockInfo(&$result, $key)
    {
        $map = $this->getPipelinePrepareData($key);
        $result['lock_flag'] = $map[$result['order_id']] ?? 0;
    }


    public function buildApprovalProductList($key, array $data)
    {
        $approvalProductList = $this->approvalProductList;
        $map = [];
//        foreach ($data as $line) {
//            //由于之前 \common\library\oms\order\Order::refreshProductList 在排除宽表字段的时候过滤了id、client_id、create_time、update_time
//            foreach ($line['product_list'] as $product) {
//                $product['id'] = $product['unique_id'];
//                $product['client_id'] = $this->clientId;
//                $product['create_time'] = $product['create_time'] ?? null;
//                $product['update_time'] = $product['update_time'] ?? null;
//                $productList[] = $product;
//            }
//        }
        $columnFields = (new OrderProductMetadata)->getMappedAttributeKeys();
        $columnFields = array_merge($columnFields, ['unique_id']);

        if (!empty($approvalProductList)) {
            foreach ($approvalProductList as &$product) {
                //补全字段以防formatter拿不到field value
                foreach ($columnFields as $columnField) {
                    if (!isset($product[$columnField])) {
                        $product[$columnField] = ($columnField == 'external_field_data') ? [] : null;
                    }
                }
            }
            unset($product);

            $batchOrderProduct = new BatchOrderProduct($this->clientId);
            $batchOrderProduct->initFromData($approvalProductList);
            $batchOrderProduct->getFormatter()->functionFieldInfoSetting();
            $batchOrderProduct->getFormatter()->displayFields($columnFields);
            $batchOrderProduct->getFormatter()->taskListSetting();
            $batchOrderProduct->getFormatter()->displaySkuInfo(true);
            $batchOrderProduct->getFormatter()->displayProductInfo(true);
            $batchOrderProduct->getFormatter()->displayPlatformProductInfo(true);
            $productList = $batchOrderProduct->getFormatter()->result($approvalProductList);
            foreach ($productList as $product) {
                $orderId = $product['order_id'] ?? $product['refer_id'];
                $map[$orderId][] = $product;
            }
        }
        return $map;
    }

    public function mapApprovalProductList(&$result, $key)
    {
        $map = $this->getPipelinePrepareData($key);
        $productList = $map[$result['order_id']] ?? [];
        $result['assoc_platform_product'] = false;
        foreach ($productList as &$product) {
            $product['refer_order_no'] = $result['order_no'];

            if (!empty($product['platform_product_id'])) {
                $result['assoc_platform_product'] = true;
            }

            if (!empty($product['is_master_product'] ?? 0) && empty($product['master_id'] ?? 0)) {
                $product['master_group_id'] = $product['unique_id'];
            }
            //配件产品 赋值master_group_id
            if (empty($product['is_master_product'] ?? 0) && !empty($product['master_id'] ?? 0)) {
                $product['master_group_id'] = $product['master_id'];
            }

        }

        \ArrayUtil::multisort($productList, 'sort');
        $result['product_list'] = $productList;
    }

    public function buildPinMap($key, array $data)
    {
        $orderIds = array_unique(array_column($data, 'order_id'));
        $pinList = \Pin::getPinReferIds($this->getViewUserId(), \Pin::TYPE_ORDER, $orderIds);
        return empty($pinList) ? [] : array_fill_keys($pinList, 1);
    }

    public function mapPin(&$result, $key, $orderId)
    {
        $map = $this->getPipelinePrepareData($key);
        $result[$key] = $map[$orderId] ?? null;
    }

    public function buildQuotationMap($key, array $data)
    {
        $quotation_ids = Arr::uniqueFilterValues(array_column($data, 'quotation_id'));
        $quotationInfo = [];
        if (!empty($quotation_ids)) {
            $quotationFilter = new QuotationFilter($this->clientId);
            $quotationFilter->select(['quotation_no','quotation_id','name']);
            $quotationFilter->quotation_id = $quotation_ids;
            $quotationInfo = $quotationFilter->rawData();
        }
        return array_column($quotationInfo, null, 'quotation_id');
    }

    public function mapQuotation(&$result, $key, $orderId)
    {
        $map = $this->getPipelinePrepareData($key);
        $result['quotation_info'] = $map[$result['quotation_id'] ?? -1] ?? [];
        $result['refer_quotation_no'] = $result['quotation_info']['quotation_no'] ?? "";
    }

    public function buildStatusInfoMap($key, array $data)
    {
        $statusIds = array_unique(array_column($data, 'status'));
        $statusList = (new InvoiceStatusService($this->clientId, \Constants::TYPE_ORDER))->baseInfoList(false, $statusIds, false);

        if ($this->showAllowExport) {
            $client = Client::getClient($this->clientId);
            $values = $client->getSettingAttributes([Client::SETTING_KEY_ORDER_ALLOW_EXPORT_DRAFT]);
            $this->allowExportDraft = $values[Client::SETTING_KEY_ORDER_ALLOW_EXPORT_DRAFT] ?? 1;
        }

        return array_combine(array_column($statusList, 'id'), $statusList);

    }

    public function mapStatusInfo(&$result, $key)
    {
        $map = $this->getPipelinePrepareData($key);
        $result['status_info'] = $map[$result['status']] ?? null;
    }

    public function resultDecorator(array &$result)
    {
        parent::resultDecorator($result);

        //字段权限

    }

    public function buildUsersMap($key, array $data)
    {
        $userIds = [];
        foreach ($data as $line) {
            $userIds = array_merge($userIds, array_column($line['users'], 'user_id'));
        }
        if (!empty($userIds)) {
            $userList = \common\library\account\Helper::getBatchUserInfo($this->clientId, $userIds);
            $userList = array_map(function ($elem) {
                return ['user_id' => $elem->user_id, 'nickname' => $elem->nickname, 'avatar' => $elem->avatar];
            }, $userList);
            $userMap = array_combine(array_column($userList, 'user_id'), $userList);
        }
        return $userMap ?? [];
    }

    public function mapUsers(&$result, $key)
    {
        $map = $this->getPipelinePrepareData($key);
        if (empty($result['users'])) {
            $result['users_info'] = [];
        } else {
            $result['users_info'] = array_map(function ($elem) use ($map) {
                return ($map[$elem['user_id'] ?? ''] ?? []) + $elem;
            }, $result['users']);
        }
    }

    public function buildUpdateUserInfoMap($key, array $data)
    {
        $userIds = array_column($data, 'update_user');
        if (!empty($userIds)) {
            $userList = \common\library\account\Helper::getBatchUserInfo($this->clientId, $userIds);
            $userList = array_map(function ($elem) {
                return ['user_id' => $elem->user_id, 'nickname' => $elem->nickname, 'avatar' => $elem->avatar];
            }, $userList);
            $userMap = array_combine(array_column($userList, 'user_id'), $userList);
        }
        return $userMap ?? [];
    }

    public function mapUpdateUserInfo(&$result, $key)
    {
        $map = $this->getPipelinePrepareData($key);
        $result['update_user_info'] = $map[$result['update_user']] ?? [];
    }

    public function buildDepartmentsMap($key, array $data)
    {
        $departmentIds = [];
        foreach ($data as $line) {
            $departmentIds = array_merge($departmentIds, array_column($line['departments'] ?? [], 'department_id'));
        }

        $departmentService = new DepartmentService($this->clientId);
        $departmentList = $departmentService->batchGetDepartmentListForIds($departmentIds);

        return array_column($departmentList, null, 'id');
    }

    public function mapDepartments(&$result, $key)
    {
        $map = $this->getPipelinePrepareData($key);
        $result['departments_info'] = [];
        if (empty($result['departments'])) {
            $result['departments_info'] = [];
        } else {
            foreach ($result['departments'] as $k => $department) {
                $departmentInfo = $map[$department['department_id'] ?? ''] ?? [];
                if (empty($departmentInfo)) {
                    unset($result['departments'][$k]);
                } else {
                    $result['departments_info'][] = $departmentInfo + $department;
                }
            }
        }

    }

    public function buildCompanyInfoMap($key, array $data)
    {
        $companyIds = array_filter(array_column($data, 'company_id'));
        if (!empty($companyIds)) {
            $list = new CompanyList($this->getViewUserId());
            $list->setCompanyIds($companyIds);
            $list->setSkipPrivilege(true);
            $list->setIsArchive(null);
            $list->setFields(['company_id', 'name', 'is_archive', 'serial_id', 'fax', 'address']);
            $companyList = $list->find();
            foreach ($companyList as $elem) {
                $companyMap[$elem['company_id']] = $elem;
            }
        }

        return $companyMap ?? [];
    }

    public function mapCompanyInfo(&$result, $key)
    {
        $map = $this->getPipelinePrepareData($key);
        $companyInfo = $map[$result['company_id']] ?? [];
        if (!empty($companyInfo)) {
            $result['company'] = [
                'company_id' => $companyInfo['company_id'],
                'name' => $companyInfo['name'],
                'is_archive' => $companyInfo['is_archive'],
                'serial_id' => $companyInfo['serial_id']
            ];
            // 要展示订单的客户信息
//            $result['company_name'] = $companyInfo['name'];
//            $result['company_fax'] = $companyInfo['fax'];
//            $result['company_address'] = $companyInfo['address'];
        }
    }

    public function buildOpportunityInfoMap($key, array $data)
    {
        $opportunityIds = array_filter(array_column($data, 'opportunity_id'));
        if (!empty($opportunityIds)) {
            $list = new OpportunityList($this->clientId);
            $list->setViewingUserId($this->getViewUserId());
            $list->setOpportunityIds($opportunityIds);
            $list->setSkipPermissionCheck(true);
            $list->setIgnoreEnableFlag();
            $list->setFields(['opportunity_id', 'name', 'enable_flag']);
            $opportunityList = $list->find();
            foreach ($opportunityList as $elem) {
                $opportunityMap[$elem['opportunity_id']] = $elem;
            }
        }

        return $opportunityMap ?? [];
    }

    public function mapOpportunityInfo(&$result, $key)
    {
        $map = $this->getPipelinePrepareData($key);
        if (!empty($result['opportunity_id'])) {
            $result['opportunity'] = $map[$result['opportunity_id']] ?? [];
        }
    }

    public function buildLeadInfoMap($key, array $data)
    {
        $leadIds = array_filter(array_column($data, 'source_lead_id'));
        if (!empty($leadIds)) {
            $leadList = new LeadList($this->getViewUserId());
            $leadList->setLeadId($leadIds);
            $leadList->setSkipPrivilege(true);
            $leadList->setShowAllStatusFlag(true);
            $leadList->setFields(['lead_id', 'name']);
            $leadList->setIsArchive(null);
            $leadInfoMap = array_column($leadList->find(), null, 'lead_id');
        }

        return $leadInfoMap ?? [];
    }

    public function mapLeadInfo(&$result, $key)
    {
        $map = $this->getPipelinePrepareData($key);
        if (!empty($result['source_lead_id'])) {
            $result['lead_info'] = $map[$result['source_lead_id']] ?? [];
        }
    }

    public function buildCustomerInfoMap($key, array $data)
    {
        $customerIds = array_filter(array_column($data, 'customer_id'));
        if (!empty($customerIds)) {
            $list = new CustomerList($this->clientId);
            $list->setCustomerId($customerIds);
            $list->setFields(['customer_id', 'name', 'email', 'is_archive', 'tel_list']);
            $customerList = $list->find();

            foreach ($customerList as $elem) {

                if (empty($elem['name'])) $elem['name'] = $elem['email'];
                $elem['tel_list'] = json_decode($elem['tel_list'], true);
                $customerMap[$elem['customer_id']] = $elem;
            }
        }


        return $customerMap ?? [];
    }

    public function mapCustomerInfo(&$result, $key)
    {
        $map = $this->getPipelinePrepareData($key);
        if (!empty($result['customer_id'])) {
            $result['customer'] = $map[$result['customer_id']] ?? [];
        }
    }

    public function mapAlibabaStatusInfo(&$result, $key)
    {
        if ($result['source_type'] == OrderConstants::TYPE_ALI_ORDER || $result['source_type'] == OrderConstants::TYPE_DIRECT_PAY_ORDER) {
            $result['ali_status_info'] = [
                'ali_status_id' => $result['ali_status_id'],
                'ali_status_name' => $result['ali_status_name'],
                'status_name' => \Yii::t('invoice', $result['ali_status_name'])
            ];
        }
    }

    public function buildAlibabaOrderInfo($key, array $data)
    {
        $alibabaOrderMap =[];
        $aliOrderIds = array_filter(array_column($data, 'ali_order_id'));
        if (!empty($aliOrderIds)) {
            $alibabaOrderRelationListObj = new AlibabaOrderRelationList($this->clientId);
            $alibabaOrderRelationListObj->setAlibabaTradeId($aliOrderIds);
            $alibabaOrderRelationListObj->setOrderBy(['update_time']);
            $alibabaOrderRelationListObj->getFormatter()->listInfoSetting();
            $alibabaOrderRelationList = $alibabaOrderRelationListObj->find();
            $alibabaOrderMap = array_combine(array_column($alibabaOrderRelationList, 'alibaba_trade_id'), $alibabaOrderRelationList);
        }

        $orderIds = array_filter(array_column($data, 'order_id'));
        $mainAlibabaOrderMap = [];
        $subAlibabaOrderMap = [];
        if (!empty($orderIds)) {
            $alibabaOrderRelationListObj = new AlibabaOrderRelationList($this->clientId);
            $alibabaOrderRelationListObj->setAlibabaMasterOrderId($orderIds);
            $alibabaOrderRelationListObj->setOrderBy(['update_time']);
            $alibabaOrderRelationListObj->setCreateType([
                OmsConstant::ALIBABA_CREATE_TYPE_TRANSFER_ALI_ORDER,
                OmsConstant::ALIBABA_CREATE_TYPE_TRANSFER_SUB_ALI_ORDER,
            ]);
            $alibabaOrderRelationListObj->getFormatter()->listInfoSetting();
            $alibabaOrderRelationList = $alibabaOrderRelationListObj->find();

            foreach ($alibabaOrderRelationList as $alibabaOrderRelation) {
                $order_id = $alibabaOrderRelation['ali_master_order_id'] ?? 0;
                if (empty($order_id)) {
                    continue;
                }

                if ($alibabaOrderRelation['create_type'] == OmsConstant::ALIBABA_CREATE_TYPE_TRANSFER_ALI_ORDER){
                    $mainAlibabaOrderMap[$order_id] = $alibabaOrderRelation;
                    continue;
                }

                if (!empty($subAlibabaOrderMap[$order_id])) {
                    $subAlibabaOrderMap[$order_id][] = $alibabaOrderRelation;
                } else {
                    $subAlibabaOrderMap[$order_id] = [$alibabaOrderRelation];
                }
            }
        }

        return [
            'sub_transfer_order_info_map' => $subAlibabaOrderMap ?? [],
            'transfer_order_info_map' => $mainAlibabaOrderMap ?? [],
            'alibaba_order_info_map' => $alibabaOrderMap ?? []
        ] ?? [];
    }

    public function mapAlibabaOrderInfo(&$result, $key)
    {
        $sub_transfer_order_info = $this->getPipelinePrepareData($key)['sub_transfer_order_info_map'][$result['order_id']] ?? [];
        $transfer_order_info = $this->getPipelinePrepareData($key)['transfer_order_info_map'][$result['order_id']] ?? [];
        $alibaba_order_info = $this->getPipelinePrepareData($key)['alibaba_order_info_map'][$result['ali_order_id']] ?? [];

        if (!empty($result['ali_order_id'])) {
            $result['alibaba_order_info'] = $alibaba_order_info ?? [];
        }

        if (!empty($result['transfer_ali_order_type'])) {
            $result['transfer_ali_order_info'] = $result['alibaba_order_info'] = $transfer_order_info ?? [];
            $result['sub_transfer_order_info'] = $sub_transfer_order_info ?? [];
            $result['transfer_ali_order_count'] = count($result['sub_transfer_order_info']);
            $result['transfer_ali_order_id'] = Arr::uniqueFilterValues(array_column($result['sub_transfer_order_info'], 'alibaba_trade_id'));
            foreach ($result['transfer_ali_order_id'] as $transfer_ali_order_id) {
                $result['ali_status_info_list'][$transfer_ali_order_id] = \common\library\alibaba\order\AlibabaOrderSyncHelper::formatStatusAction($transfer_ali_order_id, $result['status_action']);
            }
        }
    }


    public function buildStoreInfo($key, array $data)
    {
        $storeIds = array_unique(array_filter(array_column($data, 'ali_store_id')));
        $storeMap = AlibabaStoreService::getStoreInfoMaps($this->clientId, $storeIds);

        return $storeMap ?? [];
    }

    public function mapStoreInfo(&$result, $key)
    {
        $map = $this->getPipelinePrepareData($key);
        $result['store_info'] = $map[$result['ali_store_id']] ?? [];

    }

    public function buildCapitalAccountInfo($key, array $data)
    {
        $capitalAccountIds = array_unique(array_filter(array_column($data, 'capital_account_id')));

        if (!empty($capitalAccountIds)) {
            $capitalAccountFilter = new CapitalAccountFilter($this->clientId);
            $capitalAccountFilter->capital_account_id = $capitalAccountIds;
            $capitalAccountFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
            $capitalAccountFilter->select(['capital_account_id', 'name', 'capital_name', 'capital_bank', 'bank_account', 'address', 'remark']);
            $capitalAccountList = $capitalAccountFilter->rawData();
            $capitalAccountMap = array_column($capitalAccountList, null, 'capital_account_id');
        }

        return $capitalAccountMap ?? [];
    }

    public function mapCapitalAccountInfo(&$result, $key)
    {
        $map = $this->getPipelinePrepareData($key);
        $result['capital_account_info'] = $map[$result['capital_account_id']] ?? [];

        //这里是因为前端取的是外层而非data的数据
        $result['capital_account_name'] = $result['capital_account_info']['name'] ?? '';
        $result['capital_name'] = $result['capital_account_info']['capital_name'] ?? '';
        $result['capital_bank'] = $result['capital_account_info']['capital_bank'] ?? '';
        $result['bank_account'] = $result['capital_account_info']['bank_account'] ?? '';
        $result['capital_account_address'] = $result['capital_account_info']['address'] ?? '';
        $result['capital_account_remark'] = $result['capital_account_info']['remark'] ?? '';
    }

    public function buildLinkInfo($key, array $data)
    {
        $orderIds = array_column($data, 'order_id');
        if (!empty($orderIds)) {
            $purchaseOrderProductFilter = new PurchaseOrderProductFilter($this->clientId);
            $purchaseOrderProductFilter->order_id = new In($orderIds);
            $purchaseOrderProductFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
            $purchaseOrderProductFilter->select(['order_id']);

            $purchaseOrderFilter = new PurchaseOrderFilter($this->clientId);
            $purchaseOrderFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
            $purchaseOrderFilter->select(['delivery_date' => function ($column, $table, $quote) {
                return "max($table.$column) as  max_delivery_date";
            }]);

            $purchaseOrderProductFilter->initJoin()
                ->leftJoin($purchaseOrderFilter)
                ->on('purchase_order_id', 'purchase_order_id')
                ->joinGroupBy('order_id', $purchaseOrderProductFilter->getTableName());


            $purchaseOrderData = $purchaseOrderProductFilter->rawData(false);

            $maxDeliveryDateMap = array_column($purchaseOrderData, 'max_delivery_date', 'order_id');
        }
        return $maxDeliveryDateMap ?? [];
    }

    public function mapLinkInfo(&$result, $key)
    {
        $map = $this->getPipelinePrepareData($key);
        $orderLink = OrderLink::make($result['link_status']);
        $data = [];
        foreach ($orderLink->getLink() as $link) {
            $temp = [
                'link' => $link->constant(),
                'title' => $link->currentStatus()->define(),
                'link_status' => $link->currentStatus()->constant(),
                'progress' => $link->currentStatus()->progress()->toArray()
            ];

            //备货中查询最大交货时间
            if ($link->currentStatus()->constant() == OrderLinkConstant::ORDER_LINK_STOCK_UP_PENDING) {

                $maxDeliveryDate = $map[$result['order_id']] ?? '';
                if (!empty($maxDeliveryDate) && $maxDeliveryDate != '1970-01-01 08:00:00') {
                    $temp['title'] = $temp['title'] . '(' . date('Y-m-d', strtotime($maxDeliveryDate)) . ')';

                }
            }

            $data[] = $temp;
        }
        $result['link_info'] = $data;
    }

    public function mapLinkStatus(&$result, $key)
    {
        //订单待办分类，旧代码暂未设置待办类型
        $linkListType = null;
        $tagConfig = [
            OrderLinkConstant::CASH_COLLECTION_AWAIT_LIST => OrderLinkConstant::ORDER_LINK_CASH_COLLECTION,
            OrderLinkConstant::STOCK_UP_AWAIT_LIST => OrderLinkConstant::ORDER_LINK_STOCK_UP,
            OrderLinkConstant::SHIPPING_AWAIT_LIST => OrderLinkConstant::ORDER_LINK_OUTBOUND,
            OrderLinkConstant::END_AWAIT_LIST => OrderLinkConstant::ORDER_LINK_END,
            OrderLinkConstant::END_DONE_LIST => OrderLinkConstant::ORDER_LINK_END,
        ];
        $orderLink = OrderLink::make($result['link_status']);
        $data = [];

        $needTagLink = $linkListType ?? ($tagConfig[$linkListType] ?? null);
        foreach ($orderLink->getLink() as $link) {
            if (!empty($this->showLink) && !in_array($link->constant(), $this->showLink)) {
                continue;
            }
            $data[$link->constant()] = [
                'link' => $link->constant(),
                'title' => $link->currentStatus()->define(),
                'link_status' => $link->currentStatus()->constant(),
                'progress' => $link->currentStatus()->progress()->toArray(),
                'type' => $link->constant() == $needTagLink ? 'tag' : 'text'
            ];
        }

        $fieldMap = [
            OrderLinkConstant::ORDER_LINK_CASH_COLLECTION => 'cash_collection_status',
            OrderLinkConstant::ORDER_LINK_END => 'end_status',
            OrderLinkConstant::ORDER_LINK_STOCK_UP => 'stock_up_status',
            OrderLinkConstant::ORDER_LINK_OUTBOUND => 'shipping_status',
        ];
        foreach ($data as $linkConstant => $datum) {
            $result[$fieldMap[$linkConstant]] = $datum;
        }
    }

    public function buildShippingCostList($key, array $data)
    {
        $orderIds = array_column($data, 'order_id');
        $spInvoiceFilter = new ShippingInvoiceFilter($this->clientId);
        $spInvoiceFilter->order_ids = new InArray($orderIds);
        $spInvoiceFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $batchSpInvoice = $spInvoiceFilter->find();
        $batchSpInvoice->getFormatter()->displayFields(['cost_list']);
        $shippingInvoiceList = $batchSpInvoice->getAttributes();
        $shippingCostMap = array_reduce(
            array_column($shippingInvoiceList, 'cost_list'),
            function ($result, $costItems) {
                foreach ($costItems as $item) {
                    if (empty($item['order_id'])) {
                        continue;
                    }
                    //新旧数据
                    $key = isset($item['cost_item_relation_id']) && !empty($item['cost_item_relation_id'])
                        ? $item['cost_item_relation_id']
                        : ($item['cost_name'] ?? "");
                    $existingCost = $result[$item['order_id']][$key] ?? [];
                    $existingCost['cost'] = ($existingCost['cost'] ?? 0) + floatval($item['cost'] ?? 0);
                    $result[$item['order_id']][$key] = $existingCost;
                }
                return $result;
            },
            []
        );
        $costItemRelationIds = array_reduce($data, function ($carry, $item) {
            $costLists = $item['cost_list'] ?? [];
            $costItemRelationIds = array_column($costLists, 'cost_item_relation_id');
            return array_merge($carry, $costItemRelationIds);
        }, []);
        $costItemRelationIds = Arr::uniqueFilterValues(($costItemRelationIds));
        $costItemRelationMap = [];
        if (!empty($costItemRelationIds)) {
            $costItemInvoiceRelationList = CostItemInvoiceRelationApi::getCostItemRelationByInvoice(\Constants::TYPE_ORDER, 1);
            $costItemRelationMap = array_column($costItemInvoiceRelationList, null, 'relation_id');
        }
        return [
            'shipping_cost_map' => $shippingCostMap,
            'cost_item_map' => $costItemRelationMap,
        ];
    }

    public function mapShippingCostList(&$result, $key)
    {
        $relationMap = $this->getPipelinePrepareData($key)['shipping_cost_map'][$result['order_id']] ?? [];
        $costItemMap = $this->getPipelinePrepareData($key)['cost_item_map'] ?? [];
        $shipping_cost_map = [];
        if (!empty($result['cost_list'])) {
            foreach ($result['cost_list'] as $cost) {
                if (empty($cost)) {
                    continue;
                }
                if (isset($cost['cost_item_relation_id']) && !empty($cost['cost_item_relation_id'])) {
                    $costItemRelationInfo = $costItemMap[$cost['cost_item_relation_id']] ?? [];
                    $cost['cost_name'] = $costItemRelationInfo['item_name'] ?? '';
                }
                $key = isset($cost['cost_item_relation_id']) && !empty($cost['cost_item_relation_id'])
                    ? $cost['cost_item_relation_id']
                    : $cost['cost_name'];
                $amount = floatval($cost['cost'] ?? 0);
                if (!isset($shipping_cost_map[$key])) {
                    $orderCost = $amount;
                } else {
                    $orderCost = floatval($shipping_cost_map[$key]['cost']) + $amount;
                }
                $cost['order_id'] = $result['order_id'];
                //如果不存在 可继承金额为cost
                $cost['inheritable_cost'] = round((($orderCost) - round(floatval(($relationMap[$key]['cost'] ?? 0)), 4)), 4);
                $cost['inheritable_percentage'] = $orderCost != 0 ? round(abs($cost['inheritable_cost'] / $orderCost), 4) : 0;
                $cost['cost'] = $orderCost;
                $shipping_cost_map[$key] = $cost;
            }
        }
        $result['shipping_cost_list'] = array_values($shipping_cost_map);
    }


    public function buildCashCollectionStatus($key, array $data)
    {
        $orderIds = array_column($data, 'order_id');
        if (!empty($orderIds)) {
            $cashCollectionMap = \common\library\cash_collection\Helper::getReferStatsMap($this->clientId, CashCollection::REFER_TYPE_ORDER, $orderIds);
        }
        return $cashCollectionMap ?? [];
    }

    public function mapCashCollectionStatus(&$result, $key)
    {

        $map = $this->getPipelinePrepareData($key);

        $orderId = $result['order_id'];
        if ($this->showCashCollectionDetail) {
            $result['cash_collection_info'] = $map[$orderId] ?? \common\library\cash_collection\Helper::getDefaultCashCollectionStatusInfo([
                'amount' => floatval($data['amount'] ?? 0),
                'amount_rmb' => floatval($data['amount_rmb'] ?? 0),
                'amount_usd' => floatval($data['amount_usd'] ?? 0),
            ]);
            $mainCurrency = Client::getClient($this->clientId)->getMainCurrency();
            $result['cash_collection_percentage'] = ($mainCurrency == \common\library\exchange_rate\ExchangeRateService::USD) ? ($result['cash_collection_info']['percentage_usd'] ?? 0) : ($result['cash_collection_info']['percentage_rmb'] ?? 0);
        } else {
            $cashStatus = $map[$orderId] ?? [];
            $result['cash_collection_info'] = [
                'status' => $cashStatus['status'] ?? 0,
                'status_name' => $cashStatus['status_name'] ?? '',
            ];
        }

        $result['collect_amount'] = $map[$orderId]['collect_amount'] ?? 0;
        $result['not_collect_amount'] = $map[$orderId]['not_collect_amount'] ?? 0;
        $result['bank_charge'] = $map[$orderId]['bank_charge'] ?? 0;
        $result['real_amount'] = $map[$orderId]['real_amount'] ?? 0;
        $result['exchange_loss_amount_rmb'] = $map[$orderId]['exchange_loss_amount_rmb'] ?? 0;
        $result['exchange_loss_amount_usd'] = $map[$orderId]['exchange_loss_amount_usd'] ?? 0;
        try {
            $result['total_amount_rmb'] = floatval(bcsub(bcadd($map[$orderId]['real_amount_rmb'] ?? 0 ,$map[$orderId]['exchange_loss_amount_rmb'] ?? 0,2), $result['service_fee_amount_rmb'] ?? 0, 2));
            $result['total_amount_usd'] = floatval(bcsub(bcadd($map[$orderId]['real_amount_usd'] ?? 0 ,$map[$orderId]['exchange_loss_amount_usd'] ?? 0,2), $result['service_fee_amount_usd'] ?? 0, 2));
        } catch (\Throwable $e) {
            // 目前clientid为2873的客户有脏数据cash_collection_id=**************，real_amount_rmb=***************.00  导致$map[$orderId]['real_amount_rmb']是科学计数法，导致bcadd出错，暂时紧急修复
            $result['total_amount_rmb'] = $map[$orderId]['real_amount_rmb'] ?? 0 + $map[$orderId]['exchange_loss_amount_rmb'] ?? 0 - $result['service_fee_amount_rmb'] ?? 0;
            $result['total_amount_usd'] = $map[$orderId]['real_amount_usd'] ?? 0 + $map[$orderId]['exchange_loss_amount_usd'] ?? 0 - $result['service_fee_amount_usd'] ?? 0;
            \LogUtil::info("get real_amount_rmb error", [
                'msg' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
//
//        $result['total_amount_rmb'] = floatval(bcsub(bcadd($data['stats']['real_amount_rmb'] ,$data['stats']['exchange_loss_amount_rmb'],2), $listData[0]['order_info']['service_fee_amount_rmb'] ?? 0, 2));
//        $result['total_amount_usd'] = floatval(bcsub(bcadd($data['stats']['real_amount_usd'],$data['stats']['exchange_loss_amount_usd'],2), $listData[0]['order_info']['service_fee_amount_usd'] ?? 0, 2));
    }

    public function buildCostInvoiceInfo($key, array $data)
    {

        $orderIds = array_column($data, 'order_id');
        $costInvoiceInfoMap = [];
        $costInvoiceSummary = [];
        if (empty($orderIds)) {
            return [
                'cost_invoice_info' => $costInvoiceInfoMap,
                'cost_invoice_summary' => $costInvoiceSummary
            ];
        }

        $costInvoiceFilter = new CostInvoiceFilter($this->clientId);
        $costInvoiceFilter->order_id = array_unique($orderIds);
        $costInvoiceFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $costInvoiceSummaryFilter = deep_copy($costInvoiceFilter);
        $costInvoiceSummary = (new CostInvoiceApi())->getSummaryInfo($this->clientId, $costInvoiceSummaryFilter);
        $costInvoiceList = $costInvoiceFilter->find();
        $costInvoiceList->getFormatter()->orderProfitDetailWebSetting();
        $costInvoiceList = $costInvoiceList->getListAttributes();

        foreach ($costInvoiceList as $item) {
            //订单关联的费用单
            $costInvoiceInfoMap[$item['order_id']][] = $item;
        }
        unset($costInvoiceList);

        return [
            'cost_invoice_info' => $costInvoiceInfoMap,
            'cost_invoice_summary' => $costInvoiceSummary
        ];
    }


    public function mapCostInvoiceInfo(&$result, $key)
    {
        $map = $this->getPipelinePrepareData($key);
        //展示「费用单费用项」
        $result['cost_invoice_info'] = $map['cost_invoice_info'][$result['order_id']] ?? [];
        $result['cost_invoice_summary'] = $map['cost_invoice_summary'] ?? [];
        foreach ($result['cost_invoice_info'] as $cost_invoice_item) {
            $result[$cost_invoice_item['cost_item_id']] = round((($cost_invoice_item['amount_rmb'] ?? 0) + ($result[$cost_invoice_item['cost_item_id']] ?? 0)), 4);
        }
    }

    public function buildCostListInfo($key, array $data)
    {
        $costItemInvoiceRelationList = CostItemInvoiceRelationApi::getCostItemRelationByInvoice(\Constants::TYPE_ORDER, 1);
        $costItemRelationMap = array_column($costItemInvoiceRelationList, null, 'relation_id');
        return $costItemRelationMap;
    }

    public function mapCostListInfo(&$result, $key)
    {
        $map = $this->getPipelinePrepareData($key);
        foreach ($result['cost_list'] as &$cost) {
            if (empty($cost)) {
                continue;
            }
            $cost['cost'] = is_numeric($cost['cost'] ?? 0) ? ($cost['cost'] ?? 0) : 0;
            $cost['cost'] = $this->specialFieldFormat('cost', $cost['cost']);
            $cost['percent_amount'] = $this->specialFieldFormat('percent_of_total_amount', $cost['percent_amount'] ?? 0);
            if (!empty($cost['cost_item_relation_id'])) {
                $costItemRelationInfo = $map[$cost['cost_item_relation_id']] ?? [];
                $cost['cost_name'] = $costItemRelationInfo['item_name'] ?? '';
            }
        }
        $result['addition_cost_info'] = [
            'addition_cost_amount' => $result['addition_cost_amount'] ?? 0,
            'addition_cost_list' => $result['cost_list'] ?? []
        ];
    }

    public function mapCostItems(&$result, $key)
    {
        $relationMap = $this->getPipelinePrepareData($key);
        //展示费用项 订单附加费用项」
        foreach ($result['cost_list'] as $costItem) {
            if (isset($costItem['cost_item_relation_id']) && !empty($relationMap[$costItem['cost_item_relation_id']] ?? [])) {
                $item_id = $relationMap[$costItem['cost_item_relation_id']]['item_id'] ?? 0;
                $result[$item_id] = round(($costItem['cost'] * ($result['exchange_rate'] ?? 0) / 100 + ($result[$item_id] ?? 0)), 4);
            }
        }
    }

    public function mapStatusAction(&$result, $key)
    {
        $statusActions = [];
        if ($result['source_type'] == OrderConstants::TYPE_ALI_ORDER || $result['source_type'] == OrderConstants::TYPE_DIRECT_PAY_ORDER) {
            $statusActions = \common\library\alibaba\order\AlibabaOrderSyncHelper::formatStatusAction($result['ali_order_id'], $result['status_action']);
        }

        $result['status_action'] = array_values($statusActions);
    }

    public function mapCanChangeCurrency(&$result, $key)
    {
        $result['can_change_currency'] = \common\library\invoice\Helper::canChangeCurrency($this->clientId, $result['order_id']);
    }

    public function buildProgressInfo($key, array $data)
    {
        $shippingInvoiceCountMap = [];
        $purchaseOrderCountMap = [];
        $outboundCountMap = [];
        $orderProductTotalCountMap = [];
        $orderIds = [];
        foreach ($data as $line) {
            $orderId = $line['order_id'];
            $orderIds[] = $orderId;
            $orderProductTotalCountMap[$orderId] = 0;
            foreach ($line['product_list'] as $product) {
                if (isset($product['product_type']) && $product['product_type'] == ProductConstant::PRODUCT_TYPE_COMBINE) {
                    $combineProductData[$orderId][] = [
                        'product_id' => $product['product_id'],
                        'count' => $product['count'],
                    ];
                } else {
                    $orderProductTotalCountMap[$orderId] += floatval($product['count']);
                }
            }
        }

        if (!empty($orderIds)) {

            $filter = new ShippingInvoiceApi($this->clientId, $this->getViewUserId());
            $orderShippingList = $filter->getRelateInvoiceProduct(
                $orderIds,
            );
            foreach ($orderShippingList as $item) {
                if (isset($shippingInvoiceCountMap[$item['order_id']])) {
                    $shippingInvoiceCountMap[$item['order_id']]['have_shipping_count'] += $item['have_shipping_count'];
                } else {
                    $shippingInvoiceCountMap[$item['order_id']] = [
                        'have_shipping_count' => $item['have_shipping_count'],
                    ];
                }
            }

            //销售总数量为产品总数量，若产品为组合产品，计算子产品数量
            if (!empty($combineProductData)) {
                $combineProductIds = [];
                foreach ($combineProductData as $datum) {
                    $combineProductIds = array_unique(array_merge($combineProductIds, array_column($datum, 'product_id')));
                }
                $combineProductRelationMap = (new CombineProductRelationAPI($this->clientId))->getSubProductInfo($combineProductIds);
                foreach ($combineProductData as $k => $orderDatum) {
                    foreach ($orderDatum as $combineProductDatum) {
                        foreach ($combineProductRelationMap as $item) {
                            if ($combineProductDatum['product_id'] == $item['combine_product_id']) {
                                $orderProductTotalCountMap[$k] += $item['count'] * $combineProductDatum['count'];
                            }
                        }
                    }
                }
            }
            $purchaseOrderCountMap = \common\library\purchase\purchase_order_product\Helper::getPurchaseOrderCountInfoMap($this->clientId, $orderIds);
            $outboundCountMap = \common\library\oms\product_transfer\outbound\record\Helper::getOutboundCountMap($this->clientId, $orderIds);
            $cashCollectionMap = $this->getPipelinePrepareData('cash_collection_status');

            foreach ($orderIds as $orderId) {
                $purchaseProgress = $inboundProgress = 0;
                $orderProductTotalCount = round($orderProductTotalCountMap[$orderId] ?? 0, 6);

                $fromStockCount = round($outboundCountMap[$orderId]['from_stock_count'] ?? 0, 6);
                $needPurchaseCount = ($orderProductTotalCount - $fromStockCount > 0) ? (round($orderProductTotalCount, 6) - $fromStockCount) : 0;

                if ($orderProductTotalCount > 0) {
                    $purchaseProgress = ($needPurchaseCount == 0) ? 100 : round(($purchaseOrderCountMap[$orderId]['purchase_count'] ?? 0) / $needPurchaseCount, 2) * 100;
                    $inboundProgress = ($needPurchaseCount == 0) ? 100 : round(($purchaseOrderCountMap[$orderId]['inbound_count'] ?? 0) / $needPurchaseCount, 2) * 100;
                }

                $shippingCount = round($shippingInvoiceCountMap[$orderId]['have_shipping_count'] ?? 0, 6);
                $cashCollection = $cashCollectionMap[$orderId] ?? [];
                $map[$orderId] = [
                    'cash_collection_progress' => $cashCollection && isset($cashCollection['percentage']) ? $cashCollection['percentage'] : 0,
                    'purchase_count' => round($purchaseOrderCountMap[$orderId]['purchase_count'] ?? 0, 6),
                    'inbound_count' => round($purchaseOrderCountMap[$orderId]['inbound_count'] ?? 0, 6),
                    'product_total_count' => round($orderProductTotalCountMap[$orderId] ?? 0, 6),
                    'outbound_count' => round($outboundCountMap[$orderId]['outbound_count'] ?? 0, 6),
                    'purchase_progress' => $purchaseProgress,
                    'inbound_progress' => $inboundProgress,
                    'outbound_progress' => (!empty($outboundCountMap[$orderId]['outbound_count']) && !empty($orderProductTotalCount)) ? round($outboundCountMap[$orderId]['outbound_count'] / $orderProductTotalCount, 2) * 100 : 0,
                    'from_stock_count' => $fromStockCount,
                    'need_purchase_count' => $needPurchaseCount,
                    'shipping_count' => $shippingCount,
                    'shipping_progress' => ($orderProductTotalCount == 0) ? 0 : round($shippingCount / $orderProductTotalCount, 2) * 100
                ];
            }
        }
        return $map ?? [];
    }

    public function mapProgressInfo(&$result, $key)
    {
        $map = $this->getPipelinePrepareData($key);
        $result['progress_info'] = $map[$result['order_id']];
    }

    public function mapSystemInfo(&$result, $key)
    {
        $result['system_info'] = [
            'create_user' => $result['create_user_info'] ?? [],
            'create_time' => $result['create_time'] ?? '',
            'update_time' => $result['update_time'] ?? '',
            'update_user' => $result['update_user_info'] ?? [],
            'seller_account_info' => $result['seller_account_info'] ?? [],
            'ali_order_id' => $result['ali_order_id'] ?? 0,
            'archive_type' => $result['archive_type'] ?? 0,
            'store_info' => $result['store_info'] ?? [],
            'last_sync_time' => $result['last_sync_time'] == '1970-01-01 00:00:01' ? '' : $result['last_sync_time'],
        ];
    }

    public function mapReferInvoiceProductCount(&$result, $key)
    {
        $referInvoiceProductCount = Helper::hasReferDownStreamInvoiceByOrderProduct($this->getViewUserId(), $this->clientId, $result['order_id']);
        $result['refer_invoice_product_count'] = $referInvoiceProductCount ?: new \stdClass();
    }

    public function mapOperatePrivilege(&$result, $key)
    {
        $result['operate_privilege'] = $this->getOperatePrivilege($result['lock_flag'] ?? 0, $result['handler']);
    }


    public function getOperatePrivilege($lockFlag = true, $handler = [])
    {
        if (is_null($handler)) {
            $handler = [];
        }
        $permissionMap = [
            'order_edit' => PrivilegeConstants::PRIVILEGE_CRM_ORDER_EDIT,
            'cash_collection_create' => PrivilegeConstants::PRIVILEGE_CRM_CASH_COLLECTION_CREATE,
//            'cash_collection_edit' => PrivilegeConstants::PRIVILEGE_CRM_CASH_COLLECTION_EDIT,
            'logistics_view' => PrivilegeConstants::PRIVILEGE_CRM_TRANSPORT_VIEW,
            'order_export' => PrivilegeConstants::PRIVILEGE_CRM_ORDER_EXPORT,
            'order_generate_purchase_order' => PrivilegeConstants::PRIVILEGE_CRM_ORDER_TO_PURCHASE,
            'order_create' => PrivilegeConstants::PRIVILEGE_CRM_ORDER_CREATE,
            'order_delete' => PrivilegeConstants::PRIVILEGE_CRM_ORDER_REMOVE,
            'order_edit_number_create' => PrivilegeConstants::PRIVILEGE_CRM_ORDER_EDIT_NO_CREATE,
            'order_edit_number_edit' => PrivilegeConstants::PRIVILEGE_CRM_ORDER_EDIT_NO_EDIT,
            'edit_attachment' => PrivilegeConstants::PRIVILEGE_CRM_ORDER_EDIT,
        ];

        $initData = [
            'order_edit' => false,
            'cash_collection_create' => false,
            'logistics_view' => false,
            'order_export' => false,
            'order_generate_purchase_order' => false,
            'order_create' => false,
            'order_delete' => false,
            'order_edit_number_create' => false,
            'order_edit_number_edit' => false,
            'edit_attachment' => false,
        ];

        $data = $initData;
        foreach ($permissionMap as $k => $v) {
            $permissionScopeUser = \common\library\privilege_v3\Helper::getPermissionScopeUser($this->clientId, $this->getViewUserId(), $v, true);
            if ($permissionScopeUser === \common\library\privilege_v3\Helper::CAN_MANAGE_ALL_USER
                || array_intersect($handler, $permissionScopeUser)
            ) {
                $data[$k] = true;
            }
        }


        $unlock = false;
        $unlockPermission = \common\library\privilege_v3\Helper::hasPermission($this->clientId, $this->getViewUserId(), PrivilegeConstants::PRIVILEGE_CRM_ORDER_APPROVAL_UNLOCK);

        //是否可以解锁
        if ($lockFlag) {
            $data = $initData;
        }

        if ($unlockPermission && $lockFlag) {
            $unlock = true;
        }

        if (!empty($data['approval_flow_info']) && is_array($data['approval_flow_info'])) {
            $userId = \User::getLoginUser()->getUserId();
            $approvalFlowInfo = $data['approval_flow_info'];
            if ($approvalFlowInfo['status'] == \common\library\approval_flow\Constants::APPROVAL_STATUS_APPROVING) {

                //审批中的除了指定的审批人可以编辑其他的不可编辑
                $userMap = array_column($approvalFlowInfo['approving_approver'], 'edit_flag', 'user_id');

                if (isset($userMap[$userId]) && $userMap[$userId]) {
                    $data['order_edit'] = true;
                } else {
                    $data = $initData;
                }
                //审批中的不能解锁
                $unlock = false;
            }

            if ($lockFlag && $approvalFlowInfo['status'] != \common\library\approval_flow\Constants::APPROVAL_STATUS_APPROVING) {
                $data['edit_attachment'] = true;
            }
        }

        $data['order_unlock'] = $unlock;
        return $data;
    }

    public function mapOperateButtonAccess(&$result, $key)
    {
        //$result若已提前计算权限目标人员、操作权限和字段权限，formatter按钮权限时，可直接使用$result权限结果，使用过后，可删除权限目标人员和操作权限
        $result['operate_access'] = OrderButtonFactory::make($this->showOperateButtonAccess, $result);
        if (isset($result['access_privilege_stats'])) {
            unset($result['access_privilege_stats']);
        }
        if (isset($result['privilege_scope_user_ids'])) {
            unset($result['privilege_scope_user_ids']);
        }
    }

    public function buildTransferInfo($key, array $data)
    {
        $orderIds = array_column($data, 'order_id');
        $outboundTransferMap = $purchaseTransferMap = $transferRecordCountMap = [];
        if ($this->showTransferInfo && !empty($orderIds)) {
            ['purchase_transfer_map' => $purchaseTransferMap, 'outbound_transfer_map' => $outboundTransferMap] = (new ProductTransferAPI($this->clientId, $this->getViewUserId()))->getTransferInvoiceMapByReferId($orderIds);

            // 获取订单明细的相关流转数量
            if ($this->showTransferType == \Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND) {
                $outboundTransferApi = new OutboundProductTransferAPI($this->clientId);
                // 获取订单直接/间接关联的已确认出库单明细（即已下单出库数）

                // 订单关联的出库任务（非草稿状态）明细数（即已安排出库数）
                [$taskOutboundCountList, $taskOutboundTransferList] = $outboundTransferApi->getOrderRelateOutboundTransferRecordCount($orderIds);
                $transferRecordCountMap['task_outbound_count'] = $taskOutboundCountList;
                $orderRelateTaskRecordIds = array_unique(array_column($taskOutboundTransferList, 'transfer_invoice_record_id')); // 与本页订单相关联的非草稿采购任务id

                // 订单关联的出库任务关联的已确定出库单明细数
                $checkTaskOutboundCountList = $outboundTransferApi->getOutboundTransferRelateOutboundInvoiceRecordCount($orderRelateTaskRecordIds);
                $transferRecordCountMap['check_task_outbound_count'] = array_column($checkTaskOutboundCountList, 'check_task_outbound_count', 'sub_refer_id');

            }

            if ($this->showTransferType == \Constants::TYPE_PRODUCT_TRANSFER_PURCHASE) {
                // 获取已确定状态的采购订单
                $purchaseOrderFilter = new PurchaseOrderFilter($this->clientId);
                $endStatusInfo = (new InvoiceStatusService($this->clientId, \Constants::TYPE_PURCHASE_ORDER))->endStatus();
                $statusIds = array_column($endStatusInfo, 'id');
                if (empty($statusIds)) {
                    $purchaseOrderFilter->alwaysEmpty();
                }
                $purchaseOrderFilter->status = $statusIds;

                $purchaseTransferApi = new PurchaseProductTransferAPI($this->clientId);

                // 获取订单直接/间接关联的已确认采购订单明细（即已下单采购数）

                // 订单关联的采购任务（非草稿状态）明细数（即已安排采购数）
                [$taskPurchaseTransferList, $taskPurchaseCountList] = $purchaseTransferApi->getOrderRelatePurchaseTransferRecordCount($orderIds);
                $transferRecordCountMap['task_purchase_count'] = $taskPurchaseCountList;
                $orderRelateTaskRecordIds = array_unique(array_column($taskPurchaseTransferList, 'transfer_invoice_record_id')); // 与本页订单相关联的非草稿采购任务id

                // 订单关联的采购任务关联的已确定采购订单明细数
                $checkTaskPurchaseCountList = $purchaseTransferApi->getPurchaseTransferRelatePurchaseInvoiceRecordCount($orderRelateTaskRecordIds, $purchaseOrderFilter);
                $transferRecordCountMap['check_task_purchase_count'] = array_column($checkTaskPurchaseCountList, 'check_task_purchase_count', 'invoice_product_id');
            }
        }
        $map = [
            'purchase_transfer_map' => $purchaseTransferMap,
            'outbound_transfer_map' => $outboundTransferMap,
            'transfer_record_count_map' => $transferRecordCountMap,
        ];
        return $map;
    }

    public function mapTransferInfo(&$result, $key)
    {
        $map = $this->getPipelinePrepareData($key);

        $orderId = $result['order_id'];
        $purchaseTransfer = $map['purchase_transfer_map'][$orderId] ?? 0;
        $outboundTransfer = $map['outbound_transfer_map'][$orderId] ?? 0;
        $transferRecordCountMap = $map['transfer_record_count_map'];
        if ($this->showTransferInfo) {
            $result['downstream_transfer_info'] = [
                'purchase_transfer' => $purchaseTransfer,
                'outbound_transfer' => $outboundTransfer,
            ];
        }

        if ($this->showTransferInfo && $this->showTransferType) {
            $productList = [];
            foreach ($result['product_list'] ?? [] as $product) {
                $id = $product['unique_id'] ?? $product['id'];
                $product['task_purchase_count'] = $transferRecordCountMap['task_purchase_count'][$id] ?? 0;   // 已安排采购数
                $product['to_purchase_count'] = ProductTransferHelper::formatTransferCount($product['count'] - $product['task_purchase_count']);   //待采购数量
                if ($product['to_purchase_count'] < 0) {
                    $product['to_purchase_count'] = 0;
                }
                $product['task_outbound_count'] = $transferRecordCountMap['task_outbound_count'][$id] ?? 0;   // 已安排采购数
                $product['check_task_outbound_count'] = $transferRecordCountMap['check_task_outbound_count'][$id] ?? 0;        // 订单关联任务所关联的出库明细;
                $product['to_outbound_count'] = ProductTransferHelper::formatTransferCount($product['count'] - $product['task_outbound_count']);   //待采购数量
                if ($product['to_outbound_count'] < 0) {
                    $product['to_outbound_count'] = 0;
                }

                if ($this->ignorePurchaseCountDone && (isset($product['to_purchase_count']) && $product['to_purchase_count'] == 0)) {
                    continue;
                }
                $productList[] = $product;
            }
            $result['product_list'] = $productList;
        }
    }

    public function buildErpStatusInfo($key, array $data)
    {
        $orderIds = array_column($data, 'order_id');
        if (!empty($orderIds)) {
            $erpStatusInfoResult = (new OrderErpExternalApi())->orderErpInfo($this->clientId, $orderIds);
            $erpStatusInfoMap = array_column($erpStatusInfoResult, null, 'order_id');
        }
        return $erpStatusInfoMap ?? [];
    }

    public function mapErpStatusInfo(&$result, $key)
    {
        $result['erp_status_info'] = $map = $this->getPipelinePrepareData($key)[$result['order_id']] ?? null;
    }

    public function buildExportFileCount($key, array $data)
    {
        $orderIds = array_column($data, 'order_id');
        if (!empty($orderIds)) {
            $exportFileListObj = new InvoiceExportFileList($this->getViewUserId(), \Constants::TYPE_ORDER);
            $exportFileListObj->setClientId($this->clientId);
            $exportFileListObj->setReferIds($orderIds);
            $exportFileListObj->setGroupBy(['refer_id']);
            $exportFileCountList = $exportFileListObj->groupCount();
            $exportFileCountMap = array_combine(array_column($exportFileCountList, 'refer_id'), array_column($exportFileCountList, 'count'));
        }
        return $exportFileCountMap ?? [];
    }

    public function mapExportFileCount(&$result, $key)
    {
        $result['export_file_count'] = $this->getPipelinePrepareData($key)[$result['order_id']] ?? 0;
    }

    public function buildAlibabaAccountInfo($key, array $data)
    {
        $sellerAccountIds = array_column($data, 'seller_account_id');
        $orderIds = array_column($data, 'order_id');
        if (!empty($sellerAccountIds)) {
            $sellerAccountMap = \common\library\alibaba\order\AlibabaOrderSyncHelper::getAlibabaOrderSellerAccountInfoMap($this->clientId, $orderIds, $sellerAccountIds);
        }
        return $sellerAccountMap ?? [];
    }

    public function mapAlibabaAccountInfo(&$result, $key)
    {
        $result['seller_account_info'] = $this->getPipelinePrepareData($key)[$result['seller_account_id']] ?? [];
    }



    public function setProductImageFormatter(bool $productImageFormatter): void
    {
        $this->productImageFormatter = $productImageFormatter;
    }

    public function buildInquiryCollaborationInfoMap($key, array $data)
    {
        //inquiry_collaboration_id
        $inquiry_collaboration_ids = array_filter(array_column($data,'inquiry_collaboration_id'));

        //
        $inquiryCollaborationInfoMap = [];
        if (!empty($inquiry_collaboration_ids)) {
            $filter = new InquiryCollaborationFilter($this->clientId);
            $filter->inquiry_collaboration_id = $inquiry_collaboration_ids;
            $filter->enable_flag = 1;
            $filter->select(['inquiry_collaboration_id','inquiry_collaboration_no','inquiry_collaboration_name']);
            $filter->skipPrivilegeCheck(true);
            $inquiryList = $filter->rawData();
            foreach ($inquiryList as $elem){
                $inquiryCollaborationInfoMap[$elem['inquiry_collaboration_id']] = $elem;
            }
        }

        return $inquiryCollaborationInfoMap ?? [];
    }

    public function mapInquiryCollaborationInfo(&$result, $key)
    {
        $map = $this->getPipelinePrepareData($key);
        $inquiryInfo = $map[$result['inquiry_collaboration_id'] ?? ''] ?? [];
        $result['inquiry_collaboration_info'] = [];
        if (!empty($inquiryInfo)) {
            $result['inquiry_collaboration_info'] = [
                'inquiry_collaboration_id' => $inquiryInfo['inquiry_collaboration_id'],
                'inquiry_collaboration_no' => $inquiryInfo['inquiry_collaboration_no'],
                'inquiry_collaboration_name' => $inquiryInfo['inquiry_collaboration_name'],
            ];
            $result['refer_inquiry_collaboration_no'] = $inquiryInfo['inquiry_collaboration_no'];
        }
    }
}
