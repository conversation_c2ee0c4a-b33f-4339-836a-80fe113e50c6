<?php

namespace common\library\oms\order;

use common\library\account\Client;
use common\library\alibaba\order\AlibabaOrderSyncHelper;
use common\library\approval_flow\diff_format\MergeDiffFieldTrait;
use common\library\approval_flow\traits\OperateTriggerApproval;
use common\library\cash_collection\CashCollection;
use common\library\cash_collection\CashCollectionBatchOperator;
use common\library\cms\inquiry\Inquiry;
use common\library\cms\inquiry\InquiryService;
use common\library\CommandRunner;
use common\library\coroutine\exception\RuntimeException;
use common\library\custom_field\FieldList;
use common\library\customer_v3\company\orm\Company;
use common\library\customer_v3\customer\orm\Customer;
use common\library\exchange_rate\ExchangeRateService;
use common\library\history\base\Builder;
use common\library\history\base\compare\FieldCompare;
use common\library\history\invoice\OrderCompare;
use common\library\history\order\OrderSetting;
use common\library\invoice\notify\OrderNotify;
use common\library\invoice\status\InvoiceStatusService;
use common\library\lock\TraitReferLock;
use common\library\object\field\FieldFilter;
use common\library\object\field\service\FunctionFieldService;
use common\library\object\object_define\Constant as ObjConstant;
use common\library\object\traits\FieldSettingHandler;
use common\library\object\traits\GetFieldTransfer;
use common\library\object\traits\ObjectNameGetter;
use common\library\oms\alibaba_order_product\AlibabaOrderProductFilter;
use common\library\oms\alibaba_order_product\BatchAlibabaOrderProduct;
use common\library\oms\common\OmsConstant;
use common\library\oms\order_link\OrderLink;
use common\library\oms\order_link\trigger\CashCollectionTrigger;
use common\library\oms\order_link\trigger\OrderLinkStartDoneTrigger;
use common\library\oms\order_product\BatchOrderProduct;
use common\library\oms\order_product\OrderProductFilter;
use common\library\oms\order_product\OrderProductMetadata;
use common\library\oms\order_profit\Constant as OrderProfitConstant;
use common\library\oms\order_profit\OrderProfitApi;
use common\library\oms\order_profit\OrderProfitFactorTriggerTrait;
use common\library\oms\order_profit\trigger\TaxRefundAmountTrigger;
use common\library\oms\product_transfer\ProductTransferHelper;
use common\library\oms\product_transfer\traits\RelationUpdateTransferTrait;
use common\library\oms\quotation\Quotation;
use common\library\oms\traits\InvoiceEditTrait;
use common\library\opportunity\Opportunity;
use common\library\performance_v2\refer\OrderPerformance;
use common\library\performance_v2\trigger\PerformanceTriggerTrait;
use common\library\privilege_v3\field\BaseObjectPrivilegeField;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeFieldV2;
use common\library\privilege_v3\PrivilegeService;
use common\library\recycle\Recycle;
use common\library\serial\GenerateService;
use common\library\trail\events\InvoiceEvents;
use common\library\trail\TrailConstants;
use common\library\util\Arr;
use common\library\workflow\trigger\BaseObjectTriggerTrait;
use PgActiveRecord;
use User;
use xiaoman\orm\common\SingleObjectV2;
use xiaoman\orm\database\data\NotEqual;


/**
 * class Order
 * @package common\library\oms\order
 * @property integer order_id  id
 * @property string order_no  编号
 * @property string name  订单名称
 * @property integer client_id
 * @property object|array users       关联业务员
 * @property object|array departments 关联部门
 * @property array handler      处理人
 * @property array user_id      查询,可操作的用户id
 * @property integer create_user
 * @property integer update_user
 * @property integer status     订单状态
 * @property integer approval_status    审批状态
 * @property integer company_id
 * @property  integer company_group_id
 * @property  integer opportunity_id 关联商机id
 * @property integer customer_id
 * @property string create_time
 * @property string update_time
 * @property string delete_time
 * @property string remark      备注
 * @property integer account_flag   是否已经结算业绩
 * @property string account_date    业绩结算日期
 * @property string price_contract  价格条款
 * @property string price_contract_remark 价格条款说明
 * @property string currency        币种
 * @property string exchange_rate   人币民汇率
 * @property string exchange_rate_usd   usd汇率
 * @property double amount    订单金额
 * @property double amount_rmb 折rmb金额
 * @property double amount_usd 折usd金额
 * @property string payment_link 支付链接
 * @property string source_type 订单类型（来源） 1crm 2信保 3E收汇
 * @property integer ali_order_id 阿里订单id
 * @property integer ali_store_id 阿里店铺id
 * @property string seller_account_id 阿里卖家id
 * @property string last_sync_time 上次更新时间
 * @property integer ali_status_id 阿里状态id
 * @property string ali_status_name 阿里状态名称
 * @property string country 国家
 * @property object|array status_action 阿里订单行为
 * @property string discount_amount_currency 折扣金额币种
 * @property string discount_amount 折扣金额
 * @property string shipment_fee_currency 运费币种
 * @property string shipment_fee_amount 运费金额
 * @property string fulfillment_channel 履约通道
 * @property array scope_user_ids 权限归属人
 *
 * @property integer enable_flag
 * @property integer delete_flag
 * 基本信息自定义字段
 * @property array external_field_data  拓展字段
 * 交易产品
 * @property array product_list 订单产品的信息，审批流的中断需要该字段承载数据
 * @property integer product_total_count  产品总数量
 * @property double product_total_amount 产品总金额
 * @property double product_total_amount_usd 产品总金额USD
 * @property double product_total_amount_rmb 产品总金额RMB
 * @property integer package_gross_weight_amount 包装毛重总计
 * @property integer package_volume_amount 包装体积总计
 * 附加费用
 * @property object cost_list
 * @property double addition_cost_amount 附加费用总额
 * @property double addition_cost_amount_rmb 附加费用总额(rmb)
 * @property double addition_cost_amount_usd 附加费用总额(usd)
 * @property string order_contract  订单条款
 * @property string bank_info   银行信息
 * @property string receive_remittance_remark   收汇方式说明
 * @property string receive_remittance_way 收汇方式
 * @property string insurance_remark    保险说明
 * @property double service_fee_amount 交易服务费
 * @property double service_fee_amount_rmb 交易服务费(rmb)
 * @property double service_fee_amount_usd 交易服务费(usd)
 * @property string service_fee_currency 交易服务费币种
 * 买方信息
 * @property string customer_phone  联系人电话
 * @property string customer_email  联系人邮箱
 * @property string customer_address  联系人地址
 * @property string customer_name   联系人名称
 * @property string company_phone   客户电话
 * @property string company_name    客户名称
 * @property string company_fax     客户传真
 * @property string company_address     客户地址
 * 货运信息
 * @property string transport_mode     运输方式
 * @property string package_remark     包装说明
 * @property string shipment_port     装运港口
 * @property string shipment_deadline_remark     装运期限说明
 * @property string shipment_deadline     装运期限
 * @property string marked     唛头
 * @property string more_or_less     溢短装
 * @property string target_port     目标港口
 * @property integer $archive_type 创建类型
 * @property integer $source_lead_id 来源线索id
 * 来源
 * @property string $source 来源
 * @property integer $source_id 来源id
 * 附件列表
 * @property array file_list
 * @property string purchase_status_time 进入以销定购状态时间
 * @property array link_status 环节状态
 * @property string order_gross_margin 产品毛利总计
 * @property string order_gross_margin_cny 产品毛利总计cny
 * @property string order_gross_margin_usd 产品毛利总计usd
 * @property string cost_with_tax_total 含税成本总金额
 *
 * @property int capital_account_id 新银行信息（关联资金账号）
 * @property string last_order_status_update_time 最近订单状态变更时间
 * @property int tax_refund_type 退税类型
 * @property double parts_total_count 配件总数量
 * @property double product_total_count_no_parts 产品总数量（不含配件）
 * @property integer quotation_id
 * @property integer inquiry_collaboration_id
 * @property int transfer_ali_order_type
 * @method OrderOperator getOperator()
 * @method OrderFormatter getFormatter()
 */
class Order extends SingleObjectV2
{
    use  OperateTriggerApproval;
    use  OrderProfitFactorTriggerTrait;
    use  BaseObjectTriggerTrait;
    use RelationUpdateTransferTrait;
    use BaseObjectPrivilegeField;
    use TraitReferLock;
    use PerformanceTriggerTrait;
    use GetFieldTransfer;
    use ObjectNameGetter;
    use FieldSettingHandler;
    use InvoiceEditTrait;

    /**
     * 一个主对象可以有多个从对象  从对象的数据保存如何处理
     * 如果这里定义了对象关系，以后开放自定义主从关系就需要修改
     * 这里需要来源配置，结合输入确定数据分配
     * 按照聚合根的思路，实例化主对象，所有从对象也应该在内存中以保持数据一致性
     *
     * 对象关系定义配置，前端组件设置kv,objOrderProduct
     * 数据解包根据配置分拆到从对象属性中
     *
     * master->before  =>  detail->before
     * master->save => detail->save
     * master->after -> detail->after
     *
     */

    // 订单产品的数据仍然需要冗余在product_list承载,先抽出orderProductList的属性承载，减少product_list逻辑的使用
    // todo 审批同意后\common\library\approval_flow\wakeup\order\Update::processWakeup改造
    private $orderProductList;

    private $alibabaOrderProductList;

    private $ali_order_info;

    /** @var BatchOrderProduct */
    private $batchCreateOrderProduct;  //先尝试保存成对象是否构建有影响
    /** @var BatchOrderProduct */
    private $batchUpdateOrderProduct;
    /** @var BatchOrderProduct */
    private $batchDeleteOrderProduct;

    /** @var BatchAlibabaOrderProduct */
    private $batchCreateAliOrderProduct;
    /** @var BatchAlibabaOrderProduct */
    private $batchUpdateAliOrderProduct;
    /** @var BatchAlibabaOrderProduct */
    private $batchDeleteAliOrderProduct;


    use InitOrderMetadata;

    //todo trait待迁移

    private $statusService;
    private $fieldEditType; //标识字段编辑场景
    public $ali_task_id;

    //-------------以下属性先迁移过来 --------------
    protected $fillBuyer;
    protected $skipInsertCheckFlag;

    protected $createUniqueOrderNoFlag;

    protected $skipInitOrderStatus = false;

    protected $scene;

    protected $bindingApplyForm;

    public $referLinkStatus = false;

    protected $checkEditableFields = [];

    protected $changeOrderCollectionDateFlag = true;

    protected $orderEditPrivilege = PrivilegeConstants::PRIVILEGE_CRM_ORDER_EDIT;


    //-------------------------------------------
    private bool $skipManageable;


    public static function getMetadataClass()
    {
        return OrderMetadata::class;
    }

    public function formatExportInfo($userId)
    {
        $this->getFormatter()->formatExportInfoSetting();
        $this->getFormatter()->functionFieldInfoSetting();
        $this->getFormatter()->displayQuotationInfo(true);
        $this->getFormatter()->displayCashCollectionStatus(true);
        $this->getFormatter()->displayAlibabaOrderInfo(true);
        $this->getFormatter()->displayInquiryCollaborationInfo(true);
        $this->getFormatter()->setFormatterV2Privilege(false, true, PrivilegeFieldV2::SCENE_OF_EXPORT);
        $this->getFormatter()->displayExportFormatter(true, ['user_id' => $userId]);
        $attributes = $this->getAttributes();
        return $attributes;
    }

    public function __construct(int $clientId, int $order_id = null)
    {
        parent::__construct($clientId);
        if (!is_null($order_id)) {
            $this->load(['order_id' => $order_id]);
        }
    }

    public function loadByNo($orderNo)
    {
        $this->load(['client_id' => $this->_clientId, 'order_no' => $orderNo]);
        return $this;
    }


    public function getStatusService():InvoiceStatusService
    {
        if(is_null($this->statusService)) {
            $this->statusService = new InvoiceStatusService($this->getClientId(), \Constants::TYPE_ORDER);
        }
        return $this->statusService;
    }

    public function setFieldEditType($type)
    {
        $this->fieldEditType = $type;
    }

    public function getFieldEditType()
    {
        return $this->fieldEditType;
    }

    /**
     * @param mixed $ali_task_id
     */
    public function setAliTaskId($ali_task_id): void
    {
        $this->ali_task_id = $ali_task_id;
    }

    public function setSkipInsertCheckFlag($skipInsertCheckFlag)
    {
        $this->skipInsertCheckFlag = $skipInsertCheckFlag;
    }

    //当前只有阿里同步，可以考虑逻辑外移到调度层
    public function setCreateUniqueOrderNoFlag($createUniqueOrderNoFlag)
    {
        $this->createUniqueOrderNoFlag = $createUniqueOrderNoFlag;
    }
    public function setCheckEditableFields($checkEditableFields)
    {
        $this->checkEditableFields = $checkEditableFields;
    }
    public function getCheckEditableFields()
    {
        return $this->checkEditableFields;
    }

    public function setScene($scene): void
    {
        $this->scene = $scene;
        $this->getFormatter()->setScene($scene);
    }

    public function setOrderEditPrivilege(string $orderEditPrivilege): void
    {
        $this->orderEditPrivilege = $orderEditPrivilege;
    }

    /**
     * @param bool $skipInitOrderStatus
     */
    public function setSkipInitOrderStatus(bool $skipInitOrderStatus): void
    {
        $this->skipInitOrderStatus = $skipInitOrderStatus;
    }

    /**
     * @param mixed $fillBuyer
     */
    public function setFillBuyer(bool $fillBuyer): void
    {
        $this->fillBuyer = $fillBuyer;
    }

    public function setOrderProductList(array|null $orderProductList)
    {
        $this->orderProductList = $orderProductList;
    }

    /**
     * @param mixed $alibabaOrderProductList
     */
    public function setAlibabaOrderProductList($alibabaOrderProductList): void
    {
        $this->alibabaOrderProductList = $alibabaOrderProductList;
    }

    public function setAliStoreIdAttr($ali_store_id): void
    {
        $this->ali_store_id = $ali_store_id;
    }

    public function setAliAccountIdAttr($seller_account_id): void
    {
        $this->seller_account_id = $seller_account_id;
    }

    public function getOrderProductList()
    {
        return $this->orderProductList;
    }

    public function loadOrderProductList(): void
    {
        if ($this->isNew()) {
            throw new \RuntimeException("new object not support this method");
        }
        $filter = new OrderProductFilter($this->getClientId());
        $filter->selectAll();
        $filter->enable_flag = \Constants::ENABLE_FLAG_TRUE;;
        $filter->refer_id = $this->getObjectId();
        $filter->combine_record_id = 0;
        $filter->order('sort');
        $batchOrderProduct = $filter->find();
        $this->orderProductList = $batchOrderProduct->getAllAttributes();

        $aliProductFilter = new AlibabaOrderProductFilter($this->getClientId());
        $aliProductFilter->selectAll();
        $aliProductFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $aliProductFilter->order_id = $this->getObjectId();
        $batchAliOrderProduct = $aliProductFilter->find();
        $this->alibabaOrderProductList = $batchAliOrderProduct->getAllAttributes();
    }

    public function getWorkflowReferType()
    {
        return \Constants::TYPE_ORDER;
    }

    public function isExist()
    {
        return $this->enable_flag == 1 && $this->delete_flag == 0;
    }


    /**
     * 1 编号规则依赖对象属性
     * 2 编号生成器不能保证生成编号唯一，原因是编号可以由用户输入
     * @return string
     */
    public function buildNo():string
    {
        $generate = GenerateService::getInstance($this->getClientId(), \Constants::TYPE_ORDER);
        $generate->dataAdaptor($this);
        $orderNo = $generate->generate();

        if ($this->checkHasOrderNo($orderNo)) {
            \LogUtil::error("重复订单编号 {$orderNo}  {$this->getClientId()}  {$this->order_id} keysInfo:".json_encode($generate->getNumberGeneratorKey()));
            throw new \RuntimeException(\Yii::t('invoice', 'Duplicate Order Number,there is a problem with the custom rule Settings'));
        }
        return $orderNo;
    }

    /**
     * 订单编号查重
     * @param $orderNo
     * @return bool
     */
    public function checkHasOrderNo($orderNo):bool
    {
        try {
            $order = new  Order($this->getClientId());
            $order->load(['client_id' => $this->getClientId(), 'order_no' => $orderNo, 'enable_flag' => \Constants::ENABLE_FLAG_TRUE]);
            return !$order->isNew() && $this->getObjectId() != $order->getObjectId();
        } catch (\RuntimeException $exception) {
            return false;
        }
    }

    /**
     * 生成重复的则跳过
     * @return string
     */
    private function getUniqueOrderNo():string
    {
        $generate = GenerateService::getInstance($this->getClientId(), \Constants::TYPE_ORDER);
        $generate->dataAdaptor($this);
        $count = 0;
        do {
            $orderNo = $generate->generate();
            if ($this->checkHasOrderNo($orderNo))
            {
                \LogUtil::error("重复订单编号 {$orderNo}  ali_order_id: {$this->ali_order_id}  ali_store_id: {$this->ali_store_id}  client_id: {$this->client_id}  order_id: {$this->order_id} keysInfo:".json_encode($generate->getNumberGeneratorKey()));
                $orderNo = '';
            }

            $count++;
        } while (empty($orderNo) && $count < 10);

        if (empty($orderNo))
        {
            throw new \RuntimeException(\Yii::t('invoice', 'Duplicate Order Number,there is a problem with the custom rule Settings'));
        }

        return $orderNo;
    }


    /**
     * 维护user_id=>处理人,业绩归属人
     */
    public function refreshUserId()
    {
        //todo 抛异常还是兼容
        if(empty($this->users) ){
            $this->users = [];
        }

        if (empty($this->handler)) {
            $this->handler = [];
        }

        $users = [];
        foreach ($this->users as $performanceUser) {
            if (!empty($performanceUser['user_id'])) {
                $performanceUser['rate'] = $performanceUser['rate'] ?? 0;
                $users[] = $performanceUser;
            }
        }
        $this->users = array_values($users);

        $userIds = [];
        if( !empty($this->users) ){
            $userIds = array_merge($userIds,array_column($this->users,'user_id'));
        }

        if( !empty($this->handler)){
            $userIds = array_merge($userIds,$this->handler);
        }

        $this->user_id = array_values(array_unique($userIds));
    }

    //只有新建场景且未设置部门业绩归属的情况下会重新初始化部门
    public function initDepartments()
    {
        if ($this->isNew() && !empty($this->departments)) {
//            // 将department中的 'department_id' 转为 int 类型
//            $formatDepartments = [];
//            foreach($this->departments as $k => $singleDepartment){
//                $singleDepartment['department_id'] = intval($singleDepartment['department_id']);
//                $formatDepartments[$k] = $singleDepartment;
//            }
//            $this->departments = $formatDepartments;
            return;
        }
        $userRates = array_column($this->users ?? [], 'rate','user_id');
        $departments = [];
        if (!empty($userRates)) {
            $userDepartments = \common\library\performance\Helper::getUserPerformanceDepartment($this->getClientId(), array_keys($userRates));
            foreach ($userRates as $userId => $rate) {
                $departmentId = $userDepartments[$userId] ?? 0;
                if (isset($departments[$departmentId])) {
                    $departments[$departmentId]['rate'] += $rate;
                } else {
                    $departments[$departmentId] = [
                        'department_id' => intval($departmentId),
                        'rate' => $rate,
                    ];
                }
            }

        }
        $this->departments = array_values($departments);
    }


    public function getAlibabaPaymentLink()
    {
        if( !in_array($this->source_type, [OrderConstants::TYPE_ALI_ORDER, OrderConstants::TYPE_DIRECT_PAY_ORDER]))
        {
            return '';
        }

        return AlibabaOrderSyncHelper::getAlibabaPaymentLink($this->getClientId(), $this->ali_store_id, $this->ali_order_id);

    }


    public function setAliOrderInfo($ali_order_info)
    {
        $this->ali_order_info = $ali_order_info;
        $this->setAliStoreIdAttr($ali_order_info['ali_store_id']);
        $this->setAliAccountIdAttr($ali_order_info['seller_account_id']);
        $this->customer_address = $ali_order_info['shipping_address']['text'] ?? '';
        foreach ($ali_order_info['ali_order_list'] as &$ali_order) {
            $ali_order_product = $ali_order['product_list'] ?? [];
            $ali_order['product_list'] = $ali_order_product;
            $this->alibabaOrderProductList = array_merge($this->alibabaOrderProductList ?? [], $ali_order_product);
        }
    }

    //todo t1
    protected function syncRelationCompanyAndCustomerData()
    {
        $oldCompanyId  = $this->_oldAttributes['company_id'] ?? 0;
        $newCompanyId  = $this->company_id ?? 0;

        $oldCustomerId  = $this->_oldAttributes['customer_id'] ?? 0;
        $newCustomerId  = $this->customer_id ?? 0;

        if($oldCompanyId != $newCompanyId) {
            $company = new Company($this->getClientId(), $this->company_id);
            if($company){

                $this->company_name = $company->name;
                $this->company_phone = $company->tel_full;
                $this->company_fax = $company->fax;
                $this->company_address = $company->address;
                $this->company_group_id = $company->group_id;
            }else{
                $this->company_name = '';
                $this->company_phone = '';
                $this->company_fax = '';
                $this->company_address = '';
                $this->company_group_id = 0;
            }
        }

        if($oldCustomerId != $newCustomerId) {
            $customer = new Customer($this->getClientId(), $newCustomerId);
            if($customer){
                $this->customer_name = $customer->name;
                $this->customer_email = $customer->email;
                $fullTelList = array_map(function ($elem){
                    return $elem[0].' '.$elem[1];
                },$customer->tel_list??[]);
                if (!empty($customer->tel) || !empty($customer->tel_area_code))
                    $fullTelList[] = $customer->tel_area_code.' '.$customer->tel;
                $this->customer_phone = $fullTelList[0]??'';
            }else{
                $this->customer_name = '';
                $this->customer_email = '';
                $this->customer_phone = '';
            }
        }

    }

    //todo  p2 后续页面上的双汇率显示设置
    //依赖先设置currency  exchange_rate 存储表示cny汇率，前端在传递的时候不会区分
    public function refreshExchangeRate()
    {
        $exchangeRateService = new ExchangeRateService($this->getClientId());
        if (empty($this->exchange_rate)) {
            $mainCurrency = Client::getClient($this->getClientId())->getMainCurrency();
            if ($mainCurrency == $this->currency) {
                if ($mainCurrency == ExchangeRateService::USD) {
                    $this->exchange_rate_usd = 100;
                    $this->exchange_rate = $exchangeRateService->cnyRateForCurrency($this->currency);
                } else {
                    $this->exchange_rate = 100;
                    $this->exchange_rate_usd = $exchangeRateService->usdRateForCurrency($this->currency);
                }
            } else {
                $this->exchange_rate_usd = $exchangeRateService->usdRateForCurrency($this->currency);
                $this->exchange_rate = $exchangeRateService->cnyRateForCurrency($this->currency);
            }
        }
    }

    /**
     * 填充关联线索($this->source_lead_id),注意,该方法不会做save操作.如需save,请在后续逻辑中保证该值可以被save.否则需要做好异常处理.
     * 如果有关联商机,且已关联的商机有来源线索,则第一优先取商机的来源线索
     * 如果没有取到商机的来源线索(没有关联商机或者关联的商机没有线索),且关联的客户有来源线索,则取客户的来源线索
     * 如果没有取到商机和客户的来源线索,则取客户最近的关联线索
     */
    public function relateLead()
    {
        $opportunityId = $this->opportunity_id ?? 0;
        $companyId = $this->company_id ?? 0;
        $targetOpportunityId = empty($opportunityId) ? ($this->_oldAttributes['opportunity_id'] ?? 0) : $opportunityId;
        if (!empty($targetOpportunityId)) {
            $opportunity = new Opportunity($this->getClientId(), $targetOpportunityId);
            $opportunityMainLeadId = empty($opportunity->main_lead_id) ? 0 : $opportunity->main_lead_id;

            if (!empty($opportunityMainLeadId)) {
                $this->source_lead_id = $opportunityMainLeadId;
                return;
            }
        }

        $targetCompanyId = empty($companyId) ? ($this->_oldAttributes['company_id'] ?? 0): $companyId;
        if (empty($targetCompanyId)) return;
        $company = new Company($this->getClientId(), $targetCompanyId);
        $this->source_lead_id = empty($company->main_lead_id) ? \common\library\customer\Helper::getLatestRelateLead($this->getClientId(), $targetCompanyId) : $company->main_lead_id;
    }

    public function beforeCreate()
    {

        $this->handlePrivilegeFields();
        //判断用户是否有新建订单时候设定订单编号的权限
//        if (!$this->skipInsertCheckFlag && $this->order_no && !\common\library\privilege_v3\Helper::hasPermission($this->getClientId(), $this->getDomainHandler()->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_ORDER_EDIT_NO_CREATE)) {
//            throw new \RuntimeException(\Yii::t('invoice', 'No permission to edit order number'));
//        }

        // 订单号操作权限迁移到字段权限
        if (!$this->skipInsertCheckFlag && $this->skipPrivilegeField && !empty($this->order_no) ) {
            $fieldPrivilege = \common\library\privilege_v3\privilege_field\Helper::getToScopeUserFieldPrivileges($this->_clientId, $this->getDomainHandler()->getUserId(), $this->getDomainHandler()->getUserId(), $this->getPrivilegeFieldFunctionalId(), \common\library\privilege_v3\PrivilegeFieldV2::SCENE_OF_CREATE, $this->getPrivilegeFieldReferType());
            if (in_array('order_no', $fieldPrivilege['disable']) || in_array('order_no', $fieldPrivilege['readonly'])) {
                throw new \RuntimeException(\Yii::t('invoice', 'No permission to edit order number'));
            }
        }

        //todo 未知：订单创建权限检查？PRIVILEGE_CRM_ORDER_CREATE

        //初始化订单编号 &&  订单编号判重  以前是判断100次 这里需要优化
        if ($this->order_no) {

            //todo  p1  这里的逻辑要由字段校验规则和重复判断组件统一处理
            //限制编号的长度
            if (mb_strlen($this->order_no) > 100) {
                throw new \RuntimeException(\Yii::t('invoice', 'The order number must not exceed 100 characters'));
            }

            if ($this->checkHasOrderNo($this->order_no)) {
                throw new \RuntimeException(\Yii::t('invoice', 'Duplicate Order Number'));
            }
        } else {
            if($this->createUniqueOrderNoFlag) {
                $this->order_no = $this->getUniqueOrderNo()??"";
            }else{
                //只要是空的就生成新的编号
                $this->order_no = $this->buildNo();
            }
        }

        //todo  p1 填充买方信息逻辑 后续需要转为引用字段
        //当前方案 简单比较company_id 是否有变更，有变更同步下客户数据
        // 当前业务逻辑非引用字段，不能后端去覆盖客户、联系人的信息
//        $this->syncRelationCompanyAndCustomerData();

        //todo  p2 是否变更币种，全局币种统一逻辑，这里先兼容

        //初始化部门
        $this->initDepartments();
        $this->refreshExchangeRate();

        //初始化状态
        if (!$this->skipInitOrderStatus &&
            ($this->fieldEditType != OrderConstants::FIELD_EDIT_TYPE_BY_ALIBABA_ORDER_SYNC || !$this->status)
        ) {
            $beginStatus = $this->getStatusService()->beginStatus();
            $this->status = $beginStatus['id'];
        }

        //todo  p0 默认值初始化，依赖字段服务
        $time = xm_function_now();
        $this->create_user = $this->create_user ?? $this->getDomainHandler()->getUserId();
        $this->update_user = $this->update_user ?? $this->getDomainHandler()->getUserId();
        $this->create_time = $this->update_time = $time;
        $this->enable_flag = 1;
        $this->delete_flag = 0;
        $this->account_flag = 0;
        $this->account_date = !empty($this->account_date) ? $this->account_date : '1970-01-01 08:00:00';
        $this->approval_status = -1; //todo  p2  审批状态字段是否废弃
        $this->last_order_status_update_time = $time;
        $this->link_status = $this->link_status ?? OrderLink::make()->toArray();
        $this->tax_refund_type = $this->tax_refund_type ?? 0;


        /* 与refreshTotal重复了
        $this->product_total_amount = floatval($this->product_total_amount ?? 0.0);
        $this->addition_cost_amount = floatval($this->addition_cost_amount ?? 0.0);

        //todo p2 订单amount 应该由后端重新计算
        $this->amount = $this->product_total_amount + $this->addition_cost_amount;
        $this->amount = floatval($this->amount ?? 0.0);
        $this->amount_rmb = $this->amount * ($this->exchange_rate / 100);
        $this->amount_usd = $this->amount * $this->exchange_rate_usd / 100;
        */
        $this->refreshTotal();

        //填充关联线索  具体的业务，无法通过关联字段处理
        // 存在部分版本没有商机模块
        $this->opportunity_id = $this->opportunity_id ?? 0;
        $this->relateLead();


        //重新计算user_id 具体业务
        $this->refreshUserId();


        $this->file_list = $this->file_list ?? [];

        $this->refreshCostList();




        //todo  关联字段
        //todo   公式字段
        //todo   统计字段

        //todo  字段权限
        /**
         * 字段分为 读写 要解决关联字段权限的问题
         * 不可读 ， 产品单价不可读，但是产品总价可读，变更产品数量，产品总价如何处理
         * 不可写，不接受用户输入，但是通过计算的字段属于业务逻辑，不受限制
         * 所以字段权限做到业务网关层?
         */

        $this->prepareData();

        $this->calculateFunctionalFields();
        $this->refreshProductList();
        $this->refreshAlibabaProductList();
        $this->refreshTotal();

        //todo 差异比较

        /**
         * 输入格式化
         * 输出格式化
         * 1 数据库的null
         * 2 1970-01-01
         * 3 数据库存了时间戳，时分秒， 业务含义只有日期
         * 原则是 实例化到业务对象的值一定是有业务含义的
         */
        parent::beforeCreate();
        return true;
    }

    //todo  p2 异步队列
    public function afterCreate()
    {
        parent::afterCreate();

        $setting = new OrderSetting();
        $setting->setExtraCompareFields($setting->getCustomFieldId());

        $historyBuilder = new Builder($setting);
        if ($this->fieldEditType) {
            $historyBuilder->setUpdateType($this->fieldEditType);
        }

        $historyType = ($this->fieldEditType == OrderConstants::FIELD_EDIT_TYPE_BY_ALIBABA_ORDER_SYNC) ? OrderSetting::TYPE_ALIBABA_SYNC : OrderSetting::TYPE_CREATE_ORDER;
        $historyBuilder->setClientId($this->_clientId)->setType($historyType)
            ->setUpdateUser($this->getDomainHandler()->getUserId())
            ->initFromRawData($setting->sortAttributes($this->getRawAttributes()))
            ->build();

        $this->historyField();

        if (!is_null($this->orderProductList) && !is_null($this->batchCreateOrderProduct)) {
            $this->batchCreateOrderProduct->getOperator()->create();
        }

        if (!is_null($this->alibabaOrderProductList) && !is_null($this->batchCreateAliOrderProduct)) {
            $this->batchCreateAliOrderProduct->getOperator()->create();
        }

        $this->transferAliOrder();
        //执行审批流
        $this->triggerCreateApproval();

        //动态
        $this->trail(TrailConstants::TYPE_ORDER_ADD);
        $this->updateCompanyOrderTime();
        $this->feed(TrailConstants::TYPE_ORDER_ADD);

        //刷新订单利润
        $this->referOrderProfit(OrderConstants::EVENT_CREATE);

        // 消息通知
        $this->notify(OrderConstants::EVENT_CREATE);

        $this->triggerQuotation();
        //更新回款状态数据
        $this->updateCashCollectionStatus();

        //触发工作流
        $this->runWorkflow($this->getClientId(),$this->getObjectId());

        //触发上游工作流
        $this->runRelateWorkflow($this->getClientId());


        //更新es
        $this->handleEsIndexV7();
        //cms询盘  todo 逻辑确认
        $this->updateInquiryStatus();

        //阿里订单同步环节状态更新
        $this->aliOrderSyncReferLinkStatus();

        if ($this->tax_refund_type != OmsConstant::ORDER_IS_TAX_REFUND)
        {
            //销售订单利润埋点：退税金额
            $trigger = TaxRefundAmountTrigger::make($this->client_id);
            $trigger->addOrderIds($this->order_id);
            $trigger->trigger();
        }

        $this->runPerformanceRecord();

    }

    public function beforeUpdate()
    {
        //todo 审批数据合并

        //todo 数据处理
        $nowTime = xm_function_now();
        $this->update_time = $nowTime;

        if (!$this->fieldEditType) {
            $this->update_user = $this->getDomainHandler()->getUserId();
        }


        if (empty($this->account_date)) {
            $this->account_date = '1970-01-01 08:00:00';
        }
        $this->refreshUserId();

        //todo 扩展字段赋值

        //todo 控制标识处理

        //todo 环节状态刷新
        //todo 阿里配置更新

        //todo 字段权限

        //todo 订单锁定

        //todo  订单编号权限修改校验

        //todo 编号处理
        if (mb_strlen($this->order_no) > 100) {
            throw new \RuntimeException(\Yii::t('invoice', 'The order number must not exceed 100 characters'));
        }

        if ($this->checkHasOrderNo($this->order_no)) {
            throw new \RuntimeException(\Yii::t('invoice', 'Duplicate Order Number'));
        }

        // 订单号操作权限迁移到字段权限
        if (!$this->skipPrivilegeField && $this->order_no != $this->_oldAttributes['order_no'] && !$this->getStatusService()->isEndingStatus($this->status)) {
            $fieldPrivilege = \common\library\privilege_v3\privilege_field\Helper::getToScopeUserFieldPrivileges($this->_clientId, $this->getDomainHandler()->getUserId(), $this->scope_user_ids, $this->getPrivilegeFieldFunctionalId(), \common\library\privilege_v3\PrivilegeFieldV2::SCENE_OF_UPDATE, $this->getPrivilegeFieldReferType());
            if (in_array('order_no', $fieldPrivilege['disable']) || in_array('order_no', $fieldPrivilege['readonly'])) {
                throw new \RuntimeException(\Yii::t('invoice', 'No permission to edit order number'));
            }
        }



        $this->refreshCostList();
        //todo  填充买方信息逻辑 == 对字段建立引用关系

        //todo 小计字段重算
        //todo  关联字段
        //todo   公式字段
        //todo   统计字段
        //todo  销售订单处理
        //todo  填充关联线索

        //todo 差异比较
        //todo 审批流触发
        $this->calculateFunctionalFields();
        $this->refreshProductList();
        $this->refreshAlibabaProductList();
        $this->refreshTotal(); //维护单据总价
        $this->product_total_amount_rmb = $this->product_total_amount * ($this->exchange_rate / 100);
        $this->product_total_amount_usd = $this->product_total_amount * $this->exchange_rate_usd / 100;

        $wakeParams = !is_null($this->ali_order_info) ? [
            'ali_order_info' => $this->ali_order_info
        ] : [];
        $this->getOperator()->triggerEditApproval($wakeParams, $this->getRawAttributes(), $this->getOldAttributes());

        return parent::beforeUpdate();
    }

    public function afterUpdate()
    {
        $this->historyField();

        if (!is_null($this->orderProductList)) {
            if (!is_null($this->batchCreateOrderProduct)) {
                $this->batchCreateOrderProduct->getOperator()->create();
            }
            if (!is_null($this->batchUpdateOrderProduct)) {
                $this->batchUpdateOrderProduct->getOperator()->update();
            }
            if (!is_null($this->batchDeleteOrderProduct)) {
                $this->batchDeleteOrderProduct->getOperator()->delete();
            }
        }

        if (!is_null($this->alibabaOrderProductList)) {
            if (!is_null($this->batchCreateAliOrderProduct)) {
                $this->batchCreateAliOrderProduct->getOperator()->create();
            }
            if (!is_null($this->batchUpdateAliOrderProduct)) {
                $this->batchUpdateAliOrderProduct->getOperator()->update();
            }
            if (!is_null($this->batchDeleteAliOrderProduct)) {
                $this->batchDeleteAliOrderProduct->getOperator()->remove();
            }
        }

        parent::afterUpdate();

        //todo 操作历史
        //todo 动态


        //todo 刷新订单利润
        $this->referOrderProfit(OrderConstants::EVENT_UPDATE);
        //todo 消息通知
        $this->notify(OrderConstants::EVENT_UPDATE);
        //todo 更新回款单数据
        list($stat, $updateFields) = \common\library\cash_collection\Helper::updateStatsAmountByRefer($this->getClientId(), CashCollection::REFER_TYPE_ORDER, $this->getObjectId(), [
            'amount' => $this->amount,
            'amount_rmb' => $this->amount_rmb,
            'amount_usd' => $this->amount_usd,
        ], $this->changeOrderCollectionDateFlag);

        $this->additionAttributeChangeFields = $updateFields;
        //todo 触发工作流
        $this->runWorkflow($this->getClientId(), $this->getObjectId());
        //todo 触发上游工作流
        $this->runRelateWorkflow($this->getClientId());

        //todo 更新es
        $this->handleEsIndexV7();
        //todo  cms询盘
        $this->updateInquiryStatus();
        //todo 阿里订单同步环节状态更新
        $this->aliOrderSyncReferLinkStatus();
        //todo 刷新环节状态
        $this->judgmentReferLinkStatus();
        $this->referLinkStatus();
        //todo 下游任务
//        $this->updateTransferInvoice($diffData);
        //todo 回款单

        $trigger = TaxRefundAmountTrigger::make($this->getClientId());
        $trigger->addOrderIds($this->getObjectId());
        $trigger->trigger();
        //todo  业绩计算
        $this->runPerformanceRecord();

        $this->transferAliOrder();
    }

    public function afterDelete()
    {
        $domainHandler = $this->getDomainHandler();

        $orderProductFilter = new OrderProductFilter($domainHandler->getClientId());
        $orderProductFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $orderProductFilter->refer_id = $this->getObjectId();
        $orderProductFilter->combine_record_id = 0;
        $orderProductFilter->selectAll();
        $orderProduct = $orderProductFilter->find();
        $deleteRecordIds = array_column($orderProduct->getAllAttributes(),'id');
        $orderProduct->setOrder($this);
        $orderProduct->getOperator()->delete();

        $aliOrderProductFilter = new AlibabaOrderProductFilter($domainHandler->getClientId());
        $aliOrderProductFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $aliOrderProductFilter->order_id = $this->getObjectId();
        $aliOrderProductFilter->selectAll();
        $aliOrderProduct = $aliOrderProductFilter->find();
        $aliOrderProduct->setOrder($this);
        $aliOrderProduct->getOperator()->delete();

        \common\library\recycle\API::add($domainHandler->getClientId(), $domainHandler->getUserId(), \common\library\recycle\Recycle::ORDER, [$this->getObjectId()=>$deleteRecordIds],true);


        $this->trail(\common\components\BaseObject::EVENT_AFTER_DELETE);
        $this->notify(\common\components\BaseObject::EVENT_AFTER_DELETE);
        $this->referOrderProfit(\common\components\BaseObject::EVENT_AFTER_DELETE);

        $this->runRelateWorkflow($this->client_id);
        $this->runPerformanceRecord();
    }

    //变更关联商机
    public function changeOpportunity($opportunity_id){}

    //todo p2  动态逻辑梳理
    /**
     * 1 动态数据存储了当前业务对象的所有属性？
     * 2 模型改造
     * @param $nodeType
     * @return void
     */
    public function trail($nodeType)
    {
        if(!empty($this->company_id)) {
            try {
                $trail = new InvoiceEvents();
                $trail->setType($nodeType);
                $trail->setClientId($this->getClientId());
                $trail->setCreateUser($this->getDomainHandler()->getUserId());
                $trail->setCompanyId($this->company_id);
                $trail->setReferId($this->getObjectId());
                $trail->setUserId($this->getDomainHandler()->getUserId());
                $data = $this->getRawAttributes();
                $trail->setData($data);
                $trail->run();
            } catch (\RuntimeException $e) {
                \LogUtil::info("单据动态生成失败，type={$nodeType} order_id={$this->order_id} " . $e->getMessage());
            }
        }
    }

    public function feed($nodeType)
    {
        $feed = new \Feed();
        $feed->setClientId($this->getClientId());
        $feed->setUserId($this->getDomainHandler()->getUserId());
        $feed->setCreateUser($this->getDomainHandler()->getUserId());
        $feed->setNodeType($nodeType);
        $feed->setData($this->getRawAttributes());
        $feed->setReferId($this->getObjectId());
        $feed->save();
    }

    public function updateCompanyOrderTime()
    {
        if(!empty($this->company_id)) {
            try {
                // 出现从库报 ICU is not supported in this build. 影响测试测试了，先屏蔽错误
                \common\library\customer\Helper::updateOrderTime($this->getClientId(), $this->getDomainHandler()->getUserId(), $this->company_id, $this->customer_id ?? null, 'refer_modified_order');
            } catch (\Throwable $t) {
                \LogUtil::error('updateCompanyOrderTime_error', [$t->getMessage()]);
            }
        }
    }

    public function referOrderProfit($eventType)
    {
        switch ($eventType) {
            case OrderConstants::EVENT_CREATE:
                //销售订单利润埋点：创建订单利润记录
                (new OrderProfitApi($this->getClientId()))->createByOrderId($this->getObjectId());
                break;
            case OrderConstants::EVENT_UPDATE:
                //销售订单利润埋点：修改订单金额，更新利润
                if (floatval($this->getOldAttributes('amount_rmb')) != $this->amount_rmb || floatval($this->getOldAttributes('amount_usd')) != $this->amount_usd) {
                    $this->referOrderProfitFactor($this->_clientId, $this->getObjectId(), OrderProfitConstant::FACTOR_TYPE_OF_ORDER_AMOUNT);
                }

                if (floatval($this->getOldAttributes('tax_refund_type')) != $this->tax_refund_type) {
                    //销售订单利润埋点：修改退税类型，更新利润
                    $taxRefundAmountTrigger = TaxRefundAmountTrigger::make($this->_clientId);
                    $taxRefundAmountTrigger->addOrderIds($this->getObjectId());
                }

                //销售订单利润埋点
                //1.判断是否有公式配置, 返回有公式的公式配置
                //2.附加费用总金额、产品总金额、附加费用项等因子变更, 更新公式字段
                $oldCostList = [];
                $newCostList = [];
                $costListField = [];
                foreach ($this->getOldAttributes('cost_list') as $oldCost) {
                    if (isset($oldCost['cost_item_relation_id']) && !empty($oldCost['cost_item_relation_id'])) {
                        $costListField[] = \Constants::TYPE_ORDER.".cost_list.".$oldCost['cost_item_relation_id'];
                        isset($oldCostList[$oldCost['cost_item_relation_id']]) ? $oldCostList[$oldCost['cost_item_relation_id']] += $oldCost['cost'] : $oldCostList[$oldCost['cost_item_relation_id']] = $oldCost['cost'];
                    }
                }
                foreach ($this->cost_list as $newCost) {
                    if (isset($newCost['cost_item_relation_id']) && !empty($newCost['cost_item_relation_id'])) {
                        $costListField[] = \Constants::TYPE_ORDER.".cost_list.".$newCost['cost_item_relation_id'];
                        isset($newCostList[$newCost['cost_item_relation_id']]) ? $newCostList[$newCost['cost_item_relation_id']] += $newCost['cost'] : $newCostList[$newCost['cost_item_relation_id']] = $newCost['cost'];
                    }
                }

                $formulaField = array_unique(array_merge([\Constants::TYPE_ORDER.".addition_cost_amount_rmb", \Constants::TYPE_ORDER.".cost_with_tax_total_rmb", \Constants::TYPE_ORDER.".product_total_amount_rmb"], $costListField));
                $profitFormulaApi = new \common\library\setting\library\order_profit\ProfitFormulaApi($this->client_id);
                $formulaSetting = $profitFormulaApi->getProfitFormulaSetting([], $formulaField);

                if (
                    $formulaSetting
                    &&
                    (  round(floatval($this->getOldAttributes('addition_cost_amount')) * $this->getOldAttributes('exchange_rate'), 4)  != round($this->addition_cost_amount * $this->exchange_rate, 4)
                        ||
                        round(floatval($this->getOldAttributes('cost_with_tax_total')) * $this->getOldAttributes('exchange_rate'), 4)  != round($this->cost_with_tax_total * $this->exchange_rate, 4)
                        ||
                        round(floatval($this->getOldAttributes('product_total_amount_rmb')),4) != round(floatval($this->product_total_amount_rmb), 4)
                        ||
                        $oldCostList != $newCostList
                    )
                )
                {
                    $this->referOrderProfitFactor($this->_clientId, $this->getObjectId(), OrderProfitConstant::FACTOR_TYPE_OF_FORMULA_FIELD);
                }

                break;
            case OrderConstants::EVENT_DELETE:
                (new OrderProfitApi($this->_clientId))->deleteByOrderId($this->getObjectId());
                break;
            case OrderConstants::EVENT_RECOVER:
                (new OrderProfitApi($this->_clientId))->recoverByOrderId($this->getObjectId());
                break;
            default:
                break;
        }
    }

    //todo  p2 异步处理
    public function notifyDownstreamInvoice($eventType)
    {
        if(!in_array($eventType, [OrderConstants::EVENT_CREATE,OrderConstants::EVENT_STATUS]) ) {

            $types =  [\Constants::TYPE_PRODUCT_TRANSFER_PURCHASE, \Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND, \Constants::TYPE_PRODUCT_TRANSFER_OTHER];
            $upstreamInvoice = [['refer_id'=>$this->getObjectId(), 'serial_id'=>$this->order_no]];
            (new ProductTransferHelper())->upstreamInvoiceChangeNotify($this->getClientId(), $upstreamInvoice, $types);

        }
    }

    public function notify($eventType)
    {
        $notify = new OrderNotify($this->getDomainHandler()->getUserId());

        if($eventType  == OrderConstants::EVENT_DELETE){
            $notify->sendDeleteMsg($this->getRawAttributes());
            return ;
        }

        $list = $notify->compare($this->_oldAttributes,$this->_attributes);

        if( !empty($list) ){
            $notify->send($list);
        }

    }

    public function updateCashCollectionStatus()
    {
        list($stat, $updateFields) = \common\library\cash_collection\Helper::updateStatsAmountByRefer($this->_clientId, CashCollection::REFER_TYPE_ORDER, $this->getObjectId(), [
            'amount' => $this->amount,
            'amount_rmb' => $this->amount_rmb,
            'amount_usd' => $this->amount_usd,
        ], $this->changeOrderCollectionDateFlag);
        //todo p3 changeOrderCollectionDateFlag这里的逻辑需要优化


        //todo  p3 需要优化 这里耦合了工作流字段变更逻辑
        $this->additionAttributeChangeFields = $updateFields;
    }

    /**
     * Es v7 handle index
     */
    protected function handleEsIndexV7()
    {
        $operateType = \Constants::ORDER_INDEX_TYPE_UPDATE;
        if($this->isNew()) {
            $operateType = \Constants::ORDER_INDEX_TYPE_CREATE;
        }
        \common\library\server\es_search\SearchQueueService::pushOrderQueue($this->getDomainHandler()->getUserId(), $this->_clientId, [$this->getObjectId()], $operateType);
    }


    //cms询盘状态数据同步 todo 确认逻辑是否下线
    public function updateInquiryStatus(){
        if ($this->source_lead_id > 0 && ($this->isNew() || $this->source_lead_id !== $this->_oldAttributes['source_lead_id']))
        {
            $inquiryObj = new Inquiry($this->getClientId());
            $inquiryObj->loadByLeadId($this->source_lead_id);
            if (!$inquiryObj->isNew()) {
                $inquiryObj->setInquiryStatus($this->status, InquiryService::INQUIRY_STATUS_STAGE_ORDER);
                $inquiryObj->save();
            }
        }
    }


    public function aliOrderSyncReferLinkStatus()
    {
        if($this->fieldEditType != OrderConstants::FIELD_EDIT_TYPE_BY_ALIBABA_ORDER_SYNC) {
            return false;
        }
        if($this->isNew() || $this->status != $this->_oldAttributes['status']) {
            $endStatus = (new InvoiceStatusService($this->getClientId(), \Constants::TYPE_ORDER))->endStatus();
            $endStatusIds = array_column($endStatus,'id');
            if(in_array($this->status,$endStatusIds)){
                $linkTrigger = OrderLinkStartDoneTrigger::make($this->getClientId());
                $linkTrigger->addOrderIds($this->getObjectId());
                $linkTrigger->trigger();
            }else{
                $linkTrigger = CashCollectionTrigger::make($this->getClientId());
                $linkTrigger->addOrderIds($this->getObjectId());
                $linkTrigger->trigger();
            }
        }
    }

    //todo  判断从订单产品是否有变更
    public function judgmentReferLinkStatus()
    {
        // 如果用户是oms用户而且用户编辑了订单明细数量，则需要触发环节状态重算
        if(PrivilegeService::getInstance($this->getClientId())->hasFunctional(PrivilegeConstants::FUNCTIONAL_PURCHASE_ORDER)){
            $newProducts = $this->product_list;
            $oldProducts = array_column($this->getOldAttributes()['product_list'] ?? [], null, 'unique_id');    // 旧产品肯定有unique_id，新产品则没有unique_id
            if(count($newProducts) != count($oldProducts)){
                $this->referLinkStatus = true;
            }else{
                foreach($newProducts as $newProduct){
                    if(empty($newProduct['unique_id']) || !isset($oldProducts[$newProduct['unique_id']]) || $newProduct['count'] != $oldProducts[$newProduct['unique_id']]['count']){
                        $this->referLinkStatus = true;
                        break;
                    }
                }
            }
        }
    }


    public function referLinkStatus()
    {
        if(!$this->referLinkStatus) return false;

        $linkTrigger = OrderLinkStartDoneTrigger::make($this->getClientId());
        $linkTrigger->addOrderIds($this->getObjectId());
        $linkTrigger->trigger();
    }

    public function updateTransferInvoice($diffData){
        if(!$diffData){
            return;
        }
        $transferParams = [
            'refer_id' => [$this->getObjectId()],
            'refer_type' => \Constants::TYPE_ORDER,
//            'history_ids' => $diffData,
        ];
        $this->upstreamRelateUpdateTransfer($this->getClientId(), $this->getDomainHandler()->getUserId(),  $transferParams, $diffData);
    }

    /**
     * 修改回款单的客户字段信息
     * 回款单展示了关联的订单的客户 部门 负责人 商机等信息，订单变更的时候需要展示
     * todo p1 需要转化为引用字段的逻辑
     *
     */
    public function changeInfoByCashCollection($eventType,$diffData)
    {
        if ($eventType != OrderConstants::EVENT_UPDATE || empty($diffData)) {
            return false;
        }

        $changeFieldInfo = [];
        $diffField = array_column($diffData, 'id');

        //有可能是复合diff，例如 users、departments、handler等等的变更 需要进行特殊处理
        foreach ($diffData as $datum) {
            is_array($datum) && $diffField = array_merge($diffField, array_column($datum, 'id'));
        }

        if (in_array('company_id', $diffField)) {
            $changeFieldInfo['company_id'] = $this->company_id;
        }

        if (in_array('departments', $diffField)) {
            $changeFieldInfo['departments'] = $this->departments;
            $changeFieldInfo['department_id'] = array_column($this->departments, 'department_id');
        }
        if (in_array('users', $diffField)) {
            $changeFieldInfo['users'] = $this->users;
            $changeFieldInfo['user_id'] = array_column($this->users ?? [], 'user_id');
        }

        if (in_array('opportunity_id', $diffField)) {
            $changeFieldInfo['opportunity_id'] = $this->opportunity_id;
        }

        (new CashCollectionBatchOperator($this->getDomainHandler()->getUserId()))->batchChangeInfoByOrderId($this->order_id, $changeFieldInfo,$this->fieldEditType);
        // 刷新单据的回款统计表的回款字段
        if (in_array('opportunity_id', $diffField)) {
            \common\library\cash_collection\Helper::refreshCashCollectionStats($this->client_id, \common\library\cash_collection\CashCollection::REFER_TYPE_OPPORTUNITY, $this->opportunity_id, false);
        }
        \LogUtil::info("change_order_info_to_cash_collection:order[{$this->order_id}] info:[" . json_encode($changeFieldInfo, JSON_UNESCAPED_UNICODE) . "]");
    }


    public function changeExchangeRateByCashCollection($eventType,$diffData)
    {
        if ($eventType != OrderConstants::EVENT_UPDATE) {
            return false;
        }

        if (empty($diffData)) {
            return false;
        }

        $diffField = array_keys($diffData);
        if (
            array_intersect(['exchange_rate_usd', 'currency', 'exchange_rate'], $diffField)
        ) {
            $user = User::getLoginUser();
            $userId = $user->getUserId();

            $pgDb = PgActiveRecord::getDbByClientId($this->client_id);
            $select_sql = "SELECT cash_collection_id FROM tbl_cash_collection WHERE client_id={$this->client_id} and order_id = {$this->order_id} and enable_flag = 3";
            $list = $pgDb->createCommand($select_sql)->queryAll();
            $cash_collection_ids = Arr::uniqueFilterValues(array_column($list, 'cash_collection_id'));
            if (empty($cash_collection_ids)) {
                return false;
            }

            $operator = new CashCollectionBatchOperator($userId);
            $data = [
                "exchange_rate" => $this->exchange_rate,
                "exchange_rate_usd" => $this->exchange_rate_usd,
                "currency" => $this->currency,
            ];
            if (empty($this->exchange_rate) || empty($this->exchange_rate_usd) || empty($this->currency)){
                return false;
            }
            $operator->batchUpdateByData($cash_collection_ids, $data);
        }
        return true;
    }



    public function refreshProductList()
    {
        if (!is_null($this->orderProductList)) {
            $oldOrderProductIds = [];
            if (!$this->isNew()) {
                $filter = new OrderProductFilter($this->_clientId);
                $filter->enable_flag = 1;
                $filter->refer_id = $this->getObjectId();
                $filter->combine_record_id = 0;
                $filter->select(['id']);
                $oldOrderProductIds = array_column($filter->rawData(), 'id');
            }

            $partsProductMap = [];
            $isNewUniqueIds = [];
            //提前加载unique_id，并且提取主配信息

            $sort = 1;
            foreach ($this->orderProductList as &$orderProduct) {
                $orderProduct['sort'] = $sort;
                $sort++;
                $id = ($orderProduct['unique_id'] ?? $orderProduct['id']) ?? 0;
                if (!in_array($id, $oldOrderProductIds)) {
                    $orderProduct['id'] = $orderProduct['unique_id'] = \ProjectActiveRecord::produceAutoIncrementId();
                    $isNewUniqueIds[] = $orderProduct['unique_id'];
                }
                if (!empty($orderProduct['is_master_product'] ?? 0) && !empty($orderProduct['master_group_id'] ?? 0)) {
                    $partsProductMap[$orderProduct['master_group_id']] = $orderProduct['unique_id'];
                }
            }
            unset($orderProduct);

            $newOrderProduct = [];
            $updateOrderProduct = [];
            foreach ($this->orderProductList as &$orderProduct) {
                if (!$this->isNew()) {
                    $orderProduct['order_id'] = $this->getObjectId();
                }

                if ((isset($orderProduct['is_master_product']) && empty($orderProduct['is_master_product']))
                    && !empty($orderProduct['master_group_id'] ?? 0)) {
                    $orderProduct['master_id'] = $partsProductMap[$orderProduct['master_group_id']] ?? 0;
                }

                if (in_array($orderProduct['unique_id'], $isNewUniqueIds)) {
                    $newOrderProduct[] = $orderProduct;
                } else {
                    $updateOrderProduct[] = $orderProduct;
                }
            }
            unset($orderProduct);

//            $baseOrderProductColumns = (new OrderProductMetadata())->getMappedAttributeKeys();
            if (!empty($newOrderProduct)) {
                $batchOrderProduct = new BatchOrderProduct($this->getClientId());
                $batchOrderProduct->initFromData($newOrderProduct);
                $batchOrderProduct->setOrder($this);
                $batchOrderProduct->beforeCreate();
                // 回写保持order->product_list一致
                $batchCreateProductDataMap = array_column($batchOrderProduct->getRawAttributes(), null, 'id');
                foreach ($newOrderProduct as &$product) {
                    $createData = $batchCreateProductDataMap[$product['unique_id']] ?? [];
                    foreach ($createData as $attribute => $value) {
                        $product[$attribute] = $value;
                    }
                }
                unset($product);
                $this->batchCreateOrderProduct = $batchOrderProduct;
            }

            if (!empty($updateOrderProduct)) {
                //todo 一般是new filter类去实例batch对象，但是batch类批量操作能力弱，先用表单方式去处理
                $batchOrderProduct = new BatchOrderProduct($this->getClientId());
                $batchOrderProduct->initFromData($updateOrderProduct);
                $batchOrderProduct->setOrder($this);
                $batchOrderProduct->beforeUpdate();
                $batchUpdateProductDataMap = array_column($batchOrderProduct->getRawAttributes(), null, 'id');
                foreach ($updateOrderProduct as &$product) {
                    $updateData = $batchUpdateProductDataMap[$product['unique_id']] ?? [];
                    foreach ($updateData as $attribute => $value) {
                        $product[$attribute] = $value;
                    }
                }
                unset($product);
                $this->batchUpdateOrderProduct = $batchOrderProduct;
            }

            //todo 先回写product_list,product_list存在需要场景引用，需要回写进行维护, unique_id需要额外维护
            $this->product_list = array_merge($newOrderProduct, $updateOrderProduct);

            $deleteOrderProduct = array_diff($oldOrderProductIds, array_column($this->product_list, 'unique_id'));
            if (!empty($deleteOrderProduct)) {
                $filter = new OrderProductFilter($this->_clientId);
                $filter->id = $deleteOrderProduct;
                $batchOrderProduct = new BatchOrderProduct($this->_clientId, $filter);
                $batchOrderProduct->setOrder($this);
                $batchOrderProduct->beforeDelete();
                $this->batchDeleteOrderProduct = $batchOrderProduct;
            }
        }

        $orderGrossMargin = 0;
        $costWithTaxTotal = 0;
        $product_total_amount = $product_total_count = $package_volume_amount = $package_gross_weight_amount = $parts_total_count = $product_total_count_no_parts = 0;

        $productList = $this->product_list;
        foreach ($productList as &$product) {
            $product_total_amount += $product['cost_amount'];
            $product_total_count += $product['count'];
            $package_volume_amount += !empty($product['package_volume_subtotal']) ? $product['package_volume_subtotal'] : 0;
            $package_gross_weight_amount += !empty($product['package_gross_weight_subtotal']) ? $product['package_gross_weight_subtotal'] : 0;
            $orderGrossMargin += ($product['gross_margin']?:0) * $product['count'];
            $costWithTaxTotal += ($product['cost_with_tax']?:0) * $product['count'];

            //这两个字段是为了兼容灰度期间写入的问题
            if (empty($product['master_id'])) {
                $product['master_id'] = '';
            }
            if (empty($product['is_master_product']) && empty($product['master_id'])) {
                $product['master_group_id'] = '';
            }
            if (empty($product['is_master_product']) && !empty($product['master_group_id'])) {
                $parts_total_count += $product['count'];
            }
        }
        \ArrayUtil::multisort($productList, 'sort');
        $this->product_list = $productList;
        unset($product, $productList);

        //由前端计算转成后端处理
        $this->product_total_amount = $product_total_amount;
        $this->product_total_amount_rmb = $product_total_amount * ($this->exchange_rate / 100);
        $this->product_total_amount_usd = $product_total_amount * $this->exchange_rate_usd / 100;

        $this->product_total_count = $product_total_count;
        $this->package_volume_amount = $package_volume_amount;
        $this->package_gross_weight_amount = $package_gross_weight_amount;
        $this->parts_total_count = $parts_total_count;
        $this->product_total_count_no_parts = $product_total_count - $parts_total_count;

        $this->order_gross_margin = $orderGrossMargin;
        $this->order_gross_margin_cny = $orderGrossMargin * ($this->exchange_rate / 100);
        $this->order_gross_margin_usd = $orderGrossMargin * ($this->exchange_rate_usd / 100);
        $this->cost_with_tax_total = $costWithTaxTotal;
    }

    public function refreshCostList()
    {
        if (is_null($this->cost_list)) {
            return;
        }
        $additionCostAmount = 0;
        $fieldSetting = $this->getFieldTransfer()->getFieldMap($this->getMetadata()::objectName());

        $costList = $this->cost_list;
        foreach ($costList as &$item) {
            foreach ($item as $externalField => &$externalFieldValue) {
                if ($externalField == 'percent_amount') {
                    $externalField = 'percent_of_total_amount';
                }
                if (isset($fieldSetting[$externalField])) {
                    $fieldHandler = $this->getFieldHandler($fieldSetting[$externalField]);
                    !is_null($fieldHandler) && $externalFieldValue = $fieldHandler->handler($externalFieldValue);
                }
            }
            unset($externalFieldValue);
            $additionCostAmount += $item['cost'];
        }
        unset($item);
        $this->cost_list = $costList;

        $this->addition_cost_amount = $additionCostAmount;
        $this->addition_cost_amount_rmb = floatval(($this->addition_cost_amount ?? 0)) * ($this->exchange_rate / 100);
        $this->addition_cost_amount_usd = floatval(($this->addition_cost_amount ?? 0)) * ($this->exchange_rate_usd / 100);
    }

    /*
     * $this->orderProductList定义是operator层传入的订单产品的数据
     */
    public function calculateFunctionalFields()
    {
        FunctionFieldService::specialFunctionFieldHandle($this->_attributes, $this->_clientId, ObjConstant::OBJ_ORDER);

        $syncCalculator = new \common\library\object\field\updator\calculator\SyncCalculator($this->_clientId, 'objOrder');
        $syncCalculator->setMainData($this->_attributes);    // 设置主对象数据

        if (!is_null($this->orderProductList)) {
            //临时兼容，这个需要跟前端对接协议
            foreach ($this->orderProductList as &$product) {
                $product['id'] = $product['unique_id'] ?? 0;
                $product['refer_id'] = $this->order_id ?? \common\library\object\field\updator\calculator\SyncCalculator::DEFAULT_PRIMARY_KEY_ID;
            }
            $syncCalculator->setSubData('objOrderProduct', $this->orderProductList);  // 设置从对象数据，如果没有从对象，可以不设置
        }

        $syncCalculator->calculate();       // 计算字段

        $this->_attributes = $syncCalculator->getMainData();     // 获取计算后的主对象数据，覆盖到原有的 orderRow

        // getSubData当缺少子对象数据时会返回空数组，在没有外部设置orderProductList情况下，会导致orderProductList=[]而删除产品
        if (!is_null($this->orderProductList)) {
            $this->orderProductList = $syncCalculator->getSubData('objOrderProduct');     // 获取计算后的主对象数据, 覆盖到原有的 orderRecordRows
        }

    }


    public function isViewAble()
    {
        $domainHandler = $this->getDomainHandler();
        if ($this->enable_flag == \Constants::ENABLE_FLAG_FALSE || $this->delete_flag == \Constants::DELETE_FLAG_TRUE) {
            if ($this->scene == OrderConstants::ORDER_INFO_SCENE_RECYCLE && PrivilegeService::getInstance($this->getClientId(),$domainHandler->getUserId())->hasPrivilege(PrivilegeConstants::PRIVILEGE_SETTING_RECYCLE_SETTING)) {
                return true;
            }
            $name = \Yii::t('recycle', '订单');

            if (\common\library\recycle\API::isExist($this->client_id, Recycle::ORDER, $this->getObjectId()))
                throw new \RuntimeException(\Yii::t('recycle', "此{name}已被删除！\n如需查看，请联系管理员从回收箱恢复", ['{name}' => $name]), \ErrorCode::CODE_INVOICE_NOT_RECYCLE);
            else
                throw new \RuntimeException(\Yii::t('recycle', "此{name}已被彻底删除！\n不支持查看", ['{name}' => $name]), \ErrorCode::CODE_INVOICE_NOT_DELETE);
        }


        if (\common\library\privilege_v3\Helper::canOperateRecord($this->_clientId, $this->getDomainHandler()->getUserId(), $this->manageableUserIds(), PrivilegeConstants::PRIVILEGE_CRM_ORDER_VIEW)) {
            return true;
        }

        if ($this->isCanEditApprover()) {
            return true;
        }

        $company = new Company($this->client_id, $this->company_id);
        $company = $company->isExist() ? $company : null;
        if ($company && $company->checkOwner($domainHandler, true)) {
            //客户详情动态跳转到销售订单，若用户有权限访问该订单，上方逻辑已返回true,不会执行该处逻辑代码，故用户访问订单按照精细化权限处理；若用户无权限查看该订单，执行该代码逻辑，跳转时可访问，字段权限取用户角色上的大并集。
            $this->isFieldPrivilegeByUserObject = true;
            return true;
        }

        return false;
    }

    public function isManageable()
    {
        $userIds = $this->manageableUserIds();
        if (in_array($this->getDomainHandler()->getUserId(), $userIds)) {
            return true;
        } else {
            return $this->canManage($userIds);
        }
    }

    protected function manageableUserIds(){
        return $this->scope_user_ids;
    }

    public function canManage($userIds)
    {
        return \common\library\privilege_v3\Helper::canManageAnyUsers($this->client_id, $this->getDomainHandler()->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_ORDER_VIEW, $userIds);
    }

    /**
     * @throws \Exception
     */
    public function canEdit($throw = false)
    {
        try {
            !empty($this->checkEditableFields) && $this->checkEditableFields($this->checkEditableFields);


            if($this->hasReviewPrivilege() && $this->isCanEditApprover()) {
                $this->setSkipManageable(true);
                if(!\common\library\privilege_v3\Helper::canOperateRecord($this->_clientId, $this->getDomainHandler()->getUserId(), $this->manageableUserIds(), PrivilegeConstants::PRIVILEGE_CRM_ORDER_VIEW)){
                    $this->isFieldPrivilegeByUserObject = true;
                }
                return true;
            }

            if ($this->isLock($this->getObjectId(), \common\library\approval_flow\Constants::ENTITY_TYPE_ORDER)) {
                throw new \RuntimeException(\Yii::t('invoice','订单数据锁定，无法编辑'));
            }

            if(\common\library\privilege_v3\Helper::canOperateRecord($this->_clientId, $this->getDomainHandler()->getUserId(), $this->manageableUserIds(),$this->orderEditPrivilege)) {
                return true;
            }

            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'));

        } catch (\Exception $exception) {
            if ($throw) {
                throw  $exception;
            } else {
                return false;
            }
        }
    }

    public function checkEditableFields($checkEditableFields)
    {
        $fields = array_intersect($checkEditableFields, $this->getNotEditableFields());
        if(!empty($fields)){
            $objNames = \common\library\object\object_relation\Helper::getMainSubObjectNames($this->getMetadata()->objectName());
            $fieldFilter = new FieldFilter($this->getClientId());
            $fieldFilter->object_name = $objNames;
            $fieldFilter->field = array_values($fields);
            $fieldFilter->select(['field', 'field_name']);
            $notEditAbleFieldName = implode(',', array_column($fieldFilter->rawData(), 'field_name'));
            \LogUtil::info('client_id[' . $this->getClientId() . ']修改订单，字段'.$notEditAbleFieldName.'权限只读或隐藏，不可编辑');
            throw new \RuntimeException('字段'.$notEditAbleFieldName.'权限只读或隐藏，不可编辑');
        }
        return true;
    }

    public function canUnlock()
    {
        if($this->isLock($this->getObjectId(), \common\library\approval_flow\Constants::ENTITY_TYPE_ORDER) && \common\library\privilege_v3\Helper::hasPermission($this->getClientId(), $this->getDomainHandler()->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_ORDER_APPROVAL_UNLOCK)) {
            return true;
        }
        return false;
    }

    /**
     * @param bool $skipManageable
     */
    public function setSkipManageable(bool $skipManageable)
    {
        $this->skipManageable = $skipManageable;
    }


    public function getPrivilegeFieldFunctionalId()
    {
        return PrivilegeConstants::FUNCTIONAL_ORDER;
    }

    public function getPrivilegeFieldUserId()
    {
        return $this->getDomainHandler()->getUserId();
    }

    public function getPerformanceReferType()
    {
        return \Constants::TYPE_ORDER;
    }

    public function getReferPerformance()
    {
        return new OrderPerformance($this);
    }

    /**
     * 维护单据总价 -- 迁移接口 \common\library\invoice\Invoice::refreshTotal
     */
    public function refreshTotal(){
        $this->product_total_amount = floatval($this->product_total_amount ?? 0.0);
        $this->product_total_count = floatval($this->product_total_count ?? 0);
        $this->addition_cost_amount = floatval($this->addition_cost_amount ?? 0.0);

        $this->amount = $this->amount ?? 0.0;
        // 信保订单同步时，不进行计算，信保订单有场景是没有产品金额和其他费用的，只有订单总额
        if (!$this->fieldEditType == OrderConstants::FIELD_EDIT_TYPE_BY_ALIBABA_ORDER_SYNC) {
            $this->amount = $this->product_total_amount + $this->addition_cost_amount;
        }

        $this->amount_rmb = $this->amount * ($this->exchange_rate / 100);
        $this->amount_usd = $this->amount * $this->exchange_rate_usd / 100;
    }

    // 存在订单的字段不好被新的操作历史定义
    public function historyField($fields = null)
    {
        //阿里订单同步过过滤操作历史
        if ($this->fieldEditType == OrderConstants::FIELD_EDIT_TYPE_BY_ALIBABA_ORDER_SYNC) {
            $fields = ['status'] ;
        }
        $c = new OrderCompare($this->getClientId());

        if (is_null($fields)) {
            $fields = array_keys($c->getFieldCompareMap());
        }

        $fields = array_diff($fields, ['product_list']);
        $newAttributes = $this->getAttributes($fields);
        $oldAttributes = $this->getOldAttributes($fields);

        if($this->fieldEditType){
            $this->editReferType = $this->fieldEditType;
        }

        if (!empty($this->editReferId)) {
            $c->setEditInfo($this->editReferType, null, $this->editReferId);
        } else if ($this->editReferType) {
            $c->setEditInfo($this->editReferType);
        }
        $c->setType(\Constants::TYPE_ORDER);
        $c->setData($newAttributes ??[], $oldAttributes??[]);
        $c->setExtraData(['refer_id' => $this->getObjectId()]);
        $c->build($this->getDomainHandler()->getUserId());
        return $c->getProductDiffData();
    }

    public function changeAliOrderSyncConfig(array $removeSyncConfig = [], $addSyncConfig = [])
    {
        if (!in_array($this->source_type, [OrderConstants::TYPE_ALI_ORDER, OrderConstants::TYPE_DIRECT_PAY_ORDER])) {
            return false;
        }
        $alibabaOrderRelation = new \common\library\alibaba\order\AlibabaOrderRelation($this->getClientId());
        $alibabaOrderRelation->loadByOrderId($this->getObjectId());
        $alibabaOrderRelation->setOpUserId($this->getDomainHandler()->getUserId());

        $oldSyncConfig = \common\library\util\PgsqlUtil::trimArray($alibabaOrderRelation->sync_ali_order_config);
        $oldSyncConfig = empty($oldSyncConfig) ? [] : $oldSyncConfig;
        if (!empty($addSyncConfig)) {
            $diff = array_merge($oldSyncConfig, $addSyncConfig);
        } else {
            $diff = array_diff($oldSyncConfig, $removeSyncConfig);
        }
        $diff = array_unique($diff);
        if (!$alibabaOrderRelation->isNew()) {
            $alibabaOrderRelation->updateSyncConfig($diff);
        }
    }

    //修改报价单
    private function triggerQuotation()
    {
        if (empty($this->quotation_id)){
            return;
        }

       $quotation =  new Quotation($this->getClientId(),$this->quotation_id);
        if (!$quotation->isExist()){
            return ;
        }
        $quotation->getOperator()->updateTransferFlag();
    }

    private function refreshAlibabaProductList()
    {
        if (!is_null($this->alibabaOrderProductList))
        {
            $oldAliOrderProductIds = [];
            if (!$this->isNew()) {
                $filter = new AliBaBaOrderProductFilter($this->_clientId);
                $filter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
                $filter->order_id = $this->getObjectId();
                $filter->select(['ali_order_product_id']);
                $oldAliOrderProductIds = array_column($filter->rawData(), 'ali_order_product_id');
            }

            $newAliOrderProductIds = [];
            $newAliOrderProduct = [];
            $updateAliOrderProductIds = [];
            $updateAliOrderProduct = [];

            $sort = 1;
            foreach ($this->alibabaOrderProductList as &$aliOrderProduct) {
                $aliOrderProduct['sort'] = $sort++;
                $id = $aliOrderProduct['ali_order_product_id'] ?? 0;
                if (!in_array($id, $oldAliOrderProductIds)) {
                    $aliOrderProduct['ali_order_product_id']  = \PgActiveRecord::produceAutoIncrementId();
                    $newAliOrderProductIds[] = $aliOrderProduct['ali_order_product_id'];
                }

                if (!$this->isNew()) {
                    $aliOrderProduct['order_id'] = $this->getObjectId();
                }

                if (in_array($aliOrderProduct['ali_order_product_id'], $newAliOrderProductIds)) {
                    $newAliOrderProduct[] = $aliOrderProduct;
                } else {
                    $updateAliOrderProduct[] = $aliOrderProduct;
                    $updateAliOrderProductIds[] = $aliOrderProduct['ali_order_product_id'];
                }
            }
            unset($aliOrderProduct);

            if (!empty($newAliOrderProduct)) {
                $batchAliOrderProduct = new BatchAlibabaOrderProduct($this->getClientId());
                $batchAliOrderProduct->initFromData($newAliOrderProduct);
                $batchAliOrderProduct->setOrder($this);
                $batchAliOrderProduct->beforeCreate();
                $this->batchCreateAliOrderProduct = $batchAliOrderProduct;
            }

            if (!empty($updateAliOrderProduct)) {
                $batchAliOrderProduct = new BatchAlibabaOrderProduct($this->getClientId());
                $batchAliOrderProduct->initFromData($updateAliOrderProduct);
                $batchAliOrderProduct->setOrder($this);
                $batchAliOrderProduct->beforeUpdate();
                $this->batchUpdateAliOrderProduct = $batchAliOrderProduct;
            }


            $deleteOrderProduct = array_diff($oldAliOrderProductIds, array_merge($updateAliOrderProductIds,$newAliOrderProductIds));
            if (!empty($deleteOrderProduct)) {
                $filter = new AlibabaOrderProductFilter($this->_clientId);
                $filter->ali_order_product_id = $deleteOrderProduct;
                $batchAliOrderProduct = new BatchAlibabaOrderProduct($this->_clientId, $filter);
                $batchAliOrderProduct->setOrder($this);
                $batchAliOrderProduct->beforeDelete();
                $this->batchDeleteAliOrderProduct = $batchAliOrderProduct;
            }
        }

    }

    private function transferAliOrder()
    {
        if (
            empty($this->ali_store_id)
            ||
            empty($this->seller_account_id)
            ||
            empty($this->alibabaOrderProductList)
            ||
            $this->transfer_ali_order_type == OmsConstant::IS_TRANSFER_ALI_ORDER_FLAG
        ) {
            return;
        }


        $res = AlibabaOrderSyncHelper::createTransferAliOrderTask($this->getObjectId(), $this->ali_order_info, $this->ali_store_id);
        $this->setAliTaskId($res['task_id'] ?? 0);

        //下载信保订单图片
        CommandRunner::run(
            'alibaba',
            'SyncAliProductImg',
            [
                'client_id' => $this->client_id,
                'order_id' => $this->order_id,
            ],
        );
    }

    //todo 临时兼容线上问题，该方法的数据不一定准确，还得靠refreshProductList，需要考虑好在calculateFunctionalFields缺少汇总数据的问题
    public function prepareData()
    {
        if (is_null($this->orderProductList)) {
            return;
        }
        $orderGrossMargin = 0;
        $costWithTaxTotal = 0;
        $product_total_amount = $product_total_count = $package_volume_amount = $package_gross_weight_amount = $parts_total_count = 0;

        $productList = $this->orderProductList;
        foreach ($productList as &$product) {
            $product['count'] = empty($product['count']) ? 0 : $product['count'];
            $product['cost_amount'] = empty($product['cost_amount']) ? 0 : $product['cost_amount'];
            $product['package_volume_subtotal'] = empty($product['package_volume_subtotal']) ? 0 : $product['package_volume_subtotal'];
            $product['package_gross_weight_subtotal'] = empty($product['package_gross_weight_subtotal']) ? 0 : $product['package_gross_weight_subtotal'];
            $product['gross_margin'] = empty($product['gross_margin']) ? 0 : $product['gross_margin'];
            $product['cost_with_tax'] = empty($product['cost_with_tax']) ? 0 : $product['cost_with_tax'];

            $product_total_amount += ($product['cost_amount'] ?? 0);
            $product_total_count += ($product['count'] ?? 0);
            $package_volume_amount += !empty($product['package_volume_subtotal']) ? $product['package_volume_subtotal'] : 0;
            $package_gross_weight_amount += !empty($product['package_gross_weight_subtotal']) ? $product['package_gross_weight_subtotal'] : 0;
            $orderGrossMargin += ($product['gross_margin']??0) * ($product['count']??0);
            $costWithTaxTotal += ($product['cost_with_tax']??0) * ($product['count']??0);
        }

        //由前端计算转成后端处理
        $this->product_total_amount = $product_total_amount;
        $this->product_total_amount_rmb = $product_total_amount * ($this->exchange_rate / 100);
        $this->product_total_amount_usd = $product_total_amount * $this->exchange_rate_usd / 100;

        $this->product_total_count = $product_total_count;
        $this->package_volume_amount = $package_volume_amount;
        $this->package_gross_weight_amount = $package_gross_weight_amount;
        $this->parts_total_count = $parts_total_count;
        $this->product_total_count_no_parts = $product_total_count - $parts_total_count;

        $this->order_gross_margin = $orderGrossMargin;
        $this->order_gross_margin_cny = $orderGrossMargin * ($this->exchange_rate / 100);
        $this->order_gross_margin_usd = $orderGrossMargin * ($this->exchange_rate_usd / 100);
        $this->cost_with_tax_total = $costWithTaxTotal;
    }


    /**
     * 获取回款单需要更新的字段的差异比对
     */
    public function getCashCollectionDiffData($newAttributes, $oldAttributes)
    {
        // 生成差异数据
        $diffData = [];
        foreach (OrderConstants::SYNC_COLLECTION_FIELDS as $field => $compareClass) {
            $newValue =  $newAttributes[$field]??"";
            $oldValue =  $oldAttributes[$field]??"";
            // 创建比较实例并进行比较
            $compareClass =  $compareClass??FieldCompare::class;
            $comparator = new $compareClass(['id' => 0, 'base' => '0', 'new' => '', 'old' => '']);
            $comparator->setFieldKey($field);
            $diffData = array_merge($diffData, $comparator->buildDiff($newValue,$oldValue));
        }
        return $diffData;
    }
}
