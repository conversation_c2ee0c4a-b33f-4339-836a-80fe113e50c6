<?php

namespace common\library\oms\page_layout;

use common\library\privilege_v3\PrivilegeConstants;
use protobuf\CRMCommon\PBLayoutTabEnum;
use protobuf\CRMCommon\PBPageLayoutItem;

class PageLayoutConstant
{

    //菜单id
    const MENU_ID_ORDER = 'order';  //销售订单
    const MENU_ID_PURCHASE_ORDER = 'purchaseOrder';  //采购订单
    const MENU_ID_PRODUCT = 'product';  //产品

    //页面id
    const PAGE_ID_DETAIL = 1;     //详情页

    //order tab
    const ORDER_TAB_DETAILS = 'order.tab.details';  //订单详情
    const ORDER_TAB_INTERNATIONAL_STATION = 'order.tab.internationalStation';  //国际站订单信息
    const ORDER_TAB_CASH_COLLECTION = 'order.tab.cashCollection';  //回款单
    const ORDER_TAB_STOCK_UP = 'order.tab.stockUp';  //备货
    const ORDER_TAB_EX_WAREHOUSE = 'order.tab.exWarehouse';  //出库
    const ORDER_TAB_COST_INVOICE = 'order.tab.costInvoice';  //费用单
    const ORDER_TAB_ATTACHMENT = 'order.tab.attachment';  //附件
    const ORDER_TAB_SCHEDULE = 'order.tab.schedule';  //日程
    const ORDER_TAB_APPROVAL_HISTORY = 'order.tab.approvalHistory';  //审批历史
    const ORDER_TAB_LOGISTICS = 'order.tab.logistics';  //物流运输
    const ORDER_TAB_EXPORT_ORDER = 'order.tab.exportOrder';  //导出销售订单
    const ORDER_TAB_OPERATION_RECORDS = 'order.tab.operationRecords';  //操作记录
    const ORDER_TAB_SHIPPING_INVOICE = 'order.tab.shippingInvoice'; //出运单

    const PURCHASE_ORDER_TAB_DETAILS = 'purchaseOrder.tab.details';  //订单详情
    const PURCHASE_ORDER_TAB_INBOUND = 'purchaseOrder.tab.inbound';  //入库
    const PURCHASE_ORDER_TAB_COST_INVOICE = 'purchaseOrder.tab.costInvoice';  //费用单
    const PURCHASE_ORDER_TAB_PAYMENT_INVOICE = 'purchaseOrder.tab.paymentInvoice';  //付款单
    const PURCHASE_ORDER_TAB_APPROVAL_HISTORY = 'purchaseOrder.tab.approvalHistory';  //审批记录
    const PURCHASE_ORDER_TAB_EXPORT_ORDER = 'purchaseOrder.tab.exportOrder';  //采购订单导出记录
    const PURCHASE_ORDER_TAB_ATTACHMENT = 'purchaseOrder.tab.attachment';  //附件
    const PURCHASE_ORDER_TAB_OPERATION_RECORDS = 'purchaseOrder.tab.operationRecords';  //操作记录

    // 产品
    const PRODUCT_TAB_DETAILS = 'product.tab.details';  //产品详情'
    const PRODUCT_TAB_TRANSACTION_RECORDS = 'product.tab.transactionRecords';  //交易记录
    const PRODUCT_TAB_STATISTICAL_ANALYSIS = 'product.tab.statisticalAnalysis';  //统计分析
    const PRODUCT_TAB_ATTACHMENT = 'product.tab.attachment';  //附件
    const PRODUCT_TAB_SUPPLIER = 'product.tab.supplier';  //供应商
    const PRODUCT_TAB_OPERATION_RECORDS = 'product.tab.operationRecords';  //操作记录

    const PRODUCT_TAB_GROUP_INVENTORY = 'product.tab.inventory';  //库存tab

    const PRODUCT_TAB_SUB_PRODUCT = 'product.tab.subProduct'; // 子产品
    const PRODUCT_TAB_SPECIFICATION = 'product.tab.specification'; // 规格

    const PRODUCT_TAB_HISTORY = 'product.tab.history'; // 历史 == 操作记录

    const PRODUCT_TAB_DEAL = 'product.tab.deal'; // 成交 == 交易记录

    // 详情 tab 仅定义常量 用于移动端产品tab 产品详情 => 资料 国际化
    const PRODUCT_TAB_PROFILE = 'product.tab.profile'; // 产品详情 == 资料
    
    const PRODUCT_TAB_INQUIRY_COLLABORATION = 'product.tab.inquiryCollaboration'; // 询价协同';

    const TAB_MAP = [
        self::ORDER_TAB_DETAILS => '订单详情',
        self::ORDER_TAB_INTERNATIONAL_STATION => '国际站订单信息',
        self::ORDER_TAB_CASH_COLLECTION => '回款单',
        self::ORDER_TAB_STOCK_UP => '备货',
        self::ORDER_TAB_EX_WAREHOUSE => '出库',
        self::ORDER_TAB_COST_INVOICE => '费用单',
        self::ORDER_TAB_ATTACHMENT => '附件',
        self::ORDER_TAB_SCHEDULE => '日程',
        self::ORDER_TAB_APPROVAL_HISTORY => '审批历史',
        self::ORDER_TAB_LOGISTICS => '物流运输',
        self::ORDER_TAB_EXPORT_ORDER => '导出销售订单',
        self::ORDER_TAB_OPERATION_RECORDS => '操作记录',
        self::ORDER_TAB_SHIPPING_INVOICE => '出运单',

        self::PURCHASE_ORDER_TAB_DETAILS           => '订单详情',
        self::PURCHASE_ORDER_TAB_INBOUND           => '入库',
        self::PURCHASE_ORDER_TAB_COST_INVOICE      => '费用单',
        self::PURCHASE_ORDER_TAB_PAYMENT_INVOICE   => '付款单',
        self::PURCHASE_ORDER_TAB_APPROVAL_HISTORY  => '审批记录',
        self::PURCHASE_ORDER_TAB_EXPORT_ORDER      => '导出记录',
        self::PURCHASE_ORDER_TAB_ATTACHMENT        => '附件',
        self::PURCHASE_ORDER_TAB_OPERATION_RECORDS => '操作记录',

        self::PRODUCT_TAB_DETAILS                  => '产品详情',
        self::PRODUCT_TAB_TRANSACTION_RECORDS      => '成交记录',
        self::PRODUCT_TAB_STATISTICAL_ANALYSIS     => '统计分析',
        self::PRODUCT_TAB_ATTACHMENT               => '附件',
        self::PRODUCT_TAB_SUPPLIER                 => '供应商',
        self::PRODUCT_TAB_OPERATION_RECORDS        => '操作记录',
        self::PRODUCT_TAB_GROUP_INVENTORY          => '库存',
        self::PRODUCT_TAB_SUB_PRODUCT              => '子产品',
        self::PRODUCT_TAB_SPECIFICATION            => '规格',
        self::PRODUCT_TAB_HISTORY                  => '历史',
        self::PRODUCT_TAB_DEAL                     => '成交',
        self::PRODUCT_TAB_PROFILE                  => '资料',
        self::PRODUCT_TAB_INQUIRY_COLLABORATION    => '询价历史',
    ];

    // tab => pb enum映射
    const TAB_TO_PB_ENUM_MAP = [
        self::ORDER_TAB_DETAILS                    => PBLayoutTabEnum::ORDER_TAB_DETAILS,
        self::ORDER_TAB_INTERNATIONAL_STATION      => PBLayoutTabEnum::ORDER_TAB_INTERNATIONAL_STATION ,
        self::ORDER_TAB_CASH_COLLECTION            => PBLayoutTabEnum::ORDER_TAB_CASH_COLLECTION,
        self::ORDER_TAB_STOCK_UP                   => PBLayoutTabEnum::ORDER_TAB_STOCK_UP,
        self::ORDER_TAB_EX_WAREHOUSE               => PBLayoutTabEnum::ORDER_TAB_EX_WAREHOUSE,
        self::ORDER_TAB_COST_INVOICE               => PBLayoutTabEnum::ORDER_TAB_COST_INVOICE,
        self::ORDER_TAB_ATTACHMENT                 => PBLayoutTabEnum::ORDER_TAB_ATTACHMENT,
        self::ORDER_TAB_SCHEDULE                   => PBLayoutTabEnum::ORDER_TAB_SCHEDULE,
        self::ORDER_TAB_APPROVAL_HISTORY           => PBLayoutTabEnum::ORDER_TAB_APPROVAL_HISTORY,
        self::ORDER_TAB_LOGISTICS                  => PBLayoutTabEnum::ORDER_TAB_LOGISTICS,
        self::ORDER_TAB_EXPORT_ORDER               => PBLayoutTabEnum::ORDER_TAB_EXPORT_ORDER,
        self::ORDER_TAB_OPERATION_RECORDS          => PBLayoutTabEnum::ORDER_TAB_OPERATION_RECORDS,
        self::ORDER_TAB_SHIPPING_INVOICE           => PBLayoutTabEnum::ORDER_TAB_SHIPPING_INVOICE,

        self::PURCHASE_ORDER_TAB_DETAILS           => PBLayoutTabEnum::PURCHASE_ORDER_TAB_DETAILS,
        self::PURCHASE_ORDER_TAB_INBOUND           => PBLayoutTabEnum::PURCHASE_ORDER_TAB_INBOUND,
        self::PURCHASE_ORDER_TAB_COST_INVOICE      => PBLayoutTabEnum::PURCHASE_ORDER_TAB_COST_INVOICE,
        self::PURCHASE_ORDER_TAB_PAYMENT_INVOICE   => PBLayoutTabEnum::PURCHASE_ORDER_TAB_PAYMENT_INVOICE,
        self::PURCHASE_ORDER_TAB_APPROVAL_HISTORY  => PBLayoutTabEnum::PURCHASE_ORDER_TAB_APPROVAL_HISTORY,
        self::PURCHASE_ORDER_TAB_EXPORT_ORDER      => PBLayoutTabEnum::PURCHASE_ORDER_TAB_EXPORT_ORDER,
        self::PURCHASE_ORDER_TAB_ATTACHMENT        => PBLayoutTabEnum::PURCHASE_ORDER_TAB_ATTACHMENT,
        self::PURCHASE_ORDER_TAB_OPERATION_RECORDS => PBLayoutTabEnum::PURCHASE_ORDER_TAB_OPERATION_RECORDS,

        self::PRODUCT_TAB_DETAILS                  => PBLayoutTabEnum::PRODUCT_TAB_DETAILS,
        self::PRODUCT_TAB_TRANSACTION_RECORDS      => PBLayoutTabEnum::PRODUCT_TAB_TRANSACTION_RECORDS,
        self::PRODUCT_TAB_STATISTICAL_ANALYSIS     => PBLayoutTabEnum::PRODUCT_TAB_STATISTICAL_ANALYSIS,
        self::PRODUCT_TAB_ATTACHMENT               => PBLayoutTabEnum::PRODUCT_TAB_ATTACHMENT,
        self::PRODUCT_TAB_SUPPLIER                 => PBLayoutTabEnum::PRODUCT_TAB_SUPPLIER,
        self::PRODUCT_TAB_OPERATION_RECORDS        => PBLayoutTabEnum::PRODUCT_TAB_OPERATION_RECORDS,
        self::PRODUCT_TAB_SUB_PRODUCT              => PBLayoutTabEnum::PRODUCT_TAB_SUB_PRODUCT,
        self::PRODUCT_TAB_SPECIFICATION            => PBLayoutTabEnum::PRODUCT_TAB_SPECIFICATION,
    ];

    // pb enum => tab
    const PB_ENUM_TO_TAB_MAP = [
        PBLayoutTabEnum::ORDER_TAB_DETAILS                    => self::ORDER_TAB_DETAILS,
        PBLayoutTabEnum::ORDER_TAB_INTERNATIONAL_STATION      => self::ORDER_TAB_INTERNATIONAL_STATION ,
        PBLayoutTabEnum::ORDER_TAB_CASH_COLLECTION            => self::ORDER_TAB_CASH_COLLECTION,
        PBLayoutTabEnum::ORDER_TAB_STOCK_UP                   => self::ORDER_TAB_STOCK_UP,
        PBLayoutTabEnum::ORDER_TAB_EX_WAREHOUSE               => self::ORDER_TAB_EX_WAREHOUSE,
        PBLayoutTabEnum::ORDER_TAB_COST_INVOICE               => self::ORDER_TAB_COST_INVOICE,
        PBLayoutTabEnum::ORDER_TAB_ATTACHMENT                 => self::ORDER_TAB_ATTACHMENT,
        PBLayoutTabEnum::ORDER_TAB_SCHEDULE                   => self::ORDER_TAB_SCHEDULE,
        PBLayoutTabEnum::ORDER_TAB_APPROVAL_HISTORY           => self::ORDER_TAB_APPROVAL_HISTORY,
        PBLayoutTabEnum::ORDER_TAB_LOGISTICS                  => self::ORDER_TAB_LOGISTICS,
        PBLayoutTabEnum::ORDER_TAB_EXPORT_ORDER               => self::ORDER_TAB_EXPORT_ORDER,
        PBLayoutTabEnum::ORDER_TAB_OPERATION_RECORDS          => self::ORDER_TAB_OPERATION_RECORDS,
        PBLayoutTabEnum::ORDER_TAB_SHIPPING_INVOICE           => self::ORDER_TAB_SHIPPING_INVOICE,

        PBLayoutTabEnum::PURCHASE_ORDER_TAB_DETAILS           => self::PURCHASE_ORDER_TAB_DETAILS,
        PBLayoutTabEnum::PURCHASE_ORDER_TAB_INBOUND           => self::PURCHASE_ORDER_TAB_INBOUND,
        PBLayoutTabEnum::PURCHASE_ORDER_TAB_COST_INVOICE      => self::PURCHASE_ORDER_TAB_COST_INVOICE,
        PBLayoutTabEnum::PURCHASE_ORDER_TAB_PAYMENT_INVOICE   => self::PURCHASE_ORDER_TAB_PAYMENT_INVOICE,
        PBLayoutTabEnum::PURCHASE_ORDER_TAB_APPROVAL_HISTORY  => self::PURCHASE_ORDER_TAB_APPROVAL_HISTORY,
        PBLayoutTabEnum::PURCHASE_ORDER_TAB_EXPORT_ORDER      => self::PURCHASE_ORDER_TAB_EXPORT_ORDER,
        PBLayoutTabEnum::PURCHASE_ORDER_TAB_ATTACHMENT        => self::PURCHASE_ORDER_TAB_ATTACHMENT,
        PBLayoutTabEnum::PURCHASE_ORDER_TAB_OPERATION_RECORDS => self::PURCHASE_ORDER_TAB_OPERATION_RECORDS,

        PBLayoutTabEnum::PRODUCT_TAB_DETAILS                  => self::PRODUCT_TAB_DETAILS,
        PBLayoutTabEnum::PRODUCT_TAB_TRANSACTION_RECORDS      => self::PRODUCT_TAB_TRANSACTION_RECORDS,
        PBLayoutTabEnum::PRODUCT_TAB_STATISTICAL_ANALYSIS     => self::PRODUCT_TAB_STATISTICAL_ANALYSIS,
        PBLayoutTabEnum::PRODUCT_TAB_ATTACHMENT               => self::PRODUCT_TAB_ATTACHMENT,
        PBLayoutTabEnum::PRODUCT_TAB_SUPPLIER                 => self::PRODUCT_TAB_SUPPLIER,
        PBLayoutTabEnum::PRODUCT_TAB_OPERATION_RECORDS        => self::PRODUCT_TAB_OPERATION_RECORDS,
        PBLayoutTabEnum::PRODUCT_TAB_SUB_PRODUCT              => self::PRODUCT_TAB_SUB_PRODUCT,
        PBLayoutTabEnum::PRODUCT_TAB_SPECIFICATION            => self::PRODUCT_TAB_SPECIFICATION,

    ];


    //系统与信息组映射
    const SYSTEM_ID_TO_TAB_MAP = [
        //order
        self::MENU_ID_ORDER => [
            //detail
            self::PAGE_ID_DETAIL => [
                PrivilegeConstants::CRM_SYSTEM_ID => [
                    self::ORDER_TAB_DETAILS,
                    self::ORDER_TAB_INTERNATIONAL_STATION,
                    self::ORDER_TAB_CASH_COLLECTION,
                    self::ORDER_TAB_ATTACHMENT,
                    self::ORDER_TAB_SCHEDULE,
                    self::ORDER_TAB_APPROVAL_HISTORY,
                    self::ORDER_TAB_LOGISTICS,
                    self::ORDER_TAB_EXPORT_ORDER,
                    self::ORDER_TAB_OPERATION_RECORDS,
                ],
                PrivilegeConstants::CRM_PLUS_SYSTEM_ID => [
                    self::ORDER_TAB_DETAILS,
                    self::ORDER_TAB_INTERNATIONAL_STATION,
                    self::ORDER_TAB_CASH_COLLECTION,
                    self::ORDER_TAB_ATTACHMENT,
                    self::ORDER_TAB_SCHEDULE,
                    self::ORDER_TAB_APPROVAL_HISTORY,
                    self::ORDER_TAB_LOGISTICS,
                    self::ORDER_TAB_EXPORT_ORDER,
                    self::ORDER_TAB_OPERATION_RECORDS,
                ],
                PrivilegeConstants::OKKI_PRO_SYSTEM_ID => [
                    self::ORDER_TAB_DETAILS,
                    self::ORDER_TAB_INTERNATIONAL_STATION,
                    self::ORDER_TAB_CASH_COLLECTION,
                    self::ORDER_TAB_ATTACHMENT,
                    self::ORDER_TAB_SCHEDULE,
                    self::ORDER_TAB_APPROVAL_HISTORY,
                    self::ORDER_TAB_LOGISTICS,
                    self::ORDER_TAB_EXPORT_ORDER,
                    self::ORDER_TAB_OPERATION_RECORDS,
                    self::ORDER_TAB_SHIPPING_INVOICE,

                    // self::PURCHASE_ORDER_TAB_DETAILS,
                    // self::PURCHASE_ORDER_TAB_INBOUND,
                    // self::PURCHASE_ORDER_TAB_COST_INVOICE,
                    // self::PURCHASE_ORDER_TAB_PAYMENT_INVOICE,
                    // self::PURCHASE_ORDER_TAB_APPROVAL_HISTORY,
                    // self::PURCHASE_ORDER_TAB_EXPORT_ORDER,
                    // self::PURCHASE_ORDER_TAB_ATTACHMENT,
                    // self::PURCHASE_ORDER_TAB_OPERATION_RECORDS,
                ],
                PrivilegeConstants::CRM_SMART_SYSTEM_ID => [
                    self::ORDER_TAB_DETAILS,
                    self::ORDER_TAB_INTERNATIONAL_STATION,
                    self::ORDER_TAB_CASH_COLLECTION,
                    self::ORDER_TAB_ATTACHMENT,
                    self::ORDER_TAB_SCHEDULE,
                    self::ORDER_TAB_APPROVAL_HISTORY,
                    self::ORDER_TAB_LOGISTICS,
                    self::ORDER_TAB_EXPORT_ORDER,
                    self::ORDER_TAB_OPERATION_RECORDS,
                ],
                PrivilegeConstants::TW_SMART_AI_ID => [
                    self::ORDER_TAB_DETAILS,
                    self::ORDER_TAB_INTERNATIONAL_STATION,
                    self::ORDER_TAB_CASH_COLLECTION,
                    self::ORDER_TAB_ATTACHMENT,
                    self::ORDER_TAB_SCHEDULE,
                    self::ORDER_TAB_APPROVAL_HISTORY,
                    self::ORDER_TAB_LOGISTICS,
                    self::ORDER_TAB_EXPORT_ORDER,
                    self::ORDER_TAB_OPERATION_RECORDS,
                ],
                PrivilegeConstants::CRM_PRO_SYSTEM_ID => [
                    self::ORDER_TAB_DETAILS,
                    self::ORDER_TAB_INTERNATIONAL_STATION,
                    self::ORDER_TAB_CASH_COLLECTION,
                    self::ORDER_TAB_ATTACHMENT,
                    self::ORDER_TAB_SCHEDULE,
                    self::ORDER_TAB_APPROVAL_HISTORY,
                    self::ORDER_TAB_LOGISTICS,
                    self::ORDER_TAB_EXPORT_ORDER,
                    self::ORDER_TAB_OPERATION_RECORDS,
                    self::ORDER_TAB_SHIPPING_INVOICE,

                    // self::PURCHASE_ORDER_TAB_DETAILS,
                    // self::PURCHASE_ORDER_TAB_INBOUND,
                    // self::PURCHASE_ORDER_TAB_COST_INVOICE,
                    // self::PURCHASE_ORDER_TAB_PAYMENT_INVOICE,
                    // self::PURCHASE_ORDER_TAB_APPROVAL_HISTORY,
                    // self::PURCHASE_ORDER_TAB_EXPORT_ORDER,
                    // self::PURCHASE_ORDER_TAB_ATTACHMENT,
                    // self::PURCHASE_ORDER_TAB_OPERATION_RECORDS,
                ],
                PrivilegeConstants::CRM_LITE_SYSTEM_ID => [
                    self::ORDER_TAB_DETAILS,
                    self::ORDER_TAB_INTERNATIONAL_STATION,
                    self::ORDER_TAB_CASH_COLLECTION,
                    self::ORDER_TAB_ATTACHMENT,
                    self::ORDER_TAB_SCHEDULE,
                    self::ORDER_TAB_APPROVAL_HISTORY,
                    self::ORDER_TAB_LOGISTICS,
                    self::ORDER_TAB_EXPORT_ORDER,
                    self::ORDER_TAB_OPERATION_RECORDS,
                ],
                PrivilegeConstants::CRM_LITE_2021_ID => [
                    self::ORDER_TAB_DETAILS,
                    self::ORDER_TAB_INTERNATIONAL_STATION,
                    self::ORDER_TAB_ATTACHMENT,
                    self::ORDER_TAB_SCHEDULE,
                    self::ORDER_TAB_LOGISTICS,
                    self::ORDER_TAB_EXPORT_ORDER,
                    self::ORDER_TAB_OPERATION_RECORDS,
                ],
                PrivilegeConstants::CRM_LITE_2023_ID => [
                    self::ORDER_TAB_DETAILS,
                    self::ORDER_TAB_INTERNATIONAL_STATION,
                    self::ORDER_TAB_ATTACHMENT,
                    self::ORDER_TAB_SCHEDULE,
                    self::ORDER_TAB_LOGISTICS,
                    self::ORDER_TAB_EXPORT_ORDER,
                    self::ORDER_TAB_OPERATION_RECORDS,
                ],
                PrivilegeConstants::TW_LITE_AI_ID => [
                    self::ORDER_TAB_DETAILS,
                    self::ORDER_TAB_INTERNATIONAL_STATION,
                    self::ORDER_TAB_ATTACHMENT,
                    self::ORDER_TAB_SCHEDULE,
                    self::ORDER_TAB_LOGISTICS,
                    self::ORDER_TAB_EXPORT_ORDER,
                    self::ORDER_TAB_OPERATION_RECORDS,
                ],
            ],
        ],
        // product
        self::MENU_ID_PRODUCT => [
            //detail
            self::PAGE_ID_DETAIL => [
                PrivilegeConstants::CRM_SYSTEM_ID => [
                    self::PRODUCT_TAB_DETAILS,
                    self::PRODUCT_TAB_TRANSACTION_RECORDS,
                    self::PRODUCT_TAB_STATISTICAL_ANALYSIS,
                    self::PRODUCT_TAB_ATTACHMENT,
                    self::PRODUCT_TAB_OPERATION_RECORDS,
                ],
                PrivilegeConstants::CRM_PLUS_SYSTEM_ID => [
                    self::PRODUCT_TAB_DETAILS,
                    self::PRODUCT_TAB_TRANSACTION_RECORDS,
                    self::PRODUCT_TAB_STATISTICAL_ANALYSIS,
                    self::PRODUCT_TAB_ATTACHMENT,
                    self::PRODUCT_TAB_OPERATION_RECORDS,
                ],
                PrivilegeConstants::CRM_SMART_SYSTEM_ID => [
                    self::PRODUCT_TAB_DETAILS,
                    self::PRODUCT_TAB_TRANSACTION_RECORDS,
                    self::PRODUCT_TAB_STATISTICAL_ANALYSIS,
                    self::PRODUCT_TAB_ATTACHMENT,
                    self::PRODUCT_TAB_OPERATION_RECORDS,
                ],
                PrivilegeConstants::TW_SMART_AI_ID => [
                    self::PRODUCT_TAB_DETAILS,
                    self::PRODUCT_TAB_TRANSACTION_RECORDS,
                    self::PRODUCT_TAB_STATISTICAL_ANALYSIS,
                    self::PRODUCT_TAB_ATTACHMENT,
                    self::PRODUCT_TAB_OPERATION_RECORDS,
                ],
                PrivilegeConstants::CRM_PRO_SYSTEM_ID => [
                    self::PRODUCT_TAB_DETAILS,
                    self::PRODUCT_TAB_TRANSACTION_RECORDS,
                    self::PRODUCT_TAB_STATISTICAL_ANALYSIS,
                    self::PRODUCT_TAB_ATTACHMENT,
                    self::PRODUCT_TAB_SUPPLIER,
                    self::PRODUCT_TAB_OPERATION_RECORDS,
                    self::PRODUCT_TAB_GROUP_INVENTORY,
                    self::PRODUCT_TAB_INQUIRY_COLLABORATION,
                ],
                PrivilegeConstants::CRM_LITE_SYSTEM_ID => [
                    self::PRODUCT_TAB_DETAILS,
                    self::PRODUCT_TAB_TRANSACTION_RECORDS,
                    self::PRODUCT_TAB_STATISTICAL_ANALYSIS,
                    self::PRODUCT_TAB_ATTACHMENT,
                    self::PRODUCT_TAB_OPERATION_RECORDS,
                ],
                PrivilegeConstants::CRM_LITE_2021_ID => [
                    self::PRODUCT_TAB_DETAILS,
                    self::PRODUCT_TAB_TRANSACTION_RECORDS,
                    self::PRODUCT_TAB_STATISTICAL_ANALYSIS,
                    self::PRODUCT_TAB_ATTACHMENT,
                    self::PRODUCT_TAB_OPERATION_RECORDS,
                ],
                PrivilegeConstants::CRM_LITE_2023_ID => [
                    self::PRODUCT_TAB_DETAILS,
                    self::PRODUCT_TAB_TRANSACTION_RECORDS,
                    self::PRODUCT_TAB_STATISTICAL_ANALYSIS,
                    self::PRODUCT_TAB_ATTACHMENT,
                    self::PRODUCT_TAB_OPERATION_RECORDS,
                ],
                PrivilegeConstants::TW_LITE_AI_ID => [
                    self::PRODUCT_TAB_DETAILS,
                    self::PRODUCT_TAB_TRANSACTION_RECORDS,
                    self::PRODUCT_TAB_STATISTICAL_ANALYSIS,
                    self::PRODUCT_TAB_ATTACHMENT,
                    self::PRODUCT_TAB_OPERATION_RECORDS,
                ],
                PrivilegeConstants::OKKI_PRO_SYSTEM_ID => [
                    self::PRODUCT_TAB_DETAILS,
                    self::PRODUCT_TAB_TRANSACTION_RECORDS,
                    self::PRODUCT_TAB_STATISTICAL_ANALYSIS,
                    self::PRODUCT_TAB_ATTACHMENT,
                    self::PRODUCT_TAB_SUPPLIER,
                    self::PRODUCT_TAB_OPERATION_RECORDS,
                    self::PRODUCT_TAB_GROUP_INVENTORY,
                    self::PRODUCT_TAB_INQUIRY_COLLABORATION,
                ],
            ],
        ],
    ];

    //模块与订单信息组映射
    const MODULE_TO_TAB_MAP = [
        //order
        self::MENU_ID_ORDER => [
            //detail
            self::PAGE_ID_DETAIL => [
                PrivilegeConstants::MODULE_OMS => [
                    self::ORDER_TAB_DETAILS,
                    self::ORDER_TAB_INTERNATIONAL_STATION,
                    self::ORDER_TAB_CASH_COLLECTION,
                    self::ORDER_TAB_STOCK_UP,
                    self::ORDER_TAB_EX_WAREHOUSE,
                    self::ORDER_TAB_COST_INVOICE,
                    self::ORDER_TAB_ATTACHMENT,
                    self::ORDER_TAB_SCHEDULE,
                    self::ORDER_TAB_APPROVAL_HISTORY,
                    self::ORDER_TAB_LOGISTICS,
                    self::ORDER_TAB_EXPORT_ORDER,
                    self::ORDER_TAB_OPERATION_RECORDS,
                    self::ORDER_TAB_SHIPPING_INVOICE,
                ],
            ],
        ],
        self::MENU_ID_PURCHASE_ORDER => [
            //detail
            self::PAGE_ID_DETAIL => [
                PrivilegeConstants::MODULE_OMS => [
                    self::PURCHASE_ORDER_TAB_DETAILS,
                    self::PURCHASE_ORDER_TAB_INBOUND,
                    self::PURCHASE_ORDER_TAB_COST_INVOICE,
                    self::PURCHASE_ORDER_TAB_PAYMENT_INVOICE,
                    self::PURCHASE_ORDER_TAB_APPROVAL_HISTORY,
                    self::PURCHASE_ORDER_TAB_EXPORT_ORDER,
                    self::PURCHASE_ORDER_TAB_ATTACHMENT,
                    self::PURCHASE_ORDER_TAB_OPERATION_RECORDS,
                ],
            ],
        ],
        self::MENU_ID_PRODUCT => [
            //detail
            self::PAGE_ID_DETAIL => [
                PrivilegeConstants::MODULE_INVOICES => [
                    self::PRODUCT_TAB_DETAILS,
                    self::PRODUCT_TAB_TRANSACTION_RECORDS,
                    self::PRODUCT_TAB_STATISTICAL_ANALYSIS,
                    self::PRODUCT_TAB_ATTACHMENT,
                    self::PRODUCT_TAB_OPERATION_RECORDS,
                    self::PRODUCT_TAB_GROUP_INVENTORY,
                ],
                PrivilegeConstants::MODULE_OMS => [
                    self::PRODUCT_TAB_DETAILS,
                    self::PRODUCT_TAB_TRANSACTION_RECORDS,
                    self::PRODUCT_TAB_STATISTICAL_ANALYSIS,
                    self::PRODUCT_TAB_ATTACHMENT,
                    self::PRODUCT_TAB_SUPPLIER,
                    self::PRODUCT_TAB_OPERATION_RECORDS,
                    self::PRODUCT_TAB_GROUP_INVENTORY,
                    self::PRODUCT_TAB_INQUIRY_COLLABORATION,
                ],
            ],
        ],
    ];


}
