<?php

namespace common\library\oms\warning\setting\base;

use xiaoman\orm\common\SingleObject;

/**
 * class WarningSetting
 * @package common\library\oms\warning\setting
 * @property integer id
 * @property integer client_id
 * @property integer type
 * @property integer trigger_type
 * @property integer enable_flag
 * @property integer update_user
 * @property integer create_user
 * @property string create_time
 * @property string update_time
 * @method WarningSettingMetadata getMetadata()
 * @method WarningSettingFormatter getFormatter()
 * @method WarningSettingOperator getOperator()
 */
abstract class WarningSetting extends SingleObject
{
    use InitWarningSettingMetadata;


    public static function getMetadataClass()
    {
        return WarningSettingMetadata::class;
    }

    public function __construct(int $clientId, int $id = null)
    {
        parent::__construct($clientId);
        if (!is_null($id)) {
            $this->load(['client_id' => $clientId, 'id' => $id, 'type' => $this->getType()]);
        } else {
            //新建场景初始化默认值
            $this->initDefaultAttributes();
        }
    }

    /**
     * 初始化默认参数 仅限于实例化新的模型
     */
    protected function initDefaultAttributes()
    {
        $this->create_time = xm_function_now();
        $this->update_time = xm_function_now();
        $this->getType();
    }

    public function beforeCreate()
    {
        return parent::beforeCreate();
    }

    public function beforeUpdate()
    {
        return true;
    }

    public function afterCreate()
    {
        return true;
    }

    abstract public function getType();

}
