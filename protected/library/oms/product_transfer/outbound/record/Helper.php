<?php
/**
 * Created by PhpStorm.
 * User: onegong
 * Date: 2022/7/8
 * Time: 11:57 AM
 */

namespace common\library\oms\product_transfer\outbound\record;

use common\library\invoice\InvoiceProductRecordApi;
use common\library\oms\common\OmsConstant;
use common\library\oms\outbound_invoice\sale\record\SaleOutboundRecordFilter;
use common\library\oms\shipping_invoice\ShippingInvoiceFilter;
use common\library\product_v2\ProductConstant;
use common\library\util\Arr;
use common\models\client\InvoiceProductRecord;
use xiaoman\orm\database\data\InArray;

/**
 * Class Helper
 * @package common\library\oms\product_transfer\outbound\record
 */
class Helper
{
    public static function getOutboundCountMap($clientId, $orderIds)
    {
        $orderOutboundInfo = self::getOutboundInfoByOrder($clientId, $orderIds);
        $orderOutboundCount = [];
        foreach ($orderOutboundInfo as $outbound) {
            if (!array_key_exists($outbound['order_id'], $orderOutboundCount)) {
                $orderOutboundCount[$outbound['order_id']] = [
                    'outbound_count' => $outbound['outbound_count'],
                    'from_stock_count' => $outbound['from_stock_count'],
                ];
            } else {
                $orderOutboundCount[$outbound['order_id']]['outbound_count'] += $outbound['outbound_count'];
                $orderOutboundCount[$outbound['order_id']]['from_stock_count'] += $outbound['from_stock_count'];
            }
        }
        return $orderOutboundCount;
    }

    public static function getOutboundInfoByOrder($clientId, $orderIds)
    {
        $orderIdString = implode(',', $orderIds);
        $query = InvoiceProductRecord::model()->getDbConnection();


        $filter = new ShippingInvoiceFilter($clientId);
        $filter->skipPrivilegeCheck(true);
        $filter->order_ids = new InArray($orderIds);
        $filter->select(['order_ids','shipping_invoice_id']);
        $list = $filter->rawData();

        $orderMap = [];
        $shippingInvoiceIds = [];
        foreach ($list as $item) {
            $order_ids = $item['order_ids'];
            $shipping_invoice_id = $item['shipping_invoice_id'];
            foreach ($order_ids as $order_id) {
                if (isset($orderMap[$order_id])) {
                    $orderMap[$order_id][] = $shipping_invoice_id;
                } else {
                    $orderMap[$order_id] = [$shipping_invoice_id];
                }
                $shippingInvoiceIds[] = $shipping_invoice_id;
            }
        }

        // 本sql用于获取订单直接关联的出库数量和计算订单的出库环节进度/备货环节进度
        // outbound_count：已出库的出库数量汇总
        // from_stock_count:不区分状态的出库来源为从存货分配的出库数量汇总
        $sql = "select p.id,p.refer_id as order_id, p.count as order_count, 
       sum(case when i.status=2 then i.outbound_count else 0 end) outbound_count, 
       sum(case when o.source_type=2 then outbound_count else 0 end) as from_stock_count, 
       sum(i.outbound_count) all_outbound_count 
    from tbl_invoice_product_record as p 
    left join tbl_outbound_record as i on p.id=i.sub_refer_id and i.client_id=:clientId and i.refer_type=:referType and i.delete_flag=:deleteFlag 
    left join tbl_outbound_invoice as o on i.outbound_invoice_id=o.outbound_invoice_id and i.client_id=o.client_id 
    where p.client_id=:clientId and p.type=:type and p.product_type != :product_type 
    and p.refer_id IN (" . $orderIdString . ") and enable_flag=:enableFlag 
    group by p.id";

        $obInfoBySpInvoice = self::getOutboundInfoByShippingInvoice($clientId, $shippingInvoiceIds);

        $obInfoByOrderInvoice  = $query->createCommand($sql)->queryAll(true, [
            ':clientId' => $clientId,
            ':type' => \Constants::TYPE_ORDER,
            ':product_type' => ProductConstant::PRODUCT_TYPE_COMBINE,
            ':referType' => \Constants::TYPE_ORDER,
//            ':status' => OmsConstant::OUTBOUND_INVOICE_STATUS_FINISH,
            ':deleteFlag' => \Constants::DELETE_FLAG_FALSE,
            ':enableFlag' => \Constants::ENABLE_FLAG_TRUE,
        ]);
        foreach ($obInfoByOrderInvoice as &$elem) {
            foreach ($obInfoBySpInvoice as $spElem) {
                if ($elem['order_id'] == $spElem['order_id']
                    &&
                    $elem['id'] == $spElem['invoice_product_id']
                ) {
                    $elem['outbound_count'] += $spElem['outbound_count'] ?? 0;
                    $elem['from_stock_count'] += $spElem['from_stock_count'] ?? 0;
                    $elem['all_outbound_count'] += $spElem['all_outbound_count'] ?? 0;
                }
            }
        }
        return $obInfoByOrderInvoice;
    }

    public static function getOutboundInfoByShippingInvoice($clientId, $shippingInvoiceIds)
    {
        $shippingInvoiceIds = Arr::uniqueFilterValues($shippingInvoiceIds);
        if (empty($shippingInvoiceIds)){
            return [];
        }
        $shippingInvoiceIdString = implode(',', $shippingInvoiceIds);
        $query = InvoiceProductRecord::model()->getDbConnection();
        // 本sql用于获取出运单直接关联的出库数量和计算订单的出库环节进度/备货环节进度
        // outbound_count：已出库的出库数量汇总
        // from_stock_count:不区分状态的出库来源为从存货分配的出库数量汇总
        $sql = "select p.order_id as order_id, p.invoice_product_id,
       sum(case when i.status=2 then i.outbound_count else 0 end) outbound_count, 
       sum(case when o.source_type=2 then outbound_count else 0 end) as from_stock_count, 
       sum(i.outbound_count) all_outbound_count 
    from tbl_shipping_record as p 
    left join tbl_outbound_record as i on p.shipping_record_id=i.sub_refer_id and i.client_id=:clientId and i.refer_type=:referType and i.delete_flag=:deleteFlag 
    left join tbl_outbound_invoice as o on i.outbound_invoice_id=o.outbound_invoice_id and i.client_id=o.client_id 
    where p.client_id=:clientId  and p.product_type != :product_type 
    and p.shipping_invoice_id IN (" . $shippingInvoiceIdString . ") and enable_flag=:enableFlag 
    group by p.shipping_record_id";

        return $query->createCommand($sql)->queryAll(true, [
            ':clientId' => $clientId,
            ':type' => \Constants::TYPE_ORDER,
            ':product_type' => ProductConstant::PRODUCT_TYPE_COMBINE,
            ':referType' => \Constants::TYPE_SHIPPING_INVOICE,
            ':deleteFlag' => \Constants::DELETE_FLAG_FALSE,
            ':enableFlag' => \Constants::ENABLE_FLAG_TRUE,
        ]);
    }

    /**
     * 获取待出库数量，按照sum(销售订单产品销售数-已出库数)
     * @link https://xmkm.yuque.com/armee3/tni7hp/sa7vtxaaw9rlts8c#bFkh
     */
    public static function getOutboundCountByOrderProduct($clientId, $userId, $orderIds, $showRecord = false)
    {
        //需要去掉组合产品，只获取子产品进行计算
        $invoiceProductFields = ['id', 'refer_id', 'count', 'product_type'];
        $invoiceProductList = (new InvoiceProductRecordApi($userId))->getOrderProductList($orderIds, $invoiceProductFields, true);
        $invoiceProductId = array_column($invoiceProductList, 'id');

        //获取已出库销售出库单的出库数量
        $outboundRecordFilter = new SaleOutboundRecordFilter($clientId);
        $outboundRecordFilter->type = \Constants::TYPE_SALE_OUTBOUND_INVOICE;
        $outboundRecordFilter->sub_refer_id = $invoiceProductId;
        $outboundRecordFilter->refer_type = \Constants::TYPE_ORDER;
        $outboundRecordFilter->status = OmsConstant::OUTBOUND_INVOICE_STATUS_FINISH;
        $outboundRecordFilter->delete_flag = \Constants::DELETE_FLAG_FALSE;
        $outboundRecordFilter->select(['sub_refer_id',
            'outbound_count' => function ($column) {
                return 'sum(outbound_count) as  total_outbound_count';
            }
        ]);

        $outboundRecordFilter->groupBy('sub_refer_id');

        $outboundRecordList = array_column($outboundRecordFilter->rawData(), 'total_outbound_count', 'sub_refer_id');
        $orderTodoOutboundCountMap = array_combine($orderIds, array_fill(0, count($orderIds), ['todo_invoice_count' => 0]));
        $recordCountMap = array_combine($invoiceProductId, array_fill(0, count($invoiceProductId), ['todo_invoice_count' => 0]));
        foreach ($invoiceProductList as $invoiceProductItem) {
            if ($invoiceProductItem['product_type'] == 3) {
                continue;
            }
            $invoice_product_id = $invoiceProductItem['id'];
            $outboundCountByInventory = $outboundRecordList[$invoice_product_id] ?? 0;
            $todoOutboundCount = max($invoiceProductItem['count'] - $outboundCountByInventory, 0);
            //明细数
            $recordCountMap[$invoice_product_id]['todo_invoice_count'] = $todoOutboundCount;

            //总数
            $orderTodoOutboundCountMap[$invoiceProductItem['refer_id']]['todo_invoice_count'] += $todoOutboundCount;
        }

        if ($showRecord) {
            return $recordCountMap;
        }

        return $orderTodoOutboundCountMap;
    }
}
