<?php

namespace common\library\oms\shipping_invoice\record;

use common\library\history\base\Builder;
use common\library\history\shipping_invoice\ShippingInvoiceRecordSetting;
use common\library\history\shipping_invoice\ShippingInvoiceSetting;
use common\library\oms\common\OmsUtil;

use common\library\orm\pipeline\operator\HistoryOperatorTask;
use common\library\privilege_v3\field\PrivilegeFieldService;
use common\library\privilege_v3\PrivilegeConstants;
use xiaoman\orm\common\Operator;

/**
 * Class ShippingRecordOperator
 * @package common\library\oms\shipping_invoice\record
 */
class ShippingRecordOperator extends Operator
{
    protected array $record_list = [];

    const TASK_LIST = [
        'history' => [
            'task_class' => HistoryOperatorTask::class
        ]
    ];

    public function create(array $data, $shipping_invoice_id)
    {
        $privilegeFieldService = new PrivilegeFieldService($this->object->getDomainHandler()->getUserId(),PrivilegeConstants::FUNCTIONAL_SHIPPING_INVOICE);
        $data = $privilegeFieldService->handlePrivilegeFieldsForCreateWriteBack($data);

        if (empty($data)) return true;

        $partsProductMap = [];
        $recordList = array_reduce($data, function ($item, $value) use ($shipping_invoice_id,&$partsProductMap) {
            $value['shipping_invoice_id'] = $shipping_invoice_id;
            $value['combine_record_id'] = $value['combine_record_id'] ?? 0;
            $value['shipping_record_id'] = $value['shipping_record_id'] ?? intval(\ProjectActiveRecord::produceAutoIncrementId());
            //避免该字段为空时前端不传该字段导致报错，所以明细db字段需要防一下空值
            $value['product_name']    = $value['product_name'] ?? '';
            $value['product_cn_name'] = $value['product_cn_name'] ?? '';
            $value['product_model']   = $value['product_model'] ?? '';
            $value['unit']            = $value['unit'] ?? '';
            $value['product_image']   = $value['product_image'] ?? [];
            $value['create_time'] = xm_function_now();
            $value['update_time'] = xm_function_now();
            $value['enable_flag'] = \Constants::ENABLE_FLAG_TRUE;

            $recordRowData = OmsUtil::columnExtract($value, $this->object->getMetadata()->columnFields());

            //master_group_id 不在表字段中，但后续还需要用到，改为下面写法
            $recordRowData['master_group_id'] = $value['master_group_id'] ?? 0;
            $item[] = $recordRowData;
            if (!empty($value['is_master_product'] ?? 0) && !empty($value['master_group_id'] ?? 0)) {
                $partsProductMap[$value['master_group_id']] = $value['shipping_record_id'];
            }

            return $item;
        }, []);

        //配件产品填上主产品的id
        foreach ($recordList as &$product) {
            if ((isset($product['is_master_product']) && empty($product['is_master_product']))
                && !empty($product['master_group_id'] ?? 0)) {
                $product['master_id'] = $partsProductMap[$product['master_group_id']] ?? 0;
            }
            unset($product['master_group_id']);
        }
        unset($product);

        $this->batchInsert($recordList);
        $this->record_list = $recordList;

        $historyBuilder = new Builder(new ShippingInvoiceRecordSetting());
        $historyBuilder->setType(ShippingInvoiceSetting::TYPE_CREATE_RECORD)
            ->setClientId($this->object->getDomainHandler()->getClientId())
            ->setUpdateUser($this->object->getDomainHandler()->getUserId())
            ->initFromRawData($this->record_list, true)
            ->build();

        return true;
    }

    public function edit($data)
    {
        if (empty($data)) return true;

        $privilegeFieldService = new PrivilegeFieldService($this->object->getDomainHandler()->getUserId(),PrivilegeConstants::FUNCTIONAL_SHIPPING_INVOICE);
        $data = $privilegeFieldService->handlePrivilegeFieldsForUpdateWriteBack($data,$this->object->getOldAttributes());

        $this->onlySingle();

        $data['update_time'] = xm_function_now();
        $data = OmsUtil::columnExtract($data, $this->object->getMetadata()->columnFields());

        $this->standardProcess($data, []);

        $this->record_list = [$data];

        $historyBuilder = new Builder(new ShippingInvoiceRecordSetting());
        $historyBuilder->setType(ShippingInvoiceSetting::TYPE_EDIT_RECORD)
            ->setClientId($this->object->getDomainHandler()->getClientId())
            ->setUpdateUser($this->object->getDomainHandler()->getUserId())
            ->initFromRawData($data)
            ->setOldAttributes($this->object->getOldAttributes())
            ->build();

        return true;
    }


    public function delete($mode = \Constants::ENABLE_FLAG_FALSE)
    {
        $opUser = $this->object->getDomainHandler();
        $setValue = [
            'enable_flag' => $mode,
            'update_time' => xm_function_now()
        ];
        $this->standardProcess($setValue, [
            'history' => (new HistoryOperatorTask($this->clientId, $opUser->getUserId(), ShippingInvoiceSetting::TYPE_REMOVE_RECORD, new ShippingInvoiceRecordSetting()))->recordDelete(),
        ]);

        return true;
    }
}