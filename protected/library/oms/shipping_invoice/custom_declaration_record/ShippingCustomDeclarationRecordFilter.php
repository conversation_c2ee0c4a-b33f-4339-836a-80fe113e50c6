<?php

namespace common\library\oms\shipping_invoice\custom_declaration_record;

use common\library\oms\shipping_invoice\record\ShippingRecordFilter;


/**
 * class ShippingCustomDeclarationRecordFilter
 * @package common\library\oms\shipping_invoice\custom_declaration_record
  * @property int shipping_custom_declaration_record_id
 * @property int shipping_invoice_id
 * @property int order_id
 * @property int client_id
 * @property int invoice_product_id
 * @property string package_unit
 * @property float sale_price
 * @property float sale_count
 * @property float shipping_cost_amount
 * @property float count_per_package
 * @property string package_remark
 * @property float shipping_count
 * @property float product_net_weight
 * @property float package_gross_weight
 * @property string package_volume
 * @property float package_size_length
 * @property float package_size_weight
 * @property float package_size_height
 * @property float product_size_length
 * @property float product_size_weight
 * @property float product_size_height
 * @property float product_volume
 * @property float carton_size_length
 * @property float carton_size_weight
 * @property float carton_size_height
 * @property float carton_volume
 * @property float carton_gross_weight
 * @property float carton_net_weight
 * @property string description
 * @property string remark
 * @property float count_per_carton
 * @property string container_type
 * @property string container_no
 * @property string container_seal_number
 * @property string hs_code
 * @property string customs_name
 * @property string customs_cn_name
 * @property mixed external_field_data
 * @property int enable_flag
 * @property mixed create_time
 * @property mixed update_time
 * @property int product_id
 * @property int sku_id
 * @property int product_type
 * @property int combine_record_id
 * @property int record_group_id
 * @property int sort
 * @property mixed package_type
 * @property float carton_count
 * @method BatchShippingCustomDeclarationRecord find()
 * @method ShippingCustomDeclarationRecordMetadata getMetadata()
 */
class ShippingCustomDeclarationRecordFilter extends ShippingRecordFilter
{
    use InitShippingCustomDeclarationRecordMetadata;

    public static function getMetadataClass()
    {
        return ShippingCustomDeclarationRecordMetadata::class;
    }

}