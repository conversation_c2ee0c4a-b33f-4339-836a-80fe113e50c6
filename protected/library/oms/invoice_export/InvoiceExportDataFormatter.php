<?php

namespace common\library\oms\invoice_export;

use common\library\account\Client;
use common\library\account\UserInfo;
use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldExportService;
use common\library\customer_v3\company\orm\Company;
use common\library\customer_v3\customer\orm\Customer;
use common\library\inquiry_collaboration\export\InquiryCollaborationExportFormatter;
use common\library\inquiry_collaboration\InquiryCollaboration;
use common\library\invoice\export\template\InvoiceExportTemplateConstant;
use common\library\lead_v2\LeadCustomer;
use common\library\oms\inbound_invoice\other\export\OtherInboundExportFormatter;
use common\library\oms\inbound_invoice\other\OtherInboundInvoice;
use common\library\oms\inbound_invoice\purchase\export\PurchaseInboundExportFormatter;
use common\library\oms\inbound_invoice\purchase\PurchaseInboundInvoice;
use common\library\oms\outbound_invoice\other\export\OtherOutboundExportFormatter;
use common\library\oms\outbound_invoice\other\OtherOutboundInvoice;
use common\library\oms\outbound_invoice\sale\export\SalesOutboundExportFormatter;
use common\library\oms\outbound_invoice\sale\SaleOutboundInvoice;
use common\library\oms\payment_invoice\export\PaymentInvoiceExportFormatter;
use common\library\oms\payment_invoice\PaymentInvoice;
use common\library\oms\quotation\export\QuotationExportFormatter;
use common\library\oms\shipping_invoice\export\ShippingInvoiceExportFormatter;
use common\library\oms\shipping_invoice\ShippingConstant;
use common\library\oms\shipping_invoice\ShippingInvoice;
use common\library\oms\warehouse_return_invoice\purchase\export\PurchaseReturnExportFormatter;
use common\library\oms\warehouse_return_invoice\purchase\PurchaseReturnInvoice;
use common\library\product\AlibabaCategoryAttr;
use common\library\product_v2\ProductConstant;
use common\library\product_v2\sku\SkuAPI;
use common\library\util\Arr;
use common\library\util\PgsqlUtil;
use common\library\wecom\api\User;
use common\models\client\ClientProduct;
use CustomerOptionService;

/**
 * 导出给java数据格式化
 */
class InvoiceExportDataFormatter
{

    /** @var PurchaseInboundInvoice|OtherInboundInvoice|SaleOutboundInvoice|OtherOutboundInvoice|PurchaseReturnInvoice|ShippingInvoice|\common\library\oms\quotation\Quotation |InquiryCollaboration|PaymentInvoice*/
    protected $invoiceObject;

    protected $clientId;
    protected $userId;
    protected $type;
    protected $exportFormatter;

    protected array $invoiceData = [];
    protected int|null $showRecordType = null;

    //过滤别的明细数据
    protected $filterProductListMap = [
        \Constants::TYPE_SHIPPING_INVOICE => [
            InvoiceExportTemplateConstant::SHIPPING_RECORD_COMMON => [ShippingConstant::LIST_BY_PACKING, ShippingConstant::LIST_BY_DECLARATION,],
            InvoiceExportTemplateConstant::SHIPPING_RECORD_PACKING => [ShippingConstant::LIST_BY_RECORD, ShippingConstant::LIST_BY_DECLARATION,],
            InvoiceExportTemplateConstant::SHIPPING_RECORD_CUSTOM_DECLARATION => [ShippingConstant::LIST_BY_RECORD, ShippingConstant::LIST_BY_PACKING,],
        ]
    ];



    public function __construct($invoiceObject)
    {
        if (empty($invoiceObject)) {
            throw new \RuntimeException(\Yii::t('invoice', '无对象传入'));
        }
        $this->invoiceObject = $invoiceObject;
        $this->clientId = $invoiceObject->client_id;
        $this->userId = $this->invoiceObject->getDomainHandler()->getUserId();
        $this->judgeObjectsType($invoiceObject);
    }


    protected function judgeObjectsType($invoiceObject)
    {
        $className = (new \ReflectionClass($invoiceObject))->getShortName();

        $clientId = $this->invoiceObject->client_id;
        $userId = $this->invoiceObject->getDomainHandler()->getUserId();

        switch ($className) {
            case 'PurchaseInboundInvoice':
                $this->type = \Constants::TYPE_PURCHASE_INBOUND_INVOICE;
                $this->exportFormatter =  new PurchaseInboundExportFormatter($clientId, $userId);
                break;
            case 'OtherInboundInvoice':
                $this->type = \Constants::TYPE_OTHER_INBOUND_INVOICE;
                $this->exportFormatter =  new OtherInboundExportFormatter($clientId, $userId);
                break;
            case 'SaleOutboundInvoice':
                $this->type = \Constants::TYPE_SALE_OUTBOUND_INVOICE;
                $this->exportFormatter =  new SalesOutboundExportFormatter($clientId, $userId);
                break;
            case 'OtherOutboundInvoice':
                $this->type = \Constants::TYPE_OTHER_OUTBOUND_INVOICE;
                $this->exportFormatter =  new OtherOutboundExportFormatter($clientId, $userId);
                break;
            case 'PurchaseReturnInvoice':
                $this->type = \Constants::TYPE_PURCHASE_RETURN_INVOICE;
                $this->exportFormatter =  new PurchaseReturnExportFormatter($clientId, $userId);
                break;
            case 'ShippingInvoice':
                $this->type = \Constants::TYPE_SHIPPING_INVOICE;
                $this->exportFormatter =  new ShippingInvoiceExportFormatter($clientId, $userId);
                break;
            case 'Quotation':
                $this->type = \Constants::TYPE_QUOTATION;
                $this->exportFormatter =  new QuotationExportFormatter($clientId, $userId);
                break;
            case 'InquiryCollaboration':
                $this->type = \Constants::TYPE_INQUIRY_COLLABORATION;
                $this->exportFormatter =  new InquiryCollaborationExportFormatter($clientId, $userId);
                break;
            case 'PaymentInvoice':
                $this->type = \Constants::TYPE_PAYMENT_INVOICE;
                $this->exportFormatter =  new PaymentInvoiceExportFormatter($clientId, $userId);
                break;
        }
    }

    //这里封装起来，通过type 来判断不同的
    protected function buildExportMapData()
    {
        //获取信息
        $this->invoiceObject->getFormatter()->exportSetting();

        $clientId = $this->invoiceObject->client_id;
        $userId = $this->invoiceObject->getDomainHandler()->getUserId();
        $data = $this->invoiceObject->getAttributes();

        $createUserId = $data['create_user'];

        $exchangeProductList = [];
        $productPageList = [];

        //导出数据格式化
        $this->exportFormatter->setNeedStrip(false);
        $this->exportFormatter->setData($data);
        $this->invoiceData = $data = $this->exportFormatter->result();


        $filterRecord = $this->filterProductListMap[$this->type][$this->showRecordType] ?? [];
        foreach ($filterRecord as $record) {
            unset($data[$record]);
        }

        //装箱明细
        $packingList = $data['packing_list']??[];
        //报关明细
        $customDeclareList = $data['declaration_list'] ?? [];

        //供应商
        $supplier = $data['supplier_info'] ?? [];
        //供应商联系人
        $supplierContact = $data['supplier_contact_info'] ?? [];
        //客户
        $companyId = $data['company_id'] ?? 0;
        $customerId = $data['customer_id'] ?? 0;

        //关联客户
        $company = [];
        $data['company_id'] = '';
        if( $companyId > 0 ){

            $company = (new Company($this->clientId))->getOldCompanyAttributes($companyId);

            $company['origin_list'] = (\CustomerOptionService::getOriginName($this->clientId, $company['origin_list'] ?? [])) ?? '';

            $company['group_id'] = isset($company['group_id'])
                ? \common\library\group\Helper::getGroupName(\Constants::TYPE_COMPANY, $company['group_id'])
                : '';
            $company['trail_status'] = isset($company['trail_status'])
                ? (\CustomerOptionService::getStatusMap($this->clientId,
                    [$company['trail_status'] ?? '']))[ $company['trail_status']]??''
                : '';
            $company['cus_tag'] = isset($company['tag'][$this->userId]) ? \CustomerOptionService::getTagName($this->clientId, $this->userId, \common\library\setting\library\tag\Tag::TYPE_COMPANY, $company['tag'][$this->userId]) : '';
            //时区
            $company['timezone']  = isset($company['timezone'])?(\CustomerOptionService::TIMEZONE_MAP[$company['timezone']]??'未知'):'';
            //采购意向
            $company['intention_level']  = isset($company['intention_level'])?(CustomerOptionService::intentionLevelMap()[$company['intention_level']] ?? ''):'';
            //年采购额
            $company['annual_procurement'] =  isset($company['annual_procurement'])?(CustomerOptionService::annualProcurementMap()[$company['annual_procurement']]?? ''):'';
            //客户类型
            $company['biz_type'] =  isset($company['biz_type'])?(CustomerOptionService::bizTypeMap()[$company['biz_type']] ?? ''):'';
            //客户规模
            $scale =  isset($company['scale_id'])?(CustomerOptionService::SCALE_MAP[$company['scale_id']] ?? []):[];
            $company['scale_id']  = empty($scale) ? '-' : $scale['min'] . '-' . $scale['max'];
            //客户星级
            $company['star'] =  isset($company['star'])?\CustomerOptionService::starMap()[$company['star']] ?? '':'';
            //客户座机
            $data['company_id'] =  $company['name'] ?? '';

        }
        //关联客户联系人
        $customer = [];
        $data['customer_id'] = '';
        if( $customerId > 0 ){
            $customer = new Customer($this->clientId, $customerId);
            if($customer->isNew()){
                $customer = new LeadCustomer($this->clientId);
                $customer->loadById($customerId);
                $customer = $customer->isExist()?$customer->getRawData() : [];
            }else{
                $customer = $customer->getAttributes();
            }
            if($customer){
                $customer['gender'] = $customer['gender']==Customer::GENDER_TYPE_MAN?'男':($customer['gender'] == Customer::GENDER_TYPE_WOMAN?'女':'未知');
                $customer['post_grade'] = Customer::POST_GRADE_NAME[$customer['post_grade']]??'';
            }
            $data['customer_id'] = $customer['name'] ?? '';
        }


        //交易产品 特定类型明细是单据
        if ($this->type == \Constants::TYPE_PAYMENT_INVOICE) {
            if (isset($data['record_list']) && !empty($data['record_list'])) {
                $i = 0;
                foreach ($data['record_list'] as $key => $product) {
                    $product['invoice_product_no'] = ++$i;
                    $exchangeProductList[] = $product;
                }
            }
        } else {

        if( isset($data['record_list']) && !empty($data['record_list']) ){
            $skuIds = array_column($data['record_list'], 'sku_id');
            $skuApi = new SkuAPI($clientId, $userId);
            $productSkuMap = array_column($skuApi->items($skuIds), null, 'sku_id');

            $productIds = Arr::uniqueFilterValues(array_column($data['record_list'],'product_id'));
            $productPageResult = !empty($productIds) ? ClientProduct::findAllByProductIds($clientId, $productIds) : [];

            $productPageMap = [];
            $productGroupNameMap = [];
            if( !empty($productPageResult) ){
                $productPageMap = array_combine(array_column($productPageResult,'product_id'),$productPageResult);
                $productGroupIds = array_column($productPageResult,'group_id');
                $productGroupNameMap = \common\library\group\Helper::getGroupNameMap($clientId,\Constants::TYPE_PRODUCT,$productGroupIds);
            }

            $i=1;
            foreach ( $data['record_list'] as $key => $product ){
                $checkSkuId = $product['sku_id'] ?? 0;
                $product['invoice_product_no']=$i;
                // 平台产品
                $skuStr = is_array($product['sku_attributes']) ? implode(";\n", $product['sku_attributes']) : $product['sku_attributes'];
                $product['sku_id'] = $skuStr;
                $exchangeProductList[] = $product;

                $productPage = $productPageMap[$product['product_id']]??null;
                if( $productPage ){
                    $productPage =  $productPage->getAttributes();

                    //特殊处理产品图片
                    if(isset($productPage['images'])){
                        $productPage['images'] = json_decode($productPage['images'], true);
                        for($imgNo = 0; $imgNo <= 6; $imgNo++){
                            $imgIndexNo = $imgNo+1;
                            $imgIndex = "images_{$imgIndexNo}";

                            $productPage[$imgIndex] = $productPage['images'][$imgNo]['src'] ?? '';

                            $imageId = $productPage['images'][$imgNo]['id'] ?? 0;
                            if($imageId > 0){//获取内网url
                                $aliUpload = new \AliyunUpload();
                                if($aliUpload->loadByFileId($imageId)){
                                    $productPage[$imgIndex] = $aliUpload->getFileUrl(true);
                                }
                            }
                        }
                    }
                    unset($productPage['images']);

                    //产品属性字段
                    $categoryIds =PgsqlUtil::trimArray($productPage['category_ids']);
                    $categoryId = end($categoryIds);

                    $categoryAttr = new AlibabaCategoryAttr($categoryId);
                    $productPage['info_json'] = $categoryAttr->formatFieldInfoJsonForCategoryId($productPage['info_json']??[],$categoryId);
                    $productPage['info_json'] = $categoryAttr->formatFieldInfoJson($productPage['info_json']);
                    //产品分组
                    if(isset($productPage['group_id'])) {
                        $productPage['group_id'] = $productGroupNameMap[$productPage['group_id']]??'';
                    }
                    //自定义字段
                    if (isset($productPage['external_field_data']) && !is_array($productPage['external_field_data'])) {
                        $productPage['external_field_data'] = json_decode($productPage['external_field_data'], true);
                    }

                    // 如果产品是多规格的，需要用sku的自定义字段覆盖spu的
                    $skuExternalFieldData = [];
                    if ((($productPage['product_type'] ?? 0) == ProductConstant::PRODUCT_TYPE_SKU) && !empty($productSkuMap[$checkSkuId])) {
                        $skuExternalFieldData = $productSkuMap[$checkSkuId]['external_field_data'] ?? [];
                    }
                    foreach ($productPage as $productFieldId => $value) {
                        // 特殊取SKU的包装说明覆盖SPU的包装说明
                        if (
                            $productFieldId == 'package_remark'
                            && (($productPage['product_type'] ?? 0) == ProductConstant::PRODUCT_TYPE_SKU)
                            && !empty($productSkuMap[$checkSkuId]) && isset($productSkuMap[$checkSkuId][$productFieldId])
                        ) {
                            $productPage[$productFieldId] = $productSkuMap[$checkSkuId][$productFieldId];
                        }
                    }
                    foreach ($productPage['external_field_data'] ?? [] as $productFieldId => $v) {
                        // sku有的进行替换
                        if (!isset($skuExternalFieldData[$productFieldId])) {
                            continue;
                        }
                        $productPage['external_field_data'][$productFieldId] = $skuExternalFieldData[$productFieldId];
                    }
                    $productPage['category_ids'] = PgsqlUtil::trimArray($productPage['category_ids']);

                    $productPage['sku_id'] = $skuStr;
                }

                $productPageList[] = $productPage;

                $i++;
            }
        }

        }

        //费用信息
        $costList = [];
        if (isset($data['cost_list']) && !empty($data['cost_list'])) {

            $costList = $data['cost_list'];
        }

        unset($data['record_list']);
        unset($data['file_list']);

        //添加userExternalInfo
        $user = \User::getLoginUser();
        $externalFields = [
            'external_email',
            'external_mobile',
            'external_fax',
            'external_address',
            'external_other'
        ];
        $userInfo = new UserInfo($user->getUserId(), $user->getClientId());
        $userExternalMap = $userInfo->getExtentAttributes($externalFields);
        $userExternalMap['nickname'] = $userInfo->nickname;

        $createExternalMap = [];
        $createUserInfo = new UserInfo($createUserId, $user->getClientId());
        if (!$createUserInfo->isNew())
        {
            $createExternalMap = $createUserInfo->getExtentAttributes($externalFields);
            $createExternalMap['create_user_nickname'] = $createUserInfo->nickname;
        }
        $userExternalMap['create_user_nickname'] = $createExternalMap['create_user_nickname'] ?? '';
        $userExternalMap['create_user_email'] = $createExternalMap['external_email'] ?? '';
        $userExternalMap['create_user_mobile'] = $createExternalMap['external_mobile'] ?? '';
        $userExternalMap['create_user_fax'] = $createExternalMap['external_fax'] ?? '';
        $userExternalMap['create_user_address'] = $createExternalMap['external_address'] ?? '';
        $userExternalMap['create_user_other'] = $createExternalMap['external_other'] ?? '';

        $client = Client::getClient($user->getClientId());

        $enterpriseInfo = [
            'short_name' => $client->name ?? '',
            'tel' => $client->tel ?? '',
            'homepage' => $client->homepage ?? '',
            'email' => $client->email ?? '',
            'address' => $client->address ?? '',
        ];

        $groupDataMap = $this->getGroupDataMap($data,$supplier,$supplierContact,$company,$customer,$exchangeProductList,
            $productPageList,$userExternalMap,$enterpriseInfo,$costList,$packingList,$customDeclareList);

        return $groupDataMap;
    }


    /**
     * 根据导出字典导出数据
     * @return array
     */
    public function formatExportInfo()
    {

        $data = [];
        $exportFieldService = new FieldExportService($this->clientId, $this->type);
        $groupExportField = $exportFieldService->generateDictionary();

        $exportMapData = $this->buildExportMapData();
        foreach ($groupExportField as $group) {

            $groupId = $group['id'];
            if (isset($group['subs'])) {
                foreach ($group['subs'] as $subGroup) {

                    $subGroupId = $subGroup['id'];
                    $key = $exportMapData[$subGroupId]['key'] ?? "";
                    $groupData = $exportMapData[$subGroupId]['data'] ?? [];
                    $data[$key] = $data[$key] ?? [];

                    if ($subGroup['is_list'] == 1) {

                        foreach ($groupData as $itemId => $itemData) {

                            $formatGroupItemData = $this->formatExportFieldList($subGroup['fields'], $itemData);
                            $data[$key][$itemId] = array_merge($data[$key][$itemId] ?? [], $formatGroupItemData);
                        }

                    } else {

                        $formatGroupData = $this->formatExportFieldList($subGroup['fields'], $groupData);
                        $data[$key] = array_merge($data[$key] ?? [], $formatGroupData);

                    }

                }
            } else {

                $key = $exportMapData[$groupId]['key'] ?? "";
                $groupData = $exportMapData[$groupId]['data'] ?? [];
                $data[$key] = $data[$key] ?? [];

                if ($group['is_list'] == 1) {

                    foreach ($groupData as $itemId => $itemData) {

                        $formatGroupItemData = $this->formatExportFieldList($group['fields'], $itemData);
                        $data[$key][$itemId] = array_merge($data[$key][$itemId] ?? [], $formatGroupItemData);
                    }

                } else {

                    $formatGroupData = $this->formatExportFieldList($group['fields'], $groupData);
                    $data[$key] = array_merge($data[$key] ?? [], $formatGroupData);

                }

            }
        }
        return $data;

    }

    /**
     * 格式化导出子端列表
     * @param array $fieldList
     * @param array $formatFieldMap
     * @param $data
     * @return array
     */
    private function formatExportFieldList(array $fieldList, $data, $formatFieldMap = [])
    {

        $result = [];
        foreach ($fieldList as $field) {

            $value = [];

            if ($field['base'] == 1) {
                foreach ($field['columns'] as $k) {
                    if (empty($k)) {
                        $k = $field['id'] ?? '';
                        $value[$k] = $data[$k] ?? '';
                    }else{
                        $value[$k] = $data[$k] ?? '';
                    }
                }
            } else {

                foreach ($field['columns'] as $k) {
                    if (!isset($data['external_field_data'][$k]) && $field['field_type'] == CustomFieldService::FIELD_TYPE_QUOTE_FIELDS && isset($data[$k])) {
                        $value[$k] = $data[$k];
                    } else {
                        $value[$k] = $data['external_field_data'][$k]??($data[$k]??'');
                    }
                }

            }

            if (count($value) <= 1) {
                $value = current($value);
            }

            // 附件和图片类字段数据，需格式化文件信息列表
            if (isset($field['field_type']) && is_array($value) && in_array($field['field_type'], [CustomFieldService::FIELD_TYPE_IMAGE])) {
                $value = $this->formatFileList($value, 0, 1);
                $currentValue = current($value);
                if (isset($value['file_url']) && !empty($value['file_url'])) {
                    $value = $value['file_url'];
                } else {
                    $value = !empty($currentValue) ? $currentValue['file_url'] ?? '' : '';
                }
            } elseif (isset($field['field_type']) && $field['field_type'] == CustomFieldService::FIELD_TYPE_ATTACH) {
                $value = '';
            }

            $result[$field['code']] = [
                'id' => $field['id'] ?? '',
                'field_type' => $field['field_type'] ?? "1",
                'relation_field' => $field['relation_origin_field'] ?? 1,
                'relation_field_type' => $field['relation_origin_type'] ?? 0,
                'value' => $value,
                'type' => $field['type'] ?? 0
            ];
        }

        return $result;

    }

    /**
     * 格式化文件列表
     * @param array $list
     * @param int $offset
     * @param int $limit
     * @return array
     */
    public function formatFileList(array $list, $offset = 0, $limit = 0)
    {

        \ArrayUtil::multisort($list, 'create_time', SORT_DESC);

        $fileList = [];
        if ($limit) {
            $list = array_slice($list, $offset, $limit);
        }
        foreach ($list as $elem) {

            if (!isset($elem['file_id']))
                continue;

            $file = new \AliyunUpload();
            $file->loadByFileId($elem['file_id']);
            $file_obj = $file->getFileObject();

            $elem['file_name'] = $file->getFileName();
            $elem['file_size'] = $file->getFileSize();
            $elem['file_url'] = $file->getFileUrl();
            $elem['preview_url'] = $file->getPreview();
            $elem['bucket'] = $file->getBucket();
            $elem['file_key'] = $file->getFileKey();
            $elem['ETag'] = '';//已废弃，ios还有这个字段兼容处理返回空（后续版本让ios删掉，Been 2018-10-26）
            $elem['mime_type'] = $file_obj->file_mime;
            $elem['target_type'] = '2';

            if (isset($elem['create_user']) && is_array($elem['create_user'])) {
                $elem['create_user'] = $elem['create_user']['user_id'];
            }

            $userInfo = new UserInfo($elem['create_user'] ?? ($elem['user_id']??\User::getLoginUser()->getUserId()), $this->clientId);
            $user = [
                'user_id' => $userInfo['user_id'],
                'nickname' => $userInfo['nickname'],
                'avatar' => $userInfo['avatar']
            ];

            $elem['create_user'] = $user;

            $fileList[] = $elem;

        }

        return $fileList;

    }

    //单据返回字段数据
    protected function getGroupDataMap($data, $supplier, $supplierContact, $company, $customer, $exchangeProductList,
                                       $productPageList, $userExternalMap, $enterpriseInfo, $costList,
                                       $packingList = [], $customDeclareList = [])
    {
        $groupDataMap = [
            \Constants::TYPE_PURCHASE_INBOUND_INVOICE=>[
                FieldExportService::PURCHASE_INBOUND_INVOICE_GROUP_BASIC => [
                    'key' => 'base',
                    'data' => $data,
                ],
                FieldExportService::PURCHASE_INBOUND_INVOICE_GROUP_SUPPLIER_INFO => [
                    'key' => 'base',
                    'data' => $supplier,
                ],
                FieldExportService::PURCHASE_INBOUND_INVOICE_GROUP_SUPPLIER_CONTACT => [
                    'key' => 'base',
                    'data' => $supplierContact,
                ],
                FieldExportService::PURCHASE_INBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT => [
                    'key' => 'product_list',
                    'data' => $exchangeProductList,
                ],
                FieldExportService::PURCHASE_INBOUND_INVOICE_GROUP_PRODUCT_PAGE => [
                    'key' => 'product_list',
                    'data' => $productPageList,
                ],
                FieldExportService::PURCHASE_INBOUND_INVOICE_GROUP_EXTERNAL_INFO => [
                    'key' => 'base',
                    'data' => $userExternalMap,
                ],
                FieldExportService::PURCHASE_INBOUND_INVOICE_GROUP_ENTERPRISE_INFO => [
                    'key' => 'base',
                    'data' => $enterpriseInfo,
                ],
            ],
            \Constants::TYPE_OTHER_INBOUND_INVOICE=>[
                FieldExportService::OTHER_INBOUND_INVOICE_GROUP_BASIC => [
                    'key' => 'base',
                    'data' => $data,
                ],
                FieldExportService::OTHER_INBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT => [
                    'key' => 'product_list',
                    'data' => $exchangeProductList,
                ],
                FieldExportService::OTHER_INBOUND_INVOICE_GROUP_PRODUCT_PAGE => [
                    'key' => 'product_list',
                    'data' => $productPageList,
                ],
                FieldExportService::OTHER_INBOUND_INVOICE_GROUP_EXTERNAL_INFO => [
                    'key' => 'base',
                    'data' => $userExternalMap,
                ],
                FieldExportService::OTHER_INBOUND_INVOICE_GROUP_ENTERPRISE_INFO => [
                    'key' => 'base',
                    'data' => $enterpriseInfo,
                ],
            ],
            \Constants::TYPE_SALE_OUTBOUND_INVOICE=>[
                FieldExportService::SALE_OUTBOUND_INVOICE_GROUP_BASIC => [
                    'key' => 'base',
                    'data' => $data,
                ],
                FieldExportService::SALE_OUTBOUND_INVOICE_GROUP_COMPANY => [
                    'key' => 'base',
                    'data' => $company,
                ],
                FieldExportService::SALE_OUTBOUND_INVOICE_GROUP_CUSTOMER => [
                    'key' => 'base',
                    'data' => $customer,
                ],
                FieldExportService::SALE_OUTBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT => [
                    'key' => 'product_list',
                    'data' => $exchangeProductList,
                ],
                FieldExportService::SALE_OUTBOUND_INVOICE_GROUP_PRODUCT_PAGE => [
                    'key' => 'product_list',
                    'data' => $productPageList,
                ],
                FieldExportService::SALE_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO => [
                    'key' => 'base',
                    'data' => $userExternalMap,
                ],
                FieldExportService::SALE_OUTBOUND_INVOICE_GROUP_ENTERPRISE_INFO => [
                    'key' => 'base',
                    'data' => $enterpriseInfo,
                ],
            ],
            \Constants::TYPE_OTHER_OUTBOUND_INVOICE=>[
                FieldExportService::OTHER_OUTBOUND_INVOICE_GROUP_BASIC => [
                    'key' => 'base',
                    'data' => $data,
                ],
                FieldExportService::OTHER_OUTBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT => [
                    'key' => 'product_list',
                    'data' => $exchangeProductList,
                ],
                FieldExportService::OTHER_OUTBOUND_INVOICE_GROUP_PRODUCT_PAGE => [
                    'key' => 'product_list',
                    'data' => $productPageList,
                ],
                FieldExportService::OTHER_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO => [
                    'key' => 'base',
                    'data' => $userExternalMap,
                ],
                FieldExportService::OTHER_OUTBOUND_INVOICE_GROUP_ENTERPRISE_INFO => [
                    'key' => 'base',
                    'data' => $enterpriseInfo,
                ],
            ],
            \Constants::TYPE_PURCHASE_RETURN_INVOICE=>[
                FieldExportService::PURCHASE_RETURN_INVOICE_GROUP_BASIC => [
                    'key' => 'base',
                    'data' => $data,
                ],
                FieldExportService::PURCHASE_RETURN_INVOICE_GROUP_SUPPLIER_INFO => [
                    'key' => 'base',
                    'data' => $supplier,
                ],
                FieldExportService::PURCHASE_RETURN_INVOICE_GROUP_SUPPLIER_CONTACT => [
                    'key' => 'base',
                    'data' => $supplierContact,
                ],
                FieldExportService::PURCHASE_RETURN_INVOICE_GROUP_EXCHANGE_PRODUCT => [
                    'key' => 'product_list',
                    'data' => $exchangeProductList,
                ],
                FieldExportService::PURCHASE_RETURN_INVOICE_GROUP_PRODUCT_PAGE => [
                    'key' => 'product_list',
                    'data' => $productPageList,
                ],
                FieldExportService::PURCHASE_RETURN_INVOICE_GROUP_EXTERNAL_INFO => [
                    'key' => 'base',
                    'data' => $userExternalMap,
                ],
                FieldExportService::PURCHASE_RETURN_INVOICE_GROUP_ENTERPRISE_INFO => [
                    'key' => 'base',
                    'data' => $enterpriseInfo,
                ],
            ],
            \Constants::TYPE_SHIPPING_INVOICE=>[
                FieldExportService::SHIPPING_INVOICE_GROUP_BASIC => [
                    'key' => 'base',
                    'data' => $data,
                ],
                FieldExportService::SHIPPING_INVOICE_GROUP_SHIPPING_TRANSPORT => [
                    'key' => 'base',
                    'data' => $data,
                ],
                FieldExportService::SHIPPING_INVOICE_GROUP_TIME_INFO => [
                    'key' => 'base',
                    'data' => $data,
                ],
                FieldExportService::SHIPPING_INVOICE_GROUP_PRODUCT_BASE => [
                    'key' => 'product_list',
                    'data' => $exchangeProductList,
                ],
                FieldExportService::SHIPPING_INVOICE_GROUP_PACKING_BASE => [
                    'key' => 'product_list',
                    'data' => $packingList,
                ],
                FieldExportService::SHIPPING_INVOICE_GROUP_CUSTOM_DECLARATION_BASE => [
                    'key' => 'product_list',
                    'data' => $customDeclareList,
                ],
                FieldExportService::SHIPPING_INVOICE_GROUP_PRODUCT_ADDITION_FEE => [
                    'key' => 'cost_list',
                    'data' => $costList,
                ],
                FieldExportService::SHIPPING_INVOICE_GROUP_EXTERNAL_INFO => [
                    'key' => 'base',
                    'data' => $userExternalMap,
                ],
                FieldExportService::SHIPPING_INVOICE_GROUP_ENTERPRISE_INFO => [
                    'key' => 'base',
                    'data' => $enterpriseInfo,
                ],
                FieldExportService::SHIPPING_INVOICE_GROUP_COMPANY => [
                    'key' => 'base',
                    'data' => $company,
                ],
                FieldExportService::SHIPPING_INVOICE_GROUP_CUSTOMER => [
                    'key' => 'base',
                    'data' => $customer,
                ],
            ],
            \Constants::TYPE_QUOTATION=> [
                FieldExportService::QUOTATION_GROUP_BASIC => [
                    'key' => 'base',
                    'data' => $data,
                ],
                FieldExportService::QUOTATION_GROUP_COMPANY => [
                    'key' => 'base',
                    'data' => $company,
                ],
                FieldExportService::QUOTATION_GROUP_CUSTOMER => [
                    'key' => 'base',
                    'data' => $customer,
                ],
                FieldExportService::QUOTATION_GROUP_EXCHANGE_PRODUCT => [
                    'key' => 'product_list',
                    'data' => $exchangeProductList,
                ],
                FieldExportService::QUOTATION_GROUP_PRODUCT_PAGE => [
                    'key' => 'product_list',
                    'data' => $productPageList,
                ],
                FieldExportService::QUOTATION_GROUP_ADDITION_FEE => [
                    'key' => 'cost_list',
                    'data' => $costList,
                ],
                FieldExportService::QUOTATION_GROUP_EXTERNAL_INFO => [
                    'key' => 'base',
                    'data' => $userExternalMap,
                ],
                FieldExportService::QUOTATION_GROUP_ENTERPRISE_INFO => [
                    'key' => 'base',
                    'data' => $enterpriseInfo,
                ],
                ],
                \Constants::TYPE_INQUIRY_COLLABORATION=> [
                    FieldExportService::INQUIRY_COLLABORATION_GROUP_BASIC => [
                        'key' => 'base',
                        'data' => $data,
                    ],
                    FieldExportService::INQUIRY_COLLABORATION_GROUP_COMPANY => [
                        'key' => 'base',
                        'data' => $company,
                    ],
                    FieldExportService::INQUIRY_COLLABORATION_GROUP_CUSTOMER => [
                        'key' => 'base',
                        'data' => $customer,
                    ],
                    FieldExportService::INQUIRY_COLLABORATION_GROUP_EXCHANGE_PRODUCT => [
                        'key' => 'product_list',
                        'data' => $exchangeProductList,
                    ],
                    FieldExportService::INQUIRY_COLLABORATION_GROUP_PRODUCT_PAGE => [
                        'key' => 'product_list',
                        'data' => $productPageList,
                    ],
                    FieldExportService::INQUIRY_COLLABORATION_GROUP_STATISTICS => [
                        'key' => 'base',
                        'data' => $data,
                    ],
                    FieldExportService::INQUIRY_COLLABORATION_GROUP_SYS => [
                        'key' => 'base',
                        'data' => $data,
                    ],
                    FieldExportService::INQUIRY_COLLABORATION_GROUP_EXTERNAL_INFO => [
                        'key' => 'base',
                        'data' => $userExternalMap,
                    ],
                    FieldExportService::INQUIRY_COLLABORATION_GROUP_ENTERPRISE_INFO => [
                        'key' => 'base',
                        'data' => $enterpriseInfo,
                    ],
                ],
            \Constants::TYPE_PAYMENT_INVOICE => [
                FieldExportService::PAYMENT_INVOICE_GROUP_BASIC => [
                    'key' => 'base',
                    'data' => $data,
                ],
                FieldExportService::PAYMENT_INVOICE_GROUP_SYSTEM => [
                    'key' => 'base',
                    'data' => $data,
                ],
                FieldExportService::PAYMENT_INVOICE_GROUP_RECORD => [
                    'key' => 'product_list',
                    'data' => $exchangeProductList,
                ],
                FieldExportService::PAYMENT_INVOICE_GROUP_PAYMENT => [
                    'key' => 'base',
                    'data' => $data,
                ],
                FieldExportService::PAYMENT_INVOICE_GROUP_COMPANY => [
                    'key' => 'base',
                    'data' => $company,
                ],
                FieldExportService::PAYMENT_INVOICE_GROUP_CUSTOMER => [
                    'key' => 'base',
                    'data' => $customer,
                ],
                FieldExportService::PAYMENT_INVOICE_GROUP_SUPPLIER_INFO => [
                    'key' => 'base',
                    'data' => $supplier,
                ],
                FieldExportService::PAYMENT_INVOICE_GROUP_SUPPLIER_CONTACT => [
                    'key' => 'base',
                    'data' => $supplierContact,
                ],
                FieldExportService::PAYMENT_INVOICE_GROUP_EXTERNAL_INFO => [
                    'key' => 'base',
                    'data' => $userExternalMap,
                ],
                FieldExportService::PAYMENT_INVOICE_GROUP_ENTERPRISE_INFO => [
                    'key' => 'base',
                    'data' => $enterpriseInfo,
                ],
            ]
        ];
        return $groupDataMap[$this->type];
    }

    /**
     * @param int $showRecordType
     */
    public function setShowRecordType(int $showRecordType): void
    {
        $this->showRecordType = $showRecordType;
    }
}
