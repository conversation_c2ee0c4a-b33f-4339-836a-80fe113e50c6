<?php

namespace common\library\oms\outbound_invoice\record;

use common\library\oms\common\OmsUtil;
use common\library\oms\order_link\trigger\SaleOutboundTrigger;
use common\library\oms\outbound_invoice\OutboundInvoiceRecordSetting;
use common\library\oms\product_transfer\outbound\relation\BatchOutboundProductTransferRelation;
use common\library\oms\product_transfer\purchase\relation\BatchTransferOutboundRelation;
use common\library\oms\product_transfer\purchase\relation\PurchaseProductTransferRelationFilter;
use common\library\oms\traits\CompareDiffTrait;
use common\library\oms\warehouse\API as WarehouseAPI;
use common\library\oms\order_profit\Constant as OrderProfitConstant;
use common\library\oms\order_profit\OrderProfitFactorTriggerTrait;
use common\library\server\refresh_product_inventory\RefreshInventoryQueue;
use common\library\util\Arr;
use xiaoman\orm\common\DomainObject;
use xiaoman\orm\common\Operator;

/**
 * Class OutboundRecordOperator
 * @package common\library\oms\outbound_invoice\record
 */
class OutboundRecordOperator extends Operator
{
    use CompareDiffTrait;
    use OrderProfitFactorTriggerTrait;
    public $setting;
    const TASK_LIST = [
        'after_delete' => [
            'require_fields' => ['outbound_record_id', 'refer_id'],
            'method' => 'afterDelete',
        ],
    ];
    const RELATE_TRANSFER_TYPE = [\Constants::TYPE_PRODUCT_TRANSFER_PURCHASE, \Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND];

    public function __construct(DomainObject $object)
    {
        parent::__construct($object);
        $this->setting = new OutboundInvoiceRecordSetting();
    }

    public function create(array $data)
    {
        if(empty($data)) return true;

        //主键生成逻辑前置 后续的关联关系需要用到
        $data = array_reduce($data,function ($carry,$value){
            $value['outbound_record_id'] = $value['outbound_record_id'] ?? intval(\ProjectActiveRecord::produceAutoIncrementId());
            $carry[] = $value;
            return $carry;
        },[]);

        $productList = array_reduce($data,function ($carry,$value) {
            $value['create_user'] = $this->object->getDomainHandler()->getUserId();
            $value['update_user'] = $this->object->getDomainHandler()->getUserId();
            $value['create_time'] = xm_function_now();
            $value['update_time'] = xm_function_now();
            $carry[] = OmsUtil::columnExtract($value,$this->object->getMetadata()->columnFields());
            return $carry;
        },[]);

//        print_r($productList);
//        exit();

        $this->batchInsert($productList);
        $this->addTriggerReferOrder(array_filter(array_column($productList, 'refer_id')));
        //todo 操作历史

        //记录通过出库任务下推关联关系
        $outboundTransferRelationMap = [];
        $purchaseInvoiceRelationMap = [];
        foreach ($data as $datum)
        {
            if (!empty($datum['transfer_invoice_id']) && $datum['transfer_invoice_record_id'])
            {
                $transferRelation = [
                    'transfer_invoice_id' => $datum['transfer_invoice_id'],
                    'transfer_invoice_record_id' => $datum['transfer_invoice_record_id'],
                    'refer_id' => $datum['outbound_invoice_id'],
                    'sub_refer_id' => $datum['outbound_record_id'],
                    'type' => $datum['task_type'],
                    'enable_flag' => 1
                ];
                if(!empty($datum['task_type']) && $datum['task_type'] == \Constants::TYPE_PRODUCT_TRANSFER_PURCHASE){
                    $purchaseInvoiceRelationMap[] = $transferRelation;
                }else{
                    $outboundTransferRelationMap[] = $transferRelation;
                }
            }
        }
        //保存出库任务关联
        if (!empty($outboundTransferRelationMap))
        {
            $transferInvoiceRelation = TransferRelationFactory::getBatch($this->clientId, \Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND);
            $transferInvoiceRelation->getOperator()->setRelation($outboundTransferRelationMap);
        }

        if (!empty($purchaseInvoiceRelationMap))
        {
            $transferInvoiceRelation = TransferRelationFactory::getBatch($this->clientId, \Constants::TYPE_PRODUCT_TRANSFER_PURCHASE);
            $transferInvoiceRelation->getOperator()->setRelation($purchaseInvoiceRelationMap);
        }

        $recordIds = array_column($data,'outbound_record_id');
        RefreshInventoryQueue::pushQueue($this->clientId,RefreshInventoryQueue::TYPE_OUTBOUND_RECORD, $recordIds);

        return true;
    }

    public function edit(array $data)
    {
        $this->onlySingle();
        if(empty($data)) return true;

        $oldAttribute = $this->get(['outbound_count', 'cost_unit_price_rmb', 'cost_unit_price_usd', 'refer_id']);

        $data['update_user'] = $this->object->getDomainHandler()->getUserId();
        $data['update_time'] = xm_function_now();
        $data = OmsUtil::columnExtract($data,(new OutboundRecordMetadata())->columnFields());


//        print_r($data);
//        exit();

        $result = $this->execute($data);

        RefreshInventoryQueue::pushQueue($this->clientId,RefreshInventoryQueue::TYPE_OUTBOUND_RECORD, $data['outbound_record_id']);

        //销售订单利润埋点: 出库产品明细数量 或 出库成本价格变动，更新销售订单利润
        if (intval($data['outbound_count']) != intval($oldAttribute['outbound_count']) || round(floatval($data['cost_unit_price_rmb']),6) != floatval(($oldAttribute['cost_unit_price_rmb'])) || round(floatval(($data['cost_unit_price_usd'])),6) != floatval($oldAttribute['cost_unit_price_usd'])) {
            $this->batchReferOrderProfit([$data]);
        }

        // 更新订单环节状态
        $referIds = $data['refer_id'] ?? [];
        if(!is_array($referIds)){
            $referIds = [$referIds];
        }
        $this->addTriggerReferOrder(array_filter($referIds));

        //todo 操作历史
        return true;
    }

    public function delete($mode = \Constants::DELETE_FLAG_TRUE)
    {
        $clientId = $this->object->getClientId();
        $userId = $this->object->getDomainHandler()->getUserId();
        $setValue = [
            'update_time'    => xm_function_now(),
            'update_user' => $this->object->getDomainHandler()->getUserId(),
            'delete_flag' => $mode
        ];
//        print_r($setValue);
//        exit();
        return $this->standardProcess($setValue,
            [
                'after_delete'
            ]);
    }

    protected function afterDelete($data, $setting)
    {
        $invoiceIds = array_column($data, 'outbound_invoice_id');
        $recordIds = array_column($data, 'outbound_record_id');
        RefreshInventoryQueue::pushQueue($this->clientId,RefreshInventoryQueue::TYPE_OUTBOUND_RECORD, $recordIds);

        // 删除和任务的关联关系(不能删，删了后面就无法触发关联transfer任务的进度和状态了)
//        foreach(static::RELATE_TRANSFER_TYPE as $taskType){
//            $filter = TransferRelationFactory::getFilter($this->clientId, $taskType);
//            $filter->refer_id = $invoiceIds;
//            $filter->sub_refer_id = $recordIds;
//            $filter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
//            $relationOperator = $filter->find();
//            $relationOperator->getOperator()->updateRelationEnableFlag(\Constants::ENABLE_FLAG_FALSE);
//        }


        //销售订单利润埋点
        $filter = new OutboundRecordFilter($this->object->getClientId());
        $filter->outbound_record_id = $recordIds;
        $filter->select(['outbound_record_id', 'refer_id', 'refer_type','order_id']);
        $productList = $filter->rawData();
        if (!empty($productList)) {
            $this->batchReferOrderProfit($productList);
        }

        $referIds = Arr::uniqueFilterValues(array_column($productList,'order_id'));
        if ($referIds){
            $this->addTriggerReferOrder($referIds);
        }
    }

    public function addTriggerReferOrder($order_ids){
        $order_ids = array_filter($order_ids);
        $trigger = SaleOutboundTrigger::make($this->clientId);
        $trigger->addOrderIds($order_ids);
    }

    public function batchReferOrderProfit(array $productList)
    {
        //销售订单利润埋点: 已关联存货成本、已出库存货成本
        $orderIds = [];
        foreach ($productList as $item) {
            if (isset($item['refer_type']) && in_array($item['refer_type'], [\Constants::TYPE_ORDER, \Constants::TYPE_SHIPPING_INVOICE])) {
                $orderIds[] = $item['order_id'];
            }
        }
        $orderIds = array_filter(array_unique($orderIds));
        if (!empty($orderIds)) {
            foreach ($orderIds as $orderId) {
                $this->referOrderProfitFactor($this->clientId, $orderId, OrderProfitConstant::FACTOR_TYPE_OF_OUTBOUND_INVOICE_AMOUNT);
            }
        }
    }

}