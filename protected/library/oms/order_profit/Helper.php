<?php

namespace common\library\oms\order_profit;

use common\library\APIConstant;
use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldList;
use common\library\invoice\InvoiceProductRecordList;
use common\library\invoice\Order;
use common\library\oms\order_profit\Constant as OrderProfitConstant;
use common\library\privilege_v3\Helper as privilegeHelper;
use common\library\privilege_v3\PrivilegeService;
use common\library\queue_v2\job\OrderProductProfitJob;

/**
 * Class Helper
 * @package common\library\oms\order_profit
 */
class Helper
{
    use OrderProfitFactorTriggerTrait;

    public function referByOrderId(int $clientId, int $orderId)
    {
        $orderProfit = new OrderProfit($clientId);
        $orderProfit->loadByOrderId($orderId);
        if ($orderProfit->isNew()) {
            $msg = "clientId[{$clientId}]orderId[{$orderId}]未创建订单利润";
            \LogUtil::info($msg);
            return;
        }

        $factors = [
            OrderProfitConstant::FACTOR_TYPE_OF_CASH_COLLECTION_AMOUNT,
            OrderProfitConstant::FACTOR_TYPE_OF_COST_INVOICE_AMOUNT,
            OrderProfitConstant::FACTOR_TYPE_OF_OUTBOUND_INVOICE_AMOUNT,
            OrderProfitConstant::FACTOR_TYPE_OF_PURCHASE_ORDER_PRODUCT_AMOUNT,
            OrderProfitConstant::FACTOR_TYPE_OF_TAX_REFUND_AMOUNT,
            OrderProfitConstant::FACTOR_TYPE_OF_ORDER_AMOUNT,
            OrderProfitConstant::FACTOR_TYPE_OF_FORMULA_FIELD,
        ];
        foreach ($factors as $factor) {
            try {
                $factorName = OrderProfitConstant::FACTOR_TYPE_MAP[$factor];
                $this->referOrderProfitFactor($clientId, $orderId, $factor);
            } catch (\Exception $e) {
                throw new \RuntimeException('clientId[' . $clientId . ']orderId[' . $orderId . ']' . $factorName . (\Yii::t('invoice', 'Refresh failed')) . $e->getMessage());
            }
        }
        return true;
    }

    public function referHistory(int $clientId, int $userId, string $startAccountDate, string $startCreateDate, bool $isReferAllOrder,string $endCreateDate = '')
    {
        try {
            $startAccountTime = $startAccountDate . ' 00:00:00';
            $startCreateTime = $startCreateDate . ' 00:00:00';
            $endCreateTime = $endCreateDate . ' 00:00:00';
            $orderList = new \common\library\invoice\OrderList($userId);
            $orderList->setFields(['order_id', 'amount_rmb', 'amount_usd', 'exchange_rate', 'exchange_rate_usd', 'product_list']);
            $orderList->setEnableFlag(null);
            $startAccountDate && $orderList->setStartAccountDate($startAccountTime);
            $startCreateDate && $orderList->setStartCreateTime($startCreateTime);
            $endCreateDate && $orderList->setEndCreateTime($endCreateTime);

            $orderList->setSkipPermissionCheck(true);
            $orderList = $orderList->find();

            if (empty($orderList)) {
                \LogUtil::info("clientId[{$clientId}]在订单日期【{$startAccountDate}】且创建日期【{$startCreateTime}】时间以后暂无订单");
                return;
            }
            $orderIds = array_column($orderList, 'order_id');

            $orderProfitFilter = new \common\library\oms\order_profit\OrderProfitFilter($clientId);
            $orderProfitFilter->order_id = $orderIds;
            $orderProfitFilter->select(['order_id']);
            $orderProfitList = $orderProfitFilter->rawData();
            $existOrderIds = array_column($orderProfitList, 'order_id');
            $notExistOrderIds = array_diff($orderIds, $existOrderIds);

            if (empty($notExistOrderIds) && !$isReferAllOrder) {
                return;
            }

            $orderListChunk = array_chunk($orderList, 1000);
            foreach ($orderListChunk as $chunk) {
                $addOrderProfitList = [];
                foreach ($chunk as $orderItem) {
                    if (!in_array($orderItem['order_id'], $notExistOrderIds)) {
                        continue;
                    }

                    $addOrderProfitList[] = [
                        'client_id' => $clientId,
                        'order_id' => $orderItem['order_id'],
                        'order_amount_rmb' => floatval($orderItem['amount_rmb']),
                        'order_amount_usd' => floatval($orderItem['amount_usd']),
                    ];
                }

                if (!empty($addOrderProfitList)) {
                    $batchOrderProfit = new  BatchOrderProfit($clientId);
                    $batchOrderProfit->getOperator()->batchCreate($addOrderProfitList);
                }
            }

        } catch (\Exception $e) {
            \LogUtil::error("{$clientId}创建历史订单利润失败 error:" . $e->getMessage());
            throw  new \RuntimeException("{$clientId}".(\Yii::t('invoice', 'Failed to create historical order profit')) . $e->getMessage());
        }

        $referOrderIds = $isReferAllOrder ? $orderIds : $notExistOrderIds;
        foreach ($referOrderIds as $referOrderId) {
            $this->referByOrderId($clientId, $referOrderId);
        }
        return true;
    }

    public static function mapOrderProfitList($clientId, $userId, &$list, $quoteFieldRows,$scene)
    {
        $quoteFields = \common\library\custom_field\Helper::getQuoteField($clientId, $userId, \Constants::TYPE_ORDER_PROFIT, ['id', 'type','relation_field','relation_field_type']);
//        $formulaFields = array_column(\common\library\custom_field\Helper::getFields($clientId, \Constants::TYPE_ORDER, [], CustomFieldService::FIELD_TYPE_FORMULA, ['id', 'type']), 'id');

        $formatItem = [
            'cash_collection_collect_amount_rmb',
            'cash_collection_collect_amount_usd',
            'cash_collection_real_amount_rmb',
            'cash_collection_real_amount_usd',
            'cash_collection_not_collect_amount_rmb',
            'cash_collection_not_collect_amount_usd',
            'cash_collection_percentage_rmb',
            'cash_collection_percentage_usd',
            'purchase_order_product_amount_rmb',
            'purchase_order_product_amount_usd',
            'purchase_order_amount_rmb',
            'purchase_order_amount_usd',
            'purchase_order_product_payment_amount_rmb',
            'purchase_order_product_payment_amount_usd',
            'purchase_order_product_not_payment_amount_rmb',
            'purchase_order_product_not_payment_amount_usd',
            'outbound_product_inventory_amount_rmb',
            'outbound_product_inventory_amount_usd',
            'outbound_product_completed_inventory_amount_rmb',
            'outbound_product_completed_inventory_amount_usd',
            'cost_invoice_rmb',
            'cost_invoice_usd',
            'payment_cost_invoice_rmb',
            'payment_cost_invoice_usd',
            'not_payment_cost_invoice_rmb',
            'not_payment_cost_invoice_usd',
            'tax_refund_rmb',
            'tax_refund_usd',
            'real_profit_rmb',
            'real_profit_usd',
            'real_sale_profit_rate',
            'real_cost_profit_rate',
            'predict_profit_rmb',
            'predict_profit_usd',
            'predict_sale_profit_rate',
            'predict_cost_profit_rate',
            'predict_profit_rmb',
            'addition_cost_amount_rmb',
            'addition_cost_amount_usd',
        ];
        foreach ($list as &$item) {
            $item['ali_order_id'] = $item['ali_order_id'] != 0 ? $item['ali_order_id'] : '';
            $item['order_name'] = $item['name'] ?? '';
            $item['order_no'] = $item['order_no'] ?? 0;
            $item['order_status'] = $item['status'] ?? 0;
            $item['company_name'] = $item['company_name'] ?? '';
            $item['company_id'] = $item['company_id'] ?? 0;
            $item['account_date'] = $item['account_date'] ?? '1970-01-01 00:00:01';
            $item['finish_collection_date'] = $item['finish_collection_date'] ?? '1970-01-01 00:00:00';
            $item['last_collection_date'] = $item['last_collection_date'] ?? '1970-01-01 00:00:00';
            $item['first_collection_date'] = $item['first_collection_date'] ?? '1970-01-01 00:00:00';
            $item['ali_store_id'] = $item['ali_store_id'] ?? '';
            $item['order_currency'] = $item['currency'] ?? '';
            $item['order_exchange_rate'] = round(floatval($item['exchange_rate'] ?? 0) / 100, 2);
            $item['order_exchange_rate_usd'] = round(floatval($item['exchange_rate_usd'] ?? 0) / 100, 2);
            $item['order_amount'] = floatval($item['amount'] ?? 0);
            $item['order_amount_rmb'] = floatval($item['amount_rmb'] ?? 0);
            $item['order_amount_usd'] = floatval($item['amount_usd'] ?? 0);
            $item['order_cost_with_tax_total_rmb'] = round(floatval((!empty($item['cost_with_tax_total']) && !empty($item['exchange_rate'])) ? $item['cost_with_tax_total'] *  $item['exchange_rate'] / 100 : 0),6);
            $item['order_cost_with_tax_total_usd'] = round(floatval((!empty($item['cost_with_tax_total']) && !empty($item['exchange_rate_usd'])) ? $item['cost_with_tax_total'] *  $item['exchange_rate_usd'] / 100 : 0),6);
            $item['departments_info'] = $item['departments_info'] ?? [];
            $item['users_info'] = $item['users_info'] ?? [];
            $item['status_info'] = $item['status_info'] ?? [];
            $item['store_info'] = $item['store_info'] ?? [];
            $item['customer'] = $item['customer'] ?? [];
            $item['company'] = $item['company'] ?? [];
            $item['cash_collection_percentage'] = round(($item['cash_collection_collect_amount_rmb'] + $item['cash_collection_not_collect_amount_rmb']) == 0 ? 0 : $item['cash_collection_collect_amount_rmb'] * 100 / ($item['cash_collection_collect_amount_rmb'] + $item['cash_collection_not_collect_amount_rmb']), 2);
            $item['order_serial_id'] = $item['company']['serial_id'] ?? '';
            $item['order_product_amount_rmb'] = floatval($item['product_total_amount_rmb'] ?? 0);
            $item['order_product_amount_usd'] = floatval($item['product_total_amount_usd'] ?? 0);
            $item['order_product_amount'] = $item['product_total_amount'] ?? 0;
            $item['purchase_order_addition_cost_amount_rmb'] = floatval($item['purchase_order_addition_cost_amount_rmb'] ?? 0);
            $item['purchase_order_addition_cost_amount_usd'] = floatval($item['purchase_order_addition_cost_amount_usd'] ?? 0);
//            $item['addition_cost_amount_rmb'] = $item['addition_cost_amount'] * $item['exchange_rate'] / 100;
//            $item['addition_cost_amount_usd'] = $item['addition_cost_amount'] * $item['exchange_rate_usd'] / 100;
            foreach ($formatItem as $formatKey) {
                $item[$formatKey] = floatval($item[$formatKey] ?? 0);
            }

            foreach($quoteFields as $quoteField){

                // 下载导出 pass支持formatString不全
//                if (isset($item[$quoteField['relation_field']]) && $scene != APIConstant::SCENE_EXPORT) {
//                    $item[$quoteField['id']] = $item[$quoteField['relation_field']];
//                    continue;
//                }

                if(empty($quoteFieldRows[$item['order_profit_id']])) {
                    continue;
                }

                $item[$quoteField['id']] = $quoteFieldRows[$item['order_profit_id']][$quoteField['id']] ?? $item[$quoteField['relation_field']] ?? '';
            }
        }

        return $list;
    }

    public static function bulidOrderProfitOrderByField($field)
    {
        $field_map = [
            'order_amount_rmb' => 'amount_rmb',
            'order_amount_usd' => 'amount_usd',
            'order_product_amount_rmb' => 'product_total_amount_rmb',
            'order_product_amount_usd' => 'product_total_amount_usd',
            'order_name' => 'name',
            'cash_collection_percentage' => 'cash_collection_percentage_rmb',
//            'addition_cost_amount_rmb' => 'addition_cost_amount',
//            'addition_cost_amount_usd' => 'addition_cost_amount'
        ];
        return $field_map[$field] ?? $field;
    }

    public static function bulidCalculateTaxRefund(&$data, $clientId, $orderProfit)
    {
        $userId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        $order = new Order($userId, $orderProfit->order_id);
        if (isset($data['tax_refund_rmb'])) {
            $tax_refund = round($data['tax_refund_rmb'] * (1 / ($order->exchange_rate / 100)), 5);
            $orderProfit->tax_refund_usd = round($order->exchange_rate_usd * $tax_refund / 100, 5);
            $orderProfit->tax_refund_rmb = round($data['tax_refund_rmb'], 5);
            $data['tax_refund_usd'] = round($order->exchange_rate_usd * $tax_refund / 100, 5);
            $data['is_edit_tax_refund'] = 1;
            return;
        }

        if (isset($data['tax_refund_usd'])) {
            $tax_refund = round($data['tax_refund_usd'] * (1 / ($order->exchange_rate_usd / 100)), 5);
            $orderProfit->tax_refund_rmb = round($order->exchange_rate * $tax_refund / 100, 5);
            $orderProfit->tax_refund_usd = round($data['tax_refund_usd'], 5);
            $data['tax_refund_rmb'] = round($order->exchange_rate * $tax_refund / 100, 5);
            $data['is_edit_tax_refund'] = 1;
            return;
        }
    }

    public static function referRate($clientId)
    {
        $orderProfitFilter = new \common\library\oms\order_profit\OrderProfitFilter($clientId);
        $orderProfitFilter->select(['order_profit_id']);
        $orderProfitList = $orderProfitFilter->rawData();
        $orderProfitIds = array_column($orderProfitList, 'order_profit_id');
        foreach ($orderProfitIds as $orderProfitId) {
            try {
                $orderProfit = new OrderProfit($clientId, $orderProfitId);
                $orderProfit->getOperator()->edit([]);
            } catch (\Exception $e) {
                \LogUtil::info("[{$clientId}][{$orderProfitId}]刷新订单利润错误:" . $e->getMessage());
            }
        }
        return true;
    }

    public function referHistoryInvoiceProduct(int $clientId, int $userId, string $startAccountDate = '', string $startCreateTime = '', bool $isRefreshAfterSave = false)
    {
        try {
            $listObj = new \common\library\invoice\OrderList($userId);
            $listObj->setSkipPermissionCheck(true);
            $listObj->setEnableFlag(null);
            $startAccountDate && $listObj->setStartAccountDate($startAccountDate . ' 00:00:00');
            $startCreateTime && $listObj->setStartCreateTime($startCreateTime . ' 00:00:00');
            $count = $listObj->count();
            \LogUtil::info("clientId[{$clientId}]订单总数{$count}个");

            $searchNum = 0;
            $limit = 10000;
            while ($searchNum < $count) {
                $listObj->setFields(['order_id']);
                $listObj->setOrderBy('create_time');
                $listObj->setOrder('asc');
                $listObj->setOffset($searchNum);
                $listObj->setLimit($limit);
                $orderList = $listObj->find();
                foreach ($orderList as $item) {
                    try {
                        $this->referInvoiceProductByOrderId($clientId, $item['order_id'], $isRefreshAfterSave);
                    } catch (\Exception $e) {
                        //单个订单失败，继续执行
                        continue;
                    }
                }
                $searchNum += $limit;
            }

        } catch (\Exception $e) {
            \LogUtil::error("{$clientId}历史订单产品毛利, 订单查询失败 error:" . $e->getMessage());
            throw  new \RuntimeException("{$clientId}".(\Yii::t('invoice', 'Historical order product profit, order query failed')). $e->getMessage());
        }
    }

    public function referInvoiceProductByOrderId(int $clientId, int $orderId, bool $isRefreshAfterSave = false)
    {
        try {
            (new OrderProductProfitJob($clientId, $orderId, $isRefreshAfterSave))->handle();
//            QueueService::dispatch(new OrderProductProfitJob($clientId, $orderId, $isRefreshAfterSave));
        } catch (\Exception $e) {
            throw new \RuntimeException('clientId[' . $clientId . ']orderId[' . $orderId . ']' . (\Yii::t('invoice', 'Order product profit calculation failed')) . $e->getMessage());
//            throw new \RuntimeException('clientId[' . $clientId . ']orderId[' . $orderId . ']' . '产品毛利push job失败' . $e->getMessage());
        }
    }


    public function refreshProductProfitByPgSetIds(array $pgSetIds = [])
    {

        $accountBaseDb = \Yii::app()->account_base_db;
        foreach ($pgSetIds as $pgSetId) {
            \LogUtil::info("pgSetId开始执行[{$pgSetId}]");

            $sql = "select client_id from tbl_client where pgsql_set_id = {$pgSetId} group by client_id order by client_id asc";
            $result = $accountBaseDb->createCommand($sql)->queryAll();
            $clientIds = array_column($result, 'client_id');
            \LogUtil::info("[{$pgSetId}]pgSetId执行的clientId总数为" . count($clientIds) . ",范围为" . json_encode($clientIds));

            $this->refreshProductProfitByClientIds($pgSetId, $clientIds);

        }

    }

    public function refreshProductProfitByClientIds($pgSetId, $clientIds)
    {
        foreach ($clientIds as $clientId) {

            \LogUtil::info("[{$pgSetId}]pgSetId开始执行clientId[$clientId]");

            try {
                $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
                if (!$adminUserId) {
                    \LogUtil::info("[{$pgSetId}]pgSetId执行clientId失败[{$clientId}] adminUser not exist!");
                    \LogUtil::info("[{$pgSetId}]pgSetId执行clientId结束[{$clientId}]");
                    continue;
                }
                \User::setLoginUserById($adminUserId);

                $this->referHistoryInvoiceProduct($clientId, $adminUserId);

            } catch (\Exception $e) {

                \LogUtil::error("[{$pgSetId}]pgSetId执行clientId失败[{$clientId}]:" . $e->getMessage());
                throw  new \RuntimeException("[{$pgSetId}]pgSetId执行clientId失败[{$clientId}]:" . $e->getMessage());
            }
            \LogUtil::info("[{$pgSetId}]pgSetId执行clientId结束[{$clientId}]");
        }
    }

    public function refreshFactor(int $clientId, int $factorType)
    {
        $orderProfitFilter = new \common\library\oms\order_profit\OrderProfitFilter($clientId);
        $orderProfitFilter->select(['order_id']);
        $orderProfitList = $orderProfitFilter->rawData();
        $orderIds = array_column($orderProfitList, 'order_id');
        foreach ($orderIds as $orderId) {
            try {
                $this->referOrderProfitFactor($clientId, $orderId, $factorType, true);
            } catch (\Exception $e) {
                \LogUtil::info("[{$clientId}][{$orderId}]刷新订单利润因子错误:" . $e->getMessage());
            }
        }
        return true;
    }

    /****
     * 刷新历史订单退税税率、增值税率字段
     * @param $clientId
     * @return void
     */
    public function refreshTaxRefund(int $clientId, int $userId)
    {
        $db = \PgActiveRecord::getDbByClientId($clientId);

        $listObj = new \common\library\invoice\OrderList($userId);
        $listObj->setSkipPermissionCheck(true);
        $listObj->setEnableFlag(null);
        $count = $listObj->count();
        \LogUtil::info("clientId[{$clientId}]订单总数{$count}个");

        $searchNum = 0;
        $limit = 1000;
        $successOrderCount = 0;
        $successProductCount = 0;
        while ($searchNum < $count) {

            $updateData = [];

            $listObj->setFields(['order_id', 'product_list']);
            $listObj->setOrderBy('create_time');
            $listObj->setOrder('asc');
            $listObj->setOffset($searchNum);
            $listObj->setLimit($limit);
            $orderList = $listObj->find();
            foreach ($orderList as $item) {
                if(empty($item['product_list'])){
                    continue;
                }
                $productList = json_decode($item['product_list'], true);

                foreach($productList as $productItem) {
                    //unique_id没值， 或者vat_rate、tax_refund_rate两字段都没值，不需要同步
                    if(empty($productItem['unique_id'])
                        ||
                        ( !isset($productItem['vat_rate']) || empty(floatval($productItem['vat_rate'])))
                            &&
                        (!isset($productItem['tax_refund_rate']) || empty(floatval($productItem['tax_refund_rate']))
                        )
                    ){
                        continue;
                    }

                        $updateData[$item['order_id']][] = [
                            'unique_id' => $productItem['unique_id'],
                            'vat_rate' => isset($productItem['vat_rate']) ? floatval($productItem['vat_rate']) : 0,
                            'tax_refund_rate' => isset($productItem['tax_refund_rate']) ? floatval($productItem['tax_refund_rate']) : 0,
                        ];

                }
            }

            if(!empty($updateData)){
                $uniqueIds = [];
                $setVatRateSql = " vat_rate  = case";
                $setTaxRefundRateSql = " tax_refund_rate  = case";
                foreach($updateData as $orderIdKey => $productRecordList)
                {
                    foreach($productRecordList as $productRecord){
                        $setVatRateSql .= " when id = '{$productRecord['unique_id']}' then {$productRecord['vat_rate']} ";
                        $setTaxRefundRateSql .= " when id = '{$productRecord['unique_id']}' then {$productRecord['tax_refund_rate']} ";
                        $uniqueIds[] = $productRecord['unique_id'];
                    }
                }

                $uniqueIdStr = implode(',', $uniqueIds);
                $updateSql = " update tbl_invoice_product_record set  {$setVatRateSql} end,  {$setTaxRefundRateSql} end where client_id = {$clientId} and id in ({$uniqueIdStr})";
                $updateCount = $db->createCommand($updateSql)->execute();

                $successOrderCount += count($updateData);
                $successProductCount += $updateCount;
            }

            $searchNum += $limit;
        }

        \LogUtil::info("{$clientId}刷新历史订单退税税率和增值税率成功, 订单数{$successOrderCount},产品数{$successProductCount}");

    }


    /**
     * 计算退税金额
     * @param $unitPrice
     * @param $count
     * @param $exchangeRate
     * @param $exchangeRateUsd
     * @param $vatRate
     * @param $taxRefundRate
     * @return array
     */
    public static function formatTaxRefundStats($unitPrice, $count, $exchangeRate, $exchangeRateUsd, $vatRate, $taxRefundRate)
    {
        //退税金额= 含税成本价（CNY）*数量/(1+增值税率)*退税率
        //退税金额= 采购单价（CNY）*数量/(1+增值税率)*退税率
        $unitPriceRmb = is_numeric($unitPrice) ? $unitPrice * $exchangeRate / 100 : 0;
        $unitPriceUsd = is_numeric($unitPrice) ? $unitPrice * $exchangeRateUsd / 100 : 0;
        $rate = (1 + $vatRate / 100);
        $taxRefundRmb = $rate ? ($unitPriceRmb * $count) / $rate * ($taxRefundRate / 100) : 0;
        $taxRefundUsd = $rate ? ($unitPriceUsd * $count) / $rate * ($taxRefundRate / 100)  : 0;
        return [
            'tax_refund_rmb' => $taxRefundRmb,
            'tax_refund_usd' => $taxRefundUsd,
        ];
    }
}