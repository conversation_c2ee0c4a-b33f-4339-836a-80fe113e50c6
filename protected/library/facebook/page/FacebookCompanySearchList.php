<?php

namespace common\library\facebook\page;

use common\library\customer_v3\company\orm\Company;
use common\library\customer_v3\customer\CustomerList;
use common\library\privilege_v3\Helper;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\search\SearchApi;
use common\library\search\SearchParams;
use common\library\workflow\WorkflowConstant;
use ProcessException;
use User;

class FacebookCompanySearchList
{
//    protected $queryType = self::BOOL_TYPE_MUST;

    protected $queryFields = [];
    protected $clientId;
    protected $userId;
    protected $limit;

    public const ALLOWED_FIELDS = [
        'customer_list.email',
        'customer_list.tel',
        'customer_list.contact'
    ];

    public function setQueryFields(array $queryFields)
    {
        $this->queryFields = $queryFields;
    }

    public function setClientId($clientId)
    {
        $this->clientId = $clientId;
    }

    public function setUserId($userId)
    {
        $this->userId = $userId;
    }

    public function setLimit($limit)
    {
        $this->limit = $limit;
    }
//
//    /**
//     * @throws ProcessException
//     */
//    protected function buildQuery(): array
//    {
//        $conditions = [];
//        foreach ($this->queryFields as $field => $val) {
//            if (!in_array($field, self::ALLOWED_FIELDS, true)) {
//                throw new ProcessException("不支持该字段");
//            }
//            if (in_array($field, $this->keywordFields, true)) {
//                $field .= '.keyword';
//            }
//            $val = is_array($val) ? $val : [$val];
//            $conditions[] = [
//                'constant_score' => [
//                    'filter' => [
//                        "terms" => [$field => $val]
//                    ]
//                ]
//            ];
//        }
//
//        // 使用邮箱、电话与Facebook匹配
//        $contacts = array_merge([$this->queryFields['customer_list.email'] ?? ''], $this->queryFields['customer_list.tel'] ?? []);
//        $contacts = array_values(array_filter($contacts));
//        $conditions[] = [
//            "nested" => [
//                "path"  => "customer_list.contact",
//                "query" => [
//                    "constant_score" => [
//                        'filter' => [
//                            "bool" => [
//                                "must" => [
//                                    ["term" => ["customer_list.contact.type" => 'facebook']],
//                                    ["terms" => ["customer_list.contact.value.keyword" => $contacts]],
//                                ]
//                            ]
//                        ]
//                    ]
//                ]
//            ]
//        ];
//
//        return [
//            [
//                "nested" => [
//                    "path" => "customer_list",
//                    "query" => [
//                        "bool" => [
//                            "minimum_should_match" => 1,
//                            "should" => $conditions
//                        ]
//                    ]
//                ]
//            ]
//        ];
//    }
//
//
//    protected function buildFilter(): array
//    {
//        $userIds = Helper::getPermissionScopeUser($this->clientId, $this->userId, PrivilegeConstants::PRIVILEGE_CRM_COMPANY_EDIT);
//
//        if (is_array($userIds)) {   // 下属+自己的私海
//            $conditions = [['terms' => ['user_id' => $userIds]]];
//        }else if ($userIds === Helper::CAN_MANAGE_ALL_USER) { // 全部用户
//            $conditions = [['term' => ['client_id' => (int)$this->clientId]]];
//        }else{  // 自己的私海
//            $conditions = [['term' => ['user_id' => (int)$this->userId]]];
//        }
//
//        // 公海客户
//        if (Helper::hasPermission($this->clientId, $this->userId, PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_EDIT)) {
//            $conditions[] = ['term' => ['private' => false]];
//        }
//
//        return [
//            "bool" => [
//                "must" => ['term' => ['client_id' => (int)$this->clientId]],
//                "should" => $conditions,
//                "minimum_should_match" => 1
//            ]
//        ];
//    }

    public function buildSearchParams()
    {
        $params = new SearchParams();
        $params->idOnly(true);

        $filters = [];
        foreach ($this->queryFields as $field => $value) {
            if (!in_array($field, self::ALLOWED_FIELDS)) {
                throw new ProcessException("不支持该字段");
            }
            $filters[] = [
                'field' => $field,
                'operator' => (in_array($field, ['customer_list.tel', 'tel'])) ? WorkflowConstant::FILTER_OPERATOR_EQUAL : WorkflowConstant::FILTER_OPERATOR_IN,
                'value' => (array)$value,
            ];
        }

        $contacts = array_merge([$this->queryFields['customer_list.email'] ?? ''], $this->queryFields['customer_list.tel'] ?? []);
        $contacts = array_values(array_filter($contacts));
        $filters[] = [
            'field' => 'customer_list.contact',
            'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
            'value' => (array)$contacts,
            'value_type' => 'facebook',
        ];
        $params->setFilters($filters, WorkflowConstant::CRITERIA_TYPE_OR);

        $userIds = Helper::getPermissionScopeUser($this->clientId, $this->userId, PrivilegeConstants::PRIVILEGE_CRM_COMPANY_EDIT);
        $permissionFilters = [];
        if ($userIds === Helper::CAN_MANAGE_ALL_USER) { // 全部用户
            $permissionFilters[] = [
                'search_field' => 'private',
                'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                'value' => true
            ];
        }else{  // 自己的私海
            $permissionFilters[] = [
                'search_field' => 'user_id',
                'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                'value' => (array)$userIds
            ];
        }

        // 公海客户
        if (Helper::hasPermission($this->clientId, $this->userId, PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_EDIT)) {
            $permissionFilters[] = [
                'search_field' => 'private',
                'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                'value' => false
            ];
        }
        if ($permissionFilters) {
            $params->addExtraFilters($permissionFilters, WorkflowConstant::CRITERIA_TYPE_AND, WorkflowConstant::CRITERIA_TYPE_OR);
        }

        return $params;
    }

    public function find(): array
    {
        $user = User::getUserObject($this->userId, $this->clientId);

//        $results = parent::find();
//        $companyIds = array_column($results, '_id');

        $companyIds = SearchApi::company($this->clientId)->setReturnIds(true)->listByParams($this->buildSearchParams());

        if (empty($companyIds)) {
            return [];
        }
        $customerList = new CustomerList($this->clientId);
        $customerList->setCompanyId($companyIds);
        $customerList->getFormatter()->snsListSetting();
        $customerListData = $customerList->find();
        // 删除不符合查询条件的联系人
        $customerListData = array_filter($customerListData, function ($customer) {
            return $this->calcSortWeight($customer) > 0;
        });

        // 过滤无权限的联系人
        $companyIds = array_column($customerListData, 'company_id');
        $companyIds = array_filter($companyIds, function ($companyId) use($user) {
            return (new Company($this->clientId, $companyId))->isEditable($user);
        });
        $customerListData = array_filter($customerListData, static function ($customer) use($companyIds) {
            return in_array($customer['company_id'], $companyIds, false);
        });

        // 排序：邮箱 > 电话 > 社交联系方式
        usort($customerListData, function ($customerA, $customerB) {
            $weightA = $this->calcSortWeight($customerA);
            $weightB = $this->calcSortWeight($customerB);
            if ($weightA === $weightB) {
                return $customerA['customer_id'] > $customerB['customer_id'] ? 1 : -1;
            }
            return $weightA < $weightB ? 1 : -1;
        });


        return $customerListData;
    }

    /**
     * 计算排序权重
     *
     * 排序：邮箱 > 电话 > 社交联系方式
     *
     * 匹配邮箱 weight+5
     * 匹配电话 weight+4
     * 匹配社交 weight+3
     */
    protected function calcSortWeight(array $customer): int
    {
        $weight = 0;

        $email = strtolower($this->queryFields['customer_list.email'] ?? '');
        $tels = $this->queryFields['customer_list.tel'] ?? [];

        // 使用邮箱匹配联系人的邮箱
        if ($email && $customer['email'] === $email) {
            $weight += 5;
        }

        // 使用电话匹配联系人的电话
        $customerTels = array_map(static function ($tel) {
            return str_replace(' ', '', $tel);
        }, $customer['full_tel_list']?:[]);
        if (!empty($tels) && array_intersect($customerTels, $tels)) {
            $weight += 4;
        }

        // 使用邮箱、电话匹配联系人的社交-Facebook
        $contacts = array_filter($customer['contact'] ?: [], static function ($item) {
            return ($item['type'] ?? '') === 'facebook';
        });
        $items = array_merge($tels, [$email]);
        foreach ($contacts as $contact) {
            if (in_array($contact['value'] ?? '', $items, true)) {
                $weight += 3;
                break;
            }
        }

        return $weight;
    }
}