<?php

namespace common\library\flutter\data;

use common\library\inquiry_collaboration\InquiryCollaborationAttachmentsAPI;
use common\library\inquiry_collaboration\InquiryCollaborationAttachmentsReq;
use common\library\inquiry_collaboration_product\InquiryCollaborationProductAPI;
use common\library\inquiry_collaboration_product\InquiryCollaborationProductReq;
use common\library\oms\common\OmsConstant;
use protobuf\FlutterPaas\PbPaasDataListResponse;
use protobuf\FlutterPaas\PbPaasDataResponse;
use protobuf\FlutterPaas\PbPaasField;
use protobuf\FlutterPaas\PbPaasObject;
use protobuf\FlutterPaas\PaasObjectName;
use protobuf\FlutterPaas\PbPaasFieldMutliValueValue;
use protobuf\FlutterPaas\PbApprovalAttaches;
use protobuf\FlutterPaas\PbApprovalEvent;
use protobuf\FlutterPaas\PbApprovalFlowInfo;
use protobuf\FlutterPaas\PbApprovalUser;
use protobuf\FlutterPaas\PaasObjectOperatePrivilege;
use protobuf\FlutterPaas\PaasObjectOperate;

use common\library\object\field\FieldConstant;
use common\library\object\field\FieldFilter;
use common\library\flutter\field\PaasFieldBuildProtobuf;
use common\library\flutter\field\PaasFieldBuildProtobufV2;
use common\library\object\object_define\Constant as objConstant;
use common\library\inquiry_collaboration\InquiryCollaborationAPI;
use common\library\inquiry_collaboration\InquiryCollaborationReq;
use common\library\oms\inquiry_feedback\InquiryFeedBackApi;

class PaasData
{
    const OBJECT_DATA_API_MAP = [
        PaasObjectName::OBJ_INQUIRY_COLLABORATION => [
            'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION,
            'module_type' => \Constants::TYPE_INQUIRY_COLLABORATION,
            'primary_field' => 'inquiry_collaboration_id',
            'api' => InquiryCollaborationAPI::class,
            'req' => InquiryCollaborationReq::class,
        ],
        PaasObjectName::OBJ_INQUIRY_COLLABORATION_PRODUCT => [
            'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
            'module_type' => \Constants::TYPE_INQUIRY_COLLABORATION_PRODUCT,
            'primary_field' => 'inquiry_product_id',
            'api' => InquiryCollaborationProductAPI::class,
            'req' => InquiryCollaborationProductReq::class,
        ],
        PaasObjectName::OBJ_INQUIRY_COLLABORATION_ATTACHMENTS => [
            'object_name' => InquiryCollaborationAttachmentsAPI::OBJ_INQUIRY_COLLABORATION_ATTACHMENTS,
            'module_type' => \Constants::TYPE_INQUIRY_COLLABORATION,
            'primary_field' => 'file_id',
            'api' => InquiryCollaborationAttachmentsAPI::class,
            'req' => InquiryCollaborationAttachmentsReq::class,
        ],
        PaasObjectName::OBJ_INQUIRY_FEEDBACK => [
            'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
            'module_type' => \Constants::TYPE_INQUIRY_FEEDBACK,
            'primary_field' => 'inquiry_feedback_id',
            'api' => InquiryFeedBackApi::class,
            'req' => null,
        ],
    ];

    const TO_OBJECT_NAME_MAP = [
        OmsConstant::TO_QUOTATION => PaasObjectName::OBJ_QUOTATION,
    ];

    const SPECIAL_REFERENCE_FIELD = [
        \Constants::TYPE_PRODUCT => [

        ],
        \Constants::TYPE_COMPANY => [
            'product_group_ids',
            'trail_status',
            'intention_level',
            'annual_procurement',
            'biz_type',
            'origin_list',
            'scale_id',
            'category_ids',
            'ali_store_id',
            'star',
            'group_id',
        ],
        \Constants::TYPE_CUSTOMER => [
            'post_grade',
        ],
        \Constants::TYPE_OPPORTUNITY => [
            'flow_id',
            'stage',
            'type',
            'fail_type',
        ],
    ];

    protected $clientId;
    protected $userId;
    protected $objectName;
    protected $fieldList;
    protected $version;

    protected $primaryField;

    public function __construct($clientId, $userId, $objectName, $version)
    {
        if (!isset(self::OBJECT_DATA_API_MAP[$objectName])) {
            return null;
        }

        $headers = getallheaders();
        $this->clientId = $clientId;
        $this->userId = $userId;
        $this->objectName = $objectName;
        $this->version = $version;
        $this->fieldList = $this->getFieldList();
        $this->primaryField = self::OBJECT_DATA_API_MAP[$objectName]['primary_field'];
    }

    private function getFieldList()
    {
        $fieldFilter = new FieldFilter($this->clientId);
        $fieldFilter->object_name = self::OBJECT_DATA_API_MAP[$this->objectName]['object_name'];
        $fieldFilter->function_type = FieldConstant::FUNCTION_TYPE_NORMAL;
        $fieldFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $fieldFilter->select(['object_name', 'field', 'field_type', 'field_name', 'ext_info', 'array_flag']);
        $result = $fieldFilter->rawData();
        $fieldList = [];
        foreach ($result as $value) {
            $fieldList[$value['field']] = $value;
        }

        return $fieldList;
    }

    public function getDataList($request, $scene = 'list')
    {
        $searchParams = [];
        foreach ($request->getParams() as $param) {
            $key = $param->getQueryKey();
            $value = $param->getQueryValue();

            if (isset($searchParams[$key])) {
                if (is_array($searchParams[$key])) {
                    $searchParams[$key][] = $value;
                } else {
                    $searchParams[$key] = [$searchParams[$key], $value];
                }
            } else {
                $searchParams[$key] = $value ?? null;
            }
        }

        $searchParams['scene'] = $scene;
        if ($scene == 'list') {
            $searchParams['page_no'] = $request->getOffset()->getPage();
            $searchParams['page_size'] = $request->getOffset()->getPageSize();
        }

        \LogUtil::info("searchParams:" . json_encode($searchParams));

        if (!isset(self::OBJECT_DATA_API_MAP[$this->objectName])) {
            return null;
        }

        $apiClass = self::OBJECT_DATA_API_MAP[$this->objectName]['api'];
        $reqClass = self::OBJECT_DATA_API_MAP[$this->objectName]['req'];
        $api = new $apiClass($this->clientId, $this->userId);

        if (!is_null($reqClass)) {
            $req = new $reqClass($searchParams, $this->clientId);
            $data = $api->webList($req);
        } else {
            $data = $api->webList($searchParams);
        }

        if ($this->objectName == PaasObjectName::OBJ_INQUIRY_FEEDBACK) {
            foreach ($data['list'] as &$datum) {
                $datum['currency'] = $datum['inquiry_collaboration_info']['currency'] ?? '';
            }
        }

        if ($this->version > '1.0.0') {
            return (new PaasFieldBuildProtobufV2($this->clientId, $this->objectName))->build($data, 'list');
        } else {
            return (new PaasFieldBuildProtobuf($this->clientId, $this->objectName))->build($data, 'list');
        }
    }

    public function create($request)
    {
        //todo
    }

    public function edit($request)
    {
        $params = $this->PbDataUnPack($request);
        $apiClass = self::OBJECT_DATA_API_MAP[$this->objectName]['api'];
        $api = new $apiClass($this->clientId, $this->userId);
        $api->updateObject($params);
        $data = $api->dataDetail([$this->primaryField => $params[$this->primaryField]]);

        if ($this->version > '1.0.0') {
            return (new PaasFieldBuildProtobufV2($this->clientId, $this->objectName))->build(['list' => [$data]], 'detail');
        } else {
            return (new PaasFieldBuildProtobuf($this->clientId, $this->objectName))->build(['list' => [$data]], 'detail');
        }

    }

    public function getDataDetail($request)
    {
        $queryParams = [];
        foreach ($request->getParams() as $param) {
            $key = $param->getQueryKey();
            $value = $param->getQueryValue();
            $queryParams[$key] = $value ?? null;
        }

        if (!isset(self::OBJECT_DATA_API_MAP[$this->objectName])) {
            return null;
        }

        \LogUtil::info("queryParams:" . json_encode($queryParams));

        $apiClass = self::OBJECT_DATA_API_MAP[$this->objectName]['api'];
        $api = new $apiClass($this->clientId, $this->userId);
        $data = $api->dataDetail($queryParams);

        if ($this->version > '1.0.0') {
            return (new PaasFieldBuildProtobufV2($this->clientId, $this->objectName))->build(['list' => [$data]], 'detail');
        } else {
            return (new PaasFieldBuildProtobuf($this->clientId, $this->objectName))->build(['list' => [$data]], 'detail');
        }

    }

    private function PbDataPack($data, $scene = 'list')
    {
        $fieldList = $this->fieldList;
        if ($scene == 'list') {
            $rsp = new PbPaasDataListResponse();
            $rsp->setCount($data['count']);
        } else {
            $rsp = new PbPaasDataResponse();
        }
        $rsp->setObjectName($this->objectName);
        $list = [];

        foreach ($data['list'] ?? [] as $datum) {
            $primaryField = new PbPaasField();
            $primaryField->setFieldKey($this->primaryField);
            $primaryField->setSingleValue($datum[$this->primaryField]);

            $object = new PbPaasObject();
            $object->setObjectName($this->objectName);
            $object->setUniqueField($primaryField);
            $columnFields = [];

            if (!empty($datum['approval_flow_info']) && !is_a($datum['approval_flow_info'], \stdClass::class)) {
                $approvalFlowInfo = $this->buildApprovalFlowInfo($datum['approval_flow_info']);
                $object->setApprovalFlowInfo($approvalFlowInfo);
            }

            if (!empty($datum['operate_privilege'])) {
                $operatePrivilege = $this->buildOperatePrivilege($datum['operate_privilege']);
                $object->setOperatePrivilege($operatePrivilege);
            }

            foreach ($datum as $field => $value) {
                if (in_array($field, ['external_field_data', 'operate_privilege', 'notice_config', 'attachments', 'record_list'])) {
                    continue;
                }

                if (str_contains($field, '_info')) {
                    $fieldKey = str_replace('_info', '', $field);
                } else {
                    $fieldKey = $field;
                }

                if (isset($columnFields[$field])) {
                    continue;
                }

                $fieldInfo = $fieldList[$fieldKey] ?? null;
                $infoFlag = isset($datum[$field.'_info']);

                if (str_contains($field, '_info')) {
                    continue;
                }

                if ($fieldInfo && $fieldInfo['array_flag']) {
                    // 多选
                    $singleValued = false;
                } elseif (is_array($value) && empty($fieldInfo) && $field == 'create_user') {
                    // 单选
                    $singleValued = true;
                } elseif (is_array($value) && empty($fieldInfo)) {
                    // 多选
                    $singleValued = false;
                } elseif ($field == 'social_media') {
                    // 多选
                    $singleValued = false;
                } else {
                    // 单选
                    $singleValued = true;
                }

                $columnField = new PbPaasField();
                $columnField->setFieldKey($field);
                $label = '';

                if (!$singleValued) {
                    // 多选
                    $multipleValues = [];
                    if (!is_array($value)) {
                        $value = [$value];
                    }
                    foreach ($value as $index => $item) {
                        if ($infoFlag && $fieldInfo && $fieldInfo['field_type'] == FieldConstant::TYPE_USER && $fieldInfo['array_flag']) {
                            $label = implode(',', array_column($datum[$field.'_info'],'nickname'));
                        } elseif ($infoFlag) {
                            $label = $datum[$field.'_info'][$index]['info_label'] ?? '';
                        } elseif ($fieldInfo && $fieldInfo['field_type'] == FieldConstant::TYPE_IMAGE) {
                            $item = $item['file_preview_url'] ?? $item['file_url'] ?? null;
                        } elseif (is_array($item)) {
                            $item = implode(',', $item);
                        }

                        $item = is_null($item) ? '' : $item;
                        $multipleValues[] = $item;
                    }

                    $fieldValue = (new PbPaasFieldMutliValueValue())->setValues($multipleValues);
                    $columnField->setLable($label)->setMutliValue($fieldValue);
                } else {
                    // 单选
                    $value = is_null($value) ? '' : $value;
                    if ($infoFlag && $fieldInfo && $fieldInfo['field_type'] == FieldConstant::TYPE_USER) {
                        $label = $datum[$field.'_info']['nickname'] ?? '';
                    } elseif ($infoFlag) {
                        $label = $datum[$field.'_info']['info_label'] ?? '';
                    } elseif ($field == 'create_user') {
                        $label = $value['nickname'] ?? '';
                        $value = $value['user_id'] ?? null;
                    }

                    if ($field == 'sku_attributes' && !empty($value)) {
                        if (is_array($value)) {
                            $label = sprintf('%s : %s', $value[0]['item_name'], $value[0]['value']['item_name']);
                            $value = json_encode($value, JSON_UNESCAPED_UNICODE);
                        }
                    } elseif (is_array($value)) {
                        $value = array_shift($value);
                        $value = is_null($value) ? '' : $value;;
                    }

                    if (is_array($value)) {
                        $value = json_encode($value, JSON_UNESCAPED_UNICODE);
                    }

                    $columnField->setLable($label)->setSingleValue($value);
                }

                $columnFields[$field] = $columnField;
            }

            $object->setFields(array_values($columnFields));
            $list[] = $object;
        }

        if ($scene == 'list') {
            !empty($list) && $rsp->setValue($list);
        } else {
            !empty($list) && $rsp->setValue(array_shift($list));
        }

        return $rsp;
    }

    public function PbDataUnPack($request)
    {
        $object = $request->getValue();
        $primaryId = 0;
        if ($object->getUniqueField()) {
            if ($object->getUniqueField()->hasSingleValue()) {
                $primaryId = $object->getUniqueField()->getSingleValue()->getStrValue();
            } else {
                $primaryId = $object->getUniqueField()->getFieldValue();
            }
        }

        $data = [
            $this->primaryField => $primaryId,
        ];

        $fields = $object->getFields();
        foreach ($fields as $field) {
            $fieldKey = $field->getFieldKey();
            if ($field->hasSingleValue()) {
                $fieldValue = $field->getSingleValue()->getStrValue();
            } elseif ($field->hasMutliValueValue()) {
                $fieldValue = [];
                foreach ($field->getMutliValueValue()->getValues() as $value) {
                    $fieldValue[] = $value->getStrValue();
                }
            } else {
                $fieldValue = $field->getFieldValue();
            }
            $data[$fieldKey] = $fieldValue;
        }

        return $data;
    }

    private function buildApprovalFlowInfo($data)
    {
        $approvalFlowInfo = new PbApprovalFlowInfo();
        $approvalFlowInfo->setApplyFormId($data['apply_form_id']);
        $approvalFlowInfo->setApprovalFlowId($data['approval_flow_id']);
        $approvalFlowInfo->setApplyUserId($data['apply_user_id']);
        $approvalFlowInfo->setStatus($data['status']);
        $approvalFlowInfo->setReferId($data['refer_id']);
        $approvalFlowInfo->setTitle($data['title'] ?? '');
        $approvalFlowInfo->setContent($data['content'] ?? '');
        $approvalFlowInfo->setType($data['type']);
        $approvalFlowInfo->setTriggerType($data['trigger_type']);
        $approvalFlowInfo->setUpdateTime($data['update_time'] ?? '');
        $approvalFlowInfo->setApprovalType($data['approval_type'] ?? 0);

        if (isset($data['approval_form_id'])) {
            $approvalFlowInfo->setApprovalFormId($data['approval_form_id']);
        }

        $approvingApprovers = [];
        foreach ($data['approving_approver'] ?? [] as $item) {
            $approvalUser = new PbApprovalUser();
            $approvalUser->setUserId($item['user_id']);
            $approvalUser->setNickname($item['nickname']);
            $approvalUser->setEmail($item['email']);
            $approvalUser->setAvatar($item['avatar']);
            $approvalUser->setEditFlag($item['edit_flag']);
            $approvingApprovers[] = $approvalUser;
        }
        if (!empty($approvingApprovers)) {
            $approvalFlowInfo->setApprovingApprover($approvingApprovers);
        }

        $allApprovers = [];
        foreach ($data['all_approver'] ?? [] as $item) {
            $approvalUser = new PbApprovalUser();
            $approvalUser->setUserId($item['user_id']);
            $approvalUser->setNickname($item['nickname']);
            $approvalUser->setEmail($item['email']);
            $approvalUser->setAvatar($item['avatar']);
            $approvalUser->setEditFlag($item['edit_flag']);
            $allApprovers[] = $approvalUser;
        }
        if (!empty($allApprovers)) {
            $approvalFlowInfo->setAllApprover($allApprovers);
        }

        $operatorApprovers = [];
        foreach ($data['operator_approver'] ?? [] as $item) {
            $approvalUser = new PbApprovalUser();
            $approvalUser->setUserId($item['user_id']);
            $approvalUser->setNickname($item['nickname']);
            $approvalUser->setEmail($item['email']);
            $approvalUser->setAvatar($item['avatar']);
            $approvalUser->setEditFlag($item['edit_flag']);
            $operatorApprovers[] = $approvalUser;
        }
        if (!empty($operatorApprovers)) {
            $approvalFlowInfo->setOperatorApprover($operatorApprovers);
        }

        if (!empty($data['event'])) {
            $approvalUser = new PbApprovalUser();
            $approvalUser->setUserId($data['event']['user_info']['user_id']);
            $approvalUser->setNickname($data['event']['user_info']['nickname']);
            $approvalUser->setEmail($data['event']['user_info']['email']);
            $approvalUser->setAvatar($data['event']['user_info']['avatar']);

            $event = new PbApprovalEvent();
            $event->setEventId($data['event']['event_id']);
            $event->setEventType($data['event']['event_type']);
            $event->setEventOrigin($data['event']['event_origin'] ?? '');
            $event->setClientId($data['event']['client_id']);
            $event->setCreateUserId($data['event']['create_user_id']);
            $event->setReferId($data['event']['refer_id']);
            $event->setReferType($data['event']['refer_type']);
            $event->setRemark($data['event']['remark'] ?? '');
            $event->setEnableFlag($data['event']['enable_flag']);
            $event->setCreateTime($data['event']['create_time']);
            $event->setReferTypePresentation($data['event']['refer_type_presentation']);
            $event->setEventTypePresentation($data['event']['event_type_presentation']);
            $event->setPresentation($data['event']['presentation']);
            $event->setUserInfo($approvalUser);

            $approvalFlowInfo->setApprovalEvent($event);
        }

        return $approvalFlowInfo;
    }

    private function buildOperatePrivilege($data)
    {
        $operateMap = [
            'create' => [
                'operate' => PaasObjectOperate::OPERATE_CREATE,
                'name' => \Yii::t('privilege', 'Create'),
            ],
            'edit' => [
                'operate' => PaasObjectOperate::OPERATE_EDIT,
                'name' => \Yii::t('privilege', 'Edit'),
            ],
            'delete' => [
                'operate' => PaasObjectOperate::OPERATE_DELETE,
                'name' => \Yii::t('privilege', 'Delete'),
            ],
            'export' => [
                'operate' => PaasObjectOperate::OPERATE_EXPORT,
                'name' => \Yii::t('privilege', 'Export'),
            ],
            'change' => [
                'operate' => PaasObjectOperate::OPERATE_CHANGE,
                'name' => \Yii::t('privilege', 'Change'),
            ],
            'generate_object' => [
                'operate' => PaasObjectOperate::OPERATE_GENERATE_OBJECT,
                'name' => \Yii::t('privilege', 'Generate Object'),
            ],
        ];

        $operatePrivileges = [];
        $changeFields = [];
        $generateObjects = [];

        foreach ($data as $key => $item) {
            $operatePrivilege = new PaasObjectOperatePrivilege();
            $operatePrivilege->setOperate($operateMap[$key]['operate'] ?? PaasObjectOperate::OPERATE_UNKNOWN);
            $operatePrivilege->setName($operateMap[$key]['name'] ?? '');
            $operatePrivilege->setFlag(boolval($item['flag'] ?? 1));
            $operatePrivilege->setMessage($item['message'] ?? '');

            if (str_contains($key, 'change_')) {
                $fieldKey = str_replace('change_', '', $key);
                $fieldInfo = $this->fieldList[$fieldKey] ?? null;
                $operatePrivilege->setOperate(PaasObjectOperate::OPERATE_CHANGE);
                $operatePrivilege->setFieldKey($fieldKey);
                $operatePrivilege->setName($operateMap['change']['name'] . ($fieldInfo['field_name'] ?? ''));
                $changeFields[] = $operatePrivilege;
            } elseif (str_contains($key, 'to_')) {
                $operatePrivilege->setOperate(PaasObjectOperate::OPERATE_GENERATE_OBJECT);
                $operatePrivilege->setName(\Yii::t('privilege', $key));
                $operatePrivilege->setGenerateObject(self::TO_OBJECT_NAME_MAP[$key] ?? PaasObjectName::OBJECT_UNKNOWN);
                $generateObjects[] = $operatePrivilege;
            } elseif (!empty($operateMap[$key]['operate'])) {
                $operatePrivileges[] = $operatePrivilege;
            }
        }

        return array_values(array_merge($operatePrivileges, $changeFields, $generateObjects));
    }

}