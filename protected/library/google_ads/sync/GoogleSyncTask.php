<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON>.
 * User: bing
 * Date: 2020/3/9
 * Time: 10:20
 */

namespace common\library\google_ads\sync;

use common\library\google_ads\ads\AdsAccountService;
use common\library\google_ads\ads\GoogleAds;
use common\library\google_ads\sync\Dispatcher;
use common\library\google_ads\GoogleConstants;
use common\library\google_ads\sync\task\AdsDateMetricsTask;
use common\library\google_ads\sync\task\AdsGeoMetricsTask;
use common\library\google_ads\sync\task\AdsDeviceMetricsTask;
use common\library\google_ads\sync\task\AdsHourMetricsTask;
use common\library\google_ads\sync\task\AdsKeywordMetricsTask;
use common\library\google_ads\sync\task\AdsRecommendationTask;
use common\library\google_ads\sync\task\SiteDateMetricsTask;
use common\library\google_ads\sync\task\SiteDeviceMetricsTask;
use common\library\google_ads\sync\task\SiteSourceMetricsTask;
use common\library\google_ads\sync\task\AdsKeywordsTask;
use Google\Ads\GoogleAds\V17\Services\GoogleAdsRow;

class GoogleSyncTask {

    /**
     * 同步ads的日期指标
     * @param array $clientIds
     * @param string $startDate
     * @param string $endDate
     * @param int $stepDay
     */
    public static function adsDateMetricsTask(array $clientIds = [], $startDate = '', $endDate = '', int $stepDay = 90) {
        $dispatcher = new Dispatcher($clientIds);
        $dispatcher->setStartDate($startDate);
        $dispatcher->setEndDate($endDate);

        $dispatcher->registryTasks(AdsDateMetricsTask::class, [
            'type'=> AdsDateMetricsTask::SYNC_TYPE,
            'metricsType' => AdsDateMetricsTask::METRICS_TYPE,
            'stepDay' => $stepDay
        ]);
        $dispatcher->run();
    }

    /**
     * 同步ads国家地理位置指标
     * @param array $clientIds
     * @param string $startDate
     * @param string $endDate
     * @param int $stepDay
     */
    public static function adsGeoMetricsTask(array $clientIds = [], $startDate = '', $endDate = '', int $stepDay = 90) {
        $dispatcher = new Dispatcher($clientIds);
        $dispatcher->setStartDate($startDate);
        $dispatcher->setEndDate($endDate);

        $dispatcher->registryTasks(AdsGeoMetricsTask::class, [
            'type'=> AdsGeoMetricsTask::SYNC_TYPE,
            'metricsType' => AdsGeoMetricsTask::METRICS_TYPE,
            'stepDay' => $stepDay
        ]);
        $dispatcher->run();
    }

    /**
     * 同步ads的设置指标
     * @param array $clientIds
     * @param string $startDate
     * @param string $endDate
     * @param int $stepDay
     */
    public static function adsDeviceMetricsTask(array $clientIds = [], $startDate = '', $endDate = '', int $stepDay = 90) {
        $dispatcher = new Dispatcher($clientIds);
        $dispatcher->setStartDate($startDate);
        $dispatcher->setEndDate($endDate);

        $dispatcher->registryTasks(AdsDeviceMetricsTask::class, [
            'type'=> AdsDeviceMetricsTask::SYNC_TYPE,
            'metricsType' => AdsDeviceMetricsTask::METRICS_TYPE,
            'stepDay' => $stepDay
        ]);
        $dispatcher->run();
    }

    /**
     * 同步ads时段指标
     * @param array $clientIds
     * @param string $startDate
     * @param string $endDate
     * @param int $stepDay
     */
    public static function adsHourMetricsTask(array $clientIds = [], $startDate = '', $endDate = '', int $stepDay = 90) {
        $dispatcher = new Dispatcher($clientIds);
        $dispatcher->setStartDate($startDate);
        $dispatcher->setEndDate($endDate);

        $dispatcher->registryTasks(AdsHourMetricsTask::class, [
            'type'=> AdsHourMetricsTask::SYNC_TYPE,
            'metricsType' => AdsHourMetricsTask::METRICS_TYPE,
            'stepDay' => $stepDay
        ]);
        $dispatcher->run();
    }

    /**
     * 同步关键字
     * @param array $clientIds
     * @param string $startDate
     * @param string $endDate
     * @param int $stepDay
     */
    public static function adsKeyWords(array $clientIds = [], $startDate = '', $endDate = '', int $stepDay = 90) {
        $dispatcher = new Dispatcher($clientIds);
        $dispatcher->setStartDate($startDate);
        $dispatcher->setEndDate($endDate);
        $dispatcher->registryTasks(AdsKeywordsTask::class, [
            'type'=> AdsKeywordsTask::SYNC_TYPE,
            'metricsType' => AdsKeywordsTask::METRICS_TYPE,
            'stepDay' => $stepDay
        ]);

        $dispatcher->run();
    }

    /**
     * 同步ads关键字指标
     * @param array $clientIds
     * @param string $startDate
     * @param string $endDate
     * @param int $stepDay
     */
    public static function adsKeywordMetricsTask(array $clientIds = [], $startDate = '', $endDate = '', int $stepDay = 20) {
        $dispatcher = new Dispatcher($clientIds);
        $dispatcher->setStartDate($startDate);
        $dispatcher->setEndDate($endDate);

        $dispatcher->registryTasks(AdsKeywordMetricsTask::class, [
            'type'=> AdsKeywordMetricsTask::SYNC_TYPE,
            'metricsType' => AdsKeywordMetricsTask::METRICS_TYPE,
            'stepDay' => $stepDay
        ]);
        $dispatcher->run();
    }

    /**
     * 同步site日期指标
     * @param array $clientIds
     * @param string $startDate
     * @param string $endDate
     * @param int $stepDay
     */
    public static function siteDateMetricsTask(array $clientIds = [], $startDate = '', $endDate = '', int $stepDay = 90) {
        $dispatcher = new Dispatcher($clientIds);
        $dispatcher->setStartDate($startDate);
        $dispatcher->setEndDate($endDate);

        $dispatcher->registryTasks(SiteDateMetricsTask::class, [
            'type'=> SiteDateMetricsTask::SYNC_TYPE,
            'metricsType' => SiteDateMetricsTask::METRICS_TYPE,
            'stepDay' => $stepDay
        ]);
        $dispatcher->run();
    }

    /**
     * 同步site设备指标
     * @param array $clientIds
     * @param string $startDate
     * @param string $endDate
     * @param int $stepDay
     */
    public static function siteDeviceMetricsTask(array $clientIds = [], $startDate = '', $endDate = '', int $stepDay = 90) {
        $dispatcher = new Dispatcher($clientIds);
        $dispatcher->setStartDate($startDate);
        $dispatcher->setEndDate($endDate);

        $dispatcher->registryTasks(SiteDeviceMetricsTask::class, [
            'type'=> SiteDeviceMetricsTask::SYNC_TYPE,
            'metricsType' => SiteDeviceMetricsTask::METRICS_TYPE,
            'stepDay' => $stepDay
        ]);
        $dispatcher->run();
    }

    /**
     * 同步site的来源指标
     * @param array $clientIds
     * @param string $startDate
     * @param string $endDate
     * @param int $stepDay
     */
    public static function siteSourceMetricsTask(array $clientIds = [], $startDate = '', $endDate = '', int $stepDay = 90) {
        $dispatcher = new Dispatcher($clientIds);
        $dispatcher->setStartDate($startDate);
        $dispatcher->setEndDate($endDate);

        $dispatcher->registryTasks(SiteSourceMetricsTask::class, [
            'type'=> SiteSourceMetricsTask::SYNC_TYPE,
            'metricsType' => SiteSourceMetricsTask::METRICS_TYPE,
            'stepDay' => $stepDay
        ]);
        $dispatcher->run();
    }

    /**
     * @param array $clientIds
     * @param string $startDate
     * @param string $endDate
     * @param int $stepDay
     */
    public static function initSiteMetrics(array $clientIds = [], $startDate = '', $endDate = '', int $stepDay = 90) {
        ini_set('memory_limit','8096M');
        $dispatcher = new Dispatcher($clientIds);

        $dispatcher->setStartDate($startDate);
        $dispatcher->setEndDate($endDate);

        $dispatcher->registryTasks(SiteDateMetricsTask::class, [
            'type'=> SiteDateMetricsTask::SYNC_TYPE,
            'metricsType' => SiteDateMetricsTask::METRICS_TYPE,
            'stepDay' => $stepDay
        ]);

        $dispatcher->registryTasks(SiteDeviceMetricsTask::class, [
            'type'=> SiteDeviceMetricsTask::SYNC_TYPE,
            'metricsType' => SiteDeviceMetricsTask::METRICS_TYPE,
            'stepDay' => $stepDay
        ]);

        $dispatcher->registryTasks(SiteSourceMetricsTask::class, [
            'type'=> SiteSourceMetricsTask::SYNC_TYPE,
            'metricsType' => SiteSourceMetricsTask::METRICS_TYPE,
            'stepDay' => $stepDay
        ]);

        $dispatcher->run();

    }

    /**
     * ads初始化
     * @param string $clientId
     * @param string $startDate
     * @param string $endDate
     * @param int $stepDay
     * @throws \Exception
     */
    public static function initAdsMetrics($clientId = '', array $adsAccountIds=[], $startDate = '', $endDate = '', int $stepDay = 90) {
        ini_set('memory_limit','8096M');
        if($clientId == '') {
            $clientIds = [];
        }else {
            $clientIds= [$clientId];
        }

        $dispatcher = new Dispatcher($clientIds);
        $dispatcher->setReferIds($clientId, $adsAccountIds);

        $dispatcher->setStartDate($startDate);
        $dispatcher->setEndDate($endDate);
        // 同步广告系列
        GoogleSyncTask::adsCampaignAdGroup($clientIds);

        $dispatcher->registryTasks(AdsDateMetricsTask::class, [
            'type'=> AdsDateMetricsTask::SYNC_TYPE,
            'metricsType' => AdsDateMetricsTask::METRICS_TYPE,
            'stepDay' => $stepDay
        ]);

        $dispatcher->registryTasks(AdsGeoMetricsTask::class, [
            'type'=> AdsGeoMetricsTask::SYNC_TYPE,
            'metricsType' => AdsGeoMetricsTask::METRICS_TYPE,
            'stepDay' => $stepDay
        ]);

        $dispatcher->registryTasks(AdsDeviceMetricsTask::class, [
            'type'=> AdsDeviceMetricsTask::SYNC_TYPE,
            'metricsType' => AdsDeviceMetricsTask::METRICS_TYPE,
            'stepDay' => $stepDay
        ]);

        $dispatcher->registryTasks(AdsHourMetricsTask::class, [
            'type'=> AdsHourMetricsTask::SYNC_TYPE,
            'metricsType' => AdsHourMetricsTask::METRICS_TYPE,
            'stepDay' => $stepDay
        ]);

        //此处同步整合在AdsKeywordMetricsTask任务中了
//        $dispatcher->registryTasks(AdsKeywordsTask::class, [
//            'type'=> AdsKeywordsTask::SYNC_TYPE,
//            'metricsType' => AdsKeywordsTask::METRICS_TYPE,
//            'stepDay' => $stepDay
//        ]);

        $dispatcher->registryTasks(AdsKeywordMetricsTask::class, [
            'type'=> AdsKeywordMetricsTask::SYNC_TYPE,
            'metricsType' => AdsKeywordMetricsTask::METRICS_TYPE,
            'stepDay' => 20
        ]);

        $dispatcher->registryTasks(AdsRecommendationTask::class, [
            'type'=> AdsKeywordMetricsTask::SYNC_TYPE,
            'metricsType' => GoogleConstants::ADS_RECOMMENDATION,
            'stepDay' => 20
        ]);


        $dispatcher->run();

    }

    /**
     * 同步ads账号
     * @throws \Google\ApiCore\ApiException
     */
    public static function adsAccounts(array $clientIds = []) {
        $result = self::getGoogleAccessList($clientIds);
        foreach($result as $item) {
            foreach($item as $row)
            {
                try {
                    $client_id = $row['client_id'];
                    $access_id = $row['access_id'];
                    $refresh_token = $row['refresh_token'];
                    $ads = new AdsAccountService($client_id);
                    $ads->syncAdsAccountIdInfo($access_id, $refresh_token);
                }catch (\Exception $e) {
                    \LogUtil::error("同步ads账号client_id={$client_id},access_id={$access_id},error msg=".$e->getMessage());
                    continue;
                }
                usleep(50000);
            }
        }
    }

    /**
     * 同步ads广告系列
     */
    public static function adsCampaignAdGroup(array $clientIds = []) {

        \LogUtil::info("进入同步广告系列函数");

        $fields = [
            'campaign.id',
            'campaign.name',
            'campaign.advertising_channel_type',
            'campaign.status',
            'ad_group.id',
            'ad_group.name',
            'customer.currency_code'
        ];

        $fields = implode(',',$fields);

        $query = "SELECT {$fields} FROM ad_group";

        $result = self::getGoogleAccessList($clientIds);

        $adsAccounts = [];

        if(empty($result)) {
            \LogUtil::info("没有可用的access");
        }

        foreach($result as $clientId => $items) {
            foreach ($items as $item) {
                $clientId = $item['client_id'];
                $accessId = $item['access_id'];
                $refreshToken = $item['refresh_token'];
                //检查DB
                $db = \ProjectActiveRecord::getDbByClientId($clientId);
                if(!$db){
                    continue;
                }

                $googleAds = new GoogleAds($refreshToken);
                $googleAds->setAccessId($accessId);
                if(!empty($item['mcc_account'])) {
                    $googleAds->setLoginCustomerId($item['mcc_account']);
                }
                $customerId = $item['ads_account_id'];
                $campaignAdGroup = [];
                $existCampaignAdGroup = self::getCampaignAdGroup($clientId);
                try {
                    $time1 = microtime(true);
                    $response = $googleAds->searchQuery($customerId, $query, ['pageSize' => 1000]);
                    $time2 = microtime(true);
                    \LogUtil::info("请求时间:".($time2 - $time1));
                }catch (\Throwable $e) {
                    $code = $e->getCode();
                    // 该ads账号没有权限
                    if($code == GoogleConstants::GOOGLE_ERROR_CODE_OF_NO_PERSSION) {
                        \LogUtil::error("同步广告系列client_id={$clientId},ads_account_id={$customerId} error msg=".$e->getMessage());
                    }
                    \LogUtil::error("接口调用异常同步广告系列 ads_account_id={$customerId},query={$query}".$e->getMessage().$e->getTraceAsString());
                    continue;
                }

                $newAdGroup = [];
                $needUpdateAdGroup = [];

                \LogUtil::info("同步广告系列数据已返回，准备循环处理,client_id={$clientId},ads_account_id={$customerId}");

                // 广告分组维度
                foreach($response->iterateAllElements() as $googleAdsRow) {
                    try {
                        /** @var GoogleAdsRow $googleAdsRow */
                        $adGroupId = $googleAdsRow->getAdGroup()->getId();
                        $campaignId = $googleAdsRow->getCampaign()->getId();
                        $campaignAdGroup[$campaignId][$adGroupId]['ad_group_id'] = $adGroupId;
                        $campaignId = $campaignAdGroup[$campaignId][$adGroupId]['campaign_id'] = $googleAdsRow->getCampaign()->getId();
                        $campaignName = $googleAdsRow->getCampaign()->getName();
                        $campaignAdGroup[$campaignId][$adGroupId]['campaign_name'] = \Util::escapeDoubleQuoteSql($campaignName);
                        $campaignStatus = $campaignAdGroup[$campaignId][$adGroupId]['campaign_status'] = $googleAdsRow->getCampaign()->getStatus();
                        $campaignAdGroup[$campaignId][$adGroupId]['campaign_type'] = $googleAdsRow->getCampaign()->getAdvertisingChannelType();
                        $adGroupName = $googleAdsRow->getAdGroup()->getName();
                        $campaignAdGroup[$campaignId][$adGroupId]['ad_group_name'] = \Util::escapeDoubleQuoteSql($adGroupName);

                        $customer = $googleAdsRow->getCustomer();
                        $currency = $customer ? $customer->getCurrencyCode() : $googleAds->getCurrencyCode($customerId);

                        $campaignAdGroup[$campaignId][$adGroupId]['ads_account_id'] = $customerId;
                        $campaignAdGroup[$campaignId][$adGroupId]['access_id'] = $accessId;
                        $campaignAdGroup[$campaignId][$adGroupId]['currency'] = $currency;

                        if(isset($existCampaignAdGroup[$campaignId])) {
                            if(isset($existCampaignAdGroup[$campaignId][$adGroupId])) {
                                // 不等,需要更新
                                if($existCampaignAdGroup[$campaignId][$adGroupId]['ad_group_name'] != $adGroupName || $existCampaignAdGroup[$campaignId][$adGroupId]['campaign_status'] != $campaignStatus) {
                                    array_push($needUpdateAdGroup, $campaignAdGroup[$campaignId][$adGroupId]);
                                }
                            }else {
                                // 新增已存在的某系列下的某个广告分组
                                array_push($newAdGroup, $campaignAdGroup[$campaignId][$adGroupId]);
                            }
                        }else {
                            // 新的系列分组
                            array_push($newAdGroup, $campaignAdGroup[$campaignId][$adGroupId]);
                        }
                    }catch (\Throwable $e) {
                        var_dump($e->getMessage());
                    }

                }

                //新增处理
                if(!empty($newAdGroup)) {
                    \LogUtil::info("新增广告系列：".json_encode($newAdGroup));
                    self::createCampaignAdGroup($clientId, $newAdGroup);
                }else {
                    \LogUtil::info("client_id={$clientId},ads_account_id={$customerId},没有新增广告系列");
                }

                //更新处理
                if(!empty($needUpdateAdGroup)) {
                    \LogUtil::info("需要更新的广告系列：".json_encode($needUpdateAdGroup));
                    self::updateCampaignAdGroup($clientId, $needUpdateAdGroup);
                }

                usleep(10000);

                \LogUtil::info("广告系列完成,client_id={$clientId},ads_account_id={$customerId}");
            }
        }
    }

    /**
     * 同步ads搜索关键字
     */
    public static function adsSearchKeywords(array $clientIds = []) {
        \LogUtil::info("进入同步关键字函数");
        $adMetricsFields = [
            'campaign.id',//id
            'ad_group.id',// 分组id
            'ad_group_criterion.criterion_id',//id
            'ad_group_criterion.keyword.text',//关键字
            'ad_group_criterion.status',//关键字状态
            'ad_group_criterion.keyword.match_type',//关键字匹配类型
            'ad_group_criterion.quality_info.quality_score',//质量分
        ];

        $adMetricsFields = implode(',',$adMetricsFields);

        $query = "SELECT $adMetricsFields FROM ad_group_criterion" ;

        $result = self::getGoogleAccessList($clientIds);

        foreach($result as $clientId=>$items) {
            foreach($items as $item) {
                $clientId = $item['client_id'];
                $accessId = $item['access_id'];
                $refreshToken = $item['refresh_token'];
                $googleAds = new GoogleAds($refreshToken);
                $googleAds->setAccessId($accessId);
                if(!empty($item['mcc_account'])) {
                    $googleAds->setLoginCustomerId($item['mcc_account']);
                }
                $customerId = $item['ads_account_id'];

                \LogUtil::info("当前已有ads账号,client_id={$clientId},adsId=".$customerId);
                try {
                    $response = $googleAds->searchQuery($customerId, $query, ['pageSize' => 10000]);
                }catch (\Throwable $e) {
                    $code = $e->getCode();
                    // 该ads账号没有权限
                    if($code == GoogleConstants::GOOGLE_ERROR_CODE_OF_NO_PERSSION) {
                        \LogUtil::error("同步关键字ads账号没有权限client_id={$clientId},ads_account_id={$customerId} error msg=".$e->getMessage());
                    }
                    continue;
                }

                $dataArr = [];
                try {
                    /** @var GoogleAdsRow $googleAdsRow */
                    foreach($response->iterateAllElements() as $googleAdsRow) {
                        $campaignId = $googleAdsRow->getAdGroup()->getId();
                        $adGroupId = $googleAdsRow->getAdGroup()->getId();
                        $criterionId = $googleAdsRow->getAdGroupCriterion()->getCriterionId();
                        $keywordName ='';
                        $keywordMatchType = 0;
                        if($googleAdsRow->getAdGroupCriterion()->getKeyword())
                        {
                            $keywordName = $googleAdsRow->getAdGroupCriterion()->getKeyword()->getText();
                            $keywordMatchType = $googleAdsRow->getAdGroupCriterion()->getKeyword()->getMatchType();
                        }

                        $qualityScore = $googleAdsRow->getAdGroupCriterion()->getQualityInfo() ? $googleAdsRow->getAdGroupCriterion()->getQualityInfo()->getQualityScore() : 0;
                        $keywordStatus = $googleAdsRow->getAdGroupCriterion()->getStatus();

                        $dataItem = [];

                        $dataItem['client_id'] = $clientId;
                        $dataItem['access_id'] = $accessId;
                        $dataItem['ads_account_id'] = $customerId;
                        $dataItem['campaign_id'] = $campaignId;
                        $dataItem['ad_group_id'] = $adGroupId;
                        $dataItem['criterion_id'] = $criterionId;
                        $dataItem['keyword_name'] = \Util::escapeDoubleQuoteSql($keywordName);
                        $dataItem['keyword_match_type'] = $keywordMatchType;
                        $dataItem['quality_score'] = $qualityScore;
                        $dataItem['keyword_status'] = $keywordStatus;
                        // set in arr
                        $dataArr[] = $dataItem;
                    }
                }catch (\Throwable $e) {
                    \LogUtil::error("同步关键字循环处理失败,client_id={$clientId},ads_account_id={$customerId},error=".$e->getMessage());
                    continue;
                }
                if(!empty($dataArr)) {
                    self::insertSearchKeywords($clientId, $dataArr);
                }

                usleep(10000);
            }
        }
    }

    /**
     * 新增广告系列和分组
     * @param $client_id
     * @param $new_ad_group
     */
    protected static function createCampaignAdGroup($clientId, array $newAdGroup) {
        if(empty($newAdGroup)) {
            return;
        }
        $sql = "INSERT INTO tbl_google_ads_campaign (`ads_campaign_id`,`client_id`,`access_id`,`ads_account_id`,`campaign_id`,`campaign_name`,`ad_group_id`,`ad_group_name`,`campaign_type`,`currency`,`campaign_status`,`create_time`,`update_time`) VALUES ";
        $create_time = date("Y-m-d H:i:s");
        $sqlArr = [];

        $id_count = count($newAdGroup);
        $end_id = \ProjectActiveRecord::produceAutoIncrementId($id_count);
        $id = $end_id - $id_count;

        foreach($newAdGroup as $item) {
            $id++;
            $sqlArr[] = "({$id},{$clientId},{$item['access_id']},{$item['ads_account_id']},{$item['campaign_id']},'{$item['campaign_name']}',{$item['ad_group_id']},'{$item['ad_group_name']}',{$item['campaign_type']},'{$item['currency']}',{$item['campaign_status']},'{$create_time}','{$create_time}')";
        }
        $sql .= implode(',', $sqlArr);

        $db = \ProjectActiveRecord::getDbByClientId($clientId);
        $db->createCommand($sql)->execute();
    }

    /**
     * 更新广告系列和分组
     * @param $client_id
     * @param $need_update_ad_group
     */
    protected static function updateCampaignAdGroup($clientId, array $needUpdateAdGroup) {
        if(empty($needUpdateAdGroup)) {
            return;
        }

        $db = \ProjectActiveRecord::getDbByClientId($clientId);

        foreach ($needUpdateAdGroup as $item) {
            $sql = "UPDATE tbl_google_ads_campaign set campaign_status={$item['campaign_status']},ad_group_name='{$item['ad_group_name']}' WHERE client_id={$clientId} AND campaign_id={$item['campaign_id']} AND ad_group_id={$item['ad_group_id']} ";
            $db->createCommand($sql)->execute();
            \LogUtil::info("client_id={$clientId},update_sql={$sql}");
        }
    }

    /**插入搜索关键字数据
     * @param $client_id
     * @param $dataArr
     * @return bool
     * @throws \ProcessException
     */
    protected static function insertSearchKeywords($clientId, & $dataArr) {
        if(empty($dataArr)) {
            return false;
        }

        if($clientId == 0) {
            \LogUtil::info("广告关键字clientId=0，过滤");
            return false;
        }

        try {
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            $sql = "INSERT INTO tbl_google_ads_search_keywords (`criterion_id`,`client_id`, `access_id`, `keyword_name`, `keyword_match_type`, `keyword_status`, `quality_score`, `ads_account_id`, `campaign_id`, `ad_group_id`)  VALUES ";
            $sqlArr = [];
            foreach($dataArr as $item) {
                $sqlArr[] = "({$item['criterion_id']},{$clientId},{$item['access_id']},'{$item['keyword_name']}',{$item['keyword_match_type']},{$item['keyword_status']},{$item['quality_score']},{$item['ads_account_id']},{$item['campaign_id']},{$item['ad_group_id']})";
            }
            $sql .= implode(',', $sqlArr) . ' ON DUPLICATE KEY UPDATE keyword_name=VALUES(keyword_name),keyword_match_type=VALUES(keyword_match_type),keyword_status=VALUES(keyword_status),quality_score=VALUES(quality_score),campaign_id=VALUES(campaign_id),ad_group_id=VALUES(ad_group_id)';

            $db->createCommand($sql)->execute();

            \LogUtil::info("同步关键字入库成功clientId={$clientId}");

        }catch (\Throwable $e) {
            \LogUtil::info("同步关键字入库失败clientId={$clientId}");
        }

        usleep(10000);
    }

    /**
     * @return mixed
     */
    protected static function getGoogleAccessList(array $clientIds = []) {
        $access_status = GoogleConstants::ACCESS_STATUS_LIVE;
        if($clientIds) {
            $clientIds = implode(',', $clientIds);
            $sql = "SELECT 
                        A.client_id,A.ads_account_id,A.access_id,A.mcc_account,B.refresh_token 
                    FROM tbl_google_ads_account AS A 
                    LEFT JOIN tbl_google_access_account AS B 
                    ON A.access_id=B.access_id 
                    WHERE A.client_id in({$clientIds}) 
                    AND B.access_status={$access_status}
                    AND A.enable_flag=1";
        }else {
            $sql = "SELECT 
                    A.client_id,A.ads_account_id,A.access_id,A.mcc_account,B.refresh_token 
                    FROM tbl_google_ads_account AS A 
                    LEFT JOIN tbl_google_access_account AS B 
                    ON A.access_id=B.access_id 
                    WHERE B.access_status={$access_status}
                    AND A.enable_flag=1";
        }
        $db = \Yii::app()->getDb();
        $result = $db->createCommand($sql)->queryAll(true);
        $list = [];
        foreach ($result as $item) {
            $clientId = $item['client_id'];
            if(!isset($list[$clientId])) {
                $list[$clientId] = [];
            }
            $list[$clientId][] = $item;
        }
        return $list;
    }

    /**
     * @param $ads_accounts
     * @return array
     */
    protected static function parseAdsAccount(array $ads_accounts) {
        $result = [];
        foreach($ads_accounts as $item) {
            if(!isset($result[$item['access_id']])) {
                $result[$item['access_id']] = [];
            }
            $result[$item['access_id']][] = $item;
        }
        return $result;
    }

    /**
     * @param $client_id
     */
    protected static function getCampaignAdGroup($client_id) {
        $result = [];
        if($client_id == 0) {
            \LogUtil::info("同步广告系列clientId=0，过滤");
            return $result;
        }

        $sql = "SELECT campaign_id,ad_group_id,ad_group_name,campaign_status FROM tbl_google_ads_campaign WHERE client_id=:client_id";
        $params = [
            ':client_id'=>$client_id
        ];
        $db = \ProjectActiveRecord::getDbByClientId($client_id);
        $list = $db->createCommand($sql)->queryAll(true, $params);
        foreach($list as $item) {
            $result[$item['campaign_id']][$item['ad_group_id']]['ad_group_name'] = $item['ad_group_name'];
            $result[$item['campaign_id']][$item['ad_group_id']]['campaign_status'] = $item['campaign_status'];
        }
        return $result;
    }

    /**
     * @param $clientId
     * @param null $access_id
     * @return mixed
     */
    protected static function getGoogleAds($client_id, $access_id = null) {
        $sql = "SELECT ads_account_id,access_id FROM tbl_google_ads_account WHERE client_id=:client_id AND enable_flag=:enable_flag";
        if(is_numeric($access_id)) {
            $sql .= " AND access_id={$access_id}";
        }

        $params = [
            ':client_id'=> $client_id,
            ':enable_flag'=> GoogleConstants::ADS_ACCOUNT_ENABLE
        ];

        $db = \Yii::app()->db;
        $adsAccounts = $db->createCommand($sql)->queryAll(true, $params);
        return $adsAccounts;
    }

    public static function getExceptionRecords($startDate='', $endDate='') {
        $status = [
            GoogleConstants::SYNC_STATUS_NO_RUNNING,
            GoogleConstants::SYNC_STATUS_RUNNING,
            GoogleConstants::SYNC_STATUS_ERROR
        ];

        if(empty($startDate)) {
            $startDate = date('Y-m-d',strtotime('-7day'));
        }

        if(empty($endDate)) {
            $endDate = date('Y-m-d',strtotime('now'));
        }

        if(strtotime($startDate) + 90 * 24 * 3600 < strtotime($endDate)) {
            throw new \Exception("startDate和endDate只能在90天之间");
        }

        $statusStr = implode(',', $status);

        $sql = "SELECT client_id,`type`,task_date FROM tbl_sync_google_task WHERE task_date>=:start_date AND task_date<=:end_date AND `status` in ({$statusStr})";

        $params= [
            ':start_date' => $startDate,
            ':end_date' => $endDate
        ];

        $db = \Yii::app()->db;
        $result = $db->createCommand($sql)->queryAll(true, $params);

        $siteData = $adsData = [];

        foreach($result as $item) {
            $clientId = $item['client_id'];
            $taskDate = $item['task_date'];
            if($item['type'] == GoogleConstants::SYNC_SITE_TYPE) {
                if(!isset($siteData[$clientId])) {
                    $siteData[$clientId] = [];
                }
                $siteData[$clientId][] = $taskDate;
                $siteData[$clientId] = array_unique($siteData[$clientId]);
            }else if($item['type'] == GoogleConstants::SYNC_ADS_TYPE) {
                if(!isset($adsData[$clientId])) {
                    $adsData[$clientId] = [];
                }
                $adsData[$clientId][] = $taskDate;
                $adsData[$clientId] = array_unique($adsData[$clientId]);
            }
        }

        return [$siteData, $adsData];
    }

    /**
     * 同步ads广告建议
     * @param array $clientIds
     * @param string $startDate
     * @param string $endDate
     * @param int $stepDay
     */
    public static function adsRecommendationTask(array $clientIds = [], $startDate = '', $endDate = '', int $stepDay = 20) {
        $dispatcher = new Dispatcher($clientIds);
        $dispatcher->setStartDate($startDate);
        $dispatcher->setEndDate($endDate);

        $dispatcher->registryTasks(AdsRecommendationTask::class, [
            'type'=> AdsKeywordMetricsTask::SYNC_TYPE,
            'metricsType' => GoogleConstants::ADS_RECOMMENDATION,
            'stepDay' => $stepDay
        ]);
        $dispatcher->run();
    }

}