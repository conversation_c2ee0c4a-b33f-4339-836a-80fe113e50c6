<?php
/**
 * Created by PhpStorm.
 * User: bing
 * Date: 2020/02/16
 * Time: 12:51
 */

namespace common\library\google_ads\ads_analytics\geo_country_metrics;

use common\library\google_ads\GoogleConstants;
use common\library\google_ads\Helper;

class GeoCountryMetricsListFormatter extends \ListItemFormatter {

    protected $clientId;
    protected $showGeoCountryMetricsRateFlag;
    protected $showPercentDistributeFlag;

    protected $sortType;
    protected $sortField;
    protected $start;
    protected $length;
    protected $sortFlag;

    // 公共字段
    protected $sortFields = GoogleConstants::COMMON_METRICS_FIELDS;

    // 美元单位定义花费区间
    const PERCENT_USD = [
        'p1' => 0,
        'p2' => 100,
        'p3' => 500,
        'p4' => 1000
    ];

    // 微美分定义
    const PERCENT = [
        'p1' => 0,
        'p2' => 100 * 1000000,
        'p3' => 500 * 1000000,
        'p4' => 1000 * 1000000
    ];

    public function __construct($clientId) {
        $this->clientId = $clientId;
    }

    public function showGeoCountryMetricsRate(bool $showGeoCountryMetricsRateFlag) {
        $this->showGeoCountryMetricsRateFlag = $showGeoCountryMetricsRateFlag;
    }

    public function showPercentDistributeFlag(bool $percentDistributeFlag) {
        $this->showPercentDistributeFlag = $percentDistributeFlag;
    }

    public function geoCountryListMetricsSetting() {
        $this->showGeoCountryMetricsRate(true);
    }

    public function geoCountryDistributeMetricsSetting() {
        $this->showPercentDistributeFlag(true);
    }

    /**
     * 格式化数据的统计数据自定义排序
     * @param $sortType
     */
    protected function setSortType($sortType) {
        $this->sortType = $sortType;
    }

    /**格式化数据的统计数据自定义排序字段
     * @param $sortField
     */
    protected function setSortField($sortField) {
        if(!in_array($sortField, $this->sortFields)) {
            $sortField = GoogleConstants::COST;
        }
        $this->sortField = $sortField;
    }

    /**
     * @param $sortField
     * @param $sortType
     */
    public function setSort($sortField, $sortType, $start = 0, $length = 0) {
        $this->setSortType($sortType);
        $this->setSortField($sortField);
        $this->start = $start;
        $this->length = $length;
        $this->sortFlag = true;
    }

    public function format($data) {
        if($this->showPercentDistributeFlag) {
            $data['total_cost'] = Helper::costMicrosToCost($data['total_cost_micros']);
        }
        return $data;
    }

    public function result() {
        $result = parent::result();
        if($this->showGeoCountryMetricsRateFlag) {
            // list计算rate
            $result = $this->calculationGeoCountryListMetricsRate($result);
        }else if($this->showPercentDistributeFlag) {
            // 国家分布
            list($total_cost, $percent_value) = $this->geoCountryPercentDistribute($result);
            foreach($result as $k=>&$item) {
                if(empty($item['country_iso_code'])) {
                    $item['country_iso_code'] = 'ZZ';
                }
            }

            $result = [
                'list' => array_values($result),
                'total_cost' => Helper::costMicrosToCost($total_cost),
                'percent_value' => $percent_value
            ];

        }
        return $result;
    }

    /**
     * @param $result
     */
    public function calculationGeoCountryListMetricsRate(array $data) {
        if(empty($data)) {
            return $data;
        }

        $result = [];
        foreach($data as $k=>$item) {
            if($item['country_iso_code'] == null) {
                continue;
            }

            $click_rate = Helper::calculationClickRate($item['total_clicks'],$item['total_impressions'],2,false);
            $conversion_rate = Helper::calculationConversionRate($item['total_conversions'], $item['total_interactions'],2,false);
            $average_cpc = Helper::calculationAverageCpc($item['total_cost_micros']/1000000, $item['total_clicks'],6);
            $cost_per_conversion = Helper::calculationCostPerConversion($item['total_cost_micros']/1000000, $item['total_conversions'],6);

            $itemValue = [];
            $itemValue['country_iso_code'] = $item['country_iso_code'];
            // 公共指标字段处理
            $itemValue[GoogleConstants::COST] = Helper::costMicrosToCost($item['total_cost_micros']);
            $itemValue[GoogleConstants::IMPRESSIONS] = $item['total_impressions'];
            $itemValue[GoogleConstants::CLICKS] = $item['total_clicks'];
            $itemValue[GoogleConstants::CONVERSIONS] = $item['total_conversions'];
            $itemValue[GoogleConstants::CLICKS_RATE] = $click_rate;
            $itemValue[GoogleConstants::CONVERSIONS_RATE] = $conversion_rate;
            $itemValue[GoogleConstants::AVERAGE_CPC] = $average_cpc;
            $itemValue[GoogleConstants::COST_PER_CONVERSION] = $cost_per_conversion;

            $itemValue[GoogleConstants::CLICKS_RATE_FORMAT] = $click_rate.'%';
            $itemValue[GoogleConstants::CONVERSIONS_RATE_FORMAT] = $conversion_rate.'%';

            $result[] = $itemValue;
        }

        // 是否需要排序
        if($this->sortFlag) {
            $result = Helper::sortDataArr($result, $this->sortField, $this->sortType, $this->start,$this->length);
        }

        return $result;
    }

    /**
     * 分布百分比
     * @param array $data
     * @return array
     */
    public function geoCountryPercentDistribute(array $data) {
        $total_cost = 0;
        $p1 = $p2 = $p3 = $p4 = 0;
        foreach($data as $item) {
            $total_cost += $item['total_cost_micros'];
            switch(true) {
                case $item['total_cost_micros'] > self::PERCENT['p4']:
                    $p4 += $item['total_cost_micros'];
                    break;
                case ($item['total_cost_micros'] > self::PERCENT['p3'] && $item['total_cost_micros'] <= self::PERCENT['p4']) :
                    $p3 += $item['total_cost_micros'];
                    break;
                case ($item['total_cost_micros'] > self::PERCENT['p2'] && $item['total_cost_micros'] <= self::PERCENT['p3']):
                    $p2 += $item['total_cost_micros'];
                    break;
                case ($item['total_cost_micros'] >= self::PERCENT['p1'] && $item['total_cost_micros'] <= self::PERCENT['p2']):
                    $p1 += $item['total_cost_micros'];
                    break;
            }
        }

        $percent_valus = [];
        if($total_cost > 0) {
            $values = [
                'p1' => $p1,
                'p2' => $p2,
                'p3' => $p3,
                'p4' => $p4
            ];
            // 计算百分比
            $values = Helper::calculationPercentRate($values);
            $percent1 = $values['p1'];
            $percent2 = $values['p2'];
            $percent3 = $values['p3'];
            $percent4 = $values['p4'];

            $percent_valus = [
                ['title' => '<='.self::PERCENT_USD['p2'], 'value' => $percent1.'%'],
                ['title' => (1 + self::PERCENT_USD['p2']).'~'.self::PERCENT_USD['p3'], 'value' => $percent2.'%'],
                ['title' => (1 + self::PERCENT_USD['p3']).'~'.self::PERCENT_USD['p4'], 'value' => $percent3.'%'],
                ['title' => '>'.self::PERCENT_USD['p4'], 'value' => $percent4.'%'],
            ];
        }

        return [$total_cost, $percent_valus];
    }

}