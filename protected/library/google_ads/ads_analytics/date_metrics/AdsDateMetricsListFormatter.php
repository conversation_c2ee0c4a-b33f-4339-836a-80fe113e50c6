<?php
/**
 * Created by PhpStorm.
 * User: bing
 * Date: 2020/02/15
 * Time: 12:51
 */

namespace common\library\google_ads\ads_analytics\date_metrics;

use common\library\google_ads\GoogleConstants;
use common\library\google_ads\Helper;
use Google\Ads\GoogleAds\V17\Enums\AdvertisingChannelTypeEnum\AdvertisingChannelType;
use Google\Ads\GoogleAds\V17\Enums\CampaignStatusEnum\CampaignStatus;

class AdsDateMetricsListFormatter extends \ListItemFormatter {

    protected $clientId;
    protected $showSummaryFlag;
    protected $showDateCostFlag;
    protected $showImpressionAndClicksFlag;
    protected $showCtrAndCoversionRateFlag;
    protected $showCampaignListFlag;

    protected $sortType;
    protected $sortField;
    protected $sortFlag;
    protected $dateRange;

    protected $summaryInfo = [
        'total_cost_micros' => 0,//花费总数
        'total_impressions' => 0,// 展示总数
        'total_clicks' => 0,//点击总数
        'total_conversions' => 0, // 转化总数
        'total_interactions' => 0,// 互动总数
    ];

    // 公共字段
    protected $sortFields = GoogleConstants::COMMON_METRICS_FIELDS;

    /**
     * 每日指标情况
     * @var array
     */
    protected $dateMetrics = [];

    /**
     * AdsDateMetricsFormatter constructor.
     * @param $clientId
     */
    public function __construct($clientId) {
        $this->clientId = $clientId;
    }

    public function showSummaryFlag(bool $showSummaryFlag) {
        $this->showSummaryFlag = $showSummaryFlag;
    }

    public function showDateCostFlag(bool $showDateCostFlag) {
        $this->showDateCostFlag = $showDateCostFlag;
    }

    public function showImpressionAndClicksFlag(bool $showImpressionAndClicksFlag) {
        $this->showImpressionAndClicksFlag = $showImpressionAndClicksFlag;
    }

    public function showCtrAndCoversionRateFlag(bool $showCtrAndCoversionRateFlag) {
        $this->showCtrAndCoversionRateFlag = $showCtrAndCoversionRateFlag;
    }

    public function adsDateAnalyticsChartSetting() {
        $this->showSummaryFlag(true);
        $this->showImpressionAndClicksFlag(true);
        $this->showDateCostFlag(true);
        $this->showCtrAndCoversionRateFlag(true);
    }

    public function showCampaignListFlag(bool $showCampaignListFlag) {
        $this->showCampaignListFlag = $showCampaignListFlag;
    }

    public function adsCampaignListSetting() {
        $this->showCampaignListFlag(true);
    }

    /**
     * 格式化数据的统计数据自定义排序
     * @param $sortType
     */
    protected function setSortType($sortType) {
        $this->sortType = $sortType;
    }

    /**格式化数据的统计数据自定义排序字段
     * @param $sortField
     */
    protected function setSortField($sortField) {
        if(!in_array($sortField, $this->sortFields)) {
            $sortField = GoogleConstants::COST;
        }
        $this->sortField = $sortField;
    }

    /**
     * @param $sortField
     * @param $sortType
     */
    public function setSort($sortField, $sortType) {
        $this->setSortType($sortType);
        $this->setSortField($sortField);
        $this->sortFlag = true;
    }

    public function buildDateRange($startDate, $endDate) {
        $dateRange = range(strtotime($startDate), strtotime($endDate), 24*60*60);

        $dateRange = array_map(function($item) {
            return date('Y-m-d', $item);
        }, $dateRange);

        $this->dateRange = $dateRange;
    }

    public function buildMapData() {
        $map = [];
        $data = $this->listData ? $this->listData : [$this->data];

        if($this->showCampaignListFlag) {
            $campaign_ids = array_unique(array_column($data, 'campaign_id'));
            $campaign = new \common\library\google_ads\ads_analytics\campaign\CampaignList($this->clientId);
            $campaign->setFields(['campaign_id','campaign_type','campaign_status']);
            $campaign->setGroupByFields(['campaign_id']);
            $result = $campaign->find();
            $campaignInfo = [];
            foreach($result as $item) {
                if(in_array($item['campaign_id'], $campaign_ids)) {
                    $item['campaign_status_name'] = \Yii::t('google', CampaignStatus::name($item['campaign_status']) ?? '');
                    $item['campaign_type_name'] = \Yii::t('google', AdvertisingChannelType::name($item['campaign_type']) ?? '');
                    $item['campaign_type'] = strtolower(AdvertisingChannelType::name($item['campaign_type']) ?? '');
                    $item['campaign_status'] = strtolower(CampaignStatus::name($item['campaign_status']) ?? '');
                    $campaignInfo[$item['campaign_id']] = $item;
                }
            }
            $map['campaign'] = $campaignInfo;
        }

        $this->setMapData($map);
        parent::buildMapData();
    }

    public function strip($data) {

        if($this->showSummaryFlag || $this->showDateCostFlag || $this->showImpressionAndClicksFlag || $this->showCtrAndCoversionRateFlag) {
            $date = $data['analytics_date'];
        }

        // 概述
        if($this->showSummaryFlag) {
            $this->summaryInfo['total_cost_micros'] += $data['cost_micros'];
            $this->summaryInfo['total_impressions'] += $data['impressions'];
            $this->summaryInfo['total_clicks'] += $data['clicks'];
            $this->summaryInfo['total_conversions'] += $data['conversions'];
            $this->summaryInfo['total_interactions'] += $data['interactions'];
        }

        // 按照日期分组统计各个指标
        if($this->showDateCostFlag) {
            // 按日统计ads账号下的各广告系列分组的花费总和
            if(!isset($this->dateMetrics['cost'][$date]['date_total_cost_micros'])) {
                $this->dateMetrics['cost'][$date]['date_total_cost_micros'] = 0;
                $this->dateMetrics['cost'][$date]['date'] = $date;
            }
            $this->dateMetrics['cost'][$date]['date_total_cost_micros'] += $data['cost_micros'];
        }

        // 每日展示和点击数总和
        if($this->showImpressionAndClicksFlag) {
            if(!isset($this->dateMetrics['impressions_clicks'][$date]['date_total_impressions'])) {
                $this->dateMetrics['impressions_clicks'][$date]['date_total_impressions'] = 0;
                $this->dateMetrics['impressions_clicks'][$date]['date_total_clicks'] = 0;
                $this->dateMetrics['impressions_clicks'][$date]['date'] = $date;
            }

            $this->dateMetrics['impressions_clicks'][$date]['date_total_impressions'] += $data['impressions'];
            $this->dateMetrics['impressions_clicks'][$date]['date_total_clicks'] += $data['clicks'];
        }

        // 每日点击数和互动数
        if($this->showCtrAndCoversionRateFlag) {
            if(!isset($this->dateMetrics['rate'][$date]['date_total_impressions'])) {
                $this->dateMetrics['rate'][$date]['date_total_impressions'] = 0;
                $this->dateMetrics['rate'][$date]['date'] = $date;
            }
            $this->dateMetrics['rate'][$date]['date_total_impressions'] += $data['impressions'];

            if(!isset($this->dateMetrics['rate'][$date]['date_total_clicks'])) {
                $this->dateMetrics['rate'][$date]['date_total_clicks'] = 0;

            }
            $this->dateMetrics['rate'][$date]['date_total_clicks'] += $data['clicks'];

            if(!isset($this->dateMetrics['rate'][$date]['date_total_conversions'])) {
                $this->dateMetrics['rate'][$date]['date_total_conversions'] = 0;
            }
            $this->dateMetrics['rate'][$date]['date_total_conversions'] += $data['conversions'];

            if(!isset($this->dateMetrics['rate'][$date]['date_total_interactions'])) {
                $this->dateMetrics['rate'][$date]['date_total_interactions'] = 0;
            }

            $this->dateMetrics['rate'][$date]['date_total_interactions'] += $data['interactions'];
        }

        return $data;
    }

    public function format($data) {
        if($this->showCampaignListFlag) {
            $campaign_item = $this->getMapData('campaign',$data['campaign_id']);
            if($campaign_item) {
                $data['campaign_type_name'] = $campaign_item['campaign_type_name'];
                $data['campaign_status_name'] = $campaign_item['campaign_status_name'];
                $data['campaign_type'] = $campaign_item['campaign_type'];
                $data['campaign_status'] = $campaign_item['campaign_status'];
            }
        }

        return $data;
    }

    public function result() {
        $result = parent::result();

        if($this->showCampaignListFlag) {
            // 按广告系列统计指标数据
            $result = $this->calcutionCampaignListMetrics($result);
            return $result;
        }else {
            $result = [];
            // 概述部分和按日指标
            if($this->showSummaryFlag) {
                $result['summary'] = $this->calculationSummary();
            }

            if($this->showImpressionAndClicksFlag) {
                $result['cost'] = $this->calculationDateCost();
            }

            if($this->showImpressionAndClicksFlag) {
                $result['impressions_clicks'] = $this->calculationImpressionsAndClicks();
            }

            if($this->showCtrAndCoversionRateFlag) {
                $result['rate'] = $this->calculationRate();
            }
            return $result;
        }
    }

    protected function calculationSummary() {
        $summary = [];
        if($this->showSummaryFlag) {
            $summary['total_cost_micros'] = $this->summaryInfo['total_cost_micros'];
            $summary['total_cost'] = Helper::costMicrosToCost($this->summaryInfo['total_cost_micros']);
            $summary['total_impressions'] = $this->summaryInfo['total_impressions'];
            $summary['total_clicks'] = $this->summaryInfo['total_clicks'];
            $summary['total_conversions'] = $this->summaryInfo['total_conversions'];
            $summary['cost_per_click'] = Helper::calculationAverageCpc($this->summaryInfo['total_cost_micros'] / 1000000, $summary['total_clicks'],6);
            $summary['cost_per_conversion'] = Helper::calculationCostPerConversion($this->summaryInfo['total_cost_micros'] / 1000000,$summary['total_conversions'],6);
        }
        return $summary;
    }

    protected function calculationDateCost() {
        $dateMatrics = [];
        if($this->showImpressionAndClicksFlag) {
            foreach($this->dateRange as $date) {
                if(isset($this->dateMetrics['cost'][$date])) {
                    $item = $this->dateMetrics['cost'][$date];
                    $item['date_cost'] = Helper::costMicrosToCost($item['date_total_cost_micros']);
                    unset($item['date_total_cost_micros']);
                    $dateMatrics[$date] = $item;
                }else {
                    $dateMatrics[$date] = [
                        'date_cost' => Helper::costMicrosToCost(0),
                        'date' => $date,
                    ];
                }
            }
        }
        return $dateMatrics;
    }

    protected function calculationRate() {
        $result = [];
        foreach($this->dateRange as $date) {
            $rate_item = [];
            if(isset($this->dateMetrics['rate'][$date])) {
                $item = $this->dateMetrics['rate'][$date];
                $rate_item['conversion_rate'] = Helper::calculationConversionRate($item['date_total_conversions'], $item['date_total_interactions'], 2,true);
                $rate_item['click_rate'] = Helper::calculationClickRate($item['date_total_clicks'], $item['date_total_impressions'],2,true);
            }else {
                $rate_item['conversion_rate'] = Helper::calculationConversionRate(0,0);
                $rate_item['click_rate'] = Helper::calculationClickRate(0,0);
            }
            $result[$date] = $rate_item;
        }
        return $result;
    }

    protected function calculationImpressionsAndClicks() {
        $result = [];
        foreach($this->dateRange as $date) {
            if(isset($this->dateMetrics['impressions_clicks'][$date])) {
                $item = $this->dateMetrics['impressions_clicks'][$date];
                $result[$date] = $item;
            }else {
                $result[$date] = [
                    'date_total_impressions' => 0,
                    'date_total_clicks' => 0,
                    'date' => $date
                ];
            }
        }
        return $result;
    }

    /**
     * 计算各个广告系列列表指标数据
     * @param $data
     */
    protected function calcutionCampaignListMetrics($data) {
        $result = [];
        foreach($data as $k=>$item) {

            $click_rate = Helper::calculationClickRate($item['total_clicks'],$item['total_impressions'],2,false);
            $conversion_rate = Helper::calculationConversionRate($item['total_conversions'], $item['total_interactions'],2,false);
            $average_cpc = Helper::calculationAverageCpc($item['total_cost_micros']/1000000, $item['total_clicks'],6);
            $cost_per_conversion = Helper::calculationCostPerConversion($item['total_cost_micros']/1000000, $item['total_conversions'],6);

            $itemValue = [];
            $itemValue['campaign_id'] = $item['campaign_id'];
            $itemValue['campaign_name'] = $item['campaign_name'];
            $itemValue['campaign_type'] = $item['campaign_type'];
            $itemValue['campaign_type_name'] = $item['campaign_type_name'];
            $itemValue['campaign_status'] = $item['campaign_status'];
            $itemValue['campaign_status_name'] = $item['campaign_status_name'];


            $itemValue[GoogleConstants::COST] = Helper::costMicrosToCost($item['total_cost_micros']);
            // 公共指标字段处理
            $itemValue[GoogleConstants::IMPRESSIONS] = $item['total_impressions'];
            $itemValue[GoogleConstants::CLICKS] = $item['total_clicks'];
            $itemValue[GoogleConstants::CONVERSIONS] = $item['total_conversions'];
            $itemValue[GoogleConstants::CLICKS_RATE] = $click_rate;
            $itemValue[GoogleConstants::CONVERSIONS_RATE] = $conversion_rate;
            $itemValue[GoogleConstants::AVERAGE_CPC] = $average_cpc;
            $itemValue[GoogleConstants::COST_PER_CONVERSION] =$cost_per_conversion;

            $itemValue[GoogleConstants::CLICKS_RATE_FORMAT] = $click_rate.'%';
            $itemValue[GoogleConstants::CONVERSIONS_RATE_FORMAT] = $conversion_rate.'%';

            $result[] = $itemValue;
        }

        // 是否需要排序
        if($this->sortFlag) {
            $result = Helper::sortDataArr($result, $this->sortField, $this->sortType);
        }

        return $result;
    }

}
