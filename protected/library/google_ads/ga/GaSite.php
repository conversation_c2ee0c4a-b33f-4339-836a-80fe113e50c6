<?php
/**
 * Created by PhpStorm.
 * User: bing
 * Date: 2020/01/13
 * Time: 14:49
 */

namespace common\library\google_ads\ga;

use CmsSiteDomain;
use common\components\BaseObject;
use common\library\cms\history\HistoryService;
use common\library\cms\inquiry\sitemail\SiteMail;
use common\library\customer_convert\convert_config\ConfigConstants;
use common\library\customer_convert\convert_config\ConfigService;
use common\library\google_ads\GoogleConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\setting\library\origin\Origin;
use LogUtil;

/**
 * This is the model class for table "tbl_google_ga_site".
 *
 * The followings are the available columns in table 'tbl_google_ga_site':
 * @property string $site_id
 * @property string $cms_site_id
 * @property integer $client_id
 * @property integer $ga_account_id
 * @property string $ga_account_name
 * @property string $web_property_id
 * @property integer $view_id
 * @property string $web_site_url
 * @property string $web_site_name
 * @property integer $access_id
 * @property integer $track_status
 * @property integer $enable_flag
 * @property string $timezone
 * @property string $currency
 * @property string $google_token
 * @property string $create_time
 * @property string $update_time
 * @property integer $lead_owner
 * @property integer $lead_type
 * @property integer $site_type
 * @property integer $transfer_to_client
 * @property integer $create_user
 * @property integer $property_id
 * @property string $measurement_id
 * @property integer ga_report_flag
 * @property integer event_report_flag
 */

class GaSite extends BaseObject {

    protected $clientId;
    protected $userId;

    const MAX_NAME_LEN = 128;
    const MAX_COUNT = 10; // 绑定网站数上限是10

    const NEW_GA_ACCOUNT = ['<EMAIL>'];

    public function __construct($clientId, $siteId = 0)
    {
        $this->clientId = $clientId;
        if($siteId)
        {
            $this->loadById($siteId);
        }
    }

    public function loadById($site_id) {
        $model = static::getModelClass()::model()->find('site_id=:site_id and client_id=:client_id',
            [':site_id'=>$site_id, ':client_id'=>$this->clientId]);
        $this->setModel($model);
        return $this;
    }

    public function loadByCmsSiteId($cms_site_id) {
        $model = static::getModelClass()::model()->find('client_id=:client_id AND cms_site_id=:cms_site_id',
            [':client_id'=>$this->clientId, ':cms_site_id'=>$cms_site_id]);

        if($model) {
            $this->setModel($model);
        }
        return $this;
    }

    public function isExist()
    {
        return !$this->isNew() && $this->enable_flag == 1;
    }


    public function getSid()
    {
        return "{$this->client_id}-{$this->site_id}";
    }

    public function getGid()
    {
        return $this->web_property_id;
    }

    public function setUserId($userId) {
        $this->userId = $userId;
    }

    public function beforeSave() {
        // 设置默认
        if($this->isNew())
        {
            $this->site_type = $this->site_type ?? GoogleConstants::SITE_TYPE_GA;
            //shop建站的不用校验
            if ($this->site_type == GoogleConstants::SITE_TYPE_GA) {
                $this->validateSite();
            } elseif ($this->site_type == GoogleConstants::SITE_TYPE_DTC) {
                $this->validateSite(false);
            }
            $this->insertGaWebProperty();
            $this->ga_report_flag = 0;
            $this->track_status = GoogleConstants::SITE_TRACK_STATUS_ON;
            $this->lead_owner = $this->getAdminUserId();
            $this->lead_type = GoogleConstants::SITE_LEAD_APPLY_TYPE_PRIVATE;
            $this->create_time = date("Y-m-d H:i:s");
        }
        if ($this->site_type == GoogleConstants::SITE_TYPE_SHOP) {
            $this->ga_report_flag = 0;
            $this->event_report_flag = 0;
        }

        return parent::beforeSave();
    }

    public function validateSite($checkLimit=true)
    {
        //判断是否为B2B平台下网站
        if (GaService::isB2BPlatformSite($this->web_site_url)) {
            throw new \RuntimeException(\Yii::t('google','The store of the platform does not support putting tracking code at present, please bind the self operated independent station'));
        }

        $webSites = $this->getWebSites();

        if( mb_strlen($this->web_site_name) > self::MAX_NAME_LEN)
        {
            throw new \RuntimeException(\Yii::t('google','Web site name exceeds the word limit, please edit'));
        }

        $webSiteHost = parse_url($this->web_site_url,PHP_URL_HOST);
        foreach($webSites as $site)
        {
            if($this->web_site_name == $site['web_site_name'])
            {
                throw new \RuntimeException(\Yii::t('google','Web site name has exited in list,please edit it'));
            }

            $siteHost = parse_url($site['web_site_url'],PHP_URL_HOST);
            if(in_array($webSiteHost,[$siteHost,'www'.$siteHost]))
            {
                throw new \RuntimeException(\Yii::t('google','Web site domain has exited in list,please edit it'));
            }
        }

        if( $checkLimit && !$this->cms_site_id && $this->isNew() && count($webSites) >= self::MAX_COUNT  )
        {
            throw new \RuntimeException(\Yii::t('google', 'The number of sites exceeds the limit'));
        }
    }

    public function insertGaWebProperty()
    {
        if(!$this->isNew())
        {
            return false;
        }

        $currency = GoogleConstants::DEFAULT_CURRENCY;
        $timezone = GoogleConstants::DEFAULT_TIMEZONE;
        $this->site_type = $this->site_type ?? GoogleConstants::SITE_TYPE_GA;
//        if (in_array($this->site_type, [GoogleConstants::SITE_TYPE_GA, GoogleConstants::SITE_TYPE_SHOP])) {
        if ($this->site_type == GoogleConstants::SITE_TYPE_SHOP) {
            //创建GA4属性
            try {
                $gaRepository = new GaRepository();
                $useAccount = $gaRepository->getUseGaAccount();
                if (!empty($useAccount['access_id']) && !empty($useAccount['ga_account_id'])) {
                    $googleServer = new GoogleServerApi();
                    $ga4Property = $googleServer->createGA4Property($useAccount['access_id'], $useAccount['ga_account_id'], $this->web_site_url, $this->web_site_name);
                    if (!empty($ga4Property['property_id'])) {
                        $siteDomain = CmsSiteDomain::model()->find('client_id = :client_id and site_id = :site_id and domain = :domain', array(':client_id'=> $this->client_id, ':site_id' => $this->cms_site_id, ':domain' => parse_url($this->web_site_url, PHP_URL_HOST)));
                        if (!empty($siteDomain)) {
                            $siteDomain->data_stream_id = $ga4Property['data_stream_id'] ?? 0;
                            $siteDomain->measurement_id = $ga4Property['measurement_id'] ?? '';
                            $siteDomain->save();
                        }
                        $this->measurement_id = $ga4Property['measurement_id'] ?? '';
                    }
                    $gaRepository->decrease();
                }
            } catch (\Exception $e) {
                LogUtil::info('google_server GA4 创建媒体资源异常. client_id:' . $this->client_id . ' error:' . $e->getMessage());
            }
        }
        $this->access_id = $useAccount['access_id'] ?? 0;
        $this->ga_account_id = $useAccount['ga_account_id'] ?? 0;
        $this->timezone = $timezone;
        $this->currency  = $currency;
        $this->property_id = $ga4Property['property_id'] ?? 0;
    }

    /**
     * @param $lead_type
     * @param $lead_owner
     * @return string
     */
    public function saveGaLead($lead_type, $lead_owner)
    {
        $model = $this->getModel();
        $model->lead_type = $lead_type;
        $model->lead_owner = $lead_owner;
        // 公海类型，线索归属人设置为0
        $auto_assign_type = ConfigConstants::AUTO_ASSIGN_TYPE_PRIVATE;
        if($lead_type == GoogleConstants::SITE_LEAD_APPLY_TYPE_PUBLIC)
        {
            $auto_assign_type = ConfigConstants::AUTO_ASSIGN_TYPE_PUBLIC;
            $model->lead_owner = 0;
        }

        $model->update_time = date('Y-m-d H:i:s', strtotime('now'));
        HistoryService::addOperateLog($this->clientId, $this->userId ??0, $this->cms_site_id, HistoryService::MODULE_SYSTEM, HistoryService::MODULE_MARKETING_LEAD_SITE_BIND,$this->web_site_name.'('.$this->web_site_url.')');
        $res = $model->update(['lead_type','lead_owner','update_time']);

//        $config = new ConfigService($this->clientId, [Origin::SYS_ORIGIN_WEBSITE]);
//        $config->updateOriginConfigAutoAssign($auto_assign_type, $lead_owner);

        \common\library\google_ads\ga\GaSiteCacheableRepo::instance($this->client_id)->refreshCache($this->site_id);

        return $res;
    }

    /**
     * @return array|\CDbDataReader
     * @throws \CDbException
     * @throws \CException
     */
    public function existGaSite($ga_account_id) {
        $sql = "SELECT client_id,ga_account_id,web_property_id,view_id FROM tbl_google_ga_site WHERE client_id = {$this->clientId} and ga_account_id={$ga_account_id}";
        return $this->getModel()->getDbConnection()->createCommand($sql)->queryAll(true);
    }

    /**
     * 去重获取新的website
     * @param $ga_account_id
     * @param $site_data
     * @return array
     * @throws \CDbException
     * @throws \CException
     */
    public function getDiffSite($ga_account_id, $site_data) {
        $result = $this->existGaSite($ga_account_id);
        foreach($site_data as $k=>$item) {
            foreach($result as $row) {
                if($item['web_property_id'] == $row['web_property_id'] && $item['view_id'] == $row['view_id']) {
                    unset($site_data[$k]);
                }
            }
        }
        $site_data = array_values($site_data);
        return $site_data;
    }

    /**
     * 取第一个存在的ga_account_id 作为网站绑定Ga账号
     */
    public function getExistGaAccountId() {
        $sql = "SELECT client_id,ga_account_id FROM tbl_google_ga_site WHERE client_id = {$this->clientId} ORDER BY create_time ASC";
        $row =  $this->getModel()->getDbConnection()->createCommand($sql)->queryRow(true);
        return $row['ga_account_id'] ?? 0;
    }

    /**
     * @return array|\CDbDataReader
     * @throws \CDbException
     * @throws \CException
     */
    public function getWebSites()
    {
        $sql = "SELECT access_id,ga_account_id,web_site_url,web_site_name, enable_flag FROM tbl_google_ga_site WHERE client_id =:client_id  and cms_site_id =0 and enable_flag=:enable_flag";
        return $this->getModel()->getDbConnection()->createCommand($sql)->queryAll(true, [':client_id' => $this->clientId, ':enable_flag'=> BaseObject::ENABLE_FLAG_TRUE ]);
    }

    /**
     * @return int
     */
    public function getAdminUserId() {
        return PrivilegeService::getInstance($this->clientId)->getAdminUserId() ?? 0;
    }

    public function getModelClass(){
        return \GoogleGaSite::class;
    }

    public function afterSave()
    {
        $operation = HistoryService::MODULE_MARKETING_GA_SITE_MODIFY;
        if($this->isNew()) {
            $operation = HistoryService::MODULE_MARKETING_GA_SITE_BIND;
            //变更域名
        } else if(!$this->isNew() && $this->_oldAttributes['web_site_url'] != $this->web_site_url) {
//            if (in_array($this->site_type, [GoogleConstants::SITE_TYPE_GA, GoogleConstants::SITE_TYPE_SHOP])) {
            if ($this->site_type == GoogleConstants::SITE_TYPE_SHOP) {
                $googleGa = new GoogleGa($this->access_id);
                $webProperty = $googleGa->updateWebSite($this->ga_account_id,$this->web_property_id, $this->web_site_url, $this->web_site_name);
                //新版ga4，修改绑定关系
                $googleServer = new GoogleServerApi();
                $googleServer->updateGA4Property($this->access_id, $this->property_id, $this->web_site_url, $this->web_site_name);
            }
        }


        if($this->clientId > 0) {
            HistoryService::addOperateLog($this->clientId, $this->userId ?? $this->create_user, $this->cms_site_id,HistoryService::MODULE_SYSTEM, $operation, $this->web_site_name.'('.$this->web_site_url.')');
            \common\library\google_ads\ga\GaSiteCacheableRepo::instance($this->client_id)->refreshCache($this->site_id);
        }


    }

    protected function processDelete($model)
    {
        $this->enable_flag = $model->enable_flag  = self::ENABLE_FLAG_FALSE;
        return $result = $model->update(['enable_flag']);
    }

    protected function afterDelete()
    {
        $gaRepository = new GaRepository();
        $gaRepository->recordDelete($this->access_id,$this->ga_account_id);
        if($this->clientId > 0) {
            HistoryService::addOperateLog($this->clientId, $this->userId ??0, $this->cms_site_id, HistoryService::MODULE_SYSTEM, HistoryService::MODULE_MARKETING_GA_SITE_DELETE,$this->web_site_name.'('.$this->web_site_url.')');
        }

        $conversationSetting = new \common\library\cms\conversation\CmsConversationSetting($this->client_id, $this->site_id);
        if (!$conversationSetting->isNew()) {
            $conversationSetting->delete();
        }

        //删除相应询盘邮箱绑定记录
        $siteMail = new SiteMail($this->clientId);
        $siteMail->loadById($this->site_id);
        !$siteMail->isNew() && $siteMail->delete();

        \common\library\google_ads\ga\GaSiteCacheableRepo::instance($this->client_id)->refreshCache($this->site_id);

        parent::afterDelete();
    }


}
