<?php

namespace common\library\customer_convert\convert_config;

use common\components\BaseObject;
use common\library\ai\classify\setting\ApplySettings;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\setting\library\origin\Origin;
use common\library\setting\library\status\Status;


class ConfigConstants {
    
    
    const TABLE_NAME = 'tbl_customer_convert_config';
    
    const FIELD_ENABLE_FLAG     = 'enable_flag';
    const FIELD_ORIGIN_ID       = 'origin_id';
    const FIELD_RELATE_ID       = 'relate_id';
    const FIELD_CONVERT_MODULE  = 'convert_module';
    const FIELD_FILTER_CONFIG   = 'filter_config';
    const FIELD_UPDATE_DATA     = 'update_data';
    const FIELD_DUPLICATE_CONFIG     = 'duplicate_config';
    const KEY_UPDATE_DATA_STATUS          = 'status';
    const KEY_UPDATE_DATA_TRAIL_STATUS    = 'trail_status';
    const KEY_UPDATE_DATA_CLIENT_TAG_LIST = 'client_tag_list';
    const KEY_UPDATE_DATA_AUTO_ASSIGN_TYPE = 'auto_assign_type';
    const KEY_UPDATE_DATA_ASSIGN_USER_ID = 'assign_user_id';

    const KEY_FILTER_CONFIG_EDM_BEHAVIOR          = 'edm_behavior';
    const KEY_FILTER_CONFIG_MAIL_SEND_QUANTITY    = 'mail_send_quantity';
    const KEY_FILTER_CONFIG_CONDITION_RELATION    = 'condition_relation';
    const KEY_FILTER_CONFIG_MAIL_RECEIVE_QUANTITY = 'mail_receive_quantity';
    
    const KEY_FILTER_CONFIG_IGNORE_AREAS           = 'ignore_areas';
    const KEY_FILTER_CONFIG_IGNORE_USER_MAIL_ID    = 'ignore_user_mail_id';
    const KEY_FILTER_CONFIG_IGNORE_ALIBABA_ACCOUNT = 'ignore_alibaba_account';


    const KEY_DUPLICATE_CONFIG_DUPLICATE_FLAG = 'duplicate_flag';
    const KEY_DUPLICATE_CONFIG_INFO_UPDATE_TYPE = 'info_update_type';
    const KEY_DUPLICATE_CONFIG_CONVERTED_LEAD_UPDATE = 'converted_lead_update';
    const KEY_DUPLICATE_CONFIG_TRAIL_UPDATE_TYPE = 'trail_update_type';
    const KEY_DUPLICATE_CONFIG_MATCH_SCOPE = 'match_scope';
    const KEY_DUPLICATE_CONFIG_EXTRA_MATCH_FIELD = 'extra_match_field';

    // 来源流转固定的非系统匹配字段
    const SOURCE_CONVERT_FIXED_MATCH_FIELD_EMAIL_DOMAIN = 'email_domain';

    const AUTO_ASSIGN_TYPE_PRIVATE = 0;//分配到私海
    const AUTO_ASSIGN_TYPE_PUBLIC = 1;//分配到公海

    //匹配资料/动态的更新选择配置
    const TRAIL_UPDATE_TYPE_NO_SELECT = null; //默认不选择
    const TRAIL_UPDATE_TYPE_FIRST = 1; //最早一条
    const TRAIL_UPDATE_TYPE_LATEST = 2; //最近一条
    const TRAIL_UPDATE_TYPE_LATEST_CONTACT  = 3;//最近联系

    //更新资料形式
    const INFO_UPDATE_TYPE_INCREMENT = 1; //增量更新
    const INFO_UPDATE_TYPE_COVER = 2; //覆盖更新
    const INFO_UPDATE_TYPE_AUTOMATION = 3; //自动化更新

    const DEFAULT_UPDATE_DATA = [
        self::KEY_UPDATE_DATA_STATUS          => Status::SYS_STATUS_NEW,
        self::KEY_UPDATE_DATA_TRAIL_STATUS    => Status::SYS_STATUS_NONE,
        self::KEY_UPDATE_DATA_CLIENT_TAG_LIST => [],
        self::KEY_UPDATE_DATA_AUTO_ASSIGN_TYPE => self::AUTO_ASSIGN_TYPE_PRIVATE,
        self::KEY_UPDATE_DATA_ASSIGN_USER_ID => '',//空代表按照原始逻辑分配跟进人
    ];
    
    const DEFAULT_FILTER_CONFIG = [
        
        self::KEY_FILTER_CONFIG_EDM_BEHAVIOR           => [
            
            ApplySettings::EDM_OPEN,
            ApplySettings::EDM_REPLY,
            ApplySettings::EDM_CLICK,
        ],
        self::KEY_FILTER_CONFIG_MAIL_SEND_QUANTITY     => 0,
        self::KEY_FILTER_CONFIG_CONDITION_RELATION     => 1,
        self::KEY_FILTER_CONFIG_MAIL_RECEIVE_QUANTITY  => 0,
        self::KEY_FILTER_CONFIG_IGNORE_AREAS           => ['china', 'hk', 'tw', 'mo'],
        self::KEY_FILTER_CONFIG_IGNORE_USER_MAIL_ID    => [],
        self::KEY_FILTER_CONFIG_IGNORE_ALIBABA_ACCOUNT => [],
    ];


    const DEFAULT_DUPLICATE_CONFIG = [
        self::KEY_DUPLICATE_CONFIG_DUPLICATE_FLAG => 0,
        self::KEY_DUPLICATE_CONFIG_INFO_UPDATE_TYPE => 1,
        self::KEY_DUPLICATE_CONFIG_CONVERTED_LEAD_UPDATE => 0,
        self::KEY_DUPLICATE_CONFIG_TRAIL_UPDATE_TYPE => self::TRAIL_UPDATE_TYPE_NO_SELECT,
        self::KEY_DUPLICATE_CONFIG_MATCH_SCOPE => PrivilegeConstants::PRIVILEGE_SCOPE_OWNER,
        self::KEY_DUPLICATE_CONFIG_EXTRA_MATCH_FIELD => null //默认为空，不匹配任何字段
    ];

    const COMMON_MODULE_CONFIG_MAP = [
        
        self::FIELD_FILTER_CONFIG => [
            
            \Constants::TYPE_LEAD    => [
                
                self::KEY_FILTER_CONFIG_IGNORE_USER_MAIL_ID    => [],
                self::KEY_FILTER_CONFIG_IGNORE_ALIBABA_ACCOUNT => [],
            ],
            \Constants::TYPE_COMPANY => [
                
                self::KEY_FILTER_CONFIG_IGNORE_USER_MAIL_ID => [],
                self::KEY_FILTER_CONFIG_IGNORE_AREAS        => [],
            ],
        ],
    ];
    
    const CONVERT_MODULE_UPDATE_DATA_MAP = [
        
        \Constants::TYPE_LEAD    => [

            self::KEY_UPDATE_DATA_STATUS,
            self::KEY_UPDATE_DATA_CLIENT_TAG_LIST,
            self::KEY_UPDATE_DATA_AUTO_ASSIGN_TYPE,
            self::KEY_UPDATE_DATA_ASSIGN_USER_ID,
        ],
        \Constants::TYPE_COMPANY => [
            
            self::KEY_UPDATE_DATA_TRAIL_STATUS,
            self::KEY_UPDATE_DATA_CLIENT_TAG_LIST,
            self::KEY_UPDATE_DATA_AUTO_ASSIGN_TYPE,
            self::KEY_UPDATE_DATA_ASSIGN_USER_ID,
        ],
    ];

    const CONVERT_MODULE_DUPLICATE_CONFIG_MAP = [

        \Constants::TYPE_LEAD    => [

            self::KEY_DUPLICATE_CONFIG_DUPLICATE_FLAG,
            self::KEY_DUPLICATE_CONFIG_INFO_UPDATE_TYPE,
            self::KEY_DUPLICATE_CONFIG_CONVERTED_LEAD_UPDATE,
            self::KEY_DUPLICATE_CONFIG_TRAIL_UPDATE_TYPE,
            self::KEY_DUPLICATE_CONFIG_MATCH_SCOPE,
            self::KEY_DUPLICATE_CONFIG_EXTRA_MATCH_FIELD
        ],
        \Constants::TYPE_COMPANY => [

            self::KEY_DUPLICATE_CONFIG_DUPLICATE_FLAG,
            self::KEY_DUPLICATE_CONFIG_INFO_UPDATE_TYPE,
            self::KEY_DUPLICATE_CONFIG_TRAIL_UPDATE_TYPE,
            self::KEY_DUPLICATE_CONFIG_MATCH_SCOPE,
            self::KEY_DUPLICATE_CONFIG_EXTRA_MATCH_FIELD
        ],
    ];


    const ORIGIN_FILTER_CONFIG_MAP = [
        
        Origin::SYS_ORIGIN_ID_MARKETING => [
            
            self::KEY_FILTER_CONFIG_EDM_BEHAVIOR,
        ],
        
        
        Origin::SYS_ORIGIN_SYSTEM_PERSONAL_INQUIRY    => [
            
            self::KEY_FILTER_CONFIG_MAIL_SEND_QUANTITY,
            self::KEY_FILTER_CONFIG_CONDITION_RELATION,
            self::KEY_FILTER_CONFIG_MAIL_RECEIVE_QUANTITY,
        ],
        Origin::SYS_ORIGIN_SYSTEM_QUOTE               => [
            
            self::KEY_FILTER_CONFIG_MAIL_SEND_QUANTITY,
            self::KEY_FILTER_CONFIG_CONDITION_RELATION,
            self::KEY_FILTER_CONFIG_MAIL_RECEIVE_QUANTITY,
        ],
        Origin::SYS_ORIGIN_SYSTEM_SAMPLE_PI           => [
            
            self::KEY_FILTER_CONFIG_MAIL_SEND_QUANTITY,
            self::KEY_FILTER_CONFIG_CONDITION_RELATION,
            self::KEY_FILTER_CONFIG_MAIL_RECEIVE_QUANTITY,
        ],
        Origin::SYS_ORIGIN_SYSTEM_PI                  => [
            
            self::KEY_FILTER_CONFIG_MAIL_SEND_QUANTITY,
            self::KEY_FILTER_CONFIG_CONDITION_RELATION,
            self::KEY_FILTER_CONFIG_MAIL_RECEIVE_QUANTITY,
        ],
        Origin::SYS_ORIGIN_SYSTEM_CI                  => [
            
            self::KEY_FILTER_CONFIG_MAIL_SEND_QUANTITY,
            self::KEY_FILTER_CONFIG_CONDITION_RELATION,
            self::KEY_FILTER_CONFIG_MAIL_RECEIVE_QUANTITY,
        ],
        Origin::SYS_ORIGIN_SYSTEM_PL                  => [
            
            self::KEY_FILTER_CONFIG_MAIL_SEND_QUANTITY,
            self::KEY_FILTER_CONFIG_CONDITION_RELATION,
            self::KEY_FILTER_CONFIG_MAIL_RECEIVE_QUANTITY,
        ],
        Origin::SYS_ORIGIN_SYSTEM_BANK_SLIP           => [
            
            self::KEY_FILTER_CONFIG_MAIL_SEND_QUANTITY,
            self::KEY_FILTER_CONFIG_CONDITION_RELATION,
            self::KEY_FILTER_CONFIG_MAIL_RECEIVE_QUANTITY,
        ],
        Origin::SYS_ORIGIN_SYSTEM_OTHER_BUSINESS_MAIL => [
            
            self::KEY_FILTER_CONFIG_MAIL_SEND_QUANTITY,
            self::KEY_FILTER_CONFIG_CONDITION_RELATION,
            self::KEY_FILTER_CONFIG_MAIL_RECEIVE_QUANTITY,
        ],
    ];
    
    const DEFAULT_CONVERT_MODULE_MAP = [

        Origin::SYS_ORIGIN_FACEBOOK_LEAD => \Constants::TYPE_LEAD,
        Origin::SYS_ORIGIN_WEBSITE       => \Constants::TYPE_LEAD,
    ];

    const ONLY_CAN_CONVERT_TO_LEAD_LIST = [
        
        Origin::SYS_ORIGIN_ID_MARKETING
    ];
    
    const ONLY_CAN_CONVERT_TO_COMPANY_LIST = [
        
        Origin::SYS_ORIGIN_SYSTEM_SAMPLE_PI,
        Origin::SYS_ORIGIN_SYSTEM_PI,
        Origin::SYS_ORIGIN_SYSTEM_CI,
        Origin::SYS_ORIGIN_SYSTEM_PL,
        Origin::SYS_ORIGIN_SYSTEM_BANK_SLIP,
        Origin::SYS_ORIGIN_SYSTEM_OTHER_BUSINESS_MAIL,
    ];
    
    const ALLOWED_ORIGIN_LIST = [
        
        Origin::SYS_ORIGIN_UNKNOWN,
        
//        Origin::SYS_ORIGIN_ID_OKKI_SHOP,
        Origin::SYS_ORIGIN_WEBSITE,
        
        Origin::SYS_ORIGIN_ALIBABA_INQUIRY,
        Origin::SYS_ORIGIN_ALIBABA_NAME_CARD,
        Origin::SYS_ORIGIN_ALIBABA_RFQ,
        Origin::SYS_ORIGIN_ALIBABA_TM,
        Origin::SYS_ORIGIN_ALIBABA_ALIBABA,
        
        Origin::SYS_ORIGIN_SYSTEM_PERSONAL_INQUIRY,
        Origin::SYS_ORIGIN_SYSTEM_QUOTE,
        Origin::SYS_ORIGIN_SYSTEM_SAMPLE_PI,
        Origin::SYS_ORIGIN_SYSTEM_PI,
        Origin::SYS_ORIGIN_SYSTEM_CI,
        Origin::SYS_ORIGIN_SYSTEM_PL,
        Origin::SYS_ORIGIN_SYSTEM_BANK_SLIP,
        Origin::SYS_ORIGIN_SYSTEM_OTHER_BUSINESS_MAIL,
        
        Origin::SYS_ORIGIN_ID_MARKETING,
        
        Origin::SYS_ORIGIN_GLOBAL_SOURCES,
        Origin::SYS_ORIGIN_GLOBAL_MARKET,
        Origin::SYS_ORIGIN_MADE_IN_CHINA,
        Origin::SYS_ORIGIN_ELECTRICITY_PLATFORM,
        
        Origin::SYS_ORIGIN_SOCIAL_PLATFORM,
//        Origin::SYS_ORIGIN_FACEBOOK_PAGE,
        Origin::SYS_ORIGIN_FACEBOOK_LEAD,
        Origin::SYS_ORIGIN_SYSTEM_CARD,
        Origin::SYS_ORIGIN_EXHIBITION,
        Origin::SYS_ORIGIN_INTERNET,
        Origin::SYS_ORIGIN_AI_SDR
    ];
    
    const ALLOWED_CONVERT_MODULE_LIST = [
        
        \Constants::TYPE_LEAD,
        \Constants::TYPE_COMPANY,
    ];

//    PO,废弃？
    const MIGRATE_FILTER_MAIL_TYPE_MAP = [
        
        'personal_inquiry' => Origin::SYS_ORIGIN_SYSTEM_PERSONAL_INQUIRY,
        'price'            => Origin::SYS_ORIGIN_SYSTEM_QUOTE,
        'sample'           => Origin::SYS_ORIGIN_SYSTEM_SAMPLE_PI,
        'pi'               => Origin::SYS_ORIGIN_SYSTEM_PI,
        'ci'               => Origin::SYS_ORIGIN_SYSTEM_CI,
        'pl'               => Origin::SYS_ORIGIN_SYSTEM_PL,
        'bs'               => Origin::SYS_ORIGIN_SYSTEM_BANK_SLIP,
        'bl'               => Origin::SYS_ORIGIN_SYSTEM_OTHER_BUSINESS_MAIL,
    ];
    
    const MIGRATE_TRADE_BEHAVIOR = [
        
        'trade'         => Origin::SYS_ORIGIN_ALIBABA_INQUIRY,
        'exchange_card' => Origin::SYS_ORIGIN_ALIBABA_NAME_CARD,
        'other'         => Origin::SYS_ORIGIN_ALIBABA_ALIBABA,
    ];
    
    const MIGRATE_PLATFORM = [
        
        Origin::SYS_ORIGIN_GLOBAL_SOURCES,
        Origin::SYS_ORIGIN_GLOBAL_MARKET,
        Origin::SYS_ORIGIN_MADE_IN_CHINA,
        Origin::SYS_ORIGIN_ELECTRICITY_PLATFORM,
    ];

    // 来源流转时具有相同固定字段配置的来源
    const DEFAULT_MATCH_FIELD_ORIGIN_SAME_CONFIG = [
        'DEFAULT_MATCH_FIELD' => [Origin::SYS_ORIGIN_UNKNOWN],
        'DEFAULT_MATCH_FIELD_MAP_TYPE_EMAIL' => [
            Origin::SYS_ORIGIN_ID_MARKETING,
            Origin::SYS_ORIGIN_WEBSITE,

            Origin::SYS_ORIGIN_SYSTEM_PERSONAL_INQUIRY,
            Origin::SYS_ORIGIN_SYSTEM_QUOTE,
            Origin::SYS_ORIGIN_SYSTEM_SAMPLE_PI,
            Origin::SYS_ORIGIN_SYSTEM_PI,
            Origin::SYS_ORIGIN_SYSTEM_CI,
            Origin::SYS_ORIGIN_SYSTEM_PL,
            Origin::SYS_ORIGIN_SYSTEM_BANK_SLIP,
            Origin::SYS_ORIGIN_SYSTEM_OTHER_BUSINESS_MAIL,

            Origin::SYS_ORIGIN_ALIBABA_INQUIRY,
            Origin::SYS_ORIGIN_ALIBABA_NAME_CARD,
            Origin::SYS_ORIGIN_ALIBABA_RFQ,
            Origin::SYS_ORIGIN_ALIBABA_TM,
            Origin::SYS_ORIGIN_ALIBABA_ALIBABA,

            Origin::SYS_ORIGIN_GLOBAL_SOURCES,
            Origin::SYS_ORIGIN_GLOBAL_MARKET,
            Origin::SYS_ORIGIN_MADE_IN_CHINA,
        ]
    ];

    // 来源流转时固定的匹配字段
    const DEFAULT_MATCH_FIELD_MAP = [
        'DEFAULT_MATCH_FIELD' => [],
        'DEFAULT_MATCH_FIELD_MAP_TYPE_EMAIL' => [
            \Constants::TYPE_CUSTOMER => [['id'=>'email','name'=>'邮箱']],
            \Constants::TYPE_LEAD_CUSTOMER => [['id'=>'email','name'=>'邮箱']]
        ]
    ];

    // 匹配字段过滤配置
    const MATCH_FIELD_FILTER_CONFIG_MAP = [
        // OKKI获客
//        Origin::SYS_ORIGIN_WEBSITE => [],
        Origin::SYS_ORIGIN_ID_MARKETING => ['tel_list','homepage','contact','fax','tel','address'],
        // alibaba
//        Origin::SYS_ORIGIN_ALIBABA =>[],
//        Origin::SYS_ORIGIN_ALIBABA_INQUIRY =>[],
//        Origin::SYS_ORIGIN_ALIBABA_NAME_CARD =>[],
//        Origin::SYS_ORIGIN_ALIBABA_RFQ =>[],
//        Origin::SYS_ORIGIN_ALIBABA_TM =>[],
//        Origin::SYS_ORIGIN_ALIBABA_ALIBABA =>[],
        // 业务邮件
        Origin::SYS_ORIGIN_SYSTEM_PERSONAL_INQUIRY => ['fax','tel_list','contact'],
        Origin::SYS_ORIGIN_SYSTEM_QUOTE => ['fax','tel_list','contact'],
        Origin::SYS_ORIGIN_SYSTEM_SAMPLE_PI => ['fax','tel_list','contact'],
        Origin::SYS_ORIGIN_SYSTEM_PI => ['fax','tel_list','contact'],
        Origin::SYS_ORIGIN_SYSTEM_CI => ['fax','tel_list','contact'],
        Origin::SYS_ORIGIN_SYSTEM_PL => ['fax','tel_list','contact'],
        Origin::SYS_ORIGIN_SYSTEM_BANK_SLIP => ['fax','tel_list','contact'],
        Origin::SYS_ORIGIN_SYSTEM_OTHER_BUSINESS_MAIL => ['fax','tel_list','contact'],
        // B2B平台
        Origin::SYS_ORIGIN_SYSTEM_B2B_PLATFORM => ['fax','tel_list','contact'],
        Origin::SYS_ORIGIN_GLOBAL_SOURCES => ['fax','tel_list','contact'],
        Origin::SYS_ORIGIN_GLOBAL_MARKET => ['fax','tel_list','contact'],
        Origin::SYS_ORIGIN_MADE_IN_CHINA => ['fax','tel_list','contact'],
        // 社媒
//        Origin::SYS_ORIGIN_SYSTEM_SOCIAL_MEDIA =>[],
//        Origin::SYS_ORIGIN_SOCIAL_PLATFORM =>[],
        Origin::SYS_ORIGIN_FACEBOOK_LEAD =>['tel_list','contact','fax','short_name','homepage'],
        //OKKI名片夹
//        Origin::SYS_ORIGIN_SYSTEM_CARD =>[],
//        //展会
//        Origin::SYS_ORIGIN_EXHIBITION =>[],
//        //互联网
//        Origin::SYS_ORIGIN_INTERNET =>[],
    ];

    const CONDITION_FIELD_FILTER_CONFIG_MAP = [
        [
            "origin_id" => [],  // 空则代表所有来源都展示该选项
            "field_id" => "country",
            "field_name" => "国家地区",
            "operators" => [
                "in",
                "not_in",
                "is_null",
                "not_null"
            ]
        ],
        [
            "origin_id" => [Origin::SYS_ORIGIN_AI_SDR],
            "field_id" => "recommend_main_product_ids",
            "field_name" => "推荐主营产品",
            "operators" => [
                "in",
                "not_in",
                "is_null",
                "not_null"
            ]
        ],
        [
            "origin_id" => [Origin::SYS_ORIGIN_WEBSITE],
            "field_id" => "site_id",
            "field_name" => "独立站",
            "operators" => [
                "="
            ]
        ],
        [
            "origin_id" => [Origin::SYS_ORIGIN_WEBSITE,Origin::SYS_ORIGIN_SYSTEM_BUSINESS_MAIL,Origin::SYS_ORIGIN_SYSTEM_B2B_PLATFORM],
            "field_id" => "inquiry_form",
            "field_name" => "询盘表单",
            "operators" => [
                "=",
                "<>",
                "contains",
                "not_contains",
                "is_null",
                "not_null"
            ]
        ],
        [
            "origin_id" => [Origin::SYS_ORIGIN_ALIBABA],
            "field_id" => "alibaba_buyer_identity",
            "field_name" => "阿里买家身份",
            "operators" => [
                "=",
                "<>",
                "in",
                "not_in",
                "is_null",
                "not_null"
            ]
        ],
//        [
//            "origin_id" => [Origin::SYS_ORIGIN_WEBSITE,Origin::SYS_ORIGIN_SYSTEM_BUSINESS_MAIL,Origin::SYS_ORIGIN_SYSTEM_B2B_PLATFORM],
//            "field_id" => "ai_inquiry_valid",
//            "field_name" => "AI询盘有效",
//            "operators" => [
//                "="
//            ]
//        ],
    ];
    
}