<?php


namespace common\library\performance_v2\refer;

use common\library\lead\Lead;
use Constants;

/**
 * Class LeadPerformance
 * @package common\library\performance_v2\refer
 * @property Lead $refer
 */
class LeadPerformance extends AbstractPerformanceRefer
{

    public function getClientId()
    {
        return $this->refer->getClientId();
    }

    function getReferId()
    {
        return $this->refer->lead_id;
    }

    public function getReferType()
    {
        return Constants::TYPE_LEAD;
    }

    public function needRecord()
    {
        return $this->refer->is_archive == Lead::ARCHIVE_OK;
    }

    public function getUserRates($performanceField)
    {
        return [$this->refer->create_user_id => 100];
    }

    public function getDepartmentRates()
    {
        return [];
    }

    public function getCreateUser()
    {
        return $this->refer->create_user_id;
    }
}