<?php
/**
 * This file is part of the xiaoman/crm.
 *
 * (c) sevenshi <<EMAIL>>
 *
 */

namespace common\library\performance_v2;


use Carbon\Carbon;
use common\components\BaseObject;
use common\library\account\Client;
use common\library\account\UserList;
use common\library\cash_collection\CashCollectionList;
use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldList;
use common\library\department\Department;
use common\library\department\DepartmentMember;
use common\library\department\DepartmentPermission;
use common\library\department\DepartmentRedis;
use common\library\department\DepartmentService;
use common\library\department\Helper as DepartmentHelper;
use common\library\edm\EdmTask;
use common\library\formula\Parser;
use common\library\invoice\batch\OrderBatchOperator;
use common\library\invoice\Order;
use common\library\invoice\OrderList;
use common\library\invoice\status\InvoiceStatusService;
use common\library\lead\Lead;
use common\library\mail\setting\folder\MailFolderList;
use common\library\opportunity\Opportunity;
use common\library\opportunity\OpportunityList;
use common\library\opportunity\stage\migration\OpportunityStage;
use common\library\opportunity\statistics\OpportunityStageStatisticsService;
use common\library\performance_v2\goal\PerformanceV2Goal;
use common\library\performance_v2\goal\PerformanceV2GoalList;
use common\library\performance_v2\goal_config\NewPerformanceV2ProcessService;
use common\library\performance_v2\goal_config\PerformanceV2GoalConfig;
use common\library\performance_v2\goal_config\PerformanceV2GoalConfigList;
use common\library\performance_v2\goal_config\PerformanceV2ProcessService;
use common\library\performance_v2\record\PerformanceV2RecordList;
use common\library\performance_v2\rule\PerformanceV2Rule;
use common\library\performance_v2\rule\PerformanceV2RuleList;
use common\library\performance_v2\team_wall\KeyBehaviorList;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\queue_v2\job\PerformanceV2RecordJob;
use common\library\queue_v2\QueueService;
use common\library\setting\library\fund\relation\CostItemInvoiceRelationFilter;
use common\library\setting\user\UserSetting;
use common\library\statistics\decorator\field_refer\OpportunityStageType;
use common\library\swarm\SwarmService;
use common\library\util\PgsqlUtil;
use common\library\work_journal\template\WorkJournalTemplateList;
use common\library\workflow\WorkflowConstant;
use Constants;
use DateTime;
use PgActiveRecord;
use ProjectActiveRecord;
use User;


class Helper
{
    // 对于订单而言需要关联其他对象的字段map
    private const ORDER_HAS_RELATE_INFO_MAP = [
        'collect_amount',
        'collect_amount_rmb',
        'collect_amount_usd',
        'real_profit_rmb',
        'real_profit_usd',
        'real_profit',
        // 公式字段使用
        '2.collect_amount',
        '2.collect_amount_rmb',
        '2.collect_amount_usd',
        '2.real_profit_rmb',
        '2.real_profit_usd',
        '2.real_profit'
    ];
    private const SPECIAL_INFO_MAP=[
        \Constants::TYPE_CASH_COLLECTION=>[
            "exchange_loss_amount"
        ]

    ];

    /**
     * 针对选择range的情况
     * @param $clientId
     * @param $timeType
     * @param $startDate
     * @param $endDate
     * @return mixed|string
     * @throws \Exception
     */
    public static function getWhenRangeChangeTimeType($clientId, $timeType, $startDate, $endDate)
    {
        if ($timeType != PerformanceV2Constant::TIME_TYPE_RANGE) return $timeType;
        return self::checkRangeTimeType($clientId, $startDate, $endDate);
    }

    /**
     * 目标管理-获取不展示
     * 特殊情况，如果某个人在多个部门，若其中一个部门需要展示，那么这个人就需要展示
     * @param $clientId
     * @return array
     */
    public static function getFilterUserIdsAndDepartmentIds($clientId)
    {
        $client = Client::getClient($clientId);
        $displaySetting = $client->getSettingAttributes([Client::PERFORMANCE_V2_DISPLAY_CONFIG]);
        $displaySetting = json_decode($displaySetting[Client::PERFORMANCE_V2_DISPLAY_CONFIG] ?? '[]', true);
        $settingUserIds = $displaySetting['user_ids'] ?? [];
        $settingDepartmentIds = $displaySetting['department_ids'] ?? [];
        $departmentUserIds    = $settingUserIds;
        $allDepartmentIds     = DepartmentHelper::getChildrenIds($clientId, 0);
        // 获取隐藏部门的所有子部门
        foreach ($settingDepartmentIds as $settingDepartmentId) {
            $subSettingDepartmentIds = DepartmentHelper::getChildrenIds($clientId, $settingDepartmentId);
            $settingDepartmentIds = array_unique(array_merge($settingDepartmentIds, is_array($subSettingDepartmentIds) ? $subSettingDepartmentIds : []));
        }
        $departmentRedis      = new DepartmentRedis();
        // 包含子部门的人
        $departmentMapUserIds = $departmentRedis->getDepartmentUserMap($clientId, $allDepartmentIds);
        // 获取我的企业部门下的人，不包含子部门的人
        $departmentMapUserIds[0] = $departmentRedis->getDepartmentUser($clientId, 0);
        $allDepartmentIds[] = 0;
        // 要显示的人
        $otherUserIds         = [];
        // 先找部门不显示的人
        foreach ($allDepartmentIds as $departmentId) {
            $departmentUserIdList = $departmentMapUserIds[$departmentId] ?? [];
            if (empty($departmentUserIdList) || !is_array($departmentUserIdList)) {
                continue;
            }
            if (in_array($departmentId, $settingDepartmentIds)) {
                $departmentUserIds = array_unique(array_merge($departmentUserIds, $departmentUserIdList));
            } else {
                $otherUserIds = array_unique(array_merge($otherUserIds, $departmentUserIdList));
            }
        }
        // 再找这个人在多个部门情况，这个人就要显示
        $needShowUserIds = array_intersect($departmentUserIds, $otherUserIds);
        if (!empty($needShowUserIds)) {
            $departmentUserIds = array_diff($departmentUserIds, $needShowUserIds);
        }
        return [$departmentUserIds, $settingDepartmentIds];
    }

    /**
     * 过滤账号设置不显示
     * @param $clientId
     * @param array $showUserIds
     * @param array $showDepartmentIds 注意查询我的企业的情况，这里没做处理需要传入所有的
     * @return array
     */
    public static function filterUserIdsAndDepartmentIds($clientId, array $showUserIds = [], array $showDepartmentIds = [])
    {
        list($settingUserIds, $settingDepartmentIds) = self::getFilterUserIdsAndDepartmentIds($clientId);
        $showUserIds = array_diff($showUserIds, $settingUserIds);
        $showDepartmentIds = array_diff($showDepartmentIds, $settingDepartmentIds);
        return [$showUserIds, $showDepartmentIds];
    }

    /**
     * 若账号有设置目标完成情况的规则排序，那么我们按照账号规则返回
     * 设置了规则顺序后面若有规则新增或者删除怎么办？
     *  1、新增的放在账号设置的顺序前面
     *  2、删除的本来就就不会出现，不用处理
     * @param $clientId
     * @param $userId
     * @param $targetType
     * @param array $rules
     * @return array
     */
    public static function getPerformanceV2RuleSortByUserSetting($clientId, $userId, $targetType, array $rules = [])
    {
        if (empty($rules)) {
            return $rules;
        }
        // 用户设置
        $setting = new \common\library\setting\user\UserSetting($clientId, $userId, UserSetting::PERFORMANCE_V_2_RULE_SORT);
        $settingSorts = $setting->getValue()[$targetType] ?? [];
        if (empty($settingSorts) || !is_array($settingSorts)) {
            return $rules;
        }
        $addRuleIds = array_diff(array_column($rules, 'rule_id'), $settingSorts);
        $setting = array_merge($addRuleIds, $settingSorts);
        $ruleMap = array_column($rules, null, 'rule_id');
        $newRules = [];
        foreach ($setting as $item) {
            if (isset($ruleMap[$item])) {
                $newRules[] = $ruleMap[$item];
            }
        }
        return $newRules;
    }

    /**
     * @throws \xiaoman\orm\exception\QueryException
     */
    public static function getTargetFieldList($clientId, $referType, $performanceType = \common\models\client\PerformanceV2Rule::PERFORMANCE_TYPE_NULL)
    {

        $omsFlag = \common\library\privilege_v3\Helper::checkHasOmsPrivilege($clientId);

        $extendField = [];
        // 这里具体的某些模块的业绩字段是有扩展字段
        switch ($referType) {
            case \Constants::TYPE_OPPORTUNITY:
            case \Constants::TYPE_QUOTATION:
            case \Constants::TYPE_CASH_COLLECTION:
            case \Constants::TYPE_COMPANY:
            case \Constants::TYPE_LEAD:
            case \Constants::TYPE_PRODUCT:
                $extendField = self::getExtendField($clientId, $referType);
                break;
            case \Constants::TYPE_ORDER:
                $extendField = self::getExtendField($clientId, $referType);
                $costItemInvoiceRelationFilter = new CostItemInvoiceRelationFilter($clientId);
                $costItemInvoiceRelationFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
                $costItemInvoiceRelationFilter->type = \Constants::TYPE_ORDER;
                $batchCostItemInvoiceRelation = $costItemInvoiceRelationFilter->find();
                $batchCostItemInvoiceRelation->getFormatter()->listSetting();
                $costItemInvoiceRelationList = $batchCostItemInvoiceRelation->getListAttributes();
                if (empty($costItemInvoiceRelationList))break;

                foreach ($costItemInvoiceRelationList as $item) {
                    $costItem = [
                        'id' => 'cost_list.'.$item['relation_id'],
                        'field_type' => 0,
                        'calculate_rule' => PerformanceV2Constant::CALCULATE_RULE_SUM,
                        'name' => $item['item_name'] ?? $item['description'],
                        'currency' => '',
                        'department_agg_type' => PerformanceV2Constant::DEPARTMENT_AGG_TYPE_USER,
                        'ext_info' => [],
                        'unit' => 'main_currency'
                    ];
                    $extendField[] = $costItem;
                }

                break;
            case \Constants::TYPE_EDM:
                $extendField = [];
                break;

            case \Constants::TYPE_WORK_JOURNAL:
                $workJournalTemplateList = new WorkJournalTemplateList($clientId);
                $workJournalTemplateList->setFields(['template_id', 'name', 'template_type', 'description', 'template_type']);
                $templateList = $workJournalTemplateList->find();
                foreach ($templateList as $item)
                {
                    $templateItem = [
                        'id' => $item['template_id'],
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'calculate_rule' => PerformanceV2Constant::CALCULATE_RULE_SUM,
                        'name' => $item['name'],
                        'currency' => '',
                        'department_agg_type' => PerformanceV2Constant::DEPARTMENT_AGG_TYPE_USER,
                        'ext_info' => [
                            'template_type' => $item['template_type'] ?? \common\models\client\WorkJournalTemplate::WORK_REPORT_TYPE_DAY
                        ],
                    ];
                    $extendField[] = $templateItem;
                }
                break;
            default:
                break;
        }

        $targetField = PerformanceV2Constant::TARGET_FIELD_MAP[$referType] ?? [];
        foreach ($targetField as $key => &$item) {

            // 如果有声明目标类型，那么需要进行过滤
            if (!empty($performanceType) && isset($item['performance_type']) && $performanceType != $item['performance_type']) {
                unset($targetField[$key]);
            }

            // 非oms账号不展示订单毛利
            if (!$omsFlag) {
                if ($referType == \Constants::TYPE_ORDER && in_array($item['id'],['real_profit','real_profit_rmb','real_profit_usd']))
                {
                    unset($targetField[$key]);
                    continue;
                }
            }

            $item['name'] = \Yii::t('performanceV2', $item['name']);
        }
        return array_merge($targetField ?? [], $extendField);
    }

    public static function getTimeFieldList($clientId, $referType)
    {
        $result = PerformanceV2Constant::TIME_FIELD_MAP[$referType] ?? [];
        foreach ($result as &$item) {
            $item['name'] = \Yii::t('performanceV2', $item['name']);
        }

        if (in_array($referType, [Constants::TYPE_OPPORTUNITY, Constants::TYPE_ORDER, Constants::TYPE_QUOTATION, Constants::TYPE_CASH_COLLECTION, Constants::TYPE_COMPANY, Constants::TYPE_LEAD])) {
            $groupFieldMap = [
                Constants::TYPE_OPPORTUNITY => CustomFieldService::OPPORTUNITY_GROUP_BASIC,
                Constants::TYPE_ORDER => CustomFieldService::ORDER_GROUP_BASIC,
                Constants::TYPE_QUOTATION => CustomFieldService::QUOTATION_GROUP_BASIC,
                Constants::TYPE_CASH_COLLECTION => CustomFieldService::CASH_COLLECTION_GROUP_BASIC,
                // 客户、线索需要拿所有类型的日期字段
                Constants::TYPE_COMPANY => CustomFieldService::COMPANY_GROUP_ALL,
                Constants::TYPE_LEAD => CustomFieldService::LEAD_GROUP_ALL,
            ];
            $customFieldList = new FieldList($clientId);
            $customFieldList->setType($referType);
            $customFieldList->setDisableFlag(0);
            $customFieldList->setBase(0);
            $customFieldList->setFields(['id', 'field_type', 'name', 'ext_info']);
            $customFieldList->setFieldType([CustomFieldService::FIELD_TYPE_DATETIME, CustomFieldService::FIELD_TYPE_DATE]);
            $groupFieldMap[$referType] && $customFieldList->setGroupId($groupFieldMap[$referType]);
            $fieldList = $customFieldList->find();
            !empty($fieldList) && $result = array_merge($result, $fieldList);
        }
        return $result;
    }

    /**
     * 检查是否需要根据owner_id字段来做去重
     * time: 4:06 PM
     * user: huagongzi
     */
    public static function checkNeedDistinctWithOwnerId(array $ruleInfo){
        if (empty($ruleInfo)) {
            return false;
        }
        $referType         = $ruleInfo['refer_type'];
        $timeField         = $ruleInfo['time_field'];
        $calculateRule     = $ruleInfo['calculate_rule'];
        $performanceField  = $ruleInfo['performance_field'];

        // 不需要做去重的对象
        $needNotDistinctReferTypes = PerformanceV2Constant::NEED_NOT_DISTINCT_REFER_TYPES;
        $isDynamicTime             = \common\library\performance_v2\rule\PerformanceV2Rule::checkIsDynamicTimeByReferTypeAndTimeField($referType, $timeField);
        $need                      = false;

        switch ($calculateRule){
            // 其它类型 count
            case 'count':
                $need = false;
                break;

            // 金额 sum
            case 'sum':
                if(!$isDynamicTime){
                    // 判断考核项是否有业绩归属人，归属部门,(只有订单和回款单是有业绩归属人，归属部门比例；商机有归属部门，但是是100%)
                    if($performanceField == 'users'){
                        if(in_array($referType, $needNotDistinctReferTypes)){
                            $need = true;
                        }
                    } else {
                        $need = false;
                    }
                } else {
                    $need = false;
                }
                break;

            // 数值/公式(sum)
            default:
                if($isDynamicTime){
                    if(in_array($referType, $needNotDistinctReferTypes)){
                        $need = true;
                    }
                }
                break;
        }

        return $need;
    }

    public static function getPerformanceFieldList($referType)
    {
        $result = PerformanceV2Constant::PERFORMANCE_FIELD_MAP[$referType] ?? [];
        foreach ($result as &$item) {
            $item['name'] = \Yii::t('performanceV2', $item['name']);
        }
        return $result;
    }

    public static function getExtendField($clientId, $referType)
    {
        $filterFunctionField = false;
        $fieldQuery = new \common\library\custom_field\FieldList($clientId);
        // 目前是只有订单支持公式、汇总字段作为考核指标，
        // 后续商机想要接入需要进行评估
        switch ($referType) {
            case \Constants::TYPE_ORDER :
                $fieldQuery->setFieldType([
                    CustomFieldService::FIELD_TYPE_NUMBER,
                    CustomFieldService::FIELD_TYPE_FORMULA,
                    CustomFieldService::FIELD_TYPE_CALCULATE,
                ]);
                $filterFunctionField = true;
                break;
            default:
                $fieldQuery->setFieldType(CustomFieldService::FIELD_TYPE_NUMBER);
                break;
        }

        $fieldQuery->setType($referType);
        $fieldQuery->setIsList(0);
        $fieldQuery->setFields(['id', 'field_type', 'name', 'ext_info']);

        $fieldQuery->setDisableFlag(0);
        $fieldQuery->setBase(0);
        $fieldQuery->setEnableFlag(1);
        $fieldListData = $fieldQuery->find();

        //todo 未测试，先注释掉
//        if ($filterFunctionField) {
//            //公式字段存存在跨对象的因子是无法触发绩效计算的，因此需要屏蔽
//            $api = new \common\library\object\field\Api();
//            $objectName = \common\library\object\object_define\Constant::OBJ_MAP[$referType] ?? null;
//            $crossObjectFields = $api->getCrossObjectFields($clientId, $objectName, array_column($fieldListData, 'id'));
//            foreach ($fieldListData as $index => $fieldInfo) {
//                if (in_array($fieldInfo['id'], $crossObjectFields)) {
//                    unset($fieldListData[$index]);
//                }
//            }
//        }


        $fieldListData = array_map(function ($item) use ($referType) {
            $item['calculate_rule'] = 'sum';
            $item['currency'] = '';
            $item['department_agg_type'] = $referType == \Constants::TYPE_QUOTATION
                ? PerformanceV2Constant::DEPARTMENT_AGG_TYPE_USER
                : PerformanceV2Constant::DEPARTMENT_AGG_TYPE_DEPARTMENT;
            return $item;
        }, $fieldListData);
        return $fieldListData;
    }

    /**
     * 获取计算公式 仅在绩效模块使用
     * @param $clientId
     * @param $referType
     * @throws \xiaoman\orm\exception\QueryException
     */
    public static function getCalculateFormula($clientId, $referType, $performanceType = \common\models\client\PerformanceV2Rule::PERFORMANCE_TYPE_NULL)
    {
        $extInfo = [];
        $res = [];

        //参考文档：https://xmkm.yuque.com/armee3/me2z04/bdemqr#GVTx
        $supportFormulaReferConfig = [
            \common\models\client\PerformanceV2Rule::PERFORMANCE_TYPE_NULL => [
                \Constants::TYPE_OPPORTUNITY,
                \Constants::TYPE_ORDER,
                \Constants::TYPE_EDM,
                \Constants::TYPE_CASH_COLLECTION,
            ],
            \common\models\client\PerformanceV2Rule::PERFORMANCE_TYPE_RESULT => [
                \Constants::TYPE_OPPORTUNITY,
                \Constants::TYPE_ORDER,
                \Constants::TYPE_EDM,
                \Constants::TYPE_CASH_COLLECTION,
            ],
            \common\models\client\PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS => [
                \Constants::TYPE_OPPORTUNITY,
                \Constants::TYPE_ORDER,
                \Constants::TYPE_EDM,
                \Constants::TYPE_CASH_COLLECTION,
            ]
        ];

        if (in_array($referType, $supportFormulaReferConfig[$performanceType])) {

            $refers = self::getFormulaReferTypeByReferType($referType);
            $refers = array_merge([$referType],$refers);
            foreach ($refers as $refer) {

                $fieldQuery = new \common\library\custom_field\FieldList($clientId);
                $fieldQuery->setType($refer);
                $fieldQuery->setIsList(false);
                $fieldQuery->setFields(['id', 'name', 'ext_info', 'type', 'field_type']);
                $fieldQuery->setDisableFlag(false);
                $fieldQuery->setBase(0);
                $fieldQuery->setEnableFlag(true);
                // 有些字段需要支持自定义数值字段和自定义公式字段
                switch ($refer) {
                    case \Constants::TYPE_OPPORTUNITY:
                        $fieldQuery->setFieldType([
                            CustomFieldService::FIELD_TYPE_NUMBER,
                            CustomFieldService::FIELD_TYPE_FORMULA,
                        ]);
                        break;
                    case \Constants::TYPE_ORDER:
                        $fieldQuery->setFieldType([
                            CustomFieldService::FIELD_TYPE_NUMBER,
                            CustomFieldService::FIELD_TYPE_FORMULA,
                        ]);

                        $costItemInvoiceRelationFilter = new CostItemInvoiceRelationFilter($clientId);
                        $costItemInvoiceRelationFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
                        $costItemInvoiceRelationFilter->type = \Constants::TYPE_ORDER;
                        $batchCostItemInvoiceRelation = $costItemInvoiceRelationFilter->find();
                        $batchCostItemInvoiceRelation->getFormatter()->listSetting();
                        $costItemInvoiceRelationList = $batchCostItemInvoiceRelation->getListAttributes();

                        foreach ($costItemInvoiceRelationList as $item) {
                            $costItem = [
                                'id' => 'cost_list.' . $item['relation_id'],
                                'name' => $item['item_name'] ?? $item['description'],
                                'ext_info' => [],
                                'type' => \Constants::TYPE_ORDER,
                                'unit' => 'main_currency'
                            ];
                            $extInfo[] = $costItem;
                        }

                        break;
                    case \Constants::TYPE_CASH_COLLECTION:
                        $fieldQuery->setFieldType([
                            CustomFieldService::FIELD_TYPE_NUMBER,
                            CustomFieldService::FIELD_TYPE_FORMULA,
                            CustomFieldService::FIELD_TYPE_CALCULATE
                        ]);
                        break;
                    default:
                        break;
                }

                $fieldListInfo = $fieldQuery->find();
                foreach (PerformanceV2Constant::CALCULATE_FORMULA_MAP[$refer] ?? [] as $formula) {
                    // 如果有声明目标类型，那么需要进行过滤
                    if (!empty($performanceType) && isset($formula['performance_type']) && $performanceType != $formula['performance_type']) {
                        continue;
                    }
                    $extInfo[] = $formula;
                }
                $res = array_merge($res,array_merge($fieldListInfo, $extInfo));
                $extInfo = [];
            }
        }

        foreach ($res as $index => $item)
        {
            $res[$index]['name'] =  \Yii::t("performanceV2", $item['name']);
        }
        return $res;

    }

    /**
     * 仅在公式中使用 -获取绩效设置中的关联对象
     * @return array
     */
    public static function getFormulaReferTypeByReferType($referType)
    {
        $map = [
            \Constants::TYPE_ORDER => [
                \Constants::TYPE_OPPORTUNITY
            ],
            \Constants::TYPE_CASH_COLLECTION => [
                \Constants::TYPE_OPPORTUNITY,
                \Constants::TYPE_ORDER
            ]
        ];
        return $map[$referType] ?? [];
    }


    /**
     * @param User $user
     * @param $ruleId
     * @param $sortField
     * @param array $userIds
     * @param $startDate
     * @param $endDate
     * @return array
     */
    public static function getTeamWallPerformanceRanking(
        User $user,
             $ruleId,
             $sortField,
        array $userIds,
        $startDate,
        $endDate,
        $timeType = PerformanceV2Constant::TIME_TYPE_THIS_MONTH
    )
    {
        $currency = self::getTargetField($user->getClientId(), $ruleId)['currency'] ?? '';
        $ruleInfo = new PerformanceV2Rule($user->getClientId(),$ruleId);
        $clientId = $user->getClientId();
        $userId   = $user->getUserId();

        // 获取对应用户map
        $userList = \common\library\account\Helper::getBatchUserInfo($user->getClientId(), $userIds);
        $userList = array_map(function ($elem) {
            return ['user_id' => $elem->user_id, 'nickname' => $elem->nickname, 'avatar' => $elem->avatar];
        }, $userList);
        $userMap = array_column($userList, null, 'user_id');

        // 规则为过程目标
        if($ruleInfo->performance_type == \PerformanceV2Goals::TARGET_PROCESS_GOAL){

            $ruleConfig = (new PerformanceV2GoalConfig($clientId, $ruleId))->loadFirstItemByRuleId($ruleId);

            // 没有配置默认取周的
            empty($ruleConfig->time_granularity) && $ruleConfig->time_granularity = \PerformanceV2GoalsConfig::TIME_GRANULARITY_WEEK;

            if(!isset(PerformanceV2Constant::TIMEGRANULARITY_TO_TIME_TYPE_MAP[$ruleConfig->time_granularity])){
                return [];
            }

            $performanceV2ProcessService = new PerformanceV2ProcessService($clientId, $userId);
            $performanceV2ProcessService->setFilterDisplaySetting(false);
            $performanceV2ProcessService->setRuleIds([$ruleId]);
            $performanceV2ProcessService->setStartDate($startDate);
            $performanceV2ProcessService->setReferId($userIds);
            $performanceV2ProcessService->setEndDate($endDate);
            $performanceV2ProcessService->setScope(PerformanceV2Constant::SETTING_GOAL_SCOPE_USER);
            $performanceV2ProcessService->setShowGoals(true);
            $performanceV2ProcessService->setShowIndicatorValue(1);
            $performanceV2ProcessService->setUserField('user_id,client_id,email,nickname');
            $performanceV2ProcessService->setTimeType(PerformanceV2Constant::TIMEGRANULARITY_TO_TIME_TYPE_MAP[$ruleConfig->time_granularity]);
            $performanceV2ProcessService->setIndicatorField('client_id,rule_id,owner_id,record_type,sum(indicator_value) as indicator_value,account_date');
            $performanceV2ProcessService->setIndicatorGroupBy('client_id,rule_id,owner_id,record_type,account_date');
            $processGoalList    = $performanceV2ProcessService->getProcessRuleList();
            $processGoalList    = empty($processGoalList) ? [] : array_pop($processGoalList);
            $referYearMonthList = $processGoalList[$performanceV2ProcessService->getScopeName()] ?? $processGoalList;
            $result             = [];

            // 拼接用户信息
            foreach ($referYearMonthList as $year => $monthList){
                foreach ($monthList as $month => $referList){
                    foreach ($referList as $referId => $referInfo){

                        if ($ruleConfig->time_granularity == \PerformanceV2GoalsConfig::TIME_GRANULARITY_DAY)
                        {

                            foreach ($referInfo as $userId => $item)
                            {
                                // 周的目标值和日的目标值数据格式不一致 暂时强行兼容
                                $result[$userId] = [
                                    'user_info'       => $userMap[$userId] ?? null,
                                    'goal'            => isset($result[$userId]) ? ($result[$userId]['goal'] + $item['amount']) : $item['amount'],
                                    'indicator_value' => isset($result[$userId]) ? ($result[$userId]['indicator_value'] + $item['indicator_value']) : $item['indicator_value'],
                                    'currency'        => $currency,
                                ];
                            }
                        }else {
                            $result[$referId] = [
                                'user_info'       => $userMap[$referId] ?? null,
                                'goal'            => isset($result[$referId]) ? ($result[$referId]['goal'] + $referInfo['amount']) : $referInfo['amount'],
                                'indicator_value' => isset($result[$referId]) ? ($result[$referId]['indicator_value'] + $referInfo['indicator_value']) : $referInfo['indicator_value'],
                                'currency'        => $currency,
                            ];
                        }
                    }
                }
            }

            // 计算完成率
            $result = array_map(function ($item){
                $item['goal_percent']  =  $item['goal'] != 0 ? round( $item['indicator_value'] / $item['goal'], 4) : 0;
                return $item;
            }, $result);

            foreach ($userIds as $userId){
                if(isset($result[$userId])){
                    continue;
                }

                $result[$userId] = [
                    'user_info'       => $userMap[$userId] ?? null,
                    'goal'            => 0,
                    'indicator_value' => 0,
                    'currency'        => $currency,
                ];
            }

            if(empty($result)){
                return $result;
            }

            array_multisort(array_column($result, $sortField),SORT_DESC, $result);

            return array_values($result);
        }

        // 这里获取对应设置的业绩目标
        $startYear = date('Y', strtotime($startDate));
        $startMonth = date('m', strtotime($startDate));
        $endYear = date('Y', strtotime($endDate));
        $endMonth = date('m', strtotime($endDate));
        $goalList = new \common\library\performance_v2\goal\PerformanceV2GoalList($user->getClientId(), $user->getUserId(), $ruleId);
        $goalList->setStartYearMonth($startYear, $startMonth);
        $goalList->setEndYearMonth($endYear, $endMonth);
        $goalList->setReferIds($userIds,\common\library\performance_v2\PerformanceV2Constant::SETTING_GOAL_SCOPE_USER);
        $goalList->setSkipPrivilege(true);
        $goalMap = array_column($goalList->agg(), null, 'refer_id');

        // 这里是获取对应业绩
        $performanceList = new PerformanceV2RecordList($user->getClientId(), $user->getUserId());
        $performanceList->setRuleId($ruleId);
        $performanceList->setSkipOwnerCheck(true);
        $performanceList->setOwnerId($userIds);
        $performanceList->setStartAccountDate($startDate);
        $performanceList->setEndAccountDate($endDate);
        $performanceList->setRecordType(\common\library\performance_v2\PerformanceV2Constant::RECORD_TYPE_USER);
        $performanceMap = array_column($performanceList->processSumAgg(), null, 'owner_id');



        $result = [];
        foreach ($userIds as $userId) {
            if (! isset($userMap[$userId])) {
                continue;
            }
            $goal = $goalMap[$userId]['amount']  ?? 0;
            $indicatorValue = $performanceMap[$userId]['indicator_value'] ?? 0;
            $result[] = [
                'user_info' => $userMap[$userId] ?? null,
                'goal' => $goal,
                'indicator_value' => $indicatorValue,
                'goal_percent' => $indicatorValue !=0 && $goal != 0 ? round( $indicatorValue / $goal, 4) : 0,
                'currency' => $currency,
            ];
        }

        array_multisort(array_column($result, $sortField),SORT_DESC, $result);

        return $result;
    }


    public static function getNewTeamWallPerformanceRanking(
        User $user,
             $ruleId,
             $sortField,
        array $userIds,
        $startDate,
        $endDate,
        $timeType = PerformanceV2Constant::TIME_TYPE_THIS_MONTH
    )
    {
        $currency = self::getTargetField($user->getClientId(), $ruleId)['currency'] ?? '';
        $ruleInfo = new PerformanceV2Rule($user->getClientId(),$ruleId);
        $clientId = $user->getClientId();
        $userId   = $user->getUserId();

        // 获取对应用户map
        $userList = \common\library\account\Helper::getBatchUserInfo($user->getClientId(), $userIds);
        $userList = array_map(function ($elem) {
            return ['user_id' => $elem->user_id, 'nickname' => $elem->nickname, 'avatar' => $elem->avatar];
        }, $userList);
        $userMap = array_column($userList, null, 'user_id');

        // 规则为过程目标
        if($ruleInfo->performance_type == \PerformanceV2Goals::TARGET_PROCESS_GOAL){

            $ruleConfig = (new PerformanceV2GoalConfig($clientId, $ruleId))->loadFirstItemByRuleId($ruleId);

            // 没有配置默认取周的
            empty($ruleConfig->time_granularity) && $ruleConfig->time_granularity = \PerformanceV2GoalsConfig::TIME_GRANULARITY_WEEK;

            if(!isset(PerformanceV2Constant::TIMEGRANULARITY_TO_TIME_TYPE_MAP[$ruleConfig->time_granularity])){
                return [];
            }

            $performanceV2ProcessService = new NewPerformanceV2ProcessService($clientId, $userId);
            $performanceV2ProcessService->setRuleIds([$ruleId]);
            $performanceV2ProcessService->setStartDate($startDate);
            $performanceV2ProcessService->setReferId($userIds);
            $performanceV2ProcessService->setEndDate($endDate);
            $performanceV2ProcessService->setScope(PerformanceV2Constant::SETTING_GOAL_SCOPE_USER);
            $processGoalList = $performanceV2ProcessService->getNewProcessRuleTeamWall();
            $result = [];

            foreach ($processGoalList as $userId => $item) {
                $result[$userId] = [
                    'user_info'       => $userMap[$userId] ?? null,
                    'goal'            => $item['amount'],
                    'indicator_value' => $item['indicator_value'],
                    'currency'        => $currency,
                ];
            }

            // 计算完成率
            $result = array_map(function ($item){
                $item['goal_percent']  =  $item['goal'] != 0 ? round( $item['indicator_value'] / $item['goal'], 4) : 0;
                return $item;
            }, $result);

            foreach ($userIds as $userId){
                if(isset($result[$userId])){
                    continue;
                }

                $result[$userId] = [
                    'user_info'       => $userMap[$userId] ?? null,
                    'goal'            => 0,
                    'indicator_value' => 0,
                    'currency'        => $currency,
                ];
            }

            if(empty($result)){
                return $result;
            }

            array_multisort(array_column($result, $sortField),SORT_DESC, $result);

            return array_values($result);
        }

        // 这里获取对应设置的业绩目标
        $startYear = date('Y', strtotime($startDate));
        $startMonth = date('m', strtotime($startDate));
        $endYear = date('Y', strtotime($endDate));
        $endMonth = date('m', strtotime($endDate));
        $goalList = new \common\library\performance_v2\goal\PerformanceV2GoalList($user->getClientId(), $user->getUserId(), $ruleId);
        $goalList->setStartYearMonth($startYear, $startMonth);
        $goalList->setEndYearMonth($endYear, $endMonth);
        $goalList->setReferIds($userIds,\common\library\performance_v2\PerformanceV2Constant::SETTING_GOAL_SCOPE_USER);
        $goalList->setSkipPrivilege(true);
        $goalMap = array_column($goalList->agg(), null, 'refer_id');

        // 这里是获取对应业绩
        $performanceList = new PerformanceV2RecordList($user->getClientId(), $user->getUserId());
        $performanceList->setRuleId($ruleId);
        $performanceList->setSkipOwnerCheck(true);
        $performanceList->setOwnerId($userIds);
        $performanceList->setStartAccountDate($startDate);
        $performanceList->setEndAccountDate($endDate);
        $performanceList->setRecordType(\common\library\performance_v2\PerformanceV2Constant::RECORD_TYPE_USER);
        $performanceMap = array_column($performanceList->processSumAgg(), null, 'owner_id');



        $result = [];
        foreach ($userIds as $userId) {
            if (! isset($userMap[$userId])) {
                continue;
            }
            $goal = $goalMap[$userId]['amount']  ?? 0;
            $indicatorValue = $performanceMap[$userId]['indicator_value'] ?? 0;
            $result[] = [
                'user_info' => $userMap[$userId] ?? null,
                'goal' => $goal,
                'indicator_value' => $indicatorValue,
                'goal_percent' => $indicatorValue !=0 && $goal != 0 ? round( $indicatorValue / $goal, 4) : 0,
                'currency' => $currency,
            ];
        }

        array_multisort(array_column($result, $sortField),SORT_DESC, $result);

        return $result;
    }

    public static function getTeamWallPerformanceOverviewCard(
        User $user,
             $ruleId,
             $ownerId,
             $startDate,
             $endDate,
             $recordType
    )
    {
        $currency = self::getTargetField($user->getClientId(), $ruleId)['currency'] ?? '';
        $ruleInfo = new PerformanceV2Rule($user->getClientId(),$ruleId);
        $clientId = $user->getClientId();
        $userId   = $user->getUserId();

        // 规则为过程目标
        if($ruleInfo->performance_type == \PerformanceV2Goals::TARGET_PROCESS_GOAL){
            $referIds   = [$ownerId];
            $ruleConfig = (new PerformanceV2GoalConfig($clientId, $ruleId))->loadFirstItemByRuleId($ruleId);

            // 没有配置默认取周的
            empty($ruleConfig->time_granularity) && $ruleConfig->time_granularity = \PerformanceV2GoalsConfig::TIME_GRANULARITY_WEEK;

            if(!isset(PerformanceV2Constant::TIMEGRANULARITY_TO_TIME_TYPE_MAP[$ruleConfig->time_granularity])){
                return [];
            }

            $performanceV2ProcessService = new PerformanceV2ProcessService($clientId, $userId);
            $performanceV2ProcessService->setFilterDisplaySetting(false);
            $performanceV2ProcessService->setRuleIds([$ruleId]);
            $performanceV2ProcessService->setStartDate($startDate);
            $performanceV2ProcessService->setReferId($referIds);
            $performanceV2ProcessService->setEndDate($endDate);
            $performanceV2ProcessService->setScope($recordType);
            $performanceV2ProcessService->setShowGoals(true);
            $performanceV2ProcessService->setShowIndicatorValue(1);
            $performanceV2ProcessService->setUserField('user_id,client_id,email,nickname');
            $performanceV2ProcessService->setTimeType(PerformanceV2Constant::TIMEGRANULARITY_TO_TIME_TYPE_MAP[$ruleConfig->time_granularity]);
            $performanceV2ProcessService->setIndicatorField('client_id,rule_id,owner_id,record_type,sum(indicator_value) as indicator_value,account_date');
            $performanceV2ProcessService->setIndicatorGroupBy('client_id,rule_id,owner_id,record_type,account_date');
            $processGoalList    = $performanceV2ProcessService->getProcessRuleList();
            $processGoalList    = empty($processGoalList) ? [] : array_pop($processGoalList);
            $result             = [];

            if(empty($processGoalList)){
                return $result;
            }

            $departmentService = new DepartmentService($user->getClientId());
            $result['department_info'] = $departmentService->batchGetDepartmentListForIds([$ownerId])[$ownerId] ?? [];
            $result['goal']            = $processGoalList['amount'] ?? 0;
            $result['indicator_value'] = round($processGoalList['indicator_value'], 2);
            $result['goal_percent']    = $result['goal'] != 0 ? round( $result['indicator_value'] / $result['goal'], 4) : 0;
            $result['currency']        = $currency;

            return $result;
        }

        // 这里获取对应设置的业绩目标
        $startYear = date('Y', strtotime($startDate));
        $startMonth = date('m', strtotime($startDate));
        $endYear = date('Y', strtotime($endDate));
        $endMonth = date('m', strtotime($endDate));
        $goalList = new \common\library\performance_v2\goal\PerformanceV2GoalList($user->getClientId(), $user->getUserId(), $ruleId);
        $goalList->setStartYearMonth($startYear, $startMonth);
        $goalList->setEndYearMonth($endYear, $endMonth);
        $goalList->setReferIds($ownerId, $recordType);
        $goalList->setSkipPrivilege(true);
        $goal = $goalList->agg()[0] ?? [];

        // 这里是获取对应业绩
        $performanceList = new PerformanceV2RecordList($user->getClientId(), $user->getUserId());
        $performanceList->setRuleId($ruleId);
        $performanceList->setSkipOwnerCheck(true);
        if ($recordType == PerformanceV2Constant::RECORD_TYPE_DEPARTMENT) {
            $targetField = self::getTargetField($user->getClientId(), $ruleId);
            if (($targetField['department_agg_type'] ?? '') == PerformanceV2Constant::DEPARTMENT_AGG_TYPE_USER ) {
                if ($ownerId != 0) {
                    $department = new \common\library\department\Department($user->getClientId(), $ownerId);
                    $departmentUserIds = $department->getMemberList(true);
                    $performanceList->setOwnerId($departmentUserIds);
                }
                $performanceList->setRecordType(PerformanceV2Constant::RECORD_TYPE_USER);
            } else {
                $departments = \common\library\department\Helper::getChildrenIds($user->getClientId(), $ownerId);
                $departments[] = $ownerId;
                $performanceList->setOwnerId($departments);
                $performanceList->setRecordType($recordType);
            }
        } else {
            $performanceList->setOwnerId($ownerId);
        }
        $performanceList->setStartAccountDate($startDate);
        $performanceList->setEndAccountDate($endDate);
        $noOwnerIdDistinct = 'DISTINCT ON( client_id, rule_id, refer_id ) client_id,refer_id,owner_id,rule_id,refer_type,record_type,indicator_value,account_date,create_time,update_time,company_id,record_id ';
        $ownerIdDistinct = 'DISTINCT ON( client_id, rule_id, refer_id, owner_id ) client_id,refer_id,owner_id,rule_id,refer_type,record_type,indicator_value,account_date,create_time,update_time,company_id,record_id ';
        if ($ruleInfo->calculate_rule == PerformanceV2Constant::CALCULATE_RULE_COUNT) {
            // 统计类型count的只显示一个就行，去重统计
            $performanceList->setFields($noOwnerIdDistinct);
        } else {
            // 统计类型sum的因为是百分比平分到不同的owner_id，需要全部
            // 但是sum类型有特殊情况：https://xmkm.yuque.com/armee3/me2z04/aqqtbtcce7fktvrh
            // 针对销售订单 考核人若是创建人create_user、当前处理人handler则不对owner_id去重，其余则不， 回款单 考核人是create_user_id创建人不对owner_id去重
            if (in_array($ruleInfo->refer_type, [\Constants::TYPE_ORDER, \Constants::TYPE_CASH_COLLECTION])
                && in_array($ruleInfo->performance_field, ['create_user', 'handler', 'create_user_id'])) {
                $performanceList->setFields($noOwnerIdDistinct);
            } else {
                $performanceList->setFields($ownerIdDistinct);
            }
        }
        $indicatorValue = array_sum(array_column($performanceList->distinctRecordList(), 'indicator_value'));

        $departmentService = new DepartmentService($user->getClientId());
        $result['department_info'] = $departmentService->batchGetDepartmentListForIds([$ownerId])[$ownerId] ?? [];
        $result['goal'] = $goal['amount'] ?? 0;
        $result['indicator_value'] = $indicatorValue;
        $result['goal_percent'] = $result['indicator_value'] && $result['goal'] != 0 ? round( $result['indicator_value'] / $result['goal'], 4) : 0;
        $result['currency'] = $currency;
        return $result;
    }

    public static function getNewTeamWallPerformanceOverviewCard(
        User $user,
             $ruleId,
             $ownerId,
             $startDate,
             $endDate,
             $recordType
    )
    {
        $currency = self::getTargetField($user->getClientId(), $ruleId)['currency'] ?? '';
        $ruleInfo = new PerformanceV2Rule($user->getClientId(),$ruleId);
        $clientId = $user->getClientId();
        $userId   = $user->getUserId();

        // 规则为过程目标
        if($ruleInfo->performance_type == \PerformanceV2Goals::TARGET_PROCESS_GOAL){
            $referIds   = [$ownerId];
            $ruleConfig = (new PerformanceV2GoalConfig($clientId, $ruleId))->loadFirstItemByRuleId($ruleId);

            // 没有配置默认取周的
            empty($ruleConfig->time_granularity) && $ruleConfig->time_granularity = \PerformanceV2GoalsConfig::TIME_GRANULARITY_WEEK;

            if(!isset(PerformanceV2Constant::TIMEGRANULARITY_TO_TIME_TYPE_MAP[$ruleConfig->time_granularity])){
                return [];
            }

            $performanceV2ProcessService = new NewPerformanceV2ProcessService($clientId, $userId);
            $performanceV2ProcessService->setRuleIds([$ruleId]);
            $performanceV2ProcessService->setStartDate($startDate);
            $performanceV2ProcessService->setReferId($referIds);
            $performanceV2ProcessService->setEndDate($endDate);
            $performanceV2ProcessService->setScope($recordType);
            $processGoalList = $performanceV2ProcessService->getNewProcessRuleTeamWall();
            $processGoalList = empty($processGoalList) ? [] : array_pop($processGoalList);
            $result          = [];

            if(empty($processGoalList)){
                return $result;
            }

            $departmentService = new DepartmentService($user->getClientId());
            $result['department_info'] = ($departmentService->batchGetDepartmentListForIds([$ownerId]))[$ownerId] ?? [];
            $result['goal']            = $processGoalList['amount'] ?? 0;
            $result['indicator_value'] = round($processGoalList['indicator_value'], 2);
            $result['goal_percent']    = $result['goal'] != 0 ? round( $result['indicator_value'] / $result['goal'], 4) : 0;
            $result['currency']        = $currency;

            return $result;
        }

        // 这里获取对应设置的业绩目标
        $startYear = date('Y', strtotime($startDate));
        $startMonth = date('m', strtotime($startDate));
        $endYear = date('Y', strtotime($endDate));
        $endMonth = date('m', strtotime($endDate));
        $goalList = new \common\library\performance_v2\goal\PerformanceV2GoalList($user->getClientId(), $user->getUserId(), $ruleId);
        $goalList->setStartYearMonth($startYear, $startMonth);
        $goalList->setEndYearMonth($endYear, $endMonth);
        $goalList->setReferIds($ownerId, $recordType);
        $goalList->setSkipPrivilege(true);
        $goal = $goalList->agg()[0] ?? [];

        // 这里是获取对应业绩
        $performanceList = new PerformanceV2RecordList($user->getClientId(), $user->getUserId());
        $performanceList->setRuleId($ruleId);
        $performanceList->setSkipOwnerCheck(true);
        if ($recordType == PerformanceV2Constant::RECORD_TYPE_DEPARTMENT) {
            $targetField = self::getTargetField($user->getClientId(), $ruleId);
            if (($targetField['department_agg_type'] ?? '') == PerformanceV2Constant::DEPARTMENT_AGG_TYPE_USER ) {
                if ($ownerId != 0) {
                    $department = new \common\library\department\Department($user->getClientId(), $ownerId);
                    $departmentUserIds = $department->getMemberList(true);
                    $performanceList->setOwnerId($departmentUserIds);
                }
                $performanceList->setRecordType(PerformanceV2Constant::RECORD_TYPE_USER);
            } else {
                $departments = \common\library\department\Helper::getChildrenIds($user->getClientId(), $ownerId);
                $departments[] = $ownerId;
                $performanceList->setOwnerId($departments);
                $performanceList->setRecordType($recordType);
            }
        } else {
            $performanceList->setOwnerId($ownerId);
        }
        $performanceList->setStartAccountDate($startDate);
        $performanceList->setEndAccountDate($endDate);
        $noOwnerIdDistinct = 'DISTINCT ON( client_id, rule_id, refer_id ) client_id,refer_id,owner_id,rule_id,refer_type,record_type,indicator_value,account_date,create_time,update_time,company_id,record_id ';
        $ownerIdDistinct = 'DISTINCT ON( client_id, rule_id, refer_id, owner_id ) client_id,refer_id,owner_id,rule_id,refer_type,record_type,indicator_value,account_date,create_time,update_time,company_id,record_id ';
        if ($ruleInfo->calculate_rule == PerformanceV2Constant::CALCULATE_RULE_COUNT) {
            // 统计类型count的只显示一个就行，去重统计
            $performanceList->setFields($noOwnerIdDistinct);
        } else {
            // 统计类型sum的因为是百分比平分到不同的owner_id，需要全部
            // 但是sum类型有特殊情况：https://xmkm.yuque.com/armee3/me2z04/aqqtbtcce7fktvrh
            // 针对销售订单 考核人若是创建人create_user、当前处理人handler则不对owner_id去重，其余则不， 回款单 考核人是create_user_id创建人不对owner_id去重
            if (in_array($ruleInfo->refer_type, [\Constants::TYPE_ORDER, \Constants::TYPE_CASH_COLLECTION])
                && in_array($ruleInfo->performance_field, ['create_user', 'handler', 'create_user_id'])) {
                $performanceList->setFields($noOwnerIdDistinct);
            } else {
                $performanceList->setFields($ownerIdDistinct);
            }
        }
        $indicatorValue = array_sum(array_column($performanceList->distinctRecordList(), 'indicator_value'));

        $departmentService = new DepartmentService($user->getClientId());
        $result['department_info'] = ($departmentService->batchGetDepartmentListForIds([$ownerId]))[$ownerId] ?? [];
        $result['goal'] = $goal['amount'] ?? 0;
        $result['indicator_value'] = $indicatorValue;
        $result['goal_percent'] = $result['indicator_value'] && $result['goal'] != 0 ? round( $result['indicator_value'] / $result['goal'], 4) : 0;
        $result['currency'] = $currency;
        return $result;
    }

    public static function initTeamWallSetting($clientId, $userId)
    {
        $client = new \common\library\account\Client($clientId);
        $clientSetting = $client->getSettingAttributes([\common\library\account\Client::SETTING_KEY_PDCA_TEAM_WALL_SETTING]);
        $oldSetting = $clientSetting[\common\library\account\Client::SETTING_KEY_PDCA_TEAM_WALL_SETTING] ?? [];
        $ruleIds = array_column($oldSetting, 'rule_id');
        $ruleList = new PerformanceV2RuleList($clientId, $userId);
        $ruleList->setRuleId(array_filter($ruleIds));
        $ruleMap = array_column($ruleList->find(), null, 'rule_id');
        $newSetting = [];
        foreach ($oldSetting as $item) {
            switch ($item['type']) {
                case PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_PERFORMANCE_RANKING:
                case PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_PERFORMANCE_OVERVIEW_CARD:
                    if(! isset($ruleMap[$item['rule_id']]) || $ruleMap[$item['rule_id']]['enable_flag'] == PerformanceV2Constant::ENABLE_FLAG_FALSE){
                        break;
                    }
                    $newSetting[] = $item;
                    break;
                default:
                    $newSetting[] = $item;
                    break;
            }
        }
        $client = \common\library\account\Client::getClient($clientId);
        $client->setSettingAttributes([\common\library\account\Client::SETTING_KEY_PDCA_TEAM_WALL_SETTING => $newSetting]);
        $client->saveSettingAttributes();
    }

    public static function teamWallSettingReset($clientId)
    {
        $client = \common\library\account\Client::getClient($clientId);
        $client->setSettingAttributes([
            \common\library\account\Client::SETTING_KEY_PDCA_TEAM_WALL_SETTING => [
                [
                    "type" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_TIME_WIDGET,
                    "name" => \Yii::t("performanceV2", "倒计时"),
                    "id" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_TIME_WIDGET . "-*************-1",
                    "count_down_type" => 1,
                    "enable_flag" => 1,
                    "layout" => [
                        "x" => 0,
                        "y" => 0,
                        "w" => 1,
                        "h" => 1,
                        "i" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_TIME_WIDGET . "-*************-1"
                    ]
                ],
                [
                    "type" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_KEY_BEHAVIOR_PK,
                    "name" => \Yii::t("performanceV2", "员工PK榜"),
                    "fields" => [
                        KeyBehaviorList::FIELD_COMPANY_ADD_COUNT,
                        KeyBehaviorList::FIELD_FOLLOW_COMPANY_COUNT,
                        KeyBehaviorList::FIELD_OPPORTUNITY_ADD_COUNT,
                        KeyBehaviorList::FIELD_ORDER_ADD_COUNT
                    ],
                    "id" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_KEY_BEHAVIOR_PK . "-*************-2",
                    "user_ids" => [],
                    "enable_flag" => 1,
                    "layout" => [
                        "x" => 0,
                        "y" => 0,
                        "w" => 3,
                        "h" => 2,
                        "i" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_KEY_BEHAVIOR_PK . "-*************-2"
                    ]
                ]
            ]
        ]);
        $client->saveSettingAttributes();
    }

    public static function getReferPerformanceRates($referType, $referInfo, $performanceField, $userToDepartmentListMap = [], $extraData = [])
    {
        $result = [
            'users' => [],
            'departments' => [],
        ];
        $ownerUserIds = [];
        switch ($referType)
        {
            case \Constants::TYPE_SALE_OUTBOUND_INVOICE:
            case \Constants::TYPE_ORDER:
            case \Constants::TYPE_CASH_COLLECTION:
                if ($performanceField == 'users')
                {
                    $userRates = is_array($referInfo['users']) ? $referInfo['users'] : json_decode($referInfo['users'], true);
                    $result['users'] = array_column($userRates, 'rate', 'user_id');
                    $departmentRates = is_array($referInfo['departments']) ? $referInfo['departments'] : json_decode($referInfo['departments'], true);
                    $result['departments'] = array_column($departmentRates, 'rate', 'department_id');

                    return $result;
                } elseif ($performanceField == 'handler')
                {
                    if (!is_array($referInfo['handler'])) {
                        $referInfo['handler'] = PgsqlUtil::trimArray($referInfo['handler'] ?? '{}');
                    }
                    foreach ($referInfo['handler'] as $handler) {
                        // 处理人不按照比例拆分
                        $ownerUserIds[]  = $handler;
                    }
                } else {
                    $ownerUserIds[] =  $referInfo[$performanceField];
                }
                break;
            case \Constants::TYPE_OPPORTUNITY:
                $result['users'] = [$referInfo[$performanceField] => 100];
                $result['departments'] = [$referInfo['department'] => 100];
                return $result;
                break;
            case \Constants::TYPE_FOLLOWUP:
            case \Constants::TYPE_QUOTATION:
            case \Constants::TYPE_WORK_JOURNAL:
                $ownerUserIds = [$referInfo[$performanceField]];
                break;
            case \Constants::TYPE_PRODUCT:
            case \Constants::TYPE_COMPANY:
                // 跟进人
                if ($performanceField == 'user_id'){
                    if (!is_array($referInfo['user_id'])) {
                        $referInfo['user_id'] = PgsqlUtil::trimArray($referInfo['user_id'] ?? '{}');
                    }
                    $ownerUserIds = $referInfo['user_id'];
                } else if ($performanceField == 'last_owner')
                {
                    $lastOwnerMap = $extraData['lastOwnerMap'] ?? [];
                    $referCompanyId = $referInfo['company_id'] ?? 0;
                    if (isset($lastOwnerMap[$referCompanyId])) {
                        $lastOwnerIds = $lastOwnerMap[$referInfo['company_id']]['last_owner'] ?? [];
                        $lastOwnerIds = is_array($lastOwnerIds) ? $lastOwnerIds : [$lastOwnerIds];
                        if (empty($lastOwnerIds)) {
                            $lastOwnerIds = [$referInfo[$performanceField]];
                        }
                        $ownerUserIds = $lastOwnerIds;
                    } else {
                        $ownerUserIds = [$referInfo[$performanceField]];
                    }
                } else {
                    $ownerUserIds = [$referInfo[$performanceField]];
                }
                break;
            case \Constants::TYPE_LEAD:
                if ($performanceField == 'user_id'){
                    if (!is_array($referInfo['user_id'])) {
                        $referInfo['user_id'] = PgsqlUtil::trimArray($referInfo['user_id'] ?? '{}');
                    }
                    $ownerUserIds = $referInfo['user_id'];
                }else{
                    $ownerUserIds = [$referInfo[$performanceField]];
                }
                break;
            case \Constants::TYPE_MAIL:
            case \Constants::TYPE_EDM:
                $ownerUserIds = [$referInfo['user_id']];
                break;
            default:
                break;
        }

        $ownerUserIds = array_values(array_filter($ownerUserIds));
        if (empty($ownerUserIds)) {
            return $result;
        }

        // 获取对应user下部门的绩效
        $allOwnerDepartmentIds = [];
        foreach ($ownerUserIds as $userId) {
            $result['users'][$userId] = 100;
            $departmentIds = $userToDepartmentListMap[$userId] ?? [];
            if (empty($departmentIds)) continue;

            $allOwnerDepartmentIds = array_merge($allOwnerDepartmentIds, $departmentIds);
        }
        $uniqueDepartmentIds = array_values(array_unique($allOwnerDepartmentIds));
        $result['departments'] = array_fill_keys($uniqueDepartmentIds, 100);

        return $result;
    }

    // 批量从redis缓存获取 user => departmentList 的map
    public static function batchGetUserToDepartmentList(int $clientId, array $userIds)
    {
        $userToDepartmentListMap = [];
        $userIds = array_filter($userIds);
        if (empty($clientId) || empty($userIds)) {
            return $userToDepartmentListMap;
        }

        $departmentRedis = new \common\library\department\DepartmentRedis();
        $userDepartmentList = $departmentRedis->getBatchUserDepartment($clientId, $userIds, true);
        foreach ($userDepartmentList as $item)
        {
            $userToDepartmentListMap[$item['user_id']][] = $item['department_id'];
        }

        return $userToDepartmentListMap;
    }

    public static function isNeedRecordPerformance($referType, $referInfo, $ruleId = 0)
    {
        $ret = false;
        switch ($referType)
        {
            case \Constants::TYPE_ORDER:
            case \Constants::TYPE_QUOTATION:
                $ret =  ($referInfo['delete_flag'] == Order::DELETE_FLAG_FALSE);
                break;
            case \Constants::TYPE_PRODUCT:
                $ret = ($referInfo['enable_flag'] == Constants::ENABLE_FLAG_TRUE);
                break;
            case \Constants::TYPE_CASH_COLLECTION:
                $ret =  ($referInfo['enable_flag'] && (!empty($referInfo['opportunity_id']) || !empty($referInfo['order_id'])));
                break;
            case \Constants::TYPE_OPPORTUNITY:
                $ret = ($referInfo['enable_flag'] == Opportunity::ENABLE_FLAG_OK);
                break;
            case \Constants::TYPE_COMPANY:
                $ret = $referInfo['is_archive'];
                break;
            case \Constants::TYPE_LEAD:
                $ret = ($referInfo['is_archive'] == Lead::ARCHIVE_OK);
                break;
            case \Constants::TYPE_EDM:
                $ret = !$referInfo['draft_flag'];
                break;
            case \Constants::TYPE_MAIL:
                $ret = true;
                // 彻底删除不记录
                if ($referInfo['delete_flag'] == \Mail::DELETE_FLAG_DELETE) {
                    $ret = false;
                }
                if ($referInfo['mail_type'] == \Mail::MAIL_TYPE_UNKNOWN) {
                    $ret = false;
                }

                // 发送失败不计入
                if ($referInfo['mail_type'] == \Mail::MAIL_TYPE_SEND && $referInfo['send_status'] == \MAIL::SEND_STATUS_SEND_FAIL) {
                    $ret = false;
                }

                // 退信状态不记录
                if ($referInfo['bounce_flag'] == 1) {
                    $ret = false;
                }
                break;
            case \Constants::TYPE_FOLLOWUP:
                $ret = $referInfo['enable_flag'];
                break;
            case \Constants::TYPE_WORK_JOURNAL:
                $performanceListData = json_decode($referInfo['performance_list_data'] ?? '{}',true);
                $ret = in_array($ruleId,array_keys($performanceListData)) && $referInfo['enable_flag'];
                break;

            case \Constants::TYPE_SALE_OUTBOUND_INVOICE:
                $ret = true;
                break;

            default:
                break;
        }
        return $ret;
    }

    /**
     * @param $clientId
     * @param $ruleId
     * @return array
     */
    public static function getTargetField($clientId, $ruleId)
    {
        $rule = new PerformanceV2Rule($clientId, $ruleId);
        $targetFieldList = self::getTargetFieldList($clientId, $rule->refer_type);
        foreach ($targetFieldList as $item) {
            if ($item['id'] == $rule->target_field && $item['calculate_rule'] == $rule->calculate_rule) {
                return $item;
            }
        }
        return [];
    }

    //新用户初始化
    public static function initClientPrepareRuleByClient($clientId)
    {
        \LogUtil::info("initClientPrepareRuleByClient clientId:{$clientId} start");
        $mysqlDb = ProjectActiveRecord::getDbByClientId($clientId);
        ProjectActiveRecord::setConnection($mysqlDb);
        $db = PgActiveRecord::getDbByClientId($clientId);
        PgActiveRecord::setConnection($db);

        $mainCurrency = Client::getClient($clientId)->getMainCurrency();

        $sql = "select * from tbl_performance_v2_rule where client_id={$clientId} and type<>" . PerformanceV2Constant::RULE_TYPE_COMMON;
        $ruleMap = array_column($db->createCommand($sql)->queryAll(), null, 'type');

        //有预设规则无需要再给用户初始化
        if (!empty($ruleMap)) {
            return ;
        }

        $insertArr = [];
        // -------------- 设置成交订单金额
        //订单状态计入业绩标识之前已下线，新用户没有这个标识导致value为空
        //改为获取生效状态,需过滤掉名称为“已作废”、“售后”的订单状态  https://www.tapd.cn/21404721/prong/stories/view/1121404721001026457?url_cache_key=dff088bca8f7d3144d4d5f643bac227b&action_entry_type=stories
        $statusService = new InvoiceStatusService($clientId, \Constants::TYPE_ORDER);
        $orderEndStatus = $statusService->endStatus();
        $saveStatus = [];
        foreach ($orderEndStatus as $status)
        {
            if ($status['name'] === '已作废' || $status['name'] === '售后' || $status['name'] === '交易取消')
                continue;

            $saveStatus[] = $status['id'];
        }
        $filters = [[
            'value' => $saveStatus,
            'filter_no' => 1,
            'refer_type' => 2,
            'field' => 'status',
            'field_type' => 3,
            'operator' => 'in',
            'date_type' => '',
            'unit' => '',
        ]];
        $param = [
            $ruleMap[PerformanceV2Constant::RULE_TYPE_ORDER_AMOUNT]['rule_id'] ?? \ProjectActiveRecord::produceAutoIncrementId(),
            "'成交订单金额'",
            "'考核订单的订单金额，仅指定订单状态的订单会被计入绩效。'",
            $clientId,
            Constants::TYPE_ORDER,
            "'" . json_encode($filters) . "'",
            "'account_date'",
            $mainCurrency == 'CNY' ? "'amount_rmb'" : "'amount_usd'",
            "'sum'",
            "'users'",
            1,
            "'(1)'",
            PerformanceV2Constant::RULE_TYPE_ORDER_AMOUNT,
            PerformanceV2Constant::ENABLE_FLAG_TRUE,
            \common\models\client\PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
            "'{}'"
        ];
        $insertArr[] = "(" . implode(',', $param). ")";

        // ------------- 赢单商机金额
        $flowFilters = [];
        $flowFilters[] = [
            "value" => [2],
            "filter_no"=>1,
            "refer_type"=>"9",
            "field"=>"stage_type",
            "field_type" =>3,
            "operator" => "in",
            "date_type" => "",
            "unit" => ""
        ];
        $param = [
            $ruleMap[PerformanceV2Constant::RULE_TYPE_OPPORTUNITY_WIN]['rule_id'] ?? \ProjectActiveRecord::produceAutoIncrementId(),
            "'赢单商机金额'",
            "'考核赢单商机的销售金额'",
            $clientId,
            Constants::TYPE_OPPORTUNITY,
            "'" . json_encode($flowFilters) . "'",
            "'account_date'",
            $mainCurrency == 'CNY' ? "'amount_rmb'" : "'amount_usd'",
            "'sum'",
            "'main_user'",
            1,
            "'(1)'",
            PerformanceV2Constant::RULE_TYPE_OPPORTUNITY_WIN,
            PerformanceV2Constant::ENABLE_FLAG_TRUE,
            \common\models\client\PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
            "'{}'"
        ];
        $insertArr[] = "(" . implode(',', $param). ")";

        // -------------- 已回款金额
        $param = [
            $ruleMap[PerformanceV2Constant::RULE_TYPE_CASH_COLLECTION_AMOUNT]['rule_id'] ?? \ProjectActiveRecord::produceAutoIncrementId(),
            "'已回款金额'",
            "'考核回款单的本次回款金额'",
            $clientId,
            Constants::TYPE_CASH_COLLECTION,
            "'" . '[{"filter_no":1,"refer_type":"4","field":"serial_id","field_type":"1","operator":"not_null","date_type":"","unit":""},{"value":1,"filter_no":2,"refer_type":"10","field":"collect_status","field_type":"3","operator":"=","unit":""}]' . "'",
            "'collection_date'",
            $mainCurrency == 'CNY' ? "'amount_rmb'" : "'amount_usd'",
            "'sum'",
            "'users'",
            1,
            "'(1 AND 2)'",
            PerformanceV2Constant::RULE_TYPE_CASH_COLLECTION_AMOUNT,
            PerformanceV2Constant::ENABLE_FLAG_TRUE,
            \common\models\client\PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
            "'{}'"
        ];
        $insertArr[] = "(" . implode(',', $param). ")";

        // ------------------ 新建商机数
        $param = [
            $ruleMap[PerformanceV2Constant::RULE_TYPE_OPPORTUNITY_COUNT]['rule_id'] ?? \ProjectActiveRecord::produceAutoIncrementId(),
            "'新建商机数'",
            "'考核新建的商机数'",
            $clientId,
            Constants::TYPE_OPPORTUNITY,
            "'{}'",
            "'create_time'",
            "''",
            "'count'",
            "'create_user'",
            0,
            "''",
            PerformanceV2Constant::RULE_TYPE_OPPORTUNITY_COUNT,
            PerformanceV2Constant::ENABLE_FLAG_FALSE,
            \common\models\client\PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
            "'{}'"
        ];
        $insertArr[] = "(" . implode(',', $param). ")";

        // ------------------ 新建客户数
//        $param = [
//            $ruleMap[PerformanceV2Constant::RULE_TYPE_COMPANY_COUNT]['rule_id'] ?? \ProjectActiveRecord::produceAutoIncrementId(),
//            "'新建客户数'",
//            "'考核新建的客户数'",
//            $clientId,
//            Constants::TYPE_COMPANY,
//            "'{}'",
//            "'create_time'",
//            "''",
//            "'count'",
//            "'create_user'",
//            0,
//            "''",
//            PerformanceV2Constant::RULE_TYPE_COMPANY_COUNT,
//            PerformanceV2Constant::ENABLE_FLAG_FALSE
//        ];
//        $insertArr[] = "(" . implode(',', $param) . ")";

        // ------------------- 邮件营销发送数
        $param = [
            $ruleMap[PerformanceV2Constant::RULE_TYPE_EDM_COUNT]['rule_id'] ?? \ProjectActiveRecord::produceAutoIncrementId(),
            "'邮件营销发送数'",
            "'考核邮件营销模块的发送数'",
            $clientId,
            Constants::TYPE_EDM,
            "'{}'",
            "'create_time'",
            "'send_to_count'",
            "'sum'",
            "'user_id'",
            0,
            "''",
            PerformanceV2Constant::RULE_TYPE_EDM_COUNT,
            PerformanceV2Constant::ENABLE_FLAG_FALSE,
            \common\models\client\PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
            "'{}'"
        ];
        $insertArr[] = "(" . implode(',', $param) . ")";

        // --------发送邮件数
        $param = [
            $ruleMap[PerformanceV2Constant::RULE_TYPE_SEND_MAIL_COUNT]['rule_id'] ?? \ProjectActiveRecord::produceAutoIncrementId(),
            "'发送邮件数'",
            "'当前发送的总邮件数'",
            $clientId,
            \Constants::TYPE_MAIL,
            "'{}'",
            "'send_time'",
            "'send_mail_count'",
            "'count'",
            "'sender'",
            PerformanceV2Constant::CRITERIA_TYPE_NULL,
            "''",
            PerformanceV2Constant::RULE_TYPE_SEND_MAIL_COUNT,
            PerformanceV2Constant::ENABLE_FLAG_TRUE,
            \common\models\client\PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
            "'{}'"
        ];
        $insertArr[] = "(" . implode(',', $param) . ")";

        // --------添加跟进数
        $param = [
            $ruleMap[PerformanceV2Constant::RULE_TYPE_FOLLOW_UP_COUNT]['rule_id'] ?? \ProjectActiveRecord::produceAutoIncrementId(),
            "'新建跟进数'",
            "'当期新建的「线索、客户、商机」的跟进数'",
            $clientId,
            \Constants::TYPE_FOLLOWUP,
            "'{}'",
            "'create_time'",
            "''",
            "'count'",
            "'create_user'",
            PerformanceV2Constant::CRITERIA_TYPE_NULL,
            "''",
            PerformanceV2Constant::RULE_TYPE_FOLLOW_UP_COUNT,
            PerformanceV2Constant::ENABLE_FLAG_TRUE,
            \common\models\client\PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
            "'{}'"
        ];

        $insertArr[] = "(" . implode(',', $param) . ")";

        // --------新建客户数
        $param = [
            $ruleMap[PerformanceV2Constant::RULE_TYPE_COMPANY_COUNT]['rule_id'] ?? \ProjectActiveRecord::produceAutoIncrementId(),
            "'新建客户数'",
            "'当期新建的客户数'",
            $clientId,
            \Constants::TYPE_COMPANY,
            "'{}'",
            "'archive_time'",
            "''",
            "'count'",
            "'create_user'",
            PerformanceV2Constant::CRITERIA_TYPE_NULL,
            "''",
            PerformanceV2Constant::RULE_TYPE_COMPANY_COUNT,
            PerformanceV2Constant::ENABLE_FLAG_TRUE,
            \common\models\client\PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
            "'{}'",
        ];

        $insertArr[] = "(" . implode(',', $param) . ")";



        $sql = "insert into tbl_performance_v2_rule (rule_id, name, description, client_id, refer_type, filters, time_field, target_field, calculate_rule, performance_field, criteria_type, criteria, type, enable_flag,performance_type,formula) values " . implode(',', $insertArr) . " on conflict(rule_id) do update set name=EXCLUDED.name, description=EXCLUDED.description, refer_type=EXCLUDED.refer_type, filters=EXCLUDED.filters, time_field=EXCLUDED.time_field, target_field=EXCLUDED.target_field, calculate_rule=EXCLUDED.calculate_rule, performance_field=EXCLUDED.performance_field, criteria_type=EXCLUDED.criteria_type, criteria=EXCLUDED.criteria, type=EXCLUDED.type, enable_flag=EXCLUDED.enable_flag ";
        $db->createCommand($sql)->execute();
        // 初始化完业绩规则后需要对该client打开邮件发件的白名单
        self::setPerformanceSwitchCacheByReferType($clientId, \Constants::TYPE_MAIL, 1, \Mail::MAIL_TYPE_SEND);
        \LogUtil::info("initClientPrepareRuleByClient clientId:{$clientId} complete!");
    }


    //新用户初始化团队墙
    public static function initClientTeamWallSetting($clientId)
    {
        \LogUtil::info("initClientTeamWallSetting clientId:{$clientId} start");
        $privilegeService = PrivilegeService::getInstance($clientId);
        if (!$privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_PERFORMANCE_TEAM_WALL)) {
            return ;
        }
        //判断团队墙配置是否有数据,有数据则跳过
        $client = \common\library\account\Client::getClient($clientId);
        $clientSetting = $client->getSettingAttributes([\common\library\account\Client::SETTING_KEY_PDCA_TEAM_WALL_SETTING]);
        //有预设团队墙无需要再给用户初始化
        if (!empty($clientSetting[\common\library\account\Client::SETTING_KEY_PDCA_TEAM_WALL_SETTING])) {
            return ;
        }

        $db = PgActiveRecord::getDbByClientId($clientId);
        ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($clientId));
        $sql = "select * from tbl_performance_v2_rule where client_id={$clientId} and type<>" . PerformanceV2Constant::RULE_TYPE_COMMON;
        $ruleMap = array_column($db->createCommand($sql)->queryAll(), null, 'type');
        $setting = [
            [
                "type" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_TIME_WIDGET,
                "name" => \Yii::t("performanceV2", "倒计时"),
                "id" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_TIME_WIDGET . "-*************-1",
                "count_down_type" => 1,
                "enable_flag" => 1,
                "layout" => [
                    "x" => 0,
                    "y" => 0,
                    "w" => 1,
                    "h" => 1,
                    "i" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_TIME_WIDGET . "-*************-1"
                ]
            ],
            [
                "type" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_PERFORMANCE_OVERVIEW_CARD,
                "name" => \Yii::t("performanceV2", "成交订单金额"),
                "rule_id" => $ruleMap[PerformanceV2Constant::RULE_TYPE_ORDER_AMOUNT]['rule_id'],
                "value_type" => ['indicator_value', 'goal_percent'],
                "value_style" => 3,
                "id" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_PERFORMANCE_OVERVIEW_CARD . "-*************-2",
                "owner_id" => 0,
                "record_type" => 1,
                "enable_flag" => 1,
                "layout" => [
                    "x" => 1,
                    "y" => 0,
                    "w" => 1,
                    "h" => 1,
                    "i" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_PERFORMANCE_OVERVIEW_CARD . "-*************-2"
                ]
            ],
            [
                "type" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_PERFORMANCE_RANKING,
                "name" => \Yii::t("performanceV2", "成交订单金额排行榜"),
                "rule_id" => $ruleMap[PerformanceV2Constant::RULE_TYPE_ORDER_AMOUNT]['rule_id'],
                "value_type" => ['indicator_value'],
                "value_style" => 3,
                "sort_field" => "indicator_value",
                "sort_type" => "",
                "id" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_PERFORMANCE_RANKING . "-*************-3",
                "user_ids" => [],
                "enable_flag" => 1,
                "layout" => [
                    "x" => 2,
                    "y" => 0,
                    "w" => 1,
                    "h" => 2,
                    "i" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_PERFORMANCE_RANKING . "-*************-3"
                ]
            ],
            [
                "type" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_PERFORMANCE_RANKING,
                "name" => \Yii::t("performanceV2", "赢单商机金额排行榜"),
                "rule_id" => $ruleMap[PerformanceV2Constant::RULE_TYPE_OPPORTUNITY_WIN]['rule_id'],
                "value_type" => ['indicator_value'],
                "value_style" => 3,
                "sort_field" => "indicator_value",
                "sort_type" => "",
                "id" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_PERFORMANCE_RANKING . "-*************-4",
                "user_ids" => [],
                "enable_flag" => 1,
                "layout" => [
                    "x" => 0,
                    "y" => 1,
                    "w" => 1,
                    "h" => 2,
                    "i" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_PERFORMANCE_RANKING . "-*************-4"
                ]
            ],
            [
                "type" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_KEY_BEHAVIOR_RANKING,
                "name" => \Yii::t("performanceV2", "客户邮件发送数排行榜"),
                "field" => KeyBehaviorList::CUSTOMER_MAIL_SEND,
                "value_type" => ['indicator_value'],
                "value_style" => 1,
                "sort_field" => "indicator_value",
                "sort_type" => "",
                "id" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_KEY_BEHAVIOR_RANKING . "-*************-5",
                "department_id" => 0,
                "enable_flag" => 1,
                "layout" => [
                    "x" => 1,
                    "y" => 2,
                    "w" => 1,
                    "h" => 2,
                    "i" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_KEY_BEHAVIOR_RANKING . "-*************-5"
                ]
            ],
            [
                "type" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_KEY_BEHAVIOR_OVERVIEW_CARD,
                "name" => \Yii::t("performanceV2", "营销发件数"),
                "field" => KeyBehaviorList::EDM_SEND_COUNT,
                "value_type" => ['indicator_value'],
                "value_style" => 1,
                "id" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_KEY_BEHAVIOR_OVERVIEW_CARD . "-*************-6",
                "department_id" => 0,
                "enable_flag" => 1,
                "layout" => [
                    "x" => 2,
                    "y" => 2,
                    "w" => 1,
                    "h" => 1,
                    "i" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_KEY_BEHAVIOR_OVERVIEW_CARD . "-*************-6"
                ]
            ],
            [
                "type" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_KEY_BEHAVIOR_OVERVIEW_CARD,
                "name" => \Yii::t("performanceV2", "新建客户数"),
                "field" => KeyBehaviorList::FIELD_COMPANY_ADD_COUNT,
                "value_type" => ['indicator_value'],
                "value_style" => 1,
                "id" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_KEY_BEHAVIOR_OVERVIEW_CARD . "-*************-7",
                "department_id" => 0,
                "enable_flag" => 1,
                "layout" => [
                    "x" => 0,
                    "y" => 3,
                    "w" => 1,
                    "h" => 1,
                    "i" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_KEY_BEHAVIOR_OVERVIEW_CARD . "-*************-7"
                ]
            ],
            [
                "type" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_PERFORMANCE_OVERVIEW_CARD,
                "name" => \Yii::t("performanceV2", "赢单商机金额"),
                "rule_id" => $ruleMap[PerformanceV2Constant::RULE_TYPE_OPPORTUNITY_WIN]['rule_id'],
                "value_type" => ['indicator_value', 'goal_percent'],
                "value_style" => 3,
                "id" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_PERFORMANCE_OVERVIEW_CARD . "-*************-8",
                "owner_id" => 0,
                "record_type" => 1,
                "enable_flag" => 1,
                "layout" => [
                    "x" => 1,
                    "y" => 4,
                    "w" => 1,
                    "h" => 1,
                    "i" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_PERFORMANCE_OVERVIEW_CARD . "-*************-8"
                ]
            ],
            [
                "type" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_KEY_BEHAVIOR_OVERVIEW_CARD,
                "name" => \Yii::t("performanceV2", "新建订单数"),
                "field" => KeyBehaviorList::FIELD_ORDER_ADD_COUNT,
                "value_type" => ['indicator_value'],
                "value_style" => 1,
                "id" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_KEY_BEHAVIOR_OVERVIEW_CARD . "-*************-9",
                "department_id" => 0,
                "enable_flag" => 1,
                "layout" => [
                    "x" => 2,
                    "y" => 4,
                    "w" => 1,
                    "h" => 1,
                    "i" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_KEY_BEHAVIOR_OVERVIEW_CARD . "-*************-9"
                ]
            ],
            [
                "type" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_KEY_BEHAVIOR_PK,
                "name" => \Yii::t("performanceV2", "员工PK榜"),
                "fields" => [
                    KeyBehaviorList::FIELD_COMPANY_ADD_COUNT,
                    KeyBehaviorList::FIELD_FOLLOW_COMPANY_COUNT,
                    KeyBehaviorList::FIELD_OPPORTUNITY_ADD_COUNT,
                    KeyBehaviorList::FIELD_ORDER_ADD_COUNT,
                    KeyBehaviorList::CUSTOMER_MAIL_SEND,
                    KeyBehaviorList::EDM_SEND_COUNT,

                ],
                "id" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_KEY_BEHAVIOR_PK . "-*************-10",
                "user_ids" => [],
                "enable_flag" => 1,
                "layout" => [
                    "x" => 0,
                    "y" => 6,
                    "w" => 3,
                    "h" => 2,
                    "i" => PerformanceV2Constant::TEAM_WALL_SETTING_TYPE_KEY_BEHAVIOR_PK . "-*************-10"
                ]
            ]
        ];

        $client->setSettingAttributes([\common\library\account\Client::SETTING_KEY_PDCA_TEAM_WALL_SETTING => $setting]);
        $client->saveSettingAttributes();

        \LogUtil::info("initClientTeamWallSetting clientId:{$clientId} complete!");
    }

    //更新company_id的成交订单金额，平均值，成交订单金额最近时间
    public static function refreshCompanyFieldWithTransactionOrder($clientId, $ruleId, $companyId = [])
    {
        if (empty($clientId)  || empty($ruleId)) {
            return false;
        }

        if (!is_array($companyId)) {
            $companyId = array_filter([$companyId]);
        }

        $db = PgActiveRecord::getDbByClientId($clientId);
        $companyIds = [];

        $updateDealTimeFlag = false;
        if ( \CustomerOptionService::getDealSetting($clientId) == \Constants::TYPE_ORDER) {
            $updateDealTimeFlag = true;
        }

        // 参数是否指定公司，否则不做日志记录
        $paramHasCompany = true;
        //成交客户数
        if (empty($companyId)) {
            $paramHasCompany = false;
            //不指定company_id
            $recordType = \common\library\performance_v2\PerformanceV2Constant::RECORD_TYPE_USER;
            $dealCompanyCountTotalSql = "select company_id,count(distinct refer_id) as company_count, sum(indicator_value) as amount ,max(account_date) as max_account_date, coalesce(min(nullif(account_date, '1970-01-01')), '1970-01-01') as first_account_date from tbl_performance_v2_record WHERE client_id = {$clientId} and rule_id={$ruleId} and record_type ={$recordType}  group by company_id";
            $list = $db->createCommand($dealCompanyCountTotalSql)->queryAll();

            if (empty($list))
                return false;

            $companyIds = array_column($list,'company_id');
            if (empty($companyIds))
                return false;

        } else {
            //指定company_id
            $companyIdStr = implode(',',$companyId);
            $recordType = \common\library\performance_v2\PerformanceV2Constant::RECORD_TYPE_USER;
            $dealCompanyCountTotalSql = "select company_id, count(distinct refer_id) as company_count, sum(indicator_value) as amount ,max(account_date) as max_account_date, coalesce(min(nullif(account_date, '1970-01-01')), '1970-01-01') as first_account_date  from tbl_performance_v2_record WHERE client_id = {$clientId} and rule_id={$ruleId} and record_type ={$recordType} and company_id in ({$companyIdStr}) group by company_id";
            $list = $db->createCommand($dealCompanyCountTotalSql)->queryAll();
            $companyIds = $companyId;

            $filterCompanyIds = array_unique(array_column($list,'company_id'));
            foreach ($companyIds as $companyIdItem) {
                // 如果没有找到对应的company那么就给初始值 让其时间重置
                if (in_array($companyIdItem,$filterCompanyIds)) continue;

                $list[] = [
                    'company_id' => $companyIdItem
                ];
            }
        }

        $companySummaryList = [];
        foreach ($list as  $item) {

            $companyId = $item['company_id'] ?? 0;
            $amount = $item['amount'] ?? 0;
            $count = $item['company_count'] ?? 0;
            $latestTime = $item['max_account_date'] ?? '1970-01-01 00:00:00';
            $firstTime = $item['first_account_date'] ?? '1970-01-01 00:00:00';

            if (empty($item['company_id']))  {
                continue;
            }
            $companySummaryList[$companyId] = $amount;

            $set =[];

            $set[] = " transaction_order_amount = '{$amount}'";
            $set[] = " latest_transaction_order_time = '{$latestTime}'";
            $set[] = " transaction_order_first_time = '{$firstTime}'";
            $set[] = " performance_order_count = '{$count}'";

            if($updateDealTimeFlag) {
                $set[] = " deal_time = '{$latestTime}'";
            }

            if ($count > 0) {
                $avgAmount = $amount / $count;
                $set[] = " transaction_order_amount_avg = {$avgAmount}";
            } else {
                $set[] = " transaction_order_amount_avg = 0";
            }

            if (!empty($set))
            {
                $set = implode(',', $set);
                $updateSqlArray[] = "update tbl_company set $set where company_id=$companyId";
            }
        }

        if (empty($updateSqlArray)) {
            return false;
        }

        // 打此日志目的：1、是记录当时统计数据，作为参考；2、是为确认是否运行到这里。目前有些工单出现插入详情，但是汇总没有或者错误的情况
        $paramHasCompany && \LogUtil::info('refreshCompanyFieldWithTransactionOrder', ['map' => $companySummaryList]);
        $updateSqlArrayChunk = array_chunk($updateSqlArray,200);
        foreach ($updateSqlArrayChunk as $item) {
            $updateSql = implode('; ', $item);
            $res = $db->createCommand($updateSql)->execute();
        }

        //重新计算客群
        (new SwarmService($clientId))->refreshByRefer($companyIds, [
            'performance_order_count',
            'transaction_order_amount',
            'transaction_order_amount_avg',
            'latest_transaction_order_time',
            'transaction_order_first_time',
            'deal_time'
        ]);

        return true;
    }

    public static function reSummaryGoals($clientId, $userIds, $departmentId = 0)
    {
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();

        $ruleList = new \common\library\performance_v2\rule\PerformanceV2RuleList($clientId, $adminUserId);
        $ruleList->setFields('rule_id');
        $ruleList->setReferType(Constants::TYPE_ORDER);
        $ruleList->setName([\common\library\alibaba\sales_race\Helper::PERFORMANCE_NAME_ORDER_AMOUNT,
            \common\library\alibaba\sales_race\Helper::PERFORMANCE_NAME_ORDER_COUNT,
            \common\library\alibaba\sales_race\Helper::PERFORMANCE_NAME_ALI_ORDER_AMOUNT]);
        $ruleList = $ruleList->find();
        $ruleIds = array_column($ruleList, 'rule_id');

        //获取所有上级部门
        $departmentIds = [$departmentId];
        if ($departmentId) {
            $department = new Department($clientId, $departmentId);
            $departmentIds = array_filter(array_merge(explode('-',$department->prefix),$departmentIds));
        }

        //重新汇总目标
        $goals = new \common\library\performance_v2\goal\PerformanceV2Goal($clientId, $adminUserId);
        $goals->summaryCompanyGroupGoals($userIds, $ruleIds, $departmentIds);

    }

    public static function resetCompanyFieldWithTransactionOrder($clientId, $ruleId, $orderId = [])
    {
        if (empty($clientId)  || empty($ruleId)) {
            return false;
        }


        $db = PgActiveRecord::getDbByClientId($clientId);

        //成交客户数
        $recordType = \common\library\performance_v2\PerformanceV2Constant::RECORD_TYPE_USER;
        $dealCompanyCountTotalSql = "select array_to_string(array_agg(distinct refer_id),',') as order_ids,company_id from tbl_performance_v2_record WHERE client_id = {$clientId} and rule_id={$ruleId} and record_type ={$recordType} ";

        if ($orderId) {

            $dealCompanyCountTotalSql .= ' and refer_id in (' . implode(',', $orderId) . ') ';
        }

        $dealCompanyCountTotalSql .= '  group by company_id';

        $list = $db->createCommand($dealCompanyCountTotalSql)->queryAll();

        if (empty($list))
            return false;

        $orderIds = [];
        foreach ($list as  $item) {
            $orderIds = array_merge($orderIds,explode(',',$item['order_ids']));
        }

        //更新满足条件的订单的account_flag=0
        $orderIdStr = implode(',',$orderIds);
        $updateAccountFlagSql = "update tbl_order set account_flag=0 where client_id={$clientId} and order_id in ($orderIdStr)";
        $res = $db->createCommand($updateAccountFlagSql)->execute();

        $companyIds = array_column($list,'company_id');

        if (empty($companyIds))
            return false;

        $updateDealTimeFlag = false;
        if ( \CustomerOptionService::getDealSetting($clientId) == \Constants::TYPE_ORDER) {
            $updateDealTimeFlag = true;
        }

        $companyIdStr = implode(',',$companyIds);
        $set[] = " performance_order_count = 0";
        $set[] = " transaction_order_amount = 0";
        $set[] = " transaction_order_amount_avg = 0";
        $set[] = " latest_transaction_order_time = '1970-01-01 00:00:00'";
        $set[] = " transaction_order_first_time = '1970-01-01 00:00:00'";
        $set[] = " transaction_order_first_amount = 0";
        if($updateDealTimeFlag) {
            $set[] = " deal_time = '1970-01-01 00:00:00'";
        }

        $set = implode(',', $set);

        $updateSql = "update tbl_company set $set where client_id={$clientId} and  company_id in ({$companyIdStr})";
        $res = $db->createCommand($updateSql)->execute();

        //重新计算客群
        (new SwarmService($clientId))->refreshByRefer($companyIds, [
            'performance_order_count',
            'transaction_order_amount',
            'transaction_order_amount_avg',
            'latest_transaction_order_time',
            'transaction_order_first_time',
            'deal_time',
            'transaction_order_first_amount'
        ]);
    }

    //获取预设成交订单金额币种
    public static function getCurrencyFromDefaultOrderPerformanceRule($clientId)
    {
        $model = \common\models\client\PerformanceV2Rule::model()->findByAttributes([
            'client_id' => $clientId,
            'type' => PerformanceV2Constant::RULE_TYPE_ORDER_AMOUNT,
        ]);

        if (empty($model)) {
            return 'CNY';
        }


        $targetFiled = $model->target_field;

        if (in_array($targetFiled, [
            'amount_rmb',
            'product_total_amount_rmb'
        ])) {
            return "CNY";
        }

        if (in_array($targetFiled, [
            'amount_usd',
            'product_total_amount_usd'
        ])) {
            return "USD";
        }

        return "CNY";
    }

    //批量获取绩效规则
    public static function getPerFormanceTotalTop(
        int $clientId,
            $ruleIds,
            $userIds,
            $startDate,
            $endDate,
        int $top = 3
    ){
        if (empty($ruleIds) || empty($userIds) || empty($startDate) || empty($endDate)) {
            return [];
        }
        $sql = "select * from (
                    select client_id, rule_id, owner_id, indicator_value, row_number() over (partition by rule_id order by indicator_value desc) as row from
                        (SELECT client_id, rule_id, owner_id, sum(indicator_value) as indicator_value
                            FROM tbl_performance_v2_record
                            WHERE client_id = :client_id
                              AND record_type = :record_type
                              AND owner_id in (" . implode(',', $userIds).")
                              AND account_date >= :start_date
                              AND account_date <= :end_date
                              AND rule_id in (%s)
                            group by client_id, rule_id, owner_id
                        ) t1
                    ) t2 WHERE row <= :top";
        $sql = sprintf($sql, implode(',', $ruleIds));
        $pg = PgActiveRecord::getDbByClientId($clientId);
        $performanceData = $pg->createCommand($sql)->queryAll(true,[
            ':client_id' => $clientId,
            ':record_type' => \common\library\performance_v2\PerformanceV2Constant::RECORD_TYPE_USER,
            ':start_date' => $startDate,
            ':end_date' => $endDate,
            ':top' => $top
        ]);

        $data = [];
        foreach ($ruleIds as $ruleId) {
            foreach ($performanceData as $performanceDatum)
            {
                if ($performanceDatum['rule_id'] == $ruleId) {
                    $data[$ruleId][] = $performanceDatum;
                }
            }
        }
        return $data;
    }

    public static function getTeamWallCache($teamWallKey, $viewUserId, $params)
    {
        if (empty($teamWallKey) || empty($viewUserId) || empty($params)) {
            return [];
        }

        $redis = \RedisService::cache();
        $hashParams = md5(json_encode($params));

        $key = 'team_wall_cache' . ':' . $teamWallKey . ':' . $viewUserId . ":" . $hashParams;

//        echo $key.PHP_EOL;
//        echo "key存在时间：".$redis->ttl($key).PHP_EOL;

        $data = $redis->get($key);
        //序列化
        if (!empty($data)) {
            return json_decode($data, true);
        }

        return [];
    }


    /**
     * 删除团队墙缓存
     *
     * @param $teamWallKey
     * @param $viewUserId
     * @param $params
     * @return array|int
     */
    public static function delTeamWallCache($teamWallKey, $viewUserId, $params)
    {
        if (empty($teamWallKey) || empty($viewUserId) || empty($params)) {
            return [];
        }
        $redis = \RedisService::cache();
        $hashParams = md5(json_encode($params));
        \LogUtil::info('delTeamWallCache', [$params, $hashParams]);

        $key = 'team_wall_cache' . ':' . $teamWallKey . ':' . $viewUserId . ":" . $hashParams;
        return $redis->del([$key]);
    }


    public static function setTeamWallCache($teamWallKey, $viewUserId, $params, $data)
    {
        if (empty($teamWallKey) || empty($viewUserId) || empty($params) || empty($data)) {
            return false;
        }

        $redis = \RedisService::cache();
        $hashParams = md5(json_encode($params));

        $key = 'team_wall_cache' . ':' . $teamWallKey . ':' . $viewUserId . ":" . $hashParams;
        $value = json_encode($data);

        $redis->set($key, $value, "ex", 5*60);
//        echo $key.PHP_EOL;
//        echo "key存在时间：".$redis->ttl($key).PHP_EOL;
        return true;
    }

    public static function customerScoreUpdateRunRules($db, $clientId)
    {
        $uniqId = uniqid();
        try {
            $referType = \Constants::TYPE_COMPANY;
            $sql = <<<SQL
        select rule_id from tbl_performance_v2_rule where client_id = {$clientId}
        and refer_type= {$referType} and delete_flag = 0 and enable_flag = 1
        and filters::jsonb @> '[{"field":"score"}]'
SQL;
            $ruleIdList = $db->createCommand($sql)->queryColumn();
            if (!empty($ruleIdList)) {
                $ruleIdList = array_unique($ruleIdList);
            }
            \LogUtil::info("customerScoreUpdateRunRules {$clientId} ruleIdList:", ['uniqId' => $uniqId, 'ruleIdList' => $ruleIdList]);
            if (empty($ruleIdList)) {
                return;
            }
            // 有绩效规则，需要重新计算
            foreach ($ruleIdList as $ruleId) {
                [$exec, $output, $return] = \common\library\CommandRunner::run(
                    'PerformanceV2',
                    'recordPerformanceByRuleId',
                    [
                        'clientId' => $clientId,
                        'ruleId' => $ruleId,
                        'ruleInfo' => '',
                    ]
                );
                \LogUtil::info("customerScoreUpdateRunRules {$clientId} ", [
                    'uniqId' => $uniqId,
                    'rule_id' => $ruleId,
                    'exec' => $exec,
                    'messageId' =>  $output['messageId'],
                    'result' => ($return == 0) ? '入队成功' : '入队失败',
                    'log' => '[阿里日志搜索] * and controller: PerformanceV2 and action: recordPerformanceByRuleId'
                ]);
            }
        } catch (\Throwable $t) {
            \LogUtil::error('customerScoreUpdateRunRules_err', [
                'uniqId' => $uniqId,
                'client_id' => $clientId,
                'message' => $t->getMessage(),
                'line' => $t->getLine(),
            ]);
        }
    }

    /*
     * 更新订单的首单tbl_order->first_order_flag
     * 时机埋点：绩效编辑后重跑绩效规则 || 订单的afterSave
     */
    public static function refreshOrderFirstFlag(int $clientId, int $ruleId, array $companyIds = []) : int
    {

        if(empty($clientId) || empty($ruleId))
        {
            throw new \RuntimeException("Parameter error");
        }
        $pgDb = \PgActiveRecord::getDbByClientId($clientId);
        $performanceRule = \common\models\client\PerformanceV2Rule::model()->find('client_id=:client_id AND type=:type AND refer_type =:refer_type', [
            ':client_id' => $clientId,
            ':type' => \common\library\performance_v2\PerformanceV2Constant::RULE_TYPE_ORDER_AMOUNT,
            ':refer_type' => \Constants::TYPE_ORDER
        ]);
        if (empty($performanceRule) || $ruleId != $performanceRule->rule_id)
        {
            return 0;
        }
        $recordType = \common\library\performance_v2\PerformanceV2Constant::RECORD_TYPE_USER;
        $firstOrderWhere = "a.client_id = {$clientId} AND a.rule_id = {$performanceRule->rule_id} and record_type = {$recordType}";
        $cleanFirstOrderFlagWhere = "client_id={$clientId} AND enable_flag=1 AND first_order_flag=1";
        $adminUserId  = PrivilegeService::getInstance($clientId)->getAdminUserId();
        if (!empty($companyIds))
        {
            $companyIdsStr = implode(',', $companyIds);
            $cleanFirstOrderFlagWhere .= ' AND company_id IN (' . $companyIdsStr . ')';
            $firstOrderWhere .= ' AND a.company_id IN (' . $companyIdsStr . ')';
        }

        //清除首单记录
        $cleanFirstOrderFlagSql = "UPDATE tbl_order SET first_order_flag=0 WHERE {$cleanFirstOrderFlagWhere}";
        $affectRows = $pgDb->createCommand($cleanFirstOrderFlagSql)->execute();
        \LogUtil::info("refreshOrderFirstFlag cleanFirstOrderFlag--client_id={$clientId},affectRows={$affectRows},sql={$cleanFirstOrderFlagSql}");
        //存在绩效account_date同一天多个refer_id，二级排序使用订单日期
        $firstOrderSql = "SELECT refer_id, company_id, indicator_value as first_amount
                    FROM (SELECT a.refer_id,
                                 a.company_id,
                                 a.account_date,
                                 row_number() over (PARTITION BY a.company_id ORDER BY a.account_date, b.account_date) AS row,
                                 a.indicator_value
                          FROM tbl_performance_v2_record a LEFT JOIN tbl_order b ON a.refer_id = b.order_id
                          WHERE {$firstOrderWhere} )t1
                    WHERE row = 1";
        $result = $pgDb->createCommand($firstOrderSql)->queryAll();
        $firstOrderIds = array_column($result, 'refer_id');
        $countFirstOrderIds = count($firstOrderIds);
        \LogUtil::info("refreshOrderFirstFlag firstOrder--client_id={$clientId},affectRows={$countFirstOrderIds},sql={$firstOrderSql}");
        $orderBatchOperator = new OrderBatchOperator($adminUserId);
        //绩效重跑情况下 分割数据保证sql不会过长
        foreach (array_chunk($firstOrderIds,3000) as $orderIds)
        {
            $orderBatchOperator->setParams([
                'order_ids' => $orderIds,
            ]);
            $orderBatchOperator->changeFirstOrderFlag(true);
        }

        \common\library\customer\Helper::RefreshTransactionOrderFirstAmount($clientId, $result, empty($companyIds));


        return $countFirstOrderIds;
    }

    public static function getRuleList($clientId, $userId, $targetType, $ruleId, $field = "*")
    {
        $ruleList = new PerformanceV2RuleList($clientId, $userId);
        $ruleList->setFields($field);
        $ruleList->setEnableFlag(true);
        $ruleList->setPerformanceType($targetType);

        if ($ruleId) {
            $ruleList->setEnableFlag(null);
            $ruleList->setRuleId($ruleId);
        }

        return $ruleList->find();
    }


    /**
     * 根据时间粒度获取开始时间到结束时间的时间粒度列表
     * time: 3:39 PM
     * user: huagongzi
     * @param $startDate
     * @param $endDate
     * @param $timeType
     * @return array
     */
    public static function getYearTimeTypeRange($startDate, $endDate, $timeType){
        $result            = ["day" => [],"week" => [],"month" => [],"quarter" => [],"year" => [],"all" => [],"count" => 0];
        $tmpStartTimeStamp = strtotime($startDate);
        $tmpEndTimeStamp   = strtotime($endDate);
        $newWeekFlag       = true;
        $monthWeekIndex    = 1;  // 记录一个月中的第几周
        $currentQuarter    = "";

        while($tmpStartTimeStamp <= $tmpEndTimeStamp){
            $yearIndex    = date("Y", $tmpStartTimeStamp); // 年份
            $monthIndex   = date("n", $tmpStartTimeStamp); // 月份
            $quarterIndex = self::getQuator($monthIndex);         // 季度(1~4)
            $weekDayIndex = date("N", $tmpStartTimeStamp); // 周(1~7)
            $weekIndex    = self::weekOfMonth(date("Y-m-d", $tmpStartTimeStamp)); // 第几周(1~5)
            $dayIndex     = date("Y-n-d", $tmpStartTimeStamp); // 日(1~31)
            list($objYear, $yearWeekIndex) = self::weekIndexOfYear(date("Y-m-d", $tmpStartTimeStamp)); // 一年中的第几周

            $result["all"]["year_quarter_month_week_day"][$yearIndex][$quarterIndex][$monthIndex][$weekIndex][] = $dayIndex;
            $result["all"]["year_quarter_day"][$yearIndex][$quarterIndex][]                                     = $dayIndex;
            $result["all"]["year_quarter_week"][$yearIndex][$quarterIndex][$monthIndex . '_' . $weekIndex][]    = $dayIndex;
            $result["all"]["year_quarter_month"][$yearIndex][$quarterIndex][$monthIndex][]                      = $dayIndex;
            $result["all"]["year_month_week_day"][$yearIndex][$monthIndex][$weekIndex][]                        = $dayIndex;
            $result["all"]["year_month_day"][$yearIndex][$monthIndex][]                                         = $dayIndex;
            $result["all"]["year_week"][$objYear][$yearWeekIndex][]                                             = $dayIndex;
            $result["all"]["year_day"][$yearIndex][]                                                            = $dayIndex;

            switch ($timeType){
                // 天
                case PerformanceV2Constant::TIME_TYPE_THIS_DAY:
                case PerformanceV2Constant::TIME_TYPE_LAST_DAY:
                    $result["day"][$yearIndex][$monthIndex][] = $dayIndex;
                    $result["count"]++;
                    break;

                // 周
                case PerformanceV2Constant::TIME_TYPE_THIS_WEEK:
                case PerformanceV2Constant::TIME_TYPE_LAST_WEEK:
                    // 判断是否为月份中的第一周
                    if(!isset($result["week"][$yearIndex][$monthIndex])){
                        $monthWeekIndex = 1;
                    }

                    if($newWeekFlag && (!isset($result["week"][$yearIndex][$monthIndex]) || !in_array($monthWeekIndex, $result["week"][$yearIndex][$monthIndex]))){
                        $result["week"][$yearIndex][$monthIndex][] = $monthWeekIndex;
                        $result["count"]++;
                        $monthWeekIndex++;
                    }

                    // 判断是否为新周或跨月的第一周
                    if($weekDayIndex == 7 || !isset($result["week"][$yearIndex][$monthIndex])){
                        $newWeekFlag = true;
                    } else {
                        $newWeekFlag = false;
                    }

                    break;

                // 月
                case PerformanceV2Constant::TIME_TYPE_THIS_MONTH:
                case PerformanceV2Constant::TIME_TYPE_LAST_MONTH:
                case PerformanceV2Constant::TIME_TYPE_RANGE:
                    if(!isset($result["month"][$yearIndex]) || !in_array($monthIndex, $result["month"][$yearIndex])){
                        $result["month"][$yearIndex][] = $monthIndex;
                        $result["count"]++;
                    }

                    break;

                // 季度
                case PerformanceV2Constant::TIME_TYPE_THIS_QUARTER:
                case PerformanceV2Constant::TIME_TYPE_LAST_QUARTER:
                    $quarterIndex = self::getQuator($monthIndex);
                    $tmpQuarter   = "{$yearIndex}-{$quarterIndex}";

                    if(!isset($result["quarter"][$yearIndex]) || !isset($result["quarter"][$yearIndex][$quarterIndex]) || !in_array($monthIndex, $result["quarter"][$yearIndex][$quarterIndex])){
                        $result["quarter"][$yearIndex][$quarterIndex][] = $monthIndex;

                        if($currentQuarter != $tmpQuarter){
                            $result["count"]++;
                            $currentQuarter = $tmpQuarter;
                        }
                    }
                    break;

                // 年
                case PerformanceV2Constant::TIME_TYPE_THIS_YEAR:
                case PerformanceV2Constant::TIME_TYPE_LAST_YEAR:
                    if(!isset($result["year"][$yearIndex]) || !in_array($monthIndex,$result["year"][$yearIndex])){
                        $result["year"][$yearIndex][] = $monthIndex;
                        $result["count"]++;
                    }
                    break;
            }

            $tmpStartTimeStamp = strtotime("+1 days", $tmpStartTimeStamp);
        }

        // 处理跨年导致某一周多几天的问题,带week的都要处理，否则会导致后面计算天的数据有误差
        self::dealYearQuarterMonthWeekDay($result["all"]["year_quarter_month_week_day"]);
        self::dealYearQuarterWeek($result["all"]["year_quarter_week"]);
        self::dealYearMonthWeekDay($result["all"]["year_month_week_day"]);

        // 对周和季度的count数去重
        switch ($timeType){
            // 周 一个周跨多个月需要将重复计算的周去重,比如时间区间横跨n个月，需要去重的周数为(n-1),一个周跨年，除了将每年周跨月的周数去重和后还需要将跨年的周数去重，比如跨年数m，需要去重的周数为(m-1)
            case PerformanceV2Constant::TIME_TYPE_THIS_WEEK:
            case PerformanceV2Constant::TIME_TYPE_LAST_WEEK:
                $weekInfo  = $result["week"];
                $yearCount = count($weekInfo);

                foreach ($weekInfo as $yearKey => $monthValue){
                    $result["count"] -= count($monthValue) - 1;
                }

                $result["count"] -= $yearCount - 1;;

                break;

            // 季度
            case PerformanceV2Constant::TIME_TYPE_THIS_QUARTER:
            case PerformanceV2Constant::TIME_TYPE_LAST_QUARTER:
                $quarterInfo      = $result["quarter"];
                $yearCount        = count($quarterInfo);
                $result["count"] -= $yearCount - 1;;
                break;
        }

        return $result;
    }

    public static function dealYearQuarterMonthWeekDay(array &$yearQuarterMonthWeekDay){
        // 处理跨年导致某一周多几天的问题,带week的都要处理，否则会导致后面计算天的数据有误差
        foreach ($yearQuarterMonthWeekDay as $year => &$quarterList){
            foreach ($quarterList as $quarter => &$monthList){
                foreach ($monthList as $month => &$weekList){
                    foreach ($weekList as $week => &$dayList){
                        // 补充前一年的自然周
                        while(count($dayList) > 7){
                            $weekList[0][] = $dayList[0];
                            unset($dayList[0]);
                            $dayList = array_values($dayList);
                        }
                    }

                    ksort($weekList);
                }
            }

        }
    }

    public static function dealYearQuarterWeek(array &$yearQuarterWeek){
        foreach ($yearQuarterWeek as $year => &$quarterList){
            foreach ($quarterList as $quarter => &$weekList){
                foreach ($weekList as $monthWeek => &$dayList){
                    $month = (explode('_', $monthWeek))[0];
                    // 补充前一年的自然周
                    while(count($dayList) > 7){
                        $weekList["{$month}_0"][] = $dayList[0];
                        unset($dayList[0]);
                        $dayList = array_values($dayList);
                    }
                }

                ksort($weekList);
            }
        }
    }

    public static function dealYearMonthWeekDay(array &$yearMonthWeekDay){
        foreach ($yearMonthWeekDay as $year => &$monthList){
            foreach ($monthList as $month => &$weekList){
                foreach ($weekList as &$dayList){
                    // 补充前一年的自然周
                    while(count($dayList) > 7){
                        $weekList[0][] = $dayList[0];
                        unset($dayList[0]);
                        $dayList = array_values($dayList);
                    }
                }

                ksort($weekList);
            }
        }
    }

    public static function dealYearWeek(array &$yearWeek){
        foreach ($yearWeek as $year => &$monthList){
            foreach ($monthList as $monthWeek => &$dayList){
                $month = (explode('_', $monthWeek))[0];
                // 补充前一年的自然周
                while(count($dayList) > 7){
                    $weekList["{$month}_0"][] = $dayList[0];
                    unset($dayList[0]);
                    $dayList = array_values($dayList);
                }
            }
        }
    }

    /**
     * 根据指定时间和时间粒度获取环比的时间
     * time: 5:09 AM
     * user: huagongzi
     * @param $startDate
     * @param $timeType
     * @return false|int|mixed
     */
    public static function getRingRatioDate($startDate,$timeType){
        switch ($timeType) {
            // 天
            case PerformanceV2Constant::TIME_TYPE_THIS_DAY:
            case PerformanceV2Constant::TIME_TYPE_LAST_DAY:
                $startDate = date("Y-m-d", strtotime("-1 days", strtotime($startDate)));
                break;

            // 周
            case PerformanceV2Constant::TIME_TYPE_THIS_WEEK:
            case PerformanceV2Constant::TIME_TYPE_LAST_WEEK:
                $startDate = date("Y-m-d", strtotime("-7 days", strtotime($startDate)));
                break;

            // 月
            case PerformanceV2Constant::TIME_TYPE_THIS_MONTH:
            case PerformanceV2Constant::TIME_TYPE_LAST_MONTH:
            case PerformanceV2Constant::TIME_TYPE_RANGE:
                $startDate = date("Y-m-d", strtotime("-1 months", strtotime($startDate)));
                break;

            // 季度
            case PerformanceV2Constant::TIME_TYPE_THIS_QUARTER:
            case PerformanceV2Constant::TIME_TYPE_LAST_QUARTER:
                $startDate = date("Y-m-d", strtotime("-3 months", strtotime($startDate)));
                break;
        }

        return $startDate;
    }

    /**
     * 根据时间粒度获取结果目标各个refer的目标值
     * time: 4:06 PM
     * user: huagongzi
     * @param array $ruleList
     * @param int $clientId
     * @param int $userId
     * @param string $startDate
     * @param string $endDate
     * @param string $timeType
     * @param $referId
     * @param int $scope
     * @return array
     */
    public static function getResultTargetGoalByTimeType(int $clientId, int $userId, array $ruleList, string $startDate, string $endDate, string $timeType, $referId,int $scope){
        if (empty($referId)) {
            return [];
        }
        $ruleIds = array_column($ruleList, 'rule_id');

        // 根据时间粒度组装各个refer的目标值
        $yearTimeTypeRange = self::getYearTimeTypeRange($startDate,$endDate,$timeType);

        $startDay   = date("d", strtotime($startDate));
        $endDay     = date("d",strtotime($endDate));
        $startMonth = date("n", strtotime($startDate));
        $endMonth   = date("n", strtotime($endDate));

        // 根据时间粒度类型确认具体的开始月份和结束月份
        switch ($timeType){
            // 日
            case PerformanceV2Constant::TIME_TYPE_THIS_DAY:
            case PerformanceV2Constant::TIME_TYPE_LAST_DAY:
                $yearMonthWeekDayRange = $yearTimeTypeRange['day'];
                $startYear             = array_key_first($yearMonthWeekDayRange);
                $startMonth            = array_key_first($yearMonthWeekDayRange[$startYear]);
                $endYear               = array_key_last($yearMonthWeekDayRange);
                $endMonth              = array_key_last($yearMonthWeekDayRange[$endYear]);

                break;
            // 周
            case PerformanceV2Constant::TIME_TYPE_THIS_WEEK:
            case PerformanceV2Constant::TIME_TYPE_LAST_WEEK:
                $yearMonthWeekRange = $yearTimeTypeRange["week"];
                $startYear          = array_key_first($yearMonthWeekRange);
                $startMonth         = array_key_first($yearMonthWeekRange[$startYear]);
                $endYear            = array_key_last($yearMonthWeekRange);
                $endMonth           = array_key_last($yearMonthWeekRange[$endYear]);

                break;

            // 月和时间段
            case PerformanceV2Constant::TIME_TYPE_THIS_MONTH:
            case PerformanceV2Constant::TIME_TYPE_LAST_MONTH:
            case PerformanceV2Constant::TIME_TYPE_RANGE:
                $yearMonthRange = $yearTimeTypeRange["month"];
                $startYear      = array_key_first($yearMonthRange);
                $startMonth     = $yearMonthRange[$startYear][0];
                $endYear        = array_key_last($yearMonthRange);
                $endMonth       = $yearMonthRange[$endYear][count($yearMonthRange[$endYear]) - 1];
                break;

            // 季度
            case PerformanceV2Constant::TIME_TYPE_THIS_QUARTER:
            case PerformanceV2Constant::TIME_TYPE_LAST_QUARTER:
                $yearQuarterRange = $yearTimeTypeRange["quarter"];
                $startYear        = array_key_first($yearQuarterRange);
                $startQuarter     = array_key_first($yearQuarterRange[$startYear]);
                $startMonth       = $yearQuarterRange[$startYear][$startQuarter][0];
                $endYear          = array_key_last($yearQuarterRange);
                $endQuarter       = array_key_last($yearQuarterRange[$endYear]);
                $endMonth         = $yearQuarterRange[$endYear][$endQuarter][count($yearQuarterRange[$endYear][$endQuarter]) - 1];

                break;

            // 年
            case PerformanceV2Constant::TIME_TYPE_THIS_YEAR:
            case PerformanceV2Constant::TIME_TYPE_LAST_YEAR:
                $yearRange  = $yearTimeTypeRange["year"];
                $startYear  = array_key_first($yearRange);
                $endYear    = array_key_last($yearRange);
                break;
        }

        // 最终去db取目标值的时间限制条件
        $startDate = date("Y-m-d", strtotime("{$startYear}-{$startMonth}-{$startDay}"));
        $endDate   = date("Y-m-d", strtotime("{$endYear}-{$endMonth}-{$endDay}"));

        // 先根据referid获取开始时间和结束时间的目标列表
        $goalsList = new PerformanceV2GoalList($clientId, $userId);
        $goalsList->setRuleId($ruleIds);
        $goalsList->setStartYearMonth(date("Y", strtotime($startDate)), date("m",strtotime($startDate)));
        $goalsList->setEndYearMonth(date("Y", strtotime($endDate)),date("m",strtotime($endDate)));
        $goalsList->setSkipPrivilege(true);

        if($referId){
            $goalsList->setReferIds($referId, $scope);
        }

        $goals = $goalsList->agg();
        if (!empty($goals)) {
            foreach ($goals as &$goal) {
                $goal['amount'] = round($goal['amount'], 2);
            }
        }
        return $goals;
    }

    /**
     * 根据时间粒度获取过程目标各个refer的目标值
     * time: 11:17 AM
     * user: huagongzi
     * @param int $clientId
     * @param int $userId
     * @param array $ruleList
     * @param string $startDate
     * @param string $endDate
     * @param array $referIds
     * @return array
     */
    public static function getProcessGoalByTimeType(int $clientId, int $userId, array $ruleList, string $startDate, string $endDate, array $referIds){
        $ruleIds = array_column($ruleList, 'rule_id');
        $result  = [];

        $configList = new PerformanceV2GoalConfigList($clientId, $userId);
        $configList->setRuleIds($ruleIds);
        $configList->setFields('client_id,object_type,target_value,performance_rule_id,object_id,object_type,time_granularity');
        $configList->getFormatter()->setMergeList(true);

        $configs = $configList->find();

        // 根据每个规则下设置的用户数据设置目标值
        foreach ($configs as $ruleIdKey => $iterm){
            $userMaxValueMap = Helper::getUserGoalListByConfig($clientId, $iterm['iterms']);

            $tmpData = [
                "client_id"           => $iterm["client_id"],
                "performance_rule_id" => $iterm["performance_rule_id"],
                "time_granularity"    => $iterm["time_granularity"],
                "amount"              => 0,
            ];

            foreach ($userMaxValueMap as $userId => $itemInfo){

                if(empty($referIds) || (!in_array($userId, $referIds))){
                    continue;
                }

                $tmpData["scope"]    = PerformanceV2Constant::SETTING_GOAL_SCOPE_USER;
                $tmpData['refer_id'] = $userId;
                $timeGranularity     = $iterm['time_granularity'];
                $count = Helper::getRuleIntervalByRuleTimeType($startDate,$endDate, $timeGranularity);
                $tmpData['amount']  = round($itemInfo['amount'] * $count, 2);
                $result[] = $tmpData;
            }
        }

        return $result;
    }

    /**
     * 获取每个用户在每个规则下的指定时间段内，每个月的目标值
     * time: 6:56 PM
     * user: huagongzi
     * @param int $clientId
     * @param int $userId
     * @param array $ruleIds
     * @param string $startDate
     * @param string $endDate
     * @param int $referId
     * @return array
     */
    public static function getUserProcessMonthGoal(int $clientId, int $userId, array $ruleIds, string $startDate, string $endDate,int $referId = 0){
        $result  = [];

        $configList = new PerformanceV2GoalConfigList($clientId, $userId);
        $configList->setRuleIds($ruleIds);
        $configList->setObjectType(\PerformanceV2Goals::TARGET_PROCESS_GOAL);
        $configList->setFields('client_id,object_type,target_value,performance_rule_id,object_id,object_type,time_granularity');
        $configList->getFormatter()->setReferId($referId);
        $configList->getFormatter()->setMergeList(true);

        $configs = $configList->find();

        $startMonthTimeStamp = strtotime(date("Y-m", strtotime($startDate)));
        $endMonthTimeStamp   = strtotime(date("Y-m", strtotime($endDate)));

        // 根据每个规则下设置的用户数据设置目标值
        foreach ($configs as $iterm){
            $ruleIdKey       = $iterm['performance_rule_id'];
            $userMaxValueMap = Helper::getUserGoalListByConfig($clientId, $iterm['iterms']);

            foreach ($userMaxValueMap as $userId => $valueIterm){

                if($referId && ($referId != $userId)){
                    continue;
                }

                $iterm['refer_id'] = $userId;
                $timeGranularity   = $iterm['time_granularity'];

                for($i = $startMonthTimeStamp; $i <= $endMonthTimeStamp; $i = strtotime("+1 months", $i)){
                    $tmpYearMonth        = date("Y-m", $i);
                    $tmpLastDayTimeStamp = strtotime("+1 months -1 days", $i);
                    $tmpStartDate        = $tmpYearMonth;
                    $tmpEndDate          = date("Y-m-d", $tmpLastDayTimeStamp);

                    switch ($timeGranularity) {
                        // 天
                        case \PerformanceV2GoalsConfig::TIME_GRANULARITY_DAY:
                            $yearTimeTypeRange                        = Helper::getYearTimeTypeRange($tmpStartDate,$tmpEndDate, PerformanceV2Constant::TIME_TYPE_THIS_DAY);
                            $result[$ruleIdKey][$tmpYearMonth][$userId]["amount"] = $valueIterm['amount'] * $yearTimeTypeRange['count'];

                            break;

                        // 周
                        case \PerformanceV2GoalsConfig::TIME_GRANULARITY_WEEK:
                            $yearMonthWeekRange                       = Helper::getYearTimeTypeRange($tmpStartDate,$tmpEndDate, PerformanceV2Constant::TIME_TYPE_THIS_WEEK);
                            $result[$ruleIdKey][$tmpYearMonth][$userId]["amount"] = $valueIterm['amount'] * $yearMonthWeekRange['count'];
                            break;

                        // 月
                        case \PerformanceV2GoalsConfig::TIME_GRANULARITY_MONTH:
                            $yearMonthRange                           = Helper::getYearTimeTypeRange($tmpStartDate,$tmpEndDate, PerformanceV2Constant::TIME_TYPE_THIS_MONTH);
                            $result[$ruleIdKey][$tmpYearMonth][$userId]["amount"] = $valueIterm['amount'] * $yearMonthRange['count'];
                            break;
                    }
                }

                unset($iterm['iterms']);
            }
        }

        return $result;
    }

    public static function getRecordAggResult($ruleList,$clientId,$userId,$startDate,$endDate,$referId = [],$field = '')
    {
        $referId    = array_filter($referId);
        $ruleIds    = array_column($ruleList, 'rule_id');
        $recordList = [];

        if (empty($referId)) {
            return $recordList;
        }

        $performanceList = new PerformanceV2RecordList($clientId, $userId);
        $performanceList->setSkipOwnerCheck(true);
        $performanceList->setStartAccountDate($startDate);
        $performanceList->setEndAccountDate($endDate);

        if($referId){
            $performanceList->setOwnerId($referId);
        }

        list($needDistinctByReferIdRules, $needNotDistinctByReferIdRules) = self::pickOutNeedDistinctOrNotByReferIdRules($ruleList);

        // 根据client_id,rule_id,refer_id去重
        if($needDistinctByReferIdRules){
            $performanceList->setRuleId(array_column($needDistinctByReferIdRules, 'rule_id'));
            $performanceList->setFields($field ?: 'DISTINCT ON( client_id, rule_id, refer_id ) client_id,refer_id,owner_id,rule_id,refer_type,record_type,indicator_value,account_date,create_time,update_time,company_id,record_id ');
            $recordList = array_merge($recordList, $performanceList->distinctRecordList());
        }

        // 根据client_id,rule_id,refer_id,owner_id去重
        if($needNotDistinctByReferIdRules){
            $performanceList->setRuleId(array_column($needNotDistinctByReferIdRules, 'rule_id'));
            $performanceList->setFields($field ?: 'DISTINCT ON( client_id, rule_id, refer_id, owner_id ) client_id,refer_id,owner_id,rule_id,refer_type,record_type,indicator_value,account_date,create_time,update_time,company_id,record_id ');
            $recordList = array_merge($recordList, $performanceList->distinctRecordList());
        }

        return $recordList;
    }

    public static function getDepartmentList($clientId,$field = "*"){
        $departmentListObj = new \common\library\department\DepartmentList($clientId);
        $departmentListObj->setIncludeRoot(true);
        $departmentListObj->setFields($field);
        $departmentListObj->setDataType(\common\library\department\DepartmentList::DATA_TYPE_DEPARTMENT);
        $departmentListObj->setRecursive(true);
        $departmentListObj->getFormatter()->organizationTreeSetting();
        $departments = $departmentListObj->find();

        return $departments;
    }


    public static function getUserList($clientId,$field = "*"){
        $userList = new UserList();
        $userList->setClientId($clientId);
//        $userList->setEnableFlag(true);
        $userList->setFields($field);

        $users = $userList->find();

        return $users;
    }

    /**
     * 对固定目标配置挑选出最大值出来
     * time: 4:11 PM
     * user: huagongzi
     * @return void
     */
    public static function getUserGoalListByConfig(int $clientId, array $items){
        $userMaxValueMap = [];

        foreach ($items as $item){

            $item['object_id'] = is_array($item['object_id']) ? $item['object_id'] : json_decode($item['object_id'], true);
            $objectIds         = array_unique($item['object_id']);

            if(empty($objectIds)){
                continue;
            }

            // 写入默认字段
            $item['scope']  = \PerformanceV2GoalsConfig::SETTING_GOAL_SCOPE_USER;
            $item['amount'] = $item['target_value'];

            switch ($item['object_type']){
                // 部门
                case \PerformanceV2GoalsConfig::SETTING_GOAL_SCOPE_GROUP:

                    // 获取指定部门下及其子部门下的所有用户列表信息
//                    $usersInfo = \common\library\department\Helper::getUsersInfoByRecursionDepartmentTree($clientId, $objectIds);
//                    $userIds   = array_unique(array_keys($usersInfo));

                    $departmentUserIdMap = (new DepartmentRedis())->getDepartmentUserRecursiveMap($clientId, $objectIds);
                    $departmentUserIdMap = array_filter($departmentUserIdMap);
                    $userIds             = [];

                    array_walk($departmentUserIdMap, function ($tmpValue) use(&$userIds) {
                        $userIds = array_merge($userIds, $tmpValue);
                    });

                    foreach ($userIds as $userId){
                        if(isset($userMaxValueMap[$userId]) && ($item['target_value'] < $userMaxValueMap[$userId]["target_value"])){
                            continue;
                        }

                        $item['refer_id']         = $userId;
                        $userMaxValueMap[$userId] = $item;
                    }
                    break;

                // 用户
                case \PerformanceV2GoalsConfig::SETTING_GOAL_SCOPE_USER:

                    // 依次比较用户设定的值，和已暂存的目标值做大小比较，取两者最大值
                    foreach ($objectIds as $userId){
                        if(isset($userMaxValueMap[$userId]) && ($item['target_value'] < $userMaxValueMap[$userId]["target_value"])){
                            continue;
                        }

                        $item['refer_id']         = $userId;
                        $userMaxValueMap[$userId] = $item;
                    }
                    break;

                // 角色
                case \PerformanceV2GoalsConfig::SETTING_GOAL_SCOPE_ROLE:

                    // 依次遍历角色
                    $privilegeService = PrivilegeService::getInstance($clientId);

                    foreach ($objectIds as $roleId){
                        $userIds = $privilegeService->getUserIdsByRole($roleId);

                        foreach ($userIds as $userId){
                            if(isset($userMaxValueMap[$userId]) && ($item['target_value'] < $userMaxValueMap[$userId]["target_value"])){
                                continue;
                            }

                            $item['refer_id']         = $userId;
                            $userMaxValueMap[$userId] = $item;
                        }
                    }
                    break;
            }
        }

        return $userMaxValueMap;
    }

    /**
     * 获取过程目标目标值列表
     * time: 11:17 AM
     * user: huagongzi
     * @param $clientId 企业client_id
     * @param $userId 本次访问的账号user_id
     * @param $ruleIds 操作的目标规则rule_id
     * @param $startDate 用户选择开始时间 Y-m-d
     * @param $endDate 用户选择的结束时间 Y-m-d
     * @param $referId 过程目标若scope=1 则$referId是部门这里是部门ID，若scope=2则$referId是user_id
     * @param $scope 1 部门 2用户
     * @param $showRingRatio 是否展示环比 环比相对周期的上一个周期：周期是日时 2023-04-02 => 2023-04-01
     * @param $showConstrast 是否展示同比 同比相对周期的上一年的同周期：周期是日时 2023-04-02 => 2022-04-02
     * @param $showIndicatorValue 是否展示完成值
     * @return array|null
     */
    public static function getProcessGoalList($clientId,$userId,$ruleIds,$startDate,$endDate,$referId = 0,$scope = \PerformanceV2GoalsConfig::SETTING_GOAL_SCOPE_GROUP, $showRingRatio = 0, $showConstrast = 0 ,$showIndicatorValue = 1){
        $performanceV2ProcessService = new PerformanceV2ProcessService($clientId, $userId);
        $performanceV2ProcessService->setRuleIds($ruleIds);
        $performanceV2ProcessService->setStartDate($startDate);
        $performanceV2ProcessService->setEndDate($endDate);
        $performanceV2ProcessService->setScope($scope);
        $performanceV2ProcessService->setShowGoals(1);
        $performanceV2ProcessService->setShowConstrast($showConstrast);
        $performanceV2ProcessService->setShowRingRatio($showRingRatio);
        $performanceV2ProcessService->setShowIndicatorValue($showIndicatorValue);
        $performanceV2ProcessService->setUserField('user_id,client_id,email,nickname');

        if($referId){
            $performanceV2ProcessService->setReferId($referId);
        }

        return $performanceV2ProcessService->getProcessRuleList();
    }


    public static function getNewProcessGoalList($clientId,$userId,$ruleIds,$startDate,$endDate, $timeType, $referId = 0,$scope = \PerformanceV2GoalsConfig::SETTING_GOAL_SCOPE_GROUP, $showRingRatio = 0, $showConstrast = 0 ,$showIndicatorValue = 1){
        $performanceV2ProcessService = new NewPerformanceV2ProcessService($clientId, $userId);
        $performanceV2ProcessService->setRuleIds($ruleIds);
        $performanceV2ProcessService->setTimeType($timeType);
        $performanceV2ProcessService->setStartDate($startDate);
        $performanceV2ProcessService->setEndDate($endDate);
        $performanceV2ProcessService->setScope($scope);
        $performanceV2ProcessService->setShowGoals(1);
        $performanceV2ProcessService->setShowConstrast($showConstrast);
        $performanceV2ProcessService->setShowRingRatio($showRingRatio);
        $performanceV2ProcessService->setShowIndicatorValue($showIndicatorValue);
        $performanceV2ProcessService->setUserField('user_id,client_id,email,nickname');

        if($referId){
            $performanceV2ProcessService->setReferId($referId);
        }

        return $performanceV2ProcessService->getNewProcessRuleList();
    }

    /**
     * 获取完成情况的时间范围，跟规则设置的周期无关
     * 举例：$time_type=range 输入时间2023-05-15 2023-05-18 包含了4天，range超过一天的不显示环比、同比
     * 环比是2023-05-14 2023-05-17
     * 同步是2022-05-15 2022-05-18
     * $time_type = this_week/last_week 输入时间2023-05-15 2023-05-21应该算出是今年的 (是否要根据当前周数，取上一年的相同周数)
     * 环比是2023-05-08 2023-05-14
     * 同步是2022-05-15 2022-05-21
     * $time_type = this_month/last_month 输入时间2023-05-01 2023-05-31
     * 环比是2023-04-01 2023-04-30
     * 同步是2022-05-01 2022-05-31
     * $time_type = this_quarter/last_quarter 输入时间2023-04-01 2023-06-30
     * 环比是2023-01-01 2023-03-31
     * 同步是2022-04-01 2022-06-30
     * $time_type = this_year/last_year 输入时间2023-01-01 2023-12-31 需要结合财年设置 (这个时间环同比时间一致)
     * 环比是2022-01-01 2022-12-31
     * 同步是2022-01-01 2022-12-31
     * @param $startDate
     * @param $endDate
     * @param $time_type 'this_day' 'this_week' 'last_week'等
     * @param int $compareType 1 环比 / 2 同比
     * @return void
     */
    public static function getCompletionStartDateAndEndData($startDate, $endDate, $time_type, $compareType = PerformanceV2Constant::COMPARE_TYPE_CHAIN)
    {
        $startDateCarbon  = Carbon::parse($startDate);
        $endDateCarbon    = Carbon::parse($endDate);

        switch ($time_type) {
            case PerformanceV2Constant::TIME_TYPE_THIS_DAY:
            case PerformanceV2Constant::TIME_TYPE_LAST_DAY:
                // 环比
                if ($compareType == PerformanceV2Constant::COMPARE_TYPE_CHAIN) {
                    $compareStartDate = $startDateCarbon->subDays()->format("Y-m-d");
                    $compareEndDate   = $endDateCarbon->subDays()->format("Y-m-d");
                }
                // 同比
                if ($compareType == PerformanceV2Constant::COMPARE_TYPE_YEAR) {
                    $compareStartDate = $startDateCarbon->subYear()->format("Y-m-d");
                    $compareEndDate   = $endDateCarbon->subYear()->format("Y-m-d");
                }
                break;
            case PerformanceV2Constant::TIME_TYPE_THIS_WEEK:
            case PerformanceV2Constant::TIME_TYPE_LAST_WEEK:
                // 环比
                if ($compareType == PerformanceV2Constant::COMPARE_TYPE_CHAIN) {
                    $compareStartDate = $startDateCarbon->subWeek()->format("Y-m-d");
                    $compareEndDate = $endDateCarbon->subWeek()->format("Y-m-d");
                }
                // 同比 - 去年第x周
                if ($compareType == PerformanceV2Constant::COMPARE_TYPE_YEAR) {
                    $firstWeekOfLastYear = Carbon::parse(($startDateCarbon->weekYear-1) . '-01-01')->startOfWeek(Carbon::MONDAY);
                    $startOfWeek = $firstWeekOfLastYear->addWeeks($startDateCarbon->weekOfYear - 1);
                    $compareStartDate = $startOfWeek->format('Y-m-d');
                    $compareEndDate = $startOfWeek->addDays(6)->format('Y-m-d');
                }
                break;
            case PerformanceV2Constant::TIME_TYPE_THIS_MONTH:
            case PerformanceV2Constant::TIME_TYPE_LAST_MONTH:
                // 环比
                if ($compareType == PerformanceV2Constant::COMPARE_TYPE_CHAIN) {
                    $compareStartDate = $startDateCarbon->startOfMonth()->subMonth()->firstOfMonth()->format("Y-m-d");
                    $compareEndDate = $endDateCarbon->startOfMonth()->subMonth()->endOfMonth()->format("Y-m-d");
                }
                // 同比
                if ($compareType == PerformanceV2Constant::COMPARE_TYPE_YEAR) {
                    $compareStartDate = $startDateCarbon->subYear()->month($startDateCarbon->month)->firstOfMonth()->format("Y-m-d");
                    $compareEndDate = $endDateCarbon->subYear()->month($endDateCarbon->month)->endOfMonth()->format("Y-m-d");
                }
                break;
            case PerformanceV2Constant::TIME_TYPE_THIS_QUARTER:
            case PerformanceV2Constant::TIME_TYPE_LAST_QUARTER:
                // 环比
                if ($compareType == PerformanceV2Constant::COMPARE_TYPE_CHAIN) {
                    $compareStartDate = $startDateCarbon->startOfMonth()->subMonth(3)->firstOfMonth()->format("Y-m-d");
                    $compareEndDate = $endDateCarbon->startOfMonth()->subMonth(3)->endOfMonth()->format("Y-m-d");
                }
                // 同比
                if ($compareType == PerformanceV2Constant::COMPARE_TYPE_YEAR) {
                    $compareStartDate = $startDateCarbon->subYear()->month($startDateCarbon->month)->firstOfMonth()->format("Y-m-d");
                    $compareEndDate = $endDateCarbon->subYear()->month($endDateCarbon->month)->endOfMonth()->format("Y-m-d");
                }
                break;
            case PerformanceV2Constant::TIME_TYPE_THIS_YEAR:
            case PerformanceV2Constant::TIME_TYPE_LAST_YEAR:
                // 环比  同比 相同
                $compareStartDate = $startDateCarbon->subYear()->month($startDateCarbon->month)->firstOfMonth()->format("Y-m-d");
                $compareEndDate = $endDateCarbon->subYear()->month($endDateCarbon->month)->endOfMonth()->format("Y-m-d");
                break;
            default:
                $compareStartDate = $compareEndDate = '';
        }
        return [$compareStartDate, $compareEndDate];
    }

    /**
     * 获取目标值时间范围，目标值的获取跟设置的周期有关（目前只有过程目标可以设置周期，结果目标默认周期是月）
     * @param $startDate
     * @param $endDate
     * @param $timeType
     * @return array
     */
    public static function getConstrastStartDateAndEndDate($startDate,$endDate,$timeType){
        switch ($timeType){
            // 时间粒度为周
            case PerformanceV2Constant::TIME_TYPE_THIS_WEEK:
            case PerformanceV2Constant::TIME_TYPE_LAST_WEEK:
                $contrastStartDate = date("Y-m-d", strtotime("-1 weeks",strtotime($startDate)));
                $contrastEndDate   = date("Y-m-d", strtotime("-1 weeks", strtotime($endDate)));
                break;

            // 时间粒度为月
            case PerformanceV2Constant::TIME_TYPE_THIS_MONTH:
            case PerformanceV2Constant::TIME_TYPE_LAST_MONTH:
                $contrastStartDate = date("Y-m-d", strtotime("-1 months",strtotime($startDate)));
                $contrastEndDate   = date("Y-m-d", strtotime("-1 months", strtotime($endDate)));
                break;

            // 时间粒度为季度
            case PerformanceV2Constant::TIME_TYPE_THIS_QUARTER:
            case PerformanceV2Constant::TIME_TYPE_LAST_QUARTER:
                $contrastStartDate = date("Y-m-d", strtotime("-3 months",strtotime($startDate)));
                $contrastEndDate   = date("Y-m-d", strtotime("-3 months", strtotime($endDate)));
                break;

            // 时间粒度为年
            case PerformanceV2Constant::TIME_TYPE_THIS_YEAR:
            case PerformanceV2Constant::TIME_TYPE_LAST_YEAR:
                $contrastStartDate = date("Y-m-d", strtotime("-1 years",strtotime($startDate)));
                $contrastEndDate   = date("Y-m-d", strtotime("-1 years", strtotime($endDate)));
                break;

            default:
                $contrastStartDate = date("Y-m-d", strtotime("-1 days",strtotime($startDate)));
                $contrastEndDate   = date("Y-m-d", strtotime("-1 days", strtotime($endDate)));
                break;
        }

        return [$contrastStartDate, $contrastEndDate];
    }

    public static function getRulesGoalByConds($startDate,$endDate,$goalList,$targetType,$referId = 0){

        $goals = [];

        // 结果目标
        if($targetType == \PerformanceV2Goals::TARGET_RESULT_GOAL){
            $targetMonthRange = PerformanceV2Goal::getCycleListByStartAndEndDate($startDate, $endDate, true);

            foreach ($targetMonthRange as $yearMonth){
                foreach ($goalList as $referIdKey => $ruleIterms){
                    foreach ($ruleIterms as $ruleId => $datesGoal){

                        // 给一个默认值
                        if(!isset($goals[$referIdKey][$ruleId][$yearMonth])){
                            $goals[$referIdKey][$ruleId][$yearMonth]['amount'] = 0;
                        }

                        if(isset($datesGoal[$yearMonth])){
                            $goals[$referIdKey][$ruleId][$yearMonth]['amount'] += $datesGoal[$yearMonth];
                        }
                    }
                }
            }
        }

        return $goals;
    }

    public static function getRuleIndicatorByConds($startDate,$endDate,$recordList,$ruleIds,$referId,$indicators = []){
        $referIds         = is_array($referId) ? $referId : [$referId];
        $targetMonthRange = PerformanceV2Goal::getCycleListByStartAndEndDate($startDate, $endDate, true);

        // 给一个默认值
        foreach ($ruleIds as $ruleId){
            foreach ($targetMonthRange as $yearMonth){
                foreach ($referIds as $referIdValue){
                    if(!isset($indicators[$referIdValue][$ruleId][$yearMonth])){
                        $indicators[$referIdValue][$ruleId][$yearMonth]['indicator_value'] = 0;
                    }
                }
            }
        }

        foreach ($recordList as $record){
            $yearMonth = date("Y-n", strtotime($record['account_date']));

            if(!isset($indicators[$record['owner_id']][$record['rule_id']][$yearMonth])){
                continue;
            }

            $indicators[$record['owner_id']][$record['rule_id']][$yearMonth]['indicator_value'] += $record['indicator_value'];
        }

        return $indicators;
    }

    /**
     * 根据规则绑定的目标字段获取币种信息
     * time: 5:05 PM
     * user: huagongzi
     * @param string $targetField
     * @return mixed|string|null
     */
    public static function getCurrencyByTargetField(int $clientId,int $referType, string $targetField){
        // 判断是否需要获取主币种
        if(self::checkNeedToAssociatedWithExchangeRate($referType, $targetField)){
            $field = Client::getClient($clientId)->getMainCurrency();
        } else {
            $tmpArr = $targetField ? explode("_", $targetField) : [];
            $field  = empty($tmpArr) ? "" : array_pop($tmpArr);
        }

        if(!in_array($field, ['cny','usd','rmb'])){
            $field = '';
        }

        if($field == 'cny'){
            $field = 'rmb';
        }

        return $field;
    }

    /**
     * 挑出完成值需要根据referid去重的规则和不需要去重的规则
     * time: 2:40 PM
     * user: huagongzi
     * @param array $rulesList
     * @return array
     */
    public static function pickOutNeedDistinctOrNotByReferIdRules(array $rulesList = []){
        // 有些规则绑定的对象如订单，回款单，且是动态时间，sum统计不需根据referid做去重要去重
        $needDistinctRules = $needNotDistinctRules = [];

        foreach ($rulesList as $ruleInfo){
            $needDistinctWithOwnerId = self::checkNeedDistinctWithOwnerId($ruleInfo);

            if($needDistinctWithOwnerId){
                $needNotDistinctRules[] = $ruleInfo;
            } else {
                $needDistinctRules[] = $ruleInfo;
            }
        }

        return [$needDistinctRules,$needNotDistinctRules];
    }

    /**
     * 获取规则的完成情况列表
     * time: 5:13 PM
     * user: huagongzi
     * @param int $clientId
     * @param int $userId
     * @param string $time_type
     * @param string $startDate
     * @param string $endDate
     * @param int $referId
     * @param int $scope
     * @param int $targetType
     * @param int $ruleId
     * @return array
     */
    public static function getRulesContrastGoalCompletionList(int $clientId,int $userId,string $timeType,string $startDate,string $endDate, int $referId, int $scope,  $ruleId = 0, bool $showRinrate = false, $needReportGroupCache = true) {

        [$referId, $scope] = Helper::specialReferScopeHandle($clientId, $userId, $referId, $scope);

        // 获取环比的开始时间和结束时间
        $targetType = \PerformanceV2Goals::TARGET_RESULT_GOAL;

        // range 不显示环比
        if ($timeType == PerformanceV2Constant::TIME_TYPE_RANGE) {
            $showRinrate = false;
        }

        // 展示环比
        if($showRinrate){
            list($contrastStartDate,$contrastEndDate) = Helper::getCompletionStartDateAndEndData($startDate, $endDate, $timeType);
        }

        // 查所有的规则
        $Rulelist = new \common\library\performance_v2\rule\PerformanceV2RuleList($clientId, $userId);
        $Rulelist->setEnableFlag(true);
        $Rulelist->setOrderBy('create_time');
        $Rulelist->setOrder('asc');
        $Rulelist->setPerformanceType(\PerformanceV2Goals::TARGET_RESULT_GOAL);
        $Rulelist->setEnableFlag(true);

        if($ruleId){
            $Rulelist->setRuleId($ruleId);
        }

        $Rulelist->getFormatter()->setScope($scope);

        $rules      = $Rulelist->find();
        $rules      = Helper::getPerformanceV2RuleSortByUserSetting($clientId, $userId, \PerformanceV2Goals::TARGET_RESULT_GOAL, $rules);
        $ruleIds    = array_column($rules,'rule_id');
        $referIds   = is_array($referId) ? $referId : [$referId];
        $subDepartmentMap = [];
        $ownerIds   = [];

        // 查指定时间的目标值
        $goalsList = new PerformanceV2GoalList($clientId, $userId);
        $goalsList->setRuleId($ruleIds);
        $goalsList->setStartYearMonth(date("Y",strtotime($startDate)), date("m", strtotime($startDate)));
        $goalsList->setEndYearMonth(date("Y",strtotime($endDate)), date("m",strtotime($endDate)));
        $goalsList->setStartAccountDate($startDate);
        $goalsList->setEndAccountDate($endDate);
        $goalsList->setFillWithEmptyToMultiRuleMonthData(true);
        $goalsList->setScope($scope);
        $goalsList->setSkipPrivilege(true);
        $goalsList->setReferIds($referIds, $scope);
        $goals = $goalsList->find();

        // 根据不同时间类型获取目标值
        $targetGoals = Helper::getRulesGoalByConds($startDate,$endDate,$goals,$targetType,$referId);

        // 如果是部门，完成值需要包含该部门下的所有子部门完成值
        if ($scope == PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP) {
            $subDepartmentIds           = \common\library\department\Helper::getChildrenIds($clientId, $referId);
            $subDepartmentMap[$referId] = $subDepartmentIds;
            $referIds                   = array_unique(array_merge($referIds,$subDepartmentIds));
            list($temp1, $referIds)     = Helper::filterUserIdsAndDepartmentIds($clientId, [], $referIds);
            // $ownerIds                   = (new DepartmentMember($clientId))->getMemberUserIds($referId, true);
        } else {
            // 如果是人员
            // $ownerIds = [$referId];
            list($referIds, $temp1)     = Helper::filterUserIdsAndDepartmentIds($clientId, $referIds, []);
        }

        // 分别根据需要去重的规则和不需要去重的规则做数据统计
        list($needDistinctByReferIdRules, $needNotDistinctByReferIdRules) = Helper::pickOutNeedDistinctOrNotByReferIdRules($rules);
        $targetIndicator = $constrastIndicator = [];


        // 根据client_id,rule_id,refer_id去重
        if($needDistinctByReferIdRules){
            $field                        = 'DISTINCT ON( client_id, rule_id, refer_id ) client_id,refer_id,owner_id,rule_id,refer_type,record_type,indicator_value,account_date,create_time,update_time,company_id,record_id ';
            $needDistinctByReferIdRuleIds = array_column($needDistinctByReferIdRules,'rule_id');
            $needDistinctRecords          = self::getRuleRecordsByConds($clientId,$userId,$startDate,$endDate,$scope,$referIds,$needDistinctByReferIdRuleIds,$field);
            $targetIndicator              = Helper::getRuleIndicatorByConds($startDate, $endDate, $needDistinctRecords, $needDistinctByReferIdRuleIds, $referIds, $targetIndicator);

            if($showRinrate){
                $constrastRecords = self::getRuleRecordsByConds($clientId,$userId,$contrastStartDate,$contrastEndDate,$scope,$referIds,$needDistinctByReferIdRuleIds,$field);
                $constrastIndicator = Helper::getRuleIndicatorByConds($contrastStartDate,$contrastEndDate,$constrastRecords,$needDistinctByReferIdRuleIds,$referIds, $constrastIndicator);
            }
        }

        // 根据client_id,rule_id,refer_id,owner_id去重
        if($needNotDistinctByReferIdRules){
            $field                           = 'DISTINCT ON( client_id, rule_id, refer_id, owner_id ) client_id,refer_id,owner_id,rule_id,refer_type,record_type,indicator_value,account_date,create_time,update_time,company_id,record_id ';
            $needNotDistinctByReferIdRuleIds = array_column($needNotDistinctByReferIdRules,'rule_id');
            $needNotDistinctRecords          = self::getRuleRecordsByConds($clientId,$userId,$startDate,$endDate,$scope,$referIds,$needNotDistinctByReferIdRuleIds,$field);
            $targetIndicator                 = Helper::getRuleIndicatorByConds($startDate, $endDate, $needNotDistinctRecords, $needNotDistinctByReferIdRuleIds, $referIds, $targetIndicator);

            if($showRinrate){
                $constrastRecords = self::getRuleRecordsByConds($clientId,$userId,$contrastStartDate,$contrastEndDate,$scope,$referIds,$needNotDistinctByReferIdRuleIds,$field);
                $constrastIndicator = Helper::getRuleIndicatorByConds($contrastStartDate,$contrastEndDate,$constrastRecords,$needNotDistinctByReferIdRuleIds,$referIds, $constrastIndicator);
            }
        }

        $reportKey        = "ContrastGoalCompletionList:";
        $groupData        = [];
        $prefix           = "primary_key:";
        $reportGroupCache = new \common\library\statistics\foundation\store\ReportGroupCache($userId);
        $reportGroupCache->setReportKey($reportKey);
        $reportGroupCache->setReportUniqueKey($reportKey);

        if ($needReportGroupCache) {
            // 支持完成值跳转
            $params                     = [];
            $params['ruleId']           = $ruleIds;
            $params['startAccountDate'] = $startDate;
            $params['endAccountDate']   = $endDate;
            $params['recordType']       = $scope;
            $params['ownerId']          = $referIds;
            $params['skipOwnerCheck']   = true;
            if (empty($referIds)) {
                $referIdsMap = [];
            } else {
                $referIdsMap = Helper::getDistinctRuleReferIdsMap($clientId,$userId,$params);
            }
        }



        // 组装数据
        foreach ($rules as &$ruleInfo) {
            $ruleInfo["currency"]                       = Helper::getCurrencyByTargetField($ruleInfo['client_id'],$ruleInfo['refer_type'],$ruleInfo["target_field"]);
            $ruleInfo["year_month_amount"]              = [];
            $ruleInfo["year_month_indicator"]           = [];
            $ruleInfo["constrast_year_month_indicator"] = [];

            // 在指定枚举的考核对象中才有跳转功能
            if(isset(WorkflowConstant::CAN_JUMP_REFER_TYPE_ENUM[$ruleInfo['refer_type']])){
                $mainId                = WorkflowConstant::CAN_JUMP_REFER_TYPE_ENUM[$ruleInfo['refer_type']];
                $uniqueKey             = "{$clientId}:{$userId}:{$prefix}{$mainId}:{$ruleInfo['rule_id']}";
                $groupData[$uniqueKey] = isset($referIdsMap[$ruleInfo['rule_id']]) ? $referIdsMap[$ruleInfo['rule_id']] : [];
            }

            foreach ($referIds as $referValue){

                // 由目标值的referid来决定完成值和环比值是否展示相关的信息
                if(!isset($targetGoals[$referValue][$ruleInfo['rule_id']])){
                    continue;
                }

                // 目标值
                foreach ($targetGoals[$referValue][$ruleInfo['rule_id']] as $yearMonth => $amount){
                    if(isset($ruleInfo['year_month_amount'][$yearMonth]['amount'])){
                        $ruleInfo['year_month_amount'][$yearMonth]['amount'] += $amount['amount'];
                    } else {
                        $ruleInfo['year_month_amount'][$yearMonth]['amount'] = $amount['amount'];
                    }
                    $ruleInfo['year_month_amount'][$yearMonth]['amount'] = round($ruleInfo['year_month_amount'][$yearMonth]['amount'], 2);
                }

                // 完成值
                if(isset($targetIndicator[$referValue][$ruleInfo['rule_id']])){
                    foreach ($targetIndicator[$referValue][$ruleInfo['rule_id']] as $yearMonth => $indicator){
                        if(isset($ruleInfo['year_month_indicator'][$yearMonth]['indicator_value'])){
                            $ruleInfo['year_month_indicator'][$yearMonth]['indicator_value'] += $indicator['indicator_value'];
                        } else {
                            $ruleInfo['year_month_indicator'][$yearMonth]['indicator_value'] = $indicator['indicator_value'];
                        }
                    }

                    // 算上子部门的完成值
                    if(isset($subDepartmentMap[$referValue])){
                        foreach ($subDepartmentMap[$referValue] as $subDepartmentId){
                            if(isset($targetIndicator[$subDepartmentId][$ruleInfo['rule_id']])) {
                                foreach ($targetIndicator[$subDepartmentId][$ruleInfo['rule_id']] as $yearMonth => $indicator) {
                                    if (isset($ruleInfo['year_month_indicator'][$yearMonth]['indicator_value'])) {
                                        $ruleInfo['year_month_indicator'][$yearMonth]['indicator_value'] += $indicator['indicator_value'];
                                    } else {
                                        $ruleInfo['year_month_indicator'][$yearMonth]['indicator_value'] = $indicator['indicator_value'];
                                    }
                                }
                            }
                        }
                    }
                }

                // 环比完成值
                if (isset($constrastIndicator[$referValue][$ruleInfo['rule_id']])) {
                    foreach ($constrastIndicator[$referValue][$ruleInfo['rule_id']] as $yearMonth => $indicator){
                        if(isset($ruleInfo['constrast_year_month_indicator'][$yearMonth]['indicator_value'])){
                            $ruleInfo['constrast_year_month_indicator'][$yearMonth]['indicator_value'] += $indicator['indicator_value'];
                        } else {
                            $ruleInfo['constrast_year_month_indicator'][$yearMonth]['indicator_value'] = $indicator['indicator_value'];
                        }
                    }

                    // 算上子部门的完成值
                    if(isset($subDepartmentMap[$referValue])){
                        foreach ($subDepartmentMap[$referValue] as $subDepartmentId){
                            if(!isset($constrastIndicator[$subDepartmentId][$ruleInfo['rule_id']])) {
                                continue;
                            }

                            foreach ($constrastIndicator[$subDepartmentId][$ruleInfo['rule_id']] as $yearMonth => $indicator) {
                                if (isset($ruleInfo['constrast_year_month_indicator'][$yearMonth]['indicator_value'])) {
                                    $ruleInfo['constrast_year_month_indicator'][$yearMonth]['indicator_value'] += $indicator['indicator_value'];
                                } else {
                                    $ruleInfo['constrast_year_month_indicator'][$yearMonth]['indicator_value'] = $indicator['indicator_value'];
                                }
                            }
                        }
                    }
                }
            }

        }
        unset($ruleInfo);

        if ($needReportGroupCache) {
            // 支持跳转
            $cacheGroupMap = $reportGroupCache->setData($groupData);
            foreach ($rules as &$ruleInfo){
                $mainId                             = WorkflowConstant::CAN_JUMP_REFER_TYPE_ENUM[$ruleInfo['refer_type']] ?? $ruleInfo['rule_id'];
                $uniqueKey                          = "{$clientId}:{$userId}:{$prefix}{$mainId}:{$ruleInfo['rule_id']}";
                $ruleInfo['report_item_unique_key'] = isset($cacheGroupMap[$uniqueKey]) ? $cacheGroupMap[$uniqueKey] : '';
            }
            unset($ruleInfo);
        }

        return $rules;
    }

    public static function getRuleRecordsByConds(int $clientId,int $userId,string $startDate,string $endDate,int $scope,array $referIds,array $ruleId, string $field){
        if (empty($referIds)) {
            return [];
        }
        $recordModel = new PerformanceV2RecordList($clientId, $userId);
        $recordModel->setSkipOwnerCheck(true);
        $recordModel->setStartAccountDate($startDate);
        $recordModel->setEndAccountDate($endDate);
        $recordModel->setRecordType($scope);
        $recordModel->setRuleId($ruleId);
        $recordModel->setFields($field);

        if(!empty(array_filter($referIds))) {
            $recordModel->setOwnerId($referIds);
        }

        return $recordModel->distinctRecordList();
    }

    public static function getProcessRuleContrastGoalCompletionList(int $clientId,int $userId, string $startDate,string $endDate, int $referId, int $scope, int $ruleId = 0)
    {
        [$referId, $scope] = Helper::specialReferScopeHandle($clientId, $userId, $referId, $scope);
        // 查所有的规则
        $Rulelist = new \common\library\performance_v2\rule\PerformanceV2RuleList($clientId, $userId);
        $Rulelist->setEnableFlag(true);
        $Rulelist->setOrderBy('create_time');
        $Rulelist->setOrder('asc');
        $Rulelist->setPerformanceType(\PerformanceV2Goals::TARGET_PROCESS_GOAL);
        $Rulelist->setEnableFlag(true);
        $Rulelist->setFields('client_id,rule_id,name,description,refer_type,target_field,refer_type,time_field,calculate_rule,performance_field');

        if($ruleId){
            $Rulelist->setRuleId($ruleId);
        }

        $Rulelist->getFormatter()->setScope($scope);

        $rules    = $Rulelist->find();
        $rules    = Helper::getPerformanceV2RuleSortByUserSetting($clientId, $userId, \PerformanceV2Goals::TARGET_PROCESS_GOAL, $rules);
        $ruleIds  = array_column($rules,'rule_id');
        $referIds = is_array($referId) ? $referId : [$referId];
        $goals    = Helper::getProcessGoalList($clientId, $userId, $ruleIds, $startDate, $endDate, $referIds, $scope, 1);
        $ruleGoals = \ArrayUtil::index($goals, 'performance_rule_id');

        $reportKey        = __FUNCTION__;
        $groupData        = [];
        $prefix           = "primary_key:";
        $reportGroupCache = new \common\library\statistics\foundation\store\ReportGroupCache($userId);
        $reportGroupCache->setReportKey($reportKey);
        $reportGroupCache->setReportUniqueKey($reportKey);

        if($scope == PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP){
            $referIds = (new DepartmentMember($clientId))->getMemberUserIds($referId, true);
        }

        // 支持完成值跳转
        $params                     = [];
        $params['ruleId']           = $ruleIds;
        $params['startAccountDate'] = $startDate;
        $params['endAccountDate']   = $endDate;
        $params['recordType']       = PerformanceV2Constant::SETTING_GOAL_SCOPE_USER;
        $params['ownerId']          = $referIds;
        $params['skipOwnerCheck']   = true;
        $referIdsMap                = Helper::getDistinctRuleReferIdsMap($clientId,$userId,$params);

        // 组装数据
        foreach ($rules as &$ruleInfo) {
            $ruleInfo["currency"] = Helper::getCurrencyByTargetField($ruleInfo['client_id'],$ruleInfo['refer_type'],$ruleInfo["target_field"]);

            // 在指定枚举的考核对象中才有跳转功能
            if(isset(WorkflowConstant::CAN_JUMP_REFER_TYPE_ENUM[$ruleInfo['refer_type']])){
                $mainId                = WorkflowConstant::CAN_JUMP_REFER_TYPE_ENUM[$ruleInfo['refer_type']];
                $uniqueKey             = "{$clientId}:{$userId}:{$prefix}{$mainId}:{$ruleInfo['rule_id']}";
                $groupData[$uniqueKey] = isset($referIdsMap[$ruleInfo['rule_id']]) ? $referIdsMap[$ruleInfo['rule_id']] : [];
            }

            // 目标值
            if(isset($ruleGoals[$ruleInfo['rule_id']])){
                $goalInfo                    = $ruleGoals[$ruleInfo['rule_id']];
                $ruleInfo['amount']          = $goalInfo['amount'];
                $ruleInfo['indicator_value'] = $goalInfo['indicator_value'];
                $ruleInfo['completion_rate'] = floatval($ruleInfo['amount']) == 0 ? 0 : round($ruleInfo['indicator_value'] / $ruleInfo['amount'] * 100 , 2);
                $ruleInfo['constrast_value'] = $goalInfo['ringrate_indicator_value'];
                $ruleInfo['constrast_rate']  = floatval($goalInfo['ringrate_indicator_value']) == 0 ? 0 : round(($goalInfo['indicator_value'] - $goalInfo['ringrate_indicator_value']) / $goalInfo['ringrate_indicator_value'] * 100 , 2);
            } else {
                $ruleInfo['amount']          = 0;
                $ruleInfo['indicator_value'] = 0;
                $ruleInfo['completion_rate'] = 0;
                $ruleInfo['constrast_value'] = 0;
                $ruleInfo['constrast_rate']  = 0;
            }
        }
        unset($ruleInfo);

        // 支持跳转
        $cacheGroupMap = $reportGroupCache->setData($groupData);
        foreach ($rules as &$ruleInfo){
            $mainId                             = WorkflowConstant::CAN_JUMP_REFER_TYPE_ENUM[$ruleInfo['refer_type']] ?? $ruleInfo['rule_id'];
            $uniqueKey                          = "{$clientId}:{$userId}:{$prefix}{$mainId}:{$ruleInfo['rule_id']}";
            $ruleInfo['report_item_unique_key'] = isset($cacheGroupMap[$uniqueKey]) ? $cacheGroupMap[$uniqueKey] : '';
        }
        unset($ruleInfo);

        return $rules;
    }

    public static function getNewProcessRuleContrastGoalCompletionList(int $clientId,int $userId, string $startDate, string $endDate, string $timeType, int $referId, int $scope,  $ruleId = 0)
    {
        [$referId, $scope] = Helper::specialReferScopeHandle($clientId, $userId, $referId, $scope);
        // 查所有的规则
        $rulelist = new \common\library\performance_v2\rule\PerformanceV2RuleList($clientId, $userId);
        $rulelist->setEnableFlag(true);
        $rulelist->setOrderBy('create_time');
        $rulelist->setOrder('asc');
        $rulelist->setPerformanceType(\PerformanceV2Goals::TARGET_PROCESS_GOAL);
        $rulelist->setEnableFlag(true);
        $rulelist->getFormatter()->setShowWorkJournalTemplateInfo(true);

        if($ruleId){
            $rulelist->setRuleId($ruleId);
        }

        $rulelist->getFormatter()->setScope($scope);

        $rules    = $rulelist->find();
        $rules    = Helper::getPerformanceV2RuleSortByUserSetting($clientId, $userId, \PerformanceV2Goals::TARGET_PROCESS_GOAL, $rules);
        $ruleIds  = array_column($rules,'rule_id');
        $referIds = is_array($referId) ? $referId : [$referId];
        $goals    = Helper::getNewProcessGoalList($clientId, $userId, $ruleIds, $startDate, $endDate, $timeType, $referIds, $scope, 1);
        $ruleGoals = \ArrayUtil::index($goals, 'performance_rule_id');

        // 组装数据
        foreach ($rules as &$ruleInfo) {
            $ruleInfo["currency"] = Helper::getCurrencyByTargetField($ruleInfo['client_id'],$ruleInfo['refer_type'],$ruleInfo["target_field"]);
            $ruleInfo['time_type'] = $timeType; // 告诉前端，后端对range有转化
            // 目标值
            if(isset($ruleGoals[$ruleInfo['rule_id']])){
                $goalInfo                    = $ruleGoals[$ruleInfo['rule_id']];
                $ruleInfo['amount']          = round($goalInfo['amount'], 2);
                $ruleInfo['indicator_value'] = $goalInfo['indicator_value'];
                $ruleInfo['completion_rate'] = floatval($ruleInfo['amount']) == 0 ? 0 : round($ruleInfo['indicator_value'] / $ruleInfo['amount'] * 100 , 2);
                $ruleInfo['constrast_value'] = $goalInfo['ringrate_indicator_value'];
                $ruleInfo['constrast_rate']  = floatval($goalInfo['ringrate_indicator_value']) == 0 ? 0 : round(($goalInfo['indicator_value'] - $goalInfo['ringrate_indicator_value']) / $goalInfo['ringrate_indicator_value'] * 100 , 2);
            } else {
                $ruleInfo['amount']          = 0;
                $ruleInfo['indicator_value'] = 0;
                $ruleInfo['completion_rate'] = 0;
                $ruleInfo['constrast_value'] = 0;
                $ruleInfo['constrast_rate']  = 0;
            }
            if (!empty($ruleGoals[$ruleInfo['rule_id']]['report_item_unique_key'])) {
                $ruleInfo['report_item_unique_key'] = $ruleGoals[$ruleInfo['rule_id']]['report_item_unique_key'];
            }

            if ($ruleInfo['indicator_value'] >= \common\library\ai_agent\AiAgentConstants::CACHE_NUMBER_WORKFLOW_WARNING_VALUE)
            {
                \LogUtil::info('bigNumberRuleInfo', [
                    $ruleInfo['indicator_value'],
                    $ruleGoals[$ruleInfo['rule_id']]['indicator_value'] ?? ''
                ]);
            }
        }
        unset($ruleInfo);
        return $rules;
    }

    /**
     * 根据月份获取季度
     * time: 5:11 PM
     * user: huagongzi
     * @param $month
     * @return int
     */
    public static function getQuator($month){
        $remainder      = $month % 3;
        $positiveNumber = intval($month / 3);
        return $remainder > 0 ? $positiveNumber + 1 : $positiveNumber;
    }

    /**
     * 获取指定时间是当月中的第几周
     * time: 11:22 PM
     * user: huagongzi
     * @param string $date
     * @return int
     */
    public static function weekOfMonth(string $date) {
        $monthFirstDay = date("Y-m-01", strtotime($date));
        $firstWeek     = intval(date("W", strtotime($monthFirstDay)));
        $currentWeek   = intval(date("W", strtotime($date)));

        // 处理一周跨两年的问题
        if($firstWeek > 50 && $currentWeek < 50 ){
            $firstWeek = 1;
        }
        $weekIndex = $currentWeek - $firstWeek + 1;
        return $weekIndex;
    }

    public static function weekIndexOfYear(string $date){
        $year            = date("Y", strtotime($date));
        $month           = date("n", strtotime($date));
        $weekIndexOfYear = intval(date("W", strtotime($date)));;

        // 解决一周跨两年的问题
        if($month == 1 && $weekIndexOfYear > 50){
            --$year;
        }

        return [$year, $weekIndexOfYear];
    }

    /**
     * 获取目标列表
     * time: 5:26 PM
     * user: huagongzi
     * @param int $clientId
     * @param int $userId
     * @param int $targetType
     * @return array
     */
    public static function getGoalList(int $clientId,int $userId, int $targetType,string $startDate,string $endDate){

        $ruleList   = self::getRuleList($clientId,$userId,$targetType,0);
        $ruleIds    = array_column($ruleList, 'rule_id');
        $startYear  = date("Y", strtotime($startDate));
        $startMonth = date("n", strtotime($startDate));
        $endYear    = date("Y", strtotime($endDate));
        $endMonth   = date("n", strtotime($endDate));

        if($targetType == \PerformanceV2Goals::TARGET_RESULT_GOAL){
            $goalsList = new PerformanceV2GoalList($clientId, $userId);
            $goalsList->setRuleId($ruleIds);
            $goalsList->setSkipPrivilege(true);
            $goalsList->setStartYearMonth($startYear, $startMonth);
            $goalsList->setEndYearMonth($endYear, $endMonth);
            $goalsList->setFields("distinct on(client_id,performance_rule_id) client_id,performance_rule_id");
            $goalsList->setOrderBy('');
            $goalsList->setOrder('');
            $goals = $goalsList->find();
        } else {
            $configList = new PerformanceV2GoalConfigList($clientId, $userId);
            $configList->setRuleIds($ruleIds);
            $configList->getFormatter()->setMergeList(true);
            $goals = $configList->find();
        }

        return \ArrayUtil::index($goals,"performance_rule_id");
    }

    /**
     * 获取成员或部门的目标完成情况
     * time: 5:54 PM
     * user: huagongzi
     * @param int $clientId
     * @param int $userId
     * @param string $timeType
     * @param string $startDate
     * @param string $endDate
     * @param array $userIds
     * @param int $scope
     * @param int $targetType
     * @param int $ruleId
     * @return array
     */
    public static function getUserOrGroupCompletionList(int $clientId,int $userId,string $timeType,string $startDate,string $endDate, array $userIds ,int $scope,int $targetType,int $ruleId = 0){
        $ruleList               = self::getRuleList($clientId, $userId, $targetType, $ruleId, 'rule_id,name,description,enable_flag,performance_type,refer_type,time_field,calculate_rule,performance_field',);
        $userGoalCompletionList = [];

        if(empty($ruleList)){
            return $userGoalCompletionList;
        }

        if($ruleId){
            $ruleList = \ArrayUtil::index($ruleList,"rule_id");
            $ruleList = [$ruleList[$ruleId]];
        }

        if ($targetType == \PerformanceV2Goals::TARGET_RESULT_GOAL) {
            $goalList = self::getResultTargetGoalByTimeType($clientId, $userId, $ruleList, $startDate, $endDate, $timeType, $userIds, $scope);
        } else {
            $goalList = self::getProcessGoalByTimeType($clientId, $userId, $ruleList, $startDate, $endDate, $userIds);
        }

        $recordList = Helper::getRecordAggResult($ruleList, $clientId, $userId, $startDate, $endDate, $userIds);
        $records    = [];

        foreach ($recordList as $record){
            if(isset($records[$record['rule_id']][$record['owner_id']])){
                $records[$record['rule_id']][$record['owner_id']]['indicator_value'] += $record['indicator_value'];
            } else {
                $records[$record['rule_id']][$record['owner_id']] = [
                    "indicator_value" => $record['indicator_value']
                ];
            }
        }

        $ruleReferGoals = [];
        foreach ($goalList as $goal){
            if(!isset($goal['rule_id']) && isset($goal['performance_rule_id'])){
                $goal['rule_id'] = $goal['performance_rule_id'];
            }

            $ruleReferGoals[$goal['rule_id']][$goal['refer_id']] = $goal;
        }

        foreach ($ruleReferGoals as $ruleId => &$referList){
            foreach ($referList as &$referInfo){
                if(isset($records[$ruleId][$referInfo['refer_id']])){
                    $referInfo['indicator_value'] = $records[$ruleId][$referInfo['refer_id']]['indicator_value'];
                    $referInfo['completion_rate'] = intval($referInfo['amount']) != 0 ? round($referInfo['indicator_value'] / $referInfo['amount'] * 100, 2) : 0;
                } else {
                    $referInfo['indicator_value'] = isset($referInfo['indicator_value']) ? $referInfo['indicator_value'] : 0;
                    $referInfo['completion_rate'] = intval($referInfo['amount']) != 0 ? round($referInfo['indicator_value'] / $referInfo['amount'] * 100, 2) : 0;
                }
            }
        }

        return $ruleReferGoals;
    }


    /**
     * 根据根据currency转换对应字段
     * @param $calculateItem
     * @param $currency
     */
    public static function transferCalculateFieldByCurrencyType($calculateItem,$currency)
    {
        if (empty($currency))
        {
            return $calculateItem;
        }

        $map = [
            "CNY" => [
                'amount' => 'amount_rmb',
                'product_total_amount' => 'product_total_amount_rmb',
                'order_gross_margin' => 'order_gross_margin_cny',
                'real_amount' => 'real_amount_rmb',
                'collect_amount' => 'collect_amount_rmb',
                'real_profit' => 'real_profit_rmb'
            ],
            "USD" => [
                'amount' => 'amount_usd',
                'product_total_amount' => 'product_total_amount_usd',
                'order_gross_margin' => 'order_gross_margin_usd',
                'real_amount' => 'real_amount_usd',
                'collect_amount' => 'collect_amount_usd',
                'real_profit_usd' => 'real_profit_usd'
            ]
        ];
        return array_key_exists($calculateItem,$map[$currency] ?? []) ? $map[$currency][$calculateItem] : $calculateItem;
    }

    /**
     *
     * relateInfo结构：
     * primary_key => xxxx  =>
     *                          [
     *                              'order'  => [xxxxxx   这里是order中的一些字段]
     *                              'opportunity => [xxxxxxx   opportunity 中的一些字段]
     *                           ]
     *
     *  e.g $relateInfo[$referInfo['cash_collection_id']]['opportunity']['external_field_data'] ==> 根据回款单的primaryKey 找到对应商机的external_field_data字段
     * @param $referInfo
     * @param $formula
     * @return float|void
     */
    public static function getValueByPerformanceFormula($referType, $referInfo, $formula, $relateInfo,$mainCurrency)
    {
        if (empty($formula) || $formula == '{}') {
            return 0.00;
        }

        $calculateFields = $formula['calculate_fields'];
        $currencyType = $formula['currency_type'] ?? '';
        $fields = [];

        $currency = match ($currencyType) {
            'main_currency' => $mainCurrency,
            'rmb' => "CNY",
            'usd' => "USD",
            default => '',
        };


        foreach ($calculateFields as $field) {
            $fieldArr = explode('.', $field);
            $type = $fieldArr[0];
            $item = $fieldArr[1];

            $item = self::transferCalculateFieldByCurrencyType($item,$currency);

            // 订单特殊判断
            if ($type == $referType  && $type != \Constants::TYPE_ORDER) {
                if (is_numeric($item)) {
                    $externalFieldData = json_decode($referInfo['external_field_data'], true);
                    if (isset($externalFieldData[$item]) && is_numeric($externalFieldData[$item])) {
                        $fields[$field] = $externalFieldData[$item];
                    } else {
                        $fields[$field] = 0;
                    }
                    // 汇兑损益特殊处理
                }else if(isset(self::SPECIAL_INFO_MAP[$referType])&&in_array($item,self::SPECIAL_INFO_MAP[$referType])){
                    $fields[$field] = $referInfo["specialInfo"];
                } else {
                    $fields[$field] = $referInfo[$item];
                }
            } else if ($type == $referType && $type == \Constants::TYPE_ORDER){
                switch ($item){
                    case 'cost_list' :
                        $costListArr = $referInfo[$item];
                        $costId = $fieldArr[2];
                        $result = collect(json_decode($costListArr, true))->where('cost_item_relation_id', '=', $costId)->all();
                        $orderExchangeRate = $mainCurrency == 'CNY' ? $referInfo['exchange_rate'] : $referInfo['exchange_rate_usd'];
                        $cost = array_sum(array_column($result, 'cost'));
                        $fields[$field] = ($cost * $orderExchangeRate) / 100;
                        break;
                    case in_array($item,self::ORDER_HAS_RELATE_INFO_MAP):
                        $fields[$field] = $referInfo['specialInfo'][$item] ?? 0;
                        break;
                    case is_numeric($item):
                        $externalFieldData = json_decode($referInfo['external_field_data'], true);
                        if (isset($externalFieldData[$item]) && is_numeric($externalFieldData[$item])) {
                            $fields[$field] = $externalFieldData[$item];
                        } else {
                            $fields[$field] = 0;
                        }
                        break;
                    default:
                        if (self::checkNeedToAssociatedWithExchangeRate($type,$item))
                        {
                            $orderExchangeRate = $mainCurrency == 'CNY' ? $referInfo['exchange_rate'] : $referInfo['exchange_rate_usd'];
                            $fields[$field] = (($referInfo[$item] ?? 0) * $orderExchangeRate) / 100;
                        }else{
                            $fields[$field] = $referInfo[$item] ?? 0;
                        }
                }
            } else {
                // 关联对象字段 根据原关联对象获取被关联对象字段
                switch ($referType) {
                    case \Constants::TYPE_ORDER:
                        // 订单关联商机
                        if ($type == \Constants::TYPE_OPPORTUNITY) {
                            if (is_numeric($item)) {
                                $externalFieldData = json_decode($relateInfo[$referInfo['order_id']]['opportunity']['external_field_data'] ?? '', true);
                                if (isset($externalFieldData[$item]) && is_numeric($externalFieldData[$item])) {
                                    $fields[$field] = $externalFieldData[$item];
                                } else {
                                    $fields[$field] = 0;
                                }
                            } else {
                                $fields[$field] = $relateInfo[$referInfo['order_id']]['opportunity'][$item] ?? 0;
                            }
                        }
                        break;
                    case \Constants::TYPE_CASH_COLLECTION:
                        // 回款单关联商机
                        if ($type == \Constants::TYPE_OPPORTUNITY) {
                            if (is_numeric($item)) {
                                $opportunityId = $referInfo['opportunity_id'];
                                if (empty($opportunityId)) {
                                    $fields[$field] = 0;
                                    break;
                                }
                                $externalFieldData = json_decode($relateInfo[$referInfo['cash_collection_id']]['opportunity']['external_field_data'] ?? '', true);
                                if (isset($externalFieldData[$item]) && is_numeric($externalFieldData[$item])) {
                                    $fields[$field] = $externalFieldData[$item];
                                } else {
                                    $fields[$field] = 0;
                                }
                            } else {
                                $fields[$field] = $relateInfo[$referInfo['cash_collection_id']]['opportunity'][$item] ?? 0;
                            }

                        } else if ($type == \Constants::TYPE_ORDER) {
                            // 回款单关联订单
                            if ($item === 'cost_list') {
                                $costListArr = $relateInfo[$referInfo['cash_collection_id']]['order']['cost_list'] ?? '';
                                $costId = $fieldArr[2];
                                $orderExchangeRate = $mainCurrency == 'CNY' ? $referInfo['exchange_rate'] : $referInfo['exchange_rate_usd'];
                                $result = collect(json_decode($costListArr, true))->where('cost_item_relation_id', '=', $costId)->all();
                                $cost = array_sum(array_column($result, 'cost'));
                                $fields[$field] = ($cost * $orderExchangeRate) / 100;

                            } elseif (is_numeric($item)) {

                                $externalFieldData = json_decode($relateInfo[$referInfo['cash_collection_id']]['order']['external_field_data'] ?? '', true);

                                if (isset($externalFieldData[$item]) && is_numeric($externalFieldData[$item])) {
                                    $fields[$field] = $externalFieldData[$item];
                                } else {
                                    $fields[$field] = 0;
                                }
                            } else if (in_array($item,self::ORDER_HAS_RELATE_INFO_MAP))
                            {
                                $fields[$field] = $relateInfo[$referInfo['cash_collection_id']]['order']['specialInfo'][$item] ?? 0;

                            } else if (self::checkNeedToAssociatedWithExchangeRate($type,$item))
                            {
                                $orderExchangeRate = $mainCurrency == 'CNY' ? $referInfo['exchange_rate'] : $referInfo['exchange_rate_usd'];
                                $fields[$field] = (($relateInfo[$referInfo['cash_collection_id']]['order'][$item] ?? 0) * $orderExchangeRate) / 100;
                            } else {
                                $fields[$field] = $relateInfo[$referInfo['cash_collection_id']]['order'][$item] ?? 0;
                            }
                        }
                }
            }
        }
        return Parser::solve($formula['calculate'], $fields, 0, true);
    }


    public static function getPerformanceRuleCount($clientId)
    {

        $omsFlag = \common\library\privilege_v3\Helper::checkHasOmsPrivilege($clientId);

        $type = [];
        $countMap = [
            \common\models\client\PerformanceV2Rule::PERFORMANCE_TYPE_RESULT => 0,
            \common\models\client\PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS => 0,
            'result_template_count' => [
                PerformanceV2Constant::TEMPLATE_TYPE_ORDER => 0, // 订单
                PerformanceV2Constant::TEMPLATE_TYPE_OPPORTUNITY => 0, // 商机
                PerformanceV2Constant::TEMPLATE_TYPE_CASH_COLLECTION => 0 // 回款单
            ],
            'process_template_count' => [
                PerformanceV2Constant::TEMPLATE_TYPE_COMMUNICATE_PROCESS => 0, //沟通过程
                PerformanceV2Constant::TEMPLATE_TYPE_FOLLOW_UP_PROCESS => 0 //跟进过程
            ]
        ];
        $db = PgActiveRecord::getDbByClientId($clientId);
        if (!\common\library\privilege_v3\Helper::hasFunctional($clientId, \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_PERFORMANCE_TEAM_WALL)) {
            $type = [
                PerformanceV2Constant::RULE_TYPE_COMMON,
                PerformanceV2Constant::RULE_TYPE_ORDER_AMOUNT,
                PerformanceV2Constant::RULE_TYPE_OPPORTUNITY_WIN,
                PerformanceV2Constant::RULE_TYPE_CASH_COLLECTION_AMOUNT,
                PerformanceV2Constant::RULE_TYPE_COMPANY_COUNT,
                PerformanceV2Constant::RULE_TYPE_SEND_MAIL_COUNT,
                PerformanceV2Constant::RULE_TYPE_FOLLOW_UP_COUNT,

            ];
        }
        $sql = "select count(1),performance_type from tbl_performance_v2_rule where client_id = {$clientId} and delete_flag = 0 and refer_type != 53 ";
        if (!empty($type))
        {
            $typeStr = implode(",",$type);
            $sql .= "and type in ({$typeStr}) ";
        }
        $sql .= " group by  performance_type";

        $resultList = $db->createCommand($sql)->queryAll();

        $resultListArr = array_column($resultList,'count','performance_type');
        foreach ($resultListArr as $key => $val) {
            $countMap[$key] = $val;
        }
        // 模板的count
        $resultTemplateCount = \common\library\performance_v2\PerformanceTemplateConfig::getPerformanceTemplateConfig($clientId, \common\models\client\PerformanceV2Rule::PERFORMANCE_TYPE_RESULT, 0);
        foreach ($resultTemplateCount as $key => $items) {
            foreach ($items as  $item) {

                $list = $item['list'] ?? [];
                foreach ($list as $value) {
                    // oms用户需要过滤203
                    if (!$omsFlag && $value['id'] == 203) continue;
                    $countMap['result_template_count'][$key] += 1;
                }
            }
        }


        $processTemplateCount = \common\library\performance_v2\PerformanceTemplateConfig::getPerformanceTemplateConfig($clientId, \common\models\client\PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS, 0);
        foreach ($processTemplateCount as $key => $items) {
            foreach ($items as $item) {
                $countMap['process_template_count'][$key] +=  count($item['list'] ?? []);
            }
        }

        return $countMap;


    }


    public static function transferPerformanceField($referTYpe, $performanceField) : string
    {
        $map = [
            \Constants::TYPE_MAIL => [
                'sender' => 'user_id',
                'receiver' => 'user_id'
            ]
        ];
        return $map[$referTYpe][$performanceField] ?? $performanceField;
    }


    public static function transferTimeField($referTYpe, $timeField) : string
    {
        $map = [
            \Constants::TYPE_ORDER => [
                'order_status_update_time' => 'update_time',
            ],
            \Constants::TYPE_MAIL =>
                [
                    // 发送时间和接收时间用的同一个字段 这里做一下转换
                    'send_time' => 'receive_time'
                ],
            \Constants::TYPE_COMPANY => [
                // 最近成交订单日期 废弃
                'recently_order_time' => 'latest_transaction_order_time',
                // 最近成交日期 废弃
                'recently_deal_time' => 'deal_time',
                // 最近赢单日期 废弃
                'recently_opportunity_time' => 'latest_success_opportunity_time',
                // 每次成交日期
                'every_transaction_order_time' => 'latest_transaction_order_time'
            ]
        ];
        return $map[$referTYpe][$timeField] ?? $timeField;
    }



    public static function updateEdmPerformance(\common\library\edm\EdmMail $mail, $targetField)
    {
        $clientId = $mail->client_id;
        $userId = $mail->user_id;
        $taskId = $mail->task_id;

        $now = date('Y-m-d H:i:s');
        $referType = \Constants::TYPE_EDM;
        $db = \PgActiveRecord::getDbByClientId($clientId);
        // 检查当前用户是否有edm规则
        $ruleList = new PerformanceV2RuleList($clientId ,$userId);
        $ruleList->setReferType($referType);
        $ruleList->setEnableFlag(BaseObject::ENABLE_FLAG_TRUE);
        $ruleList->setTargetField($targetField);
        $count = $ruleList->count();

        if (empty($count)) return ;


        $checkRuleIdSql = "select distinct (rule_id) from tbl_performance_v2_rule as rule where client_id = {$clientId} and refer_type = {$referType} and target_field = '{$targetField}' and rule_id not in (
    select rule_id from tbl_performance_v2_record as record where record.client_id = {$clientId} and record.refer_type = {$referType} and refer_id = {$taskId})";
        $ruleIds = $db->createCommand($checkRuleIdSql)->queryColumn();
        if (!empty($ruleIds)) {
            $recordId = PgActiveRecord::produceAutoIncrementId(count($ruleIds));
            $values = [];
            // 对于这些rule新增一条为0的数据
            foreach ($ruleIds as $ruleId) {
                $info = [];
                $info['client_id'] = $clientId;
                $info['refer_id'] = $taskId;
                $info['owner_id'] = $userId ?? 0;
                $info['rule_id'] = $ruleId;
                $info['refer_type'] = \Constants::TYPE_EDM;
                $info['record_type'] = 2;
                $info['indicator_type'] = $targetField;
                $info['indicator_value'] =  0;
                $info['account_date'] = \Util::escapeDoubleQuoteSql($now);
                $info['create_time'] = \Util::escapeDoubleQuoteSql($now);
                $info['update_time'] = \Util::escapeDoubleQuoteSql($now);
                $info['company_id'] = 0;
                $values[] = "({$recordId}, {$info['client_id']}, {$info['refer_id']}, {$info['owner_id']}, {$info['rule_id']}, {$info['refer_type']}, {$info['record_type']}, '{$info['indicator_type']}', {$info['indicator_value']}, '{$info['account_date']}', '{$info['create_time']}', '{$info['update_time']}', {$info['company_id']})";
                $recordId --;
            }

            $performanceSql = "insert into tbl_performance_v2_record (record_id,client_id, refer_id, owner_id, rule_id, refer_type, record_type, indicator_type, indicator_value, account_date, create_time, update_time, company_id) VALUES";
            $commonInsertEndSql = " ON CONFLICT (client_id, rule_id, owner_id, refer_id,account_date) DO UPDATE SET indicator_type = EXCLUDED.indicator_type, account_date = EXCLUDED.account_date, update_time = EXCLUDED.update_time, indicator_value = EXCLUDED.indicator_value, owner_id = EXCLUDED.owner_id, company_id = EXCLUDED.company_id";
            $insertSql = $performanceSql . implode(",", $values) . $commonInsertEndSql;
            $db->getPdoInstance()->prepare($insertSql)->execute();
        }
        $performanceParams = [
            'indicator_value' => 1
        ];
        \PerformanceV2Record::model()->updateCounters($performanceParams, 'client_id=:clientId and refer_id=:referId and refer_type=:referType and indicator_type=:indicatorType and account_date=:accountDate',
            [
                ':clientId' => $clientId,
                'referId' => $taskId,
                'referType' => \Constants::TYPE_EDM,
                'indicatorType' => $targetField,
                'accountDate' => \Util::escapeDoubleQuoteSql($now)
            ]);
    }



    public static function runPerformance($clientId, $type, $objectIds, $oldAttributes = [], $updateFields = [],$scene = '')
    {
        if (empty($clientId)  || empty($type) || empty($objectIds)) {
            return;
        }

        // 假如存在某些类型，需要在3s中有同一批执行的referIds就直接返回，则在此处理
        if (in_array($type, [
                \Constants::TYPE_CASH_COLLECTION, \Constants::TYPE_ORDER, \Constants::TYPE_COMPANY, \Constants::TYPE_OPPORTUNITY
        ]) && self::isExec($clientId, $objectIds)) {

            \LogUtil::info('performanceIsExec', [
                'clientId' => $clientId,
                'objectIds' => $objectIds
            ]);

            return;
        }

        // EdmMail - trackDelivery 有一段特殊逻辑，如果改造以下方法请同步修改traceController
        if (php_sapi_name() == "cli" || \Yii::app()->params['env'] == 'exp') {
            (new PerformanceV2RecordJob($clientId, $type, $objectIds, $oldAttributes, $updateFields,$scene))->handle();
        } else {
            QueueService::dispatch(new PerformanceV2RecordJob($clientId, $type, $objectIds, $oldAttributes, $updateFields, $scene));
        }
    }

    /**
     * 组装所有相关的refer数据
     * @param $formula
     * @param $referIds
     * @param $originReferType
     */
    public static function getFormulaRelateInfoByReferId($clientId,$userId,$formula,$referIds,$originReferType)
    {
        // 组装数据
        $referTypes = $formula['field_type'];
        $calculateFields = $formula['calculate_fields'];
        $fields = [];
        $referTypes = is_array($referTypes) ? $referTypes : [$referTypes];
        if (empty($referTypes) || (count($referTypes) == 1 && $referTypes[0] == $originReferType))
        {
            return [];
        }
        // 获取数据
        foreach ($referTypes as $referType)
        {
            if ($referType == $originReferType) continue;
            switch ($originReferType) {
                case \Constants::TYPE_ORDER:
                    if ($referType != \Constants::TYPE_OPPORTUNITY) break;
                    $orderList = new OrderList($userId);
                    $orderList->setOrderIds($referIds);
                    $orderList->setFields(['opportunity_id','order_id']);
                    $orderList->setSkipPermissionCheck(true);
                    $orderList->setEnableFlag(true);
                    $orderOpportunityInfo = $orderList->find();


                    $opportunityIds = array_filter(array_column($orderOpportunityInfo,'opportunity_id'),function ($v){
                        return !empty($v) ;
                    });
                    $orderOpportunityMap = array_filter(array_column($orderOpportunityInfo,'opportunity_id','order_id'),function ($v)
                    {
                        return !empty($v);
                    });

                    $opportunityList = new OpportunityList($clientId);
                    $opportunityList->setOpportunityIds($opportunityIds);
                    $opportunityList->setSkipPermissionCheck(true);
                    $opportunityList->setEnableFlag(true);
                    $opportunityList->setFields(['opportunity_id','external_field_data','amount','amount_rmb','amount_usd']);
                    $opportunityInfo = $opportunityList->find();
                    $opportunityInfo = array_column($opportunityInfo,null,'opportunity_id');
                    foreach ($orderOpportunityMap as  $orderId => $opportunityId)
                    {
                        $fields[$orderId]['opportunity'] = $opportunityInfo[$opportunityId] ?? [];
                    }
                    break;

                case \Constants::TYPE_CASH_COLLECTION:
                    if (!in_array($referType,[\Constants::TYPE_ORDER,\Constants::TYPE_OPPORTUNITY])) break;
                    if ($referType == \Constants::TYPE_OPPORTUNITY)
                    {
                        $cashCollectionList = new CashCollectionList($clientId);
                        $cashCollectionList->setCashCollectionIds($referIds);
                        $cashCollectionList->setFields(['opportunity_id','cash_collection_id']);
                        $cashCollectionList->setEnableFlag(true);
                        $cashCollectionList->setSkipPermission(true);
                        $cashCollectionOpportunityInfo = $cashCollectionList->find();

                        $opportunityIds = array_filter(array_column($cashCollectionOpportunityInfo,'opportunity_id'),function ($v){
                            return !empty($v) ;
                        });
                        $cashCollectionOpportunityMap = array_filter(array_column($cashCollectionOpportunityInfo,'opportunity_id','cash_collection_id'),function ($v)
                        {
                            return !empty($v);
                        });

                        $opportunityList = new OpportunityList($clientId);
                        $opportunityList->setOpportunityIds($opportunityIds);
                        $opportunityList->setSkipPermissionCheck(true);
                        $opportunityList->setEnableFlag(true);
                        $opportunityList->setFields(['opportunity_id','external_field_data','amount','amount_rmb','amount_usd']);
                        $opportunityInfo = $opportunityList->find();
                        $opportunityInfo = array_column($opportunityInfo,null,'opportunity_id');

                        foreach ($cashCollectionOpportunityMap as  $cashCollectionId => $opportunityId)
                        {
                            $fields[$cashCollectionId]['opportunity'] = $opportunityInfo[$opportunityId] ?? [];
                        }

                    } elseif ($referType == \Constants::TYPE_ORDER) {

                        $cashCollectionList = new CashCollectionList($clientId);
                        $cashCollectionList->setCashCollectionIds($referIds);
                        $cashCollectionList->setFields(['order_id','cash_collection_id']);
                        $cashCollectionList->setEnableFlag(true);
                        $cashCollectionList->setSkipPermission(true);
                        $cashCollectionOrderInfo = $cashCollectionList->find();

                        $orderIds = array_filter(array_column($cashCollectionOrderInfo,'order_id'),function ($v){
                            return !empty($v) ;
                        });
                        $cashCollectionOrderMap = array_filter(array_column($cashCollectionOrderInfo,'order_id','cash_collection_id'),function ($v)
                        {
                            return !empty($v);
                        });

                        $orderList = new OrderList($userId);
                        $orderList->setOrderIds($orderIds);
                        $orderList->setSkipPermissionCheck(true);
                        $orderList->setEnableFlag(true);
                        $orderList->setFields(['order_id','cost_list','external_field_data','amount','amount_rmb','amount_usd','product_total_amount','product_total_amount_usd','product_total_amount_rmb','cost_with_tax_total',
                            'addition_cost_amount','order_gross_margin','order_gross_margin_cny','order_gross_margin_usd','product_total_count']);
                        $orderInfo= $orderList->find();

                        $orderInfo = array_column($orderInfo,null,'order_id');

                        if (array_intersect($calculateFields,self::ORDER_HAS_RELATE_INFO_MAP) !== [])
                        {
                            // 回款单关联的订单的已回款金额
                            $collectInfo =  self::getPerformanceOrderProfitByOrderIds($clientId,$orderIds);
                        }

                        foreach ($cashCollectionOrderMap as  $cashCollectionId => $orderId)
                        {
                            $fields[$cashCollectionId]['order'] = $orderInfo[$orderId] ?? [];
                            $fields[$cashCollectionId]['order']['specialInfo'] = $collectInfo[$orderId] ?? [];
                        }

                    }else{
                        $fields = [];
                    }
                    break;
                default:
                    $fields = [];
            }
        }
        return $fields;
    }

    public static function getRelateInfoMapByTargetField($clientId, $referTYpe, $targetField, $specialReferIds = [],$formula = [])
    {
        $referInfo = [];
        if (empty($specialReferIds)) return [];
        switch ($referTYpe)
        {
            case \Constants::TYPE_ORDER:
                // 公式字段也会需要查询该字段
                if (in_array($targetField,self::ORDER_HAS_RELATE_INFO_MAP) || array_intersect(self::ORDER_HAS_RELATE_INFO_MAP,$formula['calculate_fields'] ?? []) !== [])
                {
                    $referInfo = self::getPerformanceOrderProfitByOrderIds($clientId,$specialReferIds);
                }
                break;
            case \Constants::TYPE_EDM:
                /**  edm已经维护reply_count 因此不需要特殊查询 **/
//                if ($targetField == 'reply_count' || in_array('21.reply_count',$formula['calculate_fields'] ?? [] ))
//                {
//                    $db = \ProjectActiveRecord::getDbByClientId($clientId);
//                    ProjectActiveRecord::setConnection($db);
//                    $statisticReplyCountSql = "SELECT task_id,count(1) as reply_count FROM tbl_group_mail WHERE client_id={$clientId} and task_id in (" . implode(',', $specialReferIds) . ") and reply_mail_id > 0 group by task_id ";
//
//                    $referInfo = array_column($db->createCommand($statisticReplyCountSql)->queryAll(), null, 'task_id');
//                }
                break;
            case \Constants::TYPE_WORK_JOURNAL:
                $templateList = new WorkJournalTemplateList($clientId);
                $templateList->setTemplateId($specialReferIds);
                $referInfo = array_column($templateList->find(),null,'template_id');
                break;
            case \Constants::TYPE_CASH_COLLECTION:
                $referInfo = self::getCashCollectionValueByCollectionIds($clientId,$specialReferIds,$formula);
                break;
        }
        return $referInfo;
    }

    public static function checkIsSpecialKey($referType,$targetField)
    {
        $map = [
            \Constants::TYPE_ORDER => self::ORDER_HAS_RELATE_INFO_MAP,
            // edm已经维护reply_count 因此不需要特殊查询
//            \Constants::TYPE_EDM => [
//                'reply_count'
//            ]
        ];
        return in_array($targetField,$map[$referType] ?? []) ;
    }

    public static function getAllUserIdsByReferInfoListAndTargetField($referInfoList,$referType,$performanceField)
    {
        $result = [];
        if ($referType != \Constants::TYPE_OPPORTUNITY)
        {

            $userIdArr =  array_filter(array_unique(array_column($referInfoList, $performanceField)));
            foreach ($userIdArr as $user){
                if (is_string($user)){
                    // {123123,456456}
                    $userId = PgsqlUtil::trimArray($user);
                    $userId = is_array($userId) ? $userId : [$userId];
                    $result = array_merge($result,$userId);
                } elseif (is_array($user)){
                    // array_merge
                    $result = array_merge($result,$user);
                }else{
                    $result[] = $user;
                }
            }
        }
        return $result;
    }


    /** 对指定client设定系统预设绩效（type = 7,8,9） 此方法不会重跑绩效 只返回rule_id
     * @param $clientId
     * @return mixed
     * @throws \ProcessException
     */
    public static function SetSystemPredeterminedRules($clientId)
    {

        $insertArr = [];
        $ruleIds = [];
        $db = PgActiveRecord::getDbByClientId($clientId);
        $typeArr = [PerformanceV2Constant::RULE_TYPE_COMPANY_COUNT,PerformanceV2Constant::RULE_TYPE_SEND_MAIL_COUNT];
        $sql = "select * from tbl_performance_v2_rule where client_id={$clientId} and type in (". implode(',',$typeArr) .")";
        $ruleMap = array_column($db->createCommand($sql)->queryAll(), null, 'type');


        // --------发送邮件数
        $param = [
            $ruleMap[PerformanceV2Constant::RULE_TYPE_SEND_MAIL_COUNT]['rule_id'] ?? \ProjectActiveRecord::produceAutoIncrementId(),
            "'发送邮件数'",
            "'当前发送的总邮件数'",
            $clientId,
            \Constants::TYPE_MAIL,
            "'{}'",
            "'send_time'",
            "'send_mail_count'",
            "'count'",
            "'sender'",
            PerformanceV2Constant::CRITERIA_TYPE_NULL,
            "''",
            PerformanceV2Constant::RULE_TYPE_SEND_MAIL_COUNT,
            PerformanceV2Constant::ENABLE_FLAG_TRUE,
            \common\models\client\PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
            "'{}'"
        ];
        $ruleIds[] = $param[0];
        $insertArr[] = "(" . implode(',', $param) . ")";

//        // --------添加跟进数
//        $param = [
//            $ruleMap[PerformanceV2Constant::RULE_TYPE_FOLLOW_UP_COUNT]['rule_id'] ?? \ProjectActiveRecord::produceAutoIncrementId(),
//            "'添加跟进数'",
//            "'当期新建的「线索、客户、商机」的跟进数'",
//            $clientId,
//            \Constants::TYPE_FOLLOWUP,
//            "'{}'",
//            "'create_time'",
//            "''",
//            "'count'",
//            "'create_user'",
//            PerformanceV2Constant::CRITERIA_TYPE_NULL,
//            "''",
//            PerformanceV2Constant::RULE_TYPE_FOLLOW_UP_COUNT,
//            PerformanceV2Constant::ENABLE_FLAG_TRUE,
//            \common\models\client\PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
//            "'{}'"
//        ];
//        $ruleIds[] = $param[0];
//        $insertArr[] = "(" . implode(',', $param) . ")";

        // --------新建客户数
        $param = [
            $ruleMap[PerformanceV2Constant::RULE_TYPE_COMPANY_COUNT]['rule_id'] ?? \ProjectActiveRecord::produceAutoIncrementId(),
            "'新建客户数'",
            "'当期新建的客户数'",
            $clientId,
            \Constants::TYPE_COMPANY,
            "'{}'",
            "'archive_time'",
            "''",
            "'count'",
            "'create_user'",
            PerformanceV2Constant::CRITERIA_TYPE_NULL,
            "''",
            PerformanceV2Constant::RULE_TYPE_COMPANY_COUNT,
            PerformanceV2Constant::ENABLE_FLAG_TRUE,
            \common\models\client\PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
            "'{}'",
        ];

        $ruleIds[] = $param[0];
        $insertArr[] = "(" . implode(',', $param) . ")";
        $sql = "insert into tbl_performance_v2_rule (rule_id, name, description, client_id, refer_type, filters, time_field, target_field, calculate_rule, performance_field, criteria_type, criteria, type, enable_flag,performance_type,formula) values " . implode(',', $insertArr) . " on conflict(rule_id) do update set name=EXCLUDED.name, description=EXCLUDED.description, refer_type=EXCLUDED.refer_type, filters=EXCLUDED.filters, time_field=EXCLUDED.time_field, target_field=EXCLUDED.target_field, calculate_rule=EXCLUDED.calculate_rule, performance_field=EXCLUDED.performance_field, criteria_type=EXCLUDED.criteria_type, criteria=EXCLUDED.criteria, type=EXCLUDED.type, enable_flag=EXCLUDED.enable_flag ";
        $db->createCommand($sql)->execute();

        $existRuleIds = array_column($ruleMap,'rule_id');
        $addIds = array_diff($ruleIds,$existRuleIds);

        // 开启发送邮件白名单
        Helper::setPerformanceSwitchCacheByReferType($clientId, \Constants::TYPE_MAIL, 1, \Mail::MAIL_TYPE_SEND);

        \LogUtil::info(sprintf("SetSystemPredeterminedRules clientId:{$clientId} complete! addRuleIds[%s] existRuleIds[%s]", implode(',', $addIds), implode(',', $existRuleIds)));

        return $ruleIds;
    }

    /**
     * 根据查询条件获取每个rule_id对应的去重refer_id列表
     * time: 8:11 PM
     * user: huagongzi
     * @return mixed
     */
    public static function getDistinctRuleReferIdsMap(int $clientId,int $userId, array $paramsArr = []){
        $list = self::getRuleDistinctReferIds($clientId,$userId,$paramsArr);
        $data = [];

        foreach ($list as $item){
            $data[$item['rule_id']][] = $item['refer_id'];
        }
        foreach ($data as $ruleId => $datum) {
            $data[$ruleId] = array_unique($datum);
        }
        return $data;
    }

    public static function getRuleDistinctReferIds(int $clientId,int $userId, array $paramsArr = [],string $distinctFields = '( rule_id,refer_id) client_id,rule_id,refer_id',string $orderBy = ''){
        $recordList = new PerformanceV2RecordList($clientId, $userId);
        $db         = PgActiveRecord::getDbByClientId($clientId);
        $tableName  = $recordList->getModel()->tableName();

        foreach ($paramsArr as $key => $value){
            $methodName = 'set' . ucfirst(strtolower($key));

            if(method_exists($recordList, $methodName) && !empty($value)){
                $recordList->$methodName($value);
            }
        }

        list($where, $params) = $recordList->buildParams();

        $sql = "
            SELECT DISTINCT ON
                {$distinctFields}
            FROM
                {$tableName}
            WHERE
                {$where}
        ";

        if($orderBy){
            $sql .= " {$orderBy}";
        }

        $list = $db->createCommand($sql)->queryAll(true, $params);

        return $list;
    }


    /** 获取绩效设置缓存的key
     * @param $referType
     * @param $targetFieldType // 邮件收发件类型
     * @return string
     */
    public static function getPerformanceSwitchCacheKey(int $referType, $referSubType = null)
    {
        $referTypeToStringMap = [
            \Constants::TYPE_MAIL => 'mail'
        ];
        if (!isset($referTypeToStringMap[$referType]) || is_array($referSubType)) return '';

        return PerformanceV2Constant::PERFORMANCE_SWITCH_LIST_PREFIX . $referTypeToStringMap[$referType] . ($referSubType === null ? '' : (":" . $referSubType));
    }

    /**
     *  对应对象白名单开关设置 注意这个方法会对绩效是否运行产生影响
     * @param $clientId
     * @param $referType
     * @param $value
     * @param null $referSubType
     * @return void
     */
    public static function setPerformanceSwitchCacheByReferType($clientId, $referType, $value, $referSubType = null)
    {
        $value = boolval($value);
        // 新建且开启
        $key = self::getPerformanceSwitchCacheKey($referType, $referSubType);

        if (empty($key)) return;
        $redis = \RedisService::sf();
        $redis->hset($key, $clientId, $value);
    }

    /** 获取referInfo中的externalFieldData
     * @param $referInfo
     * @param $referType
     * @return array|mixed
     */
    public static function getExternalFieldDataByReferInfo($referInfo,$referType)
    {
        $externalFieldData = isset($referInfo['external_field_data']) ? (is_array($referInfo['external_field_data']) ? $referInfo['external_field_data'] : json_decode($referInfo['external_field_data'], true) ) : [];
        if ($referType == \Constants::TYPE_ORDER)
        {
            //订单费用特殊处理
            $costList = json_decode($referInfo['cost_list'] ?? [], true);
            foreach ($costList as $cost)
            {
                if (!isset($cost['cost_item_relation_id'])) continue;
                // cost_list 为特殊的external_field_data 他的id也是数字类型 在后续的公式中不好区分 因此在这里加cost_list 作为区分
                $costKey = 'cost_list.' . $cost['cost_item_relation_id'];
                if (!isset($externalFieldData[$costKey])) {
                    $externalFieldData[$costKey] = 0;
                }
                if (is_numeric($cost['cost'] ?? 0)) {
                    $externalFieldData[$costKey] += $cost['cost'] ?? 0;
                }
            }
        }
        return $externalFieldData;
    }

    /**
     * 获取写入绩效表的map
     * @param $commonRecord
     * @param $recordType
     * @param $referInfo
     * @param $relateInfo
     * @param $rule
     * @param $belongsTo
     * @param $rate
     * @param array $extraData
     * @return mixed
     */
    public static function getPerformanceRecordMapByRecordType($commonRecord, $recordType, $referInfo, $relateInfo, $rule, $belongsTo, $rate, array $extraData = [])
    {
        $targetField = $rule['target_field'];
        $clientId = $rule['client_id'];
        $referTYpe = $commonRecord['refer_type'];
        $performanceRecord = $commonRecord;
        $performanceRecord['record_type'] = $recordType;
        $rate = floatval($rate);
        $originAmount = 0;
        $externalFieldData = Helper::getExternalFieldDataByReferInfo($referInfo,$referTYpe);
        $formula = json_decode($rule['formula'] ?? '{}',true) ?? [];
        $exchangeRate = $extraData['exchangeRate'] ?? [];
        $mainCurrency = $extraData['mainCurrency'];
        if (empty($mainCurrency))
        {
            \Util::logInfo('-----getMainCurrencyEmpty-----!',[
                'clientId' => $rule['client_id'],
                'rule_id' => $rule['rule_id'],
            ]);

            $mainCurrency = Client::getClient($clientId)->getMainCurrency();
        }


        // 是否是特殊key 特殊key需要去找specialInfo
        $isSpecialKey = Helper::checkIsSpecialKey($referTYpe,$targetField);

        // 根据计算方式计算值
        if ($rule['calculate_rule'] == PerformanceV2Constant::CALCULATE_RULE_COUNT) {
            $performanceRecord['indicator_value'] = 1;
            $performanceRecord['owner_id'] = $belongsTo;
        } elseif ($rule['calculate_rule'] == PerformanceV2Constant::CALCULATE_RULE_SUM) {
            if (in_array($targetField, ['amount_rmb', 'amount_usd'])) {
                $amount = $referInfo[$targetField];
            } else if ($isSpecialKey && empty($formula)) {
                $amount = $referInfo['specialInfo'][$targetField] ?? 0;
            } else {
                if (!empty($formula)) {
                    if (in_array($referTYpe, [\Constants::TYPE_EDM, \Constants::TYPE_OPPORTUNITY, \Constants::TYPE_ORDER, \Constants::TYPE_CASH_COLLECTION])) {
                        $originAmount = Helper::getValueByPerformanceFormula($referTYpe, $referInfo, $formula, $relateInfo,$mainCurrency);
                    }
                } else if ($referTYpe == \Constants::TYPE_WORK_JOURNAL) {
                    $performanceListData = json_decode($referInfo['performance_list_data'],true);
                    $originAmount = $performanceListData[$rule['rule_id']];
                } else if (is_numeric($targetField)) {
                    $originAmount = $externalFieldData[$targetField] ?? 0;
                    $originAmount = empty($originAmount) ? 0 : $originAmount;
                } else if ($referTYpe == \Constants::TYPE_ORDER && self::checkNeedToAssociatedWithExchangeRate($referTYpe,$targetField))
                {
                    // 订单汇率
                    $orderExchangeRate = $mainCurrency == 'CNY' ? $referInfo['exchange_rate'] : $referInfo['exchange_rate_usd'];

                    $targetFieldStrArr = explode('.',$targetField);

                    $originAmount = ( ($targetFieldStrArr[0] ?? '') == 'cost_list') ? ( ($externalFieldData[$targetField] ?? 0) * $orderExchangeRate / 100 ) : (($referInfo[$targetField] ?? 0) * $orderExchangeRate) / 100;
                }  else {
                    if ($targetField != '{}') {
                        // 走到这里还没有满足条件的就是非法规则 需要让用户重新配置
                        $originAmount = !empty($targetField) ? $referInfo[$targetField] : 0;
                    }
                }
                $originAmount = floatval($originAmount);
                $amount = $originAmount * $exchangeRate / 100;
            }
            $performanceRecord['indicator_value'] = $amount * $rate / 100;
            $performanceRecord['owner_id'] = $belongsTo;
        }

        return $performanceRecord;
    }


    /**
     * 判断该targetField是否与主币种相关联
     * @param $referTYpe
     * @param $targetField
     * @return bool
     */
    public static function checkNeedToAssociatedWithExchangeRate($referTYpe,$targetField)
    {
        $needToAssociatedWithMainCurrencyMap = [
            \Constants::TYPE_ORDER => [
                'addition_cost_amount',
                'cost_with_tax_total'
            ]
        ];

        $stringArr = explode('.',$targetField);
        if (($stringArr[0] ?? '') == 'cost_list')
        {
            return true;
        }
        return in_array($targetField,$needToAssociatedWithMainCurrencyMap[$referTYpe] ?? []);

    }

    public static function getCurrencyTypeByReferTypeAndTargetField($referType,$targetField)
    {
        if ( in_array($targetField,PerformanceV2Constant::TARGET_FIELD_CURRENCY_UNIT_MAP[$referType] ?? []))
        {
            $targetFieldStrArr = explode('_',$targetField);
            $currency = end($targetFieldStrArr);

            if ($targetField == 'order_gross_margin_cny') {
                $currency = 'rmb';
            }
            return $currency;
        }
        if (Helper::checkNeedToAssociatedWithExchangeRate($referType,$targetField)) return 'main_currency';
        return '';
    }


    //todo 【未分配】和 【我的企业】特殊处理，后续得优化部门选择器
    public static function specialReferScopeHandle($clientId, $userId, $referId, $scope)
    {
        if ($referId == \common\library\department\Department::UNDISTRIBUTED_NODE_ID && $scope == PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP) {
            $referId = \common\library\account\Helper::getUndistributedUserIds($clientId);
            $scope = PerformanceV2Constant::SETTING_GOAL_SCOPE_USER;
        } else if ($referId == 0 && $scope == PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP) {
            $manageDepartmentIds = (new DepartmentPermission($clientId, $userId))->permission(PrivilegeConstants::PRIVILEGE_CRM_PERFORMANCE_VIEW)->departmentIds();
            if (empty($manageDepartmentIds)) {
                $referId = $userId;
                $scope = PerformanceV2Constant::SETTING_GOAL_SCOPE_USER;
            }
        }
        return [$referId, $scope];
    }

    public static function splitPerformanceRule(array $ruleInfo)
    {
        if (empty($ruleInfo) || empty($ruleInfo['refer_type'] ?? 0) || empty($ruleInfo['client_id'] ?? 0)) return [];

        $referType = $ruleInfo['refer_type'];
        $clientId = $ruleInfo['client_id'];
        $splitRuleList = [];
        switch ($referType)
        {
            case Constants::TYPE_MAIL:
                $userList = new \common\library\account\UserList();
                $userList->setClientId($clientId);
                $userList->setEnableFlag(true);
                $userList->find();
                $userIds = array_column($userList->find(), 'user_id');
                if (empty($userIds)) break;

                $mailType = PerformanceV2Constant::MAIL_TARGET_FIELD_MAP[$ruleInfo['target_field']] ?? null;
                if (empty($mailType)) break;
                $systemFolderIds = ($mailType == \Mail::MAIL_TYPE_SEND) ? [2, 5, 6, 9] : [1, 5, 6, 9];

                foreach ($userIds as $userId)
                {
                    $mailFolderList = new MailFolderList($userId);
                    $mailFolderList->setFields('folder_id');
                    $userFolderIds = array_column($mailFolderList->find(), 'folder_id');
                    $allFolderIds = array_merge($userFolderIds, $systemFolderIds);

                    foreach ($allFolderIds as $folderId)
                    {
                        $subRuleInfo = $ruleInfo;
                        $subRuleInfo['extra_filters'] = [
                            [
                                'field' => 'user_id',
                                'refer_type' => 6,
                                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                                'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                                'value' => $userId,
                            ],
                            [
                                'field' => 'folder_id',
                                'refer_type' => 6,
                                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                                'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                                'value' => $folderId,
                            ],
                        ];
                        $splitRuleList[] = $subRuleInfo;
                    }
                }
                break;
            default:
                $splitRuleList = [$ruleInfo];
                break;
        }
        \LogUtil::info(sprintf("clientId[{$ruleInfo['client_id']}] ruleId[{$ruleInfo['rule_id']}] ruleName[{$ruleInfo['name']}] referType[{$ruleInfo['refer_type']}] 拆分[%s]个子规则", count($splitRuleList)));
        return $splitRuleList;
    }

    /**
     * @param $clientId
     * @param $names
     * @return bool
     * @throws \ProcessException
     */
    public static function performanceNameConflictCheck($clientId,$names,$excludeIds = [])
    {
        if (empty($names))
        {
            return true;
        }
        $performanceList = new PerformanceV2RuleList($clientId,null);
        $performanceList->setName($names);
        $performanceList->setDeleteFlag(PerformanceV2Constant::DELETE_FLAG_FALSE);
        $performanceList->setExcludeRuleIds($excludeIds);
        $count = $performanceList->count();
        return boolval($count);
    }


    public static function getPerformanceOrderProfitByOrderIds($clientId, $orderIds = [])
    {
        if (empty($orderIds))
        {
            return [];
        }
        $db = PgActiveRecord::getDbByClientId($clientId);

        $sql = "select * from tbl_order_profit where client_id = {$clientId} and order_id in (" . implode(',', $orderIds) . ")";

        $orderList = array_column($db->createCommand($sql)->queryAll(), null, 'order_id');

        return self::formatPerformanceOrderProfitList($orderList);
    }


    public static function formatPerformanceOrderProfitList($list)
    {
        if (empty($list))
        {
            return [];
        }
        foreach ($list as $key => $item)
        {
            $list[$key]['collect_amount_rmb'] = $item['cash_collection_collect_amount_rmb'] ?? 0;
            $list[$key]['collect_amount_usd'] = $item['cash_collection_collect_amount_usd'] ?? 0;
        }
        return $list;
    }

    /**
     * 判断日期是否属于某一个周期
     * @param $start
     * @param $end
     * @return string
     * @throws \Exception
     */
    public static function checkRangeTimeType($clientId, $start, $end)
    {
        // 获取用户设定的财年起始月
        $client     = \common\library\account\Client::getClient($clientId);
        $values     = $client->getSettingAttributes([Client::SETTING_KEY_PDCA_START_MONTH]);
        $quarterStartMonth = $values['setting_key_pdca_start_month'] ?? null;

        // 年度
        $year = date('Y', strtotime($start));
        // 年度开始时间
        $yearStart = date('Y-m-d', strtotime($year.'-'.$quarterStartMonth.'-01'));
        // 年度结束时间应该是最后一天 而不应该包含第二年的第一天
        $yearEnd = date('Y-m-d', strtotime($yearStart.'+ 1 years -1days'));
        if ($start == $yearStart && $end == $yearEnd)
        {
            return PerformanceV2Constant::TIME_TYPE_THIS_YEAR;
        }

        // 月份
        $startMonth = date('m', strtotime($start));
        $endMonth = date('m', strtotime($end));
        $startDay = date('j', strtotime($start));
        $endDay = date('j', strtotime($end));
        if ($startDay === '1' && $startMonth === $endMonth && ($endDay === date('t', strtotime($end)))) {
            return PerformanceV2Constant::TIME_TYPE_THIS_MONTH;
        }


        // 季度判断
        $month = date('n', strtotime($start));
        $quarter = ceil(($month - $quarterStartMonth + 1) / 3);
        $year = date('Y', strtotime($start));
        // 获取到这个季度到开始时间和结束时间
        $quarterStart = date('Y-m-d', strtotime($year.'-'.(($quarter - 1) * 3 + $quarterStartMonth).'-01'));
        $quarterEnd = date('Y-m-t', strtotime($quarterStart.'+ 2 months'));

        if ($start == $quarterStart && $end == $quarterEnd)
        {
            return PerformanceV2Constant::TIME_TYPE_THIS_QUARTER;
        }

        // 周判断
        $weekStart = date('D', strtotime($start));
        $weekEnd   = date('D', strtotime($end));

        $diffDays = floor((strtotime($end) - strtotime($start)) / (60 * 60 * 24));


        // 相差6天才算一周
        if (($weekStart == 'Mon' && $weekEnd == 'Sun' ) && $diffDays == 6)
        {
            return PerformanceV2Constant::TIME_TYPE_THIS_WEEK;
        }

        // 日判断
        if ($start == $end) {
            return PerformanceV2Constant::TIME_TYPE_THIS_DAY;
        }

        return PerformanceV2Constant::TIME_TYPE_RANGE;
    }

    /**
     * 暂时想不到啥好名了。这个方法的职责是。对需要根据规则信息，额外填充符合条件的记录的场景做处理
     * 目前的场景有
     * 客户 => 最近赢单商机，（每次成交日期 --指定为赢单商机） 一个客户可以有多个赢单商机 但是赢单商机的时间可以变化 产品需求是对于客户关联的n个商机 只能有n个时间 用户来回改时间应该不计算在内。。ps(有点无语)
     * 客户 => 每次成交订单日期，（每次成交日期 --指定为满足成交订单金额绩效）一个客户可以有多个满足绩效的订单，计入时间根据订单id去重
     *
     * 工单链接 https://www.tapd.cn/21404721/prong/stories/view/1121404721001047444
     * 需求连接 https://www.tapd.cn/21404721/prong/stories/view/1121404721001047444
     *
     * @param $referInfoList
     * @param $ruleInfo
     * @return array|mixed
     * @throws \ProcessException
     */
    public static function processReferListByRuleInfo(array $referInfoList, array $ruleInfo)
    {
        // 这里如果有需要,可以单独抽个$retReferInfoList变量用$referInfoList赋值给它,这样可以不用污染$referInfoList;
        if (empty($referInfoList) || empty($ruleInfo['client_id']) || empty($ruleInfo['refer_type'])) {
            return $referInfoList;
        };

        $referType = $ruleInfo['refer_type'];
        $clientId = $ruleInfo['client_id'];
        $timeField = $ruleInfo['time_field'];
        $performanceField = $ruleInfo['performance_field'];
        $pgDb = PgActiveRecord::getDbByClientId($clientId);
        // 正常来说$pgDb可以获取$mysqlDb也可以获取
        $mysqlDb = \ProjectActiveRecord::getDbByClientId($clientId);
        if (empty($pgDb)) {
            return $referInfoList;
        }

        if ($referType == \Constants::TYPE_MAIL) {
            $targetField = $ruleInfo['target_field'];
            // 对方回复数计算
            if ($targetField == 'reply_from_mail_count') {
                $mailIds = array_column($referInfoList, 'mail_id');
                $mailIdsStr = implode(',', $mailIds);
                $mailTrackSql = "select reply_mail_id from tbl_mail_track where client_id={$clientId} and reply_mail_id in ({$mailIdsStr})";
                $hasTrackMailIds = $mysqlDb->createCommand($mailTrackSql)->queryColumn();
                if (empty($hasTrackMailIds)) {
                    return [];
                }
                $tempReferInfoList = [];
                foreach ($referInfoList as $item) {
                    if (in_array($item['mail_id'], $hasTrackMailIds)) {
                        $tempReferInfoList[] = $item;
                    }
                }
                $referInfoList = $tempReferInfoList;
            }
        }

        if ($referType == \Constants::TYPE_SALE_OUTBOUND_INVOICE) {
            foreach ($referInfoList as $index => $item) {
                $referInfoList[$index]['company_id'] = $item['refer_id'] ?? 0;
            }

            $targetField = $ruleInfo['target_field'];
            $targetFieldMap = [
                'product_amount_rmb' => 'product_total_amount_rmb',
                'product_amount_usd' => 'product_total_amount_usd',
            ];

            if (!empty($targetField)) {
                foreach ($referInfoList as $index => $item) {
                    $referInfoList[$index][$targetField] = $item[$targetFieldMap[$targetField]] ?? 0;
                }
            }

            if ($performanceField == 'users') {
                // 被考核角色字段是关联订单业务归属人，需要统计销售出库明细表
                $outboundInvoiceIds = array_column($referInfoList, 'outbound_invoice_id');
                if (empty($outboundInvoiceIds)) {
                    return $referInfoList;
                }

                $outboundInvoiceIdsStr = implode(',', $outboundInvoiceIds);
                $outboundInvoiceOrderSql = "SELECT a.create_user, a.outbound_invoice_id, a.product_amount, a.product_amount_rmb, a.product_amount_usd,a.order_id, b.users, b.departments FROM tbl_outbound_record as a left join tbl_order as b on a.order_id=b.order_id WHERE a.client_id={$clientId} AND a.client_id=b.client_id AND a.outbound_invoice_id IN ({$outboundInvoiceIdsStr}) AND a.order_id>0 and a.delete_flag=0";
                $distinctPerformanceRecordList = $pgDb->createCommand($outboundInvoiceOrderSql)->queryAll();
                $tempReferInfoList = \ArrayUtil::index($referInfoList, 'outbound_invoice_id');

                foreach ($distinctPerformanceRecordList as $item) {
                    if ($tempReferInfoList[$item['outbound_invoice_id']]['product_total_amount'] != 0) {
                        $currentRate = round($item['product_amount'] / $tempReferInfoList[$item['outbound_invoice_id']]['product_total_amount'], 4) * 100;
                    } else {
                        $currentRate = 0;
                    }

                    $users = json_decode($item['users'], true);
                    foreach ($users as $index => $rate) {
                        $users[$index]['rate'] = $currentRate * ($rate['rate'] / 100);
                    }
                    $tempReferInfoList[$item['outbound_invoice_id']]['users'] = array_merge($tempReferInfoList[$item['outbound_invoice_id']]['users'] ?? [], $users);

                    $departments = json_decode($item['departments'], true);
                    foreach ($departments as $index => $rate) {
                        $departments[$index]['rate'] = $currentRate * ($rate['rate'] / 100);
                    }
                    $tempReferInfoList[$item['outbound_invoice_id']]['departments'] = array_merge($tempReferInfoList[$item['outbound_invoice_id']]['departments'] ?? [], $departments);
                }

                foreach ($tempReferInfoList as $index => $item) {
                    $users = [];
                    $usersRate = 0;
                    $item['users'] = $item['users'] ?? [];
                    $item['users'] = !is_array($item['users']) ? json_decode($item['users'], true) : $item['users'];
                    foreach ($item['users'] as $user) {
                        if (isset($users[$user['user_id']])) {
                            $users[$user['user_id']] += $user['rate'];
                        } else {
                            $users[$user['user_id']] = $user['rate'];
                        }
                        $usersRate += $user['rate'];
                    }
                    $newUsers = [];
                    foreach ($users as $userId => $itemRate) {
                        $newUsers[] = [
                            'rate' => !empty($usersRate) ? round(($itemRate / $usersRate) * 100, 4) : 0,
                            'user_id' => $userId,
                        ];
                    }
                    $tempReferInfoList[$index]['users'] = json_encode($newUsers);

                    $departments = [];
                    $departmentsRate = 0;
                    $item['departments'] = $item['departments'] ?? [];
                    $item['departments'] = !is_array($item['departments']) ? json_decode($item['departments'], true) : $item['departments'];
                    foreach ($item['departments'] as $department) {
                        if (isset($departments[$department['department_id']])) {
                            $departments[$department['department_id']] += $department['rate'];
                        } else {
                            $departments[$department['department_id']] = $department['rate'];
                        }
                        $departmentsRate += $department['rate'];
                    }
                    $newDepartments = [];
                    foreach ($departments as $departmentId => $itemRate) {
                        $newDepartments[] = [
                            'rate' => !empty($departmentsRate) ? round(($itemRate / $departmentsRate) * 100, 4) : 0,
                            'department_id' => $departmentId,
                        ];
                    }

                    $tempReferInfoList[$index]['departments'] = json_encode($newDepartments);
                }

                $referInfoList = array_values($tempReferInfoList);
            }
        }

        // 计算类型，按照特殊类型进行filter referInfoList
        $calculateType = '';

        if ($referType == \Constants::TYPE_COMPANY)
        {
            // dealSetting可以在客户设置页面配置 默认为赢单商机
            $dealSetting = \CustomerOptionService::getDealSetting($clientId);
            $companyIds = array_column($referInfoList, 'company_id');
            $assocCompanyInfoMap = array_column($referInfoList, null, 'company_id');

            if (empty($companyIds)) {
                return $referInfoList;
            }
            $companyIdsStr = implode(',', $companyIds);

            // 每次赢单商机金额 和每次成交金额字段维护规则 = 每次赢单商机时 计算方式一致
            if ($timeField == 'latest_success_opportunity_time'
                || ($timeField == 'deal_time' && $dealSetting == \Constants::TYPE_OPPORTUNITY))
            {
                $calculateType = 'win_opportunity';
            }

            // 每次成交订单金额即是动态的成交订单金额字段
            if ($timeField == 'every_transaction_order_time'
                || ($timeField == 'deal_time' && $dealSetting == \Constants::TYPE_ORDER))
            {
                $calculateType = 'deal_order';
            }

            // 处理客户关联赢单商机的所有记录
            $distinctPerformanceRecordList = [];
            switch ($calculateType)
            {
                case 'win_opportunity':
                    $selectSuccessOpportunitySql = "select company_id,account_date from tbl_opportunity where client_id = {$clientId} and company_id in ($companyIdsStr) and enable_flag = 1 and stage_type = 2 group by company_id,account_date";
                    $distinctPerformanceRecordList = $pgDb->createCommand($selectSuccessOpportunitySql)->queryAll();
                    break;
                case 'deal_order':
                    $type = PerformanceV2Constant::RULE_TYPE_ORDER_AMOUNT;
                    $dealOrderSql = "select company_id, account_date
                                    from tbl_performance_v2_record
                                    where client_id = {$clientId}
                                        and company_id in ({$companyIdsStr})
                                      and rule_id = (
                                      select rule_id from tbl_performance_v2_rule where tbl_performance_v2_rule.client_id = {$clientId} and type = {$type} LIMIT 1
                                      ) group by company_id, account_date";
                    $distinctPerformanceRecordList  = $pgDb->createCommand($dealOrderSql)->queryAll();
                    break;
                default:
                    return $referInfoList;
            }

            // 处理组装的逻辑
            $companyIdToAccountDateMap = [];
            foreach ($distinctPerformanceRecordList as $item)
            {
                $itemCompanyId = $item['company_id'];
                $itemAccountDate = $item['account_date'];
                if (!isset($companyIdToAccountDateMap[$itemCompanyId])) {
                    $companyIdToAccountDateMap[$itemCompanyId] = [];
                }
                $companyIdToAccountDateMap[$itemCompanyId][] = $itemAccountDate;
            }

            $retReferInfoList = [];
            foreach ($companyIds as $itemCompanyId)
            {
                $originCompanyInfo = $assocCompanyInfoMap[$itemCompanyId] ?? [];
                if (empty($originCompanyInfo)) continue;
                $companyAccountDateArr = $companyIdToAccountDateMap[$itemCompanyId] ?? [];
                foreach ($companyAccountDateArr as $accountDate)
                {
                    $temp = $originCompanyInfo;
                    switch ($timeField) {
                        // 每次成交订单金额需要转换一下 需要转换的time_field请参考 self::transferTimeField 方法
                        case "every_transaction_order_time":
                            $temp['latest_transaction_order_time'] = $accountDate;
                            break;
                        default:
                            $temp[$timeField] = $accountDate;
                            break;
                    }
                    $retReferInfoList[] = $temp;
                }
            }
            $referInfoList = $retReferInfoList;
        }

        if ($referType == \Constants::TYPE_OPPORTUNITY) {
            // 商机最新一次的审批单若发现申请的是新建的引发的，状态在审批中3，撤回4，系统撤回5，拒绝2，草稿0都不记录绩效
            $opportunityIds = array_column($referInfoList, 'opportunity_id');
            $canPerformanceMap = \common\library\approval_flow\Helper::checkApprovalRunWorkflowAndPerformance($clientId, $opportunityIds, \Constants::TYPE_OPPORTUNITY);
            $opportunityIdUnset = [];
            foreach ($referInfoList as $key => $item) {
                if (($canPerformanceMap[$item['opportunity_id']] ?? null) === false) {
                    unset($referInfoList[$key]);
                    $opportunityIdUnset[] = $item['opportunity_id'];
                }
            }
            if (!empty($opportunityIdUnset)) {
                \LogUtil::info('checkApprovalRunWorkflow_performance', $opportunityIdUnset);
            }
            // 临时处理，赢单商机从赢单状态变为其他状态时，该时间没有被维护
            if ($timeField == 'succeed_time') {
                $tempReferInfoList = [];
                foreach ($referInfoList as $item) {
                    if ($item['stage_type'] != OpportunityStage::STAGE_WIN_STATUS) {
                        continue;
                    }
                    $tempReferInfoList[] = $item;
                }
                $referInfoList = $tempReferInfoList;
            }

            // 商机实际变更人处理
            // https://www.tapd.cn/21404721/prong/stories/view/1121404721001068905
            if ($performanceField == 'real_stage_change_user')
            {
                $opportunityIdsStr = implode(",",$opportunityIds);
                $opportunityIdToInfoMap = array_column($referInfoList, null,'opportunity_id');
                $params = [
                    ":client_id" => $clientId,
                ];
                $where = " client_id=:client_id  and action_flag =" . OpportunityStageStatisticsService::STAGE_CHANGE_ACTION_EDIT . " and opportunity_id in ({$opportunityIdsStr}) ";
                $table = \OpportunityStageStatisticsModel::getModelTableName();
                $sql = "select opportunity_id,user_id as real_stage_change_user,TO_CHAR(create_time, 'YYYY-MM-DD') as stage_edit_time
from {$table}
where {$where} ";
                $list = $pgDb->createCommand($sql)->queryAll(true, $params);

                $stageChangeList = [];
                // 同一天变更的只记录一次
                foreach ($list as $item) {
                    $filterOpportunityId = $item['opportunity_id'];
                    $filterUser = $item['real_stage_change_user'];
                    $filterDate = $item['stage_edit_time'];
                    if (empty($stageChangeList[$filterOpportunityId][$filterUser])) {
                        $stageChangeList[$filterOpportunityId][$filterUser][] = $filterDate;
                        continue;
                    }
                    if (!in_array($filterDate,$stageChangeList[$filterOpportunityId][$filterUser])) {
                        $stageChangeList[$filterOpportunityId][$filterUser][] = $filterDate;
                    }
                }


                $tmpList = [];
                foreach ($stageChangeList as $changeOpportunityId => $userToDateMap)
                {
                    $opportunityInfo = $opportunityIdToInfoMap[$changeOpportunityId] ?? [];
                    if (empty($opportunityInfo)) continue;
                    foreach ($userToDateMap as $userId => $dateArr)
                    {
                        foreach ($dateArr as $dateItem)
                        {
                            $changeItem = [
                                'real_stage_change_user' => $userId,
                                'stage_edit_time' => $dateItem
                            ];
                            $tmpList[] = array_merge($opportunityInfo, $changeItem);
                        }
                    }
                }
                $referInfoList = $tmpList;
            }
        }

        if ($referType == \Constants::TYPE_FOLLOWUP) {
            $trailIds = array_column($referInfoList, 'trail_id');
            if (!empty($trailIds)) {
                $trailIds = implode(',', $trailIds);
                $sql = "select trail_id, company_id from tbl_dynamic_trail where trail_id in ({$trailIds}) and client_id = {$clientId}";
                $trailList = $pgDb->createCommand($sql)->queryAll();

                $trail2CompanyMap = array_column($trailList, 'company_id', 'trail_id');
                foreach ($referInfoList as $index => $item) {
                    $trailId = $item['trail_id'];
                    $referInfoList[$index]['company_id'] = $trail2CompanyMap[$trailId] ?? 0;
                }
            }
        }

        $targetField = $ruleInfo['target_field'];
        $objectName = \common\library\object\object_define\Constant::OBJ_MAP[$referType] ?? null;
        if (!empty($objectName) && !empty($targetField)) {
            //获取功能字段在宽表的映射关系, system_type=2是自定义字段，系统字段还是主表数据
            $sql = "select columns from tbl_field where client_id={$clientId} and object_name='{$objectName}' and field='{$targetField}' and function_type in (" . implode(',', [\common\library\object\field\FieldConstant::FUNCTION_TYPE_FORMULA, \common\library\object\field\FieldConstant::FUNCTION_TYPE_CALCULATE]) . ') and system_type = 2';
            $fieldInfo = $pgDb->createCommand($sql)->queryAll();
            if (!empty($fieldInfo)) {
                $primaryKey = \common\library\object\object_define\Constant::OBJ_PRIMARY_KEY_MAP[$objectName];
                $referIds = array_column($referInfoList, $primaryKey);
                $columns = json_decode($fieldInfo[0]['columns'], true);
                $dataKey = $columns['data_key'];
                $sql = "select id,{$dataKey} from tbl_extend where client_id={$clientId} and object_name='{$objectName}' and id in (" . implode(',', $referIds) . ")";
                $referExtendData = $pgDb->createCommand($sql)->queryAll();
                $referExtendData = array_column($referExtendData, $dataKey, 'id');
                //将宽表数据补充到$referInfoList
                foreach ($referInfoList as &$referItem) {
                    $referItem['external_field_data'] = is_array($referItem['external_field_data']) ? $referItem['external_field_data'] : json_decode($referItem['external_field_data'], true);
                    $referItem['external_field_data'][$targetField] = $referExtendData[$referItem[$primaryKey]] ?? 0;
                }
            }
        }

        return $referInfoList;
    }




    public static function getBatchRecordPerformances($referTYpe, $referInfoList, $rule, $extraData = [])
    {
        $userPerformanceRecords = [];
        $departmentPerformanceRecords = [];
        if (empty($referInfoList)) return [$userPerformanceRecords, $departmentPerformanceRecords];

        $specialKeys = $extraData['specialKeys'] ?? [];
        $extraData['mainCurrency'] = empty($extraData['mainCurrency']) ? $extraData['mainCurrency'] :  Client::getClient($rule['client_id'])->getMainCurrency();

        // 初始化公共参数
        $specialReferIds = (isset($specialKeys['refer_id_key']) && $specialKeys['refer_id_key'] != '') ? array_column($referInfoList,$specialKeys['refer_id_key']) : 0;
        $targetField = $rule['target_field']; // 考核指标字段

        $performanceField = $rule['performance_field']; // 被考核角色字段
        // 转换performanceField => e.g 邮件中的sender和receiver同时取user_id
        $performanceField = Helper::transferPerformanceField($referTYpe,$performanceField);

        $timeField = $rule['time_field']; // 计入时间依据字段
        $now = date('Y-m-d H:i:s');
        $formula = json_decode($rule['formula'],true) ?? [];
        $clientId = $rule['client_id'];

        $relateInfo = [];
        $ownerToReferIdAccountDateMap = [];

        $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        if (!$adminUserId) {
            \LogUtil::info("clientId:$clientId adminUser not exist!");
            return [$userPerformanceRecords, $departmentPerformanceRecords];
        }

        if (!empty($formula))
        {
            $relateInfo = Helper::getFormulaRelateInfoByReferId($clientId,$adminUserId,$formula,$specialReferIds,$referTYpe);
        }

        if ($referTYpe == \Constants::TYPE_WORK_JOURNAL) {
            $templateIds = array_unique(array_column($referInfoList,'template_id'));
            $specialInfo = Helper::getRelateInfoMapByTargetField($clientId,$referTYpe,$targetField,$templateIds,$formula);
        } else {
            $specialInfo = Helper::getRelateInfoMapByTargetField($clientId,$referTYpe,$targetField,$specialReferIds,$formula);
        }

        $allReferUserIds = array_unique(Helper::getAllUserIdsByReferInfoListAndTargetField($referInfoList,$referTYpe,$performanceField));

        $userToDepartmentListMap = Helper::batchGetUserToDepartmentList($clientId, $allReferUserIds);
        $calculate = $formula['calculate'] ?? '';
        foreach ($referInfoList as $referInfo)
        {
            if (!Helper::isNeedRecordPerformance($referTYpe, $referInfo, $rule['rule_id'])) {
                \LogUtil::info("skipRecordPerformance",[
                    'rule_id' => $rule['rule_id'],
                    'refer_info' => $referInfo
                ]);
                continue;
            }

            $externalFieldData = Helper::getExternalFieldDataByReferInfo($referInfo,$referTYpe);
            $specialReferId = (isset($specialKeys['refer_id_key']) && $specialKeys['refer_id_key'] != '') ? $referInfo[$specialKeys['refer_id_key']] : 0;
            $referInfo['specialInfo'] = $specialInfo[$specialReferId] ?? [];

            $exchangeRateSetting = [
                'amount_rmb' => 'exchange_rate',
                'amount_usd' => 'exchange_rate_usd',
            ];
            $exchangeRate = (int)(isset($exchangeRateSetting[$targetField]) ? $referInfo[$exchangeRateSetting[$targetField]] : 100);

            if (empty($specialReferId)) {
                continue;
            }

            $performanceRates = Helper::getReferPerformanceRates($referTYpe, $referInfo, $performanceField, $userToDepartmentListMap);
            $userRates = $performanceRates['users'];
            $departmentRates = $performanceRates['departments'];
            $accountDate = '';
            if (in_array($referTYpe, [\Constants::TYPE_COMPANY,\Constants::TYPE_OPPORTUNITY, \Constants::TYPE_ORDER, \Constants::TYPE_QUOTATION, \Constants::TYPE_CASH_COLLECTION,\Constants::TYPE_LEAD])) {
                $accountDate = (isset($externalFieldData[$timeField]) && !empty($externalFieldData[$timeField])) ? $externalFieldData[$timeField] : '';
            } elseif ($referTYpe == \Constants::TYPE_WORK_JOURNAL) {
                $templateId = $referInfo['template_id'];
                $templateType = $specialInfo[$templateId]['template_type'] ??  \common\models\client\WorkJournalTemplate::WORK_REPORT_TYPE_DAY;
                $startTime = $referInfo['start_time'];
                $cycleEndTime = \common\library\work_journal\Helper::getCycleEndTimeByTemplateType($startTime,$templateType);
                $commitTime = $referInfo['commit_time'];
                $accountDate = $commitTime;

                // 当commit_time在周期截止时间之后提交 那么算周期截止时间
                // 支持编辑工作报告周期，因此如果提交时间不在周期内，就算周期截止时间，只有提交时间在周期内，就算提交时间
                if (strtotime($commitTime) > strtotime($cycleEndTime) || strtotime($commitTime) < strtotime($startTime))
                {
                    $accountDate = $cycleEndTime;
                }
            }

            $timeField = Helper::transferTimeField($referTYpe,$timeField);



            if (empty($accountDate)) {
                // 自定义字段可能有没有值的时候
                $accountDate = $referInfo[$timeField] ?? '1970-01-01 00:00:00';
            }

            // 特殊字段在这里加
            $extraData['exchangeRate'] = $exchangeRate;

            // 公共数据部分
            $record = [
                'client_id' => $rule['client_id'],
                'refer_type' => $referTYpe,
                'refer_id' => $specialReferId,
                'indicator_type' => $rule['target_field'],
                'account_date' => $accountDate,
                'create_time' => $now,
                'update_time' => $now,
                'rule_id' => $rule['rule_id'],
                'indicator_value' => 0,
                'owner_id' => 0,
                'company_id' => $referInfo['company_id'] ?? 0,
            ];

            $checkAccountDate = function ($ownerId,$accountDate,$referId) use ($ownerToReferIdAccountDateMap) {
                if (isset($ownerToReferIdAccountDateMap[$ownerId][$referId])) {
                    if (in_array($accountDate,$ownerToReferIdAccountDateMap[$ownerId][$referId])) {
                        return true;
                    }
                }
                return false;
            };

            foreach ($userRates as $belongsTo => $rate) {
                if (empty($belongsTo)) continue;
                $userRecord = Helper::getPerformanceRecordMapByRecordType($record, PerformanceV2Constant::RECORD_TYPE_USER, $referInfo, $relateInfo, $rule, $belongsTo, $rate, $extraData);
                $accountDate = $userRecord['account_date'];
                $referId = $userRecord['refer_id'];

                if ($checkAccountDate($belongsTo, $accountDate, $referId)) continue;
                $ownerToReferIdAccountDateMap[$belongsTo][$referId][] = $accountDate;

                $userPerformanceRecords[] = $userRecord;
            }

            foreach ($departmentRates as $belongsTo => $rate) {
                $belongsTo = (int)$belongsTo;
                $departmentRecord = Helper::getPerformanceRecordMapByRecordType($record, PerformanceV2Constant::RECORD_TYPE_DEPARTMENT, $referInfo, $relateInfo, $rule, $belongsTo, $rate, $extraData);

                $accountDate = $departmentRecord['account_date'];
                $referId = $departmentRecord['refer_id'];
                if ($checkAccountDate($belongsTo, $accountDate, $referId)) continue;

                $ownerToReferIdAccountDateMap[$belongsTo][$referId][] = $accountDate;

                $departmentPerformanceRecords[] = $departmentRecord;
            }
        }

        \LogUtil::info("getBatchRecordPerformances",[
            'rule_id' => $rule['rule_id'],
            'user' => $userPerformanceRecords,
            'department' => $departmentPerformanceRecords,
            'refer_ids' => $specialReferIds
        ]);

        return [$userPerformanceRecords, $departmentPerformanceRecords];
    }


    /**
     * 根据目标周期获取开始时间和结束时间的 时间间隔
     * @param $startDate
     * @param $endDate
     * @param $ruleTimeType
     * @return int
     */
    public static function getRuleIntervalByRuleTimeType($startDate, $endDate, $ruleTimeType)
    {
        $factor = 0;
        $start  = Carbon::parse($startDate);
        $end    = Carbon::parse($endDate);
        if (
            date('Y-m-d', strtotime($startDate)) !=  $startDate
            || date('Y-m-d', strtotime($endDate)) !=  $endDate
            || $startDate > $endDate) {
            throw new \RuntimeException('时间输入非法');
        }
        // 转化传入时间
        switch ($ruleTimeType) {
            case \PerformanceV2GoalsConfig::TIME_GRANULARITY_DAY:
                $factor = $end->diffInDays($start)+1;
                break;
            case \PerformanceV2GoalsConfig::TIME_GRANULARITY_WEEK:
                $weeks = [];
                do {
                    $next = $start->weekOfYear;
                    $weeks[] = $next;
                    $start = $start->addWeeks();
                    $f =  $start->format('Y-m-d');
                    if ($f > $endDate) {
                        if ($end->weekOfYear != $next) {
                            $weeks[] = $end->weekOfYear;
                        }
                        break;
                    }
                } while(true);
                $factor = count($weeks);
                break;
            case \PerformanceV2GoalsConfig::TIME_GRANULARITY_MONTH:
                $months = [];
                $checkEnd = $end->format('Y-m');
                do {
                    $next = $start->format('Y-m');
                    $months[] = $next;
                    $start = $start->addMonth();
                    $f =  $start->format('Y-m');
                    if ($f > $checkEnd) {
                        if ($checkEnd != $next) {
                            $months[] = $checkEnd;
                        }
                        break;
                    }
                } while(true);
                $factor = count($months);
                break;
        }
        return $factor;
    }
    public static function getCashCollectionValueByReferInfo($clientId,$formula, $referInfo){
        $currencyMapping = [
            'rmb' => [
                'invoice_exchange_rate' => 'cash_collection_invoice_exchange_rate',
                'exchange_rate' => 'exchange_rate',
            ],
            'usd' => [
                'invoice_exchange_rate' => 'cash_collection_invoice_exchange_rate_usd',
                'exchange_rate' => 'exchange_rate_usd',
            ],
        ];
        $currencyType = $formula['currency_type'] ?? ''; // 默认值处理
        if ($currencyType == 'main_currency') {
            $mainCurrency = \common\library\account\Client::getClient($clientId)->getMainCurrency();
            $currencyType = $mainCurrency == 'CNY' ? 'rmb' : 'usd';
        }
        $mapping = $currencyMapping[$currencyType] ?? null;
        if (!$mapping) {
            throw new \RuntimeException("Unsupported currency type: {$currencyType}");
        }

        if (empty($referInfo['cash_collection_invoice_id'])) {
            return 0;
        }


        $mapping = $currencyMapping[$currencyType];
        $exchangeLossAmount = round(
            ($referInfo['cash_collection_invoice_amount'] * $referInfo[$mapping['invoice_exchange_rate']] / 100)
            - ($referInfo['amount'] * $referInfo[$mapping['exchange_rate']] / 100 ),
            2
        );
        return $exchangeLossAmount;


    }
    private static function getCashCollectionValueByCollectionIds($clientId, array $specialReferIds,$formula)
    {
        $params = [':clientId' => $clientId]; // Initialize
        //获取对应回款单数据
        $db = PgActiveRecord::getDbByClientId($clientId);
        $specialReferIdsString = implode(',', $specialReferIds);


        $sql = "SELECT * FROM tbl_cash_collection WHERE client_id =:clientId AND cash_collection_id IN ($specialReferIdsString)";

        $command = $db->createCommand($sql);
        $referInfo = $command->queryAll(true,$params);


        //计算对应回款单的汇兑损益
        $result = [];
        $calculate = $formula['calculate'] ?? '';
        foreach ($referInfo as $info) {
            // 为每个回款单计算汇兑损益
            if (strpos($calculate, 'exchange_loss_amount')){
                $exchangeLossAmount = self::getCashCollectionValueByReferInfo($clientId,$formula, $info);
                $result[$info['cash_collection_id']] = $exchangeLossAmount;
            }
        }
        //返回数组 array{CashCollectionId=>Amount}
        return $result;

    }

    /**
     * 获取目标完成值
     *
     * 本函数进行了时间阶段的去重逻辑，与上面的 getRulesContrastGoalCompletionList 不一致
     *
     * @throws \Exception
     */
    public static function getDateTruncRulesContrast(int $clientId, int $userId, string $timeType, string $startDate, string $endDate, int $referId, int $scope, $ruleId = 0, string $totalStartDate = '')
    {
        // 获取特殊处理纬度成员
        [$referId, $scope] = Helper::specialReferScopeHandle($clientId, $userId, $referId, $scope);

        // 查所有的规则
        $Rulelist = new \common\library\performance_v2\rule\PerformanceV2RuleList($clientId, $userId);
        $Rulelist->setEnableFlag(true);
        $Rulelist->setOrderBy('create_time');
        $Rulelist->setOrder('asc');
        $Rulelist->setPerformanceType(\PerformanceV2Goals::TARGET_RESULT_GOAL);
        $Rulelist->setEnableFlag(true);
        $Rulelist->setFields('client_id,rule_id,name,description,refer_type,target_field,refer_type,time_field,calculate_rule,performance_field');
        $Rulelist->getFormatter()->setScope($scope);
        if ($ruleId) {
            $Rulelist->setRuleId($ruleId);
        }
        $rules = $Rulelist->find();

        // 用户设置处理
        $rules = Helper::getPerformanceV2RuleSortByUserSetting($clientId, $userId, \PerformanceV2Goals::TARGET_RESULT_GOAL, $rules);
        $ruleIds = array_column($rules, 'rule_id');
        $referIds = is_array($referId) ? $referId : [$referId];

        // 查指定时间的目标值
        $goalsList = new PerformanceV2GoalList($clientId, $userId);
        $goalsList->setRuleId($ruleIds);
        $goalsList->setStartYearMonth(date("Y", strtotime($startDate)), date("m", strtotime($startDate)));
        $goalsList->setEndYearMonth(date("Y", strtotime($endDate)), date("m", strtotime($endDate)));
        $goalsList->setStartAccountDate($startDate);
        $goalsList->setEndAccountDate($endDate);
        $goalsList->setFillWithEmptyToMultiRuleMonthData(true);
        $goalsList->setScope($scope);
        $goalsList->setSkipPrivilege(true);
        $goalsList->setReferIds($referIds, $scope);
        $goals = $goalsList->find();

        // 根据不同时间类型获取目标值
        $targetGoals = Helper::getRulesGoalByConds($startDate, $endDate, $goals, \PerformanceV2Goals::TARGET_RESULT_GOAL, $referId);

        $subDepartmentMap = [];
        if ($scope == PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP) {
            // 如果是部门，完成值需要包含该部门下的所有子部门完成值
            $subDepartmentIds = \common\library\department\Helper::getChildrenIds($clientId, $referId);
            $subDepartmentMap[$referId] = $subDepartmentIds;
            $referIds = array_unique(array_merge($referIds, $subDepartmentIds));
            list($temp1, $referIds) = Helper::filterUserIdsAndDepartmentIds($clientId, [], $referIds);
        } else {
            // 如果是人员
            list($referIds, $temp1) = Helper::filterUserIdsAndDepartmentIds($clientId, $referIds, []);
        }

        // 分别根据需要去重的规则和不需要去重的规则做数据统计
        list($needDistinctByReferIdRules, $needNotDistinctByReferIdRules) = Helper::pickOutNeedDistinctOrNotByReferIdRules($rules);

        // 获取对应完成值
        $targetIndicator = [];
        $periods = self::getPeriods($timeType, $endDate);

        $recordModel = new PerformanceV2RecordList($clientId, $userId);
        $recordModel->setSkipOwnerCheck(true);
        $recordModel->setStartAccountDate($startDate);
        $recordModel->setEndAccountDate($endDate);
        $recordModel->setRecordType($scope);
        $recordModel->setRuleId($ruleId);
        if (!empty(array_filter($referIds))) {
            $recordModel->setOwnerId($referIds);
        }

        $sumIndicatorValue = 0;
        // 根据 client_id,rule_id,refer_id 去重
        if($needDistinctByReferIdRules)
        {
            $recordModel->setDistinctField('client_id, rule_id, refer_id');
            $needDistinctRecords = $recordModel->getPeriodRuleOwnerIdGroupSumIndicatorValue($periods);

            // 第一个日期需要算环比，推前了一个时间，所以需要重新设置时间
            if (!empty($totalStartDate)){
                $recordModel->setStartAccountDate($totalStartDate);
            }
            $sumIndicatorValue = $recordModel->getDistinctIndicatorValue();

            $needDistinctByReferIdRuleIds = array_column($needDistinctByReferIdRules, 'rule_id');
            $targetIndicator = Helper::getRuleIndicatorByConds($startDate, $endDate, $needDistinctRecords, $needDistinctByReferIdRuleIds, $referIds, $targetIndicator);
        }

        // 根据 client_id,rule_id,refer_id,owner_id 去重
        if($needNotDistinctByReferIdRules)
        {
            $recordModel->setDistinctField('client_id, rule_id, refer_id, owner_id');
            $needNotDistinctRecords = $recordModel->getPeriodRuleOwnerIdGroupSumIndicatorValue($periods);

            // 第一个日期需要算环比，推前了一个时间，所以需要重新设置时间
            if (!empty($totalStartDate)){
                $recordModel->setStartAccountDate($totalStartDate);
            }
            $sumIndicatorValue = $recordModel->getDistinctIndicatorValue();

            $needNotDistinctByReferIdRuleIds = array_column($needNotDistinctByReferIdRules, 'rule_id');
            $targetIndicator = Helper::getRuleIndicatorByConds($startDate, $endDate, $needNotDistinctRecords, $needNotDistinctByReferIdRuleIds, $referIds, $targetIndicator);
        }

        // 组装数据
        foreach ($rules as &$ruleInfo)
        {
            $ruleInfo["currency"] = Helper::getCurrencyByTargetField($ruleInfo['client_id'], $ruleInfo['refer_type'], $ruleInfo["target_field"]);
            $ruleInfo["year_month_amount"] = [];
            $ruleInfo["year_month_indicator"] = [];
            $ruleInfo["constrast_year_month_indicator"] = [];

            foreach ($referIds as $referValue)
            {
                // 由目标值的 referid 来决定完成值和环比值是否展示相关的信息
                if (!isset($targetGoals[$referValue][$ruleInfo['rule_id']])) {
                    continue;
                }

                // 目标值
                foreach ($targetGoals[$referValue][$ruleInfo['rule_id']] as $yearMonth => $amount)
                {
                    if (isset($ruleInfo['year_month_amount'][$yearMonth]['amount'])) {
                        $ruleInfo['year_month_amount'][$yearMonth]['amount'] += $amount['amount'];
                    } else {
                        $ruleInfo['year_month_amount'][$yearMonth]['amount'] = $amount['amount'];
                    }

                    $ruleInfo['year_month_amount'][$yearMonth]['amount'] = round($ruleInfo['year_month_amount'][$yearMonth]['amount'], 2);
                }

                // 完成值
                if(isset($targetIndicator[$referValue][$ruleInfo['rule_id']]))
                {
                    foreach ($targetIndicator[$referValue][$ruleInfo['rule_id']] as $yearMonth => $indicator)
                    {
                        if (isset($ruleInfo['year_month_indicator'][$yearMonth]['indicator_value'])) {
                            $ruleInfo['year_month_indicator'][$yearMonth]['indicator_value'] += $indicator['indicator_value'];
                        } else {
                            $ruleInfo['year_month_indicator'][$yearMonth]['indicator_value'] = $indicator['indicator_value'];
                        }
                    }

                    // 算上子部门的完成值
                    $subDepartmentIds = $subDepartmentMap[$referValue] ?? [];
                    foreach ($subDepartmentIds as $subDepartmentId)
                    {
                        $indicatorYearMonthMap = $targetIndicator[$subDepartmentId][$ruleInfo['rule_id']] ?? [];

                        foreach ($indicatorYearMonthMap as $yearMonth => $indicator)
                        {
                            if (isset($ruleInfo['year_month_indicator'][$yearMonth]['indicator_value'])) {
                                $ruleInfo['year_month_indicator'][$yearMonth]['indicator_value'] += $indicator['indicator_value'];
                            } else {
                                $ruleInfo['year_month_indicator'][$yearMonth]['indicator_value'] = $indicator['indicator_value'];
                            }
                        }
                    }

                }
            }
        }

        return [$rules, $sumIndicatorValue];
    }

    /**
     * 获取时间纬度数组
     *
     * 因为绩效是需要进行去重逻辑的，所以导致假如在一个时间段，那么这个绩效只会存在一次。
     * 但是因为趋势图等需要展示每个季度/每个月，所以不能直接使用大的时间进行去重，而是需要转化为季度或者月份的时间纬度进行去重
     *
     * @throws \Exception
     */
    public static function getPeriods($timeType, $endDate)
    {
        $periods = [];

        switch ($timeType)
        {
            case PerformanceV2Constant::TIME_TYPE_THIS_MONTH:
                // 获取当前日期和时间
                $currentDate = new DateTime($endDate);

                // 设置当前时间为当月的第一天的00:00:00
                $currentDate->modify('first day of this month')->setTime(0, 0, 0);

                // 循环12次来获取12个月的第一天和最后一天
                for ($i = 0; $i < 12; $i++)
                {
                    // 计算当前月份的第一天的00:00:00
                    $firstDayOfMonth = $currentDate->format('Y-m-d H:i:s');

                    // 计算当前月份的最后一天的23:59:59
                    $lastDayOfMonth = $currentDate->format('Y-m-t 23:59:59');

                    // 将第一天和最后一天的日期时间字符串添加到月份数组
                    $periods[] = [$firstDayOfMonth, $lastDayOfMonth];

                    // 移动到前一个月
                    $currentDate->modify('-1 month');
                }
                break;
            case PerformanceV2Constant::TIME_TYPE_THIS_QUARTER:
            default:
                // 获取当前日期和时间
                $currentDate = new DateTime($endDate);

                // 循环4次来获取4个季度
                for ($i = 0; $i < 4; $i++)
                {
                    // 计算季度的最后一天（当前月份）
                    $endOfQuarter = clone $currentDate;
                    $endOfQuarter->modify('last day of this month 23:59:59');

                    // 计算季度的第一天（当前月份往前推两个月）
                    $startOfQuarter = clone $endOfQuarter;
                    $startOfQuarter->modify('first day of -2 month 00:00:00');

                    // 创建季度表示字符串
                    $quarterString = [$startOfQuarter->format('Y-m-d H:i:s'), $endOfQuarter->format('Y-m-d H:i:s')];

                    // 添加到季度数组
                    $periods[] = $quarterString;

                    // 移动到前一个季度（往前推三个月）
                    $currentDate->modify('first day of -3 month');
                }
                break;
        }

        return $periods;
    }

    public static function isExec($clientId, $referIds)
    {
        $referIdsMd5 = md5(json_encode([$clientId, $referIds]));

        $acquired = (bool)\RedisService::cache()->set($referIdsMd5, 1, 'ex', 3, 'nx');

        // lock 成功，则无正在执行
        return !$acquired;
    }
}
