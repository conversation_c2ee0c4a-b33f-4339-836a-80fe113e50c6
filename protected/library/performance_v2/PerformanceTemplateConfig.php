<?php

namespace common\library\performance_v2;

use common\library\account\Client;
use common\library\custom_field\CustomFieldService;
use common\library\invoice\status\InvoiceStatusService;
use common\library\opportunity\stage\migration\OpportunityStage;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\setting\library\origin\Origin;
use common\library\setting\library\origin\OriginApi;
use common\library\setting\library\stage\Stage;
use common\library\workflow\WorkflowConstant;
use common\models\client\PerformanceV2Rule;

class PerformanceTemplateConfig
{

    public static function getPerformanceTemplateConfig(int $clientId, int $performanceType, int $templateType, $templateId = '',$id = 0): array
    {
        $mainCurrency = Client::getClient($clientId)->getMainCurrency();
        $omsFlag = \common\library\privilege_v3\Helper::checkHasOmsPrivilege($clientId);
        $currencyType = $mainCurrency == "CNY" ? "rmb" : "usd";
        //订单状态
        $invoiceStatusService = new InvoiceStatusService($clientId, \Constants::TYPE_ORDER);
        $orderEndStatus = $invoiceStatusService->endStatus();
        $filterStatus = ['已作废', '售后', '交易取消'];
        foreach ($orderEndStatus as $status) {
            if (in_array($status['name'], $filterStatus)) {
                $orderFilterStatusIds = $status['id'];
            } else {
                $orderEndStatusIds[] = $status['id'];
            }
        }
        //客户来源
        $aliOriginId = [];
        $originList = \CustomerOptionService::getOriginListAll($clientId);
        foreach ($originList as $item) {
            if ($item['origin_name'] == '阿里巴巴（B2B平台）') {
                $aliOriginId[] = $item['origin_id'];
            }
        }

        $templateListData =  [
            // 模版一级分类 结果目标
            PerformanceV2Rule::PERFORMANCE_TYPE_RESULT => [
                //模板二级分类 以订单结算
                PerformanceV2Constant::TEMPLATE_TYPE_ORDER => [
                    [
                        'id' => 'amount',
                        'name' => '成交金额', //模板三级分类  成交金额
                        'description' => '以订单完成状态作为节点统计，建议可用以下维度对成交金额的完成情况进行考核。', //三级分类hover说明
                        'list' => [
                            [
                                'id'                => 201,
                                'name'              => '成交订单金额',
                                'description'       => "当前成交的订单总销售额",
                                'refer_type'        => \Constants::TYPE_ORDER,
                                'title'             => 'hot', // 是否热门
                                'time_field'        => 'account_date',
                                'target_field'      => 'amount',
                                'currency_type' => $currencyType,
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_SUM,
                                'performance_field' => 'users',
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                'criteria'          => '(1 AND 2)',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'status',
                                        'refer_type' => \Constants::TYPE_ORDER,
                                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                                        'value' => $orderEndStatusIds ?? [],
                                    ],
                                ],
                            ],
                            [
                                'id'                => 203,
                                'name'              => '订单毛利',
                                'description'       => "已完成订单的实际毛利",
                                'refer_type'        => \Constants::TYPE_ORDER,
                                'title'             => 'new',
                                'time_field'        => 'account_date',
                                'target_field'      => 'real_profit' ,
                                'currency_type' => $currencyType,
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_SUM,
                                'performance_field' => 'users',
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                'criteria'          => '(1)',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'status',
                                        'refer_type' => \Constants::TYPE_ORDER,
                                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                                        'value' => $orderEndStatusIds ?? [],
                                    ],
                                ],
                            ],
//                            [
//                                'id'                => 202,
//                                'name'              => '成交新客户订单金额',
//                                'description'       => "当期成交的新客户订单总金额",
//                                'refer_type'        => \Constants::TYPE_ORDER,
//                                'title'             => '',
//                                'time_field'        => 'last_order_status_update_time',
//                                'target_field'      => 'amount',
//                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_SUM,
//                                'performance_field' => 'users',
//                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
//                                'criteria'          => '(1 AND 2)',
//                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
//                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
//                                'formula'           => '{}',
//                                'filters' => [
//                                    [
//                                        'filter_no' => 1,
//                                        'field' => 'status',
//                                        'refer_type' => \Constants::TYPE_ORDER,
//                                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
//                                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
//                                        'value' => $orderEndStatusIds ?? [],
//                                    ],
//                                    [
//                                        'filter_no' => 2,
//                                        'field' => 'performance_order_count',
//                                        'refer_type' => \Constants::TYPE_COMPANY,
//                                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//                                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
//                                        'value' => 1,
//                                    ],
//                                ],
//                            ],
                            [
                                'id'=> '2041',
                                'name' => \Yii::t('ai',"成交新客户订单金额"),
                                'description'       => \Yii::t('ai',"当期成交的新客户订单总金额"),
                                'refer_type'        => \Constants::TYPE_ORDER,
                                'title'             => 'new',
                                'time_field'        => 'account_date',
                                'target_field'      => 'amount',
                                'currency_type' => $currencyType,
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_SUM,
                                'performance_field' => 'users',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                'criteria'          => '(1 AND 2)',
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'status',
                                        'refer_type' => \Constants::TYPE_ORDER,
                                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                                        'value' => $orderEndStatusIds ?? [],
                                    ],
                                    [
                                        'filter_no' => 2,
                                        'field' => 'first_order_flag',
                                        'refer_type' => \Constants::TYPE_ORDER,
                                        'field_type' => CustomFieldService::FIELD_TYPE_BOOLEAN,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                                        'value' => 1 ,
                                    ],
                                ],
                            ],
                            [
                                'id'=> '2042',
                                'name' => \Yii::t('ai',"成交老客户订单金额"),
                                'description'       => \Yii::t('ai',"老客户当期总成交订单金额"),
                                'refer_type'        => \Constants::TYPE_ORDER,
                                'title'             => 'new',
                                'time_field'        => 'account_date',
                                'target_field'      => 'amount',
                                'currency_type' => $currencyType,
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_SUM,
                                'performance_field' => 'users', // 待确认
                                // 订单状态 = #获取订单的所有终止状态，过滤掉「已作废」、「售后」、「 交易取消」的状态# 且 订单_是否为成交首单=是
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                'criteria'          => '(1 AND 2)',
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'status',
                                        'refer_type' => \Constants::TYPE_ORDER,
                                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                                        'value' => $orderEndStatusIds ?? [],
                                    ],
                                    [
                                        'filter_no' => 2,
                                        'field' => 'first_order_flag',
                                        'refer_type' => \Constants::TYPE_ORDER,
                                        'field_type' => CustomFieldService::FIELD_TYPE_BOOLEAN,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                                        'value' => 0,
                                    ],
                                ],
                            ],
                        ]
                    ],
                    [
                        'id' => 'customer_count',
                        'name' => '客户数',
                        'description' => '以订单完成状态作为节点统计，建议可用以下维度对成交客户数的完成情况进行考核。',
                        'list' => [
                            [
                                'id'                => 403,
                                'name'              => '复购客户数',
                                'description'       => "订单成交次数≥2的客户数（成交次数可更改）",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => 'hot',
                                'time_field'        => 'every_transaction_order_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'user_id',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                'criteria'          => '(1)',
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'performance_order_count',
                                        'refer_type' => \Constants::TYPE_COMPANY,
                                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL,
                                        'value' => 2,
                                    ],
                                ],
                            ],
                            [
                                'id'                => 404,
                                'name'              => '国际站成交客户数',
                                'description'       => "阿里国际站的成交客户数",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => 'new',
                                'time_field'        => 'every_transaction_order_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'user_id',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                'criteria'          => '(1)',
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'origin_list',
                                        'refer_type' => \Constants::TYPE_COMPANY,
                                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                                        'value' => [4],
                                    ],
                                ],
                            ],
                            [
                                'id'                => 401,
                                'name'              => '成交客户数',
                                'description'       => "当前所有已成交的客户数",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => '',
                                'time_field'        => 'every_transaction_order_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'user_id',
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'formula'           => '{}',
                                'filters' => [],
                            ],
                            [
                                'id'                => 402,
                                'name'              => '首次成交客户数',
                                'description'       => "当前首次达成合作的客户数",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => '',
                                'time_field'        => 'transaction_order_first_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'user_id',
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'formula'           => '{}',
                                'filters' => [],
                            ],
                        ]
                    ],
                    [
                        'id' => 'order_count',
                        'name' => '订单数',
                        'description' => '以订单完成状态作为节点统计，建议可用以下维度对成交订单数的完成情况进行考核。',
                        'list' => [
                            [
                                'id'                => 204,
                                'name'              => '成交订单数',
                                'description'       => "当前所有已成交的订单数",
                                'refer_type'        => \Constants::TYPE_ORDER,
                                'title'             => 'hot',
                                'time_field'        => 'account_date',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'users',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                'criteria'          => '(1)',
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'status',
                                        'refer_type' => \Constants::TYPE_ORDER,
                                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                                        'value' => $orderEndStatusIds ?? [],
                                    ]
                                ],
                            ],
                            [
                                'id'                => 207,
                                'name'              => '大额订单数',
                                'description'       => "成交订单金额≥X的订单数",
                                'refer_type'        => \Constants::TYPE_ORDER,
                                'title'             => 'new',
                                'time_field'        => 'account_date',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'users',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                'criteria'          => '(1 AND 2)',
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'amount',
                                        'refer_type' => \Constants::TYPE_ORDER,
                                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER,
                                        'unit_type' => 'currency',
                                        'unit' => $mainCurrency,
                                        'value' =>  '',
                                    ],
                                    [
                                        'filter_no' => 2,
                                        'field' => 'status',
                                        'refer_type' => \Constants::TYPE_ORDER,
                                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                                        'value' => $orderEndStatusIds ?? [],
                                    ],
                                ],
                            ],
                            [
                                'id'                => 208,
                                'name'              => '首次回款的订单数',
                                'description'       => "已有首次回款记录的订单数",
                                'refer_type'        => \Constants::TYPE_ORDER,
                                'title'             => 'new',
                                'time_field'        => 'first_collection_date',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'users',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '()',
                                'formula'           => '{}',
                                'filters' => [],
                            ],
                            [
                                'id'                => 209,
                                'name'              => '已回款的订单数',
                                'description'       => "已全部回款的订单数",
                                'refer_type'        => \Constants::TYPE_ORDER,
                                'title'             => 'new',
                                'time_field'        => 'last_collection_date',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'users',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                'criteria'          => '(1)',
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'cash_collection_status',
                                        'refer_type' => \Constants::TYPE_ORDER,
                                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                                        'value' =>3,
                                    ],
                                ],
                            ],
                            [
                                'id'                => 205,
                                'name'              => '新客户成交订单数',
                                'description'       => "当前首次达成合作的订单数",
                                'refer_type'        => \Constants::TYPE_ORDER,
                                'title'             => '',
                                'time_field'        => 'account_date',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'users',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                'criteria'          => '(1 AND 2)',
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'status',
                                        'refer_type' => \Constants::TYPE_ORDER,
                                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                                        'value' => $orderEndStatusIds ?? [],
                                    ],
                                    [
                                        'filter_no' => 2,
                                        'field' => 'first_order_flag',
                                        'refer_type' => \Constants::TYPE_ORDER,
                                        'field_type' => CustomFieldService::FIELD_TYPE_BOOLEAN,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                                        'value' => 1 ,
                                    ],
                                ],
                            ],
                            [
                                'id'                => 206,
                                'name'              => '老客户成交订单数',
                                'description'       => "复购客户的所有成交订单数",
                                'refer_type'        => \Constants::TYPE_ORDER,
                                'title'             => '',
                                'time_field'        => 'account_date',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'users',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                'criteria'          => '(1 AND 2)',
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'status',
                                        'refer_type' => \Constants::TYPE_ORDER,
                                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                                        'value' => $orderEndStatusIds ?? [],
                                    ],
                                    [
                                        'filter_no' => 2,
                                        'field' => 'first_order_flag',
                                        'refer_type' => \Constants::TYPE_ORDER,
                                        'field_type' => CustomFieldService::FIELD_TYPE_BOOLEAN,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                                        'value' => 0,
                                    ],
                                ],
                            ]

                        ]
                    ]
                ],
                PerformanceV2Constant::TEMPLATE_TYPE_OPPORTUNITY => [
                    [
                        'id' => 'amount',
                        'name' => '成交金额',
                        'description' => '以赢单商机作为节点统计，建议可用以下维度对成交金额的完成情况进行考核。',
                        'list' => [
                            [
                                'id'                => 901,
                                'name'              => '赢单商机金额',
                                'description'       => "当前赢单的商机总金额",
                                'refer_type'        => \Constants::TYPE_OPPORTUNITY,
                                'title'             => 'hot',
                                'time_field'        => 'account_date',
                                'target_field'      => 'amount',
                                'currency_type' => $currencyType,
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_SUM,
                                'performance_field' => 'main_user',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                'criteria'          => '(1)',
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'stage_type',
                                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                                        'value' => OpportunityStage::STAGE_WIN_STATUS,
                                    ],
                                ],
                            ],
//                            [
//                                'id'                => 902,
//                                'name'              => '新客户赢单商机金额',
//                                'description'       => "新客户当期赢单的商机金额",
//                                'refer_type'        => \Constants::TYPE_OPPORTUNITY,
//                                'title'             => 'hot',
//                                'time_field'        => 'stage_edit_time',
//                                'target_field'      => 'amount',
//                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_SUM,
//                                'performance_field' => 'user_id',
//                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
//                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
//                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
//                                'criteria'          => '(1 AND 2)',
//                                'formula'           => '{}',
//                                'filters' => [
//                                    [
//                                        'filter_no' => 1,
//                                        'field' => 'stage_type',
//                                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
//                                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
//                                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
//                                        'value' => 4,
//                                    ],
//                                    [
//                                        'filter_no' => 2,
//                                        'field' => 'success_opportunity_count',
//                                        'refer_type' => \Constants::TYPE_COMPANY,
//                                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//                                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
//                                        'value' => 1,
//                                    ],
//                                ],
//                            ],
//                            [
//                                'id'                => 903,
//                                'name'              => '老客户赢单商机金额',
//                                'description'       => "老客户当期赢单的商机金额",
//                                'refer_type'        => \Constants::TYPE_OPPORTUNITY,
//                                'title'             => 'hot',
//                                'time_field'        => 'stage_edit_time',
//                                'target_field'      => 'amount',
//                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_SUM,
//                                'performance_field' => 'user_id',
//                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
//                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
//                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
//                                'criteria'          => '(1 AND 2)',
//                                'formula'           => '{}',
//                                'filters' => [
//                                    [
//                                        'filter_no' => 1,
//                                        'field' => 'stage_type',
//                                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
//                                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
//                                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
//                                        'value' => 4,
//                                    ],
//                                    [
//                                        'filter_no' => 2,
//                                        'field' => 'success_opportunity_count',
//                                        'refer_type' => \Constants::TYPE_COMPANY,
//                                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//                                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER,
//                                        'value' => 1,
//                                    ],
//                                ],
//                            ],
//                            [
//                                'id'                => 911,
//                                'name'              => '输单商机数',
//                                'description'       => "当期为输单状态的商机数量->变更为输单阶段的商机数量",
//                                'refer_type'        => \Constants::TYPE_OPPORTUNITY,
//                                'title'             => 'new',
//                                'time_field'        => 'stage_edit_time',
//                                'target_field'      => '',
//                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
//                                'performance_field' => 'create_user',
//                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
//                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
//                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
//                                'criteria'          => '(1)',
//                                'formula'           => '{}',
//                                'filters' => [
//                                    [
//                                        'filter_no' => 1,
//                                        'field' => 'stage_type',
//                                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
//                                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
//                                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
//                                        'value' => 3,
//                                        'unit' => "",
//                                    ]
//
//                                ],
//                            ],
                        ]
                    ],
                    [
                        'id' => 'customer_count',
                        'name' => '客户数',
                        'description' => '以赢单商机作为节点统计，建议可用以下维度对成交客户数的完成情况进行考核。',
                        'list' => [
                            [
                                'id'                => 407,
                                'name'              => '复购客户数',
                                'description'       => "当前赢单商机次数≥2的客户数（成交次数可更改）",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => 'hot',
                                'time_field'        => 'latest_success_opportunity_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'user_id',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                'criteria'          => '(1)',
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'success_opportunity_count',
                                        'refer_type' => \Constants::TYPE_COMPANY,
                                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL,
                                        'value' => 2,
                                    ],
                                ],
                            ],
                            [
                                'id'                => 408,
                                'name'              => '国际站成交客户数',
                                'description'       => "阿里国际站的成交客户数",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => 'new',
                                'time_field'        => 'latest_success_opportunity_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'user_id',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                'criteria'          => '(1)',
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'origin_list',
                                        'refer_type' => \Constants::TYPE_COMPANY,
                                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                                        'value' => $aliOriginId,
                                    ],
                                ],
                            ],
                            [
                                'id'                => 405,
                                'name'              => '成交客户数',
                                'description'       => "当前有商机赢单的客户数",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => '',
                                'time_field'        => 'latest_success_opportunity_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'user_id',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '()',
                                'formula'           => '{}',
                                'filters' => [],
                            ],
                            [
                                'id'                => 406,
                                'name'              => '成交新客户数',
                                'description'       => "当前首次达成合作的客户数",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => '',
                                'time_field'        => 'success_opportunity_first_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'user_id',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '()',
                                'formula'           => '{}',
                                'filters' => [],
                            ],
                        ]

                    ],
                    [
                        'id' => 'order_count',
                        'name' => '商机数',
                        'description' => "以赢单商机作为节点统计，建议可用以下维度对成交订单数的跟进完成情况进行考核。",
                        'list' => [
                            [
                                'id'                => 904,
                                'name'              => '赢单商机数',
                                'description'       => "当前已赢单的商机总数",
                                'refer_type'        => \Constants::TYPE_OPPORTUNITY,
                                'title'             => 'hot',
                                'time_field'        => 'account_date',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'main_user',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                'criteria'          => '(1)',
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'stage_type',
                                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                                        'value' => Stage::STAGE_WIN_STATUS,
                                    ],
                                ],
                            ],
                            [
                                'id'                => 908,
                                'name'              => '大额商机数',
                                'description'       => "当期赢单金额≥X的商机数",
                                'refer_type'        => \Constants::TYPE_OPPORTUNITY,
                                'title'             => 'new',
                                'time_field'        => 'account_date',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'main_user',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                'criteria'          => '(1 AND 2)',
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'amount',
                                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER,
                                        'unit_type' => 'currency',
                                        'unit' => $mainCurrency,
                                        'value' => '',
                                    ],
                                    [
                                        'filter_no' => 2,
                                        'field' => 'stage_type',
                                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                                        'value' => Stage::STAGE_WIN_STATUS,
                                    ],
                                ],
                            ],
                            [
                                'id'                => 906,
                                'name'              => '成交新客户赢单商机数',
                                'description'       => "当前首次达成合作的商机数量总和",
                                'refer_type'        => \Constants::TYPE_OPPORTUNITY,
                                'title'             => '',
                                'time_field'        => 'stage_edit_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'main_user',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                'criteria'          => '(1 AND 2)',
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'stage_type',
                                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                                        'value' => Stage::STAGE_WIN_STATUS,
                                    ],
                                    [
                                        'filter_no' => 2,
                                        'field' => 'success_opportunity_count',
                                        'refer_type' => \Constants::TYPE_COMPANY,
                                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                                        'value' => 1,
                                    ],
                                ],
                            ],
                            [
                                'id'                => 907,
                                'name'              => '成交老客户赢单商机数',
                                'description'       => "当前多次下单的商机客户数量总和",
                                'refer_type'        => \Constants::TYPE_OPPORTUNITY,
                                'title'             => '',
                                'time_field'        => 'stage_edit_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'main_user',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                'criteria'          => '(1 AND 2)',
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'stage_type',
                                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                                        'value' => Stage::STAGE_WIN_STATUS,
                                    ],
                                    [
                                        'filter_no' => 2,
                                        'field' => 'success_opportunity_count',
                                        'refer_type' => \Constants::TYPE_COMPANY,
                                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER,
                                        'value' => 2,
                                    ],
                                ],
                            ],
                            [
                                'id' => 910,
                                'name' => '新建商机数',
                                'description' => "新建商机的数量（包括手动创建/自动创建）",
                                'refer_type' => \Constants::TYPE_OPPORTUNITY,
                                'title' => 'hot',
                                'time_field' => 'create_time',
                                'target_field' => '',
                                'calculate_rule' => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'create_user',
                                'type' => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type' => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'criteria_type' => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria' => '()',
                                'formula' => '{}',
                                'filters' => [],
                            ],
                        ],
                    ],


//                    [
//                        'id' => 'sample',
//                        'name' => '样品',
//                        'description' => '商机处于样品状态时，建议可用以下维度对样品的完成情况进行考核。',
//                        'list' => [
//                            [
//                                'id'                => 909,
//                                'name'              => '样品单金额',
//                                'description'       => "当期商机阶段为样品的总金额",
//                                'refer_type'        => \Constants::TYPE_OPPORTUNITY,
//                                'title'             => 'hot',
//                                'time_field'        => 'stage_edit_time',
//                                'target_field'      => 'amount',
//                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_SUM,
//                                'performance_field' => 'user_id',
//                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
//                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
//                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
//                                'criteria'          => '(1)',
//                                'formula'           => '{}',
//                                'filters' => [
//                                    [
//                                        'filter_no' => 1,
//                                        'field' => 'stage',
//                                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
//                                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
//                                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
//                                        'value' => $sampleStageIds ?? 0,
//                                    ],
//                                ],
//                            ],
//                        ]
//
//                    ],
                ],
                PerformanceV2Constant::TEMPLATE_TYPE_CASH_COLLECTION => [
                    [
                        'id' => 'cash_collection',
                        'name' => '回款单',
                        'description' => '以回款单作为结算依据时，建议可用以下维度对回款金额的完成情况进行考核。',
                        'list' => [
                            [
                                'id'                => 1001,
                                'name'              => '已回款单金额',
                                'description'       => "已生效回款单的总金额",
                                'refer_type'        => \Constants::TYPE_CASH_COLLECTION,
                                'title'             => 'hot',
                                'time_field'        => 'collection_date',
                                'target_field'      => 'amount',
                                'currency_type' => $currencyType,
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_SUM,
                                'performance_field' => 'users',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_RESULT,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                'criteria'          => '(1 AND 2)',
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'collect_status',
                                        'refer_type' => \Constants::TYPE_CASH_COLLECTION,
                                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                                        'value' => \common\library\cash_collection\CashCollection::IS_COLLECTED,
                                    ],
                                    [
                                        'filter_no' => 2,
                                        'field' => 'serial_id',
                                        'refer_type' => \Constants::TYPE_COMPANY,
                                        'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
                                        'value' => '',
                                    ],
                                ],
                            ],
                        ]

                    ]
                ]

            ],
            // 过程目标
            PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS => [
                // 沟通过程
                PerformanceV2Constant::TEMPLATE_TYPE_COMMUNICATE_PROCESS => [
                    [
                        'id' => 'edm',
                        'name' => 'EDM',
                        'list' => [
                            [
                                'id' => 2101,
                                'name' => '营销邮件发送数',
                                'description' => "当前已发送的营销邮件总数量",
                                'refer_type' => \Constants::TYPE_EDM,
                                'title' => 'hot', // 是否热门
                                'time_field' => 'start_time',
                                'target_field' => 'send_to_count',
                                'calculate_rule' => PerformanceV2Constant::CALCULATE_RULE_SUM,
                                'performance_field' => 'user_id',
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria' => '',
                                'type' => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type' => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'formula' => '{}',
                                'filters' => [],
                            ],
                            [
                                'id' => 2103,
                                'name' => '营销邮件打开数',
                                'description' => "当前已发送的营销邮件被打开的数量",
                                'refer_type' => \Constants::TYPE_EDM,
                                'title' => 'new', // 是否热门
                                'time_field' => 'start_time',
                                'target_field' => 'view_ucount',
                                'calculate_rule' => PerformanceV2Constant::CALCULATE_RULE_SUM,
                                'performance_field' => 'user_id',
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria' => '',
                                'type' => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type' => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'formula' => '{}',
                                'filters' => [],
                            ],
                            [
                                'id' => 2104,
                                'name' => '营销邮件回复数',
                                'description' => "当前收到回复的营销邮件的数量",
                                'refer_type' => \Constants::TYPE_EDM,
                                'title' => 'new', // 是否热门
                                'time_field' => 'start_time',
                                'target_field' => 'reply_count',
                                'calculate_rule' => PerformanceV2Constant::CALCULATE_RULE_SUM,
                                'performance_field' => 'user_id',
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria' => '',
                                'type' => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type' => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'formula' => '{}',
                                'filters' => [],
                            ],
                            [
                                'id' => 2105,
                                'name' => '营销邮件发送客户数',
                                'description' => "已发送营销邮件的私海客户数量",
                                'refer_type' => \Constants::TYPE_COMPANY,
                                'title' => 'new', // 是否热门
                                'time_field' => 'edm_time',
                                'target_field' => '',
                                'calculate_rule' => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'user_id',
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria' => '',
                                'type' => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type' => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'formula' => '{}',
                                'filters' => [],
                            ],
                            [
                                'id' => 2102,
                                'name' => '营销邮件送达数',
                                'description' => "当前发送的营销邮件中实际送达买家的数量",
                                'refer_type' => \Constants::TYPE_EDM,
                                'title' => '', // 是否热门
                                'time_field' => 'start_time',
                                'target_field' => 'delivery_count',
                                'calculate_rule' => PerformanceV2Constant::CALCULATE_RULE_SUM,
                                'performance_field' => 'user_id',
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria' => '',
                                'type' => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type' => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'formula' => '{}',
                                'filters' => [],
                            ],

                        ]
                    ],

                    [
                        'id' => 'mail',
                        'name' => '邮件',
                        'list' => [
                            [
                                'id'                => 605,
                                'name'              => '发送给系统中客户邮件数',
                                'description'       => "当前发送给系统中客户（含私海、公海、同事客户）的邮件总数",
                                'refer_type'        => \Constants::TYPE_MAIL,
                                'title'             => 'hot', // 是否热门
                                'time_field'        => 'send_time',
                                'target_field'      => 'send_to_customer_mail_count',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'sender',
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'formula'           => '{}',
                                'filters' => [],
                            ],
                            [
                                'id'                => 601,
                                'name'              => '发送邮件数',
                                'description'       => "当前发送成功的邮件总数（不包含发送失败和已删除的邮件）",
                                'refer_type'        => \Constants::TYPE_MAIL,
                                'title'             => '', // 是否热门
                                'time_field'        => 'send_time',
                                'target_field'      => 'send_mail_count',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'sender',
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'formula'           => '{}',
                                'filters' => [],
                            ],
                            [
                                'id'                => 602,
                                'name'              => '接收邮件数',
                                'description'       => "当前收到的邮件总数",
                                'refer_type'        => \Constants::TYPE_MAIL,
                                'title'             => '', // 是否热门
                                'time_field'        => 'receive_time',
                                'target_field'      => 'receive_mail_count',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'receiver',
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'formula'           => '{}',
                                'filters' => [],
                            ],
                            [
                                'id'                => 603,
                                'name'              => '对方回复数',
                                'description'       => "当前收到对方回复邮件的数量",
                                'refer_type'        => \Constants::TYPE_MAIL,
                                'title'             => '', // 是否热门
                                'time_field'        => 'send_time',
                                'target_field'      => 'reply_from_mail_count',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'sender',
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'formula'           => '{}',
                                'filters' => [],
                            ],
                            [
                                'id'                => 604,
                                'name'              => '我方回复数',
                                'description'       => "当前我方回复的邮件数",
                                'refer_type'        => \Constants::TYPE_MAIL,
                                'title'             => '', // 是否热门
                                'time_field'        => 'receive_time',
                                'target_field'      => 'reply_to_mail_count',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'receiver',
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'formula'           => '{}',
                                'filters' => [],
                            ],
                            [
                                'id'                => 606,
                                'name'              => '收到系统中客户的邮件数',
                                'description'       => "当前收到系统中客户（含私海、公海、同事客户）的邮件总数",
                                'refer_type'        => \Constants::TYPE_MAIL,
                                'title'             => '', // 是否热门
                                'time_field'        => 'receive_time',
                                'target_field'      => 'receive_from_customer_mail_count',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'receiver',
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'formula'           => '{}',
                                'filters' => [],
                            ],
                            [
                                'id'                => 607,
                                'name'              => '给我发送邮件的客户数',
                                'description'       => "给我发送过邮件的私海客户数",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => '', // 是否热门
                                'time_field'        => 'receive_mail_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'user_id',
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'formula'           => '{}',
                                'filters' => [],
                            ],
                            [
                                'id'                => 608,
                                'name'              => '我发送邮件的客户数',
                                'description'       => "我发送过邮件的私海客户数",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => '', // 是否热门
                                'time_field'        => 'send_mail_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'user_id',
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'formula'           => '{}',
                                'filters' => [],
                            ],
                            [
                                'id'                => 609,
                                'name'              => '有邮件沟通的客户数',
                                'description'       => "有过邮件沟通的私海客户数（包括我发送的【或】我收到的）",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => '', // 是否热门
                                'time_field'        => 'mail_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'user_id',
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'formula'           => '{}',
                                'filters' => [],
                            ]
                        ]
                    ],

                    [
                        'id' => 'TM',
                        'name' => 'TM',
                        'list' => [
                            [
                                'id'                => 2501,
                                'name'              => '给我发送阿里TM的客户数',
                                'description'       => "给我发送过阿里TM的私海客户数",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => '', // 是否热门
                                'time_field'        => 'latest_receive_ali_tm_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'user_id',
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'formula'           => '{}',
                                'filters' => [],
                            ],
                            [
                                'id'                => 2502,
                                'name'              => '我发送阿里TM的客户数',
                                'description'       => "我发送过阿里TM的私海客户数",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => '', // 是否热门
                                'time_field'        => 'latest_send_ali_tm_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'user_id',
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'formula'           => '{}',
                                'filters' => [],
                            ]
                        ]
                    ],
                ],
                // 跟进过程
                PerformanceV2Constant::TEMPLATE_TYPE_FOLLOW_UP_PROCESS => [
                    [
                        'id' => 'customer',
                        'name' => '客户',
                        'description' => '',
                        'list' => [
                            [
                                'id'                => 417,
                                'name'              => '新建客户数',
                                'description'       => "新建的客户数（包括手动创建/自动创建)",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => 'hot',
                                'time_field'        => 'archive_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'create_user',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '()',
                                'formula'           => '{}',
                                'filters' => [],
                            ],
                            [
                                'id'                => 410,
                                'name'              => '公海移入私海客户数',
                                'description'       => "从公海移入私海的客户总数",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => 'hot',
                                'time_field'        => 'private_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'user_id',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                'criteria'          => '()',
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'public_time',
                                        'refer_type' => \Constants::TYPE_COMPANY,
                                        'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
                                        'unit' => "",
                                    ],
                                ],
                            ],
                            [
                                'id'                => 413,
                                'name'              => '沟通客户数',
                                'description'       => "有过沟通的私海客户数（包括我联系的【或】联系我的）",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => 'new',
                                'time_field'        => 'order_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'user_id',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '()',
                                'formula'           => '{}',
                                'filters' => [],
                            ],
                            [
                                'id'                => 409,
                                'name'              => '新建国际站客户数',
                                'description'       => "来源为阿里国际站的新建客户总数（包括手动创建/自动创建)",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => '',
                                'time_field'        => 'archive_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'create_user',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '()',
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'origin_list',
                                        'refer_type' => \Constants::TYPE_COMPANY,
                                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                                        'value' => [4],
                                    ],
                                ],
                            ],

                            [
                                'id'                => 411,
                                'name'              => '移入公海客户数',
                                'description'       => "从私海移入公海的客户总数",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => '',
                                'time_field'        => 'public_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'last_owner',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '()',
                                'formula'           => '{}',
                                'filters' => [],
                            ],
                            [
                                'id'                => 412,
                                'name'              => '跟进客户数',
                                'description'       => "当期跟进的客户数",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => '',
                                'time_field'        => 'recent_follow_up_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'user_id',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '()',
                                'formula'           => '{}',
                                'filters' => [],
                            ],

                            [
                                'id'                => 414,
                                'name'              => '跟进新成交客户数',
                                'description'       => "跟进初次成交的客户数",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => '',
                                'time_field'        => 'recent_follow_up_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'user_id',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                'criteria'          => '(1)',
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'performance_order_count',
                                        'refer_type' => \Constants::TYPE_COMPANY,
                                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                                        'value' => 1,
                                        'unit' => "次",
                                        'unit_type' => "countable",
                                    ]
                                ],
                            ],
                            [
                                'id'                => 415,
                                'name'              => '跟进多次成交客户数',
                                'description'       => "跟进成交订单数≥2的客户数（成交次数可更改）",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => '',
                                'time_field'        => 'recent_follow_up_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'user_id',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                'criteria'          => '(1)',
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'performance_order_count',
                                        'refer_type' => \Constants::TYPE_COMPANY,
                                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL,
                                        'value' => 2,
                                        'unit' => "次",
                                        'unit_type' => "countable",
                                    ]

                                ],
                            ],
                            [
                                'id'                => 416,
                                'name'              => '「写跟进」的客户数',
                                'description'       => "有「写跟进」记录的私海客户总数（包含阿里跟进小记）",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => '',
                                'time_field'        => 'latest_write_follow_up_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'user_id',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '()',
                                'formula'           => '{}',
                                'filters' => [],
                            ],
                            [
                                'id'                => 2001,
                                'name'              => '添加跟进数',
                                'description'       => "新建的「线索、客户、商机」的跟进数（包含阿里跟进小记）",
                                'refer_type'        => \Constants::TYPE_FOLLOWUP,
                                'title'             => '',
                                'time_field'        => 'create_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'create_user',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '()',
                                'formula'           => '{}',
                                'filters' => [],
                            ],
                            [
                                'id'                => 418,
                                'name'              => '手动新建客户数',
                                'description'       => "当前手动新建的客户数",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => '',
                                'time_field'        => 'archive_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'create_user',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                'criteria'          => '(1)',
                                'formula'           => '{}',
                                'filters' => [
                                    [
                                        'filter_no' => 1,
                                        'field' => 'archive_type',
                                        'refer_type' => \Constants::TYPE_COMPANY,
                                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                                        'value' => [6,1],
                                        'unit' => "",
                                    ]

                                ],
                            ],
                            [
                                'id'                => 419,
                                'name'              => 'WhatsApp沟通客户数',
                                'description'       => "当期有WhatsApp消息往来的客户数",
                                'refer_type'        => \Constants::TYPE_COMPANY,
                                'title'             => '',
                                'time_field'        => 'latest_whatsapp_time',
                                'target_field'      => '',
                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                'performance_field' => 'user_id',
                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                'criteria'          => '()',
                                'formula'           => '{}',
                                'filters' => [],
                            ],
                        ]
                    ],
                    [
                        'id' => 'lead',
                        'name' => '线索',
                        'description' => '',
                        'list' =>
                            [
                                [
                                    'id'                => 702,
                                    'name'              => '来自国际站的新建线索数',
                                    'description'       => "来自阿里国际站的新建线索数（包括手动创建/自动创建）",
                                    'refer_type'        => \Constants::TYPE_LEAD,
                                    'title'             => 'new',
                                    'time_field'        => 'create_time',
                                    'target_field'      => '',
                                    'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                    'performance_field' => 'create_user',
                                    'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                    'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                    'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_AND,
                                    'criteria'          => '(1)',
                                    'formula'           => '{}',
                                    'filters' => [
                                        [
                                            'filter_no' => 1,
                                            'field' => 'origin_list',
                                            'refer_type' => \Constants::TYPE_LEAD,
                                            'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                                            'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                                            'value' => [4],
                                            'unit' => "",
                                        ]

                                    ],
                                ],
                                [
                                    'id'                => 701,
                                    'name'              => '新建线索数',
                                    'description'       => "新建的线索数（包括手动创建/自动创建）",
                                    'refer_type'        => \Constants::TYPE_LEAD,
                                    'title'             => '',
                                    'time_field'        => 'create_time',
                                    'target_field'      => '',
                                    'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                    'performance_field' => 'create_user',
                                    'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                    'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                    'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                    'criteria'          => '()',
                                    'formula'           => '{}',
                                    'filters' => [],
                                ],
                            ]
                    ],

                    [
                        'id' => 'opportunity',
                        'name' => '商机',
                        'description' => '',
                        'list' =>
                            [
                                [
                                    'id'                => 703,
                                    'name'              => '新建商机数',
                                    'description'       => "新建的商机数（包括手动创建/自动创建）",
                                    'refer_type'        => \Constants::TYPE_OPPORTUNITY,
                                    'title'             => 'new',
                                    'time_field'        => 'create_time',
                                    'target_field'      => '',
                                    'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                    'performance_field' => 'create_user',
                                    'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                    'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                    'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                    'criteria'          => '(1)',
                                    'formula'           => '{}',
                                    'filters' => [],
                                ],
                                [
                                    'id'                => 704,
                                    'name'              => '推进商机数',
                                    'description'       => "销售阶段有变更的商机数（包含进行中、赢单、输单）",
                                    'refer_type'        => \Constants::TYPE_OPPORTUNITY,
                                    'title'             => 'new',
                                    'time_field'        => 'stage_edit_time',
                                    'target_field'      => '',
                                    'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
                                    'performance_field' => 'real_stage_change_user',
                                    'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
                                    'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
                                    'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
                                    'criteria'          => '(1)',
                                    'formula'           => '{}',
                                    'filters' => [],
                                ]
                            ]
                    ],
//                    [
//                        'id' => 'other',
//                        'name' => '其他',
//                        'description' => '',
//                        'list' =>
//                        [
//                            [
//                                'id'                => 301,
//                                'name'              => '报价单数',
//                                'description'       => "当期已报价客户数",
//                                'refer_type'        => \Constants::TYPE_QUOTATION,
//                                'title'             => '', // 是否热门
//                                'time_field'        => 'quotation_date',
//                                'target_field'      => '',
//                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
//                                'performance_field' => 'create_user',
//                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
//                                'criteria'          => '',
//                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
//                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
//                                'formula'           => '{}',
//                                'filters' => [],
//                            ],
//                            [
//                                'id'                => 302,
//                                'name'              => '报价单金额',
//                                'description'       => "当期已报价的报价单总金额",
//                                'refer_type'        => \Constants::TYPE_QUOTATION,
//                                'title'             => '', // 是否热门
//                                'time_field'        => 'quotation_date',
//                                'target_field'      => 'amount',
//                                'currency_type' => $currencyType,
//                                'calculate_rule'    => PerformanceV2Constant::CALCULATE_RULE_COUNT,
//                                'performance_field' => 'create_user',
//                                'criteria_type'     => PerformanceV2Constant::CRITERIA_TYPE_NULL,
//                                'criteria'          => '',
//                                'type'              => PerformanceV2Constant::RULE_TYPE_COMMON,
//                                'performance_type'  => PerformanceV2Rule::PERFORMANCE_TYPE_PROCESS,
//                                'formula'           => '{}',
//                                'filters' => [],
//                            ]
//                        ]
//                    ]
                ],

            ]
        ];


        // 不传refer的时候根据type获取所有的数据
        if (empty($templateType)){
            return $templateListData[$performanceType] ?? [];
        }

        if (!empty($id) && !empty($templateId)) {
            $templateListData = $templateListData[$performanceType][$templateType]?? [];
            $templateList = collect($templateListData)->where('id' ,'=',$templateId)->first();
            if (empty($templateList))return[];
            return  collect($templateList['list'])->where('id' ,'=',$id)->first() ?? [];
        }

        if (!$omsFlag)
        {
            $orderTemplateList = $templateListData[PerformanceV2Rule::PERFORMANCE_TYPE_RESULT][PerformanceV2Constant::TEMPLATE_TYPE_ORDER];
            foreach ($orderTemplateList as $subType => $subItems) {
                foreach ($subItems['list'] as $index => $item)
                {
                    if ($item['id'] == 203)
                    {
                        unset($templateListData[PerformanceV2Rule::PERFORMANCE_TYPE_RESULT][PerformanceV2Constant::TEMPLATE_TYPE_ORDER][$subType]['list'][$index]);
                    }
                }
            }
        }

        return $templateListData[$performanceType][$templateType] ?? [];
    }
}

