<?php

namespace common\library\performance_v2\completion;

use common\library\department\DepartmentRedis;
use common\library\department\Helper as DepartmentHelper;
use common\library\performance_v2\Helper;
use common\library\performance_v2\PerformanceV2Constant;
use common\library\workflow\WorkflowConstant;
use common\library\performance_v2\rule\PerformanceV2RuleList;

class CompletionService
{

    /**
     * 部门与子部门关系
     * @var array
     */
    protected $departmentSubRelationMap = [];
    /**
     * 子部门与父部门的关系
     * @var
     */
    protected $departmentParentRelationMap = [];

    protected $ruleId;
    protected $targetType;
    protected $startDate;
    protected $endDate;
    protected $clientId;
    protected $referId    = 0;
    protected $scope      = \PerformanceV2GoalsConfig::SETTING_GOAL_SCOPE_GROUP;
    protected $viewUserId;
    protected $startYear;
    protected $startMonth;
    protected $endYear;
    protected $endMonth;
    protected $ruleList   = [];
    protected $resultList = []; // 结果列表
    protected $timeType;
    protected $freezeUserIds = [];
    protected $filterFreezeUser = true;
    protected $filterDeleteUser = true;

    protected $emptyInfo  = [
        "amount"          => 0,
        "indicator_value" => 0,
        "completion_rate" => 0,
    ];

    public function __construct(int $clientId)
    {
        $this->clientId = $clientId;
    }

    public function setDepartmentSubRelationMap(array $departmentSubRelationMap = [])
    {
        $this->departmentSubRelationMap = $departmentSubRelationMap;
    }

    public function setDepartmentParentRelationMap(array $departmentParentRelationMap = [])
    {
        $this->departmentParentRelationMap = $departmentParentRelationMap;
    }

    public function setScope($scope)
    {
        $this->scope = $scope;
    }

    public function setReferId($referId)
    {
        $this->referId = $referId;
    }

    public function setTargetType(int $targetType)
    {
        $this->targetType = $targetType;
    }

    public function setTimeType($timeType)
    {
        $this->timeType = $timeType;
    }

    public function setViewUserId(int $userId)
    {
        $this->viewUserId = $userId;
    }

    public function setStartDate(string $startDate)
    {
        $this->startDate  = $startDate;
        $this->startYear  = date('Y', strtotime($startDate));
        $this->startMonth = date('m', strtotime($startDate));
    }

    public function setEndDate(string $endDate)
    {
        $this->endDate  = $endDate;
        $this->endYear  = date('Y', strtotime($endDate));
        $this->endMonth = date('m', strtotime($endDate));
    }

    public function setFreezeUserIds(array $freezeUserIds)
    {
        $this->freezeUserIds = $freezeUserIds;
    }


    public function setFilterFreezeUser(bool $b): void
    {
        $this->filterFreezeUser = $b;
    }

    public function setFilterDeleteUser(bool $filterDeleteUser) : void
    {
        $this->filterDeleteUser = $filterDeleteUser;
    }

    /**
     * @param mixed $ruleId
     */
    public function setRuleId($ruleId)
    : void
    {
        $this->ruleId = $ruleId;
    }


    public function getCompletionListNew()
    {
        // 1、准备数据
        // 特殊部门处理一下$this->referId=user_id或者department_id
        [$this->referId, $this->scope] = Helper::specialReferScopeHandle($this->clientId, $this->viewUserId, $this->referId, $this->scope);
        // 所有排行榜需要用到的目标管理规则，默认根据创建时间升序
        $ruleList = new PerformanceV2RuleList($this->clientId, $this->viewUserId);
        $ruleList->setEnableFlag(true);
        $ruleList->setOrderBy('create_time');
        $ruleList->setOrder('asc');
        $ruleList->setPerformanceType($this->targetType);
        $ruleList->setEnableFlag(true);
        $ruleList->setRuleId($this->ruleId);
        $rules = $ruleList->find();
        // 根据用户设置目标管理规则排序进行重排
        $this->ruleList = Helper::getPerformanceV2RuleSortByUserSetting($this->clientId, $this->viewUserId, $this->targetType, $rules);
        // 需要准备[部门/用户类型 => refer_id] 查询部门是有逻辑：只展示子部门，但是数据需要累加子子部门的
        // 所以这里准备了展示规则的查询的refer_id数据$GoalReferIdMapList
        // 数据展示用的owner_id数据$recordOwnerIdMapList
        list($GoalReferIdMapList, $recordOwnerIdMapList) = $this->getGoalRecordReferIdList();
        // 过滤设置不显示的人员和部门
        list($GoalReferIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER], $GoalReferIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP]) = Helper::filterUserIdsAndDepartmentIds($this->clientId, $GoalReferIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER], $GoalReferIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP]);

        list($recordOwnerIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER], $recordOwnerIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP]) = Helper::filterUserIdsAndDepartmentIds($this->clientId, $recordOwnerIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER], $recordOwnerIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP]);

        // 获取冻结账号并过滤
        if ($this->filterFreezeUser) {
            $freezeUserIds = \common\library\account\Helper::getFreezeUserIds($this->clientId);
            $GoalReferIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER] = array_diff($GoalReferIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER], $freezeUserIds);
            $recordOwnerIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER] = array_diff($recordOwnerIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER], $freezeUserIds);
        }
        // 获取删除的账号并过滤
        if ($this->filterDeleteUser) {
            $deleteUserIds = \common\library\account\Helper::getDeleteUserIds($this->clientId);
            $GoalReferIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER] = array_diff($GoalReferIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER], $deleteUserIds);
            $recordOwnerIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER] = array_diff($recordOwnerIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER], $deleteUserIds);
        }

        // 部门下没有人的部门也不展示
        // 参考 protected/library/department/DepartmentFormatter.php:747 如果部门没有用户不显示
        $hasUserDepartmentIds = \common\library\department\Helper::getClientUserDepartmentIds($this->clientId);
        $GoalReferIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP] = array_intersect($GoalReferIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP], $hasUserDepartmentIds);
        // 如果部门下用户都不显示(隐藏)了，那么部门也不展示，虽然部门不展示，但是数据还是要查的，比如可能他的某子部门的子子部门
        // 需求原话:如果A部门下的3个成员都被隐藏了，那么在目标完成情况中的人员范围选择其中 也隐藏A部门
        // 所以$recordOwnerIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP] 这个数据不用过滤
        $checkDepartmentIds = array_unique(array_merge($GoalReferIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP], $recordOwnerIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP]));
        if (!empty($checkDepartmentIds)) {
            $departmentRedis = new DepartmentRedis();
            $departmentMapUserIds = $departmentRedis->getDepartmentUserRecursiveMap($this->clientId, $checkDepartmentIds);
            $noUserDepartmentIds = [];
            foreach ($departmentMapUserIds as $departId => $thisDepartmentUserIds) {
                // 没有交集，就是说本次所有用户的都被隐藏等情况屏蔽了，那么我们部门也不展示了
                if (empty($thisDepartmentUserIds) || !is_array($thisDepartmentUserIds)) {
                    $noUserDepartmentIds[] = $departId;
                } else if (!array_intersect($thisDepartmentUserIds, $GoalReferIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER])) {
                    $noUserDepartmentIds[] = $departId;
                }
            }
            if (!empty($noUserDepartmentIds)) {
                $GoalReferIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP] = array_diff($GoalReferIdMapList[PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP], $noUserDepartmentIds);
            }
        }


        $goalsList = [];
        // 根据 $GoalReferIdMapList 去 tbl_performance_v2_goals 表数据获取目标
        foreach ($GoalReferIdMapList as $scope => $referIds) {
            // 找到符合条件的目标列表
            if ($this->targetType == \PerformanceV2Goals::TARGET_RESULT_GOAL) {
                $goalsList[$scope] = Helper::getResultTargetGoalByTimeType($this->clientId, $this->viewUserId, $this->ruleList, $this->startDate, $this->endDate, $this->timeType, $referIds, $scope);
            } else {
                $goalsList[$scope] = Helper::getProcessGoalByTimeType($this->clientId, $this->viewUserId, $this->ruleList, $this->startDate, $this->endDate, $referIds);
            }
        }
        // 根据 $recordOwnerIdMapList 去 tbl_performance_v2_record 表数据获取完成
        $recordsList = [];
        $unDistinctField = 'DISTINCT ON( client_id, rule_id, refer_id, owner_id ) client_id,refer_id,owner_id,rule_id,refer_type,record_type,indicator_value,account_date,create_time,update_time,company_id,record_id ';
        foreach ($recordOwnerIdMapList as $scope => $ownerIds) {
            $recordsList[$scope] = Helper::getRecordAggResult($this->ruleList, $this->clientId, $this->viewUserId, $this->startDate, $this->endDate, $ownerIds, $unDistinctField);
        }

        // 2、格式化
        return $this->newFormate($GoalReferIdMapList, $recordOwnerIdMapList, $goalsList, $recordsList);
    }

    private function newFormate($GoalReferIdMapList, $recordOwnerIdMapList, $goalsList, $recordsList)
    {
        $result = [
            PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP] => ["list" => []],
            PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER]  => ["list" => []]
        ];
        if (empty($this->ruleList)) {
            return $result;
        }

        // 初始化默认值设置
        foreach ($this->ruleList as $ruleInfo) {
            $ruleId                          = $ruleInfo["rule_id"];
            $ruleInfo["currency"]            = Helper::getCurrencyByTargetField($this->clientId, $ruleInfo['refer_type'], $ruleInfo["target_field"]);
            $itermInfo                       = [
                "rule_info" => $ruleInfo,
                "list"      => []
            ];
            $itermInfo                       = array_merge($this->emptyInfo, $itermInfo);
            $result["user"]["list"][$ruleId] = $itermInfo;
            $result["department"]["list"][$ruleId] = $itermInfo;
            // 设置目标值默认
            foreach ($GoalReferIdMapList as $scope => $goalReferIds) {
                $scopeName = PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[$scope];
                foreach ($goalReferIds as $referId) {
                    $result[$scopeName]["list"][$ruleId]['list'][$referId] = array_merge($this->emptyInfo,['refer_id' => $referId,'performance_rule_id' => $ruleId,'scope' => $scope]);
                }
            }
        }

        // 设置目标值
        foreach ($goalsList as $scope => $goalItem) {
            foreach ($goalItem as $goal) {
                $tmpScope = PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[$goal['scope']];
                if (isset($result[$tmpScope]["list"][$goal['performance_rule_id']])) {
                    $result[$tmpScope]["list"][$goal['performance_rule_id']]['amount'] += $goal['amount'];
                }

                if (!isset($result[$tmpScope]["list"][$goal['performance_rule_id']]['list'][$goal['refer_id']])) {
                    $result[$tmpScope]["list"][$goal['performance_rule_id']]['list'][$goal['refer_id']] = array_merge($this->emptyInfo, $goal);
                } else {
                    $result[$tmpScope]["list"][$goal['performance_rule_id']]['list'][$goal['refer_id']]['amount'] = $goal['amount'];
                }
            }
        }

        // 规则、人员、部门与refer_id关系
        $ownerReferList = [];
        // 根据ownerid做去重处理
        $ownerRecordMap = [];
        $ruleList       = \ArrayUtil::index($this->ruleList, 'rule_id');
        foreach ($recordsList as $scope => $recordItem) {
            foreach ($recordItem as $record) {
                $ruleId = $record['rule_id'];
                $ownerId = $record['owner_id'];
                $referId = $record['refer_id'];
                $ruleInfo = $ruleList[$ruleId] ?? [];

                if (empty($ruleInfo)) {
                    continue;
                }
                $ownerReferList[PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[$scope]][$record['rule_id']][$record['owner_id']][] = $record['refer_id'];

                $needDistinctWithOwnerId = Helper::checkNeedDistinctWithOwnerId($ruleInfo);

                // 不需要根据referID和ownerid去重
                if ($needDistinctWithOwnerId) {
                    if (isset($ownerRecordMap[$ruleId][$referId][$ownerId])) {
                        $ownerRecordMap[$ruleId][$referId][$ownerId]['indicator_value'] += $record['indicator_value'];
                    } else {
                        $ownerRecordMap[$ruleId][$referId][$ownerId] = $record;
                    }
                } else {
                    if (isset($ownerRecordMap[$ruleId][$referId][$ownerId])) {
                        continue;
                    } else {
                        $ownerRecordMap[$ruleId][$referId][$ownerId] = $record;
                    }
                }
            }
        }

        // 计算完成情况
        foreach ($ownerRecordMap as $referList) {
            foreach ($referList as $recordList) {
                foreach ($recordList as $record){
                    // 将子部门的完整值计算到父部门上
                    if (
                        isset($this->departmentParentRelationMap[$record['owner_id']]) &&
                        isset($result[PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[$record['record_type']]]["list"][$record['rule_id']]['list'][$this->departmentParentRelationMap[$record['owner_id']]])
                    ) {
                        $result[PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[$record['record_type']]]["list"][$record['rule_id']]['list'][$this->departmentParentRelationMap[$record['owner_id']]]['indicator_value'] += $record['indicator_value'];
                    }

                    if (
                        isset($result[PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[$record['record_type']]]["list"][$record['rule_id']]['list'][$record['owner_id']])
                    ) {
                        $result[PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[$record['record_type']]]["list"][$record['rule_id']]["list"][$record['owner_id']]['indicator_value'] += $record['indicator_value'];
                    }
                }
            }
        }

        $departments = \ArrayUtil::index(Helper::getDepartmentList($this->clientId, ['id', 'name', 'parent_id']), 'id');
        $users       = \ArrayUtil::index(Helper::getUserList($this->clientId, ['user_id', 'nickname']), 'user_id');
        // 计算某个规则中部门或者人员的完成率
        foreach ($result as $scopeStr => $recordScopeList) {
            $firstRuleFlag     = null;
            $firstSortReferIds = [];
            foreach ($recordScopeList["list"] as $ruleId => $iterms) {
                // 挑出第一个目标对象id
                if ($firstRuleFlag === null) {
                    $firstRuleFlag = $ruleId;
                }
                // 展示部门或用户信息
                foreach ($iterms["list"] as $index => $iterm) {
                    // 计算完成率
                    $result[$scopeStr]["list"][$ruleId]["list"][$index]['completion_rate'] = $iterm["amount"] == 0 ? 0 : round($iterm['indicator_value'] / $iterm['amount'] * 100, 2);

                    if ($scopeStr == PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP]) {
                        $result[$scopeStr]["list"][$ruleId]["list"][$index]['department_info'] = isset($departments[$iterm['refer_id']]) ? $departments[$iterm['refer_id']] : [];
                    }

                    if ($scopeStr == PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER]) {
                        $result[$scopeStr]["list"][$ruleId]["list"][$index]['user_info'] = isset($users[$iterm['refer_id']]) ? $users[$iterm['refer_id']] : [];
                    }
                }
                // 根据第一个目标的完成率倒序排序
                if ($ruleId == $firstRuleFlag) {
                    // 比较两个值的大小决定排序先后
                    uasort($result[$scopeStr]["list"][$ruleId]["list"], function ($itermA, $itermB) {
                        return $itermA['completion_rate'] > $itermB['completion_rate'] ? -1 : 1;
                    });

                    // 排序后获取referid，指导后面的数据排序
                    $firstSortReferIds = array_column($result[$scopeStr]["list"][$ruleId]["list"], 'refer_id');

                } else {
                    // 根据第一个目标的排序结果来排序,保证数据的正确性
                    if (!empty($firstSortReferIds)) {
                        $tmpRecode = [];
                        foreach ($firstSortReferIds as $referId) {
                            if (isset($result[$scopeStr]["list"][$ruleId]["list"][$referId])) {
                                $tmpRecode[$referId] = $result[$scopeStr]["list"][$ruleId]["list"][$referId];
                            }
                        }
                        $result[$scopeStr]["list"][$ruleId]["list"] = $tmpRecode;
                    }
                }
                $result[$scopeStr]["list"][$ruleId]["list"] = array_values($result[$scopeStr]["list"][$ruleId]["list"]);
            }
            $result[$scopeStr]["list"] = array_values($result[$scopeStr]["list"]);
        }

        unset($iterms);

        // 跳转准备
        $reportKey        =  __CLASS__ . ":";
        $groupData        = [];
        $prefix           = "primary_key:";
        $reportGroupCache = new \common\library\statistics\foundation\store\ReportGroupCache($this->viewUserId);
        $reportGroupCache->setReportKey($reportKey);
        $reportGroupCache->setReportUniqueKey($reportKey);

        // 计算规则的完成值和完成率
        foreach ($result as $scopeStr => $recordScopeList) {
            foreach ($recordScopeList["list"] as $index => $iterms) {
                if (empty($iterms['list'])) {
                    continue;
                }
                $amount = round(array_sum(array_column($iterms["list"], 'amount')), 2);
                $result[$scopeStr]['list'][$index]["amount"]          = $amount;
                $indicatorValue = round(array_sum(array_column($iterms["list"], 'indicator_value')), 2);
                $result[$scopeStr]['list'][$index]["indicator_value"] = $indicatorValue;
                $result[$scopeStr]['list'][$index]["completion_rate"] = intval($amount) == 0 ? 0 : round($indicatorValue / $amount * 100, 2);

                $ruleInfo = $iterms['rule_info'];

                // 对部门或成员支持数据跳转
                foreach ($iterms['list'] as $referIdKey => $referValue){
                    // 在指定枚举的考核对象中才有跳转功能
                    if (isset(WorkflowConstant::CAN_JUMP_REFER_TYPE_ENUM[$ruleInfo['refer_type']])) {
                        $result[$scopeStr]['list'][$index]['list'][$referIdKey]['can_jump_flag'] = 1;
                        // 给规则添加数据跳转功能
                        $mainId                = WorkflowConstant::CAN_JUMP_REFER_TYPE_ENUM[$ruleInfo['refer_type']] ?? $ruleInfo['rule_id'];
                        $uniqueKey             = "{$prefix}{$mainId}-{$ruleInfo['rule_id']}-{$scopeStr}-{$referValue['refer_id']}";
                        $goalScope = PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[$referValue['scope']];
                        // 本次改：部门的情况，累计子部门的，规则同一个规则同一个部门的refer_id号
                        // 超过一万个refer_id，超过部分不要了，会存在不准确的问题
                        if ($scopeStr == PerformanceV2Constant::DEPARTMENT_AGG_TYPE_DEPARTMENT) {
                            // 如果部门没有用户则删除
                            // 本身部门的
                            $tmpOwnerReferList = array_unique($ownerReferList[$goalScope][$ruleInfo['rule_id']][$referValue['refer_id']] ?? []);
                            // 子部门的refer_id号
                            foreach ($this->departmentSubRelationMap[$referValue['refer_id']] ?? [] as $departmentId) {
                                if (count($tmpOwnerReferList) > 10000) {
                                    break;
                                }
                                $tmpSubOwnerReferList = $ownerReferList[$goalScope][$ruleInfo['rule_id']][$departmentId] ?? [];
                                $tmpOwnerReferList = array_unique(array_merge($tmpOwnerReferList, $tmpSubOwnerReferList));
                            }
                            $groupData[$uniqueKey] = $tmpOwnerReferList;
                        } else {
                            $tmpOwnerReferList = array_unique($ownerReferList[$goalScope][$ruleInfo['rule_id']][$referValue['refer_id']] ?? []);
                            if (count($tmpOwnerReferList) > 10000) {
                                $tmpOwnerReferList = array_chunk($tmpOwnerReferList, 10000);
                                $groupData[$uniqueKey] = $tmpOwnerReferList[0] ?? [];
                            } else {
                                $groupData[$uniqueKey] = $tmpOwnerReferList;
                            }
                        }
                    }
                }
            }
        }

        unset($iterms);

        // 设置跳转
        $cacheGroupMap = $reportGroupCache->setData($groupData);
        foreach ($result as $scopeStr => &$recordScopeList){
            foreach ($recordScopeList["list"] as &$iterms){
                if (empty($iterms['list'])) continue;
                $ruleInfo = $iterms['rule_info'];
                // 对部门或成员支持数据跳转
                foreach ($iterms['list'] as $referIdKey => &$referValue) {
                    $mainId                               = WorkflowConstant::CAN_JUMP_REFER_TYPE_ENUM[$ruleInfo['refer_type']] ?? $ruleInfo['rule_id'];
                    $uniqueKey                            = "{$prefix}{$mainId}-{$ruleInfo['rule_id']}-{$scopeStr}-{$referValue['refer_id']}";
                    $referValue['report_item_unique_key'] = isset($cacheGroupMap[$uniqueKey]) ? $cacheGroupMap[$uniqueKey] : '';
                }
            }
        }

        unset($recordScopeList, $iterms);
        // 删掉过程目标的部门信息
        if ($this->targetType == \PerformanceV2Goals::TARGET_PROCESS_GOAL) {
            $result['department']['list'] = [];
        }
        return $result;
    }

    /**
     * 获取目标规则refer_id和绩效表的owner_id
     * 部门情况下-目标规则 和 绩效 会有不一样，因为绩效需要获取子子部门
     * @return \array[][]
     */
    private function getGoalRecordReferIdList()
    {
        $resultGoals = [
            // 部门
            PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP => [],
            // 用户
            PerformanceV2Constant::SETTING_GOAL_SCOPE_USER => [],
        ];
        $resultRecords = [
            // 部门
            PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP => [],
            // 用户
            PerformanceV2Constant::SETTING_GOAL_SCOPE_USER => [],
        ];
        // 如果是查询部门，需要展示指定部门下的子部门
        $departmentRedis = new DepartmentRedis();
        // 补充未分配人员
        $UndistributedUserIds = \common\library\account\Helper::getUndistributedUserIds($this->clientId);
        $UndistributedUserIds = is_array($UndistributedUserIds) ? $UndistributedUserIds : [];
        // 结果目标
        if ($this->targetType == \PerformanceV2Goals::TARGET_RESULT_GOAL) {
            // 如果是结果目标，且指定为部门,需要展示所有的子部门数据
            if ($this->scope == PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP) {
                // 展示部门的目标值和完成值
                $departmentIds = DepartmentHelper::getFirstChildrenIds($this->clientId,$this->referId);
                $resultGoals[PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP] = $departmentIds;

                // 获取部门下的子子部门 获取record数据用
                $resultDepartmentIds = $departmentSubRelationMap = $departmentParentRelationMap = [];
                foreach ($departmentIds as $departmentId) {
                    $subDepartmentIds = DepartmentHelper::getChildrenIds($this->clientId, $departmentId);
                    foreach ($subDepartmentIds as $subDepartmentId) {
                        $departmentParentRelationMap[$subDepartmentId] = $departmentId;
                    }
                    $departmentSubRelationMap[$departmentId] = is_array($subDepartmentIds) ? $subDepartmentIds : [];
                    $resultDepartmentIds = array_merge($resultDepartmentIds, $departmentSubRelationMap[$departmentId], [$departmentId]);
                }
                // 去重复
                $resultRecords[PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP] = array_unique($resultDepartmentIds);
                // 记录下部门与子部门的关系，下面format方法有用
                $this->setDepartmentSubRelationMap($departmentSubRelationMap);
                // 记录下子部门与父部门的关系
                $this->setDepartmentParentRelationMap($departmentParentRelationMap);
                // 同时还要展示目标下所有成员的数据
                $departmentUserIds = array_unique($departmentRedis->getUserList($this->clientId, $this->referId));
                // 我的企业的时候需要把未分配的加入
                if ($this->referId == 0 && !empty($UndistributedUserIds)) {
                    $departmentUserIds = array_unique(array_merge($departmentUserIds, $UndistributedUserIds));
                }
                $resultRecords[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER] = $resultGoals[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER] = $departmentUserIds;
            } else {
                // 展示具体成员的目标值和完成值
                $resultGoals[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER] = is_array($this->referId) ? $this->referId : [$this->referId];
                $resultRecords[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER] = $resultGoals[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER];
            }
        } elseif ($this->targetType == \PerformanceV2Goals::TARGET_PROCESS_GOAL) {
            // 过程目标只需要展示成员的数据
            $referIds = is_array($this->referId) ? $this->referId : [$this->referId];
            if ($this->scope == PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP) {
                $referIds = $departmentRedis->getUserList($this->clientId, $this->referId);
                // 我的企业的时候需要把未分配的加入
                if ($this->referId == 0 && !empty($UndistributedUserIds)) {
                    $referIds = array_unique(array_merge($referIds, $UndistributedUserIds));
                }
            }
            // 展示过程目标成员的目标值和完成值
            $resultGoals[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER] = $referIds;
            $resultRecords[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER] = $referIds;
        }
        // 缓存里面的用户会存在不是企业内部的用户，很奇怪，为了保险做多一次过滤
        $clientUserIds = \common\library\account\Helper::getUserIds($this->clientId);
        if (!empty($resultGoals[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER]) && is_array($clientUserIds)) {
            $resultGoals[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER] = array_intersect($clientUserIds, $resultGoals[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER]);
        }
        if (!empty($resultRecords[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER]) && is_array($clientUserIds)) {
            $resultRecords[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER] = array_intersect($clientUserIds, $resultRecords[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER]);
        }
        return [$resultGoals, $resultRecords];
    }

    public function getCompletionList()
    {
        [$this->referId, $this->scope] = Helper::specialReferScopeHandle($this->clientId, $this->viewUserId, $this->referId, $this->scope);
        // 1.找到符合条件的所有规格列表
        $ruleList = new \common\library\performance_v2\rule\PerformanceV2RuleList($this->clientId, $this->viewUserId);
        $ruleList->setEnableFlag(true);
        $ruleList->setOrderBy('create_time');
        $ruleList->setOrder('asc');
        $ruleList->setPerformanceType($this->targetType);
        $ruleList->setEnableFlag(true);
        $ruleList->setRuleId($this->ruleId);
        $ruleList->setFields('rule_id,name,description,enable_flag,performance_type,target_field,refer_type,time_field,calculate_rule,performance_field');
        $this->ruleList = $ruleList->find();

        $result         = [
            PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP] => ["list" => []],
            PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER]  => ["list" => []]
        ];

        if (empty($this->ruleList)) {
            return $result;
        }

        $baseInfo = $this->emptyInfo;

        // 初始化默认值
        foreach ($this->ruleList as $ruleInfo) {
            $ruleInfo["currency"]            = Helper::getCurrencyByTargetField($this->clientId, $ruleInfo['refer_type'], $ruleInfo["target_field"]);
            $itermInfo                       = [
                "rule_info" => $ruleInfo,
                "list"      => []
            ];
            $itermInfo                       = array_merge($baseInfo, $itermInfo);
            $ruleId                          = $ruleInfo["rule_id"];
            $ruleInfo['indicator_value']     = 0;
            $ruleInfo['completion_rate']     = 0;
            $result["user"]["list"][$ruleId] = $itermInfo;

//            if ($this->targetType == \PerformanceV2Goals::TARGET_RESULT_GOAL) {
                $result["department"]["list"][$ruleId] = $itermInfo;
//            }
        }

        // 如果是查询部门，需要展示指定部门下的子部门
        $departmentRedis = new DepartmentRedis();

        // 结果目标
        if ($this->targetType == \PerformanceV2Goals::TARGET_RESULT_GOAL)
        {
            // 如果是结果目标，且指定为部门,需要展示所有的子部门数据
            if ($this->scope == PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP) {
                // 展示部门的目标值和完成值
                $departmentIds = DepartmentHelper::getFirstChildrenIds($this->clientId,$this->referId);
                if(!empty($departmentIds)){
                    $this->showGoals($result, $departmentIds, $this->scope);
                    $this->showIndicator($result, $departmentIds, PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP);
                }

                // 同时还要展示目标下所有成员的数据
                $departmentUserIds = array_unique($departmentRedis->getUserList($this->clientId, $this->referId));
                if(!empty($departmentUserIds)){
                    $this->showGoals($result, $departmentUserIds, PerformanceV2Constant::SETTING_GOAL_SCOPE_USER);
                    $this->showIndicator($result, $departmentUserIds);
                }
            } else {
                // 展示具体成员的目标值和完成值
                $this->showGoals($result, $this->referId, $this->scope);
                $this->showIndicator($result, $this->referId);
            }
        } elseif ($this->targetType == \PerformanceV2Goals::TARGET_PROCESS_GOAL) {
            // 过程目标只需要展示成员的数据
            $referIds = $this->referId;

            if($this->scope == PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP){
                $referIds = $departmentRedis->getUserList($this->clientId, $this->referId);
            }

            // 展示过程目标成员的目标值和完成值
            $this->showGoals($result, $referIds, PerformanceV2Constant::SETTING_GOAL_SCOPE_USER);
            $this->showIndicator($result, $referIds);
        }

        // 数据格式化并排序
        $this->formate($result);

        return $result;
    }

    /**
     * 展示目标值
     * time: 9:12 PM
     * user: huagongzi
     * @param array $result
     * @param array $referIds
     * @param int $scope
     * @return void
     */
    private function showGoals(array &$result, $referIds, int $scope)
    {
        $referIds  = is_array($referIds) ? $referIds : [$referIds];
        $emptyInfo = $this->emptyInfo;
        $scopeName = PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[$scope];
        if ($this->freezeUserIds)
        {
            $referIds = array_filter($referIds,function ($v)
            {
                return !in_array($v,$this->freezeUserIds);
            });
        }

        // 给默认值
        foreach ($this->ruleList as $ruleItem){
            foreach ($referIds as $referIdItem){
                $result[$scopeName]["list"][$ruleItem['rule_id']]['list'][$referIdItem] = array_merge($emptyInfo,['refer_id' => $referIdItem,'performance_rule_id' => $ruleItem['rule_id'],'scope' => $scope]);
            }
        }

        // 找到符合条件的目标列表
        if ($this->targetType == \PerformanceV2Goals::TARGET_RESULT_GOAL) {
            $goalList = Helper::getResultTargetGoalByTimeType($this->clientId, $this->viewUserId, $this->ruleList, $this->startDate, $this->endDate, $this->timeType, $referIds, $scope);
        } else {
            $goalList = Helper::getProcessGoalByTimeType($this->clientId, $this->viewUserId, $this->ruleList, $this->startDate, $this->endDate, $referIds);
        }

        foreach ($goalList as $goal) {
            $tmpScope = PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[$goal['scope']];

            if (isset($result[$tmpScope]["list"][$goal['performance_rule_id']])) {
                $result[$tmpScope]["list"][$goal['performance_rule_id']]['amount'] += $goal['amount'];
            }

            if (!isset($result[$tmpScope]["list"][$goal['performance_rule_id']]['list'][$goal['refer_id']])) {
                $result[$tmpScope]["list"][$goal['performance_rule_id']]['list'][$goal['refer_id']] = array_merge($emptyInfo, $goal);
            } else {
                $result[$tmpScope]["list"][$goal['performance_rule_id']]['list'][$goal['refer_id']]['amount'] = $goal['amount'];
            }
        }
    }

    /**
     * 展示完成值
     * time: 9:12 PM
     * user: huagongzi
     * @param array $result
     * @param array $referIds
     * @return void
     */
    private function showIndicator(array &$result, $referIds, $scope = PerformanceV2Constant::SETTING_GOAL_SCOPE_USER)
    {
        $referIds  = is_array($referIds) ? $referIds : [$referIds];
        $parentMap = [];
        if ($this->freezeUserIds)
        {
            $referIds = array_filter($referIds,function ($v)
            {
                return !in_array($v,$this->freezeUserIds);
            });
        }

        // 如果是部门，完成值需要包含子部门的完成值
        if($scope == PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP){
            $departmentSubRelationMap = [];
            foreach ($referIds as $referId){
                $subDepartmentIds = DepartmentHelper::getChildrenIds($this->clientId, $referId);
                $referIds         = array_merge($subDepartmentIds, $referIds);

                foreach ($subDepartmentIds as $subDepartmentId){
                    $parentMap[$subDepartmentId] = $referId;
                }
                $departmentSubRelationMap[$referId] = is_array($subDepartmentIds) ? $subDepartmentIds : [];
            }
            // 记录下部门与子部门的关系，下面format方法有用
            $this->setDepartmentSubRelationMap($departmentSubRelationMap);
        }

        // 4.获取完成情况
        $unDistinctField = 'DISTINCT ON( client_id, rule_id, refer_id, owner_id ) client_id,refer_id,owner_id,rule_id,refer_type,record_type,indicator_value,account_date,create_time,update_time,company_id,record_id ';
        $recordList      = Helper::getRecordAggResult($this->ruleList, $this->clientId, $this->viewUserId, $this->startDate, $this->endDate, $referIds, $unDistinctField);

        // 根据ownerid做去重处理
        $ownerRecordMap = [];
        $ruleList       = \ArrayUtil::index($this->ruleList, 'rule_id');
        foreach ($recordList as $record){
            $ruleId   = $record['rule_id'];
            $ownerId  = $record['owner_id'];
            $referId  = $record['refer_id'];
            $ruleInfo = $ruleList[$ruleId] ?? [];

            if(empty($ruleInfo)){
                continue;
            }

            $needDistinctWithOwnerId = Helper::checkNeedDistinctWithOwnerId($ruleInfo);

            // 不需要根据referID和ownerid去重
            if($needDistinctWithOwnerId){
                if(isset($ownerRecordMap[$ruleId][$referId][$ownerId])){
                    $ownerRecordMap[$ruleId][$referId][$ownerId]['indicator_value'] += $record['indicator_value'];
                } else {
                    $ownerRecordMap[$ruleId][$referId][$ownerId] = $record;
                }
            } else {
                if(isset($ownerRecordMap[$ruleId][$referId][$ownerId])){
                    continue;
                } else {
                    $ownerRecordMap[$ruleId][$referId][$ownerId] = $record;
                }
            }
        }

        // 计算完成情况
        foreach ($ownerRecordMap as $ruleId => $referList) {
            foreach ($referList as $recordList) {
                foreach ($recordList as $record){
                    // 将子部门的完整值计算到父部门上
                    if(isset($parentMap[$record['owner_id']]) && isset($result[PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[$record['record_type']]]["list"][$record['rule_id']]['list'][$parentMap[$record['owner_id']]])){
                        $result[PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[$record['record_type']]]["list"][$record['rule_id']]['list'][$parentMap[$record['owner_id']]]['indicator_value'] += $record['indicator_value'];
                    }

                    if (isset($result[PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[$record['record_type']]]["list"][$record['rule_id']]['list'][$record['owner_id']])) {
                        $result[PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[$record['record_type']]]["list"][$record['rule_id']]["list"][$record['owner_id']]['indicator_value'] += $record['indicator_value'];
                    }
// 产品在需求上只需要传入refer_id的子部门，并且需要累加子子部门的完成情况。但是不用显示出来子子部门
// 只累加子子部门到父部门，但是不在$result中新增子子部门
//                    else {
//                        $data                    = $this->emptyInfo;
//                        $data["scope"]           = $record['record_type'];
//                        $data["refer_id"]        = $record['owner_id'];
//                        $data["indicator_value"] = $record['indicator_value'];
//                        $data["client_id"]       = $this->clientId;
//
//                        $result[PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[$record['record_type']]]["list"][$record['rule_id']]["list"][$record['owner_id']] = $data;
//                    }
                }
            }
        }
    }


    /**
     * 结果数据排序并格式化
     * time: 9:12 PM
     * user: huagongzi
     * @param array $result
     * @return void
     */
    private function formate(array &$result)
    {
        $firstRuleFlag     = null;
        $firstSortReferIds = [];

        $departments = \ArrayUtil::index(Helper::getDepartmentList($this->clientId, ['id', 'name', 'parent_id']), 'id');
        $users       = \ArrayUtil::index(Helper::getUserList($this->clientId, ['user_id', 'nickname']), 'user_id');

        // 组装数据
        foreach ($result as $scope => $record) {
            foreach ($record["list"] as $ruleId => $iterms) {

                // 挑出第一个目标对象id
                if ($firstRuleFlag === null) {
                    $firstRuleFlag = $ruleId;
                }

                // 展示部门或用户信息
                foreach ($iterms["list"] as $index => $iterm) {

                    // 计算完成率
                    $result[$scope]["list"][$ruleId]["list"][$index]['completion_rate'] = $iterm["amount"] == 0 ? 0 : round($iterm['indicator_value'] / $iterm['amount'] * 100, 2);

                    if ($scope == PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP]) {
                        $result[$scope]["list"][$ruleId]["list"][$index]['department_info'] = isset($departments[$iterm['refer_id']]) ? $departments[$iterm['refer_id']] : [];
                    }

                    if ($scope == PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER]) {
                        $result[$scope]["list"][$ruleId]["list"][$index]['user_info'] = isset($users[$iterm['refer_id']]) ? $users[$iterm['refer_id']] : [];
                    }
                }

                // 根据第一个目标的完成率倒序排序
                if ($ruleId == $firstRuleFlag) {

                    // 比较两个值的大小决定排序先后
                    uasort($result[$scope]["list"][$ruleId]["list"], function ($itermA, $itermB) {
                        return $itermA['completion_rate'] > $itermB['completion_rate'] ? -1 : 1;
                    });

                    // 排序后获取referid，指导后面的数据排序
                    $firstSortReferIds = array_column($result[$scope]["list"][$ruleId]["list"], 'refer_id');

                } else {

                    // 根据第一个目标的排序结果来排序,保证数据的正确性
                    if (!empty($firstSortReferIds)) {
                        $tmpRecode = [];

                        foreach ($firstSortReferIds as $referId) {
                            if (isset($result[$scope]["list"][$ruleId]["list"][$referId])) {
                                $tmpRecode[$referId] = $result[$scope]["list"][$ruleId]["list"][$referId];
                            }
                        }

                        $result[$scope]["list"][$ruleId]["list"] = $tmpRecode;
                    }
                }

                $result[$scope]["list"][$ruleId]["list"] = array_values($result[$scope]["list"][$ruleId]["list"]);
            }
            $result[$scope]["list"] = array_values($result[$scope]["list"]);
        }

        // 数据跳转
        $params                     = [];
        $params['ruleId']           = array_filter(array_merge((is_array($this->ruleId) ? $this->ruleId : [$this->ruleId ?? 0]), array_column($this->ruleList ?? [], 'rule_id')));
        $params['startAccountDate'] = $this->startDate;
        $params['endAccountDate']   = $this->endDate;
        $params['skipOwnerCheck']   = true;
        $distinctField              = '( rule_id,refer_id,owner_id) client_id,rule_id,refer_id,owner_id';
        $orderBy                    = 'order by rule_id asc,owner_id asc';
        // 规则、部门与refer_id关系
        $ownerReferList             = [];

        // 部门对应的惟一referids列表
        if($this->targetType == \PerformanceV2Goals::TARGET_RESULT_GOAL && $this->scope == PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP){
            $params['recordType']  = PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP;
            $departmentReferidsMap = Helper::getRuleDistinctReferIds($this->clientId, $this->viewUserId,$params,$distinctField,$orderBy);

            foreach ($departmentReferidsMap as $referRecord){
                $ownerReferList[PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP]][$referRecord['rule_id']][$referRecord['owner_id']][] = $referRecord['refer_id'];
            }
        }

        // 成员对应的惟一referids列表
        $params['recordType']  = PerformanceV2Constant::SETTING_GOAL_SCOPE_USER;
        $userReferIdsMap       = Helper::getRuleDistinctReferIds($this->clientId, $this->viewUserId,$params,$distinctField,$orderBy);
        foreach ($userReferIdsMap as $referRecord){
            $ownerReferList[PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[PerformanceV2Constant::SETTING_GOAL_SCOPE_USER]][$referRecord['rule_id']][$referRecord['owner_id']][] = $referRecord['refer_id'];
        }

        $reportKey        =  __CLASS__ . ":";
        $groupData        = [];
        $prefix           = "primary_key:";
        $reportGroupCache = new \common\library\statistics\foundation\store\ReportGroupCache($this->viewUserId);
        $reportGroupCache->setReportKey($reportKey);
        $reportGroupCache->setReportUniqueKey($reportKey);

        // 参考 protected/library/department/DepartmentFormatter.php:747
        $clientDepartmentIds = \common\library\department\Helper::getClientUserDepartmentIds($this->clientId);

        // 求规则的完成值和完成率
        foreach ($result as $referType => $referTypeList){
            foreach ($referTypeList["list"] as $index => $ruleItemList){

                if(empty($ruleItemList['list'])){
                    continue;
                }

                $result[$referType]['list'][$index]["amount"]          = round(array_sum(array_column($ruleItemList["list"], 'amount')), 2);
                $result[$referType]['list'][$index]["indicator_value"] = round(array_sum(array_column($ruleItemList["list"], 'indicator_value')), 2);
                $result[$referType]['list'][$index]["completion_rate"] = intval($ruleItemList["amount"]) == 0 ? 0 : round($ruleItemList["indicator_value"] / $ruleItemList["amount"] * 100, 2);

                $ruleInfo = $ruleItemList['rule_info'];

                $hasDeleteDepartment = false;
                // 对部门或成员支持数据跳转
                foreach ($ruleItemList['list'] as $referIdKey => $referValue){
                    // 如果部门没有用户则不进行展示部门
                    if ($referType == PerformanceV2Constant::DEPARTMENT_AGG_TYPE_DEPARTMENT) {
                        if (!in_array($referValue['refer_id'], $clientDepartmentIds)) {
                            $hasDeleteDepartment = true;
                            unset($result[$referType]['list'][$index]['list'][$referIdKey]);
                            continue;
                        }
                    }
                    // 在指定枚举的考核对象中才有跳转功能
                    if(isset(WorkflowConstant::CAN_JUMP_REFER_TYPE_ENUM[$ruleInfo['refer_type']])){
                        $result[$referType]['list'][$index]['list'][$referIdKey]['can_jump_flag'] = 1;

                        // 给规则添加数据跳转功能
                        $mainId                = WorkflowConstant::CAN_JUMP_REFER_TYPE_ENUM[$ruleInfo['refer_type']] ?? $ruleInfo['rule_id'];
                        $uniqueKey             = "{$prefix}{$mainId}-{$ruleInfo['rule_id']}-{$referType}-{$referValue['refer_id']}";
                        $goalScope = PerformanceV2Constant::GOAL_SCOPE_FIELD_MAP[$referValue['scope']];
                        // 本次改：部门的情况，累计子部门的，规则同一个规则同一个部门的refer_id号
                        // 超过一万个refer_id，超过部分不要了，会存在不准确的问题
                        if ($referType == PerformanceV2Constant::DEPARTMENT_AGG_TYPE_DEPARTMENT) {
                            // 如果部门没有用户则删除
                            // 本身部门的
                            $tmpOwnerReferList = array_unique($ownerReferList[$goalScope][$ruleInfo['rule_id']][$referValue['refer_id']] ?? []);
                            // 子部门的refer_id号
                            foreach ($this->departmentSubRelationMap[$referValue['refer_id']] ?? [] as $departmentId) {
                                if (count($tmpOwnerReferList) > 10000) {
                                    break;
                                }
                                $tmpSubOwnerReferList = $ownerReferList[$goalScope][$ruleInfo['rule_id']][$departmentId] ?? [];
                                $tmpOwnerReferList = array_unique(array_merge($tmpOwnerReferList, $tmpSubOwnerReferList));
                            }
                            $groupData[$uniqueKey] = $tmpOwnerReferList;
                        } else {
                            $tmpOwnerReferList = array_unique($ownerReferList[$goalScope][$ruleInfo['rule_id']][$referValue['refer_id']] ?? []);
                            if (count($tmpOwnerReferList) > 10000) {
                                $tmpOwnerReferList = array_chunk($tmpOwnerReferList, 10000);
                                $groupData[$uniqueKey] = $tmpOwnerReferList[0] ?? [];
                            } else {
                                $groupData[$uniqueKey] = $tmpOwnerReferList;
                            }
                        }
                    }
                }
                // 由于上面删除部门，回出现下标为0 2 这酱紫，回调到0 1 这种顺序下标
                if ($hasDeleteDepartment) {
                    $result[$referType]['list'][$index]['list'] = array_values($result[$referType]['list'][$index]['list']);
                }
            }
        }

        $cacheGroupMap = $reportGroupCache->setData($groupData);
        foreach ($result as $referType => &$referTypeList){
            foreach ($referTypeList["list"] as $index => &$ruleItemList){

                if(empty($ruleItemList['list'])){
                    continue;
                }

                $ruleInfo = $ruleItemList['rule_info'];

                // 对部门或成员支持数据跳转
                foreach ($ruleItemList['list'] as $referIdKey => &$referValue){
                    $mainId                               = WorkflowConstant::CAN_JUMP_REFER_TYPE_ENUM[$ruleInfo['refer_type']] ?? $ruleInfo['rule_id'];
                    $uniqueKey                            = "{$prefix}{$mainId}-{$ruleInfo['rule_id']}-{$referType}-{$referValue['refer_id']}";
                    $referValue['report_item_unique_key'] = isset($cacheGroupMap[$uniqueKey]) ? $cacheGroupMap[$uniqueKey] : '';
                }
            }
        }

        // 删掉过程目标的部门信息
        if ($this->targetType == \PerformanceV2Goals::TARGET_PROCESS_GOAL) {
            $result['department']['list'] = [];
        }

    }
}