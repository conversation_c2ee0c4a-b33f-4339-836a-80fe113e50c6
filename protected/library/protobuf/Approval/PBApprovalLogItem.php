<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Approval.proto

namespace protobuf\Approval;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * 审批log item
 *
 * Generated from protobuf message <code>PBApprovalLogItem</code>
 */
class PBApprovalLogItem extends \Google\Protobuf\Internal\Message
{
    /**
     *动作：0-提交，1-同意，2-拒绝， 3-转交，4-撤回
     *
     * Generated from protobuf field <code>uint32 action = 1;</code>
     */
    protected $action = 0;
    /**
     *审批id
     *
     * Generated from protobuf field <code>uint64 approval_id = 2;</code>
     */
    protected $approval_id = 0;
    /**
     *提交log id
     *
     * Generated from protobuf field <code>uint64 approval_log_id = 3;</code>
     */
    protected $approval_log_id = 0;
    /**
     *当前状态
     *
     * Generated from protobuf field <code>uint32 cur_status = 4;</code>
     */
    protected $cur_status = 0;
    /**
     *扩展信息
     *
     * Generated from protobuf field <code>string ext_params = 5;</code>
     */
    protected $ext_params = '';
    /**
     *处理人列表
     *
     * Generated from protobuf field <code>repeated uint64 handlers = 6;</code>
     */
    private $handlers;
    /**
     * 名称
     *
     * Generated from protobuf field <code>string nick_name = 7;</code>
     */
    protected $nick_name = '';
    /**
     * 关联邮件状态
     *
     * Generated from protobuf field <code>uint32 refer_status = 8;</code>
     */
    protected $refer_status = 0;
    /**
     *备注
     *
     * Generated from protobuf field <code>string remark = 9;</code>
     */
    protected $remark = '';
    /**
     *审批主题
     *
     * Generated from protobuf field <code>string topic = 10;</code>
     */
    protected $topic = '';
    /**
     *审批人头像
     *
     * Generated from protobuf field <code>string avatar = 11;</code>
     */
    protected $avatar = '';
    /**
     *附件(首次提交)
     *
     * Generated from protobuf field <code>repeated .PBFileInfo attaches = 12;</code>
     */
    private $attaches;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $action
     *          动作：0-提交，1-同意，2-拒绝， 3-转交，4-撤回
     *     @type int|string $approval_id
     *          审批id
     *     @type int|string $approval_log_id
     *          提交log id
     *     @type int $cur_status
     *          当前状态
     *     @type string $ext_params
     *          扩展信息
     *     @type array<int>|array<string>|\Google\Protobuf\Internal\RepeatedField $handlers
     *          处理人列表
     *     @type string $nick_name
     *           名称
     *     @type int $refer_status
     *           关联邮件状态
     *     @type string $remark
     *          备注
     *     @type string $topic
     *          审批主题
     *     @type string $avatar
     *          审批人头像
     *     @type array<\protobuf\CRMCommon\PBFileInfo>|\Google\Protobuf\Internal\RepeatedField $attaches
     *          附件(首次提交)
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\Approval::initOnce();
        parent::__construct($data);
    }

    /**
     *动作：0-提交，1-同意，2-拒绝， 3-转交，4-撤回
     *
     * Generated from protobuf field <code>uint32 action = 1;</code>
     * @return int
     */
    public function getAction()
    {
        return $this->action;
    }

    /**
     *动作：0-提交，1-同意，2-拒绝， 3-转交，4-撤回
     *
     * Generated from protobuf field <code>uint32 action = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setAction($var)
    {
        GPBUtil::checkUint32($var);
        $this->action = $var;

        return $this;
    }

    /**
     *审批id
     *
     * Generated from protobuf field <code>uint64 approval_id = 2;</code>
     * @return int|string
     */
    public function getApprovalId()
    {
        return $this->approval_id;
    }

    /**
     *审批id
     *
     * Generated from protobuf field <code>uint64 approval_id = 2;</code>
     * @param int|string $var
     * @return $this
     */
    public function setApprovalId($var)
    {
        GPBUtil::checkUint64($var);
        $this->approval_id = $var;

        return $this;
    }

    /**
     *提交log id
     *
     * Generated from protobuf field <code>uint64 approval_log_id = 3;</code>
     * @return int|string
     */
    public function getApprovalLogId()
    {
        return $this->approval_log_id;
    }

    /**
     *提交log id
     *
     * Generated from protobuf field <code>uint64 approval_log_id = 3;</code>
     * @param int|string $var
     * @return $this
     */
    public function setApprovalLogId($var)
    {
        GPBUtil::checkUint64($var);
        $this->approval_log_id = $var;

        return $this;
    }

    /**
     *当前状态
     *
     * Generated from protobuf field <code>uint32 cur_status = 4;</code>
     * @return int
     */
    public function getCurStatus()
    {
        return $this->cur_status;
    }

    /**
     *当前状态
     *
     * Generated from protobuf field <code>uint32 cur_status = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setCurStatus($var)
    {
        GPBUtil::checkUint32($var);
        $this->cur_status = $var;

        return $this;
    }

    /**
     *扩展信息
     *
     * Generated from protobuf field <code>string ext_params = 5;</code>
     * @return string
     */
    public function getExtParams()
    {
        return $this->ext_params;
    }

    /**
     *扩展信息
     *
     * Generated from protobuf field <code>string ext_params = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setExtParams($var)
    {
        GPBUtil::checkString($var, True);
        $this->ext_params = $var;

        return $this;
    }

    /**
     *处理人列表
     *
     * Generated from protobuf field <code>repeated uint64 handlers = 6;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getHandlers()
    {
        return $this->handlers;
    }

    /**
     *处理人列表
     *
     * Generated from protobuf field <code>repeated uint64 handlers = 6;</code>
     * @param array<int>|array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setHandlers($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::UINT64);
        $this->handlers = $arr;

        return $this;
    }

    /**
     * 名称
     *
     * Generated from protobuf field <code>string nick_name = 7;</code>
     * @return string
     */
    public function getNickName()
    {
        return $this->nick_name;
    }

    /**
     * 名称
     *
     * Generated from protobuf field <code>string nick_name = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setNickName($var)
    {
        GPBUtil::checkString($var, True);
        $this->nick_name = $var;

        return $this;
    }

    /**
     * 关联邮件状态
     *
     * Generated from protobuf field <code>uint32 refer_status = 8;</code>
     * @return int
     */
    public function getReferStatus()
    {
        return $this->refer_status;
    }

    /**
     * 关联邮件状态
     *
     * Generated from protobuf field <code>uint32 refer_status = 8;</code>
     * @param int $var
     * @return $this
     */
    public function setReferStatus($var)
    {
        GPBUtil::checkUint32($var);
        $this->refer_status = $var;

        return $this;
    }

    /**
     *备注
     *
     * Generated from protobuf field <code>string remark = 9;</code>
     * @return string
     */
    public function getRemark()
    {
        return $this->remark;
    }

    /**
     *备注
     *
     * Generated from protobuf field <code>string remark = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setRemark($var)
    {
        GPBUtil::checkString($var, True);
        $this->remark = $var;

        return $this;
    }

    /**
     *审批主题
     *
     * Generated from protobuf field <code>string topic = 10;</code>
     * @return string
     */
    public function getTopic()
    {
        return $this->topic;
    }

    /**
     *审批主题
     *
     * Generated from protobuf field <code>string topic = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setTopic($var)
    {
        GPBUtil::checkString($var, True);
        $this->topic = $var;

        return $this;
    }

    /**
     *审批人头像
     *
     * Generated from protobuf field <code>string avatar = 11;</code>
     * @return string
     */
    public function getAvatar()
    {
        return $this->avatar;
    }

    /**
     *审批人头像
     *
     * Generated from protobuf field <code>string avatar = 11;</code>
     * @param string $var
     * @return $this
     */
    public function setAvatar($var)
    {
        GPBUtil::checkString($var, True);
        $this->avatar = $var;

        return $this;
    }

    /**
     *附件(首次提交)
     *
     * Generated from protobuf field <code>repeated .PBFileInfo attaches = 12;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getAttaches()
    {
        return $this->attaches;
    }

    /**
     *附件(首次提交)
     *
     * Generated from protobuf field <code>repeated .PBFileInfo attaches = 12;</code>
     * @param array<\protobuf\CRMCommon\PBFileInfo>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setAttaches($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \protobuf\CRMCommon\PBFileInfo::class);
        $this->attaches = $arr;

        return $this;
    }

}

