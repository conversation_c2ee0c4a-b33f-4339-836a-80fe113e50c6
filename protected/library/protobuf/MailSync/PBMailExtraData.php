<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: MailSync.proto

namespace protobuf\MailSync;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>PBMailExtraData</code>
 */
class PBMailExtraData extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>uint64 mail_id = 1;</code>
     */
    protected $mail_id = 0;
    /**
     * Generated from protobuf field <code>bool can_unlock = 2;</code>
     */
    protected $can_unlock = false;
    /**
     * Generated from protobuf field <code>string receive_read_receipt = 3;</code>
     */
    protected $receive_read_receipt = '';
    /**
     * Generated from protobuf field <code>repeated .PBFileInfo large_attach_list = 4;</code>
     */
    private $large_attach_list;
    /**
     * Generated from protobuf field <code>uint32 view_count = 5;</code>
     */
    protected $view_count = 0;
    /**
     * Generated from protobuf field <code>bool is_owner = 6;</code>
     */
    protected $is_owner = false;
    /**
     * Generated from protobuf field <code>string operate_role = 7;</code>
     */
    protected $operate_role = '';
    /**
     * Generated from protobuf field <code>.PBUserInfo owner = 8;</code>
     */
    protected $owner = null;
    /**
     * Generated from protobuf field <code>uint32 sign_id = 9;</code>
     */
    protected $sign_id = 0;
    /**
     * Generated from protobuf field <code>.PBMailContactSummary contact_summary = 10;</code>
     */
    protected $contact_summary = null;
    /**
     * Generated from protobuf field <code>.PBMailCardMap mail_card_map = 11;</code>
     */
    protected $mail_card_map = null;
    /**
     * Generated from protobuf field <code>bool inquiry_type = 12;</code>
     */
    protected $inquiry_type = false;
    /**
     * Generated from protobuf field <code>.PBMailAlibabaInfo alibaba_info = 13;</code>
     */
    protected $alibaba_info = null;
    /**
     * Generated from protobuf field <code>repeated .PBMailTodo mail_todo = 14;</code>
     */
    private $mail_todo;
    /**
     * Generated from protobuf field <code>.PBMailTranslate translate = 15;</code>
     */
    protected $translate = null;
    /**
     * Generated from protobuf field <code>bool is_manageable = 17;</code>
     */
    protected $is_manageable = false;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $mail_id
     *     @type bool $can_unlock
     *     @type string $receive_read_receipt
     *     @type array<\protobuf\CRMCommon\PBFileInfo>|\Google\Protobuf\Internal\RepeatedField $large_attach_list
     *     @type int $view_count
     *     @type bool $is_owner
     *     @type string $operate_role
     *     @type \protobuf\CRMCommon\PBUserInfo $owner
     *     @type int $sign_id
     *     @type \protobuf\MailSync\PBMailContactSummary $contact_summary
     *     @type \protobuf\MailSync\PBMailCardMap $mail_card_map
     *     @type bool $inquiry_type
     *     @type \protobuf\MailSync\PBMailAlibabaInfo $alibaba_info
     *     @type array<\protobuf\MailSync\PBMailTodo>|\Google\Protobuf\Internal\RepeatedField $mail_todo
     *     @type \protobuf\MailSync\PBMailTranslate $translate
     *     @type bool $is_manageable
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\MailSync::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>uint64 mail_id = 1;</code>
     * @return int|string
     */
    public function getMailId()
    {
        return $this->mail_id;
    }

    /**
     * Generated from protobuf field <code>uint64 mail_id = 1;</code>
     * @param int|string $var
     * @return $this
     */
    public function setMailId($var)
    {
        GPBUtil::checkUint64($var);
        $this->mail_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool can_unlock = 2;</code>
     * @return bool
     */
    public function getCanUnlock()
    {
        return $this->can_unlock;
    }

    /**
     * Generated from protobuf field <code>bool can_unlock = 2;</code>
     * @param bool $var
     * @return $this
     */
    public function setCanUnlock($var)
    {
        GPBUtil::checkBool($var);
        $this->can_unlock = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string receive_read_receipt = 3;</code>
     * @return string
     */
    public function getReceiveReadReceipt()
    {
        return $this->receive_read_receipt;
    }

    /**
     * Generated from protobuf field <code>string receive_read_receipt = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setReceiveReadReceipt($var)
    {
        GPBUtil::checkString($var, True);
        $this->receive_read_receipt = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .PBFileInfo large_attach_list = 4;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getLargeAttachList()
    {
        return $this->large_attach_list;
    }

    /**
     * Generated from protobuf field <code>repeated .PBFileInfo large_attach_list = 4;</code>
     * @param array<\protobuf\CRMCommon\PBFileInfo>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setLargeAttachList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \protobuf\CRMCommon\PBFileInfo::class);
        $this->large_attach_list = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 view_count = 5;</code>
     * @return int
     */
    public function getViewCount()
    {
        return $this->view_count;
    }

    /**
     * Generated from protobuf field <code>uint32 view_count = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setViewCount($var)
    {
        GPBUtil::checkUint32($var);
        $this->view_count = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool is_owner = 6;</code>
     * @return bool
     */
    public function getIsOwner()
    {
        return $this->is_owner;
    }

    /**
     * Generated from protobuf field <code>bool is_owner = 6;</code>
     * @param bool $var
     * @return $this
     */
    public function setIsOwner($var)
    {
        GPBUtil::checkBool($var);
        $this->is_owner = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string operate_role = 7;</code>
     * @return string
     */
    public function getOperateRole()
    {
        return $this->operate_role;
    }

    /**
     * Generated from protobuf field <code>string operate_role = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setOperateRole($var)
    {
        GPBUtil::checkString($var, True);
        $this->operate_role = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.PBUserInfo owner = 8;</code>
     * @return \protobuf\CRMCommon\PBUserInfo|null
     */
    public function getOwner()
    {
        return $this->owner;
    }

    public function hasOwner()
    {
        return isset($this->owner);
    }

    public function clearOwner()
    {
        unset($this->owner);
    }

    /**
     * Generated from protobuf field <code>.PBUserInfo owner = 8;</code>
     * @param \protobuf\CRMCommon\PBUserInfo $var
     * @return $this
     */
    public function setOwner($var)
    {
        GPBUtil::checkMessage($var, \protobuf\CRMCommon\PBUserInfo::class);
        $this->owner = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 sign_id = 9;</code>
     * @return int
     */
    public function getSignId()
    {
        return $this->sign_id;
    }

    /**
     * Generated from protobuf field <code>uint32 sign_id = 9;</code>
     * @param int $var
     * @return $this
     */
    public function setSignId($var)
    {
        GPBUtil::checkUint32($var);
        $this->sign_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.PBMailContactSummary contact_summary = 10;</code>
     * @return \protobuf\MailSync\PBMailContactSummary|null
     */
    public function getContactSummary()
    {
        return $this->contact_summary;
    }

    public function hasContactSummary()
    {
        return isset($this->contact_summary);
    }

    public function clearContactSummary()
    {
        unset($this->contact_summary);
    }

    /**
     * Generated from protobuf field <code>.PBMailContactSummary contact_summary = 10;</code>
     * @param \protobuf\MailSync\PBMailContactSummary $var
     * @return $this
     */
    public function setContactSummary($var)
    {
        GPBUtil::checkMessage($var, \protobuf\MailSync\PBMailContactSummary::class);
        $this->contact_summary = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.PBMailCardMap mail_card_map = 11;</code>
     * @return \protobuf\MailSync\PBMailCardMap|null
     */
    public function getMailCardMap()
    {
        return $this->mail_card_map;
    }

    public function hasMailCardMap()
    {
        return isset($this->mail_card_map);
    }

    public function clearMailCardMap()
    {
        unset($this->mail_card_map);
    }

    /**
     * Generated from protobuf field <code>.PBMailCardMap mail_card_map = 11;</code>
     * @param \protobuf\MailSync\PBMailCardMap $var
     * @return $this
     */
    public function setMailCardMap($var)
    {
        GPBUtil::checkMessage($var, \protobuf\MailSync\PBMailCardMap::class);
        $this->mail_card_map = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool inquiry_type = 12;</code>
     * @return bool
     */
    public function getInquiryType()
    {
        return $this->inquiry_type;
    }

    /**
     * Generated from protobuf field <code>bool inquiry_type = 12;</code>
     * @param bool $var
     * @return $this
     */
    public function setInquiryType($var)
    {
        GPBUtil::checkBool($var);
        $this->inquiry_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.PBMailAlibabaInfo alibaba_info = 13;</code>
     * @return \protobuf\MailSync\PBMailAlibabaInfo|null
     */
    public function getAlibabaInfo()
    {
        return $this->alibaba_info;
    }

    public function hasAlibabaInfo()
    {
        return isset($this->alibaba_info);
    }

    public function clearAlibabaInfo()
    {
        unset($this->alibaba_info);
    }

    /**
     * Generated from protobuf field <code>.PBMailAlibabaInfo alibaba_info = 13;</code>
     * @param \protobuf\MailSync\PBMailAlibabaInfo $var
     * @return $this
     */
    public function setAlibabaInfo($var)
    {
        GPBUtil::checkMessage($var, \protobuf\MailSync\PBMailAlibabaInfo::class);
        $this->alibaba_info = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .PBMailTodo mail_todo = 14;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getMailTodo()
    {
        return $this->mail_todo;
    }

    /**
     * Generated from protobuf field <code>repeated .PBMailTodo mail_todo = 14;</code>
     * @param array<\protobuf\MailSync\PBMailTodo>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setMailTodo($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \protobuf\MailSync\PBMailTodo::class);
        $this->mail_todo = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.PBMailTranslate translate = 15;</code>
     * @return \protobuf\MailSync\PBMailTranslate|null
     */
    public function getTranslate()
    {
        return $this->translate;
    }

    public function hasTranslate()
    {
        return isset($this->translate);
    }

    public function clearTranslate()
    {
        unset($this->translate);
    }

    /**
     * Generated from protobuf field <code>.PBMailTranslate translate = 15;</code>
     * @param \protobuf\MailSync\PBMailTranslate $var
     * @return $this
     */
    public function setTranslate($var)
    {
        GPBUtil::checkMessage($var, \protobuf\MailSync\PBMailTranslate::class);
        $this->translate = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool is_manageable = 17;</code>
     * @return bool
     */
    public function getIsManageable()
    {
        return $this->is_manageable;
    }

    /**
     * Generated from protobuf field <code>bool is_manageable = 17;</code>
     * @param bool $var
     * @return $this
     */
    public function setIsManageable($var)
    {
        GPBUtil::checkBool($var);
        $this->is_manageable = $var;

        return $this;
    }

}

