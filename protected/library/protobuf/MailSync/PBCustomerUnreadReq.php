<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: MailSync.proto

namespace protobuf\MailSync;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 *查询具体客户的未读数
 *url: /api/stormsFury/customerMailRead/customerUnread
 *
 * Generated from protobuf message <code>PBCustomerUnreadReq</code>
 */
class PBCustomerUnreadReq extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>repeated uint64 company_ids = 1;</code>
     */
    private $company_ids;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type array<int>|array<string>|\Google\Protobuf\Internal\RepeatedField $company_ids
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\MailSync::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>repeated uint64 company_ids = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getCompanyIds()
    {
        return $this->company_ids;
    }

    /**
     * Generated from protobuf field <code>repeated uint64 company_ids = 1;</code>
     * @param array<int>|array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setCompanyIds($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::UINT64);
        $this->company_ids = $arr;

        return $this;
    }

}

