<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: CRMCommon.proto

namespace protobuf\CRMCommon;

use UnexpectedValueException;

/**
 * Protobuf type <code>PBPostGrade</code>
 */
class PBPostGrade
{
    /**
     * Generated from protobuf enum <code>POST_GRADE_NONE = 0;</code>
     */
    const POST_GRADE_NONE = 0;
    /**
     * 普通职员
     *
     * Generated from protobuf enum <code>POST_GRADE_COMMON = 1;</code>
     */
    const POST_GRADE_COMMON = 1;
    /**
     * 中层管理者
     *
     * Generated from protobuf enum <code>POST_GRADE_MIDDLE = 2;</code>
     */
    const POST_GRADE_MIDDLE = 2;
    /**
     * 高层管理者
     *
     * Generated from protobuf enum <code>POST_GRADE_SENIOR = 3;</code>
     */
    const POST_GRADE_SENIOR = 3;

    private static $valueToName = [
        self::POST_GRADE_NONE => 'POST_GRADE_NONE',
        self::POST_GRADE_COMMON => 'POST_GRADE_COMMON',
        self::POST_GRADE_MIDDLE => 'POST_GRADE_MIDDLE',
        self::POST_GRADE_SENIOR => 'POST_GRADE_SENIOR',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

