<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: CRMCommon.proto

namespace protobuf\CRMCommon;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>PBWeeklyStatisticValues</code>
 */
class PBWeeklyStatisticValues extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>bool is_open = 1;</code>
     */
    protected $is_open = false;
    /**
     * Generated from protobuf field <code>uint32 rank = 2;</code>
     */
    protected $rank = 0;
    /**
     * Generated from protobuf field <code>string name = 3;</code>
     */
    protected $name = '';
    /**
     * Generated from protobuf field <code>string type = 4;</code>
     */
    protected $type = '';
    /**
     * Generated from protobuf field <code>string type_name = 5;</code>
     */
    protected $type_name = '';
    /**
     * Generated from protobuf field <code>string unit = 6;</code>
     */
    protected $unit = '';
    /**
     * Generated from protobuf field <code>string field = 7;</code>
     */
    protected $field = '';
    /**
     * Generated from protobuf field <code>uint32 value = 8;</code>
     */
    protected $value = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type bool $is_open
     *     @type int $rank
     *     @type string $name
     *     @type string $type
     *     @type string $type_name
     *     @type string $unit
     *     @type string $field
     *     @type int $value
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\CRMCommon::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>bool is_open = 1;</code>
     * @return bool
     */
    public function getIsOpen()
    {
        return $this->is_open;
    }

    /**
     * Generated from protobuf field <code>bool is_open = 1;</code>
     * @param bool $var
     * @return $this
     */
    public function setIsOpen($var)
    {
        GPBUtil::checkBool($var);
        $this->is_open = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 rank = 2;</code>
     * @return int
     */
    public function getRank()
    {
        return $this->rank;
    }

    /**
     * Generated from protobuf field <code>uint32 rank = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setRank($var)
    {
        GPBUtil::checkUint32($var);
        $this->rank = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string name = 3;</code>
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Generated from protobuf field <code>string name = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setName($var)
    {
        GPBUtil::checkString($var, True);
        $this->name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string type = 4;</code>
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Generated from protobuf field <code>string type = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkString($var, True);
        $this->type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string type_name = 5;</code>
     * @return string
     */
    public function getTypeName()
    {
        return $this->type_name;
    }

    /**
     * Generated from protobuf field <code>string type_name = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setTypeName($var)
    {
        GPBUtil::checkString($var, True);
        $this->type_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string unit = 6;</code>
     * @return string
     */
    public function getUnit()
    {
        return $this->unit;
    }

    /**
     * Generated from protobuf field <code>string unit = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setUnit($var)
    {
        GPBUtil::checkString($var, True);
        $this->unit = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string field = 7;</code>
     * @return string
     */
    public function getField()
    {
        return $this->field;
    }

    /**
     * Generated from protobuf field <code>string field = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setField($var)
    {
        GPBUtil::checkString($var, True);
        $this->field = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 value = 8;</code>
     * @return int
     */
    public function getValue()
    {
        return $this->value;
    }

    /**
     * Generated from protobuf field <code>uint32 value = 8;</code>
     * @param int $var
     * @return $this
     */
    public function setValue($var)
    {
        GPBUtil::checkUint32($var);
        $this->value = $var;

        return $this;
    }

}

