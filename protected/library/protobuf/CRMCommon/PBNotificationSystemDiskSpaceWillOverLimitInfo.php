<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: CRMCommon.proto

namespace protobuf\CRMCommon;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>PBNotificationSystemDiskSpaceWillOverLimitInfo</code>
 */
class PBNotificationSystemDiskSpaceWillOverLimitInfo extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>sint32 day = 1;</code>
     */
    protected $day = 0;
    /**
     * Generated from protobuf field <code>string valid_to = 2;</code>
     */
    protected $valid_to = '';
    /**
     * Generated from protobuf field <code>string space = 3;</code>
     */
    protected $space = '';
    /**
     * Generated from protobuf field <code>string can_user_space = 4;</code>
     */
    protected $can_user_space = '';
    /**
     * Generated from protobuf field <code>string current_space = 5;</code>
     */
    protected $current_space = '';
    /**
     * Generated from protobuf field <code>string used_total = 6;</code>
     */
    protected $used_total = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $day
     *     @type string $valid_to
     *     @type string $space
     *     @type string $can_user_space
     *     @type string $current_space
     *     @type string $used_total
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\CRMCommon::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>sint32 day = 1;</code>
     * @return int
     */
    public function getDay()
    {
        return $this->day;
    }

    /**
     * Generated from protobuf field <code>sint32 day = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setDay($var)
    {
        GPBUtil::checkInt32($var);
        $this->day = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string valid_to = 2;</code>
     * @return string
     */
    public function getValidTo()
    {
        return $this->valid_to;
    }

    /**
     * Generated from protobuf field <code>string valid_to = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setValidTo($var)
    {
        GPBUtil::checkString($var, True);
        $this->valid_to = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string space = 3;</code>
     * @return string
     */
    public function getSpace()
    {
        return $this->space;
    }

    /**
     * Generated from protobuf field <code>string space = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setSpace($var)
    {
        GPBUtil::checkString($var, True);
        $this->space = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string can_user_space = 4;</code>
     * @return string
     */
    public function getCanUserSpace()
    {
        return $this->can_user_space;
    }

    /**
     * Generated from protobuf field <code>string can_user_space = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setCanUserSpace($var)
    {
        GPBUtil::checkString($var, True);
        $this->can_user_space = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string current_space = 5;</code>
     * @return string
     */
    public function getCurrentSpace()
    {
        return $this->current_space;
    }

    /**
     * Generated from protobuf field <code>string current_space = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setCurrentSpace($var)
    {
        GPBUtil::checkString($var, True);
        $this->current_space = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string used_total = 6;</code>
     * @return string
     */
    public function getUsedTotal()
    {
        return $this->used_total;
    }

    /**
     * Generated from protobuf field <code>string used_total = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setUsedTotal($var)
    {
        GPBUtil::checkString($var, True);
        $this->used_total = $var;

        return $this;
    }

}

