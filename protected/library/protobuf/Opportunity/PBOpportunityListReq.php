<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Opportunity.proto

namespace protobuf\Opportunity;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * 商机列表请求
 *
 * Generated from protobuf message <code>PBOpportunityListReq</code>
 */
class PBOpportunityListReq extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>uint64 company_id = 1;</code>
     */
    protected $company_id = 0;
    /**
     * Generated from protobuf field <code>uint32 cur_page = 2;</code>
     */
    protected $cur_page = 0;
    /**
     * Generated from protobuf field <code>uint32 page_size = 3;</code>
     */
    protected $page_size = 0;
    /**
     * Generated from protobuf field <code>string sort_field = 4;</code>
     */
    protected $sort_field = '';
    /**
     * Generated from protobuf field <code>string sort_type = 5;</code>
     */
    protected $sort_type = '';
    /**
     *跟权限相关，默认1，可以不传
     *
     * Generated from protobuf field <code>uint32 show_all = 6;</code>
     */
    protected $show_all = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $company_id
     *     @type int $cur_page
     *     @type int $page_size
     *     @type string $sort_field
     *     @type string $sort_type
     *     @type int $show_all
     *          跟权限相关，默认1，可以不传
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\Opportunity::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>uint64 company_id = 1;</code>
     * @return int|string
     */
    public function getCompanyId()
    {
        return $this->company_id;
    }

    /**
     * Generated from protobuf field <code>uint64 company_id = 1;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCompanyId($var)
    {
        GPBUtil::checkUint64($var);
        $this->company_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 cur_page = 2;</code>
     * @return int
     */
    public function getCurPage()
    {
        return $this->cur_page;
    }

    /**
     * Generated from protobuf field <code>uint32 cur_page = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setCurPage($var)
    {
        GPBUtil::checkUint32($var);
        $this->cur_page = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 page_size = 3;</code>
     * @return int
     */
    public function getPageSize()
    {
        return $this->page_size;
    }

    /**
     * Generated from protobuf field <code>uint32 page_size = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setPageSize($var)
    {
        GPBUtil::checkUint32($var);
        $this->page_size = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string sort_field = 4;</code>
     * @return string
     */
    public function getSortField()
    {
        return $this->sort_field;
    }

    /**
     * Generated from protobuf field <code>string sort_field = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setSortField($var)
    {
        GPBUtil::checkString($var, True);
        $this->sort_field = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string sort_type = 5;</code>
     * @return string
     */
    public function getSortType()
    {
        return $this->sort_type;
    }

    /**
     * Generated from protobuf field <code>string sort_type = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setSortType($var)
    {
        GPBUtil::checkString($var, True);
        $this->sort_type = $var;

        return $this;
    }

    /**
     *跟权限相关，默认1，可以不传
     *
     * Generated from protobuf field <code>uint32 show_all = 6;</code>
     * @return int
     */
    public function getShowAll()
    {
        return $this->show_all;
    }

    /**
     *跟权限相关，默认1，可以不传
     *
     * Generated from protobuf field <code>uint32 show_all = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setShowAll($var)
    {
        GPBUtil::checkUint32($var);
        $this->show_all = $var;

        return $this;
    }

}

