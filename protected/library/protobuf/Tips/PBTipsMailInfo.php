<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Tips.proto

namespace protobuf\Tips;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * type:2:回复邮件提醒/ 3:有未被回应的关键邮件/ 4:关键邮件被查看提醒
 *FEED_TYPE_MAIL_REMIND_REPLY/FEED_TYPE_MAIL_NOT_RESPOND/FEED_TYPE_MAIL_KEY_VIEWED
 *
 * Generated from protobuf message <code>PBTipsMailInfo</code>
 */
class PBTipsMailInfo extends \Google\Protobuf\Internal\Message
{
    /**
     *商机名称
     *
     * Generated from protobuf field <code>string emial = 1;</code>
     */
    protected $emial = '';
    /**
     * Generated from protobuf field <code>uint64 mail_id = 2;</code>
     */
    protected $mail_id = 0;
    /**
     * Generated from protobuf field <code>string subject = 3;</code>
     */
    protected $subject = '';
    /**
     * Generated from protobuf field <code>uint64 company_id = 4;</code>
     */
    protected $company_id = 0;
    /**
     *名称
     *
     * Generated from protobuf field <code>string name = 5;</code>
     */
    protected $name = '';
    /**
     *邮件接受时间
     *
     * Generated from protobuf field <code>string mail_receive_time = 6;</code>
     */
    protected $mail_receive_time = '';
    /**
     * Generated from protobuf field <code>uint32 mail_tag = 7;</code>
     */
    protected $mail_tag = 0;
    /**
     * Generated from protobuf field <code>uint32 user_mail_id = 8;</code>
     */
    protected $user_mail_id = 0;
    /**
     * Generated from protobuf field <code>string feed_type_id = 9;</code>
     */
    protected $feed_type_id = '';
    /**
     * Generated from protobuf field <code>string type = 10;</code>
     */
    protected $type = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $emial
     *          商机名称
     *     @type int|string $mail_id
     *     @type string $subject
     *     @type int|string $company_id
     *     @type string $name
     *          名称
     *     @type string $mail_receive_time
     *          邮件接受时间
     *     @type int $mail_tag
     *     @type int $user_mail_id
     *     @type string $feed_type_id
     *     @type string $type
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\Tips::initOnce();
        parent::__construct($data);
    }

    /**
     *商机名称
     *
     * Generated from protobuf field <code>string emial = 1;</code>
     * @return string
     */
    public function getEmial()
    {
        return $this->emial;
    }

    /**
     *商机名称
     *
     * Generated from protobuf field <code>string emial = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setEmial($var)
    {
        GPBUtil::checkString($var, True);
        $this->emial = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint64 mail_id = 2;</code>
     * @return int|string
     */
    public function getMailId()
    {
        return $this->mail_id;
    }

    /**
     * Generated from protobuf field <code>uint64 mail_id = 2;</code>
     * @param int|string $var
     * @return $this
     */
    public function setMailId($var)
    {
        GPBUtil::checkUint64($var);
        $this->mail_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string subject = 3;</code>
     * @return string
     */
    public function getSubject()
    {
        return $this->subject;
    }

    /**
     * Generated from protobuf field <code>string subject = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setSubject($var)
    {
        GPBUtil::checkString($var, True);
        $this->subject = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint64 company_id = 4;</code>
     * @return int|string
     */
    public function getCompanyId()
    {
        return $this->company_id;
    }

    /**
     * Generated from protobuf field <code>uint64 company_id = 4;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCompanyId($var)
    {
        GPBUtil::checkUint64($var);
        $this->company_id = $var;

        return $this;
    }

    /**
     *名称
     *
     * Generated from protobuf field <code>string name = 5;</code>
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     *名称
     *
     * Generated from protobuf field <code>string name = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setName($var)
    {
        GPBUtil::checkString($var, True);
        $this->name = $var;

        return $this;
    }

    /**
     *邮件接受时间
     *
     * Generated from protobuf field <code>string mail_receive_time = 6;</code>
     * @return string
     */
    public function getMailReceiveTime()
    {
        return $this->mail_receive_time;
    }

    /**
     *邮件接受时间
     *
     * Generated from protobuf field <code>string mail_receive_time = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setMailReceiveTime($var)
    {
        GPBUtil::checkString($var, True);
        $this->mail_receive_time = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 mail_tag = 7;</code>
     * @return int
     */
    public function getMailTag()
    {
        return $this->mail_tag;
    }

    /**
     * Generated from protobuf field <code>uint32 mail_tag = 7;</code>
     * @param int $var
     * @return $this
     */
    public function setMailTag($var)
    {
        GPBUtil::checkUint32($var);
        $this->mail_tag = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 user_mail_id = 8;</code>
     * @return int
     */
    public function getUserMailId()
    {
        return $this->user_mail_id;
    }

    /**
     * Generated from protobuf field <code>uint32 user_mail_id = 8;</code>
     * @param int $var
     * @return $this
     */
    public function setUserMailId($var)
    {
        GPBUtil::checkUint32($var);
        $this->user_mail_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string feed_type_id = 9;</code>
     * @return string
     */
    public function getFeedTypeId()
    {
        return $this->feed_type_id;
    }

    /**
     * Generated from protobuf field <code>string feed_type_id = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setFeedTypeId($var)
    {
        GPBUtil::checkString($var, True);
        $this->feed_type_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string type = 10;</code>
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Generated from protobuf field <code>string type = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkString($var, True);
        $this->type = $var;

        return $this;
    }

}

