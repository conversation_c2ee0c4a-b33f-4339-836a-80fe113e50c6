<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Performance.proto

namespace protobuf\Performance;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>PBContrastGoalCompletionListRsp</code>
 */
class PBContrastGoalCompletionListRsp extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>repeated .PBGoalCompletionInfo completion_list = 1;</code>
     */
    private $completion_list;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type array<\protobuf\Performance\PBGoalCompletionInfo>|\Google\Protobuf\Internal\RepeatedField $completion_list
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\Performance::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>repeated .PBGoalCompletionInfo completion_list = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getCompletionList()
    {
        return $this->completion_list;
    }

    /**
     * Generated from protobuf field <code>repeated .PBGoalCompletionInfo completion_list = 1;</code>
     * @param array<\protobuf\Performance\PBGoalCompletionInfo>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setCompletionList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \protobuf\Performance\PBGoalCompletionInfo::class);
        $this->completion_list = $arr;

        return $this;
    }

}

