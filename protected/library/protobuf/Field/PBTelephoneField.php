<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Field.proto

namespace protobuf\Field;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * 电话
 *
 * Generated from protobuf message <code>PBTelephoneField</code>
 */
class PBTelephoneField extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string name = 1;</code>
     */
    protected $name = '';
    /**
     * Generated from protobuf field <code>.PBTelephone value = 2;</code>
     */
    protected $value = null;
    /**
     * Generated from protobuf field <code>bool is_empty = 3;</code>
     */
    protected $is_empty = false;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $name
     *     @type \protobuf\Field\PBTelephone $value
     *     @type bool $is_empty
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\Field::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string name = 1;</code>
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Generated from protobuf field <code>string name = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setName($var)
    {
        GPBUtil::checkString($var, True);
        $this->name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.PBTelephone value = 2;</code>
     * @return \protobuf\Field\PBTelephone|null
     */
    public function getValue()
    {
        return $this->value;
    }

    public function hasValue()
    {
        return isset($this->value);
    }

    public function clearValue()
    {
        unset($this->value);
    }

    /**
     * Generated from protobuf field <code>.PBTelephone value = 2;</code>
     * @param \protobuf\Field\PBTelephone $var
     * @return $this
     */
    public function setValue($var)
    {
        GPBUtil::checkMessage($var, \protobuf\Field\PBTelephone::class);
        $this->value = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool is_empty = 3;</code>
     * @return bool
     */
    public function getIsEmpty()
    {
        return $this->is_empty;
    }

    /**
     * Generated from protobuf field <code>bool is_empty = 3;</code>
     * @param bool $var
     * @return $this
     */
    public function setIsEmpty($var)
    {
        GPBUtil::checkBool($var);
        $this->is_empty = $var;

        return $this;
    }

}

