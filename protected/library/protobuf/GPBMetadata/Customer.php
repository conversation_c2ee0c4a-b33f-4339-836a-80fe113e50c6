<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Customer.proto

namespace protobuf\GPBMetadata;

class Customer
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \protobuf\GPBMetadata\CRMCommon::initOnce();
        \protobuf\GPBMetadata\MailSync::initOnce();
        \protobuf\GPBMetadata\Chat::initOnce();
        \protobuf\GPBMetadata\SalesGuide::initOnce();
        \protobuf\GPBMetadata\Field::initOnce();
        \GPBMetadata\Google\Protobuf\Struct::initOnce();
        \GPBMetadata\Google\Protobuf\Timestamp::initOnce();
        $pool->internalAddGeneratedFile(
            '
��
Customer.protoMailSync.proto
Chat.protoSalesGuide.protoField.protogoogle/protobuf/struct.protogoogle/protobuf/timestamp.proto"�
PBTrailCommentReq
trail_id (

comment_id (
content (	\'
scene (2.PBTrailCommentSceneType
at_user_ids (
file_ids ("�
PBTrailCommentRsp

comment_id (
content (	
at_users (2.PBUserInfo%
create_user_info (2.PBUserInfo,
parent_create_user_info (2.PBUserInfo
	file_list (2.PBFileList"�
PBTrailInfo
trail_id (
summary (	B
refer_id (
module (
type (
create_user (

create_time (	)
company_info (2.PBTrailCompanyInfo+

customer_list	 (2.PBTrailCustomerInfo4
lead_customer_list (2.PBTrailLeadCustomerInfo)
remark_info (2.PBTrailRemarkInfoH %
	mail_info (2.PBTrailMailInfoH #
edm_info
 (2.PBTrailEdmInfoH /
quotation_info (2.PBTrailQuotationInfoH !
pi_info (2.PBTrailPiInfoH \'

order_info (2.PBTrailOrderInfoH .
pay_phone_info (2.PBTrailPayPhoneInfoH 3
opportunity_info (2.PBTrailOpportunityInfoH 0
site_track_info (2.PBTrailSiteTrackInfoH )

schedule_info (2.PBTrailScheduleH 
delete_flag
 (

node_type_name (	
refer_opportunity_id (
refer_opportunity_name (	7
refer_opportunity_info (2.PBTrailOpportunityInfo#
	lead_info (2.PBTrailLeadInfo%
create_user_info (2.PBUserInfo
ali_account (	B
data"6
PBTrailCompanyInfo

company_id (
name (	"G
PBTrailCustomerInfo
customer_id (
name (	
email (	"K
PBTrailLeadCustomerInfo
customer_id (
name (	
email (	"�
PBTrailOpportunityInfo
opportunity_id (
	serial_id (	
name (	
currency (	
amount (
content (	

stage_name (	
enable_flag (
end_time_stamp	 ("�
PBTrailSiteTrackInfo
title (	
url (	
keyword (	
gclid (	
site_session_id (	
event_id (+
track_visit_list (2.PBTrailVisitInfo
count (

enable_flag	 (
	form_data
 (	
extract_data (	
email (	"C
PBTrailVisitInfo
title (	
url (	
create_time (	"z
PBTrailRemarkInfo
content (	
	file_list (2.PBFileInfo
address (	
latitude (	
	longitude (	"�
PBTrailMailInfo
mail_id (
	mail_type (

subject (	
delete_flag (

is_yours (
user_id (

user_mail_id (

	open_flag (
	read_flag	 (
attach_flag
 (
	file_list (2.PBFileInfo
summary (	
tag_list
 (2
.PBTagInfo

track_flag (
&

track_info (2.PBMailTrackDetail
sender (	
receiver (	\'
remark_info (2.PBTrailRemarkInfo"~
PBTrailEdmInfo
task_id (
subject (	
status (


track_flag (
&

track_info (2.PBMailTrackDetail"�
PBTrailQuotationInfo
quotation_id (
quotation_name (	
currency (	
quotation_amount (
status_name (	
product_total_count (

total_price ("�
PBTrailOrderInfo
order_id (

order_name (	
currency (	
order_amount (
status_name (	
product_total_count (

total_price ("/

PBTrailPiInfo
pi_id (
pi_name (	"
PBTrailPayPhoneInfo
call_id (

target_tel (	
seconds (	

record_url (	
file_ext (	
type (	"e
PBTrailModuleInfo
module (

name (	
count (
#
	type_list (2.PBTrailTypeInfo"-
PBTrailTypeInfo
type (

name (	"}
PBTrailStat
begin (	
end (	
date (	
	date_type (

count (
"
module (2.PBTrailModuleInfo"�
PBTrailSchedule
schedule_id (
title (	
remark (	 
attach_info (2.PBFileInfo

image_info (2.PBFileInfo
delete_flag ("0
PBTrailLeadInfo
lead_id (
name (	"�
PBCustomerInfo
customer_id (

company_id (
email (	
	email_md5 (	
name (	
main_customer_flag (
gender (2
.PBGenderType
birth_timestamp ( 

post_grade	 (2.PBPostGrade
post
 (	$
growth_level (2.PBGrowthLevel"{

PBCompanyInfo

company_id (
user_ids (
name (	"
	customers (2.PBCustomerInfo

short_name (	"+
	PBTelInfo
	area_code (	
tel (	"0
PBCustomerContact
type (	
value (	"h
PBCustomerStatusInfo
	status_id (
status_name (	
status_color (	

order_rank (	"y
PBCustomerOriginInfo
	origin_id (
origin_name (	
display_flag (#
node (2.PBCustomerOriginInfo"-
PBCustomerCategoryItem
category_id ("]
PBCustomerScore
compact (

active (

star (

data (

total (
"4
PBCustomerPool
pool_id (
	pool_name (	"r
PBCustomerSearchInfo
customer_id (

company_id (
email (	
name (	
company_name (	"<
PBCompanyCardReq

company_id (	
direct_owner ("�
PBCompanyCardRsp
	card_type (
email (	
name (	
	postition (	
nickname (	
tel_list (	
refer_id (
is_black (

company_id	 ("T
PBCustomerFieldGroup
group_id (
name (	
fields (2.PBFieldItem"�
PBCompanyBaseInfo

company_id (
user_ids (

name (	

short_name (	
star (2.PBStar
trail_status (
group_id (
score	 (2.PBCustomerScore
tag
 (
pool_id (
pin_flag (
duplicate_flag
 (

tag_list (2
.PBTagInfo1
main_customer_info (2.PBCustomerDetailInfo"�
PBCustomerDetailInfo
customer_id (

company_id (
name (	
email (	
birth (	
tel_list (2
.PBTelInfo
post (	 

post_grade (2.PBPostGrade
remark	 (	
gender
 (2
.PBGenderType#
contact (2.PBCustomerContact
main_customer_flag (I
external_field_data
 (2,.PBCustomerDetailInfo.ExternalFieldDataEntry

image_list (2.PBFileInfo8
ExternalFieldDataEntry
key (	
value (	:8"�

PBCompanyDetailInfo

company_id (
user_id (

name (	

short_name (	
star (2.PBStar
trail_status (
group_id (
score	 (2.PBCustomerScore
tag
 (
pool_id (
pin_flag (
	serial_id
 (	
origin (-
category_ids (2.PBCustomerCategoryItem
biz_type (	
timezone (	
country (	
province (	
city (	
scale_id (2.PBScale
homepage (	
fax (	
tel (	
address (	
remark (	

order_time (	
create_time (	
archive_time (	

last_owner (

archive_type (


tel_area_code  (	

image_list# (2.PBFileInfoH
external_field_data$ (2+.PBCompanyDetailInfo.ExternalFieldDataEntry\'
customer% (2.PBCustomerDetailInfo%
last_trail_info& (2.PBTrailInfo
	deal_time\' (	
performance_order_count( (!
success_opportunity_count) (\'
last_remark_trail* (2.PBTrailInfo
next_follow_up_time+ (	(
alibaba_store_info, (2.PBStoreInfo
annual_procurement- (
intention_level. (

release_count/ (

private_time0 (	
	edit_time1 (	
last_edit_user2 (

create_user3 (

public_time4 (	
recent_follow_up_time5 (	8
ExternalFieldDataEntry
key (	
value (	:8"V
PBCompanyGroupDetailInfo

company_id (&
company (2.PBCustomerFieldGroup"~
PBCustomerGroupDetailInfo

company_id (
customer_id (
main_customer_flag (
fields (2.PBFieldItem"�
PBCompanyFullGroupDetailInfo

company_id (&
company (2.PBCustomerFieldGroup-
	customers (2.PBCustomerGroupDetailInfo!
system_info (2.PBFieldItem"Q
PBCustomerArchiveResult
type (	

company_id (
customer_ids ("*
PBCompanySummaryReq
customer_id ("�
PBCompanySummaryRsp

company_id (
customer_id (
name (	
country (	
address (	
province (	
city (	
group_id (
trail_status_id	 (
	user_list
 (2.PBUserInfo 

last_trail (2.PBTrailInfo
tag_id_list (
star
 (


customer_name (	
post (	
email (	
tel_list (2
.PBTelInfo":
PBCompanyCompareReq
version (

limit_flag (
"s
PBCompanyCompareRsp

session_id (	
size (
version (
expired_time (

max_version ("D
PBCompanySyncReq

session_id (	
offset (

size (
"f
PBCompanySyncRsp
version ($
company_list (2.PBCompanyInfo
remove_company_list ("�
PBCustomerSearchReq
keyword (	
	group_ids (
tags (
	countries (	
province (	
city (	

status_ids (
stars (
origins	 (
	biz_types
 (	
category_ids (
B

start_date (	
end_date
 (	
offset (

limit (

compare_day (

compare_day_op (6
multiple_category_ids (2.PBCustomerCategoryItem1
main_customer_flag (2.MainCustomerFlagEnum
pin ($
will_public (2.WillPublicEnum"

stage_type (2.StageTypeEnum
user_id (1

company_field (2.PBSearchExternalFieldInfo2
customer_field (2.PBSearchExternalFieldInfo
ali_store_id (!
next_follow_up_begin_time (	
next_follow_up_end_time (	$
search_model (2.PBSearchModel-
tag_match_mode (2.PBSearchTagMatchMode1
send_mail_condition (2.PBSendMailCondition"R
PBSendMailCondition

send_mail_day (
$

had_send_mail (2
.SendMailEnum"I
PBCustomerSearchRsp
total (#
list (2.PBCustomerSearchInfo">
PBCustomerStatusListRsp#
list (2.PBCustomerStatusInfo">
PBCustomerOriginListRsp#
list (2.PBCustomerOriginInfo"6
PBCustomerPoolListRsp
list (2.PBCustomerPool"5
PBCompanyGroupRsp 

group_list (2.PBGroupInfo"/
PBCompanyTagRsp
tag_list (2
.PBTagInfo"b
PBCompanyTrailStatus
	status_id (
status_name (	
statuc_color (	
rank (
"I
PBCompanyTrailStatusRsp.
status_list (2.PBCompanyTrailStatusB"*
PBCompanyBaseInfoReq

company_id ("E
PBCompanyBaseInfoRsp-
company_base_info (2.PBCompanyBaseInfo",
PBCompanyDetailInfoReq

company_id ("T
PBCompanyDetailInfoRsp:
company_detail_info (2.PBCompanyFullGroupDetailInfo"%
PBCompanyListHeaderReq
key (	"6
PBCompanyListHeaderRep
fields (2.PBFieldItem"-
PBCompanyFilterListReq
public_flag (
"4
PBCompanyFilterListRsp
list (2.PBFieldItem"�	
PBCompanyListReq
keyword (	
search_field (	
user_num (


sort_field (	
	sort_type (	
	group_ids (
tags (

status_ids (
stars	 (2.PBStar
offset
 (

limit (

	countries (	
province
 (	
city (	
origins (
	biz_types (	-
category_ids (2.PBCustomerCategoryItem

start_date (	
end_date (	
compare_day (

compare_day_op (
pin (
public_remind_day (
show_field_key (	$
search_model (2.PBSearchModel

stage_type (
pool_id (
recent_select_flag (
show_all (
user_id (1

company_field (2.PBSearchExternalFieldInfo2
customer_field  (2.PBSearchExternalFieldInfo-
tag_match_mode! (2.PBSearchTagMatchMode
acquired_company_day" (
last_owner_ids# (4
filter_ext_param$ (2.PBSearchExternalFieldInfo%
min_success_opportunity_count% (%
max_success_opportunity_count& (#
min_performance_order_count\' (#
max_performance_order_count( (
deal_time_start_date) (	
deal_time_end_date* (	
archive_type+ (4
filter_ext_field, (2.PBSearchExternalFieldInfo
ali_store_id- (!
next_follow_up_begin_time. (	
next_follow_up_end_time/ (	"_
PBSearchExternalFieldInfo
field_id (
keyword (	

match_type (	
key (	"�
PBCompanyListByFilterReq

filter_key (	
	filter_id (	
offset (

limit (


sort_field (	
	sort_type (	"E
PBCompanyListRsp
total (
"
list (2.PBCompanyDetailInfo"%
PBTrailParamReq

company_id ("�
PBTrailParamRsp\'
module_list (2.PBTrailModuleInfo%
create_user_list (2.PBUserInfo+

customer_list (2.PBTrailCustomerInfo"l
PBTrailStatReq

company_id (
customer_id (
module (

create_user (

type (
"2
PBTrailStatRsp 

trail_stat (2.PBTrailStat"�
PBTrailListReq

company_id (
customer_id (
module (

create_user (


begin_time (	
end_time (	
offset (

limit (

type	 (

modules
 (

cur_page (

	page_size (
";
PBTrailListRsp
list (2.PBTrailInfo
count (
"\'
PBContactListReq
company_ids ("@
PBContactListRsp,

customer_list (2.PBCustomerSearchInfo"<
PBCustomerSetStatusReq

company_id (
status ("A
PBCustomerSetStarReq

company_id (
star (2.PBStar">
PBCustomerSetGroupReq
company_ids (
group_id ("X
PBCustomerSetTagsReq
company_ids (
add_tag_ids (
delete_tag_ids (";
PBCustomerAddTagReq
company_ids (
tag_ids ("Q
PBCustomerResetTagReq

company_id (
tag_ids (
company_ids ("=
PBCustomerRemoveTagReq
company_ids (
tag_id ("�
PBCustomerRemarkReq

company_id (
customer_id (
content (	
remark_time (	
remark_type (

file_ids (
next_follow_up_time (	"�
PBCompanyRemarkReq

company_id (
customer_id (
content (	
remark_time (

remark_type (	
file_ids (
next_follow_up_time (

address (	
latitude	 (
	longitude
 (
opportunity_ids (
mail_ids ("+
PBCompanyTrailDetailReq
trail_id (";
PBCompanyTrailDetailRsp 

trail_info (2.PBTrailInfo"<
PBCustomerSetPoolReq
company_ids (
pool_id ("J
PBCustomerSetTagReq
tag_id (
tag_name (	
	tag_color (	"%
PBCustomerSetTagRsp
tag_id ("U
PBCustomerHoldReq
company_ids (
group_id (
transfer_user_ids (";
PBCustomerShareReq
company_ids (
user_ids (">
PBCustomerTransferReq
company_ids (
user_ids ("A
PBCustomerTransferAllReq
company_ids (
user_ids ("*
PBCustomerRemoveReq
company_ids ("u
PBCustomerMergeReq
main_company_id (
another_company_id (*
company (2.PBCompanyGroupDetailInfo"*
PBCustomerDeleteReq
company_ids ("u
PBCustomerMoveToPublicReq
company_ids (
pool_id (
change_pool_flag (

public_reason_id ("J
PBCustomerPublicReasonListRsp)
list (2.PBCustomerPublicReasonItem"D
PBCustomerPublicReasonItem
reason_name (	
	reason_id ("N
PBCustomerArchiveReq
mail_id (
emails (	

force_archive ("@
PBCustomerArchiveRsp(
result (2.PBCustomerArchiveResult"U
PBEditCompanyByFieldReq

company_id (
	field_key (	
field_value (	"3
PBAiTagListRsp!
ai_tag_list (2.PBAiTagItem"/
PBAiTagItem
tag_id (

tag_name (	"<
PBAiStatusListRsp\'
ai_status_list (2.PBAiStatusItem"8
PBAiStatusItem
	status_id (

status_name (	"\'
PBSaveByAdviceReq

advice_ids ("W
PBAdviceNotAcceptTypeReq

advice_ids (
type (

not_accept_remark (	"7
PBArchiveMailReq

company_id (
mail_id (">
PBBindHashIdReq

company_id (	
company_hash_id (	"f
PBEditCompanyByFieldsReq

company_id ((
fields (2.PBEditCompanyByFieldReq
type ("*
PBDeleteContactReq
customer_ids ("^
PBSaveCustomerReq

company_id (
customer_id ( 
fields (2.PBCustomerField"9
PBCustomerField
	field_key (	
field_value (	"�
PBEdmReadListReq

begin_time (

end_time (
"
send_status (2
.PBSendStatus$
trigger_type (2.PBTriggerType
	task_type (	
report_item_unique_key (	
cur_page (

	page_size (

user_id	 (
"g
PBCheckFieldValueReq

company_id (
field (	
value (	
type (
lead_id ("�
PBCheckFieldValueRsp
is_exist (
type (
message (	
field_id (	
value (	
company_ids (
unique_check (
"�
PBUniqueCheckFieldValuesReq

company_id (
customer_id (#
data (2.PBNeedCheckFieldItem
type (
lead_id (
pool_id ("B
PBUniqueCheckFieldValuesRsp#
list (2.PBCheckFieldValueRsp"7
PBNeedCheckFieldItem
field_id (	
value (	"<
PBBatchToPrivateReq
company_ids (
group_id ("@
PBBatchToPrivateRsp

success_count (


fail_count (
"=
PBBatchDistributeReq
company_ids (
user_ids ("A
PBBatchDistributeRsp

success_count (


fail_count (
"@
PBEdmReadListRsp
count (

list (2.PBEdmEmailInfo"�
PBEdmEmailInfo
task_id (
edit_enable (
user_id (

task_subject (	

send_to_count (

create_time (

status (2	.PBStatus
delivery_count (

view_ucount	 (
"
send_status
 (2
.PBSendStatus
type (2
.PBEdmType
template_id (
	plan_flag
 (
plan_send_time (

sender_type (

reply_count (


reply_rate (


fail_count (

opened_rate (

	send_rate (
"?
PBDynamicQuickTextListRsp"
list (2.PBQuickTextListItem"Z
PBQuickTextListItem
texts (2.PBQuickTextItem
	item_name (	
user_id (
"B
PBQuickTextItem
text_id (
group_id (
text (	"+
PBSearchAssociateListReq
keyword (	"@
PBSearchAssociateListRsp$
list (2.PBSearchAssociateInfo"U
PBSearchAssociateInfo

companyHashId (	
companyName (	
homepage (	"�
PBFormFieldListReq

company_id (
customer_id (
company_hash_id (	
company_hash_origin (	
archive_flag (
type (	
business_card_id (
buyer_account_id (
seller_account_id	 (
lead_id
 ("�
PBFormFieldListRsp(
company (2.PBFormFieldCompanyInfo+
	customers (2.PBFormFieldCustomerInfo&
system (2.PBFormFieldSystemInfo"�
PBFormFieldCompanyInfo
group_id (
name (	

company_id ((
contact (2.PBFormFieldContactInfo
fields (2.PBFieldItem"5
PBFormFieldContactInfo
type (	
value (	"�
PBFormFieldCustomerInfo

company_id (
customer_id (
email (	
main_customer_flag (
name (	
fields (2.PBFieldItem"y
PBFormFieldSystemInfo
group_id (
name (	"
config (2.PBConfigFieldItem
fields (2.PBFieldItem"Q
PBConfigFieldItem
group_id (
name (	
fields (2.PBFieldItem"�
PBSubmitCompanyReq

company_id (
archive_flag (
company_hash_id (	
company_hash_origin (	1
company (2 .PBSubmitCompanyReq.CompanyEntry(
	customers (2.PBSubmitCustomerInfo
lead_id (
business_card_id (
buyer_account_id	 (
seller_account_id
 (.
CompanyEntry
key (	
value (	:8"r
PBSubmitCustomerInfo-
info (2.PBSubmitCustomerInfo.InfoEntry+
	InfoEntry
key (	
value (	:8"F
PBSubmitCompanyRsp

company_id (
swarms (2.PBSwarmInfo"-
PBSwarmInfo
swarm_id (
name (	":
PBCustomerFieldByFileReq
file_id (
vcard (	"s
PBCustomerSwarmReadListReq
user_id (
"
params (2.PBSwarmReadParams 

swarm_type (2.PBSwarmType"6
PBSwarmReadParams
user_id (

show_all (
"L
PBCustomerSwarmReadListRsp
data (2.PBSwarmListField
count (
"k
PBSwarmListField
swarm_id (
name (	
customer_count (

node (2.PBSwarmListField"<
PBFieldRuleConfigRsp$
info (2.PBFieldRuleConfigInfo"f
PBFieldRuleConfigInfo
	rule_type (

	rule_name (	\'
config_info (2.PBSwarmConfigInfo"u
PBSwarmConfigInfo
base (

ext_info (	

field_type (


id (	
name (	

refer_type (
"�
PBCustomerContactInfo

session_id (
sns_type (	

company_id (
customer_id (
sns_id (	
sns_nickname (	

sns_avatar (	
user_sns_id (	
user_sns_nickname	 (	
user_sns_avatar
 (	
message_sync_time (	
message_sync_count (3
last_message
 (2.PBCustomerContactMessageInfo

user_nickname (	"7
$PBCustomerContactMessageBodyTextInfo
content (	"5
%PBCustomerContactMessageBodyImageInfo
link (	"5
%PBCustomerContactMessageBodyAudioInfo
link (	"5
%PBCustomerContactMessageBodyVideoInfo
link (	"Y
$PBCustomerContactMessageBodyFileInfo
	file_name (	
	file_size (	
url (	"b
%PBCustomerContactMessageBodyEventInfo
event (	
updateSeqId (	

reactionEmoji (	"7
\'PBCustomerContactMessageBodyContactInfo
info (	"7
\'PBCustomerContactMessageBodyStickerInfo
link (	"n
(PBCustomerContactMessageBodyLocationInfo
address (	
latitude (
	longitude (
name (	"H
(PBCustomerContactMessageBodyTemplateInfo
struct (	
data (	";
+PBCustomerContactMessageBodyUnsupportedInfo
data (	"�
PBCustomerContactMessageInfo

id (

message_id (	-
	send_type (2.PBCustomerContactSendType
	send_time (	
seq_id (
sns_id (	
sns_nickname (	

sns_avatar (	
type	 (	5
text
 (2%.PBCustomerContactMessageBodyTextInfoH 7
image (2&.PBCustomerContactMessageBodyImageInfoH 7
audio (2&.PBCustomerContactMessageBodyAudioInfoH 7
video
 (2&.PBCustomerContactMessageBodyVideoInfoH 5
file (2%.PBCustomerContactMessageBodyFileInfoH 7
event (2&.PBCustomerContactMessageBodyEventInfoH ;
contact (2(.PBCustomerContactMessageBodyContactInfoH ;
sticker (2(.PBCustomerContactMessageBodyStickerInfoH =
location (2).PBCustomerContactMessageBodyLocationInfoH =
template (2).PBCustomerContactMessageBodyTemplateInfoH C
unsupported (2,.PBCustomerContactMessageBodyUnsupportedInfoH 

session_id (B
body"@
PBCustomerContactListReq

company_id (	
sns_type (	"@
PBCustomerContactListRsp$
list (2.PBCustomerContactInfo"X
PBCustomerContactMessageListReq

session_id (
pageSize (

curPage (
"]
PBCustomerContactMessageListRsp
total (
+
list (2.PBCustomerContactMessageInfo"�
PBContactIdentifyReq
platform (


identifier (	
direct_owner (
user_id (
seller_account_id (
corp_wecom_open_userid (	"�
PBContactIdentifyRsp
	card_type (

company_id (
name (	

short_name (	
email (	
	serial_id (	
country (	
timezone (	
identity_id	 (
owner
 (2.PBUserInfo+
trail_status (2.PBCustomerStatusInfo&
	flow_link (2.PBUserFlowLinkInfo/
ali_customer_info
 (2.PBAliCustomerInfoH 3
wecom_customer_info (2.PBWecomCustomerInfoH 
lead_id (B
data"K
PBAliCustomerInfo
ali_company_id (
contact_info_allowable ("a
PBWecomCustomerInfo
name (	
corp_full_name (	
position (	

createtime (
"�
PBContactBindCustomerReq
sns_type (	
sns_id (	
customer_id (
user_sns_id (	
update_contact (
	is_rebind ("*
PBContactBindCustomerRsp
result ("�
PBCompanyWithContactMessageInfo

company_id (
user_id (

name (	<
last_whatsapp_message (2.PBCustomerContactMessageInfo
latest_whatsapp_time (	$
latest_whatsapp_receive_time (	"�
PBContactCompanyListReq

sort_field (	
	sort_type (	
offset (

limit (

user_id (
swarm_id (-
	send_type (2.PBCustomerContactSendType

start_date	 (	
end_date
 (	
user_sns_id (	
sns_type (	"X
PBContactCompanyListRsp
total (
.
list (2 .PBCompanyWithContactMessageInfo"`
PBImportantCompanyReq
swarm_id (
user_id (

start_date (	
end_date (	"]
PBImportantCompanyRsp

company_count (
-
company_list (2.PBImportantCompanyInfo"�
PBImportantCompanyInfo
company_name (	

company_id (
last_remark_content (	
last_remark_trail_id (
remark_time (	"�
PBTemplateLanguage
language_id (
name (	
	namespace (	
category (	
language (	 

components (2.PBComponent
waba_template_id (	
config (	"�
PBAvailableTemplateRsp:
	templates (2\'.PBAvailableTemplateRsp.PBTemplateGroupY
PBTemplateGroup
name (	
category (	&
	languages (2.PBTemplateLanguage"{
PBBusinessCardByFileIdReq
file_id (
file_url (	
vcard (	

save_draft (
business_card_id ("�
PBSaveBusinessCardReq
business_card_id (\'
companyCard (2.PBCompanyCardInfo)
customerCard (2.PBCustomerCardInfo
enable_flag (
image (2.PBImageInfo"�
PBCompanyCardInfo
name (	
address (	
tel (2
.PBTelInfo
fax (	
homepage (	%
origin (2.PBCustomerOriginInfo
remark (	"�
PBCustomerCardInfo
name (	
email (	
tel_list (2
.PBTelInfo
post (	!
contact (2.PBSocialContact"?
PBSocialContact
type (	
value (	
website (	"3
PBBusinessCardDetailReq
business_card_id ("�
PBBusinessCardListReq
	page_size (

page_no (

archive_type (

content (	
user_id (


department_id (
%
status (2.PBBusinessCardStatus"�
PBBusinessCardRsp)
customerFields (2.PBCustomerFields\'

companyFields (2.PBCompanyFields
image (2.PBImageInfo
business_card_id (

is_archive (

company_id (

is_background (
create_customer_flag (
edit_customer_flag	 (%
systemFields
 (2.PBSystemFields
transferable (

archivable ( 

back_image
 (2.PBImageInfo"-
PBSystemFields
field (2.PBFieldItem"O
PBBusinessCardTransferReq
business_card_id (
belonger_user_id ("7
PBBusinessCardOperateLogReq
business_card_id ("=
PBBusinessCardOperateLogRsp
list (2.PBBCOpLogDetail"o
PBBCOpLogDetail
operator_user_id (

operate_event (	
operate_time (

operator_name (	"&
PBImageInfo

id (
url (	"/
PBCustomerFields
field (2.PBFieldItem".
PBCompanyFields
field (2.PBFieldItem"P
PBBusinessCardListRsp
total (
(
list (2.PBBusinessCardSummaryInfo"�
PBBusinessCardSummaryInfo\'
companyCard (2.PBCompanyCardInfo)
customerCard (2.PBCustomerCardInfo
business_card_id (
image (2.PBImageInfo

is_archive (
create_time (
erasable (
transferable (

archivable	 ( 

back_image
 (2.PBImageInfo"�
PBSaveStenographyReq
stenography_id (
content (	
enable_flag (
business_card_id (
images (2.PBFileListH B
attachments"0
PBStenographyDetailReq
stenography_id ("T
PBStenographyListReq
	page_size (

page_no (

business_card_id ("a
PBStenographyListRsp
business_card_id (
total (
 
list (2.PBStenographyInfo"
PBStenographyInfo
create_time (
stenography_id (
content (	
images (2.PBFileListH B
attachments"T
PBStenographyDetailRsp
business_card_id ( 
data (2.PBStenographyInfo"=
PBBatchSaveBusinessCardReq
	vcardList (2.PBVcardInfo"Y
PBVcardInfo
file_id (
file_url (	
vcard (	
business_card_id ("=
PBBatchSaveBusinessCardRsp
	vcardList (2.PBVcardInfo"L
PBCheckBusinessFieldRsp1
list (2#.PBBusinessCompanyFieldConflictInfo"�
"PBBusinessCompanyFieldConflictInfo
is_viewable (
is_editable (
	is_public (
has_pool_privilege (
duplicate_fields (	

company_id (
	user_list (2.PBUserInfo
name (	
create_time	 (
check_owner
 ("N
PBTMArchivingCompanyReq
seller_account_id (
buyer_account_id ("B
PBTMArchivingCompanyRsp

company_id (
customer_id ("-
PBAvailableUserListReq
company_ids ("3
PBAvailableUserListRsp
list (2.PBUserInfo"z
PBCustomerTrailEditReq
content (	
customer_id (
trail_id (
follow_up_time (
file_ids ("+
PBCustomerInfoListReq

company_id (";
PBCustomerInfoListRsp"
	info_list (2.PBCustomerInfo"B
PBCustomerRemarkTypeListRsp#
	type_list (2.PBTrailTypeInfo"=
PBQuickTemplateListReq
cur_page (

	page_size (
"Q
PBQuickTemplateListRsp"
list (2.PBQuickTemplateItem
total_count (
"H
PBQuickTemplateItem
text (2.PBQuickTextItem
	item_name (	"&
PBOriginLeadsReq

company_id ("9
PBOriginLeadsRsp%
	lead_list (2.PBOriginLeadsList"J
PBOriginLeadsList
lead_id (
name (	
main_lead_flag ("<
PBProductGroupIdsRsp$
list (2.PBProductGroupIdsItem"l
PBProductGroupIdsItem

id (
name (	

order_rank (%
nodes (2.PBProductGroupIdsItem"A
PBCompanyFieldListReq

company_id (
archive_flag ("K
PBCompanyFieldListRsp2
company_field_groups (2.PBCompanyFieldGroup"U
PBCompanyFieldGroup
group_id (

name (	
fields (2.PBCommonField"i
PBFilterFieldInfo
field_id (	%
value (2.google.protobuf.Value

refer_type (2.PBType"�
PBAppCompanyBaseInfo

company_id (
company_name (	 

attributes (2.PBAttribute
company_tag (2
.PBTagInfo
country (2
.PBCountry+
trail_status (2.PBCustomerStatusInfo
star (2.PBStar)
last_trail_info (2.PBTrailBaseInfo4
last_update_time	 (2.google.protobuf.Timestamp
pin_flag
 ($
growth_level (2.PBGrowthLevel
origin_list ("9
	PBCountry
name (	
code (	
timezone (	"S
PBTrailBaseInfo
trail_id (
format (	
module (2.PBTrailModule"z
PBRemarkRelateBusinessReq

company_id (
keywords (	

refer_type (

cur_page (

	page_size (
"q
PBRemarkRelateBusinessRsp0
relate_opportunity (2.PBRelateOpportunity"
relate_mail (2
.PBRelateMail"�
PBRelateOpportunity9
relate_opportunity_list (2.PBRelateOpportunityItem
count (

cur_page (

	page_size (
"o
PBRelateMail+
relate_mail_list (2.PBRelateMailItem
count (

cur_page (

	page_size (
"�
PBRelateOpportunityItem
opportunity_id (
name (	
	serial_id (	
currency (	
amount (
create_time (	"s
PBRelateMailItem
search_subject (	
search_content (	
	mail_type (2.PBMailType
mail_id ("?
PBSetMainCustomerReq

company_id (
customer_id ("+
PBSetMainCustomerRsp
customer_id ("�
PBSubmitCustomersReq

company_id ((
	customers (2.PBSubmitCustomerInfo
check_public (
user_id (
archive_flag (

leads_flag (
save_all (
seller_account_id (
buyer_account_id	 (
store_id
 ("D
PBSubmitCustomersRsp

company_id (
new_customer_ids (*P
PBTrailCommentSceneType
_UNKNOWN 
CUSTOMER
LEAD
OPPORTUNITY	*+
SendMailEnum
	NONE_SEND 
HAD_SEND*j

StageTypeEnum

STAGE_ON_NONE 
STAGE_ON_GOING_STATUS
STAGE_WIN_STATUS
STAGE_FAIL_STATUS*I
MainCustomerFlagEnum
ALL 

MAIN_CUSTOMER
NOT_MAIN_CUSTOMER*l
WillPublicEnum
WILL_PUBLIC_DAY_0 
WILL_PUBLIC_DAY_3
WILL_PUBLIC_DAY_5
WILL_PUBLIC_DAY_7*J

PBSearchModel
SEARCHER 
	LIKE_LEFT

LIKE_RIGHT
LIKE_ALL*0
PBSearchTagMatchMode
COMPLETE 

SINGLE*O
PBSendStatus
DEFAULT_STATUS 
SENDING
SENT
SCHEDULE_SENDING*<

PBTriggerType
DEFAULT_TYPE 

MANUAL
	AUTOMATIC*�
PBStatus
STATUS_WAIT_START 
STATUS_DECOMPOSED
STATUS_SENDING

STATUS_FINISH
STATUS_FAIL

STATUS_END

STATUS_IGNORE*5
	PBEdmType
DEFAULT_EDM_TYPE 

NORMAL
AI*B
PBSwarmType
TYPE_DEFAULT 
TYPE_PRIVATE
TYPE_PUBLIC*a
PBCustomerContactSendType
SEND_TYPE_NONE 
SEND_TYPE_BY_USER
SEND_TYPE_BY_CUSTOMER*]
PBCompanyOwnerType
TYPE_ALL_COMPANY 
TYPE_PRIVATE_COMPANY
TYPE_PUBLIC_COMPANY*s

PBGrowthLevel
GROWTH_LEVEL_0 
GROWTH_LEVEL_1
GROWTH_LEVEL_2
GROWTH_LEVEL_3
GROWTH_LEVEL_4*A
	PBShowAll
DEFAULT_SETTING 

PERSONAL_ONLY
SHOW_ALL*�

PBTrailModule
MODULE_UNKNOWN 

MODULE_REMARK
MODULE_MAIL

MODULE_EDM
MODULE_QUOTATION
	MODULE_PI
MODULE_CUSTOMER
MODULE_USER
MODULE_ORDER
MODULE_CALL	
MODULE_MEETING

MODULE_PAY_CALL
MODULE_OPPORTUNITY
MODULE_SITE_TRACK

MODULE_ALIBABA_TRADE
MODULE_CONTACT_MESSAGE*t
PBBusinessCardStatus
BUSINESS_CARD_SAVED_STATUS 
BUSINESS_CARD_DRAFT_STATUS
BUSINESS_CARD_ALL_STATUS*A
PBBusinessCardSaveType
TYPE_SAVE_BY_IDS 

TYPE_SAVE_ALLBD
cn.xiaoman.apollo.proto�protobuf\\Customer�protobuf\\GPBMetadatabproto3'
        , true);

        static::$is_initialized = true;
    }
}

