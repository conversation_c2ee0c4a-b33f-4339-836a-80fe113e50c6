<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Chat.proto

namespace protobuf\Chat;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>PBChannelUpdate</code>
 */
class PBChannelUpdate extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>uint64 channel_id = 1;</code>
     */
    protected $channel_id = 0;
    /**
     * Generated from protobuf field <code>uint32 online_status = 2;</code>
     */
    protected $online_status = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $channel_id
     *     @type int $online_status
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\Chat::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>uint64 channel_id = 1;</code>
     * @return int|string
     */
    public function getChannelId()
    {
        return $this->channel_id;
    }

    /**
     * Generated from protobuf field <code>uint64 channel_id = 1;</code>
     * @param int|string $var
     * @return $this
     */
    public function setChannelId($var)
    {
        GPBUtil::checkUint64($var);
        $this->channel_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 online_status = 2;</code>
     * @return int
     */
    public function getOnlineStatus()
    {
        return $this->online_status;
    }

    /**
     * Generated from protobuf field <code>uint32 online_status = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setOnlineStatus($var)
    {
        GPBUtil::checkUint32($var);
        $this->online_status = $var;

        return $this;
    }

}

