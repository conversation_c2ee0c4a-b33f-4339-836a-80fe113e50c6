<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Chat.proto

namespace protobuf\Chat;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * 表单元数据
 *
 * Generated from protobuf message <code>FormMeta</code>
 */
class FormMeta extends \Google\Protobuf\Internal\Message
{
    /**
     *表单id
     *
     * Generated from protobuf field <code>int64 form_id = 1;</code>
     */
    protected $form_id = 0;
    /**
     *表单名称
     *
     * Generated from protobuf field <code>string form_name = 2;</code>
     */
    protected $form_name = '';
    /**
     *展示方式，1水平,2垂直
     *
     * Generated from protobuf field <code>int32 show_type = 3;</code>
     */
    protected $show_type = 0;
    /**
     * Generated from protobuf field <code>int32 layout_type = 4;</code>
     */
    protected $layout_type = 0;
    /**
     * Generated from protobuf field <code>int32 enable_name_flag = 5;</code>
     */
    protected $enable_name_flag = 0;
    /**
     * Generated from protobuf field <code>int32 show_flag = 6;</code>
     */
    protected $show_flag = 0;
    /**
     *表单字段
     *
     * Generated from protobuf field <code>repeated .FormField field_item = 8;</code>
     */
    private $field_item;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $form_id
     *          表单id
     *     @type string $form_name
     *          表单名称
     *     @type int $show_type
     *          展示方式，1水平,2垂直
     *     @type int $layout_type
     *     @type int $enable_name_flag
     *     @type int $show_flag
     *     @type array<\protobuf\Chat\FormField>|\Google\Protobuf\Internal\RepeatedField $field_item
     *          表单字段
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\Chat::initOnce();
        parent::__construct($data);
    }

    /**
     *表单id
     *
     * Generated from protobuf field <code>int64 form_id = 1;</code>
     * @return int|string
     */
    public function getFormId()
    {
        return $this->form_id;
    }

    /**
     *表单id
     *
     * Generated from protobuf field <code>int64 form_id = 1;</code>
     * @param int|string $var
     * @return $this
     */
    public function setFormId($var)
    {
        GPBUtil::checkInt64($var);
        $this->form_id = $var;

        return $this;
    }

    /**
     *表单名称
     *
     * Generated from protobuf field <code>string form_name = 2;</code>
     * @return string
     */
    public function getFormName()
    {
        return $this->form_name;
    }

    /**
     *表单名称
     *
     * Generated from protobuf field <code>string form_name = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setFormName($var)
    {
        GPBUtil::checkString($var, True);
        $this->form_name = $var;

        return $this;
    }

    /**
     *展示方式，1水平,2垂直
     *
     * Generated from protobuf field <code>int32 show_type = 3;</code>
     * @return int
     */
    public function getShowType()
    {
        return $this->show_type;
    }

    /**
     *展示方式，1水平,2垂直
     *
     * Generated from protobuf field <code>int32 show_type = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setShowType($var)
    {
        GPBUtil::checkInt32($var);
        $this->show_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 layout_type = 4;</code>
     * @return int
     */
    public function getLayoutType()
    {
        return $this->layout_type;
    }

    /**
     * Generated from protobuf field <code>int32 layout_type = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setLayoutType($var)
    {
        GPBUtil::checkInt32($var);
        $this->layout_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 enable_name_flag = 5;</code>
     * @return int
     */
    public function getEnableNameFlag()
    {
        return $this->enable_name_flag;
    }

    /**
     * Generated from protobuf field <code>int32 enable_name_flag = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setEnableNameFlag($var)
    {
        GPBUtil::checkInt32($var);
        $this->enable_name_flag = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 show_flag = 6;</code>
     * @return int
     */
    public function getShowFlag()
    {
        return $this->show_flag;
    }

    /**
     * Generated from protobuf field <code>int32 show_flag = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setShowFlag($var)
    {
        GPBUtil::checkInt32($var);
        $this->show_flag = $var;

        return $this;
    }

    /**
     *表单字段
     *
     * Generated from protobuf field <code>repeated .FormField field_item = 8;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getFieldItem()
    {
        return $this->field_item;
    }

    /**
     *表单字段
     *
     * Generated from protobuf field <code>repeated .FormField field_item = 8;</code>
     * @param array<\protobuf\Chat\FormField>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFieldItem($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \protobuf\Chat\FormField::class);
        $this->field_item = $arr;

        return $this;
    }

}

