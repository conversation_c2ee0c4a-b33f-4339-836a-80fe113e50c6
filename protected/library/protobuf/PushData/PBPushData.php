<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: PushData.proto

namespace protobuf\PushData;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>PBPushData</code>
 */
class PBPushData extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>repeated .PBPushDatum list = 1;</code>
     */
    private $list;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type array<\protobuf\PushData\PBPushDatum>|\Google\Protobuf\Internal\RepeatedField $list
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\PushData::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>repeated .PBPushDatum list = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getList()
    {
        return $this->list;
    }

    /**
     * Generated from protobuf field <code>repeated .PBPushDatum list = 1;</code>
     * @param array<\protobuf\PushData\PBPushDatum>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \protobuf\PushData\PBPushDatum::class);
        $this->list = $arr;

        return $this;
    }

}

