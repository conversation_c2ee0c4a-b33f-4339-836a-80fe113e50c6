<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Customer.proto

namespace protobuf\Customer;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * 社交平台聊天客户列表请求 customerRead/contactCompanyList
 *
 * Generated from protobuf message <code>PBContactCompanyListReq</code>
 */
class PBContactCompanyListReq extends \Google\Protobuf\Internal\Message
{
    /**
     * latest_whatsapp_time
     *
     * Generated from protobuf field <code>string sort_field = 2;</code>
     */
    protected $sort_field = '';
    /**
     * desc,asc
     *
     * Generated from protobuf field <code>string sort_type = 3;</code>
     */
    protected $sort_type = '';
    /**
     * Generated from protobuf field <code>uint32 offset = 4;</code>
     */
    protected $offset = 0;
    /**
     * Generated from protobuf field <code>uint32 limit = 5;</code>
     */
    protected $limit = 0;
    /**
     *需要查看的user_id
     *
     * Generated from protobuf field <code>repeated int32 user_id = 6;</code>
     */
    private $user_id;
    /**
     *主客群id
     *
     * Generated from protobuf field <code>uint64 swarm_id = 7;</code>
     */
    protected $swarm_id = 0;
    /**
     *发送方
     *
     * Generated from protobuf field <code>.PBCustomerContactSendType send_type = 8;</code>
     */
    protected $send_type = 0;
    /**
     * Generated from protobuf field <code>string start_date = 9;</code>
     */
    protected $start_date = '';
    /**
     * Generated from protobuf field <code>string end_date = 10;</code>
     */
    protected $end_date = '';
    /**
     * 社交账号id
     *
     * Generated from protobuf field <code>repeated string user_sns_id = 11;</code>
     */
    private $user_sns_id;
    /**
     * whatsapp,whatsapp_business
     *
     * Generated from protobuf field <code>string sns_type = 12;</code>
     */
    protected $sns_type = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $sort_field
     *           latest_whatsapp_time
     *     @type string $sort_type
     *           desc,asc
     *     @type int $offset
     *     @type int $limit
     *     @type array<int>|\Google\Protobuf\Internal\RepeatedField $user_id
     *          需要查看的user_id
     *     @type int|string $swarm_id
     *          主客群id
     *     @type int $send_type
     *          发送方
     *     @type string $start_date
     *     @type string $end_date
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $user_sns_id
     *           社交账号id
     *     @type string $sns_type
     *           whatsapp,whatsapp_business
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\Customer::initOnce();
        parent::__construct($data);
    }

    /**
     * latest_whatsapp_time
     *
     * Generated from protobuf field <code>string sort_field = 2;</code>
     * @return string
     */
    public function getSortField()
    {
        return $this->sort_field;
    }

    /**
     * latest_whatsapp_time
     *
     * Generated from protobuf field <code>string sort_field = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setSortField($var)
    {
        GPBUtil::checkString($var, True);
        $this->sort_field = $var;

        return $this;
    }

    /**
     * desc,asc
     *
     * Generated from protobuf field <code>string sort_type = 3;</code>
     * @return string
     */
    public function getSortType()
    {
        return $this->sort_type;
    }

    /**
     * desc,asc
     *
     * Generated from protobuf field <code>string sort_type = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setSortType($var)
    {
        GPBUtil::checkString($var, True);
        $this->sort_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 offset = 4;</code>
     * @return int
     */
    public function getOffset()
    {
        return $this->offset;
    }

    /**
     * Generated from protobuf field <code>uint32 offset = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setOffset($var)
    {
        GPBUtil::checkUint32($var);
        $this->offset = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 limit = 5;</code>
     * @return int
     */
    public function getLimit()
    {
        return $this->limit;
    }

    /**
     * Generated from protobuf field <code>uint32 limit = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setLimit($var)
    {
        GPBUtil::checkUint32($var);
        $this->limit = $var;

        return $this;
    }

    /**
     *需要查看的user_id
     *
     * Generated from protobuf field <code>repeated int32 user_id = 6;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getUserId()
    {
        return $this->user_id;
    }

    /**
     *需要查看的user_id
     *
     * Generated from protobuf field <code>repeated int32 user_id = 6;</code>
     * @param array<int>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setUserId($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::INT32);
        $this->user_id = $arr;

        return $this;
    }

    /**
     *主客群id
     *
     * Generated from protobuf field <code>uint64 swarm_id = 7;</code>
     * @return int|string
     */
    public function getSwarmId()
    {
        return $this->swarm_id;
    }

    /**
     *主客群id
     *
     * Generated from protobuf field <code>uint64 swarm_id = 7;</code>
     * @param int|string $var
     * @return $this
     */
    public function setSwarmId($var)
    {
        GPBUtil::checkUint64($var);
        $this->swarm_id = $var;

        return $this;
    }

    /**
     *发送方
     *
     * Generated from protobuf field <code>.PBCustomerContactSendType send_type = 8;</code>
     * @return int
     */
    public function getSendType()
    {
        return $this->send_type;
    }

    /**
     *发送方
     *
     * Generated from protobuf field <code>.PBCustomerContactSendType send_type = 8;</code>
     * @param int $var
     * @return $this
     */
    public function setSendType($var)
    {
        GPBUtil::checkEnum($var, \protobuf\Customer\PBCustomerContactSendType::class);
        $this->send_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string start_date = 9;</code>
     * @return string
     */
    public function getStartDate()
    {
        return $this->start_date;
    }

    /**
     * Generated from protobuf field <code>string start_date = 9;</code>
     * @param string $var
     * @return $this
     */
    public function setStartDate($var)
    {
        GPBUtil::checkString($var, True);
        $this->start_date = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string end_date = 10;</code>
     * @return string
     */
    public function getEndDate()
    {
        return $this->end_date;
    }

    /**
     * Generated from protobuf field <code>string end_date = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setEndDate($var)
    {
        GPBUtil::checkString($var, True);
        $this->end_date = $var;

        return $this;
    }

    /**
     * 社交账号id
     *
     * Generated from protobuf field <code>repeated string user_sns_id = 11;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getUserSnsId()
    {
        return $this->user_sns_id;
    }

    /**
     * 社交账号id
     *
     * Generated from protobuf field <code>repeated string user_sns_id = 11;</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setUserSnsId($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->user_sns_id = $arr;

        return $this;
    }

    /**
     * whatsapp,whatsapp_business
     *
     * Generated from protobuf field <code>string sns_type = 12;</code>
     * @return string
     */
    public function getSnsType()
    {
        return $this->sns_type;
    }

    /**
     * whatsapp,whatsapp_business
     *
     * Generated from protobuf field <code>string sns_type = 12;</code>
     * @param string $var
     * @return $this
     */
    public function setSnsType($var)
    {
        GPBUtil::checkString($var, True);
        $this->sns_type = $var;

        return $this;
    }

}

