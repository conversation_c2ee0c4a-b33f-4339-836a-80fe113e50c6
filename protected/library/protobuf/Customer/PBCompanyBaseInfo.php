<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Customer.proto

namespace protobuf\Customer;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * 客户基本信息类型
 *
 * Generated from protobuf message <code>PBCompanyBaseInfo</code>
 */
class PBCompanyBaseInfo extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>uint64 company_id = 1;</code>
     */
    protected $company_id = 0;
    /**
     * Generated from protobuf field <code>repeated uint32 user_ids = 2;</code>
     */
    private $user_ids;
    /**
     * Generated from protobuf field <code>string name = 3;</code>
     */
    protected $name = '';
    /**
     * Generated from protobuf field <code>string short_name = 4;</code>
     */
    protected $short_name = '';
    /**
     * Generated from protobuf field <code>.PBStar star = 5;</code>
     */
    protected $star = 0;
    /**
     * Generated from protobuf field <code>uint64 trail_status = 6;</code>
     */
    protected $trail_status = 0;
    /**
     * Generated from protobuf field <code>uint64 group_id = 7;</code>
     */
    protected $group_id = 0;
    /**
     * Generated from protobuf field <code>.PBCustomerScore score = 9;</code>
     */
    protected $score = null;
    /**
     * Generated from protobuf field <code>repeated uint64 tag = 10;</code>
     */
    private $tag;
    /**
     *-1表示未开通公海分组功能
     *
     * Generated from protobuf field <code>sint64 pool_id = 11;</code>
     */
    protected $pool_id = 0;
    /**
     * Generated from protobuf field <code>bool pin_flag = 12;</code>
     */
    protected $pin_flag = false;
    /**
     *重复标识
     *
     * Generated from protobuf field <code>uint32 duplicate_flag = 13;</code>
     */
    protected $duplicate_flag = 0;
    /**
     *客户标签
     *
     * Generated from protobuf field <code>repeated .PBTagInfo tag_list = 14;</code>
     */
    private $tag_list;
    /**
     *主联系人信息
     *
     * Generated from protobuf field <code>.PBCustomerDetailInfo main_customer_info = 15;</code>
     */
    protected $main_customer_info = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $company_id
     *     @type array<int>|\Google\Protobuf\Internal\RepeatedField $user_ids
     *     @type string $name
     *     @type string $short_name
     *     @type int $star
     *     @type int|string $trail_status
     *     @type int|string $group_id
     *     @type \protobuf\Customer\PBCustomerScore $score
     *     @type array<int>|array<string>|\Google\Protobuf\Internal\RepeatedField $tag
     *     @type int|string $pool_id
     *          -1表示未开通公海分组功能
     *     @type bool $pin_flag
     *     @type int $duplicate_flag
     *          重复标识
     *     @type array<\protobuf\CRMCommon\PBTagInfo>|\Google\Protobuf\Internal\RepeatedField $tag_list
     *          客户标签
     *     @type \protobuf\Customer\PBCustomerDetailInfo $main_customer_info
     *          主联系人信息
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\Customer::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>uint64 company_id = 1;</code>
     * @return int|string
     */
    public function getCompanyId()
    {
        return $this->company_id;
    }

    /**
     * Generated from protobuf field <code>uint64 company_id = 1;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCompanyId($var)
    {
        GPBUtil::checkUint64($var);
        $this->company_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated uint32 user_ids = 2;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getUserIds()
    {
        return $this->user_ids;
    }

    /**
     * Generated from protobuf field <code>repeated uint32 user_ids = 2;</code>
     * @param array<int>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setUserIds($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::UINT32);
        $this->user_ids = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string name = 3;</code>
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Generated from protobuf field <code>string name = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setName($var)
    {
        GPBUtil::checkString($var, True);
        $this->name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string short_name = 4;</code>
     * @return string
     */
    public function getShortName()
    {
        return $this->short_name;
    }

    /**
     * Generated from protobuf field <code>string short_name = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setShortName($var)
    {
        GPBUtil::checkString($var, True);
        $this->short_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.PBStar star = 5;</code>
     * @return int
     */
    public function getStar()
    {
        return $this->star;
    }

    /**
     * Generated from protobuf field <code>.PBStar star = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setStar($var)
    {
        GPBUtil::checkEnum($var, \protobuf\CRMCommon\PBStar::class);
        $this->star = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint64 trail_status = 6;</code>
     * @return int|string
     */
    public function getTrailStatus()
    {
        return $this->trail_status;
    }

    /**
     * Generated from protobuf field <code>uint64 trail_status = 6;</code>
     * @param int|string $var
     * @return $this
     */
    public function setTrailStatus($var)
    {
        GPBUtil::checkUint64($var);
        $this->trail_status = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint64 group_id = 7;</code>
     * @return int|string
     */
    public function getGroupId()
    {
        return $this->group_id;
    }

    /**
     * Generated from protobuf field <code>uint64 group_id = 7;</code>
     * @param int|string $var
     * @return $this
     */
    public function setGroupId($var)
    {
        GPBUtil::checkUint64($var);
        $this->group_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.PBCustomerScore score = 9;</code>
     * @return \protobuf\Customer\PBCustomerScore|null
     */
    public function getScore()
    {
        return $this->score;
    }

    public function hasScore()
    {
        return isset($this->score);
    }

    public function clearScore()
    {
        unset($this->score);
    }

    /**
     * Generated from protobuf field <code>.PBCustomerScore score = 9;</code>
     * @param \protobuf\Customer\PBCustomerScore $var
     * @return $this
     */
    public function setScore($var)
    {
        GPBUtil::checkMessage($var, \protobuf\Customer\PBCustomerScore::class);
        $this->score = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated uint64 tag = 10;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getTag()
    {
        return $this->tag;
    }

    /**
     * Generated from protobuf field <code>repeated uint64 tag = 10;</code>
     * @param array<int>|array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setTag($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::UINT64);
        $this->tag = $arr;

        return $this;
    }

    /**
     *-1表示未开通公海分组功能
     *
     * Generated from protobuf field <code>sint64 pool_id = 11;</code>
     * @return int|string
     */
    public function getPoolId()
    {
        return $this->pool_id;
    }

    /**
     *-1表示未开通公海分组功能
     *
     * Generated from protobuf field <code>sint64 pool_id = 11;</code>
     * @param int|string $var
     * @return $this
     */
    public function setPoolId($var)
    {
        GPBUtil::checkInt64($var);
        $this->pool_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool pin_flag = 12;</code>
     * @return bool
     */
    public function getPinFlag()
    {
        return $this->pin_flag;
    }

    /**
     * Generated from protobuf field <code>bool pin_flag = 12;</code>
     * @param bool $var
     * @return $this
     */
    public function setPinFlag($var)
    {
        GPBUtil::checkBool($var);
        $this->pin_flag = $var;

        return $this;
    }

    /**
     *重复标识
     *
     * Generated from protobuf field <code>uint32 duplicate_flag = 13;</code>
     * @return int
     */
    public function getDuplicateFlag()
    {
        return $this->duplicate_flag;
    }

    /**
     *重复标识
     *
     * Generated from protobuf field <code>uint32 duplicate_flag = 13;</code>
     * @param int $var
     * @return $this
     */
    public function setDuplicateFlag($var)
    {
        GPBUtil::checkUint32($var);
        $this->duplicate_flag = $var;

        return $this;
    }

    /**
     *客户标签
     *
     * Generated from protobuf field <code>repeated .PBTagInfo tag_list = 14;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getTagList()
    {
        return $this->tag_list;
    }

    /**
     *客户标签
     *
     * Generated from protobuf field <code>repeated .PBTagInfo tag_list = 14;</code>
     * @param array<\protobuf\CRMCommon\PBTagInfo>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setTagList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \protobuf\CRMCommon\PBTagInfo::class);
        $this->tag_list = $arr;

        return $this;
    }

    /**
     *主联系人信息
     *
     * Generated from protobuf field <code>.PBCustomerDetailInfo main_customer_info = 15;</code>
     * @return \protobuf\Customer\PBCustomerDetailInfo|null
     */
    public function getMainCustomerInfo()
    {
        return $this->main_customer_info;
    }

    public function hasMainCustomerInfo()
    {
        return isset($this->main_customer_info);
    }

    public function clearMainCustomerInfo()
    {
        unset($this->main_customer_info);
    }

    /**
     *主联系人信息
     *
     * Generated from protobuf field <code>.PBCustomerDetailInfo main_customer_info = 15;</code>
     * @param \protobuf\Customer\PBCustomerDetailInfo $var
     * @return $this
     */
    public function setMainCustomerInfo($var)
    {
        GPBUtil::checkMessage($var, \protobuf\Customer\PBCustomerDetailInfo::class);
        $this->main_customer_info = $var;

        return $this;
    }

}

