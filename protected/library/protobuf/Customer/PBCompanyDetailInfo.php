<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Customer.proto

namespace protobuf\Customer;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * 客户详细信息类型
 *
 * Generated from protobuf message <code>PBCompanyDetailInfo</code>
 */
class PBCompanyDetailInfo extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>uint64 company_id = 1;</code>
     */
    protected $company_id = 0;
    /**
     * Generated from protobuf field <code>repeated uint32 user_id = 2;</code>
     */
    private $user_id;
    /**
     * Generated from protobuf field <code>string name = 3;</code>
     */
    protected $name = '';
    /**
     * Generated from protobuf field <code>string short_name = 4;</code>
     */
    protected $short_name = '';
    /**
     * Generated from protobuf field <code>.PBStar star = 5;</code>
     */
    protected $star = 0;
    /**
     * Generated from protobuf field <code>uint64 trail_status = 6;</code>
     */
    protected $trail_status = 0;
    /**
     * Generated from protobuf field <code>uint64 group_id = 7;</code>
     */
    protected $group_id = 0;
    /**
     * Generated from protobuf field <code>.PBCustomerScore score = 9;</code>
     */
    protected $score = null;
    /**
     * Generated from protobuf field <code>repeated uint64 tag = 10;</code>
     */
    private $tag;
    /**
     * Generated from protobuf field <code>uint64 pool_id = 11;</code>
     */
    protected $pool_id = 0;
    /**
     * Generated from protobuf field <code>bool pin_flag = 12;</code>
     */
    protected $pin_flag = false;
    /**
     * Generated from protobuf field <code>string serial_id = 13;</code>
     */
    protected $serial_id = '';
    /**
     * Generated from protobuf field <code>uint64 origin = 14;</code>
     */
    protected $origin = 0;
    /**
     * Generated from protobuf field <code>repeated .PBCustomerCategoryItem category_ids = 15;</code>
     */
    private $category_ids;
    /**
     * Generated from protobuf field <code>string biz_type = 16;</code>
     */
    protected $biz_type = '';
    /**
     * Generated from protobuf field <code>string timezone = 17;</code>
     */
    protected $timezone = '';
    /**
     * Generated from protobuf field <code>string country = 18;</code>
     */
    protected $country = '';
    /**
     * Generated from protobuf field <code>string province = 19;</code>
     */
    protected $province = '';
    /**
     * Generated from protobuf field <code>string city = 20;</code>
     */
    protected $city = '';
    /**
     * Generated from protobuf field <code>.PBScale scale_id = 21;</code>
     */
    protected $scale_id = 0;
    /**
     * Generated from protobuf field <code>string homepage = 22;</code>
     */
    protected $homepage = '';
    /**
     * Generated from protobuf field <code>string fax = 23;</code>
     */
    protected $fax = '';
    /**
     * Generated from protobuf field <code>string tel = 24;</code>
     */
    protected $tel = '';
    /**
     * Generated from protobuf field <code>string address = 25;</code>
     */
    protected $address = '';
    /**
     * Generated from protobuf field <code>string remark = 26;</code>
     */
    protected $remark = '';
    /**
     * Generated from protobuf field <code>string order_time = 27;</code>
     */
    protected $order_time = '';
    /**
     * Generated from protobuf field <code>string create_time = 28;</code>
     */
    protected $create_time = '';
    /**
     * Generated from protobuf field <code>string archive_time = 29;</code>
     */
    protected $archive_time = '';
    /**
     * Generated from protobuf field <code>uint32 last_owner = 30;</code>
     */
    protected $last_owner = 0;
    /**
     * Generated from protobuf field <code>uint32 archive_type = 31;</code>
     */
    protected $archive_type = 0;
    /**
     * Generated from protobuf field <code>string tel_area_code = 32;</code>
     */
    protected $tel_area_code = '';
    /**
     * Generated from protobuf field <code>repeated .PBFileInfo image_list = 35;</code>
     */
    private $image_list;
    /**
     * Generated from protobuf field <code>map<string, string> external_field_data = 36;</code>
     */
    private $external_field_data;
    /**
     * Generated from protobuf field <code>.PBCustomerDetailInfo customer = 37;</code>
     */
    protected $customer = null;
    /**
     * Generated from protobuf field <code>.PBTrailInfo last_trail_info = 38;</code>
     */
    protected $last_trail_info = null;
    /**
     *最近成交日期
     *
     * Generated from protobuf field <code>string deal_time = 39;</code>
     */
    protected $deal_time = '';
    /**
     *计入业绩订单数
     *
     * Generated from protobuf field <code>int32 performance_order_count = 40;</code>
     */
    protected $performance_order_count = 0;
    /**
     *赢单商机数
     *
     * Generated from protobuf field <code>int32 success_opportunity_count = 41;</code>
     */
    protected $success_opportunity_count = 0;
    /**
     *最近跟进动态
     *
     * Generated from protobuf field <code>.PBTrailInfo last_remark_trail = 42;</code>
     */
    protected $last_remark_trail = null;
    /**
     *下次跟进时间
     *
     * Generated from protobuf field <code>string next_follow_up_time = 43;</code>
     */
    protected $next_follow_up_time = '';
    /**
     *来源详情
     *
     * Generated from protobuf field <code>repeated .PBStoreInfo alibaba_store_info = 44;</code>
     */
    private $alibaba_store_info;
    /**
     * Generated from protobuf field <code>int32 annual_procurement = 45;</code>
     */
    protected $annual_procurement = 0;
    /**
     * Generated from protobuf field <code>int32 intention_level = 46;</code>
     */
    protected $intention_level = 0;
    /**
     * Generated from protobuf field <code>uint32 release_count = 47;</code>
     */
    protected $release_count = 0;
    /**
     * Generated from protobuf field <code>string private_time = 48;</code>
     */
    protected $private_time = '';
    /**
     * Generated from protobuf field <code>string edit_time = 49;</code>
     */
    protected $edit_time = '';
    /**
     * Generated from protobuf field <code>uint32 last_edit_user = 50;</code>
     */
    protected $last_edit_user = 0;
    /**
     * Generated from protobuf field <code>uint32 create_user = 51;</code>
     */
    protected $create_user = 0;
    /**
     * Generated from protobuf field <code>string public_time = 52;</code>
     */
    protected $public_time = '';
    /**
     * Generated from protobuf field <code>string recent_follow_up_time = 53;</code>
     */
    protected $recent_follow_up_time = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $company_id
     *     @type array<int>|\Google\Protobuf\Internal\RepeatedField $user_id
     *     @type string $name
     *     @type string $short_name
     *     @type int $star
     *     @type int|string $trail_status
     *     @type int|string $group_id
     *     @type \protobuf\Customer\PBCustomerScore $score
     *     @type array<int>|array<string>|\Google\Protobuf\Internal\RepeatedField $tag
     *     @type int|string $pool_id
     *     @type bool $pin_flag
     *     @type string $serial_id
     *     @type int|string $origin
     *     @type array<\protobuf\Customer\PBCustomerCategoryItem>|\Google\Protobuf\Internal\RepeatedField $category_ids
     *     @type string $biz_type
     *     @type string $timezone
     *     @type string $country
     *     @type string $province
     *     @type string $city
     *     @type int $scale_id
     *     @type string $homepage
     *     @type string $fax
     *     @type string $tel
     *     @type string $address
     *     @type string $remark
     *     @type string $order_time
     *     @type string $create_time
     *     @type string $archive_time
     *     @type int $last_owner
     *     @type int $archive_type
     *     @type string $tel_area_code
     *     @type array<\protobuf\CRMCommon\PBFileInfo>|\Google\Protobuf\Internal\RepeatedField $image_list
     *     @type array|\Google\Protobuf\Internal\MapField $external_field_data
     *     @type \protobuf\Customer\PBCustomerDetailInfo $customer
     *     @type \protobuf\Customer\PBTrailInfo $last_trail_info
     *     @type string $deal_time
     *          最近成交日期
     *     @type int $performance_order_count
     *          计入业绩订单数
     *     @type int $success_opportunity_count
     *          赢单商机数
     *     @type \protobuf\Customer\PBTrailInfo $last_remark_trail
     *          最近跟进动态
     *     @type string $next_follow_up_time
     *          下次跟进时间
     *     @type array<\protobuf\CRMCommon\PBStoreInfo>|\Google\Protobuf\Internal\RepeatedField $alibaba_store_info
     *          来源详情
     *     @type int $annual_procurement
     *     @type int $intention_level
     *     @type int $release_count
     *     @type string $private_time
     *     @type string $edit_time
     *     @type int $last_edit_user
     *     @type int $create_user
     *     @type string $public_time
     *     @type string $recent_follow_up_time
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\Customer::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>uint64 company_id = 1;</code>
     * @return int|string
     */
    public function getCompanyId()
    {
        return $this->company_id;
    }

    /**
     * Generated from protobuf field <code>uint64 company_id = 1;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCompanyId($var)
    {
        GPBUtil::checkUint64($var);
        $this->company_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated uint32 user_id = 2;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getUserId()
    {
        return $this->user_id;
    }

    /**
     * Generated from protobuf field <code>repeated uint32 user_id = 2;</code>
     * @param array<int>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setUserId($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::UINT32);
        $this->user_id = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string name = 3;</code>
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Generated from protobuf field <code>string name = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setName($var)
    {
        GPBUtil::checkString($var, True);
        $this->name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string short_name = 4;</code>
     * @return string
     */
    public function getShortName()
    {
        return $this->short_name;
    }

    /**
     * Generated from protobuf field <code>string short_name = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setShortName($var)
    {
        GPBUtil::checkString($var, True);
        $this->short_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.PBStar star = 5;</code>
     * @return int
     */
    public function getStar()
    {
        return $this->star;
    }

    /**
     * Generated from protobuf field <code>.PBStar star = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setStar($var)
    {
        GPBUtil::checkEnum($var, \protobuf\CRMCommon\PBStar::class);
        $this->star = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint64 trail_status = 6;</code>
     * @return int|string
     */
    public function getTrailStatus()
    {
        return $this->trail_status;
    }

    /**
     * Generated from protobuf field <code>uint64 trail_status = 6;</code>
     * @param int|string $var
     * @return $this
     */
    public function setTrailStatus($var)
    {
        GPBUtil::checkUint64($var);
        $this->trail_status = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint64 group_id = 7;</code>
     * @return int|string
     */
    public function getGroupId()
    {
        return $this->group_id;
    }

    /**
     * Generated from protobuf field <code>uint64 group_id = 7;</code>
     * @param int|string $var
     * @return $this
     */
    public function setGroupId($var)
    {
        GPBUtil::checkUint64($var);
        $this->group_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.PBCustomerScore score = 9;</code>
     * @return \protobuf\Customer\PBCustomerScore|null
     */
    public function getScore()
    {
        return $this->score;
    }

    public function hasScore()
    {
        return isset($this->score);
    }

    public function clearScore()
    {
        unset($this->score);
    }

    /**
     * Generated from protobuf field <code>.PBCustomerScore score = 9;</code>
     * @param \protobuf\Customer\PBCustomerScore $var
     * @return $this
     */
    public function setScore($var)
    {
        GPBUtil::checkMessage($var, \protobuf\Customer\PBCustomerScore::class);
        $this->score = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated uint64 tag = 10;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getTag()
    {
        return $this->tag;
    }

    /**
     * Generated from protobuf field <code>repeated uint64 tag = 10;</code>
     * @param array<int>|array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setTag($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::UINT64);
        $this->tag = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint64 pool_id = 11;</code>
     * @return int|string
     */
    public function getPoolId()
    {
        return $this->pool_id;
    }

    /**
     * Generated from protobuf field <code>uint64 pool_id = 11;</code>
     * @param int|string $var
     * @return $this
     */
    public function setPoolId($var)
    {
        GPBUtil::checkUint64($var);
        $this->pool_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool pin_flag = 12;</code>
     * @return bool
     */
    public function getPinFlag()
    {
        return $this->pin_flag;
    }

    /**
     * Generated from protobuf field <code>bool pin_flag = 12;</code>
     * @param bool $var
     * @return $this
     */
    public function setPinFlag($var)
    {
        GPBUtil::checkBool($var);
        $this->pin_flag = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string serial_id = 13;</code>
     * @return string
     */
    public function getSerialId()
    {
        return $this->serial_id;
    }

    /**
     * Generated from protobuf field <code>string serial_id = 13;</code>
     * @param string $var
     * @return $this
     */
    public function setSerialId($var)
    {
        GPBUtil::checkString($var, True);
        $this->serial_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint64 origin = 14;</code>
     * @return int|string
     */
    public function getOrigin()
    {
        return $this->origin;
    }

    /**
     * Generated from protobuf field <code>uint64 origin = 14;</code>
     * @param int|string $var
     * @return $this
     */
    public function setOrigin($var)
    {
        GPBUtil::checkUint64($var);
        $this->origin = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .PBCustomerCategoryItem category_ids = 15;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getCategoryIds()
    {
        return $this->category_ids;
    }

    /**
     * Generated from protobuf field <code>repeated .PBCustomerCategoryItem category_ids = 15;</code>
     * @param array<\protobuf\Customer\PBCustomerCategoryItem>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setCategoryIds($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \protobuf\Customer\PBCustomerCategoryItem::class);
        $this->category_ids = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string biz_type = 16;</code>
     * @return string
     */
    public function getBizType()
    {
        return $this->biz_type;
    }

    /**
     * Generated from protobuf field <code>string biz_type = 16;</code>
     * @param string $var
     * @return $this
     */
    public function setBizType($var)
    {
        GPBUtil::checkString($var, True);
        $this->biz_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string timezone = 17;</code>
     * @return string
     */
    public function getTimezone()
    {
        return $this->timezone;
    }

    /**
     * Generated from protobuf field <code>string timezone = 17;</code>
     * @param string $var
     * @return $this
     */
    public function setTimezone($var)
    {
        GPBUtil::checkString($var, True);
        $this->timezone = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string country = 18;</code>
     * @return string
     */
    public function getCountry()
    {
        return $this->country;
    }

    /**
     * Generated from protobuf field <code>string country = 18;</code>
     * @param string $var
     * @return $this
     */
    public function setCountry($var)
    {
        GPBUtil::checkString($var, True);
        $this->country = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string province = 19;</code>
     * @return string
     */
    public function getProvince()
    {
        return $this->province;
    }

    /**
     * Generated from protobuf field <code>string province = 19;</code>
     * @param string $var
     * @return $this
     */
    public function setProvince($var)
    {
        GPBUtil::checkString($var, True);
        $this->province = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string city = 20;</code>
     * @return string
     */
    public function getCity()
    {
        return $this->city;
    }

    /**
     * Generated from protobuf field <code>string city = 20;</code>
     * @param string $var
     * @return $this
     */
    public function setCity($var)
    {
        GPBUtil::checkString($var, True);
        $this->city = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.PBScale scale_id = 21;</code>
     * @return int
     */
    public function getScaleId()
    {
        return $this->scale_id;
    }

    /**
     * Generated from protobuf field <code>.PBScale scale_id = 21;</code>
     * @param int $var
     * @return $this
     */
    public function setScaleId($var)
    {
        GPBUtil::checkEnum($var, \protobuf\CRMCommon\PBScale::class);
        $this->scale_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string homepage = 22;</code>
     * @return string
     */
    public function getHomepage()
    {
        return $this->homepage;
    }

    /**
     * Generated from protobuf field <code>string homepage = 22;</code>
     * @param string $var
     * @return $this
     */
    public function setHomepage($var)
    {
        GPBUtil::checkString($var, True);
        $this->homepage = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string fax = 23;</code>
     * @return string
     */
    public function getFax()
    {
        return $this->fax;
    }

    /**
     * Generated from protobuf field <code>string fax = 23;</code>
     * @param string $var
     * @return $this
     */
    public function setFax($var)
    {
        GPBUtil::checkString($var, True);
        $this->fax = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string tel = 24;</code>
     * @return string
     */
    public function getTel()
    {
        return $this->tel;
    }

    /**
     * Generated from protobuf field <code>string tel = 24;</code>
     * @param string $var
     * @return $this
     */
    public function setTel($var)
    {
        GPBUtil::checkString($var, True);
        $this->tel = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string address = 25;</code>
     * @return string
     */
    public function getAddress()
    {
        return $this->address;
    }

    /**
     * Generated from protobuf field <code>string address = 25;</code>
     * @param string $var
     * @return $this
     */
    public function setAddress($var)
    {
        GPBUtil::checkString($var, True);
        $this->address = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string remark = 26;</code>
     * @return string
     */
    public function getRemark()
    {
        return $this->remark;
    }

    /**
     * Generated from protobuf field <code>string remark = 26;</code>
     * @param string $var
     * @return $this
     */
    public function setRemark($var)
    {
        GPBUtil::checkString($var, True);
        $this->remark = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string order_time = 27;</code>
     * @return string
     */
    public function getOrderTime()
    {
        return $this->order_time;
    }

    /**
     * Generated from protobuf field <code>string order_time = 27;</code>
     * @param string $var
     * @return $this
     */
    public function setOrderTime($var)
    {
        GPBUtil::checkString($var, True);
        $this->order_time = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string create_time = 28;</code>
     * @return string
     */
    public function getCreateTime()
    {
        return $this->create_time;
    }

    /**
     * Generated from protobuf field <code>string create_time = 28;</code>
     * @param string $var
     * @return $this
     */
    public function setCreateTime($var)
    {
        GPBUtil::checkString($var, True);
        $this->create_time = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string archive_time = 29;</code>
     * @return string
     */
    public function getArchiveTime()
    {
        return $this->archive_time;
    }

    /**
     * Generated from protobuf field <code>string archive_time = 29;</code>
     * @param string $var
     * @return $this
     */
    public function setArchiveTime($var)
    {
        GPBUtil::checkString($var, True);
        $this->archive_time = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 last_owner = 30;</code>
     * @return int
     */
    public function getLastOwner()
    {
        return $this->last_owner;
    }

    /**
     * Generated from protobuf field <code>uint32 last_owner = 30;</code>
     * @param int $var
     * @return $this
     */
    public function setLastOwner($var)
    {
        GPBUtil::checkUint32($var);
        $this->last_owner = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 archive_type = 31;</code>
     * @return int
     */
    public function getArchiveType()
    {
        return $this->archive_type;
    }

    /**
     * Generated from protobuf field <code>uint32 archive_type = 31;</code>
     * @param int $var
     * @return $this
     */
    public function setArchiveType($var)
    {
        GPBUtil::checkUint32($var);
        $this->archive_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string tel_area_code = 32;</code>
     * @return string
     */
    public function getTelAreaCode()
    {
        return $this->tel_area_code;
    }

    /**
     * Generated from protobuf field <code>string tel_area_code = 32;</code>
     * @param string $var
     * @return $this
     */
    public function setTelAreaCode($var)
    {
        GPBUtil::checkString($var, True);
        $this->tel_area_code = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .PBFileInfo image_list = 35;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getImageList()
    {
        return $this->image_list;
    }

    /**
     * Generated from protobuf field <code>repeated .PBFileInfo image_list = 35;</code>
     * @param array<\protobuf\CRMCommon\PBFileInfo>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setImageList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \protobuf\CRMCommon\PBFileInfo::class);
        $this->image_list = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>map<string, string> external_field_data = 36;</code>
     * @return \Google\Protobuf\Internal\MapField
     */
    public function getExternalFieldData()
    {
        return $this->external_field_data;
    }

    /**
     * Generated from protobuf field <code>map<string, string> external_field_data = 36;</code>
     * @param array|\Google\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setExternalFieldData($var)
    {
        $arr = GPBUtil::checkMapField($var, \Google\Protobuf\Internal\GPBType::STRING, \Google\Protobuf\Internal\GPBType::STRING);
        $this->external_field_data = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.PBCustomerDetailInfo customer = 37;</code>
     * @return \protobuf\Customer\PBCustomerDetailInfo|null
     */
    public function getCustomer()
    {
        return $this->customer;
    }

    public function hasCustomer()
    {
        return isset($this->customer);
    }

    public function clearCustomer()
    {
        unset($this->customer);
    }

    /**
     * Generated from protobuf field <code>.PBCustomerDetailInfo customer = 37;</code>
     * @param \protobuf\Customer\PBCustomerDetailInfo $var
     * @return $this
     */
    public function setCustomer($var)
    {
        GPBUtil::checkMessage($var, \protobuf\Customer\PBCustomerDetailInfo::class);
        $this->customer = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.PBTrailInfo last_trail_info = 38;</code>
     * @return \protobuf\Customer\PBTrailInfo|null
     */
    public function getLastTrailInfo()
    {
        return $this->last_trail_info;
    }

    public function hasLastTrailInfo()
    {
        return isset($this->last_trail_info);
    }

    public function clearLastTrailInfo()
    {
        unset($this->last_trail_info);
    }

    /**
     * Generated from protobuf field <code>.PBTrailInfo last_trail_info = 38;</code>
     * @param \protobuf\Customer\PBTrailInfo $var
     * @return $this
     */
    public function setLastTrailInfo($var)
    {
        GPBUtil::checkMessage($var, \protobuf\Customer\PBTrailInfo::class);
        $this->last_trail_info = $var;

        return $this;
    }

    /**
     *最近成交日期
     *
     * Generated from protobuf field <code>string deal_time = 39;</code>
     * @return string
     */
    public function getDealTime()
    {
        return $this->deal_time;
    }

    /**
     *最近成交日期
     *
     * Generated from protobuf field <code>string deal_time = 39;</code>
     * @param string $var
     * @return $this
     */
    public function setDealTime($var)
    {
        GPBUtil::checkString($var, True);
        $this->deal_time = $var;

        return $this;
    }

    /**
     *计入业绩订单数
     *
     * Generated from protobuf field <code>int32 performance_order_count = 40;</code>
     * @return int
     */
    public function getPerformanceOrderCount()
    {
        return $this->performance_order_count;
    }

    /**
     *计入业绩订单数
     *
     * Generated from protobuf field <code>int32 performance_order_count = 40;</code>
     * @param int $var
     * @return $this
     */
    public function setPerformanceOrderCount($var)
    {
        GPBUtil::checkInt32($var);
        $this->performance_order_count = $var;

        return $this;
    }

    /**
     *赢单商机数
     *
     * Generated from protobuf field <code>int32 success_opportunity_count = 41;</code>
     * @return int
     */
    public function getSuccessOpportunityCount()
    {
        return $this->success_opportunity_count;
    }

    /**
     *赢单商机数
     *
     * Generated from protobuf field <code>int32 success_opportunity_count = 41;</code>
     * @param int $var
     * @return $this
     */
    public function setSuccessOpportunityCount($var)
    {
        GPBUtil::checkInt32($var);
        $this->success_opportunity_count = $var;

        return $this;
    }

    /**
     *最近跟进动态
     *
     * Generated from protobuf field <code>.PBTrailInfo last_remark_trail = 42;</code>
     * @return \protobuf\Customer\PBTrailInfo|null
     */
    public function getLastRemarkTrail()
    {
        return $this->last_remark_trail;
    }

    public function hasLastRemarkTrail()
    {
        return isset($this->last_remark_trail);
    }

    public function clearLastRemarkTrail()
    {
        unset($this->last_remark_trail);
    }

    /**
     *最近跟进动态
     *
     * Generated from protobuf field <code>.PBTrailInfo last_remark_trail = 42;</code>
     * @param \protobuf\Customer\PBTrailInfo $var
     * @return $this
     */
    public function setLastRemarkTrail($var)
    {
        GPBUtil::checkMessage($var, \protobuf\Customer\PBTrailInfo::class);
        $this->last_remark_trail = $var;

        return $this;
    }

    /**
     *下次跟进时间
     *
     * Generated from protobuf field <code>string next_follow_up_time = 43;</code>
     * @return string
     */
    public function getNextFollowUpTime()
    {
        return $this->next_follow_up_time;
    }

    /**
     *下次跟进时间
     *
     * Generated from protobuf field <code>string next_follow_up_time = 43;</code>
     * @param string $var
     * @return $this
     */
    public function setNextFollowUpTime($var)
    {
        GPBUtil::checkString($var, True);
        $this->next_follow_up_time = $var;

        return $this;
    }

    /**
     *来源详情
     *
     * Generated from protobuf field <code>repeated .PBStoreInfo alibaba_store_info = 44;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getAlibabaStoreInfo()
    {
        return $this->alibaba_store_info;
    }

    /**
     *来源详情
     *
     * Generated from protobuf field <code>repeated .PBStoreInfo alibaba_store_info = 44;</code>
     * @param array<\protobuf\CRMCommon\PBStoreInfo>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setAlibabaStoreInfo($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \protobuf\CRMCommon\PBStoreInfo::class);
        $this->alibaba_store_info = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 annual_procurement = 45;</code>
     * @return int
     */
    public function getAnnualProcurement()
    {
        return $this->annual_procurement;
    }

    /**
     * Generated from protobuf field <code>int32 annual_procurement = 45;</code>
     * @param int $var
     * @return $this
     */
    public function setAnnualProcurement($var)
    {
        GPBUtil::checkInt32($var);
        $this->annual_procurement = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 intention_level = 46;</code>
     * @return int
     */
    public function getIntentionLevel()
    {
        return $this->intention_level;
    }

    /**
     * Generated from protobuf field <code>int32 intention_level = 46;</code>
     * @param int $var
     * @return $this
     */
    public function setIntentionLevel($var)
    {
        GPBUtil::checkInt32($var);
        $this->intention_level = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 release_count = 47;</code>
     * @return int
     */
    public function getReleaseCount()
    {
        return $this->release_count;
    }

    /**
     * Generated from protobuf field <code>uint32 release_count = 47;</code>
     * @param int $var
     * @return $this
     */
    public function setReleaseCount($var)
    {
        GPBUtil::checkUint32($var);
        $this->release_count = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string private_time = 48;</code>
     * @return string
     */
    public function getPrivateTime()
    {
        return $this->private_time;
    }

    /**
     * Generated from protobuf field <code>string private_time = 48;</code>
     * @param string $var
     * @return $this
     */
    public function setPrivateTime($var)
    {
        GPBUtil::checkString($var, True);
        $this->private_time = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string edit_time = 49;</code>
     * @return string
     */
    public function getEditTime()
    {
        return $this->edit_time;
    }

    /**
     * Generated from protobuf field <code>string edit_time = 49;</code>
     * @param string $var
     * @return $this
     */
    public function setEditTime($var)
    {
        GPBUtil::checkString($var, True);
        $this->edit_time = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 last_edit_user = 50;</code>
     * @return int
     */
    public function getLastEditUser()
    {
        return $this->last_edit_user;
    }

    /**
     * Generated from protobuf field <code>uint32 last_edit_user = 50;</code>
     * @param int $var
     * @return $this
     */
    public function setLastEditUser($var)
    {
        GPBUtil::checkUint32($var);
        $this->last_edit_user = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 create_user = 51;</code>
     * @return int
     */
    public function getCreateUser()
    {
        return $this->create_user;
    }

    /**
     * Generated from protobuf field <code>uint32 create_user = 51;</code>
     * @param int $var
     * @return $this
     */
    public function setCreateUser($var)
    {
        GPBUtil::checkUint32($var);
        $this->create_user = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string public_time = 52;</code>
     * @return string
     */
    public function getPublicTime()
    {
        return $this->public_time;
    }

    /**
     * Generated from protobuf field <code>string public_time = 52;</code>
     * @param string $var
     * @return $this
     */
    public function setPublicTime($var)
    {
        GPBUtil::checkString($var, True);
        $this->public_time = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string recent_follow_up_time = 53;</code>
     * @return string
     */
    public function getRecentFollowUpTime()
    {
        return $this->recent_follow_up_time;
    }

    /**
     * Generated from protobuf field <code>string recent_follow_up_time = 53;</code>
     * @param string $var
     * @return $this
     */
    public function setRecentFollowUpTime($var)
    {
        GPBUtil::checkString($var, True);
        $this->recent_follow_up_time = $var;

        return $this;
    }

}

