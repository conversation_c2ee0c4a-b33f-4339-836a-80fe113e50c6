<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: report.proto

namespace protobuf\Report;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>PBReportDetail</code>
 */
class PBReportDetail extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>uint64 report_id = 1;</code>
     */
    protected $report_id = 0;
    /**
     * Generated from protobuf field <code>string name = 2;</code>
     */
    protected $name = '';
    /**
     * Generated from protobuf field <code>string tips = 3;</code>
     */
    protected $tips = '';
    /**
     * Generated from protobuf field <code>.PBReportType report_date_type = 4;</code>
     */
    protected $report_date_type = 0;
    /**
     * Generated from protobuf field <code>string report_date = 5;</code>
     */
    protected $report_date = '';
    /**
     * Generated from protobuf field <code>uint32 create_time = 6;</code>
     */
    protected $create_time = 0;
    /**
     * Generated from protobuf field <code>string title = 7;</code>
     */
    protected $title = '';
    /**
     * Generated from protobuf field <code>string date_range = 8;</code>
     */
    protected $date_range = '';
    /**
     * Generated from protobuf field <code>repeated .PBSummatList summat_list = 9;</code>
     */
    private $summat_list;
    /**
     * Generated from protobuf field <code>bool enable_flag = 12;</code>
     */
    protected $enable_flag = false;
    /**
     * Generated from protobuf field <code>uint32 update_time = 13;</code>
     */
    protected $update_time = 0;
    /**
     * Generated from protobuf field <code>string start_date = 14;</code>
     */
    protected $start_date = '';
    /**
     * Generated from protobuf field <code>string end_date = 15;</code>
     */
    protected $end_date = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $report_id
     *     @type string $name
     *     @type string $tips
     *     @type int $report_date_type
     *     @type string $report_date
     *     @type int $create_time
     *     @type string $title
     *     @type string $date_range
     *     @type array<\protobuf\Report\PBSummatList>|\Google\Protobuf\Internal\RepeatedField $summat_list
     *     @type bool $enable_flag
     *     @type int $update_time
     *     @type string $start_date
     *     @type string $end_date
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\Report::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>uint64 report_id = 1;</code>
     * @return int|string
     */
    public function getReportId()
    {
        return $this->report_id;
    }

    /**
     * Generated from protobuf field <code>uint64 report_id = 1;</code>
     * @param int|string $var
     * @return $this
     */
    public function setReportId($var)
    {
        GPBUtil::checkUint64($var);
        $this->report_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string name = 2;</code>
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Generated from protobuf field <code>string name = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setName($var)
    {
        GPBUtil::checkString($var, True);
        $this->name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string tips = 3;</code>
     * @return string
     */
    public function getTips()
    {
        return $this->tips;
    }

    /**
     * Generated from protobuf field <code>string tips = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setTips($var)
    {
        GPBUtil::checkString($var, True);
        $this->tips = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.PBReportType report_date_type = 4;</code>
     * @return int
     */
    public function getReportDateType()
    {
        return $this->report_date_type;
    }

    /**
     * Generated from protobuf field <code>.PBReportType report_date_type = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setReportDateType($var)
    {
        GPBUtil::checkEnum($var, \protobuf\Report\PBReportType::class);
        $this->report_date_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string report_date = 5;</code>
     * @return string
     */
    public function getReportDate()
    {
        return $this->report_date;
    }

    /**
     * Generated from protobuf field <code>string report_date = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setReportDate($var)
    {
        GPBUtil::checkString($var, True);
        $this->report_date = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 create_time = 6;</code>
     * @return int
     */
    public function getCreateTime()
    {
        return $this->create_time;
    }

    /**
     * Generated from protobuf field <code>uint32 create_time = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setCreateTime($var)
    {
        GPBUtil::checkUint32($var);
        $this->create_time = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string title = 7;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Generated from protobuf field <code>string title = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string date_range = 8;</code>
     * @return string
     */
    public function getDateRange()
    {
        return $this->date_range;
    }

    /**
     * Generated from protobuf field <code>string date_range = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setDateRange($var)
    {
        GPBUtil::checkString($var, True);
        $this->date_range = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .PBSummatList summat_list = 9;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getSummatList()
    {
        return $this->summat_list;
    }

    /**
     * Generated from protobuf field <code>repeated .PBSummatList summat_list = 9;</code>
     * @param array<\protobuf\Report\PBSummatList>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setSummatList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \protobuf\Report\PBSummatList::class);
        $this->summat_list = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool enable_flag = 12;</code>
     * @return bool
     */
    public function getEnableFlag()
    {
        return $this->enable_flag;
    }

    /**
     * Generated from protobuf field <code>bool enable_flag = 12;</code>
     * @param bool $var
     * @return $this
     */
    public function setEnableFlag($var)
    {
        GPBUtil::checkBool($var);
        $this->enable_flag = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 update_time = 13;</code>
     * @return int
     */
    public function getUpdateTime()
    {
        return $this->update_time;
    }

    /**
     * Generated from protobuf field <code>uint32 update_time = 13;</code>
     * @param int $var
     * @return $this
     */
    public function setUpdateTime($var)
    {
        GPBUtil::checkUint32($var);
        $this->update_time = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string start_date = 14;</code>
     * @return string
     */
    public function getStartDate()
    {
        return $this->start_date;
    }

    /**
     * Generated from protobuf field <code>string start_date = 14;</code>
     * @param string $var
     * @return $this
     */
    public function setStartDate($var)
    {
        GPBUtil::checkString($var, True);
        $this->start_date = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string end_date = 15;</code>
     * @return string
     */
    public function getEndDate()
    {
        return $this->end_date;
    }

    /**
     * Generated from protobuf field <code>string end_date = 15;</code>
     * @param string $var
     * @return $this
     */
    public function setEndDate($var)
    {
        GPBUtil::checkString($var, True);
        $this->end_date = $var;

        return $this;
    }

}

