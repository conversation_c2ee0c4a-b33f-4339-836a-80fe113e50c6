<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: DingDing.proto

namespace protobuf\DingDing;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 *响应 /api/stormsFury/dingDingRead/loginUrl
 *获取免登URL响应
 *可能会返回status_code = 233 error_msg=未绑定账号
 *可能会返回status_code = -1 error_msg=调用钉钉接口获取信息异常或者其他一些异常
 *
 * Generated from protobuf message <code>PBDingDingLoginUrlRsp</code>
 */
class PBDingDingLoginUrlRsp extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string token = 1;</code>
     */
    protected $token = '';
    /**
     * Generated from protobuf field <code>int32 expire = 2;</code>
     */
    protected $expire = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $token
     *     @type int $expire
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\DingDing::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string token = 1;</code>
     * @return string
     */
    public function getToken()
    {
        return $this->token;
    }

    /**
     * Generated from protobuf field <code>string token = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->token = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 expire = 2;</code>
     * @return int
     */
    public function getExpire()
    {
        return $this->expire;
    }

    /**
     * Generated from protobuf field <code>int32 expire = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setExpire($var)
    {
        GPBUtil::checkInt32($var);
        $this->expire = $var;

        return $this;
    }

}

