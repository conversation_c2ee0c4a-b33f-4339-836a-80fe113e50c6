<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Product.proto

namespace protobuf\Product;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>PBProductBaseInfo</code>
 */
class PBProductBaseInfo extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int64 product_id = 1;</code>
     */
    protected $product_id = 0;
    /**
     * Generated from protobuf field <code>string title = 2;</code>
     */
    protected $title = '';
    /**
     * Generated from protobuf field <code>repeated .PBProductAttribute attributes = 3;</code>
     */
    private $attributes;
    /**
     * Generated from protobuf field <code>.PBProductInventory inventory = 4;</code>
     */
    protected $inventory = null;
    /**
     * Generated from protobuf field <code>.PBProductFob fob = 5;</code>
     */
    protected $fob = null;
    /**
     * Generated from protobuf field <code>repeated .PBProductTag tags = 6;</code>
     */
    private $tags;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $product_id
     *     @type string $title
     *     @type array<\protobuf\Product\PBProductAttribute>|\Google\Protobuf\Internal\RepeatedField $attributes
     *     @type \protobuf\Product\PBProductInventory $inventory
     *     @type \protobuf\Product\PBProductFob $fob
     *     @type array<\protobuf\Product\PBProductTag>|\Google\Protobuf\Internal\RepeatedField $tags
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\Product::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int64 product_id = 1;</code>
     * @return int|string
     */
    public function getProductId()
    {
        return $this->product_id;
    }

    /**
     * Generated from protobuf field <code>int64 product_id = 1;</code>
     * @param int|string $var
     * @return $this
     */
    public function setProductId($var)
    {
        GPBUtil::checkInt64($var);
        $this->product_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string title = 2;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Generated from protobuf field <code>string title = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .PBProductAttribute attributes = 3;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getAttributes()
    {
        return $this->attributes;
    }

    /**
     * Generated from protobuf field <code>repeated .PBProductAttribute attributes = 3;</code>
     * @param array<\protobuf\Product\PBProductAttribute>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setAttributes($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \protobuf\Product\PBProductAttribute::class);
        $this->attributes = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.PBProductInventory inventory = 4;</code>
     * @return \protobuf\Product\PBProductInventory|null
     */
    public function getInventory()
    {
        return $this->inventory;
    }

    public function hasInventory()
    {
        return isset($this->inventory);
    }

    public function clearInventory()
    {
        unset($this->inventory);
    }

    /**
     * Generated from protobuf field <code>.PBProductInventory inventory = 4;</code>
     * @param \protobuf\Product\PBProductInventory $var
     * @return $this
     */
    public function setInventory($var)
    {
        GPBUtil::checkMessage($var, \protobuf\Product\PBProductInventory::class);
        $this->inventory = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.PBProductFob fob = 5;</code>
     * @return \protobuf\Product\PBProductFob|null
     */
    public function getFob()
    {
        return $this->fob;
    }

    public function hasFob()
    {
        return isset($this->fob);
    }

    public function clearFob()
    {
        unset($this->fob);
    }

    /**
     * Generated from protobuf field <code>.PBProductFob fob = 5;</code>
     * @param \protobuf\Product\PBProductFob $var
     * @return $this
     */
    public function setFob($var)
    {
        GPBUtil::checkMessage($var, \protobuf\Product\PBProductFob::class);
        $this->fob = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .PBProductTag tags = 6;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getTags()
    {
        return $this->tags;
    }

    /**
     * Generated from protobuf field <code>repeated .PBProductTag tags = 6;</code>
     * @param array<\protobuf\Product\PBProductTag>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setTags($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \protobuf\Product\PBProductTag::class);
        $this->tags = $arr;

        return $this;
    }

}

