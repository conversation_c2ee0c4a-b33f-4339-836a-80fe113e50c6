<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: MailSetting.proto

namespace protobuf\MailSetting;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * 新增编辑邮件规则 /mailSettingWrite/mailRule
 *
 * Generated from protobuf message <code>PBEditMailRuleReq</code>
 */
class PBEditMailRuleReq extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>.PBMailRule rule = 1;</code>
     */
    protected $rule = null;
    /**
     * Generated from protobuf field <code>repeated uint64 merge_rule_ids = 2;</code>
     */
    private $merge_rule_ids;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \protobuf\MailSetting\PBMailRule $rule
     *     @type array<int>|array<string>|\Google\Protobuf\Internal\RepeatedField $merge_rule_ids
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\MailSetting::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>.PBMailRule rule = 1;</code>
     * @return \protobuf\MailSetting\PBMailRule|null
     */
    public function getRule()
    {
        return $this->rule;
    }

    public function hasRule()
    {
        return isset($this->rule);
    }

    public function clearRule()
    {
        unset($this->rule);
    }

    /**
     * Generated from protobuf field <code>.PBMailRule rule = 1;</code>
     * @param \protobuf\MailSetting\PBMailRule $var
     * @return $this
     */
    public function setRule($var)
    {
        GPBUtil::checkMessage($var, \protobuf\MailSetting\PBMailRule::class);
        $this->rule = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated uint64 merge_rule_ids = 2;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getMergeRuleIds()
    {
        return $this->merge_rule_ids;
    }

    /**
     * Generated from protobuf field <code>repeated uint64 merge_rule_ids = 2;</code>
     * @param array<int>|array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setMergeRuleIds($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::UINT64);
        $this->merge_rule_ids = $arr;

        return $this;
    }

}

