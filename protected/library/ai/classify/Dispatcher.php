<?php
/**
 * Created by PhpStorm.
 * User: andy
 * Date: 2018/12/20
 * Time: 9:06
 */

namespace common\library\ai\classify;

use common\library\account\Client;
use common\library\ai\classify\customer\CustomerApply;
use common\library\ai\classify\lead\LeadApply;
use common\library\ai\classify\mail\Classify;
use common\library\ai\classify\mail\MailApply;
use common\library\ai\classify\marketing\MarketingInquiryApply;
use common\library\ai\classify\opportunity\OpportunityApply;
use common\library\ai\classify\product\ProductApplyV2;
use common\library\ai\classify\setting\Settings;
use common\library\ai\classify\task\PiMailHistoryTask;
use common\library\email\Util;
use common\library\google_ads\track\Params;
use common\library\mail\service\CallbackService;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\setting\library\tag\Tag;

class Dispatcher
{

    public static function scriptProcess($clientId, $userId, $userMailId, array $mailIds)
    {
        \User::setLoginUserById($userId);
        $db = \ProjectActiveRecord::getDbByClientId($clientId);

        if (\UserMail::isPublicUserMail($userMailId)) {
            \LogUtil::info('user_mail_id:'.$userMailId .' 公共邮箱不执行自动化');
            return;
        }

        if (empty($mailIds))
            return;

        if (count($mailIds) == 1)
        {
            $data = [['mail_id' => reset($mailIds)]];
        }
        else
        {
            $data = $db->createCommand("select mail_id from tbl_mail where mail_id in (" . implode(',', $mailIds) . ") order by receive_time asc")->queryAll();
        }

        $mailApply = new MailApply($clientId, $userId);
        $mailApply->setProcessMailTodoFlag(true);
        $settings = $mailApply->getSetting();

        if (!$settings->isEnableApply()) {
            return;
        }

        $classify = new Classify($clientId, $userId);

        $privilegeService = PrivilegeService::getInstance($clientId, $userId);
        if ($privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_LEAD) || $privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_AI_TAG_MARK))
        {
            $missingFunction = $privilegeService->getMissingFunctional([
                PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_EMAIL,
                PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_CUSTOMER,
            ]);

            if (!empty($missingFunction))//只有classify LEAD 权限
            {
//                $classify->setSkipRule('validate_mail');
//                $classify->setSkipRule('classify_mail_type');
//                $classify->setSkipRule('mail_tag');
                $classify->setSkipRule('customer_prob');
                $classify->setSkipRule('customer_archive');
                $classify->setSkipRule('customer_check');
                $classify->setSkipRule('invoice_info');
            }
        }

        $leadApply = new LeadApply($clientId, $userId);
        $customerApply = new CustomerApply($clientId, $userId);
        $productApply = new ProductApplyV2($clientId, $userId);
        $opportunityApply = new OpportunityApply($clientId, $userId);

        foreach ($data as $elem)
        {
            $mailId = $elem['mail_id'];

            try
            {
                $applyRule = $classify->process($userMailId, $mailId);

                \LogUtil::info($mailId . ' process: ' . json_encode($applyRule));

                $customerData = null;

                if ($classify->getLastMail()->isExpose() || $classify->getLastMail()->isSecondExpose())
                {
                    $customerData = $customerApply->apply($classify, $applyRule);
                }
                else
                {
                    if ($settings->isBindForMarketing($userMailId)) {
                        $marketingInquiryApply = new MarketingInquiryApply($clientId, $userId);
                        $marketingInquiryApply->apply($classify, $applyRule);
                    }

                    if ($settings->isEnableMailApply())
                    {
                        $mailApply->apply($classify, $applyRule);
                    } else                     {
                        \LogUtil::info('mail_id:'.$mailId .' 未启用邮件自动化');
                    }

                    $mail = $classify->getLastMail();

                    if ($settings->isEnableProductApply()) {
                        $productApply->apply($classify, $applyRule);
                    } else {
                        \LogUtil::info('mail_id:'.$mailId .' 未启用产品自动化'. json_encode($settings->getApplySettings()->getAttributes()));
                    }

                    if ($settings->isEnableLeadApply()) {
                        $leadApply->apply($classify, $applyRule);
                    } else {
                        \LogUtil::info('mail_id:'.$mailId .' 未启用线索自动化'. json_encode($settings->getApplySettings()->getAttributes()));
                    }

                    $customerData = $customerApply->apply($classify, $applyRule);

                    if ($settings->isEnableOpportunityApply()) {
                        $opportunityApply->apply($classify, $applyRule);
                    } else {
                        \LogUtil::info('mail_id:'.$mailId .' 未启用商机自动化'. json_encode($settings->getApplySettings()->getAttributes()));
                    }

                    $callbackService = new CallbackService($mail);
                    $callbackService->processCustomer(!$settings->isEnableOpportunityApply(), true);
                }

                $classify->applyClassifyLog($customerData);
                $classify->getCaptureCardLog()->save();
            }
            catch (\InnerApiException $e)
            {
                \LogUtil::error('InnerApiException mail_id:'.$mailId . $e->getMessage());

                if (!$classify->getLastMail()->isExpose() && !$classify->getLastMail()->isSecondExpose()) {
                    // 外部接口调用异常，邮件退回CallbackService处理
                    $callbackService = new CallbackService($classify->getLastMail());
                    $callbackService->processCustomer(true, true);
                }
            }
            catch (\RuntimeException $e)
            {
                \LogUtil::error('RuntimeException mail_id:'. $mailId .$e->getMessage());
                throw $e;
            }
            catch (\Exception $e)
            {
                \LogUtil::error('Exception mail_id:'. $mailId .$e->getMessage());
                \common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null,$e),$e->getTrace());
                throw $e;
            }
        }
    }

    public static function customerSimulation($clientId, $simulateNew)
    {
        /**
         * @var \CDbConnection $accountDb
         */
        $accountDb = \Yii::app()->account_base_db;

        $adminDb = \Yii::app()->db;

        $db = \ProjectActiveRecord::getDbByClientId($clientId);

        $userMailList = $adminDb->createCommand("select client_id, user_id, user_mail_id,email_address from tbl_user_mail where client_id=:client_id and enable_flag=1 and user_id!=0 order by user_id limit 2")
            ->queryAll(true, [':client_id' => $clientId]);

        /*/
        $userMailList = [
            ['client_id' => 1, 'user_id' => ********, 'user_mail_id' => ********]
        ];
        //*/

        $url = 'https://crm.xiaoman.cn/pro/mail/detail?mail_id=';

        $simulation = true;

        foreach ($userMailList as $elem)
        {
            $clientId = $elem['client_id'];
            $userId = $elem['user_id'];
            $userMailId = $elem['user_mail_id'];
            $email = $elem['email_address'];

            $user = \User::getUserObject($userId);
            \User::setLoginUser($user);
            $account = $user->getEmail();

            if ($simulateNew)
            {
                $filename = "/tmp/simulation_new_{$clientId}_user_id_{$userId}_$userMailId.csv";
            }
            else
            {
                $filename = "/tmp/simulation_{$clientId}_user_id_{$userId}_$userMailId.csv";
            }

            $fp = fopen($filename, 'w');

            echo "processing... $filename\n";

            $header = [
                '邮件类型',
                '主题',
                '邮件链接',
                '客户概率',
                '算法版本',
                '建档操作',
                '过滤操作',
                '邮件标签',

                'company_id',
                '客户名称',
                '电话',
                '传真',
                '主页',
                '国家',
                '省份',
                '地区',
                '地址',
                '企业规模',
                '备注',
                'company_hash_id',

                '联系人信息'
            ];

            fputcsv($fp, $header);

            \User::setLoginUserById($userId);

            $mailInfoList = $db->createCommand("select mail_id, mail_type, subject from tbl_mail where user_mail_id=:user_mail_id and folder_id not in (0,5,6,9,10,11) order by receive_time asc")
                ->queryAll(true, [':user_mail_id' => $userMailId]);

            /*$mailInfoList = [
                ['mail_id' => 290773, 'mail_type' => 2, 'subject' => ''],
                ['mail_id' => 290775, 'mail_type' => 2, 'subject' => ''],
            ];*/

            $classify = new Classify($clientId, $userId);
            $classify->setSimulation($simulation);
            $classify->setSimulateNewCustomer($simulateNew);
            $classify->setSkipRule('invoice_info');
            $classify->setSkipRule('customer_check');
            $customerApply = new CustomerApply($clientId, $userId);
            $customerApply->setSimulation($simulation);

            echo 'total: ' . count($mailInfoList) . "\n";
            $i = 0;

            foreach ($mailInfoList as $item)
            {
                ++$i;

                if ($i % 1000 == 0)
                {
                    echo "processing $i \n";
                }

                $mailId = $item['mail_id'];
                $mailType = $item['mail_type'];
                $subject = $item['subject'];

                try
                {
                    $classifyResult = $classify->process($userMailId, $mailId);
                    $customerProb = $classify->getLastCustomerProb();
                    $customerProbVersion = $classify->getLastCustomerProbVersion();

                    $applyResult = $customerApply->apply($classify, $classifyResult);
                    $applyResult = $customerApply->prettyResult($applyResult);
                }
                catch (\Exception $e)
                {
                    echo $e->getMessage() . "\n";
                    \LogUtil::error($e->getMessage());
                    $customerProb = 'java接口出错:' . $e->getMessage();
                }

                $filterSummary = $classify->getLastFilterSummary();

                $mailTag = '';
                if (isset($classifyResult['inquiry']))
                    $mailTag = \common\library\setting\library\tag\Tag::MAIL_TAG_SYSTEM_LIST[\common\library\setting\library\tag\Tag::MAIL_TAG_INQUIRY];
                else if (isset($classifyResult['mail_tag']))
                    $mailTag = \common\library\setting\library\tag\Tag::MAIL_TAG_SYSTEM_LIST[$classifyResult['mail_tag']];

                $archiveSummary = '';
                if (isset($classifyResult['customer_archive']))
                {
                    $archiveCount = count($classifyResult['customer_archive'][0]);
                    $privateCount = count($classifyResult['customer_archive'][1]);

                    $archiveSummary = "新建{$archiveCount}个 更新私海{$privateCount}个";

                }
                else if (isset($classifyResult['inquiry_customer_archive']))
                {
                    $str = $classifyResult['inquiry_customer_archive']['operator'] == 'archive' ? '新建' : '更新';

                    $archiveSummary = "询盘规则： $str " . $classifyResult['inquiry_customer_archive']['data'][0];
                }

                $data = [
                    $mailType == \Mail::MAIL_TYPE_RECEIVE ? '收件' : '发件',
                    $subject,
                    $url . $mailId,
                    $customerProb,
                    $customerProbVersion,
                    $archiveSummary,
                    $filterSummary,
                    $mailTag,
                ];

                if (!empty($applyResult))
                {
                    $data[] = $applyResult[0]['company']['company_id'] ?? '';
                    $data[] = $applyResult[0]['company']['name'] ?? '';
                    $data[] = $applyResult[0]['company']['tel'] ?? '';
                    $data[] = $applyResult[0]['company']['fax'] ?? '';
                    $data[] = $applyResult[0]['company']['homepage'] ?? '';
                    $data[] = $applyResult[0]['company']['country'] ?? '';
                    $data[] = $applyResult[0]['company']['province'] ?? '';
                    $data[] = $applyResult[0]['company']['city'] ?? '';
                    $data[] = $applyResult[0]['company']['address'] ?? '';
                    $data[] = $applyResult[0]['company']['scale_id'] ?? '';
                    $data[] = $applyResult[0]['company']['remark'] ?? '';
                    $data[] = $applyResult[0]['company']['company_hash_id'] ?? '';

                    if (empty($applyResult[0]['customers']))
                    {
                        $customerInfo = '无更新';
                    }
                    else
                    {
                        $customerInfo = '';

                        foreach ($applyResult[0]['customers'] as $customer)
                        {
                            $customerInfo .= ('email: ' . $customer['email'] . ' (customer_id: ' . $elem['customer_id']);

                            if (!empty($customer['tel_list']))
                                $customerInfo .= (' 电话: ' . implode(',', $customer['tel_list']));

                            if (!empty($customer['contact']))
                            {
                                foreach ($customer['contact'] as $contactInfo)
                                    $customerInfo .= ( ' ' . $contactInfo['type'] . ': ' .  $contactInfo['value']);
                            }


                            $customerInfo .= ') ';
                        }
                    }


                    $data[] = $customerInfo;
                }

                fputcsv($fp, $data);
            }

            fclose($fp);
        }
    }

    public static function clientScriptProcess($clientId)
    {

        $adminDb = \Yii::app()->db;

        $db = \ProjectActiveRecord::getDbByClientId($clientId);

        $userMailList = $adminDb->createCommand("select client_id, user_id, user_mail_id,email_address from tbl_user_mail where client_id=:client_id  and enable_flag=1 and user_id!=0 order by user_id")
            ->queryAll(true, [':client_id' => $clientId]);


//        $userMailList = [
//            ['client_id' => 1, 'user_id' => 765, 'user_mail_id' => 765]
//        ];


        foreach ($userMailList as $elem) {
            $clientId = $elem['client_id'];
            $userId = $elem['user_id'];
            $userMailId = $elem['user_mail_id'];

            $user = \User::getUserObject($userId);
            \User::setLoginUser($user);

            $mailInfoList = $db->createCommand("select mail_id, mail_type, subject from tbl_mail where user_mail_id=:user_mail_id and folder_id not in (0,5,6,9,10,11) order by receive_time asc")
                ->queryAll(true, [':user_mail_id' => $userMailId]);

            $classify = new Classify($clientId, $userId);
            $mailApply = new MailApply($clientId, $userId);
            $settings = $mailApply->getSetting();
            if (!$settings->isEnableApply())
                return;

            $privilegeService = PrivilegeService::getInstance($clientId, $userId);
            if ($privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_LEAD))
            {
                $missingFunction = $privilegeService->getMissingFunctional([
                    PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_EMAIL,
                    PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_CUSTOMER,
                ]);

                if (!empty($missingFunction))//只有classify LEAD 权限
                {
                    $classify->setSkipRule('validate_mail');
                    $classify->setSkipRule('classify_mail_type');
                    $classify->setSkipRule('mail_tag');
                    $classify->setSkipRule('customer_prob');
                    $classify->setSkipRule('customer_archive');
                    $classify->setSkipRule('customer_check');
                    $classify->setSkipRule('invoice_info');
                }
            }

            $leadApply = new LeadApply($clientId, $userId);
            $customerApply = new CustomerApply($clientId, $userId);
            $productApply = new ProductApplyV2($clientId, $userId);
            $opportunityApply = new OpportunityApply($clientId, $userId);

            $total = count($mailInfoList);
            echo 'user_mail_id ' . $userMailId . ' total: ' . count($mailInfoList) . "\n";
            $i = 0;

            foreach ($mailInfoList as $item) {
                ++$i;

                if ($i % 1000 == 0) {
                    echo "user_mail_id:{$userMailId}   processing $i / $total  \n";
                }

                $mailId = $item['mail_id'];

                try {
                    $applyRule = $classify->process($userMailId, $mailId);

                    \LogUtil::info($mailId . ' process: ' . json_encode($applyRule));

                    $customerData = null;

                    if ($classify->getLastMail()->isExpose())
                    {
                        $customerData = $customerApply->apply($classify, $applyRule);
                    }
                    else
                    {
                        if ($settings->isBindForMarketing($userMailId)) {
                            $marketingInquiryApply = new MarketingInquiryApply($clientId, $userId);
                            $marketingInquiryApply->apply($classify, $applyRule);
                        }

                        if ($settings->isEnableMailApply())
                        {
                            $mailApply->apply($classify, $applyRule);
                        } else                     {
                            \LogUtil::info('mail_id:'.$mailId .' 未启用邮件自动化');
                        }

                        if ($settings->isEnableProductApply()) {
                            $productApply->apply($classify, $applyRule);
                        } else {
                            \LogUtil::info('mail_id:'.$mailId .' 未启用产品自动化'. json_encode($settings->getApplySettings()->getAttributes()));
                        }

                        if ($settings->isEnableLeadApply()) {
                            $leadApply->apply($classify, $applyRule);
                        } else {
                            \LogUtil::info('mail_id:'.$mailId .' 未启用线索自动化'. json_encode($settings->getApplySettings()->getAttributes()));
                        }

                        $customerData = $customerApply->apply($classify, $applyRule);

                        if ($settings->isEnableOpportunityApply()) {
                            $opportunityApply->apply($classify, $applyRule);
                        } else {
                            \LogUtil::info('mail_id:'.$mailId .' 未启用商机自动化'. json_encode($settings->getApplySettings()->getAttributes()));
                        }
                    }

                    $classify->applyClassifyLog($customerData);
                    $classify->getCaptureCardLog()->save();

                } catch (\Exception $e) {
                    echo $e->getMessage() . "\n" . $e->getTraceAsString() . "\n";
                }
            }
        }
    }


    public static function piMailHistoryScriptProcess($clientId)
    {

        \LogUtil::info("client: {$clientId} 处理历史pi邮件");
        /** @var \CDbConnection $adminDb */
        $adminDb = \Yii::app()->db;
        $adminUser = Client::getClient($clientId)->getMasterUser();
        \User::setLoginUser($adminUser);

        $setting = new Settings($clientId);
        if( !$setting->isEnableApply() || !PrivilegeService::getInstance($clientId)->hasFunctional(PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_EMAIL))
        {
            \LogUtil::info("client: {$clientId} 未开通自动化功能,跳过处理");
            return;
        }

        $userIds = $adminDb->createCommand("select  distinct user_id  from tbl_user_mail where client_id=:client_id and enable_flag=1 and user_id!=0 order by user_id")
            ->queryColumn([':client_id' => $clientId]);

        foreach ( $userIds as $userId )
        {
            $task = new PiMailHistoryTask($userId);
            $task->run();
        }

        $setting->getApplySettings()->history_flag = 1;
        $setting->getApplySettings()->history_time = date('Y-m-d H:i:s');
        $setting->getApplySettings()->save();

        \LogUtil::info("client: {$clientId} 已处理完毕, user count:".count($userIds));

    }

    /**
     * 重新执行邮件分类-建档建议处理逻辑
     * @param $clientId
     * @param $userId
     * @param $userMailId
     * @param array $mailIds
     * @return bool
     * @throws \CDbException
     * @throws \CException
     * @throws \ProcessException
     */
    public static function AdviceScriptProcess($clientId, $userId, $userMailId, array $mailIds)
    {
        \User::setLoginUserById($userId);
        $db = \ProjectActiveRecord::getDbByClientId($clientId);

        if (empty($mailIds))
            return false;

        if (count($mailIds) == 1)
        {
            $data = [['mail_id' => reset($mailIds)]];
        }
        else
        {
            $data = $db->createCommand("select mail_id from tbl_mail where mail_id in (" . implode(',', $mailIds) . ") order by receive_time desc")->queryAll();
        }

        $classify = new Classify($clientId, $userId);

        $mailApply = new MailApply($clientId, $userId);
        $mailApply->setProcessMailTodoFlag(true);
        $settings = $mailApply->getSetting();
        if (!$settings->isEnableApply())
            return false;

        $privilegeService = PrivilegeService::getInstance($clientId, $userId);
        if ($privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_LEAD))
        {
            $missingFunction = $privilegeService->getMissingFunctional([
                PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_EMAIL,
                PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_CUSTOMER,
            ]);

            if (!empty($missingFunction))//只有classify LEAD 权限
            {
                return false;
            }

            $classify->setSkipRule('mail_attach_classify_type');
        }

        $customerApply = new CustomerApply($clientId, $userId);
        $customerApply->setApplyModel(CustomerApply::APPLY_MODEL_TASK);
        foreach ($data as $elem)
        {
            $mailId = $elem['mail_id'];

            try
            {
                $applyRule = $classify->process($userMailId, $mailId);

                \LogUtil::info($mailId . ' process: ' . json_encode($applyRule));

                $customerData = null;

                if ($classify->getLastMail()->isExpose())
                {
                    $customerData = $customerApply->apply($classify, $applyRule);
                }
                else
                {
                    if ($settings->isEnableMailApply())
                    {
                        $mailApply->apply($classify, $applyRule);
                    } else                     {
                        \LogUtil::info('mail_id:'.$mailId .' 未启用邮件自动化');
                    }

                    $customerData = $customerApply->apply($classify, $applyRule);
                }

                $classify->applyClassifyLog($customerData);
                $classify->getCaptureCardLog()->save();

                if (!empty($customerData)) {
                    foreach ($customerData['archive'] as $value) {
                        if (!empty($value['data']['customer'])) {
                            return true;
                        }
                    }
                }

            }
            catch (\RuntimeException $e)
            {
                \LogUtil::error('RuntimeException mail_id:'. $mailId .$e->getMessage());
            }
            catch (\Exception $e)
            {
                \LogUtil::error('Exception mail_id:'. $mailId .$e->getMessage());
                \common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null,$e),$e->getTrace());
            }
        }

        return false;
    }
}
