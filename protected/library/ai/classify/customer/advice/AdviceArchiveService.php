<?php
/**
 * Created by PhpStorm.
 * User: beenzhang
 * Date: 2019/5/17
 * Time: 2:39 PM
 */

namespace common\library\ai\classify\customer\advice;

use common\components\BaseObject;
use common\library\custom_field\company_field\CustomerField;
use common\library\customer_v3\company\orm\Company;
use common\library\customer_v3\customer\orm\Customer;
use common\library\customer\Helper;
use common\library\lead\Lead;
use common\library\lead\LeadCustomerList;
use common\library\lead\LeadList;
use common\library\notification\Constant;
use common\library\notification\Notification;
use common\library\notification\PushHelper;

class AdviceArchiveService
{
    const TASK_LOCK_PREFIX_KEY = 'ai:customer:advice:archive:lock';

    protected $clientId;
    protected $opUser;
    protected $task;
    protected $failLogList = [];

    protected $companyMap = [];
    protected $companyTelMap = [];
    protected $customerTelMap = [];
    protected $companyIds = [];

    private $customerFieldData;//不要直接使用，用get获取

    protected $fieldEditType = BaseObject::FIELD_EDIT_TYPE_BY_AI;

    public function __construct($userId, $taskId)
    {
        $this->opUser = \User::getUserObject($userId);
        $this->clientId = $this->opUser->getClientId();

        $this->task = new CustomerAdviceArchiveTask($taskId);
        $this->task->begin();
    }

    public function setFieldEditType($fieldEditType)
    {
        $this->fieldEditType = $fieldEditType;
    }

    public function getCompanyIds() : array
    {
        return $this->companyIds;
    }

    public function process()
    {
        $adviceIds = $this->task->getAdviceIds();
        $poolDuplicateSwitch = \common\library\customer\pool\Helper::getCheckPoolDuplicateSwitch($this->opUser->getClientId(), \Constants::TYPE_COMPANY);

        if (empty($adviceIds)) {
            $this->task->fail('process fail! params error, empty advice list');
            throw new \ProcessException('process fail! params error, empty advice list');
        }

        $adviceList = new CustomerAdviceList($this->opUser->getUserId());
        $adviceList->setFields('advice_id,email,company_id,company_name,last_mail_id,user_id');
        $adviceList->setAdviceId($adviceIds);
        $adviceList->setOrderBy('order_time');
        $adviceList->setOrder('asc');
        $list = $adviceList->find();

        if (empty($list)) {
            $this->task->fail('process fail! empty advice list');
            \LogUtil::info('process fail! empty advice list');
            return false;
        }

        $deleteAdviceIds = $adviceIds;
        $lastMailIdList = [];
        foreach ($list as $item) {
            $lastMailIdList[] = $item['last_mail_id'];

            if (!in_array($item['advice_id'], $adviceIds))
                $this->failLog($item['email'], 'advice not exists');
        }

        $extractDataMap = $this->getAdviceDataList($lastMailIdList);

        $nameVsCompany = [];
        $mailIdVsCompanyId = [];
        foreach ($list as $item) {
            $extractData = $extractDataMap[$item['last_mail_id']]??[];
            if (empty($extractData)) {
                $this->failLog($item['email'], '');
                continue;
            }

            $companyId = $item['company_id'];
            if (!$companyId) {
                //前一封mailId建档成功的companyId
                $companyId = $mailIdVsCompanyId[$item['last_mail_id']] ?? $companyId;

                //根据名字匹配companyId
                if (!$companyId && !empty($item['company_name'])) {
                    if ($poolDuplicateSwitch) {
                        $checkCompany = $nameVsCompany[$item['company_name']] ?? (new Company($this->clientId))->loadByNameToPool($item['company_name'], null, $this->opUser->getUserId());
                    } else {
                        $checkCompany = $nameVsCompany[$item['company_name']] ?? (new Company($this->clientId))->loadByName($item['company_name']);
                    }

                    //关联公司名已建档
                    if ($checkCompany->isExist()) {
                        $nameVsCompany[$item['company_name']] = $checkCompany;
                        $this->companyMap[$companyId] = $checkCompany;

                        //关联公司名未被当前userID建档
                        if (!in_array($this->opUser->getUserId(), $checkCompany->user_id)) {
                            $this->failLog($item['email'], \Yii::t('customer', 'Non-own data'));
                            continue;
                        }

                        $companyId = $checkCompany->company_id;
                    }
                }
            }

            try {
                // 查询邮箱是否已存在于该user的私海线索，则走转化线索逻辑，而不是新建客户逻辑
                $leadCustomers = $this->matchLeadsByEmails($item['email'], $this->opUser->getUserId());
                if (!empty($leadCustomers)) {
                    $companyId = $this->processTransform($item['email'], $leadCustomers[$item['email']], $this->opUser->getUserId(), $item['last_mail_id'], $companyId);
                    if ($companyId > 0) {
                        $mailIdVsCompanyId[$item['last_mail_id']] = $companyId;
                    } else {
                        $deleteAdviceIds = array_diff($deleteAdviceIds, [$item['advice_id']]);
                    }

                    continue;
                }

                //新建
                if (!$companyId) {
                    $companyId = $this->processArchive($item['email'], $this->opUser->getUserId(), $extractData, $item['last_mail_id'], $poolDuplicateSwitch);

                    if ($companyId > 0)
                    {
                        $mailIdVsCompanyId[$item['last_mail_id']] = $companyId;
                    } else {
                        $deleteAdviceIds = array_diff($deleteAdviceIds, [$item['advice_id']]);
                    }

                    continue;
                }

                $checkCompany = $this->companyMap[$companyId] ?? new Company($this->clientId, $companyId);
                if ($checkCompany->isExist() && !in_array($this->opUser->getUserId(), $checkCompany->user_id)) {
                    $this->companyMap[$companyId] = $checkCompany;
                    $this->failLog($item['email'], \Yii::t('customer', 'Non-own data'));
                    continue;
                }

                $companyId = $this->processPrivate($item['email'], $companyId, $extractData, $item['last_mail_id'], $poolDuplicateSwitch);
                if ($companyId > 0)
                    $mailIdVsCompanyId[$item['last_mail_id']] = $companyId;

            } catch (\Exception $e) {
                $this->failLog($item['email'], $e->getMessage());
            }

        }

        $this->companyIds = array_values($mailIdVsCompanyId);

        //删除推荐数据
        if (!empty($deleteAdviceIds))
        {
            $operator = new CustomerAdviceBatchOperator($this->opUser->getUserId());
            $operator->setParams(['advice_id' => $deleteAdviceIds]);
            $operator->delete();
        }

        $preFix = self::TASK_LOCK_PREFIX_KEY;
        $lockKey = $preFix . '_' . $this->opUser->getClientId() . '_' . $this->opUser->getUserId();
        \Yii::app()->cache->executeCommand('DEL', [$lockKey]);

        //上传失败原因
        $uploadClass = $this->uploadFailFile();

        $fileId = $uploadClass !== null ? $uploadClass->getFileId() : 0;
        $this->task->finish($fileId, count($adviceIds), count($this->failLogList));

        $fileUrl = $uploadClass !== null ? $uploadClass->getFileUrl() : '';

        $notification = new Notification($this->opUser->getClientId(), Constant::NOTIFICATION_TYPE_CUSTOMER_ADVICE_ARCHIVE);
        $notification->user_id = $this->opUser->getUserId();
        $notification->create_user_id = $this->opUser->getUserId();
        $notification->setSourceData([
            'task_id' => $this->task->getTaskId(),
            'import_time' => $this->task->getImportTime(),
            'total_count' => $this->task->getTotalCount(),
            'fail_count' => $this->task->getFailCount(),
            'url' => $fileUrl,
        ]);
        PushHelper::pushNotification($this->opUser->getClientId(), $this->opUser->getUserId(), $notification);

        return true;
    }

    protected function getAdviceDataList($mailIds)
    {
        $mailIdsString = implode(',', $mailIds);

        $sql = "select * from tbl_ai_customer_advice_data where mail_id IN ($mailIdsString)";
        $db = \ProjectActiveRecord::getDbByClientId($this->clientId);
        $dataList = $db->createCommand($sql)->queryAll(true);

        $return = [];
        foreach ($dataList as $item) {
            $item['company_data'] = $item['company_data'] ? json_decode($item['company_data'], true) : [];
            $item['customer_data'] = $item['customer_data'] ? json_decode($item['customer_data'], true) : [];
            $return[$item['mail_id']] = $item;
        }

        return $return;
    }

    protected function getCustomerFieldData()
    {
        if ($this->customerFieldData !== null) {
            return $this->customerFieldData;
        }

        $customerFieldClass = new CustomerField($this->clientId);
        $this->customerFieldData = Helper::stripFormat([$customerFieldClass->format()]);

        return $this->customerFieldData;
    }

    protected function processPrivate($email, $companyId, $extractData, $mailId, $poolDuplicateSwitch)
    {
        \LogUtil::info("process private: $email,company_id:$companyId");

        $customerData = $extractData['customer_data'];

        if (isset($this->companyMap[$companyId])) {
            $company = $this->companyMap[$companyId];
        } else {
            $company = new Company($this->clientId, $companyId);
        }

        $company->setFieldEditType($this->fieldEditType);
        $company->setFieldEditRefer($mailId, \Constants::TYPE_MAIL);
        $company->setOperatorUserId($this->opUser->getUserId());
        $company->setDeleteAdviceAfterArchive(false);

        if (Helper::emailExists($this->clientId, $email)) {
            $this->failLog($email, 'The contact already exists');
            return 0;
        }
        $customer = new Customer($this->clientId);

        //初始化联系人数据
        $customer->email = $email;
        $customer->main_customer_flag = 0;
        $customer->name = '';
        $customer->post_grade = 0;
        $customer->post = '';
        $customer->tel_list = [];
        $customer->birth = '';
        $customer->gender = 0;
        $customer->remark = '';
        $customer->contact = [];
        $customer->image_list = [];
        $customer->external_field_data = $this->getCustomerFieldData()['external_field_data'] ?? [];

        foreach ($customerData[$email] as $field => $value) {
            if ($field == 'tel_list') {
                $tel = reset($value);
                $fullTel = $tel[0] . $tel[1];

                if ($poolDuplicateSwitch) {
                    $checkCustomer = $this->customerTelMap[$fullTel] ?? (new Customer($this->clientId))->loadByTelToPool($fullTel, 0, null, $this->opUser->getUserId());
                } else {
                    $checkCustomer = $this->customerTelMap[$fullTel] ?? (new Customer($this->clientId))->loadByTel($fullTel);
                }

                if ($checkCustomer->isExist()) {
                    $this->customerTelMap[$fullTel] = $checkCustomer;

                    if ($checkCustomer->company_id != $companyId)
                        continue;
                }

                if ($poolDuplicateSwitch) {
                    $checkCompany = $this->companyTelMap[$fullTel] ?? (new Company($this->clientId))->loadByTelToPool($fullTel, null, $this->opUser->getUserId());
                } else {
                    $checkCompany = $this->companyTelMap[$fullTel] ?? (new Company($this->clientId))->loadByTel($fullTel);
                }

                if ($checkCompany->isExist()) {
                    $this->companyTelMap[$fullTel] = $checkCompany;

                    if ($checkCompany->company_id != $companyId)
                        continue;
                }
            }

            $customer->$field = $value;
        }
        $company->addCustomer($customer);
        $company->save();

        \LogUtil::info("private company_id: $companyId customer_id: {$customer->customer_id}");

        return $company->company_id;
    }

    protected function processArchive($email, $archiveUserId, $extractData, $mailId, $poolDuplicateSwitch)
    {
        \LogUtil::info("process archive: $email, user_id:$archiveUserId");

        if (!Helper::emailCanArchive($this->clientId, $email))
        {
            $this->failLog($email, \Yii::t('customer', 'Save failed, contact mail already exists'));
            return 0;
        }

        $companyData = $extractData['company_data'];
        $customerData = $extractData['customer_data'];

        $company = new Company($this->clientId);
        $company->setOperatorUserId($this->opUser->getUserId());
        $company->setFieldEditType($this->fieldEditType);
        $company->setFieldEditRefer($mailId, \Constants::TYPE_MAIL);
        $company->setDuplicateNameRename(true);
        $company->archive_type = Company::ARCHIVE_TYPE_ADVICE;
        $company->setDeleteAdviceAfterArchive(false);//调度层统一删除推荐
        $company->setCheckQuotaFlag(false);//在调用层检测客户上限
        $company->setCreateScene(Company::CREATE_SCENE_AUTO);

        //检测客户数量上限
        $user = \User::getUserObject($archiveUserId);
        $countData = $user->getCompanyCountData();
        $count = $countData['total_quota'] + 1;
        $limit = $user->getCustomerLimit();
        if ($limit && $count > $limit) {
            $this->failLog($email, \Yii::t('customer', 'Customer amount exceeds the upper limit'));
            return 0;
        }

        if (!isset($companyData['name']) || $companyData['name'] === '') {
            $companyData['name'] = $email;
        }

        foreach ($companyData as $field => $value) {
            if ($field === 'tel') {
                if ($poolDuplicateSwitch) {
                    $checkCompany = $this->companyTelMap[$value] ?? (new Company($this->clientId))->loadByTelToPool($value, null, $this->opUser->getUserId());
                } else {
                    $checkCompany = $this->companyTelMap[$value] ?? (new Company($this->clientId))->loadByTel($value);
                }

                if ($checkCompany->isExist()) {
                    $this->companyTelMap[$value] = $checkCompany;

                    if ($checkCompany->company_id != $company->company_id)
                        continue;
                }

                if ($poolDuplicateSwitch) {
                    $checkCustomer = $this->customerTelMap[$value] ?? (new Customer($this->clientId))->loadByTelToPool($value, 0, null, $this->opUser->getUserId());
                } else {
                    $checkCustomer = $this->customerTelMap[$value] ?? (new Customer($this->clientId))->loadByTel($value);
                }

                if ($checkCustomer->isExist()) {
                    $this->customerTelMap[$value] = $checkCustomer;

                    if ($checkCustomer->company_id != $company->company_id)
                        continue;
                }
            }

            $company->$field = $value;
        }

        if (!Helper::emailCanArchive($this->clientId, $email)) {
            $this->failLog($email, 'Email can not archive');
            return 0;
        } else {
            $customer = new Customer($this->clientId);
            $customer->email = $email;
        }
        //联系人需要初始化清空所有数据
        $customer->name = '';
        $customer->post_grade = 0;
        $customer->post = '';
        $customer->tel_list = [];
        $customer->birth = '';
        $customer->gender = 0;
        $customer->remark = '';
        $customer->contact = [];
        $customer->image_list = [];
        $customer->external_field_data = $this->getCustomerFieldData()['external_field_data'] ?? [];
        $customer->main_customer_flag = 1;

        foreach ($customerData[$email] as $field => $value) {
            if ($field == 'tel_list') {
                $tel = reset($value);
                $fullTel = $tel[0] . $tel[1];

                if ($poolDuplicateSwitch) {
                    $checkCustomer = $this->customerTelMap[$fullTel] ?? (new Customer($this->clientId))->loadByTelToPool($fullTel, 0, null, $this->opUser->getUserId());
                } else {
                    $checkCustomer = $this->customerTelMap[$fullTel] ?? (new Customer($this->clientId))->loadByTel($fullTel);
                }

                if ($checkCustomer->isExist()) {
                    $this->customerTelMap[$fullTel] = $checkCustomer;

                    if ($checkCustomer->company_id != $customer->company_id)
                        continue;
                }

                if ($poolDuplicateSwitch) {
                    $checkCompany = $this->companyTelMap[$fullTel] ?? (new Company($this->clientId))->loadByTelToPool($fullTel, null, $this->opUser->getUserId());
                } else {
                    $checkCompany = $this->companyTelMap[$fullTel] ?? (new Company($this->clientId))->loadByTel($fullTel);
                }

                if ($checkCompany->isExist()) {
                    $this->companyTelMap[$fullTel] = $checkCompany;

                    if ($checkCompany->company_id != $customer->company_id)
                        continue;
                }
            }

            $customer->$field = $value;
        }

        $company->addUser($archiveUserId);
        $company->setCustomerList([$customer]);
        $company->save();

        \LogUtil::info('archive company_id: ' . $company->company_id);
        return $company->company_id;
    }

    protected function processTransform($email, $leadId, $archiveUserId, $lastMailId, $companyId = 0)
    {
        $result = null;
        $customerIds = [];

        if (empty($leadId)) {
            return $result;
        }

        \LogUtil::info("process transform emails:[{$email}]");

        $company = new Company($this->clientId, $companyId);
        $company->setFieldEditType($this->fieldEditType);
        $company->setFieldEditRefer($lastMailId, \Constants::TYPE_MAIL);
        $company->setOperatorUserId($this->opUser->getUserId());

        if ($company->isNew()) {
            $company->addUser($archiveUserId);
            $company->setDeleteAdviceAfterArchive(false);//调度层统一删除推荐
            $company->setCheckQuotaFlag(false);//自动化建档不做客户上限检查
            $company->setCreateScene(Company::CREATE_SCENE_AUTO);
            $company->setDuplicateNameRename(true);
            $company->archive_type = Company::ARCHIVE_TYPE_ADVICE;
        }

        $lead = new Lead($this->clientId, $leadId);
        $lead->setFieldEditType($this->fieldEditType);
        $lead->setFieldEditRefer($lastMailId, \Constants::TYPE_MAIL);

        if ($lead->isExist()) {
            $customerList = \common\library\customer\Helper::leadTransformCompany($lead, $company, $this->opUser->getUserId());
            foreach ($customerList as $customer) {
                $customerIds[] = $customer->customer_id;
            }
        }

        \LogUtil::info("process transform emails:[{$email}]: leadId:[$leadId] new companyId:{$company->company_id} ");
        return $company->company_id;
    }

    protected function matchLeadsByEmails($emails, $archiveUserId)
    {
        if (empty($emails)) {
            return [];
        }

        if (!is_array($emails)) {
            $emails = [$emails];
        }

        $list = new LeadCustomerList($this->clientId);
        $list->setFields(['lead_id', 'email']);
        $list->setUserId($archiveUserId);
        $list->setEmail($emails);
        $list->setIsArchive(Lead::ARCHIVE_OK);
        $result = $list->find();
        $leadIds = array_column($result, 'lead_id');
        $leadCustomers = [];

        if (!empty($leadIds)) {
            $list = new LeadList($archiveUserId);
            $list->setLeadId($leadIds);
            $list->setStatusId([
                \common\library\setting\library\status\Status::SYS_STATUS_NEW,
                \common\library\setting\library\status\Status::SYS_STATUS_COMPLETE_INFO,
                \common\library\setting\library\status\Status::SYS_STATUS_INITIATIVE_TO_CONTACT,
                \common\library\setting\library\status\Status::SYS_STATUS_INTERKNIT,
            ]);
            $list->setIsArchive(Lead::ARCHIVE_OK);
            $list->setOrderBy('order_time desc,edit_time desc,update_time');
            $list->getFormatter()->setShowCustomer(true);
            $result = $list->find();

            foreach ($result as $item) {
                foreach ($item['customers'] as $customer) {
                    if (!empty($customer['email']) && in_array($customer['email'], $emails) && !isset($leadCustomers[$customer['email']])) {
                        $leadCustomers[$customer['email']] = $item['lead_id'];
                    }
                }
            }
        }

        return $leadCustomers;
    }

    protected function failLog($email, $msg)
    {
        $this->failLogList[$email] = $msg;
    }

    /**
     * @return null|\UploadObject
     */
    protected function uploadFailFile(){

        if (empty($this->failLogList)) {
            return null;
        }

        $fileName = "customer_advice_archive_fail_result_{$this->task->getTaskId()}.csv";
        $path = "/tmp/$fileName";

        $firstLine = ['email', 'fail reason'];
        $fp = fopen($path, 'w');
        fputcsv($fp, $firstLine);
        foreach ($this->failLogList as $email => $reason) {
            fputcsv($fp, [$email, $reason]);
        }
        fclose($fp);

        $old_contents = file_get_contents($path);
        $utf_contents = mb_convert_encoding($old_contents, "GBK", "UTF-8");

        //如果没有转换失败的话
        if (!empty($utf_contents) || $utf_contents != false)
            file_put_contents($path, $utf_contents);


        $fileKey = \UploadService::getFileKey($fileName);
        $upload = \UploadService::uploadRealFile($path, $fileName, $fileKey);

        return $upload;
    }
}