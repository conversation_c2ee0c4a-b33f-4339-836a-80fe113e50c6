<?php
/**
 * Created by PhpStor<PERSON>.
 * User: andy
 * Date: 2018/11/15
 * Time: 18:09
 */

namespace common\library\ai\classify\mail;

use common\library\account\Client;
use common\library\account\UserList;
use common\library\ai\classify\capture\CaptureCard;
use common\library\ai\classify\capture\CaptureCardLog;
use common\library\ai\classify\customer\advice\AiCustomerAdvice;
use common\library\ai\classify\customer\advice\CustomerAdviceList;
use common\library\ai\classify\extract\ExtractFactory;
use common\library\ai\classify\extract\FileExtract;
use common\library\ai\classify\setting\Settings;
use common\library\api\InnerApi;
use common\library\cms\inquiry\sitemail\SiteMail;
use common\library\cms\site\SiteService;
use common\library\contact\ContactList;
use common\library\contact\Helper;
use common\library\customer\blacklist\CustomerBlacklistList;
use common\library\customer_v3\common\BaseList;
use common\library\customer_v3\company\orm\CompanyFilter;
use common\library\customer_v3\customer\CustomerList;
use common\library\customer\field_unique\DuplicateFlagBuilder;
use common\library\customer_convert\ConvertHandler;
use common\library\duplicate\DuplicateConstants;
use common\library\email\CommonDomain;
use common\library\email\Util;
use common\library\email_identity\EmailIdentityList;
use common\library\lead\Lead;
use common\library\lead\LeadCustomerList;
use common\library\mail\Mail;
use common\library\mail\setting\black\MailBlackList;
use common\library\statistics\condition\reference_value\OrderNotIntoPerformanceStatus;
use common\library\util\IP;
use common\library\util\PgsqlUtil;
use UserMailAlias;

class Classify
{
    const RULE_SUBJECT = 'keyword_subject';
    const RULE_CONTENT = 'keyword_content';
    const RULE_RISK = 'risk';
    const RULE_SYSTEM_EMAIL = 'system_email';
    const RULE_SYSTEM_DOMAIN = 'system_domain';
    const RULE_SYSTEM_PREFIX = 'system_prefix';
    const RULE_SYSTEM_EMAIL_KEYWORD = 'system_email_keyword';
    const RULE_B2B_EMAIL = 'b2b_email';
    const RULE_B2B_DOMAIN = 'b2b_domain';
    const RULE_B2C_EMAIL = 'b2c_email';
    const RULE_B2C_DOMAIN = 'b2c_domain';
    const RULE_BLACK_LIST = 'black';
    const RULE_MAIL_BLACK_LIST = 'mail_black';
    const RULE_COLLEGE_ACCOUNT = 'college_account';
    const RULE_COLLEGE_EMAIL = 'college_email';
    const RULE_CONTACT = 'contact';
    const RULE_COLLEGE_CUSTOMER = 'college_customer';
    const RULE_PUBLIC_CUSTOMER = 'public_customer';
    const RULE_IGNORE_AREA_CUSTOMER = 'ignore_area_customer';
    const RULE_FILTER_IMAP_FOLDER = 'filter_imap_folder';
    /**
     * @deprecated
     */
    const RULE_MULTI_PRIVATE = 'multi_private_customer';
    /**
     * @deprecated
     */
    const RULE_PRIVATE_CUSTOMER_DOMAIN = 'private_customer_domain';
    /**
     * @deprecated
     */
    const RULE_PRIVATE_CUSTOMER_PUBLIC_DOMAIN = 'private_customer_public_domain';
    const RULE_COLLEGE_DOMAIN = 'college_domain';
    /**
     * @deprecated
     */
    const RULE_NOT_SAME_DOMAIN_WITH_SENDER = 'not_same_domain_with_sender';
    const RULE_NOT_SAME_WITH_MAIN_DOMAIN = 'not_same_with_main_domain';
    /**
     * @deprecated
     */
    const RULE_PRIVATE_CUSTOMER_NOT_SAME_DOMAIN_WITH_SENDER = 'not_private_same_domain_with_sender';
    const RULE_CUSTOMER_ADVICE_NOT_ACCEPT = 'customer_advice_not_accept';
    const RULE_COLLEGE_CUSTOMER_DOMAIN = 'college_customer_domain';
    const RULE_INQUIRY_EMPTY_EMAIL = 'inquiry_empty_email';
    const RULE_INQUIRY_CUSTOMER_EXISTS = 'inquiry_customer_exists';
    /**
     * @deprecated
     */
    const RULE_QUOTATION_NOT_FIRST_CONVERSATION = 'quotation_not_first_conversation';
    /**
     * @deprecated
     */
    const RULE_QUOTATION_FIRST_CONVERSATION = 'quotation_first_conversation';
    const RULE_MAIL_FOLDER_JUNK_ID = 'mail_folder_junk_id';
    const RULE_MAIL_BOUNCE_FLAG = 'mail_bounce_flag';
    const RULE_RECEIVERS_DOMAIN_NOT_UNIQUE = 'receivers_domain_not_unique';
    const RULE_PRIVATE_CUSTOMER = 'private_customer';

    const INQUIRY_TYPE_ALIBABA = 'alibaba_inquiry';
    const INQUIRY_TYPE_GLOBAL_SOURCES = 'global_sources_inquiry';
    const INQUIRY_TYPE_GLOBAL_MARKET = 'global_market_inquiry';
    const INQUIRY_TYPE_MADE_IN_CHINA = 'made_in_china_inquiry';
    const INQUIRY_TYPE_PERSONAL = 'personal_inquiry'; //个人询盘邮件

    const MAIL_CLASSIFY_TYPE_DEVELOP = 'develop';
    const MAIL_CLASSIFY_TYPE_INQUIRY = 'rfq';//java返回的是rfq
    const MAIL_CLASSIFY_TYPE_QUOTATION = 'price';//java返回的是price
    const MAIL_CLASSIFY_TYPE_PO = 'po';
    const MAIL_CLASSIFY_TYPE_PI = 'pi';
    const MAIL_CLASSIFY_TYPE_SAMPLE_PI = 'sample';
    const MAIL_CLASSIFY_TYPE_PL = 'pl';
    const MAIL_CLASSIFY_TYPE_CI = 'ci';
    const MAIL_CLASSIFY_TYPE_BS = 'bs'; //水单
    const MAIL_CLASSIFY_TYPE_BL = 'bl'; //其他业务邮件

    const RECOGNITION_MODE_CANCEL_BY_USER = -1; //用户取消的
    const RECOGNITION_MODE_BY_MODE = 1; //模型识别的报价
    const RECOGNITION_MODE_PI_BY_CONTENT = 2; //内容识别对的PI
    const RECOGNITION_MODE_PI_BY_ATTACH = 3; //附件识别的PI
    const RECOGNITION_MODE_QUOTATION_BY_SUBJECT = 4; //规则识别的报价
    const RECOGNITION_MODE_QUOTATION_BY_CONTENT = 5; //规则识别的报价
    const RECOGNITION_MODE_QUOTATION_BY_ATTACH = 6; //规则识别的报价
    const RECOGNITION_MODE_INQUIRY_PLATFORM = 7; //识别的平台询盘邮件
    const RECOGNITION_MODE_INQUIRY_PERSONAL_BY_SUBJECT = 8; //识别的个人询盘邮件 标题
    const RECOGNITION_MODE_INQUIRY_PERSONAL_BY_CONTENT = 9; //识别的个人询盘邮件 内容
    const RECOGNITION_MODE_CI_BY_CONTENT = 10; //内容识别的ci
    const RECOGNITION_MODE_CI_BY_ATTACH = 1; //通过附加识别的ci
    const RECOGNITION_MODE_BS_BY_CONTENT = 12; //通过内容识别的水单
    const RECOGNITION_MODE_BS_BY_ATTACH = 13; //通过附件识别的水单
    const RECOGNITION_MODE_PL_BY_CONTENT = 14; //通过内容识别的pl
    const RECOGNITION_MODE_PL_BY_ATTACH = 15; //通过附件识别的pl
    const RECOGNITION_MODE_SAMPLE_PI_BY_CONTENT = 16; //内容识别对的样品PI
    const RECOGNITION_MODE_SAMPLE_PI_BY_ATTACH = 17; //附件识别的样品PI



    protected $clientId;
    protected $userId;
    protected $userMailId;
    protected $mailId;
    protected $invoiceExtraData;
    protected $setting;
    protected $applyList = [];

    protected $reuseJavaData = true;

    /**
     * @var Mail
     */
    protected $mail;

    protected $customer;

    protected $classifyRules = [];

    protected $lastInquiryType;
    protected $lastCustomerProb;
    protected $lastCustomerProbVersion;
    protected $lastMailClassifyType;
    protected $lastMailClassifyProb;
    protected $lastMailClassifyProbVersion;
    protected $lastMailClassifyProbVersionType;
    protected $lastFilterResult = [];
    protected $lastCompanyInfo = [];
    protected $processCompany = false;
    /**
     * @var \AiMailClassify
     */
    protected $lastClassifyLog;

    protected $customerFaker = [];

    protected $simulation = false;
    protected $simulateNewCustomer = false;
    static protected $simulationCustomerMap = [];

    protected $skipRules = [];

    // 一下6个变量是建档检查中间数据，不能直接调用，必须用get函数调用
    private $mailBlackList;
    private $customerBlackList;
    private $userEmails; // 用户登录帐号
    private $userMailEmails; // 用户绑定邮箱
    private $contactsEmails;
    private $adviceFilterEmails;//不采纳客户建议邮箱


    private $archiveEmails=[];
    private $dynamicParams=[];  //动态参数, 会覆盖配置文件中的参数

    // Capture Card 记录
    protected $captureCardLog;

    protected $mailAttachList; // 邮件附件列表

    // 产品自动化列表
    protected $productResult = [];

    // mkt cms site id、ga site_id
    protected $marketingCmsSiteId;
    protected $marketingGaSiteId;

    public function __construct($clientId, $userId, $config='ai_mail_classify')
    {
        $this->clientId = $clientId;
        $this->userId = $userId;
        $this->classifyRules = $this->loadConfig($config);

        \ProjectActiveRecord::setConnection(\ProjectActiveRecord::getDbByClientId($clientId));
        \PgActiveRecord::setConnection(\PgActiveRecord::getDbByClientId($clientId));
    }

    private function loadConfig($config)
    {
        $path = \Yii::getPathOfAlias("application.config.yaml.{$config}") . '.yaml';
        $classifyRules = \Symfony\Component\Yaml\Yaml::parse(file_get_contents($path));

        $path = '/etc/crm_config/ai_validate_mail.yaml';
        $mailRules = is_file($path) ? \Symfony\Component\Yaml\Yaml::parse(file_get_contents($path)) : [];
        if (isset($mailRules['rule']['validate_mail']) && isset($classifyRules['rule']['validate_mail'])) {
            $classifyRules['rule']['validate_mail'] = array_merge($classifyRules['rule']['validate_mail'], $mailRules['rule']['validate_mail'] ?? []);
        }
        return $classifyRules;
    }

    /**
     * @param array $dynamicParams
     */
    public function setDynamicParams(array $dynamicParams)
    {
        $this->dynamicParams = $dynamicParams;
    }


    public function addCustomerFaker($companyId, array $emails)
    {
        foreach ($emails as $email)
        {
            $this->customerFaker[$email] = $companyId;

            if ($this->simulateNewCustomer)
            {
                self::$simulationCustomerMap[$this->clientId][$email] = [
                    'customer_id' => $email,
                    'email' => $email,
                    'user_id' => [$this->userId],
                    'company_id' => $companyId
                ];
            }
        }
    }

    public function resetCustomerFaker()
    {
        $this->customerFaker = [];
    }

    public function setSimulation($flag)
    {
        $this->simulation = $flag;
    }

    public function setSimulateNewCustomer($flag)
    {
        $this->simulateNewCustomer = $flag;
    }

    public function setSkipRule($rule, $result=false)
    {
        $this->skipRules[$rule] = $result;
    }

    public function setReuseJavaData($flag)
    {
        $this->reuseJavaData = $flag;
    }

    public function getCustomerFaker()
    {
        return $this->customerFaker;
    }

    public function getLastCustomerProb()
    {
        return $this->lastCustomerProb;
    }

    public function getLastCustomerProbVersion()
    {
        return $this->lastCustomerProbVersion;
    }

    public function getLastMailClassifyType()
    {
        return $this->lastMailClassifyType;
    }

    public function getLastMailClassifyProbVersion()
    {
        return $this->lastMailClassifyProbVersion;
    }

    public function getLastMailClassifyProbVersionType()
    {
        return $this->lastMailClassifyProbVersionType;
    }

    public function getLastMailClassifyProb()
    {
        return $this->lastMailClassifyProb;
    }

    public function getLastFilterResult()
    {
        return $this->lastFilterResult;
    }

    public function setProductResult($result)
    {
        $this->productResult = $result;
    }

    public function getProductResult()
    {
        return $this->productResult;
    }

    public static function prettyFilterSummary($data)
    {
        $filterSummary = '';

        foreach ($data as $rule => $elem)
        {
            if (empty($elem))
                $str = '邮件';
            else
                $str = '邮箱地址: ' . implode(', ', $elem);

            $ruleName = $rule;

            switch ($rule)
            {
                case Classify::RULE_SUBJECT:
                    $ruleName = '邮件标题';
                    break;
                case Classify::RULE_CONTENT:
                    $ruleName = '邮件正文';
                    break;
                case Classify::RULE_RISK:
                    $ruleName = '高危邮件';
                    break;
                case Classify::RULE_SYSTEM_PREFIX:
                    $ruleName = '系统前缀';
                    break;
                case Classify::RULE_SYSTEM_EMAIL:
                    $ruleName = '系统邮箱';
                    break;
                case Classify::RULE_SYSTEM_DOMAIN:
                    $ruleName = '系统域名';
                    break;
                case Classify::RULE_SYSTEM_EMAIL_KEYWORD:
                    $ruleName = '邮箱关键字';
                    break;
                case Classify::RULE_B2B_EMAIL:
                    $ruleName = 'B2B邮箱';
                    break;
                case Classify::RULE_B2B_DOMAIN:
                    $ruleName = 'B2B域名';
                    break;
                case Classify::RULE_B2C_EMAIL:
                    $ruleName = 'B2C邮箱';
                    break;
                case Classify::RULE_B2C_DOMAIN:
                    $ruleName = 'B2C域名';
                    break;
                case Classify::RULE_BLACK_LIST:
                    $ruleName = '建档黑名单';
                    break;
                case Classify::RULE_MAIL_BLACK_LIST:
                    $ruleName = '邮件黑名单';
                    break;
                case Classify::RULE_COLLEGE_ACCOUNT:
                    $ruleName = '同事帐号';
                    break;
                case Classify::RULE_COLLEGE_EMAIL:
                    $ruleName = '企业内绑定邮箱';
                    break;
                case Classify::RULE_CONTACT:
                    $ruleName = '通讯录';
                    break;
                case Classify::RULE_COLLEGE_CUSTOMER:
                    $ruleName = '同事私海';
                    break;
                case Classify::RULE_PUBLIC_CUSTOMER:
                    $ruleName = '公海';
                    break;
                case Classify::RULE_MULTI_PRIVATE:
                    $ruleName = '多个私海';
                    break;
                case Classify::RULE_PRIVATE_CUSTOMER_DOMAIN:
                    $ruleName = '私海客户域名不匹配';
                    break;
                case Classify::RULE_PRIVATE_CUSTOMER_PUBLIC_DOMAIN:
                    $ruleName = '私海客户公共域名过滤';
                    break;
                case Classify::RULE_COLLEGE_DOMAIN:
                    $ruleName = '公司内账号域名/邮箱域名过滤';
                    break;
                case Classify::RULE_NOT_SAME_DOMAIN_WITH_SENDER:
                    $ruleName = '与发件人域名不一致';
                    break;
                case Classify::RULE_NOT_SAME_WITH_MAIN_DOMAIN:
                    $ruleName = '与主要邮箱域名不一致';
                    break;
                case Classify::RULE_PRIVATE_CUSTOMER_NOT_SAME_DOMAIN_WITH_SENDER:
                    $ruleName = '私海客户与发件人域名不一致';
                    break;
                case Classify::RULE_CUSTOMER_ADVICE_NOT_ACCEPT:
                    $ruleName = '客户推荐标记不采纳';
                    break;
                case Classify::RULE_COLLEGE_CUSTOMER_DOMAIN:
                    $ruleName = '同事私海已建档企业邮箱域名，且该域名未被本人私海建档';
                    break;
                case Classify::RULE_INQUIRY_EMPTY_EMAIL:
                    $ruleName = '未提取到询盘邮箱';
                    break;
                case Classify::RULE_INQUIRY_CUSTOMER_EXISTS:
                    $ruleName = '询盘邮箱已建档';
                    break;
                case Classify::RULE_QUOTATION_NOT_FIRST_CONVERSATION:
                    $ruleName = '报价单判定非首次会话';
                    break;
                case Classify::RULE_QUOTATION_FIRST_CONVERSATION:
                    $ruleName = '报价单判定首次会话';
                    break;
                case Classify::RULE_MAIL_FOLDER_JUNK_ID:
                    $ruleName = '垃圾邮件';
                    break;
                case Classify::RULE_MAIL_BOUNCE_FLAG:
                    $ruleName = '退信邮件';
                    break;
                case Classify::RULE_RECEIVERS_DOMAIN_NOT_UNIQUE:
                    $ruleName = '收件人不唯一且收件邮箱后缀不一致';
                    break;
                case Classify::RULE_PRIVATE_CUSTOMER:
                    $ruleName = '邮箱私海已建档';
                    break;
            }

            $filterSummary .= "条件 $ruleName 命中，{$str}被过滤；\n";
        }

        return $filterSummary;
    }

    public static function prettyArchiveData($data)
    {
        $archive = $data[0] ?? [];
        $private = $data[1] ?? [];

        $summary = '';

        foreach ($archive as $elem)
        {
            $summary .= '新建客户（联系人：' . implode(' ', $elem) . ')';
        }

        foreach ($private as $companyId => $elem)
        {
            $summary .= '更新私海'.$companyId.'（联系人：' . implode(' ', $elem) . ')';
        }

        return $summary;
    }

    public function getLastFilterSummary()
    {
        return self::prettyFilterSummary($this->lastFilterResult);
    }

    public function isCompanyInfoAvailable()
    {
        return $this->processCompany;
    }

    public function getLastCompanyInfo()
    {
        return $this->lastCompanyInfo;
    }

    /**
     * 保存邮件来的联系人信息
     * 可以保存的信息:
     * 1 发出的邮件, 取收件人邮箱
     * 2 收到的邮件取发件人信息
     * 3 客户自动化建档的邮箱
     * @param $companyId
     * @param array $customerIds
     */
    public function setCompanyInfo($companyId, array $customerIds)
    {
        $this->lastCompanyInfo[$companyId] = array_values(array_unique(array_merge($this->lastCompanyInfo[$companyId] ?? [], $customerIds)));
    }

    /**
     * @return Mail
     */
    public function getLastMail()
    {
        return $this->mail;
    }

    /**
     * @return \AiMailClassify
     */
    public function getLastClassifyLog(): \AiMailClassify
    {
        return $this->lastClassifyLog;
    }

    public function applyClassifyLog($customerData = null)
    {
        if ($customerData)
            $this->lastClassifyLog->customer_data = snappy_compress(json_encode($customerData));;

        $this->lastClassifyLog->apply_flag = 1;
        return $this->lastClassifyLog->save();
    }

    public function getCaptureCardLog(): CaptureCardLog
    {
        return $this->captureCardLog;
    }

    public function process($userMailId, $mailId)
    {
        $this->userMailId = $userMailId;
        $this->mailId = $mailId;
        $this->captureCardLog = new CaptureCardLog($this->clientId, $mailId);
        $this->captureCardLog->biz_type = CaptureCard::BIZ_TYPE_MAIL;
        $this->captureCardLog->cleanUp();
        $this->setting = new Settings($this->clientId);

        $this->lastClassifyLog = \AiMailClassify::model()->find('client_id=:client_id and user_id=:user_id and user_mail_id=:user_mail_id and mail_id=:mail_id',
            [':client_id' => $this->clientId, ':user_id' => $this->userId, ':user_mail_id' => $userMailId, ':mail_id' => $mailId]);

        if (!$this->lastClassifyLog)
        {
            $this->lastClassifyLog = new \AiMailClassify();
            $this->lastClassifyLog->client_id = $this->clientId;
            $this->lastClassifyLog->user_id = $this->userId;
            $this->lastClassifyLog->user_mail_id = $userMailId;
            $this->lastClassifyLog->mail_id = $mailId;
            $this->lastClassifyLog->create_time = date('Y-m-d H:i:s');
        }

        $this->lastFilterResult = [];
        $this->lastCustomerProb = null;
        $this->lastCustomerProbVersion = null;
        $this->lastMailClassifyType = null;
        $this->lastMailClassifyProb = null;
        $this->lastMailClassifyProbVersion = null;
        $this->lastMailClassifyProbVersionType = null;
        $this->lastInquiryType = null;
        $this->lastCompanyInfo = [];
        $this->processCompany = false;
        $this->archiveEmails =[];

        $dynamicParams = $this->dynamicParams;
        $this->dynamicParams =[];

        $this->mail = new Mail($mailId);
        $this->mail->setOperatorUser($this->userId);

        $rules = $this->classifyRules['rule'];

        foreach ($rules as $apply => $rule)
        {
            if (array_key_exists($apply, $this->skipRules))
            {
                if( $this->skipRules[$apply] !== false )
                {
                    $this->applyList[$apply] = $this->skipRules[$apply];
                }

                continue;
            }


            $function = $rule['function'];
            //支持动态注入参数
            $params = array_merge($rule['params'] ?? [], $dynamicParams[$apply]??[]);

            $continue = false;
            $successOne = false;

            $requireList = $rule['require'] ?? [];
            $requireType = $rule['require_type'] ?? 'and';

            foreach ($requireList as $requireApply => $requireResult)
            {
                if ($requireResult && !array_key_exists($requireApply, $this->applyList))
                {
                    if ($requireType == 'and')
                    {
                        $continue = true;
                        break;
                    }
                    else if ($requireType == 'or')
                    {
                        continue;
                    }
                    else throw new \ProcessException("require type $requireType not supported");
                }

                if (!$requireResult && array_key_exists($requireApply, $this->applyList))
                {
                    if ($requireType == 'and')
                    {
                        $continue = true;
                        break;
                    }
                    else if ($requireType == 'or')
                    {
                        continue;
                    }
                    else throw new \ProcessException("require type $requireType not supported");

                }

                $successOne = true;
            }

            if ($requireType == 'and' && $continue)
                continue;

            if ($requireType == 'or' && !$successOne)
                continue;

            try
            {
                $result = call_user_func_array([$this, $function], $params);
            }
            catch (\Exception $e)
            {
                $this->lastClassifyLog->save();
                throw $e;
            }

            if ($result !== false)
                $this->applyList[$apply] = $result;
        }

        $this->lastClassifyLog->classify_flag = 1;
//        if (!empty($this->applyList))
//            $this->lastClassifyLog->classify_data = snappy_compress(json_encode($this->applyList));
//
//        if (!empty($this->lastFilterResult))
//            $this->lastClassifyLog->customer_filter_data = snappy_compress(json_encode($this->lastFilterResult));


        $this->lastClassifyLog->save();
        $this->captureCardLog->save();

        return $this->applyList;
    }

    protected function customerProb($prob)
    {
        //客户概率只针对收件
        if (!$this->getLastMail()->isReceiverType())
            return false;

        if ($this->reuseJavaData && !$this->lastClassifyLog->getIsNewRecord() && $this->lastClassifyLog->customer_classify_call)
        {
            $customerProb = $this->lastClassifyLog->customer_prob;
            $customerProbVersion = $this->lastClassifyLog->customer_prob_version;
        }
        else
        {
            $api = new InnerApi('mail_classify');
            if ($this->simulation || $this->userId == 55258608)
                $api->setAccessLog(true);

            $data = $api->call('customer', [
                'mailId' => $this->mailId, 'userMailId' => $this->userMailId,
                'clientId' => $this->clientId, 'userId' => $this->userId
            ]);

            if ($this->userId == 55258608)
                \LogUtil::info('andy log mail id: ' . $this->mailId . ' customer prob: ' . json_encode($data));

            $data = $data['data'];

            $customerProbVersion = $this->formatVersion($data['version']);

            $this->lastClassifyLog->customer_classify_call = 1;
            $this->lastClassifyLog->customer_prob_version = $customerProbVersion;

            $customerType = $data['mailType'];
            $customerProb = 0;
            foreach ($customerType as $elem)
            {
                if ($elem['typeName'] == 'customer')
                {
                    $customerProb = $elem['prob'];
                    $this->lastClassifyLog->customer_prob = $customerProb;
                }
            }
        }

        $this->lastCustomerProb = $customerProb;
        $this->lastCustomerProbVersion = $customerProbVersion;

        $result = $customerProb > $prob ? $customerProb : false;

        if ($result) {
            $this->captureCardLog->tag_id = \common\library\setting\library\tag\Tag::MAIL_TAG_BL;
            $this->captureCardLog->classify_type = self::MAIL_CLASSIFY_TYPE_BL;
            $this->captureCardLog->type = CaptureCard::REASON_TYPE_BY_MACHINE_LEARNING_MODEL;

            $this->lastClassifyLog->mail_tag_id = \common\library\setting\library\tag\Tag::MAIL_TAG_BL;
            $this->lastMailClassifyType = self::MAIL_CLASSIFY_TYPE_BL;
            $this->lastClassifyLog->mail_classify_type = self::MAIL_CLASSIFY_TYPE_BL;
        }

        return $result;
    }

    public function logCounterRule($emails, $rule)
    {
        if (!isset($this->lastFilterResult[$rule]))
            $this->lastFilterResult[$rule] = [];

        if (is_array($emails))
            $this->lastFilterResult[$rule] = array_merge($this->lastFilterResult[$rule], $emails);
        else
            $this->lastFilterResult[$rule][] = $emails;

        return $this->lastFilterResult;
    }

    /**
     * @return array|bool
     * @throws \ProcessException
     * 邮件被排除分析的情况：
    符合以下条件之一的业务邮件，整个邮件停止建档处理
    1、邮件联系人中含有【同事私海已建档】邮箱
    2、邮件联系人中含有【邮箱设置的黑名单】邮箱
    3、邮件中出现同事的私海已建档客户的企业邮箱后缀，且该企业邮箱后缀未被本人私海建档。
    4、来自特殊发件人的邮件。（如：邮箱前缀等于“postmaster”“noreply”“no-reply”“no_reply”的发件人）--validMail方法保证
    5、来自阿里巴巴、环球资源、环球市场、中国制造的平台邮件。（来自平台的询盘邮件将处理为建档到线索）--inquiry 方法保证
    6、垃圾邮件。即同步到小满前，已被用户邮箱服务器标记为垃圾邮件的邮件。--validMail方法保证
     * 邮箱被排除建档的情况：
    可建档的业务邮件内，符合以下情况的邮箱将被排除
    1、客户建档黑名单内的邮箱/企业邮箱后缀
    2、企业内所有已绑定的邮箱
    3、企业组织架构内的账号邮箱
    4、与前项2、3内的企业邮箱拥有相同后缀的邮箱
    5、公司范围内所有已建档客户的邮箱
    6、被用户标记为不建档的邮箱
     */
    protected function customerArchiveCheck($emails = [])
    {
        if ($this->mail->getMailType() == \Mail::MAIL_TYPE_UNKNOWN)
        {
            \LogUtil::info("mail id: {$this->mailId} MailType 0");
            return false;
        }

        $allEmails = [];
        $mainEmail = '';
        $mainDomain = '';
        if ($this->mail->isSendType())
        {
            $receivers = \EmailUtil::findAllMailAddress($this->mail->receiver);
            $ccAndBcc = \EmailUtil::findAllMailAddress($this->mail->cc . ';' . $this->mail->bcc);
            $ccAndBcc = is_array($ccAndBcc) ? array_values($ccAndBcc) : [];
            $allEmails = array_merge($receivers, $ccAndBcc);

            if (count($receivers) === 1)
                $mainEmail = $receivers[0];

            //收件人不唯一，检测所有邮箱后缀是否一致
            if (!$mainEmail)
            {
                $existsDomain = '';
                foreach ($allEmails as $email)
                {
                    $domain = \EmailUtil::getDomain($email);

                    if ($existsDomain && $domain !== $existsDomain)
                    {
                        $this->logCounterRule([], self::RULE_RECEIVERS_DOMAIN_NOT_UNIQUE);
                        return false;
                    }

                    $existsDomain = $domain;
                }
                $mainDomain = $existsDomain;
            } else {
                $mainDomain = \EmailUtil::getDomain($mainEmail);
            }

            //检测是否客户邮箱
            $checkLastCompanyEmails = $allEmails;
        }
        else
        {
            if (empty($this->mail->reply_to))
            {
                $mainEmail = \EmailUtil::findEmail($this->mail->sender);
            } else {
                $mainEmail = \EmailUtil::findEmail($this->mail->reply_to);
            }
            $mainDomain = \EmailUtil::getDomain($mainEmail);

            $allReceiver = \EmailUtil::findAllMailAddress($this->mail->receiver . ';' . $this->mail->cc);

            $allReceiver = is_array($allReceiver) ? array_values($allReceiver) : [];
            $allEmails = array_merge([$mainEmail], $allReceiver);

            $checkLastCompanyEmails = [$mainEmail];
        }
        if (!empty($emails)) {
            $allEmails = array_merge($allEmails, $emails);
        }
        $allEmails = array_values(array_unique(array_filter($allEmails)));

        if ($this->simulation && $this->simulateNewCustomer)
        {
            $customerList = [];
            $checkList = self::$simulationCustomerMap[$this->clientId] ?? [];
            foreach ($allEmails as $email)
            {
                if (array_key_exists($email, $checkList))
                    $customerList[] = $checkList[$email];
            }
        } else {
            $list = new CustomerList($this->clientId);
            $list->setIsArchive(1);
            $list->setEmail($allEmails); //注意 $allEmails 必须是包含了往来邮箱, 否则会导致lastCompanyInfo信息不完整, 导致客户动态有bug
            $list->setCheckPoolDuplicateSwitchFlag(true);
            $list->setFields(['customer_id', 'email', 'user_id', 'company_id']);
            $customerList = $list->find();
        }

        $companyId = 0;
        $publicEmails = [];
        $privateEmails = [];
        $emailVsPrivateCompany = [];

        $return = false;
        $this->processCompany = true;
        foreach ($customerList as $customer)
        {
            //记录邮件中相关客户信息用于后续商机、客户动态使用
            if (in_array($customer['email'], $checkLastCompanyEmails))
                $this->lastCompanyInfo[$customer['company_id']][] = $customer['customer_id'];

            if ($return) continue;

            $customer['user_id'] = is_array($customer['user_id']) ? $customer['user_id'] : PgsqlUtil::trimArray($customer['user_id']);

            if (empty($customer['user_id']))
            {
                $publicEmails[] = $customer['email'];
                continue;
            }

            // 邮件联系人中含有【同事私海已建档】邮箱 整封邮件剔除
            if (!in_array($this->userId, $customer['user_id']))
            {
                $this->logCounterRule([$customer['email']], self::RULE_COLLEGE_CUSTOMER);
                $return = true;
                continue;
            }

            // 自己私海
            $privateEmails[] = $customer['email'];
            $emailVsPrivateCompany[$customer['email']] = $customer['company_id'];

            if ($mainEmail == $customer['email'])
                $companyId = $customer['company_id'];
        }

        //邮件被排除分析的情况优先
        if ($return)
            return false;

        //没有主要邮箱或主要邮箱未被建档，查询其余邮箱是否建档
        if (!$companyId && !empty($emailVsPrivateCompany))
        {
            foreach ($allEmails as $email)
            {
                //与主要邮箱域名不一致跳过；
                $domain = \EmailUtil::getDomain($email);
                if ($domain != $mainDomain)
                    continue;

                $companyId = $emailVsPrivateCompany[$email] ?? 0;
                //私海已建档
                if ($companyId)
                    break;
            }
        }

        $enterPriceDomain = [];
        $mailBlackList = $this->getMailBlackList();
        foreach ($allEmails as $email)
        {
            if (array_key_exists($email, $mailBlackList['email']))
            {
                $this->logCounterRule([$email], self::RULE_MAIL_BLACK_LIST);
                return false;
            }

            $domain = \EmailUtil::getDomain($email);

            if (array_key_exists('@' . $domain, $mailBlackList['domain']))
            {
                $this->logCounterRule([$email], self::RULE_MAIL_BLACK_LIST);
                return false;
            }

            if (CommonDomain::check($domain))
                continue;

            $enterPriceDomain[] = $domain;
        }

        $collegeCompanyDomain = [];
        $privateCompanyDomain = [];
        $privateEmailByMainDomain = [];
        if ($enterPriceDomain)
        {
            $list = new EmailIdentityList($this->mail->getUserId());
            $list->setDomains($enterPriceDomain);
            $list->setEmailType(EmailIdentityList::EMAIL_TYPE_PRIVATE_CUSTOMER);
            $list->setFields('email, domain, customer_user');
            $list->setCheckPoolDuplicateSwitchFlag(true);
            $identityList = $list->find();

            foreach ($identityList as $item)
            {
                $customerUserIds = PgsqlUtil::trimArray($item['customer_user']);

                if (!in_array($this->mail->getUserId(), $customerUserIds))
                {
                    $collegeCompanyDomain[] = $item['domain'];
                    continue;
                }

                $privateCompanyDomain[] = $item['domain'];

                if ($mainDomain == $item['domain'])
                    $privateEmailByMainDomain[] = $item['email'];
            }
        }

        //邮件中出现同事的私海已建档客户的企业邮箱后缀，且该企业邮箱后缀未被本人私海建档 整封邮件剔除
        $diffDomains = array_diff($collegeCompanyDomain, $privateCompanyDomain);
        if (!empty($diffDomains))
        {
            $diffDomains = array_unique($diffDomains);
            $this->logCounterRule($diffDomains, self::RULE_COLLEGE_CUSTOMER_DOMAIN);
            return false;
        }

        // region 过滤邮箱--start
        //私海已建档
        if ($archivedEmails = array_intersect($privateEmails, $allEmails))
        {
            $allEmails = array_diff($allEmails, $archivedEmails);
            $this->logCounterRule($archivedEmails, self::RULE_PRIVATE_CUSTOMER);

            if (!$this->processCheck($allEmails))
                return false;
        }

        // 公海 从处理邮箱中剔除
        if ($publicList = array_intersect($publicEmails, $allEmails))
        {
            $allEmails = array_diff($allEmails, $publicList);
            $this->logCounterRule($publicList, self::RULE_PUBLIC_CUSTOMER);

            if (!$this->processCheck($allEmails))
                return false;
        }

        //建档黑名单
        $customerBlackList = $this->getCustomerBlackList();
        if ($blackList = array_intersect(array_keys($customerBlackList['email']), $allEmails))
        {
            $allEmails = array_diff($allEmails, $blackList);
            $this->logCounterRule($blackList, self::RULE_BLACK_LIST);

            if (!$this->processCheck($allEmails))
                return false;
        }

        //企业组织架构内的账号邮箱
        if ($accountList = array_intersect($this->getUserEmails(), $allEmails))
        {
            $allEmails = array_diff($allEmails, $accountList);
            $this->logCounterRule($accountList, self::RULE_COLLEGE_ACCOUNT);

            if (!$this->processCheck($allEmails))
                return false;
        }

        //企业内所有已绑定的邮箱
        if ($collegeEmailList = array_intersect($this->getUserMailEmails(), $allEmails))
        {
            $allEmails = array_diff($allEmails, $collegeEmailList);
            $this->logCounterRule($collegeEmailList, self::RULE_COLLEGE_EMAIL);

            if (!$this->processCheck($allEmails))
                return false;
        }

        //通讯录
        if ($contactsEmails = array_intersect($this->getContacts(), $allEmails))
        {
            $allEmails = array_diff($allEmails, $contactsEmails);
            $this->logCounterRule($contactsEmails, self::RULE_CONTACT);

            if (!$this->processCheck($allEmails))
                return false;
        }

        //过滤不采纳建档建议
        if ($filterEmails = array_intersect($this->getCustomerAdviceFilterEmail(), $allEmails))
        {
            $allEmails = array_diff($allEmails, $filterEmails);
            $this->logCounterRule($filterEmails, self::RULE_CUSTOMER_ADVICE_NOT_ACCEPT);

            if (!$this->processCheck($allEmails))
                return false;
        }
        //endregion 过滤邮箱--end

        //region 过滤域名--start
        $collegeEmails = array_unique(array_merge($this->getUserMailEmails(), $this->getUserEmails()));
        $collegeDomainList = [];
        foreach ($collegeEmails as $email)
        {
            $domain = \EmailUtil::getDomain($email);
            $collegeDomainList[$domain] = 1;
        }

        foreach ($allEmails as $email)
        {
            $domain = \EmailUtil::getDomain($email);

            //与主域名不一致
            if ($domain != $mainDomain)
            {
                $allEmails = array_diff($allEmails, [$email]);
                $this->logCounterRule([$email], self::RULE_NOT_SAME_WITH_MAIN_DOMAIN);

                if (!$this->processCheck($allEmails))
                    return false;
            }

            //建档黑名单
            if (array_key_exists("@$domain", $customerBlackList['domain']))
            {
                $allEmails = array_diff($allEmails, [$email]);
                $this->logCounterRule([$email], self::RULE_BLACK_LIST);

                if (!$this->processCheck($allEmails))
                    return false;
            }

            //与公司内账号、绑定邮箱拥有相同后缀的邮箱
            if (array_key_exists($domain, $collegeDomainList) && !CommonDomain::check($domain))
            {
                $this->logCounterRule([$email], self::RULE_COLLEGE_DOMAIN);
                $allEmails = array_diff($allEmails, [$email]);

                if (!$this->processCheck($allEmails))
                    return false;
            }
        }
        //endregion 过滤域名--end

        $archiveList = [];
        $privateList = [];
        //$allEmails均未建档：找出私海含有该企业邮箱后缀的company_id(order_time desc)
        if (!$companyId && !empty($privateEmailByMainDomain) && !CommonDomain::check($mainDomain))
        {
            $list = new CustomerList($this->clientId);
            $list->setUserId($this->mail->getUserId());
            $list->setEmail($privateEmailByMainDomain);
            $list->setAlias('a');
            $list->setFields('a.company_id');
            $list->setJoin((new CompanyFilter($this->clientId, 'b')), BaseList::JOIN_TYPE_LEFT, ['company_id', 'company_id']);
            $list->setOrderBy('b.order_time');
            $list->setOrder('desc');
            $list->setIsArchive(1);
            $list->setLimit(1);
            $list->setCheckPoolDuplicateSwitchFlag(true);
            $companyIdList = $list->find();
            $companyId = !empty($companyIdList) ? (reset($companyIdList))['company_id'] : null;
        }

        if ($companyId)
        {
            $privateList[$companyId] = array_values($allEmails);
        }
        else
        {
            $archiveList[] = array_values($allEmails);
        }

        return [
            $archiveList,
            $privateList,
            (\common\library\setting\library\origin\Origin::CLASSIFY_FILTER_MAIL_TYPE_MAP[$this->lastMailClassifyType] ?? 0)
        ];
    }

    protected function processCheck($all)
    {
        if (empty($all))
            return false;

        return true;
    }

    protected function mailTagCheck()
    {
        \LogUtil::info("mailId:{$this->mail->mail_id} process: mailTagCheck");

        if ($this->lastInquiryType !== null)
        {
            return $this->lastClassifyLog->mail_tag_id = \common\library\setting\library\tag\Tag::MAIL_TAG_INQUIRY;
        }

        switch (strval($this->lastMailClassifyType)) {
            case self::MAIL_CLASSIFY_TYPE_DEVELOP:
                $mailTagId = \common\library\setting\library\tag\Tag::MAIL_TAG_DEVELOPMENT;
                break;
            case self::MAIL_CLASSIFY_TYPE_INQUIRY:
                $mailTagId = \common\library\setting\library\tag\Tag::MAIL_TAG_INQUIRY;
                break;
            case self::MAIL_CLASSIFY_TYPE_QUOTATION:
                $mailTagId = \common\library\setting\library\tag\Tag::MAIL_TAG_QUOTATION;
                break;
            case self::MAIL_CLASSIFY_TYPE_PO:
                $mailTagId = \common\library\setting\library\tag\Tag::MAIL_TAG_PO;
                break;
            case self::MAIL_CLASSIFY_TYPE_SAMPLE_PI:
                $mailTagId = \common\library\setting\library\tag\Tag::MAIL_TAG_SAMPLE_PI;
                break;
            case self::MAIL_CLASSIFY_TYPE_PI:
                $mailTagId = \common\library\setting\library\tag\Tag::MAIL_TAG_PI;
                break;
            case self::MAIL_CLASSIFY_TYPE_PL:
                $mailTagId = \common\library\setting\library\tag\Tag::MAIL_TAG_PL;
                break;
            case self::MAIL_CLASSIFY_TYPE_CI:
                $mailTagId = \common\library\setting\library\tag\Tag::MAIL_TAG_CI;
                break;
            case self::MAIL_CLASSIFY_TYPE_BS:
                $mailTagId = \common\library\setting\library\tag\Tag::MAIL_TAG_BS;
                break;
            case self::INQUIRY_TYPE_PERSONAL:
                $mailTagId = \common\library\setting\library\tag\Tag::MAIL_TAG_PERSONAL_INQUIRY;
                break;
            default:
                $mailTagId = 0;
                break;
        }

        $this->captureCardLog->tag_id = $mailTagId;
        $this->captureCardLog->classify_type = $this->lastMailClassifyType;
        return $this->lastClassifyLog->mail_tag_id = $mailTagId;
    }

    protected function formatVersion($version)
    {
        return substr_replace($version, '', strrpos($version, '.'), 1);
    }

    protected function formatVersionType($type)
    {
        switch (strval($type))
        {
            case 'rule':
                return 1;
                break;
            case 'model':
                return 2;
                break;
            default:
                return 0;
                break;
        }
    }

    protected function invoiceExtract($ext)
    {
        \LogUtil::info("mailId:{$this->mail->mail_id} process: invoiceExtract");
        if (!$this->mail->attach_flag)
            return false;

        if (! in_array($this->lastMailClassifyType, [self::MAIL_CLASSIFY_TYPE_PI, self::MAIL_CLASSIFY_TYPE_SAMPLE_PI, self::MAIL_CLASSIFY_TYPE_CI]))
        {
            return false;
        }

        if ($this->reuseJavaData && !$this->lastClassifyLog->getIsNewRecord() && $this->lastClassifyLog->file_extract_call > 0)
        {
            $this->invoiceExtraData = json_decode(snappy_uncompress($this->lastClassifyLog->file_extract_data), true);
        }
        else
        {
            $fileIds = $this->mail->getAttachmentList();

            if (empty($fileIds))
                return false;

            $fileIds = \common\library\file\Helper::filterFile($fileIds, $ext);

            if (empty($fileIds))
                return false;


            $fileExtract = new FileExtract($this->mail);
            if( $this->simulation )
                $fileExtract->setAccessLog(true);

            $fileExtract->setAsync(false);
            $data = $fileExtract->extract([]);
            if( $data === false)
            {
                $this->lastClassifyLog->file_extract_call = -1;
                return false;
            }

            $this->invoiceExtraData = $data['data'];

            $this->lastClassifyLog->file_extract_call = 1;
            $this->lastClassifyLog->file_extract_data = snappy_compress(json_encode($this->invoiceExtraData));
        }

        $result = array_filter($this->invoiceExtraData['structInfo']??[], function($elem){
            return !empty($elem['data']);
        });

        $result = array_values($result);
        $extractData = $result[0] ?? [];

        // Capture 记录 PI、CI附件分析数据
        if (count($extractData)) {
            $currency = $extractData['data']['currencyType'] ?? '';
            $extractFileId = $extractData['fileId'] ?? 0;
            $extractPiNo = $extractData['data']['piNumber'] ?? '';
            $productAmount = $extractData['data']['total'] ?? '';
            $templateNo = $extractData['data']['tmpl_no'] ?? '';
            $extractType = $extractData['typeName'] ?? '';

            if (!empty($extractFileId) && in_array($extractType, [self::MAIL_CLASSIFY_TYPE_PI, self::MAIL_CLASSIFY_TYPE_CI])) {
                $capture = new CaptureCardLog($this->clientId, $extractFileId);
                $capture->biz_type = CaptureCard::BIZ_TYPE_FILE;
                $capture->type = CaptureCard::REASON_TYPE_BY_ATTACH_CONTENT;
                $capture->currency = $currency;

                if ($this->lastMailClassifyType == self::MAIL_CLASSIFY_TYPE_CI) {
                    $capture->tag_id = \common\library\setting\library\tag\Tag::MAIL_TAG_CI;
                    $capture->classify_type = self::MAIL_CLASSIFY_TYPE_CI;
                    $capture->ci_amount = $productAmount;
                } else {
                    $capture->tag_id = \common\library\setting\library\tag\Tag::MAIL_TAG_PI;
                    $capture->classify_type = self::MAIL_CLASSIFY_TYPE_PI;
                    $capture->pi_no = $extractPiNo;
                    $capture->pi_amount = $productAmount;
                    $capture->template_no = $templateNo;
                }

                $capture->save();
            }
        }

        return $result ?: false;
    }

    protected function inquiry($rules)
    {
        \LogUtil::info("mailId:{$this->mail->mail_id} process: inquiry");
        if ($this->mail->isJunk())
        {
            $this->logCounterRule([], self::RULE_MAIL_FOLDER_JUNK_ID);
            return false;
        }

        if ($this->mail->bounced_mail_id)
        {
            $this->logCounterRule([], self::RULE_MAIL_BOUNCE_FLAG);
            return false;
        }

        if (!$this->mail->isReceiverType())
            return false;

        $type = null;
        $replyTo = null;

        foreach ($rules as $rule)
        {
            $sender = strtolower(\EmailUtil::findEmail($this->mail->sender));
            $replyTo = strtolower(\EmailUtil::findEmail($this->mail->reply_to));

            $domainCheck = $rule['domain'] ?? null;
            if ($domainCheck)
            {
                $senderDomain = substr($sender, strpos($sender, '@') + 1);

                if ($domainCheck != $senderDomain)
                    continue;
            }

            $senderCheck = $rule['sender'] ?? null;
            if ($senderCheck)
            {
                if (is_array($senderCheck))
                {
                    if (!in_array($sender, $senderCheck))
                        continue;
                }
                else
                {
                    if (strcasecmp($senderCheck, $sender) !== 0)
                        continue;
                }
            }

            $senderPattern = $rule['senderPattern'] ?? null;
            if ($senderPattern)
            {
                $senderPattern = $senderPattern ? implode('|', $senderPattern) : '';
                $senderPattern = $senderPattern ? '/' . $senderPattern . '/i' : '';
                if (!empty($senderPattern) && !preg_match($senderPattern, $sender, $matches))
                    continue;
            }

            $replyToCheck = $rule['reply_to'] ?? null;
            if ($replyToCheck)
            {
                if (empty($replyTo))
                    continue;
            }

            $replyToExcludeDomain = $rule['reply_to_exclude_domain'] ?? null;
            if ($replyToExcludeDomain)
            {
                if (empty($replyTo))
                    continue;

                $replyToDomain = substr($replyTo, strpos($replyTo, '@') + 1);

                if ($replyToDomain == $replyToExcludeDomain)
                    continue;

            }

            $excludeSender = $rule['exclude_sender'] ?? null;
            if ($excludeSender)
            {
                if (is_array($excludeSender))
                {
                    if (in_array($sender, $excludeSender))
                        continue;
                }
                else
                {
                    if (strcasecmp($excludeSender, $sender) !== 0)
                        continue;
                }
            }

            $subject = $rule['subject'] ?? [];
            if (!empty($subject))
            {
                $matchSubject = false;
                foreach ($subject as $elem)
                {
                    if (strpos($this->mail->subject, $elem) === false)
                        continue;

                    $matchSubject = true;
                    break;
                }

                if ($matchSubject == false)
                    continue;
            }

            $type = $rule['name'];
            $this->lastInquiryType = $type;
            $this->lastClassifyLog->inquiry_classify = $type;
            $this->lastClassifyLog->mail_tag_id = \common\library\setting\library\tag\Tag::MAIL_TAG_INQUIRY;
            $this->lastClassifyLog->recognition_mode = self::RECOGNITION_MODE_INQUIRY_PLATFORM;

            $this->captureCardLog->inquiry_classify = $type;
            $this->captureCardLog->tag_id = \common\library\setting\library\tag\Tag::MAIL_TAG_INQUIRY;
            $this->captureCardLog->type = CaptureCard::REASON_TYPE_BY_MAIL_SUBJECT;
            return $type;
        }
        \LogUtil::info("mailId:{$this->mail->mail_id} process: inquiry  false");
        return false;
    }

    protected function getMailBlackList()
    {
        if ($this->mailBlackList !== null)
            return $this->mailBlackList;

        $list = new MailBlackList($this->clientId, $this->userId);
        $list = $list->find();

        $result = [
            'email' => [],
            'domain' => [],
        ];

        foreach ($list as $elem)
        {
            if ($elem['black_type'] == 1)
                $result['email'][strtolower(trim($elem['black_content']))] = 1;
            else if ($elem['black_type'] == 2)
                $result['domain'][strtolower(trim($elem['black_content']))] = 1;
        }

        $this->mailBlackList = $result;

        return $this->mailBlackList;

    }

    protected function getCustomerBlackList()
    {
        if ($this->customerBlackList !== null)
            return $this->customerBlackList;

        $listModel = new CustomerBlacklistList($this->clientId);
        $list = $listModel->find();

        $result = [
            'email' => [],
            'domain' => [],
        ];

        foreach ($list as $elem)
        {
            if ($elem['type'] == 1)
                $result['email'][strtolower(trim($elem['value']))] = 1;
            else if ($elem['type'] == 2)
                $result['domain'][strtolower(trim($elem['value']))] = 1;
        }

        $this->customerBlackList = $result;

        return $this->customerBlackList;
    }

    protected function getUserEmails($emailOnly = true)
    {
        if ($this->userEmails !== null)
        {
            if ($emailOnly)
                return array_keys($this->userEmails);

            return $this->userEmails;
        }


        $list = new UserList();
        $list->setClientId($this->clientId);
        $list->setEnableFlag(1);
        $list->setFields(['email','user_id']);
        $userEmail = array_column($list->find(), 'user_id','email');

        $this->userEmails = $userEmail;

        if ($emailOnly)
            return array_keys($this->userEmails);

        return $this->userEmails;
    }

    protected function getUserMailEmails($emailOnly = true)
    {
        if ($this->userMailEmails !== null)
        {
            if ($emailOnly)
                return array_keys($this->userMailEmails);

            return $this->userMailEmails;
        }

        $list = new \UserMailList();
        $list->setClientId($this->clientId);
        $this->userMailEmails = array_column($list->find(), 'user_id', 'email_address');

        if ($emailOnly)
            return array_keys($this->userMailEmails);

        return $this->userMailEmails;
    }

    protected function getContacts()
    {
        if ($this->contactsEmails !== null)
            return $this->contactsEmails;

        // 通讯录 非客户
        $customerContactGroup = Helper::getNotCustomerGroup($this->clientId, $this->userId);

        $publicGroup = [];
        $privateGroup = [];

        foreach ($customerContactGroup as $group)
        {
            if ($group['user_id'])
                $privateGroup[] = $group['group_id'];
            else
                $publicGroup[] = $group['group_id'];
        }

        // 名字带客户
        $list = new ContactList($this->clientId);
        $list->setUserId($this->userId);
        $list->setKeyword('客户');
        $list->setKeywordField('remark');
        $list->setKeywordLike(false);
        $list->setFields(['mail']);
        $contacts = $list->find();

        if (!empty($publicGroup))
        {
            $list = new ContactList($this->clientId);
            $list->setUserId(0);
            $list->setGroupId($publicGroup);
            $list->setFields(['mail']);
            $contacts = array_merge($contacts, $list->find());
        }


        if (!empty($privateGroup))
        {
            $list = new ContactList($this->clientId);
            $list->setUserId($this->userId);
            $list->setGroupId($privateGroup);
            $list->setFields(['mail']);
            $contacts = array_merge($contacts, $list->find());
        }

        $this->contactsEmails = array_column($contacts, 'mail');

        return $this->contactsEmails;
    }

    /**
     * @throws \ProcessException
     */
    protected function customerCheck()
    {
        $clientId = $this->mail->getMailClientId();
        $allReceive = $this->mail->getContactEmails();
        if (empty($allReceive))
            return false;

        $listObj = new CustomerList($clientId);
        $listObj->setEmail($allReceive);
//        $listObj->setUserId($this->mail->user_id);
        $listObj->setFields(['company_id', 'customer_id', 'user_id']);
        $listObj->setCheckPoolDuplicateSwitchFlag(true);
        $customerList = $listObj->find();

        if (empty($customerList))
            return false;

        $companyCustomerIdMap = array_reduce($customerList, function ($carry, $item) {
            $carry[$item['company_id']]['customer_ids'][] = $item['customer_id'];
            $carry[$item['company_id']]['user_id'] = PgsqlUtil::trimArray($item['user_id']);
            return $carry;
        }, []);

        $this->lastCompanyInfo = [];
        foreach ($companyCustomerIdMap as $companyId => $item) {
            $this->setCompanyInfo($companyId, $item['customer_ids']);
        }

        $this->processCompany = true;

        return $companyCustomerIdMap;
    }

    protected function getCustomerAdviceFilterEmail()
    {
        if ($this->adviceFilterEmails !== null) {
            return $this->adviceFilterEmails;
        }

        $list = new CustomerAdviceList($this->mail->getUserId());
        $list->setFields('email');
        $list->setNotAcceptType(AiCustomerAdvice::NOT_ACCEPT_LIST);
        $adviceList = $list->find();

        $this->adviceFilterEmails = array_column($adviceList, 'email');

        return $this->adviceFilterEmails;
    }

    /**
     * @param $system
     * @param $b2b
     * @param $b2c
     * @param $keyword
     * @param $folder
     * @return bool
     */
    protected function validateMail($system, $b2b, $b2c, $keyword, $folder)
    {
        \LogUtil::info("mailId:{$this->mail->mail_id} process: validateMail");

        // 检查邮件的原始文件夹名称是否属于广告/垃圾邮件
        $imapFolderName = strtolower(\common\library\mail\Helper::getImapFolderName($this->mail->getMailClientId(), $this->mail->imap_folder_id));
        if (!empty($imapFolderName)) {
            foreach ($folder['keyword'] as $item) {
                if (strpos($imapFolderName, strtolower($item)) !== false) {
                    $this->logCounterRule([], self::RULE_FILTER_IMAP_FOLDER);
                    \LogUtil::info("mailId:{$this->mail->mail_id} IMAP folder 关键词");
                    return false;
                }
            }
        }

        if ($this->mail->isJunk())
        {
            $this->logCounterRule([], self::RULE_MAIL_FOLDER_JUNK_ID);
            \LogUtil::info("mailId:{$this->mail->mail_id} 垃圾邮件 ");
            return false;
        }

        if ($this->mail->bounce_flag)
        {
            $this->logCounterRule([], self::RULE_MAIL_BOUNCE_FLAG);
            \LogUtil::info("mailId:{$this->mail->mail_id} 退信");
            return false;
        }

        // 关键词
        foreach ($keyword['subject'] as $elem)
        {
            if (strpos($this->mail->subject, $elem) !== false)
            {
                $this->logCounterRule([], self::RULE_SUBJECT);
                \LogUtil::info("mailId:{$this->mail->mail_id} subject 关键词");
                return false;
            }
        }

        $plainText = $this->mail->getPlainText();
        foreach ($keyword['content'] as $elem)
        {
            if (strpos($plainText, $elem) !== false)
            {
                $this->logCounterRule([], self::RULE_CONTENT);
                \LogUtil::info("mailId:{$this->mail->mail_id} content 关键词");
                return false;
            }
        }

        if ($this->mail->getMailType() == \Mail::MAIL_TYPE_RECEIVE)
        {
            // 尼日利亚相关ip
            if (($ip = $this->mail->getSenderIp()) && IP::getInstance()->find($ip)['CountryCode'] == 'NG')
            {
                $this->logCounterRule([], self::RULE_RISK);
                \LogUtil::info("mailId:{$this->mail->mail_id} 尼日利亚ip");
                return false;
            }

            $email = \EmailUtil::findEmail($this->mail->sender);

            if (empty($email)) // 没有发件人的收件，直接跳过 例子：clientId=14455 mailId=8838237314
            {
                \LogUtil::info('empty sender');
                return false;
            }

            $domain = \EmailUtil::getDomain($email);

            $prefix = \EmailUtil::getEmailPrefix($email);

            foreach ($system['keyword'] as $keyword)
            {
                if (strpos($email, $keyword) !== false)
                {
                    $this->logCounterRule([$email], self::RULE_SYSTEM_EMAIL_KEYWORD);
                    \LogUtil::info("mailId:{$this->mail->mail_id} 邮箱关键词");
                    return false;
                }
            }

            // 小满系统邮件
            if (in_array($email, $system['email']))
            {
                $this->logCounterRule([$email], self::RULE_SYSTEM_EMAIL);
                \LogUtil::info("mailId:{$this->mail->mail_id} 小满系统邮件");
                return false;
            }

            if (in_array($domain, $system['domain']))
            {
                $this->logCounterRule([$email], self::RULE_SYSTEM_DOMAIN);
                \LogUtil::info("mailId:{$this->mail->mail_id} domain");
                return false;
            }

            if (in_array($prefix, $system['prefix'])) {
                $this->logCounterRule([$email], self::RULE_SYSTEM_PREFIX);
                \LogUtil::info("mailId:{$this->mail->mail_id} system prefix");
                return false;
            }

            // 外贸平台
            if (in_array($email, $b2b['email'])) {

                $this->logCounterRule([$email], self::RULE_B2B_EMAIL);
                \LogUtil::info("mailId:{$this->mail->mail_id} b2b email");
                return false;
            }

            foreach ($b2b['domain'] as $filterDomain) {
                if (strpos($domain, $filterDomain) !== false)
                {
                    $this->logCounterRule([$email], self::RULE_B2B_DOMAIN);
                    \LogUtil::info("mailId:{$this->mail->mail_id} b2b domain");
                    return false;
                }
            }

            // B2C
            if (in_array($email, $b2c['email']))
            {
                $this->logCounterRule([$email], self::RULE_B2C_EMAIL);
                \LogUtil::info("mailId:{$this->mail->mail_id} b2c email");
                return false;
            }

            foreach ($b2c['domain'] as $filterDomain) {
                if (strpos($domain, $filterDomain) !== false)
                {
                    $this->logCounterRule([$email], self::RULE_B2C_DOMAIN);
                    \LogUtil::info("mailId:{$this->mail->mail_id} b2c domain");
                    return false;
                }
            }

            //发件人是否为当前账号、当前绑定邮箱
            $emailVsUser = $this->getUserEmails(false);
            if (array_key_exists($email, $emailVsUser))
            {
                $this->logCounterRule([$email], self::RULE_COLLEGE_ACCOUNT);
                \LogUtil::info("mailId:{$this->mail->mail_id} 发件人账号");
                return false;
            }

            $userEmailVsUser = $this->getUserMailEmails(false);
            if (array_key_exists($email, $userEmailVsUser))
            {
                $this->logCounterRule([$email], self::RULE_COLLEGE_EMAIL);
                \LogUtil::info("mailId:{$this->mail->mail_id} 发件人邮箱");
                return false;
            }

            $mailBlackList = $this->getMailBlackList();

            if (array_key_exists($email, $mailBlackList['email']))
            {
                $this->logCounterRule([$email], self::RULE_MAIL_BLACK_LIST);
                \LogUtil::info("mailId:{$this->mail->mail_id} 黑名单");
                return false;
            }

            $domain = '@' . $domain;
            if (array_key_exists($domain, $mailBlackList['domain']))
            {
                $this->logCounterRule([$email], self::RULE_MAIL_BLACK_LIST);
                \LogUtil::info("mailId:{$this->mail->mail_id} domain");
                return false;
            }

            //return true;
        }

        if($this->mail->isReceiverType()) {
            $contactEmails = $this->mail->getSenderEmail();
        }else{
            $receiver = \EmailUtil::findAllMailAddress($this->mail->receiver);
            $contactEmails = $receiver ? $receiver : [];
        }

        //解析当前联系人邮箱和邮箱后缀

        if(empty($contactEmails)) {
            return false;
        }

        $userMailEmails =  $this->getUserMailEmails(true);
        //全部为该公司的绑定邮箱判断为同事邮件
        $noBandEmails  =  array_diff($contactEmails, $userMailEmails);
        if (!empty($contactEmails) && empty($noBandEmails)) {
            $this->logCounterRule($contactEmails, self::RULE_COLLEGE_EMAIL);
            \LogUtil::info("mailId:{$this->mail->mail_id} 全部为该公司的绑定邮箱");
            return false;
        }
        $clientEmails = $this->getUserEmails(true);

        //剩余的邮箱为组织架构邮箱 则判定为同事邮件
        $noClientEmails = array_diff($noBandEmails, $clientEmails);

        if (empty($noClientEmails)) {
            $this->logCounterRule($contactEmails, self::RULE_COLLEGE_EMAIL);
            \LogUtil::info("mailId:{$this->mail->mail_id} 全部为该公司企业架构的邮箱");
            return false;
        }

        $clientEmailsDomain = [];
        foreach ( $clientEmails as  $email) {
            $start = strpos($email, '@');
            if ($start !== false) {
                $clientEmailsDomain[] = substr($email, $start+1);
            }
        }

        $noClientEmailsDomain = [];
        foreach ( $noClientEmails as  $email) {
            $start = strpos($email, '@');
            if ($start !== false) {
                $noClientEmailsDomain[] = substr($email, $start+1);
            }
        }
        //如果剩余的邮箱的后缀全部在 绑定和企业架构的邮箱后缀则为同事邮件
        if(empty(array_diff($noClientEmailsDomain,array_diff($clientEmailsDomain,        CommonDomain::$commonDomain)))) {
            $this->logCounterRule($contactEmails, self::RULE_COLLEGE_EMAIL);
            \LogUtil::info("mailId:{$this->mail->mail_id} 剩余的邮箱的后缀全部在绑定和企业架构的邮箱后缀");
            return false;
        }

        \LogUtil::info("mailId:{$this->mail->mail_id} validate true");
        return true;
    }

    protected function classifyMailType(
        $prob,
        $types,
        $piAttachPattern,
        $piContent,
        $sampleAttachPattern,
        $sampleAssociatedWord1,
        $sampleAssociatedWord2,
        $quotationAttach,
        $quotationSubject,
        $quotationSubjectFilter,
        $quotationContent,
        $quotationContentPattern,
        $quotationContentFilter,
        $quotationContentPatternFilter,
        $quotationAssociatedWord1,
        $quotationAssociatedWord2,
        $bsAssociatedWord1,
        $bsAssociatedWord2,
        $bsAttachPattern,
        $ciContent,
        $ciAttachPattern,
        $plContent,
        $plAttachPattern,
        $personalInquiryContentFilter,
        $personalInquirySubjectFilter
    )
    {
        \LogUtil::info("mailId:{$this->mail->mail_id}".__FUNCTION__);
        $db = \ProjectActiveRecord::getDbByClientId($this->mail->client_id);

        $files = [];
        $fileNames = [];
        if ($this->mail->attach_flag)
        {
            $fileIds = $this->mail->getAttachmentList();

            if (!empty($fileIds))
            {
                $fileIds = implode(',',$fileIds);
                $files = $db->createCommand("SELECT file_id,file_name from tbl_upload_file where  file_id in ({$fileIds})")->queryAll(true);

                if(!empty($files)) {
                    $fileNames = array_column($files,'file_name');
                }
            }
        }

        $this->mailAttachList = $files;
        $customerList = new CustomerList($this->clientId);
        $customerList->setUserId($this->mail->getUserId());


        //调用接口判断是首次会话
        $api = new InnerApi('mail_classify_extract_data');
        if ($this->simulation || $this->userId == 55258608)
            $api->setAccessLog(true);
        $data = $api->call('current_session', [
            'mail_id' => $this->mailId, 'user_mail_id' => $this->userMailId,
        ]);

        //一下判断的正文只去当前会话的内容
        //$data['data']['structInfo']['data'] 会有返回空字符串的情况（===false时才代表首次会话）
        $isFirstSession = true;
        if(isset($data['data']['structInfo']['data']) && $data['data']['structInfo']['data'] !== false) {
            $isFirstSession = false;
            $content =  $data['data']['structInfo']['data'];
        }else{
            $content = $this->filterMailContent($this->mail->getContent());
        }

        $this->lastClassifyLog->mail_classify_type = '';
        $this->lastClassifyLog->recognition_mode = 0;

        if ($this->mail->isReceiverType()) {
            //收件类型的判断

            $customerList->setEmail($this->mail->getSenderEmail());
            $privateCustomerCount = $customerList->count();
            //识别水单逻辑
            if(!empty($fileNames) && $privateCustomerCount) {
                //content  匹配规则
                $stringPattern1 = '('.implode('|',$bsAssociatedWord1).')';
                $stringPattern2 = '('.implode('|',$bsAssociatedWord2).')';

                $pattern1 = '/\b'.$stringPattern1.'\w*\s(\w*\s+){0,3}\b'.$stringPattern2.'\w*/i';
                $pattern2 = '/\b'.$stringPattern2.'\w*\s(\w*\s+){0,3}\b'.$stringPattern1.'\w*/i';

                if (preg_match($pattern1, $content, $matches) || preg_match($pattern2, $content, $matches) ) {
                    $this->captureCardLog->classify_type = self::MAIL_CLASSIFY_TYPE_BS;
                    $this->captureCardLog->type = CaptureCard::REASON_TYPE_BY_MAIL_CONTENT;

                    $this->lastMailClassifyType = self::MAIL_CLASSIFY_TYPE_BS;
                    $this->lastClassifyLog->recognition_mode = self::RECOGNITION_MODE_BS_BY_CONTENT;
                    return $this->lastClassifyLog->mail_classify_type = $this->lastMailClassifyType;
                }


                $bsAttachPattern = $bsAttachPattern ? implode('|', $bsAttachPattern) : '';
                $bsAttachPattern = $bsAttachPattern ? '/' . $bsAttachPattern . '/i' : '';
                //附件 匹配规则
                foreach ($fileNames as $key => $fileName)
                {
                    if (!empty($bsAttachPattern) && preg_match($bsAttachPattern, $fileName, $matches))
                    {
                        $this->captureCardLog->classify_type = self::MAIL_CLASSIFY_TYPE_BS;
                        $this->captureCardLog->type = CaptureCard::REASON_TYPE_BY_ATTACH_NAME;

                        $this->lastMailClassifyType = self::MAIL_CLASSIFY_TYPE_BS;
                        $this->lastClassifyLog->mail_classify_type = $this->lastMailClassifyType;
                        $this->lastClassifyLog->recognition_mode = self::RECOGNITION_MODE_BS_BY_ATTACH;
                        return $this->lastMailClassifyType;
                    }
                }
            }


            //识别为询盘邮件(个人)逻辑
            //不是第一次会话

            if(!empty($this->mail->reply_to_mail_id) || !$isFirstSession ) {
                return false;
            }

            $personalInquiryContentFilterPattern = $personalInquiryContentFilter ? implode('|', $personalInquiryContentFilter) : '';
            $personalInquiryContentFilterPattern = $personalInquiryContentFilterPattern ? '/' . $personalInquiryContentFilterPattern . '/i' : '';

            //判断正文 过滤规则
            if (preg_match($personalInquiryContentFilterPattern, $content, $matchs)){
                return false;
            }

            if (!empty($this->mail->subject)) {

                $personalInquirySubjectFilterPattern = $personalInquirySubjectFilter ? implode('|', $personalInquirySubjectFilter) : '';
                $personalInquirySubjectFilterPattern = $personalInquirySubjectFilterPattern ? '/' . $personalInquirySubjectFilterPattern . '/i' : '';

                if (preg_match($personalInquirySubjectFilterPattern, $this->mail->subject, $matchs) ) {
                    return false;
                }
            }
        } else {
            //发件类型的判断

            if ($this->mail->isExposeSub() || $this->mail->isExpose())
                return false;

            //识别pl
            if(!empty($fileNames)) {

                $customerList->setEmail($this->mail->getAllReceiverEmails());
                $privateCustomerCount = $customerList->count();
                if($privateCustomerCount) {
                    //判断附件
                    $plAttachPattern = $plAttachPattern ? implode('|', $plAttachPattern) : '';
                    $plAttachPattern = $plAttachPattern ? '/' . $plAttachPattern . '/i' : '';

                    $matchFiles = [];
                    foreach ($fileNames as $key => $fileName)
                    {
                        if (!empty($plAttachPattern) && preg_match($plAttachPattern, $fileName, $matches))
                        {
                            $matchFiles[] = $files[$key];
                        }
                    }

                    if (count($matchFiles)) {
                        //附件名符合规则继续调用接口判断附件内容
                        $isPl = $this->mailFileRecognition($matchFiles,self::MAIL_CLASSIFY_TYPE_PL);
                        if($isPl) {
                            $this->captureCardLog->classify_type = self::MAIL_CLASSIFY_TYPE_PL;
                            $this->captureCardLog->type = CaptureCard::REASON_TYPE_BY_ATTACH_CONTENT;

                            $this->lastMailClassifyType = self::MAIL_CLASSIFY_TYPE_PL;
                            $this->lastClassifyLog->mail_classify_type = $this->lastMailClassifyType;
                            $this->lastClassifyLog->recognition_mode = self::RECOGNITION_MODE_PL_BY_ATTACH;
                            return $this->lastMailClassifyType;
                        }
                    }

                    if ( \Util::striposArray($content, $plContent) !== false) {
                        //如果内容包含则条用接口判断附件内容

                        $isPl = $this->mailFileRecognition($files,self::MAIL_CLASSIFY_TYPE_PL);

                        if($isPl) {
                            $this->captureCardLog->classify_type = self::MAIL_CLASSIFY_TYPE_PL;
                            $this->captureCardLog->type = CaptureCard::REASON_TYPE_BY_ATTACH_CONTENT;

                            $this->lastMailClassifyType = self::MAIL_CLASSIFY_TYPE_PL;
                            $this->lastClassifyLog->mail_classify_type = $this->lastMailClassifyType;
                            $this->lastClassifyLog->recognition_mode = self::RECOGNITION_MODE_PL_BY_ATTACH;
                            return $this->lastMailClassifyType;
                        }
                    }


                    //识别ci
                    //判断附件
                    $ciAttachPattern = $ciAttachPattern ? implode('|', $ciAttachPattern) : '';
                    $ciAttachPattern = $ciAttachPattern ? '/' . $ciAttachPattern . '/i' : '';

                    $matchFiles = [];
                    foreach ($fileNames as $key => $fileName)
                    {
                        if (!empty($ciAttachPattern) && preg_match($ciAttachPattern, $fileName, $matches))
                        {
                            $matchFiles[] = $files[$key];
                        }
                    }

                    if (count($matchFiles)) {
                        //继续判断附件
                        $isCi = $this->mailFileRecognition($matchFiles,self::MAIL_CLASSIFY_TYPE_CI);

                        if($isCi) {
                            $this->captureCardLog->classify_type = self::MAIL_CLASSIFY_TYPE_CI;
                            $this->captureCardLog->type = CaptureCard::REASON_TYPE_BY_ATTACH_CONTENT;

                            $this->lastMailClassifyType = self::MAIL_CLASSIFY_TYPE_CI;
                            $this->lastClassifyLog->mail_classify_type = $this->lastMailClassifyType;
                            $this->lastClassifyLog->recognition_mode = self::RECOGNITION_MODE_CI_BY_ATTACH;
                            return $this->lastMailClassifyType;
                        }
                    }

                    if ( \Util::striposArray($content, $ciContent) !== false) {
                        //如果内容包含则条用接口判断附件内容
                        $isCi = $this->mailFileRecognition($files,self::MAIL_CLASSIFY_TYPE_CI);

                        if($isCi) {
                            $this->captureCardLog->classify_type = self::MAIL_CLASSIFY_TYPE_CI;
                            $this->captureCardLog->type = CaptureCard::REASON_TYPE_BY_ATTACH_CONTENT;

                            $this->lastMailClassifyType = self::MAIL_CLASSIFY_TYPE_CI;
                            $this->lastClassifyLog->mail_classify_type = $this->lastMailClassifyType;
                            $this->lastClassifyLog->recognition_mode = self::RECOGNITION_MODE_CI_BY_ATTACH;
                            return $this->lastMailClassifyType;
                        }
                    }

                }
            }


            //PI:一个邮件有两个附件：ci for pi.pdf(非pi附件) 和 pi.pdf（pi附件），那么这还是一封PI类型的邮件
            if (!empty($fileNames))
            {
                $piAttachPattern = $piAttachPattern ? implode('|', $piAttachPattern) : '';
                $piAttachPattern = $piAttachPattern ? '/' . $piAttachPattern . '/i' : '';

                $matchFiles = [];
                foreach ($fileNames as $key => $fileName)
                {
                    $fileNameArr = explode(' for ',$fileName);
                    if(count($fileNameArr) > 1) {
                        //判断$fileNameArr[0]是否不包含关键词 & $fileNameArr[1]包含关键词
                        if(!preg_match($piAttachPattern, $fileNameArr[0], $matches) && preg_match($piAttachPattern, $fileNameArr[1], $matches)) {
                            //需要过滤
                            continue;
                        }
                    }

                    if (!empty($piAttachPattern) && preg_match($piAttachPattern, $fileName, $matches))
                    {
                        $matchFiles[] = $files[$key];
                    }
                }

                $isPi = $isSamplePi = false;
                $sampleRecognitionMode = $sampleReasonType = null;
                if (count($matchFiles)) {
                    //匹配了附件  继续调用接口
                    $isPi = $this->mailFileRecognition($matchFiles,self::MAIL_CLASSIFY_TYPE_PI);
                }

                //如果正文包含 pi invoice,则通过java接口判断附件是不是包含关键词 如果包含则是pi邮件
                if ($isPi === false && \Util::striposArray($content, $piContent) !== false) {
                    //如果内容包含则条用接口判断附件内容
                    $isPi = $this->mailFileRecognition($files,self::MAIL_CLASSIFY_TYPE_PI);
                }

                // 如果确定是PI邮件，继续分析是不是样品PI邮件
                if ($isPi) {
                    $sampleAttachPattern = $sampleAttachPattern ? implode('|', $sampleAttachPattern) : '';
                    $sampleAttachPattern = $sampleAttachPattern ? '/' . $sampleAttachPattern . '/i' : '';

                    foreach ($fileNames as $key => $fileName) {
                        if (!empty($sampleAttachPattern) && preg_match($sampleAttachPattern, $fileName, $matches)) {
                            $isSamplePi = true;
                            $sampleReasonType = CaptureCard::REASON_TYPE_BY_ATTACH_NAME;
                            $sampleRecognitionMode = self::RECOGNITION_MODE_SAMPLE_PI_BY_ATTACH;
                            break;
                        }
                    }

                    if ($isSamplePi === false) {
                        //新需求 中间组合词之间可以匹配0~3个词，中间不能有标点符号，但空格可以，不区分大小写，且无先后关系
                        $stringPattern1 = '('.implode('|',$sampleAssociatedWord1).')';
                        $stringPattern2 = '('.implode('|',$sampleAssociatedWord2).')';

                        $pattern1 = '/\b'.$stringPattern1.'\s(\w*\s+){0,3}\b'.$stringPattern2.'/i';
                        $pattern2 = '/\b'.$stringPattern2.'\s(\w*\s+){0,3}\b'.$stringPattern1.'/i';

                        if (preg_match($pattern1, $content, $matches) || preg_match($pattern2, $content, $matches) ) {
                            $isSamplePi = true;
                            $sampleReasonType = CaptureCard::REASON_TYPE_BY_MAIL_CONTENT;
                            $sampleRecognitionMode = self::RECOGNITION_MODE_SAMPLE_PI_BY_CONTENT;
                        }
                    }
                }

                if ($isSamplePi) {
                    $this->captureCardLog->classify_type = self::MAIL_CLASSIFY_TYPE_SAMPLE_PI;
                    $this->captureCardLog->type = $sampleReasonType;

                    $this->lastMailClassifyType = self::MAIL_CLASSIFY_TYPE_SAMPLE_PI;
                    $this->lastClassifyLog->mail_classify_type = $this->lastMailClassifyType;
                    $this->lastClassifyLog->recognition_mode = $sampleRecognitionMode;
                    return $this->lastMailClassifyType;
                }

                if ($isPi) {
                    $this->captureCardLog->classify_type = self::MAIL_CLASSIFY_TYPE_PI;
                    $this->captureCardLog->type = CaptureCard::REASON_TYPE_BY_ATTACH_CONTENT;

                    $this->lastMailClassifyType = self::MAIL_CLASSIFY_TYPE_PI;
                    $this->lastClassifyLog->mail_classify_type = $this->lastMailClassifyType;
                    $this->lastClassifyLog->recognition_mode = self::RECOGNITION_MODE_PI_BY_ATTACH;
                    return $this->lastMailClassifyType;
                }

            }

            //报价单
            //前置条件：发件且不是是第一封
            if ($this->mail->reply_to_mail_id > 0 && $this->mail->isSendType())
            {
                $this->lastMailClassifyType = self::MAIL_CLASSIFY_TYPE_QUOTATION;

                $attachNameString = !empty($fileNames) ? implode(' ', $fileNames) : '';

                $quotationAttachPattern = $quotationAttach ? implode('|', $quotationAttach) : '';
                $quotationAttachPattern = $quotationAttachPattern ? '/' . $quotationAttachPattern . '/i' : '';

                if ($attachNameString && !empty($quotationAttachPattern) && preg_match($quotationAttachPattern, $attachNameString, $matches)){
                    $this->captureCardLog->classify_type = self::MAIL_CLASSIFY_TYPE_QUOTATION;
                    $this->captureCardLog->type = CaptureCard::REASON_TYPE_BY_ATTACH_NAME;

                    $this->lastClassifyLog->recognition_mode = self::RECOGNITION_MODE_QUOTATION_BY_ATTACH;
                    return $this->lastClassifyLog->mail_classify_type =  $this->lastMailClassifyType;
                }

                if (!empty($this->mail->subject)
                    && \Util::striposArray($this->mail->subject, $quotationSubject) !== false
                    && \Util::striposArray($this->mail->subject, $quotationSubjectFilter) === false
                ) {
                    $this->captureCardLog->classify_type = self::MAIL_CLASSIFY_TYPE_QUOTATION;
                    $this->captureCardLog->type = CaptureCard::REASON_TYPE_BY_MAIL_SUBJECT;

                    $this->lastClassifyLog->recognition_mode = self::RECOGNITION_MODE_QUOTATION_BY_SUBJECT;
                    return $this->lastClassifyLog->mail_classify_type =  $this->lastMailClassifyType;
                }

                $matchContentFlag = false;
                if (\Util::striposArray($content, $quotationContentFilter) === false)
                {
                    if (!empty($quotationContentPatternFilter))
                    {
                        $quotationContentPatternFilter = implode('|', $quotationContentPatternFilter);
                        $quotationContentPatternFilter = '/'.$quotationContentPatternFilter.'/i';
                        if (!preg_match($quotationContentPatternFilter, $content, $matches))
                        {
                            $matchContentFlag = true;
                        }
                    }
                }

                if ($matchContentFlag)
                {
                    if (\Util::striposArray($content, $quotationContent) !== false) {
                        $this->captureCardLog->classify_type = self::MAIL_CLASSIFY_TYPE_QUOTATION;
                        $this->captureCardLog->type = CaptureCard::REASON_TYPE_BY_MAIL_CONTENT;

                        $this->lastClassifyLog->recognition_mode = self::RECOGNITION_MODE_QUOTATION_BY_CONTENT;
                        return $this->lastClassifyLog->mail_classify_type =  $this->lastMailClassifyType;

                    }

                    //content preg
                    if (!empty($quotationContentPattern))
                    {
                        $quotationContentPattern = implode('|', $quotationContentPattern);
                        $quotationContentPattern = '/'.$quotationContentPattern.'/i';

                        if (preg_match($quotationContentPattern, $content, $matches)) {
                            $this->captureCardLog->classify_type = self::MAIL_CLASSIFY_TYPE_QUOTATION;
                            $this->captureCardLog->type = CaptureCard::REASON_TYPE_BY_MAIL_CONTENT;

                            $this->lastClassifyLog->recognition_mode = self::RECOGNITION_MODE_QUOTATION_BY_CONTENT;
                            return $this->lastClassifyLog->mail_classify_type =  $this->lastMailClassifyType;
                        }
                    }

//                            associated 只要两个同时出现在邮件正文中
//                    if (\Util::striposArray($content, $quotationAssociatedWord1) !== false
//                        && \Util::striposArray($content, $quotationAssociatedWord2) !== false
//                    )
//                    {
//                        return $this->lastClassifyLog->mail_classify_type =  $this->lastMailClassifyType;
//                    }

                    //新需求 中间组合词之间可以匹配0~3个词，中间不能有标点符号，但空格可以，不区分大小写，且无先后关系
                    $stringPattern1 = '('.implode('|',$quotationAssociatedWord1).')';
                    $stringPattern2 = '('.implode('|',$quotationAssociatedWord2).')';

                    $pattern1 = '/\b'.$stringPattern1.'\s(\w*\s+){0,3}\b'.$stringPattern2.'/i';
                    $pattern2 = '/\b'.$stringPattern2.'\s(\w*\s+){0,3}\b'.$stringPattern1.'/i';

                    if (preg_match($pattern1, $content, $matches) || preg_match($pattern2, $content, $matches) ) {
                        $this->captureCardLog->classify_type = self::MAIL_CLASSIFY_TYPE_QUOTATION;
                        $this->captureCardLog->type = CaptureCard::REASON_TYPE_BY_MAIL_CONTENT;

                        $this->lastClassifyLog->recognition_mode = self::RECOGNITION_MODE_QUOTATION_BY_CONTENT;
                        return $this->lastClassifyLog->mail_classify_type = $this->lastMailClassifyType;
                    }

                }
            }
        }

        $this->lastMailClassifyType = '';

        //java 数据模型
        if ($this->reuseJavaData && !$this->lastClassifyLog->getIsNewRecord() && $this->lastClassifyLog->mail_classify_call)
        {
            $maxProb = $this->lastClassifyLog->mail_classify_prob;
            $maxType = $this->lastClassifyLog->mail_classify_type;
            $version = $this->lastClassifyLog->mail_classify_prob_version;
            $versionType = $this->lastClassifyLog->mail_classify_prob_version_type;
        }
        else
        {
            $api = new InnerApi('mail_classify');
            if ($this->simulation || $this->userId == 55258608)
                $api->setAccessLog(true);

            if (YII_DEBUG) {
                $api->setTimeout(10);
            }

            $data = $api->call('mail_type', [
                'mailId' => $this->mailId, 'userMailId' => $this->userMailId,
                'clientId' => $this->clientId, 'userId' => $this->userId
            ]);

            if ($this->userId == 55258608)
                \LogUtil::info('andy log mail id: ' . $this->mailId . ' mail tag ' . json_encode($data));

            $data = $data['data'];
            $this->lastClassifyLog->mail_classify_call = 1;

            $maxProb = 0;
            $maxType = '';
            foreach ($data['mailType'] as $elem)
            {
                if (in_array($elem['typeName'], $types) && ($elem['prob'] > $maxProb))
                {
                    $maxProb = $elem['prob'];
                    $maxType = $elem['typeName'];
                }
            }

            // Java返回的rfq(询盘)其实应该为personal_inquiry(个人询盘)，所以把值转换为个人询盘
            $maxType = $maxType == self::MAIL_CLASSIFY_TYPE_INQUIRY ? self::INQUIRY_TYPE_PERSONAL : $maxType;
            $this->lastClassifyLog->mail_classify_prob = $maxProb;
            $this->lastClassifyLog->mail_classify_type = $maxType;

            $version = $this->formatVersion($data['version']);
            $versionType = $this->formatVersionType($data['versionType'] ?? '');
            $this->lastClassifyLog->mail_classify_prob_version = $version;
            $this->lastClassifyLog->mail_classify_prob_version_type = $versionType;
        }

        $this->lastMailClassifyType = $maxType;
        $this->lastMailClassifyProb = $maxProb;
        $this->lastMailClassifyProbVersion = $version;
        $this->lastMailClassifyProbVersionType = $versionType;

        if ($this->lastMailClassifyType && $maxProb > $prob) {
            if ($maxType == self::INQUIRY_TYPE_PERSONAL) {
                $this->captureCardLog->inquiry_classify = $maxType;
            } else {
                $this->captureCardLog->classify_type = $maxType;
            }
            $this->captureCardLog->type = CaptureCard::REASON_TYPE_BY_MACHINE_LEARNING_MODEL;

            $this->lastClassifyLog->recognition_mode = self::RECOGNITION_MODE_BY_MODE;
            return $this->lastMailClassifyType;
        }

        return false;
    }

    /**
     * @param $content
     * @return bool|mixed|string
     * 获取正文最新会话内容
     */
    protected function filterMailContent($content)
    {
        $content = \Util::plainText($content);
        if (empty($content))
            return '';

        //处理回复转发-------- 原始邮件 --------，------------------ Original ------------------
        $replaceString = "#######TAG######";
        $content = preg_replace("/[\\-]{6,}/", $replaceString, $content);

        $endPos = strpos($content, $replaceString);
        if ($endPos === false)
            return $content;

        $content = substr($content,0, $endPos);

        return $content ? $content : '';
    }

    protected function inquiryLead()
    {
        // 非B2B平台询盘 && 用户设置的MKT邮箱过滤
        if (empty($this->applyList['inquiry']) && $this->marketingUserMailBindCheck()) {
            return false;
        }

        //用户设置的线索自动化创建规则-过滤邮箱
//        if (in_array($this->mail->getUserMailId(), $this->setting->leadApplySettings['lead_ignore_user_mail_id'])) {
//            \LogUtil::info("mail id: {$this->mailId} MailType:{$this->lastMailClassifyType} 用户设置的线索自动化创建规则 - 设置的忽略邮箱");
//            return false;
//        }
//        if (in_array($this->mail->getUserMailId(), ($this->setting->customerConvertConfigList[\common\library\setting\library\origin\Origin::SYS_ORIGIN_UNKNOWN]['list'][0]['filter_config'][\Constants::TYPE_LEAD]['ignore_user_mail_id'] ?? []))) {
//            \LogUtil::info("mail id: {$this->mailId} MailType:{$this->lastMailClassifyType} 用户设置的线索自动化创建规则 - 设置的忽略邮箱");
//            return false;
//        }

        //用户设置的线索自动化创建规则-业务邮件类型
//        if (empty($this->lastInquiryType) && !in_array($this->lastMailClassifyType, $this->setting->leadApplySettings['lead_filter_mail_type'])) {
//            \LogUtil::info("mail id: {$this->mailId} MailType:{$this->lastMailClassifyType} 用户设置的线索自动化创建规则 - 业务邮件类型不符合");
//            return false;
//        }
        $lastMailClassifyTypeMatchOrigin = \common\library\setting\library\origin\Origin::CLASSIFY_FILTER_MAIL_TYPE_MAP[$this->lastMailClassifyType] ?? 0;
        if (empty($this->lastInquiryType)
            && !in_array($lastMailClassifyTypeMatchOrigin, array_column($this->setting->customerConvertConfigList[\Constants::TYPE_LEAD]['list'], 'origin_id'))
            && !in_array($lastMailClassifyTypeMatchOrigin, array_column($this->setting->customerConvertConfigList[\Constants::TYPE_COMPANY]['list'],'origin_id'))
        ) {
            \LogUtil::info("mail id: {$this->mailId}, MailType:{$this->lastMailClassifyType}, 用户设置的线索自动化创建规则 - 业务邮件类型不符合");
            return false;
        }

        // 用户设置的线索自动化创建规则-过滤邮箱
        // 根据获客来源分为转化成线索、客户不同类型
        $convertedIntoCompanyOfOriginList = array_column($this->setting->customerConvertConfigList[\Constants::TYPE_COMPANY]['list'], 'origin_id');

        $origin = \common\library\setting\library\origin\Origin::CLASSIFY_FILTER_MAIL_TYPE_MAP[$this->lastInquiryType] ?? 0;

        $convertModule = in_array($origin ?: $lastMailClassifyTypeMatchOrigin, $convertedIntoCompanyOfOriginList) ? \Constants::TYPE_COMPANY : \Constants::TYPE_LEAD ;

        if (in_array($this->mail->getUserMailId(), ($this->setting->customerConvertConfigList[\common\library\setting\library\origin\Origin::SYS_ORIGIN_UNKNOWN]['list'][0]['filter_config'][$convertModule]['ignore_user_mail_id'] ?? []))) {
            \LogUtil::info("mail id: {$this->mailId} Origin:$origin MailType:{$this->lastMailClassifyType} 用户设置的线索自动化创建规则 - 设置的忽略邮箱");
            return false;
        }

        // 校验收发件次数，判断是否能创建线索
        $originId = \common\library\setting\library\origin\Origin::CLASSIFY_FILTER_MAIL_TYPE_MAP[$this->lastMailClassifyType] ?? 0;
        $filterConfig = $this->setting->customerConvertConfigList[\Constants::TYPE_COMPANY]['list'][$originId]['filter_config'] ?? [];

        if (isset($filterConfig['mail_receive_quantity']) && isset($filterConfig['mail_send_quantity']) && !$this->filterCustomerMailUserRule())
        {
            \LogUtil::info('filterCustomerMailUserRuleError', [
                'originId' => $originId,
                'filterConfig' => $filterConfig,
                'mail_id' => $this->mail->mail_id
            ]);
            return false;
        }

        \LogUtil::info("mailId:{$this->mail->mail_id} process: inquiryLead");

        $return = [
            'type' => !empty($this->lastInquiryType) ? $this->lastInquiryType : $this->lastMailClassifyType,
            'operator' => 'archive',
            'lead_id' => [],
            'data' => []
        ];

        $matchFlag = false;
        $extractType = 'inquiry';
        $origin = '';

        // 平台询盘
//        switch ($this->lastInquiryType) {
//            case self::INQUIRY_TYPE_ALIBABA :
//                $origin = \common\library\setting\library\origin\Origin::SYS_ORIGIN_ALIBABA;
//                $matchFlag = true;
//                break;
//            case self::INQUIRY_TYPE_GLOBAL_SOURCES :
//                $origin = \common\library\setting\library\origin\Origin::SYS_ORIGIN_GLOBAL_SOURCES;
//                $matchFlag = true;
//                break;
//            case self::INQUIRY_TYPE_GLOBAL_MARKET :
//                $origin = \common\library\setting\library\origin\Origin::SYS_ORIGIN_GLOBAL_MARKET;
//                $matchFlag = true;
//                break;
//            case self::INQUIRY_TYPE_MADE_IN_CHINA :
//                $origin = \common\library\setting\library\origin\Origin::SYS_ORIGIN_MADE_IN_CHINA;
//                $matchFlag = true;
//                break;
//        }

        // 平台询盘
        $origin = \common\library\setting\library\origin\Origin::CLASSIFY_FILTER_MAIL_TYPE_MAP[$this->lastInquiryType] ?? 0;

        // 个人询盘、报价
        if (empty($origin)) {
            switch ($this->lastMailClassifyType) {
                case self::INQUIRY_TYPE_PERSONAL :
                    $extractType = 'normal';
                    $matchEmails = $this->getLastMail()->getSenderEmail();
//                    $origin = \common\library\setting\library\origin\Origin::SYS_ORIGIN_UNKNOWN;
//                    $matchFlag = true;
                    break;
                case self::MAIL_CLASSIFY_TYPE_QUOTATION :
                    $extractType = 'normal';
                    $matchEmails = $this->getLastMail()->getAllReceiverEmails();
//                    $origin = \common\library\setting\library\origin\Origin::SYS_ORIGIN_UNKNOWN;
//                    $matchFlag = true;
                    break;
            }

            $origin = \common\library\setting\library\origin\Origin::CLASSIFY_FILTER_MAIL_TYPE_MAP[$this->lastMailClassifyType] ?? 0;
        }

//        if ($matchFlag === false) {
//            return false;
//        }

        if (empty($origin)) {
            \LogUtil::info("mail id: {$this->mailId} InquiryType:{$this->lastInquiryType} MailType:{$this->lastMailClassifyType} 用户设置的线索自动化创建规则 - 获取不到来源");
            return false;
        }

        $extractClass = ExtractFactory::create($extractType);
        $extractClass->setExtractMail($this->getLastMail());

        if ($extractType == 'inquiry') {
            $extractData = $extractClass->extract(['inquiry_type' => $this->lastInquiryType]);
        } else {
            $mainEmail = reset($matchEmails);
            $extractData = $extractClass->extract(['main_email' => $mainEmail, 'emails' => $matchEmails]);
        }

        $leadData = $extractData['company'];
        $leadData['origin_id'] = $origin;
        $leadData['origin_list'] = [$origin];
        $customerData = $extractData['customer'];

        // todo Tony 个人询盘、报价的邮件与平台询盘不一样，可能有多个邮箱，这里的处理逻辑要等产品确定后再修改，暂时只提取一个邮箱来执行下面的逻辑
        if ($extractType == 'normal') {
            $customerData = $customerData[$mainEmail];
        }

        $archiveEmail = $customerData['email'] ?? '';
        $return['data'] = ['lead' => $leadData, 'customer' => $customerData];

        //未提取到邮箱
        if (empty($archiveEmail))
        {
            $this->logCounterRule([], self::RULE_INQUIRY_EMPTY_EMAIL);
            return false;
        }

        $customerBlackList = $this->getCustomerBlackList();
        if (array_key_exists($archiveEmail, $customerBlackList['email']))
        {
            $this->logCounterRule([$archiveEmail], self::RULE_BLACK_LIST);
            return false;
        }

        $domain = \EmailUtil::getDomain($archiveEmail);

        if (array_key_exists("@$domain", $customerBlackList['domain']))
        {
            $this->logCounterRule([$archiveEmail], self::RULE_BLACK_LIST);
            return false;
        }

        $archiveEmail = \EmailUtil::findEmail($archiveEmail);
        if (empty($archiveEmail))
        {
            $this->logCounterRule([], self::RULE_INQUIRY_EMPTY_EMAIL);
            return false;
        }

        $handler = new ConvertHandler($this->clientId, $this->userId, $origin);
        $duplicateFlag = $handler->getDuplicateFlag();

        //邮箱已被建档到私海线索
        $list = new LeadCustomerList($this->clientId);
        $list->setFields('lead_id');
        $list->setUserId($this->userId);
        $list->setEmail($archiveEmail);
        $customerList = $list->find();
        if (!empty($customerList)){
            $return['operator'] = $duplicateFlag ? 'archive' :'renew_time';
            $return['lead_id'] = array_column($customerList, 'lead_id');
            return $return;
        }

        //邮箱已在公司范围内、或者同个公海分组内被建档为客户
        if(\common\library\customer\Helper::emailExists($this->clientId, $archiveEmail, 0, $this->userId))
        {
            $this->logCounterRule([$archiveEmail], self::RULE_INQUIRY_CUSTOMER_EXISTS);
            return false;
        }

        //公共邮箱后缀--新建
        if (CommonDomain::check($domain))
            return $return;

        //企业邮箱后缀已被建档于线索私海--合并
        $list = new EmailIdentityList($this->userId);
        $list->setFields('email');
        $list->setDomains($domain);
        $list->setEmailType(EmailIdentityList::EMAIL_TYPE_OWNER_LEAD);
        $emailList = $list->find();
        if (!empty($emailList))
        {
            $emailList = array_column($emailList, 'email');
            $list = new LeadCustomerList($this->clientId);
            $list->setAlias('customer');
            $list->setFields('customer.lead_id');
            $list->setJoin( ' inner join tbl_lead as lead on customer.lead_id = lead.lead_id and lead.is_archive='.Lead::ARCHIVE_OK);
            $list->setUserId($this->userId);
            $list->setEmail($emailList);
            $list->setIsArchive(Lead::ARCHIVE_OK);
            $list->setOrderBy(['order_time']);
            $list->setOrder('desc');
            $leadList = $list->find();

            $leadId = !empty($leadList) ? (reset($leadList))['lead_id'] : null;
            if (!$leadId)
                return false;

            $return['lead_id'] = [$leadId];
            $return['operator'] = 'private';
            return $return;
        }

        //新建
        return $return;
    }

    public static function prettyLeadArchiveData($data)
    {
        if (empty($data))
            return '';
        $operator = $data['operator'];
        $leadId = $data['lead_id'];

        if ($operator == 'archive')
            return "新建线索:".json_encode($data['data']);

        if ($operator == 'private')
            return '合并联系人至私海线索: leadId ' . $leadId . json_encode($data['data']['customer']);

        if ($operator == 'renew_time')
            return '更新私海线索更新时间 lead_ids:'.implode(' ', $leadId);

        return '';
    }


    protected function archiveEmailAnalyze($emails=[])
    {
        //未指定邮箱就通过邮件提取邮箱
        if( empty($emails) )
        {
            if (empty($this->mail->reply_to)) {
                $sender = \EmailUtil::findEmail($this->mail->sender);
            } else {
                $sender = \EmailUtil::findEmail($this->mail->reply_to);
            }

            $receiver = \EmailUtil::findAllMailAddress($this->mail->receiver . ';' . $this->mail->cc);
            $receiver = is_array($receiver) ? array_values($receiver) : [];


            if( $this->mail->mail_type == \Mail::MAIL_TYPE_SEND ||  $this->mail->mail_type == \Mail::MAIL_TYPE_UNKNOWN )
            {
                $emails = $receiver;
            }else
            {
                $emails = [$sender];
            }
        }

        $emails = array_values(array_unique(array_filter($emails)));
        $this->archiveEmails = $emails;
        return empty($emails)?false: $emails;
    }

    protected function archiveDomainCheck($force=false)
    {
        $emails = $this->archiveEmails;
        //检测域名
        $domainList = [];
        foreach ($emails as $email) {
            $domain = \EmailUtil::getDomain($email);

            if (CommonDomain::check($domain))
                continue;

            $domainList[] = $domain;
        }

        $privateCompanyDomain = [];
        $collegeCompanyDomain = [];
        if ($domainList)
        {
            $userId = $this->mail->getUserId()?:$this->userId;
            $list = new EmailIdentityList($userId);
            $list->setDomains($domainList);
            $list->setEmailType(EmailIdentityList::EMAIL_TYPE_ARCHIVE_CUSTOMER);
            $list->setFields('email, domain, customer_user');
            $identityList = $list->find();

            foreach ($identityList as $item)
            {
                $customerUserIds = PgsqlUtil::trimArray($item['customer_user']);

                if (!in_array($this->mail->getUserId(), $customerUserIds))
                {
                    $collegeCompanyDomain[] = $item['domain'];
                    continue;
                }

                $privateCompanyDomain[] = $item['domain'];
            }
        }

        //如果不是强制建档的话,提示相同域名的邮箱已被建档为客户
        if( !$force && (!empty($privateCompanyDomain) || !empty($collegeCompanyDomain)))
        {
            return false;
        }

        return [$privateCompanyDomain, $collegeCompanyDomain];
    }

    protected function archiveCheck()
    {
        $emails = $this->archiveEmails;
        //检查邮箱是否建档
        $customerList = new CustomerList($this->clientId);
        $customerList->setEmail($emails);
        $customerList->setFields(['email']);
        $customerList->setCheckPoolDuplicateSwitchFlag(true);
        $archiveEmail = array_column($customerList->find(), 'email');
        //已经建档邮箱就过滤掉
        $emails = array_diff($emails, $archiveEmail);
        $this->archiveEmails = $emails;
        return empty($emails)?false:$emails;
    }

    protected function customerArchiveOnekey()
    {
        $archiveList =[];
        $privateList =[];
        // 目前需求都统一建档为私海客户
        $archiveList = array_map(function ($item){
            return [$item];
        },            $this->archiveEmails);
        if( empty($archiveList) && empty($privateList) )
        {
            return false;
        }

        return [
            $archiveList,
            $privateList
        ];
    }

    protected function mailFileRecognition($files,$stage)
    {
        //过滤附件
        $recognitionFiles = [];
        foreach ($files as $file) {
            if(in_array(strtolower((pathinfo($file['file_name'])['extension']??'')),[
                'pdf',
                'doc',
                'docx',
                'xlsx',
                'xls',
                'csv'
            ])){
                $recognitionFiles[] = $file['file_id'];
            }
        }

        if(empty($recognitionFiles)) {
            return false;
        }

        $stage = strtoupper($stage);

        $api = new InnerApi('file_classify');
        $api->setTimeout(10);
        if ($this->simulation || $this->userId == 55258608)
            $api->setAccessLog(true);
        $params = [
            'file_id' => implode(',',$recognitionFiles),'user_id' => $this->mail->getUserId(),
            'client_id' => (int)$this->mail->getMailClientId(),'mail_stage' => $stage
        ];

        $data = $api->call('recognition',$params);

        $recognitionResult =  empty($data['data']['structInfo']) ? []:$data['data']['structInfo'];

        $recognitionFilesResult = [];
        foreach ($recognitionResult as $item) {
            if($item['mail_stage'] == $stage) {
                $recognitionFilesResult[] = $item['file_id'];
            }
        }

        if(empty($recognitionFilesResult)) {
            return false;
        }else{
            $this->lastClassifyLog->mail_classify_file_data = json_encode($recognitionFilesResult);
            return true;
        }
    }

    /**
     * 检查业务员与联系人的业务邮件类型或者业务员与联系人的收发件次数是否符合用户设置的客户自动化创建规则（两个条件只要符合其中一条就通过）
     * @return bool
     */
    protected function customerUserRuleCheck()
    {
//        if (!empty($this->lastMailClassifyType) && in_array($this->lastMailClassifyType, $this->setting->customerApplySettings['filter_mail_type'])) {
//            return true;
//        }
//
//        if ($this->filterCustomerMailUserRule()) {
//            return true;
//        }

//        \LogUtil::info("mail id: {$this->mailId} MailType:{$this->lastMailClassifyType} 业务邮件类型或者收发邮件次数不符合用户设置的客户自动化创建规则");
//        return false;

        // todo 新自动化这里两个条件变成了 且的 关系，在没有配置来源时，其实也没有邮件次数的配置信息
        if (!empty($this->lastMailClassifyType) && in_array((\common\library\setting\library\origin\Origin::CLASSIFY_FILTER_MAIL_TYPE_MAP[$this->lastMailClassifyType] ?? 0), array_keys($this->setting->customerConvertConfigList[\Constants::TYPE_COMPANY]['list']))) {
            if ($this->filterCustomerMailUserRule()) {
                return true;
            }
        }

        \LogUtil::info("mail id: {$this->mailId} MailType:{$this->lastMailClassifyType} 没有匹配到获客来源转化规则");
        return false;
    }

    /**
     * 检查业务员与联系人的收发件次数是否符合用户设置的客户自动化创建规则
     * @return bool
     */
    protected function filterCustomerMailUserRule()
    {
        $originId = \common\library\setting\library\origin\Origin::CLASSIFY_FILTER_MAIL_TYPE_MAP[$this->lastMailClassifyType] ?? 0;
        if (isset($this->setting->customerConvertConfigList[\Constants::TYPE_COMPANY]['list'][$originId]['filter_config'])) {
            $condition_relation = $this->setting->customerConvertConfigList[\Constants::TYPE_COMPANY]['list'][$originId]['filter_config']['condition_relation'];
            $mail_send_quantity = $this->setting->customerConvertConfigList[\Constants::TYPE_COMPANY]['list'][$originId]['filter_config']['mail_send_quantity'];
            $mail_receive_quantity = $this->setting->customerConvertConfigList[\Constants::TYPE_COMPANY]['list'][$originId]['filter_config']['mail_receive_quantity'];
        } else {
            $condition_relation = $mail_send_quantity = $mail_receive_quantity = 0;
        }

        $mailSendFlag = $mailReceiverFlag = false;
        if (empty($mail_send_quantity) && empty($mail_receive_quantity)) {
            return false;
        }

        if (!empty($mail_send_quantity)) {
            if ($mail_send_quantity == 1 && $this->mail->isSendType()) {
                $mailSendFlag = true;
            } else {
                $mailSendFlag = \common\library\ai\classify\mail\Helper::getCustomerMailCount(
                        \Mail::MAIL_TYPE_SEND,
                        $this->clientId,
                        $this->userId,
                        $this->userMailId,
                        $this->mail->isSendType() ? $this->mail->getAllReceivers() : $this->mail->getSenderEmail()
                    ) >= $mail_send_quantity;
            }
        }

        if (!empty($mail_receive_quantity)) {
            if ($mail_receive_quantity == 1 && $this->mail->isReceiverType()) {
                $mailReceiverFlag = true;
            } else {
                $mailReceiverFlag = \common\library\ai\classify\mail\Helper::getCustomerMailCount(
                        \Mail::MAIL_TYPE_RECEIVE,
                        $this->clientId,
                        $this->userId,
                        $this->userMailId,
                        $this->mail->isSendType() ? $this->mail->getAllReceivers() : $this->mail->getSenderEmail()
                    ) >= $mail_receive_quantity;
            }
        }

        if ($condition_relation == 1) {
            return $mailSendFlag && $mailReceiverFlag;
        } elseif ($condition_relation == 2) {
            return $mailSendFlag || $mailReceiverFlag;
        }

        return false;
    }

    /**
     * 假如存在邮件过滤规则，那么在构建线索的时候，需要校验一下规则才能进行构建，假如没有的话，走历史逻辑
     *
     * 历史逻辑：
     * 1. ai_lead_apply.yaml 文件中，以前配置为 inquiry_archive.require: inquiry_lead_archive
     * 2. 所以历史逻辑默认取 inquiry_lead_archive 的值，这里把 inquiry_lead_archive 设置为 1，所以没有规则，默认为 true，即走了历史逻辑
     */
    protected function leadArchiveCheck()
    {
        $flag = true;

        $originId = \common\library\setting\library\origin\Origin::CLASSIFY_FILTER_MAIL_TYPE_MAP[$this->lastMailClassifyType] ?? 0;
        $filterConfig = $this->setting->customerConvertConfigList[\Constants::TYPE_COMPANY]['list'][$originId]['filter_config'] ?? [];

        // 校验收发件次数，判断是否能创建线索
        if (isset($filterConfig['mail_receive_quantity']) && isset($filterConfig['mail_send_quantity']))
        {
            $flag = $this->filterCustomerMailUserRule();
        }

        return $flag;
    }

    protected function mailAttachClassifyType()
    {
        //过滤附件
        $files = $this->mailAttachList;
        if (empty($files)) {
            return false;
        }

        $recognitionFiles = [];
        foreach ($files as $file) {
            if(in_array(strtolower((pathinfo($file['file_name'])['extension']) ?? ''),[
                'pdf',
                'doc',
                'docx',
                'xlsx',
                'xls'
            ]) || $this->lastMailClassifyType == self::MAIL_CLASSIFY_TYPE_BS){
                $recognitionFiles[] = $file['file_id'];
            }
        }

        if(empty($recognitionFiles)) {
            return false;
        }

        $api = new InnerApi('file_classify');
        $api->setTimeout(10);
        if ($this->simulation || $this->userId == 55258608)
            $api->setAccessLog(true);
        $params = [
            'file_id' => implode(',',$recognitionFiles),
            'user_id' => (int)$this->mail->getUserId(),
            'client_id' => (int)$this->mail->getMailClientId(),
            'mail_id' => (int)$this->mail->getMailId()
        ];

        $data = $api->call('fileType', $params);
        $files =  empty($data['data']['structInfo']) ? [] : $data['data']['structInfo'];

        $result = [];
        foreach ($files as $item) {
            if ($item['file_type'] != 'unknown') {
                $item['file_type'] = strtolower($item['file_type']);
                switch ($item['file_type']) {
                    case self::MAIL_CLASSIFY_TYPE_QUOTATION:
                        $tagId = \common\library\setting\library\tag\Tag::MAIL_TAG_QUOTATION;
                        break;
                    case self::MAIL_CLASSIFY_TYPE_PO:
                        $tagId = \common\library\setting\library\tag\Tag::MAIL_TAG_PO;
                        break;
                    case self::MAIL_CLASSIFY_TYPE_PI:
                        $tagId = \common\library\setting\library\tag\Tag::MAIL_TAG_PI;
                        break;
                    case self::MAIL_CLASSIFY_TYPE_PL:
                        $tagId = \common\library\setting\library\tag\Tag::MAIL_TAG_PL;
                        break;
                    case self::MAIL_CLASSIFY_TYPE_CI:
                        $tagId = \common\library\setting\library\tag\Tag::MAIL_TAG_CI;
                        break;
                    case self::MAIL_CLASSIFY_TYPE_BS:
                        $tagId = \common\library\setting\library\tag\Tag::MAIL_TAG_BS;
                        break;
                    default:
                        $tagId = 0;
                        break;
                }

                $result[] = [
                    'file_id' => $item['file_id'],
                    'file_type' => $tagId
                ];
                $capture = new CaptureCardLog($this->clientId, $item['file_id']);
                $capture->biz_type = CaptureCard::BIZ_TYPE_FILE;
                $capture->tag_id = $tagId;
                $capture->type = $item['reason'] == 'content' ? CaptureCard::REASON_TYPE_BY_ATTACH_CONTENT : CaptureCard::REASON_TYPE_BY_ATTACH_NAME;
                $capture->classify_type = $item['file_type'];

                if (isset($this->invoiceExtraData[0]['fileId']) && $this->invoiceExtraData[0]['fileId'] == $item['file_id'] && count($this->invoiceExtraData[0]['data']['products'])) {
                    $capture->pi_no = $this->invoiceExtraData[0]['data']['piNumber'] ?? '';
                    $capture->pi_amount = $this->invoiceExtraData[0]['data']['total'] ?? '';
                    $capture->currency = strtoupper($this->invoiceExtraData[0]['data']['currencyType']) ?? '';
                }

                $capture->save();
            }
        }

        return $result;
    }

    /**
     * @return int|mixed
     */
    public function marketingUserMailBindCheck()
    {
        $this->marketingGaSiteId = \common\library\cms\inquiry\sitemail\Helper::getSiteIdByUserMailBound($this->userMailId, $this->clientId);
        if (!$this->marketingGaSiteId) {
            $this->marketingCmsSiteId = array_column(\common\library\account\Helper::getClientSettingValue($this->clientId, Client::SETTING_KEY_MARKETING_IGNORE_AI_USER_MAIL_LIST) ?: [], 'site_id', 'user_mail_id')[$this->userMailId] ?? 0;
            $this->marketingGaSiteId = 0;
            if ($this->marketingCmsSiteId) {
                $site = SiteService::getGaSiteByCmsSiteId($this->clientId, $this->marketingCmsSiteId);
                $this->marketingGaSiteId = $site['site_id'] ?? 0;
            }
        }
        return $this->marketingGaSiteId  ? true : false;
    }

    public function marketingUserMailApplyCheck()
    {
        //超过7天收件不在处理
        if( strtotime($this->mail->receive_time) <  (time() -  86400 * 7))
        {
            \LogUtil::info("processMarketingClassify: receive_time : {$this->mail->receive_time}  old ,userMailId:{$this->userMailId},mailId=".$this->mail->mail_id);
            return  false;
        }

        //不是用户设置的MKT邮箱过滤
        if (!$this->marketingUserMailBindCheck()) {
            return false;
        }

        // 通知邮件建档
        if (isset($this->applyList['marketing_user_notify_mail_check']['extra_data']['email'])) {
            $extractData = $this->applyList['marketing_user_notify_mail_check'];
            return [
                'sender_email' => $extractData['extra_data']['email'],
                'cms_site_id' => $this->marketingCmsSiteId,
                'ga_site_id' => $this->marketingGaSiteId,
                'extract_data' => $extractData['email_data'],
                'country' => $extractData['extra_data']['country']
            ];
        }

        // 是否收到过邮件
        $senderEmailAddress = current($this->mail->getSenderEmail());
        if(empty($senderEmailAddress))
            return false;

        $senderEmailAddress = Util::getPureEmail($senderEmailAddress);
        // 对发件回复时，mail类型=2时,此时userMailId是发件人mailId
        if($this->mail->mail_type == \Mail::MAIL_TYPE_SEND)
        {
            $userMail = new \common\library\email\Email($this->userMailId);
            $email = $userMail->email_address;
            if($email == $senderEmailAddress)
            {
                \LogUtil::info("processMarketingClassify:send $email reply,userMailId:{$this->userMailId},mailId=".$this->mail->mail_id);
                return false;
            }

            // 兼容别名邮箱
            $mailAliasesList = UserMailAlias::findByUserMailIds([$this->userMailId], $this->clientId);
            $aliasAddresses = $mailAliasesList ? array_map(function ($item) {
                return $item->alias_address;
            }, $mailAliasesList) : [];
            if (in_array($senderEmailAddress, $aliasAddresses)) {
                \LogUtil::info("processMarketingClassify:send alias $email reply,userMailId:{$this->userMailId},mailId=".$this->mail->mail_id);
                return false;
            }
        }

        $emailId = \common\library\email\Helper::getEmailId($senderEmailAddress)[$senderEmailAddress]??0;
        if (empty($emailId)) {
            \LogUtil::info("processMarketingClassify: $senderEmailAddress emailId:{$emailId} not exist");
            return false;
        }
        // 绑定mkt邮箱之后的邮件往来记录
        $emailBindTime = \common\library\cms\inquiry\sitemail\Helper::getUserMailBindTime($this->clientId, $this->userMailId, $this->marketingGaSiteId);
        $mailRelation = "select count(1) from tbl_email_relation where client_id = {$this->clientId} and user_mail_id = {$this->userMailId}
              and email_id = $emailId and mail_time > '{$emailBindTime}'";
        $db = \ProjectActiveRecord::getDbByClientId($this->clientId);
        //存在非本次邮件生成的来往记录
        if ($db->createCommand($mailRelation)->queryScalar() > 1) {
            \LogUtil::info("processMarketingClassify: $senderEmailAddress emailId:{$emailId} send mail to {$this->userMailId} before");
            return false;
        }

        // 是否线索建档
        $list = new LeadCustomerList($this->clientId);
        $list->setFields('lead_id');
        $list->setEmail($senderEmailAddress);
        $customerList = $list->find();
        if (!empty($customerList)) {
            \LogUtil::info("processMarketingClassify: $senderEmailAddress already archive lead: " . implode(',', array_column($customerList, 'lead_id')));
            return false;
        }

        \LogUtil::info("processMarketingClassify: sender $senderEmailAddress cms_site: {$this->marketingCmsSiteId} ga_site: {$this->marketingGaSiteId}");
        return ['sender_email' => $senderEmailAddress, 'cms_site_id' => $this->marketingCmsSiteId, 'ga_site_id' => $this->marketingGaSiteId];
    }

    public function marketingUserNotifyMailCheck(array $emailKeyword, array $countryKeyword) {
        // 内容提取
        $plainText = $this->mail->getPlainText();
        \LogUtil::info("marketingUserNotifyMailCheck processing, client_id: {$this->clientId} site_id: {$this->marketingGaSiteId} mail_plain_text: {$plainText}");

        // 优先定制化模板匹配
        $cmsSiteMail = new SiteMail($this->clientId, $this->marketingGaSiteId);
        $notifyTemplate = $cmsSiteMail->notify_template;
        // 暂不开放通用逻辑
        if (!$notifyTemplate) {
            return false;
        }

        $matchResult = [];
        if ($notifyTemplate) {
            preg_match_all("/{$notifyTemplate}/", $plainText, $matchResult);
            $matchResult = array_filter($matchResult);
        }

        // 暂不开放通用逻辑
//        if (empty($matchResult)) {
//            // 尝试通用模板匹配
//            foreach ($emailPattern as $pattern) {
//                preg_match_all("/{$pattern}/", $plainText, $matchResult);
//                $matchResult = array_filter($matchResult);
//                if (!empty($matchResult)) {
//                    break;
//                }
//            }
//        }

        $extractData = array_combine($matchResult['key']??[], $matchResult['value']??[]);
        $validResult = array_filter($extractData);
        // 没有任何一个模板匹配
        if (empty($validResult)) {
            \LogUtil::info("marketingUserNotifyMailCheck cannot extract valid info, client_id: {$this->clientId} site_id: {$this->marketingGaSiteId} mail_plain_text: {$plainText}");
            return false;
        }

        $emailKey = null;
        $countryKey = null;
        foreach ($validResult as $item => $value) {
            $validResult[$item] = trim($value);
            foreach ($emailKeyword as $keyword) {
                if (str_contains($item, $keyword)) {
                    $emailKey = $item;
                    continue 2;
                }
            }
            foreach ($countryKeyword as $keyword) {
                if (str_contains($item, $keyword)) {
                    $countryKey = $item;
                    continue 2;
                }
            }
        }
        if (empty($emailKey)) {
            \LogUtil::info("marketingUserNotifyMailCheck no valid email, extract data: ".json_encode($validResult));
            return false;
        }


        $validResult['email'] = $validResult[$emailKey]; // 如果没有email字段，补全email字段
        if ($countryKey) {
            $country = \CountryService::checkNameInTable($validResult[$countryKey])['alpha2'] ?? '';
        }

        return [
            'email_data' => $validResult,
            'extra_data' => [
                'email' => $validResult[$emailKey],
                'country' => $country??''
            ]
        ];
    }

    public function marketingInquiryArchive() {
        if (isset($this->applyList['customer_archive'])) {
            return $this->applyList['customer_archive'];
        }

        if (!isset($this->applyList['marketing_user_notify_mail_check']['extra_data']['email'])) {
            return false;
        }

        //  建档查询
        $inquiryEmail = $this->applyList['marketing_user_notify_mail_check']['extra_data']['email'];
        $result = $this->customerArchiveCheck([$inquiryEmail]);
        \LogUtil::info("marketing inquiry process: ".json_encode($result));
        return $result;
    }

}
