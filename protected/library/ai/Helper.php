<?php
/**
 * Copyright (c) 2012 - 2018 <PERSON><PERSON>.All Rights Reserved
 * Author: nuxse
 * Data: 2018/8/30
 */

namespace common\library\ai;

use common\library\ai\recommend\RecommendRecord;
use common\library\ai\service\RecommendService;
use common\library\custom_field\company_field\CompanyField;
use common\library\custom_field\company_field\CustomerField;
use common\library\custom_field\lead_field\LeadCustomerField;
use common\library\custom_field\lead_field\LeadField;
use common\library\discovery\api\Company;
use common\library\edm\EdmTask;
use common\library\lead\Lead;
use common\library\lead\LeadCustomer;
use common\library\validation\Str;

class Helper
{
    const SUBMIT_AI_EDM_TASK_KEY = 'crm:ai:edm:submit:';

    /**
     * 根据权重随机获取模板内容id
     *
     * $data = [
     *      ['content' => 1,'weight' => 1],
     *      ['content' => 2,'weight' => 2],
     * ];
     * @param array $data
     *
     * @return int type
     */
    public static function random(array $data)
    {
        $tmp = [];
        foreach ($data as $datum) {

            for ($i = 0; $i < $datum['weight']; $i++) {
                $tmp[] = $datum['content'];
            }
        }

        return $tmp[array_rand($tmp)];
    }

    /**
     * 可发送时间 9-22
     * @param string $country_code
     * @return array
     */
    public static function sendTimeSuggest(string $country_code)
    {
        $result = \Country::findByAlpha2($country_code);
        if ($result && $result['utc'] != '') {
            $location  = $result['country_name'];
            $utc       = intval($result['utc']);
            $localDate = gmdate('Y-m-d H:i:s', time() + $utc * 3600);

            $localHour = intval(gmdate('H', time() + $utc * 3600));

            if (9 <= $localHour && $localHour <= 22) {
                //处于工作时间  不需要提示
                return [];
            }

            if ($localHour >= 0 && $localHour < 9) {
                //未到工作时间 当天9点发送
                $local_work_date = gmdate('Y-m-d') . ' 09:00:00';
                //当地工作时间换算成北京时间
                $beijingDate = date('Y-m-d H:i:s', strtotime(gmdate('Y-m-d 00:00:00')) + 9 * 3600 + (8 - $utc) * 3600);

            } else {
                //超过工作时间 明天9点发送
                $local_work_date = gmdate('Y-m-d', time() + 24 * 3600) . ' 09:00:00';
                //当地工作时间换算成北京时间
                $beijingDate = date('Y-m-d H:i:s',
                    strtotime(gmdate('Y-m-d 00:00:00')) + 9 * 3600 + (8 - $utc) * 3600 + 24 * 3600);
            }

            $data = [
                'alpha2'          => $result['alpha2'],   //APP端多语言通过alpha2来匹配
                'location'        => $location,
                'local_date'      => $localDate,
                'local_work_date' => $local_work_date,
                'beijing_date'    => $beijingDate
            ];

            return $data;

        } else {
            return [];
        }

    }

    /**
     * @param $companyHashId
     * @param bool $filterDuplicateEmail 过滤重复邮箱
     * @param int $contactLimit
     * @param array $contactIds
     * @param string $scene
     * @param bool $briefOnly true的时候使用 get_company接口，false 的时候使用 company_info 接口，返回值中部分字段取值不一样
     * @return array
     */
    public static function getRecommendArchiveData($companyHashId, $filterDuplicateEmail = false, $contactLimit = 30, $contactIds = [], $scene = \common\library\discovery\Constant::SCENE_NORMAL, $briefOnly = true)
    {
        if(empty($companyHashId)) {
            throw new \ProcessException(\Yii::t('common', 'Parameter error'));
        }
        //目前只有两种类型
        if ($briefOnly) {
            $company = RecommendService::getCompanyByIdV2($companyHashId);
        } else {
            $api = new Company(0, 0);
            $companyInfo = $api->getCompanyInfo($companyHashId);
            $company=[];
            foreach ($companyInfo as $key => $value) {
                if ($key == 'primaryPhone') {
                    $company['phone'] = $value;
                } else {
                    $key = Str::snake($key);
                    $company[$key] = $value;
                }
            }
            $company['homepage'] = $company['homepage']??$company['domain'];
        }
        if (empty($company)) {
            \LogUtil::info("getRecommendArchiveData fail: company profile is empty!");
            return ['company' => [], 'customers' => []];
        }

        if ($scene == \common\library\discovery\Constant::SCENE_CONTACTS) {
            //根据邮箱获取联系人数据--取前100个
            $contactList = !empty($contactIds) ? RecommendService::getContactListByEmails(array_slice($contactIds,0,\common\library\discovery\Constant::CONTACT_LIMIT_NUM)) : [];
        } else {
            $contactList = RecommendService::getRecommendContactsList($companyHashId, 1, $contactLimit, '', false);//'confidence,desc'
            $contactList = $contactList['emails'] ?? [];
        }

        //规模
        $scaleMap = \CustomerOptionService::SCALE_MAP;
        $scaleId  = 0;
        $scale    = $company['employees_max'] ?? 0;
        if ($scale) {
            foreach ($scaleMap as $key => $item) {
                if ($scale >= $item['min'] && $scale <= $item['max']) {
                    $scaleId = $key;
                    break;
                }
            }
        }
        $originId    = \common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_AI_RECOMMEND;
        $country     = $company['country'] ?? '';
        $country     = $country ? (\CountryService::checkNameInTable($country)['alpha2'] ?? '') : '';
        $remark      = $company['description'] ?? '';
        $remark      = mb_substr($remark, 0, 1024, 'utf-8');

        //公司名称为空 暂时用domain 替代 等底层数据修复
        if(isset($company['company_customs_name']) && !empty($company['company_customs_name'])) {
            $name = $company['company_customs_name'];
        }elseif(empty($company['name'])) {
            $domain = $company['domain']??'';
            $name = ucfirst(explode('.',$domain)[0]);
        }else{
            $name = $company['name'];       //直接取name字段
        }

        if (!empty($company['phone'])) {
            $phone = \Util::parsePhoneNumber((string) $company['phone']);
        }

        $province = $company['province'] ?? '';
        $province = (strpos($province,'N/A')!==false) ? '' : $province;

        $socialLinks = [];
        if (isset($company['social_links']) && !empty($company['social_links'])) {
            $socialLinks = is_array($company['social_links'])?$company['social_links']:(json_decode($company['social_links'], true) ?? []);
            foreach ($socialLinks ?? [] as $item) {
                if (isset($item['typeId'])) {
                    $socialLinks[] = [
                        'type' => $item['typeId'],
                        'value' => $item['url'] ?? '',
                    ];
                }
            }
        }

        $companyData = [
            'name'     => $name,
            'origin'   => $originId,
            'origin_list'=> [$originId],
            'country'  => $country,
            'scale_id' => $scaleId,
            'province' => $province,
            'city'     => $company['city'] ?? '',
            'address'  => $company['address'] ?? '',
            'homepage' => $company['homepage'] ?? '',
            'remark'   => $remark,
            'socialLinks' => $socialLinks,
            'logo_url'    => $company['logo_url'] ?? '',
        ];

        if (!empty($phone)) {
            $companyData['tel'] = $phone['national_number'] ?? ltrim($phone['raw_input'], '+');
            $companyData['tel_area_code'] = $phone['country_code'] ?? '';
        }

        $existsEmail = [];
        $customerData = [];
        foreach ($contactList as $contact) {
            $contactName = ($contact['first_name']??'') .' '. ($contact['last_name']??'');
            $contactName = trim($contactName);

            $contactEmail = $contact['value'] ?? '';

            $preName = '';
            if ($contactEmail) {
                list($preName, $contactEmail) = \EmailUtil::getPreFixAndEmail($contactEmail);

                $contactEmail = strtolower($contactEmail);
                if ($filterDuplicateEmail && in_array($contactEmail, $existsEmail)){
                    continue;
                }
                $existsEmail[] = $contactEmail;
            }

            if (isset($contact['position'])) {
                $post = $contact['position'];
            } elseif (isset($contact['type']) && $contact['type'] == 'personal') {
                $post = 'Staff';
            } else {
                $post = 'Department';
            }

            if(strlen($post) > 255) {
                $post = substr($post,0,255);
            }

            $telList = [];
            $customerPhoneNums = $contact['phone_numbers'] ?? (!empty($contact['phone_number']) ? [$contact['phone_number']] : []);
            foreach ($customerPhoneNums as $phoneNum) {
                if (empty($phoneNum)) {
                    continue;
                }

                $phoneNumber = \Util::parsePhoneNumber((string) $phoneNum);
                $telList[] = [
                    $phoneNumber['country_code'] ?? '',
                    $phoneNumber['national_number'] ?? ltrim($phoneNumber['raw_input'], '+'),
                ];
            }

            $customerData[] = [
                'name'     => $contactName ? $contactName : $preName,
                'email'    => $contactEmail,
                'tel_list' => $telList,
                'post'     => $post
            ];
        }
        return ['company' => $companyData, 'customers' => $customerData];
    }

    public static function getRecommendArchiveDataField($clientId, $userId, $companyHashId, $type = \Constants::TYPE_COMPANY, CompanyField $companyFieldFormatter = null, CustomerField $customerFieldFormatter = null, $formatFlag = false)
    {
        if ($companyFieldFormatter === null) {
            //目前只有两种类型
            $companyFieldFormatter = $type == \Constants::TYPE_COMPANY ? new CompanyField($clientId) : new LeadField($clientId);
            $companyFieldFormatter->setFieldUserId($userId);
        }
        if ($customerFieldFormatter === null) {
            $customerFieldFormatter = $type == \Constants::TYPE_COMPANY ? new CustomerField($clientId) : new LeadCustomerField($clientId);
            $customerFieldFormatter->setFieldUserId($userId);
        }

        $filterDuplicateEmail = $type == \Constants::TYPE_COMPANY ? true : false;

        $data         = self::getRecommendArchiveData($companyHashId, $filterDuplicateEmail);
        $company      = $data['company']??[];
        $contactList  = $data['customers']??[];
        $socialLinks  = $company['socialLinks']??[];

        if (empty($company))
        {
            \LogUtil::info("getRecommendArchiveData fail: company profile is empty!");

            $companyField = $companyFieldFormatter->setFormatValueFlag($formatFlag)->format();
            $customerFields[] = $customerFieldFormatter->format();

            return [$companyField, $customerFields];
        }

        $customerFieldFormatter->setFormatValueFlag(true);
        foreach ($contactList as $contact) {
            $customerFields[] = $customerFieldFormatter->format($contact);
        }

        $companyField = $companyFieldFormatter->setFormatValueFlag($formatFlag)->format($company);
        if (empty($customerFields)) {
            $customerFields[] = $customerFieldFormatter->format();
        }
        return [$companyField, $customerFields, $socialLinks];
    }

    /**
     * @param $userId
     * @param $companyHashId
     * @param int $pin
     * @param string $domain
     * 异步调用 self::saveLeadFromCompanyHash()
     */
    public static function asyncSaveLeadFromCompanyHash($userId, $companyHashId, $domain,$pin = 0){

        //判断当前公司否是被建立线索
        $record = new RecommendRecord($companyHashId,$userId);
        if($record->lead && $record->lead_id) {
            return true;
        }

        $log = '/tmp/lead_ai_from_company_hash.log';
        \common\library\CommandRunner::run(
            'lead',
            'saveFromRecommend',
            [
                'userId' => $userId,
                'companyHashId' => $companyHashId,
                'domain' => $domain,
                'pin' => $pin,
            ],
            $log
        );
    }

    /**
     * @param $userId
     * @param $companyHashId
     * @param $domain
     * @param int $pin
     * @return bool|Lead
     * 根据companyHashId保存线索
     */
    public static function saveLeadFromCompanyHash($userId, $companyHashId, $domain, $pin = 0)
    {
        $user = \User::getUserObject($userId);
        $clientId = $user->getClientId();

        $result = self::getRecommendArchiveData($companyHashId);
        $leadData = $result['company']??[];
        $customerData = $result['customers']??[];
        if (empty($leadData)){
            \LogUtil::info("leadData is empty! userId:$userId companyHashId=$companyHashId");
            return false;
        }

        $v = new \Validator();
        $v->setConfig(\CustomerOptionService::getValidatorRule());
        $v->setInputData($leadData);
        $v->validateConfig($v->getConfig(\CustomerOptionService::VALIDATE_RULE_OF_LEAD));

        $lead = new Lead($clientId);
        $lead->setOperatorUserId($user->getUserId());
        $lead->is_archive = 1;
        $lead->name = $leadData['name'];
        $lead->company_name = $leadData['name'];
        $lead->origin_list = $leadData['origin_list'] ?? [];
        $lead->country = $leadData['country'] ?? '';
        $lead->scale_id = $leadData['scale_id'] ?? 0;
        $lead->tel = $leadData['tel'] ?? '';
        $lead->province = $leadData['province'] ?? '';
        $lead->city = $leadData['city'] ?? '';
        $lead->address = $leadData['address'] ?? '';
        $lead->homepage = $leadData['homepage'] ?? '';
        $lead->remark = $leadData['remark'] ?? '';
        $lead->create_user_id = $user->getUserId();
        $lead->company_hash_id = $companyHashId;
        $lead->addUser($user->getUserId());

        $customerList = [];
        foreach ($customerData as $contact) {
            $v->setInputData($contact);
            $v->validateConfig($v->getConfig(\CustomerOptionService::VALIDATE_RULE_OF_LEAD_CUSTOMER));
            $customer = (new LeadCustomer($clientId));
            $customer->name = $contact['name'] ?? '';
            $customer->email = $contact['email'];
            $customer->post = $contact['post'] ?? '';
            $customer->tel_list = $contact['tel_list'] ?? [];
            $customerList[] = $customer;
        }

        if(empty($customerList)){//默认要有一个位置...
            $customer = new LeadCustomer($clientId);
            $customer->main_customer_flag = 1;
            $customer->email = '';
            $customerList = [$customer];
        }

        $lead->setCustomerList($customerList);
        $result = $lead->save();

        \LogUtil::info("save status: {$result}");
        if (!$result) {
            return false;
        }

        //关注
        $pin && \Pin::it(\Pin::TYPE_LEAD, $lead->lead_id, $user);

        return $lead;
    }

    public static function sendEdm(
        $userId,
        $subject,
        array $mailSubject,
        $content,
        $senderMail,
        $senderName = '',
        array $sendToAddress = [],
        $planSendTime = '',
        array $inlineImageList = [],
        array $imageSrcList = [],
        array $attachment = [])
    {
        if (empty($sendToAddress)) {
            return false;
        }

        // commit 后的逻辑中，剩余营销数计算出现了溢出的情况（减去消耗数后变为负数），
        // Counter 能保证正确执行结果，但是执行前的判断不能保证准确的，例如，
        // 实例 A 和 实例 B 额度都为 6，但是 A 执行成功后有消耗数，B 没有同步更新，再消耗 6 就溢出了
        // 可以改进 Counter 加乐观锁；也可以在此处控制流量进入
        $key = self::SUBMIT_AI_EDM_TASK_KEY . $userId;
        if (!\Yii::app()->cache->executeCommand('SET', [$key, 1, 'EX', 5, 'NX'])) {
            throw new \RuntimeException(\Yii::t('common', 'The operation is too frequent, please try again later'));
        }

        $user = \User::getUserObject($userId);
        $clientId = $user->getClientId();
        $task = new EdmTask($clientId);
        $task->setOpUserId($userId);
        $task->setType(EdmTask::TYPE_AI);
        $task->setAddressType(EdmTask::ADDRESS_TYPE_AI_RECOMMEND);
        $task->setSubject($subject);
        $task->setMailSubjects($mailSubject);
        $task->setSenderMail($senderMail);
        $task->setSenderName($senderName);
        $task->setPlanSendTime($planSendTime);
        $task->setEdmFilterFlag(EdmTask::FILTER_ALL);
        $task->setAddress($sendToAddress);
        $task->setInlineImageList($inlineImageList);
        $task->setParamMailContent($content);
        $task->setAttachmentList($attachment);
        $task->draft_flag = EdmTask::DRAFT_FLAG_NO;
        if (!$task->plan_flag) {
            $task->commit();
        } else {
            $task->start_time = $task->plan_send_time; // start_time 影响排序，先给一个预先的时间
            $task->save();
        }
        return $task->task_id;
    }

    /**
     * 是否到了采购高峰期
     * @param array $seasonMonths
     * @return bool
     */
    public static function isPurchaseRush(array $season_months = [])
    {
        $currentMonth =  intval(gmdate('m'));

        if($currentMonth <= 9) {
            $range = range($currentMonth,$currentMonth+3);
        }else{
            $range = range($currentMonth,12);
            $append = array_slice([1,2,3],0,4-count($range));
            $range = array_merge( $range,$append);
        }

        if(!empty(array_intersect($range,$season_months))){
            $isPurchaseRush = true;
        }else{
            $isPurchaseRush = false;
        }

        return $isPurchaseRush;
    }

    /**
     * 该AI的客户是否是即将移入公海的客户
     * @param $clientId
     * @param $userId
     * @param $companyId
     * @return bool
     */
    public static function isWillMoveToPublic($clientId, $userId, $companyId)
    {
        $type  = Constant::WILL_MOVE_TO_PUBLIC;
        $where = "client_id = {$clientId} AND user_id = {$userId} AND type = {$type} AND company_id= {$companyId} AND read_flag=0";
        $sql = "SELECT count(1) FROM tbl_ai_activate_company WHERE {$where}";
        $isWillMoveToPublic = \PgActiveRecord::getDbByUserId($userId)->createCommand($sql)->queryScalar();
        return $isWillMoveToPublic ? true : false;
    }

    /**
     * 该AI的线索是否是跟进线索
     * @param $clientId
     * @param $userId
     * @param $leadId
     * @return bool
     */
    public static function isFollowUpLead($clientId, $userId, $leadId)
    {
        $where = "client_id = {$clientId} AND user_id = {$userId}  AND lead_id= {$leadId} AND read_flag=0";
        $sql = "SELECT count(1) FROM tbl_ai_activate_lead WHERE {$where}";
        $isFollowUpLead = \PgActiveRecord::getDbByUserId($userId)->createCommand($sql)->queryScalar();
        return $isFollowUpLead ? true : false;
    }


    public static function positionColor($position)
    {
        //匹配不到用绿色--如果职务为空用灰色
        //red green yellow gray
        if(!$position || $position == 'Staff') return 'gray';
        $colorMap = [
            "Director" => "yellow",
            "President"  => "yellow",
            "Manager" => "green",
            "Vice President" => "yellow",
            "Partner" => "yellow",
            "Executive Director" => "yellow",
            "Owner" => "yellow",
            "Coordinator" => "green",
            "General Manager" => "yellow",
            "Principal" => "yellow",
            "Real Estate" => "green",
            "Attorney" => "green",
            "Chair" => "yellow",
            "Secretary" => "green",
            "Managing Director" => "green",
            "Sales Manager" => "green",
            "Design" => "green",
            "Office Manager" => "green",
            "CEO" => "yellow",
            "Administrative Assistant" => "green",
            "Assistant" => "green",
            "Executive" => "yellow",
            "Project Manager" => "green",
            "Volunteer" => "green",
            "Administration" => "green",
            "Administrator" => "green",
            "Founder" => "yellow",
            "Treasurer" => "green",
            "Specialist" => "green",
            "Operations Manager" => "green",
            "Consultant" => "green",
            "Representative" => "green",
            "Realtor" => "green",
            "Customer Service" => "green",
            "Account Manager" => "green",
            "Senior Vice President" => "yellow",
            "Superintendent" => "green",
            "Chairman" => "yellow",
            "Marketing Manager" => "green",
            "Officer" => "green",
            "Technical" => "green",
            "Branch Manager" => "green",
            "Editor" => "green",
            "Teacher" => "green",
            "Investor" => "green",
            "Program Director" => "green",
            "Program Manager" => "green",
            "Supervisor" => "green",
            "Coach" => "green",
            "Account Executive" => "green",
            "Chief" => "yellow",
            "Business Development Manager" => "green",
            "Sales Representative" => "green",
            "Leader" => "yellow",
            "Executive Vice President" => "yellow",
            "Head Coach" => "green",
            "Chief Financial Officer" => "yellow",
            "Administrative" => "green",
            "Organizer" => "green",
            "Director Of Marketing" => "green",
            "Public Relations" => "green",
            "Broker" => "green",
            "Executive Assistant" => "green",
            "Chief Executive Officer" => "yellow",
            "Service Manager" => "green",
            "Managing Partner" => "yellow",
            "Program Coordinator" => "green",
            "Pastor" => "green",
            "Controller" => "green",
            "Regional Sales Manager" => "green",
            "Advisor" => "green",
            "Social Media" => "green",
            "Lawyer" => "green",
            "Business Manager" => "green",
            "Principal Investigator" => "green",
            "Instructor" => "green",
            "Marketing Director" => "green",
            "Sales Consultant" => "green",
            "Director Of Sales" => "green",
            "Professor" => "green",
            "Board Member" => "green",
            "Co-Founder" => "yellow",
            "Director Of Development" => "green",
            "Chief Operating Officer" => "yellow",
            "Media Relations" => "green",
            "Manufacturing" => "red",
            "Sales Associate" => "green",
            "Regional Manager" => "yellow",
            "Shareholder" => "green",
            "CFO" => "yellow",
            "Property Manager" => "green",
            "Communications Manager" => "green",
            "Designer" => "green",
            "Librarian" => "green",
            "Investment Advisor Representative" => "green",
            "Athletic Director" => "green",
            "Commissioner" => "green",
            "Clerk" => "green",
            "Assistant Director" => "green",
            "Sales Director" => "green",
            "Associate Director" => "green",
            "Logistics" => "green",
            "Resident" => "green",
            "Counselor" => "green",
            "Reporter" => "green",
            "Attending" => "green",
            "Recruiter" => "green",
            "Volunteer Coordinator" => "green",
            "Development Director" => "green",
            "Director Of Business Development" => "green",
            "Loan Officer" => "green",
            "Dealer" => "red",
            "Producer" => "yellow",
            "Marketing Coordinator" => "green",
            "Speaker" => "green",
            "Sales And Marketing" => "green",
            "Receptionist" => "green",
            "Production Manager" => "green",
            "Product Manager" => "green",
            "Senior Manager" => "green",
            "Webmaster" => "green",
            "Publisher" => "green",
            "Artist" => "green",
            "Vice President Of Sales" => "yellow",
            "Intern" => "green",
            "Real Estate Agent" => "green",
            "Senior Consultant" => "green",
            "Architect" => "green",
            "Finance Manager" => "green",
            "Store Manager" => "green",
            "Customer Service Representative" => "green",
            "Communications Director" => "green",
            "Regional Director" => "yellow",
            "Financial Advisor" => "green",
            "Photographer" => "green",
            "Parts Manager" => "green",
            "Senior Director" => "green",
            "Engineer" => "green",
            "Finance Director" => "green",
            "Registrar" => "green",
            "Staff Writer" => "green",
            "Project Coordinator" => "green",
            "Management Team" => "green",
            "Office Administrator" => "green",
            "Recruiting" => "green",
            "Chief Executive" => "yellow",
            "Sales Executive" => "green",
            "Bookkeeper" => "green",
            "Is Associate" => "green",
            "Development Manager" => "green",
            "Service Advisor" => "green",
            "Director Of Human Resources" => "green",
            "Buyer" => "red",
            "Team Leader" => "green",
            "Contractor" => "green",
            "Operations Director" => "green",
            "Legal Assistant" => "green",
            "National Sales Manager" => "green",
            "Billing" => "green",
            "Technician" => "green",
            "Planner" => "green",
            "Analyst" => "green",
            "Development Officer" => "green",
            "Creative Director" => "green",
            "Vice President Sales" => "yellow",
            "Manufacturer" => "red",
            "Developer" => "green",
            "Assistant Principal" => "green",
            "Trading" => "red",
            "Technical Support" => "green",
            "Programming" => "green",
            "Territory Manager" => "green",
            "Platform" => "green",
            "Athletics" => "green",
            "COO" => "yellow",
            "Communications Coordinator" => "green",
            "Distributor" => "red",
            "Facilitator" => "green",
            "Vp Sales" => "yellow",
            "Builder" => "green",
            "Tourism" => "green",
            "Assistant Vice President" => "green",
            "Deputy Director" => "red",
            "City Manager" => "green",
            "Writer" => "green",
            "Director Of Finance" => "green",
            "Human Resources Manager" => "green",
            "Trainer" => "green",
            "Fundraiser" => "green",
            "Customer Service Manager" => "green",
            "Leadership Team" => "green",
            "Hr Manager" => "green",
            "Senior Project Manager" => "green",
            "Accounting Manager" => "green",
            "Contributor" => "green",
            "Services Director" => "green",
            "Listing Agent" => "green",
            "Area Manager" => "green",
            "Associate Professor" => "green",
            "Managing Editor" => "green",
            "Captain" => "yellow",
            "Director Of Sales And Marketing" => "green",
            "staff" => "green"
        ];

        return $colorMap[$position] ?? 'green';
    }


    /**
     * 根据client 获取当前用户公司的推荐数量
     * @param int $clientId
     * @param int $userId
     * @param string $start_date
     * @param string $type
     * @return array|\CDbDataReader
     * @throws \ProcessException
     */
    public static function getRecommendCompanyStatisticsByType(int $clientId, int $userId, string $start_date, $type='week')
    {

        if(empty($clientId)) {
            throw new \ProcessException('参数异常');
        }

        try {
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if ($type == 'week') {
                $sql = "SELECT date_sub(date(create_time),INTERVAL DAYOFWEEK(date(create_time)) - 1 DAY) as day,";
            } elseif ($type == 'month') {
                $sql = "SELECT concat(date_format(LAST_DAY(create_time),'%Y-%m-'),'01') as day,";
            } else {
                throw new \RuntimeException(\Yii::t('ai', "No such type"));
            }
            $sql .= "count(1) as `count` 
            FROM tbl_recommend_record 
            WHERE client_id=:client_id AND user_id=:user_id  AND create_time >=:start_date 
            GROUP BY day 
            ORDER BY day";
            $params = [
                ':client_id' => $clientId,
                ':start_date' => $start_date,
                ':user_id' => $userId
            ];
            $data =  $db->createCommand($sql)->queryAll(true,$params);

            $return = [];
            foreach ($data as $datum) {
                $return[$datum['day']] = $datum;
            }

            return $return;
        }catch (\Exception $exception) {
            echo $exception->getMessage();
            \LogUtil::error('获取当前client AI推荐统计数量异常'.$exception->getMessage());
            throw $exception;
        }
    }

    /**
     * 获取当前公司建档统计
     * @param int $clientId
     * @param int $userId
     * @param string $startDate
     * @param string $type
     * @return array|\CDbDataReader
     * @throws \ProcessException
     */
    public static function getClientArchiveStatisticsByType(int $clientId, int $userId, string $startDate, $type='week')
    {
        if(empty($clientId)) {
            throw new \ProcessException('参数异常');
        }
        try {
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            if ($type == 'week') {
                $sql = "SELECT date_sub(date,INTERVAL DAYOFWEEK(date) - 1 DAY) as day,";
            } elseif ($type == 'month') {
                $sql = "SELECT concat(date_format(LAST_DAY(date),'%Y-%m-'),'01') as day,";
            } else {
                throw new \RuntimeException(\Yii::t('ai', "No such type"));
            }
            $sql .= "sum(customer_add_count) as `count` 
            FROM tbl_user_statistics_day 
            WHERE client_id=:client_id AND user_id=:user_id AND date >=:start_date 
            GROUP BY day 
            ORDER BY day
            ";
            $params = [
                ':client_id' => $clientId,
                ':start_date' => $startDate,
                ':user_id' => $userId
            ];
            $data =  $db->createCommand($sql)->queryAll(true,$params);
            $return = [];
            foreach ($data as $datum) {
                $return[$datum['day']] = $datum;
            }

            return $return;
        }catch (\Exception $exception) {
            echo $exception->getMessage();
            \LogUtil::error('获取当前client 员工建线索统计异常'.$exception->getMessage());
            throw $exception;
        }
    }

    /**
     * 获取当前公司线索统计
     * @param int $clientId
     * @param int $userId
     * @param string $startDate
     * @param string $type
     * @return array|\CDbDataReader
     * @throws \ProcessException
     */
    public static function getClientLeadStatisticsByType(int $clientId, int $userId, string $startDate, string $type = 'week')
    {

        if(empty($clientId)) {
            throw new \ProcessException('参数异常');
        }
        try {

            $db = \PgActiveRecord::getDbByClientId($clientId);

            if ($type == 'week') {
                $sql = "SELECT (create_time::date - EXTRACT(DOW FROM create_time)::integer) as day,";
            } elseif ($type == 'month') {
                $sql = "SELECT (create_time::date - EXTRACT(day FROM create_time)::integer +1) as day,";
            } else {
                throw new \RuntimeException(\Yii::t('ai', "No such type"));
            }

            $sql .= "count(1) as count 
            FROM tbl_lead 
            WHERE client_id=:client_id 
                and user_id@>array[:user_id::bigint] 
                and create_time>:start_date 
                and origin!=3 
            GROUP BY day 
            ORDER BY day 
            ";
            $params = [
                ':client_id' => $clientId,
                ':start_date' => $startDate,
                ':user_id' => $userId
            ];
            $data =  $db->createCommand($sql)->queryAll(true,$params);
            $return = [];

            foreach ($data as $datum) {
                $return[$datum['day']] = $datum;
            }

            return $return;
        }catch (\Exception $exception) {
            echo $exception->getMessage();
            \LogUtil::error('获取当前client AI推荐统计数量异常'.$exception->getMessage());
            throw $exception;
        }
    }


    /**
     * 获取client 开始使用AI的日期
     * @param int $client_id
     * @return bool|false|string
     * @throws \ProcessException
     */
    public static function getClientAIStartDate(int $client_id)
    {
        if(empty($client_id)) {
            throw new \ProcessException('参数异常');
        }
        try {
            $db = \ProjectActiveRecord::getDbByClientId($client_id);

            $sql = "SELECT min(create_time) FROM tbl_recommend_record WHERE client_id=:client_id";

            $data = $db->createCommand($sql)->queryColumn([':client_id' => $client_id]);

            $startDate = empty($data[0]) ? date('Y-m-d') : date('Y-m-d',strtotime($data[0]));
            return $startDate;

        }catch (\Exception $exception) {
            \LogUtil::error('获取当前client AI起始时间异常'.$exception->getMessage());
            throw $exception;
        }
    }

    /**
     * 获取客户推荐公司
     * @param $user_id
     * @param int $page
     * @param int $page_size
     * @return array|bool|\CDbDataReader
     * @throws \ProcessException
     */
    public static function  getRecentRecommend(int $user_id,int $page = 1, int $page_size = 1)
    {
        if(empty($user_id)) {
            throw new \ProcessException('参数异常');
        }
        try {
            $db = \ProjectActiveRecord::getDbByUserId($user_id);

            $limit = $page_size;
            $offset = ($page-1) * $page_size;

            $sql = "SELECT company_hash_id,create_time,task_id FROM tbl_recommend_record WHERE user_id=:user_id "
                 . "AND send_recommend_mail=1 ORDER BY create_time DESC limit {$limit} offset {$offset}";

            $data = $db->createCommand($sql)->queryAll(true,[':user_id' => $user_id]);

            return $data;

        }catch (\Exception $exception) {
            \LogUtil::error('获取当前user 最近推荐公司数据异常'.$exception->getMessage());
            throw $exception;
        }
    }

    /**
     * 获取客户推荐公司数量
     * @param $user_id
     * @param int $page
     * @param int $page_size
     * @return array|bool|\CDbDataReader
     * @throws \ProcessException
     */
    public static function  getRecentRecommendCount(int $user_id)
    {
        if(empty($user_id)) {
            throw new \ProcessException('参数异常');
        }
        try {
            $db = \ProjectActiveRecord::getDbByUserId($user_id);

            $sql = "SELECT count(1) FROM tbl_recommend_record WHERE user_id=:user_id AND send_recommend_mail=1";

            $data = $db->createCommand($sql)->queryScalar([':user_id' => $user_id]);
            return $data;
        }catch (\Exception $exception) {
            \LogUtil::error('获取当前user 最近推荐公司数量异常'.$exception->getMessage());
            throw $exception;
        }
    }

    /**
     * @param int $user_id
     * @return bool|\CDbDataReader|int|mixed|string
     * @throws \ProcessException
     */
    public static  function getUserSendEdmCount(int $user_id)
    {
        if(empty($user_id)) {
            throw new \ProcessException('参数异常');
        }
        try {
            $sql = "SELECT SUM(finish_count) as total FROM tbl_group_mail_task WHERE user_id=:user_id AND type=2";
            $result = \ProjectActiveRecord::getDbByUserId($user_id)->createCommand($sql)->queryScalar([':user_id' => $user_id]);

            return empty($result) ? 0 : $result;

        }catch (\Exception $exception) {
            \LogUtil::error('获取当前user send edm数据异常'.$exception->getMessage());
            throw $exception;
        }
    }


    /**
     * @param int $user_id
     * @return bool|\CDbDataReader|int|mixed|string
     * @throws \ProcessException
     */
    public static  function getUserArchiveCount(int $user_id)
    {
        if(empty($user_id)) {
            throw new \ProcessException('参数异常');
        }
        try {
            $db = \ProjectActiveRecord::getDbByUserId($user_id);

            $sql = "SELECT count(1) FROM tbl_recommend_record WHERE user_id=:user_id AND archive=1";

            return  $db->createCommand($sql)->queryScalar([':user_id' => $user_id]);
        }catch (\Exception $exception) {
            \LogUtil::error('获取当前user 建档数据异常'.$exception->getMessage());
            throw $exception;
        }
    }

    /**
     * @param int $user_id
     * @return bool|\CDbDataReader|int|mixed|string
     * @throws \ProcessException
     */
    public static  function getUserRecommendCount(int $user_id)
    {
        if(empty($user_id)) {
            throw new \ProcessException('参数异常');
        }
        try {
            $db = \ProjectActiveRecord::getDbByUserId($user_id);

            $sql = "SELECT count(1) FROM tbl_recommend_record WHERE user_id=:user_id";

            return  $db->createCommand($sql)->queryScalar([':user_id' => $user_id]);
        }catch (\Exception $exception) {
            \LogUtil::error('获取当前user 推荐数据异常'.$exception->getMessage());
            throw $exception;
        }
    }

    /**
     * @param int $user_id
     * @return bool|\CDbDataReader|int|mixed|string
     * @throws \ProcessException
     */
    public static  function getUserOpenEdmCount(int $user_id)
    {
        if(empty($user_id)) {
            throw new \ProcessException('参数异常');
        }
        try {

            $sql = "SELECT SUM(view_ucount) as total FROM tbl_group_mail_task WHERE user_id=:user_id AND type=2";

            $result =  \ProjectActiveRecord::getDbByUserId($user_id)->createCommand($sql)->queryScalar([':user_id' => $user_id]);
            return empty($result) ? 0 : $result;

        }catch (\Exception $exception) {
            \LogUtil::error('获取当前user open edm数据异常'.$exception->getMessage());
            throw $exception;
        }
    }

    /**
     * @param int $user_id
     * @return bool|\CDbDataReader|int|mixed|string
     * @throws \ProcessException
     */
    public static  function getUserReplyEdmCount(int $user_id)
    {
        if(empty($user_id)) {
            throw new \ProcessException('参数异常');
        }
        try {

            $sql = "SELECT count(1) FROM tbl_recommend_email_statistics WHERE user_id=:user_id AND edm_reply>0 ";
            return \ProjectActiveRecord::getDbByUserId($user_id)->createCommand($sql)->queryScalar([':user_id' => $user_id]);
        }catch (\Exception $exception) {
            \LogUtil::error('获取当前user reply edm数据异常'.$exception->getMessage());
            throw $exception;
        }
    }

    /**
     * @param string $companyHashId
     * @param int $group
     * @param int $page
     * @param int $size
     * @param string $sort
     * @return array
     */
    public static function getQualityEmailList(
        string $companyHashId = '',
        int $group = Constant::GROUP_OPEN,
        int $page = 1,
        int $size = 30,
        string $sort = ''
    ){
        $contactsList = RecommendService::getRecommendContactsList($companyHashId, $page, $size, $sort, false);
        if(empty($contactsList['emails'])) {
            return [];
        }
        $contactEmailList = array_column($contactsList['emails'], 'value');
        $contactEmailList = array_unique($contactEmailList);
        return $contactEmailList;
    }


    public static function isGuessEmail(
        string $companyHashId = '',
        string $email = '',
        int $page = 1,
        int $size = 30
    )
    {
        $contactsList = RecommendService::getRecommendContactsList($companyHashId, $page, $size, '');
        $emailList = $contactsList['emails'] ?? [];
        $contactEmailList = array_column($emailList, 'value', '');
        return in_array($email, $contactEmailList) ? false : true;
    }


    public static function getFirstContact(int $user_id)
    {
        if(empty($user_id)) {
            throw new \ProcessException('参数异常');
        }
        try {
            $db = \ProjectActiveRecord::getDbByUserId($user_id);

            $sql = "SELECT count(1) FROM tbl_recommend_email_statistics WHERE user_id=:user_id AND transform_flag=0  AND  receive<2 AND (send>0 OR edm_send>0) ";

            return $db->createCommand($sql)->queryScalar([':user_id' => $user_id]);
        }catch (\Exception $exception) {
            \LogUtil::error('获取第一次接触数据异常'.$exception->getMessage());
            throw $exception;
        }
    }


    public static function getSecondContact(int $user_id)
    {
        if(empty($user_id)) {
            throw new \ProcessException('参数异常');
        }
        try {
            $db = \ProjectActiveRecord::getDbByUserId($user_id);

            $sql = "SELECT count(1) FROM tbl_recommend_email_statistics WHERE user_id=:user_id AND transform_flag=0  AND  receive >= 2 AND receive<=4 ";

            return $db->createCommand($sql)->queryScalar([':user_id' => $user_id]);
        }catch (\Exception $exception) {
            \LogUtil::error('获取取得好感数据异常'.$exception->getMessage());
            throw $exception;
        }
    }

    public static function getThirdContact(int $user_id)
    {
        if(empty($user_id)) {
            throw new \ProcessException('参数异常');
        }
        try {
            $db = \ProjectActiveRecord::getDbByUserId($user_id);

            $sql = "SELECT count(1) FROM tbl_recommend_email_statistics WHERE user_id=:user_id AND transform_flag=0  AND  receive > 4 ";

            return $db->createCommand($sql)->queryScalar([':user_id' => $user_id]);
        }catch (\Exception $exception) {
            \LogUtil::error('获取亲密接触数据异常'.$exception->getMessage());
            throw $exception;
        }
    }

    public static function getTransformed(int $user_id)
    {
        if(empty($user_id)) {
            throw new \ProcessException('参数异常');
        }
        try {
            $db = \ProjectActiveRecord::getDbByUserId($user_id);

            $sql = "SELECT count(1) FROM tbl_recommend_email_statistics WHERE user_id=:user_id AND transform_flag>0";

            return $db->createCommand($sql)->queryScalar([':user_id' => $user_id]);
        }catch (\Exception $exception) {
            \LogUtil::error('获取已转化数据异常'.$exception->getMessage());
            throw $exception;
        }
    }

    public static  function getEmailStatisticsByEmails(array $emails,int $user_id)
    {

        if(empty($emails) || empty($user_id)) {
            throw new \RuntimeException(\Yii::t('common', 'Parameter error'));
        }

        try {
            $db = \ProjectActiveRecord::getDbByUserId($user_id);

            array_walk($emails,function (&$value){
                $value = \Util::escapeDoubleQuoteSql($value);
                $value = "'$value'";
            });
            $emailsSting = implode(',',$emails);
            $sql = "SELECT * FROM tbl_recommend_email_statistics WHERE user_id=:user_id AND email in({$emailsSting}) order by update_time";

            return $db->createCommand($sql)->queryAll(true,[':user_id' => $user_id]);
        }catch (\Exception $exception) {

            throw $exception;
        }

    }

    /** 获取不感兴趣的company_hash_id
     * @param int $userId
     * @param array $companyHashIds
     * @return array
     * @throws \Exception
     */
    public static function getNotInterestCompanyHashIds(int $userId, array $companyHashIds)
    {
        if (empty($companyHashIds)) {
            return [];
        }
        try {
            $db = \ProjectActiveRecord::getDbByUserId($userId);
            array_walk($companyHashIds,function (&$value){
                $value = "'$value'";
            });

            $companyHashIdsSting = implode(',', $companyHashIds);
            $sql = "SELECT company_hash_id FROM tbl_recommend_record WHERE user_id=:user_id AND interest=0 AND company_hash_id in ({$companyHashIdsSting})";

            return $db->createCommand($sql)->queryColumn([':user_id' => $userId]);
        }catch (\Exception $exception) {
            throw $exception;
        }
    }

    public static function isArchiveInCrm(string $company_hash_id,int $client_id)
    {
        $sql = "select count(1) from tbl_company where company_hash_id='{$company_hash_id}' AND client_id={$client_id} AND is_archive=1";
        return \PgActiveRecord::getDbByClientId($client_id)->createCommand($sql)->queryScalar();
    }

    //判断ai推荐公司查看数是否超限 true 超过限制 false 未超过限制
    public static function recommendCountIstLimit($user)
    {
        $redis = \Yii::app()->cache;
        $date = date('Ymd');
        $key = \common\library\ai\service\RecommendService::IGNORE_COUNT_PREFIX . "{$user->getClientId()}:{$user->getUserId()}:{$date}";
        if (($redis->executeCommand('GET', [$key]) ?? 0) > \common\library\ai\service\RecommendService::IGNORE_COUNT_LIMIT) {
            return true;
        }

        return false;
    }

    public static function recommendCountAdd($user)
    {
        $redis = \Yii::app()->cache;
        $date = date('Ymd');
        $key = \common\library\ai\service\RecommendService::IGNORE_COUNT_PREFIX . "{$user->getClientId()}:{$user->getUserId()}:{$date}";
        $redis->executeCommand('INCR', [$key]);
        $redis->executeCommand('EXPIRE', [$key, 24 * 3600]);
    }

    // 拉取三方联系人数据
    public static function pullContactsData($clientId, $userId, $companyHashId)
    {
        if(!$clientId || !$userId || !$companyHashId) {
            throw new \ProcessException('参数异常');
        }
        try {
            $company = new \common\library\discovery\api\Company($clientId, $userId);
            $domain = $company->getCompanyDomain($companyHashId);

            if ($domain) {
                \common\library\ai\service\RecommendService::getEmailByDomain($domain);
            }

        }catch (\Exception $exception) {
            \LogUtil::error('拉取三方联系人数据异常'.$exception->getMessage());
            throw $exception;
        }
    }

}
