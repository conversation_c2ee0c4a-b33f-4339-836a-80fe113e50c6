<?php
/**
 * Copyright (c) 2012 - 2018 Xiaoman.All Rights Reserved
 * Author: nuxse
 * Data: 2018/8/30
 */

namespace common\library\ai\service;


use common\library\ai\Constant;
use common\library\ai\recommend\RecommendRecord;

/**
 * 推荐记录
 * + 数据统计
 * Class RecommendRecordService
 * @package common\library\ai\service
 */
class RecommendRecordService
{
    CONST REFER_TYPE_EDM = 1;    //邮件营销EDM
    CONST REFER_TYPE_MAIL = 2;  //普邮群发单显

    // 发送邮件或者建档完成后推荐有dx来源的数据
    const SOURCE_TYPE_AI = 'ai';
    const SOURCE_TYPE_DX = 'dx';
    /**
     * 已查看
     * @param string $company_hash_id
     * @param int|null $user_id
     */
    public static function view(string $company_hash_id, int $user_id = null)
    {
        $recommendRecord = new RecommendRecord($company_hash_id,$user_id);
        $recommendRecord->save();
    }

    /**
     * 收藏公司
     * @param string $company_hash_id
     * @param int|null $user_id
     */
    public static function pin(string $company_hash_id, int $user_id = null)
    {
        $recommendRecord = new RecommendRecord($company_hash_id,$user_id);
        $recommendRecord->pin = RecommendRecord::PIN;
        $recommendRecord->save();
    }

    /**
     * 不感兴趣
     * @param string $company_hash_id
     * @param int|null $user_id
     */
    public static function disinterest(string $company_hash_id,int $user_id=null)
    {
        $recommendRecord = new RecommendRecord($company_hash_id,$user_id);
        $recommendRecord->interest = RecommendRecord::DISINTEREST;
        $recommendRecord->save();
    }


    /**
     * 加入线索
     * @param string $company_hash_id
     * @param int|null $user_id
     * @param int $pin
     */
    public static function lead(string $company_hash_id,int $lead_id, int $user_id=null, int $pin = 0)
    {
        $recommendRecord = new RecommendRecord($company_hash_id,$user_id);
        $recommendRecord->lead = RecommendRecord::LEAD;
        $recommendRecord->lead_id = $lead_id;
        $recommendRecord->pin = !empty($pin) ? RecommendRecord::PIN : 0;
        $recommendRecord->save();
    }

    /**
     * 建档
     * @param string $company_hash_id
     * @param int|null $user_id
     */
    public static function archive(string $company_hash_id, int $company_id, int $user_id = null)
    {
        $recommendRecord = new RecommendRecord($company_hash_id,$user_id);
        $recommendRecord->archive = RecommendRecord::ARCHIVE;
        $recommendRecord->company_id = $company_id;
        $recommendRecord->save();
    }

    /**
     * 发送邮件
     * @param string $company_hash_id
     * @param int $company_id
     * @param int $lead_id
     * @param int $task_id
     * @param int $scene_type
     * @param int $content_id
     * @param string $platform
     * @param int $subject_id
     * @param int|null $user_id
     */
    public static function sendMail(
        string $company_hash_id,
        int $company_id,
        int $lead_id,
        int $task_id,
        int $scene_type,
        int $content_id,
        $platform,
        int $subject_id = 0,
        int $user_id = null
    )
    {
        $recommendRecord = new RecommendRecord($company_hash_id,$user_id);
        $recommendRecord->send_recommend_mail = RecommendRecord::SEND_RECOMMEND_MAIL;
        $recommendRecord->task_id = $task_id;
        $recommendRecord->subject_id = $subject_id;
        $recommendRecord->content_id = $content_id;
        $recommendRecord->platform = $platform;
        $recommendRecord->save();

        //记录邮件的发送日志
        self::aiMailRecord($company_hash_id, $company_id, $lead_id, self::REFER_TYPE_EDM, $task_id, $scene_type, $content_id, $subject_id, $user_id);

    }


    /**
     * 邮件发送的记录--包括普邮和EDM(开发信和营销信)
     * @param string $company_hash_id
     * @param int $company_id
     * @param int $lead_id
     * @param int $refer_type
     * @param int $refer_id
     * @param int $scene_type
     * @param int $content_id
     * @param int $subject_id
     * @param int|null $user_id
     */
    public static function aiMailRecord(
        string $company_hash_id,
        int $company_id,
        int $lead_id,
        int $refer_type,
        int $refer_id,
        int $scene_type,
        int $content_id,
        int $subject_id = 0,
        int $user_id = null
    )
    {
        if (!empty($user_id)) {
            \User::setLoginUserById($user_id);
        }

        $userId = \User::getLoginUser()->getUserId();
        $clientId = \User::getLoginUser()->getClientId();

        $record = new \AiMailRecord();
        $record->user_id = $userId;
        $record->client_id = $clientId;
        $record->company_hash_id = $company_hash_id;
        $record->company_id = $company_id;
        $record->lead_id = $lead_id;
        $record->scene_type = $scene_type;
        $record->group_type = self::getGroupType($refer_type, $scene_type);
        $record->content_id = $content_id;
        $record->subject_id = $subject_id;
        $record->refer_type = $refer_type;
        $record->refer_id = $refer_id;
        $record->create_time = date('Y-m-d H:i:s');
        $record->update_time = date('Y-m-d H:i:s');
        $record->save();
    }


    /**
     * AI 统计结果(排除不感兴趣的统计)
     * @param int $user_id
     * @return array
     * @throws \CException
     */
    public static function statistics(int $user_id)
    {
        $sql = 'SELECT * FROM tbl_recommend_record WHERE user_id=:user_id AND interest=1';

        $result =  \ProjectActiveRecord::getDbByUserId($user_id)->createCommand($sql)->queryAll(true,[
            'user_id' => $user_id
        ]);

        $recommend = count($result);
        $sendRecommendMail = 0;
        $pin = 0;
        $archive = 0;
        $lead = 0;


        foreach ($result as $item) {

            if($item['send_recommend_mail']) {
                $sendRecommendMail ++;
            }

            if($item['pin']) {
                $pin ++;
            }

            if($item['archive']) {
                $archive ++;
            }

            if($item['lead']) {
                $lead ++;
            }

        }
        return [
            'recommend' => $recommend,
            'send_recommend_mail' => $sendRecommendMail,
            'pin' => $pin,
            'archive'=> $archive,
            'lead' => $lead
        ];

    }

    /**
     * 记录开发信|营销信的收件人列表日志
     *
     * @param int $client_id
     * @param int $user_id
     * @param string $company_hash_id
     * @param int $company_id
     * @param int $lead_id
     * @param int $type
     * @param int $content_id
     * @param int $subject_id
     * @param array $default_receiver
     * @param array $user_receiver
     */
    public static function saveMailLog(
        int $client_id = 0,
        int $user_id = 0,
        string $company_hash_id = '',
        int $company_id = 0,
        int $lead_id = 0,
        int $type = 0,
        int $content_id = 0,
        int $subject_id = 0,
        array $default_receiver = [],
        array $user_receiver = []
    )
    {
        $diff_receiver = [];
        if (count($user_receiver)) {
            $diff_receiver = count($default_receiver) ? array_diff($user_receiver, $default_receiver) : $user_receiver;
        }

        $log = new \RecommendMailLog();
        $log->client_id = $client_id;
        $log->user_id = $user_id;
        $log->company_hash_id = $company_hash_id;
        $log->company_id = $company_id;
        $log->lead_id = $lead_id;
        $log->type = $type;
        $log->content_id = $content_id;
        $log->subject_id = $subject_id;
        $log->default_receiver = count($default_receiver) ? implode(',', $default_receiver) : '';
        $log->user_receiver = count($user_receiver) ? implode(',', $user_receiver) : '';
        $log->diff_receiver = count($diff_receiver) ? implode(',', $diff_receiver) : '';
        $log->create_time = date('Y-m-d H:i:s');
        $log->update_time = date('Y-m-d H:i:s');
        $log->save();
    }

    /*
     * 通过营销场景来得到EDM的类型:开发信、营销信
     */
    public static function getGroupType($refer_type, $scene_type)
    {
        if ($refer_type == self::REFER_TYPE_EDM) {   //EDM的时候才分开发信和营销信
            $sceneMk = [Constant::WILL_MOVE_TO_PUBLIC, Constant::CUSTOM_DATA_UPDATE];
            if (in_array($scene_type, $sceneMk)) {
                return Constant::GROUP_MK;
            } else {
                return Constant::GROUP_OPEN;
            }
        }
        return 0;
    }


}
