<?php
/**
 * Created by PhpStor<PERSON>.
 * User: troyli
 * Date: 2019-02-15
 * Time: 10:40
 */

namespace common\library\cache;


use common\library\account\UserInfoCacheableRepo;
use common\library\account\UserInviteCacheableRepo;
use common\library\account\UserRelativeCacheableRepo;
use common\library\alibaba\cache\AlibabaAccountCacheableRepo;
use common\library\alibaba\cache\AlibabaStoreAccountCacheableRepo;
use common\library\alibaba\cache\AlibabaStoreCacheableRepo;
use common\library\alibaba\cache\AlibabaStoreMemberCacheableRepo;
use common\library\alibaba\cache\AlibabaVisitorMarketingSettingCacheableRepo;
use common\library\customer\blacklist\CustomerBlacklistCacheableRepo;
use common\library\customer\CountryCacheableRepo;
use common\library\department\Department;
use common\library\department\DepartmentCacheableRepo;
use common\library\email\GsTicketCacheableRepo;
use common\library\email\UserMailAliasCacheableRepo;
use common\library\email\UserMailCacheableRepo;
use common\library\email\UserMailOverseasProxyCacheableRepo;
use common\library\exchange_rate\ExchangeRateCacheableRepo;
use common\library\google_ads\ga\GaSiteCacheableRepo;
use common\library\mail\MailSystemTemplateCacheableRepo;
use common\library\privilege_v3\PrivilegeCache;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\product_v2\category\CategoryCacheableRepo;
use common\library\risk\SecurityToken;
use common\library\risk\SecurityUserToken;
use common\library\waba\channel\WabaChannel;
use common\modules\internal\library\privilege\PrivilegeService;

class CacheConstant
{

    public static $cacheIsHash = [
        PrivilegeCache::CACHE_TYPE_OF_ROLE,
        PrivilegeCache::CACHE_TYPE_OF_ROLE_PRIVILEGE,
        PrivilegeCache::CACHE_TYPE_OF_PRIVILEGE_ROLE,
        PrivilegeCache::CACHE_TYPE_OF_ROLE_USERS,
        PrivilegeCache::CACHE_TYPE_OF_USER_ROLES,
        PrivilegeCache::CACHE_TYPE_OF_USER_ROLE_PRIVILEGE,
        SecurityToken::CACHE_TYPE_OF_TOKEN,
        PrivilegeCache::CACHE_TYPE_OF_REFER_FIELD_LIST,
        PrivilegeCache::CACHE_TYPE_OF_ROLE_FIELD,
        PrivilegeCache::CACHE_TYPE_OF_SYSTEM_USERS,
        PrivilegeCache::CACHE_TYPE_OF_SYSTEM_USERS_WITH_BUSINESS_ID,
    ];

    public static $cacheTypeConfig = [
        PrivilegeCache::PREFIX_OF_CLIENT      => [
            PrivilegeCache::CACHE_TYPE_OF_SYSTEM,
            PrivilegeCache::CACHE_TYPE_OF_FUNCTION,
        ],
        PrivilegeCache::PREFIX_OF_CLIENT_MODULE      => [
            PrivilegeCache::CACHE_TYPE_OF_MODULE,
        ],
        PrivilegeCache::PREFIX_OF_ROLE        => [
            PrivilegeCache::CACHE_TYPE_OF_ROLE,
            PrivilegeCache::CACHE_TYPE_OF_ROLE_PRIVILEGE,
            PrivilegeCache::CACHE_TYPE_OF_PRIVILEGE_ROLE,
        ],
        PrivilegeCache::PREFIX_OF_USER        => [
            PrivilegeCache::CACHE_TYPE_OF_ADMIN,
            PrivilegeCache::CACHE_TYPE_OF_ROLE_USERS,
            PrivilegeCache::CACHE_TYPE_OF_USER_ROLES,
            PrivilegeCache::CACHE_TYPE_OF_USER_ROLE_PRIVILEGE,
            PrivilegeCache::CACHE_TYPE_OF_SYSTEM_USERS,
            PrivilegeCache::CACHE_TYPE_OF_SYSTEM_USERS_WITH_BUSINESS_ID,
        ],
        PrivilegeCache::PREFIX_OF_USER_ACCESS => [
            PrivilegeCache::CACHE_TYPE_OF_USER_PRIVILEGE,
            PrivilegeConstants::ENABLE_FLAG_FALSE,                        // 无权
            PrivilegeConstants::ENABLE_FLAG_TRUE,                         // 有权
            PrivilegeConstants::ENABLE_FLAG_DISABLE,                      // 已禁用
            PrivilegeConstants::ENABLE_FLAG_NOT_PURCHASE,                 // 未购买产品
            PrivilegeConstants::ENABLE_FLAG_EXPIRE,                       // 账号过期

            PrivilegeCache::CACHE_TYPE_OF_USER_RELATION,                  //用户关系
        ],
        PrivilegeCache::PREFIX_OF_USER_ROLE        => [
            PrivilegeCache::CACHE_TYPE_OF_RECORD_ACCESS,            //用户角色操作权限
            PrivilegeCache::CACHE_TYPE_OF_RECORD_FIELD,             //用户角色字段权限
        ],
        SecurityToken::PREFIX_OF_TOKEN => [
            SecurityToken::CACHE_TYPE_OF_TOKEN,
            SecurityUserToken::CACHE_TYPE_OF_USER,
            SecurityUserToken::CACHE_TYPE_OF_PSKEY
        ],
        PrivilegeCache::PREFIX_OF_FIELD_SCOPE => [
            PrivilegeCache::CACHE_TYPE_OF_ROLE_FIELD,
            PrivilegeCache::CACHE_TYPE_OF_REFER_FIELD_LIST,
            PrivilegeCache::CACHE_TYPE_OF_CLIENT_ROLE_FIELD,
        ],
        WabaChannel::CACHE_KEY_PREFIX => [
            WabaChannel::CACHE_TYPE_CHANNEL_ID,
            WabaChannel::CACHE_TYPE_PHONE_NUMBER,
        ],
    ];

    const CACHEABLE_LIST_PREFIX_USER_INFO = 'crm:list:user_info:';
    const CACHEABLE_LIST_PREFIX_USER_INVITE = 'crm:list:user_invite:';
    const CACHEABLE_LIST_PREFIX_USER_RELATIVE = 'crm:list:user_relative:';
    const CACHEABLE_LIST_PREFIX_DEPARTMENT = 'crm:list:department:';
    const CACHEABLE_LIST_PREFIX_USER_MAIL = 'crm:list:user_mail:';
    const CACHEABLE_LIST_PREFIX_USER_MAIL_ALIAS = 'crm:list:user_mail_alias:';
    const CACHEABLE_LIST_PREFIX_USER_MAIL_PROXY = 'crm:list:user_mail_proxy:';
    const CACHEABLE_LIST_PREFIX_GS_TICKET = 'crm:list:gs_tickets:';
    const CACHEABLE_LIST_PREFIX_CATEGORY = 'crm:list:category:';
    const CACHEABLE_LIST_PREFIX_ALIBABA_STORE = 'crm:list:alibaba_store:';
    const CACHEABLE_LIST_PREFIX_ALIBABA_ACCOUNT = 'crm:list:alibaba_account:';
    const CACHEABLE_LIST_PREFIX_ALIBABA_STORE_ACCOUNT = 'crm:list:alibaba_store_account:';
    const CACHEABLE_LIST_PREFIX_ALIBABA_STORE_MEMBER = 'crm:list:alibaba_store_member:';
    const CACHEABLE_LIST_PREFIX_ALIBABA_VISITOR_MARKETING_SETTING = 'crm:list:alibaba_visitor_marketing_setting:';
    const CACHEABLE_LIST_PREFIX_GA_SITE = 'crm:list:ga_site:';
    const CACHEABLE_LIST_PREFIX_CUSTOMER_BLACKLIST = 'crm:list:customer_blacklist:';
    const CACHEABLE_LIST_PREFIX_COUNTRY = 'crm:list:country:';
//    const CACHEABLE_LIST_PREFIX_COUNTRY_REGION = 'crm:list:country_region:';
    const CACHEABLE_LIST_PREFIX_SYS_MAIL_TEMPLATE = 'crm:list:sys_mail_template:';
    const CACHEABLE_LIST_PREFIX_EXCHANGE_RATE = 'crm:list:exchange_rate:';

    const CACHEABLE_LIST_PREFIX_SPECIFY_SCENE = 'crm:list:specify_scene:';

    const CACHEABLE_REPO_MAP = [
        self::CACHEABLE_LIST_PREFIX_USER_INFO => UserInfoCacheableRepo::class,
        self::CACHEABLE_LIST_PREFIX_USER_INVITE => UserInviteCacheableRepo::class,
        self::CACHEABLE_LIST_PREFIX_USER_RELATIVE => UserRelativeCacheableRepo::class,
        self::CACHEABLE_LIST_PREFIX_DEPARTMENT => DepartmentCacheableRepo::class,
        self::CACHEABLE_LIST_PREFIX_USER_MAIL => UserMailCacheableRepo::class,
        self::CACHEABLE_LIST_PREFIX_USER_MAIL_ALIAS => UserMailAliasCacheableRepo::class,
        self::CACHEABLE_LIST_PREFIX_USER_MAIL_PROXY => UserMailOverseasProxyCacheableRepo::class,
        self::CACHEABLE_LIST_PREFIX_GS_TICKET => GsTicketCacheableRepo::class,
        self::CACHEABLE_LIST_PREFIX_ALIBABA_STORE => AlibabaStoreCacheableRepo::class,
        self::CACHEABLE_LIST_PREFIX_ALIBABA_ACCOUNT => AlibabaAccountCacheableRepo::class,
        self::CACHEABLE_LIST_PREFIX_ALIBABA_STORE_ACCOUNT => AlibabaStoreAccountCacheableRepo::class,
        self::CACHEABLE_LIST_PREFIX_ALIBABA_STORE_MEMBER => AlibabaStoreMemberCacheableRepo::class,
        self::CACHEABLE_LIST_PREFIX_ALIBABA_VISITOR_MARKETING_SETTING => AlibabaVisitorMarketingSettingCacheableRepo::class,
        self::CACHEABLE_LIST_PREFIX_GA_SITE => GaSiteCacheableRepo::class,
        self::CACHEABLE_LIST_PREFIX_CUSTOMER_BLACKLIST => CustomerBlacklistCacheableRepo::class,
        self::CACHEABLE_LIST_PREFIX_CATEGORY => CategoryCacheableRepo::class,
        self::CACHEABLE_LIST_PREFIX_COUNTRY => CountryCacheableRepo::class,
        self::CACHEABLE_LIST_PREFIX_SYS_MAIL_TEMPLATE => MailSystemTemplateCacheableRepo::class,
        self::CACHEABLE_LIST_PREFIX_EXCHANGE_RATE => ExchangeRateCacheableRepo::class,
    ];

    const CACHEABLE_REPO_LOAD_CHECK_LIST = [
        UserInfoCacheableRepo::class,
        UserMailCacheableRepo::class,
        DepartmentCacheableRepo::class,
    ];


}
