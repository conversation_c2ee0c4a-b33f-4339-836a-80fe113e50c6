<?php

namespace common\library\cache;

use common\library\report\error\ErrorReport;
use common\library\util\RedLock;
use common\library\util\SqlBuilder;
use common\library\workflow\WorkflowConstant;
use Predis\Command\ScriptCommand;

/**
 */
abstract class CacheableListRepo
{
    const BOOLEAN_OPERATOR = 'or';
    const BOOLEAN_AND = 'and';

    const ENABLE_SINGLE_DB_CACHE = true;

    const VALUE_TYPE_FIELD = 'field';

    protected $enableCache = true;
    protected $allowGetAll = true;

    protected $_attributes = [];

    public $clientId;
    protected $sql;
    protected $params = [];
    protected $filters = [];
    protected $ids = [];

    protected $allFlag = null; // false:直接返回空 true:直接返回全部

    public $recentSelectUserId;
    public $recentSelectType;
    public $sortByRecentSelect = false;

    /**
     * @var \Predis\Client
     */
    protected static $connection;

    public function __construct($clientId = null)
    {
        $this->clientId = $clientId;
    }

    public static function instance($clientId)
    {

        return new static($clientId);
    }

    public function __set($name, $value)
    {
        if ($name == 'clientId') {
            $this->clientId = $value;
        }
        $this->_attributes[$name] = $value;
    }

    public function __get($name)
    {
        return $this->_attributes[$name] ?? null;
    }

    public function __isset($name)
    {
        return isset($this->_attributes[$name]);
    }

    public function __unset($name)
    {
        if (isset($this->_attributes[$name]))
            unset($this->_attributes[$name]);
    }

    public function setAllFlag($allFlag)
    {
        $this->allFlag = $allFlag;
    }

    /**
     * 根据id删除缓存，id=null则删除全部
     *
     * @param $id
     * @return void
     */
    public function deleteCache($id)
    {
        // 删除指定缓存
        if ($ids = array_filter((array)$id)) {
            // delete specify
            $this->getRedis()->hdel($this->getCachekey(), $ids);
        }
    }

    protected function cleanupTemporary()
    {
        $this->getRedis()->del([$this->getSpecifySceneKey()]);
        ClassCacheRepository::resetInstance($this->clientId, static::class);
    }

    /**
     * 根据id刷新缓存，id=null则刷新全部
     *
     * @param $id
     * @return bool
     */
    public function refreshCache($id = null, $delete = false, $updateIndex = false)
    {
        if (!$this->enableCache) {
            return false;
        }
        if (!$this->ignoreClient() && !$this->clientId) {
            $e = new \RuntimeException("not client for cache repo");
            \LogUtil::exception($e);
            ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());
            return false;
        }
        try {
            $this->cleanupTemporary();
            $needCleanup = false;
            $existKeys = [];
            if (empty($id)) {
                $existKeys = $this->getRedis()->hkeys($this->getCachekey()) ?: [];
                $needCleanup = true;
            } else {
                if ($delete) {
                    $this->deleteCache($id);
                }
            }
            $needIndex = ($id === null || $updateIndex) && $this->indexColumns();
            if ($needIndex) {
                $id = null;
            }

            $list = $this->listByDb($id, !$needIndex);
            // hash set
            if ($list) {
                // 更新索引
                if ($needIndex) {
                    $this->updateIndex($list, $needCleanup);
                    $list = $this->listToDictionary($list);
                }
                if ($needCleanup) {
                    $cleanupKeys = array_unique(array_filter(array_diff($existKeys, array_keys($list))));
                    if ($cleanupKeys) {
                        $this->deleteCache($cleanupKeys);
                    }
                }
                // 更新数据
                $this->getRedis()->hmset($this->getCachekey(), $list);
                if ($expiredSeconds = $this->getExpiredTime()) {
                    $this->getRedis()->expire($this->getCachekey(), $expiredSeconds);
                }
                \LogUtil::info("list cache refresh " . static::class, ['clientId' => $this->clientId, 'id' => $id]);
            } else {
                if (!$id) {
                    if ($existKeys) {
                        $this->deleteCache($existKeys);
                    }
                    $this->getRedis()->hmset($this->getCachekey(), ['0' => '{}']);
                    \LogUtil::info("list cache refresh but empty " . static::class, ['clientId' => $this->clientId, 'id' => $id]);
                }
            }
            return true;
        } catch (\Throwable $exception) {
            \LogUtil::exception($exception, ['cacheList' => static::class]);
            ErrorReport::phpError(new \CExceptionEvent(null, $exception), $exception->getTrace(), null);

            return false;
        }
    }

    protected function updateIndex($list, $needClean = false)
    {
        $indexColumns = $this->indexColumns() ?? [];
        if (!$indexColumns || !$this->getIdName()) {
            return false;
        }
        $idName = $this->getIdName();
        foreach ($indexColumns as $column) {
            $indexMap = [];
            foreach ($list as $datum) {
                $indexMap[$datum[$column]][] = $datum[$idName];
            }
            foreach ($indexMap as &$idList) {
                $idList = '{' . implode(',', $idList) . '}';
            }
            if ($needClean) {
                $existKeys = $this->getRedis()->hkeys($this->getCachekey($column));
                $needCleanKeys = array_values(array_unique(array_diff($existKeys, array_keys($indexMap))));
                if ($needCleanKeys) {
                    $this->getRedis()->hdel($this->getCachekey($column), $needCleanKeys);
                }
            }
            $this->getRedis()->hmset($this->getCachekey($column), $indexMap);
        }
        \LogUtil::info("list cache refresh index " . static::class, ['clientId' => $this->clientId]);

        return true;
    }

    /**
     * 根据id从db获取数据
     *
     * @param $id
     * @return mixed
     */
    protected function listByDb($id = null, $asDictionary = true, $fields = '*')
    {
        [$sql, $params] = $this->buildListByDb($id, $fields);
        $listData = $this->getDbConnection()->createCommand($sql)->queryAll(true, $params);

        if ($asDictionary) {
            $result = $this->listToDictionary($listData);
        } else {
            $result = $listData;
        }

        return $result;
    }

    protected function listToDictionary($listData)
    {
        $result = [];
        if ($this->getIdName()) {
            foreach ($listData as $datum) {
                $result[$datum[$this->getIdName()]] = json_encode($datum, JSON_UNESCAPED_UNICODE);
            }
        } else {
            $result[$this->clientId] = json_encode($listData, JSON_UNESCAPED_UNICODE);
        }

        return $result;
    }

    protected function buildListByDb($id = null, $fields = '*')
    {
        $where = "client_id = :client_id";
        $params = [
            ':client_id' => $this->clientId,
        ];
        if ($id) {
            SqlBuilder::buildIntWhere('', $this->getIdName(), $id, $where, $params);
        }

        $sql = "select * from {$this->getTableName()} where {$where}";

        return [$sql, $params];
    }

    abstract protected function getTableName();

    protected function getFullSql($fields = '*')
    {
        [$where, $params] = $this->getSql();

        $sql = "select {$fields} from {$this->getTableName()} " . (trim($where) ? "where {$where}" : '');

        return [$sql, $params];
    }

    public function setEnableCache($flag)
    {
        $this->enableCache = $flag;

        return $this;
    }

    public function isEnableCache()
    {
        $runtimeDisable = getenv('DISABLE_SINGLE_DB_CACHE') === false ? true : getenv('DISABLE_SINGLE_DB_CACHE'); // 默认关闭
        return self::ENABLE_SINGLE_DB_CACHE && !$runtimeDisable && (bool)$this->enableCache && ($this->ignoreClient() || $this->clientId);
    }

    public function ignoreClient()
    {
        return false;
    }

    abstract public function build();

    public function getSql()
    {
        if ($this->sql === null) {
            $this->build();
        }

        $this->sql = ltrim(trim($this->sql), 'and');

        return [$this->sql, $this->params];
    }

    abstract protected function getDbConnection();

    public function findByIds($ids, $fields = '*')
    {
        $result = null;
        if ($this->isEnableCache()) {
            try {
                $result = $this->findByFilters([], $ids, $fields);
            } catch (\Throwable $e) {
                \LogUtil::exception($e);
                ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());
                $result = null;
            }
        }

        if ($result === null) {
            $result = $this->listByDb($ids, false, $fields);
        }

        return $result;
    }

    public function findById($id)
    {
        $list = $this->findByIds($id);
        return $list ? (current($list) ?: []) : [];
    }

    public function findAllByConditions($conditions, $fields = '*', $orderBy = '')
    {
//        $defaultConditions = [];
//        if (!$this->ignoreClient()) {
//            $defaultConditions = array_diff_key(['client_id' => $this->clientId], $conditions);
//        }
//        $conditions = array_merge($conditions, $defaultConditions);

        foreach ($conditions as $k => $v) {
            if (is_numeric($k)) {
                if (isset($v['or']) || isset($v['and'])) {
                    $this->buildBool($v);
                } else {
                    $this->buildParam(...$v);
                }
            } else {
                $this->buildParam($k, $v);
            }
        }

        [$sql, $params] = $this->getFullSql($fields);

        if ($orderBy) {
            $sql .= " order by $orderBy";
        }

        return $this->find($sql, $params, $orderBy, $fields);
    }

    public function findOneByConditions($conditions, $fields = '*')
    {
        $list = $this->findAllByConditions($conditions, $fields);

        return $list ? current($list) : false;
    }

    public function find($sql, $params = [], $orderBy = '', $fields = '*', $limit = 0, $offset = 0)
    {
        if ($this->sql === null) {
            $this->build();
        }

        $listData = null;
        if ($this->isEnableCache()) {
            try {
                $listData = $this->findByFilters($this->filters, $this->ids, $fields, $orderBy);
                $listData = $this->sort($listData, $orderBy);
                $listData = $this->limit($listData, $limit, $offset);
            } catch (\Throwable $e) {
                \LogUtil::exception($e);
                ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());
                $listData = null;
            } finally {
                $this->resetParams();
            }
        }

        if ($listData === null) {
            $listData = $this->getDbConnection()->createCommand($sql)->queryAll(true, $params);
        }

        return $listData;
    }

    protected function resetParams()
    {
        $this->ids = [];
        $this->sql = null;
        $this->params = [];
        $this->filters = [];
    }

    protected function sort($listData, $orderBy)
    {
        if (empty($listData)) {
            return $listData;
        }
        if ($orderMap = $this->buildOrderBy($orderBy)) {
            $listData = $this->arrayOrderBy($listData, ...$orderMap);
            if ($this->sortByRecentSelect) {
                $listData = $this->sortByRecentSelect($listData);
            }

            return $listData;
        } else {
            return $listData;
        }
    }

    protected function limit($listData, int $limit, int $offset)
    {
        if (empty($listData) || empty($limit)) {
            return $listData;
        }
        $listData = array_slice($listData, $offset, $limit);

        return $listData;
    }

    protected function sortByRecentSelect($listData)
    {
        $result = [];
        if ($this->sortByRecentSelect && $this->recentSelectUserId && $this->recentSelectType) {
            $db = \ProjectActiveRecord::getDbByUserId($this->recentSelectUserId);
            $sql = "SELECT refer_id FROM tbl_recent_select WHERE user_id={$this->recentSelectUserId} AND type={$this->recentSelectType} ORDER BY create_time DESC";
            $referIds = $db->createCommand($sql)->queryColumn();
            if (empty($referIds)) {
                return $listData;
            }

            $listData = array_column($listData, null, $this->getIdName());
            foreach ($referIds as $referId) {
                if (isset($listData[$referId])) {
                    $result[] = $listData[$referId];
                    unset($listData[$referId]);
                }
            }
            $result = array_merge($result, $listData);
        }

        return $result;
    }

    protected function buildOrderBy($orderBy, $returnFields = false)
    {
        $orderMap = [];
        $fields = [];

        if (!$this->getIdName()) {
            return $orderMap;
        }

        if ($orderBy = trim(str_replace('order by', '', strtolower($orderBy)), ' ')) {
            foreach (explode(',', $orderBy) as $orderString) {
                $orderArr = explode(' ', $orderString);
                $orderMap[] = $fields[] = trim($orderArr[0], '`\ ');
                $orderMap[] = strtolower(trim($orderArr[1] ?? 'asc')) == 'asc' ? SORT_ASC : SORT_DESC;
            }
        } else {
            $orderMap = [$this->getIdName(), SORT_ASC];
            $fields[] = $this->getIdName();
        }

        if ($returnFields) {
            return $fields;
        }

        return $orderMap;
    }

    public function count($sql, $params)
    {
        $count = null;
        if ($this->isEnableCache()) {
            try {
                $count = $this->countByFilters($this->filters, $this->ids);
            } catch (\Throwable $e) {
                \LogUtil::exception($e);
                ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());
                $count = null;
            }
        }

        if ($count === null) {
            $count = $this->getDbConnection()->createCommand($sql)->queryScalar($params);
        }

        return $count;
    }

    private function getCachePrefix()
    {
        if (empty(array_flip(CacheConstant::CACHEABLE_REPO_MAP)[static::class])) {
            $exception = new \RuntimeException(static::class . "未配置缓存");
            \LogUtil::exception($exception);
            throw $exception;
        }

        return array_flip(CacheConstant::CACHEABLE_REPO_MAP)[static::class];
    }

    protected function getCachekey($field = '')
    {
        if ($this->ignoreClient()) {
            $mainKey = $this->getCachePrefix() . "{0}";
        } else {
            $mainKey = $this->getCachePrefix() . "{{$this->clientId}}";
        }

        if ($field) {
            return $mainKey . ":{$field}";
        }

        return $mainKey;
    }

    protected function getSpecifySceneKey()
    {
        if ($this->ignoreClient()) {
            $mainKey = CacheConstant::CACHEABLE_LIST_PREFIX_SPECIFY_SCENE . "{0}";
        } else {
            $mainKey = CacheConstant::CACHEABLE_LIST_PREFIX_SPECIFY_SCENE . "{{$this->clientId}}";
        }

        return $mainKey;
    }

    abstract protected function getIdName();

    protected function buildBool($filtersMap)
    {
//        $this->disableLua('boolean filters');

        foreach ($filtersMap as $boolOperator => $filterList) {
            $sql = '';
            $filter = [];
            $filter['bool'] = $boolOperator;
            foreach ($filterList as $filterInfo) {
                if ($subFilter = $this->buildParam($filterInfo[0], $filterInfo[1], $filterInfo[2] ?? WorkflowConstant::FILTER_OPERATOR_EQUAL, $filterInfo[3] ?? null, $sql, $sql ? $boolOperator : '')) {
                    $filter['filters'][] = $subFilter;
                }
            }
            if ($sql) {
                $this->sql .= " and ($sql)";
                $this->filters[] = $filter;
            }
        }
    }

    protected function buildParam($field, $value, $operator = WorkflowConstant::FILTER_OPERATOR_EQUAL, $valueType = null, &$sql = null, $sqlOperator = self::BOOLEAN_AND)
    {
        if ($value === null && !in_array($operator, [WorkflowConstant::FILTER_OPERATOR_IS_NULL, WorkflowConstant::FILTER_OPERATOR_NOT_NULL])) {
            return null;
        }

        $useMainSql = $sql === null;
        $sql = $useMainSql ? $this->sql : $sql;

        if ($valueType == self::VALUE_TYPE_FIELD) {
            SqlBuilder::buildFieldCompareWhere('', $field, $value, $operator, $sql, $sqlOperator);
        } else {
            switch ($operator) {
                case WorkflowConstant::FILTER_OPERATOR_NOT_IN:
                case WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL:
                    SqlBuilder::buildNotIntWhere('', $field, $value, $sql, $this->params, $sqlOperator);
                    break;
                case WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL:
                case WorkflowConstant::FILTER_OPERATOR_GREATER:
                case WorkflowConstant::FILTER_OPERATOR_LESS_OR_EQUAL:
                case WorkflowConstant::FILTER_OPERATOR_LESS:
                    // Lua比对当前只支持数字
//                    if (!is_numeric($value)) {
//                        $this->disableLua('compare non-numeric');
//                    }
                    SqlBuilder::buildCompareWhere('', $field, $value, $operator, $sql, $this->params, $sqlOperator);
                    break;
                case WorkflowConstant::FILTER_OPERATOR_CONTAINS:
                    SqlBuilder::buildLikeWhere('', $field, $value, $sql, $this->params, $sqlOperator);
                    break;
                case WorkflowConstant::FILTER_OPERATOR_IS_NULL:
                    SqlBuilder::buildIsNull('', $field, $sql, $this->params, $sqlOperator);
                    break;
                default:
                    SqlBuilder::buildIntWhere('', $field, $value, $sql, $this->params, $sqlOperator);
                    break;
            }
        }

        if ($useMainSql) {
            $this->sql = $sql;
        }

        if ($useMainSql && $field == $this->getIdName() && in_array($operator, [WorkflowConstant::FILTER_OPERATOR_IN, WorkflowConstant::FILTER_OPERATOR_EQUAL])) {
            $this->ids = array_unique(array_merge($this->ids, (array)$value));
        } else {
            if ($operator == WorkflowConstant::FILTER_OPERATOR_CONTAINS) {
                $left = $value[-1] == '%';
                $right = $value[0] == '%';
                $value = trim($value, '%');
                if (($left && $right) || (!$left && !$right)) {
                    $valueType = 'all';
                } elseif ($left) {
                    $valueType = 'left';
                } else {
                    $valueType = 'right';
                }
            }
            $filter = [
                'field' => $field,
                'value' => $value,
                'operator' => $operator,
                'value_type' => $valueType,
            ];
            if ($useMainSql) {
                $this->filters[] = $filter;
            } else {
                return $filter;
            }
        }
    }

    public function countByFilters($filters, $ids = [])
    {
        if ($ids) {
            $ids = array_values(array_filter((array)$ids));
            if (!$ids) {
                return 0;
            }
        }

//        if ($this->allFlag === null) {
//            if ($this->isUsingLua($filters)) {
//                return $this->findByLua($filters, $ids, true);
//            }
//        }

        $list = null;
        if ($this->isEnableCache()) {
            try {
                $list = $this->findByFilters($filters, $ids);
            } catch (\Throwable $e) {
                \LogUtil::exception($e);
                ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());
                $list = null;
            }
        }

        return $list === null ? $list : count($list ?: []);
    }

    protected function findByFilters($filters, $ids = [], $fields = '*', $orderBy = '')
    {
        if ($ids) {
            $ids = array_values(array_unique(array_filter((array)$ids)));
            if (!$ids) {
                return [];
            }
        }
        $validFieldFlag = true;
        $orderFields = $this->buildOrderBy($orderBy, true);
        $mappingFields = [];
        if ($fields != '*' and is_string($fields)) {
            $fields = explode(',', $fields);
            foreach ($fields as &$field) {
                $field = trim($field);
                if (str_contains($field, '(')) {
                    $validFieldFlag = false;
                } elseif (str_contains($field, ' as ')) {
                    [$fromField, $toField] = explode(' as ', $field);
                    $mappingFields[trim($fromField)] = trim($toField);
                    $field = trim($fromField);
                } elseif (str_contains($field, ' ')) {
                    $validFieldFlag = false;
                }
            }
            $fields = array_unique(array_merge($fields, $orderFields));
        } else {
            $validFieldFlag = false;
        }

        if ($this->allFlag === false) {
            return [];
        }
//        elseif ($this->allFlag === null) {
//            if ($this->isUsingLua($filters)) {
//                $listData = $this->findByLua($filters, $ids, false, $validFieldFlag ? $fields : []);
//                if ($mappingFields && $listData) {
//                    foreach ($mappingFields as $fromField => $toField)
//                        foreach ($listData as &$datum) {
//                            $datum[$toField] = $datum[$fromField];
//                        }
//                }
//                return $listData;
//            }
//        }
        if ($ids) {
            // 静态缓存
            $classCacheRepository = ClassCacheRepository::instance($this->clientId, static::class);
//            $cachedList = $classCacheRepository->hmget($this->getCachekey() . ':ids', $ids);
            $allList = [];
            $queryIds = [];
            foreach ($ids as $id) {
                if (!empty($cachedList[$id])) {
                    $allList[$id] = $cachedList[$id];
                } else {
                    $queryIds[] = $id;
                }
            }
            if ($queryIds) {
                $allList = array_replace($allList, array_combine($queryIds, $this->getRedis()->hmget($this->getCachekey(), $queryIds)));
                $allList = array_filter($allList);
                $classCacheRepository->hmset($this->getCachekey() . ':ids', $allList);
            }
        } else {
            if ($indexFilter = $this->findIndexInfo($filters)) {
                // get by lua
                if ($indexValueIsArray = is_array($indexValue = $indexFilter['value'])) {
                    $indexValue = array_unique($indexValue);
                    if (count($indexValue) == 1) {
                        $indexValue = current($indexValue);
                        if (is_string($indexValue)) {
                            $indexValue = str_replace("'", "\'", $indexValue);
                        }
                        $indexValue = str_replace("'", "\'", $indexValue);
                        $indexValueIsArray = false;
                    } else {
                        $isString = is_string(current($indexValue));
                        if ($isString) {
                            $indexValue = str_replace("'", "\'", $indexValue);
                            $indexValue = "{'" . implode("','", $indexValue) . "'}";
                        } else {
                            $indexValue = '{' . implode(',', $indexValue) . '}';
                        }
                    }
                }
                $luaParams = [
                    $this->getCachekey(),
                    $this->getCachekey($indexFilter['field']),
                    $indexValueIsArray ? 'true' : 'false',
                    $indexValue
                ];
                $luaCacheKey = md5(serialize($luaParams));
                $allList = ClassCacheRepository::instance($this->clientId, static::class)->remember($this->getCachekey() . $luaCacheKey, function () use ($luaParams) {
                    return $this->getPreparedRedis()->getCacheableRepoByColumnIndex(...$luaParams);
                });
//                $allList = $this->getPreparedRedis()->getCacheableRepoByColumnIndex(
//                    $this->getCachekey(),
//                    $this->getCachekey($indexFilter['field']),
//                    $indexValueIsArray ? 'true' : 'false',
//                    $indexValue
//                );
            } else {
                if (!$this->allowGetAll) {
                    // 退化为sql
                    return null;
                }
                $allList = ClassCacheRepository::instance($this->clientId, static::class)->remember($this->getCachekey(), function () {
                    return $this->getRedis()->hgetall($this->getCachekey());
                });
            }
        }

        if (empty($allList)) {
            if ($this->needRefresh()) {
                $lockKey = static::class . '_refresh_lock';
                $locker = new RedLock(\RedisService::cache(), $lockKey);
                if ($locker->lock($lockTime = 5)) {
                    try {
                        $this->refreshCache();
                        $allList = ClassCacheRepository::instance($this->clientId, static::class)->remember($this->getCachekey(), function () {
                            return $this->getRedis()->hgetall($this->getCachekey());
                        });
                    } finally {
                        $locker->unlock();
                    }
                } else {
                    // 未预热但又没拿到锁的退化为sql查询
                    return null;
                }
            }
        }

        if (!$this->getIdName()) {
            $allList = json_decode(current($allList), true);
            if (!$allList || !is_array($allList)) {
                return null;
            }
        }

        if ($this->allFlag) {
            return $allList;
        }

        $result = [];
        if (!$allList) {
            return $result;
        }

        $filterFields = $validFieldFlag && $fields ? array_fill_keys($fields, '') : [];
        foreach ($allList as $item) {
            if (!is_array($item)) {
                $item = json_decode($item, true);
            }

            if (!is_array($item) || empty($item)) {
                continue;
            }

            if ($ids) {
                $matched = in_array($item[$this->getIdName()] ?? 0, $ids);
            } else {
                $matched = $item;
            }

            foreach ($filters as $filter) {
                if (empty($filter['bool']) && !array_key_exists($filter['field'], $item)) {
                    \LogUtil::error("cache list field not exist", ['filter' => $filter]);
                    return null;
                }
                if (!($matched = $this->compareByFilter($filter, $item))) {
                    break;
                }
            }
            if ($matched) {
                $resultDatum = $filterFields ? array_intersect_key($item, $filterFields) : $item;
                if ($mappingFields) {
                    foreach ($mappingFields as $fromField => $toField) {
                        $resultDatum[$toField] = $resultDatum[$fromField];
                    }
                }
                $result[] = $resultDatum;
            }
        }

        return $result;
    }

    protected function compareByFilter($filter, $item)
    {
        if (isset($filter['bool']) && !empty($filter['filters'])) {
            $isOr = $filter['bool'] == self::BOOLEAN_OPERATOR;
            $boolResult = !$isOr;
            foreach ($filter['filters'] as $boolFilter) {
                $boolResult = $isOr ? ($boolResult || $this->compareByFilter($boolFilter, $item)) : ($boolResult && $this->compareByFilter($boolFilter, $item));
            }

            return $boolResult;
        }

        $columnValue = $item[$filter['field']];
        $filterValue = $filter['value'];
        switch ($filter['operator']) {
            case WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL:
                return $columnValue != $filterValue;
            case WorkflowConstant::FILTER_OPERATOR_EQUAL:
            case WorkflowConstant::FILTER_OPERATOR_IN:
            return $filterValue === [] || in_array(strtolower($columnValue), array_map('strtolower', (array)$filterValue));
            case WorkflowConstant::FILTER_OPERATOR_NOT_IN:
                return $filterValue === [] || !in_array(strtolower($columnValue), array_map('strtolower', (array)$filterValue));
            case WorkflowConstant::FILTER_OPERATOR_LESS:
                return $columnValue < $filterValue;
            case WorkflowConstant::FILTER_OPERATOR_LESS_OR_EQUAL:
                return $columnValue <= $filterValue;
            case WorkflowConstant::FILTER_OPERATOR_GREATER:
                return $columnValue > $filterValue;
            case WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL:
                return $columnValue >= $filterValue;
            case WorkflowConstant::FILTER_OPERATOR_IS_NULL:
                return $columnValue === null;
            case WorkflowConstant::FILTER_OPERATOR_CONTAINS:
                $columnValue = strtolower($columnValue);
                $filterValue = strtolower($filterValue);
                switch ($filter['value_type']) {
                    case 'left':
                        return str_starts_with($columnValue, $filterValue);
                    case 'right':
                        return str_ends_with($columnValue, $filterValue);
                    default:
                        return str_contains($columnValue, $filterValue);
                }
            default:
                return false;
        }
    }

//    protected function findByLua($filters, $ids = [], $countOnly = false, $fields = '*')
//    {
//        $cacheKey = sha1(serialize($filters) . serialize($ids) . serialize($fields));
//        $allList = ClassCacheRepository::instance($this->clientId, static::class)->get($cacheKey);
//        if ($allList !== null) {
//            if ($countOnly) {
//                return count($allList);
//            }
//            return $allList;
//        }
//
//        $redis = $this->getPreparedRedis();
//        $fields = json_encode($fields);
//        $allList = $redis->getCacheableRepoFilterList($this->getCachekey(), json_encode((array)$ids), json_encode($filters), $countOnly ? 'true' : 'false', $this->getIdName(), $fields);
//
//        if ($allList === null) {
//            $this->refreshCache();
//            $allList = $redis->getCacheableRepoFilterList($this->getCachekey(), json_encode((array)$ids), json_encode($filters), $countOnly ? 'true' : 'false', $this->getIdName(), $fields);
//        }
//
//        if (!$countOnly) {
//            if ($allList === null) {
//                return null;
//            }
//
//            foreach ($allList as &$item) {
//                if (!is_array($item)) {
//                    $item = json_decode($item, true);
//                    if (is_array($item)) {
//                        foreach ($item as &$itemValue) {
//                            if ($itemValue == 'null') {
//                                $itemValue = null;
//                            }
//                        }
//                    }
//                }
//            }
//        }
//
//        if (!$countOnly) {
//            ClassCacheRepository::instance($this->clientId, static::class)->set($cacheKey, $allList);
//        }
//
//        return $allList;
//    }

    protected function getPreparedRedis()
    {
        if (!self::$connection) {
            $redis = $this->getRedis();
            $redis->getProfile()->defineCommand('getCacheableRepoByColumnIndex', new class extends ScriptCommand {

                public function getKeysCount()
                {
                    return 2;
                }

                public function getScript()
                {
                    return <<<LUA
local tableKey = KEYS[1]
local indexKey = KEYS[2]
local isMultiIndexKeys = ARGV[1] == "true"

local indexIds = {}
local allIdList = {}
if isMultiIndexKeys then
    local ids = loadstring("return "..ARGV[2])()
    indexIds = redis.call("hmget",KEYS[2], unpack(ids));
    for i,idString in pairs(indexIds) do
        if idString then
            local idValues = loadstring("return "..idString)()
            for j,id in pairs(idValues) do
                table.insert(allIdList, id)
            end
        end
    end
else
    local idString = redis.call("hget",KEYS[2], ARGV[2]);
    if idString then
        allIdList = loadstring("return "..idString)()
    end
end

local hashList = {}
if next(allIdList) ~= nil then
    hashList = redis.call("hmget",KEYS[1], unpack(allIdList));
end

return hashList
LUA;
                }
            });


            self::$connection = $redis;
        }

        return self::$connection;
    }

    protected function getRedis()
    {
        return \RedisService::sf();
    }

    protected function arrayOrderBy()
    {
        $args = func_get_args();
        $data = array_shift($args);
        foreach ($args as $n => $field) {
            if (is_string($field)) {
                $tmp = array();
                foreach ($data as $key => $row)
                    $tmp[$key] = $row[$field] ?? '';
                $args[$n] = $tmp;
            }
        }
        $args[] = &$data;
        call_user_func_array('array_multisort', $args);
        return array_pop($args);
    }

    protected function getExpiredTime()
    {
        return 0;
    }

    /**
     * @return CacheRepository
     */
    protected function getClassStaticCache(): CacheRepository
    {
        return ClassCacheRepository::instance($this->clientId, static::class);
    }

    public static function refreshAll($clientId)
    {
        $result = [];
        foreach (CacheConstant::CACHEABLE_REPO_MAP as $className) {
            if (!is_subclass_of($className, self::class)) {
                continue;
            }
            $obj = new $className($clientId);

            if (($clientId && !$obj->ignoreClient()) || (!$clientId && $obj->ignoreClient())) {
                $obj->refreshCache(null, false, true);
                $result[] = $className;
            }
        }

        return $result;
    }

    public function indexColumns()
    {
        return [];
    }

    protected function findIndexInfo($filters)
    {
        foreach ($filters as $filter) {
            if (isset($filter['field']) && in_array($filter['field'], $this->indexColumns() ?? []) && in_array($filter['operator'], [WorkflowConstant::FILTER_OPERATOR_EQUAL, WorkflowConstant::FILTER_OPERATOR_IN])) {
                return $filter;
            }
        }

        return false;
    }

    protected function needRefresh()
    {
        // todo 预热完开放判断
//        if (in_array(static::class, CacheConstant::CACHEABLE_REPO_LOAD_CHECK_LIST)) {
//            return false;
//        }

        return !$this->getRedis()->exists($this->getCachekey());
    }

    public function getBySpecifyScene($prefix, $params, $buildFunction, $lockTime = 5, $waitLockLoopTimeMs = 10)
    {
        if (!$this->isEnableCache()) {
            return call_user_func($buildFunction);
        }
        $buildSceneKey = function ($params) use ($prefix) {
            $keys = [$prefix];
            foreach ($params as $k => $v) {
                $key = $k;
                if ($v === null) {
                    $key .= ':null';
                } elseif ($v === true) {
                    $key .= ':true';
                } elseif ($v === false) {
                    $key .= ':false';
                } elseif (is_numeric($v) || is_string($v)) {
                    $key .= ":$v";
                } else {
                    $key .= json_encode($v);
                }
                $keys[] = $key;
            }
            return implode(':', $keys);
        };
        $sceneKey = $buildSceneKey($params);
        if (!$sceneKey) {
            return [];
        }
        $lockKey = $sceneKey . '_lock';
        $redis = $this->getRedis();

        while (($result = $redis->hget($this->getSpecifySceneKey(), $sceneKey)) === null) {
            $locker = new RedLock(\RedisService::cache(), $lockKey);
            if ($locker->lock($lockTime)) {
                try {
                    $buildResult = call_user_func($buildFunction);

                    $redis->hset($this->getSpecifySceneKey(), $sceneKey, json_encode($buildResult));
                } catch (\Throwable $e) {
                    throw $e;
                } finally {
                    $locker->unlock();
                }
            } else {
                while ($locker->checkLock())
                    usleep($waitLockLoopTimeMs);
            }
        }

        return json_decode($result, true);
    }

    public function getValidateData($conditionList, $greySql, $limit = 1000)
    {
        if ($greySql && !$this->ignoreClient()) {
            $conditionList[] = $greySql;
        }

        $where = implode(' and ', $conditionList);
        $params = [];

        $sql = "select * from {$this->getTableName()} where {$where} limit {$limit}";

        $dataList =  $this->getDbConnection()->createCommand($sql)->queryAll(true, $params);

//        \LogUtil::debug('query validate data', [
//            'sql' => $sql,
//            'count' => count($dataList),
//        ]);

        if ($this->getIdName()) {
            return array_column($dataList, null, $this->getIdName());
        }

        return $dataList;
    }

    public function validateByData($clientId, $dataList, $enableConfig)
    {
        $result = [];
        if ($this->ignoreClient()) {
            return [];
        }
        if (!$clientId) {
            return [];
        } else {
            $this->clientId = $clientId;
        }

        if (!$dataList) {
            return $result;
        }

        $ids = array_keys($dataList);
        \LogUtil::info(static::class . " updated info", ['ids' => $ids]);

        $pkMap = [
            CacheConstant::CACHEABLE_LIST_PREFIX_ALIBABA_STORE_ACCOUNT => [ 'client_id', 'seller_account_id', 'store_id'],
        ];
        $skipCheckFields = [
            CacheConstant::CACHEABLE_LIST_PREFIX_USER_INFO => ["login_time","login_ip","update_time","used_count_company","current_count_company",],
            CacheConstant::CACHEABLE_LIST_PREFIX_USER_MAIL => ["update_time", "shard_key"],
            CacheConstant::CACHEABLE_LIST_PREFIX_USER_INVITE => ["send_status","send_time","send_count"],
        ];
        if (!empty($pkMap[$this->getCachePrefix()])) {
            $cacheData = $this->getRedis()->hgetall($this->getCachekey());
            $cacheDataList = [];
            $cacheData = current($cacheData);
            $cacheData = json_decode($cacheData ?: '{}', true);
            foreach ($cacheData ?? [] as $dbData) {
                $keys = array_map(function($k) use ($dbData) {
                    return $dbData[$k];
                }, $pkMap[$this->getCachePrefix()]);
                $cacheDataList[implode('-', $keys)] = $dbData;
            }
        } else {
            $cacheData = $this->getRedis()->hmget($this->getCachekey(), $ids);
            $cacheDataList = [];
            foreach ($cacheData ?? [] as $cacheDataItem) {
                $cacheDataList[] = json_decode($cacheDataItem ?: '{}', true);
            }
            $cacheDataList = array_column($cacheDataList, null, $this->getIdName());
        }

        // 索引
        $indexes = [];
        if ($this->indexColumns()) {
            foreach ($this->indexColumns() as $indexColumn) {
                $indexes[$indexColumn] = $this->getRedis()->hgetall($this->getCachekey($indexColumn));
                if (empty($indexes[$indexColumn])) {
                    $result['missing_index'][] = $indexColumn;
                }
            }
        }

        $skipFields = $skipCheckFields[$this->getCachePrefix()] ?? [];
        if ($this->getIdName() || in_array($this->getCachePrefix(), $pkMap)) {
            foreach ($dataList as $dataId => $dbData) {
                if ($enableConfig && $dbData[$enableConfig[0]] != $enableConfig[1]) {
                    // db已删除
                    if (isset($cacheDataList[$dataId])) {
                        $result['not_deleted'][] = $dataId;
                    }
                    continue;
                }
                if (empty($cacheDataList[$dataId])) {
                    $result['missing'][] = $dataId;
                } else {
                    foreach ($cacheDataList[$dataId] as $field => $value) {
                        if (in_array($field, $skipFields)) {
                            continue;
                        }
                        if ($value != $dbData[$field]) {
                            $result['error_ids'][] = $dataId;
                            $result['error_fields'][] = $field;
                            $result['error_detail'][] = [$dataId => ['field' => $field, 'cache' => $value, 'db' => $dbData[$field]]];
                        }
                        if ($this->indexColumns() && in_array($field, $this->indexColumns())) {
                            if (!str_contains($indexes[$field][$dbData[$field]] ?? '{}', $dataId)) {
                                $result['error_index'][] = $dataId;
                            }
                        }
                    }
                    if (!empty($result['error_fields'])) {
                        $result['error_fields'] = array_values(array_unique($result['error_fields']));
                    }
                    if (!empty($result['error_ids'])) {
                        $result['error_ids'] = array_values(array_unique($result['error_ids']));
                    }
                }
            }
        }
        return $result;
    }

    public static function validate($from, $refresh = true, $greyNum = [])
    {
        $result = [];
        $skipCheck = [
            CacheConstant::CACHEABLE_LIST_PREFIX_CATEGORY,
            CacheConstant::CACHEABLE_LIST_PREFIX_COUNTRY,
        ];
        $updateTimeMap = [
            CacheConstant::CACHEABLE_LIST_PREFIX_USER_INVITE => 'create_time',
            CacheConstant::CACHEABLE_LIST_PREFIX_GS_TICKET => 'create_time',
        ];
//        $conditionMap = [
//            CacheConstant::CACHEABLE_LIST_PREFIX_SYS_MAIL_TEMPLATE => [ "enable_flag = 1" ],
//            CacheConstant::CACHEABLE_LIST_PREFIX_USER_MAIL => [ "enable_flag = 1" ],
//            CacheConstant::CACHEABLE_LIST_PREFIX_USER_MAIL_ALIAS => [ "enable_flag = 1" ],
//            CacheConstant::CACHEABLE_LIST_PREFIX_CUSTOMER_BLACKLIST => [ "enable_flag = 1" ],
//            CacheConstant::CACHEABLE_LIST_PREFIX_DEPARTMENT => [ "enable_flag = 1" ],
//            CacheConstant::CACHEABLE_LIST_PREFIX_USER_RELATIVE => [ "enable_flag = 1" ],
//        ];
        $enableFlagMap = [
            CacheConstant::CACHEABLE_LIST_PREFIX_SYS_MAIL_TEMPLATE => [ "enable_flag", "1" ],
            CacheConstant::CACHEABLE_LIST_PREFIX_USER_MAIL => [ "enable_flag", "1" ],
            CacheConstant::CACHEABLE_LIST_PREFIX_USER_MAIL_ALIAS => [ "enable_flag", "1" ],
            CacheConstant::CACHEABLE_LIST_PREFIX_CUSTOMER_BLACKLIST => [ "enable_flag", "1" ],
            CacheConstant::CACHEABLE_LIST_PREFIX_DEPARTMENT => [ "enable_flag", "1" ],
            CacheConstant::CACHEABLE_LIST_PREFIX_USER_RELATIVE => [ "enable_flag", "1" ],
        ];
        $pkMap = [
            CacheConstant::CACHEABLE_LIST_PREFIX_ALIBABA_STORE_ACCOUNT => [ 'client_id', 'seller_account_id', 'store_id'],
        ];

        $greyConditionSql = '';
        if ($greyNum) {
            foreach ($greyNum as $greyNumData) {
                $greyConditionList[] = "client_id like '%$greyNumData'";
            }
            if (!empty($greyConditionList)) {
                $greyConditionSql = '(' . implode(' or ', $greyConditionList) . ')';
            }
        }

        foreach (CacheConstant::CACHEABLE_REPO_MAP as $prefix => $className) {
            if (in_array($prefix, $skipCheck)) {
                continue;
            }
            $updateField = $updateTimeMap[$prefix] ?? 'update_time';
            $conditionList = [
                "{$updateField} >= '{$from}'"
            ];

//            foreach ($conditionMap[$prefix] ?? [] as $classCondition) {
//                $conditionList[] = $classCondition ;
//            }

            $cacheObj = new $className();
            $dbDataList = $cacheObj->getValidateData($conditionList, $greyConditionSql);
            if (empty($dbDataList)) {
                \LogUtil::info("$className from {$from} no update, skip");
                continue;
            }
            $clientDbDataMap = [];
            // 聚合client
            foreach ($dbDataList as $k => $dbData) {
                $dbClientId = $dbData['client_id'] ?? 0;
                if (!empty($pkMap[$prefix])) {
                    $keys = array_map(function($k) use ($dbData) {
                        return $dbData[$k];
                    }, $pkMap[$prefix]);
                    $clientDbDataMap[$dbClientId][implode('-', $keys)] = $dbData;
                } else {
                    $clientDbDataMap[$dbClientId][$k] = $dbData;
                }
            }

            \LogUtil::info("$className from {$from} updated client count:" . count($clientDbDataMap));

            //获取缓存数据
            foreach ($clientDbDataMap as $clientId => $clientData) {
                if ($validateResult = $cacheObj->validateByData($clientId, $clientData, $enableFlagMap[$prefix] ?? [])) {
                    $result[$className][$clientId] = $validateResult;
                }
            }
        }

        //refresh
        foreach ($result as $cacheClass => $clientValidateResult) {
            if ($clientValidateResult) {
                \LogUtil::info("cacheable_list_not_validate", [
                    'cacheName' => $cacheClass,
                    'detail' => $clientValidateResult
                ]);
            }
            foreach ($clientValidateResult as $clientId => $validateResult) {
                if ($validateResult) {
                    if ($refresh) {
                        $cacheObj = new $cacheClass($clientId);
                        $cacheObj->refreshCache();
                    }
                }
            }
        }

        return $result;
    }

    public function debugGetAllCache()
    {
        $res = [];
        $res['data'] = $this->getRedis()->hgetall($this->getCachekey());
        $indexColumns = $this->indexColumns() ?? [];
        foreach ($indexColumns as $column) {
            $res['index'][$column] = $this->getRedis()->hgetall($this->getCachekey($column));
        }

        return $res;
    }

}