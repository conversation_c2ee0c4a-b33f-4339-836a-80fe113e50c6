<?php
/**
 * Created by PhpStorm.
 * User: onegong
 * Date: 2023-11-26
 * Time: 2:46 PM
 */

use common\library\custom_field\CustomFieldService;
use common\library\object\object_define\Constant as objConstant;
use common\library\object\field\FieldConstant;

return [
    //todo 目前客户、客户联系人、商机、产品配置，仅对字段类型做转换，若需要迁移该对象，需要重新定义其他配置
    \Constants::TYPE_COMPANY => [
        'field' => [
            'address' => [         //联系地址
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'address',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'annual_procurement' => [         //年采购额
                'columns' => [
                    'data_key' => 'annual_procurement',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
                'ext_info' => [
                    'item_type' => FieldConstant::VALUE_ITEM_TYPE,
                    'value_type' => FieldConstant::SELECT_VALUE_TYPE_VALUE_MAP_LABEL,
                    'value' => [
                        ["value" => 0, "label" => "无采购额",],
                        ["value" => 1, "label" => "0~1千美元",],
                        ["value" => 2, "label" => "1千～5千美元",],
                        ["value" => 3, "label" => "5千～1万美元",],
                        ["value" => 4, "label" => "1万～3万美元",],
                        ["value" => 5, "label" => "3万～5万美元",],
                        ["value" => 6, "label" => "5万～10万美元",],
                        ["value" => 7, "label" => "10万～30万美元",],
                        ["value" => 8, "label" => "30万～50万美元",],
                        ["value" => 9, "label" => "50万～100万美元",],
                        ["value" => 10, "label" => "100万～500万美元",],
                        ["value" => 11, "label" => "500万美元以上",]
                    ]
                ],
            ],
            'biz_type' => [         //客户类型
                'columns' => [
                    'data_key' => 'biz_type',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'ext_info' => [
                    'item_type' => FieldConstant::VALUE_ITEM_TYPE,
                    'value_type' => FieldConstant::SELECT_VALUE_TYPE_VALUE_EQUAL_LABEL,
                    'value' => [
                        "原材料供应商",
                        "生产商",
                        "加盟商",
                        "渠道商",
                        "贸易商",
                        "代理商",
                        "批发商",
                        "分销商",
                        "代销商",
                        "零售商",
                        "采购办事处",
                        "采购咨询公司",
                        "出口商",
                        "进口商",
                        "个人消费者",
                        "机构\/团体消费者",
                        "工程商",
                        "其他"
                    ]
                ]
            ],
            'category_ids' => [         //主营产品
                'field_type' => FieldConstant::TYPE_SELECT,
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'category_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
                'ext_info' => [
                    "item_type" => FieldConstant::ITEM_SETTING_ITEM_TYPE,
                    "is_item_setting" => false,
                ]
            ],
            'city' => [         //城市
                'columns' => [
                    'data_key' => 'city',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'ext_info' => [
                    'parent_field' => 'country_region',
                ]
            ],
            'country' => [         //国家地区
                'columns' => [
                    'data_key' => 'country',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'ext_info' => [
                    'parent_field' => 'country_region',
                ]
            ],
            'cus_tag' => [         //客户标签
                'field_type' => FieldConstant::TYPE_SELECT,
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                ],
                'ext_info' => [
                    "item_type" => FieldConstant::ITEM_SETTING_ITEM_TYPE,
                    "is_item_setting" => false,
                ]
            ],
            'fax' => [         //传真
                'field_type' => FieldConstant::TYPE_FAX,
                'columns' => [
                    'data_key' => 'fax',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'group_id' => [         //客户分组
                'columns' => [
                    'data_key' => 'group_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'ext_info' => [
                    "item_type" => FieldConstant::ITEM_SETTING_ITEM_TYPE,
                    "is_item_setting" => true,
                    "item_setting_model" => Constants::TYPE_COMPANY,
                    "item_setting_item_type" => \common\library\setting\item\ItemSettingConstant::ITEM_TYPE_GROUP
                ]
            ],
            'homepage' => [         //主页
                'field_type' => FieldConstant::TYPE_URL,
                'columns' => [
                    'data_key' => 'homepage',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'image_list' => [         //图片
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'image_list',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            'intention_level' => [         //采购意向
                'columns' => [
                    'data_key' => 'intention_level',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
                'ext_info' => [
                    'item_type' => FieldConstant::VALUE_ITEM_TYPE,
                    'value_type' => FieldConstant::SELECT_VALUE_TYPE_VALUE_MAP_LABEL,
                    'value' => [
                        ["value" => 0, "label" => "未知",],
                        ["value" => 1, "label" => "低",],
                        ["value" => 2, "label" => "中",],
                        ["value" => 3, "label" => "高",]
                    ]
                ]
            ],
            'lonlat' => [         //经维度坐标
                'field_type' => FieldConstant::TYPE_COORDINATE,
                'columns' => [
                    'data_key' => ["longitude", "latitude"]
                ],
            ],
            'name' => [         //公司名称
                'columns' => [
                    'data_key' => 'name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'next_follow_up_time' => [         //下次跟进时间
                'columns' => [
                    'data_key' => 'next_follow_up_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'origin_list' => [         //客户来源
                'field_type' => FieldConstant::TYPE_SELECT,
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'origin_list',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
                'ext_info' => [
                    "item_type" => FieldConstant::ITEM_SETTING_ITEM_TYPE,
                    "is_item_setting" => true,
                    "item_setting_model" => Constants::TYPE_COMPANY,
                    "item_setting_item_type" => \common\library\setting\item\ItemSettingConstant::ITEM_TYPE_ORIGIN
                ]
            ],
             'origin' => [         // 客户来源 NOTE: 兼容历史数据，避免报错
                 'field_type' => FieldConstant::TYPE_SELECT,
                 'columns' => [
                     'data_key' => 'origin',
                     'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                 ],
                 'ext_info' => [
                     "item_type" => FieldConstant::ITEM_SETTING_ITEM_TYPE,
                     "is_item_setting" => true,
                     "item_setting_model" => Constants::TYPE_COMPANY,
                     "item_setting_item_type" => \common\library\setting\item\ItemSettingConstant::ITEM_TYPE_ORIGIN
                 ]
             ],
            'pool_id' => [         //公海分组
                'field_type' => FieldConstant::TYPE_SELECT,
                'is_array_flag' => FieldConstant::IS_NOT_ARRAY,
                'columns' => [
                    'data_key' => 'pool_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'ext_info' => [
                    "item_type" => FieldConstant::ITEM_SETTING_ITEM_TYPE,
                    "is_item_setting" => true,
                    "item_setting_model" => Constants::TYPE_COMPANY,
                    "item_setting_item_type" => \common\library\setting\item\ItemSettingConstant::ITEM_TYPE_POOL
                ],
                'default_value' => [
                    "type" => FieldConstant::DEFAULT_VALUE_TYPE_FIXED,
                    "value" => ""
                ]
            ],
            'product_group_ids' => [         //产品分组
                'field_type' => FieldConstant::TYPE_SELECT,
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'product_group_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
                'ext_info' => [
                    "item_type" => FieldConstant::ITEM_SETTING_ITEM_TYPE,
                    "is_item_setting" => true,
                    "item_setting_model" => Constants::TYPE_PRODUCT,
                    "item_setting_item_type" => \common\library\setting\item\ItemSettingConstant::ITEM_TYPE_GROUP
                ]
            ],
            'province' => [         //省份
                //todo
                'columns' => [
                    'data_key' => 'province',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'ext_info' => [
                    'parent_field' => 'country_region',
                ]
            ],
            'remark' => [         //备注
                'columns' => [
                    'data_key' => 'remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
                'field_name' => '公司备注'
            ],
            'scale_id' => [         //规模
                'columns' => [
                    'data_key' => 'scale_id',
                    'data_db_type' => FieldConstant::DB_TYPE_INTEGER,
                ],
                'ext_info' =>  [
                    'item_type' => FieldConstant::VALUE_ITEM_TYPE,
                    'value_type' => FieldConstant::SELECT_VALUE_TYPE_VALUE_MAP_LABEL,
                    'value' => [
                        ["value" => 1, "label" => "少于59人",],
                        ["value" => 2, "label" => "60-149人",],
                        ["value" => 3, "label" => "150-499人",],
                        ["value" => 4, "label" => "500-999人",],
                        ["value" => 5, "label" => "1000-4999人",],
                        ["value" => 6, "label" => "5000人以上",]
                    ]
                ]
            ],
            'serial_id' => [         //客户编号
                'columns' => [
                    'data_key' => 'serial_id',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'short_name' => [         //简称
                'columns' => [
                    'data_key' => 'short_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'star' => [         //客户星级
                'columns' => [
                    'data_key' => 'star',
                    'data_db_type' => FieldConstant::DB_TYPE_INTEGER,
                ],
                'ext_info' =>  [
                    'item_type' => FieldConstant::VALUE_ITEM_TYPE,
                    'value_type' => FieldConstant::SELECT_VALUE_TYPE_VALUE_MAP_LABEL,
                    'value' => [
                        ["value" => 0, "label" => "0星",],
                        ["value" => 1, "label" => "1星",],
                        ["value" => 2, "label" => "2星",],
                        ["value" => 3, "label" => "3星",],
                        ["value" => 4, "label" => "4星",],
                        ["value" => 5, "label" => "5星",],
                    ]
                ]
            ],
            'tel' => [         //座机
                'columns' => [
                    'data_key' => 'tel',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'ext_info' => [
                    'parent_field' => 'tel_full_new',
                ]
            ],
            'timezone' => [         //时区
                'columns' => [
                    'data_key' => 'timezone',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'ext_info' => [
                    'item_type' => FieldConstant::VALUE_ITEM_TYPE,
                    'value_type' => FieldConstant::SELECT_VALUE_TYPE_VALUE_MAP_LABEL,
                    'value' => [
                        ["label" => "零时区：伦敦", "value" => "0"],
                        ["label" => "东一区：罗马，巴黎", "value" => "1"],
                        ["label" => "东二区：雅典，以色列", "value" => "2"],
                        ["label" => "东三区：莫斯科，科威特", "value" => "3"],
                        ["label" => "东四区：喀布尔", "value" => "4"],
                        ["label" => "东五区：伊斯兰堡，卡拉奇", "value" => "5"],
                        ["label" => "东六区：阿拉木图，科伦坡", "value" => "6"],
                        ["label" => "东七区：曼谷，雅加达", "value" => "7"],
                        ["label" => "东八区：北京，香港，台湾", "value" => "8"],
                        ["label" => "东九区：东京", "value" => "9"],
                        ["label" => "东十区：悉尼", "value" => "10"],
                        ["label" => "东十一区：霍尼亚拉，马加丹", "value" => "11"],
                        ["label" => "东西十二区: 奥克兰", "value" => "12"],
                        ["label" => "西十一区：帕果帕果，阿洛菲", "value" => "-11"],
                        ["label" => "西十区：夏威夷", "value" => "-10"],
                        ["label" => "西九区：阿拉斯加", "value" => "-9"],
                        ["label" => "西八区：洛杉矶，旧金山", "value" => "-8"],
                        ["label" => "西七区：盐湖城、丹佛、凤凰城", "value" => "-7"],
                        ["label" => "西六区：芝加哥，休斯顿，亚特兰大", "value" => "-6"],
                        ["label" => "西五区：纽约，华盛顿，波士顿", "value" => "-5"],
                        ["label" => "西四区：加拿大，加拉加斯", "value" => "-4"],
                        ["label" => "西三区：巴西利亚", "value" => "-3"],
                        ["label" => "西二区：协调世界时", "value" => "-2"],
                        ["label" => "西一区：佛得角群岛", "value" => "-1"]
                    ]
                ]
            ],
            'trail_status' => [         //客户阶段
                'columns' => [
                    'data_key' => 'trail_status',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'ext_info' => [
                    "item_type" => FieldConstant::ITEM_SETTING_ITEM_TYPE,
                    "is_item_setting" => true,
                    "item_setting_model" => Constants::TYPE_COMPANY,
                    "item_setting_item_type" => \common\library\setting\item\ItemSettingConstant::ITEM_TYPE_STATUS
                ]
            ],
            'ali_store_id' => [         //来源详情
                'field_type' => FieldConstant::TYPE_OBJECT_MULTIPLE,
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'ali_store_id',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
                'ext_info' => [
                    "is_create_object" => false, //是否已创建对象
                ]
            ],
            'company_id' => [
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'columns' => [
                    'data_key' => 'company_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'country_region' => [
                'field_type' => FieldConstant::TYPE_COUNTRY_REGION,
                'columns' => [
                    'data_key' => 'country_region',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
                'ext_info' => [
                    'children_field' => [
                        "country",
                        "province",
                        "city"
                    ]
                ],
            ],
            'private_user_time' => [
                'columns' => [
                    'data_key' => 'private_user_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
        ],
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'company_id',
                'field_name' => '客户',
                'group_id' => 1,
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'company_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => [
                ]
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'country_region',
                'field_name' => '国家地区(新)',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_COUNTRY_REGION,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'children_field' => [
                        "country",
                        "province",
                        "city"
                    ]
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'country_region',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'tel_full_new',
                'field_name' => '联系电话（新）',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_TEL,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'children_field' => [
                        "tel_area_code",
                        "tel",
                    ]
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'tel_full_new',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'swarm_ids',
                'field_name' => '客群',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_SELECT,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    "item_type" => FieldConstant::ITEM_SETTING_ITEM_TYPE,
                    "is_item_setting" => false,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [

                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'relate_lead_ids',
                'field_name' => '关联线索',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_DATA_SET,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'main_lead_id',
                'field_name' => '来源线索',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    "is_create_object" => false, //是否已创建对象
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'main_lead_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'customers',
                'field_name' => '关联联系人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_SUB_OBJECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                  "object_name" => objConstant::OBJ_CUSTOMER
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                ],
            ],
            //1
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'last_owner',
                'field_name' => '原跟进人',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'last_owner',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            //alibaba_user_id、alibaba_last_owner存储的不是user_id
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'alibaba_user_id',
                'field_name' => '客户通跟进人',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_SELECT,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    "item_type" => FieldConstant::ITEM_SETTING_ITEM_TYPE,
                    "is_item_setting" => false,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'alibaba_user_id',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'alibaba_last_owner',
                'field_name' => '客户通原跟进人',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_SELECT,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    "item_type" => FieldConstant::ITEM_SETTING_ITEM_TYPE,
                    "is_item_setting" => false,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'alibaba_last_owner',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'duplicate_flag',
                'field_name' => '是否疑似重复',
                'group_id' => CustomFieldService::COMPANY_GROUP_CHARACTERISTIC,
                'field_type' => FieldConstant::TYPE_FLAG,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'duplicate_flag',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'customer_count',
                'field_name' => '联系人数',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'customer_count',
                    'data_db_type' => FieldConstant::DB_TYPE_INTEGER,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'transaction_order_amount',
                'field_name' => '成交订单金额',
                'group_id' => CustomFieldService::COMPANY_GROUP_OTHER,
                'field_type' => FieldConstant::TYPE_PRICE,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'transaction_order_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'transaction_order_first_amount',
                'field_name' => '首次成交订单金额',
                'group_id' => CustomFieldService::COMPANY_GROUP_OTHER,
                'field_type' => FieldConstant::TYPE_PRICE,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'transaction_order_first_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'transaction_order_amount_avg',
                'field_name' => '成交订单均价',
                'group_id' => CustomFieldService::COMPANY_GROUP_OTHER,
                'field_type' => FieldConstant::TYPE_PRICE,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'transaction_order_amount_avg',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'success_opportunity_amount_cny',
                'field_name' => '赢单商机金额(CNY)',
                'group_id' => CustomFieldService::COMPANY_GROUP_OTHER,
                'field_type' => FieldConstant::TYPE_PRICE,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'success_opportunity_amount_cny',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'success_opportunity_amount_usd',
                'field_name' => '赢单商机金额(USD)',
                'group_id' => CustomFieldService::COMPANY_GROUP_OTHER,
                'field_type' => FieldConstant::TYPE_PRICE,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'success_opportunity_amount_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            //11
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'ongoing_opportunity_count',
                'field_name' => '进行中的商机数',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'ongoing_opportunity_count',
                    'data_db_type' => FieldConstant::DB_TYPE_INTEGER,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'success_opportunity_amount_avg_cny',
                'field_name' => '赢单商机均价(CNY)',
                'group_id' => CustomFieldService::COMPANY_GROUP_OTHER,
                'field_type' => FieldConstant::TYPE_PRICE,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'success_opportunity_amount_avg_cny',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'success_opportunity_amount_avg_usd',
                'field_name' => '赢单商机均价(USD)',
                'group_id' => CustomFieldService::COMPANY_GROUP_OTHER,
                'field_type' => FieldConstant::TYPE_PRICE,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'success_opportunity_amount_avg_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'edit_time',
                'field_name' => '资料更新时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'edit_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'latest_write_follow_up_time',
                'field_name' => '最近「写跟进」时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'latest_write_follow_up_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'latest_transaction_order_time',
                'field_name' => '最近成交订单日期',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATE,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'latest_transaction_order_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'latest_success_opportunity_time',
                'field_name' => '最近赢单日期',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATE,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'latest_success_opportunity_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'recent_follow_up_time',
                'field_name' => '最近跟进时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'recent_follow_up_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'tips_latest_update_time',
                'field_name' => 'Tips最近更新时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'tips_latest_update_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'send_mail_time',
                'field_name' => '最近发件时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'send_mail_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            //21
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'receive_mail_time',
                'field_name' => '最近收件时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'receive_mail_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'edm_time',
                'field_name' => '最近发EDM时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'edm_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'latest_receive_ali_tm_time',
                'field_name' => '最近收到阿里TM消息时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'latest_receive_ali_tm_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'latest_send_ali_tm_time',
                'field_name' => '最近发送阿里TM消息时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'latest_send_ali_tm_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'latest_receive_ali_trade_time',
                'field_name' => '最近收到阿里TM询盘时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'latest_receive_ali_trade_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'next_move_to_public_date',
                'field_name' => '下次移入公海日期',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATE,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'next_move_to_public_date',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'transaction_order_first_time',
                'field_name' => '首次成交订单日期',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATE,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'transaction_order_first_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'success_opportunity_first_time',
                'field_name' => '首次赢单日期',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATE,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'success_opportunity_first_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'latest_whatsapp_time',
                'field_name' => '最近WhatsApp沟通时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'latest_whatsapp_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'latest_whatsapp_receive_time',
                'field_name' => '最近whatsApp接收时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'latest_whatsapp_receive_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            //31
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'latest_whatsapp_send_time',
                'field_name' => '最近whatsApp发送时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'latest_whatsapp_send_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'latest_whatsapp_business_receive_time',
                'field_name' => '最近WhatsApp企业版接收时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'latest_whatsapp_business_receive_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'latest_whatsapp_business_send_time',
                'field_name' => '最近WhatsApp企业版发送时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'latest_whatsapp_business_send_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'latest_facebook_receive_time',
                'field_name' => '最近facebook接收时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'latest_facebook_receive_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'latest_facebook_send_time',
                'field_name' => '最近facebook发送时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'latest_facebook_send_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'latest_ins_time',
                'field_name' => '最近Instagram Messenger沟通时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'latest_ins_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'latest_ins_receive_time',
                'field_name' => '最近Instagram Messenger接收时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'latest_ins_receive_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'latest_ins_send_time',
                'field_name' => '最近Instagram Messenger发送时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'latest_ins_send_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'latest_wechat_time',
                'field_name' => '最近企业微信沟通时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'latest_wechat_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'latest_wechat_receive_time',
                'field_name' => '最近企业微信接收时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'latest_wechat_receive_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            //41
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'latest_wechat_send_time',
                'field_name' => '最近企业微信发送时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'latest_wechat_send_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'alibaba_first_sync_time',
                'field_name' => '关联客户通客户首次同步时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'alibaba_first_sync_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'alibaba_recent_sync_time',
                'field_name' => '关联客户通客户最近同步时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'alibaba_recent_sync_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'lead_archive_time',
                'field_name' => '最近转化线索创建时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'lead_archive_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'order_time',
                'field_name' => '最近联系时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'order_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'private_time',
                'field_name' => '最近进入私海时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'private_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'public_time',
                'field_name' => '最近进入公海时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'public_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'release_count',
                'field_name' => '进入公海次数',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'release_count',
                    'data_db_type' => FieldConstant::DB_TYPE_INTEGER,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'create_user',
                'field_name' => '创建人',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'create_user',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'last_edit_user',
                'field_name' => '最近修改人',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'last_edit_user',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            //51
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'archive_time',
                'field_name' => '创建时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'archive_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'score',
                'field_name' => '评分',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATA_SET,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'score',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'performance_order_count',
                'field_name' => '成交订单数',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'performance_order_count',
                    'data_db_type' => FieldConstant::DB_TYPE_INTEGER,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'success_opportunity_count',
                'field_name' => '赢单商机数',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'success_opportunity_count',
                    'data_db_type' => FieldConstant::DB_TYPE_INTEGER,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'user_id',
                'field_name' => '跟进人',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'user_id',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'archive_type',
                'field_name' => '创建方式',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_SELECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'item_type' => FieldConstant::VALUE_ITEM_TYPE,
                    'value_type' => FieldConstant::SELECT_VALUE_TYPE_VALUE_MAP_LABEL,
                    'value' => [
                        ["value" => \common\library\customer_v3\company\orm\Company::ARCHIVE_TYPE_NORMAL, "label" => "手动创建"],
                        ["value" => \common\library\customer_v3\company\orm\Company::ARCHIVE_TYPE_AI, "label" => "自动创建"],
                        ["value" => \common\library\customer_v3\company\orm\Company::ARCHIVE_TYPE_ADVICE, "label" => "自动化建档建议"],
                        ["value" => \common\library\customer_v3\company\orm\Company::ARCHIVE_TYPE_ALIBABA, "label" => "客户通同步"],
                        ["value" => \common\library\customer_v3\company\orm\Company::ARCHIVE_TYPE_IMPORT, "label" => "文件导入"],
                        ["value" => \common\library\customer_v3\company\orm\Company::ARCHIVE_TYPE_CARD, "label" => "OKKI名片夹创建"],
                    ]
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'archive_type',
                    'data_db_type' => FieldConstant::DB_TYPE_INTEGER,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'deal_time',
                'field_name' => '最近成交日期',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATE,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'deal_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'ciq_latest_update_time',
                'field_name' => '海关数据更新时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'ciq_latest_update_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'latest_reply_ali_trade_time',
                'field_name' => '最新回复阿里询盘消息时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'latest_reply_ali_trade_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'public_type',
                'field_name' => '进入公海方式',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_SELECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'item_type' => FieldConstant::VALUE_ITEM_TYPE,
                    'value_type' => FieldConstant::SELECT_VALUE_TYPE_VALUE_MAP_LABEL,
                    'value' => [
                        [ "value"=> 0, "label"=> "空" ],
                        [ "value"=> 1, "label"=> "移入公海规则" ],
                        [ "value"=> 2, "label"=> "手动移入" ],
                        [ "value"=> 3, "label"=> "工作流" ],
                        [ "value"=> 4, "label"=> "客户通规则移入" ],
                    ]
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'public_type',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            //61
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'public_reason_id',
                'field_name' => '移入公海原因',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_SELECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'item_type' => FieldConstant::ITEM_SETTING_ITEM_TYPE,
                    "is_item_setting"=> true,
                    "item_setting_model" => Constants::TYPE_COMPANY,
                    "item_setting_item_type" => \common\library\setting\item\ItemSettingConstant::ITEM_TYPE_PUBLIC_REASON
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'public_reason_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'private_user_time',
                'field_name' => '跟进人更新时间',
                'group_id' => CustomFieldService::COMPANY_GROUP_MANAGE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'private_user_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => 'latest_inquiry_status',
                'field_name' => '最新询盘是否有效',
                'group_id' => CustomFieldService::COMPANY_GROUP_CHARACTERISTIC,
                'field_type' => FieldConstant::TYPE_SELECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'item_type' => FieldConstant::VALUE_ITEM_TYPE,
                    'value_type' => FieldConstant::SELECT_VALUE_TYPE_VALUE_MAP_LABEL,
                    'value' => [
                        ["value" => 0, "label" => '否',],
                        ["value" => 1, "label" => '是',],
                        ["value" => 2, "label" => '否',],
                    ],
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'latest_inquiry_status',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_COMPANY,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],

        ]
    ],
    \Constants::TYPE_CUSTOMER => [
        'field' => [
            'birth' => [         //生日
                'field_type' => FieldConstant::TYPE_DATE,
                'columns' => [
                    'data_key' => 'birth',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'contact' => [         //社交平台
                'array_flag' => FieldConstant::IS_ARRAY,
                'field_type' => FieldConstant::TYPE_SOCIAL_PLATFORM,
                'columns' => [
                    'data_key' => 'contact',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'email' => [         //邮箱
                'field_type' => FieldConstant::TYPE_EMAIL,
                'columns' => [
                    'data_key' => 'email',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'gender' => [         //性别
                'columns' => [
                    'data_key' => 'gender',
                    'data_db_type' => FieldConstant::DB_TYPE_INTEGER,
                ],
                'ext_info' =>  [
                    'item_type' => FieldConstant::VALUE_ITEM_TYPE,
                    'value_type' => FieldConstant::SELECT_VALUE_TYPE_VALUE_MAP_LABEL,
                    'value' => [
                        ["value" => 0, "label" => "不设置"],
                        ["value" => 1, "label" => "男"],
                        ["value" => 2, "label" => "女"],
                    ]
                ]
            ],
            'image_list' => [         //图片
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'image_list',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            'main_customer_flag' => [         //主要联系人
                'field_type' => FieldConstant::TYPE_FLAG,
                'columns' => [
                    'data_key' => 'main_customer_flag',
                    'data_db_type' => FieldConstant::DB_TYPE_INTEGER,
                ],
            ],
            'name' => [         //昵称
                'columns' => [
                    'data_key' => 'name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'post' => [         //职位
                'columns' => [
                    'data_key' => 'post',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'post_grade' => [         //职级
                'columns' => [
                    'data_key' => 'post_grade',
                    'data_db_type' => FieldConstant::DB_TYPE_INTEGER,
                ],
                'ext_info' => [
                    'item_type' => FieldConstant::VALUE_ITEM_TYPE,
                    'value_type' => FieldConstant::SELECT_VALUE_TYPE_VALUE_MAP_LABEL,
                    'value' => [
                        ["value" => 1, "label" => "普通职员"],
                        ["value" => 2, "label" => "中层管理者"],
                        ["value" => 3, "label" => "高层管理者"],
                    ]
                ]
            ],
            'remark' => [         //备注
                'columns' => [
                    'data_key' => 'remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
                'field_name' => '联系人备注'
            ],
            'tel_list' => [         //联系电话
                'field_type' => FieldConstant::TYPE_TEL,
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'tel_list',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'reach_count' => [
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'reach_count',
                    'data_db_type' => FieldConstant::DB_TYPE_INTEGER,
                ],
            ],
            'reach_success_count' => [
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'reach_success_count',
                    'data_db_type' => FieldConstant::DB_TYPE_INTEGER,
                ],
            ],
        ],
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_CUSTOMER,
                'field' => 'customer_id',
                'field_name' => '客户联系人ID',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_CUSTOMER
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'customer_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_CUSTOMER,
                'field' => 'company_id',
                'field_name' => '客户ID',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_CUSTOMER
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'company_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_CUSTOMER,
                'field' => 'growth_level',
                'field_name' => '阿里买家身份',
                'group_id' => CustomFieldService::CUSTOMER_GROUP_BASIC,
                'field_type' => FieldConstant::TYPE_SELECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'growth_level',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_CUSTOMER,
                'field' => 'suspected_invalid_email_flag',
                'field_name' => '是否疑似失效邮箱',
                'group_id' => CustomFieldService::CUSTOMER_GROUP_BASIC,
                'field_type' => FieldConstant::TYPE_FLAG,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'suspected_invalid_email_flag',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_CUSTOMER,
                'field' => 'forbidden_flag',
                'field_name' => '已禁用联系人',
                'group_id' => CustomFieldService::CUSTOMER_GROUP_BASIC,
                'field_type' => FieldConstant::TYPE_FLAG,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,      //读写
                'columns' => [
                    'data_key' => 'forbidden_flag',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_CUSTOMER,
                'field' => 'reach_status',
                'field_name' => '触达状态',
                'group_id' => CustomFieldService::CUSTOMER_GROUP_BASIC,
                'field_type' => FieldConstant::TYPE_SELECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'reach_status',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_CUSTOMER,
                'field' => 'reach_status_time',
                'field_name' => '最近触达状态时间',
                'group_id' => CustomFieldService::CUSTOMER_GROUP_BASIC,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'reach_status_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],

        ]
    ],
    \Constants::TYPE_LEAD => [ // TODO: 线索paas化后可删除
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_LEAD,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ],
    ],
    //todo 仅对db类型配置，其余配置重新定义
    \Constants::TYPE_OPPORTUNITY => [
        'field' => [
            'account_date' => [         //结束日期
                'columns' => [
                    'data_key' => 'account_date',
                    'data_db_type' => FieldConstant::DB_TYPE_DATE,
                ],
            ],
            'amount' => [         //销售金额
                'columns' => [
                    'data_key' => 'amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'company_id' => [         //客户
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_COMPANY,
                ],
                'columns' => [
                    'data_key' => 'company_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'is_writable' => FieldConstant::WRITABLE_FLAG,
            ],
            'currency' => [         //币种
                'columns' => [
                    'data_key' => 'currency',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'customer_id' => [         //关联联系人
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_CUSTOMER,
                ],
                'columns' => [
                    'data_key' => 'customer_id',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            'department' => [         //归属部门
                'field_type' => FieldConstant::TYPE_DEPARTMENT,
                'columns' => [
                    'data_key' => 'department',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'exchange_rate' => [         //汇率
                'field_type' => FieldConstant::TYPE_EXCHANGE_RATE,
                'columns' => [
                    'data_key' => 'exchange_rate',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'fail_remark' => [         //输单描述
                'columns' => [
                    'data_key' => 'fail_remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'fail_type' => [         //输单原因
                'columns' => [
                    'data_key' => 'fail_type',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'flow_id' => [         //销售流程
                'columns' => [
                    'data_key' => 'flow_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'is_writable' => FieldConstant::WRITABLE_FLAG,
            ],
            'handler' => [         //协同跟进人
                'field_type' => FieldConstant::TYPE_USER,
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'handler',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            'main_lead_id' => [         //来源线索
                'columns' => [
                    'data_key' => 'main_lead_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'main_user' => [         //负责人
                'field_type' => FieldConstant::TYPE_USER,
                'columns' => [
                    'data_key' => 'main_user',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'name' => [         //商机名称
                'columns' => [
                    'data_key' => 'name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'origin_list' => [         //商机来源
                'field_type' => FieldConstant::TYPE_SELECT,
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'origin_list',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            'remark' => [         //备注
                'columns' => [
                    'data_key' => 'remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'stage' => [         //销售阶段
                'columns' => [
                    'data_key' => 'stage',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'type' => [         //商机类型
                'columns' => [
                    'data_key' => 'type',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
        ],
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_OPPORTUNITY,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ]
    ],
    //todo 仅对db类型、字段归属哪个对象配置，其余配置重新定义
    \Constants::TYPE_PRODUCT => [
        'field' => [
            'ali_store_id' => [         //国际站店铺
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'ali_store_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'is_parts' => [        //是否可为配件
                'field_type' => FieldConstant::TYPE_FLAG,
                'columns' => [
                    'data_key' => 'is_parts',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'carton_gross_weight' => [         //单箱毛重
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'columns' => [
                    'data_key' => 'carton_gross_weight',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'carton_net_weight' => [         //单箱净重
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'columns' => [
                    'data_key' => 'carton_net_weight',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'carton_size' => [         //单箱尺寸(长*宽*高cm)
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'columns' => [
                    'data_key' => ["carton_size_length", "carton_size_weight", "carton_size_height"],
                ],
                'ext_info' => [
                    'children_field' => ["carton_size_length", "carton_size_weight", "carton_size_height"]
                ]
            ],
            'carton_volume' => [         //单箱体积
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'columns' => [
                    'data_key' => 'carton_volume',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'category_ids' => [         //产品类目
                'field_type' => FieldConstant::TYPE_SELECT,
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'category_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            'cn_name' => [         //中文产品名称
                'columns' => [
                    'data_key' => 'cn_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'combine_info' => [         //组合情况
                'field_type' => FieldConstant::TYPE_DATA_SET,
                'columns' => [],
            ],
            'cost_with_tax' => [         //含税成本价
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'field_type' => FieldConstant::TYPE_DATA_SET,
                'columns' => [
                    'data_key' => ["cost_currency", "cost", "cost_type", "associate_cost_flag"],
                ],
            ],
            'count_per_carton' => [         //每箱产品数
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'columns' => [
                    'data_key' => 'count_per_carton',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'count_per_package' => [         //每包装产品数
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'columns' => [
                    'data_key' => 'count_per_package',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'create_time' => [         //创建时间
                'columns' => [
                    'data_key' => 'create_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'create_type' => [         //自动创建
                'columns' => [
                    'data_key' => 'create_type',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'create_user' => [         //创建人
                'field_type' => FieldConstant::TYPE_USER,
                'columns' => [
                    'data_key' => 'create_user',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'customs_cn_name' => [         //报关中文名
                'columns' => [
                    'data_key' => 'customs_cn_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'customs_name' => [         //报关英文名
                'columns' => [
                    'data_key' => 'customs_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'description' => [         //产品描述
                'columns' => [
                    'data_key' => 'description',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'fob' => [         //离岸价
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'field_type' => FieldConstant::TYPE_DATA_SET,
                'columns' => [
                    'data_key' => ["price_currency", "fob_price", "price_min", "price_max", "quantity", "fob_type", "gradient_price"],
                ],
            ],
            'from_url' => [         //产品链接
                'columns' => [
                    'data_key' => 'from_url',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'group_id' => [         //产品分组
                'columns' => [
                    'data_key' => 'group_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'hs_code' => [         //海关编码
                'columns' => [
                    'data_key' => 'hs_code',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'images' => [         //产品图片
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'images',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'info_json' => [         //产品属性
                'field_type' => FieldConstant::TYPE_DATA_SET,
                'columns' => [
                    'data_key' => 'info_json',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'model' => [         //产品型号
                'columns' => [
                    'data_key' => 'model',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'name' => [         //产品名称
                'columns' => [
                    'data_key' => 'name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'package_gross_weight' => [         //包装毛重
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'columns' => [
                    'data_key' => 'package_gross_weight',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'package_remark' => [         //包装说明
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'columns' => [
                    'data_key' => 'package_remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'package_unit' => [
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'columns' => [
                    'data_key' => 'package_unit',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ]
            ],
            'package_size' => [         //包装尺寸
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'field_type' => FieldConstant::TYPE_DATA_SET,
                'columns' => [
                    'data_key' => ["package_size_length", "package_size_weight", "package_size_height"],
                ],
                'ext_info' => [
                    'children_field' => ["package_size_length", "package_size_weight", "package_size_height"]
                ]
            ],
            'package_type' => [         //包装方式
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'columns' => [
                    'data_key' => 'package_type',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'package_volume' => [         //包装体积
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'columns' => [
                    'data_key' => 'package_volume',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'product_net_weight' => [         //产品净重
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'columns' => [
                    'data_key' => 'product_net_weight',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'product_no' => [         //产品编号
                'columns' => [
                    'data_key' => 'product_no',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'product_remark' => [         //产品备注
                'columns' => [
                    'data_key' => 'product_remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'product_size' => [         //产品尺寸(长*宽*高cm)
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'field_type' => FieldConstant::TYPE_DATA_SET,
                'columns' => [
                    'data_key' => ["product_size_length", "product_size_weight", "product_size_height"],
                ],
                'ext_info' => [
                    'children_field' => ["product_size_length", "product_size_weight", "product_size_height"]
                ]
            ],
            'product_size_length' => [
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'columns' => [
                    'data_key' => 'product_size_length',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'ext_info' => [
                    'parent_field' => 'product_size',
                    "decimal" => 4,
                    "decimal_logic" => "round",
                    "allow_setting_decimal" => 1,
                ]
            ],
             'product_size_weight' => [
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'columns' => [
                    'data_key' => 'product_size_weight',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'ext_info' => [
                    'parent_field' => 'product_size',
                    "decimal" => 4,
                    "decimal_logic" => "round",
                    "allow_setting_decimal" => 1,
                ]
            ],
            'product_size_height' => [
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'columns' => [
                    'data_key' => 'product_size_height',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'ext_info' => [
                    'parent_field' => 'product_size',
                    "decimal" => 4,
                    "decimal_logic" => "round",
                    "allow_setting_decimal" => 1,
                ]
            ],

            'carton_size_length' => [
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'columns' => [
                    'data_key' => 'carton_size_length',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'ext_info' => [
                    'parent_field' => 'carton_size',
                    "decimal" => 4,
                    "decimal_logic" => "round",
                    "allow_setting_decimal" => 1,
                ]
            ],
            'carton_size_weight' => [
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'columns' => [
                    'data_key' => 'carton_size_weight',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'ext_info' => [
                    'parent_field' => 'carton_size',
                    "decimal" => 4,
                    "decimal_logic" => "round",
                    "allow_setting_decimal" => 1,
                ]
            ],
            'carton_size_height' => [
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'columns' => [
                    'data_key' => 'carton_size_height',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'ext_info' => [
                    'parent_field' => 'carton_size',
                    "decimal" => 4,
                    "decimal_logic" => "round",
                    "allow_setting_decimal" => 1,
                ]
            ],


            'package_size_length' => [
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'columns' => [
                    'data_key' => 'package_size_length',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'ext_info' => [
                    'parent_field' => 'package_size',
                    "decimal" => 4,
                    "decimal_logic" => "round",
                    "allow_setting_decimal" => 1,
                ]
            ],
            'package_size_weight' => [
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'columns' => [
                    'data_key' => 'package_size_weight',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'ext_info' => [
                    'parent_field' => 'package_size',
                    "decimal" => 4,
                    "decimal_logic" => "round",
                    "allow_setting_decimal" => 1,
                ]
            ],
            'package_size_height' => [
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'columns' => [
                    'data_key' => 'package_size_height',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'ext_info' => [
                    'parent_field' => 'package_size',
                    "decimal" => 4,
                    "decimal_logic" => "round",
                    "allow_setting_decimal" => 1,
                ]
            ],

            'product_type' => [         //产品类型
                'field_type' => FieldConstant::TYPE_FLAG,
                'columns' => [
                    'data_key' => 'product_type',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'product_volume' => [         //产品体积
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'columns' => [
                    'data_key' => 'product_volume',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'sku_attributes' => [         //产品规格
                'field_type' => FieldConstant::TYPE_DATA_SET,
                'columns' => [
                    'data_key' => 'sku_attributes',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'source_type' => [         //创建方式
                'columns' => [
                    'data_key' => 'source_type',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'tax_refund_rate' => [         //退税税率
                'field_type' => FieldConstant::TYPE_PERCENTAGE,
                'columns' => [
                    'data_key' => 'tax_refund_rate',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'unit' => [         //计量单位
                'columns' => [
                    'data_key' => 'unit',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'update_time' => [         //更新时间
                'columns' => [
                    'data_key' => 'update_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'update_user' => [         //修改人
                'field_type' => FieldConstant::TYPE_USER,
                'columns' => [
                    'data_key' => 'update_user',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'vat_rate' => [         //增值税率
                'field_type' => FieldConstant::TYPE_PERCENTAGE,
                'columns' => [
                    'data_key' => 'vat_rate',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'cost' => [
                'object_name' => \common\library\object\object_define\Constant::OBJ_PRODUCT_SKU,
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'cost',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ]
        ],
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_PRODUCT,
                'field' => 'product_id',
                'field_name' => '产品',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => [


                ]
            ],
            [
                'object_name' => objConstant::OBJ_PRODUCT,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'from' => [],
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ],
    ],
    \Constants::TYPE_PRODUCT_SKU => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'field' => 'sku_id',
                'field_name' => '产品规格',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'sku_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => [


                ]

            ],
            [
                'object_name' => objConstant::OBJ_PRODUCT_SKU,
                'field' => 'product_id',
                'field_name' => '产品',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_MASTER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_PRODUCT,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => [


                ]

            ],
        ],
        'field' => [
            'cost' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'cost',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ]
        ]
    ],

    \Constants::TYPE_ORDER => [
        'field' => [
            'vat_rate' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_PERCENTAGE,
                'function_type' => FieldConstant::FUNCTION_TYPE_LINK,
                'columns' => [
                    'data_key' => 'vat_rate',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'users' => [
                'field_type' => FieldConstant::TYPE_DATA_SET,
                'columns' => [
                    'data_key' => 'users',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'unit_price' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'unit_price',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'unit' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'unit',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'ext_info' => [
                    "item_type" => FieldConstant::ITEM_SETTING_ITEM_TYPE,
                    "is_item_setting" => false,
                ]
            ],
            'unique_id' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'transport_mode' => [
                'columns' => [
                    'data_key' => 'transport_mode',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'to_purchase_count' => [
                'columns' => [],
            ],
            'to_outbound_count' => [
                'columns' => [],
            ],
            'todo_shipping_count' => [
                'columns' => [],
            ],
            'tax_refund_type' => [
                'columns' => [
                    'data_key' => 'tax_refund_type',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'tax_refund_rate' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_PERCENTAGE,
                'function_type' => FieldConstant::FUNCTION_TYPE_LINK,
                'columns' => [
                    'data_key' => 'tax_refund_rate',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'quotation_id' => [
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_QUOTATION,
                ],
                'columns' => [
                    'data_key' => 'quotation_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'inquiry_collaboration_id' => [
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION,
                ],
                'columns' => [
                    'data_key' => 'inquiry_collaboration_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'inquiry_product_id' => [
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                ],
                'columns' => [
                    'data_key' => 'inquiry_product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'task_purchase_count' => [
                'columns' => [],
            ],
            'task_outbound_count' => [
                'columns' => [],
            ],
            'target_port' => [
                'columns' => [
                    'data_key' => 'target_port',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'status' => [
                'columns' => [
                    'data_key' => 'status',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'sku_id' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_PRODUCT_SKU,
                ],
                'columns' => [
                    'data_key' => 'sku_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'sku_attributes' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_DATA_SET,
                'columns' => [
                    'data_key' => 'sku_attributes',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'shipment_port' => [
                'columns' => [
                    'data_key' => 'shipment_port',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'shipment_deadline_remark' => [
                'columns' => [
                    'data_key' => 'shipment_deadline_remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'shipment_deadline' => [
                'columns' => [
                    'data_key' => 'shipment_deadline',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'remark' => [
                'columns' => [
                    'data_key' => 'remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'refer_order_no' => [       //关联销售订单编号, 未找到系统何处使用，先同步
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [],
            ],
            'refer_quotation_no' => [
                'object_name' => objConstant::OBJ_ORDER,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [],
            ],
            'receive_remittance_way' => [
                'columns' => [
                    'data_key' => 'receive_remittance_way',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'receive_remittance_remark' => [
                'columns' => [
                    'data_key' => 'receive_remittance_remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'purchase_cost_unit_rmb' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'purchase_cost_unit_rmb',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'product_type' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'product_type',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'product_total_count' => [
                'columns' => [
                    'data_key' => 'product_total_count',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'product_total_amount_usd' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'product_total_amount_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'product_total_amount_rmb' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'product_total_amount_rmb',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'product_total_amount_en_upper' => [
                'columns' => [],
            ],
            'product_total_amount' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'product_total_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'product_remark' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'product_remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'product_package_remark' => [         //订单明细表该字段为package_remark
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'package_remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'product_no' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,       //下游采购订单可能关联了该字段，此处临时方案处理，定义db的类型作为下游关联字段的类型配置
                ],
            ],
            'product_name' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'product_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'product_model' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'product_model',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'product_image' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_IMAGE,
                'columns' => [
                    'data_key' => 'product_image',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'product_id' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_PRODUCT,
                ],
                'columns' => [
                    'data_key' => 'product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'product_enable_flag' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_FLAG,
                'columns' => [],
            ],
            'product_disable_flag' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_FLAG,
                'columns' => [],
            ],
            'product_cn_name' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'product_cn_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'price_contract_remark' => [
                'columns' => [
                    'data_key' => 'price_contract_remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'price_contract' => [
                'columns' => [
                    'data_key' => 'price_contract',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'platform_sku_id' => [                  //该字段已删除
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'platform_sku_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'platform_product_info' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_DATA_SET,
                'columns' => [],
            ],
            'platform_product_id' => [              //该字段已删除
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'platform_product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'percent_of_total_amount' => [
                'field_type' => FieldConstant::TYPE_PERCENTAGE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'columns' => [],
            ],
            'package_volume_subtotal' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'package_volume_subtotal',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'package_volume_amount' => [
                'columns' => [
                    'data_key' => 'package_volume_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'package_volume' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'package_volume',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'package_unit' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'package_unit',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'ext_info' => [
                    "item_type" => FieldConstant::ITEM_SETTING_ITEM_TYPE,
                    "is_item_setting" => false,
                ],
            ],
            'package_remark' => [
                'object_name' => objConstant::OBJ_ORDER,
                'columns' => [
                    'data_key' => 'package_remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ]
            ],
            'package_gross_weight_subtotal' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'package_gross_weight_subtotal',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'package_gross_weight_amount' => [
                'columns' => [
                    'data_key' => 'package_gross_weight_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'package_gross_weight' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'package_gross_weight',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'package_count' => [            //单据产品中无该字段，需补充
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'package_count',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'other_cost' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'other_cost',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'order_no' => [
                'columns' => [
                    'data_key' => 'order_no',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ]
            ],
            'order_gross_margin' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'order_gross_margin',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'order_gross_margin_cny' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'order_gross_margin_cny',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'order_gross_margin_usd' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'order_gross_margin_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'order_contract' => [
                'columns' => [
                    'data_key' => 'order_contract',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ]
            ],
            'opportunity_id' => [
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_OPPORTUNITY,
                ],
                'columns' => [
                    'data_key' => 'opportunity_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ]
            ],
            'offer_data' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'offer_data',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ]
            ],
            'name' => [
                'columns' => [
                    'data_key' => 'name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ]
            ],
            'more_or_less' => [
                'columns' => [
                    'data_key' => 'more_or_less',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ]
            ],
            'marked' => [
                'columns' => [
                    'data_key' => 'marked',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ]
            ],
            'local_product_no' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [],
            ],
            'is_sub_product' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_FLAG,
                'columns' => [],
            ],
            'is_add' => [                   //该字段已删除
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_FLAG,
                'columns' => [],
            ],
            'insurance_remark' => [
                'columns' => [
                    'data_key' => 'insurance_remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ]
            ],
            'hs_code' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'hs_code',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'handler' => [
                'field_type' => FieldConstant::TYPE_USER,
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'handler',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            'gross_profit_margin' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'gross_profit_margin',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'gross_margin' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'gross_margin',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'exchange_rate' => [
                'field_type' => FieldConstant::TYPE_EXCHANGE_RATE,
                'columns' => [
                    'data_key' => 'exchange_rate',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'exchange_rate_usd' => [
                'field_type' => FieldConstant::TYPE_EXCHANGE_RATE,
                'columns' => [
                    'data_key' => 'exchange_rate_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'enable_count' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [],
            ],
            'description' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'description',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'departments' => [
                'field_type' => FieldConstant::TYPE_DATA_SET,
                'columns' => [
                    'data_key' => 'departments',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'customer_phone' => [
                'columns' => [
                    'data_key' => 'customer_phone',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'customer_name' => [
                'columns' => [
                    'data_key' => 'customer_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'customer_id' => [
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_CUSTOMER,
                ],
                'columns' => [
                    'data_key' => 'customer_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'customer_email' => [
                'columns' => [
                    'data_key' => 'customer_email',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'customer_address' => [
                'columns' => [
                    'data_key' => 'customer_address',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'currency' => [
                'columns' => [
                    'data_key' => 'currency',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'create_user' => [
                'field_type' => FieldConstant::TYPE_USER,
                'columns' => [
                    'data_key' => 'create_user',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'create_time' => [
                'field_type' => FieldConstant::TYPE_DATETIME,
                'columns' => [
                    'data_key' => 'create_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'update_time' => [
                'field_type' => FieldConstant::TYPE_DATETIME,
                'columns' => [
                    'data_key' => 'update_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'count_per_package' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'count_per_package',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'country' => [
                'columns' => [
                    'data_key' => 'country',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'count' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'count',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'cost_with_tax_total' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'cost_with_tax_total',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'cost_with_tax' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'cost_with_tax',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'cost_remark' => [
                'columns' => [],
            ],
            'cost_name' => [
                'columns' => [],
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
            ],
            'cost_item_relation_id' => [
                'columns' => [],
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
            ],
            'cost_amount' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'cost_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'cost' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [],
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
            ],
            'company_phone' => [
                'columns' => [
                    'data_key' => 'company_phone',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'company_name' => [
                'columns' => [
                    'data_key' => 'company_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'company_id' => [
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_COMPANY,
                ],
                'columns' => [
                    'data_key' => 'company_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'company_fax' => [
                'columns' => [
                    'data_key' => 'company_fax',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'company_address' => [
                'columns' => [
                    'data_key' => 'company_address',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'combine_product_no' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [],
            ],
            'combine_product_id' => [                       //定义待确定
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [],
            ],
            'cash_collection_percentage' => [
                'field_type' => FieldConstant::TYPE_PERCENTAGE,
                'columns' => [],
            ],
            'cash_collection_info.not_collect_amount_usd' => [
                'field' => 'not_collect_amount_usd',
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'not_collect_amount_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'cash_collection_info.not_collect_amount_rmb' => [
                'field' => 'not_collect_amount_rmb',
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'not_collect_amount_rmb',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'cash_collection_info.collect_amount_usd' => [
                'field' => 'collect_amount_usd',
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'collect_amount_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'cash_collection_info.collect_amount_rmb' => [
                'field' => 'collect_amount_rmb',
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'collect_amount_rmb',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'capital_name' => [
                'columns' => [],
                'is_writable' => FieldConstant::READONLY_FLAG,
            ],
            'capital_bank' => [
                'columns' => [],
                'is_writable' => FieldConstant::READONLY_FLAG,
            ],
            'capital_account_remark' => [
                'columns' => [],
                'is_writable' => FieldConstant::READONLY_FLAG,
            ],
            'capital_account_id' => [
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_CAPITAL_ACCOUNT,
                ],
                'columns' => [
                    'data_key' => 'capital_account_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'capital_account_address' => [
                'columns' => [],
                'is_writable' => FieldConstant::READONLY_FLAG,
            ],
            'bank_info' => [
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'bank_info',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'bank_account' => [
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [],
            ],
            'amount_usd' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'amount_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'amount_rmb' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'amount_rmb',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'amount_en_upper' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [],
            ],
            'amount' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'addition_cost_amount' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'addition_cost_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'addition_cost_amount_rmb' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'addition_cost_amount_rmb',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'addition_cost_amount_usd' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'addition_cost_amount_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'account_date' => [
                'columns' => [
                    'data_key' => 'account_date',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'parts_total_count' => [
                'columns' => [
                    'data_key' => 'parts_total_count',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'product_total_count_no_parts' => [
                'columns' => [
                    'data_key' => 'product_total_count_no_parts',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'is_master_product' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_FLAG,
                'columns' => [
                    'data_key' => 'is_master_product',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'master_id' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'master_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'ratio' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'ratio',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'master_group_id' => [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'columns' => [],
            ],
            'approval_status' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'approval_status',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'update_user' => [
                'field_type' => FieldConstant::TYPE_USER,
                'columns' => [
                    'data_key' => 'update_user',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'cash_collection_status' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [],
            ],
            'stock_up_status' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [],
            ],
            'shipping_status' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [],
            ],
            'end_status' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [],
            ],
            'archive_type' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'archive_type',
                    'data_db_type' => FieldConstant::DB_TYPE_INTEGER,
                ],
            ],
            'ali_order_id' => [
                'field_type' => FieldConstant::TYPE_TEXT, //todo 先处理成文本保证前端精度不缺失
                'columns' => [
                    'data_key' => 'ali_order_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'ali_store_id' => [
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'ali_store_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'seller_account_id' => [
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'seller_account_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'fulfillment_channel' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'fulfillment_channel',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'source_type' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'source_type',
                    'data_db_type' => FieldConstant::DB_TYPE_INTEGER,
                ],
            ],
            'last_sync_time' => [
                'field_type' => FieldConstant::TYPE_DATETIME,
                'columns' => [
                    'data_key' => 'last_sync_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
//            'ali_status_id' => [
//                'field_type' => FieldConstant::TYPE_FLAG,
//                'columns' => [
//                    'data_key' => 'ali_status_id',
//                    'data_db_type' => FieldConstant::DB_TYPE_INTEGER,
//                ],
//            ],
            'ali_status_name' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'ali_status_name',
                    'data_db_type' => FieldConstant::DB_TYPE_INTEGER,
                ],
            ],
            'erp_status' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [],
            ],
            'service_fee_amount' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'service_fee_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'service_fee_amount_rmb' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'service_fee_amount_rmb',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'service_fee_amount_usd' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'service_fee_amount_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'service_fee_currency' => [
                'columns' => [
                    'data_key' => 'service_fee_currency',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'refer_inquiry_collaboration_no' => [
                'object_name' => objConstant::OBJ_ORDER,
                'columns' => [],
            ],
        ],
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_ORDER,
                'field' => 'order_id',
                'field_name' => '销售订单',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'order_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => [


                ]

            ],
            [
                'object_name' => objConstant::OBJ_ORDER,
                'field' => 'quotation_id',
                'field_name' => '报价单ID',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_QUOTATION,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'quotation_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => [
                ]
            ],
            [
                'object_name' => objConstant::OBJ_ORDER,
                'field' => 'inquiry_collaboration_id',
                'field_name' => '询价任务ID',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'inquiry_collaboration_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => [
                ]
            ],
            [
                'object_name' => objConstant::OBJ_ORDER,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'from' => ['user_id']
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ],
    ],
    \Constants::TYPE_ORDER_PRODUCT => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field' => 'id',
                'field_name' => '销售订单产品',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => [


                ]

            ],
            [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field' => 'order_id',
                'field_name' => '销售订单',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_MASTER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_ORDER,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'refer_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                    'bind_field' => 'type',
                ],
                'field_relations' => [


                ]

            ],
            [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field' => 'currency',
                'field_name' => '币种',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_TEXT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_QUOTE,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'currency',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'field_relations' => [
                    'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                    'field' => 'currency',
                    'function_type' => FieldConstant::FUNCTION_TYPE_QUOTE,
                    'relation_field' => 'refer_id',
                    'relation_object_name' => objConstant::OBJ_ORDER,
                    'relation_object_field' => 'currency',
                    'relation_object_function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                    'relation_object_relation_field' => 'order_id',
                ]

            ],
            [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field' => 'quotation_product_id',
                'field_name' => '报价单产品',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'quotation_product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => []
            ],
            [
                'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                'field' => 'inquiry_product_id',
                'field_name' => '询价任务产品',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'inquiry_product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => []
            ],
        ],
    ],
    \Constants::TYPE_PURCHASE_ORDER => [
        'field' => [
            'wait_inbound_count' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [],
            ],
            'update_time' => [
                'columns' => [
                    'data_key' => 'update_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'unit_price' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'unit_price',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'unit' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'unit',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'transfer_invoice_serial_id' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [],
            ],
            'transfer_invoice_record_id' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [],
            ],
            'transfer_invoice_id' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                //todo 采购订单产品无存储该字段，类型待确认 该字段暂保留历史字段类型，定义跟单协同相关对象时，处理该字段类型
//                'field_type' => FieldConstant::TYPE_OBJECT
                'columns' => [],
            ],
            'to_inbound_count' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [],
            ],
            'task_inbound_count' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [],
            ],
            'supplier_product_no' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [],
            ],
            'supplier_product_name' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [],
            ],
            'supplier_id' => [
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_SUPPLIER,
                ],
                'columns' => [
                    'data_key' => 'supplier_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'supplier_contact' => [
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_SUPPLIER_CONTACT,
                ],
                'columns' => [
                    'data_key' => 'supplier_contact',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'status' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'status',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'sku_id' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_PRODUCT_SKU,
                ],
                'columns' => [
                    'data_key' => 'sku_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'remark' => [
                'columns' => [
                    'data_key' => 'remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'refer_order_id' => [
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_ORDER,
                ],
                'columns' => [
                    'data_key' => 'refer_order_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'purchase_order_product_id' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'columns' => [
                    'data_key' => 'purchase_order_product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'purchase_order_no' => [
                'columns' => [
                    'data_key' => 'purchase_order_no',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'is_writable' => FieldConstant::WRITABLE_FLAG,
            ],
            'purchase_date' => [
                'columns' => [
                    'data_key' => 'purchase_date',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'product_total_count' => [
                'columns' => [
                    'data_key' => 'product_total_count',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'product_total_amount_usd' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'product_total_amount_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'product_total_amount_rmb' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'product_total_amount_rmb',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'product_total_amount' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'product_total_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'product_remark' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'product_remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'product_no' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [],
            ],
            'product_id' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_PRODUCT,
                ],
                'columns' => [
                    'data_key' => 'product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'product_disable_flag' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [],
            ],
            'percent_of_total_amount' => [
                'field_type' => FieldConstant::TYPE_PERCENTAGE,
                'columns' => [],
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
            ],
            'payment_wait_amount_rmb' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [],
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
            ],
            'payment_wait_amount_usd' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [],
            ],
            'payment_wait_amount' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [],
            ],
            'payment_status' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'payment_status',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
            ],
            'payment_amount_usd' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'payment_amount_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'payment_amount_rmb' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'payment_amount_rmb',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'payment_amount' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'payment_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
            ],
            'order_no' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [],
            ],
            'order_id' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_ORDER,
                ],
                'columns' => [
                    'data_key' => 'order_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'modifier' => [
                'field_type' => FieldConstant::TYPE_USER,
                'columns' => [
                    'data_key' => 'modifier',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'is_sub_product' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_FLAG,
                'columns' => [],
            ],
            'invoice_product_id' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                ],
                'columns' => [
                    'data_key' => 'invoice_product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'inbound_status' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'inbound_status',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
            ],
            'have_return_count' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [],
            ],
            'have_inbound_count' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [],
            ],
            'handler' => [
                'field_type' => FieldConstant::TYPE_USER,
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'handler',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            'exchange_rate_usd' => [
                'field_type' => FieldConstant::TYPE_EXCHANGE_RATE,
                'columns' => [
                    'data_key' => 'exchange_rate_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'exchange_rate' => [
                'field_type' => FieldConstant::TYPE_EXCHANGE_RATE,
                'columns' => [
                    'data_key' => 'exchange_rate',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'enable_count' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [],
            ],
            'delivery_date' => [
                'columns' => [
                    'data_key' => 'delivery_date',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'currency' => [
                'columns' => [
                    'data_key' => 'currency',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'creator' => [
                'field_type' => FieldConstant::TYPE_USER,
                'columns' => [
                    'data_key' => 'creator',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'create_type' => [
                'columns' => [
                    'data_key' => 'create_type',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'create_time' => [
                'columns' => [
                    'data_key' => 'create_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'count' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'count',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'cost_remark' => [
                'columns' => [],
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
            ],
            'cost_name' => [
                'columns' => [],
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
            ],
            'cost_item_relation_id' => [
                'columns' => [],
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
            ],
            'cost_amount' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'cost_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'cost' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [],
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
            ],
            'combine_product_no' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [],
            ],
            'combine_product_id' => [                       //定义待确定
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [],
            ],
            'check_inbound_count' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [],
            ],
            'amount_usd' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'amount_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'is_writable' => FieldConstant::READONLY_FLAG,
            ],
            'amount_rmb' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'amount_rmb',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'is_writable' => FieldConstant::READONLY_FLAG,
            ],
            'amount' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'addition_cost_amount' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'addition_cost_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'purchase_type' => [
                'field_type' => FieldConstant::TYPE_FLAG,
                'columns' => [
                    'data_key' => 'purchase_type',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'parts_total_count' => [
                'columns' => [
                    'data_key' => 'parts_total_count',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'product_total_count_no_parts' => [
                'columns' => [
                    'data_key' => 'product_total_count_no_parts',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'is_master_product' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_FLAG,
                'columns' => [
                    'data_key' => 'is_master_product',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'master_id' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'master_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'master_group_id' => [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [],
            ],
            'product_image' => [
                'field_type' => FieldConstant::TYPE_IMAGE,
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'product_image',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'product_name' => [
                'field_type' => FieldConstant::TYPE_TEXT,
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'product_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'product_cn_name' => [
                'field_type' => FieldConstant::TYPE_TEXT,
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'product_cn_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'product_model' => [
                'field_type' => FieldConstant::TYPE_TEXT,
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'columns' => [
                    'data_key' => 'product_model',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
        ],
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER,
                'field' => 'purchase_order_id',
                'field_name' => '采购订单',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'purchase_order_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => [],
            ],
            [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'from' => ['handler']
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ],
    ],
    \Constants::TYPE_PURCHASE_ORDER_PRODUCT => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                'field' => 'purchase_order_id',
                'field_name' => '采购订单',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_MASTER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_PURCHASE_ORDER,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'purchase_order_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => [


                ]

            ]
        ]
    ],
    \Constants::TYPE_PRODUCT_TRANSFER_PURCHASE => [
        'field' => [
            // 主对象
            'finish_time' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'columns' => [
                    'data_key' => 'finish_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'expect_time' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE,
                'field_type' => FieldConstant::TYPE_DATE,
                'columns' => [
                    'data_key' => 'expect_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'handler' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE,
                'field_type' => FieldConstant::TYPE_USER,
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'handler',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            'attachment' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE,
                'field_type' => FieldConstant::TYPE_FILE,
                'columns' => [
                    'data_key'     => 'attachment',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'create_time' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'columns' => [
                    'data_key'     => 'create_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'update_time' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'columns' => [
                    'data_key'     => 'update_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'create_user' => [
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'columns' => [
                    'data_key' => 'create_user',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'update_user' => [
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'columns' => [
                    'data_key' => 'update_user',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'status' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'status',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'purchase_type' => [
                'field_type' => FieldConstant::TYPE_FLAG,
                'columns' => [
                    'data_key' => 'purchase_type',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'product_total_count_no_parts' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'product_total_count_no_parts',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'parts_total_count' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'parts_total_count',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'notice_config' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE,
                'field_type' => FieldConstant::TYPE_DATA_SET,
                'columns' => [
                    'data_key'     => 'notice_config',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'serial_id' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'serial_id',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'name' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'remark' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE,
                'field_type' => FieldConstant::TYPE_TEXTAREA,
                'columns' => [
                    'data_key' => 'remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'generate_schedule_flag' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE,
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [],
                'ext_info' => [
                    'item_type' => FieldConstant::VALUE_ITEM_TYPE,
                    'value_type' => FieldConstant::SELECT_VALUE_TYPE_VALUE_EQUAL_LABEL,
                    'value' => [
                        '创建', '不创建'
                    ]
                ],
            ],
            'last_comment' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE,
                'field_type' => FieldConstant::TYPE_RICH_TEXT,
                'columns' => [],
            ],
            'order_id' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [],
            ],
            'task_progress' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [],
            ],
            // 从对象
            'check_purchase_count' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [],
            ],
            'have_purchase_count' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [],
            ],
            'inbound_count' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [],
            ],
            'master_group_id' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [],
            ],
            'order_record_id' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_ORDER_PRODUCT,
                ],
                'columns' => [
                    'data_key' => 'sub_refer_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'product_cn_name' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [],
            ],
            'product_image' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'field_type' => FieldConstant::TYPE_IMAGE,
                'columns' => [],
            ],
            'product_model' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [],
            ],
            'product_name' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [],
            ],
            'product_unit' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [],
            ],
            'purchase_count' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [],
            ],
            'sale_count' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [],
            ],
            'sku_attributes' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [],
            ],
            'sku_code' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [],
            ],
            'task_purchase_count' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [],
            ],
            'to_purchase_count' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [],
            ],
            'sku_id' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_PRODUCT_SKU,
                ],
                'columns' => [
                    'data_key' => 'sku_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'product_id' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_PRODUCT,
                ],
                'columns' => [
                    'data_key' => 'product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'master_id' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'columns' => [
                    'data_key' => 'master_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ]
            ],
            'is_master_product' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'field_type' => FieldConstant::TYPE_FLAG,
                'columns' => [
                    'data_key' => 'is_master_product',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'record_remark' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'field_type' => FieldConstant::TYPE_TEXTAREA,
                'columns' => [
                    'data_key' => 'remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'description' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'field_type' => FieldConstant::TYPE_TEXTAREA,
                'columns' => [
                    'data_key' => 'description',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'reach_count' => [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'reach_count',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
        ],
    ],
    \Constants::TYPE_PRODUCT_TRANSFER_PURCHASE_PRODUCT => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
                'field' => 'refer_id',
                'field_name' => '订单',
                'group_id' => CustomFieldService::PRODUCT_TRANSFER_PURCHASE_GROUP_PRODUCT,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_ORDER,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'refer_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => [],
            ],
        ],
    ],
    \Constants::TYPE_SUPPLIER => [
        'field' => [
            'supplier_id' => [
                'object_name' => objConstant::OBJ_SUPPLIER,
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'columns' => [
                    'data_key' => 'supplier_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'supplier_no' => [
                'columns' => [
                    'data_key' => 'supplier_no',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'name' => [
                'object_name' => objConstant::OBJ_SUPPLIER,
                'columns' => [
                    'data_key' => 'name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'rate_id' => [
                'object_name' => objConstant::OBJ_SUPPLIER,
                'columns' => [
                    'data_key' => 'rate_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'homepage' => [
                'object_name' => objConstant::OBJ_SUPPLIER,
                'columns' => [
                    'data_key' => 'homepage',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'address' => [
                'object_name' => objConstant::OBJ_SUPPLIER,
                'columns' => [
                    'data_key' => 'address',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'delivery_date' => [
                'object_name' => objConstant::OBJ_SUPPLIER,
                'columns' => [
                    'data_key' => 'custom_delivery_date',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'remark' => [
                'object_name' => objConstant::OBJ_SUPPLIER,
                'columns' => [
                    'data_key' => 'remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'archive_user' => [
                'object_name' => objConstant::OBJ_SUPPLIER,
                'field_type' => FieldConstant::TYPE_USER,
                'columns' => [
                    'data_key' => 'archive_user',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'user_ids' => [
                'object_name' => objConstant::OBJ_SUPPLIER,
                'field_type' => FieldConstant::TYPE_USER,
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            'update_user' => [
                'object_name' => objConstant::OBJ_SUPPLIER,
                'field_type' => FieldConstant::TYPE_USER,
                'columns' => [
                    'data_key' => 'update_user',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'create_time' => [
                'object_name' => objConstant::OBJ_SUPPLIER,
                'columns' => [
                    'data_key' => 'create_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'update_time' => [
                'object_name' => objConstant::OBJ_SUPPLIER,
                'columns' => [
                    'data_key' => 'update_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'attachments' => [
                'object_name' => objConstant::OBJ_SUPPLIER,
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'attachments',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'supplier_contact_id' => [
                'object_name' => objConstant::OBJ_SUPPLIER_CONTACT,
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'columns' => [
                    'data_key' => 'supplier_contact_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'contact_name' => [
                'object_name' => objConstant::OBJ_SUPPLIER_CONTACT,
                'columns' => [
                    'data_key' => 'contact_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'phone' => [
                'object_name' => objConstant::OBJ_SUPPLIER_CONTACT,
                'columns' => [
                    'data_key' => 'phone',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'email' => [
                'object_name' => objConstant::OBJ_SUPPLIER_CONTACT,
                'columns' => [
                    'data_key' => 'email',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'social_platform' => [
                'object_name' => objConstant::OBJ_SUPPLIER_CONTACT,
                'columns' => [
                    'data_key' => 'social_platform',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'main' => [
                'object_name' => objConstant::OBJ_SUPPLIER_CONTACT,
                'columns' => [
                    'data_key' => 'main',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'partner_account_id' => [
                'object_name' => objConstant::OBJ_SUPPLIER_ACCOUNT,
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'columns' => [
                    'data_key' => 'partner_account_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'partner_account_name' => [
                'object_name' => objConstant::OBJ_SUPPLIER_ACCOUNT,
                'columns' => [
                    'data_key' => 'partner_account_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'bank_account' => [
                'object_name' => objConstant::OBJ_SUPPLIER_ACCOUNT,
                'columns' => [
                    'data_key' => 'bank_account',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'account_name' => [
                'object_name' => objConstant::OBJ_SUPPLIER_ACCOUNT,
                'columns' => [
                    'data_key' => 'account_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'bank_name' => [
                'object_name' => objConstant::OBJ_SUPPLIER_ACCOUNT,
                'columns' => [
                    'data_key' => 'bank_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'account_image' => [
                'object_name' => objConstant::OBJ_SUPPLIER_ACCOUNT,
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'account_image',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'is_main_flag' => [
                'object_name' => objConstant::OBJ_SUPPLIER_ACCOUNT,
                'columns' => [
                    'data_key' => 'is_main_flag',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
        ],
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_SUPPLIER,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'from' => ['user_ids']
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ],
    ],
    \Constants::TYPE_SUPPLIER_CONTACT => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_SUPPLIER_CONTACT,
                'field' => 'supplier_id',
                'field_name' => '供应商',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_MASTER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_SUPPLIER,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'supplier_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => [


                ]

            ]
        ]
    ],
    \Constants::TYPE_SUPPLIER_ACCOUNT => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_SUPPLIER_ACCOUNT,
                'field' => 'refer_id',
                'field_name' => '供应商',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_MASTER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_SUPPLIER,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'refer_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                    'bind_field' => 'refer_type',
                ],
                'field_relations' => [


                ]

            ]
        ]
    ],
    \Constants::TYPE_QUOTATION => [
        'field' => [
            'name' => [
                'columns' => [
                    'data_key' => 'name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'remark' => [
                'columns' => [
                    'data_key' => 'remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'status' => [
                'columns' => [
                    'data_key' => 'status',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'currency' => [
                'columns' => [
                    'data_key' => 'currency',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'amount' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'marked' => [
                'columns' => [
                    'data_key' => 'marked',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ]
            ],
            'amount_rmb' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'amount_rmb',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'amount_usd' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'amount_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'product_total_amount' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'product_total_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'is_writable' => FieldConstant::READONLY_FLAG,
            ],
            'product_total_amount_rmb' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'product_total_amount_rmb',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'is_writable' => FieldConstant::READONLY_FLAG,
            ],
            'product_total_amount_usd' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'product_total_amount_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'is_writable' => FieldConstant::READONLY_FLAG,
            ],
            'company_id' => [
                'field_type' => FieldConstant::TYPE_OBJECT,
                'columns' => [
                    'data_key' => 'company_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'ext_info' => [
                    'object_name' => objConstant::OBJ_COMPANY
                ]
            ],
            'create_time' => [
                'field_type' => FieldConstant::TYPE_DATETIME,
                'columns' => [
                    'data_key' => 'create_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'create_user' => [
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'columns' => [
                    'data_key' => 'create_user',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'update_user' => [
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'columns' => [
                    'data_key' => 'update_user',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'customer_id' => [
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_CUSTOMER,
                ],
                'columns' => [
                    'data_key' => 'customer_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'update_time' => [
                'field_type' => FieldConstant::TYPE_DATETIME,
                'columns' => [
                    'data_key' => 'update_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'quotation_no' => [
                'columns' => [
                    'data_key' => 'quotation_no',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'exchange_rate' => [
                'field_type' => FieldConstant::TYPE_EXCHANGE_RATE,
                'columns' => [
                    'data_key' => 'exchange_rate',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'opportunity_id' => [
                'field_type' => FieldConstant::TYPE_OBJECT,
                'columns' => [
                    'data_key' => 'opportunity_id',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
                'ext_info' => [
                    'object_name' => objConstant::OBJ_OPPORTUNITY
                ]
            ],
            'quotation_date' => [
                'field_type' => FieldConstant::TYPE_DATE,
                'columns' => [
                    'data_key' => 'quotation_date',
                    'data_db_type' => FieldConstant::DB_TYPE_DATE,
                ],
            ],
            'cost' => [
                'columns' => [

                ],
            ],
            'cost_name' => [
                'columns' => [
                ],
            ],
            'cost_remark' => [
                'columns' => [
                ],
            ],
            'percent_of_total_amount' => [
                'columns' => [
                ],
            ],
            'cost_item_relation_id' => [
                'columns' => [
                ],
            ],
            'price_contract' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'is_array_flag' => FieldConstant::IS_NOT_ARRAY,
                'columns' => [
                    'data_key' => 'price_contract',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'insurance_remark' => [
                'columns' => [
                    'data_key' => 'insurance_remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'refer_inquiry_collaboration_no' => [
                'object_name' => objConstant::OBJ_QUOTATION,
                'columns' => [],
            ],
            'addition_cost_amount' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'addition_cost_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'is_writable' => FieldConstant::READONLY_FLAG,
            ],
            'addition_cost_amount_rmb' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'addition_cost_amount_rmb',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'is_writable' => FieldConstant::READONLY_FLAG,
            ],
            'addition_cost_amount_usd' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'addition_cost_amount_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'is_writable' => FieldConstant::READONLY_FLAG,
            ],
            'has_transfer_order' => [
                'columns' => [
                    'data_key' => 'has_transfer_order',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'package_remark' => [
                'columns' => [
                    'data_key' => 'package_remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ]
            ],
            'receive_remittance_remark' => [
                'columns' => [
                    'data_key' => 'receive_remittance_remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'price_contract_remark' => [
                'columns' => [
                    'data_key' => 'price_contract_remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'receive_remittance_way' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'is_array_flag' => FieldConstant::IS_NOT_ARRAY,
                'columns' => [
                    'data_key' => 'receive_remittance_way',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'company_fax' => [
                'columns' => [
                    'data_key' => 'company_fax',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'company_short_name' => [
                'columns' => [
                    'data_key' => 'company_short_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'company_name' => [
                'columns' => [
                    'data_key' => 'company_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'company_phone' => [
                'columns' => [
                    'data_key' => 'company_phone',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'customer_name' => [
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'customer_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'customer_email' => [
                'columns' => [
                    'data_key' => 'customer_email',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'customer_phone' => [
                'columns' => [
                    'data_key' => 'customer_phone',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'company_address' => [
                'columns' => [
                    'data_key' => 'company_address',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'customer_address' => [
                'columns' => [
                    'data_key' => 'customer_address',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'target_port' => [
                'columns' => [
                    'data_key' => 'target_port',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'shipment_port' => [
                'columns' => [
                    'data_key' => 'shipment_port',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'transport_mode' => [
                'columns' => [
                    'data_key' => 'transport_mode',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'shipment_deadline' => [
                'field_type' => FieldConstant::TYPE_DATE,
                'columns' => [
                    'data_key' => 'shipment_deadline',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'shipment_deadline_remark' => [
                'columns' => [
                    'data_key' => 'shipment_deadline_remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'country' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'country',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'handler' => [
                'field_type' => FieldConstant::TYPE_USER,
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'handler',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            'exchange_rate_usd' => [
                'field_type' => FieldConstant::TYPE_EXCHANGE_RATE,
                'columns' => [
                    'data_key' => 'exchange_rate_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'parts_total_count' => [
                'columns' => [
                    'data_key' => 'parts_total_count',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'product_total_count' => [
                'columns' => [
                    'data_key' => 'product_total_count',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'product_total_count_no_parts' => [
                'columns' => [
                    'data_key' => 'product_total_count_no_parts',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'package_gross_weight_amount' => [
                'columns' => [
                    'data_key' => 'package_gross_weight_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'package_volume_amount' => [
                'columns' => [
                    'data_key' => 'package_volume_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],

            'unit' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'unit',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ]
            ],
            'count' => [
                'field_type' => FieldConstant::TYPE_NUMBER,
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'count',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'unit_price' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'unit_price',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'cost_amount' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'cost_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'ratio' => [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'ratio',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'sku_id' => [
                'field_type' => FieldConstant::TYPE_OBJECT,
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'sku_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'ext_info' => [
                    'object_name' => objConstant::OBJ_PRODUCT_SKU
                ]
            ],
            'quotation_id' => [
                'field_type' => FieldConstant::TYPE_MASTER,
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'quotation_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'ext_info' => [
                    'object_name' => objConstant::OBJ_QUOTATION
                ]
            ],
            'quotation_product_id' => [
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'quotation_product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'ext_info' => [
                    'object_name' => objConstant::OBJ_QUOTATION_PRODUCT
                ]
            ],
            'master_id' => [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'master_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ]
            ],
            'package_remark' => [         //报价单明细表该字段为package_remark
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'package_remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'unique_id' => [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    //已存在quotation_product_id
                ]
            ],
            'offer_data' => [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'offer_data',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ]
            ],
            'other_cost' => [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'other_cost',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'product_id' => [
                'field_type' => FieldConstant::TYPE_OBJECT,
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_PRODUCT,
                ],
                'columns' => [
                    'data_key' => 'product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ]
            ],
            'product_no' => [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                ]
            ],
            'description' => [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'description',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ]
            ],
            'package_unit' => [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'package_unit',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ]
            ],
            'product_name' => [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'product_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ]
            ],
            'product_type' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'product_type',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ]
            ],
            'cost_with_tax' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'cost_with_tax',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'package_count' => [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'package_count',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'product_image' => [
                'field_type' => FieldConstant::TYPE_IMAGE,
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                ]
            ],
            'product_model' => [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'product_model',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ]
            ],
            'package_volume' => [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'package_volume',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'minimum_order_quantity' => [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'minimum_order_quantity',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'product_remark' => [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'product_remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ]
            ],
            'master_group_id' => [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [

                ]
            ],
            'product_cn_name' => [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'product_cn_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ]
            ],
            'count_per_package' => [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'count_per_package',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'is_master_product' => [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'is_master_product',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ]
            ],
            'gross_profit_margin' => [
                'field_type' => FieldConstant::TYPE_PERCENTAGE,
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'gross_profit_margin',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'package_gross_weight' => [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'package_gross_weight',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'product_disable_flag' => [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                ]
            ],
            'package_volume_subtotal' => [
                'field_type' => FieldConstant::TYPE_NUMBER,
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'package_volume_subtotal',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'is_writable' => FieldConstant::READONLY_FLAG,
            ],
            'package_gross_weight_subtotal' => [
                'field_type' => FieldConstant::TYPE_NUMBER,
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'package_gross_weight_subtotal',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'is_writable' => FieldConstant::READONLY_FLAG,
            ],
            'hs_code' => [
                'field_type' => FieldConstant::TYPE_TEXT,
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'hs_code',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ]
            ],
            'vat_rate' => [
                'field_type' => FieldConstant::TYPE_PERCENTAGE,
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'vat_rate',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'tax_refund_rate' => [
                'field_type' => FieldConstant::TYPE_PERCENTAGE,
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'tax_refund_rate',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'standard_price' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'columns' => [
                    'data_key' => 'standard_price',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ]
            ],
            'create_type' => [         //自动创建
                'columns' => [
                    'data_key' => 'create_type',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'approval_status' => [         //自动创建
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'approval_status',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'purchase_quote_cny' => [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'purchase_quote_cny',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'purchase_quote_usd' => [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'purchase_quote_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'purchase_quote' => [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'purchase_quote',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
        ],
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_QUOTATION,
                'field' => 'quotation_id',
                'field_name' => '报价单ID',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_QUOTATION,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'quotation_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => [
                ]
            ],
            [
                'object_name' => objConstant::OBJ_QUOTATION,
                'field' => 'inquiry_collaboration_id',
                'field_name' => '询价协同',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'inquiry_collaboration_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => [
                ]
            ],
            [
                'object_name' => objConstant::OBJ_QUOTATION,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'from' => ['user_id']
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ]
    ],
    \Constants::TYPE_QUOTATION_PRODUCT => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'field' => 'quotation_id',
                'field_name' => '报价单ID',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_MASTER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_QUOTATION,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'quotation_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => [
                ]
            ],
            [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'field' => 'quotation_product_id',
                'field_name' => '报价单产品ID',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_QUOTATION,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'quotation_product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => [
                ]
            ],
            [
                'object_name' => objConstant::OBJ_QUOTATION_PRODUCT,
                'field' => 'inquiry_product_id',
                'field_name' => '询价协同产品',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'inquiry_product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => [
                ]
            ],
        ]
    ],
    \Constants::TYPE_INQUIRY_COLLABORATION => [

        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION,
                'field' => 'inquiry_collaboration_id',
                'field_name' => '询价协同ID',       //
                'group_id' => 1,
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'inquiry_collaboration_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => [

                ]

            ],
            [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'from' => ['create_user','handler']
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ],
        "field"=>[
            'inquiry_collaboration_no' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'inquiry_collaboration_no',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'inquiry_collaboration_name' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'inquiry_collaboration_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'status' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'status',
                    'data_db_type' => FieldConstant::DB_TYPE_INTEGER,
                ],
                'is_writable' => FieldConstant::WRITABLE_FLAG
            ],

            'handler' => [
                'field_type' => FieldConstant::TYPE_USER,
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'handler',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],

            'inquiry_deadline' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION,
                'field_type' => FieldConstant::TYPE_DATE,
                'columns' => [
                    'data_key' => 'inquiry_deadline',
                    'data_db_type' => FieldConstant::DB_TYPE_DATE,
                ],
            ],

            'remark' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION,
                'field_type' => FieldConstant::TYPE_TEXTAREA,
                'columns' => [
                    'data_key' => 'remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],

            'finish_time' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION,
                'field_type' => FieldConstant::DB_TYPE_DATE,
                'columns' => [
                    'data_key' => 'finish_time',
                    'data_db_type' => FieldConstant::DB_TYPE_DATE,
                ],
            ],
            'progress' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'progress',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'company_id' => [         //客户
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_COMPANY,
                ],
                'columns' => [
                    'data_key' => 'company_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],

            'currency' => [
                'columns' => [
                    'data_key' => 'currency',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'customer_id' => [
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_CUSTOMER,
                ],
                'columns' => [
                    'data_key' => 'customer_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'lead_id' => [
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_LEAD,
                ],
                'columns' => [
                    'data_key' => 'lead_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],

            'opportunity_id' => [
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_OPPORTUNITY,
                ],
                'columns' => [
                    'data_key' => 'opportunity_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ]
            ],
            'country' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION,
                'group_id' => 1,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'country',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'customer_name' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION,
                'group_id' => 1,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'customer_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'customer_email' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION,
                'group_id' => 1,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'customer_email',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'customer_phone' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION,
                'group_id' => 1,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'customer_phone',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],

            'create_user' => [
                'field_type' => FieldConstant::TYPE_USER,
                'columns' => [
                    'data_key' => 'create_user',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'update_user' => [
                'field_type' => FieldConstant::TYPE_USER,
                'columns' => [
                    'data_key' => 'update_user',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'create_schedule' => [
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'create_schedule',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'exchange_rate' => [         //创建人
                'field_type' => FieldConstant::TYPE_EXCHANGE_RATE,
                'columns' => [
                    'data_key' => 'exchange_rate',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],

            'exchange_rate_usd' => [
                'field_type' => FieldConstant::TYPE_EXCHANGE_RATE,
                'columns' => [
                    'data_key' => 'exchange_rate_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'shipment_port' => [
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'shipment_port',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'target_port' => [
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'target_port',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'transport_mode' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'transport_mode',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'price_contract' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'price_contract',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'social_media' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'social_media',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'attachments' => [
                'field_type' => FieldConstant::TYPE_FILE,
                'columns' => [
                    'data_key' => 'attachments',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'source' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'source',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            'product_total_amount' => [
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'product_total_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'product_total_count' => [
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'product_total_count',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'create_time' => [
                'field_type' => FieldConstant::TYPE_DATETIME,
                'columns' => [
                    'data_key' => 'create_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],

            'update_time' => [
                'field_type' => FieldConstant::TYPE_DATETIME,
                'columns' => [
                    'data_key' => 'update_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'approval_status' => [         //自动创建
               'field_type' => FieldConstant::TYPE_FLAG,
               'columns'    => [],
            ],
            'product_no' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'group_id' => 1,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'product_no',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],

            'inquiry_collaboration_id' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION,
                'group_id' => 1,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'columns' => [
                    'data_key' => 'inquiry_collaboration_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'ext_info' => [
                    'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION,
                ],
            ],
            'product_model' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'group_id' => 1,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'product_model',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'sku_id' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_PRODUCT_SKU,
                ],
                'columns' => [
                    'data_key' => 'sku_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'product_id' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_PRODUCT,
                ],
                'columns' => [
                    'data_key' => 'product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'supplier_id' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_SUPPLIER,
                ],
                'columns' => [
                    'data_key' => 'supplier_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],

            'product_name' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'product_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'product_cn_name' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'product_cn_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'product_images' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'product_images',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'unit' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'is_array_flag' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'unit',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'description' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_TEXTAREA,
                'columns' => [
                    'data_key' => 'description',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'product_remark' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_TEXTAREA,
                'columns' => [
                    'data_key' => 'product_remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'usage' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_TEXTAREA,
                'columns' => [
                    'data_key' => 'usage',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'delivery_date' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_DATE,
                'columns' => [
                    'data_key' => 'delivery_date',
                    'data_db_type' => FieldConstant::DB_TYPE_DATE,
                ],
            ],
            'target_price' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'target_price',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'certificate_requirements' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                "field_type"=> FieldConstant::TYPE_TEXTAREA,
                'columns' => [
                    'data_key' => 'certificate_requirements',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'inquiry_product_id' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'columns' => [
                    'data_key' => 'inquiry_product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'ext_info' => [
                    'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT
                ]
            ],
            'tax_included' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'tax_included',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'purchase_quote_cny' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'purchase_quote_cny',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'purchase_quote_usd' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'purchase_quote_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'purchase_quote' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'purchase_quote',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'vat_rate' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'vat_rate',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ]
            ,'tax_refund_rate' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'tax_refund_rate',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'amount' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],

            ]
            ,'count' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'count',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'local_product' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'local_product',
                    'data_db_type' => FieldConstant::DB_TYPE_INTEGER,
                ],
            ],
            'package_remark' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_TEXTAREA,
                'columns' => [
                    'data_key' => 'package_remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'price_validity_date' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_DATE,
                'columns' => [
                    'data_key' => 'price_validity_date',
                    'data_db_type' => FieldConstant::DB_TYPE_DATE,
                ],
            ],
            'market_information' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_TEXTAREA,
                'columns' => [
                    'data_key' => 'market_information',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'sku_attributes' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_TEXTAREA,
                'columns' => [
                    'data_key' => 'sku_attributes',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'hs_code' => [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'hs_code',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],

        ]
    ],
    \Constants::TYPE_INQUIRY_COLLABORATION_PRODUCT => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field' => 'inquiry_collaboration_id',
                'field_name' => '询价协同',       //
                'group_id' => 2,
                'field_type' => FieldConstant::TYPE_MASTER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION,
                ],
                //{"object_name":"objInquiryCollaboration"}
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'inquiry_collaboration_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => [

                ]

            ],
            [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field' => 'inquiry_product_id',
                'field_name' => '询价产品ID',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'inquiry_product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => [
                ]
            ],
        ],
    ],

    \Constants::TYPE_PURCHASE_INBOUND_INVOICE => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_PURCHASE_INBOUND_INVOICE,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'from' => ['handler']
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ]
    ],
    \Constants::TYPE_OTHER_INBOUND_INVOICE => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_OTHER_INBOUND_INVOICE,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'from' => ['handler']
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ]
    ],
    \Constants::TYPE_ALIBABA_ORDER_PRODUCT => [
        'field' => [
            'ali_product_images' => [
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'ali_product_images',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'ali_product_image_path' => [
                'is_array_flag' => FieldConstant::IS_NOT_ARRAY,
                'columns' => [
                    'data_key' => 'ali_product_image_path',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'ali_product_name' => [         //中文产品名称
                'columns' => [
                    'data_key' => 'ali_product_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'ali_product_id' => [
                'object_name' => objConstant::OBJ_ALIBABA_ORDER_PRODUCT,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'ali_product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'ali_product_sku_attributes' => [         //产品规格
                'field_type' => FieldConstant::TYPE_DATA_SET,
                'columns' => [
                    'data_key' => 'ali_product_sku_attributes',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'ali_product_model' => [
                'columns' => [
                    'data_key' => 'ali_product_model',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'product_images' => [
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'product_images',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],

            'product_name' => [         //产品名称
                'columns' => [
                    'data_key' => 'product_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'product_cn_name' => [         //中文产品名称
                'columns' => [
                    'data_key' => 'product_cn_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'product_no' => [
                'object_name' => objConstant::OBJ_ALIBABA_ORDER_PRODUCT,
                'columns' => [
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,       //下游采购订单可能关联了该字段，此处临时方案处理，定义db的类型作为下游关联字段的类型配置
                ],
            ],
            'product_model' => [
                'columns' => [
                    'data_key' => 'product_model',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'sku_attributes' => [         //产品规格
                'field_type' => FieldConstant::TYPE_DATA_SET,
                'columns' => [
                    'data_key' => 'sku_attributes',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'count' => [
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'count',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'unit' => [
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'unit',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'unit_price' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'unit_price',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'amount' => [
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'amount_rmb' => [
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'amount_rmb',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'amount_usd' => [
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'amount_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'remark' => [         //备注
                'columns' => [
                    'data_key' => 'remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
                'field_name' => '备注'
            ],
            'create_time' => [
                'field_type' => FieldConstant::TYPE_DATETIME,
                'columns' => [
                    'data_key' => 'create_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'update_time' => [
                'field_type' => FieldConstant::TYPE_DATETIME,
                'columns' => [
                    'data_key' => 'update_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],

        ],
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_ALIBABA_ORDER_PRODUCT,
                'field' => 'ali_order_product_id',
                'field_name' => '阿里订单产品',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'ali_order_product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => [
                ]

            ]
        ],
    ],
    \Constants::TYPE_INQUIRY_FEEDBACK => [
        'field' => [
            'inquiry_feedback_id' => [
                'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                'group_id' => 1,
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'columns' => [
                    'data_key' => 'inquiry_feedback_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'ext_info' => [
                    'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                ],
            ],
            'inquiry_collaboration_id' => [
                'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                'group_id' => 1,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'columns' => [
                    'data_key' => 'inquiry_collaboration_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'ext_info' => [
                    'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION,
                ],
            ],
            'inquiry_product_id' => [
                'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'columns' => [
                    'data_key' => 'inquiry_product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'ext_info' => [
                    'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT
                ]
            ],
            'product_id' => [
                'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_PRODUCT,
                ],
                'columns' => [
                    'data_key' => 'product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'sku_id' => [
                'field_type' => FieldConstant::TYPE_OBJECT,
                'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                'columns' => [
                    'data_key' => 'sku_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'ext_info' => [
                    'object_name' => objConstant::OBJ_PRODUCT_SKU
                ]
            ],
            'tax_included' => [
                'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'tax_included',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'purchase_quote_cny' => [
                'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'purchase_quote_cny',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'purchase_quote_usd' => [
                'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'purchase_quote_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'purchase_quote' => [
                'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'purchase_quote',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'supplier_id' => [
                'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_SUPPLIER,
                ],
                'columns' => [
                    'data_key' => 'supplier_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'price_validity_date' => [
                'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                'field_type' => FieldConstant::TYPE_DATE,
                'columns' => [
                    'data_key' => 'price_validity_date',
                    'data_db_type' => FieldConstant::DB_TYPE_DATE,
                ],
            ],
            'create_user' => [
                'field_type' => FieldConstant::TYPE_USER,
                'columns' => [
                    'data_key' => 'create_user',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'is_writable' => FieldConstant::READONLY_FLAG
            ],
            'update_user' => [
                'field_type' => FieldConstant::TYPE_USER,
                'columns' => [
                    'data_key' => 'update_user',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'is_writable' => FieldConstant::READONLY_FLAG
            ],
            'adoption_time' => [
                'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'columns' => [
                    'data_key' => 'adoption_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
                'is_writable' => FieldConstant::READONLY_FLAG
            ],
            'adoption_status' => [
                'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                'field_type' => FieldConstant::TYPE_FLAG,
                'columns' => [
                    'data_key' => 'adoption_status',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'adoption_user' => [
                'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                'field_type' => FieldConstant::TYPE_USER,
                'columns' => [
                    'data_key' => 'adoption_user',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'is_writable' => FieldConstant::READONLY_FLAG
            ],
            'market_information' => [
                'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                'field_type' => FieldConstant::TYPE_TEXTAREA,
                'columns' => [
                    'data_key' => 'market_information',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'product_images' => [
                'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
//                    'data_key' => 'product_images',
//                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
                'is_writable' => FieldConstant::READONLY_FLAG
            ],
            'product_name' => [
                'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                'is_writable' => FieldConstant::READONLY_FLAG
            ],
            'product_no' => [
                'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                'is_writable' => FieldConstant::READONLY_FLAG
            ],
            'description' => [
                'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                'is_writable' => FieldConstant::READONLY_FLAG
            ],
            'currency' => [
                'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                'is_writable' => FieldConstant::READONLY_FLAG
            ],
            'inquiry_feedback_no' => [
                'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                'is_writable' => FieldConstant::READONLY_FLAG
            ],
            'create_time' => [
                'field_type' => FieldConstant::TYPE_DATETIME,
                'columns' => [
                    'data_key' => 'create_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
                'is_writable' => FieldConstant::READONLY_FLAG
            ],
            'update_time' => [
                'field_type' => FieldConstant::TYPE_DATETIME,
                'columns' => [
                    'data_key' => 'update_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
                'is_writable' => FieldConstant::READONLY_FLAG
            ],
        ],
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                'field' => 'inquiry_product_id',
                'field_name' => '询价产品ID',       //
                'group_id' => 1,
                'field_type' => FieldConstant::TYPE_MASTER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'inquiry_product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => []
            ],
            [
                'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                'field' => 'inquiry_feedback_id',
                'field_name' => '询价反馈ID',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'inquiry_feedback_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => []
            ],
            [
                'object_name' => objConstant::OBJ_INQUIRY_FEEDBACK,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'from' => ['user_ids']
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
         ],
    ],
    \Constants::TYPE_OTHER_OUTBOUND_INVOICE => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_OTHER_OUTBOUND_INVOICE,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'from' => ['handler']
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ]
    ],
    \Constants::TYPE_SALE_OUTBOUND_INVOICE => [
        // 相关表名 tbl_outbound_invoice, tbl_outbound_record
        'field' => [
            'attachment' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_FILE,
                'columns' => [
                    'data_key' => 'attachment',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'company_id' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_OBJECT,
            ],
            'cost_unit_price' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field_type' => FieldConstant::TYPE_PRICE,
            ],
            'create_time' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'columns' => [
                    'data_key' => 'create_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'create_user' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_USER,
                'columns' => [
                    'data_key' => 'create_user',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'currency' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'currency',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'discount' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'discount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'discount_rate' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_PERCENTAGE,
                'columns' => [
                    'data_key' => 'discount_rate',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'exchange_rate' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_EXCHANGE_RATE,
                'columns' => [
                    'data_key' => 'exchange_rate',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'for_outbound_count' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field_type' => FieldConstant::TYPE_NUMBER,
            ],
            'handler' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_USER,
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'handler',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            'have_outbound_count' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field_type' => FieldConstant::TYPE_NUMBER,
            ],
            'invoice_warehouse_id' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'invoice_warehouse_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'allow_setting' => FieldConstant::ALLOW_SETTING
            ],
            'is_master_product' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field_type' => FieldConstant::TYPE_FLAG,
                'columns' => [
                    'data_key' => 'is_master_product',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'master_group_id' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
            ],
            'master_id' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'columns' => [
                    'data_key' => 'master_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'moment_enable_count' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'moment_enable_count',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'order_id' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'columns' => [
                    'data_key' => 'order_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'ext_info' => [
                    'object_name' => objConstant::OBJ_ORDER,
                ],
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING
            ],
            'order_no' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field_type' => FieldConstant::TYPE_TEXT,
            ],
            'order_record_id' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING
            ],
            'outbound_count' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'outbound_count',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'parts_total_count' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'parts_total_count',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'product_amount' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'product_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'product_cn_name' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'product_cn_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'product_disable_flag' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_FLAG,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING
            ],
            'product_id' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'columns' => [
                    'data_key' => 'product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING
            ],
            'product_image' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field_type' => FieldConstant::TYPE_IMAGE,
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'product_image',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'product_model' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'product_model',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'product_name' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'product_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'product_total_amount' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'product_total_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'product_total_count' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'product_total_count',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'product_total_count_no_parts' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'product_total_count_no_parts',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'product_unit' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'product_unit',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'ext_info' => [
                    'value' =>  \common\library\product_v2\ProductConstant::UNIT_LIST,
                    'item_type' => FieldConstant::VALUE_ITEM_TYPE,
                    'value_type' => FieldConstant:: SELECT_VALUE_TYPE_VALUE_EQUAL_LABEL,
                ],
            ],
            'refer_shipping_invoice_id' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_OBJECT,
            ],
            'refer_shipping_invoice_no' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_TEXT,
            ],
            'refer_shipping_record_id' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
            ],
            'remark' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_TEXTAREA,
                'columns' => [
                    'data_key' => 'remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'sale_count' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field_type' => FieldConstant::TYPE_NUMBER,
            ],
            'sale_price' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'sale_price',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'serial_id' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_SERIAL_ID,
                'columns' => [
                    'data_key' => 'serial_id',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'shipping_count' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field_type' => FieldConstant::TYPE_NUMBER,
            ],
            'sku_attributes' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field_type' => FieldConstant::TYPE_TEXT,
            ],
            'sku_code' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field_type' => FieldConstant::TYPE_TEXT,
            ],
            'sku_id' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'columns' => [
                    'data_key' => 'sku_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING
            ],
            'source_type' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'source_type',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
                'ext_info' => [

                ],
            ],
            'status' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'status',
                    'data_db_type' => FieldConstant::DB_TYPE_INTEGER,
                ],
            ],
            'transfer_invoice_id' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
            ],
            'transfer_invoice_record_id' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
            ],
            'transfer_invoice_serial_id' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field_type' => FieldConstant::TYPE_TEXT,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
            ],
            'update_time' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'columns' => [
                    'data_key' => 'update_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'update_user' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_USER,
                'columns' => [
                    'data_key' => 'update_user',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'warehouse_id' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'columns' => [
                    'data_key' => 'warehouse_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING
            ],
            'warehouse_invoice_time' => [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'columns' => [
                    'data_key' => 'warehouse_invoice_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
        ],
        'insert_field' => [
                 [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'from' => ['handler']
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field' => 'outbound_invoice_id',
                'field_name' => '出库单id',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'outbound_invoice_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => []
            ],
            [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE,
                'field' => 'refer_id',
                'field_name' => '关联对象id',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'refer_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => []
            ],
        ],
    ],
    \Constants::TYPE_SALE_OUTBOUND_PRODUCT => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field' => 'outbound_record_id',
                'field_name' => '出库单明细id',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'outbound_record_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => []
            ],

            [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field' => 'sub_refer_id',
                'field_name' => '关联明细id',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'sub_refer_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => []
            ],
            [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field' => 'origin_info',
                'field_name' => '数量来源占比',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_TEXT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'origin_info',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
                'field_relations' => []
            ],
            [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field' => 'shipping_invoice_id',
                'field_name' => '上游出运单id',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'shipping_invoice_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => []
            ],
            [
                'object_name' => objConstant::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
                'field' => 'invoice_product_id',
                'field_name' => '订单产品invoice_product_id',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_PURCHASE_ORDER_PRODUCT,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'invoice_product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => []
            ],
        ],
    ],
    \Constants::TYPE_SHIPPING_INVOICE => [

        'field' => [
            'shipping_status' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'shipping_status',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'outbound_status' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'outbound_status',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
            ],
            'order_ids' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'order_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            'handler' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_USER,
                'is_array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'handler',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            'company_id' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'columns' => [
                    'data_key' => 'company_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'ext_info' => [
                    'object_name' => objConstant::OBJ_COMPANY,
                ],
            ],
            'customer_id' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'columns' => [
                    'data_key' => 'customer_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'shipping_mode' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'shipping_mode',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'shipping_address' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'shipping_address',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'target_port' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'target_port',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'shipment_port' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'shipment_port',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'customs_broker' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'customs_broker',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'shipping_deadline' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_DATE,
                'columns' => [
                    'data_key' => 'shipping_deadline',
                    'data_db_type' => FieldConstant::DB_TYPE_DATE,
                ],
            ],
            'shipping_time' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_DATE,
                'columns' => [
                    'data_key' => 'shipping_time',
                    'data_db_type' => FieldConstant::DB_TYPE_DATE,
                ],
            ],
            'arrival_time' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_DATE,
                'columns' => [
                    'data_key' => 'arrival_time',
                    'data_db_type' => FieldConstant::DB_TYPE_DATE,
                ],
            ],
            'finish_time' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_DATE,
                'columns' => [
                    'data_key' => 'finish_time',
                    'data_db_type' => FieldConstant::DB_TYPE_DATE,
                ],
            ],
            'currency' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'currency',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'exchange_rate' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'exchange_rate',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'exchange_rate_usd' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'exchange_rate_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'remark' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'create_user' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_USER,
                'columns' => [
                    'data_key' => 'create_user',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'create_time' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'columns' => [
                    'data_key' => 'create_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'update_user' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_USER,
                'columns' => [
                    'data_key' => 'update_user',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'update_time' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'columns' => [
                    'data_key' => 'update_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
            ],
            'addition_cost_amount' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'addition_cost_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'addition_cost_amount_rmb' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'addition_cost_amount_rmb',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'addition_cost_amount_usd' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'addition_cost_amount_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'forwarder_id' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'columns' => [
                    'data_key' => 'forwarder_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'forwarder_contact_id' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'columns' => [
                    'data_key' => 'forwarder_contact_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'attachments' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_FILE,
                'array_flag' => FieldConstant::IS_ARRAY,
                'columns' => [
                    'data_key' => 'attachments',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
            ],
            'consignee' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'consignee',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'consignee_phone' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'consignee_phone',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'parts_total_count' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'columns' => [
                    'data_key' => 'parts_total_count',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'shipping_total_amount' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'shipping_total_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'shipping_total_amount_rmb' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'shipping_total_amount_rmb',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'shipping_total_amount_usd' => [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'shipping_total_amount_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
        ],
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field' => 'shipping_invoice_id',
                'field_name' => '运单ID',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'shipping_invoice_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => []
            ],
            [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field' => 'shipping_invoice_no',
                'field_name' => '出运编号',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_TEXT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'shipping_invoice_no',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'field_relations' => []
            ],
            [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field' => 'tracking_status',
                'field_name' => '轨迹状态',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_SELECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'tracking_status',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
                'field_relations' => []
            ],
            [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field' => 'main_order_id',
                'field_name' => '主要关联订单ID',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_ORDER,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'main_order_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => []
            ],
            [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field' => 'shipping_voucher_no',
                'field_name' => '物流单号',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_TEXT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'shipping_voucher_no',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'field_relations' => []
            ],
            [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field' => 'shipping_enterprise',
                'field_name' => '物流商',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_TEXT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'shipping_enterprise',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'field_relations' => []
            ],
            [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field' => 'is_customs',
                'field_name' => '是否报关',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_FLAG,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'is_customs',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
                'field_relations' => []
            ],
//            [
//                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
//                'field' => 'cost_list',
//                'field_name' => '附加费用',
//                'group_id' => 0,
//                'field_type' => FieldConstant::TYPE_TEXT,
//                'array_flag' => FieldConstant::IS_NOT_ARRAY,
//                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
//                'ext_info' => [],
//                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
//                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
//                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
//                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
//                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
//                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
//                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
//                'default_value' => '',
//                'tips' => '',
//                'show_flag' => FieldConstant::SHOW_FLAG,
//                'is_writable' => FieldConstant::WRITABLE_FLAG,
//                'columns' => [
//                    'data_key' => 'cost_list',
//                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
//                ],
//                'field_relations' => []
//            ],
            [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field' => 'product_total_count',
                'field_name' => '产品总数量',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'product_total_count',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'field_relations' => []
            ],
            [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field' => 'cost_calculation_type',
                'field_name' => '费用计算类型',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_SELECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'cost_calculation_type',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
                'field_relations' => []
            ],
            [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field' => 'product_total_count_no_parts',
                'field_name' => '产品总数量（不含配件）',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'product_total_count_no_parts',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'field_relations' => []
            ],
            [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field' => 'receivable_time',
                'field_name' => '应收日期',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_DATETIME,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'receivable_time',
                    'data_db_type' => FieldConstant::DB_TYPE_TIMESTAMP,
                ],
                'field_relations' => []
            ],
            [
                'object_name' => objConstant::OBJ_SHIPPING_INVOICE,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'from' => ['handler']
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ]
    ],

    \Constants::TYPE_SHIPPING_PRODUCT => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'shipping_record_id',
                'field_name' => '出运产品id',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_OBJECT_ID,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'shipping_record_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'field_relations' => []
            ],
            [
                'object_name' => objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'product_type',
                'field_name' => '产品类型',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_SELECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'product_type',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
                'field_relations' => []
            ],

            [
                'object_name' => objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'carton_count',
                'field_name' => '箱数',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'array_flag' => 0,
                'function_type' => 0,
                'ext_info' => [],
                'system_type' => 1,
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 1,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
                'is_writable' => 1,
                'columns' => [
                    'data_key' => 'carton_count',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'field_relations' => []
            ],

            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'carton_gross_weight',
                'field_name' => '单箱毛重',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'array_flag' => 0,
                'function_type' => 0,
                'ext_info' => [],
                'system_type' => 1,
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 1,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
                'is_writable' => 1,
                'columns' => [
                    'data_key' => 'carton_gross_weight',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'carton_net_weight',
                'field_name' => '单箱净重',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'array_flag' => 0,
                'function_type' => 0,
                'ext_info' => [],
                'system_type' => 1,
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 1,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
                'is_writable' => 1,
                'columns' => [
                    'data_key' => 'carton_net_weight',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'carton_size',
                'field_name' => '单箱尺寸(长宽高cm)',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'array_flag' => 0,
                'function_type' => 0,
                'ext_info' => ["carton_size_length", "carton_size_weight", "carton_size_height"],
                'system_type' => 1,
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 1,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 1,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
                'is_writable' => 1,
                // 这个字段不需要columns
                'columns' => [
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'carton_volume',
                'field_name' => '单箱体积(m³)',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'array_flag' => 0,
                'function_type' => 0,
                'ext_info' => [],
                'system_type' => 1,
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 0,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 1,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
                'is_writable' => 1,
                'columns' => [
                    'data_key' => 'carton_volume',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'container_no',
                'field_name' => '柜号',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_TEXT,
                'array_flag' => 0,
                'function_type' => 0,
                'ext_info' => [],
                'system_type' => 1,
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 1,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 1,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
                'is_writable' => 1,
                'columns' => [
                    'data_key' => 'container_no',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'container_seal_number',
                'field_name' => '封柜号',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_TEXT,
                'array_flag' => 0,
                'function_type' => 0,
                'ext_info' => [],
                'system_type' => 1,
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 1,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 1,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
                'is_writable' => 1,
                'columns' => [
                    'data_key' => 'container_seal_number',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'field_relations' => []
            ],
            [

                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'container_type',
                'field_name' => '货柜类型',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_SELECT,
                'array_flag' => 0,
                'function_type' => 0,
                'ext_info' => [
                    'value' => ["20'GP", "40'RH", "40'HQ", "40'GP"],
                    'item_type' => FieldConstant::VALUE_ITEM_TYPE,
                    'value_type' => FieldConstant:: SELECT_VALUE_TYPE_VALUE_EQUAL_LABEL,
                ],

                'system_type' => 1,
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 1,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 1,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
                'is_writable' => 1,
                'columns' => [
                    'data_key' => 'container_type',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'field_relations' => []
            ],

            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'count_per_carton',
                'field_name' => '每箱产品数',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'array_flag' => 0,
                'function_type' => 0,
                'ext_info' => [],
                'system_type' => 1,
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 1,
                'allow_setting_default' => 0,
                'allow_setting_tips' => 1,
                'required' => 0,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
                'is_writable' => 1,
                'columns' => [
                    'data_key' => 'count_per_carton',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'field_relations' => []
            ],

            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'count_per_package',
                'field_name' => '每包装产品数',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'array_flag' => 0,
                'function_type' => 0,
                'ext_info' => '',
                'system_type' => 1,
                'allow_setting' => 1,
                'allow_setting_show' => 1,
                'allow_setting_required' => 1,
                'allow_setting_default' => 1,
                'allow_setting_tips' => 1,
                'required' => 1,
                'default_value' => '',
                'tips' => '',
                'show_flag' => 1,
                'is_writable' => 1,
                'columns' => [
                    'data_key' => 'count_per_package',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'field_relations' => []
            ],

            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'customs_cn_name',
                'field_name' => '报关中文名',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_TEXT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'customs_cn_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'customs_name',
                'field_name' => '报关英文名',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_TEXT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'customs_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'description',
                'field_name' => '产品描述',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_TEXTAREA,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'description',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'hs_code',
                'field_name' => '海关编码',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_TEXT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'hs_code',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'invoice_product_id',
                'field_name' => 'invoice_product_id',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_PRODUCT
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'invoice_product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'field_relations' => []
            ],

            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'order_id',
                'field_name' => 'order_id',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_ORDER
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'order_id',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'package_gross_weight',
                'field_name' => '包装毛重',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => '',
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'package_gross_weight',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'package_remark',
                'field_name' => '产品包装说明',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_TEXTAREA,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'package_remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
                'field_relations' => []
            ],


            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'package_size',
                'field_name' => '包装尺寸(长宽高cm)',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => ["package_size_length", "package_size_weight", "package_size_height"],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                // 因为没有映射所以不写columns
                'columns' => [

                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'package_type',
                'field_name' => '包装方式',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_TEXT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'package_type',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'package_unit',
                'field_name' => '包装单位',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => '',
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'package_unit',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'package_volume',
                'field_name' => '包装体积',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => '',
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'package_volume',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'product_cn_name',
                'field_name' => '中文产品名称',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_TEXT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'product_cn_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'product_id',
                'field_name' => '产品id',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_TEXT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_PRODUCT
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'product_id',
                    'data_db_type' => FieldConstant::DB_TYPE_INTEGER,
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'product_image',
                'field_name' => '产品图片',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_IMAGE,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'product_image',
                    'data_db_type' => FieldConstant::DB_TYPE_JSONB,
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'product_model',
                'field_name' => '产品型号',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_TEXT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'product_model',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'product_name',
                'field_name' => '产品名称',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_TEXT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'product_name',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'product_net_weight',
                'field_name' => '单个净重',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'product_net_weight',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'field_relations' => []
            ],

            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'product_size',
                'field_name' => '单个尺寸(长宽高cm)',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => ["product_size_length", "product_size_weight", "product_size_height"],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'product_volume',
                'field_name' => '单个体积(m³)',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'product_volume',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'remark',
                'field_name' => '备注',
                'group_id' => 1,
                'field_type' => FieldConstant::TYPE_TEXT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'remark',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'sale_price',
                'field_name' => '销售单价',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_PRICE,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'sale_price',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'shipping_count',
                'field_name' => '本次出运数量',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_NUMBER,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::ALLOW_SETTING_TIPS,
                'required' => FieldConstant::ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'shipping_count',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'sku_id',
                'field_name' => 'sku_id',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'object_name' => objConstant::OBJ_PRODUCT_SKU,
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'sku_id',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'field_relations' => []
            ],
            [
                'object_name' =>  objConstant::OBJ_SHIPPING_RECORD,
                'field' => 'unit',
                'field_name' => '单位',
                'group_id' => 4,
                'field_type' => FieldConstant::TYPE_SELECT,
                'array_flag' => FieldConstant::IS_NOT_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' =>  ['item_type' => 1, 'value_type' => 1],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::SHOW_FLAG,
                'is_writable' => FieldConstant::WRITABLE_FLAG,
                'columns' => [
                    'data_key' => 'unit',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
                'field_relations' => []
            ]
        ],
    ],
    \Constants::TYPE_CASH_COLLECTION => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'from' => ['user_id']
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ],
        'field' => [
            'amount' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'bank_charge' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'bank_charge',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'capital_account_id' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'columns' => [
                    'data_key' => 'capital_account_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],
            'cash_collection_invoice_amount' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'cash_collection_invoice_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'cash_collection_invoice_bank_charge' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'cash_collection_invoice_bank_charge',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],

            'cash_collection_invoice_currency' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'cash_collection_invoice_currency',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'cash_collection_invoice_exchange_rate' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_EXCHANGE_RATE,
                'columns' => [
                    'data_key' => 'cash_collection_invoice_exchange_rate',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'cash_collection_invoice_exchange_rate_usd' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_EXCHANGE_RATE,
                'columns' => [
                    'data_key' => 'cash_collection_invoice_exchange_rate_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'cash_collection_invoice_id' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'columns' => [
                    'data_key' => 'cash_collection_invoice_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
            ],

            'collecting_bank' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'collecting_bank',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'collection_date' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_DATE,
                'columns' => [
                    'data_key' => 'collection_date',
                    'data_db_type' => FieldConstant::DB_TYPE_DATE,
                ],
            ],

            'collect_status' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'collect_status',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
                'ext_info' => [
                    'item_type' => FieldConstant::VALUE_ITEM_TYPE,
                    'value_type' => FieldConstant::SELECT_VALUE_TYPE_VALUE_MAP_LABEL,
                    'value' => [
                        ["value" => \common\library\cash_collection\CashCollection::IS_COLLECTED, "label" => "已生效",],
                        ["value" => \common\library\cash_collection\CashCollection::IS_NOT_COLLECTED, "label" => "未生效",],

                    ]
                ],
            ],
            'comment' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_TEXTAREA,
                'columns' => [
                    'data_key' => 'comment',
                    'data_db_type' => FieldConstant::DB_TYPE_TEXT,
                ],
            ],
            'company_id' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'columns' => [
                    'data_key' => 'company_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'allow_setting'  => FieldConstant::NOT_ALLOW_SETTING,
            ],
            'currency' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'currency',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'department_id' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_DEPARTMENT,
                'columns' => [
                    'data_key' => 'department_id',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            'exchange_loss_amount' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_PRICE,
            ],
            'exchange_loss_amount_usd' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_PRICE,
            ],
            'exchange_rate' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_EXCHANGE_RATE,
                'columns' => [
                    'data_key' => 'exchange_rate',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'exchange_rate_usd' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_EXCHANGE_RATE,
                'columns' => [
                    'data_key' => 'exchange_rate_usd',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],
            'opportunity_id' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'columns' => [
                    'data_key' => 'opportunity_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'allow_setting'  => FieldConstant::NOT_ALLOW_SETTING,
            ],
            'order_id' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_OBJECT,
                'columns' => [
                    'data_key' => 'order_id',
                    'data_db_type' => FieldConstant::DB_TYPE_BIGINT,
                ],
                'allow_setting'  => FieldConstant::NOT_ALLOW_SETTING,
            ],

            'payee' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'payee',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'real_amount' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_PRICE,
                'columns' => [
                    'data_key' => 'real_amount',
                    'data_db_type' => FieldConstant::DB_TYPE_NUMERIC,
                ],
            ],

            'refer_type' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'refer_type',
                    'data_db_type' => FieldConstant::DB_TYPE_SMALLINT,
                ],
                'ext_info' => [
                    'item_type' => FieldConstant::VALUE_ITEM_TYPE,
                    'value_type' => FieldConstant::SELECT_VALUE_TYPE_VALUE_MAP_LABEL,
                    'value' => [
                        ["value" => \common\library\cash_collection\CashCollection::REFER_TYPE_NONE, "label" => "未关联",],
                        ["value" => \common\library\cash_collection\CashCollection::REFER_TYPE_ORDER, "label" => "关联订单",],
                        ["value" => \common\library\cash_collection\CashCollection::REFER_TYPE_OPPORTUNITY, "label" => "关联商机",],

                    ]
                ],
                'allow_setting'  => FieldConstant::NOT_ALLOW_SETTING,
            ],
            'trade_no' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_TEXT,
                'columns' => [
                    'data_key' => 'trade_no',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],
            ],
            'type' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_SELECT,
                'columns' => [
                    'data_key' => 'type',
                    'data_db_type' => FieldConstant::DB_TYPE_VARCHAR,
                ],

                'ext_info' => [
                    'item_type' => FieldConstant::VALUE_ITEM_TYPE,
                    'value_type' => FieldConstant::SELECT_VALUE_TYPE_VALUE_EQUAL_LABEL,
                    'value' => array_values(\common\library\cash_collection\CashCollection::TYPE_MAP)
                ]
            ],
            'user_id' => [
                'object_name' => objConstant::OBJ_CASH_COLLECTION,
                'field_type' => FieldConstant::TYPE_USER,
                'columns' => [
                    'data_key' => 'user_id',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ],
    ],
    \Constants::TYPE_CASH_COLLECTION_INVOICE => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_CASH_COLLECTION_INVOICE,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'from' => ['handler']
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ]
    ],
    \Constants::TYPE_PAYMENT_INVOICE => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_PAYMENT_INVOICE,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'from' => 'handler'
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ],
    ],
    \Constants::TYPE_FORWARDER => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_FORWARDER,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'from' => ['handler']
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ]
    ],
    \Constants::TYPE_PURCHASE_RETURN_INVOICE => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_PURCHASE_RETURN_INVOICE,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'from' => ['handler']
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ]
    ],
    \Constants::TYPE_PLATFORM_PRODUCT_SKU => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_PLATFORM_PRODUCT,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'from' => ['handler']
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ],
    ],

    \Constants::TYPE_COST_INVOICE => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_COST_INVOICE,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'from' => ['handler']
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ],
    ],

    \Constants::TYPE_FOLLOWUP => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_FOLLOW_UP,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                    'from' => ['handler']
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ]
    ],

    //谈单指南-建议
    \Constants::TYPE_TMS_SUGGESTION => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_TMS_SUGGESTION,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_TMS_SUGGESTION,
                'field' => FieldConstant::SCOPE_HANDLER_FIELD_NAME,
                'field_name' => '权限负责人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'handler',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ]
    ],
    //谈单指南-话术
    \Constants::TYPE_TMS_SPEECHCRAFT => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_TMS_SPEECHCRAFT,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_TMS_SPEECHCRAFT,
                'field' => FieldConstant::SCOPE_HANDLER_FIELD_NAME,
                'field_name' => '权限负责人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'handler',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ]
    ],
    //谈单指南-交易文档
    \Constants::TYPE_TRADE_DOCUMENT => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_TRADE_DOCUMENT,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_TRADE_DOCUMENT,
                'field' => FieldConstant::SCOPE_HANDLER_FIELD_NAME,
                'field_name' => '权限负责人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'handler',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ]
    ],

    //名片夹
    \Constants::TYPE_BUSINESS_CARD => [
        'insert_field' => [
            [
                'object_name' => objConstant::OBJ_BUSINESS_CARD,
                'field' => FieldConstant::SCOPE_USER_IDS_FIELD_NAME,
                'field_name' => '权限归属人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'scope_user_ids',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
            [
                'object_name' => objConstant::OBJ_BUSINESS_CARD,
                'field' => FieldConstant::SCOPE_HANDLER_FIELD_NAME,
                'field_name' => '权限负责人',
                'group_id' => 0,
                'field_type' => FieldConstant::TYPE_USER,
                'array_flag' => FieldConstant::IS_ARRAY,
                'function_type' => FieldConstant::FUNCTION_TYPE_NORMAL,
                'ext_info' => [
                ],
                'system_type' => FieldConstant::SYSTEM_TYPE_BASE,
                'allow_setting' => FieldConstant::NOT_ALLOW_SETTING,
                'allow_setting_show' => FieldConstant::NOT_ALLOW_SETTING_SHOW,
                'allow_setting_required' => FieldConstant::NOT_ALLOW_SETTING_REQUIRED,
                'allow_setting_default' => FieldConstant::NOT_ALLOW_SETTING_DEFAULT,
                'allow_setting_tips' => FieldConstant::NOT_ALLOW_SETTING_TIPS,
                'required' => FieldConstant::NOT_ALLOW_REQUIRED,
                'default_value' => '',
                'tips' => '',
                'show_flag' => FieldConstant::HIDDEN_FLAG,
                'is_writable' => FieldConstant::READONLY_FLAG,
                'columns' => [
                    'data_key' => 'handler',
                    'data_db_type' => FieldConstant::DB_TYPE_ARRAY_BIGINT,
                ],
            ],
        ]
    ]
];
