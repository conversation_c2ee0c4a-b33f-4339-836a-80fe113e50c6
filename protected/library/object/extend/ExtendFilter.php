<?php

namespace common\library\object\extend;

use xiaoman\orm\common\Filter;

/**
 * Class ExtendFilter
 * @property string $client_id
 * @property integer $id
 * @property string $object_name
 * @property string $create_time
 * @property string $update_time
 * @property string ext_varchar_1
 * @property string ext_varchar_2
 * @property string ext_varchar_3
 * @property string ext_varchar_4
 * @property string ext_varchar_5
 * @property string ext_varchar_6
 * @property string ext_varchar_7
 * @property string ext_varchar_8
 * @property string ext_varchar_9
 * @property string ext_varchar_10
 * @property string ext_varchar_11
 * @property string ext_varchar_12
 * @property string ext_varchar_13
 * @property string ext_varchar_14
 * @property string ext_varchar_15
 * @property string ext_varchar_16
 * @property string ext_varchar_17
 * @property string ext_varchar_18
 * @property string ext_varchar_19
 * @property string ext_varchar_20
 * @property string ext_numeric_1
 * @property string ext_numeric_2
 * @property string ext_numeric_3
 * @property string ext_numeric_4
 * @property string ext_numeric_5
 * @property string ext_numeric_6
 * @property string ext_numeric_7
 * @property string ext_numeric_8
 * @property string ext_numeric_9
 * @property string ext_numeric_10
 * @property string ext_numeric_11
 * @property string ext_numeric_12
 * @property string ext_numeric_13
 * @property string ext_numeric_14
 * @property string ext_numeric_15
 * @property string ext_numeric_16
 * @property string ext_numeric_17
 * @property string ext_numeric_18
 * @property string ext_numeric_19
 * @property string ext_numeric_20
 * @property integer ext_int_1
 * @property integer ext_int_2
 * @property integer ext_int_3
 * @property integer ext_int_4
 * @property integer ext_int_5
 * @property integer ext_int_6
 * @property integer ext_int_7
 * @property integer ext_int_8
 * @property integer ext_int_9
 * @property integer ext_int_10
 * @property string $ext_text_1
 * @property string $ext_text_2
 * @property string $ext_text_3
 * @property string $ext_text_4
 * @property string $ext_text_5
 * @property string $ext_text_6
 * @property string $ext_text_7
 * @property string $ext_text_8
 * @property string $ext_text_9
 * @property string $ext_text_10
 * @property string $ext_text_11
 * @property string $ext_text_12
 * @property string $ext_text_13
 * @property string $ext_text_14
 * @property string $ext_text_15
 * @property string $ext_text_16
 * @property string $ext_text_17
 * @property string $ext_text_18
 * @property string $ext_text_19
 * @property string $ext_text_20
 * @property mixed ext_datetime_1
 * @property mixed ext_datetime_2
 * @property mixed ext_datetime_3
 * @property mixed ext_datetime_4
 * @property mixed ext_datetime_5
 * @property mixed ext_jsonb_1
 * @property mixed ext_jsonb_2
 * @property mixed ext_jsonb_3
 * @property mixed ext_jsonb_4
 * @property mixed ext_jsonb_5
 * @property mixed ext_array_bigint_1
 * @property mixed ext_array_bigint_2
 * @property mixed ext_array_bigint_3
 * @property mixed ext_array_bigint_4
 * @property mixed ext_array_bigint_5
 * @property mixed ext_array_varchar_1
 * @property mixed ext_array_varchar_2
 * @property mixed ext_array_varchar_3
 * @property mixed ext_array_varchar_4
 * @property mixed ext_array_varchar_5
 * @property mixed ext_array_varchar_6
 * @property mixed ext_array_varchar_7
 * @property mixed ext_array_varchar_8
 * @property mixed ext_array_varchar_9
 * @property mixed ext_array_varchar_10
 * @method BatchExtend find()
 */
class ExtendFilter extends Filter
{
    public static function getMetadataClass()
    {
        return ExtendMetadata::class;
    }

    protected function defaultSetting()
    {
        $this->client_id = $this->clientId;
    }
}