<?php

namespace common\library\object\object_define;

use common\library\business_card\orm\BusinessCardMetadata;
use common\library\forwarder\ForwarderMetadata;
use common\library\inquiry_collaboration\InquiryCollaborationMetadata;
use common\library\inquiry_collaboration_product\InquiryCollaborationProductMetadata;
use common\library\lead_v2\LeadCustomerMetadata;
use common\library\lead_v2\LeadMetadata;
use common\library\object\extend\ExtendMetadata;
use common\library\oms\alibaba_order_product\AlibabaOrderProductMetadata;
use common\library\oms\cash_collection_invoice\CashCollectionInvoiceMetadata;
use common\library\oms\cost_invoice\CostInvoiceMetadata;
use common\library\oms\inbound_invoice\other\OtherInboundInvoiceMetadata;
use common\library\oms\inbound_invoice\purchase\PurchaseInboundInvoiceMetadata;
use common\library\oms\inquiry_feedback\InquiryFeedBackMetadata;
use common\library\oms\inventory\product_inventory\ProductInventoryMetadata;
use common\library\oms\order\OrderMetadata;
use common\library\oms\order_product\OrderProductMetadata;
use common\library\oms\order_profit\OrderProfitMetadata;
use common\library\oms\outbound_invoice\other\OtherOutboundInvoiceMetadata;
use common\library\oms\outbound_invoice\sale\SaleOutboundInvoiceMetadata;
use common\library\oms\payment_invoice\PaymentInvoiceMetadata;
use common\library\oms\payment_invoice\record\PaymentInvoiceRecordMetadata;
use common\library\oms\product_transfer\inbound\InboundProductTransferMetadata;
use common\library\oms\product_transfer\other\OtherTransferMetadata;
use common\library\oms\product_transfer\outbound\OutboundProductTransferMetadata;
use common\library\oms\product_transfer\relation\ProductTransferRelationMetadata;
use common\library\oms\quotation\QuotationMetadata;
use common\library\oms\quotation_product\QuotationProductMetadata;
use common\library\oms\shipping_invoice\ShippingInvoiceMetadata;
use common\library\oms\warehouse\WarehouseMetadata;
use common\library\oms\warehouse_return_invoice\purchase\PurchaseReturnInvoiceMetadata;
use common\library\oms_v2\cash_collection\CashCollectionMetadata;
use common\library\oms_v2\other_outbound_invoice_record\OtherOutboundInvoiceRecordMetadata;
use common\library\oms_v2\product_transfer_purchase\ProductTransferPurchaseMetadata;
use common\library\oms_v2\product_transfer_purchase_record\ProductTransferPurchaseRecordMetadata;
use common\library\oms_v2\sale_outbound_record\SaleOutboundRecordMetadata;
use common\library\opportunity\orm\OpportunityMetadata;
use common\library\platform_product\PlatformProductMetadata;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\product_v2\ProductMetadata;
use common\library\product_v2\relation\CombineProductRelationMetadata;
use common\library\product_v2\sku\ProductSkuMetadata;
use common\library\purchase\purchase_order\PurchaseOrderMetadata;
use common\library\purchase\purchase_order_product\PurchaseOrderProductMetaData;
use common\library\speechcraft\SpeechcraftMetadata;
use common\library\supplier\contact\SupplierContactMetadata;
use common\library\supplier\product\SupplierProductMetadata;
use common\library\supplier\SupplierMetadata;
use common\library\tms_tips\TmsTipsMetadata;
use common\library\trade_document\page\user\PageMetadata;
use common\library\trail\orm\FollowupMetadata;
use common\library\translate_v2\system\SystemTranslateMetadata;
use common\library\translate_v2\user\UserTranslateMetadata;
use xiaoman\orm\metadata\Metadata;

class Constant
{
    const OBJ_ORDER = 'objOrder';        //订单
    const OBJ_ORDER_PRODUCT = 'objOrderProduct';     //订单产品
    const OBJ_ALIBABA_ORDER_PRODUCT = 'objAlibabaOrderProduct'; //阿里订单产品

    const OBJ_PRODUCT = 'objProduct';    //产品
    const OBJ_PRODUCT_SKU = 'objProductSku';     //产品规格
    const OBJ_PLATFORM_PRODUCT = 'objPlatformProduct';  // 平台产品
    const OBJ_PLATFORM_PRODUCT_SKU = 'objPlatformProductSKU';  // 平台产品规格

    const OBJ_QUOTATION = 'objQuotation';
    const OBJ_QUOTATION_PRODUCT = 'objQuotationProduct';
    const OBJ_COMPANY = 'objCompany';
    const OBJ_CUSTOMER = 'objCustomer';
    const OBJ_MAIL = 'objMail';
    const OBJ_LEAD = 'objLead';
    const OBJ_LEAD_CUSTOMER = 'objLeadCustomer';
    const OBJ_OPPORTUNITY = 'objOpportunity';
    const OBJ_OPPORTUNITY_PRODUCT = 'objOpportunityProduct';    //商机产品
    const OBJ_CASH_COLLECTION = 'objCashCollection';
    const OBJ_PURCHASE_ORDER = 'objPurchaseOrder';
    const OBJ_PURCHASE_ORDER_PRODUCT = 'objPurchaseOrderProduct';
    const OBJ_PRODUCT_TRANSFER_PURCHASE = 'objProductTransferPurchase';//采购任务
    const OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD = 'objProductTransferPurchaseRecord';//采购任务产品
    const OBJ_SUPPLIER = 'objSupplier';
    const OBJ_SUPPLIER_PRODUCT = 'objSupplierProduct';
    const OBJ_SUPPLIER_CONTACT = 'objSupplierContact';
    const OBJ_SUPPLIER_ACCOUNT = 'objSupplierAccount';
    const OBJ_COST_INVOICE = 'objCostInvoice';
    const OBJ_ORDER_PROFIT = 'objOrderProfit';
    const OBJ_PURCHASE_INBOUND_INVOICE = 'objPurchaseInboundInvoice';
    const OBJ_PURCHASE_INBOUND_INVOICE_RECORD = 'objPurchaseInboundInvoiceRecord';
    const OBJ_OTHER_INBOUND_INVOICE = 'objOtherInboundInvoice';
    const OBJ_OTHER_INBOUND_INVOICE_RECORD = 'objOtherInboundInvoiceRecord';
    const OBJ_SALE_OUTBOUND_INVOICE = 'objSaleOutBoundInvoice';
    const OBJ_SALE_OUTBOUND_INVOICE_RECORD = 'objSaleOutBoundInvoiceRecord';
    const OBJ_OTHER_OUTBOUND_INVOICE = 'objOtherOutboundInvoice';
    const OBJ_OTHER_OUTBOUND_INVOICE_RECORD = 'objOtherOutboundInvoiceRecord';
    const OBJ_WAREHOUSE = 'objWarehouse';
    const OBJ_PURCHASE_RETURN_INVOICE = 'objPurchaseReturnInvoice';
    const OBJ_PAYMENT_INVOICE = 'objPaymentInvoice';
    const OBJ_CASH_COLLECTION_INVOICE = 'objCashCollectionInvoice';
    const OBJ_WORK_JOURNAL = 'objWorkJournal';
    const OBJ_SHIPPING_INVOICE = 'objShippingInvoice';  //出运单
    const OBJ_SHIPPING_RECORD = 'objShippingRecord'; //出运单产品
    const OBJ_FORWARDER = 'objForwarder';
    const OBJ_CAPITAL_ACCOUNT = 'objCapitalAccount';
    const OBJ_PRODUCT_TRANSFER_INBOUND = 'objProductTransferInbound';
    const OBJ_PRODUCT_TRANSFER_INBOUND_RECORD = 'objProductTransferInboundRecord';
    const OBJ_PRODUCT_TRANSFER_OUTBOUND = 'objProductTransferOutbound'; // product_transfer_outbound

    const OBJ_PRODUCT_TRANSFER_OUTBOUND_RECORD = 'objProductTransferOutboundRecord'; // product_transfer_outbound_record
    const OBJ_PRODUCT_TRANSFER_OTHER = 'objProductTransferOther';
    const OBJ_INQUIRY_COLLABORATION = 'objInquiryCollaboration';
    const OBJ_INQUIRY_COLLABORATION_PRODUCT = 'objInquiryCollaborationProduct';
    const OBJ_INQUIRY_FEEDBACK = 'objInquiryFeedback';

    const OBJ_TMS_SUGGESTION = 'objTmsSuggestion';
    const OBJ_TMS_SPEECHCRAFT = 'objSpeechCraft';
    const OBJ_TRADE_DOCUMENT = 'objTradeDocument';

    const OBJ_BUSINESS_CARD = 'objBusinessCard';

    const OBJ_DYNAMIC_TRAIL = 'objDynamicTrail';
    const OBJ_FOLLOW_UP = 'objFollowUp';
    const OBJ_SCHEDULE = 'objSchedule';
    const OBJ_DISK  = 'objDisk';//云盘

    // 业务类型
    const BUSINESS_TYPE_COMMON = 'common';          // 公共业务类型
    const BUSINESS_TYPE_COMPANY_COMMON = 'common';          // 业务类型：公海客户
    const BUSINESS_TYPE_COMPANY_POOL = 'pool';          // 业务类型：公海客户
    const BUSINESS_TYPE_COMPANY_PRIVATE = 'private';    // 业务类型：私海客户
    const BUSINESS_TYPE_COMPANY_DUPLICATION = 'duplication'; // 业务类型：客户查重

    const BUSINESS_TYPE_MAP = [
        self::OBJ_COMPANY => [
            self::BUSINESS_TYPE_COMPANY_POOL,
            self::BUSINESS_TYPE_COMPANY_PRIVATE,
        ],
        self::OBJ_CUSTOMER => [
            self::BUSINESS_TYPE_COMPANY_POOL,
            self::BUSINESS_TYPE_COMPANY_PRIVATE,
        ]
    ];

    const OBJ_MAP = [
        \Constants::TYPE_ORDER => self::OBJ_ORDER,
        \Constants::TYPE_ORDER_PRODUCT => self::OBJ_ORDER_PRODUCT,
        \Constants::TYPE_ALIBABA_ORDER_PRODUCT => self::OBJ_ALIBABA_ORDER_PRODUCT,
        \Constants::TYPE_PRODUCT => self::OBJ_PRODUCT,
        \Constants::TYPE_PRODUCT_SKU => self::OBJ_PRODUCT_SKU,
        \Constants::TYPE_PLATFORM_PRODUCT_SKU => self::OBJ_PLATFORM_PRODUCT,
        \Constants::TYPE_QUOTATION => self::OBJ_QUOTATION,
        \Constants::TYPE_QUOTATION_PRODUCT => self::OBJ_QUOTATION_PRODUCT,
        \Constants::TYPE_COMPANY => self::OBJ_COMPANY,
        \Constants::TYPE_COMPANY_POOL => self::OBJ_COMPANY,
        \Constants::TYPE_CUSTOMER => self::OBJ_CUSTOMER,
        \Constants::TYPE_MAIL => self::OBJ_MAIL,
        \Constants::TYPE_LEAD => self::OBJ_LEAD,
        \Constants::TYPE_LEAD_CUSTOMER => self::OBJ_LEAD_CUSTOMER,
        \Constants::TYPE_OPPORTUNITY => self::OBJ_OPPORTUNITY,
        \Constants::TYPE_OPPORTUNITY_PRODUCT => self::OBJ_OPPORTUNITY_PRODUCT,
        \Constants::TYPE_CASH_COLLECTION => self::OBJ_CASH_COLLECTION,
        \Constants::TYPE_PURCHASE_ORDER => self::OBJ_PURCHASE_ORDER,
        \Constants::TYPE_PURCHASE_ORDER_PRODUCT => self::OBJ_PURCHASE_ORDER_PRODUCT,
        \Constants::TYPE_SUPPLIER => self::OBJ_SUPPLIER,
        \Constants::TYPE_SUPPLIER_PRODUCT => self::OBJ_SUPPLIER_PRODUCT,
        \Constants::TYPE_SUPPLIER_CONTACT => self::OBJ_SUPPLIER_CONTACT,
        \Constants::TYPE_SUPPLIER_ACCOUNT => self::OBJ_SUPPLIER_ACCOUNT,
        \Constants::TYPE_COST_INVOICE => self::OBJ_COST_INVOICE,
        \Constants::TYPE_ORDER_PROFIT => self::OBJ_ORDER_PROFIT,
        \Constants::TYPE_PURCHASE_INBOUND_INVOICE => self::OBJ_PURCHASE_INBOUND_INVOICE,
        \Constants::TYPE_OTHER_INBOUND_INVOICE => self::OBJ_OTHER_INBOUND_INVOICE,
        \Constants::TYPE_SALE_OUTBOUND_INVOICE => self::OBJ_SALE_OUTBOUND_INVOICE,
        \Constants::TYPE_OTHER_OUTBOUND_INVOICE => self::OBJ_OTHER_OUTBOUND_INVOICE,
        \Constants::TYPE_WAREHOUSE => self::OBJ_WAREHOUSE,
        \Constants::TYPE_PURCHASE_RETURN_INVOICE => self::OBJ_PURCHASE_RETURN_INVOICE,
        \Constants::TYPE_PAYMENT_INVOICE => self::OBJ_PAYMENT_INVOICE,
        \Constants::TYPE_CASH_COLLECTION_INVOICE => self::OBJ_CASH_COLLECTION_INVOICE,
        \Constants::TYPE_WORK_JOURNAL => self::OBJ_WORK_JOURNAL,
        \Constants::TYPE_SHIPPING_INVOICE => self::OBJ_SHIPPING_INVOICE,
        \Constants::TYPE_SHIPPING_PRODUCT=> self::OBJ_SHIPPING_RECORD,
        \Constants::TYPE_PRODUCT_TRANSFER_PURCHASE => self::OBJ_PRODUCT_TRANSFER_PURCHASE,
        \Constants::TYPE_PRODUCT_TRANSFER_PURCHASE_PRODUCT => self::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
        \Constants::TYPE_PRODUCT_TRANSFER_INBOUND => self::OBJ_PRODUCT_TRANSFER_INBOUND,
        \Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND => self::OBJ_PRODUCT_TRANSFER_OUTBOUND,
        \Constants::TYPE_PRODUCT_TRANSFER_OTHER => self::OBJ_PRODUCT_TRANSFER_OTHER,
        \Constants::TYPE_INQUIRY_COLLABORATION => self::OBJ_INQUIRY_COLLABORATION,
        \Constants::TYPE_INQUIRY_COLLABORATION_PRODUCT => self::OBJ_INQUIRY_COLLABORATION_PRODUCT,
        \Constants::TYPE_INQUIRY_FEEDBACK => self::OBJ_INQUIRY_FEEDBACK,
        \Constants::TYPE_TMS_SUGGESTION => self::OBJ_TMS_SUGGESTION,
        \Constants::TYPE_TMS_SPEECHCRAFT => self::OBJ_TMS_SPEECHCRAFT,
        \Constants::TYPE_TRADE_DOCUMENT => self::OBJ_TRADE_DOCUMENT,
        \Constants::TYPE_SALE_OUTBOUND_PRODUCT => self::OBJ_SALE_OUTBOUND_INVOICE_RECORD,
        \Constants::TYPE_DISK => self::OBJ_DISK,
        \Constants::TYPE_FORWARDER => self::OBJ_FORWARDER,
        \Constants::TYPE_FOLLOWUP => self::OBJ_FOLLOW_UP,
        \Constants::TYPE_BUSINESS_CARD => self::OBJ_BUSINESS_CARD,
    ];

    const OBJ_PRIMARY_KEY_MAP = [
        self::OBJ_ORDER => 'order_id',
        self::OBJ_PRODUCT => 'product_id',
        self::OBJ_PRODUCT_SKU => 'sku_id',
        self::OBJ_QUOTATION => 'quotation_id',
        self::OBJ_QUOTATION_PRODUCT => 'quotation_product_id',
        self::OBJ_COMPANY => 'company_id',
        self::OBJ_CUSTOMER => 'customer_id',
        self::OBJ_MAIL => 'mail_id',
        self::OBJ_LEAD => 'lead_id',
        self::OBJ_OPPORTUNITY => 'opportunity_id',
        self::OBJ_CASH_COLLECTION => 'cash_collection_id',
        self::OBJ_PURCHASE_ORDER => 'purchase_order_id',
        self::OBJ_PURCHASE_ORDER_PRODUCT => 'purchase_order_product_id',
        self::OBJ_SUPPLIER => 'supplier_id',
        self::OBJ_SUPPLIER_CONTACT => 'supplier_contact_id',
        self::OBJ_SUPPLIER_ACCOUNT => 'supplier_account_id',
        self::OBJ_COST_INVOICE => 'cost_invoice_id',
        self::OBJ_ORDER_PROFIT => 'order_profit_id',
        self::OBJ_PURCHASE_INBOUND_INVOICE => 'inbound_invoice_id',
        self::OBJ_OTHER_INBOUND_INVOICE => 'inbound_invoice_id',
        self::OBJ_SALE_OUTBOUND_INVOICE => 'outbound_invoice_id',
        self::OBJ_OTHER_OUTBOUND_INVOICE => 'outbound_invoice_id',
        self::OBJ_WAREHOUSE => 'warehouse_id',
        self::OBJ_PURCHASE_RETURN_INVOICE => 'warehouse_return_invoice_id',
        self::OBJ_PAYMENT_INVOICE => 'payment_invoice_id',
        self::OBJ_CASH_COLLECTION_INVOICE => 'cash_collection_invoice_id',
        self::OBJ_WORK_JOURNAL => 'journal_id',
        self::OBJ_SHIPPING_INVOICE => 'shipping_invoice_id',
        self::OBJ_SHIPPING_RECORD => 'shipping_record_id',
        self::OBJ_FORWARDER => 'forwarder_id',
        self::OBJ_ORDER_PRODUCT => 'id',
        self::OBJ_ALIBABA_ORDER_PRODUCT => 'ali_order_product_id',
        self::OBJ_PRODUCT_TRANSFER_PURCHASE => 'transfer_invoice_id',
        self::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD => 'transfer_invoice_record_id',
        self::OBJ_PRODUCT_TRANSFER_INBOUND => 'transfer_invoice_id',
        self::OBJ_PRODUCT_TRANSFER_INBOUND_RECORD => 'transfer_invoice_record_id',
        self::OBJ_PRODUCT_TRANSFER_OUTBOUND => 'transfer_invoice_id',
        self::OBJ_PRODUCT_TRANSFER_OTHER => 'transfer_invoice_id',
        self::OBJ_INQUIRY_COLLABORATION => 'inquiry_collaboration_id',
        self::OBJ_INQUIRY_COLLABORATION_PRODUCT => 'inquiry_product_id',
        self::OBJ_INQUIRY_FEEDBACK => 'inquiry_feedback_id',
        self::OBJ_TMS_SUGGESTION => 'tips_id',
        self::OBJ_TMS_SPEECHCRAFT => 'tips_id',
        self::OBJ_SALE_OUTBOUND_INVOICE_RECORD => 'outbound_record_id',
        self::OBJ_TRADE_DOCUMENT => 'page_id',
        self::OBJ_BUSINESS_CARD => 'business_card_id',
        self::OBJ_FOLLOW_UP => 'follow_up_id',
        self::OBJ_OTHER_INBOUND_INVOICE_RECORD => 'inbound_record_id',
        self::OBJ_OTHER_OUTBOUND_INVOICE_RECORD => 'outbound_record_id'
    ];

    // 后续编号字段定义清晰可以考虑舍弃该常量
    const OBJ_SERIAL_ID_MAP = [
        self::OBJ_ORDER => 'order_no',
        self::OBJ_PRODUCT => 'product_no',
        self::OBJ_PRODUCT_SKU => 'sku_id',
        self::OBJ_QUOTATION => 'quotation_no',
        self::OBJ_COMPANY => 'serial_id',
        self::OBJ_LEAD => 'serial_id',
        self::OBJ_OPPORTUNITY => 'serial_id',
        self::OBJ_CASH_COLLECTION => 'cash_collection_no',
        self::OBJ_PURCHASE_ORDER => 'purchase_order_no',
        self::OBJ_SUPPLIER => 'supplier_no',
        self::OBJ_SUPPLIER_CONTACT => 'supplier_product_no',
        self::OBJ_COST_INVOICE => 'cost_invoice_no',
        self::OBJ_PURCHASE_INBOUND_INVOICE => 'serial_id',
        self::OBJ_OTHER_INBOUND_INVOICE => 'serial_id',
        self::OBJ_SALE_OUTBOUND_INVOICE => 'serial_id',
        self::OBJ_OTHER_OUTBOUND_INVOICE => 'serial_id',
        self::OBJ_WAREHOUSE => 'warehouse_no',
        self::OBJ_PURCHASE_RETURN_INVOICE => 'serial_id',
        self::OBJ_PAYMENT_INVOICE => 'payment_invoice_no',
        self::OBJ_CASH_COLLECTION_INVOICE => 'cash_collection_invoice_no',
        self::OBJ_SHIPPING_INVOICE => 'shipping_invoice_no',
        self::OBJ_FORWARDER => 'forwarder_no',
        self::OBJ_PRODUCT_TRANSFER_PURCHASE => 'serial_id',
        self::OBJ_PRODUCT_TRANSFER_INBOUND => 'serial_id',
        self::OBJ_PRODUCT_TRANSFER_OUTBOUND => 'serial_id',
        self::OBJ_PRODUCT_TRANSFER_OTHER => 'serial_id',
        self::OBJ_INQUIRY_COLLABORATION => 'inquiry_collaboration_no',
    ];

    const OBJ_METADATA_MAP = [
        self::OBJ_ORDER => OrderMetadata::class,
        self::OBJ_PRODUCT => ProductMetadata::class,
        self::OBJ_PRODUCT_SKU => ProductSkuMetadata::class,
        self::OBJ_CASH_COLLECTION => CashCollectionMetadata::class,
        self::OBJ_PURCHASE_ORDER => PurchaseOrderMetadata::class,
        self::OBJ_PURCHASE_ORDER_PRODUCT => PurchaseOrderProductMetaData::class,
        self::OBJ_SUPPLIER => SupplierMetadata::class,
        self::OBJ_SUPPLIER_CONTACT => SupplierContactMetadata::class,
        self::OBJ_SUPPLIER_PRODUCT => SupplierProductMetadata::class,
        self::OBJ_COST_INVOICE => CostInvoiceMetadata::class,
        self::OBJ_ORDER_PROFIT => OrderProfitMetadata::class,
        self::OBJ_PURCHASE_INBOUND_INVOICE => PurchaseInboundInvoiceMetadata::class,
        self::OBJ_OTHER_INBOUND_INVOICE => OtherInboundInvoiceMetadata::class,
        self::OBJ_SALE_OUTBOUND_INVOICE => SaleOutboundInvoiceMetadata::class,
        self::OBJ_OTHER_OUTBOUND_INVOICE => OtherOutboundInvoiceMetadata::class,
        self::OBJ_WAREHOUSE => WarehouseMetadata::class,
        self::OBJ_PURCHASE_RETURN_INVOICE => PurchaseReturnInvoiceMetadata::class,
        self::OBJ_PAYMENT_INVOICE => PaymentInvoiceMetadata::class,
        self::OBJ_CASH_COLLECTION_INVOICE => CashCollectionInvoiceMetadata::class,
        self::OBJ_SHIPPING_INVOICE => ShippingInvoiceMetadata::class,
        self::OBJ_FORWARDER => ForwarderMetadata::class,
        self::OBJ_ORDER_PRODUCT => OrderProductMetadata::class,
    
        //todo bob检查这里是否可以直接替换
        //        self::OBJ_COMPANY => CompanyMetadata::class,
        //        self::OBJ_CUSTOMER => CustomerMetadata::class,
    
        self::OBJ_COMPANY => \common\library\customer_v3\company\orm\CompanyMetadata::class,
        self::OBJ_CUSTOMER => \common\library\customer_v3\customer\orm\CustomerMetadata::class,

        // TODO: 暂时用旧的ORM，线索paas化后替换为新版
        self::OBJ_LEAD => LeadMetadata::class,
        self::OBJ_LEAD_CUSTOMER => LeadCustomerMetadata::class,

        self::OBJ_OPPORTUNITY => OpportunityMetadata::class,
        self::OBJ_PRODUCT_TRANSFER_PURCHASE => ProductTransferPurchaseMetadata::class,
        self::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD => ProductTransferPurchaseRecordMetadata::class,
        self::OBJ_PRODUCT_TRANSFER_INBOUND => InboundProductTransferMetadata::class,
        self::OBJ_PRODUCT_TRANSFER_OUTBOUND => OutboundProductTransferMetadata::class,
        self::OBJ_PRODUCT_TRANSFER_OTHER => OtherTransferMetadata::class,
        self::OBJ_QUOTATION => QuotationMetadata::class,
        self::OBJ_QUOTATION_PRODUCT => QuotationProductMetadata::class,
        self::OBJ_INQUIRY_COLLABORATION => InquiryCollaborationMetadata::class,
        self::OBJ_INQUIRY_COLLABORATION_PRODUCT => InquiryCollaborationProductMetadata::class,
        self::OBJ_INQUIRY_FEEDBACK => InquiryFeedBackMetadata::class,
        self::OBJ_ALIBABA_ORDER_PRODUCT => AlibabaOrderProductMetadata::class,
        self::OBJ_TMS_SPEECHCRAFT => SpeechcraftMetadata::class,
        self::OBJ_TMS_SUGGESTION => TmsTipsMetadata::class,
        self::OBJ_SALE_OUTBOUND_INVOICE_RECORD => SaleOutboundRecordMetadata::class,
        self::OBJ_PLATFORM_PRODUCT => PlatformProductMetadata::class,
        self::OBJ_TRADE_DOCUMENT => PageMetadata::class,
        self::OBJ_FOLLOW_UP => FollowUpMetadata::class,
        self::OBJ_BUSINESS_CARD => BusinessCardMetadata::class,
        self::OBJ_OTHER_OUTBOUND_INVOICE_RECORD => OtherOutboundInvoiceRecordMetadata::class,
    ];

    //用于字段迁移脚本，其他功能甚用
    const RELATION_OBJ_MAP = [
        \Constants::TYPE_PRODUCT => [
            \Constants::TYPE_PRODUCT,
            \Constants::TYPE_PRODUCT_SKU
        ],
        \Constants::TYPE_COMPANY => [
            \Constants::TYPE_COMPANY,
        ],
        \Constants::TYPE_CUSTOMER => [
            \Constants::TYPE_CUSTOMER,
        ],
        \Constants::TYPE_OPPORTUNITY => [
            \Constants::TYPE_OPPORTUNITY,
        ],
        \Constants::TYPE_SUPPLIER => [
            \Constants::TYPE_SUPPLIER,
            \Constants::TYPE_SUPPLIER_CONTACT,
            \Constants::TYPE_SUPPLIER_ACCOUNT
        ],
        \Constants::TYPE_ORDER => [
            \Constants::TYPE_ORDER,
            \Constants::TYPE_ORDER_PRODUCT
        ],
        \Constants::TYPE_PRODUCT_TRANSFER_PURCHASE => [
            \Constants::TYPE_PRODUCT_TRANSFER_PURCHASE,
            \Constants::TYPE_PRODUCT_TRANSFER_PURCHASE_PRODUCT,
        ],
        \Constants::TYPE_PURCHASE_ORDER => [
            \Constants::TYPE_PURCHASE_ORDER,
            \Constants::TYPE_PURCHASE_ORDER_PRODUCT
        ],
        \Constants::TYPE_QUOTATION => [
            \Constants::TYPE_QUOTATION,
            \Constants::TYPE_QUOTATION_PRODUCT
        ],
        \Constants::TYPE_INQUIRY_COLLABORATION => [
            \Constants::TYPE_INQUIRY_COLLABORATION,
            \Constants::TYPE_INQUIRY_COLLABORATION_PRODUCT
        ],
        \Constants::TYPE_INQUIRY_FEEDBACK => [
            \Constants::TYPE_INQUIRY_FEEDBACK
        ],
        \Constants::TYPE_SHIPPING_INVOICE => [
            \Constants::TYPE_SHIPPING_INVOICE,
            \Constants::TYPE_SHIPPING_PRODUCT,
        ],
        \Constants::TYPE_SALE_OUTBOUND_INVOICE => [
            \Constants::TYPE_SALE_OUTBOUND_INVOICE,
            \Constants::TYPE_SALE_OUTBOUND_PRODUCT,
        ],
        \Constants::TYPE_CASH_COLLECTION => [
            \Constants::TYPE_CASH_COLLECTION,
        ],
    ];

    //需要兼容添加字段的模块， 用于"web接口"和"初始化模块字段"时兼容处理
    const WEB_ADD_FIELD_TYPE = [
        \Constants::TYPE_PRODUCT,
        \Constants::TYPE_COMPANY,
        \Constants::TYPE_CUSTOMER,
        \Constants::TYPE_OPPORTUNITY,
        \Constants::TYPE_SUPPLIER,
        \Constants::TYPE_ORDER,
        \Constants::TYPE_PURCHASE_ORDER,
        \Constants::TYPE_PURCHASE_ORDER_PRODUCT,
        \Constants::TYPE_QUOTATION,
        \Constants::TYPE_QUOTATION_PRODUCT,
        \Constants::TYPE_INQUIRY_COLLABORATION,
        \Constants::TYPE_INQUIRY_COLLABORATION_PRODUCT,
        \Constants::TYPE_INQUIRY_FEEDBACK,
        \Constants::TYPE_ALIBABA_ORDER_PRODUCT,
        \Constants::TYPE_PRODUCT_TRANSFER_PURCHASE,
        \Constants::TYPE_PRODUCT_TRANSFER_PURCHASE_PRODUCT,
        \Constants::TYPE_SALE_OUTBOUND_INVOICE,
        \Constants::TYPE_SALE_OUTBOUND_PRODUCT,
        \Constants::TYPE_CASH_COLLECTION,
    ];
    
    // 模块没有权限管控的，这里会逐步被FUNCTIONAL_MAP替代
    const NO_FUNCTIONAL_LIST = [
        self::OBJ_PRODUCT_TRANSFER_PURCHASE,
        self::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD,
    ];

    const FUNCTIONAL_MAP = [
        self::OBJ_PRODUCT => PrivilegeConstants::FUNCTIONAL_PRODUCT,
        self::OBJ_PRODUCT_SKU => PrivilegeConstants::FUNCTIONAL_PRODUCT,
        self::OBJ_COMPANY => PrivilegeConstants::FUNCTIONAL_CUSTOMER,
        self::OBJ_CUSTOMER => PrivilegeConstants::FUNCTIONAL_CUSTOMER,
        self::OBJ_LEAD => PrivilegeConstants::FUNCTIONAL_LEAD,
        self::OBJ_LEAD_CUSTOMER => PrivilegeConstants::FUNCTIONAL_LEAD,
        self::OBJ_OPPORTUNITY => PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
        self::OBJ_ORDER => PrivilegeConstants::FUNCTIONAL_ORDER,
        self::OBJ_ORDER_PRODUCT => PrivilegeConstants::FUNCTIONAL_ORDER,
        self::OBJ_QUOTATION => PrivilegeConstants::FUNCTIONAL_QUOTATION,
        self::OBJ_QUOTATION_PRODUCT => PrivilegeConstants::FUNCTIONAL_QUOTATION,
        self::OBJ_CASH_COLLECTION => PrivilegeConstants::FUNCTIONAL_CASH_COLLECTION,
        self::OBJ_CASH_COLLECTION_INVOICE => PrivilegeConstants::FUNCTIONAL_CASH_COLLECTION_INVOICE,
        self::OBJ_SUPPLIER => PrivilegeConstants::FUNCTIONAL_PURCHASE_SUPPLIER,
        self::OBJ_SUPPLIER_PRODUCT => PrivilegeConstants::FUNCTIONAL_PURCHASE_SUPPLIER_PRODUCT,
        self::OBJ_SUPPLIER_CONTACT => PrivilegeConstants::FUNCTIONAL_PURCHASE_SUPPLIER,
        self::OBJ_PURCHASE_ORDER => PrivilegeConstants::FUNCTIONAL_PURCHASE_ORDER,
        self::OBJ_PURCHASE_ORDER_PRODUCT => PrivilegeConstants::FUNCTIONAL_PURCHASE_ORDER,
        self::OBJ_INQUIRY_COLLABORATION => PrivilegeConstants::FUNCTIONAL_INQUIRY_COLLABORATION,
        self::OBJ_INQUIRY_COLLABORATION_PRODUCT => PrivilegeConstants::FUNCTIONAL_INQUIRY_COLLABORATION,
        self::OBJ_INQUIRY_FEEDBACK => PrivilegeConstants::FUNCTIONAL_INQUIRY_FEEDBACK,
        self::OBJ_SALE_OUTBOUND_INVOICE_RECORD => PrivilegeConstants::FUNCTIONAL_SALES_OUTBOUND,
        self::OBJ_SHIPPING_RECORD => PrivilegeConstants::FUNCTIONAL_SHIPPING_INVOICE,
        self::OBJ_SALE_OUTBOUND_INVOICE => PrivilegeConstants::FUNCTIONAL_SALES_OUTBOUND,
        self::OBJ_TMS_SUGGESTION => PrivilegeConstants::FUNCTIONAL_SUGGESTION,
        self::OBJ_TMS_SPEECHCRAFT => PrivilegeConstants::FUNCTIONAL_SPEECHCRAFT,
        self::OBJ_OTHER_OUTBOUND_INVOICE => PrivilegeConstants::FUNCTIONAL_OTHER_OUTBOUND,
        self::OBJ_PAYMENT_INVOICE => PrivilegeConstants::FUNCTIONAL_PAYMENT_INVOICE,
        self::OBJ_COST_INVOICE => PrivilegeConstants::FUNCTIONAL_COST_INVOICE,
        self::OBJ_FORWARDER => PrivilegeConstants::FUNCTIONAL_FORWARDER,
        self::OBJ_PURCHASE_RETURN_INVOICE => PrivilegeConstants::FUNCTIONAL_PURCHASE_RETURN,
        self::OBJ_SHIPPING_INVOICE => PrivilegeConstants::FUNCTIONAL_SHIPPING_INVOICE,
        self::OBJ_PURCHASE_INBOUND_INVOICE => PrivilegeConstants::FUNCTIONAL_PURCHASE_INBOUND,
        self::OBJ_OTHER_INBOUND_INVOICE => PrivilegeConstants::FUNCTIONAL_OTHER_INBOUND,
        self::OBJ_PLATFORM_PRODUCT => PrivilegeConstants::FUNCTIONAL_PLATFORM_PRODUCT,
        self::OBJ_TRADE_DOCUMENT => PrivilegeConstants::FUNCTIONAL_DOCUMENT,
        self::OBJ_BUSINESS_CARD => PrivilegeConstants::FUNCTIONAL_BUSINESS_CARD_FOLDER,
    ];

    const FUNCTIONAL_BUSINESS_MAP = [
        self::BUSINESS_TYPE_COMPANY_POOL => PrivilegeConstants::FUNCTIONAL_COMPANY_POOL,
        self::BUSINESS_TYPE_COMPANY_PRIVATE => PrivilegeConstants::FUNCTIONAL_CUSTOMER,
        self::BUSINESS_TYPE_COMPANY_DUPLICATION => PrivilegeConstants::FUNCTIONAL_COMPANY_DUPLICATION_CHECK,
    ];


    const OLD_TYPE_MAP = [
        self::OBJ_PRODUCT => \Constants::TYPE_PRODUCT,
        self::OBJ_PRODUCT_SKU => \Constants::TYPE_PRODUCT,
        self::OBJ_COMPANY => \Constants::TYPE_COMPANY,
        self::OBJ_CUSTOMER => \Constants::TYPE_CUSTOMER,
        self::OBJ_OPPORTUNITY => \Constants::TYPE_OPPORTUNITY,
        self::OBJ_OPPORTUNITY_PRODUCT => \Constants::TYPE_OPPORTUNITY,
        self::OBJ_SUPPLIER => \Constants::TYPE_SUPPLIER,
        self::OBJ_SUPPLIER_PRODUCT => \Constants::TYPE_SUPPLIER,
        self::OBJ_SUPPLIER_CONTACT => \Constants::TYPE_SUPPLIER,
        self::OBJ_SUPPLIER_ACCOUNT => \Constants::TYPE_SUPPLIER,
        self::OBJ_ORDER => \Constants::TYPE_ORDER,
        self::OBJ_ORDER_PRODUCT => \Constants::TYPE_ORDER,
        self::OBJ_ALIBABA_ORDER_PRODUCT => \Constants::TYPE_ALIBABA_ORDER_PRODUCT,
        self::OBJ_PURCHASE_ORDER => \Constants::TYPE_PURCHASE_ORDER,
        self::OBJ_PAYMENT_INVOICE => \Constants::TYPE_PAYMENT_INVOICE,
        self::OBJ_PURCHASE_ORDER_PRODUCT => \Constants::TYPE_PURCHASE_ORDER,
        self::OBJ_PRODUCT_TRANSFER_PURCHASE => \Constants::TYPE_PRODUCT_TRANSFER_PURCHASE,
        self::OBJ_CAPITAL_ACCOUNT => \Constants::TYPE_CAPITAL_ACCOUNT,
        self::OBJ_QUOTATION => \Constants::TYPE_QUOTATION,
        self::OBJ_QUOTATION_PRODUCT => \Constants::TYPE_QUOTATION,
        self::OBJ_INQUIRY_COLLABORATION => \Constants::TYPE_INQUIRY_COLLABORATION,
        self::OBJ_INQUIRY_COLLABORATION_PRODUCT => \Constants::TYPE_INQUIRY_COLLABORATION,
        self::OBJ_INQUIRY_FEEDBACK => \Constants::TYPE_INQUIRY_FEEDBACK,
        self::OBJ_LEAD => \Constants::TYPE_LEAD,
        self::OBJ_LEAD_CUSTOMER => \Constants::TYPE_LEAD_CUSTOMER,
        self::OBJ_PURCHASE_INBOUND_INVOICE => \Constants::TYPE_PURCHASE_INBOUND_INVOICE,
        self::OBJ_OTHER_INBOUND_INVOICE => \Constants::TYPE_OTHER_INBOUND_INVOICE,
        self::OBJ_SALE_OUTBOUND_INVOICE => \Constants::TYPE_SALE_OUTBOUND_INVOICE,
        self::OBJ_SALE_OUTBOUND_INVOICE_RECORD => \Constants::TYPE_SALE_OUTBOUND_INVOICE,
        self::OBJ_OTHER_OUTBOUND_INVOICE => \Constants::TYPE_OTHER_OUTBOUND_INVOICE,
        self::OBJ_OTHER_OUTBOUND_INVOICE_RECORD => \Constants::TYPE_OTHER_OUTBOUND_INVOICE,
        self::OBJ_CASH_COLLECTION => \Constants::TYPE_CASH_COLLECTION,
        self::OBJ_COST_INVOICE => \Constants::TYPE_COST_INVOICE,
        self::OBJ_CASH_COLLECTION_INVOICE => \Constants::TYPE_CASH_COLLECTION_INVOICE,
        self::OBJ_PRODUCT_TRANSFER_PURCHASE_RECORD => \Constants::TYPE_PRODUCT_TRANSFER_PURCHASE,
        self::OBJ_PRODUCT_TRANSFER_INBOUND => \Constants::TYPE_PRODUCT_TRANSFER_INBOUND,
        self::OBJ_PRODUCT_TRANSFER_INBOUND_RECORD => \Constants::TYPE_PRODUCT_TRANSFER_INBOUND,
        self::OBJ_PRODUCT_TRANSFER_OUTBOUND => \Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND,
        self::OBJ_PRODUCT_TRANSFER_OUTBOUND_RECORD => \Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND,
        self::OBJ_PRODUCT_TRANSFER_OTHER => \Constants::TYPE_PRODUCT_TRANSFER_OTHER,
        self::OBJ_SHIPPING_INVOICE => \Constants::TYPE_SHIPPING_INVOICE,
        self::OBJ_SHIPPING_RECORD  => \Constants::TYPE_SHIPPING_INVOICE,
        self::OBJ_FORWARDER  => \Constants::TYPE_FORWARDER,
        self::OBJ_PURCHASE_RETURN_INVOICE => \Constants::TYPE_PURCHASE_RETURN_INVOICE,
        self::OBJ_PLATFORM_PRODUCT => \Constants::TYPE_PLATFORM_PRODUCT_SKU,
        self::OBJ_TRADE_DOCUMENT => \Constants::TYPE_TRADE_DOCUMENT,
        self::OBJ_TMS_SUGGESTION => \Constants::TYPE_TMS_SUGGESTION,
        self::OBJ_TMS_SPEECHCRAFT => \Constants::TYPE_TMS_SPEECHCRAFT,
        self::OBJ_DISK => \Constants::TYPE_DISK,
        self::OBJ_BUSINESS_CARD => \Constants::TYPE_BUSINESS_CARD,
        self::OBJ_FOLLOW_UP => \Constants::TYPE_FOLLOWUP,
    ];

    const REPOSITORY_METADATA = [
        ExtendMetadata::class,
        ProductTransferRelationMetadata::class,
        PaymentInvoiceRecordMetadata::class,
        ProductInventoryMetadata::class,
        CombineProductRelationMetadata::class,
        SystemTranslateMetadata::class,
        UserTranslateMetadata::class,
    ];

    const GET_OBJ_FLAG_UPSTREAM = 2;
    const SUB_OBJ_FLAG_DOWNSTREAM = 1;

    public static function getMetadataByObjectName($objectName):Metadata|null{
        $metadataClass =  self::OBJ_METADATA_MAP[$objectName]??'';
        if(empty($metadataClass)){
            return null;
        }

        return (new $metadataClass);
    }

    // 这里的 $repository 暂时指的是 table_name
    public static function getMetadataByRepository($repository){
        static $tableMap;

        if(is_null($tableMap)){
            $tableMap = [];

            foreach(self::OBJ_METADATA_MAP as $metadataClass){
                $tableMap[$metadataClass::table()] = $metadataClass;
            }

            foreach(self::REPOSITORY_METADATA as $metadataClass){
                $tableMap[$metadataClass::table()] = $metadataClass;
            }
        }

        return $tableMap[$repository] ?? null;
    }
}
