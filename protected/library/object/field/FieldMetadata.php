<?php
/**
 * Created by PhpStorm.
 * User: onegong
 * Date: 2023/11/15
 * Time: 10:00
 */

namespace common\library\object\field;

use xiaoman\orm\metadata\Metadata;

class FieldMetadata extends Metadata
{
    protected $columns = [
        'field_id' => [
            'type' => 'bigint',
            'name' => 'field_id',
            'nullable' => 0,
            'php_type' => 'integer',
            'filter' => [
                'enable' => true,
                'batch' => true,
            ],
        ],
        'client_id' => [
            'type' => 'bigint',
            'name' => 'client_id',
            'nullable' => 0,
            'php_type' => 'integer',
            'filter' => [
                'enable' => true,
                'batch' => true,
            ],
        ],
        //对象标识
        'object_name' => [
            'type' => 'string',
            'name' => 'object_name',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [
                'enable' => true,
            ],
        ],
        //字段
        'field' => [
            'type' => 'string',
            'name' => 'field',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [
                'enable' => true,
            ],
        ],
        //字段名称
        'field_name' => [
            'type' => 'string',
            'name' => 'field_name',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [
                'enable' => true,
            ],
        ],
        // 分组id
        'group_id' => [
            'type' => 'bigint',
            'name' => 'group_id',
            'nullable' => 0,
            'php_type' => 'integer',
            'filter' => [
                'enable' => true,
                'batch' => true,
            ],
        ],
        'field_type' => [
            'type' => 'smallint',
            'name' => 'field_type',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [
                'enable' => true,
                'batch' => true,
            ]
        ],
        'array_flag' => [
            'type' => 'smallint',
            'name' => 'array_flag',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [
                'enable' => true,
                'batch' => true,
            ]
        ],
        'function_type' => [
            'type' => 'smallint',
            'name' => 'function_type',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [
                'enable' => true,
                'batch' => true,
            ]
        ],
        'ext_info' => [
            'type' => 'jsonb',
            'name' => 'ext_info',
            'nullable' => 0,
            'php_type' => 'array',
            'filter' => [
                'enable' => true,
                'json' => true,
            ]
        ],
        'system_type' => [
            'type' => 'smallint',
            'name' => 'system_type',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [
                'enable' => true,
                'batch' => true,
            ]
        ],
        'allow_setting' => [
            'type' => 'smallint',
            'name' => 'allow_setting',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [
                'enable' => true,
                'batch' => true,
            ]
        ],
        'allow_setting_show' => [
            'type' => 'smallint',
            'name' => 'allow_setting_show',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [
                'enable' => true,
                'batch' => true,
            ]
        ],
        'allow_setting_required' => [
            'type' => 'smallint',
            'name' => 'allow_setting_required',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [
                'enable' => true,
                'batch' => true,
            ]
        ],
        'allow_setting_default' => [
            'type' => 'smallint',
            'name' => 'allow_setting_default',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [
                'enable' => true,
                'batch' => true,
            ]
        ],
        'allow_setting_tips' => [
            'type' => 'smallint',
            'name' => 'allow_setting_tips',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [
                'enable' => true,
                'batch' => true,
            ]
        ],
        'required' => [
            'type' => 'smallint',
            'name' => 'required',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [
                'enable' => true,
                'batch' => true,
            ]
        ],
        'default_value' => [
            'type' => 'jsonb',
            'name' => 'default_value',
            'nullable' => 0,
            'php_type' => 'array',
            'filter' => [
                'enable' => true,
                'json' => true,
            ]
        ],
        'tips' => [
            'type' => 'character',
            'name' => 'tips',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [
                'enable' => true,
            ],
        ],
        'enable_flag' => [
            'type' => 'smallint',
            'name' => 'enable_flag',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [
                'enable' => true,
                'batch' => true,
            ]
        ],
        'show_flag' => [
            'type' => 'smallint',
            'name' => 'show_flag',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [
                'enable' => true,
                'batch' => true,
            ]
        ],
        'is_writable' => [
            'type' => 'smallint',
            'name' => 'is_writable',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [
                'enable' => true,
                'batch' => true,
            ]
        ],
        'columns' => [
            'type' => 'jsonb',
            'name' => 'columns',
            'nullable' => 0,
            'php_type' => 'array',
            'filter' => [
                'enable' => true,
                'json' => true,
            ]
        ],
        'web_order' => [
            'type' => 'int',
            'name' => 'web_order',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [
                'enable' => true,
                'batch' => true,
            ]
        ],
        // 创建人
        'create_user' => [
            'type' => 'bigint',
            'name' => 'create_user',
            'nullable' => 0,
            'php_type' => 'integer',
            'filter' => [
                'enable' => true,
                'batch' => true,
            ],
        ],
        // 更新人
        'update_user' => [
            'type' => 'bigint',
            'name' => 'update_user',
            'nullable' => 0,
            'php_type' => 'integer',
            'filter' => [
                'enable' => true,
                'batch' => true,
            ],
        ],
        'create_time' => [
            'type' => 'timestamp',
            'name' => 'create_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [
                'enable' => true,
                'range' => true,
            ]
        ],
        'update_time' => [
            'type' => 'timestamp',
            'name' => 'update_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [
                'enable' => true,
                'range' => true,
            ]
        ],
    ];
    
    public static function table()
    {
        return 'tbl_field';
    }

    public static function dataSource()
    {
        return Metadata::DATA_SOURCE_POSTGRESQL_CLIENT;
    }

    public static function singeObject()
    {
        return Field::class;
    }

    public static function batchObject()
    {
        return BatchField::class;
    }

    public static function objectIdKey()
    {
        return 'field_id';
    }

    public static function filter()
    {
        return FieldFilter::class;
    }

    public static function formatter()
    {
        return FieldFormatter::class;
    }

    public static function operator()
    {
        return FieldOperator::class;
    }
}