<?php

namespace common\library\object\field\formatter\layout_formatter;

use common\library\object\field\field_setting\FieldSetting;
use common\library\object\field\FieldConstant;
use common\library\object\field\formatter\layout_formatter\Factory as LayoutFormatterFactory;

/**
 * Trait LayoutFormatterTraits
 * @package common\library\object\field\formatter\layout_formatter
 */
trait LayoutFormatterTraits
{
    protected $isLayoutFormatter = false;       //是否需要布局格式化

    protected $layoutFields = [];
    protected $objectName = '';
    protected $pageType = '';
    protected $device = '';
    protected $businessType = '';
    protected $customerUnEditEmail = false;
    protected $notFormatLayoutFields = [];      // 有些字段的 _info 在Formatter子类已经格式化好，就不会在这里格式化，否则就会覆盖（如客户列表的金额和日期字段）；但有些字段（如cus_tag）依旧需要在这里再次格式化，这种字段需要设置在本属性中

    public function setLayoutSetting($isLayoutFormatter = false, $pageType = '', $device = '', $businessType = '')
    {
        $this->setIsLayoutFormatter($isLayoutFormatter);
        $this->setPageType($pageType);
        $this->setDevice($device);
        $this->setBusinessType($businessType);
    }

    public function setIsLayoutFormatter(bool $flag)
    {
        $this->isLayoutFormatter = $flag;
    }

    public function addNotFormatLayoutFields($notFormatFields){
        $this->notFormatLayoutFields = array_merge($this->notFormatLayoutFields, $notFormatFields);
    }

    public function setIsLayoutFieldControl(bool $flag)
    {
        $this->isLayoutFieldControl = $flag;
    }

    public function getIsLayoutFormatter()
    {
        return $this->isLayoutFormatter;
    }

    /**
     * 设置布局字段
     *
     */
    public function setLayoutFields(array $layoutFields)
    {
        $this->layoutFields = $layoutFields;
    }

    /**
     * 获取布局字段
     * 规则：
     * 1.先获取自定义的布局字段
     * 2.未定义布局字段，根据objectName+pageType获取默认布局的布局字段
     * 3.未定义布局字段，未设置布局的页面（中间态开发场景， 现如客户联系人列表），获取displayFields的字段作为布局字段格式化
     * */
    public function getLayoutFields()
    {
        $layoutFields = $this->layoutFields;
        if (empty($layoutFields) && $this->pageType) {
            //默认布局
            $layoutApi = new \common\library\layout\LayoutApi($this->clientId, $this->getObjectName(), $this->pageType);
            $objLayoutFields = $layoutApi->getLayoutFields();
            foreach ($objLayoutFields as $fields) {
                $layoutFields = array_merge($layoutFields, $fields);
            }
            $layoutFields = array_filter(array_unique($layoutFields));
        }
        if (empty($layoutFields)) {
            $layoutFields = $this->displayFields;
        }
        $this->layoutFields = $layoutFields;
        return $this->layoutFields;
    }

    public function getObjectName()
    {
        return $this->getMetadata()::objectName();
    }

    public function setPageType($pageType)
    {
        $this->pageType = $pageType;
    }

    public function setDevice($device)
    {
        $this->device = $device;
    }

    public function setBusinessType($businessType)
    {
        $this->businessType = $businessType;
    }

    public function setCustomerUnEditEmail(bool $flag)
    {
        $this->customerUnEditEmail = $flag;
    }

    public function getPageType()
    {
        return $this->pageType;
    }

    public function LayoutFormatter(&$result)
    {
        if (empty($result)) {
            return [];
        }

        //获取字段配置信息+布局字段
        $this->setShowComponent(true);
        $this->fieldSetting();
        $this->getLayoutFields();

        //对每条数据进行布局格式化
        foreach ($result as $key => &$item) {
            $item = $this->LayoutFormatterRow($item);
        }
    }

    public function LayoutFormatterRow($data)
    {
        if (empty($data)) {
            return $data;
        }

        $layoutData = [];
        foreach ($this->layoutFields as $field) {

            $layoutInfoKey = $field . '_info';
            $fieldType = $this->fieldSetting[$field]['field_type'] ?? '';
            /**
             * 1.需要返回组件信息
             * (1)字段类型为数值、百分比、布尔、金额，且字段值为0
             * (3)字段类型field_type=3选择，且选项类型为item_type=1, 且字段值为0
             * (4)字段类型field_type=3选择，且选项类型为选项集、对象时item_type=2，且字段值为0，是否需要返回？统一不返回，但目前客户分组group_id=0时返回未分组，通过配置判断返回
             *
             * 2.无组件信息
             * (1)字段值为null、[]、''时
             * (2)字段值为0，且不为上述情况时，无组件信息
             * (3)字段值为0，且满足上述情况，但在字段组件信息中配置了为0是否返回组件，则根据配置判断，是否返回组件信息，return_component_by_zero=false即无组件信息,不返回, 若return_component_by_zero=true,则返回
             * (4)字段类型field_type=3选择，且选项类型为选项集、对象时item_type=2，且字段值为0，是否需要返回？统一不返回
             */
            if (
                (
                    !(
                        !empty($data[$field])
                        ||
                        (isset($data[$field]) && $data[$field] == 0 && in_array($fieldType, [FieldConstant::TYPE_NUMBER, FieldConstant::TYPE_FLAG, FieldConstant::TYPE_PRICE, FieldConstant::TYPE_PERCENTAGE]))
                        ||
                        (isset($data[$field]) && $data[$field] == 0 && isset($this->fieldSetting[$field]['ext_info']['item_type']) && $this->fieldSetting[$field]['ext_info']['item_type'] == FieldConstant::VALUE_ITEM_TYPE)
                    )
                    &&
                    !(isset($this->fieldSetting[$field]['component']['return_component_by_zero']) && $this->fieldSetting[$field]['component']['return_component_by_zero'])
                )
                ||
                (isset($data[$field]) && $data[$field] == 0 && isset($this->fieldSetting[$field]['component']['return_component_by_zero']) && !$this->fieldSetting[$field]['component']['return_component_by_zero'])
            ) {
                continue;
            }

            //2.功能字段类型为引用、数值、公式时，目前返回字段格式统一以文本格式返回
            $functionType = $this->fieldSetting[$field]['function_type'] ?? 0;
            if (in_array($functionType, [FieldConstant::FUNCTION_TYPE_QUOTE, FieldConstant::FUNCTION_TYPE_FORMULA, FieldConstant::FUNCTION_TYPE_CALCULATE])) {
                $layoutData[$layoutInfoKey] = [
                    FieldSetting::LAYOUT_INFO_VALUE => $data[$field],
                    FieldSetting::LAYOUT_INFO_LABEL => $data[$field],
                ];
                continue;
            }

            //3.不在字段配置中的布局字段，不支持布局格式化
            if (!isset($this->fieldSetting[$field])) {
                continue;
            }


            //4.根据布局component_key返回对应的数据结构
            $fieldLayoutFormatter = LayoutFormatterFactory::make($this->clientId, $this->fieldSetting[$field]);
            if (is_null($fieldLayoutFormatter) || in_array($field, $this->notFormatLayoutFields)) {
                continue;
            }

            $layoutData[$layoutInfoKey] = $fieldLayoutFormatter->formatter($data);

        }

        //原始的info数据用于格式化展示，故而此处layout的info数据需要覆盖原始的info数据
        //留意该处不要使用array_merge,$data中自定义字段的键为整数键，merge会导致重新索引
        $data = $layoutData + $data;

        return $data;
    }

}
