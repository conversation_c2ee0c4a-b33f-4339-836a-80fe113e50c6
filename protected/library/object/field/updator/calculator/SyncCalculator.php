<?php
namespace common\library\object\field\updator\calculator;

use common\library\object\field\FieldConstant;
use common\library\object\field\FieldFilter;
use common\library\object\field\updator\data\DataObjGetter;
use common\library\object\field\updator\data\Selector;
use common\library\object\field\updator\data\SelectTask;
use common\library\object\field\updator\graph\node\FieldNode;
use common\library\object\field\util\FieldTransferUtil;
use common\library\object\field_relation\FieldRelationFilter;
use common\library\object\object_define\Constant;
use common\library\object\object_relation\Helper;
use common\library\object\object_relation\ObjectRelationConstant;
use common\library\object\object_relation\ObjectRelationFilter;

/**
 * 功能字段计算器，用于在同步进程中计算当前对象的功能字段值，支持同时传入主从对象
 * 例如用户创建或者更新某个订单，Order.amount 发生变化会引起 Order.amount_rmb 和 PurchaseOrder.amount 的变化，该 Updator 负责计算 Order.amount_rmb，但不计算 PurchaseOrder.amount
 *
 * 使用场景(支持新建 和 编辑两种场景)：
 * 1.业务对象调用保存 or 新增接口的beforeSave中调用本类；
 * 2.业务对象在详情页的编辑态 or 新建态下修改了某个字段，且这个字段是本对象功能字段的因子时，需要调用功能字段计算接口返回给前端渲染，此时接口需要调用本类
 *
 * 该对象仅支持单个主从对象的计算，而且传入的row需要包含主对象和从对象的所有字段（也就是详情页编辑态/新建态保存的场景）
 * 不支持批量编辑场景下的本对象功能字段计算，批量场景需要依赖pg消费者做关联更新(例如在列表页批量编辑以及导入场景，这些批量场景是不需要在页面上马上看到)
 *
 * @property FieldTransferUtil $fieldTransfer
 */
class SyncCalculator implements CalculatorInterface {
    use SyncCalculatorTrait;
    protected $mainObjPid;      // 主对象id值
    protected $mainObjData=[];     // 一维数组row，row中的字段是业务字段
    protected $mainObjDbData = [];
    protected $subObjData=[];      // 三维数组，第一维是子对象的objName，第二维是rows，第三维度是row
    protected $subObjDbData=[];
    protected $isCustom = false;    // 是否为自定义对象
    protected $scene;
    const DEFAULT_PRIMARY_KEY_ID = -1;

    const SUB_OBJECT_INDEX_KEY = '_index_id';

    public function __construct($clientId, $mainObjName){
        $this->clientId = $clientId;
        $this->mainObjName = $mainObjName;
    }

    // 兼容 db row
    public function setMainDbData($row){
        $this->mainObjDbData = $row;
    }

    public function getClientId(){
        return $this->clientId;
    }

    public function setSubDbData($subObjName, $rows){
        $subPidKey = Constant::OBJ_PRIMARY_KEY_MAP[$subObjName];
        foreach($rows as $k =>$row){
            $key = !empty($row[$subPidKey]) ? $row[$subPidKey] : $k;
            $this->subObjDbData[$subObjName][$key] = $row;
        }
    }

    public function setMainData($row){
        $pidKey = Constant::OBJ_PRIMARY_KEY_MAP[$this->mainObjName];
        // 临时兼容 external_field_data
        foreach($row['external_field_data'] ?? [] as $key => $value){
            if(!isset($row[$key])){
                $row[$key] = $value;
            }
        }
        $this->mainObjData = $row;
//        $this->mainObjData[$pidKey] = $row[$pidKey] ?? self::DEFAULT_PRIMARY_KEY_ID;
        $this->mainObjPid = $row[$pidKey] ?? self::DEFAULT_PRIMARY_KEY_ID;
    }

    public function setSubData($subObjName, $rows){
        $subPidKey = Constant::OBJ_PRIMARY_KEY_MAP[$subObjName];
        $this->subObjData[$subObjName] = [];
        foreach($rows as $k => $row){

            //主键id在同步计算暂不存在应用场景，若使用主键id，需要处理getSubData()获取的数组被batchObject类重新排序的问题
            //$subPid = !empty($row[$subPidKey]) ? $row[$subPidKey] : $k;

            // 临时兼容 external_field_data
            foreach($row['external_field_data'] ?? [] as $key => $value){
                if(!isset($row[$key])){
                    $row[$key] = $value;
                }
            }
            $this->subObjData[$subObjName][$k] = $row;
        }
    }

    public function setDataByPidField($object, $pid, $field, $val){
        if($object == $this->mainObjName){
            $this->setMainDataByPidField($pid, $field, $val);
            return;
        }

        $this->setSubDataByPidField($object, $pid, $field, $val);
    }

    public function setMainDataByPidField($pid, $field, $val){
        if($pid != $this->mainObjPid){
            return;
        }
        $this->mainObjData[$field] = $val;
        $this->getDataObj($this->mainObjName)->setData($pid, $field, $val, true);
    }

    public function setSubDataByPidField($subObjName, $subPid ,$field, $val){
        if(!isset($this->subObjData[$subObjName][$subPid])){
            return;
        }
        $this->subObjData[$subObjName][$subPid][$field] = $val;
        $this->getDataObj($subObjName)->setData($subPid, $field, $val, true);
    }

    public function getSubObjects(){
        return array_keys($this->subObjData);
    }

    public function calculate(){
        $objNames = array_merge(array_keys($this->subObjData), array_keys($this->subObjDbData));
        $objNames[] = $this->mainObjName;
        $this->buildFieldRelations($objNames);

        // 将 db row 转为 业务field row
        if(!empty($this->mainObjDbData)){
            $this->setMainData($this->fieldTransfer->tranDbColumnRow2BizRow($this->mainObjName, $this->mainObjDbData, $this->mainObjDbData));
        }

        if(!empty($this->subObjDbData)){
            foreach($this->subObjDbData as $subObjectName => $subDbData){
                $subData = [];
                foreach($subDbData as $subDatum){
                    $subData[] = $this->fieldTransfer->tranDbColumnRow2BizRow($subObjectName, $subDatum, $subDatum);
                }
                $this->setSubData($subObjectName, $subData);
            }
        }

        if(empty($this->functionalFieldsMap)){       // 说明用户没有设置任何功能字段
            return;
        }

        // 将已有的数据写入到dataObj中
        $dataObj = $this->getDataObj($this->mainObjName);
        $pidKey = Constant::OBJ_PRIMARY_KEY_MAP[$this->mainObjName];
        $dataObj->setData($this->mainObjPid, $pidKey, $this->mainObjPid, true);
        foreach($this->mainObjData as $field => $value){
            $dataObj->setData($this->mainObjPid, $field, $value, true);
        }

        foreach($this->subObjData as $subObjName => $rows){
            $dataObj = $this->getDataObj($subObjName);
            $pidKey = Constant::OBJ_PRIMARY_KEY_MAP[$subObjName];

            foreach($rows as $subObjPid => $row){
                foreach($row as $field => $value){
                    $dataObj->setData($subObjPid, $field, $value, true);
                }
                $dataObj->setData($subObjPid, $pidKey, $subObjPid, true);

                // 将主对象的id设置到子对象的 dataObj 中
                $dataObj->setData($subObjPid, $this->objectRelationsMap[$this->mainObjName.'_'.$subObjName]['relation_field'], $this->mainObjPid, true);
            }
        }

        // 构造查询依赖的数据请求
        $fieldNodeMap = [];     // key是 $objectName.$field
        $adjacency = [];        // 邻接表
        $selectCondMap = $selectTaskMap = [];
        foreach($this->fieldRelations as $fieldRelation){
            $key = $fieldRelation['function_type'] == FieldConstant::FUNCTION_TYPE_CALCULATE ? $fieldRelation['object_name'].'_'.$fieldRelation['relation_object_name'] : $fieldRelation['relation_object_name'].'_'.$fieldRelation['object_name'];

            $fieldNodeKey = $fieldRelation['object_name'].'_'.$fieldRelation['field'];      // 字段节点的唯一标识
            $upstreamFieldNodeKey = $fieldRelation['relation_object_name'].'_'.$fieldRelation['relation_object_field'];
            if(empty($fieldNodeMap[$fieldNodeKey])){
                $fieldNode = new FieldNode($this, $fieldRelation['object_name'], $fieldRelation['field'], $fieldRelation['function_type']);
                $fieldNode->setFieldSetting($this->functionalFieldsMap[$fieldRelation['object_name']][$fieldRelation['field']] ?? []);
                $fieldNodeMap[$fieldNodeKey] = $fieldNode;
            }

            $fieldNode = $fieldNodeMap[$fieldNodeKey];
            $fieldNode->setFactor($fieldRelation['relation_object_name'], $fieldRelation['relation_object_field'], $fieldRelation['foreign_field']);

            if(!isset($adjacency[$fieldNodeKey])){
                $adjacency[$fieldNodeKey] = [];
            }

            if(!isset($adjacency[$upstreamFieldNodeKey])){
                $adjacency[$upstreamFieldNodeKey] = [];
            }

            if($fieldRelation['relation_object_name'] == $this->mainObjName || isset($this->subObjData[$fieldRelation['relation_object_name']])){     // 如果图中的上游对象属于主对象或者从对象，则需要写入邻接表；如果是非主对象或从对象的其他依赖对象，则无需写入邻接表
                $adjacency[$fieldNodeKey][] = $upstreamFieldNodeKey;
            }

            // 统计下游对象依赖的上游对象
            if(isset($this->objectRelationsMap[$key]) && $fieldRelation['foreign_field'] == $this->objectRelationsMap[$key]['relation_field'] || $fieldRelation['function_type'] == FieldConstant::FUNCTION_TYPE_CALCULATE){
                continue;       // 如果主对象/子对象的 功能字段 是依赖 子对象/主对象 的话，那么无需查询db，因为这些数据已经有调用者提供
            }

            if(!isset($selectTaskMap[$fieldRelation['relation_object_name']])){
                $selectTaskMap[$fieldRelation['relation_object_name']] = new SelectTask($fieldRelation['relation_object_name']);   // 不论是汇总还是公式字段，数据源都应该从上游对象所在的表查询
            }

            $selectTask = $selectTaskMap[$fieldRelation['relation_object_name']];
            $selectTask->addSelectFields($fieldRelation['relation_object_field']);
            if(!isset($selectCondMap[$fieldRelation['relation_object_name']])){
                $selectCondMap[$fieldRelation['relation_object_name']] = [];
            }
            if(!isset($selectCondMap[$fieldRelation['relation_object_name']][$fieldRelation['relation_object_relation_field']])){
                $selectCondMap[$fieldRelation['relation_object_name']][$fieldRelation['relation_object_relation_field']] = [];
            }
            $selectCondMap[$fieldRelation['relation_object_name']][$fieldRelation['relation_object_relation_field']] = array_merge($selectCondMap[$fieldRelation['relation_object_name']][$fieldRelation['relation_object_relation_field']], $this->getDataObj($fieldRelation['object_name'])->getDataByFieldPids($fieldRelation['relation_field']));
        }

        //
        $this->selectAffectFields($adjacency);
        // 查询上游依赖字段值
        $selector = new Selector($this->clientId, $this);
        foreach($selectTaskMap as $upstreamObjectName => $selectTask){
            foreach($selectCondMap[$upstreamObjectName] ?? [] as $condField => $condVal){
                $selectTask->addConds($condField, $condVal);
            }

            $selector->addTask($selectTask);
        }
        $selector->execute($this->fieldTransfer);

        // 字段拓扑排序 & 计算
        list($hasCycle, $sortResult) = \Util::topoSort($adjacency);
        if($hasCycle){
            throw new \RuntimeException("计算功能字段错误，字段存在循环引用");
        }
        foreach($sortResult as $sortNodeKey){
            if(!isset($fieldNodeMap[$sortNodeKey])){        // 说明该节点已经遍历过了 或者 该nodeKey对应的是不需要计算的普通字段
                continue;
            }

            /** @var FieldNode $fieldNode */
            $fieldNode = $fieldNodeMap[$sortNodeKey];
            $fieldNode->calculate();
            unset($fieldNodeMap[$sortNodeKey]);
        }
    }

    // 供调用侧获取计算好的主对象数据
    public function getMainData($ignoreNormalField = false){
        if($ignoreNormalField){
            $targetData = [];
            foreach($this->mainObjData as $field => $value){
                if($this->fieldTransfer->isFunctionBizField($this->mainObjName, $field) || ($this->mergeFormulaDefaultValueField && $this->fieldTransfer->isFormulaDefaultValueField($this->mainObjName, $field))){
                    $targetData[$field] = $value;
                }
            }
            return $targetData;
        }
        return $this->mainObjData;
    }

    // 供调用侧获取计算好的从对象数据
    public function getSubData($subObjName, $ignoreNormalField = false){
        if($ignoreNormalField){
            $targetRows = [];
            foreach($this->subObjData[$subObjName] ?? [] as $row){
                $targetData = [];
                foreach($row as $field => $value){
                    if($this->fieldTransfer->isFunctionBizField($subObjName, $field) || ($this->mergeFormulaDefaultValueField && $this->fieldTransfer->isFormulaDefaultValueField($subObjName, $field))){
                        $targetData[$field] = $value;
                    }
                }
                $targetRows[] = $targetData;
            }
            return $targetRows;
        }
        return $this->subObjData[$subObjName] ?? [];
    }

    public function getMainDbData($ignoreNormalField = false){
        $mainObjData = $this->mainObjData;
        $mainObjDbData = $this->mainObjDbData;

        if($ignoreNormalField){
            $mainObjDbData = [];
            foreach($mainObjData as $field => $value){
                if(!$this->fieldTransfer->isFunctionBizField($this->mainObjName, $field) && !($this->mergeFormulaDefaultValueField && $this->fieldTransfer->isFormulaDefaultValueField($this->mainObjName, $field))){
                    unset($mainObjData[$field]);
                }
            }
        }
        return $this->fieldTransfer->tranBizRow2DbColumnRow($this->mainObjName, $mainObjData, $mainObjDbData);
    }

    public function getSubDbData($subObjName, $ignoreNormalField = false){
        if(!isset($this->subObjData[$subObjName])){
            return [];
        }

        $result = [];
        foreach($this->subObjData[$subObjName] as $subPid => $subDatum){
            if($ignoreNormalField){ // 是否忽略非功能字段
                foreach($subDatum as $field => $value){
                    if(!$this->fieldTransfer->isFunctionBizField($subObjName, $field) && !($this->mergeFormulaDefaultValueField && $this->fieldTransfer->isFormulaDefaultValueField($subObjName, $field))){
                        unset($subDatum[$field]);
                    }
                }
            }

            $row = $this->fieldTransfer->tranBizRow2DbColumnRow($subObjName, $subDatum, $this->subObjDbData[$subObjName][$subPid]??[]);

            $result[] = $row;
        }
        return $result;
    }

    public function clear(){
        $this->dataMap = $this->mainObjData = $this->subObjData = [];
    }

    public function setObjNameToModifiedFieldsMap(array $objNameToModifiedFieldsMap) : void
    {
        $this->objNameToModifiedFieldsMap[$this->mainObjName][$this->mainObjPid] = $objNameToModifiedFieldsMap[$this->mainObjName][$this->mainObjPid] ?? [];

        //子对象不区分
        foreach ($this->subObjData as $objectName => $subObjs) {
            if (empty($objNameToModifiedFieldsMap[$objectName])) {
                continue;
            }
            foreach ($subObjs as $k => $subObj) {
                $indexKey = $subObj[self::SUB_OBJECT_INDEX_KEY] ?? $k;
                $this->objNameToModifiedFieldsMap[$objectName][$k] = $objNameToModifiedFieldsMap[$objectName][$indexKey] ?? [];
            }
        }

    }

    //用户行为变更的字段通过邻接表去找出影响的字段
    public function selectAffectFields($adjacency) : void
    {
        if (!$this->mergeFormulaDefaultValueField || empty($this->objNameToModifiedFieldsMap)) {
            return ;
        }

        $factorToDownFieldsMap = [];
        foreach ($this->fieldRelations as $relation)
        {
            $factorKey = $relation['relation_object_name'] . '_' . $relation['relation_object_field'];
            $factorRelationKey = $relation['object_name'] . '_' . $relation['relation_field'];
            $factorToDownFieldsMap[$factorKey][] = $relation['object_name'] . '_' . $relation['field'];
            $factorToDownFieldsMap[$factorRelationKey][] = $relation['object_name'] . '_' . $relation['field'];
            if (
                !empty($relation['function_type'])
                && $relation['function_type'] == FieldConstant::FUNCTION_TYPE_CALCULATE
                && !empty($relation['trigger_fields'])
            ) {
                $trigger_fields = is_array($relation['trigger_fields']) ? $relation['trigger_fields'] : json_decode($relation['trigger_fields'], true);
                foreach ($trigger_fields as $trigger_field) {
                    $factorToDownFieldsMap[$relation['relation_object_name'] . '_' . $trigger_field][] = $relation['object_name'] . '_' . $relation['field'];
                }
            }
        }

        //计算每一个对象变更的字段会影响对象哪些字段
        $publicObjectAffectFields = [];
        foreach ($this->objNameToModifiedFieldsMap as $curObjName => $indexToFields) {
            foreach ($indexToFields as $pid => $fields) {
                if (empty($fields)) {
                    continue;
                }
                $modifiedFieldToAffectFieldsMap = [];
                foreach ($fields as $field) {
                    $pointer = $curObjName . '_' . $field;
                    $pointerAffectFields = [$pointer];
                    $visitedKey = [];
                    $this->iterStack($pointerAffectFields, $visitedKey, $factorToDownFieldsMap, 0);
                    $modifiedFieldToAffectFieldsMap[$pointer] = array_unique(array_diff($pointerAffectFields,[$pointer]));
                }

                foreach ($modifiedFieldToAffectFieldsMap as $pointer => &$affectFields)
                {
                    $traceExistOtherObj = false;
                    foreach ($affectFields as $field) {
                        $objectName = explode('_', $field)[0];
                        // 如果影响的字段路径中存在其他对象，那么当路径回到当前对象则为当前对象都被影响的字段
                        if ($objectName != $curObjName) {
                            $traceExistOtherObj = true;
                        }
                        if ($objectName == $curObjName) {
                            //过滤掉当前对象影响的其他对象字段
                            $this->objToAffectFields[$curObjName][$pid][] = $field;
                        }
                        if ($traceExistOtherObj) {
                            $publicObjectAffectFields[$objectName][] = $field;//karonyang
                        }
                    }
                }
            }
        }

        //合并对象都需要计算的字段
        if (isset($publicObjectAffectFields[$this->mainObjName])) {
            $this->objToAffectFields[$this->mainObjName][$this->mainObjPid] = array_unique(array_merge($publicObjectAffectFields[$this->mainObjName], $this->objToAffectFields[$this->mainObjName][$this->mainObjPid]??[]));
        }

        foreach ($this->subObjData as $objName => $subDataList) {
            if (empty($publicObjectAffectFields[$objName])) {
                continue;
            }
            foreach ($subDataList as $index => $subData) {
                $this->objToAffectFields[$objName][$index] = array_unique(array_merge($publicObjectAffectFields[$objName], $this->objToAffectFields[$objName][$index]??[]));
            }
        }


        foreach ($this->objToAffectFields ?? [] as $objName => $pidToFields)
        {
            foreach ($pidToFields as $pid => $affectFields)
            {
                //对每个字段，需要去除前缀object_name
                foreach ($affectFields as &$field) {
                    $field = str_replace("{$objName}_", '' , $field);
                }
                $this->objToAffectFields[$objName][$pid] = $affectFields;
                $this->objNameToAffectFields[$objName] = array_merge($affectFields, $this->objNameToAffectFields[$objName] ?? []);
            }
        }

        foreach ($this->objNameToAffectFields ?? [] as &$affectFields) {
            $affectFields = array_unique($affectFields);
        }

    }
}
