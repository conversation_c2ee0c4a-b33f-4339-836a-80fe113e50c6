<?php

namespace common\library\object\field\field_setting;

use common\library\privilege_v3\PrivilegeConstants;
use common\library\object\object_define\Constant;

class SupplierFieldSetting extends FieldSetting
{
    public function __construct($clientId, $objectName)
    {
        parent::__construct($clientId, $objectName);
        $this->quotable = true;
        $this->quoteOther = false;
    }

    public static function quotableFields()
    {
        return [
            "user_ids" => true,         //跟进人
            "update_user" => true,      //修改人
            "update_time" => true,      //修改时间
            "supplier_id" => false,      //供应商id
            "supplier_no" => true,      //供应商编号
            "remark" => true,      //备注
            "rate_id" => true,      //供应商评级
            "name" => true,      //供应商名称
            "homepage" => true,      //主页
            "delivery_date" => true,      //参考交期
            "create_time" => true,      //创建时间
            "attachments" => true,      //附件
            "archive_user" => true,      //创建人
            "address" => true,      //供应商地址
            "bank_name" => false,      //收款银行
            "bank_account" => false,      //收款账号
            "account_name" => false,      //收款户名
            "is_main_flag" => false,      //主资金账户标识
        ];
    }

    public static function searchFields()
    {
        return [
            self::FAST_SEARCH_TYPE => [
                Constant::OBJ_SUPPLIER => [
                    'supplier_keyword',
                    'rate_id',
                    'user_ids'
                ]
            ],
            self::COMMON_SEARCH_TYPE => [
                Constant::OBJ_SUPPLIER => [
                    'archive_user',
                    'update_user',
                    'delivery_date',
                    'create_time',
                    'update_time'
                ]
            ],
            self::ALL_SEARCH_TYPE => [
                Constant::OBJ_SUPPLIER => [
                    'supplier_keyword',
                    'rate_id',
                    'user_ids',
                    'archive_user',
                    'update_user',
                    'delivery_date',
                    'create_time',
                    'update_time'
                ]
            ]
        ];
    }

    public static function searchComponents()
    {
        return [
            'supplier_keyword' => [
                'search_field' => 'supplier_keyword',
                'search_field_name' => '供应商',
                'component_key' => self::TEXT_COMPONENT,
                'component_props' => [
                    'placeholder' => '请输入供应商编号/名称',
                ],
            ],
            'name' => [
                'search_field' => 'supplier_keyword',
                'component_key' => self::TEXT_COMPONENT,
            ],
            'rate_id' => [              //供应商评级
                'component_key' => self::SUPPLIER_RATE_SELECT,
            ],
            'user_ids' => [
                'search_field' => 'user_id',
                'component_key' => self::OMS_DEPARTMENT_USER_COMPONENT,
                'component_props' => [
                    'mode' => 'multiple',
                    'params' => [
                        'show_member' => 1,
                        'permission' => [PrivilegeConstants::PRIVILEGE_CRM_SUPPLIER_VIEW],
                    ]
                ],
            ],
            'archive_user' => [
                'component_key' => self::OMS_DEPARTMENT_USER_COMPONENT,
                'component_props' => [
                    'mode' => 'multiple',
                    'params' => [
                        'show_member' => 1,
                        'permission' => [PrivilegeConstants::PRIVILEGE_CRM_SUPPLIER_VIEW],
                    ]
                ],
            ],
            'update_user' => [
                'component_key' => self::OMS_DEPARTMENT_USER_COMPONENT,
                'component_props' => [
                    'mode' => 'multiple',
                    'params' => [
                        'show_member' => 1,
                        'permission' => [PrivilegeConstants::PRIVILEGE_CRM_SUPPLIER_VIEW],
                    ]
                ],
            ],
            'delivery_date' => [
                'component_key' => self::SINGLE_SELECTOR_COMPONENT,
                'component_props' => [
                    'options_source' => self::OPTIONS_SOURCE,
                ],
            ],
            'create_time' => [
                'component_key' => self::DATE_RANGE_COMPONENT,
            ],
            'update_time' => [
                'component_key' => self::DATE_RANGE_COMPONENT,
            ],
        ];
    }

}