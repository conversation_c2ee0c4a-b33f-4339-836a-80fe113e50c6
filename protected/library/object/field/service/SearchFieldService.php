<?php

namespace common\library\object\field\service;


use common\library\custom_field\CustomFieldService;
use common\library\customer\Helper;
use common\library\object\field\field_setting\CompanyFieldSetting;
use common\library\object\field\field_setting\FieldSetting;
use common\library\object\field\field_setting\FieldSettingFactory;
use common\library\object\field\FieldConstant;
use common\library\object\field\FieldFilter;
use common\library\object\field\Helper as FieldHelper;
use common\library\object\field_relation\FieldRelationFilter;
use common\library\object\field_relation\service\OriginFieldService;
use common\library\object\object_define\Constant as ObjConstant;
use common\library\setting\library\origin\OriginApi;
use common\library\translate_v2\task\FieldTranslateTask;
use common\library\translate_v2\TranslateConstants;
use common\library\translate_v2\TranslateService;
use common\library\privilege_v3\object_service\UserObjectPrivilegeService;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeFieldV2;
use common\library\util\Arr;
use xiaoman\orm\database\data\InArray;
use xiaoman\orm\database\data\DateRange;
use xiaoman\orm\database\data\Range;

class SearchFieldService
{

    public $clientId;
    public $objectName;
    public $functionalId;
    private $searchRule = [];    //搜索字段规则
    protected $operatorMap = [];    //搜索字段操作符
    protected $searchType = null;   //搜索类型
    protected $baseFieldList = [];   //系统字段列表信息
    protected $operateUser;
    protected ?FieldSetting $baseFieldSetting = null;

    //搜索类型
    const COMMON_SEARCH_TYPE = 1;   //普通搜索
    const FAST_SEARCH_TYPE = 2;     //快捷搜索
    const ADVANCED_SEARCH_TYPE = 3; //高级搜索
    const SEARCH_SETTING_EXT_INFO = 'search_setting_ext_info';  //用户自定义搜索字段配置，字段额外信息

    public function __construct($clientId, $objectName)
    {
        $this->clientId = $clientId;
        $this->objectName = $objectName;

        $this->baseFieldSetting = FieldSettingFactory::make($clientId, $objectName);
    }

    public function setOperateUser($user){
        $this->operateUser = $user;
    }

    public function setSearchType($searchType)
    {
        $this->searchType = $searchType;
    }

    protected function getOperatorMap()
    {
        if (empty($this->operatorMap)) {
            $fieldTypeOperatorMap = FieldConstant::FIELD_TYPE_OPERATOR_MAP;
            $operatorMap = [];
            foreach ($fieldTypeOperatorMap as $fieldType => $fieldTypeMap) {
                foreach ($fieldTypeMap as $arrayFlag => $operators) {
                    $firstElement = reset($operators);
                    foreach ($operators as $operator) {
                        $operatorElement = [
                            'key' => $operator,
                            'name' => \Yii::t('workflow', 'filter_operator.' . $operator),
                        ];
                        if ($firstElement == $operator) {
                            $operatorElement['is_default'] = true;
                        }
                        $operatorMap[$fieldType][$arrayFlag][] = $operatorElement;
                    }

                }
            }
            $this->operatorMap = $operatorMap;
        }
        return $this->operatorMap;
    }
    
    public function setFunctionalId($functionalId){
        $this->functionalId = $functionalId;
    }

    public function getFunctionalId(){
        if(!$this->functionalId){
            if($this->objectName == ObjConstant::OBJ_COMPANY){
                throw new \RuntimeException("获取可筛选字段集合请调用 SearchFieldService::setFunctionId() 设置 functionalId");
            }
            $this->functionalId = ObjConstant::FUNCTIONAL_MAP[$this->objectName];
        }

        return $this->functionalId;
    }

    public function getViewUserId(){
        return \User::getLoginUser()->getUserId();
    }
    
    public function searchFieldList($show_field_key='')
    {
        $baseSearchFieldList = $this->baseSearchFieldList();
        $selfCustomSearchField = $this->selfCustomSearchFieldList();
        $linkSearchField = $this->linkSearchFieldList();
        $functionSearchFieldList = $this->functionSearchFieldList();
//        $searchFieldList = array_merge($baseSearchFieldList, $selfCustomSearchField, $functionSearchFieldList);
        $searchFieldList = array_merge($baseSearchFieldList, $selfCustomSearchField, $linkSearchField, $functionSearchFieldList);

        // 字段权限过滤
        $objectNames = array_unique(array_column($searchFieldList, 'object_name'));
        foreach($objectNames as $objectName){
            if($objectName == ObjConstant::OBJ_COMPANY || $objectName == ObjConstant::OBJ_CUSTOMER){      // 客户模块的字段权限分公私海，需要临时兼容
                $functionalId = CompanyFieldSetting::getFunctionalIdByUserSettingKey($show_field_key);
                $this->setFunctionalId($functionalId);
                $fieldPrivileges = \common\library\privilege_v3\Helper::getFieldPrivilegeByObjName($this->operateUser, $objectName, $functionalId);
            }else{
                $fieldPrivileges = \common\library\privilege_v3\Helper::getFieldPrivilegeByObjName($this->operateUser, $this->objectName);
            }

            $disableFields = $fieldPrivileges['disable'] ?? [];
            $searchDisableFields = $this->baseFieldSetting::searchDisableFields();
            foreach($searchFieldList as $k => $searchField){
                if($searchField['object_name'] != $objectName){
                    continue;
                }

                if (in_array($show_field_key, [\common\library\setting\user\UserSetting::CUSTOMER_COMMON_SEARCH_FILTER, \common\library\setting\user\UserSetting::CUSTOMER_COMMON_SEARCH_FILTER_V2, \common\library\setting\user\UserSetting::CUSTOMER_PUBLIC_COMMON_SEARCH_FILTER, \common\library\setting\user\UserSetting::CUSTOMER_PUBLIC_COMMON_SEARCH_FILTER_V2]) && $searchField['object_name'] == ObjConstant::OBJ_OPPORTUNITY && $searchField['field'] == 'stage') {
                    $searchFieldList[$k]['name'] = \Yii::t('field', '商机阶段');
                }

                // 这里会特殊处理一些不受字段权限控制的字段
                // $searchField['ext_info']['children_field'] ?? []; 这种情况没有处理 如果遇到需要额外处理
                if (in_array($searchField['field'], $searchDisableFields[$searchField['object_name']] ?? [])) {
                    continue;
                }

                if(!$searchField['show_flag']){
                    unset($searchFieldList[$k]);
                }

                $childFields = $searchField['ext_info']['children_field'] ?? [];
                if(in_array($searchField['field'], $disableFields) || array_intersect($childFields, $disableFields)){
                    unset($searchFieldList[$k]);
                }
            }
        }

        return array_values($searchFieldList);
    }

    /**
     * 搜索字段列表
     * @return array
     */
    public function allSearchField($showFieldKey = '')
    {
        $searchFieldList = $this->searchFieldList($showFieldKey);

        if ($this->objectName == ObjConstant::OBJ_COMPANY) {
            $searchFieldGroupData = \common\library\object\field\Helper::formatCompanyFieldGroup($this->clientId, $this->objectName, FieldConstant::SEARCH_SCENE, $searchFieldList, $showFieldKey);
        } else {
            $searchFieldGroupData = \common\library\object\field\Helper::formatFieldGroup($this->clientId, $this->objectName, $searchFieldList);
        }

        $baseFields = [];
        foreach ($this->baseFieldList as $datum) {
            $baseFields = array_merge($baseFields, array_column($datum, 'field'));
        }

        foreach ($searchFieldGroupData as $key => $datum) {
            foreach ($datum['field_list'] ?? [] as $fieldKey => $item) {
                $searchFieldGroupData[$key]['field_list'][$fieldKey] = [
                    'object_name' => $item['object_name'],
                    'field' => $item['field'],
                    'name' => $item['name'],
                    'name'.TranslateConstants::FORMAT_KEY_INFO_SUFFIX => $item['name'.TranslateConstants::FORMAT_KEY_INFO_SUFFIX] ?? TranslateService::formatValue($item['name'], $item['name']),
                    'is_default_search' => in_array($item['field'], $baseFields) ? true : false,
                    'field_type' => $item['field_type'],
                    'array_flag' => $item['array_flag'],
                    'system_type' => $item['system_type'],
                    'operators' => $item['operators'],
                    'component_key' => $item['children'][0]['component_key'] ?? '',
                    'component_props' => $item['children'][0]['component_props'] ?? [],
                ];
            }
        }
        return $searchFieldGroupData;
    }

    public function noFieldBaseSearchField($type, $userId, $scene)
    {
        $searchFieldList = [];
        $customFieldData = array_column(Helper::getFields($type, $this->clientId, $userId, $scene), null, 'id');

        list($objNames, $baseSearchFields, $baseSearchComponents) = $this->baseSearchComponents();

        foreach ($baseSearchComponents as $searchObjectName => $searchObjectItem) {
            foreach ($searchObjectItem as $searchField => $searchRule) {
                $fieldInfo = $customFieldData[$searchField] ?? [];
                $name = $fieldInfo['name'] ?? ($searchRule['search_name'] ?? '');
                $field = $fieldInfo['id'] ?? $searchField;
                $searchGroup = $searchRule['search_group'] ?? null;
                unset($searchRule['search_name']);

                $component_props =  $searchRule['component_props'] ?? [];
                unset($searchRule['component_props']);
                if(isset($component_props['placeholder'])){
                    $component_props['placeholder'] = \Yii::t('field',$component_props['placeholder']);
                }

                $searchFieldRule = [
                    'children' => [
                        array_merge([
                            'field' => 'value',
                            'name' => \yii::t('field', $name),
                            'component_key' => $searchRule['component_key'],
                            'component_props' => $component_props,
                        ], $searchRule)
                    ],
                ];

                $searchField = [
                    'field' => $field,
                    'name' => \yii::t('field', $name),
                    'search_group' => $searchGroup,
                    'component_key' => '',
                    'component_props' => $component_props,
                ];
                $searchField = array_merge($searchField, $searchFieldRule);
                if (isset($searchField['search_filed'])) {
                    unset($searchField['search_filed']);
                }
                $searchFieldList[] = $searchField;
            }
        }
        return [$searchFieldList, $customFieldData];
    }

    public function noFieldAllSearchField($type, $userId, $scene)
    {
        list($searchFieldList, $customFieldData) = $this->noFieldBaseSearchField($type, $userId, $scene);

        $searchFieldGroup = [];
        foreach ($searchFieldList as $item) {
            $fieldGroupId = $customFieldData[$item['field']]['group_id'] ?? ($item['search_group'] ?? 0);
            if (empty($fieldGroupId)) {
                continue;
            }
            $searchFieldGroup[$fieldGroupId][] = array_replace($item, [
                'field' => $item['field'],
                'name' => $item['name'],
                'is_default_search' => (($customFieldData[$item['field']]['base'] ?? 0) == 1) ? true : false,
            ]);
        }

        $searchField = [];
        foreach ($searchFieldGroup as $fieldGroupId => $groupItem) {
            if (empty($groupItem)) {
                continue;
            }
            $searchField[$fieldGroupId] = [
                'group_id' => $fieldGroupId,
                'name' => \yii::t('field', CustomFieldService::GROUP_MAP[$type][$fieldGroupId]['name']),
                'field_list' => $groupItem,
            ];
        }
        ksort($searchField);
        return array_values($searchField);
    }

    public function baseSearchComponents()
    {
        $baseFieldSetting = $this->baseFieldSetting;
        $baseSearchFields = $baseFieldSetting->searchFields()[$baseFieldSetting::ALL_SEARCH_TYPE] ?? [];
        if (empty($baseSearchFields)) {
            return [[],[],[]];
        }
        $baseSearchComponents = $objNames = [];
        foreach ($baseSearchFields as $objName => $objNameSearchFields) {
            $fieldSetting = FieldSettingFactory::make($this->clientId, $objName);
            $objNameSearchComponents = $fieldSetting->searchComponents();
            foreach ($objNameSearchFields as $objNameSearchField) {
                if (isset($objNameSearchComponents[$objNameSearchField])) {
                    $baseSearchComponents[$objName][$objNameSearchField] = $objNameSearchComponents[$objNameSearchField];
                    $objNames[] = $objName;
                }
            }
        }
        $objNames = array_unique($objNames);
        return [$objNames, $baseSearchFields, $baseSearchComponents];
    }

    /**
     * 触发对象的搜索字段（系统字段）
     * 目前以白名单方式获取
     * @return array
     */
    public function baseSearchFieldList()
    {
        //获取触发对象的搜索字段
        $baseSearchComponents = $this->baseSearchComponents();
        if (empty($baseSearchComponents)) {
            return [];
        }
        list($objNames, $baseSearchFields, $baseSearchComponents) = $baseSearchComponents;

        $fieldFilter = new FieldFilter($this->clientId);
        $fieldFilter->object_name = $objNames;
        $fieldFilter->function_type = FieldConstant::FUNCTION_TYPE_NORMAL;
        $fieldFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $fieldFilter->system_type = FieldConstant::SYSTEM_TYPE_BASE;
        $fieldFilter->select(['object_name', 'field', 'field_name', 'ext_info', 'field_type', 'array_flag', 'system_type','show_flag']);
        $fieldList = $fieldFilter->rawData();
        foreach ($fieldList as $item) {
            $this->baseFieldList[$item['object_name']][$item['field']] = $item;
        }

        $baseSearchFieldList = [];
        foreach ($baseSearchComponents as $searchObjectName => $searchObjectItem) {
            foreach ($searchObjectItem as $searchField => $searchRule) {
                if (isset($searchRule['component_props']['options_source']) && $searchRule['component_props']['options_source'] == FieldSetting::OPTIONS_SOURCE) {
                    $searchRule['component_props']['options'] = $this->baseFieldList[$searchObjectName][$searchField]['ext_info']['value'] ?? [];
                    unset($searchRule['component_props']['options_source']);
                }

                $fieldInfo = $this->baseFieldList[$searchObjectName][$searchField] ?? [];

                if (in_array($this->objectName, [
                        objConstant::OBJ_QUOTATION,
                        objConstant::OBJ_QUOTATION_PRODUCT,
                    ]) && !empty($fieldInfo)) {
                    $fieldInfo['field_name'] = \yii::t('quotation', $fieldInfo['field_name']);
                }

                if (empty($fieldInfo)) {
                    if (!isset($searchRule['search_field_name'])) {
                        continue;
                    }
                    //搜索字段不在字段表中，
                    $fieldInfo = [
                        'object_name' => $searchObjectName,
                        'field' => $searchField,
                        'field_name' => $searchRule['search_field_name'],
                        'field_type' => $searchRule['search_field_type'] ?? '',
                        'array_flag' => $searchRule['search_array_flag'] ?? FieldConstant::IS_NOT_ARRAY,
                        'show_flag' => 1,
                        'system_type' => $searchRule['search_system_type'] ?? FieldConstant::SYSTEM_TYPE_CUSTOM,
                    ];
                }

                $baseSearchFieldList[] = $this->searchFieldComponentInfo($fieldInfo, $searchRule['component_key'], $searchRule);
            }
        }

        return $baseSearchFieldList;
    }

    /**
     * 本对象自定义非功能字段搜索字段
     * 目前以白名单方式获取
     * @return array
     */
    public function selfCustomSearchFieldList()
    {
        //默认返回本对象的自定义字段
        $objNames[] = $this->objectName;

        //客户列表需要返回从对象的自定义字段
        if ($this->objectName == ObjConstant::OBJ_COMPANY) {
            $objNames = array_merge($objNames, \common\library\object\object_relation\Helper::getSubObjectNames($this->objectName));
        }

        $fieldFilter = new FieldFilter($this->clientId);
        $fieldFilter->object_name = $objNames;
        $fieldFilter->function_type = FieldConstant::FUNCTION_TYPE_NORMAL;
        $fieldFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $fieldFilter->system_type = FieldConstant::SYSTEM_TYPE_CUSTOM;
        $fieldFilter->select(['object_name', 'field', 'field_name', 'field_type', 'array_flag', 'ext_info', 'system_type','show_flag']);
        $fieldList = $fieldFilter->rawData();

        $task = new FieldTranslateTask($this->clientId);

        $task->prepare($fieldList);
        
        array_walk($fieldList, function (&$item) use ($task) {

            $task->setReferenceData($item);

            $task->run($item);
        });

        $fieldSetting = $this->baseFieldSetting;

        $selfCustomSearchFieldList = [];
        foreach ($fieldList as $item) {
            
            $componentKey = $fieldSetting::FIELD_TYPE_SEARCH_COMPONENT_MAP[$item['field_type']] ?? null;
            if (empty($componentKey)) {
                //未定义组件的字段类型，过滤不搜索
                continue;
            }

            $searchRule = [];
            switch ($item['field_type']) {
                case FieldConstant::TYPE_TEXT:
                case FieldConstant::TYPE_TEXTAREA:
                    $searchRule = FieldSetting::getTextFieldDefaultComponentRule();
                    $componentKey = $fieldSetting::DIV_COMPONENT;
                    $selfCustomSearchFieldList[] = $this->componentInfo($item, $componentKey, $searchRule);
                    break;
                case FieldConstant::TYPE_SELECT:
                    $searchRule['component_props'] = [
                        'options' => $item['ext_info']['value'] ?? [],
                        'options'.TranslateConstants::FORMAT_KEY_INFO_SUFFIX => $item['ext_info'.TranslateConstants::FORMAT_KEY_INFO_SUFFIX] ?? [],
                        'mode' => 'multiple',
                    ];
                    $selfCustomSearchFieldList[] = $this->searchFieldComponentInfo($item, $componentKey, $searchRule);
                    break;
                default :
                    $selfCustomSearchFieldList[] = $this->searchFieldComponentInfo($item, $componentKey, $searchRule);
                    break;
            }

        }
        return $selfCustomSearchFieldList;
    }

    /**
     * 功能搜索字段列表
     * @return array
     */
    public function functionSearchFieldList()
    {
        //自定义功能字段搜索列表
        $functionFieldList = FieldHelper::getCustomFieldFunctionField($this->clientId, $this->objectName);
        if (empty($functionFieldList)) {
            return [];
        }

        //功能字段顶级字段信息
        $quoteFieldList = [];
        foreach ($functionFieldList as $functionField) {
            if ($functionField['function_type'] == FieldConstant::FUNCTION_TYPE_QUOTE) {
                $quoteFieldList[] = $functionField['field'];
            }
        }
        $quoteFieldLis[$this->objectName] = $quoteFieldList;
        $originFieldService = new OriginFieldService($this->clientId);
        $originFieldMap = $originFieldService->getQuoteOriginField($quoteFieldLis);

        $originObjectName = [];
        foreach ($originFieldMap as $originItem) {
            foreach ($originItem as $originFieldItem) {
                $originObjectName[] = $originFieldItem['relation_origin_object_name'];
            }
        }
        $originObjectName = array_filter(array_unique($originObjectName));

        $originFieldData = [];
        if (!empty($originObjectName)) {
            $originFieldFilter = new FieldFilter($this->clientId);
            $originFieldFilter->object_name = $originObjectName;
            $originFieldFilter->select(['object_name', 'field', 'field_name', 'field_type', 'system_type', 'ext_info', 'system_type','show_flag']);
            $originFieldList = $originFieldFilter->rawData();
            foreach ($originFieldList as $originFieldItem) {
                $originFieldData[$originFieldItem['object_name']][$originFieldItem['field']] = $originFieldItem;
            }
        }

        //功能字段搜索数据组件信息
        $extFieldDbTypeConfig = FieldHelper::getExtFieldConfig();
        $functionSearchFields = [];
        foreach ($functionFieldList as $functionField) {
            //未建立索引的字段，不支持搜索
            //todo 先允许搜索字段搜索，测试完，再放开该处限制
//            if (!in_array($functionField['columns']['data_key'], $extFieldDbTypeConfig[$functionField['columns']['data_db_type']]['index'])) {
//                continue;
//            }

            $originObjectName = $originFieldMap[$this->objectName][$functionField['field']]['relation_origin_object_name'] ?? '';
            $originField = $originFieldMap[$this->objectName][$functionField['field']]['relation_origin_object_field'] ?? '';
            $originFieldType = $originFieldData[$originObjectName][$originField]['field_type'] ?? '';
            $originSystemType = $originFieldData[$originObjectName][$originField]['system_type'] ?? '';

            // todo 客户列表paas项目未正式上线前，新字段筛选组件不启用
            if ($this->objectName == 'objInquiryCollaboration' && $originObjectName == 'objCompany' && $originField == 'annual_procurement') {
               continue;
            }

            switch ($functionField['columns']['data_db_type']) {
                //1.numeric数值类型使用数值范围查询
                case FieldConstant::DB_TYPE_NUMERIC:
                    $functionSearchFields[] = $this->searchFieldComponentInfo($functionField, FieldSetting::NUMBER_RANGE_COMPONENT);
                    break;
                //2.timestamp时间类型使用时间范围查询
                case FieldConstant::DB_TYPE_TIMESTAMP:
                    $functionSearchFields[] = $this->searchFieldComponentInfo($functionField, FieldSetting::DATE_RANGE_COMPONENT);
                    break;
                //3.varchar、array_varchar类型，引用顶级字段为
                // 系统字段且为下拉单选：根据配置规则中字段返回组件key，其余不搜索
                // 系统字段且为其他类型：根据配置规则中字段返回组件key，其余返回文本框组件key
                // 系统字段且引用了OBJ_ORDER_PRODUCT的product_image字段，不搜索
                // 自定义字段且下拉单选：返回下拉框组件
                // 自定义字段且文本类型：返回文本框组件
                // 自定义字段且其他：不搜索
                case FieldConstant::DB_TYPE_VARCHAR:
                    $componentKey = '';
                    $searchRule = [];
                    if ($originSystemType == FieldConstant::SYSTEM_TYPE_BASE) {
                        $searchRule = $this->getFieldSearchRule($originObjectName, $originField);

                        if ($originFieldType == FieldConstant::TYPE_SELECT) {
                            if (isset($searchRule['component_props']['options_source']) && $searchRule['component_props']['options_source'] == FieldSetting::OPTIONS_SOURCE) {
                                $searchRule['component_props']['options'] = $originFieldData[$originObjectName][$originField]['ext_info']['value'] ?? [];
                                unset($searchRule['component_props']['options_source']);
                            }
                            $componentKey = $searchRule['component_key'] ?? '';
                        } else if ($originObjectName == ObjConstant::OBJ_ORDER_PRODUCT && $originField == 'product_image') {
                            break;
                        }else {
                            $componentKey = empty($searchRule) ? FieldSetting::TEXT_COMPONENT : $searchRule['component_key'];
                        }
                    } else {
                        if ($originFieldType == FieldConstant::TYPE_SELECT) {
                            $componentKey = FieldSetting::SINGLE_SELECTOR_COMPONENT;
                            $searchRule['component_props'] = [
                                'options' => $originFieldData[$originObjectName][$originField]['ext_info']['value'] ?? [],
                                'mode' => 'multiple',
                            ];
                        } elseif ($originFieldType == FieldConstant::TYPE_TEXT) {
                            $componentKey = FieldSetting::TEXT_COMPONENT;
                        }
                    }


                    if (!empty($componentKey)) {
                        $functionSearchFields[] = $this->searchFieldComponentInfo($functionField, $componentKey, $searchRule);
                    }
                    break;
                //4.int类型，引用顶级字段为
                // 系统字段且为对象关系字段: 不搜索
                // 系统字段且为下拉单选：根据配置规则中字段返回组件key，其余不搜索
                // 系统字段且为其他类型：根据配置规则中字段返回组件key，其余返回文本框组件key
                case FieldConstant::DB_TYPE_BIGINT:
                    $componentKey = '';
                    if ($originSystemType == FieldConstant::SYSTEM_TYPE_BASE) {
                        $searchRule = $this->getFieldSearchRule($originObjectName, $originField);

                        if (in_array($originFieldType, [FieldConstant::TYPE_MASTER, FieldConstant::TYPE_OBJECT, FieldConstant::TYPE_OBJECT_MULTIPLE, FieldConstant::TYPE_OBJECT_DYNAMIC])) {
                            $componentKey = '';
                        } elseif ($originFieldType == FieldConstant::TYPE_SELECT) {
                            if (isset($searchRule['component_props']['options_source']) && $searchRule['component_props']['options_source'] == FieldSetting::OPTIONS_SOURCE) {
                                $searchRule['component_props']['options'] = $originFieldData[$originObjectName][$originField]['ext_info']['value'] ?? [];
                                unset($searchRule['component_props']['options_source']);
                            }
                            $componentKey = $searchRule['component_key'] ?? '';
                        } else {
                            $componentKey = empty($searchRule) ? FieldSetting::TEXT_COMPONENT : $searchRule['component_key'];
                        }
                    }

                    if (!empty($componentKey)) {
                        $functionSearchFields[] = $this->searchFieldComponentInfo($functionField, $componentKey, $searchRule);
                    }
                    break;
                //5.array_bigint类型，引用顶级字段，根据配置规则中字段返回组件key，其余不搜索
                case FieldConstant::DB_TYPE_ARRAY_BIGINT:
                    $searchRule = $this->getFieldSearchRule($originObjectName, $originField);
                    $componentKey = $searchRule['component_key'] ?? '';
                    if (!empty($componentKey)) {
                        $functionSearchFields[] = $this->searchFieldComponentInfo($functionField, $componentKey, $searchRule);
                    }
                    break;
                //6.json、text类型字段不搜索
                case FieldConstant::DB_TYPE_TEXT:
                case FieldConstant::DB_TYPE_JSONB:
                // array_varchar类型字段不搜索
                case FieldConstant::DB_TYPE_ARRAY_VARCHAR:
                default:
                    break;
            }
        }

        return $functionSearchFields;
    }


    /**
     * 销售订单关联字段，搜索字段
     * 该处代码与引用字段隔离，后续优化完关联字段，该处逻辑将下架
     * @return array
     */
    public function linkSearchFieldList()
    {
        //目前仅支持销售订单关联字段搜索
        if ($this->objectName != ObjConstant::OBJ_ORDER) {
            return [];
        }

        //自定义关联字段搜索列表
        $fieldFilter = new FieldFilter($this->clientId);
        $fieldFilter->object_name = $this->objectName;
        $fieldFilter->function_type = FieldConstant::FUNCTION_TYPE_LINK;
        $fieldFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $fieldFilter->system_type = FieldConstant::SYSTEM_TYPE_CUSTOM;
        $fieldFilter->select(['object_name', 'field', 'field_name', 'function_type', 'columns', 'field_type', 'array_flag', 'system_type','show_flag']);
        $functionFieldList = $fieldFilter->rawData();
        if (empty($functionFieldList)) {
            return [];
        }


        //功能字段顶级字段信息
        $linkFieldList = [];
        foreach ($functionFieldList as $functionField) {
            if ($functionField['function_type'] == FieldConstant::FUNCTION_TYPE_LINK) {
                $linkFieldList[] = $functionField['field'];
            }
        }
        //关联字段
        $originFieldService = new OriginFieldService($this->clientId);
        $linkFieldLis[$this->objectName] = $linkFieldList;
        $linkOriginFieldMap = $originFieldService->getLinkOriginField($linkFieldLis);

        //合并顶级字段关系
        $originFieldMap = $linkOriginFieldMap[$this->objectName] ?? [];

        $originObjectName = [];
        foreach ($originFieldMap as $originFieldItem) {
            $originObjectName[] = $originFieldItem['relation_origin_object_name'];
        }
        $originObjectName = array_filter(array_unique($originObjectName));
        if (empty($originObjectName)) {
            return [];
        }

        $originFieldFilter = new FieldFilter($this->clientId);
        $originFieldFilter->object_name = $originObjectName;
        $originFieldFilter->select(['object_name', 'field', 'field_name', 'field_type', 'system_type', 'ext_info']);
        $originFieldList = $originFieldFilter->rawData();
        $originFieldData = [];
        foreach ($originFieldList as $originFieldItem) {
            $originFieldData[$originFieldItem['object_name']][$originFieldItem['field']] = $originFieldItem;
        }


        //文本类型模糊字段搜索组件
        $fieldSetting = $this->baseFieldSetting;
        $textSearchRule = FieldSetting::getTextFieldDefaultComponentRule();
        $textComponentKey = $fieldSetting::DIV_COMPONENT;

        //功能字段搜索数据组件信息
        $functionSearchFields = [];
        foreach ($functionFieldList as $functionField) {
            $originObjectName = $originFieldMap[$functionField['field']]['relation_origin_object_name'] ?? '';
            $originField = $originFieldMap[$functionField['field']]['relation_origin_object_field'] ?? '';
            $originFieldType = $originFieldData[$originObjectName][$originField]['field_type'] ?? '';
            $originSystemType = $originFieldData[$originObjectName][$originField]['system_type'] ?? '';

            switch ($functionField['columns']['data_db_type']) {
                //1.numeric数值类型使用数值范围查询
                case FieldConstant::DB_TYPE_NUMERIC:
                    $functionSearchFields[] = $this->searchFieldComponentInfo($functionField, FieldSetting::NUMBER_RANGE_COMPONENT);
                    break;
                //2.timestamp时间类型使用时间范围查询
                case FieldConstant::DB_TYPE_TIMESTAMP:
                    $functionSearchFields[] = $this->searchFieldComponentInfo($functionField, FieldSetting::DATE_RANGE_COMPONENT);
                    break;
                //3.varchar、array_varchar类型，引用顶级字段为
                // 系统字段且为下拉单选：根据配置规则中字段返回组件key，其余不搜索
                // 系统字段且为其他类型：根据配置规则中字段返回组件key，其余返回文本框组件key
                // 自定义字段且下拉单选：返回下拉框组件
                // 自定义字段且文本类型：返回文本框组件, 组件支持模糊搜索
                // 自定义字段且其他：不搜索
                case FieldConstant::DB_TYPE_VARCHAR:
                case FieldConstant::DB_TYPE_ARRAY_VARCHAR:
                    $componentKey = '';
                    $searchRule = [];
                    if ($originSystemType == FieldConstant::SYSTEM_TYPE_BASE) {
                        $searchRule = $this->getFieldSearchRule($originObjectName, $originField);

                        if ($originFieldType == FieldConstant::TYPE_SELECT) {
                            if (isset($searchRule['component_props']['options_source']) && $searchRule['component_props']['options_source'] == FieldSetting::OPTIONS_SOURCE) {
                                $searchRule['component_props']['options'] = $originFieldData[$originObjectName][$originField]['ext_info']['value'] ?? [];
                                unset($searchRule['component_props']['options_source']);
                            }
                            $componentKey = $searchRule['component_key'] ?? '';
                        } else {
                            $componentKey = empty($searchRule) ? FieldSetting::TEXT_COMPONENT : $searchRule['component_key'];

                            if ($componentKey == FieldSetting::TEXT_COMPONENT) {
                                $functionSearchFields[] = $this->componentInfo($functionField, $textComponentKey, $textSearchRule);
                                break;
                            }
                        }
                    } else {
                        if ($originFieldType == FieldConstant::TYPE_SELECT) {
                            $componentKey = FieldSetting::SINGLE_SELECTOR_COMPONENT;
                            $searchRule['component_props'] = [
                                'options' => $originFieldData[$originObjectName][$originField]['ext_info']['value'] ?? [],
                                'mode' => 'multiple',
                            ];
                        } elseif ($originFieldType == FieldConstant::TYPE_TEXT) {
                            $functionSearchFields[] = $this->componentInfo($functionField, $textComponentKey, $textSearchRule);
                            break;
                        }
                    }


                    if (!empty($componentKey)) {
                        $functionSearchFields[] = $this->searchFieldComponentInfo($functionField, $componentKey, $searchRule);
                    }
                    break;
                //4.int类型，引用顶级字段为
                // 系统字段且为对象关系字段: 不搜索
                // 系统字段且为下拉单选：根据配置规则中字段返回组件key，其余不搜索
                // 系统字段且为其他类型：根据配置规则中字段返回组件key，其余返回文本框组件key
                case FieldConstant::DB_TYPE_INTEGER:
                case FieldConstant::DB_TYPE_SMALLINT:
                case FieldConstant::DB_TYPE_BIGINT:
                    $componentKey = '';
                    if ($originSystemType == FieldConstant::SYSTEM_TYPE_BASE) {
                        $searchRule = $this->getFieldSearchRule($originObjectName, $originField);

                        if (in_array($originFieldType, [FieldConstant::TYPE_MASTER, FieldConstant::TYPE_OBJECT, FieldConstant::TYPE_OBJECT_MULTIPLE, FieldConstant::TYPE_OBJECT_DYNAMIC])) {
                            $componentKey = '';
                        } elseif ($originFieldType == FieldConstant::TYPE_SELECT) {
                            if (isset($searchRule['component_props']['options_source']) && $searchRule['component_props']['options_source'] == FieldSetting::OPTIONS_SOURCE) {
                                $searchRule['component_props']['options'] = $originFieldData[$originObjectName][$originField]['ext_info']['value'] ?? [];
                                unset($searchRule['component_props']['options_source']);
                            }
                            $componentKey = $searchRule['component_key'] ?? '';
                        } else {
                            $componentKey = empty($searchRule) ? FieldSetting::TEXT_COMPONENT : $searchRule['component_key'];
                        }
                    }

                    if (!empty($componentKey)) {
                        $functionSearchFields[] = $this->searchFieldComponentInfo($functionField, $componentKey, $searchRule);
                    }
                    break;
                //5.array_bigint类型，引用顶级字段，根据配置规则中字段返回组件key，其余不搜索
                case FieldConstant::DB_TYPE_ARRAY_BIGINT:
                    $searchRule = $this->getFieldSearchRule($originObjectName, $originField);
                    $componentKey = $searchRule['component_key'] ?? '';
                    if (!empty($componentKey)) {
                        $functionSearchFields[] = $this->searchFieldComponentInfo($functionField, $componentKey, $searchRule);
                    }
                    break;
                //6.text类型, 引用字段不搜索，关联字段可以搜素
                case FieldConstant::DB_TYPE_TEXT:
                    $functionSearchFields[] = $this->componentInfo($functionField, $textComponentKey, $textSearchRule);
                    break;
                //7.json类型字段不搜索
                case FieldConstant::DB_TYPE_JSONB:
                default:
                    break;
            }
        }

        return $functionSearchFields;

    }

    /**
     * 获取字段搜索规则
     * @param  $objectName
     * @param  $filed
     * @return array
     */
    public function getFieldSearchRule($objectName, $filed)
    {
        if (!isset($this->searchRule[$objectName])) {
            $fieldSetting = FieldSettingFactory::make($this->clientId, $objectName);
            $this->searchRule[$objectName] = $fieldSetting->searchComponents();
        }
        return $this->searchRule[$objectName][$filed] ?? [];
    }

    /**
     * 搜索字段组件结构基本结构
     * @param array $fieldInfo
     * @param string $componentKey
     * @param array $searchFieldRule
     * @return array
     */
    public function searchFieldComponentInfo(array $fieldInfo, string $componentKey, array $searchFieldRule = [])
    {
        // 本身是引用字段扩展字段时，做下特殊处理，保留引用字段名称
        if (
            !empty($fieldInfo['field'])
            && !empty($fieldInfo['function_type'])
            && $fieldInfo['function_type'] == FieldConstant::FUNCTION_TYPE_QUOTE
            && !empty($searchFieldRule['search_field_name']) && !empty($fieldInfo['field_name'])
        ) {
            unset($searchFieldRule['search_field_name']);
        }
        $specialOperators = $searchFieldRule['operators'] ?? [];
    
//        todo
        is_numeric($fieldInfo['field'] ?? '') && $searchFieldRule['name' . TranslateConstants::FORMAT_KEY_INFO_SUFFIX] = $fieldInfo['field_name' . TranslateConstants::FORMAT_KEY_INFO_SUFFIX] ?? TranslateService::formatValue($fieldInfo['field_name'], $fieldInfo['field_name']);


        $component_props =  $searchFieldRule['component_props'] ?? [];
        unset($searchFieldRule['component_props']);
        if(isset($component_props['placeholder'])){
            $component_props['placeholder'] = \Yii::t('field',$component_props['placeholder']);
        }
        $searchFieldRule = [
            'children' => [
                array_merge([
                    'field' => 'value',
                    'name' => \yii::t('field', isset($searchFieldRule['search_field_name']) ? $searchFieldRule['search_field_name'] : $fieldInfo['field_name']),
                    'component_key' => $componentKey,
                    'component_props' =>$component_props,
                ], $searchFieldRule)
            ],
        ];
        return $this->componentInfo($fieldInfo, '', $searchFieldRule, $specialOperators);
    }

    /**
     * 搜索字段组件信息
     * @param array $fieldInfo
     * @param array $searchFieldRule
     * @return array
     */
    public function componentInfo(array $fieldInfo, string $componentKey = '', array $searchFieldRule = [], array $specialOperators = [])
    {
        $operators = $this->fieldOperatorList($fieldInfo, $specialOperators);
//        todo
        is_numeric($fieldInfo['field'] ?? '') && $searchFieldRule['name' . TranslateConstants::FORMAT_KEY_INFO_SUFFIX] = $fieldInfo['field_name' . TranslateConstants::FORMAT_KEY_INFO_SUFFIX] ?? TranslateService::formatValue($fieldInfo['field_name'], $fieldInfo['field_name']);

        $searchField = [
            'object_name' => $fieldInfo['object_name'],
            'field' => $fieldInfo['field'],
            'field_type' => $fieldInfo['field_type'] ?? '',
            'array_flag' => $fieldInfo['array_flag'] ?? '',
            'ext_info' => $fieldInfo['ext_info'] ?? [],
            'show_flag' => $fieldInfo['show_flag'] ?? 1,
            'system_type' => $fieldInfo['system_type'] ?? FieldConstant::SYSTEM_TYPE_CUSTOM,
            'name' => \yii::t('field', $fieldInfo['field_name']),
            'component_key' => $componentKey,
            'component_props' => $searchFieldRule['component_props'] ?? [],
            'operators' => $operators,
        ];
        $searchField = array_merge($searchField, $searchFieldRule);
        if (isset($searchField['search_filed'])) {
            unset($searchField['search_filed']);
        }

        return $searchField;
    }

    public function fieldOperatorList(array $fieldInfo, array $specialOperators = [])
    {
        $operators = [];
        if (!empty($specialOperators)) {
            //特殊配置的操作符
            $firstElement = reset($specialOperators);
            foreach ($specialOperators as $operator) {
                $operatorElement = [
                    'key' => $operator,
                    'name' => \Yii::t('workflow', 'filter_operator.' . $operator),
                ];
                if ($firstElement == $operator) {
                    $operatorElement['is_default'] = true;
                }
                $operators[] = $operatorElement;
            }
        } else {
            //根据field_type，获取操作符
            if (isset($fieldInfo['field_type']) && isset($fieldInfo['array_flag'])) {
                $operators = $this->getOperatorMap()[$fieldInfo['field_type']][$fieldInfo['array_flag']] ?? [];
            }
        }

        if (in_array($this->searchType, [self::COMMON_SEARCH_TYPE, self::FAST_SEARCH_TYPE])) {
            $operators = [array_shift($operators)];
        }
        return $operators;
    }

    /**
     * 搜索字段条件
     * @param $quoteData
     * @return array
     */
    public function quoteSearchWhere($quoteData)
    {
        //自定义功能字段搜索列表
        $functionFieldList = array_column(FieldHelper::getCustomFieldFunctionField($this->clientId, $this->objectName), null, 'field');

        $quoteWheres = [];
        foreach ($quoteData as $quoteField => $quoteFieldItem) {
            if (!array_key_exists($quoteField, $functionFieldList)) {
                continue;
            }

            switch ($functionFieldList[$quoteField]['columns']['data_db_type']) {
                case FieldConstant::DB_TYPE_NUMERIC:
                    $quoteWheres[$quoteField] = new Range(isset($quoteFieldItem[0]) && is_numeric($quoteFieldItem[0]) ? $quoteFieldItem[0] : null, isset($quoteFieldItem[1]) && is_numeric($quoteFieldItem[1]) ? $quoteFieldItem[1] : null);
                    break;
                case FieldConstant::DB_TYPE_TIMESTAMP:
                    $quoteWheres[$quoteField] = new DateRange(isset($quoteFieldItem[0]) && !empty($quoteFieldItem[0]) && $quoteFieldItem[0] !== 0 ? $quoteFieldItem[0] : null, isset($quoteFieldItem[1]) && !empty($quoteFieldItem[1]) && $quoteFieldItem[1] !== 0 ? $quoteFieldItem[1] : null);
                    break;
                case FieldConstant::DB_TYPE_VARCHAR:
                case FieldConstant::DB_TYPE_BIGINT:
                    $quoteWheres[$quoteField] = $quoteFieldItem;
                    break;
                case FieldConstant::DB_TYPE_ARRAY_BIGINT:
                    $quoteFieldItem = is_array($quoteFieldItem) ? $quoteFieldItem : [$quoteFieldItem];
                    $quoteWheres[$quoteField] = new InArray($quoteFieldItem);
                    break;
                case FieldConstant::DB_TYPE_ARRAY_VARCHAR:
                    //todo 搜索数组字符串
                default:
                    break;
            }

        }
        return $quoteWheres;
    }


    /**
     * 搜索字段兼容历史参数
     * @param $queryFilters
     * @return array
     */
    public function queryFiltersParams($queryFilters)
    {
        $queryFilters = is_array($queryFilters) ? $queryFilters : json_decode($queryFilters, true);
        $queryFilters = array_filter($queryFilters, function ($queryFilter) {
            return is_array($queryFilter);
        });
        $filtersFields = array_keys($queryFilters);
        if (empty($filtersFields)) {
            return [];
        }

        list($objNames, $baseSearchFields, $baseSearchComponents) = $this->baseSearchComponents();

        $fieldFilter = new FieldFilter($this->clientId);
        $fieldFilter->object_name = $objNames;
        $fieldFilter->field = $filtersFields;
        $fieldFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $fieldFilter->select(['object_name', 'field', 'field_name', 'field_type', 'array_flag', 'system_type', 'function_type', 'ext_info']);
        $fieldList = $fieldFilter->rawData();

        $filtersParams = [];
        $baseFields = [];
        $quoteFieldItems = [];
        foreach ($fieldList as $item) {
            if ($item['system_type'] == FieldConstant::SYSTEM_TYPE_BASE) {
                //系统字段
                $searchField = $baseSearchComponents[$item['object_name']][$item['field']]['search_field'] ?? $item['field'];
                $filtersParams[$searchField] = $queryFilters[$item['field']]['value'];
                $baseFields[$item['object_name']][] = $item['field'];
            } elseif ($item['system_type'] == FieldConstant::SYSTEM_TYPE_CUSTOM && in_array($item['function_type'], [FieldConstant::FUNCTION_TYPE_NORMAL, FieldConstant::FUNCTION_TYPE_LINK])) {
                //自定义字段
                $customFieldItem = [
                    'field_id'   => $item['field'],
                    'keyword'    => $queryFilters[$item['field']]['value'],
                    'field_type' => $item['field_type'],
                ];
                if (isset($queryFilters[$item['field']]['operate'])) {
                    $customFieldItem['match_type'] = $queryFilters[$item['field']]['operate'];
                } elseif ($item['field_type'] == FieldConstant::TYPE_SELECT) {
                    if ($item['array_flag'] == FieldConstant::IS_ARRAY) {
                        $customFieldItem['match_type'] = FieldSetting::TERMS_SEARCH;
                    } else {
                        $customFieldItem['match_type'] = FieldSetting::TERM_SEARCH;
                    }
                }

                $filtersParams['custom_field'][] = $customFieldItem;
            } elseif ($item['system_type'] == FieldConstant::SYSTEM_TYPE_CUSTOM && in_array($item['function_type'], [FieldConstant::FUNCTION_TYPE_QUOTE, FieldConstant::FUNCTION_TYPE_FORMULA, FieldConstant::FUNCTION_TYPE_CALCULATE])) {
                if($item['function_type'] == FieldConstant::FUNCTION_TYPE_QUOTE){
                    $quoteFieldItems[] = $item;
                }
                $filtersParams['quote_field'][$item['field']] = $queryFilters[$item['field']]['value'];
            }
        }

        // 如果用户有使用引用字段作为搜索条件
        if(!empty($quoteFieldItems)){
            $fieldRelationFilter = new FieldRelationFilter($this->clientId);
            $fieldRelationFilter->field = array_column($quoteFieldItems, 'field');
            $fieldRelationFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
            $fieldRelationFilter->select(['relation_object_name', 'field', 'relation_object_field']);
            $relationMap = array_column($fieldRelationFilter->rawData(), null, 'field');
            foreach($quoteFieldItems as $quoteFieldItem){
                if(!isset($relationMap[$quoteFieldItem['field']])){
                    continue;
                }

                $relationItem = $relationMap[$quoteFieldItem['field']];
                if(!isset($queryFilters[$relationItem['field']])){
                    continue;
                }


                $queryFilterValue = $this->handleSpecialQueryFilter($relationItem['relation_object_field'],$relationItem['relation_object_name'],$queryFilters[$relationItem['field']]['value']);
                $filtersParams['quote_field'][$relationItem['field']] = $queryFilterValue;
            }
        }

        if (isset($filtersParams['custom_field'])) {
            $filtersParams['custom_field'] = json_encode($filtersParams['custom_field']);
        }

        //对比字段表与配置中的搜索字段，若配置表中存在的搜索字段，字段表中不存在，需要添加搜索
        foreach ($baseSearchFields as $objName => $fields) {
            if (empty($fields)) {
                continue;
            }
            $baseObjNameFields = $baseFields[$objName] ?? [];
            $extSearchFields = array_diff($fields, $baseObjNameFields);
            if (empty($extSearchFields)) {
                continue;
            }
            foreach ($extSearchFields as $extSearchField) {
                if (empty($queryFilters[$extSearchField])) {
                    continue;
                }
                $searchField = $baseSearchComponents[$objName][$extSearchField]['search_field'] ?? $extSearchField;
                $filtersParams[$searchField] = $queryFilters[$extSearchField]['value'];
            }

        }

        return $filtersParams;
    }

    // 格式化有特殊逻辑字段的筛选项value
    protected function handleSpecialQueryFilter($field, $objectName, $value){
        // 先采用简陋的写法，以后如果要处理的特殊字段多再写成单独的类进行扩展
        if($objectName == ObjConstant::OBJ_COMPANY){
            if($field == 'origin_list'){
                $value = (new OriginApi($this->clientId))->getIdList(array_filter((array)$value, function ($item) {
                    return !empty($item) && is_numeric($item);
                }), true, true, true);
            }
        }

        return $value;
    }

    /**
     * 获取默认搜索字段
     * @return array|int[]|string[]
     */
    public function defaultSearchFields()
    {
        $baseFieldSetting = $this->baseFieldSetting;
        $baseSearchFields = $baseFieldSetting->searchFields();

        $defaultSearchFields = [];
        foreach ($baseSearchFields[$baseFieldSetting::FAST_SEARCH_TYPE] ?? [] as $searchFields) {
            foreach ($searchFields as $searchField) {
                $defaultSearchFields[] = [
                    'field' => $searchField,
                    'search_type' => $baseFieldSetting::FAST_SEARCH_TYPE
                ];
            }
        }
        foreach ($baseSearchFields[$baseFieldSetting::COMMON_SEARCH_TYPE] ?? [] as $searchFields) {
            foreach ($searchFields as $searchField) {
                $defaultSearchFields[] = [
                    'field' => $searchField,
                    'search_type' => $baseFieldSetting::COMMON_SEARCH_TYPE
                ];
            }
        }
        return $defaultSearchFields;
    }

    public function defaultMainSearchFields()
    {
        $baseFieldSetting = $this->baseFieldSetting;
        $baseSearchFields = $baseFieldSetting->searchFields();
        $fastSearchFields = $baseSearchFields[$baseFieldSetting::FAST_SEARCH_TYPE][$this->objectName] ?? [];
        $commonSearchFields = $baseSearchFields[$baseFieldSetting::COMMON_SEARCH_TYPE][$this->objectName] ?? [];

        $defaultSearchFields = [];
        foreach ($fastSearchFields as $searchField) {
            $defaultSearchFields[] = [
                'field' => $searchField,
                'search_type' => $baseFieldSetting::FAST_SEARCH_TYPE
            ];
        }
        foreach ($commonSearchFields as $searchField) {
            $defaultSearchFields[] = [
                'field' => $searchField,
                'search_type' => $baseFieldSetting::COMMON_SEARCH_TYPE
            ];
        }

        return $defaultSearchFields;
    }

}
