<?php

namespace common\library\object\service;

class FieldService
{
    protected $clientId;
    protected $type;

    public function __construct($clientId,$type)
    {
        $this->type = $type;
        $this->clientId = $clientId;
    }

    public static function make($clientId,$type)
    {
        return new static($clientId,$type);
    }

    public function unpack(array $data)
    {

    }

    public function validate()
    {

    }
}