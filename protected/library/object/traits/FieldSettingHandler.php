<?php

namespace common\library\object\traits;

use common\library\object\field\field_handler\Factory;
use common\library\object\field\field_handler\FieldValueHandler;

/**
 * Trait FieldSettingHandler
 * @package common\library\object\traits
 * @property  $clientId
 */
trait FieldSettingHandler
{
    public function getFieldHandler($fieldInfo)
    {
        $clientId = $this->clientId ?? ($this->_clientId ?? null);
        if (empty($clientId)) {
            throw new \RuntimeException('client_id is null');
        }

        $metadata = null;
        if(method_exists($this, 'getMetadata')){
            $metadata = $this->getMetadata();
        }
        return Factory::make($clientId, $fieldInfo, $metadata);
    }

    public function fieldHandler($fieldSetting, $field, &$value)
    {
        if (empty($fieldSetting)) {
            return $value;
        }

        if ($field == 'external_field_data') {
            $value = is_null($value) ? [] : $value;
            foreach ($value as $externalField => &$externalFieldValue) {
                if (isset($fieldSetting[$externalField])) {
                    $fieldHandler = $this->getFieldHandler($fieldSetting[$externalField]);
                    !is_null($fieldHandler) && $externalFieldValue = $fieldHandler->handler($externalFieldValue);
                }
            }
        }

        if (isset($fieldSetting[$field])) {
            $fieldHandler = $this->getFieldHandler($fieldSetting[$field]);
            /** @var FieldValueHandler $fieldHandler */
            !is_null($fieldHandler) && $value = $fieldHandler->handler($value);
        }

        return $value;
    }

    public function batchFieldHandler($fieldSetting, &$data)
    {
        if (empty($fieldSetting)) {
            return;
        }

        foreach ($data as &$datum) {
            foreach ($datum as $field => &$value) {
                $this->fieldHandler($fieldSetting, $field, $value);
            }
            unset($value);
        }
        unset($datum);
    }

}