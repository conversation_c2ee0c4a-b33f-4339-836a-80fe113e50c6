<?php

namespace common\library\inquiry_collaboration;

use common\library\product_v2\search\GetSearcher;
use xiaoman\orm\common\FilterV2;


/**
 * class InquiryCollaborationFilter
 * @package common\\Users\robert\project\php\PHP-CRM\protected\library\inquiry_collaboration
  * @property int inquiry_collaboration_id
 * @property string inquiry_collaboration_no
 * @property int client_id
 * @property string name
 * @property int status
 * @property float progress
 * @property mixed handler
 * @property mixed inquiry_deadline
 * @property int create_schedule
 * @property string remark
 * @property mixed finish_time
 * @property mixed attachments
 * @property string currency
 * @property float exchange_rate
 * @property float exchange_rate_usd
 * @property float product_total_amount
 * @property float product_total_amount_rmb
 * @property float product_total_amount_usd
 * @property float product_total_count
 * @property string shipment_port
 * @property string target_port
 * @property string transport_mode
 * @property string price_contract
 * @property int customer_id
 * @property int company_id
 * @property int lead_id
 * @property int opportunity_id
 * @property string country
 * @property string customer_phone
 * @property string customer_email
 * @property string customer_name
 * @property string social_media
 * @property mixed source
 * @property int create_user
 * @property mixed create_time
 * @property int update_user
 * @property int enable_flag
 * @property mixed update_time

 * @method BatchInquiryCollaboration find()
 * @method InquiryCollaborationMetadata getMetadata()
 */
class InquiryCollaborationFilter extends FilterV2
{
    use InitInquiryCollaborationMetadata;
    use GetSearcher;

    public static function getMetadataClass()
    {
        return InquiryCollaborationMetadata::class;
    }

    public function defaultSetting()
    {
        $this->client_id = $this->clientId;

        parent::defaultSetting();
    }



    public function rawWhere($where)
    {
        return parent::rawWhere($where);
    }

    public function defaultRaw(){
        return $this->rawWhere(' and 1 = 0 ');
    }

}
