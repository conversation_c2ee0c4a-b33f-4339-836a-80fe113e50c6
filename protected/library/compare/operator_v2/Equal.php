<?php
/**
 * Created by PhpStorm.
 * User: beenzhang
 * Date: 2020/5/27
 * Time: 10:40 AM
 */

namespace common\library\compare\operator_v2;


use common\library\custom_field\CustomFieldService;
use common\library\object\field\FieldConstant;

class Equal extends AbstractOperator
{
    const ARRAY_TYPE_FIELDS = [
        FieldConstant::TYPE_SELECT,
        FieldConstant::TYPE_IMAGE,
        FieldConstant::TYPE_FILE,
    ];

    const SINGAL = '=';

    public function compare($value, $targetValue, $extParams = [])
    {
        if($this->fieldInfo['array_flag']){     // 对于多选字段
            if(!is_array($value) && $value !== '' && !is_null($value)){
                $value = [$value];
            }
            if(!is_array($targetValue) && $targetValue !== '' && !is_null($targetValue)){
                $targetValue = [$targetValue];
            }
        }
        
        if (
            in_array($this->fieldInfo['field_type'], self::ARRAY_TYPE_FIELDS)
            || (is_array($value) || is_array($targetValue))
        )
            return $this->arrayEqual($targetValue, $value);

        if(empty($value) && empty($targetValue)){
            // 防止出现 '', 0这种值误判断
            if (strlen($value) != strlen($targetValue)) {
                return false;
            } else {
                return true;
            }
        }
        return $value == $targetValue;
    }

    protected function compareEmpty($new, $old){
        if($new === '0' || $old === '0' || $new === 0 || $old === 0){
            return $new === $old;
        }

        return true;
    }

    protected function arrayEqual($new, $old)
    {
        if (empty($new) && empty($old)){
            return $this->compareEmpty($new, $old);
        }

        $new = $new ?: [];
        $old = $old ?: [];

        $new = is_array($new) ? $new : [$new];
        $old = is_array($old) ? $old : [$old];

        //一维数组&&数字下标
        $firstIndex = array_key_first($new);

        if ($firstIndex === null)
            return $new == $old;

        if (is_numeric($firstIndex) && count($new) == count($new,1))
        {
            $add = array_values(array_diff($new, $old));
            $remove = array_values(array_diff($old, $new));

            //数组属性新增、删除
            if (!empty($add) || !empty($remove))
                return false;

            return true;
        }

        //多维数组判断是否改了value
        $boll = $this->arrayRecursiveEqual($new, $old);

        return $boll;
    }

    protected function arrayRecursiveEqual($new, $old)
    {
        $new = is_array($new) ? $new : [];
        $old = is_array($old) ? $old : [];

        if (empty($new) && empty($old))
            return true;

        //一维数组&&数字下标
        $firstIndex = array_key_first($new);
        if ($firstIndex === null)
            return $new == $old;

        if (is_numeric($firstIndex) && count($new) == count($new,1))
        {
            $add = array_values(array_diff($new, $old));
            $remove = array_values(array_diff($old, $new));

            //数组属性新增、删除
            if (!empty($add) || !empty($remove))
                return false;

            return true;
        }

        //数组属性新增、删除
        if (count(array_keys($new)) !==  count(array_keys($old)))
            return false;

        //多维数组
        foreach ($new as $key => $item)
        {
            $oldItem = $old[$key] ?? null;
            if ($oldItem === null)
                return false;

            if (!is_array($item))
            {
                if ($item != $oldItem)
                    return false;

                continue;
            }

            if (!is_array($oldItem))
                return false;

            $boll = $this->arrayRecursiveEqual($item, $oldItem);
            if (!$boll)
                return false;
        }

        return true;
    }
}