<?php

namespace common\library\swarm\filter;

use common\library\swarm\SwarmConfig;
use common\library\workflow\filter\WorkflowFilterRunner;

class SqlFilter extends BaseFilter
{

    private function getFilter($ruleInfo)
    {
        $filter = new WorkflowFilterRunner($ruleConfig = new SwarmConfig($this->clientId, $this->referType, $this->referIds, $ruleInfo));
        $filter->setFilters($ruleInfo['filters'], $ruleInfo['criteria']);
        $filter->addExtraFilters($this->getExtraFilters($ruleInfo));
        $filter->addExtraWorkflowFilters(...$ruleConfig->getExtraFilters());

        return $filter;
    }

    public function iterate($limit = 2000)
    {
        foreach ($this->ruleList as $ruleInfo) {
            if (empty($ruleInfo['filters']) && empty($ruleInfo['extra_filters'])) {
                yield [[$ruleInfo['swarm_id']], [0]];
            } else {
                $filter = $this->getFilter($ruleInfo);
                $exist = false;
                $startTime = microtime(true);
                foreach ($filter->iterate($limit) as $item) {
                    $item = array_column($item, 'company_id');
                    $exist = true;
                    \LogUtil::info('sql matched', [
                        'swarmId'       => $ruleInfo['swarm_id'],
                        'filters'       => $ruleInfo['filters'],
                        'sql'           => $filter->getCurrentSql()[0] ?? '',
                        'params'        => $filter->getCurrentSql()[1] ?? [],
                        'result'        => count($item),
                        'speed'         => round((microtime(true) - $startTime) * 1000, 2),
                        'swarmType'     => $this->getSwarmType(),
                        'ruleSwarmType' => $ruleInfo['swarm_type'] ?? 0,
                    ]);
                    $startTime = microtime(true);
                    yield [[$ruleInfo['swarm_id']], $item];
                }
                if (!$exist) {
                    \LogUtil::info('iterate swarm', [
                        'swarmId'       => $ruleInfo['swarm_id'],
                        'filters'       => $ruleInfo['filters'],
                        'sql'           => $filter->getCurrentSql()[0] ?? '',
                        'params'        => $filter->getCurrentSql()[1] ?? [],
                        'result'        => 0,
                        'swarmType'     => $this->getSwarmType(),
                        'ruleSwarmType' => $ruleInfo['swarm_type'] ?? 0,
                    ]);
                }
            }
        }

        return [0, []];
    }

    public function count()
    {
        $map = [];
        foreach ($this->ruleList as $ruleInfo) {
            $startTime = microtime(true);
            $filter = $this->getFilter($ruleInfo);
            $map[$ruleInfo['swarm_id']] = $filter->count();
            \LogUtil::info('total sql matched count', [
                'swarmId'       => $ruleInfo['swarm_id'],
                'filters'       => $ruleInfo['filters'],
                'sql'           => $filter->getCurrentSql()[0] ?? '',
                'params'        => $filter->getCurrentSql()[1] ?? [],
                'result'        => $map[$ruleInfo['swarm_id']],
                'speed'         => round((microtime(true) - $startTime) * 1000, 2),
                'swarmType'     => $this->getSwarmType(),
                'ruleSwarmType' => $ruleInfo['swarm_type'] ?? 0,
            ]);
        }

        return $map;
    }

}