<?php

namespace common\library\swarm;

use common\library\cache\CacheAble;
use common\library\cache\CacheAbleTrait;
use common\library\customer_v3\company\list\CompanyList;
use common\library\duplicate\conflict_record\ConflictConstants;
use common\library\duplicate\conflict_record\ConflictService;
use common\library\queue_v2\job\crontab\SwarmDailyRefresh;
use common\library\queue_v2\job\PublicRuleReferChanged;
use common\library\queue_v2\job\PublicRuleRefreshForClient;
use common\library\queue_v2\job\PublicRuleRuleChanged;
use common\library\queue_v2\job\PublicRuleRuleDeleted;
use common\library\queue_v2\job\SwarmDeleted;
use common\library\queue_v2\job\SwarmEdited;
use common\library\queue_v2\job\SwarmReferChanged;
use common\library\queue_v2\job\SwarmRefreshForClient;
use common\library\queue_v2\job\SwarmRefreshIterateJob;
use common\library\queue_v2\QueueConstant;
use common\library\queue_v2\QueueService;
use common\library\report\error\ErrorReport;
use common\library\setting\item\Api;
use common\library\setting\item\ItemSettingConstant;
use common\library\setting\library\aiSwarm\AiSwarmApi;
use common\library\setting\library\publicRule\TriggerByPublicRuleTrait;
use common\library\setting\library\publicSwarm\PublicSwarm;
use common\library\setting\library\publicSwarm\PublicSwarmApi;
use common\library\setting\library\swarm\Swarm;
use common\library\setting\library\swarm\SwarmApi;
use common\library\swarm\handler\CompanyHandler;
use common\library\validation\Validator;
use common\library\workflow\filter\WorkflowCriteriaBuilder;
use common\library\workflow\filter\WorkflowFilterRunner;
use common\library\workflow\WorkflowConstant;

class SwarmService implements CacheAble
{
    use CacheAbleTrait, TriggerByPublicRuleTrait;

    const CACHE_PREFIX = 'crm:swarm:company';
    const CACHE_TYPE_MAP = 'count_map';
    const CACHE_TYPE_COMPANY_LIST = 'company_list';

    const REFER_IDS_TRUCK_COUNT = 1000;

    protected $clientId;
    protected $referType = \Constants::TYPE_COMPANY;

    public $enableCache = true;

    protected     $skipUpdateSearch = false;
    protected int $swarmType;
    
    public function __construct($clientId, $referType = \Constants::TYPE_COMPANY) {
        
        $this->clientId = $clientId;
        $this->referType = $referType;


//        兜底
        try {

            if (!$this->clientId && \User::isLogin()) {

                $this->clientId = \User::getLoginUser()->getClientId();
            }
        } catch (\Throwable $throwable) {

            \LogUtil::exception($throwable);
        }

    }

    protected function getCacheKey($type)
    {
        return self::CACHE_PREFIX . ":{{$this->clientId}}:$type";
    }

    public function flushCache()
    {
        if ($this->isEnableCache()) {
            $this->flush();
            $cache = $this->getCache();
            $cache->del($this->getCacheKey(self::CACHE_TYPE_MAP));
        }
    }
    
    /**
     * @deprecated
     */
    public function cleanDryRunCache($key)
    {
        return $this->getCache()->del([$this->getCompanyListCacheKey($key)]);
    }

    public function dryRun($userId, $ruleInfo, $tempSwarmId = '')
    {
        if (!$tempSwarmId) {
            $tempSwarmId = $this->createTempSwarm($ruleInfo['filters'], $ruleInfo['criteria_type'] ?? WorkflowConstant::LOGIC_OPERATOR_AND, $ruleInfo['criteria'] ?? '', $ruleInfo['user_id'] ?? 0, '');
        }
    
        $userNum = SwarmService::getSwarmUserNum($this->swarmType);
        
//        $list = new CompanyList($userId);
        $list = new CompanyList($userId);
        $list->setUserNum($userNum);
        $list->setTempSwarmId($tempSwarmId);
        $count = $list->count();

        return [$tempSwarmId, $count];
//        $ruleInfo['swarm_id'] = $tempSwarmId;
//        $ruleInfo['criteria'] = WorkflowCriteriaBuilder::buildCriteria($ruleInfo['filters'], $ruleInfo['criteria_type'] ?? WorkflowConstant::LOGIC_OPERATOR_AND, $ruleInfo['criteria'] ?? '');
//        $map = (new RuleTrigger($this->clientId, $ruleInfo))->useSqlFilter()->run(2) ?: [];
//        $ids = $map[$tempSwarmId] ?? [];
//        $this->getCache()->set($this->getCompanyListCacheKey($tempSwarmId), $ids, 'EX', 60 * 15);

//        return [$tempSwarmId, count($ids)];
    }

    protected function getCompanyListCacheKey($key)
    {
        return $this->getCacheKey(self::CACHE_TYPE_COMPANY_LIST) . $key;
    }

    public function getSwarmCountMap($clientId, $swarmIds, $userId, array $emptyFilterSwarmIds = [], $opUserId = 0, $queryParams = [])
    {
    
        $this->swarmType = $this->swarmType ?? ItemSettingConstant::ITEM_TYPE_SWARM;
        
        $swarmIds = array_filter((array)$swarmIds);
        $cacheHash = $this->swarmType . '_';
        $cacheHash .= $opUserId ? ($userId != $opUserId ? "{$userId}:{$opUserId}" : "$userId") : "$userId";
        if (!empty($queryParams)) {
            if (is_array($queryParams) && count($queryParams) == 1 && isset($queryParams['show_all'])) {
                $cacheHash .= ":" . $queryParams['show_all'];
            } else {
                $this->setEnableCache(false);
            }
        }
        if ($this->isEnableCache()) {
            $cacheKey = $this->getCacheKey(self::CACHE_TYPE_MAP);
            $cache = $this->getCache();
            $cache->hdel($cacheKey, [$cacheHash]);
            $map = $cache->hget($cacheKey, $cacheHash);
            if (!empty($map)) {
                return $map;
            }
        }

        $companyAlias = 'c';
        $swarmAlias = 'cc';
    
        $table = CompanyHandler::RELATE_TABLE_MAP[$this->swarmType];
    
        if ($this->swarmType == ItemSettingConstant::ITEM_TYPE_SWARM) {
            
            $userNum = [1, 2];
        
            $userIdFlag = true;
        } else {
            
            $userNum = [0];
        
            $userIdFlag = false;
        }

//        $companyList = new CompanyList($userId);
        $companyList = new CompanyList($userId);
        $companyList->setAlias($companyAlias);
        $companyList->setUserNum($userNum);
        $companyList->setOpUserId($opUserId);
        $companyList->setUserIdFlag($userIdFlag);
        $companyList->paramsMapping($queryParams);

        [$where, $params] = $companyList->buildParams(true, false);
   
        // 走user_id索引时跳过client_id索引
        if (!preg_match("/{$companyAlias}\s*\.\s*user_id\s*!=\s*'{}'/", $where)) {
            
            $where = preg_replace('/\w+\.\s*client_id=.*?AND\s*/', '', $where);
            $where = preg_replace('/\w+\.\s*client_id=.*?$/', '', $where);


            foreach ($params as $key => $value) {
                if (strpos($key, 'where_client_id') !== false) {
                    unset($params[$key]);
                }
            }
        }

        
        $join = "left join tbl_company as $companyAlias on $companyAlias.company_id=$swarmAlias.company_id";

        $defaultMap = array_fill_keys($swarmIds, 0);

//        if (count($swarmIds)) {
//            $join .= " AND $swarmAlias.swarm_id in (" . implode(',', $swarmIds) . ")";
//        }
        $sql = "select {$swarmAlias}.swarm_id as swarm_id, count({$swarmAlias}.company_id) as company_count from {$table} as {$swarmAlias} {$join} where {$where} group by {$swarmAlias}.swarm_id";
        $db = \PgActiveRecord::getDbByClientId($this->clientId);
        $result = $db->createCommand($sql)->queryAll(true, $params);
        \LogUtil::debug("count map sql", [
            'sql'       => $sql,
            'params'    => $params,
            'swarmType' => $this->swarmType,
        ]);
        $dbMap = array_column($result, 'company_count', 'swarm_id');
        $dbMap = array_intersect_key($dbMap, $defaultMap);
    
        $allCompanySwarmIds = $this->getAllCompanySwarmIds($clientId, $swarmIds, $this->swarmType);
        $emptyFilterSwarmIds = array_merge($allCompanySwarmIds, $emptyFilterSwarmIds);
        $extraMap = [];
        if (in_array(Swarm::SWARM_ID_OF_ALL, $swarmIds) || count($emptyFilterSwarmIds)) {
            $allCompanyCount = $companyList->count();
            $extraMap = array_fill_keys(array_merge($emptyFilterSwarmIds, [Swarm::SWARM_ID_OF_ALL]), $allCompanyCount);
        }

//        if (in_array(Swarm::SWARM_ID_OF_OWNER, $swarmIds)) {
//            $companyList->showAll(false);
//            $extraMap[Swarm::SWARM_ID_OF_OWNER] = $companyList->count();
//        }

        if (in_array(Swarm::SWARM_ID_OF_FOLLOW, $swarmIds)) {
            $companyList->setIsPin(true);
            $extraMap[Swarm::SWARM_ID_OF_FOLLOW] = $companyList->count();
        }

        $res = array_replace($defaultMap, $dbMap, $extraMap);
        if ($this->isEnableCache()) {
            $cacheKey = $this->getCacheKey(self::CACHE_TYPE_MAP);
            $cache = $this->getCache();
            $cache->hset($cacheKey, $cacheHash, $res);
        }

        return $res;
    }


    public function getSwarmByCompany($userId, $referIds)
    {
        //根据客户id获取id归属的客群信息
        $table = CompanyHandler::RELATE_TABLE_MAP[$this->swarmType];
        $db = \PgActiveRecord::getDbByClientId($this->clientId);
        $referIds[] = 0;
        $sql = "select swarm_id from {$table} where client_id={$this->clientId} and company_id in (" . implode($referIds) . ")";
        $swarmIds = $db->createCommand($sql)->queryColumn();
        $list = [];
    
        if ($swarmIds) {
            
            $api = \common\library\swarm\SwarmService::getSwarmApi($this->swarmType, $this->clientId, $userId);
    
            $list = $api->listById($swarmIds);
        }

        return $list;
    }

	public function getSwarmMapByCompany($userId, $referIds) {

		if (empty($referIds)) {

			return [];
		}

		//根据客户id获取id归属的客群信息
        $table = CompanyHandler::RELATE_TABLE_MAP[$this->swarmType];

		$db = \PgActiveRecord::getDbByClientId($this->clientId);
		$referIds[] = 0; // 满足所有客户的客群关联表中客户id为0

		$sql = "select swarm_id, company_id from {$table} where client_id={$this->clientId} and company_id in (" . implode(',', $referIds) . ")";

		$swarmList = $db->createCommand($sql)->queryAll();

        $result = [];
        $swarmIds = array_unique(array_column($swarmList, 'swarm_id'));
     
        if ($swarmIds) {
            
            $api = \common\library\swarm\SwarmService::getSwarmApi($this->swarmType, $this->clientId, $userId);
    
            $swarmNameMap = $api->getNameMap($swarmIds);
            foreach ($swarmList as $swarmInfo) {
                if (isset($swarmNameMap[$swarmInfo['swarm_id']]))
                    $result[$swarmInfo['company_id']][] = [
                        'swarm_id' => $swarmInfo['swarm_id'],
                        'name'     => $swarmNameMap[$swarmInfo['swarm_id']],
                    ];
            }
        }

        return $result;

//		$swarmMap = [];
//
//		$swarmIds = [];
//
//		foreach ($swarmList as $swarmInfo) {
//
//			$swarmMap[$swarmInfo['company_id']][] = $swarmInfo['swarm_id'];
//
//			$swarmIds[] = $swarmInfo['swarm_id'];
//		}
//
//		$result = [];
//
//		if ($swarmIds) {
//
//			$list = (new SwarmApi($this->clientId, $userId))->listById(array_unique($swarmIds));
//
//			$list = array_column($list, null, 'swarm_id');
//
//			foreach ($swarmMap as $companyId => $swarmIds) {
//
//				foreach ($swarmIds as $swarmId) {
//
//					if (isset($list[$swarmId])) {
//
//						$result[$companyId][] = [
//
//							'swarm_id' => $list[$swarmId]['swarm_id'],
//							'name'     => $list[$swarmId]['name'],
//						];
//					}
//				}
//			}
//		}
//
//		return $result;
	}

    public function setSkipUpdateSearch($flag = true)
    {
        $this->skipUpdateSearch = $flag;
        return $this;
    }

    public function refreshByRefer($referIds, $changeFields = [], $async = true, $dataList = [])
    {
        $referIds = array_filter((array)$referIds);
        if (empty($referIds)) {
            return;
        }

        $async = $async && !$this->isExp() && (getenv('RUNTIME_APP') != 'alibaba-consumer');
    
        $changeFields = (array)$changeFields;
        
        in_array('client_tag_list', $changeFields) && $changeFields[] = 'cus_tag';
    
        if (!empty($changeFields) && !array_intersect($changeFields, $this->getCachedFilterField())) {

            return;
        }
        
        $referList = array_chunk($referIds, static::REFER_IDS_TRUCK_COUNT);
        foreach ($referList as $referIds) {
            $job = new SwarmReferChanged($this->clientId, $referIds, $changeFields);
    
            !empty($this->swarmType) && $job->setSwarmType($this->swarmType);
            
            if (count($referIds) >= static::REFER_IDS_TRUCK_COUNT) {
        
                $job->setIsDelay(false);
            }
            
            if (getenv('RUNTIME_APP') == 'alibaba-command' || getenv('RUNTIME_ENV') != 'k8s') {
                $job->tag = QueueConstant::CONSUMER_TAG_ALIBABA;
            }
            $job->setTriggerByPublicRule($this->triggerByPublicRule);
            if ($dataList) {
                $job->setReferDataList($dataList);
            }
            if ($async) {
                // alibaba consumer生成速率过高，拆分队列
//            if (getenv('RUNTIME_APP') == 'alibaba-consumer') {
//                $job->tag = QueueConstant::CONSUMER_TAG_ALIBABA;
//            }
                QueueService::dispatch($job, ['client_id' => $this->clientId]);
                if (!$this->skipUpdateSearch) {
                    try {
                        \common\library\server\es_search\SearchQueueService::pushCompanyQueue(\User::getLoginUser() ? \User::getLoginUser()->getUserId() : 0, $this->clientId, (array)$referIds,\Constants::SEARCH_INDEX_TYPE_UPDATE_NEW_COMPANY, true);
                    } catch (\Throwable $e) {
                        \LogUtil::exception($e, ['error_hint' => 'company update push index', 'swarmType' => $this->swarmType,]);
                    }
                }
            } else {
                try {
                    $job->handle();
                } catch (\Throwable $e) {
                    \LogUtil::exception($e, compact('referIds', 'changeFields'));
                    ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());
                }
            }
        }
    
        if (empty($changeFields) || array_intersect($changeFields, ['user_id', 'pool_id'])) {
        
            (new ConflictService($this->clientId, \Constants::TYPE_COMPANY))->detectByRefer(array_merge(...array_values($referList)), ConflictConstants::RESOLVE_TYPE_COMPANY_SAVE);
        }
    }

    public function refreshDaily($async = true)
    {
        $job = new SwarmDailyRefresh($this->clientId);
        $job->setStateList([$this->clientId]);
        $job->maxTimeout = 36000;
        if ($async) {
            QueueService::dispatch($job);
        } else {
            $job->handle();
        }
    }

    public function refreshByRules($ids, $async = true)
    {
        $async = $async && !$this->isExp();
        $job = (new SwarmEdited($this->clientId, null, (array)$ids));
        $job->setSwarmType($this->swarmType);
        $job->setTriggerByPublicRule($this->triggerByPublicRule);
        if ($async) {
            QueueService::dispatch($job);
        } else {
            $job->handle();
        }
    }

    public function refreshByRulesDeleted($ids, $async = true)
    {
        $async = $async && !$this->isExp();
        $job = (new SwarmDeleted($this->clientId, $ids));
        $job->setSwarmType($this->swarmType);
        if ($async) {
            QueueService::dispatch($job);
        } else {
            $job->handle();
        }
    }

    public function refreshAll($async = true)
    {
        $async = $async && !$this->isExp();
        $job = new SwarmRefreshForClient($this->clientId);
        if ($async) {
            QueueService::dispatch($job);
        } else {
            $job->handle();
        }
    }
    
    
    public function refreshIterate($iterateFields, $module = null, $async = true) {
        
        $iterateFields = array_values(array_filter((array)$iterateFields));
        
        if (empty($iterateFields)) {
            
            return;
        }
        
        $async = $async && !$this->isExp();
    
        $user = \User::getLoginUser();
        
        $job = new SwarmRefreshIterateJob($this->clientId, $user->getUserId());
        
        $job->setIterateFields((array)$iterateFields);
      
        $job->setModule($module);
        
        !empty($this->swarmType) && $job->setSwarmType($this->swarmType);
        
        if ($async) {
            
            QueueService::dispatch($job);
            
        } else {
            
            $job->handle();
        }
    }
    
    
    public function getCacheKeyPrefix()
    {
        return self::CACHE_PREFIX;
    }

    public function cleanSwarm($swarmId)
    {
        if (empty($swarmId)) {
            return 0;
        }
    
        $tableName = CompanyHandler::RELATE_TABLE_MAP[$this->swarmType];
    
        if (is_numeric($swarmId)) {
            $sql = "delete from {$tableName} where client_id={$this->clientId} and swarm_id = {$swarmId}";
        } else {
            $sql = "delete from {$tableName} where client_id={$this->clientId} and swarm_id in (" . implode(',', $swarmId) . ")";
        }
        $db = \PgActiveRecord::getDbByClientId($this->clientId);
        $count = $db->createCommand($sql)->execute();
    
        \LogUtil::info("clean all relate", [
            'clientId'  => $this->clientId,
            'swarmId'   => $swarmId,
            'sql'       => $sql,
            'params'    => [],
            'result'    => $count,
            'swarmType' => $this->swarmType,
        ]);

        return $count;
    }
    
    /**
     * @deprecated
     */
    public function cleanRefer($referIds)
    {
        if (empty($referIds)) {
            return 0;
        }
        $tableName = CompanyHandler::RELATE_TABLE;
        if (is_numeric($referIds)) {
            $sql = "delete from {$tableName} where client_id={$this->clientId} and company_id = {$referIds}";
        } else {
            $sql = "delete from {$tableName} where client_id={$this->clientId} and company_id in (" . implode(',', $referIds) . ")";
        }
        $db = \PgActiveRecord::getDbByClientId($this->clientId);
        $count = $db->createCommand($sql)->execute();
    
        \LogUtil::info("clean all relate for refer", [
            'clientId'  => $this->clientId,
            'referIds'  => $referIds,
            'sql'       => $sql,
            'params'    => [],
            'result'    => $count,
            'swarmType' => $this->swarmType,
        ]);

        return $count;
    }
    
    /**
     * @deprecated
     */
    public function buildParamsBySwarm($swarmId, $alias = '')
    {
        $ruleInfo = Api::swarm($this->clientId)->cachedList($swarmId)[$swarmId] ?? [];

        return $this->buildParams($ruleInfo, $alias);
    }

    public function hasFilterWithCustomer($ruleInfo)
    {
        $usingCustomerFilter = false;
        foreach (($ruleInfo['filters'] ?? []) as $filter) {
            if (($filter['refer_type'] ?? null) == \Constants::TYPE_CUSTOMER) {
                $usingCustomerFilter = true;
                break;
            }
        }

        return $usingCustomerFilter;
    }

    public function getRuleInfoByTempSwarm($swarmId)
    {
        return $this->getCache()->get($this->getCompanyListCacheKey($swarmId)) ?: [];
    }

    public function buildParamsByFilters($filters, $criteriaType, $criteria = '', $alias = '', $userId = 0, $returnRule = false)
    {
        if (!$criteria) {
            $i = 1;
            foreach ($filters as &$filter) {
                $filter['filter_no'] = $i++;
            }
        }
        $criteria = WorkflowCriteriaBuilder::buildCriteria($filters, $criteriaType, $criteria);

        if ($returnRule) {
            return [
                'filters'=> $filters,
                'criteria_type' => $criteriaType,
                'criteria' => $criteria,
                'user_id' => $userId
            ];
        }

        return $this->buildParams(
            [
                'filters' => $filters,
                'criteria' => $criteria,
                'user_id' => $userId,
            ],
            $alias
        );
    }

    public function createTempSwarm($filters, $criteriaType, $criteria = '', $userId = 0, $alias = '')
    {
        (new Validator([
            Swarm::EXTERNAL_KEY_CRITERIA_TYPE => $criteriaType,
            Swarm::EXTERNAL_KEY_CRITERIA => $criteria,
            Swarm::EXTERNAL_KEY_FILTERS => $filters,
        ], [
            Swarm::EXTERNAL_KEY_CRITERIA_TYPE => 'required|in:' . implode(',', [
                    WorkflowConstant::CRITERIA_TYPE_CUSTOM,
                    WorkflowConstant::CRITERIA_TYPE_AND,
                    WorkflowConstant::CRITERIA_TYPE_OR,
                    WorkflowConstant::CRITERIA_TYPE_NULL,
                ]),
            Swarm::EXTERNAL_KEY_CRITERIA => 'required_if:criteria_type,' . WorkflowConstant::CRITERIA_TYPE_CUSTOM,
            Swarm::EXTERNAL_KEY_FILTERS . '.*.filter_no' => 'not_empty|numeric',
            Swarm::EXTERNAL_KEY_FILTERS => 'swarm_rule_filter:' . implode(',', [$this->clientId, \Constants::TYPE_COMPANY]),
        ], []))->validate();

        $ruleInfo = $this->buildParamsByFilters($filters, $criteriaType, $criteria, $alias, $userId, true);
        $tempSwarmId = $this->clientId . md5(json_encode([$filters, $criteriaType, $criteria, $this->swarmType]));
        $this->getCache()->set($this->getCompanyListCacheKey($tempSwarmId), $ruleInfo, 'EX', 60 * 30);

        return $tempSwarmId;
    }

    public function buildParams($ruleInfo, $alias = '')
    {
        $filter = new WorkflowFilterRunner($ruleConfig = new SwarmConfig($this->clientId, $this->referType, [], $ruleInfo));
        if ((empty($ruleInfo['filters']) || empty($ruleInfo['criteria'])) && (empty($ruleInfo['extra_filters']) || empty($ruleInfo['extra_criteria']))) {
            return ['', []];
        }
        $filter->setFilters($ruleInfo['filters'], $ruleInfo['criteria']);
        !empty($ruleInfo['extra_criteria']) && $filter->addExtraWorkflowFilters($ruleInfo['extra_filters'], $ruleInfo['extra_criteria']);
        $alias = str_replace('.', '', $alias);
        $filter->setAliasMap([
            \Constants::TYPE_COMPANY => $alias
        ]);

        return $filter->buildParams();
    }

    public function refreshForPublicRule($ruleIds, $removeSwarmIds = [], $async = true)
    {
        $async = $async && !$this->isExp();
        $job = new PublicRuleRuleChanged($this->clientId, $ruleIds, $removeSwarmIds);
        if ($async) {
            QueueService::dispatch($job);
        } else {
            $job->handle();
        }
    }

    public function refreshForPublicRuleDeleted($ruleIds, $async = true)
    {
        $async = $async && !$this->isExp();
        $job = new PublicRuleRuleDeleted($this->clientId, $ruleIds);
        if ($async) {
            QueueService::dispatch($job);
        } else {
            $job->handle();
        }
    }

    public function refreshForPublicRuleByRefer($referIds, $async = true)
    {
        $async = $async && !$this->isExp() && !$this->isCli();
        $job = new PublicRuleReferChanged($this->clientId, $referIds);
        if ($async) {
            QueueService::dispatch($job);
        } else {
            try {
                $job->handle();
            } catch (\Throwable $e) {
                \LogUtil::exception($e, compact('referIds'));
                ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());
            }
        }
    }

    public function refreshForPublicRuleForClient($async = true)
    {
        $async = $async && !$this->isExp();
        $job = new PublicRuleRefreshForClient($this->clientId);
        if ($async) {
            QueueService::dispatch($job);
        } else {
            $job->handle();
        }
    }

    public function isExp()
    {
        return \Yii::app()->params['env'] == 'exp';
    }

    protected function isCli()
    {
        return php_sapi_name() == "cli";
    }
    
    /**
     * @param       $clientId
     * @param array $swarmIds
     * @param int   $swarmType
     * @return array|
     * @throws \CDbException
     * @throws \CException
     * @throws \ProcessException
     */
    public static function getAllCompanySwarmIds($clientId, array $swarmIds = [], int $swarmType = ItemSettingConstant::ITEM_TYPE_SWARM)
    {
    
        $table = CompanyHandler::RELATE_TABLE_MAP[$swarmType];
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $where = " company_id = 0 and client_id={$clientId} ";
        if (count($swarmIds)) {
            $where .= " AND swarm_id in (" . implode(',', $swarmIds) . ")";
        }
        $allCompanySql = "select swarm_id from {$table} where $where";

        return $db->createCommand($allCompanySql)->queryColumn();
    }

    public static function buildSwarmFilterJoin($clientId, $swarmId, $companyAlias, $relateAlias = '', int $swarmType = ItemSettingConstant::ITEM_TYPE_SWARM)
    {
        $table = CompanyHandler::RELATE_TABLE_MAP[$swarmType];
        $relateAlias = $relateAlias ?: 'rcs';
        $joinSql = " inner join {$table} as {$relateAlias} on {$companyAlias}.company_id={$relateAlias}.company_id and {$relateAlias}.swarm_id={$swarmId}";

        return $joinSql;
    }

    /**
     * @param bool $enableCache
     */
    public function setEnableCache(bool $enableCache)
    {
        $this->enableCache = $enableCache;
        return $this;
    }

    /**
     * @return bool
     */
    public function isEnableCache()
    {
        return $this->enableCache;
    }
    
    /**
     * @return int
     */
    public function getSwarmType(): int {
        
        return $this->swarmType;
    }
    
    /**
     * @param  $swarmType
     */
    public function setSwarmType($swarmType) {
        
        $this->swarmType = $swarmType;
        
        return $this;
    }
    
    /**
     * @param $swarmType
     * @param $clientId
     * @param $userId
     * @return PublicSwarmApi|SwarmApi|AiSwarmApi
     */
    public static function getSwarmApi($swarmType, $clientId, $userId = 0) {
    
        (new Validator([
            'swarmType' => $swarmType,
            'clientId'  => $clientId,
        ], [
            'swarmType' => 'required|not_empty|in:' . implode(',', array_keys(Swarm::SWARM_TYPE_MAP)),
            'clientId'  => 'required|not_empty|numeric',
        ]))->validate();
        
        $swarm = Swarm::SWARM_TYPE_MAP[$swarmType]['name'];
        
        return Api::$swarm($clientId, $userId);
    }
    
    /**
     * @param $swarmType
     * @param $clientId
     * @param $userId
     * @return PublicSwarm|Swarm
     */
    public static function getNewSwarm($swarmType, $clientId, $userId = 0) {
        
        $api = self::getSwarmApi($swarmType, $clientId, $userId);
        
        $obj = $api->getMetadata()::singeObject();
        
        return (new $obj($clientId));
    }
    
    public static function getRuleConfigType($swarmType) {
    
        (new Validator([
            'swarmType' => $swarmType,
        ], [
            'swarmType' => 'required|not_empty|in:' . implode(',', array_keys(Swarm::SWARM_TYPE_MAP)),
        ]))->validate();
        
        return Swarm::SWARM_TYPE_MAP[$swarmType]['rule_config'];
    }
    
    public static function getRuleConfigExtraParam($swarmType) {
        
        return [
            
            self::getRuleConfigType($swarmType),
            
        ];
    }
    
    public static function getSwarmUserNum($swarmType) {
        
        (new Validator([
            'swarmType' => $swarmType,
        ], [
            'swarmType' => 'required|not_empty|in:' . implode(',', array_keys(Swarm::SWARM_TYPE_MAP)),
        ]))->validate();
        
        return Swarm::SWARM_TYPE_MAP[$swarmType]['user_num'];
    }
    
    public static function getSwarmExtraUserFilter($swarmType) {
        
        return Swarm::SWARM_TYPE_MAP[$swarmType]['extra_user_filter'] ?? [];
    }
    
    
    public static function getSwarmCachedRuleList($swarmType, $clientId, $referIds = []) {
    
        $referIds = array_filter((array)$referIds);
        
        if (!empty($swarmType)) {
            
            $api = self::getSwarmApi($swarmType, $clientId);
            
            $ruleList = $api->cachedList($referIds);
            
        } else {
            
            $swarmApi = new SwarmApi($clientId);
            
            $publicSwarmApi = new PublicSwarmApi($clientId);
    
            $swarmList = $swarmApi->cachedList([]);
            
            $publicSwarmList = $publicSwarmApi->cachedList([]);
            
            $ruleList = array_merge($swarmList, $publicSwarmList);
        }
        
        return $ruleList;
    }

    /**
     * @return array
     */
    public function getCachedFilterField() {
        
        $field = [];
        
        $type = !empty($this->swarmType) ? [$this->swarmType] : array_keys(Swarm::SWARM_TYPE_MAP);
        
        foreach ($type as $swarmType) {
            
            $field = array_merge($field, self::getSwarmApi($swarmType, $this->clientId)->getCachedFilterField());
        }
        
        return array_values(array_filter(array_unique($field)));
    }
    
}
