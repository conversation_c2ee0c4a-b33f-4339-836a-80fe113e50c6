<?php

namespace common\library\mail;

class MultiMailContentList extends \MysqlList
{

    protected $clientId = null;
    protected $userId = null;
    protected $contentType = null;
    protected $mailId = null;
    private $fields = null;

    public function __construct($client_id = null, $user_id = null, $mail_id = null, $content_type = null)
    {

        if (!$user_id) {
            $user_id = \User::getLoginUser()->getUserId();
        }

        if (!$client_id) {
            $client_id = \User::getUserObject($user_id)->getClientId();
        }

        if (empty($mail_id)) {
            throw new \RuntimeException('mail_id is required');
        }

        if (empty($content_type)) {
            throw new \RuntimeException('content_type is required');
        }

        $this->mailId = $mail_id;
        $this->userId = $user_id;
        $this->contentType = $content_type;
        $this->clientId = $client_id;
        $this->formatter = new MultiMailContentFormatter();
        $this->setOrder('asc');
        $this->setOrderBy('content_id');
    }

    public function setFields($fields)
    {
        if (is_array($fields)) {
            $fields = implode(',', $fields);
        }
        $this->fields = $fields;
    }

    public function find()
    {
        list($where, $params) = $this->buildParam();

        if (empty($where)) {
            return [];
        }

        $db = \ProjectActiveRecord::getDbByClientId($this->clientId);


        $orderBy = $this->buildOrderBy();
        $limit  = $this->buildLimit();

        $table = \MultiMailContent::model()->tableName();
        if (empty($this->fields)) {
            $fields = '*';
        } else {
            $fields = $this->fields;
        }

        $sql = "SELECT $fields FROM {$table}  WHERE {$where} {$orderBy} {$limit}";

        $command = $db->createCommand($sql);
        $data = $command->queryAll(true, $params);
        if (!empty($this->fields))
            return $data;

        $this->formatter->setListData($data);
        return $this->formatter->result();

    }

    public function count()
    {
        list($where, $params) = $this->buildParam();

        if (empty($where)) {
            return 0;
        }

        $db = \ProjectActiveRecord::getDbByClientId($this->clientId);

        $table = \MultiMailContent::model()->tableName();

        $sql = "SELECT COUNT(*) FROM {$table} WHERE {$where}";

        $command = $db->createCommand($sql);

        return $command->queryScalar($params);
    }

    private function buildParam()
    {
        $where = [];
        $params = [];

        if ($this->clientId) {
            $where[] = 'client_id = :client_id';
            $params[':client_id'] = $this->clientId;
        }

        if ($this->userId) {
            $where[] = 'user_id = :user_id';
            $params[':user_id'] = $this->userId;
        }

        if ($this->mailId) {
            $where[] = 'mail_id = :mail_id';
            $params[':mail_id'] = $this->mailId;
        }

        if ($this->contentType) {
            $where[] = 'content_type = :content_type';
            $params[':content_type'] = $this->contentType;
        }

        return [implode(' AND ', $where), $params];
    }
}
