<?php


namespace common\library\mail;


use common\models\client\UserMailStatus;

class MailAsyncTaskFormatter extends \ListItemFormatter
{
    protected $userId;
    protected $specifyFields;

    protected $showExposeInfo = false;

    public function __construct($userId)
    {
        $this->userId = $userId;
    }

    public function setSpecifyFields($fields)
    {
        $this->specifyFields = $fields;
    }

    public function setShowExposeInfo($showExposeInfoFlag)
    {
        $this->showExposeInfo = $showExposeInfoFlag;
    }

    public function buildMapData()
    {
        //初始化Map
        $exposeMap = [];
        $mailBaseInfoMap = [];
        $needSelectUserMailConfig = false;
        $list = $this->batchFlag ? $this->listData : [$this->data];

        $mailIds = array_column($list, 'mail_id');
        if (empty($mailIds)) return;

        //邮件额外信息map
        $mailExternalMap = \MailExternal::findByMailIds($mailIds);

        //群发单显map
        if ($this->showExposeInfo) {
            $exposeMailIds = [];
            foreach ($mailExternalMap as $mailId => $item) {
                if ($item['expose_flag'] ?? 0) $exposeMailIds[] = $mailId;
            }
            $exposeMap = !empty($exposeMailIds) ?  \MailExpose::findByMailIds($exposeMailIds) : [];
            foreach ($exposeMap as $exposeInfo){
                if($exposeInfo['suspend_flag'] > 0){
                    $needSelectUserMailConfig = true;
                }
            }
        }

        //邮件基本信息
        $mailList = new MailList(null, $this->userId);
        $mailList->setMailIds($mailIds);
        $mailList->setFields(['mail_id', 'subject', 'time_flag', 'folder_id', 'mail_type', 'create_time', 'receiver', 'sender','user_mail_id']);
        $mailData = $mailList->find();
        foreach ($mailData as $item) {
            $mailBaseInfoMap[$item['mail_id']] = $item;
        }

        $userMailInfoMap = [];
        $mailToUserMailStatusMap = [];

        if ($needSelectUserMailConfig){
            $userMailIdMap = array_column($mailBaseInfoMap,'user_mail_id','mail_id');
            $userMailList = new \UserMailList();
            $userMailList->setUserId($this->userId);
            $userMailList->setUserMailId(array_unique(array_values($userMailIdMap)));
            $userMailList->setShowBindMailInfo(true);
            $userMailInfo = $userMailList->find();
            $userMailInfo = array_column($userMailInfo,null, 'user_mail_id');

            $allUserMailIds = [];
            foreach ($userMailInfo as $item) {
                $allUserMailIds = array_merge($allUserMailIds, [$item['user_mail_id'], $item['source_user_mail_id']]);
            }
            $userMailStatusMap = \UserMailStatus::findByUserMailIds(array_unique(array_values($allUserMailIds)));
            foreach ($userMailIdMap as $mail_id => $user_mail_id) {
                if (!isset($userMailInfo[$user_mail_id])) continue;
               $trueUserMailId = $userMailInfo[$user_mail_id]['source_user_mail_id'] ?: $user_mail_id;
               $userMailInfoMap[$mail_id] = $userMailInfo[$user_mail_id];
               $mailToUserMailStatusMap[$mail_id] = $userMailStatusMap[$trueUserMailId] ?? [];
            }
        }

        $map = [
            'external' => $mailExternalMap,
            'expose' => $exposeMap,
            'mail_base_info' => $mailBaseInfoMap,
            'user_mail_info' => $userMailInfoMap,
            'user_mail_status' => $mailToUserMailStatusMap,
        ];
        $this->setMapData($map);
        return;
    }

    public function produceBaseInfo($data, $specifyFields = null)
    {
        $result = [];
        $mailId = $data['mail_id'];

        if ($specifyFields === null) {
            $specifyFields = $this->specifyFields;
            if ($this->specifyFields === null) $specifyFields = array_keys($data);
        }

        $mailInfo = $this->getMapData('mail_base_info', $mailId);
        if ($mailInfo === null) {
            return $result;
        }
        foreach ($specifyFields as $field) {
            switch ($field) {
                case 'total':
                    $result['total'] = count(\common\library\email\Util::findAllEmails($mailInfo['receiver']));
                    break;
                case 'subject':
                    $result['subject'] = $mailInfo['subject'] ?? '';
                    break;
                case 'time':
                    $isTiming = Helper::isTiming($mailInfo['folder_id'], $mailInfo['time_flag']);
                    if (!$isTiming) $result['time'] = $mailInfo['create_time'];
                    else {
                        $result['time'] = $data['send_time'] ?? '';
                    }
                    break;
                default:
                    $result[$field] = $data[$field];
            }
        }
        return $result;
    }

    public function format($data)
    {
        $result = [];
        $mailId = $data['mail_id'];
        $result = array_merge($result, $this->produceBaseInfo($data));

        //显示群发单显信息
        if ($this->showExposeInfo) {
            $mailInfo = $this->getMapData('mail_base_info', $mailId);
            $exposeInfo = $this->getMapData('expose', $mailId);
            $externalInfo = $this->getMapData('external', $mailId);
            $userMailStatusInfo = $this->getMapData('user_mail_status', $mailId);
            $exposeFlag = $externalInfo['expose_flag'] ?? 0;
            if ($exposeFlag) {
                $subMailSet = $exposeInfo['sub_mail_set'] ?? '';
                $result['processedCount'] = empty($subMailSet) ? 0 : count(explode(',', $subMailSet));
                $result['expose_suspend_flag'] = $exposeInfo['suspend_flag'] ?? 0;
                $result['user_mail_suspend_end_time'] = date("Y-m-d H:i:s", strtotime($userMailStatusInfo['suspend_end_time'] ?? ''));
                $result['sender'] = $mailInfo['sender'];
                if ($result['expose_suspend_flag'] == 1){
                    $expose_suspend_msg = json_decode($exposeInfo['suspend_msg'],true) ?? [];
                    $result['suspend_mail_id'] = $expose_suspend_msg['mailId'] ?? '';
                }
                if ($result['expose_suspend_flag'] == 2){
                    $result['user_mail_info'] = $this->getMapData('user_mail_info' ,$mailId) ?? [];
                }
            }

            $result['error_msg'] = '';
            if(isset($exposeInfo['expose_user_mail_error']))
            {
                $exposeUserMailError = json_decode($exposeInfo['expose_user_mail_error'],true) ?? [];
                if(isset($exposeUserMailError['type']) && $exposeUserMailError['type'] == 'UserNetworkNotAvailable')
                {
                    $result['error_msg'] = $exposeUserMailError['msg'] ?? '';
                }
            }

            // 是否是桌面端群发单显邮件
            $result['is_desktop_expose'] = 0;
            if(isset($exposeInfo['expose_device_type']) && isset($exposeInfo['expose_user_mail_info']) )
            {
                $result['is_desktop_expose'] = 1;
            }
        }

        return $result;
    }

    public function listInfoSetting()
    {
        $this->setSpecifyFields(['mail_id', 'total', 'subject', 'time', 'group_flag']);
        $this->setShowExposeInfo(true);
    }
}