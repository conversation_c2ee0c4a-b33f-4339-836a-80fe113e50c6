<?php
/**
 * Copyright (c) 2012 - 2017 <PERSON><PERSON>.All Rights Reserved
 * Author: nuxse
 * Data: 2017/10/26
 */

namespace common\library\mail\strategy;


use common\library\mail\MailList;

class PinListStrategy extends AbstractListStrategy
{

    private $user_mail_id;

    public function __construct($params)
    {
        parent::__construct($params);

        $this->user_mail_id = empty($params['user_mail_id']) ? null : $params['user_mail_id'];
    }

    public function setting(MailList $mailList)
    {
       $mailList->setIsPin(true);

        //获取用户的自定义文件
        $folderListObj = new \common\library\mail\setting\folder\MailFolderList(\User::getLoginUser()->getUserId());
        $folderListObj->setFields('folder_id');
        $folderList = $folderListObj->find();
        $folderIdList = array_column($folderList,'folder_id');

        $folderIdList = array_merge($folderIdList,[
            \Mail::FOLDER_INBOX_ID,
            \Mail::FOLDER_SEND_ID
        ]);
        $mailList->setFolderIds($folderIdList);
        $mailList->setUserMailId($this->user_mail_id);
    }

}