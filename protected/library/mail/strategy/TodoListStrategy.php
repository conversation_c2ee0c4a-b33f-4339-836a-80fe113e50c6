<?php
/**
 * Created by PhpStorm.
 * User: beenzhang
 * Date: 2018/3/27
 * Time: 下午8:15
 */
namespace common\library\mail\strategy;

use common\library\mail\MailList;

class TodoListStrategy extends AbstractListStrategy
{
    use ArchiveTrait;
    private $completedFlag;
    private $user_mail_id;
    private $showArchiveFlag;
    private $pin_flag;
    private $expire_type;
    public function __construct(array $params = [])
    {
        parent::__construct($params);
        $this->completedFlag = $params['todo_completed_flag'] ? boolval($params['todo_completed_flag']) : false;
        $this->user_mail_id = empty($params['user_mail_id']) ? null : $params['user_mail_id'];
        $this->showArchiveFlag = empty($params['show_archive_flag']) ? null : $params['show_archive_flag'];
        $this->pin_flag = $params['pin'] ? intval($params['pin']) : null;
        $this->expire_type = !empty($params['expire_type']) ? $params['expire_type'] : null;
    }

    public function setting(MailList $mailList)
    {
        $mailList->setTodoCompleteFlag($this->completedFlag);
        $mailList->setUserMailId($this->user_mail_id);
        $mailList->setNotInFolderIds([\Mail::FOLDER_DRAFT_ID, \Mail::FOLDER_DELETE_ID]);
        $mailList->setDeleteFlag(\Mail::DELETE_FLAG_NONE);
        $mailList->setShowArchiveFlag($this->showArchiveFlag);
        $mailList->setIsPin($this->pin_flag);
        $mailList->setExpireType($this->expire_type);
        $this->archive($mailList);
    }

}