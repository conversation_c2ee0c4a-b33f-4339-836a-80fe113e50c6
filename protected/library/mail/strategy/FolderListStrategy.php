<?php
/**
 * Copyright (c) 2012 - 2017 Xiaoman.All Rights Reserved
 * Author: nuxse
 * Data: 2017/10/26
 */

namespace common\library\mail\strategy;


use common\library\mail\MailList;

class FolderListStrategy extends AbstractListStrategy
{
    use ArchiveTrait;
    private $folder_id;
    private $pin_flag;//移除旧版之后 pin_flag的设置可以去掉 其他策略类同
    private $user_mail_id;

    public function __construct($params)
    {
        parent::__construct($params);

        if (!isset($params['folder_id']) || $params['folder_id'] == '') {
            throw new \RuntimeException('folder_id参数错误',1270);
        }

        $this->folder_id = intval($params['folder_id']);
        $this->pin_flag = $params['pin'] ? intval($params['pin']) : null;
        $this->user_mail_id = empty($params['user_mail_id']) ? null : $params['user_mail_id'];
    }

    public function setting(MailList $mailList)
    {
        $mailList->setFolderIds($this->folder_id);
        $this->pin_flag && $mailList->setIsPin($this->pin_flag);
        $mailList->setUserMailId($this->user_mail_id);
        $this->archive($mailList);
    }

}
