<?php
/**
 * Copyright (c) 2012 - 2017 Xiaoman.All Rights Reserved
 * Author: nuxse
 * Data: 2017/8/8
 */

namespace common\library\mail;


use common\library\chat_assistant\Constants;
use common\library\email\Util;
use common\library\email_identity\cards\Card;
use common\library\mail\draft\MailDraftContentList;

use common\library\mail\setting\todo\MailTodoList;
use common\library\util\Speed;

class MailFormatter extends \ListItemFormatter
{
    //显示配置
    private $showContent = false;
    private $showPlainText = false;
    private $showSummary = false;
    private $showCompanySummary = false;
    private $showAttachList = false;
    private $showTagList = false;
    private $showTrack = false;
    private $showTrackDetail = false;
    private $showDistributeList = false;
    private $showPinFlag = false;
    private $showContactSummary = false;
    private $showFailErrorMessage = false;
    private $showHasTrack = false;
    private $showForwardList = false;
    private $showLockInfo = false;
    private $showPlanSendTime = false;

    private $showIsOwner = false;
    private $emailNickReplace = false;
    private $emailNicknameReplaceFirst = false;
    private $showMailHighLight = false;
    private $mailHighLightMap = [];

    private $showApprovalFlowInfo = false;
    private $showApprovalFlowEventInfo = false;

    private $showCompanyInfo = false;
    private $showAlibabaInfoFlag = false;

    //暂时app独有
    private $showMailTask = false;
    private $showTrackInfo = false;
    private $appShowMailCardList = false;
    private $appShowMailCardMap = false;

    private $showMailCardMap = false;
    private $mailCardDirectOwner = true;

    // 是否以邮件所有者视角来显示邮箱身份
    private $mailCardUseDirectOwner = true;

    private $showTodo = false;
    private $showLargeAttach = false;

    private $showRisk = false;

    private $showInquiry = false;
    private $inquirySenderReplace = false;
    private $showCaptureCard = false;
    private $showReceiveOriginSender = false;
    private $showFolderName = false;
    private $showReceiptInfo = false;
    private $showMailExposeInfo = false;
    private $showUsedTradeDocument = false; // TMS
    private $showConversationInfo = false;
    private $showConferenceInfo = false; // 会议信息
    private $showRemark = false;

    private $showMailLanguage = false;
    /**
     * 显示字段
     * @var
     */
    private $specifyFields;
    private $showContactSummaryForMailConversation = false;//会话邮件列表显示发件人身份以及昵称

    private $showTopFlag = false;

    private $topMailIdInfo = [];

    private $showMultiContentFlag = false;

    public function __construct()
    {

    }

    /**
     * 设置显示字段
     * @param $fields
     */
    public function setSpecifyFields($fields)
    {
        $this->specifyFields = $fields;
    }

    public function setShowInquiry($showInquiry)
    {
        $this->showInquiry = $showInquiry;
    }

    public function setInquirySenderReplace($flag)
    {
        $this->inquirySenderReplace = $flag;
    }

    public function setShowCaptureCard($flag)
    {
        $this->showCaptureCard = $flag;
    }

    /**
     * @param bool $showContent
     */
    public function setShowContent(bool $showContent)
    {
        $this->showContent = $showContent;
    }

    /**
     * @param mixed $showPlainText
     */
    public function setShowPlainText($showPlainText)
    {
        $this->showPlainText = $showPlainText;
    }

    /**
     * @param bool $showSummary
     */
    public function setShowSummary(bool $showSummary)
    {
        $this->showSummary = $showSummary;
    }

    public function setShowCompanySummary(bool $showCompanySummary)
    {
        $this->showCompanySummary = $showCompanySummary;
    }

    /**
     * @param bool $showAttachList
     */
    public function setShowAttachList(bool $showAttachList)
    {
        $this->showAttachList = $showAttachList;
    }

    /**
     * @param bool $showTagList
     */
    public function setShowTagList(bool $showTagList)
    {
        $this->showTagList = $showTagList;
    }

    /**
     * @param bool $showTrack
     */
    public function setShowTrack(bool $showTrack)
    {
        $this->showTrack = $showTrack;
    }


    /**
     * @param bool $showTrackDetail
     */
    public function setShowTrackDetail(bool $showTrackDetail)
    {
        $this->showTrackDetail = $showTrackDetail;
    }

    /**
     * @param bool $showDistributeList
     */
    public function setShowDistributeList(bool $showDistributeList)
    {
        $this->showDistributeList = $showDistributeList;
    }

    /**
     * @param bool $showPinFlag
     */
    public function setShowPinFlag(bool $showPinFlag)
    {
        $this->showPinFlag = $showPinFlag;
    }

    /**
     * @param bool $showContactSummary
     */
    public function setShowContactSummary(bool $showContactSummary)
    {
        $this->showContactSummary = $showContactSummary;
    }

    /**
     * @param bool $showFailErrorMessage
     */
    public function setShowFailErrorMessage(bool $showFailErrorMessage)
    {
        $this->showFailErrorMessage = $showFailErrorMessage;
    }

    /**
     * @param bool $showHasTrack
     */
    public function setShowHasTrack(bool $showHasTrack)
    {
        $this->showHasTrack = $showHasTrack;
    }

    /**
     * @param bool $showTrackInfo
     */
    public function setShowTrackInfo(bool $showTrackInfo)
    {
        $this->showTrackInfo = $showTrackInfo;      //兼容APP端
    }


    /**
     * @param bool $emailNickReplace
     */
    public function setEmailNickReplace(bool $emailNickReplace)
    {
        if ($emailNickReplace)
            $this->emailNicknameReplaceFirst = false; // 如果全替换 就不用处理单个替换
        $this->emailNickReplace = $emailNickReplace;
    }

    public function setEmailNicknameReplaceFirst(bool $flag)
    {
        $this->emailNicknameReplaceFirst = $flag;
    }

    public function setShowForwardList(bool $showForwardList)
    {
        $this->showForwardList = $showForwardList;
    }

    /**
     * @param bool $showMailHighLight
     */
    public function setShowMailHighLight(bool $showMailHighLight)
    {
        $this->showMailHighLight = $showMailHighLight;
    }


    public function setMailHighLightMap(array $mail_high_light_map)
    {
        $this->mailHighLightMap = $mail_high_light_map;
    }

    public function setShowMailExposeInfo(bool $showMailExposeInfo){
        $this->showMailExposeInfo = $showMailExposeInfo;
    }

    public function setShowRemark(bool $showRemark){
        $this->showRemark = $showRemark;
    }

    public function getMailHighLightMap()
    {
        return $this->mailHighLightMap;
    }

    /**
     * @param bool $showMailTask
     */
    public function setShowMailTask(bool $showMailTask)
    {
        $this->showMailTask = $showMailTask;
    }

    /**
     * @param bool $showAlibabaInfoFlag
     */
    public function setShowAlibabaInfoFlag(bool $showAlibabaInfoFlag)
    {
        $this->showAlibabaInfoFlag = $showAlibabaInfoFlag;
    }

    public function setShowMailCardMap(bool $showMailCardMap, bool $directOwner = true)
    {
        $this->showMailCardMap = $showMailCardMap;
        $this->mailCardDirectOwner = $directOwner;
    }

    public function setAppShowMailCardList(bool $showMailCardList, bool $directOwner = true)
    {
        $this->appShowMailCardList = $showMailCardList;
        $this->mailCardDirectOwner = $directOwner;
    }

    public function setAppShowMailCardMap(bool $showMailCardMap, bool $directOwner = true)
    {
        $this->appShowMailCardMap = $showMailCardMap;
        $this->mailCardDirectOwner = $directOwner;
    }

    /**
     * @param bool $showCompanyInfo
     */
    public function setShowCompanyInfo(bool $showCompanyInfo)
    {
        $this->showCompanyInfo = $showCompanyInfo;
    }

    public function setShowTodo(bool $showTodo){
        $this->showTodo = $showTodo;
    }

    public function setShowLargeAttach(bool $showLargeAttach){
        $this->showLargeAttach = $showLargeAttach;
    }

    /**
     * @param bool $showRisk
     */
    public function setShowRisk(bool $showRisk)
    {
        $this->showRisk = $showRisk;
    }

    public function setShowLockInfo(bool $showLockInfo)
    {
        $this->showLockInfo = $showLockInfo;
    }

    /**
     * 显示审批流信息
     * @param bool $showApprovalFlowInfo
     */
    public function setShowApprovalFlowInfo(bool $showApprovalFlowInfo)
    {
        $this->showApprovalFlowInfo = $showApprovalFlowInfo;
    }

    /**
     * 显示审批进度
     * @param bool $showApprovalFlowEventInfo
     */
    public function setShowApprovalFlowEventInfo(bool $showApprovalFlowEventInfo)
    {
        $this->showApprovalFlowEventInfo = $showApprovalFlowEventInfo;
    }

    /**
     * 显示邮件所属人关系
     * @param bool $showIsOwner
     */
    public function setShowIsOwner(bool $showIsOwner)
    {
        $this->showIsOwner = $showIsOwner;
    }

    //显示计划发送时间
    public function setShowPlanSendTime(bool $showPlanSendTime)
    {
        $this->showPlanSendTime = $showPlanSendTime;
    }

    public function setShowReceiptInfo(bool $showReceiptInfo)
    {
        $this->showReceiptInfo = $showReceiptInfo;
    }

    /**
     * 显示邮件的原始发件人
     * @param bool $showReceiveOriginSender
     */
    public function setReceiveOriginSender(bool $showReceiveOriginSender)
    {
        $this->showReceiveOriginSender = $showReceiveOriginSender;
    }

    /**
     * 是否以邮件所有者视角来显示邮箱身份
     *
     * @param bool $useDirectOwner
     */
    public function setMailCardUseDirectOwner(bool $useDirectOwner)
    {
        $this->mailCardUseDirectOwner = $useDirectOwner;
    }

    /**
     * @param mixed $showTopFlag
     */
    public function setShowTopFlag($showTopFlag): void
    {
        $this->showTopFlag = $showTopFlag;
    }

    /**
     * @param array $topMailIdInfo
     */
    public function setTopMailIdInfo(array $topMailIdInfo): void
    {
        $this->topMailIdInfo = $topMailIdInfo;
    }

    public function baseInfoSetting()
    {
        $this->setShowSummary(true);
        $this->setShowContent(true);
        $this->setShowAttachList(true);
        $this->setEmailNickReplace(true);
    }

    /**
     * @param bool $showUsedTradeDocument
     */
    public function setShowUsedTradeDocument(bool $showUsedTradeDocument): void
    {
        $this->showUsedTradeDocument = $showUsedTradeDocument;
    }

    /**
     * @param bool $showConversationInfo
     */
    public function setShowConversationInfo(bool $showConversationInfo)
    {
        $this->showConversationInfo = $showConversationInfo;
    }

    /**
     * @param bool $showConferenceInfo
     */
    public function setShowConferenceInfo(bool $showConferenceInfo): void
    {
        $this->showConferenceInfo = $showConferenceInfo;
    }

    public function setShowMailLanguage(bool $showMailLanguage): void
    {
        $this->showMailLanguage = $showMailLanguage;
    }

    public function setShowContactSummaryForMailConversation(bool $showContactSummaryForMailConversation): void
    {
        $this->showContactSummaryForMailConversation = $showContactSummaryForMailConversation;
    }

    public function setShowMultiContentFlag(bool $showMultiContentFlag): void
    {
        $this->showMultiContentFlag = $showMultiContentFlag;
    }


    // 设置网页端邮件详细数据配置
    public function webInfoSetting()
    {
        $this->setSpecifyFields([
            'mail_id',
            'mail_type',
            'sender',
            'receiver',
            'folder_id',
            'delete_flag',
            'read_flag',
            'cc',
            'bcc',
            'subject',
            'attach_flag',
            'urgent_flag',
            'receipt_flag',
            'user_id',
            'user_mail_id',
            'create_time',
            'update_time',
            'receive_time',
            'read_flag',
            'open_flag',
            'reply_flag',
            'reply_to',
            'forward_flag',
            'delete_flag',
            'time_flag',
            'send_status',
            'client_id',
            'distribute_flag',
            'track_flag',
            'expose_flag',
            'plan_send_time',
            'sign_id',
            'imap_folder_id',
            'root_mail_id',
            'bounced_mail_id',
            'relate_company_flag',
            'delay_send_flag',
            'email_size',
            'reply_mail_id',
            'alias_id',
            'reply_to_mail_id',
            'source_mail_id',
            'conference_flag',
            'archive_flag',
        ]);
        $this->setShowSummary(true);
        $this->setShowContent(true);
        $this->setShowDistributeList(true);
        $this->setShowTrack(false);
        $this->setShowTrackDetail(false);
        $this->setShowAttachList(true);
        $this->setShowTagList(true);
        $this->setShowPinFlag(true);
        $this->setShowFailErrorMessage(true);
        $this->setEmailNickReplace(true);
        $this->setShowForwardList(true);
        $this->setShowPlainText(true);
        $this->setShowContactSummary(true);
        $this->setShowHasTrack(true);
        $this->setShowTodo(true);
        $this->setShowLargeAttach(true);
        $this->setShowRisk(true);
        $this->setShowInquiry(true);
        $this->setInquirySenderReplace(true);
        $this->setShowReceiptInfo(true);

        $this->setShowMailCardMap(true);

        $this->setShowUsedTradeDocument(true);
        $this->setShowConferenceInfo(true);
        $this->setShowMailLanguage(true);
    }

    /**
     * 设置网页端邮件详细数据配置
     */
    public function webSalesInfoSetting()
    {
        $this->setSpecifyFields([
            'mail_id',
            'mail_type',
            'sender',
            'receiver',
            'folder_id',
            'delete_flag',
            'read_flag',
            'cc',
            'bcc',
            'subject',
            'attach_flag',
            'urgent_flag',
            'receipt_flag',
            'user_id',
            'user_mail_id',
            'create_time',
            'update_time',
            'receive_time',
            'read_flag',
            'open_flag',
            'reply_flag',
            'reply_to',
            'forward_flag',
            'delete_flag',
            'time_flag',
            'send_status',
            'client_id',
            'distribute_flag',
            'track_flag',
            'expose_flag',
            'plan_send_time',
            'sign_id',
            'imap_folder_id',
            'bounced_mail_id',
            'source_mail_id',
        ]);
        $this->setShowSummary(true);
        $this->setShowContent(true);
        $this->setShowDistributeList(true);
        $this->setShowTrack(false);
        $this->setShowTrackDetail(false);
        $this->setShowAttachList(true);
        $this->setShowTagList(true);
        $this->setShowPinFlag(true);
        $this->setShowFailErrorMessage(true);
        $this->setEmailNickReplace(true);
        $this->setShowForwardList(true);
        $this->setShowPlainText(true);
        $this->setShowHasTrack(true);
        $this->setShowTodo(true);
        $this->setShowLargeAttach(true);
        $this->setShowRisk(true);
        $this->setShowTrackDetail(true);
        $this->setShowTrack(true);
    }

    /**
     * 设置网页端邮件列表配置
     */
    public function webListSetting()
    {
        $this->setSpecifyFields([
            'mail_id',
            'mail_type',
            'sender',
            'receiver',
            'reply_to',
            'folder_id',
            'delete_flag',
            'read_flag',
            'cc',
            'bcc',
            'subject',
            'attach_flag',
            'urgent_flag',
            'receipt_flag',
            'receive_time',
            'create_time',
            'update_time',
            'send_status',
            'track_flag',
            'user_id',
            'time_flag',
            'reply_flag',
            'reply_mail_id',
            'reply_time',
            'forward_flag',
            'user_mail_id',
            'root_mail_id',
            'bounced_mail_id',
            'relate_company_flag',
            'reply_to_mail_id',
            'source_mail_id',
            'conference_flag',
            'archive_flag'
        ]);
        $this->setShowSummary(true);
        $this->setShowCompanySummary(true);
        $this->setShowTagList(true);
        $this->setShowPinFlag(true);
        $this->setShowContactSummary(true);
        $this->setEmailNicknameReplaceFirst(true);
        // TODO 旧版本的邮箱列表需要返回附件 移除旧版本后附件可以去掉
        $this->setShowAttachList(true);
        $this->setShowHasTrack(true);
        $this->setShowTodo(true);
        $this->setShowInquiry(true);
        $this->setInquirySenderReplace(true);
        $this->setShowIsOwner(true);
        $this->setShowForwardList(true);
        $this->setShowRisk(true);
    }

    public function webAdvanceSearchSetting()
    {
        $this->setSpecifyFields([
            'mail_id',
            'mail_type',
            'sender',
            'receiver',
            'reply_to',
            'folder_id',
            'delete_flag',
            'read_flag',
            'cc',
            'bcc',
            'subject',
            'attach_flag',
            'urgent_flag',
            'receipt_flag',
            'receive_time',
            'update_time',
            'send_status',
            'track_flag',
            'user_id',
            'time_flag',
            'reply_flag',
            'reply_mail_id',
            'forward_flag',
            'user_mail_id',
            'relate_company_flag',
            'bounced_mail_id',
            'source_mail_id',
            'reply_to_mail_id',
            'conference_flag',
            'archive_flag'
        ]);
        $this->setShowPinFlag(true);
        $this->setShowSummary(true);
        $this->setShowTagList(true);
        $this->setShowContactSummary(true);
        $this->setShowCompanySummary(true);
        $this->setShowPinFlag(true);
        $this->setEmailNicknameReplaceFirst(true);
        $this->setShowMailHighLight(true);
        //旧版本的邮箱列表需要返回附件 移除旧版本后附件可以去掉
        $this->setShowAttachList(true);
        $this->setShowHasTrack(true);
        $this->setShowTodo(true);

        $this->setShowInquiry(true);
        $this->setInquirySenderReplace(true);
        $this->setShowRisk(true);

        $this->showFolderName = true;
        $this->setShowConferenceInfo(true);

    }

    public function webSalesAdvanceSearchSetting()
    {
        $this->setSpecifyFields([
            'mail_id',
            'mail_type',
            'sender',
            'receiver',
            'reply_to',
            'folder_id',
            'delete_flag',
            'read_flag',
            'cc',
            'bcc',
            'subject',
            'attach_flag',
            'urgent_flag',
            'receipt_flag',
            'receive_time',
            'update_time',
            'send_status',
            'track_flag',
            'user_id',
            'time_flag',
            'reply_flag',
            'reply_mail_id',
            'forward_flag',
            'user_mail_id',
            'relate_company_flag',
            'bounced_mail_id',
            'source_mail_id',
            'reply_to_mail_id',
        ]);
        $this->setShowPinFlag(true);
        $this->setShowSummary(true);
        $this->setShowTagList(true);
        $this->setShowContactSummary(true);
        $this->setShowCompanySummary(true);
        $this->setShowPinFlag(true);
        $this->setEmailNicknameReplaceFirst(true);
        $this->setShowMailHighLight(true);

        //旧版本的邮箱列表需要返回附件 移除旧版本后附件可以去掉
        $this->setShowAttachList(true);
        $this->setShowHasTrack(true);
        $this->setShowTodo(true);

        $this->setShowInquiry(true);
        $this->setInquirySenderReplace(true);
        $this->setShowIsOwner(true);
        $this->setShowRisk(true);

        $this->showFolderName = true;
    }

    public function webRecycleListSetting()
    {
        $this->setSpecifyFields([
            'mail_id',
            'mail_type',
            'sender',
            'receiver',
            'folder_id',
            'delete_flag',
            'read_flag',
            'subject',
            'urgent_flag',
            'user_id',
            'user_mail_id',
            'create_time',
            'update_time',
            'receive_time',
            'reply_flag',
            'forward_flag',
            'source_mail_id',
        ]);

        $this->SetShowSummary(true);
    }

    public function webContentSetting()
    {
        $this->setSpecifyFields(['subject','sender','receiver','receive_time']);
        $this->setShowContent(true);
    }

    public function appInfoSetting()
    {
        $this->setSpecifyFields([
            'mail_id',
            'mail_type',
            'sender',
            'receiver',
            'folder_id',
            'delete_flag',
            'read_flag',
            'reply_to',
            'cc',
            'bcc',
            'subject',
            'attach_flag',
            'urgent_flag',
            'receipt_flag',
            'user_id',
            'user_mail_id',
            'create_time',
            'update_time',
            'receive_time',
            'read_flag',
            'star_flag',
            'open_flag',
            'reply_flag',
            'forward_flag',
            'delete_flag',
            'time_flag',
            'send_status',
            'client_id',
            'distribute_flag',
            'track_flag',
            'expose_flag',
            'plan_send_time',
            'plansend_time',
            'imap_folder_id',
            'subject_remark',
            'source_mail_id',
        ]);

        $this->setShowSummary(true);
        $this->setShowAttachList(true);
        $this->setShowTagList(true);
        $this->setEmailNickReplace(true);
        $this->setShowMailTask(true);
        $this->setShowTrack(true);
        $this->setShowContent(true);
        $this->setShowPlainText(true);
        $this->setShowDistributeList(true);
        $this->setShowHasTrack(true);
        $this->setShowTrackInfo(true);
        $this->setAppShowMailCardMap(true);
    }

    public function appSalesInfoSetting()
    {
        $this->setSpecifyFields([
            'mail_id',
            'mail_type',
            'sender',
            'receiver',
            'folder_id',
            'delete_flag',
            'read_flag',
            'reply_to',
            'cc',
            'bcc',
            'subject',
            'attach_flag',
            'urgent_flag',
            'receipt_flag',
            'user_id',
            'user_mail_id',
            'create_time',
            'update_time',
            'receive_time',
            'read_flag',
            'star_flag',
            'open_flag',
            'reply_flag',
            'forward_flag',
            'delete_flag',
            'time_flag',
            'send_status',
            'client_id',
            'distribute_flag',
            'track_flag',
            'expose_flag',
            'plan_send_time',
            'plansend_time',
            'imap_folder_id',
            'subject_remark',
            'source_mail_id',
        ]);

        $this->setShowSummary(true);
        $this->setShowAttachList(true);
        $this->setShowTagList(true);
        $this->setEmailNickReplace(true);
        $this->setShowMailTask(true);
        $this->setShowTrack(true);
        $this->setShowContent(true);
        $this->setShowPlainText(true);
        $this->setShowDistributeList(true);
        $this->setShowHasTrack(true);
        $this->setShowTrackInfo(true);
        $this->setAppShowMailCardMap(true, false);
    }

    public function appListSetting()
    {
        $this->setSpecifyFields([
            'mail_id',
            'mail_type',
            'sender',
            'receiver',
            'folder_id',
            'delete_flag',
            'read_flag',
            'cc',
            'bcc',
            'subject',
            'attach_flag',
            'urgent_flag',
            'receipt_flag',
            'user_id',
            'user_mail_id',
            'create_time',
            'update_time',
            'receive_time',
            'read_flag',
            'open_flag',
            'reply_flag',
            'forward_flag',
            'delete_flag',
            'time_flag',
            'send_status',
            'client_id',
            'distribute_flag',
            'track_flag',
            'expose_flag',
            'plansend_time',
            'imap_folder_id',
            'source_mail_id',
        ]);
        $this->setShowTrack(true);
        $this->setShowSummary(true);
        $this->setShowTagList(true);
        $this->setShowPinFlag(true);
        $this->setEmailNicknameReplaceFirst(true);
        $this->setAppShowMailCardList(true);
        $this->setShowAttachList(true);
        $this->setShowHasTrack(true);
        $this->setShowMailTask(true);
        $this->setShowTrackInfo(true);
    }

    public function appAdvanceSearchSetting($mailCardDirectOwner = true)
    {
        $this->setSpecifyFields([
            'mail_id',
            'mail_type',
            'sender',
            'receiver',
            'folder_id',
            'delete_flag',
            'read_flag',
            'cc',
            'bcc',
            'subject',
            'attach_flag',
            'urgent_flag',
            'receipt_flag',
            'user_id',
            'user_mail_id',
            'create_time',
            'update_time',
            'receive_time',
            'read_flag',
            'open_flag',
            'reply_flag',
            'forward_flag',
            'delete_flag',
            'time_flag',
            'send_status',
            'client_id',
            'distribute_flag',
            'track_flag',
            'expose_flag',
            'plansend_time',
            'imap_folder_id',
            'reply_to',
            'reply_mail_id',
            'star_flag',
            'email_size',
            'subject_remark',
            'bounced_mail_id',
            'source_mail_id',
            'conversation_id',
            'relate_company_flag',
            'sign_id',
        ]);
        $this->setShowTrack(true);
        $this->setShowSummary(true);
        $this->setShowTagList(true);
        $this->setShowPinFlag(true);
        $this->setEmailNicknameReplaceFirst(true);
        $this->setAppShowMailCardList(true);
        $this->setShowAttachList(true);
        $this->setShowHasTrack(true);
        $this->setShowMailTask(true);
        $this->setShowMailHighLight(true);
        $this->setShowTrackInfo(true);
        $this->setShowTodo(true);
        $this->mailCardDirectOwner = $mailCardDirectOwner;
    }

    public function appConversationMailListSetting($mailCardDirectOwner = true)
    {
        $this->setSpecifyFields([
            'mail_id',
            'mail_type',
            'sender',
            'receiver',
            'folder_id',
            'delete_flag',
            'read_flag',
            'cc',
            'bcc',
            'subject',
            'attach_flag',
            'urgent_flag',
            'receipt_flag',
            'user_id',
            'user_mail_id',
            'create_time',
            'update_time',
            'receive_time',
            'read_flag',
            'open_flag',
            'reply_flag',
            'forward_flag',
            'delete_flag',
            'time_flag',
            'send_status',
            'client_id',
            'distribute_flag',
            'track_flag',
            'expose_flag',
            'plansend_time',
            'imap_folder_id',
            'reply_to',
            'reply_mail_id',
            'star_flag',
            'email_size',
            'subject_remark',
            'bounced_mail_id',
            'source_mail_id',
            'conversation_id',
            'relate_company_flag',
            'sign_id'
        ]);
        $this->setShowTrack(true);
        $this->setShowSummary(true);
        $this->setShowTagList(true);
        $this->setShowPinFlag(true);
        $this->setEmailNicknameReplaceFirst(true);
        $this->setAppShowMailCardList(true);
        $this->setShowAttachList(true);
        $this->setShowHasTrack(true);
        $this->setShowMailTask(true);
        $this->setShowMailHighLight(true);
        $this->setShowTrackInfo(true);
        $this->mailCardDirectOwner = $mailCardDirectOwner;
    }

    public function mailRiskEventInfoSetting()
    {
        $this->setSpecifyFields([
            'mail_id',
            'mail_type',
            'sender',
            'receiver',
            'cc',
            'bcc',
            'subject',
            'user_id',
            'user_mail_id',
            'create_time',
            'receive_time',
            'client_id',
            'reply_flag',
            'reply_to_mail_id',
            'receive_origin_sender',
            'source_mail_id',
        ]);

        $this->setShowTodo(false);
    }

    public function tmsOtherDocumentSetting()
    {
        $this->setSpecifyFields([
            'mail_id',
            'mail_type',
            'update_time'
        ]);
    }

    public function trailInfoSetting()
    {
        $this->setSpecifyFields([
            'mail_id',
            'mail_type',
            'sender',
            'receiver',
            'reply_to',
            'folder_id',
            'delete_flag',
            'read_flag',
            'cc',
            'bcc',
            'subject',
            'attach_flag',
            'urgent_flag',
            'receipt_flag',
            'receive_time',
            'update_time',
            'send_status',
            'track_flag',
            'user_id',
            'time_flag',
            'reply_flag',
            'reply_mail_id',
            'reply_time',
            'forward_flag',
            'user_mail_id',
            'root_mail_id',
            'bounced_mail_id',
            'relate_company_flag',
            'reply_to_mail_id',
            'source_mail_id',
            'open_flag'
        ]);
        $this->setShowSummary(true);
        $this->setShowAttachList(true);
        $this->setShowTrackDetail(true);
        $this->setShowTagList(true);
    }

    public function appUserGroupSetting($mailCardDirectOwner = true)
    {
        $this->setSpecifyFields([
            'mail_id',
            'mail_type',
            'sender',
            'receiver',
            'folder_id',
            'delete_flag',
            'read_flag',
            'cc',
            'bcc',
            'subject',
            'attach_flag',
            'urgent_flag',
            'receipt_flag',
            'user_id',
            'user_mail_id',
            'create_time',
            'update_time',
            'receive_time',
            'read_flag',
            'open_flag',
            'reply_flag',
            'forward_flag',
            'delete_flag',
            'time_flag',
            'send_status',
            'client_id',
            'distribute_flag',
            'track_flag',
            'expose_flag',
            'plansend_time',
            'imap_folder_id',
            'reply_to',
            'reply_mail_id',
            'star_flag',
            'email_size',
            'subject_remark',
            'bounced_mail_id',
            'source_mail_id',
            'conversation_id',
            'relate_company_flag',
            'sign_id',
        ]);
        $this->setShowTrack(true);
        $this->setShowSummary(true);
        $this->setShowTagList(true);
        $this->setShowPinFlag(true);
        $this->setEmailNicknameReplaceFirst(true);
        $this->setAppShowMailCardList(true);
        $this->setShowAttachList(true);
        $this->setShowHasTrack(true);
        $this->setShowMailTask(true);
        $this->setShowMailHighLight(true);
        $this->setShowTrackInfo(true);
        $this->setShowTodo(true);
        $this->mailCardDirectOwner = $mailCardDirectOwner;
    }

    /**
     * 数据解包格式转换
     * @param $data
     * @return mixed
     */
    public function strip($data)
    {
        return $data;
    }

    /**
     * 构建数据映射器
     * 对于list可以批量获取数据 减少数据库请求次数
     */
    public function buildMapData()
    {
        Speed::log('buildMapDataStart');

        $list = $this->batchFlag ? $this->listData : [$this->data];

        $draftMailIds = [];
        $formalMailIds = [];
        $trackMailIds = [];
        $mailContentKeys = [];
        $conversationIds = [];
        //这里默认获取的邮件列表都是属于同一个用户的，这样取pin数据可能会有问题
        $userId = null;
        $userIds = array_unique(array_column($list, 'user_id'));
        $clientId = null;
        $mailIds = array_column($list, 'mail_id');
        $userMailIds = array_column($list, 'user_mail_id');

        if (empty($mailIds))
            return;

        $mailExternalMap = \MailExternal::findByMailIds($mailIds);

        $plan_send_timezone = '';
        $plan_type = 0;
        foreach ($list as $mail)
        {
            $userId = $mail['user_id'] ? $mail['user_id'] : null;
            $clientId = $mail['client_id'] ? $mail['client_id'] : null;
            $isExpose = (isset($mailExternalMap[$mail['mail_id']]['expose_flag']) && $mailExternalMap[$mail['mail_id']]['expose_flag'] == 1);
            $mailId = $mail['mail_id'];

            if (isset($mailExternalMap[$mail['mail_id']]['plan_send_timezone'])){
                $plan_send_timezone = $mailExternalMap[$mail['mail_id']]['plan_send_timezone'];
            }
            if (isset($mailExternalMap[$mail['mail_id']]['plan_type'])){
                $plan_type = $mailExternalMap[$mail['mail_id']]['plan_type'];
            }

            $conversationIds[] = $mail['conversation_id'];

            if (Helper::isTiming($mail['folder_id'], $mail['time_flag'])) {
                $formalMailIds[$mail['user_mail_id']][] = $mail['mail_id'];
                $mailContentKeys[$mailId] = [
                    'clientId' => $clientId,
                    'userMailId' => $mail['user_mail_id'],
                    'mailId' => $mailId,
                ];
            } elseif($isExpose) {
                $draftMailIds[] = $mail['mail_id'];
            } else {
                if (Helper::isDraft($mail['folder_id'], $mail['time_flag'])) {
                    $draftMailIds[] = $mail['mail_id'];
                } else {
                    $formalMailIds[$mail['user_mail_id']][] = $mail['mail_id'];
                    $mailContentKeys[$mailId] = [
                        'clientId' => $clientId,
                        'userMailId' => $mail['user_mail_id'],
                        'mailId' => $mailId,
                    ];
                    //所有发送的正式邮件需要查找对方已回复  不能用track_flag去判断。java在收件的时候如果记录到回复邮件也会写追踪信息，就算用户设置的track_flag = 0
                    if ($mail['mail_type'] == \Mail::MAIL_TYPE_SEND) {
                        $trackMailIds[] = $mail['mail_id'];
                    }
                }
            }
        }

        $approvalLockMap = [];
        if ($this->showLockInfo) {
            $approvalLockList = \common\library\approval_flow\Helper::getReferLockList($userId, \Constants::TYPE_MAIL, $draftMailIds, ['refer_id', 'lock_flag']);
            $approvalLockMap = array_column($approvalLockList, 'lock_flag', 'refer_id');
        }

        
        //审批流信息
        $approvalFlowInfoMap = [];
        if ($this->showApprovalFlowInfo) {
            // 获取定时邮件ID
            // feature 定时邮件也需要考虑审批场景
            $timeMailIds = [];
            foreach ($list as $mail) {
                if (isset($mail['time_flag']) && $mail['time_flag'] == 1 && !in_array($mail['mail_id'], $draftMailIds)) {
                    $timeMailIds[] = $mail['mail_id'];
                }
            }
            
            // 合并草稿邮件和定时邮件ID，一起查询审批流信息
            $allMailIds = array_merge($draftMailIds, $timeMailIds);
            $approvalFlowInfoMap = \common\library\approval_flow\Helper::getApprovalProgressInfo(\Constants::TYPE_MAIL, $allMailIds, $this->showApprovalFlowEventInfo);
        }

        //仅供app邮件会话列表使用，有好几个标签，打钉的逻辑的查询
        $conversationInfoMap = [];
        $mailAttachmentsMap = [];
        $mailTagListMap = [];
        $mailPinMap = [];
        $topMailMap = [];

        if ($this->showConversationInfo) {

            $conversationIds = array_filter($conversationIds);
            $conversationInfoMap = array_column(\MailConversation::findByConversationIds($conversationIds), null, 'mail_conversation_id');

            //获取会话的所有子邮件数据
            $mailIdConversationIds = \common\library\mail\Helper::getMailIdsByConversationIds($clientId,$conversationIds);
            //所有子邮件
            $subMailIds = array_filter(array_column($mailIdConversationIds,'mail_id'));
            //邮件会话映射
            $mailIdConversationIdMap = array_column($mailIdConversationIds,'conversation_id','mail_id');
            //附件
            $mailAttachmentsMap = \MailAttach::getFormatMailAttach($subMailIds);
            //标签
            $mailTagListMap = \MailTagAssoc::getMailTagListByMailIds($subMailIds);
            //打钉
            $mailPinMap = array_fill_keys(\Pin::getPinReferIds($userId, \Pin::TYPE_MAIL, $subMailIds),1);

            //是否已读
            $readMailMap = array_column($mailIdConversationIds,'read_flag','mail_id');

            $conversationMap = [];

            foreach ($subMailIds as $mailId) {

                if (!isset($mailIdConversationIdMap[$mailId]) && !empty($mailIdConversationIdMap[$mailId])) {
                    continue;
                }

                $conversationId = $mailIdConversationIdMap[$mailId]??0;
                $conversationInfoMap[$conversationId]['sub_mail_ids'][] = $mailId;

                //是否已读
                $readFlag = $readMailMap[$mailId]??-1;

                //是否已打钉
                $pinFlag = 0;
                if (array_key_exists($mailId,$mailPinMap)) {
                    $pinFlag = 1;
                }

                //附件数量
                $attachCount = 0;
                $subMailAttachment = $mailAttachmentsMap[$mailId][\MailAttach::TYPE_ATTACHMENT]??[];
                $attachCount = count($subMailAttachment);


                //判断是否未读
                if ($readFlag==0) {
                    $conversationInfoMap[$conversationId]['read_flag'] = $readFlag;
                }

                if ($pinFlag>0) {
                    $conversationInfoMap[$conversationId]['pin_flag'] = $pinFlag;
                }

                if ($attachCount>0) {
                    //最后一封附件 跟邮件顺序有关系
                    $conversationInfoMap[$conversationId]['attach_count'] = $conversationInfoMap[$conversationId]['attach_count']??0 + $attachCount;
                    $lastAttach = end($subMailAttachment);
                    $conversationInfoMap[$conversationId]['last_attach'] = $lastAttach;
                }

                //标签
                $tags = $mailTagListMap[$mailId] ?? [];
                if (!empty($tags)) {
                    foreach ($tags as $tag) {
                        if ($tag['system_flag']==1) {
                            $conversationInfoMap[$conversationId]['tags']['system_flag'][] = $tag;
                        } else {
                            $conversationInfoMap[$conversationId]['tags']['custom_tag'][] = $tag;
                        }
                    }
                }
            }
        }

        //缓存邮件body
        $mailBodyMap = [];
        $summaryMap = [];
        if ($this->showSummary || $this->showContent || $this->showPlainText)
        {
            if (!empty($draftMailIds))
            {
                $mailDraftContentList = new MailDraftContentList($userId);
                $mailDraftContentList->setMailId($draftMailIds);
                $draftMail =  $mailDraftContentList->find();
                $draftMail = array_combine(array_column($draftMail,'mail_id'),$draftMail);

                if ($this->showSummary)
                {
                    foreach ($draftMail as $mailContent)
                        $summaryMap[$mailContent['mail_id']] = mb_substr(trim($mailContent['plain_text']), 0, 100, 'utf-8');
                }
                $mailBodyMap = $mailBodyMap + $draftMail;
            }

            if (!empty($formalMailIds))
            {
                if ($this->showSummary)
                {
                    try {
                        $summaries = MailContentHelper::getSummaryList(array_values($mailContentKeys));
                        $summaryMap = array_column($summaries, 'summary', 'mailId');
                    } catch (\Exception $e) {
                        \common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null, $e),$e->getTrace());
                    }
                }

                //暂时不支持批量拉取正文
                if ($this->showContent || $this->showPlainText) {
                    if (count($mailIds) == 1) {
                        $nowMailId = $mailIds[0];
                        $contentInfo = [];
                        $formalMail = [];
                        try {
                            $contentInfo = MailContentHelper::getContentAndPlainText($mailContentKeys[$nowMailId]['clientId'], $mailContentKeys[$nowMailId]['userMailId'], $nowMailId);
                        } catch (\Exception $e) {
                            \common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null, $e),$e->getTrace());
                        }
                        $formalMail[$nowMailId] = [
                            'content' => $contentInfo['content'] ?? '',
                            'plain_text' => $contentInfo['plainText'] ?? '',
                        ];
                        $mailBodyMap = $mailBodyMap + $formalMail;
                    }
                }
            }
        }

        //缓存邮件标签
        if ($this->showTagList && empty($mailTagListMap)) {
            $mailTagListMap = \MailTagAssoc::getMailTagListByMailIds($mailIds);
        }

        //缓存打钉数据
        if ($this->showPinFlag && !empty($userIds) && empty($mailPinMap)) {
            $pinList = \Pin::getPinReferAndUserIds($userIds, \Pin::TYPE_MAIL, $mailIds);
            $pinList = array_column($pinList,"refer_id");
            $mailPinMap = empty($pinList) ? [] : array_fill_keys($pinList, 1);
        }

        //缓存置顶数据
        if ($this->showTopFlag && $userId !== null && !empty($this->topMailIdInfo)) {
            $topMailMap = array_fill_keys($this->topMailIdInfo, 1);
        }

        //缓存邮件附件
        if ($this->showAttachList && empty($mailAttachmentsMap)) {
            $mailAttachmentsMap = \MailAttach::getFormatMailAttach($mailIds);
        }

        //缓存邮件联系人
        $mailContactSummaryMap = [];
        $mailSenderSummaryMap = [];
        $emailNickMap = [];
        if ($this->showContactSummary || $this->emailNicknameReplaceFirst || $this->inquirySenderReplace || $this->showContactSummaryForMailConversation)
        {
            $mailContactMap = [];
            $mailSenderMap = [];
            foreach ($list as $mail)
            {
                $ownerUserId = $this->mailCardUseDirectOwner ? $mail['user_id'] : \User::getLoginUser()->getUserId();
                $mailContactMap[$ownerUserId][$mail['mail_id']] = [];

                //收件  取发件人的邮箱作为联系人
                if ($mail['mail_type'] == \Mail::MAIL_TYPE_RECEIVE)
                {
                    if (empty($mail['sender'])) {
                        continue;
                    }

                    if (!$this->inquirySenderReplace || !$this->inquiryType($mail)) {
                        $sender = Util::findAllEmails($mail['sender'])[0] ?? [];
                        if (!empty($sender)) {
                            $mailContactMap[$ownerUserId][$mail['mail_id']] = [$sender];
                        }
                    } else {
                        $replyTo = Util::findAllEmails($mail['reply_to'])[0] ?? [];
                        if (!empty($replyTo)) {
                            $mailContactMap[$ownerUserId][$mail['mail_id']] = [$replyTo];
                        }
                    }
                }

                //未知邮件类型忽略 兼容旧的草稿邮件数据 将草稿当成
                //发件  按序取 收件人， 抄送， 密送的第一个作为联系人
                if ($mail['mail_type'] == \Mail::MAIL_TYPE_SEND || $mail['mail_type'] == \Mail::MAIL_TYPE_UNKNOWN)
                {
                    $contactString = $mail['receiver'] . ';' . $mail['cc'] . ';' . $mail['bcc'];
                    $contactList = Util::findAllEmails($contactString);

                    if (empty($contactList)) {
                        continue;
                    }
                    $mailContactMap[$ownerUserId][$mail['mail_id']] = $contactList;
                }


                //如果是聚合模式全部取sender，不区分收发件
                if ($this->showContactSummaryForMailConversation) {
                    if (empty($mail['sender'])) {
                        continue;
                    }

                    //如果是收件，直接取上面收件的数据
                    if ($mail['mail_type'] == \Mail::MAIL_TYPE_RECEIVE) {
                        $mailSenderMap[$ownerUserId][$mail['mail_id']] = $mailContactMap[$ownerUserId][$mail['mail_id']];
                        continue;
                    }

                    //如果是发件则走下面的逻辑
                    if (!$this->inquirySenderReplace || !$this->inquiryType($mail)) {
                        $sender = Util::findAllEmails($mail['sender'])[0] ?? [];
                        if (!empty($sender)) {
                            $mailSenderMap[$ownerUserId][$mail['mail_id']] = [$sender];
                        }
                    } else {
                        $replyTo = Util::findAllEmails($mail['reply_to'])[0] ?? [];
                        if (!empty($replyTo)) {
                            $mailSenderMap[$ownerUserId][$mail['mail_id']] = [$replyTo];
                        }
                    }
                }

            }

            foreach ($mailContactMap as $ownerUserId => $mailContact) {
                $data = Helper::getMailContactSummaryMap($ownerUserId, $mailContact, $this->mailCardDirectOwner, $this->showCompanySummary);
                foreach ($data as $mailId => $item) {
                    $mailContactSummaryMap[$mailId] = $item;
                }
            }

            if ($this->showContactSummaryForMailConversation) {
                foreach ($mailSenderMap as $ownerUserId => $mailContact) {
                    $data = Helper::getMailContactSummaryMap($ownerUserId, $mailContact, $this->mailCardDirectOwner, $this->showCompanySummary);
                    foreach ($data as $mailId => $item) {
                        $mailSenderSummaryMap[$mailId] = $item;
                    }
                }
            }
        }

        //获取收件联系人昵称
        if ($this->emailNickReplace) {
            $emailList = [];
            foreach ($list as $mail) {
                //收件  取发件人的邮箱作为联系人
                if ($mail['mail_type'] == \Mail::MAIL_TYPE_RECEIVE)
                {
                    $emailString = $mail['sender'] . ';' . $mail['reply_to'];
                    $findAllMailAddress = Util::findAllEmails($emailString);
                    if (!empty($findAllMailAddress)) {
                        $emailList = array_merge($emailList, $findAllMailAddress);
                    }
                }
            }

            if (!empty($emailList)){
                $getCardUser = \User::getLoginUser();
                $card = new Card($getCardUser->getUserId());
                $emailNickMap = $card->nameMap($emailList);
            }
        }

        $hasTrackMap = [];
        if($this->showHasTrack && !empty($trackMailIds)){
            $hasTrackMap = Helper::getMailHasTrackMap($trackMailIds);
        }

        //待处理邮件
        $mailTodoMap = [];
        if ($this->showTodo && $userId !== null) {

            $todoList = new MailTodoList($userId);
            $todoList->setMailId($mailIds);
            $todoList = $todoList->find();

            $mailTodoMap = empty($todoList) ? [] : array_combine(array_column($todoList,'mail_id'), $todoList);
        }

        $largeAttachMap = [];
        if($this->showLargeAttach && $userId !== null) {
            $largeAttachMap = \common\library\external_file\Helper::mailFileShareMap($userId, $mailIds);
        }

        // Capture Card
        $captureCardMap = [];
        if ($this->showCaptureCard) {
            $mailAttachments = !empty($mailAttachmentsMap) ? $mailAttachmentsMap : [];
            $mailAttachments = array_column(array_reduce(array_reduce($mailAttachments, 'array_merge', []), 'array_merge', []), 'file_id');
            $captureCardMap = \common\library\ai\classify\capture\Helper::getCardInfo($clientId, array_merge($mailIds, $mailAttachments));
        }

        $mailCardMap = [];
        if ($this->showMailCardMap || $this->appShowMailCardList || $this->appShowMailCardMap) {

            $userMailList = \UserMail::findAllByUserMailId(\User::getLoginUser()->getClientId(), $userMailIds);
            $userMailList = array_column($userMailList, null, 'user_mail_id');

            $findEmailList = [];
            $findCardEmailMap = [];
            foreach ($list as $mail) {
                $ownerUserId = $this->mailCardUseDirectOwner ? $mail['user_id'] : \User::getLoginUser()->getUserId();
                $userMail = $userMailList[$mail['user_mail_id']] ?? [];
                $mailContactEmails = [
                    'mail_id' => $mail['mail_id'],
                    'mail_type' => $mail['mail_type'],
                    'nickname' => [
                        $userMail->email_address ?? '' => ''
                    ],
                    'source_user_mail_id' => $userMail->source_user_mail_id ?? 0,
                    'sender' => '',
                    'receiver' => [],
                    'cc' => [],
                    'bcc' => [],
                ];

                $sender = Util::findAllEmails($mail['sender'], false)[0] ?? '';

                //异常收件人展示给前端
                if(empty(Util::findAllEmails($mail['receiver'])) && !empty($mail['receiver'])) {
                    $receiver =  [$mail['receiver']];
                }else{
                    $receiver = Util::findAllEmails($mail['receiver'], false);
                }
                $cc = Util::findAllEmails($mail['cc'], false);
                $bcc = Util::findAllEmails($mail['bcc'], false);

                if($this->showMailCardMap){

                    $findEmailList[$ownerUserId] = array_merge([$sender], $receiver, $cc, $bcc);
                    $mailContactEmails['sender'] = $sender;
                    $mailContactEmails['receiver'] = $receiver;
                    $mailContactEmails['cc'] = $cc;
                    $mailContactEmails['bcc'] = $bcc;

                    if (!empty($mailContactEmails['source_user_mail_id']) && $mail['mail_type'] == \Mail::MAIL_TYPE_SEND) {
                        $mailContactEmails['nickname'][$sender] = trim(Util::getEmailNick($mail['sender']));
                    } elseif (!empty($mailContactEmails['source_user_mail_id']) && $mail['mail_type'] == \Mail::MAIL_TYPE_RECEIVE) {
                        $emailList = explode(';', $mail['receiver']);
                        foreach ($emailList as $value) {
                            $mailContactEmails['nickname'][Util::findAllEmails($value)[0] ?? ''] = trim(Util::getEmailNick($value));
                        }
                    }

                } elseif ($this->appShowMailCardList || $this->appShowMailCardMap){

                    if ($mail['mail_type'] == \Mail::MAIL_TYPE_RECEIVE) {

                        $findEmailList[$ownerUserId] = array_merge($findEmailList[$ownerUserId] ?? [], [$sender]);
                        $mailContactEmails['sender'] = $sender;

                    } elseif ($mail['mail_type'] == \Mail::MAIL_TYPE_SEND || $mail['mail_type'] == \Mail::MAIL_TYPE_UNKNOWN) {

                        $findEmailList[$ownerUserId] = array_merge($findEmailList[$ownerUserId] ?? [], $receiver);
                        $mailContactEmails['receiver'] = $receiver;

                        if ($this->appShowMailCardMap){
                            $findEmailList[$ownerUserId] = array_merge($findEmailList[$ownerUserId] ?? [], $cc, $bcc);
                            $mailContactEmails['cc'] = $cc;
                            $mailContactEmails['bcc'] = $bcc;
                        }

                    }

                }

                $findCardEmailMap[$ownerUserId][$mail['mail_id']] = $mailContactEmails;
            }

            foreach ($findEmailList as $ownerUserId => $emails) {
                $data = Helper::getMailCardMap($ownerUserId, $emails, $findCardEmailMap[$ownerUserId], $this->mailCardDirectOwner);
                foreach ($data as $mailId => $item) {
                    $mailCardMap[$mailId] = $item;
                }
            }

//            $cardData = Helper::getContactSummary($findEmailList, null, $this->mailCardDirectOwner);
        }

        $folderNameMap = [];
        if ($this->showFolderName) {
            $folderNameMap = \MailFolder::getFolderNames(array_column($list, 'folder_id'));
        }

        $alibabaMap = [];

        if($this->showAlibabaInfoFlag){
            $alibabaMap = \common\library\alibaba\trade\Helper::formatTradeByMailIds($clientId,$mailIds);
        }

        $mailExposeInfoMap = [];
        if ($this->showMailExposeInfo){
            $mailExposeInfoMap = Helper::getExposeData($clientId,$mailIds);
        }

        $asyncMailTaskMap = [];
        $mailErrorCodeMap = [];
        if($this->showFailErrorMessage) {
            $asyncMailTaskMap = array_column(\AsyncMailTask::findByMailIds($mailIds), null,'mail_id');
            $mailErrorCodeMap = array_column(Helper::getMailErrorCodeCache() , null , 'fail_code');
        }

        //群发单显针对sign_id特殊处理
        $signMap = Helper::getSignIdMapByRootMailId($clientId,$mailExternalMap);

        $tradeDocumentListMap = [];
        if ($this->showUsedTradeDocument) {
            $tradeDocumentListMap = \common\library\chat_assistant\Helper::getUsedTradeDocData($clientId, $userId, Constants::REFER_TYPE_MAIL, $mailIds, [Constants::ACTION_TYPE_DRAFT, Constants::ACTION_TYPE_SEND]);
        }

        $conferenceInfoMap = [];
        if ($this->showConferenceInfo) {
            $conferenceInfos = \common\library\mail\Helper::getConferenceInfoByMailIds($clientId, $mailIds);
            $conferenceInfoMap = array_column($conferenceInfos,null,'mail_id');
        }

        $remarkMap = [];
        if ($this->showRemark) {
            $tmpUserIds = array_column($list, 'user_id');
            $tmpUserIds = array_unique($tmpUserIds);
            $tmpMailIds = array_unique($mailIds);

            $remarkInfos = \common\library\mail\Helper::getRemarkInfoByUserIdsAndMailIds($clientId, $tmpUserIds, $tmpMailIds);
            $remarkMap = array_column($remarkInfos,'remark','mail_id');
        }

        $forwardListMap = [];
        if ($this->showForwardList) {
            $forwardListMap = Helper::forwardList($mailIds) ?? [];
        }

        $map = [
            'tag' => $mailTagListMap,
            'mail_body' => $mailBodyMap,
            'summary' => $summaryMap,
            'pin' => $mailPinMap,
            'top_mail_map' => $topMailMap,
            'attachments' => $mailAttachmentsMap,
            'contact_summary' => $mailContactSummaryMap,
            'sender_summary_map' => $mailSenderSummaryMap,
            'has_track' => $hasTrackMap,
            'external' => $mailExternalMap,
            'email_nick' => $emailNickMap,
            'mail_todo' => $mailTodoMap,
            'large_attach_list' => $largeAttachMap,
            'mail_card_map' => $mailCardMap,
            'capture_card_map' => $captureCardMap,
            'approval_lock_map' => $approvalLockMap,
            'approval_flow_info' => $approvalFlowInfoMap,
            'folder_name_map' => $folderNameMap,
            'alibaba_map' => $alibabaMap,
            'mail_expose_info_map' => $mailExposeInfoMap,
            'sign_map' => $signMap,
            'async_mail_task_map' => $asyncMailTaskMap,
            'trade_document_list' => $tradeDocumentListMap,
            'conversation_info_map' => $conversationInfoMap,
            'mail_error_code' => $mailErrorCodeMap,
            'conference_info_map' => $conferenceInfoMap,
            'remark_map' => $remarkMap,
            'forward_list_map' => $forwardListMap,
            'plan_send_timezone' => $plan_send_timezone,
            'plan_type' => $plan_type,
        ];
        $this->setMapData($map);
        Speed::log('buildMapDataEnd');
        return;
    }

    /**
     * 根据数据仓库获得的数据 和 设定的展示字段 装配数据
     * @param $data
     * @return array
     */
    public function buildFieldsInfo($data)
    {
        $result = [];
        $specifyFields = $this->specifyFields;
        if (empty($this->specifyFields)) {
            $specifyFields = array_keys($data);
        }
        foreach ($specifyFields as $field) {
            $mailExternal = $this->getMapData('external', $data['mail_id']);
            switch ($field) {
                //plan_send_time 对app做了冗余 后续调整一致为plan_send_time
                case 'plan_send_time':
                case 'plansend_time':
                    $result['plan_send_time'] = $mailExternal['plan_send_time'] ?? null;
                    $result['plansend_time'] = $mailExternal['plan_send_time'] ?? null;
                    break;
                case 'expose_flag':
                    $result['expose_flag'] = $mailExternal['expose_flag'] ?? null;
                    break;
                case 'sign_id':
                    $signId = $this->getMapData('sign_map', $data['mail_id']);
                    $result['sign_id'] = $signId;
                    break;
                case 'reply_mail_id':
                    $result['reply_mail_id'] = $this->getMapData('has_track', $data['mail_id'])['reply_mail_id'] ?? null;
                    break;
                case 'reply_time':
                    $result['reply_time'] = $this->getMapData('has_track', $data['mail_id'])['reply_time'] ?? null;
                    break;
                case 'root_mail_id':
                    $result['root_mail_id'] = $mailExternal['root_mail_id'] ?? null;
                    break;
                case 'bounced_mail_id':
                    $result['bounced_mail_id'] = $mailExternal['mail_bounced_by'] ?? null;
                    break;
                case 'receive_origin_sender':
                    $result['receive_origin_sender'] = $mailExternal['receive_origin_sender'] ?? null;
                    break;
                case 'delay_send_flag':
                    $result['delay_send_flag'] = $mailExternal['delay_send_flag'] ?? 0;
                    break;
                case 'alias_id':
                    $result['alias_id'] = $mailExternal['alias_id'] ?? 0;
                    break;
                case 'subject_remark':
                    $result['subject_remark'] = $mailExternal['subject_remark'] ?? '';
                    break;
                case 'source_mail_id':
                    $result['source_mail_id'] = $mailExternal['source_mail_id'] ?? null;
                    break;
                default:
                    $result[$field] = $data[$field];
            }
        }
        return $result;
    }

    /**
     * 格式化数据
     * @param $data
     * @return array
     */
    public function format($data)
    {
        $mailId = $data['mail_id'];

        $result = $this->buildFieldsInfo($data);

        if ($this->showApprovalFlowInfo) {
            $result['approval_flow_info'] = $this->getMapData('approval_flow_info', $data['mail_id']) ?? [];
        }

        if ($this->showLockInfo) {
            $result['lock_flag'] = $this->getMapData('approval_lock_map', $mailId) ?? 0;
        }

        if ($this->showContent) {
            $mapContentInfo = $this->getMapData('mail_body', $mailId) ?? [];
            $result['content'] = $mapContentInfo['content'] ?? '';
        }
        if ($this->showPlainText) {
            $mapPlainText = $this->getMapData('mail_body', $mailId) ?? [];
            $result['plain_text'] = $mapPlainText['plain_text'] ?? '';
        }

        if ($this->showAttachList) {
            $attachment_list = $this->getMapData('attachments', $mailId);
            $result['attachment_list'] = $attachment_list[0] ?? [];
            $result['inline_image_list'] = $attachment_list[1] ?? [];
        }

        if ($this->showSummary) {
            $result['summary'] = $this->getSummary($mailId);
        }

        if ($this->showTagList) {
            $result['tag_list'] = $this->getMapData('tag', $mailId) ?? [];
        }

        if ($this->showDistributeList) {
            $result['distribute_list'] = Helper::getMailDistributeList($mailId, $data['distribute_flag']);
        }

        if ($this->showPinFlag) {
            $result['is_pin'] = $this->getMapData('pin', $mailId) ?? 0;
        }

        if ($this->showTopFlag) {
            $result['top_flag'] = $this->getMapData('top_mail_map', $mailId) ?? 0;
        }

        if ($this->showTrack && $data['mail_type'] == \Mail::MAIL_TYPE_SEND) {
            $mailTrackList = Helper::getMailTrackList($mailId);
            $trackCount = count($mailTrackList);
            if ($trackCount) {
                $result['track']['last_info'] = Helper::getMailTrackLastInfo($mailId);
                $result['track']['list'] = $mailTrackList;
                $result['track']['count'] = count($result['track']['list']);
            } else {
                $result['track']['count'] = 0;
            }
        }

        //客户动态，邮件详情
        if ($this->showTrackDetail && $data['track_flag'] ==1  && $data['mail_type'] == \Mail::MAIL_TYPE_SEND) {

            $lastInfo = Helper::getMailTrackLastInfo($mailId);
            if($lastInfo){
                $result['track']['last_info'] = $lastInfo;
            }else{
                $result['track']['last_info'] = null;
            }
        }

        if ($this->showContactSummary) {
            $result['contact_type'] = $this->getMapData('contact_summary', $mailId)['contact_type'];
            $result['contact_summary'] = $this->getMapData('contact_summary', $mailId);
        }

        if ($this->showContactSummaryForMailConversation) {
            $result['sender_summary'] = $this->getMapData('sender_summary_map', $mailId);
        }

        if ($this->showFailErrorMessage && ($data['send_status'] == \Mail::SEND_STATUS_SEND_FAIL ||$data['send_status'] == \Mail::SEND_STATUS_SEND_UNKNOWN)) {
            $task = $this->getMapData('async_mail_task_map', $mailId);
            if ($task){
                $key = ($result['expose_flag'] || !empty($result['root_mail_id']))? 'expose' : 'normal';
                $errCode = $task['fail_code'] ?? '0';
                $mailErrorCode = $this->getMapData('mail_error_code' , $errCode) ?? [];
                if (empty($mailErrorCode) && $errCode != '0') {
                    $e = new \Exception("mailErrorCode can't null");
                    \common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());
                }
                $result['fail_error_code'] = $errCode;
                $result['fail_error_ori_message'] = $task['fail_desc'] ?? '';
                $result['fail_error_transfer_message'] = Helper::translateMessage($mailErrorCode) ?: '';
                $result['fail_error_replace_list'] = \AsyncMailTask::SEND_FAILED_MSG[$errCode][$key]['replace_list'] ?? [];
                if ($result['fail_error_code'] == \AsyncMailTask::SEND_FAIL_CODE_ACCESS_DENIED && strripos($result['fail_error_ori_message'], '小满发件IP') == 0)
                {
                    $ipStr = explode(',', $result['fail_error_ori_message'])[0];
                    $ip = explode(':', $ipStr)[1] ?? '';
                    if (!empty($ip)) {
                        $result['fail_error_transfer_message'] = str_replace('xiaoManIp', $ip, $result['fail_error_transfer_message']);
                    }
                }
            }
        }

        if ($this->showHasTrack) {
            $trackData = $this->getMapData('has_track', $mailId);
            $hasTrack = ($trackData == null) ? false : true;
            $result['has_track'] = $hasTrack;
            $result['view_count'] = $hasTrack ? $trackData['view_count'] : 0;
        }

        if ($this->showInquiry || $this->inquirySenderReplace)
        {
            $inquiryType = $this->inquiryType($data);

            if ($this->showInquiry)
                $result['inquiry_type'] = $inquiryType;

            if ($this->inquirySenderReplace && $inquiryType == 1)
                $result['sender'] = $result['reply_to'];
        }

        //收件  邮箱昵称替换
        if ($this->emailNickReplace || $this->emailNicknameReplaceFirst || $this->inquirySenderReplace)
        {
            if ($data['mail_type'] == \Mail::MAIL_TYPE_RECEIVE)
                $result['sender'] = implode(';', $this->replaceEmailNick(explode(';', $result['sender'])));
        }

        if ($this->showMailHighLight) {
            $result['search_subject'] = $this->mailHighLightMap[$data['mail_id']]['search_subject'] ?? '';
            $result['search_content'] = $this->mailHighLightMap[$data['mail_id']]['search_content'] ?? '';
        }

        if ($this->showForwardList)
        {
            $result['forward_list']['list'] = $this->getMapData('forward_list_map', $mailId) ?? [];
            $result['forward_list']['count'] = count($result['forward_list']['list']);
        }

        if($this->showMailTask) {
            if ($data['mail_type'] == \Mail::MAIL_TYPE_SEND) {
                $result['mail_task'] = null;
                $task = \AsyncMailTask::model()->find('mail_id=:mail_id', array(':mail_id' => $data['mail_id']));
                if ($task != null) {
                    $result['mail_task'] = $task->getAttributes();
                }
            }
        }

        //app 获取邮件卡片数据
        if($this->appShowMailCardList) {

            $cardList = $this->getMapData('mail_card_map', $data['mail_id']) ?? [];

            $mailCardList = [];
            if($data['mail_type'] == \Mail::MAIL_TYPE_RECEIVE) {

                $mailCardList = $cardList['sender']??[];

            } elseif ($data['mail_type'] == \Mail::MAIL_TYPE_SEND || $data['mail_type'] == \Mail::MAIL_TYPE_UNKNOWN) {

                $mailCardList = array_merge(
                    $cardList['receiver'] ?? [],
                    $cardList['cc'] ?? [],
                    $cardList['bcc'] ?? []
                );

            }

            $result['mail_card_list'] = $mailCardList;
        }

        if ($this->showMailCardMap || $this->appShowMailCardMap) {
            $mailCardList = $this->getMapData('mail_card_map', $data['mail_id']) ?? [];
            $result['mail_card_map'] = $mailCardList;
        }

        if($this->showCompanyInfo) {
            $mail = new Mail($data['mail_id']);
            $companyInfo = Helper::getCustomerInfo($mail);
            $result = array_merge($result, $companyInfo);
        }

        if($this->showAlibabaInfoFlag){
            $result['alibaba_info']  = $this->getMapData('alibaba_map', $mailId)??null;
        }
        if($this->showTodo){
            $result['mail_todo'] = $this->getMapData('mail_todo', $mailId)??[];
        }

        //兼容APP原先的字段
        if($this->showTrackInfo) {
            $result['track_flag'] = $this->getMapData('has_track', $mailId) === null ? 0 : 1;
            $trackInfo = Helper::getMailTrackLastInfo($mailId);
            $result['track_info'] = $trackInfo ? $trackInfo : null;
        }

        if($this->showLargeAttach) {
            $result['large_attach_list'] = $this->getMapData('large_attach_list', $mailId)??[];
        }
        if ($this->showUsedTradeDocument) {
            $result['trade_document_list'] = $this->getMapData('trade_document_list',$mailId) ?? [];
        }

        if($this->showRisk) {
            $result['risk'] = [
                'risk_flag' => 0,
                'risk_reason' => []
            ];

            $risk = $this->getMapData('external', $data['mail_id'])['risk_reason'] ?? null;
            if($risk) {
                $risk_reason = json_decode($risk,true);
                $result['risk'] = [
                    'risk_flag' => 1,
                    'risk_reason' => $risk_reason
                ];
            }
        }

        //Capture Card
        if ($this->showCaptureCard) {
            $captureCard = $this->getMapData('capture_card_map', $data['mail_id']) ?? null;
            foreach ($result['tag_list'] as $key => $item) {
                if (!empty($captureCard) && $item['tag_id'] == $captureCard['tag_id']) {
                    $result['tag_list'][$key]['capture_card'] = $captureCard;
                }
            }

            if ($this->showAttachList) {
                foreach ($result['attachment_list'] as $key => $item) {
                    $captureCard = $this->getMapData('capture_card_map', $item['file_id']) ?? null;
                    if (!empty($captureCard)) {
                        $result['attachment_list'][$key]['capture_card'] = $captureCard;
                    }
                }
            }
        }

        //is Owner
        if ($this->showIsOwner) {
            $result['is_owner'] = \User::getLoginUser()->getUserId() == $result['user_id'] ? true : false;
        }
        if($this->showReceiveOriginSender) {
            $result['receive_origin_sender'] = $this->getMapData('external', $data['mail_id'])['receive_origin_sender'] ?? '';
        }

        if ($this->showPlanSendTime) {
            $result['plan_send_time'] = $this->getMapData('external', $data['mail_id'])['plan_send_time'] ?? '';
            $result['plan_send_timezone'] = $this->getMapData('external', $data['mail_id'])['plan_send_timezone'] ?? '8';
            $result['plan_type'] = $this->getMapData('external', $data['mail_id'])['plan_type'] ?? 0;
        }

        if ($this->showReceiptInfo) {
            $result['receive_read_receipt'] = $this->getMapData('external', $data['mail_id'])['receive_read_receipt'] ?? '';
            $result['read_receipt_flag'] = $this->getMapData('external', $data['mail_id'])['read_receipt_flag'] ?? '';
        }

        if ($this->showFolderName) {
            $result['folder_name'] = $this->getMapData('folder_name_map', $data['folder_id']) ?? '';
        }

        if ($this->showMailExposeInfo){
            $mailExposeInfo = $this->getMapData('mail_expose_info_map', $mailId) ?? [];
            $result['mail_expose_info'] = $mailExposeInfo;
        }

        if ($this->showConversationInfo) {
            $result['conversation_info'] = $this->getMapData('conversation_info_map', $data['conversation_id']) ?? [];
        }

        if ($this->showConferenceInfo) {
            $result['conference_info'] = $this->getMapData('conference_info_map', $mailId) ?? [];

            $conferenceDescription = $result['conference_info']['description'] ??'';
            if ( isset($result['content']) && $result['content'] == '' && $conferenceDescription != null){
                // 高级搜索没有content的场景,需要先判断content是否存在
                // 如果是会议邮件且内容为空串但description不为空,则赋值给content返回
                $result['content'] = $conferenceDescription;
                $result['plain_text'] = $conferenceDescription;
            }
        }

        if ($this->showRemark) {
            $result['mail_remark'] = [
                'content' => $this->getMapData('remark_map', $mailId) ?? '',
            ];
        }

        //针对单封邮件 不要做列表展示的字段
        if ($this->showMailLanguage) {
            $result['auto_source'] = $this->getMapData('external', $mailId)['language_type'] ?? '';
            if (empty($result['auto_source'])) {
                $mapPlainText = $this->getMapData('mail_body', $mailId) ?? [];
                $plainText = $mapPlainText['plain_text'] ?? '';
                $result['auto_source'] =  Helper::getMailSourceLanguageByPlainText($result['client_id']??0,$mailId,$plainText);
            }
        }

        if ($this->showMultiContentFlag) {
            $result['ai_write_flag'] = $this->getMapData('external', $mailId)['multi_content_flag'] ?? 0;
        }

        return $result;
    }

    private function inquiryType($data)
    {
        if (!empty($data['reply_to']))
        {
            $sender = strtolower(\common\library\email\Util::getPureEmail($data['sender']));

            if ((in_array($sender, Mail::INQUIRY_ALIBABA_EMAIL) &&
                    stripos($data['reply_to'], 'delegate.alibaba.com') === false)
                || (preg_match( '/^[a-zA-Z0-9]+_notification@(mail\.)?made\-in\-china\.com$/',$sender))
            ) {
                return 1;
            }
        }

        return 0;
    }

    /**
     * 获取邮件的摘要
     * @return string
     */
    private function getSummary($mailId)
    {
        $summary = $this->getMapData('summary', $mailId);

        return $summary ? $summary : '';
    }

    public function getFailErrorCode($mail_id)
    {
        $task = \AsyncMailTask::findByMailId($mail_id);
        return $task->fail_code ?? '';
    }

    private function replaceEmailNick(array $emails)
    {
        $result = [];
        foreach ($emails as $email)
        {
            $pureEmail = strtolower(Util::getPureEmail($email));
            if (empty($pureEmail))
            {
                $result[] = Util::getEmailNick($email);
            }
            else
            {
                $mapData = $this->getMapData('email_nick', $pureEmail);
                if ($mapData)
                {
                    $nickname = $mapData;

                    if (empty($nickname))
                        $result[] = $email;
                    else
                        $result[] = $nickname . '<' . $pureEmail . '>';
                }
                else
                {
                    $result[] = $email;
                }
            }
        }

        return $result;
    }
}
