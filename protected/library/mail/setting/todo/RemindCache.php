<?php
/**
 * Created by PhpStorm.
 * User: beenzhang
 * Date: 2019/2/11
 * Time: 10:50 AM
 */

namespace common\library\mail\setting\todo;

use common\library\notification\PushHelper;
use common\library\push\Browser;
use common\library\todo\TodoConstant;
use LogUtil;

class RemindCache
{
    protected $clientId;
    protected $userId;
    protected $mailIds;
    protected $redis;

    const KEY = 'mail_todo:remind:list';

    public function __construct()
    {
        $this->redis = \RedisService::queue();
    }

    public function setClientId($clientId)
    {
        $this->clientId = $clientId;
    }

    public function setUserId($userId)
    {
        $this->userId = $userId;
    }

    public function setRemind(array $mailIds, $processTime)
    {
        if (!$this->clientId || !$this->userId) {
            return 0;
        }

        $setData = [];
        foreach ($mailIds as $mailId) {

            if (!is_numeric($processTime)) {
                $processTime = strtotime($processTime);
            }

            $member = $this->buildMember($this->clientId, $this->userId, $mailId);
            $setData[$member] = $processTime;
        }

        if (empty($setData)) {
            return 0;
        }

        $successCount = $this->redis->zadd(self::KEY, $setData);

        // 如果设置的提醒时间是最近5分钟内，直接生成待处理邮件的待办事项
        /**
        if (!empty($mailIds) &&  $processTime - time() <= 300) {
            \common\library\todo\Helper::pushFeed($this->clientId , $this->userId, TodoConstant::OBJECT_TYPE_MAIL, TodoConstant::TODO_TYPE_PENDING_MAIL, $mailIds);
        }
        */

        //存在重新设置提醒时间的MailID，从过期数据中删除
        (ExpireRemindCache::getInstance())->removeMailId($this->userId, $mailIds);

        return $successCount;
    }

    public function cancelRemind($mailIds)
    {
        if (!$this->clientId || !$this->userId) {
            return 0;
        }

        $data = [];
        foreach ($mailIds as $mailId) {
            $data[] = $this->buildMember($this->clientId, $this->userId, $mailId);
        }

        $this->redis->zrem(self::KEY, $data);

        // 标记待处理邮件的待办事项
        \common\library\todo\Helper::updateFeedStatusByObjectId($this->clientId, $this->userId, TodoConstant::OBJECT_TYPE_MAIL, TodoConstant::TODO_TYPE_PENDING_MAIL, $mailIds);

        //取消提醒标识，从过期数据中删除
        return (ExpireRemindCache::getInstance())->removeMailId($this->userId, $mailIds);
    }

    protected function buildMember($clientId, $userId, $mailId)
    {
        return "{$clientId}:{$userId}:{$mailId}";
    }

    public function getRemind($min = 0, $max = 0)
    {
        $data = $this->redis->zrangebyscore(self::KEY, $min, $max);
        return $data ? $data : [];
    }

    public function setRemindForScript(array $mailTodoList)
    {
        $nowTime = time();

        $setData = [];
        $expireData = [];
        foreach ($mailTodoList as $mailData) {
            $clientId = $mailData['client_id'] ?? 0;
            $mailId = $mailData['mail_id'] ?? 0;
            $userId = $mailData['user_id'] ?? 0;
            $processTime = $mailData['process_time'] ?? 0;

            if (!$mailId || !$clientId || !$userId || !$processTime)
                continue;

            if (!is_numeric($processTime))
                $processTime = strtotime($processTime);

            //过期提醒
            if ($processTime <= $nowTime)
            {
                $expireData[$userId][] = $mailId;
                continue;
            }

            $member = $this->buildMember($clientId, $userId, $mailId);
            $setData[$member] = $processTime;
        }

        $successCount = 0;
        if (!empty($setData)) {
            $successCount = $this->redis->zadd(self::KEY, $setData);
        }

        if (!empty($expireData))
        {
            foreach ($expireData as $setUserId => $mailIds)
            {
                (ExpireRemindCache::getInstance()->setValue($setUserId, $mailIds));
            }
        }

        return $successCount;
    }

    public function cleanClient()
    {
        if (!$this->clientId) {
            return false;
        }

        $index = 0;
        do {
            $result = $this->redis->zscan(self::KEY, $index, ['match' => "{$this->clientId}:*", 'count' => 1000]);
            $keys = array_keys($result[1]);
            !empty($keys) && $this->redis->zrem(self::KEY, $keys);

            $index = $result[0];
        } while ($index);

        return true;
    }

    public function cleanUser()
    {
        if (!$this->clientId || !$this->userId) {
            return false;
        }

        $index = 0;
        do {
            $result = $this->redis->zscan(self::KEY, $index, ['match' => "{$this->clientId}:{$this->userId}:*", 'count' => 1000]);
            $keys = array_keys($result[1]);
            !empty($keys) && $this->redis->zrem(self::KEY, $keys);

            $index = $result[0];
        } while ($index);

        return true;
    }

    public function removeSet()
    {
        $this->redis->del([self::KEY]);
    }

    public function doRemind()
    {
        $nowMinutes = date('Y-m-d H:i');
        $min = strtotime("{$nowMinutes}:00");
        $max = strtotime("{$nowMinutes}:59");
        $data = $this->redis->zrangebyscore(self::KEY, $min, $max, ['WITHSCORES' => true]);
        $nowMinuteNotifyList = [];

        foreach ($data as $key => $timeStamp)
        {
            list($clientId, $userId, $mailId) = explode(':', $key);
            $nowMinuteNotifyList[$userId]['clientId'] = $clientId;
            $nowMinuteNotifyList[$userId]['mailIds'][] = $mailId;
        }

        /**
         * @var $cache \CRedisCache
         */
        $cache = \Yii::app()->cache;
        foreach ($nowMinuteNotifyList as $userId => $item)
        {
            \User::setLoginUserById($userId);

            $lockKey = "mail:todo:notify:lock:{$userId}";
            $setSuccess = $cache->executeCommand('SET', [$lockKey, 1, 'EX', 50, 'NX']);
            if (!$setSuccess) {
                \LogUtil::info('setSuccess fail continue!!!');
                continue;
            }

            try {
                //web端推送当前这一分钟到提醒时间+已过期且remind_flag=1
                $expireMailIds = (ExpireRemindCache::getInstance())->getValue($userId);
                $allNotifyMailIds = array_values(array_unique(array_merge($item['mailIds'], $expireMailIds)));

                (ExpireRemindCache::getInstance())->addMailId($userId, $item['mailIds']);

                $remindId = $userId . '_web_' . time();
                $summaryInfo = ['id' => $remindId, 'count' => count($allNotifyMailIds)];
                $return = Browser::push($userId, Browser::TYPE_MAIL_TODO_REMIND, $summaryInfo);

                if ($return) {
                    $redisKey = \MailTodo::REMIND_REDIS_PREFIX . $remindId;
                    $cache->set($redisKey, $allNotifyMailIds, 86400);
                    \LogUtil::info("push user:$userId;id:{$summaryInfo['id']};count:{$summaryInfo['count']};redis_key:{$redisKey}");
                }

                if (!empty($allNotifyMailIds)) {
                    // 生成待处理邮件的待办事项
                    \common\library\todo\Helper::pushFeed($item['clientId'], $userId, TodoConstant::OBJECT_TYPE_MAIL, TodoConstant::TODO_TYPE_PENDING_MAIL, $allNotifyMailIds);
                }

                \LogUtil::info("push user:$userId;id:{$summaryInfo['id']};count:{$summaryInfo['count']}");
            } catch (\Exception $e) {
                \LogUtil::info("browser push fail! msg:{$e->getMessage()}");
            }

            PushHelper::pushAppMailTodo($item['clientId'], $userId, $item['mailIds']);

            $cache->executeCommand('DEL', [$lockKey]);
        }
    }

    /**
     * @return int
     * 清理前一天之前的通知
     */
    public function cleanExpire()
    {
        $min = 0;
        $nowMinutes = date('Y-m-d' ,strtotime('-1day'));
        $max = strtotime("{$nowMinutes} 23:59:59");
        $count = $this->redis->zremrangebyscore(self::KEY, $min, $max);
        LogUtil::info("zremrangebyscore: min:{$min}; max:{$max}; count:{$count}");
        return $count;
    }
}
