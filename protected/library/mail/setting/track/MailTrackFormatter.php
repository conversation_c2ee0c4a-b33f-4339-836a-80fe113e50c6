<?php
namespace common\library\mail\setting\track;

use common\library\email\Util;
use common\library\email_identity\cards\Card;
use common\library\email_identity\EmailIdentity;
use common\library\mail\Helper;

/**
 * Created by PhpStorm.
 * User: beenzhang
 * Date: 18/01/10
 * Time: 上午9:38
 */
class MailTrackFormatter extends \ListItemFormatter
{
    const TYPE_LIST = 'formatToList';
    const TYPE_DESK_DYNAMIC = 'formatToDynamic';

    protected $formatType;
    protected $trackType;
    protected $opUser;
    protected $directOwner = true;
    private $showContactSummary = false;


    public function __construct($userId, $formatType = self::TYPE_LIST)
    {
        if (!$userId)
            $user = \User::getLoginUser();
        else
            $user = \User::getUserObject($userId);
        if ($user->isEmpty())
            throw new \RuntimeException('账号不存在！');
        $this->opUser = $user;
        $this->setFormatType($formatType);
    }

    public function setDirectOwner($directOwner)
    {
        if (!is_null($directOwner))
            $this->directOwner = $directOwner;
    }

    public function setTrackType($trackType){
        if (!in_array($trackType, [MailTrackList::TYPE_OPEN, MailTrackList::TYPE_UNOPEN])) {
            throw new \ProcessException('please specify track_type');
        }
        $this->trackType = $trackType;
    }

    /**
     * @param bool $showContactSummary
     */
    public function setShowContactSummary(bool $showContactSummary)
    {
        $this->showContactSummary = $showContactSummary;
    }

    public function setFormatType($formatType)
    {
        //todo: 之后要改造成setSpecialFields和setShowXxx的形式
        if (!method_exists($this, $formatType)) {
            throw new \ProcessException("function $formatType not exists!");
        }

        $this->formatType = $formatType;
    }

    protected function format($data)
    {
        if (!isset($this->batchFlag)) {
            return $data;
        }

        if (isset($this->formatType)) {
            return $this->{$this->formatType}($data);
        }

        return $data;
    }

    public function strip($data)
    {
        if(is_array($data))
            return $data;

        return $data->getAttributes();
    }

    public function buildMapData(){
        $list = $this->batchFlag ? $this->listData : [$this->data];

        $mailIds = array_column($list, 'mail_id');

        $mailModel = \Mail::model();
        $tableName = $mailModel->tableName();
        $mailIdsString = implode(",", $mailIds);
        $sql = "select mail_id,receiver,subject,send_status,cc,bcc,user_id,folder_id from {$tableName} where mail_id in ({$mailIdsString})";
        $resultList = $mailModel->getDbConnection()->createCommand($sql)->queryAll();

        $resultMap = [];
        $findCardEmails = [];
        $mailContactMap = [];
        foreach ($resultList as $key => $value) {
            $mailId = $value['mail_id'];
            $subject = $value['subject'];
            $receiver = $value['receiver'];
            $status = $value['send_status'];
            $folderId = $value['folder_id'];
            $mailData = ['subject' => $subject, 'receiver' => $receiver, 'send_status' => $status, 'folder_id' => $folderId];
            $ownerUserId = $this->directOwner ? $value['user_id'] : \User::getLoginUser()->getUserId();

            if ($this->formatType == self::TYPE_LIST){
                $reArray = explode(";", $receiver);
                $findCardEmails[$mailId] = strtolower(Util::getPureEmail($reArray[0]));
            }

            if ($this->showContactSummary) {
                $contactString = $value['receiver'] . ';' . $value['cc'] . ';' . $value['bcc'];
                $contactList = Util::findAllEmails($contactString);

                if (empty($contactList)) {
                    continue;
                }
                $mailContactMap[$ownerUserId][$mailId] = $contactList;
            }

            $resultMap[$mailId] = $mailData;
        }

        if ($this->formatType == self::TYPE_LIST)
        {
            $card = new Card($this->opUser->getUserId());
            $cardMap = $card->cardTypeMap($findCardEmails, $this->directOwner);

            foreach ($findCardEmails as $mailIdKey => $findCardEmail) {
                $resultMap[$mailIdKey]['contact_type'] = $cardMap[$findCardEmail] ?? EmailIdentity::CARD_TYPE_STRANGER;
            }
        }

        if ($this->showContactSummary) {
            foreach ($mailContactMap as $ownerUserId => $mailContact) {
                $data = Helper::getMailContactSummaryMap($ownerUserId, $mailContact, $this->directOwner, true);
                foreach ($data as $mailId => $item) {
                    $resultMap[$mailId]['contact_summary'] = $item;
                }
            }
        }

        $this->mapData = $resultMap;
    }

    protected function formatToList(array $trackItem)
    {
        $mailId = $trackItem['mail_id'];

        //subject receiver send_status
        $trackItem['subject'] = $this->mapData[$mailId]['subject'] ?? '';
        $trackItem['receiver'] = $this->mapData[$mailId]['receiver'] ?? '';
        $trackItem['send_status'] = $this->mapData[$mailId]['send_status'] ?? 0;

        //增加客户信息  receiver只有一个的时候
        $reArray = explode(";", $trackItem['receiver']);
        if (count($reArray) > 1) {
            //receiver多个的时候不添加客户信息
        } else {
            try
            {
                $mail = new \common\library\mail\Mail($mailId);
                $trackItem = array_merge($trackItem, Helper::getCustomerInfo($mail));
            }catch ( \RuntimeException $e )
            {
                \LogUtil::error('邮件不存在: '.$e->getMessage().$e->getTraceAsString());
            }

        }

        //获取邮箱
        $trackItem['contact_type'] = $this->mapData[$mailId]['contact_type'] ?? EmailIdentity::CARD_TYPE_STRANGER;

        if($this->showContactSummary) {
            $trackItem['contact_summary'] = $this->mapData[$mailId]['contact_summary'] ?? [];
        }

        $detailList = ['list' => [], 'totalItem' => 0];
        if ($this->trackType == MailTrackList::TYPE_OPEN) {
            //取最近100条明细
            $detailList = array(
                'list' => \MailTrackDetail::findList($this->opUser, $mailId, 100),
                'totalItem' => \MailTrackDetail::trackCount($mailId),
            );
        }
        $trackItem['detail_list'] = $detailList;
        $trackItem['folder_id'] = $this->mapData[$mailId]['folder_id'] ?? \Mail::FOLDER_SEND_ID;
        return $trackItem;
    }


    protected function formatToDynamic($trackItem)
    {
        $mailId = $trackItem['mail_id'];
        $result = [];

        $subject = '';
        $receiver = '';
        if (isset($this->mapData[$mailId])) {
            $subject = $this->mapData[$mailId]['subject'];
            $receiver = $this->mapData[$mailId]['receiver'];
        }

        $result['email'] = $receiver;
        $result['create_time'] = $trackItem['last_view_time'];
        $result['type'] = 0;
        $result['system'] = 'crm';

        $detailList = ['list' => [], 'totalItem' => 0];
        if ($this->trackType == MailTrackList::TYPE_OPEN) {
            $result['type'] = 1;
            $result['title'] = '打开了邮件';
            //取最近100条明细
            $detailList = array(
                'list' => \MailTrackDetail::findList($this->opUser, $mailId, 100),
                'totalItem' => intval(\MailTrackDetail::trackCount($mailId)),
            );
        }

        $result['data'] = [
            'refer_id' => $mailId,
            'subject' => $subject,
            'trail_detail_list' => $detailList,
            'reply_mail_id' => $trackItem['reply_mail_id']??0
        ];
        return $result;
    }
}
