<?php

namespace common\library\mail\setting\rule;


use common\library\customer_v3\common\BaseList;
use common\library\customer_v3\company\orm\CompanyFilter;
use common\library\customer_v3\customer\orm\Customer;
use common\library\customer_v3\customer\CustomerList;
use common\library\email\Email;
use common\library\email\Util;
use common\library\mail\Mail;
use common\library\mail\service\ReplyService;
use common\library\report\error\ErrorReport;
use common\library\setting\library\tag\Tag;
use common\library\util\PgsqlUtil;
use UserMailAlias;

/**
 * 收件规则
 * <AUTHOR> <<EMAIL>> Created On 2016-09-02
 */

class MailRuleOperator {
    /**
     * @var Mail
     */
    private $mail;
    private $ruleList = null;

    /**
     * @var \User
     */
    private $user;
    private $senderEmail = '';
    private $senderEmailRaw = ''; // 发件人原始字符串，包含昵称和邮箱
    private $encryptString = '';   //to be used in forword email case.

    const RULE_LIST_CACHE_PREFIX = 'mail_rule_list';
    const AUTO_REPLY_CACHE_PREFIX = 'auto_reply';
    const AUTO_REPLY_HOLIDAY_CACHE_PREFIX = 'auto_reply_holiday';
    const RULE_FILTER_TYPE_NOT_CONTAIN = 0; //不包含
    const RULE_FILTER_TYPE_CONTAIN = 1;     //包含
    const RULE_FILTER_TYPE_SUBJECTION = 2;  //隶属
    const RULE_FILTER_TYPE_EQUAL = 3; //等于
    const RULE_FILTER_TYPE_NOT_EQUAL = 4; //不等于
    const CUSTOMER_GROUP_NOT_PUBLIC = 0;        //私海
    const CUSTOMER_GROUP_PUBLIC = 1;            //公海
    const CONDITION_AND = 1;  //并且
    const CONDITION_OR = 2;   //或者
    const REPLY_EMAIL_TO = "REPLY_EMAIL_TO";
    const MAIL_TODO_RECEIVE_TIME = 1; //待处理邮件时间为邮件收/发时间
    const MAIL_TODO_CUSTOM_TIME = 2; //待处理邮件时间为自定义时间


    protected $historyModel;//是否执行的是历史邮件归档
    protected $type = \MailRule::TYPE_RECEIVE;// 区分收/发件规则


    /**
     * @param Mail $mail
     * @param null $ruleList
     * @throws \ProcessException
     */
    public function __construct(Mail $mail, $ruleList = null, $type = \MailRule::TYPE_RECEIVE)
    {
        $this->user = \User::getLoginUser();

        $this->mail = $mail;
        $this->encryptString = $this->user->getUserId() . '-' . $this->user->getEmail();

        $sendEmailArray = Util::findAllEmails($mail->sender);
        $sendEmail = !empty($sendEmailArray) ? $sendEmailArray[0] : '';
        $this->senderEmail = $sendEmail;
        $this->senderEmailRaw = $mail->sender;
        $this->ruleList = $ruleList;
        $this->type = $type;
        if($ruleList === null)
            $this->ruleList = $this->getAllRuleList();

        $this->historyModel = false;
    }

    public function setHistoryModel($historyModel){
        $this->historyModel = $historyModel ? true : false;
    }

    public function matchAndExecute() {
        $method = $this->type == \MailRule::TYPE_RECEIVE ? '收': '发';
        \LogUtil::info('['.$this->mail->getMailId().']开始执行'.$method.'件规则');
        if (!$this->ruleList)
            return;

        $operationMap = [
            'operation_trash_flag' => 0,
            'operation_move_to_folder' => 0,
            'operation_mark_as_tag' => [],
            'operation_mark_as_read' => 0,
            'operation_pin' => 0,
            'operation_forward' => [],
            'operation_auto_reply_content' => '',
            'operation_todo_flag' => 0,
            'operation_todo' => '',
            'operation_distribute' => 0,
            'operation_distribute_settings' => [],
        ];

        $isPublicUserMail = \UserMail::isPublicUserMail($this->mail->getUserMailId());

        foreach ($this->ruleList as $rule) {

            // 公共邮箱的跳过假期自动回复
            if ($this->type == \MailRule::TYPE_RECEIVE && $isPublicUserMail && $rule['mail_rule_type'] == \MailRule::MAIL_HOLIDAY_AUTO_REPLY) {
                continue;
            }

            //邮件规则是假期自动回复，则跳开
            if($this->type == \MailRule::TYPE_RECEIVE && $rule['mail_rule_type'] == \MailRule::MAIL_HOLIDAY_AUTO_REPLY)
            {
                //检查是否开启假期自动回复
                try {
                    $this->holidayAutoReplyExecute($rule);
                } catch (\Throwable $e) {
                    \LogUtil::info(sprintf("[%s]doHolidayAutoReply end:user_id[%s] error_msg: ".$e->getMessage(),
                        $this->mail->getMailId(),$this->mail->getUserId()));
                }

                continue;

            }


            if (!$this->matchFilter($rule))
                 continue;

            \LogUtil::info("[{$this->mail->getMailId()}][{$this->mail->getUserId()}]matchRule[{$rule['rule_id']}]ruleInfo[" . json_encode($rule) ."]");

            if($rule['operation_trash_flag'] > 0){
                $operationMap['operation_trash_flag'] =  $rule['operation_trash_flag'];
                break;
            }

            if($rule['operation_move_to_folder_flag'] && $rule['operation_move_to_folder'] > 0)
                $operationMap['operation_move_to_folder'] =  $rule['operation_move_to_folder'];

            if($rule['operation_mark_as_tag_flag'] && $rule['operation_mark_as_tag'] > 0)
                $operationMap['operation_mark_as_tag'][] =  $rule['operation_mark_as_tag'];

            if($rule['operation_mark_as_read'] > 0)
                $operationMap['operation_mark_as_read'] =  $rule['operation_mark_as_read'];

            if($rule['operation_pin'] > 0)
                $operationMap['operation_pin'] =  $rule['operation_pin'];

            if($this->historyModel == false && $rule['operation_forward_flag'] && $rule['operation_forward'] !== null && strlen($rule['operation_forward']) > 0)
                $operationMap['operation_forward'][] =  $rule['operation_forward'];

            // 公共邮箱的跳过收件规则的自动回复
            if($isPublicUserMail==false && $this->historyModel == false && $rule['operation_auto_reply_content_flag'] && $rule['operation_auto_reply_content'] !== null && strlen($rule['operation_auto_reply_content']) > 0)
                $operationMap['operation_auto_reply_content'] =  $rule['operation_auto_reply_content'];

            if ($rule['operation_todo_flag'] > 0) {
                $operationMap['operation_todo_flag'] = $rule['operation_todo_flag'];
                $operationMap['operation_todo'] = $rule['operation_todo'];
            }

            if ($rule['operation_distribute'] > 0 && !empty($rule['operation_distribute_settings'])) {
                $operationMap['operation_distribute'] = $rule['operation_distribute'];
                $operationMap['operation_distribute_settings'] = is_array($rule['operation_distribute_settings']) ? $rule['operation_distribute_settings'] : json_decode($rule['operation_distribute_settings'], true);
            }

            if ($rule['execute_next'] == 0)
                break;
        }


        $this->executeRule($operationMap);
    }

    /**
     * 执行单个收件规则
     * @param array $ruleOperationArray
     * @param bool $executeNow
     */
    public function executeRule($ruleOperationArray)
    {
        $updateFields = $this->mail->getSetFields();

        if ($ruleOperationArray['operation_trash_flag'] > 0)
        {
            $this->mail->folder_id = \Mail::FOLDER_TRASH_ID;
            $this->mail->delete_flag = \Mail::DELETE_FLAG_TRASH;

            $updateFields = array_merge($updateFields, ['folder_id', 'delete_flag']);

            return $this->mail->update($updateFields);
        }

        $info = '';

        if ($ruleOperationArray['operation_move_to_folder'] > 0)
        {
            $folderId = $ruleOperationArray['operation_move_to_folder'];

            $this->mail->folder_id =$folderId;
            // 从邮件服务器收取下来时, 邮件本身可能就是已删除 (folder_id=5 (Mail::FOLDER_TRASH_ID)),
            // 在前面的流程中则会依此而置 delete_flag 为 1 (Mail::DELETE_FLAG_TRASH)
            // 如果在小满这边需要安排到自定义文件夹中, 那么 delete_flag 也需要对齐修正
            $this->mail->delete_flag = \Mail::DELETE_FLAG_NONE;
            $updateFields = array_merge($updateFields, ['folder_id', 'delete_flag']);

            $info .= 'folder '.$folderId.';';
        }

        if (!empty($ruleOperationArray['operation_mark_as_tag']))
        {
            foreach ($ruleOperationArray['operation_mark_as_tag'] as $tagId)
            {
               \MailTagAssoc::mark($this->user->getClientId(), $this->user->getUserId(), array($this->mail->getMailId()), [$tagId]);
                $info .= 'tag '.$tagId.';';
            }
        }

        if ($ruleOperationArray['operation_mark_as_read'] > 0)
        {
            $this->mail->read_flag = \Mail::READ_FLAG_TRUE;
            $updateFields = array_merge($updateFields, ['read_flag']);

            $info .= 'read;';
        }

        if ($ruleOperationArray['operation_pin'] > 0) {
            \Pin::it(\Pin::TYPE_MAIL, $this->mail->getMailId(), $this->user);
            $info .= 'pin;';
        }

        if ($ruleOperationArray['operation_forward'] !== null && !empty($ruleOperationArray['operation_forward'])) {
            $ruleOperationArray['operation_forward'] = array_unique($ruleOperationArray['operation_forward']);
            $forwardArray = [];
            $str = md5($this->encryptString);
            $content = $this->mail->getContent();
            if(stripos($content, $str) === false){
                foreach ($ruleOperationArray['operation_forward'] as $forward) {
                    if($forward != $this->user->getEmail())
                        $forwardArray[] = $forward;
                }
            }
            if (!empty($forwardArray)) {
                $forwardMail = $this->mail->forward();
                $content = $forwardMail->getContent();

                //加入转发的特殊标识，防止转发死循环。
                $content .= '<input type="hidden" value="' . md5($this->encryptString) . '" />';
                $forwardMail->setContent($content);

                $forwardMailStr = implode(';', $forwardArray);
                $forwardMail->receiver = $forwardMailStr;
                $forwardMail->setCheckSendFrequencyLimit(false);
                $forwardMail->send();
                $info .= 'forward ' . $forwardMail->subject . ';';
            }
        }

        if ($ruleOperationArray['operation_auto_reply_content'] !== null && strlen($ruleOperationArray['operation_auto_reply_content']) > 0) {
            $rs = $this->checkAutoReply(\MailRule::MAIL_AUTO_REPLY);
            if($rs){
                $content = '<input type="hidden" value="' . md5($this->encryptString) . '" />';
                $content .= $ruleOperationArray['operation_auto_reply_content'];
                $replyMail = $this->mail->autoReply($content);
                $replyMail->setCheckSendFrequencyLimit(false);
                $replyMail->send();
                $info .= 'reply;';

                // 自动回复过了, 记录下来, 接下来 4 天内不会再自动回复
                $userId = $this->mail->user_id;
                $sendEmail = Util::getPureEmail($this->mail->sender);
                $redisKey = self::AUTO_REPLY_CACHE_PREFIX."_".$userId."_".$sendEmail;
                \Yii::app()->redis->set($redisKey, time(),3600 * 24*4);
            }
        }


        //标记为待处理邮件
        if (isset($ruleOperationArray['operation_todo_flag']) && !empty($ruleOperationArray['operation_todo_flag'])) {

            $processTime = '';
            if ($ruleOperationArray['operation_todo_flag'] == self::MAIL_TODO_CUSTOM_TIME) {
                $processTime = date("Y-m-d ".$ruleOperationArray['operation_todo']['time'], strtotime("+{$ruleOperationArray['operation_todo']['day']} day", strtotime($this->mail->receive_time)));
            } else if ($ruleOperationArray['operation_todo_flag'] == self::MAIL_TODO_RECEIVE_TIME) {
                $processTime = date("Y-m-d H:i:s", strtotime($this->mail->receive_time));
            }


            try {
                $mailBatchOperator = new \common\library\mail\MailListBatchOperate($this->mail->user_id);
                $mailBatchOperator->setParams(['mail_ids' => [$this->mail->mail_id]]);
                $res = $mailBatchOperator->setTodo($processTime);
            } catch (\Throwable $e) {
                \LogUtil::error("set mail todo exception:mail_id{$this->mail->mail_id}" . $e->getMessage());
                throw $e;
            }

            $info .= 'set todo;';

        }

        //分发邮件处理
        if ($ruleOperationArray['operation_distribute'] > 0 && !empty($ruleOperationArray['operation_distribute_settings'])) {
            $res = false;
            foreach ($ruleOperationArray['operation_distribute_settings']['target_user_mail_ids'] ?? [] as $targetUserMailId) {
                $distributeService = new \common\library\mail\service\DistributeService([$this->mail->mail_id], $targetUserMailId);
                $res = $distributeService->distribute();
            }

            if (!empty($res)) {
                $info .= ' distribute mail;';
            }
        }

        if($info == ''){
            $info = 'nothing to do';
        }

        if (!empty($updateFields))
        {
            $this->mail->update($updateFields);
        }

        \LogUtil::info("[{$this->mail->getMailId()}]".$info);
        return true;
    }


    //假期自动回复功能
    public function holidayAutoReplyExecute($rule){

        \LogUtil::info(sprintf("[%s]doHolidayAutoReply begin:user_id[%s]",
            $this->mail->getMailId(),$this->mail->getUserId()));

        //如果假期规则不存在，或者是关闭了
        if (!$rule || $rule['operation_auto_reply_content_flag'] == \MailRule::MAIL_AUTO_REPLY_OFF)
            return;

        $holidayAutoReplyFlag = false; //假期自动回复标识
        $autoReplyContent = "";

        //检查假期自动回复是否开启
        if($rule['mail_rule_type'] == \MailRule::MAIL_HOLIDAY_AUTO_REPLY ){

            $holidayAutoReplyFlag = $this->checkHolidDayAutoReply($rule);
            $autoReplyContent = $rule['operation_auto_reply_content'];

        }

        if($holidayAutoReplyFlag && strlen($autoReplyContent) > 0){
            $replyMail = $this->mail->reply(null,false);
            $replyMail->setContent($autoReplyContent);
            $replyMail->setCheckSendFrequencyLimit(false);
            $replyMail->send();

            // 自动回复过了, 记录下来, 接下来 4 天内不会再自动回复
            $userId = $this->mail->user_id;
            $sendEmail = Util::getPureEmail($this->mail->sender);
            $redisKey = self::AUTO_REPLY_HOLIDAY_CACHE_PREFIX."_".$userId."_".$sendEmail;
            \Yii::app()->redis->set($redisKey, time(),3600 * 24*4);
        }

        \LogUtil::info(sprintf("[%s]holidayAutoReplyRes:user_id[%s] res[%s] matchRule[%s]",
            $this->mail->getMailId(),
            $this->mail->getUserId(),
            $holidayAutoReplyFlag ? "true" : "false",
            $rule['rule_id']));

    }

    //检查假期自动回复是否开启
    public function checkHolidDayAutoReply($rule){


        $holidayAutoReplyFlag = false; //假期自动回复标识
        if(!$rule){
            return $holidayAutoReplyFlag;
        }

        //1.判断当前时间，是否在自动回复时间段内
        $filterSettings = is_array($rule['filter_settings']) ? $rule['filter_settings'] : json_decode($rule['filter_settings'],true);

        if($filterSettings ){
            foreach ($filterSettings as $item){
                if($item['key'] == "filter_auto_reply"){

                    $startTime = $item['start_time'] > 0 ? strtotime($item['start_time']) :0 ;
                    $endTime = $item['end_time'] > 0 ? strtotime($item['end_time']): 0;
                    if($endTime){
                        $endTime = $endTime + 3600*24;
                    }

                    //邮件的收发件时间
                    $nowTime = $this->mail->receive_time ? strtotime($this->mail->receive_time) : 0;
                    if(!$nowTime){
                        $nowTime = time();
                    }

                    //如果设置开始时间或者结束时间，邮件的收发间时间不在范围内，不发送
                    if(($startTime > 0 && $startTime > $nowTime) || ($endTime > 0 &&  $nowTime >= $endTime)){

                        return $holidayAutoReplyFlag;
                    }
                }
            }
        }


        //2.进行自动回复限制（防止因对方也设置自动回复而死循环）
        $rs = $this->checkAutoReply(\MailRule::MAIL_HOLIDAY_AUTO_REPLY);

        if(!$rs){
            return $holidayAutoReplyFlag;
        }

        $holidayAutoReplyFlag = true;

        return $holidayAutoReplyFlag;
    }

    /*
     * 判断是否可以自动回复；
     * 自动回复的发件人和收件人不能相同
     * 检测防止循环回复的隐藏值
     * $mailRuleType 自动回复邮件类型 1收件规则 2假期自动回复
     * 同一个邮箱地址，在4个自然天内最多执行一次自动回复
     */
    protected function checkAutoReply($mailRuleType = 1){


        $replyService  = new ReplyService($this->mail);
        $sender = Email::getEmailObject($replyService->getUserMailId(), $this->mail->getOperatorUserId())->email_address;
        $receiver = $this->mail->reply_to ?? $this->mail->sender;

        if(Util::getPureEmail($sender) == Util::getPureEmail($receiver)){
            return false;
        }

        $str = md5($this->encryptString);
        $content = $this->mail->getContent();
        if(stripos($content, $str) !== false){
            return false;
        }

        //是否在4天内，已经执行过收件规则自动回复或者假期自动回复
        if($this->isAlreadyAutoReply($mailRuleType)){
            return false;
        }

        //如果是分发邮件或者不是真实自己收到的邮件(导入,备份等), 不处理自动回复
        if ($this->isDistributeMail() || !$this->isRealReceiveMail())
        {
            return  false;
        }

        return true;
    }
    /*
     * 判断是否可以做自动转发。
     *
     * 在邮件内容中插入内容，对转发的邮件内容进行过滤。如果发现是转发的邮件则停止转发。
     */
    protected function checkAutoForward($operationForward){
        //前台已经对接受人做限制，但是这里仍然限制，主要是为了限制历史规则。
        if($operationForward == $this->user->getEmail()){
            return false;
        }
        $str = md5($this->encryptString);
        $content = $this->mail->getContent();
        if(stripos($content, $str) !== false){
            return false;
        }
        return true;
    }
    /**
     * 所有启用的规则列表
     */
    protected function getAllRuleList() {
        if($this->ruleList)
            return $this->ruleList;

        if (!$this->mail->user_id)
            return [];

        $userMailId = $this->mail->getUserMailId();
        $userId = $this->mail->getUserId();
        $clientId = $this->mail->getMailClientId();
        $redisKey = self::RULE_LIST_CACHE_PREFIX.'_'.$userId.'_'.$clientId.'_'.$this->type;

        //\Yii::app()->redis->delete($redisKey);//test
        $list = \Yii::app()->redis->get($redisKey);
        if(!$list){
            $listObj = new MailRuleList($userId);
            $listObj->setType($this->type);
            $listObj->setClientId($clientId);
            $listObj->setEnableFlag(1);
            $listObj->setLimit(500);
            $listObj->setOffset(0);
            $listObj->setOrderBy(['mail_rule_type desc','order_num asc', 'rule_id desc']);
            $list = $listObj->find();
            \Yii::app()->redis->set($redisKey, $list, 3600 * 24);
        }

        $userMailRule = [];
        foreach ($list as $item) {
            if($item['user_mail_id'] == 0 || $item['user_mail_id'] == $userMailId)
                $userMailRule[] = $item;
        }
        return $userMailRule;
    }

    //是否在4天内，已经执行过收件规则自动回复或者假期自动回复
    protected function isAlreadyAutoReply($mailRuleType) {

        $flag = true;
        $userId = $this->mail->user_id;
        $sendEmail = Util::getPureEmail($this->mail->sender);
        //redis的KEY类型是收件规则回复，还是假期自动回复
        $redisKey = $mailRuleType == \MailRule::MAIL_AUTO_REPLY ? self::AUTO_REPLY_CACHE_PREFIX : self::AUTO_REPLY_HOLIDAY_CACHE_PREFIX;
        $redisKey = $redisKey."_".$userId."_".$sendEmail;


        //\Yii::app()->redis->delete($redisKey);

        $autoReplyTime = \Yii::app()->redis->get($redisKey);

        if(!$autoReplyTime){
           $flag = false;
        }

        \LogUtil::info(sprintf("isAlreadyAutoReplyFLag user_id[%s] mail_id[%s] flag[%s] autoReplyTime[%s]",
            $this->mail->getUserId(),
            $this->mail->getMailId(),
            $flag,
            $autoReplyTime
        ));
        return $flag;
    }

    /**
     * 判断条件是否符合
     * @param array $rule
     * @return boolean
     */
    protected function matchFilter(array $rule) {

        $userMailId = $this->mail->getUserMailId();
        if ($rule['user_mail_id'] > 0 && $userMailId != $rule['user_mail_id'])
            return false;

        $filterSetting = is_array($rule['filter_settings'])?$rule['filter_settings']: json_decode($rule['filter_settings'], true);
        if (empty($filterSetting))
            return false;

        if ($rule['operation_move_to_folder'] == 0 && $rule['operation_mark_as_tag'] == 0 &&
            $rule['operation_trash_flag'] == 0 && $rule['operation_mark_as_read'] == 0 &&
            $rule['operation_pin'] == 0 && $rule['operation_forward'] == '' &&
            $rule['operation_auto_reply_content'] == '' && $rule['operation_todo_flag'] == 0 && $rule['operation_todo'] == '')
            return false;

        $relation = $rule['condition_relation'];

        $match = false;
        foreach ($filterSetting as $filter) {
            $filterKey = $filter['key'];
            $filterValue = $filter['value'];
            $filterType = $filter['type'];

            $matchContent = '';
            $fullMatch = 1;
            $res = false;

            if($this->type == \MailRule::TYPE_RECEIVE && $filterKey == 'filter_sender' && $filterType == self::RULE_FILTER_TYPE_SUBJECTION){
                //收件人隶属于（公海、私海）分组
                if($filterValue === ''){
                    continue;//兼容脏数据
                }
                $isPublic = isset($filter['is_public']) && $filter['is_public'] ? 1 : 0;
                
                // 先使用sender字段进行匹配
                if ($isPublic == self::CUSTOMER_GROUP_NOT_PUBLIC) {
                    $res = $this->matchCustomer($filterValue, self::CUSTOMER_GROUP_NOT_PUBLIC);
                } elseif ($isPublic == self::CUSTOMER_GROUP_PUBLIC) {
                    $res = $this->matchCustomer($filterValue, self::CUSTOMER_GROUP_PUBLIC);
                }
                
                // 如果sender字段匹配失败，并且reply_to字段不为空，则使用reply_to匹配
                if (!$res && !empty($this->mail->reply_to)) {
                    $replyToEmail = \common\library\email\Util::findAllEmails($this->mail->reply_to);
                    $replyToEmail = !empty($replyToEmail) ? $replyToEmail[0] : '';
                    
                    if (!empty($replyToEmail)) {
                        // 临时保存原始senderEmail
                        $originalSenderEmail = $this->senderEmail;
                        // 设置senderEmail为reply_to邮箱
                        $this->senderEmail = $replyToEmail;
                        
                        // 尝试用reply_to邮箱进行匹配
                        if ($isPublic == self::CUSTOMER_GROUP_NOT_PUBLIC) {
                            $res = $this->matchCustomer($filterValue, self::CUSTOMER_GROUP_NOT_PUBLIC);
                        } elseif ($isPublic == self::CUSTOMER_GROUP_PUBLIC) {
                            $res = $this->matchCustomer($filterValue, self::CUSTOMER_GROUP_PUBLIC);
                        }
                        
                        // 恢复原始senderEmail
                        $this->senderEmail = $originalSenderEmail;
                    }
                }
            } elseif ($filterKey == 'filter_sender') {
                // 处理发件人匹配
                $matchContent = $this->senderEmail;
                // 包含/不包含, 关键词用于匹配   昵称 <邮箱>
                if ($filterType == self::RULE_FILTER_TYPE_CONTAIN || $filterType == self::RULE_FILTER_TYPE_NOT_CONTAIN) {
                    $matchContent = $this->senderEmailRaw;
                }
                
                // 先尝试匹配 sender 字段
                $res = $this->filterAndMatch($matchContent, $filterType, $filterValue, $fullMatch);
                
                // 如果 sender 字段匹配失败，并且 reply_to 字段不为空，则尝试匹配 reply_to 字段
                if (!$res && !empty($this->mail->reply_to)) {
                    $replyToEmail = \common\library\email\Util::findAllEmails($this->mail->reply_to);
                    $replyToEmail = !empty($replyToEmail) ? $replyToEmail[0] : '';
                    
                    if (!empty($replyToEmail)) {
                        if ($filterType == self::RULE_FILTER_TYPE_CONTAIN || $filterType == self::RULE_FILTER_TYPE_NOT_CONTAIN) {
                            // 使用完整的 reply_to 进行匹配
                            $res = $this->filterAndMatch($this->mail->reply_to, $filterType, $filterValue, $fullMatch);
                        } else {
                            // 使用 reply_to 中提取的纯邮箱地址进行匹配
                            $res = $this->filterAndMatch($replyToEmail, $filterType, $filterValue, $fullMatch);
                        }
                    }
                }
            } elseif ($filterKey == 'filter_sender_domain') {
                // 先尝试匹配sender域名
                $matchContent = stristr($this->senderEmail, '@');
                $res = $this->filterAndMatch($matchContent, $filterType, $filterValue, $fullMatch);
                
                // 如果sender域名匹配失败，并且reply_to不为空，则尝试匹配reply_to域名
                if (!$res && !empty($this->mail->reply_to)) {
                    $replyToEmail = \common\library\email\Util::findAllEmails($this->mail->reply_to);
                    $replyToEmail = !empty($replyToEmail) ? $replyToEmail[0] : '';
                    
                    if (!empty($replyToEmail)) {
                        $replyToDomain = stristr($replyToEmail, '@');
                        if (!empty($replyToDomain)) {
                            $res = $this->filterAndMatch($replyToDomain, $filterType, $filterValue, $fullMatch);
                        }
                    }
                }
            } elseif ($filterKey == 'filter_receiver') {
                //收件人包含不包含
                $matchContentArray = Util::findAllEmails($this->mail->receiver);
                // 包含/不包含, 关键词用于匹配   昵称 <邮箱>
                if ($filterType == self::RULE_FILTER_TYPE_CONTAIN || $filterType == self::RULE_FILTER_TYPE_NOT_CONTAIN) {
                    $matchContentArray = [$this->mail->receiver];
                }
                $res = $this->filterAndMatch($matchContentArray,$filterType,$filterValue,$fullMatch);
            } elseif ($filterKey == 'filter_cc') {
                //抄送人
                $matchContentArray = Util::findAllEmails($this->mail->cc);
                $res = $this->filterAndMatch($matchContentArray,$filterType,$filterValue,$fullMatch);
            }elseif($filterKey == 'filter_content'){
                $matchContent = $this->mail->subject;
                $res = $this->filterAndMatch($matchContent, $filterType, $filterValue, $fullMatch);
            } else if ($filterKey == 'filter_text'){
                if ($filterValue == '') {
                    continue;
                }
                $matchContentStatus = $this->matchMailContent($filterValue);
                //包含、不包含rue
                if ($filterType == self::RULE_FILTER_TYPE_CONTAIN)
                    $res = $matchContentStatus;
                elseif ($filterType == self::RULE_FILTER_TYPE_NOT_CONTAIN)
                    $res = $matchContentStatus != true;
            }else {
                //兼容不存在key值的情况
                    $res = false;
            }

            //并、或
            if (!$res) {
                if($relation == self::CONDITION_AND)  //当各个条件关系为并时，只要一个失败，即可返回False;
                    return false;
            }
            else{
                if($relation == self::CONDITION_OR)  //当各个条件关系为或时，只要有一个成功，即可返回true.
                    return true;
            }

            $match = $res;
        }
        return $match;
    }

    /**
     * @var Customer[]
     */
    protected static $customerMap = [];

    /**
     * @param $groupId
     * @param int $publicFlag 0私海 1公海
     * @return bool
     */
    protected function matchCustomer($groupId, $publicFlag) {
        if(!$this->senderEmail){
            return false;
        }

        if (!isset(self::$customerMap[$this->senderEmail])) {
            self::$customerMap[$this->senderEmail] = [];
            $customer = new \common\library\customer_v3\customer\orm\Customer($this->user->getClientId());
            $customer->loadByEmail($this->senderEmail);

//            // 检查是否有符合条件的customer
            $customerList = new CustomerList($this->user->getClientId());
            $customerList->setAlias('a');
            $findFieldsArray = ['a.email', 'a.customer_id', 'b.user_id', 'b.group_id', 'b.company_id', 'b.pool_id'];
            $customerList->setJoin((new CompanyFilter($this->user->getClientId(), 'b')), BaseList::JOIN_TYPE_LEFT, ['company_id', 'company_id']);
            $findFields = implode(',', $findFieldsArray);
            $customerList->setFields($findFields);
            $customerList->setEmail($this->senderEmail);
            $customerList->setIsArchive(true);
            $customerList->setOrderBy('order_time');
            $customerList->setOrder('desc');
            $customerList = $customerList->find();
            foreach ($customerList as $item) {
                $customerUserIds = is_array($item['user_id']) ? $item['user_id'] : PgsqlUtil::trimArray($item['user_id']);
                self::$customerMap[$this->senderEmail][] = [
                    'customer_id' => $item['customer_id'],
                    'company_id' => $item['company_id'],
                    'user_id' => $customerUserIds,
                    'pool_id' => $item['pool_id'],
                    'group_id' => $item['group_id'],
                ];
            }
        }

        $matchEmailCustomerList = self::$customerMap[$this->senderEmail];
        if (empty($matchEmailCustomerList)) {
            return false;
        }

        $matchFlag = false;
        foreach ($matchEmailCustomerList as $customerItem) {
            if ($publicFlag == self::CUSTOMER_GROUP_NOT_PUBLIC) {
                if (!empty($customerItem['user_id']) && in_array($this->mail->getUserId(), $customerItem['user_id'])) {
                    if (($groupId < 0) || ($groupId == $customerItem['group_id'])) {
                        $matchFlag = true;
                        break;
                    }
                }
            } else {
                if (empty($customerItem['user_id'])) {
                    $matchFlag = true;
                    break;
                }
            }
            continue;
        }
        return $matchFlag;
    }

    /**
     * @param $content
     * @param $filter
     * @param $fullMatch
     * @return bool
     * 匹配内容
     */
    protected function match($content, $filter, $fullMatch = 1) {
        if($content === false  || is_null($content) || $content === '')
            return false;

        $content = strtolower($content);
        $filter = strtolower($filter);

        if($fullMatch > 0)
            return $content == trim($filter);
        else
            return stripos($content, trim($filter)) !== false;
    }


    public static function delRuleCache($clientId, $userId, $type = null): bool
    {
        $allTypes = [
            \MailRule::TYPE_RECEIVE,
            \MailRule::TYPE_SEND,
        ];

        //指定type 删除指定的type
        if ($type && in_array($type, $allTypes)) {
            $redisKey = self::RULE_LIST_CACHE_PREFIX . '_' . $userId . '_' . $clientId . '_' . $type;
            return \Yii::app()->redis->delete($redisKey);
        }

        //默认null，删除全部type
        if ($type === null) {
            foreach ($allTypes as $item) {
                $redisKey = self::RULE_LIST_CACHE_PREFIX.'_'.$userId.'_'.$clientId.'_'.$item;
                \Yii::app()->redis->delete($redisKey);
            }
            return true;
        }

        //其他不处理
        return false;
    }

	/**
	 * 验证收件人
	 *
	 * @return array
	 */
	private function isRealReceiveMail(): array {

		$aliasList = Email::getEmailObject($this->mail->user_mail_id)->getAllEmailAddress();

		$receiverList = Util::findAllEmails($this->mail->receiver);

		return array_intersect($receiverList, $aliasList);
	}

	/**
	 * 是否是分发邮件
	 *
	 * @return bool
	 */
	private function isDistributeMail(): bool {

		$mailTagList = \MailTagAssoc::getMailTagList($this->mail->mail_id);

		$tagIdArr = array_column($mailTagList, 'tag_id');

		//2 已分发邮件
		return in_array(Tag::MAIL_TAG_DISTRIBUTE_RECEIVE, $tagIdArr);
	}

    /**
     * 邮件内容是否包含关键字
     */
	private function matchMailContent($filter, $fullMatch =0)
    {
        //获取邮件正文内容
        $body = Helper::getMailBodyContent($this->mail->user_mail_id, $this->mail->mail_id, $this->mail->client_id);

        if ($body) {
            //匹配是否包含关键字
            return $this->match($body,$filter,$fullMatch);
        }
        return false;

    }

    private function filterMailContent($content)
    {
        $content = \Util::plainText($content);
        if (empty($content))
            return '';

        //处理回复转发-------- 原始邮件 --------，------------------ Original ------------------
        $replaceString = "#######TAG######";
        $content = preg_replace("/[\\-]{6,}/", $replaceString, $content);

        $endPos = strpos($content, $replaceString);
        if ($endPos === false)
            return $content;

        $content = substr($content,0, $endPos);

        return $content ? $content : '';
    }

    private function filterAndMatch($matchContent,$filterType,$filterValue,$fullMatch =1) {

        //包含则不是全匹配
        if ($filterType == self::RULE_FILTER_TYPE_CONTAIN || $filterType == self::RULE_FILTER_TYPE_NOT_CONTAIN) {
            $fullMatch = 0;
        }


        if (!is_array($matchContent)) {
            $matchContent = [$matchContent];
        }

        $matchCount = 0;
        $res = false;
        if($matchContent){
            $filterArr = array_filter(explode(';', str_replace('；', ';', $filterValue)));
            foreach ($filterArr as $item) {
                foreach ($matchContent as $content) {
                    $matchBoll = $this->match($content, $item, $fullMatch);
                    if ($matchBoll)
                        $matchCount++;
                }
            }
        }

        //相等/不相等/包含/不包含
        if ($filterType == self::RULE_FILTER_TYPE_EQUAL || $filterType == self::RULE_FILTER_TYPE_CONTAIN)
            $res = $matchCount;
        elseif ($filterType == self::RULE_FILTER_TYPE_NOT_EQUAL || $filterType == self::RULE_FILTER_TYPE_NOT_CONTAIN)
            $res = $matchCount != true;
        return $res;
    }
}
