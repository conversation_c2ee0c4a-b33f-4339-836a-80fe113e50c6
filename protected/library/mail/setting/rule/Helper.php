<?php

namespace common\library\mail\setting\rule;

use common\library\api\InnerApi;
use common\library\mail\MailContentHelper;
use common\library\report\error\ErrorReport;
use common\library\util\SqlBuilder;
use common\library\version\Constant;
use common\library\version\UserModuleVersion;


/**
 * Created by PhpStorm.
 * User: beenzhang
 * Date: 2018/3/2
 * Time: 下午4:08
 */
class Helper
{

    public static function updateOrder($userId, $clientId, array $ids)
    {
        $model = \MailRule::model();
        $table = $model->tableName();
        $sql = array();
        foreach ($ids as $k => $id) {
            $index = $k + 1;
            $sql[] = "update {$table} set order_num = {$index} where rule_id = {$id} and user_id = {$userId}";
        }
        if (!$sql) {
            return true;
        }
        $ret = $model->getDbConnection()->createCommand(implode(';', $sql))->execute();

        $version = new UserModuleVersion($userId);
        $version->setModule(Constant::USER_MODULE_MAIL_RULE);
        $version->add();

        MailRuleOperator::delRuleCache($clientId, $userId);
        return $ret;
    }

    public static function delBatch($userId, $clientId, array $ids)
    {
        $ids = array_filter($ids);
        if (empty($ids)) {
            return false;
        }
        $params = [':user_id' => $userId];
        $condition = '';
        SqlBuilder::buildIntWhere('','rule_id',$ids,$condition,$params,'');
        $condition .= " AND user_id = :user_id";

        $ret = \MailRule::model()->deleteAll($condition, $params);

        $version = new UserModuleVersion($userId);
        $version->setModule(Constant::USER_MODULE_MAIL_RULE);
        $version->add();

        MailRuleOperator::delRuleCache($clientId, $userId);
        return $ret;
    }

    /**
     * @param $user_id
     * @param $client_id
     * @param array | int $folder_id
     * @return int
     * 用于删除自定义文件夹时删除跟这个文件夹相关的收件规则
     */
    public static function delByFolder($user_id, $client_id, $folder_id)
    {

        $condition = 'client_id = :client_id AND user_id = :user_id';
        $params = array(':client_id' => $client_id, ':user_id' => $user_id);

        SqlBuilder::buildIntWhere('','operation_move_to_folder',$folder_id,$condition,$params);

        $result = \MailRule::model()->deleteAll($condition, $params);

        $version = new UserModuleVersion($user_id);
        $version->setModule(Constant::USER_MODULE_MAIL_RULE);
        $version->add();

        MailRuleOperator::delRuleCache($client_id, $user_id);
        return $result;
    }

    /**
     * @param $user_id
     * @param $client_id
     * @param $tag_ids
     * @return int
     * 用于删除标签时删除跟该标签相关的收件规则
     */
    public static function delBatchByGeneralTag($user_id, $client_id, $tag_ids)
    {
        if (is_array($tag_ids)) {
            $tag_ids = implode(',', $tag_ids);
        }
        $params = array(':client_id' => $client_id, ':user_id' => $user_id);

        $condition = "client_id = :client_id AND user_id = :user_id";

        SqlBuilder::buildIntWhere('','operation_mark_as_tag',$tag_ids,$condition,$params);

        $result = \MailRule::model()->deleteAll($condition, $params);

        $version = new UserModuleVersion($user_id);
        $version->setModule(Constant::USER_MODULE_MAIL_RULE);
        $version->add();

        MailRuleOperator::delRuleCache($client_id, $user_id);
        return $result;
    }

    public static function buildMergeRules($clientId, $userId, $ruleData = null)
    {
        $map = [];

        if ($ruleData === null) {
            $list = new \common\library\mail\setting\rule\MailRuleList($userId);
            $list->setClientId($clientId);
            $list->setmailRuleType(\MailRule::MAIL_AUTO_REPLY);
            $list->setType(\MailRule::TYPE_RECEIVE);
            $list->setEnableFlag(1);
            $ruleData = $list->find();
        }


        foreach ($ruleData as $item) {
            if (self::checkMergeRule($item)) {
                $filterSettingArr = is_array($item['filter_settings']) ? $item['filter_settings'] : json_decode($item['filter_settings'], true);
                $map[$item['user_mail_id']][$item['operation_move_to_folder']]['same_rule_ids'][] = $item['rule_id'];

                $emailTypeMap = [];
                foreach ($filterSettingArr as $filterItem) {
                    if (!empty($filterItem['value']) && !empty($filterItem['type'])) {
                        $emailTypeMap[] = [
                            'email' => $filterItem['value'],
                            'type' => $filterItem['type']
                        ];
                    }
                }
                $map[$item['user_mail_id']][$item['operation_move_to_folder']]['senders'][] = $emailTypeMap;
            }
        }

        return $map;
    }

    /**
     * @param $ruleItem
     * @return bool
     * <AUTHOR>
     * @date 2019-11-01 11:32
     * 判断是否当前规则满足合并规则的前置条件
     */
    public static function checkMergeRule($ruleItem)
    {
        $filterSettingArr = is_array($ruleItem['filter_settings']) ? $ruleItem['filter_settings'] : json_decode($ruleItem['filter_settings'], true);
        $filterFlag = !empty($filterSettingArr);

        if (!$filterFlag) {
            return false;
        }

        foreach ($filterSettingArr as $settingItem) {
            if ($settingItem['key'] != 'filter_sender') {
                $filterFlag = false;
                break;
            }
        }

        if (!$filterFlag || $ruleItem['condition_relation'] != 1
            || $ruleItem['operation_auto_reply_content_flag'] != 0
            || $ruleItem['operation_mark_as_read'] != 0
            || $ruleItem['operation_mark_as_tag_flag'] != 0
            || $ruleItem['operation_pin'] != 0
            || $ruleItem['operation_trash_flag'] != 0
            || !$ruleItem['enable_flag']
            || $ruleItem['history_flag'] != 1
            || $ruleItem['history_folder_id'] != -1
            || $ruleItem['execute_next'] != 0
            || $ruleItem['operation_move_to_folder_flag'] != 1
            || $ruleItem['operation_todo_flag'] != 0) {
            return false;
        }

        return true;
    }

    public static function getMailBodyContent(string $user_mail_id, string $mail_id, $client_id)
    {
        $content = '';

        $api = new InnerApi('mail_classify_extract_data');
        $api->setApiNoCodeHandler(function ($json) {
            return;
        });

        try {
            $data = $api->call('get_email_content', [
                'mail_id' => $mail_id, 'user_mail_id' => $user_mail_id,
            ]);

        } catch (\Exception $e) {
            \LogUtil::error("mailId[{$mail_id}] get_email_content failed -- {$e->getMessage()}");
            ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace(), null);
            $data = [];
        }

        if (isset($data['data']['structInfo']['data']) && $data['data']['structInfo']['data'] !== false && !empty($data['data']['structInfo']['data'])) {
            $content = $data['data']['structInfo']['data'] ?? '';
        } else {

            try {
                $mailContent = MailContentHelper::getContentAndPlainText($client_id, $user_mail_id, $mail_id);
                $content = self::filterMailContent($mailContent['plainText']??'');
            } catch (\Exception $e) {
                \LogUtil::error("mailId[{$mail_id}] getContentAndPlainText failed -- {$e->getMessage()}");
                ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace(), null);
                $content = '';
            }
        }

        return $content;
    }

    public static function filterMailContent($content)
    {
        $content = \Util::plainText($content);
        if (empty($content))
            return '';

        //处理回复转发-------- 原始邮件 --------，------------------ Original ------------------
        $replaceString = "#######TAG######";
        $content = preg_replace("/[\\-]{6,}/", $replaceString, $content);

        $endPos = strpos($content, $replaceString);
        if ($endPos === false)
            return $content;

        $content = substr($content,0, $endPos);

        return $content ? $content : '';
    }

}