<?php


namespace common\library\mail\service;


use common\library\account\UserInfo;
use common\library\customer_v3\customer\CustomerList;
use common\library\department\DepartmentPermission;
use common\library\email\Util;
use common\library\mail\MailList;
use common\library\privilege_v3\PrivilegeConstants;

class BatchAccessService
{
    const READ_ACCESS_ROLE = [
        'exist',
        'unbind',
        'owner',
        'developer',
        'superAdmin',
        'departmentAdmin',
        'cooperation',
        'approval',
        'visible',
    ];
    const WRITE_ACCESS_ROLE = [
        'owner'
    ];
    const FORWARD_ACCESS_ROLE = [
        'unbind',
        'owner',
        'cooperation',
        'departmentAdmin',
        'superAdmin',
        'visible',
    ];
    const REPLY_ACCESS_ROLE = [
        'unbind',
        'owner',
        'cooperation',
        'departmentAdmin',
        'superAdmin',
        'visible',
    ];
    const DELETE_ACCESS_ROLE = [
        'owner',
        'visible',
    ];
    const SEND_ACCESS_ROLE = [
        'owner',
        'approval',
    ];

    private $noParamsFunc = [
        'developer',
        'superAdmin',
    ];

    private $mailIds;
    private $mailList;
    private $opUserId;
    private $cooperationMap = [];

    public function __construct($opUserId, array $mailIds)
    {
        $this->mailIds = $mailIds;
        $mailList = new MailList(null, $opUserId);
        $mailList->setMailIds($mailIds);
        $mailList->setFormatter(false);
        $mailList->setFields([
            'mail_id', 'user_id', 'client_id', 'delete_flag', 'mail_type',
            'sender', 'receiver', 'cc', 'bcc', 'reply_to',
        ]);
        $this->mailList = array_column($mailList->find(), null, 'mail_id');
        $this->opUserId = $opUserId;
    }

    public function checkAll($roles)
    {
        if (in_array('cooperation', $roles)) {
            $this->buildCooperationMap();
        }
        foreach ($this->mailList as $mailInfo) {
            if (!$this->checkOne($roles, $mailInfo)) {
                return false;
            }
        }
        return true;
    }

    public function checkOne($roles, $mailInfo)
    {
        foreach ($roles as $role) {
            $function = 'is' . ucfirst($role);
            if (method_exists($this, $function)) {
                if (in_array($role, $this->noParamsFunc)) {
                    if ($this->$function()) return true;
                } else {
                    if ($this->$function($mailInfo)) return true;
                }

            }
        }
        return false;
    }

    public function isExist($mailInfo)
    {
        $isNotExist = ($mailInfo['delete_flag'] == \Mail::DELETE_FLAG_DELETE);
        if ($isNotExist) {
            throw new \RuntimeException('邮件已被彻底删除',1219);
        }
        return false;
    }

    public function isUnbind($mailInfo)
    {
        if ($mailInfo['user_id'] == 0) {
            return true;
        }
        return false;
    }

    public function isDeveloper()
    {
        return \User::getUserObject($this->opUserId)->isSupperPasswordLogin();
    }

    public function isSupperAdmin()
    {
        return \User::getUserObject($this->opUserId)->getAdminType() == UserInfo::ADMIN_TYPE_SUPER;
    }

    public function isDepartmentAdmin($mailInfo)
    {
        $user = \User::getUserObject($this->opUserId);
        if ($user->getAdminType() == UserInfo::ADMIN_TYPE_DEPARTMENT) {
            $manageableUserList = $user->getAllManageableUserIds();
            if (in_array($mailInfo['user_id'], $manageableUserList)) {
                return true;
            }
            return false;
        }
        return false;
    }

    public function isApproval($mailInfo)
    {
        $approvingInfos = \common\library\approval_flow\Helper::getReferApprovalInfos($mailInfo['client_id'], $mailInfo['mail_id']);
        $approverList = array_unique(array_column($approvingInfos, 'approver'));
        if (!empty($approverList) && in_array($this->opUserId, $approverList)) {
            return true;
        }

        return false;
    }

    public function isOwner($mailInfo)
    {
        return $this->opUserId == $mailInfo['user_id'];
    }

    /**
     * 协作者
     *
     * 1 该邮件的客户联系人是当前操作人的私海客户，多个联系人的情况
     * 2 该邮件的客户联系人是公海客户
     * @param $mailInfo
     * @return bool
     */
    public function isCooperation($mailInfo)
    {
        return ($this->cooperationMap[$mailInfo['mail_id']] ?? 0) ? true : false;
    }

    public function isVisible($mailInfo)
    {
        $clientId = \User::getUserObject($this->opUserId)->getClientId();
        $departmentPermission = new DepartmentPermission($clientId, $this->opUserId);
        $departmentPermission->permission(PrivilegeConstants::PRIVILEGE_EMAIL_VIEW);
        $result = $departmentPermission->userList();
        if (!empty($result)) {
            $userList = array_column($result, 'user_id');
            if (in_array($mailInfo['user_id'], $userList)) {
                return true;
            }
        }
        return false;
    }

    public function checkReadAccess()
    {
        if (!$this->checkAll(self::READ_ACCESS_ROLE)) {
            throw new \RuntimeException('邮件读取权限校验未通过',1220);
        }
        return true;
    }

    public function checkWriteAccess()
    {
        if (!$this->checkAll(self::WRITE_ACCESS_ROLE)) {
            throw new \RuntimeException('邮件写入权限校验失败,请确认是否切换了登录账号!',1221);
        }
        return true;
    }

    public function checkSendAccess()
    {
        if (!$this->checkAll(self::SEND_ACCESS_ROLE)) {
            throw new \RuntimeException('邮件发送权限校验未通过',1222);
        }
        return true;
    }

    public function checkDeleteAccess()
    {
        if (!$this->checkAll(self::DELETE_ACCESS_ROLE)) {
            throw new \RuntimeException('邮件删除权限校验未通过',1223);
        }
        return true;

    }

    /**
     * 校验邮件回复权限
     * @return bool
     */
    public function checkReplyAccess()
    {
        if (!$this->checkAll(self::REPLY_ACCESS_ROLE)) {
            throw new \RuntimeException('邮件回复权限校验未通过',1224);
        }
        return true;
    }

    /**
     * 校验邮件转发权限
     * @return bool
     */
    public function checkForwardAccess()
    {
        if (!$this->checkAll(self::FORWARD_ACCESS_ROLE)) {
            throw new \RuntimeException('邮件转发权限校验未通过',1225);
        }
        return true;
    }

    public function buildCooperationMap()
    {
        $allEmails = [];
        $mailToEmailsMap = [];
        foreach ($this->mailList as $mailInfo)
        {
            if ($mailInfo['mail_type'] == \Mail::MAIL_TYPE_RECEIVE) {
                $contactEmails = [
                    trim($mailInfo['sender']),
                    trim($mailInfo['reply_to'])
                ];
            } else {
                $contactEmails = [
                    trim($mailInfo['receiver']),
                    trim($mailInfo['cc']),
                    trim($mailInfo['bcc'])
                ];
            }
            $contactEmails = array_unique(Util::findAllEmails($contactEmails));
            $mailToEmailsMap[$mailInfo['mail_id']] = $contactEmails;

            $allEmails = array_merge($allEmails, $contactEmails);
        }
        if (empty($allEmails)) return;

        $customerList = new CustomerList(\User::getUserObject($this->opUserId)->getClientId());
        $customerList->setEmail($allEmails);
        $customerList->setUserId($this->opUserId);
        $customerList->setFields(['email']);
        $privateCustomerEmails = array_column($customerList->find(), 'email');

        $customerList->setUserId(0);
        $publicCustomerEmails = array_column($customerList->find(), 'email');

        foreach ($mailToEmailsMap as $mailId => $emailArr)
        {
            foreach ($emailArr as $email) {
                if (in_array($email, $privateCustomerEmails) || in_array($email, $publicCustomerEmails)) {
                    $this->cooperationMap[$mailId] = 1;
                    break;
                }
            }
            if (!isset($this->cooperationMap[$mailId])) {
                $this->cooperationMap[$mailId] = 0;
            }
        }
    }
}