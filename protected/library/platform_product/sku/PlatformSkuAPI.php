<?php
/**
 * This file is part of php-crm.
 *
 * <AUTHOR> <<EMAIL>>
 * @created_at  2021/8/14 11:28 上午
 */

namespace common\library\platform_product\sku;

use ArrayUtil;
use common\components\BaseObject;
use common\library\account\Client;
use common\library\APIConstant;
use common\library\CommandRunner;
use common\library\custom_field\CustomFieldService;
use common\library\exchange_rate\ExchangeRateService;
use common\library\invoice\Helper;
use common\library\product_v2\ProductConstant;
use common\library\product_v2\sku\ProductSku;
use common\library\product_v2\sku\ProductSkuFilter;
use common\library\platform_product\relation\PlatformProductRelation;
use common\library\platform_product\relation\PlatformProductRelationAPI;
use common\library\platform_product\relation\PlatformProductRelationFilter;
use common\library\platform_product\PlatformProductAPI;
use common\library\platform_product\PlatformProductConstants;
use common\library\platform_product\PlatformProductFilter;
use RuntimeException;
use User;
use xiaoman\orm\database\data\In;
use xiaoman\orm\database\data\NotEqual;
use function DeepCopy\deep_copy;

class PlatformSkuAPI
{
    private $clientId;
    private $userId;

    public function __construct($clientId, $userId = null)
    {
        $this->clientId = $clientId;
        $this->userId   = $userId;
    }

    /**
     * SKU产品列表
     * @param array $params
     * @return array
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function skuList(array $params)
    {
        $skuCols = [
            'sku' => function ($column, $tableName, $systemQuto) {
                return "{$tableName}.*";
            },
        ];
        /** @var $filter \xiaoman\orm\common\Filter */
        /** @var $skuFilter PlatformSkuFilter */
        /** @var $productFilter PlatformProductFilter */
        [$filter, $skuFilter, $productFilter] = $this->webQueryFilter($params, []);

        $data = ['count' => 0, 'list' => [], 'product_count' => 0];

        $cloneFilter = deep_copy($filter);
        $tbl = $productFilter->getTableName();
        $skuTbl = $skuFilter->getTableName();
        $cloneFilter->select([
            'third_del_count' => function () use ($skuTbl) {
                return "sum({$skuTbl}.third_delete) as third_del_count";
            },
            'product_count' => function () use ($tbl) {
                return "count(distinct {$tbl}.platform_product_id) as product_count";
            },
            'count'         => function () {
                return 'count(1) as count';
            },
        ]);
        $count = $cloneFilter->rawData();

        $data['count']         = $count[0]['count'] ?? 0;
        $data['product_count'] = $count[0]['product_count'] ?? 0;
        !empty($params['get_third_del_count']) && $data['third_del_count'] = $count[0]['third_del_count'] ?? 0;
        if (!$data['count']) {
            return $data;
        }

        $filter->joinOrder('platform_sku_id', 'desc', $skuFilter->getTableName());
        $skuFilter->select($skuCols);
        $productFilter->select(['images', 'from_url', 'name', 'sku_attributes', 'sync_time', 'model']);
        isset($params['cur_page'], $params['page_size']) && $filter->joinlimit($params['page_size'], ($params['cur_page'] - 1) * $params['page_size']);

        $rawData = $filter->rawData();


        $batch = new BatchPlatformSku($this->clientId);
        $batch->initFromData($rawData);

        $batch->getFormatter()->webListSetting();
        $data['list'] = $batch->getAttributes();

        return $data;
    }

    /**
     * @param array $params
     * @param array $columns
     * @return array
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function skus(array $params, array $columns)
    {
        $filter = $this->buildFilter($params);
        $filter->select($columns);
        return $filter->find()->getAttributes($columns);
    }

    /**
     * @param $params
     * @param array|null $skuColumns
     * @param array|null $productColumns
     * @return array
     * @throws \xiaoman\orm\exception\OrmException
     */
    public function webQueryFilter($params, array $skuColumns, array $productColumns = [])
    {
        $skuFilter = $filter = $this->buildFilter($params);

        $filter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
        $filter->initJoin();
        $filter->select($skuColumns);

        // 搜索本地产品编号
        if (isset($params['product_no_kw']) && $params['product_no_kw'] !== '') {
            $localFilter = new ProductSkuFilter($this->clientId);
            $searcher    = $localFilter->getSearcher();
            $skuIds      = $searcher->paramsMapping(['product_no_keyword' => $params['product_no_kw']])->findIds();

            $relationApi    = new PlatformProductRelationAPI($this->clientId);
            $relationFilter = $relationApi->buildFilter([
                'enable_flag' => BaseObject::ENABLE_FLAG_TRUE,
            ]);

            if ($skuIds) {
                $relationFilter->sku_id = $skuIds;
            } else {
                $relationFilter->alwaysEmpty();
            }
            unset($params['product_no_kw']);

            $filter = $filter->initJoin()
                ->innerJoin($relationFilter)
                ->on('platform_sku_id', 'platform_sku_id');
        }

        // 平台产品
        $productApi         = new PlatformProductAPI($this->clientId, $this->userId);
        $thirdProductFilter = $productApi->buildFilter($params);

        $thirdProductFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
        $thirdProductFilter->select($productColumns);

        $filter->innerJoin($thirdProductFilter)
            ->on('platform_product_id', 'platform_product_id');

        return [$filter, $skuFilter, $thirdProductFilter];
    }

    /**
     * 数量信息
     * @return int[]
     * @throws \xiaoman\orm\exception\OrmException
     */
    public function webSummary()
    {
        $filter              = new PlatformSkuFilter($this->clientId);
        $filter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
        $countCol            = [
            'count',
            function ($column, $table, $systemQuotes) {
                return 'count(1) as count';
            },
        ];

        $filter->aggregation(['is_match', 'third_delete', $countCol], ['is_match', 'third_delete']);

        $data = $filter->aggregation(['is_match', 'third_delete', $countCol], ['is_match', 'third_delete'])
            ->getAttributes(['is_match', 'third_delete', 'count']);

        $summary = [
            'total'         => 0,
            'matched'       => 0,
            'unmatched'     => 0,
            'third_deleted' => 0,
        ];
        foreach ($data as $item) {
            $summary['total'] += $item['count'];
            if ($item['is_match']) {
                $summary['matched'] += $item['count'];
            } else {
                $summary['unmatched'] += $item['count'];
            }

            $item['third_delete'] && $summary['third_deleted'] += $item['count'];

        }

        return $summary;
    }

    /**
     * @param array $params
     * @return PlatformSkuFilter
     */
    public function buildFilter(array $params)
    {
        $filter = new PlatformSkuFilter($this->clientId);

        !empty($params['cur_page']) && !empty($params['page_size']) && $filter->limit($params['page_size'], $params['cur_page']);

        $taskApi = new \common\library\platform_product\task\PlatformProductTaskApi($this->clientId, $this->userId);
        if (!empty($params['task_id'])) {
            $platformSkuIds = $taskApi->filterThirdProductByTask($params['task_id'], PlatformProductConstants::TASK_HISTORY_SUCCESS);
            if (empty($platformSkuIds)) {
                $filter->alwaysEmpty();
            } else {
                $params['platform_sku_id'] = new In($platformSkuIds);
            }
        }

        if (!empty($params['gen_task_id'])) {
            $platformSkuIds = $taskApi->filterThirdProductByTask($params['gen_task_id'], PlatformProductConstants::TASK_HISTORY_FAILED);
            if (empty($platformSkuIds)) {
                $filter->alwaysEmpty();
            } else {
                $params['platform_sku_id'] = new In($platformSkuIds);
            }
        }

        $searchParams = ArrayUtil::columns(['third_product_name', 'third_product_model', 'third_product_id', 'third_sku_code'], $params);
        $searchParams = array_filter($searchParams, function ($item) {
            return trim($item) !== '';
        });
        if (!empty($searchParams)) {
            $filter->search($searchParams);
            unset($params['third_product_name'], $params['third_product_model'], $params['third_product_id'], $params['third_sku_code']);
        }

        if (!empty($params['store_id'])) {
            $params['store_id']     = array_filter($params['store_id']);
            $filter->third_store_id = $params['store_id'];
        }
        !empty($params['third_store_id']) && $filter->third_store_id = $params['third_store_id'];

        isset($params['is_match']) && $filter->is_match = $params['is_match'];
        isset($params['third_delete']) && $filter->third_delete = $params['third_delete'];
        isset($params['platform']) && $filter->platform = $params['platform'];
        isset($params['enable_flag']) && $filter->enable_flag = $params['enable_flag'];

        isset($params['platform_sku_id']) && $filter->platform_sku_id = $params['platform_sku_id'];
        isset($params['platform_product_id']) && $filter->platform_product_id = $params['platform_product_id'];
        isset($params['third_sku_id']) && $filter->third_sku_id = $params['third_sku_id'];

        return $filter;
    }

    /**
     * 删除 疑似删除
     * @param array $params
     * @param bool $thirdDeleted
     * @return int
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function delete(array $params, ?bool $thirdDeleted = PlatformProductConstants::THIRD_DELETED)
    {
        !is_null($thirdDeleted) && $params['third_delete'] = $thirdDeleted;
        $params['enable_flag'] = BaseObject::ENABLE_FLAG_TRUE;
        /** @var \xiaoman\orm\common\Filter $filter */

        $selectColumns = ['platform_sku_id', 'platform_product_id'];
        if((!empty($params['platform_sku_id']) || !empty($params['platform_product_id'])) && $thirdDeleted === null){
            $filter = new PlatformSkuFilter($this->clientId);
            $filter->buildWhereIdsClause($params['platform_product_id'], $params['platform_sku_id'], 'or');
            $filter->select($selectColumns);
        }else{
            [$filter] = $this->webQueryFilter($params, $selectColumns);
        }

        $batch = $filter->find();
        $batch->setDomainHandler(User::getUserObject($this->userId));
        return $batch->getOperator()->delete();
    }

    /**
     * 删除所有疑似删除的
     * @return int
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function deleteAllThirdDeleted()
    {
        $filter = $this->buildFilter([
            'third_delete' => PlatformProductConstants::THIRD_DELETED,
            'enable_flag'  => BaseObject::ENABLE_FLAG_TRUE,
        ]);
        $filter->select(['platform_sku_id']);
        $platformSkuIds = array_column($filter->find()->getAttributes(['platform_sku_id']), 'platform_sku_id');

        $count  = 0;
        $chunks = array_chunk($platformSkuIds, 1000);
        foreach ($chunks as $chunk) {
            $count += $this->delete(['platform_sku_id' => $chunk]);
        }

        return $count;
    }

    /**
     * 通过PlatformSkuId获取平台产品sku
     * @param array $platformSkuIds
     * @param bool $includeDeleted
     * @param string $scene
     * @return array
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function productSkusByIds(array $platformSkuIds, bool $includeDeleted = false, string $scene = APIConstant::SCENE_LIST)
    {
        $params = ['platform_sku_id' => $platformSkuIds];
        $includeDeleted && $params['enable_flag'] = [BaseObject::ENABLE_FLAG_FALSE, BaseObject::ENABLE_FLAG_TRUE];
        return $this->productSkus($params, $scene);
    }

    /**
     * 通过ThirdSkuId获取平台产品sku
     * @param array $thirdSkuIds
     * @param string $scene
     * @param string $platform
     * @return array
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function productSkusByThirdIds(array $thirdSkuIds, string $scene = APIConstant::SCENE_LIST, string $platform = PlatformProductConstants::PLATFORM_ALIBABA)
    {
        return $this->productSkus(['third_sku_id' => $thirdSkuIds, 'platform' => $platform,], $scene);
    }


    /**
     * @param array $params
     * @param string $scene
     * @return array
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function productSkus(array $params, string $scene)
    {
        switch ($scene) {
            case APIConstant::SCENE_LIST:
                $skuCols     = [
                    'platform_sku_id', 'third_sku_code', 'third_sku_id', 'third_product_id', 'platform_product_id',
                    'enable_flag', 'sku_images', 'attributes', 'is_match',
                ];
                $productCols = ['name', 'model', 'unit', 'enable_flag' => 'product_enable_flag', 'sku_attributes', 'from_url', 'cipher_product_id'];
                $filter      = $this->buildProductSkuFilter($params, $skuCols, $productCols);
                $batch       = $filter->find();
                $batch->getFormatter()->baseListSetting();
                return $batch->getAttributes();
            default:
                return [];
        }
    }

    /**
     * 产品+Sku（默认只获取enable_flag=1）
     * @param array $params
     * @param array|null $skuCols
     * @param array|null $thirdProductCols
     * @return \xiaoman\orm\common\Filter
     */
    public function buildProductSkuFilter(array $params, array $skuCols = null, array $thirdProductCols = null)
    {
        if (!isset($params['enable_flag'])) {
            $params['enable_flag'] = BaseObject::ENABLE_FLAG_TRUE;
        }
        $filter = $this->buildFilter($params);
        if (null === $skuCols) {
            $skuCols = [
                'sku' => function ($column, $tableName, $systemQuto) {
                    return "{$tableName}.*";
                },
            ];
        }
        $filter->select($skuCols);

        $productApi    = new PlatformProductAPI($this->clientId, $this->userId);
        $productFilter = $productApi->buildFilter($params);
        if (null === $thirdProductCols) {
            $thirdProductCols = [
                'product' => function ($column, $tableName, $systemQuto) {
                    return "{$tableName}.*";
                },
            ];
        }
        $productFilter->select($thirdProductCols);

        $filter->initJoin()
            ->innerJoin($productFilter)
            ->on('platform_product_id', 'platform_product_id');

        return $filter;
    }

    public function batchMatchTo($platformSkuIds, $skuId)
    {
        $skuFilter = new PlatformSkuFilter($this->clientId);
        $skuFilter->platform_sku_id = $platformSkuIds;
        $skuFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $batch = $skuFilter->find();
        return $batch->getOperator()->batchMatch($platformSkuIds,$skuId);
    }

    /**
     * 匹配到本地产品
     * @param PlatformSku|int ThirdProductSku|$platformSkuId
     * @param ProductSku|int ProductSku|$skuId
     * @return bool
     */
    public function matchTo($platformSkuId, $skuId)
    {
        $thirdSku = $platformSkuId;
        if (!is_object($thirdSku)) {
            $thirdSku = new PlatformSku($this->clientId, $thirdSku);
        }

        if ($thirdSku->isNew() || !$thirdSku->enable_flag) {
            \LogUtil::error("[matchTo] third sku not exists, platform_sku_id: {$thirdSku->platform_sku_id}, sku_id: {$skuId}");
            return false;
        }

        $sku = $skuId;
        if (!is_object($sku)) {
            $sku = new ProductSku($this->clientId, $sku);
        }

        if ($sku->isNew() || !$sku->enable_flag) {
            throw new RuntimeException(\Yii::t('common', 'Data does not exist or deleted'));
        }

        // 疑似删除
        //if ($thirdSku->third_delete == ThirdProductConstants::THIRD_DELETED) {
        //    throw new RuntimeException(\Yii::t('platform_product', 'Suspected deleted products cannot be used for matching'));
        //}

        //== 放开限制，不检查
        //== 【平台产品】多规格产品和无规格产品可混合匹配 https://www.tapd.cn/21404721/s/1764403
        // 只能匹配相同类型的产品
        //if (
        //    ($thirdSku->third_sku_id == 0 && !empty($sku->attributes)) ||
        //    ($thirdSku->third_sku_id != 0 && empty($sku->attributes))
        //) {
        //    throw new RuntimeException(\Yii::t('platform_product', 'Only local products of the same type can be paired'));
        //}

        $relationObj = new PlatformProductRelation($this->clientId);
        $relationObj->loadByPlatformSkuId($thirdSku->platform_sku_id);
        // 未变更
        if (!$relationObj->isNew() && $relationObj->enable_flag == BaseObject::ENABLE_FLAG_TRUE && $relationObj->sku_id == $skuId) {
            return true;
        }

        //== 放开限制，不检查
        //== 【平台产品】多规格产品和无规格产品可混合匹配 https://www.tapd.cn/21404721/s/1764403
        // 当前平台sku所属spu是否关联其他本地产品
        //$relationAPI     = new PlatformProductRelationAPI($this->clientId);
        //$relationProduct = $relationAPI->getMatchedSkus([
        //    'third_sku_id'        => $thirdSku->third_sku_id == 0 ? 0 : new NotEqual(0), // 相同类型
        //    'platform_product_id' => [$thirdSku->platform_product_id],
        //    'platform_sku_id'     => new NotEqual($thirdSku->platform_sku_id),
        //], null, ['sku_id', 'product_id']);
        //
        //
        //if (!empty($relationProduct)) {
        //    $product = $relationProduct[0];
        //
        //    // 只能匹配 已匹配产品下的sku
        //    if ($sku->product_id != $product['product_id']) {
        //        throw new RuntimeException(\Yii::t('platform_product', 'There are skus of the platform product are matched with other local products'));
        //    }
        //}

        // 匹配
        $now = date('Y-m-d H:i:s');

        $relationObj->product_id          = $sku->product_id;
        $relationObj->sku_id              = $sku->sku_id;
        $relationObj->platform_product_id = $thirdSku->platform_product_id;
        $relationObj->platform_sku_id     = $thirdSku->platform_sku_id;
        $relationObj->third_product_id    = $thirdSku->third_product_id;
        $relationObj->third_sku_id        = $thirdSku->third_sku_id;
        $relationObj->platform            = $thirdSku->platform;
        $relationObj->enable_flag         = BaseObject::ENABLE_FLAG_TRUE;
        $relationObj->update_user         = $this->userId;
        $relationObj->update_time         = $now;
        if ($relationObj->isNew()) {
            $relationObj->create_user = $this->userId;
            $relationObj->create_time = $now;
        }
        $relationObj->isNew() ? $relationObj->create() : $relationObj->update();

        $thirdSku->is_match = PlatformProductConstants::MATCHED;
        $thirdSku->update_time = $now;
        $thirdSku->update();

        return true;
    }

    /**
     * 创建本地产品后匹配
     * @param $platformProductId
     * @param array $localSkuItems
     * @return void
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function matchAfterCreateLocal($platformProductId, array $localSkuItems)
    {
        if (empty($localSkuItems)) {
            return;
        }

        $platformSkus = $this->getAttributeInfoOfSkus(['platform_product_id' => $platformProductId]);

        // 无规格产品，直接匹配
        if (count($localSkuItems) == 1 && empty($localSkuItems[0]['attributes_info'])) {
            $platformSku = [];
            foreach ($platformSkus as $item) {
                if (empty($item['attributes_info']))  {
                    $platformSku = $item;
                    break;
                }
            }

            if (!$platformSku) {
                $localSkuItems = json_encode($localSkuItems, JSON_UNESCAPED_UNICODE);
                \LogUtil::info("[matchAfterCreateLocal] platform sku empty, platformProductId: {$platformProductId}, localSkuItems: {$localSkuItems}");
                return;
            }

            $this->matchTo($platformSku['platform_sku_id'], $localSkuItems[0]['sku_id']);
            return;
        }

        foreach ($platformSkus as $platformSku) {
            $attribute = ArrayUtil::keyBy($platformSku['attributes_info'] ?? [], function ($item) {
                return [
                    'key' => strtolower($item['attr_name']),
                    'value' => $item['value_name'],
                ];
            });
            foreach ($localSkuItems as $skuItem) {
                if (count($attribute) != count($skuItem['attributes_info']??[])) {
                    continue;
                }
                $matched = true;
                foreach ($skuItem['attributes_info'] ?? [] as $attr) {
                    $lowerName = strtolower($attr['item_name']);
                    if (!isset($attribute[$lowerName]) || $attribute[$lowerName] != $attr['value']['item_name']) {
                        $matched = false;
                        break;
                    }
                }

                if ($matched) {
                    $this->matchTo($platformSku['platform_sku_id'], $skuItem['sku_id']);
                }
            }
        }

    }

    /**
     * @param array $params
     * @return array
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function getAttributeInfoOfSkus(array $params): array
    {
        $skuCols     = ['platform_sku_id', 'platform_product_id', 'attributes', 'fob_price', 'third_sku_code'];
        $productCols = ['sku_attributes', 'cipher_product_id'];
        $filter      = $this->buildProductSkuFilter($params, $skuCols, $productCols);
        $batch       = $filter->find();
        $batch->getFormatter()
            ->displayFields(['platform_sku_id', 'platform_product_id', 'fob_price', 'third_sku_code']);
        $batch->getFormatter()->displayAttributesInfo(true);
        return $batch->getAttributes();
    }

    /**
     * 释放匹配
     * @param array $platformSkuIds
     * @param array $platformProductIds
     * @return int
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function releaseMatch(array $platformSkuIds = [], array $platformProductIds = [])
    {
        if (!$platformSkuIds && !$platformProductIds) {
            return 0;
        }

        $user = User::getUserObject($this->userId);

        $filter = $this->buildFilter([
            'is_match'        => PlatformProductConstants::MATCHED,
        ]);
        $filter->buildWhereIdsClause($platformProductIds, $platformSkuIds, 'or');
        $batch  = $filter->find();
        $batch->setDomainHandler($user);
        return $batch->getOperator()->releaseMatch();
    }

    /**
     * 平台产品转本地产品
     * @param $platformSkuId
     * @return array
     */
    public function genLocalSkuInfo($platformSkuId, $fillSpuImage = true): array    // $fillSpuImage 平台sku图片为空时是否填充平台spu图片到sku
    {
        $platformSku = new PlatformSku($this->clientId, $platformSkuId);
        if ($platformSku->isNew() || $platformSku->enable_flag != BaseObject::ENABLE_FLAG_TRUE) {
            throw new RuntimeException(\Yii::t('common', 'Data does not exist or deleted'));
        }

        $platformProductApi = new PlatformProductAPI($this->clientId);
        $product            = $platformProductApi->products([
            'platform_product_id' => $platformSku->platform_product_id,
            'enable'              => BaseObject::ENABLE_FLAG_TRUE,
        ], APIConstant::SCENE_GEN_PRODUCT, $fillSpuImage);

        // 去除重复规格名
        foreach($product as &$productItem){
            foreach($productItem['sku_attributes'] ?? [] as $k => $attrItem){
                if(!empty($attrItem['attrs']) && is_array($attrItem['attrs'])){
                    $attrItem['attrs'] = array_unique($attrItem['attrs']);
                    $productItem['sku_attributes'][$k] = $attrItem;
                }
            }
        }

        if (empty($product)) {
            throw new RuntimeException(\Yii::t('common', 'Data does not exist or deleted'));
        }

        [$product] = $product;

        // 校验是否可以生成本地产品
        [$generatable, $error] = $this->isGeneratable($platformSku, $product);
        if (!$generatable) {
            throw new RuntimeException(
                sprintf('%s(%s)',
                    \Yii::t('platform_product', 'The platform product cannot generate local products'),
                    \Yii::t('platform_product', PlatformProductConstants::GEN_ERROR_MSG[$error])
                )
            );
        }

        // sku总数限制 600 个
        if (count($product['all_skus']) > 600) {
            throw new RuntimeException(\Yii::t(
                'platform_product',
                'The platform product has more than 10 specifications, or the specification value exceeds 600, it cannot be generated')
            );
        }

        /*
        $productField = new ProductField($this->clientId);
        $productField->setFormatGroupIds([
            CustomFieldService::PRODUCT_GROUP_BASIC,
            CustomFieldService::PRODUCT_GROUP_PRICE,
            CustomFieldService::PRODUCT_GROUP_PACKAGE,
            CustomFieldService::PRODUCT_GROUP_DESCRIBE,
            CustomFieldService::PRODUCT_GROUP_SKU,
            CustomFieldService::PRODUCT_GROUP_SIZE,           //尺寸信息
            CustomFieldService::PRODUCT_GROUP_CUSTOM,           //报关信息
            CustomFieldService::PRODUCT_GROUP_CARTON,           //报关信息
        ]);
        $formatData = $productField->format($product);
        */

        return [
            'name'            => $product['name'],
            'product_type'    => $platformSku->third_sku_id ? PlatformProductConstants::PRODUCT_TYPE_MULTI_SP : PlatformProductConstants::PRODUCT_TYPE_NO_SP,
            'data'            => $product,
            'platform_sku_id' => $platformSku->platform_sku_id,
            'sku_items'       => $product['all_skus'],
        ];
    }

    /**
     * @param array $params
     * @return array
     * @throws \xiaoman\orm\exception\OrmException
     */
    public function count(array $params)
    {
        /** @var $filter \xiaoman\orm\common\Filter */
        /** @var $productFilter PlatformProductFilter */
        [$filter, $skuFilter, $productFilter] = (new PlatformSkuAPI($this->clientId))->webQueryFilter($params, []);

        $tbl = $productFilter->getTableName();
        $filter->select([
            'product_count' => function () use ($tbl) {
                return "count(distinct {$tbl}.platform_product_id) as product_count";
            },
            'count'         => function () {
                return 'count(1) as count';
            },
        ]);
        $count = $filter->rawData();

        return [
            'count' => $count[0]['count'] ?? 0,
            'product_count' => $count[0]['product_count'] ?? 0
        ];
    }

    /**
     * 判断平台产品是否可以生成本地产品
     * @param PlatformSku $platformSku
     * @param $platformProduct
     * @return array
     */
    public function isGeneratable(PlatformSku $platformSku, $platformProduct): array
    {
        if ($platformSku->enable_flag == BaseObject::ENABLE_FLAG_FALSE) {
            return [false, PlatformProductConstants::GEN_E_TYPE_DELETED];
        }

        if (isset($platformProduct['enable_flag']) && $platformProduct['enable_flag'] == BaseObject::ENABLE_FLAG_FALSE) {
            return [false, PlatformProductConstants::GEN_E_TYPE_DELETED];
        }

        if ($platformSku->is_match != PlatformProductConstants::UNMATCHED) {
            return [false, PlatformProductConstants::GEN_E_TYPE_MATCHED];
        }

        // 多规格产品
        if ($platformSku->third_sku_id != 0 && !empty($platformProduct['spu_match_info']['product_type']) &&
            in_array(PlatformProductConstants::PRODUCT_TYPE_MULTI_SP, $platformProduct['spu_match_info']['product_type'])
        ) {
            return [false, PlatformProductConstants::GEN_E_TYPE_MATCHED];
        }

        return [true, null];
    }

    /**
     * 标记为疑似删除
     * @param $platformSkuIds
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function markThirdDeleted($platformSkuIds)
    {
        $filter = $this->buildFilter(['platform_sku_id' => $platformSkuIds]);
        $batch  = $filter->find();
        $batch->getOperator()->markThirdDeleted();
    }

    /**
     * 为单个信保订单修改平台产品匹配关系
     * @param $orderId
     * @param $productId
     * @param $skuId
     * @param $platformProductId
     * @param $platformSkuId
     * @return bool
     * @throws \Exception
     */
    public function changeMatchForOrder($orderId, $productId, $skuId, $platformProductId, $platformSkuId): bool
    {
        $order       = new \common\library\invoice\Order($this->userId, $orderId);
        $productList = $order->product_list;

        $sku = null;
        if ($skuId) {
            $skuApi = new \common\library\product_v2\sku\SkuAPI($this->clientId);
            $sku = $skuApi->webList(['sku_id' => $skuId]);
            $sku = $sku['list'][0] ?? [];
        }

        $uniqueIds = [];
        $uniqueIdImageMap = [];
        foreach ($productList as &$product) {
            if (isset($product['platform_product_info']['platform_product_id'], $product['platform_product_info']['platform_sku_id']) &&
                $product['platform_product_info']['platform_product_id'] == $platformProductId &&
                $product['platform_product_info']['platform_sku_id'] == $platformSkuId
            ) {
                $imageInfo                 = $sku['image_info'] ?? [];
                $productImages             = [];
                if (!empty($imageInfo['file_id'])) {
                    $productImages = [[
                        'file_id'     => (string)$imageInfo['file_id'],
                        'user_id'     => (string)$this->userId,
                        'create_time' => xm_function_now(),
                    ]];
                }
                $product['sku_id']           = $skuId;
                $product['product_id']       = $productId;
                $product['product_type']     = $sku['product_type'] ?? 0;
                $product['product_name']     = $sku['name'] ?? '';
                $product['product_cn_name']  = $sku['cn_name'] ?? '';
                $product['sku_attributes']   = $sku['attributes'] ?? [];
                $product['product_model']    = $sku['model'] ?? '';
                $product['product_image']    = $imageInfo['file_path'] ?? '';
                $product['product_images']   = $productImages;
                if (!empty($product['unique_id'])) {
                    $uniqueIds[] = $product['unique_id'];
                    $uniqueIdImageMap[$product['unique_id']] = $productImages;
                }
            }
        }
        $order->product_list = $productList;
        $order->getFormatter()->platformProductMatchSetting();
        $orderData = $order->formatInfo();

        $orderData = $this->fillProductToOrder($orderData, array_fill_keys($uniqueIds, $sku));

        return Helper::fillGroupDataToOrder($order, $orderData['data'], $uniqueIdImageMap);
    }


    /**
     * 异步更新草稿状态信保订单里平台产品的匹配关系
     * @param $platformSkuId
     * @param $productId
     * @param $skuId
     */
    public function batchMatchForDraftOrder($platformProductId, $platformSkuId, $productId, $skuId)
    {
        CommandRunner::run('PlatformProduct', 'changeMatchForOrder', [
            'client_id'       => $this->clientId,
            'product_id'      => $productId,
            'sku_id'          => $skuId,
            'platform_sku_id' => $platformSkuId,
            'platform_product_id' => $platformProductId,
            'user_id' => $this->userId,
        ], '/tmp/match_product_for_order.log');
    }

    public function getRelationByPlatformSkuId($platformSkuId)
    {
        if (empty($platformSkuId))
            return [];

        $platformSkuId           = is_array($platformSkuId) ? $platformSkuId : [$platformSkuId];
        $filter                  = new PlatformProductRelationFilter($this->clientId);
        $filter->platform_sku_id = $platformSkuId;
        $filter->enable_flag     = 1;
        $filter->select(['sku_id', 'platform_sku_id', 'product_id', 'platform_product_id']);
        return $filter->find()->getListAttributes(['sku_id', 'platform_sku_id', 'platform_product_id', 'product_id']);
    }


    /**
     * 填充匹配的本地产品信息到订单产品中
     * @param array $order
     * @param array $skus
     * @param array|null $fullRates
     * @return array
     */
    public function fillProductToOrder(array $order, array $skus, array $fullRates = null): array
    {
        $orderData = array_column($order['data'] ?? [], null, 'id');
        if (empty($orderData[CustomFieldService::ORDER_GROUP_PRODUCT])) {
            throw new RuntimeException('订单缺少产品字段');
        }
        $orderProducts = $orderData[CustomFieldService::ORDER_GROUP_PRODUCT];

        $list = ArrayUtil::keyBy($orderProducts['list'] ?? [], function ($item) {
            $item = array_column($item, null, 'id');
            return $item['unique_id']['value'] ?? '';
        });

        // 获取货币汇率信息
        if (!$fullRates) {
            $rateService = new ExchangeRateService();
            $rateService->setMainCurrencyByClientId($this->clientId);
            $fullRates = $rateService->fullRates();
        }
        $ratesMaps = array_column($fullRates, null, 'name');
        $mainCurrency = Client::getClient($this->clientId)->getMainCurrency();

        // 列映射
        $fieldMap = [
            'images'          => 'image_info',
            'product_name'    => 'name',
            'product_cn_name' => 'cn_name',
            'product_image'   => 'image_info',
            'product_model'   => 'model',
        ];

        // 不变更的列
        $excludeFields = [
            'product_image', 'unique_id', 'images', 'count', 'unit', 'platform_product_info',
            'cost_amount', 'sku_attributes', 'unit_price', 'description',
        ];

        foreach ($skus as $productUniqueId => $sku) {
            if (empty($list[$productUniqueId])) {
                throw new RuntimeException("缺少平台产品: {$productUniqueId}");
            }

            if ($sku) {
                $sku['external_field_data'] = array_column($sku['external_field_data'], 'value', 'id');
                $sku = array_replace($sku, $sku['external_field_data']);
                unset($sku['external_field_data']);
            }

            foreach ($list[$productUniqueId] as $k => $field) {
                if ($field['disable_flag']) {
                    continue;
                }

                $realId = $field['id'];

                if (in_array($realId, $excludeFields)) {
                    continue;
                }

                if ($field['field_type'] == CustomFieldService::FIELD_TYPE_FIELDS) {
                    $realId = $field['relation_field'];
                }
                $realId = $fieldMap[$realId] ?? $realId;

                $field['value'] = $sku[$realId] ?? $field['default'];

                // 货币汇率转化
                if ($realId == 'cost_with_tax' && !empty($sku['cost_with_tax'])) {
                    $field['value'] = $sku['cost_with_tax']['cost'] ?? 0;
                    if (
                        !empty($sku['cost_with_tax']['cost']) && !empty($sku['cost_with_tax']['cost_currency']) &&
                        !empty($order['currency']) && !empty($order['exchange_rate']) &&
                        $sku['cost_with_tax']['cost_currency'] != $order['currency']
                    ) {
                        $productCurrency = $sku['cost_with_tax']['cost_currency'];
                        $productRate = 100; // 默认RMB
                        if (!empty($ratesMaps[$productCurrency])) {
                            $rateInfo = $ratesMaps[$productCurrency];
                            if ($rateInfo['custom_rate'] == -1) {
                                $productRate = $productCurrency === 'NTD' ? $rateInfo['m_buy_pri'] : $rateInfo['f_buy_pri'];
                            } else {
                                $productRate = $rateInfo['custom_rate'];
                            }
                        }

                        $toRate = $mainCurrency == 'USD' ? $order['exchange_rate_usd'] : $order['exchange_rate'];
                        $field['value'] = round(bcdiv(bcmul($sku['cost_with_tax']['cost'], $productRate, 4), $toRate, 4), 2);
                    }
                }

                // 引用字段
                if ($field['field_type'] === CustomFieldService::FIELD_TYPE_FIELDS) {
                    $field['value'] = $sku[$field['relation_field']] ?? '';
                    if (str_starts_with($field['relation_field'], 'images')) {
                        [$name, $index] = explode('_', $field['relation_field']);
                        $field['value'] = $sku[$name][$index] ?? '';
                    }
                }

                // 最小起订量
                if ($field['relation_field'] === 'minimum_order_quantity' && !empty($sku['fob'])) {
                    $fobType = $field['fob']['fob_type'] ?? '';
                    $gradientPrice = $field['fob']['gradient_price'] ?? [];
                    $quantity = $field['fob']['quantity'] ?? 0;

                    $field['value'] = $fobType === ProductConstant::FOB_TYPE_GRADIENT ? ($gradientPrice[0]['quantity'] ?? 0) : $quantity;
                }

                $list[$productUniqueId][$k] = $field;
            }
        }

        // 重新计算总数
        $fields = array_column($orderProducts['fields'] ?? [], null, 'id');
        $totalReducers = [
            'package_gross_weight_amount' => [$this, 'handleTotalGrossWeight'],
            'package_volume_amount' => [$this, 'handleTotalVolume'],
        ];
        foreach ($fields as $k => $field) {
            if (empty($totalReducers[$field['id']]) || !is_callable($totalReducers[$field['id']])) {
                continue;
            }
            $reducer = $totalReducers[$field['id']];

            $fields[$k]['value'] = array_reduce($list, $reducer, 0);
        }

        $orderData[CustomFieldService::ORDER_GROUP_PRODUCT]['list'] = array_values($list);
        $orderData[CustomFieldService::ORDER_GROUP_PRODUCT]['fields'] = array_values($fields);

        $order['data'] = array_values($orderData);

        return $order;
    }

    /**
     * 计算总毛重
     * @param $carry
     * @param $item
     * @return float|int|mixed
     */
    private function handleTotalGrossWeight($carry, $item)
    {
        $item = array_column($item, 'value', 'id');
        if (empty($item['count_per_package']) || empty($item['count']) || empty($item['package_gross_weight'])) {
            return $carry;
        }

        return $carry + ceil($item['count']/$item['count_per_package']) * $item['package_gross_weight'];
    }

    /**
     * 计算总体积
     * @param $carry
     * @param $item
     * @return float|int|mixed
     */
    private function handleTotalVolume($carry, $item)
    {
        $item = array_column($item, 'value', 'id');
        if (empty($item['count_per_package']) || empty($item['count']) || empty($item['package_volume'])) {
            return $carry;
        }

        return $carry + ceil($item['count']/$item['count_per_package']) * $item['package_volume'];
    }
}
