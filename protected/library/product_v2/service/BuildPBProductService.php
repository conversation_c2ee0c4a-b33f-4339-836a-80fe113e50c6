<?php
/**
 *
 * Author: ruisenlin
 * Date: 2024/5/15
 */

namespace common\library\product_v2\service;

use common\library\custom_field\CustomFieldFormatter;
use common\library\custom_field\FieldList;
use common\library\oms\inventory\product_inventory\ProductInventoryApi;
use common\library\oms\invoice_export\InvoiceExportFieldFormatter;
use common\library\oms\page_layout\PageLayoutApi;
use common\library\oms\page_layout\PageLayoutConstant;
use common\library\oms\warehouse\WarehouseFilter;
use common\library\privilege_v3\Helper as PrivilegeHelper;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\product_v2\ProductConstant;
use common\modules\storms_fury\services\BuildPBFieldService;
use protobuf\Product\PBProductAttribute;
use protobuf\Product\PBProductBaseInfo;
use protobuf\Product\PBProductFob;
use protobuf\Product\PBProductFobType;
use protobuf\Product\PBProductInventory;
use protobuf\Product\PBProductProfileField;
use protobuf\Product\PBProductSkuBaseInfo;
use protobuf\Product\PBProductSkuHeaderInfo;
use protobuf\Product\PBProductSkuSpecificationListRsp;
use protobuf\Product\PBProductSpecificationListRsp;
use protobuf\Product\PBProductType;
use protobuf\Product\PBSimpleTableCell;
use protobuf\Product\PBSimpleTableField;
use protobuf\Product\PBSimpleTableRow;
use protobuf\Product\PBSingleTextField;
use protobuf\Product\PBSkuTreeAttribute;
use protobuf\Product\PBSpecificationProductInfo;
use protobuf\Product\PBSpecificationProductSkuInfo;
use protobuf\Product\PBSubProductInfo;
use protobuf\Product\PBSubProductListRsp;
use User;

class BuildPBProductService extends BuildPBFieldService
{
    protected $clientId;

    protected $baseData;

    protected $opUserId;

    const FIELD_SETTING = [
        'profile' => [
            'ignore' => [
                'combine_info'
            ],
            'header' => [
                'name',
                'cn_name',
                'images',
                'fob',
            ],
            'header_external' => [
                'inventory_info',
                'parts_list_count',
                'sku_items',
                'product_type',
                'disable_flag',
                'product_id',
            ],
            'special' => [
                'create_user',
                'update_user',
                'sku_attributes',
                'is_parts',
            ],
        ],
        'sku_info' => [
            'ignore' => [
                'combine_info'
            ],
            'header' => [
                'name',
                'cn_name',
                'images',
                'fob',
            ],
            'header_external' => [
                'inventory_info',
                'parts_list_count',
                'sku_items',
                'product_type',
                'disable_flag',
                'product_id',
                'attribute_name',
                'sku_id',
                'product_no',
                'product_name',
                'fob',
                'images'
            ],
            'special' => [
                'create_user',
                'update_user',
                'sku_attributes',
                'is_parts',
            ],
            'sku_field_map' => [
                'product_no' => 'sku_code',
                'description' => 'sku_description',
                'product_remark' => 'sku_remark',
            ]
        ],
        'sub' => [
            'base' => [
                'name',
                'cn_name',
                'product_no',
                'fob',
                'image_info',
                'count',
                'unit',
                'attributes_info',
            ],
        ],
        'specification' => [
              'base' => [
                  'sku_code',
                  'fob',
                  'image_info',
                  'cost_with_tax',
                  'inventory_info',
                  'parts_list_count'
              ],
        ],
    ];

    /**
     * @param $clientId
     */
    public function __construct($clientId)
    {
        parent::__construct('product');
        $this->clientId = $clientId;

        $opUser = \User::getLoginUser();

        if ($opUser)
        {
            $this->opUserId = $opUser->getUserId();
        }
    }

    /**
     * @param mixed $baseData
     */
    public function setBaseData($baseData): void
    {
        $this->baseData = $baseData;
    }

    public function buildProfile($data)
    {
        $rsp = new \protobuf\Product\PBProductProfileInfoRsp();

        if (empty($data))
            return $rsp;

        $groups = [];
        $headerInfo = new \protobuf\Product\PBProductHeaderInfo();
        foreach ($data as $groupField) {
            $group = new \protobuf\Product\PBProductProfileGroup();
            $group->setGroupName($groupField['name']);
            $fields = [];
            foreach ($groupField['fields'] as $field) {

                if ($field['disable_flag'] || in_array($field['id'], self::FIELD_SETTING['profile']['ignore']))
                    continue;

                if (in_array($field['id'], self::FIELD_SETTING['profile']['special']))
                {
                    $fields[] = match ($field['id']) {
                        'create_user', 'update_user' => $this->buildUserInfoField($field['name'], $field['format'] ),
                        'is_parts' => $this->buildIsPartsField($field['name'], $field['value'] ?? ''),
                        default => new PBProductProfileField(),
                    };
                    continue;
                }
                if (in_array($field['id'], self::FIELD_SETTING['profile']['header'])) {
                    $this->buildHeaderInfo($headerInfo, $field['field_type'], $field['id'], $field['value'] ?? '', $field['format'] ?? '');
                    if ($field['id'] != 'fob')
                    {
                        continue;
                    }
                }
                $field = $this->formatField($field['field_type'], $field['id'], $field['name'], $field['value'] ?? '', $field['format'] ?? '');
                if (is_array($field))
                {
                    $fields = array_merge($fields, $field);
                } else {
                    $fields[] = $field;
                }
            }
            $group->setItems($fields);
            $groups[$groupField['name']] = $group;
        }

        foreach (self::FIELD_SETTING['profile']['header_external'] as $key) {
            match ($key)
            {
                'inventory_info' => $this->buildHeaderInfo($headerInfo, '', $key, '', $this->buildInventoryInfo()),
                'parts_list_count' => $this->buildHeaderInfo($headerInfo, '', $key, $this->baseData['parts_list_count'] ?? 0, $this->baseData['parts_list_count'] ?? 0),
                'sku_items' => $this->buildHeaderInfo($headerInfo, '', $key, $this->baseData['sku_items'] ?? [], $this->baseData['sku_items'] ?? []),
                'product_type' => $this->buildHeaderInfo($headerInfo, '',  $key, $this->baseData['product_type'] ?? 0, ''),
                'disable_flag' => $this->buildHeaderInfo($headerInfo, '',  $key, $this->baseData['disable_flag'] ?? 0, ''),
                'product_id' => $this->buildHeaderInfo($headerInfo, '',  $key, $this->baseData['product_id'] ?? 0, ''),
            };
        }

        if (($this->baseData['product_type'] ?? 0) == ProductConstant::PRODUCT_TYPE_SKU)
        {
            unset($groups[\Yii::t('field', '外箱信息')]);
            unset($groups[\Yii::t('field', '包装信息')]);
            unset($groups[\Yii::t('field', '尺寸信息')]);
            unset($groups[\Yii::t('field', '价格信息')]);
        }

        if (($this->baseData['product_type'] ?? 0) == ProductConstant::PRODUCT_TYPE_COMBINE || !PrivilegeHelper::checkHasOmsPrivilege($this->clientId))
        {
            unset($groups[\Yii::t('field', '产品特性')]);
        }

        $rsp->setGroups($groups);
        $rsp->setHeader($headerInfo);

        return $rsp;
    }


    public function buildSkuInfo($data)
    {
        $rsp = new \protobuf\Product\PBProductSkuInfoRsp();

        if (empty($data))
            return $rsp;

        $groups = [];
        $headerInfo = new PBProductSkuHeaderInfo();
        foreach ($data as $groupField) {
            $group = new \protobuf\Product\PBProductProfileGroup();
            $group->setGroupName($groupField['name']);
            $fields = [];
            foreach ($groupField['fields'] as $field) {

                if ($field['disable_flag'] || in_array($field['id'], self::FIELD_SETTING['sku_info']['ignore']))
                    continue;

                if (in_array($field['id'], self::FIELD_SETTING['sku_info']['special']))
                {
                    $fields[] = match ($field['id']) {
                        'create_user', 'update_user' => $this->buildUserInfoField($field['name'], $field['format'] ),
                        'is_parts' => $this->buildIsPartsField($field['name'], $field['value'] ?? ''),
                        default => new PBProductProfileField(),
                    };
                    continue;
                }
                if (in_array($field['id'], self::FIELD_SETTING['sku_info']['header'])) {
                    $this->buildSkuHeaderInfo($headerInfo, $field['field_type'], $field['id'], $field['value'] ?? '', $field['format'] ?? '');
                    if ($field['id'] != 'fob')
                    {
                        continue;
                    }
                }
                $field = $this->formatField($field['field_type'], $field['id'], $field['name'], $field['value'] ?? '', $field['format'] ?? '');
                if (is_array($field))
                {
                    $fields = array_merge($fields, $field);
                } else {
                    $fields[] = $field;
                }
            }
            $group->setItems($fields);
            $groups[$groupField['name']] = $group;
        }
        foreach (self::FIELD_SETTING['sku_info']['header_external'] as $key) {
            match ($key)
            {
                'inventory_info' => $this->buildSkuHeaderInfo($headerInfo, '', $key, $this->baseData['sku_id'] ?? 0,''),
                'images' => $this->buildSkuHeaderInfo($headerInfo, '', $key, '', $this->baseData['images'] ?? []),
                'parts_list_count' => $this->buildSkuHeaderInfo($headerInfo, '', $key, $this->baseData['parts_list_count'] ?? 0, $this->baseData['parts_list_count'] ?? 0),
                'sku_items' => $this->buildSkuHeaderInfo($headerInfo, '', $key, $this->baseData['sku_items'] ?? [], $this->baseData['sku_items'] ?? []),
                'product_type' => $this->buildSkuHeaderInfo($headerInfo, '',  $key, $this->baseData['product_type'] ?? 0, ''),
                'disable_flag' => $this->buildSkuHeaderInfo($headerInfo, '',  $key, $this->baseData['disable_flag'] ?? 0, ''),
                'product_id' => $this->buildSkuHeaderInfo($headerInfo, '',  $key, $this->baseData['product_id'] ?? 0, ''),
                'sku_id' => $this->buildSkuHeaderInfo($headerInfo, '',  $key, $this->baseData['sku_id'] ?? 0, ''),
                'attribute_name' => $this->buildSkuHeaderInfo($headerInfo, '',  $key, $this->baseData['attributes_info'] ?? '', ''),
                'product_no' => $this->buildSkuHeaderInfo($headerInfo, '',  $key, $this->baseData['product_no'] ?? '', ''),
                'product_name' => $this->buildSkuHeaderInfo($headerInfo, '',  $key, $this->baseData['product_name'] ?? '', ''),
                'fob' => $this->buildSkuHeaderInfo($headerInfo, '',  $key, '', $this->baseData['fob'] ?? []),
            };
        }

//        if (($this->baseData['product_type'] ?? 0) == ProductConstant::PRODUCT_TYPE_SKU)
//        {
//            unset($groups[\Yii::t('field', '外箱信息')]);
//            unset($groups[\Yii::t('field', '包装信息')]);
//            unset($groups[\Yii::t('field', '尺寸信息')]);
//            unset($groups[\Yii::t('field', '价格信息')]);
//        }

        if (($this->baseData['product_type'] ?? 0) == ProductConstant::PRODUCT_TYPE_COMBINE || !PrivilegeHelper::checkHasOmsPrivilege($this->clientId))
        {
            unset($groups[\Yii::t('field', '产品特性')]);
        }

        $rsp->setGroups($groups);
        $rsp->setHeader($headerInfo);

        return $rsp;
    }




    protected function getFieldList($includeField = [], $groups = [])
    {
        $fieldListObj = new FieldList($this->clientId);
        $fieldListObj->setType(\Constants::TYPE_PRODUCT);
        $fieldListObj->setGroupId($groups);
//        $fieldListObj->setDisableFlag(true);
        $fieldListObj->setNeedList(true);
        $fieldListObj->setPrivilegeInfo($this->opUserId ?? 0);
        $fieldListObj->setFormatterType(CustomFieldFormatter::TYPE_GROUP_ID);
        $fields = $fieldListObj->find();

        // 过滤隐藏字段
        foreach ($fields as $field) {
            foreach ($includeField as $index => $item) {
                if (!empty($field[$item]) && $field[$item]['disable_flag'])
                    unset($includeField[$index]);
            }
        }
        return $includeField;
    }

    public function buildSubProductList($data, $totalCount)
    {
        $rsp = new PBSubProductListRsp();
        if (empty($data))
            return $rsp;

        $infos = [];
        $includeField = $this->getFieldList(self::FIELD_SETTING['sub']['base'], [\common\library\custom_field\CustomFieldService::PRODUCT_GROUP_BASIC, \common\library\custom_field\CustomFieldService::PRODUCT_GROUP_PRICE]);

        foreach ($data as $datum) {
            $infos[] = $this->buildSubProductInfo($datum, $includeField);
        }

        $rsp->setSubProductList($infos);
        $rsp->setTotalCount($totalCount);
        return $rsp;
    }

    public function buildSpecificationList($attributes, $data, $totalCount)
    {
        $rsp = new PBProductSpecificationListRsp();
        if (empty($data))
            return $rsp;

        $infos = [];
        $includeField = $this->getFieldList(self::FIELD_SETTING['specification']['base'], [\common\library\custom_field\CustomFieldService::PRODUCT_GROUP_BASIC, \common\library\custom_field\CustomFieldService::PRODUCT_GROUP_PRICE]);

        foreach ($data as $datum) {
            $infos[] = $this->buildSpecificationInfo($datum, $includeField);
        }

        $rsp->setProductList($infos);
        $rsp->setAttributeNodes($this->buildTree($attributes));
        $rsp->setTotalCount($totalCount);
        return $rsp;
    }

    public function buildSkuSpecificationList($attributes, $data, $totalCount)
    {
        $rsp = new PBProductSkuSpecificationListRsp();
        if (empty($data))
            return $rsp;

        $infos = [];
        $includeField = $this->getFieldList(self::FIELD_SETTING['specification']['base'], [\common\library\custom_field\CustomFieldService::PRODUCT_GROUP_BASIC, \common\library\custom_field\CustomFieldService::PRODUCT_GROUP_PRICE]);

        foreach ($data as $datum) {
            $infos[] = $this->buildSkuSpecificationInfo($datum, $includeField);
        }

        $rsp->setProductList($infos);
        $rsp->setAttributeNodes($this->buildTree($attributes));
        $rsp->setTotalCount($totalCount);
        return $rsp;
    }

    protected function buildSkuSpecificationInfo($data, $attrList)
    {
        $info = new PBSpecificationProductSkuInfo();
        $baseInfo = new PBProductSkuBaseInfo();
        $baseInfo->setProductId($data['product_id']);
        $baseInfo->setTitle($this->buildAttributeTitle($data['attributes_info']));

        $attrs = [];
        foreach ($attrList as $item) {
            $attr = new PBProductAttribute();
            $baseInfo->setSkuId($data['sku_id'] ?? 0);
            switch ($item)
            {
                case 'fob' :
                    $baseInfo->setFob($this->buildFob($data['fob'] ?? []));
                    break;
                case 'image_info':
                    $attr->setKey('image');
                    $attr->setStringValue($data['image_info']['file_preview_url'] ?? '');
                    $attr->setFormat($data['image_info']['file_preview_url'] ?? '');
                    $attrs[] = $attr;
                    break;
                case 'sku_code':
                    $attr->setName(\Yii::t('field', '产品编号'));
                    $attr->setStringValue($data['sku_code']);
                    $attr->setFormat($data['sku_code']);
                    $attrs[] = $attr;
                    break;
                case 'inventory_info':
                    $this->buildHeaderInventory( $baseInfo, $this->buildInventoryInfo($data['sku_id'], ProductConstant::PRODUCT_TYPE_SPU));
                    break;
                case 'cost_with_tax':
                    $attr->setName(\Yii::t('field', '含税成本价'));
                    $attr->setFormat(InvoiceExportFieldFormatter::formatProductCostWithTaxString($data['cost_with_tax']));
                    $attr->setDoubleValue($data['cost_with_tax']['cost'] ?? 0);
                    $attrs[] = $attr;
                    break;
                case 'parts_list_count':
                    $this->buildProductPartsTags($baseInfo, $data['parts_list_count'] ?? 0);
                    break;
            }
        }
        $baseInfo->setAttributes($attrs);

        $info->setBaseInfo($baseInfo);
        $info->setDisableFlag($data['disable_flag']);
        return $info;
    }

    protected function buildSpecificationInfo($data, $attrList)
    {
        $info = new PBSpecificationProductInfo();
        $baseInfo = new PBProductBaseInfo();
        $baseInfo->setProductId($data['product_id']);
        $baseInfo->setTitle($this->buildAttributeTitle($data['attributes_info']));

        $attrs = [];
        foreach ($attrList as $item) {
            $attr = new PBProductAttribute();
            switch ($item)
            {
                case 'fob' :
                    $baseInfo->setFob($this->buildFob($data['fob'] ?? []));
                    break;
                case 'image_info':
                    $attr->setKey('image');
                    $attr->setStringValue($data['image_info']['file_preview_url'] ?? '');
                    $attr->setFormat($data['image_info']['file_preview_url'] ?? '');
                    $attrs[] = $attr;
                    break;
                case 'sku_code':
                    $attr->setName(\Yii::t('field', '产品编号'));
                    $attr->setStringValue($data['sku_code']);
                    $attr->setFormat($data['sku_code']);
                    $attrs[] = $attr;
                    break;
                case 'inventory_info':
                    $this->buildHeaderInventory( $baseInfo, $this->buildInventoryInfo($data['sku_id'], ProductConstant::PRODUCT_TYPE_SPU));
                    break;
                case 'cost_with_tax':
                    $attr->setName(\Yii::t('field', '含税成本价'));
                    $attr->setFormat(InvoiceExportFieldFormatter::formatProductCostWithTaxString($data['cost_with_tax']));
                    $attr->setDoubleValue($data['cost_with_tax']['cost'] ?? 0);
                    $attrs[] = $attr;
                    break;
                case 'parts_list_count':
                    $this->buildProductPartsTags($baseInfo, $data['parts_list_count'] ?? 0);
                    break;
            }
        }
        $baseInfo->setAttributes($attrs);
        $info->setBaseInfo($baseInfo);
        $info->setDisableFlag($data['disable_flag']);
        return $info;
    }

    protected function buildAttributeTitle($attributesInfo)
    {
        $attributes = [];
        foreach ($attributesInfo as $attribute) {
            $attributes[] = $attribute['value']['item_name'] ?? '';
        }
        return implode(',', array_filter($attributes));
    }

    public function buildTree(array $items)
    {
        $trees = [];
        foreach ($items as $item) {
            $tree = new \protobuf\Product\PBSkuTreeAttribute();
            if(!empty($item))
            {
                $fieldItem = new \protobuf\CRMCommon\PBFieldItem();
                $fieldItem->setId($item['item_id']);
                $fieldItem->setName($item['item_name']);
                $tree->setNode($fieldItem);
                if (!empty($item['value']))
                {
                    $tree->setChild($this->buildTree($item['value']));
                }
            }
            $trees[] = $tree;
        }
        return $trees;
    }

    protected function buildSubProductInfo($data, $attrList)
    {
        $info = new PBSubProductInfo();
        $baseInfo = new PBProductBaseInfo();
        $baseInfo->setProductId($data['product_id']);
        $this->buildProductTypeTags($baseInfo, $data['product_type']);
        $attrs = [];
        foreach ($attrList as $item) {
            $attr = new PBProductAttribute();
            switch ($item)
            {
                case 'name' :
                    $baseInfo->setTitle($data['name']);
                    break;
                case 'fob' :
                    $baseInfo->setFob($this->buildFob($data['fob'] ?? []));
                    break;
                case 'cn_name':
                    $attr->setName(\Yii::t('field', '中文产品名称'));
                    $attr->setStringValue($data['cn_name']);
                    $attr->setFormat($data['cn_name']);
                    $attrs[] = $attr;
                    break;
                case 'product_no':
                    $attr->setName(\Yii::t('field', '产品编号'));
                    $attr->setStringValue($data['product_no']);
                    $attr->setFormat($data['product_no']);
                    $attrs[] = $attr;
                    break;
                case 'image_info':
                    $attr->setKey('image');
                    $attr->setStringValue($data['image_info']['file_preview_url'] ?? '');
                    $attr->setFormat($data['image_info']['file_preview_url'] ?? '');
                    $attrs[] = $attr;
                    break;
                case 'count' :
                    $info->setCount($data['count']);
                    break;
                case 'unit' :
                    $info->setUnit($data['unit']);
                    break;
                case 'attributes_info':
                    if (empty($data['attributes_info']) || empty($attributesInfo = $this->buildAttributesInfoField($data['attributes_info'])))
                        break;
                    $attr->setKey('attributes_info');
                    $attr->setName('规格组合');
                    $attr->setFormat($attributesInfo);
                    $attrs[] = $attr;
                    break;
            }
        }
        $baseInfo->setAttributes($attrs);
        $info->setBaseInfo($baseInfo);
        return $info;
    }

    protected function buildAttributesInfoField($attributesInfo)
    {
        $attrs = [];
        foreach ($attributesInfo as $item) {
            $attrs[] = $item['value']['item_name'] ?? '';
        }

        return implode(',', $attrs);
    }

    protected function buildInventoryInfo($skuId = 0, $productType = 0)
    {
        $skuId = $skuId ?: $this->baseData['sku_id'] ?? 0;
        $productType = $productType ?: $this->baseData['product_type'] ?? 0;
        if ($productType == ProductConstant::PRODUCT_TYPE_SPU)
        {
            // 库存查询简化
            $inventoryFilter = new \common\library\oms\inventory\product_inventory\ProductInventoryFilter($this->clientId);
            $warehouseFilter = new WarehouseFilter($this->clientId);
            $warehouseFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
            $warehouseFilter->select(['name']);
            $inventoryFilter->select(['product_id', 'warehouse_id', 'enable_count', 'real_count']);
            $joinFilter = $inventoryFilter->initJoin();
            $joinFilter->innerJoin($warehouseFilter)->on('warehouse_id', 'warehouse_id');
            $inventoryFilter->sku_id = $skuId;
            $batch = $joinFilter->find();
            $batch->getFormatter()->displayFloatVal(true);
            $batch->getFormatter()->displayFields(['product_id','warehouse_id', 'enable_count', 'real_count']);
            $result = $batch->getAttributes();
            return [
                'real_count' => array_sum(array_column($result, 'real_count')),
                'enable_count' => array_sum(array_column($result, 'enable_count')),
            ];
        } else if ($productType == ProductConstant::PRODUCT_TYPE_COMBINE)
        {
            $inventoryApi = new ProductInventoryApi();
            $result = $inventoryApi->skuTotalInventory([$skuId]);
            $realCount = [];
            $enableCount = [];
            foreach ($result as $item) {
                $realCount[] = $item['real_count'] / $item['combine_count'];
                $enableCount[] = $item['enable_count'] / $item['combine_count'];
            }

            return [
                'real_count' => min($realCount),
                'enable_count' => min($enableCount),
            ];
        }
    }

    protected function buildIsPartsField($name, $value)
    {
        $field = new PBProductProfileField();
        $singleText = new PBSingleTextField();
        $singleText->setName($name);
        $singleText->setValue($value ? '是' : '否');
        $field->setSingleText($singleText);
        return $field;
    }

    protected function buildAttrField($name, $value)
    {
        $filed = new PBProductProfileField();
        if(empty($value) || empty($value[0]['name']))
        {
            $singleText = new PBSingleTextField();
            $singleText->setName($name);
            $singleText->setIsEmpty(true);
            $filed->setSingleText($singleText);
            return $filed;
        }

        $table = new PBSimpleTableField();
        $table->setName($name);
        $rows = [];

        $cells = [];
        $row = new PBSimpleTableRow();
        $attrName = new PBSimpleTableCell();
        $attrName->setText('属性名称');
        $cells[] = $attrName;
        $attrValue = new PBSimpleTableCell();
        $attrValue->setText('属性值');
        $cells[] = $attrValue;
        $row->setCells($cells);
        $rows[] = $row;

        foreach ($value as $item) {
            $cells = [];
            $row = new PBSimpleTableRow();
            $key = new PBSimpleTableCell();
            $key->setText($item['name']);
            $cells[] = $key;
            $value = new PBSimpleTableCell();
            $value->setText($item['value']);
            $cells[] = $value;
            $row->setCells($cells);
            $rows[] = $row;
        }
        $table->setRows($rows);

        $filed->setTable($table);
        return $filed;
    }

    protected function buildUserInfoField($name, $value)
    {
        $field = new PBProductProfileField();
        $singleText = new PBSingleTextField();
        $singleText->setName($name);
        $singleText->setValue($value['nickname'] ?? '');
        $singleText->setIsEmpty(empty($value['nickname']));
        $field->setSingleText($singleText);
        return $field;
    }

    protected function buildHeaderInfo(\protobuf\Product\PBProductHeaderInfo &$headerInfo, $type, $id, $value, $format)
    {
        match ($id) {
            'name' => $headerInfo->setTitle($format),
            'cn_name' => $headerInfo->setCnName($format),
            'images' => $headerInfo->setPictures($this->formatFileList($format)),
            'product_type' => $this->buildProductType($headerInfo, $value),
            'fob' => $this->buildHeaderFob($headerInfo, $format),
            'inventory_info' => $this->buildHeaderInventory($headerInfo, $format),
            'parts_list_count' => $this->buildProductPartsTags($headerInfo, $format),
            'sku_items' => $this->buildSkuItemsFob($headerInfo, $value),
            'disable_flag' => $headerInfo->setDisableFlag($value),
            'product_id' => $headerInfo->setProductId($value),
            default => null,
        };
    }

    protected function buildSkuHeaderInfo(PBProductSkuHeaderInfo &$headerInfo, $type, $id, $value, $format)
    {
        match ($id) {
            'name' => $headerInfo->setTitle($format),
            'cn_name' => $headerInfo->setCnName($format),
            'images' => $headerInfo->setPictures($this->formatFileList($format)),
            'product_type' => $this->buildProductType($headerInfo, $value),
            'fob' => $this->buildHeaderFob($headerInfo, $format),
            'inventory_info' => $this->buildSkuHeaderInventory($headerInfo, $value),
            'parts_list_count' => $this->buildProductPartsTags($headerInfo, $format),
            'sku_items' => $this->buildSkuItemsFob($headerInfo, $value),
            'disable_flag' => $headerInfo->setDisableFlag($value),
            'product_id' => $headerInfo->setProductId($value),
            'sku_id' => $headerInfo->setSkuId($value),
            'product_no' => $headerInfo->setProductNo($value),
            'product_name' => $headerInfo->setName($value),
            default => null,
        };
    }

    public function buildSkuItemsFob($headerInfo, $value)
    {
        if (empty($value[0]))
            return ;
        $value = $value[0]['fob'] ?? [];
        $headerInfo->setFob($this->buildFob($value));
        $this->buildHeaderFob($headerInfo, $value);
    }


    protected function buildSkuHeaderInventory(\protobuf\Product\PBProductHeaderInfo|PBProductBaseInfo|PBProductSkuHeaderInfo|PBProductSkuBaseInfo &$headerInfo, $value)
    {

        $inventory = new PBProductInventory();
        // 没有库存模块不返回库存
        if (!PrivilegeService::getInstance($this->clientId)->hasFunctional(PrivilegeConstants::FUNCTIONAL_INVENTORY_QUERY) || empty($value)) {
            $inventory->setIsEmpty(true);
            $headerInfo->setInventory($inventory);
            return;
        }
        $user = User::getLoginUser();
        $userId = $user->getUserId();

        $api = new \common\library\product_v2\ProductAPI($this->clientId, $userId);
        $inventoryData = $api->getProductInventoryBySku($value);

        $inventory->setActual(array_sum(array_column($inventoryData,'real_count')));
        $inventory->setAvailable(array_sum(array_column($inventoryData,'enable_count')));
        $headerInfo->setInventory($inventory);
    }

    protected function buildHeaderInventory(\protobuf\Product\PBProductHeaderInfo|PBProductBaseInfo|PBProductSkuHeaderInfo|PBProductSkuBaseInfo &$headerInfo, $value)
    {
        $inventory = new PBProductInventory();
        // 没有库存模块不返回库存
        if (!PrivilegeService::getInstance($this->clientId)->hasFunctional(PrivilegeConstants::FUNCTIONAL_INVENTORY_QUERY) || empty($value))
        {
            $inventory->setIsEmpty(true);
            $headerInfo->setInventory($inventory);
            return ;
        }
        $inventory->setActual($value['real_count']);
        $inventory->setAvailable($value['enable_count']);
        $headerInfo->setInventory($inventory);
    }

    protected function buildHeaderFob(\protobuf\Product\PBProductHeaderInfo|PBProductSkuHeaderInfo &$header, $value)
    {
        $header->setFob($this->buildFob($value));
    }

    protected function buildFob($value)
    {
        $fob = new PBProductFob();
        if (empty($value))
        {
            $fob->setIsEmpty(true);
            return new $fob;
        }

        $fob = new PBProductFob();
        switch ($value['fob_type'])
        {
            case ProductConstant::FOB_TYPE_PRICE:
                $max = $value['price_max'];
                $min = $value['price_min'];
                if ($this->isBlackEmpty($max) || $this->isBlackEmpty($min))
                {
                    $fob->setIsEmpty(true);
                    break;
                }
                $fob->setPriceCurrency($value['price_currency']);
                $fob->setDoublePriceMax($max);
                $fob->setDoublePriceMin($min);
                $fob->setFormat($min . '~' . $max);
                $fob->setType(PBProductFobType::FOB_TYPE_PRICE);
                break;
            case ProductConstant::FOB_TYPE_GRADIENT:
                $min = last($value['gradient_price'] ?: [])['price'] ?? null;
                $max = current($value['gradient_price'] ?: [])['price'] ?? null;
                if ($this->isBlackEmpty($min) || $this->isBlackEmpty($max))
                {
                    $fob->setIsEmpty(true);
                    break;
                }
                $fob->setPriceCurrency($value['price_currency']);
                $fob->setDoublePriceMax($max);
                $fob->setDoublePriceMin($min);
                $fob->setFormat(  $min .' ~ '. $max);
                $fob->setType(PBProductFobType::FOB_TYPE_GRADIENT);
                break;
            case ProductConstant::FOB_TYPE_SKU:
                $min = $max = $value['fob_price'];
                if ($this->isBlackEmpty($min))
                {
                    $fob->setIsEmpty(true);
                    break;
                }
                $fob->setPriceCurrency($value['price_currency']);
                $fob->setDoublePriceMax($max);
                $fob->setDoublePriceMin($min);
                $fob->setFormat($max);
                $fob->setType(PBProductFobType::FOB_TYPE_SKU);
                break;
        }
        return $fob;
    }

    protected function isBlackEmpty($data)
    {
        return $data == null || $data == '';
    }

    protected function buildProductType(\protobuf\Product\PBProductHeaderInfo|PBProductSkuHeaderInfo &$headerInfo, $value)
    {
        $headerInfo->setProductType(match ($value) {
            ProductConstant::PRODUCT_TYPE_SPU => PBProductType::PRODUCT_TYPE_SPU,
            ProductConstant::PRODUCT_TYPE_SKU => PBProductType::PRODUCT_TYPE_SKU,
            ProductConstant::PRODUCT_TYPE_COMBINE => PBProductType::PRODUCT_TYPE_COMBINE,
        });

        $this->buildProductTypeTags($headerInfo, $value);
    }

    protected function buildProductTypeTags(\protobuf\Product\PBProductHeaderInfo|PBProductBaseInfo|PBProductSkuHeaderInfo &$headerInfo, $value)
    {
        // 无规格 不需要展示标签
        if ($value == ProductConstant::PRODUCT_TYPE_SPU)
            return ;

        $tags = $headerInfo->getTags();
        $tag = new \protobuf\Product\PBProductTag();
        $tag->setName(InvoiceExportFieldFormatter::formatSpecificationString($value));
        $tags[] = $tag;
        $headerInfo->setTags($tags);
    }

    protected function buildProductPartsTags(\protobuf\Product\PBProductHeaderInfo|PBProductBaseInfo|PBProductSkuHeaderInfo|PBProductSkuBaseInfo &$headerInfo, $value)
    {
        if (empty($value))
            return ;

        $tags = $headerInfo->getTags();
        $tag = new \protobuf\Product\PBProductTag();
        $tag->setName('有配件');
        $tags[] = $tag;
        $headerInfo->setTags($tags);
    }

    public function formatField($type, $id, $name, $value, $format)
    {
        return match(intval($type))
        {
            \common\library\custom_field\CustomFieldService::FIELD_TYPE_OTHER => $this->formatSpecialField($id, $name, $format),
            default => parent::formatField($type, $id, $name, $value, $format),
        };
    }

    protected function formatSpecialField($id, $name, $value)
    {
        return match($id) {
            'fob' => $this->buildFobField($name, $value),
            'info_json' => $this->buildAttrField($name, $value),
            'product_size','package_size','carton_size' => $this->buildSizeField($id, $name, $value),
            'cost_with_tax' => $this->buildCostWithTaxField($name, $value),
            'category_ids' => $this->buildCategoryIdsField($name, $value),
            default => new PBProductProfileField(),
        };
    }

    protected function buildCategoryIdsField($name, $value)
    {
        $filed = new PBProductProfileField();
        $singleText = new PBSingleTextField();
        $singleText->setName($name);
        if (empty($value))
        {
            $singleText->setIsEmpty(true);
            $filed->setSingleText($singleText);
            return $filed ;
        }
        $singleText->setValue(InvoiceExportFieldFormatter::formatProductCategoryString($value));
        $filed->setSingleText($singleText);
        return $filed;

    }

    protected function buildCostWithTaxField($name, $value)
    {
        $filed = new PBProductProfileField();
        $singleText = new PBSingleTextField();
        $singleText->setName($name);
        if (empty($value))
        {
            $singleText->setIsEmpty(true);
            return $filed ;
        }
        $formatValue = InvoiceExportFieldFormatter::formatProductCostWithTaxString($value);
        $singleText->setValue($formatValue);
        $singleText->setIsEmpty(empty($formatValue));
        $filed->setSingleText($singleText);
        return $filed;
    }

    protected function buildSizeField($id, $name, $value)
    {
        $filed = new PBProductProfileField();
        $singleText = new PBSingleTextField();
        $singleText->setName($name);
        if (empty($value) || empty(trim(implode($value))))
        {
            $singleText->setIsEmpty(true);
            $filed->setSingleText($singleText);
            return $filed ;
        }
        $value = match($id) {
          'package_size' => InvoiceExportFieldFormatter::formatPackageSizeField($value),
          'product_size' => InvoiceExportFieldFormatter::formatProductSizeField($value),
          'carton_size' => InvoiceExportFieldFormatter::formatCartonSizeField($value),
          default => '0*0*0',
        };
        $singleText->setValue($value);
        $filed->setSingleText($singleText);
        return $filed;
    }

    protected function buildFobField($name, $value)
    {
        $field = new PBProductProfileField();

        $fields = [];
        $externalFields = [];

        if (empty($value))
        {
            $singleText = new PBSingleTextField();
            $singleText->setName($name);
            $singleText->setIsEmpty(true);
            $field->setSingleText($singleText);

            $externalField = new PBProductProfileField();
            $singleText = new PBSingleTextField();
            $singleText->setName('最小起订量');
            $singleText->setIsEmpty(true);
            $externalField->setSingleText($singleText);
            $externalFields[] = $externalField;

            array_push($fields, $field, ...$externalFields);

            return $fields;
        }

        switch (intval($value['fob_type']))
        {
            case ProductConstant::FOB_TYPE_PRICE:
                $singleText = new PBSingleTextField();
                $fob = $value['price_currency'] . ' ' . $value['price_min'] . '~' . $value['price_max'];
                $singleText->setName($name);
                $singleText->setIsEmpty(empty($value['price_min']) || empty($value['price_max']));
                $singleText->setValue($fob);
                $field->setSingleText($singleText);

                $externalField = new PBProductProfileField();
                $singleText = new PBSingleTextField();
                $quantity = $value['quantity'];
                $singleText->setName('最小起订量');
                $singleText->setValue($quantity);
                $singleText->setIsEmpty(empty($quantity));
                $externalField->setSingleText($singleText);
                $externalFields[] = $externalField;
                break;
            case ProductConstant::FOB_TYPE_GRADIENT:
                $table = new PBSimpleTableField();
                $table->setName($name);
                $rows = [];

                $gradientPrice = $value['gradient_price'] ?? [];
                $tempQuantity = 0;
                $count = count($gradientPrice);
                for($i = $count-1; $i >= 0; $i --)
                {
                    $cells = [];
                    $row = new PBSimpleTableRow();
                    $cellKey = new PBSimpleTableCell();
                    $cellKey->setText($i == $count-1 ? ('>= ' . $gradientPrice[$i]['quantity']) : ($gradientPrice[$i]['quantity'] . ' ~ ' . $tempQuantity - 1));
                    $cells[] = $cellKey;
                    $cellValue = new PBSimpleTableCell();
                    $cellValue->setText($value['price_currency'] . ' ' . $gradientPrice[$i]['price']);
                    $cells[] = $cellValue;
                    $row->setCells($cells);
                    $rows[] = $row;
                    $tempQuantity = $gradientPrice[$i]['quantity'];
                }

                $cells = [];
                $row = new PBSimpleTableRow();
                $attrName = new PBSimpleTableCell();
                $attrName->setText('最小起订量');
                $cells[] = $attrName;
                $attrValue = new PBSimpleTableCell();
                $attrValue->setText('离岸价');
                $cells[] = $attrValue;
                $row->setCells($cells);
                $rows[] = $row;

                $table->setRows(array_reverse($rows));
                $table->setIsEmpty(empty($value['gradient_price']));

                $field->setTable($table);
                break;
            case ProductConstant::FOB_TYPE_SKU :
                $singleText = new PBSingleTextField();
                $fob = $value['price_currency'] . ' ' . $value['fob_price'];
                $singleText->setName($name);
                $singleText->setValue($fob);
                $singleText->setIsEmpty(empty(trim($fob)));
                $field->setSingleText($singleText);

                $externalField = new PBProductProfileField();
                $singleText = new PBSingleTextField();
                $quantity = $value['quantity'];
                $singleText->setName('最小起订量');
                $singleText->setValue($quantity);
                $singleText->setIsEmpty(empty($quantity));
                $externalField->setSingleText($singleText);
                $externalFields[] = $externalField;
                break;
        }

        array_push($fields, $field, ...$externalFields);
        return $fields;
    }

    public function buildSearchAttributes($attributeNodes)
    {
        // 目前只有两层
        $search = [];

        foreach ($attributeNodes as $attributeNode) {
            /**
            * @var $attributeNode PBSkuTreeAttribute
            */
            $node = $attributeNode->getNode();
            $name = $node->getId();
            $values = array_map(function($child) {
                /**
                 * @var $child PBSkuTreeAttribute
                 */
                return $child->getNode()->getId();
            }, iterator_to_array($attributeNode->getChild()));

            $search[] = [
                'name' => $name,
                'value' => $values
            ];
        }
        return $search;
    }

    public function buildUserProductTab($userId, $productId)
    {
        $data = (new PageLayoutApi())->getTabListByUser(
            $userId,
            PageLayoutConstant::MENU_ID_PRODUCT);

        $tabList = [];

        $product = new \common\library\product_v2\Product($this->clientId, $productId);

        $tabMap = PageLayoutConstant::TAB_MAP;
        $tabToPbEnumMap = PageLayoutConstant::TAB_TO_PB_ENUM_MAP;

        // 移动端仅支持的tab
        $includeTab = [
            PageLayoutConstant::PRODUCT_TAB_DETAILS,                // 资料
            PageLayoutConstant::PRODUCT_TAB_TRANSACTION_RECORDS,    // 成交记录
            PageLayoutConstant::PRODUCT_TAB_ATTACHMENT,             // 附件
//            PageLayoutConstant::PRODUCT_TAB_SUPPLIER,               // 供应商
            PageLayoutConstant::PRODUCT_TAB_OPERATION_RECORDS,      // 操作记录
        ];

        foreach (array_intersect($includeTab, $data) as $tab) {
            if (array_key_exists($tab, $tabMap)) {
                $tabInfo = new \protobuf\CRMCommon\PBPageLayoutEnumItem();
                $tabInfo->setTab($tabToPbEnumMap[$tab]);
                $tabInfo->setName(\Yii::t('pageLayout',
                    match($tab) {
                        PageLayoutConstant::PRODUCT_TAB_DETAILS => $tabMap[PageLayoutConstant::PRODUCT_TAB_PROFILE],
                        PageLayoutConstant::PRODUCT_TAB_TRANSACTION_RECORDS => $tabMap[PageLayoutConstant::PRODUCT_TAB_DEAL],
                        PageLayoutConstant::PRODUCT_TAB_OPERATION_RECORDS => $tabMap[PageLayoutConstant::PRODUCT_TAB_HISTORY],
                        default => $tabMap[$tab],
                    }
                ));
                $tabList[] = $tabInfo;

                if ($tab == PageLayoutConstant::PRODUCT_TAB_DETAILS)
                {
                    $externalTab = match(intval($product->product_type))
                    {
                        \common\library\product_v2\ProductConstant::PRODUCT_TYPE_SKU => PageLayoutConstant::PRODUCT_TAB_SPECIFICATION,
                        \common\library\product_v2\ProductConstant::PRODUCT_TYPE_COMBINE => PageLayoutConstant::PRODUCT_TAB_SUB_PRODUCT,
                        default => '',
                    };

                    if (empty($externalTab))
                        continue;

                    $tabInfo = new \protobuf\CRMCommon\PBPageLayoutEnumItem();
                    $tabInfo->setTab($tabToPbEnumMap[$externalTab]);
                    $tabInfo->setName(\Yii::t('pageLayout', $tabMap[$externalTab]));
                    $tabList[] = $tabInfo;
                }

            }
        }
        return $tabList;
    }

    //用于处理sku字段
    public function buildFormatSkuInfo($sku_id, $data)
    {
        $user = User::getLoginUser();
        if (empty($this->baseData['sku_items'])) {
            return $data;
        }

        if (empty($sku_id)) {
            return $data;
        }

        $sku_info = array_column($this->baseData['sku_items'], null, 'sku_id')[$sku_id] ?? [];

        $rsp = new \protobuf\Product\PBProductSkuInfoRsp();

        if (empty($data))
            return $rsp;

        //将sku字段覆盖至spu
        foreach ($data as &$groupField) {
            $group = new \protobuf\Product\PBProductProfileGroup();
            $group->setGroupName($groupField['name']);
            $fields = [];
            foreach ($groupField['fields'] as &$field) {
                if (isset($sku_info[$field['id']])) {
                    $field['value'] = $sku_info[$field['id']];
                    $field['format'] = $sku_info[$field['id']];
                }
                if (in_array($field['id'], array_keys(self::FIELD_SETTING['sku_info']['sku_field_map']))) {
                    $field['value'] = $sku_info[self::FIELD_SETTING['sku_info']['sku_field_map'][$field['id']]];
                    $field['format'] = $sku_info[self::FIELD_SETTING['sku_info']['sku_field_map'][$field['id']]];
                }
                $fields[] = $field;
            }

            $groupField['fields'] = $fields;
        }

        unset($this->baseData['data']);
        unset($this->baseData['sku_items']);
        $this->baseData['sku_id'] = $sku_id;
        $this->baseData['images'] = [$sku_info['image_info']];
        $this->baseData['fob'] = $sku_info['fob'] ?? [];
        $this->baseData['product_no'] = $sku_info['sku_code'] ?? '';
        $this->baseData['product_name'] = $this->baseData['name'] ?? '';
        $this->baseData['disable_flag'] = $sku_info['disable_flag'] ?? 0;

        unset($field);
        unset($groupField);
        return $data;
    }
}