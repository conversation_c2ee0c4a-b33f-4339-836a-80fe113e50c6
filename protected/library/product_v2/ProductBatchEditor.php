<?php

namespace common\library\product_v2;

use common\library\custom_field\company_field\duplicate\FieldUniqueValidator;
use common\library\history\base\Builder;
use common\library\history\product\ProductSetting;
use common\library\product_v2\formatter\ProductField;
use common\library\product_v2\parts\ProductPartsFilter;
use common\library\product_v2\sku\ProductSku;
use common\library\product_v2\sku\ProductSkuFilter;
use common\library\product_v2\sku\ProductSkuMetadata;
use common\library\server\es_search\SearchQueueService;
use common\library\util\Arr;
use common\library\validation\Validator;

class ProductBatchEditor{   // 分批处理，支持大批量数据编辑
    protected $clientId;
    protected $updateUserId;
    protected $setVal;
    protected $batchSetVal;
    protected $conds;       // 查询产品的条件
    protected $productIds;  // 直接设置Id作为条件
    protected $skuIds;  // 直接设置Id作为条件
    protected $batchSize = 100;
    protected $productField;
    protected $needUpdateSku;   // 是否需要修改sku数据
    protected $needUpdateSpu;   // 是否需要修改spu数据
    protected $needGetSkuData;   // 是否需要先查sku数据再改
    protected $needGetSpuData;   // 是否需要先查spu数据再改
    protected $getSpuFields = ['product_id', 'name', 'product_no', 'source_type','product_type'];    // 要获取的spu的字段
    protected $getSkuFields = ['product_id', 'sku_id'];    // 要获取的sku的字段
    protected $spuSet = [];
    protected $skuSet = [];
    protected $spuParams = [];
    protected $skuParams = [];
    protected $mainGroupMap = [];
    protected $paramKeyMap = [];
    protected $existSpuData = [];
    protected $existSkuData = [];
    protected $spuDbFieldMap = [];
    protected $skuDbFieldMap = [];
    protected $skuMap = [];
    protected $single = false;

    /**
     * @param bool $single
     */
    public function setSingle($single): void
    {
        $this->single = $single;
    }


    /**
     * 约定返回错误
     * @var array
     * [
     *     'product_id' => '要给前端的产品id数组',
     *     'result' => true|false, 编辑结果
     *     'message' => '提示消息/不一定是报错信息',
     * ]
     */
    protected $runBatchEditResult = [];

    protected $fieldSetHandler = [     // 这里的field是针对前端传过来的field
        'cost_price' => 'collectCostPriceSet',
        'cost_currency' => 'collectCostCurrencySet',
        'fob_price' => 'collectFobPriceSet',
        'fob' => 'collectFobSet',
        'product_volume' => 'collectProductVolumnSet',
        'package_volume' => 'collectPackageVolumnSet',
        'carton_volume' => 'collectCartonVolumnSet',
        'group_id' => 'collectGroupIdSet',
//        'product_remark' => 'collectBothSpuSkuField',
//        'description' => 'collectBothSpuSkuField',
    ];

    protected $spuSkuBothField = [      // 批量编辑下 spu 和 sku 都需要存储相同值的字段
        'product_remark' => 'sku_remark',
        'description' => 'sku_description',
    ];
    protected $skuIngnoreField =[
        'sku_remark',
        'sku_description',
        'sku_code',
    ];

    public function __construct($clientId, $updateUserId, array $setVal){
        $this->clientId = $clientId;
        $this->setVal = $setVal;
        $this->updateUserId = $updateUserId;
    }

    public function getRunBatchEditResult()
    {
        return $this->runBatchEditResult;
    }

    public function setConds($conds){
        $this->conds = $conds;
    }

    public function setProductIds($productIds){
        $this->productIds = $productIds;
    }

    public function setSkuIds($skuIds)
    {
        $skuIds = is_array($skuIds) ? $skuIds : [$skuIds];
        $this->skuIds = Arr::uniqueFilterValues($skuIds);
    }

    public function run(){
        $this->init();

        // 如果传入了 productIds，则优先使用 productIds 作为范围，否则使用 conds 作为范围
        $api = new ProductAPI($this->clientId, $this->updateUserId);
        if(!empty($this->productIds)){
            $filter = $api->buildFilter(['product_id' => $this->productIds]);
        }else{
            $filter = $api->buildFilter($this->conds);
        }
        $filter->select(['product_id', 'product_type']);
        $productInfos = $filter->rawData();

        $productInfos = array_chunk($productInfos, 200);    // 200个产品一批更新
        foreach($productInfos as $productInfoItem){
            $this->runBatchNew($productInfoItem);
        }
    }

    /***
     * 复用批量编辑能力
     * single切分编辑spu、sku，true仅编辑spu，不编辑sku，false则不限制编辑sku
     * 有传入sku 则会支持编辑sku字段
     * @return void
     * @throws \Throwable
     */
    public function singleRun()
    {
        $this->setSingle(true);

        $product_id = is_array($this->productIds) ? $this->productIds[0] ?? 0 : $this->productIds;
        $sku_id = is_array($this->skuIds) ? $this->skuIds[0] ?? 0 : $this->skuIds;

        $product = new Product($this->clientId, $product_id);
        if ($product->isNew()){
            throw new \RuntimeException(\Yii::t('invoice', '产品不存在或已删除'));
        }
        $product->bindAttrbuties($this->setVal);
        //如果存在不符合规则编号 不变更则不校验
        if (
            in_array('product_no', array_values(array_keys($this->setVal)))
            &&
            $product->product_no != $product->getOldAttributes(['product_no'])['product_no']
        ) {
            $product->checkNoExist();

            if ($product->product_no == '') {
                $this->setVal['product_no'] = $product->buildNo($product);
            }

            //无规格/组合产品 sku编号要一致 需要加sku_code 编辑 则退化成 spu+sku的单个编辑
            if (in_array($product->product_type,[
                 ProductConstant::PRODUCT_TYPE_SPU,
                 ProductConstant::PRODUCT_TYPE_COMBINE,
            ])) {
                $this->setVal['sku_code'] = $this->setVal['product_no'];
                $filter = new ProductSkuFilter($this->clientId);
                $filter->product_id = $product_id;
                $filter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
                $filter->select(['sku_id']);
                $sku_ids = array_column($filter->rawData() ?? [], 'sku_id');
                $sku_ids = Arr::uniqueFilterValues($sku_ids);
                $this->skuIds = $sku_ids;
                $this->setSingle(false);
            }
        }

        if (in_array('sku_code', array_values(array_keys($this->setVal))) && !empty($sku_id)) {
            $productSku = new ProductSku($this->clientId, $sku_id);
            if ($productSku->isNew()){
                throw new \RuntimeException(\Yii::t('invoice', '产品不存在或已删除'));
            }

            $productSku->bindAttrbuties($this->setVal);

            if ($productSku->sku_code != $productSku->getOldAttributes(['sku_code'])['sku_code']) {
                $productSku->checkNoExist();
            }
        }
        (new Validator($this->setVal, [
            'product_no' => 'string|max:255',
            'sku_code' => 'string|max:255',
            'name' => 'string|max:255',
            'model' => 'string|max:255',
        ]))->validate();

        $this->init();

        $this->runBatchNew([[
            'product_id' => $product->product_id,
            'product_type' => $product->product_type
        ]], $this->skuIds);
    }

    protected function getProductField(){
        if(!$this->productField){
            $this->productField = new ProductField($this->clientId);
        }
        return $this->productField;
    }

    protected function init(){
        $productField = $this->getProductField();
        $skuFieldMap = array_flip(ProductField::$skuFields);
        $skuUnpackFieldMap = array_flip(ProductField::$skuUnpackFields);
        $skuExternalFieldMap = array_flip($productField->getSkuExternalFields());
        $skuFieldsMap = $skuFieldMap + $skuUnpackFieldMap + $skuExternalFieldMap;       // 包含了 sku 的系统+自定义字段

        foreach($this->setVal as $field => $value){
            if(isset($skuFieldsMap[$field]) || in_array($field, ['fob_price', 'cost_price'])){
                $this->needUpdateSku = true;
            }

            if($field == 'fob_price'){
                $this->needGetSkuData = true;
                $this->getSkuFields = array_merge($this->getSkuFields, ['fob_type', 'price_min', 'price_max', 'gradient_price', 'fob_price']);
            }

            if($field == 'cost_price' || $field == 'cost_currency'){
                // 变更成本价的时候，需要考虑是否关联成本单价，如果是则不变更这些产品的成本价和成本价币种
                $this->getSpuFields = array_merge($this->getSpuFields, ['associate_cost_flag']);
                $this->getSkuFields[] = 'cost';
            }

            if($field == 'group_id'){
                if($value === ''){
                    $this->setVal['group_id'] = 0;
                }
                $targetGroupId = is_array($this->setVal['group_id']) ? $this->setVal['group_id'] : [$this->setVal['group_id']];
                $this->mainGroupMap = ProductHelper::getMainGroupMap($this->clientId, $targetGroupId);
            }
        }

        //sku别名字段不在字段表 需要写入
        $ignoreField = [];
        if (!empty($this->skuIds)) {
            $ignoreField = array_values($this->skuIngnoreField);
        }

        // 为了写入历史操作而需要查询的旧数据字段
        $unpackSetVal = $productField->unpackFormData($this->setVal, $ignoreField);
        foreach($unpackSetVal as $key => $_){
            if($this->isSkuDbField($key)){
                $this->needGetSkuData = true;
                $this->getSkuFields[] = $key;
            }
            if($this->isSpuDbField($key)){
                $this->getSpuFields[] = $key;
            }
        }
    }


    protected function makeProductEditFields($productIds, $productInfos)
    {
        $productEditMap = [];
        // 过滤组合产品改为可为配件，目前不允许组合产品为配件
        if (isset($this->setVal['is_parts']) && $this->setVal['is_parts'] == ProductConstant::IS_PARTS_YES) {
            $combineProductIds = array_reduce($productInfos, function ($carry, $v) {
                if ($v['product_type'] == ProductConstant::PRODUCT_TYPE_COMBINE) {
                    $carry[] = $v['product_id'];
                }
                return $carry;
            }, []);
            if (!empty($combineProductIds)) {
                $updateFields = $this->setVal;
                unset($updateFields['is_parts']);
                $productEditMap[] = [
                    'product_id' => $combineProductIds,
                    'update_Fields' => $updateFields,
                ];
                $productIds = array_diff($productIds, $combineProductIds);
            }
        }
        // 特殊处理逻辑 is_parts 如果已经成为其他产品的配件了，那么不再允许它被设置从is_parts 1 => 0
        if (isset($this->setVal['is_parts']) && $this->setVal['is_parts'] == ProductConstant::IS_PARTS_NO) {
            $partsFilter = new ProductPartsFilter($this->clientId);
            $partsFilter->parts_product_id = $productIds;
            $partsFilter->select(['parts_product_id']);
            $partsFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
            $partsProductIds = array_column($partsFilter->rawData(), 'parts_product_id');
            $norAllowProductIds = array_intersect($productIds, $partsProductIds);
            if (!empty($norAllowProductIds)) {
                $updateFields = $this->setVal;
                unset($updateFields['is_parts']);
                $productEditMap[] = [
                    'product_id' => $norAllowProductIds,
                    'update_Fields' => $updateFields,
                ];
                // 调整配件提示信息
                $this->runBatchEditResult['is_parts'] = [
                    'product_id' => $norAllowProductIds,
                    'result' => true,
                    'message' => \Yii::t('product', '保存成功，部分产品已做为其他产品的配件，暂不支持关闭「可为配件」特性'),
                ];
                $productIds = array_diff($productIds, $norAllowProductIds);
            }
        }
        // 默认是所有都更新
        // 如果为空，那么就没有所有都更新的产品了
        if (!empty($productIds)) {
            $productEditMap[] = [
                'product_id' => $productIds,
                'update_Fields' => $this->setVal,
            ];
        }
        return $productEditMap;
    }

    protected function runBatchNew($productInfos, $sku_ids = [])
    {
        /** 分2个维度，4种情况，每1批数据最多执行4次updateSql
         * 编辑的字段在SKU：a.需要先查询再编辑; b.直接编辑
         * 编辑的字段在SPU：a.需要先查询再编辑; b.直接编辑
         */
        $productIds = array_column($productInfos, 'product_id');
        // 1 查询数据
        $this->existSpuData = $this->getExistData($productIds, new ProductFilter($this->clientId), $this->getSpuFields);

        if ($this->needGetSkuData || !empty($this->skuIds)) {
            // 操作历史是product纬度 会组装完整数据 不能过滤
            $this->existSkuData = $this->getExistData($productIds, new ProductSkuFilter($this->clientId), $this->getSkuFields);
        }

        if (!empty($this->skuIds)) {
            $skuMap = [];
            foreach ($this->existSkuData as $item) {
                $productId = $item['product_id'];
                $sku_id = $item['sku_id'];

                if (!in_array($sku_id, $sku_ids)) {
                    continue;
                }

                if (array_key_exists($productId, $skuMap)) {
                    $skuMap[$productId][] = $sku_id;
                } else {
                    $skuMap[$productId] = [$sku_id];
                }
            }
            $this->skuMap = $skuMap;
        }

        // 2 特别字段处理
        $productEditMap = $this->makeProductEditFields($productIds, $productInfos);

        foreach ($productEditMap as &$productEdit) {
            // 先清空一波初始化变量
            $externalFields = [];
            $this->setVal = $this->spuSet = $this->skuSet = $this->spuParams = $this->skuParams = [];
            $batchSetVal = $this->batchSetVal = $productEdit['update_Fields'];
            // 3 收集sql
            foreach($batchSetVal as $field => $val){
                if(is_numeric($field)){
                    $externalFields[$field] = $val;
                    unset($this->batchSetVal[$field]);
                }

                if(isset($this->fieldSetHandler[$field])){
                    call_user_func_array([$this, $this->fieldSetHandler[$field]], [$field, $val, $productEdit['product_id']]);
                    unset($this->batchSetVal[$field]);
                }

                if (isset($this->spuSkuBothField[$field]) && !$this->single) {
                    $this->collectBothSpuSkuField($field, $this->spuSkuBothField[$field], $val, $productEdit['product_id']);
                }
            }

            // 此时特殊处理的字段已经组装进 skuSet 和 spuSet 中了，接下来是对 pack 字段进行unpack
            $productField = $this->getProductField();
            $this->batchSetVal = $productField->unpackFormData($this->batchSetVal, [], true);

            foreach($this->batchSetVal as $field => $val){
                $this->collectCommonFieldSet($field, $val, $productEdit['product_id']);
            }
            $this->collectExternalFieldSet($externalFields, $productEdit['product_id']);

            // 保留一下条件
            $productEdit['spu_set'] = $this->spuSet;
            $productEdit['spu_param'] = $this->spuParams;
            $productEdit['sku_set'] = $this->skuSet;
            $productEdit['sku_param'] = $this->skuParams;
        }
        unset($productEdit);

        // 3 执行sql
        try{
            $db = \PgActiveRecord::getDbByClientId($this->clientId);
            $transation = $db->beginTransaction();

            foreach ($productEditMap as $productEditItem) {
                if (!empty($productEditItem['sku_set'])) {
                    $this->updateSkuRowsNew($productEditItem['product_id'], $productEditItem['sku_set'], $productEditItem['sku_param']);
                }

                if(!empty($productEditItem['spu_set'])){
                    $this->updateSpuRowsNew($productEditItem['product_id'], $productEditItem['spu_set'], $productEditItem['spu_param']);
                }
            }
            // 4 后置操作（操作历史、统计、ES）
            $this->writeHistory($productIds);   // 4.1 操作历史
            $transation->commit();

        }catch (\Throwable $e){
            $transation->rollback();
            throw $e;
        }

        foreach ($productIds as $productId) {   // 4.2 统计
            $product = new Product($this->clientId, $productId);
            $product->statistics();
        }

        // 4.3 ES
        SearchQueueService::pushProductQueue($this->updateUserId, $this->clientId, $productIds, \Constants::PRODUCT_INDEX_TYPE_UPDATE, \Constants::TYPE_PRODUCT);

        // 5 清空本批查询的数据 和 sql语句
        $this->clearBatch();
    }
    // 执行单批数据
    protected function runBatch($productIds){
        /** 分2个维度，4种情况，每1批数据最多执行4次updateSql
         * 编辑的字段在SKU：a.需要先查询再编辑; b.直接编辑
         * 编辑的字段在SPU：a.需要先查询再编辑; b.直接编辑
         */

        // 1 查询数据
        $this->existSpuData = $this->getExistData($productIds, new ProductFilter($this->clientId), $this->getSpuFields);

        if($this->needGetSkuData){
            $this->existSkuData = $this->getExistData($productIds, new ProductSkuFilter($this->clientId), $this->getSkuFields);
        }

        // 2 收集sql
        $externalFields = [];
        $batchSetVal = $this->batchSetVal = $this->setVal;
        foreach($batchSetVal as $field => $val){
            if(is_numeric($field)){
                $externalFields[$field] = $val;
                unset($this->batchSetVal[$field]);
            }

            if(isset($this->fieldSetHandler[$field])){
                call_user_func_array([$this, $this->fieldSetHandler[$field]], [$field, $val,$productIds]);
                unset($this->batchSetVal[$field]);
            }

            if(isset($this->spuSkuBothField[$field])){
                $this->collectBothSpuSkuField($field, $this->spuSkuBothField[$field], $val, $productIds);
            }
        }

        // 此时特殊处理的字段已经组装进 skuSet 和 spuSet 中了，接下来是对 pack 字段进行unpack
        $productField = $this->getProductField();
        $this->batchSetVal = $productField->unpackFormData($this->batchSetVal, [], true);

        foreach($this->batchSetVal as $field => $val){
            $this->collectCommonFieldSet($field, $val,$productIds);
        }
        $this->collectExternalFieldSet($externalFields,$productIds);

        // 3 执行sql
        try{
            $db = \PgActiveRecord::getDbByClientId($this->clientId);
            $transation = $db->beginTransaction();
            if(!empty($this->skuSet)){
                $this->updateSkuRows($productIds);
            }

            if(!empty($this->spuSet)){
                $this->updateSpuRows($productIds);
            }

            // 4 后置操作（操作历史、统计、ES）
            $this->writeHistory($productIds);   // 4.1 操作历史
            $transation->commit();
        }catch (\Throwable $e){
            $transation->rollback();
            throw $e;
        }

        foreach ($productIds as $productId) {   // 4.2 统计
            $product = new Product($this->clientId, $productId);
            $product->statistics();
        }

        // 4.3 ES
        SearchQueueService::pushProductQueue($this->updateUserId, $this->clientId, $productIds, \Constants::PRODUCT_INDEX_TYPE_UPDATE, \Constants::TYPE_PRODUCT);

        // 5 清空本批查询的数据 和 sql语句
        $this->clearBatch();
    }

    protected function clearBatch(){
        $this->skuSet = $this->spuSet = $this->existSpuData = $this->existSkuData = $this->paramKeyMap = [];
    }

    protected function writeHistory($productIds){
        // 查询更新后的产品
        $newSpu = $this->getExistData($productIds, new ProductFilter($this->clientId), $this->getSpuFields);
        $newSku = [];
        if($this->needGetSkuData){
            $newSku = $this->getExistData($productIds, new ProductSkuFilter($this->clientId), $this->getSkuFields);
        }

        // 组装比对数据
        $extraData = $oldData = $newData = [];
        $existSkuDataGroups = \ArrayUtil::groupBy($this->existSkuData, 'product_id', null, true);
        foreach($this->existSpuData as $existSpuDatum){
            $extraData[$existSpuDatum['product_id']] = [
                'data' => $existSpuDatum,
                'update_type' => \Constants::FIELD_EDIT_TYPE_BY_USER,
                'refer_id' => 0,
                'refer_type' => 0,
            ];
            $existSpuDatum['sku_list'] = $existSkuDataGroups[$existSpuDatum['product_id']] ?? [];
            if(in_array($existSpuDatum['product_type'], [ProductConstant::PRODUCT_TYPE_SPU, ProductConstant::PRODUCT_TYPE_COMBINE])){
                $existSkuDatum = isset($existSkuDataGroups[$existSpuDatum['product_id']]) ? current($existSkuDataGroups[$existSpuDatum['product_id']]) : [];
                $existSpuDatum = $existSpuDatum + $existSkuDatum;
            }
            $oldData[] = $existSpuDatum;
        }
        unset($existSkuDataGroups);


        $skuDataGroups = \ArrayUtil::groupBy($newSku, 'product_id', null, true);
        foreach($newSpu as $spuDatum){
            $extraData[$spuDatum['product_id']] = [
                'data' => $spuDatum,
                'update_type' => \Constants::FIELD_EDIT_TYPE_BY_USER,
                'refer_id' => 0,
                'refer_type' => 0,
            ];
            $spuDatum['sku_list'] = $skuDataGroups[$spuDatum['product_id']] ?? [];
            if(in_array($spuDatum['product_type'], [ProductConstant::PRODUCT_TYPE_SPU, ProductConstant::PRODUCT_TYPE_COMBINE])){
                $skuDatum = isset($skuDataGroups[$spuDatum['product_id']]) ? current($skuDataGroups[$spuDatum['product_id']]) : [];
                $spuDatum = $spuDatum + $skuDatum;
            }
            $newData[] = $spuDatum;
        }
        unset($skuDataGroups);

        $historyBuilder = new Builder(new ProductSetting());
        $historyBuilder->setClientId($this->clientId)
            ->setType(ProductSetting::TYPE_EDIT_PRODUCT)
            ->setUpdateUser($this->updateUserId)
            ->initFromBatchData($newData)
            ->setExtraData([],$extraData)
            ->setOldAttributes($oldData);
        $historyBuilder->buildBatch();
    }

    protected function updateSkuRowsNew($productIds, $skuSet, $skuParams) {
        $now = date('Y-m-d H:i:s');
        $productIdsStr = implode(',', $productIds);
        $skuSet['update_time'] = "update_time='{$now}'";
        $skuSet['update_user'] = "update_user={$this->updateUserId}";
        $setSql = implode(',', $skuSet);
        $where = " 1 = 1";

        if (!empty($this->skuIds)) {
            $sku_ids = [];
            foreach ($productIds as $productId){
                if (empty($this->skuMap[$productId])){
                    continue;
                }
                $sku_ids = array_merge($this->skuMap[$productId],$sku_ids);
            }

            if (empty($sku_ids)) {
                $where = " 0 = 1";
            } else {
                $skuIdStr = implode(',', $sku_ids);
                $where = " sku_id IN ({$skuIdStr}) ";
            }

        }

        $sql = "UPDATE tbl_product_sku SET {$setSql} WHERE client_id={$this->clientId} AND product_id IN ({$productIdsStr}) and enable_flag=1 and $where ";
        $db = \PgActiveRecord::getDbByClientId($this->clientId);
        $count = $db->createCommand($sql)->execute($skuParams);
        \LogUtil::info("batch update product sku client_id:{$this->clientId}, update count:".$count);
    }

    protected function updateSkuRows($productIds){
        $now = date('Y-m-d H:i:s');
        $productIdsStr = implode(',', $productIds);
        $this->skuSet['update_time'] = "update_time='{$now}'";
        $this->skuSet['update_user'] = "update_user={$this->updateUserId}";
        $setSql = implode(',', $this->skuSet);
        $sql = "UPDATE tbl_product_sku SET {$setSql} WHERE client_id={$this->clientId} AND product_id IN ({$productIdsStr}) and enable_flag=1";
        $db = \PgActiveRecord::getDbByClientId($this->clientId);
        $count = $db->createCommand($sql)->execute($this->skuParams);
        \LogUtil::info("batch update product sku client_id:{$this->clientId}, update count:".$count);
    }

    protected function updateSpuRowsNew($productIds, $spuSet, $spuParams) {
        $now = date('Y-m-d H:i:s');
        $productIdsStr = implode(',', $productIds);
        $spuSet['update_time'] = "update_time='{$now}'";
        $spuSet['update_user'] = "update_user={$this->updateUserId}";
        $setSql = implode(',', $spuSet);
        $sql = "UPDATE tbl_product SET {$setSql} WHERE client_id={$this->clientId} AND product_id IN ({$productIdsStr}) and enable_flag=1";
        $db = \PgActiveRecord::getDbByClientId($this->clientId);
        $count = $db->createCommand($sql)->execute($spuParams);
        \LogUtil::info("batch update product spu client_id:{$this->clientId}, update count:".$count);
    }

    protected function updateSpuRows($productIds){
        $now = date('Y-m-d H:i:s');
        $productIdsStr = implode(',', $productIds);
        $this->spuSet['update_time'] = "update_time='{$now}'";
        $this->spuSet['update_user'] = "update_user={$this->updateUserId}";
        $setSql = implode(',', $this->spuSet);
        $sql = "UPDATE tbl_product SET {$setSql} WHERE client_id={$this->clientId} AND product_id IN ({$productIdsStr}) and enable_flag=1";
        $db = \PgActiveRecord::getDbByClientId($this->clientId);
        $count = $db->createCommand($sql)->execute($this->spuParams);
        \LogUtil::info("batch update product spu client_id:{$this->clientId}, update count:".$count);
    }

    protected function getParamKey($field){
        if(empty($this->paramKeyMap[$field])){
            $this->paramKeyMap[$field] = 1;
        }else{
            $this->paramKeyMap[$field]++;
        }

        return ":".$field."_".$this->paramKeyMap[$field];
    }

    protected function getExistData($productIds, $filter, $selectFields){
        $filter->product_id = $productIds;
        $filter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $filter->select(array_unique($selectFields));
        return $filter->rawData();
    }

    protected function collectCostPriceSet($field, $value, $productIds=[]){
        $editType = $value['percent_type'];
        $editAmount = $value['percent_amount'];

        // 先确认要变更的这一批产品里面有没有关联成本单价的产品
        $associateProductIds = $this->getAssociateCostProductIds();
        $dataSql = self::getPriceUpdateSql('cost', $editType, $editAmount);
        if(empty($associateProductIds)){
            $this->skuSet['cost'] = " cost = {$dataSql} ";
        }else{
            $productIdStr = implode(',', $associateProductIds);
            $this->skuSet['cost'] = " cost = case when product_id in ({$productIdStr}) then cost else {$dataSql} end";
        }

//        unset($this->batchSetVal[$field]);
    }

    protected function collectCostCurrencySet($field, $value, $productIds=[]){
        $associateProductIds = $this->getAssociateCostProductIds();
        if(empty($associateProductIds)){
            $this->skuSet['cost_currency'] = " cost_currency = '{$value}' ";
        }else{
            $productIdStr = implode(',', $associateProductIds);
            $this->skuSet['cost_currency'] = " cost_currency = case when product_id in ({$productIdStr}) then cost_currency else '{$value}' end";
        }
    }

    protected function getAssociateCostProductIds(){
        $associateCostProductIds = [];
        foreach($this->existSpuData as $spuDatum){
            if($spuDatum['associate_cost_flag'] == ProductConstant::ASSOCIATE_COST_TRUE){
                $associateCostProductIds[] = $spuDatum['product_id'];
            }
        }
        return $associateCostProductIds;
    }

    protected function collectFobPriceSet($field, $value, $productIds=[]){
        $editType = $value['percent_type'];
        $editAmount = $value['percent_amount'];
        $db = \PgActiveRecord::getDbByClientId($this->clientId);
        $skuDataChunk = array_chunk($this->existSkuData, 1000);

        foreach($skuDataChunk as $skuData){
            $priceMinSql = $priceMaxSql = $gradientPriceSql = $exactPriceSql = '';
            $skuSet = [];
            $updateSkuFobPrice = false;

            foreach ($skuData as $datum) {
                switch ($datum['fob_type']) {
                    case ProductConstant::FOB_TYPE_PRICE:
                        $targetField = 'price_min';
                        $newData = self::calculatePrice($datum[$targetField], $editType, $editAmount);
                        $priceMinSql .= " when sku_id={$datum['sku_id']} then {$newData} ";

                        $targetField = 'price_max';
                        $newData = self::calculatePrice($datum[$targetField], $editType, $editAmount);
                        $priceMaxSql .= " when sku_id={$datum['sku_id']} then {$newData} ";
                        break;
                    case ProductConstant::FOB_TYPE_GRADIENT:
                        $targetField = 'gradient_price';
                        $oldData =  $datum[$targetField] ?? [];
                        $newData = [];
                        foreach ($oldData as $key => $item) {
                            $price = (float)$item['price'] ?? 0;
                            $item['price'] = self::calculatePrice($price, $editType, $editAmount);
                            if($item['price'] == 'null::numeric'){
                                $item['price'] = '';
                            }
                            $newData[] = $item;
                        }
                        $gradientSql = json_encode($newData);
                        $gradientPriceSql .= " when sku_id={$datum['sku_id']} then '{$gradientSql}' ";
                        break;
                    case ProductConstant::FOB_TYPE_SKU:
                        $newData = self::calculatePrice($datum['fob_price'], $editType, $editAmount);
                        $exactPriceSql .= " when sku_id={$datum['sku_id']} then {$newData} ";
                    default:
                        break;
                }
            }
            if (!empty($priceMinSql)) {
                $skuSet[] = "price_min = case $priceMinSql else price_min end";
                $updateSkuFobPrice = true;
            }
            if (!empty($priceMaxSql)) {
                $skuSet[] = "price_max = case $priceMaxSql else price_max end";
                $updateSkuFobPrice = true;
            }
            if (!empty($gradientPriceSql)) {
                $skuSet[] = "gradient_price = case $gradientPriceSql else gradient_price end";
                $updateSkuFobPrice = true;
            }
            if (!empty($exactPriceSql)) {
                $skuSet[] = "fob_price = case $exactPriceSql else fob_price end";
                $updateSkuFobPrice = true;
            }

            //update data
            $now = date('Y-m-d H:i:s');
            $skuIdsSql = implode(',', array_column($skuData, 'sku_id'));
            if ($updateSkuFobPrice) {
                $skuSet[] = "update_time='{$now}'";
                $setSql = implode(',', $skuSet);
                $sql = "UPDATE tbl_product_sku SET {$setSql} WHERE client_id={$this->clientId} AND sku_id IN ($skuIdsSql)";
                $db->createCommand($sql)->execute();
            }
        }

//        unset($this->batchSetVal[$field]);
    }

    protected function collectFobSet($field, $value, $productIds=[]){
//        {"price_min":10,"price_max":20,"quantity":5,"fob_type":"1","gradient_price":[]}
        $fobType = $value['fob_type'] ?? 0;
        if(empty($fobType)){
            return;
        }

        if ($fobType == ProductConstant::FOB_TYPE_PRICE) {
            $this->batchSetVal['quantity'] = $value['quantity'];
            $this->skuSet['price_min'] = " price_min = " . $this->formatNumericSql($value['price_min']);
            $this->skuSet['price_max'] = " price_max = " . $this->formatNumericSql($value['price_max']);
        }

        if ($fobType == ProductConstant::FOB_TYPE_GRADIENT) {
            $this->batchSetVal['gradient_price'] = $value['gradient_price'];
        }

        if ($fobType == ProductConstant::FOB_TYPE_SKU) {
            $this->batchSetVal['quantity'] = $value['quantity'];
            $this->skuSet['fob_price'] = " fob_price = ".$this->formatNumericSql($value['fob_price']);
        }

        $this->batchSetVal['fob_type'] = $fobType;
    }

    protected function collectPackageVolumnSet($field, $value, $productIds=[]){
        $this->collectVolumnSet($field, $value, 'package_size','package_size_length', 'package_size_height' ,'package_size_weight');
    }

    protected function collectProductVolumnSet($field, $value, $productIds=[]){
        $this->collectVolumnSet($field, $value,'product_size', 'product_size_length', 'product_size_height' ,'product_size_weight');
    }

    protected function collectCartonVolumnSet($field, $value, $productIds=[]){
        $this->collectVolumnSet($field, $value,'carton_size', 'carton_size_length', 'carton_size_height' ,'carton_size_weight');
    }

    protected function collectVolumnSet($field, $value, $dataKey, $lengthField, $heightField, $weightField){
        $editType = $value['edit_type'];

        if ($editType == ProductConstant::BATCH_EDIT_PRICE_TYPE_VALUE) {
            $editVal = trim($value['edit_value']) === '' ? '' : $value['edit_value'];
            $this->setCommonFieldVal($field, $editVal);
//            $this->skuSet[$field] = " {$field} = {$editVal} ";
            return;
        }
        //根据产品包装尺寸重算包装体积
        if (isset($this->batchSetVal[$dataKey]) && isset($this->batchSetVal[$dataKey][$lengthField]) &&
            isset($this->batchSetVal[$dataKey][$heightField] ) &&
            isset($this->batchSetVal[$dataKey][$weightField])
        ) {
            //更新长宽高时刷新包装体积，以提交的长宽高为准
            $editVal = $this->calculPackageVolumn($this->batchSetVal[$dataKey][$lengthField], $this->batchSetVal[$dataKey][$heightField], $this->batchSetVal[$dataKey][$weightField]);
            $this->skuSet[$field] = " {$field} = {$editVal} ";
            return;
        }

        $this->skuSet[$field] = " {$field} = ROUND({$weightField}/100 * {$lengthField}/100 * {$heightField}/100, 8) ";

        unset($this->batchSetVal[$field]);
    }

    protected function collectGroupIdSet($field, $value, $productIds=[]){
        $realGroupId = !empty($value) ? $value : 0;
        $mainGroupId = $this->mainGroupMap[$realGroupId];
        $this->spuSet['main_group_id'] = "main_group_id=:main_group_id";
        $this->spuParams[":main_group_id"] = $mainGroupId;

        $this->spuSet['group_id'] = "group_id={$realGroupId}";
    }

    protected function collectCommonFieldSet($field, $value, $productIds=[]){
        if(is_numeric($field)){
            return;
        }

        switch($field){
            case 'gradient_price':
                $fieldDataSql = json_encode($value);
                $this->skuSet[$field] = "gradient_price = '$fieldDataSql'";
                break;
            case 'tax_refund_rate':
            case 'vat_rate':
                if ($value != ""){
                    $value = round($value,2);
                    $this->spuSet[$field] = "{$field} = '$value'";
                }else{
                    $this->spuSet[$field] = "{$field} = null::numeric";
                }
                break;
            default:
                $this->setCommonFieldVal($field, $value);
                break;
        }
    }

    protected function isSpuDbField($field){
        if(empty($this->spuDbFieldMap)){
            $this->spuDbFieldMap = (new ProductMetadata())->columnFieldsSetting();
        }

        return isset($this->spuDbFieldMap[$field]);
    }

    protected function isSkuDbField($field){
        if(empty($this->skuDbFieldMap)){
            $this->skuDbFieldMap = (new ProductSkuMetadata())->columnFieldsSetting();
        }

        return isset($this->skuDbFieldMap[$field]);
    }

    protected function collectExternalFieldSet($externalFieldsData, $productIds=[]){
        if(empty($externalFieldsData)){
            return ;
        }

        // 1 将 spu 的自定义字段 和 sku 的自定义字段分开来
        $productField = $this->getProductField();
        $skuExternalFieldMap = array_flip($productField->getSkuExternalFields());

        $skuExternalFieldData = [];
        foreach($externalFieldsData as $key => $val){
            if(isset($skuExternalFieldMap[$key])){
                $skuExternalFieldData[$key] = $val;
            }
        }

        // 2 拼接sql
        if(!empty($skuExternalFieldData)){
            $skuSql = json_encode($skuExternalFieldData, JSON_HEX_APOS);
            $this->skuSet['sku_external_field_data'] = "sku_external_field_data = (case when (sku_external_field_data = '[]') then '{}' else sku_external_field_data end) || '$skuSql'";
        }

        if(!empty($externalFieldsData)){
            $spuSql = json_encode($externalFieldsData, JSON_HEX_APOS);
            $this->spuSet['external_field_data'] = "external_field_data = (case when (external_field_data = '[]') then '{}' else external_field_data end) || '$spuSql'";
        }
    }

    public static function getPriceUpdateSql($field_id, $edit_type, $edit_amount)
    {
        if ($edit_type == ProductConstant::BATCH_EDIT_PRICE_TYPE_VALUE) {
            if(trim($edit_amount) === ''){
                return "null::numeric";
            }
            return "{$edit_amount}";
        }

        $edit_amount = !empty($edit_amount) ? $edit_amount : 0;
        $sql = " {$field_id} " . ProductConstant::BATCH_EDIT_PRICE_OPERATOR[$edit_type] . " {$edit_amount}";

        if (in_array($edit_type, [ProductConstant::BATCH_EDIT_PRICE_TYPE_PLUS_PERCENTAGE, ProductConstant::BATCH_EDIT_PRICE_TYPE_SUBTRACT_PERCENTAGE])) {
            $sql .=  "* {$field_id} /100";
        }

        return $sql;
    }

    public static function calculatePrice($originPrice, $edit_type, $editAmount, $round = 8)
    {
        $realEditAmount = trim($editAmount) === '' ? 'null::numeric' : $editAmount;
        $originPrice = floatval($originPrice);
        $editAmount = floatval($editAmount);

        switch ($edit_type) {
            case ProductConstant::BATCH_EDIT_PRICE_TYPE_VALUE:
                return $realEditAmount;
            case ProductConstant::BATCH_EDIT_PRICE_TYPE_PLUS_VALUE:
                return $originPrice + $editAmount;
            case ProductConstant::BATCH_EDIT_PRICE_TYPE_SUBTRACT_VALUE;
                return $originPrice - $editAmount;
            case ProductConstant::BATCH_EDIT_PRICE_TYPE_PLUS_PERCENTAGE;
                return round($originPrice + $originPrice * $editAmount/ 100, $round);
            case ProductConstant::BATCH_EDIT_PRICE_TYPE_SUBTRACT_PERCENTAGE:
                return round($originPrice - $originPrice * $editAmount/ 100, $round);
            default:
                return 0;
        }
    }

    /**
     * 计算包装体积
     * @param $l
     * @param $w
     * @param $h
     * @return float
     */
    public function calculPackageVolumn($l, $w, $h) {
        $l = !empty($l) ? $l : 0;
        $w = !empty($w) ? $w : 0;
        $h = !empty($h) ? $h : 0;
        return round($l * $w * $h / (100*100*100), 8);
    }

    protected function formatNumericSql($val){
        if(is_null($val) || (is_string($val) && trim($val) == '')){
            return 'null::numeric';
        }

        return $val;
    }

    protected function setCommonFieldVal($field, $value){
        if($this->isSpuDbField($field)){
            $fieldMetadata = $this->spuDbFieldMap[$field];

            if($fieldMetadata['type'] == 'numeric' && !empty($fieldMetadata['nullable']) && ($value === null || $value === '')){
                $this->spuSet[$field] = "{$field}= null::numeric";
            }else{
                $this->spuSet[$field] = "{$field}=:{$field}";
                $this->spuParams[":".$field] = $value;
            }
        }

        if($this->isSkuDbField($field)){
            $fieldMetadata = $this->skuDbFieldMap[$field];
            if($fieldMetadata['type'] == 'numeric' && !empty($fieldMetadata['nullable']) && ($value === null || $value === '')){
                $this->skuSet[$field] = "{$field}= null::numeric";
            }else{
                $this->skuSet[$field] = "{$field}=:{$field}";
                $this->skuParams[":".$field] = $value;
            }
        }
    }

    protected function collectBothSpuSkuField($spuField, $skuField, $val, $productIds=[]){
        $this->batchSetVal[$spuField] = $this->batchSetVal[$skuField] = $val;
    }
}