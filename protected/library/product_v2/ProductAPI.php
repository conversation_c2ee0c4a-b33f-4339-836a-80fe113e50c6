<?php


namespace common\library\product_v2;


use common\components\BaseObject;
use common\components\SerialNaturalSort;
use common\library\ai\classify\product\ProductApplyV2;
use common\library\APIConstant;
use common\library\CommandRunner;
use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldList;
use common\library\exchange_rate\ExchangeRateService;
use common\library\file\Helper;
use common\library\history\base\BatchHistory;
use common\library\history\base\History;
use common\library\history\base\HistoryFilter;
use common\library\history\product\ProductSetting;
use common\library\import\BatchImportRecord;
use common\library\import\Import;
use common\library\import\ImportConstants;
use common\library\import\ImportRecordApi;
use common\library\invoice\InvoiceProductRecordList;
use common\library\invoice\status\InvoiceStatusService;
use common\library\oms\warehouse\WarehouseFilter;
use common\library\platform_product\PlatformProductConstants;
use common\library\platform_product\sku\PlatformSkuAPI;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeFieldV2;
use common\library\privilege_v3\PrivilegeService;
use common\library\product_v2\formatter\ProductField;
use common\library\product_v2\group\ProductGroupApi;
use common\library\product_v2\relation\CombineProductRelationConstant;
use common\library\product_v2\relation\CombineProductRelationFilter;
use common\library\product_v2\search\ProductSearcher;
use common\library\product_v2\sku\BatchProductSku;
use common\library\product_v2\sku\ProductSku;
use common\library\product_v2\sku\ProductSkuFilter;
use common\library\product_v2\sku\ProductSkuMetadata;
use common\library\product_v2\sku\SkuAPI;
use common\library\serial\GenerateService;
use common\library\server\es_search\SearchQueueService;
use common\library\setting\library\group\GroupApi;
use common\models\client\ClientProduct;
use Constants;
use User;
use xiaoman\orm\database\data\DateRange;
use xiaoman\orm\database\data\GT;
use xiaoman\orm\database\data\NotEqual;
use xiaoman\orm\database\DBConstants;
use function DeepCopy\deep_copy;

class ProductAPI
{
    use SerialNaturalSort;

   protected $clientId;
   protected $userId;
   protected $errors=[];
   protected $errorMsg = '';

    /**
     * API constructor.
     * @param $clientId
     */
    public function __construct($clientId, $userId=0)
    {
        $this->clientId = $clientId;
        $this->userId = $userId;
    }

    /**
     * @param int|mixed $userId
     */
    public function setUserId( $userId)
    {
        $this->userId = $userId;
    }

    /**
     * @param array $params
     * @return ProductFilter
     */
    public function buildFilter(array $params, $mustEnable = true)
    {
        $productFilterBuilder = new ProductFilterBuilder($this->clientId, $this->userId);
        $productFilterBuilder->setSortFlag(false);
        $productFilterBuilder->setShowEnableFlag($mustEnable);
        $productFilterBuilder->setFilterByGroupFlag(false);
        $productFilterBuilder->setPaginateFlag(false);
        $productFilterBuilder->paramsMapping($params);
        $productFilter = $productFilterBuilder->getSpuFilter();
        return $productFilter;
    }

    public function spuMailList($params, $specify_lang){
        $productInfo = json_decode($params['product_info'], true);
        if(empty($params['product_info'])){
            throw new \RuntimeException(\Yii::t('product', '产品信息传参缺失'));
        }
        $groupFilter = new ProductSkuFilter($this->clientId);
        $spuFilter = new ProductFilter($this->clientId);
        $groupFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $spuFilter->select(['product_type']);
        $spuFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $groupFilter->select(['product_id', 'sku_num' => function ($column, $table, $quto) {
            return 'count(sku_id) as sku_num';
        }]);
        $productIds = array_keys($productInfo);
        $productMap = [];
        foreach($productIds as $productId){
            $productMap[$productId] = true;
        }
        $groupFilter->product_id = $productIds;
        $groupFilter = $groupFilter->initJoin();
        $groupFilter->innerJoin($spuFilter)->on('product_id', 'product_id');
        $groupFilter->joinGroupBy("product_id", $groupFilter->getTableName());
        $groupFilter->joinGroupBy("product_type", $spuFilter->getTableName());
        $groupInfo =  $groupFilter->rawData();

        if(empty($groupInfo)){
            return [];
        }

        $groupInfo = array_column($groupInfo, null, 'product_id');
        $targetSkuIds = [];
        $singleSkuProductIds = [];   // 单sku的多规格产品要展示产品规格
        foreach($productInfo as $productId => $skuIds){
            if(!isset($groupInfo[$productId])){
                continue;
            }
            $targetSkuNum = count($skuIds);
            $removeSpu = false;
            if($targetSkuNum < $groupInfo[$productId]['sku_num'] && $targetSkuNum < ProductConstant::MAIL_SAMESPU_SKU_MAX){
                $targetSkuIds = array_merge($targetSkuIds, $skuIds);
                if($targetSkuNum >0 ){
                    $removeSpu = true;
                }
            }
            if($groupInfo[$productId]['sku_num'] == 1 && $groupInfo[$productId]['product_type'] == ProductConstant::PRODUCT_TYPE_SKU){
                $singleSkuProductIds[] = $productId;
                $removeSpu = true;
            }
            if($removeSpu){
                unset($productMap[$productId]);
            }
        }

        if(!empty($singleSkuProductIds)){
            $skuFilter = new ProductSkuFilter($this->clientId);
            $skuFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
            $skuFilter->product_id = $singleSkuProductIds;
            $skuFilter->select(['sku_id']);
            $skus = $skuFilter->rawData();
            $targetSkuIds = array_merge($targetSkuIds, array_column($skus, 'sku_id'));
        }

        $skuList = [];
        if($targetSkuIds){
            $skuList = $this->skuMailList($targetSkuIds, $specify_lang, $params);
        }

        $productIds = array_keys($productMap);
        $list = [];
        if(!empty($productIds)){
            $productFilter = new ProductFilter($this->clientId);
            $productFilter->product_id = $productIds;
            /** @var BatchProduct $batchProduct */
            $batchProduct = $productFilter->getWithSkuBatch();      // 获取到一个 BatchProduct 的代理类
            $formatter =  $batchProduct->getFormatter();

            //控制字段权限
            $formatter->getFieldFormatter()->setFieldUserId($this->userId);
            $specify_lang && $formatter->getFieldFormatter()->setSpecifyLang($specify_lang);
            $formatter->flatFieldsListSetting();
            $list = $batchProduct->getListAttributes();
        }

        return array_merge($list, $skuList);
    }

    public function skuMailList($skuIds, $specify_lang, $params=[]){
        $skuApi = new SkuAPI($this->clientId, $this->userId);
        [$skuFilter, $productFilter] = $skuApi->buildFilter(["sku_id"=>$skuIds]);
        $skuFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $productFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $joinFilter = $skuApi->joinFilter($skuFilter, $productFilter,$params);
        $batchProductSku = $joinFilter->find();
        $skuFormatter =  $batchProductSku->getFormatter();

        $skuFormatter->displayWithoutZero(true);
        //控制字段权限
        $skuFormatter->getFieldFormatter()->setFieldUserId($this->userId);
        $specify_lang && $skuFormatter->getFieldFormatter()->setSpecifyLang($specify_lang);
        $formatAttributes = $params['format_attributes'] ?? false;
        $skuFormatter->flatFieldsListSetting($formatAttributes);
        return $batchProductSku->getListAttributes();
    }

    public function appSpuList(array $params, string $scene= APIConstant::SCENE_LIST, $specify_lang=null){
        if($scene == APIConstant::SCENE_MAIL){
            $list = $this->spuMailList($params, $specify_lang);
            foreach($list as &$product){
                $productFields = $product['flat_fields'];
                $productFields[] = ["id" => "product_id", "name" => "product_id", "type" => "1", "value" => $product['product_id'] ?? 0];
                $productFields[] = ["id" => "product_type", "name" => 'Product Type', "type" => "1", "value" => $product['product_type'] ?? 1];
                $product = $productFields;
            }
            return ['list' => $list, 'count' => count($list)];
        }

        $productFilterBuilder = new ProductFilterBuilder($this->clientId, $this->userId);
        $productFilterBuilder->paramsMapping($params);
        $filter = $productFilterBuilder->getSpuFilter();
        $idParams = $productFilterBuilder->getParamsByProductSkuIds($params);
        $spuCount = $filter->count();

        $list = [];
        if( $spuCount )
        {
            $filter->order($filter->getMetadata()::objectIdKey());
            $batchProduct =  $filter->find();
            $formatter =  $batchProduct->getFormatter();
            //控制字段权限
            $formatter->getFieldFormatter()->setFieldUserId($this->userId);
            $specify_lang && $formatter->getFieldFormatter()->setSpecifyLang($specify_lang);
            switch ($scene)
            {
                case APIConstant::SCENE_LIST:
                    $formatter->appSpuListSetting($idParams);
                    break;
                case APIConstant::SCENE_SELECT:
                    $formatter->appSelectSpuListSetting($idParams);
                    break;
            }
            $list = $batchProduct->getListAttributes();
        }

        foreach($list as &$product){
            $product['source_type'] = \common\library\product_v2\ProductHelper::getProductSourceTypeMap()[$product['source_type'] ?? ProductConstant::SOURCE_TYPE_XIAOMAN_USER]['key'];
            $product['product_type_name'] = \common\library\product_v2\ProductHelper::getProductTypeMap()[$product['product_type'] ?? ProductConstant::PRODUCT_TYPE_SPU]['key'];
            $product['_id'] = $product['product_id'];
            $product['product_disalbe_flag'] = $product['disalbe_flag'] ?? 0;
        }

        $data = ['list' => array_values($list), 'count' => $spuCount];

        return $data;
    }

    public function buildMatchFilter($matchModelParams){
        $productFilterBuilder = new ProductFilterBuilder($this->clientId, $this->userId);
        $productFilterBuilder->setPaginateFlag(false);
        $productFilterBuilder->setFilterByGroupFlag(false);
        $productFilterBuilder->setSortFlag(false);
        $productFilterBuilder->paramsMapping($matchModelParams);
        $matchFilter = $productFilterBuilder->getSpuFilter();
        $matchFilter->enable_sku_count = new GT(0);
        $matchFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        return $matchFilter;
    }

    public function webSpuList(array $params, string $scene= APIConstant::SCENE_LIST, $specify_lang=null){
        if($scene == APIConstant::SCENE_MAIL){
            $list = $this->spuMailList($params, $specify_lang);
            return ['list' => $list, 'count' => count($list)];
        }

        $productFilterBuilder = new ProductFilterBuilder($this->clientId, $this->userId);
        $productFilterBuilder->paramsMapping($params);
        $skuFilter = $productFilterBuilder->getSkuFilter();
        $filter = $productFilterBuilder->getSpuFilter();
        $idParams = $productFilterBuilder->getParamsByProductSkuIds($params);

        $match = [];
        if( $scene == APIConstant::SCENE_SELECT)
        {
            $matchFilter = $this->buildMatchFilter($idParams);
            $matchFilter->setDisableFlagBySetting($this->userId);
            $skuFilter->disable_flag = ProductConstant::DISABLE_FLAG_ON_SELL;
            $filter->enable_sku_count = new GT(0);
            $match = $this->initMatchInfo($params['product_model_keyword'] ?? []);
            $filter->setDisableFlagBySetting($this->userId);
        }else{
            $skuFilter->setEnableFlagBySetting($this->userId, "sku");
        }

        $cloneFilter = deep_copy($filter);
        $skuFilter->initJoin();
        $skuFilter->innerJoin($cloneFilter)
            ->on('product_id', 'product_id');
        if (!empty($params['has_parts'])) {
            $skuFilter->setHasParts($params['has_parts']);
        }
        if (!empty($params['parts_import_id'])) {
            $skuFilter->setPartsImportId($params['parts_import_id']);
        }
        $skuCount = $skuFilter->count();
        if (!empty($params['has_parts'])) {
            $cloneFilter->setHasParts($params['has_parts']);
            $filter->setHasParts($params['has_parts']);
        }
        if (!empty($params['parts_import_id'])) {
            $cloneFilter->setPartsImportId($params['parts_import_id']);
            $filter->setPartsImportId($params['parts_import_id']);
        }
        $spuCount = $cloneFilter->count();
        $skipPkSort = ($params['sort_field'] ?? '') == 'product_no';

        $list = [];
        if( $spuCount )
        {
            !$skipPkSort && $filter->rawOrder(function ($sortField) {
                return $sortField;
            }, $filter->getMetadata()::objectIdKey());   // 防止翻页重复
            $batchProduct =  $filter->find();
            $formatter =  $batchProduct->getFormatter();
            //控制字段权限
            $formatter->getFieldFormatter()->setFieldUserId($this->userId);
            $specify_lang && $formatter->getFieldFormatter()->setSpecifyLang($specify_lang);
            switch ($scene)
            {
                case APIConstant::SCENE_LIST:
                    $formatter->webSpuListSetting($idParams);
                    break;
                case APIConstant::SCENE_SELECT:
                    if(isset($matchFilter)){
                        $match = $this->matchInfo($matchFilter, $params);
                    }
                    $formatter->selectSpuListSetting($idParams);
                    break;
                case APIConstant::SCENE_EXPORT:
                    $formatter->exportSetting($idParams);
                    break;
                case APIConstant::SKU_ATTRIBUTE_LENGTH:
                    $formatter->exportSkuAttributes($idParams);
                    break;
            }
            $list = $batchProduct->getListAttributes();
        }

        // 临时逻辑，select场景下移除sku_items或sku_ids为空的spu
        $sku_num = 0;
        $isSelectScene = $scene == APIConstant::SCENE_SELECT;
        $list = array_filter($list, function($spu) use (&$sku_num, $isSelectScene){
            if($spu['product_type'] == ProductConstant::PRODUCT_TYPE_SKU){
                $sku_num += $spu['sku_num'] ?? 0;
                return !$isSelectScene || count($spu['sku_infos']) > 0;
            }else{
                $sku_num++;
                return true;
            }
        });

        $hasAnySku = $skuCount > 0 || (new SkuAPI($this->clientId))->hasAnySku(); // 无条件下的sku数量
        $data = ['list' => array_values($list), 'count' => $spuCount, 'match'=>$match, 'cur_sku_num'=>$sku_num, 'total_sku_num'=>$skuCount, 'has_any_sku'=>$hasAnySku];

        return $data;
    }

    public function webList(array $params, string $scene= APIConstant::SCENE_LIST, $specify_lang=null)
    {
        $productFilterBuilder = new ProductFilterBuilder($this->clientId, $this->userId);
        $productFilterBuilder->paramsMapping($params);
        $filter = $productFilterBuilder->getSpuFilter();
        $count = $filter->count();
        $list = [];
        if( $count )
        {
           $batchProduct =  $filter->find();
            $formatter =  $batchProduct->getFormatter();
            //控制字段权限
            $formatter->getFieldFormatter()->setFieldUserId($this->userId);
           $specify_lang && $formatter->getFieldFormatter()->setSpecifyLang($specify_lang);
           switch ($scene)
           {
               case APIConstant::SCENE_LIST:
                   $formatter->webListSetting();
                   break;
               case APIConstant::SCENE_SELECT:
                   $formatter->selectListSetting();
                   break;
               case APIConstant::SCENE_MAIL:
                   $formatter->flatFieldsListSetting();
                   break;
               case APIConstant::SCENE_IMPORT_IMAGE:
                   $formatter->importImageSetting();
                   break;
               case APIConstant::SCENE_OPEN_API:
                   $formatter->apiListSetting();
                   break;
           }
           $list = $batchProduct->getListAttributes();
        }

        return ['list' => $list, 'count' => $count];
    }

    public function invoiceProductSkuList($params){
        $listObj = $this->buildInvoiceProductRecordList($params);
        $tableAlias = 'invoice_product';
        $listObj->setAlias($tableAlias);
        $listObj->setGroupBy([$tableAlias.'.product_id', $tableAlias.'.sku_id']);
        $listObj->setOffset(($params['page']-1)*$params['page_size']);
        $listObj->setLimit($params['page_size']);
        $listObj->setOrderBy(['invoice_create_time','product_id']);
        $listObj->setOrder('desc');

        $aggregateFields = [
            "MAX({$tableAlias}.create_time) as invoice_create_time ",
        ];

        $listObj->formatter->setShowProductInfo(true);
        if( $params['select_model'] )
        {
            $listObj->formatter->setProductSelectModel(true);
        }else{
            $aggregateFields = array_merge($aggregateFields,[
                'COUNT( DISTINCT refer_id) AS invoice_count',
                "(array_agg(price_contract ORDER BY {$tableAlias}.create_time DESC))[1] AS invoice_price_contract",
                "(array_agg(unit ORDER BY {$tableAlias}.create_time DESC))[1] AS invoice_unit",
                "(array_agg(unit_price ORDER BY {$tableAlias}.create_time DESC))[1] AS invoice_unit_price",
                "(array_agg(currency ORDER BY {$tableAlias}.create_time DESC))[1] AS currency",
            ]);
        }

        $listObj->setOrder('invoice_count');

        $listObj->setAggregateFields($aggregateFields);
        return [
            'list' =>$listObj->find(),
            'count' =>$listObj->count(),
        ];
    }

    public function invoiceProductSpuList($params){
        $listObj = $this->buildInvoiceProductRecordList($params);
        $tableAlias = 'invoice_product';
        $listObj->setAlias($tableAlias);
        $listObj->setGroupBy([$tableAlias.'.product_id', $tableAlias.'.sku_id']);
        $listObj->setOrderBy(['invoice_create_time','product_id']);
        $listObj->setOrder('desc');

        $aggregateFields = [
            "MAX({$tableAlias}.create_time) as invoice_create_time ",
        ];

        $listObj->setOrder('invoice_count');
        $listObj->setAggregateFields($aggregateFields);
        $spuGroupdata = $listObj->rowData();

        if(!$spuGroupdata){
            return ['list' => [], 'count' => 0];
        }

        $spuSkuMap = [];
        foreach($spuGroupdata as $datum){
            $spuSkuMap[$datum['product_id']][] = $datum['sku_id'];
        }

        $spuParams = [
            'cur_page' => $params['page'],
            'page_size' => $params['page_size'],
            'product_id' => array_keys($spuSkuMap),
            'sort_field' => $params['sort_field'],
            'sort_type' => $params['sort_type'],
            'mode' => $params['mode'],
            'pin' => $params['pin'] ?? 0,
        ];

        $productFilterBuilder = new ProductFilterBuilder($this->clientId, $this->userId);
        $productFilterBuilder->paramsMapping($spuParams);
        $filter = $productFilterBuilder->getSpuFilter();
        if(empty($params['sort_field'])){
            $productIdClause = implode(',', $spuParams['product_id']);
            $filter->rawOrder(function() use ($productIdClause){
                return " position(product_id::text in '{$productIdClause}') ";
            });
        }

        $match = [];
        if( $params['select_model'])   // select场景
        {
            $matchModelParams = $productFilterBuilder->getParamsByProductSkuIds($params);
            $matchFilter = $this->buildMatchFilter($matchModelParams);
            $match = $this->initMatchInfo($params['product_model_keyword'] ?? []);
        }

        $count = $filter->count();
        $list = [];
        if( $count )
        {
            $res = $filter->rawData();
            $batchProduct = new BatchProduct($this->clientId);
            $batchProduct->initFromData($res);
            $spuParams['sku_id'] = [];
            foreach($res as $datum){
                $spuParams['sku_id'] = array_merge($spuParams['sku_id'], $spuSkuMap[$datum['product_id']]);
            }
            $formatter =  $batchProduct->getFormatter();
            //控制字段权限
            $formatter->getFieldFormatter()->setFieldUserId($this->userId);
            if($params['select_model']) {
                if (isset($matchFilter)) {
                    $match = $this->matchInfo($matchFilter, $params);
                }
                $formatter->selectSpuListSetting($spuParams);
            }else{
                $formatter->webSpuListSetting($spuParams);
            }
            $list = $batchProduct->getListAttributes();
        }

        // 临时逻辑，移除sku_items或sku_ids为空的spu
        $sku_num = 0;
        $isSelectScene = $params['select_model'] ?? 1;
        $list = array_filter($list, function($spu) use (&$sku_num, $isSelectScene){
            if($spu['product_type'] == ProductConstant::PRODUCT_TYPE_SKU){
                $sku_num += $spu['sku_num'] ?? 0;
                return !$isSelectScene || count($spu['sku_infos']) > 0;
            }else{
                $sku_num++;
                return true;
            }
        });

        $data = ['list' => $list, 'count' => $count, 'match'=>$match, 'cur_sku_num'=>$sku_num];
        return $data;
    }
    public function buildInvoiceProductRecordList($params){
        $listObj = new InvoiceProductRecordList($this->userId);
        $listObj->setClientId($this->clientId);
        $listObj->setType($params['type']);
        $listObj->setProductSkuEnable(\common\components\BaseObject::ENABLE_FLAG_TRUE);
        if( $params['user_id'] ){
            $listObj->setUserId($params['user_id']);
        }
        if( $params['company_id'] ){
            $listObj->setCompanyId($params['company_id']);
        }

        if ($params['opportunity_id']) {
            $listObj->setOpportunityId($params['opportunity_id']);
        }

        if( $params['performance_flag'] ){
            $statusList =  (new InvoiceStatusService($this->clientId, \Constants::TYPE_ORDER))->list(true);
            $statusIds = array_column($statusList,'id');
            $listObj->setStatus($statusIds);
        }

        $category_ids =  is_null($params['category_ids']) ? $params['category_ids'] : array_filter(explode(',', $params['category_ids']));
        if ($category_ids) {
            $listObj->setProductCategoryIds($category_ids);
        }

        if ($params['group_id']) {
            $listObj->setProductGroupId($params['group_id']);
        }

        if (!empty($params['product_type'])){
            $listObj->setProductType($params['product_type']);
        }

        if (!empty($params['select_model']) && $params['select_model'] == 1){
            $listObj->setProductSkuDisableFlag(BaseObject::ENABLE_FLAG_FALSE);
        }

        if( $params['create_time_start'] )
            $listObj->setSkuStartCreateTime($params['create_time_start']);
        if( $params['create_time_end'] )
            $listObj->setSkuEndCreateTime($params['create_time_end']);
        if( $params['update_time_start'] )
            $listObj->setSkuStartUpdateTime($params['update_time_start']);
        if( $params['update_time_end'] )
            $listObj->setSkuEndUpdateTime($params['update_time_end']);

        if ($params['keyword'] || $params['product_no_keyword'] || $params['product_model_keyword'])
        {
            $productIds = $this->search(['keyword' => $params['keyword'], 'product_no_keyword' => $params['product_no_keyword'], 'product_model_keyword' => $params['product_model_keyword']]);
            if( empty($productIds) )
            {
                $listObj->setAlwaysEmpty();
            }else{
                $listObj->setProductId($productIds);
            }
        }

        return $listObj;
    }

    protected function matchInfo(ProductFilter $productFilter, $params)
    {
        if (empty($params['product_model_keyword'])) {
            return [];
        }
        $models = explode(',', $params['product_model_keyword']);

        $productFilter->model = $models;
        $productFilter->select([
            'model' => function ($column, $table, $quto) {
                return 'distinct model';
            }
        ]);
        $productFilter->limit(9999);
        $hitModels = $productFilter->rawData();
        $hit = array_column($hitModels, 'model');
        $miss = array_diff($models, $hit);
        return [
            'hit' => [
                'total' => count($hit),
                'list' => $hit
            ],
            'miss' => [
                'total' => count($miss),
                'list' => array_values($miss)
            ]
        ];
    }

    public function initMatchInfo($models){
        if(empty($models)){
            return [];
        }
        if(!is_array($models)){
            $models = explode(',', $models);
        }
        return [
            'hit' => [
                'total' => 0,
                'list' => []
            ],
            'miss' => [
                'total' => count($models),
                'list' => array_values($models)
            ]
        ];
    }

    /**
     * @param array $params
     * @return array|mixed
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function exportToMktList(array $params)
    {
        $filter = $this->buildFilter($params);
        $batchProduct = $filter->find();

        $batchProduct->getFormatter()->exportToMktSetting();

        return $batchProduct->getListAttributes();
    }

    // spu字段迁移sku后，该方法作为兼容版本，以后会逐渐弃用
    public function productInfo($productId, array $groups = [], $specify_lang = null, $scene = '', $productNo = '', $skuLimit = null, $subLimit=null)
    {
        $product = new Product($this->clientId, $productId);
        $productNo && $product->loadByProductNo($productNo);
        $product->checkDelete();
        $product->checkGroupPrivilege($this->userId);
        $product->setDomainHandler(\User::getUserObject($this->userId, $this->clientId));
        $formatter =  $product->getFormatter();
        //控制字段权限
        $formatter->getFieldFormatter()->setFieldUserId($this->userId);

        $specify_lang && $formatter->getFieldFormatter()->setSpecifyLang($specify_lang);

        switch ($scene)
        {
            case APIConstant::SCENE_EDIT:
                $formatter->detailInfoSetting($groups);
                $formatter->fileListSetting(null, null);
                $formatter->displaySupplierProductList(true);
                break;
            case APIConstant::SCENE_APP:
                $formatter->appDetailInfoSetting($groups, $skuLimit, $subLimit);
                break;
            case APIConstant::SCENE_APP_PB:
                $formatter->pbDetailInfoSetting($groups);
                break;
            case APIConstant::SCENE_OPEN_API:
                $formatter->apiDetailInfoSetting($groups);
                break;
            default:
                $formatter->detailInfoSetting($groups);
        }
        return $product->getAttributes();
    }

    // spu字段迁移sku后，产品详情接口的更新版本，暂时只有web端使用，其余场景仍然走 productInfo() 方法
    public function productWebInfo($productId, $scene = '', $skip_view_privilege = false)
    {
        $product = new Product($this->clientId, $productId);
        // 报表查询不检测删除情况，https://www.tapd.cn/21404721/bugtrace/bugs/view?bug_id=1121404721001107070
        if ($scene != 'report') {
            $product->checkDelete();
        }
        $product->checkProductViewPrivilege($this->userId);
        $product->checkGroupPrivilege($this->userId);
        $product->setDomainHandler(\User::getUserObject($this->userId, $this->clientId));
        $formatter =  $product->getFormatter();

        switch ($scene)
        {
            case APIConstant::SCENE_EDIT:
                $formatter->detailWebInfoSetting($skip_view_privilege);
                $formatter->fileListSetting(null, null);
                $formatter->displaySupplierProductList(true);
                $formatter->displayProductInventoryWarningList(true);
                break;
            default:
                $formatter->detailWebInfoSetting($skip_view_privilege);
        }
        return $product->getAttributes();
    }

    public function recycleInfo($productId, $deleteSkuIds)
    {
        $product = new Product($this->clientId, $productId);
        $product->setDomainHandler(\User::getUserObject($this->userId, $this->clientId));
        $product->getFormatter()->detailWebInfoSetting();
        $product->getFormatter()->fileListSetting(null, null);
        $data = $product->getAttributes();
        if( !empty($deleteSkuIds) )
        {
            $skuApi = new SkuAPI($this->clientId, $this->userId);
            $data['sku_items'] =  $skuApi->deleteItems($deleteSkuIds);
        }

        return $data;
    }

    public function skuInfos($params)
    {
        $skuFilter = new ProductSkuFilter($this->clientId);
        if(in_array($params['scene'], [APIConstant::SCENE_SELECT, APIConstant::SCENE_APP])){
            $skuFilter->enable_flag = ProductConstant::ENABLE_FLAG_TRUE;
        }
        if(!empty($params['product_id']) && !empty($params['sku_id']) && strtolower(trim($params['relation']))=='or'){
            $skuIdStr = "(".implode(',', $params['sku_id']) . ")";
            $platformProductIdStr = "(". implode(',', $params['product_id']) .")";
            $skuFilter->rawWhere("and (product_id in {$platformProductIdStr} or sku_id in {$skuIdStr})");
        }else{
            !empty($params['product_id']) && $skuFilter->product_id = $params['product_id'];
            !empty($params['sku_id']) && $skuFilter->sku_id = $params['sku_id'];
        }
        $skuFilter->order('product_id');
        $skuFilter->order('disable_flag');
        $skuFilter->order('display_order');

        !empty($params['cur_page']) && !empty($params['page_size']) && $skuFilter->limit( $params['page_size'], $params['cur_page']);
        !empty($params['page']) && !empty($params['page_size']) && $skuFilter->limit( $params['page_size'], $params['page']);

        $count = $skuFilter->count();
        $batchSku = $skuFilter->find();
        $scene = $params['scene'] ?? APIConstant::SCENE_LIST;
        switch($params['scene']){
            case APIConstant::SCENE_LIST:
                $batchSku->getFormatter()->skuInfoListSetting();
                break;
            case APIConstant::SCENE_APP:
                $batchSku->getFormatter()->appSkuInfoListSetting();
            break;
            default:
                $batchSku->getFormatter()->skuInfoListSetting();
        }

        $data = $batchSku->getListAttributes();
        return [$data, $count];
    }

    public function skuItems($productId, $skuId=0)
    {
        if( !$productId && $skuId )
        {
            $productId = (new ProductSku($this->clientId, $skuId))->product_id;
        }

        $product = new Product($this->clientId, $productId);
        if( $product->isNew() )
        {
            throw new \RuntimeException(\Yii::t('invoice', '产品不存在或已删除'));
        }
        $product->setDomainHandler(\User::getUserObject($this->userId, $this->clientId));
        $product->getFormatter()->skuItemsInfoSetting();
        $product->getFormatter()->setFormatterV2Privilege(true, true, PrivilegeFieldV2::SCENE_OF_VIEW);
        $data = $product->getAttributes();
        return $data;
    }

    public function items(array $productIds, string $scene= APIConstant::SCENE_LIST, array $fields=[])
    {
        $productIds = array_filter($productIds);
        if(empty($productIds))
            return [];

        $filter = new ProductFilter($this->clientId);
        $filter->product_id = $productIds;
        //指定查询字段,直接返回
        if( !empty($fields) )
        {
            $filter->select($fields);
            $batchProduct=$filter->find();
            return $batchProduct->getListAttributes($fields);
        }
        $batchProduct=$filter->find();

        switch ($scene)
        {
            case APIConstant::SCENE_LIST:
                $batchProduct->getFormatter()->baseInfoSetting();
                break;
            case APIConstant::SCENE_SELECT:
                $batchProduct->getFormatter()->selectListSetting();
                break;
            case APIConstant::SCENE_COMPANY_FOLLOW:
                $batchProduct->getFormatter()->companyFollowSetting();
                break;
            case APIConstant::SCENE_COMPANY_HISTORY:
                $batchProduct->getFormatter()->companyHistoryListSetting();
                break;
            case APIConstant::SCENE_OPEN_API_INVOICE:
                $batchProduct->getFormatter()->openApiInvoiceSetting();
                break;
            case APIConstant::SCENE_ALI_PRODUCT_SYNC:
                $batchProduct->getFormatter()->aliSyncSetting();
                break;
        }
        return $batchProduct->getListAttributes();
    }

    public function deleteItems(array $productIds, string $scene= APIConstant::SCENE_RECYCLE)
    {
        $productIds = array_filter($productIds);
        if(empty($productIds))
            return [];

        $filter = new ProductFilter($this->clientId);
        $filter->product_id = $productIds;
        $filter->enable_flag = \Constants::ENABLE_FLAG_FALSE;
        $batchProduct=$filter->find();
        switch ($scene)
        {
            case APIConstant::SCENE_RECYCLE:
                $batchProduct->getFormatter()->recycleListSetting();
                break;
        }
        return $batchProduct->getListAttributes();
    }

    public function validFormData(array $formData, array $formSkuList=[], array $ignoreVerifyFields = [], array $formSubProductList=[])
    {
        $fieldFormatter = new ProductField($this->clientId);
        $data = $fieldFormatter->unpackFormData($formData, ProductField::CREATE_IGNORE_FIELDS);
        if(!empty($formSkuList)){
            $ignoreVerifyFields = array_merge($ignoreVerifyFields, $fieldFormatter->getSkuExternalFields());
        }
        $ignoreVerifyFields = array_merge($ignoreVerifyFields,ProductField::$productSizeFields);
        $fieldFormatter->validateData($data, true, $ignoreVerifyFields);
        $skuList = $fieldFormatter->unpackSkuListFormData($formSkuList);
        $fieldFormatter->validateSkuData($skuList);
        $subProductList = $fieldFormatter->unpackSubProductListFormData($formSubProductList);
        return [$data, $skuList, $subProductList];
    }

    public function create(array $formData,array $formSkuData=[],$validFormData=true, array $formSubProductList=[]):Product
    {
        \common\library\privilege_v3\Helper::checkPermission($this->clientId, $this->userId, PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_CREATE);
        list($data, $skuList, $subProductList) = $validFormData ? $this->validFormData($formData, $formSkuData, [], $formSubProductList) : [$formData, $formSkuData, $formSubProductList];
        $product = new Product($this->clientId);
        $product->setDomainHandler(\User::getUserObject($this->userId));
        $product->bindAttrbuties($data);
        $product->setSkuList($skuList);
        $product->setPartsList($formData['parts_list'] ?? []); // 设置无规格产品的配件清单
        $product->setSubProductList($subProductList);
        $product->create();
        return $product;
    }

    /**
     * @param $platformSkuId
     * @param array $formData
     * @param array $formSkuData
     * @param int $orderId
     * @return array|string|null
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function createFromPlatformProduct($platformSkuId, array $formData, array $formSkuData = [], $orderId = 0)
    {
        \common\library\privilege_v3\Helper::checkPermission($this->clientId, $this->userId, PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_CREATE);
        [$data, $skuList] = $this->validFormData($formData, $formSkuData);

        $thirdApi = new PlatformSkuAPI($this->clientId, $this->userId);
        $sku = $thirdApi->skus(['platform_sku_id' => $platformSkuId], ['third_product_id', 'third_store_id', 'platform_product_id']);
        if (empty($sku)) {
            throw new \RuntimeException(\Yii::t('platform_product', 'Product sku not exists'));
        }
        $sku = $sku[0];

        $data['source_type'] = ProductConstant::SOURCE_TYPE_FROM_PLATFORM;
        $data['ali_store_id'] = $sku['third_store_id'];
        $data['ali_product_id'] = $sku['third_product_id'];

        $product = new Product($this->clientId);
        $product->setDomainHandler(\User::getUserObject($this->userId));
        $product->bindAttrbuties($data);
        $product->setSkuList($skuList);
        $product->create();

        $product->getFormatter()->baseInfoSetting();
        $data = $product->getAttributes();

        if (!empty($data['sku_items'])) {
            $thirdApi->matchAfterCreateLocal($sku['platform_product_id'], $data['sku_items']);

            if ($orderId) {
                $relationList = $thirdApi->getRelationByPlatformSkuId($platformSkuId);
                $relationList = array_column($relationList, null, 'platform_sku_id');
                // 订单详情|已销订购产品列表中，处理平台产品生成本地产品时匹配关系
                if (!empty($relationList[$platformSkuId])) {
                    $thirdApi->changeMatchForOrder($orderId, $product->product_id, $relationList[$platformSkuId]['sku_id'], $relationList[$platformSkuId]['platform_product_id'], $platformSkuId);
                    $thirdApi->batchMatchForDraftOrder($relationList[$platformSkuId]['platform_product_id'], $platformSkuId, $product->product_id, $relationList[$platformSkuId]['sku_id']);
                }
            }
        }

        return $data;
    }

    public function edit($productId, array $formData,array $formSkuData=[],$validFormData=true, array $formSubProductList=[]):Product
    {
        $product = new Product($this->clientId, $productId);
        if( !$product->isExist() )
        {
            throw new \RuntimeException(\Yii::t('invoice', '产品不存在或已删除'));
        }
        $ignoreVerifyFields = [];
        //编辑场景下，如未改动产品编号则不校验产品编号（因为存在不符合新规则的旧产品编号数据）
        //绑定数据是后置处理 这里直接取data数据 如果data没传 默认不校验 防止出现编辑情况非编辑字段异常
        if (($formData['product_no'] ?? $product->getOldAttributes(['product_no'])['product_no']) == $product->getOldAttributes(['product_no'])['product_no']) {
            $ignoreVerifyFields[] = 'product_no';
        }
        if (isset($formData['product_type']) && $product->getOldAttributes(['product_type'])['product_type'] != $formData['product_type']) {
            throw new \RuntimeException(\Yii::t('product', '产品类型不可变更'));
        }
        $ignoreVerifyFields = array_merge(ProductField::$productSizeFields, $ignoreVerifyFields);
        list($data, $skuList, $subProductList) = $validFormData ? $this->validFormData($formData, $formSkuData, $ignoreVerifyFields, $formSubProductList) : [$formData, $formSkuData, $formSubProductList];
        $this->userId && $product->setDomainHandler(\User::getUserObject($this->userId));
        $product->bindAttrbuties($data);
        $product->setSkuList($skuList);
        $product->setPartsList($formData['parts_list'] ?? []); // 设置无规格产品的配件清单
        $product->setSubProductList($subProductList);
        $product->update();
        return $product;
    }

    public function delete(array $params)
    {
        list($targetIds, $discardIds) = (new ProductAPI($this->clientId))->getProductIdWithoutSubProduct($params);
        if (count($discardIds) != 0) {
            $this->errors['combine_product_num'] = count($discardIds);
            $this->errorMsg .= count($params['product_id']) == 1 ? \Yii::t('product', 'This is sub of combination product, it is not allowed to delete.') : count($discardIds) . \Yii::t('product', ' of product selected is sub of combination product, it is not allowed to delete.');
        }
        if(count($targetIds) == 0){
            return 0;
        }
        $params = ['product_id'=>$targetIds];
        $filter = $this->buildFilter($params);
        $batch = $filter->find();
        $this->userId && $batch->setDomainHandler(\User::getUserObject($this->userId));
        $count = $batch->getOperator()->delete();
        return $count;
    }

    public function move(array $params, $groupId)
    {
        $filter = $this->buildFilter($params);
        $batch = $filter->find();
        $this->userId &&  $batch->setDomainHandler(\User::getUserObject($this->userId));
        $count = $batch->getOperator()->move($groupId);
        return $count;
    }

    public function disable(array $params, $flag)
    {
        if($flag == ProductConstant::DISABLE_FLAG_HALT_SALE){
            list($targetIds, $discardIds) = (new ProductAPI($this->clientId))->getProductIdWithoutSubProduct($params);
            if (count($discardIds) != 0) {
                $this->errors['combine_product_num'] = count($discardIds);
                $this->errorMsg .= (!empty($params['product_id']) && count($params['product_id']) == 1) ? \Yii::t('product', 'This is sub of combination product, it is not allowed to disable.') : count($discardIds) . \Yii::t('product', ' of product selected is sub of combination product, it is not allowed to disable.');
            }
            if(count($targetIds) == 0){
                return 0;
            }
            $params = ['product_id'=>$targetIds];
        }

        $filter = $this->buildFilter($params);
        $batch = $filter->find();
        $this->userId && $batch->setDomainHandler(\User::getUserObject($this->userId));
        $count = $batch->getOperator()->disable($flag);
        return $count;
    }

    public function recover(array $productIds, array $skuMap)
    {
        if (empty($productIds) )
            return [];

        foreach ($skuMap as $k => $item)
        {
            $item = is_array($item)?$item:(json_decode($item, true)?:[]);
            $skuMap[$k] = $item;
        }

        $db = \PgActiveRecord::getDbByClientId($this->clientId);
        $sql = "select product_id,product_no,enable_flag from tbl_product where client_id =:client_id and product_no != ''".
            " and  product_no in (select product_no from tbl_product where client_id =:client_id and product_id in (".implode(',', $productIds)."))";
        $list = $db->createCommand($sql)->queryAll(true,[':client_id' => $this->clientId]);
        $hasExistProducts = array_sum(array_column($list,'enable_flag')) > 0;       // 要恢复的产品的编号在已存在的产品中有的情况
        if($hasExistProducts){
            throw new \RuntimeException(\Yii::t('product', 'Product number existed, recovery is not supported'));
        }

        $repeatRecoverProductNoMap = [];
        $list = array_column($list, null, 'product_id');
        foreach($productIds as $productIdToRevocer){
            if(!isset($list[$productIdToRevocer])){
                continue;
            }
            $produtNoToRevocer = $list[$productIdToRevocer]['product_no'];
            if(!isset($repeatRecoverProductNoMap[$produtNoToRevocer])){
                $repeatRecoverProductNoMap[$produtNoToRevocer] = 0;
            }
            $repeatRecoverProductNoMap[$produtNoToRevocer] += 1;
            if($repeatRecoverProductNoMap[$produtNoToRevocer] > 1){
                throw new \RuntimeException(\Yii::t('product', 'Recovery is not supported because of recovering product number repeat'));
            }
        }

        $noRecoverNo = [];
        $recoverIds = [];
        $map = [];
        foreach ($list as $item)
        {
            $map[$item['product_id']] = $item['product_no'];
            $item['enable_flag'] == 1 && $noRecoverNo[] = $item['product_no'];
        }

        foreach ($productIds as $productId)
        {
            if( isset($map[$productId]) && in_array($map[$productId], $noRecoverNo))
                continue;
            $recoverIds[] = $productId;
        }

        if (!empty($recoverIds))
        {
            $filter = new ProductFilter($this->clientId);
            $filter->product_id = $recoverIds;
            $batch = $filter->find();
            $this->userId && $batch->setDomainHandler(\User::getUserObject($this->userId));
            $batch->getOperator()->recover($skuMap);
        }

        return  $recoverIds;
    }

    public function fileList($productId, $curPage=1, $pageSize=20)
    {
        $product = new Product($this->clientId, $productId);
        $product->getFormatter()->fileListSetting($curPage, $pageSize);
        return $product->getAttributes();
    }

    public function addFile($productId, array $fileIds)
    {
        if(empty($fileIds))
            return;

        $product = new Product($this->clientId, $productId);
        if( $product->isNew() )
        {
            throw new \RuntimeException(\Yii::t('product', 'Please select a product'));
        }
        $product->setDomainHandler(\User::getUserObject($this->userId, $this->clientId));
        $product->getOperator()->addFile($fileIds);
    }

    public function removeFile($productId, array $fileIds)
    {
        if(empty($fileIds))
            return;

        $product = new Product($this->clientId, $productId);
        if( $product->isNew() )
        {
            throw new \RuntimeException(\Yii::t('product', 'Please select a product'));
        }
        $this->userId && $product->setDomainHandler(\User::getUserObject($this->userId, $this->clientId));
        $product->getOperator()->removeFile($fileIds);
    }

    public function setImages($productId, array $images)
    {
        $product = new Product($this->clientId, $productId);
        if( $product->isNew() )
        {
            throw new \RuntimeException(\Yii::t('product', 'Please select a product'));
        }
        $this->userId && $product->setDomainHandler(\User::getUserObject($this->userId, $this->clientId));
        $product->getOperator()->setImages($images);
    }

    public function existField($field,$value, $excludeProductId=0)
    {
        if( empty($value) )
            return false;

        return ProductHelper::existField($this->clientId, $field, $value, $excludeProductId);
    }

    public function count(array $params)
    {
        $filter = $this->buildFilter($params);
        return $filter->count();
    }

    public function search(array $params)
    {
        $params = array_filter($params,function ($item)
        {
            $item = trim($item);
            return $item !== '';
        });

        if( empty($params) )
            return [];

        $filter = new ProductSearcher($this->clientId);
        return $filter->paramsMapping($params)->findIds();
    }

    public function groupCountMap(array $groupIds, $module = \Constants::TYPE_PRODUCT, $searchParams=[])
    {
        $productFilterBuilder = new ProductFilterBuilder($this->clientId, $this->userId);
        $productFilterBuilder->paramsMapping($searchParams);
        $productFilter = $productFilterBuilder->getSpuFilter();

        $productFilter->group_id = $groupIds;
        if($module){
            $productFilter->module = $module;
        }
        $productFilter->enable_flag =1;
        $countCol = [
            'count',
            function ($column, $table, $systemQuotes) {
                return 'count(1) as count';
            }
        ];
        $data = $productFilter->aggregation(['group_id', $countCol], ['group_id'])->getAttributes(['group_id', 'count']);

        return array_column($data,'count', 'group_id');
    }

    public function subProductList($params){
        $joinFilter = $this->buildCombineJoinFilter($params, ProductConstant::SHOW_RELATION_PRODUCT_TYPE_SUB);
        $count = $joinFilter->count();
        if($count == 0){
            return ['list'=>[], 'count'=>0];
        }

        $rawData = $joinFilter->rawData();

        $batchProductSku = new BatchProductSku($this->clientId);
        $batchProductSku->initFromData($rawData);
        $formatter =  $batchProductSku->getFormatter();

        switch($params['scene']){
            case CombineProductRelationConstant::SHOW_SCENE_SIMPLE:
                $formatter->subProductBaseListSetting();
            break;
            case CombineProductRelationConstant::SHOW_SCENE_DETAIL:
                $formatter->subProductSelectListSetting();
            break;
        }
        $list = $batchProductSku->getListAttributes();
        return ['list' => $list, 'count' => $count];
    }

    public function combineProductList($params){
        $joinFilter = $this->buildCombineJoinFilter($params, ProductConstant::SHOW_RELATION_PRODUCT_TYPE_COMBINE);
        $count = $joinFilter->count();
        if($count == 0){
            return ['list'=>[], 'count'=>0];
        }

        $rawData = $joinFilter->rawData();

        $batchProductSku = new BatchProductSku($this->clientId);
        $batchProductSku->initFromData($rawData);
        $formatter =  $batchProductSku->getFormatter();
        $formatter->combineProductBaseListSetting();
        $list = $batchProductSku->getListAttributes();
        return ['list' => $list, 'count' => $count];
    }

    public function invoiceSubProductList($invoice_id, $recycle_model)
    {
        $invoiceObj = new InvoiceProductRecordList($this->userId);
        $invoiceObj->setInvoiceProductIds($invoice_id);
        if (!$recycle_model) {
            $invoiceObj->setEnableFlag(BaseObject::ENABLE_FLAG_TRUE);
        } else {
            $invoiceObj->setEnableFlag(BaseObject::ENABLE_FLAG_FALSE);
        }
        $invoiceObj->setFields(['id', 'combine_product_config']);
        $invoiceInfo = $invoiceObj->find();
        $subProduct = array_column($invoiceInfo, 'combine_product_config')[0] ?? 0;

        if (!$subProduct) {
            return ['list' => [], 'count' => 0];
        }

        $subProductArr = json_decode($subProduct,true);
        if (!$subProductArr) {
            return ['list' => [], 'count' => 0];
        }
        $combineSubProductMap = array_column($subProductArr, null, 'sub_sku_id');
        $subProductSku = array_column($subProductArr, 'sub_sku_id');

        $scene = CombineProductRelationConstant::SHOW_SCENE_SIMPLE;
        $productFilter = new ProductFilter($this->clientId);
        $productFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
        $productSkuFilter = new ProductSkuFilter($this->clientId);
        $productSkuFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
        $productSkuFilter->sku_id = $subProductSku;

        empty($productFields) && $productFields = CombineProductRelationConstant::getProductFields($scene)?:$productFilter->getSelectFields();
        empty($productSkuFields) && $productSkuFields = CombineProductRelationConstant::getProductSkuFields($scene)?:$productSkuFilter->getSelectFields();
        $productFilter->select($productFields);
        $productSkuFilter->select($productSkuFields);
        $joinFilter = $productFilter->initJoin();

        $joinFilter = $joinFilter->innerJoin($productSkuFilter)->on('product_id', 'product_id');

        $subProductArr = $joinFilter->rawData();

        array_walk($subProductArr, function (&$val) use ($combineSubProductMap) {
            $addVal = [
                'combine_product_id' => $combineSubProductMap[$val['sku_id']]['combine_product_id'] ?? 0,
                'combine_sku_id' => $combineSubProductMap[$val['sku_id']]['combine_sku_id'] ?? 0,
                'count' => $combineSubProductMap[$val['sku_id']]['count'] ?? 0,
                'relation_id' => $combineSubProductMap[$val['sku_id']]['relation_id'] ?? 0,
            ];
            $val = array_merge($val, $addVal);
        });

        //展示排序
        array_multisort(array_column($subProductArr, 'relation_id'), SORT_ASC, $subProductArr);
        $batchProductSku = new BatchProductSku($this->clientId);
        $batchProductSku->initFromData($subProductArr);
        $formatter =  $batchProductSku->getFormatter();
        $formatter->subProductBaseListSetting();
        $list = $batchProductSku->getListAttributes();
        $count = $joinFilter->count();
        return ['list' => $list, 'count' => $count];
    }

    public function buildCommbineFilter($params, $enable_flag = true){
        $relationFilter = new CombineProductRelationFilter($this->clientId);
        if($enable_flag){
            $relationFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
        }
        !empty($params['combine_product_id']) && $relationFilter->combine_product_id = $params['combine_product_id'];
        !empty($params['combine_sku_id']) && $relationFilter->combine_sku_id = $params['combine_sku_id'];
        !empty($params['sub_product_id']) && $relationFilter->sub_product_id = $params['sub_product_id'];
        !empty($params['sub_sku_id']) && $relationFilter->sub_sku_id = $params['sub_sku_id'];
        return $relationFilter;
    }

    public function buildCombineJoinFilter($params, $type = ProductConstant::SHOW_RELATION_PRODUCT_TYPE_SUB, $productFields=[], $productSkuFields=[], $combine_enable_flag=true){
        $relationFilter = $this->buildCommbineFilter($params, $combine_enable_flag);
        $scene = $params['scene'] ?? CombineProductRelationConstant::SHOW_SCENE_SIMPLE;
        $productFilter = new ProductFilter($this->clientId);
        $productFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
        $productSkuFilter = new ProductSkuFilter($this->clientId);
        $productSkuFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;

        empty($productFields) && $productFields = CombineProductRelationConstant::getProductFields($scene)?:$productFilter->getSelectFields();
        empty($productSkuFields) && $productSkuFields = CombineProductRelationConstant::getProductSkuFields($scene)?:$productSkuFilter->getSelectFields();
        $productFilter->select($productFields);
        $productSkuFilter->select($productSkuFields);
        $relationFilter->select($relationFilter->getSelectFields());
        $joinFilter = $productFilter->initJoin();
        !empty($params['cur_page']) && !empty($params['page_size']) && $joinFilter->joinlimit( $params['page_size'], ($params['cur_page'] -1) * $params['page_size']);

        if(!isset(ProductConstant::COMBINE_RELATION_FIELD[$type])){
            return;
        }
        list($productRelationField, $skuRelationField) = ProductConstant::COMBINE_RELATION_FIELD[$type];
        $joinFilter = $joinFilter->innerJoin($relationFilter)->on($productFilter->getTableName(), 'product_id', $relationFilter->getTableName(), $productRelationField);
        $joinFilter = $joinFilter->innerJoin($productSkuFilter)->on($relationFilter->getTableName(), $skuRelationField, $productSkuFilter->getTableName(), 'sku_id');
        $joinFilter = $joinFilter->joinOrder('relation_id', 'asc', $relationFilter->getTableName());

        return $joinFilter;
    }

    /**
     * 组合产品成本价计算
     * @param $productList 产品列表
     * @param $baseCurrency 目标币种
     * @param $productIdKey 产品ID键名
     * @param $skuIdKey SKU_ID键名
     * @param $countKey 产品数量键名
     * @return float|int
     */
    public function calculCombineProductCostBySub($productList, $baseCurrency, $productIdKey = 'sub_product_id', $skuIdKey = 'sub_sku_id', $countKey='count'){
//$exchangeService = new ExchangeRateService($this->clientId);
//$exchangeService->setMainCurrency($baseCurrency);
//$exchangeRates = $exchangeService->getFormatRates();
//die;
        $subProductIds = array_unique(array_column($productList, $productIdKey));
        $productFilter = new ProductFilter($this->clientId);
        $productFilter->product_id = $subProductIds;
        $productFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
        $productFilter->select(['product_id']);
        $productMap = $productFilter->rawData();
        $productMap = array_column($productMap, null, 'product_id');

        $subSkuIds = array_unique(array_column($productList, $skuIdKey));
        $skuFilter = new ProductSkuFilter($this->clientId);
        $skuFilter->sku_id = $subSkuIds;
        $skuFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
        $skuFilter->select(['sku_id', 'cost', 'cost_currency']);
        $costMap = $skuFilter->rawData();
        $costMap = array_column($costMap, null, 'sku_id');

        $totalCost = 0;
        $exchangeService = new ExchangeRateService($this->clientId);
        $exchangeService->setMainCurrency($baseCurrency);
        $exchangeRates = $exchangeService->getFormatRates();

        foreach($productList as $product){
            if(!isset($productMap[$product[$productIdKey]])){
                throw new \RuntimeException(\Yii::t('product', '不存在product_id为{productId}的子产品！', ['{productId}' => $product[$productIdKey]]));
            }
//            $productCostCurrency = $productMap[$product[$productIdKey]];
            $productCost = $costMap[$product[$skuIdKey]];
//            if(!$productCostCurrency['cost_currency']){
//                throw new \RuntimeException("子产品【{$productCostCurrency['product_id']}】的成本没有设置币种，无法计算关联成本！");
//            }
            if(!empty($productCost['cost_currency'])){
                if(!isset($exchangeRates[$productCost['cost_currency']])){
                    throw new \RuntimeException(\Yii::t('product', '没有子产品币种【{cost_currency}】的汇率信息！', ['{cost_currency}' => $productCost['cost_currency']]));
                }
                $rateInfo = $exchangeRates[$productCost['cost_currency']];
                $exchangeCost = $productCost['cost'] * $rateInfo['rate'];
            }else{
                $exchangeCost = 0;
            }

            $totalCost += $exchangeCost * $product[$countKey];
        }
        return $totalCost;
    }

    public function calculProductCostMap($productIds){
        $relationFilter = new CombineProductRelationFilter($this->clientId);
        $relationFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
        $relationFilter->combine_product_id = $productIds;
        $relationFilter->select(['combine_product_id', 'sub_product_id', 'sub_sku_id', 'count']);

        $skuFilter = new ProductSkuFilter($this->clientId);
        $skuFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
        $skuFilter->select(['cost', 'cost_currency']);

        $joinFilter = $skuFilter->initJoin();
        $joinFilter = $joinFilter->innerJoin($relationFilter)->on('sku_id', 'sub_sku_id');
        $rawData = $joinFilter->rawData();


        $exchangeService = new ExchangeRateService($this->clientId);
        $exchangeService->setMainCurrency('USD');
        $ratesBaseUSD = $exchangeService->getFormatRates();
        $exchangeService->setMainCurrency('CNY');
        $ratesBaseCNY = $exchangeService->getFormatRates();

        $subSkuMap = [];

        foreach($rawData as $subSku){  // 先全部计算为CNY
            $usdRate = !isset($ratesBaseUSD[$subSku['cost_currency']]) ? 0 : $ratesBaseUSD[$subSku['cost_currency']]['rate'];
            $cnyRate = !isset($ratesBaseCNY[$subSku['cost_currency']]) ? 0 : $ratesBaseCNY[$subSku['cost_currency']]['rate'];
            if(!isset($subSkuMap[$subSku['combine_product_id']])){
                $subSkuMap[$subSku['combine_product_id']] = ['usd_cost'=>0,'cny_cost'=>0];
            }
            $subSkuMap[$subSku['combine_product_id']]['usd_cost'] += $usdRate * $subSku['cost'] * $subSku['count'];
            $subSkuMap[$subSku['combine_product_id']]['cny_cost'] += $cnyRate * $subSku['cost'] * $subSku['count'];
        }
        return $subSkuMap;
    }

    public function getProductIdWithoutSubProduct($params){
        $filter = $this->buildFilter($params);
        $filter->select(['product_id']);
        $data = $filter->rawData();
        $productIds = array_column($data, 'product_id');
        $productIdsMap = array_flip($productIds);

        $productIdsChunk = array_chunk($productIds, 1000);      // 分批查询
        $productCantDelete = [];
        foreach($productIdsChunk as $batchProductIds){
            $combineFilter = $this->buildCommbineFilter(['sub_product_id'=>$batchProductIds]);
            $combineFilter->select(['sub_product_id', 'combine_product_id']);
            $combineInfos = $combineFilter->rawData();      // 待删除的子产品和这些子产品涉及到的组合产品
            if($combineInfos){
                foreach($combineInfos as $combineInfo){
                    if(!isset($productIdsMap[$combineInfo['combine_product_id']])){
                        $productCantDelete[$combineInfo['sub_product_id']] = true;
                    }
                }
            }
        }

        $productCantDelete = array_keys($productCantDelete);
        $targetIds = array_diff($productIds, $productCantDelete);
        return [$targetIds, $productCantDelete];
    }

    public function hasSubProductByIds($productIds=[], $skuIds=[]){
        if((!$productIds && !$skuIds) || ($productIds && $skuIds)){
            throw new \RuntimeException(\Yii::t('product', '产品id和sku id至少且只能传一个'));
        }
        $relationFilter = $this->buildCommbineFilter(['sub_product_id'=>$productIds, 'sub_sku_id'=>$skuIds]);
        return $relationFilter->count() > 0;
    }

    public function imagePreMatch($imgNames,$appendMode,$matchMode, $withImages=false){
        $resultMap = array_flip($imgNames);        // key:文件名 value:[匹配成功与否，匹配到的产品信息，错误信息]
        $uniqueMap = [];
        $targetNameMap = [];       // key:处理后的目标名称 如 Coffe(3)处理为Coffe value:[文件名] | 这里包含不超过6张且名称不重复的图片
        $matchModeName = \Yii::t('product', $matchMode);

        // 检查文件名是否重复（不含后缀）
        foreach($imgNames as $imgName){
            $pointPos = strrpos($imgName, '.');
            $imgNameWithoutSuffix = $pointPos !== false ? substr($imgName, 0, strrpos($imgName, '.')) : substr($imgName, 0);
            if(!isset($uniqueMap[$imgNameWithoutSuffix])){
                $uniqueMap[$imgNameWithoutSuffix] = ['num' => 0, 'img_name' => $imgName];
            }
            $uniqueMap[$imgNameWithoutSuffix]['num']++;
            if($uniqueMap[$imgNameWithoutSuffix]['num'] > 1){
                $resultMap[$imgName] = $this->buildImagePreMatchResult($imgName, false, [], \Yii::t('product', 'import image | image name repeat'));
            }
        }

        foreach($uniqueMap as $imgNameWithoutSuffix => ['num' => $num, 'img_name'=>$originName]){
            $targetName = $imgNameWithoutSuffix;
            $seqNo = 0;

            // 检测括号
             preg_match('/(\((\d+)\)|（(\d+)）)$/',$imgNameWithoutSuffix,$bracketMatchRes,PREG_OFFSET_CAPTURE);
            if(!empty($bracketMatchRes) && !empty($bracketMatchRes[0]) && !empty($bracketMatchRes[0][1]) && $bracketMatchRes[0][1] != 0){
                $bracketPos = $bracketMatchRes[0][1];
                $seqNoFirst = isset($bracketMatchRes[2]) && isset($bracketMatchRes[2][0]) ? $bracketMatchRes[2][0] : 0;
                $seqNoSec = isset($bracketMatchRes[3]) && isset($bracketMatchRes[3][0]) ? $bracketMatchRes[3][0] : 0;
                $seqNo = max($seqNoFirst, $seqNoSec);
                $targetName = substr($imgNameWithoutSuffix, 0, $bracketPos);
            }
            if(!isset($targetNameMap[$targetName])){
                $targetNameMap[$targetName] = [];
            }

            // 如果需要以括号内的序号顺序作为选取前6张图片的标准，而非以窗口界面图片的顺序为标准，则打开这段注释，并注释掉下面的判断语句
//            $targetNameMap[$targetName][] = ['img_name_without_suffix' => $imgNameWithoutSuffix , 'seq_no' =>$seqNo, 'img_name' => $originName];
            if(count($targetNameMap[$targetName]) < ProductConstant::SPU_IMG_MAX_NUM){
                $targetNameMap[$targetName][] = ['img_name_without_suffix' => $imgNameWithoutSuffix , 'seq_no' =>$seqNo, 'img_name' => $originName];
            }else{
                $resultMap[$originName] = $this->buildImagePreMatchResult($originName,false, [], sprintf(\Yii::t('product', 'import image | image for single {match_mode} product exceed 6 pieces'), $matchModeName));
            }
        }

        $matchRes = [];     // key:targetName，  value:buildImagePreMatchResult
        if($targetNameMap){
            switch($matchMode){
                case ProductConstant::IMPORT_IMG_MATCH_MODEL:
                    $matchRes = $this->imagePreMatchModel(array_keys($targetNameMap),$withImages);
                    break;
                case ProductConstant::IMPORT_IMG_MATCH_NO:
                    $matchRes = $this->imagePreMatchNo(array_keys($targetNameMap), $withImages);
                    break;
                case ProductConstant::IMPORT_IMG_MATCH_NAME:
                    $matchRes = $this->imagePreMatchName(array_keys($targetNameMap), $withImages);
                    break;
            }
        }

        foreach($targetNameMap as $targetName => $originImgNameInfos){
            $succ = $matchRes[$targetName]['success'];
            $existProductImgNum = $succ ? $matchRes[$targetName]['product'][0]['product_img_num'] : 0;

            usort($originImgNameInfos, function($prev,$next){
                $prevSeqNo = intval($prev['seq_no']);
                $nextSeqNo = intval($next['seq_no']);
                if($prevSeqNo == $nextSeqNo){
                    return 0;
                }
                return ($prevSeqNo < $nextSeqNo) ? -1 : 1;
            });

            foreach($originImgNameInfos as $k => $originImgNameInfo){
                if(isset($resultMap[$originImgNameInfo['img_name']]) && isset($resultMap[$originImgNameInfo['img_name']]['success'])  && $resultMap[$originImgNameInfo['img_name']]['success'] == 0){
                    continue;
                }
                if($appendMode == ProductConstant::IMPORT_IMG_APPEND_MODE){
                    $curMatchImageNum = $existProductImgNum + $k + 1;
                    if(!empty($matchRes[$targetName]['match_sku']) && $curMatchImageNum > ProductConstant::SKU_IMG_MAX_NUM){
                        $resultMap[$originImgNameInfo['img_name']] = array_merge($matchRes[$targetName],$this->buildImagePreMatchResult($originImgNameInfo['img_name'],false, $matchRes[$targetName]['product'], \Yii::t('product', 'import image | image for this sku has been exist')));
                        continue;
                    }elseif(empty($matchRes[$targetName]['match_sku']) && $curMatchImageNum > ProductConstant::SPU_IMG_MAX_NUM){
                        $resultMap[$originImgNameInfo['img_name']] = array_merge($matchRes[$targetName],$this->buildImagePreMatchResult($originImgNameInfo['img_name'],false, $matchRes[$targetName]['product'], sprintf(\Yii::t('product', 'import image | image for single {match_mode} product exceed 6 pieces'), $matchModeName)));
                        continue;
                    }
                }
                $resultMap[$originImgNameInfo['img_name']] = $matchRes[$targetName];
                $resultMap[$originImgNameInfo['img_name']]['file_name'] = $originImgNameInfo['img_name'];
                if(empty($matchRes[$targetName]['match_sku']) && $k == 0){
                    $resultMap[$originImgNameInfo['img_name']]['is_main'] = true;       // 第一张为主图
                }
            }
        }
//ksort($resultMap);
        return array_values($resultMap);
    }

    protected function buildImagePreMatchResult($fileName, $succ, $product =[], $err='', $showErrType='product'){
        foreach($product as &$datum){
            $datum['success'] = $succ;
            $datum['err'] = $err;
        }
        return [
            'file_name' => $fileName,
            'success' => intval($succ),
            'product' => $product,
            'err' => $err,
            'show_err_type' =>$showErrType,
        ];
    }

    protected function buildImageProductMatchResult($product, $succ, $err=''){
        $product['success'] = intval($succ);
        $product['err'] = $err;
        return $product;
    }

    public function imagePreMatchModel($models, $withImages=false){
        $selectFields = ['product_id', 'name', 'product_no', 'model', 'images', 'product_type'];
        $matchModeName = \Yii::t('product', ProductConstant::IMPORT_IMG_MATCH_MODEL);
        $filter = new ProductFilter($this->clientId);
        $filter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $filter->model = $models;
        $filter->select($selectFields);
        $batchProduct = $filter->find();
        $formatter =  $batchProduct->getFormatter();
        $formatter->preImgMatchSetting();
        $products = $batchProduct->getListAttributes();
        $matchRes = [];     // key:model值，  value:buildImagePreMatchResult

        $productModelGroup = [];
        foreach($products as $product){
            if(!isset($productModelGroup[$product['model']])){
                $productModelGroup[$product['model']] = [];
            }
            $product['product_img_num'] = count($product['images']);
            if(!$withImages){
                unset($product['images']);
            }
            $productModelGroup[$product['model']][] = $product;
        }

        foreach($models as $model){
            if(!isset($productModelGroup[$model])){     // 图片名指定的型号不存在
                $matchRes[$model] = $this->buildImagePreMatchResult('',false, [], sprintf(\Yii::t('product', 'import image | product for specific {match_mode} does not exist'), $matchModeName));
                continue;
            }
            if(count($productModelGroup[$model]) > 1){  // 判断是否匹配多个产品
                $matchRes[$model] = $this->buildImagePreMatchResult('',false, $productModelGroup[$model], sprintf(\Yii::t('product', 'import image | more than one product have same {match_mode}'), $matchModeName));
                $matchRes[$model]['need_handle_match'] = true;
                continue;
            }
            $matchRes[$model] = $this->buildImagePreMatchResult('',true, $productModelGroup[$model]);
        }
        return $matchRes;
    }

    public function imagePreMatchNo($productNo, $withImages=false){
        $selectSpuFields = ['product_id', 'name', 'product_no', 'model', 'images', 'product_type'];
        $selectSkuFields = ['sku_code', 'image_file_id'];
        $matchModeName = \Yii::t('product', ProductConstant::IMPORT_IMG_MATCH_NO);

        // spu信息
        $filter = new ProductFilter($this->clientId);
        $filter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $filter->select($selectSpuFields);
        $cloneFilter = deep_copy($filter);
        $filter->product_no = $productNo;
        $batchProduct = $filter->find();
        $formatter =  $batchProduct->getFormatter();
        $formatter->preImgMatchSetting();
        $products = $batchProduct->getListAttributes();

        // sku信息
//        $skuApi = new SkuAPI($this->clientId, $this->userId);
        $cloneFilter->product_type = ProductConstant::PRODUCT_TYPE_SKU;
        $skuFilter = new ProductSkuFilter($this->clientId);
        $skuFilter->select($selectSkuFields);
        $skuFilter->sku_code = $productNo;
        $skuFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $joinFilter =  $skuFilter->initJoin();
        $joinFilter = $joinFilter->innerJoin($cloneFilter);
        $joinFilter = $joinFilter->on('product_id', 'product_id');
//        $joinFilter = $skuApi->joinFilter($skuFilter, $cloneFilter);
        $skus = $joinFilter->rawData();

        $matchRes = [];     // key:model值，  value:buildImagePreMatchResult

        $productNoGroup = [];
        foreach($products as $product){
            if(!isset($productNoGroup[$product['product_no']])){
                $productNoGroup[$product['product_no']] = [];
            }
            $product['product_img_num'] = count($product['images']);
            if(!$withImages){
                unset($product['images']);
            }
            $productNoGroup[$product['product_no']][] = $product;
        }

        foreach($skus as $sku){
            if(!isset($productNoGroup[$sku['sku_code']])){
                $productNoGroup[$sku['sku_code']] = [];
            }
            $sku['product_img_num'] = empty($sku['image_file_id']) ? 0 : 1;
            $productNoGroup[$sku['sku_code']][] = $sku;
        }

        foreach($productNo as $singleProductNo){
            if(!isset($productNoGroup[$singleProductNo])){     // 图片名指定的编号不存在
                $matchRes[$singleProductNo] = $this->buildImagePreMatchResult('',false, [], sprintf(\Yii::t('product', 'import image | product for specific {match_mode} does not exist'), $matchModeName));
            }else{
                $matchRes[$singleProductNo] = $this->buildImagePreMatchResult('',true, $productNoGroup[$singleProductNo]);
            }
            $matchRes[$singleProductNo]['match_sku'] = !empty($productNoGroup[$singleProductNo][0]['sku_code']);
        }
        return $matchRes;
    }

    public function imagePreMatchName($names, $withImages=false){
        $selectFields = ['product_id', 'name', 'product_no', 'model', 'images', 'product_type'];
        $matchModeName = \Yii::t('product', ProductConstant::IMPORT_IMG_MATCH_NAME);
        $filter = new ProductFilter($this->clientId);
        $filter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $filter->name = $names;
        $filter->select($selectFields);
        $batchProduct = $filter->find();
        $formatter =  $batchProduct->getFormatter();
        $formatter->preImgMatchSetting();
        $products = $batchProduct->getListAttributes();
        $matchRes = [];     // key:model值，  value:buildImagePreMatchResult

        $productNameGroup = [];
        foreach($products as $product){
            if(!isset($productNameGroup[$product['name']])){
                $productNameGroup[$product['name']] = [];
            }
            $product['product_img_num'] = count($product['images']);
            if(!$withImages){
                unset($product['images']);
            }
            $productNameGroup[$product['name']][] = $product;
        }

        foreach($names as $name){
            if(!isset($productNameGroup[$name])){     // 图片名指定的名称不存在
                $matchRes[$name] = $this->buildImagePreMatchResult('',false, [], sprintf(\Yii::t('product', 'import image | product for specific {match_mode} does not exist'), $matchModeName));
                continue;
            }
            if(count($productNameGroup[$name]) > 1){  // 判断是否匹配多个产品
                $matchRes[$name] = $this->buildImagePreMatchResult('',false, $productNameGroup[$name], sprintf(\Yii::t('product', 'import image | more than one product have same {match_mode}'), $matchModeName));
                $matchRes[$name]['need_handle_match'] = true;
                continue;
            }
            $matchRes[$name] = $this->buildImagePreMatchResult('',true, $productNameGroup[$name]);
        }
        return $matchRes;
    }

    public function imageMatch($imgInfos,$append_mode,$match_mode, $import_id=0, $last_batch=0){
        // 查询已匹配成功的图片
        $imgMatchedMap = [];
        $importRecordApi = new ImportRecordApi($this->clientId, $this->userId);
        if($import_id){
            $importRecordFilter = $importRecordApi->buildFilter(['import_id'=>$import_id, 'status'=>ImportConstants::IMPORT_RECORD_STATUS_SUCCESS], ['refer_id']);
            $imgMatchedMap = array_flip(array_column($importRecordFilter->rawData(), 'refer_id'));
        }

        // 查询新插入图片的url
        $fileIds = array_column($imgInfos, 'file_id');
        $fileIdUrlMap = Helper::fileUrlMap($fileIds);

        $matchModeName = \Yii::t('product', $match_mode);
        $skuImgStatMap = $spuImgStatMap = $spuOldImgs=[];     // 统计当前产品的图片上限是否已满，key为product_id 或 sku_code
        $matchRes = [];     // key：图片名
        $imgInfos = array_column($imgInfos, null, 'img_name');
        $preMatchRes = $this->imagePreMatch(array_column($imgInfos, 'img_name'), $append_mode, $match_mode, true);
        foreach($preMatchRes as $datum){
            $fileName = $datum['file_name'];
            $curFile = $imgInfos[$fileName];
            $showErrType = 'product';

            // 预匹配时就发生错误
            if(isset($curFile['pre_match_succ']) && empty($curFile['pre_match_succ'])){
                $showErrType = 'image';
                $matchRes[$fileName] = $this->buildImagePreMatchResult($fileName,false, $datum['product'], $curFile['err'], $showErrType);
                continue;
            }
            if(empty($datum['product'])){   // 没有匹配到产品
                $matchRes[$fileName] = $this->buildImagePreMatchResult($fileName,false, $datum['product'], sprintf(\Yii::t('product', 'import image | product for specific {match_mode} does not exist'), $matchModeName), $showErrType);
                continue;
            }

            // 将产品当前images划分为 已匹配成功图片 和 以前就存在的图片 两堆
            if(!empty($datum['match_sku'])){    // 匹配到sku
                $sku = $datum['product'][0];
                if(!isset($skuImgStatMap[$sku['sku_code']])){
                    $skuImgStatMap[$sku['sku_code']] = ['matched'=>[], 'history'=>[]];
                    if(!empty($sku['image_file_id'])){
                        if(isset($imgMatchedMap[$sku['image_file_id']])){
                            $skuImgStatMap[$sku['sku_code']]['matched'][] = $sku['image_file_id'];
                        }else{
                            $skuImgStatMap[$sku['sku_code']]['history'][] = $sku['image_file_id'];
                        }
                    }
                }
                if($append_mode == ProductConstant::IMPORT_IMG_APPEND_MODE){
                    if(count($skuImgStatMap[$sku['sku_code']]['matched']) + count($skuImgStatMap[$sku['sku_code']]['history']) >= ProductConstant::SKU_IMG_MAX_NUM){
                        $matchRes[$fileName] = $this->buildImagePreMatchResult($fileName,false, $datum['product'], \Yii::t('product', 'import image | image for this sku has been exist'), $showErrType);
                        continue;
                    }else{
                        $skuImgStatMap[$sku['sku_code']]['matched'][] = $curFile['file_id'];
                    }
                }else{
                    // 清除旧图片
                    $skuImgStatMap[$sku['sku_code']]['history'] = [];
                    if(count($skuImgStatMap[$sku['sku_code']]['matched']) >= ProductConstant::SKU_IMG_MAX_NUM){
                        $matchRes[$fileName] = $this->buildImagePreMatchResult($fileName,false, $datum['product'], \Yii::t('product', 'import image | image for this sku has been exist'), $showErrType);
                        continue;
                    }else{      // 弹出已有图片，追加新图片
                        $skuImgStatMap[$sku['sku_code']]['matched'][] = $curFile['file_id'];
//                        array_shift($skuImgStatMap[$sku['sku_code']]['history']);
                    }
                }
                $matchRes[$fileName] = $this->buildImagePreMatchResult($fileName,true, $datum['product'], $showErrType);
            }else{  // 匹配到spu
                $matchSucc = true;
                $productRes = [];
                foreach($datum['product'] as $k=>$spu){
                    $singleMatchSucc = true;
                    if(!empty($curFile['product_id']) && !in_array($spu['product_id'], $curFile['product_id'])){        // 未命中用户指定该图片要匹配的产品时跳过
                        unset($datum['product'][$k]);
                        continue;
                    }
                    $spuOldImgs[$spu['product_id']] = $spu['images'];
                    if(!isset($spuImgStatMap[$spu['product_id']])){
                        $spuImgStatMap[$spu['product_id']] = ['matched'=>[], 'history'=>[]];
                        foreach($spu['images'] as $curSpuImg){
                            if(isset($imgMatchedMap[$curSpuImg['id']])){
                                $spuImgStatMap[$spu['product_id']]['matched'][$curSpuImg['id']] = $curSpuImg['src'] ?? '';
                            }else{
                                $spuImgStatMap[$spu['product_id']]['history'][$curSpuImg['id']] = $curSpuImg['src'] ?? '';
                            }
                        }
                    }
                    $errMsg = count($curFile['product_id']) <= 1 ? sprintf(\Yii::t('product', 'import image | image for single {match_mode} product exceed 6 pieces'), $matchModeName) : \Yii::t('product', 'import image | all or partial product exceed 6 images');
                    if($append_mode == ProductConstant::IMPORT_IMG_APPEND_MODE){
                        if(count($spuImgStatMap[$spu['product_id']]['matched']) + count($spuImgStatMap[$spu['product_id']]['history']) >= ProductConstant::SPU_IMG_MAX_NUM){
                            $matchRes[$fileName] = $this->buildImagePreMatchResult($fileName,false, [], $errMsg, $showErrType);
                            $singleMatchSucc = $matchSucc = false;
//                            continue;       // 如果是break，则多产品匹配情况下，部分产品可能匹配成功，部分产品由于图片超量失败
                        }else{
                            $spuImgStatMap[$spu['product_id']]['matched'][$curFile['file_id']] = $fileIdUrlMap[$curFile['file_id']]['file_path'];   // 或者不用 $fileIdUrlMap[$curFile['file_id']]['file_path'] 而用 $curFile['file_url']
                        }
                    }else{
                        // 清除旧图片
                        $spuImgStatMap[$spu['product_id']]['history'] = [];
                        if(count($spuImgStatMap[$spu['product_id']]['matched']) >= ProductConstant::SPU_IMG_MAX_NUM){
                            $matchRes[$fileName] = $this->buildImagePreMatchResult($fileName,false, [], $errMsg, $showErrType);
                            $singleMatchSucc = $matchSucc = false;
//                            continue;
                        }else{      // 弹出已有图片，追加新图片
                            $spuImgStatMap[$spu['product_id']]['matched'][$curFile['file_id']] = $fileIdUrlMap[$curFile['file_id']]['file_path'];
//                            array_shift($spuImgStatMap[$spu['product_id']]['history']);
                        }
                    }
                    $productRes[] = $this->buildImageProductMatchResult($spu, $singleMatchSucc, $singleMatchSucc ? '' : $errMsg);
                }

                if($matchSucc){
                    $matchRes[$fileName] = $this->buildImagePreMatchResult($fileName,true, [], '', $showErrType);
                }
                $matchRes[$fileName]['product'] = $productRes;
            }
        }

        $updateProductIds = array_keys($spuImgStatMap);
        if(!empty($updateProductIds)){
            $this->batchUpdateSpuImages($spuImgStatMap,$updateProductIds, $spuOldImgs);
        }

        $updateSkuCodes = array_keys($skuImgStatMap);
        if(!empty($updateSkuCodes)){
            $updateSkuCodesStr = "'" . implode("','", array_keys($skuImgStatMap)) . "'";
            $updateTime = xm_function_now();
            $updateSkuSql = "update tbl_product_sku set update_time='{$updateTime}',update_user={$this->userId}, image_file_id = case ";     // key:sku_code      value:image_file_id
            foreach($skuImgStatMap as $skuCode => ['matched' => $matchedImgs, 'history'=>$historyImgs]){
                $imgFileId = $matchedImgs[0] ?? $historyImgs[0] ?? 0;
                $updateSkuSql .= " when sku_code = '{$skuCode}' then {$imgFileId} ";
            }

            $updateSkuSql .= " else image_file_id end where client_id = {$this->clientId} and sku_code in ({$updateSkuCodesStr})";
            $db = \PgActiveRecord::getDbByClientId($this->clientId);
            $db->createCommand($updateSkuSql)->execute();
        }


        if(empty($import_id)){
            $import = new Import($this->clientId);
            $importType = \Constants::TYPE_PRODUCT;
            $replace_flag = intval($append_mode == ProductConstant::IMPORT_IMG_RECOVER_MODE);       // 1 覆盖；2 追加
            $scene = ImportConstants::PRODUCT_IMG_IMPORT;
            $external_field_data = ["match_mode"=>$match_mode];
            $import->getOperator()->create($importType,0, $replace_flag,$external_field_data, $scene);
            $import_id = $import->import_id;
        }

        // 导入明细批量入库
        $importRecord = [];
        foreach($imgInfos as $fn => $imgInfo){
            $singleMatchRes = $matchRes[$fn];
            $failReason = '';
            if(!empty($singleMatchRes['show_err_type']) && $singleMatchRes['show_err_type']=='product' && count($imgInfo['product_id']) > 1){     // 匹配到多张图片且展示product维度的错误时
                foreach($singleMatchRes['product'] as $resProduct){     // 收集失败产品的原因
                    if(empty($resProduct['success'])){
                        $productNoField = isset($resProduct['sku_code']) ? 'sku_code' : 'product_no';
                        switch($match_mode){
                            case 'match_model':
                                $matchModeField = 'model';
                                break;
                            case 'match_name':
                                $matchModeField = 'name';
                                break;
                        }
                        $failReason .= $resProduct[$matchModeField] . '(' .$resProduct[$productNoField] . ') ' . $resProduct['err'] . "\n";
                    }
                }
            }else{
                $failReason = $singleMatchRes['err'];
            }
            $importRecord[] = [
                'refer_id' => $imgInfo['file_id'],
                'import_id' => $import_id,
                'status' => $singleMatchRes['success'] ? ImportConstants::IMPORT_RECORD_STATUS_SUCCESS : ImportConstants::IMPORT_RECORD_STATUS_FAIL,
                'fail_reason' => json_encode(['seq'=>$imgInfo['seq'], 'file_name'=>$fn, 'reason'=> $failReason]),
                'create_time' => xm_function_now(),
                'update_time' => xm_function_now(),
            ];
        }
        $batchImportRecord = new BatchImportRecord($this->clientId);
        $batchImportRecord->getOperator()->batchSave($importRecord);

        // 统计成功/失败数量
        list($successNum, $failNum) = $importRecordApi->getImportSuccFailCount($import_id);

        // 如果是最后一次调用匹配接口，修改任务状态并上传失败文件
        $failFile = [];
        if($last_batch){
            $failFile = $this->saveImgMatchTaskResult($import_id, $successNum, $failNum);
        }

        return ["import_id"=>$import_id, "success_num"=> $successNum, "fail_num"=> $failNum, "match_result" => array_values($matchRes), "fail_file" => $failFile];
    }

    public function batchUpdateSpuImages($spuImgStatMap, $updateProductIds,$spuOldImgs){
        $spuFilter = new ProductFilter($this->clientId);
        $spuFilter->product_id = array_unique($updateProductIds);
        $spuFilter->select(['name','product_id', 'product_no', 'source_type', 'product_type']);
        $spuInfos = array_column($spuFilter->rawData(), null,'product_id');

        $updateTime = xm_function_now();

        // 历史记录类
        $historySetting = new ProductSetting();
        $class = $historySetting->metadataClass();
        History::setMetadataClass($class);
        BatchHistory::setMetadataClass($class);
        HistoryFilter::setMetadataClass($class);

        $spuImgStatMapChunks = array_chunk($spuImgStatMap, 1000, true);
        foreach($spuImgStatMapChunks as $spuImgStatMapChunk){
            $chunkUpdateProductIds = $chunkNoAttrProductIds = [];
            $historyData = [];
            $updateSpuSql = "update tbl_product set update_time = '{$updateTime}', update_user = {$this->userId}, images = case ";
            $updateSkuSql = "update tbl_product_sku set update_time = '{$updateTime}',  update_user = {$this->userId}, image_file_id = case";
            foreach($spuImgStatMapChunk as $productId => ['matched' => $matchedImgs, 'history'=>$historyImgs]){
                // 构建images字段
                $images = [];
                foreach($historyImgs as $fileId => $fileSrc){
                    $images[] = ["id" => $fileId, "src"=>$fileSrc];
                }
                foreach($matchedImgs as $fileId=>$fileSrc){
                    $images[] = ["id" => $fileId, "src"=>$fileSrc];
                }

//                $product = new Product($this->clientId, $productId);
//                $product->images = $images;
//                $product->update(['images', 'update_time', 'update_user']);
//                $updateSpuData[$productId] = $images;

                $needLogHistory = false;
                if(count($images) == count($spuOldImgs[$productId])){
                    $newImgIds = array_column($images, 'id');
                    $oldImgIds = array_column($spuOldImgs[$productId], 'id');
                    sort($newImgIds);
                    sort($oldImgIds);
                    foreach($newImgIds as $key => $id){
                        if($oldImgIds[$key] != $id){
                            $needLogHistory = true;
                            break;
                        }
                    }
                }else{
                    $needLogHistory = true;
                }

                if($needLogHistory){
                    $historyData[] = [
                        'client_id' => $this->clientId,
                        'product_id' => $productId,
                        'type' => ProductSetting::TYPE_EDIT_PRODUCT,
                        'data' => [
                            'name'=>$spuInfos[$productId]['name'],
                            'product_id'=>$productId,
                            'product_no'=>$spuInfos[$productId]['product_no'],
                            'source_type'=>$spuInfos[$productId]['source_type'],
                        ],
                        'diff' => [["id"=>'images', 'new' => $images, 'old'=>$spuOldImgs[$productId], 'base'=>1]],
                        'update_user' => $this->userId,
                        'update_time' => $updateTime,
                        'update_type' => 1,
                        'refer_type' => 0,
                        'refer_id' => 0,
                        'create_time' => $updateTime
                    ];
                    $chunkUpdateProductIds[] = $productId;
                    $updateSpuSql .= " when product_id = {$productId} then '". json_encode($images) . "' ";
                    if(isset($spuInfos[$productId]) && in_array($spuInfos[$productId]['product_type'],[ProductConstant::PRODUCT_TYPE_SPU,ProductConstant::PRODUCT_TYPE_COMBINE])){
                        $chunkNoAttrProductIds[] = $productId;
                        $updateSkuSql .= " when product_id = {$productId} then ".($images[0]['id'] ?? 0)." ";
                    }
                }
            }
            if(!empty($chunkUpdateProductIds)){
                $updateProductIdsStr = implode(",", $chunkUpdateProductIds);
                $updateSpuSql .= " else images end where client_id = {$this->clientId} and product_id in ({$updateProductIdsStr})";

                $db = \PgActiveRecord::getDbByClientId($this->clientId);
                $db->createCommand($updateSpuSql)->execute();
            }

            if(!empty($chunkNoAttrProductIds)){
                $updateNoAttrIdsStr = implode(",", $chunkNoAttrProductIds);
                $updateSkuSql .= " else image_file_id end where client_id = {$this->clientId} and product_id in ({$updateNoAttrIdsStr})";

                $db = \PgActiveRecord::getDbByClientId($this->clientId);
                $db->createCommand($updateSkuSql)->execute();
            }

            // 变更历史记录
            if(!empty($historyData)){
                $batchObjectClass = call_user_func([$class, 'batchObject']);
                $batchObject = new $batchObjectClass($this->clientId);
                $batchObject->initFromData($historyData);
                $batchObject->getOperator()->create($historySetting);
            }
        }
    }

    public function saveImgMatchTaskResult($importId, $successNum, $failNum){
        $resultFileId = 0;
        $import = new Import($this->clientId, $importId);
        $failFile = [];
        if ($failNum > 0) {
            // 获取失败明细
            $importRecordApi = new ImportRecordApi($this->clientId, $this->userId);
            $importRecordFilter = $importRecordApi->buildFilter(['import_id'=>$importId, 'status'=>ImportConstants::IMPORT_RECORD_STATUS_FAIL]);
            $failRecords = $importRecordFilter->rawData();
            $fileName = 'product_import_image' . $import->user_id . '_' . time() . '.csv';
            $path = '/tmp/' . $fileName;
            $fp = fopen($path, 'w');
            fputcsv($fp, ['序号（No）', '文件名（File Name）', '原因（Message）']);
            foreach ($failRecords as $failRecord) {
                $failReason = json_decode($failRecord['fail_reason'], true);
                fputcsv($fp, [$failReason['seq'], $failReason['file_name'], $failReason['reason']]);
            }

            $oldContents = file_get_contents($path);
            $utfContents = iconv("UTF-8", "GB2312//IGNORE", $oldContents);
            file_put_contents($path, $utfContents);
            $upload = \UploadService::uploadRealFile($path, $fileName, \UploadService::getFileKey($path));
            $resultFileId = $upload->getFileId();
            $failFile = [
                'file_id' => $resultFileId,
                'file_name' =>$upload->getFileName(),
                'file_url' => $upload->getFileUrl(),
                'preview_url' => $upload->getPreview(),
            ];
        }

        $data = [
            'result_file_id' => $resultFileId,
            'total_count' => $successNum + $failNum,
            'fail_count' => $failNum,
            'finish_time' => date('Y-m-d H:i:s'),
        ];

        $failNum == 0 ? $import->getOperator()->success($data) : $import->getOperator()->fail($data);

        return $failFile;
    }

    public function getErrors(){
        return $this->errors;
    }

    public function getErrorMsg(){
        return $this->errorMsg;
    }

    public function deleteAll($query_params = []){
        if(!is_array($query_params)){
            $query_params = json_decode($query_params, true);
        }

        unset(
            $query_params['page'],
            $query_params['page_size'],
            $query_params['scene'],
            $query_params['sort_field'],
            $query_params['sort_type'],
        );

        if(empty($query_params)){       // 无筛选条件
            $filter = new ProductFilter($this->clientId);
            $batch = $filter->find();
            $count = $batch->getOperator()->deleteAll();
        }else{  // 有筛选条件
            list($targetIds, $discardIds) = (new ProductAPI($this->clientId))->getProductIdWithoutSubProduct($query_params);

            if (count($discardIds) != 0) {
                $errMsg = count($discardIds) . \Yii::t('product', ' of product selected is sub of combination product, it is not allowed to delete.');
                throw new \RuntimeException($errMsg);
            }

            if(count($targetIds) == 0){
                return 0;
            }

            if(count($targetIds) <= 1000){  // 直接同步删除 且 同步执行后置操作
                $filter = new ProductFilter($this->clientId);
                $filter->product_id = $targetIds;
                $batch = $filter->find();
                $this->userId && $batch->setDomainHandler(\User::getUserObject($this->userId));
                $count = $batch->getOperator()->delete();
                return $count;
            }

            // 分批删除产品
            $targetIdChunks = array_chunk($targetIds, 1000);
            $deleteTime = date('Y-m-d H:i:s');
            $db = \PgActiveRecord::getDbByClientId($this->clientId);
            $count = 0;
            foreach($targetIdChunks as $batchIds){
                if(empty($batchIds)){
                    continue;
                }
                $idStr = implode(',', $batchIds);
                $sql = "update tbl_product set enable_flag = 0, delete_time = '{$deleteTime}', delete_user={$this->userId} where client_id={$this->clientId} and enable_flag = 1 and product_id in ({$idStr})";
                $count += $db->createCommand($sql)->execute();
            }

            $taskData = [
                'query_params' => $query_params,
                'delete_time' => $deleteTime,
                'total_count' => $count
            ];
            $import = new Import($this->clientId);
            $import->getOperator()->create(\Constants::TYPE_PRODUCT,0,0, $taskData,ImportConstants::DELETE_ALL_PRODUCT);
            CommandRunner::run('product', 'doAfterDelete', [
                'user_id' => $this->userId,
                'import_id' => $import->import_id,
            ], '/tmp/product_do_after_delete.log');
        }

        return $count;
    }


    /***
     * 按规则匹配产品，仅将未匹配的产品作为无规格产品创建
     * 暂时仅供商机产品使用 todo 该接口需要补全各参数校验
     * @param $productList
     * @param $sourceType  int  创建方式
     * @param $newProductCreateFlag bool 新产品创建标志 true 新建 false 跳过
     * @return array|int[]
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function batchMathCreateSPUProduct($productList , $sourceType = ProductConstant::SOURCE_TYPE_XIAOMAN_USER, $newProductCreateFlag = true): array
    {
        if (empty($productList)) {
            return [
                'total_count' => 0,
                'create_success_count' => 0,
                'match_success_count' => 0,
            ];
        }

        $productApply = new ProductApplyV2($this->clientId, $this->userId);
        //step 1 匹配已存在产品
        $analyzeProductList = $productApply->analyzeSkuProducts($productList);
        //step 2 新建匹配失败的无规格产品
        $createProducts = array_values(array_filter($analyzeProductList, static function ($item) {
            return empty($item['product_id']) || empty($item['sku_id']);
        }));

        $total_count = count($productList);
        $create_product_count = count($createProducts) ;
        $match_count = $total_count - $create_product_count;

        if (!$newProductCreateFlag) {
            $createProducts = [];
            $create_product_count = 0;
        }

        if (empty($createProducts)) {
            return [
                'total_count' => $total_count,
                'create_success_count' => 0,
                'match_success_count' => $match_count,
            ];
        }

        $createProduct = [];
        $createProductSku = [];
        $generate = GenerateService::getInstance($this->clientId, \Constants::TYPE_PRODUCT);

        $mainCurrency = \common\library\account\Client::getClient($this->clientId)->getMainCurrency();


        $availableFields = [
//            'model',
            'name'
        ];

        $nowTime = date('Y-m-d H:i:s',time());
        $isExistProductNos = [];
        foreach ($createProducts as $createProductItem) {
            $productId = \ProjectActiveRecord::produceAutoIncrementId();
            $productNo = $createProductItem['product_no'] ?: $generate->generate();
            foreach ($availableFields as $availableField){
                if (empty($createProductItem[$availableField])){
                    continue 2;
                }
            }
            if (in_array($productNo, $isExistProductNos)) {
                continue;
            }

            $isExistProductNos[] = $productNo;

            $createProduct[] = [
                'client_id' => $this->clientId,
                'product_id' => $productId,
                'name' => trim($createProductItem['name']),
                'product_no' => $productNo,
                'product_type' => ProductConstant::PRODUCT_TYPE_SPU,
                'model' => $createProductItem['model'] ?? '',
                'description' => $createProductItem['description'] ?? '',
                'product_remark' => $createProductItem['product_remark'] ?? '',
                'unit' => $createProductItem['unit'] ?? '',
                'create_user' => $this->userId,
                'create_time' => $nowTime,
                'update_time' => $nowTime,
                'update_user' => $this->userId,
                'source_type' => $sourceType,
            ];
            $createProductSku[] = [
                'client_id' => $this->clientId,
                'sku_id' => \ProjectActiveRecord::produceAutoIncrementId(),
                'product_id' => $productId,
                'sku_code' => $productNo,
                'create_user' => $this->userId,
                'update_user' => $this->userId,
                'create_time' => $nowTime,
                'update_time' => $nowTime,
                'cost_currency' => $mainCurrency,
                'package_remark' => $createProductItem['package_remark'] ?? '',
            ];
        }

        if (empty($createProduct)){
            return [
                'total_count' => $total_count,
                'create_success_count' => 0,
                'match_success_count' => $total_count - $create_product_count,
            ];
        }

        $success_create_count = count($createProduct);

        //分块插入 日志记录插入批次以及成功数量
        $productChunk = array_chunk($createProduct, 500);
        $productSkuChunk = array_chunk($createProductSku, 500);

        $createProductIds = array_column($createProduct, 'product_id');

        foreach ($productChunk as $chunk) {
            $productOperator = (new BatchProduct($this->clientId))->getOperator();
            $productOperator->batchInsert($chunk, DBConstants::INSERT_MODE_CONFLICT, [], [ProductMetadata::objectIdKey()]);
        }

        foreach ($productSkuChunk as $chunk) {
            $productSkuOperator = (new BatchProductSku($this->clientId))->getOperator();
            $productSkuOperator->batchInsert($chunk, DBConstants::INSERT_MODE_CONFLICT, [], [ProductSkuMetadata::objectIdKey()]);
        }

        // 插入完成进行es更新
        SearchQueueService::pushProductQueue($this->userId, $this->clientId, $createProductIds, \Constants::PRODUCT_INDEX_TYPE_CREATE, \Constants::TYPE_PRODUCT);


        return [
            'total_count' => $total_count??0,
            'create_success_count' => $success_create_count??0,
            'match_success_count' => ($total_count - $create_product_count)??0,
        ];
    }

    public function singleEditProduct($product_id, $sku_id, $data)
    {

        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        //format 单行编辑与批量编辑不一致的数据结构
//        foreach ($data as $field => &$value) {
//            if (in_array($field, ProductField::$specialVolumeFormatField)) {
//                $value = [
//                    'edit_value' => $value,
//                    'edit_type' => ProductConstant::BATCH_EDIT_PRICE_TYPE_VALUE
//                ];
//            }
//        }
        $batchEditor = new \common\library\product_v2\ProductBatchEditor($clientId, $user->getUserId(), $data);
        $batchEditor->setProductIds($product_id);
        $batchEditor->setSkuIds($sku_id);
        $batchEditor->singleRun();

        return $batchEditor->getRunBatchEditResult();
    }

    public function getProductInventoryBySku($sku_id){
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        // 库存查询简化
        $inventoryFilter = new \common\library\oms\inventory\product_inventory\ProductInventoryFilter($clientId);
        $warehouseFilter = new WarehouseFilter($clientId);
        $warehouseFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $warehouseFilter->select(['name']);
        $inventoryFilter->select(['warehouse_id', 'enable_count', 'real_count']);
        $joinFilter = $inventoryFilter->initJoin();
        $joinFilter->innerJoin($warehouseFilter)->on('warehouse_id', 'warehouse_id');
        $inventoryFilter->sku_id = $sku_id;
        $batch = $joinFilter->find();
        $batch->getFormatter()->displayFloatVal(true);
        $batch->getFormatter()->displayFields(['warehouse_id', 'enable_count', 'real_count','name']);
        $inventoryData = $batch->getAttributes();
        foreach($inventoryData as &$datum){
            $datum['actual_inventory'] = $datum['real_count'];
        }
        return $inventoryData;
    }

    //仅提供给ai用
    public function getApiProductList(
        $client_id,
        $user_id,
        array $product_id_list,
        array $sku_id_list,
    )
    {
        User::setLoginUserById($user_id);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        if (!$this->canView()) {
            return [];
        }

        if (
            empty($sku_id_list) || empty($product_id_list)
        ) {
            return [];
        }

        $scene = APIConstant::SCENE_AI_API_SELECT;
        $api = new \common\library\product_v2\sku\SkuAPI($clientId, $userId);
        $data = $api->webList(['product_ids' => $product_id_list, 'sku_id' => $sku_id_list], $scene);

        // 获取可见的顶级分组id
        $groupApi = new ProductGroupApi($clientId);
        $viewableMainGroupIds = $groupApi->getViewableGroupIds($userId);

        $fieldList = new FieldList($user->getClientId());
        $fieldList->setType(Constants::TYPE_PRODUCT);
        $fieldList->setFields(['id','name']);
        $fieldList->setEnableFlag(null);
        $fieldList = $fieldList->rawData();
        $fieldMap = array_column($fieldList,'name','id');

        $data = $data['list'] ?? [];

        $format_field_map = ['external_field_data', 'fob', 'cost_with_tax', 'package_size', 'product_size', 'carton_size'];
        $privilege_field_map = \common\library\privilege_v3\Helper::getFieldIdByScope(
            $clientId,
            $userId,
            PrivilegeConstants::FUNCTIONAL_PRODUCT,
            Constants::TYPE_PRODUCT,
            [PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_DISABLE]
        )[PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_DISABLE] ?? [];

        foreach ($data as $index => &$datum) {

            if (!$this->checkGroupPrivilege($viewableMainGroupIds, $datum['group_id'] ?? 0, $datum['main_group_id'] ?? 0)) {
                unset($data[$index]);
                continue;
            }

            foreach ($format_field_map as $format_field_field) {
                if (!empty($datum[$format_field_field]) && is_array($datum[$format_field_field])) {
                    foreach ($datum[$format_field_field] as $array_field => $array_value) {
                        if (in_array($array_field, $privilege_field_map)) {
                            continue;
                        }
                        //组合字段 如果没权限跳过
                        if (in_array($format_field_field, $privilege_field_map)) {
                            foreach ($datum[$format_field_field] as $field_key => $field_value) {
                                unset($datum[$field_key]);
                            }
                            continue;
                        }
                        if ($format_field_field == 'external_field_data'){
                            $datum[$fieldMap[$array_field] ?? ''] = $array_value;
                        }else{
                            $datum[$array_field] = $array_value;
                        }
                    }
                    unset($datum[$format_field_field]);
                }
            }

            foreach ($privilege_field_map as $privilege_field) {
                unset($datum[$privilege_field]);
            }
        }
        return $data;
    }

    public function canView()
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $privilegeService = PrivilegeService::getInstance($clientId, $userId);
        // 主账号
        $adminUserId = $privilegeService->getAdminUserId();
        if ($adminUserId == $userId) {
            return true;
        }
        // 其余账号
        if ($privilegeService->hasPrivilege(PrivilegeConstants::PRIVILEGE_CRM_PRODUCT_VIEW)) {
            return true;
        }
    }

    //简单format 不可复用 $viewableMainGroupIds可能为数组 也可能为数值 有坑
    public function checkGroupPrivilege($viewableMainGroupIds,$group_id,$main_group_id)
    {
        if (in_array($main_group_id, [0, 1]) || in_array($group_id, [0, 1])) {
            return true;
        }

        if ($viewableMainGroupIds == GroupApi::CAN_VIEW_ALL_GROUPS) {
            return true;
        }

        if (is_array($viewableMainGroupIds) && !in_array($main_group_id, $viewableMainGroupIds)) {
            return false;
        }

        return true;
    }

}
