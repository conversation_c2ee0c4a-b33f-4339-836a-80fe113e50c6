<?php
/**
 * Created by PhpStorm.
 * User: ganyaoyao
 * Date: 18/5/9
 * Time: 11:25
 */
namespace common\library\export;

use common\library\custom_field\CustomFieldService;
use common\library\customer\BaseCompanyList;
use common\library\object\field\FieldConstant;
use common\library\opportunity\Opportunity;
use common\library\opportunity\OpportunityList;
use common\library\opportunity\product\OpportunityProductList;
use common\library\privilege_v3\PrivilegeFieldV2;
use common\library\setting\library\flow\FlowApi;
use common\library\setting\library\origin\OriginApi;
use common\library\setting\library\stage\Stage as OpportunityStage;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\setting\library\stage\StageApi;
use common\library\setting\user\UserSetting;
use common\library\util\Speed;

class OpportunityExport extends Export{


    //导出字段
    public static $fieldMap = [];
    //导出自定义字段
    public static $externalFieldSetting = [];

    protected static $country_list = array();

    //产品系统字段
    private static $productFieldMap = [];

    //产品累计总金额字段
    private static $productTotalFieldMap = [];

    protected function getProductFieldMap()
    {
        $map = [
            'product_line' => \Yii::t('field', '序号'),
            'product_no' => \Yii::t('field', '商机-产品编号'),
            'product_name' => \Yii::t('field', '商机-产品名称'),
            'product_model' => \Yii::t('field', '商机-产品型号'),
            'sku_id' => \Yii::t('field', '商机-产品规格'),
            'unit_price' => \Yii::t('field', '商机-产品销售价格'),
            'count' => \Yii::t('field', '商机-产品销售数量'),
            'other_cost' => \Yii::t('field', '商机-产品其他费用'),
            'cost_amount' => \Yii::t('field', '商机-产品销售金额'),
            'product_edit_time' => \Yii::t('field', '商机-产品更新时间'),
            'product_remark' => \Yii::t('field', '备注')
        ];
        return $map;
    }

    protected function getProductTotalFieldMap()
    {
        $map = [
            'product_total_count' => \Yii::t('field', '商机-产品销售总数量'),
            'product_other_amount' => \Yii::t('field', '商机-产品其他总费用'),
            'product_total_amount' => \Yii::t('field', '商机-产品销售总金额'),
        ];
        return $map;
    }


    public static function getDefaultFieldMap(){
        return [
            ['id' => 'main_user_info.nickname' ,'name' => \Yii::t('field', '负责人'), 'type' => \Constants::TYPE_OPPORTUNITY], //主跟进人
            ['id' => 'handler_info.nickname', 'name'  =>\Yii::t('field', '协同跟进人'), 'type' => \Constants::TYPE_OPPORTUNITY], //负责人  main_user_info + handler_info
            ['id' => 'create_user_info.nickname' ,'name' => \Yii::t('field', '创建人'), 'type' => \Constants::TYPE_OPPORTUNITY],
            ['id' => 'department' ,'name' => \Yii::t('field', '归属部门'), 'type' => \Constants::TYPE_OPPORTUNITY],
            ['id' => 'serial_id' ,'name' => \Yii::t('field', '商机编号'), 'type' => \Constants::TYPE_OPPORTUNITY],
            ['id' => 'name' ,'name' => \Yii::t('field', '商机名称'), 'type' => \Constants::TYPE_OPPORTUNITY],
            ['id' => 'company.name' ,'name' => \Yii::t('field', '客户'), 'type' => \Constants::TYPE_COMPANY],
            ['id' => 'customer_id' ,'name' => \Yii::t('field', '关联联系人'), 'type' => \Constants::TYPE_OPPORTUNITY],
            ['id' => 'currency' ,'name' => \Yii::t('field', '币种'), 'type' => \Constants::TYPE_OPPORTUNITY],
            ['id' => 'exchange_rate' ,'name' => \Yii::t('field', '汇率'), 'type' => \Constants::TYPE_OPPORTUNITY],
            ['id' => 'amount' ,'name' => \Yii::t('field', '销售金额'), 'type' => \Constants::TYPE_OPPORTUNITY],
            ['id' => 'stage_type_name' ,'name' => \Yii::t('field', '商机状态'), 'type' => \Constants::TYPE_OPPORTUNITY],
            ['id' => 'sale_flow_name' ,'name' => \Yii::t('field', '销售流程'), 'type' => \Constants::TYPE_OPPORTUNITY],
            ['id' => 'stage_info.name' ,'name' => \Yii::t('field', '销售阶段'), 'type' => \Constants::TYPE_OPPORTUNITY],
            ['id' => 'stage_info.success_rate' ,'name' => \Yii::t('field', '赢率'), 'type' => \Constants::TYPE_OPPORTUNITY],
            ['id' => 'account_date' ,'name' => \Yii::t('field', '结束日期'), 'type' => \Constants::TYPE_OPPORTUNITY],
            ['id' => 'origin_list' ,'name' => \Yii::t('field', '商机来源'), 'type' => \Constants::TYPE_OPPORTUNITY],
            ['id' => 'type' ,'name' => \Yii::t('field', '商机类型'), 'type' => \Constants::TYPE_OPPORTUNITY],
            ['id' => 'fail_stage_info.name' ,'name' => \Yii::t('field', '输单阶段'), 'type' => \Constants::TYPE_OPPORTUNITY],
            ['id' => 'fail_type_name' ,'name' => \Yii::t('field', '输单原因'), 'type' => \Constants::TYPE_OPPORTUNITY],
            ['id' => 'fail_remark' ,'name' => \Yii::t('field', '输单描述'), 'type' => \Constants::TYPE_OPPORTUNITY],
            ['id' => 'create_time' ,'name' => \Yii::t('field', '创建时间'), 'type' => \Constants::TYPE_OPPORTUNITY],
            ['id' => 'update_time' ,'name' => \Yii::t('field', '更新时间'), 'type' => \Constants::TYPE_OPPORTUNITY],
            ['id' => 'edit_time' ,'name' => \Yii::t('field', '资料更新时间'), 'type' => \Constants::TYPE_OPPORTUNITY],
            ['id' => 'trail_time' ,'name' => \Yii::t('field', '动态更新时间'), 'type' => \Constants::TYPE_OPPORTUNITY],
            ['id' => 'stage_stay_time' ,'name' => \Yii::t('field', '当前阶段停留时间'), 'type' => \Constants::TYPE_OPPORTUNITY],
            ['id' => 'remark' ,'name' => \Yii::t('field', '备注'), 'type' => \Constants::TYPE_OPPORTUNITY]
        ];
    }

    /*
     * 获取Excel的表头数据
     * @param  int $   type
     * @param  array  $fields 请求字段信息
     * @return array  excel表头的字段
     * */
    public function getFirstLineData($type, $fields)
    {
        $first_line = array();

        $setting = new UserSetting(Export::$clientId, Export::$userInfo->getUserId(), UserSetting::OPPORTUNITY_EXPORT_MAP);
        $value = $setting->getValue();

        if (!empty($value)) {
            $exportFields = json_decode($value,true);
        } else {
            $exportFields = self::getDefaultFieldMap();
        }

        $externalField = ["id", "name", "require", "hint", "field_type", "disable_flag", "ext_info"];
        //获取商机自定义字段
        $fieldSetting = \common\library\customer\Helper::getExternalFieldSetting(self::$clientId, \Constants::TYPE_OPPORTUNITY, 0, $externalField);
        $externalMap = array_column($fieldSetting,'id','name');

        $index = 0;

        foreach ($exportFields as $field) {
            $fieldId = $field['id'];
            $fieldName = $field['name'];
            $fieldType = $field['type'];
            if (in_array($fieldType,[\Constants::TYPE_OPPORTUNITY, \Constants::TYPE_COMPANY])) {
                //自定义字段
                if (in_array($fieldId, $externalMap)) {
                    $first_line[] = $fieldName; //Excel 输出的表头
                    self::$fieldMap[$fieldId] = $index;
                    self::$externalFieldSetting[] = $fieldId;
                    $index++;
                    continue;
                }
                $first_line[] = $fieldName; //Excel 输出的表头
                self::$fieldMap[$fieldId] = $index;
                $index++;
            }
        }

        //导出是详细版
        if(Export::$invoiceType == \CustomerExportTask::TYPE_DETAILED){
            self::$productTotalFieldMap = $this->getProductTotalFieldMap();
            self::$productFieldMap = $this->getProductFieldMap();

            foreach (self::$productTotalFieldMap as $k => $name){
                $first_line[] = $name;
                self::$fieldMap[$k] = $index;
                $index++;
            }
            foreach (self::$productFieldMap as $k => $name){
                $first_line[] = $name;
                self::$fieldMap[$k] = $index;
                $index++;
            }
        }

        return $first_line;
    }

    public function getExportDataCount($params,$user){
        $listObj = $this->buildSearchParams($params, $user);
        $count = $listObj->count();
        return $count;
    }

    //插入数据
    public function insertTableData(&$fp,&$failCount,$params,$dataCount)
    {
        $userInfo = Export::$userInfo;
        $pageSize = Export::$maxPageSize;
        $taskId = Export::$taskId;
        Speed::log(sprintf("export@insertDataStart:task_id[%s] count[%s]", $taskId,$dataCount));


        $pageCount = ceil($dataCount / $pageSize);
        $pageCount = $pageCount > 0 ? $pageCount : 1;

        for ($i = 1; $i <= $pageCount; $i++) {
            echo $i . '/' . $pageCount;
            echo "\n";
            $curPage = $i;
            $list = $this->findList($params, $userInfo, $curPage, $pageSize);

            if(!$list){
                \LogUtil::info(sprintf("opportunityExport@dataEmpty:task_id[%s] list[%s]", self::$taskId ,count($list)));
                break;
            }
            //获取其他拓展字段信息
            $extInfoData = $this->getExtInfoData($list);
            $this->insertData($fp,$failCount,$list,$extInfoData);
        }
    }

    /**
     * @param $fp
     * @param $failCount
     * @param $list
     * @param $extInfoData
     */
    protected function insertData(&$fp,&$failCount,$list,$extInfoData)
    {

        //获取需要插入的所有字段信息
        $allFieldMap = self::$fieldMap;

        $columnCount = count($allFieldMap);

        //商机销售流程
        $salesFlowList = $extInfoData['sales_flow_list'];
        //商机销售阶段
        $stageList = $extInfoData['stage_list'];
        //来源
        $originList = $extInfoData['origin_list'];
        //公司列表
        //$companyList = $extInfoData['company_list'];
        //部门信息
        $departmentList = $extInfoData['department_list'];


        foreach ($list as $opportunityItem) {

            //如果导出是详细版,根据产品id分行
            if(Export::$invoiceType == \CustomerExportTask::TYPE_DETAILED && isset($opportunityItem['product_list']) && is_array($opportunityItem['product_list'])){

                $opportunityProductList = $opportunityItem['product_list'];

                foreach ($opportunityProductList as  $proIndex => $opportunityProItem){
                    $this->insertOpportunityItem($opportunityItem,$salesFlowList,$stageList,$originList,$departmentList,$allFieldMap,$columnCount,$fp,$failCount,$proIndex);
                }
            }else{
                $this->insertOpportunityItem($opportunityItem,$salesFlowList,$stageList,$originList,$departmentList,$allFieldMap,$columnCount,$fp,$failCount);
            }

        }
    }

    public function insertOpportunityItem($opportunityItem,$salesFlowList,$stageList,$originList,$departmentList,$allFieldMap,$columnCount,&$fp,&$failCount,$proIndex = 0){

        $opportunity_row = array_fill(0, $columnCount, '');
        $opportunity_external_field_map = array_column($opportunityItem['external_field_data'],null,'id');

        foreach(self::$fieldMap as $id => $index) {
            
            // 字段权限
            $noPermission = false;
            $prefix = strpos($id, '.') ? substr($id, 0, strpos($id, '.')) : $id;
            foreach ($opportunityItem['field_privilege_stats'] ?? [] as $fieldPrivilegeStat) {
                if (!in_array($fieldPrivilegeStat['refer_type'], [\Constants::TYPE_OPPORTUNITY, \Constants::TYPE_COMPANY])) {
                    continue;
                }
                if ($prefix == 'company' && $fieldPrivilegeStat['refer_type'] != \Constants::TYPE_COMPANY) {
                    continue;
                }
                $field = match ($prefix) {
                    'handler_info' => 'handler',
                    'company' => substr($id, strrpos($id, '.') + 1),
                    'main_user_info' => 'main_user',
                    'stage_info' => 'stage',
                    'fail_stage_info' => 'fail_stage',
                    'fail_type_name' => 'fail_type',
                    'sales_flow_name' => 'sales_flow',
                    'stage_type_name' => 'stage_type',
                    default => $id,
                };
                if (in_array($field, $fieldPrivilegeStat['disable'] ?? [])) {
                    $opportunity_row[$index] = \Yii::t('privilege', FieldConstant::FIELD_VALUE_EXPORT_MASK);
                    $noPermission = true;
                    break;
                }
            }
            if ($noPermission) {
                continue;
            }
            
            //商机自定义字段
            if (in_array($id, self::$externalFieldSetting)) {
                $extFieldId = $id;
                if(!isset($opportunity_external_field_map[$extFieldId])){
                    continue;
                }
                $extFieldValue = $opportunity_external_field_map[$extFieldId];
                if (is_array($extFieldValue['value']) && $extFieldValue['value']) {
                    $extFieldValue['value'] = join(',', $extFieldValue['value']);
                    //判断下拉的值，是否在系统设置的选项里面
                } else if ($extFieldValue['value'] && $extFieldValue['field_type'] == CustomFieldService::FIELD_TYPE_SELECT) {
                    //对于下拉单选选的历史 删除/修改选项 一并进行导出
                    //$extFieldValue['value'] = Helper::filterExtInfo($extFieldValue['ext_info'], array($extFieldValue['value']));
                } else if (is_array($extFieldValue['value']) && !$extFieldValue['value']) {
                    $extFieldValue['value'] = "";
                }
                //如果匹配到了+,-号，在前面增加'号
                //或者超过15位的纯数字
                if ((is_numeric($extFieldValue['value']) && strlen($extFieldValue['value']) > 15) || str_contains($extFieldValue['value'],'+') || str_contains($extFieldValue['value'],'-')) {
                    $extFieldValue['value'] =  "'".$extFieldValue['value'];
                }

                $opportunity_row[$index] = $extFieldValue['value'] ?? '';
                continue;
            }
            if (!isset($index))
                continue;

            switch ($id) {
                case 'create_time':
                    $opportunity_row[$index] = date('Y/m/d H:i:s',strtotime($opportunityItem[$id]));
                    break;
                case 'create_user_info.nickname':
                    //创建人
                    $opportunity_row[$index] = $opportunityItem['create_user_info']['nickname'] ?? "";
                    break;
                case 'main_user_info.nickname':
                    //负责人
                    $opportunity_row[$index] = $opportunityItem['main_user_info']['nickname'] ?? "";
                    break;
                case 'handler_info.nickname':
                    $mainUser = "";
                    if (isset($opportunityItem['main_user_info']['nickname'])) {
                        $mainUser = $opportunityItem['main_user_info']['nickname'] . "(负责人)";
                    }
                    //团队成员 ，负责人 + 跟进人
                    if ($opportunityItem['handler_info'] && is_array($opportunityItem['handler_info'])) {
                        $handlerInfo = [];
                        foreach ($opportunityItem['handler_info'] as $handlerItem) {

                            if ($handlerItem['role_id'] == 2) {
                                $roleName = $handlerItem['nickname'] . "(可编辑)";
                            } else {
                                $roleName = $handlerItem['nickname'] . "(仅查看与添加动态)";
                            }

                            $handlerInfo[] = $roleName;
                        }
                        $handlerInfo = array_merge(array($mainUser) , $handlerInfo);
                        $handlerInfoStr = implode(';', $handlerInfo);
                    } else {
                        $handlerInfoStr = $mainUser;
                    }
                    $opportunity_row[$index] = $handlerInfoStr;
                    break;
                case 'department':
                    //部门信息,显示根目录
                    $departmentName = "";
                    if (isset($departmentList[$opportunityItem['opportunity_id']])) {
                        $departmentName = $departmentList[$opportunityItem['opportunity_id']]['name'] ?? "";
                    }
                    if ($departmentName) {
                        $departmentName = '我的企业-' . $departmentName;
                    } else {
                        $departmentName = '我的企业';
                    }
                    $opportunity_row[$index] = $departmentName;
                    break;
                //customer => customer_id
                case 'customer_id':
                    //联系人信息
                    $customerData = array_column($opportunityItem['customer'], 'name');
                    $customerData = join(';', $customerData);
                    $opportunity_row[$index] = $customerData;
                    break;
                //stage =stage_type_name
                case 'stage_type_name':
                    //销售阶段
                    $stageTypeName = OpportunityStage::STAGE_LIST_MAP[$opportunityItem['stage_type']];
                    $opportunity_row[$index] = $stageTypeName;
                    break;
                case 'type':
                    //商机类型
                    $typeName = Opportunity::TYPE_MAP[$opportunityItem[$id]] ?? '';
                    $opportunity_row[$index] = $typeName;
                    break;
                //flow_id => sale_flow_name
                case 'sale_flow_name':
                    //商机销售流程
                    $salesFlowName = isset($salesFlowList[$opportunityItem['flow_id']]) ? $salesFlowList[$opportunityItem['flow_id']]['name'] : "";
                    $opportunity_row[$index] = $salesFlowName;
                    break;
                //stage => stage_type_name
                //success_rate => stage_info.success_rate
                case 'stage_info.name':
                    //商机销售阶段
                    $stageName = isset($stageList[$opportunityItem['stage']]) ? $stageList[$opportunityItem['stage']]['name'] : "";
                    $opportunity_row[$index] = $stageName;
                    break;
                case 'stage_info.success_rate':
                    $successRate = isset($stageList[$opportunityItem['stage']]) ? $stageList[$opportunityItem['stage']]['success_rate'] : "0";
                    $successRate .= '%';
                    $opportunity_row[$index] = $successRate;
                    break;
                case 'cus_tag':
                case 'tag':
                    $tag_list = array_column($opportunityItem['tag'] ?? [], 'tag_name');
                    $opportunity_row[$index] = implode(';', $tag_list);
                    break;
                case 'origin':
                case 'origin_list':
                    //来源
                    $originName = $opportunityItem['origin_name'] ?? '';
                    $opportunity_row[$index] = $originName;
                    break;
                //fail_stage_info => fail_stage_info.name
                case 'fail_stage_info.name':
                    //输单阶段
                    $failStageInfo = is_array($opportunityItem['fail_stage_info']) && isset($opportunityItem['fail_stage_info']['name']) ? $opportunityItem['fail_stage_info']['name'] : "";
                    $opportunity_row[$index] = $failStageInfo;
                    break;
                case 'stage_stay_time':
                    //滞留时间
                    $stageStayTime = \Util::formatTimeToDate($opportunityItem[$id]);
                    $opportunity_row[$index] = $stageStayTime;
                    break;
                case array_key_exists($id, self::$productFieldMap):
                    if (Export::$invoiceType == \CustomerExportTask::TYPE_DETAILED && !empty($opportunityItem['product_list'][$proIndex])) {
                        if ($id == 'product_line') {
                            $opportunity_row[$index] = $proIndex + 1;
                            break;
                        }
                        if (isset($opportunityItem['product_list'][$proIndex][$id])) {
                            $opportunity_row[$index] = $this->formatProductField($id, $opportunityItem['product_list'][$proIndex][$id] ?? '', $opportunityItem['product_list'][$proIndex]);
                        } else {
                            $opportunity_row[$index] = $opportunityItem[$id] ?? '';
                        }
                    }
                    break;
                //产品总金额等字段,详细版时候需要导出
                case  in_array($id, ['product_total_count', 'product_other_amount', 'product_total_amount']):
                    if (Export::$invoiceType == \CustomerExportTask::TYPE_DETAILED) {
                        //如果没有商机产品详细信息
                        if (!$opportunityItem['product_list']) {
                            $opportunityItem[$id] = "";
                        }
                        $opportunity_row[$index] = $opportunityItem[$id];
                    }
                    break;
                case 'exchange_rate':
                    if (self::$clientCurrency == "USD") {
                        $opportunity_row[$index] = $opportunityItem['exchange_rate_usd'];
                    } else {
                        $opportunity_row[$index] = $opportunityItem[$id];
                    }
                    break;
                case 'name':
                    if (is_numeric($opportunityItem[$id])) {
                        $item = '`' . $opportunityItem[$id];
                    } else {
                        $item = $opportunityItem[$id];
                    }
                    $opportunity_row[$index] = $item ?? '';
                    break;
                case 'create_type':
                    $opportunity_row[$index] = Opportunity::CREATE_TYPE_MAP[$opportunityItem[$id]] ?? '';
                    break;
                case 'opportunity_trail':
                    $opportunity_row[$index] = !empty($opportunityItem['last_trail']) && !empty($opportunityItem['last_trail']['data']['content']) ? $opportunityItem['last_trail']['data']['content'] : '';
                    break;
                case 'main_lead_id':
                    $opportunity_row[$index] = $opportunityItem['lead_name'] ?? '';
                    break;
                //company_id => company.name
                case 'company.name':
                    //获取公司名称
                    $opportunity_row[$index] = !empty($opportunityItem['company']) ? $opportunityItem['company']['name'] : "已删除";
                    break;
                case 'company.short_name':
                    $opportunity_row[$index] = !empty($opportunityItem['company']) ? $opportunityItem['company']['short_name'] : "";
                    break;
                case 'company.country':
                    $country = !empty($opportunityItem['company']) ? $opportunityItem['company']['country'] : "";
                    if ($country && !array_key_exists($country, self::$country_list)) {
                        $country_data = \CountryService::checkNameInTable($country);
                        if ($country_data) {
                            self::$country_list[$country] = $country_data;
                        }
                    }
                    $opportunity_row[$index] = self::$country_list[$country]['country_name'] ?? "";
                    break;
                case 'company.timezone':
                    $opportunity_row[$index] = !empty($opportunityItem['company']) ? $opportunityItem['company']['timezone'] : "";
                    break;
                case 'company.biz_type':
                    $opportunity_row[$index] = !empty($opportunityItem['company']) ? $opportunityItem['company']['biz_type'] : "";
                    break;
                case 'company.origin':
                case 'company.origin_list':
                    $opportunity_row[$index] = !empty($opportunityItem['company']) ? $opportunityItem['company']['origin_name'] : "";
                    break;
                case 'company.product_group_ids':
                    $product_group_list = !empty($opportunityItem['company']) ? array_column($opportunityItem['company']['product_group_ids'], 'name') : [];
                    $opportunity_row[$index] = implode(';', $product_group_list);
                    break;
                case 'pin_user_list':
                    $opportunity_row[$index] = implode(';',array_column($opportunityItem[$id], 'nickname') ?? []);
                    break;
                default:
                    $opportunity_row[$index] = $opportunityItem[$id] ?? '';
                    break;
            }
        }
        $res = fputcsv($fp, $opportunity_row);
        if ($res == false) {
            $failCount++;
        }

    }

    /**
     * @param $params
     * @param $user
     * @param int $curPage
     * @param int $pageSize
     * @return mixed
     */
    public function findList($params, $user, $curPage = 1, $pageSize = 20)
    {
        $listObj = $this->buildSearchParams($params, $user);
        $listObj->setOffset(($curPage - 1) * $pageSize);
        $listObj->setLimit($pageSize);
        if(Export::$invoiceType == \CustomerExportTask::TYPE_DETAILED){
            $listObj->getFormatter()->setShowProductList(true);
        }
        $listObj->getFormatter()->setShowExternalFieldDetail(true);
        $listObj->getFormatter()->listInfoSetting();
        $listObj->getFormatter()->setListFormatterPrivilege(fieldPrivilegeFormatterFlag: true, objectScene: PrivilegeFieldV2::SCENE_OF_EXPORT);

        $opportunityList = $listObj->find();
        return $opportunityList;
    }


    /** 构造搜索的数据
     * @param $params
     * @param $user
     * @return array|OpportunityList
     */
    protected function buildSearchParams($params, $user)
    {
        $show_all = isset($params['show_all']) && $params['show_all'] ? $params['show_all'] : 1;
        $userType = isset($params['user_type']) && $params['user_type'] ? $params['user_type'] : [];
        $sortField = $params['sort_field'] ?? 'update_time';
        $sortType = $params['sort_type'] ?? 'desc';
        $pin = $params['pin'] ?? '';
        $keyword = $params['keyword'] ?? '';
        $searchField = $params['search_field'] ?? '';
        $minAmount = $params['min_amount'] ?? 0;
        $maxAmount = $params['max_amount'] ?? 0;
        $currency = isset($params['currency']) && $params['currency'] ? $params['currency'] : [];
        $userIdArr = isset($params['user_id']) && $params['user_id'] ? $params['user_id'] : [];
        $stageType = isset($params['stage_type']) && $params['stage_type'] ? $params['stage_type'] : [];
        $stage = isset($params['stage']) && $params['stage'] ? $params['stage'] : [];
        $originList = isset($params['origin_list']) && $params['origin_list'] ? $params['origin_list'] : [];
        $failType = isset($params['fail_type']) && $params['fail_type'] ? $params['fail_type'] : [];
        $failStage = isset($params['fail_stage']) && $params['fail_stage'] ? $params['fail_stage'] : [];
        $typeArr = isset($params['opportunity_type']) && $params['opportunity_type'] ? $params['opportunity_type'] : [];
        $companyId = isset($params['company_id']) && $params['company_id'] ? $params['company_id'] : [];
        $createStartDate = $params['create_start_date'] ?? '';
        $createEndDate = $params['create_end_date'] ?? '';
        $accountStartDate = $params['account_start_date'] ?? '';
        $accountEndDate = $params['account_end_date'] ?? '';
        $editStartDate = $params['edit_start_date'] ?? '';
        $editEndDate = $params['edit_end_date'] ?? '';
        $trailStartDate = $params['trail_start_date'] ?? '';
        $trailEndDate = $params['trailEndDate'] ?? '';
        $orderStartDate = $params['order_start_date'] ?? '';
        $orderEndDate = $params['order_end_date'] ?? '';
        $flowId = $params['flow_id'] ?? '';
        $trailActiveFlag = isset($params['trail_active_flag']) && $params['trail_active_flag'] ? $params['trail_active_flag']: -1;
        $opportunityField = $params['opportunity_field']?? "";
        $statisticType = $params['statistic_type']?? "";
        $statisticField = $params['statistic_field']?? "";
        $statisticStage = $params['statistic_stage']?? "";
        $statisticDepartmentId = $params['statistic_department_id']?? "";
        $statisticCacheKey = $params['statistic_cache_key']?? "";
        $createUserId = isset($params['create_user_id']) && $params['create_user_id'] ? $params['create_user_id'] : [];
        $scene = $params['scene']?? "";
        $sceneData = $params['scene_data']?? "";
        $reportItemUniqueKey = $params['report_item_unique_key']?? "";
        $productEditStartDate =  $params['product_edit_start_date']?? "";
        $productEditEndDate =  $params['product_edit_end_date']?? "";
        $opportunityType = $params['opportunity_type']?? [];
        $createType = $params['create_type']?? null;
        $tags = $params['tags'] ?? [];
        $tagMatchMode = $params['tag_match_mode'] ?? BaseCompanyList::TAG_MATCH_MODE_SINGLE;
        $disableFlag = $params['disable_flag'] ?? null;
        $filters = $params['filters'] ?? [];
        $criteria_type = $params['criteria_type'] ?? 1;
        $criteria = $params['criteria'] ?? '';

        self::info(var_export($params, true));

        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $list = new OpportunityList($clientId);
        $list->setTagMatchMode($tagMatchMode);
        $list->setTags($tags);
        $list->setViewingUserId($userId);
        $list->setCreateUser($createUserId);
        $list->setUserId($userIdArr);
        $list->setUserType($userType);
        $list->setShowAll($show_all);
        $list->setKeyword($keyword, $searchField);
        $list->setType($typeArr);
        $list->setCurrency($currency);
        !empty((array)array_filter($originList)) && $list->setOriginList((array)$originList);
        $list->setAmount($minAmount, $maxAmount);
        $list->setTrailActiveFlag($trailActiveFlag);
        $list->setCompanyIds($companyId);
        $list->setFailType($failType);
        $list->setCreateStartDate($createStartDate);
        $list->setCreateEndDate($createEndDate);
        $list->setAccountStartDate($accountStartDate);
        $list->setAccountEndDate($accountEndDate);
        $list->setEditStartDate($editStartDate);
        $list->setEditEndDate($editEndDate);
        $list->setTrailStartDate($trailStartDate);
        $list->setTrailEndDate($trailEndDate);
        $list->setOrderStartDate($orderStartDate);
        $list->setOrderEndDate($orderEndDate);
        $list->setStage($stage);
        $list->setStageType($stageType);
        $list->setFailStage($failStage);
        $list->setCreateType($createType);
        $list->setReportItemUniqueKey($reportItemUniqueKey);
        $list->setProductEditStartDate($productEditStartDate);
        $list->setProductEditEndDate($productEditEndDate);
        $list->setType($opportunityType);
        $list->setCreateType($createType);
        $list->setFlowId($flowId);
        !is_null($disableFlag) && $list->setDisableFlag($disableFlag);
        !empty($filters) && $list->setFilters($filters,$criteria_type,$criteria);

        //提供给工作台统计数据特殊筛选使用
        if (in_array($scene, [$list::SCENE_STATISTIC_SUCCEED, $list::SCENE_STATISTIC_FAIL, $list::SCENE_STATISTIC_PUSH])) {
            $list = \common\library\opportunity\statistics\Helper::handleStatisticScene($scene, $sceneData,  $list, $clientId);
        }

        $opportunityField = array_filter(json_decode($opportunityField, true) ?? []);
        !empty($opportunityField) && $list->setExternalFields(\Constants::TYPE_OPPORTUNITY, $opportunityField);

        if ($pin) {
            $list->setIsPin($pin);
        }

        if (!empty($params['pin_user_list'])) $list->setPinUserList($params['pin_user_list']);

        if ($statisticType) {
            if (empty($statisticField)) {
                throw new \RuntimeException(\Yii::t('opportunity', 'Field of opportunity statistics cannot be blank'));
            }
            if ($statisticDepartmentId) {
                $userIds = (new \common\library\department\DepartmentMember($clientId))->getMemberUserIds($statisticDepartmentId,true);
                $list->setUserId($userIds);
            }
            if ($show_all) {
                $list->setShowAll(true, PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_VIEW);
            }
            switch ($statisticType) {
                case 'advance':
                    if (empty($statisticStage)) {
                        throw new \RuntimeException(\Yii::t('opportunity', 'Field of sales stage statistics cannot be blank'));
                    }
                    switch ($statisticField) {
                        case 'stay_list':
                            $list->setStage($statisticStage);
                            break;
                        case 'fail_list':
                            $list->setStageType(OpportunityStage::STAGE_FAIL_STATUS);
                            $list->setFailStage($statisticStage);
                            break;
                        case 'conversion_list':
                            $list->setOpportunityIds(\common\library\account\Helper::getClientSearchCache($statisticCacheKey));
                            break;
                        case 'success_list':
                            $list->setStageType(OpportunityStage::STAGE_WIN_STATUS);
                            break;
                        default:
                            throw new \RuntimeException(\Yii::t('opportunity', 'Field of statistics view  error'));
                    }
                    break;
                case 'forecast':
                    switch ($statisticField) {
                        case 'ongoing_opportunity_list':
                            $list->setStageType(OpportunityStage::STAGE_ON_GOING_STATUS);
                            break;
                        case 'win_opportunity_list':
                            $list->setStageType(OpportunityStage::STAGE_WIN_STATUS);
                            break;
                        default:
                            throw new \RuntimeException(\Yii::t('opportunity', 'Field of statistics view  error'));
                    }
                    break;
                case 'origin':
                case 'origin_list':
                    if (empty($origin)) {
                        throw new \RuntimeException(\Yii::t('opportunity', 'Field of source statistics cannot be empty'));
                    }
                    break;
                case 'fail':
                    if (empty($failType)) {
                        throw new \RuntimeException(\Yii::t('opportunity', 'Field of the losing order type statistics cannot be empty'));
                    }
                    $list->setStageType(OpportunityStage::STAGE_FAIL_STATUS);
                    $list->setFailType($failType);
                    break;
                default:
                    throw new \RuntimeException(\Yii::t('opportunity', 'Statistical type parameter error'));
            }
        }

        if ($sortField && in_array($sortType, ['asc', 'desc'])) {
            $list->setOrderBy($sortField);
            $list->setOrder($sortType);
        }

        //OS断约导出需要导出所有账号的数据
        if (isset($params['os_task_id']) && $params['os_task_id']) {
            $list->setSkipPermissionCheck(true);
        }

        return $list;

    }

    public function formatShowMsg($params)
    {
        $conditionString = '';
        return $conditionString;
    }

    //获取其他拓展信息
    public function getExtInfoData($opportunityList)
    {

        $clientId = Export::$clientId;

        //获取部门信息
        $departmentMap = $this->getDepartmentList($clientId,$opportunityList);
        $result['department_list'] = $departmentMap;

        //商机销售阶段列表
        $stageMaps = $this->getStageList($clientId);
        $result['stage_list'] = $stageMaps;

        //获取来源列表
        $originMaps = $this->getOriginList($clientId);
        $result['origin_list'] = $originMaps;


        //商机销售流程
        $salesFlowMaps = $this->getSalesFlowList($clientId);
        $result['sales_flow_list'] = $salesFlowMaps;

        return $result;
    }

    //商机产品
    public function getOpportunityProduct($clientId,$opportunityIds){

        $productListMap = [];

        //商机列表不为空，并且导出是详细版
        if (!empty($opportunityIds) && Export::$invoiceType == \CustomerExportTask::TYPE_DETAILED) {
            $productList = new OpportunityProductList($clientId);
            $productList->setOpportunityIds($opportunityIds);
            foreach ($productList->find() as $product) {
                $productListMap[$product['opportunity_id']][] = $product;
            }
        }
        return $productListMap;
    }

    //客户信息
    public function  getCompanyList($clientId,$companyIds){

        $companyMaps = [];
        if($companyIds){
            $companyIdStr = join(',',$companyIds);
            $sql = "select company_id,name, serial_id as company_serial_id from tbl_company where client_id = {$clientId} and company_id in($companyIdStr)";

            $db = \PgActiveRecord::getDbByClientId($clientId);
            $companyList = $db->createCommand($sql)->queryAll();
            $companyIdsList = array_combine(array_column($companyList,'company_id'),$companyList);
            if($companyList && is_array($companyList)){
                foreach ($companyIds as $companyId){
                    if(isset($companyIdsList[$companyId])){
                        $companyMaps[$companyId] = $companyIdsList[$companyId];
                    }
                }

            }
        }

        return $companyMaps;
    }

    //部门信息
    public function getDepartmentList($clientId,$opportunityList){
        $departmentMap = [];
        $departmentIds = [];
        foreach ($opportunityList as $item){
            if(isset($item['department_info']) && isset($item['department_info']['id'])){
                $departmentMap[$item['opportunity_id']]['id'] = $item['department_info']['id'];
                $departmentIds[] = $item['department_info']['id'];
            }
        }

        $departmentFullNameList = \common\library\department\Helper::getDepartmentFullNameByIds($clientId,$departmentIds);

        foreach ($departmentMap as $opportunity_id => $item){
            if(isset($departmentFullNameList[$item['id']])){
                $departmentMap[$opportunity_id]['name'] = $departmentFullNameList[$item['id']];
            }
        }

        return $departmentMap;

    }

    //销售阶段列表
    public function  getStageList($clientId){
        $api = new StageApi($clientId);
        $stageList = $api->listAll();

        return array_column($stageList, null, 'stage_id');
//        $opportunityStageList = new OpportunityStageList($clientId);
//        $stageList = $opportunityStageList->find();
//        $stageMaps = [];
//        if($stageList && is_array($stageList)){
//            $stageMaps =  array_combine(array_column($stageList,'stage_id'),$stageList);
//        }
//        return $stageMaps;
    }

    //销售流程列表
    public function  getSalesFlowList($clientId){

        $api = new FlowApi($clientId);
        $flows = $api->listAll();
        return array_column($flows, null, 'flow_id');

//
//        $opportunityStageList = new OpportunitySalesFlowList($clientId);
//        $salesFlowList = $opportunityStageList->find();
//        $salesFlowMaps = [];
//        if($salesFlowList && is_array($salesFlowList)){
//            $salesFlowMaps =  array_combine(array_column($salesFlowList,'flow_id'),$salesFlowList);
//        }
//        return $salesFlowMaps;
    }

    //客户来源列表
    public function getOriginList($clientId){

//        $originMaps = [];
//        $originListObj = new OriginList($clientId);
//        $originListObj->getFormatter()->listSetting();
//        $originList = $originListObj->find();
//        if($originList && is_array($originList)){
//            $originMaps = array_combine(array_column($originList,'item_id'),$originList);
//        }
//        return $originMaps;

        $api = new OriginApi($clientId);
        $api->setApiFields([
            'item_id',
            'item_name',
            'color',
            'expand_field',
            'field_type',
            'display_flag'
        ]);
        return array_column($api->listAll(), null, 'item_id');

    }

    protected function formatProductField($fieldKey, $fieldValue, $productSkuInfo): string
    {
        switch ($fieldKey) {
            case 'product_no':
                $fieldValue = $productSkuInfo['sku_code'] ?? '';
                break;

            case 'sku_id':
                $skuStr = [];
                foreach ($productSkuInfo['attributes_info'] ?? [] as $item) {
                    $skuStr[] = ($item['item_name'] ?? '').': '.($item['value']['item_name'] ?? '');
                }
                $fieldValue = implode(";\n", $skuStr);
                break;
        }

        return $fieldValue ?? '';
    }

}
