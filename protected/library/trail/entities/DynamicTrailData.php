<?php
/**
 * Created by <PERSON><PERSON>Storm.
 * User: Tony
 * Date: 18/7/26
 * Time: 上午10:16
 */

namespace common\library\trail\entities;

use common\components\BaseObject;
use common\library\queue_v2\job\AtNotificationJob;
use common\library\queue_v2\QueueService;
use common\library\trail\TrailConstants;

/**
 * @property integer $data_id
 * @property integer $trail_id
 * @property integer $type
 * @property integer $client_id
 * @property integer $user_id
 * @property string  $data
 * @property integer $enable_flag
 * @property string $create_time
 * @property string $update_time
 *
 * Class BaseDynamicTrailData
 * @package common\library\trail\entities
 */
class DynamicTrailData extends BaseObject
{
    protected $_client_id;

    public function __construct($client_id, $id = null)
    {
        $this->_client_id = $client_id;

        if ($id !== null) {
            $this->loadById($id);
        }
    }

    public static function getPrimaryKey()
    {
        return 'trail_id';
    }

    public function getModelClass()
    {
        return \DynamicTrailData::class;
    }

    public function loadById($id)
    {
        $model = static::getModelClass()::model()->find(
            $this->getPrimaryKey() . ' = :id and client_id = :client_id and enable_flag = :enable_flag', [
                ':id' => $id,
                ':client_id' => $this->_client_id,
                ':enable_flag' => TrailConstants::ENABLE_FLAG
            ]
        );

        $this->setModel($model);
        return $this;
    }

    public function beforeSave()
    {
        $this->_attributes['data'] = $this->_attributes['data'] ?? '{}';
        $this->_attributes['update_time'] = $this->_attributes['update_time'] ?? date('Y-m-d H:i:s');

        if ($this->isNew()) {
            $this->_attributes['create_time'] = $this->_attributes['create_time'] ?? date('Y-m-d H:i:s');
        }

        $result = parent::beforeSave();
        return $result;
    }

    public function afterSave()
    {
        $data = json_decode($this->_attributes['data'] ?? '', true);
        if (($data['at_list'] ?? false) && ($data['obj_name'] ?? false))
        {
            $job = new AtNotificationJob($this->client_id, $data['obj_name'], AtNotificationJob::TYPE_DYNAMIC, [$this->trail_id]);
//            $job->handle();
            QueueService::dispatch($job);
        }
        parent::afterSave(); // TODO: Change the autogenerated stub
    }

    public function containAt(): bool
    {
        $data = is_array($this->data) ? $this->data : json_decode($this->data, true);
        return $data && !empty($data['at_list']['at_users']);
    }
}