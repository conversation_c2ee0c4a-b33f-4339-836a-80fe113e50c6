<?php

/**
 * Created by PhpStorm.
 * User: czzhengkw <<EMAIL>>
 * Date: 2017/2/13
 * Time: 上午9:59
 */

namespace common\library\trail\builder;

use common\library\lead\Lead;
use common\library\trail\events\CustomerEmailBuilderEvents;
use common\library\trail\events\MailEvents;
use common\library\trail\Helper;

abstract class LeadTrailBuilder
{
    /**
     * @var Lead
     */
    protected $lead;
    /**
     * @var array
     */
    protected array $leadCustomers;

    /**
     * @var array
     */
    protected $userIds;

    /**
     * @var int
     */
    protected $clientId;

    /**
     * TrailBuilder constructor.
     * @param $leadId
     * @param $clientId
     * @throws \ProcessException
     */
    public function __construct($clientId, $leadId)
    {
        $this->lead = new \common\library\lead\Lead($clientId, $leadId);
        if (!$this->lead->isExist()) {
            throw new \ProcessException('找不到相应的线索');
        }
        $this->clientId = $clientId;
        $this->userIds = $this->getUserIds();
        $this->leadCustomers = $this->getLeadCustomers();
    }

    /**
     * 线索类型
     *
     * @return int
     */
    abstract protected function getTrailType();

    abstract public function buildTrails();

    protected function buildTrail($elem)
    {
        // 邮件动态事件（新）
        try {
            $trail = new MailEvents();
            $trail->setType($this->getTrailType());
            $trail->setClientId($this->clientId);
            $trail->setCreateUser($elem['user_id']);
            $trail->setLeadId($this->lead->lead_id);
            $trail->setLeadCustomerId($elem['customer_id']);
            $trail->setReferId($elem['refer_id']);
            $trail->setUserId($elem['user_id']);
            $trail->setCreateTime($elem['create_time']);
            $trail->run();
        } catch (\RuntimeException $e) {
            \LogUtil::info("邮件动态生成失败，mail_id={$elem['refer_id']} " . $e->getMessage());
            return false;
        }

        return true;
    }

    protected function hasTrail($clientId, $leadId, $leadCustomerId, $referId)
    {
        // 是否生成过邮件动态
        return Helper::hasLeadTrail($this->getTrailType(), $clientId, $leadId, $leadCustomerId, $referId);
    }

    private function getLeadCustomers()
    {
        $list = new \common\library\lead\LeadCustomerList($this->clientId);
        $list->setLeadId($this->lead->lead_id);
        $list->setFields(['customer_id', 'email']);
        return $list->find();
    }

    private function getUserIds()
    {
        return !empty($this->lead->user_id) ? $this->lead->user_id : \common\library\account\Helper::getRealUserIds($this->clientId);
    }
}