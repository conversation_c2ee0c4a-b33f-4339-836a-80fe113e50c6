<?php
/**
 * Created by PhpStorm.
 * User: tony
 * Date: 2020-09-18
 * Time: 10:58
 */


namespace common\library\trail;

use common\components\SearchList;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\statistics\Constant;
use common\library\statistics\foundation\store\ReportGroupCache;
use common\library\statistics\ListReportSelect;
use common\library\util\SqlBuilder;

class FollowupList extends \MysqlList
{
    use ListReportSelect;

    const REFER_TYPE_LIST = [
        \Constants::TYPE_COMPANY,
        \Constants::TYPE_LEAD,
        \Constants::TYPE_OPPORTUNITY
    ];

    protected $clientId;
    protected $type;
    protected $refer_type;
    protected $create_user;
    protected $createTimeStart;
    protected $createTimeEnd;
    protected $company_id;
    protected $lead_id;
    protected $opportunity_id;
    protected $keyword;
    protected $keywordMatchType;
    protected $followupIds;
    protected $trailIds;

    protected $searcher;
    protected $filterFreezeUserFlag = false;
    protected $skipPermissionCheck = false;
    protected $showAll = false;
    protected $showAllPermission;
    protected $viewingUserId;
    protected $userId;
    protected $fields;
    protected $alias = 't1';
    protected $joinAlias = 't2';
    protected $join = '';

    protected $enableFlag = TrailConstants::ENABLE_FLAG;

    public function __construct($clientId)
    {
        $this->clientId = $clientId;
        $this->setOrderBy($this->alias.'.create_time');
        $this->setOrder('desc');

        $this->initFormatter();
    }

    public function initFormatter()
    {
        $this->formatter = new FollowupFormatter($this->clientId);
    }

    public function initSearcher()
    {
        if (!$this->searcher) {
            $this->searcher = new FollowupSearchList();
            $this->searcher->setClientId($this->clientId);
            $this->searcher->setHighlightFields(['content']);
        }
    }

    public function initJoin()
    {
        $alias = $this->alias;

        if (!empty($this->company_id) || !empty($this->lead_id) || !empty($this->opportunity_id) || !empty($this->refer_type)) {
            $this->joinAlias = 't2';
            $joinTable = 'tbl_dynamic_trail';

            $this->join = "LEFT JOIN {$joinTable} AS {$this->joinAlias} ON {$this->joinAlias}.trail_id={$alias}.trail_id";
        }
    }

    public function setShowAll(bool $showAll, $permission = PrivilegeConstants::PRIVILEGE_CRM_FOLLOWUP_VIEW)
    {
        $this->showAll = $showAll;
        $this->showAllPermission = $permission;
    }

    public function setSkipPermissionCheck(bool $skipPermissionCheck)
    {
        $this->skipPermissionCheck = $skipPermissionCheck;
    }

    public function setViewingUserId($viewingUserId)
    {
        $this->viewingUserId = $viewingUserId;
    }

    public function getViewingUserId()
    {
        if (!$this->viewingUserId) {
            throw new \RuntimeException('未指定用户');
        }
        return $this->viewingUserId;
    }

    public function setFields($fields)
    {
        $this->fields = is_array($fields) ? implode(',', $fields) : $fields;
    }

    public function setType($type)
    {
        $this->type = $type;
    }

    public function setReferType($refer_type)
    {
        $refer_type = array_filter($refer_type, function($item) {
            return in_array($item, self::REFER_TYPE_LIST);
        });

        if (count($refer_type)) {
            $this->refer_type = array_unique($refer_type);
        }
    }

    public function setCreateUser($create_user)
    {
        if (!empty($create_user)) {
            $this->create_user = $this->transToArray($create_user, 'is_numeric');
        }

    }

    public function setCreateTimeStart($createTimeStart)
    {
        $this->createTimeStart = $createTimeStart;
    }

    public function setCreateTimeEnd($createTimeEnd)
    {
        $this->createTimeEnd = $createTimeEnd;
    }

    public function setCompanyId($company_id)
    {
        $this->company_id = $company_id;
    }

    public function setLeadId($lead_id)
    {
        $this->lead_id = $lead_id;
    }

    public function setOpportunityId($opportunity_id)
    {
        $this->opportunity_id = $opportunity_id;
    }

    public function setKeyword($keyword, $matchType = SearchList::MATCH_TYPE_DEFAULT)
    {
        $this->keyword = $keyword;
        $this->keywordMatchType = $matchType;
    }

    public function setFollowupIds($followupIds)
    {
        $this->followupIds = $followupIds;
    }

    public function setTrailIds($trailIds)
    {
        $this->trailIds = $trailIds;
    }

    public function setEnableFlag($enableFlag)
    {
        $this->enableFlag = $enableFlag;
    }

    protected function transToArray($data, $callback = null)
    {
        if (is_callable($callback)) {
            return array_unique(array_filter(is_array($data) ? $data : [$data], $callback));
        }
        return array_unique(array_filter(is_array($data) ? $data : [$data]));
    }

    protected function buildUserSql()
    {
        // 跳过用户&角色筛选
        if ($this->skipPermissionCheck) {
            return 0;
        }

        if ($this->showAll) {
            $allManageableUserId = \common\library\privilege_v3\Helper::getPermissionScopeUser($this->clientId, $this->getViewingUserId(),
                $this->showAllPermission, $this->filterFreezeUserFlag, $this->filterFreezeUserFlag);
            if (\common\library\privilege_v3\Helper::CAN_MANAGE_ALL_USER === $allManageableUserId) {
                //查看全部且权限为全公司且不指定查看用户 跳过查询
                if (empty($this->userId)) {
                    return 0;
                } else {
                    $userId = $this->userId;
                }
            } else {
                $allManageableUserId[] = $this->getViewingUserId();
                $userId = $allManageableUserId = array_unique($allManageableUserId);
                if ($this->create_user) {
                    $userId = array_intersect($userId, $this->create_user);
                    //可查看用户为空
                    if (empty($userId)) {
                        return -1;
                    }
                }
            }
        } else {
            $userId = [$this->getViewingUserId()];
        }

        return array_values($userId);
    }

    public function buildParams()
    {
        $this->initJoin();

        $alias = $this->alias ? $this->alias . '.' : '';
        $sql = "{$alias}client_id=:client_id";
        $params[':client_id'] = $this->clientId;
        $userId = $this->buildUserSql();

        $client = \common\library\account\Client::getClient($this->clientId);
        $onSwitch = $client->getExtentAttribute(\common\library\account\Client::EXTERNAL_KEY_PRIVILEGE_SCOPE_USER_SWITCH, 1);

        if (!is_null($this->keyword) && strlen($this->keyword) > 0) {
            //从筛选器获取数据，匹配到当前分页需要返回的id，再查询详情
            $this->buildEsSearch();
            $ids = $this->searcher->findIds();
            if (!$ids) {
                $sql .= ' AND 1=0';
            } else {
                SqlBuilder::buildWhere($alias, 'follow_up_id', $ids, $sql, $params);
            }
        }

        if (!empty($this->type)) {
            $this->type = is_array($this->type) ? $this->type : array($this->type);
            SqlBuilder::buildWhere($alias, 'type', $this->type, $sql, $params);
        }

        if (!empty($this->biz_type)) {
            $this->biz_type = is_array($this->biz_type) ? $this->biz_type : array($this->biz_type);
            SqlBuilder::buildWhere($alias, 'biz_type', $this->biz_type, $sql, $params);
        }

        if (!empty($userId)) {
            $userId = is_array($userId) ? $userId : (array) $userId;
            if ($onSwitch) {
                $sql .= " AND {$alias}scope_user_ids && ARRAY[" . implode(',', $userId) . "]::BIGINT[]";
            } else {
                SqlBuilder::buildWhere($alias, 'create_user', $userId, $sql, $params);
            }
        }

        if (!empty($this->create_user)) {
            $sql .= " AND {$alias}create_user IN (" . implode(',', is_array($this->create_user) ? $this->create_user : (array)$this->create_user) . ')';
        }

        if (!empty($this->begin_time) && !empty($this->end_time)) {
            SqlBuilder::buildDateRange($alias, 'create_time', $this->begin_time, $this->end_time, $sql, $params);
        }

        if (!empty($this->company_id)) {
            SqlBuilder::buildWhere($this->joinAlias.'.', 'company_id', $this->company_id, $sql, $params);
        }

        if (!empty($this->lead_id)) {
            SqlBuilder::buildWhere($this->joinAlias.'.', 'lead_id', $this->lead_id, $sql, $params);
        }

        if (!empty($this->opportunity_id)) {
            SqlBuilder::buildWhere($this->joinAlias.'.', 'opportunity_id', $this->opportunity_id, $sql, $params);
        }

        if (!empty($this->refer_type)) {
            $referSql = [];

            foreach ($this->refer_type as $item) {
                switch ($item) {
                    case \Constants::TYPE_COMPANY:
                        $referSql[] = $this->joinAlias.'.company_id > 0';
                        break;

                    case \Constants::TYPE_LEAD:
                        $referSql[] = $this->joinAlias.'.lead_id > 0';
                        break;

                    case \Constants::TYPE_OPPORTUNITY:
                        $referSql[] = $this->joinAlias.'.opportunity_id > 0';
                        break;
                }
            }

            if (count($referSql) && count($referSql) < 3) {
                $sql .= ' AND (' . implode(' OR ', $referSql) . ')';
            }
        }

        if (!empty($this->createTimeStart) && !empty($this->createTimeEnd)) {
            SqlBuilder::buildDateRange($alias, 'create_time', $this->createTimeStart, $this->createTimeEnd, $sql,$params);
        }

        if (is_numeric($this->enableFlag)) {
            $sql .= " AND {$alias}enable_flag=:enable_flag";
            $params[':enable_flag'] = $this->enableFlag;
        }

        if (!empty($this->followupIds)) {
            SqlBuilder::buildWhere($alias, 'follow_up_id', $this->followupIds, $sql, $params);
        }

        // 统计报表跳转过来的unique_key，需要先查询 tbl_dynamic_trail表 获得 trail_id 来查询 tbl_follow_up
        if (!empty($this->reportItemUniqueKey)) {
            // 兼容过程目标完成值跳转
            $cacheData = (new ReportGroupCache())->getGroupData($this->reportItemUniqueKey);
            $dataType  = $cacheData['type'] ?? null;

            if(($dataType == Constant::DATA_TYPE_ARRAY) && !empty($cacheData['ids'])){
                $sql .= " AND follow_up_id IN (" . implode(",",$cacheData['ids']) . ")";
            } else {
                try {
                    $where = ' 1=1 '.$this->buildReportSelect('follow_up_id', $alias);
                    $sql = "SELECT trail_id FROM tbl_dynamic_trail $this->alias WHERE {$where}";
                    $result = \PgActiveRecord::getDbByClientId($this->clientId)->createCommand($sql)->queryAll(true);
                    if ($result) {
                        $sql = "{$alias}trail_id IN (" . implode(',', array_column($result, 'trail_id')) .")";
                    } else {
                        $sql = ' 0=1 ';
                    }
                } catch (\Exception $e) {
                    $sql = ' 0=1 ';
                }
                //清空params
                $params = [];
            }
        }

        return [$sql, $params];
    }

    //ES搜索器查询
    protected function buildEsSearch()
    {
        $this->initSearcher();

        //名称查询
        if (!empty($this->keyword)) {
            $this->keyword = \Util::escapeDoubleQuoteSql($this->keyword);
            $this->searcher->setKeyword($this->keyword, $this->keywordMatchType);
        }

        if (!empty($this->type)) {
            $this->searcher->setType($this->type);
        }

        if (!empty($this->create_user)) {
            $this->searcher->setCreateUser($this->create_user);
        }

        //ES搜索不分页
        $this->searcher->setLimit(9999);
        $this->searcher->setOffset(0);
    }

    public function find()
    {
        $db = \PgActiveRecord::getDbByClientId($this->clientId);
        list($where, $params) = $this->buildParams();
        $limit = $this->buildLimit();
        $orderBy = $this->buildOrderBy();
        $table = \Followup::model()->tableName();
        $fields = $this->fields ?? '*';
        $alias = empty($this->alias) ? '' : 'as ' . $this->alias;

        $sql = "SELECT {$fields} FROM {$table} {$alias} {$this->join} WHERE {$where} {$orderBy} {$limit}";
        $listData = $db->createCommand($sql)->queryAll(true, $params);
        // 如果有指定字段 说明是特殊的简单查询，不需要走Formatter因为可能没有足够的数据进行format
        if (!empty($this->fields)) {
            return $listData;
        }

        if (isset($this->searcher)) {
            $this->formatter->setHighLightData($this->searcher->getHighlightData());
        }

        $this->formatter->setListData($listData);
        $list = $this->formatter->result();
        return $list;
    }

    public function count()
    {
        $db = \PgActiveRecord::getDbByClientId($this->clientId);
        list($where, $params) = $this->buildParams();
        $table = \Followup::model()->tableName();
        $alias = empty($this->alias) ? '' : 'as ' . $this->alias;

        $sql = "SELECT count(1) FROM {$table} {$alias} {$this->join} WHERE {$where}";
        $count = $db->createCommand($sql)->queryScalar($params);
        return $count;
    }

}
