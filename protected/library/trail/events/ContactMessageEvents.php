<?php

namespace common\library\trail\events;

use common\library\trail\TrailConstants;

class ContactMessageEvents extends AbstractEvents
{
    protected $type_map = [
        TrailConstants::TYPE_CONTACT_MESSAGE_FACEBOOK,
        TrailConstants::TYPE_CONTACT_MESSAGE_WHATSAPP,
        TrailConstants::TYPE_CONTACT_MESSAGE_WHATSAPP_GROUP,
        TrailConstants::TYPE_CONTACT_MESSAGE_WHATSAPP_BUSINESS,
        TrailConstants::TYPE_CONTACT_MESSAGE_TM,
        TrailConstants::TYPE_CONTACT_MESSAGE_WECOM,
        TrailConstants::TYPE_CONTACT_MESSAGE_INSTAGRAM,
        TrailConstants::TYPE_CONTACT_MESSAGE_WHATSAPP_CLOUD,
    ];

    const KEY_PREFIX = 'trail_contact_message:';

    /**
     * 执行聊天记录类型动态生成规则
     */
    public function processRun()
    {
        //记录行为
        $result = null;
        if (! empty($this->opportunity_id)) {
            $result = $this->saveOpportunityDynamicTrail();
        } elseif (! empty($this->lead_id)) {
            $result = $this->saveLeadDynamicTrail();
        } elseif (! empty($this->company_id)) {
            // 搜索满足条件的商机列表
            $opportunity_flag = false;
            $list = $this->opportunityList($this->company_id, $this->customer_id, $this->user_id, $this->client_id);
            foreach ($list as $item) {
                $customer_id = array_intersect($this->customer_id, $item['customer_id']);
                $result = $this->saveOpportunityDynamicTrail($customer_id, $item['opportunity_id']);
                $opportunity_flag = true;
            }

            if (!$result && $opportunity_flag === false) {
                $result = $this->saveCustomerDynamicTrail();
            }
        }

        if ($result) {
            $redis = \RedisService::cache();
            $key = $this->getCacheKey();
            $today = date('Y-m-d 23:59:59');
            $redis->setex($key, strtotime($today) - time(), $this->refer_id);
            \LogUtil::info("ContactMessageEvents_set_lock",[
                'key' => $key,
                'refer_id' => $this->refer_id,
            ]);
        }

        return $result;
    }

    protected function getCacheKey(): string
    {
        $day = date('Ymd', strtotime($this->create_time));
        return self::KEY_PREFIX."{$this->client_id}_{$this->company_id}_{$this->lead_id}_{$this->opportunity_id}_{$this->refer_id}_{$day}";
    }

    public function flushCache(): int
    {
        $key = $this->getCacheKey();
        $redis = \RedisService::cache();
        return $redis->del([$key]);
    }

    /**
     * 合法性验证
     */
    protected function processValidator()
    {
        if (empty($this->refer_id)) {
            $this->message = '请设置对话ID';
            return false;
        }

        return true;
    }

    protected function validateCreateTime(): bool
    {
        if (!empty($this->create_time) && $this->create_time < '1970-01-01 00:00:00') {
            $this->message = '时间格式错误';
            return false;
        }

        return true;
    }


    protected function validator(): bool
    {
        if (!in_array($this->type, $this->type_map)) {
            $this->message = '动态类型参数错误';
            return false;
        }

        if (!$this->validateCustomerId()) {
            return false;
        }

        if (!$this->validateCreateTime()) {
            return false;
        }

        $redis = \RedisService::cache();
        $day = date('Ymd', strtotime($this->create_time));
        $key = self::KEY_PREFIX."{$this->client_id}_{$this->company_id}_{$this->lead_id}_{$this->opportunity_id}_{$this->refer_id}_{$day}";
        $data = $redis->get($key);
//        $data = [];
        if (!empty($data)) {
            $this->message = '当天聊天动态已生成';
            return false;
        } else {
            $today = date('Y-m-d 23:59:59');
            $startTime = date('Y-m-d 00:00:00', strtotime($this->create_time));
            $endTime = date('Y-m-d 23:59:59', strtotime($this->create_time));
            $where = " AND create_time BETWEEN '{$startTime}' AND '{$endTime}'";
            if (!empty($this->company_id)) {
                $where .= " AND company_id={$this->company_id}";
            }
            if (!empty($this->lead_id)) {
                $where .= " AND lead_id={$this->lead_id}";
            }
            if (!empty($this->opportunity_id)) {
                $where .= " AND opportunity_id={$this->opportunity_id}";
            }
            $db = \PgActiveRecord::getDbByClientId($this->client_id);
            $sql = "SELECT trail_id FROM tbl_dynamic_trail WHERE client_id={$this->client_id} {$where} AND type={$this->type} AND refer_id={$this->refer_id} AND enable_flag=1";
            $row = $db->createCommand($sql)->queryScalar();
            if (!empty($row)) {
                $redis->setex($key, strtotime($today) - time(), $this->refer_id);
                $this->message = '当天聊天动态已生成. trailId:'.$row . 'key:' .$key;
                \LogUtil::info("ContactMessageEvents_set_lock",[
                    'key' => $key,
                    'refer_id' => $this->refer_id,
                ]);
                return false;
            }
        }

        return true;
    }
}