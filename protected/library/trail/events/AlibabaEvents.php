<?php
/**
 * Created by PhpStorm.
 * User: Tony
 * Date: 18/7/27
 * Time: 下午2:37
 */

namespace common\library\trail\events;

use common\library\behavior\BehaviorService;
use common\library\behavior\Helper;
use common\library\setting\library\origin\Origin;
use common\library\trail\TrailConstants;

/**
 * Class AlibabaEvents
 * @package common\library\trail\events
 *
 * 阿里巴巴动态事件
 */
class AlibabaEvents extends AbstractEvents
{
    protected $type_map = [
        TrailConstants::TYPE_REMARK_TEL,
        TrailConstants::TYPE_ALIBABA_RECEIVED_TRADE,
        TrailConstants::TYPE_ALIBABA_REPLY_TRADE,
        TrailConstants::TYPE_ALIBABA_MARKETING_TRADE
    ];

    protected int $source = Origin::SYS_ORIGIN_ALIBABA;

    /**
     * 执行跟进类型动态生成规则
     */
    public function processRun()
    {

        if (! empty($this->lead_id)) {
            $result = $this->saveLeadTrail();
        } else {
            $result = $this->saveCustomerTrail();
            \common\library\customer\Helper::updateRemarkTime($this->client_id, $this->user_id, $this->company_id, 0,$this->type, $this->create_time);
        }
        if($this->type == TrailConstants::TYPE_REMARK_TEL){
            \StatisticsService::dynamicRemarkAdd($this->client_id,$this->user_id,1);
        }
        return $result;
    }

    /**
     * 合法性验证
     */
    protected function processValidator()
    {


        return true;
    }

}
