<?php
/**
 * Created by PhpStorm.
 * User: andy
 * Date: 2020/4/27
 * Time: 17:40
 */

namespace common\library\swoole\replication\mysql;

use common\library\swoole\BinlogConsumer;
use common\library\swoole\EchoLogger;
use common\library\swoole\MailConversationCollector;
use common\library\swoole\replication\mysql\json_decode\JsonBinaryDecoderFormatter;
use common\library\swoole\replication\mysql\json_decode\JsonBinaryDecoderService;
use Swoole\Coroutine\Client;
use Swoole\Table;

/**
 * This class only function on 64-bit OS
 * Class MysqlBinlogConnect
 * @package common\library\swoole\replication\mysql
 */
class MysqlBinlogConnect
{
    private const COM_BINLOG_DUMP = 0x12;
    private const COM_REGISTER_SLAVE = 0x15;
    private const COM_BINLOG_DUMP_GTID = 0x1e;

    public const RESTORE_NONE = 0;
    public const RESTORE_REDIS = 1;
    public const RESTORE_APCU = 2;

    public const EVENT_NAME_MAP = [0 => 'UNKNOWN_EVENT', 1 => 'START_EVENT_V3', 2 => 'QUERY_EVENT', 3 => 'STOP_EVENT', 4 => 'ROTATE_EVENT', 5 => 'INTVAR_EVENT', 6 => 'LOAD_EVENT', 7 => 'SLAVE_EVENT', 8 => 'CREATE_FILE_EVENT', 9 => 'APPEND_BLOCK_EVENT', 10 => 'EXEC_LOAD_EVENT', 11 => 'DELETE_FILE_EVENT', 12 => 'NEW_LOAD_EVENT', 13 => 'RAND_EVENT', 14 => 'USER_VAR_EVENT', 15 => 'FORMAT_DESCRIPTION_EVENT', 16 => 'XID_EVENT', 17 => 'BEGIN_LOAD_QUERY_EVENT', 18 => 'EXECUTE_LOAD_QUERY_EVENT', 19 => 'TABLE_MAP_EVENT', 20 => 'PRE_GA_WRITE_ROWS_EVENT', 21 => 'PRE_GA_UPDATE_ROWS_EVENT', 22 => 'PRE_GA_DELETE_ROWS_EVENT', 23 => 'WRITE_ROWS_EVENT_V1', 24 => 'UPDATE_ROWS_EVENT_V1', 25 => 'DELETE_ROWS_EVENT_V1', 26 => 'INCIDENT_EVENT', 27 => 'HEARTBEAT_LOG_EVENT', 28 => 'IGNORABLE_LOG_EVENT', 29 => 'ROWS_QUERY_LOG_EVENT', 30 => 'WRITE_ROWS_EVENT_V2', 31 => 'UPDATE_ROWS_EVENT_V2', 32 => 'DELETE_ROWS_EVENT_V2', 33 => 'GTID_LOG_EVENT', 34 => 'ANONYMOUS_GTID_LOG_EVENT', 35 => 'PREVIOUS_GTIDS_LOG_EVENT',];

    public const UNKNOWN_EVENT = 0;
    public const START_EVENT_V3 = 1;
    public const QUERY_EVENT = 2;
    public const STOP_EVENT = 3;
    public const ROTATE_EVENT = 4;
    public const INTVAR_EVENT = 5;
    public const LOAD_EVENT = 6;
    public const SLAVE_EVENT = 7;
    public const CREATE_FILE_EVENT = 8;
    public const APPEND_BLOCK_EVENT = 9;
    public const EXEC_LOAD_EVENT = 10;
    public const DELETE_FILE_EVENT = 11;
    public const NEW_LOAD_EVENT = 12;
    public const RAND_EVENT = 13;
    public const USER_VAR_EVENT = 14;
    public const FORMAT_DESCRIPTION_EVENT = 15;

    //Transaction ID for 2PC, written whenever a COMMIT is expected.
    public const XID_EVENT = 16;
    public const BEGIN_LOAD_QUERY_EVENT = 17;
    public const EXECUTE_LOAD_QUERY_EVENT = 18;

    // Row-Based Binary Logging // TABLE_MAP_EVENT,WRITE_ROWS_EVENT // UPDATE_ROWS_EVENT,DELETE_ROWS_EVENT
    public const TABLE_MAP_EVENT = 19;

    // MySQL 5.1.5 to 5.1.17,
    public const PRE_GA_WRITE_ROWS_EVENT = 20;
    public const PRE_GA_UPDATE_ROWS_EVENT = 21;
    public const PRE_GA_DELETE_ROWS_EVENT = 22;

    // MySQL 5.1.15 to 5.6.x
    public const WRITE_ROWS_EVENT_V1 = 23;
    public const UPDATE_ROWS_EVENT_V1 = 24;
    public const DELETE_ROWS_EVENT_V1 = 25;

    public const INCIDENT_EVENT = 26;
    public const HEARTBEAT_LOG_EVENT = 27;
    public const IGNORABLE_LOG_EVENT = 28;
    public const ROWS_QUERY_LOG_EVENT = 29;

    // MySQL 5.6.x
    public const WRITE_ROWS_EVENT_V2 = 30;
    public const UPDATE_ROWS_EVENT_V2 = 31;
    public const DELETE_ROWS_EVENT_V2 = 32;

    public const GTID_LOG_EVENT = 33;
    public const ANONYMOUS_GTID_LOG_EVENT = 34;
    public const PREVIOUS_GTIDS_LOG_EVENT = 35;

    public const ROW_EVENT_WRITE = 'write';
    public const ROW_EVENT_UPDATE = 'update';
    public const ROW_EVENT_DELETE = 'delete';

    public const ROW_EVENT_MAP = [self::WRITE_ROWS_EVENT_V1 => self::ROW_EVENT_WRITE, self::WRITE_ROWS_EVENT_V2 => self::ROW_EVENT_WRITE, self::UPDATE_ROWS_EVENT_V1 => self::ROW_EVENT_UPDATE, self::UPDATE_ROWS_EVENT_V2 => self::ROW_EVENT_UPDATE, self::DELETE_ROWS_EVENT_V1 => self::ROW_EVENT_DELETE, self::DELETE_ROWS_EVENT_V2 => self::ROW_EVENT_DELETE,];

    public const DECIMAL = 0;
    public const TINY = 1;
    public const SHORT = 2;
    public const LONG = 3;
    public const FLOAT = 4;
    public const DOUBLE = 5;
    public const NULL = 6;
    public const TIMESTAMP = 7;
    public const LONGLONG = 8;
    public const INT24 = 9;
    public const DATE = 10;
    public const TIME = 11; // MySQL 5.5
    public const DATETIME = 12;
    public const YEAR = 13;
    public const NEWDATE = 14;
    public const VARCHAR = 15;
    public const BIT = 16;
    public const TIMESTAMP2 = 17;
    public const DATETIME2 = 18;
    public const TIME2 = 19;
    public const JSON = 245;
    public const NEWDECIMAL = 246;
    public const ENUM = 247;
    public const SET = 248;
    public const TINY_BLOB = 249;
    public const MEDIUM_BLOB = 250;
    public const LONG_BLOB = 251;
    public const BLOB = 252;
    public const VAR_STRING = 253;
    public const STRING = 254;
    public const GEOMETRY = 255;

    private const DEFAULT_HEARTBEAT = 30;

    const BITMAP_COUNT = [0, 1, 1, 2, 1, 2, 2, 3, 1, 2, 2, 3, 2, 3, 3, 4, 1, 2, 2, 3, 2, 3, 3, 4, 2, 3, 3, 4, 3, 4, 4, 5, 1, 2, 2, 3, 2, 3, 3, 4, 2, 3, 3, 4, 3, 4, 4, 5, 2, 3, 3, 4, 3, 4, 4, 5, 3, 4, 4, 5, 4, 5, 5, 6, 1, 2, 2, 3, 2, 3, 3, 4, 2, 3, 3, 4, 3, 4, 4, 5, 2, 3, 3, 4, 3, 4, 4, 5, 3, 4, 4, 5, 4, 5, 5, 6, 2, 3, 3, 4, 3, 4, 4, 5, 3, 4, 4, 5, 4, 5, 5, 6, 3, 4, 4, 5, 4, 5, 5, 6, 4, 5, 5, 6, 5, 6, 6, 7, 1, 2, 2, 3, 2, 3, 3, 4, 2, 3, 3, 4, 3, 4, 4, 5, 2, 3, 3, 4, 3, 4, 4, 5, 3, 4, 4, 5, 4, 5, 5, 6, 2, 3, 3, 4, 3, 4, 4, 5, 3, 4, 4, 5, 4, 5, 5, 6, 3, 4, 4, 5, 4, 5, 5, 6, 4, 5, 5, 6, 5, 6, 6, 7, 2, 3, 3, 4, 3, 4, 4, 5, 3, 4, 4, 5, 4, 5, 5, 6, 3, 4, 4, 5, 4, 5, 5, 6, 4, 5, 5, 6, 5, 6, 6, 7, 3, 4, 4, 5, 4, 5, 5, 6, 4, 5, 5, 6, 5, 6, 6, 7, 4, 5, 5, 6, 5, 6, 6, 7, 5, 6, 6, 7, 6, 7, 7, 8,];

    protected $writeV1TableIdLength;
    protected $updateV1TableIdLength;
    protected $deleteV1TableIdLength;

    /**
     * http://dev.mysql.com/doc/internals/en/auth-phase-fast-path.html 00 FE
     */
    private $packageOkHeader = [0, 254];
    private $binaryDataMaxLength = 16777215;

    /**
     * @var ServerInfo
     */
    private $serverInfo;

    /**
     * @var Client
     */
    protected $socket;

    /**
     * @var MySQLRepository
     */
    protected $repository;

    protected $slaveId;

    protected $checksum;

    /**
     * @var BinlogCurrent
     */
    protected $binlogCurrent;

    protected $listenDb = [];
    protected $listenDbPrefix = [];

    protected $skipDb = [];

    protected $processSchemas = [];

    protected $tableCache = [];

    protected $tableRegisterInfo = [];

    protected $tableRegisterProcessInfo = [];

    protected $tableToIdMapping = [];

    protected $host;
    protected $port;
    protected $username;
    protected $password;

    protected $queueKey = '';

    /**
     * @var \Predis\Client
     */
    protected $redis;

    /**
     * @var EchoLogger $logger
     */
    protected $logger;

    /**
     * @var Table
     */
    protected $statisticsTable;

    protected $collector;

    public function __construct($dcConfig, $serverConfig = [])
    {
        $this->host = $dcConfig['host'];
        $this->port = $dcConfig['port'];
        $this->username = $dcConfig['username'];
        $this->password = $dcConfig['password'];

        $this->queueKey = $serverConfig['queue_key'] ?? '';

        $this->slaveId = rand(6489, 8964);

        $this->logger = new EchoLogger();
        $this->logger->setPrefix(explode('.', $this->host)[0]);

        $this->logger->info("constructing for {$this->host}");

        $this->collector = new MailConversationCollector();
    }

    public function __destruct()
    {
        if ($this->socket) {
            $this->socket->close();
        }

        $this->logger->info("destructing connection for {$this->host}");
    }

    public function setLogLevel($level)
    {
        $this->logger->setLevel($level);
    }

    public function setRedis($redis)
    {
        $this->redis = $redis;
    }

    public function setStatisticsTable($table)
    {
        $this->statisticsTable = $table;
    }

    protected function statisticsIncr($key)
    {
        if (!$this->statisticsTable)
            return;

        $this->statisticsTable->incr($key, 'count');
    }

    /**
     * @param $restoreType
     * @return bool
     * @throws \Exception
     */
    public function connect($restoreType)
    {
        $this->logger->info("connecting {$this->host} restore type $restoreType");

        $this->repository = new MySQLRepository($this->host, $this->port, $this->username, $this->password);

        $this->checksum = $this->repository->getCheckSum();

        $this->binlogCurrent = new BinlogCurrent($this->host);
        $this->binlogCurrent->setRedis($this->redis);

        if ($restoreType)
        {
            $restoreResult = $restoreType == self::RESTORE_APCU ? $this->binlogCurrent->restore() : $this->binlogCurrent->restoreFromRedis();

            if (!$restoreResult)
            {
                $this->logger->warning("restore {$restoreType} fail.");

                [$binlogFile, $binlogPos, $gtid] = $this->repository->getMasterStatus();

                $this->binlogCurrent->setFilename($binlogFile);
                $this->binlogCurrent->setPosition($binlogPos);
                $this->binlogCurrent->setGtidCollection(GtidCollection::makeCollectionFromString($gtid));
            }
            else
            {
                $this->logger->info("restore success: " . $this->binlogCurrent->getGtidCollection()->toString());
            }
        }
        else
        {
            [$binlogFile, $binlogPos, $gtid] = $this->repository->getMasterStatus();

            $this->logger->info("master status: " . $gtid);

            $this->binlogCurrent->setFilename($binlogFile);
            $this->binlogCurrent->setPosition($binlogPos);
            $this->binlogCurrent->setGtidCollection(GtidCollection::makeCollectionFromString($gtid));

            $this->binlogCurrent->landing();
        }

        if ($this->socket) $this->socket->close();

        $this->socket = new Client(SWOOLE_SOCK_TCP);

        $this->socket->set([
            'timeout' => 5, //总超时，包括连接、发送、接收所有超时
            'connect_timeout' => 5, //连接超时，会覆盖第一个总的 timeout
            'write_timeout' => 10.0,//发送超时，会覆盖第一个总的 timeout
            'read_timeout' => 10.0,//接收超时，会覆盖第一个总的 timeout
            'open_length_check' => true,
            'package_length_func' => function ($data)
            {
                if (strlen($data) < 4) return 0;

                $dataLength = unpack('L', $data[0] . $data[1] . $data[2] . chr(0))[1];
                return $dataLength + 4;
            },
            //'package_body_offset' => 4,
            'package_max_length' => $this->binaryDataMaxLength + 4
        ]);

        if (!$this->socket->connect($this->host, $this->port, 1))
        {
            $this->logger->error("$this->host connect failed. Error: {$this->socket->errCode}");
        }

        $data = $this->socket->recv();

        $this->serverInfo = new ServerInfo($data);

        $this->logger->info($this->host . ': ' . var_export($this->serverInfo->getData(), true));

        $this->authenticate();

        // IMPORTANT!!!
        // Note: if CRC32 is in use, the Event Length is 4 bytes bigger in size.
        // The 4 bytes CRC32 are written at the end of the event (just after the last 'data' byte).
        if ($this->checksum)
            $this->execute("SET @master_binlog_checksum = @@global.binlog_checksum");

        $this->execute("SET @master_heartbeat_period =" . (self::DEFAULT_HEARTBEAT * 1000000000));

        if ($this->registerSlave() !== 0x00)
        {
            $this->logger->error("register slave fail");
            throw new \ProcessException("{$this->host} register fail");
        }

        if ($this->binlogDumpGtid($this->binlogCurrent->getGtidCollection()) !== 0x00)
        {
            $this->logger->error("binlog dump gtid fail");
            throw new \ProcessException("{$this->host} binlog dump gtid fail");
        }
        //$this->binlogDump($this->binlogCurrent->getFilename(), $this->binlogCurrent->getPosition());

        return true;
    }

    public function setListenDBPrefix(array $prefix)
    {
        $this->listenDbPrefix = $prefix;
    }

    public function setListenDb(array $db)
    {
        $this->listenDb = array_merge($this->listenDb, array_combine($db, $db));
    }

    public function register($table, callable $callable)
    {
        $this->skipDb = [];

        $this->tableRegisterInfo[$table] = ['table' => $table, 'modify_fields' => [], 'report_fields' => [], 'callable' => $callable];
    }

    public function registerForQueue($table, array $modifyFields, array $reportFields, $queue)
    {
        $this->skipDb = [];

        $this->tableRegisterInfo[$table] = ['table' => $table, 'modify_fields' => $modifyFields, 'report_fields' => $reportFields, 'queue' => $queue];
    }

    public function getRepository()
    {
        return $this->repository;
    }

    public function getBinlogCurrent()
    {
        return $this->binlogCurrent;
    }

    public function dump()
    {
        $data = $this->socket->recv(5);

        if ($data === false)
        {
            if ($this->socket->errCode == 110)
                return true;

            $this->logger->error("{$this->host} {$this->socket->errCode}, {$this->socket->errMsg}");
            return false;
        }
        else if ($data === '')
        {
            $this->logger->warning("$this->host server side close connection");
            return false;
        }

        $reader = new BinaryDataReader($data);

        /*/
        //$this->printHexData($data);

        $length = $reader->readUInt24();
        $seq = $reader->readUInt8();

        $okPacket = $reader->readUInt8();

        if ($okPacket !== 0)
        {
            $this->logger->warning("$this->host not ok packet. ($okPacket)");
            $this->printHexData($data);
            return true;
        }

        $timestamp = $reader->readUInt32();

        $this->eventType = $reader->readUInt8();

        $serverId = $reader->readUInt32();

        $eventSize = $reader->readUInt32();

        $logPos = $reader->readUInt32();

        $flag = $reader->readUInt16();

        $this->logger->info(sprintf("[%s]%s length: %s seq:%s type: %s pos: %s", date('Y-m-d H:i:s', $timestamp), $serverId, $length, $seq, self::EVENT_NAME_MAP[$this->eventType] ?? $this->eventType, $logPos));
        /*/
        // this part of code has better performance
        $reader->advance(5);

        $timestamp = $reader->readUInt32();

        $this->eventType = $reader->readUInt8();
        //$this->eventType = ord($data[9]);

        $reader->advance(14);

        //$reader->advance(8);

        //$logPos = $reader->readUInt32();

        //$reader->advance(2);
        //*/

        // TODO get event type first, boost performance
        switch ($this->eventType)
        {
            case self::GTID_LOG_EVENT:
                // http://baijiahao.baidu.com/s?id=1647336650617189630
                $this->decodeGtidEvent($reader);
                break;
            case self::TABLE_MAP_EVENT:
                $this->decodeTableMapEvent($reader);
                break;
            case self::FORMAT_DESCRIPTION_EVENT:
                $this->decodeFormatDescriptionEvent($reader);
                break;
            case self::UPDATE_ROWS_EVENT_V1:
                $this->decodeRowEvent($reader, self::ROW_EVENT_MAP[$this->eventType], false, true);
                break;
            case self::WRITE_ROWS_EVENT_V1:
            case self::DELETE_ROWS_EVENT_V1:
                $this->decodeRowEvent($reader, self::ROW_EVENT_MAP[$this->eventType], false, false);
                break;
            case self::UPDATE_ROWS_EVENT_V2:
                $this->decodeRowEvent($reader, self::ROW_EVENT_MAP[$this->eventType], true, true);
                break;
            case self::WRITE_ROWS_EVENT_V2:
            case self::DELETE_ROWS_EVENT_V2:
                $this->decodeRowEvent($reader, self::ROW_EVENT_MAP[$this->eventType], true, false);
                break;
            case self::ROTATE_EVENT:
                [$filename, $logPos] = $this->decodeRotateEvent($reader);
                $this->binlogCurrent->setFilename($filename);
                $this->binlogCurrent->setPosition($logPos);
                break;
            case self::HEARTBEAT_LOG_EVENT:
                //$this->printHexData($data);
                break;
            default:
        }

        $this->statisticsIncr('event');

        return true;
    }

    /**
     * @param string $sql
     * @link https://dev.mysql.com/doc/internals/en/com-query.html
     */
    private function execute(string $sql)
    {
        // indicate seq begin with zero
        $this->socket->send(pack('LC', strlen($sql) + 1, 0x03) . $sql);
        $data = $this->socket->recv();
        return $this->processResponse($data);
    }

    /**
     * @param $data
     * @link https://dev.mysql.com/doc/internals/en/packet-OK_Packet.html
     */
    private function processResponse($data)
    {
        $reader = new BinaryDataReader($data);

        $length = $reader->readUInt24();
        $seq = $reader->readUInt8();
        $header = $reader->readUInt8();

        $log = "$this->host rsp: length($length) seq($seq) ";

        switch ($header)
        {
            case 0x00:
                $log .= "OK Packet";
                break;
            case 0xfe:
                $log .= "EOF Packet";
                break;
            case 0xff:
                $errorCode = $reader->readInt16();
                $marker = $reader->readString(1);
                $state = $reader->readString(5);
                $msg = $reader->readString(null);
                $log .= "ERR Packet: code($errorCode) {$marker}{$state} {$msg}";
                break;
            default:
                $log .= "unknown header $header";
        }

        $this->logger->trace($log);

        return $header;
    }

    /**
     * @link http://dev.mysql.com/doc/internals/en/secure-password-authentication.html#packet-Authentication::Native41
     */
    private function authenticate(): void
    {
        $data = pack('L', self::getCapabilities());
        $data .= pack('L', $this->binaryDataMaxLength);
        $data .= chr(33);
        for ($i = 0; $i < 23; ++$i)
        {
            $data .= chr(0);
        }

        $username = $this->username;
        $password = $this->password;

        $result = sha1($password, true) ^ sha1($this->serverInfo->getSalt() . sha1(sha1($password, true), true), true);

        $data = $data . $username . chr(0) . chr(strlen($result)) . $result;
        $str = pack('L', strlen($data));
        $s = $str[0] . $str[1] . $str[2];
        $data = $s . chr(1) . $data;

        $this->socket->send($data);
        $data = $this->socket->recv();

        $reader = new BinaryDataReader($data);

        $length = $reader->readUInt24();
        $seq = $reader->readUInt8();
        $code = $reader->readUInt8();

        $this->logger->trace(sprintf("$this->host length %s seq %s code %s", $length, $seq, $code));
    }

    /**
     * needs REPLICATION SLAVE privilege
     * @see https://dev.mysql.com/doc/internals/en/com-register-slave.html
     */
    private function registerSlave()
    {
        $username = $this->username;
        $password = $this->password;

        $host = gethostname();
        $hostLength = strlen($host);
        $userLength = strlen($username);
        $passLength = strlen($password);

        $data = pack('l', 18 + $hostLength + $userLength + $passLength);
        $data .= chr(self::COM_REGISTER_SLAVE);
        $data .= pack('V', $this->slaveId);
        $data .= pack('C', $hostLength);
        $data .= $host;
        $data .= pack('C', $userLength);
        $data .= $username;
        $data .= pack('C', $passLength);
        $data .= $password;
        $data .= pack('v', $this->port);
        $data .= pack('V', 0);
        $data .= pack('V', 0);

        $this->socket->send($data);
        $ret = $this->socket->recv();
        return $this->processResponse($ret);
    }

    /**
     * @param $file
     * @param $pos
     * @see https://dev.mysql.com/doc/internals/en/com-binlog-dump.html
     */
    private function binlogDump($file, $pos)
    {
        $data = pack('i', strlen($file) + 11) . chr(self::COM_BINLOG_DUMP);
        $data .= pack('I', $pos);
        $data .= pack('v', 0);
        $data .= pack('I', $this->slaveId);
        $data .= $file;

        $this->socket->send($data);
        $ret = $this->socket->recv();
        return $this->processResponse($ret);
    }


    /*
            00:00:                                    flags
            96:89:3f:01:                              server-id
            0d:00:00:00:                              binlog-filename-len
            00:00:00:00:00:00:00:00:00:00:00:00:00:   binlog-filename
            04:00:00:00:00:00:00:00:                  binlog-pos

            58:00:00:00:                              data-size

            #data
            02:00:00:00:00:00:00:00: n_sids
            a5:72:d2:ec:68:f1:11:e6:9c:0b:38:ea:a7:13:ab:5c: SID1
            01:00:00:00:00:00:00:00: gtid-set-index
            01:00:00:00:00:00:00:00: begin
            03:00:00:00:00:00:00:00: end

            f4:67:59:7b:68:e8:11:e6:b6:d0:38:ea:a7:13:ab:5c: SID2
            01:00:00:00:00:00:00:00: gtid-set-index
            01:00:00:00:00:00:00:00: begin
            03:00:00:00:00:00:00:00  end
     * */
    private function binlogDumpGtid(GtidCollection $collection)
    {
        $this->logger->info("binlog dump gtid " . $collection->toString());

        //$collection = GtidCollection::makeCollectionFromString($gtid);

        $data = pack('l', 26 + $collection->getEncodedLength()) . chr(self::COM_BINLOG_DUMP_GTID);
        $data .= pack('S', 0); // flags
        $data .= pack('L', $this->slaveId); // server id
        $data .= pack('L', 3); // binlog-filename-len
        $data .= (chr(0) . chr(0) . chr(0)); // binlog-filename
        $data .= pack('Q', 4); //binlog-pos
        $data .= pack('I', $collection->getEncodedLength());
        $data .= $collection->getEncoded();

        $this->socket->send($data);
        $ret = $this->socket->recv(60);

        if ($ret === false)
        {
            $this->logger->error("binlog dump gtid fail {$this->socket->errCode} {$this->socket->errMsg}");
            return -1;
        }

        return $this->processResponse($ret);
    }

    /**
     * http://dev.mysql.com/doc/internals/en/capability-flags.html#packet-protocol::capabilityflags
     * https://github.com/siddontang/mixer/blob/master/doc/protocol.txt
     */
    private static function getCapabilities(): int
    {
        $noSchema = 1 << 4;
        $longPassword = 1;
        $longFlag = 1 << 2;
        $transactions = 1 << 13;
        $secureConnection = 1 << 15;
        $protocol41 = 1 << 9;

        return ($longPassword | $longFlag | $transactions | $protocol41 | $secureConnection | $noSchema);
    }

    protected function decodeRotateEvent(BinaryDataReader $reader)
    {
        $pos = $reader->readUInt64();
        $filename = $reader->readString(null);

        return [$filename, $pos];
    }

    protected function decodeGtidEvent(BinaryDataReader $reader)
    {
        $commit_flag = 1 === $reader->readUInt8();
        $sid = unpack('H*', $reader->readString(16))[1];
        $sid = vsprintf('%s%s%s%s%s%s%s%s-%s%s%s%s-%s%s%s%s-%s%s%s%s-%s%s%s%s%s%s%s%s%s%s%s%s', str_split($sid));
        $gno = $reader->readUInt64();

        $this->logger->trace($sid . ':' . $gno);

        $this->binlogCurrent->updateGtid($sid, $gno);
        $this->binlogCurrent->landing();

        $this->logger->trace($this->binlogCurrent->getGtidCollection()->toString());
    }

    protected function decodeFormatDescriptionEvent(BinaryDataReader $reader)
    {
        $binlogVersion = $reader->readUInt16();

        $mysqlVersion = $reader->readString(50);

        $timestamp = $reader->readUInt32();

        // after comment this code, the first element of $eventHeader will be event header length
        // so the index of rest will match the value of event.
        //$eventHeaderLength = $reader->readUint8();

        $eventHeader = $reader->readString(null);

        $this->deleteV1TableIdLength = ord($eventHeader[self::DELETE_ROWS_EVENT_V1]); // 8/6
        $this->updateV1TableIdLength = ord($eventHeader[self::UPDATE_ROWS_EVENT_V1]); // 8/6
        $this->writeV1TableIdLength = ord($eventHeader[self::WRITE_ROWS_EVENT_V1]); // 8/6
    }

    /**
     * @link https://mariadb.com/kb/en/rows_event_v1/
     * @param BinaryDataReader $reader
     * @return void|null
     */
    protected function decodeTableMapEvent(BinaryDataReader $reader)
    {
        // [tableID(6)][flag(2)][schemaLength(1)][schemaName(n)][0x00(1)][tableNameLength(1)][tableName(n)][0x00(1)]
        $tableId = $reader->readUInt48();
        $flag = $reader->readUInt16();
        $schemaLength = $reader->readInt8();
        $schema = $reader->readString($schemaLength);
        $reader->advance(1); // 以[0x00]结束
        $tableNameLength = $reader->readUInt8();
        $table = $reader->readString($tableNameLength);

        if (isset($this->skipDb[$schema][$table])) {
            return;
        }

        if (!isset($this->tableRegisterProcessInfo[$tableId]))
        {
            $this->logger->trace("{$this->host} decode table event on $schema $table");

            $needProcess = false;
            if (array_key_exists($schema, $this->processSchemas)) {
                $needProcess = true;
            } elseif (array_key_exists($schema, $this->listenDb)) {
                $this->processSchemas[$schema] = $schema;
                $needProcess = true;
            } else {
                foreach ($this->listenDbPrefix as $prefix)
                {
                    if (strncasecmp($schema, $prefix, strlen($prefix)) === 0) {
                        $needProcess = true;
                        $this->processSchemas[$schema] = $schema;
                    }
                }
            }

            if (!$needProcess || !array_key_exists($table, $this->tableRegisterInfo))
            {
                $this->skipDb[$schema][$table] = $tableId;
                $this->logger->trace("{$this->host} don't need $schema $table, add to skip list");
                return;
            }

            // 构建tableId=>processInfo和cache
            $tableIdChange = false;
            // if table_open_cache set too small, table_id will be change rapidly
            if (isset($this->tableToIdMapping[$table][$schema]))
            {
                $tableIdChange = true;
                $oldTableId = $this->tableToIdMapping[$table][$schema];
                unset($this->tableRegisterProcessInfo[$oldTableId]);
                unset($this->tableCache[$oldTableId]);
            }

            $this->tableRegisterProcessInfo[$tableId] = $this->tableRegisterInfo[$table];
            $this->tableRegisterProcessInfo[$tableId]['process_modify_fields'] = [];
            $this->tableRegisterProcessInfo[$tableId]['process_report_fields'] = [];
            $this->tableToIdMapping[$table][$schema] = $tableId;

            if ($tableIdChange) {
                $this->logger->info("$schema $table($tableId) table_id change, table process count:" . count($this->tableRegisterProcessInfo));
            } else {
                $this->logger->info("$schema $table($tableId) on watch list, table process count:" . count($this->tableRegisterProcessInfo));
            }
        }
        $reader->advance(1); // [0x00]
        $columnCount = $reader->readLengthEncodedInt();
        if ($columnCount === null) {
            $this->logger->error("$this->host error: column num is null");
            return;
        }

        $now = time();
        if (array_key_exists($tableId, $this->tableCache) && $this->tableCache[$tableId]['column_count'] == $columnCount && ($now - $this->tableCache[$tableId]['timestamp']) < 300) {
            return;
        }

        $this->logger->trace("building $schema $table metadata...");

        $this->tableCache[$tableId] = [
            'schema' => $schema,
            'table' => $table,
            'column_count' => $columnCount,
            'timestamp' => $now,
            'metadata' => []
        ];

        // 记录了表的列的类型，每一列占 1 个字节，总共 columnCount 个字节。如：
        // 0x04 对应 MYSQL_TYPE_FLOAT，0x05 对应 MYSQL_TYPE_DOUBLE，0xfc 对应 MYSQL_TYPE_BLOB 等。
        $columnDef = $reader->readString($columnCount);
        // 记录了表的列的类型的元数据（通常为列的长度和精度），有些列类型没有元数据，有些类型有元数据，根据类型不同，有的用 1 个字节记录，有的用 2 个字节记录。列的元数据解析列值至关重要。
        $columnMeta = $reader->readLengthEncodedString();
        $nullLength = intval(($columnCount + 8) / 7);
        $nullMap = $reader->readString($nullLength);

        $metaReader = new BinaryDataReader($columnMeta);
        $fields = $this->repository->getFields($schema, $table);
        $columnDefLen = strlen($columnDef);
        for ($i = 0; $i < $columnDefLen; ++$i)
        {
            $type = ord($columnDef[$i]);
            $fieldInfo = $fields[$i];
            $metadata = [];

            switch ($type)
            {
                case self::VARCHAR:
                    $metadata = ['max_length' => $metaReader->readUInt16()];
                    break;
                case self::STRING:
                case self::VAR_STRING:
                case self::ENUM:// mysql consider this as string
                case self::SET: // mysql consider this as string
                    $realType = $metaReader->readUInt8();
                    $size = $metaReader->readUInt8();

                    if ($realType == self::ENUM)
                    {
                        $type = $realType;
                        $enumValues = explode(',', str_replace(['enum(', ')', '\''], '', $fieldInfo['COLUMN_TYPE']));

                        $metadata = ['max_length' => $size, 'values' => $enumValues];
                    }
                    else if ($realType == self::SET)
                    {
                        $type = $realType;
                        $setValues = explode(',', str_replace(['set(', ')', '\''], '', $fieldInfo['COLUMN_TYPE']));

                        $metadata = ['max_length' => $size, 'values' => $setValues];
                    }
                    else // @link https://bugs.mysql.com/bug.php?id=37426
                    {
                        $size = (($realType << 4) & 0x300) ^ 0x300 + $size;
                        $metadata = ['max_length' => $size];
                    }
                    break;
                case self::BIT: // can't find any document here, reference to other code's doing.
                    echo "table map  $type \n";
                    $metadata = ['bits' => $metaReader->readUInt8(), 'bytes' => $metaReader->readUInt8()];
                    break;
                case self::FLOAT:
                case self::DOUBLE:
                case self::TIME2:
                case self::TIMESTAMP2:
                case self::DATETIME2:
                case self::JSON:
                case self::BLOB:
                case self::TINY_BLOB:
                case self::MEDIUM_BLOB:
                case self::LONG_BLOB:
                case self::GEOMETRY:
                    $metadata = ['length' => $metaReader->readUInt8()];
                    break;
                case self::DECIMAL:
                case self::NEWDECIMAL:
                    $metadata = ['precision' => $metaReader->readUInt8(), 'decimals' => $metaReader->readUInt8()];
                    break;
                default:
            }

            $columnName = $fieldInfo['COLUMN_NAME'];
            $metadata['unsigned'] = stripos($fieldInfo['COLUMN_TYPE'], 'unsigned') !== false;

            $this->tableCache[$tableId]['metadata'][$i] = [
                'column_name' => $fieldInfo['COLUMN_NAME'],
                'type' => $type,
                'metadata' => $metadata
            ];

            if (in_array($columnName, $this->tableRegisterInfo[$table]['modify_fields']))
            {
                $this->tableRegisterProcessInfo[$tableId]['process_modify_fields'][$i] = $i;
                $this->tableRegisterProcessInfo[$tableId]['process_fields'][$i] = $i;
            }

            if (in_array($columnName, $this->tableRegisterInfo[$table]['report_fields']))
            {
                $this->tableRegisterProcessInfo[$tableId]['process_report_fields'][$i] = $i;
                $this->tableRegisterProcessInfo[$tableId]['process_fields'][$i] = $i;
            }
        }

        $this->logger->trace(sprintf("table_id: %s scheme: %s table: %s column-count: %s column-def-length: %s",
            $tableId, $schema, $table, $columnCount, strlen($columnMeta)));
    }

    /**
     * @link https://dev.mysql.com/doc/internals/en/rows-event.html
     * @link https://dev.mysql.com/doc/internals/en/event-data-for-specific-event-types.html
     * @param BinaryDataReader $reader
     * @param $rowEvent
     * @param bool $v2
     * @param bool $update
     */
    protected function decodeRowEvent(BinaryDataReader $reader, $rowEvent, $v2 = true, $update = true)
    {
        $this->statisticsIncr($this->host . '_row');
        $tableId = $reader->readUInt48();
        $flags = $reader->readUInt16();

        if (!array_key_exists($tableId, $this->tableRegisterProcessInfo)) {
            return;
        }

        if ($v2) {
            $extraDataLength = $reader->readUInt16();
            $extraData = $reader->readString($extraDataLength - 2);
        }

        $columnCount = $reader->readLengthEncodedInt();
        $bitmapLength = intval(($columnCount + 7) / 8);

        // TODO check bit order if map size larger than 1 byte
        // For UPDATE_ROWS_LOG_EVENT, a row matching the first row-image is removed, and the row described by the second row-image is inserted.
        $presentMapOne = $reader->readString($bitmapLength);
        if ($update) {
            $presentMapTwo = $reader->readString($bitmapLength);
        }

        $presentMapOneColumnCount = 0;
        for ($i = 0; $i < strlen($presentMapOne); ++$i) {
            $presentMapOneColumnCount += self::BITMAP_COUNT[ord($presentMapOne[$i])];
        }
        $nullBitmapOneLength = intval(($presentMapOneColumnCount + 7) / 8);
        if ($update)
        {
            $presentMapTwoColumnCount = 0;
            for ($i = 0; $i < strlen($presentMapTwo); ++$i) {
                $presentMapTwoColumnCount += self::BITMAP_COUNT[ord($presentMapTwo[$i])];
            }
            $nullBitmapTwoLength = intval(($presentMapTwoColumnCount + 7) / 8);
        }

        $length = $reader->packetLength();

        if ($this->checksum) {
            $length -= 4;
        }

        $result = [];
        while ($reader->getIndex() < $length)
        {
            $nullMapOne = $reader->readString($nullBitmapOneLength);
            $rowValueOne = $this->readData($tableId, $presentMapOne, $nullMapOne, $reader);

            if ($update) {
                $nullMapTwo = $reader->readString($nullBitmapTwoLength);
                $rowValueTwo = $this->readData($tableId, $presentMapOne, $nullMapTwo, $reader);

                $isModified = empty($this->tableRegisterProcessInfo[$tableId]['process_modify_fields']);
                foreach ($this->tableRegisterProcessInfo[$tableId]['process_modify_fields'] as $fieldIndex)
                {
                    if ($rowValueOne[$fieldIndex] !== $rowValueTwo[$fieldIndex])
                    {
                        $isModified = true;
                        break;
                    }
                }
            } else {
                // 创建/删除 也是修改
                $isModified = true;
            }

            if ($isModified)
            {
                if ($update) {
                    $newValue = $rowValueTwo;
                    $oldValue = $rowValueOne;
                } else {
                    $newValue = $rowValueOne;
                    $oldValue = null;
                }
                if (!empty($this->tableRegisterProcessInfo[$tableId]['process_report_fields']))
                {
                    $newValue = array_intersect_key($newValue, $this->tableRegisterProcessInfo[$tableId]['process_report_fields']);
                    $oldValue = $update ? array_intersect_key($oldValue, $this->tableRegisterProcessInfo[$tableId]['process_report_fields']) : null;
                }

                $result[] = [
                    'new' => $newValue,
                    'old' => $oldValue
                ];

                // 打印
//                echo "-------------------------------------------------------------------------------------\n";
                $tableColumnIndexToNameMap = array_column($this->tableCache[$tableId]['metadata'], 'column_name');
                $resultFormat = [];
//                foreach ($result as $changeItem) {
//                    echo "current_time : " . date('Y-m-d H:i:s') . "\n";
//                    echo sprintf("current_time[%s] changeItem[%s]\n", date('Y-m-d H:i:s'), json_encode($changeItem));
//                    foreach ($changeItem as $key => $data) {
//                        echo "---------------> {$key}\n";
//                        if (empty($data)) {
//                            echo "无数据\n";
//                            continue;
//                        }
//                        foreach ($data as $columnIndex => $value) {
//                            echo "{$tableColumnIndexToNameMap[$columnIndex]} : {$value}\n";
//                        }
//                    }
//                }
//                echo "-------------------------------------------------------------------------------------\n";
            }
        }

        // if modify a field haven't been listen, result will be empty
        if (empty($result))
            return;

        if (array_key_exists('queue', $this->tableRegisterProcessInfo[$tableId]))
            $this->pushQueue($this->tableRegisterProcessInfo[$tableId]['queue'],
                $this->tableRegisterProcessInfo[$tableId]['table'],
                $rowEvent, $this->pretty($tableId, $result));
        else
            call_user_func($this->tableRegisterProcessInfo[$tableId]['callable'], $rowEvent, $this->pretty($tableId, $result));
    }

    protected function readData($tableId, $representMap, $nullMap, BinaryDataReader $reader)
    {
        if (!array_key_exists($tableId, $this->tableCache))
        {
            $this->logger->error( "$this->host table id $tableId not exists.");
            return [];
        }

        $tableScheme = $this->tableCache[$tableId]['metadata'];

        $value = [];

        $nullIndex = 0;
        $index = 0;

        foreach ($tableScheme as ['type' => $type, 'metadata' => $metadata, 'column_name' => $columnName])
        {
            $dataIndex = $index;
            ++$index;
            $bitIndex = intval($dataIndex / 8);

            $valueExist = ord($representMap[$bitIndex]) & (1 << intval($dataIndex % 8));

            if  (!$valueExist)
                continue;

            $nullBitIndex = intval($nullIndex / 8);

            $isNull = ord($nullMap[$nullBitIndex]) & (1 << intval($nullIndex % 8));

            ++$nullIndex;

            if ($isNull)
            {
                $value[$dataIndex] = null;
                continue;
            }

            switch ($type)
            {
                case self::DECIMAL: // can't find any document, process it as NEW DECIMAL
                case self::NEWDECIMAL: // @see https://dev.mysql.com/doc/refman/5.6/en/precision-math-decimal-characteristics.html
                    $value[$dataIndex] = $this->getDecimal($reader, $metadata['precision'], $metadata['decimals']);
                    break;
                case self::TINY:
                    $value[$dataIndex] = $metadata['unsigned'] ? $reader->readUInt8() : $reader->readInt8();
                    break;
                case self::SHORT:
                    $value[$dataIndex] = $metadata['unsigned'] ? $reader->readUInt16() : $reader->readInt16();
                    break;
                case self::LONG:
                    $value[$dataIndex] = $metadata['unsigned'] ? $reader->readUInt32() : $reader->readInt32();
                    break;
                case self::FLOAT:
                    $value[$dataIndex] = $reader->readFloat();
                    break;
                case self::DOUBLE:
                    $value[$dataIndex] = $reader->readDouble();
                    break;
                case self::NULL: // store in null bitmap
                    break;
                case self::TIMESTAMP:
                    $value[$dataIndex] = date('Y-m-d H:i:s', $reader->readUInt32());
                    break;
                case self::LONGLONG:
                    $value[$dataIndex] = $metadata['unsigned'] ? $reader->readUInt64() : $reader->readInt64();
                    break;
                case self::INT24:
                    $value[$dataIndex] = $metadata['unsigned'] ? $reader->readUInt24() : $reader->readInt24();
                    break;
                case self::DATE:
                case self::NEWDATE:
                    // Stored as a 3 byte value where bits 1 to 5 store the day,
                    // bits 6 to 9 store the month and the remaining bits store the year.
                    $date = $reader->readUInt24();
                    if ($date === 0) // 0000-00-00
                    {
                        $value[$dataIndex] = null;
                        break;
                    }

                    $day = $date & 0x1F;
                    $month = ($date >> 5) & 0xF;
                    $year = ($date >> 9) & 0x7FFF;
                    if ($year === 0 || $month === 0 || $day === 0)
                    {
                        $value[$dataIndex] = null;
                        break;
                    }
                    $value[$dataIndex] = (new \DateTime())->setDate($year, $month, $day)->format('Y-m-d');
                    break;
                case self::TIME:
                    $time = $reader->readUInt24();
                    $value[$dataIndex] = sprintf('%s%02d:%02d:%02d', $time < 0 ? '-' : '',
                        $time / 10000, ($time % 10000) / 100, $time % 100);
                    break;
                case self::DATETIME:
                    $datetime = $reader->readUInt64();
                    if ($datetime == 0) // 0000-00-00 00:00:00
                    {
                        $value[$dataIndex] = null;
                        break;
                    }
                    $value[$dataIndex] = \DateTime::createFromFormat('YmdHis', $datetime)->format('Y-m-d H:i:s');
                    if (array_sum(\DateTime::getLastErrors()) > 0)
                        $value[$dataIndex] = null;
                    break;
                case self::YEAR:
                    $year = $reader->readUInt8();
                    $value[$dataIndex] = ($year === 0) ? null : 1900 + $year;
                    break;
                case self::VARCHAR:
                case self::VAR_STRING:
                case self::STRING:
                    $maxLength = $metadata['max_length'];
                    $size = $maxLength > 255 ? $reader->readUInt16() : $reader->readUInt8();
                    $value[$dataIndex] = $reader->readString($size);
                    break;
                case self::BIT:
                    $value[$dataIndex] = $reader->readString($metadata['bytes'] + ($metadata['bits'] ? 1 : 0));
                    break;
                case self::TIMESTAMP2: // @see https://dev.mysql.com/doc/internals/en/date-and-time-data-type-representation.html
                    $timestamp = $reader->readUInt32Be();
                    $value[$dataIndex] = $metadata['length'] ?
                        floatval($timestamp . '.' . $this->getFSP($metadata['length'], $reader)) : $timestamp;
                    break;
                case self::DATETIME2: // @see https://dev.mysql.com/doc/internals/en/date-and-time-data-type-representation.html
                    $datetimeData = $reader->readUInt40Be();
                    $second = $datetimeData & 0x3F; // 6 bits second
                    $minute = ($datetimeData >> 6) & 0x3F; // 6 bits minute
                    $hour = ($datetimeData >> 12) & 0x1F; // 5 bits hour
                    $day = ($datetimeData >> 17) & 0x1F; // 5 bits day
                    $yearMonth = ($datetimeData >> 22) & 0x1FFFF; // 17 bits year*13+month
                    $positive = ($datetimeData >> 39) & 0x1; // 1 bit  sign (1= non-negative, 0= negative)
                    $month = $yearMonth % 13;
                    $year = intval($yearMonth / 13);

                    if ($metadata['length'])
                        $value[$dataIndex] = (new \DateTime())->setDate($year, $month, $day)
                            ->setTime($hour, $minute, $second, $this->getFSP($metadata['length'], $reader, true))
                            ->format('Y-m-d H:i:s.u');
                    else
                        $value[$dataIndex] = (new \DateTime())->setDate($year, $month, $day)
                            ->setTime($hour, $minute, $second)
                            ->format('Y-m-d H:i:s');
                    break;
                case self::TIME2: // @see https://dev.mysql.com/doc/internals/en/date-and-time-data-type-representation.html
                    $timeData = $reader->readUInt24Be();
                    $second = $timeData & 0x3F; // 6 bits second
                    $minute = ($timeData >> 6) & 0x3F; // 6 bits minute
                    $hour = ($timeData >> 12) & 0x3FF; // 10 bits hour

                    $value[$dataIndex] = "$hour:$minute:$second";
                    if ($metadata['length'])
                    {
                        $fsp = $this->getFSP($metadata['length'], $reader);
                        $value[$dataIndex] .= '.' . $fsp;
                    }
                    break;
                case self::JSON:
                    $jsonData = $reader->readString($metadata['length']);
                    $json = new JsonBinaryDecoderService(new BinaryDataReader($jsonData), new JsonBinaryDecoderFormatter());
                    $value[$dataIndex] = $json->parseToString();
                    break;
                case self::ENUM:
                    $enumValue = $reader->readString($metadata['max_length']);
                    $value[$dataIndex] = $metadata['values'][$enumValue - 1] ?? '';
                    break;
                case self::SET:
                    $setValues = $metadata['values'];
                    $setValue = $reader->readString($metadata['max_length']);
                    $finalValues = [];
                    foreach ($setValues as $k => $v)
                    {
                        if ($setValue & (2 ** $k))
                            $finalValues[] = $v;
                    }
                    $value[$dataIndex] = $finalValues;
                    break;
                case self::TINY_BLOB:
                case self::MEDIUM_BLOB:
                case self::LONG_BLOB:
                case self::BLOB:
                    $lengthBytes = $reader->readUIntBySize($metadata['length']);
                    $value[$dataIndex] = $reader->readString($lengthBytes);
                    break;
                case self::GEOMETRY:
                    $lengthBytes = $reader->readUIntBySize($metadata['length']);
                    $value[$dataIndex] = $reader->readString($lengthBytes);
                    break;
            }

            //var_dump($value[$dataIndex]);

            //$reader->summary();
        }

        return $value;
    }

    /**
     *  fs length from 0 to 6
     *  example:
     *  01:01:01.1 fsp value 10
     *  01:01:01.12 fsp value 12
     *  01:01:01.123 fsp value 1230
     *  01:01:01.1234 fsp value 1234
     *  01:01:01.12345 fsp value 123450
     *  01:01:01.123456 fsp value 123456
     */
    protected function getFSP($fsLength, BinaryDataReader $reader, $toMicroSecond = false)
    {
        $bytes = intval(($fsLength + 1) / 2);

        $fsp = null;

        switch ($bytes)
        {
            case 1:
                $fsp = $reader->readUInt8();
                break;
            case 2:
                $fsp = $reader->readUInt16Be();
                break;
            case 3:
                $fsp = $reader->readUInt24Be();
                break;
        }

        if ($fsp !== null)
        {
            if ($fsLength % 2)
                $fsp = (int)($fsp / 10);


            $fsp = $fsp * (10 ** (6 - $fsLength));
        }

        return $fsp;
    }

    /**
     *
     * @see https://github.com/mysql/mysql-server/blob/7d10c82196c8e45554f27c00681474a9fb86d137/strings/decimal.cc
     * @param BinaryDataReader $reader
     * @param $precision
     * @param $decimals
     * @return string
     */
    protected function getDecimal(BinaryDataReader $reader, $precision, $decimals)
    {
        $digitsPerInteger = 9;
        $compressedBytes = [0, 1, 1, 2, 2, 3, 3, 4, 4, 4];
        $integral = $precision - $decimals;
        $unCompIntegral = (int)($integral / $digitsPerInteger);
        $unCompFractional = (int)($decimals / $digitsPerInteger);
        $compIntegral = $integral - ($unCompIntegral * $digitsPerInteger);
        $compFractional = $decimals - ($unCompFractional * $digitsPerInteger);

        $compIntegralSize = $compressedBytes[$compIntegral];
        $compFractionalSize = $compressedBytes[$compFractional];

        $bytes = 4 * ($unCompIntegral + $unCompFractional) + $compIntegralSize + $compFractionalSize;

        $data = $reader->readString($bytes);

        // The very first bit of the resulting byte array is inverted
        // so for positive decimal, the first bit is 1
        if (ord($data[0]) & 0x80 !== 0)
        {
            $mask = 0;
            $res = '';
        }
        else
        {
            $mask = -1; // 0xFFFFFFFFFFFFFFFF for 64bit system
            $res = '-';
        }

        $data[0] = ord($data[0]) ^ 0x80; // revert first bit

        $decimalDataReader = new BinaryDataReader($data);

        if ($compIntegralSize)
            $res .= ($decimalDataReader->readUIntBeBySize($compIntegralSize) ^ $mask);

        for ($i = 0; $i < $unCompIntegral; ++$i)
            $res .= sprintf('%09d', $decimalDataReader->readUInt32Be() ^ $mask);

        $res .= '.';

        for ($i = 0; $i < $unCompFractional; ++$i)
            $res .= sprintf('%09d', $decimalDataReader->readUInt32Be() ^ $mask);

        if ($compFractionalSize > 0)
            $res .= sprintf('%0' . $compFractional . 'd', $decimalDataReader->readUIntBeBySize($compFractionalSize) ^ $mask);

        return $res;
    }

    private function pushQueue($queue, $table, $eventType, $data)
    {
        $this->statisticsIncr($this->host . '_' . $queue);
        $this->statisticsIncr($queue);

        $pushData = [
            'event' => $eventType,
            'table' => $table,
            'data' => $data
        ];

        $queueKey = empty($queue) ? BinlogConsumer::QUEUE_KEY : $queue;


        $res = $this->redis->executeRaw(['XADD', $queueKey, '*', 'binlog_data', json_encode($pushData)]);
        $enablePrint = getenv("enable_print_push_data");
        if ($enablePrint === '1') {
            //打印push_data
            echo sprintf("stream_res: %s  push_data: %s\n", $res, json_encode($pushData));
        }
//        \Util::batchLogInfo([
//            'table' => $table,
//            'queueKey' => $queueKey,
//            'pushData' => json_encode($pushData),
//            'res' => $res
//        ]);
        $this->collector->reportQueueCount($this->redis, $queueKey, 1);

    }

    private function pretty($tableId, $data)
    {
        $metadata = $this->tableCache[$tableId]['metadata'];

        $result = [];

        foreach ($data as $elem)
        {
            $new = $elem['new'];
            $old = $elem['old'] ?: [];

            $datum = [];

            foreach ($new as $key => $value)
                $datum['new'][$metadata[$key]['column_name']] = $value;

            foreach ($old as $key => $value)
                $datum['old'][$metadata[$key]['column_name']] = $value;

            $result[] = $datum;
        }

        return $result;
    }

    private function printHexData($data)
    {
        for ($i = 0; $i < strlen($data); ++$i)
        {
            echo sprintf('%02X ', ord($data[$i]));
            if (($i + 1) % 8 == 0)
                echo "\n";
        }
        echo "\n";
    }
}
