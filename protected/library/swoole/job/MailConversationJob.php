<?php

namespace common\library\swoole\job;

use common\library\mail\Mail;
use common\library\mail_conversation\ConversationConstants;
use common\library\mail_conversation\Helper;
use common\library\queue_v2\job\BaseJob;
use common\library\queue_v2\job\JsonJobFactoryTrait;
use common\library\queue_v2\QueueConstant;
use common\library\report\error\ErrorReport;
use common\library\search\SearchApi;
use common\library\swoole\MailConversationBinlogConsumer;
use <PERSON><PERSON>\Queue\Job\JobFactoryContract;
/**
 * 批量消费binlog维护会话表
 */
class MailConversationJob extends BaseJob  implements JobFactoryContract
{
    use JsonJobFactoryTrait;
    public $channel = QueueConstant::CONNECTION_NAME_BINLOG_MAIL;
    public $tag = QueueConstant::CONSUMER_TAG_MAIL_CONVERSATION_TBL_MAIL;
    public $maxExceptions = 2;

    public $timeout = 500;
    public $maxTimeout = 1800;
    public $maxTries = 3;
    private $tableName = ConversationConstants::CONVERSATION_TABLE_NAME;
    private $debug = ConversationConstants::DEBUG;
    private $mode ='error';

    private $clientId;
    private $table;
    private $event;
    private $db;
    //会话最大标签数量
    const MAX_TAG_NUM = 50;
    //会话最大联系人数量
    const MAX_CONTACT_EMAIL_NUMS = 50;
    //会话子邮件数据
    const MAIN_FOLDER_ID = [
        \Mail::FOLDER_INBOX_ID
    ];


    /**
     * 事件
     */

    //    WRITE_ROWS_EVENT：包含了要插入的数据
    //    UPDATE_ROWS_EVENT：包含了修改前的值，也包含了修改后的值
    //    DELETE_ROWS_EVENT：包含了需要删除行前的值
    const EVENT_WRITE = 'write';
    const EVENT_UPDATE = 'update';
    const EVENT_DELETE = 'delete';

    /**
     * 表名
     */
    const TABLE_MAIL = 'tbl_mail';
    const TABLE_PIN = 'tbl_pin';
    const TABLE_MAIL_TAG_ASSOC = 'tbl_mail_tag_assoc';
    const TABLE_MAIL_TODO = 'tbl_mail_todo';
    private $messageIds = '';
    public $config;


    /**
     * @param $clientId
     * @param $mailId
     * @param $event
     * @param $table
     * @param $changFields
     * @throws \Exception
     */
    public function __construct($config)
    {
        $this->config = $config;
    }

    public function handle()
    {
        if (empty($this->config) || !is_array($this->config)) {
            return null;
        }

        $binlogData = $this->config;
        $this->table = $binlogData['table'] ?? '';
        $this->clientId = $binlogData['client_id'] ?? 0;
        $data = $binlogData['binlog_data'] ?? [];

        if (empty($this->table)  || empty($this->clientId) || empty($data)) {
            return false;
        }

        switch ($this->table) {
            case self::TABLE_MAIL:
                $this->batchProcessMailChangeData($data);
                break;
            case self::TABLE_PIN:
                $this->batchProcessPinChangeData($data);
                break;
            case self::TABLE_MAIL_TAG_ASSOC:
               $this->batchProcessTagChangeData($data);
                break;
            case self::TABLE_MAIL_TODO:
                $this->batchProcessTodoChangeData($data);
                break;
            default:
                break;
        }

        return true;
    }


    private function log($message)
    {
        if ($this->debug) {
//            \LogUtil::debug($message,[
//                "traceId" => $this->messageIds
//            ]);
            echo sprintf("[%s][%s]%s\n", date("Y-m-d H:i:s"), 'I', $message);
        }
    }

    //只处理用户操作或者收发件规则导致的邮件变更
    private function batchProcessMailChangeData($data)
    {
        //获取字段变更
        list($conversationMailTypeChangeField, $allConversationIds) = $this->buildChangeField($data);
        if (empty($conversationMailTypeChangeField)) {
            $this->log('conversationMailTypeChangeField is empty');
            return false;
        }

        $this->log('conversationMailTypeChangeField:' . json_encode($conversationMailTypeChangeField));

        if (empty($allConversationIds)) {
            $this->log('allConversationIds is empty');
            return false;
        }

        try {
            $this->db = \ProjectActiveRecord::getDbByClientId($this->clientId);
        }catch (\Throwable $e) {
            $this->db = null;
            $this->errorReport($e->getMessage(),$e);
            return false;
        }

        //剪枝比对
        list($updateConversationDataMap,$needProcessConversationIds, $needProcessConversationIdFiledMap,
            $needProcessLastConversationIds, $needProcessLastReceiveConversationIds,
            $needProcessLastSendConversationIds,$needRecoverConversationIds)
            = $this->pruneAndCompare($conversationMailTypeChangeField, $allConversationIds);

        $this->log('updateConversationDataMap:' . json_encode($updateConversationDataMap));
        $this->log('needProcessConversationIds:' . json_encode($needProcessConversationIds));
        $this->log('needProcessConversationIdFiledMap:' . json_encode($needProcessConversationIdFiledMap));
        $this->log('needProcessLastConversationIds:' . json_encode($needProcessLastConversationIds));
        $this->log('needProcessLastReceiveConversationIds:' . json_encode($needProcessLastReceiveConversationIds));
        $this->log('needProcessLastSendConversationIds:' . json_encode($needProcessLastSendConversationIds));
        $this->log('needRecoverConversationIds:' . json_encode($needRecoverConversationIds));

        //更新会话数据
        $this->saveConversation($updateConversationDataMap,$needProcessConversationIds,
            $needProcessConversationIdFiledMap, $needProcessLastConversationIds, $needProcessLastReceiveConversationIds,
            $needProcessLastSendConversationIds,$needRecoverConversationIds);
    }


    private function buildChangeField($data)
    {
        $conversationMailTypeChangeField = [];
        $allConversationIds = [];

        foreach ($data as $datum) {

            $changeDataArr = $datum['data'] ?? [];
            if (empty($changeDataArr)) {
                continue;
            }

            //遍历每一个消息数据
            //data 里的数据是一个数组，里面包含了old和new
            foreach ($changeDataArr as $changeData) {

                $new = $changeData['new'] ?? [];
                $old = $changeData['old'] ?? [];

                if (empty($new) && empty($old)) {
                    continue;
                }

                $conversationId = $new['conversation_id'] ?? 0;
                $userMailId = $new['user_mail_id'] ?? 0;
                $receiveTime = $new['receive_time'] ?? '';
                $mailId = $new['mail_id'] ?? 0;
                $mailType = $new['mail_type'] ?? 0;
                $updateTime = $new['update_time'] ?? 0;


                //只处理conversation不为空的数据，屏蔽掉为空场景的使用
                //conversation_id==0的场景就是调用java接口清除了，一般这种先更新delete_flag=1或者fodler_id=6,已经被消费了，这时候过滤
                if ($conversationId == 0 || $mailId == 0 || $mailType == 0 || $userMailId == 0
                    || empty($receiveTime) || empty($updateTime)) {
                    continue;
                }

                //删除邮件的变动不处理
                if (($old['delete_flag'] ?? 0) == 1 && ($new['delete_flag'] ?? 0) == 1) {
                    continue;
                }

                //解绑换绑不处理
                if (($old['user_id'] ?? 0) != ($new['user_id'] ?? 0)) {
                    continue;
                }

                //conversation_id从0变成了其他值，不处理
                if (($old['conversation_id'] ?? 0) == 0 && ($new['conversation_id'] ?? 0) != 0) {
                    continue;
                }


                $isChange = false;

                //并且old new 都不等于0 只考虑conversation_id变更
                if (($old['conversation_id'] ?? 0) != ($new['conversation_id'] ?? 0)
                    && ($old['conversation_id'] ?? 0) != 0 && ($new['conversation_id'] ?? 0) != 0) {

                    $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['conversation_id'] = $new['conversation_id'] ?? 0;
                    $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['delete_flag'] = $new['delete_flag'] ?? 0;
                    $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['folder_id'] = $new['folder_id'] ?? 0;
                    $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['relate_company_flag'] = $new['relate_company_flag'] ?? 0;
                    $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['attach_flag'] = $new['attach_flag'] ?? 0;
                    $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['subject'] = $new['subject'] ?? '';
                    $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['read_flag'] = $new['read_flag'] ?? 1;
                    $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['archive_flag'] = $new['archive_flag'] ?? 1;

                    if ($mailType == \Mail::MAIL_TYPE_RECEIVE) {
                        $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['sender'] = $new['sender'] ?? '';
                        $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['urgent_flag'] = ($new['urgent_flag'] ?? 0) > 0 ? 1 : 0;
                        $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['reply_flag'] = ($new['reply_flag'] ?? 0) > 0 ? 1 : 0;
                        $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['forward_flag'] = ($new['forward_flag'] ?? 0) > 0 ? 1 : 0;
                    }

                    $isChange = true;
                }

                // 判断这一批有重复的mail_id，如果有重复的mail_id，就要判断时间，拿最新的时间的字段值来更新
                $isExist = isset($conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['update_time']);
                $mailUpdateTimeInt = $updateTime;
                $updateTimeInt = 0;
                if ($isExist) {
                    $updateTimeInt = $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['update_time'];
                }

                //folder_id 移入垃圾箱，移入自定义文件夹
                if (($old['folder_id'] ?? 0) != ($new['folder_id'] ?? 0)) {

                    if (!isset($conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['folder_id'])
                        || ($isExist && ($mailUpdateTimeInt > $updateTimeInt))) {


                        $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['folder_id'] = $new['folder_id'] ?? 0;
                        //移入垃圾箱或者从垃圾箱移入自定义文件夹，一般要把原来的字段加进来
                        if (($new['folder_id']??0) == \Mail::FOLDER_JUNK_ID
                            || ($old['folder_id']??0) == \Mail::FOLDER_JUNK_ID ) {

                            $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['relate_company_flag'] = $new['relate_company_flag'] ?? 0;
                            $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['subject'] = $new['subject'] ?? '';
                            $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['archive_flag'] = $new['archive_flag'] ?? 1;

                            if ($mailType == \Mail::MAIL_TYPE_RECEIVE) {
                                $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['sender'] = $new['sender'] ?? '';
                                $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['read_flag'] = $new['read_flag'] ?? 1;
                                $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['urgent_flag'] = ($new['urgent_flag'] ?? 0) > 0 ? 1 : 0;
                                $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['reply_flag'] = ($new['reply_flag'] ?? 0) > 0 ? 1 : 0;
                                $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['forward_flag'] = ($new['forward_flag'] ?? 0) > 0 ? 1 : 0;

                                //垃圾邮箱恢复场景
                                if (($old['folder_id']??0) == \Mail::FOLDER_JUNK_ID) {
                                    $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['junk_folder_id'] = \Mail::FOLDER_JUNK_ID;
                                }
                            }
                            $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['attach_flag'] = $new['attach_flag'] ?? 0;
                        }

                    }
                    $isChange = true;
                }


                //delete_flag 删除或者恢复
                if (($old['delete_flag'] ?? 0) != ($new['delete_flag'] ?? 0)) {
                    if (!isset($conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['delete_flag'])
                        || ($isExist && ($mailUpdateTimeInt > $updateTimeInt))) {

                        $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['delete_flag'] = $new['delete_flag'] ?? 0;
                        //删除或者恢复场景，一般要把原来的字段加进来
                        $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['folder_id'] = $new['folder_id'] ?? 0;
                        $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['relate_company_flag'] = $new['relate_company_flag'] ?? 0;
                        $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['attach_flag'] = $new['attach_flag'] ?? 0;
                        $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['subject'] = $new['subject'] ?? '';
                        $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['read_flag'] = $new['read_flag'] ?? 1;
                        $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['archive_flag'] = $new['archive_flag'] ?? 1;

                        if ($mailType == \Mail::MAIL_TYPE_RECEIVE) {
                            $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['sender'] = $new['sender'] ?? '';
                            $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['urgent_flag'] = ($new['urgent_flag'] ?? 0) > 0 ? 1 : 0;
                            $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['reply_flag'] = ($new['reply_flag'] ?? 0) > 0 ? 1 : 0;
                            $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['forward_flag'] = ($new['forward_flag'] ?? 0) > 0 ? 1 : 0;
                        }

                        $isChange = true;
                    }
                }

                //附件有一个附件就是有附件
                if (($old['attach_flag'] ?? 0) != ($new['attach_flag'] ?? 0)) {
                    if (!isset($conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['attach_flag'])
                        || ($isExist && ($mailUpdateTimeInt > $updateTimeInt))) {
                        $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['attach_flag'] = $new['attach_flag'] ?? 0;
                        $isChange = true;
                    }
                }

                //relate_company_flag
                if (($old['relate_company_flag'] ?? 0) != ($new['relate_company_flag'] ?? 0)) {
                    if (!isset($conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['relate_company_flag'])
                        || ($isExist && ($mailUpdateTimeInt > $updateTimeInt))) {

                        $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['relate_company_flag'] = $new['relate_company_flag'] ?? 0;
                        $isChange = true;
                    }
                }

                //archive_flag
                if (($old['archive_flag'] ?? -1) != ($new['archive_flag'] ?? -1)) {
                    if (!isset($conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['archive_flag'])
                        || ($isExist && ($mailUpdateTimeInt > $updateTimeInt))) {
                        $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['archive_flag'] = $new['archive_flag'] ?? 0;
                        $isChange = true;
                    }
                }

                if ($mailType == \Mail::MAIL_TYPE_RECEIVE) {

                    if (($old['urgent_flag'] ?? 0) != ($new['urgent_flag'] ?? 0)) {
                        //当前时间比上一个更新要新
                        if (!isset($conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['urgent_flag'])
                            || ($isExist && ($mailUpdateTimeInt > $updateTimeInt))) {
                            $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['urgent_flag'] = ($new['urgent_flag'] ?? 0) > 0 ? 1 : 0;
                            $isChange = true;
                        }

                    }

                    if (($old['reply_flag'] ?? 0) != ($new['reply_flag'] ?? 0)) {
                        //当前时间比上一个更新要新
                        if (!isset($conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['reply_flag'])
                            || ($isExist && ($mailUpdateTimeInt > $updateTimeInt))) {
                            $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['reply_flag'] = ($new['reply_flag'] ?? 0) > 0 ? 1 : 0;
                            $isChange = true;
                        }
                    }

                    if (($old['forward_flag'] ?? 0) != ($new['forward_flag'] ?? 0)) {
                        //当前时间比上一个更新要新
                        if (!isset($conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['forward_flag'])
                            || ($isExist && ($mailUpdateTimeInt > $updateTimeInt))) {
                            $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['forward_flag'] = ($new['forward_flag'] ?? 0) > 0 ? 1 : 0;
                            $isChange = true;
                        }
                    }

                    if (($old['read_flag'] ?? -1) != ($new['read_flag'] ?? -1)) {
                        if (!isset($conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['read_flag'])
                            || ($isExist && ($mailUpdateTimeInt > $updateTimeInt))) {
                            $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['read_flag'] = $new['read_flag'] ?? 0;
                            $isChange = true;
                        }
                    }

                }

                //有变化才需要时间
                if ($isChange) {

                    if (!isset($conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['receive_time'])
                        || ($isExist && ($mailUpdateTimeInt > $updateTimeInt))) {

                        $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['mail_id'] = $mailId;
                        $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['update_time'] = $new['update_time'] ?? 0;
                        $conversationMailTypeChangeField[$conversationId][$mailType][$mailId]['receive_time'] = $new['receive_time']??'1970-01-01 09:00:00.000';
                    }

                    $allConversationIds[] = $conversationId;
                }
            }
        }

        return [$conversationMailTypeChangeField, $allConversationIds];

    }

    //支持多个conversation_id在同一个job消费
    //支持同个mail_id在同一个job多次消费
    private function batchProcessPinChangeData($data)
    {
        $newMailIdArr = [];
        $deleteMailIdArr = [];
        $pinType = 0;
        foreach ($data as $datum)
        {

            $changeDataArr = $datum['data'] ?? [];
            $event = $datum['event'] ?? '';
            if (empty($changeDataArr) || empty($event)) {
                continue;
            }

            //遍历每一个消息数据
            //data 里的数据是一个数组，里面包含了old和new
            foreach ($changeDataArr as $changeData) {
                $new = $changeData['new'] ?? [];
                $old = $changeData['old'] ?? [];

                if (empty($new) && empty($old)) {
                    continue;
                }

                $mailId = ($old['refer_id'] ??0)?: ($new['refer_id'] ?? 0);
                $clientId = ($old['client_id'] ??0)?: ($new['client_id'] ?? 0);
                $pinType = ($old['type'] ??0)?: ($new['type'] ?? 0);

                if (empty($mailId) || empty($clientId) || empty($pinType)) {
                    continue;
                }

                if ($event == self::EVENT_WRITE) {
                    $newMailIdArr[] = $mailId;
                } elseif ($event == self::EVENT_DELETE) {
                    $deleteMailIdArr[] = $mailId;
                }

            }
        }

        $allMailIds = array_merge($newMailIdArr,$deleteMailIdArr);
        if (empty($allMailIds)) {
            return false;
        }

        try {
            $this->db = \ProjectActiveRecord::getDbByClientId($this->clientId);
        }catch (\Throwable $e) {
            $this->db = null;
            $this->errorReport($e->getMessage(),$e);
        }

        if (empty($this->db)) {
            $this->log('db is empty');
            return false;
        }


        //新版删除不会清空conversation
        $mailConversationIds = $this->getConversationIdsByMailIds($allMailIds);

        if (empty($mailConversationIds)) {
            return false;
        }

        $mailIdConversationIdMap = array_column($mailConversationIds,'conversation_id','mail_id');
        $allConversationIds = array_unique(array_values($mailIdConversationIdMap));
        $mailConversationDataMap = Helper::getMailConversationDataByConversationIds($this->clientId,$allConversationIds);


        //过滤需要更新数据的的会话id
        $mailConversationData = [];
        //新增，只要原来未打钉，那就是=1
        if (!empty($newMailIdArr)) {

            foreach ($newMailIdArr as $newMailId) {
                $conversationId = $mailIdConversationIdMap[$newMailId]??0;
                if ($conversationId == 0) {
                    continue;
                }

                if ($pinType == \Pin::TYPE_MAIL) {

                    //如果是新增，会话如果是已经打钉，就无须再计算了
                    $pinFlag = $mailConversationDataMap[$conversationId]['pin_flag'] ?? 0;
                    if ($pinFlag == 1) {
                        continue;
                    }
                    $mailConversationData[$conversationId]['pin_flag'] = 1;

                } elseif ($pinType == \Pin::TYPE_MAIL_TOP) {

                    //如果是新增，会话如果是已经置顶，就无须再计算了
                    $topFlag = $mailConversationDataMap[$conversationId]['top_flag'] ?? 0;
                    if ($topFlag == 1) {
                        continue;
                    }
                    $mailConversationData[$conversationId]['top_flag'] = 1;

                } else {
                    continue;
                }

                if (isset($mailConversationDataMap[$conversationId]['user_id'])) {
                    $mailConversationData[$conversationId]['user_id'] = $mailConversationDataMap[$conversationId]['user_id']??0;
                }

                if (isset($mailConversationDataMap[$conversationId]['user_mail_id'])) {
                    $mailConversationData[$conversationId]['user_mail_id'] = $mailConversationDataMap[$conversationId]['user_mail_id']??0;
                }
            }
        }

        //删除需要判断是否还有打钉的邮件
        $needProcessConversationIds = [];
        if (!empty($deleteMailIdArr)) {
            foreach ($deleteMailIdArr as $deleteMailId) {

                $conversationId = $mailIdConversationIdMap[$deleteMailId]??0;
                if ($conversationId == 0) {
                    continue;
                }

                if ($pinType == \Pin::TYPE_MAIL) {

                    //这个会话id的打钉,还没打钉，就跳过
                    $pinFlag = $mailConversationDataMap[$conversationId]['pin_flag']??0;
                    if ($pinFlag == 0) {
                        continue;
                    }
                } elseif ($pinType == \Pin::TYPE_MAIL_TOP) {

                    //这个会话id的置顶,还没置顶，就跳过
                    $topFlag = $mailConversationDataMap[$conversationId]['top_flag']??0;
                    if ($topFlag == 0) {
                        continue;
                    }
                } else {
                    continue;
                }

                $needProcessConversationIds[] = $conversationId;
            }

            if (!empty($needProcessConversationIds)) {

                $needProcessConversationIds = array_unique($needProcessConversationIds);

                foreach ($needProcessConversationIds as $needProcessConversationId) {

                    if ($pinType == \Pin::TYPE_MAIL) {
                        $ret  = \common\library\mail_conversation\Helper::getPinFlagByConversationId($this->db,$needProcessConversationId);
                        $mailConversationData[$needProcessConversationId]['pin_flag'] = $ret > 0 ? 1 : 0;
                    } else if ($pinType == \Pin::TYPE_MAIL_TOP) {
                        $ret = \common\library\mail_conversation\Helper::getTopFlagByConversationId($this->db,$needProcessConversationId);
                        $mailConversationData[$needProcessConversationId]['top_flag'] = $ret > 0 ? 1 : 0;
                    } else {
                        continue;
                    }

                    if (isset($mailConversationDataMap[$needProcessConversationId]['user_id'])) {
                        $mailConversationData[$needProcessConversationId]['user_id'] = $mailConversationDataMap[$needProcessConversationId]['user_id']??0;
                    }

                    if (isset($mailConversationDataMap[$needProcessConversationId]['user_mail_id'])) {
                        $mailConversationData[$needProcessConversationId]['user_mail_id'] = $mailConversationDataMap[$needProcessConversationId]['user_mail_id']??0;
                    }
                }
            }
        }

        //批量更新数据
        if (!empty($mailConversationData)) {
            \common\library\mail_conversation\Helper::batchUpsertConversationDataOneByOne($this->clientId, $this->db, $mailConversationData);
        }

    }

    private function batchProcessTagChangeData($data)
    {
        $newMailIdArr = [];
        $deleteMailIdArr = [];
        foreach ($data as $datum)
        {

            $changeDataArr = $datum['data'] ?? [];
            $event = $datum['event'] ?? '';
            if (empty($changeDataArr) || empty($event)) {
                $this->log("tbl_mail_tag_assoc表的数据为空");
                continue;
            }

            //遍历每一个消息数据
            //data 里的数据是一个数组，里面包含了old和new
            foreach ($changeDataArr as $changeData) {
                $new = $changeData['new'] ?? [];
                $old = $changeData['old'] ?? [];

                if (empty($new) && empty($old)) {
                    $this->log("new和old都为空");
                    continue;
                }

                $mailId = ($old['mail_id'] ??0)?: ($new['mail_id'] ?? 0);
                $clientId = ($old['client_id'] ??0)?: ($new['client_id'] ?? 0);
                $tagId = ($old['tag_id'] ??0)?: ($new['tag_id'] ?? 0);

                if (empty($mailId) || empty($clientId) || empty($tagId)) {
                    $this->log("mailId或者clientId或者tagId为空");
                    continue;
                }

                if ($event == self::EVENT_WRITE) {
                    $newMailIdArr[$mailId][] = $tagId;
                } elseif ($event == self::EVENT_DELETE) {
                    $deleteMailIdArr[$mailId][] = $tagId;
                }

            }
        }

        $allMailIds = array_merge(array_keys($newMailIdArr),array_keys($deleteMailIdArr));

        if (empty($allMailIds)) {
            $this->log("allMailIds为空");
            return false;
        }

        try {
            $this->db = \ProjectActiveRecord::getDbByClientId($this->clientId);
        }catch (\Throwable $e) {
            $this->db = null;
            $this->errorReport($e->getMessage(),$e);
        }

        if (empty($this->db)) {
            $this->log('db is empty');
            return false;
        }

        //先求出这一批mailId的会话id
        $mailConversationIds = $this->getConversationIdsByMailIds($allMailIds);

        if (empty($mailConversationIds)) {
            $this->log("mailConversationIds为空");
            return false;
        }

        $mailIdConversationIdMap = array_column($mailConversationIds,'conversation_id','mail_id');

        $allConversationIds = array_unique(array_values($mailIdConversationIdMap));
        $mailConversationDataMap = Helper::getMailConversationDataByConversationIds($this->clientId,$allConversationIds);


        //过滤需要更新数据的的会话id
        $mailConversationData = [];
        if (!empty($newMailIdArr)) {

            foreach ($newMailIdArr as $newMailId => $tagIds) {

                $conversationId = $mailIdConversationIdMap[$newMailId]??0;
                if ($conversationId == 0) {
                    continue;
                }

                if (empty($tagIds)) {
                    continue;
                }

                //同一个会话，判断是否存在,存在则更新的data合并
                if (isset($mailConversationData[$conversationId]['tag_ids'])) {
                    $tagIdArr = $mailConversationData[$conversationId]['tag_ids']??[];
                    // 新增直接去重加
                    $tagIds = array_diff($tagIds,$tagIdArr);
                    //有新增，则合并
                    if (!empty($tagIds)) {
                        $tagIds = array_merge($tagIdArr,$tagIds);
                    }

                } else {
                    //这个会话id的标签 不在这个标签里面，就需要处理
                    $tagIdArr = $mailConversationDataMap[$conversationId]['tag_ids']??'[]';
                    $conversationDataTagIds = json_decode($tagIdArr,true);
                    if (!is_array($conversationDataTagIds)) {
                        $conversationDataTagIds = [];
                    }

                    // 新增直接去重加
                    $tagIds = array_diff($tagIds,$conversationDataTagIds);
                    //有新增，则合并
                    if (!empty($tagIds)) {
                        $tagIds = array_merge($conversationDataTagIds,$tagIds);
                    }
                }

                if (!empty($tagIds) && is_array($tagIds)) {
                    //tagIds，都转为字符串
                    $tagIds = array_map(function ($tagId) {
                        return (string)$tagId;
                    }, $tagIds);

                    $mailConversationData[$conversationId]['tag_ids'] = array_unique($tagIds);

                    if (isset($mailConversationDataMap[$conversationId]['user_id'])) {
                        $mailConversationData[$conversationId]['user_id'] = $mailConversationDataMap[$conversationId]['user_id']??0;
                    }

                    if (isset($mailConversationDataMap[$conversationId]['user_mail_id'])) {
                        $mailConversationData[$conversationId]['user_mail_id'] = $mailConversationDataMap[$conversationId]['user_mail_id']??0;
                    }
                }

            }
        }


        $needProcessConversationIds = [];
        if (!empty($deleteMailIdArr)) {
            foreach ($deleteMailIdArr as $deleteMailId => $tagIds) {
                $conversationId = $mailIdConversationIdMap[$deleteMailId]??0;
                if ($conversationId == 0) {
                    continue;
                }
                $needProcessConversationIds[] = $conversationId;
            }

            if (!empty($needProcessConversationIds)) {
                $needProcessConversationIds = array_unique($needProcessConversationIds);
                foreach ($needProcessConversationIds as $needProcessConversationId) {

                    $tagIds =  \common\library\mail_conversation\Helper::getTagIdsByConversationId($this->db,$needProcessConversationId);
                    $mailConversationData[$needProcessConversationId]['tag_ids'] = $tagIds;

                    if (isset($mailConversationDataMap[$needProcessConversationId]['user_id'])) {
                        $mailConversationData[$needProcessConversationId]['user_id'] = $mailConversationDataMap[$needProcessConversationId]['user_id']??0;
                    }

                    if (isset($mailConversationDataMap[$needProcessConversationId]['user_mail_id'])) {
                        $mailConversationData[$needProcessConversationId]['user_mail_id'] = $mailConversationDataMap[$needProcessConversationId]['user_mail_id']??0;
                    }

                }
            }
        }

        //批量更新数据
        if (!empty($mailConversationData)) {
            \common\library\mail_conversation\Helper::batchUpsertConversationDataOneByOne($this->clientId, $this->db, $mailConversationData);
        }

    }

    private function batchProcessTodoChangeData($data)
    {
        if (empty($data)) {
            return false;
        }

        $newMailIdArr = [];
        $deleteMailIdArr = [];
        foreach ($data as $datum)
        {

            $changeDataArr = $datum['data'] ?? [];
            $event = $datum['event'] ?? '';
            if (empty($changeDataArr) || empty($event)) {
                continue;
            }

            //遍历每一个消息数据
            //data 里的数据是一个数组，里面包含了old和new
            foreach ($changeDataArr as $changeData) {
                $new = $changeData['new'] ?? [];
                $old = $changeData['old'] ?? [];

                if (empty($new) && empty($old)) {
                    continue;
                }

                $mailId = ($old['mail_id'] ??0)?: ($new['mail_id'] ?? 0);
                $clientId = ($old['client_id'] ??0)?: ($new['client_id'] ?? 0);
                $completedFlag = ($old['completed_flag'] ??null)?: ($new['completed_flag'] ?? null);

                if (empty($mailId) || empty($clientId) || is_null($completedFlag)) {
                    continue;
                }

                //新增待办
                if ($event == self::EVENT_WRITE) {
                    $newMailIdArr[] = $mailId;
                    //从待办变成已办 待办变已办都要查询
                } else if($event == self::EVENT_UPDATE){

                    //从待办变成已办
                    if (($old['completed_flag']??-1) == 0 && ($new['completed_flag']??-1) == 1) {
                        $deleteMailIdArr[] = $mailId;
                        //已办变待办相当于新增
                    } else if (($old['completed_flag']??-1) == 1 && ($new['completed_flag']??-1) == 0) {
                        $newMailIdArr[] = $mailId;
                    }

                } else if ($event == self::EVENT_DELETE) {

                    //判断old的completed_flag为1，删除已办则过滤
                    if (($old['completed_flag']??-1) == 1 && ($new['completed_flag']??-1) == 0) {
                        continue;
                    }

                    $deleteMailIdArr[] = $mailId;
                }

            }
        }

        $allMailIds = array_merge($newMailIdArr,$deleteMailIdArr);

        if (empty($allMailIds)) {
            return false;
        }

        try {
            $this->db = \ProjectActiveRecord::getDbByClientId($this->clientId);
        }catch (\Throwable $e) {
            $this->db = null;
            $this->errorReport($e->getMessage(),$e);
        }

        if (empty($this->db)) {
            $this->log('db is empty');
            return false;
        }

        //先求出这一批mailId的会话id
        $mailConversationIds = $this->getConversationIdsByMailIds($allMailIds);

        if (empty($mailConversationIds)) {
            return false;
        }

        $mailIdConversationIdMap = array_column($mailConversationIds,'conversation_id','mail_id');
        $allConversationIds = array_unique(array_values($mailIdConversationIdMap));
        $mailConversationDataMap = Helper::getMailConversationDataByConversationIds($this->clientId,$allConversationIds);

        //过滤需要更新数据的的会话id
        $mailConversationData = [];
        if (!empty($newMailIdArr)) {

            foreach ($newMailIdArr as $newMailId) {
                $conversationId = $mailIdConversationIdMap[$newMailId]??0;
                if ($conversationId == 0) {
                    continue;
                }

                //这个会话id的已经是待办 则无需更新
                $todoFlag = $mailConversationDataMap[$conversationId]['todo_flag']??0;
                if ($todoFlag == 1) {
                    continue;
                }

                $mailConversationData[$conversationId]['todo_flag'] = 1;

                if (isset($mailConversationDataMap[$conversationId]['user_id'])) {
                    $mailConversationData[$conversationId]['user_id'] = $mailConversationDataMap[$conversationId]['user_id']??0;
                }

                if (isset($mailConversationDataMap[$conversationId]['user_mail_id'])) {
                    $mailConversationData[$conversationId]['user_mail_id'] = $mailConversationDataMap[$conversationId]['user_mail_id']??0;
                }
            }
        }

        $needProcessConversationIds = [];
        if (!empty($deleteMailIdArr)) {
            foreach ($deleteMailIdArr as $deleteMailId) {
                $conversationId = $mailIdConversationIdMap[$deleteMailId]??0;
                if ($conversationId == 0) {
                    continue;
                }

                //这个会话id的没有待办，删除了也不影响
                $todoFlag = $mailConversationDataMap[$conversationId]['todo_flag']??0;
                if ($todoFlag == 0) {
                    continue;
                }
                $needProcessConversationIds[] = $conversationId;
            }

            if (!empty($needProcessConversationIds)) {

                $needProcessConversationIds = array_unique($needProcessConversationIds);

                foreach ($needProcessConversationIds as $needProcessConversationId) {
                    $ret = \common\library\mail_conversation\Helper::getTodoFlagByConversationId($this->db, $needProcessConversationId);
                    $mailConversationData[$needProcessConversationId]['todo_flag'] = $ret > 0 ? 1 : 0;

                    if (isset($mailConversationDataMap[$needProcessConversationId]['user_id'])) {
                        $mailConversationData[$needProcessConversationId]['user_id'] = $mailConversationDataMap[$needProcessConversationId]['user_id']??0;
                    }

                    if (isset($mailConversationDataMap[$needProcessConversationId]['user_mail_id'])) {
                        $mailConversationData[$needProcessConversationId]['user_mail_id'] = $mailConversationDataMap[$needProcessConversationId]['user_mail_id']??0;
                    }
                }
            }
        }

        //批量更新数据,一般是看到已经会话了，有user_mail_id以及user_id，没必要再去查询
        if (!empty($mailConversationData)) {
            \common\library\mail_conversation\Helper::batchUpsertConversationDataOneByOne($this->clientId, $this->db, $mailConversationData);
        }
    }


    //获取这一批邮件的会话数据
    //过滤删除，垃圾箱以及解绑，已删除以及
    //恢复之后，可能这边的打定会受影响
    private function getConversationIdsByMailIds(array $mailIdArr)
    {
        if (empty($mailIdArr)) {
            return '';
        }
        $mailIdStr = implode(',',$mailIdArr);

        $sql = "SELECT conversation_id,mail_id FROM  tbl_mail  FORCE INDEX(PRIMARY) WHERE mail_id  in ($mailIdStr)";
        try{
            $this->log("getConversationIdsByMailIds sql:".$sql);
            $conversationIds = $this->db->createCommand($sql)->queryAll();
        }catch (\Throwable $e) {
            $this->errorReport($e->getMessage(),$e);
            return [];
        }
        return $conversationIds?:[];
    }


    //批量获取所有的会话属性
    private function getAttachReadFolderRelateFiledDataByConversationIds($conversationIds,$needProcessConversationIdFiledMap)
    {
        if (empty($conversationIds)) {
            return [];
        }

        //过滤为0的场景
        $conversationIds = array_filter($conversationIds);

        //1.降级处理，count一下，设置处理子邮件yuzhi，快速处理积压问题
        //2.丢到low 消费组，不影响正常消费

        $conversationIdstr = implode(',',$conversationIds);
        //垃圾箱 跟delete_flag=0不计算
        $junkFolderId =  \Mail::FOLDER_JUNK_ID;

        $fields = [];
        //对field做一次剪枝，有需要的才需要去查询
        if (empty($needProcessConversationIdFiledMap)) {
            $fields = [
                'conversation_id',
                'min(read_flag) as read_flag',
                'min(archive_flag) as archive_flag',
                'GROUP_CONCAT(DISTINCT folder_id) AS folder_ids',
                'GROUP_CONCAT(distinct relate_company_flag) as contact_types',
                'max(attach_flag) as attach_flag',
                'count(mail_id) as mail_count',
                'mail_type',
                'urgent_flag',
                'reply_flag',
                'forward_flag',
                'sender',
                'subject',
                'receive_time',
                'mail_id'
            ];
        } else {
            //取并集
            foreach ($needProcessConversationIdFiledMap as $conversationId => $item) {

                $item = array_unique($item);

                foreach ($item as $keyValue) {
                    if ($keyValue == 'read_flag') {
                        $fields[] = 'min(read_flag) as read_flag';
                    } elseif ($keyValue == 'archive_flag') {
                        $fields[] = 'min(archive_flag) as archive_flag';
                    } elseif ($keyValue == 'folder_id') {
                        $fields[] = 'GROUP_CONCAT(DISTINCT folder_id) AS folder_ids';
                    } elseif ($keyValue == 'relate_company_flag') {
                        $fields[] = 'GROUP_CONCAT(distinct relate_company_flag) as contact_types';
                    } elseif ($keyValue == 'attach_flag') {
                        $fields[] = 'max(attach_flag) as attach_flag';
                    } elseif ($keyValue == 'last_mail_receive_time') {
                        $fields[] = 'max(receive_time) as last_mail_receive_time';
                    } elseif ($keyValue == 'mail_count') {
                        $fields[] = 'count(mail_id) as mail_count';
                    } elseif ($keyValue == 'sender') {
                        $fields[] = 'sender';
                    }
                }
                //去重
                if (!empty($fields)) {
                    $fields = array_unique($fields);
                }
            }

            //默认的字段
            $fields = array_merge($fields,[
                'conversation_id as mail_conversation_id',
                'mail_type',
                'subject',
                'receive_time',
                'urgent_flag',
                'reply_flag',
                'forward_flag',
                'mail_id'
            ]);

            if (!empty($fields)) {
                $fields = array_unique($fields);
            }
        }

        $fieldStr = implode(',',$fields);
        $sql = "select {$fieldStr} from tbl_mail where 
                conversation_id in ({$conversationIdstr}) and delete_flag=0 and folder_id!={$junkFolderId} and user_id!=0  group by conversation_id";
        $this->log("getAttachReadFolderRelateFiledDataByConversationIds sql:".$sql);
        try {
            $data =  $this->db->createCommand($sql)->queryAll();
        }catch (\Throwable $e) {
            $this->errorReport($e->getMessage(),$e);
            return [];
        }

        return $data?:[];
    }



    private function deleteConversationByConversationIds($conversationIds)
    {
        if (empty($conversationIds)) {
            return false;
        }

        $conversationIdStr = implode(',',$conversationIds);
        $table = $this->tableName;
        $deleteSql = "update {$table} set delete_flag=1  where mail_conversation_id in({$conversationIdStr})";

        try {
            $res =  $this->db->createCommand($deleteSql)->execute();
            $this->log("deleteConversationByConversationIds deleteSql:".$deleteSql);
        }catch (\Throwable $e) {
            $this->errorReport($e->getMessage(),$e);
            return false;
        }

        try {
            $api = SearchApi::mailConversation($this->clientId);
            $res = $api->delete( $conversationIds);
        }catch (\Throwable $e) {
            $this->errorReport($e->getMessage(),$e);
            return false;
        }
    }

    private function errorReport($message, $e = null)
    {
        if ($this->mode == 'error') {
            if (is_array($message)) {
                $message = json_encode($message);
            }

            if (!is_null($e)) {
                \common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());
            } else {
                \common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null, new \CException($message)), $message, null);
            }

            throw new \ProcessException($message);
        }
    }

    private function processConversationIds(array $needProcessConversationIds,array $needProcessConversationIdFiledMap,
                                            array $needProcessLastConversationIds, array $needProcessLastReceiveConversationIds,
                                            array $needProcessLastSendConversationIds,array $updateConversationDataMap,
                                            array $needRecoverConversationIds)
    {
        if (empty($needProcessConversationIds)) {
            return false;
        }

        //过滤重复的会话id
        $needProcessConversationIds = array_unique($needProcessConversationIds);

        $newConversationData = $this->getAttachReadFolderRelateFiledDataByConversationIds($needProcessConversationIds,$needProcessConversationIdFiledMap);

        $this->log("processConversationIds newConversationData:".json_encode($newConversationData));

        //删除查不到子邮件的会话，todo 直接set mail_count=0
        if (empty($newConversationData)) {
            //直接删除所有的会话
            $this->deleteConversationByConversationIds($needProcessConversationIds);
            //如果还是有更新的数据，就需要更新
            if(!empty($updateConversationDataMap)) {
                \common\library\mail_conversation\Helper::batchUpsertConversationDataOneByOne($this->clientId, $this->db, $updateConversationDataMap);
            }
            return true;
        } else {
            $validConversationIds = array_column($newConversationData,'mail_conversation_id');
            $notHaveSubMailConversationIds = array_diff($needProcessConversationIds,$validConversationIds);
        }

        if (!empty($notHaveSubMailConversationIds)) {
            $this->deleteConversationByConversationIds($notHaveSubMailConversationIds);
        }

        $mailConversationDataMap = [];
        foreach($newConversationData as $item) {

            $conversationId = $item['mail_conversation_id']??0;
            if ($conversationId == 0) {
                continue;
            }

            //如果有在收件箱,如果需要查询子邮件才需要重算下,或者需要更新folder_ids的时候都需要维护下mail_folder_id
            if ((isset($item['folder_ids']) && !isset($item['main_folder_id']))) {
                if (is_array($item['folder_ids']??'')) {
                    array_intersect(self::MAIN_FOLDER_ID,$item['folder_ids']??[]) ? $item['main_folder_id'] = 1 : $item['main_folder_id'] = 0;
                } else {
                    array_intersect(self::MAIN_FOLDER_ID,array_filter(explode(',',$item['folder_ids']??''))) ? $item['main_folder_id'] = 1 : $item['main_folder_id'] = 0;
                }
            }

            //需要计算最新会话子邮件数据
            if (in_array($conversationId,$needProcessLastConversationIds)) {
                if ($item['mail_count']==1) {
                    $mailConversationDataMap[$conversationId]['last_mail_id'] = $item['mail_id']??0;
                    $mailConversationDataMap[$conversationId]['last_mail_subject'] = $item['subject']??'';
                    $mailConversationDataMap[$conversationId]['last_mail_receive_time'] = $item['receive_time']??'1970-01-01 09:00:00';
                } else {
                    $lastMailData =\common\library\mail_conversation\Helper::getLastMailFiledDataByConversationId($this->db,$conversationId);
                    $mailConversationDataMap[$conversationId]['last_mail_id'] = $lastMailData['last_mail_id']??0;
                    $mailConversationDataMap[$conversationId]['last_mail_subject'] = $lastMailData['last_mail_subject']??'';
                    $mailConversationDataMap[$conversationId]['last_mail_receive_time'] = $lastMailData['last_mail_receive_time']??'1970-01-01 09:00:00';
                }
            }

            //需要计算最新会话收件子邮件数据
            if (in_array($conversationId,$needProcessLastReceiveConversationIds)) {
                if ($item['mail_count']==1 && $item['mail_type'] == 1) {
                    //等于最后一封邮件的id以及时间
                    $mailConversationDataMap[$conversationId]['last_receive_mail_receive_time'] = $item['receive_time']??'1970-01-01 09:00:00';
                    $mailConversationDataMap[$conversationId]['last_receive_mail_id'] = $item['mail_id']??0;
                    $mailConversationDataMap[$conversationId]['urgent_flag'] = ($item['urgent_flag'] ?? 0) > 0 ? 1 : 0;
                    $mailConversationDataMap[$conversationId]['reply_flag'] = ($item['reply_flag'] ?? 0) > 0 ? 1 : 0;
                    $mailConversationDataMap[$conversationId]['forward_flag'] = ($item['forward_flag'] ?? 0) > 0 ? 1 : 0;

                } elseif($item['mail_count']>1) {
                    //会话的最新收件 urgent_flag reply_flag last_receive_mail_receive_time last_receive_mail_id
                    $lastReceiveMailData = \common\library\mail_conversation\Helper::getLastReceiveMailDataByConversationId($this->db,$conversationId);
                    $mailConversationDataMap[$conversationId]['last_receive_mail_receive_time'] = $lastReceiveMailData['last_receive_mail_receive_time']??'1970-01-01 09:00:00';
                    $mailConversationDataMap[$conversationId]['last_receive_mail_id'] = $lastReceiveMailData['last_receive_mail_id']??0;
                    $mailConversationDataMap[$conversationId]['urgent_flag'] = ($lastReceiveMailData['urgent_flag'] ?? 0) > 0 ? 1 : 0;
                    $mailConversationDataMap[$conversationId]['reply_flag'] = ($lastReceiveMailData['reply_flag'] ?? 0) > 0 ? 1 : 0;
                    $mailConversationDataMap[$conversationId]['forward_flag'] = ($lastReceiveMailData['forward_flag'] ?? 0) > 0 ? 1 : 0;

                } else {
                    $mailConversationDataMap[$conversationId]['last_receive_mail_receive_time'] = '1970-01-01 09:00:00';
                    $mailConversationDataMap[$conversationId]['last_receive_mail_id'] = 0;
                    $mailConversationDataMap[$conversationId]['urgent_flag'] = 0;
                    $mailConversationDataMap[$conversationId]['reply_flag'] = 0;
                    $mailConversationDataMap[$conversationId]['forward_flag'] = 0;
                }
            }

            //需要计算最新会话发件子邮件数据
            if (in_array($conversationId,$needProcessLastSendConversationIds)) {
                if ($item['mail_count']==1 && $item['mail_type'] == 2) {
                    //等于最后一封邮件的id以及时间
                    $mailConversationDataMap[$conversationId]['last_send_mail_id'] = $item['mail_id']??0;
                    $mailConversationDataMap[$conversationId]['last_send_mail_receive_time'] = $item['receive_time']??'1970-01-01 09:00:00';
                }elseif($item['mail_count']>1){
                    $lastSendMailData = \common\library\mail_conversation\Helper::getLastSendMailDataByConversationId($this->db,$conversationId);
                    $mailConversationDataMap[$conversationId]['last_send_mail_id'] = $lastSendMailData['last_send_mail_id']??0;
                    $mailConversationDataMap[$conversationId]['last_send_mail_receive_time'] = $lastSendMailData['last_send_mail_receive_time']??'1970-01-01 09:00:00';
                } else {
                    $mailConversationDataMap[$conversationId]['last_send_mail_id'] = 0;
                    $mailConversationDataMap[$conversationId]['last_send_mail_receive_time'] = '1970-01-01 09:00:00';
                }
            }

            if (in_array('sender',$needProcessConversationIdFiledMap[$conversationId]??[])) {
                //contact_emails
                $contactEmails = '';
                if ($item['mail_count']==1 && $item['mail_type'] == 1) {
                    $contactEmails = $item['sender']??'';
                } elseif($item['mail_count']>1 ) {
                    $contactEmails = \common\library\mail_conversation\Helper::getContactEmailsByConversationId($this->db,$conversationId)['contact_emails']??'';
                }
                $contactEmailsStr = '';
                if (!empty($contactEmails)) {
                    $contactEmails = implode(';',array_filter(explode(',',$contactEmails)));
                    $contactEmails = \common\library\email\Util::findAllEmails($contactEmails);
                    if (!empty($contactEmails)) {
                        $contactEmailsStr = implode(',',$contactEmails);
                    }
                }
                $mailConversationDataMap[$conversationId]['contact_emails']  = $contactEmailsStr;
            }

            if (in_array($conversationId,$needRecoverConversationIds)) {
                $mailConversationDataMap[$conversationId]['delete_flag'] = 0;
            }

            unset($item['mail_ids']);
            unset($item['subject']);
            unset($item['mail_type']);
            unset($item['sender']);
            unset($item['receive_time']);
            unset($item['urgent_flag']);
            unset($item['reply_flag']);
            unset($item['forward_flag']);
            unset($item['mail_id']);

            //合并重算以及更新的字段，如果在重算里，就直接用重算的数据
            if (isset($updateConversationDataMap[$conversationId])) {
                $item = array_merge($updateConversationDataMap[$conversationId],$item);
            }

            $mailConversationDataMap[$conversationId] = array_merge($mailConversationDataMap[$conversationId]??[],$item);
        }

        if (!empty($updateConversationDataMap)) {
            foreach ($updateConversationDataMap as $conversationId => $item) {
                if (!in_array($conversationId,$validConversationIds)) {
                    $mailConversationDataMap[$conversationId] = $item;
                }
            }
        }

        //$mailConversationDataMap
        $this->log("mailConversationDataMap:".json_encode($mailConversationDataMap));

        //批量插入数据
        if (!empty($mailConversationDataMap)) {
            \common\library\mail_conversation\Helper::batchUpsertConversationDataOneByOne($this->clientId, $this->db, $mailConversationDataMap);
        }
    }

    private function pruneAndCompare($conversationMailTypeChangeField, $allConversationIds)
    {

        //查询一次这一批会话的mail_conversation_data数据
        $mailConversationDatas = Helper::getMailConversationDataByConversationIds($this->clientId,$allConversationIds);

        $this->log('mailConversationDatas:' . json_encode($mailConversationDatas));

        //从每个更新的会话来判断每个会话需要更新的字段
        $updateConversationDataMap = [];
        //需要查询getAttachReadFolderRelateFiledDataByConversationIds
        $needProcessConversationIds = [];
        //需要计算会话的字段
        $needProcessConversationIdFiledMap = [];
        //需要计算最新时间的会话
        $needProcessLastConversationIds = [];
        //需要计算最新收件的会话
        $needProcessLastReceiveConversationIds = [];
        //需要计算最新发件的会话
        $needProcessLastSendConversationIds = [];
        //需要从删除状态恢复的会话
        $needRecoverConversationIds = [];

        //2.遍历每个会话，与data对比判断哪些会话的哪些字段需要更新
        foreach ($conversationMailTypeChangeField as $conversationId => $mailList) {

            $mailConversationData = $mailConversationDatas[$conversationId] ?? [];

            foreach ($mailList as $mailType => $mails) {

                foreach ($mails as $mailId => $mail) {

                    //判断有没有删除或者移入垃圾箱
                    $deleteFlag = $mail['delete_flag'] ?? -1;
                    $folderId = $mail['folder_id'] ?? -1;
                    $userId = $mail['user_id'] ?? -1;

                    //纯粹新增 删除恢复 邮件换绑 垃圾箱恢复场景 注意会话的delete_flag要恢复
                    if((isset($mail['conversation_id']) && ($mail['conversation_id'] ?? 0) > 0)
                        || (isset($mail['delete_flag']) && ($mail['delete_flag'] ?? -1) == 0)
                        || (isset($mail['junk_folder_id']) && ($mail['junk_folder_id'] ?? -1) == \Mail::FOLDER_JUNK_ID)
                    ) {

                        //mail_count要重算
                        $needProcessConversationIds[] = $conversationId;
                        $needProcessConversationIdFiledMap[$conversationId] = array_merge($needProcessConversationIdFiledMap[$conversationId] ?? [], [
                            'mail_count',
                            'read_flag',
                            'archive_flag'
                        ]);
                        $needRecoverConversationIds[] = $conversationId;

                        //判断该邮件时间是不是比data的最新邮件时间新，如果是就要去重算，直接更新
                        if (isset($mail['receive_time']) && (strtotime($mail['receive_time'] ?? '1970-01-01 09:00:00.000')
                                > strtotime($mailConversationData['last_mail_receive_time'] ?? '1970-01-01 09:00:00.000'))) {

                            //还有更新的，直接就更新了
                            if (!isset($updateConversationDataMap[$conversationId]['last_mail_receive_time']) ||
                                strtotime($mail['receive_time'] ?? '1970-01-01 09:00:00.000')
                                > strtotime($updateConversationDataMap[$conversationId]['last_mail_receive_time'])) {

                                $updateConversationDataMap[$conversationId]['last_mail_receive_time'] = $mail['receive_time'] ?? '1970-01-01 09:00:00.000';
                                $updateConversationDataMap[$conversationId]['last_mail_id'] = $mail['mail_id'] ?? 0;
                                $updateConversationDataMap[$conversationId]['last_mail_subject'] = $mail['subject'] ?? '';
                            }
                        }


                        if (isset($mail['relate_company_flag']) && ($mail['relate_company_flag'] ?? -1) >= 0) {
                            $needProcessConversationIdFiledMap[$conversationId] = array_merge($needProcessConversationIdFiledMap[$conversationId] ?? [], [
                                'relate_company_flag',
                            ]);

                        }

                        if (isset($mail['attach_flag']) && ($mail['attach_flag'] ?? -1) == 1) {
                            $updateConversationDataMap[$conversationId]['attach_flag'] = $mail['attach_flag'] ?? 0;
                        }

                        if (isset($mail['folder_id']) && ($mail['folder_id'] ?? -1) > 0) {
                            $needProcessConversationIdFiledMap[$conversationId] = array_merge($needProcessConversationIdFiledMap[$conversationId] ?? [], [
                                'folder_id',
                            ]);
                        }

                        if (isset($mail['archive_flag']) && ($mail['archive_flag'] ?? -1) == 0) {
                            $needProcessConversationIdFiledMap[$conversationId] = array_merge($needProcessConversationIdFiledMap[$conversationId] ?? [], [
                                'archive_flag',
                            ]);
                        }

                        //收件
                        if ($mailType == \Mail::MAIL_TYPE_RECEIVE) {

                            //新增收件，直接更新
                            if (isset($mail['read_flag']) && ($mail['read_flag'] ?? -1) == 0) {
                                $needProcessConversationIdFiledMap[$conversationId] = array_merge($needProcessConversationIdFiledMap[$conversationId] ?? [], [
                                    'read_flag',
                                ]);
                            }

                            if (isset($mail['sender']) && ($mail['sender'] ?? '') != "") {
                                $contactEmails = $mail['sender'] ?? '';
                                //过滤sender
                                $sender = '';
                                if (!empty($contactEmails)) {
                                    $contactEmails = implode(';', array_filter(explode(',', $contactEmails)));
                                    $contactEmails = \common\library\email\Util::findAllEmails($contactEmails);
                                    if (!empty($contactEmails)) {
                                        //只会有一个发件人
                                        $sender = $contactEmails[0] ?? '';
                                    }
                                }

                                //判断是否在Data里面
                                if (!empty($sender)) {
                                    if (isset($updateConversationDataMap[$conversationId]['contact_emails'])) {
                                        $senderArr = explode(',', $updateConversationDataMap[$conversationId]['contact_emails']);
                                        if (!in_array($sender, $senderArr)) {
                                            $senderArr[] = $sender;
                                            $updateConversationDataMap[$conversationId]['contact_emails'] = implode(',', $senderArr);
                                        }
                                    } else {
                                        $senderArr = json_decode($mailConversationData['contact_emails'] ?? '[]', true);
                                        if (!in_array($sender, $senderArr)) {
                                            $senderArr[] = $sender;
                                            $updateConversationDataMap[$conversationId]['contact_emails'] = implode(',', $senderArr);
                                        }
                                    }
                                }
                            }


                            //最新收件时间
                            if (strtotime($mail['receive_time'] ?? '1970-01-01 09:00:00.000')
                                >= strtotime($mailConversationData['last_receive_mail_receive_time'] ?? '1970-01-01 09:00:00.000')) {

                                if (!isset($updateConversationDataMap[$conversationId]['last_receive_mail_receive_time']) ||
                                    strtotime($mail['receive_time'] ?? '1970-01-01 09:00:00.000')
                                    > strtotime($updateConversationDataMap[$conversationId]['last_receive_mail_receive_time']?? '1970-01-01 09:00:00.000')) {

                                    $updateConversationDataMap[$conversationId]['last_receive_mail_receive_time'] = $mail['receive_time'] ?? '1970-01-01 09:00:00.000';
                                    $updateConversationDataMap[$conversationId]['last_receive_mail_id'] = $mail['mail_id'] ?? 0;
                                    $updateConversationDataMap[$conversationId]['urgent_flag'] = ($mail['urgent_flag'] ?? 0) > 0 ? 1 : 0;
                                    $updateConversationDataMap[$conversationId]['reply_flag'] = ($mail['reply_flag'] ?? 0) > 0 ? 1 : 0;
                                    $updateConversationDataMap[$conversationId]['forward_flag'] = ($mail['forward_flag'] ?? 0) > 0 ? 1 : 0;
                                }
                            }

                        }

                        //发件
                        if ($mailType == \Mail::MAIL_TYPE_SEND) {

                            //判断该邮件时间是不是比data的最新发件类型的邮件时间新，如果是就要去重算
                            if (strtotime($mail['receive_time'] ?? '1970-01-01 09:00:00.000')
                                > strtotime($mailConversationData['last_send_mail_receive_time'] ?? '1970-01-01 09:00:00.000')) {

                                if (!isset($updateConversationDataMap[$conversationId]['last_send_mail_receive_time']) ||
                                    strtotime($mail['receive_time'] ?? '1970-01-01 09:00:00.000')
                                    > strtotime($updateConversationDataMap[$conversationId]['last_send_mail_receive_time'] ?? '1970-01-01 09:00:00.000')
                                ) {
                                    $updateConversationDataMap[$conversationId]['last_send_mail_receive_time'] = $mail['receive_time'] ?? '1970-01-01 09:00:00.000';
                                    $updateConversationDataMap[$conversationId]['last_send_mail_id'] = $mail['mail_id'] ?? 0;
                                }
                            }
                        }
                        //删除，移入垃圾箱，解绑
                    } elseif ($deleteFlag == 1 || $folderId == \Mail::FOLDER_JUNK_ID) {

                        //删除的数据，先判断mail_conversation_data在不在，不在的话就不用处理
                        if (empty($mailConversationData) || ($mailConversationData['mail_count']??0)==0) {
//                            $this->log('mailConversationData is empty,conversationId:' . $conversationId);
                            continue;
                        }

                        //删除会重算mail_count
                        $needProcessConversationIds[] = $conversationId;
                        $needProcessConversationIdFiledMap[$conversationId] = array_merge($needProcessConversationIdFiledMap[$conversationId] ?? [], [
                            'mail_count',
                        ]);

                        //attach_flag
                        if (isset($mail['attach_flag']) && ($mail['attach_flag'] ?? -1) == 1) {
                            $needProcessConversationIdFiledMap[$conversationId] = array_merge($needProcessConversationIdFiledMap[$conversationId] ?? [], [
                                'attach_flag',
                            ]);
                        }

                        //folder_id
                        if (isset($mail['folder_id']) && ($mail['folder_id'] ?? 0) != 0) {
                            $needProcessConversationIdFiledMap[$conversationId] = array_merge($needProcessConversationIdFiledMap[$conversationId] ?? [], [
                                'folder_id',
                            ]);
                        }

                        //relate_company_flag
                        if (isset($mail['relate_company_flag']) && ($mail['relate_company_flag'] ?? -1) >= 0) {
                            $needProcessConversationIdFiledMap[$conversationId] = array_merge($needProcessConversationIdFiledMap[$conversationId] ?? [], [
                                'relate_company_flag',
                            ]);
                        }

                        //archive_flag
                        if (isset($mail['archive_flag']) && ($mail['archive_flag'] ?? -1) == 0) {
                            $needProcessConversationIdFiledMap[$conversationId] = array_merge($needProcessConversationIdFiledMap[$conversationId] ?? [], [
                                'archive_flag',
                            ]);
                        }

                        //删除收件，要重算,sender一定会重算的
                        if ($mailType == \Mail::MAIL_TYPE_RECEIVE ) {

                            //sender
                            if (isset($mail['sender']) && ($mail['sender'] ?? '') != "") {
                                $needProcessConversationIdFiledMap[$conversationId] = array_merge($needProcessConversationIdFiledMap[$conversationId] ?? [], [
                                    'sender', //重算contact_emails
                                ]);
                            }

                            //read_flag
                            if (isset($mail['read_flag']) && ($mail['read_flag'] ?? -1) == 0) {
                                $needProcessConversationIdFiledMap[$conversationId] = array_merge($needProcessConversationIdFiledMap[$conversationId] ?? [], [
                                    'read_flag',
                                ]);
                            }

                        }

                        //判断是不是最后一封的，如果是就要重算
                        if (($mail['mail_id'] ?? 0) > 0 && (($mail['mail_id'] ?? 0) >= ($mailConversationData['last_mail_id'] ?? 0))) {
                            $needProcessLastConversationIds[] = $conversationId;
                        }

                        //判断是不是最后一封收件，如果是就要重新计算
                        //last_receive_mail_id，last_receive_mail_receive_time,urgent_flag,reply_flag看下是不是
                        if ($mailType==\Mail::MAIL_TYPE_RECEIVE && ($mail['mail_id'] ?? 0) > 0 && (($mail['mail_id'] ?? 0)
                                >= ($mailConversationData['last_receive_mail_id'] ?? 0))) {
                            $needProcessLastReceiveConversationIds[] = $conversationId;
                        }

                        //判断是不是最后一封发件，如果是就要重新计算
                        //last_receive_mail_id，last_receive_mail_receive_time,urgent_flag,reply_flag看下是不是
                        if ($mailType==\Mail::MAIL_TYPE_SEND && ($mail['mail_id'] ?? 0) > 0 && (($mail['mail_id'] ?? 0)
                                >= ($mailConversationData['last_send_mail_id'] ?? 0))) {
                            $needProcessLastSendConversationIds[] = $conversationId;
                        }

                    }else {

                        //兼容还没消费，却被更新了
                        if (empty($mailConversationData)) {
                            $needProcessConversationIds[] = $conversationId;
                            $needProcessLastConversationIds[] = $conversationId;
                            $needProcessLastReceiveConversationIds[] = $conversationId;
                            $needProcessConversationIdFiledMap[$conversationId] = array_merge($needProcessConversationIdFiledMap[$conversationId] ?? [], [
                                'read_flag',
                                'archive_flag',
                                'folder_id',
                                'relate_company_flag',
                                'attach_flag',
                                'mail_count',
                                'sender', //重算contact_emails
                            ]);
                            continue;
                        }


                        //移动文件夹folder_id,减少都要去重算,增加判断在不在
                        //todo 检查 减少能不能去查询有没有，新增的肯定要更新，不用重算直接更新
                        if (isset($mail['folder_id']) && ($mail['folder_id'] ?? 0) != 0) {
                            $needProcessConversationIds[] = $conversationId;
                            $needProcessConversationIdFiledMap[$conversationId][] = 'folder_id';
                        }

                        //relate_company_flag，变化都要重算
                        //todo 检查 能不能去查询有没有，新增的肯定要更新，不用重算直接更新
                        if (isset($mail['relate_company_flag']) && ($mail['relate_company_flag'] ?? -1) != -1) {
                            $needProcessConversationIds[] = $conversationId;
                            $needProcessConversationIdFiledMap[$conversationId][] = 'relate_company_flag';
                        }


                        //是最新的收件
                        if ($mailType == \Mail::MAIL_TYPE_RECEIVE && isset($mail['mail_id']) && ($mail['mail_id'] ?? 0)
                            > 0 && ($mail['mail_id'] ?? 0) >= ($mailConversationData['last_receive_mail_id'] ?? 0)) {

                            //判断属性与data的变更
                            if (isset($mail['urgent_flag']) && ($mail['urgent_flag'] ?? 0) != ($mailConversationData['urgent_flag'] ?? 0)) {
                                $updateConversationDataMap[$conversationId]['urgent_flag'] = ($mail['urgent_flag'] ?? 0) > 0 ? 1 : 0;
                            }

                            if (isset($mail['reply_flag']) && ($mail['reply_flag'] ?? 0) != ($mailConversationData['reply_flag'] ?? 0)) {
                                $updateConversationDataMap[$conversationId]['reply_flag'] = ($mail['reply_flag'] ?? 0) > 0 ? 1 : 0;
                            }

                            if (isset($mail['forward_flag']) && ($mail['forward_flag'] ?? 0) != ($mailConversationData['forward_flag'] ?? 0)) {
                                $updateConversationDataMap[$conversationId]['forward_flag'] = ($mail['forward_flag'] ?? 0) > 0 ? 1 : 0;
                            }
                        }


                        if (isset($mail['attach_flag']) && ($mail['attach_flag'] ?? 0) != ($mailConversationData['attach_flag'] ?? 0)) {

                            //有一个为1就是1
                            if (($mail['attach_flag'] ?? 0) == 1) {
                                $updateConversationDataMap[$conversationId]['attach_flag'] = $mail['attach_flag'] ?? 0;
                            } else {
                                //先处理处理回退场景，收件场景一般不会重新编辑这个字段
                                $needProcessConversationIds[] = $conversationId;
                                $needProcessConversationIdFiledMap[$conversationId][] = 'attach_flag';
                            }
                        }


                        //判断是否已读，有一个未读就是未读
                        if ($mailType == \Mail::MAIL_TYPE_RECEIVE && isset($mail['read_flag']) && ($mail['read_flag'] ?? -1)
                            != ($mailConversationData['read_flag'] ?? -1)) {
                            //有一个未读就是未读
                            if (($mail['read_flag'] ?? -1) == 0) {
                                $updateConversationDataMap[$conversationId]['read_flag'] = $mail['read_flag'] ?? 0;
                            } else {
                                $needProcessConversationIds[] = $conversationId;
                                $needProcessConversationIdFiledMap[$conversationId][] = 'read_flag';
                            }
                        }

                        //判断是否已归并，有一个未归并就是未归并
                        if (isset($mail['archive_flag']) && ($mail['archive_flag'] ?? -1)
                            != ($mailConversationData['archive_flag'] ?? -1)) {
                            //有一个未归并就是未归并
                            if (($mail['archive_flag'] ?? -1) == 0) {
                                $updateConversationDataMap[$conversationId]['archive_flag'] = $mail['archive_flag'] ?? 0;
                            } else {
                                $needProcessConversationIds[] = $conversationId;
                                $needProcessConversationIdFiledMap[$conversationId][] = 'archive_flag';
                            }
                        }


                        //sender不会更新，可能有修数据场景
                        if ($mailType == \Mail::MAIL_TYPE_RECEIVE && isset($mail['sender']) && ($mail['sender'] ?? '') != "") {

                            $contactEmails = $mail['sender'] ?? '';
                            //过滤sender
                            $sender = '';
                            if (!empty($contactEmails)) {
                                $contactEmails = implode(';', array_filter(explode(',', $contactEmails)));
                                $contactEmails = \common\library\email\Util::findAllEmails($contactEmails);
                                if (!empty($contactEmails)) {
                                    //只会有一个发件人
                                    $sender = $contactEmails[0] ?? '';
                                }
                            }

                            //判断是否在Data里面
                            if (!empty($sender)) {
                                if (isset($updateConversationDataMap[$conversationId]['contact_emails'])) {
                                    $senderArr = array_filter(explode(',', $updateConversationDataMap[$conversationId]['contact_emails']),function($item) {
                                        return $item !== '';
                                    });

                                    if (!in_array($sender, $senderArr)) {
                                        $senderArr[] = $sender;
                                        $updateConversationDataMap[$conversationId]['contact_emails'] = implode(',', $senderArr);
                                    }
                                } else {
                                    $senderArr = json_decode($mailConversationData['contact_emails'] ?? '[]', true);
                                    if (!in_array($sender, $senderArr)) {
                                        $senderArr[] = $sender;

                                        $updateConversationDataMap[$conversationId]['contact_emails'] = implode(',', $senderArr);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            //如果是更新的会话，直接就使用user_id user_mail_id
            if (!empty($updateConversationDataMap[$conversationId])) {
                //如果有更新folder_ids,要维护main_folder_id
                if (isset($updateConversationDataMap[$conversationId]['folder_ids'])) {

                    if (array_intersect(self::MAIN_FOLDER_ID,$updateConversationDataMap[$conversationId]['folder_ids'])) {
                        $mainFolderId = 1;
                    } else {
                        $mainFolderId = 0;
                    }
                    $updateConversationDataMap[$conversationId]['main_folder_id'] = $mainFolderId;
                }
            }
        }

        return [
             $updateConversationDataMap,
             array_unique($needProcessConversationIds),
             $needProcessConversationIdFiledMap,
             $needProcessLastConversationIds,
             $needProcessLastReceiveConversationIds,
             $needProcessLastSendConversationIds,
             array_unique($needRecoverConversationIds)
        ];

    }

    private function saveConversation($updateConversationDataMap,$needProcessConversationIds, $needProcessConversationIdFiledMap,
                                      $needProcessLastConversationIds, $needProcessLastReceiveConversationIds,
                                      $needProcessLastSendConversationIds,$needRecoverConversationIds)
    {
        //3.插入数据，如果只有变更会话每个会话的变更字段都不一样，调用单个会话更新接口
        if (!empty($updateConversationDataMap) && empty($needProcessConversationIds)) {
            //todo 注意db的调用 注意常驻在其他地方的调用
            $this->db = \ProjectActiveRecord::getDbByClientId($this->clientId);
            \common\library\mail_conversation\Helper::batchUpsertConversationDataOneByOne($this->clientId, $this->db, $updateConversationDataMap);
        }

        //如果conversation又要更新，又要重算，可以合并成一条sql 合并sql
        if (!empty($needProcessConversationIds)) {
            $this->processConversationIds($needProcessConversationIds, $needProcessConversationIdFiledMap,
                $needProcessLastConversationIds, $needProcessLastReceiveConversationIds,
                $needProcessLastSendConversationIds,$updateConversationDataMap,
                $needRecoverConversationIds);
        }

    }

}
