<?php

namespace common\library\setting\library\common;

use common\library\setting\item\ItemSettingConstant;
use common\library\setting\item\ItemSettingMetadata;

class AiTagMetadata extends CommonMetadata
{

    public static function getExtraDataMap($module)
    {
        $map = [
            \Constants::TYPE_LEAD => [
                ItemSettingConstant::AI_TAG_OPEN_EDM => '打开了营销',
                ItemSettingConstant::AI_TAG_REPLY_EDM => '回复了营销',
                ItemSettingConstant::AI_TAG_OPEN_URL_EDM => '点击了链接',
                ItemSettingConstant::AI_TAG_OPEN_MAIL => '打开了邮件',
                ItemSettingConstant::AI_TAG_REPLY_MAIL => '回复了邮件',
            ],
        ];

        return $map[$module] ?? [];
    }

    public static function getApiMapping($module)
    {
        $map = [
            \Constants::TYPE_LEAD => [
                'tag_id' => 'item_id',
                'tag_name' => 'item_name',
            ]
        ];
        return $map[$module] ?? [];
    }

    public static function getTypeMapping($module)
    {
        $map = [
            \Constants::TYPE_LEAD => [
                'tag_id' => 'int',
            ]
        ];

        return $map[$module] ?? [];
    }

    public static function getItemType()
    {
        return ItemSettingConstant::ITEM_TYPE_AI_TAG;
    }
}