<?php

namespace common\library\setting\library\shipping_invoice;

use common\library\oms\common\OmsConstant;
use common\library\setting\item\Api;
use common\library\setting\item\ItemSettingConstant;
use RuntimeException;

class ShippingInvoiceApi extends Api
{
    public function __construct($clientId)
    {
        parent::__construct($clientId, \Constants::TYPE_SHIPPING_INVOICE, ItemSettingConstant::ITEM_TYPE_SHIPPING_INVOICE_FORMULA_FIELD);
    }

    /**
     * 初始化净重小计公式设置   net_weight_total_count
     **/
    public function initNetWeightAmountFormula($new_oms_flag = 0)
    {
        $clientId = $this->clientId;
        if ($new_oms_flag) {
            $type = OmsConstant::FORMULA_CARTON_CALCULATE;
        } else {
            $type = OmsConstant::FORMULA_PRODUCT_CALCULATE;
        }

        try {
            $mysqlDb = \ProjectActiveRecord::getDbByClientId($clientId);
            if (empty($mysqlDb)) {
                throw new RuntimeException('mysqlDb为空');
            }
            $info = [
                    'calculate_type'=>$type,
                    'decimal'=>2,
                    'is_editable'=>true,
                ];
            $extInfo[] = $info;
            $extInfoStr = json_encode($extInfo);
            $update = "update tbl_custom_field set ext_info='$extInfoStr' where client_id={$clientId} and type = 57 and id = 'net_weight_total_count' limit 1";
            if (empty($tryRun)) {
                $mysqlDb->createCommand($update)->execute();
            }
        } catch (\Throwable $t) {
            \LogUtil::info("{$clientId}:initShippingInvoiceNetWeightTotalCount_err", [
                'line' => $t->getLine(), 'error' => $t->getMessage(),
            ]);
        }
    }

}