<?php


namespace common\library\setting\library\tag;


use common\library\behavior\BehaviorConstant;
use common\library\behavior\BehaviorService;
use common\library\customer\BaseCompanyList;
use common\library\customer_tag\TagService;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\setting\item\ItemSettingOperator;

class TagOperator extends ItemSettingOperator
{

    protected function beforeDelete($module, $itemTye)
    {
        $dataList = $this->get(['system_flag',]);

        foreach ($dataList as $item) {
            if ($item['system_flag']) {
                throw new \RuntimeException(\Yii::t('trail', 'Unable to delete system tags'));
            }
        }

        parent::beforeDelete($module, $itemTye); // TODO: Change the autogenerated stub
    }

    protected function afterDelete(array $data, array $setting)
    {
        $userId = $this->object->getDomainHandler()->getUserId();
        $clientId = $this->object->getDomainHandler()->getClientId();
        $module = $setting['module'];
        $tagIds = array_column($data, 'item_id');

        if (empty($tagIds)) {
            return ;
        }

        if ($module == \Constants::TYPE_MAIL) {
            // 处理邮件tag
            \common\library\mail\setting\rule\Helper::delBatchByGeneralTag($userId, $clientId, $tagIds);
            foreach ($tagIds as $tagId) {
                \MailTagAssoc::removeByTagId($clientId, $userId, $tagId);
            }
        } elseif ($module == \Constants::TYPE_COMPANY) {
    
            $services = (new TagService($clientId, \Constants::TYPE_COMPANY));
    
            $services->setParam(['show_all' => 1, 'tags' => $tagIds, 'tag_match_mode' => BaseCompanyList::TAG_MATCH_MODE_SINGLE]);
            
            $services->setAllTags([], $tagIds, true);
        }


        parent::afterDelete($data, $setting);
    }

    protected function afterCreate(array $data, array $setting)
    {
        parent::afterCreate($data, $setting);
    }
}