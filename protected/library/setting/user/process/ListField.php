<?php
/**
 * Created by PhpStorm.
 * User: troyli
 * Date: 2018/12/26
 * Time: 3:52 PM
 */

namespace common\library\setting\user\process;


use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldList;
use common\library\object\binlog\constants;
use common\library\privilege_v3\Helper;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeFieldV2;
use common\library\privilege_v3\PrivilegeService;
use common\library\setting\user\UserSetting;

class ListField extends Base
{

    public function getDefaultResult()
    {
        $map = [
            UserSetting::PRIVATE_COMPANY_LIST_FIELD_WIDTH => [
                'base' => [PrivilegeConstants::FUNCTIONAL_CUSTOMER],
                'field' => [],
                'type' => '',
                'default' => [],
            ],
            UserSetting::PUBLIC_COMPANY_LIST_FIELD_WIDTH => [
                'base' => [PrivilegeConstants::FUNCTIONAL_CUSTOMER],
                'field' => [],
                'type' => '',
                'default' => [],
            ],
            UserSetting::PRIVATE_COMPANY_LIST_FIELD => [
                'base' => [PrivilegeConstants::FUNCTIONAL_CUSTOMER],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'name', 'width' => 260, 'fixed' => 1],
                    ['id' => 'customer.name', 'width' => 260, 'fixed' => 0],
                    ['id' => 'customer.email', 'width' => 240, 'fixed' => 0],
                    ['id' => 'last_remark_trail', 'width' => 200, 'fixed' => 0],
                    ['id' => 'last_trail', 'width' => 200, 'fixed' => 0],
                    ['id' => 'group_name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'owner', 'width' => 200, 'fixed' => 0],
                    ['id' => 'star', 'width' => 160, 'fixed' => 0],
                    ['id' => 'trail_status_name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'tag', 'width' => 300, 'fixed' => 0],
                    ['id' => 'score', 'width' => 90, 'fixed' => 0],
                    ['id' => 'order_time', 'width' => 180, 'fixed' => 0],
                    ['id' => 'create_time', 'width' => 180, 'fixed' => 0],
                    ['id' => 'archive_type', 'width' => 180, 'fixed' => 0],
                ]
            ],
            UserSetting::PUBLIC_COMPANY_LIST_FIELD => [
                'base' => [PrivilegeConstants::FUNCTIONAL_COMPANY_POOL],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'name', 'width' => 260, 'fixed' => 1],
                    ['id' => 'last_remark_trail', 'width' => 200, 'fixed' => 0],
                    ['id' => 'last_trail', 'width' => 200, 'fixed' => 0],
                    ['id' => 'pool_name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'last_owner_name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'country', 'width' => 200, 'fixed' => 0],
                    ['id' => 'biz_type', 'width' => 200, 'fixed' => 0],
                    ['id' => 'origin_name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'score', 'width' => 90, 'fixed' => 0],
                    ['id' => 'order_time', 'width' => 180, 'fixed' => 0],
                    ['id' => 'create_time', 'width' => 180, 'fixed' => 0],
                ]
            ],
            UserSetting::OPPORTUNITY_LIST_FIELD => [
                'base' => [PrivilegeConstants::FUNCTIONAL_OPPORTUNITY],
                'field' => [],
                'type' => \Constants::TYPE_OPPORTUNITY,
                'default' => [
                    ['id' => 'name', 'width' => 200, 'fixed' => 1],
                    ['id' => 'company.name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'customer.name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'amount', 'width' => 120, 'fixed' => 0],
                    ['id' => 'currency', 'width' => 80, 'fixed' => 0],
                    ['id' => 'account_date', 'width' => 120, 'fixed' => 0],
                    ['id' => 'stage_info.name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'stage_info.success_rate', 'width' => 64, 'fixed' => 0,],
                    ['id' => 'origin_name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'type_name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'main_user_info.nickname', 'width' => 200, 'fixed' => 0],
                    ['id' => 'handler_info.nickname', 'width' => 200, 'fixed' => 0],
                    ['id' => 'fail_type_name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'create_user_info.nickname', 'width' => 200, 'fixed' => 0],
                    ['id' => 'create_time', 'width' => 180, 'fixed' => 0],
                    ['id' => 'update_time', 'width' => 180, 'fixed' => 0],
                    ['id' => 'edit_time', 'width' => 180, 'fixed' => 0],
                    ['id' => 'trail_time', 'width' => 180, 'fixed' => 0],
                    ['id' => 'product_edit_time', 'width' => 180, 'fixed' => 0],
                    ['id' => 'create_type', 'width' => 180, 'fixed' => 0],
                    ['id' => 'stage_stay_time', 'width' => 180, 'fixed' => 0],
                ]
            ],
            UserSetting::CASH_COLLECTION_LIST_FIELD => [
                'base' => [PrivilegeConstants::FUNCTIONAL_CASH_COLLECTION],
                'field' => [],
                'type' => \Constants::TYPE_CASH_COLLECTION,
                'default' => [
                    ['id' => 'cash_collection_no', 'width' => 200, 'fixed' => 1],
                    ['id' => 'company_info.name', 'width' => 200, 'fixed' => 0, 'field' => 'company_id'],
                    ['id' => 'order_info.name', 'width' => 200, 'fixed' => 0, 'field' => 'order_id'],
                    ['id' => 'opportunity_info.name', 'width' => 200, 'fixed' => 0, 'field' => 'opportunity_id'],
                    ['id' => 'amount', 'width' => 150, 'fixed' => 0],
                    ['id' => 'currency', 'width' => 80, 'fixed' => 0],
                    ['id' => 'user_info.nickname', 'width' => 200, 'fixed' => 0, 'field' => 'user_id'],
                    ['id' => 'collection_date', 'width' => 200, 'fixed' => 0],
                    ['id' => 'type', 'width' => 120, 'fixed' => 0],
                ]
            ],
            UserSetting::CUSTOMER_ADVICE_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => \Constants::TYPE_CUSTOMER_ADVICE,
                'default' => [
                    ['id' => 'trust_level', 'width' => 200, 'fixed' => 0],
                    ['id' => 'email', 'width' => 200, 'fixed' => 0],
                    ['id' => 'name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'company_name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'user_name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'last_mail_subject', 'width' => 200, 'fixed' => 0],
                    ['id' => 'order_time', 'width' => 200, 'fixed' => 0],
                    ['id' => 'not_accept_remark', 'width' => 200, 'fixed' => 0],
                ]
            ],
            UserSetting::PAYPAL_INVOICE_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'transaction_no', "width" => 200, 'fixed' => 0],
                    ['id' => 'invoice_id', "width" => 285, 'fixed' => 0],
                    ['id' => 'create_time', "width" => 213, 'fixed' => 0],
                    ['id' => 'amount', "width" => 152, 'fixed' => 0],
                    ['id' => 'currency', "width" => 112, 'fixed' => 0],
                    ['id' => 'receive_name', "width" => 164, 'fixed' => 0],
                    ['id' => 'receive_email', "width" => 256, 'fixed' => 0],
                    ['id' => 'status', "width" => 421, 'fixed' => 0],
                    ['id' => 'note', "width" => 421, 'fixed' => 0],
                ]
            ],
            UserSetting::PRODUCT_LIST_FIELD => [
                'base' => [PrivilegeConstants::FUNCTIONAL_PRODUCT],
                'field' => [],
                'type' => \Constants::TYPE_PRODUCT,
                'default' => [
                    ['id' => 'images', 'width' => 100],
                    ['id' => 'name', 'width' => 200],
                    ['id' => 'cn_name', 'width' => 200],
                    ['id' => 'product_no', 'width' => 160],
                    ['id' => 'sku_attributes', 'width' => 200],
                    ['id' => 'model', 'width' => 160],
                    ['id' => 'group_id', 'width' => 160],
                    ['id' => 'fob', 'width' => 200],
                    ['id' => 'cost_with_tax', 'width' => 160],
                    ['id' => 'description', 'width' => 300],
                    ['id' => 'update_user', 'width' => 100],
                    ['id' => 'update_time', 'width' => 160],
                    ['id' => 'product_type', 'width' => 160],
                ]
            ],
            UserSetting::PRODUCT_SKU_LIST_FIELD => [
                'base' => [PrivilegeConstants::FUNCTIONAL_PRODUCT],
                'field' => [],
                'type' => \Constants::TYPE_PRODUCT,
                'default' => [
                    ['id' => 'attributes_info', 'width' => 120],
                    ['id' => 'image_info', 'width' => 80],
                    ['id' => 'sku_code', 'width' => 200],
                    ['id' => 'sku_remark', 'width' => 200],
                    ['id' => 'sku_description', 'width' => 200],
                    ['id' => 'cost_with_tax', 'width' => 160],
                    ['id' => 'fob', 'width' => 200],
                    ['id' => 'product_size', 'width' => 240],
                    ['id' => 'product_volume', 'width' => 160],
                    ['id' => 'product_net_weight', 'width' => 160],
                    ['id' => 'count_per_package', 'width' => 144],
                    ['id' => 'package_size', 'width' => 240],
                    ['id' => 'package_volume', 'width' => 160],
                    ['id' => 'package_gross_weight', 'width' => 160],
                    ['id' => 'package_remark', 'width' => 200],
                    ['id' => 'disable_flag', 'width' => 78],
                ]
            ],
            UserSetting::PRODUCT_PARTS_LIST_FIELD => [
                'base' => [PrivilegeConstants::FUNCTIONAL_PRODUCT],
                'field' => [],
                'type' => \Constants::TYPE_PRODUCT,
                'default' => [
                    ['id' => 'images', 'width' => 64],
                    ['id' => 'product_no', 'width' => 120],
                    ['id' => 'name', 'width' => 160],
                    ['id' => 'cn_name', 'width' => 160],
                    ['id' => 'unit', 'width' => 88],
                    ['id' => 'ratio', 'width' => 120],
                    ['id' => 'description', 'width' => 160],
                    ['id' => 'product_remark', 'width' => 160],
                    ['id' => 'cost_with_tax', 'width' => 144],
                    ['id' => 'parts_product_remark', 'width' => 240],
                ]
            ],
            UserSetting::SCHEDULE_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => \Constants::TYPE_SCHEDULE,
                'default' => [
                    ["id" => "title", 'width' => 233],
                    ["id" => "create_time", 'width' => 170],
                    ["id" => "create_user", 'width' => 89],
                    ["id" => "participant_user_list", 'width' => 157],
                    ["id" => "repeat_id", 'width' => 62],
                    ["id" => "start_time", 'width' => 175],
                    ["id" => "end_time", 'width' => 170],
                    ["id" => "full_day_flag", 'width' => 65],
                    ["id" => "refer_type", 'width' => 85],
                    ["id" => "refer_info", 'width' => 228],
                    ["id" => "relation_mail_info", 'width' => 139],
                    ["id" => "remark", 'width' => 161],
                    ["id" => "remind_time", 'width' => 173],
                ]
            ],
            UserSetting::ORDER_LIST_FIELD => [
                'base' => [],
                'field' => [
                    'cash_collection' => [
                        PrivilegeConstants::FUNCTIONAL_CASH_COLLECTION
                    ],
                    'approval_status' => [
                        PrivilegeConstants::FUNCTION_REVIEW_BASE
                    ],
                ],
                'type' => '',
                'default' => [
                    ['id' => 'name', 'width' => 200],
                    ['id' => 'order_no', 'width' => 200, 'fixed' => 1],
                    ['id' => 'update_time', 'width' => 200],
                    ['id' => 'amount', 'width' => 200],
                    ['id' => 'company_id', 'width' => 200],
                    ['id' => 'status', 'width' => 200],
                    ['id' => 'erp_status', 'width' => 200],
                    ['id' => 'cash_collection', 'width' => 200],
                    ['id' => 'approval_status', 'width' => 200],
                    ['id' => 'handler', 'width' => 200],
                    ['id' => 'account_date', 'width' => 200],
                    ['id' => 'opportunity_id', 'width' => 200],
                ]
            ],
            UserSetting::ORDER_PRODUCT_LIST_FIELD => [
                'base'  => [],
                'field' => [],
                'type'  => '',
                'default' => [
                    ['id' => 'order_no', 'width' => 200, 'fixed' => 1],
                    ['id' => 'product_no', 'width' => 200],
                    ['id' => 'product_image', 'width' => 200],
                    ['id' => 'product_name', 'width' => 200],
                    ['id' => 'product_cn_name', 'width' => 200],
                    ['id' => 'product_model', 'width' => 200],
                    ['id' => 'sku_id', 'width' => 200],
                    ['id' => 'count', 'width' => 200],
                    ['id' => 'currency', 'width' => 200],
                    ['id' => 'unit_price', 'width' => 200],
                    ['id' => 'cost_amount', 'width' => 200],
                ]
            ],
            UserSetting::ALI_ORDER_LIST_FIELD => [
                'base' => [],
                'field' => [
                    'cash_collection' => [
                        PrivilegeConstants::FUNCTIONAL_CASH_COLLECTION,
                    ],
                    'approval_status' => [
                        PrivilegeConstants::FUNCTION_REVIEW_BASE,
                    ],
                ],
                'type' => '',
                'default' => [
                    ['id' => 'name', 'width' => 200, 'fixed' => 1],
                    ['id' => 'order_no', 'width' => 200],
                    ['id' => 'ali_store_id', 'width' => 200],
                    ['id' => 'update_time', 'width' => 200],
                    ['id' => 'amount', 'width' => 200],
                    ['id' => 'company_id', 'width' => 200],
                    ['id' => 'ali_status_id', 'width' => 200],
                ]
            ],
            UserSetting::LEAD_PRIVATE_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'name', 'width' => 200, 'fixed' => 1],
                    ['id' => 'customer.email', 'width' => 200],
                    ['id' => 'status', 'width' => 200],
                    ['id' => 'ai_tags', 'width' => 200],
                    ['id' => 'origin_list', 'width' => 200],
                    ['id' => 'create_time', 'width' => 200],
                ]
            ],
            UserSetting::LEAD_PUBLIC_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'name', 'width' => 200, 'fixed' => 1],
                    ['id' => 'customer.email', 'width' => 200],
                    ['id' => 'status', 'width' => 200],
                    ['id' => 'ai_tags', 'width' => 200],
                    ['id' => 'origin_list', 'width' => 200],
                    ['id' => 'create_time', 'width' => 200],
                ]
            ],
            UserSetting::COMPANY_DUPLICATION_LIST_FIELD_WIDTH => [
                'base' => [PrivilegeConstants::FUNCTIONAL_CUSTOMER],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'serial_id', 'width' => 200],
                    ['id' => 'status', 'width' => 200],
                    ['id' => 'company', 'width' => 200],
                    ['id' => 'name', 'width' => 200],
                    ['id' => 'email', 'width' => 200],
                    ['id' => 'tel', 'width' => 200],
                    ['id' => 'origin_name', 'width' => 200],
                    ['id' => 'country', 'width' => 200],
                    ['id' => 'last_owner_name', 'width' => 200],
                    ['id' => 'owner', 'width' => 200],
                    ['id' => 'create_time', 'width' => 200],
                    ['id' => 'contact', 'width' => 200],
                ]
            ],
            UserSetting::COMPANY_DUPLICATION_LIST_FIELD_V2 => [
                'base' => [PrivilegeConstants::FUNCTIONAL_CUSTOMER],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'serial_id', 'width' => 200, 'fixed' => 1, 'object_name' => 'objCompany'],
                    ['id' => 'trail_status', 'width' => 200, 'fixed' => 0, 'object_name' => 'objCompany'],
                    ['id' => 'name', 'width' => 200, 'fixed' => 0, 'object_name' => 'objCompany'],
                    ['id' => 'short_name', 'width' => 200, 'fixed' => 0, 'object_name' => 'objCompany'],
                    ['id' => 'name', 'width' => 200, 'fixed' => 0, 'object_name' => 'objCustomer'],
                    ['id' => 'email', 'width' => 200, 'fixed' => 0, 'object_name' => 'objCustomer'],
                    ['id' => 'tel_list', 'width' => 200, 'fixed' => 0, 'object_name' => 'objCustomer'],
                    ['id' => 'origin_list', 'width' => 200, 'fixed' => 0, 'object_name' => 'objCompany'],
                    ['id' => 'country_region', 'width' => 200, 'fixed' => 0, 'object_name' => 'objCompany'],
                    ['id' => 'last_owner', 'width' => 200, 'fixed' => 0, 'object_name' => 'objCompany'],
                    ['id' => 'user_id', 'width' => 200, 'fixed' => 0, 'object_name' => 'objCompany'],
                    ['id' => 'archive_time', 'width' => 200, 'fixed' => 0, 'object_name' => 'objCompany'],
                    ['id' => 'contact', 'width' => 200, 'fixed' => 0, 'object_name' => 'objCustomer'],
                    ['id' => 'order_time', 'width' => 200, 'fixed' => 0, 'object_name' => 'objCompany'],
                ]
            ],
            UserSetting::PURCHASE_ORDER_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => \Constants::TYPE_PURCHASE_ORDER,
                'default' => [
                    ['id' => 'purchase_order_no', 'width' => 200, 'fixed' => 1],
                    ['id' => 'status', 'width' => 200],
                    ['id' => 'purchase_date', 'width' => 200],
                    ['id' => 'delivery_date', 'width' => 200],
                    ['id' => 'currency', 'width' => 120],
                    ['id' => 'amount', 'width' => 200],
                    ['id' => 'exchange_rate', 'width' => 200],
                    ['id' => 'exchange_rate_usd', 'width' => 200],
                    ['id' => 'amount_rmb', 'width' => 200],
                    ['id' => 'amount_usd', 'width' => 200],
                    ['id' => 'supplier_no', 'width' => 200],
                    ['id' => 'supplier_id', 'width' => 200],
                    ['id' => 'supplier_contact', 'width' => 200],
                    ['id' => 'handler', 'width' => 200],
                ]
            ],
            UserSetting::PURCHASE_PRODUCT_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => \Constants::TYPE_PURCHASE_ORDER_PRODUCT,
                'default' => [


                    // PO验收后改的新版默认值
                    ['id' => 'product_no', 'width' => 160, 'fixed' => 1],
                    ['id' => 'product_image', 'width' => 88, 'fixed' => 1],
                    ['id' => 'product_name', 'width' => 160, 'fixed' => 1],
                    ['id' => 'supplier_id', 'width' => 160, 'fixed' => 1],
                    ['id' => 'status', 'width' => 88],
                    ['id' => 'count', 'width' => 96],
                    ['id' => 'unit_price', 'width' => 96],
                    ['id' => 'cost_amount', 'width' => 96],
                    ['id' => 'have_inbound_count', 'width' => 104],
                    ['id' => 'wait_inbound_count', 'width' => 104],
                    ['id' => 'have_return_count', 'width' => 104],
                    ['id' => 'purchase_order_no', 'width' => 160],
                    ['id' => 'transfer_invoice_serial_id', 'width' => 160],
                    ['id' => 'order_no', 'width' => 160],
                    ['id' => 'product_cn_name', 'width' => 160],
                    ['id' => 'purchase_date', 'width' => 120],
                    ['id' => 'delivery_date', 'width' => 120],
                    ['id' => 'handler', 'width' => 120],
                    ['id' => 'inbound_status', 'width' => 88],
                ]
            ],
            UserSetting::PRODUCT_PURCHASE_PRODUCT_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => \Constants::TYPE_PURCHASE_ORDER_PRODUCT,
                'default' => [
                    ['id' => 'purchase_order_no', 'width' => 160, 'fixed' => 1],
                    ['id' => 'status', 'width' => 88],
                    ['id' => 'inbound_status', 'width' => 88, 'fixed' => 1],
                    ['id' => 'handler', 'width' => 120],
                    ['id' => 'product_image', 'width' => 92],
                    ['id' => 'product_name', 'width' => 200],
                    ['id' => 'product_cn_name', 'width' => 160],
                    ['id' => 'product_no', 'width' => 200, 'fixed' => 1],
                    ['id' => 'product_remark', 'width' => 200],
                    ['id' => 'supplier_product_no', 'width' => 160],
                    ['id' => 'supplier_product_name', 'width' => 160],
                    ['id' => 'supplier_id', 'width' => 160],
                    ['id' => 'supplier_no', 'width' => 160],
                    ['id' => 'order_no', 'width' => 160],
                    ['id' => 'transfer_invoice_serial_id', 'width' => 160],
                    ['id' => 'count', 'width' => 88],
                    ['id' => 'unit_price', 'width' => 160],
                    ['id' => 'cost_amount', 'width' => 160],
                    ['id' => 'have_inbound_count', 'width' => 102],
                    ['id' => 'wait_inbound_count', 'width' => 102],
                    ['id' => 'have_return_count', 'width' => 102]
                ]
            ],

            UserSetting::SUPPLIER_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => \Constants::TYPE_SUPPLIER,
                'default' => [
                    ['id' => 'name', 'width' => 230, 'fixed' => 1],
                    ['id' => 'rate_id', 'width' => 150, 'fixed' => 1],
                    ['id' => 'homepage', 'width' => 230],
                    ['id' => 'mainContact', 'width' => 250],
                    ['id' => 'delivery_date', 'width' => 120],
                    ['id' => 'remark', 'width' => 250],
                    ['id' => 'follower', 'width' => 200],
                ]
            ],
            UserSetting::LIGHTHOUSE_PRIVATE_COMPANY_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'name', 'width' => 260, 'fixed' => 1],
                    ['id' => 'customer.name', 'width' => 260, 'fixed' => 0],
                    ['id' => 'customer.email', 'width' => 240, 'fixed' => 0],
                    ['id' => 'last_remark_trail', 'width' => 200, 'fixed' => 0],
                    ['id' => 'last_trail', 'width' => 200, 'fixed' => 0],
                    ['id' => 'group_name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'owner', 'width' => 200, 'fixed' => 0],
                    ['id' => 'star', 'width' => 160, 'fixed' => 0],
                    ['id' => 'trail_status_name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'tag', 'width' => 300, 'fixed' => 0],
                    ['id' => 'score', 'width' => 90, 'fixed' => 0],
                    ['id' => 'order_time', 'width' => 180, 'fixed' => 0],
                    ['id' => 'create_time', 'width' => 180, 'fixed' => 0],
                    ['id' => 'archive_type', 'width' => 180, 'fixed' => 0],
                ]
            ],
            UserSetting::LIGHTHOUSE_PUBLIC_COMPANY_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'name', 'width' => 260, 'fixed' => 1],
                    ['id' => 'last_remark_trail', 'width' => 200, 'fixed' => 0],
                    ['id' => 'last_trail', 'width' => 200, 'fixed' => 0],
                    ['id' => 'pool_name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'last_owner_name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'country', 'width' => 200, 'fixed' => 0],
                    ['id' => 'biz_type', 'width' => 200, 'fixed' => 0],
                    ['id' => 'origin_name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'score', 'width' => 90, 'fixed' => 0],
                    ['id' => 'order_time', 'width' => 180, 'fixed' => 0],
                    ['id' => 'create_time', 'width' => 180, 'fixed' => 0],
                ]
            ],
            UserSetting::PURCHASE_INBOUND_REFER_PRODUCT_TRANSFER_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'serial_id', 'width' => 200, 'fixed' => 1],// 入库任务编号
                    ['id' => 'name', 'width' => 200, 'fixed' => 0],//任务名称
                    ['id' => 'status', 'width' => 200, 'fixed' => 0],//任务状态
                    ['id' => 'purchase_order_id', 'width' => 200, 'fixed' => 0],//关联采购订单
                    ['id' => 'expect_time', 'width' => 200, 'fixed' => 0],//预计到货时间
                    ['id' => 'last_comment', 'width' => 200, 'fixed' => 0],//最新评论
                    ['id' => 'handler', 'width' => 200, 'fixed' => 0],//处理人
                    ['id' => 'supplier_no', 'width' => 200, 'fixed' => 0],//供应商
                    ['id' => 'supplier_id', 'width' => 200, 'fixed' => 0],//供应商
                    ['id' => 'finish_time', 'width' => 200, 'fixed' => 0],//任务完成时间
                    ['id' => 'remark', 'width' => 200, 'fixed' => 0],//任务备注
                    ['id' => 'task_progress', 'width' => 200, 'fixed' => 0],//任务进度
                    ['id' => 'create_user', 'width' => 200, 'fixed' => 0],//创建人
                    ['id' => 'create_time', 'width' => 200, 'fixed' => 0],//创建时间
                    ['id' => 'update_user', 'width' => 200, 'fixed' => 0],//修改人
                    ['id' => 'update_time', 'width' => 200, 'fixed' => 0],//修改时间
                ]
            ],
            UserSetting::PURCHASE_INBOUND_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'serial_id', 'width' => 230, 'fixed' => 1],
                    ['id' => 'warehouse_invoice_time', 'width' => 150],
                    ['id' => 'supplier_no', 'width' => 250],
                    ['id' => 'supplier_id', 'width' => 250],
                    ['id' => 'status', 'width' => 120],
                    ['id' => 'currency', 'width' => 120],
                    ['id' => 'product_total_amount', 'width' => 200],
                    ['id' => 'exchange_rate', 'width' => 200],
                    ['id' => 'exchange_rate_usd', 'width' => 200],
                    ['id' => 'product_total_amount_rmb', 'width' => 200],
                    ['id' => 'product_total_amount_usd', 'width' => 200],
                    ['id' => 'remark', 'width' => 200],
                    ['id' => 'handler', 'width' => 200],
                    ['id' => 'create_time', 'width' => 200],
                ]
            ],
            UserSetting::SALES_OUTBOUND_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'serial_id', 'width' => 230, 'fixed' => 1],
                    ['id' => 'warehouse_invoice_time', 'width' => 150, 'fixed' => 1],
                    ['id' => 'company_id', 'width' => 250],
                    ['id' => 'status', 'width' => 200],
                    ['id' => 'remark', 'width' => 200],
                    ['id' => 'handler', 'width' => 200],
                    ['id' => 'create_time', 'width' => 200],
                ]
            ],
            UserSetting::OTHER_INBOUND_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'serial_id', 'width' => 230, 'fixed' => 1],
                    ['id' => 'warehouse_invoice_time', 'width' => 150],
                    ['id' => 'status', 'width' => 250],
                    ['id' => 'cause_id', 'width' => 230],
                    ['id' => 'remark', 'width' => 200],
                    ['id' => 'handler', 'width' => 120],
                    ['id' => 'create_time', 'width' => 250],
                ]
            ],
            UserSetting::OTHER_OUTBOUND_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'serial_id', 'width' => 230, 'fixed' => 1],
                    ['id' => 'warehouse_invoice_time', 'width' => 150],
                    ['id' => 'status', 'width' => 250],
                    ['id' => 'cause_id', 'width' => 230],
                    ['id' => 'remark', 'width' => 200],
                    ['id' => 'handler', 'width' => 250],
                    ['id' => 'create_time', 'width' => 200],
                ]
            ],
            UserSetting::PURCHASE_RETURN_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'serial_id', 'width' => 230, 'fixed' => 1],
                    ['id' => 'warehouse_invoice_time', 'width' => 150],
                    ['id' => 'supplier_no', 'width' => 230],
                    ['id' => 'supplier_id', 'width' => 230],
                    ['id' => 'status', 'width' => 250],
                    ['id' => 'currency', 'width' => 120],
                    ['id' => 'product_total_amount', 'width' => 200],
                    ['id' => 'exchange_rate', 'width' => 200],
                    ['id' => 'exchange_rate_usd', 'width' => 200],
                    ['id' => 'product_total_amount_rmb', 'width' => 200],
                    ['id' => 'product_total_amount_usd', 'width' => 200],
                    ['id' => 'remark', 'width' => 200],
                    ['id' => 'handler', 'width' => 250],
                    ['id' => 'create_time', 'width' => 200],
                ]
            ],
            UserSetting::INVENTORY_REPORT_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => [],
                'default' => [
                    ['id' => 'product_no', 'width' => 180, 'fixed' => 1],
                    ['id' => 'currency', 'width' => 140, 'fixed' => 1],
                    ['id' => 'exchange_rate', 'width' => 140],
                    ['id' => 'exchange_rate_usd', 'width' => 140],
                    ['id' => 'name', 'width' => 210, 'fixed' => 1],
                    ['id' => 'group', 'width' => 180],
                    ['id' => 'cn_name', 'width' => 210],
                    ['id' => 'model', 'width' => 160],
                    ['id' => 'sku_attributes', 'width' => 160],
                    ['id' => 'unit', 'width' => 90],
                    ['id' => 'warehouse_id', 'width' => 200],
                    ['id' => 'type', 'width' => 120],
                    ['id' => 'serial_id', 'width' => 180],
                    ['id' => 'supplier_no', 'width' => 180],
                    ['id' => 'supplier_id', 'width' => 180],
                    ['id' => 'company_id', 'width' => 180],
                    ['id' => 'handler', 'width' => 120],
                    ['id' => 'warehouse_invoice_time', 'width' => 100],
                    ['id' => 'in_unit_price', 'width' => 120],
                    ['id' => 'in_unit_price_rmb', 'width' => 140],
                    ['id' => 'in_unit_price_usd', 'width' => 140],
                    ['id' => 'in_count', 'width' => 120],
                    ['id' => 'in_price', 'width' => 120],
                    ['id' => 'in_price_rmb', 'width' => 140],
                    ['id' => 'in_price_usd', 'width' => 140],
                    ['id' => 'out_unit_price', 'width' => 120],
                    ['id' => 'out_unit_price_rmb', 'width' => 140],
                    ['id' => 'out_unit_price_usd', 'width' => 140],
                    ['id' => 'out_count', 'width' => 120],
                    ['id' => 'out_price', 'width' => 120],
                    ['id' => 'out_price_rmb', 'width' => 140],
                    ['id' => 'out_price_usd', 'width' => 140],
                    ['id' => 'last_count', 'width' => 120, 'fixed' => 1],
                ]
            ],
            UserSetting::INVENTORY_QUERY_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => [],
                'default' => [
                    ['id' => 'images', 'width' => 180, 'fixed' => 1],
                    ['id' => 'product_no', 'width' => 160],
                    ['id' => 'name', 'width' => 160],
                    ['id' => 'model', 'width' => 120],
                    ['id' => 'sku_attributes', 'width' => 180],
                    ['id' => 'unit', 'width' => 90],
                    ['id' => 'enable_count', 'width' => 120],
                    ['id' => 'real_count', 'width' => 120],
                    ['id' => 'cost_unit_price', 'width' => 120],
                ]
            ],
            UserSetting::WAREHOUSE_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => [],
                'default' => [
                    ['id' => 'warehouse_no', 'width' => 120],
                    ['id' => 'name', 'width' => 160],
                    ['id' => 'manager', 'width' => 120],
                    ['id' => 'contact', 'width' => 144],
                    ['id' => 'address', 'width' => 200],
                    ['id' => 'remark', 'width' => 200],
                    ['id' => 'status', 'width' => 120],
                ]
            ],
            UserSetting::CAPITAL_ACCOUNT_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => [],
                'default' => [
                    ['id' => 'name', 'width' => 160],
                    ['id' => 'capital_name', 'width' => 160],
                    ['id' => 'capital_bank', 'width' => 160],
                    ['id' => 'bank_account', 'width' => 160],
                    ['id' => 'address', 'width' => 160],
                    ['id' => 'remark', 'width' => 160],
                    ['id' => 'status', 'width' => 88],
                ]
            ],
            UserSetting::PAGE_LAYOUT_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => [],
                'default' => [
                    ['id' => 'layout_name', 'width' => 200, 'fixed' => 1],
                    ['id' => 'privilege_tab', 'width' => 400],
                    ['id' => 'role_ids', 'width' => 200],
                    ['id' => 'remark', 'width' => 400],
                    ['id' => 'create_user', 'width' => 200],
                    ['id' => 'update_user', 'width' => 200],
                ]
            ],
            UserSetting::PRODUCT_TRANSFER_PURCHASE_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'serial_id', 'width' => 168],
                    ['id' => 'name', 'width' => 200],
                    ['id' => 'status', 'width' => 88],
                    ['id' => 'order_id', 'width' => 200],
                    ['id' => 'expect_time', 'width' => 160],
                    ['id' => 'last_comment', 'width' => 200],
                    ['id' => 'handler', 'width' => 120],
                    ['id' => 'product_total_sum', 'width' => 120],
                    ['id' => 'from_stock_sum', 'width' => 144],
                    ['id' => 'need_purchase_sum', 'width' => 120],
                    ['id' => 'have_purchase_sum', 'width' => 120],
                    ['id' => 'todo_purchase_sum', 'width' => 120],
                    ['id' => 'todo_purchase_count', 'width' => 120],
                    ['id' => 'purchase_order_no', 'width' => 280],
                    ['id' => 'purchase_product_count', 'width' => 160],
                    ['id' => 'delivery_date', 'width' => 160],
                    ['id' => 'finish_time', 'width' => 160],
                    ['id' => 'remark', 'width' => 200],
                    ['id' => 'create_user', 'width' => 120],
                    ['id' => 'create_time', 'width' => 160],
                    ['id' => 'update_user', 'width' => 120],
                    ['id' => 'update_time', 'width' => 160],
                ]
            ],
            UserSetting::PRODUCT_TRANSFER_INBOUND_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'serial_id', 'width' => 168],
                    ['id' => 'name', 'width' => 200],
                    ['id' => 'status', 'width' => 88],
                    ['id' => 'purchase_order_id', 'width' => 200],
                    ['id' => 'expect_time', 'width' => 160],
                    ['id' => 'last_comment', 'width' => 200],
                    ['id' => 'handler', 'width' => 120],
                    ['id' => 'supplier_no', 'width' => 200],
                    ['id' => 'supplier_id', 'width' => 200],
                    ['id' => 'purchase_sum', 'width' => 120],
                    ['id' => 'have_inbound_sum', 'width' => 120],
                    ['id' => 'todo_inbound_sum', 'width' => 120],
                    ['id' => 'inbound_no', 'width' => 280],
                    ['id' => 'inbound_product_count', 'width' => 280],
                    ['id' => 'inbound_date', 'width' => 160],
                    ['id' => 'finish_time', 'width' => 160],
                    ['id' => 'remark', 'width' => 200],
                    ['id' => 'create_user', 'width' => 120],
                    ['id' => 'create_time', 'width' => 160],
                    ['id' => 'update_user', 'width' => 120],
                    ['id' => 'update_time', 'width' => 160],
                ]
            ],
            UserSetting::PRODUCT_TRANSFER_OUTBOUND_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'serial_id', 'width' => 168],
                    ['id' => 'name', 'width' => 200],
                    ['id' => 'status', 'width' => 88],
                    ['id' => 'order_id', 'width' => 200],
                    ['id' => 'expect_time', 'width' => 160],
                    ['id' => 'last_comment', 'width' => 200],
                    ['id' => 'handler', 'width' => 120],
                    ['id' => 'shipping_voucher_number', 'width' => 200],
                    ['id' => 'product_total_sum', 'width' => 120],
                    ['id' => 'have_outbound_sum', 'width' => 120],
                    ['id' => 'todo_outbound_sum', 'width' => 120],
                    ['id' => 'outbound_no', 'width' => 280],
                    ['id' => 'outbound_product_count', 'width' => 160],
                    ['id' => 'outbound_date', 'width' => 160],
                    ['id' => 'finish_time', 'width' => 160],
                    ['id' => 'remark', 'width' => 200],
                    ['id' => 'create_user', 'width' => 120],
                    ['id' => 'create_time', 'width' => 160],
                    ['id' => 'update_user', 'width' => 120],
                    ['id' => 'update_time', 'width' => 160],
                ]
            ],
            UserSetting::OTHER_TRANSFER_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'serial_id', 'width' => 230, 'fixed' => 1],
                    ['id' => 'refer_id', 'width' => 230, 'fixed' => 1],
                    ['id' => 'status', 'width' => 150, 'fixed' => 1],
                    ['id' => 'handler', 'width' => 250],
                    ['id' => 'expect_time', 'width' => 150],
                    ['id' => 'finish_time', 'width' => 150],
                    ['id' => 'remark', 'width' => 200],
                    ['id' => 'create_user', 'width' => 200],
                    ['id' => 'create_time', 'width' => 200],
                    ['id' => 'update_user', 'width' => 200],
                    ['id' => 'update_time', 'width' => 200],
                ]
            ],
            UserSetting::PLATFORM_PRODUCT_LIST_FIELD => [
                'base' => [PrivilegeConstants::FUNCTIONAL_PLATFORM_PRODUCT],
                'field' => [],
                'type' => \Constants::TYPE_PLATFORM_PRODUCT_SKU,
                'default' => [
                    ['id' => 'product_image', 'width' => 88, 'fixed' => 1],
                    ['id' => 'third_product_id', 'width' => 180, 'fixed' => 0],
                    ['id' => 'store_name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'model', 'width' => 200, 'fixed' => 0],
                    ['id' => 'attributes_info', 'width' => 200, 'fixed' => 0],
                    ['id' => 'third_sku_code', 'width' => 200, 'fixed' => 0],
                    ['id' => 'sync_time', 'width' => 200, 'fixed' => 0],
                    ['id' => 'local_sku', 'width' => 180, 'fixed' => 0],
                    ['id' => 'is_match', 'width' => 88, 'fixed' => 0],
                ]
            ],
            UserSetting::TRADE_DOCUMENT_LIST_FIELD => [
                'base' => [PrivilegeConstants::FUNCTIONAL_DOCUMENT],
                'field' => [],
                'type' => \Constants::TYPE_TRADE_DOCUMENT,
                'default' => [
                    ['id' => 'name', 'width' => 200, 'fixed' => 1],
                    ['id' => 'category', 'width' => 100, 'fixed' => 0],
                    ['id' => 'owner', 'width' => 100, 'fixed' => 0],
                    ['id' => 'custom_description', 'width' => 100, 'fixed' => 0],
                    ['id' => 'links', 'width' => 200, 'fixed' => 0],
                    ['id' => 'customer', 'width' => 200, 'fixed' => 0],
                    ['id' => 'status', 'width' => 88, 'fixed' => 0],
                    ['id' => 'is_accessible', 'width' => 88, 'fixed' => 0],
                    ['id' => 'latest_track', 'width' => 300, 'fixed' => 0],
                    ['id' => 'create_user', 'width' => 200, 'fixed' => 0],
                    ['id' => 'update_user', 'width' => 200, 'fixed' => 0],
                    ['id' => 'create_time', 'width' => 120, 'fixed' => 0],
                    ['id' => 'update_time', 'width' => 120, 'fixed' => 0],
                    ['id' => 'first_release_time', 'width' => 120, 'fixed' => 0],
                    ['id' => 'latest_release_time', 'width' => 120, 'fixed' => 0],
                ]
            ],
            UserSetting::PAYMENT_INVOICE_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'handler', 'width' => 250],
                    ['id' => 'payment_invoice_no', 'width' => 250],
                    ['id' => 'refer_no_info', 'width' => 250],
                    ['id' => 'status', 'width' => 150],
                    ['id' => 'supplier_id', 'width' => 200],
                    ['id' => 'account_name', 'width' => 230],
                    ['id' => 'bank_name', 'width' => 150],
                    ['id' => 'bank_account', 'width' => 200],
                    ['id' => 'expect_time', 'width' => 200],
                    ['id' => 'currency', 'width' => 200],
                    ['id' => 'exchange_rate', 'width' => 200],
                    ['id' => 'amount', 'width' => 200],
                    ['id' => 'payment_time', 'width' => 200],
                    ['id' => 'trade_no', 'width' => 200],
                    ['id' => 'remark', 'width' => 200],
                    ['id' => 'create_user', 'width' => 200],
                    ['id' => 'create_time', 'width' => 200],
                    ['id' => 'update_user', 'width' => 200],
                    ['id' => 'update_time', 'width' => 200],
                ]
            ],
            UserSetting::CASH_COLLECTION_INVOICE_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => \Constants::TYPE_CASH_COLLECTION_INVOICE,
                'default' => [
                    ['id' => 'cash_collection_invoice_no', 'width' => 200],
                    ['id' => 'status', 'width' => 200],
                    ['id' => 'collection_date', 'width' => 200],
                    ['id' => 'capital_account_id', 'width' => 200],
                    ['id' => 'type', 'width' => 200],
                    ['id' => 'currency', 'width' => 200],
                    ['id' => 'exchange_rate', 'width' => 200],
                    ['id' => 'exchange_rate_usd', 'width' => 200],
                    ['id' => 'amount', 'width' => 200],
                    ['id' => 'real_amount', 'width' => 200],
                    ['id' => 'bank_charge', 'width' => 200],
                    ['id' => 'trade_no', 'width' => 200],
                    ['id' => 'account_name', 'width' => 200],
                    ['id' => 'bank_name', 'width' => 200],
                    ['id' => 'bank_account', 'width' => 200],
                    ['id' => 'handler', 'width' => 200],
                    ['id' => 'company_id', 'width' => 200],
                    ['id' => 'remark', 'width' => 200],
                    ['id' => 'allocated_amount', 'width' => 200],
                    ['id' => 'current_amount', 'width' => 200],
                    ['id' => 'create_user', 'width' => 200],
                    ['id' => 'create_time', 'width' => 200],
                    ['id' => 'update_user', 'width' => 200],
                    ['id' => 'update_time', 'width' => 200],
                    ['id' => 'create_type', 'width' => 200],
                ]
            ],
            UserSetting::COST_INVOICE_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => \Constants::TYPE_COST_INVOICE,
                'default' => [
                    ['id' => 'cost_invoice_no', 'width' => 200],
                    ['id' => 'refer_id', 'width' => 200],
                    ['id' => 'cost_item_id', 'width' => 200],
                    ['id' => 'receive_refer_id', 'width' => 200],
                    ['id' => 'currency', 'width' => 200],
                    ['id' => 'exchange_rate', 'width' => 200],
                    ['id' => 'amount', 'width' => 200],
                    ['id' => 'cost_time', 'width' => 200],
                    ['id' => 'expect_time', 'width' => 200],
                    ['id' => 'account_name', 'width' => 200],
                    ['id' => 'bank_name', 'width' => 200],
                    ['id' => 'payment_amount', 'width' => 200],
                    ['id' => 'payment_wait_amount', 'width' => 200],
                    ['id' => 'remark', 'width' => 200],
                    ['id' => 'handler', 'width' => 200],
                    ['id' => 'status', 'width' => 200],
                    ['id' => 'create_user', 'width' => 200],
                    ['id' => 'create_time', 'width' => 200],
                    ['id' => 'update_user', 'width' => 200],
                    ['id' => 'update_time', 'width' => 200],
                ]
            ],
            UserSetting::PRODUCT_SELECTOR_QUERY_LIST_FIELD => [
                'base' => [PrivilegeConstants::FUNCTIONAL_PRODUCT],
                'field' => [],
                'type' => \Constants::TYPE_PRODUCT,
                'default' => [
                    ['id' => 'images', 'width' => 70, 'fixed' => 0],
                    ['id' => 'product_no', 'width' => 100, 'fixed' => 0],
                    ['id' => 'name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'cn_name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'model', 'width' => 200, 'fixed' => 0],
                    ['id' => 'sku_attributes', 'width' => 200, 'fixed' => 0],
                    ['id' => 'fob', 'width' => 200, 'fixed' => 0],
                    ['id' => 'group_id', 'width' => 200, 'fixed' => 0],
                    ['id' => 'category_ids', 'width' => 200, 'fixed' => 0],
                    ['id' => 'source_type', 'width' => 200, 'fixed' => 0],
                ]
            ],
            UserSetting::ORDER_PROFIT_DEPARTMENT_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => \Constants::TYPE_ORDER_PROFIT,
                'default' => [
                    ['id' => 'order_no', 'width' => 160, 'fixed' => 0],
                    ['id' => 'order_name', 'width' => 160, 'fixed' => 0],
                    ['id' => 'order_status', 'width' => 96, 'fixed' => 0],
                    ['id' => 'company_name', 'width' => 160, 'fixed' => 0],
                    ['id' => 'order_performance_department', 'width' => 200, 'fixed' => 0],
                    ['id' => 'order_performance_department_rate', 'width' => 200, 'fixed' => 0],
                    ['id' => 'account_date', 'width' => 200, 'fixed' => 0],
                    ['id' => 'real_profit_rmb', 'width' => 164, 'fixed' => 0],
                    ['id' => 'real_sale_profit_rate', 'width' => 164, 'fixed' => 0],
                    ['id' => 'predict_profit_rmb', 'width' => 164, 'fixed' => 0],
                    ['id' => 'predict_sale_profit_rate', 'width' => 164, 'fixed' => 0],
                    ['id' => 'order_currency', 'width' => 87, 'fixed' => 0],
                    ['id' => 'order_exchange_rate', 'width' => 87, 'fixed' => 0],
                    ['id' => 'order_amount', 'width' => 164, 'fixed' => 0],
                    ['id' => 'order_amount_rmb', 'width' => 164, 'fixed' => 0],
                    ['id' => 'cash_collection_collect_amount_rmb', 'width' => 164, 'fixed' => 0],
                    ['id' => 'purchase_order_product_amount_rmb', 'width' => 180, 'fixed' => 0],
                    ['id' => 'purchase_order_amount_rmb', 'width' => 180, 'fixed' => 0],
                    ['id' => 'purchase_order_product_payment_amount_rmb', 'width' => 180, 'fixed' => 0],
                    ['id' => 'purchase_order_product_not_payment_amount_rmb', 'width' => 188, 'fixed' => 0],
                    ['id' => 'outbound_product_completed_inventory_amount_rmb', 'width' => 188, 'fixed' => 0],
                    ['id' => 'outbound_product_inventory_amount_rmb', 'width' => 188, 'fixed' => 0],
                    ['id' => 'cost_invoice_rmb', 'width' => 200, 'fixed' => 0],
                    ['id' => 'payment_cost_invoice_rmb', 'width' => 188, 'fixed' => 0],
                    ['id' => 'not_payment_cost_invoice_rmb', 'width' => 188, 'fixed' => 0],
                    ['id' => 'tax_refund_rmb', 'width' => 164, 'fixed' => 0],
                ]
            ],
            UserSetting::ORDER_PROFIT_MEMBER_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => \Constants::TYPE_ORDER_PROFIT,
                'default' => [
                    ['id' => 'order_no', 'width' => 160, 'fixed' => 0],
                    ['id' => 'order_name', 'width' => 160, 'fixed' => 0],
                    ['id' => 'order_status', 'width' => 96, 'fixed' => 0],
                    ['id' => 'company_name', 'width' => 160, 'fixed' => 0],
                    ['id' => 'order_performance_user', 'width' => 120, 'fixed' => 0],
                    ['id' => 'order_performance_user_rate', 'width' => 148, 'fixed' => 0],
                    ['id' => 'account_date', 'width' => 200, 'fixed' => 0],
                    ['id' => 'real_profit_rmb', 'width' => 164, 'fixed' => 0],
                    ['id' => 'real_sale_profit_rate', 'width' => 164, 'fixed' => 0],
                    ['id' => 'predict_profit_rmb', 'width' => 164, 'fixed' => 0],
                    ['id' => 'predict_sale_profit_rate', 'width' => 164, 'fixed' => 0],
                    ['id' => 'order_currency', 'width' => 87, 'fixed' => 0],
                    ['id' => 'order_exchange_rate', 'width' => 87, 'fixed' => 0],
                    ['id' => 'order_amount', 'width' => 164, 'fixed' => 0],
                    ['id' => 'order_amount_rmb', 'width' => 164, 'fixed' => 0],
                    ['id' => 'cash_collection_collect_amount_rmb', 'width' => 164, 'fixed' => 0],
                    ['id' => 'purchase_order_product_amount_rmb', 'width' => 180, 'fixed' => 0],
                    ['id' => 'purchase_order_amount_rmb', 'width' => 180, 'fixed' => 0],
                    ['id' => 'purchase_order_product_payment_amount_rmb', 'width' => 180, 'fixed' => 0],
                    ['id' => 'purchase_order_product_not_payment_amount_rmb', 'width' => 188, 'fixed' => 0],
                    ['id' => 'outbound_product_completed_inventory_amount_rmb', 'width' => 188, 'fixed' => 0],
                    ['id' => 'outbound_product_inventory_amount_rmb', 'width' => 188, 'fixed' => 0],
                    ['id' => 'cost_invoice_rmb', 'width' => 200, 'fixed' => 0],
                    ['id' => 'payment_cost_invoice_rmb', 'width' => 188, 'fixed' => 0],
                    ['id' => 'not_payment_cost_invoice_rmb', 'width' => 188, 'fixed' => 0],
                    ['id' => 'tax_refund_rmb', 'width' => 164, 'fixed' => 0],
                ]
            ],
            UserSetting::SHIPPING_INVOICE_PRIVATE_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => \Constants::TYPE_SHIPPING_INVOICE,
                'default' => [
                    ['id' => 'shipping_invoice_no', 'width' => 160, 'fixed' => 0],
                    ['id' => 'shipping_status', 'width' => 88, 'fixed' => 0],
                    ['id' => 'tracking_status', 'width' => 88, 'fixed' => 0],
                    ['id' => 'outbound_status', 'width' => 88, 'fixed' => 0],
                    ['id' => 'handler', 'width' => 88, 'fixed' => 0],
                    ['id' => 'company_id', 'width' => 200, 'fixed' => 0],
                    ['id' => 'customer_id', 'width' => 160, 'fixed' => 0],
                    ['id' => 'order_id', 'width' => 160, 'fixed' => 0],
                    ['id' => 'forwarder_id', 'width' => 200, 'fixed' => 0],
                    ['id' => 'forwarder_contact_id', 'width' => 160, 'fixed' => 0],
                    ['id' => 'shipping_mode', 'width' => 120, 'fixed' => 0],
                    ['id' => 'shipping_address', 'width' => 164, 'fixed' => 0],
                    ['id' => 'shipping_enterprise', 'width' => 240, 'fixed' => 0],
                    ['id' => 'shipping_voucher_no', 'width' => 240, 'fixed' => 0],
                    ['id' => 'shipment_port', 'width' => 200, 'fixed' => 0],
                    ['id' => 'target_port', 'width' => 200, 'fixed' => 0],
                    ['id' => 'customs_broker', 'width' => 200, 'fixed' => 0],
                    ['id' => 'shipping_deadline', 'width' => 160, 'fixed' => 0],
                    ['id' => 'shipping_time', 'width' => 160, 'fixed' => 0],
                    ['id' => 'arrival_time', 'width' => 160, 'fixed' => 0],
                    ['id' => 'shipping_cycle', 'width' => 120, 'fixed' => 0],
                    ['id' => 'finish_time', 'width' => 160, 'fixed' => 0],
                    ['id' => 'currency', 'width' => 88, 'fixed' => 0],
                    ['id' => 'product_total_amount', 'width' => 160, 'fixed' => 0],
                    ['id' => 'addition_cost_amount', 'width' => 160, 'fixed' => 0],
                    ['id' => 'shipping_total_amount', 'width' => 160, 'fixed' => 0],
                    ['id' => 'exchange_rate', 'width' => 160, 'fixed' => 0],
                    ['id' => 'product_total_amount_rmb', 'width' => 200, 'fixed' => 0],
                    ['id' => 'addition_cost_amount_rmb', 'width' => 200, 'fixed' => 0],
                    ['id' => 'shipping_total_amount_rmb', 'width' => 200, 'fixed' => 0],
                    ['id' => 'exchange_rate_usd', 'width' => 164, 'fixed' => 0],
                    ['id' => 'product_total_amount_usd', 'width' => 200, 'fixed' => 0],
                    ['id' => 'addition_cost_amount_usd', 'width' => 200, 'fixed' => 0],
                    ['id' => 'shipping_total_amount_usd', 'width' => 200, 'fixed' => 0],

//                    ['id' => 'remark', 'width' => 164, 'fixed' => 0],

//                    ['id' => 'create_user', 'width' => 164, 'fixed' => 0],
//                    ['id' => 'create_time', 'width' => 164, 'fixed' => 0],
//                    ['id' => 'update_user', 'width' => 164, 'fixed' => 0],
//                    ['id' => 'update_time', 'width' => 164, 'fixed' => 0],

                ]
            ],
            UserSetting::SHIPPING_RECORD_SELECT_PRODUCT_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => \Constants::TYPE_SHIPPING_INVOICE,
                'default' => [
                    ['id' => 'product_info', 'width' => 200, 'fixed' => 1],
                    ['id' => 'refer_order_no', 'width' => 160, 'fixed' => 1],
                    ['id' => 'enable_count', 'width' => 120],
                    ['id' => 'description', 'width' => 120],
                    ['id' => 'unit', 'width' => 92],
                    ['id' => 'todo_shipping_count', 'width' => 92],
                ]
            ],
//            UserSetting::SHIPPING_INVOICE_PRIVATE_RECORD_SELECT_LIST_FIELD => [
//                'base' => [],
//                'field' => [],
//                'type' => \Constants::TYPE_SHIPPING_INVOICE,
//                'default' => [
//                    ['id' => 'product_info', 'width' => 447, 'fixed' => 1],
//                    ['id' => 'order_no', 'width' => 177, 'fixed' => 1],
//                    ['id' => 'todo_shipping_count', 'width' => 120, 'fixed' => 1],//待出运数量
//                    ['id' => 'description', 'width' => 160, 'fixed' => 1],//产品描述
//                ]
//            ],
            UserSetting::FORWARDER_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => \Constants::TYPE_FORWARDER,
                'default' => [
                    ['id' => 'name', 'width' => 230, 'fixed' => 1],
                    ['id' => 'rate_id', 'width' => 150, 'fixed' => 1],
                    ['id' => 'website', 'width' => 230],
                    ['id' => 'address', 'width' => 230],
                    ['id' => 'remark', 'width' => 250],
                    ['id' => 'handler', 'width' => 230],
                ]
            ],
            UserSetting::PURCHASE_RECORD_SELECT_PRODUCT_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => \Constants::TYPE_PURCHASE_ORDER,
                'default' => []
            ],
            UserSetting::PURCHASE_PUSH_DOWN_SPLIT_ORDER_PRODUCT_LIST_FIELD => [
                 'base' => [],
                 'field' => [],
                 'type' => \Constants::TYPE_PURCHASE_ORDER,
                 'default' => [
                      [
                        'id' => 'product_info',
                        'width' => 200,
                        'fixed' => 1,
                      ],
                      [
                        'id' => 'purchase_supplier_id',
                        'width' => 200,
                        'fixed' => 0,
                      ],
                      [
                        'id' => 'purchase_currency',
                        'width' => 200,
                        'fixed' => 0,
                      ],
                      [
                        'id' => 'purchase_unit_price',
                        'width' => 200,
                        'fixed' => 0,
                      ],
                      [
                        "id" => "purchase_count",
                        "width" => 200,
                        "fixed" => 0
                      ],
                      [
                         'id' => 'to_purchase_count',
                         'width' =>  200,
                         'fixed' =>  0
                      ],
                      [
                        'id' => 'count',
                        'width' =>  200,
                        'fixed' =>  0
                      ],
                      [
                        'id' => 'product_remark',
                        'width' =>  200,
                        'fixed' =>  0
                      ],
                      [
                        'id' => 'unit_price',
                        'width' =>  200,
                        'fixed' =>  0
                      ],
                      [
                        'id' => 'description',
                        'width' =>  200,
                        'fixed' =>  0
                      ],

                ]
            ],
            UserSetting::PURCHASE_RECORD_REFER_ORDER_RECORD_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => \Constants::TYPE_PURCHASE_ORDER,
                'default' => []
            ],
            UserSetting::PURCHASE_RECORD_REFER_PURCHASE_PRODUCT_TRANSFER_RECORD_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => \Constants::TYPE_PURCHASE_ORDER,
                'default' => []
            ],
            UserSetting::PURCHASE_RECORD_REFER_PURCHASE_PRODUCT_TRANSFER_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'serial_id', 'width' => 168],
                    ['id' => 'name', 'width' => 200],
                    ['id' => 'status', 'width' => 88],
                    ['id' => 'order_id', 'width' => 200],
                    ['id' => 'expect_time', 'width' => 160],
                    ['id' => 'last_comment', 'width' => 200],
                    ['id' => 'handler', 'width' => 120],
                    ['id' => 'product_total_sum', 'width' => 120],
                    ['id' => 'from_stock_sum', 'width' => 144],
                    ['id' => 'need_purchase_sum', 'width' => 120],
                    ['id' => 'have_purchase_sum', 'width' => 120],
                    ['id' => 'todo_purchase_sum', 'width' => 120],
                    ['id' => 'todo_purchase_count', 'width' => 120],
                    ['id' => 'purchase_order_no', 'width' => 280],
                    ['id' => 'purchase_product_count', 'width' => 160],
                    ['id' => 'delivery_date', 'width' => 160],
                    ['id' => 'finish_time', 'width' => 160],
                    ['id' => 'remark', 'width' => 200],
                    ['id' => 'create_user', 'width' => 120],
                    ['id' => 'create_time', 'width' => 160],
                    ['id' => 'update_user', 'width' => 120],
                    ['id' => 'update_time', 'width' => 160],
                ]
            ],
            UserSetting::PURCHASE_INBOUND_RECORD_REFER_PURCHASE_PRODUCT_TRANSFER_RECORD_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'product_info', 'width' => 200, 'fixed' => 1],
                    ['id' => 'product_unit', 'width' => 120, 'fixed' => 0],
                    ['id' => 'purchase_count', 'width' => 120, 'fixed' => 0],
                    ['id' => 'description', 'width' => 120, 'fixed' => 0],
                    ['id' => 'record_remark', 'width' => 120, 'fixed' => 0],
                    ['id' => 'purchase_order_no', 'width' => 200, 'fixed' => 0],
                    ['id' => 'transfer_invoice_serial_id', 'width' => 200, 'fixed' => 0],
                    ['id' => 'to_inbound_count', 'width' => 120, 'fixed' => 0],
                    ['id' => 'task_inbound_count', 'width' => 120, 'fixed' => 0],
                    ['id' => 'have_inbound_count', 'width' => 120, 'fixed' => 0],
                    ['id' => 'reach_count', 'width' => 120, 'fixed' => 0],
                ]
            ],
            UserSetting::PURCHASE_INBOUND_RECORD_REFER_PURCHASE_ORDER_RECORD_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'product_info', 'width' => 200, 'fixed' => 1],
                    ['id' => 'unit', 'width' => 120, 'fixed' => 0],
                    ['id' => 'supplier_product_no', 'width' => 200, 'fixed' => 0],
                    ['id' => 'supplier_product_name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'product_remark', 'width' => 120, 'fixed' => 0],
                    ['id' => 'order_no', 'width' => 200, 'fixed' => 0],
                    ['id' => 'purchase_order_no', 'width' => 200, 'fixed' => 0],
                    ['id' => 'transfer_invoice_serial_id', 'width' => 200, 'fixed' => 0],
                    ['id' => 'count', 'width' => 120, 'fixed' => 0],
                    ['id' => 'unit_price', 'width' => 120, 'fixed' => 0],
                    ['id' => 'cost_amount', 'width' => 120, 'fixed' => 0],
                    ['id' => 'have_return_count', 'width' => 120, 'fixed' => 0],
                    ['id' => 'have_inbound_count', 'width' => 120, 'fixed' => 0],
                    ['id' => 'wait_inbound_count', 'width' => 120, 'fixed' => 0],
                ]
            ],
            UserSetting::SELECT_PURCHASE_PRODUCT_TO_INBOUND_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'product_image', 'width' => 80],
                    ['id' => 'product_name', 'width' => 150],
                    ['id' => 'product_cn_name', 'width' => 150],
                    ['id' => 'sku_code', 'width' => 200],
                    ['id' => 'product_model', 'width' => 150],
                    ['id' => 'sku_attributes', 'width' => 150],
                    ['id' => 'product_unit', 'width' => 100],
                    ['id' => 'purchase_count', 'width' => 100],
                    ['id' => 'description', 'width' => 150],
                    ['id' => 'record_remark', 'width' => 150],
                    ['id' => 'to_inbound_count', 'width' => 150],
                    ['id' => 'task_inbound_count', 'width' => 150],
                    ['id' => 'have_inbound_count', 'width' => 150],
                    ['id' => 'reach_count', 'width' => 150],
                    ['id' => 'serial_id', 'width' => 200],
                    ['id' => 'name', 'width' => 100],
                    ['id' => 'status', 'width' => 100],
                    ['id' => 'purchase_order_id', 'width' => 200],
                    ['id' => 'expect_time', 'width' => 200],
                    ['id' => 'last_comment', 'width' => 150],
                    ['id' => 'handler', 'width' => 150],
                    ['id' => 'supplier_no', 'width' => 150],
                    ['id' => 'supplier_id', 'width' => 150],
                    ['id' => 'finish_time', 'width' => 150],
                    ['id' => 'remark', 'width' => 150],
                    ['id' => 'task_progress', 'width' => 150],
                    ['id' => 'create_user', 'width' => 100],
                    ['id' => 'create_time', 'width' => 150],
//                    ['id' => 'update_user', 'width' => 200],
//                    ['id' => 'update_time', 'width' => 200],

                    //币种 汇率
                    ['id' => 'currency', 'width' => 100],
                    ['id' => 'exchange_rate', 'width' => 150],
                    ['id' => 'exchange_rate_usd', 'width' => 150],


//                    ['id' => 'check_inbound_count', 'width' => 200],
//                    ['id' => 'unit_price', 'width' => 200],
//                    ['id' => 'inbound_count', 'width' => 200],
//                    ['id' => 'product_amount', 'width' => 200],
                ]
            ],
            UserSetting::SELECT_PURCHASE_PRODUCT_TO_INBOUND_CONFIRM_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'product_image', 'width' => 80, 'fixed' => 1],
                    ['id' => 'product_name', 'width' => 150, 'fixed' => 1],
                    ['id' => 'product_cn_name', 'width' => 150],
                    ['id' => 'sku_code', 'width' => 200],
                    ['id' => 'product_model', 'width' => 150],
                    ['id' => 'sku_attributes', 'width' => 150],
                    ['id' => 'product_unit', 'width' => 100],
                    ['id' => 'purchase_count', 'width' => 100],
                    ['id' => 'description', 'width' => 150],
                    ['id' => 'record_remark', 'width' => 150],
                    ['id' => 'to_inbound_count', 'width' => 150],
                    ['id' => 'task_inbound_count', 'width' => 150],
                    ['id' => 'have_inbound_count', 'width' => 150],
                    ['id' => 'reach_count', 'width' => 150],
                    ['id' => 'unit_price', 'width' => 150],
                    ['id' => 'inbound_count', 'width' => 150],
                    ['id' => 'product_amount', 'width' => 150],
                    ['id' => 'serial_id', 'width' => 200],
//                    ['id' => 'name', 'width' => 100],
//                    ['id' => 'status', 'width' => 100],
                    ['id' => 'purchase_order_id', 'width' => 200],
                    ['id' => 'expect_time', 'width' => 200],
                    ['id' => 'last_comment', 'width' => 150],
//                    ['id' => 'handler', 'width' => 150],
                    ['id' => 'supplier_no', 'width' => 150],
                    ['id' => 'supplier_id', 'width' => 150],
                    ['id' => 'finish_time', 'width' => 150],
//                    ['id' => 'remark', 'width' => 150],
//                    ['id' => 'task_progress', 'width' => 150],
                    ['id' => 'create_user', 'width' => 100],
                    ['id' => 'create_time', 'width' => 150],
//                    ['id' => 'update_user', 'width' => 100],
//                    ['id' => 'update_time', 'width' => 150],
                    ['id' => 'currency', 'width' => 100],
                    ['id' => 'exchange_rate', 'width' => 150],
                    ['id' => 'exchange_rate_usd', 'width' => 150],
                ]
            ],
            UserSetting::SUPPLIER_PRODUCT_PRIVATE_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => '',
                'default' => [
                    ['id' => 'supplier_no', 'width' => 160],
                    ['id' => 'supplier_name', 'width' => 200],
                    ['id' => 'user_ids', 'width' => 88],
                    ['id' => 'product_image', 'width' => 88],
                    ['id' => 'sku_code', 'width' => 160],
                    ['id' => 'product_name', 'width' => 200],
                    ['id' => 'product_cn_name', 'width' => 200],
                    ['id' => 'product_model', 'width' => 160],
                    ['id' => 'sku_attributes', 'width' => 200],
                    ['id' => 'supplier_product_no', 'width' => 160],
                    ['id' => 'supplier_product_name', 'width' => 200],
                    ['id' => 'quantity', 'width' => 88],
                    ['id' => 'price', 'width' => 200],
                    ['id' => 'expire_date', 'width' => 200],
                    ['id' => 'remark', 'width' => 200],
                    ['id' => 'create_time', 'width' => 200],
                    ['id' => 'create_user', 'width' => 200],
                    ['id' => 'update_time', 'width' => 200],
                ]
            ],
            UserSetting::PRODUCT_INVENTORY_WARNING_PRIVATE_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => \Constants::TYPE_PRODUCT_INVENTORY_WARNING,
                'default' => [
                    ['id' => 'images', 'width' => 200, 'fixed' => 1],
                    ['id' => 'product_no', 'width' => 120, 'fixed' => 0],
                    ['id' => 'name', 'width' => 120, 'fixed' => 0],
                    ['id' => 'cn_name', 'width' => 120, 'fixed' => 0],
                    ['id' => 'model', 'width' => 120, 'fixed' => 0],
                    ['id' => 'sku_attributes', 'width' => 200, 'fixed' => 0],
                    ['id' => 'warning_status', 'width' => 200, 'fixed' => 0],
                    ['id' => 'limit_count', 'width' => 120, 'fixed' => 0],
                    ['id' => 'warehouse_id', 'width' => 120, 'fixed' => 0],
                    ['id' => 'enable_count', 'width' => 120, 'fixed' => 0],
                    ['id' => 'real_count', 'width' => 120, 'fixed' => 0]
                ]
            ],
            UserSetting::QUOTATION_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => \Constants::TYPE_QUOTATION,
                'default' => [
                    ['id' => 'quotation_no', 'width' => 200, 'fixed' => 1],
                    ['id' => 'name', 'width' => 120, 'fixed' => 0],
                    ['id' => 'status', 'width' => 120, 'fixed' => 0],
                    ['id' => 'company_id', 'width' => 120, 'fixed' => 0],
                    ['id' => 'model', 'width' => 120, 'fixed' => 0],
                    ['id' => 'handler', 'width' => 200, 'fixed' => 0],
                    ['id' => 'quotation_date', 'width' => 200, 'fixed' => 0],
                    ['id' => 'approval_status', 'width' => 120, 'fixed' => 0],
                    ['id' => 'refer_order_ids', 'width' => 120, 'fixed' => 0],
                ]
            ],
            UserSetting::INQUIRY_COLLABORATION_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => \Constants::TYPE_INQUIRY_COLLABORATION,
                'default' => [
                    ['id' => 'inquiry_collaboration_no','width' => 160, 'fixed' => 1],
                    ['id' => 'inquiry_collaboration_name','width' => 200, 'fixed' => 0],
                    ['id' => 'status','width' => 120, 'fixed' => 0],
                    ['id' => 'progress','width' => 200, 'fixed' => 0],
                    ['id' => 'latest_comment','width' => 304, 'fixed' => 0],
                    ['id' => 'handler','width' => 160, 'fixed' => 0],
                    ['id' => 'inquiry_deadline','width' => 200, 'fixed' => 0],
                    ['id' => 'remark','width' => 200, 'fixed' => 0],
                    ['id' => 'finish_time','width' => 200, 'fixed' => 0],
                    ['id' => 'company_id','width' => 200, 'fixed' => 0],
                    ['id' => 'lead_id','width' => 200, 'fixed' => 0],
                    ['id' => 'customer_id','width' => 200, 'fixed' => 0],
                    ['id' => 'customer_name','width' => 200, 'fixed' => 0],
                    ['id' => 'country','width' => 120, 'fixed' => 0],
                    ['id' => 'currency','width' => 120, 'fixed' => 0],
                    ['id' => 'product_total_count','width' => 120, 'fixed' => 0],
                    ['id' => 'product_total_amount','width' => 160, 'fixed' => 0],
                    ['id' => 'create_user','width' => 160, 'fixed' => 0],
                    ['id' => 'create_time','width' => 200, 'fixed' => 0],
                    ['id' => 'update_user','width' => 160, 'fixed' => 0],
                    ['id' => 'update_time','width' => 200, 'fixed' => 0],
                ]
            ],
            UserSetting::INQUIRY_COLLABORATION_PRODUCT_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => \Constants::TYPE_INQUIRY_COLLABORATION,
                'default' => [
                    ['id' => 'product_no', 'width' => 160, 'fixed' => 1],
                    ['id' => 'product_images', 'width' => 92, 'fixed' => 0],
                    ['id' => 'product_name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'product_cn_name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'sku_attributes', 'width' => 160, 'fixed' => 0],
                    ['id' => 'description', 'width' => 160, 'fixed' => 0],
                    ['id' => 'product_remark', 'width' => 160, 'fixed' => 0],
                    ['id' => 'tax_included', 'width' => 120, 'fixed' => 0],
                    ['id' => 'purchase_quote_cny', 'width' => 160, 'fixed' => 0],
                    ['id' => 'purchase_quote', 'width' => 160, 'fixed' => 0],
                    ['id' => 'count', 'width' => 160, 'fixed' => 0],
                    ['id' => 'supplier_id', 'width' => 160, 'fixed' => 0],
                    ['id' => 'price_validity_date', 'width' => 200, 'fixed' => 0],
                    ['id' => 'market_information', 'width' => 200, 'fixed' => 0],
                    ['id' => 'inquiry_collaboration_no', 'width' => 160, 'fixed' => 0],
                    ['id' => 'inquiry_collaboration_name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'status', 'width' => 120, 'fixed' => 0],
                    ['id' => 'latest_comment', 'width' => 304, 'fixed' => 0],
                    ['id' => 'handler', 'width' => 160, 'fixed' => 0],
                    ['id' => 'inquiry_deadline', 'width' => 200, 'fixed' => 0],
                    ['id' => 'remark', 'width' => 200, 'fixed' => 0],
                    ['id' => 'finish_time', 'width' => 200, 'fixed' => 0],
                    ['id' => 'company_id', 'width' => 200, 'fixed' => 0],
                    ['id' => 'lead_id', 'width' => 200, 'fixed' => 0],
                    ['id' => 'customer_id', 'width' => 200, 'fixed' => 0],
                    ['id' => 'customer_name', 'width' => 200, 'fixed' => 0],
                    ['id' => 'country', 'width' => 120, 'fixed' => 0],
                    ['id' => 'currency', 'width' => 120, 'fixed' => 0],
                    ['id' => 'create_user', 'width' => 160, 'fixed' => 0],
                    ['id' => 'create_time', 'width' => 200, 'fixed' => 0],
                    ['id' => 'update_user', 'width' => 160, 'fixed' => 0],
                    ['id' => 'update_time', 'width' => 200, 'fixed' => 0],
                ]
            ],
            UserSetting::INQUIRY_FEEDBACK_LIST_FIELD => [
                'base' => [],
                'field' => [],
                'type' => \Constants::TYPE_INQUIRY_FEEDBACK,
                'default' => [
                    ['id' => 'product_images', 'width' => 160, 'fixed' => 1],
                    ['id' => 'product_name', 'width' => 92, 'fixed' => 0],
                    ['id' => 'product_no', 'width' => 200, 'fixed' => 0],
                    ['id' => 'description', 'width' => 200, 'fixed' => 0],
                    ['id' => 'currency', 'width' => 88, 'fixed' => 0],
                    ['id' => 'tax_included', 'width' => 88, 'fixed' => 0],
                    ['id' => 'purchase_quote_cny', 'width' => 153, 'fixed' => 0],
                    ['id' => 'purchase_quote', 'width' => 110, 'fixed' => 0],
                    ['id' => 'supplier_id', 'width' => 152, 'fixed' => 0],
                    ['id' => 'price_validity_date', 'width' => 171, 'fixed' => 0],
                    ['id' => 'market_information', 'width' => 138, 'fixed' => 0],
                    ['id' => 'create_user', 'width' => 74, 'fixed' => 0],
                    ['id' => 'create_time', 'width' => 171, 'fixed' => 0],
                    ['id' => 'update_time', 'width' => 74, 'fixed' => 0],
                    ['id' => 'update_time', 'width' => 171, 'fixed' => 0],
                    ['id' => 'inquiry_feedback_no', 'width' => 127, 'fixed' => 0],
                ]
            ]

        ];
        $map = $this->filterDefaultField($map);
        return $map[$this->key] ?? null;
    }

    private function filterDefaultField($map)
    {
        $filterFields = [];
        switch ($this->key) {
            case UserSetting::LIGHTHOUSE_PUBLIC_COMPANY_LIST_FIELD:
            case UserSetting::PUBLIC_COMPANY_LIST_FIELD :
                $client = \common\library\account\Client::getClient($this->clientId);
                if (!$client->getExtentAttribute(\common\library\account\Client::EXTERNAL_KEY_CUSTOMER_POOL_SWITCH)) {
                    $filterFields = ['pool_name'];
                }
                break;
            case UserSetting::OPPORTUNITY_LIST_FIELD :
                if (!PrivilegeService::getInstance($this->clientId)->hasFunctional([PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_OPPORTUNITY])) {
                    $filterFields = ['create_type', 'stage_stay_time'];
                }
                break;

            case UserSetting::LIGHTHOUSE_PRIVATE_COMPANY_LIST_FIELD:
            case UserSetting::PRIVATE_COMPANY_LIST_FIELD :
                if (!PrivilegeService::getInstance($this->clientId)->hasFunctional([PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_OPPORTUNITY])) {
                    $filterFields = ['archive_type'];
                }
                break;

            default :
                break;
        }
        foreach ($map[$this->key]['default'] as $key => $fieldInfo) {
            if (in_array($fieldInfo['id'], $filterFields)) {
                unset($map[$this->key]['default'][$key]);
            }
        }
        $map[$this->key]['default'] = array_values($map[$this->key]['default']);

        return $map;
    }

    public function formatResult($value)
    {
        $default = $this->getDefaultResult();
        $result = ($value !== null ? json_decode($value, true) : null) ?? $default['default'];
        
        // 没有设置过的情况下
        if ($value == null && $this->key == UserSetting::PURCHASE_PUSH_DOWN_SPLIT_ORDER_PRODUCT_LIST_FIELD) {
            // 没有设置PURCHASE_PUSH_DOWN_SPLIT_ORDER_PRODUCT_LIST_FIELD，默认可以用PURCHASE_RECORD_SELECT_PRODUCT_LIST_FIELD的值
            $user = \User::getLoginUser();
            if (!empty($user)) {
                $setting = new \common\library\setting\user\UserSetting($user->getClientId(), $user->getUserId(), UserSetting::PURCHASE_RECORD_SELECT_PRODUCT_LIST_FIELD);
                $data = $setting->getValue();

                // 原来代码有一个问题，比如value目前是['product_info', 'purchase_supplier_id', 'purchase_currency', 'purchase_unit_price', 'to_purchase_count', 'purchase_count']
                // 其中'product_info', 'purchase_supplier_id', 'purchase_currency', 'purchase_unit_price', 'to_purchase_count'这些字段在$data里全都存在，就会被unset掉，最后通过array_merge的方式再加回来
                // 这样无法保留在默认值里设置的顺序，所以这里改成unset data的数据，之后用for each的方式追加到最后
//                if (!empty($data)) {
//                    $ids = array_column($data, 'id');
//                    foreach ($result as $index => $item) {
//                        if (in_array(($item['id'] ?? ''), $ids)) {
//                            unset($result[$index]);
//                        }
//                    }
//                    $result = array_values($result);
//                    $result = array_merge($result, $data);
//                }
                if (!empty($data)) {
                    $appendToResult = [];
                    $resultIds = array_column($result, 'id');
                    foreach ($data as $item) {
                        if (!in_array($item['id'], $resultIds)) {
                            $appendToResult[] = $item;
                        }
                    }
                    foreach ($appendToResult as $appendItem) {
                        $result[] = $appendItem;
                    }
                }
            }
        }

        $disableFields = [];

//			base
        if (!empty($default['base'])) {
            foreach ($default['base'] as $functionalId) {

                $referType = PrivilegeConstants::FIELD_PRIVILEGE_FUNCTIONAL_MAP[$functionalId];
                $disableFields = Helper::getFieldIdByScope($this->clientId, $this->userId, $functionalId, $referType, PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_DISABLE);
                $fieldMap = \common\library\customer\Helper::TRANS_FIELD_MAP;
                foreach ($disableFields as $disableField) {
                    if (array_key_exists($disableField, ($fieldMap[$referType] ?? []))) {
                        $disableFields[] = $fieldMap[$referType][$disableField];
                    }
                }
                if ($subReferType = PrivilegeConstants::FIELD_PRIVILEGE_EXTRA_FUNCTIONAL_MAP[$functionalId] ?? false) {
                    $prefixMap = [
                        \Constants::TYPE_CUSTOMER => 'customer.',
                        \Constants::TYPE_LEAD_CUSTOMER => 'customer.',
                    ];
                    $prefix = $prefixMap[$subReferType] ?? '';
                    $subDisableFields = Helper::getFieldIdByScope($this->clientId, $this->userId, $functionalId, $subReferType, PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_DISABLE);
                    foreach ($subDisableFields as $subDisableField) {
                        if (array_key_exists($subDisableField, ($fieldMap[$subReferType] ?? []))) {
                            $disableFields[] = $fieldMap[$subReferType][$subDisableField];
                        } else {
                            $disableFields[] = $prefix . $subDisableField;
                        }
                    }
                }

            }
        }

//			field
        if (!empty($default['field'])) {
            $privilegeService = PrivilegeService::getInstance($this->clientId, $this->userId);

            foreach ($default['field'] as $fieldName => $privileges) {

                foreach ($privileges as $privilege) {

                    if (!$privilegeService->checkFunctional($privilege)) {

                        $disableFields[] = $fieldName;
                        break;
                    }
                }
            }
        }

        if (count($disableFields)) {
            foreach ($result as $k => $item) {
                if (in_array($item['id'], $disableFields)) {
                    unset($result[$k]);
                }
            }
        }

        $defaultResult = array_column($default['default'] ?? [], 'fixed', 'id');

        //过滤已删除字段
        $filterFields = [];

        $fieldIds = array_filter(array_column($result, 'id'), 'is_numeric');

        if (!empty($fieldIds)) {
            if($default['type']==\Constants::TYPE_PRODUCT_INVENTORY_WARNING) {
                $customFieldWarningList = new FieldList($this->clientId);
                $customFieldWarningList->setFields(['id']);
                $customFieldWarningList->setId($fieldIds);
                $customFieldWarningList->setType($default['type']);
                $customFieldWarningList->setEnableFlag(0);
                $warningList = $customFieldWarningList->find()??[];
                $customFieldProductList = new FieldList($this->clientId);
                $customFieldProductList->setFields(['id']);
                $customFieldProductList->setId($fieldIds);
                $customFieldProductList->setType(\Constants::TYPE_PRODUCT);
                $customFieldProductList->setEnableFlag(0);
                $productList = $customFieldProductList->find();
                $filterWarningFields = array_column($warningList, 'id');
                $filterProductFields = array_column($productList, 'id');
                $filterFields = array_intersect($filterProductFields,$filterWarningFields);
            }else {
                $customFieldList = new FieldList($this->clientId);
                $customFieldList->setFields(['id']);
                $customFieldList->setId($fieldIds);
                $customFieldList->setType($default['type']);
                $customFieldList->setEnableFlag(0);
                $list = $customFieldList->find();
                if (!empty($list)) {
                    $filterFields = array_column($list, 'id');
                }
            }
        }

        // 列表字段进行字段权限处理
        if (in_array($default['type'], [\Constants::TYPE_CASH_COLLECTION])) {
            $fieldPrivileges = \common\library\privilege_v3\privilege_field\Helper::getObjectFieldPrivileges($this->clientId, $this->userId, current($default['base']), PrivilegeFieldV2::SCENE_OF_VIEW, $default['type']);
            $filterFields = array_merge($filterFields, $fieldPrivileges['disable']);
            if (!is_null($value)) {
                $fieldConfigMap = array_column($default['default'],null, 'id');
                foreach ($result as $key => $item) {
                    if (isset($fieldConfigMap[$item['id']]['field'])) {
                        $result[$key]['field'] = $fieldConfigMap[$item['id']]['field'];
                    }
                }
            }

        }

        $client = \common\library\account\Client::getClient($this->clientId);
        $assessFlag = intval($client->getExtentAttributes()[\common\library\account\Client::EXTERNAL_KEY_ASSESS_DIMENSION_SWITCH] ?? 0);

        foreach ($result as $key => $item) {
            if (!isset($item['fixed']) && !in_array($this->key, [UserSetting::PRIVATE_COMPANY_LIST_FIELD_WIDTH, UserSetting::PUBLIC_COMPANY_LIST_FIELD_WIDTH])) {
                $result[$key]['fixed'] = $defaultResult[$item['id']] ?? 0;
            }

            if (isset($item['field']) && in_array($item['field'], $filterFields)) {
                unset($result[$key]);
                continue;
            }

            //过滤已删除字段
            if (in_array($item['id'], $filterFields)) {
                unset($result[$key]);
            } else {

                //新版来源
                $result[$key]['id'] = UserSetting::REPLACE_KEY_MAP[$this->originKey][$item['id']] ?? $item['id'];

                $assessFlag && ($item['id'] == 'score') && $result[$key]['id'] = 'assess';
                !$assessFlag && ($item['id'] == 'assess') && $result[$key]['id'] = 'score';

            }

        }

        return array_values($result);
    }


    public function formatValue($value, $oldValue)
    {

        $rule = [
            'value' => 'array|required',
            'value.*.id' => 'required|string',
            'value.*.width' => 'required|numeric|gt:0',
            'value.*.fixed' => 'required|numeric|gte:0',
        ];

        if ((UserSetting::ORIGIN_KEY_MAP[$this->key] ?? $this->key) == UserSetting::PRIVATE_COMPANY_LIST_FIELD_WIDTH || (UserSetting::ORIGIN_KEY_MAP[$this->key] ?? $this->key) == UserSetting::COMPANY_DUPLICATION_LIST_FIELD_WIDTH || (UserSetting::ORIGIN_KEY_MAP[$this->key] ?? $this->key) == UserSetting::PUBLIC_COMPANY_LIST_FIELD_WIDTH) {

            unset($rule['value.*.fixed']);
        }


        $this->validate(
            ['value' => is_array($value) ? $value : json_decode($value, true)],
            $rule
        );

        if (is_array($value)) {
            $value = json_encode($value);
        }

        return $value;
    }

}
