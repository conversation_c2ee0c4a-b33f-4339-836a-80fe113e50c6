<?php


namespace common\library\setting\item;


use common\library\approval_flow\ApprovalFlowConfigList;
use common\library\cache\ClassCacheRepository;
use common\library\checker\ItemSettingChecker;
use common\library\custom_field\CustomFieldService;
use common\library\customer\rule_config\RuleConfigConstants;
use common\library\performance_v2\rule\PerformanceV2RuleList;
use common\library\setting\item\event\ItemOperated;
use common\library\setting\library\aiSwarm\AiSwarmApi;
use common\library\setting\library\attributes\AttributesApi;
use common\library\setting\library\common\CommonApi;
use common\library\setting\library\config\ConfigApi;
use common\library\setting\library\flow\FlowApi;
use common\library\setting\library\group\GroupApi;
use common\library\setting\library\invalidReason\InvalidReasonApi;
use common\library\setting\library\MailAutoCleanUpRule\MailAutoCleanUpRuleApi;
use common\library\setting\library\publicReason\PublicReasonApi;
use common\library\setting\library\publicRule\PublicRuleApi;
use common\library\setting\library\link\LinkApi;
use common\library\setting\library\origin\OriginApi;
use common\library\setting\library\pool\PoolApi;
use common\library\setting\library\reason\ReasonApi;
use common\library\setting\library\assessDimension\AssessDimensionApi;
use common\library\setting\library\stage\StageApi;
use common\library\setting\library\status\StatusApi;
use common\library\setting\library\storehouseReason\StorehouseReasonApi;
use common\library\setting\library\swarm\Swarm;
use common\library\setting\library\swarm\SwarmApi;
use common\library\setting\library\publicSwarm\PublicSwarmApi;
use common\library\setting\library\tag\TagApi;
use common\library\swarm\SwarmService;
use common\library\translate_v2\TranslateConstants;
use common\library\translate_v2\TranslateService;
use common\library\validation\Validator;
use common\library\workflow\filter\WorkflowCriteriaBuilder;
use common\library\workflow\WorkflowConstant;
use common\library\workflow\WorkflowRuleList;
use LogUtil;
use Throwable;
use xiaoman\orm\common\SingleObject;
use xiaoman\orm\database\data\In;
use xiaoman\orm\exception\OrmException;
use xiaoman\orm\exception\QueryException;
use common\library\history\setting\ItemSettingHistoryCompare;


/**
 * @method static AttributesApi attributes($clientId, $module)
 * @method static GroupApi group($clientId, $module)
 * @method static StatusApi status($clientId, $module)
 * @method static OriginApi origin($clientId, $module = \Constants::TYPE_COMPANY)
 * @method static TagApi tag($clientId, $module, $userId)
 * @method static FlowApi flow($clientId, $module = \Constants::TYPE_OPPORTUNITY)
 * @method static StageApi stage($clientId, $module = \Constants::TYPE_OPPORTUNITY)
 * @method static ReasonApi reason($clientId, $module = \Constants::TYPE_OPPORTUNITY)
 * @method static PoolApi pool($clientId, $module = \Constants::TYPE_COMPANY)
 * @method static ConfigApi config($clientId, $module)
 * @method static StorehouseReasonApi storeHouseReason($clientId, $module = \Constants::TYPE_PURCHASE_INBOUND_INVOICE)
 * @method static SwarmApi swarm($clientId, $userId = 0, $module = \Constants::TYPE_COMPANY)
 * @method static PublicSwarmApi publicSwarm($clientId, $userId = 0, $module = \Constants::TYPE_COMPANY)
 * @method static PublicRuleApi publicRule($clientId)
 * @method static LinkApi link($clientId, $module = \Constants::TYPE_OPPORTUNITY)
 * @method static CommonApi growthLevel()
 * @method static MailAutoCleanUpRuleApi MailAutoCleanUpRule($clientId, $userId)
 * @method static PublicReasonApi publicReason($clientId)
 * @method static AssessDimensionApi assessDimension($clientId, $module = \Constants::TYPE_COMPANY)
 * @method static InvalidReasonApi invalidReason($clientId, $module = \Constants::TYPE_LEAD)
 * @method static AiSwarmApi aiSwarm($clientId, $userId = 0, $module = \Constants::TYPE_COMPANY)
 */
class Api {

    /***
     * @var ItemSettingMetadata
     */
    protected $metadata;

    protected $module;
    protected $itemType;
    protected $clientId;

    /**
     * @var object 指定操作人
     */
    protected $opUser;

    /**
     * @var external\API
     */
    protected $externalAPI;

    /**
     * @var ApiParams
     */
    protected $apiParams;
    protected $formatParams;
    protected $ownerUser;

    protected $apiCache;

    protected $skipEvent = false;
    protected $globalSortFlag = false;

    public function __construct($clientId, $module, $itemType, $userId = 0)
    {
        $this->clientId = $clientId;
        $this->module = $module;
        $this->itemType = $itemType;
        $this->metadata = ItemSettingConstant::$metadataTypeMap[$itemType] ?? ItemSettingMetadata::class;
        $this->refreshParams();
        if ($userId) {
            $this->setOwnerUser($userId);
        }
    }

    public static function instance($clientId, $module, $itemType, $userId = 0, $extra = [])
    {
        if ($apiClass = ItemSettingConstant::$apiMap[$itemType] ?? self::class) {
            $class = new \ReflectionClass($apiClass);
            $constructParams = [];
            foreach ($class->getConstructor()->getParameters() as $parameter) {
                if (isset(${$parameter->getName()})) {
                    $constructParams[$parameter->getName()] = ${$parameter->getName()};
                } elseif (isset($extra[$parameter->getName()])) {
                    $constructParams[$parameter->getName()] = $extra[$parameter->getName()];
                } else {
                    throw new \RuntimeException("实例化API缺少{$parameter->getName()}值");
                }
            }
            return new $apiClass(...array_values($constructParams));
        }
        return new self($clientId, $module, $itemType, $userId);
    }

    public static function __callStatic(string $name, array $arguments)
    {
        $commonConfig = [
            'growthLevel' => ItemSettingConstant::ITEM_TYPE_GROWTH_LEVEL,
        ];

        if (isset($commonConfig[$name])) {
            return new CommonApi($commonConfig[$name], ...$arguments);
        }

        $config = [
            'attributes' => AttributesApi::class,
            'group' => GroupApi::class,
            'status' => StatusApi::class,
            'origin' => OriginApi::class,
            'tag' => TagApi::class,
            'flow' => FlowApi::class,
            'stage' => StageApi::class,
            'reason' => ReasonApi::class,
            'pool' => PoolApi::class,
            'config' => ConfigApi::class,
            'storeHouseReason' => StorehouseReasonApi::class,
            'swarm' => SwarmApi::class,
            'publicSwarm' => PublicSwarmApi::class,
            'publicRule' => PublicRuleApi::class,
            'link' => LinkApi::class,
            'MailAutoCleanUpRule' => MailAutoCleanUpRuleApi::class,
            'publicReason' => PublicReasonApi::class,
            'assessDimension' => AssessDimensionApi::class,
            'invalidReason' => InvalidReasonApi::class,
            'aiSwarm' => AiSwarmApi::class,
        ];
        if (isset($config[$name])) {
            return new $config[$name](...$arguments);
        }

        throw new \RuntimeException("配置项{$name}类型不存在");
    }

    public function getMetadata()
    {
        return $this->metadata;
    }

    public function setSkipEvent($skip = true)
    {
        $this->skipEvent = $skip;
        return $this;
    }
    public function setGlobalSortFlag($globalSortFlag)
    {
        $this->globalSortFlag = $globalSortFlag;
        return $this;
    }

    protected function refreshParams()
    {
        $this->apiParams = new ApiParams($this->clientId, $this->module, $this->itemType, $this->metadata);
    }

    public function getParams()
    {
        return $this->apiParams;
    }

    protected function getFormatParams()
    {
        return $this->formatParams ?? $this->apiParams;
    }

    public function setOpUser($userId)
    {
        $this->opUser = is_a($userId, \User::class) ? $userId : \User::getUserObject($userId);
        return $this;
    }

    public function setApiFields($apiFields)
    {
        $this->apiParams->setApiFields($apiFields);
        return $this;
    }

    public function setOwnerUser($userIds, $withPublic = false)
    {
        $this->ownerUser = $userIds;
        $this->apiParams->setOwnerUser($userIds, $withPublic);
        return $this;
    }

    public function getExternalAPI()
    {
        if (is_null($this->externalAPI)) {
            $this->externalAPI = new external\API($this->clientId, $this->module, $this->itemType);
        }

        return $this->externalAPI;
    }

    public function rootList()
    {
        return $this->baseList(0, true, false);
    }
    
    public function getIdList($parentId = 0, $includeRoot = true, $recursive = true, $includeExtra = false) {
       
        $this->getParams()->setId($parentId);
        $this->getParams()->setRecursive($recursive, $includeRoot);
        $filter = $this->getFilter();
        $filter->select(['item_id']);
        $data = $filter->rawData();
    
        if ($includeExtra) {
        
            $data = array_merge($data, array_filter($this->getExtraDataList($recursive), function ($item) use ($includeRoot, $parentId) {
    
                return ($includeRoot ? (in_array($item['item_id'], (array)$parentId) || in_array($item['parent_id'], (array)$parentId)) : (in_array($item['item_id'], (array)$parentId)));
            }));
        }
        
        return array_column($data, 'item_id');
    }

    public function checkHasChild($parentId = 0)
    {
        if ($parentId > 0) {
            return !empty(array_diff($this->getIdList($parentId), [$parentId]));
        } else {
            return false;
        }
    }

    public function exist($itemId)
    {
        $filter = $this->getFilter();
        $filter->item_id = $itemId;

        return (bool)$filter->count();
    }

    public function nameExist($itemName)
    {
        $filter = $this->getFilter();
        $filter->item_name = $itemName;
        return $filter->count() ? true : false;
    }

    public function getValueMap($valueName, $ids, $includeExtra = false, $includeRoot = true, $recursive = false)
    {
        $map = array_column($this->getExtraDataList($recursive), $valueName, 'item_id');
        $queryExtra = false;
        if (!empty($ids) && array_intersect(array_keys($map), (array)$ids)) {
            $queryExtra = true;
            $map = array_intersect_key($map, array_fill_keys((array)$ids, ''));
        }
        if ($ids === 0 || !empty($ids)) {
    
            if ($valueName == 'item_name') {
    
                $data = $this->baseList($ids, $includeRoot, $ids === 0 ? true : $recursive, $includeExtra, [], null, false, false);
        
                $filedList = array_fill_keys(['item_id', $valueName], '');
        
                array_walk($data, function (&$item) use ($filedList){
            
                    $item = array_intersect_key($item, $filedList);
                });
                
            }else{
    
                $batchObj = $this->baseList($ids, $includeRoot, $ids === 0 ? true : $recursive, $includeExtra, [], null, true);
                $data = $batchObj->getListAttributes(['item_id', $valueName]);
            }
            
            if ($includeExtra || $queryExtra) {
                $map = array_replace($map, array_column($data, $valueName, 'item_id'));
            } else {
                $map = array_column($data, $valueName, 'item_id');
            }
        } else {
            return $includeExtra ? $map : [];
        }

        return $map;
    }

    public function getValueListMap($valueNameList, $ids, $includeExtra = false, $includeRoot = true, $recursive = false)
    {
        $map = array_column($this->getExtraDataList($recursive), null, 'item_id');
        $queryExtra = false;
        if (!empty($ids) && array_intersect(array_keys($map), (array)$ids)) {
            $queryExtra = true;
            $map = array_intersect_key($map, array_fill_keys((array)$ids, ''));
        }
        if ($ids === 0 || !empty($ids)) {
    
            if (in_array('item_name', $valueNameList)) {
    
                $data = $this->baseList($ids, $includeRoot, $ids === 0 ? true : $recursive, $includeExtra, [], null, false, false);
    
                $filedList = array_fill_keys(array_merge(['item_id'], $valueNameList), '');
                
                array_walk($data, function (&$item) use ($filedList){
    
                    $item = array_intersect_key($item, $filedList);
                });
            }else{
    
                $batchObj = $this->baseList($ids, $includeRoot, $ids === 0 ? true : $recursive, $includeExtra, [], null, true);
              
                $data = $batchObj->getListAttributes(array_merge(['item_id'], $valueNameList));
            }
            
            if ($includeExtra || $queryExtra) {
                $map = array_replace($map, array_column($data, null, 'item_id'));
            } else {
                $map = array_column($data, null, 'item_id');
            }
        } else {
            return $includeExtra ? $map : [];
        }

        return $map;
    }

    public function getNameMap($ids, $includeExtra = false, $includeRoot = true, $recursive = false)
    {
        return $this->getValueMap('item_name', ($ids?:0), $includeExtra, $includeRoot, $recursive);
    }

    public function getNameById($ids, $includeExtra = false, $includeRoot = true, $recursive = false)
    {
        return $this->getNameMap($ids, $includeExtra, $includeRoot, $recursive)['item_name'] ?? '';
    }

    public function getOneNameById($id)
    {
        return $this->getExtraDataMap()[$id] ?? ($this->getNameMap($id)[$id] ?? '');
    }

    protected function translate(&$elem)
    {

//        todo
        if (!empty($this->clientId) && (new TranslateService($this->clientId, $this->module, $this->itemType))->check(TranslateConstants::CHECK_TYPE_ITEM_SETTING)) {
    
            return;
        }
        
        $map = $this->metadata::getTranslateMap($this->module);
        if ($map) {
            foreach ($map as $field => $setting) {
                if (!isset($elem[$field])) {
                    continue;
                }
                if (is_string($setting)) {
                    $elem[$field] = \Yii::t($setting, $elem[$field]);
                }
            }
        }
    }

    public function listById($parentId, $includeExtra = false, $externalKeys = [], $userId = null)
    {
        $this->getParams()->setRecursive(false, true);
        return $this->baseList($parentId, $this->apiParams->includeRoot ?? true, $this->apiParams->recursive ?? false, $includeExtra, $externalKeys, $userId);
    }

    public function list($parentId, $includeExtra = false, $externalKeys = [], $userId = null)
    {
        return $this->baseList($parentId, $this->apiParams->includeRoot ?? true, $this->apiParams->recursive ?? false, $includeExtra, $externalKeys, $userId);
    }

    public function firstOne($externalKeys = [], $userId = null)
    {
        $this->getParams()->setExternalKeys($externalKeys, $userId);
        $filter = $this->getFilter();
        $filter->limit(1);
        $obj = $filter->find();

        $this->getFormatParams()->format($obj);

        $obj->getFormatter()->listSetting($this->module);

        return current($obj->getListAttributes()) ?: null;
    }

    public function find($parentId, $externalKeys = [], $userId = null)
    {
        $obj = $this->findOne($parentId, $externalKeys, $userId);

        return $obj->isNew() ? [] : $obj->getAttributes();
    }


    public function findOne($itemId, $externalKeys = [], $userId = null)
    {
        $this->apiParams->setExternalKeys($externalKeys, $userId);
        $singleObj = $this->metadata::singeObject();
        /**
         * @var $item ItemSetting
         */
        $item = new $singleObj($this->clientId, $itemId, $this->apiParams->withDeleted);
        $this->getFormatParams()->format($item);

        $item->getFormatter()->detailInfoSetting($this->module);

        return $item;
    }

    public function listAll($includeExtra = true, $externalKeys = [], $userId = null, $needApiMapping = true)
    {
        return $this->baseList(0, true, true, $includeExtra, $externalKeys, $userId, false, $needApiMapping);
    }

    public function getIdMapByName(array $name, $layer = null)
    {
        $filter = $this->getFilter();
        $filter->select(['item_id', 'item_name']);
        $filter->item_name = new In($name);
        if ($layer) {
            $filter->layer = $layer;
        } else {
            $filter->order('layer', 'desc');
        }

        $map = array_column($filter->rawData(), 'item_id', 'item_name');

        return $map;
    }

    public function getIdByName($name, $withExtra = false)
    {
        return $this->getIdMapByName([$name])[$name] ?? ($withExtra ? $this->getExtraIdByName($name) : 0);
    }

    public function getExtraIdByName($name)
    {
        return array_flip($this->getMetadataExtraDataMap())[$name] ?? 0;
    }

    public function findByName($name, $parentId = 0, $includeExtra = false, $onlyCurrent = false)
    {
        $filter = $this->getFilter();
        $filter->item_name = $name;
        if ($parentId || $onlyCurrent) {
            $filter->parent_id = $parentId;
        } else {
            $filter->order('layer', 'desc');
        }
        $filter->limit(1);
        $batch = $filter->find();
        $batch->getFormatter()->baseListSetting($this->module);
        if ($includeExtra) {
            $batch->getFormatter()->appendExtraData($this->findExtraRawData(['item_name' => $name]));
        }

        return current($batch->getAttributes()) ?: [];
    }

    protected function findExtraRawData($condition)
    {
        return array_filter($this->getExtraDataList(), function ($item) use ($condition) {
            foreach ($condition as $k => $value) {
                if (!isset($item[$k]) || $value != $item[$k]) {
                    return false;
                }
            }
            return true;
        });
    }

    public function getByNames(array $names, $parentId = null, $limit = null)
    {
        $filter = $this->getFilter();
        $filter->item_name = $names;
        if (!is_null($parentId)) {
            $filter->parent_id = $parentId;
        }
        !is_null($limit) && $filter->limit($limit);
        $batch = $filter->find();
        $batch->getFormatter()->baseListSetting($this->module);

        if ($limit === 1) {
            return current($batch->getAttributes()) ?: [];
        }

        return $batch->getAttributes();
    }

    // 根据名称和层级获取item信息
    public function getByLayerNames(array $names){
        if(empty($names)){
            return [];
        }
        $filter = $this->getFilter();
        $where = [];
        $names = array_values($names);
        foreach($names as $k => $name){
            $name = addslashes($name);
            $where[] = " (layer = " . ($k + 1) . " and item_name = '{$name}') ";
        }
        $where = '('.implode(' or ', $where).')';
        $filter->rawWhere(" and {$where}");
        $filter->order('layer');
        $batch = $filter->find();
        $batch->getFormatter()->baseListSetting($this->module);
        return $batch->getAttributes();
    }

    public function listRecursive($parentId, $includeExtra = false, $externalKeys = [], $userId = null)
    {
        return $this->baseList($parentId, true, true, $includeExtra, $externalKeys, $userId);
    }

    /**
     * @param $key
     * @param $value
     * @param int $userId
     *
     * 使用扩展表值过滤
     */
    public function setFilterByExternal($key, $value, $userId = 0)
    {
        $this->apiParams->setExternalFilterConfig($key, $value, $userId);
        return $this;
    }

    public function setFilteredByUserDisplay($userId)
    {
        return $this->setFilterByExternal($this->metadata::getUserDisplayFlagName(), 1, $userId);
    }

    protected function baseList($parentId, $includeRoot = true, $recursive = false, $includeExtra = false, $externalKeys = [], $externalSettingUser = null, $returnObject = false, $needApiMapping = true)
    {
        $this->apiParams->setId($parentId);
        $this->apiParams->setRecursive($recursive, $includeRoot);
        $this->apiParams->setExternalKeys($externalKeys, $externalSettingUser);
        $filter = $this->getFilter();
        $batchObj = $filter->find();

        if (is_array($batchObj)) {
            $dataList = $batchObj;
            $batchObj = $this->getBatchObj();
            $batchObj->initFromData($dataList);
        }

        if ($includeExtra) {
            $extraData = $this->getExtraDataList($recursive);
            if (!empty($this->formatParams->layer)) {
                $extraData = array_filter($extraData, fn ($it) => !isset($it['layer']) || in_array($it['layer'], (array)$this->formatParams->layer));
            }
            $batchObj->getFormatter()->appendExtraData($extraData);
        }
    
        $batchObj->getFormatter()->setNeedApiMapping($needApiMapping);
        
        $this->getFormatParams()->format($batchObj);

        $batchObj->getFormatter()->listSetting($this->module);

        return $returnObject ? $batchObj : $batchObj->getListAttributes();
    }

    public function listExtraData($externalKeys = [], $userId = null)
    {
        $this->apiParams->setExternalKeys($externalKeys, $userId);
        $obj = $this->getBatchObj();

        $obj->initFromData($this->getExtraDataList(false));
        $obj->getFormatter()->listSetting($this->module);

        $this->getFormatParams()->format($obj);

        return $obj->getListAttributes();
    }

    public function findExtraData($id, $externalKeys = [], $userId = null)
    {
        $this->apiParams->setExternalKeys($externalKeys, $userId);
        $obj = $this->getBatchObj();

        $extraList = array_filter($this->getExtraDataList(false), function ($item) use ($id) {
            return $item['item_id'] == $id;
        });
        $obj->initFromData($extraList);
        $obj->getFormatter()->listSetting($this->module);

        $this->getFormatParams()->format($obj);

        return current($obj->getListAttributes()) ?: null;
    }

    /**
     * @return ItemSettingFilter
     * @throws \xiaoman\orm\exception\OrmException
     */
    protected function getFilter()
    {
        $filterClass = $this->metadata::filter();
        /**
         * @var $filter ItemSettingFilter
         */
        $filter = new $filterClass($this->clientId);
        $this->apiParams->build($filter);
        $this->formatParams = clone $this->apiParams;
        $this->refreshParams();

        return $filter;
    }

    protected function getRawFilter()
    {
        $filterClass = $this->metadata::filter();
        /**
         * @var $filter ItemSettingFilter
         */
        $filter = new $filterClass($this->clientId);
        $filter->client_id = $this->clientId;
        $filter->module = $this->module;
        $filter->item_type = $this->itemType;

        return $filter;
    }


    public function tree($parentId, $includeRoot = true, $includeExtra = false, $externalKeys = [], $userId = null, $returnObject = false)
    {
        $batchObj = self::baseList($parentId, $includeRoot, true, $includeExtra, $externalKeys, $userId, true);
        $batchObj->getFormatter()->treeListSetting($includeRoot, $this->module);

        return $returnObject ? $batchObj : $batchObj->getListAttributes();
    }

    public function settingTree($parentId, $includeRoot = true, $includeExtra = false, $externalKeys = [], $userId = null, $returnObject = false)
    {
        $batchObj = self::baseList($parentId, $includeRoot, true, $includeExtra, $externalKeys, $userId, true);
        $batchObj->getFormatter()->treeSettingListSetting($includeRoot, $this->module);

        return $returnObject ? $batchObj : $batchObj->getListAttributes();
    }


    public function oneTree($itemId, $externalKeys = [], $userId = null)
    {
        $treeList = $this->tree($itemId, true, false, $externalKeys, $userId);

        return current($treeList) ?: [];
    }

    public function count($parentId = 0, $includeRoot = true, $recursive = false)
    {
        $this->apiParams->setId($parentId);
        $this->apiParams->setRecursive($recursive, $includeRoot);
        $filter = $this->getFilter();

        return $filter->count();
    }

    public function listWithParent($itemIds, $index = true)
    {
        $includeExtra = false;
        $itemIds = (array)$itemIds;
        if (array_intersect($itemIds, array_column($this->getExtraDataList(false), 'item_id'))) {
            $includeExtra = true;
        }
//        $itemIds = array_filter($itemIds);

        $allItemIds = $itemIds;
        if (!empty($allItemIds)) {
            $filter = $this->getFilter();
            $filter->item_id = new In($itemIds);
            $filter->select(['item_id', 'prefix']);
            $itemList = $filter->rawData();
            $allItemIds = array_reduce($itemList, function ($carry, $item) {
                $prefix = trim(substr($item['prefix'], 2), '-');
                $parents = empty($prefix) ? [] : explode('-', $prefix);
                return array_merge($parents, $carry);
            }, $itemIds);
        }

        $batchObj = $this->baseList($allItemIds ?: 0, true, empty($allItemIds) ? true : null, $includeExtra, [], null, true);
        $batchObj->getFormatter()->listWithParentSetting($this->module);

        $list = $batchObj->getListAttributes();

        if (!empty($itemIds)) {
            $list = array_diff_key($list, array_fill_keys(array_diff($allItemIds, $itemIds), 1));
        }

        return $index ? $list : array_values($list);
    }

    public function getParentIdsMap($itemIds)
    {
        $includeExtra = false;
        if (array_intersect($itemIds, array_column($this->getExtraDataList(false), 'item_id'))) {
            $includeExtra = true;
        }
        $itemIds = array_filter($itemIds);

        $batchObj = $this->baseList($itemIds ?: 0, true, empty($itemIds) ? true : null, $includeExtra, [], null, true);
        $listData = $batchObj->getListAttributes(['item_id', 'prefix']);

        $resultMap = [];
        foreach ($listData as $item) {
            $groupId = $item['item_id'];
            $groupPrefix = $item['prefix'];
            $resultMap[$groupId] = array_unique(array_map('intval', explode('-', ($groupPrefix . $groupId))));;
        }

        return $resultMap;
    }


    public function createBatchByName(array $nameList, $owner = 0, $parentId = 0, $description = '', $attributes = '', $fieldType = 0, $systemFlag = 0)
    {
        $dataList = [];
        foreach ($nameList as $name) {
            $dataList[] = [
                'item_name' => $name,
                'description' => $description,
                'attributes' => $attributes,
            ];
        }

        return $this->createBatch($dataList, $owner, $parentId, $fieldType, $systemFlag);
    }

    //client维度
    public function createInit($onlyInitList = [], $skipInitList = [])
    {
        if ($this->count()) {
            return 0;
        }

        $settingList = $this->metadata::getSystemInitList($this->module);
        $settingList = self::filterSystemInitList($settingList, $onlyInitList, $skipInitList);
        $dataList = [];
        foreach ($settingList as $k => $v) {
            if (is_array($v)) {
                $v['item_name'] = $k;
                $dataList[] = $v;
            } else {
                $dataList[] = [
                    'item_name' => $v,
                ];
            }
        }

        return $this->createBatch($dataList);
    }
    //user维度
    public function createInitForUser($onlyInitList = [], $skipInitList = [] , $userId = 0)
    {
        if ($this->count() || empty($userId)) {
            return 0;
        }

        $settingList = $this->metadata::getUserInitList($this->module);
        $settingList = self::filterSystemInitList($settingList, $onlyInitList, $skipInitList);
        $dataList = [];
        foreach ($settingList as $k => $v) {
            if (is_array($v)) {
                $v['item_name'] = $k;
                $v['user_id'] = $userId;
                $dataList[] = $v;
            } else {
                $dataList[] = [
                    'item_name' => $v,
                    'user_id' => $userId,
                ];
            }
        }

        return $this->createBatch($dataList);
    }

    /**
     * 过滤初始化列表
     * @param $settingList
     * @param $whiteList
     * @param $blackList
     * @return array
     */
    public static function filterSystemInitList($settingList, $whiteList = [], $blackList = [])
    {
        if (empty($settingList)) {
            return [];
        }

        if (empty($whiteList) && empty($blackList)) {
            return $settingList;
        }

        $ret = [];
        foreach ($settingList as $setting) {
            //优先以白名单为准
            if (!empty($whiteList) && !in_array($setting['name'] ?? '', $whiteList)) {
                continue;
            }
            //过滤黑名单
            if (empty($whiteList) && !empty($blackList) && in_array($setting['name'] ?? '', $blackList)) {
                continue;
            }

            $ret[] = $setting;
        }

        return $ret;
    }

    /**
     * @return BatchItemSetting
     */
    protected function getBatchObj()
    {
        $batchObjClass = $this->metadata::batchObject();

        /**
         * @var $batchItem BatchItemSetting
         */
        $batchItem = new $batchObjClass($this->clientId);
        if ($this->opUser) {
            $batchItem->setDomainHandler($this->opUser);
        }

        return $batchItem;
    }

    public function createBatch(array $itemDataList, $owner = 0, $parentId = 0, $fieldType = 0, $systemFlag = 0)
    {
        if (empty($itemDataList)) {
            return [];
        }
        $batchItem = $this->getBatchObj();

        $dataList = [];
        foreach ($itemDataList as $itemData) {
            $itemData = $this->unpack($itemData);
            $dataList[] = array_replace([
                'item_id' => null,
                'module' => $this->module,
                'item_type' => $this->itemType,
                'field_type' => $fieldType,
                'parent_id' => $parentId,
                'system_flag' => $systemFlag,
                'user_id' => $owner,
                'order_rank' => null,
                'description' => '',
                'attributes' => '',
                'enable_flag' => 1,
                'disable_flag' => 0,
                'relate_id' => 0,
                'create_time' => date("Y-m-d H:i:s"),
                'update_time' => date("Y-m-d H:i:s"),
            ], $itemData);
        }

        $this->validate($dataList);
        $batchItem->initFromData($dataList);
        $itemIds = $batchItem->getOperator()->create($this->module, $this->itemType, $parentId);
        $this->fire((new ItemOperated($this->clientId, $this->module, $this->itemType))->created($itemIds));

        return $itemIds ?: [];
    }

    public function createWithTree($treeData, $ownerId = 0, $parentId = 0, $fieldType = 0)
    {
        if (!isset($treeData['item_name'])) {
            foreach ($treeData as $treeDatum) {
                $this->createWithTree($treeDatum, $ownerId, $parentId, $fieldType);
            }
        } else {
            $parentId = current($this->createBatchByName([$treeData['item_name']], $ownerId, $parentId ?: ($treeData['parent_id'] ?? 0), $fieldType));
            if ($nodeData = $treeData['nodes'] ?? []) {
                foreach ($nodeData as $nodeDatum) {
                    $this->createWithTree($nodeDatum, $ownerId, $parentId, $fieldType);
                }
            }
        }
    }

    protected function unpack($inputData)
    {
        if ($apiMapping = $this->metadata::getApiMapping($this->module)) {
            foreach ($inputData as $k => $v) {
                if (isset($apiMapping[$k]) && is_string($apiMapping[$k]) && $apiMapping[$k] != $k) {
                    $inputData[$apiMapping[$k]] = $v;
                    unset($inputData[$k]);
                }
            }
        }
        if ($this->metadata::getExtAttributeKeys()) {
            $inputData['attributes'] = [];
            foreach ($this->metadata::getExtAttributeKeys() as $extKey) {
                if (isset($inputData[$extKey])) {
                    $inputData['attributes'][$extKey] = $inputData[$extKey];
                    unset($inputData[$extKey]);
                }
            }
        }

        if ($flagMap = $this->metadata::getFlagMap($this->module)) {
            //编辑时无需获取默认值
            $inputData['field_type'] = $inputData['field_type'] ?? (empty($inputData['item_id']) ? $this->metadata::getFlagMapDefaultValue($this->module) : []);
            foreach ($flagMap as $flagPos => $flagKey) {
                if (isset($inputData[$flagKey])) {
                    $inputData['field_type'][$flagPos] = $inputData[$flagKey];
                    unset($inputData[$flagKey]);
                }
            }
        }

        if (isset($inputData['field_type'])) {
            $inputData['field_type'] = $this->metadata::formatFieldType($this->module, $inputData['field_type']);
        }

        if (isset($inputData['item_name'])) {
            $inputData['item_name'] = self::itemNameSpecialCharsFormat($inputData['item_name']);
        }

        return $inputData;
    }

    public static function itemNameSpecialCharsFormat($itemName){
        return str_replace(["\n", "\r", "\t"], ' ', trim($itemName, " \t\n\r\0\x0B\""));
    }

    protected function unpackExternal(&$inputData)
    {
        if ($extKeys = $this->metadata::getTileExternalKeys($this->module)) {
            $extSetValue = array_intersect_key($inputData, array_flip((array)$extKeys));
            $inputData = array_diff_key($inputData, array_flip((array)$extKeys));
            return $extSetValue;
        }
        return [];
    }

    public function getTileExternalKeys()
    {
        return $this->metadata::getTileExternalKeys($this->module);
    }

    public function findOrCreateByName($name)
    {

    }

    final public function create($inputData, $ownerId = 0, $systemFlag = 0, array $nodes = [])
    {
        if ($nodes) {
            $transaction = \ProjectActiveRecord::getDbByClientId($this->clientId)->beginTransaction();
        }

        try {
            $setValues = $this->unpack($inputData);

            if ($this->metadata::userOnly() && !$ownerId) {
                $ownerId = $this->apiParams->ownerUserId;
            }

            $setValues = [
                'item_id' => $setValues['item_id'] ?? null,
                'item_name' => $setValues['item_name'],
                'parent_id' => $setValues['parent_id'] ?? 0,
                'field_type' => $setValues['field_type'] ?? 0,
                'description' => $setValues['description'] ?? '',
                'attributes' => $setValues['attributes'] ?? '',
                'order_rank' => $setValues['order_rank'] ?? '',
                'disable_flag' => $setValues['disable_flag'] ?? (in_array($this->itemType, ItemSettingConstant::DEFAULT_DISABLE_ITEM) ? 1 : 0),
                'relate_id' => $setValues['relate_id'] ?? 0,
                'system_flag' => $setValues['system_flag'] ?? $systemFlag,
                'user_id' => $ownerId,
            ];

            $externalMap = $this->unpackExternal($inputData);
            $this->validate([array_replace($setValues, ['external' => $externalMap])]);

            $singleObj = $this->metadata::singeObject();
            /**
             * @var ItemSetting $item
             */
            $item = new $singleObj($this->clientId);
            $item->module = $this->module;
            $item->item_type = $this->itemType;
            $item->user_id = $ownerId;
            $item->system_flag = $systemFlag;
            if ($this->opUser) {
                $item->setDomainHandler($this->opUser);
            }
            $item = $this->fillData($item, $setValues);

            $newItemIds = $item->getOperator()->create($this->module, $this->itemType, $inputData['parent_id'] ?? 0);
            $newItemId = current($newItemIds) ?: false;

            if (!$newItemId) {
                return false;
            }

            if ($nodes) {
                $item->getFormatter()->displayFields(['item_name', 'system_flag', 'order_rank', 'attributes', 'description', 'field_type', 'relate_id']);
                $this->setNode($item, $nodes, true);
            }

            //set external key
            $this->setItemExternalValueMap($ownerId, $newItemId, $externalMap);

            $module = $this->module;
            $itemType = $this->itemType;


            // 操作历史记录
            try {
                $compareEditSetting = new ItemSettingHistoryCompare($this->clientId);
                $compareEditSetting->setModule(ItemSettingHistoryCompare::SETTING_TYPE_ITEM_SETTING);
                $compareEditSetting->setReferModule($module);
                $compareEditSetting->setReferKey($itemType);
                $compareEditSetting->setType(ItemSettingHistoryCompare::ITEM_SETTING_OPERATE_TYPE_CREATE);
                $itemRawData = $item->getRawData();
                $itemAttributes = json_decode($item->attributes, true) ?? [];
                $newAttributes = array_replace($itemRawData, $itemAttributes, $externalMap);
                $oldAttributes = [];
                $compareEditSetting->setData($newAttributes, $oldAttributes);
                $compareEditSetting->setExtraData(['refer_id' => $newItemId]);
                $compareEditSetting->build($item->getDomainHandler()->getUserId());
            } catch (Throwable $throwable) {
                LogUtil::info("history collect fail - ItemSetting - create");
                LogUtil::info("clientId: {$this->clientId}, userId: {$item->getDomainHandler()->getUserId()}");
                LogUtil::info("referModule: {$module}, referKey: {$itemType}");
                LogUtil::info("referId: {$newItemId}");
                $validNewSet = 0;
                $valueNew = "";
                if (isset($newAttributes)) {
                    $validNewSet = 1;
                    $valueNew = json_encode($newAttributes);
                }
                LogUtil::info("validNewSet: {$validNewSet}, valueNew: `{$valueNew}`");
                LogUtil::info("throwable message:{$throwable->getMessage()}");
            }


            if (isset($transaction)) {
                $transaction->commit();
            }
        } catch (\Throwable $exception) {
            if (isset($transaction)) {
                $transaction->rollback();
            }

            \LogUtil::exception($exception, [
                'inputData' => $inputData,
                'nodes' => $nodes,
                'ownerId' => $ownerId,
            ]);

            throw $exception;
        }

        $this->fire((new ItemOperated($this->clientId, $this->module, $this->itemType))->created($newItemId, $setValues));

        return $newItemId;
    }

    protected function buildNode($nodes, ItemSetting $item)
    {
        $nodeDataList = [];
        if (!empty($nodes)) {
            foreach ($nodes as $node) {
                if (empty($node['item_name']) && $node['item_name'] !== '0') {
                    continue;
                }
                $nodeDataList[] = [
                    'item_name' => $node['item_name'],
                    'item_id' => $node['item_id'] ?? 0,
                ];
            }
        }

        return $nodeDataList;
    }

    final public function setNode(ItemSetting $item, $nodes, $isCreate = false)
    {
        if (count($nodeDataList = $this->buildNode($nodes, $item, $isCreate))) {
            list($createNodeTds, $cleanNodeIds) = $item->getOperator()->setNode($this->module, $this->itemType, $nodeDataList);
            \LogUtil::info("set node for $item->item_id", [
                'node' => $nodeDataList
            ]);
            // external setting
            $filter = $this->getRawFilter();
            $filter->parent_id = $item->item_id;
            $filter->user_id = $item->user_id;
            $filter->enable_flag = 1;
            $filter->select(['item_id', 'item_name']);
            $persistNameMap = array_column($filter->rawData(), 'item_id', 'item_name');
            $externalMapList = [];
            foreach ($nodeDataList as $inputData) {
                $inputData['parent_id'] = $item->item_id;
                $inputData['user_id'] = $item->user_id;
                if (isset($persistNameMap[$inputData['item_name']])) {
                    $externalMapList[$persistNameMap[$inputData['item_name']]] = $this->unpackExternal($inputData);
                }
            }
            $this->getExternalAPI()->setMultiItems($item->user_id, $externalMapList);

            if(!empty($cleanNodeIds)){
                $this->fire((new ItemOperated($this->clientId, $this->module, $this->itemType))->deleted($cleanNodeIds));
            }
        }
    }

    public function editName($itemId, $itemName)
    {
        $this->edit($itemId, ['item_name' => $itemName]);
    }

    public function move($itemId, $parentId)
    {
        $this->edit($itemId, ['parent_id' => $parentId]);
    }

    /**
     * @param $itemId
     * @param $inputData
     * @return array|false|string|null
     */
    final public function edit($itemId, $inputData, $nodes = null, $ownerId = 0)
    {
        if ($nodes) {
            $transaction = \ProjectActiveRecord::getDbByClientId($this->clientId)->beginTransaction();
        }

        try {
            $inputData = array_filter($inputData, function ($datum) {
                return $datum !== null;
            });
            $inputData['item_id'] = $itemId;
            $setValue = $this->unpack($inputData);

            $setValue = array_intersect_key($setValue, array_fill_keys([
                'item_id',
                'item_name',
                'parent_id',
                'field_type',
                'description',
                'attributes',
                'order_rank',
                'relate_id',
                'user_id',
                'relate_id',
                'disable_flag',
                'system_flag',
            ], ''));

            $extSetValue = $this->unpackExternal($inputData);
            $this->validate([array_replace($setValue, ['external' => $extSetValue])]);

            $singleObj = $this->metadata::singeObject();
            /**
             * @var $item ItemSetting
             */
            $item = new $singleObj($this->clientId, $itemId);
            if ($item->isNew()) {
                return false;
            }
            unset($setValue['item_id']); // 排除id

            $diff = [];
            $item = $this->fillData($item, $setValue, $diff);
            $item->getOperator()->edit();

            $diffsExternal = [];
            $oldExternalSetting = [];
            if ($extSetValue) {
                $oldExternalSetting = $this->getExternalKvMap($ownerId ?: $item->user_id, $itemId, array_keys($extSetValue));
                foreach ($extSetValue as $extK => $extV) {
                    if (($oldExternalSetting[$extK] ?? '') != $extV) {
                        $d = [
                            'new' => $extV,
                            'old' => $oldExternalSetting[$extK] ?? '',
                        ];
                        $diff[$extK] = $d;
                        $diffsExternal[$extK] = $d;
                    }
                }
                $this->setItemExternalValueMap($ownerId ?: $item->user_id, $itemId, $extSetValue);
            }

            $module = $this->module;
            $itemType = $this->itemType;


            // 操作历史记录
            try {
                $compareEditSetting = new ItemSettingHistoryCompare($this->clientId);
                $compareEditSetting->setModule(ItemSettingHistoryCompare::SETTING_TYPE_ITEM_SETTING);
                $compareEditSetting->setReferModule($module);
                $compareEditSetting->setReferKey($itemType);
                $compareEditSetting->setType(ItemSettingHistoryCompare::ITEM_SETTING_OPERATE_TYPE_EDIT);

                $itemSettingNew = array_replace($item->getRawData(), (json_decode($item->attributes, true) ?? []));
                $itemSettingOld = ($item->getOldAttributes() ?? []);

                if ($extSetValue) {
                    foreach ($extSetValue as $extK => $extV) {
                        $itemSettingNew[$extK] = $extV;
                        $itemSettingOld[$extK] = $oldExternalSetting[$extK] ?? '';
                    }
                }
                $compareEditSetting->setData($itemSettingNew, $itemSettingOld);
                $compareEditSetting->setExtraData(['refer_id' => $item->item_id]);
                $compareEditSetting->build($item->getDomainHandler()->getUserId());
            } catch (Throwable $throwable) {
                LogUtil::info("history collect fail - ItemSetting - edit");
                LogUtil::info("clientId: {$this->clientId}, userId: {$item->getDomainHandler()->getUserId()}");
                LogUtil::info("referModule: {$module}, referKey: {$itemType}");
                LogUtil::info("referId: {$item->item_id}");
                $validNewSet = 0;
                $valueNew = "";
                $validOldSet = 0;
                $valueOld = "";
                if (isset($itemSettingNew)) {
                    $validNewSet = 1;
                    $valueNew = json_encode($itemSettingNew);
                }
                if (isset($itemSettingOld)) {
                    $validOldSet = 1;
                    $valueOld = json_encode($itemSettingOld);
                }
                LogUtil::info("validNewSet: {$validNewSet}, valueNew: `{$valueNew}`");
                LogUtil::info("validOldSet: {$validOldSet}, valueOld: `{$valueOld}`");
                LogUtil::info("throwable message:{$throwable->getMessage()}");
            }


            $item->getFormatter()->detailInfoSetting($this->module);

            if ($nodes) {
                $this->setNode($item, $nodes);
            } elseif ($nodes === []) {
                $item->getOperator()->setNode($this->module, $this->itemType, []);
            }

	        if (isset($transaction)) {
		        $transaction->commit();
	        }

        } catch (\Throwable $exception) {
            if (isset($transaction)) {
                $transaction->rollback();
            }

            \LogUtil::exception($exception, [
                'itemId' => $itemId,
                'inputData' => $inputData,
                'nodes' => $nodes,
            ]);

            throw new \RuntimeException($exception->getMessage());
        }


        $this->fire((new ItemOperated($this->clientId, $this->module, $this->itemType))->edited($itemId, $diff));

        return $item->getAttributes();
    }

    protected function fillData(SingleObject $object, $data, &$diff = null)
    {
        $attributeKeys = $this->metadata::getExtAttributeKeys();
        foreach ($attributeKeys as $attributeKey) {
            if (isset($data[$attributeKey])) {
                $data['attributes'][$attributeKey] = $data[$attributeKey];
                unset($data[$attributeKey]);
            }
        }
        foreach ($data as $k => $v) {
            if (isset($diff) && $object->$k != $v) {
                $diff[$k] = [
                    'new' => $v,
                    'old' => $object->$k
                ];
            }
            if ($v !== null) {
                $object->$k = $v;
            }
        }

        $object->description = $object->description ?? '';

        return $object;
    }

    /**
     * @param $itemIds
     * @return int
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function disable($itemId)
    {
        $singleObj = $this->metadata::singeObject();
        $itemOld = new $singleObj($this->clientId, $itemId);

        $filter = $this->getFilter();
        $filter->item_id = is_array($itemId) ? new In($itemId) : $itemId;
        $res = $filter->find()->getOperator()->disable($this->module, $this->itemType);

        if ($res) {
            $this->fire((new ItemOperated($this->clientId, $this->module, $this->itemType))->disable($itemId));

            $module = $this->module;
            $itemType = $this->itemType;


            // 操作历史记录
            try {
                $compareEditSetting = new ItemSettingHistoryCompare($this->clientId);
                $compareEditSetting->setModule(ItemSettingHistoryCompare::SETTING_TYPE_ITEM_SETTING);
                $compareEditSetting->setReferModule($module);
                $compareEditSetting->setReferKey($itemType);
                $compareEditSetting->setType(ItemSettingHistoryCompare::ITEM_SETTING_OPERATE_TYPE_DISABLE);
                $itemNew = new $singleObj($this->clientId, $itemId);
                $itemSettingNew = array_replace($itemNew->getRawData(), (json_decode($itemNew->attributes, true) ?? []));
                $itemSettingOld = ($itemOld->getOldAttributes() ?? []);
                $compareEditSetting->setData($itemSettingNew, $itemSettingOld);
                $compareEditSetting->setExtraData(['refer_id' => $itemId]);
                $compareEditSetting->build($itemOld->getDomainHandler()->getUserId());
            } catch (Throwable $throwable) {
                LogUtil::info("history collect fail - ItemSetting - enable");
                LogUtil::info("clientId: {$this->clientId}, userId: {$itemOld->getDomainHandler()->getUserId()}");
                LogUtil::info("referModule: {$module}, referKey: {$itemType}");
                LogUtil::info("referId: {$itemId}");
                $validNewSet = 0;
                $valueNew = "";
                $validOldSet = 0;
                $valueOld = "";
                if (isset($itemSettingNew)) {
                    $validNewSet = 1;
                    $valueNew = json_encode($itemSettingNew);
                }
                if (isset($itemSettingOld)) {
                    $validOldSet = 1;
                    $valueOld = json_encode($itemSettingOld);
                }
                LogUtil::info("validNewSet: {$validNewSet}, valueNew: `{$valueNew}`");
                LogUtil::info("validOldSet: {$validOldSet}, valueOld: `{$valueOld}`");
                LogUtil::info("throwable message:{$throwable->getMessage()}");
            }
        }

        return $res;
    }

    /**
     * @param $itemIds
     * @return int
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function enable($itemId)
    {
        $singleObj = $this->metadata::singeObject();
        $itemOld = new $singleObj($this->clientId, $itemId);

        $this->apiParams->setWithDisabled(true);
        $filter = $this->getFilter();
        $filter->item_id = is_array($itemId) ? new In($itemId) : $itemId;
        $res = $filter->find()->getOperator()->enable($this->module, $this->itemType);

        if ($res) {
            $this->fire((new ItemOperated($this->clientId, $this->module, $this->itemType))->enable($itemId));

            $module = $this->module;
            $itemType = $this->itemType;

            // 操作历史记录
            try {
                $compareEditSetting = new ItemSettingHistoryCompare($this->clientId);
                $compareEditSetting->setModule(ItemSettingHistoryCompare::SETTING_TYPE_ITEM_SETTING);
                $compareEditSetting->setReferModule($module);
                $compareEditSetting->setReferKey($itemType);
                $compareEditSetting->setType(ItemSettingHistoryCompare::ITEM_SETTING_OPERATE_TYPE_ENABLE);
                $itemNew = new $singleObj($this->clientId, $itemId);
                $itemSettingNew = array_replace($itemNew->getRawData(), (json_decode($itemNew->attributes, true) ?? []));
                $itemSettingOld = ($itemOld->getOldAttributes() ?? []);
                $compareEditSetting->setData($itemSettingNew, $itemSettingOld);
                $compareEditSetting->setExtraData(['refer_id' => $itemId]);
                $compareEditSetting->build($itemOld->getDomainHandler()->getUserId());
            } catch (Throwable $throwable) {
                LogUtil::info("history collect fail - ItemSetting - enable");
                LogUtil::info("clientId: {$this->clientId}, userId: {$itemOld->getDomainHandler()->getUserId()}");
                LogUtil::info("referModule: {$module}, referKey: {$itemType}");
                LogUtil::info("referId: {$itemId}");
                $validNewSet = 0;
                $valueNew = "";
                $validOldSet = 0;
                $valueOld = "";
                if (isset($itemSettingNew)) {
                    $validNewSet = 1;
                    $valueNew = json_encode($itemSettingNew);
                }
                if (isset($itemSettingOld)) {
                    $validOldSet = 1;
                    $valueOld = json_encode($itemSettingOld);
                }
                LogUtil::info("validNewSet: {$validNewSet}, valueNew: `{$valueNew}`");
                LogUtil::info("validOldSet: {$validOldSet}, valueOld: `{$valueOld}`");
                LogUtil::info("throwable message:{$throwable->getMessage()}");
            }

        }

        return $res;
    }

    public function canBeTree()
    {
        return in_array($this->itemType, ItemSettingConstant::ITEM_TYPE_CAN_BE_TREE)
            && !in_array($this->module, ItemSettingConstant::ITEM_MODULE_CAN_NOT_BE_TREE[$this->itemType] ?? []);
    }

    /**
     * @param $itemId
     * @param int $newItemId
     * @return array|int
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    final public function delete($itemId, $newItemId = 0)
    {
        $itemId = array_diff((array)$itemId, array_column($this->getExtraDataIds(), 'item_id'));

        if (empty($itemId)) {
            return [];
        }
        $this->apiParams->setWithDisabled(true);
        $filter = $this->getFilter();
        $filter->item_id = new In((array)$itemId);

        $allItemIds = $this->canBeTree() ? $this->getIdList($itemId) : (array)$itemId;
        (new ItemSettingChecker($this->clientId, $this->module, $this->itemType))->checkRule($allItemIds);
        
        $rows = $filter->find()->getOperator()->delete($this->module, $this->itemType, $this->canBeTree(), $newItemId);

        $this->fire((new ItemOperated($this->clientId, $this->module, $this->itemType))->deleted($itemId));

        return $rows;
    }
    
    
    
    
    public function restore($itemId)
    {
        if (empty($itemId)) {
            return [];
        }
        $filter = $this->getFilter();
        $filter->item_id = new In((array)$itemId);
        $filter->enable_flag = 0;
        $rows = $filter->find()->getOperator()->restore($this->module, $this->itemType, $this->canBeTree());

        $this->fire((new ItemOperated($this->clientId, $this->module, $this->itemType))->created($itemId));

        return $rows;
    }

    public function setExternalValueRecursive($userId, $itemId, string $key, $value)
    {
        $ids = $this->getIdList($itemId, true);
        if (empty($ids)) {
            return false;
        }

        $itemValueMap = array_fill_keys($ids, $value);

        return $this->setExternalValue($userId, $key, $itemValueMap);
    }

    public function getExternalValue($userId, $itemId, $key)
    {
        $map = array_column($this->getExternalAPI()->list($itemId, $key, $userId), 'ext_value', 'item_id');
        if (is_numeric($itemId)) {
            return $map[$itemId] ?? null;
        }

        return $map;
    }

    public function getExternalValues($userId, $itemId, $key)
    {
        $map = $this->getExternalAPI()->list($itemId, $key, $userId);

        return $map;
    }

    public function getExternalKvMap($userId, $itemId, $key)
    {
        $map = $this->getExternalAPI()->kvMap($itemId, $key, $userId);
        if (is_numeric($itemId)) {
            return $map[$itemId] ?? null;
        }

        return $map;
    }

    public function editOrderRank($userId, $itemIds)
    {
        if (empty($itemIds)) {
            return 0;
        }
        // 指定用户 且 非仅用户层级的配置
        if (($userId && !$this->metadata::userOnly()) || $this->globalSortFlag) {
            $key = $this->metadata::getUserOrderRankName();
            $rank = 1;
            $map = [];
            foreach ($itemIds as $itemId) {
                $map[$itemId] = $rank++;
            }
            return $this->setExternalValue($userId, $key, $map);
        } else {
            $filter = $this->getFilter();
            $filter->item_id = new In($itemIds);
            return $filter->find()->getOperator()->updateOrderRank($itemIds, $this->module, $this->itemType);
        }
    }

    public function editUserDisplay($userId, $itemIds, $showFlag)
    {
        if ($userId && !$this->apiParams->ownerUserId) {
            $key = $this->metadata::getUserDisplayFlagName();
            $map = [];
            foreach ((array)$itemIds as $itemId) {
                $map[$itemId] = $showFlag ? 1 : 0;
            }
            return $this->setExternalValue($userId, $key, $map);
        }
        return 0;
    }

    public function setExternalValue($userId, $key, $itemValueMap)
    {
        return $this->getExternalAPI()->set($userId, $key, $itemValueMap);
    }

    public function setItemExternalValue($userId, $itemId, $key, $value)
    {
        return $this->setItemExternalValueMap($userId, $itemId, [$key => $value]);
    }

    public function setItemExternalValueMap($userId, $itemId, $map)
    {
        return $this->getExternalAPI()->setMultiKeys($userId, $itemId, $map);
    }

    public function deleteAllUserExternal($userId, $key)
    {
        return $this->getExternalAPI()->delete($userId, $key, [], true);
    }

    public function deleteExternal($userId, $key, array $itemIdList, $recursive = false)
    {
        if ($recursive) {
            $itemIdList = $this->getIdList($itemIdList);
        }

        return $this->getExternalAPI()->delete($userId, $key, $itemIdList, false);
    }

    public function getExtraDataMap()
    {
        $map = [];
        foreach ($this->getMetadataExtraDataMap() as $itemId => $itemName) {
            $item = ['item_name' => $itemName];
            $this->translate($item);
            $map[$itemId] = $item['item_name'];
        }

        return $map;
    }

    protected function getExtraDataListDefaultSetting()
    {
        $dataList = $this->metadata::getExtraDataList($this->module);

        return array_column($dataList, null, 'item_id');
    }

    protected function getExtraDataList($listRecursive = true)
    {
        $list = [];
        $dataList = $this->getExtraDataListDefaultSetting();

        $limitFields = \CustomerOptionService::getLimitFieldByLimitSetting($this->clientId, $this->module);
        $disableItemIds = $limitFields['item_setting'][$this->itemType] ?? [];

        foreach ($this->getMetadataExtraDataMap() as $itemId => $itemName)
        {
            if (in_array($itemId, $disableItemIds)) {
                continue;
            }

            if (!$this->metadata::checkExtraDataPermission($this->clientId, $itemId)) {
                continue;
            }
    
            if (!$listRecursive && ($dataList[$itemId]['parent_id'] ?? 0) > 0) {
    
                continue;
            }
            
            $elem = array_replace([
                'client_id' => $this->clientId,
                'user_id' => 0,
                'item_id' => $itemId,
                'module' => $this->module,
                'item_type' => $this->itemType,
                'item_name' => $itemName,
                'parent_id' => $dataList[$itemId]['parent_id'] ?? 0,
                'field_type' => 0,
                'layer'  => $dataList[$itemId]['layer'] ?? 1,
                'prefix' => '0-' . (($dataList[$itemId]['parent_id'] ?? 0) ?: ''),
                'system_flag' => 1,
                'order_rank' => 0,
                'relate_id' => 0,
                'create_user' => 0,
                'update_user' => 0,
                'create_time' => '',
                'update_time' => '',
                'attributes' => '',
                'description' => '',
                'enable_flag' => 1,
                'disable_flag' => 0,
            ], $dataList[$itemId] ?? []);
            $this->translate($elem);
            $list[] = $elem;
        }

        return $list;
    }

    public function getExtraDataIds()
    {
        return array_keys($this->getExtraDataMap());
    }

    public function isSystemSetting($id)
    {
        return array_key_exists($id, $this->getExtraDataMap());
    }

    protected function validate($dataList)
    {
        list($rules, $messages) = $this->metadata::getValidateConfig($this->module);
        foreach ($dataList as $datum) {
            (new Validator($datum, $rules, $messages))->validate();
        }
    }

//    public static function listConstantItem($module)
//    {
//        $dataList = ItemSettingConstant::ITEM_MODULE_CONFIG_MAP[$module] ?? [];
//        $apiMap = ItemSettingConstant::ITEM_MODULE_API_MAP[$module] ?? [];
//
//        foreach ($dataList as &$datum) {
//            $datum['item_name'] = \Yii::t('field', $datum['item_name']);
//            foreach ($apiMap as $apiKey => $dataKey) {
//                $dataValue = $datum[$dataKey] ?? '';
//                unset($datum[$dataKey]);
//                $datum[$apiKey] = $dataValue;
//            }
//        }
//
//        return array_values($dataList);
//    }

//    /**
//     * @param $module
//     * @return array
//     *
//     * @deprecated
//     */
//    public static function listConstantItemNameMap($module)
//    {
//        $dataList = ItemSettingConstant::ITEM_MODULE_CONFIG_MAP[$module] ?? [];
//        $map = [];
//        foreach ($dataList as &$datum) {
//            $map[$datum['item_id']] = \Yii::t('field', $datum['item_name']);
//        }
//
//        return $map;
//    }

    public function editFlag($statusId, $flagSettingMap)
    {
        $this->edit($statusId, [
            'field_type' => $flagSettingMap
        ]);
    }

//    valuetype
    public function getFieldNameMap($field, $withEmpty = true, $layer = 1, $itemModule = null, $relateItemId = null, $userId = 0, $withPublic = true, $valueType = '')
    {
        $module = $itemModule && !in_array($itemModule, [\Constants::TYPE_CUSTOMER]) ? $itemModule : $this->module;
        $itemType = in_array($field, ItemSettingConstant::$fieldItemTypeMap[$module] ?? []) ? $field : (ItemSettingConstant::$fieldItemTypeMap[$module][$field] ?? 0);
        if ($itemType) {
            $apiModule = $module;
            $apiItemType = $itemType;
            if (isset(ItemSettingConstant::$fieldItemTypeRelateMap[$itemType]) && is_array(ItemSettingConstant::$fieldItemTypeRelateMap[$itemType])) {
                list($apiModule, $apiItemType) = ItemSettingConstant::$fieldItemTypeRelateMap[$itemType];
            }
            $api = new Api($this->clientId, $apiModule, $apiItemType);
            $layer && $api->getParams()->setLayer($layer); // 第一层级
            if ($relateItemId) {
                $api->getParams()->setRelateId($relateItemId);
            }
    
            $api->setOwnerUser($userId, $withPublic);

            if ($relateApiItemType = ItemSettingConstant::$itemTypeRelateMap[$apiItemType] ?? null) {
                $itemNameList = $api->getValueListMap(['relate_id', 'item_name'], 0, true);
                $relateNameMap = ClassCacheRepository::instance($this->clientId, self::class)->remember('relateItemNameMap_' . $relateApiItemType, function () use ($apiModule, $relateApiItemType)  {
                    $api = new Api($this->clientId, $apiModule, $relateApiItemType);
                    return $api->getNameMap(0, true);
                });
                $map = [];
                foreach ($itemNameList as $itemId => $itemDatum) {
                    $namePrefix = $relateNameMap[$itemDatum['relate_id']] ?? '';
                    $map[$itemId] = ($namePrefix ? "「{$namePrefix}」" : '') . $itemDatum['item_name'];
                }
            } else {
                $map = $api->getNameMap(0, true, true, empty($layer));
            }

            if ($withEmpty && !in_array($itemType, ItemSettingConstant::$fieldItemTypeWithoutEmpty)) {
                $emptyValue = ItemSettingConstant::$fieldItemTypeEmptyMap[$apiItemType] ?? '';
                if (!isset($map[$emptyValue])) {
                    $map[$emptyValue] = '空';
                }
            }

            return $map;
        }

        if ($map = ItemSettingConstant::getCustomFieldNameMap($this->clientId, $module, $field, $valueType)) {
            return $map;
        }

        $customFieldService = new CustomFieldService($this->clientId, $module);
        $field = $customFieldService->field($field);
        if ($field) {
            $result = [];
    
            if (is_array($field['ext_info'])) {
                $result = array_combine($field['ext_info'], $field['ext_info']);
            }
    
            if ($withEmpty && ($field['field_type'] ?? 0) == CustomFieldService::FIELD_TYPE_SELECT) {
                $result[''] = '空';
            }
            
            return $result;
        }

        return [];
    }

    public function checkFieldValueMatchAll($field, $value)
    {
        $fixedSelectionField = ['annual_procurement',
            'biz_type',
            'intention_level',
            'scale_id',
            'timezone',
            'post_grade',
            'archive_type',
            'star',
            'gender',];
        if (in_array($field, $fixedSelectionField)) {
            return empty(array_diff(array_keys($this->getFieldNameMap($field)), (array)$value));
        }

        return false;
    }

    public function cachedList($ids = [], $recursive = true, $removeSubIdsFlag = false)
    {
        $ids = (array)$ids;
        $ids = array_unique($ids);
        if (!isset(ItemSettingConstant::$cacheSetting[$this->itemType][$this->module]['params'])) {
            return [];
        }

        $cache = $this->getApiCache();
        if ($removeSubIdsFlag) {
            $ids = array_diff($ids, $cache->subIdsWithoutParent($ids));
        }
        $list = $cache->list($ids, $recursive);
        if ($list === null) {
            $cache->refresh();
            $list = $cache->list($ids, $recursive);
        }

        return array_filter($list ?: []);
    }

    public function cachedSubIds($ids, $preload = false)
    {
        $ids = array_unique((array)$ids);
        if (!isset(ItemSettingConstant::$cacheSetting[$this->itemType][$this->module]['params'])) {
            return [];
        }

        $cache = $this->getApiCache();
        if ($preload) {
            $map = $cache->getSubMap();
            if ($map === null) {
                $cache->refresh();
                $map = $cache->getSubMap();
            }
            $list = (array)$ids;
            foreach ($ids as $id) {
                $list = array_merge($list, $map[$id] ?? []);
            }
        } else {
            $list = $cache->subIds($ids);
            if ($list === null) {
                $cache->refresh();
                $list = $cache->subIds($ids);
            }
        }

        return $list;
    }

    public function getApiCache()
    {
        if ($this->apiCache == null) {
            $this->apiCache = new ApiCache($this->clientId, $this->module, $this->itemType);
        }

        return $this->apiCache;
    }

    public function listen(ItemOperated $event)
    {
        \LogUtil::info("handle item operate", [
            'handler' => [
                'itemType' => $this->itemType,
                'module' => $this->module,
            ],
            'event' => json_decode(strval($event), true),
        ]);
    }

    protected function fire(ItemOperated $event)
    {
        $this->handle($event);
    }

    protected function handle(ItemOperated $event)
    {
        $this->getApiCache()->refresh();
    
        (new TranslateService($this->clientId, $event->module, $event->itemType))->refreshByRefer($event->getIds());
        
        if (!$this->skipEvent && !empty($event->getIds())) {
            Api::instance($event->clientId, $event->module, ItemSettingConstant::ITEM_TYPE_SWARM)->listen($event);
            Api::instance($event->clientId, $event->module, ItemSettingConstant::ITEM_TYPE_SWARM_PUBLIC)->listen($event);
            Api::instance($event->clientId, $event->module, ItemSettingConstant::ITEM_TYPE_PUBLIC_RULE)->listen($event);
        } else {
            \LogUtil::debug("skip item event", [
                'handler' => [
                    'itemType' => $this->itemType,
                    'module' => $this->module,
                ],
                'event' => json_decode(strval($event), true),
            ]);

        }
    }

    /**
     * @return array
     */
    protected function getMetadataExtraDataMap(): array
    {
        return $this->metadata::getExtraDataMap($this->module);
    }

    public static function getItemFieldId($module, $value, $swarmType)
    {
        if (is_numeric($value)) {
            return $value;
        }
    
        if (in_array($value, Swarm::GLOBAL_ITERATE_FIELD_DISABLED) || in_array($value, RuleConfigConstants::FILTER_FIELDS_IGNORE[SwarmService::getRuleConfigType($swarmType)][$module] ?? [])) {
        
            return 0;
        }
        
        return ItemSettingConstant::$fieldItemTypeMap[$module][$value] ?? (ItemSettingConstant::$fieldItemTypeCustomMap[$module][$value] ?? 0);
    }

    protected function getFieldId($iterateField, $itemModule) {
        return array_flip(ItemSettingConstant::$fieldItemTypeMap[$itemModule])[$iterateField] ?? (array_flip(ItemSettingConstant::$fieldItemTypeCustomMap[$itemModule] ?? [])[$iterateField] ?? $iterateField);
    }

    public function skipCache($data)
    {
        return false;
    }

    //获取规格值列表增加规格分组
    public function attributeTree()
    {
        $attributeGroupApi = new \common\library\product_v2\attribute_group\ProductAttributeGroupApi($this->clientId);
        $list = $attributeGroupApi->newAttributeGroupList();
        $groupData = $list['list']??[];
        $groupIds = array_column($groupData,'attribute_group_id');
        $groupMap = [];
        foreach ($groupData as $groupItem){
            $groupMap[$groupItem['attribute_group_id']] = [];
        }
        $filter = new \common\library\setting\item\ProductAttributeApi($this->clientId);
        $attributeList = $filter->pagination($groupIds,'group');
        $attributeData = $attributeList['list']??[];
        foreach ($attributeData as $attributeDatum){
            $groupMap[$attributeDatum['relate_id']][] = $attributeDatum;
        }
        foreach ($groupData as $listKey=>$listData){
            $groupData[$listKey]['node'] = $groupMap[$listData['attribute_group_id']] ;
        }

        return $groupData;
    }


}
