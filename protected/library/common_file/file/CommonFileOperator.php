<?php

namespace common\library\common_file\file;

use common\components\BaseObject;
use common\library\object\object_define\Constant;
use xiaoman\orm\common\Operator;
use xiaoman\orm\database\DBConstants;
use common\library\recycle;


/**
 * class CommonFileOperator
 * @package common\library\common_file\file
 */
class CommonFileOperator extends Operator
{
    const TASK_LIST = [
        'recycle' => [
            'method' => 'recycle',
        ]
    ];

    public function batchInsertOrUpdate(array $data = [], $insertModel = DBConstants::INSERT_MODE_GENERAL, array $duplicateSet = [], array $conflict = [])
    {
        return parent::batchInsert($data, $insertModel, $duplicateSet, $conflict);
    }

    public function update($updateData)
    {
        if (empty($this->object->getListAttributes())) {
            return 0;
        }
        return $this->execute($updateData);
    }

    public function execute(array $setValue)
    {
        return parent::execute($setValue);
    }

    protected function recycle(array $data, array $setting)
    {
        $opUser = $this->object->getDomainHandler();
        // 写入回收箱
        recycle\API::add($this->clientId, $opUser->getUserId(), recycle\Recycle::DISK_FILE, $setting['file_ids'], objectName: $setting['object_name']);
    }

    public function delete()
    {
        $this->onlySingle();
        if ( $this->object->isNew() || $this->object->delete_flag ){
            throw new \RuntimeException(\Yii::t('file', 'File does not exist or has been deleted'));
        }

        $company = new \common\library\customer_v3\company\orm\Company($this->clientId, $this->object->object_id);

        if (!$company->isExist())
            throw new \RuntimeException(\Yii::t('customer', 'Delete failed, customer does not exist or has been deleted'));

        if (!$company->checkOwner($this->object->getDomainHandler()))
            throw new \RuntimeException(\Yii::t('customer', 'Non-own data'));

        $opUser = $this->object->getDomainHandler();
        $setValue = [
            'delete_flag' => BaseObject::ENABLE_FLAG_TRUE,
            'delete_user' => $opUser->getUserId(),
            'delete_time' => date('Y-m-d H:i:s'),
        ];
        $rows = $this->standardProcess($setValue, [
            'recycle' => [
                'object_name' => $this->object->object_name,
                'file_ids' => [$this->object->file_id],
            ],
        ]);
        $filesCount = $this->getFilter()->count();

        \LogUtil::info('delete file count:' . $filesCount . ', delete success count:' . $rows);
        return $rows;
    }

    public function batchDelete()
    {
        $opUser = $this->object->getDomainHandler();
        $commonFiles = $this->get(['object_name', 'file_id']);
        $setValue = [
            'delete_flag' => BaseObject::ENABLE_FLAG_TRUE,
            'delete_user' => $opUser->getUserId(),
            'delete_time' => date('Y-m-d H:i:s'),
        ];

        $rows = $this->standardProcess($setValue,  [
            'recycle' => [
                'object_name' => array_column($commonFiles, 'object_name')[0] ?? '',
                'file_ids' => array_column($commonFiles, 'file_id'),
            ]
        ]);
        $filesCount = $this->getFilter()->count();

        \LogUtil::info('delete file count:' . $filesCount . ', delete success count:' . $rows);
        return $rows;
    }
}