<?php
/**
 * This file is part of the xiaoman/crm.
 *
 * (c) sevenshi <<EMAIL>>
 *
 */

namespace common\library\invoice\export;

use common\library\api\InnerApi;
use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldExportService;
use common\library\custom_field\FieldMoreFormats;
use common\library\customer_v3\company\orm\Company;
use common\library\invoice\export\template\InvoiceExportTemplateApi;
use common\library\invoice\export\template\InvoiceExportTemplateConstant;
use common\library\invoice\Order;
use common\library\invoice\Quotation;
use common\library\object\field\FieldConstant;
use common\library\object\field\formatter\field_formatter\CountryFormatter;
use common\library\object\object_define\Constant as ObjConstant;
use common\library\oms\shipping_invoice\ShippingInvoice;
use common\library\privilege_v3\object_service\UserObjectPrivilegeService;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeFieldV2;
use common\library\product_v2\ProductHelper;
use common\library\purchase\purchase_order\PurchaseOrder;
use common\models\client\InvoiceExportTemplate;

abstract class AbstractInvoiceExport
{
    protected $type; //导出文件类型
    protected $fileName; //文件名字
    protected $templateFileId; //模版文件fileId
    protected $templateId; //模版id
    protected $templateName; // 模版名字
    protected $base = []; //系统字段分组
    protected $products = []; //产品列表字段分组
    protected $costs = []; //附加费用字段分组
    protected $clientId;
    protected $userId;
    protected $invoiceId; //对象id
    protected $invoiceObj; //导出对象
    protected $invoiceType; //订单/报价单
    protected $modelData; //导出对象数据
    protected $productStartRow; //产品起始行
    protected $productEndRow; //产品结束行
    protected $priceStartRow;// 附加价格起开始行
    protected $priceEndRow;//附加价格结束行
    protected $exportMethod; // 导出/预览
    protected $module; //上报客户端
    protected $needOutPut = true; // 是否需要在浏览器输出
    protected $templateFileUrl; //模版url 旧的模版只有url 没有template_file_id
    protected $fileId = null; //文件id
    protected $recordType = InvoiceExportTemplateConstant::INVOICE_RECORD_COMMON; //明细类型，默认是普通
    protected $fieldPrivileges = [];

    protected static $category = [];

    const METHOD_PREVIEW = 1;
    const METHOD_EXPORT = 2;

    const CREATE_TYPE_MANUAL = 1;
    const CREATE_TYPE_JAVA_IDENTIFY = 2;

    const REPORT_MODULE_CRM = 'crm';
    const REPORT_MODULE_APP = 'app';

    protected $spacialFieldFunctionMap = [
        '#产品_产品类目#' => 'getProductCategory',
        '#交易产品_产品备注#' => 'formatProductRemark',
        '#报价产品_产品备注#' => 'formatProductRemark',
        '#产品_备注#' => 'formatProductRemark',
        '#产品_包装说明#' => 'formatProductRemark',
        '#交易产品_产品描述#' => 'formatProductRemark',
        '#报价产品_产品描述#' => 'formatProductRemark',
        '#产品_所有产品属性字段#' => 'formatProductInfoJson',
    ];

    protected $specialIdFunctionMap = [
        'images_1' => 'formatProductImages',
        'images_2' => 'formatProductImages',
        'images_3' => 'formatProductImages',
        'images_4' => 'formatProductImages',
        'images_5' => 'formatProductImages',
        'images_6' => 'formatProductImages',
        'category_ids' => 'getProductCategory',
        'product_remark' => 'formatProductRemark',
        'package_size' => 'formatProductPackageSize',
        'product_size' => 'formatProductSize',
        'carton_size' => 'formatCartonSize',
        'info_json' => 'formatProductInfoJson',
        'trail_status' => 'formatTrailStatus',
        'cus_tag' => 'formatCusTag'
    ];

    protected $noPrivilegeValue = '无权限';


    public function __construct($clientId, $userId, $invoiceId, $templateId)
    {
        $this->clientId = $clientId;
        $this->userId = $userId;
        $this->invoiceId = $invoiceId;
        $templateId && $this->loadTemplateById($templateId);
        $this->noPrivilegeValue = \Yii::t('privilege', FieldConstant::FIELD_VALUE_EXPORT_MASK);
        $this->loadFieldPrivilege();
    }


    private function loadTemplateById($templateId)
    {
        $templateSetting = InvoiceExportTemplate::getTemplateById($templateId, $this->clientId);
        if (!$templateSetting)
            throw new \RuntimeException('获取模板失败！');

        if ($templateSetting['system_template_id'] > 0 && $templateSetting['template_file_id'] == 0) {
            $invoiceExportTemplateApi = new InvoiceExportTemplateApi($this->clientId);
            $templateSetting['template_file_id'] = $invoiceExportTemplateApi->updateSystemTemplateFileId($templateSetting)['template_file_id'];
        }

        $this->templateFileId = $templateSetting['template_file_id'];
        $this->productStartRow = $templateSetting['product_start_row'];
        $this->productEndRow = $templateSetting['product_end_row'];
        $this->priceStartRow = $templateSetting['price_start_row'];
        $this->priceEndRow = $templateSetting['price_end_row'];
        $this->templateName = \Yii::t('export', $templateSetting['template_name']);
        $this->recordType = $templateSetting['record_type'];
        $this->templateId = $templateId;
        $this->templateFileUrl = '';
    }

    /**
     * @param mixed $type
     */
    public function setType($type): void
    {
        $this->type = $type;
    }

    /**
     * @param mixed $fileName
     */
    public function setFileName($fileName): void
    {
        $this->fileName = $fileName;
    }

    /**
     * @param mixed $exportMethod
     * @param bool $needOutPut
     */
    public function setExportMethod($exportMethod, $needOutPut = true): void
    {
        $this->exportMethod = $exportMethod;
        $this->needOutPut = $needOutPut;
    }

    /**
     * @param string $module
     * 设置客户端（用于上报）
     */
    public function setModule($module = 'crm')
    {
        $this->module = $module;
    }

    /**
     * @param bool $needOutPut
     */
    public function setNeedOutPut(bool $needOutPut): void
    {
        $this->needOutPut = $needOutPut;
    }

    /**
     * @param int $fileId
     */
    public function setFile($fileId): void
    {
        $this->fileId = $fileId;
    }

    /**
     * @return OrderExportFile
     * <AUTHOR>
     */
    abstract protected function getClassExportFile();

    private function buildExportData()
    {
        $this->formatModelData();

        if (empty($this->fileName)) {
            $name = explode('.', $this->templateName);
            if (count($name) >= 2) {
                $this->fileName = $name[0] ?? '';
            }
        }

        return [
            'template_file_id' => intval($this->templateFileId),
            'template_file_url' => $this->templateFileUrl,
            'template_id' => intval($this->templateId),
            'client_id' => intval($this->clientId),
            'user_id' => intval($this->userId),
            'file_name' => $this->fileName,
            'type' => $this->type,
            'invoice_id' => intval($this->invoiceId),
            'update_time' => $this->invoiceObj->update_time,
            'base' => $this->modelData['base'] ?: [],
            'products' => $this->modelData['product_list'] ?: [],
            'costs' => $this->modelData['cost_list'] ?? [],
            'product_start_row' => intval($this->productStartRow),
            'product_end_row' => intval($this->productEndRow),
            'price_start_row' => intval($this->priceStartRow),
            'price_end_row' => intval($this->priceEndRow)
        ];
    }

    public function statisticUsed($count = 1)
    {
        if ($this->templateId > 0)
            InvoiceExportTemplate::statisticUsedNum($this->templateId, $this->clientId, $count);
    }

    private function getExportInfoFromJava()
    {
        $postParams = $this->buildExportData();
        $responseData = $this->postJava($postParams, 'order_export');
        $responseData = $responseData['data'];

        $fileId = $responseData['fileId'] ?? '';

        // 体验系统
        if (in_array(\Yii::app()->params['env'], ['exp', 'exp-mig'])) {
            $fileId = \common\library\file\Helper::saveUniqueFile($this->clientId, $this->userId, $responseData['md5File'] ?? '', $responseData['fileSize'] ?? 0);
        }

        if (empty($fileId)) {
            throw  new \RuntimeException(\Yii::t('invoice', 'The export failed'));
        }

        $aliYunUpload = new \AliyunUpload();
        $aliYunUpload->loadByFileId($fileId);

        return [
            'file_id' => $fileId,
            'preview_url' => $aliYunUpload->getPreview(),
            'export_url' => $aliYunUpload->getFileUrl(),
            'file_name' => $aliYunUpload->getFileName(),
            'file_size' => $aliYunUpload->getFileSize()
        ];
    }

    private function postJava($postParams, $interface)
    {
        $needOutPut = $this->needOutPut;
        $api = new InnerApi('invoice_export_optimization');
        $api->setTimeout(40);
        $api->resetHeader();
        $api->setHttpMethod(InnerApi::HTTP_METHOD_POST_JSON);
        $api->addHeader(["content-type:application/json; charset=utf-8; protocol=gRPC"]);
        //不处理错误码
        $api->setApiNoCodeHandler(function ($json) {
            return;
        });
        $api->setTimeoutHandler(function ($code, $msg) use ($needOutPut) {
            //导出操作提示导出失败
            if ($needOutPut) {
                $e = new ExportFileException(\Yii::t('invoice', 'The export failed'));
                \common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());
                throw $e;
            }

            throw new \RuntimeException(\Yii::t('invoice', 'File generating, please retry after several minutes'));
        });

        $api->setApiErrorHandler(function ($json) use ($needOutPut) {

            $errMsg = $json['msg'] ?? '';
            if (str_contains($errMsg, 'template don\'t support this request, because this template contains portrait merged Region')) {
                throw  new \RuntimeException("模板报错",\ErrorCode::CODE_TEMPLATE_ERROR);
            }
            $errorMessage = \Yii::t('invoice', 'The export failed');

            //判断一下是否是xls文件
            $aliYunUpload = new \AliyunUpload();
            $aliYunUpload->loadByFileId($this->templateFileId);
            $ext = $aliYunUpload->getFileExt();

            if ($ext == '.xls') {
                $errorMessage = \Yii::t('invoice', 'The export failed，Use an .xlsx file to export');
            }
            $e = new ExportFileException(\Yii::t('invoice', 'The export failed'));
            \common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());


            if ($needOutPut) {
                throw $e;
            }

            throw  new \RuntimeException($errorMessage);
        });

        $responseData = $api->call($interface, $postParams);
        if (!isset($responseData['data']) || empty($responseData['data'])) {
            throw  new \RuntimeException(\Yii::t('invoice', 'The export failed'));
        }
        return $responseData;
    }

    private function getPdfPreViewFromJava()
    {
        $postParams = [
            'client_id' => intval($this->clientId),
            'user_id' => intval($this->userId),
            'file_id' => $this->fileId,
        ];

        $responseData = $this->postJava($postParams, 'order_export_pdf');

        $responseData = $responseData['data'];

        $fileId = $responseData['fileId'] ?? '';

        // 体验系统
        if (in_array(\Yii::app()->params['env'], ['exp', 'exp-mig'])) {
            $fileId = \common\library\file\Helper::saveUniqueFile($this->clientId, $this->userId, $responseData['md5File'] ?? '', $responseData['fileSize'] ?? 0);
        }

        if (empty($fileId)) {
            throw  new \RuntimeException(\Yii::t('invoice', 'The export failed'));
        }

        $aliYunUpload = new \AliyunUpload();
        $aliYunUpload->loadByFileId($fileId);

        return [
            'file_id' => $fileId,
            'preview_url' => $aliYunUpload->getPreview(),
            'export_url' => $aliYunUpload->getFileUrl(),
            'file_name' => $aliYunUpload->getFileName(),
            'file_size' => $aliYunUpload->getFileSize()
        ];
    }


    //预览
    public function preview()
    {
        return $this->getExportInfoFromJava();
    }

    //在线编辑切换pdf,获取pdf预览文件
    public function pdfPreViewByFile()
    {
        return $this->getPdfPreViewFromJava();
    }

    //导出
    public function export()
    {
        if (!empty($this->fileId)) {
            $aliYunUpload = new \AliyunUpload();
            $aliYunUpload->loadByFileId($this->fileId);
            $exportInfo = [
                'file_id' => $this->fileId,
                'export_url' => $aliYunUpload->getFileUrl(),
                'file_name' => $this->fileName . $aliYunUpload->getFileExt(),
            ];
        } else {
            $exportInfo = $this->getExportInfoFromJava();
        }

        $this->saveExportFile($exportInfo['file_id']);
        $this->statisticUsed();
        return $exportInfo;
    }

    /**
     * 文件下载
     * @param string $fileUrl
     * @param string $fileName
     * 使用该方法，需考虑文件大小，内存，服务器网络带宽等因素，慎用
     */
    private function outPut($fileUrl, $fileName)
    {
        header("Content-type:application/" . $this->type);
        header('Content-Disposition: attachment;filename="' . $fileName . '"');
        readfile($fileUrl);
    }

    private function saveExportFile(string $fileId)
    {
        $exportFile = $this->getClassExportFile();
        $exportFile->setInvoice($this->invoiceObj);
        $exportFile->loadFileById($fileId);
        if ($this->templateId) {
            $exportFile->setTemplateId($this->templateId);
            $exportFile->setTemplateName($this->templateName);
        }
        $exportFile->saveExportFile();
    }


    private function formatModelData()
    {
        //产品没有权限字段
        $noPermissionProductFields = $this->getDisableField(\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_PRODUCT, \Constants::TYPE_PRODUCT);

        //私海客户没有权限模块
        $companyFunctional = \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_CUSTOMER;
        $company = new Company($this->clientId, $this->invoiceObj->company_id);
        if ($company->isPublic()) {
            //公海客户权限模块
            $companyFunctional = \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_COMPANY_POOL;
        }

        //todo 客户和联系人接入新版字段权限后，修改该处逻辑，按人维度获取没有权限的字段
        //客户没有权限字段
        $noPermissionCompanyFields = $this->getHasFieldPermission($companyFunctional, \Constants::TYPE_COMPANY);
        $noPermissionCompanyFields = $noPermissionCompanyFields[\common\library\privilege_v3\PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_DISABLE] ?? [];

        //联系人没有权限字段
        $noPermissionCustomerFields = $this->getHasFieldPermission($companyFunctional, \Constants::TYPE_CUSTOMER);
        $noPermissionCustomerFields = $noPermissionCustomerFields[\common\library\privilege_v3\PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_DISABLE] ?? [];

        $specialFields = [
            '#交易产品_序号#',
            '#报价产品_序号#',
            '#采购产品_序号#',
            '#出运明细_序号#',
            '#装箱明细_序号#',
            '#报关明细_序号#',
            '#导出日期#',
            '#导出人_昵称#',
            '#导出人_邮箱#',
            '#导出人_电话#',
            '#导出人_传真#',
            '#导出人_联系地址#',
            '#导出人_其他信息#',
            '#创建人_昵称#',
            '#创建人_邮箱#',
            '#创建人_电话#',
            '#创建人_传真#',
            '#创建人_联系地址#',
            '#创建人_其他信息#',
            '#客户#',
            '#联系人#',
            '#阿里业务员#',
            '#出口方式#',
            '#订单类型#',
            '#阿里订单ID#',
            '#阿里订单状态#',
            '#来源店铺#'
        ];


        //不同模块的字段权限
        //当前模块的字段权限放在 $this->fieldPrivileges
        $noPermissionFields = [
            \Constants::TYPE_PRODUCT => $noPermissionProductFields,
            \Constants::TYPE_COMPANY => $noPermissionCompanyFields,
            \Constants::TYPE_CUSTOMER => $noPermissionCustomerFields
        ];


        foreach ($this->modelData as $key => &$data) {
            if (in_array($key, ['product_list', 'cost_list'])) {

                if (empty($data)) {
                    $data = $this->setDefaultValue($key);
                    continue;
                }

                $productNo = 1;

                foreach ($data as &$datum) {

                    if ($this->invoiceObj instanceof Order) {
                        $datum = array_merge($datum, ['#交易产品_序号#' => [
                            'field_type' => "1",
                            'id' => 'export_no',
                            'relation_field' => "0",
                            'relation_field_type' => "0",
                            'value' => (string)$productNo
                        ]]);
                    }

                    if ($this->invoiceObj instanceof Quotation) {
                        $datum = array_merge($datum, ['#报价产品_序号#' => [
                            'field_type' => "1",
                            'id' => 'export_no',
                            'relation_field' => "0",
                            'relation_field_type' => "0",
                            'value' => (string)$productNo
                        ]]);
                    }

                    if ($this->invoiceObj instanceof PurchaseOrder) {
                        $datum = array_merge($datum, ['#采购产品_序号#' => [
                            'field_type' => "1",
                            'id' => 'export_no',
                            'relation_field' => "0",
                            'relation_field_type' => "0",
                            'value' => (string)$productNo
                        ]]);
                    }

                    if ($key == 'product_list' && $this->invoiceObj instanceof ShippingInvoice) {
                        $datum = array_merge($datum, ['#出运明细_序号#' => [
                            'field_type' => "1",
                            'id' => 'export_no',
                            'relation_field' => "0",
                            'relation_field_type' => "0",
                            'value' => (string)$productNo
                        ]]);
                    }

                    $productNo++;
                    $this->processField($datum, $noPermissionFields, $specialFields, true);
                }
            } else {

                $data = array_merge($data, ['#导出日期#' => [
                    'field_type' => "1",
                    'id' => 'export_time',
                    'relation_field' => "0",
                    'relation_field_type' => "0",
                    'value' => date('Y-m-d')
                ]]);

                $this->processField($data, $noPermissionFields, $specialFields);
            }
        }
        $this->formateMoreData();
    }

    private function formateMoreData(): void {
        if (!array_key_exists($this->invoiceType, FieldMoreFormats::$formats_map)) {
            return;
        }
        $more_formats = FieldMoreFormats::$formats_map[$this->invoiceType];
        $uppercase_format_map = $more_formats['uppercase_format'] ?? null;
        $country_format = $more_formats['country_format']['country'] ?? null;

        if (!$uppercase_format_map && !$country_format) {
            return;
        }

        if ($uppercase_format_map) {
            foreach ($uppercase_format_map as $key => $uppercase_map) {
                $dependCode = $uppercase_map['depend_code'];
                $value = $this->modelData['base'][$dependCode]['value'] ?? null;
                if ($value !== null) {
                    // 如果原数据的无权限，则返回无权限
                    if ($value != $this->noPrivilegeValue) {
                        $value = \common\library\oms\common\Helper::tranAmountEnglishByCurrency($value, $this->invoiceObj->currency);
                    }
                    $this->modelData['base'] = array_merge($this->modelData['base'],[$uppercase_map['code'] => ['field_type'=> '1','value' =>$value]]);
                }
            }
        }

        if ($country_format) {
            $countryInfo = null;
            foreach ($country_format as $country_format_map) {
                $dependCode = $country_format_map['depend_code'];
                $countryEnName = $this->modelData['base'][$dependCode]['value'] ?? null;

                if ($countryEnName != $this->noPrivilegeValue) {
                    if ($countryInfo == null){
                        $countryService = new \CountryService();
                        $countryInfo = $countryService->getByCountryEnName($countryEnName);
                    }
                    $value = \common\library\oms\common\Helper::countryConvert($countryInfo, $country_format_map["name"]);
                } else {
                    $value = $this->noPrivilegeValue;
                }

                $this->modelData['base'] = array_merge($this->modelData['base'],[$country_format_map['code'] => ['field_type'=> '1','value' =>$value]]);
            }
        }
    }

    private function processField(&$data, $noPermissionFields, $specialFields,$isList=false)
    {
        foreach ($data as $fieldName => &$datum) {
            //$datum 改造后用两种格式，一种是一位数组，一种是二维数组（合并用）
            if ($isList && self::isMultiData($datum)) {
                foreach ($datum as &$item) {
                    //对字段进行鉴权
                    $this->checkFieldPermission($fieldName, $item, $specialFields, $noPermissionFields);
                    //转换格式以及特殊字段处理
                    $this->formatFieldType($fieldName, $item);
                    //对结果值进行处理
                    $this->formatFieldValue($fieldName, $item);
                }
            }else{
                //对字段进行鉴权
                $this->checkFieldPermission($fieldName, $datum, $specialFields, $noPermissionFields);
                //转换格式以及特殊字段处理
                $this->formatFieldType($fieldName, $datum);
                //对结果值进行处理
                $this->formatFieldValue($fieldName, $datum);
            }

        }
    }

    private function getProductCategory($categoryArray)
    {
        if (!is_array($categoryArray))
            return $categoryArray;

        $categoryArray = $categoryArray ? $categoryArray : [];

        $findCategoryIds = [];
        foreach ($categoryArray as $ids) {
            $ids = is_array($ids) ? $ids : [$ids];
            foreach ($ids as $id) {
                if (!isset(self::$category[$id])) {
                    $findCategoryIds[] = $id;
                }
            }
        }

        if ($findCategoryIds) {
            $list = \Category::findAllByIds($findCategoryIds);
            foreach ($list as $item) {
                self::$category[$item->id] = "{$item->cn_name}（{$item->en_name}）";
            }
        }

        $insertValue = [];
        foreach ($categoryArray as $ids) {
            $ids = is_array($ids) ? $ids : [$ids];
            foreach ($ids as $id) {
                $insertValue[] = isset(self::$category[$id]) ? self::$category[$id] : '';
            }
        }

        $insertString = implode('—', $insertValue);
        return $insertString;
    }

    private function formatProductRemark($remark)
    {
        return \Util::replaceBrToLinebreak($remark);
    }

    private function formatProductInfoJson($infoJson)
    {
        if (is_string($infoJson)) {
            return $infoJson;
        }

        $data = [];
        $infoJson = is_array($infoJson) ? $infoJson : [];
//        foreach ($infoJson as $item) {
//            if(isset($item['name']) && $item['name']){
//                $data[] = "{$item['name']}:".(isset($item['value']) ? $item['value'] : '-');
//            }
//        }
//        return implode("\n", $data);
        return ProductHelper::formatInfoJsonString($infoJson, "\n");
    }

    private function getHasFieldPermission($functionType, $type)
    {
        return \common\library\privilege_v3\Helper::getFieldIdByScope(
            $this->clientId,
            $this->userId,
            $functionType,
            $type,
            [
                \common\library\privilege_v3\PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_READONLY,
                \common\library\privilege_v3\PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_DISABLE,
            ]
        );
    }

    private function getDisableField($functionType, $type)
    {
        $privilegeFieldStats = (new UserObjectPrivilegeService($this->clientId))
            ->setUserId($this->userId)
            ->setFunctionalId($functionType)
            ->setFieldPrivilegeScene( PrivilegeFieldV2::SCENE_OF_EXPORT)
            ->getPrivilegeFieldStats();

        $disableFields = [];
        foreach($privilegeFieldStats as $stat){
            if($stat['refer_type'] == $type){
                $disableFields = array_merge($disableFields, $stat['disable'] ?? []);
            }
        }
        return $disableFields;
    }

    private function formatProductImages($image)
    {
        if (is_array($image)) {
            $image = current($image);
            $fileId = $image['file_id'] ?? 0;

            if ($fileId != 0) {
                $aliYunUpload = new \AliyunUpload();
                $aliYunUpload->loadByFileId($fileId);
                return $aliYunUpload->getFileUrl();
            }

            return '';
        }
        return $image;
    }

    protected function formatProductPackageSize($value)
    {
        if (!is_array($value))
            return $value;

        $empty = true;
        foreach ($value as $item) {
            if ($item > 0) {
                $empty = false;
                break;
            }
        }

        if ($empty == true)
            return '';

        $data = [
            'package_size_length' => ($value['package_size_length'] ?? 0) . '*',
            'package_size_weight' => ($value['package_size_weight'] ?? 0) . '*',
            'package_size_height' => ($value['package_size_height'] ?? 0),
        ];

        $value = implode(' ', $data);
        return $value;
    }

    protected function formatProductSize($value)
    {
        if (!is_array($value))
            return $value;

        $empty = true;
        foreach ($value as $item) {
            if ($item > 0) {
                $empty = false;
                break;
            }
        }

        if ($empty == true)
            return '';

        $data = [
            'product_size_length' => ($value['product_size_length'] ?? 0) . '*',
            'product_size_weight' => ($value['product_size_weight'] ?? 0) . '*',
            'product_size_height' => ($value['product_size_height'] ?? 0),
        ];

        $value = implode(' ', $data);
        return $value;
    }

    protected function formatCartonSize($value)
    {
        if (!is_array($value))
            return $value;

        $empty = true;
        foreach ($value as $item) {
            if ($item > 0) {
                $empty = false;
                break;
            }
        }

        if ($empty == true)
            return '';

        $data = [
            'carton_size_length' => ($value['carton_size_length'] ?? 0) . '*',
            'carton_size_weight' => ($value['carton_size_weight'] ?? 0) . '*',
            'carton_size_height' => ($value['carton_size_height'] ?? 0),
        ];

        $value = implode(' ', $data);
        return $value;
    }

    protected function formatTrailStatus($value)
    {
        if (is_array($value)) {
            return $value['item_name'] ?? '';
        }

        return (string)$value;
    }

    protected function formatCusTag($value)
    {
        if (is_array($value)) {
            $res = [];
            foreach ($value as $item) {
                $res[] = $item['tag_name'] ?? '';
            }

            return implode(',', $res);
        }

        return (string)$value;
    }

    private function checkFieldPermission($fieldName, &$datum, $specialFields, $noPermissionFields)
    {
        //特殊字段不处理或者在有权限字段里面
        if ($this->invoiceType != \Constants::TYPE_PURCHASE_ORDER
            && !in_array($fieldName, $specialFields)
            && in_array($datum['id'], $noPermissionFields[$datum['type']] ?? [])) {
            $datum['value'] = $this->noPrivilegeValue;
        }

        // 如果是非特殊字段 && 没有字段权限，则将值置为无权限
        if ( !in_array($datum['id'], $specialFields) &&
            !empty($this->fieldPrivileges['disable'])
            && ($datum['type'] ?? null) == $this->fieldPrivileges['refer_type']
            && in_array($datum['id'] ?? null, $this->fieldPrivileges['disable'] ?? [])
        ) {
            $datum['value'] = \Yii::t('privilege', FieldConstant::FIELD_VALUE_EXPORT_MASK);
        }
        // Java侧的pb协议不允许传type
        unset($datum['type']);
    }

    private function formatFieldType($fieldName, &$datum)
    {

        //引用字段转换
        if ($datum['field_type'] == CustomFieldService::FIELD_TYPE_FIELDS) {
            $datum['field_type'] = $datum['relation_field_type'];
            $datum['id'] = $datum['relation_field'];
        }

        //转换不符合导出字段类型的系统字段和关联字段
        if (
            !in_array($datum['field_type'], [
                CustomFieldService::FIELD_TYPE_TEXT, //文本类型
                CustomFieldService::FIELD_TYPE_IMAGE, //图片字段
                CustomFieldService::FIELD_TYPE_NUMBER, //数值字段
                CustomFieldService::FIELD_TYPE_FIELDS, //引用字段
                CustomFieldService::FIELD_TYPE_DATE,     //日期字段
                CustomFieldService::FIELD_TYPE_DATETIME, //日期字段
                CustomFieldService::FIELD_TYPE_FORMULA,//公式字段
                CustomFieldService::FIELD_TYPE_CALCULATE,//汇总字段
            ])
            || ($datum['field_type'] == CustomFieldService::FIELD_TYPE_FIELDS
                && !in_array($datum['field_type'], [
                    CustomFieldService::FIELD_TYPE_TEXT, //文本类型
                    CustomFieldService::FIELD_TYPE_IMAGE, //图片字段
                    CustomFieldService::FIELD_TYPE_NUMBER, //数值字段
                    CustomFieldService::FIELD_TYPE_FIELDS //引用字段
                ])
            )) {
            $datum['field_type'] = (string)CustomFieldService::FIELD_TYPE_TEXT;
        }

        if (in_array($datum['field_type'], [
            CustomFieldService::FIELD_TYPE_FORMULA, //公式字段
            CustomFieldService::FIELD_TYPE_CALCULATE,    //汇总字段
        ])
        ) {
            $datum['field_type'] = (string)CustomFieldService::FIELD_TYPE_NUMBER;
        }

        if ($fieldName == '#交易产品_图片#' || $fieldName == '#报价产品_图片#') {
            $datum['field_type'] = (string)CustomFieldService::FIELD_TYPE_IMAGE;
        }

        //图片字段若是无权限，则修改成为文本格式
        if ($datum['field_type'] == CustomFieldService::FIELD_TYPE_IMAGE &&
            ($datum['value'] == $this->noPrivilegeValue || $datum['value'] == '')) {
            $datum['field_type'] = (string)CustomFieldService::FIELD_TYPE_TEXT;
        }
    }

    private function formatFieldValue($fieldName, &$datum)
    {
        if ($datum['value'] == $this->noPrivilegeValue) {
            return;
        }

        if (isset($this->spacialFieldFunctionMap[$fieldName])) {
            $function = $this->spacialFieldFunctionMap[$fieldName];
            $datum['value'] = $this->$function($datum['value']);
        }

        if (isset($this->specialIdFunctionMap[$datum['id']])) {
            $function = $this->specialIdFunctionMap[$datum['id']];
            $datum['value'] = $this->$function($datum['value']);
        }
        if ($datum['relation_field_type'] == CustomFieldService::FIELD_TYPE_ATTACH
        ) {
            $datum['value'] = "";
        }

        if ($datum['field_type'] == CustomFieldService::FIELD_TYPE_IMAGE
        ) {
            if (!preg_match("/^http(s)?:\\/\\/.+/", $datum['value'])) {
                $datum['value'] = '';
                $datum['field_type'] = (string)CustomFieldService::FIELD_TYPE_TEXT;
            }
        }

        if (is_array($datum['value'])) {
            if ($this->canImplodeArray($datum['value'])) {
                $datum['value'] = implode(',', $datum['value']);
            } else {
                $datum['value'] = "";
            }
        }

        if (is_numeric($datum['value'])) {
            $datum['value'] = (string)$datum['value'];
        }
    }

    function canImplodeArray($array): bool
    {
        // 检查数组的维度
        if (!is_array($array)) {
            return false;
        }
        // 检查数组元素的类型
        foreach ($array as $element) {
            if (is_array($element)) {
                return false;
            }
        }

        return true;
    }

    //处理没有值时候
    private function setDefaultValue($key)
    {
        $group = FieldExportService::ORDER_GROUP_PRODUCT;
        if ($key == 'product_list') {
            $group = FieldExportService::ORDER_GROUP_PRODUCT;
            if (in_array($this->invoiceType, [
                \Constants::TYPE_SHIPPING_INVOICE,
                \Constants::TYPE_PAYMENT_INVOICE,
            ])) {
                return [];
            }
        } else {
            $group = FieldExportService::ORDER_GROUP_ADDITION_FEE;
            if ($this->invoiceType == \Constants::TYPE_SHIPPING_INVOICE) {
                $group = FieldExportService::SHIPPING_INVOICE_GROUP_PRODUCT_ADDITION_FEE;
            }
        }
        //拿到不同的key 设置字典
        $service = new FieldExportService($this->clientId, $this->invoiceType);
        $exportFields = $service->generateDictionaryByGroups($group);

        if (empty($exportFields)) {
            return [];
        }

        $res = [];
        foreach ($exportFields as $field) {
            if (!isset($field['code'])) {
                continue;
            }
            $res[$field['code']] = [
                'id' => $field['id'],
                'field_type' => strval(CustomFieldService::FIELD_TYPE_TEXT),
                'relation_field' => $field['relation_field'] ?? '',
                'relation_field_type' => $field['relation_field_type'] ?? '',
                'value' => '',
            ];
        }
        //值设置为空
        return [$res];
    }

    protected function isMultiData($data)
    {
        if (isset($data['id']))
            return false;

        if (is_array($data)) {
            foreach ($data as $item) {
                if (isset($item['id']))
                    return true;
            }
        }
        return false;
    }

    protected function loadFieldPrivilege()
    {
        $objectName = ObjConstant::OBJ_MAP[$this->invoiceType] ?? null;
        if (empty($objectName)) {
            return;
        }
        $functionalId = ObjConstant::FUNCTIONAL_MAP[$objectName] ?? null;
        if (empty($functionalId) || !isset(PrivilegeConstants::FIELD_PRIVILEGE_FUNCTIONAL_MAP[$functionalId])) {
            return;
        }

        $metadataClass = ObjConstant::OBJ_METADATA_MAP[$objectName];

        $singleObject = new ($metadataClass::singeObject())($this->clientId);
        $singleObject->load([$metadataClass::objectIdKey() => $this->invoiceId]);
        if ($singleObject->isNew()) {
            return;
        }
        $scopeUserIdsField = \common\library\object\field\FieldConstant::SCOPE_USER_IDS_FIELD_NAME;
        $scopeUserIds = $singleObject->{$scopeUserIdsField};

        $this->fieldPrivileges = \common\library\privilege_v3\privilege_field\Helper::getToScopeUserFieldPrivileges($this->clientId, $this->userId, $scopeUserIds, $functionalId, PrivilegeFieldV2::SCENE_OF_EXPORT, $this->invoiceType);
    }
}
