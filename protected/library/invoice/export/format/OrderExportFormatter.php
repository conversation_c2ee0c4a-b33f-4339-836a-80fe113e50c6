<?php
/**
 * Created by PhpStorm.
 * User: beenzhang
 * Date: 2020/6/17
 * Time: 4:42 PM
 */

namespace common\library\invoice\export\format;

use common\library\account\Client;
use common\library\alibaba\store\AlibabaStoreAccountSyncHelper;
use common\library\alibaba\store\AlibabaStoreService;
use common\library\cash_collection\CashCollection;
use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldList;
use common\library\customer_v3\company\list\CompanyList;
use common\library\customer_v3\customer\CustomerList;
use common\library\department\DepartmentService;
use common\library\exchange_rate\ExchangeRateService;
use common\library\group\Helper;
use common\library\invoice\status\InvoiceStatusService;
use common\library\object\extend\ExtendMetadata;
use common\library\oms\common\OmsConstant;
use common\library\oms\invoice_export\InvoiceExportFieldFormatter;
use common\library\opportunity\OpportunityList;
use common\library\platform_product\sku\PlatformSkuAPI;
use common\library\product_v2\ProductConstant;
use common\library\product_v2\ProductHelper;
use common\library\product_v2\sku\SkuAPI;
use common\library\setting\library\origin\OriginApi;
use common\library\util\PgsqlUtil;

class OrderExportFormatter extends \ListItemFormatter
{
    use InvoiceExportFieldFormatter;
    protected $clientId;
    protected $opUserId;
    protected $moduleType;

    protected $fieldMap;
    protected $attributesMap;
    protected $mainCurrency;
    protected $primaryKey;

    // 导出字段，特殊处理，外部不会用到
    protected $specialFeilds = [
        'platform_product_info', 'product_images',
    ];


    protected $additionalData = [
        'cash_collection_info.collect_amount_rmb' => 0,
        'cash_collection_info.collect_amount_usd' => 0,
        'cash_collection_info.not_collect_amount_rmb' => 0,
        'cash_collection_info.not_collect_amount_usd' => 0,
        'cash_collection_percentage' => 0,
        'cost_with_tax_total_cny' => 0,
        'cost_with_tax_total_usd' => 0,
    ];

    public function __construct($clientId, $userId)
    {
        $this->clientId = $clientId;
        $this->opUserId = $userId;
        $this->init();
    }

    protected function init()
    {
        $this->moduleType = \Constants::TYPE_ORDER;
        $this->fieldMap = $this->getFieldMap();
        $this->primaryKey = 'order_id';
    }

    public function getListDataFieldMap(){
        return \common\library\invoice\Helper::getOrderListDataFieldMap();
    }

    protected function processExternalField($externalData)
    {
        $fieldMap = $this->getFieldMap();
        foreach ($externalData as $field => &$value)
        {
            $fieldInfo = $fieldMap[$field] ?? [];
            $fieldType = intval($fieldInfo['field_type']??0);

            //关联company字段 and 系统字段
            if ($fieldType == CustomFieldService::FIELD_TYPE_FIELDS && !is_numeric($fieldInfo['relation_field']))
            {
                $value = $this->processRelationCompanyField($fieldInfo['relation_field'], $value);
                continue;
            }

            $value = $this->processByFieldType($field, $value);
        }

        return $externalData;
    }

    protected function processRelationCompanyField($companyField, $value)
    {
        switch ($companyField)
        {
            case 'tel':
                $value = array_filter([$value['tel_area_code']??'',$value['tel']??'']);
                $value = implode(' ', $value);
                break;
            case 'group_id':
                $value = $this->getMapData('company:group_names', $value) ?? \Yii::t('field', '未分组');
                break;
            case 'origin':
            case 'origin_list':
                $value = (array)$value;
                $valueArr = [];
                foreach ($value as $item) {
                    $valueArr[] = $this->getMapData('company:origin_names', $item) ?? '';
                }
                $value = implode(', ', array_filter($valueArr));
                break;
            case 'trail_status':
                $value = $this->getMapData('company:trail_status', $value) ?? '';
                break;
            case 'category_ids':
                $categoryNames = [];
                foreach ($value?:[] as $item)
                {
                    $names = [];
                    foreach ($item as $elem)
                    {
                        $name = $this->getMapData('company:category_names', $elem) ?? '';
                        !empty($name) && $names[] = $name;
                    }
                    if (!empty($names))
                    {
                        $categoryNames[] = implode('-', $names);
                    }
                }

                $value = implode(';', $categoryNames);
                break;
            case 'scale_id':
                $value = $this->getMapData('company:scale', $value) ?? '';
                break;
            case 'timezone':
                $value = \Yii::t('customer', \CustomerOptionService::TIMEZONE_MAP[$value]??'未知');
                break;
            case 'product_group_ids':
                $productGroupNames = [];
                if (is_array($value)) {
                    foreach ($value as $valueId) {
                        $productGroupNames[] = $this->getMapData('company:product_group_ids', $valueId) ?? '';
                    }
                }
                $value = implode(';', array_filter($productGroupNames));
                break;
            case 'annual_procurement':
                $value = $this->getMapData('company:annual_procurement', $value);
                empty($value) && $value = '';
                break;
            case 'intention_level':
                $value = $this->getMapData('company:intention_level', $value);
                empty($value) && $value = '';
                break;
        }

        $value = $this->processString($value);
        return $value;
    }

    public function format($data)
    {
        $data = array_merge($data, $this->additionalData);

        $this->attributesMap = ProductHelper::getSkuAttributesMap($this->clientId, $data['product_list'] ?? [], 'sku_attributes', 'attrs');

        $cnyRate = $data['exchange_rate'];
        $usdRate = $data['exchange_rate_usd'];

        foreach ($data as $field => &$value)
        {
            if ($field == 'external_field_data')
            {
                $value = $this->processExternalField($value);

                continue;
            }

            if ($field == 'product_list')
            {
                $value = $this->processProduct($value);
                continue;
            }

            $value = $this->processByField($field, $value, $data, $cnyRate, $usdRate);
        }
        unset($value);
        return $data;
    }

    public function strip($data)
    {
        $data['user_id'] = PgsqlUtil::trimArray($data['user_id'] ?? '{}');
        $data['users'] = json_decode($data['users'] ?? '{}', true);
        $data['departments'] = json_decode($data['departments'] ?? '{}', true);
        $data['handler'] = PgsqlUtil::trimArray($data['handler'] ?? '{}');
        $data['external_field_data'] = json_decode($data['external_field_data'] ?? '{}', true);
        $data['product_list'] = json_decode($data['product_list'] ?? '{}', true);
        $data['cost_list'] = json_decode($data['cost_list'] ?? '{}', true);
        $data['file_list'] = json_decode($data['file_list'] ?? '{}', true);
        $data['delete_time'] = $data['delete_time'] != '1970-01-01 08:00:00' ? $data['delete_time'] : '';
        $data['account_date'] = $data['account_date'] != '1970-01-01 08:00:00' ? $data['account_date'] : '';

        return $data;
    }

    public function buildMapData()
    {
        $data = $this->data;

        if (empty($data))
            return ;

        $userIds = [];
        $statusIds = [];
        $departmentIds = [];
        $opportunityIds = [];
        $companyIds = [];
        $customerIds = [];
        $externalFieldData = [];
        $productList = [];
        $imageIds = [];
        $orderId = 0;
        $sellerAccountId = 0;
        $countryCode = "";
        $aliStoreId = 0;

        $fieldMap = $this->getFieldMap();
        foreach ($data as $field => $value)
        {
            $fieldInfo = $fieldMap[$field] ?? [];
            $fieldType = intval(($fieldInfo['field_type']?? 0));

            switch ($field)
            {
                case 'create_user':
                    $userIds[] = $value;
                    break;
                case 'status':
                    $statusIds[] = $value;
                    break;
                case 'users':
                    if (!is_array($value)) {
                        $value = json_decode($value, true) ?? [];
                    }
                    $userIds = array_merge($userIds, array_column($value, 'user_id'));
                    break;
                case 'departments':
                    $value = is_array($value) ? $value : json_decode($value, true) ?? [];
                    $departmentIds = array_merge($departmentIds, array_column($value, 'department_id'));
                    break;
                case 'handler':
                    if (!is_array($value)) {
                        $value = PgsqlUtil::trimArray($value);
                        $value = $value ?: [];
                    }
                    $userIds = array_merge($userIds,$value);
                    break;
                case 'company_id':
                    $companyIds[] = $value;
                    break;
                case 'customer_id':
                    $customerIds[] = $value;
                    break;
                case 'opportunity_id':
                    $opportunityIds[] = $value;
                    break;
                case 'external_field_data':
                    $externalFieldData = is_array($value) ? $value : json_decode($value, true);
                    break;
                case 'product_list':
                    $productList = is_array($value) ? $value : json_decode($value, true);
                    break;
                case 'order_id':
                    $orderId = $value;
                    break;
                case 'seller_account_id':
                    $sellerAccountId = $value;
                    break;
                case 'ali_store_id':
                    $aliStoreId  = $value;
                    break;
                case 'country':
                    $countryCode  = $value;
                    break;
                default :
                    if ($fieldType == CustomFieldService::FIELD_TYPE_IMAGE) {
                        $fileId = is_array($value) ? $value : [];
                        $fileId = array_column($fileId, 'file_id');
                        $imageIds = array_merge($imageIds, $fileId);
                    }
                    break;
            }
        }

        $cashCollectionMap = [];
        if ($orderId)
        {
            $cashCollectionMap = \common\library\cash_collection\Helper::getReferStatsMap($this->clientId, CashCollection::REFER_TYPE_ORDER, [$orderId]);
        }

        $sellerAccountMap = [];
        if($sellerAccountId){
            $sellerAccountMap = AlibabaStoreAccountSyncHelper::getStoreAccountInfoMap($this->clientId,[$sellerAccountId]);
        }

        $aliStoreMap = [];
        if($aliStoreId){
            $aliStoreMap = AlibabaStoreService::getStoreInfoMaps($this->clientId,[],true);
        }

        $countryMaps = [];
        if($countryCode){
            $countryList = \CountryService::getCountryListFromAlpha2([$countryCode]);
            if($countryList){
                $countryMaps = array_combine(array_column($countryList,'alpha2'),$countryList);
            }
        }

        $map = [
            'user' => $this->getUserMap($userIds),
            'status' => $this->getInvoiceStatusMap($statusIds),
            'company' =>$this->getCompanyMap($companyIds),
            'customer' => $this->getCustomerMap($customerIds),
            'opportunity' => $this->getOpportunityMap($opportunityIds),
            'department' => $this->getDepartmentMap($departmentIds),
            'cashCollection' => $cashCollectionMap,
            'country' => $countryMaps,
            'seller_account_map' =>$sellerAccountMap,
            'ali_store_map' => $aliStoreMap,
        ];

        foreach ($externalFieldData as $field => $value)
        {
            $fieldInfo = $fieldMap[$field] ?? [];
            $fieldType = intval(($fieldInfo['field_type']?? 0));
            $originType = $fieldInfo['relation_origin_field_type'] ?? 0;

            //关联字段不处理,用buildRelationCompanyData处理
            if ($fieldType == CustomFieldService::FIELD_TYPE_FIELDS)
                continue;

            if ($fieldType == CustomFieldService::FIELD_TYPE_IMAGE || $originType == CustomFieldService::FIELD_TYPE_IMAGE) {
                $fileId = is_array($value) ? $value : [];
                $fileId = array_column($fileId, 'file_id');
                $imageIds = array_merge($imageIds, $fileId);
            }
        }

        $relationCompanyFieldMap = $this->buildRelationCompanyData($externalFieldData);
        $map = array_merge($map, $relationCompanyFieldMap);

        $relationProductFieldMap = $this->buildRelationProductData($productList);
        $imageIds = array_merge($imageIds, $relationProductFieldMap['image_id'] ?? []);
        unset($relationProductFieldMap['image_id']);
        $map = array_merge($map, $relationProductFieldMap);

        $productNoFieldMap = $this->buildProductNoData($productList);
        $map = array_merge($map, $productNoFieldMap);

        $map['image'] = $this->getFileMap($imageIds);

        $this->setMapData($map);
        parent::buildMapData();
    }


    protected function buildRelationCompanyData($externalData)
    {
        $map = [];

        $fieldMap = $this->getFieldMap();
        $groupIds = [];
        $categoryIds = [];
        $trailStatus = [];
        $originIds = [];
        $productGroupIds = [];
        $annualProcurements = [];
        $intentionLevels = [];

        foreach ($externalData as $field => $value)
        {
            $fieldInfo = $fieldMap[$field] ?? [];
            $fieldType = intval(($fieldInfo['field_type']?? 0));

            //非关联字段不处理
            if ($fieldType != CustomFieldService::FIELD_TYPE_FIELDS)
                continue;

            switch ($fieldInfo['relation_field'])
            {
                case 'group_id':
                    $groupIds[] = $value;
                    break;
                case 'category_ids':
                    foreach ($value?:[] as $ids) {
                        $categoryIds = array_merge($categoryIds, $ids);
                    }
                    break;
                case 'trail_status':
                    $trailStatus[] = $value;
                    break;
                case 'origin':
                case 'origin_list':
                    $originIds = array_merge($originIds, (array)$value);
                    break;
                case 'product_group_ids':
                    $productGroupIds = $value;
                    break;
                case 'annual_procurement':
                    $annualProcurements[] = $value;
                    break;
                case 'intention_level':
                    $intentionLevels[] = $value;
                    break;
            }
        }


        if (!empty($groupIds))
            $map['company:group_names'] = Helper::getGroupNameMap($this->clientId, \Constants::TYPE_COMPANY, $groupIds);

        if (!empty($originIds))
            $map['company:origin_names'] = \CustomerOptionService::getOriginNameMap($this->clientId, $originIds);

        if (!empty($trailStatus))
        {
            $trailStatusMap = \CustomerOptionService::getStatusMap($this->clientId, $trailStatus);
            $map['company:trail_status'] = array_column($trailStatusMap, 'item_name', 'item_id');
        }

        if (!empty($categoryIds))
            $map['company:category_names'] = \Category::getNameMap('zh', $categoryIds);

        if (!empty($productGroupIds)) {
            $map['company:product_group_ids'] = \common\library\group\Helper::getGroupNameMap($this->clientId, \Constants::TYPE_PRODUCT, $productGroupIds);
        }

        if (!empty($annualProcurements)) {
            $map['company:annual_procurement'] = \CustomerOptionService::annualProcurementMap();
        }

        if (!empty($intentionLevels)) {
            $map['company:intention_level'] = \CustomerOptionService::intentionLevelMap();
        }

        $map['company:scale'] = [];
        foreach (\CustomerOptionService::SCALE_MAP as $key => $item)
        {
            if ($item['min'] <=1 )
            {
                $map['company:scale'][$key] = "<{$item['max']}";
                continue;
            }

            if ($item['max'] >= 10000)
            {
                $map['company:scale'][$key] = "<{$item['min']}";
                continue;
            }

            $map['company:scale'][$key] = "{$item['min']}~{$item['max']}";
        }

        return $map;
    }

    protected function buildRelationProductData($productList)
    {
        $map = [];

        $fieldMap = $this->getFieldMap();
        $groupIds = [];

        foreach ($productList as $item)
        {
            if (!isset($item['external_field_data']) || empty($item['external_field_data']))
            {
                continue;
            }

            foreach ($item['external_field_data'] as $field => $value)
            {
                //脏数据处理
                if (!is_numeric($field))
                    continue;

                $fieldInfo = $fieldMap[$field] ?? [];
                $fieldType = intval(($fieldInfo['field_type']?? 0));
                $relationOriginType = intval(($fieldInfo['relation_origin_field_type']?? 0));
                $relationField = $fieldInfo['relation_field'] ?? '';

                if ($fieldType == CustomFieldService::FIELD_TYPE_IMAGE || $relationOriginType == CustomFieldService::FIELD_TYPE_IMAGE) {
                    if (!is_array($value))
                       continue;
                    $hasFirstFile = $value[0] ?? [];
                    $fileId       = [];
                    if (!empty($hasFirstFile['file_id'])) {
                        $fileId = array_column($value, 'file_id');
                    } else if (!empty($hasFirstFile['id'])) {
                        $fileId = array_column($value, 'id');
                    } else if (empty($hasFirstFile) && (isset($value['file_id']) || isset($value['id']))) {
                        $fileId[] = $value['file_id'] ?? ($value['id'] ?? 0);
                    }
                    $map['image_id'] = array_merge($map['image_id'] ?? [], $fileId);
                }

                switch ($relationField) {
                    case 'group_id':
                        $groupIds[] = $value;
                        break;
                }
            }
        }

        if (!empty($groupIds))
        {
            $map['product:group_names'] = Helper::getGroupNameMap($this->clientId, \Constants::TYPE_PRODUCT, $groupIds);
        }

        return $map;
    }

    protected function buildProductNoData($productList)
    {
        $map = [];

        $skuIds = array_column($productList, 'sku_id');
        $skuApi = new SkuAPI($this->clientId, $this->opUserId);
        $productSkuMap = array_column($skuApi->items($skuIds), null, 'sku_id');

        $platformSkuIds = [];
        foreach ($productList as $item) {
            array_push($platformSkuIds, $item['platform_product_info']['platform_sku_id'] ?? 0);
        }

        $platformProductSkuMap = [];
        $platformSkuIds = array_filter(array_unique($platformSkuIds));
        if (!empty($platformSkuIds)) {
            $platSkuApi = new PlatformSkuAPI($this->clientId);
            $platformProductSkuMap = array_column($platSkuApi->productSkusByIds($platformSkuIds, true), null,'platform_sku_id');
        }

        $productNoMap = [];
        foreach ($productList as $product)
        {
            $productSku = $productSkuMap[$product['sku_id'] ?? 0] ?? [];
            $platformProductSku = $platformProductSkuMap[$product['platform_product_info']['platform_sku_id'] ?? 0] ?? [];
            $isPlatformProduct = !empty($product['platform_product_info']['platform_product_id']);
            if ($isPlatformProduct) {
                $skuCode = '';
                if (!empty($product['platform_product_info']['platform_product_id']) && !empty($productSku['product_type'])) {
                    if ($productSku['product_type'] == ProductConstant::PRODUCT_TYPE_SPU) {
                        $skuCode = $productSku['product_no'] ?? '';
                    } else {
                        $skuCode = $productSku['sku_code'] ?? '';
                    }
                }
                $productNo = $skuCode;
            }else{
                $productNo = $platformProductSku['third_sku_code'] ?? $productSku['sku_code'] ?? '';
            }
            $productNoMap[$product['unique_id']] = $productNo;
        }

        if (!empty($productNoMap))
        {
            $map['product:product_no'] = $productNoMap;
        }

        return $map;
    }

    protected function getCompanyMap(array $companyIds = [])
    {
        $companyMap = [];
        $companyIds = array_filter($companyIds);
        if (!empty($companyIds))
        {
            $companyIds = array_values($companyIds);
            $list = new CompanyList($this->opUserId);
            $list->setSkipPrivilege(true);
            $list->setCompanyIds($companyIds);
            $list->setFields(['company_id', 'name']);
            $companyList = $list->find();

            foreach ($companyList as $elem) {
                $companyMap[$elem['company_id']] = $elem['name'];
            }
        }

        return $companyMap;
    }

    protected function getCustomerMap(array $customerIds = [])
    {
        $customerMap = [];
        if (empty($customerIds))
            return [];

        $customerIds = array_filter($customerIds);
        if (!empty($customerIds)) {
            $customerIds = array_values($customerIds);

            $list = new CustomerList($this->clientId);
            $list->setCustomerId($customerIds);
            $list->setFields(['customer_id', 'name', 'email']);
            $customerList = $list->find();

            foreach ($customerList as $elem) {

                if (empty($elem['name'])) $elem['name'] = $elem['email'];
                $customerMap[$elem['customer_id']] = $elem;
            }
        }

        return $customerMap;
    }

    protected function getDepartmentMap(array $departmentIds = [])
    {
        $departmentMap = [];
        $departmentIds = array_filter($departmentIds);
        if (!empty($departmentIds)) {
            $departmentService = new DepartmentService($this->clientId);
            $departmentMap = $departmentService->batchGetDepartmentListForIds($departmentIds);
        }
        return $departmentMap;
    }

    protected function getUserMap(array $userIds = [])
    {
        $userMap = [];
        $userIds = array_filter($userIds);
        if( !empty($userIds) )
        {
            $userList =  \common\library\account\Helper::getBatchUserInfo($this->clientId,$userIds);
            $userList =array_map(function ($elem){
                return ['user_id'=>$elem->user_id,'nickname'=>$elem->nickname,'avatar'=>$elem->avatar,'email'=>$elem->email];
            },$userList);

            $userMap = array_combine(array_column($userList,'user_id'),$userList);
        }
        return $userMap;
    }

    protected function getOpportunityMap(array $opportunityIds = [])
    {
        $opportunityMap = [];
        $opportunityIds = array_filter($opportunityIds);
        if (!empty($opportunityIds))
        {
            $opportunityIds = array_values($opportunityIds);
            $list = new OpportunityList($this->clientId);
            $list->setViewingUserId($this->opUserId);
            $list->setUserId(0);
            $list->setSkipPermissionCheck(true);
            $list->setOpportunityIds($opportunityIds);
            $list->setFields(['opportunity_id', 'name']);
            $opportunityList = $list->find();

            foreach ($opportunityList as $elem) {
                $opportunityMap[$elem['opportunity_id']] = $elem;
            }
        }

        return $opportunityMap;
    }

    public function getOriginMap(array $originIds = [])
    {
        $originMap = [];
        $originIds = array_filter($originIds);
        $api = new OriginApi($this->clientId);
        if (!empty($originIds))
        {
            $findOriginIds = [];
            foreach ($originIds as $originId)
            {
                //系统来源
                if (isset($api->getExtraDataMap()[$originId]))
                {
                    $originMap[$originId] = \Yii::t('customer', $api->getExtraDataMap()[$originId]);
                    continue;
                }
                $findOriginIds[] = $originId;
            }

            if ($findOriginIds)
            {
                /*
                $originList = new OriginList($this->clientId);
                $originList->setItemId($findOriginIds);
                $originList = $originList->find();
                $originList = array_column($originList,'item_name','item_id');
                */

                $originList = $api->getNameMap($findOriginIds);

                $originMap = $originMap+$originList;
            }
        }
        return $originMap;
    }

    protected function getInvoiceStatusMap(array $statusIds = [])
    {
        $statusMap = [];
        $statusIds = array_filter($statusIds);
        if (!empty($statusIds)) {
            $statusList = (new InvoiceStatusService($this->clientId, $this->moduleType))->baseInfoList(false, $statusIds, false);
            $statusMap = array_column($statusList, 'name', 'id');
        }

        return $statusMap;
    }

    public function getImageUrlMap(array $fileIds = [])
    {
        $fileIds = array_values(array_unique(array_filter($fileIds)));
        if (empty($fileIds))
            return [];

        $map = [];
        $objArray = \UploadFile::findByIds($fileIds);
        foreach ($objArray as $obj) {
            $upload = new \AliyunUpload();
            $upload->loadByObject($obj);

            $map[$upload->getFileId()] = $upload->getFileUrl();
        }
        return $map;
    }

    protected function getFieldMap($refresh = false)
    {
        if ($this->fieldMap === null || $refresh == true)
        {
            $customFieldList = new FieldList($this->clientId);
            $customFieldList->setType($this->moduleType);
            $customFieldList->setEnableFlag(null);
            $customFieldList->setFields(['id', 'name', 'field_type', 'relation_field', 'relation_field_type', 'relation_origin_type','relation_origin_field','relation_origin_field_type']);
//            $customFieldList->getFormatter()->setShowRelationFieldInfo(true);
            $fieldMapList = $customFieldList->find();
            $fieldMap = array_column($fieldMapList, null, 'id');

            if (isset($fieldMap['customer_emial'])) {
                $fieldMap['customer_email'] = $fieldMap['customer_emial'];
                $fieldMap['customer_email']['id'] = 'customer_email';
            }

            $this->fieldMap = $fieldMap;
        }

        return $this->fieldMap;
    }

    public function getFileMap(array $fileIds = [])
    {
        $fileIds = array_values(array_unique(array_filter($fileIds)));
        if (empty($fileIds))
            return [];

        $map = [];
        $objArray = \UploadFile::findByIds($fileIds);
        foreach ($objArray as $obj) {
            $upload = new \AliyunUpload();
            $upload->loadByObject($obj);

            $map[$upload->getFileId()] = [
                'file_name' => $upload->getFileName(),
                'file_size' => $upload->getFileSize(),
                'file_url' => $upload->getFileUrl(),
                'preview_url' => $upload->getPreview(),
                'file_key' => $upload->getFileKey(),
            ];
        }
        return $map;
    }

    private function processString($str)
    {
        if (is_string($str))
        {
            $str = \Util::replaceBrToLinebreak($str);
            $str = \Util::unEmoji($str);
        }

        return $str;
    }

    protected function processProduct(array $productList)
    {
        $extendFields = (new ExtendMetadata())->columnFields();
        foreach ($productList as &$product)
        {
            $product['product_no'] = $this->getMapData('product:product_no', $product['unique_id']) ?? '';

            if (isset($product['["package_volume_subtotal"]']))
            {
                $product['package_volume_subtotal'] = $product['["package_volume_subtotal"]'];
                unset($product['["package_volume_subtotal"]']);
            }

            if (isset($product['["package_gross_weight_subtotal"]']))
            {
                $product['package_gross_weight_subtotal'] = $product['["package_gross_weight_subtotal"]'];
                unset($product['["package_gross_weight_subtotal"]']);
            }

            if (isset($product['sku_attributes'])) {
                $skuAttr = [];
                foreach ($product['sku_attributes']?:[] as $attrName => $attrVals) {
                    if($this->attributesMap && isset($this->attributesMap[$attrName])){
                        $attrRealName = $this->attributesMap[$attrName]['item_name'];
                        $attrRealVals = [];
                        $attrVals = is_array($attrVals) ? ($attrVals['attrs'] ?? []) : '';
                        foreach($attrVals as $attrVal){
                            $attrRealVals[] = $this->attributesMap[$attrVal]['item_name'];
                        }
                        $skuAttr[] = "{$attrRealName}:". implode(',', $attrRealVals);
                    }else{
                        if (is_array($attrVals)) {
                            $attrVals = is_array($attrVals) ? ($attrVals['attrs'] ?? []) : [];
                            $attrVals = implode(',', $attrVals);
                        }
                        $skuAttr[] = "{$attrName}:{$attrVals}";
                    }
                }
                $product['sku_attributes'] = implode(";\n", $skuAttr);
            }

            foreach ($product as $field => &$value)
            {
                if ($field == 'external_field_data')
                {
                    foreach ($value as $productField => &$item) {
                        $item = $this->processProductField($productField, $item);
                    }
                    continue;
                }

                //以防万一，过滤基建写入的宽表字段
                if (in_array($field, $extendFields)) {
                    unset($product[$field]);
                }

                $value = $this->processProductField($field, $value);
            }

            unset($item);
            unset($value);
        }
        unset($product);

        return $productList;
    }

    protected function processByFieldType($field, $value)
    {
        $fieldMap = $this->getFieldMap();
        $fieldInfo = $fieldMap[$field] ?? [];
        $fieldType = intval($fieldInfo['field_type'] ?? 0);

        if(!in_array($fieldType,[CustomFieldService::FIELD_TYPE_FIELDS, CustomFieldService::FIELD_TYPE_QUOTE_FIELDS])){
            if (in_array($field, $this->specialFeilds)) {
                $value = '';
            } else {
                $value = $this->processByFieldTypeInner($fieldType,$value);
            }
        }else{
            $originFieldType = $fieldInfo['relation_origin_field_type'] ?? 0;
            $value = $this->processByFieldTypeInner($originFieldType,$value);
        }

        return $this->processString($value);
    }

    protected function processByFieldTypeInner($fieldType, $value)
    {
        switch ($fieldType)
        {
            case CustomFieldService::FIELD_TYPE_IMAGE:
                if (is_array($value))
                {
                    foreach ($value as $index => $file) {
                        $value = $this->getMapData('image', $file['file_id']??0)['file_url'] ?? '';
                        break;
                    }
                }
                break;
            case CustomFieldService::FIELD_TYPE_ATTACH:
                //附件不支持
                $value = '';
                break;
            case CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT:
                //$value = is_array($value) ? implode(';', $value) : $value;
                $value = $this->valueToStringNoThrowable($value);
                break;
            default:
                // $value = is_array($value) ? implode(';', $value) : $value;
                $value = $this->valueToStringNoThrowable($value);
                break;
        }

        return $value;
    }

    protected function valueToStringNoThrowable($value)
    {
        try {
            if (is_array($value)) {
                if (count($value, 1) == count($value)) {
                    $value = @implode(';', $value);
                } else {
                    $value = '';
                }
            }
        } catch (\Throwable $t) {
            // 各种异常无法兼容那么多，直接报异常的先处理成空字符串
            $value = '';
        }
        return $value;
    }

    protected function processByField($field, $value, $data, $cnyRate, $usdRate)
    {
        switch ($field)
        {
            case 'company_id':
                $value = $this->getMapData('company', $value) ?? '';
                break;
            case 'opportunity_id':
                $value = $this->getMapData('opportunity', $value)['name'] ?? '';
                break;
            case 'status':
                $value = $this->getMapData('status', $value)??'';
                break;
            case 'customer_id':
                $value = $this->getMapData('customer', $value)['name'] ?? '';
                break;
            case 'users':
                $names = [];
                if (is_array($value)) {
                    foreach ($value as $handler) {
                        $name = $this->getMapData('user', $handler['user_id'])['nickname']??'';
                        $names[] = "{$name} ({$handler['rate']}%)";
                    }
                    $value = implode(';', $names);
                }
                break;

            case 'user_id':
            case 'handler':
                $names = [];
                //报价单回滚兼容
                if (!is_array($value)){
                    $value = [];
                }
                foreach ($value as $userId) {
                    $names[] = $this->getMapData('user', $userId)['nickname']??'';
                }
                $value = implode(';', $names);
                break;

            case 'create_user':
                $value = $this->getMapData('user', $value)['nickname']??'';
                break;

            case 'departments':
                $names = [];
                if (is_array($value)) {
                    foreach ($value as $department) {
                        $name = ($department['name'] ?? '') ?: $this->getMapData('department', $department['department_id'])['name']??'我的企业';
                        $names[] = "{$name} ({$department['rate']}%)";
                    }
                    $value = implode(';', $names);
                }
                break;
            case 'create_time':
            case 'update_time':
            case 'account_date':
                $formatValue = date('Y-m-d', strtotime($value));
                $value = $formatValue == '1970-01-01' ? '' : $formatValue;
                break;
//            case 'exchange_rate':
//                //导出时，该字段定义为订单的汇率字段，不再是cny汇率字段
//                $currency = $this->getMainCurrency();
//                if ($currency == ExchangeRateService::USD) {
//                    $value = $data['exchange_rate_usd'];
//                }
//                $value = sprintf('%.2f',$value);
//                break;
            case 'cost_list':
                break;
            case 'file_list':
                break;
            case 'ali_status_id':
            case 'ali_status_name' :
                $value =  $data['ali_status_name'] ? \Yii::t('invoice', $data['ali_status_name']):"";
                break;
            case 'ali_store_id':
                $value = $this->getMapData('ali_store_map', $value)['store_name']??'';
                break;
            case 'source_type':
                $value = \common\library\invoice\Helper::getOrderSourceTypeMap()[$value]??'';
                break;
            case 'seller_account_id':
                $value = $this->getMapData('seller_account_map', $value)['seller_email']??'';
                break;
            case 'country':
                $countryData = $this->getMapData('country', $value)??'';
                if($countryData){
                    // $value = $countryData['country_ename']."({$countryData['country_name']})";
                    // https://www.tapd.cn/********/prong/stories/view/11********001065881
                    $value = $countryData['country_ename'];
                }else{
                    $value = "";
                }
                break;
            case 'archive_type':
                $archiveTypeMaps = \common\library\invoice\Helper::getOrderArchiveTypeMaps();
                $value = isset($archiveTypeMaps[$value]) && isset($archiveTypeMaps[$value]['name'])?$archiveTypeMaps[$value]['name']:'';
                break;
            case 'last_sync_time':
                $value =  isset($data['last_sync_time']) && date('Y-m-d',strtotime($data['last_sync_time'])) != '1970-01-01' ? $data['last_sync_time'] : '';
                break;
            case 'cash_collection_info.collect_amount_rmb':
                $value = $this->getMapData('cashCollection', $data[$this->primaryKey])['collect_amount_rmb']??'';
                break;
            case 'cash_collection_info.collect_amount_usd':
                $value = $this->getMapData('cashCollection', $data[$this->primaryKey])['collect_amount_usd']??'';
                break;
            case 'cash_collection_info.not_collect_amount_rmb':
                $value = $this->getMapData('cashCollection', $data[$this->primaryKey])['not_collect_amount_rmb']??'';
                break;
            case 'cash_collection_info.not_collect_amount_usd':
                $value = $this->getMapData('cashCollection', $data[$this->primaryKey])['not_collect_amount_usd']??'';
                break;
            case 'cash_collection_percentage':
                //根据币种获取对应币种的比例
                $cashCollectionInfo = $this->getMapData('cashCollection', $data[$this->primaryKey]);
                $value = strval($this->getMainCurrency() == ExchangeRateService::USD ? ($cashCollectionInfo['percentage_usd'] ?? 0) : ($cashCollectionInfo['percentage_rmb'] ?? 0)) . "%";
                break;
            case 'cost_with_tax_total_cny':
                $value = isset($data['cost_with_tax_total']) ? round($data['cost_with_tax_total'] * ($cnyRate  / 100), 4) : 0;
                break;
            case 'cost_with_tax_total_usd':
                $value = isset($data['cost_with_tax_total']) ? round($data['cost_with_tax_total'] * ($usdRate / 100), 4) : 0;
                break;
            case 'tax_refund_type':
                $value = isset($data['tax_refund_type']) ? OmsConstant::ORDER_TAX_REFUND_TYPE_MAP[$data['tax_refund_type']] ?? "" : "";
                break;
            case 'transport_mode':
                $value = OmsConstant::SHIPMENT_METHOD_MAP[$value] ?? $value;
                break;
            case 'ali_order_id':
                if (!empty($data['transfer_ali_order_type'])) {
                    $value = implode(",", $data['transfer_ali_order_id']);
                } else {
                    $value = empty($data['ali_order_id']) ? "" : $data['ali_order_id'];
                }
                break;
            default:
                $value = $this->processByFieldType($field, $value);
                break;
        }

        $value = $this->processString($value);
        return $value;
    }

    protected function processRelationProductField($field, $value)
    {
        switch ($field)
        {
            case 'group_id':
                $value = $this->getMapData('product:group_names', $value);
                break;
            case 'fob':
                $data = [
                    $value['price_currency']??'',
                    $value['price_min']??'',
                    '-',
                    $value['price_max']??'',
                    $value['price_unit']??'',
                ];
                $value = implode(' ', $data);
                break;

            case 'minimum_order_quantity':
                if (is_array($value))
                {
                    $data = [$value['quantity']??'',$value['quantity_unit']??''];
                    $value = implode(' ', $data);
                }
                break;

            case 'cost_with_tax':
                $data = [
                    'cost_currency' => $value['cost_currency'] ?? '',
                    'cost' => $value['cost'] ?? '',
                ];
                $value = implode(' ', $data);
                break;

            case 'package_size':
                $value = $this->formatPackageSizeField($value);
                break;
            case 'product_size':
                $value = $this->formatProductSizeField($value);
                break;
            case 'carton_size':
                $value = $this->formatCartonSizeField($value);
                break;

            case 'gross_profit_margin':
                $value .= '%';
                break;

            case 'category_ids':
                $value = $this->getProductCategoryString($value);
                break;
        }

        $value = $this->processString($value);
        return $value;
    }

    protected function processProductField($field, $value)
    {
        $fieldInfo = $this->getFieldMap()[$field] ?? [];
        $fieldType = intval($fieldInfo['field_type'] ?? 0);

        //关联字段
        if (
            $fieldType == CustomFieldService::FIELD_TYPE_FIELDS
            ||
            ($fieldType == CustomFieldService::FIELD_TYPE_QUOTE_FIELDS && ($fieldInfo['relation_field_type']??0) == CustomFieldService::FIELD_TYPE_IMAGE)
        )
        {
            $field = $fieldInfo['relation_field'] ?? $field;
            $fieldType = $fieldInfo['relation_field_type']??0;
            switch ($fieldType)
            {
                case CustomFieldService::FIELD_TYPE_IMAGE:
                    if (is_array($value))
                    {
                        foreach ($value as $index => $file) {
                            if (!empty($file['file_id'])) {
                                $value = $this->getMapData('image', $file['file_id'])['file_url'] ?? '';
                            } elseif (!empty($file['id'])) {
                                $value = $this->getMapData('image', $file['id'])['file_url'] ?? '';
                            }
                            break;
                        }
                    }
                    break;
                case CustomFieldService::FIELD_TYPE_ATTACH:
                    //附件不支持
                    $value = '';
                    break;
                case CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT:
                    $value = is_array($value) ? implode(';', $value) : $value;
                    break;
                default:
                    $value = $this->processRelationProductField($field, $value);
                    break;
            }

            return $value;
        }

        switch ($field)
        {
            case 'group_id':
                $value = $this->getMapData('product:group_names', $value);
                break;
            case 'fob':
                $data = [
                    $value['price_currency']??'',
                    $value['price_min']??'',
                    '-',
                    $value['price_max']??'',
                    $value['price_unit']??'',
                ];
                $value = implode(' ', $data);
                break;

            case 'minimum_order_quantity':
                //此处包含扩展字段中minimum_order_quantity处理
                $data = [$value['quantity']??'',$value['quantity_unit']??''];
                $data = is_numeric($value) ? [$value] : $data;
                $value = implode(' ', $data);
                break;

            case 'package_size':
                $value = $this->formatPackageSizeField($value);
                break;

            case 'gross_profit_margin':
                $value .= '%';
                break;
            case 'platform_product_info':
                break;
            default:
                $value = $this->processByFieldType($field, $value);
                break;
        }

        $value = $this->processString($value);
        return $value;
    }

    protected function getMainCurrency()
    {
        if ($this->mainCurrency === null)
        {
            $this->mainCurrency = Client::getClient($this->clientId)->getMainCurrency();
        }
        return $this->mainCurrency;
    }

    static $category;
    protected function getProductCategoryString($categoryArray)
    {
        if(!is_array($categoryArray))
            return $categoryArray;

        $categoryArray = $categoryArray ? $categoryArray : [];

        $findCategoryIds = [];
        foreach ($categoryArray as $ids) {
            $ids = is_array($ids) ? $ids : [$ids];
            foreach ($ids as $id) {
                if(!isset(self::$category[$id])){
                    $findCategoryIds[] = $id;
                }
            }
        }

        if($findCategoryIds){
            $list = \Category::findAllByIds($findCategoryIds);
            foreach ($list as $item) {
                self::$category[$item->id] = "{$item->cn_name}（{$item->en_name}）";
            }
        }

        $insertValue = [];
        foreach ($categoryArray as $ids)
        {
            $ids = is_array($ids) ? $ids : [$ids];
            foreach ($ids as $id) {
                $insertValue[] = isset(self::$category[$id]) ? self::$category[$id] : '';
            }
        }

        $insertString = implode('—', $insertValue);
        return $insertString;
    }

}
