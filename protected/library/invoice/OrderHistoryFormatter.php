<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 17/4/17
 * Time: 上午10:20
 */

namespace common\library\invoice;
use common\components\BaseObject;
use common\library\ai\classify\ai_field_data\OpportunityAIFieldData;
use common\library\alibaba\Constant;
use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldList;
use common\library\customer_v3\company\list\CompanyList;
use common\library\customer_v3\customer\CustomerList;
use common\library\department\DepartmentService;
use common\library\erp_service\chanjet\ChanjetConstant;
use common\library\erp_service\kingdee\KingDeeConstant;
use common\library\group\Helper;
use common\library\history\base\Setting;
use common\library\history\order\OrderSetting;
use common\library\invoice\status\InvoiceStatusService;
use common\library\object\field\FieldConstant;
use common\library\object\field\FieldFilter;
use common\library\oms\capital_account\CapitalAccountFilter;
use common\library\oms\common\OmsConstant;
use common\library\oms\invoice_export\InvoiceExportFieldFormatter;
use common\library\oms\order\OrderFilter;
use common\library\opportunity\OpportunityList;
use common\library\privilege_v3\object_service\RecordPrivilegeService;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeFieldV2;
use common\library\setting\library\fund\relation\CostItemInvoiceRelationApi;
use common\library\setting\library\origin\OriginApi;
use common\library\util\Arr;
use common\library\workflow\WorkflowRuleList;
use common\models\client\OrderHistory;
use Constants;

/**
 * 订单操作历史格式器
 * Class OrderHistoryFormatter
 * @package common\library\invoice
 */
class OrderHistoryFormatter extends \ListItemFormatter
{

    use InvoiceExportFieldFormatter;
    /**
     * @var integer
     */
    protected $clientId;
    protected $fieldFormatType;

    //订单产品关联特殊字段
    protected $productRelationSpecialField = [
        'group_id',
        'fob',
        'minimum_order_quantity',
        'cost_with_tax',
        'package_size',
        'product_size',
        'carton_size',
        'gross_profit_margin',
        'category_ids',
    ];
    public function __construct($clientId)
    {
        $this->clientId = $clientId;
    }

    /**
     * @param string $fieldFormatType
     */
    public function setFieldFormatType(string $fieldFormatType)
    {
        $this->fieldFormatType = $fieldFormatType;
    }

    public function buildMapData()
    {
        $list  = $this->batchFlag?$this->listData:[$this->data];
        $userIds = [];
        $departmentIds = [];
        $statusIds = [];
        $companyIds =[];
        $opportunityIds =[];
        $customerIds = [];
        $workflowIds = [];
        $companyOriginIds = [];
        $capitalAccountIds = [];
        $groupIds = [];
        $categoryIds = [];
        $companyProductGroupIds = [];

        $viewUser = \User::getLoginUser();
        $customFieldList = new FieldList($this->clientId);
        $customFieldList->setType(Constants::TYPE_ORDER);
        $customFieldList->setEnableFlag(null);
        $customFieldList->setFields(['id', 'name', 'field_type','relation_type','relation_field','relation_field_type']);
        $fieldMapList = $customFieldList->find();
        $fieldMap = array_column($fieldMapList, null, 'id');
        if (isset($fieldMap['customer_emial'])) {
            $fieldMap['customer_email'] = $fieldMap['customer_emial'];
            $fieldMap['customer_email']['id'] = 'customer_email';
        }

        $companyOriginFieldIds = array_filter($fieldMap, function ($item) {
            if ($item['relation_field'] == 'origin_list')
                return $item['id'];
        });

        $bizFieldMap = $functionFieldMap = [];
        $fieldFilter = new FieldFilter($this->clientId);
        $fieldFilter->object_name = [\common\library\object\object_define\Constant::OBJ_ORDER, \common\library\object\object_define\Constant::OBJ_ORDER_PRODUCT];
        $fieldFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $fieldFilter->function_type = FieldConstant::CALCULATE_FUNCTION_FIELD_TYPE;
        $bizFieldList = $fieldFilter->rawData();

        foreach($bizFieldList as $bizField){
            if(!isset($bizFieldMap[$bizField['object_name']])){
                $bizFieldMap[$bizField['object_name']] = [];
            }

            $bizFieldMap[$bizField['object_name']][$bizField['field']] = $bizField;
            if(in_array($bizField['function_type'], FieldConstant::CALCULATE_FUNCTION_FIELD_TYPE)){
                $functionFieldMap[$bizField['field']] = $bizField;
            }
        }

        $displayFunctionFields = $pgUpdateFormatRows=[
            \common\library\object\object_define\Constant::OBJ_ORDER => [],
            \common\library\object\object_define\Constant::OBJ_ORDER_PRODUCT => []
        ];       // 第一维key是对象名称，第二维是行数据（混杂new和old的行），第三维是字段的kv

        foreach ( $list as $elem ) {
            $userIds[] = $elem['update_user'];

            $diffList = $elem['diff']?json_decode($elem['diff'],1):[];
            if(!$diffList || !is_array($diffList)){
                continue;
            }

            // 判断 $elem 的变更字段中是否包含功能字段
            if($elem['order_product_id'] == 0){
                $objectNameKey = \common\library\object\object_define\Constant::OBJ_ORDER;
                $historyPidKey = 'order_id';
            }else{
                $objectNameKey = \common\library\object\object_define\Constant::OBJ_ORDER_PRODUCT;
                $historyPidKey = 'order_product_id';
            }
            $pidKey = \common\library\object\object_define\Constant::OBJ_PRIMARY_KEY_MAP[$objectNameKey];
            $uniqueKeyPrefix = md5($elem['order_id'].$elem['diff']);
            $newUniqueKey = $uniqueKeyPrefix.'_new';
            $oldUniqueKey = $uniqueKeyPrefix.'_old';
            $functionFieldNewLine = $functionFieldOldLine = [];

            foreach($diffList as $diff){
                $fieldId = $diff['id'];
                if(isset($functionFieldMap[$fieldId])){
                    $functionFieldNewLine[$fieldId] = $diff['new'];
                    $functionFieldOldLine[$fieldId] = $diff['old'];
                    $displayFunctionFields[$objectNameKey][]=$fieldId;
                }
            }

            if(!empty($functionFieldNewLine)){
                // 将主键id塞到line里
                $functionFieldOldLine[$pidKey] = $functionFieldNewLine[$pidKey] = $elem[$historyPidKey];
                $pgUpdateFormatRows[$objectNameKey][$newUniqueKey] = $functionFieldNewLine;
                $pgUpdateFormatRows[$objectNameKey][$oldUniqueKey] = $functionFieldOldLine;
            }

            if (in_array($elem['update_type'], [BaseObject::FIELD_EDIT_TYPE_BY_WORKFLOW, BaseObject::FIELD_EDIT_TYPE_BY_WORKFLOW_ROLLBACK]) && $elem['update_refer']) {
                $workflowIds[] = $elem['update_refer'];
            }

            //关联字段的信息map
            foreach ($diffList as $diff) {
                $fieldInfo = $fieldMap[$diff['id']] ?? [];
                $fieldRelation = $fieldInfo['relation_field'] ?? '';
                switch ($fieldRelation) {
                    case 'group_id':
                        $groupIds[] = $diff['old'];
                        $groupIds[] = $diff['new'];
                        break;
                }
            }

            switch ($elem['type']) {
                case OrderHistory::TYPE_EDIT_USER:

                    foreach ($diffList as $diff) {

                        if (!empty($diff['old'])) {
                            $userIds = array_merge($userIds, array_column($diff['old'], 'user_id'));
                        }
                        if (!empty($diff['new'])) {
                            $userIds = array_merge($userIds, array_column($diff['new'], 'user_id'));
                        }

                    }

                    break;
                case OrderHistory::TYPE_EDIT_DEPARTMENTS:
                    foreach ($diffList as $diff) {
                        if (!empty($diff['old'])) {
                            $departmentIds = array_merge($departmentIds, array_column($diff['old'], 'department_id'));
                        }
                        if (!empty($diff['new'])) {
                            $departmentIds = array_merge($departmentIds, array_column($diff['new'], 'department_id'));
                        }
                    }
                    break;
                case OrderHistory::TYPE_HANDLER:

                    foreach ($diffList as $diff) {

                        if (!empty($diff['old'])) {
                            $userIds = array_merge($userIds, $diff['old']);
                        }
                        if (!empty($diff['new'])) {
                            $userIds = array_merge($userIds, $diff['new']);
                        }

                    }

                    break;
                case OrderHistory::TYPE_STATUS:
                case OrderHistory::TYPE_ALIBABA_SYNC:

                    foreach ($diffList as $diff) {
                        if ($diff['id'] == 'status') {
                            $statusIds[] = $diff['old'];
                            $statusIds[] = $diff['new'];
                        }

                    }

                    break;
                case OrderHistory::TYPE_EDIT_ORDER:
                case \common\library\history\order\OrderSetting::TYPE_CREATE_ORDER:
                    foreach ($diffList as $diff) {

                        if( !isset($diff['id']))
                        {
                            continue;
                        }

                        if ($diff['id'] == 'company_id') {
                            $companyIds[] = $diff['old'];
                            $companyIds[] = $diff['new'];
                            continue;
                        }

                        if ($diff['id'] == 'opportunity_id') {
                            $opportunityIds[] = $diff['old'];
                            $opportunityIds[] = $diff['new'];
                            continue;
                        }

                        if ($diff['id'] == 'customer_id') {
                            $customerIds[] = $diff['old'];
                            $customerIds[] = $diff['new'];
                            continue;
                        }

                        if ($diff['id'] == 'capital_account_id') {
                            $capitalAccountIds[] = $diff['old'];
                            $capitalAccountIds[] = $diff['new'];
                            continue;
                        }

                        if (isset($companyOriginFieldIds[$diff['id']])) {
                            $companyOriginIds = array_merge($companyOriginIds, (array)$diff['old'], (array)$diff['new']);
                        }

                        //关联客户字段，组建map
                        $relationField = $fieldMap[$diff['id']]['relation_field'] ?? '';
                        if ($relationField=='category_ids') {
                            $categoryIds = array_merge($categoryIds, (array)$diff['old'], (array)$diff['new']);
                        }
                        switch ($relationField) {
                            case 'category_ids':
                                $categoryIds = array_merge($categoryIds, (array)$diff['old'], (array)$diff['new']);
                                break;
                            case 'product_group_ids':
                                $companyProductGroupIds = array_merge($companyProductGroupIds, (array)$diff['old'], (array)$diff['new']);
                                break;
                        }

                    }
                    break;
                default:
                    break;
            }
        }

        $orderIds = array_column($list, 'order_id');
        $orderFilter = new OrderFilter($this->clientId);
        $orderFilter->order_id = $orderIds;
        $orderFilter->select(['order_id', 'scope_user_ids']);
        $orderListData = $orderFilter->rawData();

        $orderToPrivilegeMap = [];
        foreach ($orderListData as $orderData){
            $orderToPrivilegeMap[$orderData['order_id']] = \common\library\privilege_v3\privilege_field\Helper::getToScopeUserFieldPrivileges($this->clientId, $viewUser->getUserId(), $orderData['scope_user_ids'], PrivilegeConstants::FUNCTIONAL_ORDER, PrivilegeFieldV2::SCENE_OF_VIEW, \Constants::TYPE_ORDER);
        }

        $userIds = array_unique(array_filter($userIds));
        $userMap = [];
        if( !empty($userIds) ){
            $userList =  \common\library\account\Helper::getBatchUserInfo($this->clientId,$userIds);
            $userList =array_map(function ($elem){
                return ['user_id'=>$elem->user_id,'nickname'=>$elem->nickname,'avatar'=>$elem->avatar,'email'=>$elem->email];
            },$userList);

            $userMap = array_combine(array_column($userList,'user_id'),$userList);
        }

        $departmentMap = [];
//        $departmentIds = array_filter($departmentIds);
        if (!empty($departmentIds)) {
            $departmentService = new DepartmentService($this->clientId);
            $departmentMap = $departmentService->batchGetDepartmentListForIds($departmentIds);
        }

        $statusMap = [];
        if( !empty($statusIds) ){
            $statusList = (new InvoiceStatusService($this->clientId,Constants::TYPE_ORDER))->baseInfoList(false,$statusIds,false);
            $statusMap = array_column($statusList,'name','id');
        }

        $companyMap = [];
        $companyIds = array_filter($companyIds);
        if( !empty($companyIds) ){
            $companyIds = array_values($companyIds);
            $userId = reset($userIds);
            $list = new CompanyList($userId);
            $list->setSkipPrivilege(true);
            $list->setCompanyIds($companyIds);
            $list->setFields(['company_id', 'name']);
            $list->setIsArchive(null);
            $companyList = $list->find();

            foreach ($companyList as $elem){
                $companyMap[$elem['company_id']] = $elem;
            }
        }

        $opportunityMap = [];
        $opportunityIds = array_filter($opportunityIds);
        if( !empty($opportunityIds) ) {
            $opportunityIds = array_values($opportunityIds);
            $userId = reset($userIds);
            $list = new OpportunityList($this->clientId);
            $list->setViewingUserId($userId);
            $list->setUserId(0);
            $list->setSkipPermissionCheck(true);
            $list->setOpportunityIds($opportunityIds);
            $list->setFields(['opportunity_id', 'name']);
            $opportunityList = $list->find();

            foreach ($opportunityList as $elem){
                $opportunityMap[$elem['opportunity_id']] = $elem;
            }
        }

        $customerMap = [];
        $customerIds = array_filter($customerIds);
        if( !empty($customerIds) ) {
            $customerIds = array_values($customerIds);

            $list = new CustomerList($this->clientId);
            $list->setCustomerId($customerIds);
            $list->setFields(['customer_id', 'name', 'email']);
            $list->setIsArchive(null);
            $customerList = $list->find();

            foreach ($customerList as $elem){

                if (empty($elem['name'])) $elem['name'] = $elem['email'];
                $customerMap[$elem['customer_id']] = $elem;
            }
        }

        $workflowMap = [];
        if (count($workflowIds)) {
            $workflowListQuery = new WorkflowRuleList($this->clientId);
            $workflowListQuery->setRuleIds(array_unique($workflowIds));
            $workflowListQuery->setEnableFlag(null);
            $workflowListQuery->setDisableFlag(null);
            $workflowListQuery->setFields(['rule_id', 'name']);
            $workflowList = $workflowListQuery->find();
            $workflowMap = array_column($workflowList, 'name', 'rule_id');
        }

        $originMap = [];
        if (count($companyOriginIds) > 0) {
            /*
            $originList = new OriginList($this->clientId);
            $originList->setItemId($companyOriginIds);
            $originList->getFormatter()->setSpecifyFields(['id', 'name']);
            $result = $originList->find();
            $originMap = array_column($result, 'name', 'id');
            */

            $api = new OriginApi($this->clientId);
            $originMap = $api->getNameMap($companyOriginIds, true);
        }

        $capitalAccountMap = [];
        $capitalAccountIds = array_unique(array_filter($capitalAccountIds));
        if (count($capitalAccountIds) > 0) {
            $capitalAccountFilter = new CapitalAccountFilter($this->clientId);
            $capitalAccountList = $capitalAccountFilter->rawData();
            $capitalAccountMap = array_column($capitalAccountList, null, 'capital_account_id');
        }

        //费用项（旧：款项信息）
        $costItemRelationList = CostItemInvoiceRelationApi::getCostItemRelationByInvoice(\Constants::TYPE_ORDER, 1);
        $costItemRelationMap = array_column($costItemRelationList, null, 'relation_id');

        $groupMap = [];
        $groupIds = array_merge($groupIds, $companyProductGroupIds);
        if (!empty($groupIds)) {
            $groupIds = Arr::uniqueFilterValues($groupIds);
            $groupMap = Helper::getGroupNameMap($this->clientId, \Constants::TYPE_PRODUCT, $groupIds);
        }

        $relateCategoryMap = [];
        if (!empty($categoryIds)) {
            $externalCategoryIds = Arr::uniqueFilterValues(\Util::flattenArray($categoryIds));
            if(!empty($externalCategoryIds)){
                $user = \User::getLoginUser();
                $language = str_starts_with($user->getLanguage(), 'zh') ? 'zh' :'en';
                $relateCategoryMap = \Category::getNameMap($language, $externalCategoryIds);
            }
        }

        // 格式化功能字段值
        foreach($pgUpdateFormatRows as $objName => $objData){
            if(empty($objData)){
                continue;
            }
            $formatTask = new \common\library\orm\pipeline\formatter\FieldV2FormatTask($this->clientId);
            $formatTask->setObjName($objName);
            $formatTask->setDisplayFields($displayFunctionFields[$objName]);
            $formatTask->setIsBizData(true);
            $formatTask->prepare($objData);
            foreach ($objData as $key => $datum){
                $formatTask->setReferenceData($datum);
                $formatTask->run($datum);
                $objData[$key] = $datum;
            }

            $pgUpdateFormatRows[$objName] = $objData;
        }

        $map = [
            'user' => $userMap,
            'field' => $fieldMap,
            'status' => $statusMap,
            'company' => $companyMap,
            'opportunity' => $opportunityMap,
            'customer' => $customerMap,
            'department' => $departmentMap,
            'workflow' => $workflowMap,
            'company_origin_map' => $originMap,
            'company_category' => $relateCategoryMap,
            'capital_account_map' => $capitalAccountMap,
            'cost_item_relation_map' => $costItemRelationMap,
            'product:group_names' => $groupMap,
            'biz_field' => $bizFieldMap,
            'function_field' => $functionFieldMap,
            'pg_update_format_data' => $pgUpdateFormatRows,
            'order_to_privilege_map' => $orderToPrivilegeMap,
        ];


        $this->setMapData($map);

        parent::buildMapData();
    }


    protected function formatPercentAmount($type,$value){

        $result = '';
        if( $type && ($type == CustomFieldService::ADDITION_FEE_TYPE_PLUS_PERCENTAGE || $type == CustomFieldService::ADDITION_FEE_TYPE_SUBTRACT_PERCENTAGE )){
            $result = $value.'%';
        }

        return $result;

    }

    protected function isShowFormatPercentAmount($elem){

        if( !isset($elem['old']) || !isset($elem['new']) ){
            return false;
        }

        if(  isset($elem['old']) && ($elem['old'] == CustomFieldService::ADDITION_FEE_TYPE_SUBTRACT_PERCENTAGE
                || $elem['old'] == CustomFieldService::ADDITION_FEE_TYPE_PLUS_PERCENTAGE) ){
            return true;
        }

        if(  isset($elem['new']) && ($elem['new'] == CustomFieldService::ADDITION_FEE_TYPE_SUBTRACT_PERCENTAGE
                || $elem['new'] == CustomFieldService::ADDITION_FEE_TYPE_PLUS_PERCENTAGE) ){
            return true;
        }

        return false;

    }

    // 该方法需要在调用了 buildBizField() 方法后使用
    protected function isFunctionField($fieldId){
        return !empty($this->getMapData('function_field', $fieldId));
    }

    /**
     *  格式动作
     * @param null|array $data
     * @return array
     */
    protected function format($data=null)
    {


        $result = [
            'client_id' => $data['client_id']??0,
            'order_id' => $data['order_id']??0,
            'type' => $data['type']??0,
            'create_time' => $data['create_time']??'',
            'update_user' => $this->getMapData('user',$data['update_user']) ?? ['user_id' => 0, 'nickname' => ''],  // ios端需要update_user返回对象，否则会异常闪退
        ];


        $diffList = $data['diff'] ? json_decode($data['diff'],1):[];
        if (!is_array($diffList)) {
            return $result;
        }
        $diff = array_combine(array_column($diffList,'id'),$diffList);
        $historyData = $data['data'] ? json_decode($data['data'],1):[];
        //处理title有涉及到字段信息的字段权限
        if ($data['order_product_id']) {
            $fieldPrivileges = $this->getMapData('order_to_privilege_map', $data['order_id']);
            foreach (['product_name', 'product_model'] as $field) {
                if (isset($historyData[$field]) && in_array($field, $fieldPrivileges['disable'] ?? [])) {
                    $historyData[$field] = FieldConstant::FIELD_VALUE_MASK;
                }
            }
        }

        $title = '';
        $prefix = '';
        $changeFiled = [];
        $isSub = $data['order_product_id'] > 0;

        $updateType = $data['update_type'];


        switch ($updateType){

            //2系统AI修改
            case OpportunityAIFieldData::FIELD_EDIT_TYPE_BY_AI:
                $prefix = '[' . \Yii::t('history', 'Auto') . ']';
                break;

            //工作流修改
            case  OpportunityAIFieldData::FIELD_EDIT_TYPE_BY_WORKFLOW:
                $prefix = $this->getMapData('workflow', $data['update_refer']) . '[' . \Yii::t('history', 'Workflow') . '] ';
                $result['update_user']['nickname'] = $this->getMapData('workflow', $data['update_refer']);
                break;

            //阿里订单
            case  Order::FIELD_EDIT_TYPE_BY_ALIBABA_ORDER_SYNC:
                $prefix = '[' . \Yii::t('history', 'Alibaba Order Sync') . ']';
                $result['update_user']['nickname'] = \Yii::t('history', 'Alibaba Order Sync');
                $result['update_user']['avatar'] = 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/icon/alibaba.svg';
                break;

            // ERP订单
            case  Order::FIELD_EDIT_TYPE_BY_ERP_KINGDEE:
            case  Order::FIELD_EDIT_TYPE_BY_ERP_CHANJET:
                $prefixKey = sprintf('ERP %s Order Sync', $updateType == Order::FIELD_EDIT_TYPE_BY_ERP_KINGDEE ? 'kingdee' : 'chanjet');
                $prefix = '[' . \Yii::t('history', $prefixKey) . ']';
                $result['update_user']['nickname'] = \Yii::t('history', $prefixKey);
                $result['update_user']['avatar'] = 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/icon/erp.svg';
                break;
            //工作流撤回
            case  BaseObject::FIELD_EDIT_TYPE_BY_WORKFLOW_ROLLBACK:
                $prefix = $this->getMapData('workflow', $data['update_refer']) . '[' . \Yii::t('history', 'Workflow rollback') . '] ';
                $result['update_user']['nickname'] = $this->getMapData('workflow', $data['update_refer']);
                break;
            default:
                break;
        }

        switch ($data['type']){
            case OrderHistory::TYPE_EDIT_ORDER:
            case OrderSetting::TYPE_CREATE_ORDER:

                if ($data['type'] == OrderSetting::TYPE_CREATE_ORDER) {
                    $title = \Yii::t('history', 'Create Order', ['{prefix}' => $prefix]);
                } else {
                    $title = \Yii::t('history', 'Modified order information', ['{prefix}' => $prefix]);
                }


                foreach ( $diff as $k => $elem ){

//                    // 这里内部有对人民币汇率和对美元汇率，对用户层需要处理成一个
//                    if ($elem['id'] == 'exchange_rate_usd') {
//                        $elem['id'] = 'exchange_rate';
//                        $diff[$k]['id'] = 'exchange_rate';
//                    }

                    $fieldInfo = $this->getMapData('field',$elem['id']);
                    $changeFiled[] = $diff[$k]['name'] = $fieldInfo['name'] ?? '';
                    $diff[$k]['field_type'] = intval($fieldInfo['field_type'] ?? 0);

                    $diff[$k]['relation_type'] = $fieldInfo['relation_type'] ?? 0;
                    $diff[$k]['relation_field'] = $fieldInfo['relation_field']??'';
                    $diff[$k]['relation_field_type'] = $fieldInfo['relation_field_type']??0;

                    if( $k =='company_id' ){
                        $newCompany = empty($elem['new'])?['name'=>'']: $this->getMapData('company',$elem['new']);
                        $oldCompany = empty($elem['old'])?['name'=>'']: $this->getMapData('company',$elem['old']);
                        $diff[$k]['new'] =$newCompany['name']??$elem['new'];
                        $diff[$k]['old'] =$oldCompany['name']??$elem['old'];
                    }

                    if( $elem['id'] =='opportunity_id' ){
                        $newOpportunity = empty($elem['new'])?['name'=>'']: $this->getMapData('opportunity',$elem['new']);
                        $oldOpportunity = empty($elem['old'])?['name'=>'']: $this->getMapData('opportunity',$elem['old']);
                        $diff[$k]['new'] =$newOpportunity['name']??$elem['new'];
                        $diff[$k]['old'] =$oldOpportunity['name']??$elem['old'];
                    }

                    if( $k =='customer_id' ){
                        $newCustomer = empty($elem['new'])?['name'=>'']:$this->getMapData('customer',$elem['new']);
                        $oldCustomer = empty($elem['old'])?['name'=>'']:$this->getMapData('customer',$elem['old']);
                        $diff[$k]['new'] =$newCustomer['name']??$elem['new'];
                        $diff[$k]['old'] =$oldCustomer['name']??$elem['old'];
                    }

                    if( $k =='capital_account_id' ){
                        $newCapitalAccount = empty($elem['new'])?['name'=>'']:$this->getMapData('capital_account_map',$elem['new']);
                        $oldCapitalAccount = empty($elem['old'])?['name'=>'']:$this->getMapData('capital_account_map',$elem['old']);
                        $diff[$k]['new'] =$newCapitalAccount['name']??$elem['new'];
                        $diff[$k]['old'] =$oldCapitalAccount['name']??$elem['old'];
                    }

                    if ($k == 'tax_refund_type') {
                        $diff[$k]['new'] = !empty($elem['new']) ? OmsConstant::ORDER_TAX_REFUND_TYPE_MAP[$elem['new']] ?? "" : "";
                        $diff[$k]['old'] = !empty($elem['old']) ? OmsConstant::ORDER_TAX_REFUND_TYPE_MAP[$elem['old']] ?? "" : "";
                    }


                    if ($elem['id'] == 'transport_mode') {
                        $diff[$k]['new'] = !empty($elem['new']) ? OmsConstant::SHIPMENT_METHOD_MAP[$elem['new']] ?? $elem['new'] : "";
                        $diff[$k]['old'] = !empty($elem['old']) ? OmsConstant::SHIPMENT_METHOD_MAP[$elem['old']] ?? $elem['old'] : "";
                    }

                    // 附件和图片类字段数据，需格式化文件信息列表
                    $diff[$k] = $this->formatFileDiff($diff[$k], $diff[$k]['field_type']);

                    //处理关联客户字段
                    if ($diff[$k]['field_type'] == CustomFieldService::FIELD_TYPE_FIELDS && $diff[$k]['relation_type'] == \Constants::TYPE_COMPANY) {
                        $diff[$k]['old']=$this->companyRelationFieldFormatter($diff[$k]['relation_field'], $elem['old']);
                        $diff[$k]['new']=$this->companyRelationFieldFormatter($diff[$k]['relation_field'], $elem['new']);
                    }
                }

                break;
            case  OrderHistory::TYPE_NEW_PRODUCT:

                $title = \Yii::t('history', 'Added product', ['{prefix}' => $prefix]);
                $title .=(isset($historyData['product_name'])&& !empty($historyData['product_name']))?" 【{$historyData['product_name']}】 " : (" 【".$historyData['product_no']."】 " ?? '');
//                $title .=(isset($historyData['product_no'])&& !empty($historyData['product_no']))? "{$historyData['product_no']}" :'';
                $title .=(isset($historyData['product_model'])&& !empty($historyData['product_model']))?" [{$historyData['product_model']}]" :'';

                foreach ( $diff as $k => $elem ){

                    $fieldInfo = $this->getMapData('field',$elem['id']);
                    $changeFiled[] = $diff[$k]['name'] = $fieldInfo['name'] ?? '';
                    $diff[$k]['field_type'] = intval($fieldInfo['field_type'] ?? 0);

                    $diff[$k]['relation_field'] = $productRelationField = $fieldInfo['relation_field']??'';
                    $diff[$k]['relation_field_type'] = $fieldInfo['relation_field_type']??0;

                    if (in_array($productRelationField, $this->productRelationSpecialField)) {
                        $diff[$k]['new'] =$this->productRelationFieldFormatter($productRelationField, $elem['new']);
                        $diff[$k]['old'] =$this->productRelationFieldFormatter($productRelationField, $elem['old']);
                    }


                }

                break;
            case  OrderHistory::TYPE_EDIT_PRODUCT:

                $title = \Yii::t('history', 'Edited product', ['{prefix}' => $prefix]) . ' ';
                $title .=(isset($historyData['product_name'])&& !empty($historyData['product_name']))?" 【{$historyData['product_name']}】 " : (" 【".$historyData['product_no']."】 " ?? '');
//                $title .=(isset($historyData['product_no'])&& !empty($historyData['product_no']))? "{$historyData['product_no']}" :'';
                $title .=(isset($historyData['product_model'])&& !empty($historyData['product_model']))?" [{$historyData['product_model']}]" :'';

                foreach ( $diff as $k => $elem ){

                    $fieldInfo = $this->getMapData('field',$elem['id']);
                    $changeFiled[] = $diff[$k]['name'] = $fieldInfo['name'] ?? '';
                    $diff[$k]['field_type'] = intval($fieldInfo['field_type'] ?? 0);

                    $diff[$k]['relation_field'] = $productRelationField = $fieldInfo['relation_field']??'';
                    $diff[$k]['relation_field_type'] = $fieldInfo['relation_field_type']??0;
                    if (in_array($productRelationField, $this->productRelationSpecialField)) {
                        $diff[$k]['new'] =$this->productRelationFieldFormatter($productRelationField, $elem['new']);
                        $diff[$k]['old'] =$this->productRelationFieldFormatter($productRelationField, $elem['old']);
                    }
                    $diff[$k] = $this->formatFileDiff($diff[$k], $diff[$k]['field_type']);
                }

                break;
            case OrderHistory::TYPE_DELETE_PRODUCT:

                $title = \Yii::t('history', 'Delete product', ['{prefix}' => $prefix]) . ' ';
                $title .=(isset($historyData['product_name'])&& !empty($historyData['product_name']))?" 【{$historyData['product_name']}】 " : (" 【".$historyData['product_no']."】 " ?? '');
//                $title .=(isset($historyData['product_no'])&& !empty($historyData['product_no']))? "{$historyData['product_no']}" :'';
                $title .=(isset($historyData['product_model'])&& !empty($historyData['product_model']))?" [{$historyData['product_model']}]" :'';

                foreach ( $diff as $k => $elem ){

                    $fieldInfo = $this->getMapData('field',$elem['id']);
                    $changeFiled[] = $diff[$k]['name'] = $fieldInfo['name'] ?? '';
                    $diff[$k]['field_type'] = intval($fieldInfo['field_type'] ?? 0);

                    $diff[$k]['relation_field'] = $productRelationField = $fieldInfo['relation_field'] ?? '';
                    $diff[$k]['relation_field_type'] = $fieldInfo['relation_field_type']??0;

                    if (in_array($productRelationField, $this->productRelationSpecialField)) {
                        $diff[$k]['new'] =$this->productRelationFieldFormatter($productRelationField, $elem['new']);
                        $diff[$k]['old'] =$this->productRelationFieldFormatter($productRelationField, $elem['old']);
                    }
                }

                break;
            case OrderHistory::TYPE_NEW_COST:

                isset($historyData['cost_item_relation_id']) && !empty($historyData['cost_item_relation_id']) && $costName = $this->getMapData('cost_item_relation_map', $historyData['cost_item_relation_id'])['item_name'] ?? '';
                empty($costName) && $costName = $historyData['cost_name'] ?? '';
                $title = \Yii::t('history', 'Added cost') . ' ' . $costName;

                foreach ( $diff as $k => $elem ){

                    $fieldInfo = $this->getMapData('field',$elem['id']);
                    $diff[$k]['name'] = $fieldInfo['name'] ?? '';
                    $diff[$k]['field_type'] = intval($fieldInfo['field_type'] ?? 0);

                    $diff[$k]['relation_field'] = $fieldInfo['relation_field']??'';
                    $diff[$k]['relation_field_type'] = $fieldInfo['relation_field_type']??0;

                    if( $k == 'percent_amount'  ){
                        $diff[$k]['old'] = $this->formatPercentAmount($diff['percent_type']['old']??0,$elem['old']);
                        $diff[$k]['new'] = $this->formatPercentAmount($diff['percent_type']['new']??0,$elem['new']);
                        $diff[$k]['name'] = \Yii::t('history', 'Percentage of the total amount of the product');
                    }
                    if( $k == 'cost_item_relation_id'  ){
                        $newCostItemRelation = empty($elem['new']) ? '' : $this->getMapData('cost_item_relation_map', $elem['new'])['item_name']??'';
                        $oldCostItemRelation = empty($elem['old']) ? '' : $this->getMapData('cost_item_relation_map', $elem['old'])['item_name']??'';

                        $diff[$k]['new'] = $newCostItemRelation ?? $elem['new'];
                        $diff[$k]['old'] = $oldCostItemRelation ?? $elem['old'];
                    }
                }

                if( !$this->isShowFormatPercentAmount($diff['percent_type']??null) ){
                    unset($diff['percent_amount']);
                }
                unset($diff['percent_type']);

                break;

            case OrderHistory::TYPE_EDIT_COST:
                isset($historyData['cost_item_relation_id']) && !empty($historyData['cost_item_relation_id']) && $costName = $this->getMapData('cost_item_relation_map', $historyData['cost_item_relation_id'])['item_name'] ?? '';
                empty($costName) && $costName = $historyData['cost_name'] ?? '';
                $title = \Yii::t('history', 'Edited cost') . ' ' . $costName;

                foreach ( $diff as $k => $elem ){

                    $fieldInfo = $this->getMapData('field',$elem['id']);
                    $diff[$k]['name'] = $fieldInfo['name'] ?? '';
                    $diff[$k]['field_type'] = intval($fieldInfo['field_type'] ?? 0);

                    $diff[$k]['relation_field'] = $fieldInfo['relation_field']??'';
                    $diff[$k]['relation_field_type'] = $fieldInfo['relation_field_type']??0;

                    if( $k == 'percent_amount'  ){
                        $diff[$k]['old'] = $this->formatPercentAmount($diff['percent_type']['old']??0,$elem['old']);
                        $diff[$k]['new'] = $this->formatPercentAmount($diff['percent_type']['new']??0,$elem['new']);
                        $diff[$k]['name'] = \Yii::t('history', 'Percentage of the total amount of the product');
                    }
                    if( $k == 'cost_item_relation_id'  ){
                        $newCostItemRelation = empty($elem['new']) ? '' : $this->getMapData('cost_item_relation_map', $elem['new'])['item_name']??'';
                        $oldCostItemRelation = empty($elem['old']) ? '' : $this->getMapData('cost_item_relation_map', $elem['old'])['item_name']??'';

                        $diff[$k]['new'] = $newCostItemRelation ?? $elem['new'];
                        $diff[$k]['old'] = $oldCostItemRelation ?? $elem['old'];
                    }
                }

                if( !$this->isShowFormatPercentAmount($diff['percent_type']??null) ){
                    unset($diff['percent_amount']);
                }
                unset($diff['percent_type']);

                break;
            case OrderHistory::TYPE_DELETE_COST:
                isset($historyData['cost_item_relation_id']) && !empty($historyData['cost_item_relation_id']) && $costName = $this->getMapData('cost_item_relation_map', $historyData['cost_item_relation_id'])['item_name'] ?? '';
                empty($costName) && $costName = $historyData['cost_name'] ?? '';
                $title = \Yii::t('history', 'Delete cost') . ' ' . $costName;

                foreach ( $diff as $k => $elem ){

                    $fieldInfo = $this->getMapData('field',$elem['id']);
                    $diff[$k]['name'] = $fieldInfo['name'] ?? '';
                    $diff[$k]['field_type'] = intval($fieldInfo['field_type'] ?? 0);

                    $diff[$k]['relation_field'] = $fieldInfo['relation_field']??'';
                    $diff[$k]['relation_field_type'] = $fieldInfo['relation_field_type']??0;

                    if( $k == 'percent_amount'  ){
                        $diff[$k]['old'] = $this->formatPercentAmount($diff['percent_type']['old']??0,$elem['old']);
                        $diff[$k]['new'] = $this->formatPercentAmount($diff['percent_type']['new']??0,$elem['new']);
                        $diff[$k]['name'] = \Yii::t('history', 'Percentage of the total amount of the product');
                    }
                    if( $k == 'cost_item_relation_id'  ){
                        $newCostItemRelation = empty($elem['new']) ? '' : $this->getMapData('cost_item_relation_map', $elem['new'])['item_name']??'';
                        $oldCostItemRelation = empty($elem['old']) ? '' : $this->getMapData('cost_item_relation_map', $elem['old'])['item_name']??'';

                        $diff[$k]['new'] = $newCostItemRelation ?? $elem['new'];
                        $diff[$k]['old'] = $oldCostItemRelation ?? $elem['old'];
                    }
                }

                if( !$this->isShowFormatPercentAmount($diff['percent_type']??null) ){
                    unset($diff['percent_amount']);
                }
                unset($diff['percent_type']);

                break;

            case OrderHistory::TYPE_EDIT_USER:

                $title = \Yii::t('history', 'Edited the order owner');

                foreach ( $diff as $k => $elem ){

                    $fieldInfo = $this->getMapData('field',$elem['id']);
                    $diff[$k]['name'] = $fieldInfo['name'] ?? '';
                    $diff[$k]['field_type'] = intval($fieldInfo['field_type'] ?? 0);

                    $diff[$k]['relation_field'] = $fieldInfo['relation_field']??'';
                    $diff[$k]['relation_field_type'] = $fieldInfo['relation_field_type']??0;

                    $departmentMap = $this->mapData['user']??[];

                    $diff[$k]['new'] = array_reduce($elem['new'],function ($carry,$item)use($departmentMap){
                        $nickname = $departmentMap[$item['user_id']]['nickname']??'';
                        $carry[]="{$nickname} ({$item['rate']}%)\n";
                        return $carry;
                    },[]);
                    $diff[$k]['new'] = implode('、',$diff[$k]['new']);

                    $diff[$k]['old'] = array_reduce($elem['old'],function ($carry,$item)use($departmentMap){
                        $nickname = $departmentMap[$item['user_id']]['nickname']??'';
                        $carry[]="{$nickname} ({$item['rate']}%)\n";
                        return $carry;
                    },[]);
                    $diff[$k]['old'] = implode('、',$diff[$k]['old']);

                }

                break;
            case OrderHistory::TYPE_EDIT_DEPARTMENTS:

                $title = \Yii::t('history', 'Edited the order owner departments');

                foreach ( $diff as $k => $elem ){
                    $fieldInfo = $this->getMapData('field',$elem['id']);
                    $diff[$k]['name'] = $fieldInfo['name'] ?? '';
                    $diff[$k]['field_type'] = intval($fieldInfo['field_type'] ?? 0);

                    $diff[$k]['relation_field'] = $fieldInfo['relation_field']??'';
                    $diff[$k]['relation_field_type'] = $fieldInfo['relation_field_type']??0;

                    $departmentMap = $this->mapData['department']??[];

                    $diff[$k]['new'] = array_reduce($elem['new'],function ($carry,$item)use($departmentMap){
                        $nickname = $departmentMap[$item['department_id']]['name']??'';
                        $carry[]="{$nickname} ({$item['rate']}%)\n";
                        return $carry;
                    },[]);
                    $diff[$k]['new'] = implode('、',$diff[$k]['new']);

                    $diff[$k]['old'] = array_reduce($elem['old'],function ($carry,$item)use($departmentMap){
                        $nickname = $departmentMap[$item['department_id']]['name']??'';
                        $carry[]="{$nickname} ({$item['rate']}%)\n";
                        return $carry;
                    },[]);
                    $diff[$k]['old'] = implode('、',$diff[$k]['old']);
                }

                break;

            case OrderHistory::TYPE_STATUS:

                $title = \Yii::t('history', 'Changed order status', ['{prefix}' => $prefix]);

                foreach ( $diff as $k => $elem ){

                    $fieldInfo = $this->getMapData('field',$elem['id']);
                    $changeFiled[] = $diff[$k]['name'] = $fieldInfo['name'] ?? '';
                    $diff[$k]['field_type'] = intval($fieldInfo['field_type'] ?? 0);

                    $diff[$k]['relation_field'] = $fieldInfo['relation_field']??'';
                    $diff[$k]['relation_field_type'] = $fieldInfo['relation_field_type']??0;

                    if($elem['id'] == 'status'){
                        $diff[$k]['old'] = $this->getMapData('status',$elem['old'])??$elem['old'];
                        $diff[$k]['new'] = $this->getMapData('status',$elem['new'])??$elem['new'];
                    }

                }

                break;

            case OrderHistory::TYPE_HANDLER:

                $title = \Yii::t('history', 'Edited current handler', ['{prefix}' => $prefix]);

                foreach ( $diff as $k => $elem ){

                    $fieldInfo = $this->getMapData('field',$elem['id']);

                    $diff[$k]['name'] = $fieldInfo['name'] ?? '';
                    $diff[$k]['field_type'] = intval($fieldInfo['field_type'] ?? 0);

                    $diff[$k]['relation_field'] = $fieldInfo['relation_field']??'';
                    $diff[$k]['relation_field_type'] = $fieldInfo['relation_field_type']??0;

                    $departmentMap = $this->mapData['user']??[];

                    $diff[$k]['new'] = array_reduce($elem['new'],function ($carry,$item)use($departmentMap){
                        $nickname = $departmentMap[$item]['nickname']??'';
                        $carry[]=" {$nickname} \n";
                        return $carry;
                    },[]);

                    $diff[$k]['new'] = implode('、',$diff[$k]['new']);

                    $diff[$k]['old'] = array_reduce($elem['old'],function ($carry,$item)use($departmentMap){
                        $nickname = $departmentMap[$item]['nickname']??'';
                        $carry[]="{$nickname} \n";
                        return $carry;
                    },[]);

                    $diff[$k]['old'] = implode('、',$diff[$k]['old']);
                }

                break;
            case OrderHistory::TYPE_ALIBABA_SYNC:
                $showFields = ['status'];

                $isShowField = false;

                foreach ( $diff as $k => $elem ) {
                    if(in_array($k, $showFields)){
                        $isShowField = true;
                    }
                }

                if($isShowField){

                    $title = \Yii::t('history', 'Changed order status', ['{prefix}' => $prefix]);

                    foreach ( $diff as $k => $elem ){
                        if(!in_array($k, $showFields)){
                            unset($diff[$k]);
                            continue;
                        }

                        $fieldInfo = $this->getMapData('field',$elem['id']);
                        $changeFiled[] = $diff[$k]['name'] = $fieldInfo['name'] ?? '';
                        $diff[$k]['field_type'] = intval($fieldInfo['field_type'] ?? 0);

                        $diff[$k]['relation_field'] = $fieldInfo['relation_field']??'';
                        $diff[$k]['relation_field_type'] = $fieldInfo['relation_field_type']??0;

                        if($elem['id'] == 'status'){
                            $diff[$k]['old'] = $this->getMapData('status',$elem['old'])??$elem['old'];
                            $diff[$k]['new'] = $this->getMapData('status',$elem['new'])??$elem['new'];
                        }
                    }

                }else{
                    $title = \Yii::t('history', 'Modified order information', ['{prefix}' => $prefix]);
                    $diff = [];
                }
                break;

            case OrderHistory::TYPE_OPEN_SYNC_ALI_ORDER_SWITCH:

                $prefix = $result['update_user']['nickname'];
                $title = \Yii::t('history', 'Enable order synchronization', ['{prefix}' => $prefix]);
                break;
            case OrderHistory::TYPE_CLOSE_SYNC_ALI_ORDER_SWITCH:
                $prefix = $result['update_user']['nickname'];
                $title = \Yii::t('history', 'Turn off order synchronization', ['{prefix}' => $prefix]);
                break;
            case OrderHistory::TYPE_REMOVE:
                $title = \Yii::t('history', 'Delete order', ['{prefix}' => $prefix]);
                $diff = [];
                break;
            case OrderHistory::TYPE_RECOVER:
                $title = \Yii::t('history', 'Recover order', ['{prefix}' => $prefix]);
                break;
            case OrderHistory::TYPE_SYNC_ALI_ORDER_CONFIG_UPDATE:
                $title = \Yii::t('history', 'Update order synchronization', ['{prefix}' => $prefix]);
                $newConfig = array_filter($diff['sync_ali_order_config']['new']??[]);
                $oldConfig = array_filter($diff['sync_ali_order_config']['old']??[]);
                $configMap = [
                    Constant::SYNC_ALIBABA_ORDER_BASE=>'同步订单基础数据',
                    Constant::SYNC_ALIBABA_ORDER_PRODUCT=>'同步订单明细数据',
                    Constant::SYNC_ALIBABA_ORDER_CASH_COLLECTION=>'同步回款单数据',
                    Constant::SYNC_ALIBABA_ORDER_STATUS=>'同步订单状态',
                ];
                $newConfigString = [];
                $oldConfigString = [];
                foreach ($newConfig as  $value) {
                    $newConfigString[] = $configMap[$value];
                }
                foreach ($oldConfig as  $value) {
                    $oldConfigString[] = $configMap[$value];
                }

                $diff['sync_ali_order_config']['new'] = implode(',',$newConfigString);
                $diff['sync_ali_order_config']['old'] = implode(',',$oldConfigString);
                $changeFiled[]= '订单同步配置';
                break;
            case OrderHistory::TYPE_IMPORT_ORDER:
                $title = \Yii::t('history', 'Import order', ['{prefix}' => $prefix]);
                break;
            case OrderHistory::TYPE_SYNC_ALI_ALIBABA_ORDER_TASK_RESULT:
                $title = \Yii::t('history', '本地订单生成信保订单记录', ['{prefix}' => $prefix]);
                break;
            case OrderHistory::TYPE_SYNC_ERP_ORDER_STATUS_UPDATE:
                $prefixKey = sprintf('ERP %s Order Sync', $updateType == Order::FIELD_EDIT_TYPE_BY_ERP_KINGDEE ? 'kingdee' : 'chanjet');
                $prefix = '[' . \Yii::t('history', $prefixKey) . ']';
                $title = \Yii::t('history', 'Erp edit order', ['{prefix}' => $prefix]);
                foreach($diff as $k => $elem){
                    if($elem['id'] == 'erp_status'){
                        if($updateType == Order::FIELD_EDIT_TYPE_BY_ERP_KINGDEE) {
                            $erpStatusNamMap = KingDeeConstant::ERP_STATUS_NAME_MAP;
                            $diff[$k]['name'] = \Yii::t('history', 'erp audit status');
                        }else{
                            $erpStatusNamMap = ChanjetConstant::ERP_STATUS_NAME_MAP;
                            $diff[$k]['name'] = \Yii::t('history', 'erp invoice status');
                        }
                        $diff[$k]['new'] = $erpStatusNamMap[$diff[$k]['new']]??'';
                        $diff[$k]['old'] = $erpStatusNamMap[$diff[$k]['old']]??'';
                    }
                    if($elem['id'] == 'other_erp_status'){
                        if($updateType == Order::FIELD_EDIT_TYPE_BY_ERP_KINGDEE) {
                            $otherErpStatusDiff = $diff[$k];
                            unset($diff[$k]);
                            $statusMap = KingDeeConstant::STAUTS_TOP_MAP;
                            foreach($otherErpStatusDiff['new'] as $otherStatusType => $otherStatus){
                                $diff[$otherStatusType] = [
                                    'id' => $otherStatusType,
                                    'name' => KingDeeConstant::STATUS_KEY_NAME_MAP[$otherStatusType],
                                    'new' => $statusMap[$otherStatusType][$otherStatus],
                                    'old' => $statusMap[$otherStatusType][$otherErpStatusDiff['old'][$otherStatusType] ?? ''] ?? '',
                                    'base' => 1
                                ];
                            }
                            foreach($otherErpStatusDiff['old'] as $otherStatusType => $otherStatus){
                                if(isset($diff[$otherStatusType])){
                                    continue;
                                }
                                $diff[$otherStatusType] = [
                                    'id' => $otherStatusType,
                                    'name' => KingDeeConstant::STATUS_KEY_NAME_MAP[$otherStatusType],
                                    'new' => $statusMap[$otherStatusType][$otherErpStatusDiff['new'][$otherStatusType] ?? ''] ?? '',
                                    'old' => $statusMap[$otherStatusType][$otherStatus],
                                    'base' => 1
                                ];
                            }
                            $diff = array_values($diff);
                        }else{
                            $diff[$k]['name'] = \Yii::t('history', 'erp invoice execute status');
                            $diff[$k]['old'] = implode('、', $diff[$k]['old']?:[]);
                            $diff[$k]['new'] = implode('、', $diff[$k]['new']?:[]);
                        }
                    }
                }
                break;
            case Setting::TYPE_EDIT_BY_PG_UPDATOR:
                $title = \Yii::t('history', "relation update");
                foreach ( $diff as $k => $elem ) {
                    $fieldInfo = $this->getMapData('field', $elem['id']);
                    $changeFiled[] = $diff[$k]['name'] = $fieldInfo['name'] ?? '';
                    $diff[$k]['field_type'] = intval($fieldInfo['field_type'] ?? 0);
                }
                break;
            default:
                break;

        }

        foreach ($diff as $k => $elem) {

            if (!empty($changeFiled)) {
                // 功能字段格式化
                if($this->isFunctionField($elem['id'])){
                    $changeFiled[] = $diff[$k]['name'] = $this->getMapData('function_field', $elem['id'])['field_name'] ?? '未知字段';
                    $uniqueKeyPrefix = md5($data['order_id'].$data['diff']);
                    $newUniqueKey = $uniqueKeyPrefix.'_new';
                    $oldUniqueKey = $uniqueKeyPrefix.'_old';
                    $objectNameKey = $isSub ? \common\library\object\object_define\Constant::OBJ_ORDER_PRODUCT : \common\library\object\object_define\Constant::OBJ_ORDER;
                    $pgFormatFieldVals = $this->getMapData('pg_update_format_data', $objectNameKey);
                    $diff[$k]['new'] = $pgFormatFieldVals[$newUniqueKey][$elem['id']]??null;
                    $diff[$k]['old'] = $pgFormatFieldVals[$oldUniqueKey][$elem['id']]??null;
                    $diff[$k]['field_type'] = $diff[$k]['relation_field_type'] = $this->getMapData('function_field', $elem['id'])['field_type'];
                    $diff[$k]['base'] = 0;
                }
            }

            if ($this->fieldFormatType == AbstractInvoiceFormatter::FIELD_FORMAT_TYPE_JSON) {
                if (isset($diff[$k]['new']) && is_array($diff[$k]['new'])) $diff[$k]['new'] = json_encode($diff[$k]['new']);
                if (isset($diff[$k]['old']) && is_array($diff[$k]['old'])) $diff[$k]['old'] = json_encode($diff[$k]['old']);
            }
        }



        $changeFiled = array_unique($changeFiled);
        $result['title'] = $title;
        $result['change_filed'] = implode(',',array_filter(array_unique($changeFiled)));
        $result['diff'] = array_values($diff);

        return $result;

    }

    protected function productRelationFieldFormatter($field,$value)
    {
        switch ($field)
        {
            case 'group_id':
                $value = $this->getMapData('product:group_names', $value);
                break;
            case 'fob':
                $data = [
                    $value['price_currency']??'',
                    $value['price_min']??'',
                    '-',
                    $value['price_max']??'',
                    $value['price_unit']??'',
                ];
                $value = implode(' ', $data);
                break;

            case 'minimum_order_quantity':
                //此处包含扩展字段中minimum_order_quantity处理
                $data = [$value['quantity']??'',$value['quantity_unit']??''];
                $data = is_numeric($value) ? [$value] : $data;
                $value = implode(' ', $data);
                break;

            case 'package_size':
                $value = $this->formatPackageSizeField($value);
                break;
            case 'product_size':
                $value = $this->formatProductSizeField($value);
                break;
            case 'carton_size':
                $value = $this->formatCartonSizeField($value);
                break;
            case 'gross_profit_margin':
                $value .= '%';
                break;
            case 'category_ids':
                $value = $this->getProductCategoryString($value);
                break;
        }

        return $value;
    }

    static $category;
    protected function getProductCategoryString($categoryArray)
    {
        if(!is_array($categoryArray))
            return $categoryArray;

        $categoryArray = $categoryArray ? $categoryArray : [];

        $findCategoryIds = [];
        foreach ($categoryArray as $ids) {
            $ids = is_array($ids) ? $ids : [$ids];
            foreach ($ids as $id) {
                if(!isset(self::$category[$id])){
                    $findCategoryIds[] = $id;
                }
            }
        }

        if($findCategoryIds){
            $list = \Category::findAllByIds($findCategoryIds);
            foreach ($list as $item) {
                self::$category[$item->id] = "{$item->cn_name}（{$item->en_name}）";
            }
        }

        $insertValue = [];
        foreach ($categoryArray as $ids)
        {
            $ids = is_array($ids) ? $ids : [$ids];
            foreach ($ids as $id) {
                $insertValue[] = isset(self::$category[$id]) ? self::$category[$id] : '';
            }
        }

        $insertString = implode('—', $insertValue);
        return $insertString;
    }

    protected function companyRelationFieldFormatter($field,$value)
    {
        if (empty($value) && !in_array($field, ['timezone', 'annual_procurement', 'intention_level'])) {
            return '';
        }
        switch ($field) {
            case 'origin':
            case 'origin_list':
                $value = (array)$value;
                $returnValue = [];
                foreach ($value as $item) {
                    $returnValue[] = $this->getMapData('company_origin_map', $item) ?? '';
                }
                $value = implode('，', array_filter($returnValue));
                break;
            case "category_ids":    // 主营产品
                $mapKey = 'company_category';
                $result = "";
                foreach($value as $categoryPath){
                    $formatCategory = [];
                    foreach($categoryPath as $category){
                        $formatCategory[] = $this->getMapData($mapKey, $category);
                    }
                    $formatCategory = array_filter($formatCategory);
                    if($formatCategory){
                        $result .= implode('-', $formatCategory) . "\n";
                    }
                }
                $result = rtrim($result, "\n");
                $value = $result;
                break;
            case 'product_group_ids':   //客户-产品分组
                $mapKey = 'product:group_names';
                if (is_array($value)) {
                    $value = '';
                } else {
                    $value = $this->getMapData($mapKey, $value);
                }
                break;
            case "timezone":    // 时区
                $value = \CustomerOptionService::TIMEZONE_MAP[$value] ?? '';
                break;
            case "scale_id":    // 规模
                $scale = \CustomerOptionService::SCALE_MAP[$value] ?? [];
                $value = empty($scale) ? '' : $scale['min'] .'-'.$scale['max'];
                break;
            case "annual_procurement":    // 年采购额
                $starMap = \CustomerOptionService::annualProcurementMap();
                $value = $starMap[$value] ?? '';
                break;
            case "intention_level":    // 采购意向
                $starMap = \CustomerOptionService::intentionLevelMap();
                $value = $starMap[$value] ?? '';
                break;
            case "star":    // 星级
                $starMap = \CustomerOptionService::starMap();
                $value = $starMap[$value] ?? '';
                break;
            case "trail_status":    // 客户状态
                $mapKey = 'company_trail_status';
                if(!$this->hasMapData($mapKey, $value)){
                    $this->mapData[$mapKey] = array_column(\CustomerOptionService::getCustomerStatusList($this->clientId), "status_name", "status_id");
                }
                $value = $this->getMapData($mapKey, $value);
                break;
            case "tel":     // 座机
                if (isset($value['tel_area_code']) || isset($value['tel'])){
                    $value = ($value['tel_area_code'] ?? '') . '-' . ($value['tel'] ?? '');
                }
                break;
        }

        return $value;
    }

    protected function formatFileDiff($elem, $fieldType){
        // 附件和图片类字段数据，需格式化文件信息列表
        if (in_array($fieldType, [CustomFieldService::FIELD_TYPE_ATTACH, CustomFieldService::FIELD_TYPE_IMAGE])) {
            if($elem['old'] && is_array($elem['old'])) {
                foreach ($elem['old'] as $kk => $item) {
                    $elem['old'][$kk] = $this->formatFile($item);
                }
            }
            if($elem['new'] && is_array($elem['new'])) {
                foreach ($elem['new'] as $kk => $item) {
                    $elem['new'][$kk] = $this->formatFile($item);
                }
            }
        }

        return $elem;
    }

    protected function formatFile($item){
        $file = new \AliyunUpload();
        $file->loadByFileId($item['file_id']);
        $file_obj = $file->getFileObject();

        $item['file_id'] = $file->getFileId();
        $item['file_name'] = $file->getFileName();
        $item['file_size'] = $file->getFileSize();
        $item['file_url'] = $file->getFileUrl();
        $item['preview_url'] = $file->getPreview();
        $item['bucket'] = $file->getBucket();
        $item['file_key'] = $file->getFileKey();
        $item['ETag'] = '';
        $item['mime_type'] = $file_obj->file_mime;
        $item['target_type'] = '2';
        return $item;
    }
}
