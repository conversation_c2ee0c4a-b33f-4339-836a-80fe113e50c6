<?php
/**
 * Created by PhpStorm.
 * User: beenzhang
 * Date: 2020/6/17
 * Time: 4:42 PM
 */

namespace common\library\invoice\export_from_filter;

use common\library\account\Client;
use common\library\alibaba\store\AlibabaStoreAccountSyncHelper;
use common\library\alibaba\store\AlibabaStoreService;
use common\library\cash_collection\CashCollection;
use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldList;
use common\library\custom_field\quote_field\QuoteFieldSwitch;
use common\library\customer_v3\company\list\CompanyList;
use common\library\customer_v3\customer\CustomerList;
use common\library\department\DepartmentService;
use common\library\exchange_rate\ExchangeRateService;
use common\library\formula\AttributeFormulaTrait;
use common\library\group\Helper;
use common\library\invoice\InvoiceProductRecordList;
use common\library\invoice\order_erp_external\OrderErpExternalApi;
use common\library\invoice\status\InvoiceStatusService;
use common\library\oms\capital_account\CapitalAccountFilter;
use common\library\oms\common\OmsConstant;
use common\library\oms\order_link\Constant;
use common\library\oms\order_link\OrderLink;
use common\library\opportunity\OpportunityList;
use common\library\platform_product\sku\PlatformSkuAPI;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\product_v2\ProductConstant;
use common\library\product_v2\ProductHelper;
use common\library\product_v2\sku\SkuAPI;
use common\library\setting\library\fund\relation\CostItemInvoiceRelationApi;
use common\library\setting\library\origin\OriginApi;
use common\library\setting\library\tag\TagApi;
use common\library\util\PgsqlUtil;

class OrderListExportFormatter extends \ListItemFormatter
{
    use AttributeFormulaTrait, QuoteFieldSwitch;
    protected $clientId;
    protected $opUserId;
    protected $moduleType;

    protected $fieldMap;
    protected $mainCurrency;
    protected $needSubProductFlag;
    protected $showProduct;

    //需要导出的 字段，为了减少请求，不必要的字段不请求对应的数据，根据业务来判断
    protected $needExportInvoiceFields = [];

    protected $specifyFields = [
        'approval_status',
        'cash_collection',
        'link_status',
        'capital_account_id',
        'erp_status',
    ];

    public function __construct($clientId, $userId)
    {
        $this->clientId = $clientId;
        $this->opUserId = $userId;
        $this->init();
    }

    protected function init()
    {
        $this->moduleType = \Constants::TYPE_ORDER;
        $this->fieldMap = $this->getFieldMap();
    }

    public function setNeedSubProduct($flag)
    {
        $this->needSubProductFlag = $flag;
    }

    public function setShowProduct($flag){
        $this->showProduct = $flag;
    }

    public function getListDataFieldMap(){
        return \common\library\invoice\Helper::getOrderListDataFieldMap();
    }

    protected function processExternalField($externalData)
    {
        $fieldMap = $this->getFieldMap();
        foreach ($externalData as $field => &$value)
        {
            $fieldInfo = $fieldMap[$field] ?? [];
            $fieldType = intval($fieldInfo['field_type']??0);

            //关联company字段 and 系统字段
            if ($fieldType == CustomFieldService::FIELD_TYPE_FIELDS && !is_numeric($fieldInfo['relation_field']))
            {
                $value = $this->processRelationCompanyField($fieldInfo['relation_field'], $value);
                continue;
            }

            $value = $this->processByFieldType($field, $value);
        }

        return $externalData;
    }


    protected function processRelationCompanyField($companyField, $value)
    {
        switch ($companyField)
        {
            case 'tel':
                $value = array_filter([$value['tel_area_code']??'',$value['tel']??'']);
                $value = implode(' ', $value);
                break;
            case 'group_id':
                $value = $this->getMapData('company:group_names', $value) ?? \Yii::t('field', '未分组');
                break;
            case 'origin':
            case 'origin_list':
                $value = (array)$value;
                $valueArr = [];
                foreach ($value as $item) {
                    $valueArr[] = $this->getMapData('company:origin_names', $item) ?? '';
                }
                $value = implode(', ', array_filter($valueArr));
                break;
            case 'image_list':
                $value = $this->getFileUrlFromFileInfoMap($value);
                break;
            case 'cus_tag': //客户标签 数据是数组 [111,222,333] 如果是其他的格式，说明数据错误
                $value = (array)$value;
                $valueArr = [];
                foreach ($value as $item) {
                    $valueArr[] = $this->getMapData('company:cus_tag', $item) ?? '';
                }
                $value = implode('; ', array_filter($valueArr));
                break;
            case 'trail_status':
                $value = $this->getMapData('company:trail_status', $value) ?? '';
                break;
            case 'category_ids':
                $categoryNames = [];
                foreach ($value?:[] as $item)
                {
                    $names = [];
                    foreach ($item as $elem)
                    {
                        $name = $this->getMapData('company:category_names', $elem) ?? '';
                        !empty($name) && $names[] = $name;
                    }
                    if (!empty($names))
                    {
                        $categoryNames[] = implode('-', $names);
                    }
                }

                $value = implode(';', $categoryNames);
                break;
            case 'scale_id':
                $value = $this->getMapData('company:scale', $value) ?? '';
                break;
            case 'timezone':
                $value = \Yii::t('customer', \CustomerOptionService::TIMEZONE_MAP[$value]??'未知');
                break;

	        case 'annual_procurement':
		        $value = \CustomerOptionService::annualProcurementMap()[$value] ?? $value;

		        break;

	        case 'intention_level':
		        $value = \CustomerOptionService::intentionLevelMap()[$value] ?? $value;
		        break;

	        case 'product_group_ids':  //产品分组
                $value = \common\library\group\Helper::formatProductGroupName($this->clientId, $value, \Constants::TYPE_PRODUCT) ?: '-';
		        break;


        }

        $value = $this->processString($value);
        return $value;
    }

    public function format($data)
    {
	    foreach ($data as $field => &$value) {
		    try {
			    if ($field == 'external_field_data') {
				    $value = $this->processExternalField($value);
				    continue;
			    }

			    if ($field == 'product_list') {
				    $value = $this->processProduct($value);
				    continue;
			    }
                if ($field == 'cost_list') {
                    $value = $this->processCostItem($value);
                    continue;
                }

			    $value = $this->processByField($field, $value, $data);
		    } catch (\Throwable $exception) {

			    echo 'field:' . $field . 'value' . json_encode($value) . $exception->getMessage().' | file:'.$exception->getFile().' | line:'.$exception->getLine();
			    \LogUtil::error('field:' . $field . 'value' . json_encode($value) . $exception->getMessage());
			    continue;
		    }
	    }
        unset($value);

        if (in_array('cash_collection', $this->specifyFields))
        {
            $data['cash_collection'] = $this->getMapData('cashCollection', $data['order_id'])['status_name']??'';
            $data['cash_collection_percentage'] = strval($this->getMainCurrency() == ExchangeRateService::USD ? ($this->getMapData('cashCollection', $data['order_id'])['percentage_usd'] ?? 0) : ($this->getMapData('cashCollection', $data['order_id'])['percentage_rmb'] ?? 0))."%";
            $data['cash_collection_info.collect_amount_rmb'] = $this->getMapData('cashCollection', $data['order_id'])['collect_amount_rmb']??'';
            $data['cash_collection_info.collect_amount_usd'] = $this->getMapData('cashCollection', $data['order_id'])['collect_amount_usd']??'';
            $data['cash_collection_info.not_collect_amount_rmb'] = $this->getMapData('cashCollection', $data['order_id'])['not_collect_amount_rmb']??'';
            $data['cash_collection_info.not_collect_amount_usd'] = $this->getMapData('cashCollection', $data['order_id'])['not_collect_amount_usd']??'';
        }
        if (in_array('link_status', $this->specifyFields)) {
            $linkStatus = $this->processLinkStatus($data['link_status']);
            foreach ($linkStatus as $link => $status) {
                $data[$link] = $status;
            }
        }
        if (in_array('approval_status', $this->specifyFields))
        {
            $data['approval_status'] = $this->getMapData('approval', $data['order_id'])['status'] ?? null;
            if ($data['approval_status'] !== null)
                $data['approval_status'] = \Yii::t('approvalflow', 'apply_form_status.'.$data['approval_status']);
        }

        if (in_array('capital_account_id', $this->specifyFields)) {
            $data['capital_name'] = $this->getMapData('capital_account', $data['capital_account_id'])['capital_name'] ?? '';
            $data['capital_bank'] = $this->getMapData('capital_account', $data['capital_account_id'])['capital_bank'] ?? '';
            $data['bank_account'] = $this->getMapData('capital_account', $data['capital_account_id'])['bank_account'] ?? '';
            $data['capital_account_address'] = $this->getMapData('capital_account', $data['capital_account_id'])['address'] ?? '';
            $data['capital_account_remark'] = $this->getMapData('capital_account', $data['capital_account_id'])['remark'] ?? '';
            $data['capital_account_id'] = $this->getMapData('capital_account', $data['capital_account_id'])['name'] ?? '';

        }

        if (in_array('erp_status', $this->needExportInvoiceFields)) {
            $erpStatus = $this->getMapData('erp_status_map', $data['order_id'])['erp_status'] ?? '0';
            $erpPlatformType = $this->getMapData('erp_status_map', $data['order_id'])['platform_type'] ?? '0';
            $data['erp_status'] = OrderErpExternalApi::erpStatusMap($erpStatus, $erpPlatformType);
        }

        return $data;
    }

    public function strip($data)
    {
        $data['user_id'] = PgsqlUtil::trimArray($data['user_id'] ?? '{}');
        $data['users'] = json_decode($data['users'] ?? '{}', true);
        $data['departments'] = json_decode($data['departments'] ?? '{}', true);
        $data['handler'] = PgsqlUtil::trimArray($data['handler'] ?? '{}');
        $data['external_field_data'] = json_decode($data['external_field_data'] ?? '{}', true);
        $data['product_list'] = json_decode($data['product_list'] ?? '{}', true);
        $data['cost_list'] = json_decode($data['cost_list'] ?? '{}', true);
        $data['file_list'] = json_decode($data['file_list'] ?? '{}', true);
        $data['delete_time'] = $data['delete_time'] != '1970-01-01 08:00:00' ? $data['delete_time'] : '';
        $data['account_date'] = $data['account_date'] != '1970-01-01 08:00:00' ? $data['account_date'] : '';

        return $data;
    }

    public function buildMapData()
    {
        $list = $this->batchFlag ? $this->listData : [$this->data];

        if (empty($list))
            return ;

        $userIds = [];
        $statusIds = [];
        $departmentIds = [];
        $opportunityIds = [];
        $companyIds = [];
        $customerIds = [];
        $externalFieldDataList = [];
        $productFieldDataList = [];
        $countryMaps = [];
        $countryCodes = [];
        $sellerAccountIds = [];
        $aliStoreIds = [];
        $skuIds = [];
        $uniqueIds = [];
        $platformSkuIds = [];
        $capitalAccountIds = [];
        $costItemRelationIds = [];
        $fileIds = [];
        $combineUniqueId = [];

        foreach ($list as $elem)
        {

            $orderIds[] = $elem['order_id'];
            $userIds[] = $elem['create_user'];
            $statusIds[] = $elem['status']??0;
            $capitalAccountIds[] = $elem['capital_account_id'] ?? 0;

            if( !empty($elem['users']) ) {
                if (!is_array($elem['users'])) {
                    $elem['users'] = json_decode($elem['users'], true) ?? [];
                }
                $userIds = array_merge($userIds, array_column($elem['users'], 'user_id'));
            }

            if( !empty($elem['country']) ) {

                $countryCodes[] = $elem['country'];
            }
            if( !empty($elem['seller_account_id']) ) {

                $sellerAccountIds[] = $elem['seller_account_id'];
            }
            if( !empty($elem['ali_store_id']) ) {
                $aliStoreIds[] = $elem['ali_store_id'];
            }

            if ( !empty($elem['departments'])) {
                $elem['departments'] = is_array($elem['departments']) ? $elem['departments'] : json_decode($elem['departments'], true) ?? [];
                $departmentIds = array_merge($departmentIds, array_column($elem['departments'], 'department_id'));
            }

            if( !empty($elem['handler']) ){
                if (!is_array($elem['handler'])) {
                    $elem['handler'] = PgsqlUtil::trimArray($elem['handler']);
                    $elem['handler'] = $elem['handler'] ?: [];
                }
                $userIds = array_merge($userIds,$elem['handler']);
            }

            if ($elem['company_id']) {
                $companyIds[$elem['company_id']] = $elem['company_id'];
            }

            if ($elem['customer_id']??0) {
                $customerIds[$elem['customer_id']] = $elem['customer_id'];
            }

            if (isset($elem['opportunity_id']) && $elem['opportunity_id']) {    //兼容原先的订单
                $opportunityIds[$elem['opportunity_id']] = $elem['opportunity_id'];
            }

            $this->buildFileIds($elem, $fileIds);
            if (isset($elem['external_field_data']) && !empty($elem['external_field_data']))
            {
                $externalFieldData = is_array($elem['external_field_data'])? $elem['external_field_data'] :json_decode($elem['external_field_data'], true);
                $externalFieldDataList[] = $externalFieldData;
                $this->buildFileIds($externalFieldData,$fileIds);
            }

            if (isset($elem['product_list']) && !empty($elem['product_list']))
            {
                $productList = is_array($elem['product_list'])? $elem['product_list'] :json_decode($elem['product_list'], true);
                $productFieldDataList = array_merge($productFieldDataList, $productList);

                foreach ($productList??[] as $item) {
                    $this->buildFileIds($item, $fileIds);
                    $this->buildFileIds($item['external_field_data']??[], $fileIds);
                    $platformSkuId = $item['platform_product_info']['platform_sku_id'] ?? 0;
                    array_push($platformSkuIds, $platformSkuId);
                    if(!empty($item['product_type']) && $item['product_type'] == ProductConstant::PRODUCT_TYPE_COMBINE && !empty($item['unique_id'])){
                        $combineUniqueId[] = $item['unique_id'];
                    }
                }
                $skuIds = array_merge($skuIds, array_column($productList, 'sku_id'));
                $uniqueIds = array_merge($uniqueIds, array_column($productList, 'unique_id'));
            }

            if (isset($elem['cost_list']) && !empty($elem['cost_list']))
            {
                $costItemList = is_array($elem['cost_list'])? $elem['cost_list'] :json_decode($elem['cost_list'], true);
                foreach ($costItemList??[] as $item) {
                    $costItemRelationIds[] = $item['cost_item_relation_id'] ?? 0;
                }
            }
        }

        $cashCollectionMap = [];
        $approvalFlowInfoMap = [];
        $erpStatusInfoMap = [];
        if (!empty($orderIds))
        {
            if (in_array('cash_collection', $this->specifyFields))
            {
                $cashCollectionMap = \common\library\cash_collection\Helper::getReferStatsMap($this->clientId, CashCollection::REFER_TYPE_ORDER, $orderIds);
            }

            if (in_array('approval_status', $this->specifyFields))
            {
                $approvalFlowInfoMap = \common\library\approval_flow\Helper::getApprovalProgressInfo($this->moduleType, $orderIds, false);
            }

            if (in_array('erp_status', $this->needExportInvoiceFields))
            {
                $erpStatusInfoResult = (new OrderErpExternalApi())->orderErpInfo($this->clientId, $orderIds);
                $erpStatusInfoMap = array_column($erpStatusInfoResult, null, 'order_id');
            }

        }

        $countryCodes = array_unique($countryCodes);
        $aliStoreIds = array_unique($aliStoreIds);
        $sellerAccountIds = array_unique($sellerAccountIds);

        $sellerAccountMap = [];
        $aliStoreMap = [];

        if($sellerAccountIds){
            $sellerAccountMap = AlibabaStoreAccountSyncHelper::getStoreAccountInfoMap($this->clientId,$sellerAccountIds);
        }
        if($aliStoreIds){
            $aliStoreMap = AlibabaStoreService::getStoreInfoMaps($this->clientId,[],true);
        }
        if($countryCodes){
            $countryList = \CountryService::getCountryListFromAlpha2($countryCodes);
            if($countryList){
                $countryMaps = array_combine(array_column($countryList,'alpha2'),$countryList);
            }
        }

        $productSkuMap = [];
        $skuIds = array_unique($skuIds);
        if (!empty($skuIds)) {
            $skuApi = new SkuAPI($this->clientId);
            $productSkuMap = array_column($skuApi->items($skuIds), null,'sku_id');
        }

        // 子产品信息查询
        $subProductMap = [];        // 父产品unique_id => 子产品名称、编号和单位组合数量
        if($this->needSubProductFlag && !empty($combineUniqueId)){
            $recordList = new InvoiceProductRecordList($this->opUserId);
            $recordList->setInvoiceProductIds($combineUniqueId);
            $recordList->setFields(['id', 'combine_product_config']);
            $recordList->setEnableFlag(true);
            $combineConfigList = array_column($recordList->find(), 'combine_product_config','id');
            $combineConfigMap = [];
            foreach($combineConfigList as $combineRecordId => $combineConfig){
                $combineConfig = !is_array($combineConfig) ? json_decode($combineConfig, true) : $combineConfig;
                $combineConfig = array_column($combineConfig, 'count', 'sub_sku_id');
                $combineConfigMap[$combineRecordId] = $combineConfig;
            }

            $recordList = new InvoiceProductRecordList($this->opUserId);
            $recordList->setProductSkuTableAlias("sku");
            $recordList->setCombineRecordId($combineUniqueId);
            $recordList->setAlias("p");
            $recordList->setEnableFlag(true);
            $recordList->setFields(['p.id as id', 'p.combine_record_id','p.product_name', 'p.sku_id','sku.sku_code']);
            $subProductList = $recordList->find();
            foreach($subProductList as $subProduct){
                if(!isset($combineConfigMap[$subProduct['combine_record_id']]) || !isset($combineConfigMap[$subProduct['combine_record_id']][$subProduct['sku_id']])){
                    continue;
                }
                if(!isset($subProductMap[$subProduct['combine_record_id']])){
                    $subProductMap[$subProduct['combine_record_id']] = [];
                }
                $subProduct['combine_count'] = $combineConfigMap[$subProduct['combine_record_id']][$subProduct['sku_id']];
                $subProductMap[$subProduct['combine_record_id']][] = $subProduct;
            }
        }

        $platformProductSkuMap = [];
        $platformSkuIds = array_filter(array_unique($platformSkuIds));
        if (!empty($platformSkuIds)) {
            $platSkuApi = new PlatformSkuAPI($this->clientId);
            $platformProductSkuMap = array_column($platSkuApi->productSkusByIds($platformSkuIds, true), null,'platform_sku_id');
        }

        $capitalAccountMap = [];
        $capitalAccountIds = array_unique(array_filter($capitalAccountIds));
        if (!empty($capitalAccountIds)) {
            $capitalAccountFilter = new CapitalAccountFilter($this->clientId);
            $capitalAccountFilter->capital_account_id = $capitalAccountIds;
            $capitalAccountFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
            $capitalAccountFilter->select(['capital_account_id', 'name', 'capital_name', 'capital_bank', 'bank_account', 'address', 'remark']);
            $capitalAccountList = $capitalAccountFilter->rawData();
            $capitalAccountMap = array_column($capitalAccountList, null, 'capital_account_id');
        }

        $costItemRelationMap = [];
        $costItemRelationIds = array_unique(array_values(array_filter($costItemRelationIds)));
        if (!empty($costItemRelationIds)) {
            $costItemInvoiceRelationList = CostItemInvoiceRelationApi::getCostItemRelationByInvoice(\Constants::TYPE_ORDER);
            $costItemRelationMap = array_column($costItemInvoiceRelationList, null, 'relation_id');
        }

        $map = [
            'user' => $this->getUserMap($userIds),
            'status' => $this->getInvoiceStatusMap($statusIds),
            'company' =>$this->getCompanyMap($companyIds),
            'customer' => $this->getCustomerMap($customerIds),
            'opportunity' => $this->getOpportunityMap($opportunityIds),
            'department' => $this->getDepartmentMap($departmentIds),
            'cashCollection' => $cashCollectionMap,
            'approval' => $approvalFlowInfoMap,
            'country' => $countryMaps,
            'seller_account_map' =>$sellerAccountMap,
            'ali_store_map' => $aliStoreMap,
            'product_sku' => $productSkuMap,
            'platform_product_sku' => $platformProductSkuMap,
            'capital_account' => $capitalAccountMap,
            'cost_item_relation' => $costItemRelationMap,
            'fileInfoMap' => $this->getFileInfoMap($fileIds),
            'subProductMap' => $subProductMap,
            'erp_status_map' => $erpStatusInfoMap,
        ];

        $relationCompanyFieldMap = $this->buildRelationCompanyData($externalFieldDataList);
        $map = array_merge($map, $relationCompanyFieldMap);

        $relationProductFieldMap = $this->buildRelationProductData($productFieldDataList);
        $map = array_merge($map, $relationProductFieldMap);

        $this->setMapData($map);
        parent::buildMapData();
    }

    protected function getFileInfoMap($fileIds){
        $fileInfoMap = [];
        $objArray = \UploadFile::findByIds($fileIds);
        if (!empty($objArray)) {
            foreach ($objArray as $obj) {
                $upload = new \AliyunUpload();
                $upload->loadByObject($obj);
                $fileInfoMap[$obj->file_id] = array(
                    'file_id' => $upload->getFileId(),
                    'file_url' => $upload->getFileUrl(),
                    'download_url' => $upload->generatePresignedUrl(),
                    'preview_url' => $upload->getPreview(),
                    'file_name' => $upload->getFileName(),
                    'file_ext' => $upload->getFileExt(),
                    'file_size' => $upload->getFileSize(),
                );
            }
        }
        return $fileInfoMap;
    }

    protected function getFileUrlFromFileInfoMap($fieldValue){
        if(is_string($fieldValue) || empty($fieldValue)){
            return $fieldValue;
        }

        $fileInfoMap = $this->mapData['fileInfoMap'];
        if(isset($fieldValue[0])){
            $fileId = $fieldValue[0]['file_id']??$fieldValue[0]['id'] ??0;
        }else{
            $fileId = $fieldValue['file_id']??$fieldValue['id']??0;
        }

        $fileInfo =  $fileInfoMap[$fileId] ?? [];
        return $fileInfo['file_url'] ?? '';
    }

    protected function buildFileIds($list, &$fileIds){
        $fieldMap = $this->getFieldMap();
        foreach($list as $fieldId=>$fieldValue){
            if(!isset($fieldMap[$fieldId])){
                continue;
            }
            if(
                in_array($fieldMap[$fieldId]['field_type'], [CustomFieldService::FIELD_TYPE_IMAGE, CustomFieldService::FIELD_TYPE_ATTACH])
                || in_array($fieldMap[$fieldId]['relation_field_type'], [CustomFieldService::FIELD_TYPE_IMAGE, CustomFieldService::FIELD_TYPE_ATTACH])
            ){
                if(is_string($fieldValue)){
                    continue;
                }
                // 判断fieldValue是否为多个文件
                if(isset($fieldValue[0])){
                    foreach($fieldValue as $value){
                        if(!isset($value['file_id'])){
                            continue;
                        }
                        $fileIds[] = $value['file_id'];
                    }
                }else{
                    if(!isset($fieldValue['file_id'])){
                        continue;
                    }
                    $fileIds[] = $fieldValue['file_id'];
                }
            }
        }
    }

    protected function buildRelationCompanyData($externalDataList)
    {
        $map = [];

        $fieldMap = $this->getFieldMap();
        $groupIds = [];
        $categoryIds = [];
        $trailStatus = [];
        $originIds = [];
        foreach ($externalDataList as $item)
        {
            foreach ($item as $field => $value)
            {
                $fieldInfo = $fieldMap[$field] ?? [];
                $fieldType = intval(($fieldInfo['field_type']?? 0));

                //非关联字段不处理
                if ($fieldType != CustomFieldService::FIELD_TYPE_FIELDS)
                    continue;

                switch ($fieldInfo['relation_field'])
                {
                    case 'group_id':
                        $groupIds[] = $value;
                        break;
                    case 'category_ids':
                        foreach ($value?:[] as $ids) {
                            $categoryIds = array_merge($categoryIds, $ids);
                        }
                        break;
                    case 'trail_status':
                        $trailStatus[] = $value;
                        break;
                    case 'origin':
                    case 'origin_list':
                        $originIds = array_merge($originIds, (array)$value);
                        break;
                }
            }
        }

        if (!empty($groupIds))
            $map['company:group_names'] = Helper::getGroupNameMap($this->clientId, \Constants::TYPE_COMPANY, $groupIds);

        if (!empty($originIds))
            $map['company:origin_names'] = \CustomerOptionService::getOriginNameMap($this->clientId, $originIds);

        if (!empty($trailStatus))
        {
            $trailStatusMap = \CustomerOptionService::getStatusMap($this->clientId, $trailStatus);
            $map['company:trail_status'] = array_column($trailStatusMap, 'item_name', 'item_id');
        }

        if (!empty($categoryIds))
            $map['company:category_names'] = \Category::getNameMap('zh', $categoryIds);

        $map['company:scale'] = [];
        foreach (\CustomerOptionService::SCALE_MAP as $key => $item)
        {
            if ($item['min'] <=1 )
            {
                $map['company:scale'][$key] = "<{$item['max']}";
                continue;
            }

            if ($item['max'] >= 10000)
            {
                $map['company:scale'][$key] = "<{$item['min']}";
                continue;
            }

            $map['company:scale'][$key] = "{$item['min']}~{$item['max']}";
        }

        //客户标签
        $api = new TagApi($this->clientId, \Constants::TYPE_COMPANY, 0);
        $api->getParams()->setIgnoreOwnerUserId(true);
        $map['company:cus_tag'] = array_column($api->list(0), 'tag_name', 'tag_id');

        return $map;
    }

    protected function buildRelationProductData($productList)
    {
        $map = [];

        $fieldMap = $this->getFieldMap();
        $groupIds = [];

        foreach ($productList as $item)
        {
            if (!isset($item['external_field_data']) || empty($item['external_field_data']))
            {
                continue;
            }

            if(!is_array($item['external_field_data'])){
                $item['external_field_data'] = json_decode($item['external_field_data'], true);
            }

            foreach ($item['external_field_data'] as $field => $value)
            {
                //脏数据处理
                if (!is_numeric($field))
                    continue;

                $fieldInfo = $fieldMap[$field] ?? [];
                $fieldType = intval(($fieldInfo['field_type']?? 0));

                //非关联字段不处理
                if ($fieldType != CustomFieldService::FIELD_TYPE_FIELDS)
                    continue;

                switch ($fieldInfo['relation_field']) {
                    case 'group_id':
                        $groupIds[] = $value;
                        break;
                }
            }
        }

        if (!empty($groupIds))
        {
            $map['product:group_names'] = Helper::getGroupNameMap($this->clientId, \Constants::TYPE_PRODUCT, $groupIds);
        }

        $skuIds = array_column($productList, 'sku_id');
        if (!empty($skuIds = array_unique($skuIds))){
            $skuApi = new SkuAPI($this->clientId, $this->opUserId);
            $skuMap = array_column($skuApi->items($skuIds), null,'sku_id');
        }
        $map['product_sku'] = $skuMap ?? [];

        return $map;
    }

    protected function getCompanyMap(array $companyIds = [])
    {
        $companyMap = [];
        $companyIds = array_filter($companyIds);
        if (!empty($companyIds))
        {
            $companyIds = array_values($companyIds);
            $list = new CompanyList($this->opUserId);
            $list->setSkipPrivilege(true);
            $list->setCompanyIds($companyIds);
            $list->setFields(['company_id', 'name']);
            $companyList = $list->find();

            foreach ($companyList as $elem) {
                $companyMap[$elem['company_id']] = $elem['name'];
            }
        }

        return $companyMap;
    }

    protected function getCustomerMap(array $customerIds = [])
    {
        $customerMap = [];
        if (empty($customerIds))
            return [];

        $customerIds = array_filter($customerIds);
        if (!empty($customerIds)) {
            $customerIds = array_values($customerIds);

            $list = new CustomerList($this->clientId);
            $list->setCustomerId($customerIds);
            $list->setFields(['customer_id', 'name', 'email']);
            $customerList = $list->find();

            foreach ($customerList as $elem) {

                if (empty($elem['name'])) $elem['name'] = $elem['email'];
                $customerMap[$elem['customer_id']] = $elem;
            }
        }

        return $customerMap;
    }

    protected function getDepartmentMap(array $departmentIds = [])
    {
        $departmentMap = [];
        $departmentIds = array_filter($departmentIds);
        if (!empty($departmentIds)) {
            $departmentService = new DepartmentService($this->clientId);
            $departmentMap = $departmentService->batchGetDepartmentListForIds($departmentIds);
        }
        return $departmentMap;
    }

    protected function getUserMap(array $userIds = [])
    {
        $userMap = [];
        $userIds = array_filter($userIds);
        if( !empty($userIds) )
        {
            $userList =  \common\library\account\Helper::getBatchUserInfo($this->clientId,$userIds);
            $userList =array_map(function ($elem){
                return ['user_id'=>$elem->user_id,'nickname'=>$elem->nickname,'avatar'=>$elem->avatar,'email'=>$elem->email];
            },$userList);

            $userMap = array_combine(array_column($userList,'user_id'),$userList);
        }
        return $userMap;
    }

    protected function getOpportunityMap(array $opportunityIds = [])
    {
        $opportunityMap = [];
        $opportunityIds = array_filter($opportunityIds);
        if (!empty($opportunityIds))
        {
            $opportunityIds = array_values($opportunityIds);
            $list = new OpportunityList($this->clientId);
            $list->setViewingUserId($this->opUserId);
            $list->setUserId(0);
            $list->setSkipPermissionCheck(true);
            $list->setOpportunityIds($opportunityIds);
            $list->setFields(['opportunity_id', 'name']);
            $opportunityList = $list->find();

            foreach ($opportunityList as $elem) {
                $opportunityMap[$elem['opportunity_id']] = $elem;
            }
        }

        return $opportunityMap;
    }

    public function getOriginMap(array $originIds = [])
    {
        $originMap = [];
        $originIds = array_filter($originIds);
        $api = new OriginApi($this->clientId);
        if (!empty($originIds))
        {
            $findOriginIds = [];
            foreach ($originIds as $originId)
            {
                //系统来源
                if (isset($api->getExtraDataMap()[$originId]))
                {
                    $originMap[$originId] = \Yii::t('customer', $api->getExtraDataMap()[$originId]);
                    continue;
                }
                $findOriginIds[] = $originId;
            }

            if ($findOriginIds)
            {
                /*
                $originList = new OriginList($this->clientId);
                $originList->setItemId($findOriginIds);
                $originList = $originList->find();
                $originList = array_column($originList,'item_name','item_id');
                */

                $originList = $api->getNameMap($findOriginIds);

                $originMap = $originMap+$originList;
            }
        }
        return $originMap;
    }

    protected function getInvoiceStatusMap(array $statusIds = [])
    {
        $statusMap = [];
        $statusIds = array_filter($statusIds);
        if (!empty($statusIds)) {
            $statusList = (new InvoiceStatusService($this->clientId, $this->moduleType))->baseInfoList(false, $statusIds, false);
            $statusMap = array_column($statusList, 'name', 'id');
        }

        return $statusMap;
    }

    public function getImageUrlMap(array $fileIds = [])
    {
        $fileIds = array_values(array_unique(array_filter($fileIds)));
        if (empty($fileIds))
            return [];

        $map = [];
        $objArray = \UploadFile::findByIds($fileIds);
        foreach ($objArray as $obj) {
            $upload = new \AliyunUpload();
            $upload->loadByObject($obj);

            $map[$upload->getFileId()] = $upload->getFileUrl();
        }
        return $map;
    }

    protected function getFieldMap($refresh = false)
    {
        if ($this->fieldMap === null || $refresh == true)
        {
            $customFieldList = new FieldList($this->clientId);
            $customFieldList->setType($this->moduleType);
            $customFieldList->setEnableFlag(null);
            $customFieldList->setFields(['id','is_list','type', 'name', 'field_type', 'relation_field', 'relation_field_type','relation_origin_type','relation_origin_field','relation_origin_field_type']);
//            $customFieldList->getFormatter()->setShowRelationFieldInfo(true);
            $fieldMapList = $customFieldList->find();
            $fieldMap = array_column($fieldMapList, null, 'id');

            if (isset($fieldMap['customer_emial'])) {
                $fieldMap['customer_email'] = $fieldMap['customer_emial'];
                $fieldMap['customer_email']['id'] = 'customer_email';
            }

            $this->fieldMap = $fieldMap;
        }

        return $this->fieldMap;
    }

    private function processString($str)
    {
        if (is_string($str))
        {
            $str = \Util::replaceBrToLinebreak($str);
            $str = \Util::unEmoji($str);
        }

        return $str;
    }

    protected function processCostItem(array $costList)
    {
        //统计相同费用的金额
        $costSumMap = [];
        $costItemRelationIds = array_unique(array_column($costList, 'cost_item_relation_id'));
        if ($costItemRelationIds) {
            $costSumMap = array_combine($costItemRelationIds, array_fill(0, count($costItemRelationIds), 0));
            foreach ($costList as $item) {
                if (!empty($item['cost_item_relation_id']??0)) {
                    $costSumMap[$item['cost_item_relation_id']] += $item['cost'] ?? 0;
                }
            }
        }

        foreach ($costList as &$costItem)
        {
            if (!empty($costItem['cost_item_relation_id']??0)) {
                $costItem['cost']=$costSumMap[$costItem['cost_item_relation_id']];
            }

            foreach ($costItem as $field => &$value) {
                if ($field == 'cost_item_relation_id') {
                    $value = $this->getMapData('cost_item_relation', $value) ?? [];
                }
            }
            unset($value);
        }
        unset($costItem);

        return $costList;
    }

    protected function processProduct(array $productList)
    {
        foreach ($productList as &$product)
        {
            if (isset($product['["package_volume_subtotal"]']))
            {
                $product['package_volume_subtotal'] = $product['["package_volume_subtotal"]'];
                unset($product['["package_volume_subtotal"]']);
            }

            if (isset($product['["package_gross_weight_subtotal"]']))
            {
                $product['package_gross_weight_subtotal'] = $product['["package_gross_weight_subtotal"]'];
                unset($product['["package_gross_weight_subtotal"]']);
            }

            $platformSkuId = $product['platform_product_info']['platform_sku_id'] ?? 0;
            $productSku = $this->getMapData('product_sku', $product['sku_id']??0);
            $platformProductSku = $this->getMapData('platform_product_sku', $platformSkuId);

            $isPlatformProduct = !empty($product['platform_product_info']['platform_product_id']);
            $masterProductFlag = '';
            if (!empty($product['is_master_product'] ?? 0)) {
                $masterProductFlag = '【主】';
            } elseif (empty($product['is_master_product'] ?? 0) && !empty($product['master_id'] ?? 0)) {
                $masterProductFlag = '【配】';
            }
            /** 订单列表导出- 产品编号取值规则：平台产品-取匹配的 local_product_no下的：sku_code ;非平台产品-取本身的sku_id下的：sku_code */
            if ($isPlatformProduct) {
                $skuCode = '';
                if (!empty($product['platform_product_info']['platform_product_id']) && !empty($productSku['product_type'])) {
                    if ($productSku['product_type'] == ProductConstant::PRODUCT_TYPE_SPU) {
                        $skuCode = $productSku['product_no'] ?? '';
                    } else {
                        $skuCode = $productSku['sku_code'] ?? '';
                    }
                }
                $product['product_no'] = $masterProductFlag . $skuCode;
                $product['third_product_id'] = $product['platform_product_info']['third_product_id'];
            }else{
                $product['product_no'] = $masterProductFlag. ($platformProductSku['third_sku_code'] ?? $productSku['sku_code'] ?? '');
                $product['third_product_id'] = '';
            }

            foreach ($product as $field => &$value) {
	            try {

		            if ($field == 'external_field_data') {
                        if(!is_array($value)){
                            $value = json_decode($value, true);
                        }
			            foreach ($value as $productField => &$item) {
				            $item = $this->processProductField($productField, $item);
			            }
			            continue;
		            }

                    // 多加一个判断empty($product['sku_id']),关联本地产品后下载用本地产品的
		            if ($field == 'sku_id' && !empty($product['platform_product_info']['platform_product_id'])
                        && empty($product['sku_id'])) {
			            $value = [];
			            foreach ($product['sku_attributes'] ?? [] as $k => $attr) {
				            $value[] = "{$k}:{$attr}";
			            }
			            $value = implode(',', $value);
			            continue;
		            }

                    if ($field == 'package_count' && !empty($product['count']) && !empty($product['count_per_package'])) {
                        $value = ($product['count_per_package'] != 0) ? ceil($product['count'] / $product['count_per_package']) : 0;
                    }

		            $value = $this->processProductField($field, $value);

	            } catch (\Throwable $exception) {

		            echo json_encode($exception->getTraceAsString());
		            \LogUtil::info($exception->getTraceAsString());
		            continue;
	            }
            }

            // 子产品填充
            $product['sub_product'] = $this->getMapData('subProductMap', $product['unique_id']??0);
            unset($item);
            unset($value);
        }
        unset($product);

        return $productList;
    }

    protected function processByFieldType($field, $value)
    {
        $fieldMap = $this->getFieldMap();
        $fieldInfo = $fieldMap[$field] ?? [];
        $fieldType = intval($fieldInfo['field_type'] ?? 0);

        if(in_array($fieldType, [CustomFieldService::FIELD_TYPE_QUOTE_FIELDS, CustomFieldService::FIELD_TYPE_FIELDS])){
            $fieldType = intval($fieldInfo['relation_origin_field_type'] ?? 0);
        }

        switch ($fieldType)
        {
            case CustomFieldService::FIELD_TYPE_ATTACH:
            case CustomFieldService::FIELD_TYPE_IMAGE:
                //图片附件暂不支持
                $value = $this->getFileUrlFromFileInfoMap($value);
                break;
            case CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT:
                $value = is_array($value) ? implode(';', $value) : $value;
                break;
            case CustomFieldService::FIELD_TYPE_OTHER:
                $value = json_encode($value);
                break;
            default:
                if(is_array($value)){
                    if(empty($value) || isset($value[0])){
                        $value = implode(';', $value);
                    }else{
                        $value = json_encode($value);     // 粗暴点，先保证不报错
                    }
                }

                break;
        }

        return $this->processString($value);
    }

    protected function processByField($field, $value, $data)
    {

        switch ($field)
        {
            case 'company_id':
                $value = $this->getMapData('company', $value) ?? '';
                break;

            case 'opportunity_id':
                $value = $this->getMapData('opportunity', $value)['name'] ?? '';
                break;
            case 'status':
                $value = $this->getMapData('status', $value)??'';
                break;
            case 'customer_id':
                $value = $this->getMapData('customer', $value)['name'] ?? '';
                break;
            case 'ali_status_id':
                $value =  $data['ali_status_name'] ? \Yii::t('invoice', $data['ali_status_name']):"";
                break;
            case 'ali_store_id':
                $value = $this->getMapData('ali_store_map', $value)['store_name']??'';
                break;
            case 'source_type':
                $value = \common\library\invoice\Helper::getOrderSourceTypeMap()[$value]??'';
                break;
            case 'seller_account_id':
                $value = $this->getMapData('seller_account_map', $value)['seller_email']??'';
                break;
            case 'country':
                $countryData = $this->getMapData('country', $value)??'';
                if($countryData){
                    $value = $countryData['country_ename']."({$countryData['country_name']})";
                }else{
                    $value = "";
                }
                break;
            case 'archive_type':
                $archiveTypeMaps = \common\library\invoice\Helper::getOrderArchiveTypeMaps();
                $value = isset($archiveTypeMaps[$value]) && isset($archiveTypeMaps[$value]['name'])?$archiveTypeMaps[$value]['name']:'';
                break;
            case 'user_id':
            case 'handler':
                $names = [];
                foreach ($value as $userId) {
                    $names[] = $this->getMapData('user', $userId)['nickname']??'';
                }
                $value = implode(';', $names);
                break;
            case 'tax_refund_type':
                $value =  OmsConstant::ORDER_TAX_REFUND_TYPE_MAP[$value] ?? "";
                break;
            case 'create_user':
            case 'update_user':
                $value = $this->getMapData('user', $value)['nickname']??'';
                break;
            case 'users':
                $names = [];
                foreach ($value as $handler) {
                    $name = $this->getMapData('user', $handler['user_id'])['nickname']??'';
                    $names[] = "{$name} ({$handler['rate']}%)";
                }
                $value = implode(';', $names);
                break;

            case 'departments':
                $names = [];
                foreach ($value as $department) {
                    $name = ($department['name'] ?? '') ?: $this->getMapData('department', $department['department_id'])['name']??'我的企业';
                    $departmentRate = $department['rate'] ?? 0;
                    $names[] = "{$name} ({$departmentRate}%)";
                }
                $value = implode(';', $names);
                break;
            case 'create_time':
            case 'update_time':
            case 'account_date':
                $formatValue = date('Y-m-d', strtotime($value));
                $value = $formatValue == '1970-01-01' ? '' : $formatValue;
                break;
            case 'last_sync_time':
                $value =  isset($data['last_sync_time']) && date('Y-m-d',strtotime($data['last_sync_time'])) != '1970-01-01' ? $data['last_sync_time'] : '';
                break;
//            case 'exchange_rate':
//                $currency = $this->getMainCurrency();
//                if ($currency == ExchangeRateService::USD) {
//                    $value = $data['exchange_rate_usd'];
//                }
//                $value = sprintf('%.2f',$value);
//                break;
            case 'cost_list':
                break;
            case 'file_list':
                break;
            default:
                $value = $this->processByFieldType($field, $value);
                break;
        }


        $value = $this->processString($value);
        return $value;
    }

    protected function processRelationProductField($field, $value)
    {
        switch ($field)
        {
            case 'group_id':
                $value = $this->getMapData('product:group_names', $value);
                break;
            case 'fob':
                $data = [
                    $value['price_currency']??'',
                    $value['price_min']??'',
                    '-',
                    $value['price_max']??'',
                    $value['price_unit']??'',
                ];
                $value = implode(' ', $data);
                break;

            case 'minimum_order_quantity':

                if (is_array($value))
                {
                    $data = [$value['quantity']??'',$value['quantity_unit']??''];
                    $value = implode(' ', $data);
                }
                break;

            case 'cost_with_tax':
                $data = [
                    'cost_currency' => $value['cost_currency'] ?? '',
                    'cost' => $value['cost'] ?? '',
                ];
                $value = implode(' ', $data);
                break;

            case 'package_size':
                $packageSize = [
                    'package_size_length' => 'L:'.($value['package_size_length'] ?? 0).'cm',
                    'package_size_weight' => 'W:'.($value['package_size_weight'] ?? 0).'cm',
                    'package_size_height' => 'H:'.($value['package_size_height'] ?? 0).'cm',
                ];
                $value = implode(' ', $packageSize);
                break;

//            case 'vat_rate':
//            case 'tax_refund_rate':
            case 'gross_profit_margin':
                $value .= '%';
                break;


	        case 'info_json':

		        try {

			        if (is_array($value)) {

//			        array_walk($value, function (&$item) {
//
//				        $item = $item['name'] . ': ' . $item['value'] ?? '--';
//			        });
//
//			        $value = implode('', $value);
                        $value = ProductHelper::formatInfoJsonString($value);
                    }

		        } catch (\Throwable $throwable) {

			        \LogUtil::info('产品属性格式化失败，格式化前value：' . json_encode($value));

			        $value = '';
		        }

		        break;

        }
        $value = $this->processString($value);
        return $value;
    }

    protected function processProductField($field, $value)
    {
        $fieldInfo = $this->getFieldMap()[$field] ?? [];
        $fieldType = intval($fieldInfo['field_type'] ?? 0);

        //关联字段
        if ($fieldType == CustomFieldService::FIELD_TYPE_FIELDS)
        {
            $field = $fieldInfo['relation_field'] ?? $field;
            return $this->processRelationProductField($field, $value);
        }

        switch ($field)
        {
            case 'group_id':
                $value = $this->getMapData('product:group_names', $value);
                break;
            case 'fob':
                $data = [
                    $value['price_currency']??'',
                    $value['price_min']??'',
                    '-',
                    $value['price_max']??'',
                    $value['price_unit']??'',
                ];
                $value = implode(' ', $data);
                break;

            case 'minimum_order_quantity':

                $data = [$value['quantity']??'',$value['quantity_unit']??''];
                $value = implode(' ', $data);
                break;

            case 'package_size':
                $packageSize = [
                    'package_size_length' => 'L:'.($value['package_size_length'] ?? 0).'cm',
                    'package_size_weight' => 'W:'.($value['package_size_weight'] ?? 0).'cm',
                    'package_size_height' => 'H:'.($value['package_size_height'] ?? 0).'cm',
                ];
                $value = implode(' ', $packageSize);
                break;

            case 'gross_profit_margin':
                $value .= '%';
                break;

            case 'product_image':
                $value = $this->getFileUrlFromFileInfoMap($value);
                break;

            case 'sku_id':
                $attrInfos = $this->getMapData('product_sku', $value)['attributes_info'] ?? [];
                foreach ($attrInfos as $attr) {
                    $attrStr[] = ($attr['item_name'] ?? '').': '.($attr['value']['item_name'] ?? '');
                }
                $value = implode('; ', $attrStr ?? []);
                break;

            default:
                $value = $this->processByFieldType($field, $value);
                break;
        }

        $value = $this->processString($value);
        return $value;
    }

    protected function processLinkStatus($value) {
        $linkStatus = array_map('intval', explode(",", trim($value, "{}")));
        $orderLink = OrderLink::make($linkStatus);
        $data = [];

        foreach ($orderLink->getLink() as $link) {
            $data[$link->constant()] = $link->currentStatus()->define();
        }

        $fieldMap = [
            Constant::ORDER_LINK_END => 'end_status',
            Constant::ORDER_LINK_STOCK_UP => 'stock_up_status',
            Constant::ORDER_LINK_OUTBOUND => 'shipping_status',
        ];

        $result = [];
        foreach ($data as  $linkConstant => $datum)
        {
            if (array_key_exists($linkConstant, $fieldMap)) {
                $result[$fieldMap[$linkConstant]] = $datum;
            }
        }

        // 根据有没有oms权限显示回款状态的不同文案
        if (PrivilegeService::getInstance($this->clientId)->hasFunctional(PrivilegeConstants::FUNCTIONAL_PURCHASE)) {
            $result['cash_collection'] = $data[Constant::ORDER_LINK_CASH_COLLECTION];
        }
        return $result;
    }

    protected function getMainCurrency()
    {
        if ($this->mainCurrency === null)
        {
            $this->mainCurrency = Client::getClient($this->clientId)->getMainCurrency();
        }
        return $this->mainCurrency;
    }

    /**
     * @param array $needExportInvoiceFields
     */
    public function setNeedExportInvoiceFields(array $needExportInvoiceFields)
    {
        $this->needExportInvoiceFields = $needExportInvoiceFields;
    }
}
