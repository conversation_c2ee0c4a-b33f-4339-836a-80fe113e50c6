<?php

/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 17/3/20
 * Time: 上午9:48
 */
namespace common\library\invoice;

use common\library\approval_flow\ApplyForm;
use common\library\approval_flow\ApprovalLogUtil;
use common\library\approval_flow\diff_format\MergeDiffFieldTrait;
use common\library\approval_flow\signal\interrupt\QuotationChangeStatusInterrupt;
use common\library\approval_flow\signal\interrupt\QuotationEditInterrupt;
use common\library\approval_flow\signal\trigger\CreateTrigger;
use common\library\approval_flow\signal\trigger\UpdateTrigger;
use common\library\approval_flow\traits\TraitApprovalFlow;
use common\library\compare\CompareFieldTrait;
use common\library\formula\AttributeFormulaTrait;
use common\library\lock\TraitReferLock;
use common\library\notification\Constant;
use common\library\notification\Notification;
use common\library\notification\PushHelper;
use common\library\oms\order\OrderConstants;
use common\library\performance_v2\PerformanceV2Constant;
use common\library\performance_v2\refer\QuotationPerformance;
use common\library\performance_v2\trigger\PerformanceTriggerTrait;
use common\library\product_v2\sku\SkuAPI;
use common\library\serial\GenerateService;
use common\library\util\PgsqlUtil;
use common\library\custom_field\CustomFieldService;
use common\library\history\invoice\QuotationCompare;
use common\library\invoice\status\InvoiceStatusService;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\trail\TrailConstants;
use common\library\workflow\trigger\BaseObjectTriggerTrait;
use common\models\client\Quotation as QuotationModel ;
use Constants;
use common\library\recycle;
use User;

/**
 * Class Quotation
 * @package common\library\invoice
 * 基本信息
 * @property integer quotation_id  id
 * @property string quotation_no  编号
 * @property string name  订单名称
 * @property integer client_id
 * @property integer create_user
 * @property integer $handler
 * @property integer user_id
 * @property integer status     订单状态
 * @property integer approval_status    审批状态
 * @property integer company_id
 * @property  integer company_group_id
 * @property integer customer_id
 * @property string create_time
 * @property string update_time
 * @property string delete_time
 * @property string quotation_date   报价单日期
 * @property integer account_flag   结束标志
 * @property string account_date   报价单结算日期
 * @property string remark      备注
 * @property string price_contract  价格条款
 * @property string price_contract_remark 价格条款说明
 * @property string currency        币种
 * @property string exchange_rate   汇率
 * @property double amount    报价单金额
 * @property double amount_rmb 折rmb金额
 * @property integer enable_flag
 * @property integer delete_flag
 * 基本信息自定义字段
 * @property array external_field_data  拓展字段
 * 交易产品
 * @property array product_list     产品列表
 * @property string product_total_count  产品总数量
 * @property string product_total_amount 产品总金额
 * @property integer package_gross_weight_amount 包装毛重总计
 * @property integer package_volume_amount 包装体积总计
 * 附加费用
 * @property object cost_list
 * @property string addition_cost_amount  附加费用总额
 * @property string addition_cost_amount_usd  附加费用总额usd
 * @property string addition_cost_amount_rmb  附加费用总额rmb
 * @property string quotation_contract  报价单条款
 * @property string bank_info   银行信息
 * @property string receive_remittance_remark   收汇方式说明
 * @property string insurance_remark    保险说明
 * 买方信息
 * @property string customer_phone  联系人电话
 * @property string customer_email  联系人邮箱
 * @property string customer_address  联系人地址
 * @property string customer_name   联系人名称
 * @property string company_phone   客户电话
 * @property string company_name    客户名称
 * @property string company_fax     客户传真
 * @property string company_address     客户地址
 * 货运信息
 * @property string transport_mode     运输方式
 * @property string package_remark     包装说明
 * @property string shipment_port     装运港口
 * @property string shipment_deadline_remark     装运期限说明
 * @property string shipment_deadline     装运期限
 * @property string marked     唛头
 * @property string more_or_less     溢短装
 * @property string target_port     目标港口
 * 附件列表
 * @property array file_list
 *
 * @method   Quotation getModel
 * @method QuotationFormatter getFormatter()
 */
class Quotation extends Invoice
{
    use AttributeFormulaTrait, BaseObjectTriggerTrait, TraitApprovalFlow, CompareFieldTrait, MergeDiffFieldTrait;
    use TraitReferLock;
    use PerformanceTriggerTrait;

    const EDIT_SOURCE_DEFAULT = 'edit';

    const PRODUCT_LIST_SORT_START_COUNT = 2;

    const PRODUCT_LIST_SORT_TYPE_DEFAULT = 0;
    const PRODUCT_LIST_SORT_TYPE_PRODUCT_MODEL_ASC = 1;
    const PRODUCT_LIST_SORT_TYPE_PRODUCT_MODEL_DESC = 2;

    protected $fillBuyer;

    //产品列表排序
    protected $productListSortType = 0;

    /**
     * 业务场景调用入口
     */
    protected $scene;

    /**
     * @var InvoiceStatusService
     */
    protected $statusService;

    public function __construct($userId,$id=null)
    {
        parent::__construct($userId,$id);

        $this->_formatter = new QuotationFormatter($this->userObj->getClientId());
        $this->_formatter->setNeedStrip(false);
        $this->_formatter->setUserId($userId);

        if($id){
            $this->loadById($id);
        }

        $this->statusService = new InvoiceStatusService($this->userObj->getClientId(),Constants::TYPE_QUOTATION);
        $this->invoiceType = \Constants::TYPE_QUOTATION;

        $this->setCompareFieldClientId($this->userObj->getClientId());
    }

    /**
     * @return string
     */
    public function getModelClass()
    {
        return QuotationModel::class;
    }

    /**
     * @return string
     */
    public function buildNo($quotation)
    {
        $generate = GenerateService::getInstance($this->userObj->getClientId(),Constants::TYPE_QUOTATION);
        $generate->dataAdaptor($quotation);
        $quotationNo =  $generate->generate();

        if ($this->checkQuotationNoExist($quotationNo)) {
            throw new \RuntimeException(\Yii::t('invoice', 'Duplicate quotation number,there is a problem with the custom rule Settings'));
        }
        return $quotationNo;
    }

    /**
     * @param $id
     */
    public function loadById($id)
    {

        if($model = QuotationModel::findById($id)){
            $this->setModel($model);
        }else{
            throw  new \RuntimeException(\Yii::t('invoice', '报价单不存在'));
        }
    }

    /**
     * @param $no
     */
    public function loadByNo($no)
    {
        if($model = QuotationModel::findByNO($this->userObj->getClientId(),$no)){
            $this->setModel($model);
            return $this;
        }else{
            throw  new \RuntimeException(\Yii::t('invoice', '报价单不存在'));
        }
    }

    /**
     * @return string
     */
    public function getCompareClass()
    {
        return QuotationCompare::class;
    }

    /**
     * 事件与线索映射
     * @return array
     */
    public function getEventTraitMap()
    {
        return [
            self::EVENT_AFTER_INSERT => TrailConstants::TYPE_QUOTATION_ADD,
            self::EVENT_AFTER_UPDATE => TrailConstants::TYPE_QUOTATION_EDIT,
            self::EVENT_AFTER_DELETE => TrailConstants::TYPE_QUOTATION_DEL,
            self::EVENT_AFTER_STATUS => TrailConstants::TYPE_QUOTATION_STATUS,
            self::EVENT_AFTER_RECOVER => TrailConstants::TYPE_QUOTATION_RECOVER,
        ];
    }

    /**
     * 获取主键ID
     * @return int
     */
    public function getPrimaryId()
    {
        return $this->quotation_id;
    }

    /**
     * 维护user_id=>创建人
     */
    public function refreshUserId()
    {
        $userIds = [$this->create_user];
        $this->user_id = $userIds;
    }

    // 获取报价单产品原图片ID字段信息
    protected function getQuotationProductImages($quotation_id)
    {
        $invoiceProductMap  = [];
        $invoiceProductList = new InvoiceProductRecordList($this->userId);
        $invoiceProductList->setFields(['id', 'product_images']);
        $invoiceProductList->setReferId($quotation_id);
        $invoiceProductList->setType(\Constants::TYPE_QUOTATION);
        $invoiceProductList->setEnableFlag(\Constants::ENABLE_FLAG_TRUE);
        $invoiceProductListData = $invoiceProductList->find();
        foreach ($invoiceProductListData as $invoiceProductListDatum) {
            if (!empty($invoiceProductListDatum['product_images']) && $invoiceProductListDatum['product_images'] != '{}') {
                $invoiceProductMap[$invoiceProductListDatum['id']] = json_decode($invoiceProductListDatum['product_images'], true);
            } else {
                $invoiceProductMap[$invoiceProductListDatum['id']] = [];
            }
        }
        return $invoiceProductMap;
    }

    /**
     * 创建订单
     * @param array $formatData
     * @param int $fillBuyer 是否自动填充买方信息
     * @return bool
     * @throws \ProcessException
     */
    public function create(array $formatData, int $fillBuyer = 0)
    {
        $this->fillBuyer = $fillBuyer;
        $attributes = $this->getFormatter()->unFormatInfoGroup($formatData);
        $attributes = $this->refreshExchangeRate($attributes);

        foreach ($attributes as $field =>$value){
            $this->$field = $value;
        }

        $this->client_id = $this->userObj->getClientId();
        $this->create_user = $this->userId;

        $group = array_column($formatData,'id');
        //新建的时候，自动填上没有设置组的默认值
        $this->fillDefaultFields(array_diff(
            [
                CustomFieldService::QUOTATION_GROUP_BASIC,
                CustomFieldService::QUOTATION_GROUP_PRODUCT,
                CustomFieldService::QUOTATION_GROUP_FEE,
                CustomFieldService::QUOTATION_GROUP_BUYER,
                CustomFieldService::QUOTATION_GROUP_TRANSPORT,
            ],
            $group
        ));

        return $this->save();
    }

    /**
     * 编辑报价单
     * @param array $formatData
     * @return bool
     */
    public function edit(array $formatData)
    {

        if(!$this->isNew() && $this->beforeRunApprovalFlow()) {
            if ($apply_form_id = \common\library\approval_flow\Helper::getApprovingApplyFormId($this->client_id,
                $this->quotation_id, \common\library\approval_flow\Constants::ENTITY_TYPE_QUOTATION)) {
                $this->bindingApplyForm = new ApplyForm($apply_form_id);

                $this->mergeAttributesFromApprovalDiff($this->bindingApplyForm, $this->bindingApplyForm->apply_user_id);
            }
        }

        $group = array_column($formatData,'id');
        $this->getFormatter()->setShowGroupIds($group);
        $this->scene = self::EDIT_SOURCE_DEFAULT;

        $attributes = $this->getFormatter()->unFormatInfoGroup($formatData);
        // 编辑保留原来的图片
        if (!empty($attributes['product_list'])) {
            $invoiceProductMap = $this->getQuotationProductImages($this->quotation_id);
            foreach ($attributes['product_list'] as &$productItem) {
                if (!empty($productItem['unique_id'])
                    && !isset($productItem['product_images'])
                    && !empty($invoiceProductMap[$productItem['unique_id']])
                ) {
                    $productImages = $invoiceProductMap[$productItem['unique_id']];
                    $productItem['product_images'] = $productImages;
                }
            }
            unset($productItem);
        }

        $attributes = $this->refreshExchangeRate($attributes);

        unset($attributes['create_time']);
        unset($attributes['create_user']);
        unset($attributes['status']);

        $external_field_data = $this->external_field_data??[];
        foreach ($attributes as $field =>$value){
            if( $field == 'external_field_data'){
                foreach ( $value as $k => $v){
                    $external_field_data[$k] = $v;
                }
                $this->external_field_data = $external_field_data;
                continue;
            }
            $this->$field = $value;
        }

        //自动填上买方信息,如果已经编辑了买方信息就不填充
        if( !in_array(CustomFieldService::QUOTATION_GROUP_BUYER,$group) )
            $this->fillBuyer = 1;



        return $this->save();
    }

    /**
     * 改变订单状态
     * @param $statusId
     */
    public function changeStatus($statusId)
    {
        $this->eventType = self::EVENT_AFTER_STATUS;
        if (!$this->statusService->isStatusExist($statusId)) {
            throw  new \RuntimeException(\Yii::t('status','状态不存在'));
        }

        $this->status = $statusId;
        $this->update_time = date('Y-m-d H:i:s');

        if ($this->_oldAttributes['status'] != $this->status)
        {
            //尝试触发审批流的中断
            $signal = new QuotationChangeStatusInterrupt(
                $this->userId,
                $this->quotation_id,
                \common\library\approval_flow\Constants::ENTITY_TYPE_QUOTATION,
                $this->name,
                $this->getUpdateFields(),
                [
                    'quotation_id' => $this->quotation_id,
                    'status_id' => $statusId,
                ],
                $this
                );

            $interrupt = $this->setIncludeApprovingConfig(true)->runApprovalFlow($signal);

            if($interrupt) {
                throw new \RuntimeException('',1700);
            }
        }
        $this->save();

    }

    /**
     * 修改订单审批状态
     * @param int $approvalStatus
     */
    public function changeApprovalStatus($approvalStatus)
    {
        $model = $this->getModel();

        $model->setIsNewRecord($this->isNew());

        $model->approval_status = $approvalStatus;
        $model->update_time = date('Y-m-d H:i:s');

        $result = $model->update(['approval_status','update_time']);
        if($result == false){
            throw  new \RuntimeException(\Yii::t('common', 'Save failed'));
        }

        $this->attributeChangeFields = ['approval_status'];
        $this->runWorkflow($this->client_id, $this->quotation_id);

        $this->_oldAttributes = $this->_attributes = $model->getAttributes();

    }


    /**
     * 修改备注
     * @param $remark
     */
    public function changeRemark($remark)
    {
        $model = $this->getModel();

        $model->setIsNewRecord($this->isNew());

        $model->remark = $remark;
        $model->update_time = date('Y-m-d H:i:s');

        $result = $model->update(['remark','update_time']);
        if($result == false){
            throw  new \RuntimeException(\Yii::t('common', 'Save failed'));
        }

        $this->_attributes = $model->getAttributes();
        $this->history();

        $this->attributeChangeFields = ['remark'];
        $this->runWorkflow($this->client_id, $this->quotation_id);

        $this->_oldAttributes = $this->_attributes;


    }

    /**
     * 保存之后处理
     */
    public function afterSave()
    {
        $eventType = $this->getEventType();
        $diffData = $this->history();
        if($eventType != self::EVENT_AFTER_UPDATE  || ($eventType  == self::EVENT_AFTER_UPDATE && !empty($diffData)) )
        {
            $this->trail($eventType);
            if( $this->company_id ){
                \common\library\customer\Helper::updateOrderTime($this->userObj->getClientId(), $this->userId, $this->company_id, $this->customer_id, 'refer_modified_quotation');
            }
        }
        $this->addProductRecord($eventType);

        $this->runWorkflow($this->client_id, $this->quotation_id);

        //审批流事件

        if($this->isNew()) {
            $signal = new CreateTrigger(
                $this->userId,
                $this->quotation_id,
                \common\library\approval_flow\Constants::ENTITY_TYPE_QUOTATION,
                $this->name,
                $this->getUpdateFields()
            );
            /**
             * tips:
             * 1 当不是前端未传递approval_flow_confirm的值这里只是适配当前的diff是否触发审批流
             * 2 当前端传递了确认值 这里会触发并启动审批流程
             * 3 由于过滤机制是依赖于sql 所以数据需要先持久化到数据 这就导致create和update 无论用户是否点击需要审批都会变化
             */
            self::$matchOnly = false;
            $this->runApprovalFlow($signal);
        }

        // 写入ES
        $this->handleEsIndex();

        //注意业绩计算要放在最下面
        $this->statistics($eventType);

        parent::afterSave();

    }

    protected function handleEsIndex()
    {
        $operateType = \Constants::QUOTATION_INDEX_TYPE_UPDATE;
        if($this->isNew()) {
            $operateType = \Constants::QUOTATION_INDEX_TYPE_CREATE;
        }
        \common\library\server\es_search\SearchQueueService::pushQuotationQueue($this->user_id, $this->client_id, [$this->quotation_id], $operateType);
    }

    /**
     * 报价单删除之后处理
     */
    public function afterDelete()
    {
        $eventType = $this->getEventType();

        recycle\API::add($this->client_id, $this->userId, recycle\Recycle::QUOTATION, [$this->quotation_id]);

        $this->trail($eventType);
        $this->statistics($eventType);
        $this->addProductRecord($eventType);
        $this->notify();

        \common\library\server\es_search\SearchQueueService::pushQuotationQueue($this->user_id, $this->client_id, [$this->quotation_id], \Constants::QUOTATION_INDEX_TYPE_DELETE);

        $performanceRecorder = $this->getReferPerformance();
        $performanceRecorder->setScene(PerformanceV2Constant::PERFORMANCE_RECORD_JOB_SCENE_DELETE);
        $this->runPerformanceRecord();
        parent::afterDelete();
    }

    protected function notify()
    {
        $userIds = $this->user_id;
        $user = User::getLoginUser();

        if( empty($userIds) ){
            return;
        }
        $orderUrl = '/quotation/detail/'. $this->quotation_id;
        foreach ($userIds as $userId) {
            $notification = new Notification($this->client_id, Constant::NOTIFICATION_TYPE_QUOTATION_DELETE);
            $notification->user_id = $userId;
            $notification->refer_id = $this->quotation_id;
            $notification->create_user_id = $this->userId;
            $notification->setSourceData([
                'quotation_id' => $this->quotation_id,
                'quotation_no' => $this->quotation_no,
                'quotation_name' => $this->name,
                'quotation_url' => $orderUrl,
                'op_user_name' => $user->getNickname()
            ]);
            PushHelper::pushNotification($this->client_id, $userId, $notification);
        }
        return ;
    }


    public function recover(){

        $this->setSkipManageable(true);

        $this->eventType = self::EVENT_AFTER_RECOVER;

        if ($this->checkQuotationNoExist($this->quotation_no)) {
            throw new \RuntimeException(\Yii::t('invoice', 'Quotation number existed, recovery is not supported'));
        }

        if(!$this->beforeSave()){
            return false;
        }

        $model = $this->getModel();

        //如果订单状态已被删除,就恢复到草稿状态
        if (!$this->statusService->isStatusExist($this->status)) {
            $status = $this->statusService->beginStatus();
            $model->status = $status['id'];
        }

        $model->setIsNewRecord($this->isNew());

        $model->enable_flag  = self::ENABLE_FLAG_TRUE;
        $model->delete_flag  = self::DELETE_FLAG_FALSE;
        $model->update_time = date('Y-m-d H:i:s');

        $result = $model->update(['status','enable_flag','delete_flag','update_time']);

        if($result == false){
            throw  new \RuntimeException(\Yii::t('common', 'Save failed'));
        }

        $this->_attributes = $model->getAttributes();

        $this->afterRecover();

        $this->runWorkflow($this->client_id, $this->quotation_id);

        $this->_oldAttributes = $this->_attributes;
        return true;

    }

    public function beforeSave()
    {
        $this->handlePrivilegeFields();

        if($this->isLock($this->quotation_id,\common\library\approval_flow\Constants::ENTITY_TYPE_QUOTATION) && $this->isOpenForApprovalFlow()) {
            $approverIds = array_column(\common\library\approval_flow\Helper::getReferApprovalInfos($this->client_id, $this->quotation_id, \common\library\approval_flow\Constants::APPROVAL_STATUS_APPROVING), 'approver');

            if (!in_array($this->userId, $approverIds)) {
                throw new  \RuntimeException(\Yii::t('approvalflow', "Current object is locked,can't be update"));
            }
        }
        //检查状态
        if($this->scene == self::EDIT_SOURCE_DEFAULT && $this->statusService->isEndingStatus($this->_oldAttributes['status'])){
            throw  new \RuntimeException(\Yii::t('invoice', '报价单已处于最终状态,不能再编辑'));
        }

        $time = date('Y-m-d H:i:s');
        $this->refreshTotal();
        $this->refreshAmountExchange();
        $this->refreshProductAmountExchange();
        $this->refreshAdditionAmountExchange();
        $this->refreshUserId();
        $this->refreshCompanyGroupId();
        $this->update_time = $time;

        if ($this->isNew()) {

            //判断用户是否有权限编辑报价单号
            if (!empty($this->_attributes['quotation_no']) && !Helper::hasFieldEditPermission('quotation_no', $this->client_id, $this->userId)) {
                throw new \RuntimeException(\Yii::t('invoice', 'No permission to edit quotation number'));
            }

            //草稿状态
            $beginStatus = $this->statusService->beginStatus();
            $this->status = $beginStatus['id'];

            $this->account_flag = 0;
            $this->create_time = $time;
            $this->enable_flag = 1;
            $this->delete_flag = 0;
            $this->approval_status = -1;
        } else {
            if (!empty($this->_attributes['quotation_no']) && $this->_attributes['quotation_no'] != $this->_oldAttributes['quotation_no'] && !Helper::hasFieldEditPermission('quotation_no', $this->client_id, $this->userId)) {
                throw new \RuntimeException(\Yii::t('invoice', 'No permission to edit quotation number'));
            }
        }

        if (!empty($this->_attributes['quotation_no'])) {

            //限制编号的长度
            if (mb_strlen($this->_attributes['quotation_no']) > 100) {
                throw new \RuntimeException(\Yii::t('invoice', 'The quotation number must not exceed 100 characters'));
            }

            if ($this->checkQuotationNoExist($this->_attributes['quotation_no'])) {
                throw new \RuntimeException(\Yii::t('invoice', 'Duplicate quotation number'));
            }
        } else {
            //只要是空的就生成新的编号
            $this->quotation_no = $this->buildNo($this);
        }

        //自动填上买方信息,如果已经编辑了买方信息就不填充
        if ($this->fillBuyer)
            $this->fillBuyerInfo();

        $this->checkAttributeChangeFields();

        if(!$this->isNew() && $this->beforeRunApprovalFlow()) {

            //如果当前处于审批中 则需要更新申请单content
            //覆盖diff  由审批人的修改归属到申请单的修改
            if($apply_form_id = \common\library\approval_flow\Helper::getApprovingApplyFormId($this->client_id,$this->quotation_id,\common\library\approval_flow\Constants::ENTITY_TYPE_QUOTATION)) {
                if(empty($this->bindingApplyForm)) {
                    $this->bindingApplyForm = new ApplyForm($apply_form_id);
                }

                $this->bindingApplyForm->updateContent($this->getCompareValueForApproval());
                //取消后续逻辑
                return false;
            }else{
                $signal = new QuotationEditInterrupt(
                    $this->userId,
                    $this->quotation_id,
                    \common\library\approval_flow\Constants::ENTITY_TYPE_QUOTATION,
                    $this->name,
                    $this->getCompareValueForApproval(),
                    [],
                    $this
                );
                $interrupt = $this->setIncludeApprovingConfig(true)->runApprovalFlow($signal);

                if($interrupt) {
                    throw new \RuntimeException('',1700);
                }
            }
        }

        if (isset($this->_attributes['user_id']) && is_array($this->_attributes['user_id'])) {
            $this->_attributes['user_id'] = PgsqlUtil::formatArray($this->_attributes['user_id']) ?? '{}';
        }

        if (isset($this->_attributes['external_field_data']) && is_array($this->_attributes['external_field_data'])) {
            $this->_attributes['external_field_data'] = json_encode($this->_attributes['external_field_data'] ?? []) ?? '{}';
        }

        if (isset($this->_attributes['product_list']) && is_array($this->_attributes['product_list'])) {
            //产品列表进行排序 数量大于2才排序
            if ($this->productListSortType && count($this->_attributes['product_list']) >= self::PRODUCT_LIST_SORT_START_COUNT) {
                $this->_attributes['product_list'] = $this->productListSort($this->_attributes['product_list'], $this->productListSortType);
            }
            $this->_attributes['product_list_sort_type'] = $this->productListSortType;

            $existUniqueIds = [];
            //处理主配产品
            $partsProductMap = [];
            foreach ($this->_attributes['product_list']  as &$product) {
                if (empty($product['unique_id']) || isset($existUniqueIds[$product['unique_id']])) {
                    $product['unique_id'] = \ProjectActiveRecord::produceAutoIncrementId();
                }
                $existUniqueIds[$product['unique_id']] = 1;

                if (!empty($product['is_master_product'] ?? 0) && !empty($product['master_group_id'] ?? 0)) {
                    $partsProductMap[$product['master_group_id']] = $product['unique_id'];
                }
            }

            //配件产品填上主产品的id
            if (!empty($partsProductMap)) {
                foreach ($this->_attributes['product_list'] as &$product) {
                    if ((isset($product['is_master_product']) && empty($product['is_master_product']))
                        && !empty($product['master_group_id'] ?? 0)) {
                        $product['master_id'] = $partsProductMap[$product['master_group_id']] ?? 0;
                    }
                }
            }

            //配件数据计算
            \common\library\oms\common\Helper::computePartCount($this->_attributes['product_list'], 'count', null, $this->_attributes);

            $this->_attributes['product_list'] = json_encode($this->_attributes['product_list']) ?? '{}';
        }

        if (isset($this->_attributes['cost_list']) && is_array($this->_attributes['cost_list'])) {
            $this->_attributes['cost_list'] = json_encode($this->_attributes['cost_list'] ?? []) ?? '{}';
        }

        if (isset($this->_attributes['file_list']) && is_array($this->_attributes['file_list'])) {
            $this->_attributes['file_list'] = json_encode($this->_attributes['file_list'] ?? []) ?? '{}';
        }

        if (isset($this->_attributes['handler']) && is_array($this->_attributes['handler'])) {
            $this->_attributes['handler'] = PgsqlUtil::formatArray($this->_attributes['handler']) ?? '{}';
        }

        if (isset($this->_attributes['users']) && is_array($this->_attributes['users'])) {
            $this->_attributes['users'] = PgsqlUtil::formatArray($this->_attributes['users']) ?? '{}';
        }

        $this->_attributes['user_id'] = !empty($this->_attributes['user_id']) ? $this->_attributes['user_id'] : '{}';
        $this->_attributes['external_field_data'] = !empty($this->_attributes['external_field_data']) ? $this->_attributes['external_field_data'] : '{}';
        $this->_attributes['product_list'] = !empty($this->_attributes['product_list']) ? $this->_attributes['product_list'] : '{}';
        $this->_attributes['cost_list'] = !empty($this->_attributes['cost_list']) ? $this->_attributes['cost_list'] : '{}';
        $this->_attributes['file_list'] = !empty($this->_attributes['file_list']) ? $this->_attributes['file_list'] : '{}';
        $this->_attributes['handler'] = !empty($this->_attributes['handler']) ? $this->_attributes['handler'] : PgsqlUtil::formatArray($this->_oldAttributes['handler'] ?? [$this->create_user]);
        $this->_attributes['delete_time'] = !empty($this->_attributes['delete_time']) ? $this->_attributes['delete_time'] : '1970-01-01 08:00:00';
        $this->_attributes['account_date'] = !empty($this->_attributes['account_date']) ? $this->_attributes['account_date'] : '1970-01-01 08:00:00';
        $this->_attributes['quotation_date'] = !empty($this->_attributes['quotation_date']) ? $this->_attributes['quotation_date'] : date("Y-m-d H:i:s");
        $this->_attributes['company_id'] = !empty($this->_attributes['company_id']) ? $this->_attributes['company_id'] : 0;
        $this->_attributes['customer_id'] = !empty($this->_attributes['customer_id']) ? $this->_attributes['customer_id'] : 0;
        $this->_attributes['opportunity_id'] = !empty($this->_attributes['opportunity_id']) ? $this->_attributes['opportunity_id'] : 0;
        $this->_attributes['users'] = !empty($this->_attributes['users']) ? $this->_attributes['users'] : '{}';
        //校验是否能适配审批流
//        if(!$this->isNew() && $this->beforeRunApprovalFlow() && \common\library\approval_flow\Helper::getApprovingApplyFormId($this->client_id,$this->quotation_id,\common\library\approval_flow\Constants::ENTITY_TYPE_QUOTATION)) {
//            ApprovalLogUtil::instance()->log('审批中的数据校验属性改变是否引起新的审批');
//            /**
//             * tips:
//             * 这里用来处理审批中的对象再次编辑是否触发审批流的提示
//             * 不是用来处理对象的编辑再次确认的逻辑
//             */
//
//            $this->runApprovalFlow(
//                new UpdateTrigger($this->userId,
//                    $this->quotation_id,
//                    \common\library\approval_flow\Constants::ENTITY_TYPE_QUOTATION,
//                    $this->name,
//                    $this->getUpdateFields())
//            );
//        }

        return parent::beforeSave();
    }

    /**
     * 报价单恢复之后处理
     */
    public function afterRecover()
    {
        $eventType = $this->getEventType();
        $this->trail($eventType);
        $this->history();
        $this->statistics($eventType);
        $this->addProductRecord($eventType);
        \common\library\server\es_search\SearchQueueService::pushQuotationQueue($this->user_id, $this->client_id, [$this->quotation_id], \Constants::QUOTATION_INDEX_TYPE_CREATE);    // 写入ES
    }

    public function refreshAdditionAmountExchange()
    {
        $this->addition_cost_amount_rmb = round(($this->addition_cost_amount ?? 0) * ($this->exchange_rate / 100), 5);
        $this->addition_cost_amount_usd = round(($this->addition_cost_amount ?? 0) * $this->exchange_rate_usd / 100, 5);
    }


    /**
     * 处理报价单统计信息
     * @param $eventType
     */
    public function statistics($eventType)
    {
        \LogUtil::info("quotation_id:{$this->quotation_id}");

        $accountFlag = 0;
        //如果之前有计入统计则先清除统计(新建无需清除统计)
        if ($this->account_flag == 1 && $eventType !== self::EVENT_AFTER_INSERT){
            if(isset($this->_attributes['account_date']) && $this->_attributes['account_date']){
                $this->cleanStatistic();
            }
        }

        $updateAccountDate = false;
        $createDate = date('Y-m-d', strtotime($this->_attributes['create_time']));//存在没有account_date的情况
        if(!isset($this->_attributes['account_date']) || !$this->_attributes['account_date']){
            $this->_attributes['account_date'] = $createDate ?? date('Y-m-d');
            $updateAccountDate = true;
        }

        //删除操作、已删除单据 不统计
        if ($eventType != self::EVENT_AFTER_DELETE
            && ($this->_attributes['delete_flag'] != Invoice::DELETE_FLAG_TRUE || $this->_attributes['enable_flag'] != Invoice::ENABLE_FLAG_FALSE)
        ) {
            $this->addStatistic();
            $accountFlag = 1;
        }

        // 统计业绩
        $quotationPerformance = $this->getReferPerformance();
        $this->runPerformanceRecord($quotationPerformance);

        $model = $this->getModel();
        if ($accountFlag != $this->account_flag) {
            $updateField = ['account_flag'];
            $this->account_flag = $model->account_flag = $accountFlag ? 1 : 0;
        }
        if($updateAccountDate){
            $this->account_date = $model->account_date = $this->_attributes['account_date'];
            $updateField[] = 'account_date';
        }

        if(!empty($updateField)){
            $model->update($updateField);
        }
    }

    private function addStatistic()
    {
        if(empty($this->_attributes))
            return;

        $company = null;
        //将就相关旧数据统计清除
        if($this->_attributes['company_id'] ?? 0)
        {
            $companyObj = $this->getCompany($this->_attributes['company_id']);
            $company = $companyObj ? $companyObj->getAttributes() : null;
        }
        $country = $company['country'] ?? 0;
        $origin = $company['origin'] ?? 0;
        $groupId = $company['group_id'] ?? 0;

        if (!is_array($this->_attributes['product_list'])) {
            $productList = json_decode($this->_attributes['product_list'], true);
        }

        $operator = '+';
        \StatisticsService::quotationStatistic($this->client_id, $this->_attributes['create_user'],
            $productList??[], $this->_attributes['amount_rmb'],
            $country, $origin, $groupId, $this->_attributes['create_time'], $operator);
    }

    private function cleanStatistic()
    {
        if(empty($this->_oldAttributes))
            return;

        $company = null;
        if($this->_oldAttributes['company_id'] ?? 0){
            $companyObj = $this->getCompany($this->_oldAttributes['company_id']);
            $company = $companyObj ? $companyObj->getAttributes() : null;
        }
        $country = $company['country'] ?? 0;
        $origin = $company['origin'] ?? 0;
        $groupId = $company['group_id'] ?? 0;

        $operator = '-';

        if ($this->_oldAttributes['product_list'] == '[]' || empty($this->_oldAttributes['product_list'])) {
            $this->_oldAttributes['product_list'] = [];
        }

        \StatisticsService::quotationStatistic($this->client_id, $this->_oldAttributes['create_user'],
            $this->_oldAttributes['product_list'], $this->_oldAttributes['amount_rmb'],
            $country, $origin, $groupId, $this->_oldAttributes['create_time'], $operator);
    }

    public function beforeDelete()
    {

        if(\common\library\approval_flow\Helper::getReferApprovalInfos($this->client_id,$this->quotation_id,\common\library\approval_flow\Constants::APPROVAL_STATUS_APPROVING))
        {
            throw new \RuntimeException(\Yii::t('invoice', 'Failed to delete, the quotation is still under approval'));
        }

        $newDataForApprovalFlow = \common\library\approval_flow\Helper::getReferApprovalInfos($this->client_id,$this->quotation_id);

        return parent::beforeDelete();
    }

    public function canManage($userIds)
    {
        return \common\library\privilege_v3\Helper::canManageAnyUsers($this->client_id, $this->userId, PrivilegeConstants::PRIVILEGE_CRM_QUOTATION_VIEW, $userIds);
    }

    public function canUnlock()
    {
        if($this->isLock($this->quotation_id, \common\library\approval_flow\Constants::ENTITY_TYPE_QUOTATION) && \common\library\privilege_v3\Helper::hasPermission($this->client_id, $this->userId, PrivilegeConstants::PRIVILEGE_CRM_QUOTATION_APPROVAL_UNLOCK)) {
            return true;
        }
        return false;
    }

    public function canEdit($throw =false)
    {
        try {

            if($this->hasReviewPrivilege() && $this->isCanEditApprover()) {
                $this->setSkipManageable(true);
                return true;
            }

            if ($this->isLock($this->quotation_id, \common\library\approval_flow\Constants::ENTITY_TYPE_QUOTATION)) {
                throw new \RuntimeException('报价单数据锁定，无法编辑');
            }

            if($this->isManageAble() && \common\library\privilege_v3\Helper::hasPermission($this->client_id, $this->userId, PrivilegeConstants::PRIVILEGE_CRM_QUOTATION_EDIT)) {
                return true;
            }

            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'));

        } catch (\Exception $exception) {
            if ($throw) {
                throw  $exception;
            } else {
                return false;
            }
        }



    }
    /**
     * 检测是否能编辑审批相关的对象
     * @return bool
     */
    public function canEditApprovalRefer()
    {
        //如果没有client级别的审批权限,则不会走审批流
        if (!$this->hasReviewPrivilege()) {
            return true;
        }

        if ($this->isCanEditApprover()) {
            $this->setSkipManageable(true);
            return true;
        }

        if ($this->isLock($this->quotation_id, \common\library\approval_flow\Constants::ENTITY_TYPE_QUOTATION)) {
            return false;
        }

        return true;
    }

    public function hasReviewPrivilege()
    {
        //没有审批和商机审批的权限权限，允许通过
        if (!\common\library\privilege_v3\Helper::hasFunctional($this->client_id, PrivilegeConstants::FUNCTION_REVIEW_BASE)) {
            return false;
        }
        return true;
    }

    public function isCanEditApprover()
    {
        $approver = array_column(\common\library\approval_flow\Helper::getReferApprovalInfos($this->client_id, $this->quotation_id, \common\library\approval_flow\Constants::APPROVAL_STATUS_APPROVING), 'edit_flag', 'approver');

        if ($approver[$this->userId] ?? 0) {
            return true;
        }
        return false;
    }

    public function getWorkflowReferType()
    {
        return Constants::TYPE_QUOTATION;
    }

    public function isExist()
    {
        return $this->enable_flag == 1 && $this->delete_flag == 0;
    }

    public function getFieldListType()
    {
        return Constants::TYPE_QUOTATION;
    }

    public function getListDataFieldMap()
    {
        return $this->getFormatter()->getListDataFieldMap();
    }

    public function checkQuotationNoExist($quotation_no)
    {
        try {
            $quotation = (new Quotation($this->userId))->loadByNo($quotation_no);
            return $quotation->isNew() ? false : ($this->quotation_id != $quotation->quotation_id);
        } catch (\RuntimeException $exception) {
            return false;
        }
    }

    public function getPrivilegeFieldFunctionalId()
    {
        return PrivilegeConstants::FUNCTIONAL_QUOTATION;
    }

    public function getPerformanceReferType()
    {
        return Constants::TYPE_QUOTATION;
    }

    public function getReferPerformance()
    {
        return new QuotationPerformance($this);
    }

    /**
     * @param int $productListSortType
     */
    public function setProductListSortType(int $productListSortType)
    {
        $this->productListSortType = $productListSortType;
    }

    protected function productListSort($data, $productListSortType)
    {
        $skuIds = array_column($data, 'sku_id');
        $skuSortData = (new SkuAPI($this->client_id, $this->user_id))->productListSort($skuIds, $productListSortType);

        $productList = $data;

        $return = [];

        //排序产品 ,注意有相同sku_id情况
        foreach ($skuSortData as $sku_id) {
            foreach ($productList as $productIndex => $productItem) {
                if ($sku_id == $productItem['sku_id']) {
                    $return[] = $productItem;
                    unset($productList[$productIndex]);
                }
            }
        }

        //如果$productList有剩下，放到最后面
        if ($productList) {
            $productList = array_values($productList);
            $return = array_merge($return, $productList);
        }

        return $return;
    }

    protected function getFormulaAttributeMapping()
    {
        return [
            'product_list' => [
                '["package_gross_weight_subtotal"]' => 'package_gross_weight_subtotal',
                '["package_volume_subtotal"]' => 'package_volume_subtotal',
            ],
        ];
    }
}
