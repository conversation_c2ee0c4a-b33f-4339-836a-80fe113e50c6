<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 17/4/5
 * Time: 下午3:14
 */

namespace common\library\invoice;

use common\library\account\Client;
use common\library\account\UserInfo;
use common\library\alibaba\order\AlibabaOrderRelationList;
use common\library\alibaba\order\AlibabaOrderSyncHelper;
use common\library\alibaba\store\AlibabaStoreService;
use common\library\button\order\OrderButtonFactory;
use common\library\cash_collection\CashCollection;
use common\library\cash_collection\CashCollectionList;
use common\library\custom_field\CustomFieldFormatter;
use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldExportService;
use common\library\custom_field\FieldList;
use common\library\customer_v3\company\list\CompanyList;
use common\library\customer_v3\company\orm\Company;
use common\library\customer_v3\customer\CustomerList;
use common\library\customer_v3\customer\orm\Customer;
use common\library\department\DepartmentService;
use common\library\exchange_rate\ExchangeRateService;
use common\library\formula\AttributeFormulaTrait;
use common\library\invoice\export\format\OrderExportFormatter;
use common\library\invoice\export\InvoiceExportFileList;
use common\library\invoice\order_erp_external\OrderErpExternalApi;
use common\library\invoice\push_down\InvoicePushDownFormatter;
use common\library\invoice\quote_field\OrderQuoteFieldHandler;
use common\library\invoice\quote_field\OrderRecordQuoteFieldHandler;
use common\library\invoice\status\InvoiceStatusService;
use common\library\lead\LeadList;
use common\library\object\field\util\FieldTransferUtil;
use common\library\object\object_define\Constant as ObjConstant;
use common\library\object\traits\FieldSettingHandler;
use common\library\oms\capital_account\CapitalAccountFilter;
use common\library\oms\common\OmsConstant;
use common\library\oms\cost_invoice\CostInvoiceApi;
use common\library\oms\cost_invoice\CostInvoiceFilter;
use common\library\oms\invoice\order_erp_external\OrderErpExternalFilter;
use common\library\oms\order\OrderConstants;
use common\library\oms\order\OrderMetadata;
use common\library\oms\order_link\Constant;
use common\library\oms\order_link\OrderLink;
use common\library\oms\order_link\status_computation\ShipmentsComputation;
use common\library\oms\outbound_invoice\sale\record\Helper as OutboundRecordHelper;
use common\library\oms\outbound_invoice\sale\record\SaleOutboundRecordFilter;

use common\library\oms\product_transfer\outbound\OutboundProductTransferAPI;
use common\library\oms\product_transfer\ProductTransferAPI;
use common\library\oms\product_transfer\ProductTransferHelper;
use common\library\oms\product_transfer\purchase\PurchaseProductTransferAPI;
use common\library\oms\shipping_invoice\record\ShippingRecordFilter;
use common\library\oms\shipping_invoice\ShippingInvoiceApi;
use common\library\oms\shipping_invoice\ShippingInvoiceFilter;
use common\library\opportunity\Opportunity;
use common\library\opportunity\OpportunityList;
use common\library\platform_product\PlatformProductAPI;
use common\library\platform_product\sku\PlatformSkuAPI;
use common\library\privilege_v3\object_service\formatter\ListFormatterPrivilegeTrait;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\product\AlibabaCategoryAttr;
use common\library\product_v2\ProductConstant;
use common\library\product_v2\ProductFilter;
use common\library\product_v2\relation\CombineProductRelationAPI;
use common\library\product_v2\sku\SkuAPI;
use common\library\purchase\purchase_order\PurchaseOrderFilter;
use common\library\purchase\purchase_order_product\PurchaseOrderProductFilter;
use common\library\setting\library\fund\relation\CostItemInvoiceRelationApi;
use common\library\trade_document\Constants as DocConstants;
use common\library\trade_document\page\user\Page;
use common\library\util\Arr;
use common\library\util\PgsqlUtil;
use common\models\client\ClientProduct;
use Constants;
use ProcessException;
use xiaoman\orm\database\data\Equal;
use xiaoman\orm\database\data\In;
use xiaoman\orm\database\data\InArray;
use xiaoman\orm\database\data\NotIn;
use function DeepCopy\deep_copy;

class OrderFormatter extends AbstractInvoiceFormatter
{
    use AttributeFormulaTrait;
    use FieldSettingHandler;
    use InvoicePushDownFormatter;
    use ListFormatterPrivilegeTrait;
    protected $showUsersInfo = false;
    protected $showHandlerInfo = false;
    protected $showUsersDepartment = false;
    protected $showDepartmentsName = false;
    protected $showAllowExportDraft = false;
    protected $showAllowExport = false;
    protected $showSystemInfo = false;
    protected $showTransport = false;
    protected $showUpdateUserInfo = false;
    protected $showCustomerInfo = false;
    protected $showMainCurrencyExchangeRate = false;
    protected $showAlibabaOrderInfo= false;
    protected $showExchangeRate = false;
    protected $hideReadonly;
    protected $showExternalFieldDetail = false;
    protected $fieldList;
    protected $externalFieldSettingList;
    protected $showLeadInfo = false;
    protected $showAmountInfo;

    protected $showCashCollectionDetail = true;
    protected $showApprovalStatusInfo;
    protected $showArchiveInfo;
    protected $showProductList;
    protected $showBaseInfo;
    protected $showOrderDetailInfo;
    protected $onlyLocalProduct = false;

    protected $showLinkInfo = false;
    protected $showTransferType;
    protected $showTransferInfo = false;
    protected $showLink = [];
    protected $linkListType;//ui控制字段需要根据类型来处

    protected $showShippingRecordInfo = false;
    protected $showCashCollectionInfo = false;
    protected $showCostInvoiceInfo =false;
    protected $showPaymentInvoiceInfo = false;
    protected $showOutboundRecordInfo = false;
    protected $showWebListAccess = false;
    protected $showSubProduct = false;
    protected $showCapitalAccountInfo = false;

    protected $showInvoiceProductCount = false;
    protected $showHasReferDownStreamInvoice = false;   //是否有下游单据
    protected $showReferInvoiceProductCount = false;    //销售订单产品关联下游单据的数量

    protected $showErpStatusInfo = false;   //展示erp信息，有授权erp成功的才展示
    protected $showCostItems = false;
    protected $showToBePurchaseCount = false;//显示待采购数量
    protected $showProductImageInfoList = false; //产品图片显示为file列表，与其他单据的product_image结构保持一致
    protected $ignorePurchaseCountDone = 0;//过滤待采购数是0的产品

    protected $showProductQuoteField = false; //强制展示产品明细引用字段，否则只根据分组信息进行展示

    //获取订单关联的出运单附加费用信息
    protected $showShippingCostList = false;
    protected $showCanChangeCurrency = false;   //是否可以修改币种

    protected $showCountryName = false; //展示国家名称

    protected $showApiQuoteField = false;//api展示引用字段
    protected $flattenFieldMap;
    protected $metadata;

    public function getMetadata(){      // 临时兼容 fieldHandler
        if(!$this->metadata){
            $this->metadata = new OrderMetadata();
        }
        return $this->metadata;
    }

    /**
     * @param bool $showShippingInvoiceCostList
     */
    public function setShowShippingCostList(bool $showShippingInvoiceCostList): void
    {
        $this->showShippingCostList = $showShippingInvoiceCostList;
    }

    /**
     * @param mixed $hideReadonly
     */
    public function setHideReadonly($hideReadonly): void
    {
        $this->hideReadonly = $hideReadonly;
    }


    /**
     * @param bool $showApiQuoteField
     */
    public function setShowApiQuoteField($showApiQuoteField):void
    {
        $this->showApiQuoteField = $showApiQuoteField;
    }


    /**
     * @param bool $showCostItems
     */
    public function setShowCostItems(bool $showCostItems): void
    {
        $this->showCostItems = $showCostItems;
    }

    /**
     * 展示订单产品数量
     * @param bool $showInvoiceProductCount
     */
    public function setShowInvoiceProductCount(bool $showInvoiceProductCount): void
    {
        $this->showInvoiceProductCount = $showInvoiceProductCount;
    }

    /**
     * @param bool $showShippingRecordInfo
     */
    public function setShowShippingRecordInfo(bool $showShippingRecordInfo): void
    {
        $this->showShippingRecordInfo = $showShippingRecordInfo;
    }



    public function __construct($clientId)
    {
        $this->clientId = intval($clientId);
        $this->setFieldType(Constants::TYPE_ORDER);
    }

    public function getModuleType()
    {
        return Constants::TYPE_ORDER;
    }

    public function getListDataFieldMap()
    {
        return Helper::getOrderListDataFieldMap();
    }

    public function getFieldList()
    {
        // 不能存储fieldList对象到Formatter对象属性里面，否则fieldList对象随着buildMapData()和format()的多次调用，其查询范围会越限越小
//        if (isset($this->fieldList)) {
//            return $this->fieldList;
//        }

        $this->fieldList = new FieldList($this->clientId);
        $this->fieldList->setType(Constants::TYPE_ORDER);
        $this->fieldList->setPrivilegeInfo($this->getFieldUserId(), $this->getFieldFunctionalId());
        !$this->showQuoteField && $this->fieldList->setExcludeFieldType([CustomFieldService::FIELD_TYPE_QUOTE_FIELDS]);
        return $this->fieldList;
    }

    // 获取新字段配置用于兼容订单老orm的格式化
    public function getFlattenFieldMap($objectName)
    {
        if(is_null($this->flattenFieldMap)){
            $fieldTransferUtil = new FieldTransferUtil($this->clientId, [ObjConstant::OBJ_ORDER, ObjConstant::OBJ_ORDER_PRODUCT]);
            $this->flattenFieldMap[ObjConstant::OBJ_ORDER] = $fieldTransferUtil->getFieldMap(ObjConstant::OBJ_ORDER);
            $this->flattenFieldMap[ObjConstant::OBJ_ORDER_PRODUCT] = $fieldTransferUtil->getFieldMap(ObjConstant::OBJ_ORDER_PRODUCT);
        }
        return $this->flattenFieldMap[$objectName];
    }

    public function getExternalFieldSettingList()
    {
        if (isset($this->externalFieldSettingList)) {
            return $this->externalFieldSettingList;
        }

        $fieldList = $this->getFieldList();
        $fieldList->setBase(0);
        $fieldList->setDisableFlag(false);
        $fieldList->setReadonly($this->hideReadonly ? 0 : 1);
        $fieldList->setNeedList(true);
        $this->externalFieldSettingList = $fieldList->find();
        return $this->externalFieldSettingList;
    }

    public function getFieldExportService()
    {
        return  new FieldExportService($this->clientId,Constants::TYPE_ORDER);
    }

    /**
     * @param bool $showUsersInfo
     */
    public function setShowUsersInfo(bool $showUsersInfo)
    {
        $this->showUsersInfo = $showUsersInfo;
    }

    public function setShowUsersDepartment($showUsersDepartment)
    {
        $this->showUsersDepartment = $showUsersDepartment;
    }

    public function setShowDepartmentsName($showDepartmentsName)
    {
        $this->showDepartmentsName = $showDepartmentsName;
    }

    /**
     * @param bool $showCountryName
     */
    public function setShowCountryName(bool $showCountryName)
    {
        $this->showCountryName = $showCountryName;
    }

    /**
     * @param bool $showAlibabaOrderInfo
     */
    public function setShowAlibabOrderInfo(bool $showAlibabaOrderInfo)
    {
        $this->showAlibabaOrderInfo = $showAlibabaOrderInfo;
    }

    /**
     * @param bool $showHandlerInfo
     */
    public function setShowHandlerInfo(bool $showHandlerInfo)
    {
        $this->showHandlerInfo = $showHandlerInfo;
    }

    public function setShowAllowExportDraft(bool $boll)
    {
        $this->showAllowExportDraft = $boll;
    }

    public function setShowAllowExport(bool $boll)
    {
        $this->showAllowExport = $boll;
    }

    /**
     * @param bool $showSystemInfo
     */
    public function setShowSystemInfo(bool $showSystemInfo): void
    {
        $this->showSystemInfo = $showSystemInfo;
    }

    /**
     * @param bool $showTransport
     */
    public function setShowTransport(bool $showTransport): void
    {
        $this->showTransport = $showTransport;
    }

    /**
     * @param bool $showUpdateUserInfo
     */
    public function setShowUpdateUserInfo(bool $showUpdateUserInfo): void
    {
        $this->showUpdateUserInfo = $showUpdateUserInfo;
    }

    /**
     * @param bool $showExternalFieldDetail
     */
    public function setShowExternalFieldDetail(bool $showExternalFieldDetail): void
    {
        $this->showExternalFieldDetail = $showExternalFieldDetail;
    }

    /**
     * 这里会根据当前主币种的汇率来覆盖exchange_rate字段
     * @param bool $showMainCurrencyExchangeRate
     */
    public function setShowMainCurrencyExchangeRate(bool $showMainCurrencyExchangeRate)
    {
        $this->showMainCurrencyExchangeRate = $showMainCurrencyExchangeRate;

    }

    public function setShowLeadInfo(bool $showLeadInfo)
    {
        $this->showLeadInfo = $showLeadInfo;
    }

    /**
     * @return bool
     */
    public function isShowOutboundRecordInfo(): bool
    {
        return $this->showOutboundRecordInfo;
    }

    /**
     * @param bool $showOutboundRecordInfo
     */
    public function setShowOutboundRecordInfo(bool $showOutboundRecordInfo): void
    {
        $this->showOutboundRecordInfo = $showOutboundRecordInfo;
    }


    /**
     * @return bool
     */
    public function isShowCashCollectionInfo(): bool
    {
        return $this->showCashCollectionInfo;
    }

    /**
     * @param bool $showCashCollectionInfo
     */
    public function setShowCashCollectionInfo(bool $showCashCollectionInfo): void
    {
        $this->showCashCollectionInfo = $showCashCollectionInfo;
    }

    /**
     * @return bool
     */
    public function isShowCostInvoiceInfo(): bool
    {
        return $this->showCostInvoiceInfo;
    }

    /**
     * @param bool $showCostInvoiceInfo
     */
    public function setShowCostInvoiceInfo(bool $showCostInvoiceInfo): void
    {
        $this->showCostInvoiceInfo = $showCostInvoiceInfo;
    }

    /**
     * @return bool
     */
    public function isShowPaymentInvoiceInfo(): bool
    {
        return $this->showPaymentInvoiceInfo;
    }

    /**
     * @param bool $showPaymentInvoiceInfo
     */
    public function setShowPaymentInvoiceInfo(bool $showPaymentInvoiceInfo): void
    {
        $this->showPaymentInvoiceInfo = $showPaymentInvoiceInfo;
    }

    public function setShowArchiveInfo($showArchiveInfo): void
    {
        $this->showArchiveInfo = $showArchiveInfo;
    }

    public function setShowTransferInfo($showTransferInfo, $showTransferType)
    {
        $this->showTransferInfo = $showTransferInfo;
        $this->showTransferType = $showTransferType;
    }

    public function setShowAmountInfo(bool $showAmountInfo)
    {
        $this->showAmountInfo = $showAmountInfo;
    }

    public function setShowCashCollectionStatus($showCashCollectionStatus, $showCashCollectionDetail = true)
    {
        $this->showCashCollectionStatus = $showCashCollectionStatus;
        $this->showCashCollectionDetail = $showCashCollectionDetail;
    }

    public function setShowApprovalStatusInfo($showApprovalStatusInfo): void
    {
        $this->showApprovalStatusInfo = $showApprovalStatusInfo;
    }
    public function setShowBaseInfo(int $showBaseInfo)
    {
        $this->showBaseInfo = $showBaseInfo;
        if ($showBaseInfo) {
            $this->setShowInfoGroup(true);
            $this->setShowGroupIds([CustomFieldService::ORDER_GROUP_BASIC]);
        }
    }

    public function setShowProductList($showProductList): void
    {
        $this->showProductList = $showProductList;
        if ($showProductList) {
            $this->setShowInfoGroup(true);
            $this->setShowGroupIds([CustomFieldService::ORDER_GROUP_PRODUCT]);
        }
    }

    public function setShowOrderDetailInfo($showOrderDetailInfo): void
    {
        $this->showOrderDetailInfo = $showOrderDetailInfo;
    }

    public function setOnlyLocalProduct($flag)
    {
        $this->onlyLocalProduct = $flag;
    }

    public function setShowHasReferDownStreamInvoice(bool $showHasReferDownStreamInvoice): void
    {
        $this->showHasReferDownStreamInvoice = $showHasReferDownStreamInvoice;
    }

    public function setShowReferInvoiceProductCount(bool $showReferInvoiceProductCount): void
    {
        $this->showReferInvoiceProductCount = $showReferInvoiceProductCount;
    }

    /**
     * @param bool $showErpStatusInfo
     */
    public function setShowErpStatusInfo(bool $showErpStatusInfo): void
    {
        $this->showErpStatusInfo = $showErpStatusInfo;
    }

    /**
     * @param bool $showCanChangeCurrency
     */
    public function setShowCanChangeCurrency(bool $showCanChangeCurrency): void
    {
        $this->showCanChangeCurrency = $showCanChangeCurrency;
    }

    /**
     * @param bool $showExchangeRate
     */
    public function setShowExchangeRate(bool $showExchangeRate): void
    {
        $this->showExchangeRate = $showExchangeRate;
    }

    /**
     * @param bool $showWebListAccess
     */
    public function setShowWebListAccess(bool $showWebListAccess): void
    {
        $this->showWebListAccess = $showWebListAccess;

        $this->showOperateButtonAccess = [
            'change_status' ,
            'batch_change_status' ,
            'member_manage',
            'batch_member_manage',
            'unlock',
            'edit',
            'copy',
            'delete' ,
            'create_schedule' ,
            'export',
            'associate_business',
            'create_cash_collection_invoice' ,
            'create_cash_collection' ,
            'create_logistics',
            'generate_purchase_order',
            'assurance',
            'eProceed',
            'generate_order_outbound',
            'create_outbound_product_transfer',
            'create_stock_up_invoice' ,
            'create_transfer_invoice',
            'confirm_stock_up_done',
            'confirm_shipping_done',
            'confirm_end_done',
            'cancel_end_done',
            'generate_cost_invoice',
            'generate_shipping_invoice',
            'edit_attachment'
        ];
    }

    public function showOrderProfitDetailWebSetting(){
        $this->setSpecifyFields(
            [
                'update_user',
                'name',
                'departments',
                'currency',
                'amount_rmb',
                'amount_usd',
                'exchange_rate',
                'exchange_rate_usd',
                'company_id',
                'company_name',
                'approval_status',
                'account_date',
                'create_time',
                'update_time',
                'archive_type',
                'amount',
                'amount_rmb',
                'amount_usd',
                'users',
                'type',
                'source_type',
                'status_action',
                'ali_status_name',
                'fulfillment_channel',
                'product_total_amount_rmb',
                'product_total_amount',
                'product_total_amount_usd',
                'product_total_count',
                'ali_order_id',
                'user_id',
                'link_status',
                'order_gross_margin',
                'order_gross_margin_cny',
                'order_gross_margin_usd',
                'capital_account_id',
            ]
        );
        $this->setIgnorePrivilege(true);
        $this->setShowCashCollectionStatus(true, true);
        $this->setShowDepartmentsName(true);
        $this->setShowUsersInfo(true);
        $this->setShowCashCollectionInfo(true);
        $this->setShowCompanyInfo(true);
        $this->setShowCustomerInfo(true);
        $this->setShowCostInvoiceInfo(true);
        $this->setShowPaymentInvoiceInfo(true);
        $this->setShowPurchaseOrder(true);
        $this->setShowStatusInfo(true);
        $this->setShowOutboundRecordInfo(true);
        $this->setShowExchangeRate(true);
    }

    /**
     * @param bool $showLinkInfo
     */
    public function setShowLinkInfo(bool $showLinkInfo): void
    {
        $this->showLinkInfo = $showLinkInfo;
    }

    /**
     * @param array $showLink
     */
    public function setShowLink(array $showLink): void
    {
        $this->showLink = $showLink;
    }

    /**
     * @param mixed $linkListType
     */
    public function setLinkListType($linkListType): void
    {
        $this->linkListType = $linkListType;
    }

    /**
     * @param bool $showSubProduct
     */
    public function setShowSubProduct(bool $showSubProduct)
    {
        $this->showSubProduct = $showSubProduct;
    }

    /**
     * @param bool $showCapitalAccountInfo
     */
    public function setShowCapitalAccountInfo(bool $showCapitalAccountInfo): void
    {
        $this->showCapitalAccountInfo = $showCapitalAccountInfo;
    }

    /**
     * @param bool $showProductQuoteField
     */
    public function setShowProductQuoteField(bool $showProductQuoteField): void
    {
        $this->showProductQuoteField = $showProductQuoteField;
    }
    
    protected $isOpen = false;
    
    public function setIsOpen($isOpen)
    {
        return $this->isOpen = $isOpen;
    }

    public function buildMapData()
    {
        $oldSpecifyFields = $this->specifyFields;
        $noSpecifyFields  = is_null($this->specifyFields);
        $this->initPrivilegeForFormatter();
        $list  = $this->batchFlag?$this->listData:[$this->data];

        // 兼容字段权限写法
        if ($this->isOpen && $noSpecifyFields && is_array(current($list))) {
            $specifyFields = array_keys(current($list));
            $this->setSpecifyFields($specifyFields);
        }

        // app旧ORM兼容显示字段
        if ($this->clientScene == \Constants::CLIENT_TYPE_APP && !empty($oldSpecifyFields)) {
            $this->setSpecifyFields($oldSpecifyFields);
        }

        $userIds = [];
        $companyIds = [];
        $opportunityIds = [];
        $customerIds = [];
        $orderNos = [];
        $statusIds = [];
        $orderIds = [];
        $departmentUserIds = [];
        $departmentIds = [];
        $externalFieldDatas = [];
        $sellerAccountIds = [];
        $aliOrderIds = [];
        $leads = [];
        $skuIds = [];
        $productIds = [];
        $platformSkuIds = [];
        $platformProductIds = [];
        $uniqueIds = [];
        $groupUniqueIds = [];
        $orderProductTotalCountMap = [];
        $combineProductData = [];
        $capitalAccountIds = [];
        $costItemRelationIds = [];
        $countrys = [];
        $productImages = [];

        foreach ( $list as $elem ){

            $userIds[] = $elem['create_user'];
            if( $elem['seller_account_id']){
                $sellerAccountIds[] = $elem['seller_account_id'];
            }
            if( $elem['ali_order_id']){
                $aliOrderIds[] = $elem['ali_order_id'];
            }
            if ($elem['update_user'])
                $userIds[] = $elem['update_user'];

            if( !empty($elem['users']) ) {
                if (!is_array($elem['users'])) {
                    $elem['users'] = json_decode($elem['users'], true) ?? [];
                }
                $userIds = array_merge($userIds, array_column($elem['users'], 'user_id'));
            }

            if ( !empty($elem['departments'])) {
                $elem['departments'] = is_array($elem['departments']) ? $elem['departments'] : json_decode($elem['departments'], true) ?? [];
                $departmentIds = array_merge($departmentIds, array_column($elem['departments'], 'department_id'));
            }

            if ($this->showUsersDepartment) {
                if (!is_array($elem['users'])) {
                    $elem['users'] = json_decode($elem['users'], true) ?? [];
                }
                $departmentUserIds = array_merge($departmentUserIds, array_column($elem['users'], 'user_id'));
            }

            if( !empty($elem['handler']) ){
                if (!is_array($elem['handler'])) {
                    $elem['handler'] = PgsqlUtil::trimArray($elem['handler']);
                    $elem['handler'] = $elem['handler'] ?: [];
                }
                $userIds = array_merge($userIds,$elem['handler']);
            }

            if($this->showFileList && !empty($elem['file_list'])){
                if (!is_array($elem['file_list'])) {
                    $elem['file_list'] = json_decode($elem['file_list'], true) ?? [];
                }
                $userIds = array_merge($userIds,array_column($elem['file_list'],'user_id'));
            }

            if( $elem['company_id'] ){
                $companyIds[$elem['company_id']] = $elem['company_id'];
            }

            if( isset($elem['opportunity_id']) && $elem['opportunity_id'] ){    //兼容原先的订单
                $opportunityIds[$elem['opportunity_id']] = $elem['opportunity_id'];
            }

            if( $elem['customer_id']??0 ){
                $customerIds[$elem['customer_id']] = $elem['customer_id'];
            }

            if( isset($elem['product_list'])) {
                if (is_array($elem['product_list'])) {
                    $datum = $elem['product_list'];
                } else {
                    $datum = json_decode($elem['product_list'], true);;
                }
                $orderProductTotalCountMap[$elem['order_id']] = 0;
                foreach ($datum??[] as $item) {
                    $platformSkuId = $item['platform_product_info']['platform_sku_id'] ?? 0;
                    $platformProductId = $item['platform_product_info']['platform_product_id'] ?? 0;
                    $key = "{$elem['order_id']}_{$platformProductId}_{$platformSkuId}";
                    if (!isset($groupUniqueIds[$key])) {
                        $groupUniqueIds[$key] = [];
                    }
                    isset($item['unique_id']) && $groupUniqueIds[$key][] = $item['unique_id'];
                    array_push($platformSkuIds, $platformSkuId);
                    array_push($platformProductIds, $platformProductId);

                    //销售总数量为产品总数量，若产品为组合产品，计算子产品数量
                    if( $this->showProgressInfo)
                    {
                        if(isset($item['product_type']) && $item['product_type'] == ProductConstant::PRODUCT_TYPE_COMBINE){
                            $combineProductData[$elem['order_id']][] = [
                                'product_id' => $item['product_id'],
                                'count' => $item['count'],
                            ];
                        }else{
                            $orderProductTotalCountMap[$elem['order_id']] += floatval($item['count']);
                        }
                    }
                }
                $productIds = array_merge($productIds, array_column($datum, 'product_id'));
                $skuIds = array_merge($skuIds, array_column($datum, 'sku_id'));
                $uniqueIds = array_merge($uniqueIds, array_column($datum, 'unique_id'));
                // 获取图片file_id信息
                foreach (array_column($datum, 'product_images') as $productImageItems) {
                    if (!empty($productImageItems)) {
                        foreach ($productImageItems as $imageItem) {
                            $productImages[] = $imageItem;
                        }
                    }
                }
            }
            $orderNos[] = $elem['order_no'];
            $statusIds[] = $elem['status']??0;
            $orderIds[] = $elem['order_id'];
            $capitalAccountIds[] = $elem['capital_account_id']??0;
            $externalFieldDatas[] = is_array($elem['external_field_data']) ? $elem['external_field_data'] : json_decode($elem['external_field_data'] ?? '{}', true);
            $countrys[] = $elem['country'] ?? '';

            if ($this->showLeadInfo) {
                $leads[] = $elem['source_lead_id'];
            }

            if (isset($elem['cost_list'])) {
                if (is_array($elem['cost_list'])) {
                    $costList = $elem['cost_list'];
                } else {
                    $costList = json_decode($elem['cost_list'], true);;
                }
                foreach ($costList??[] as $costItem) {
                    $costItemRelationIds[] = $costItem['cost_item_relation_id'] ?? 0;
                }
            }
        }

        // 引用字段准备
        $quoteFieldRows = $recordQuoteRows = [];
        if($this->showQuoteField || $this->showProductQuoteField){

            if(
                empty($this->showGroupIds) ||
                (is_array($this->showGroupIds) && in_array(CustomFieldService::ORDER_GROUP_BASIC, $this->showGroupIds)) ||
                CustomFieldService::ORDER_GROUP_BASIC == $this->showGroupIds
            ){
                $quoteFieldHandler = OrderQuoteFieldHandler::instance($this->clientId, $this->userId);
                $quoteFieldRows = array_column($quoteFieldHandler->getQuoteFieldRows($orderIds), null,'order_id');
            }

            if(
                $this->showProductQuoteField ||
                (
                    $this->showGroupIds &&
                    (
                        (is_array($this->showGroupIds) && in_array(CustomFieldService::ORDER_GROUP_PRODUCT, $this->showGroupIds)) ||
                        $this->showGroupIds == CustomFieldService::ORDER_GROUP_PRODUCT
                    )
                )
            ){
                $recordQuoteHandler = OrderRecordQuoteFieldHandler::instance($this->clientId, $this->userId);
                $recordQuoteRows = array_column($recordQuoteHandler->getQuoteFieldRows($uniqueIds), null, 'id');
            }
        }

        $approvalLockList = \common\library\approval_flow\Helper::getReferLockList($this->userId, Constants::TYPE_ORDER, $orderIds, ['refer_id', 'lock_flag']);
        $approvalLockMap = array_column($approvalLockList, 'lock_flag', 'refer_id');

        //审批流信息
        $approvalFlowInfoMap = [];
        if ($this->showApprovalFlowInfo) {
            $approvalFlowInfoMap = \common\library\approval_flow\Helper::getApprovalProgressInfo(Constants::TYPE_ORDER, $orderIds, $this->showApprovalFlowEventInfo);
        }

        $productMap = [];
        $productIds = array_values(array_filter(array_unique($productIds)));
        if (!empty($productIds)) {
            $productFilter = new ProductFilter($this->clientId);
            $productFilter->product_id = $productIds;
            $productBatch = $productFilter->find();
            $productBatch->getFormatter()->skuItemsInfoSetting();
            $productList = $productBatch->getAttributes();
            $productMap = array_column($productList, null, 'product_id');
        }
        $outboundRecordMap =[];
        $outboundRecordSummary = [];

        $outBoundSkuIds = [];
        if ($this->showOutboundRecordInfo) {
            $outbound_product_inventory_amount_rmb_sum = 0;
            $outbound_product_inventory_amount_usd_sum = 0;
            $outboundRecordList = OutboundRecordHelper::getOutboundRecordByOrderId($this->clientId, $orderIds, OmsConstant::OUTBOUND_INVOICE_SOURCE_TYPE_STORE);
            foreach ($outboundRecordList as $item) {
                $outBoundSkuIds[] = $item['sku_id'];
                $outboundRecordMap[$item['order_id']][] = $item;
                $outbound_product_inventory_amount_rmb_sum += floatval($item['cost_unit_price_rmb']) * floatval($item['outbound_count']);
                $outbound_product_inventory_amount_usd_sum += floatval($item['cost_unit_price_usd']) * floatval($item['outbound_count']);
            }
            $outboundRecordSummary = [
                'outbound_product_inventory_amount_rmb_sum' => $outbound_product_inventory_amount_rmb_sum,
                'outbound_product_inventory_amount_usd_sum' => $outbound_product_inventory_amount_usd_sum
            ];
        }

        //产品关联的出运明细
        $shippingRecordMap = [];
        if ($this->showShippingRecordInfo) {

            $spInvoiceFilter = new ShippingInvoiceFilter($this->clientId);
            $spInvoiceFilter->order_ids = new InArray($orderIds);
            $spInvoiceFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
            $spInvoiceFilter->shipping_status = new NotIn([OmsConstant::SHIPPING_INVOICE_STATUS_DISCARD]);
            $spRecordFilter = new ShippingRecordFilter($this->clientId);
            $spRecordFilter->filterSubProduct();
            $spRecordFilter->invoice_product_id = $uniqueIds;
            $spRecordFilter->order_id =  $orderIds;
            $spRecordFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
            $spRecordFilter->select(['invoice_product_id','shipping_count' => function ($column, $table) {
                                return "sum($table.$column) as  have_shipping_count";
                            }]);
            $spInvoiceFilter->initJoin()
                            ->leftJoin($spRecordFilter)
                            ->on('shipping_invoice_id','shipping_invoice_id')
                            ->joinGroupBy('invoice_product_id', $spRecordFilter->getTableName());

            $spInvoiceList = $spInvoiceFilter->rawData();
            $shippingRecordMap = array_column($spInvoiceList,null,'invoice_product_id');
        }

        //出运单 可继承附加费用
        $shippingCostMap = [];
        if ($this->showShippingCostList) {
            $spInvoiceFilter = new ShippingInvoiceFilter($this->clientId);
            $spInvoiceFilter->order_ids = new InArray($orderIds);
            $spInvoiceFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
            $batchSpInvoice = $spInvoiceFilter->find();
            $batchSpInvoice->getFormatter()->displayFields(['cost_list']);
            $shippingInvoiceList = $batchSpInvoice->getAttributes();
            $shippingCostMap = array_reduce(
                array_column($shippingInvoiceList, 'cost_list'),
                function ($result, $costItems) {
                    foreach ($costItems as $item) {
                        if (empty($item['order_id'])){
                            continue;
                        }
                        //新旧数据
                        $key = isset($item['cost_item_relation_id']) && !empty($item['cost_item_relation_id'])
                            ? $item['cost_item_relation_id']
                            : ($item['cost_name'] ?? "");
                        $existingCost = $result[$item['order_id']][$key] ?? [];
                        $existingCost['cost'] = ($existingCost['cost'] ?? 0) + floatval($item['cost']??0);
                        $result[$item['order_id']][$key] = $existingCost;
                    }
                    return $result;
                },
                []
            );
        }
        $productSkuMap = [];
        $skuIds = array_merge($outBoundSkuIds,$skuIds);
        $skuIds = \array_unique($skuIds);
        if (!empty($skuIds)) {
            $skuApi = new SkuAPI($this->clientId);
            $productSkuMap = array_column($skuApi->items($skuIds), null,'sku_id');
        }
        $platformProductSkuMap = [];
        $platformSkuIds = array_filter(array_unique($platformSkuIds));
        if (!empty($platformSkuIds)) {
            $platSkuApi = new PlatformSkuAPI($this->clientId);
            $platformProductSkuMap = array_column($platSkuApi->productSkusByIds($platformSkuIds, true), null,'platform_sku_id');
        }

        $platformProductMap = [];
        $platformProductIds = array_filter(array_unique($platformProductIds));
        if (!empty($platformProductIds)) {
            $platApi = new PlatformProductAPI($this->clientId);
            $filter = $platApi->buildFilter(['platform_product_id' => $platformProductIds]);
            $filter->select(['from_url', 'platform_product_id', 'third_product_id']);
            $platformProductMap = array_column($filter->rawData(), null,'platform_product_id');
        }

        $userIds  = array_filter($userIds);

        $userMap = [];
        if( !empty($userIds) ){
            $userList =  \common\library\account\Helper::getBatchUserInfo($this->clientId,$userIds);
            $userList =array_map(function ($elem){
                return ['user_id'=>$elem->user_id,'nickname'=>$elem->nickname,'avatar'=>$elem->avatar];
            },$userList);
            $userMap = array_combine(array_column($userList,'user_id'),$userList);
        }

        $departmentMap = [];
        if (!empty($departmentIds)) {
            $departmentService = new DepartmentService($this->clientId);
            $departmentMap = $departmentService->batchGetDepartmentListForIds($departmentIds);
        }

        $departmentUsersMap = [];
        if (!empty($departmentUserIds)) {
            $departmentUserIds = array_values(array_filter($departmentUserIds));
            $departmentUsersMap = (new DepartmentService($this->clientId))->batchGetUserDepartmentList($departmentUserIds);
        }
        $companyMap = [];
        $showGroupNotProduct = $this->showInfoGroup && $this->showGroupIds != [CustomFieldService::ORDER_GROUP_PRODUCT];
        $companyIds = Arr::uniqueFilterValues($companyIds);
        if (!empty($userIds) && !empty($companyIds) && ($this->showCompanyInfo || $showGroupNotProduct)) {
            $userId = reset($userIds);
            $list = new CompanyList($userId);
            $list->setSkipPrivilege(true);
            $list->setCompanyIds($companyIds);
            $list->setIsArchive(null);
            $list->setFields(['company_id', 'name', 'is_archive', 'serial_id']);
            $companyList = $list->find();
            foreach ($companyList as $elem) {
                $companyMap[$elem['company_id']] = $elem;
            }
        }

        $opportunityMap = [];
        if( !empty($opportunityIds) && ($this->showOpportunityInfo || $showGroupNotProduct )){
            $opportunityIds = array_values($opportunityIds);
            $userId = reset($userIds);
            $list = new OpportunityList($this->clientId);
            $list->setViewingUserId($userId);
            $list->setOpportunityIds($opportunityIds);
            $list->setSkipPermissionCheck(true);
            $list->setIgnoreEnableFlag();
            $list->setFields(['opportunity_id', 'name', 'enable_flag']);
            $opportunityList = $list->find();
            foreach ($opportunityList as $elem){
                $opportunityMap[$elem['opportunity_id']] = $elem;
            }
        }

        $customerMap = [];
        $customerIds = Arr::uniqueFilterValues($customerIds);
        if( !empty($customerIds) && ($this->showCustomerInfo || $showGroupNotProduct )) {

            $list = new CustomerList($this->clientId);
            $list->setCustomerId($customerIds);
            $list->setFields(['customer_id', 'name', 'email','is_archive','tel_list']);
            $customerList = $list->find();

            foreach ($customerList as $elem){

                if (empty($elem['name'])) $elem['name'] = $elem['email'];
                $elem['tel_list'] = json_decode($elem['tel_list'],true);
                $customerMap[$elem['customer_id']] = $elem;
            }
        }


        $statusMap = [];
        if( !empty($statusIds) && ( $this->showStatusInfo|| $showGroupNotProduct )){

            $statusList = (new InvoiceStatusService($this->clientId, Constants::TYPE_ORDER))->baseInfoList(false, $statusIds,false);
            $statusMap = array_combine(array_column($statusList,'id'),$statusList);

        }

        $exportFileCountMap = [];

        if( $this->showExportFileCount && !empty($orderIds) ){

            $exportFileListObj = new InvoiceExportFileList($this->userId, \Constants::TYPE_ORDER);
            $exportFileListObj->setClientId($this->clientId);
            $exportFileListObj->setReferIds($orderIds);
            $exportFileListObj->setGroupBy(['refer_id']);
            $exportFileCountList = $exportFileListObj->groupCount();

            $exportFileCountMap = array_combine(array_column($exportFileCountList,'refer_id'),array_column($exportFileCountList,'count'));
        }

        $pinMap = [];
        if( $this->userId && $this->showPinFlag ){
            $pinList = \Pin::getPinReferIds($this->userId,\Pin::TYPE_ORDER,$orderIds);
            $pinMap = empty($pinList)?[]:array_fill_keys($pinList,1);
        }

        $cashCollectionMap = [];
        if ($this->showCashCollectionStatus) {
            \common\library\cash_collection\Helper::setClientScene($this->clientScene);
            $cashCollectionMap = \common\library\cash_collection\Helper::getReferStatsMap($this->clientId, CashCollection::REFER_TYPE_ORDER, $orderIds);
        }
        $cashCollectionInfoMap =[];
        $cashCollectionSummary =[];
        if ($this->showCashCollectionInfo) {
            $cashCollectionList = new CashCollectionList($this->clientId);
            $cashCollectionList->setOrderId($orderIds);
            $cashCollectionList->setSkipPermission(true);
            $cashCollectionList->setViewingUserId($this->userId);
            $cashCollectionList->getFormatter()->setIgnorePrivilege(true);
            $cashCollectionList->getFormatter()->orderProfitDetailWebSetting();
            $cashCollectionRes = $cashCollectionList->find();
            $exchange_loss_amount_rmb = 0;
            $exchange_loss_amount_usd = 0;
            foreach ($cashCollectionRes as &$item) {
                if( $item['cash_collection_invoice_id'] && $item['collect_status'] == CashCollection::IS_COLLECTED) {
                    $item['exchange_loss_amount'] = round($item['cash_collection_invoice_amount'] * $item['cash_collection_invoice_exchange_rate'] / 100  - $item['amount'] * $item['exchange_rate'] / 100, 2);
                    $item['exchange_loss_amount_usd'] = round($item['cash_collection_invoice_amount'] * $item['cash_collection_invoice_exchange_rate_usd'] / 100 - $item['amount'] *  $item['exchange_rate_usd'] / 100,2);
                    $exchange_loss_amount_rmb = $exchange_loss_amount_rmb + doubleval($item['exchange_loss_amount']);
                    $exchange_loss_amount_usd = $exchange_loss_amount_usd + doubleval($item['exchange_loss_amount_usd']);
                }
                $cashCollectionInfoMap[$item['order_id']][] = $item;
            }
            unset($item);
            $cashCollectionList->setCollectStatus(CashCollection::IS_COLLECTED);
            $cashCollectionSummary = $cashCollectionList->summary(['real_amount_rmb', 'real_amount_usd', 'bank_charge_rmb', 'bank_charge_usd']);
            $cashCollectionSummary['exchange_loss_amount_rmb'] = round($exchange_loss_amount_rmb,2);
            $cashCollectionSummary['exchange_loss_amount_usd'] = round($exchange_loss_amount_usd,2);
        }

        $storeMap = AlibabaStoreService::getStoreInfoMaps($this->clientId,[],true);

        $sellerAccountMap = [];
        $sellerAccountIds = array_values(array_unique(array_filter($sellerAccountIds)));
        if ($sellerAccountIds)
        {
            $sellerAccountMap = AlibabaOrderSyncHelper::getAlibabaOrderSellerAccountInfoMap($this->clientId, $orderIds, $sellerAccountIds);
        }

        $alibabaOrderMap = [];


        if($this->showAlibabaOrderInfo){

            $aliOrderIds = array_unique($aliOrderIds);
            if (!empty($aliOrderIds)) {
                $alibabaOrderRelationListObj = new AlibabaOrderRelationList($this->clientId);
                $alibabaOrderRelationListObj->setAlibabaTradeId($aliOrderIds);
                $alibabaOrderRelationListObj->getFormatter()->listInfoSetting();
                $alibabaOrderRelationList = $alibabaOrderRelationListObj->find();

                $alibabaOrderMap = array_combine(array_column($alibabaOrderRelationList,'alibaba_trade_id'),$alibabaOrderRelationList);
            }
        }

        $transportMap = [];
        if ($this->showTransport) {
            $transportMap = \common\library\transport\Helper::getRefertransportMap($this->clientId,$orderIds);
        }


        $allowExportDraftMap = [];
        if ($this->showAllowExport)
        {
            $client = Client::getClient($this->clientId);
            $values = $client->getSettingAttributes([Client::SETTING_KEY_ORDER_ALLOW_EXPORT_DRAFT]);
            $allowExportDraftMap[$this->clientId] = $values[Client::SETTING_KEY_ORDER_ALLOW_EXPORT_DRAFT] ?? 1;
        }

        $leadInfoMap = [];
        $leads = Arr::uniqueFilterValues($leads);
        if ($this->showLeadInfo && !empty($leads))
        {
            $leadList = new LeadList($this->userId);
            $leadList->setLeadId($leads);
            $leadList->setSkipPrivilege(true);
            $leadList->setShowAllStatusFlag(true);
            $leadList->setFields(['lead_id', 'name']);
            $leadList->setIsArchive(null);
            $leadInfoMap = array_column($leadList->find(), null, 'lead_id');
        }

        $fileInfoMap = [];
        if (in_array('external_field_data', $this->specifyFields ?? [])) {

            $filterFieldIds = [];
            $customFields = $this->getExternalFieldSettingList();
            $fileFieldTypes = [CustomFieldService::FIELD_TYPE_IMAGE, CustomFieldService::FIELD_TYPE_ATTACH];
            foreach ($customFields as $field) {
                $customFieldType = $field['field_type'];
                $upstreamFieldType = $field['relation_origin_field_type'] ?? 0;
                if (in_array($customFieldType, $fileFieldTypes) || in_array($upstreamFieldType, $fileFieldTypes)) {
                    $filterFieldIds[] = $field['id'];
                }
            }

            $fileIds = [];
            foreach ($externalFieldDatas as $key => $data) {
                foreach ($filterFieldIds as $id) {
                    if (!empty($data[$id])) {
                        $fileIds = array_unique(array_merge(array_column($data[$id], 'file_id'),$fileIds));
                    }
                }
            }

            $objArray = \UploadFile::findByIds($fileIds);
            if (!empty($objArray)) {
                foreach ($objArray as $obj) {

                    $upload = new \AliyunUpload();
                    $upload->loadByObject($obj);
                    $fileInfoMap[$obj->file_id] = array(
                        'file_id' => $upload->getFileId(),
                        'file_url' => $upload->getFileUrl(),
                        'download_url' => $upload->generatePresignedUrl(),
                        'preview_url' => $upload->getPreview(),
                        'file_name' => $upload->getFileName(),
                        'file_ext' => $upload->getFileExt(),
                        'file_size' => $upload->getFileSize(),
                    );
                }
            }
        }

        $purchaseOrderMap = [];
        $relateInvoiceCountMap = [];
        $paymentInvoiceMap =[];
        $costInvoiceInfoMap = [];
        $costInvoiceSummary = [
            'amount_rmb' => 0,
            'payment_amount_rmb' => 0,
            'payment_wait_amount_rmb' => 0
        ];
        $paymentInvoiceSummary = [
            'total_payment_wait_amount' => 0,
            'total_payment_amount' => 0,
            'total_amount' => 0
        ];
        if (($this->showPurchaseOrder || in_array(CustomFieldService::ORDER_GROUP_PRODUCT, $this->showGroupIds)) && !empty($orderIds))
        {
            $filter = new PurchaseOrderProductFilter($this->clientId);
            $filter->order_id = new In($orderIds);
            $filter->enable_flag = 1;
            $filter->select(['invoice_product_id','order_id','count','cost_amount']);

            $purchaseFilter = new PurchaseOrderFilter($this->clientId);
            $purchaseFilter->enable_flag = new Equal(1);
            $purchaseFilter->select(['purchase_order_id', 'purchase_order_no', 'exchange_rate']);

            $filter->initJoin()
                ->leftJoin($purchaseFilter)
                ->on('purchase_order_id','purchase_order_id');

            $purchaseList = $filter->rawData(false);

            foreach ($purchaseList as $item)
            {
                $cost_amount = $item['cost_amount'];
                if (isset($purchaseOrderMap[$item['order_id']][$item['purchase_order_id']]['cost_amount'])) {
                    $cost_amount += $purchaseOrderMap[$item['order_id']][$item['purchase_order_id']]['cost_amount'];
                }
                $purchaseOrderMap[$item['order_id']][$item['purchase_order_id']] = [
                    'order_id' => $item['order_id'],
                    'purchase_order_id' => $item['purchase_order_id'],
                    'purchase_order_no' => $item['purchase_order_no'],
                    'cost_amount' => $cost_amount,
                    'exchange_rate' => $item['exchange_rate']
                ];
                $purchaseOrderIdList[] = $item['purchase_order_id'];
                if (!isset($relateInvoiceCountMap[$item['invoice_product_id']])) {
                    $relateInvoiceCountMap[$item['invoice_product_id']] = 0;
                }
                $relateInvoiceCountMap[$item['invoice_product_id']] += $item['count'];
            }
            $purchaseOrderMap = array_map(function ($elem){
                return array_values($elem);
            },$purchaseOrderMap);
            if ($this->showPaymentInvoiceInfo && !empty($purchaseOrderIdList)) {
                $purchaseFilter->purchase_order_id = $purchaseOrderIdList;
                $purchaseFilter->select(['amount','purchase_order_id','exchange_rate', 'purchase_order_no', 'payment_status','status', 'product_total_amount', 'addition_cost_amount','product_total_amount_rmb']);
                $list = $purchaseFilter->find();
                $list->getFormatter()->displayFields(['amount','purchase_order_id','exchange_rate', 'purchase_order_no', 'product_total_amount','product_total_amount_rmb', 'payment_status','status', 'addition_cost_amount']);
                $list->getFormatter()->displayShowPaymentInvoiceInfo(true);
                $list->getFormatter()->displayShowStatus(true);
                $paymentInvoiceList = $list->getAttributes();
                $paymentInvoiceRecordMap = [];
                foreach($paymentInvoiceList as $item){
                    $paymentInvoiceRecordMap[$item['purchase_order_id']] = $item;
                }
                $total_amount = 0;
                foreach ($purchaseOrderMap as $key => $purchaseOrderItem) {
                    foreach ($purchaseOrderItem as $item) {
                        $item['cost_amount'] = ($item['exchange_rate'] / 100) * $item['cost_amount'];
                        $purchaseOrderInfo = $paymentInvoiceRecordMap[$item['purchase_order_id']] ?? [];
                        if (!empty($purchaseOrderInfo)) {
                            $purchaseOrderInfo['product_total_amount'] = ($item['exchange_rate'] / 100) * $purchaseOrderInfo['product_total_amount'];
                            $purchaseOrderInfo['addition_cost_amount'] = ($item['exchange_rate'] / 100) * $purchaseOrderInfo['addition_cost_amount'];
                            $paymentInvoiceIds = [];
                            foreach ($purchaseOrderInfo['paymentInfo'] as $index => &$paymentItem) {
                                if (in_array($paymentItem['payment_invoice_id'], $paymentInvoiceIds)) {
                                    unset($purchaseOrderInfo['paymentInfo'][$index]);
                                    continue;
                                }
                                //计算关联采购订单付费采购金额 (当前订单产品金额/订单产品总金额) * (其他费用 + 订单产品金额总数)
                                //这里其他费用被囊括在源单金额中 所以直接计算比例就可以了
                                //$referPaymentInvoiceAmount =$purchaseOrderInfo['product_total_amount']!=0? ($item['cost_amount'] / $purchaseOrderInfo['product_total_amount']) * ($purchaseOrderInfo['product_total_amount'] + $purchaseOrderInfo['addition_cost_amount'])* 支付比例;
                                $paymentItem['amount'] = $purchaseOrderInfo['product_total_amount'] != 0 ? ($item['cost_amount'] / $purchaseOrderInfo['product_total_amount']) * $paymentItem['amount'] : 0;
                                $paymentInvoiceIds[] = $paymentItem['payment_invoice_id'];
                            }
                            unset($paymentInvoiceIds);
                            \LogUtil::info("orderProfit_Purchase_amount  cost_amount" . $item['cost_amount'] . 'product_total_amount ' . $purchaseOrderInfo['product_total_amount'] . ' addition_cost_amount' . $purchaseOrderInfo['addition_cost_amount'].' exchange_rate'.$item['exchange_rate']);
                            $purchaseOrderInfo['referPurchaseAmount'] = $purchaseOrderInfo['product_total_amount'] != 0 ? (($item['cost_amount'] / $purchaseOrderInfo['product_total_amount']) * $purchaseOrderInfo['addition_cost_amount'] + ($item['cost_amount'])) : 0;
                            $total_amount += $purchaseOrderInfo['referPurchaseAmount'];
                            $paymentInvoiceMap[$item['order_id']][] = $purchaseOrderInfo;
                        }
                    }
                }
                $paymentInvoiceSummary['total_amount'] = $total_amount;
            }
        }
        if ($this->showCostInvoiceInfo) {
            $costInvoiceFilter = new CostInvoiceFilter($this->clientId);
            $costInvoiceFilter->order_id = array_unique($orderIds);
            $costInvoiceFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
            $costInvoiceSummaryFilter = deep_copy($costInvoiceFilter);
            $costInvoiceSummary = (new CostInvoiceApi())->getSummaryInfo($this->clientId, $costInvoiceSummaryFilter);
            $costInvoiceList = $costInvoiceFilter->find();
            $costInvoiceList->getFormatter()->orderProfitDetailWebSetting();
            $costInvoiceList = $costInvoiceList->getListAttributes();
            foreach ($costInvoiceList as $item) {
                //订单关联的费用单
                $costInvoiceInfoMap[$item['order_id']][] = $item;
            }
            unset($costInvoiceList);
        }

        $freezeProductMap = [];
        $hasPurchaseProductCountMap = [];//已采购产品数量
        $imgId2InfoMap = [];

        if (in_array(CustomFieldService::ORDER_GROUP_PRODUCT, $this->showGroupIds)) {
            $uniqueIds = array_filter($uniqueIds,function ($v){
                return $v!=null && $v!='';
            });

            if(!empty($uniqueIds)) {
                $outBoundFilter = new SaleOutboundRecordFilter($this->clientId);
                $outBoundFilter->refer_type = \Constants::TYPE_ORDER;
                $outBoundFilter->sub_refer_id = $uniqueIds;
                $outBoundFilter->delete_flag = 0;
                $outBoundFilter->select([
                    'unique_id' => function ($column, $table, $systemQuotes) {
                        return 'distinct sub_refer_id as unique_id';
                    }
                ]);
                $outBoundMap = array_column($outBoundFilter->rawData(), 'unique_id', 'unique_id');

                foreach ($groupUniqueIds as $key => $ids) {
                    foreach ($ids as $id) {
                        $invoiceCount = $relateInvoiceCountMap[$id] ?? 0;
                        $outBound = $outBoundMap[$id] ?? null;
                        if ($invoiceCount > 0 || $outBound) {
                            $freezeProductMap[$key] = true;
                            break;
                        }
                    }
                }
            }

            if ($this->showToBePurchaseCount) {
                $purchaseOrderProductFilter = new PurchaseOrderProductFilter($this->clientId);
                $purchaseOrderProductFilter->order_id = $orderIds;
                $purchaseOrderProductFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
                $purchaseOrderProductFilter->select(['invoice_product_id', 'all_purchase_count' => function ($column) {
                    return "sum(count) as all_purchase_count";
                }]);
                $purchaseOrderProductFilter->groupBy('invoice_product_id');
                $hasPurchaseProductCountMap = array_column($purchaseOrderProductFilter->rawData(), 'all_purchase_count', 'invoice_product_id');
            }

            if ($this->showProductImageInfoList && !empty($productImages)) {
                $productImageIds = Arr::uniqueFilterValues(array_column($productImages,'file_id'));
                if (!empty($productImageIds)) {
                    $imgId2InfoMap = \common\library\file\Helper::fileUrlMap($productImageIds);
                }
            }
        }

        $maxDeliveryDateMap = [];
        if($this->showLinkInfo && !empty($orderIds)) {

            $purchaseOrderProductFilter = new PurchaseOrderProductFilter($this->clientId);
            $purchaseOrderProductFilter->order_id = new In($orderIds);
            $purchaseOrderProductFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
            $purchaseOrderProductFilter->select(['order_id']);

            $purchaseOrderFilter = new PurchaseOrderFilter($this->clientId);
            $purchaseOrderFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
            $purchaseOrderFilter->select(['delivery_date'=>function($column,$table,$quote){
                return "max($table.$column) as  max_delivery_date";
            }]);

            $purchaseOrderProductFilter->initJoin()
                ->leftJoin($purchaseOrderFilter)
                ->on('purchase_order_id','purchase_order_id')
                ->joinGroupBy('order_id',$purchaseOrderProductFilter->getTableName());


            $purchaseOrderData = $purchaseOrderProductFilter->rawData(false);

            $maxDeliveryDateMap = array_column($purchaseOrderData,'max_delivery_date','order_id');
        }

        $shippingInvoiceCountMap =[];
        $purchaseOrderCountMap = [];
        $outboundCountMap = [];
        if( $this->showProgressInfo && !empty($orderIds) )
        {
            $filter = new ShippingInvoiceApi($this->clientId, $this->userId);
            $orderShippingList = $filter->getRelateInvoiceProduct(
                $orderIds,
            );
            foreach ($orderShippingList as $item) {
                if (isset($shippingInvoiceCountMap[$item['order_id']])) {
                    $shippingInvoiceCountMap[$item['order_id']]['have_shipping_count'] += $item['have_shipping_count'];
                } else {
                    $shippingInvoiceCountMap[$item['order_id']] = [
                        'have_shipping_count' => $item['have_shipping_count'],
                    ];
                }
            }
        }
        if( $this->showProgressInfo && !empty($orderIds) )
        {
            $purchaseOrderCountMap = \common\library\purchase\purchase_order_product\Helper::getPurchaseOrderCountInfoMap($this->clientId, $orderIds);
            $outboundCountMap = \common\library\oms\product_transfer\outbound\record\Helper::getOutboundCountMap($this->clientId, $orderIds);
            //销售总数量为产品总数量，若产品为组合产品，计算子产品数量
            if(!empty($combineProductData)){
                $combineProductIds = [];
                foreach($combineProductData as $datum){
                    $combineProductIds = array_unique(array_merge($combineProductIds, array_column($datum,'product_id')));
                }
                $combineProductRelationMap = (new CombineProductRelationAPI($this->clientId))->getSubProductInfo($combineProductIds);
                foreach($combineProductData as $k =>  $orderDatum){
                    foreach($orderDatum as $combineProductDatum){
                        foreach($combineProductRelationMap as $item){
                            if($combineProductDatum['product_id'] == $item['combine_product_id']){
                                $orderProductTotalCountMap[$k] += $item['count'] * $combineProductDatum['count'];
                            }
                        }
                    }
                }

            }
        }
        $outboundTransferMap = $purchaseTransferMap = $transferRecordCountMap= [];
        if($this->showTransferInfo){
            list('purchase_transfer_map'=>$purchaseTransferMap, 'outbound_transfer_map'=>$outboundTransferMap) = (new ProductTransferAPI($this->clientId, $this->userId))->getTransferInvoiceMapByReferId($orderIds);

            // 获取订单明细的相关流转数量
            $transferRecordCountMap = [];
            if($this->showTransferType == \Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND){
                $outboundTransferApi = new OutboundProductTransferAPI($this->clientId);
                // 获取订单直接/间接关联的已确认出库单明细（即已下单出库数）
//                $checkPurchaseCountList = $outboundTransferApi->getOrderRelateOutboundInvoiceRecordCount($orderIds);
//                $transferRecordCountMap['check_outbound_count'] = array_column($checkPurchaseCountList, 'check_outbound_count', 'sub_refer_id');

                // 订单关联的出库任务（非草稿状态）明细数（即已安排出库数）
                list($taskOutboundCountList, $taskOutboundTransferList) = $outboundTransferApi->getOrderRelateOutboundTransferRecordCount($orderIds);
                $transferRecordCountMap['task_outbound_count'] = $taskOutboundCountList;
                $orderRelateTaskRecordIds = array_unique(array_column($taskOutboundTransferList, 'transfer_invoice_record_id')); // 与本页订单相关联的非草稿采购任务id

                // 订单关联的出库任务关联的已确定出库单明细数
                $checkTaskOutboundCountList = $outboundTransferApi->getOutboundTransferRelateOutboundInvoiceRecordCount($orderRelateTaskRecordIds);
                $transferRecordCountMap['check_task_outbound_count'] = array_column($checkTaskOutboundCountList, 'check_task_outbound_count', 'sub_refer_id');

            }

            if($this->showTransferType == \Constants::TYPE_PRODUCT_TRANSFER_PURCHASE){
                // 获取已确定状态的采购订单
                $purchaseOrderFilter = new PurchaseOrderFilter($this->clientId);
                $endStatusInfo = (new InvoiceStatusService($this->clientId, \Constants::TYPE_PURCHASE_ORDER))->endStatus();
                $statusIds = array_column($endStatusInfo, 'id');
                if (empty($statusIds)) {
                    $purchaseOrderFilter->alwaysEmpty();
                }
                $purchaseOrderFilter->status = $statusIds;

                $purchaseTransferApi = new PurchaseProductTransferAPI($this->clientId);

                // 获取订单直接/间接关联的已确认采购订单明细（即已下单采购数）
//                $checkPurchaseCountList = $purchaseTransferApi->getOrderRelatePurchaseInvoiceRecordCount($orderIds, $purchaseOrderFilter);
//                $transferRecordCountMap['check_purchase_count'] = array_column($checkPurchaseCountList, 'check_purchase_count', 'invoice_product_id');

                // 订单关联的采购任务（非草稿状态）明细数（即已安排采购数）
                list($taskPurchaseTransferList, $taskPurchaseCountList) = $purchaseTransferApi->getOrderRelatePurchaseTransferRecordCount($orderIds);
                $transferRecordCountMap['task_purchase_count'] = $taskPurchaseCountList;
                $orderRelateTaskRecordIds = array_unique(array_column($taskPurchaseTransferList, 'transfer_invoice_record_id')); // 与本页订单相关联的非草稿采购任务id

                // 订单关联的采购任务关联的已确定采购订单明细数
                $checkTaskPurchaseCountList = $purchaseTransferApi->getPurchaseTransferRelatePurchaseInvoiceRecordCount($orderRelateTaskRecordIds, $purchaseOrderFilter);
                $transferRecordCountMap['check_task_purchase_count'] = array_column($checkTaskPurchaseCountList, 'check_task_purchase_count', 'invoice_product_id');
            }
        }

        $capitalAccountMap = [];
        $capitalAccountIds = Arr::uniqueFilterValues($capitalAccountIds);
        if ($this->showCapitalAccountInfo && !empty($capitalAccountIds)) {
            $capitalAccountFilter = new CapitalAccountFilter($this->clientId);
            $capitalAccountFilter->capital_account_id = $capitalAccountIds;
            $capitalAccountFilter->enable_flag = Constants::ENABLE_FLAG_TRUE;
            $capitalAccountFilter->select(['capital_account_id', 'name', 'capital_name', 'capital_bank', 'bank_account', 'address', 'remark']);
            $capitalAccountList = $capitalAccountFilter->rawData();
            $capitalAccountMap = array_column($capitalAccountList, null, 'capital_account_id');
        }

        $costItemRelationMap = [];
        $costItemRelationIds = Arr::uniqueFilterValues(($costItemRelationIds));

        if ((in_array(CustomFieldService::ORDER_GROUP_FEE, $this->showGroupIds) || $this->showAmountInfo )
            && !empty($costItemRelationIds)) {
            $costItemInvoiceRelationList = CostItemInvoiceRelationApi::getCostItemRelationByInvoice(\Constants::TYPE_ORDER, 1);
            $costItemRelationMap = array_column($costItemInvoiceRelationList, null, 'relation_id');
        }

        $hasReferDownStreamInvoiceMap = [];
        if ($this->showHasReferDownStreamInvoice && $orderIds) {
            $hasReferDownStreamInvoiceResult = Helper::hasReferDownStreamInvoice($this->clientId, $orderIds);
            $hasReferDownStreamInvoiceMap = array_combine($hasReferDownStreamInvoiceResult, array_fill(0, count($hasReferDownStreamInvoiceResult), 1));
        }
        //erp状态
        $erpStatusInfoMap = [];
        if ($this->showErpStatusInfo && $orderIds) {
            $erpStatusInfoResult = (new OrderErpExternalApi())->orderErpInfo($this->clientId, $orderIds);
            $erpStatusInfoMap = array_column($erpStatusInfoResult, null, 'order_id');
        }

        // 公式字段：关联对象数据填充
        if ($this->getShowRelateObjFormulaFields() && $this->hasFormulaFields($this->clientId , \Constants::TYPE_ORDER,$this->getExternalFieldSettingList())) {
            $this->getFormulaHandler($this->clientId, \Constants::TYPE_ORDER)->setCashCollectionMapData($cashCollectionMap);
        }

        // 预获取关联字段涉及的上游字段信息
        $relateCategoryMap = $relateTrailStatusMap =[];
        if ($this->showExternalFieldDetail && in_array('external_field_data', $this->specifyFields)) {
            $targetExternalFieldIds = [
                'category_ids' => \Constants::TYPE_COMPANY,
                'trail_status' => \Constants::TYPE_COMPANY
            ];

            $fields = $this->getExternalFieldSettingList();
            foreach($fields as $externalField){
                if(isset($targetExternalFieldIds[$externalField['relation_field']])){
                    $targetExternalFieldType = $targetExternalFieldIds[$externalField['relation_field']];
                    if($targetExternalFieldType == $externalField['relation_origin_type'] || $targetExternalFieldType == $externalField['relation_type']){
                        if(
                            $externalField['relation_field'] == 'category_ids' ||
                            $externalField['relation_origin_field'] == 'category_ids'
                        ){
                            $externalCategoryIds = array_unique(array_filter(\Util::flattenArray(array_column($externalFieldDatas, $externalField['id']))));
                            if(!empty($externalCategoryIds)){
                                $user = \User::getUserObject($this->userId);
                                $language = str_starts_with($user->getLanguage(), 'zh') ? 'zh' :'en';
                                $relateCategoryMap = \Category::getNameMap($language, $externalCategoryIds);
                            }
                        }
                    }
                    if($externalField['relation_field'] == 'trail_status' ||
                        $externalField['relation_origin_field'] == 'trail_status'){
                        $externalTrailStatusIds = array_unique(array_column($externalFieldDatas, $externalField['id']));
                        if(!empty($externalTrailStatusIds)){
                            $relateTrailStatusMap = array_column(\CustomerOptionService::getCustomerStatusList($this->clientId), "status_name", "status_id");
                        }
                    }
                }
            }
        }

        //国家地区名称
        $countryMap = [];
        if($this->showCountryName){
            $countryAlphaList = array_filter($countrys);
            if (count($countryAlphaList)) {
                $nameKey = \Yii::app()->language == 'zh-CN' ? 'country_name' : 'country_ename';
                $countryMap = array_column(\CountryService::getCountryListFromAlpha2($countryAlphaList), $nameKey, 'alpha2');
            }
        }

        $map = [
            'user' => $userMap,
            'company' => $companyMap,
            'opportunity' => $opportunityMap,
            'customer' => $customerMap,
            'status' => $statusMap,
            'store_map' => $storeMap,
            'alibaba_order_map' => $alibabaOrderMap,
            'seller_account_map' => $sellerAccountMap,
            'export_file_count' =>$exportFileCountMap,
            'pin' => $pinMap,
            'department' => $departmentMap,
            'department_users' => $departmentUsersMap,
            'product_map' => $productMap,
            'product_sku' => $productSkuMap,
            'platform_product_sku' => $platformProductSkuMap,
            'platform_product' => $platformProductMap,
            'cash_collection_info' => $cashCollectionMap,
            'approval_lock_map' => $approvalLockMap,
            'approval_flow_info' => $approvalFlowInfoMap,
            'allow_export_draft' => $allowExportDraftMap,
            'transportMap' => $transportMap,
            'fileInfoMap' => $fileInfoMap,
            'leadInfoMap' => $leadInfoMap,
            'purchaseOrderMap' => $purchaseOrderMap,
            'freezeProductMap' => $freezeProductMap,
            'maxDeliveryDateMap' => $maxDeliveryDateMap,
            'purchase_order_count_map' => $purchaseOrderCountMap,
            'outbound_count_map' => $outboundCountMap,
            'purchase_transfer_map' => $purchaseTransferMap,
            'outbound_transfer_map' => $outboundTransferMap,
            'transfer_record_count_map' => $transferRecordCountMap,
            'order_product_total_count_map' => $orderProductTotalCountMap,
            'capital_account_map' => $capitalAccountMap,
            'cost_item_relation_map' => $costItemRelationMap,
            'has_refer_down_stream_invoice_map' => $hasReferDownStreamInvoiceMap,
            'cashCollectionInfoMap' => $cashCollectionInfoMap,
            'cashCollectionSummary' => $cashCollectionSummary,
            'costInvoiceInfoMap' => $costInvoiceInfoMap,
            'costInvoiceSummary' => $costInvoiceSummary,
            'paymentInvoiceMap' => $paymentInvoiceMap,
            'paymentInvoiceSummary' => $paymentInvoiceSummary,
            'outboundRecordMap' => $outboundRecordMap,
            'outboundRecordSummary' => $outboundRecordSummary,
            'quoteFieldsRows' => $quoteFieldRows,
            'recordQuoteRows' => $recordQuoteRows,
            'erp_status_info_map' => $erpStatusInfoMap,
            'company_trail_status' => $relateTrailStatusMap,
            'company_category' => $relateCategoryMap,
            'has_purchase_product_count_map' => $hasPurchaseProductCountMap,
            'shipping_invoice_cost_map' => $shippingCostMap,
            'shipping_invoice_count_map' => $shippingInvoiceCountMap,
            'shipping_record_map' => $shippingRecordMap,
            'country' => $countryMap,
            'imgId2InfoMap' => $imgId2InfoMap,
        ];


        $this->setMapData($map);
    }

    public function openApiListInfoSetting()
    {
        $this->setSpecifyFields(
            [
                'exchange_rate',
                'exchange_rate_usd',
                'name',
                'users',
                'currency',
                'amount',
                'amount_rmb',
                'amount_usd',
                'addition_cost_amount',
                'product_total_amount',
                'product_total_count',
                'cost_with_tax_total',
                'price_contract',
                'receive_remittance_way',
                'price_contract_remark',
                'receive_remittance_remark',
                'insurance_remark',
                'bank_info',
                'order_contract',
                'company_name',
                'company_phone',
                'company_fax',
                'company_address',
                'customer_name',
                'customer_phone',
                'customer_email',
                'customer_address',
                'transport_mode',
                'shipment_deadline',
                'shipment_port',
                'target_port',
                'shipment_deadline_remark',
                'more_or_less',
                'package_remark',
                'marked',
                'account_date',
                'country',
                'opportunity_id',
                'remark',
                'link_status',
                'source_type',
                'archive_type',
                'create_time',
                'update_time',
                'create_user',
                'update_user',
            ]
        );
        $this->setShowDepartmentsName(true);
        $this->setShowUsersInfo(true);
        $this->setShowHandlerInfo(true);
        $this->setShowStatusInfo(true);
        $this->setShowCompanyInfo(true);
        $this->setShowLink([
            Constant::ORDER_LINK_STOCK_UP,
            Constant::ORDER_LINK_OUTBOUND,
            Constant::ORDER_LINK_END
        ]);
    }

    public function companyOrderListSetting(){
        $this->setSpecifyFields(
            [
                'name',
                'account_date',
                'create_time',
                'update_time',
                'amount',
                'amount_rmb',
                'amount_usd',
                'enable_flag',
                'source_type',
                'currency',
                'account_flag'
            ]
        );
        $this->setShowCashCollectionStatus(true);
        $this->setShowCreateUserInfo(true);
        $this->setShowStatusInfo(true);
        $this->setShowPurchaseOrder(true);
        $this->setShowInvoiceProductCount(true);
        $this->setShowUsersInfo(true);
    }

    public function platformProductMatchSetting()
    {
        $this->setSpecifyFields([
            'update_user',
            'name',
            'departments',
            'currency',
            'amount_rmb',
            'amount_usd',
            'exchange_rate',
            'exchange_rate_usd',
            'company_id',
            'approval_status',
            'account_date',
            'create_time',
            'update_time',
            'archive_type',
            'amount',
            'amount_rmb',
            'amount_usd',
            'users',
            'type',
            'source_type',
            'status_action',
            'ali_status_name',
            'fulfillment_channel',
            'product_total_amount_rmb',
            'product_total_amount_usd',
            'ali_order_id',
            'user_id',
            'link_status',
            'order_gross_margin',
            'order_gross_margin_cny',
            'order_gross_margin_usd',
        ]);
        $this->setShowInfoGroup(true);
        $this->setShowGroupIds([CustomFieldService::ORDER_GROUP_PRODUCT]);
    }

    public function listInfoSetting(){
        $this->setSpecifyFields(
            [
                'approval_status',
                'exchange_rate',
                'exchange_rate_usd',
                'name',
                'users',
                'departments',
                'currency',
                'amount',
                'amount_rmb',
                'amount_usd',
                'account_date',
                'create_time',
                'update_time',
                'enable_flag',
                'external_field_data',
                'price_contract',
                'cost_name',
                'percent_of_total_amount',
                'cost',
                'cost_list',
                'receive_remittance_way',
                'shipment_deadline_remark',
                'more_or_less',
                'bank_info',
                'order_contract',
                'addition_cost_amount',
                'marked',
                'product_total_amount',
                'package_unit',
                'currency',
                'exchange_rate',
                'company_address',
                'customer_name',
                'customer_phone',
                'company_name',
                'company_phone',
                'company_fax',
                'company_address',
                'remark',
                'insurance_remark',
                'transport_mode',
                'shipment_port',
                'target_port',
                'shipment_deadline',
                'receive_remittance_remark',
                'price_contract_remark',
                'customer_email',
                'customer_address',
                'package_remark',
                'country',
                'ali_status_id',
                'type',
                'ali_store_id',
                'seller_account_id',
                'archive_type',
                'ali_order_id',
                'product_total_amount_rmb',
                'product_total_amount_usd',
                'payment_link',
                'source_type',
                'status_action',
                'last_sync_time',
                'ali_status_name',
                'fulfillment_channel',
                'user_id',
                'link_status',
                'order_gross_margin',
                'order_gross_margin_cny',
                'order_gross_margin_usd',
                'cost_with_tax_total',
                'capital_account_id',
                'tax_refund_type',
            ]
        );
        $this->setShowUsersDepartment(true);
        $this->setShowDepartmentsName(true);
        $this->setShowUsersInfo(true);
        $this->setShowStatusInfo(true);
        $this->setShowCompanyInfo(true);
        $this->setShowPinFlag(true);
        $this->setShowOpportunityInfo(true);
        $this->setShowAllowExport(true);
        $this->setShowCashCollectionStatus(true);
        $this->setShowTransport(true);
        $this->setShowHandlerInfo(true);
        $this->setShowCreateUserInfo(true);
        $this->setShowUpdateUserInfo(true);
        $this->setShowCustomerInfo(true);
        $this->setShowAlibabaAccountInfo(true);
        $this->setShowAlibabStatusInfo(true);
        $this->setShowStoreInfo(true);
        $this->setShowStatusAction(true);
        $this->setShowCapitalAccountInfo(true);
        $this->setShowHasReferDownStreamInvoice(true);
        $this->setShowAlibabOrderInfo(true);
        $this->setShowShippingCostList(true);
        $this->setShowAmountInfo(true);
        $this->setShowLink([
            Constant::ORDER_LINK_CASH_COLLECTION,
            Constant::ORDER_LINK_STOCK_UP,
            Constant::ORDER_LINK_OUTBOUND,
            Constant::ORDER_LINK_END
        ]);
    }

    public function aliOrderListInfoSetting(){
        $this->setSpecifyFields(
            [
                'approval_status',
                'exchange_rate',
                'exchange_rate_usd',
                'name',
                'users',
                'departments',
                'currency',
                'amount',
                'amount_rmb',
                'amount_usd',
                'account_date',
                'create_time',
                'update_time',
                'enable_flag',
                'external_field_data',
                'price_contract',
                'cost_name',
                'percent_of_total_amount',
                'cost',
                'cost_list',
                'receive_remittance_way',
                'shipment_deadline_remark',
                'more_or_less',
                'bank_info',
                'order_contract',
                'addition_cost_amount',
                'marked',
                'product_total_amount',
                'package_unit',
                'currency',
                'exchange_rate',
                'company_address',
                'customer_name',
                'customer_phone',
                'company_name',
                'company_phone',
                'company_fax',
                'company_address',
                'remark',
                'insurance_remark',
                'transport_mode',
                'shipment_port',
                'target_port',
                'shipment_deadline',
                'receive_remittance_remark',
                'price_contract_remark',
                'customer_email',
                'customer_address',
                'package_remark',
                'country',
                'ali_status_id',
                'type',
                'ali_store_id',
                'seller_account_id',
                'archive_type',
                'source_type',
                'ali_order_id',
                'product_total_amount_rmb',
                'product_total_amount_usd',
                'payment_link',
                'status_action',
                'last_sync_time',
                'ali_status_name',
                'fulfillment_channel',
                'user_id',
                'link_status',
                'order_gross_margin',
                'order_gross_margin_cny',
                'order_gross_margin_usd',
                'tax_refund_type'
            ]
        );
        $this->setShowUsersDepartment(true);
        $this->setShowDepartmentsName(true);
        $this->setShowUsersInfo(true);
        $this->setShowHandlerInfo(true);
        $this->setShowStatusInfo(true);
        $this->setShowCompanyInfo(true);
        $this->setShowPinFlag(true);
        $this->setShowOpportunityInfo(true);
        $this->setShowAllowExport(true);
        $this->setShowCashCollectionStatus(true);
        $this->setShowTransport(true);
        $this->setShowOpportunityInfo(true);
        $this->setShowHandlerInfo(true);
        $this->setShowCreateUserInfo(true);
        $this->setShowUpdateUserInfo(true);
        $this->setShowCustomerInfo(true);
        $this->setShowLeadInfo(true);
        $this->setShowAlibabaAccountInfo(true);
        $this->setShowAlibabStatusInfo(true);
        $this->setShowStoreInfo(true);
        $this->setShowStatusAction(true);
        $this->setShowAlibabOrderInfo(true);
        $this->setShowLink([
            Constant::ORDER_LINK_CASH_COLLECTION,
            Constant::ORDER_LINK_STOCK_UP,
            Constant::ORDER_LINK_OUTBOUND,
            Constant::ORDER_LINK_END
        ]);
    }

    public function appListInfoSetting(){
        $this->setSpecifyFields(
            [
                'approval_status',
                'exchange_rate',
                'exchange_rate_usd',
                'name',
                'users',
                'departments',
                'currency',
                'amount',
                'amount_rmb',
                'amount_usd',
                'account_date',
                'create_time',
                'update_time',
                'enable_flag',
                'external_field_data',
                'price_contract',
                'cost_name',
                'percent_of_total_amount',
                'cost',
                'cost_list',
                'receive_remittance_way',
                'shipment_deadline_remark',
                'more_or_less',
                'bank_info',
                'order_contract',
                'addition_cost_amount',
                'marked',
                'product_total_amount',
                'package_unit',
                'currency',
                'exchange_rate',
                'company_address',
                'customer_name',
                'customer_phone',
                'company_name',
                'company_phone',
                'company_fax',
                'company_address',
                'remark',
                'insurance_remark',
                'transport_mode',
                'shipment_port',
                'target_port',
                'shipment_deadline',
                'receive_remittance_remark',
                'price_contract_remark',
                'customer_email',
                'customer_address',
                'package_remark',
                'source_type',
                'last_sync_time',
                'country',
                'fulfillment_channel',
                'product_total_amount_rmb',
                'product_total_amount_usd',
                'ali_order_id',
            ]
        );
        $this->setShowUsersDepartment(true);
        $this->setShowDepartmentsName(true);
        $this->setShowUsersInfo(true);
        $this->setShowStatusInfo(true);
        $this->setShowCompanyInfo(true);
        $this->setShowPinFlag(true);
        $this->setShowOpportunityInfo(true);
        $this->setShowAllowExport(true);
        $this->setShowCashCollectionStatus(true);
        $this->setShowTransport(true);
        $this->setShowHandlerInfo(true);
        $this->setShowCreateUserInfo(true);
        $this->setShowUpdateUserInfo(true);
        $this->setShowCustomerInfo(true);
        $this->setShowAlibabaAccountInfo(true);
        $this->setShowAlibabStatusInfo(true);
        $this->setShowStoreInfo(true);
        $this->setShowExternalFieldDetail(true);
        $this->setshowArchiveInfo(true);
    }

    public function selectListInfoSetting(){
        $this->setSpecifyFields(
            [
                'name',
                'currency',
                'create_time',
                'update_time',
                'enable_flag'
            ]
        );

        $this->setShowStatusInfo(true);
        $this->setShowCompanyInfo(true);
        $this->setShowExportFileCount(true);
    }

    public function opportunityDetailListInfoSetting()
    {
        $this->setSpecifyFields([
            'order_id','approval_status', 'status', 'name', 'currency', 'amount', 'amount_rmb', 'amount_usd', 'create_time', 'update_time', 'enable_flag', 'opportunity_id'
        ]);
        $this->setShowStatusInfo(true);
    }

    public function detailInfoSetting($groups=[]){
        $this->setSpecifyFields(
            [
                'update_user',
                'name',
                'departments',
                'currency',
                'amount_rmb',
                'amount_usd',
                'exchange_rate',
                'exchange_rate_usd',
                'company_id',
                'company_name',
                'approval_status',
                'account_date',
                'create_time',
                'update_time',
                'archive_type',
                'amount',
                'amount_rmb',
                'amount_usd',
                'users',
                'type',
                'source_type',
                'status_action',
                'ali_status_name',
                'fulfillment_channel',
                'product_total_amount_rmb',
                'product_total_amount',
                'product_total_amount_usd',
                'parts_total_count',
                'product_total_count',
                'product_total_count_no_parts',
                'product_list',
                'ali_order_id',
                'user_id',
                'link_status',
                'order_gross_margin',
                'order_gross_margin_cny',
                'order_gross_margin_usd',
                'capital_account_id',
                'has_partial_purchase_order',
                'cost_list'
            ]
        );
        $this->setShowLastApprovalInfo(true);
        $this->setShowPinFlag(true);
        $this->setShowStatusInfo(true);
        if( !empty($groups) ){
            $this->setShowInfoGroup(true);
            $this->setShowGroupIds($groups);
        }
        $this->setShowUsersInfo(true);
        $this->setShowUsersDepartment(true);
        $this->setShowCompanyInfo(true);
        $this->setShowDepartmentsName(true);
        $this->setShowApprovalFlowInfo(true);
        $this->setShowApprovalFlowEventInfo(true);
        $this->setShowAllowExport(true);
        $this->setShowOpportunityInfo(true);
        $this->setShowHandlerInfo(true);
        $this->setShowCreateUserInfo(true);
        $this->setShowUpdateUserInfo(true);
        $this->setShowCustomerInfo(true);
        $this->setShowCashCollectionStatus(true);
        $this->setShowLeadInfo(true);
        $this->setShowAlibabStatusInfo(true);
        $this->setShowAlibabOrderInfo(true);
        $this->setShowStoreInfo(true);
        $this->setShowCapitalAccountInfo(true);
        $this->setShowStatusAction(true);
        $this->setShowAssocPlatformProduct(true);
        $this->setShowLinkInfo(true);
        $this->setShowAmountInfo(true,false);
        $this->setShowShippingCostList(true);
        $this->setShowShippingRecordInfo(true);
        $this->setShowCanChangeCurrency(true);
    }

    public function appDetailInfoSetting($groups=[]){

        $this->setSpecifyFields(
            [
                'name',
                'account_date',
                'currency',
                'amount',
                'amount_rmb',
                'amount_usd',
            ]
        );
        $this->setShowLastApprovalInfo(true);
        $this->setShowApprovalFlowInfo(true);
        $this->setShowStatusInfo(true);
        $this->setShowHandlerInfo(true);


        if( !empty($groups) ){
            $this->setShowInfoGroup(true);
            $this->setShowGroupIds($groups);
        }

        $this->setFormatAppFieldList(true);
        $this->setShowFullImageUrl(true);
        $this->setShowDepartmentsName(true);
        $this->setFilterFieldList(self::FIELD_LIST_FILTER_APP);
        $this->setFieldFormatType(self::FIELD_FORMAT_TYPE_JSON);

    }

    public function appCompanyOrderListSetting(){
        $this->setSpecifyFields(
            [
                'name',
                'account_date',
                'amount',
                'amount_rmb',
                'amount_usd',
                'enable_flag',
                'currency',
            ]
        );
        $this->setShowCashCollectionStatus(true);
        $this->setShowCreateUserInfo(true);
        $this->setShowStatusInfo(true);
    }

    public function appOrderOverviewInfoSetting(){
        $this->setSpecifyFields(
            [
                'name',
                'product_total_amount',
                'amount',
                'amount_usd',
                'amount_rmb',
                'currency',
                'account_date',
                'source_type',
                'order_no',
                'order_id',
                'approval_status',
            ]
        );
        $this->setShowAmountInfo(true);
        $this->setShowHandlerInfo(true);
        $this->setShowStatusInfo(true);
        $this->setShowAlibabStatusInfo(true);
        $this->setShowCompanyInfo(true);
        $this->setShowCashCollectionStatus(true, false);
        $this->setShowApprovalFlowInfo(true);
        $this->setShowLastApprovalInfo(true);
        $this->setShowApprovalStatusInfo(true);
        $this->setShowApprovalDiffTip(true);
        $this->setClientScene(\Constants::CLIENT_TYPE_APP);
    }

    public function appOrderAmountInfoSetting(){
        $this->setSpecifyFields(
            [
                'amount',
                'product_total_amount',
                'account_date',
                'currency',
            ]
        );
        $this->setShowAmountInfo(true);
    }

    public function appOrderDetailInfoSetting()
    {
        $this->setSpecifyFields(
            [
                'order_id',
                'order_no',
                'create_user',
                'create_time',
                'update_user',
                'update_time',
                'source_lead_id',
                'source_type',
                'last_sync_time',
            ]
        );
        $this->setShowLeadInfo(true);
        $this->setShowOrderDetailInfo(true);
        $this->showQuoteFieldFlag(false);
    }

    public function appOrderProductListSetting()
    {
        $this->setSpecifyFields(
            [
                'order_id',
                'order_no',
                'product_list',
            ]
        );
        $this->setShowProductList(true);
    }

    public function orderSpecifyProductSetting($invoiceProductId)
    {
        $this->setSpecifyFields(
            [
                'order_id',
                'order_no',
            ]
        );
        $this->setShowProductList(true);
        $this->setSpecifyInvoiceProductId($invoiceProductId);
    }

    public function orderPushDownSetting($groups=[]){
        $this->setSpecifyFields(
            [
                'update_user',
                'name',
                'currency',
                'amount_rmb',
                'amount_usd',
                'exchange_rate',
                'exchange_rate_usd',
                'company_id',
                'approval_status',
                'account_date',
                'create_time',
                'update_time',
                'archive_type',
                'amount',
                'amount_rmb',
                'amount_usd',
                'users',
                'type',
                'source_type',
                'status_action',
                'ali_status_name',
                'fulfillment_channel',
                'product_total_amount_rmb',
                'product_total_amount_usd',
                'user_id',
                'has_partial_purchase_order',
            ]
        );
        $this->setShowInfoGroup(true);
        $this->setShowGroupIds($groups);
        $this->setShowSubProduct(true);
        $this->setShowCompanyInfo(true);
        $this->setShowToBePurchaseCount(true);
    }

    /**
     * @param bool $showToBePurchaseCount
     */
    public function setShowToBePurchaseCount(bool $showToBePurchaseCount): void
    {
        $this->showToBePurchaseCount = $showToBePurchaseCount;
    }

    /**
     * @param bool $showProductImageInfoList
     */
    public function setShowProductImageInfoList(bool $showProductImageInfoList): void
    {
        $this->showProductImageInfoList = $showProductImageInfoList;
    }

    /**
     * @param int $ignorePurchaseCountDone
     */
    public function setIgnorePurchaseCountDone(int $ignorePurchaseCountDone): void
    {
        $this->ignorePurchaseCountDone = $ignorePurchaseCountDone;
        $this->showQuoteFieldFlag(true);
    }

    protected function format($data = null)
    {
        $this->recordPrivilege($data);
        $orderId = $data['order_id'];
        $orderNo = $data['order_no'];
        $result = [
            'order_id' => $orderId,
            'order_no' => $orderNo,
            'lock_flag' => $this->getMapData('approval_lock_map', $orderId) ?? 0,
        ];

        if(isset($data['scope_user_ids'])){
            $result['scope_user_ids'] = $data['scope_user_ids'];
        }

        $fieldMap = $this->getFlattenFieldMap(ObjConstant::OBJ_ORDER);
        $productFieldMap = $this->getFlattenFieldMap(ObjConstant::OBJ_ORDER_PRODUCT);
        // 数值字段格式化逻辑（兼容老版本）
        foreach($data as $field => $value){
            if($field == 'product_list'){
                foreach($value as &$productItem){
                    foreach($productItem as $productField => $productValue){
                        if(!isset($productFieldMap[$productField]) && $productField != 'external_field_data'){
                            continue;
                        }
                        $this->fieldHandler($productFieldMap, $productField, $productValue);
                        $productItem[$productField] = $productValue;
                    }
                }
                $data[$field] = $value;
                continue;
            }
            if(!isset($fieldMap[$field]) && $field != 'external_field_data'){
                continue;
            }
            $this->fieldHandler($fieldMap, $field, $value);
            $data[$field] = $value;
        }

        $data['product_total_amount'] = floatval($data['product_total_amount']);
        $data['addition_cost_amount'] = floatval($data['addition_cost_amount']);
        $data['package_gross_weight_amount'] = floatval($data['package_gross_weight_amount']);
        $data['package_volume_amount'] = floatval($data['package_volume_amount']);
        $data['product_total_count'] = floatval($data['product_total_count']);
        $data['ali_order_id'] = (string)$data['ali_order_id'];
        if (!is_array($data['user_id'])) {
            $data['user_id'] = PgsqlUtil::trimArray($data['user_id'] ?? '{}');
        }

        if ($this->showMainCurrencyExchangeRate) {
            $currency = Client::getClient($this->clientId)->getMainCurrency();
            if ($currency == ExchangeRateService::USD) {
                $data['exchange_rate'] = $data['exchange_rate_usd'];
            }
        }

        //用于保证字段被隐藏情况可以正常展示汇率
        if ($this->showExchangeRate) {
            $result['exchange_rate'] = $data['exchange_rate']??0;
            $result['exchange_rate_usd'] = $data['exchange_rate_usd']??0;
        }

        if (isset($data['fulfillment_channel']) && in_array('fulfillment_channel', $this->specifyFields)) {
            $data['fulfillment_channel'] = \Yii::t('invoice', $data['fulfillment_channel']);
        }

        if (!is_array($data['users'])) {
            $data['users'] = json_decode($data['users'] ?? '{}', true);
        }

        if (!is_array($data['departments'])) {
            $data['departments'] = json_decode($data['departments'] ?? '{}', true);
        }

        // 过滤删除部门
        foreach ($data['departments'] as $dataDepartmentKey => $department) {
            $departmentInfo = $this->getMapData('department', $department['department_id'] ?? '');
            if (is_null($departmentInfo)) {
                unset($data['departments'][$dataDepartmentKey]);
            }
        }

        $data['departments'] = array_values($data['departments']);

        if (!is_array($data['handler'])) {
            $data['handler'] = PgsqlUtil::trimArray($data['handler'] ?? '{}');
        }


        if ($this->showSubProduct) {
            //下推展示子产品列表
            $data['product_list'] = $this->getSubProductList($orderId,$data['product_list']);
        }

        if (!is_array($data['product_list'])) {
            $data['product_list'] = json_decode($data['product_list'] ?? '{}', true);
        }

        if($this->showTransferInfo && $this->showTransferType) {
//            $checkPurchaseCountMap = $this->getMapData('transfer_record_count_map', 'check_purchase_count');
            $taskPurchaseCountMap = $this->getMapData('transfer_record_count_map', 'task_purchase_count');
            $checkTaskPurchaseCountMap = $this->getMapData('transfer_record_count_map', 'check_task_purchase_count');
//            $checkOutboundCountMap = $this->getMapData('transfer_record_count_map', 'check_outbound_count');
            $taskOutboundCountMap = $this->getMapData('transfer_record_count_map', 'task_outbound_count');
            $checkTaskOutboundCountMap = $this->getMapData('transfer_record_count_map', 'check_task_outbound_count');
        }

        $fields = $this->getExternalFieldSettingList();
        $productList = [];
        $product_total_volume =0;
        foreach ($data['product_list'] as &$product) {
            if ($this->specifyInvoiceProductId > 0 && $this->specifyInvoiceProductId != $product['unique_id'])
            {
                continue;
            }
            $product_total_volume += floatval($product["[\"package_volume_subtotal\"]"]?? 0);
            $productInfo = $this->getMapData('product_sku', $product['sku_id']??0);
            $product['product_no'] = $productInfo['product_no'] ?? $product['product_no']??'';
            $product['sku_code'] = $productInfo['sku_code'] ?? $product['sku_code']??'';
            $product['attributes_info'] = $productInfo['attributes_info'] ?? $product['attributes_info']??[];
            $product['ali_product_id'] = !empty($product['platform_product_info']['third_product_id']) ?$product['platform_product_info']['third_product_id'] :($productInfo['ali_product_id'] ?? 0);
            $product['gross_margin'] = round(floatval($product['gross_margin'] ?? 0), 4);
            $product['product_disable_flag'] = $productInfo['product_disable_flag'] ??$productInfo['disable_flag'] ?? 0;
            $product['product_enable_flag'] = $productInfo['product_enable_flag'] ??$productInfo['enable_flag'] ?? 0;
            // todo @young 暂时使用产品库图片，需要改造为订单明细图片
            if ($this->showProductImageInfoList) {
                $productImages = $product['product_images'] ?? '[]';
                $productImages = is_array($productImages) ? $productImages : json_decode($productImages, true);
                $product['product_image'] = $product['product_images'] = [];
                if (!empty($productImages[0]['file_id'])) {
                    $imageInfo = $this->getMapData('imgId2InfoMap', $productImages[0]['file_id']);
                    $imageInfo = is_array($imageInfo) ? $imageInfo : [];
                    if (!empty($imageInfo)) {
                        $imageInfo = [array_merge($imageInfo, $productImages[0])];
                    }
                    $product['product_image'] = $imageInfo;
                }
                if (empty($product['product_image']) && !empty($productInfo['image_info'])) {
                    $product['product_image'] = [array_merge($productInfo['image_info'], ['user_id' => (string)($data['user_id'][0] ?? 0), 'create_time' => xm_function_now()])];
                }
            }

            $product['package_count'] = 0;
            if (!empty($product['count']) && !empty($product['count_per_package']) && is_numeric($product['count']) && is_numeric($product['count_per_package'])) {
                $product['package_count'] = ($product['count_per_package'] != 0) ? ceil($product['count'] / $product['count_per_package']) : 0;
            }
            if ($this->showProductQuoteField) {
                $recordQuoteRows = $this->getMapData("recordQuoteRows", $product['unique_id']) ?? [];
                if (!empty($recordQuoteRows)) {
                    $recordQuoteRows = array_filter($recordQuoteRows, function ($k) {
                        return is_numeric($k);
                    }, ARRAY_FILTER_USE_KEY);
                    $product['external_field_data'] = $recordQuoteRows + ($product['external_field_data'] ?? []);
                }
            }

            if($this->showTransferInfo && $this->showTransferType){
//                $product['check_purchase_count'] = $checkPurchaseCountMap[$product['unique_id']] ?? 0; //已下单采购数
                $product['task_purchase_count'] = $taskPurchaseCountMap[$product['unique_id']] ?? 0;   // 已安排采购数
                $checkTaskPurchaseCount = $checkTaskPurchaseCountMap[$product['unique_id']] ?? 0;        // 订单关联任务所关联的采购明细
                $product['to_purchase_count'] = ProductTransferHelper::formatTransferCount($product['count'] - $product['task_purchase_count']);   //待采购数量
                if($product['to_purchase_count'] < 0){
                    $product['to_purchase_count'] = 0;
                }

//                $product['check_outbound_count'] = $checkOutboundCountMap[$product['unique_id']] ?? 0; //已下单采购数
                $product['task_outbound_count'] = $taskOutboundCountMap[$product['unique_id']] ?? 0;   // 已安排采购数
                $checkTaskOutboundCount = $checkTaskOutboundCountMap[$product['unique_id']] ?? 0;        // 订单关联任务所关联的出库明细
                $product['check_task_outbound_count'] = $checkTaskOutboundCount;
                $product['to_outbound_count'] = ProductTransferHelper::formatTransferCount($product['count'] - $product['task_outbound_count']);   //待采购数量
                if ($product['to_outbound_count'] < 0) {
                    $product['to_outbound_count'] = 0;
                }
            }
            if ($this->showShippingRecordInfo){
                $product['have_shipping_count'] = $this->getMapData('shipping_record_map', $product['unique_id'] ?? 0)['have_shipping_count'] ?? 0;
                $product['todo_shipping_count'] = max(($product['count'] - $product['have_shipping_count']), 0);
            }
            $product['refer_order_no'] = $orderNo;
            //下推到采购订单
            if ($this->showToBePurchaseCount && !$this->showTransferInfo) {
                $product['has_purchase_count'] = $this->getMapData('has_purchase_product_count_map', $product['unique_id'] ?? 0);
                $product['to_purchase_count'] = ProductTransferHelper::formatTransferCount($product['count'] - $product['has_purchase_count']);   //待采购数量
                if($product['to_purchase_count'] < 0){
                    $product['to_purchase_count'] = 0;
                }
            }

            //下推 过滤采购数是0的产品
            if ($this->showSubProduct && $this->ignorePurchaseCountDone && (isset($product['to_purchase_count']) && $product['to_purchase_count'] == 0)) {
                continue;
            }
            //主产品 赋值master_group_id
            if (!empty($product['is_master_product'] ?? 0) && empty($product['master_id'] ?? 0)) {
                $product['master_group_id'] = $product['unique_id'];
            }
            //配件产品 赋值master_group_id
            if (empty($product['is_master_product'] ?? 0) && !empty($product['master_id'] ?? 0)) {
                $product['master_group_id'] = $product['master_id'];
            }
            $productList[] = $product;
        }
        $result['product_total_volume'] = $product_total_volume;
        $data['product_list'] = $productList;

        $result['shipment_port'] = $data['shipment_port']??'';
        $result['company_address'] = $data['company_address']??'';
        $result['target_port'] = $data['target_port']??'';
        $result['shipment_deadline'] = $data['shipment_deadline']??'';
        $result['transport_mode'] = $data['transport_mode']??'';
        if (!is_array($data['cost_list'])) {
            $data['cost_list'] = json_decode($data['cost_list'] ?? '{}', true);
        }

        if (!is_array($data['file_list'])) {
            $data['file_list'] = json_decode($data['file_list'] ?? '{}', true);
        }

        $data['delete_time'] = $data['delete_time'] != '1970-01-01 08:00:00' ? $data['delete_time'] : '';
        $data['account_date'] = $data['account_date'] != '1970-01-01 08:00:00' ? $data['account_date'] : '';

        // 公式字段计算
        if($this->getShowRelateObjFormulaFields() && $this->hasFormulaFields){
            $this->calculateRelateObjFieldsByRow($this->clientId, \Constants::TYPE_ORDER, $data, $this->getExternalFieldSettingList());
        }

        $result = array_merge($result,$this->buildFieldsInfo($data));
        $result['_id'] = $orderId;
        $result['approval_flow_info'] = null;
        //审批流信息
        if ($this->showApprovalFlowInfo) {
            $result['approval_flow_info'] = $this->getMapData('approval_flow_info', $data['order_id']);
        }

        if ($this->showDepartmentsName) {
            $result['departments_info'] = [];
            foreach ($data['departments'] as $department) {
                $departmentInfo = $this->getMapData('department', $department['department_id']);
                $department['name'] = $departmentInfo['name'];
                $result['departments_info'][] = $department;
            }
        }

        if ($this->showCreateUserInfo) {
            $result['create_user_info'] = $this->getMapData('user', $data['create_user']) ?? null;
        }

        if ($this->showUpdateUserInfo) {
            $result['update_user_info'] = $this->getMapData('user', $data['update_user']) ?? null;
        }

        if( $this->showUsersInfo ){
            $users = is_array($data['users'])?$data['users']:[];
            foreach ( $users as &$elem ){
                $user = $this->getMapData('user',$elem['user_id']);
                if ($this->showUsersDepartment) {
                    $elem['department_info'] = $this->getMapData('department_users', $elem['user_id']);
                }
                $elem['nickname'] = $user['nickname']??'';
                $elem['avatar'] = $user['avatar']??'';
            }
            $result['users_info'] =  $users;
        }

        if( $this->showHandlerInfo ){
            $handler = [];
            $data['handler'] = empty($data['handler'])?[]:$data['handler'];
            foreach ($data['handler'] as $userId){
                $handler[] = $this->getMapData('user',$userId);
            }
            $result['handler_info'] = $handler;
        }
        if ($this->showCompanyInfo) {
            $result['company'] = $this->getMapData('company', $data['company_id']) ?? [
                'company_id' => $result['company_id'] ?? 0,
                'name' => $result['company_name'] ?? '',
                'serial_id' => '',
                'is_archive' => 0
            ];
            empty($result['company_name']) && $result['company_name'] = $result['company']['name'] ?? '';
        }

        if( $this->showOpportunityInfo ){
            $result['opportunity'] = $this->getMapData('opportunity',$data['opportunity_id']);
        }

        if( $this->showCustomerInfo ){
            $result['customer'] = $this->getMapData('customer',$data['customer_id']);
        }


        if( $this->showStatusInfo ){
            $result['status_info'] = $this->getMapData('status', $data['status'] ?? 0) ?? [];
        }


        if( $this->showLastApprovalInfo ){
            $result['last_approval_info'] =  \common\library\approval_flow\Helper::lastApproval($data['order_id'],$result['approval_flow_info']);

        }
        if( $this->showInfoGroup ){
            $formatFieldMap = $this->buildInfoGroupFormatFiled($data);
            $result['data'] = $this->formatInfoGroup($data,$formatFieldMap,true);
        }

        if( $this->showFileList ){
            $result['file_list'] = $this->formatFileList($data['file_list']??[],$this->showFileOffset,$this->showFileLimit);

        }

        if($this->showFileTotalCount){
            $result['file_count'] = count($data['file_list']??[]);
        }

        if( $this->showExportFileCount ){
            $result['export_file_count'] = $this->getMapData('export_file_count',$orderId)??0;
        }

        if( $this->showPinFlag ){
            $result['is_pin'] = $this->getMapData('pin',$orderId)?1:0;
        }
        if ($this->showCashCollectionStatus) {
            if ($this->showCashCollectionDetail) {
                $result['cash_collection_info'] = $this->getMapData('cash_collection_info', $orderId) ?: \common\library\cash_collection\Helper::getDefaultCashCollectionStatusInfo([
                    'amount' => floatval($data['amount'] ?? 0),
                    'amount_rmb' => floatval($data['amount_rmb'] ?? 0),
                    'amount_usd' => floatval($data['amount_usd'] ?? 0),
                ]);
                $mainCurrency = Client::getClient($this->clientId)->getMainCurrency();
                $result['cash_collection_percentage'] = ($mainCurrency == \common\library\exchange_rate\ExchangeRateService::USD) ? ($result['cash_collection_info']['percentage_usd'] ?? 0) : ($result['cash_collection_info']['percentage_rmb'] ?? 0);
            } else {
                $cashStatus = $this->getMapData('cash_collection_info', $orderId);
                $result['cash_collection_info'] = [
                    'status'=>$cashStatus['status'] ?? 0,
                    'status_name'=>$cashStatus['status_name'] ?? '',
                ];
            }
        }

        if ($this->showAllowExport)
        {
            $result['allow_export'] = 1;
            $isBeginning = ($this->getMapData('status',$data['status']??0))['is_beginning'] ?? 0;
            if ($isBeginning && !($this->getMapData('allow_export_draft', $this->clientId) ?? 1))//草稿 & 设置不允许导出草稿
                $result['allow_export'] = 0;
        }




        if ($this->showSystemInfo) {

            $result['system_info'] = [
                'create_user' => $this->getMapData('user', $data['create_user']) ?: [],
                'create_time' => $data['create_time'] ?? '',
                'update_time' => $data['update_time'] ?? '',
                'update_user' => $this->getMapData('user', $data['update_user']) ?: null,
                'seller_account_info' => $this->getMapData('seller_account_map', $data['seller_account_id']) ?: [],
                'ali_order_id' => $data['ali_order_id'] ?? 0,
                'archive_type' => $data['archive_type'],
                'store_info' => $this->getMapData('store_map', $data['ali_store_id']) ?: [],
                'last_sync_time' => $data['last_sync_time'] == '1970-01-01 00:00:01' ? '': $data['last_sync_time'],
            ];
         }

        if ($this->showTransport) {
            $result['transport_info'] = $this->getMapData('transportMap', $data['order_id']) ?: [];
        }


        if ($this->showStoreInfo) {

            $result['store_info'] = $this->getMapData('store_map', $data['ali_store_id']) ?: [];
        }

        if (in_array('amount_usd', $this->specifyFields)) {
            $result['amount_usd'] = isset($result['amount_usd'])?round($result['amount_usd'],2):0;
        }

        if ($this->showStatusAction)
        {
            $statusActions=[];
            if($data['source_type'] == Order::TYPE_ALI_ORDER || $data['source_type'] == Order::TYPE_DIRECT_PAY_ORDER)
            {
                $statusActions =  AlibabaOrderSyncHelper::formatStatusAction($data['ali_order_id'],json_decode($data['status_action'],true)??[]);
            }

            $result['status_action'] = array_values($statusActions);
        }

        if ($this->showAssocPlatformProduct) {
            $result['assoc_platform_product'] = false;
            foreach ($data['product_list'] ?? [] as $item) {
                if (!empty($item['platform_product_info']['platform_product_id'])) {
                    $result['assoc_platform_product'] = true;
                    break;
                }
            }
        }

        if ($this->showAlibabaStatusInfo)
        {
            $aliStatusInfo =[];
            if($data['source_type'] == Order::TYPE_ALI_ORDER || $data['source_type'] == Order::TYPE_DIRECT_PAY_ORDER)
            {
                $aliStatusInfo = [
                    'ali_status_id' => $data['ali_status_id'],
                    'status_name' => \Yii::t('invoice', $data['ali_status_name'])
                ];
            }
            $result['ali_status_info'] = $aliStatusInfo;
        }


        if($this->showAlibabaOrderInfo){
            $result['alibaba_order_info'] = $this->getMapData('alibaba_order_map', $data['ali_order_id']) ?: [];
        }

        if ($this->showAlibabaAccountInfo) {
            $result['seller_account_info'] = $this->getMapData('seller_account_map', $data['seller_account_id']) ?: [];

        }
        if (isset($result['amount_rmb']) && in_array('amount_rmb', $this->specifyFields)) {
            $result['amount_rmb'] = round($result['amount_rmb'],2);
        }

        if (isset($result['amount_usd']) && in_array('amount_usd', $this->specifyFields)) {
            $result['amount_usd'] = round($result['amount_usd'],2);
        }


        if ($this->showLeadInfo) {
            $result['lead_info'] = $this->getMapData('leadInfoMap', $data['source_lead_id']) ?: [];
        }

        if ($this->showPurchaseOrder)
        {
            $result['purchase_order_list'] = $this->getMapData('purchaseOrderMap', $data['order_id'])??[];
        }

        if (($this->showExternalFieldDetail && in_array('external_field_data', $this->specifyFields)) || $this->showApiQuoteField) {
            $externalFieldWithValue = [];
            $quoteFieldRow = $this->showQuoteField ? $this->getMapData("quoteFieldsRows",$data['order_id']) : [];
            foreach ($fields as $field) {
                if (
                    array_key_exists($field['id'], $data['external_field_data']) ||
                    ($field['field_type'] == CustomFieldService::FIELD_TYPE_QUOTE_FIELDS && !empty($quoteFieldRow[$field['id']]))
                ) {
                    if($field['field_type'] == CustomFieldService::FIELD_TYPE_QUOTE_FIELDS && $quoteFieldRow){
                        $value = $quoteFieldRow[$field['id']] ?? '';
                    }else{
                        $value = !empty($data['external_field_data'][$field['id']]) ? $data['external_field_data'][$field['id']] : '';
                    }

                    $externalFieldValue = array_merge(\ArrayUtil::columns([
                        'id',
                        'name',
                        'disable_flag',
                        'field_type',
                        'relation_field_type',
                        'relation_origin_field_type',
                        'hint',
                        'is_editable',
                        'require',
                        'type',
                    ], $field), [
                        'value' => $value,
                    ]);

                    //处理自定义字段图片和附件列表
                    if ($field['field_type'] == CustomFieldService::FIELD_TYPE_IMAGE|| $field['field_type'] == CustomFieldService::FIELD_TYPE_ATTACH
                    ) {
                        if (!empty($externalFieldValue['value'])) {
                            $fileIds = array_column($externalFieldValue['value'], 'file_id');
                            foreach ($fileIds as $fileId) {
                                $fileInfo[$fileId] = $this->getMapData('fileInfoMap', $fileId);
                            }
                        }

                        $externalFieldValue['file_info'] = $fileInfo ?? [];
                        $externalFieldValue['value'] =array_values($fileInfo ?? []);
                        unset($fileInfo);
                    }

                    // 处理自定义关联字段
                    if($field['field_type'] == CustomFieldService::FIELD_TYPE_FIELDS){
                        $this->formatRelationField($externalFieldValue, $field);
                    }
                    $externalFieldWithValue[] = $externalFieldValue;
                }
            }

            $result['external_field_data'] = $externalFieldWithValue;
        }

        if ($this->showAmountInfo) {
            foreach ($data['cost_list']  as &$cost) {
                $cost['cost'] = is_numeric($cost['cost'] ?? 0) ? ($cost['cost']??0) : 0;
                $cost['cost'] = number_format($cost['cost'] ?? 0, 2, '.', '');
                if (isset($cost['cost_item_relation_id']) && !empty($cost['cost_item_relation_id'])) {
                    $costItemRelationInfo = $this->getMapData('cost_item_relation_map', $cost['cost_item_relation_id']) ?? [];
                    $cost['cost_name'] = $costItemRelationInfo['item_name'] ?? '';

//                    if (isset($cost['cost_remark']) && !empty($cost['cost_remark'])) {
//                        $cost['cost_name'] .= "({$cost['cost_remark']})";
//                    }
                }
            }
            unset($cost);
            $result['addition_cost_info'] = [
                'addition_cost_amount' => $data['addition_cost_amount'] ?? 0,
                'addition_cost_list' => $data['cost_list'] ?? []
            ];
        }

        if ($this->showArchiveInfo) {
            $result['archive_info'] = [
                'archive_type' => $data['archive_type'],
                'archive_name' => Helper::getOrderArchiveTypeMaps()[$data['archive_type']]['name']
            ];
        }

        if ($this->showProductList && $this->showInfoGroup) {
            $result['count'] = count($data['product_list']);
            $result['product_list'] = $this->formatOrderProductListData($result['data']);
        }

        if ($this->showInvoiceProductCount) {
            $result['invoice_product_count'] = count($data['product_list']);
        }

        if ($this->showOrderDetailInfo) {
            $result['detail_info'] = $this->formatOrderDetailInfos($result);
        }

        if  ($this->showApprovalStatusInfo) {
            $lastApprovalInfo = $result['last_approval_info']  ??  \common\library\approval_flow\Helper::lastApproval($data['order_id'],$result['approval_flow_info']);
            if (!is_null($lastApprovalInfo)) {
                // 注意点：last_approval_info参数 -- 销售订单的apply_form_id,这里做了判断，在报价单、商机中是没有这个逻辑的，特殊处理化了，app、web都会用到改参数
                $result['last_approval_info'] = [
                    'status' => $lastApprovalInfo['status'] ?? 0,
                    'approval_type' => $lastApprovalInfo['approval_type'] ?? 0,
                    'apply_form_id' => ($lastApprovalInfo['approval_type'] ?? \common\library\approval_flow\Constants::APPROVAL_TYPE_APPLY) == \common\library\approval_flow\Constants::APPROVAL_TYPE_APPLY ? ($lastApprovalInfo['apply_form_id'] ?? 0) : $lastApprovalInfo['id'] ?? 0,
                    'id' => $lastApprovalInfo['id'] ?? 0,
                ];
            } else {
                $result['last_approval_info'] = [
                    'status' => 0,
                    'approval_type' => 0,
                    'apply_form_id' => 0,
                    'id' => 0,
                ];
            }
        }

        if ($this->showApprovalDiffTip) {
            $tips = \common\library\approval_flow\Helper::getApprovalDiffTip($data['order_id'], false);
            if (!empty($tips)) {
                $result['approval_diff_tip'] = $tips;
            } else {
                $result['approval_diff_tip'] = [];
            }
        }

        if ($this->showHasReferDownStreamInvoice) {
            $result['has_refer_down_stream_invoice'] = $this->getMapData('has_refer_down_stream_invoice_map', $data['order_id']);
        }

        if ($this->showCanChangeCurrency) {
            $result['can_change_currency'] = Helper::canChangeCurrency($this->clientId, $data['order_id']);
        }

        if ($this->showCashCollectionInfo) {
            $result['cash_collection_info'] = $this->getMapData('cashCollectionInfoMap', $data['order_id'])?? [];
            $result['cash_collection_summary'] = $this->mapData['cashCollectionSummary'] ?? [];
        }

        if ($this->showCostInvoiceInfo) {
            $result['cost_invoice_info'] = $this->getMapData('costInvoiceInfoMap', $data['order_id']) ?? [];
            $result['cost_invoice_summary'] = $this->mapData['costInvoiceSummary'] ?? [];
        }

        if ($this->showOutboundRecordInfo) {
            //获取销售订单产品数量
            $productListMap = array_column($productList,null,'sku_id');
            $outboundRecord = $this->getMapData('outboundRecordMap', $data['order_id']) ?? [];
            foreach ($outboundRecord as &$recordItem){
                $productSku = $this->getMapData('product_sku', $recordItem['sku_id']) ?? [];
                $recordItem['name'] = $productSku['name'] ?? '';
                $recordItem['cn_name'] = $productSku['cn_name'] ?? '';
                $recordItem['unit'] = $productSku['unit'] ?? '';
                $recordItem['product_no'] = $productSku['product_no'] ?? '';
                $recordItem['product_id'] = $productSku['product_id'] ?? '';
                $recordItem['model'] = $productSku['model'] ?? '';
                $recordItem['image_info'] = $productSku['image_info'] ?? [];
                $recordItem['sku_code'] = $productSku['sku_code'] ?? [];
                $recordItem['count'] = $recordItem['outbound_count'] ?? 0;
                $recordItem['attributes_info'] = $productListMap[$recordItem['sku_id']]['attributes_info'] ?? [];
            }
            $result['outbound_record_info'] = $outboundRecord;
            $result['outbound_record_summary'] = $this->mapData['outboundRecordSummary'] ?? [];
        }

        if ($this->showPaymentInvoiceInfo) {
            $result['payment_invoice_info'] = $this->getMapData('paymentInvoiceMap', $data['order_id'])?? [];
            $result['payment_invoice_summary'] = $this->mapData['paymentInvoiceSummary'] ?? [];

        }

        if ($this->showOperatePrivilege){
            $handlers = array_column($result['handler_info'],'user_id');
            $result['operate_privilege'] = $this->getOperatePrivilege($result['lock_flag'],$handlers);
        }
        if (!empty($this->showOperateButtonAccess)){
            $result['operate_access'] = $this->getOperateAccess($this->showOperateButtonAccess,$result);
        }

        //详情页 展示环节进度
        if($this->showLinkInfo) {
            $statusName = $result['status_info']['name'] ?? '';
            $result['link_info'] = $this->buildLink($result['link_status'],$result['order_id'],$statusName);
        }

        //展示订单任务进度
        if($this->showProgressInfo) {
            $cashCollection = $this->getMapData('cash_collection_info', $data['order_id']);
            $purchase = $this->getMapData('purchase_order_count_map', $data['order_id']);
            $outbound = $this->getMapData('outbound_count_map', $data['order_id']);
            $shippingCount = $this->getMapData('shipping_invoice_count_map', $data['order_id'])['have_shipping_count'] ?? 0;
            $orderProductTotalCount = $this->getMapData('order_product_total_count_map', $data['order_id']);
            $fromStockCount = $outbound && isset($outbound['from_stock_count']) ? round($outbound['from_stock_count'],6) : 0;
            $needPurchaseCount = !empty($orderProductTotalCount) && ($orderProductTotalCount-$fromStockCount > 0) ? (round($orderProductTotalCount,6) - $fromStockCount) : 0;       // 负数的情况下也为0
            $purchaseProgress = $inboundProgress = 0;
            if($orderProductTotalCount > 0){
                $purchaseProgress = $needPurchaseCount ==0 ? 100 : ($purchase && !empty($purchase['purchase_count']) ? round($purchase['purchase_count'] / $needPurchaseCount , 2) * 100 : 0);
                $inboundProgress = $needPurchaseCount ==0 ? 100 : ($purchase &&  !empty($purchase['inbound_count']) ? round($purchase['inbound_count'] / $needPurchaseCount , 2) * 100 : 0);
            }
            $result['progress_info'] = [
                'cash_collection_progress' => $cashCollection && isset($cashCollection['percentage']) ? $cashCollection['percentage'] : 0,
                'purchase_count' => $purchase && isset($purchase['purchase_count']) ? round($purchase['purchase_count'],6) : 0,
                'inbound_count' => $purchase && isset($purchase['inbound_count']) ? round($purchase['inbound_count'],6) : 0,
                'product_total_count' => !empty($orderProductTotalCount) ? round($orderProductTotalCount,6) : 0,
                'outbound_count' => $outbound && isset($outbound['outbound_count']) ? round($outbound['outbound_count'],6) : 0,
                'purchase_progress' => $purchaseProgress,
                'inbound_progress' => $inboundProgress,
                'outbound_progress' => $outbound && !empty($outbound['outbound_count']) && !empty($orderProductTotalCount) ? round($outbound['outbound_count'] / $orderProductTotalCount , 2) * 100 : 0,
                'from_stock_count' => $fromStockCount,
                'need_purchase_count' => $needPurchaseCount,
                'shipping_count' => round($shippingCount, 6),
                'shipping_progress' => $orderProductTotalCount == 0 ? 0 : round($shippingCount / $orderProductTotalCount, 2) * 100
            ];
        }

        // 下推任务创建页 展示关联的所有任务
        if($this->showTransferInfo){
            $result['downstream_transfer_info'] = [
                'purchase_transfer' => $this->getMapData('purchase_transfer_map', $data['order_id']),
                'outbound_transfer' => $this->getMapData('outbound_transfer_map', $data['order_id']),
            ];
        }

        //列表页展示环节状态字段
        if(!empty($this->showLink)) {
            $result += $this->buildLinkStatus($result['link_status'],$this->showLink);
        }

        //展示资金账号信息
        if ($this->showCapitalAccountInfo) {
            $capitalAccountInfo = $this->getMapData('capital_account_map', $data['capital_account_id']??0);

            $result['capital_account_name'] = $capitalAccountInfo['name'] ?? '';
            $result['capital_name'] = $capitalAccountInfo['capital_name'] ?? '';
            $result['capital_bank'] = $capitalAccountInfo['capital_bank'] ?? '';
            $result['bank_account'] = $capitalAccountInfo['bank_account'] ?? '';
            $result['capital_account_address'] = $capitalAccountInfo['address'] ?? '';
            $result['capital_account_remark'] = $capitalAccountInfo['remark'] ?? '';
        }

        //销售订单产品关联下游数量
        if ($this->showReferInvoiceProductCount) {
            $referInvoiceProductCount = Helper::hasReferDownStreamInvoiceByOrderProduct($this->userId, $this->clientId, $orderId);
            $result['refer_invoice_product_count'] = $referInvoiceProductCount ?: new \stdClass();
        }

        // 引用字段格式化
//        if($this->showQuoteField){
//            $result['quote_field_data'] = $this->getMapData('quoteFieldsRows', $data['order_id']) ?: [];
//        }

        if ($this->showErpStatusInfo) {
            $result['erp_status_info'] = $this->getMapData('erp_status_info_map', $data['order_id']) ?? null;
        }
        //展示费用项 订单附加费用项」及「费用单费用项」
        //不需要format信息
        if ($this->showCostItems) {
            $relationMap = $this->mapData['cost_item_relation_map'];
            $costList = $result['cost_list'] ?? [];
            foreach ($costList as $costItem) {
                if (isset($costItem['cost_item_relation_id']) && !empty($relationMap[$costItem['cost_item_relation_id']] ?? [])) {
                    $item_id = $relationMap[$costItem['cost_item_relation_id']]['item_id'] ?? 0;
                    $result[$item_id] = round((($costItem['cost'] ?? 0) * ($data['exchange_rate'] ?? 0) / 100 + ($result[$item_id] ?? 0)), 4);
                }
            }
            $costInvoiceList = $result['cost_invoice_info'] ?? [];
            foreach ($costInvoiceList as $cost_invoice_item) {
                $result[$cost_invoice_item['cost_item_id']] = round((($cost_invoice_item['amount_rmb'] ?? 0) + ($result[$cost_invoice_item['cost_item_id']] ?? 0)), 4);

            }
        }

        if ($this->showShippingCostList) {
            $relationMap = $this->mapData['shipping_invoice_cost_map'][$data['order_id']] ?? [];
            $shipping_cost_map = [];
            foreach ($result['cost_list'] as $cost) {
                if (empty($cost)) {
                    continue;
                }
                if (isset($cost['cost_item_relation_id']) && !empty($cost['cost_item_relation_id'])) {
                    $costItemRelationInfo = $this->getMapData('cost_item_relation_map', $cost['cost_item_relation_id']) ?? [];
                    $cost['cost_name'] = $costItemRelationInfo['item_name'] ?? '';
                }
                $key = isset($cost['cost_item_relation_id']) && !empty($cost['cost_item_relation_id'])
                    ? $cost['cost_item_relation_id']
                    : $cost['cost_name'];
                $amount = floatval($cost['cost'] ?? 0);
                if (!isset($shipping_cost_map[$key])) {
                    $orderCost = $amount;
                }else{
                    $orderCost = floatval($shipping_cost_map[$key]['cost']) + $amount;
                }
                $cost['order_id'] = $data['order_id'];
                //如果不存在 可继承金额为cost
                $cost['inheritable_cost'] = round((($orderCost) - round(floatval(($relationMap[$key]['cost'] ?? 0)), 4)), 4);
                $cost['inheritable_percentage'] = $orderCost != 0 ? round(abs($cost['inheritable_cost'] / $orderCost), 2) : 0;
                $cost['cost'] = $orderCost;
                $shipping_cost_map[$key] = $cost;
            }
            $result['shipping_cost_list']= array_values($shipping_cost_map);
        }

        if($this->showCountryName){
            $result['country'] = $this->getMapData('country', $data['country']) ?? '';
        }

        return $result;
    }

    protected function formatRelationField(&$externalField, $fieldInfo){

        $value = $externalField['value'] ?? '';
        if(!$value){
            return ;
        }
        //处理自定义字段图片和附件列表
        if ($fieldInfo['relation_field_type'] == CustomFieldService::FIELD_TYPE_IMAGE
            || $fieldInfo['relation_field_type'] == CustomFieldService::FIELD_TYPE_ATTACH
        ) {
            if (!empty($value)) {
                $fileIds = array_column($value, 'file_id');
                foreach ($fileIds as $fileId) {
                    $fileInfo[$fileId] = $this->getMapData('fileInfoMap', $fileId);
                }
            }
            $externalField['value'] =array_values($fileInfo ?? []);
        }

        switch($fieldInfo['relation_field']){
           
            case "origin_list":
        
                $mapKey = 'company_origin_list';
        
                $originName = [];
        
                $value = (array)$value;
        
                foreach ($value as $origin) {
            
                    if (!$this->hasMapData($mapKey, $origin)) {
                
                        $this->mapData[$mapKey] = \CustomerOptionService::getOriginNameMap($this->clientId);
                    }
            
                    $originName[] = $this->mapData[$mapKey][$origin] ?? '';
                }
        
                $externalField['value'] = array_values(array_filter($originName));
        
                break;
            case "origin":  // 客户来源
                $mapKey = 'company_origin';
                $value = (int)(((array)$value)[0] ?? 0);
                if(!$this->hasMapData($mapKey, $value)){
                    $this->mapData[$mapKey] = \CustomerOptionService::getOriginNameMap($this->clientId);
                }
                $externalField['value'] = $this->getMapData($mapKey, $value);
            break;
            case "category_ids":    // 主营产品
                $mapKey = 'company_category';
//                if(!isset($this->mapData[$mapKey])){
//                    // 获取用户使用的语言
//                    $user = \User::getUserObject($this->userId);
//                    $language = str_starts_with($user->getLanguage(), 'zh') ? 'zh' :'en';
//                    $this->mapData[$mapKey] = \Category::getNameMap($language);
//                }
                $result = "";
                foreach($value as $categoryPath){
                    $formatCategory = [];
                    foreach($categoryPath as $category){
                        $formatCategory[] = $this->getMapData($mapKey, $category);
                    }
                    $formatCategory = array_filter($formatCategory);
                    if($formatCategory){
                        $result .= implode('-', $formatCategory) . "\n";
                    }
                }
                $result = rtrim($result, "\n");
                $externalField['value'] = $result;
            break;
            case "timezone":    // 时区
                $externalField['value'] = \CustomerOptionService::TIMEZONE_MAP[$value] ?? '-';
            break;
            case "scale_id":    // 规模
                $scale = \CustomerOptionService::SCALE_MAP[$value] ?? [];
                $externalField['value'] = empty($scale) ? '-' : $scale['min'] .'-'.$scale['max'];
            break;
            case "annual_procurement":    // 年采购额
                $starMap = \CustomerOptionService::annualProcurementMap();
                $externalField['value'] = $starMap[$value] ?? '-';
            break;
            case "intention_level":    // 采购意向
                $starMap = \CustomerOptionService::intentionLevelMap();
                $externalField['value'] = $starMap[$value] ?? '-';
            break;
            case "product_group_ids":    // 产品分组
                if (!empty($value)) {
                    $externalField['value'] = \common\library\group\Helper::formatProductGroupName($this->clientId, $value, \Constants::TYPE_PRODUCT) ?: '-';
                }
                break;
            case "star":    // 星级
                $starMap = \CustomerOptionService::starMap();
                $externalField['value'] = $starMap[$value] ?? '-';
            break;
            case "trail_status":    // 客户状态
                $mapKey = 'company_trail_status';
                if(!$this->hasMapData($mapKey, $value)){
                    $this->mapData[$mapKey] = array_column(\CustomerOptionService::getCustomerStatusList($this->clientId), "status_name", "status_id");
                }
                $externalField['value'] = $this->getMapData($mapKey, $value);
            break;
            case "tel":     // 座机
                if (isset($value['tel_area_code']) || isset($value['tel'])){
                    $externalField['value'] = ($value['tel_area_code'] ?? '') . '-' . ($value['tel'] ?? '');
                }
            break;
        }
    }

    protected function buildInfoGroupFormatFiled($data){

        $map = [];

        if( empty($this->showGroupIds) || in_array( CustomFieldService::ORDER_GROUP_BASIC,$this->showGroupIds)){

            $departments = $data['departments'];
            foreach ($departments as $key => &$departmentEnum) {
                $department = $this->getMapData('department', $departmentEnum['department_id']);
                if (!is_null($department)) {
                    $departmentEnum['department_id'] = strval($department['id']);
                    $departmentEnum['name'] = $department['name'] ?? '';
                } else {
                    unset($departments[$key]);
                }
            }

            $map['departments'] = $departments;

            $users = $data['users'];
            foreach ( $users as &$elem ){
                $user = $this->getMapData('user',$elem['user_id']);
                $elem['user_id'] = strval($elem['user_id']);
                $elem['nickname'] = $user['nickname']??'';
                $elem['avatar'] = $user['avatar']??'';
            }
            $map['users'] =  $users;

            $handler = [];
            $data['handler'] = empty($data['handler'])?[]:$data['handler'];
            foreach ($data['handler'] as $userId){
                $handler[] = $this->getMapData('user',$userId);
            }
            $map['handler'] = $handler;

            $map['status'] =   $this->getMapData('status',$data['status']??0)??'';

            $map['company_id'] =  $this->getMapData('company',$data['company_id']??0);

            $map['opportunity_id'] =  $this->getMapData('opportunity',$data['opportunity_id']??0);

            $map['customer_id'] = $this->getMapData('customer',$data['customer_id']??0);

            $map['create_user'] = $this->getMapData('user',$data['create_user']);

            //银行信息格式化
            $map['capital_account_id'] = $this->getMapData('capital_account_map', $data['capital_account_id']) ?? [];
            $map['capital_name'] = $this->getMapData('capital_account_map',$data['capital_account_id'])['capital_name']??'';
            $map['capital_bank'] = $this->getMapData('capital_account_map',$data['capital_account_id'])['capital_bank']??'';
            $map['bank_account'] = $this->getMapData('capital_account_map',$data['capital_account_id'])['bank_account']??'';
            $map['capital_account_address'] = $this->getMapData('capital_account_map',$data['capital_account_id'])['address']??'';
            $map['capital_account_remark'] = $this->getMapData('capital_account_map',$data['capital_account_id'])['remark']??'';

        }
        $sourceType = $data['source_type'];
        if(in_array( CustomFieldService::ORDER_GROUP_PRODUCT, $this->showGroupIds))
        {
            $map['sku_id'] = [];
            $map['product_name'] = [];
            foreach ($data['product_list'] as $product)
            {
                $productId = $product['product_id'] ?? 0;
                $skuId = $product['sku_id'] ?? 0;
//                $platformSkuId = $product['platform_product_info']['platform_sku_id'] ?? 0;
                $platformSkuId = $product['platform_sku_id'] ?? $product['platform_product_info']['platform_sku_id'] ?? 0;
                $platformProductId = $product['platform_product_info']['platform_product_id'] ?? 0;
                $productInfo = $this->getMapData('product_map', $productId) ?? [];
                $productSku = $this->getMapData('product_sku', $skuId);
                $platformProductSku = $this->getMapData('platform_product_sku', $platformSkuId);
                $platformProduct = $this->getMapData('platform_product', $platformProductId);
                $key = "{$data['order_id']}_{$platformProductId}_{$platformSkuId}";
                $freeze = $this->getMapData('freezeProductMap', $key);

                //统一产品属性的格式
                if ($platformProductId) {
                    $attr = [];
                    $skuAttributes = empty($product['sku_attributes']) ? [] : $product['sku_attributes'];
                    foreach ($skuAttributes as $attribute => $value) {
                        $attr[] = [
                            'item_name' => $attribute,
                            'value' => ['item_name' => $value],
                        ];
                    }
                    $platformProductSku['attributes_info'] = $attr;
                }

                $uniqueId = $product['unique_id'] ?? 0;
                $skuKey = $uniqueId . '_' . $skuId;
                $productKey = $uniqueId . '_' . $productId;
                //规格
                $map['sku_id'][$skuKey] = [
                    'product_type' => $platformProductSku['product_type'] ?? $productSku['product_type'] ?? ProductConstant::PRODUCT_TYPE_SPU,
                    'attributes_info' => $platformProductSku['attributes_info'] ?? $productSku['attributes_info'] ?? [],
                    'sku_code' => $platformProductSku['third_sku_code'] ?? $productSku['sku_code'] ?? '',
                    'product_id' => $platformProductSku['platform_product_id'] ?? $productSku['product_id'] ?? '',
                ];

                //产品
                $map['product_id'][$productKey] = [
                    'fob' => $productInfo['fob'] ?? [],
                ];

                if (!empty($productSku) && ($productSku['product_enable_flag'] ?? \Constants::ENABLE_FLAG_TRUE) == \Constants::ENABLE_FLAG_FALSE)
                {
                    $map['product_name'][$skuKey] = \Yii::t('common', 'Deleted') . '_' . ($productSku['name'] ?? '');
                }

                $productNo = '';
                if (!empty($productSku['product_type'])) {
                    $productNo = $productSku['product_type'] == ProductConstant::PRODUCT_TYPE_SPU ? ($productSku['product_no'] ?? '') : ($productSku['sku_code'] ?? '');
                }
                $map['product_no'][$skuKey] = $platformProduct['third_product_id'] ?? $productNo;

                $map['platform_product_info'][$skuKey] = [
                    'from_url' => $platformProduct['from_url'] ?? '',
                    'is_platform_product' => !empty($product['platform_product_info']['platform_product_id']),
                    'is_match_local_product' => !empty($product['platform_product_info']['platform_product_id']) && $skuId > 0,
                    'platform_match_flag' => !empty($platformProductSku['is_match']),
                    'no_attr_sku' => empty($platformSkuId), // 没选规格的多规则产品
                    'platform_sku_id' => $platformSkuId,
                    'platform_product_id' => $product['platform_product_info']['platform_product_id'] ?? 0,
                    'spu_match_info' => $platformProductSku['spu_match_info'] ?? [],
                    'is_relate_invoice' => boolval($freeze),
                ];

                $skuCode = '';
                $productId = 0;
                if (!empty($product['platform_product_info']['platform_product_id']) && !empty($productSku['product_type'])) {
                    if ($productSku['product_type'] == ProductConstant::PRODUCT_TYPE_SPU) {
                        $skuCode = $productSku['product_no'] ?? '';
                    } else {
                        $skuCode = $productSku['sku_code'] ?? '';
                    }
                    $productId = $productSku['product_id'] ?? 0;
                }

                $map['local_product_no'][$skuKey] = [
                    'sku_code' => $skuCode,
                    'product_id' => $productId,
                ];
            }
        }

        if (in_array(CustomFieldService::ORDER_GROUP_FEE, $this->showGroupIds)) {
            foreach ($data['cost_list'] as $costItem)
            {
                if (isset($costItem['cost_item_relation_id']) && !empty($costItem['cost_item_relation_id'])) {
                    $map['cost_item_relation_id'][$costItem['cost_item_relation_id']] = $this->getMapData('cost_item_relation_map', $costItem['cost_item_relation_id']) ?? [];
                }
            }
        }
        return $map;
    }

    protected function buildExportMapData()
    {
        //tips 导出的交易产品是用tbl_order中的product_list，如果作废product_list 用tbl_invoice_product_record，记得做逻辑处理，避免导出数据出错
        $data = $this->data;

        $companyId = $data['company_id'];
        $customerId = $data['customer_id'];
        $costList = $data['cost_list'];
        $createUserId = $data['create_user'];
        $capitalAccountId = $data['capital_account_id'];

        $costItemInfo = [];
        $exchangeProductList = [];
        $additionFeeList = [];
        $productPageList = [];

        $exportFormatter = new OrderExportFormatter($this->clientId, $this->userId);
        $exportFormatter->setNeedStrip(false);
        $exportFormatter->showQuoteFieldFlag(true);
        $exportFormatter->setData($data);
        $data = $exportFormatter->result();

        //关联客户
        $company = [];
        if( $companyId > 0 ){
            $company = new Company($this->clientId, $companyId);
            $company = $company->isExist() ? $company->getAttributes() : [];

            if($company){
                $company['origin_list'] = (\CustomerOptionService::getOriginName($this->clientId, $company['origin_list'] ?? [])) ?? '';
                $company['group_id'] = \common\library\group\Helper::getGroupName(Constants::TYPE_COMPANY,$company['group_id']);
                $company['trail_status'] = (\CustomerOptionService::getStatusMap($this->clientId,[$company['trail_status']]))[$company['trail_status']]??'';
                $company['cus_tag'] = isset($company['tag'][$this->userId]) ? \CustomerOptionService::getTagName($this->clientId, $this->userId, \common\library\setting\library\tag\Tag::TYPE_COMPANY, $company['tag'][$this->userId]) : '';
            }
        }

        //关联客户联系人
        $customer = [];
        if( $customerId > 0 ){
            $customer = new Customer($this->clientId, $customerId);
            $customer = $customer->isExist() ? $customer->getAttributes() : [];
            if($customer){
                $customer['gender'] = $customer['gender']==Customer::GENDER_TYPE_MAN ? '男' :($customer['gender'] == Customer::GENDER_TYPE_WOMAN?'女':'未知');
                $customer['post_grade'] = Customer::POST_GRADE_NAME[$customer['post_grade']]??'';
            }

        }

        //交易产品
        if( isset($data['product_list']) && !empty($data['product_list']) ){
            $skuIds = array_column($data['product_list'], 'sku_id');
            $skuApi = new SkuAPI($this->clientId, $this->userId);
            $productSkuMap = array_column($skuApi->items($skuIds), null, 'sku_id');

            $productIds = array_column($data['product_list'],'product_id');
            $productPageResult = ClientProduct::findAllByProductIds($this->clientId,$productIds);

            $productPageMap = [];
            $productGroupNameMap = [];
            if( !empty($productPageResult) ){
                $productPageMap = array_combine(array_column($productPageResult,'product_id'),$productPageResult);
                $productGroupIds = array_column($productPageResult,'group_id');
                $productGroupNameMap = \common\library\group\Helper::getGroupNameMap($this->clientId,Constants::TYPE_PRODUCT,$productGroupIds);
            }

            $productTotal = [
                'parts_total_amount' => 0,//产品金额（全部主配）
                'parts_total_all_gross_profit' => 0,//产品毛利（全部主配）
                'parts_total_gross_profit' => 0,//产品毛利（单个）
                'parts_total_cost' => 0,//产品成本（单个）
                'parts_total_unit_price' => 0,//产品单价（单个）
            ];

            //获取主产品、普通产品的id
            $productIds = $productCountMap = [];
            foreach ($data['product_list'] as $product ) {
                if (empty($product['master_id'])) {//主产品、普通产品 的master_id 就是为空
                    $productIds[]=$product['unique_id'];
                    $productCountMap[$product['unique_id']]=floatval($product['count'] ?? 0);
                }
            }
            $productTotalMap = array_combine($productIds, array_fill(0, count($productIds), $productTotal));

            //统计全部主配的信息
            foreach ( $data['product_list'] as $product ){
                if (!empty($productTotalMap[$product['unique_id'] ?? 0]) ) {
                    $productTotalMap[$product['unique_id']]['parts_total_amount'] += (floatval($product['cost_amount'] ?? 0) - floatval($product['other_cost'] ?? 0));
                    $productTotalMap[$product['unique_id']]['parts_total_all_gross_profit'] += floatval($product['gross_margin'] ?? 0) * floatval($product['count'] ?? 0);
                }
                if (!empty($productTotalMap[$product['master_id'] ?? 0])) {
                    $productTotalMap[$product['master_id']]['parts_total_amount'] += (floatval($product['cost_amount'] ?? 0) - floatval($product['other_cost'] ?? 0));
                    $productTotalMap[$product['master_id']]['parts_total_all_gross_profit'] += floatval($product['gross_margin'] ?? 0) * floatval($product['count'] ?? 0);
                }
            }

            foreach ( $data['product_list'] as $product ){
                //产品规格信息
                if (empty($product['platform_product_info']) || !empty($product['sku_id'])) {
                    // 本地产品
                    $skuStr = [];
                    $skuAttributesValue = [];
                    if (!empty($skuAttrInfo = $productSkuMap[$product['sku_id'] ?? 0]['attributes_info'] ?? [])) {
                        foreach ($skuAttrInfo as $attr) {
                            $skuStr[] = ($attr['item_name'] ?? '').': '.($attr['value']['item_name'] ?? '');
                            $skuAttributesValue[] = ($attr['value']['item_name'] ?? '');
                        }
                    }
                    $skuStr = implode(";\n", $skuStr);
                    $sku_attributes_value_str = implode(";\n", $skuAttributesValue);
                } else {
                    // 平台产品
                    $skuStr = $product['sku_attributes'] ?? '';
                    $sku_attributes_value_str = $product['sku_attributes'] ?? '';
                    if (!empty($product['platform_product_info']['sku_attributes'])) {
                        $sku_attributes_value_str = implode(';', array_values($product['platform_product_info']['sku_attributes']));
                    }
                }
                $product['sku_id'] = $skuStr;
                $product['sku_attributes_value'] = $sku_attributes_value_str;

                //特殊处理交易产品毛利率
                $product['gross_profit_margin_1'] = $product['gross_profit_margin']??0;
                $product['gross_profit_margin_2'] = isset($product['gross_profit_margin'])? trim($product['gross_profit_margin'],'%'):'';
                unset($product['gross_profit_margin']);

                //配件产品计算配比
                if (!empty($product['master_id']) && !empty($productCountMap[$product['master_id']] ?? 0)) {
                    $product['ratio'] = round(($product['count'] ?? 0) / $productCountMap[$product['master_id']], 4);
                }

                //主产品,处理主配小计金额
                $productTotalData = $productTotalMap[$product['unique_id']] ?? [];
                if (!empty($productTotalData)) {
                    $productCount = floatval($product['count'] ?? 0);
                    $productTotalData['parts_total_gross_profit'] = round(empty($productCount) ? 0 : ($productTotalData['parts_total_all_gross_profit'] / $productCount), 4);
                    $productTotalData['parts_total_unit_price']=round(empty($productCount) ? 0 : ($productTotalData['parts_total_amount'] / $productCount), 4);
                    $productTotalData['parts_total_cost'] = round($productTotalData['parts_total_unit_price'] - $productTotalData['parts_total_gross_profit'], 4);
                }
                $product = array_merge($product, $productTotalData);

                $exchangeProductList[] = $product;

                $productPage = $productPageMap[$product['product_id']]??null;
                if( $productPage ){
                    $productPage =  $productPage->getAttributes();

                    //特殊处理产品图片
                    if(isset($productPage['images'])){
                        $productPage['images'] = json_decode($productPage['images'], true);
                        for($imgNo = 0; $imgNo <= 6; $imgNo++){
                            $imgIndexNo = $imgNo+1;
                            $imgIndex = "images_{$imgIndexNo}";

                            $productPage[$imgIndex] = $productPage['images'][$imgNo]['src'] ?? '';

                            $imageId = $productPage['images'][$imgNo]['id'] ?? 0;
                            if($imageId > 0){//获取内网url
                                $aliUpload = new \AliyunUpload();
                                if($aliUpload->loadByFileId($imageId)){
                                    $productPage[$imgIndex] = $aliUpload->getFileUrl(true);
                                }
                            }
                        }
                    }
                    unset($productPage['images']);

                    //产品属性字段
                    $categoryIds =PgsqlUtil::trimArray($productPage['category_ids']);
                    $categoryId = end($categoryIds);

                    $categoryAttr = new AlibabaCategoryAttr($categoryId);
                    $productPage['info_json'] = $categoryAttr->formatFieldInfoJsonForCategoryId($productPage['info_json']??[],$categoryId);
                    $productPage['info_json'] = $categoryAttr->formatFieldInfoJson($productPage['info_json']);
                    //产品分组
                    if(isset($productPage['group_id'])) {
                        $productPage['group_id'] = $productGroupNameMap[$productPage['group_id']]??'';
                    } else {
                        $productPage['group_id'] = '';
                    }
                    //自定义字段
                    $productPage['external_field_data'] = json_decode($productPage['external_field_data'], true);

                    $productPage['package_count'] = 0;
                    $productPage['category_ids'] = PgsqlUtil::trimArray($productPage['category_ids']);
                    if (!empty($product['count']) && !empty($product['count_per_package'])) {
                        $productPage['package_count'] = ($product['count_per_package'] != 0) ? ceil($product['count'] / $product['count_per_package']) : 0;
                    }
                    $productPage['sku_id'] = $skuStr;
                }

                $productPageList[] = $productPage;

            }
        }

        //附加费用
        $costItemRelationList = CostItemInvoiceRelationApi::getCostItemRelationByInvoice(\Constants::TYPE_ORDER);
        $costItemRelationMap = array_column($costItemRelationList, null, 'relation_id');
        if( !empty($costList) ){

            foreach ( $costList as &$cost){
                if (isset($cost['cost_item_relation_id']) && !empty($cost['cost_item_relation_id'])) {
                    $cost['cost_name'] = $costItemRelationMap[$cost['cost_item_relation_id']]['item_name'] ?? '';

                    //备注是跟费用一期的，所以放在这里面
//                    if (isset($cost['cost_remark']) && !empty($cost['cost_remark'])) {
//                        $cost['cost_name'] .= "({$cost['cost_remark']})";
//                    }  // 费用名称和费用备注分开展示
                }
                //特殊处理
                if( $cost['percent_type'] == CustomFieldService::ADDITION_FEE_TYPE_PLUS_PERCENTAGE || $cost['percent_type'] == CustomFieldService::ADDITION_FEE_TYPE_SUBTRACT_PERCENTAGE ){
                    $cost['percent_of_total_amount_1'] = $cost['percent_amount'].'%';
                    $cost['percent_of_total_amount_2'] = $cost['percent_amount'];
                }else{
                    $cost['percent_of_total_amount_1'] = '';
                    $cost['percent_of_total_amount_2'] = '';
                }

                unset($cost['percent_type']);
                unset($cost['percent_amount']);

                $additionFeeList[] = $cost;
            }
            unset($cost);
        }

        //关联回款单

        unset($data['product_list']);
        unset($data['cost_list']);
        unset($data['file_list']);

        //添加userExternalInfo
        $user = \User::getLoginUser();
        $externalFields = [
            'external_email',
            'external_mobile',
            'external_fax',
            'external_address',
            'external_other'
        ];
        $userInfo = new UserInfo($user->getUserId(), $user->getClientId());
        $userExternalMap = $userInfo->getExtentAttributes($externalFields);
        $userExternalMap['nickname'] = $userInfo->nickname;

        $createExternalMap = [];
        $createUserInfo = new UserInfo($createUserId, $user->getClientId());
        if (!$createUserInfo->isNew())
        {
            $createExternalMap = $createUserInfo->getExtentAttributes($externalFields);
            $createExternalMap['create_user_nickname'] = $createUserInfo->nickname;
        }
        $userExternalMap['create_user_nickname'] = $createExternalMap['create_user_nickname'] ?? '';
        $userExternalMap['create_user_email'] = $createExternalMap['external_email'] ?? '';
        $userExternalMap['create_user_mobile'] = $createExternalMap['external_mobile'] ?? '';
        $userExternalMap['create_user_fax'] = $createExternalMap['external_fax'] ?? '';
        $userExternalMap['create_user_address'] = $createExternalMap['external_address'] ?? '';
        $userExternalMap['create_user_other'] = $createExternalMap['external_other'] ?? '';

        $client = Client::getClient($user->getClientId());

        $enterpriseInfo = [
            'short_name' => $client->name ?? '',
            'tel' => $client->tel ?? '',
            'homepage' => $client->homepage ?? '',
            'email' => $client->email ?? '',
            'address' => $client->address ?? '',
        ];

        //银行信息
        $capitalAccountInfo = [];
        if ($capitalAccountId>0) {
            $capitalAccountFilter = new CapitalAccountFilter($this->clientId);
            $capitalAccountFilter->capital_account_id = $capitalAccountId;
            $capitalAccountFilter->enable_flag = Constants::ENABLE_FLAG_TRUE;
            $capitalAccountFilter->select(['capital_account_id', 'name', 'capital_name', 'capital_bank', 'bank_account', 'address', 'remark']);
            $capitalAccountList = $capitalAccountFilter->rawData();
            if (!empty($capitalAccountList)) {
                $capitalAccountInfo['capital_account_id'] = $capitalAccountList[0]['name'] ?? '';
                $capitalAccountInfo['capital_name'] = $capitalAccountList[0]['capital_name'] ?? '';
                $capitalAccountInfo['capital_bank'] = $capitalAccountList[0]['capital_bank'] ?? '';
                $capitalAccountInfo['bank_account'] = $capitalAccountList[0]['bank_account'] ?? '';
                $capitalAccountInfo['capital_account_address'] = $capitalAccountList[0]['address'] ?? '';
                $capitalAccountInfo['capital_account_remark'] = $capitalAccountList[0]['remark'] ?? '';
            }
        }

        $groupDataMap = [
            FieldExportService::ORDER_GROUP_BASIC => [
                'key' => 'base',
                'data' => $data,
            ],
            FieldExportService::ORDER_GROUP_COMPANY => [
                'key' => 'base',
                'data' => $company,
            ],
            FieldExportService::ORDER_GROUP_CUSTOMER => [
                'key' => 'base',
                'data' => $customer,
            ],
            FieldExportService::ORDER_GROUP_EXCHANGE_PRODUCT => [
                'key' => 'product_list',
                'data' => $exchangeProductList,
            ],
            FieldExportService::ORDER_GROUP_PRODUCT_PAGE => [
                'key' => 'product_list',
                'data' => $productPageList,
            ],
            FieldExportService::ORDER_GROUP_ADDITION_FEE => [
                'key' => 'cost_list',
                'data' => $additionFeeList,
            ],
            FieldExportService::ORDER_GROUP_EXTERNAL_INFO => [
                'key' => 'base',
                'data' => $userExternalMap,
            ],
            FieldExportService::ORDER_GROUP_ENTERPRISE_INFO => [
                'key' => 'base',
                'data' => $enterpriseInfo,
            ],
            FieldExportService::ORDER_GROUP_BANK_INFO => [
                'key' => 'base',
                'data' => $capitalAccountInfo,
            ],
        ];
        return $groupDataMap;

    }

    /**
     * 导出数据的处理
     * @return array
     */
    protected function buildExportMapDataOld(){

        $data = $this->data;
        $exchangeProductList = [];
        $additionFeeList = [];
        $productPageList = [];

        $createUserId = $data['create_user'];
        $userIds = array_merge([$data['create_user']],$data['handler']??[],array_column($data['users']??[],'user_id'));
        $userList =  \common\library\account\Helper::getBatchUserInfo($this->clientId,$userIds);
        $userList =array_map(function ($elem){
            return ['user_id'=>$elem->user_id,'nickname'=>$elem->nickname,'avatar'=>$elem->avatar];
        },$userList);
        $userMap = array_combine(array_column($userList,'user_id'),$userList);

        //归属人处理
        $usersFormat ='';
        if( !empty($data['users']) ){
            $users = [];
            foreach ( $data['users'] as $elem ){
                $nickname = $userMap[$elem['user_id']]['nickname']??'未知';
                $users[] = "{$nickname} ({$elem['rate']}%)";
            }

            $usersFormat = implode(';',$users);

        }
        $data['users'] = $usersFormat;


        //处理人
        $handlerFormat ='';
        if( !empty($data['handler']) ){
            $handler = [];

            foreach ( $data['handler'] as $userId ){
                $nickname = $userMap[$userId]['nickname']??'未知';
                $handler[] = "{$nickname}";
            }
            $handlerFormat = implode(';',$handler);
        }
        $data['handler'] = $handlerFormat;

        //创建人
        $data['create_user']= $userMap[$data['create_user']]['nickname']??'';

        //业绩归属日期
        $data['account_date']= substr($data['account_date']??'',0,10);

        //状态
        $data['status'] = (new InvoiceStatusService($this->clientId, Constants::TYPE_ORDER))->getName($data['status']);

        //关联客户
        $company = [];
        if( isset($data['company_id']) && $data['company_id'] > 0 ){
            $company = new Company($this->clientId, $data['company_id']);
            $company = $company->isExist() ? $company->getAttributes() : [];
            if($company){
                $company['origin_list'] = (\CustomerOptionService::getOriginName($this->clientId, $company['origin_list'] ?? [])) ?? '';
                $company['group_id'] = \common\library\group\Helper::getGroupName(Constants::TYPE_COMPANY,$company['group_id']);
                $company['trail_status'] = (\CustomerOptionService::getStatusMap($this->clientId,[$company['trail_status']]))[$company['trail_status']]??'';
            }
        }

        //关联商机
        if( isset($data['opportunity_id']) && $data['opportunity_id'] > 0 ){
            $opportunity = new Opportunity($this->clientId, $data['opportunity_id']);
            $opportunity = $opportunity->isExist() ? $opportunity->getAttributes() : [];
            if($opportunity){
                $data['opportunity_id'] = $opportunity['name'] ?? '';
            }
        }

        //关联客户联系人
        $customer = [];
        if( isset($data['customer_id']) && $data['customer_id'] > 0 ){
            $customer = new Customer($this->clientId, $data['customer_id']);
            $customer = $customer->isExist() ? $customer->getAttributes() : [];
            if($customer){
                $customer['gender'] = $customer['gender']==Customer::GENDER_TYPE_MAN ? '男' :($customer['gender'] == Customer::GENDER_TYPE_WOMAN?'女':'未知');
                $customer['post_grade'] = Customer::POST_GRADE_NAME[$customer['post_grade']]??'';
            }

        }


        //交易产品
        if( isset($data['product_list']) && !empty($data['product_list']) ){

            $productIds = array_column($data['product_list'],'product_id');
            $productPageResult = ClientProduct::findAllByProductIds($this->clientId,$productIds);
            $productPageMap = [];
            $productGroupNameMap = [];
            if( !empty($productPageResult) ){
                $productPageMap = array_combine(array_column($productPageResult,'product_id'),$productPageResult);
                $productGroupIds = array_column($productGroupNameMap,'group_id');
                $productGroupNameMap = \common\library\group\Helper::getGroupNameMap($this->clientId,Constants::TYPE_PRODUCT,$productGroupIds);
            }

            foreach ( $data['product_list'] as $product ){

                //特殊处理交易产品毛利率
                $product['gross_profit_margin_1'] = $product['gross_profit_margin'].'%';
                $product['gross_profit_margin_2'] = $product['gross_profit_margin'];
                unset($product['gross_profit_margin']);

                $exchangeProductList[] = $product;

                $productPage = $productPageMap[$product['product_id']]??null;
                if( $productPage ){
                    $productPage =  $productPage->getAttributes();

                    //特殊处理产品图片
                    if(isset($productPage['images'])){
                        $productPage['images'] = json_decode($productPage['images'], true);
                        for($imgNo = 0; $imgNo <= 6; $imgNo++){
                            $imgIndexNo = $imgNo+1;
                            $imgIndex = "images_{$imgIndexNo}";

                            $productPage[$imgIndex] = $productPage['images'][$imgNo]['src'] ?? '';

                            $imageId = $productPage['images'][$imgNo]['id'] ?? 0;
                            if($imageId > 0){//获取内网url
                                $aliUpload = new \AliyunUpload();
                                if($aliUpload->loadByFileId($imageId)){
                                   $productPage[$imgIndex] = $aliUpload->getFileUrl(true);
                                }
                            }
                        }
                    }
                    unset($productPage['images']);

                    //产品属性字段
                    $categoryIds = $data['category_ids']??[];
                    $categoryId = end($categoryIds);

                    $categoryAttr = new AlibabaCategoryAttr($categoryId);
                    $productPage['info_json'] = $categoryAttr->formatFieldInfoJsonForCategoryId($productPage['info_json']??[],$categoryId);
                    $productPage['info_json'] = $categoryAttr->formatFieldInfoJson($productPage['info_json']);
                    //产品分组
                    $productPage['group_id'] = '';
                    if(isset($productPage['group_id'])) {
                        $productPage['group_id'] = $productGroupNameMap[$productPage['group_id']]??'';
                    }
                    //自定义字段
                    $productPage['external_field_data'] = json_decode($productPage['external_field_data'], true);

                    $productPage['category_ids'] = PgsqlUtil::trimArray($productPage['category_ids']);
                }

                $productPageList[] = $productPage;

            }
        }

        //附加费用
        if( isset($data['cost_list']) && !empty($data['cost_list']) ){

            foreach ( $data['cost_list'] as $cost){
                //特殊处理
                if( $cost['percent_type'] == CustomFieldService::ADDITION_FEE_TYPE_PLUS_PERCENTAGE || $cost['percent_type'] == CustomFieldService::ADDITION_FEE_TYPE_SUBTRACT_PERCENTAGE ){
                    $cost['percent_of_total_amount_1'] = $cost['percent_amount'].'%';
                    $cost['percent_of_total_amount_2'] = $cost['percent_amount'];
                }else{
                    $cost['percent_of_total_amount_1'] = '';
                    $cost['percent_of_total_amount_2'] = '';
                }

                unset($cost['percent_type']);
                unset($cost['percent_amount']);

                $additionFeeList[] = $cost;
            }
        }

        unset($data['product_list']);
        unset($data['cost_list']);
        unset($data['file_list']);

        //添加userExternalInfo
        $user = \User::getLoginUser();
        $externalFields = [
            'external_email',
            'external_mobile',
            'external_fax',
            'external_address',
            'external_other'
        ];
        $userInfo = new UserInfo($user->getUserId(), $user->getClientId());
        $userExternalMap = $userInfo->getExtentAttributes($externalFields);
        $userExternalMap['nickname'] = $userInfo->nickname;

        $createExternalMap = [];
        $createUserInfo = new UserInfo($createUserId, $user->getClientId());
        if (!$createUserInfo->isNew())
        {
            $createExternalMap = $createUserInfo->getExtentAttributes($externalFields);
            $createExternalMap['create_user_nickname'] = $createUserInfo->nickname;
        }
        $userExternalMap['create_user_nickname'] = $createExternalMap['create_user_nickname'] ?? '';
        $userExternalMap['create_user_email'] = $createExternalMap['external_email'] ?? '';
        $userExternalMap['create_user_mobile'] = $createExternalMap['external_mobile'] ?? '';
        $userExternalMap['create_user_fax'] = $createExternalMap['external_fax'] ?? '';
        $userExternalMap['create_user_address'] = $createExternalMap['external_address'] ?? '';
        $userExternalMap['create_user_other'] = $createExternalMap['external_other'] ?? '';

        $groupDataMap = [
            FieldExportService::ORDER_GROUP_BASIC => [
                'key' => 'base',
                'data' => $data,
                ],
            FieldExportService::ORDER_GROUP_COMPANY => [
                'key' => 'base',
                'data' => $company,
            ],
            FieldExportService::ORDER_GROUP_CUSTOMER => [
                'key' => 'base',
                'data' => $customer,
            ],
            FieldExportService::ORDER_GROUP_EXCHANGE_PRODUCT => [
                'key' => 'product_list',
                'data' => $exchangeProductList,
            ],
            FieldExportService::ORDER_GROUP_PRODUCT_PAGE => [
                'key' => 'product_list',
                'data' => $productPageList,
            ],
            FieldExportService::ORDER_GROUP_ADDITION_FEE => [
                'key' => 'cost_list',
                'data' => $additionFeeList,
            ],
            FieldExportService::ORDER_GROUP_EXTERNAL_INFO => [
                'key' => 'base',
                'data' => $userExternalMap,
            ],
        ];

        return $groupDataMap;

    }

    /***
     * 从文档中提取买家信息
     *
     * @param Page $page
     * @return array|string[]
     */
    protected function _fetchBuyerInfo(Page $page): array
    {
        $result = [];

        $modules = $page->data['modules'] ?? [];
        // 文档关联客户、联系人；(第一优先级)
        if ($page->company_id && $page->customer_id) {
            $company = new Company($this->clientId, $page->company_id);
            if ($company->isExist()) {
                $result['company_id'] = $company->company_id;
                $result['company_name'] = $company->name;
                $result['company_address'] = $company->address;
                $result['company_phone'] = $company->tel_full;

                $customer = new Customer($this->clientId, $page->customer_id);
                if ($customer->isExist()) {
                    $result['customer_id'] = $customer->customer_id;
                    $result['customer_name'] = $customer->name;
                    $result['customer_email'] = $customer->email;

                    // 联系人电话可以设置多个，取第一个
                    $firstTel = Arr::first($customer->tel_list);
                    if ($firstTel) {
                        $result['customer_phone'] = implode(' ', (array)$firstTel);
                    }
                }
            }
        }

        // 文档没有关联客户/联系人，则取单据表头的买家信息（次优先级）
        if (!$result) {
            $billHeader = Arr::first($modules,
                function ($module) {
                    return $module['module'] == DocConstants::PAGE_MODULE_BILL_HEADER;
                });
            $result = [
                'company_address' => $billHeader['data']['buyer_company_address'] ?? '',
                'customer_email' => $billHeader['data']['buyer_email'] ?? '',
                'customer_phone' => $billHeader['data']['buyer_phone'] ?? '',
            ];
        }
        return $result;
    }

    /**
     * 报价单文档转订单
     *
     * @param Page $page
     * @return array
     * @throws ProcessException
     */
    public function formatTradeDoc(Page $page): array
    {
        // 销售订单字段
        $fieldGroups = $this->getFieldList()
            ->setGroupId($this->showGroupIds)
            ->setFormatterType(CustomFieldFormatter::TYPE_GROUP_INFO)
            ->setNeedList(true)->find();

        $fieldGroups = array_column($fieldGroups, null, 'id');
        $basicGroup = $fieldGroups[CustomFieldService::ORDER_GROUP_BASIC];

        // 报价单名称->订单名称
        Arr::setItemKV($basicGroup['fields'],
            ['value' => $page->name], function ($item) {
                return $item['id'] == 'name';
            });

        // 填充买家信息
        $buyerInfo = $this->_fetchBuyerInfo($page);
        foreach ($buyerInfo as $key => $value) {
            Arr::setItemKV($basicGroup['fields'],
                ['value' => $value, 'format' => $value], function ($item) use ($key) {
                    return $item['id'] == $key;
                });
        }
        $fieldGroups[CustomFieldService::ORDER_GROUP_BASIC] = $basicGroup;

        // 填充产品信息
        $fieldsList = [];
        $modules = $page->data['modules'] ?? [];
        $priceListModules = Arr::where($modules, function ($module) {
            return $module['module'] == 'price_list';
        });
        $productGroup = $fieldGroups[CustomFieldService::ORDER_GROUP_PRODUCT];
        foreach ($priceListModules as $module) {
            $productList = Arr::flatten(data_get($module, 'data.sheet.*.list', []), 1);
            $productList = Arr::where($productList, function ($item) {
                return ($item['type'] ?? $item['text'] ?? '') == DocConstants::PAGE_MODULE_PRICE_LIST_ITEM_TYPE_PRODUCT;
            });

            foreach ($productList as $product) {
                $fieldValues = [];
                $fieldValues['product_id'] = $product['product_id'] ?? 0;
                $fieldValues['product_name'] = $product['item'] ?? '';
                if (isset($product['picture']['url'])) {
                    $fieldValues['product_image'] = $product['picture']['url'];
                }
                // 产品数量
                $fieldValues['count'] = $product['quantity'] ?? 0;
                // 含税成本价
                $fieldValues['cost_with_tax'] = ($product['quantity'] ?? 0) * ($product['unitPrice'] ?? 0);

                $productFields = Arr::first($productGroup['list']);
                foreach ($fieldValues as $field => $value) {
                    Arr::setItemKV($productFields,
                        ['value' => $value, 'format' => $value], function ($item) use ($field) {
                            return $item['id'] == $field;
                        });
                }
                $fieldsList[] = $productFields;
            }
        }
        $productGroup['list'] = $fieldsList;
        $fieldGroups[CustomFieldService::ORDER_GROUP_PRODUCT] = $productGroup;

        return array_values($fieldGroups);
    }

    /**
     * 询价单转订单，相同名称的赋值
     * @param array $quotationFormatData
     * @return array
     */
    public function formatInfoGroupForInquiryCollaboration(array $inquiryCollaborationFormatData,$inquiry_product_ids) {

        $groupOrder2InquiryMap = [
            CustomFieldService::ORDER_GROUP_BASIC => CustomFieldService::TYPE_INQUIRY_COLLABORATION_BASIC,
            CustomFieldService::ORDER_GROUP_PRODUCT => CustomFieldService::TYPE_INQUIRY_COLLABORATION_PRODUCT,
        ];

        //订单属性列表
        $list = $this->getFieldList();
        $list->setGroupId($this->showGroupIds);
        $list->setFormatterType(CustomFieldFormatter::TYPE_GROUP_INFO);
        $list->setFormatterScene(OmsConstant::CREATE);
        $list->setNeedList(true);
        $orderFormatData = $list->find();


        // //询价数据和属性列表
        $fieldList  = new FieldList($this->clientId);
        $fieldList->setType(Constants::TYPE_INQUIRY_COLLABORATION);
        $fieldList->setGroupId([]);
        $fieldList->setOrderBy(true ? FieldList::ORDER_APP : FieldList::ORDER_PC);
        $fieldList->setFormatterType(CustomFieldFormatter::TYPE_GROUP_INFO);
        $fieldList->setNeedList(true);
        $inquiryGroupFields = $fieldList->find();
        $inquiryFormatDataMap = array_column($inquiryGroupFields, null, 'id');

        //数据处理
        $this->matchFieldMapping($inquiryFormatDataMap,$orderFormatData,$groupOrder2InquiryMap);
        $this->setSkuInfo($inquiryCollaborationFormatData['record_list']??[]);
        $this->setInvoiceProductIds($inquiry_product_ids);
        $orderInfoGroup = [];
        foreach ($orderFormatData as $group) {
            $groupId = $group['id'];
            if (!isset($groupOrder2InquiryMap[$groupId])) {
                $group['list']=[];
                $orderInfoGroup[] = $group;
                continue;
            }
            $mapping = $groupId == CustomFieldService::ORDER_GROUP_BASIC ? $this->getBasicMappings() : $this->getProductMappings();
            $group['fields'] = $this->mapFields($group['fields'], $inquiryCollaborationFormatData, $mapping);
            $group['list'] = $this->mapListFields($group['list'][0] ?? [], $inquiryCollaborationFormatData['record_list'] ?? [], $mapping);
            $orderInfoGroup[] = $group;
        }
        return $orderInfoGroup;
    }



    /**
     * 报价单转订单，相同名称的赋值
     * @param array $quotationFormatData
     * @return array
     */
    public function formatInfoGroupForQuotation(array $quotationFormatData)
    {

        $infoGroup = [];

        $groupMap = [
            CustomFieldService::ORDER_GROUP_BASIC => CustomFieldService::QUOTATION_GROUP_BASIC,
            CustomFieldService::ORDER_GROUP_PRODUCT => CustomFieldService::QUOTATION_GROUP_PRODUCT,
            CustomFieldService::ORDER_GROUP_FEE => CustomFieldService::QUOTATION_GROUP_FEE,
            CustomFieldService::ORDER_GROUP_TRANSPORT => CustomFieldService::QUOTATION_GROUP_TRANSPORT,
            CustomFieldService::ORDER_GROUP_BUYER => CustomFieldService::QUOTATION_GROUP_BUYER,
        ];
        $costItemMap = $this->getCostItemMap();

        $list = $this->getFieldList();
        $list->setGroupId($this->showGroupIds);
        $list->setFormatterScene(OmsConstant::CREATE);
        $list->setFormatterType(CustomFieldFormatter::TYPE_GROUP_INFO);
        $list->setNeedList(true);
        $orderFormatData = $list->find();
//        $orderFormatData = $this->getFieldList()->fieldsList($this->showGroupIds);
        $orderName = $quotationFormatData['name'];
        $quotation_no = $quotationFormatData['quotation_no'];
        $quotation_id = $quotationFormatData['quotation_id'] ?? 0;
        $inquiryCollaborationId = $quotationFormatData['inquiry_collaboration_id'] ?? 0;
        $inquiryCollaborationInfo = $quotationFormatData['inquiry_collaboration_info'] ?? [];
        $quotationProductIds = array_column($quotationFormatData['product_list'] ?? [], 'quotation_product_id', 'unique_id');
        $quotationFormatData = array_combine(array_column($quotationFormatData['data'],'id'),$quotationFormatData['data']);
        $orderFormatData = array_combine(array_column($orderFormatData,'id'),$orderFormatData);

        foreach ( $orderFormatData as $group){

            $groupId = $group['id'];

            if( !isset($groupMap[$groupId]) ){
                $infoGroup[] = $group;
                continue;
            }

            $orderFields = $group['fields'];
            $quotationGroupId = $groupMap[$groupId];

            if (!isset($quotationFormatData[$quotationGroupId])){
                continue;
            }

            $quotationGroup = $quotationFormatData[$quotationGroupId];

            // 订单的贸易信息组字段已经移入基本信息组（报价单还保留贸易信息组），所以这里    做特殊转换处理
            // 报价单买方信息 合并至基本信息组
            if ($quotationGroupId == CustomFieldService::QUOTATION_GROUP_BASIC) {
                $quotationGroup['fields'] = array_merge(
                    $quotationGroup['fields'],
                    $quotationFormatData[CustomFieldService::QUOTATION_GROUP_FEE]['fields'],
                    $quotationFormatData[CustomFieldService::QUOTATION_GROUP_TRANSPORT]['fields'],
                    $quotationFormatData[CustomFieldService::QUOTATION_GROUP_BUYER]['fields'] ?? []
                );

                $quotationGroup['list'] = array_merge(
                    $quotationGroup['list'],
                    $quotationFormatData[CustomFieldService::QUOTATION_GROUP_FEE]['list'],
                    $quotationFormatData[CustomFieldService::QUOTATION_GROUP_TRANSPORT]['list'],
                    $quotationFormatData[CustomFieldService::QUOTATION_GROUP_BUYER]['list'] ?? []
                );
            }

            $quotationFields = array_combine(array_column($quotationGroup['fields'],'name'),$quotationGroup['fields']);

            $orderListFields = $group['list'][0]??[];
            $quotationListFields = $quotationGroup['list']??[];

            $listInfoGroup = [];

            if(!empty($orderListFields) && !empty($quotationListFields) ){

                foreach ($quotationListFields as $itemData){

                    $itemData = array_combine(array_column($itemData,'name'),$itemData);
                    $relationData = array_combine(array_column($itemData, 'relation_field'), $itemData);

                    $listItem = [];

                    foreach ( $orderListFields as  &$itemFiled ){


                        if( isset($itemData[$itemFiled['name']]) ){
                            if( $itemFiled['base'] == $itemData[$itemFiled['name']]['base'])
                            {
                                $itemFiled['value'] = $itemData[$itemFiled['name']]['value'];
                                $itemFiled['format'] = $itemData[$itemFiled['name']]['format'];
                            }
                        } elseif (
                            $itemFiled['field_type'] == CustomFieldService::FIELD_TYPE_FIELDS
                            &&
                            isset($relationData[$itemFiled['relation_field']])
                        ) {
                            // 产品关联字段
                            $itemFiled['value'] = $relationData[$itemFiled['relation_field']]['value'];
                            $itemFiled['format'] = $relationData[$itemFiled['relation_field']]['format'];
                        }

                        //费用字段的值引用需要改变掉
                        if ($itemFiled['id'] == 'cost_item_relation_id') {
                            $costItemRelationInfo = $costItemMap[$itemData[$itemFiled['name']]['value']] ?? [];
                            if (empty($costItemRelationInfo)) {
                                //销售订单不存在的费用，不转化
                                continue 2;
                            }
                            $itemFiled['format'] = $costItemRelationInfo;
                            $itemFiled['value'] = $costItemRelationInfo['relation_id'] ?? 0;
                        }

                        if ($itemFiled['id'] == 'quotation_product_id') {
                            $itemFiled['value'] = $quotationProductIds[$itemData['unique_id']['value'] ?? 0] ?? 0;
                        }

                        $listItem[] = $itemFiled;

                    }

                    $listInfoGroup[] = $listItem;

                }

            }

            foreach ( $orderFields as  &$filed ){

                //特殊字段处理
                if( $filed['id'] =='name' ){
                    $filed['value'] = $orderName;
                    continue ;
                }

                if ($filed['id'] == 'refer_quotation_no') {
                    $filed['value'] = [
                        'quotation_id' => $quotation_id ?? 0,
                        'quotation_no' => $quotation_no,
                    ];
                    continue;
                }

                if ($filed['id'] == 'inquiry_collaboration_id') {
                    $filed['value'] = $inquiryCollaborationId ?? 0;
                    continue;
                }

                if ($filed['id'] == 'refer_inquiry_collaboration_no') {
                    $filed['value'] = [
                        'inquiry_collaboration_id' => $inquiryCollaborationId,
                        'inquiry_collaboration_no' => $inquiryCollaborationInfo['inquiry_collaboration_no'] ?? '',
                    ];
                    continue;
                }
                if ($filed['id'] == 'quotation_id') {
                    $filed['value'] = $quotation_id ?? 0;
                    continue;
                }

                if( isset($quotationFields[$filed['name']]) ){
                    if( $quotationFields[$filed['name']]['base'] == $filed['base'])
                    {
                        $filed['value'] = $quotationFields[$filed['name']]['value'];
                        $filed['format'] = $quotationFields[$filed['name']]['format'];
                    }

                }
            }

            $group['list'] = $listInfoGroup;
            $group['fields'] = $orderFields;
            $infoGroup[] = $group;

        }

        return $infoGroup;

    }

    public function strip($data)
    {
        $data['user_id'] = PgsqlUtil::trimArray($data['user_id'] ?? '{}');
        $data['scope_user_ids'] = PgsqlUtil::trimArray($data['scope_user_ids'] ?? '{}');
        $data['users'] = json_decode($data['users'] ?? '{}', true);
        $data['departments'] = json_decode($data['departments'] ?? '{}', true);
        $data['handler'] = PgsqlUtil::trimArray($data['handler'] ?? '{}');
        $data['external_field_data'] = json_decode($data['external_field_data'] ?? '{}', true);
        $data['product_list'] = json_decode($data['product_list'] ?? '{}', true);
        $data['cost_list'] = json_decode($data['cost_list'] ?? '{}', true);
        $data['file_list'] = json_decode($data['file_list'] ?? '{}', true);
        $data['delete_time'] = $data['delete_time'] != '1970-01-01 08:00:00' ? $data['delete_time'] : '';
        $data['account_date'] = $data['account_date'] != '1970-01-01 08:00:00' ? $data['account_date'] : '';
        $data['last_sync_time'] =  isset($data['last_sync_time']) && date('Y-m-d',strtotime($data['last_sync_time'])) != '1970-01-01' ? $data['last_sync_time'] : '';
        $data['link_status'] = PgsqlUtil::trimArray($data['link_status'] ?? '{}');

        return $data;
    }

    public function getFieldReferType()
    {
        return Constants::TYPE_ORDER;
    }

    /**
     * 格式化app订单详情产品列表
     * @param $data
     * @return array
     */
    protected function formatOrderProductListData($data)
    {
        $data = array_column($data, null, 'id');
        if ($productInfo = $data[CustomFieldService::ORDER_GROUP_PRODUCT] ?? false) {
            return $this->buildOrderDetailProductList($productInfo['list']);
        }
        return [];
    }

    protected function buildOrderDetailProductList($list){
        foreach ($list as $product) {
            $product = array_column($product, null, 'id');
            $productList[] = [
                'product_no' => [
                    'id' => $product['product_no']['id'],
                    'name' => $product['product_no']['name'],
                    'disable_flag' => $product['product_no']['disable_flag'],
                    'value' => $product['product_no']['value']
                ],
                'product_id' => [
                    'id' => $product['product_id']['id'],
                    'name' => $product['product_id']['name'],
                    'disable_flag' => $product['product_id']['disable_flag'],
                    'value' => $product['product_id']['value']
                ],
                'product_image' => [
                    'id' => $product['product_image']['id'],
                    'name' => $product['product_image']['name'],
                    'disable_flag' => $product['product_image']['disable_flag'],
                    'value' => $product['product_image']['value']
                ],
                'product_name' => [
                    'id' => $product['product_name']['id'],
                    'name' => $product['product_name']['name'],
                    'disable_flag' => $product['product_name']['disable_flag'],
                    'value' => $product['product_name']['value']
                ],
                'product_model' => [
                    'id' => $product['product_model']['id'],
                    'name' => $product['product_model']['name'],
                    'disable_flag' => $product['product_model']['disable_flag'],
                    'value' => $product['product_model']['value']
                ],
                'unit_price' => [
                    'id' => $product['unit_price']['id'],
                    'name' => $product['unit_price']['name'],
                    'disable_flag' => $product['unit_price']['disable_flag'],
                    'value' => number_format($product['unit_price']['value'], 2,'.', '')
                ],
                'count' => [
                    'id' => $product['count']['id'],
                    'name' => $product['count']['name'],
                    'disable_flag' => $product['count']['disable_flag'],
                    'value' => $product['count']['value']
                ],
                'cost_amount' => [
                    'id' => $product['cost_amount']['id'],
                    'name' => $product['cost_amount']['name'],
                    'disable_flag' => $product['cost_amount']['disable_flag'],
                    'value' => number_format((is_numeric($product['cost_amount']['value']) ? $product['cost_amount']['value'] : 0), 2,'.', '')
                ],
                'product_enable_flag' => [
                    'id' => $product['product_enable_flag']['id'] ?? 'product_enable_flag',
                    'name' => $product['product_enable_flag']['name'] ?? '产品是否被删除',
                    'disable_flag' => $product['product_enable_flag']['disable_flag'] ?? 0,
                    'value' => $product['product_enable_flag']['value'] ?? 1
                ]
            ];
        }
        return $productList ?? [];
    }

    /**
     * 格式化app订单详情：基本信息、国际站订单信息、系统信息
     * @param array $result
     * @return array
     */
    protected function formatOrderDetailInfos(array $result)
    {
        if ($this->showBaseInfo && $this->showInfoGroup) {
            $baseInfo = array_column($result['data'], null, 'id')[CustomFieldService::ORDER_GROUP_BASIC]['fields'] ?? [];
            //兼容app数据结构格式
            foreach ($baseInfo as $index =>$item) {
                if ($item['id'] == 'account_date') {
                    $baseInfo[$index]['format'] = $baseInfo[$index]['value']= substr($item['format'],0,10);
                }

                if (in_array($item['id'], ['name', 'handler', 'order_no', 'status', 'opportunity_id', 'company_id'])) {
                    unset($baseInfo[$index]);
                    continue;
                }

                if (!is_numeric($item['id'])) {
                    if ($item['id'] == 'customer_id' && $item['value'] == '0') {
                        $baseInfo[$index]['format'] = '{"customer_id":0,"name":"","email":"","is_archive":"","tel_list":[]}';
                    }
                    continue;
                }

                if (!empty($item['relation_field'])) {
                    $baseInfo[$index]['id'] = $item['relation_field'];
                    if (is_numeric($item['relation_field']) && $item['field_type'] == CustomFieldService::FIELD_TYPE_FIELDS && !in_array($item['relation_field_type'], [CustomFieldService::FIELD_TYPE_IMAGE, CustomFieldService::FIELD_TYPE_ATTACH])) {
                            $baseInfo[$index]['format'] = is_array($item['value']) ? implode(',', $item['value']) : (is_string($item['value'])  ? $item['value'] : json_encode($item['value']));

                    }
                    if ($item['relation_field'] == 'annual_procurement') {
                        $baseInfo[$index]['format'] = \CustomerOptionService::annualProcurementMap()[$baseInfo[$index]['value']] ?? \Yii::t('customer', 'annual_procurement_0');
                    }
                    if ($item['relation_field'] == 'intention_level') {
                        $baseInfo[$index]['format'] = \CustomerOptionService::intentionLevelMap()[$baseInfo[$index]['value']] ?? \Yii::t('customer', 'intention_level_unknown');
                    }
                    continue;
                }

                if ($item['field_type'] == CustomFieldService::FIELD_TYPE_FIELDS) {
                    $baseInfo[$index]['format'] = is_array($item['value']) ? implode(',', $item['value']) : (is_string($item['value']) ? $item['value'] : json_encode($item['value']));
                    continue;
                }

                if ($item['field_type'] == CustomFieldService::FIELD_TYPE_ATTACH) {
                    $files = [];
                    foreach (($baseInfo[$index]['format'] ?? []) as $file) {
                        $files[] = [
                            'file_id' => $file['file_id'] ?? 0,
                            'file_name' => $file['file_name'] ?? '',
                            'file_size' => $file['file_size'] ?? 0,
                            'file_url' => $file['file_url'] ?? '',
                        ];
                    }
                    $baseInfo[$index]['format'] = json_encode($files ?? $baseInfo[$index]['format']);
                    continue;
                }

                $baseInfo[$index]['format'] = is_string($item['format']) ? $item['format'] : json_encode($item['format']);
            }
            $infos[] = [
                'name' => \Yii::t('field', '基本信息'),
                'fields' => $this->buildDetailInfoFields($baseInfo),
            ];
        }

        if ($this->showAlibabaOrderInfo && in_array($result['source_type'], [Order::TYPE_ALI_ORDER, Order::TYPE_DIRECT_PAY_ORDER])) {
            $aliOrderInfo = $result['alibaba_order_info'];
            $internationalInfo = [
                [
                    'id' => 'amount',
                    'name' => \Yii::t('field', '订单金额'),
                    'value' => number_format($aliOrderInfo['amount'] ?? 0, 2, '.', ''),
                    'format' => ($aliOrderInfo['currency'] ?? '') .' '.number_format($aliOrderInfo['amount'] ?? 0, 2, '.', ''),
                ],
                [
                    'id' => 'product_total_amount',
                    'name' => \Yii::t('field', '产品总金额'),
                    'value' => number_format($aliOrderInfo['product_total_amount'] ?? 0, 2, '.', ''),
                    'format' => ($aliOrderInfo['product_currency'] ?? '') .' '.number_format($aliOrderInfo['product_total_amount'] ?? 0, 2, '.', ''),
                ],
                [
                    'id' => 'shipment_fee_amount',
                    'name' => \Yii::t('field', '运费'),
                    'value' => number_format($aliOrderInfo['shipment_fee_amount'] ?? 0, 2, '.', ''),
                    'format' => ($aliOrderInfo['shipment_fee_currency'] ?? '') .' '. number_format($aliOrderInfo['shipment_fee_amount'] ?? 0, 2, '.', ''),
                ],
                [
                    'id' => 'shipment_insurance_fee',
                    'name' => \Yii::t('field', '物流保险费'),
                    'value' => number_format($aliOrderInfo['shipment_insurance_fee'] ?? 0,2, '.', ''),
                    'format' =>  ($aliOrderInfo['shipment_insurance_currency'] ?? '') .' '. number_format($aliOrderInfo['shipment_insurance_fee'] ?? 0,2, '.', ''),
                ],
                [
                    'id' => 'buyer',
                    'name' => \Yii::t('field', '买家昵称'),
                    'value' => $aliOrderInfo['buyer_email'] ?? '',
                ],
                [
                    'id' => 'store_name',
                    'name' => \Yii::t('field', '来源店铺'),
                    'value' => $aliOrderInfo['store_name'] ?? '',
                ],
                [
                    'id' => 'owner_email',
                    'name' => \Yii::t('field', '国际站业务员'),
                    'value' => $aliOrderInfo['owner_email'] ?? '',
                ],
                [
                    'id' => 'alibaba_trade_id',
                    'name' => \Yii::t('field', '国际站订单ID'),
                    'value' => $aliOrderInfo['alibaba_trade_id'] ?? 0,
                ],
                [
                    'id' => 'fulfillment_channel',
                    'name' => \Yii::t('field', '出口方式'),
                    'value' => $aliOrderInfo['fulfillment_channel'] ?? '',
                ],
            ];
            $infos[] = [
                'name' => \Yii::t('field', '国际站订单信息'),
                'fields' => $this->buildDetailInfoFields($internationalInfo),
            ];
        }

        if ($this->showSystemInfo) {
            $systemInfo = $result['system_info'];
            $systemInfo = [
                [
                    'id' => 'create_user',
                    'name' => \Yii::t('field', '创建人'),
                    'value' => $systemInfo['create_user']['nickname'] ?? '',
                    'format' => ['user_id' => $systemInfo['create_user']['user_id'] ?? 0, 'nickname' => $systemInfo['create_user']['nickname'] ?? '']
                ],
                [
                    'id' => 'create_time',
                    'name' => \Yii::t('field', '创建时间'),
                    'value' => $systemInfo['create_time'] ?? '',
                ],
                [
                    'id' => 'update_user',
                    'name' => \Yii::t('field', '修改人'),
                    'value' => $systemInfo['update_user']['nickname'] ?? '',
                    'format' => ['user_id' => $systemInfo['update_user']['user_id'] ?? 0, 'nickname' => $systemInfo['update_user']['nickname'] ?? '']
                ],
                [
                    'id' => 'update_time',
                    'name' => \Yii::t('field', '更新时间') ?? '',
                    'value' => $systemInfo['update_time'] ?? '',
                ],
                [
                    'id' => 'archive_type',
                    'name' => \Yii::t('field', '创建方式'),
                    'value' => Helper::getOrderArchiveTypeMaps()[$systemInfo['archive_type']]['name'] ?? '',
                    'format' => ['archive_type' => $systemInfo['archive_type'] ?? 0, 'name' => Helper::getOrderArchiveTypeMaps()[$systemInfo['archive_type']]['name'] ?? '']
                ],
                [
                    'id' => 'source_lead',
                    'name' => \Yii::t('field', '来源线索'),
                    'value' => $result['lead_info']['name'] ?? '',
                    'format' => ['lead_id' => $result['lead_info']['lead_id'] ?? 0, 'name' => $result['lead_info']['name'] ?? '']

                ],
                [
                    'id' => 'source_type',
                    'name' => \Yii::t('field', '订单类型'),
                    'value' => Helper::getOrderSourceTypeMaps()[$result['source_type']]['name'] ?? '',
                    'format' => ['source_type' => $result['source_type'] ?? 0, 'name' => Helper::getOrderSourceTypeMaps()[$result['source_type']]['name'] ?? '']

                ],
                [
                    'id' => 'last_sync_time',
                    'name' => \Yii::t('field', '上次同步时间'),
                    'value' => $systemInfo['last_sync_time'] ?? '',
                ],
            ];
            $infos[] = [
                'name' => \Yii::t('field', '系统信息'),
                'fields' =>  $this->buildDetailInfoFields($systemInfo),
            ];
        }

        return array_filter($infos ?? []);
    }

    protected static function buildDetailInfoFields($data)
    {
        $Fields = [];
        foreach ($data as $item) {
            $formatStr = $item['format'] ?? ($item['value'] ?? '');
            $formatStr = is_string($formatStr) ? $formatStr : json_encode($formatStr);
            $fieldInfo = [
                'id'            => $item['id'] ?? '',
                'name'          => $item['name'] ?? '',
                'base'          => $item['base'] ?? '1',
                'require'       => $item['require'] ?? '1',
                'group_id'      => $item['group_id'] ?? '0',
                'hint'          => $item['hint'] ?? '',
                'field_type'    => $item['field_type'] ?? '1',
                'ext_info'      => $item['ext_info'] ?? [],
                'default'       => $item['default'] ?? '',
                'disable_flag'  => $item['disable_flag'] ?? '0',
                'is_editable'   => $item['is_editable'] ?? '0',
                'unique_check'  => $item['unique_check'] ?? '0',
                'unique_prevent' => $item['unique_prevent'] ?? '1',
                'unique_message' => $item['unique_message'] ?? '',
                'value'         => $item['value'] ?? '',
                'format'        => $formatStr,
            ];
            $Fields[] = $fieldInfo;
        }
        return $Fields;
    }


    public function getOperatePrivilege($lockFlag = true,$handler = [])
    {
        $permissionMap = [
            'order_edit' => PrivilegeConstants::PRIVILEGE_CRM_ORDER_EDIT,
            'cash_collection_create' => PrivilegeConstants::PRIVILEGE_CRM_CASH_COLLECTION_CREATE,
            'cash_collection_invoice_create' => PrivilegeConstants::PRIVILEGE_CRM_CASH_COLLECTION_INVOICE_CREATE,
            'cash_collection_edit' => PrivilegeConstants::PRIVILEGE_CRM_CASH_COLLECTION_INVOICE_UPDATE_APPORTION,
            'logistics_view' => PrivilegeConstants::PRIVILEGE_CRM_TRANSPORT_VIEW,
            'order_export' => PrivilegeConstants::PRIVILEGE_CRM_ORDER_EXPORT,
            'order_generate_purchase_order' => PrivilegeConstants::PRIVILEGE_CRM_ORDER_TO_PURCHASE,
            'order_create' => PrivilegeConstants::PRIVILEGE_CRM_ORDER_CREATE,
            'order_delete' => PrivilegeConstants::PRIVILEGE_CRM_ORDER_REMOVE,
            'order_edit_number_create' => PrivilegeConstants::PRIVILEGE_CRM_ORDER_EDIT_NO_CREATE,
            'order_edit_number_edit' => PrivilegeConstants::PRIVILEGE_CRM_ORDER_EDIT_NO_EDIT,
            'edit_attachment' => PrivilegeConstants::PRIVILEGE_CRM_ORDER_EDIT,
        ];

        $initData = [
            'order_edit' => false,
            'cash_collection_create' => false,
            'logistics_view' => false,
            'order_export' => false,
            'order_generate_purchase_order' => false,
            'order_create' => false,
            'order_delete' => false,
            'order_edit_number_create' => false,
            'order_edit_number_edit' => false,
            'edit_attachment' => false,
        ];

        $data = $initData;
        foreach ($permissionMap as $k => $v){
            $permissionScopeUser = \common\library\privilege_v3\Helper::getPermissionScopeUser($this->clientId, $this->userId, $v, true);
            if ($permissionScopeUser === \common\library\privilege_v3\Helper::CAN_MANAGE_ALL_USER
                || array_intersect($handler, $permissionScopeUser)
            ) {
                $data[$k] = true;
            }
        }


        $unlock = false;
        $unlockPermission =  \common\library\privilege_v3\Helper::hasPermission($this->clientId,$this->userId, PrivilegeConstants::PRIVILEGE_CRM_ORDER_APPROVAL_UNLOCK);

        //是否可以解锁
        if($lockFlag) {
            $data = $initData;
        }

        if($unlockPermission && $lockFlag) {
            $unlock = true;
        }

        if(!empty($data['approval_flow_info']) && is_array($data['approval_flow_info'])) {
            $userId = \User::getLoginUser()->getUserId();
            $approvalFlowInfo = $data['approval_flow_info'];
            if($approvalFlowInfo['status'] == \common\library\approval_flow\Constants::APPROVAL_STATUS_APPROVING) {

                //审批中的除了指定的审批人可以编辑其他的不可编辑
                $userMap = array_column($approvalFlowInfo['approving_approver'],'edit_flag','user_id');

                if(isset($userMap[$userId]) && $userMap[$userId]) {
                    $data['order_edit'] = true;
                }else{
                    $data = $initData;
                }
                //审批中的不能解锁
                $unlock = false;
            }

            if($lockFlag && $approvalFlowInfo['status'] != \common\library\approval_flow\Constants::APPROVAL_STATUS_APPROVING){
                $data['edit_attachment'] = true;
            }
        }

        $data['order_unlock'] = $unlock;
        return  $data;
    }

    public function setData($data)
    {
        if ($this->onlyLocalProduct) {
            $productList = [];
            foreach ($data['product_list']??[] as $product) {
                if (!empty($product['sku_id'])) {
                    unset($product['platform_product_info']);
                    $productList[] = $product;
                }
            }
            $data['product_list'] = $productList;
        }

        parent::setData($data);
    }
    public function getOperateAccess($buttonConfig,$result)
    {
        return OrderButtonFactory::make($buttonConfig,$result);
    }


    public function buildLink($link,$order_id,$status_name)
    {


        $orderLink = OrderLink::make($link);
        $data = [];
        foreach ($orderLink->getLink() as  $link) {
            $temp = [
                'link' => $link->constant(),
                'title' => $link->currentStatus()->define(),
                'link_status' => $link->currentStatus()->constant(),
                'progress' => $link->currentStatus()->progress()->toArray()
            ];

            //备货中查询最大交货时间
            if($link->currentStatus()->constant() == Constant::ORDER_LINK_STOCK_UP_PENDING) {

                $maxDeliveryDate = $this->getMapData('maxDeliveryDateMap',$order_id);
                if(!empty($maxDeliveryDate) && $maxDeliveryDate != '1970-01-01 08:00:00'){
                    $temp['title'] = $temp['title'].'('.date('Y-m-d',strtotime($maxDeliveryDate)).')';

                }
            }

            $data[] = $temp;
        }

        return $data;
    }

    public function buildLinkStatus(array $order_link_status,$show_link_status = [])
    {
        $tagConfig  = [
            Constant::CASH_COLLECTION_AWAIT_LIST => Constant::ORDER_LINK_CASH_COLLECTION,
            Constant::STOCK_UP_AWAIT_LIST => Constant::ORDER_LINK_STOCK_UP,
            Constant::SHIPPING_AWAIT_LIST => Constant::ORDER_LINK_OUTBOUND,
            Constant::END_AWAIT_LIST => Constant::ORDER_LINK_END,
            Constant::END_DONE_LIST => Constant::ORDER_LINK_END,
        ];
        $orderLink = OrderLink::make($order_link_status);
        $data = [];

        $needTagLink = $tagConfig[$this->linkListType] ?? null;
        foreach ($orderLink->getLink() as  $link) {
            if(!empty($show_link_status) && !in_array($link->constant(),$show_link_status)) {
                continue;
            }
            $data[$link->constant()] = [
                'link' => $link->constant(),
                'title' => $link->currentStatus()->define(),
                'link_status' => $link->currentStatus()->constant(),
                'progress' => $link->currentStatus()->progress()->toArray(),
                'type' => $link->constant() == $needTagLink ? 'tag' : 'text'
            ];
        }

        $fieldMap = [
            Constant::ORDER_LINK_CASH_COLLECTION => 'cash_collection_status',
            Constant::ORDER_LINK_END => 'end_status',
            Constant::ORDER_LINK_STOCK_UP => 'stock_up_status',
            Constant::ORDER_LINK_OUTBOUND => 'shipping_status',
        ];

        $result = [];
        foreach ($data as  $linkConstant => $datum)
        {
            $result[$fieldMap[$linkConstant]] = $datum;
        }
        return $result;

    }

    protected function getSubProductList($order_id,$product_list)
    {
        $productListMap = array_column($product_list, null, 'unique_id');
        $invoiceProductRecodeObj = new InvoiceProductRecordList(\User::getLoginUser()->getUserId());
        $invoiceProductRecodeObj->setShowAllField();
        $invoiceProductRecodeObj->setEnableFlag(1);
        $invoiceProductRecodeObj->setReferId($order_id);
        $invoiceProductRecodeObj->setType(\Constants::TYPE_ORDER);
        $invoiceProductRecodeObj->setOrderBy(['sort','id']);
        $invoiceProductRecodeObj->setOrder('asc');
        $invoiceProductRecodeObj->formatter->setShowCombineProduct(true);
        $invoiceProductRecodeList = $invoiceProductRecodeObj->find();

        $subProductMap = [];
        foreach ($invoiceProductRecodeList as $key => $item) {
            $invoiceProductRecodeList[$key]['unique_id'] = $item['id'];
            if ($item['combine_record_id']){
                if(!isset($subProductMap[$item['combine_record_id']])){
                    $subProductMap[$item['combine_record_id']] = [];
                }
                $subProductMap[$item['combine_record_id']][] = $invoiceProductRecodeList[$key];
            }
        }

        $realList = [];
        foreach($invoiceProductRecodeList as $item){
            if($item['combine_record_id']){
                continue;
            }
            if($item['product_type'] == ProductConstant::PRODUCT_TYPE_COMBINE){
                $realList = array_merge($realList, $subProductMap[$item['id']] ?? []);
            }else{
                $productItem = $productListMap[$item['id']] ?? [];
                $realList[] = !empty($productItem) ? $productItem : $item;
            }
        }
        return $realList;
    }

    //获取费用项映射 报价单=>销售订单
    protected function getCostItemMap()
    {
        $quotationCostItemInvoiceRelation = CostItemInvoiceRelationApi::getCostItemRelationByInvoice(\Constants::TYPE_QUOTATION, 1);
        $orderCostItemInvoiceRelation = CostItemInvoiceRelationApi::getCostItemRelationByInvoice(\Constants::TYPE_ORDER);

        $quotationCostItemInvoiceMap = array_column($quotationCostItemInvoiceRelation, null, 'item_id');
        $orderCostItemInvoiceMap = array_column($orderCostItemInvoiceRelation, null, 'item_id');

        $costItemMap = [];
        foreach ($quotationCostItemInvoiceMap as $key => $value) {
            $costItemMap[$value['relation_id']] = $orderCostItemInvoiceMap[$key] ?? [];
        }
        return $costItemMap;
    }
}
