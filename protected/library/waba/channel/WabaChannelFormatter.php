<?php

namespace common\library\waba\channel;

use common\library\orm\pipeline\formatter\UserInfoTask;
use xiaoman\orm\common\Formatter;

/***
 * @method displayAssignUserList(bool $display)
 */
class WabaChannelFormatter extends Formatter
{
    public const MAPPING_SETTING = [
        'assign_user_list' => [
            'task_class' => UserInfoTask::class,
            'user_fields' => ['assign_user_list' => 'assign_users'],
        ],
    ];

    /***
     * 列表展示配置
     *
     * @return $this
     */
    public function searchListSetting(): self
    {
        $this->displayFields([
            'channel_id',
            'phone_name',
            'phone_number',
            'status',
            'current_limit',
            'current_quality_rating',
            'create_time',
            'update_time',
            'auth_status',
            'waba_channel_id',
            'name_status',
            'waba_id',
        ]);
        $this->displayAssignUserList(true);
        return $this;
    }

}
