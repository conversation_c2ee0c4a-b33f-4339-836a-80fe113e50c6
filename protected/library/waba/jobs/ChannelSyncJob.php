<?php

namespace common\library\waba\jobs;

use common\components\BaseObject;
use common\library\api\InnerApi;
use common\library\facebook\page\FacebookPageList;
use common\library\privilege_v3\PrivilegeService;
use common\library\queue_v2\job\BaseJob;
use common\library\queue_v2\QueueConstant;
use common\library\sns\Constants;
use common\library\social_auth\Constant;
use common\library\social_auth\Constant as SocialAuthConstants;
use common\library\social_auth\SocialAuth;
use common\library\util\Arr;
use common\library\waba\ChannelService;
use common\library\waba\WabaConstants;
use Exception;
use LogUtil;
use User;

class ChannelSyncJob extends BaseJob
{
    public $maxExceptions = 5;

    public $channel = QueueConstant::CONNECTION_NAME_DEFAULT;

    public $tag = QueueConstant::CONSUMER_PRIORITY_HIGH;

    /**
     * @var int 客户ID
     */
    public $clientId;

    /**
     * @var int 用户ID
     */
    public $userId;

    /**
     * @var int 频道类型
     */
    public $channelType = Constant::CHANNEL_TYPE_WABA;

    /**
     * @var string 消息内容
     */
    public function __construct(array $data, int $channelType = Constant::CHANNEL_TYPE_WABA)
    {
        $this->clientId = $data['client_id'];
        $this->userId = $data['user_id'];
        $this->channelType = $channelType;
    }

    protected function getAvailableChannelsIn360($wabaId): array
    {
        $service = ChannelService::getInstance($this->clientId);
        $list = $service->getAvailableChannelsFor360($wabaId);

        $channel = [];
        foreach ($list as $item) {
            if (empty($item['api_key'])) {
                continue;
            }

            $channel[] = [
                'channel_type' => SocialAuthConstants::CHANNEL_TYPE_WABA,
                'channel_open_id' => $item['phone_number'],
                'channel_name' => $item['phone_name'],
                'bsp_type' => 0,
                'config' => [
                    'token' => $item['api_key'],
                    'open_id' => $item['waba_channel_id'],
                    'waba_id' => $wabaId,
                ],
                'assign_users' => Arr::explodeStr($item['assign_users']),
            ];
        }
        return $channel;
    }

    protected function getAvailableChannelsInChatapp($wabaId): array
    {
        $service = ChannelService::getInstance($this->clientId);
        $list = $service->getAvailableChannelsForChatapp($wabaId);

        $channel = [];
        foreach ($list as $item) {
            $channel[] = [
                'channel_type' => SocialAuthConstants::CHANNEL_TYPE_WABA,
                'channel_open_id' => $item['phone_number'],
                'channel_name' => $item['phone_name'],
                'bsp_type' => 1,
                'assign_user' => Arr::explodeStr($item['assign_users']),
            ];
        }
        return $channel;
    }


    /**
     * 获取可用的Facebook主页
     */
    protected function getChannelsInFacebook(): array
    {
        $facebookPage = new FacebookPageList($this->clientId);
        $facebookPage->getFormatter()->listSyncInfoSetting();
        $list = $facebookPage->find();

        $channels = [];
        foreach ($list as $item) {
            $isUnbind = intval($item['oauth_flag']) ===  Constant::AUTH_TYPE_CANCEL;
            $enableFlag = intval($item['enable_flag']) === Constant::ENABLE_FLAG_TRUE && intval($item['oauth_flag']) === Constant::AUTH_TYPE_ENABLE;
            $channels[] = [
                'channel_type' => SocialAuthConstants::CHANNEL_TYPE_FACEBOOK,
                'channel_open_id' => $item['page_id'],
                'channel_name' => $item['page_name'],
                'avatar' => $item['page_avatar'],
                'enable_flag' => $enableFlag ? 1 : 0,
                'config' => [
                    'token' => $item['page_token'],
                    'open_id' => '',
                    'is_unbind' => $isUnbind ? 1 : 0
                ]
            ];
        }
        return $channels;
    }

    /**
     * 获取可用的Instagram主页
     */
    protected function getInstagramChannels(): array
    {
        $facebookPage = new FacebookPageList($this->clientId);
        $facebookPage->setOnlyIns(true);
        $facebookPage->setAuthType(Constants::SNS_TYPE_FACEBOOK_AUTH_INSTAGRAM);
        $facebookPage->getFormatter()->listSyncInstagramInfoSetting();
        $list = $facebookPage->find();

        $channels = [];
        foreach ($list as $item) {
            $isUnbind = intval($item['oauth_flag']) ===  Constant::AUTH_TYPE_CANCEL;
            $enableFlag = intval($item['enable_flag']) === Constant::ENABLE_FLAG_TRUE && intval($item['oauth_flag']) === Constant::AUTH_TYPE_ENABLE;
            $channels[] = [
                'channel_type' => SocialAuthConstants::CHANNEL_TYPE_INSTAGRAM,
                'channel_open_id' => $item['instagram_account_id'],
                'channel_name' => $item['instagram_username'],
                'avatar' => $item['instagram_avatar'],
                'enable_flag' => $enableFlag ? 1 : 0,
                'config' => [
                    'page_id' => $item['page_id'],
                    'token' => $item['page_token'],
                    'open_id' => '',
                    'is_unbind' => $isUnbind ? 1 : 0
                ]
            ];
        }
        return $channels;
    }

    /**
     * @throws Exception
     */
    public function handle(): void
    {
        LogUtil::info('begin sync chat channels', ['client_id' => $this->clientId]);

        $this->userId = PrivilegeService::getInstance($this->clientId)->getAdminUserId();
        if (!$this->userId) {
            LogUtil::error('waba sync channels failed, admin not found', ['client_id' => $this->clientId]);
            return;
        }
        User::setLoginUserById($this->userId);

        $api = new InnerApi('okki_chat_service');
        $api->addHeader(["cookie:clientId={$this->clientId}"]);
        $api->setCheckRspHttpCode(true);
        $api->setCheckRspJsonCode(true);

        switch($this->channelType) {
            case Constant::CHANNEL_TYPE_WABA :
                $channels = $this->getWabaChannels();
                break;
            case Constant::CHANNEL_TYPE_FACEBOOK :
                $channels = $this->getChannelsInFacebook();
                break;
            case Constant::CHANNEL_TYPE_INSTAGRAM :
                $channels = $this->getInstagramChannels();
                break;
            default:
                return;
        }

        $response = $api->call('syncChannel', [
            'client_id' => $this->clientId,
            'channels' => $channels,
            'channel_type' => $this->channelType
        ]);
        LogUtil::info('finish sync chat channels', $response);
    }

    /**
     * 获取waba频道
     * @return array
     */
    protected function getWabaChannels(): array
    {
        $accountInfo = (new SocialAuth())->loadBy([
            'client_id' => $this->clientId,
            'platform' => SocialAuthConstants::PLATFORM_WABA,
            'enable_flag' => BaseObject::ENABLE_FLAG_TRUE,
        ]);
        if ($accountInfo->isNew()) {
            LogUtil::error('waba sync channels failed, account not found', ['client_id' => $this->clientId]);
            return [];
        }

        $api = new InnerApi('okki_chat_service');
        $api->addHeader(["cookie:clientId={$this->clientId}"]);
        $api->setCheckRspHttpCode(true);
        $api->setCheckRspJsonCode(true);

        if ($accountInfo->source == WabaConstants::SOURCE_360DIALOG) {
            $channels = $this->getAvailableChannelsIn360($accountInfo->account_id);
        } else {
            $channels = $this->getAvailableChannelsInChatapp($accountInfo->account_id);
            $external = json_decode($accountInfo->external, true);
            Arr::setItemKV($channels, ['config' => ['custSpaceId' => $external['custSpaceId'], 'waba_id' => $accountInfo->account_id]]);
        }
        $response = $api->call('syncChannel', [
            'client_id' => $this->clientId,
            'channel_type' => WabaConstants::CHAT_CHANNEL_TYPE_WABA,
            'channels' => $channels,
        ]);
        LogUtil::info('waba sync channels', $response);

        return $channels;
    }

    // TOOD @heather 处理同步instagram消息类型到facebook
}
