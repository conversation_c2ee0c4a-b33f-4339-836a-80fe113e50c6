<?php
/**
 * Created by PhpStorm.
 * Author: <PERSON>(h<PERSON><PERSON><PERSON>)
 * Date: 2023/8/9
 * Time: 5:50 PM
 */
namespace common\library\waba\template_language\formatter;

use common\library\whatsapp_marketing\SystemParamHelper;
use common\library\whatsapp_marketing\WhatsappSystemParam;
use xiaoman\orm\common\PipelineTask;

class  ParamsTask extends PipelineTask {

    public function prepare(array $params)
    {

    }

    public function run(array $params)
    {
        $components = $params['components'];
        array_walk($components, function(&$component) {
            if (!isset($component['params'])) {
                return;
            }

            if (isset($component['text'])) {
                $component['text'] = SystemParamHelper::format($component['text'], $component['params']);
            }
            if (isset($component['extra']) && isset($component['extra']['html'])) {
                $component['extra']['html'] = SystemParamHelper::format($component['extra']['html'], $component['params']);
            }
        });
        $this->referenceData['components'] = $components;
    }
}