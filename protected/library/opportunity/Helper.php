<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON>.
 * User: troy.li
 * Date: 2018-07-26
 * Time: 10:38 AM
 */

namespace common\library\opportunity;

use common\library\account\external\UserInfoExternal;
use common\library\account\UserInfo;
use common\library\ai\classify\ai_field_data\OpportunityAIFieldData;
use common\library\ai\classify\capture\CaptureCard;
use common\library\ai\classify\capture\CaptureCardLog;
use common\library\ai\service\EventsReport;
use common\library\behavior\BehaviorService;
use common\library\cash_collection\CashCollectionList;
use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldList;
use common\library\customer\rule_config\RuleConfigConstants;
use common\library\department\DepartmentService;
use common\library\mail\Mail;
use common\library\object\field\service\ScopeUserFieldService;
use common\library\opportunity\orm\OpportunityMetadata;
use common\library\opportunity\statistics\OpportunityStageStatisticsList;
use common\library\privilege_v3\object_service\ObjPrivilegeService;
use common\library\privilege_v3\object_service\UserObjectPrivilegeService;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\privilege_v3\second_privilege\SecondPrivilegeConstants;
use common\library\server\es_search\SearchQueueService;
use common\library\setting\library\stage\Stage;
use common\library\setting\library\stage\Stage as OpportunityStage;
use common\library\setting\library\stage\StageApi;
use common\library\setting\user\UserSetting;
use common\library\trail\CompanyDynamicList;
use common\library\trail\events\RemarkEvents;
use common\library\trail\events\OpportunityEvents;
use common\library\trail\TrailConstants;
use common\library\util\PgsqlUtil;
use common\library\workflow\WorkflowConstant;
use common\models\search\OpportunitySearch;

class Helper
{

    //需要判断权限的字段
    const TRANS_FIELD_MAP = [
        'main_user' => 'main_user_info.nickname',
        'handler' => 'handler_info.nickname',
        'create_user' => 'create_user_info.nickname',
        'fail_type' => 'fail_type_name',
        'stage' => 'stage_info.name',
    ];

    const OPPORTUNITY_FIELDS = ["name","main_user","handler","exchange_rate","currency","customer_id","amount","main_lead_id","origin_list","type", 'cus_tag',"remark","fail_remark","department","serial_id","stage_info.success_rate","sale_flow_name","stage_info.name","fail_type_name","stage_type_name","fail_stage_info.name","main_user_info.nickname","handler_info.nickname","create_user_info.nickname","create_type","stage_stay_time","opportunity_trail","pin_user_list"];
    const COMPANY_FIELDS = ["company.name","company.short_name","company.country","company.timezone","company.biz_type","company.origin_list","company.product_group_ids"];
    const DATE_FIELDS = ["account_date","create_time","update_time","edit_time","end_time","trail_time","product_edit_time","next_follow_up_time"];
    const SORT_FIELDS = ["create_time","trail_time","order_time","edit_time","amount","account_date","update_time","product_edit_time","stage_stay_time"];

    public static function getFieldsSort() {

        return [
            RuleConfigConstants::RULE_TYPE_OPPORTUNITY => self::OPPORTUNITY_FIELDS,
            RuleConfigConstants::RULE_TYPE_COMPANY => self::COMPANY_FIELDS,
            RuleConfigConstants::RULE_TYPE_DATE => self::DATE_FIELDS,
        ];

    }


    public static function getValidatorFieldsRule()
    {
        return [
            'name'          => [
                'name'  => '商机名称',
                'rules' => [
                    'length' => [
                        'min'        => 1,
                        'max'        => 256,
                        'allowEmpty' => false,
                        'tooLong'    => '{attribute}长度超过限制',
                        'message'    => '{attribute}格式不正确'
                    ],
                    'match'  => [
                        'pattern' => '[;]',
                        'not'     => true,
                        'message' => '{attribute}不能包含分号'
                    ]
                ]
            ],
            'exchange_rate' => [
                'name'  => '汇率',
                'rules' => [
                    'numerical' => [
                        'min'        => 0,
                        'allowEmpty' => false,
                        'message'    => '{attribute}只能为数字'
                    ],
                ],
            ],
            'currency'      => [
                'name'  => '币种',
                'rules' => [
                    'length' => [
                        'allowEmpty' => false,
                        'message'    => '{attribute}格式不正确'
                    ],
                ],
            ],
            'stage'         => [
                'name'  => '销售状态',
                'rules' => [
                    'numerical' => [
                        'allowEmpty' => false,
                        'message'    => '{attribute}错误'
                    ],
                ],
            ],
            'account_date'  => [
                'name'  => '结束日期',
                'rules' => [
                    'length' => [
                        'min'        => 8,
                        'max'        => 10,
                        'allowEmpty' => false,
                        'tooShort'   => '{attribute}不能为空且为日期',
                        'tooLong'    => '{attribute}不能为空且为日期',
                        'message'    => '{attribute}不能为空且为日期'
                    ],
                ],
            ]
        ];
    }

    public static function getTypeMap($keyOnly = false)
    {
        $map = [
            Opportunity::TYPE_OF_NEW_CUSTOMER => \Yii::t('opportunity', 'New customer'),
            Opportunity::TYPE_OF_OLD_CUSTOMER => \Yii::t('opportunity', 'Old customer')
        ];
        return $keyOnly ? array_keys($map) : $map;
    }

    public static function getExporetFieldTypeOperatorMap()
    {
        return [
            CustomFieldService::FIELD_TYPE_TEXT => [
                WorkflowConstant::FILTER_OPERATOR_MATCH
            ],

            CustomFieldService::FIELD_TYPE_TEXTAREA => [
                WorkflowConstant::FILTER_OPERATOR_MATCH
            ],

            CustomFieldService::FIELD_TYPE_SELECT => [
                WorkflowConstant::FILTER_OPERATOR_IN
            ],

            CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT => [
                WorkflowConstant::FILTER_OPERATOR_IN
            ],

            CustomFieldService::FIELD_TYPE_DATE => [
                WorkflowConstant::FILTER_OPERATOR_EQUAL
            ],

            CustomFieldService::FIELD_TYPE_DATETIME => [
                WorkflowConstant::FILTER_OPERATOR_EQUAL
            ],

            CustomFieldService::FIELD_TYPE_NUMBER => [
                WorkflowConstant::FILTER_OPERATOR_RANGE,
            ],

            CustomFieldService::FIELD_TYPE_BOOLEAN => [
                WorkflowConstant::FILTER_OPERATOR_EQUAL,
            ],
        ];

    }

    /**
     * 通过客户获取商机
     *
     * @param       $userId
     * @param       $client_id
     * @param array $companyId
     * @param array $customerId
     * @param array $userType
     * @param array $fields
     *
     * @return array|
     */
    public static function getCustomerOpportunity(
        $client_id,
        $userId,
        array $companyId = [],
        array $customerId = [],
        array $userType = [OpportunityList::USER_TYPE_MAIN_USER, OpportunityList::USER_TYPE_HANDLER],
        $fields = []
    ) {
        if (empty($companyId) && empty($customerId)) {
            return [];
        }

        if (!\common\library\privilege_v3\PrivilegeService::getInstance($client_id, $userId)->hasFunctional(\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_OPPORTUNITY)) {
            return [];
        }

        $list = new OpportunityList($client_id);
        $list->setViewingUserId($userId);
        $list->setTrailActiveFlag(Opportunity::TRAIL_ACTIVE_FLAG_OK);
        $list->setUserType($userType);
        $list->setCompanyIds($companyId);
        $list->setCustomerIds($customerId);
        $list->setFields(empty($fields)
            ? ['opportunity_id', 'company_id', 'user_id', 'main_user', 'handler', 'customer_id']
            : $fields
        );

        return $list->find();
    }

    public static function updateTrailTime($clientId, $userId, $opportunityId, $time = null)
    {
        $time = $time ?? date('Y-m-d H:i:s');

        $db = \PgActiveRecord::getDbByClientId($clientId);

        $set = "update_time='{$time}', order_time=(CASE WHEN order_time < '{$time}'::timestamp THEN '{$time}' ELSE order_time END), trail_time=(CASE WHEN trail_time < '{$time}'::timestamp THEN '{$time}' ELSE trail_time END)";

        if (is_array($opportunityId) && !empty($opportunityId = array_filter($opportunityId))) {
            $opportunityIdSql = implode(',', $opportunityId);
            $sql = "update tbl_opportunity set {$set} where opportunity_id in ({$opportunityIdSql})";
        } else {
            $sql = "update tbl_opportunity set {$set} where opportunity_id={$opportunityId}";
        }

        $updateCount = $db->createCommand($sql)->execute();

        //商机绩效计算
        $opportunityId = is_array($opportunityId) ? $opportunityId : [$opportunityId];
        $opportunity = new OpportunityBatchOperator($userId);
        $opportunity->statistics($clientId,$opportunityId);

        return $updateCount;
    }

    public static function transfer($clientId, $userId, $opportunityIds, $toUserId, $operatorType)
    {

        $operator = new OpportunityBatchOperator($userId);
        $operator->setParams(['opportunity_ids' => $opportunityIds]);
        $operator->getList()->setShowAll(true, PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_TRANSFER);
        $operator->getList()->setAsMainUser();
        $count = $operator->transfer($toUserId);

        $companyIds = $operator->getCompanyIds();

        if (! empty($companyIds)) {
            \common\library\customer\Helper::opportunityOperatorTypeHandle($clientId, $userId, $toUserId, $operatorType, $companyIds);
        }

        return $count;
    }

    public static function archive($userId, $opportunityIds, $cancel_archive)
    {
        $operator = new OpportunityBatchOperator($userId);
        $operator->setParams(['opportunity_ids' => $opportunityIds]);
        $operator->getList()->setDisableFlag($cancel_archive ? Opportunity::DISABLE_FLAG_TRUE : Opportunity::DISABLE_FLAG_FALSE);
        if (!$cancel_archive) {
            $operator->getList()->setStageType([\common\library\setting\library\stage\Stage::STAGE_WIN_STATUS,\common\library\setting\library\stage\Stage::STAGE_FAIL_STATUS]);
        }
        $operator->getList()->setShowAll(true, PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_ARCHIVE);
        $count = $operator->archive($cancel_archive);
        return $count;
    }

    public static function batchChangeRecycleStage($clientId, $userId, $fromStage, $toStage)
    {
        if ($fromStage == $toStage) {
            return 0;
        }

        $operator = new OpportunityBatchOperator($userId);
        $operator->setParams(['stage' => $fromStage]);
        $operator->getList()->setSkipPermissionCheck(true);
        $operator->getList()->setEnableFlag(Opportunity::ENABLE_FLAG_DELETE);
        $count = $operator->changeStage($toStage);

        return $count;
    }

    public static function batchChangeFailStage($clientId, $userId, $fromStage, $toStage)
    {
        if ($fromStage == $toStage) {
            return 0;
        }

        $operator = new OpportunityBatchOperator($userId);
        $operator->setParams(['fail_stage' => $fromStage]);
        $operator->getList()->setSkipPermissionCheck(true);
        $operator->getList()->setIgnoreEnableFlag();
        $count = $operator->changeFailStage($toStage);

        return $count;
    }

    public static function batchChangeFailType($clientId, $userId, $fromFailType, $toFailType)
    {
        if ($fromFailType == $toFailType) {
            return 0;
        }

        $operator = new OpportunityBatchOperator($userId);
        $operator->setParams(['fail_type' => $fromFailType]);
        $operator->getList()->setIgnoreEnableFlag();
        $operator->getList()->setSkipPermissionCheck(true);
        $count = $operator->changeFailType($toFailType ?: 0);

        return $count;
    }

    public static function delete($clientId, $userId, $opportunityIds)
    {
        $operator = new OpportunityBatchOperator($userId);
        $operator->setParams(['opportunity_ids' => $opportunityIds]);
        $operator->getList()->setShowAll(true, PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_REMOVE);
        $operator->getList()->setAsMainUser();
        $count = $operator->delete();

        return $count;
    }

    /**
     * 提交跟进类型动态
     *
     * @param $userId
     * @param $opportunityId
     * @param $content
     * @param $remarkType
     * @param $customerId
     * @param $fileIds
     * @param $remarkTime
     * @param $address,
     * @param $longitude
     * @param $latitude
     *
     * @return bool
     */
    public static function remark($userId, $opportunityId, $content, $remarkType, $customerId, $fileIds, $remarkTime, $address = '', $longitude = 0, $latitude = 0)
    {
        $user = \User::getUserObject($userId);
        $clientId = $user->getClientId();
        $remarkTime = date('Y-m-d H:i:s', empty($remarkTime) ? time() : strtotime($remarkTime));
        $opportunity = new Opportunity($clientId, $opportunityId);
        $opportunity->setUserId($userId);
        if (!$opportunity->isExist()) {
            throw new \RuntimeException('商机:' . $opportunityId . '不存在');
        }
        if (!$opportunity->canRemark()) {
            throw new \RuntimeException(\Yii::t('account', 'No permission'));
        }

        $data = [
            'content'  => $content,
            'file_ids' => $fileIds,
            'address' => $address,
            'longitude' => round(floatval($longitude), 6),
            'latitude' => round(floatval($latitude), 6)
        ];

        $event = new RemarkEvents();
        $event->setType($remarkType);
        $event->setOpportunityId($opportunityId);
        $event->setClientId($clientId);
        $event->setCreateUser($userId);
        $event->setCompanyId($opportunity->company_id);
        $event->setCreateTime($remarkTime);
        $event->setUserId($userId);
        $event->setData($data);

        if (!empty($customerId)) {
            $event->setCustomerId($customerId);
        }

        $event->run();

        return true;
    }

    /**
     * 商机重大变更事件动态生成
     *
     * @param $client_id
     * @param        $user_id
     * @param        $type
     * @param array $opportunityIds
     * @param string $lastStage
     * @param string $nextStage
     * @param string $createTime
     * @param integer $fieldEditType
     * @param integer $referId
     * @return bool
     */
    public static function dynamicTrail(
        $client_id,
        $user_id,
        $type,
        array $opportunityIds,
        $lastStage = '',
        $nextStage = '',
        $createTime = '',
        $fieldEditType = OpportunityAIFieldData::FIELD_EDIT_TYPE_BY_USER,
        $referId = 0
    )
    {
        $list = new OpportunityList($client_id);
        $list->setViewingUserId($user_id);
        $list->setOpportunityIds($opportunityIds);
        $list->setSkipPermissionCheck(true);
        $list->setFields(['user_id', 'opportunity_id', 'client_id', 'company_id', 'customer_id', 'currency', 'amount', 'stage' , 'account_date']);
        $list->setEnableFlag($type === TrailConstants::TYPE_OPPORTUNITY_DEL ?
            Opportunity::ENABLE_FLAG_DELETE : Opportunity::ENABLE_FLAG_OK);
        $result = $list->find();

        foreach ($result as $opportunity) {
            try {
                $trail = new OpportunityEvents();
                $trail->setType($type);
                $trail->setOpportunityId($opportunity['opportunity_id']);
                $trail->setClientId($opportunity['client_id']);
                $trail->setCreateUser($user_id);
                $trail->setCompanyId($opportunity['company_id']);
                $trail->setCustomerId(PgsqlUtil::trimArray($opportunity['customer_id'] ?? '{}'));
                $trail->setUserId($user_id);
                if ($createTime)
                    $trail->setCreateTime($createTime);

                $data = [
                    'currency' => $opportunity['currency'],
                    'amount' => $opportunity['amount'],
                    'stage' => \common\library\opportunity\stage\Helper::getOneStage($client_id, $opportunity['stage'])['name'] ?? '',
                    'account_date' => $opportunity['account_date'] ?? ''
                ];

                if ($type === TrailConstants::TYPE_OPPORTUNITY_STAGE) {
                    $data['content'] = "商机销售阶段从【{$lastStage}】变更为【{$nextStage}】";
                } elseif ($type === TrailConstants::TYPE_OPPORTUNITY_TRAIL_STOP) {
                    $data['content'] = '停止了同步动态';
                }

                if ($fieldEditType == OpportunityAIFieldData::FIELD_EDIT_TYPE_BY_AI && !empty($referId) && $mail = new Mail($referId)) {
                    $data['refer_type'] = \Constants::TYPE_MAIL;
                    $data['refer_id'] = $referId;
                    $data['subject'] = $mail->subject;
                }

                $trail->setData($data);
                $trail->run();

                // 记录AI自动化执行的商机阶段变更|创建新商机，对应的Capture Card
                if ($fieldEditType == OpportunityAIFieldData::FIELD_EDIT_TYPE_BY_AI && !empty($referId) && isset($data['subject']) && in_array($type, [TrailConstants::TYPE_OPPORTUNITY_ADD, TrailConstants::TYPE_OPPORTUNITY_STAGE]) && $trail->getTrailId()) {
                    $capture = new CaptureCardLog($client_id, $trail->getTrailId());
                    $capture->biz_type = CaptureCard::BIZ_TYPE_DYNAMIC;
                    $capture->status = CaptureCard::STATUS_APPLY;
                    $capture->type = CaptureCard::REASON_TYPE_BY_MAIL_CONTENT;
                    $capture->setResult([
                        'refer_type' => CaptureCard::REFER_TYPE_MAIL,
                        'method' => $type == TrailConstants::TYPE_OPPORTUNITY_ADD ? CaptureCard::METHOD_CREATE : CaptureCard::METHOD_UPDATE_STAGE,
                        'name' => $data['subject'],
                        'mail_id' => $referId,
                        'user_id' => $user_id
                    ]);
                    $capture->save();
                }

                // 时间线记录
                $feed = new \Feed();
                $feed->setClientId($opportunity['client_id']);
                $feed->setUserId($user_id);
                $feed->setCreateUser($user_id);
                $feed->setNodeType($type);
                $feed->setReferId($opportunity['opportunity_id']);
                $feed->setData([]);
                $feed->save();

            } catch (\RuntimeException $e) {
                \LogUtil::info("商机重大事件动态生成失败，opportunity_id={$opportunity['opportunity_id']} " . $e->getMessage());
            }
        }

        return true;
    }

    public static function getUserIdsWhichHasOpportunity($clientId, $userId, array $companyId = [])
    {
        $list = new \common\library\opportunity\OpportunityList($clientId);
        $list->setViewingUserId($userId);
        $list->setSkipPermissionCheck(true);
        $list->setCompanyIds($companyId);
        $list->setEnableFlag(1);
        $list->setTrailActiveFlag(\common\library\opportunity\Opportunity::TRAIL_ACTIVE_FLAG_OK);
        $list->getFormatter()->setSpecifyFields(['user_id']);
        $res = $list->find();
        return array_values(array_unique(
            array_reduce(array_column($res, 'user_id'), 'array_merge', [])
        ));
    }

    public static function refreshIndex($clientId, $userId, $opportunityIds)
    {
        // 已迁移至es7
//        $list = new OpportunityList($clientId);
//        $list->setViewingUserId($userId);
//        $list->setOpportunityIds($opportunityIds);
//        $list->setSkipPermissionCheck(true);
//        $list->setFields('*');
//        $listData = $list->find();
//        OpportunitySearch::model()->indexData($clientId, $listData, true, array_merge([OpportunitySearch::dbPrimaryKey()], array_values(OpportunitySearch::sourceMapping())));
        return SearchQueueService::pushOpportunityQueue($userId, $clientId, $opportunityIds, \Constants::SEARCH_INDEX_TYPE_UPDATE);
    }

	/**
	 * 更改关注的客户CompanyID
	 *
	 * @param $needChangeCompanyId
	 * @param $changeToCompanyId
	 * @param $userId
	 * @return mixed
	 */
	public static function batchChangeCompanyId($needChangeCompanyId, $changeToCompanyId, $userId) {

		$user = \User::getUserObject($userId);
		$operator = new OpportunityBatchOperator($user->getUserId());
		$operator->setParams([
			'client_id'      => $user->getClientId(),
			'company_id'     => $needChangeCompanyId,
			'skip_privilege' => true,
		]);
		$res = $operator->updateMany([
			'company_id' => $changeToCompanyId,
		]);

		if ($needChangeCompanyId != $changeToCompanyId)
			\common\library\customer\Helper::refreshStaticCount($user->getClientId(), [$needChangeCompanyId, $changeToCompanyId], [\Constants::TYPE_OPPORTUNITY]);

        if ($needChangeCompanyId != $changeToCompanyId && !empty($res)) {
            \common\library\customer\Helper::batchUpdateOpportunityFlag($user->getClientId(), [$changeToCompanyId], 1);
        }

		return $res;
	}

	public static function batchChangeCustomerId($companyId, $fromCustomerId, $toCustomerId, $userId) {

		$user = \User::getUserObject($userId);
		$operator = new OpportunityBatchOperator($user->getUserId());
		$operator->setParams([
			'client_id'      => $user->getClientId(),
			'company_id'     => $companyId,
			'customer_id'    => $fromCustomerId,
			'skip_privilege' => true,
		]);
		$res = $operator->updateMany([
			'customer_id' => PgsqlUtil::formatArray([$toCustomerId]),
			'company_id'  => $companyId,
		]);

		return $res;
	}

    //返回客户的商机的标志位
    public static function getActiveOpCompanyList($userId, $clientId, array $companyIds)
    {
        $op = new OpportunityList($clientId);
        $op->setCompanyIds($companyIds);
        $op->setViewingUserId($userId);
        $op->setShowAll(1);
        $op->setFields("distinct company_id,stage_type");
        $op->setOrderBy([]);
        $opCompanyList = $op->find();
        $opResult = [];
        foreach ($opCompanyList as $opValue)
        {
            $opCompanyId = $opValue['company_id'];
            $opStageType = $opValue['stage_type'];
            if (!key_exists($opCompanyId, $opResult)) {
                $opResult[$opCompanyId] = $opStageType;
            } else {
                $opResult[$opCompanyId] = min($opStageType, $opResult[$opCompanyId]);
            }
        }
        return $opResult;
    }

    //返回客户最近的商机详情 (第一优先级 商机状态：进行中/其他 第二优先级 创建时间最新)
    public static function getLatestOpInfoCompanyList($userId, $clientId, array $companyIds)
    {
        $op = new OpportunityList($clientId);
        $op->setCompanyIds($companyIds);
        $op->setViewingUserId($userId);
        $op->setShowAll(1);
        $op->setFields("*,CASE stage_type
        WHEN 1 THEN 1
        ELSE 0
        END AS is_going");
        $op->setOrderBy(['is_going','update_time']);
        $op->setOrder('desc');
        $opCompanyList = $op->find() ?? [];
        $opResult = [];

        [$approvalLockMap, $failTypeMap] = self::getExtraMap($opCompanyList, $userId, $clientId);

        foreach ($opCompanyList as $opValue)
        {
            $opCompanyId = $opValue['company_id'];
            if (!key_exists($opCompanyId, $opResult)) {
                $opResult[$opCompanyId] = [
                    'opportunity_id' => $opValue['opportunity_id'],
                    'name' => $opValue['name'],
                    'stage_type' => $opValue['stage_type'],
                    'stage' => $opValue['stage'],
                    'currency' => $opValue['currency'],
                    'flow_id' => $opValue['flow_id'],
                    'account_date' => $opValue['account_date'],
                    'exchange_rate' => $opValue['exchange_rate'],
                    'exchange_rate_usd' => $opValue['exchange_rate_usd'],
                    'amount' => $opValue['amount'],
                    'amount_usd' => $opValue['amount_usd'],
                    'lock_flag' => $approvalLockMap[$opValue['opportunity_id']] ?? 0,
                    'fail_type' => $opValue['fail_type'],
                    'fail_type_name' => $failTypeMap[$opValue['fail_type']] ?? '',
                    'fail_remark' => $opValue['fail_remark'],
                    'fail_stage' => $opValue['fail_stage'] ?? 0,
                    'remark' => $opValue['remark'] ?? '',
                    'opportunity_count' => 1,
                ];
            } else {
                $opResult[$opCompanyId]['opportunity_count']++;
            }
        }
        return $opResult;
    }

    public static function getExtraMap($opportunityList, $userId, $clientId): array
    {
        $approvalLockMap = $failTypeMap = [];
        if ($opportunityList) {
            return [$approvalLockMap, $failTypeMap];
        }
        $opportunityIds = array_column($opportunityList, 'opportunity_id');
        $approvalLockList = \common\library\approval_flow\Helper::getReferLockList($userId, \Constants::TYPE_OPPORTUNITY, $opportunityIds, ['refer_id', 'lock_flag']);
        $approvalLockMap = array_column($approvalLockList, 'lock_flag', 'refer_id');

        $failTypeIds = array_unique(array_values(array_column($opportunityList, 'fail_type')));
        $failTypeMap = \common\library\opportunity\stage\Helper::getFailTypeNameMap($clientId, $failTypeIds);
        return [$approvalLockMap, $failTypeMap];
    }

    public static function getOpportunityIdByPiNumber($clientId, $piNo)
    {
        $list = \OpportunityExternalModel::model()->findAll('client_id=:client_id and key=:key and value=:value',
            [':client_id'=>$clientId, ':key'=>'pi_number', ':value'=>$piNo]);

        return array_column($list??[], 'opportunity_id');
    }

    public static function getHasPiNumberOpportunityIds($clientId, array $opportunityIds)
    {
        $db = \OpportunityExternalModel::getDbByClientId($clientId);
        $sql = "select opportunity_id from  tbl_opportunity_external where client_id=:client_id and opportunity_id in (" . implode(',', $opportunityIds) .
            ") and key=:key and value !=''";

        $params = [':client_id' => $clientId, ':key' => 'pi_number'];
        return $db->createCommand($sql)->queryColumn($params);
    }

    public static function transferByCompanyId($userId, array $companyId, array $stage, $toUser, $operatorType = 1)
    {
        if (empty($userId) || empty($companyId) || empty($stage) || empty($toUser)) {
            throw new \RuntimeException(\Yii::t('common', 'Parameter error'));
        }
        $operator = new OpportunityBatchOperator($userId);
        $operator->setParams([
            'user_type' => [1, 2],
            'show_all' => true,
            'company_id' => $companyId,
            'stage' => $stage
        ]);
        if ($operatorType == 1) {
            $operator->getList()->setShowAll(true, PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_TRANSFER);
            $count = $operator->transfer($toUser);
        } elseif ($operatorType == 2) {
            $operator->getList()->setShowAll(true, PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_MEMBER_MANAGE);
            $count = $operator->share($toUser);
        } else {
            throw new \RuntimeException(\Yii::t('opportunity', 'No right to delete team members'));
        }
        return $count;
    }

    public static function getSerialId($userId, $opportunityId)
    {
        $db = \PgActiveRecord::getDbByUserId($userId);
        $serialId = '';
        if($db) {
            $serialId = $db->createCommand("select serial_id from tbl_opportunity where opportunity_id=:opportunity_id")->queryScalar([':opportunity_id'=>$opportunityId]);
        }
        return $serialId ? Opportunity::getSerialIdPrefix() . $serialId : '';
    }

    public static function getName($userId, $opportunityId)
    {
        $db = \PgActiveRecord::getDbByUserId($userId);
        $name = '';
        if($db) {
            $name = $db->createCommand("select name from tbl_opportunity where opportunity_id=:opportunity_id")->queryScalar([':opportunity_id'=>$opportunityId]);
        }
        return $name;
    }

    public static function updateProductEditTime($opportunityId)
    {
        $time = date('Y-m-d H:i:s');
        return \Opportunity::model()->updateByPk($opportunityId, [
            'product_edit_time' => $time,
            'update_time' => $time,
        ]);
    }

    public static function getOpportunityCountMap($clientId, array $stageIds)
    {
        if( empty($stageIds) )
            return [];
        $stageIdStr = implode(',', $stageIds);
        $sql = "SELECT stage,count(1) as total FROM tbl_opportunity WHERE client_id = {$clientId} AND stage  in($stageIdStr) AND enable_flag = 1 group by stage";
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $opportunityList = $db->createCommand($sql)->queryAll(true);
        return array_column($opportunityList, 'total', 'stage');
    }

    public static function aiEventReport(array $opportunityList, $eventType, $userId, $clientId, $setUserId = false)
    {
        $eventReport = new EventsReport($clientId, $userId, \common\library\ai\service\EventsReport::REFER_TYPE_OPPORTUNITY_STAGE);

        foreach ($opportunityList as $opportunity) {

            if ($setUserId) {
                $opportunity['user_id'] = is_array($opportunity['user_id'])?json_encode($opportunity['user_id']):$opportunity['user_id'];
                $user_ids = \common\library\util\PgsqlUtil::trimArray($opportunity['user_id']);

                //兼容为空的情况
                if (empty($user_ids)) {
                    $user_ids = [0];
                }
                foreach ($user_ids as $user_id) {
                    $eventReport->setEvent($eventType);
                    $eventReport->setUserId($user_id);
                    $eventReport->setReferId($opportunity['opportunity_id'] ?? 0);
                    $eventReport->setExtraInfo([
                        'origin' => isset($opportunity['origin_list']) ? ($opportunity['origin_list'][0] ?? 0) : ($opportunity['origin'] ?? 0),
                        'stage' => $opportunity['stage'] ?? 0,
                        'weight' => (double)($opportunity['amount_usd'] ?? 0)
                    ]);
                    $eventReport->addBodyItem();
                }
            } else {
                $eventReport->setEvent($eventType);
                $eventReport->setReferId($opportunity['opportunity_id'] ?? 0);
                $eventReport->setExtraInfo([
                    'origin' => $opportunity['origin'] ?? 0,
                    'stage' => $opportunity['stage'] ?? 0,
                    'weight' => (double)($opportunity['amount_usd'] ?? 0)
                ]);
                $eventReport->addBodyItem();
            }
        }
        return $eventReport->report();
    }

    public static function stripFormat($data)
    {
        $result = [];
        foreach ($data as $groupData)
        {
            foreach ($groupData['fields'] as $field)
            {
                if (empty($field) || !isset($field['id'])) {
                    continue;
                }
                if ($field['base'] == 1 ) {
                    $value = $field['value'] ?? '';
                    if (is_string($value)) $value = trim($value);
                    $result[$field['id']] = $value;
                } else {
                    $value = $field['value'] ?? '';
                    if (is_string($value)) $value = trim($value);
                    $result['external_field_data'][$field['id']] = $value;
                }
            }
        }

        return $result;
    }


    public static function getOpportunityFilterList($clientId,$userId){

        $searchFieldData = [
            ['key'=>\Yii::t('opportunity', 'Opportunity name'),'value'=>'keyword'],
            ['key'=>\Yii::t('opportunity', 'Serial keyword'),'value'=>'serial_keyword'],
            ['key'=>\Yii::t('opportunity', 'Company keyword'),'value'=>'company_keyword'],
            ['key'=>\Yii::t('opportunity', 'Customer keyword'),'value'=>'customer_keyword'],
        ];

        $userTypeData = [
            ['key'=>\Yii::t('opportunity', 'Principal'),'value'=>'1'],
            ['key'=>\Yii::t('opportunity', 'Follow up'),'value'=>'2'],
            ['key'=>\Yii::t('opportunity', 'Founder'),'value'=>'3'],
        ];
        $approvalStatusData =[
                ['key'=>\Yii::t('opportunity', 'all'),'value'=>'0'],
                ['key'=>\Yii::t('opportunity', 'in examination and approval'),'value'=>'3'],
                ['key'=>\Yii::t('opportunity', 'agree'),'value'=>'1'],
                ['key'=>\Yii::t('opportunity', 'reject'),'value'=>'2'],
                ['key'=>\Yii::t('opportunity', 'backout'),'value'=>'4']
        ];
        $collectionStatuData = [
                ['key'=>\Yii::t('opportunity', 'None'),'value'=>'1'],
                ['key'=>\Yii::t('opportunity', 'Partial'),'value'=>'2'],
                ['key'=>\Yii::t('opportunity', 'Done'),'value'=>'3'],
        ];
        $pinData = [
            ['key'=>\Yii::t('opportunity', 'Add the attention'),'value'=>'1'],
            ['key'=>\Yii::t('opportunity', 'Remove attention'),'value'=>'0'],
        ];
        $typeData = [
            ['key'=>\Yii::t('opportunity', 'Old customer'),'value'=>'1'],
            ['key'=>\Yii::t('opportunity', 'New customer'),'value'=>'2'],
        ];
        $createTypeData  = [
            ['key'=>\Yii::t('opportunity', 'Unlimited'),'value'=>'0'],
            ['key'=>\Yii::t('opportunity', 'Yes'),'value'=>'2'],
            ['key'=>\Yii::t('opportunity', 'No'),'value'=>'1'],
        ];
        $stageTypeData = [
            ['key'=>\Yii::t('opportunity', 'stage_type_1'),'value'=>'1'],
            ['key'=>\Yii::t('opportunity', 'stage_type_2'),'value'=>'2'],
            ['key'=>\Yii::t('opportunity', 'stage_type_3'),'value'=>'3']
        ];

        $filterMap = [
            [   'title' => \Yii::t('opportunity', 'Select personnel'),
                'key' =>'user_id',
                'group_id' => 'user_id',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => [],
                'split' => null
            ],
            [   'title' => \Yii::t('opportunity', 'User type'),
                'key' =>'user_type',
                'group_id' => 'user_type',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => $userTypeData,
                'split' => null
            ],
            [   'title' => \Yii::t('opportunity', 'Search field'),
                'key' =>'search_field',
                'group_id' => 'search_field',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                'data' => $searchFieldData,
                'split' => null
            ],
            [   'title' => \Yii::t('opportunity', 'Search field'),
                'key' =>'keyword',
                'group_id' => 'search_field',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                'data' => null,
                'split' => null
            ],
            [   'title' => \Yii::t('opportunity', 'Is pin'),
                'key' =>'pin',
                'group_id' => 'pin',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                'data' => $pinData,
                'split' => null
            ],

            [   'title' => \Yii::t('opportunity', 'Sales flow'),
                'key' =>'flow_id',
                'group_id' => 'flow_id',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                'data' => [],
                'split' => null
            ],
            [   'title' => \Yii::t('opportunity', 'Sales stage'),
                'key' =>'stage',
                'group_id' => 'stage',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => [],
                'split' => null,
            ],
//            [
//                'title' => \Yii::t('opportunity', 'Approval status'),
//                'key' => 'approval_status',
//                'group_id' => 'approval_status',
//                'base'=>CustomFieldService::FIELD_SYS_FLAG,
//                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
//                'data' => $approvalStatusData,
//                'split' => null
//            ],
            [
                'title' => \Yii::t('opportunity', 'Source'),
                'key' => 'origin',
                'group_id' => 'origin',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => null,
                'split' => null,
                'columns' => 'origin_list'
            ],
            [
                'title' => \Yii::t('opportunity', 'Status'),
                'key' => 'stage_type',
                'group_id' => 'stage_type',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => $stageTypeData,
                'split' => null
            ],
            [
                'title' => \Yii::t('opportunity', 'Cash collection status'),
                'key' => 'cash_collection_status',
                'group_id' => 'cash_collection_status',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => $collectionStatuData,
                'split' => null
            ],
            /*
            [   'title' => \Yii::t('opportunity', 'Approval status'),
                'key' =>'approval_status',
                 'group_id' => 'approval_status',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => $approvalStatusData,
                'split' => null
            ],
*/
            [
                'title' => \Yii::t('opportunity', 'Cash collection date'),
                'key' => 'cash_collection_date',
                'group_id' => 'cash_collection_date',
                'base'=> CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_DATE_RANGE,
                'data' => [
                    ['key' => 'cash_collection_start_date'],
                    ['key' => 'cash_collection_end_date']],
                'split' => null
            ],
            [
                'title' => \Yii::t('opportunity', 'Sales amount'),
                'key' => 'min_amount',
                'group_id' => 'min_amount',
                'base'=> CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'data' => [],
                'split' => '~',
                'columns' => 'amount'
            ],
            [
                'title' => \Yii::t('opportunity', 'Sales amount'),
                'key' => 'max_amount',
                'group_id' => 'min_amount',
                'base'=> CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'data' => [],
                'split' => '',
                 'columns' => 'amount'
            ],
            [
                'title' => \Yii::t('opportunity', 'Currency'),
                'key' => 'currency',
                'group_id' => 'currency',
                'base'=> CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                'data' => [],
                'split' => ''
            ],
            [
                'title' => \Yii::t('opportunity', 'Account date'),
                'key' => 'account_date',
                'group_id' => 'account_date',
                'base'=> CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_DATE_RANGE,
                'data' => [
                    ['key' => 'account_start_date'],
                    ['key' => 'account_end_date']],
                'split' => null
            ],
            [
                'title' => \Yii::t('opportunity', 'Create date'),
                'key' => 'create_date',
                'group_id' => 'create_date',
                'base'=> CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_DATE_RANGE,
                'data' => [
                    ['key' => 'create_start_date'],
                    ['key' => 'create_end_date']],
                'split' => null
            ],
            [
                'title' => \Yii::t('opportunity', 'Edit date'),
                'key' => 'edit_date',
                'group_id' => 'edit_date',
                'base'=> CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_DATE_RANGE,
                'data' => [
                    ['key' => 'edit_start_date'],
                    ['key' => 'edit_end_date']],
                'split' => null
            ],
            [
                'title' => \Yii::t('opportunity', 'Trail date'),
                'key' => 'trail_date',
                'group_id' => 'trail_date',
                'base'=> CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_DATE_RANGE,
                'data' => [
                    ['key' => 'trail_start_date'],
                    ['key' => 'trail_end_date']],
                'split' => null
            ],
            [
                'title' => \Yii::t('opportunity', 'Product edit date'),
                'key' => 'product_edit_date',
                'group_id' => 'product_edit _date',
                'base'=> CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_DATE_RANGE,
                'data' => [
                    ['key' => 'product_edit_start_date'],
                    ['key' => 'product_edit_end_date']],
                'split' => null
            ],
            [
                'title' => \Yii::t('opportunity', 'Stage Edit Date'),
                'key' => 'stage_edit_date',
                'group_id' => 'stage_edit_date',
                'base'=> CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_DATE_RANGE,
                'data' => [
                    ['key' => 'stage_edit_start_date'],
                    ['key' => 'stage_edit_end_date']],
                'split' => null
            ],
            [
                'title' => \Yii::t('opportunity', 'Reason for losing the order'),
                'key' => 'fail_type',
                'group_id' => 'fail_type',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => [],
                'split' => null
            ],
            [
                'title' => \Yii::t('opportunity', 'Loss stage'),
                'key' => 'fail_stage',
                'group_id' => 'fail_stage',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => [],
                'split' => null
            ],
            [
                'title' => \Yii::t('opportunity', 'Type'),
                'key' => 'type',
                'group_id' => 'type',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => $typeData,
                'split' => null
            ],
            [
                'title' => \Yii::t('opportunity', 'Create type'),
                'key' => 'create_type',
                'group_id' => 'create_type',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                'data' => $createTypeData,
                'split' => null
            ],
        ];

        $filterMap = array_values($filterMap);
        //自定义字段
        $fields = ['id','field_id','name','ext_info','field_type'];
        $fieldList = self::getUserOpptunitySearchFiledSetting($userId,$fields);
        $opptunityField =
            [
                'title' => \Yii::t('opportunity', 'Opportunity field'),
                'key' =>'opportunity_field',
                'group_id' =>'opportunity_field',
                'base'=>CustomFieldService::FIELD_CUSTOM_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_OTHER,
                'data' => $fieldList??[]
            ];

        $filterMap[] = $opptunityField;

        $user = \User::getUserObject($userId);
        $fieldPrivileges = \common\library\privilege_v3\Helper::getFieldPrivilegeByObjName($user, \common\library\object\object_define\Constant::OBJ_OPPORTUNITY, PrivilegeConstants::FUNCTIONAL_OPPORTUNITY);
        $disableFields = $fieldPrivileges['disable'];
        foreach ($filterMap as $index => $filter) {
            $columns = $filter['columns'] ?? $filter['key'];
            if (in_array($columns, $disableFields)) {
                unset($filterMap[$index]);
            }
        }
        $filterMap = array_values($filterMap);

        return $filterMap;
    }

    public static function getUserOpptunitySearchFiledSetting($userId,$fields = []){
        $user = \User::getUserObject($userId);
        $clientId = $user->getClientId();
        $opptunitySearchSetting =[];
        $module = \Constants::TYPE_OPPORTUNITY;

        $attrKeys = UserInfoExternal::EXTERNAL_KEY_OPPORTUNITY_EXTERNAL_SEARCH_FIELD;
        $attrs = $user->getInfoObject()->getExtentAttributes($attrKeys);
        if(!$attrs || !isset($attrs[$attrKeys])){
            return [];
        }
        $opptunitySearchFiled = $attrs[$attrKeys];
        $opptunitySearchFiled = array_filter(is_array($opptunitySearchFiled) ? $opptunitySearchFiled : [$opptunitySearchFiled], 'is_numeric');
        if(!empty($opptunitySearchFiled)) {
            $fieldListObj = new FieldList($clientId);

            $fieldListObj->setType($module);
            $fieldListObj->setId($opptunitySearchFiled);
            $fieldListObj->setDisableFlag(0);
            $fieldListObj->setBase(0);
            if($fields){
                $fieldListObj->setFields($fields);
            }

            $opptunitySearchSetting = $fieldListObj->find();
            foreach ( $opptunitySearchSetting as &$opptunityItem ){
                $opptunityItem['match_type'] = 'term';
                $opptunityItem['field_id'] = $opptunityItem['id'];
            }
        }
        return $opptunitySearchSetting;

    }

    /**
     * 检查当前用户是否拥有商机的编辑权限
     * @param $clientId
     * @param $loginUserId 登录user_id
     * @param array $handlerInfo 商机团队成员
     * @param array $opportunityUserIds 商机的所有跟进人
     * @return bool
     * @throws \Exception
     */
    public static function checkOpportunityEditPrivilege($clientId,$loginUserId,array $handlerInfo,array $opportunityUserIds) {

        //判断登录用户是否为商机团队成员并且有编辑权限
        $needCheckRoleIds =[SecondPrivilegeConstants::MODULE_OPPORTUNITY_EDIT_ROLE,SecondPrivilegeConstants::MODULE_OPPORTUNITY_EDIT_ROLE_WITHOUT_TRAIL];
        foreach ($handlerInfo as $item){
            if($item['user_id'] == $loginUserId && in_array($item['role_id'],$needCheckRoleIds)){
                return true;
            }
        }

        //判断用户是否为商机的跟进人的上级
        $manageUserIds = [];
        foreach ($opportunityUserIds as $userId){
            $departmentService = new DepartmentService($clientId);
            $departmentAdminUserList = $departmentService->getUserDepartmentAdminUserList($userId);
            if($departmentAdminUserList){
                $departmentAdminUserIds = array_column($departmentAdminUserList,'user_id');
                $manageUserIds = array_merge($departmentAdminUserIds,$manageUserIds);
            }
        }
        //跟进人的管理人，允许编辑
        if($manageUserIds){
            $manageUserIds = array_values(array_unique($manageUserIds));
            if(in_array($loginUserId,$manageUserIds)){
                return true;
            }
        }


        return false;
    }



    /** 删除商机pi number 设置，商机文档
     * @param $opportunityId
     * @param $fileId
     * @param $clientId
     * @param $userId
     * @return bool
     */
    public static function removePiNumberAndFile($opportunityId,$fileId,$clientId,$userId)
    {
        if(!$opportunityId || !$fileId || !$clientId || !$userId){
            return false;
        }
        $opportunity = new \common\library\opportunity\Opportunity($clientId,$opportunityId);
        $opportunity->setUserId($userId);
        //删除商机附件
        $opportunity->removeFile($fileId);

        //商机的pi number 更新为空
        $opportunity->extendAttributes()->pi_number = "";
        $opportunity->save();

        return true;
    }

    /** 检查商机的file_id 是否存在
     * @param $opportunityId
     * @param $clientId
     * @param $fileId
     * @return bool
     */
    public static function checkOpportunityFileId($opportunityId,$clientId,$fileId){

        $opportunity = new Opportunity($clientId,$opportunityId);
        $opportunity->getFormatter()->setShowCaptureCard(true);
        $opportunity->getFormatter()->setShowFileList(true);
        $opportunityInfo = $opportunity->getAttributes();

        //根据$fileId
        if(isset($opportunityInfo['file_list']) && $opportunityInfo['file_list']){
            foreach ($opportunityInfo['file_list'] as $item){
                if(isset($item['file_id']) && $item['file_id'] == $fileId){
                    return true;
                }
            }
        }
        return false;
    }

    /** 插入商机邮件动态，更新商机pi number
     * @param array $opportunityIds
     * @param $fileId
     * @param $piNumber
     * @param $mailId
     * @param $clientId
     * @param $userId
     * @return bool
     */
    public static function bindTrailReferPiNumberAndFile(array $opportunityIds,$fileId,$piNumber,$mailId,$clientId,$userId)
    {
        foreach ($opportunityIds as $opportunityId){

            $opportunity = new \common\library\opportunity\Opportunity($clientId,$opportunityId);
            $opportunity->setUserId($userId);
            if(!$opportunity->isExist()){
                continue;
            }
            //如果pi number 没改变，不需要处理
            if($opportunity->extendAttributes()->pi_number && $opportunity->extendAttributes()->pi_number == $piNumber){
                continue;
            }
            /*
            //先移除商机旧的pi文档
            if($opportunity->extendAttributes()->pi_number){
                //寻找商机文档fileId
                $oldFileId = self::findPiNumberFileId($opportunityId,$clientId);
                $opportunity->removeFile($oldFileId);

            }
            */
            $opportunity->extendAttributes()->pi_number  = $piNumber;
            $exitsFileId = self::checkOpportunityFileId($opportunityId,$clientId,$fileId);

            //file id 不存在，商机文档插入新的
            if(!$exitsFileId){
                //新增新的商机pi文档
                $opportunity->addFile(
                    [$fileId],
                    Opportunity::FILE_LIST_REFER_TYPE_AI,
                    $mailId);
            }

            $opportunity->save();
        }

        return true;
    }


    /** 批量移除商机的pi number 以及文档
     * @param $clientId
     * @param array $opportunityIds
     * @param $userId
     * @return bool
     */
    public static function batchRemovePiNumberAndFile($clientId,array $opportunityIds,$userId)
    {
        foreach ($opportunityIds as $opportunityId){

            $opportunity = new \common\library\opportunity\Opportunity($clientId,$opportunityId);
            $opportunity->setUserId($userId);
            if(!$opportunity->isExist()){
                continue;
            }

            if(!$opportunity->extendAttributes()->pi_number){
                continue;
            }
            $opportunity->extendAttributes()->pi_number  = "";
            //寻找商机文档fileId
            $oldFileId = self::findPiNumberFileId($opportunityId,$clientId);
            if($oldFileId){
                //商机旧的pi文档
                $opportunity->removeFile($oldFileId);
            }
            $opportunity->save();
        }

        return true;
    }

    /** 或者company_id + mail_id，已绑定的商机id
     * @param $clientId
     * @param $userId
     * @param $mailId
     * @param $companyId
     * @return array
     */
    public static function getEmailDynamicOpportunityIds($clientId,$userId,$mailId,$companyId){
        $opportunityIds = [];

        //判断商机是否有邮件动态
        $dynamicList = new \common\library\trail\CompanyDynamicList($clientId);
        $dynamicList->setOperatorUserId($userId);
        $dynamicList->setReferId($mailId);
        $dynamicList->setCompanyId($companyId);
        $dynamicList->setModuleId(\common\library\trail\TrailConstants::MODULE_MAIL);
        $dynamicList->setSkipCheckPrivilege(true);
        $list  = $dynamicList->find();
        if(!$list){
            return $opportunityIds;
        }
        foreach ($list as $item){
            if($item['opportunity_id']){
                $opportunityIds[] = $item['opportunity_id'];
            }
        }
        return array_values(array_unique($opportunityIds));
    }

    public static function getDashboradSetting($clientId,$userId){
        $dashboard  = new \common\library\setting\user\UserSetting($clientId,$userId,\common\library\setting\user\UserSetting::OPPORTUNITY_DASHBOARD_SETTING);
        if(!$dashboard){
            return null;
        }
        $value = json_decode($dashboard->value,true);

        $result['type'] = $value['type']??null;
        $result['format'] = $value['format']??null;
        $result['field'] = isset($value['field'])?json_decode($value['field'],true):null;
        return $result;
    }

    /**
     * 是否有手动创建过商机
     * @param  $clientId
     * @param  $startDate
     * @param  $endDate
     * @return bool
     */
    public static function checkHasCreateByUser($clientId, $startDate, $endDate) {
        if (!$clientId || !$startDate || !$endDate) {
            return false;
        }
        if (!\common\library\privilege_v3\Helper::hasFunctional($clientId,PrivilegeConstants::FUNCTIONAL_OPPORTUNITY)) {
            return false;
        }
        $listObj = new OpportunityList($clientId);
        $listObj->setSkipPermissionCheck(true);
        $listObj->setCreateType(Opportunity::CREATE_TYPE_USER);
        $listObj->setCreateStartDate($startDate);
        $listObj->setCreateEndDate($endDate);
        $listObj->setLimit(1);
        return !empty($listObj->find()) ? true : false;
    }

    /**
     * 获取阶段停留时间
     * @param $clientId
     * @param $opportunity_id
     * @param $stageIds
     * @return array
     */
    public static function getOpportunityStageStayTime($clientId, $opportunity_id, $stageIds)
    {
        $listObj = new OpportunityStageStatisticsList($clientId);
        $listObj->setOpportunityId($opportunity_id);
        $listObj->setStageId($stageIds);
        $stageStatisticslist = $listObj->find();
        $result = [];
        foreach ($stageStatisticslist as $item)
        {
            $stageId = $item['stage_id'];
            if (!key_exists($stageId, $result)) {
                $createTime = strtotime($item['create_time']);
                $endTime = $item['end_time'] == '1970-01-01 00:00:00' ? time() : strtotime($item['end_time']);
                $result[$stageId] = [
                    'stay_time' => $endTime - $createTime
                ];
            }
        }
        return $result;
    }

    public static function archiveMail($clientId, $userId, $opportunityId, $mailId)
    {
        $opportunity = new Opportunity($clientId, $opportunityId);
        $opportunity->setUserId($userId);
        if (!$opportunity->isExist()) {
            throw new \RuntimeException('商机:' . $opportunityId . '不存在');
        }
        if (!$opportunity->canRemark()) {
            throw new \RuntimeException(\Yii::t('account', 'No permission'));
        }

        $mail = new Mail($mailId);
        if($mail->isNew()) {
            throw new \RuntimeException(\Yii::t('mail', 'Mail does not exist'));
        }

        try {
            $event = new RemarkEvents();
            $event->setType(TrailConstants::TYPE_REMARK_RELATE_MAIL);
            $event->setClientId($clientId);
            $event->setCreateUser($userId);
            $event->setOpportunityId($opportunityId);
            $event->setCompanyId($opportunity->company_id);
            $event->setUserId($userId);
            $event->setReferId($mailId);
            $event->setCreateTime($mail->receive_time);
            $event->run();
        } catch (\RuntimeException $e) {
            \LogUtil::info("关联邮件备注动态生成失败，mail_id={$mailId} " . $e->getMessage());
        }
        return true;
    }

    /**
     * 返回商机导出已设置字段
     * @param $userId
     * @param $clientId
     * @return array
     */
    public static function getExportMapList($userId, $clientId)
    {
        //如果已经存在映射关系，先加载目前的映射
        $setting = new UserSetting($clientId, $userId, UserSetting::OPPORTUNITY_EXPORT_MAP);
        $opportunityExportMap = $setting->getValue();
        $opportunityExportMap = json_decode($opportunityExportMap,true);
        if (empty($opportunityExportMap)) {
            //返回默认导出字段
            $opportunityExportMap = \common\library\export\OpportunityExport::getDefaultFieldMap();
        }
        return $opportunityExportMap;
    }

    /**
     * 返回商机支持导出的字段
     * @param $userId
     * @param $clientId
     * @return array
     */
    public static function getExportFieldList($userId, $clientId)
    {
        $result = [];

        $fullFieldList = \common\library\customer\Helper::getFullFieldList($clientId, $userId, \Constants::TYPE_OPPORTUNITY, false);
        
        // 导出字段权限
        [$type, $functionalId] = \common\library\privilege_v3\Helper::getRealReferAndFunctionalId(\Constants::TYPE_OPPORTUNITY);
        $privilegeService = new UserObjectPrivilegeService($clientId);
        $privilegeService->setUserId($userId);
        $privilegeService->setFunctionalId($functionalId);
        $privilegeService->setFieldPrivilegeScene(ObjPrivilegeService::OBJECT_SCENE_EXPORT);
        $privilegeService->getFieldIdByScope($type, [PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_DISABLE]);
        $fieldScopes = $privilegeService->getFieldScope();

        $groupFieldList = [];
        foreach ($fullFieldList as $item)
        {
            foreach ($item as $field)
            {
                $id = ($pos = strpos($field['id'], '.')) === false ? $field['id'] : substr($field['id'], $pos + 1);
                $existKey = "{$field['type']}-{$id}";
                if (isset($fieldScopes[$existKey]) && $fieldScopes[$existKey] == PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_DISABLE) {
                    continue;
                }
                
                $field['field_type'] = $field['field_type'] ?? 0;
                $field['relation_field_type'] = $field['relation_field_type'] ?? 0;
                if (!in_array($field['type'],[\Constants::TYPE_OPPORTUNITY, \Constants::TYPE_COMPANY]) || $field['field_type'] == CustomFieldService::FIELD_TYPE_IMAGE || $field['relation_field_type'] == CustomFieldService::FIELD_TYPE_IMAGE)
                {
                    continue;
                }
                $groupFieldList[$field['type']][] = $field;
            }
        }

        $group_info = [\Constants::TYPE_OPPORTUNITY => '商机信息', \Constants::TYPE_COMPANY => '客户信息'];

        foreach ($group_info as $type => $groupName)
        {
            $result[] = [
                'group_type' => $type,
                'group_name' => $groupName,
                'group_list' => $groupFieldList[$type] ?? [],
            ];
        }
        return $result;
    }

    public static function batchRemark($clientId, $userId, $params)
    {
        if (empty($params['opportunity_ids'])) {
            return 0;
        }

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $opportunityIdsSql = implode(",", $params['opportunity_ids']);
        $sql = "select opportunity_id,company_id from tbl_opportunity where enable_flag = 1 and client_id = {$clientId} and opportunity_id in ({$opportunityIdsSql}) and company_id > 0";
        $opportunityList = $db->createCommand($sql)->queryAll();

        if (!$opportunityList) {
            return 0;
        }

        $opportunityIds = [];
        $companyIds = [];
        $trailData['data'] = [];
        foreach($opportunityList as $index => $opportunity){
            $opportunityIds[$index] = $opportunity['opportunity_id'];
            $companyIds[$index] = $opportunity['company_id'];
            $trailData['data'][$index] = [
                'content'   => $params['content'],
                'file_ids'  => $params['file_ids'],
                'address'   => '',
                'longitude' => 0,
                'latitude'  => 0,
                'at_list'   => ($params['at_users'] ?? []) ? ['at_users' => $params['at_users']] : [],
            ];
        }

        $customer_ids = [];
        if(!empty($params['customer_id'])){
            foreach ($opportunityIds as $index => $id) {
                $customer_ids[$index] = [$params['customer_id']];
            }
        }

        $createTime = !empty($params['remark_time']) ? $params['remark_time'] : date('Y-m-d H:i:s');
        $trail = new \common\library\trail\events\BatchTrailEvents();
        $trail->setType($params['remark_type']);
        $trail->setClientId($clientId);
        $trail->setCreateUser($userId);
        $trail->setUserId($userId);
        $trail->setOpportunityId($opportunityIds);
        $trail->setCompanyId($companyIds);
        $trail->setCustomerId($customer_ids);
        $trail->setData($trailData['data']);
        $trail->setCreateTime($createTime);
        $trail->setSkipValidatorMap([$params['remark_type']]);
        $trail->run();


        \common\library\trail\Helper::resetCompanyLastTrailId($clientId, $companyIds);
        //更新最联系时间
        \common\library\customer\Helper::updateRemarkTime($clientId, $userId, $companyIds, 0,$params['remark_type'], '');
		\common\library\opportunity\Helper::updateTrailTime($clientId, $userId, $opportunityIds);

		//行为记录
        $behavior = new BehaviorService();
        $behavior->setReferId($opportunityIds);
        $behavior->setType(\common\library\behavior\Helper::convertOpportunityTrailType($params['remark_type']));
        $behavior->add();
        \StatisticsService::dynamicRemarkAdd($clientId, $userId, count($opportunityIds));

        return count($opportunityIds);
    }

    /**
     * 获取客户的商机统计数据（平均销售周期、赢单率）
     * @param $userId
     * @param $clientId
     * @param $companyIds
     * @return array
     */
    public static function getOpStatisticsData($userId, $clientId, $companyIds)
    {
        $list = new OpportunityList($clientId);
        $list->setCompanyIds($companyIds);
        $list->setViewingUserId($userId);
        $list->setShowAll(1);
        $list->setSkipPermissionCheck(true);
        $list->setFields(['opportunity_id','company_id','stage_type','create_time','succeed_time']);
        $opportunityList = $list->find();

        $statisticsMap = [];

        foreach($opportunityList as $opportunity) {
            $companyId = $opportunity['company_id'] ?? 0;
            $win_flag = $opportunity['stage_type'] == \common\library\setting\library\stage\Stage::STAGE_WIN_STATUS ? true : false;

            if (!array_key_exists($companyId, $statisticsMap)) {
                $statisticsMap[$companyId]['opportunity_count'] = 1;
                $statisticsMap[$companyId]['sale_total_time'] = $win_flag ? (strtotime($opportunity['succeed_time']) - strtotime($opportunity['create_time'])) : 0;
                $statisticsMap[$companyId]['win_count'] = $win_flag ? 1 : 0;
                $statisticsMap[$companyId]['avg_sale_period'] = $win_flag ? \Util::formatTimeToHours($statisticsMap[$companyId]['sale_total_time']) : 0;
                $statisticsMap[$companyId]['win_scale'] = $win_flag ? '100%' : '0%';
            } else {
                $statisticsMap[$companyId]['opportunity_count'] += 1;
                if ($win_flag) {
                    $statisticsMap[$companyId]['sale_total_time'] += strtotime($opportunity['succeed_time']) - strtotime($opportunity['create_time']);
                    $statisticsMap[$companyId]['win_count'] += 1;
                }
                $opportunity_count = $statisticsMap[$companyId]['opportunity_count'];
                $win_count = $statisticsMap[$companyId]['win_count'] ?? 0;
                $win_sale_total_time = $statisticsMap[$companyId]['sale_total_time'] ?? 0;
                $statisticsMap[$companyId]['avg_sale_period'] = $win_count > 0 ? \Util::formatTimeToHours((int)($win_sale_total_time / $win_count)) : 0;
                $statisticsMap[$companyId]['win_scale'] = round($win_count/$opportunity_count * 100,2).'%';
            }
        }

        return $statisticsMap;
    }

    /**
     * @throws \CDbException
     * @throws \ProcessException
     * @throws \CException
     */
    public static function remarkRelateBusinessByMail($clientId, $userId, $params)
    {
        $mailIds = $params['mail_ids'];
        $opportunityIds = $customerIds = $companyIds = [];
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $opportunityId = $params['opportunity_id'];
        $sql = "select company_id from tbl_opportunity where enable_flag = 1 and client_id = {$clientId} and opportunity_id = {$opportunityId} and company_id > 0";
        $result = $db->createCommand($sql)->queryRow();
        if (!$result) {
            return 0;
        }

        $companyId = $result['company_id'];

        foreach ($mailIds as $index => $mailId) {
            $opportunityIds[$index] = $opportunityId;
            $companyIds[$index] = $companyId;
            $customerIds[$index] = [$params['customer_id']];
        }

        $trailData['data'] = [];
        foreach($opportunityIds as $index => $id){
            $trailData['data'][$index] = [
                'content'   => $params['content'],
                'file_ids'  => $params['file_ids'],
                'address'   => '',
                'longitude' => 0,
                'latitude'  => 0,
            ];
        }

        $createTime = !empty($params['remark_time']) ? $params['remark_time'] : date('Y-m-d H:i:s');
        $trail = new \common\library\trail\events\BatchTrailEvents();
        $trail->setType(TrailConstants::TYPE_REMARK_RELATE_MAIL);
        $trail->setClientId($clientId);
        $trail->setCreateUser($userId);
        $trail->setUserId($userId);
        $trail->setOpportunityId($opportunityIds);
        $trail->setCompanyId($companyIds);
        $trail->setCustomerId($customerIds);
        $trail->setReferId($mailIds);
        $trail->setData($trailData['data']);
        $trail->setCreateTime($createTime);
        $trail->setSkipValidatorMap([TrailConstants::TYPE_REMARK_RELATE_MAIL]);
        $trail->run();


        \common\library\trail\Helper::resetCompanyLastTrailId($clientId, $companyId);
        //更新最联系时间
        \common\library\customer\Helper::updateRemarkTime($clientId, $userId, $companyId, $params['customer_id'],TrailConstants::TYPE_REMARK_RELATE_MAIL, '');

        //行为记录
        $behavior = new BehaviorService();
        $behavior->setReferId($opportunityIds);
        $behavior->setType(\common\library\behavior\Helper::convertOpportunityTrailType(TrailConstants::TYPE_REMARK_RELATE_MAIL));
        $behavior->add();
        \StatisticsService::dynamicRemarkAdd($clientId, $userId, count($opportunityIds));

        return count($opportunityIds);
    }


    /**
     * 获取所有阶段设置的必填字段（如果该阶段没有设置，则使用系统默认设置）
     * @param $client_id
     * @return array
     */
    public static function getAllStageRequireSetting($client_id)
    {
        $result = [];
        $customer_field_list = new FieldList($client_id);
        $customer_field_list->setType(\Constants::TYPE_OPPORTUNITY);
        $customer_field_list->setGroupId(CustomFieldService::OPPORTUNITY_GROUP_BASIC);
        $customer_field_list->setRequire(true);
        $require_field_list = $customer_field_list->find();
        $require_field_arr = array_column($require_field_list, 'id');

        $api = new StageApi($client_id);
        $stageList = $api->listAll();
        $flowApi = new \common\library\setting\library\flow\FlowApi($client_id);
        $flowList = $flowApi->listAll();
        $requireFlowMap = array_column($flowList, 'require_flag', 'flow_id');

        foreach ($stageList as $stage) {
            $require_flag = $requireFlowMap[$stage['flow_id']] ?? 0;
            $result[$stage['stage_id']] = (!empty($stage['require_field_setting']) && $require_flag) ? array_intersect($stage['require_field_setting'], $require_field_arr) : $require_field_arr;
        }
        return $result;
    }

    /**
     * 检查是否缺少必填字段
     * @param $requiredFieldMap
     * @param $data
     * @param $userId
     * @param $amount_can_edit_flag
     * @return array
     */
    public static function checkMissingRequiredField($requiredFieldMap, $data, $clientId, $userId, $amount_can_edit_flag, $stage)
    {
        $result = false;
        $missField = [];

        $stageApi = new StageApi($clientId);
        $stage = $stageApi->find($stage);

        //非输单阶段不需要判断输单原因和输单描述
        if (isset($stage['type']) && $stage['type'] != Stage::STAGE_FAIL_STATUS) {
            $requiredFieldMap = array_values(array_diff($requiredFieldMap, ['fail_type', 'fail_remark']));
        }
        //过滤掉部门字段 部门我的企业 0值处理
        $requiredFieldMap = array_values(array_diff($requiredFieldMap, ['department']));

        foreach ($requiredFieldMap as $fieldId) {
            switch ($fieldId) {
                case 'cus_tag':
                    if (empty($data['tag'][$userId]) && empty($data['client_tag_list'])) {
                        $missField[] = $fieldId;
                        $result = true;
                        break;
                    }
                    break;
                case 'amount':
                    if ($amount_can_edit_flag && empty($data['amount'])) {
                        $missField[] = $fieldId;
                        $result = true;
                        break;
                    }
                    break;
                default:
                    if (empty($data[$fieldId]) && empty($data['external_field_data'][$fieldId])) {
                        $missField[] = $fieldId;
                        $result = true;
                        break;
                    }
                    break;
            }
        }
        return [$result,$missField];
    }

    public static function getMissingRequiredField($opportunity_id, $to_stage, $client_id, $user_id)
    {
        $opportunity = new \common\library\opportunity\Opportunity($client_id, $opportunity_id);
        if (!$opportunity->isExist()) {
            throw new \RuntimeException(\Yii::t('opportunity', 'Opportunity does not exist'));
        }
        $data = $opportunity->getAttributes();

        $stageRequiredFieldMap = \common\library\opportunity\Helper::getAllStageRequireSetting($client_id);
        $requiredFieldMap = $stageRequiredFieldMap[$to_stage] ?? [];

        $amount_can_edit_flag = true;
        $client = \common\library\account\Client::getClient($client_id);
        $clientSetting = $client->getSettingAttributes([\common\library\account\Client::SETTING_KEY_OPPORTUNITY_AUTO_SYNC_AMOUNT_SWITCH,\common\library\account\Client::SETTING_KEY_OPPORTUNITY_AUTO_SYNC_AMOUNT_REFER_TYPE]);
        $opportunity_sync_amount_switch = $clientSetting[\common\library\account\Client::SETTING_KEY_OPPORTUNITY_AUTO_SYNC_AMOUNT_SWITCH] ?? false;
        if ($opportunity_sync_amount_switch && ($data['auto_summary_flag'] ?? false)) {
            $amount_can_edit_flag = false;
        }

        return \common\library\opportunity\Helper::checkMissingRequiredField($requiredFieldMap, $data, $client_id,$user_id, $amount_can_edit_flag,$to_stage);
    }


    /**
     * 检查创建订单的条件
     * @return bool
     */
    public static function checkCreateOrderCondition($stage, $client_id, $user_id ,$opportunity_id): bool
    {
        if (!$stage) {
            return false;
        }

        $list = new \common\library\invoice\OrderList($user_id);
        $list->setViewingUserId($client_id);
        $list->setAlias('t1');
        $list->paramsMapping(['opportunity_id' => $opportunity_id]);
        $list = \common\library\invoice\Helper::HandleOrderScene('opportunity', $list);
        [$count, , ]= $list->summaryOrder();
        if ($count > 0) {
            return false;
        }

        $client = \common\library\account\Client::getClient($client_id);
        $values = $client->getSettingAttributes([
            \common\library\account\Client::SETTING_KEY_OPPORTUNITY_STAGE_CREATE_ORDER_SWITCH,
            \common\library\account\Client::SETTING_KEY_OPPORTUNITY_STAGE_CREATE_ORDER_CONFIG
        ]);
        $create_order_switch = $values[\common\library\account\Client::SETTING_KEY_OPPORTUNITY_STAGE_CREATE_ORDER_SWITCH] ?? 0;
        $create_order_config = $values[\common\library\account\Client::SETTING_KEY_OPPORTUNITY_STAGE_CREATE_ORDER_CONFIG] ?? '{}';
        $create_order_config = json_decode($create_order_config,true);
        $stage_create_order_config = array_merge(...$create_order_config);

        if (!$create_order_switch || empty($create_order_config) || !in_array($stage, $stage_create_order_config)) {
            return false;
        }
        return true;
    }

    public static function buildSubmitData($clientId,$data, $stageId, $extParams): array
    {
        // 数据准备
        $with_fail_field = false;
        $department = $data['department'] ?? -99;
        unset($data['department']);
        $data['opportunity']['stage'] = $stageId;
        $stage = \common\library\opportunity\stage\Helper::getOneStage($clientId, $stageId);
        if (!$stage){
            throw new \RuntimeException(\Yii::t('opportunity', 'Sales stage does not exist'));
        }
        switch ($stage['type']) {
            //改为赢单
            case OpportunityStage::STAGE_WIN_STATUS:
                $extParams = json_decode($extParams,1);
                if (isset($extParams['trail_active_flag'])) {
                    $data['opportunity']['trail_active_flag'] = $extParams['trail_active_flag'];
                }
                if (!empty($extParams['account_date'])) {
                    $data['opportunity']['account_date'] = $extParams['account_date'];
                }
                break;
            //改为输单
            case OpportunityStage::STAGE_FAIL_STATUS:
                $with_fail_field = true;
                $extParams = json_decode($extParams,1);
                if (isset($extParams['fail_type'])) {
                    $data['opportunity']['fail_type'] = $extParams['fail_type'];
                }
                if (isset($extParams['fail_remark'])) {
                    $data['opportunity']['fail_remark'] = $extParams['fail_remark'];
                }
                break;
        }
        return [$data, $department, $with_fail_field];
    }

    // 新建客户添加客户id统计
    public static function opportunityWorkReportStatisticsAdd($clientId, $userId, $id, $date = null)
    {
        $timeType = 'day';
        $key = 'opportunity_add_count';
        if ($date === null)
            $date = date('Ymd');

        $sql = "INSERT INTO tbl_statistic_work_report as t1 (client_id, user_id, date, time_type, key, ids)
VALUES ($clientId, $userId, '{$date}', '{$timeType}', '{$key}', '{{$id}}')
ON CONFLICT (client_id, user_id, time_type, key, date) DO UPDATE SET ids=t1.ids||excluded.ids";

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $db->createCommand($sql)->execute();
    }
    
    public static function buildUpdateScopeUsers(int $clientId, array $row, $returnAsPgStr = false): string|array|null
    {
        $scopeUserService = new ScopeUserFieldService($clientId, new OpportunityMetadata($clientId));
        $result = $scopeUserService->buildScopeUserIds([$row]);
        \LogUtil::info('[buildUpdateScopeUsersSql] opportunity_scope_user_ids, result: ' . json_encode($result));
        
        $scopeUsers = reset($result)['scope_user_ids'] ?? null;
        if (is_null($scopeUsers))    {
            return null;
        }
        
        return $returnAsPgStr ? "'" . PgsqlUtil::formatArray($scopeUsers) . "'" : $scopeUsers;
    }
}
