<?php
/**
 * Created by PhpStorm.
 * User: troy.li
 * Date: 2018-07-30
 * Time: 5:08 PM
 */

namespace common\library\opportunity;

use common\library\cash_collection\CashCollection;
use common\library\cash_collection\CashCollectionReferListFilter;
use common\library\custom_field\CustomFieldService;
use common\library\customer\BaseCompanyList;
use common\library\customer_tag\TagTrait;
use common\library\customer_v3\company\list\CompanyList;
use common\library\list\ListConfig;
use common\library\orm\pipeline\formatter\PrivilegeFormatTask;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeFieldV2;
use common\library\search\criteria\Highlight;
use common\library\search\SearchApi;
use common\library\search\SearchParams;
use common\library\setting\item\Api;
use common\library\setting\library\common\GenderMetadata;
use common\library\setting\library\origin\OriginApi;
use common\library\statistics\ListReportSelect;
use common\library\util\PgsqlUtil;
use common\library\util\SqlBuilder;
use common\library\workflow\filter\WorkflowCriteriaBuilder;
use common\library\workflow\filter\WorkflowFilterRunner;
use common\library\workflow\WorkflowConstant;
use function DeepCopy\deep_copy;


/**
 * Class OpportunityList
 * @package common\library\opportunity
 *
 * @property OpportunityFormatter $formatter
 * @method OpportunityFormatter getFormatter()
 */
class OpportunityList extends \MysqlList
{
    
    use ListReportSelect, CashCollectionReferListFilter, TagTrait;

    /**
     * 跟进人
     */
    const USER_TYPE_HANDLER = 2;
    /**
     * 创建人
     */
    const USER_TYPE_CREATE_USER = 3;
    /**
     * 负责人
     */
    const USER_TYPE_MAIN_USER = 1;

    const SEARCH_FIELD_MAP = [
        'company_keyword'  => 'setCompanyKeyword',
        'serial_keyword'   => 'setSerialIdKeyword',
        'customer_keyword' => 'setCustomerKeyword',
        'customer_name' => 'setCustomerNameKeyword',
    ];

    //工作台统计
    const SCENE_STATISTIC_SUCCEED = 'statistic_succeed_opportunity';
    const SCENE_STATISTIC_FAIL = 'statistic_fail_opportunity';
    const SCENE_STATISTIC_PUSH = 'statistic_push_opportunity';

    const SCENE_SLIM_LIST = 'slim_list';

    protected $type;
    protected $currency;
    protected $origin;
    protected $failType;
    protected $minAmount = null;
    protected $maxAmount = null;
    protected $enableFlag = Opportunity::ENABLE_FLAG_OK;
    protected $trailActiveFlag;
    protected $externalSearchFields = [];
    protected $stage;
    protected $stageType;
    protected $fields;

    /**
     * @var array|integer 指定查看用户
     */
    protected $userId;
    /**
     * @var int 当前登录用户
     */
    protected $viewingUserId;
    protected $clientId;

    protected $keyword;
    protected $topKeyword;
    protected $keywordField;
    protected $serialIdKeyword;
    protected $companyKeyword;
    protected $customerKeyword;
    protected $companyIds;
    protected $customerIds;

    protected $showAll = false;
    protected $showAllPermission = PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_VIEW;
    protected $userType;
    protected $mainUser;
    protected $ignoreEnableFlag = false;
    protected $createUser;
    protected $department;

    protected $opportunityIds;
    protected $sortBySearchScore = false;
    protected $failStage;
    
    protected $greaterThanOpportunityId;

    protected $createStartDate;
    protected $createEndDate;
    protected $mainLeadId;
    protected $editStartDate;
    protected $productEditStartDate;
    protected $productEditEndDate;
    protected $editEndDate;
    protected $orderStartDate;
    protected $orderEndDate;
    protected $trailStartDate;
    protected $trailEndDate;
    protected $accountStartDate;
    protected $accountEndDate;
    protected $leadId;

    protected $skipPermissionCheck = false;
    protected $createType;
    protected $customerNotEmpty = false;
    protected $join = '';
    protected $joinCustomer = false;
    protected $joinCompany = false;
    protected $alias = '';
    protected $customerAlias = '';
    protected $companyAlias = '';
    protected $historyAlias = 'toh';
    protected $flowId;
    protected $approvalStatus;
    protected $joinSalesFlow = false;
    protected $gender = 0;
    protected $post = '';

    protected $distinctFlag = false;

    protected $mainCustomerFlag;

    private $lastSql;
    protected $filterFreezeUserFlag = false;

	protected $stageEditStartDate;
	protected $stageEditEndDate;

    protected $originList;

    
    protected $tags;
    
    protected $tagMatchMode = BaseCompanyList::TAG_MATCH_MODE_COMPLETE;
    protected $tagUserId = null;
    
    

    protected $disableFlag = null;

    protected $referFieldFilters;
    protected $advancedSearchFilters;
    protected $criteriaType;
    protected $filters;
    protected $criteria;

    protected $paramIsSet = false;
    protected $nameOrSerialKeyword;
    protected $customerContact = [];
    protected $filterEmptyCustomerTel = false;
    protected $suspectedInvalidFlag;

    /**
     * 默认仅搜索 商机名称、商机编号、客户名称、联系人邮箱、关联联系人
     * @var array 搜索的字段
     */
    protected $searchFields = \common\library\search\SearchConstant::OPPORTUNITY_FIELDS_TOP_SEARCH;
    //顶部搜索 客户/联系人 数量限制
    protected $topSearchLimit = 500;

    public function __construct($clientId, $userId = 0)
    {
        $this->clientId = $clientId;

        $this->formatter = new OpportunityFormatter($this->clientId);
        $userId && $this->setViewingUserId($userId);
    
        $this->initTagTrait($clientId);
    }

    /**
     * @return mixed
     */
    public  function getLastSql()
    {
        return $this->lastSql;
    }

    /**
     * @return mixed
     */
    public function getViewingUserId()
    {
        if (!$this->viewingUserId) {
            throw new \RuntimeException('未指定用户');
        }
        return $this->viewingUserId;
    }


    /**
     * @param bool $distinctFlag
     */
    public function setDistinctFlag(bool $distinctFlag)
    {
        $this->distinctFlag = $distinctFlag;
    }

    /**
     * @param mixed $viewingUserId
     */
    public function setViewingUserId($viewingUserId)
    {
        $this->viewingUserId = $viewingUserId;
        $this->formatter->setUserId($viewingUserId);
    }

    public function setDisableFlag($disableFlag)
    {
        $this->disableFlag = $disableFlag ? Opportunity::DISABLE_FLAG_TRUE: Opportunity::DISABLE_FLAG_FALSE;
    }

    /**
     * @param bool $filterFreezeUserFlag
     */
    public function setFilterFreezeUserFlag(bool $filterFreezeUserFlag): void
    {
        $this->filterFreezeUserFlag = $filterFreezeUserFlag;
    }

    public function setGreaterThenOpportunityId($greaterThanId)
    {
        $this->greaterThanOpportunityId = $greaterThanId;
    }

    /**
     * @param bool $skipPermissionCheck
     */
    public function setSkipPermissionCheck(bool $skipPermissionCheck)
    {
        $this->skipPermissionCheck = $skipPermissionCheck;
        $this->getFormatter()->setIgnorePrivilege($skipPermissionCheck);
    }
    
    public function getSkipPrivilege(): bool
    {
        return $this->skipPermissionCheck;
    }

    /**
     * @param mixed $createUser
     */
    public function setCreateUser($createUser)
    {
        $this->createUser = $this->transToArray($createUser, 'is_numeric');
    }

    public function setUserType($userType)
    {
        if (!empty($userType)) {
            $this->userType = $this->transToArray($userType, 'is_numeric');
        }
    }

    public function setAsMainUser()
    {
        $this->userType = [static::USER_TYPE_MAIN_USER];
    }

    public function setAsHandler()
    {
        $this->userType = [static::USER_TYPE_HANDLER];
    }

    public function setMainUser($mainUser)
    {
        $this->mainUser = $mainUser;
    }

    public function setAsFollower()
    {
        $this->userType = [static::USER_TYPE_MAIN_USER, static::USER_TYPE_HANDLER];
    }


    public function setType($type)
    {
        $this->type = $this->transToArray($type, function ($value) {
            return in_array($value, Helper::getTypeMap(true));
        });
    }

    public function setCurrency($currency)
    {
        $this->currency = $this->transToArray($currency);
    }

    public function setOrigin($origin)
    {
        $origin = $this->transToArray($origin, 'is_numeric');

        !empty($origin) && $this->setOriginList((array)$origin);
    }

    public function setLeadId($leadId)
    {
        $this->leadId = $leadId;
    }

    public function setFailType($failType)
    {
        $this->failType = $this->transToArray($failType, 'is_numeric');
    }

    public function setCustomerIdNotEmpty($flag = true)
    {
        $this->customerNotEmpty = $flag;
    }

    /**
     * @param mixed $sortBySearchScore
     */
    public function setSortBySearchScore($sortBySearchScore)
    {
        $this->sortBySearchScore = $sortBySearchScore;
    }

    /**
     * @param mixed $opportunityIds
     */
    public function setOpportunityIds($opportunityIds)
    {
        $this->opportunityIds = $this->transToArray($opportunityIds, 'self::isPositiveNumeric');
    }
    
    public function setIds($ids)
    {
        $this->setOpportunityIds($ids);
    }

    /**
     * @param mixed $trailActiveFlag
     */
    public function setTrailActiveFlag(int $trailActiveFlag)
    {
        $this->trailActiveFlag = in_array($trailActiveFlag,
            [Opportunity::TRAIL_ACTIVE_FLAG_OK, Opportunity::TRAIL_ACTIVE_FLAG_DISABLE])
            ? $trailActiveFlag
            : null;
    }

    public function setAmount($minAmount, $maxAmount)
    {
        $this->minAmount = is_numeric($minAmount) ? $minAmount : 0;
        $this->maxAmount = is_numeric($maxAmount) ? $maxAmount : 0;
    }

    public function setEnableFlag($enableFlag)
    {
        $this->enableFlag = $enableFlag ? Opportunity::ENABLE_FLAG_OK : Opportunity::ENABLE_FLAG_DELETE;
    }

    public function setIgnoreEnableFlag()
    {
        $this->ignoreEnableFlag = true;
    }

    public function setExternalFields($type, $externalFields)
    {
        $this->externalSearchFields[$type] = $externalFields;
    }

    public function setStage($stage)
    {
        $this->stage = $this->transToArray($stage, 'self::isPositiveNumeric');
    }


    /**
     * @param mixed $companyIds
     */
    public function setCompanyIds($companyIds)
    {
        $this->companyIds = $this->transToArray($companyIds, 'self::isPositiveNumeric');
    }

    public function setFields($fields)
    {
        if (is_array($fields)) {
            $fields = implode(',', $fields);
        }

        $this->fields = $fields;
    }

    public function setOrderBy($orderBy, $isAlias = true)
    {
        parent::setOrderBy($orderBy);
        $alias = $isAlias ? $this->alias : '';
        $this->orderBy = array_map(function ($orderBy) use ($alias) {
            return implode(',', array_map(function ($orderField) use ($alias) {
                $orderField = trim($orderField);
                return empty($alias) ? $orderField : "$alias.$orderField";
            }, explode(',', $orderBy)));
        }, $this->orderBy);
    }

    /**
     * @param mixed $customerIds
     */
    public function setCustomerIds($customerIds)
    {
        $this->customerIds = $this->transToArray($customerIds, 'self::isPositiveNumeric');
    }

    public function setFailStage($stage)
    {
        $this->failStage = $this->transToArray($stage, 'self::isPositiveNumeric');
    }

    public function setStageType($stageType)
    {
        $this->stageType = $this->transToArray($stageType, 'self::isPositiveNumeric');
    }

    /**
     * @param mixed $accountEndDate
     */
    public function setAccountEndDate($accountEndDate)
    {
        $this->accountEndDate = $accountEndDate;
    }

    /**
     * @param mixed $flowId
     */
    public function setFlowId($flowId)
    {
        $this->flowId = $this->transToArray($flowId, 'self::isPositiveNumeric');
    }

    /**
     * @param mixed $approvalStatus
     */
    public function setApprovalStatus($approvalStatus)
    {
        $this->approvalStatus = $this->transToArray($approvalStatus, 'self::isPositiveNumeric');
    }

    /**
     * @param mixed $createStartDate
     */
    public function setCreateStartDate($createStartDate)
    {
        $this->createStartDate = $createStartDate;
    }

    /**
     * @param mixed $editStartDate
     */
    public function setEditStartDate($editStartDate)
    {
        $this->editStartDate = $editStartDate;
    }

    /**
     * @param mixed $accountStartDate
     */
    public function setAccountStartDate($accountStartDate)
    {
        $this->accountStartDate = $accountStartDate;
    }

    /**
     * @param mixed $trailStartDate
     */
    public function setTrailStartDate($trailStartDate)
    {
        $this->trailStartDate = $trailStartDate;
    }

    /**
     * @param $productEditStartDate
     */
    public function setProductEditStartDate($productEditStartDate)
    {
        $this->productEditStartDate = $productEditStartDate;
    }

    /**
     * @param $productEditEndDate
     */
    public function setProductEditEndDate($productEditEndDate)
    {
        $this->productEditEndDate = $productEditEndDate;
    }

    /**
     * @param mixed $trailEndDate
     */
    public function setTrailEndDate($trailEndDate)
    {
        $this->trailEndDate = $trailEndDate;
    }

    /**
     * @param mixed $orderEndDate
     */
    public function setOrderEndDate($orderEndDate)
    {
        $this->orderEndDate = $orderEndDate;
    }

    /**
     * @param mixed $orderStartDate
     */
    public function setOrderStartDate($orderStartDate)
    {
        $this->orderStartDate = $orderStartDate;
    }

    /**
     * @param mixed $editEndDate
     */
    public function setEditEndDate($editEndDate)
    {
        $this->editEndDate = $editEndDate;
    }

    /**
     * @param mixed $createEndDate
     */
    public function setCreateEndDate($createEndDate)
    {
        $this->createEndDate = $createEndDate;
    }

    /**
     * @param mixed $department
     */
    public function setDepartment($department)
    {
        $this->department = $department;
    }


    /**
     * @param mixed $userId
     */
    public function setUserId($userId)
    {
        if (!empty($userId)) {
            $this->userId = $this->transToArray($userId, 'is_numeric');
        }
    }

    /**
     * @param boolean $showAll
     * @param string  $permission
     */
    public function setShowAll(bool $showAll, $permission = PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_VIEW)
    {
        $this->showAll = $showAll;
        $this->showAllPermission = $permission;
    }

    public function setMainLeadId($mainLeadId)
    {
        if ((is_array($mainLeadId) && count($mainLeadId = array_filter($mainLeadId, 'is_numeric'))) || is_numeric($mainLeadId)) {
            $this->mainLeadId = $mainLeadId;
        } else {
            $this->mainLeadId = null;
        }
    }

    /**
     * @param mixed  $keyword
     * @param string $keywordField
     */
    public function setKeyword($keyword, $keywordField = 'keyword')
    {
        if (isset(self::SEARCH_FIELD_MAP[$keywordField])) {
            $keywordMethod = self::SEARCH_FIELD_MAP[$keywordField];
            $this->$keywordMethod($keyword);
        } else {
            $this->keyword = trim($keyword);
        }
        $this->setSortBySearchScore(true);
    }

    public function setTopKeyword($topKeyword)
    {
        $this->topKeyword = trim($topKeyword);
        $this->setSortBySearchScore(true);
    }

    /**
     * @param mixed $companyKeyword
     */
    public function setCompanyKeyword($companyKeyword)
    {
        $this->companyKeyword = trim($companyKeyword);
    }

    /**
     * @param mixed $serialIdKeyword
     */
    public function setSerialIdKeyword($serialIdKeyword)
    {
        $serialIdKeyword = trim($serialIdKeyword);
        if (is_numeric($this->serialIdKeyword)) {
            $this->serialIdKeyword = $serialIdKeyword;
        } else {
            $this->serialIdKeyword = OpportunityFormatter::trimSerialId($serialIdKeyword);
        }
    }

    /**
     * @param mixed $customerKeyword
     */
    public function setCustomerKeyword($customerKeyword)
    {
        $this->keywordField = 'email';
        $this->customerKeyword = trim($customerKeyword);
    }

    public function setCustomerNameKeyword($customerKeyword)
    {
        $this->keywordField = 'name';
        $this->customerKeyword = trim($customerKeyword);
    }

    /**
     * @param $createType
     */
    public function setCreateType($createType)
    {
        if (! empty($createType)) {
            $this->createType = $createType;
        }
    }

    public function setCustomerMainCustomerFlag($mainCustomerFlag)
    {
        $this->mainCustomerFlag = $mainCustomerFlag;
    }
    
    public function setGender($gender) {
        
        if (in_array($gender, array_keys(GenderMetadata::getExtraDataMap(\Constants::TYPE_COMPANY)))) {
            
            $this->gender = $gender;
        }
    }

    public function setPost($post)
    {
        $this->post = $post;
    }

    /**
     * @return int
     */
    public function getPinType()
    {
        return \Pin::TYPE_OPPORTUNITY;
    }

    public function setNameOrSerialKeyword($nameOrSerialKeyword)
    {
        $this->nameOrSerialKeyword = $nameOrSerialKeyword;
    }



    public function buildUserSql($alias)
    {
        $sql = '';

        // 跳过用户&角色筛选
        if ($this->skipPermissionCheck) {
            return $sql;
        }

        if ($this->showAll) {
            $allManageableUserId = \common\library\privilege_v3\Helper::getPermissionScopeUser($this->clientId, $this->getViewingUserId(),
                $this->showAllPermission, $this->filterFreezeUserFlag, $this->filterFreezeUserFlag);
            if (\common\library\privilege_v3\Helper::CAN_MANAGE_ALL_USER === $allManageableUserId) {
                //查看全部且权限为全公司且不指定查看用户 跳过查询
                if (empty($this->userId)) {
                    return $sql;
                } else {
                    $userId = $this->userId;
                }
            } else {
                $allManageableUserId[] = $this->getViewingUserId();
                $userId = $allManageableUserId = array_unique($allManageableUserId);
                if ($this->userId) {
                    $userId = array_intersect($userId, $this->userId);
                    //可查看用户为空
                    if (empty($userId)) {
                        $sql = ' and 0=1';
                        return $sql;
                    }
                }
            }
        } else {
            $allManageableUserId = \common\library\privilege_v3\Helper::getPermissionScopeUser($this->clientId, $this->getViewingUserId(),
                $this->showAllPermission, $this->filterFreezeUserFlag, $this->filterFreezeUserFlag);
            $userId = $this->getViewingUserId();
        }

        $userField = $this->showAll ? 'user_id' : 'main_user';
        if (!empty($this->userType)) {
            if (count(array_diff($this->userType, [self::USER_TYPE_MAIN_USER])) == 0) {
                $userField = 'main_user';
            } elseif (count(array_diff($this->userType, [self::USER_TYPE_HANDLER])) == 0) {
                $userField = 'handler';
            } elseif (count(array_diff($this->userType, [self::USER_TYPE_CREATE_USER])) == 0) {
                $userField = '';
                if (! $this->createUser) {
                    $this->setCreateUser($userId);
                }
            } elseif (count(array_diff($this->userType, [self::USER_TYPE_MAIN_USER, self::USER_TYPE_HANDLER])) == 0) {
                $userField = 'user_id';
            } elseif (count(array_diff($this->userType,
                    [self::USER_TYPE_MAIN_USER, self::USER_TYPE_CREATE_USER])) == 0) {
                $userField = 'main_user';
                $this->setCreateUser($userId);
            } elseif (count(array_diff($this->userType, [self::USER_TYPE_HANDLER, self::USER_TYPE_CREATE_USER])) == 0) {
                $userField = 'handler';
                $this->setCreateUser($userId);
            } elseif (count(array_diff($this->userType,
                    [self::USER_TYPE_HANDLER, self::USER_TYPE_CREATE_USER, self::USER_TYPE_MAIN_USER])) == 0) {
                $userField = 'user_id';
                $this->setCreateUser($userId);
            }
        }

        $createUserSql = '';
        if (!empty($this->createUser)) {
            if (count($this->createUser) > 1) {
                $createUserSql = " {$alias}create_user in (" . implode(',', $this->createUser) . ") ";
            } else {
                $createUserId = current($this->createUser);
                $createUserSql = " {$alias}create_user = {$createUserId}";
            }
            //
            if (\common\library\privilege_v3\Helper::CAN_MANAGE_ALL_USER !== $allManageableUserId) {
                if (is_array($allManageableUserId)) {
                    if (count($allManageableUserId) > 1) {
                        $createUserSql = " ( {$alias}user_id && ARRAY[" . implode(',',
                                $allManageableUserId) . "]::bigint[] and $createUserSql )";
                    } else {
                        $createUserSql = " ( {$alias}user_id @> ARRAY[" . current($allManageableUserId) . "]::bigint[] and $createUserSql )";
                    }
                } else {
                    $createUserSql = " ( {$alias}user_id @> ARRAY[$allManageableUserId]::bigint[] and $createUserSql )";
                }
            }
        }

        $userSql = '';
        if ($userField == 'main_user') {
            if (is_array($userId)) {
                if (count($userId) > 1) {
                    $userSql = "{$alias}{$userField} in (" . implode(',', $userId) . ")";
                } else {
                    $userSql = "{$alias}{$userField}=" . current($userId);
                }
            } else {
                $userSql = "{$alias}{$userField}={$userId}";
            }
        } elseif (in_array($userField, ['handler', 'user_id'])) {
            if (is_array($userId)) {
                if (count($userId) > 1) {
                    $userSql = "{$alias}{$userField} && ARRAY[" . implode(',', $userId) . "]::bigint[]";
                } else {
                    $userSql = "{$alias}{$userField} @> ARRAY[" . current($userId) . "]::bigint[]";
                }
            } else {
                $userSql = "{$alias}{$userField} @> ARRAY[$userId]::bigint[]";
            }
        }

        if ($createUserSql) {
            if ($userSql) {
                $sql .= " and ( {$userSql} or {$createUserSql} )";
            } else {
                $sql .= " and {$createUserSql}";
            }
        } else {
            $sql .= " and {$userSql}";
        }

        return $sql;
    }

    protected function buildSearch($alias)
    {
        $sql = '';

        $companyIds = [];
        $customerIds = [];

        $companyQueryFilters = [];
        if ($this->companyKeyword) {
            $companyQueryFilters[] = [
                'search_field' => 'name',
                'operator' => WorkflowConstant::FILTER_OPERATOR_MATCH_PHRASE,
                'value' => $this->companyKeyword,
                'value_type' => ['slop' => 0],
            ];
        }
        if ($this->customerKeyword) {
            $companyQueryFilters[] = [
                'search_field' => $this->keywordField ?? 'email',
                'operator' => ($this->keywordField ?? 'email') == 'email' ? WorkflowConstant::FILTER_OPERATOR_EQUAL : WorkflowConstant::FILTER_OPERATOR_MATCH_PHRASE,
                'value' => $this->customerKeyword,
                'path' => 'customer_list',
            ];
        }

        $advancedSearchFilters = [];
        if (!empty($this->advancedSearchFilters)) {
            foreach ($this->advancedSearchFilters as $filter) {
                if (strpos($filter['field'], 'company_') === 0) {
                    $companyQueryFilters[] = [
                        'search_field' => str_replace('company_', '', $filter['field']),
                        'operator' => $filter['operator'],
                        'value' => $filter['value'],
                        'value_type' => ['slop' => 0],
                    ];
                } elseif (strpos($filter['field'], 'customer_list.') === 0) {
                    $companyQueryFilters[] = [
                        'search_field' => str_replace('customer_list.', '', $filter['field']),
                        'operator' => $filter['operator'],
                        'value' => $filter['value'],
                        'path' => 'customer_list',
                    ];
                } elseif ($filter['field'] == 'serial_id') {
                    $filter['value'] = OpportunityFormatter::trimSerialId($filter['value'] ?? '');
                    $advancedSearchFilters[] = $filter;
                } else {
                    $advancedSearchFilters[] = $filter;
                }
            }
        }

        if ($companyQueryFilters) {
            $companySearchParams = new SearchParams();
            $companySearchParams->setFilters($companyQueryFilters, $this->criteriaType ?? WorkflowConstant::CRITERIA_TYPE_OR);
            $companySearchParams->pagination(0, 10000);
            $companySearchParams->idOnly(true);
            if ($this->customerKeyword) {
                $companySearchParams->setHighlight((new Highlight())->setInnerHitHighlight(false));
                $companySearchResult = SearchApi::company($this->clientId)->listByParams($companySearchParams)['list'] ?? [];
                $companyIds = array_column($companySearchResult, '_id');
                $customerIds = array_reduce($companySearchResult, function ($carry, $item) {
                    $carry = array_merge($carry, array_column($item['_search_info']['inner_hits']['customer_list'] ?? [], 'customer_id'));
                    return $carry;
                }, []);
            } else {
                $companyIds = SearchApi::company($this->clientId)->setReturnIds(true)->listByParams($companySearchParams);
            }

            if (empty($companyIds)) {
                $sql .= " and 0=1";
            }
        }

//        $searchFlag = false;
//        $companyQueryFields = [];
//        if ($this->companyKeyword || $this->customerKeyword) {
//
//            $companySearcher = new CompanySearchList();
//            $companySearcher->setClientId($this->clientId);
//
//            if ($this->companyKeyword) {
//                $companyQueryFields['name'] = [
//                    'query' => strtolower($this->companyKeyword),
//                    'type'  => SearchList::MATCH_TYPE_PHRASE,
//                    'slop'  => 0
//                ];
//                $searchFlag = true;
//            }
//
//            if ($this->customerKeyword) {
//                $companyQueryFields['customer_list.email'] = [
//                    'query'    => strtolower($this->customerKeyword),
//                    'analyzer' => SearchConstant::BUILD_IN_ANALYZER_KEYWORD,
//                ];
//                $companySearcher->setHighlightFields(['customer_list.email']);
//                $companySearcher->setHighlightOptions(['require_field_match' => false]);
//                $companySearcher->setPreTags(['']);
//                $companySearcher->setPostTags(['']);
//                $companySearcher->setFilterHighlight(true);
//                $searchFlag = true;
//            }
//
//            if ($searchFlag) {
//                $companySearcher->setQueryFields($companyQueryFields);
//                if ($this->customerKeyword) {
//                    $companyResult = $companySearcher->find();
//                    $companyIds = array_column($companyResult, '_id');
//                    $customerIds = array_reduce($companyResult, function ($carry, $item) {
//                        $carry = array_merge($carry, array_column($item['customer_list'] ?? [], 'customer_id'));
//                        return $carry;
//                    }, []);
//                } else {
//                    $companyIds = $companySearcher->findIds();
//                }
//                if (empty($companyIds)) {
//                    $sql .= " and 0=1";
//                }
//            }
//        }

        $opportunityQueryFilters = [];
        $opportunityIds = [];
        foreach ($advancedSearchFilters as $filter) {
            $opportunityQueryFilters[] = [
                'search_field' => $filter['field'],
                'operator' => $filter['operator'],
                'value' => $filter['value'],
            ];
        }

        $opportunitySearchApi = SearchApi::opportunity($this->clientId);
        if ($this->keyword) {
            $opportunityQueryFilters[] = [
                'search_field' => 'name',
                'operator' => WorkflowConstant::FILTER_OPERATOR_MATCH,
                'value' => $this->keyword,
            ];
        }
        if ($this->serialIdKeyword) {
            $opportunityQueryFilters[] = [
                'search_field' => 'serial_id',
                'operator' => WorkflowConstant::FILTER_OPERATOR_MATCH,
                'value' => $this->serialIdKeyword,
            ];
        }
        if ($this->externalSearchFields) {
            [$searchSql, $opportunityExternalQueryFilters] = $opportunitySearchApi->analyseExternalField($this->externalSearchFields, $alias);
            if ($searchSql) {
                $sql .= $searchSql;
            } else {
                $opportunityQueryFilters = array_merge($opportunityQueryFilters, $opportunityExternalQueryFilters);
            }
        }

        if ($opportunityQueryFilters) {
            $opportunityIds = $opportunitySearchApi->listIds($opportunityQueryFilters);
            if (empty($opportunityIds)) {
                $sql .= " and 0=1";
            }
        }

//        $searchFlag = false;
//        $opportunityIds = [];
//        $opportunityQueryFields = [];
//        if ($this->serialIdKeyword || $this->keyword || count($this->externalSearchFields)) {
//            $opportunitySearcher = new OpportunitySearchList();
//            $opportunitySearcher->setClientId($this->clientId);
//
//            if ($this->serialIdKeyword) {
//                $opportunityQueryFields['serial_id'] = $this->serialIdKeyword;
//                $searchFlag = true;
//            }
//
//            if ($this->keyword) {
//                $opportunityQueryFields['name'] = $this->keyword;
//                $searchFlag = true;
//            }
//
//            if (!empty($opportunityQueryFields)) {
//                $opportunitySearcher->setQueryFields($opportunityQueryFields);
//                $opportunitySearcher->setQueryType(SearchList::BOOL_TYPE_MUST);
//            }
//
//            if (!empty($this->externalSearchFields)) {
//                $externalSearchFieldType = \Constants::TYPE_OPPORTUNITY;
//                $externalSearchFields = $this->externalSearchFields[$externalSearchFieldType] ?? [];
//
//                $opportunitySearcher->setExternalFields($externalSearchFieldType, $externalSearchFields);
//                //一旦自定义字段搜索，就只能使用and keyword
//                $opportunitySearcher->setQueryType(SearchList::BOOL_TYPE_MUST);
//                $analysedExternalFields = $opportunitySearcher->analyseExternalFields($externalSearchFieldType);
//                if (empty($analysedExternalFields)) {
//                    $searchFlag = false;
//                } else {
//                    $externalNeedSearch = count(array_filter($analysedExternalFields, function ($externalSearchField) {
//                        return 'term' != ($externalSearchField['match_type'] ?? '');
//                    }));
//                    if ($externalNeedSearch) {
//                        $searchFlag = true;
//                    } else {
//                        $externalSearchFieldMaps = array_column($analysedExternalFields, 'keyword', 'field_id');
//                        $existFieldIds = array_column($analysedExternalFields, 'field_id');
//                        $fieldTypeMap = array_column($analysedExternalFields, 'field_type', 'field_id');
//
//                        if (count($externalSearchFieldMaps)) {
//                            $externalFieldStringValueData = [];
//                            foreach ($externalSearchFieldMaps as $externalFieldKey => $externalFieldValue) {
//                                if (! in_array($externalFieldKey, $existFieldIds)) {
//                                    continue;
//                                }
//                                if (is_array($externalFieldValue)) {
//                                    $externalFieldValuesSql = [];
//                                    if (($fieldTypeMap[$externalFieldKey] ?? 0) == CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT) {
//                                        $sql .= " AND jsonb_exists_any({$alias}external_field_data->'{$externalFieldKey}', array['" . implode("','", $externalFieldValue) . "'])";
//                                    } else {
//                                        foreach ($externalFieldValue as $externalFieldValueEnum) {
//                                            $externalFieldValuesSql[] = " {$alias}external_field_data->>'{$externalFieldKey}' = '{$externalFieldValueEnum}' ";
//                                        }
//                                        $sql .= ' AND (' . implode(' OR ', $externalFieldValuesSql) . ")";
//                                    }
//                                } else {
//                                    $externalFieldStringValueData[$externalFieldKey] = " {$alias}external_field_data->>'{$externalFieldKey}' = '{$externalFieldValue}' ";
//                                }
//                            }
//                            if (count($externalFieldStringValueData)) {
//                                $sql .= " AND " . implode(" AND ", $externalFieldStringValueData);
//                            }
//                        } else {
//                            $sql .= " and 0=1";
//                        }
//                    }
//                }
//            }
//
//            if ($searchFlag) {
//                $opportunityIds = $opportunitySearcher->findIds();
//                if (empty($opportunityIds)) {
//                    $sql .= " and 0=1";
//                }
//            }
//        }

        return [$sql, $opportunityIds, $companyIds, $customerIds];
    }

    protected function buildTopSearch($alias)
    {
        $sql = ' AND (';

        $companyQueryFilters = [];
        $customerQueryFilters = [];
        $opportunityQueryFilters = [];
        $companyIds = [];
        $customerCompanyIds = [];
        $customerIds = [];
        $opportunityIds = [];

        foreach($this->searchFields as $searchField)
        {
            if (strpos($searchField, 'company_') === 0) {
                $filter = [
                    'search_field' => str_replace('company_', '', $searchField),
                    'operator' => WorkflowConstant::FILTER_OPERATOR_MATCH_PHRASE ,
                    'value' => $this->topKeyword,
                    'value_type' => ['slop' => 0],
                ];
                $companyQueryFilters[] = $filter;
                $customerQueryFilters[] = $filter;
            } elseif (strpos($searchField, 'customer_list.') === 0) {
                $customerQueryFilters[] = [
                    'search_field' => str_replace('customer_list.', '', $searchField),
                    'operator' => str_contains($searchField, 'email') !== false ? WorkflowConstant::FILTER_OPERATOR_EQUAL : WorkflowConstant::FILTER_OPERATOR_MATCH_PHRASE,
                    'value' => $this->topKeyword,
                    'path' => 'customer_list',
                ];
            } elseif($searchField == 'serial_id') {
                $serialIdKeyword = trim($this->topKeyword);
                if (!is_numeric($serialIdKeyword)) {
                    $serialIdKeyword = OpportunityFormatter::trimSerialId($serialIdKeyword);
                }
                $opportunityQueryFilters[] = [
                    'search_field' => $searchField,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_MATCH,
                    'value' => $serialIdKeyword,
                ];
            }else {
                $opportunityQueryFilters[] = [
                    'search_field' => $searchField,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_MATCH,
                    'value' => $this->topKeyword,
                ];
            }
        }

        if ($companyQueryFilters) {
            $companySearchParams = new SearchParams();
            $companySearchParams->setFilters($companyQueryFilters, WorkflowConstant::CRITERIA_TYPE_OR );
            $companySearchParams->pagination(0, 10000);
            $companySearchParams->idOnly(true);
            $companyIds = SearchApi::company($this->clientId)->setReturnIds(true)->listByParams($companySearchParams);
            unset($companySearchParams);
        }

        if ($customerQueryFilters) {
            $companySearchParams = new SearchParams();
            $companySearchParams->setFilters($customerQueryFilters, WorkflowConstant::CRITERIA_TYPE_OR );
            $companySearchParams->pagination(0, 10000);
            $companySearchParams->idOnly(true);
            $companySearchParams->setHighlight((new Highlight())->setInnerHitHighlight(false));
            $companySearchResult = SearchApi::company($this->clientId)->listByParams($companySearchParams)['list'] ?? [];
            $customerCompanyIds = array_column($companySearchResult, '_id');
            $customerIds = array_reduce($companySearchResult, function ($carry, $item) {
                $carry = array_merge($carry, array_column($item['_search_info']['inner_hits']['customer_list'] ?? [], 'customer_id'));
                return $carry;
            }, []);
        }


        if ($opportunityQueryFilters) {
            $opportunitySearchApi = SearchApi::opportunity($this->clientId);
            $opportunityIds = $opportunitySearchApi->listIds($opportunityQueryFilters,10000,0,[],WorkflowConstant::CRITERIA_TYPE_OR);
        }

        if (!empty($opportunityIds)) {
            $sql .= "{$alias}opportunity_id IN (". implode(',', $opportunityIds) .") OR ";
        }

        if (!empty($companyIds)) {
            $companyIds = array_slice($companyIds, 0, $this->topSearchLimit);
            $sql .= "{$alias}company_id IN (". implode(',', $companyIds) .") OR ";
        }

        if (!empty($customerIds)) {
            $customerIds = array_slice($customerIds, 0, $this->topSearchLimit);
            $customerIdString = implode(',', $customerIds);
            $sql .= "({$alias}company_id IN (". implode(',', $customerCompanyIds) .") AND {$alias}customer_id && ARRAY[$customerIdString]::bigint[]) OR ";
        }

        if (empty($companyIds) && empty($opportunityIds) && empty($customerIds)) {
            $sql = ' AND 1=0';
            return $sql;
        }
        // 去除字符串末尾的 "or"
        $sql = rtrim($sql, " OR");
        $sql .= ')';

        return $sql;
    }

    public function setAlias($alias)
    {
        $this->alias = $alias;
    }

    public function buildParams()
    {
        $entityId = 'opportunity_id';
        $alias = empty($this->alias) ? '' : $this->alias . '.';

        $params = [];
        $opportunityIds = null;
        $companyIds = null;
        $customerIds = null;

        if (!$this->ignoreEnableFlag) {
            if (!is_null($this->enableFlag)) {
                $sql = " {$alias}enable_flag=" . $this->enableFlag;
            } else {
                $sql = " {$alias}enable_flag=" . Opportunity::ENABLE_FLAG_OK;
            }
        } else {
            $sql = '1=1';
        }

        SqlBuilder::buildWhere($alias, 'type', $this->type, $sql, $params);

        if (!is_null($this->trailActiveFlag)) {
            $sql .= " and {$alias}trail_active_flag=" . $this->trailActiveFlag;
        }

        $userSql = $this->buildUserSql($alias);
        $sql .= $userSql;

        $hasKeyword = count(array_filter([
            $this->keyword,
            $this->companyKeyword,
            $this->customerKeyword,
            $this->serialIdKeyword,
        ]));

        if ($hasKeyword || !empty($this->externalSearchFields) || !empty($this->advancedSearchFilters)) {
            [$searchSql, $opportunityIds, $companyIds, $customerIds] = $this->buildSearch($alias);
            $sql .= $searchSql;
            if (empty($this->orderBy) && $this->sortBySearchScore && !empty($opportunityIds)) {
                $idsStr = json_encode(array_flip($opportunityIds));
                $this->setOrderBy("'{$idsStr}'::jsonb->{$entityId}::text", false);
                $this->setOrder('asc');
            }
        }

        if ($this->topKeyword) {
            $searchSql = $this->buildTopSearch($alias);
            $sql .= $searchSql;
        }

        //todo 调整顺序
        //搜索得到opportunityIds 和指定opportunityIds 取交集
        if (!is_null($this->opportunityIds)) {
            if (!empty($this->opportunityIds)) {
                $opportunityIds = is_null($opportunityIds)
                    ? $this->opportunityIds
                    : array_intersect($this->opportunityIds, $opportunityIds);
                if (empty($opportunityIds)) {
                    $sql .= ' and 1=0';
                }
            } else {
                $sql .= ' and 1=0';
            }
        }
        
        if ($this->greaterThanOpportunityId) {
            $sql .= " AND {$alias}opportunity_id > :greater_than_opportunity_id";
            $params[':greater_than_opportunity_id'] = $this->greaterThanOpportunityId;
        }

        //如果opportunityIds存在，就不需要clientId了，这样规划器才会用到opportunity_id的索引
        if (empty($opportunityIds)) {
            $sql .= " and {$alias}client_id=:client_id ";
            $params[':client_id'] = $this->clientId;
        } else {
            $sql .= " and {$alias}{$entityId} in (" . implode(',', $opportunityIds) . ")";
        }

        if (!empty($this->companyIds)) {
            $companyIds = empty($companyIds)
                ? $this->companyIds
                : array_intersect($this->companyIds, $companyIds);
            if (empty($companyIds)) {
                $sql .= ' and 1=0';
            } else {
                //商机名称商机编号不走es的情况
                if (!empty($this->nameOrSerialKeyword)) {
                    $word = $this->nameOrSerialKeyword;
                    $sql .= " AND ({$alias}name ILIKE :name or {$alias}serial_id ILIKE :serialId) ";
                    $params[':name'] = "%{$word}%";
                    $params[':serialId'] = "%{$word}%";
                }
            }
        }
        SqlBuilder::buildWhere($alias, 'company_id', $companyIds ?? [], $sql, $params);

        if (!empty($this->customerIds)) {
            $customerIds = is_null($customerIds)
                ? $this->customerIds
                : array_intersect($this->customerIds, $customerIds);
            if (empty($customerIds)) {
                $sql .= ' and 1=0';
            }
        } else {
            $customerIds = empty($customerIds) ? [] : $customerIds;
        }

        if (!empty($customerIds)) {
            if (is_array($customerIds)) {
                $customerIdString = implode(',', $customerIds);
                $sql .= " and {$alias}customer_id && ARRAY[$customerIdString]::bigint[]";
            } else {
                $sql .= " and {$alias}customer_id @> ARRAY[$customerIds]::bigint[]";
            }
        }

        if ($this->customerNotEmpty == true) {
            $sql .= " and {$alias}customer_id != '{}'";
        }

        if (!is_null($this->suspectedInvalidFlag)) {
            $sql .= " and {$alias}suspected_invalid_email_flag = $this->suspectedInvalidFlag";
        }

        if ($this->isPin) {
            $sql .= ' and ' . $this->buildPin($this->getViewingUserId());
        }

        if(!empty($this->pin_user_list)){
            $sql .= ' and ' . $this->buildPin($this->pin_user_list);
        }

        if (!is_null($this->disableFlag)) {
            $sql .= " and {$alias}disable_flag=" . $this->disableFlag;
        }

        SqlBuilder::buildWhere($alias, 'stage', $this->stage, $sql, $params);
        SqlBuilder::buildWhere($alias, 'flow_id', $this->flowId, $sql, $params);
        SqlBuilder::buildWhere($alias, 'stage_type', $this->stageType, $sql, $params);
        SqlBuilder::buildWhere($alias, 'origin', $this->origin, $sql, $params);
        SqlBuilder::buildWhere($alias, 'currency', $this->currency, $sql, $params);
        SqlBuilder::buildWhere($alias, 'fail_type', $this->failType, $sql, $params);
        SqlBuilder::buildWhere($alias, 'fail_stage', $this->failStage, $sql, $params);
        SqlBuilder::buildWhere($alias, 'create_type', $this->createType, $sql, $params);
        SqlBuilder::buildWhere($alias, 'approval_status', $this->approvalStatus, $sql, $params);
        SqlBuilder::buildWhere($alias, 'department', $this->department, $sql, $params);
        SqlBuilder::buildWhere($alias, 'main_lead_id', $this->leadId, $sql, $params);

        if (isset($this->originList)) {

            $sql .= " and {$alias}origin_list && ARRAY[" . implode(',', $this->originList) . "]::BIGINT[] ";
        }

        if ($this->minAmount > 0) {
            if ($this->maxAmount > 0) {
                $sql .= " and {$alias}amount between {$this->minAmount} and {$this->maxAmount}";
            } else {
                $sql .= " and {$alias}amount >= {$this->minAmount}";
            }
        } else {
            if ($this->maxAmount > 0) {
                $sql .= " and {$alias}amount <= {$this->maxAmount}";
            }
        }

        SqlBuilder::buildDateRange($alias, 'create_time', $this->createStartDate, $this->createEndDate, $sql, $params);
        SqlBuilder::buildDateRange($alias, 'order_time', $this->orderStartDate, $this->orderEndDate, $sql, $params);
        SqlBuilder::buildDateRange($alias, 'edit_time', $this->editStartDate, $this->editEndDate, $sql, $params);
        SqlBuilder::buildDateRange($alias, 'trail_time', $this->trailStartDate, $this->trailEndDate, $sql, $params);
        SqlBuilder::buildDateRange($alias, 'account_date', $this->accountStartDate, $this->accountEndDate, $sql, $params);
        SqlBuilder::buildDateRange($alias, 'product_edit_time', $this->productEditStartDate, $this->productEditEndDate, $sql, $params);
        SqlBuilder::buildDateRange($alias, 'stage_edit_time', $this->stageEditStartDate, $this->stageEditEndDate, $sql, $params);

        if ($this->isRecentSelected) {
            $condition = $this->buildRecentList($this->getViewingUserId(), \RecentSelect::TYPE_OPPORTUNITY);
            $sql .= ' and ' . $condition;
        }

        //main_customer_flag =1 查询主要联系人
        //main_customer_flag =0 查询全部
        //main_customer_flag = 2 查询非主要联系人
        if ($this->joinCustomer && $this->mainCustomerFlag) {

            $mainCustomerFlag = $this->mainCustomerFlag;
            if ($this->mainCustomerFlag == 2) {
                $mainCustomerFlag = 0;
            }
            $sql .= " AND $this->customerAlias.main_customer_flag = :main_customer_flag";
            $params[':main_customer_flag'] = $mainCustomerFlag;
        }
        if ($this->joinCustomer && !empty($this->customerContact)) {
            $contact = json_encode($this->customerContact);
            if ($this->filterEmptyCustomerTel) {
                $sql .= " and ({$this->customerAlias}.contact @> '[$contact]' or {$this->customerAlias}.tel_list != '{}')";
            } else {
                $sql .= " and {$this->customerAlias}.contact @> '[$contact]'";
            }
        }
        if($this->mainUser){
            SqlBuilder::buildWhere($alias, 'main_user', intval($this->mainUser ), $sql, $params);
        }
        //联合company 表查询，company 未被删除
        if ($this->joinCompany){
            $sql .= " AND $this->companyAlias.is_archive = :is_archive";
            $params[':is_archive'] = 1;
        }


        if ($this->mainLeadId !== null) {
            if (is_array($this->mainLeadId)) {
                $sql .= " and {$alias}main_lead_id in (" . implode(',', $this->mainLeadId) . ")";
            } else {
                $sql .= " and {$alias}main_lead_id=:main_lead_id";
                $params[':main_lead_id'] = $this->mainLeadId;
            }
        }
        if (!empty($this->gender)) {
            $sql .= " AND $this->customerAlias.gender = :gender";
            $params[':gender'] = $this->gender;
        }
        if (!empty($this->post)) {
            $sql .= " AND $this->customerAlias.post = :post";
            $params[':post'] = $this->post;
        }


        $sql .= $this->buildReportSelect('opportunity_id', $alias);

        // 通过flow_id 过滤
        if ($this->joinSalesFlow) {
            $flowIdMap = Api::flow($this->clientId, \Constants::TYPE_OPPORTUNITY)->getNameMap([]);
            if ($flowIdMap) {
                SqlBuilder::buildIntWhere($alias, 'flow_id', array_keys($flowIdMap), $sql, $params);
            }
        }

        $filters = $this->filters;
        if (!empty($this->filters)) {
            [$swarmSql, $swarmParams] = $this->buildParamsByFilters($filters, $this->criteriaType, $this->criteria, $alias, $this->viewingUserId ?: 0);
            if ($customerUserIdSql = $config['userIdSql'] ?? '') {
                $customerUserIdSql = $alias ? str_replace($alias, '', $customerUserIdSql) : $customerUserIdSql;
                $swarmSql = str_replace("from tbl_customer where  client_id={$this->clientId}", "from tbl_customer where  client_id={$this->clientId} {$customerUserIdSql}", $swarmSql);
            }
            if ($swarmSql) {
                $sql .= " and ($swarmSql)";
                $params = array_merge($params, $swarmParams);
            }
        }

        if (!empty($this->referFieldFilters)) {
            $companyList = new CompanyList($this->viewingUserId);
            $companyList->showAll(true);
            $companyList->setSkipPrivilege(true);
            $companyList->setOpportunityFlag(true);
            $companyList->setFilters($this->referFieldFilters, $this->criteriaType, $this->criteria);
            $companyList->setFields('company_id');
            $companyList->setLimit(10000);
            $companyList->setEnableListSearch(true);
            $companyList->setSortBySearchScore(false);
            $companyIds = array_column($companyList->find(), 'company_id');
            if (empty($companyIds) && $this->criteriaType == WorkflowConstant::CRITERIA_TYPE_AND) {
                $sql .= ' and 1=0';
            }elseif (!empty($companyIds)) {
                $sql .= " AND {$alias}company_id IN (". implode(',', $companyIds) .")";
            }
        }
    
        if (!empty($this->tags)) {
    
            $sql .=  $this->buildTagSql($alias, $this->tags, $this->tagMatchMode, ($this->tagUserId ?: $this->viewingUserId ?: \User::getLoginUser()->getUserId()));
        }
    
    
        return [$sql, $params];
    }

    public function initJoin()
    {
        $joins = [];
        $alias = $this->alias;
        $fields = ["{$alias}.*"];
        if ($this->joinCustomer) {
            $customerAlias = $this->customerAlias;
            $fields = ["{$alias}.*", "$customerAlias.customer_id as id","$customerAlias.email", "$customerAlias.suspected_invalid_email_flag","$customerAlias.forbidden_flag","$customerAlias.name", "$alias.name as opportunity_name", "$customerAlias.contact", "$customerAlias.post"];
            $joins[] = "left join tbl_customer as $customerAlias on $customerAlias.customer_id=any($alias.customer_id)";

            if ($this->getEnableCashCollectionReferFilter()) {
                $fields = ["{$alias}.*", "$customerAlias.email", "$customerAlias.name", "$alias.name as opportunity_name"];
                $this->setDistinctFlag(true);
                $joins[] = $this->buildCashCollectionJoinSql($this->alias, $this->clientId);
            }

        } else {
            if ($this->getEnableCashCollectionReferFilter()) {
                $fields = ["distinct {$alias}.*"];
                $this->setDistinctFlag(true);
                $joins[] = $this->buildCashCollectionJoinSql($this->alias, $this->clientId);
            }
        }

        if ($this->joinCompany) {
            $companyAlias = $this->companyAlias;
            $joins[] = "left join tbl_company as $companyAlias on $companyAlias.company_id=$alias.company_id ";
        }
        

        if (count($joins)) {
            $this->setFields($fields);
            $this->join = implode(' ', $joins);
        }
    }

    /**
     * @return string
     */
    public function getPinPrimaryKey()
    {
        $alias = $this->alias ? $this->alias . '.' : '';
        return $alias . 'opportunity_id';
    }

    public function find()
    {
        $this->initJoin();

        [$where, $params] = $this->buildParams();

        $db = \PgActiveRecord::getDbByClientId($this->clientId);

        $limit = $this->buildLimit();

        $alias = '';
        if (!empty($this->alias)) {
            $alias = 'as ' . $this->alias;

            if (!$this->sortBySearchScore) {
                foreach ($this->orderBy as &$item) {
                    if (!str_contains($item, '.') && !str_contains($item, 'score') && !str_contains($item, 'timezone') && !str_contains($item, 'CASE') && !str_contains($item, 'case')) {
                        if (str_contains($item, 'external_field_data->')) {
                            $item = str_replace('external_field_data->', $this->alias . '.' . 'external_field_data->', $item);
                        } else {
                            $item = $this->alias . '.' . $item;
                        }
                    }
                }
            }
        }

        if (is_null($this->orderBy)) {
            $this->setOrderBy(['update_time desc', 'opportunity_id desc']);
            $this->setOrder(null);
        }
        $orderBy = $this->buildOrderBy();

        $field = $this->fields ?? '*';

        $table = \Opportunity::getModelTableName();

        if (!empty($this->join) && empty($field)) {
            $field = "distinct {$this->alias}.*";
            $this->setDistinctFlag(true);
        }

        //如果调用搜索器且联表
        if (!empty($this->join)) {
            if (($this->sortBySearchScore || strpos($orderBy, 'score#>') !== false) || !empty($this->distinct)) {
                foreach ($this->orderBy as $orderByField) {
                    if (strpos(strtolower($orderByField), 'jsonb') !== false || str_contains($orderByField, 'score') || str_contains($orderByField, 'timezone')) {
                        $field .= ', ' . "{$orderByField}";
                    } else {
                        $orderByField = strpos($orderByField, '.') === false? "{$this->alias}.{$orderByField}" : $orderByField;
                        $field .=  ', ' . "{$orderByField}" ;
                    }
                }
            }
        }

        $sql = "select $field from $table $alias $this->join where $where $orderBy $limit";
        $command = $db->createCommand($sql);
        $data = $command->queryAll(true, $params);

        if (empty($this->fields) || $this->join) {
            $this->formatter->setListData($data);

            $data = $this->formatter->result();
        }

        return $data;
    }

    public function batchFind()
    {

        [$where, $params] = $this->buildParams();

        $db = \PgActiveRecord::getDbByClientId($this->clientId);

        $limit = $this->limit ?? 20;

        $alias = empty($this->alias) ? '' : $this->alias . ".";

        if (is_null($this->orderBy)) {
            $this->setOrderBy('update_time');
            $this->setOrder('desc');
        }
        $orderBy = $this->buildOrderBy();

        $field = $this->fields ?? '*';

        $table = \Opportunity::getModelTableName();

        $sql = "SELECT $field FROM
                (
                SELECT
                    $field,
                    ROW_NUMBER ( ) OVER ( PARTITION BY stage $orderBy NULLS LAST ) AS post_num
                FROM
                    $table $this->alias $this->join
                WHERE
                    $where
                ) stage_table
            WHERE
                post_num <= $limit";


        $command = $db->createCommand($sql);
        $data = $command->queryAll(true, $params);

        if (empty($this->fields) || $this->join) {
            $this->formatter->setListData($data);

            $data = $this->formatter->result();
        }

        return $data;
    }

    public function count()
    {
        $this->initJoin();

        [$where, $params] = $this->buildParams();

        $db = \PgActiveRecord::getDbByClientId($this->clientId);

        $alias = empty($this->alias) ? '' : 'as ' . $this->alias;

        $table = \Opportunity::getModelTableName();

        $countField = 1;
        $distinct = '';
        if (!empty($this->join)) {
            $countField = "{$this->alias}.*";
            //关联customer不做去重
            if (empty($this->joinCustomer))
            {
                $distinct = 'distinct ';
            }
        }

        $sql = "select count({$distinct}{$countField}) from $table $alias $this->join where $where";
        $count = $db->createCommand($sql)->queryScalar($params);

        return $count;
    }

    public function summary()
    {
        $this->initJoin();

        [$where, $params] = $this->buildParams();

        $db = \PgActiveRecord::getDbByClientId($this->clientId);
        $alias = empty($this->alias) ? '' : $this->alias . ".";
        $table = \Opportunity::getModelTableName();

        $countField = 1;
        $distinct = '';
        if (!empty($this->join)) {
            $countField = "{$this->alias}.*";
            $distinct = 'distinct ';
        }

        $sql = "select count({$distinct}{$countField}), sum({$alias}amount_rmb), sum({$alias}amount_usd),
                sum(CASE WHEN stage_type=1 THEN {$alias}amount_rmb ELSE 0 END) ongoing_amount_rmb,
                sum(CASE WHEN stage_type=1 THEN {$alias}amount_usd ELSE 0 END) ongoing_amount_usd,
                sum(CASE WHEN stage_type=2 THEN {$alias}amount_rmb ELSE 0 END) win_amount_rmb,
                sum(CASE WHEN stage_type=2 THEN {$alias}amount_usd ELSE 0 END) win_amount_usd
                from $table $this->alias $this->join where $where";

        $summary = $db->createCommand($sql)->queryRow(false, $params);

        return $summary;
    }

    public function stageSummary()
    {
        $this->initJoin();

        [$where, $params] = $this->buildParams();

        $db = \PgActiveRecord::getDbByClientId($this->clientId);
        $alias = empty($this->alias) ? '' : $this->alias . ".";
        $table = \Opportunity::getModelTableName();

        $countField = 1;
        $distinct = '';
        if (!empty($this->join)) {
            $countField = "{$this->alias}.*";
            $distinct = 'distinct ';
        }

        $sql = "select stage,count({$distinct}{$countField}) as stage_count, sum({$alias}amount_rmb) as stage_amount_rmb, sum({$alias}amount_usd) as stage_amount_usd
                from $table $this->alias $this->join where $where group by stage";

        $batchSummary = $db->createCommand($sql)->queryAll(true, $params);

        return $batchSummary;
    }


    /**
     * @param          $data
     * @param callable $callback
     *
     * @return array
     */
    protected function transToArray($data, $callback = null)
    {
        if (is_callable($callback)) {
            return array_unique(array_filter(is_array($data) ? $data : [$data], $callback));
        }
        return array_unique(array_filter(is_array($data) ? $data : [$data]));
    }

    public function setJoinCustomer($joinCustomer)
    {
        $this->alias = 't1';
        $this->customerAlias = 't2';
        $this->joinCustomer = $joinCustomer;
    }

    public function setJoinCompany($joinCompany)
    {
        $this->alias = 't1';
        $this->companyAlias = 't2';
        $this->joinCompany = $joinCompany;
    }

    public function setJoinSalesFlow($joinSalesFlow)
    {
        $this->joinSalesFlow = $joinSalesFlow;
    }

    public function getCustomerAlias()
    {
        return $this->customerAlias;
    }

    public function getAlias()
    {
        return $this->alias;
    }

    public static function isPositiveNumeric($v)
    {
        return is_numeric($v) && $v > 0;
    }

    public function getCashCollectionStatusReferType()
    {
        return CashCollection::REFER_TYPE_OPPORTUNITY;
    }


    public function queryEmails()
    {
        $this->initJoin();

        [$where, $params] = $this->buildParams();

        $db = \PgActiveRecord::getDbByClientId($this->clientId);

        $limit = $this->buildLimit();

        $alias = '';
        if (!empty($this->alias)) {
            $alias = 'as ' . $this->alias;
        }

        $table = \Opportunity::getModelTableName();

        $sub_sql = "select distinct on (t2.email) t2.email,t1.update_time,t2.customer_id from $table $alias $this->join where $where";
        $sql = "select temp.email from ($sub_sql) as temp order by temp.update_time desc,temp.customer_id desc $limit";

        $command = $db->createCommand($sql);

        $data = $command->queryAll(true, $params);

        return $data;
    }

	/**
	 * @return string
	 */
	public function getStageChangeStartDate(): string {

		return $this->stageEditStartDate;
	}

	/**
	 * @param string $stageChangeStartDate
	 */
	public function setStageEditStartDate(string $stageChangeStartDate): void {

		if (empty($stageChangeStartDate)) {

			return;
		}

		$this->stageEditStartDate = date('Y-m-d H:i:s', strtotime($stageChangeStartDate));
	}

	/**
	 * @return string
	 */
	public function getstageEditEndDate(): string {

		return $this->stageEditEndDate;
	}

	/**
	 * @param string $stageEditEndDate
	 */
	public function setStageEditEndDate(string $stageEditEndDate): void {

		if (empty($stageEditEndDate)) {

			return;
		}

		$this->stageEditEndDate = date('Y-m-d H:i:s', strtotime($stageEditEndDate));
	}


    /**
     * @return mixed
     */
    public function getOriginList() {

        return $this->originList;
    }

    /**
     * @param mixed $originList
     */
    public function setOriginList(array $originList, $recursive = true): void {

        $origin = (new OriginApi($this->clientId))->getIdList((array)$this->transToArray($originList, 'is_numeric'), true, $recursive, true);

        if (empty($originList)) {

            return;
        }

        $this->originList = array_filter($origin);
    }

    public function paramsMapping($setting)
    {
        if (isset($setting['opportunity_ids']) && !empty($setting['opportunity_ids'])) {
            $this->setOpportunityIds($setting['opportunity_ids']);
            $this->paramIsSet = true;
        }

        if (isset($setting['create_user_id'])) {
            $this->setCreateUser($setting['create_user_id']);
            $this->paramIsSet = true;
        }

        if (isset($setting['user_id'])) {
            $this->setUserId($setting['user_id']);
            $this->paramIsSet = true;
        }

        if (isset($setting['user_type'])) {
            $this->setUserType($setting['user_type']);
            $this->paramIsSet = true;
        }

        if (isset($setting['show_all']) && $setting['show_all'] == true) {
            $this->setShowAll(true);
            $this->paramIsSet = true;
        }

        if (isset($setting['keyword']) && !empty($setting['keyword'])) {
            if (isset($setting['search_field']) && !empty($setting['search_field'])) {
                $this->setKeyword($setting['keyword'] ?? '', $setting['search_field'] ?? '');
            } else {
                $this->setTopKeyword($setting['keyword']);
            }

            $this->paramIsSet = true;
        }

        if (isset($setting['serial_id_keyword']) && $setting['serial_id_keyword']) {
            $this->setSerialIdKeyword($setting['serial_id_keyword']);
            $this->paramIsSet = true;
        }

        if (isset($setting['company_keyword']) && $setting['company_keyword']) {
            $this->setCompanyKeyword($setting['company_keyword']);
            $this->paramIsSet = true;
        }

        if (isset($setting['customer_keyword']) && $setting['customer_keyword']) {
            $this->setCustomerKeyword($setting['customer_keyword']);
            $this->paramIsSet = true;
        }

        if (isset($setting['customer_name']) && $setting['customer_name']) {
            $this->setCustomerNameKeyword($setting['customer_keyword']);
            $this->paramIsSet = true;
        }


        if (isset($setting['type'])) {
            $this->setType($setting['type']);
            $this->paramIsSet = true;
        }

        if (isset($setting['currency'])) {
            $this->setCurrency($setting['currency']);
            $this->paramIsSet = true;
        }

        if (isset($setting['origin'])) {
            $this->setOrigin($setting['origin']);
            $this->paramIsSet = true;
        }

        if (isset($setting['min_amount']) || isset($setting['max_amount'])) {
            $this->setAmount($setting['min_amount'] ?? 0, $setting['max_amount'] ?? 0);
            $this->paramIsSet = true;
        }


        if(isset($setting['trail_active_flag']) && in_array($setting['trail_active_flag'],
                [Opportunity::TRAIL_ACTIVE_FLAG_OK, Opportunity::TRAIL_ACTIVE_FLAG_DISABLE])
        ){
            $this->setTrailActiveFlag($setting['trail_active_flag']);
            $this->paramIsSet = true;
        }

        if (isset($setting['company_id'])) {
            $this->setCompanyIds($setting['company_id']);
            $this->paramIsSet = true;
        }

        if (isset($setting['fail_type'])) {
            $this->setFailType($setting['fail_type']);
            $this->paramIsSet = true;
        }

        if (isset($setting['create_start_date'])) {
            $this->setCreateStartDate($setting['create_start_date']);
            $this->paramIsSet = true;
        }

        if (isset($setting['create_end_date'])) {
            $this->setCreateEndDate($setting['create_end_date']);
            $this->paramIsSet = true;
        }

        if (isset($setting['account_start_date'])) {
            $this->setAccountStartDate($setting['account_start_date']);
            $this->paramIsSet = true;
        }

        if (isset($setting['account_end_date'])) {
            $this->setAccountEndDate($setting['account_end_date']);
            $this->paramIsSet = true;
        }

        if (isset($setting['edit_start_date'])) {
            $this->setEditStartDate($setting['edit_start_date']);
            $this->paramIsSet = true;
        }

        if (isset($setting['edit_end_date'])) {
            $this->setEditEndDate($setting['edit_end_date']);
            $this->paramIsSet = true;
        }

        if (isset($setting['product_edit_start_date'])) {
            $this->setProductEditStartDate($setting['product_edit_start_date']);
            $this->paramIsSet = true;
        }

        if (isset($setting['product_edit_end_date'])) {
            $this->setProductEditEndDate($setting['product_edit_end_date']);
            $this->paramIsSet = true;
        }

        if (isset($setting['trail_start_date'])) {
            $this->setTrailStartDate($setting['trail_start_date']);
            $this->paramIsSet = true;
        }

        if (isset($setting['trail_end_date'])) {
            $this->setTrailEndDate($setting['trail_end_date']);
            $this->paramIsSet = true;
        }

        if (isset($setting['order_start_date'])) {
            $this->setOrderStartDate($setting['order_start_date']);
            $this->paramIsSet = true;
        }

        if (isset($setting['order_end_date'])) {
            $this->setOrderEndDate($setting['order_end_date']);
            $this->paramIsSet = true;
        }

        if (isset($setting['stage'])) {
            $this->setStage($setting['stage']);
            $this->paramIsSet = true;
        }

        if (isset($setting['stage_type'])) {
            $this->setStageType($setting['stage_type']);
            $this->paramIsSet = true;
        }

        if (isset($setting['fail_stage'])) {
            $this->setFailStage($setting['fail_stage']);
            $this->paramIsSet = true;
        }

        if (isset($setting['create_type'])) {
            $this->setCreateType($setting['create_type']);
            $this->paramIsSet = true;
        }

        if (isset($setting['approval_status'])) {
            $this->setApprovalStatus($setting['approval_status']);
            $this->paramIsSet = true;
        }

        if (isset($setting['report_item_unique_key'])) {
            $this->setReportItemUniqueKey($setting['report_item_unique_key']);
            $this->paramIsSet = true;
        }


        if (isset($setting['cash_collection_status'])) {
            $this->setCashCollectionStatus($setting['cash_collection_status']);
            $this->paramIsSet = true;
        }

        if (isset($setting['cash_collection_start_date'])) {
            $this->setCashCollectionStartDate($setting['cash_collection_start_date']);
            $this->paramIsSet = true;
        }

        if (isset($setting['cash_collection_end_date'])) {
            $this->setCashCollectionEndDate($setting['cash_collection_end_date']);
            $this->paramIsSet = true;
        }

        if (isset($setting['flow_id'])) {
            $this->setFlowId($setting['flow_id']);
            $this->paramIsSet = true;
        }

        if (isset($setting['customer_id'])) {
            $this->setCustomerIds($setting['customer_id']);
            $this->paramIsSet = true;
        }

        if (isset($setting['main_user']) && $setting['main_user']) {
            $this->setMainUser($setting['main_user']);
            $this->paramIsSet = true;
        }

        $opportunity_field = $setting['opportunity_field'] ?? '';
        $opportunity_field = array_filter(json_decode($opportunity_field, true) ?? []);
        if (!empty($opportunity_field)) {
            
            $this->setExternalFields(\Constants::TYPE_OPPORTUNITY, $opportunity_field);
            $this->paramIsSet = true;
        }

        if (isset($setting['pin']) && $setting['pin']) {
            $this->setIsPin($setting['pin']);
            $this->paramIsSet = true;
        }

        //检查客户是否被删除
        if(isset($setting['check_company_exits']) && $setting['check_company_exits']){
            $this->setJoinCompany(true);
            $this->paramIsSet = true;
        }

        if (isset($setting['pin_user_list']) && !empty($setting['pin_user_list'])) {
            $this->setPinUserList($setting['pin_user_list']);
            $this->paramIsSet = true;
        }

        if (isset($setting['origin_list']) && !empty($setting['origin_list'])) {
            $this->setOriginList((array)$setting['origin_list'], false);
            $this->paramIsSet = true;
        }

        if (isset($setting['disable_flag']) && !is_null($setting['disable_flag'])) {
            $this->setDisableFlag($setting['disable_flag']);
            $this->paramIsSet = true;
        }

        if (isset($setting['skip_privilege'])) {
            $this->setSkipPermissionCheck($setting['skip_privilege']);
            $this->paramIsSet = true;
        }

        if (isset($setting['filters'])) {
            $this->setFilters($setting['filters'], $setting['criteria_type'] ?? 1, $setting['criteria'] ?? '');
            $this->paramIsSet = true;
        }
    
    
        if (!empty($setting['tags'])) {
        
            $this->setTags($setting['tags']);
            $this->paramIsSet = true;
        }
    
        if (!empty($setting['tag_match_mode'])) {
        
            $this->setTagMatchMode($setting['tag_match_mode']);
            $this->paramIsSet = true;
        }
    }

    public function setFilters($filters, $criteria_type, $criteria)
    {
        $filters = array_filter($filters, function($filter) {
            return is_array($filter) && array_key_exists('field', $filter) && array_key_exists('value', $filter);
        });

        $referFieldFilters = [];
        foreach ($filters as $key => $filter) {
            if ($filter['field_type'] == CustomFieldService::FIELD_TYPE_QUOTE_FIELDS) {
                $filter['field'] = $filter['relation_origin_field'];
                $filter['field_type'] = $filter['relation_field_type'];
                $filter['refer_type'] = $filter['relation_origin_type'];
                $referFieldFilters[$key] = $filter;
                unset($filters[$key]);
            } elseif (in_array($filter['field'], ['company_name', 'company_user_id', 'customer_list.name', 'customer_list.email', 'customer_list.reach_status', 'customer_list.reach_status_time']) && !in_array($filter['operator'], [\common\library\workflow\WorkflowConstant::FILTER_OPERATOR_MATCH, \common\library\workflow\WorkflowConstant::FILTER_OPERATOR_PREFIX])) {
                $filter['field'] = str_replace('company_', '', $filter['field']);
                $referFieldFilters[$key] = $filter;
                unset($filters[$key]);
            }
        }

        (new \common\library\validation\Validator(
            compact('filters', 'criteria_type'),
            [
                'filters' => 'array|max:100|swarm_rule_filter:' . implode(',', [$this->clientId, \Constants::TYPE_OPPORTUNITY, WorkflowConstant::RULE_TYPE_FILTERS]),
                'criteria_type' => 'required|integer'
            ]
        ))->validate();

        $advancedSearchFilters = array_filter($filters, function($item) {
            return in_array($item['operator'], [\common\library\workflow\WorkflowConstant::FILTER_OPERATOR_MATCH, \common\library\workflow\WorkflowConstant::FILTER_OPERATOR_PREFIX]);
        });

        $filters = array_filter($filters, function($item) {
            return !(in_array($item['operator'], [\common\library\workflow\WorkflowConstant::FILTER_OPERATOR_MATCH, \common\library\workflow\WorkflowConstant::FILTER_OPERATOR_PREFIX]));
        });

        foreach ($advancedSearchFilters as $key => $filter) {
            if ($filter['field'] == 'customer_list.tel_list') {
                $filter['field'] = 'customer_list.tel';
            }

            $advancedSearchFilters[$key] = $filter;
        }

        $this->referFieldFilters = array_values($referFieldFilters);
        $this->advancedSearchFilters = array_values($advancedSearchFilters);
        $this->filters = array_values($filters);
        $this->criteriaType = $criteria_type;
        $this->criteria = $criteria;
    }

    public function buildParamsByFilters($filters, $criteriaType, $criteria = '', $alias = '', $userId = 0, $returnRule = false)
    {
        if (!$criteria) {
            $i = 1;
            foreach ($filters as &$filter) {
                $filter['filter_no'] = $i++;
            }
        }
        $criteria = WorkflowCriteriaBuilder::buildCriteria($filters, $criteriaType, $criteria);

        if ($returnRule) {
            return [
                'filters'=> $filters,
                'criteria_type' => $criteriaType,
                'criteria' => $criteria,
                'user_id' => $userId
            ];
        }

        return $this->buildFilterParams(
            [
                'filters' => $filters,
                'criteria' => $criteria,
                'user_id' => $userId,
            ],
            $alias
        );
    }

    public function buildFilterParams($ruleInfo, $alias = '')
    {
        $filter = new WorkflowFilterRunner($ruleConfig = new ListConfig($this->clientId, \Constants::TYPE_OPPORTUNITY, [], $ruleInfo));
        if ((empty($ruleInfo['filters']) || empty($ruleInfo['criteria'])) && (empty($ruleInfo['extra_filters']) || empty($ruleInfo['extra_criteria']))) {
            return ['', []];
        }
        $filter->setFilters($ruleInfo['filters'], $ruleInfo['criteria']);
        !empty($ruleInfo['extra_criteria']) && $filter->addExtraWorkflowFilters($ruleInfo['extra_filters'], $ruleInfo['extra_criteria']);
        $alias = str_replace('.', '', $alias);
        $filter->setAliasMap([
            \Constants::TYPE_OPPORTUNITY => $alias
        ]);

        return $filter->buildParams();
    }

    public function getParamIsSet()
    {
        return $this->paramIsSet;
    }
    
    public function setTags(array $tags) {
        
        $tags = array_filter(array_map('intval', $tags));
        
        if (empty($tags)) {
            
            return;
        }
        
        $this->tags = $tags;
    }
    
    /**
     * @param int $tagMatchMode
     */
    public function setTagMatchMode(int $tagMatchMode = BaseCompanyList::TAG_MATCH_MODE_COMPLETE): void {
        
        $this->tagMatchMode = $tagMatchMode;
    }
    
    public function getJoin() {
        
        return $this->join;
    }

    public function setCustomerContact($contact)
    {
        $this->customerContact = $contact;
    }

    public function setFilterEmptyCustomerTel(bool $flag){
        $this->filterEmptyCustomerTel = $flag;
    }

    public function setSuspectedInvalidFlag($suspectedInvalidFlag)
    {
        $this->suspectedInvalidFlag = $suspectedInvalidFlag;
    }
    
    /**
     * 获取可编辑的线索ID
     *
     * @param array $context
     * @param ?string $field 指定的字段
     * @return array
     */
    public function fetchEditableObjIds(array $context, string $field = null): array
    {
        $privilegeId = $context['opportunity_privilege'] ?? null;
        $hasPrivilegeId = !is_null($privilegeId);
        $hasField = !is_null($field);
        
        if (is_null($privilegeId) && is_null($field)) {
            // 无需过滤
            return [null, null];
        }
        
        $opportunityIds = [];
        $list = deep_copy($this);
        $alias = $list->getAlias() ? $list->getAlias() . '.' : '';
        $list->setFields(["{$alias}opportunity_id", "{$alias}scope_user_ids", "{$alias}user_id"]);
        $list->setOrderBy('opportunity_id');
        $list->setSkipPermissionCheck(true);
        $opportunities = $list->find();
        if (!$opportunities) {
            return [[], []];
        }
        array_walk($opportunities, function (&$opportunity) {
            $opportunity['user_id'] = PgsqlUtil::arrayOrTrimArray($opportunity['user_id']);
            $opportunity['scope_user_ids'] = PgsqlUtil::arrayOrTrimArray($opportunity['scope_user_ids']);
        });
        $allOpportunityIds = array_column($opportunities, 'opportunity_id');
        
        // 避免数据量过大，实时计算
        $privilegeTask = (new PrivilegeFormatTask($this->clientId, $this->viewingUserId))
            ->setOperatePrivilegeFormatterFlag($hasPrivilegeId)
            ->setFieldPrivilegeFormatterFlag($hasField)
            ->setObjectScene(PrivilegeFieldV2::SCENE_OF_UPDATE)
            ->setReferType(\Constants::TYPE_OPPORTUNITY)
            ->setObjName(\common\library\object\object_define\Constant::OBJ_OPPORTUNITY);
        $privilegeTask->prepare($opportunities);
        
        foreach ($opportunities as $opportunity) {
            $privilegeTask->setReferenceData($opportunity);
            $privilegeTask->run($opportunity);
            
            $hasPermission = true;
            if ($hasPrivilegeId) {
                $hasPermission = in_array($privilegeId, $opportunity['access_privilege_stats']);
            }
            
            $isEditableField = true;
            if ($hasField) {
                foreach ($opportunity['field_privilege_stats'] ?? [] as $privilege) {
                    if ($privilege['refer_type'] != \Constants::TYPE_OPPORTUNITY) {
                        continue;
                    }
                    if (in_array($field, $privilege['disable'] ?? []) || in_array($field, $privilege['readonly'] ?? [])) {
                        $isEditableField = false;
                        break;
                    }
                }
            }
            if ($hasPermission && $isEditableField) {
                $opportunityIds[] = $opportunity['opportunity_id'];
            }
        }
        
        return [$opportunityIds, array_diff($allOpportunityIds, $opportunityIds)];
    }
}
