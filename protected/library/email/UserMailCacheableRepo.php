<?php

namespace common\library\email;

use common\library\cache\CacheableListRepo;
use common\library\cache\CacheConstant;
use common\library\util\SqlBuilder;
use common\library\workflow\WorkflowConstant;
use Util;

class UserMailCacheableRepo extends CacheableListRepo
{

    protected $allowGetAll = false;

    public $userId;
    public $onlyBind = false;
    public $onlyUnbind = false;
    public $userMailId;
    public $emailAddress;
    public $validFlag;
    public $enableFlag = 1;
    public $keyword;

    public $showMailSignInfo;
    public $showMobileMailSignInfo;
    public $showMailSettingInfo;
    public $showUserInfo;
    public $showBindMailInfo; //显示绑定参数
    public $showDefaultMailFlag;  //显示默认邮箱标志
    public $showUnreadCount;
    public $showPassword = false;
    public $isKeywordMatchUser = true;
    public $showMailAliases = false; // 显示邮箱别名
    public $hideSubUserMails = false; // 隐藏公共邮箱 - 子邮箱
    public $showSubUserIds = false;
    public $showOnlySubUserMails = null; // 只显示公共子邮箱
    public $showSendServerValidFlag = false; // 判断是否可用邮箱（非网易腾讯邮箱）
    public $showExposeTimes = false; //判断是否展示邮箱群发单显次数
    public $showRemark = false; //判断是否展示邮箱显示名称

    protected function buildListByDb($id = null, $fields = '*')
    {
        $where = "client_id = :client_id and enable_flag=1";
        $params = [
            ':client_id' => $this->clientId,
        ];
        if ($id) {
            SqlBuilder::buildIntWhere('', $this->getIdName(), $id, $where, $params);
        }

        $sql = "select {$fields} from tbl_user_mail where {$where}";

        return [$sql, $params];
    }

    public function build()
    {
        $this->sql = '';
        $this->params = [];
        $sql = &$this->sql;
        $params = &$this->params;

        if ($this->clientId) {
            $this->buildParam('client_id', $this->clientId);
        }

        if ($this->userId) {
            $this->buildParam('user_id', is_array($this->userId) ? array_filter($this->userId) : $this->userId);
        }

        if ($this->onlyUnbind)
        {
            $this->buildParam('user_id', '0', WorkflowConstant::FILTER_OPERATOR_EQUAL);
        }

        if ($this->onlyBind) {
            $this->buildParam('user_id', '0', WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL);
        }

        if ($this->userMailId) {
            $this->buildParam('user_mail_id', $this->userMailId);
        }

        if ($this->emailAddress) {
//            $emails = array_map(function ($email) {
//                return Util::escapeDoubleQuoteSql($email);
//            }, (array)$this->emailAddress);
//            $emails = '\'' . implode("','", $emails) . '\'';
//            $sql .= " and email_address in ({$emails})";
            $this->buildParam('email_address', $this->emailAddress);
        }

        if ($this->validFlag !== null) {
            $this->buildParam('valid_flag', $this->validFlag);
        }

        if ($this->enableFlag !== null) {
            $this->buildParam('enable_flag', $this->enableFlag);
            if (!$this->enableCache) {
                $this->setEnableCache(false);
            }
        }

        if ($this->keyword) {
            $keyword = Util::escapeDoubleQuoteSql($this->keyword);
            $result = [];
            if ($this->isKeywordMatchUser) {
                $userListObj = new \common\library\account\UserList();
                $userListObj->setClientId($this->clientId);
                $userListObj->setEnableFlag(1);
                $userListObj->setKeyWord($this->keyword);
                $result = $userListObj->find();
            }
            if (!empty($result)) {
                $this->buildBool([
                    'or' => [
                        ['email_address', "%{$keyword}%", WorkflowConstant::FILTER_OPERATOR_CONTAINS],
                        ['user_id', array_column($result, 'user_id')],
                    ],
                ]);
//                $sql .= " and (email_address like '%{$keyword}%' or user_id in (" . implode(',', array_column($result, 'user_id')) . '))';
            } else {
                $this->buildParam('email_address', "%{$keyword}%", WorkflowConstant::FILTER_OPERATOR_CONTAINS);
            }
        }

        if ($this->hideSubUserMails) {
            $this->buildBool([
                'or' => [
                    ['source_user_mail_id', 0],
                    ['source_user_mail_id', 'user_mail_id', WorkflowConstant::FILTER_OPERATOR_EQUAL, self::VALUE_TYPE_FIELD],
                ],
            ]);
//            $sql .= ' and (source_user_mail_id = 0 or source_user_mail_id = user_mail_id)';
        }

        if ($this->showOnlySubUserMails) {
            $this->buildBool([
                'and' => [
                    ['source_user_mail_id', 0, WorkflowConstant::FILTER_OPERATOR_GREATER],
                    ['source_user_mail_id', 'user_mail_id', WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL, self::VALUE_TYPE_FIELD],
                ],
            ]);
//            $sql .= ' and source_user_mail_id > 0 and source_user_mail_id != user_mail_id';
        }

        return [$sql, $params];
    }

    protected function getDbConnection()
    {
        return \UserMail::model()->getDbConnection();
    }

    protected function getIdName()
    {
        return 'user_mail_id';
    }

    protected function getTableName()
    {
        return 'tbl_user_mail';
    }

    public function getSourceUserMailInfo($userMailIds)
    {
        $userMailIds = array_values(array_filter($userMailIds));
        if (empty($userMailIds)) return [];

        $result = null;
        if ($this->isEnableCache()) {
            $subMailList = $this->findAllByConditions([
                'client_id' => $this->clientId,
                'user_mail_id' => $userMailIds,
                ['source_user_mail_id', 0, WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL],
                'enable_flag' => 1,
            ], 'user_mail_id,user_id,source_user_mail_id');
            if ($subMailList !== null) {
                $result = [];
                if (!$subMailList) {
                    return $result;
                }
                $subMailMap = array_column($subMailList,  'user_mail_id', 'source_user_mail_id');
                $mainMailList = $this->findAllByConditions([
                    'client_id' => $this->clientId,
                    'user_mail_id' => array_keys($subMailMap),
                    'enable_flag' => 1,
                ], 'user_mail_id,user_id,source_user_mail_id');
                foreach ($mainMailList as $mainMail) {
                    $result[] = [
                        'main_user_mail_id' => $mainMail['user_mail_id'],
                        'sub_user_mail_id' => $subMailMap[$mainMail['user_mail_id']] ?? 0,
                        'main_user_id' => $mainMail['user_id'],
                    ];
                }
                $result = array_column($result, null, 'sub_user_mail_id');
            }
        }

        if ($result === null) {
            $adminDb = $this->getDbConnection();
            $userMailIdsSql = implode(',', $userMailIds);
            $sql = "SELECT u1.user_mail_id as main_user_mail_id,u2.user_mail_id as sub_user_mail_id,u1.user_id as main_user_id from tbl_user_mail as u1 INNER JOIN tbl_user_mail as u2 on u1.user_mail_id = u2.source_user_mail_id where u2.user_mail_id in ({$userMailIdsSql}) and u1.enable_flag = 1;";
            $result = array_column($adminDb->createCommand($sql)->queryAll(), null, 'sub_user_mail_id');
        }

        return $result ?? [];
    }

    public function indexColumns()
    {
        return ['email_address', 'user_id'];
    }

}