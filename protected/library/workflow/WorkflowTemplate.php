<?php

namespace common\library\workflow;

use common\library\cash_collection\CashCollection;
use common\library\custom_field\CustomFieldService;
use common\library\invoice\Order;
use common\library\invoice\status\InvoiceStatusService;
use common\library\oms\common\OmsConstant;
use common\library\privilege_v3\functional\FunctionalHandler;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\setting\library\origin\Origin;
use common\library\setting\library\stage\Stage;
use common\library\setting\library\tag\Tag;
use common\library\workflow\handler\processor\DingTalkProcessor;

class WorkflowTemplate
{
    public static function getTemplateListData($clientId)
    {
        $invoiceStatusService = new InvoiceStatusService($clientId, \Constants::TYPE_ORDER);
        $orderEndStatus = $invoiceStatusService->endStatus();
        $filterStatus = ['已作废', '售后', '交易取消'];
        foreach ($orderEndStatus as $status) {
            if (in_array($status['name'], $filterStatus)) {
                $orderFilterStatusIds[] = $status['id'];
            } else {
                $orderEndStatusIds[] = $status['id'];
            }
        }
        //图文类型的预置图片
        $dingTalkPreparePicture = [
            'file_id' => 3259887505,
            'file_name' => 'ding-talk-bg.png',
            'status' => 1,
            'file_path' => "https://v4client.oss-cn-hangzhou.aliyuncs.com/invoice-file/img/11858405/34140763b354d25b6cac0cee076c09beb706f99e7019ec7c848dc4eaddd7a240.png",
            'file_size' => 183828,
            'file_url' => "https://v4client.oss-cn-hangzhou.aliyuncs.com/invoice-file/img/11858405/34140763b354d25b6cac0cee076c09beb706f99e7019ec7c848dc4eaddd7a240.png",
            'preview_url' => "https://v4client.oss-cn-hangzhou.aliyuncs.com/invoice-file%2Fimg%2F11858405%2F34140763b354d25b6cac0cee076c09beb706f99e7019ec7c848dc4eaddd7a240.png?response-content-disposition=inline%3B%20filename%3Dding-talk-bg.png%3B%20filename%2A%3Dutf-8%27%27ding-talk-bg.png&response-content-type=image%2Fpng&OSSAccessKeyId=LTAI4Fsrgw3EhF2TNvsN9iye&Signature=DiyV0xrKTBTQzhLLNJ7zWc%2FrZQE%3D&Expires=1678332121",
            'file_preview_url' => "https://v4client.oss-cn-hangzhou.aliyuncs.com/invoice-file%2Fimg%2F11858405%2F34140763b354d25b6cac0cee076c09beb706f99e7019ec7c848dc4eaddd7a240.png?response-content-disposition=inline%3B%20filename%3Dding-talk-bg.png%3B%20filename%2A%3Dutf-8%27%27ding-talk-bg.png&response-content-type=image%2Fpng&OSSAccessKeyId=LTAI4Fsrgw3EhF2TNvsN9iye&Signature=DiyV0xrKTBTQzhLLNJ7zWc%2FrZQE%3D&Expires=1678332121",
        ];

        $cashCollectionInvoiceJumpageLabel = \Yii::t('workflow', 'refer_type.' . \Constants::TYPE_CASH_COLLECTION_INVOICE) . "的详情页";
        $templateListData = [
            // 最新
            1034 => [
                'id' => 1034,
                'group_id' => \Constants::TYPE_ORDER,
                'second_group_id' => WorkflowConstant::RECLASSIFY_DING_TALK,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_ORDER],
                'name' => '订单首次回款战报推送',
                'description' => '订单首次回款时，自动推送钉钉群通知',
                'refer_type' => \Constants::TYPE_ORDER,
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'field' => 'first_collection_date',
                        'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => '',
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'first_collection_date',
                        'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => '',
                        'filter_no' => 2,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_DING_TALK,
                        'config' => [
                            'type' => 2,
                            'title' => '今日喜报',
                            'content' => '<p>恭喜{{2.departments}}的{{2.users}}订单首次回款！<br />客户名称：{{4.name}}<br />首次回款日期：{{2.first_collection_date}}</p>',
                            'pic_url' => '',
                            'user_field' => '2.users',
                            'amount_format_type' => 1,
                            'amount_field' => 'amount',
                            'webhooks' => [],
                        ]
                    ]
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2)',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_ORDER,
                ],
            ],
            1018 => [
                'id' => 1018,
                'group_id' => \Constants::TYPE_COMPANY,
                'name' => '根据订单情况自动评定5星客户',
                'description' => '多次成交且单笔订单金额≥20万人民币自动将该客户星级调整为5星级',
                'refer_type' =>\Constants::TYPE_ORDER,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_ORDER],
                'second_group_id' => WorkflowConstant::RECLASSIFY_MODIFY_FIELD,
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_REPEAT,
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_DAY,
                    'end_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => '2999-12-31',
                    ],
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => date("Y-m-d", strtotime("+1 day")),
                    ],
                ],
                'filters' => [
                    [
                        'unit' => '',
                        'field' => 'amount_rmb',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => 200000,
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'performance_order_count',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => 2,
                        'filter_no' => 2,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => 'field',
                        'config' => [
                            [
                                'field' => 'star',
                                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                                'value' => 5,
                                'refer_type' => \Constants::TYPE_COMPANY,
                                'uiType' => '1',
                                'unit' => '',
                                'label' => '',
                            ],
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2)',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_ORDER,
                ],
            ],
            1035 => [
                'id' => 1035,
                'group_id' => \Constants::TYPE_ORDER,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '大额订单通知',
                'description' => '订单金额超过5万美金及时向业务主管提示（可选择日程/系统/邮件通知）',
                'refer_type' => \Constants::TYPE_ORDER,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_ORDER],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'unit' => 'CNY',
                        'field' => 'amount',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => '',
                        'filter_no' => 1,
                    ],
                    [
                        'unit' => 'USD',
                        'field' => 'amount',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => 50000,
                        'filter_no' => 2,
                    ],
                    [
                        'field' => 'status',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => [],
                        'filter_no' => 3,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_EMAIL_NOTIFY,
                        'config' => [
                            'addition_user' => [],
                            'addition_email' => [],
                            'user_field' => [],
                            'subject' => '大额订单创建通知-【{{2.order_no}}】{{2.name}}',
                            'content' => '<p>客户：{{2.company_name}}<br />订单金额：{{2.amount}} {{2.currency}}<br />订单状态：{{2.status}}</p>',
                            'contentText' => '<p>客户：#销售订单_客户名称#<br />订单金额：#销售订单_订单金额# #销售订单_币种#<br />订单状态：#销售订单_订单状态#</p>',
                            'subjectText' => '大额订单创建通知-【#销售订单_订单号#】#销售订单_订单名称#',
                            'channel' => [
                                'email' => 1,
                                'message' => 1,
                                'app' => 1,
                                'desktop' => 1,
                            ],
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2 AND 3)',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_ORDER,
                ],
            ],
            1036 => [
                'id' => 1036,
                'group_id' => \Constants::TYPE_ORDER,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '订单状态变更提醒',
                'description' => '订单状态变更后通知对应的人员，譬如订单变更到备货提醒（收定金）则提醒工厂生产排期（可选择日程/系统/邮件通知）',
                'refer_type' => \Constants::TYPE_ORDER,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_ORDER],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'field' => 'status',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => '',
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'status',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => '',
                        'filter_no' => 2,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_EMAIL_NOTIFY,
                        'config' => [
                            'addition_user' => [],
                            'addition_email' => [],
                            'user_field' => [],
                            'subject' => '订单状态变更提醒-【{{2.order_no}}】{{2.name}}状态变更为{{2.status}}',
                            'content' => '<p>客户：{{2.company_name}}<br />订单金额：{{2.amount}} {{2.currency}}<br />订单状态：{{2.status}}</p>',
                            'contentText' => '<p>客户：#销售订单_客户名称#<br />订单金额：#销售订单_订单金额# #销售订单_币种#<br />订单状态：#销售订单_订单状态#</p>',
                            'subjectText' => '订单状态变更提醒-【#销售订单_订单号#】#销售订单_订单名称#状态变更为#销售订单_订单状态#',
                            'channel' => [
                                'email' => 1,
                                'message' => 1,
                                'app' => 1,
                                'desktop' => 1,
                            ]
                        ]
                    ]
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2)',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_ORDER,
                ],
            ],
            1037 => [
                'id' => 1037,
                'group_id' => \Constants::TYPE_ORDER,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '订单客户公海流转提醒',
                'description' => '当客户有订单进行时，如果客户被移入了公海，会自动触发邮件通知提醒，让对应订单跟进人去公海拣回继续跟进',
                'refer_type' => \Constants::TYPE_ORDER,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_ORDER],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_REPEAT,
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_DAY,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => date("Y-m-d", strtotime("+1 day"))
                    ],
                    'end_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => '2999-12-31',
                    ],
                ],
                'filters' => [
                    [
                        'field' => 'user_id',
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => '',
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'status',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => [],
                        'filter_no' => 2,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => 'email',
                        'config' => [
                            'addition_user' => [],
                            'addition_email' => [],
                            'user_field' => [
                                '4.last_owner',
                            ],
                            'subject' => '有进行中订单的客户【{{4.name}}】被流转到公海',
                            'content' => '<p>客户：【{{4.name}}】有进行中的订单，被流转到公海，请关注</p>',
                            'contentText' => '<p>客户：【#销售订单_客户_公司名称#】有进行中的订单，被流转到公海，请关注</p>',
                            'subjectText' => '有进行中订单的客户【#销售订单_客户_公司名称#】被流转到公海',
                            'channel' => [
                                'email' => 1,
                                'message' => 1,
                                'app' => 1,
                                'desktop' => 1,
                            ],
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2)',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_ORDER,
                ],
            ],
            1040 => [
                'id' => 1040,
                'group_id' => \Constants::TYPE_ORDER,
                'second_group_id' => WorkflowConstant::RECLASSIFY_MODIFY_FIELD,
                'name' => '订单客户自动变更阶段',
                'description' => '客户成交后根据订单成交次数，变更客户所属阶段，提高客户分组准确性，减少业务员操作',
                'refer_type' => \Constants::TYPE_ORDER,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_ORDER],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_REPEAT,
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_DAY,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => date("Y-m-d", strtotime("+1 day"))
                    ],
                    'end_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => '2999-12-31',
                    ],
                ],
                'filters' => [
                    [
                        'field' => 'performance_order_count',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => '',
                        'filter_no' => 1,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_UPDATE_FIELD,
                        'config' => [
                            [
                                'field' => 'trail_status',
                                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                'value' => '',
                                'refer_type' => \Constants::TYPE_COMPANY,
                                'unit' => '',
                                'label' => '',
                            ],
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_ORDER,
                ],
            ],
            1041 => [
                'id' => 1041,
                'group_id' => \Constants::TYPE_COMPANY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '已成交订单客户阶段检查',
                'description' => '未成交客户但业务员将客户从未成交分组调整为已成交分组时，邮件提示主管进行检查，避免分组错误',
                'refer_type' => \Constants::TYPE_COMPANY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_COMPANY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'field' => 'performance_order_count',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => 0,
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'success_opportunity_count',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => 0,
                        'filter_no' => 2,
                    ],
                    [
                        'field' => 'trail_status',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => '',
                        'filter_no' => 3,
                    ],
                    [
                        'field' => 'trail_status',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => '',
                        'filter_no' => 4,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_EMAIL_NOTIFY,
                        'config' => [
                            'addition_user' => [],
                            'addition_email' => [],
                            'user_field' => [],
                            'subject' => '未成交客户阶段变更，请检查',
                            'subjectText' => '未成交客户阶段变更，请检查',
                            'content' => '<p>未成交客户【{{4.name}}】的客户阶段变更为{{4.trail_status}}<br />客户跟进人：{{4.user_id}}</p>',
                            'contentText' => '<p>未成交客户【#客户_公司名称#】的客户阶段变更为#客户_客户阶段#<br />客户跟进人：#客户_跟进人#</p>',
                            'channel' => [
                                'email' => 1,
                                'message' => 1,
                                'app' => 1,
                                'desktop' => 1,
                            ],
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2 AND 3 AND 4)',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [],
            ],
            1007 => [
                'id' => 1007,
                'group_id' => \Constants::TYPE_COMPANY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_AUTOMATIC_ALLOCATION,
                'name' => '根据渠道自动分配公海客户',
                'description' => '公海里来源某个渠道的客户可自动分配给指定人员，通常用在按来源渠道跟进客户的团队',
                'refer_type' =>\Constants::TYPE_COMPANY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_COMPANY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_REPEAT,
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_DAY,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => date("Y-m-d", strtotime("+1 day"))
                    ],
                    'end_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => '2999-12-31'
                    ]
                ],
                'filters' => [
                    [
                        'filter_no' => 1,
                        'field' => 'user_id',
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 2,
                        'field' => 'origin_list',
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                        'value' => [],
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_ASSIGN,
                        'config' => [
                            [
                                'field' => 'user_id',
                                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                                'method' => WorkflowConstant::HANDLER_TYPE_ASSIGN_METHOD_ITERATE_REPLACE,
                                'value' => [
                                    'user_id' => []
                                ]
                            ]
                        ]
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2)',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [],
            ],
            1042 => [
                'id' => 1042,
                'group_id' => \Constants::TYPE_COMPANY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_AUTOMATIC_ALLOCATION,
                'name' => '根据国家自动分配公海客户',
                'description' => '公海里来源某个国家的客户可自动分配给指定人员，通常用在按国家跟进客户的团队',
                'refer_type' =>\Constants::TYPE_COMPANY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_COMPANY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_REPEAT,
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_DAY,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => date("Y-m-d", strtotime("+1 day"))
                    ],
                    'end_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => '2999-12-31',
                    ],
                ],
                'filters' => [
                    [
                        'field' => 'user_id',
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'country',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => [],
                        'filter_no' => 2,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_ASSIGN,
                        'config' => [
                            [
                                'field' => 'user_id',
                                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                                'value' => [
                                    'last_owner_ids' => '',
                                    'user_departments' => [],
                                    'user_id' => [],
                                    'exclude_user_id' => [],
                                ],
                                'method' => 'iterate_replace',
                                'label' => '',
                            ],
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2)',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [],
            ],
            1044 => [
                'id' => 1044,
                'group_id' => \Constants::TYPE_COMPANY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_MODIFY_FIELD,
                'name' => '不同人员跟进的客户移入公海时，自动进入不同公海分组',
                'description' => '不同跟进人的客户自动进入对应部门的公海',
                'refer_type' =>\Constants::TYPE_COMPANY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_COMPANY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'field' => 'last_owner',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => [
                            'last_owner_ids' => '',
                            'user_departments' => [],
                            'user_id' => [],
                            'exclude_user_id' => [],
                            'is_all' => 0,
                        ],
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'user_id',
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => '',
                        'filter_no' => 2,
                    ],
                    [
                        'field' => 'user_id',
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => '',
                        'filter_no' => 3,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_UPDATE_FIELD,
                        'config' => [
                            [
                                'field' => 'pool_id',
                                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                'value' => '',
                                'refer_type' => \Constants::TYPE_COMPANY,
                                'uiType' => '1',
                                'unit' => '',
                                'label' => '',
                            ],
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2 AND 3)',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [],
            ],
            1045 => [
                'id' => 1045,
                'group_id' => \Constants::TYPE_COMPANY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '未及时跟进客户提醒',
                'description' => '超过30天没有跟进的客户自动新建日程提醒（可选择日程/系统/邮件通知）',
                'refer_type' => \Constants::TYPE_COMPANY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_COMPANY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_ONCE,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_DYNAMIC,
                        'field' => 'recent_follow_up_time',
                        'field_name' => '最近跟进时间',
                        'operator' => WorkflowConstant::DATE_TYPE_OPERATOR_AFTER,
                        'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => 30,
                    ],
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_ONCE,
                ],
                'filters' => [],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_SCHEDULE,
                        'config' => [
                            'type_id' => 1,
                            'content' => '<p>客户{{4.name}} 30天未跟进，请关注！</p>',
                            'user_field' => [
                                '4.user_id',
                            ],
                            'addition_user' => [],
                            'start_time' => [
                                'field' => 'workflow_trigger_time',
                                'value' => 0,
                            ],
                            'end_time' => [
                                'field' => 'workflow_trigger_time',
                                'value' => 1,
                            ],
                            'relate_refer' => 1,
                            'notify' => [
                                [
                                    'type' => 'day',
                                    'time' => '',
                                ],
                            ],
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [],
            ],
            1049 => [
                'id' => 1049,
                'group_id' => \Constants::TYPE_COMPANY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_MODIFY_FIELD,
                'name' => '自动评定5星客户（基于商机）',
                'description' => '多次成交且单笔商机金额≥20万人民币自动将该客户星级调整为5星级',
                'refer_type' => \Constants::TYPE_OPPORTUNITY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_OPPORTUNITY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_REPEAT,
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_DAY,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => date("Y-m-d", strtotime("+1 day")),
                    ],
                    'end_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => '2999-12-31',
                    ],
                ],
                'filters' => [
                    [
                        'unit' => 'CNY',
                        'field' => 'amount',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL,
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'value' => 200000,
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'success_opportunity_count',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => 2,
                        'filter_no' => 2,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_UPDATE_FIELD,
                        'config' => [
                            [
                                'field' => 'star',
                                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                                'value' => 5,
                                'refer_type' => \Constants::TYPE_COMPANY,
                                'uiType' => '1',
                                'unit' => '',
                                'showTypeSelect' => false,
                                'label' => '',
                            ],
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2)',
                'icon' => 1,
                'tag' => '',
                'functional' => [],
            ],
            1050 => [
                'id' => 1050,
                'group_id' => \Constants::TYPE_ORDER,
                'second_group_id' => WorkflowConstant::RECLASSIFY_WECOM,
                'name' => '订单首次回款战报推送',
                'description' => '订单首次回款时，自动推送企微群通知',
                'refer_type' => \Constants::TYPE_ORDER,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_ORDER],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'field' => 'first_collection_date',
                        'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => '',
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'first_collection_date',
                        'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => '',
                        'filter_no' => 2,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_QYWECHAT,
                        'config' => [
                            'type' => DingTalkProcessor::DINGTALK_MESSAGE_TYPE_POSTER,
                            'title' => '今日喜报',
                            'content' => '<p>恭喜{{2.departments}}的{{2.users}}订单首次回款！<br />客户名称：{{4.name}}<br />首次回款日期：{{2.first_collection_date}}</p>',
                            'pic_url' => '',
                            'user_field' => '2.users',
                            'amount_format_type' => 1,
                            'webhooks' => [],
                            'amount_field' => 'amount',
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2)',
                'icon' => 1,
                'tag' => '',
                'functional' => [],
            ],
            1008 => [
                'id' => 1008,
                'group_id' => \Constants::TYPE_COMPANY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '成交客户60天未联系提醒',
                'description' => '有过成交的客户60天未联系做系统提醒',
                'refer_type' =>\Constants::TYPE_COMPANY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_COMPANY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_ONCE,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_DYNAMIC,
                        'value' => 60,
                        'field' => 'recent_follow_up_time',
                        'operator' => WorkflowConstant::DATE_TYPE_OPERATOR_AFTER,
                        'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                        'refer_type' => \Constants::TYPE_COMPANY,
                    ],
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_ONCE
                ],
                'filters' => [
                    [
                        'field' => 'user_id',
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator' => 'not_null',
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => '',
                        'filter_no' => 1,
                    ],
                    [
                        'unit' => '',
                        'field' => 'success_opportunity_count',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => 1,
                        'filter_no' => 2,
                    ],
                    [
                        'unit' => '',
                        'field' => 'performance_order_count',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => 1,
                        'filter_no' => 3,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => 'notify',
                        'config' => [
                            'channel' => [
                                'app' => 1,
                                'email' => 0,
                                'browser' => 1,
                                'desktop' => 1,
                                'message' => 1,
                            ],
                            'content' => '<p>客户：{{4.name}}<br />您已超过60天未联系该成交客户，请及时进行跟进。</p>',
                            'subject' => '成交客户60天未联系提醒',
                            'subjectText' => '成交客户60天未联系提醒',
                            'contentText' => '<p>客户：#客户_公司名称#<br />您已超过60天未联系该成交客户，请及时进行跟进。</p>',
                            'user_field' => [
                                '4.user_id',
                            ],
                            'addition_user' => [],
                            'addition_user_info' => [],
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_CUSTOM,
                'criteria' => '(1 AND (2 OR 3))',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [],
            ],
            1010 => [
                'id' => 1010,
                'group_id' => \Constants::TYPE_COMPANY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '客户资料补充提醒',
                'description' => '新建客户5天后，客户来源数据为空或者客户国家为空，自动给跟进人系统提醒',
                'refer_type' =>\Constants::TYPE_COMPANY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_COMPANY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    "start_date" => [
                        "field" => "archive_time",
                        "value" => 5,
                        "operator" => WorkflowConstant::DATE_TYPE_OPERATOR_AFTER,
                        "date_type" => WorkflowConstant::DATE_TYPE_DYNAMIC,
                        "field_type" => CustomFieldService::FIELD_TYPE_DATETIME,
                        "refer_type" => \Constants::TYPE_COMPANY
                    ],
                    "regular_type" => WorkflowConstant::REGULAR_TYPE_ONCE,
                    "cycle_type" => WorkflowConstant::CYCLE_TYPE_ONCE
                ],
                'filters' => [
                    [
                        "field" => "trail_status",
                        "field_type" => CustomFieldService::FIELD_TYPE_SELECT,
                        "operator" => WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                        "refer_type" => \Constants::TYPE_COMPANY,
                        "value" => '',
                        "filter_no" => 1
                    ],
                    [
                        "field" => "group_id",
                        "field_type" => CustomFieldService::FIELD_TYPE_SELECT,
                        "operator" => WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                        "refer_type" => \Constants::TYPE_COMPANY,
                        "value" => '',
                        "filter_no" => 2
                    ],
                    [
                        "unit" => "",
                        "field" => "country",
                        "field_type" => CustomFieldService::FIELD_TYPE_SELECT,
                        "operator" => WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                        "refer_type" => \Constants::TYPE_COMPANY,
                        "value" => '',
                        "filter_no" => 3
                    ],
                    [
                        "unit" => "",
                        "field" => "origin_list",
                        "field_type" => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        "operator" => WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                        "refer_type" => \Constants::TYPE_COMPANY,
                        "value" => '',
                        "filter_no" => 4
                    ]
                ],
                'handlers' => [
                    [
                        "type" => WorkflowConstant::HANDLER_TYPE_EMAIL_NOTIFY,
                        "config" => [
                            "addition_user" => [],
                            "addition_email" => [],
                            "user_field" => ["4.user_id"],
                            "subject" => "【客户资料补充提醒】",
                            "content" => "<p>客户名称：{{4.name}}，客户阶段、客户分组、国家地区、客户来源字段未补充完全，请及时处理，以免遗漏；</p>",
                            "subjectText" => "【客户资料补充提醒】",
                            "contentText" => "<p>客户名称：#客户_公司名称#，客户阶段、客户分组、国家地区、客户来源字段未补充完全，请及时处理，以免遗漏；</p>",
                            "channel" => [
                                "email" => 1,
                                "message" => 1,
                                "app" => 1,
                                "desktop" => 1
                            ]
                        ]
                    ]
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_OR,
                'criteria' => '(1 OR 2 OR 3 OR 4)',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [],
            ],
            1013 => [
                'id' => 1013,
                'group_id' => \Constants::TYPE_COMPANY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_MODIFY_FIELD,
                'name' => '成交客户阶段自动变更',
                'description' => '首次成交的客户自动变更为成交客户阶段',
                'refer_type' =>\Constants::TYPE_COMPANY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_COMPANY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_REPEAT,
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_DAY,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => date("Y-m-d", strtotime("+1 day")),
                    ],
                    'end_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => '2999-12-31',
                    ],
                ],
                'filters' => [
                    [
                        'field' => 'performance_order_count',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => 1,
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'success_opportunity_count',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => 1,
                        'filter_no' => 2,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => 'field',
                        'config' => [
                            [
                                'field' => 'trail_status',
                                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                'value' => '',
                                'refer_type' => \Constants::TYPE_COMPANY,
                                'uiType' => '1',
                                'unit' => '',
                                'label' => '',
                            ],
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_OR,
                'criteria' => '(1 OR 2)',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [],
            ],
            1014 => [
                'id' => 1014,
                'group_id' => \Constants::TYPE_COMPANY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_MODIFY_FIELD,
                'name' => '复购客户阶段自动变更',
                'description' => '成交次数>=2的客户自动变更为复购客户阶段',
                'refer_type' =>\Constants::TYPE_COMPANY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_COMPANY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_REPEAT,
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_DAY,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => date("Y-m-d", strtotime("+1 day")),
                    ],
                    'end_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => '2999-12-31',
                    ],
                ],
                'filters' => [
                    [
                        'field' => 'performance_order_count',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => 2,
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'success_opportunity_count',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => 2,
                        'filter_no' => 2,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => 'field',
                        'config' => [
                            [
                                'field' => 'trail_status',
                                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                'value' => '',
                                'refer_type' => \Constants::TYPE_COMPANY,
                                'uiType' => '1',
                                'unit' => '',
                                'label' => '',
                            ],
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_OR,
                'criteria' => '(1 OR 2)',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [],
            ],
            1015 => [
                'id' => 1015,
                'group_id' => \Constants::TYPE_OPPORTUNITY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '商机客户公海流转提醒',
                'description' => '客户挂有进行中商机，当被流转到公海时，会有邮件进行提醒',
                'refer_type' => \Constants::TYPE_COMPANY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_COMPANY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'field' => 'user_id',
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => '',
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'user_id',
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => '',
                        'filter_no' => 2,
                    ],
                    [
                        'unit' => '',
                        'field' => 'ongoing_opportunity_count',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => 0,
                        'filter_no' => 3,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_EMAIL_NOTIFY,
                        'config' => [
                            'addition_user' => [],
                            'addition_email' => [],
                            'user_field' => [
                                '4.last_owner',
                            ],
                            'subject' => '有进行中商机的客户【{{4.name}}】被流转到公海',
                            'content' => '<p>【{{4.name}}】有{{4.ongoing_opportunity_count}}个进行中的商机，被流转到公海，请关注！</p>',
                            'channel' => [
                                'email' => 1,
                                'message' => 1,
                                'app' => 1,
                                'desktop' => 1,
                            ],
                            'contentText' => '<p>【#客户_公司名称#】有#客户_进行中的商机数#个进行中的商机，被流转到公海，请关注！</p>',
                            'subjectText' => '有进行中商机的客户【#客户_公司名称#】被流转到公海',
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2 AND 3)',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
                ],
            ],
            907 => [
                'id' => 907,
                'group_id' => \Constants::TYPE_OPPORTUNITY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '大额商机结束前日程提醒',
                'description' => '大额商机的结束日期前一周，自动发送系统通知提醒相关人员持续跟进（可选择日程/系统/邮件通知）',
                'refer_type' => \Constants::TYPE_OPPORTUNITY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_OPPORTUNITY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_ONCE,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_DYNAMIC,
                        'field' => 'account_date',
                        'field_name' => '结束日期',
                        'operator' => WorkflowConstant::DATE_TYPE_OPERATOR_BEFORE,
                        'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'value' => 7,
                    ],
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_ONCE,
                ],
                'filters' => [
                    [
                        'field' => 'stage_type',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'value' => [
                            Stage::STAGE_ON_GOING_STATUS
                        ],
                        'filter_no' => 1,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_SCHEDULE,
                        'config' => [
                            'type_id' => 1,
                            'content' => '<p>商机：{{9.name}} 7天后结单，请及时跟进。</p>',
                            'user_field' => [
                                '9.handler',
                                '9.main_user',
                            ],
                            'addition_user' => [],
                            'start_time' => [
                                'field' => 'workflow_trigger_time',
                                'value' => 0,
                            ],
                            'end_time' => [
                                'field' => 'workflow_trigger_time',
                                'value' => 1,
                            ],
                            'relate_refer' => 1,
                            'notify' => [
                                [
                                    'type' => 'day',
                                    'time' => '',
                                ],
                            ]
                        ]
                    ]
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
                ],
            ],
            902 => [
                'id' => 902,
                'group_id' => \Constants::TYPE_OPPORTUNITY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '商机赢单后系统自动通知',
                'description' => '商机赢单后可通过多种方式通知指定人员按需配置（可选择日程/系统/邮件通知）',
                'refer_type' => \Constants::TYPE_OPPORTUNITY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_OPPORTUNITY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'filter_no' => 1,
                        'field' => 'stage_type',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 2,
                        'field' => 'stage_type',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'value' => Stage::STAGE_WIN_STATUS,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_EMAIL_NOTIFY,
                        'config' => [
                            'subject' => '商机赢单通知-【{{9.serial_id}}】{{9.name}}',
                            'subjectText' => '商机赢单通知-【#商机_商机编号#】#商机_商机名称#',
                            'content' => "【{{9.serial_id}}】{{9.name}} 商机已赢单。销售金额：{{9.amount}}",
                            'contentText' => "【#商机_商机编号#】#商机_商机名称# 商机已赢单。销售金额：#商机_销售金额#",
                            'user_field' => [
                            ],
                            'user_email' => [],
                            'addition_email' => [],
                            'channel' => [
                                'browser' => 0,
                                'email' => 1,
                                'message' => 0,
                                'app' => 0,
                                'desktop' => 0,
                            ],
                        ]
                    ]
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1 AND 2',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
                ],
            ],
            1020 => [
                'id' => 1020,
                'group_id' => \Constants::TYPE_OPPORTUNITY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '成交商机售后提醒',
                'description' => '赢单后10天，自动生成日程提醒业务员问候买家产品是否有问题（可选择日程/系统/邮件通知）',
                'refer_type' => \Constants::TYPE_OPPORTUNITY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_OPPORTUNITY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_ONCE,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_DYNAMIC,
                        'field' => 'succeed_time',
                        'field_name' => '赢单时间',
                        'operator' => WorkflowConstant::DATE_TYPE_OPERATOR_AFTER,
                        'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'value' => 10,
                    ],
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_ONCE,
                ],
                'filters' => [
                    [
                        'field' => 'stage_type',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'value' => 2,
                        'filter_no' => 1,
                    ]
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_SCHEDULE,
                        'config' => [
                            'type_id' => 1,
                            'content' => '<p>商机：{{9.name}} 已过去10天，请联系客户沟通产品质量情况</p>',
                            'user_field' => [
                                '9.handler',
                                '9.main_user',
                            ],
                            'addition_user' => [],
                            'start_time' => [
                                'field' => 'workflow_trigger_time',
                                'value' => 0,
                            ],
                            'end_time' => [
                                'field' => 'workflow_trigger_time',
                                'value' => 1,
                            ],
                            'relate_refer' => 1,
                            'notify' => [
                                [
                                    'type' => 'day',
                                    'time' => '',
                                ],
                            ],
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
                ]
            ],
            1021 => [
                'id' => 1021,
                'group_id' => \Constants::TYPE_OPPORTUNITY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_MODIFY_FIELD,
                'name' => '客户流转公海后商机输单变更',
                'description' => '客户掉公海商机自动变更到输单状态，输单原因变更为：跟进不及时',
                'refer_type' => \Constants::TYPE_OPPORTUNITY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_OPPORTUNITY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_REPEAT,
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_DAY,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => date("Y-m-d", strtotime("+1 day")),
                    ],
                    'end_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => '2999-12-31',
                    ],
                ],
                'filters' => [
                    [
                        'field' => 'user_id',
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => '',
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'stage_type',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'value' => [
                            Stage::STAGE_ON_GOING_STATUS
                        ],
                        'filter_no' => 2,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_UPDATE_FIELD,
                        'config' => [
                            [
                                'field' => 'fail_type',
                                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                'value' => '',
                                'refer_type' => \Constants::TYPE_OPPORTUNITY,
                                'uiType' => '1',
                            ]
                        ]
                    ]
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1 AND 2',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
                ]
            ],
            1022 => [
                'id' => 1022,
                'group_id' => \Constants::TYPE_OPPORTUNITY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '重要商机流入公海提醒',
                'description' => '商机阶段在PI等重要阶段时，商机客户在7天后准备移入公海，系统会自动在日程或者邮件生成提醒',
                'refer_type' => \Constants::TYPE_OPPORTUNITY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_OPPORTUNITY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_ONCE,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_DYNAMIC,
                        'field' => 'next_move_to_public_date',
                        'field_name' => '下次移入公海日期',
                        'operator' => WorkflowConstant::DATE_TYPE_OPERATOR_BEFORE,
                        'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => 7,
                    ],
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_ONCE,
                ],
                'filters' => [
                    [
                        'field' => 'stage',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'value' => [],
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'stage_type',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'value' => [
                            Stage::STAGE_ON_GOING_STATUS
                        ],
                        'filter_no' => 2,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_EMAIL_NOTIFY,
                        'config' => [
                            'addition_user' => [],
                            'addition_email' => [],
                            'user_field' => [
                                '4.last_owner',
                            ],
                            'subject' => '有进行中商机的客户【{{4.name}}】,7天后将被流转到公海',
                            'content' => '<p>【{{4.name}}】有{{4.ongoing_opportunity_count}}个进行中的商机，7天后将被流转到公海，请及时跟进！</p>',
                            'channel' => [
                                'email' => 1,
                                'message' => 1,
                                'app' => 1,
                                'desktop' => 1,
                            ],
                            'contentText' => '有进行中商机的客户【#客户_公司名称#】,7天后将被流转到公海',
                            'subjectText' => '<p>【#客户_公司名称#】有#客户_进行中的商机数#个进行中的商机，7天后将被流转到公海，请及时跟进！</p>'
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1 AND 2',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
                ]
            ],
            1023 => [
                'id' => 1023,
                'group_id' => \Constants::TYPE_OPPORTUNITY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_MODIFY_FIELD,
                'name' => '商机推进自动变更客户阶段',
                'description' => '将商机阶段和客户阶段关联起来，当商机推进赢单，客户阶段自动调整为成交客户',
                'refer_type' => \Constants::TYPE_OPPORTUNITY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_OPPORTUNITY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'field' => 'stage_type',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'value' => '',
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'stage_type',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'value' => Stage::STAGE_WIN_STATUS,
                        'filter_no' => 2,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_UPDATE_FIELD,
                        'config' => [
                            [
                                'field' => 'trail_status',
                                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                'value' => '',
                                'refer_type' => \Constants::TYPE_COMPANY,
                            ]
                        ]
                    ]
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1 AND 2',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
                ]
            ],
            1025 => [
                'id' => 1025,
                'group_id' => \Constants::TYPE_LEAD,
                'second_group_id' => WorkflowConstant::RECLASSIFY_AUTOMATIC_ALLOCATION,
                'name' => '根据渠道来源自动分配线索',
                'description' => '当某个渠道线索被创建时，自动轮流分配给指定业务员，通常用于按渠道跟进询盘的团队',
                'refer_type' => \Constants::TYPE_LEAD,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_LEAD],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'field' => 'origin_list',
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                        'refer_type' => \Constants::TYPE_LEAD,
                        'value' => [],
                        'filter_no' => 1,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_ASSIGN,
                        'config' => [
                            [
                                'field' => 'user_id',
                                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                                'value' => [
                                    'last_owner_ids' => '',
                                    'user_departments' => [],
                                    'user_id' => [],
                                    'exclude_user_id' => [],
                                ],
                                'method' => WorkflowConstant::HANDLER_TYPE_ASSIGN_METHOD_ITERATE_REPLACE,
                            ]
                        ]
                    ]
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_LEAD,
                ]
            ],
            704 => [
                'id' => 704,
                'group_id' => \Constants::TYPE_LEAD,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '按时跟进线索提醒',
                'description' => '超过2天没有跟进的线索（询盘）自动发邮件提醒对应的跟进人（可选择日程/系统/邮件通知）',
                'refer_type' => \Constants::TYPE_LEAD,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_LEAD],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_ONCE,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_DYNAMIC,
                        'field' => 'order_time',
                        'field_name' => '最近联系时间',
                        'operator' => WorkflowConstant::DATE_TYPE_OPERATOR_AFTER,
                        'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                        'refer_type' => \Constants::TYPE_LEAD,
                        'value' => 2,
                    ],
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_ONCE
                ],
                'filters' => [],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_NOTIFY,
                        'config' => [
                            'addition_user' => [],
                            'user_field' => [
                                '7.user_id',
                            ],
                            'subject' => '按时跟进线索提醒',
                            'content' => '<p>线索：{{7.name}}超过2天没有跟进，请关注！</p>',
                            'contentText' => '<p>线索：#线索_线索名称#超过2天没有跟进，请关注！</p>',
                            'subjectText' => '按时跟进线索提醒',
                            'channel' => [
                                'email' => 0,
                                'message' => 1,
                                'app' => 1,
                                'browser' => 1,
                                'desktop' => 1,
                            ],
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_LEAD,
                ]
            ],
            1028 => [
                'id' => 1028,
                'group_id' => \Constants::TYPE_ORDER,
                'second_group_id' => WorkflowConstant::RECLASSIFY_MODIFY_FIELD,
                'name' => '订单日期自动更新',
                'description' => '当订单状态标记为「已完成」时，将订单日期更新为当日',
                'refer_type' => \Constants::TYPE_ORDER,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_ORDER],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'field' => 'status',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => '',
                        'filter_no' => 1,
                    ],
                    [
                        'filter_no' => 2,
                        'field' => 'status',
                        'refer_type' => \Constants::TYPE_ORDER,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                        'value' => $orderEndStatusIds ?? [],
                    ]
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_UPDATE_FIELD,
                        'config' => [
                            [
                                'field' => 'account_date',
                                'field_type' => \Constants::TYPE_COMPANY,
                                'value' => [
                                    'refer_type' => null,
                                    'field' => WorkflowConstant::HANDLER_TYPE_SCHEDULE_CONFIG_FIELD_TRIGGER_TIME,
                                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                                    'value' => 0,
                                ],
                                'refer_type' => \Constants::TYPE_ORDER,
                                'uiType' => '2',
                                'unit' => '',
                                'showTypeSelect' => true,
                                'dateType' => 2,
                            ],
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1 AND 2',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_ORDER,
                ]
            ],
            1029 => [
                'id' => 1029,
                'group_id' => \Constants::TYPE_COMPANY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_AUTOMATIC_ALLOCATION,
                'name' => '久未跟进的私海客户自动流转给管理者',
                'description' => '如果业务员久未跟进私海客户，将该客户分配给管理者，让管理者手动分配给其他人跟进',
                'refer_type' => \Constants::TYPE_COMPANY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_COMPANY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_ONCE,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_DYNAMIC,
                        'field' => 'order_time',
                        'field_name' => '最近联系时间',
                        'operator' => WorkflowConstant::DATE_TYPE_OPERATOR_AFTER,
                        'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => 30,
                    ],
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_ONCE,
                ],
                'filters' => [
                    [
                        'field' => 'trail_status',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => [],
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'user_id',
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => '',
                        'filter_no' => 2,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_ASSIGN,
                        'config' => [
                            [
                                'field' => 'user_id',
                                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                                'value' => [
                                    'last_owner_ids' => '',
                                    'user_departments' => [],
                                    'user_id' => '',
                                    'exclude_user_id' => [],
                                ],
                                'method' => 'replace',
                                'label' => '',
                            ]
                        ]
                    ]
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1 AND 2',
                'icon' => 1,
                'tag' => 'new',
                'functional' => []
            ],
            1031 => [
                'id' => 1031,
                'group_id' => \Constants::TYPE_COMPANY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_AUTOMATIC_ALLOCATION,
                'name' => '将阿里国际站客户自动共享给运营人员',
                'description' => '运营人员需要查看阿里国际站客户，通过工作流实现自动共享',
                'refer_type' => \Constants::TYPE_COMPANY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_COMPANY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_REPEAT,
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_DAY,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => date("Y-m-d", strtotime("+1 day")),
                    ],
                    'end_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => '2999-12-31',
                    ],
                ],
                'filters' => [
                    [
                        'field' => 'origin_list',
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => [
                            '4',
                        ],
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'user_id',
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_NOT_IN,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => [
                            'last_owner_ids' => '',
                            'user_departments' => [],
                            'user_id' => '',
                            'exclude_user_id' => [],
                            'is_all' => 0,
                        ],
                        'filter_no' => 2,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_ASSIGN,
                        'config' => [
                            [
                                'field' => 'user_id',
                                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                                'value' => [
                                    'last_owner_ids' => '',
                                    'user_departments' => [],
                                    'user_id' => '',
                                    'exclude_user_id' => [],
                                ],
                                'method' => 'share',
                                'label' => '',
                            ]
                        ]
                    ]
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1 AND 2',
                'icon' => 1,
                'tag' => 'new',
                'functional' => []
            ],
            1032 => [
                'id' => 1032,
                'group_id' => \Constants::TYPE_ORDER,
                'second_group_id' => WorkflowConstant::RECLASSIFY_AUTOMATIC_ALLOCATION,
                'name' => '自动变更订单当前处理人',
                'description' => '当订单流转到xx状态时，将订单当前处理人自动变更为指定人员',
                'refer_type' => \Constants::TYPE_ORDER,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_ORDER],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'field' => 'status',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => '',
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'status',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => '',
                        'filter_no' => 2,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_ASSIGN,
                        'config' => [
                            [
                                'field' => 'handler',
                                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                                'value' => [
                                    'last_owner_ids' => '',
                                    'user_departments' => [],
                                    'user_id' => '',
                                    'exclude_user_id' => [],
                                ],
                                'method' => WorkflowConstant::HANDLER_TYPE_ASSIGN_METHOD_REPLACE,
                                'role_id' => '',
                            ],
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1 AND 2',
                'icon' => 1,
                'tag' => 'new',
                'functional' => []
            ],
            1033 => [
                'id' => 1033,
                'group_id' => \Constants::TYPE_OPPORTUNITY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_AUTOMATIC_ALLOCATION,
                'name' => '自动共享商机团队成员',
                'description' => '当商机流转到xx阶段时，将跟单人员分配商机团队成员',
                'refer_type' => \Constants::TYPE_OPPORTUNITY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_OPPORTUNITY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'field' => 'stage',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'value' => '',
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'stage',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'value' => [],
                        'filter_no' => 2,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_ASSIGN,
                        'config' => [
                            [
                                'field' => 'handler',
                                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                                'value' => [
                                    'last_owner_ids' => '',
                                    'user_departments' => [],
                                    'user_id' => '',
                                    'exclude_user_id' => [],
                                ],
                                'method' => WorkflowConstant::HANDLER_TYPE_ASSIGN_METHOD_SHARE,
                                'role_id' => '1',
                            ]
                        ]
                    ]
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1 AND 2',
                'icon' => 1,
                'tag' => 'new',
                'functional' => []
            ],

            // 最热
            911 => [
                'id' => 911,
                'group_id' => \Constants::TYPE_OPPORTUNITY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_DING_TALK,
                'name' => '商机赢单战报推送',
                'description' => '每次商机赢单后，自动推送钉钉群通知',
                'refer_type' =>\Constants::TYPE_OPPORTUNITY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_OPPORTUNITY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'filter_no' => 1,
                        'field' => 'stage_type',
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 2,
                        'field' => 'stage_type',
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'value' => Stage::STAGE_WIN_STATUS,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_DING_TALK,
                        'config' => [
                            'type' => DingTalkProcessor::DINGTALK_MESSAGE_TYPE_POSTER,
                            'title' => '赢单之战_以热爱敬无畏',
                            "pic_url" => '',
                            'user_field' => "9.main_user",
                            'amount_field' => 'amount',
                            'content' => "<p>恭喜{{9.department}}的{{9.main_user}}拿下一单{{4.country}}的客户{{4.name}}。</p>\n<p>赢单日期：{{9.account_date}}</p>",
                            "webhooks" => [],
                        ]
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2)',
                'icon' => 1,
                'tag' => 'hot',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_ORDER,
                ],
            ],
            1048 => [
                'id' => 1048,
                'group_id' => \Constants::TYPE_COMPANY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_AUTOMATIC_ALLOCATION,
                'name' => '无人跟进公海客户每周一自动分配',
                'description' => '将久未联系（例如180天）的公海客户在每周一自动轮流分配给多个业务员盘活',
                'refer_type' => \Constants::TYPE_COMPANY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_COMPANY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_REPEAT,
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_WEEK,
                    'cycle_value' => [
                        1,
                    ],
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'field_name' => '具体时间',
                        'value' => date("Y-m-d", strtotime("+1 day")),
                    ],
                    'end_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => '2999-12-31',
                    ],
                ],
                'filters' => [
                    [
                        'field' => 'user_id',
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'order_time',
                        'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EARLIER,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => -180,
                        'filter_no' => 2,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_ASSIGN,
                        'config' => [
                            [
                                'field' => 'user_id',
                                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                                'value' => [
                                    'last_owner_ids' => '',
                                    'user_departments' => [],
                                    'user_id' => [],
                                    'exclude_user_id' => [],
                                ],
                                'method' => WorkflowConstant::HANDLER_TYPE_ASSIGN_METHOD_ITERATE_REPLACE,
                                'label' => '',
                            ],
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2)',
                'icon' => 1,
                'tag' => 'hot',
                'functional' => [],
            ],
            1009 => [
                'id' => 1009,
                'group_id' => \Constants::TYPE_COMPANY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => 'L3&L4客户的跟进提醒',
                'description' => '阿里国际站L3/L4高质量买家做日程提醒',
                'refer_type' =>\Constants::TYPE_COMPANY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_COMPANY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_ONCE,
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_ONCE,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_DYNAMIC,
                        'value' => 30,
                        'field' => 'recent_follow_up_time',
                        'operator' => WorkflowConstant::DATE_TYPE_OPERATOR_AFTER,
                        'field_type' => 10,
                        'refer_type' => \Constants::TYPE_COMPANY,
                    ]
                ],
                'filters' => [
                    [
                        "unit" => "",
                        "field" => "growth_level",
                        "field_type" => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        "operator" => WorkflowConstant::FILTER_OPERATOR_IN,
                        "refer_type" => \Constants::TYPE_COMPANY,
                        "value" => [3, 4],
                        "filter_no" => 1
                    ]
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_SCHEDULE,
                        'config' => [
                            'notify' => [
                                [
                                    'time' => '',
                                    'type' => 'day'
                                ]
                            ],
                            'content' => "<p>L3&amp;L4客户{{4.name}} 30天未联系，请及时跟进。</p>",
                            'type_id' => 1,
                            'end_time' => [
                                'field' => 'workflow_trigger_time',
                                'value' => 1
                            ],
                            'start_time' => [
                                'field' => 'workflow_trigger_time',
                                'value' => 0
                            ],
                            'user_field' => ['4.user_id'],
                            "relate_refer" => 1,
                            "addition_user" => []
                        ]
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1',
                'icon' => 1,
                'tag' => 'hot',
                'functional' => [],
            ],
            1011 => [
                'id' => 1011,
                'group_id' => \Constants::TYPE_COMPANY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '重要客户跟进提醒',
                'description' => '当重要客户的未联系天数>30天时，自动给跟进人系统提醒',
                'refer_type' =>\Constants::TYPE_COMPANY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_COMPANY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    "regular_type" => WorkflowConstant::REGULAR_TYPE_ONCE,
                    "cycle_type" => WorkflowConstant::CYCLE_TYPE_ONCE,
                    "start_date" => [
                        "date_type" => WorkflowConstant::DATE_TYPE_DYNAMIC,
                        "field" => "order_time",
                        "operator" => WorkflowConstant::DATE_TYPE_OPERATOR_AFTER,
                        "field_type" => CustomFieldService::FIELD_TYPE_DATETIME,
                        "refer_type" => \Constants::TYPE_COMPANY,
                        "value" => 30
                    ]
                ],
                'filters' => [
                    [
                        "field" => "trail_status",
                        "field_type" => CustomFieldService::FIELD_TYPE_SELECT,
                        "operator" => WorkflowConstant::FILTER_OPERATOR_IN,
                        "refer_type" => \Constants::TYPE_COMPANY,
                        "value" => [],
                        "filter_no" => 1
                    ]
                ],
                'handlers' => [
                    [
                        "type" => "notify",
                        "config" => [
                            "addition_user" => [],
                            "user_field" => ["4.user_id"],
                            "subject" => "重要客户跟进提醒",
                            "content" => "<p>客户：{4.name}<br/>您已超过30天未联系该重要客户，请及时进行跟进。</p>",
                            'contentText' => '<p>客户：#客户_公司名称#<br />您已超过30天未联系该重要客户，请及时进行跟进。</p>',
                            'subjectText' => '重要客户跟进提醒',
                            "channel" => [
                                "email" => 0,
                                "message" => 1,
                                "app" => 1,
                                "browser" => 1,
                                "desktop" => 1
                            ]
                        ]
                    ]
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1',
                'icon' => 1,
                'tag' => 'hot',
                'functional' => [],
            ],
            914 => [
                'id' => 914,
                'group_id' => \Constants::TYPE_OPPORTUNITY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_WECOM,
                'name' => '商机赢单战报推送',
                'description' => '每次商机赢单后，自动推送企微群通知',
                'refer_type' => \Constants::TYPE_OPPORTUNITY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_OPPORTUNITY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'filter_no' => 1,
                        'field' => 'stage_type',
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 2,
                        'field' => 'stage_type',
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'value' => Stage::STAGE_WIN_STATUS,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_QYWECHAT,
                        'config' => [
                            'type' => DingTalkProcessor::DINGTALK_MESSAGE_TYPE_POSTER,
                            'title' => '赢单之战_以热爱敬无畏',
                            "pic_url" => '',
                            'user_field' => "9.main_user",
                            'amount_field' => 'amount',
                            'content' => "<p>恭喜{{9.department}}的{{9.main_user}}拿下一单{{4.country}}的客户{{4.name}}。</p>\n<p>赢单日期：{{9.account_date}}</p>",
                            "webhooks" => [],
                        ]
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2 )',
                'icon' => 1,
                'tag' => 'hot',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_ORDER,
                ],
            ],
            915 => [
                'id' => 915,
                'group_id' => \Constants::TYPE_OPPORTUNITY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_WECOM,
                'name' => '大单商机赢单战报',
                'description' => '赢单商机金额大于X时，自动推送企微群通知',
                'refer_type' => \Constants::TYPE_OPPORTUNITY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_OPPORTUNITY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'filter_no' => 1,
                        'field' => 'stage_type',
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 2,
                        'field' => 'stage_type',
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'value' => Stage::STAGE_WIN_STATUS,
                    ],
                    [
                        'filter_no' => 3,
                        'field' => 'amount',
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER,
                        'value' => '',
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_QYWECHAT,
                        'config' => [
                            'type' => DingTalkProcessor::DINGTALK_MESSAGE_TYPE_POSTER,
                            'title' => '成交之战_势不可挡',
                            "pic_url" => '',
                            'user_field' => "9.main_user",
                            'amount_field' => 'amount',
                            'content' => "<p>重磅消息，有一笔大额订单成交！</p>\n<p>恭喜{{9.department}}的{{9.main_user}}成功拿下一单{{4.country}}的大单客户{{4.name}}，努力定会有回报。</p>\n<p>赢单日期：{{9.account_date}}<p/>",
                            "webhooks" => [],
                        ]
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2 AND 3)',
                'icon' => 1,
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_ORDER,
                ],
            ],
            901 => [
                'id' => 901,
                'group_id' => \Constants::TYPE_OPPORTUNITY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '滞留商机跟进提醒',
                'description' => '不同阶段商机，停留时间超过一段期限，需要发邮件提醒跟进人做提醒；例如样品阶段超过7天做跟进人邮件提醒',
                'refer_type' => \Constants::TYPE_OPPORTUNITY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_OPPORTUNITY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_REPEAT,
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_DAY,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => date("Y-m-d", strtotime("+1 day")),
                    ],
                    'end_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => '2999-12-31',
                    ],
                ],
                'filters' => [
                    [
                        'filter_no' => 1,
                        'field' => 'stage_edit_time',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER,
                        'value' => 7,
                    ],
                    [
                        'filter_no' => 2,
                        'field' => 'stage_type',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_NOT_IN,
                        'value' => [
                            Stage::STAGE_WIN_STATUS,
                            Stage::STAGE_FAIL_STATUS
                        ],
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_NOTIFY,
                        'config' => [
                            'subject' => '滞留商机跟进提醒',
                            'subjectText' => '滞留商机跟进提醒',
                            'content' => "商机：【{{9.serial_id}}】{{9.name}} 停留在 {{9.stage}} 销售阶段{{9.stage_edit_time}}天了，请及时跟进。",
                            'contentText' => "商机：【#商机_商机编号#】#商机_商机名称# 停留在 #商机_销售阶段# 销售阶段#商机_当前阶段停留天数#天了，请及时跟进。",
                            'user_field' => [
                                '9.handler',
                                '9.main_user',
                            ],
                            'addition_user' => [],
                            'channel' => [
                                'browser' => 1,
                                'email' => 0,
                                'message' => 1,
                                'app' => 1,
                                'desktop' => 0,
                            ],
                        ]
                    ]
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1 AND 2',
                'icon' => 1,
                'tag' => 'hot',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
                ],
            ],
            1019 => [
                'id' => 1019,
                'group_id' => \Constants::TYPE_OPPORTUNITY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '大额商机邮件通知',
                'description' => '创建大额商机（例如20万美金），自动发送邮件通知、系统提醒通知给主管，引起重视协助该业务完成',
                'refer_type' => \Constants::TYPE_OPPORTUNITY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_OPPORTUNITY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'unit' => 'USD',
                        'field' => 'amount',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER,
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'value' => 200000,
                        'filter_no' => 1,
                    ],
                    [
                        'unit' => 'CNY',
                        'field' => 'amount',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'value' => '',
                        'filter_no' => 2,
                    ],
                    [
                        'field' => 'stage_type',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_NOT_IN,
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'value' => [
                            Stage::STAGE_FAIL_STATUS
                        ],
                        'filter_no' => 3,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_EMAIL_NOTIFY,
                        'config' => [
                            'addition_user' => [],
                            'user_field' => [],
                            'subject' => '大额商机创建通知-【{{9.serial_id}}】{{9.name}}',
                            'content' => '<p>大额商机【{{9.serial_id}}】{{9.name}}<br />销售金额：{{9.amount}}{{9.currency}}<br />创建人：{{9.create_user}}<br />负责人：{{9.main_user}}<br />团队成员：{{9.handler}}</p>',
                            'channel' => [
                                'email' => 0,
                                'message' => 1,
                                'app' => 1,
                                'browser' => 1,
                                'desktop' => 1,
                            ],
                            'contentText' => '<p>大额商机【#商机_商机编号#】#商机_商机名称#<br />销售金额：#商机_销售金额##商机_币种#<br />创建人：#商机_创建人#<br />负责人：#商机_负责人#<br />团队成员：#商机_团队成员#</p>',
                            'subjectText' => '大额商机创建通知-【#商机_商机编号#】#商机_商机名称#',
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2 AND 3)',
                'icon' => 0,
                'tag' => 'hot',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
                ],
            ],
            1024 => [
                'id' => 1024,
                'group_id' => \Constants::TYPE_LEAD,
                'second_group_id' => WorkflowConstant::RECLASSIFY_AUTOMATIC_ALLOCATION,
                'name' => '线索自动分配',
                'description' => '将新建的线索自动轮流分配给多个业务员',
                'refer_type' => \Constants::TYPE_LEAD,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_LEAD],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'field' => 'user_id',
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'refer_type' => \Constants::TYPE_LEAD,
                        'value' => [
                            'last_owner_ids' => '',
                            'user_departments' => '',
                            'user_id' => '',
                            'exclude_user_id' => []
                        ],
                        'filter_no' => 1,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_ASSIGN,
                        'config' => [
                            [
                                'field' => 'user_id',
                                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                                'value' => [
                                    'last_owner_ids' => '',
                                    'user_departments' => [],
                                    'user_id' => [],
                                    'exclude_user_id' => [],
                                ],
                                'method' => WorkflowConstant::HANDLER_TYPE_ASSIGN_METHOD_ITERATE_REPLACE,
                            ]
                        ]
                    ]
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1',
                'icon' => 1,
                'tag' => 'hot',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_LEAD,
                ]
            ],
            1027 => [
                'id' => 1027,
                'group_id' => \Constants::TYPE_LEAD,
                'second_group_id' => WorkflowConstant::RECLASSIFY_AUTOMATIC_ALLOCATION,
                'name' => '未及时跟进线索自动分配',
                'description' => '超过7天没有跟进的线索（询盘）自动轮流分配给其他用户',
                'refer_type' => \Constants::TYPE_LEAD,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_LEAD],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_ONCE,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_DYNAMIC,
                        'field' => 'order_time',
                        'field_name' => '最近联系时间',
                        'operator' => WorkflowConstant::DATE_TYPE_OPERATOR_AFTER,
                        'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                        'refer_type' => \Constants::TYPE_LEAD,
                        'value' => 7,
                    ],
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_ONCE
                ],
                'filters' => [],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_ASSIGN,
                        'config' => [
                            [
                                'field' => 'user_id',
                                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                                'value' => [
                                    'last_owner_ids' => '',
                                    'user_departments' => [],
                                    'user_id' => [],
                                    'exclude_user_id' => [],
                                ],
                                'method' => WorkflowConstant::HANDLER_TYPE_ASSIGN_METHOD_ITERATE_REPLACE,
                            ]
                        ]
                    ]
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1',
                'icon' => 1,
                'tag' => 'hot',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_LEAD,
                ]
            ],

            // 普通
            301 => [
                'id' => 301,
                'group_id' => \Constants::TYPE_QUOTATION,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '创建大额报价单-邮件通知',
                'description' => '创建大额报价单，自动发送邮件通知给相关人员，引起重视（也可选择日程通知/系统通知）',
                'refer_type' => \Constants::TYPE_QUOTATION,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_QUOTATION],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'filter_no' => 1,
                        'field' => 'amount',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 2,
                        'unit' => 'USD',
                        'field' => 'amount',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'refer_type' => \Constants::TYPE_QUOTATION,
                        'value' => '',
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_EMAIL_NOTIFY,
                        'config' => [
                            'subject' => '大额报价单创建通知-【{{3.quotation_no}}】{{3.name}}',
                            'content' => "大额报价单：【{{3.quotation_no}}】{{3.name}}<br/>报价单金额：{{3.amount}}{{3.currency}}<br/>创建人：{{3.create_user}}<br/>收件人：",
                            'contentText' => '<p>大额报价单：【#报价单_报价单号#】#报价单_报价单名称#<br />报价单金额：#报价单_报价单金额##报价单_币种#<br />创建人：#报价单_创建人#<br />收件人：</p>',
                            'subjectText' => '大额报价单创建通知-【#报价单_报价单号#】#报价单_报价单名称#',
                            'user_field' => [],
                            'user_email' => [],
                            'addition_email' => [],
                            'channel' => [
                                'browser' => 0,
                                'email' => 1,
                                'message' => 0,
                                'app' => 0,
                                'desktop' => 0,
                            ],
                        ]
                    ]
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2)',
                'icon' => 1,
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_QUOTATION,
                ],
            ],
            912 => [
                'id' => 912,
                'group_id' => \Constants::TYPE_OPPORTUNITY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_DING_TALK,
                'name' => '大单商机赢单战报',
                'description' => '赢单商机金额≥X时，自动推送钉钉群通知',
                'refer_type' =>\Constants::TYPE_OPPORTUNITY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_OPPORTUNITY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'filter_no' => 1,
                        'field' => 'stage_type',
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 2,
                        'field' => 'stage_type',
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'value' => Stage::STAGE_WIN_STATUS,
                    ],
                    [
                        'filter_no' => 3,
                        'field' => 'amount',
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER,
                        'value' => '',
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_DING_TALK,
                        'config' => [
                            'type' => DingTalkProcessor::DINGTALK_MESSAGE_TYPE_POSTER,
                            'title' => '成交之战_势不可挡',
                            "pic_url" => '',
                            'user_field' => "9.main_user",
                            'amount_field' => 'amount',
                            'content' => "<p>重磅消息，有一笔大额订单成交！</p>\n<p>恭喜{{9.department}}的{{9.main_user}}成功拿下一单{{4.country}}的大单客户{{4.name}}，努力定会有回报。</p>\n<p>赢单日期：{{9.account_date}}<p/>",
                            "webhooks" => [],
                        ]
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2 AND 3)',
                'icon' => 1,
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_ORDER,
                ],
            ],
            913 => [
                'id' => 913,
                'group_id' => \Constants::TYPE_OPPORTUNITY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_DING_TALK,
                'name' => '某个渠道商机赢单金额突破XX喜报',
                'description' => '通过指定渠道来源的客户总的商机赢单金额大于等于X时，自动推送钉钉群通知，帮助业务员对指定渠道引起重视',
                'refer_type' =>\Constants::TYPE_OPPORTUNITY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_OPPORTUNITY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'filter_no' => 1,
                        'field' => 'stage_type',
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 2,
                        'field' => 'stage_type',
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'value' => Stage::STAGE_WIN_STATUS,
                    ],
                    [
                        'filter_no' => 3,
                        'field' => 'amount',
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 4,
                        'field' => 'origin_list',
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'value' => '',
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_DING_TALK,
                        'config' => [
                            'type' => DingTalkProcessor::DINGTALK_MESSAGE_TYPE_GRAPHIC,
                            'title' => '赢单之战_开辟新渠道',
                            "pic_url" => $dingTalkPreparePicture,
                            'user_field' => "9.main_user",
                            'amount_field' => 'amount',
                            'content' => "<p>恭喜{{9.department}}的{{9.main_user}}拿下一单{{4.country}}的客户。</p>\n<p>商机金额：{{9.amount}}</p>\n<p>客户名称：{{4.name}}</p>\n<p>客户来源：{{4.origin_list}}</p>\n<p>赢单日期：{{9.account_date}}<p/>",
                            "webhooks" => [],
                        ]
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2 AND 3 AND 4)',
                'icon' => 1,
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
                ],
            ],
            208 => [
                'id' => 208,
                'group_id' => \Constants::TYPE_ORDER,
                'name' => '成交订单时战报推送',
                'description' => '每次成交订单后，自动推送钉钉群通知',
                'refer_type' =>\Constants::TYPE_ORDER,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_ORDER],
                'second_group_id' => WorkflowConstant::RECLASSIFY_DING_TALK,
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'filter_no' => 1,
                        'field' => 'status',
                        'refer_type' => \Constants::TYPE_ORDER,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 2,
                        'field' => 'status',
                        'refer_type' => \Constants::TYPE_ORDER,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                        'value' => $orderEndStatusIds ?? [],
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_DING_TALK,
                        'config' => [
                            'type' => DingTalkProcessor::DINGTALK_MESSAGE_TYPE_POSTER,
                            'title' => '今日喜报',
                            "pic_url" => '',
                            'user_field' => "2.users",
                            'amount_field' => 'amount',
                            'content' => "<p>恭喜{{2.departments}}的{{2.users}}签下一单{{4.country}}的客户{{4.name}}</p>\n<p>订单日期：{{2.account_date}}</p>",
                            "webhooks" => [],
                        ]
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2)',
                'icon' => 1,
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_ORDER,
                ],
            ],
            209 => [
                'id' => 209,
                'group_id' => \Constants::TYPE_ORDER,
                'name' => '大额订单成交战报',
                'description' => '成交订单金额≥X时，自动推送钉钉群通知',
                'refer_type' =>\Constants::TYPE_ORDER,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_ORDER],
                'second_group_id' => WorkflowConstant::RECLASSIFY_DING_TALK,
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'filter_no' => 1,
                        'field' => 'status',
                        'refer_type' => \Constants::TYPE_ORDER,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 2,
                        'field' => 'status',
                        'refer_type' => \Constants::TYPE_ORDER,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                        'value' => $orderEndStatusIds ?? [],
                    ],
                    [
                        'filter_no' => 3,
                        'field' => 'amount',
                        'refer_type' => \Constants::TYPE_ORDER,
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER,
                        'value' => '',
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_DING_TALK,
                        'config' => [
                            'type' => DingTalkProcessor::DINGTALK_MESSAGE_TYPE_POSTER,
                            'title' => '大单来袭_舍我其谁',
                            "pic_url" => '',
                            'user_field' => "2.users",
                            'amount_field' => 'amount',
                            'content' => "<p>重磅消息，有一笔大额订单成交！</p>\n<p>恭喜{{2.departments}}的{{2.users}}签下{{4.country}}的大单客户{{4.name}}，努力定会有回报。</p>\n<p>订单日期：{{2.account_date}}</p> ",
                            "webhooks" => [],
                        ]
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2 AND 3)',
                'icon' => 1,
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_ORDER,
                ],
            ],
            210 => [
                'id' => 210,
                'group_id' => \Constants::TYPE_ORDER,
                'name' => '某个渠道成交订单金额突破XX喜报',
                'description' => '通过某个渠道来源的客户总的成交订单金额大于等于X时，自动推送钉钉群通知，帮助业务员对指定渠道引起重视',
                'refer_type' =>\Constants::TYPE_ORDER,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_ORDER],
                'second_group_id' => WorkflowConstant::RECLASSIFY_DING_TALK,
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'filter_no' => 1,
                        'field' => 'status',
                        'refer_type' => \Constants::TYPE_ORDER,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 2,
                        'field' => 'status',
                        'refer_type' => \Constants::TYPE_ORDER,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                        'value' => $orderEndStatusIds ?? [],  //订单的完成状态
                    ],
                    [
                        'filter_no' => 3,
                        'field' => 'amount',
                        'refer_type' => \Constants::TYPE_ORDER,
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 4,
                        'field' => 'origin_list',
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'value' => '',
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_DING_TALK,
                        'config' => [
                            'type' => DingTalkProcessor::DINGTALK_MESSAGE_TYPE_GRAPHIC,
                            'title' => '成交之战_开辟新渠道',
                            "pic_url" => $dingTalkPreparePicture,
                            'user_field' => "2.users",
                            'amount_field' => 'amount',
                            'content' => "<p>恭喜{{2.departments}}的{{2.users}}签下一单{{4.country}}的客户。</p>\n<p>订单金额：{{2.amount}}</p>\n<p>客户名称：{{4.name}}</p>\n<p>客户来源：{{4.origin_list}}</p>\n<p>订单日期：{{2.account_date}} </p>",
                            "webhooks" => [
                            ],
                        ]
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2 AND 3 AND 4)',
                'icon' => 1,
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_ORDER,
                ],
            ],
            207 => [
                'id' => 207,
                'group_id' => \Constants::TYPE_ORDER,
                'second_group_id' => WorkflowConstant::RECLASSIFY_MODIFY_FIELD,
                'name' => '信保订单自动流转到「已完成」',
                'description' => '当信保订单的阿里订单状态处于已完成时，该订单的OKKI订单状态自动切换为「已完成」',
                'refer_type' => \Constants::TYPE_ORDER,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_ORDER],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'filter_no' => 1,
                        'field' => 'ali_status_id',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 2,
                        'field' => 'ali_status_id',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'value' => Order::ALI_ORDER_TYPE_COMPLETED,
                    ],

                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_UPDATE_FIELD,
                        'config' => [
                            [
                                'field' => 'status',
                                'value' => \common\library\invoice\Helper::getFinishOrderStatusByName($clientId),
                                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                'refer_type' => \Constants::TYPE_ORDER,
                            ]
                        ]
                    ]
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1 AND 2',
                'icon' => 1,
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_ORDER,
                ],
            ],
            1038 => [
                'id' => 1038,
                'group_id' => \Constants::TYPE_ORDER,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '订单结单日期将近提醒',
                'description' => '提醒订单的业绩归属人结单日期将近，需尽快跟进',
                'refer_type' => \Constants::TYPE_ORDER,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_ORDER],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_ONCE,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_DYNAMIC,
                        'field' => 'account_date',
                        'field_name' => '结束日期',
                        'operator' => WorkflowConstant::DATE_TYPE_OPERATOR_BEFORE,
                        'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => 7,
                    ],
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_ONCE,
                ],
                'filters' => [
                    [
                        'field' => 'status',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => [],
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'cash_collection_status',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => 3,
                        'filter_no' => 2,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_NOTIFY,
                        'config' => [
                            'addition_user' => [],
                            'user_field' => [
                                '2.users',
                                '2.handler',
                            ],
                            'subject' => '订单结单日期将近，请及时跟进',
                            'content' => '<p>订单：【{{2.order_no}}】{{2.name}} 的结单日期为{{2.account_date}}，请及时跟进。</p>',
                            'contentText' => '<p>订单：【#销售订单_订单号#】#销售订单_订单名称# 的结单日期为#销售订单_订单日期#，请及时跟进。</p>',
                            'subjectText' => '订单结单日期将近，请及时跟进',
                            'channel' => [
                                'email' => 0,
                                'message' => 1,
                                'app' => 1,
                                'browser' => 1,
                                'desktop' => 1,
                            ],
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1 AND 2',
                'icon' => 1,
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_ORDER,
                ],
            ],
            1039 => [
                'id' => 1039,
                'group_id' => \Constants::TYPE_ORDER,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '即将结单的订单及时跟进提醒',
                'description' => '订单结单当天，为订单业绩归属人新建日程，以确认订单状态（可选择日程/系统/邮件通知）',
                'refer_type' => \Constants::TYPE_ORDER,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_ORDER],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_ONCE,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_DYNAMIC,
                        'field' => 'account_date',
                        'field_name' => '订单日期',
                        'operator' => WorkflowConstant::DATE_TYPE_OPERATOR_BEFORE,
                        'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => 3,
                    ],
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_ONCE,
                ],
                'filters' => [
                    [
                        'field' => 'cash_collection_status',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => 3,
                        'filter_no' => 1,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_SCHEDULE,
                        'config' => [
                            'type_id' => 1,
                            'content' => '<p>未回款订单：{{2.name}} 将于{{2.account_date}}结单，请及时跟进。</p>',
                            'user_field' => [
                                '2.users',
                            ],
                            'addition_user' => [],
                            'start_time' => [
                                'field' => 'workflow_trigger_time',
                                'value' => 0,
                            ],
                            'end_time' => [
                                'field' => 'workflow_trigger_time',
                                'value' => 1,
                            ],
                            'relate_refer' => 1,
                            'notify' => [
                                [
                                    'type' => 'day',
                                    'time' => '',
                                ],
                            ],
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1',
                'icon' => 1,
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_ORDER,
                ],
            ],
            1046 => [
                'id' => 1046,
                'group_id' => \Constants::TYPE_ORDER,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '订单回款邮件通知',
                'description' => '当订单回款完成时，自动发送邮件通知相关人员（可选择日程/系统/邮件通知）',
                'refer_type' => \Constants::TYPE_ORDER,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_ORDER],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'field' => 'cash_collection_status',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => 3,
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'cash_collection_status',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => '',
                        'filter_no' => 2,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_EMAIL_NOTIFY,
                        'config' => [
                            'addition_user' => [],
                            'addition_email' => [],
                            'user_field' => [
                                '2.users',
                                '2.handler',
                            ],
                            'subject' => '订单回款完成通知 -【{{2.order_no}}】{{2.name}}',
                            'content' => '<p>订单：【{{2.order_no}}】{{2.name}}回款完成<br />客户：{{2.company_name}}<br />订单金额：{{2.amount}} {{2.currency}}<br />已回款金额：{{2.cash_collection_collect_amount}} {{2.currency}}</p>',
                            'channel' => [
                                'email' => 1,
                                'message' => 1,
                                'app' => 1,
                                'desktop' => 1,
                            ],
                            'contentText' => '<p>订单：【#销售订单_订单号#】#销售订单_订单名称#回款完成<br />客户：#销售订单_客户名称#<br />订单金额：#销售订单_订单金额# #销售订单_币种#<br />已回款金额：#销售订单_已回款金额# #销售订单_币种#</p>',
                            'subjectText' => '订单回款完成通知 -【#销售订单_订单号#】#销售订单_订单名称#',
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2)',
                'icon' => 1,
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_ORDER,
                ],
            ],
            1047 => [
                'id' => 1047,
                'group_id' => \Constants::TYPE_ORDER,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '临近结单日期的订单待回款提醒',
                'description' => '临近结单日期的订单，尾款未收齐，自动发送邮件通知给相关人员进行跟进',
                'refer_type' => \Constants::TYPE_ORDER,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_ORDER],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_ONCE,
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_ONCE,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_DYNAMIC,
                        'field' => 'account_date',
                        'field_name' => '订单日期',
                        'operator' => WorkflowConstant::DATE_TYPE_OPERATOR_BEFORE,
                        'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => 3,
                    ],
                ],
                'filters' => [
                    [
                        'field' => 'cash_collection_status',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => 3,
                        'filter_no' => 1,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_EMAIL_NOTIFY,
                        'config' => [
                            'addition_user' => [],
                            'addition_email' => [],
                            'user_field' => [
                                '2.users',
                                '2.handler',
                            ],
                            'subject' => '即将结单订单待回款提醒-【{{2.order_no}}】{{2.name}}',
                            'content' => '<p>订单：【{{2.order_no}}】{{2.name}}临近结单日期，请及时跟进回款情况。<br />客户：{{2.company_name}}<br />订单金额：{{2.amount}} {{2.currency}}<br />已回款金额：{{2.cash_collection_collect_amount}} {{2.currency}}<br />结单日期：{{2.account_date}}</p>',
                            'channel' => [
                                'email' => 1,
                                'message' => 1,
                                'app' => 1,
                                'desktop' => 1,
                            ],
                            'contentText' => '<p>订单：【#销售订单_订单号#】#销售订单_订单名称#临近结单日期，请及时跟进回款情况。<br />客户：#销售订单_客户名称#<br />订单金额：#销售订单_订单金额# #销售订单_币种#<br />已回款金额：#销售订单_已回款金额# #销售订单_币种#<br />结单日期：#销售订单_订单日期#</p>',
                            'subjectText' => '即将结单订单待回款提醒-【#销售订单_订单号#】#销售订单_订单名称#',
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1',
                'icon' => 1,
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_ORDER,
                ],
            ],
            1001 => [
                'id' => 1001,
                'group_id' => \Constants::TYPE_CASH_COLLECTION,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '回款战报',
                'description' => '新建回款单并生效时，自动推送钉钉群通知',
                'refer_type' => \Constants::TYPE_CASH_COLLECTION,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_CASH_COLLECTION],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'filter_no' => 1,
                        'field' => 'collect_status',
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 2,
                        'field' => 'collect_status',
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'value' => 1,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_DING_TALK,
                        'config' => [
                            'type' => DingTalkProcessor::DINGTALK_MESSAGE_TYPE_POSTER,
                            'title' => '回款之战_全力以赴',
                            "pic_url" => '',
                            'user_field' => "10.user_id",
                            'amount_field' => 'amount',
                            'content' => "<p>念念不忘，必有回响！</p>\n<p>恭喜{{10.department_id}}的{{10.user_id}}收回一笔{{10.amount}}的回款单</p>\n<p>回款日期：{{10.collection_date}}<p/>",
                            "webhooks" => [],
                        ]
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2)',
                'icon' => 1,
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_CASH_COLLECTION,
                ],
            ],
            1002 => [
                'id' => 1002,
                'group_id' => \Constants::TYPE_CASH_COLLECTION,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '大额回款战报',
                'description' => '回款金额≥X并生效时，自动推送钉钉群通知',
                'refer_type' => \Constants::TYPE_CASH_COLLECTION,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_CASH_COLLECTION],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'filter_no' => 1,
                        'field' => 'collect_status',
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 2,
                        'field' => 'collect_status',
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'value' => 1,
                    ],
                    [
                        'filter_no' => 3,
                        'field' => 'amount',
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION,
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL,
                        'value' => '',
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_DING_TALK,
                        'config' => [
                            'type' => DingTalkProcessor::DINGTALK_MESSAGE_TYPE_POSTER,
                            'title' => '回款业绩冲刺',
                            "pic_url" => '',
                            'user_field' => "10.user_id",
                            'amount_field' => 'amount',
                            'content' => "<p>念念不忘，必有回响！</p>\n<p>恭喜{{10.department_id}}的{{10.user_id}}收回一笔{{10.amount}}的大额回款单</p>\n<p>回款日期：{{10.collection_date}} <p/>",
                            "webhooks" => [],
                        ]
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2 AND 3)',
                'icon' => 1,
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_CASH_COLLECTION,
                ],
            ],
            1003 => [
                'id' => 1003,
                'group_id' => \Constants::TYPE_CASH_COLLECTION,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '回款战报',
                'description' => '新建回款单并生效时，自动推送企微群通知',
                'refer_type' => \Constants::TYPE_CASH_COLLECTION,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_CASH_COLLECTION],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'filter_no' => 1,
                        'field' => 'collect_status',
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 2,
                        'field' => 'collect_status',
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'value' => 1,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_QYWECHAT,
                        'config' => [
                            'type' => DingTalkProcessor::DINGTALK_MESSAGE_TYPE_POSTER,
                            'title' => '回款之战_全力以赴',
                            "pic_url" => '',
                            'user_field' => "10.user_id",
                            'amount_field' => 'amount',
                            'content' => "<p>念念不忘，必有回响！</p>\n<p>恭喜{{10.department_id}}的{{10.user_id}}收回一笔{{10.amount}}的回款单</p>\n<p>回款日期：{{10.collection_date}}<p/>",
                            "webhooks" => [],
                        ]
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2)',
                'icon' => 1,
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_CASH_COLLECTION,
                ],
            ],
            1004 => [
                'id' => 1004,
                'group_id' => \Constants::TYPE_CASH_COLLECTION,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '大额回款战报',
                'description' => '回款金额≧X并生效时，自动推送企微群通知',
                'refer_type' => \Constants::TYPE_CASH_COLLECTION,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_CASH_COLLECTION],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'filter_no' => 1,
                        'field' => 'collect_status',
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 2,
                        'field' => 'collect_status',
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'value' => 1,
                    ],
                    [
                        'filter_no' => 3,
                        'field' => 'amount',
                        'refer_type' => \Constants::TYPE_CASH_COLLECTION,
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL,
                        'value' => '',
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_QYWECHAT,
                        'config' => [
                            'type' => DingTalkProcessor::DINGTALK_MESSAGE_TYPE_POSTER,
                            'title' => '回款业绩冲刺',
                            "pic_url" => '',
                            'user_field' => "10.user_id",
                            'amount_field' => 'amount',
                            'content' => "<p>念念不忘，必有回响！</p>\n<p>恭喜{{10.department_id}}的{{10.user_id}}收回一笔{{10.amount}}的大额回款单</p>\n<p>回款日期：{{10.collection_date}} <p/>",
                            "webhooks" => [],
                        ]
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2 AND 3)',
                'icon' => 1,
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_CASH_COLLECTION,
                ],
            ],
            1006 => [
                'id' => 1006,
                'group_id' => \Constants::TYPE_COMPANY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_AUTOMATIC_ALLOCATION,
                'name' => '高质量公海客户自动分配',
                'description' => '有联系人的公海客户自动分配给转化能力强的人员',
                'refer_type' =>\Constants::TYPE_COMPANY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_COMPANY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_REPEAT,
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_DAY,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => date("Y-m-d", strtotime("+1 day"))
                    ],
                    'end_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => '2999-12-31'
                    ]
                ],
                'filters' => [
                    [
                        'filter_no' => 1,
                        'field' => 'user_id',
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 2,
                        'unit' => '',
                        'field' => 'customer_count',
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL,
                        'value' => 1,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_ASSIGN,
                        'config' => [
                            [
                                'field' => 'user_id',
                                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                                'method' => WorkflowConstant::HANDLER_TYPE_ASSIGN_METHOD_ITERATE_REPLACE,
                                'value' => [
                                    'user_id' => []
                                ]
                            ]
                        ]
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2)',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [],
            ],
            1012 => [
                'id' => 1012,
                'group_id' => \Constants::TYPE_COMPANY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_SYSTEM_NOTIFICATION,
                'name' => '新建档客户跟进提醒',
                'description' => '建档客户2天内需要跟进该客户，如未跟进系统提醒',
                'refer_type' =>\Constants::TYPE_COMPANY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_COMPANY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_ONCE,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_DYNAMIC,
                        'field' => 'archive_time',
                        'field_name' => '创建时间',
                        'operator' => WorkflowConstant::DATE_TYPE_OPERATOR_AFTER,
                        'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'value' => 2,
                    ],
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_ONCE,
                ],
                'filters' => [],
                'handlers' => [
                    [
                        'type' => 'notify',
                        'config' => [
                            'addition_user' => [],
                            'user_field' => [
                                '4.user_id',
                            ],
                            'subject' => '新建档客户跟进提醒',
                            'content' => '<p>客户：{{4.name}}建档2天内需要跟进该客户；</p>',
                            'contentText' => '<p>客户：#客户_公司名称#建档2天内需要跟进该客户；</p>',
                            'subjectText' => '新建档客户跟进提醒',
                            'channel' => [
                                'email' => 0,
                                'message' => 1,
                                'app' => 1,
                                'browser' => 1,
                                'desktop' => 1,
                            ],
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1',
                'icon' => 1,
                'functional' => [],
            ],
            916 => [
                'id' => 916,
                'group_id' => \Constants::TYPE_OPPORTUNITY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_WECOM,
                'name' => '某个渠道商机赢单金额突破X喜报',
                'description' => '通过某个渠道来源的客户总的商机赢单金额大于X时，自动推送企微群通知',
                'refer_type' => \Constants::TYPE_OPPORTUNITY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_OPPORTUNITY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'filter_no' => 1,
                        'field' => 'stage_type',
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 2,
                        'field' => 'stage_type',
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'value' => 2,
                    ],
                    [
                        'filter_no' => 3,
                        'field' => 'amount',
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 4,
                        'field' => 'origin_list',
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'value' => '',
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_QYWECHAT,
                        'config' => [
                            'type' => DingTalkProcessor::DINGTALK_MESSAGE_TYPE_GRAPHIC,
                            'title' => '赢单之战_开辟新渠道',
                            "pic_url" => $dingTalkPreparePicture,
                            'user_field' => "9.main_user",
                            'amount_field' => 'amount',
                            'content' => "<p>恭喜{{9.department}}的{{9.main_user}}拿下一单{{4.country}}的客户。</p>\n<p>商机金额：{{9.amount}}</p>\n<p>客户名称：{{4.name}}</p>\n<p>客户来源：{{4.origin_list}}</p>\n<p>赢单日期：{{9.account_date}}<p/>",
                            "webhooks" => [],
                        ]
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2 AND 3 AND 4)',
                'icon' => 1,
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
                ],
            ],
            1016 => [
                'id' => 1016,
                'group_id' => \Constants::TYPE_ORDER,
                'second_group_id' => WorkflowConstant::RECLASSIFY_WECOM,
                'name' => '成交订单时战报推送',
                'description' => '每次成交订单后，自动推送企微群通知',
                'refer_type' =>\Constants::TYPE_ORDER,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_ORDER],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'field' => 'status',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => '',
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'status',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_NOT_IN,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => $orderEndStatusIds ?? [],
                        'filter_no' => 2,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_QYWECHAT,
                        'config' => [
                            'type' => DingTalkProcessor::DINGTALK_MESSAGE_TYPE_POSTER,
                            'title' => '今日喜报',
                            'content' => '<p>恭喜{{2.departments}}的{{2.users}}签下一单{{4.country}}的客户{{2.company_name}}<br />订单日期：{{2.account_date}}</p>',
                            'pic_url' => '',
                            'user_field' => '2.users',
                            'amount_format_type' => 1,
                            'amount_field' => 'amount',
                            'webhooks' => [],
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2)',
                'icon' => 0,
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_ORDER,
                ],
            ],
            1017 => [
                'id' => 1017,
                'group_id' => \Constants::TYPE_ORDER,
                'second_group_id' => WorkflowConstant::RECLASSIFY_WECOM,
                'name' => '大额订单成交战报',
                'description' => '成交订单金额≧X时，自动推送企微群通知',
                'refer_type' => \Constants::TYPE_ORDER,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_ORDER],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'field' => 'status',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => '',
                        'filter_no' => 1,
                    ],
                    [
                        'field' => 'status',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_NOT_IN,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => $orderEndStatusIds ?? [],
                        'filter_no' => 2,
                    ],
                    [
                        'unit' => '',
                        'field' => 'amount',
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER,
                        'refer_type' => \Constants::TYPE_ORDER,
                        'value' => '',
                        'filter_no' => 3,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_QYWECHAT,
                        'config' => [
                            'type' => DingTalkProcessor::DINGTALK_MESSAGE_TYPE_POSTER,
                            'title' => '大单来袭_舍我其谁',
                            'content' => '<p>重磅消息，有一笔大额订单成交！<br />恭喜{{2.departments}}的{{2.users}}签下{{4.country}}的大单客户{{4.name}}，努力定会有回报。</p><p>订单日期：{{2.account_date}}</p>',
                            'pic_url' => '',
                            'webhooks' => [],
                            'user_field' => '2.users',
                            'amount_field' => 'amount',
                            'amount_format_type' => 1,
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2 AND 3)',
                'icon' => 0,
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_ORDER,
                ],
            ],
            214 => [
                'id' => 214,
                'group_id' => \Constants::TYPE_ORDER,
                'name' => '某个渠道成交订单金额突破X喜报',
                'description' => '通过某个渠道来源的客户总的成交订单金额大于X时，自动推送企微群通知',
                'refer_type' =>\Constants::TYPE_ORDER,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_ORDER],
                'second_group_id' => WorkflowConstant::RECLASSIFY_WECOM,
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'filter_no' => 1,
                        'field' => 'status',
                        'refer_type' => \Constants::TYPE_ORDER,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 2,
                        'field' => 'status',
                        'refer_type' => \Constants::TYPE_ORDER,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                        'value' => $orderEndStatusIds ?? [],  //订单的完成状态
                    ],
                    [
                        'filter_no' => 3,
                        'field' => 'amount',
                        'refer_type' => \Constants::TYPE_ORDER,
                        'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_GREATER,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 4,
                        'field' => 'origin_list',
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'value' => '',
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_QYWECHAT,
                        'config' => [
                            'type' => DingTalkProcessor::DINGTALK_MESSAGE_TYPE_GRAPHIC,
                            'title' => '成交之战_开辟新渠道',
                            "pic_url" => $dingTalkPreparePicture,
                            'user_field' => "2.users",
                            'amount_field' => 'amount',
                            'content' => "<p>恭喜{{2.departments}}的{{2.users}}签下一单{{4.country}}的客户。</p>\n<p>订单金额：{{2.amount}}</p>\n<p>客户名称：{{4.name}}</p>\n<p>客户来源：{{4.origin_list}}</p>\n<p>订单日期：{{2.account_date}} </p>",
                            "webhooks" => [],
                        ]
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2 AND 3 AND 4)',
                'icon' => 1,
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_ORDER,
                ],
            ],
            1026 => [
                'id' => 1026,
                'group_id' => \Constants::TYPE_LEAD,
                'second_group_id' => WorkflowConstant::RECLASSIFY_AUTOMATIC_ALLOCATION,
                'name' => '根据国家自动分配线索',
                'description' => '如公司重点跟进某个地区的线索，可自动分配给到指定转化高的业务员，提高高质量询盘转化',
                'refer_type' => \Constants::TYPE_LEAD,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_LEAD],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_REPEAT,
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_DAY,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => date("Y-m-d", strtotime("+1 day")),
                    ],
                    'end_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => '2999-12-31',
                    ],
                ],
                'filters' => [
                    [
                        'field' => 'country',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                        'refer_type' => \Constants::TYPE_LEAD,
                        'value' => [],
                        'filter_no' => 1,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_ASSIGN,
                        'config' => [
                            [
                                'field' => 'user_id',
                                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                                'value' => [
                                    'last_owner_ids' => '',
                                    'user_departments' => [],
                                    'user_id' => [],
                                    'exclude_user_id' => [],
                                ],
                                'method' => WorkflowConstant::HANDLER_TYPE_ASSIGN_METHOD_ITERATE_REPLACE,
                            ]
                        ]
                    ]
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1',
                'icon' => 1,
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_LEAD,
                ]
            ],
            1051 => [
                'id' => 1051,
                'group_id' => \Constants::TYPE_COMPANY,
                'second_group_id' => WorkflowConstant::RECLASSIFY_AUTOMATIC_ALLOCATION,
                'name' => 'AI SDR挖掘高质量客户自动分配',
                'description' => '针对AI SDR自动挖掘的高价值潜客可分配给对应人员跟进',
                'refer_type' =>\Constants::TYPE_COMPANY,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_COMPANY],
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_DAILY,
                'time_setting' => [
                    'regular_type' => WorkflowConstant::REGULAR_TYPE_REPEAT,
                    'cycle_type' => WorkflowConstant::CYCLE_TYPE_DAY,
                    'start_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => date("Y-m-d", strtotime("+1 day"))
                    ],
                    'end_date' => [
                        'date_type' => WorkflowConstant::DATE_TYPE_STATIC,
                        'value' => '2999-12-31'
                    ]
                ],
                'filters' => [
                    [
                        'filter_no' => 1,
                        'field' => 'origin_list',
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                        'value' => [Origin::SYS_ORIGIN_AI_SDR],
                    ],
                    [
                        'filter_no' => 2,
                        'unit' => '',
                        'field' => 'client_tag_list',
                        'refer_type' => \Constants::TYPE_COMPANY,
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_IN,
                        'value' => [Tag::TAG_HIGH_VALUE],
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_ASSIGN,
                        'config' => [
                            [
                                'field' => 'user_id',
                                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                                'method' => WorkflowConstant::HANDLER_TYPE_ASSIGN_METHOD_ITERATE_REPLACE,
                                'value' => [
                                    'user_id' => []
                                ]
                            ]
                        ]
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2)',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_AI_SDR,
                ],
            ],
            215 => [
                'id' => 215,
                'group_id' => \Constants::TYPE_ORDER,
                'name' => '根据订单状态自动更新商机阶段',
                'description' => '若订单状态变更，则关联此订单的商机销售阶段自动变更',
                'refer_type' =>\Constants::TYPE_ORDER,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_ORDER],
                'second_group_id' => \common\library\workflow\WorkflowConstant::RECLASSIFY_MODIFY_FIELD,
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'filter_no' => 1,
                        'field' => 'status',
                        'refer_type' => \Constants::TYPE_ORDER,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                        'value' => '',
                    ],
                    [
                        'filter_no' => 2,
                        'field' => 'status',
                        'refer_type' => \Constants::TYPE_ORDER,
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'value' => [],  //订单的完成状态
                    ],
                    [
                        'filter_no' => 3,
                        'field' => 'disable_flag',
                        'refer_type' => \Constants::TYPE_OPPORTUNITY,
                        'field_type' => CustomFieldService::FIELD_TYPE_BOOLEAN,
                        'operator' => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'value' => 0,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_UPDATE_FIELD,
                        'config' => [
                            [
                                'field' => 'stage',
                                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                                'value' => null,
                                'refer_type' => \Constants::TYPE_OPPORTUNITY,
                                'unit' => '',
                                'label' => '',
                            ],
                        ],
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '(1 AND 2 AND 3)',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
                ],
            ],
            216 => [
                'id' => 216,
                'group_id' => \Constants::TYPE_CASH_COLLECTION_INVOICE,
                'name' => '回款认领通知',
                'description' => '财务登记水单回款后，通过群聊通知业务员进行认领核销',
                'refer_type' =>\Constants::TYPE_CASH_COLLECTION_INVOICE,
                'refer_type_name' => WorkflowConstant::REFER_TYPE_NAME_MAP[\Constants::TYPE_CASH_COLLECTION_INVOICE],
                'second_group_id' => \common\library\workflow\WorkflowConstant::RECLASSIFY_DING_TALK ,
                'trigger_type' => WorkflowConstant::TRIGGER_TYPE_CREATE_UPDATE,
                'time_setting' => [],
                'filters' => [
                    [
                        'filter_no' => 1,
                        'field' => 'status',
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'operator'   => WorkflowConstant::FILTER_OPERATOR_EQUAL,
                        'value' => OmsConstant::CASH_COLLECTION_INVOICE_STATUS_NOT_HANDLE        ,
                    ],
                ],
                'handlers' => [
                    [
                        'type' => WorkflowConstant::HANDLER_TYPE_DING_TALK ,
                        'config' => [
                            'type' => DingTalkProcessor::DINGTALK_MESSAGE_TYPE_INTERACTIVE_CARD,
                            'title' => '回款认领通知',
                            'content' => "<p>回款金额：{{46.amount_usd}}</p>\n<p>交易号：{{46.trade_no}}</p>\n<p>来款方：{{46.account_name}}</p>\n<p>来款账户：{{46.bank_account}}</p>\n<p>来款银行：{{46.bank_name}}</p>\n<p>&nbsp;</p>\n<p>请相关业务人员及时认领核销</p>",
                            'jump_page' => 46,
                            'jump_page_label' => "{$cashCollectionInvoiceJumpageLabel}", //"{\Yii::t('workflow', 'refer_type.' . \Constants::TYPE_CASH_COLLECTION_INVOICE)}",
                            'link_title' => '查看详情',

                        ]
                    ],
                ],
                'criteria_type' => WorkflowConstant::CRITERIA_TYPE_AND,
                'criteria' => '1',
                'icon' => 1,
                'tag' => 'new',
                'functional' => [
                    PrivilegeConstants::FUNCTIONAL_CASH_COLLECTION_INVOICE ,
                ],
            ],
        ];
        foreach ($templateListData as $id => &$template) {
            $template['enable_flag'] = $template['enable_flag'] ?? 1;
            if (!empty($template['name'])) {
                $template['name'] = \Yii::t('workflowTemplate', $template['name']);
            }
            if (!empty($template['description'])) {
                $template['description'] = \Yii::t('workflowTemplate', $template['description']);
            } 
        }

        return array_values($templateListData);
    }
}
