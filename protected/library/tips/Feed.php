<?php
/**
 * Copyright (c) 2012 - 2019 <PERSON><PERSON>.All Rights Reserved
 * Author: nuxse
 * Date: 2019/8/12
 */

namespace common\library\tips;


use common\components\BaseObject;

class Feed extends BaseObject
{
    private $user_id;
    private $feed_id;

    public static function getModelTableName()
    {
        return \TipsFeed::model()->tableName();
    }

    /**
     * @return mixed
     */
    public function getModelClass()
    {
        return \TipsFeed::class;
    }

    public function __construct($feed_id,$user_id=null)
    {
        $this->user_id = is_null($user_id) ? \User::getLoginUser()->getUserId():$user_id;
        $this->feed_id = $feed_id;

        $this->loadById($feed_id,$this->user_id);
    }

    private function loadById($feed_id, $user_id)
    {
        $model = static::getModelClass()::model()->find('feed_id=:feed_id and user_id=:user_id',
                [':feed_id' => $feed_id, ':user_id' => $user_id]);

        if ($model) {
            $this->setModel($model);
        }
    }
}