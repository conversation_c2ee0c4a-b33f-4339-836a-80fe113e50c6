<?php
/**
 * Created by PhpStorm.
 * User: tony
 * Date: 2019-08-07
 * Time: 15:45
 */


namespace common\library\tips;


class TipsConstants
{

    const SCENE_MAIL = 'mail';
    const SCENE_COMPANY = 'company';


    //新消息阈值 指的是feed表每次吐出用来排序的量
    const NEW_FEED_THRESHOLD = 50;

    //pull 更新消息源扩散阈值
    const DIFFUSE_MSG_ORIGIN_THRESHOLD = 200;

    const TIPS_LIMIT_FEED_TYPE = [
        self::FEED_TYPE_OPPORTUNITY_FORGET,
        self::FEED_TYPE_MAIL_NOT_RESPOND,
        self::FEED_TYPE_MAIL_KEY_VIEWED,
        self::FEED_TYPE_CUSTOMERS_UPDATED,
        self::FEED_TYPE_CUSTOMERS_FIELD_NOT_FILLED,
        self::FEED_TYPE_MAIL_SAVE_CUSTOMERS,
        self::FEED_TYPE_COMPANY_RECOMMEND,
        self::FEED_TYPE_GOOGLE_KEYWORD,
        self::FEED_TYPE_DISCOVERY_KEYWORD,
        self::FEED_TYPE_SIC_CODE,
    ];

    //邮件场景关注的feed 类型
    static $scene_mail_subscribe = [

    ];

    //客户场景关注的feed 类型
    static $scene_company_subscribe = [

    ];

    const BIZ_TYPE_EMAIL = 1;                           // 业务主体：Email
    const BIZ_TYPE_COMPANY = 2;                         // 业务主体：DX公司
    const BIZ_TYPE_CUSTOMERS = 3;                       // 业务主体：建档客户

    const REFER_TYPE_EMAIL = 1;                         // 关联主体：Email
    const REFER_TYPE_COMPANY = 2;                       // 关联主体：DX公司
    const REFER_TYPE_OPPORTUNITY = 3;                   // 关联主体：商机
    const REFER_TYPE_CUSTOMERS = 4;                     // 关联主体：建档客户

    const FEED_TYPE_OPPORTUNITY_FORGET = 1;             // 商机遗忘提醒
    const FEED_TYPE_MAIL_REMIND_REPLY = 2;              // 回复邮件提醒
    const FEED_TYPE_MAIL_NOT_RESPOND = 3;               // 有未被回应的关键邮件
    const FEED_TYPE_MAIL_KEY_VIEWED = 4;                // 关键邮件被查看提醒
    const FEED_TYPE_CUSTOMERS_UPDATED = 5;              // 资料检查提示
    const FEED_TYPE_CUSTOMERS_FIELD_NOT_FILLED = 6;     // 关键信息填写提示
    const FEED_TYPE_MAIL_SAVE_CUSTOMERS = 7;            // 建档建议
    const FEED_TYPE_OPPORTUNITY_UPDATED = 8;            // 机会洞见
    const FEED_TYPE_COMPANY_RECOMMEND = 12;             // 相似公司
    const FEED_TYPE_COMPANY_CONTACTS_NEW = 13;          // 新增联系人提示
    const FEED_TYPE_COMPANY_CONTACTS_INVALID = 14;      // 联系人邮箱失效提示
    const FEED_TYPE_COMPANY_BUSINESS = 15;              // 业务提示
    const FEED_TYPE_CUSTOMS_NEW = 16;                   // 海关客户动态
    const FEED_TYPE_GOOGLE_KEYWORD = 17;                // Google 推荐关键词
    const FEED_TYPE_DISCOVERY_KEYWORD = 18;             // DX 推荐关键词
    const FEED_TYPE_SIC_CODE = 19;                      // DX 推荐行业
    const FEED_TYPE_MAIL_NORMAL_NOT_RESPOND = 20;       // 普通邮件未被查看

    const ORIGIN_TYPE_QUEUE = 1;                        // DX 消费队列
    const ORIGIN_TYPE_TASK = 2;                         // TASK 定时任务脚本
    const ORIGIN_TYPE_EVENT = 3;                        // 业务实时事件
    const ORIGIN_TYPE_ONLINE = 4;                       // 在线任务脚本
    const ORIGIN_TYPE_TODO = 5;                         // 工作待办-跟进任务

    const SPECIFIC_FORBIDDEN_COUNTRYS = ['CU','IR','KP','SY','SD'];  // dx tips屏蔽风控国家

    const FORBIDDEN_DX_TYPE = [   // dx tips屏蔽风控国家 DX公司的相关feed
        self::FEED_TYPE_OPPORTUNITY_UPDATED,
        self::FEED_TYPE_COMPANY_RECOMMEND,
        self::FEED_TYPE_COMPANY_CONTACTS_NEW,
        self::FEED_TYPE_COMPANY_CONTACTS_INVALID,
        self::FEED_TYPE_COMPANY_BUSINESS,
        self::FEED_TYPE_CUSTOMS_NEW,
        self::FEED_TYPE_GOOGLE_KEYWORD,
        self::FEED_TYPE_DISCOVERY_KEYWORD,
        self::FEED_TYPE_SIC_CODE,
    ];

}