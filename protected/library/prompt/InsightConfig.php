<?php

namespace common\library\prompt;

use common\library\ai_agent\api\AIClient;
use common\library\gpt\GptService;
use common\library\invoice\status\InvoiceStatusService;
use common\library\performance_v2\rule\PerformanceV2RuleList;
use DateTime;
use PgActiveRecord;

class InsightConfig
{
    const INSIGHT_PROMPT_TOKEN_MAX_MAP = [
        AIClient::AZURE_OPENAI_GPT_FOUR_O_MINI => 10000
    ];

    private $config;
    private $performanceConfig;

    const INSIGHT_PROMPT = "假如你是服务于外贸业务团队人数为#激活账号数#的#受众角色#的#充当角色#，请你根据以下报表数据，回答问题：#prompt问题#每个回答<200字；请用小白能听懂的语言回答，体现数据分析的专业。请用Json格式返回结果，JSON格式例子如下：{\"分析结论\":\"xxx\",\"业务存在问题\":\"xxx\",\"行动建议\":\"xxx\"}，注意，分析结论、存在问题和行动建议都用一句话回答，以下是具体的报表数据：";


    const INSIGHT_ACT_AS_ROLE_ANALYST = '数据分析师';


    // 分析类别：1-客户分析 2-订单分析 3-产品分析
    const INSIGHT_TYPE_COMPANY = 1;
    const INSIGHT_TYPE_ORDER = 2;
    const INSIGHT_TYPE_PRODUCT = 3;
    const INSIGHT_TYPE_TARGET = 4;
    const INSIGHT_TYPE_OPPORTUNITY = 5;


    // 统计周期
    const INSIGHT_SECTION_HALF_A_YEAR = 'half_a_year';
    const INSIGHT_SECTION_BEFORE_TWO_MONTHS = 'before_two_months';


    // 阀值
    const INSIGHT_RICHNESS_THRESHOLD = 0.5 ;
    const INSIGHT_OTHERNESS_THRESHOLD = 0.5;

    // 卡片KEY
    const NEW_CUSTOMER_SITUATION = 'new_customer_situation';
    const PRIVATE_SEA_CUSTOMERS_DISTRIBUTION = 'private_sea_customers_distribution';
    const TOP5_CUSTOMERS_WITH_SALES_ORDER = 'top5_customers_with_sales_order';
    const ORDER_CUSTOMER_ANALYSIS = 'order_customer_analysis';
    const CHANGE_TREND_ORDER_AMOUNT = 'change_trend_order_amount';
    const HOT_SALE_PRODUCTS = 'hot_sale_products';
    const RESULT_TARGET_WARNING = 'result_target_warning';
    const PROCESS_TARGET_WARNING = 'process_target_warning';
    const ANALYSIS_REASON_OF_FAIL_OPPORTUNITY = 'analysis_reason_of_opportunity';
    const TEAM_WORK_SITUATION = 'team_work_situation';
    const TOP5_CUSTOMERS_WITH_OPPORTUNITY = 'top5_customers_with_opportunity';
    const ANALYSIS_OF_BUSINESS_OPPORTUNITIES_AND_WINNING_CUSTOMERS = 'analysis_of_business_opportunities_and_winning_customers';


    const NEW_CUSTOMER_TRANSACTION_CONVERSION_STATUS_WITH_SALES_ORDER = 'new_customer_transaction_conversion_status_with_sales_order';
    const NEW_CUSTOMER_TRANSACTION_CONVERSION_STATUS_WITH_OPPORTUNITY = 'new_customer_transaction_conversion_status_with_opportunity';
    const CUSTOMER_REPURCHASE_SITUATION_WITH_SALES_ORDER = 'customer_repurchase_situation_with_sales_order';
    const CUSTOMER_REPURCHASE_SITUATION_WITH_OPPORTUNITY = 'customer_repurchase_situation_with_opportunity';

    const CUSTOMER_FOLLOW_REMIND = 'customer_follow_remind';




    // 问题KEY
    const TOP5_CUSTOMERS_ORDER_AMOUNT = 'top5_customers_order_amount';
    const TOP5_CUSTOMERS_ORDER_QUANTITY = 'top5_customers_order_quantity';
    const TOP5_CUSTOMERS_OPPORTUNITY_AMOUNT = 'top5_customers_opportunity_amount';
    const TOP5_CUSTOMERS_OPPORTUNITY_QUANTITY = 'top5_customers_opportunity_quantity';
    const PRIVATE_SEA_CUSTOMERS_SOURCE_DISTRIBUTION = 'private_sea_customers_source_distribution';
    const PRIVATE_SEA_CUSTOMERS_COUNTRY_DISTRIBUTION = 'private_sea_customers_country_distribution';
    const DISTRIBUTION_OF_SOURCE_CHANNELS_FOR_WINNING_CUSTOMERS_IN_BUSINESS_OPPORTUNITIES = 'distribution_of_source_channels_for_winning_customers_in_business_opportunities';
    const DISTRIBUTION_OF_MAIN_COUNTRIES_AND_REGIONS_WHERE_BUSINESS_OPPORTUNITIES_AND_WINNING_CUSTOMERS_ARE_LOCATED = 'distribution_of_main_countries_and_regions_where_business_opportunities_and_winning_customers_are_located';

    const TREND_OF_NEW_CUSTOMERS = 'trend_of_new_customers';
    const ORDER_CUSTOMERS_SOURCE_DISTRIBUTION = 'order_customers_source_distribution';
    const ORDER_CUSTOMERS_COUNTRY_DISTRIBUTION = 'order_customers_country_distribution';
    const RECENT_CHANGE_ORDER_VALUE = 'recent_change_order_value';
    const RECENT_CHANGE_ORDER_VALUE_TWO = 'recent_change_order_value_two';
    const RECENT_HOT_SALE_PRODUCTS = 'recent_hot_sale_products';
    const RECENT_HOT_SALE_PRODUCTS_MODEL = 'recent_hot_sale_products_model';
    const RECENT_HOT_SALE_PRODUCTS_GROUP = 'recent_hot_sale_products_group';
    const RECENT_HOT_SALE_PRODUCTS_CATEGORIES = 'recent_hot_sale_products_categories';
    const PROCESS_GOAL_LOWER_THAN_TWENTY_PERCENT_BEFORE_TWO_MONTHS = 'process_goal_lower_than_twenty_percent_before_two_months';
    const RESULT_GOAL_LOWER_THAN_TWENTY_PERCENT_BEFORE_TWO_MONTHS = 'result_goal_lower_than_twenty_percent_before_two_months';
    const ANALYSIS_MAIN_REASON_OF_FAIL_OPPORTUNITY = 'analysis_main_reason_of_fail_opportunity';
    const ANALYSIS_OF_FAIL_REASON_FOR_EMPLOYEE = 'analysis_of_fail_reason_for_employee';
    const ALL_TEAM_WORK_SITUATION = 'all_team_work_situation';
    const COMPANY_FOLLOW_UP_SITUATION = 'company_follow_up_situation';


    const TREND_OF_CUSTOMER_TRANSACTION_CONVERSION_RATE_CHANGES_WITH_SALE_ORDER = 'trend_of_customer_transaction_conversion_rate_changes_with_sales_order';
    const DISTRIBUTION_OF_UNIT_PRICE_AND_CONVERSION_RATE_BY_COUNTRY_AND_REGION_WITH_SALE_ORDER = 'distribution_of_unit_price_and_conversion_rate_by_country_and_region_with_sales_order';
    const DISTRIBUTION_OF_UNIT_PRICE_AND_CONVERSION_RATE_OF_SOURCE_CHANNELS_WITH_SALE_ORDER = 'distribution_of_unit_price_and_conversion_rate_of_source_channels_with_sales_order';


    const TREND_OF_CUSTOMER_TRANSACTION_CONVERSION_RATE_CHANGES_WITH_OPPORTUNITY = 'trend_of_customer_transaction_conversion_rate_changes_with_opportunity';
    const DISTRIBUTION_OF_UNIT_PRICE_AND_CONVERSION_RATE_BY_COUNTRY_AND_REGION_WITH_OPPORTUNITY = 'distribution_of_unit_price_and_conversion_rate_by_country_and_region_with_with_opportunity';
    const DISTRIBUTION_OF_UNIT_PRICE_AND_CONVERSION_RATE_OF_SOURCE_CHANNELS_WITH_OPPORTUNITY = 'distribution_of_unit_price_and_conversion_rate_of_source_channels_with_with_opportunity';

    const DISTRIBUTION_OF_REPURCHASE_RATE_AND_REPURCHASE_CYCLE_OF_SOURCE_CHANNELS_WITH_SALE_ORDER = 'distribution_of_repurchase_rate_and_repurchase_cycle_of_source_channels_with_sales_order';
    const DISTRIBUTION_OF_REPURCHASE_RATES_AND_REPURCHASE_CYCLES_IN_DIFFERENT_COUNTRIES_AND_REGIONS_WITH_SALE_ORDER = 'distribution_of_repurchase_rates_and_repurchase_cycles_in_different_countries_and_regions_with_sales_order';

    const DISTRIBUTION_OF_REPURCHASE_RATE_AND_REPURCHASE_CYCLE_OF_SOURCE_CHANNELS_WITH_OPPORTUNITY = 'distribution_of_repurchase_rate_and_repurchase_cycle_of_source_channels_with_opportunity';
    const DISTRIBUTION_OF_REPURCHASE_RATES_AND_REPURCHASE_CYCLES_IN_DIFFERENT_COUNTRIES_AND_REGIONS_WITH_OPPORTUNITY = 'distribution_of_repurchase_rates_and_repurchase_cycles_in_different_countries_and_regions_with_opportunity';

    const PREDICT_REORDER_COMPANY_WITH_ORDER = 'predict_reorder_company_with_order';

    const PREDICT_REORDER_COMPANY_WITH_OPPORTUNITY = 'predict_reorder_company_with_opportunity';



    /*
     * 统计差异的两种方法：
     *
     * 1. label-按照排序逻辑，本次前x的分组名称!=现在（方法B）
     * 按照排序逻辑，本次前5的分组名称 != 上次
     * 2. value-对比新旧数据value值（方法A）
     * 将每个周期作为key，数据列的元素作为值,设为a=0
     * if (本次key的值 !=上次key的值) ，则a=a+1，
     * if（有新的key），则a=a+1,
     * if (本次key的值=上次key的值) ，则不计入，
     * 如果a/key的值的数量合计>50%，则说明差异显著，需要推送
     */
    const STAT_DIFF_LABEL = 'diff_label';
    const STAT_DIFF_VALUE = 'diff_value';

    // 问题KEY对应REFER_ID的MAP数组
    const INSIGHT_SUBTITLE_REFER_ID_MAP = [
        self::TOP5_CUSTOMERS_ORDER_AMOUNT => 10001,
        self::PRIVATE_SEA_CUSTOMERS_SOURCE_DISTRIBUTION => 10002,
        self::PRIVATE_SEA_CUSTOMERS_COUNTRY_DISTRIBUTION => 10003,
        self::TREND_OF_NEW_CUSTOMERS => 10004,
        self::ORDER_CUSTOMERS_SOURCE_DISTRIBUTION => 10005,
        self::ORDER_CUSTOMERS_COUNTRY_DISTRIBUTION => 10006,
        self::RECENT_CHANGE_ORDER_VALUE => 10007,
        self::RECENT_HOT_SALE_PRODUCTS => 10008,
        self::RECENT_HOT_SALE_PRODUCTS_MODEL => 10009,
        self::RECENT_HOT_SALE_PRODUCTS_GROUP => 10010,
        self::TOP5_CUSTOMERS_ORDER_QUANTITY => 10011,
        self::RECENT_CHANGE_ORDER_VALUE_TWO => 10012,
        self::RECENT_HOT_SALE_PRODUCTS_CATEGORIES => 10013,
        self::ANALYSIS_MAIN_REASON_OF_FAIL_OPPORTUNITY => 10014,
        self::ANALYSIS_OF_FAIL_REASON_FOR_EMPLOYEE => 10015,
        self::ALL_TEAM_WORK_SITUATION => 10016,
        self::TREND_OF_CUSTOMER_TRANSACTION_CONVERSION_RATE_CHANGES_WITH_SALE_ORDER => 10017,
        self::DISTRIBUTION_OF_UNIT_PRICE_AND_CONVERSION_RATE_BY_COUNTRY_AND_REGION_WITH_SALE_ORDER => 10018,
        self::DISTRIBUTION_OF_UNIT_PRICE_AND_CONVERSION_RATE_OF_SOURCE_CHANNELS_WITH_SALE_ORDER => 10019,
        self::TREND_OF_CUSTOMER_TRANSACTION_CONVERSION_RATE_CHANGES_WITH_OPPORTUNITY => 100110,
        self::DISTRIBUTION_OF_UNIT_PRICE_AND_CONVERSION_RATE_BY_COUNTRY_AND_REGION_WITH_OPPORTUNITY => 100111,
        self::DISTRIBUTION_OF_UNIT_PRICE_AND_CONVERSION_RATE_OF_SOURCE_CHANNELS_WITH_OPPORTUNITY => 100112,
        self::DISTRIBUTION_OF_REPURCHASE_RATE_AND_REPURCHASE_CYCLE_OF_SOURCE_CHANNELS_WITH_SALE_ORDER => 100113,
        self::DISTRIBUTION_OF_REPURCHASE_RATES_AND_REPURCHASE_CYCLES_IN_DIFFERENT_COUNTRIES_AND_REGIONS_WITH_SALE_ORDER => 100114,
        self::DISTRIBUTION_OF_REPURCHASE_RATE_AND_REPURCHASE_CYCLE_OF_SOURCE_CHANNELS_WITH_OPPORTUNITY => 100115,
        self::DISTRIBUTION_OF_REPURCHASE_RATES_AND_REPURCHASE_CYCLES_IN_DIFFERENT_COUNTRIES_AND_REGIONS_WITH_OPPORTUNITY => 100116,
        self::TOP5_CUSTOMERS_OPPORTUNITY_AMOUNT => 100117,
        self::TOP5_CUSTOMERS_OPPORTUNITY_QUANTITY => 100118,
        self::DISTRIBUTION_OF_SOURCE_CHANNELS_FOR_WINNING_CUSTOMERS_IN_BUSINESS_OPPORTUNITIES => 100120,
        self::DISTRIBUTION_OF_MAIN_COUNTRIES_AND_REGIONS_WHERE_BUSINESS_OPPORTUNITIES_AND_WINNING_CUSTOMERS_ARE_LOCATED => 100121,
        self::COMPANY_FOLLOW_UP_SITUATION => 100122,
        self::PREDICT_REORDER_COMPANY_WITH_ORDER => 100123,
        self::PREDICT_REORDER_COMPANY_WITH_OPPORTUNITY => 100124,
        self::RESULT_GOAL_LOWER_THAN_TWENTY_PERCENT_BEFORE_TWO_MONTHS => 1,
        self::PROCESS_GOAL_LOWER_THAN_TWENTY_PERCENT_BEFORE_TWO_MONTHS => 2,

    ];

    // 动态insight subkey
    const INSIGHT_VARIABLE_SUB_KEY_PREFIX_KEY_MAP = [
        self::RESULT_GOAL_LOWER_THAN_TWENTY_PERCENT_BEFORE_TWO_MONTHS ,
        self::PROCESS_GOAL_LOWER_THAN_TWENTY_PERCENT_BEFORE_TWO_MONTHS,
    ];

    const GOAL_TYPE_MAP = [
        \PerformanceV2Goals::TARGET_PROCESS_GOAL => self::PROCESS_TARGET_WARNING,
        \PerformanceV2Goals::TARGET_RESULT_GOAL => self::RESULT_TARGET_WARNING,
    ];

    // Redis需要生成Insight的clients集合key
    const CACHE_INSIGHT_CLIENT_IDS = 'cache_insight_client_ids';

    public function __construct()
    {
        $this->initConfig();
    }


    private function initConfig()
    {
        $this->config = [

            self::NEW_CUSTOMER_SITUATION => [
                'name' => \Yii::t('ai', '新增客户数变化趋势'),
                'title' => \Yii::t('ai', '新增客户情况'),
                'type' => self::INSIGHT_TYPE_COMPANY,
                'section' => self::INSIGHT_SECTION_HALF_A_YEAR,

                'sub' => [
                    self::TREND_OF_NEW_CUSTOMERS => [
                        'sub_key' => self::TREND_OF_NEW_CUSTOMERS,
                        'report_key' => ['khts1'],
                        'subtitle' => \Yii::t('ai', '近期数量变化趋势'),
                        'prompt' => '近3个月新增客户数变化趋势是怎样的？',
                        'stat_diff' => self::STAT_DIFF_VALUE,
                        'old_data_flag' => false,
                    ]
                ],

                'params' => [
                    'khts1' => [
                        ['field' => 'company.select_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '时间区间'],
                        ['field' => 'common.select_cycle', 'type' => 'select', 'value' => 'week']
                    ]
                ],
                'old_params' => [
                    'khts1' => [
                        ['field' => 'company.select_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '时间区间'],
                        ['field' => 'common.select_cycle', 'type' => 'select', 'value' => 'week']
                    ]
                ],

                'statistic_field' => [
                    self::TREND_OF_NEW_CUSTOMERS => [
                        'khts1' => ['company.date', 'mergeCount-company.create_ids', 'monthOnMonthIncreaseNum-company.create_ids', 'monthOnMonthIncrease-company.create_ids']
                    ]
                ],

                'summaries' => [
                    'khts1' => [
                        [
                            "key" => "mergeCount-company.create_ids",
                            "tip" => "",
                            "type" => "mergeCount",
                            "label" => \Yii::t('ai', '新建客户数'),
                            "order" => "",
                            "hidden" => 0,
                            "field_type" => "",
                            "refer_list" => "companyList",
                            "value_type" => ""
                        ],
                        [
                            "key" => "monthOnMonthIncreaseNum-company.create_ids",
                            "tip" => "",
                            "type" => "monthOnMonthIncreaseNum",
                            "label" => \Yii::t('ai', '环比新增客户数'),
                            "order" => "",
                            "hidden" => 0,
                            "field_type" => "",
                            "refer_list" => "",
                            "value_type" => ""
                        ],
                        [
                            "key" => "monthOnMonthIncrease-company.create_ids",
                            "tip" => "",
                            "type" => "monthOnMonthIncrease",
                            "label" => \Yii::t('ai', '环比增加比例'),
                            "order" => "",
                            "hidden" => 0,
                            "field_type" => "",
                            "refer_list" => "",
                            "value_type" => ""
                        ],
                        [
                            "key" => "yearOnYearIncreaseNum-company.create_ids",
                            "tip" => "",
                            "type" => "yearOnYearIncreaseNum",
                            "label" => \Yii::t('ai', '同比新增客户数'),
                            "order" => "",
                            "hidden" => 0,
                            "field_type" => "",
                            "refer_list" => "",
                            "value_type" => ""
                        ],
                        [
                            "key" => "yearOnYearIncrease-company.create_ids",
                            "tip" => "",
                            "type" => "yearOnYearIncrease",
                            "label" => \Yii::t('ai', '同比增长比例'),
                            "order" => "",
                            "hidden" => 0,
                            "field_type" => "",
                            "refer_list" => "",
                            "value_type" => ""
                        ]
                    ]
                ]
            ],

            self::PRIVATE_SEA_CUSTOMERS_DISTRIBUTION => [
                'name' => \Yii::t('ai', '客户分布'),
                'title' => \Yii::t('ai', '私海客户分布情况'),
                'type' => self::INSIGHT_TYPE_COMPANY,
                'section' => self::INSIGHT_SECTION_HALF_A_YEAR,

                'sub' => [
                    self::PRIVATE_SEA_CUSTOMERS_COUNTRY_DISTRIBUTION => [
                        'sub_key' => self::PRIVATE_SEA_CUSTOMERS_COUNTRY_DISTRIBUTION,
                        'report_key' => ['kh3'],
                        'subtitle' => \Yii::t('ai', '近期国家地区分布'),
                        'prompt' => '客户主要分布在哪几个国家？',
                        'cut_out' => 10,
                        'order_field' => 'row-company.company_id',
                        'order' => 'desc',
                        'stat_diff' => self::STAT_DIFF_LABEL,
                        'old_data_flag' => true,
                    ],
                    self::PRIVATE_SEA_CUSTOMERS_SOURCE_DISTRIBUTION => [
                        'sub_key' => self::PRIVATE_SEA_CUSTOMERS_SOURCE_DISTRIBUTION,
                        'report_key' => ['kh4'],
                        'subtitle' => \Yii::t('ai', '近期来源渠道分布'),
                        'prompt' => '客户主要来自哪些渠道？',
                        'cut_out' => 10,
                        'order_field' => 'row-company.company_id',
                        'order' => 'desc',
                        'stat_diff' => self::STAT_DIFF_LABEL,
                        'old_data_flag' => true,
                    ]
                ],

                'params' => [
                    'kh3' => [
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '建档时间'],
                        ['field' => 'company.owner_type', 'type' => 3, 'value' => 2],
                        ['field' => 'common.select_regional_scope', 'type' => 'select', 'value' => 3]
                    ],
                    'kh4' => [
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '建档时间'],
                        ['field' => 'company.owner_type', 'type' => 3, 'value' => 2]
                    ]
                ],
                'old_params' => [
                    'kh3' => [
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '建档时间'],
                        ['field' => 'company.owner_type', 'type' => 3, 'value' => 2],
                        ['field' => 'common.select_regional_scope', 'type' => 'select', 'value' => 3]
                    ],
                    'kh4' => [
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '建档时间'],
                        ['field' => 'company.owner_type', 'type' => 3, 'value' => 2]
                    ]
                ],

                'statistic_field' => [
                    self::PRIVATE_SEA_CUSTOMERS_SOURCE_DISTRIBUTION => [
                        'kh4' => ['company.origin_list', 'row-company.company_id', 'rowPercent-company.company_id'],
                    ]
                ],

                'summaries' => [
                    'kh4' => [
                        ['key' => 'row-company.company_id', 'type' => 'row', 'field_type' => '', 'refer_list' => 'companyList', 'label' => '客户数', 'tip' => '', 'order' => '', 'value_type' => ''],
                        ['key' => 'rowPercent-company.company_id', 'type' => 'rowPercent', 'field_type' => '', 'refer_list' => '', 'label' => '记录计数百分比(客户)', 'tip' => '', 'order' => '', 'value_type' => '']
                    ],
                    'kh3' => [
                        ['key' => 'row-company.company_id', 'type' => 'row', 'field_type' => '', 'refer_list' => 'companyList', 'label' => '客户数', 'tip' => '', 'order' => '', 'value_type' => ''],
                        ['key' => 'rowPercent-company.company_id', 'type' => 'rowPercent', 'field_type' => '', 'refer_list' => '', 'label' => '记录计数百分比(客户)', 'tip' => '', 'order' => '', 'value_type' => '']
                    ],
                ]
            ],

            self::TOP5_CUSTOMERS_WITH_SALES_ORDER => [
                'name' => \Yii::t('ai', '客户订单金额情况'),
                'title' => \Yii::t('ai', 'Top5成单客户（销售订单）'),
                'type' => self::INSIGHT_TYPE_COMPANY,
                'section' => self::INSIGHT_SECTION_HALF_A_YEAR,

                'sub' => [
                    self::TOP5_CUSTOMERS_ORDER_AMOUNT => [
                        'sub_key' => self::TOP5_CUSTOMERS_ORDER_AMOUNT,
                        'report_key' => ['kh10'],
                        'subtitle' => \Yii::t('ai', '近期成单金额Top5的客户'),
                        'prompt' => '订单金额最高的5位客户依次是谁？',
                        'order_field' => 'sum-order.amount',
                        'cut_out' => 10,
                        'stat_diff' => self::STAT_DIFF_LABEL,
                        'old_data_flag' => true,
                    ],
                    self::TOP5_CUSTOMERS_ORDER_QUANTITY => [
                        'sub_key' => self::TOP5_CUSTOMERS_ORDER_QUANTITY,
                        'report_key' => ['kh10'],
                        'subtitle' => \Yii::t('ai', '近期成单次数Top5的客户'),
                        'prompt' => '成单次数（即表头中的记录-计数）最高的5位客户依次是谁？',
                        'cut_out' => 10,
                        'order_field' => 'row-order.order_id',
                        'order' => 'desc',
                        'stat_diff' => self::STAT_DIFF_LABEL
                    ]
                ],

                'params' => [
                    'kh10' => [
                        ['field' => 'order.account_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '订单日期'],
                        ['field' => 'order.status', 'type' => 3, 'value' => []],
                    ]
                ],
                'old_params' => [
                    'kh10' => [
                        ['field' => 'order.account_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '订单日期'],
                        ['field' => 'order.status', 'type' => 3, 'value' => []],
                    ]
                ],

                'statistic_field' => [
                    self::TOP5_CUSTOMERS_ORDER_AMOUNT => [
                        'kh10' => ['company.name', 'sum-order.amount', 'sumPercent-order.amount']
                    ],
                    self::TOP5_CUSTOMERS_ORDER_QUANTITY => [
                        'kh10' => ['company.name', 'sum-order.amount', 'row-order.order_id']
                    ]
                ],

                'summaries' => [
                    'kh10' => [
                        [
                            "key" => "sum-order.amount",
                            "type" => "sum",
                            "field_type" => "currency",
                            "refer_list" => "",
                            "label" => "订单金额(销售订单)-总和",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "sumPercent-order.amount",
                            "type" => "sumPercent",
                            "field_type" => "currency",
                            "refer_list" => "",
                            "label" => "总和占比",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "row-order.order_id",
                            "type" => "row",
                            "field_type" => "",
                            "refer_list" => "orderList",
                            "label" => "订单数",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "row-cash_collection.cash_collection_id",
                            "type" => "row",
                            "field_type" => "",
                            "refer_list" => "cashCollectionList",
                            "label" => "回款单数",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "sum-cash_collection.amount",
                            "type" => "sum",
                            "field_type" => "currency",
                            "refer_list" => "",
                            "label" => "回款金额",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "row-cash_collection.cash_collection_count",
                            "type" => "row",
                            "field_type" => "",
                            "refer_list" => "cashCollectionList",
                            "label" => "已生效回款单数",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "sum-cash_collection.collect_amount",
                            "type" => "sum",
                            "field_type" => "currency",
                            "refer_list" => "",
                            "label" => "已生效回款金额",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ]
                    ]
                ]
            ],

            self::TOP5_CUSTOMERS_WITH_OPPORTUNITY => [
                'name' => \Yii::t('ai', '客户商机销售金额情况'),
                'title' => \Yii::t('ai', 'Top5成单客户（基于商机）'),
                'type' => self::INSIGHT_TYPE_COMPANY,
                'section' => self::INSIGHT_SECTION_HALF_A_YEAR,

                'sub' => [
                    self::TOP5_CUSTOMERS_OPPORTUNITY_AMOUNT => [
                        'sub_key' => self::TOP5_CUSTOMERS_OPPORTUNITY_AMOUNT,
                        'report_key' => ['kh11'],
                        'subtitle' => \Yii::t('ai', '近期商机赢单金额Top5的客户'),
                        'order_field' => 'sum-opportunity.amount',
                        'cut_out' => 5,
                        'stat_diff' => self::STAT_DIFF_LABEL,
                        'old_data_flag' => true,
                    ],
                    self::TOP5_CUSTOMERS_OPPORTUNITY_QUANTITY => [
                        'sub_key' => self::TOP5_CUSTOMERS_OPPORTUNITY_QUANTITY,
                        'report_key' => ['kh11'],
                        'subtitle' => \Yii::t('ai', '近期赢单商机数Top5的客户'),
                        'cut_out' => 5,
                        'order_field' => 'row-opportunity.opportunity_id',
                        'order' => 'desc',
                        'stat_diff' => self::STAT_DIFF_LABEL
                    ]
                ],

                'params' => [
                    'kh11' => [
                        ['field' => 'opportunity.account_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '结束日期'],
                        ['field' => 'opportunity.stage_type', 'type' => 3, 'value' => 2],
                    ]
                ],
                'old_params' => [
                    'kh11' => [
                        ['field' => 'opportunity.account_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '结束日期'],
                        ['field' => 'opportunity.stage_type', 'type' => 3, 'value' => 2],
                    ]
                ],
                'statistic_field' => [
                    self::TOP5_CUSTOMERS_OPPORTUNITY_AMOUNT => [
                        'kh11' => ['company.name', 'sum-opportunity.amount', 'sumPercent-opportunity.amount']
                    ],
                    self::TOP5_CUSTOMERS_OPPORTUNITY_QUANTITY => [
                        'kh11' => ['company.name', 'row-opportunity.opportunity_id', 'sumPercent-opportunity.amount']
                    ]
                ],
            ],

            self::ORDER_CUSTOMER_ANALYSIS => [
                'name' => \Yii::t('ai', '客户订单金额情况'),
                'title' => \Yii::t('ai', '成单客户分析'),
                'type' => self::INSIGHT_TYPE_COMPANY,
                'section' => self::INSIGHT_SECTION_HALF_A_YEAR,

                'sub' => [
                    self::ORDER_CUSTOMERS_SOURCE_DISTRIBUTION => [
                        'sub_key' => self::ORDER_CUSTOMERS_SOURCE_DISTRIBUTION,
                        'report_key' => ['khdd3'],
                        'subtitle' => \Yii::t('ai', '近期来源渠道分布'),
                        'prompt' => '成单客户数主要分布在哪几个客户来源，订单金额占比（即表头中的总和占比,单位为%，保留1位小数）从大到小依次是怎样的？',
                        'cut_out' => 10,
                        'order_field' => 'sumPercent-order.amount',
                        'order' => 'desc',
                        'stat_diff' => self::STAT_DIFF_LABEL,
                        'old_data_flag' => true,
                    ],
                    self::ORDER_CUSTOMERS_COUNTRY_DISTRIBUTION => [
                        'sub_key' => self::ORDER_CUSTOMERS_COUNTRY_DISTRIBUTION,
                        'report_key' => ['khdd6'],
                        'subtitle' => \Yii::t('ai', '近期国家地区分布'),
                        'prompt' => '成单客户数主要分布在哪几个国家地区，订单金额占比（即表头中的总和占比,单位为%，保留1位小数）从大到小依次是怎样的？',
                        'cut_out' => 10,
                        'order_field' => 'sumPercent-order.amount',
                        'order' => 'desc',
                        'stat_diff' => self::STAT_DIFF_LABEL,
                        'old_data_flag' => true,
                    ]
                ],

                'params' => [
                    'khdd3' => [
                        ['field' => 'order.account_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '订单日期'],
                        ['field' => 'order.status', 'type' => 3, 'value' => []],
                    ],
                    'khdd6' => [
                        ['field' => 'order.account_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '订单日期'],
                        ['field' => 'common.select_regional_scope', 'type' => 'select', 'value' => 3],
                        ['field' => 'order.status', 'type' => 3, 'value' => []],
                    ]
                ],
                'old_params' => [
                    'khdd3' => [
                        ['field' => 'order.account_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '订单日期'],
                        ['field' => 'order.status', 'type' => 3, 'value' => []],
                    ],
                    'khdd6' => [
                        ['field' => 'order.account_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '订单日期'],
                        ['field' => 'common.select_regional_scope', 'type' => 'select', 'value' => 3],
                        ['field' => 'order.status', 'type' => 3, 'value' => []],
                    ]
                ],

                'statistic_field' => [
                    self::ORDER_CUSTOMERS_SOURCE_DISTRIBUTION => [
                        'khdd3' => ['company.origin_list', 'row-company.company_id', 'sum-order.amount', 'sumPercent-order.amount']
                    ],
                    self::ORDER_CUSTOMERS_COUNTRY_DISTRIBUTION => [
                        'khdd6' => ['company.country', 'row-company.company_id', 'sum-order.amount', 'sumPercent-order.amount']
                    ]
                ],

                'summaries' => [
                    'khdd3' => [
                        [
                            "key" => "row-company.company_id",
                            "type" => "row",
                            "field_type" => "",
                            "refer_list" => "companyList",
                            "label" => "客户数",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "sum-order.amount",
                            "type" => "sum",
                            "field_type" => "currency",
                            "refer_list" => "",
                            "label" => "订单金额(销售订单)-总和",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "sumPercent-order.amount",
                            "type" => "sumPercent",
                            "field_type" => "currency",
                            "refer_list" => "",
                            "label" => "总和占比",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "row-order.order_id",
                            "type" => "row",
                            "field_type" => "",
                            "refer_list" => "orderList",
                            "label" => "订单数",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "row-cash_collection.cash_collection_id",
                            "type" => "row",
                            "field_type" => "",
                            "refer_list" => "cashCollectionList",
                            "label" => "回款单数",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "sum-cash_collection.amount",
                            "type" => "sum",
                            "field_type" => "currency",
                            "refer_list" => "",
                            "label" => "回款金额",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "row-cash_collection.cash_collection_count",
                            "type" => "row",
                            "field_type" => "",
                            "refer_list" => "cashCollectionList",
                            "label" => "已生效回款单数",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "sum-cash_collection.collect_amount",
                            "type" => "sum",
                            "field_type" => "currency",
                            "refer_list" => "",
                            "label" => "已生效回款金额",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ]
                    ],
                    'khdd6' => [
                        [
                            "key" => "row-company.company_id",
                            "type" => "row",
                            "field_type" => "",
                            "refer_list" => "companyList",
                            "label" => "客户数",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "sum-order.amount",
                            "type" => "sum",
                            "field_type" => "currency",
                            "refer_list" => "",
                            "label" => "订单金额(销售订单)-总和",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "sumPercent-order.amount",
                            "type" => "sumPercent",
                            "field_type" => "currency",
                            "refer_list" => "",
                            "label" => "总和占比",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "row-order.order_id",
                            "type" => "row",
                            "field_type" => "",
                            "refer_list" => "orderList",
                            "label" => "订单数",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "row-cash_collection.cash_collection_id",
                            "type" => "row",
                            "field_type" => "",
                            "refer_list" => "cashCollectionList",
                            "label" => "回款单数",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "sum-cash_collection.amount",
                            "type" => "sum",
                            "field_type" => "currency",
                            "refer_list" => "",
                            "label" => "回款金额",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "row-cash_collection.cash_collection_count",
                            "type" => "row",
                            "field_type" => "",
                            "refer_list" => "cashCollectionList",
                            "label" => "已生效回款单数",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "sum-cash_collection.collect_amount",
                            "type" => "sum",
                            "field_type" => "currency",
                            "refer_list" => "",
                            "label" => "已生效回款金额",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ]
                    ],
                ]
            ],

            self::ANALYSIS_OF_BUSINESS_OPPORTUNITIES_AND_WINNING_CUSTOMERS => [
                'name' => \Yii::t('ai', '商机赢单客户分析'),
                'title' => \Yii::t('ai', '商机赢单客户分析'),
                'type' => self::INSIGHT_TYPE_COMPANY,
                'section' => self::INSIGHT_SECTION_HALF_A_YEAR,

                'sub' => [
                    self::DISTRIBUTION_OF_SOURCE_CHANNELS_FOR_WINNING_CUSTOMERS_IN_BUSINESS_OPPORTUNITIES => [
                        'sub_key' => self::DISTRIBUTION_OF_SOURCE_CHANNELS_FOR_WINNING_CUSTOMERS_IN_BUSINESS_OPPORTUNITIES,
                        'report_key' => ['khsj3'],
                        'subtitle' => \Yii::t('ai', '近期来源渠道分布'),
                        'stat_diff' => self::STAT_DIFF_LABEL,
                        'old_data_flag' => true,
                    ],
                    self::DISTRIBUTION_OF_MAIN_COUNTRIES_AND_REGIONS_WHERE_BUSINESS_OPPORTUNITIES_AND_WINNING_CUSTOMERS_ARE_LOCATED => [
                        'sub_key' => self::DISTRIBUTION_OF_MAIN_COUNTRIES_AND_REGIONS_WHERE_BUSINESS_OPPORTUNITIES_AND_WINNING_CUSTOMERS_ARE_LOCATED,
                        'report_key' => ['khsj6'],
                        'subtitle' => \Yii::t('ai', '近期国家地区分布'),
                        'order' => 'desc',
                        'stat_diff' => self::STAT_DIFF_LABEL,
                        'old_data_flag' => true,
                    ]
                ],
                'statistic_field' => [
                    self::DISTRIBUTION_OF_SOURCE_CHANNELS_FOR_WINNING_CUSTOMERS_IN_BUSINESS_OPPORTUNITIES => [
                        'khsj3' => ['company.origin_list', 'row-company.company_id', 'sum-opportunity.amount', 'sumPercent-opportunity.amount'],
                    ],
                    self::DISTRIBUTION_OF_MAIN_COUNTRIES_AND_REGIONS_WHERE_BUSINESS_OPPORTUNITIES_AND_WINNING_CUSTOMERS_ARE_LOCATED => [
                        'khsj6' => ['company.country', 'row-company.company_id', 'sum-opportunity.amount', 'sumPercent-opportunity.amount'],
                    ]
                ],

                'params' => [
                    'khsj3' => [
                        ['field' => 'opportunity.account_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')]],
                        ['field' => 'opportunity.stage_type', 'type' => 3, 'value' => 2],
                    ],
                    'khsj6' => [
                        ['field' => 'opportunity.account_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')]],
                        ['field' => 'opportunity.stage_type', 'type' => 3, 'value' => 2],
                        ['field' => 'common.select_regional_scope', 'type' => 'select', 'value' => 3]
                    ]
                ],
                'old_params' => [
                    'khsj3' => [
                        ['field' => 'order.account_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '订单日期'],
                        ['field' => 'order.status', 'type' => 3, 'value' => []],
                    ],
                    'khsj6' => [
                        ['field' => 'order.account_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '订单日期'],
                        ['field' => 'opportunity.stage_type', 'type' => 3, 'value' => 2],
                        ['field' => 'common.select_regional_scope', 'type' => 'select', 'value' => 3]
                    ]
                ]
            ],

            self::CHANGE_TREND_ORDER_AMOUNT => [
                'name' => \Yii::t('ai', '销售订单金额构成'),
                'title' => \Yii::t('ai', '成单变化趋势'),
                'type' => self::INSIGHT_TYPE_ORDER,
                'section' => self::INSIGHT_SECTION_HALF_A_YEAR,

                'sub' => [
                    self::RECENT_CHANGE_ORDER_VALUE => [
                        'sub_key' => self::RECENT_CHANGE_ORDER_VALUE,
                        'report_key' => ['dd3'],
                        'subtitle' =>  \Yii::t('ai', '近期成交订单金额变化趋势'),
                        'prompt' => '近3个月成交订单金额（即表头中的订单金额-总和）的变化趋势是怎样的？',
                        'stat_diff' => self::STAT_DIFF_VALUE,
                        'old_data_flag' => false,
                    ],
                    self::RECENT_CHANGE_ORDER_VALUE_TWO => [
                        'sub_key' => self::RECENT_CHANGE_ORDER_VALUE_TWO,
                        'report_key' => ['dd3'],
                        'subtitle' => \Yii::t('ai', '近期成交订单数量变化趋势'),
                        'prompt' => '近3个月成交订单数（即表头中的记录-计数）的变化趋势是怎样的？',
                        'stat_diff' => self::STAT_DIFF_VALUE,
                        'old_data_flag' => false,
                    ]
                ],

                'params' => [
                    'dd3' => [
                        ['field' => 'order.account_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '订单日期'],
                        ['field' => 'common.select_cycle', 'type' => 'select', 'value' => 'week'],
                        ['field' => 'order.status', 'type' => 3, 'value' => []]
                    ]
                ],
                'old_params' => [
                    'dd3' => [
                        ['field' => 'order.account_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '订单日期'],
                        ['field' => 'common.select_cycle', 'type' => 'select', 'value' => 'week'],
                        ['field' => 'order.status', 'type' => 3, 'value' => []]
                    ]
                ],

                'statistic_field' => [
                    self::RECENT_CHANGE_ORDER_VALUE => [
                        'dd3' => ['order.account_date', 'sum-order.amount', 'row-order.order_id']
                    ],
                    self::RECENT_CHANGE_ORDER_VALUE_TWO => [
                        'dd3' => ['order.account_date', 'sum-order.amount', 'row-order.order_id']
                    ]
                ],

                'summaries' => [
                    'dd3' => [
                        [
                            "key" => "sum-order.amount",
                            "type" => "sum",
                            "field_type" => "currency",
                            "refer_list" => "",
                            "label" => "订单金额(销售订单)-总和",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "sum-order.product_total_amount",
                            "type" => "sum",
                            "field_type" => "currency",
                            "refer_list" => "",
                            "label" => "产品总金额(销售订单)-总和",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "sum-order.addition_cost_amount",
                            "type" => "sum",
                            "field_type" => "currency",
                            "refer_list" => "",
                            "label" => "附加费用总金额(销售订单)-总和",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "row-order.order_id",
                            "type" => "row",
                            "field_type" => "",
                            "refer_list" => "orderList",
                            "label" => "记录-计数",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ]
                    ]
                ]
            ],

            self::HOT_SALE_PRODUCTS => [
                'name' => \Yii::t('ai', '产品销售排行（基于销售订单)'),
                'title' => \Yii::t('ai', '热销产品Top5'),
                'type' => self::INSIGHT_TYPE_PRODUCT,
                'section' => self::INSIGHT_SECTION_HALF_A_YEAR,

                'sub' => [
                    self::RECENT_HOT_SALE_PRODUCTS => [
                        'sub_key' => self::RECENT_HOT_SALE_PRODUCTS,
                        'report_key' => ['cp1'],
                        'subtitle' => \Yii::t('ai', '近期热销产品'),
                        'prompt' => '销售数量（即表头中：数量（交易产品）-总和占比）最高的5个产品从高到低依次是哪些，占比是多少（单位为%，保留1位小数）？',
                        'cut_out' => 10,
                        'order_field' => 'sumPercent-invoice_product.count',
                        'order' => 'desc',
                        'stat_diff' => self::STAT_DIFF_LABEL,
                        'old_data_flag' => true,
                    ],
                    self::RECENT_HOT_SALE_PRODUCTS_MODEL => [
                        'sub_key' => self::RECENT_HOT_SALE_PRODUCTS_MODEL,
                        'report_key' => ['cp23'],
                        'subtitle' => \Yii::t('ai', '近期热销产品型号'),
                        'prompt' => '销售数量（即表头中：数量（交易产品）-总和占比）最高的5个产品型号从高到低依次是哪些，占比是多少（单位为%，保留1位小数）？',
                        'cut_out' => 10,
                        'order_field' => 'sumPercent-invoice_product.count',
                        'order' => 'desc',
                        'stat_diff' => self::STAT_DIFF_LABEL,
                        'old_data_flag' => true,
                    ],
                    self::RECENT_HOT_SALE_PRODUCTS_GROUP => [
                        'sub_key' => self::RECENT_HOT_SALE_PRODUCTS_GROUP,
                        'report_key' => ['cp3'],
                        'subtitle' => \Yii::t('ai', '近期热销产品分组'),
                        'prompt' => '销售数量（即表头中：数量（交易产品）-总和占比）最高的5个产品分组从高到低依次是哪些，占比是多少（单位为%，保留1位小数）？',
                        'cut_out' => 10,
                        'order_field' => 'sumPercent-invoice_product.count',
                        'order' => 'desc',
                        'stat_diff' => self::STAT_DIFF_LABEL,
                        'old_data_flag' => true,
                    ],
                    self::RECENT_HOT_SALE_PRODUCTS_CATEGORIES => [
                        'sub_key' => self::RECENT_HOT_SALE_PRODUCTS_CATEGORIES,
                        'report_key' => ['cp2'],
                        'subtitle' => \Yii::t('ai', '近期热销产品类目'),
                        'prompt' => '销售数量（即表头中：数量（交易产品）-总和占比）最高的5个产品类目从高到低依次是哪些，占比是多少（单位为%，保留1位小数）？',
                        'cut_out' => 10,
                        'order_field' => 'sumPercent-invoice_product.count',
                        'order' => 'desc',
                        'stat_diff' => self::STAT_DIFF_LABEL,
                        'old_data_flag' => true,
                    ]

                ],

                'params' => [
                    'cp1' => [
                        ['field' => 'order.account_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '订单日期'],
                        ['field' => 'order.status', 'type' => 3, 'value' => []]
                    ],
                    'cp23' => [
                        ['field' => 'order.account_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '订单日期'],
                        ['field' => 'order.status', 'type' => 3, 'value' => []]
                    ],
                    'cp3' => [
                        ['field' => 'order.account_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '订单日期'],
                        ['field' => 'order.status', 'type' => 3, 'value' => []]
                    ],
                    'cp2' => [
                        ['field' => 'order.account_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '订单日期'],
                        ['field' => 'order.status', 'type' => 3, 'value' => []]
                    ]
                ],
                'old_params' => [
                    'cp1' => [
                        ['field' => 'order.account_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '订单日期'],
                        ['field' => 'order.status', 'type' => 3, 'value' => []]
                    ],
                    'cp23' => [
                        ['field' => 'order.account_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '订单日期'],
                        ['field' => 'order.status', 'type' => 3, 'value' => []]
                    ],
                    'cp3' => [
                        ['field' => 'order.account_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '订单日期'],
                        ['field' => 'order.status', 'type' => 3, 'value' => []]
                    ],
                    'cp2' => [
                        ['field' => 'order.account_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '订单日期'],
                        ['field' => 'order.status', 'type' => 3, 'value' => []]
                    ]
                ],

                'statistic_field' => [
                    self::RECENT_HOT_SALE_PRODUCTS => [
                        'cp1' => ['product.product_no', 'sum-invoice_product.count', 'sumPercent-invoice_product.count', 'row-order.order_id']
                    ],
                    self::RECENT_HOT_SALE_PRODUCTS_MODEL => [
                        'cp23' => ['product.model', 'sum-invoice_product.count', 'sumPercent-invoice_product.count', 'row-order.order_id']
                    ],
                    self::RECENT_HOT_SALE_PRODUCTS_GROUP => [
                        'cp3' => ['product.group_id', 'sum-invoice_product.count', 'sumPercent-invoice_product.count', 'row-order.order_id']
                    ],
                    self::RECENT_HOT_SALE_PRODUCTS_CATEGORIES => [
                        'cp2' => ['product.category_ids', 'sum-invoice_product.count', 'sumPercent-invoice_product.count', 'row-order.order_id']
                    ]
                ],

                'summaries' => [
                    'cp1' => [
                        [
                            "key" => "sum-invoice_product.count",
                            "type" => "sum",
                            "field_type" => "",
                            "refer_list" => "",
                            "label" => "数量(交易产品)-总和",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "sumPercent-invoice_product.count",
                            "type" => "sumPercent",
                            "field_type" => "",
                            "refer_list" => "",
                            "label" => "数量(交易产品)-总和占比",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "sum-invoice_product.amount",
                            "type" => "sum",
                            "field_type" => "currency",
                            "refer_list" => "",
                            "label" => "金额小计(交易产品)-总和",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "sumPercent-invoice_product.amount",
                            "type" => "sumPercent",
                            "field_type" => "currency",
                            "refer_list" => "",
                            "label" => "金额小计(交易产品)-总和占比",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "row-order.order_id",
                            "type" => "row",
                            "field_type" => "",
                            "refer_list" => "orderList",
                            "label" => "记录-计数",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ]
                    ],
                    'cp23' => [
                        [
                            "key" => "sum-invoice_product.count",
                            "type" => "sum",
                            "field_type" => "",
                            "refer_list" => "",
                            "label" => "数量(交易产品)-总和",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "sumPercent-invoice_product.count",
                            "type" => "sumPercent",
                            "field_type" => "",
                            "refer_list" => "",
                            "label" => "数量(交易产品)-总和占比",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "sum-invoice_product.amount",
                            "type" => "sum",
                            "field_type" => "currency",
                            "refer_list" => "",
                            "label" => "金额小计(交易产品)-总和",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "sumPercent-invoice_product.amount",
                            "type" => "sumPercent",
                            "field_type" => "currency",
                            "refer_list" => "",
                            "label" => "金额小计(交易产品)-总和占比",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "row-order.order_id",
                            "type" => "row",
                            "field_type" => "",
                            "refer_list" => "orderList",
                            "label" => "记录-计数",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ]
                    ],
                    'cp3' => [
                        [
                            "key" => "sum-invoice_product.count",
                            "type" => "sum",
                            "field_type" => "",
                            "refer_list" => "",
                            "label" => "数量(交易产品)-总和",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "sumPercent-invoice_product.count",
                            "type" => "sumPercent",
                            "field_type" => "",
                            "refer_list" => "",
                            "label" => "数量(交易产品)-总和占比",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "sum-invoice_product.amount",
                            "type" => "sum",
                            "field_type" => "currency",
                            "refer_list" => "",
                            "label" => "金额小计(交易产品)-总和",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "sumPercent-invoice_product.amount",
                            "type" => "sumPercent",
                            "field_type" => "currency",
                            "refer_list" => "",
                            "label" => "金额小计(交易产品)-总和占比",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                        [
                            "key" => "row-order.order_id",
                            "type" => "row",
                            "field_type" => "",
                            "refer_list" => "orderList",
                            "label" => "记录-计数",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ]
                    ],
                    'cp2' => [
                        [
                            'key' => 'sum-invoice_product.count',
                            'type' => 'sum',
                            'field_type' => '',
                            'refer_list' => '',
                            'label' => '数量(交易产品)-总和',
                            'tip' => '',
                            'order' => '',
                            'value_type' => '',
                            'hidden' => 0,
                        ],
                        [
                            'key' => 'sumPercent-invoice_product.count',
                            'type' => 'sumPercent',
                            'field_type' => '',
                            'refer_list' => '',
                            'label' => '数量(交易产品)-总和占比',
                            'tip' => '',
                            'order' => '',
                            'value_type' => '',
                            'hidden' => 0,
                        ],
                        [
                            'key' => 'sum-invoice_product.amount',
                            'type' => 'sum',
                            'field_type' => 'currency',
                            'refer_list' => '',
                            'label' => '金额小计(交易产品)-总和',
                            'tip' => '',
                            'order' => '',
                            'value_type' => '',
                            'hidden' => 0,
                        ],
                        [
                            'key' => 'sumPercent-invoice_product.amount',
                            'type' => 'sumPercent',
                            'field_type' => 'currency',
                            'refer_list' => '',
                            'label' => '金额小计(交易产品)-总和占比',
                            'tip' => '',
                            'order' => '',
                            'value_type' => '',
                            'hidden' => 0,
                        ],
                        [
                            'key' => 'row-order.order_id',
                            'type' => 'row',
                            'field_type' => '',
                            'refer_list' => 'orderList',
                            'label' => '记录-计数',
                            'tip' => '',
                            'order' => '',
                            'value_type' => '',
                            'hidden' => 0,
                        ]
                    ],
                ]
            ],

            self::ANALYSIS_REASON_OF_FAIL_OPPORTUNITY => [
                'name' => \Yii::t('ai', '商机输单原因分析'),
                'title' => \Yii::t('ai', '商机输单原因分析'),
                'type' => self::INSIGHT_TYPE_OPPORTUNITY,
                'section' => self::INSIGHT_SECTION_HALF_A_YEAR,

                'sub' => [
                    self::ANALYSIS_MAIN_REASON_OF_FAIL_OPPORTUNITY => [
                        'sub_key' => self::ANALYSIS_MAIN_REASON_OF_FAIL_OPPORTUNITY,
                        'report_key' => ['sj12'],
                        'subtitle' => \Yii::t('ai', '近期输单原因分析'),
                        'prompt' => '近期主要输单原因分析？',
                        'stat_diff' => self::STAT_DIFF_VALUE,
                        'old_data_flag' => true,
                        'order_field' => 'rowPercent-opportunity.opportunity_id',
                        'order' => 'desc',
                    ],
//                    self::ANALYSIS_OF_FAIL_REASON_FOR_EMPLOYEE => [
//                        'sub_key' => self::ANALYSIS_OF_FAIL_REASON_FOR_EMPLOYEE,
//                        'report_key' => ['sj16'],
//                        'subtitle' => '近期各员工输单原因分析',
//                        'prompt' => '请分析一下近3个月各员工最主要的商机输单原因，并给出计数百分比',
//                        'stat_diff' => self::STAT_DIFF_LABEL,
//                        'old_data_flag' => true,
//                        'order_field' => 'rowPercent-opportunity.opportunity_id',
//                        'order' => 'desc',
//
//                    ]
                ],

                'params' => [
                    'sj12' => [
                        ['field' => 'opportunity.create_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '创建时间'],
                    ],
//                    'sj16' => [
//                        ['field' => 'opportunity.create_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '创建时间'],
//                    ]
                ],
                'old_params' => [
                    'sj12' => [
                        ['field' => 'opportunity.create_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '创建时间'],
                    ],
                    'sj16' => [
                        ['field' => 'opportunity.create_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '创建时间'],
                    ]
                ],

                'summaries' => [
                    'sj12' => [
                        [
                            "key" => "rowPercent-opportunity.opportunity_id",
                            "type" => "rowPercent",
                            "field_type" => "",
                            "refer_list" => "",
                            "label" => "计数百分比",
                            "tip" => "",
                            "order" => "",
                            "value_type" => "",
                            "hidden" => 0
                        ],
                    ],
//                    'sj16' => [
//                        [
//                            "key" => "rowPercent-opportunity.opportunity_id",
//                            "type" => "rowPercent",
//                            "field_type" => "",
//                            "refer_list" => "",
//                            "label" => "计数百分比",
//                            "tip" => "",
//                            "order" => "",
//                            "value_type" => "",
//                            "hidden" => 0
//                        ],
//                    ]
                ]
            ],

            self::TEAM_WORK_SITUATION => [
                'name' => \Yii::t('ai', '团队工作情况分析'),
                'title' => \Yii::t('ai', '团队工作情况分析'),
                'type' => self::INSIGHT_TYPE_OPPORTUNITY,
                'section' => self::INSIGHT_SECTION_HALF_A_YEAR,

                'sub' => [
                    self::ALL_TEAM_WORK_SITUATION => [
                        'sub_key' => self::ALL_TEAM_WORK_SITUATION,
                        'report_key' => ['xs1'],
                        'subtitle' => \Yii::t('ai', '团队整体工作情况'),
                        'prompt' => '请分析一下近3个月团队员工各项工作数据情况，作为管理者 需要关注哪些落后员工，奖励哪些优秀员工？并按照以下角度去分析：
1、登录天数代表工作积极性
2、新建线索数 和新建客户数 代表开发客户的工作情况
3、新建跟进、动态数、邮件发送数、营销发送数 代表跟进客户的情况
4、新建商机数、新建订单数 代表转化客户的情况',
                        'stat_diff' => self::STAT_DIFF_VALUE,
                        'old_data_flag' => true,
                        'order_field' => 'sum-user.company_add_count',
                        'order' => 'desc',
                        'cut_out' => 50,
                    ],
                    self::COMPANY_FOLLOW_UP_SITUATION => [
                        'sub_key' => self::COMPANY_FOLLOW_UP_SITUATION,
                        'report_key' => ['khgj3'],
                        'subtitle' => \Yii::t('ai', '跟进客户情况'),
                        'prompt' => '', // 该字段已废弃使用
                        'stat_diff' => self::STAT_DIFF_VALUE,
                        'old_data_flag' => true,
                        'order_field' => 'send.message_company_count',
                        'order' => 'desc',
                        'cut_out' => 50,
                        'repeat_day' => date('Y-m-d', strtotime('this week')), // 如果没有设置该字段，默认需要每天都跑。如果设置了该字段，那么只需要在特定的日期跑，这里的情况是只需要在每周一跑。
                    ],
                ],

                'params' => [
                    'xs1' => [
                        ['field' => 'common.date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '时间'],
                    ],
                    'khgj3' => [
                        ['field' => 'common.date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('last week')), 'end' => date('Y-m-d', strtotime('last sunday'))], 'field_name' => '跟进时间'],
                    ]
                ],
                'old_params' => [
                    'xs1' => [
                        ['field' => 'common.date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '时间'],
                    ],
                    'khgj3' => [
                        ['field' => 'common.date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('last week -1 week')), 'end' => date('Y-m-d', strtotime('last sunday -1 week'))], 'field_name' => '跟进时间'],
                    ]
                ],

                'summaries' => []
            ],

            self::NEW_CUSTOMER_TRANSACTION_CONVERSION_STATUS_WITH_SALES_ORDER => [
                'name' => \Yii::t('ai', '新客户成交转化情况（基于销售订单）'),
                'title' => \Yii::t('ai', '新客户成交转化情况（基于销售订单）'),
                'type' => self::INSIGHT_TYPE_ORDER,
                'section' => self::INSIGHT_SECTION_HALF_A_YEAR,

                'sub' => [
                    self::TREND_OF_CUSTOMER_TRANSACTION_CONVERSION_RATE_CHANGES_WITH_SALE_ORDER => [
                        'sub_key' => self::TREND_OF_CUSTOMER_TRANSACTION_CONVERSION_RATE_CHANGES_WITH_SALE_ORDER,
                        'report_key' => ['kh14'],
                        'subtitle' => \Yii::t('ai', '客户成交转化率变化趋势'),
                        'stat_diff' => self::STAT_DIFF_VALUE,
                        'old_data_flag' => true,
                        'order_field' => 'company.archive_time',
                        'order' => 'desc',
                        'cut_out' => 100,
                    ],

                    self::DISTRIBUTION_OF_UNIT_PRICE_AND_CONVERSION_RATE_BY_COUNTRY_AND_REGION_WITH_SALE_ORDER => [
                        'sub_key' => self::DISTRIBUTION_OF_UNIT_PRICE_AND_CONVERSION_RATE_BY_COUNTRY_AND_REGION_WITH_SALE_ORDER,
                        'report_key' => ['kh16'],
                        'subtitle' => \Yii::t('ai', '国家地区的客单价与转化率分布'),
                        'stat_diff' => self::STAT_DIFF_LABEL,
                        'old_data_flag' => true,
                        'order_field' => 'formula-transform',
                        'order' => 'desc',
                        'cut_out' => 10,
                    ],

                    self::DISTRIBUTION_OF_UNIT_PRICE_AND_CONVERSION_RATE_OF_SOURCE_CHANNELS_WITH_SALE_ORDER => [
                        'sub_key' => self::DISTRIBUTION_OF_UNIT_PRICE_AND_CONVERSION_RATE_OF_SOURCE_CHANNELS_WITH_SALE_ORDER,
                        'report_key' => ['kh17'],
                        'subtitle' => \Yii::t('ai', '来源渠道的客单价与转化率分布'),
                        'stat_diff' => self::STAT_DIFF_LABEL,
                        'old_data_flag' => false,
                        'order_field' => 'formula-transform',
                        'order' => 'desc',
                        'cut_out' => 10,
                    ],
                ],

                'params' => [
                    'kh14' => [
                        ['field' => 'common.select_cycle', 'type' => 'select', 'value' => 'week'],
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '建档时间'],
                    ],
                    'kh17' => [
                        ['field' => 'company.select_origin_scope', 'type' => 'select', 'value' => 1],
                        ['field' => 'common.select_cycle', 'type' => 'select', 'value' => 'week'],
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '建档时间'],
                    ],
                    'kh16' => [
                        ['field' => 'company.select_origin_scope', 'type' => 'select', 'value' => 3],
                        ['field' => 'common.select_cycle', 'type' => 'select', 'value' => 'week'],
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '建档时间'],
                    ]
                ],
                'old_params' => [
                    'kh14' => [
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '建档时间'],
                        ['field' => 'common.select_cycle', 'type' => 'select', 'value' => 'week'],
                    ],
                    'kh17' => [
                        ['field' => 'company.select_origin_scope', 'type' => 'select', 'value' => 1],
                        ['field' => 'common.select_cycle', 'type' => 'select', 'value' => 'week'],
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '建档时间'],
                    ],
                    'kh16' => [
                        ['field' => 'company.select_origin_scope', 'type' => 'select', 'value' => 3],
                        ['field' => 'common.select_cycle', 'type' => 'select', 'value' => 'week'],
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '建档时间'],
                    ]
                ],

                'summaries' => []
            ],


            self::NEW_CUSTOMER_TRANSACTION_CONVERSION_STATUS_WITH_OPPORTUNITY => [
                'name' => \Yii::t('ai', '新客户成交转化情况（基于商机）'),
                'title' => \Yii::t('ai', '新客户成交转化情况（基于商机）'),
                'type' => self::INSIGHT_TYPE_OPPORTUNITY,
                'section' => self::INSIGHT_SECTION_HALF_A_YEAR,

                'sub' => [
                    self::TREND_OF_CUSTOMER_TRANSACTION_CONVERSION_RATE_CHANGES_WITH_OPPORTUNITY => [
                        'sub_key' => self::TREND_OF_CUSTOMER_TRANSACTION_CONVERSION_RATE_CHANGES_WITH_OPPORTUNITY,
                        'report_key' => ['kh24'],
                        'subtitle' => \Yii::t('ai', '客户成交转化率变化趋势'),
                        'stat_diff' => self::STAT_DIFF_VALUE,
                        'old_data_flag' => false,
                        'order_field' => 'formula-transform',
                        'order' => 'desc',
                        'cut_out' => 50,
                    ],
                    self::DISTRIBUTION_OF_UNIT_PRICE_AND_CONVERSION_RATE_BY_COUNTRY_AND_REGION_WITH_OPPORTUNITY => [
                        'sub_key' => self::DISTRIBUTION_OF_UNIT_PRICE_AND_CONVERSION_RATE_BY_COUNTRY_AND_REGION_WITH_OPPORTUNITY,
                        'report_key' => ['kh26'],
                        'subtitle' => \Yii::t('ai', '国家地区的客单价与转化率分布'),
                        'stat_diff' => self::STAT_DIFF_LABEL,
                        'old_data_flag' => false,
                        'order_field' => 'formula-transform',
                        'order' => 'desc',
                        'cut_out' => 50,
                    ],
                    self::DISTRIBUTION_OF_UNIT_PRICE_AND_CONVERSION_RATE_OF_SOURCE_CHANNELS_WITH_OPPORTUNITY => [
                        'sub_key' => self::DISTRIBUTION_OF_UNIT_PRICE_AND_CONVERSION_RATE_OF_SOURCE_CHANNELS_WITH_OPPORTUNITY,
                        'report_key' => ['kh27'],
                        'subtitle' => \Yii::t('ai', '来源渠道的客单价与转化率分布'),
                        'stat_diff' => self::STAT_DIFF_LABEL,
                        'old_data_flag' => true,
                        'order_field' => 'formula-transform',
                        'order' => 'desc',
                        'cut_out' => 50,
                    ],
                ],

                'params' => [
                    'kh24' => [
                        ['field' => 'common.select_cycle', 'type' => 'select', 'value' => 'week'],
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '建档时间'],
                    ],
                    'kh26' => [
                        ['field' => 'company.select_origin_scope', 'type' => 'select', 'value' => 3],
                        ['field' => 'common.select_cycle', 'type' => 'select', 'value' => 'week'],
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '建档时间'],
                    ],
                    'kh27' => [
                        ['field' => 'company.select_origin_scope', 'type' => 'select', 'value' => 1],
                        ['field' => 'common.select_cycle', 'type' => 'select', 'value' => 'week'],
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '建档时间'],
                    ]
                ],
                'old_params' => [
                    'kh24' => [
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '建档时间'],
                        ['field' => 'common.select_cycle', 'type' => 'select', 'value' => 'week'],
                    ],
                    'kh26' => [
                        ['field' => 'company.select_origin_scope', 'type' => 'select', 'value' => 1],
                        ['field' => 'common.select_cycle', 'type' => 'select', 'value' => 'week'],
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '建档时间'],
                    ],
                    'kh27' => [
                        ['field' => 'company.select_origin_scope', 'type' => 'select', 'value' => 3],
                        ['field' => 'common.select_cycle', 'type' => 'select', 'value' => 'week'],
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '建档时间'],
                    ]

                ],

                'summaries' => []
            ],


            self::CUSTOMER_REPURCHASE_SITUATION_WITH_SALES_ORDER => [
                'name' => \Yii::t('ai', '客户复购情况（基于销售订单）'),
                'title' => \Yii::t('ai', '客户复购情况（基于销售订单）'),
                'type' => self::INSIGHT_TYPE_ORDER,

                'sub' => [
                    self::DISTRIBUTION_OF_REPURCHASE_RATE_AND_REPURCHASE_CYCLE_OF_SOURCE_CHANNELS_WITH_SALE_ORDER => [
                        'sub_key' => self::DISTRIBUTION_OF_REPURCHASE_RATE_AND_REPURCHASE_CYCLE_OF_SOURCE_CHANNELS_WITH_SALE_ORDER,
                        'report_key' => ['kh22'],
                        'subtitle' => \Yii::t('ai', '来源渠道的复购率和复购周期分布'),
                        'stat_diff' => self::STAT_DIFF_LABEL,
                        'old_data_flag' => true,
                        'order_field' => 'formula-company.repeat_customer_rate',
                        'order' => 'desc',
                        'cut_out' => 10,
                    ],
                    self::DISTRIBUTION_OF_REPURCHASE_RATES_AND_REPURCHASE_CYCLES_IN_DIFFERENT_COUNTRIES_AND_REGIONS_WITH_SALE_ORDER => [
                        'sub_key' => self::DISTRIBUTION_OF_REPURCHASE_RATES_AND_REPURCHASE_CYCLES_IN_DIFFERENT_COUNTRIES_AND_REGIONS_WITH_SALE_ORDER,
                        'report_key' => ['kh21'],
                        'subtitle' => \Yii::t('ai', '国家地区的复购率和复购周期分布'),
                        'stat_diff' => self::STAT_DIFF_LABEL,
                        'old_data_flag' => true,
                        'order_field' => 'formula-company.repeat_customer_rate',
                        'order' => 'desc',
                        'cut_out' => 10,
                    ],
                ],

                'params' => [
                    'kh22' => [
                        ['field' => 'company.select_origin_scope', 'type' => 'select', 'value' => 1],
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '建档时间'],
                    ],
                    'kh21' => [
                        ['field' => 'company.select_origin_scope', 'type' => 'select', 'value' => 3],
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '建档时间'],
                    ],
                ],
                'old_params' => [
                    'kh22' => [
                        ['field' => 'company.select_origin_scope', 'type' => 'select', 'value' => 1],
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '建档时间'],
                    ],
                    'kh21' => [
                        ['field' => 'company.select_origin_scope', 'type' => 'select', 'value' => 3],
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '建档时间'],
                    ],
                ],

                'summaries' => []
            ],



            self::CUSTOMER_REPURCHASE_SITUATION_WITH_OPPORTUNITY => [
                'name' => \Yii::t('ai', '客户复购情况（基于商机）'),
                'title' => \Yii::t('ai', '客户复购情况（基于商机）'),
                'type' => self::INSIGHT_TYPE_OPPORTUNITY,
                'section' => self::INSIGHT_SECTION_HALF_A_YEAR,

                'sub' => [
                    self::DISTRIBUTION_OF_REPURCHASE_RATE_AND_REPURCHASE_CYCLE_OF_SOURCE_CHANNELS_WITH_OPPORTUNITY => [
                        'sub_key' => self::DISTRIBUTION_OF_REPURCHASE_RATE_AND_REPURCHASE_CYCLE_OF_SOURCE_CHANNELS_WITH_OPPORTUNITY,
                        'report_key' => ['kh31'],
                        'subtitle' => \Yii::t('ai', '来源渠道的复购率和复购周期分布'),
                        'stat_diff' => self::STAT_DIFF_LABEL,
                        'old_data_flag' => true,
                        'order_field' => 'formula-company.repeat_customer_rate',
                        'order' => 'desc',
                        'cut_out' => 10,
                    ],
                    self::DISTRIBUTION_OF_REPURCHASE_RATES_AND_REPURCHASE_CYCLES_IN_DIFFERENT_COUNTRIES_AND_REGIONS_WITH_OPPORTUNITY => [
                        'sub_key' => self::DISTRIBUTION_OF_REPURCHASE_RATES_AND_REPURCHASE_CYCLES_IN_DIFFERENT_COUNTRIES_AND_REGIONS_WITH_OPPORTUNITY,
                        'report_key' => ['kh30'],
                        'subtitle' => \Yii::t('ai', '国家地区的复购率和复购周期分布'),
                        'stat_diff' => self::STAT_DIFF_LABEL,
                        'old_data_flag' => true,
                        'order_field' => 'formula-company.repeat_customer_rate',
                        'order' => 'desc',
                        'cut_out' => 10,
                    ]
                ],

                'params' => [
                    'kh31' => [
                        ['field' => 'company.select_origin_scope', 'type' => 'select', 'value' => 1],
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '建档时间'],
                    ],
                    'kh30' => [
                        ['field' => 'company.select_origin_scope', 'type' => 'select', 'value' => 3],
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')], 'field_name' => '建档时间'],
                    ],
                ],
                'old_params' => [
                    'kh31' => [
                        ['field' => 'company.select_origin_scope', 'type' => 'select', 'value' => 1],
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '建档时间'],
                    ],
                    'kh30' => [
                        ['field' => 'company.select_origin_scope', 'type' => 'select', 'value' => 3],
                        ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-6 months')), 'end' => date('Y-m-d', strtotime('-3 months'))], 'field_name' => '建档时间'],
                    ],
                ],

                'summaries' => []
            ],

            self::CUSTOMER_FOLLOW_REMIND => [
                'name' => \Yii::t('ai', '客户跟进提醒'),
                'title' => \Yii::t('ai', '客户跟进提醒'),
                'type' => self::INSIGHT_TYPE_COMPANY,

                'sub' => [
                    self::PREDICT_REORDER_COMPANY_WITH_ORDER => [
                        'sub_key' => self::PREDICT_REORDER_COMPANY_WITH_ORDER,
                        'report_key' => ['khfd1'],
                        'subtitle' => \Yii::t('ai', '本周可能返单客户（基于销售订单）'),
                        'stat_diff' => '',
                        'old_data_flag' => false,
                        'order_field' => 'transaction_order_amount_avg',
                        'order' => 'desc',
                        'cut_out' => 20,
                        'richness_skip_flag' => true,  // 跳过丰富度才需要配置该key
                        'repeat_day' => date('Y-m-d', strtotime('this week')),
                    ],
                    self::PREDICT_REORDER_COMPANY_WITH_OPPORTUNITY => [
                        'sub_key' => self::PREDICT_REORDER_COMPANY_WITH_OPPORTUNITY,
                        'report_key' => ['khfd2'],
                        'subtitle' => \Yii::t('ai', '本周可能返单客户（基于商机）'),
                        'stat_diff' => '',
                        'old_data_flag' => false,
                        'order_field' => 'success_opportunity_amount_avg_cny',
                        'order' => 'desc',
                        'cut_out' => 20,
                        'richness_skip_flag' => true,  // 跳过丰富度才需要配置该key
                        'repeat_day' => date('Y-m-d', strtotime('this week')),
                    ],
                ],

                'params' => [
                    'khfd1' => [
                        ['field' => 'predict_transaction_order_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('monday this week')), 'end' => date('Y-m-d', strtotime('sunday this week'))]],
                    ],
                    'khfd2' => [
                        ['field' => 'predict_transaction_order_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('monday this week')), 'end' => date('Y-m-d', strtotime('sunday this week'))]],
                    ],
                ],
            ],
        ];
    }


    public function getConfig($clientId = 0)
    {
        $configs = $this->config;
        if (empty($clientId)) {
            return $configs;
        }

        $invoiceStatusService = new InvoiceStatusService($clientId, \Constants::TYPE_ORDER);
        $endOrderStatus = $invoiceStatusService->filterEndOrderStatus($clientId, ['已作废', '售后', '交易取消']);
        $endOrderStatusIds = array_column($endOrderStatus, 'id');

        foreach ($configs as $cardKey => $config) {
            $params = $config['params'] ?? [];
            $supplementParams = $params;
            foreach ($params as $reportKey => $param) {
                $supplementParam = $param;
                foreach ($param as $key => $item) {
                    $field = $item['field'] ?? '';
                    if ($field == 'order.status') {
                        $supplementParam[$key]['value'] = $endOrderStatusIds;
                    }
                }
                $supplementParams[$reportKey] = $supplementParam;
            }
            $configs[$cardKey]['params'] = $supplementParams;
        }

        return $configs;
    }


    public function getPerformanceConfig($ruleId,$performanceType,$viewableUserIds)
    {
        $performanceConfig = [
            self::RESULT_TARGET_WARNING => [
                'name' => \Yii::t('ai', '结果目标完成情况监测'),
                'title' => \Yii::t('ai', '结果目标完成情况监测'),
                'ruleId' => $ruleId,
                'type' => self::INSIGHT_TYPE_TARGET,
                'section' => self::INSIGHT_SECTION_BEFORE_TWO_MONTHS,
                'sub' => [
                    self::RESULT_GOAL_LOWER_THAN_TWENTY_PERCENT_BEFORE_TWO_MONTHS => [
                        'sub_key' => self::RESULT_GOAL_LOWER_THAN_TWENTY_PERCENT_BEFORE_TWO_MONTHS,
                        'report_key' => ['yjbhu'],
                        'subtitle' => \Yii::t('ai', '结果目标完成情况监测'),
                        'prompt' => '',
                        'stat_diff' => '',
                        'old_data_flag' => true,
                    ],
                ],
                'params' => [
                    'yjbhu' => [
                        ['field' => 'common.account_date', 'field_name' => '提交时间', 'type' => 'date', 'value' => ['start' => date("Y-m-01", strtotime("first day of last month")), 'end' => date("Y-m-d", strtotime("last day of last month", time()))]],
                        ['field' => 'common.rule_id', 'type' => 'performance_rule_id', 'field_name' => '目标', 'value' => $ruleId],
                        [
                            'field' => 'common.visible',
                            'type' => 'select_visible_user_id',
                            'value' => $viewableUserIds,
                        ]
                    ],
                ],
                'statistic_field' => [],
                'old_params' => [
                    'yjbhu' => [
                        ['field' => 'common.account_date', 'field_name' => '提交时间', 'type' => 'date', 'value' => ['start' => date("Y-m-01", strtotime("-2 month", time())), 'end' => date("Y-m-d", strtotime("last day of -2 month", time()))]],
                        ['field' => 'common.rule_id', 'type' => 'performance_rule_id', 'field_name' => '目标', 'value' => $ruleId],
                        [
                            'field' => 'common.visible',
                            'type' => 'select_visible_user_id',
                            'value' => $viewableUserIds,
                        ]
                    ],
                ]
            ],
            self::PROCESS_TARGET_WARNING => [
                'name' => \Yii::t('ai', '过程目标完成情况监测'),
                'title' => \Yii::t('ai', '过程目标完成情况监测'),
                'type' => self::INSIGHT_TYPE_TARGET,
                'section' => self::INSIGHT_SECTION_BEFORE_TWO_MONTHS,
                'ruleId' => $ruleId,
                'sub' => [
                    self::PROCESS_GOAL_LOWER_THAN_TWENTY_PERCENT_BEFORE_TWO_MONTHS => [
                        'sub_key' => self::PROCESS_GOAL_LOWER_THAN_TWENTY_PERCENT_BEFORE_TWO_MONTHS,
                        'report_key' => ['yjbhu'],
                        'subtitle' =>  \Yii::t('ai', '过程目标完成情况监测'),
                        'prompt' => '',
                        'stat_diff' => '',
                        'old_data_flag' => true,
                    ],
                ],
                'params' => [
                    'yjbhu' => [
                        ['field' => 'common.account_date', 'field_name' => '提交时间', 'type' => 'date', 'value' => ['start' => date("Y-m-01", strtotime("first day of last month")), 'end' => date("Y-m-d", strtotime("last day of last month", time()))]],
                        ['field' => 'common.rule_id', 'type' => 'performance_rule_id', 'field_name' => '目标', 'value' => $ruleId],
                        [
                            'field' => 'common.visible',
                            'type' => 'select_visible_user_id',
                            'value' => $viewableUserIds,
                        ]
                    ],
                ],
                'old_params' => [
                    'yjbhu' => [
                        ['field' => 'common.account_date', 'field_name' => '提交时间', 'type' => 'date', 'value' => ['start' => date("Y-m-01", strtotime("-2 month", time())), 'end' => date("Y-m-d", strtotime("last day of -2 month", time()))]],
                        ['field' => 'common.rule_id', 'type' => 'performance_rule_id', 'field_name' => '目标', 'value' => $ruleId],
                        [
                            'field' => 'common.visible',
                            'type' => 'select_visible_user_id',
                            'value' => $viewableUserIds,
                        ]
                    ],
                ]
            ],
        ];

        $subKey = $performanceType == \PerformanceV2Goals::TARGET_RESULT_GOAL ? self::RESULT_TARGET_WARNING : self::PROCESS_TARGET_WARNING;
        $this->performanceConfig[$subKey] = $performanceConfig[$subKey];
        return $this->performanceConfig;

    }


    public static function getQuestionRecommendPromptTemplate()
    {
        return <<<PROMPT
你是saas领域的专家，现在负责跨境电商text to sql的业务。刚刚你的同事根据用户报表数据进行了数据分析，老板希望你根据报表名及数据分析结果向用户推荐五个用户可能关心的问题。
请先根据报表名和数据分析结果考虑用户可能关心的信息，然后进行问题推荐。由于在用户点击问题后，你需要通过数据分析回答这些问题，请确保这些问题能通过写SQL和对数据库中已有的信息进行回答。Let's think step by step!

## 数据表的描述
'''
{{table_info}}
'''
请注意上面是公司数据库，你推荐的问题需要基于该数据库进行数据收集，并进一步分析回答。**请确保你推荐的问题不需要用到超出数据库范围的数据！**

## 推荐问题要求
### 首要要求
- 基于以下数据表的描述的相关字段生成问题，请保证生成的问题是**能使用SQL语句**查询的。
- 请注意不要推荐分析结果中已经给出的问题。
- 如果数据分析结果中存在**具体的信息**，如名字，国家等信息，可以考虑在推荐的问题中保留这些信息。
- 不允许推荐下面四种问题
    - 1.非数据问题：与数据完全无关的问题，如：如何改进客户服务？
    - 2.外部数据问题：需要使用到超出上面所给数据库范围的数据才能回答的问题，如：‘客户报价数据’
    - 3.根据同事数据分析结果即可回答的问题，不需要进一步数据分析。
    - 4.行动建议和原因分析问题：如“如何提高销量下降产品的销售额？”、“xxx的原因是什么？”，因为这些问题与数据库数据关系不大。

### 其他要求
- 推荐问题请尽可能简洁，如果过长可能无法展示给用户，但同时需要保证信息完整。
- **请猜测用户可能关心的问题，从而提高用户点击推荐问题的概率**。
- 推荐问题中不要包含币种，如美元、人民币等。

## workflow
1.仔细阅读数据分析结果，找出其中较具体的信息，如客户名、产品名、国家名、渠道名，并根据表信息推荐出与这些相关的个性化问题，请注意，推荐的问题与分析的报表不需要一致。
2.对生成的问题逐个分析：
  - 若该问题需要用到超出数据库范围的数据才能回答，则认为该问题是无效的。
  - 若该问题不需要使用数据库中数据，则认为该问题是无效的。
  - 若该问题不需要进一步分析也能解答，则认为是无效的。
  - 其余问题认为是有效的。

## 分析报表名
```{{table_name}}```

## 数据分析结果
```{{analysis_result}}```

上面数据分析都是基于近三个月或近六个月的数据进行的，请重点关注conclusion.

## examples
请你从下面几个角度推荐问题：
- 推荐不同时间尺度的问题，如本月、今年
- 推荐对比类问题，如各国家过去一年每月成交金额和商机金额对比；近两个月占比最大的输单原因分别是什么；2024年各来源渠道复购率
- 推荐不同数据口径问题：过去一年，某产品每月成交额；过去一年，来自xxx渠道的客户每月成交额；2024年每个季度新客户数量
- 推荐深入分析问题：这个月新客户成交转化率是多少 ；计算2024年各产品复购率；9月新客户平均成交额
- 推荐top相关问题：今年总成交额最高的客户；本月购买次数最多的产品；商机金额最大的客户
- 推荐业务员相关问题：2024年赢单金额最高的业务员；11月输单次数最多的业务员；每个业务员的新客户数量

## output
返回结果使用下面json格式：
```json
[
  {"question": "问题1", "is_Effective": 0/1},
  { "question": "问题2", "is_Effective": 0/1},
  { "question": "问题3", "is_Effective": 0/1},
  { "question": "问题4", "is_Effective": 0/1},
  { "question": "问题5", "is_Effective": 0/1}
]
```
no explain!
开始
PROMPT;
    }


}
