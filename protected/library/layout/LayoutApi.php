<?php


namespace common\library\layout;

use common\library\layout\init\Factory;
use common\library\layout\layout_rule\calculate\RuleFlow;
use common\library\layout\layout_rule\LayoutRule;
use common\library\layout\layout_rule\calculate\RuleFlowFactory;
use common\library\object\field\field_setting\FieldSettingFactory;
use common\library\object\field\FieldConstant;
use common\library\object\object_define\Constant as ObjConstant;
use xiaoman\orm\database\data\Like;
use xiaoman\orm\database\data\NotEqual;

/**
 * @property RuleFlow $ruleFlow
 */
class LayoutApi
{
    protected $clientId;
    protected $objectName;
    protected $pageType;
    protected $device;
    protected $fieldList;
    protected $businessType;
    protected $businessScene;       // 业务场景，具体指新建、编辑、详情、抽屉、下推等场景
    protected $ruleFlow;

    public function __construct($clientId, $object_name = '', $page_type = '', $device = \Constants::CLIENT_TYPE_WEB, $businessType = ObjConstant::BUSINESS_TYPE_COMMON)
    {
        $this->clientId = $clientId;
        $this->objectName = $object_name;
        $this->pageType = $page_type;
        $this->device = $device;
        $this->businessType = $businessType;
    }

    public function getCommonLayout(){
        $layout = new \common\library\layout\Layout($this->clientId);
        $layout->load([
            'object_name' => $this->objectName,
            'page_type' => $this->pageType,
            'device' => $this->device,
            'default_layout' => true,
            'business_type' => ObjConstant::BUSINESS_TYPE_COMMON,
            'enable_flag' => true
        ]);
        return $layout;
    }

    public function setBusinessScene($scene){
        $this->businessScene = $scene;
    }

    public function getLayout($defaultLayout = \common\library\layout\LayoutConstants::LAYOUT_DEFAULT_TRUE): Layout
    {
        $layout = new \common\library\layout\Layout($this->clientId);
        $layout->load([
            'object_name' => $this->objectName,
            'page_type' => $this->pageType,
            'device' => $this->device,
            'default_layout' => $defaultLayout,
            'business_type' => $this->businessType,
            'enable_flag' => true
        ]);

        // 布局在initClient初始化，不会再在getLayout()生成
//        if ($defaultLayout && $layout->isNew()) {
//            $layoutDecorateClass = \common\library\layout\init\Factory::make($this->clientId, $this->objectName);
//            $layoutDecorateClass->setInitPageType($this->pageType);
//            $layoutDecorateClass->init();
//            $layout = new Layout($this->clientId);
//            $layout->load([
//                'object_name' => $this->objectName,
//                'page_type' => $this->pageType,
//                'device' => $this->device,
//                'default_layout' => \common\library\layout\LayoutConstants::LAYOUT_DEFAULT_TRUE
//            ]);
//        }

        return $layout;
    }

    public function buildLayoutFilter($params)
    {
        $layoutFilter = new LayoutFilter($this->clientId);
        !empty($params['layout_id']) && $layoutFilter->layout_id = $params['layout_id'];
        !empty($params['page_type']) && $layoutFilter->page_type = $params['page_type'];
        !empty($params['device']) && $layoutFilter->device = $params['device'];
        !empty($params['object_name']) && $layoutFilter->object_name = $params['object_name'];
        isset($params['business_type']) && $layoutFilter->business_type = $params['business_type'];
        isset($params['default_layout']) && $layoutFilter->default_layout = $params['default_layout'];
        isset($params['disable_flag']) && $layoutFilter->disable_flag = $params['disable_flag'];

        if(isset($params['name']) && trim($params['name']) != ''){
            $layoutFilter->name = new Like($params['name']);
        }
        $layoutFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;

        if(!empty($params['page_no']) && !empty($params['page_size'])){
            $layoutFilter->limit($params['page_size'], $params['page_no']);
        }
        return $layoutFilter;
    }

    // 获取单个Layout接口信息
    public function getWebLayoutInfo($params)
    {
        $fieldMap = $this->
        buildLayoutFieldMap($params['object_name'], $params['page_type'], $params['business_type']??'', $params['privilege_scene']);
        $layoutFilter = $this->buildLayoutFilter($params);
        $batch = $layoutFilter->find();
        $batch->getFormatter()->setFieldMap($fieldMap);
        $batch->getFormatter()->infoSetting();
        $batch->getFormatter()->displayHideGroup(true);
        $targetLayouts = $batch->getAttributes();
        return isset($targetLayouts[0]) ? $targetLayouts[0] : [];
    }

    // 获取布局配置页的Layout接口信息
    public function getSettingLayoutInfo($layout_id)
    {
        $layout = new \common\library\layout\Layout($this->clientId, $layout_id);
        if($layout->isNew()){
            throw new \RuntimeException(\Yii::t('layout', 'Layout does not exist'));
        }

        $fieldMap = $this->buildLayoutFieldMap($layout->object_name, $layout->page_type);

        $layout->getFormatter()->setFieldMap($fieldMap);
        $layout->getFormatter()->infoSetting();
        return $layout->getAttributes();
    }

    public function getLayoutList($params)
    {
        $layoutFilter = $this->buildLayoutFilter($params);
        $layoutFilter->business_type = ObjConstant::BUSINESS_TYPE_COMPANY_COMMON;
        $batch = $layoutFilter->find();
        $batch->getFormatter()->webListSetting();

        $count = $layoutFilter->count();
        return [
            'list' => $batch->getAttributes(),
            'count' => $count
        ];
    }

    public function getLayoutFields()
    {
        $layout = $this->getLayout();
        return $layout->layout_fields;
    }

    public function formatFormData($objName, $data)
    {
        $fieldFilter = (new \common\library\object\field\Api())->buildFilter($this->clientId, [
            'object_name' => $objName,
//            'enable_flag'=>\Constants::ENABLE_FLAG_TRUE
        ]);
        $fieldFilter->select(['field', 'group_id', 'field_type']);
        $fieldMap = array_column($fieldFilter->rawData(), null, 'field');

        $data['main_field'] = $data['main_field'];
        $rules = [];

        foreach ($data['rules'] ?? [] as $ruleKey => $rule) {
            $filters = [];
            foreach($rule['filters'] ?? [] as $key => $filter){
                $field = $filter['field'];
                $formatFilter['filter_no'] = $key + 1;
                $formatFilter['field'] = $filter['field'];
                $formatFilter['field_type'] = $fieldMap[$field]['field_type'];
                $formatFilter['main_field'] = $field == $data['main_field'];
                $formatFilter['operator'] = $filter['operator'];
                $formatFilter['value'] = $filter['value'] ?? null;
                $filters[$key] = $formatFilter;
            }

            $fieldControls = [];
            foreach($rule['action'] ?? [] as $idx => $action){
                $fieldControls[$action['field_control']] = [];
                $fieldControls[$action['field_control']]['index'] = $idx;
                $fieldControls[$action['field_control']]['fields'] = $action['fields'];

                if(isset($action['page_type'])){
                    $fieldControls[$action['field_control']]['page_type'] = $action['page_type'];
                }
            }

            $rules[$ruleKey] = [
                'rule_no' => $ruleKey+1,
                'filters' => $filters,
                'action' => [['config'=>['field_control' => $fieldControls], 'type' => 'layout']]
            ];
        }

        $data['layout_rule_config'] = ['rules' => $rules];
        unset($data['rules']);

        return $data;
    }

    public function setLayoutFieldRule($data): LayoutRule
    {
        $object_name = $data['object_name'];

        // 多布局启用之前，对所有的布局编辑相同的布局规则
        $layoutFilter = new LayoutFilter($this->clientId);
        $layoutFilter->object_name = $object_name;
        $layoutFilter->page_type = [LayoutConstants::LAYOUT_PAGE_TYPE_DETAIL,LayoutConstants::LAYOUT_PAGE_TYPE_DRAWER,LayoutConstants::LAYOUT_PAGE_TYPE_EDIT];
        $layoutFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $layoutFilter->select(['layout_id', 'object_name', 'page_type','device','default_layout','layout_fields','business_type']);
        $allLayouts = $layoutFilter->rawData();
        $targetLayoutRule = null;

        $data = $this->formatFormData($object_name, $data);

        /** @var Layout $layout */
        foreach ($allLayouts as $layout) {
            $layoutRule = new LayoutRule($this->clientId);
            $layoutRule->load(['layout_id' => $layout['layout_id'], 'enable_flag' => \Constants::ENABLE_FLAG_TRUE,'action_type'=> LayoutConstants::LAYOUT_RULE_ACTION_TYPE_FIELD]);
            $layoutRule->layout_rule_config = $data['layout_rule_config'];
            $layoutRule->main_field = $data['main_field'];
            $layoutRule->name = $data['name'];
            $layoutRule->priority = $data['priority'] ?? 0;
            $layoutRule->action_type = LayoutConstants::LAYOUT_RULE_ACTION_TYPE_FIELD;
            if ($layoutRule->isNew()) {
                $layoutRule->layout_id = $layout['layout_id'];
                $layoutRule->object_name = $layout['object_name'];
                $layoutRule->disable_flag = \Constants::DELETE_FLAG_FALSE;
                $layoutRule->create();
            } else {
                $layoutRule->update();
            }

            if ($layout['page_type'] == LayoutConstants::LAYOUT_PAGE_TYPE_DETAIL) {
                $targetLayoutRule = $layoutRule;
            }
        }

        return $targetLayoutRule;
    }

    public function createLayout($data)
    {
        $layout = new Layout($this->clientId);
        $layout->name = $data['name'] ?? '';
        $layout->description = $data['description'] ?? '';
        $layout->object_name = $data['object_name'] ?? $this->objectName;
        $layout->page_type = $data['page_type'] ?? $this->pageType;
        $layout->device = $data['device'] ?? $this->device;
        $layout->default_layout = \Constants::ENABLE_FLAG_FALSE;        // 能创建出来的都是非默认布局
        $layout->disable_flag = \Constants::ENABLE_FLAG_FALSE;

        $layoutInit = Factory::make($this->clientId, $data['object_name']);
        $layout->schema = $layoutInit->getSchema($layout->page_type, $layout->device);
        $layout->widget_settings = $layoutInit->getWidgetSettings($layout->page_type, $layout->device);
        $layout->field_group_settings = $layoutInit->getFieldGroupSettings($layout->page_type, $layout->device);

        $syncDetailToEditPage = true;       // 之后该变量会由前端传入

        // 对于详情布局，需要生成相应的抽屉页布局
        if($layout->page_type == LayoutConstants::LAYOUT_PAGE_TYPE_DETAIL){
            $transaction = \PgActiveRecord::getDbByClientId($this->clientId)->beginTransaction();
            try {
                $data['page_type'] = LayoutConstants::LAYOUT_PAGE_TYPE_DRAWER;
                $drawerLayout = $this->createLayout($data);
                $layoutConfig = [
                    'independent_drawer_flag' => $layoutInit->defaultIndependentDrawer(),              // 新建的详情页布局默认不将对应的抽屉页独立出去
                    'drawer_layout_id' => $drawerLayout->layout_id
                ];

                // 同步编辑布局
                if($syncDetailToEditPage){
                    $data['page_type'] = LayoutConstants::LAYOUT_PAGE_TYPE_EDIT;
                    $editLayout = $this->getEditLayoutFromDetail($layout->getRawData());
                    $editLayout->create();
                    $layoutConfig ['edit_layout_id'] = $editLayout->layout_id;
                }

                // 创建详情布局
                $layout->layout_config = $layoutConfig;
                $layout->create();
                $transaction->commit();
            }catch (\Exception $exception) {
                $transaction->rollback();
                throw $exception;
            }catch (\Throwable $throwable) {
                throw $throwable;
            }
        }else{
            $layout->create();
        }

        return $layout;
    }

    public function updateLayout($layoutId, $data)
    {
        $layout = new Layout($this->clientId, $layoutId);

        // 布局的业务对象、页面类型、设备端、默认布局flag不可变更
        isset($data['disable_flag']) && $layout->disable_flag = $data['disable_flag'];
        isset($data['description']) && $layout->description = $data['description'];
        isset($data['name']) && $layout->name = $data['name'];

        if(!empty($data['schema'])){
            if(!is_array($data['schema'])){
                $data['schema'] = json_decode($data['schema'], true);
            }
            is_array($data['schema']) && $layout->schema = $data['schema'];
        }

        if(!empty($data['field_group_settings'])){
            if(!is_array($data['field_group_settings'])){
                $data['field_group_settings'] = json_decode($data['field_group_settings'], true);
            }
            is_array($data['field_group_settings']) && $layout->field_group_settings = $data['field_group_settings'];
        }

        if(!empty($data['widget_settings'])){
            if(!is_array($data['widget_settings'])){
                $data['widget_settings'] = json_decode($data['widget_settings'], true);
            }
            is_array($data['widget_settings']) && $layout->widget_settings = $data['widget_settings'];
        }

//        if(!empty($data['widget_field_settings'])){
//            if(!is_array($data['widget_field_settings'])){
//                $data['widget_field_settings'] = json_decode($data['widget_field_settings'], true);
//            }
//            !empty($data['widget_field_settings']) && $layout->widget_field_settings = $data['widget_field_settings'];
//        }

        $syncDetailToEditPage = true;       // 之后该变量会由前端传入

        // 如果是详情布局且抽屉布局与详情布局独立，则变更抽屉布局的settings
        if($layout->page_type == LayoutConstants::LAYOUT_PAGE_TYPE_DETAIL){
            $layoutConfig = $layout->layout_config;
            $drawerLayoutId = $layoutConfig['drawer_layout_id'] ?? 0;
            if(!empty($drawerLayoutId)){
                if(!is_array($data['independent_config'])){
                    $data['independent_config'] = json_decode($data['independent_config'], true);
                }

                $indepentDrawerFlag = !empty($data['independent_config']);
                $layoutConfig['independent_drawer_flag'] = $indepentDrawerFlag;
                $layout->layout_config = $layoutConfig;

                $drawerLayoutData = $data;      // 不独立抽屉页的时候，抽屉页的样式和详情页一样
                if($indepentDrawerFlag){    // 独立抽屉页时
                    $drawerLayoutData = $data['independent_config'] + $data;
                }
                $drawerLayoutData['page_type'] = LayoutConstants::LAYOUT_PAGE_TYPE_DRAWER;
                unset($data['independent_config']);
                $this->updateLayout($drawerLayoutId, $drawerLayoutData);
            }

            // 同步编辑布局
            if($syncDetailToEditPage && !empty($layoutConfig['edit_layout_id'])){
                $editLayoutId = $layoutConfig['edit_layout_id'];
                $editLayout = $this->getEditLayoutFromDetail($layout->getRawData(), $editLayoutId);
                $editLayout->update();
            }
        }

        $layout->update();
    }

    public function getEditLayoutFromDetail($detailLayoutData, $editLayoutId=0){
        // 查询编辑布局
        if(!empty($editLayoutId)){
            $editLayout = new Layout($this->clientId, $editLayoutId);
        }else{
            $editLayout = new Layout($this->clientId);
            $editLayout->object_name = $detailLayoutData['object_name'];
            $editLayout->page_type = LayoutConstants::LAYOUT_PAGE_TYPE_EDIT;
            $editLayout->device = $detailLayoutData['device'];
        }
        $editLayout->name = $detailLayoutData['name'] ?? '';
        $editLayout->description = $detailLayoutData['description'] ?? '';
        $editLayout->disable_flag = $detailLayoutData['disable_flag'] ?? \Constants::ENABLE_FLAG_FALSE;

        $layoutInit = Factory::make($this->clientId, $detailLayoutData['object_name']);
        $editLayout->schema = $layoutInit->getSchema($editLayout->page_type, $editLayout->device);
        $editLayout->widget_settings = $layoutInit->getWidgetSettings($editLayout->page_type, $editLayout->device);
        $editLayout->field_group_settings = $layoutInit->getFieldGroupSettings($editLayout->page_type, $editLayout->device);

        // 收集编辑布局的字段
        $editLayoutBuilder = new LayoutSyncBuilder($this->clientId, $editLayout->getRawData());
        $detailLayoutBuilder = new LayoutSyncBuilder($this->clientId, $detailLayoutData);

        $editLayoutData = $editLayoutBuilder->rebuildLayoutFrom($detailLayoutBuilder);
        $editLayout->schema = $editLayoutData['schema'];
        $editLayout->field_group_settings = $editLayoutData['field_group_settings'];
        $editLayout->widget_settings = $editLayoutData['widget_settings'];
        return $editLayout;
    }

    // 布局规则默认的动态字段控制（即没有指定任何布局规则时的字段显隐必填只读行为）
//    public function defaultRuleControlFields(Layout $layout, $fieldMap = null,$fieldPrivilege = null, $userId=0){
//        $layoutFields = $layout->layout_fields;
//        $targetObjects = \common\library\object\object_relation\Helper::getMainSubObjectNames($layout->object_name);
//
//        if(empty($fieldMap)){
//            $fieldFilter = (new \common\library\object\field\Api())->list($this->clientId, ['object_name'=>$targetObjects,'enable_flag'=>\Constants::ENABLE_FLAG_TRUE]);
//
//            foreach($fieldFilter->rawData() as $field){
//                if(!isset($fieldMap[$field['object_name']])){
//                    $fieldMap[$field['object_name']] = [];
//                }
//                $fieldMap[$field['object_name']][$field['field']] = $field;
//            }
//        }
//
//        if(is_null($fieldPrivilege)){
//            $functionId = \common\library\object\object_define\Constant::FUNCTIONAL_MAP[$layout->object_name];
//            if(!\User::getLoginUser()){
//                \User::setLoginUserById($userId);
//            }
//            $fieldPrivilege = $this->getPrivilegeFields(\User::getLoginUser(), $fieldMap,$functionId);     // 这里需要根据page_type来决定第三参 $scene
//        }
//
//        $privilegeDisableFields = array_flip($fieldPrivilege['disable'] ?? []);
//        $privilegeReadOnlyFields = array_flip($fieldPrivilege['readonly'] ?? []);
//
//        $result = [];
//
//        foreach($layoutFields as $objectName => $fields){
//            foreach($fields as $layoutField){
//                if (!isset($fieldMap[$objectName][$layoutField])) {
//                    continue;
//                }
//
//                // 如果是 新建/编辑 场景，需要将创建人、创建时间、修改人和修改时间这种tbl_field中的只读字段（tbl_field.is_writable=0）置为隐藏
//                if($layout->page_type == LayoutConstants::LAYOUT_PAGE_TYPE_EDIT && $fieldMap[$objectName][$layoutField]['is_writable'] == 0){
//                    continue;
//                }
//
//                $result[$objectName][$layoutField]['show_flag'] = !isset($privilegeDisableFields[$layoutField]);     // 如果角色权限隐藏了该字段，则不可见
//                $result[$objectName][$layoutField]['read_only'] = empty($fieldMap[$objectName][$layoutField]['is_writable']) || $result[$objectName][$layoutField]['show_flag'] && isset($privilegeReadOnlyFields[$layoutField]);     // 如果字段可见 且 字段权限只读，则布局只读
//                $result[$objectName][$layoutField]['is_writable'] = !$result[$objectName][$layoutField]['read_only'];
//                $result[$objectName][$layoutField]['required'] = $result[$objectName][$layoutField]['show_flag'] && !empty($fieldMap[$objectName][$layoutField]['required']) && $result[$objectName][$layoutField]['is_writable'];  // 如果字段可见 且 可写 且 在字段配置中必填，则布局中必填
//            }
//        }
//
//        return $result;
//    }

    public function defaultRuleControlFields(Layout $layout, $fieldMap = null,$fieldPrivilege = null, $userId=0){
        $layoutFields = $layout->layout_fields;
        $targetObjects = \common\library\object\object_relation\Helper::getMainSubObjectNames($layout->object_name);

        if (empty($fieldMap)) {
            $fieldFilter = (new \common\library\object\field\Api())->list($this->clientId, ['object_name' => $targetObjects, 'enable_flag' => \Constants::ENABLE_FLAG_TRUE]);

            foreach ($fieldFilter->rawData() as $field) {
                if (!isset($fieldMap[$field['object_name']])) {
                    $fieldMap[$field['object_name']] = [];
                }
                $fieldMap[$field['object_name']][$field['field']] = $field;
            }
        }

        if (is_null($fieldPrivilege)) {
            $functionId = \common\library\object\object_define\Constant::FUNCTIONAL_MAP[$layout->object_name];
            if (!\User::getLoginUser()) {
                \User::setLoginUserById($userId);
            }
            $fieldPrivilege = $this->getPrivilegeFields(\User::getLoginUser(), $fieldMap, $functionId);     // 这里需要根据page_type来决定第三参 $scene
        }

        $privilegeDisableFields = array_flip($fieldPrivilege['disable'] ?? []);
        $privilegeReadOnlyFields = array_flip($fieldPrivilege['readonly'] ?? []);

        $result = [];

        foreach ($layoutFields as $objectName => $fields) {
            foreach ($fields as $layoutField) {
                if (!isset($fieldMap[$objectName][$layoutField])) {
                    continue;
                }

                // 如果是 新建/编辑 场景，需要将创建人、创建时间、修改人和修改时间这种tbl_field中的只读字段（tbl_field.is_writable=0）置为隐藏
                if ($layout->page_type == LayoutConstants::LAYOUT_PAGE_TYPE_EDIT && $fieldMap[$objectName][$layoutField]['is_writable'] == 0) {
                    continue;
                }

                if ($objectName == $this->objectName) {
                    $result[$objectName][$layoutField]['show_flag'] = !isset($privilegeDisableFields[$layoutField]);     // 如果角色权限隐藏了该字段，则不可见
                    $result[$objectName][$layoutField]['read_only'] = empty($fieldMap[$objectName][$layoutField]['is_writable']) || $result[$objectName][$layoutField]['show_flag'] && isset($privilegeReadOnlyFields[$layoutField]);     // 如果字段可见 且 字段权限只读，则布局只读
                } else {
                    $result[$objectName][$layoutField]['show_flag'] = !empty($fieldMap[$objectName][$layoutField]['show_flag']);
                    $result[$objectName][$layoutField]['read_only'] = empty($fieldMap[$objectName][$layoutField]['is_writable']);     // 如果字段可见 且 字段权限只读，则布局只读
                }

                $result[$objectName][$layoutField]['is_writable'] = !$result[$objectName][$layoutField]['read_only'];
                $result[$objectName][$layoutField]['required'] = $result[$objectName][$layoutField]['show_flag'] && !empty($fieldMap[$objectName][$layoutField]['required']) && $result[$objectName][$layoutField]['is_writable'];  // 如果字段可见 且 可写 且 在字段配置中必填，则布局中必填
            }
        }

        return $result;
    }

    // 布局规则默认可选字段
    public function enableRuleFields($layoutId, $fieldMap = [])
    {
        $layout = new Layout($this->clientId, $layoutId);
        return $this->enableRuleFieldsByLayout($layout, $fieldMap);
    }

    // 第三参表示子对象的字段是否参与布局规则
    public function enableRuleFieldsByLayout(Layout $layout, $fieldMap = [])
    {
        $targetObjects = [$layout->object_name];
        $targetFieldList = [];      // 返回结果
        if (!empty(LayoutConstants::ALLOW_SUBOBJ_RULE_FIELD_CONF[$layout->object_name])) {
            $targetObjects = \common\library\object\object_relation\Helper::getMainSubObjectNames($layout->object_name);
        }

        if (empty($fieldMap)) {
            $fieldList = (new \common\library\object\field\Api())->list($this->clientId, ['object_name' => [$targetObjects], 'enable_flag' => \Constants::ENABLE_FLAG_TRUE]);
            $fieldMap = [];
            foreach ($fieldList as $field) {
                if (!isset($fieldMap[$field['object_name']])) {
                    $fieldMap[$field['object_name']] = [];
                }
                $fieldMap[$field['object_name']][$field['field']] = $field;
            }
        }

        $filterableFieldsType = array_flip(LayoutConstants::FILTERABLE_FIELD_TYPE);
        $excludeFields = [];
        if ($layout->object_name == ObjConstant::OBJ_COMPANY) {
            $excludeFields = ['user_id', 'next_move_to_public_date', 'public_type', 'public_reason_id'];
        }

        // 过滤掉布局中已经在字段配置里被删除的字段（多布局但单布局规则的情况下，不做 $layout->layout_fields的限制，因为每个布局的layout_fields都不一样，但布局规则只有一个，要取哪个的layout_fields作为基准呢）
        foreach ($fieldMap as $objectName => $fieldInfos) {
            $filterFields = [];         // 字段配置中可作为条件的字段
            $readOnlyFields = [];       // 字段配置中的只读字段
            $mainFields = [];       // 主字段
            $baseAllowFields = [];       // 基准的可作为只读、必填和隐藏的字段
            $mustRequiresFields = [];   // 必须必填的字段，如果某些字段默认必填而且不允许修改字段配置，这种字段不能隐藏和只读
            $allRequireFields = [];     // 必填字段，包括用户设置的必填字段和系统默认不可变更的必填字段
            $isSubObj = $objectName != $layout->object_name;

            foreach ($fieldInfos as $field => $fieldInfo) {
                // 调用层的$fieldMap有包含已删除的字段，需要过滤
                if (!$fieldInfo['enable_flag']) {
                    continue;
                }

                $field = (string)$field;

                // 选择字段才可以作为主字段
                if ($objectName == $layout->object_name && $fieldInfo['field_type'] == FieldConstant::TYPE_SELECT) {
                    $mainFields[] = $field;
                }

                if ($fieldInfo['required']) {
                    $allRequireFields[] = $field;
                    if(!$fieldInfo['allow_setting_required']){
                        $mustRequiresFields[] = $field;
                    }
                }

                if ($objectName == $layout->object_name && isset($filterableFieldsType[$fieldInfo['field_type']]) && empty($fieldInfo['ext_info']['children_field'])) {     // 有子字段的复合字段不能作为条件字段
                    $filterFields[] = $field;
                }

                // 子字段不能作为隐藏、显示和必填字段，需要转化为父字段
                if (!empty($fieldInfo['ext_info']['parent_field'])) {
                    $field = $fieldInfo['ext_info']['parent_field'];
                }

                if (isset($fieldInfo['is_writable']) && !$fieldInfo['is_writable']) {
                    $readOnlyFields[] = $field;
                }

                $baseAllowFields[] = $field;
            }

            $conditionFields = array_unique(array_values(array_diff($filterFields, $excludeFields)));
            $mainFields = $mainFields;
            $realHideFields = array_unique(array_values(array_diff($baseAllowFields, $allRequireFields, $excludeFields)));
            $realRequiredFields = array_unique(array_values(array_diff($baseAllowFields, $readOnlyFields, $excludeFields)));
            $realReadOnlyFields = array_unique(array_values(array_diff($baseAllowFields, $mustRequiresFields, $readOnlyFields,$excludeFields)));

            $targetFieldList[$objectName] = [
                'main_field_list' => array_values(array_diff($mainFields, LayoutFieldConstants::getDisableFields($objectName, 'main_field', $isSubObj))),
                "condition_fields_list" => array_values(array_diff($conditionFields, LayoutFieldConstants::getDisableFields($objectName, 'condition_field', $isSubObj))),        // 可作为布局规则的条件字段
                "hide_fields_list" => array_values(array_diff($realHideFields, LayoutFieldConstants::getDisableFields($objectName, 'hide_field', $isSubObj))),        // 可作为隐藏的字段
                "required_fields_list" => array_values(array_diff($realRequiredFields, LayoutFieldConstants::getDisableFields($objectName, 'required_field', $isSubObj))),        // 可作为必填的字段
                "read_only_fields_list" => array_values(array_diff($realReadOnlyFields, LayoutFieldConstants::getDisableFields($objectName, 'read_only_field', $isSubObj)))        // 可作为只读的字段：系统本身只读的字段不能再作为只读字段
            ];
        }


        return $targetFieldList;
    }

    // 初始化控制字段和组件显隐
    public function initControlAction($userId, $objectId, $objectData, $subObjectData = [])
    {    // $subObjectData 是一个二维数组，支持多种子对象
        $layout = $this->getCommonLayout();
        $ruleFlow = RuleFlowFactory::build($layout->object_name, $userId, $layout);
        $ruleFlow->setRequestScene(RuleFlow::REQUEST_SCENE_INIT)
            ->setObjectId($objectId)
            ->setPrivilegeScene($this->businessScene)
            ->setObjectData($objectData);
        $ruleFlow->calculate();

        // 当前页的字段 和 组件显隐情况
        return [
            'layout_rules' => [
                'field_control' => $ruleFlow->getFieldControl(),
                'component_control' => $ruleFlow->getComponentControl(),
                'trigger_fields' => $ruleFlow->getTriggerFields(),
            ],
            'values' => $ruleFlow->getDataControl()
        ];
    }

    public function listControlAction($userId, $listData)
    {
        $layout = new \common\library\layout\Layout($this->clientId);
        $layout->loadBy($this->objectName, $this->pageType, $this->device, \common\library\layout\LayoutConstants::LAYOUT_DEFAULT_TRUE, ObjConstant::BUSINESS_TYPE_COMMON, false);

        if($layout->isNew()){
            $layoutDecorateClass = \common\library\layout\init\Factory::make($this->clientId, $this->objectName);
            $layoutDecorateClass->setInitPageType($this->pageType);
            $layoutDecorateClass->init();
            $layout = new Layout($this->clientId);
            $layout->loadBy($this->objectName, $this->pageType, $this->device, \common\library\layout\LayoutConstants::LAYOUT_DEFAULT_TRUE, ObjConstant::BUSINESS_TYPE_COMMON);
        }

        $objectIdKey = ObjConstant::OBJ_PRIMARY_KEY_MAP[$layout->object_name];

        $ruleFlow = RuleFlowFactory::build($layout->object_name, $userId, $layout);
        $ruleFlow->setRequestScene(RuleFlow::REQUEST_SCENE_INIT)
            ->setPrivilegeScene($this->businessScene);

        foreach ($listData as &$listDatum) {
            $ruleFlow->setObjectData($listDatum);
            $ruleFlow->setObjectId($listDatum[$objectIdKey]);
            $ruleFlow->calculate();
            $listDatum['layout_rules'] = [
                'field_control' => $ruleFlow->getFieldControl(),
            ];
            unset($listDatum['field_privilege_stats']);
            $ruleFlow->clearCalculateResult();
        }
        return $listData;
    }

    // 触发控制字段和组件显隐
    public function triggerControlAction($userId, $objectId = 0, $updateData = [], $subObjectData = [])
    {
        $layout = $this->getCommonLayout();
        $ruleFlow = RuleFlowFactory::build($layout->object_name, $userId, $layout);
        $ruleFlow->setRequestScene(RuleFlow::REQUEST_SCENE_DYNAMIC)
            ->setTriggerScene($layout->page_type == LayoutConstants::LAYOUT_PAGE_TYPE_EDIT ? RuleFlow::TRIGGER_SCENE_EDIT_PAGE : RuleFlow::TRIGGER_SCENE_INLINE)
            ->setObjectId($objectId)
            ->setObjectData($updateData);
        $ruleFlow->calculate();

        // 当前页的字段 和 组件显隐情况
        return [
            'layout_rules' => [
                'field_control' => $ruleFlow->getFieldControl(),
                'component_control' => $ruleFlow->getComponentControl(),
                'trigger_fields' => $ruleFlow->getTriggerFields(),
            ],
            'values' => $ruleFlow->getDataControl()
        ];
    }

    // 根据用户id获取角色字段权限
//    public function getPrivilegeFields(\User $user, $fieldMap, $functionId=''){
//        if(empty($functionId)){
//            $functionId = \common\library\object\object_define\Constant::FUNCTIONAL_MAP[$this->objectName];
//        }
//
//        $fieldStats = \common\library\privilege_v3\Helper::getPrivilegeFieldStats($this->clientId, $user->getUserId(), $functionId);
//
//        $result = [];
//        foreach($fieldStats as $stat){
//            $objName = ObjConstant::OBJ_MAP[$stat['refer_type']];
//            if($objName == $this->objectName){
//                $result['disable'] = $stat['disable'];
//                $result['readonly'] = $stat['readonly'];
//            }
//        }
//
//        // 做子字段的兼容
//        foreach($result as $scene => $fields){
//            foreach($fields as $field){
//                if(isset($fieldMap[$this->objectName][$field]) && !empty($fieldMap[$this->objectName][$field]['ext_info']['parent_field'])){
//                    $fields[] = $fieldMap[$this->objectName][$field]['ext_info']['parent_field'];
//                }
//            }
//            $result[$scene] = array_unique($fields);
//        }
//
//        return $result;
//    }

    public function getPrivilegeFields(\User $user, $fieldMap, $functionId=''){
        if(empty($functionId)){
            $functionId = \common\library\object\object_define\Constant::FUNCTIONAL_MAP[$this->objectName];
        }

        $fieldStats = \common\library\privilege_v3\Helper::getPrivilegeFieldStats($this->clientId, $user->getUserId(), $functionId);

        $result = [];
        foreach ($fieldStats as $stat) {
            $objName = ObjConstant::OBJ_MAP[$stat['refer_type']];
            if ($objName == $this->objectName) {
                $result['disable'] = $stat['disable'];
                $result['readonly'] = $stat['readonly'];
            }
        }

        $fieldSettingClass = FieldSettingFactory::OBJECT_FIELD_SETTING_MAP[$this->objectName];
        $parentFieldMap = call_user_func_array([$fieldSettingClass, 'parentFieldMap'], []);

        // 做子字段的兼容
        foreach ($result as $scene => $fields) {
            foreach ($fields as $field) {
                if (isset($fieldMap[$this->objectName][$field]) && !empty($fieldMap[$this->objectName][$field]['ext_info']['parent_field'])) {
                    $fields[] = $fieldMap[$this->objectName][$field]['ext_info']['parent_field'];
                }

                if(isset($parentFieldMap[$field])){
                    $fields[] = $parentFieldMap[$field];
                }
            }
            $result[$scene] = array_unique($fields);
        }

        return $result;
    }

    public function getLayoutFieldList(){
        return $this->fieldList;
    }

    // 获取布局配置中依赖的fieldList字段
    public function buildLayoutFieldList($objectName, $pageType, $businessType = '',$privilegeScene = null)
    {
        if (is_null($this->fieldList)) {
            $privilegeParams = [];
            if (!empty($businessType)) {
                $privilegeParams = [        // 和字段权限相关，不能移除
                    'business_type' => $businessType
                ];
            }

            $layoutInitClass = Factory::INIT_LAYOUT_OBJECT_MAP[$objectName];

            //获取父子对象
            $objNames = \common\library\object\object_relation\Helper::getMainSubObjectNames($objectName);
            if(!empty($layoutInitClass::relateObjectNames())){
                $objNames = array_unique(array_merge($objNames, $layoutInitClass::relateObjectNames()));
            }
            $fieldApi = new \common\library\object\field\Api();
            if(empty($privilegeScene)){
                $privilegeScene = $pageType;
            }
            $this->fieldList = $fieldApi->fieldList($this->clientId, $objectName, $privilegeScene, ['object_name' => $objNames], $privilegeParams);
        }
        return $this->fieldList;
    }

    public function buildLayoutFieldMap($objectName, $pageType, $businessType = '', $privilegeScene=null)
    {
        $this->buildLayoutFieldList($objectName, $pageType, $businessType,$privilegeScene);
        $map = [];
        foreach ($this->fieldList as $object => $list) {
            $map[$object] = array_reduce($list, function ($carry, $item) {
                $carry[$item['field']] = $item;
                return $carry;
            });
        }
        return $map;
    }
}