<?php

/**
 * This file is part of xiaoman-crm.
 *
 * Copyright © 2012 - 2020 Xiaoman. All Rights Reserved.
 *
 * Created by <PERSON><PERSON><PERSON><PERSON> <she<PERSON><PERSON><PERSON>@xiaoman.cn>, on 2020/04/13.
 */

namespace common\library\product\import;

use common\components\BaseObject;


/**
 * This is the model class for table "tbl_product_import".
 *
 * The followings are the available columns in table 'tbl_product_import':
 * @property string $task_id
 * @property string $user_id
 * @property string $client_id
 * @property string $file_id
 * @property string $import_time
 * @property integer $status
 * @property string $result_file_id
 * @property string $finish_time
 * @property string $total_count
 * @property string $fail_count
 * @property string $fail_reason
 * @property string $result_line
 * @property integer $enable_flag
 * @property string $create_time
 * @property string $update_time
 * @property string $category_ids
 * @property integer $group_id
 * @property integer $update_same
 * @property string $field_mapping
 * @property integer $system
 *
 * @deprecated
 */
class ProductImportTask extends BaseObject
{
    const CRM_SYSTEM = 1;
    const CMS_SYSTEM = 2;

    protected $clientId;

    protected $userId;

    public function __construct(int $clientId, int $userId)
    {
        $this->clientId = $clientId;
        $this->userId = $userId;
    }

    public function getModelClass()
    {
        return \ProductImport::class;
    }

    public function loadByTaskId(int $taskId)
    {
        $task = \ProductImport::model()->find('task_id=:task_id', [':task_id' => $taskId]);

        $this->setModel($task);
    }

    /**
     * @return array
     */
    public function getFieldMapping(): array
    {
        return json_decode($this->field_mapping, true) ?: [];
    }

    /**
     * @param $system
     */
    public function setSystem($system) {
        if(!in_array($system, [self::CRM_SYSTEM, self::CMS_SYSTEM])) {
            throw new \RuntimeException(\Yii::t('cms','System params error'));
        }
        $this->system = $system;
    }

    /**
     * @param int $fileId
     * @param int $isUpdateSame
     * @param array $fieldMapping
     */
    public function init(int $fileId, int $isUpdateSame, array $fieldMapping)
    {
        if (!$this->isNew()) {
            throw new \RuntimeException(\Yii::t('product', 'The task has been initialized'));
        }

        $file = \UploadFile::findByFileId($fileId);
        if (!$file) {
            throw new \RuntimeException(\Yii::t('file', 'Uploaded file ID error'));
        }

        $fileName = $file->getAttribute('file_name');
        $fileExt = pathinfo($fileName,PATHINFO_EXTENSION);
        if (!in_array($fileExt, ['xls', 'xlsx', 'csv'])) {
            throw new \RuntimeException(\Yii::t('file', 'Import failed, file format error'));
        }

        $fileSize = $file->getAttribute('file_size');
        if ($fileSize == 0) {
            throw new \RuntimeException(\Yii::t('file', 'Import failed, file is empty'));
        } elseif ($fileSize > 5242880) {
            throw new \RuntimeException(\Yii::t('file', 'Import failed, file size must not be greater than') . '5M');
        }

        $this->user_id = $this->userId;
        $this->client_id = $this->clientId;
        $this->file_id = $fileId;
        $this->category_ids = '[]';
        $this->field_mapping = json_encode($fieldMapping);
        $this->update_same = $isUpdateSame ? 1 : 0;
        $this->group_id = 0;
        $this->create_time = date('Y-m-d H:i:s');
        $this->fail_reason = '';
        $this->result_line = '';
        $this->status = \ProductImport::STATUS_INIT;
        $this->save();
    }

    /**
     * @param string $error
     *
     * @throws \RuntimeException
     */
    public function failed($error)
    {
        if ($this->isNew()) {
            throw new \RuntimeException(\Yii::t('common', 'The task does not exist and cannot start'));
        }
        if ($this->status != \ProductImport::STATUS_IMPORT_BEGIN) {
            throw new \RuntimeException(\Yii::t('task', 'The task has been run (status: {status})', ['{status}' => $this->status]));
        }

        $this->status = \ProductImport::STATUS_IMPORT_FAIL;
        $this->fail_reason = $error;
        $this->update(['status', 'fail_reason']);
    }

    /**
     * @param int $fileId
     * @param int $totalCount
     * @param int $failCount
     * @param array $failMessages
     *
     * @throws \RuntimeException
     */
    public function finish(int $fileId, int $totalCount, int $failCount, array $failMessages)
    {
        if ($this->isNew()) {
            throw new \RuntimeException(\Yii::t('common', 'The task does not exist and cannot start'));
        }
        if ($this->status != \ProductImport::STATUS_IMPORT_BEGIN) {
            throw new \RuntimeException(\Yii::t('task', 'The task has been run (status: {status})', ['{status}' => $this->status]));
        }

        $this->result_file_id = $fileId;
        $this->total_count = $totalCount;
        $this->fail_count = $failCount;
        $this->result_line = json_encode($failMessages);
        $this->finish_time = date('Y-m-d H:i:s');
        $this->status = \ProductImport::STATUS_IMPORT_FINISH;
        $this->update([
            'status', 'finish_time', 'result_file_id',
            'result_line', 'total_count', 'fail_count'
        ]);
    }

}
