<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 17/8/30
 * Time: 下午11:16
 */

namespace common\library\department;


use common\library\account\UserList;
use common\library\department\DepartmentFormatter;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\workflow\WorkflowConstant;

class Helper
{
    protected static $manageableUserList = [];

    public static function getPermissionTree(
        $clientId, $userId,
        array $permission = [], $show_member = 0, $filter_freeze_user = 0, $filter_delete_user = 0, $scope = 0, $includePrivilege = []
    )
    {
        $departmentPermission = new \common\library\department\DepartmentPermission($clientId, $userId);
        if ($filter_freeze_user) {
            $departmentPermission->filterFreezeUser(true);
        }

        $permission = array_filter($permission);
        if( !empty($permission) )
        {
            $departmentPermission->permission($permission);
        }

        if (!empty($includePrivilege))
        {
            $departmentPermission->setIncludePrivilege($includePrivilege);
        }

        if (!empty($scope)) {
            $departmentPermission->privilegeScope($scope);
        }

        if( empty($permission) && $show_member )
        {
            //多公海客户, 客户信息管理员, 特殊处理,能管理所有用户
            if(\common\library\privilege_v3\Helper::hasPermission($clientId,$userId,
                \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_SETTING_CUSTOMER_POOL_MANAGE))
            {
                $departmentPermission->privilegeScope(\common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_SCOPE_DIRECTOR);
            }
        }

        $result = $departmentPermission->tree($show_member);

        if (!empty($permission) && $departmentPermission->getScope() == PrivilegeConstants::PRIVILEGE_SCOPE_DIRECTOR) {
            if (!$filter_freeze_user) {
                $result['node'][] = \common\library\department\Helper::frozenNode($clientId, true, false);
            }

            if (!$filter_delete_user) {
                $result['node'][] = \common\library\department\Helper::deleteNode($clientId, true, false);
            }
        }

        if (!\common\library\privilege_v3\PrivilegeService::getInstance($clientId)->hasFunctional(PrivilegeConstants::FUNCTIONAL_DEPARTMENT)){
            $result = \common\library\department\Helper::tiledTree($result);
        }
        return $result;
    }

    public static function getBatchDepartment($clientId, array $departmentIds)
    {
        $list = [];
        if( empty($departmentIds) )
            return $list;

        $cacheList = (new Department($clientId))->getCacheListByPk($departmentIds);
        $cacheList = array_map(function ($item){
            return json_decode($item, true);
        },array_filter($cacheList));


        foreach ( \Department::model()->populateRecords($cacheList)  as $model)
        {
            $list[$model->id] = (new Department($clientId))->loadByModel($model,false);
        }

        //特殊处理我的企业
        if( in_array(0, $departmentIds ))
        {
            $department = new Department($clientId);
            $department->id = 0;
            $department->name = \Yii::t('common', 'My enterprise');
            $department->prefix = '';
            $list[0] = $department;
            $cacheList[] = $department->getAttributes();
        }

        $notCacheIds = array_diff($departmentIds ,array_column($cacheList, 'id'));
        if( !empty($notCacheIds) && $departmentList = \Department::findByIds($clientId, $notCacheIds) )
        {
            foreach ( $departmentList as $model )
            {
                $list[$model->id] = (new Department($clientId))->loadByModel($model, true);
            }

        }

        return $list;
    }

    public static function getChildrenIds($clientId, $parentId,$prefix=''){
//        $db = \Department::model()->getDbConnection();
        if( $parentId ==0 ){
            $prefix ='0-';
        }elseif( $prefix ){
            $prefix .= $parentId.'-';
        }else{
            $department =  \Department::findById($clientId, $parentId);
            if( !$department )
                return [];
            $prefix = $department->prefix.$parentId.'-';
        }
//        $enable_flag_sql = sprintf('and enable_flag=%d', Department::ENABLE_FLAG_TRUE);
//        $ids =  $db->createCommand("select id from tbl_department where client_id={$clientId} and prefix like '{$prefix}%' {$enable_flag_sql}")->queryColumn();
        $ids = DepartmentCacheableRepo::instance($clientId)->findAllByConditions([
            'client_id' => $clientId,
            'enable_flag' => Department::ENABLE_FLAG_TRUE,
            ['prefix', "{$prefix}%", WorkflowConstant::FILTER_OPERATOR_CONTAINS]
        ], 'id');

        return array_column($ids ?: [], 'id');
//        return $ids??[];
    }

    /**
     * 获取指定部门下第一级子部门id
     * time: 10:37 AM
     * user: huagongzi
     * @param $clientId
     * @param $parentId
     * @param $prefix
     * @return array
     */
    public static function getFirstChildrenIds(int $clientId, int $parentId){
        $ids = DepartmentCacheableRepo::instance($clientId)->findAllByConditions([
            'client_id'   => $clientId,
            'enable_flag' => Department::ENABLE_FLAG_TRUE,
            ['parent_id', "{$parentId}", WorkflowConstant::FILTER_OPERATOR_EQUAL]
        ], 'id');

        return array_column($ids ?: [], 'id');
    }

    //返回在crm中有成员的部门id
    public static function  getClientUserDepartmentIds($clientId){
        $clientUserIds = \common\library\account\Helper::getActivationUserIds($clientId);

        if (empty($clientUserIds)) return [];

        $clientUserDepartmentIds = array();
        $userDepartmentIds = static::getUserDepartmentIds($clientId, $clientUserIds);

//        $userDepartmentIds = $db->createCommand("select DISTINCT(department_id) from tbl_department_member where client_id = {$clientId} and  user_id in (" . implode(',', $clientUserIds) . ")")->queryColumn();
        if (!empty($userDepartmentIds)) {
            $userDepartment = (new DepartmentCacheableRepo($clientId))->findByIds($userDepartmentIds);
//            $userDepartment = $db->createCommand('select id,prefix from tbl_department where client_id=' . $clientId . ' and id in (' . implode(',', $userDepartmentIds) . ')')->queryAll();
            foreach ($userDepartment as $depart) {
                $itemPrefix = $depart['prefix'];
                $clientUserDepartmentIds[] = $depart['id'];
                $clientUserDepartmentIds = array_merge($clientUserDepartmentIds, explode('-', trim($itemPrefix, '-')));
            }

            if (in_array(0, $userDepartmentIds))
            {
                $clientUserDepartmentIds[] = 0;
            }

            $clientUserDepartmentIds = array_unique($clientUserDepartmentIds);
        }

        return $clientUserDepartmentIds;
    }

    public static function getUserDepartmentIds($clientId, $clientUserIds)
    {
        $result = (new DepartmentRedis())->getBatchUserDepartment($clientId, $clientUserIds);

        return array_unique(array_column($result, 'department_id'));
    }

    /**
     * @param $clientId
     * @param $userIds
     * @param $departmentIds
     * @return array
     * @throws \CDbException
     * @throws \CException
     *
     * @depracated
     */
    public static function getUserDepartmentMemberChangeRecordMap($clientId,$userIds,$departmentIds){

        $params =array(
            ':client_id'=>$clientId,
        );

        $subSql ='';
        if (!empty($userIds))
        {
            $subSql .=' and user_id in ('.implode(',',$userIds).') ';
        }

        if (!empty($departmentIds))
        {
            $subSql .=' and department_id in ('.implode(',',$departmentIds).') ';
        }


        $sql = "SELECT user_id,department_id,date(create_time) as date,type  FROM tbl_department_member_change_record WHERE client_id=:client_id  $subSql  order by create_time asc";
        $result = \DepartmentMember::model()->getDbConnection()->createCommand($sql)->queryAll(true,$params );


        $data = array();

        foreach($result as $item){
            $data[$item['user_id']][$item['department_id']][] = $item;
        }

        $map =array();
        foreach($data as $userId => $userItem){

            foreach($userItem as $departmentId => $departmentElem){
                $i = 0;
                $mapItem = array();

                foreach($departmentElem as $elem) {
                    if ($elem['type'] == \DepartmentMemberChangeRecord::TYPE_DEPARTMENT_MEMBER_CHANGE_IN) {
                        if (isset($mapItem[$i]) && count($mapItem[$i]) == 2) $i++;
                        $mapItem[$i]['in'] = $elem['date'];
                    }

                    if ($elem['type'] == \DepartmentMemberChangeRecord::TYPE_DEPARTMENT_MEMBER_CHANGE_OUT) {
                        if ($i == 0 && !isset($mapItem[$i])) {
                            $mapItem[$i]['in'] = '2014-01-01';
                        }
                        $mapItem[$i]['out'] = $elem['date'];
                        $i++;
                    }

                    $map[$userId][$departmentId] = $mapItem;
                }
            }
        }

        return $map;
    }

    public static function batchGetUserDepartmentList($clientId,$userIds)
    {
        $result =[];
        if (empty($userIds)) return $result;

//        $db = \Department::model()->getDbConnection();
//        $sql = 'select user_id,department_id,user_type  from tbl_department_member where client_id=:client_id and user_id in ('.implode(',',$userIds).')';
//        $departmentMemberList = $db->createCommand($sql)->queryAll(true,[':client_id'=>$clientId]);
        $departmentMemberList = (new DepartmentRedis())->getBatchUserDepartment($clientId, $userIds, true);
        $departmentIds = array_column($departmentMemberList??[],'department_id');
        if(empty($departmentIds))
            return $result;

//        $sql_list = sprintf('select id,name,prefix,parent_id from tbl_department where client_id=:client_id and id in (%s) and enable_flag=:enable_flag', implode(',',$departmentIds));
//        $departmentList =  $db->createCommand($sql_list)->queryAll(true,[':client_id'=>$clientId, ':enable_flag' => Department::ENABLE_FLAG_TRUE]);
        $departmentList = DepartmentCacheableRepo::instance($clientId)->findAllByConditions(['client_id' => $clientId, 'enable_flag' => 1, 'id' => $departmentIds], 'id,name,prefix,parent_id');

        $departmentMap = array_combine(array_column($departmentList,'id'),$departmentList);

        foreach ( $departmentMemberList as $item ){
            if( !$item['department_id'] ){
                $department = [
                    'id'=>'0',
                    'name' => \Yii::t('common', 'My enterprise'),
                    'admin_type' => $item['user_type'],
                ];
            }else{
                $department = $departmentMap[$item['department_id']]??[];
            }

            if( empty($department) )
                continue;
            $department['admin_type'] = $item['user_type'];
            $result[$item['user_id']][] = $department;

        }

        return $result;
    }

    public static function batchGetDepartmentListForIds($clientId,array $departmentIds){

        $result =[];
        $departmentIds = array_unique(array_diff($departmentIds, ['']));
        if(empty($departmentIds))
            return $result;

//        $db = \DepartmentMember::model()->getDbConnection();
//        $departmentList =  $db->createCommand("select id, name,prefix,parent_id from tbl_department where client_id=:client_id and id in (".implode(',',$departmentIds).')')->queryAll(true,[':client_id'=>$clientId]);

        $departmentList = DepartmentCacheableRepo::instance($clientId)->findAllByConditions(['client_id' => $clientId, 'enable_flag' => 1, 'id' => $departmentIds], 'id,name,prefix,parent_id');

        $departmentIdsByCache = array_column($departmentList, 'id');
        $departmentIdsByDb = array_diff($departmentIds, $departmentIdsByCache);
        if (!empty($departmentIdsByDb)) {
            $db = \DepartmentMember::model()->getDbConnection();
            $departmentListByDb =  $db->createCommand("select id, name,prefix,parent_id from tbl_department where client_id=:client_id and id in (".implode(',',$departmentIdsByDb).')')->queryAll(true,[':client_id'=>$clientId]);
            $departmentList = array_merge($departmentList, $departmentListByDb);
        }
        $departmentMap = array_combine(array_column($departmentList,'id'),$departmentList);


        foreach ( $departmentIds as $id ){
            if( !$id ){
                $department = [
                    'id'=>'0',
                    'name' => \Yii::t('common', 'My enterprise'),
                ];
            }else{
                $department = $departmentMap[$id]??[];
            }

            !empty($department) &&$result[$id] = $department;
        }

        return $result;
    }


    public static function getAtHomeUserIds($clientId)
    {
        return (new DepartmentRedis)->getUserList($clientId, 0);
//        return (new DepartmentRedis)->getDepartmentUserRecursiveMap($clientId, 0)[0] ?? [];
//        $result = \DepartmentMember::model()->getDbConnection()->createCommand("select distinct(user_id) as user_id from tbl_department_member where client_id=:client_id")->queryAll(true,[':client_id'=>$clientId]);
//        return array_unique(array_column($result, 'user_id'));
    }

    public static function getDepartmentMemberAdminList($clientId,$userIds=[]){

        $adminUserIds = (new DepartmentRedis())->getBatchDepartmentAdminUser($clientId, [], true);
        if (!empty($userIds)) {
            $adminUserIds = array_values(array_intersect($userIds, $adminUserIds));
        }
        return $adminUserIds;
//        $sql = 'select user_id  from tbl_department_member where client_id=:client_id and user_type=:user_type';
//        if( !empty($userIds) ){
//            $sql .= ' and user_id in('.implode(',',$userIds).')';
//        }
//        $result = \DepartmentMember::model()->getDbConnection()->createCommand($sql)->queryColumn([':client_id'=>$clientId,':user_type'=>1]);
//        return $result;
    }

    /** 跟进部门id列表，返回对应的部门全程
     * @param $clientId
     * @param array $departmentIds
     * @return array
     * @throws \CDbException
     */
    public static function getDepartmentFullNameByIds($clientId,array $departmentIds, $connector = '-')
    {
        $result =[];
        if(empty($departmentIds))
            return $result;
        $departmentIds = array_unique($departmentIds);
//        $db = \DepartmentMember::model()->getDbConnection();
//        //获取全公司的部门
//        $enable_flag_sql = sprintf(' and enable_flag = %d', Department::ENABLE_FLAG_TRUE);
//        $allDepartmentList =  $db->createCommand("select id, name,prefix,parent_id from tbl_department where client_id=:client_id $enable_flag_sql ")->queryAll(true,[':client_id'=>$clientId]);

        $allDepartmentList = DepartmentCacheableRepo::instance($clientId)->findAllByConditions(['client_id' => $clientId, 'enable_flag' => 1], 'id,name,prefix,parent_id');
        $allDepartmentMap = array_combine(array_column($allDepartmentList,'id'),$allDepartmentList);
        $departmentMap = [];
        //获取本次处理的部门
        foreach ($departmentIds as $id){
            if(isset($allDepartmentMap[$id])){
                $departmentMap[$id] = $allDepartmentMap[$id];
            }
        }

        //拼接需要处理的部门的数据
        foreach ( $departmentMap as $item ){
            //如果是一级部门，返回自己
            if($item['parent_id'] == 0 ){
                $departmentTreeName = $item['name'];
            }else{
                //处理父级部门
                $prefixList = explode('-',$item['prefix']);
                $departmentTreeName = "";
                foreach ($prefixList as $key => $prefixItem){
                    //获取层级关系
                    if($prefixItem && $prefixItem>0){
                        $prefixIds[] = $prefixItem;
                        $departmentTreeName .= $allDepartmentMap[$prefixItem]['name']??"";
                    }
                }
                // 父级部门 + 自己部门
                $departmentTreeName .= $connector.$item['name'];
            }

            $result[$item['id']] = $departmentTreeName;
        }

        return $result;

    }
        /**
     * 是否是下属
     * @param \User $user
     * @param $subordinateUserId
     * @return bool
         *
         * @depracated
     */
    public static function isSubordinate(\User $user, $subordinateUserId)
    {
        if($user->getUserId() == $subordinateUserId)
            return true;

        $all = $user->getAllManageableUserIds();
        return in_array($subordinateUserId, $all);
    }


    public static function syncRedis($clientId, $redisClient = null)
    {
        $redis = $redisClient ?: new DepartmentRedis();
        $redisData = self::buildDepartmentUserRecusiveData($clientId);

        foreach ($redisData as $departmentId => $members)
        {
            $redis->del($clientId, $departmentId);
            $redis->addUser($clientId, $departmentId, $members);
        }

        $redis->setDepartmentRecursiveUser($clientId, $redisData);
        \common\library\department\DepartmentCacheableRepo::instance($clientId)->refreshMember([], []);

        return $redisData;

    }

    // 重建部门层级
    public static function rebuildDepartmentRecursive($clientId)
    {
        $db = \Department::model()->getDbConnection();

        $sql = "select * from tbl_department where client_id=:client_id";
        $departmentList = $db->createCommand($sql)->queryAll(true, [
            ':client_id' => $clientId,
        ]);
        $departmentList = array_column($departmentList, null, 'id');
        foreach ($departmentList as $department)
        {
            $process = $department;

            $layer = 1;
            $prefix = '0-';

            if ($process['parent_id'] != 0)
            {
                $prefix = '';
                do
                {
                    //如果父级已经被删除，就跳出
                    if(!isset($departmentList[$process['parent_id']]) ){
                        $layer = 1;
                        $prefix = '';
                        break;
                    }

                    $process = $departmentList[$process['parent_id']];
                    $layer ++ ;
                    $prefix =  $process['id'] . '-' . $prefix;
                }
                while ($process['parent_id'] != 0);

                $prefix = '0-' . $prefix;
            }

            $updateSql = "update tbl_department set layer=$layer,prefix='$prefix' where id={$department['id']}";
            \LogUtil::info("rebuildDepartmentRecursive : $updateSql");
            $db->createCommand($updateSql)->execute();
        }
        //清理部门缓存
        self::syncRedis($clientId);

        //重算员工权限关系
        (new \common\library\privilege_v3\UserPrivilegeRelationService($clientId))->clientUserRelationInit();
        //重算员工角色权限
        (new \common\library\privilege_v3\UserRolePrivilegeService($clientId))->clientUserRolePrivilegeInit();
    }

    public static function buildDepartmentUserRecusiveData($clientId)
    {
        $db = \Department::model()->getDbConnection();

        $sql = "select * from tbl_department where client_id=:client_id and enable_flag = :enable_flag ";
        $result = $db->createCommand($sql)->queryAll(true, [
            ':client_id'=>$clientId,
            ':enable_flag' => Department::ENABLE_FLAG_TRUE
        ]);
        $result[] = ['id' => 0, 'prefix' => ''];

        $redisData = [];

        $date = date('Y-m-d H:i:s');

        $departmentMemberMap = \DepartmentMember::findBatchDepartmentMemberList($clientId, array_column($result, 'id'));
        foreach ($result as $elem)
        {
//            $members = \DepartmentMember::findMemberList($elem['id'], $clientId);
            $members = $departmentMemberMap[$elem['id']] ?? [];
            $members = array_combine($members, array_fill(0, count($members), strtotime($date)));

            $redisData[$elem['id']] = array_replace($redisData[$elem['id']] ?? [] , $members);

            $prefix = trim($elem['prefix'], '-');

            $list = explode('-', $prefix);

            foreach ($list as $parentId)
            {
                $redisData[$parentId] = array_replace($redisData[$parentId] ?? [] , $members);
            }
        }

        return $redisData;
    }

    public static function undistributedNode($clientId, $showMemberInfo=true, $showUserMailList=false)
    {
        $undistributedMembers = [];
        if ($showMemberInfo)
        {
            $atHomeUserIds = Helper::getAtHomeUserIds($clientId);
            $userListObj = new UserList();
            $userListObj->setClientId($clientId);
            $userListObj->setEnableFlag(\common\library\account\UserInfo::ENABLE_TRUE);
            $userListObj->setExcludeUserIds($atHomeUserIds);
            $userListObj->getFormatter()->setShowUserMailList($showUserMailList);
            $undistributedMembers = $userListObj->find();
        }

        $undistributedNode = [
            'id' => Department::UNDISTRIBUTED_NODE_ID,
            'name' => \Yii::t('customer', 'Unallocated'),
            'description' => "",
            'parent_id' => "0",
            'prefix' => "0-",
            'layer' => "1",
            'rank' => "9999",
            'status' => "1",
            'version' => "1",
            'client_id' => $clientId,
            'create_user' => "0",
            'create_time' => "",
            'update_user' => "0",
            'update_time' => "",
            'member' => $undistributedMembers,
            'member_count' => count($undistributedMembers),
            'member_recursive_count' => count($undistributedMembers),
        ];

        return $undistributedNode;

    }

    public static function rootNode($clientId, array $memberList = [], array $invitingList = [])
    {
        $rootNode = [
            'id' => 0,
            'client_id' => $clientId,
            'name' => \Yii::t('common', 'My enterprise'),
            'member' => $memberList,
            'member_count' => count($memberList),
            'member_recursive_count' => count($memberList),
            'invitingMember' => $invitingList,
        ];

        return $rootNode;
    }


    public static function canManageAnyUsers($clientId, $userId, $permission, $scopeUser)
    {
        return \common\library\privilege_v3\Helper::canManageAnyUsers($clientId, $userId, $permission, $scopeUser);
    }

    public static function getPermissionUserIds($clientId, $userId, $permission = '')
    {
        $userListIds = [];
        $departmentPermission = new \common\library\department\DepartmentPermission( $clientId, $userId );
        if ($permission) {
            $departmentPermission->permission($permission);
        } else {
            $departmentPermission->filterBrotherAdmin(true);
        }
        $result = $departmentPermission->userList();
        if($result){
            $userListIds = array_column($result,'user_id');
        }
        return $userListIds;
    }

    public static function frozenNode($clientId, $showMemberInfo=true, $showUserMailList=false)
    {

        $userListObj = new \common\library\account\UserList();
        $userListObj->setClientId($clientId);
        $userListObj->setFreezeFlag(1);
        if ($showMemberInfo)
        {
            $userListObj->getFormatter()->setShowUserMailList($showUserMailList);
        }

        $list = $userListObj->find();

        $node = [
            'id' => Department::FROZEN_NODE_ID,
            'name' => \Yii::t('customer', 'Frozen'),
            'description' => "",
            'parent_id' => "0",
            'prefix' => "0-",
            'layer' => "1",
            'rank' => "10000",
            'status' => "1",
            'version' => "1",
            'client_id' => $clientId,
            'create_user' => "0",
            'create_time' => "",
            'update_user' => "0",
            'update_time' => "",
            'member' => $list,
            'member_count' => count($list),
            'member_recursive_count' => 0,
        ];

        return $node;
    }

    public static function deleteNode($clientId, $showMemberInfo=true, $showUserMailList=false)
    {

        $userListObj = new \common\library\account\UserList();
        $userListObj->setClientId($clientId);
        $userListObj->setEnableFlag(\common\library\account\UserInfo::ENABLE_DELETE);
        if ($showMemberInfo)
        {
            $userListObj->getFormatter()->setShowUserMailList($showUserMailList);
        }

        $list = $userListObj->find();

        $node = [
            'id' => Department::DELETE_NODE_ID,
            'name' => \Yii::t('customer', 'Delete'),
            'description' => "",
            'parent_id' => "0",
            'prefix' => "0-",
            'layer' => "1",
            'rank' => "10001",
            'status' => "1",
            'version' => "1",
            'client_id' => $clientId,
            'create_user' => "0",
            'create_time' => "",
            'update_user' => "0",
            'update_time' => "",
            'member' => $list,
            'member_count' => count($list),
            'member_recursive_count' => 0,
        ];

        return $node;
    }


    /** 根据父级部门返回所有子部门id，包含父部门
     * @param $clientId
     * @param array $departmentIds
     * @return array
     * @throws \CDbException
     */
    public static function getBatchDepartmentChildIds($clientId, $departmentId)
    {
        $result = $departmentIds = array_unique((array)$departmentId);
//        $db = \DepartmentMember::model()->getDbConnection();
//        //获取全公司的部门
//        $enable_flag_sql = sprintf(' and enable_flag = %d', Department::ENABLE_FLAG_TRUE);
//        $allDepartmentList =  $db->createCommand("select id, name,prefix,parent_id from tbl_department where client_id=:client_id $enable_flag_sql ")->queryAll(true,[':client_id'=>$clientId]);
        $allDepartmentList = DepartmentCacheableRepo::instance($clientId)->findAllByConditions(['client_id' => $clientId, 'enable_flag' => 1], 'id,name,prefix,parent_id');

        foreach ($allDepartmentList as $department) {
            $prefixList = explode('-', $department['prefix']);
            if (array_intersect($prefixList, $departmentIds)) {
                $result[] = intval($department['id']);
            }
        }

        return array_unique($result);
    }

    /**
     * 平铺组织架构列表
     * time: 11:17 AM
     * user: huagongzi
     * @param $clientId
     * @param $deppartmentId
     * @return array
     */
    public static function getDepartmentList($clientId,$departmentId = DepartmentFormatter::ROOT_DEPARTMENT_ID){
        $result = [];

        if(empty($clientId)){
            return $result;
        }

        try {
            return DepartmentCacheableRepo::instance($clientId)->findAllByConditions(array_merge([
                'client_id' => $clientId,
                'enable_flag' => Department::ENABLE_FLAG_TRUE,
            ], $departmentId ? ['id' => $departmentId] : []), '*', 'layer asc,`rank` asc');
//            $db     = \Department::model()->getDbConnection();
//            $params = [":client_id" => $clientId];
//
//            $enable_flag_sql = sprintf(' and enable_flag = %d', Department::ENABLE_FLAG_TRUE);
//            $sql = "select * from tbl_department where client_id=:client_id $enable_flag_sql ";
//
//            if($departmentId){
//                $sql          .= " and id=:id ";
//                $params[":id"] = intval($departmentId);
//            }
//
//            $sql   .= " order by layer asc,`rank` asc";
//            $result = $db->createCommand($sql)->queryAll(true, $params);
//
//            return $result;
        } catch (\Exception $e){
            // 记录错误日志方便问题追踪
            \LogUtil::error($e->getMessage());

            return $result;
        }
    }

    /**
     * 返回部门id和部门下的用户userid关系，主要给阿里同学用
     * time: 3:23 PM
     * user: huagongzi
     * @param $client
     * @param $departmentId
     * @return array
     */
    public static function getDepartmentUserRelationList($clientId, $departmentId = DepartmentFormatter::ROOT_DEPARTMENT_ID){
        $result = [];

        if(empty($clientId)){
            return $result;
        }

        try {
            $db     = \Department::model()->getDbConnection();
            $params = [":client_id" => $clientId];

            // 获取部门id列表
            $enable_flag_sql = sprintf(' and enable_flag = %d', Department::ENABLE_FLAG_TRUE);
            $result          = $db->createCommand("select distinct(id) from tbl_department where client_id=:client_id $enable_flag_sql ")->queryAll(true, $params);
            $departmentIds   = !empty($result) ? array_column($result,'id') : [];

            // 查询企业用户id
            $userIds = $db->createCommand("select distinct(user_id) from tbl_user_info where client_id=:client_id")->queryAll(true, $params);
            $userIds = !empty($userIds) ? array_column($userIds,'user_id') : [];

            // 获取部门下的所有的用户
            $sql    = "SELECT department_id,user_id,user_type,client_id FROM tbl_department_member WHERE client_id=:client_id ";
            $where  = ["client_id:client_id"];

            // 指定具体的部门id
            if($departmentId){
                array_push($where,"department_id:department_id");
                $params[":department_id"] = $departmentId;

                $sql .= " AND department_id=:department_id";
            } else {
                // 包含根节点
                array_push($departmentIds,DepartmentFormatter::ROOT_DEPARTMENT_ID);
            }

            // 指定部门id集合，过滤脏数据
            if(!empty($departmentIds)){
                $sql .= " AND department_id in(" . implode(',', $departmentIds) . ")";
            }

            // 指定用户列表，过滤脏数据
            if(!empty($userIds)){
                $sql .= " AND user_id in(" . implode(',', $userIds) . ")";
            }

            $sql .= " ORDER BY department_id ASC";

            $userList = $db->createCommand($sql)->queryAll(true, $params);

            return $userList ?? $result;

        } catch (Exception $e){
            // 记录错误日志方便问题追踪
            LogUtil::error($e->getMessage());

            return $result;
        }


    }


    /**
     * ames获取小满企业信息(ames专用)
     * time: 5:36 PM
     * user: huagongzi
     * @param $clientId
     * @return array
     */
    public static function amesGetClientInfo($clientId){

        $result = [];

        if(empty($clientId)){
            return $result;
        }

        // todo:缓存化
        $contract = \common\library\account\ClientContractRelation::getClient($clientId);

        if($contract){
            $client = \common\library\account\Client::getClient($clientId);

            if($client->isNew()){
                return $result;
            }

            $result = [
                "client_id"   => $clientId,
                "credit_code" => $contract['credit_code'],
                "ali_id"      => $contract['ali_id'],
                "full_name"   => $client->full_name
            ];
        }

        return $result;
    }

    /**
     * @param $tree
     * @return mixed|void
     * 所有成员平铺展示
     */
    public static function tiledTree($tree){
        $curMembers = $tree['member'] ?? [];
        $allMembers = self::getNodeMembers($tree,$curMembers);
        $tree['member'] = $allMembers;
        $tree['member_count'] = count($allMembers);
        $tree['member_recursive_count'] = count($allMembers);
        $tree['node'] = [];
        return $tree;
    }


    /**
     * @param $node
     * @param $members
     * @return array
     * 获取所有节点成员
     */
    public static function getNodeMembers($node, $members = []){
        if (!isset($node['node'])){
            return $members;
        }

        foreach ($node['node'] as $nodeItem){
            if (isset($nodeItem['node'])){
                $members = self::getNodeMembers($nodeItem,$members);
            }
            $members = array_merge($members,$nodeItem['member'] ?? []);
        }

        return array_values(array_column($members,null,'user_id'));
    }

    /**
     * 发送修改部门用户类型信息给ames
     * time: 11:51 AM
     * user: huagongzi
     * @param $clientId
     * @param $departmentId
     * @param $userType
     * @param $userIds
     * @return void
     */
    public static function sendChangeUserTypeMsgToAmes($clientId,$departmentId,$userType,$userIds){
        if(!is_array($userIds)){
            $userIds = [$userIds];
        }

        $userIds = array_filter($userIds);

        if(empty($userIds)){
            throw new \RuntimeException("Empty userIds!", -1);
        }

        // ames基于消息通知，暂时不支持批量操作，如果遇到性能问题，需要推动ames一起来修改
        foreach ($userIds as  $userId){
            // 通知ames埋点且不允许影响主要流程
            $parames    = ["clientId" => $clientId, "bizId" => $departmentId, "bizScene" => "okkiOrgUserPositionUpdateEvent"];
            $remark     = ["remark" => json_encode(["user_id" => $userId,"user_type" => intval($userType)])];
            $tmpParames = array_merge($parames,$remark);

            \common\library\ames\Helper::amesEventNotify($tmpParames);
        }
    }

    //获取删除部门的节点
    public static function deleteDepartmentNode($clientId)
    {
        $departmentObj = new DepartmentList($clientId);
        $departmentObj->setEnableFlag(\Constants::ENABLE_FLAG_FALSE);
        $departmentObj->setFields(['id','layer','prefix','name','parent_id','`rank`']);
        $departmentList = $departmentObj->find();
        $node = [
            'id' => Department::DELETE_DEPARTMENT_NODE_ID,
            'name' => \Yii::t('customer', 'Delete'),
            'parent_id' => "0",
            'prefix' => "0-",
            'layer' => "1",
            'rank' => "10002",
            'client_id' => $clientId,
            'node' => $departmentList,
        ];

        return $node;
    }
}
