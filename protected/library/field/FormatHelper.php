<?php
/**
 * Created by PhpStorm.
 * User: andy
 * Date: 2021/09/17
 * Time: 15:51
 */

namespace common\library\field;

class FormatHelper
{
    public static function dayGap(array $line, array $columns)
    {
        $key = $columns[Constant::COLUMN_KEY_DEFAULT];
        $datetime = $line[$key]; // TODO
        $diff = date_diff(date_create(date('Y-m-d', time())), date_create(date('Y-m-d', strtotime($datetime))));
        return $diff->days;
    }
}