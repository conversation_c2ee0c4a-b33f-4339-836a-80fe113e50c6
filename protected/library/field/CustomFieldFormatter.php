<?php
/**
 * Created by PhpStorm.
 * User: andy
 * Date: 2021/04/21
 * Time: 14:47
 */

namespace common\library\field;

use common\library\field\biz_type\FieldBizTypeFilter;
use common\library\field\layout\FieldLayoutFilter;
use common\library\privilege_v3;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use xiaoman\orm\common\Formatter;

class CustomFieldFormatter extends Formatter
{
    protected $bizTypeId;
    protected $layoutId;
    protected $type;
    protected $hideDisabled = false;
    protected $privilegeUser;
    protected $functionalId;

    const MAPPING_SETTING = [
        'with_privilege' => [
            'build_function' => 'buildPrivilege',
            'mapping_function' => 'mapPrivilege',
        ],
        'with_biz_type' => [
            'build_function' => 'buildBizType',
            'mapping_function' => 'mapBizType',
        ],
        'with_layout' => [
            'build_function' => 'buildLayout',
            'mapping_function' => 'mapLayout',
        ],

        'with_group_info' => [
            'build_function' => 'build_group_info'
        ],
    ];

    public function normalSetting()
    {
        $this->displayFields($this->metadata->columnFields());
    }

    public function useUserPrivilege($user, $functionalId = null)
    {
        $this->privilegeUser = $user;
        $this->functionalId = $functionalId;
        $this->displayWithPrivilege(true);
    }

    public function restoreSetting()
    {
        parent::restoreSetting();

        $this->layoutId = null;
        $this->bizTypeId = null;
        $this->type = null;
        $this->privilegeUser = null;
        $this->functionId = null;
    }

    protected function processDisabledFlag(array &$row, $flag, array $rawData)
    {
        if ($rawData['disabled_flag'] == 1 || (array_key_exists('disabled_flag', $row) && ($row['disabled_flag'] == 1))) // if field is disabled in first place, will not be reversed.
            return;

        if ($this->hideDisabled && $flag)
            $row['disabled_flag'] = 1; // force set value in row, process in constructResultItem

        if (array_key_exists('disabled_flag', $row))
            $row['disabled_flag'] = $flag;
    }

    protected function getType(array $data)
    {
        if ($this->type !== null)
            return $this->type;

        $types = array_unique(array_column($data, 'type'));

        if (count($types) != 1)
            throw new \ProcessException('multiple types in data is invalid');

        return $this->type = $types[array_key_first($types)];
    }

    public function useBizType($bizTypeId)
    {
        $this->bizTypeId = $bizTypeId;
        $this->displayWithBizType(true);
    }

    protected function buildBizType($key, array $data)
    {
        $filter = new FieldBizTypeFilter($this->clientId);
        $filter->biz_type_id = $this->bizTypeId;
        $filter->type = $this->getType($data);
        $filter->select(['field_id', 'disabled_flag']);
        $result = $filter->find()->getAttributes(['field_id', 'disabled_flag']);
        return array_column($result, 'disabled_flag', 'field_id');
    }

    protected function mapBizType(array &$result, $key, array $data)
    {
        $map = $this->getPipelinePrepareData($key);

        if (array_key_exists($data['field_id'], $map))
        {
            $this->processDisabledFlag($result, $map[$data['field_id']], $data);
        }
    }

    public function useLayout($layoutId)
    {
        $this->layoutId = $layoutId;
        $this->displayWithLayout(true);
    }

    protected function buildLayout($key, array $data)
    {
        $filter = new FieldLayoutFilter($this->clientId);
        $filter->layout_id = $this->layoutId;
        $filter->type = $this->getType($data);
        $filter->select(['field_id', 'group_id', 'required', 'is_editable', 'disabled_flag']);
        $result = $filter->find()->getAttributes(['field_id', 'group_id', 'required', 'is_editable', 'disabled_flag']);
        return array_column($result, null, 'field_id');
    }

    protected function mapLayout(array &$result, $key, array $data)
    {
        $map = $this->getPipelinePrepareData($key);

        if (array_key_exists($data['field_id'], $map))
        {
            $this->processDisabledFlag($result, $map[$data['field_id']]['disabled_flag'], $data);

            if (array_key_exists('group_id', $result))
                $result['group_id'] = $map[$data['field_id']]['group_id'];

            if (array_key_exists('required', $result))
                $result['required'] = $map[$data['field_id']]['required'];

            if (array_key_exists('is_editable', $result))
                $result['is_editable'] = $map[$data['field_id']]['is_editable'];
        }
    }

    protected function buildPrivilege($key, array $data)
    {
        if ($this->functionalId === null)
        {
            $type = $this->getType($data);
            $this->functionalId = privilege_v3\Helper::getFunctionalIdByRefer($type);
        }
        // TODO  key as type-field_id
        return PrivilegeService::getInstance($this->clientId, $this->privilegeUser)->getFieldScopeMap($this->functionalId);
    }

    protected function mapPrivilege(array &$result, $key, array $data)
    {
        $map = $this->getPipelinePrepareData($key);

        $fieldId = $data['id'];
        $type = $data['type'];
        if ($scope = $map[$type . '-' . $fieldId] ?? false)
        {
            switch ($scope) {
                case PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_DISABLE:
                    $this->processDisabledFlag($result, Constant::FIELD_DISABLE, $data);
                    break;
                case PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_READONLY:
                    if (array_key_exists('is_editable', $result))
                        $result['is_editable'] = 0;
                    break;
                case PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_READ_WRITE:
                    $this->processDisabledFlag($result, Constant::FIELD_ENABLE, $data);
                    if (array_key_exists('is_editable', $result))
                        $result['is_editable'] = 1;
                    break;
                default:
                    break;
            }
        }
    }

    protected function constructResultList(array &$result, ?array $item)
    {
        if ($item === null)
            return;

        $result[] = $item;
    }

    protected function constructResultItem(array $item)
    {
        if ($this->hideDisabled && $item['disabled_flag'])
            $item = null;

        return $item;
    }

    public function groupInfoSetting()
    {

    }


}