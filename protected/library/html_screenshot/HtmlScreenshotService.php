<?php

namespace common\library\html_screenshot;

use common\library\api\InnerApi;
use ProcessException;
use RuntimeException;

class HtmlScreenshotService
{
    protected $userId = 0;
    protected $clientId = 0;
    /**
     * @var array 调试跟踪信息
     */
    protected $traceData = [];




    /**
     * HTML转图片
     *
     * @param string $html HTML文档
     * @return array   图片 @return array 图片 ["url" => "https://...", "file_id" => xxx]
     * @throws ProcessException
     * @throws RuntimeException
     * @return array{url: string, file_id: int}
     */
    public function html2image(string $html, string $scene): array
    {
        $data = [
            'html' => $html,
            'scene' => $scene,
            'user_id' => $this->userId,
            'client_id' => $this->clientId,
            'trace_data' => $this->traceData
        ];

        $innerApi = new InnerApi('html_screenshot');
        $innerApi->addHeader("Expect: ");
        $result = $innerApi->call('Html2Image', $data);

        if (empty($result['data']['file'])) {
            throw new RuntimeException("HTML转图片失败: ". $result['message'] ?? '');
        }
        return (array) $result['data']['file'];
    }

    /**
     * （同步）网页转pdf
     * @param string $url
     * @param string $scene
     * @param string $filename 指定文件名
     * @param array<string> $fallbackUrls 压缩图片后的降级地址
     * @return array
     * @throws ProcessException
     */
    public function html2pdf(string $url, string $scene, string $filename = '', array $fallbackUrls = []): array
    {
        $data = [
            'url' => $url,
            'scene' => $scene,
            'user_id' => $this->userId,
            'client_id' => $this->clientId,
            'filename' => $filename,
            'trace_data' => $this->traceData,
            'fallback_urls' => $fallbackUrls,
        ];

        $innerApi = new InnerApi('html_screenshot');
        $innerApi->addHeader("Expect: ");
        $innerApi->setTimeout(600); // pdf转换服务首次转换+2次重试
        $result = $innerApi->call('Html2Pdf', $data);
        if (empty($result['data']['file'])) {
            throw new RuntimeException("HTML转pdf失败: ". $result['message'] ?? '');
        }
        return (array) $result['data']['file'];
    }

    public function setUserId(int $userId): self
    {
        $this->userId = $userId;
        return $this;
    }

    public function setClientId(int $clientId): self
    {
        $this->clientId = $clientId;
        return $this;
    }

    /**
     * 设置调试跟踪信息，信息会在转换服务日志中打印出来
     */
    public function setTraceData(array $data): self
    {
        $this->traceData = $data;
        return $this;
    }

    /**
     * 转换失败时，生成备用的地址
     * 通过增加url的query参数：compress_level=1 和 compress_level=2，来生成备用的地址
     * @see https://xmkm.yuque.com/armee3/dzbqgm/sct5rm6ls7txg40m#VGtbo
     * @param string $url
     * @return array<string>
     */
    public function makeFallbackUrls(string $url): array
    {
        $count = 2;
        $urls = [];
        for ($i = 1; $i <= $count; $i++) {
            if(str_contains($url, '?')) {
                $urls[] = "$url&compress_level=$i";
            } else {
                $urls[] = "$url?compress_level=$i";
            }
        }

        return $urls;
    }
}