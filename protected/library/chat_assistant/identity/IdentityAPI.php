<?php
/**
 * The file is part of the php-crm.
 *
 * (c) anhoder <<EMAIL>>.
 *
 * 2021/12/14 7:01 PM
 */

namespace common\library\chat_assistant\identity;

use common\library\alibaba\customer\TMAlibabaCustomerService;
use common\library\alibaba\store\AlibabaStore;
use common\library\alibaba\trade\AlibabaTradeRelationList;
use common\library\api\InnerApi;
use common\library\chat_assistant\Constants;
use common\library\chat_assistant\identity_external\IdentityExternalAPI;
use common\library\customer\service\AccessService;
use common\library\customer\service\LeadAccessService;
use common\library\customer_v3\company\list\CompanyList;
use common\library\customer_v3\company\orm\Company;
use common\library\customer_v3\customer\orm\Customer;
use common\library\email\CommonDomain;
use common\library\email\Util;
use common\library\email_identity\cards\Card;
use common\library\email_identity\EmailIdentity;
use common\library\lead\LeadCustomerList;
use common\library\lead\LeadList;
use common\library\lead_v2\Lead;
use common\library\privilege_v3\Helper;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\sales_assistant\AssistantTabSettingService;
use common\library\sns\Constants as SnsConstants;
use common\library\sns\customer\CustomerContactHelper;
use common\library\sns\customer\CustomerContactService;
use common\library\social_auth\Constant;
use common\library\track\TrackHelper;
use common\library\util\PgsqlUtil;
use common\library\util\RedLock;
use common\library\util\TimeUtil;
use common\library\visitor_marketing\visitorMarketingService;
use common\library\wecom\customer\WecomCustomerService;
use common\models\shop\AimsLanguage;
use http\Exception\RuntimeException;
use LogUtil;
use xiaoman\orm\database\data\In;
use xiaoman\orm\exception\OrmException;

class IdentityAPI
{
    protected $clientId;
    protected $userId;
    protected $externalAPI;

    public function __construct($clientId, $userId = 0)
    {
        $this->clientId = $clientId;
        $this->userId = $userId;
        $this->externalAPI = new IdentityExternalAPI($this->clientId, $this->userId);
    }

    /**
     * 验证身份
     * @param $platform
     * @param $identifier
     * @param bool $directOwner
     * @param int $sellerAccountId
     * @return array
     * $corpWecomOpenUserid   企微用户ID 字符串
     */
    public function identify(int $platform, $identifier, bool $directOwner = true, $sellerAccountId = 0, int $facebookPageId = 0, $channelOpenId = '', $instagramAccountId = 0, string $corpWecomOpenUserid = ''): array
    {
        if(!$identifier){
            return [];
        }
        $identityMap = [
            Constants::PLATFORM_EMAIL    => [new Card($this->userId), 'mailCard'],
            Constants::PLATFORM_WHATSAPP => [$this, 'identifyWhatsApp'],
            Constants::PLATFORM_TM       => [$this, 'identifyTM'],
            Constants::PLATFORM_FACEBOOK => [$this, 'identifyFacebook'],
            Constants::PLATFORM_WHATSAPP_BUSINESS => [$this, 'identifyWhatsAppBusiness'],
            Constants::PLATFORM_VISITOR_MARKETING => [$this, 'identifyVisitorMarketing'],
            Constants::PLATFORM_INSTAGRAM => [$this, 'identifyInstagram'],
            Constants::PLATFORM_WECOM => [$this, 'identifyWecom'],
            Constants::PLATFORM_WHATSAPP_CLOUD => [$this, 'identifyWhatsAppCloud'],
        ];

        if (!isset($identityMap[$platform])) {
            throw new \RuntimeException(\Yii::t('common','Platform type error'));
        }

        $channelId = 0;
        if ($sellerAccountId) {
            // sellerAccountId 获取storeId
            $accountInfo = \common\library\alibaba\Helper::getBindAccountInfoBySellerId($sellerAccountId);
            $channelId = $accountInfo['store_id'] ?? 0;
        }

        if ($corpWecomOpenUserid) {
            // $corpWecomOpenUserid 获取corpid
            $accountInfo = \common\library\wecom\Helper::getBindAccountInfoByCorpWecomOpenUserId($corpWecomOpenUserid);
            $channelId = $accountInfo['corpid'] ?? 0;
        }

        if ($platform === Constants::PLATFORM_FACEBOOK) {
            $channelId = $facebookPageId;
        }

        if (in_array($platform, [Constants::PLATFORM_VISITOR_MARKETING])) {
            $channelId = $channelOpenId;
        }

        if ($platform === Constants::PLATFORM_INSTAGRAM) {
            $channelId = $instagramAccountId;
        }

        $card = call_user_func($identityMap[$platform], $identifier, $directOwner, $channelId);
        if (in_array($platform, [Constants::PLATFORM_EMAIL, Constants::PLATFORM_WHATSAPP, Constants::PLATFORM_WHATSAPP_CLOUD])) {
            $autoComplete = true;
        }
        $cardType = $card['card_type'] ?? EmailIdentity::CARD_TYPE_STRANGER;

        // fixme AI销售助手埋点统计陌生人出现的次数，超过2025年6月1日可以下线
        if ($platform == Constants::PLATFORM_TM) {
            \LogUtil::info("identifyStatistics",[
                'cardType' => $cardType,
                'identifier' => $identifier,
            ]);
        }
        [$identity, $identityExternal] = $this->findPlatformIdentity($platform, $identifier, $channelId, $autoComplete ?? false);
        $card['identity_id'] = $identity->identity_id;
        $card['company_hash_id'] = $identityExternal->company_hash_id;
        $card['domain'] = $identityExternal->domain;
        $card['company_name'] = $identityExternal->company_name;
        $card['country_code'] = $identityExternal->country_code;
        $card['user_defined_info'] = $identityExternal->user_defined_info ?: null;

        $card['flow_link'] = [
            'flow_id' => $identity->flow_id,
            'stage_id' => $identity->stage_id,
            'link_id' => $identity->link_id,
        ];

        //用于销售助手-智能背调Tab,其他渠道按需接入
        !isset($card['origin_ip']) && $card['origin_ip'] = '';

        $card['privilege_field_stats'] = Helper::getPrivilegeFieldStats(
            $this->clientId, $this->userId,
            empty($card['is_public']) ? PrivilegeConstants::FUNCTIONAL_CUSTOMER : PrivilegeConstants::FUNCTIONAL_COMPANY_POOL
        );
        $card['tab_setting'] = (new AssistantTabSettingService($this->clientId, $this->userId))->getSetting();

        return $card;
    }

    /**
     * 查询同事匹配记录
     * @param $sns_type
     * @param $sns_id
     * @return array
     */
    public function colleagueIdentifyList($sns_type, $sns_id): array
    {
        $result = [];
        $contacts = CustomerContactHelper::getArchiveCustomersBySnsIds($this->clientId, $sns_type, [$sns_id]);
        if (!$contacts) {
            return $result;
        }
        $userIds = [];
        foreach ($contacts as $contact){
            if($this->userId == $contact['contact_user_id']){
                continue;
            }
            $itemUserIds = is_array($contact['user_id']) ? $contact['user_id'] : PgsqlUtil::trimArray($contact['user_id']);
            $itemScopeUsers = is_array($contact['scope_user_ids']) ? $contact['scope_user_ids'] : PgsqlUtil::trimArray($contact['scope_user_ids']);
            $contact['user_id'] = $itemUserIds;
            $itemPoolId = $contact['pool_id'];
            $cardType = (new AccessService($this->clientId, $this->userId))
                ->setCompanyInfoData($this->clientId, $itemUserIds, $itemPoolId, $itemScopeUsers)
                ->cardType(true);
            //仅展示私海、同事、公海客户， 无权限的同事、公海也展示
            if(!in_array($cardType,[EmailIdentity::CARD_TYPE_COMPANY,EmailIdentity::CARD_TYPE_CAN_NOT_MANAGE_COMPANY,EmailIdentity::CARD_TYPE_PUBLIC_COMPANY,EmailIdentity::CARD_TYPE_CAN_NOT_MANAGE_PUBLIC_COMPANY,EmailIdentity::CARD_TYPE_CAN_MANAGE_COLLEAGUES_COMPANY])){
                continue;
            }
            $userIds = array_values(array_unique(array_merge($userIds,$itemUserIds,[$contact['contact_user_id']])));
            $contact['card_type'] = $cardType;
            $contact['owner'] = [];
            $result[] = $contact;
        }

        if($userIds && $result){
            $userList = \common\library\account\Helper::getBatchUserInfo($this->clientId, $userIds);
            $userInfoList = array_map(function ($item) {
                $name = $item['nickname'] ?: ($item['ames_email'] ?: $item['email']);
                return [
                    'user_id' => $item['user_id'],
                    'avatar' => $item['avatar'],
                    'name' => $name,
                    'nickname' => $name,
                ];

            }, $userList ?? []);
            $userMap = array_column($userInfoList,null, 'user_id');
            foreach ($result as $k => $v){
                if(!empty($v['user_id'])){
                    foreach ($v['user_id'] as $user_id){
                        $result[$k]['owner'][] = $userMap[$user_id] ?? [];
                    }
                }
                if(!empty($v['contact_user_id'])){
                    $result[$k]['contact_user_name'] = $userMap[$v['contact_user_id']]['name'] ?? '';
                    $result[$k]['contact_user_nickname'] = $userMap[$v['contact_user_id']]['nickname'] ?? '';
                }
            }
        }

		$data = [];
		foreach ($result as $item){
			if(!isset($data[$item['customer_id']])){
				$data[$item['customer_id']] = $item;
				$data[$item['customer_id']]['customer_name'] = $data[$item['customer_id']]['customer_name'] ?: ($data[$item['customer_id']]['email'] ?: $data[$item['customer_id']]['sns_id']);
				unset($data[$item['customer_id']]['contact_user_id']);
				unset($data[$item['customer_id']]['contact_user_name']);
				unset($data[$item['customer_id']]['contact_user_nickname']);
			}
			if (!in_array($item['contact_user_id'], ($data[$item['customer_id']]['op_user_id'] ?? []))) {
				$data[$item['customer_id']]['op_user_id'][] = $item['contact_user_id'];
				$data[$item['customer_id']]['op_user_info'][] = $item['contact_user_name'];
			}

		}

        return array_values($data);
    }

    /**
     * 通过WhatsApp验证
     * @param $whatsAppId
     * @param bool $directOwner
     * @return array
     */
    public function identifyWhatsApp($whatsAppId, bool $directOwner = true)
    {
        $cardType = EmailIdentity::CARD_TYPE_STRANGER;
        $result = [
            'id'            => 0,
            'customer_id'   => 0,
            'name'          => '',
            'email'         => '',
            'card_type'     => $cardType,
            'whatsapp_id'   => $whatsAppId,
            'time_show'     => TimeUtil::TIMEZONE_NOT_SHOW,
            'in_black_list' => 0,
        ];

        $contacts = CustomerContactHelper::listContactBySnsId($this->clientId, SnsConstants::SNS_CLIENT_WHATSAPP, (string)$whatsAppId, $this->userId);
        if (!$contacts) {
            return $result;
        }

        $contactsCustomer = CustomerContactHelper::getArchiveCustomersBySnsIds($this->clientId, SnsConstants::SNS_CLIENT_WHATSAPP, array($whatsAppId), '', $this->userId);
        $targetCustomerId = array_column($contactsCustomer, 'customer_id','sns_id')[$whatsAppId]??0;

        $leadContactMap = [];
        foreach ($contacts as $contact) {
            $key = $contact['sns_id'] . '_' . $contact['user_id'];
            $leadContactMap[$key] = $contact['lead_id'];
        }
        $key = $whatsAppId . '_' . $this->userId;
        $targetLeadId = $leadContactMap[$key] ?? 0;

        $user = \User::getUserObject($this->userId);
        if (!empty($targetCustomerId)&&($customer = new Customer($this->clientId))->loadById($targetCustomerId)->isExist()) {
            $company = new Company($this->clientId, $customer->company_id);
            $company->setOperatorUserId($this->userId);
            if($company->isExist()){
                $itemUserIds = is_array($company->user_id) ? $company->user_id :  PgsqlUtil::trimArray($company->user_id);
                $itemPoolId = $company->pool_id;
                $cardType = (new AccessService($this->clientId, $this->userId))
                    ->setCompanyInfoData($this->clientId, $itemUserIds, $itemPoolId, $company->scope_user_ids)
                    ->cardType($directOwner);

                $company->getFormatter()->cardInfoSettingByType($cardType, $customer);
                $company->getFormatter()->setShowCaptureCard(true);
                $company->getFormatter()->setShowAlibabaChatInfo(true);
                $company->getFormatter()->setShowAlibabaStoreInfo(true);
                $result = array_merge($result, $company->getAttributes());

                if(!empty($customer->email)){
                    $identity = new Identity($this->clientId);
                    $identity->loadByEmail($this->clientId, $customer->email);
                }

                $result['email_identity_id'] = $identity->identity_id ?? 0;

                $result['id'] = $company->company_id;
                $result['customer_id'] = $targetCustomerId;
                $result['name'] = $company->name;
                $result['email'] = $customer->email;
                $result['card_type'] = $cardType;
                $result['is_edit'] = $company->isEditable($user);
                $result['time_show'] = empty($result['card_type']) || $result['card_type'] == EmailIdentity::CARD_TYPE_STRANGER ?
                    TimeUtil::TIMEZONE_NOT_SHOW : TimeUtil::TIMEZONE_SHOW;

                return $result;
            }
        }

        if(empty($targetLeadId)||$result['card_type']!=EmailIdentity::CARD_TYPE_STRANGER){
            return $result;
        }

        $lead = new Lead($this->clientId);
        $lead->loadById($targetLeadId);
        $accessService = new LeadAccessService($this->clientId, $this->userId);
        $accessService->setLeadInfoData($this->clientId, $lead->user_id, $lead->scope_user_ids);
        $result['card_type'] = $accessService->cardType($directOwner);
        $result['lead_id'] = $result['card_type'] != EmailIdentity::CARD_TYPE_STRANGER ? $targetLeadId : 0;

        return $result;
    }

    /**
     * 通过WhatsAppCloud验证
     * @param $whatsAppId
     * @param bool $directOwner
     * @return array
     */
    public function identifyWhatsAppCloud($whatsAppCloudId, bool $directOwner = true)
    {
        $cardType = EmailIdentity::CARD_TYPE_STRANGER;
        $result = [
            'id'            => 0,
            'customer_id'   => 0,
            'name'          => '',
            'email'         => '',
            'card_type'     => $cardType,
            'whatsapp_cloud_id'   => $whatsAppCloudId,
            'time_show'     => TimeUtil::TIMEZONE_NOT_SHOW,
            'in_black_list' => 0,
        ];

        $contacts = CustomerContactHelper::listContactBySnsId($this->clientId, SnsConstants::SNS_CLIENT_WHATSAPP,  (string)$whatsAppCloudId, $this->userId);
        if (!$contacts) {
            return $result;
        }

        $contactsCustomer = CustomerContactHelper::getArchiveCustomersBySnsIds($this->clientId, SnsConstants::SNS_CLIENT_WHATSAPP, array($whatsAppCloudId), '', $this->userId);
        $targetCustomerId = array_column($contactsCustomer, 'customer_id','sns_id')[$whatsAppCloudId]??0;

        $leadContactMap = [];
        foreach ($contacts as $contact) {
            $key = $contact['sns_id'] . '_' . $contact['user_id'];
            $leadContactMap[$key] = $contact['lead_id'];
        }
        $key = $whatsAppCloudId . '_' . $this->userId;
        $targetLeadId = $leadContactMap[$key] ?? 0;

        $user = \User::getUserObject($this->userId);
        if (!empty($targetCustomerId)&&($customer = new Customer($this->clientId))->loadById($targetCustomerId)->isExist()) {
            $company = new Company($this->clientId, $customer->company_id);
            $company->setOperatorUserId($this->userId);
            if($company->isExist()){
                $itemUserIds = is_array($company->user_id) ? $company->user_id :  PgsqlUtil::trimArray($company->user_id);
                $itemPoolId = $company->pool_id;
                $cardType = (new AccessService($this->clientId, $this->userId))
                    ->setCompanyInfoData($this->clientId, $itemUserIds, $itemPoolId, $company->scope_user_ids)
                    ->cardType($directOwner);

                $company->getFormatter()->cardInfoSettingByType($cardType, $customer);
                $company->getFormatter()->setShowCaptureCard(true);
                $company->getFormatter()->setShowAlibabaChatInfo(true);
                $company->getFormatter()->setShowAlibabaStoreInfo(true);
                $result = array_merge($result, $company->getAttributes());

                if(!empty($customer->email)){
                    $identity = new Identity($this->clientId);
                    $identity->loadByEmail($this->clientId, $customer->email);
                }

                $result['email_identity_id'] = $identity->identity_id ?? 0;

                $result['id'] = $company->company_id;
                $result['customer_id'] = $targetCustomerId;
                $result['name'] = $company->name;
                $result['email'] = $customer->email;
                $result['card_type'] = $cardType;
                $result['is_edit'] = $company->isEditable($user);
                $result['time_show'] = empty($result['card_type']) || $result['card_type'] == EmailIdentity::CARD_TYPE_STRANGER ?
                    TimeUtil::TIMEZONE_NOT_SHOW : TimeUtil::TIMEZONE_SHOW;

                return $result;
            }
        }

        if(empty($targetLeadId)||$result['card_type']!=EmailIdentity::CARD_TYPE_STRANGER){
            return $result;
        }

        $lead = new Lead($this->clientId);
        $lead->loadById($targetLeadId);
        $accessService = new LeadAccessService($this->clientId, $this->userId);
        $accessService->setLeadInfoData($this->clientId, $lead->user_id, $lead->scope_user_ids);
        $result['card_type'] = $accessService->cardType($directOwner);
        $result['lead_id'] = $result['card_type'] != EmailIdentity::CARD_TYPE_STRANGER ? $targetLeadId : 0;

        return $result;
    }

    /**
     * 通过TM buyerAccountId & sellerAccountId 验证
     *
     * @param $buyerAccountId
     * @param bool $directOwner
     * @param $storeId
     * @return array
     * @throws OrmException
     */
    public function identifyTM($buyerAccountId, bool $directOwner = true, $storeId = 0)
    {
    
        $result = [
            'id'                     => 0,
            'name'                   => '',
            'email'                  => '',
            'card_type'              => EmailIdentity::CARD_TYPE_STRANGER,
            'tm_buyer_account_id'    => $buyerAccountId,
            'store_id'               => $storeId,
            'time_show'              => TimeUtil::TIMEZONE_NOT_SHOW,
            'in_black_list'          => 0,
            'current_store_flag'     => false,
            'visitor_marketing_flag' => false,
            'store_name'             => '',
        ];

        if(!is_numeric($buyerAccountId)){
            throw new \RuntimeException('identifier 必须是一个数字');
        }
    
        if (!empty($storeId)) {
        
            $storeInfo = \common\library\alibaba\cache\AlibabaStoreCacheableRepo::instance($this->clientId)->findOneByConditions(['store_id' => $storeId, 'client_id' => $this->clientId]);
        
            $result['store_name'] = empty($storeInfo) ? '' : ($storeInfo['store_alias'] ?: $storeInfo['store_name'] ?? '');
        }
        
        $service = new TMAlibabaCustomerService($this->clientId, $this->userId);

        // 通过 customer_relation 表查询customer信息
        $relation = $service->getRelationByStoreAndBuyer($storeId, $buyerAccountId, $directOwner);

        $targetCustomerId = 0;
        if(!empty($relation)){
            $targetCustomerId = $relation['customer_id'];
            if($relation['store_id'] == $storeId){
                $result['current_store_flag'] = true;
            }
        }

        // 判断线索情况
        if (empty($targetCustomerId)) {
            $result =  $this->getLeadCard($result, $storeId, $buyerAccountId, $directOwner);
        }else{
            // 获取客户卡片
            $result = $this->getCompanyCardByCustomerId($result, $targetCustomerId, $directOwner);
        }

        // 陌生人判断是否能获取到ali customer信息
        if ($result['card_type'] ==  EmailIdentity::CARD_TYPE_STRANGER) {
            //陌生人检查是否来自潜客运营
            $result['visitor_marketing_flag'] = $service->checkFromVisitorMarketing($storeId,$buyerAccountId);

            $aliCustomerInfo = $service->getAliCustomerInfo($storeId, $buyerAccountId);
            $result['ali_customer_info'] = [
                'store_id'               => $storeId,
                'ali_customer_id'        => $aliCustomerInfo['alibaba_customer_id'] ?? 0,
                'ali_company_id'         => $aliCustomerInfo['alibaba_company_id'] ?? 0,
                'contact_info_allowable' => ($aliCustomerInfo['basic_info_allowable']??0) && ($aliCustomerInfo['contact_info_allowable']??0) ? 1 : 0,
                'company_name'           => $aliCustomerInfo['company_name'] ?? '',
                'full_fax'               => $aliCustomerInfo['full_fax'] ?? '',
                'homepage'               => $aliCustomerInfo['homepage'] ?? '',
                'country'                => $aliCustomerInfo['country'] ?? '',
                'address'                => $aliCustomerInfo['address'] ?? '',
                'trail_status'           => $aliCustomerInfo['trail_status'] ?? '',
                'annual_procurement'     => $aliCustomerInfo['annual_procurement'] ?? 0,
                'intention_level'        => $aliCustomerInfo['intention_level'] ?? 0,
                'remark'                 => $aliCustomerInfo['remark'] ?? '',
                'name'                   => $aliCustomerInfo['name'] ?? '',
                'gender'                 => $aliCustomerInfo['gender'] ?? 0,
                'avatar_url'             => $aliCustomerInfo['avatar_url'] ?? '',
                'email'                  => $aliCustomerInfo['email'] ?? '',
                'tel_list'               => $aliCustomerInfo['tel_list'] ?? [],
                'contact'                => $aliCustomerInfo['contact'] ?? [],
                'post'                   => $aliCustomerInfo['post'] ?? '',
            ];
        }

        return $result;
    }


    /**
     * 通过企业微信 buyerAccountId & sellerAccountId 验证
     *
     *  $card = call_user_func($identityMap[$platform], $identifier, $directOwner, $channelId);
     *
     * @param $wecomUserId   外部联系人
     * @param bool $directOwner
     * @param $storeId
     * @return array
     * @throws OrmException
     */
    public function identifyWecom($buyerAccountId, bool $directOwner = true, $corpId = 0)
    {

        $result = [
            'id'                    => 0,
            'name'                  => '',
            'email'                 => '',
            'card_type'             => EmailIdentity::CARD_TYPE_STRANGER,
            'tm_buyer_account_id'   => $buyerAccountId,
            'store_id'              => $corpId,
            'time_show'             => TimeUtil::TIMEZONE_NOT_SHOW,
            'in_black_list'         => 0,
            'current_store_flag'    => false,
            'visitor_marketing_flag' => false,
        ];

        $service = new WecomCustomerService($this->clientId, $this->userId);

        // 通过 customer_relation 表查询customer信息
        $relation = $service->getRelationByCorpAndBuyer($corpId, $buyerAccountId, $directOwner);

        $targetCustomerId = 0;
        if(!empty($relation)){
            $targetCustomerId = $relation['customer_id'];
            if($relation['corpid'] == $corpId){
                $result['current_store_flag'] = true;
            }
        }

        $result = $this->getCompanyCardByCustomerId($result, $targetCustomerId, $directOwner);

        // 陌生人判断是否能获取到外部联系人信息
        if ($result['card_type'] ==  EmailIdentity::CARD_TYPE_STRANGER) {
            $wecomAccountService = new \common\library\wecom\oauth\WecomAccountService($this->clientId, $this->userId);
            $data = $wecomAccountService->getExternalUserInfo($buyerAccountId);

            // 访客信息
            $result['visitorinfo'] = $data;
        }

        return $result;


    }

    /**
     * 查询Facebook访客关联的联系人信息
     *
     * @param int $identifier Facebook访客ID
     * @param bool $directOwner
     * @param int $facebookPageId Facebook公共主页ID
     * @return array
     * @throws OrmException|\ProcessException
     */
    public function identifyFacebook(int $identifier, bool $directOwner, int $facebookPageId): array
    {
        $result = [
            'id'            => 0,
            'name'          => '',
            'email'         => '',
            'card_type'     => EmailIdentity::CARD_TYPE_STRANGER,
            'facebook_page_id'   => $facebookPageId,
            'facebook_page_user_id'   => $identifier,
            'time_show'     => TimeUtil::TIMEZONE_NOT_SHOW,
            'in_black_list' => 0,
        ];

        $contactService = new CustomerContactService($this->clientId, $this->userId);
        $contact = $contactService->getFacebookArchiveCustomerInfo($facebookPageId, $identifier);

        // 查询关联的联系人信息
        $targetCustomerId = $contact->customer_id;
        if ($targetCustomerId) {
            $result = $this->getCompanyCardByCustomerId($result, $targetCustomerId, $directOwner);
        }
        // facebook访客信息
        $result['visitor'] = \ArrayUtil::columns(
            ['timezone', 'email', 'tel_list', 'sns_nickname', 'message_sync_time'],
            $contact->getAttributes()
        );

        return $result;
    }

    /**
     * 查询Instagram访客关联的联系人信息
     *
     * @param int $identifier Instagram访客ID
     * @param bool $directOwner
     * @param int $instagramAccountId Instagram账号ID
     * @return array
     * @throws OrmException|\ProcessException
     */
    public function identifyInstagram(int $identifier, bool $directOwner, int $instagramAccountId): array
    {
        $result = [
            'id'            => 0,
            'name'          => '',
            'email'         => '',
            'card_type'     => EmailIdentity::CARD_TYPE_STRANGER,
            'instagram_account_id'   => $instagramAccountId,
            'instagram_account_user_id'   => $identifier,
            'time_show'     => TimeUtil::TIMEZONE_NOT_SHOW,
            'in_black_list' => 0,
        ];

        $contactService = new CustomerContactService($this->clientId, $this->userId);
        $contact = $contactService->getFacebookArchiveCustomerInfo($instagramAccountId, $identifier, [],SnsConstants::SNS_CLIENT_INSTAGRAM, Constant::CHANNEL_TYPE_INSTAGRAM);

        // 查询关联的联系人信息
        $targetCustomerId = $contact->customer_id;
        if ($targetCustomerId) {
            $result = $this->getCompanyCardByCustomerId($result, $targetCustomerId, $directOwner);
        }

        // instagram访客信息
        $result['visitor'] = \ArrayUtil::columns(
            ['timezone', 'email', 'tel_list', 'sns_nickname', 'message_sync_time'],
            $contact->getAttributes()
        );

        return $result;
    }

    /**
     * 通过WhatsAppBusiness验证
     * @param $whatsAppBusinessId
     * @param bool $directOwner
     * @return array
     */
    public function identifyWhatsAppBusiness($whatsAppBusinessId, bool $directOwner = true): array
    {
        $result = [
            'id'            => 0,
            'name'          => '',
            'email'         => '',
            'card_type'     => EmailIdentity::CARD_TYPE_STRANGER,
            'whatsapp_business_id'   => $whatsAppBusinessId,
            'time_show'     => TimeUtil::TIMEZONE_NOT_SHOW,
            'in_black_list' => 0,
        ];

        $contacts = CustomerContactHelper::getArchiveCustomersBySnsId($this->clientId, SnsConstants::SNS_CLIENT_WHATSAPP_BUSINESS, (string)$whatsAppBusinessId);
        $userIds = array_reduce($contacts, function ($res, $item) {
            $userIds = is_array($item['user_id']) ? $item['user_id'] : PgsqlUtil::trimArray($item['user_id']);
            return array_merge($res, $userIds);
        }, []);
        $scopeUserIds = array_reduce($contacts, function ($res, $item) {
            $ids = is_array($item['scope_user_ids']) ? $item['scope_user_ids'] : PgsqlUtil::trimArray($item['scope_user_ids']);
            return array_merge($res, $ids);
        }, []);
        $poolIds = array_column($contacts, 'pool_id');
        if (!$contacts) {
            return $result;
        }

        $cardType = (new AccessService($this->clientId, $this->userId))
            ->setCompanyInfoData($this->clientId, $userIds, $poolIds, $scopeUserIds)
            ->cardType($directOwner);

        foreach ($contacts as $contact) {
            $itemUserIds = is_array($contact['user_id']) ? $contact['user_id'] : PgsqlUtil::trimArray($contact['user_id']);
            $itemScopeUsers = is_array($contact['scope_user_ids']) ? $contact['scope_user_ids'] : PgsqlUtil::trimArray($contact['scope_user_ids']);
            $itemPoolId = $contact['pool_id'];
            $itemCardType = (new AccessService($this->clientId, $this->userId))
                ->setCompanyInfoData($this->clientId, $itemUserIds, $itemPoolId, $itemScopeUsers)
                ->cardType($directOwner);
            if ($cardType != $itemCardType) continue;
            $targetCustomerId = $contact['customer_id'];
            break;
        }
        if (empty($targetCustomerId)) {
            return $result;
        }

        $customer = new Customer($this->clientId);
        $customer->loadById($targetCustomerId);
        if (!$customer->isExist()) {
            return $result;
        }

        $company = new Company($this->clientId, $customer->company_id);
        $company->setOperatorUserId($this->userId);
        if (!$company->isExist()) {
            return $result;
        }

        $company->getFormatter()->cardInfoSettingByType($cardType, $customer);
        $company->getFormatter()->setShowCaptureCard(true);
        $company->getFormatter()->setShowAlibabaChatInfo(true);
        $company->getFormatter()->setShowAlibabaStoreInfo(true);
        $result = array_merge($result, $company->getAttributes());

        if(!empty($customer->email)){
            $identity = new Identity($this->clientId);
            $identity->loadByEmail($this->clientId, $customer->email);
        }
        $result['email_identity_id'] = $identity->identity_id ?? 0;

        $result['id'] = $company->company_id;
        $result['name'] = $company->name;
        $result['email'] = $customer->email;
        $result['card_type'] = $cardType;
        $result['time_show'] = empty($result['card_type']) || $result['card_type'] == EmailIdentity::CARD_TYPE_STRANGER ?
            TimeUtil::TIMEZONE_NOT_SHOW : TimeUtil::TIMEZONE_SHOW;

        return $result;
    }

    /**
     * 查询独立站访客信息
     *
     * @param $identifyId
     * @return array
     */
    public function identifyVisitorMarketing($identifyId, bool $directOwner, $channelOpenId)
    {
        $result = [
            'visitor_id'            => '',
            'visitor_name'          => '',
            'second_name'           => '',
            'card_type'             => EmailIdentity::CARD_TYPE_STRANGER,
            'visitor_identify_id'   => $identifyId,
            'time_show'             => TimeUtil::TIMEZONE_NOT_SHOW,
            'in_black_list'         => 0,
            'user_list'             => [],
            'source_detail'         => [],
            'location'              => [],
            'origin_ip'             => '',
        ];

        $visitorService = new visitorMarketingService($this->clientId, $identifyId, $channelOpenId);
        $data = $visitorService->getVisitorInfo();

        if(empty($data)){
            return $result;
        }
        // 访客数据 site_url site_name 不准确，shop站点 以 aim_language表为准
        $cmsSiteInfo = (new \common\library\google_ads\ga\GaSiteCacheableRepo())->findOneByConditions(['site_id' => $channelOpenId, 'client_id' => $this->clientId]);
        if (!empty($cmsSiteInfo)){
            $cmsSiteId = $cmsSiteInfo['cms_site_id'] ?? 0;
            $siteType = $cmsSiteInfo['site_type'] ?? 0;
            if (!empty($cmsSiteId) && $siteType == 3){
                $language = AimsLanguage::model()->find('client_id=:client_id AND site_id=:site_id', [':client_id' => $this->clientId, ':site_id' => $cmsSiteId]);
                if (!empty($language)){
                    $data['site_url'] = $language['siteurl'];
                    $data['site_name'] = $language['sitetitle'];
                }
            }
        }

        return $this->formatVisitorInfo($result, $data);

    }
    /**
     * @param $result
     * @param $data
     * @return array
     */
    public function formatVisitorInfo($result, $data)
    {
        if(!empty($data['user_list'])){
            $listObj = new \common\library\account\UserList();
            $listObj->setClientId($this->clientId);
            $listObj->setUserIds($data['user_list']);
            $listObj->setFields(['nickname', 'email', 'avatar', 'user_id']);
            $userList = $listObj->find();
        }

        if(!empty($data['from_ip'])){
            $result['origin_ip'] = $data['from_ip'];
            $locationInfo = \common\library\util\IP::getInstance($this->clientId)->find($data['from_ip']);
            $location = [
                'from_ip' => $data['from_ip'],
                'country' => $locationInfo['Country'] ?? '',
                'country_code' => $locationInfo['CountryCode'] ?? '',
            ];
        }
        //访客编号
        if($result['visitor_identify_id']){
            $session = TrackHelper::getSessionByIdentity($this->clientId, $result['visitor_identify_id']);
            $serialNo = $session->serial_no ?? '';
        }

        $result['visitor_id'] = $serialNo ?? '';
        $result['visitor_name'] = $data['visitor_name'] ?? '';
        $result['second_name'] = $data['second_name'] ?? '';
        $result['user_list'] = $userList ?? [];
        $result['location'] = $location ?? [];
        $result['source_detail'] = [
            'site_url' => $data['site_url'] ?? '',
            'site_name' => $data['site_name'] ?? '',
        ];

        return $result;
    }


    /**
     * TM 通过customerId获取客户卡片
     *
     * @param $result
     * @param $targetCustomerId
     * @param $directOwner
     * @return array|mixed
     * @throws OrmException
     */
    protected function getCompanyCardByCustomerId(&$result, $targetCustomerId, $directOwner = true)
    {
        // 获取联系人信息
        $customer = new Customer($this->clientId);
        $customer->loadById($targetCustomerId);
        if (!$customer->isExist()) {
            return $result;
        }

        // 获取客户信息
        $company = new Company($this->clientId, $customer->company_id);
        $company->setOperatorUserId($this->userId);
        if ($company->isExist()) {

            // 判断卡片类型
            $cardType = (new AccessService($this->clientId, $this->userId))
                ->setCompanyInfoData($this->clientId, $company->user_id, $company->pool_id, $company->scope_user_ids)
                ->cardType($directOwner);

            $company->getFormatter()->cardInfoSettingByType($cardType, $customer);
            $company->getFormatter()->setShowCaptureCard(true);
            $company->getFormatter()->setShowAlibabaChatInfo(true);
            $company->getFormatter()->setShowAlibabaStoreInfo(true);
            $result = array_merge($result, $company->getAttributes());

            if(!empty($customer->email)){
                $identity = new Identity($this->clientId);
                $identity->loadByEmail($this->clientId, $customer->email);
            }
            $result['email_identity_id'] = $identity->identity_id ?? 0;

            $result['id'] = $company->company_id;
            $result['name'] = $company->name;
            $result['serial_id'] = $company->serial_id;
            $result['email'] = $customer->email;
            $result['card_type'] = $cardType;
            $result['time_show'] = empty($result['card_type']) || $result['card_type'] == EmailIdentity::CARD_TYPE_STRANGER ?
                TimeUtil::TIMEZONE_NOT_SHOW : TimeUtil::TIMEZONE_SHOW;

            return $result;
        }

        return $result;
    }


    /**
     * TM 获取线索卡片
     *
     * @param $result
     * @param $storeId
     * @param $buyerAccountId
     * @param bool $directOwner
     * @return array
     * @throws \ProcessException
     */
    protected function getLeadCard(&$result, $storeId, $buyerAccountId, $directOwner = true)
    {
        // 通过阿里询盘关系获取线索联系人
        $alibabaTradeRelationList = new AlibabaTradeRelationList($this->clientId);
        $alibabaTradeRelationList->setBuyerAccountId($buyerAccountId);
        $alibabaTradeRelationList->setStoreId($storeId);
        $alibabaTradeRelationList->setOrderBy('update_time');
        $alibabaTradeRelationList->setOrder('desc');
        $alibabaTradeRelationList->setShowAll(true);
        $list = $alibabaTradeRelationList->find();

        if (empty($list)) {
            return $result;
        }

        // 获取关联的所有线索联系人
        $leadCustomerList = new LeadCustomerList($this->clientId);
        $leadCustomerList->getFormatter()->setSpecifyFields(['user_id', 'lead_id', 'email', 'name', 'tel_list', 'contact', 'post','customer_id', 'order_time']);
        $leadCustomerList->setCustomerId(array_column($list, 'lead_customer_id'));
        $leadCustomerList = $leadCustomerList->find();

        if (empty($leadCustomerList)) {
            return $result;
        }

        $leadCustomerList = \Util::array_sort($leadCustomerList, 'order_time');

        // 判断卡片类型
        $accessService = new LeadAccessService($this->clientId, $this->userId);
        $allLeadUserIds = [];
        foreach ($leadCustomerList as $leadCustomer) {
            $leadCustomerUserIds = $leadCustomer['user_id'];
            $allLeadUserIds = array_merge($allLeadUserIds, $leadCustomerUserIds);
        }
        $accessService->setLeadInfoData($this->clientId, $allLeadUserIds);
        $cardType = $accessService->cardType($directOwner);

        // 根据卡片类型返回对应线索信息
        $leadList = new LeadList($this->userId);
        $leadList->setShowAllStatusFlag(true);
        $leadList->setLeadId(array_column($list, 'lead_id'));
        $leadList->setOrderBy('lead_id');

        $leadList->setOrder('asc');
        if ($cardType == EmailIdentity::CARD_TYPE_LEAD) {
            $leadList->setUserId($this->userId);
        } elseif ($cardType == EmailIdentity::CARD_TYPE_COLLEAGUE_LEAD) {
            $manageAbleUserIds = \common\library\privilege_v3\Helper::getPermissionScopeUser($this->clientId, $this->userId, PrivilegeConstants::PRIVILEGE_CRM_LEAD_VIEW, true);
            $manageAbleUserIds = array_intersect($manageAbleUserIds, $allLeadUserIds);
            if (!empty($manageAbleUserIds)) {
                $leadList->setUserNum([1,2]);
                $leadList->setUserId($manageAbleUserIds);
            }
        } else {
            $leadList->setUserId(null);
        }
        $leadList->getFormatter()->leadCardSetting();

        $leadList->setLimit(1);
        $targetLeadInfo = $leadList->find();
        if (empty($targetLeadInfo)) {
            return $result;
        }
        $targetLeadInfo = $targetLeadInfo[0];
        $result['customer'] = array_column($leadCustomerList, null, 'lead_id')[$targetLeadInfo['lead_id']] ?? [];
        $result['email'] = $result['customer']['email'] ?? '';
        $result = array_merge($result, $targetLeadInfo);

        $result['card_type'] = $cardType;
        $result['time_show'] = empty($result['card_type']) || $result['card_type'] == EmailIdentity::CARD_TYPE_STRANGER ?
            TimeUtil::TIMEZONE_NOT_SHOW : TimeUtil::TIMEZONE_SHOW;

        return $result;
    }

    /**
     * @param $platform
     * @param $identifier
     * @param $storeId
     * @return Identity
     */
    public function findPlatformIdentity($platform, $identifier, $storeId = 0, $autoComplete = false, $createExternal = true): array
    {
        $identity = new Identity($this->clientId);
        $identity->loadByIdentifier($platform, $identifier, $storeId);

        if ($identity->isNew()) {
            $this->createNewPlatformIdentity($platform, $identifier, $storeId, $identity);
        }

        // 关联联系人扩展字段表
        $external = $this->externalAPI->findIdentityExternal($identity->identity_id, false);
        if (($external->isNew() || $external->last_source == 2) && ($autoComplete || $createExternal)) {
            if ($autoComplete) {
                // 从leads-go服务中是否有匹配的联系人。
                $api = new InnerApi('leads_service');
                try {
                    $params = [
                        'email' => $identity->email,
                        'phone_number' => $identity->whatsapp_id,
                    ];
                    $api->setHttpMethod(InnerApi::HTTP_METHOD_POST_JSON);
                    $result = $api->call('getMatchEmployee', $params)['data'] ?? [];
                } catch (\Throwable $exception) {
                    LogUtil::exception($exception);
                }

                $externalAttributes = [
                    'company_name' => $result['company']['company_name'] ?? '',
                    'company_hash_id' => $result['company']['company_hash_id'] ?? '',
                    'country_code' => $result['company']['country_code'] ?? '',
                    'domain' => $result['company']['domain'] ?? '',
                ];
            }
            $external = $this->externalAPI->createNewIdentityExternal($identity->identity_id, $externalAttributes??[]);
        }
        return [$identity, $external];
    }

    public function createNewPlatformIdentity($platform, $identifier, $storeId, Identity &$identity) {
        try {
            if ($platform == Constants::PLATFORM_EMAIL) {
                $locker = new RedLock( \RedisService::cache(), \common\library\chat_assistant\Helper::generateIdentityLockKey($this->clientId.$identifier));
                if (!$locker->lock(1)) {
                    throw new \ProcessException("identity lock fail");
                }
            }
            $identity->bindIdentifier($platform, $identifier, $storeId);
            $identity->create_time = $identity->update_time = date('Y-m-d H:i:s');
            $identity->create();

        } finally {
            if (!empty($locker)) {
                $locker->unlock();
            }
        }

        return $identity;
    }

    /**
     * 获取本地时间
     * @param $platform
     * @param $identifiers
     * @return array|mixed
     * @throws \ProcessException
     */
    public function getLocalTime($platform, $identifiers)
    {
        switch ($platform) {
            case Constants::PLATFORM_EMAIL:
                $result = \common\library\mail\Helper::getEmailsTimezone($this->userId, $this->clientId, $identifiers);
                break;
            case Constants::PLATFORM_WHATSAPP:
                $result = $this->getLocalTimeByWhatsAppIds($identifiers);
                break;
            default:
                throw new \RuntimeException(\Yii::t('common','Platform type error'));
        }

        foreach ($result as $identifier => $timezoneInfo) {
            $email = $platform == Constants::PLATFORM_EMAIL ? $identifier : ($timezoneInfo['mail'] ?? '');
            $result[$identifier] = [
                'timezone'  => $timezoneInfo['timezone'],
                'country'   => $timezoneInfo['country'],
                'time'      => TimeUtil::getLocalTime($timezoneInfo['timezone'], 'now'),
                // 是公共域名邮箱&&时区来源是whois
                'time_show' => (
                    $timezoneInfo['timezone'] === ''
                    && $timezoneInfo['timezone_source'] == TimeUtil::TIMEZONE_SOURCE_WHOIS
                    && in_array(\EmailUtil::getDomain($email), CommonDomain::$commonDomain)
                ) ? TimeUtil::TIMEZONE_NOT_SHOW : TimeUtil::TIMEZONE_SHOW,
            ];
        }

        return $result;
    }

    /**
     * 通过WhatAppId获取本地时间
     * @param array $whatsAppIds
     * @return array|mixed
     * @throws \ProcessException
     */
    public function getLocalTimeByWhatsAppIds(array $whatsAppIds)
    {
        $whatsAppIds = array_unique($whatsAppIds);
        $result = array_fill_keys($whatsAppIds, ['timezone' => '', 'country' => '', 'timezone_source' => TimeUtil::TIMEZONE_SOURCE_UN_KNOW]);

        $customerList = CustomerContactHelper::getCompaniesBySnsIds($this->clientId, SnsConstants::SNS_CLIENT_WHATSAPP, $whatsAppIds);

        $snsMap = array_column($customerList, 'company_id', 'sns_id');
        $companyMap = [];
        foreach ($snsMap as $key => $value) {
            $companyMap[$value][] = $key;
        }

        if (!empty($customerList)) {
            $companyIds = array_column($customerList, 'company_id');
            $companyObj = new CompanyList($this->userId);
            $companyObj->setSkipPrivilege(true);
            $companyObj->setCompanyIds($companyIds);
            $companyObj->setFields(['timezone', 'company_id', 'country', 'main_customer_email']);
            $companyList = $companyObj->find();

            $domainMap = [];
            $result = array_reduce($companyList, function ($itemResult, $item) use ($domainMap, $companyMap) {
                $snsIds = $companyMap[$item['company_id']] ?? [];
                foreach ($snsIds as $snsId) {
                    if ($item['timezone'] !== '') {
                        $itemResult[$snsId]['timezone'] = $item['timezone'];
                        $itemResult[$snsId]['country'] = $item['country'];
                        $itemResult[$snsId]['timezone_source'] = TimeUtil::TIMEZONE_SOURCE_COMPANY;
                        continue;
                    }

                    $domain = \EmailUtil::getDomain($item['main_customer_email']);
                    if (!isset($domainMap[$domain])) {
                        $domainMap[$domain] = TimeUtil::getDomainTimezone($domain);
                    }
                    $itemResult[$snsId]['timezone'] = $domainMap[$domain]['offset'] ?? '';
                    $itemResult[$snsId]['country'] = $domainMap[$domain]['countryCode'] ?? '';
                    $itemResult[$snsId]['mail'] = $item['main_customer_email'];
                    $itemResult[$snsId]['timezone_source'] = TimeUtil::TIMEZONE_SOURCE_WHOIS;
                }
                return $itemResult;
            }, $result);
        }

        return $result;
    }



    /**
     * 通过identity_id数组获取关联信息
     * @param $identityIds
     * @return array
     * @throws OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function getIdentityInfoById($identityIds)
    {

        if (empty($identityIds)){
            return [];
        }

        $filter = new IdentityFilter($this->clientId);
        $filter->identity_id = $identityIds;
        $batch = $filter->find();
        return $batch->getAttributes(['identity_id','whatsapp_id', 'email', 'tm_buyer_account_id', 'store_id' ,'wecom_open_userid' ,'corpid']);
    }


    /**
     * 更新身份信息
     * @param $identityId
     * @param $newData
     * @return void
     */
    public function update($identityId, $newData)
    {
        $identity = new Identity($this->clientId, $identityId);
        if (!$identity->isExist()) {
            throw new \RuntimeException(\Yii::t('common', 'Data does not exist or deleted'));
        }
        isset($newData['flow_id']) && $identity->flow_id = $newData['flow_id'];
        isset($newData['stage_id']) && $identity->stage_id = $newData['stage_id'];
        isset($newData['link_id']) && $identity->link_id = $newData['link_id'];
        $identity->update_time = date('Y-m-d H:i:s');
        $identity->update();

        $identityExt = new IdentityExternalAPI($this->clientId, $this->userId);
        $external = $identityExt->findIdentityExternal($identity->identity_id);
        if (! $external->isNew()) {
            isset($newData['country_code']) && $external->country_code = $newData['country_code'];
            isset($newData['domain']) && $external->domain = $newData['domain'];
            isset($newData['company_name']) && $external->company_name = $newData['company_name'];
            isset($newData['company_hash_id']) && $external->company_hash_id = $newData['company_hash_id'];
            $external->update_time = date('Y-m-d H:i:s');
            $external->update();
        }
    }


    /**
     * 获取身份校验列表
     *
     * @param $platform
     * @param array $identifier
     * @param $storeId
     * @return array
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function getIdentityList($platform, array $identifier, $storeId = 0)
    {

        $filter = new IdentityFilter($this->clientId);

        $identifier = array_unique(array_filter($identifier));

        switch ($platform) {
            case Constants::PLATFORM_EMAIL:
                $identifier = (new Card($this->userId))->processEmails($identifier);
                $filter->email = $identifier;
                break;
            case Constants::PLATFORM_WHATSAPP:
                $filter->whatsapp_id = $identifier;
                break;
            case Constants::PLATFORM_TM:
                $filter->tm_buyer_account_id = $identifier;
                $filter->store_id = $storeId;
                break;
            default:
                throw new RuntimeException('platform error');
        }

        $count = $filter->count();
        $list = [];
        if ($count) {
            $batch = $filter->find();
            $batch->getFormatter()->defaultIdentityInfo();
            $list = $batch->getAttributes();
        }

        return compact('count', 'list');
    }

    /**
     * 获取多个Waba联系人的客户跟进人
     * @param array $snsIds
     * @return array
     */
    public function getCompanyOwnersOfWabaContacts(array $snsIds): array
    {
        if (!$snsIds) {
            return [];
        }
        $customers = CustomerContactHelper::getArchiveCustomersBySnsIds($this->clientId, SnsConstants::SNS_CLIENT_WHATSAPP_BUSINESS, $snsIds);
        $customerGroups = \ArrayUtil::groupBy($customers, 'sns_id');

        $result = [];
        foreach ($snsIds as $snsId) {
            $ownerIds = [];
            if (!empty($customerGroups[$snsId])) {
                $contacts = $customerGroups[$snsId];
                $ownerIds = array_reduce($contacts, function ($res, $item) {
                    $userIds = is_array($item['user_id']) ? $item['user_id'] : PgsqlUtil::trimArray($item['user_id']);
                    return array_merge($res, $userIds);
                }, []);
            }
            $result[] = [
                'sns_id' => $snsId,
                'user_ids' => $ownerIds,
            ];
        }
        return $result;
    }

    public function findBatchIdentity(int $platform, array $identities) {
        if (empty($identities)) return [];
        switch ($platform) {
            case Constants::PLATFORM_EMAIL:
                $queryField = 'email';
                array_walk($identities, function (&$identity) {
                    $identity = Util::getPureEmail($identity);
                });
                break;
            case Constants::PLATFORM_WHATSAPP:
                $queryField = 'whatsapp_id';
                break;
            default:
                throw new \RuntimeException(\Yii::t('common','Platform type error'));
        }
        $identityFilter = new IdentityFilter($this->clientId);
        $identityFilter->{$queryField} = new In($identities);
        $identityFilter->select(['identity_id', $queryField]);
        return array_column($identityFilter->rawData(), $queryField, 'identity_id');
    }
}
