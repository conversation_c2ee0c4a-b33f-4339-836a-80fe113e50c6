<?php

namespace common\library\track\event\ames;

use common\library\track\AmesIdentity;

trait AmesIdentityEventTrait
{

    protected $amesCookieId;
    protected $amesUserId;

    public function getIdentity()
    {
        if ($this->identity === null) {
            $this->identity = new AmesIdentity($this->getParams());
            if ($this->amesCookieId) {
                $this->identity->setAmesCookieId($this->amesCookieId);
                $this->identity->setAmesUserId($this->amesUserId ?: 0);
            }
            $this->identity->mark();
        }

        return $this->identity;
    }

    public function getAmesCookieId()
    {
        if (!$this->amesCookieId) {
            $this->amesCookieId =  $this->getIdentity()->getAmesCookieId();
        }

        return $this->amesCookieId;
    }

    public function getAmesUserId()
    {
        if (!$this->amesUserId) {
            $this->amesUserId =  $this->getIdentity()->getAmesUserId();
        }

        return $this->amesUserId;
    }

    public function getSiteSessionId()
    {
        return $this->getAmesCookieId();
    }

}
