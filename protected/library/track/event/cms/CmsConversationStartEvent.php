<?php


namespace common\library\track\event\cms;


use common\library\track\params\CmsConversationParams;

/**
 * @deprecated
 */
class CmsConversationStartEvent extends CmsEvent
{

    public function __construct(CmsConversationParams $params, $siteSessionId)
    {
        $this->siteSessionId = $siteSessionId;
        parent::__construct($params);
    }

    public function needApply()
    {
        return false;
    }
}