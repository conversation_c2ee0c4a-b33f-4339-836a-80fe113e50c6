<?php


namespace common\library\track\event\cms;


use common\library\track\event\TrackEventAbstract;
use common\library\track\params\CmsEventParams;

/**
 * Class CmsGclidEvent
 * @package common\library\track\event\cms
 *
 * @method CmsEventParams getParams()
 */
class CmsGclidEvent extends TrackEventAbstract
{

    use CmsIdentityEventTrait;

    public function __construct($clientId, $gclidList)
    {
        $params = new CmsEventParams($clientId, ['gclidList' => $gclidList], true);
        parent::__construct($params);
    }

    public function getGclidList()
    {
        return $this->getParams()->gclidList;
    }

}