<?php


namespace common\library\track\params;


use common\library\edm\Helper;

class EdmMailClickParams extends EdmMailParams
{

    public $urlId;
    public $repeatSubmitKey;

    public function initInfo()
    {
        $sign = \SecurityUtil::tripleDESDecrypt($this->t, $this->decryptKey);

        if (!$sign || strpos($sign, ',') === false) {
            $this->validatedFailInfo = 'wrong decrypt for ' . $this->t  . ' with ' . $this->decryptKey;
            return false;
        }

        list ($this->urlId, $this->userId) = explode(",", $sign);

        $user = \User::getUserObject($this->userId);
        if ($user->isEmpty()) {
            $this->validatedFailInfo = 'wrong decrypt user ' . $this->userId . ' for ' . $this->t  . ' with ' . $this->decryptKey;
            return false;
        }

        $this->repeatSubmitKey = Helper::TRIPLE_DESC_ENCRYPT_KEY_EDM_URL . '_' . $this->t . '_' . $this->mailId;

        \Yii::app()->cache->executeCommand('DEL', [$this->repeatSubmitKey]);

        $submitCount = \Yii::app()->cache->executeCommand('incr', [$this->repeatSubmitKey]);
        if ($submitCount > 1) //防止短时间内重复提交
        {
            $this->validatedFailInfo = 'attempt too many';
            return false;
        }

        if (!$this->mailId) {
            \Yii::app()->cache->executeCommand('DEL', [$this->repeatSubmitKey]);
            return false;
        }

        \User::setLoginUser($user);
        $edmMail = $this->getMail();
        if ($edmMail->isNew()) {
            \Yii::app()->cache->executeCommand('DEL', [$this->repeatSubmitKey]);
            $this->validatedFailInfo = "edm {$this->mailId} not exist";
            return false;
        }
    }

}