<?php


namespace common\library\track\listener\mail;


use common\library\mail\Mail;
use common\library\mail\MailAnalysisUtil;
use common\library\queue_v2\job\TriggerCallbackJob;
use common\library\queue_v2\QueueConstant;
use common\library\queue_v2\QueueService;
use common\library\risk\service\TrackMailRiskService;
use common\library\setting\user\UserSetting;
use common\library\track\event\mail\MailRead;
use common\library\track\listener\TrackListenerContract;
use common\library\version\Constant;
use common\library\version\MailVersion;
use common\library\todo\TodoConstant;

class TrackMailRead
{

    public function handle(MailRead $mailRead)
    {
        $params = $mailRead->getParams();

        $db = \ProjectActiveRecord::getDbByClientId($params->getClientId());
        if(!$db){
            \LogUtil::info("MailRead DbEmpty client_id:{$params->getClientId()}");
            return false;
        }
        \ProjectActiveRecord::setConnection($db);

        if (!empty($params->ip)) {
            //判断ip是否在263邮箱的反垃圾邮件ip
            $ips = \Yii::app()->params['mail_track_filter_ip']??['**************','*************'];
            if (in_array($params->ip, $ips)) {
                \LogUtil::info("263邮箱的反垃圾邮件ip不追踪");
                return;
            }

            //读取配置
            try {
                $userSetting = new UserSetting($params->getClientId(), $params->userId, UserSetting::MAIL_TRACKING_IP_RANGE);
                $mailTrackIpRange = $userSetting->getValue()?:0;
            } catch (\Exception $e) {
                \LogUtil::error("MailRead UserSetting Error:{$e->getMessage()}");
                $mailTrackIpRange = 0;
            }

            //配置了过滤大陆ip也不追踪
            if ($mailTrackIpRange && $params->getEnCountryName() == 'China') {

                //过滤港澳台
                if (!in_array($params->getCity(), [
                    '中国香港',
                    '中国台湾',
                    '中国澳门'
                ])) {
                    \LogUtil::info("大陆ip不追踪 ip:{$params->ip}}");
                    return;
                }
            }

        }


        $now = date('Y-m-d H:i:s');

        $mail = $params->getMail();
        // 防止java还没更新好mail的folder_id时,命中了timing agent的条件走before save把async task删了的情况
        if (in_array($mail->folder_id, [\Mail::FOLDER_DRAFT_ID, \Mail::FOLDER_HIDDEN_DRAFT_ID])) {
            return;
        }
        $receivers = $mail->getAllReceiverEmails();
        $clientId = $mail->getMailClientId();
        // 邮件解绑后默认归属原用户
        $userId = $mail->getUserId() ?: $params->userId;
        $mailId = $mail->getMailId();
        $userMailId = $mail->getUserMailId();

        $firstView = false;
        // 第一次打开
        if ($mail->open_flag == 0) {
            $mail->open_flag = 1;
            $mail->update(['open_flag']);

            $this->updateOpenCount($mail, $receivers); // customer统计上报

            MailAnalysisUtil::handleOpen($userId, $clientId);

            //线索ai标签更新
            \common\library\lead\Helper::updateOpenMailFlag($clientId, $userId, $receivers);
            $firstView = true;
            //更新触达状态
            \common\library\customer\Helper::updateReachStatus($clientId, $userId, $mailId, $receivers,
                \common\library\customer_v3\customer\orm\Customer::MAIL_REACH_STATUS_OPEN,true,false,false);
        }

        $obj = \MailTrack::model()->find('mail_id=:m', array(':m' => $mailId));
        if ($obj === null) {
            $obj = new \MailTrack();
            $obj->mail_id = $mailId;
            $obj->user_id = $userId;
            $obj->create_time = $now;
            $obj->view_count = 0;
            $obj->client_id = $clientId;

			$obj->user_mail_id = $userMailId;

            try {
                $obj->insert();
            } catch (\Throwable $e) {
                // Outlook客户端会发起并发请求，无法加锁
                if (strpos($e->getMessage(), 'Integrity constraint violation') !== false) {
                    \LogUtil::error($e->getMessage());
                    return false;
                }
                throw $e;
            }

            $mailVersion = new MailVersion($clientId, $userMailId);
            $mailVersion->setMailId($mailId);
            $mailVersion->setType(Constant::MAIL_MODULE_TRACK);
            $mailVersion->add();

            // 标记邮件追踪的待办事项
            \common\library\todo\Helper::pushFeed($clientId, $userId, TodoConstant::OBJECT_TYPE_MAIL,TodoConstant::TODO_TYPE_TRACK_RECORDS_AND_UPDATE_EMAILS, [$mailId]);
        }

        $obj->last_session_id = $mailRead->getSessionId();
        $obj->last_view_time = $mailRead->readTime;
        $obj->last_view_ip = $params->ip;
        $obj->last_view_country_code = $params->getCountryCode();
        $obj->last_view_country = $params->getEnCountryName();
        $obj->last_view_province = $params->getProvince();
        $obj->last_view_city = $params->getCity();
        $obj->last_view_location = '';
        $obj->stat_open_times_flag = $obj->stat_open_flag == 1 ? 1 : 0;
        $obj->stat_open_flag = 1;

        if (!$obj->update(array(
            'last_session_id',
            'last_view_ip',
            'last_view_country_code',
            'last_view_country',
            'last_view_province',
            'last_view_city',
            'last_view_location',
            'last_view_time',
            'stat_open_times_flag',
            'stat_open_flag'
        ))
        ) {
            \LogUtil::error('mail track update fail!  mail id:' . $mailId);
        }

        if ($obj->updateCounters(array('view_count' => 1), 'mail_id=:m', array(':m' => $mailId)) == 0) {
            \LogUtil::error('mail track updateCounters fail!  mail id:' . $mailId);
        }

        $detail = new \MailTrackDetail();
        $detail->mail_id = $mailId;
        $detail->session_id = $obj->last_session_id;
        $detail->user_id = $userId;
        $detail->client_id = $clientId;
        $detail->create_time = $now;
        $detail->view_time = $obj->last_view_time;
        $detail->view_country_code = $obj->last_view_country_code;
        $detail->view_country = $obj->last_view_country;
        $detail->view_province = $obj->last_view_province;
        $detail->view_city = $obj->last_view_city;
        $detail->view_location = $obj->last_view_location;
        $detail->view_ip = $obj->last_view_ip;
        $detail->insert();

        $this->marketingTriggerCallback($mailId,$userId,$clientId, $receivers);

        // 更新java邮箱大表
        foreach ($receivers as $receiver) {
            $receiverParams = array();
            $receiverParams['_id'] = $receiver;
            $receiverParams['lastOpenIp'] = $obj->last_view_ip;
            $receiverParams['lastOpenAddress'] = is_array($params->getLocation()) ? implode(' ',
                $params->getLocation()) : $params->getLocation();
            $receiverParams['openCount'] = 1;
            if ($firstView) {
                $receiverParams['uniqueOpenCount'] = 1;
            }

            \EmailInfo::update($receiverParams);
        }

        //检查风险
        $service = new TrackMailRiskService($mail->mail_id);
        $service->initByTrackMail($mail, $detail);
        $service->check();
    }

    /**
     * @return int
     */
    private function updateOpenCount(Mail $mail, $receivers)
    {
        if (empty($receivers)) {
            return 0;
        }

        $clientId = $mail->getMailClientId();
        $userId = $mail->getUserId();
        $time = time();
        $date = date('Ymd', $time);
        $datetime = date('Y-m-d H:i:s', $time);

        $sqlPrefix = "INSERT INTO `tbl_mail_statistics_day`(`client_id`,`user_id`,`email`,`date`, `open_count`, `create_time`) VALUES ";
        $sqlSuffix = "ON DUPLICATE KEY UPDATE open_count = open_count + 1;";
        $values = [];

        foreach ($receivers as $receiver) {
            $receiver = addslashes($receiver);
            $values[] = "({$clientId}, {$userId}, '{$receiver}', {$date}, 1, '{$datetime}')";
        }

        $sql = $sqlPrefix . implode(',', $values) . $sqlSuffix;

        return \ProjectActiveRecord::getDbByClientId($clientId)->createCommand($sql)->execute();
    }

    /**
     * 邮件已读回调MA处理
     * @param $id
     * @param $user_id
     * @param $client_id
     * @param $contacts
     * @return void
     */
    public function marketingTriggerCallback($id, $user_id, $client_id, $contacts): void
    {
        if (!is_array($contacts) || empty($contacts)) {
            return;
        }
        $triggerCallbackJob = new TriggerCallbackJob([
            'event' =>  \common\library\auto_market\Constant::TRIGGER_EVENT_READ,
            'trigger' =>  \common\library\auto_market\Constant::TYPE_MARKET_EMAIL_EXPOSE,
            'data' => [
                'id' => $id,
                'user_id' => $user_id,
                'client_id' => $client_id,
                'contact' => $contacts[0]
            ],
        ]);
        QueueService::dispatch($triggerCallbackJob);
    }

}
