<?php

namespace common\library\translate\strategy;

use common\library\setting\item\ItemSettingConstant;
use common\library\translate\ItemStrategy;

class OpportunityStageItemStrategy implements ItemStrategy
{
    public function insert($itemParams, $translateMap, &$fp, &$failCount)
    {
        $clientId = $itemParams['client_id'];
        $columnCount = $itemParams['column_count'];
        $indexMap = $itemParams['index_map'];
        $module = $itemParams['module'];
        $referType = $itemParams['refer_type'];

        if (!\common\library\privilege_v3\Helper::hasFunctional($clientId, \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_OPPORTUNITY)) {
            return false;
        }

        $opportunityTranslateMap = array_filter($translateMap, function ($item) {
            return ($item['module'] == \Constants::TYPE_OPPORTUNITY && $item['refer_type'] == ItemSettingConstant::ITEM_TYPE_STAGE);
        });

        $translateMapKeys = array_map(function($item) {
            return $item['refer_id'] . '_' . $item['refer_sub_type'] . '_' . $item['map_key'];
        }, $opportunityTranslateMap);

        $newTranslateKeysMap = array_combine($translateMapKeys, $opportunityTranslateMap);

        //商机流程
        $api = new \common\library\setting\library\stage\StageApi($clientId);
        $stageList = $api->listAll();

        foreach ($stageList as $stage) {
            $key = $stage['stage_id'].'_'.\common\library\translate\Helper::TRANSLATE_SUB_REFER_TYPE_NAME.'_'.$stage['name'];
            $row = array_fill(0, $columnCount, '');
            $row[$indexMap['refer_id']] = "'".($stage['stage_id'] ?? 0);
            $row[$indexMap['map_key']] = $stage['name'] ?? '';
            $row[$indexMap['module']] = \common\library\translate\Helper::MODULE_NAME_MAP[$module] ?? '';
            $row[$indexMap['refer_type']] = \common\library\translate\Helper::REFER_TYPE_NAME_MAP[$referType] ?? '';
            $row[$indexMap['refer_sub_type']] = \common\library\translate\Helper::REFER_SUB_TYPE_NAME_MAP[\common\library\translate\Helper::TRANSLATE_SUB_REFER_TYPE_NAME];
            $row[$indexMap['map_value']] = $newTranslateKeysMap[$key]['map_value'] ?? '';
            $row[$indexMap['remark']] = $newTranslateKeysMap[$key]['remark'] ?? '';
            $res = fputcsv($fp, $row);
            if ($res == false) {
                $failCount++;
            }
        }
    }
}
