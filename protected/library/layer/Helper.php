<?php

namespace common\library\layer;

use common\library\layer\LayerComponent\ContactInfo;
use common\library\layer\LayerComponent\KeyInfo;
use common\library\layer\LayerConfig\ContactInfoConfig;
use common\library\layer\LayerConfig\KeyInfoConfig;
use common\library\layer\LayerIntegrityComponent\BaseInfoIntegrity;
use common\library\layer\LayerIntegrityComponent\KeyIntegrity;
use common\library\layer\LayerIntegrityConfig\BaseInfoIntegrityConfig;
use common\library\layer\LayerIntegrityConfig\KeyIntegrityConfig;

class Helper
{
    /**
     * 获取分层完整度信息
     * https://xmkm.yuque.com/crn8rp/xpcimb/sw67ste9fe7o88sc#%20
     * @param $clientId
     * @param $module
     * @param array $objIds
     * @return array
     */
    public static function getLayerIntegrityInfo($clientId, $module, array $objIds = []): array
    {
        $objectIntegrity = [];
        $keyIntegrity = new KeyIntegrity($clientId, $module, new KeyIntegrityConfig(\common\library\layer\LayerConstants::LAYER_INTEGRITY_RULE['key_field_rule'][$module] ?? []), $objIds);
        $objectKeyIntegrity = $keyIntegrity->calculateIntegrity();

        $baseInfoIntegrity = new BaseInfoIntegrity($clientId, $module, new BaseInfoIntegrityConfig(\common\library\layer\LayerConstants::LAYER_INTEGRITY_RULE['base_info_field_rule'][$module] ?? []), $objIds);
        $objectBaseInfoIntegrity = $baseInfoIntegrity->calculateIntegrity();
        foreach ($objIds as $objectId) {
            if (isset($objectKeyIntegrity[$objectId]) && $objectKeyIntegrity[$objectId] > 0) {
                $objectIntegrity[$objectId] = ($objectKeyIntegrity[$objectId]['integrity'] ?? 0) * ($objectBaseInfoIntegrity[$objectId]['integrity'] ?? 0);
            } else {
                $objectIntegrity[$objectId] = -1;
            }
        }
        return $objectIntegrity;
    }


    /**
     * 获取AI画像的完整度信息
     * @param $clientId
     * @param $module
     * @param array $objIds
     * @return array
     */
    public static function getAIIntegrityInfo($clientId, $module, array $objIds = []): array
    {
        $baseInfoIntegrity = new BaseInfoIntegrity($clientId, $module, new BaseInfoIntegrityConfig(\common\library\layer\LayerConstants::AI_INTEGRITY_RULE['base_info_field_rule'][$module] ?? []), $objIds);
        return $baseInfoIntegrity->calculateIntegrity();
    }


    /**
     * 获取分层信息
     * @param $clientId
     * @param $module
     * @param array $objIds
     * @return array
     */
    public static function getObjLayer($clientId, $module, array $objIds = []): array
    {
        $keyInfo = new KeyInfo($clientId, $module, new KeyInfoConfig(\common\library\layer\LayerConstants::LAYER_RULE['key_info_rule'] ?? []), $objIds);
            $objKeyInfoLayer = $keyInfo->calculateLayer();

        $contactInfo = new ContactInfo($clientId, $module, new ContactInfoConfig(\common\library\layer\LayerConstants::LAYER_RULE['contact_info_rule'][$module] ?? []), $objIds);
        $objContactInfoLayer = $contactInfo->calculateLayer();

        $objLayer = [];
        foreach ($objIds as $objId) {
            if (!isset($objKeyInfoLayer[$objId]['layer'])) {
                $objLayer[$objId] = \common\library\lead\Constant::LAYER_ID_DEFAULT;
            } elseif ($objKeyInfoLayer[$objId]['layer'] == 0) {
                $objLayer[$objId] = \common\library\lead\Constant::LAYER_ID_NO_CONTACT;
            } else {
                if (!isset($objContactInfoLayer[$objId]['layer'])) {
                    $objLayer[$objId] = \common\library\lead\Constant::LAYER_ID_COMMON;
                } else {
                    $objLayer[$objId] = $objContactInfoLayer[$objId]['layer'];
                }
            }
        }

        return $objLayer;
    }

    /**
     * @param $clientId
     * @param $module
     * @param array $objIds
     * @return array
     * @deprecated
     * 后续分层信息请调用$this->getObjLayer()
     */
    public static function getLeadLayer($clientId, array $leadIds = [])
    {
        $keyInfoRule = [
            \Constants::TYPE_LEAD => [
                'homepage' => 1,
            ],
            \Constants::TYPE_LEAD_CUSTOMER => [
                'email' => 1,
                'contact' => 1,
                'tel_list' => 1,
            ]
        ];
        $keyInfo = new KeyInfo($clientId, \Constants::TYPE_LEAD, new KeyInfoConfig($keyInfoRule), $leadIds);
        $leadKeyInfoLayer = $keyInfo->calculateLayer();

        //优先级高的排上面
        $contactInfoRule = [
            [
                'layer_id' => \common\library\lead\Constant::LAYER_ID_PREMIUM,
                'operator' => 'in',
                'fields' => [
                    'origin_list' => [
                        \common\library\setting\library\origin\Origin::SYS_ORIGIN_ALIBABA_TM,
                        \common\library\setting\library\origin\Origin::SYS_ORIGIN_GLOBAL_MARKET,
                        \common\library\setting\library\origin\Origin::SYS_ORIGIN_GLOBAL_SOURCES,
                        \common\library\setting\library\origin\Origin::SYS_ORIGIN_MADE_IN_CHINA,
                        \common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_OKKI_SHOP,
                        \common\library\setting\library\origin\Origin::SYS_ORIGIN_WEBSITE
                    ]
                ]
            ],
            [
                'layer_id' => \common\library\lead\Constant::LAYER_ID_POTENTIAL,
                'operator' => 'or',
                'fields' => [
                    'open_edm_flag',
                    'open_mail_flag',
                    'reply_mail_flag',
                    'reply_edm_flag',
                    'latest_receive_ali_tm_time',
                ],
                'value' => 1,
            ],
            [
                'layer_id' => \common\library\lead\Constant::LAYER_ID_COMMON,
                'operator' => 'and',
                'fields' => [
                    'open_edm_flag',
                    'open_mail_flag',
                    'reply_mail_flag',
                    'reply_edm_flag',
                    'latest_receive_ali_tm_time',
                ],
                'value' => 0,
            ],
        ];

        $contactInfo = new ContactInfo($clientId, \Constants::TYPE_LEAD, new ContactInfoConfig($contactInfoRule), $leadIds);
        $leadContactInfoLayer = $contactInfo->calculateLayer();


        $leadLayer = [];
        foreach ($leadIds as $leadId) {
            if (!isset($leadKeyInfoLayer[$leadId]['layer'])) {
                $leadLayer[$leadId] = \common\library\lead\Constant::LAYER_ID_DEFAULT;
            } elseif ($leadKeyInfoLayer[$leadId]['layer'] == 0) {
                $leadLayer[$leadId] = \common\library\lead\Constant::LAYER_ID_NO_CONTACT;
            } else {
                if (!isset($leadContactInfoLayer[$leadId]['layer'])) {
                    $leadLayer[$leadId] = \common\library\lead\Constant::LAYER_ID_COMMON;
                } else {
                    $leadLayer[$leadId] = $leadContactInfoLayer[$leadId]['layer'];
                }
            }
        }

        return $leadLayer;
    }

    /**
     * 查询指定日期的分层快照数据
     * @param int $clientId
     * @param array $userIds
     * @param string $snapshotDateTime
     * @param int $module
     * @return mixed
     * @throws \ProcessException
     */
    public static function queryLayerRecordStatistics(int $clientId, int $opUserId, string $snapshotDateTime, int $module)
    {
        $db = \PgActiveRecord::getDbByClientId($clientId);

        $table = 'tbl_layer_record';

        $snapshotDate = date('Y-m-d', strtotime($snapshotDateTime)) ?? date('Y-m-d');

        $params = [
            ':module' => $module,
            ':client_id' => $clientId,
            ':user_id' => $opUserId,
            ':create_date' => $snapshotDate,
        ];

        $where = " module = :module AND client_id = :client_id AND user_id = :user_id AND create_date = :create_date ";

        $sql = " SELECT  layer_id, layer_count AS total_count FROM $table WHERE $where ORDER BY layer_id";

        return $db->createCommand($sql)->queryAll(true, $params);

    }

    public static function updateLeadLayer($clientId, $leadIds = [])
    {
        if (empty($leadIds)) {
            return;
        }
        $leadLayer = self::getObjLayer($clientId, \Constants::TYPE_LEAD, $leadIds);
        foreach ($leadLayer as $leadId => $layer_id) {
            if (empty($layer_id)) {
                continue;
            }
            $updateSqlArray[] = "update tbl_lead set layer_id =$layer_id where lead_id={$leadId}";
        }

        if (!empty($updateSqlArray)) {
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $updateSql = implode(';', $updateSqlArray);
            $db->createCommand($updateSql)->execute();
        }
    }

    public static function updateCompanyAIIntegrity($clientId, $companyIds = []): void
    {
        if (empty($companyIds)) {
            return;
        }

        $module = \Constants::TYPE_COMPANY;
        $analyze_type = \common\library\ai_portrait_analysis\Constant::ANALYZE_TYPE_BASIC_INFO;
        $status = \common\library\ai_portrait_analysis\Constant::ANALYZE_STATUS_COMPLETE;
        $sort = \common\library\ai_portrait_analysis\Constant::ANALYZE_TYPE_SORT_MAP[$analyze_type] ?? 0;
        $time = date('Y-m-d H:m:s');

        $companyAIIntegrityInfo = self::getAIIntegrityInfo($clientId, $module , $companyIds);

        $sqlItem = [];
        foreach ($companyAIIntegrityInfo as $aiIntegrityInfo) {
            if (!empty($aiIntegrityInfo['refer_id'])) {
                $tags = [];
                if (!empty($aiIntegrityInfo['empty_fields'])) {
                    if (!empty($aiIntegrityInfo['empty_fields'][\Constants::TYPE_COMPANY])) {
                        $tags['basic_info'][] = [
                            'tag_name' => '公司信息缺失',
                            'tag_type' => '基础资料',
                            'tag_value' => '公司信息缺失',
                            'tag_value_reason' => '当存在公司任一字段信息缺失时，展示对应的标签。'
                        ];
                    }

                    if (!empty($aiIntegrityInfo['empty_fields'][\Constants::TYPE_CUSTOMER])) {
                        $tags['basic_info'][] = [
                            'tag_name' => '联系人信息缺失',
                            'tag_type' => '基础资料',
                            'tag_value' => '联系人信息缺失',
                            'tag_value_reason' => '当存在联系人任一字段信息缺失时，展示对应的标签。'
                        ];
                    }
                }

                $analysis_id = \ProjectActiveRecord::produceAutoIncrementId();
                $analyze_result = json_encode([
                   'integrity_percent' => $aiIntegrityInfo['integrity'] ?? 0,
                   'empty_fields' => $aiIntegrityInfo['empty_fields'] ?? []
                ]);
                $tags = json_encode($tags);
                $sqlItem[] .= "({$analysis_id},{$clientId}, {$module},{$aiIntegrityInfo['refer_id']},{$analyze_type},'{$analyze_result}', '{$tags}', {$sort}, {$status}, '{$time}', '{$time}')";
            }
        }

        if (!empty($sqlItem)) {
            $sqlItemStr = implode(',', $sqlItem);
            $insertSql = "insert into tbl_ai_portrait_analysis(analysis_id,client_id, module, refer_id, analyze_type, analyze_result, tags, sort, status, create_time, update_time) values {$sqlItemStr} ON CONFLICT (client_id, refer_id, analyze_type, module) DO UPDATE 
              SET analyze_result = EXCLUDED.analyze_result,tags =EXCLUDED.tags,  update_time=EXCLUDED.update_time, status=EXCLUDED.status";
            \PgActiveRecord::getDbByClientId($clientId)->createCommand($insertSql)->execute();
        }
    }
}