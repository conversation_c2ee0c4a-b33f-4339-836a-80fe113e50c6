<?php
/**
 * Created by PhpStorm.
 * User: june
 * Date: 2022/02/08
 * Time: 5:00 PM
 */

namespace common\library\alibaba\sales_race;

use common\library\custom_field\CustomFieldService;
use common\library\invoice\status\InvoiceStatusService;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\models\client\CustomField;

class InitConfig
{
    protected $clientId;
    protected $adminUserId;

    public function __construct($clientId)
    {
        $this->clientId = $clientId;
        $this->adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        \User::setLoginUserById($this->adminUserId);
    }

    public function run()
    {
        $this->actionAssignFunction();

        $this->actionAddExternalField();

        $this->actionAddStatus();

        $this->actionAddPerformanceRule();

        $this->actionAddRaceRole();

    }

    protected function actionAssignFunction()
    {
        PrivilegeService::getInstance($this->clientId)->assignFunction(PrivilegeConstants::FUNCTIONAL_ALI_SALES_RACE);
        $functionalArr = [
            //PrivilegeConstants::FUNCTIONAL_DX,                        // discovery
            //PrivilegeConstants::FUNCTIONAL_DX_PRODUCT_SEARCH,        // DX 产品搜索
            //PrivilegeConstants::FUNCTIONAL_DX_COMPANY_SEARCH,        // 公司搜索
            //PrivilegeConstants::FUNCTIONAL_DX_MINING,                        // DX 发掘
            //PrivilegeConstants::FUNCTIONAL_DX_FOLLOW,                        // DX 我的关注
            //PrivilegeConstants::FUNCTIONAL_DX_RECOMMENDED,              // DX 定制推荐
            //PrivilegeConstants::FUNCTIONAL_DX_SUBSCRIPTION,            // DX 订阅
            //PrivilegeConstants::FUNCTIONAL_DX_CIQ,                              // DX 海关数据3.0
            PrivilegeConstants::FUNCTIONAL_AI,    //AI
            PrivilegeConstants::FUNCTIONAL_AI_AUTO_SETTING,      // AI 自动化设置
            PrivilegeConstants::FUNCTIONAL_AI_DASHBOARD,             // AI 仪表盘
            PrivilegeConstants::FUNCTIONAL_AI_RECOMMEND,              // AI 推荐
            PrivilegeConstants::FUNCTIONAL_AI_CIQ,                          // AI 客户海关数据更新
            PrivilegeConstants::FUNCTIONAL_AI_FOLLOW_CUSTOMER,  // AI 跟进客户
            PrivilegeConstants::FUNCTIONAL_AI_DEVELOP_LEAD,       // AI 开发新线索
            PrivilegeConstants::FUNCTIONAL_AI_FOLLOW_LEAD,          // AI 跟进线索
            PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_EMAIL,    // AI 邮件自动分类
            PrivilegeConstants::FUNCTIONAL_AI_PRODUCT,    //AI开发信产品
            PrivilegeConstants::FUNCTIONAL_FOCUS_COMPANY_DYNAMIC,    //DX关注公司动态
            PrivilegeConstants::FUNCTIONAL_MK,
            PrivilegeConstants::FUNCTIONAL_CIQ, //海关3.0

        ];

        PrivilegeService::getInstance($this->clientId)->removeFunction($functionalArr);
    }

    protected function actionAddExternalField()
    {
        $db = \PgActiveRecord::getDbByClientId($this->clientId);
        \PgActiveRecord::setConnection($db);
        $db = \ProjectActiveRecord::getDbByClientId($this->clientId);
        \ProjectActiveRecord::setConnection($db);
        $list = [
            [
                'group_id' => 1,
                'name' => '订单来源',
                'default' => ['type' => 1],
                'hint' => '',
                'require' => 0,
                'disable_flag' => 0,
                'is_editable' => 1,
                'edit_hide' => 1,
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                'relation_field' => '',
                'relation_type' => 0,
                'relation_field_type' => 0,
                'is_list' => 0,
                'ext_info' => ["阿里订单", "非阿里订单"],
            ],
            [
                'group_id' => 1,
                'name' => '圈战订单类型',
                'default' => ['type' => 1],
                'hint' => '',
                'require' => 0,
                'disable_flag' => 0,
                'is_editable' => 1,
                'edit_hide' => 1,
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                'relation_field' => '',
                'relation_type' => 0,
                'relation_field_type' => 0,
                'is_list' => 0,
                'ext_info' => ["信保订单", "非信保订单"],
            ],
            [
                'group_id' => 1,
                'name' => '是否为半托管订单',
                'default' => ['type' => 1],
                'hint' => '',
                'require' => 0,
                'disable_flag' => 0,
                'is_editable' => 1,
                'edit_hide' => 1,
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                'relation_field' => '',
                'relation_type' => 0,
                'relation_field_type' => 0,
                'is_list' => 0,
                'ext_info' => ["是", "否", "-"],
            ],
            [
                'group_id' => 1,
                'name' => '新老客户',
                'default' => ['type' => 1],
                'hint' => '',
                'require' => 0,
                'disable_flag' => 0,
                'is_editable' => 1,
                'edit_hide' => 1,
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                'relation_field' => '',
                'relation_type' => 0,
                'relation_field_type' => 0,
                'is_list' => 0,
                'ext_info' => ["新客户", "老客户"],
            ],
            [
                'group_id' => 1,
                'name' => '新客户数',
                'default' => ['type' => 1],
                'hint' => '',
                'require' => 0,
                'disable_flag' => 0,
                'is_editable' => 1,
                'edit_hide' => 1,
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'relation_field' => '',
                'relation_type' => 0,
                'relation_field_type' => 0,
                'is_list' => 0,
                'ext_info' => '',
            ],
            [
                'group_id' => 1,
                'name' => '商机数',
                'default' => ['type' => 1],
                'hint' => '',
                'require' => 0,
                'disable_flag' => 0,
                'is_editable' => 1,
                'edit_hide' => 1,
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'relation_field' => '',
                'relation_type' => 0,
                'relation_field_type' => 0,
                'is_list' => 0,
                'ext_info' => '',
            ]
        ];

        foreach ($list as $data)
        {
            $exist = CustomField::model()->find('client_id=:client_id and type=:type and name=:name and base=0 ', [':client_id'=> $this->clientId,':type'=> \Constants::TYPE_ORDER, ':name'=> $data['name']]);
            if( $exist )
            {
                \LogUtil::info("field exist {$exist['client_id']} {$exist['id']}, {$data['name']} continue!");
                continue;
            }

            $customFieldService = new CustomFieldService($this->clientId, \Constants::TYPE_ORDER);
            $field = $customFieldService->insertField($data);

            \LogUtil::info("insert field success {$field['client_id']} {$field['id']}, {$data['name']} ");

        }
    }

    protected function actionAddStatus()
    {
        $status = new InvoiceStatusService($this->clientId, \Constants::TYPE_ORDER);

        if (empty($status->statusByName('圈战订单'))) {
            $status->create(['name' => '圈战订单', 'remark' => '']);
        }

        $statusList = new InvoiceStatusService($this->clientId, \Constants::TYPE_ORDER);
        $statusMap = array_column($statusList->list(), null, 'name');
        $status->update([['is_ending' => 0, 'id' => $statusMap['圈战订单']['id']]]);
    }

    protected function actionAddPerformanceRule()
    {
        $statusList = new InvoiceStatusService($this->clientId, \Constants::TYPE_ORDER);
        $statusMap = array_column($statusList->list(), null, 'name');

        $orderSource = CustomField::model()->find('client_id=:client_id and type=:type and name=:name and base=0 and enable_flag=:enable_flag', [':client_id'=> $this->clientId,':type'=> \Constants::TYPE_ORDER, ':name'=> '订单来源', ':enable_flag'=>1]);

        if(!$orderSource->id)
        {
            throw new \RuntimeException('缺少订单来源字段'.$this->clientId);
        }
        $customerNumSource = CustomField::model()->find('client_id=:client_id and type=:type and name=:name and base=0 and enable_flag=:enable_flag', [':client_id'=> $this->clientId,':type'=> \Constants::TYPE_ORDER, ':name'=> Helper::PERFORMANCE_NAME_CUSTOMER_NUM, ':enable_flag'=>1]);
        if (!$customerNumSource->id) {
            throw new \RuntimeException('缺少客户数源字段'.$this->clientId);
        }
        $businessNumSource = CustomField::model()->find('client_id=:client_id and type=:type and name=:name and base=0 and enable_flag=:enable_flag', [':client_id'=> $this->clientId,':type'=> \Constants::TYPE_ORDER, ':name'=> Helper::PERFORMANCE_NAME_BUSINESS_NUM, ':enable_flag'=>1]);
        if (!$businessNumSource->id) {
            throw new \RuntimeException('缺少客户数源字段'.$this->clientId);
        }
        $rule = new \common\library\performance_v2\rule\PerformanceV2Rule($this->clientId);
        $rule->loadByName(Helper::PERFORMANCE_NAME_ORDER_AMOUNT);
        if($rule->isNew()) {
            $rule->setUserId($this->adminUserId);
            $rule->name = Helper::PERFORMANCE_NAME_ORDER_AMOUNT;
            $rule->description = '';
            $rule->refer_type = \Constants::TYPE_ORDER;
            $rule->filters = [["unit" => "", "field" => "status", "operator" => "=", "filter_no" => 1, "field_type" => "3", "refer_type" => "2", "value" => $statusMap['圈战订单']['id']]];
            $rule->time_field = 'create_time';
            $rule->target_field = 'amount_rmb';
            $rule->calculate_rule = 'sum';
            $rule->performance_field = 'create_user';
            $rule->criteria_type = 1;
            $rule->criteria = '(1)';
            $rule->save();
        }

        $rule = new \common\library\performance_v2\rule\PerformanceV2Rule($this->clientId);
        $rule->loadByName(Helper::PERFORMANCE_NAME_ORDER_COUNT);
        if($rule->isNew()) {
            $rule->setUserId($this->adminUserId);
            $rule->name = Helper::PERFORMANCE_NAME_ORDER_COUNT;
            $rule->description = '';
            $rule->refer_type = \Constants::TYPE_ORDER;
            $rule->filters = [
                ["unit" => "", "field" => "status", "operator" => "=", "filter_no" => 1, "field_type" => CustomFieldService::FIELD_TYPE_SELECT, "refer_type" => "2", "value" => $statusMap['圈战订单']['id']],
                ["unit" => "", "field" => "amount_rmb", "operator" => ">", "filter_no" => 1, "field_type" => CustomFieldService::FIELD_TYPE_NUMBER, "refer_type" => "2", "value" => '0'],
            ];
            $rule->time_field = 'create_time';
            $rule->target_field = '';
            $rule->calculate_rule = 'count';
            $rule->performance_field = 'create_user';
            $rule->criteria_type = 1;
            $rule->criteria = '(1 AND 2)';
            $rule->save();
        }

        $rule = new \common\library\performance_v2\rule\PerformanceV2Rule($this->clientId);
        $rule->loadByName(Helper::PERFORMANCE_NAME_ALI_ORDER_AMOUNT);
        if($rule->isNew()) {
            $rule->setUserId($this->adminUserId);
            $rule->name = Helper::PERFORMANCE_NAME_ALI_ORDER_AMOUNT;
            $rule->description = '统计圈战晒单中，阿里订单的总金额';
            $rule->refer_type = \Constants::TYPE_ORDER;
            $rule->filters = [
                ["unit" => "", "field" => "status", "operator" => "=", "filter_no" => 1, "field_type" => CustomFieldService::FIELD_TYPE_SELECT, "refer_type" => "2", "value" => $statusMap['圈战订单']['id']],
                ["unit" => "", "field" => $orderSource->id, "operator" => "=", "filter_no" => 2, "field_type" => CustomFieldService::FIELD_TYPE_SELECT, "refer_type" => "2", "value" => '阿里订单']
            ];
            $rule->time_field = 'create_time';
            $rule->target_field = 'amount_rmb';
            $rule->calculate_rule = 'sum';
            $rule->performance_field = 'create_user';
            $rule->criteria_type = 1;
            $rule->criteria = '(1 AND 2)';
            $rule->save();
        }

        $rule = new \common\library\performance_v2\rule\PerformanceV2Rule($this->clientId);
        $rule->loadByName(Helper::PERFORMANCE_NAME_BUSINESS_NUM);
        if($rule->isNew()) {
            $rule->setUserId($this->adminUserId);
            $rule->name = Helper::PERFORMANCE_NAME_BUSINESS_NUM;
            $rule->description = '';
            $rule->refer_type = \Constants::TYPE_ORDER;
            $rule->filters = [["unit" => "", "field" => "status", "operator" => "=", "filter_no" => 1, "field_type" => CustomFieldService::FIELD_TYPE_SELECT, "refer_type" => "2", "value" => $statusMap['圈战订单']['id']]];
            $rule->time_field = 'create_time';
            $rule->target_field = $businessNumSource->id;
            $rule->calculate_rule = 'sum';
            $rule->performance_field = 'create_user';
            $rule->criteria_type = 1;
            $rule->criteria = '(1)';
            $rule->save();
        }

        $rule = new \common\library\performance_v2\rule\PerformanceV2Rule($this->clientId);
        $rule->loadByName(Helper::PERFORMANCE_NAME_CUSTOMER_NUM);
        if($rule->isNew()) {
            $rule->setUserId($this->adminUserId);
            $rule->name = Helper::PERFORMANCE_NAME_CUSTOMER_NUM;
            $rule->description = '';
            $rule->refer_type = \Constants::TYPE_ORDER;
            $rule->filters = [["unit" => "", "field" => "status", "operator" => "=", "filter_no" => 1, "field_type" => CustomFieldService::FIELD_TYPE_SELECT, "refer_type" => "2", "value" => $statusMap['圈战订单']['id']]];
            $rule->time_field = 'create_time';
            $rule->target_field = $customerNumSource->id;
            $rule->calculate_rule = 'sum';
            $rule->performance_field = 'create_user';
            $rule->criteria_type = 1;
            $rule->criteria = '(1)';
            $rule->save();
        }

    }

    protected function actionAddRaceRole()
    {
        $privileges = [
            [
                "name" => "线索",
                "scope" => 1,
                "scope_open" => true,
                "scope_description" => "",
                "description" => "",
                "list" => [
                    "crm.lead.private.view" => [
                        "name" => "查看",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 1,
                        "include" => []
                    ],
                    "crm.lead.private.create" => [
                        "name" => "创建",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.lead.private.remove" => [
                        "name" => "删除",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.lead.private.edit" => [
                        "name" => "编辑",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.lead.private.transfer" => [
                        "name" => "转移",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.lead.private.transform" => [
                        "name" => "转化",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.lead.private.report" => [
                        "name" => "线索导出",
                        "description" => "",
                        "enable" => true,
                        "depends" => "crm.functional.lead.report",
                        "dependsExtra" => [],
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ]
                ],
                "functional" => "crm.functional.lead",
                "field_privilege" => true
            ], [
                "name" => "商机",
                "scope" => 1,
                "scope_open" => true,
                "scope_description" => "",
                "description" => "",
                "list" => [
                    "crm.opportunity.view" => [
                        "name" => "查看",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 1,
                        "include" => []
                    ],
                    "crm.opportunity.create" => [
                        "name" => "创建",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.opportunity.remove" => [
                        "name" => "删除",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.opportunity.edit" => [
                        "name" => "编辑",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.opportunity.transfer" => [
                        "name" => "转移",
                        "description" => "可以将商机转移给其他成员",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.opportunity.member.manage" => [
                        "name" => "团队成员管理",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.opportunity.trail.create" => [
                        "name" => "添加动态",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.opportunity.export" => [
                        "name" => "商机批量导出",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.opportunity.approval.unlock" => [
                        "description" => "",
                        "enable" => true,
                        "name" => "解锁",
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ]
                ],
                "functional" => "crm.functional.opportunity",
                "field_privilege" => true
            ], [
                "name" => "客户",
                "scope" => 1,
                "scope_open" => true,
                "scope_description" => "",
                "description" => "",
                "list" => [
                    "crm.company.private.view" => [
                        "name" => "查看",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 1,
                        "include" => []
                    ],
                    "crm.company.private.create" => [
                        "name" => "创建",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.company.private.edit" => [
                        "name" => "编辑",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.company.private.transfer" => [
                        "name" => "转移",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.company.private.assign" => [
                        "name" => "重新分配",
                        "description" => "执行该操作，将把指定客户的所有跟进人取消，替换成新的跟进人",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.company.private.release.specify.user" => [
                        "name" => "移除跟进人",
                        "description" => "执行该操作，可批量移除所选客户的指定跟进人",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.company.private.release" => [
                        "name" => "取消跟进",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.company.private.share" => [
                        "name" => "共享客户",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.company.private.move.pool" => [
                        "name" => "移入公海",
                        "description" => "",
                        "enable" => true,
                        "depends" => "crm.functional.company.pool",
                        "dependsExtra" => [],
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.company.private.merge" => [
                        "name" => "合并客户",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ]
                ],
                "functional" => "crm.functional.base",
                "field_privilege" => true
            ], [
                "name" => "公海客户",
                "scope" => 1,
                "scope_open" => true,
                "scope_description" => "",
                "description" => "",
                "list" => [
                    "crm.company.pool.view" => [
                        "name" => "查看",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 1,
                        "include" => []
                    ],
                    "crm.company.pool.create" => [
                        "name" => "创建",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.company.pool.edit" => [
                        "name" => "编辑",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.company.pool.remove" => [
                        "name" => "删除",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.company.pool.hold" => [
                        "name" => "移入私海",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.company.pool.assign" => [
                        "name" => "分配客户",
                        "description" => "执行该操作，可将指定公海客户分配给其他成员跟进",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ]
                ],
                "functional" => "crm.functional.company.pool",
                "field_privilege" => true
            ], [
                "name" => "产品",
                "scope" => 1,
                "scope_open" => true,
                "scope_description" => "",
                "description" => "",
                "list" => [
                    "crm.product.view" => [
                        "description" => "",
                        "enable" => true,
                        "name" => "查看",
                        "required" => 0,
                        "basic" => 1,
                        "include" => []
                    ],
                    "crm.product.create" => [
                        "description" => "",
                        "enable" => true,
                        "name" => "创建",
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.product.edit" => [
                        "description" => "",
                        "enable" => true,
                        "name" => "编辑",
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.product.delete" => [
                        "description" => "",
                        "enable" => true,
                        "name" => "删除",
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.product.edit.no.create" => [
                        "description" => "",
                        "enable" => true,
                        "name" => "编辑产品编号(新建产品)",
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.product.edit.no.update" => [
                        "description" => "",
                        "enable" => true,
                        "name" => "编辑产品编号(编辑产品)",
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.product.export" => [
                        "description" => "",
                        "enable" => true,
                        "name" => "产品批量下载",
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ]
                ],
                "functional" => "crm.functional.product",
                "field_privilege" => true
            ], [
                "name" => "报价单",
                "scope" => 1,
                "scope_open" => true,
                "scope_description" => "",
                "description" => "",
                "list" => [
                    "crm.quotation.view" => [
                        "name" => "查看",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 1,
                        "include" => []
                    ],
                    "crm.quotation.create" => [
                        "name" => "创建",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.quotation.edit" => [
                        "name" => "编辑",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.quotation.remove" => [
                        "name" => "删除",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.quotation.export" => [
                        "name" => "报价单批量下载",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.quotation.approval.unlock" => [
                        "description" => "",
                        "enable" => true,
                        "name" => "解锁",
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ]
                ],
                "functional" => "crm.functional.quotation",
                "field_privilege" => true
            ], [
                "name" => "订单",
                "scope" => 1,
                "scope_open" => true,
                "scope_description" => "",
                "description" => "",
                "list" => [
                    "crm.order.view" => [
                        "name" => "查看",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 1,
                        "include" => []
                    ],
                    "crm.order.create" => [
                        "name" => "创建",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.order.edit" => [
                        "name" => "编辑",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.order.export" => [
                        "name" => "导出",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.order.remove" => [
                        "name" => "删除",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.order.edit.no.create" => [
                        "name" => "编辑订单编号(新建订单)",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.order.edit.no.edit" => [
                        "name" => "编辑订单编号(编辑订单)",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.order.export" => [
                        "name" => "订单批量下载",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.order.approval.unlock" => [
                        "description" => "",
                        "enable" => true,
                        "name" => "解锁",
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ]
                ],
                "functional" => "crm.functional.order",
                "field_privilege" => true
            ], [
                "name" => "报关",
                "scope" => 1,
                "scope_open" => true,
                "scope_description" => "",
                "description" => "",
                "list" => [
                    "crm.customs.view" => [
                        "name" => "查看",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ]
                ],
                "functional" => "crm.functional.customs",
                "field_privilege" => false
            ], [
                "name" => "回款单",
                "scope" => 1,
                "scope_open" => true,
                "scope_description" => "",
                "description" => "",
                "list" => [
                    "crm.cashCollection.view" => [
                        "description" => "",
                        "enable" => true,
                        "name" => "查看",
                        "required" => 0,
                        "basic" => 1,
                        "include" => []
                    ],
                    "crm.cashCollection.create" => [
                        "description" => "",
                        "enable" => true,
                        "name" => "创建(编辑)",
                        "required" => 0,
                        "basic" => 0,
                        "include" => ["crm.cashCollection.relate"]
                    ],
                    "crm.cashCollection.relate" => [
                        "description" => "",
                        "enable" => true,
                        "name" => "关联",
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.cashCollection.remove" => [
                        "description" => "",
                        "enable" => true,
                        "name" => "删除",
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.cashCollection.setting" => [
                        "description" => "",
                        "enable" => true,
                        "name" => "设置",
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ]
                ],
                "functional" => "crm.functional.cashCollection",
                "field_privilege" => true
            ], [
                "name" => "物流运输",
                "scope" => 1,
                "scope_open" => true,
                "scope_description" => "",
                "description" => "",
                "list" => [
                    "crm.transport.view" => [
                        "name" => "查看",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ]
                ],
                "functional" => "crm.functional.transport",
                "field_privilege" => false
            ], [
                "name" => "统计分析",
                "scope" => 1,
                "scope_open" => true,
                "scope_description" => "",
                "description" => "",
                "list" => [
                    "crm.statistics.view" => [
                        "name" => "查看",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.statistics.export" => [
                        "description" => "自定义报表支持导出详情数据",
                        "enable" => true,
                        "name" => "导出报表详情",
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ]
                ],
                "functional" => "crm.functional.statistic",
                "field_privilege" => false
            ], [
                "name" => "绩效",
                "scope" => 1,
                "scope_open" => true,
                "scope_description" => "",
                "description" => "",
                "list" => [
                    "crm.performance.view" => [
                        "description" => "",
                        "enable" => true,
                        "name" => "查看绩效目标",
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.performance.create" => [
                        "description" => "",
                        "enable" => true,
                        "name" => "设定目标值",
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.performance.rule.view" => [
                        "description" => "",
                        "enable" => true,
                        "name" => "查看绩效规则",
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.performance.team.wall" => [
                        "description" => "",
                        "enable" => true,
                        "depends" => "crm.functional.performance.team.wall",
                        "name" => "查看团队墙",
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ]
                ],
                "functional" => "crm.functional.performance",
                "field_privilege" => false
            ], [
                "name" => "邮件",
                "scope" => 1,
                "scope_open" => true,
                "scope_description" => "",
                "description" => "",
                "list" => [
                    "crm.mail.view" => [
                        "name" => "查看",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 1,
                        "include" => []
                    ],
                    "crm.mail.hard.remove" => [
                        "name" => "彻底删除",
                        "description" => "草稿箱彻底删除不受权限限制",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.email.approval.unlock" => [
                        "description" => "",
                        "enable" => true,
                        "name" => "解锁",
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.mail.export" => [
                        "name" => "导出",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                ],
                "functional" => "crm.functional.base",
                "field_privilege" => false
            ], [
                "name" => "跟进动态",
                "scope" => 1,
                "scope_open" => true,
                "scope_description" => "",
                "description" => "",
                "list" => [
                    "crm.followup.view" => [
                        "name" => "查看",
                        "description" => "",
                        "enable" => true,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ]
                ],
                "functional" => "crm.functional.followup",
                "field_privilege" => false
            ], [
                "name" => "小满营销",
                "scope" => 1,
                "scope_open" => true,
                "scope_description" => "",
                "description" => "",
                "list" => [
                    "crm.edm.view" => [
                        "name" => "营销邮件",
                        "description" => "",
                        "enable" => false,
                        "required" => 0,
                        "basic" => 1,
                        "include" => []
                    ],
                    "crm.edm.auto" => [
                        "name" => "自动化营销",
                        "description" => "",
                        "depends" => "crm.functional.mk.auto",
                        "enable" => false,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ]
                ],
                "functional" => "crm.functional.mk",
                "field_privilege" => false
            ], [
                "name" => "小满发现",
                "scope" => 1,
                "scope_open" => true,
                "scope_description" => "",
                "description" => "",
                "list" => [
                    "crm.dx.user" => [
                        "name" => "查看小满发现",
                        "description" => "",
                        "enable" => false,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ]
                ],
                "functional" => "crm.functional.dx",
                "field_privilege" => false
            ], [
                "name" => "小满AI",
                "scope" => 1,
                "scope_open" => true,
                "scope_description" => "",
                "description" => "",
                "list" => [
                    "crm.ai.user" => [
                        "name" => "小满AI",
                        "description" => "",
                        "enable" => false,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ]
                ],
                "functional" => "crm.functional.ai",
                "field_privilege" => false
            ], [
                "name" => "设置管理",
                "scope" => 1,
                "scope_open" => true,
                "scope_description" => "",
                "description" => "",
                "list" => [
                    "crm.setting.email.manage" => [
                        "name" => "邮箱绑定",
                        "description" => "",
                        "enable" => false,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.email.template" => [
                        "name" => "邮件模板",
                        "description" => "",
                        "enable" => false,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.customer.manage" => [
                        "name" => "客户设置",
                        "description" => "",
                        "enable" => false,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.customer.export" => [
                        "name" => "客户导出",
                        "description" => "",
                        "enable" => false,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.product.manage" => [
                        "name" => "产品设置",
                        "description" => "",
                        "depends" => "crm.functional.product",
                        "enable" => false,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.quotation.manage" => [
                        "name" => "报价单设置",
                        "description" => "",
                        "enable" => false,
                        "depends" => "crm.functional.quotation",
                        "dependsExtra" => [],
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.quotation.template" => [
                        "name" => "报价单导出模板设置",
                        "description" => "",
                        "enable" => false,
                        "depends" => "crm.functional.quotation",
                        "dependsExtra" => [],
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.order.manage" => [
                        "name" => "订单设置",
                        "description" => "",
                        "enable" => false,
                        "depends" => "crm.functional.order",
                        "dependsExtra" => [],
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.order.template" => [
                        "name" => "订单导出模板设置",
                        "description" => "",
                        "enable" => false,
                        "depends" => "crm.functional.order",
                        "dependsExtra" => [],
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.customs.manage" => [
                        "name" => "报关设置",
                        "description" => "",
                        "enable" => false,
                        "depends" => "crm.functional.customs",
                        "dependsExtra" => [],
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.performance.setting" => [
                        "name" => "绩效设置",
                        "description" => "",
                        "enable" => false,
                        "depends" => "crm.functional.performance",
                        "dependsExtra" => [],
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.edm" => [
                        "name" => "营销设置",
                        "description" => "",
                        "enable" => false,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.exchange.manage" => [
                        "name" => "汇率设置",
                        "description" => "",
                        "enable" => false,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.contacts.manage" => [
                        "name" => "通讯录设置",
                        "description" => "",
                        "enable" => false,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.email.review.specify" => [
                        "name" => "设置特殊审批人",
                        "description" => "",
                        "enable" => false,
                        "depends" => "crm.functional.review.base",
                        "dependsExtra" => [],
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.origin.manage" => [
                        "name" => "来源设置",
                        "description" => "",
                        "enable" => false,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.module.manage" => [
                        "name" => "功能开关",
                        "description" => "",
                        "enable" => false,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.fields.manage" => [
                        "name" => "字段设置",
                        "description" => "",
                        "enable" => false,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.opportunity.manage" => [
                        "name" => "商机设置",
                        "description" => "",
                        "enable" => false,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.paypal.manage" => [
                        "name" => "PayPal收款",
                        "description" => "",
                        "enable" => false,
                        "depends" => "crm.functional.paypal",
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.privilege.manage" => [
                        "name" => "角色权限",
                        "description" => "",
                        "enable" => false,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.user.manage" => [
                        "name" => "成员管理",
                        "description" => "",
                        "enable" => false,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.account.info" => [
                        "name" => "账号信息",
                        "description" => "",
                        "enable" => false,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.disk.manage" => [
                        "name" => "云盘管理",
                        "description" => "",
                        "enable" => false,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.ai.setting" => [
                        "name" => "AI 自动化设置",
                        "description" => "",
                        "enable" => false,
                        "depends" => "crm.functional.ai",
                        "dependsExtra" => ["crm.functional.ai.classify.customer", "crm.functional.ai.classify.opportunity"],
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.workflow.manage" => [
                        "enable" => false,
                        "depends" => "crm.functional.workflow",
                        "name" => "工作流管理",
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.approvalflow.manage" => [
                        "enable" => false,
                        "depends" => "crm.functional.approvalflow",
                        "name" => "审批流管理",
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.recycle.manage" => [
                        "name" => "回收站",
                        "description" => "",
                        "enable" => false,
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ],
                    "crm.setting.alibaba.store.manage" => [
                        "name" => "阿里店铺管理",
                        "description" => "",
                        "enable" => false,
                        "depends" => "crm.functional.alibaba.trade",
                        "required" => 0,
                        "basic" => 0,
                        "include" => []
                    ]
                ],
                "functional" => "crm.functional.base",
                "field_privilege" => false
            ]];

        $roleService = PrivilegeService::getInstance($this->clientId)->getUserPrivilege()->getRoles();
        $scope = $privileges[0]['scope'] ?? PrivilegeConstants::PRIVILEGE_SCOPE_OWNER;
        foreach ($privileges as $key => $item) {
            $privileges[$key]['scope'] = $scope;
        }
        $systemModule = PrivilegeConstants::SYSTEM_MODULE_CRM;

        if(!PrivilegeService::getInstance($this->clientId)->getRoleIdByName('业务员')){
            $roleId = $roleService->create($systemModule, '业务员', $scope);
            $roleService->setPrivileges($roleId, $privileges);
        }

        $privileges = array_map(function ($privilege) {
            $privilege['scope'] = 2;
            return $privilege;
        }, $privileges);
        $roleService = PrivilegeService::getInstance($this->clientId)->getUserPrivilege()->getRoles();
        $scope = $privileges[0]['scope'] ?? PrivilegeConstants::PRIVILEGE_SCOPE_OWNER;
        foreach ($privileges as $key => $item) {
            $privileges[$key]['scope'] = $scope;
        }
        $systemModule = PrivilegeConstants::SYSTEM_MODULE_CRM;

        if(!PrivilegeService::getInstance($this->clientId)->getRoleIdByName('企业管理员')){
            $companyRoleId = $roleService->create($systemModule, '企业管理员', $scope);
            $roleService->setPrivileges($companyRoleId, $privileges);
        }

        $privileges = array_map(function ($privilege) {
            $privilege['scope'] = 3;
            return $privilege;
        }, $privileges);
        $roleService = PrivilegeService::getInstance($this->clientId)->getUserPrivilege()->getRoles();
        $scope = $privileges[0]['scope'] ?? PrivilegeConstants::PRIVILEGE_SCOPE_OWNER;
        foreach ($privileges as $key => $item) {
            $privileges[$key]['scope'] = $scope;
        }
        $systemModule = PrivilegeConstants::SYSTEM_MODULE_CRM;

        if(!PrivilegeService::getInstance($this->clientId)->getRoleIdByName('军团管理员')){
            $groupRoleId = $roleService->create($systemModule, '军团管理员', $scope);
            $roleService->setPrivileges($groupRoleId, $privileges);
        }

    }

    public function createAccount($count, $password)
    {
        $adminEmail = \User::getLoginUser()->getEmail();

        list($prefix, $suffix) = explode('@',$adminEmail);

        $roleService = PrivilegeService::getInstance($this->clientId)->getUserPrivilege()->getRoles();
        $systemModule = PrivilegeConstants::SYSTEM_MODULE_CRM;
        $roleId = $roleService->getRoleIdByName('业务员', $systemModule);

        if (!$roleId) {
            throw new \RuntimeException('还没有完成初始化');
        }

        $invalidEmails = [];
        for ($i = 1; $i <= $count; $i++) {

            //aliquanzhan，-，大区首拼音，-，大区的申请自增数，-，'【自增账号数】'，@xiaoman.cn
            $accountName = $prefix . '-' . $i . '@' . $suffix;
            $accountName = $prefix . '-' . $i . '@xiaoman.cn';

            list ($userId, $account) = $this->actionAddUser($accountName, $password, $roleId, $i);
            $invalidEmails[] = "('{$account}',1)";
            \LogUtil::info("clientId:{$this->clientId} account: {$account} userId:{$userId} success!\n");
        }

        if (!empty($invalidEmails)) {
            $sql = "insert into tbl_invalid_email_address (email_address, enable_flag) values " . implode(',', $invalidEmails);
            $sql .= " ON DUPLICATE KEY UPDATE enable_flag=1";
            $db = \Yii::app()->db;
            $db->createCommand($sql)->execute();
        }

    }

    public function actionAddUser($accountName, $password, $roleId, $nickname, string $roleName = '普通用户')
    {
        $userInfo = (new \common\library\account\UserInfo())->loadByEmail($accountName, $this->clientId);
        if( !$userInfo->isNew() && $userInfo->user_id)
        {
            \LogUtil::info("add User exist continue {$this->clientId} {$userInfo->user_id} {$accountName}");
            return  [$userInfo->user_id, $userInfo->email];
        }
        $account = new \common\library\account\action\Create($accountName);
        $account->setClientId($this->clientId);
        $account->setActivation(1);
        $account->setPassword($password);
        $account->setData([
            'account' => $account,
            'pwd_salt' => null,
            'create_time' => date('Y-m-d H:i:s'),
            'nickname' => $nickname,
        ]);
        $account->run();
        $userId = $account->getUserId();
        PrivilegeService::getInstance($this->clientId, $userId)->initUser();

        if ($roleName) {
            $removeRoleId = PrivilegeService::getInstance($this->clientId)->getRoleIdByName($roleName);
            PrivilegeService::getInstance($this->clientId)->assignRole($roleId, [$userId]);
            PrivilegeService::getInstance($this->clientId)->removeRole($removeRoleId, [$userId]);
        }
        return [$userId, $accountName];
    }


}
