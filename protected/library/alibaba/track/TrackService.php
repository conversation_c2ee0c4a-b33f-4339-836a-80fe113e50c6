<?php
namespace common\library\alibaba\track;
use common\library\ai\classify\ai_field_data\AIFieldData;
use common\library\ai\classify\capture\CaptureCard;
use common\library\ai\classify\capture\CaptureCardLog;
use common\library\ai\classify\mail\Classify;
use common\library\ai_agent\AiAgentConstants;
use common\library\alibaba\Constant;
use common\library\alibaba\customer\AlibabaCompanyRelation;
use common\library\alibaba\customer\CustomerSyncHelper;
use common\library\alibaba\Helper;
use common\library\alibaba\services\message\DelayMessageService;
use common\library\alibaba\services\message\TmMessageService;
use common\library\alibaba\store\AlibabaStore;
use common\library\alibaba\store\AlibabaStoreMembersList;
use common\library\alibaba\trade\AlibabaTradeRelationList;
use common\library\coroutine\exception\RuntimeException;
use common\library\customer_convert\convert_config\ConfigService;
use common\library\customer_convert\ConvertHandler;
use common\library\customer_convert\ConvertService;
use common\library\customer_v3\company\orm\Company;
use common\library\customer_v3\customer\orm\Customer;
use common\library\lead\Lead;
use common\library\lead\LeadBatchOperator;
use common\library\lead\LeadCustomer;
use common\library\lead\LeadList;
use common\library\mail\Mail;
use common\library\notification\PushHelper;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\push\Browser;
use common\library\push\DesktopPush;
use common\library\report\sensors\events\EventCreateCompany;
use common\library\setting\item\Api;
use common\library\setting\library\origin\Origin;
use common\library\sns\customer\CustomerContactHelper;
use common\library\trail\events\AlibabaEvents;
use common\library\trail\TrailConstants;
use common\library\util\PgsqlUtil;
use common\library\util\RedLock;
use Xiaoman\Sidekick\Trace\TracerProvider;

/**
 * Created by PhpStorm.
 * User: ganyaoyao
 * Date: 2020/9/8
 * Time: 15:24
 */

class TrackService {

    protected $params;//请求参数
    protected $opUserId;//操作userId
    protected $clientId;
    protected $rules;
    protected $companyId;
    protected $customerId;
    protected $leadId;
    protected $leadCustomerId;
    protected $tradeId;
    protected $messageType;
    protected $tradeType;
    protected $messageTime;
    protected $accessToken;
    protected $buyerAccountId;
    protected $buyerEmail;
    protected $sellerAccountId;
    protected $sellerEmail;
    protected $storeId;
    protected $businessId;
    protected $businessIdEncrypt;
    protected $fromSellerReplyFlag;//是否卖家回复
    protected $updateCustomerOrderTimeFlag;//更新客户联系人标识
    protected $sendMessageFlag;//发送信息标识
    protected $updateLeadOrderTimeFlag;//更新线索最新时间标识
    protected $buyerMemberInfo;//通过阿里买家匹配到客户通信息
    protected $assignImquiryLeadList;//分配询盘的线索列表
    protected $assignImquiryUserId;//分配询盘的跟进人
    protected $inquiryIsAssignedFlag; //询盘是否已分配给新跟进人
    protected $enterpriseLeadList =[];//企业邮箱线索列表
    protected $leadApplySettings = [];//线索自动化设置配置

    protected $privateLeadList =[];//私海线索列表
    protected $privateCompanyList =[];//私海客户列表

    protected $buyIdPrivateLeadList =[];//根据买家id查找私海线索列表
    protected $buyIdPrivateCompanyList =[];//根据买家id查找私海客户列表
    protected $alibabaTradeRelationListResult =[]; // 根据买家 id 查找出的买家 id 与客户/线索的关系列表

    protected $analyzeResult =[];//分析结果集
    protected $handleResult =[];//执行结果集

    protected $locker;

    protected $performanceCompanyIds;


    /**
     * TrackService constructor.
     * @param array $params
     * @throws RuntimeException
     * @throws \ProcessException
     */
    public function __construct(array $params)
    {
        $this->params = $params;
        if(!$params['client_id'] || !$params['op_user_id']) {
            throw  new RuntimeException("获取登录用户信息为空:".json_encode($params));
        }
        $this->opUserId = intval($params['op_user_id']);
        \User::setLoginUserById($this->opUserId);
        $user = \User::getLoginUser();
        if(!$user || !$user->getClientId() || !$user->getUserId()){
            throw  new  RuntimeException("获取登录用户信息为空 client_id:{this->clientId} user_id:{$this->opUserId}:".json_encode($params));
        }
        $this->clientId = $params['client_id'];
        $this->messageType = $params['message_type']??null;
        $this->tradeType = $this->params['trade_type'];
        $this->messageTime = $this->params['message_time'];
        /** @var \CApcCache $cache */
        $cache =  \Yii::app()->apccache;
        $cacheKey = 'alibaba.config.trade_apply';
        $value =  $cache->get($cacheKey);
        $rules = $value?json_decode($value, true):[];
        $rules = [];
        if( empty($rules) )
        {
            $path = \Yii::getPathOfAlias('application.library.alibaba.config.trade_lead_apply') . '.yaml';
            $rules = \Symfony\Component\Yaml\Yaml::parse(file_get_contents($path));
            $cache->set($cacheKey,json_encode($rules), 600);
        }

        $this->rules = $rules;
    }


    public function apply()
    {
        $this->takeMutex();

        try {
            $this->analyze();

            $this->handle();

            return $this->getResult();
        } finally {
            $this->releaseMutex();
        }
    }

    protected function takeMutex()
    {
        if (!isset($this->params['seller_account_id'], $this->params['buyer_account_id'])) {
            return;
        }

        $redis = \RedisService::cache();
        $key = "alibaba:track:{$this->params['seller_account_id']}:{$this->params['buyer_account_id']}";
        $this->locker = new RedLock($redis, $key);

        do {
            if ($this->locker->lock(5))
                return;

            usleep(200000); // 200 ms
        } while (true);
    }

    protected function releaseMutex()
    {
        if ($this->locker) {
            $this->locker->unlock();
        }
    }

    protected function analyze()
    {
        $rules = $this->rules['analyze'];

        $this->analyzeResult = [];

        foreach ($rules as $apply => $rule) {
            $this->executeRule($apply, $rule, $this->analyzeResult );
        }

        $this->log($this->analyzeResult, 'analyze');

        // echo "analyzeResult\n";
        // var_dump($this->analyzeResult);

        return $this->analyzeResult;
    }

    public function executeRule($apply, $rule, &$resultList)
    {
        $rules = $this->rules['analyze'];
        $function = $rule['function'];
        $params = $rule['params'] ?? [];
        $requireList = $rule['require'] ?? [];
        $op = ($rule['op'] ?? '') ?: 'and';
        $analyze = true;
        if (!empty($requireList)) {
            //默认值
            $analyze = $op == 'and' ? true : false;

            foreach ($requireList as $requireApply => $requireResult) {

                if (!array_key_exists($requireApply, $resultList)) {
                    $this->executeRule($requireApply, $rules[$requireApply], $resultList);
                }
                if (($requireResult && !$resultList[$requireApply]) ||
                    !$requireResult && $resultList[$requireApply]) {
                    $break = $op == 'and' ? true : false;
                } else {
                    $break = $op == 'or' ? true : false;
                }
                if ($break) {
                    $analyze = $op == 'or' ? true : false;
                    break;
                }
            }
        }
//        var_dump($apply,$requireList, $analyze);

        $result = $analyze ? call_user_func_array([$this, $function], $params) : 0;
        $resultList[$apply] = $result;
        return $result;
    }

    protected function handle()
    {
        $rules = $this->rules['handle'];
        $analyzeResult = $this->analyzeResult;
        $this->handleResult = [];
        foreach ($rules as $key => $rule) {
            $function = $rule['function'];
            $params = $rule['params'] ?? [];
            $requireList = $rule['require'] ?? [];
            // echo "requireList\n";
            // var_dump($requireList);
            $require = false;
            $handleRequireRule = [];
            foreach ($requireList as $requireNo => $requireRule) {
                $require = false;
                foreach ($requireRule as $requireApply => $requireResult) {
                    if ($requireResult && !$analyzeResult[$requireApply]) {
                        $require = false;
                        break;
                    }
                    if (!$requireResult && $analyzeResult[$requireApply]) {
                        $require = false;
                        break;
                    }
                    $require = true;
                }

                if ($require) {
                    $handleRequireRule = $requireRule;
                    break;
                }
            }

            if ($require) {
                $this->log(compact('require', 'key', 'handleRequireRule'), 'execute');
            }
            $result = $require ? call_user_func_array([$this, $function], $params) : 0;
            $this->handleResult[$key] = $result;
        }
        $this->log($this->handleResult, 'handle');

        return $this->handleResult;
    }

    /**
     * @return array
     */
    public function getResult(): array
    {
        $traceId = \Xiaoman\Sidekick\getCurrentTraceId();
        \LogUtil::info("chatMessageHandlerTraceId:[$traceId]");
        return ['analyze' => $this->analyzeResult, 'handle' => $this->handleResult,'traceId' => $traceId];
    }

    protected function log($msg, $title='')
    {
        $msg = is_string($msg)? $msg: json_encode($msg);
        $msg = $title.':'.$msg;
        \LogUtil::debug($msg);

    }

    public function checkSendMessageType()
    {

        //如果没有发送过通知，非系统通知信息，需要发送
        if (!$this->sendMessageFlag && !in_array( $this->messageType,Constant::$sysMessageType)) {
            return true;
        }
        if (!in_array( $this->messageType,Constant::$messageTypeList) && !in_array( $this->messageType,Constant::$sysMessageType)) {
            return true;
        }
        return false;
    }

    //是否来自买家回复
    public function checkFromSellerReplyMessage()
    {
        if ($this->params['trade_type'] == Constant::FROM_SELLER_MESSAGE_FLAG) {
            return true;
        }
        return false;
    }

    public function checkSyncDynamic()
    {
        if (in_array( $this->messageType,Constant::$videoMap)) {
            return true;
        }
        return false;
    }

    public function checkUpdateOrderTime()
    {
        if (in_array( $this->messageType,Constant::$updateOrderTimeMap)) {
            return true;
        }
        return false;
    }

    //询盘业务类型
    public function checkEvent()
    {
        if (!$this->messageType) {
            return false;
        }
        return true;
    }

    //是否潜客运营待接待提醒
    public function checkVisitorMarketingReceptionNotify()
    {
        if ($this->messageType != Constant::MESSAGE_TYPE_TEMPLATE_TEXT) {
            return false;
        }

        if (!in_array( $this->params['visitor_scene'], Constant::$visitorMarketingSceneMap )) {
            return false;
        }

        return true;
    }

    /**
     * 判断是否要保存消息
     * @return bool
     */
    public function checkSaveTmMessage(): bool
    {
        // 暂定保存所有消息
        return true;
    }

    public function saveTmMessage(): bool
    {
        $buyerAccountId = $this->params['buyer_account_id'] ?? '';
        $sellerAccountId = $this->params['seller_account_id'] ?? '';
        $storeId = $this->storeId;


        $messageService = new TmMessageService($this->clientId, $this->opUserId);

        try {
            $messageService->loadUserContact($buyerAccountId, $storeId, []);
            $message = $this->params;
            $isFromBuyer = ((int)$this->params['trade_type']) === Constant::FROM_BUYER_MESSAGE_FLAG;
            $message['from_account_id'] = $isFromBuyer ? $buyerAccountId : $sellerAccountId;
            $message['to_account_id'] = $isFromBuyer ? $sellerAccountId : $buyerAccountId;

            $messageService->saveMessages([$message]);

            // fixme: 迁移逻辑 等读取逻辑全部改造完成后下掉 storeId 的逻辑
            $sellerMessageService = new TmMessageService($this->clientId, $this->opUserId);
            $sellerMessageService->loadTmSellerContact($buyerAccountId,$storeId,$sellerAccountId);
            $sellerMessageService->saveMessages([$message]);


        } catch (\Throwable $e) {
            \LogUtil::error("保存TM消息失败", [
                'buyer_account_id' => $buyerAccountId,
                'store_id' => $storeId,
                'params' => $this->params,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ]);
            return false;
        }
        return true;
    }

    /**
     * 统一兜底运行绩效
     * @return bool
     */
    public function runPerformance()
    {
        $companyIds = is_array($this->performanceCompanyIds) ? $this->performanceCompanyIds : [$this->performanceCompanyIds];
        $companyIds = array_filter($companyIds);

        if (!empty($companyIds))
        {
            \LogUtil::info('trackServiceRunPerformance', [
                'client_id' => $this->clientId,
                'user_id' => $this->opUserId,
                'buyer_account_id' => $this->buyerAccountId,
                'company_ids' => $companyIds
            ]);

            \common\library\performance_v2\Helper::runPerformance($this->clientId, \Constants::TYPE_COMPANY, $companyIds);
        }

        return true;
    }

    //新建线索的消息类型
    public function checkInsertLeadMessageType()
    {
        if(in_array( $this->messageType,Constant::$messageTypeList)){
            return true;
        }
        return false;
    }


    //检查线索模块权限
    protected function checkLeadPrivilege() {

        if(!PrivilegeService::getInstance($this->clientId,$this->opUserId)->hasFunctional([PrivilegeConstants::FUNCTIONAL_LEAD])) {
            return false;
        }
        return true;
    }

    //检查阿里授权账号是否过期
    protected function checkAlibabaAccount(){

        $alibabaData = \common\library\alibaba\Helper::getBindAccountInfoBySellerId($this->params['seller_account_id']);

        if($alibabaData){
            $this->analyzeResult['alibaba_account']['seller_email'] = $alibabaData['seller_email'];
            $this->analyzeResult['alibaba_account']['seller_account_id'] = $alibabaData['seller_account_id'];
            if(in_array($alibabaData['oauth_flag'], [Constant::OAUTH_FLAG_BIND, Constant::OAUTH_FLAG_BIND_AND_AUTH]) && $alibabaData['access_token']){
                $this->accessToken = $alibabaData['access_token'];
            }
            $this->sellerAccountId = $alibabaData['seller_account_id'];
            $this->sellerEmail = $alibabaData['seller_email'];
            $this->storeId = $alibabaData['store_id'];
            $this->buyerAccountId = $this->params['buyer_account_id']; //买家id
            $this->buyerEmail = $this->params['buyer_email'];

            if(($this->params['business_id']??[]) && is_array($this->params['business_id'])){
                $this->businessId = is_array($this->params['business_id'])?$this->params['business_id'][0]:$this->params['business_id'];
            }else{
                $this->businessId = $this->params['business_id']?:0;
            }
            if(($this->params['business_id_encrypt']??[]) && is_array($this->params['business_id_encrypt'])){
                $this->businessIdEncrypt = is_array($this->params['business_id_encrypt'])?$this->params['business_id_encrypt'][0]:$this->params['business_id_encrypt'];
            }else{
                $this->businessIdEncrypt = $this->params['business_id_encrypt']?:"";
            }

            return true;
        }
        return false;
    }


    //检查数据
    public function analyzeTrackData()
    {
        //检查消息类型
        if( empty( $this->messageType) || !in_array( $this->messageType,Constant::$messageTypeList)){
            return false;
        }
        //检查买家id或者买家邮箱是否为空
        if(!($this->params['buyer_account_id']??0) && !($this->params['buyer_email']??"")){
            return false;
        }
        return true;
    }

    //检查是否满足分配询盘条件
    public function  checkAssignImquiry(){

        $this->assignImquiryUserId = 0;
        $this->inquiryIsAssignedFlag = false;
        //分配询盘的业务类型
        if(!in_array( $this->messageType,Constant::$assignInquiryMap)){
            return false;
        }
        //分配询盘的新跟进人
        if(!$this->params['new_owner_account_id']??0){
            return false;
        }
        //询盘的业务id
        if(!($this->params['business_id']??[])){
            return false;
        }

        //查找分配询盘的跟进人
        if($this->params['new_owner_account_id'] != $this->params['seller_account_id']){
            //询盘的seller_account_id对应绑定的xiaoman_user
            $newOwnerAccount = Helper::getBindAccountInfoBySellerId($this->params['new_owner_account_id']);
            if($newOwnerAccount){
                $this->assignImquiryUserId = $newOwnerAccount['user_id'];
            }else{
                //阿里店铺映射表对应的user_id
                $storeMemberListObj = new AlibabaStoreMembersList($this->clientId);
                $storeMemberListObj->setStoreId($this->storeId);
                $storeMemberListObj->setFields(['seller_email','user_id','seller_account_id']);
                $storeMemberList = $storeMemberListObj->find();
                foreach ($storeMemberList as $item){
                    if($item['seller_account_id'] == $this->params['new_owner_account_id']){
                        $this->assignImquiryUserId = $item['user_id'];
                    }
                }
            }
        }else{
            $this->assignImquiryUserId = $this->opUserId;
        }

        //检查分配询盘的线索是否存在本人私海/client 下私海/原跟进人私海
        if(!$this->checkAssignLeadExist()){
            //不存在，修改操作op_user 为新的new_owner_account_id
            if($this->assignImquiryUserId &&  $this->opUserId != $this->assignImquiryUserId){
                $this->opUserId = $this->assignImquiryUserId;
            }
            return false;
        }
        return true;
    }


    public function checkAssignLeadExist(){
        $leadCustomerList = [];
        //检查询盘业务id记录
        $alibabaTradeList = new AlibabaTradeRelationList($this->clientId);
        $alibabaTradeList->setEventId($this->params['business_id']);
        $list = $alibabaTradeList->find()??[];
        $leadCustomerIds = array_column($list,'lead_customer_id')??[];
        $leadIds = array_column($list,'lead_id')??[];
        if(!$list || !$leadCustomerIds){
            return false;
        }

        //询盘id关联原跟进人私海线索
        $privateLeadCustomerList = \common\library\lead\Helper::getPrivateLeadByCustomerIds($this->clientId,$this->opUserId,$leadCustomerIds);
        if(!$privateLeadCustomerList){
            $userNum = \common\library\privilege_v3\Helper::getUserNumByPrivilege($this->clientId, $this->opUserId, [PrivilegeConstants::PRIVILEGE_CRM_LEAD_POOL_TRANSFER, PrivilegeConstants::PRIVILEGE_CRM_LEAD_TRANSFER]);
            if($userNum){
                //原跟进人是否可以查看client 下之前线索数据
                $leadListObj = new LeadList($this->opUserId);
                $leadListObj->setUserNum($userNum);
                $leadListObj->showAll(true);
                $leadListObj->setLeadId($leadIds);
                $leadListObj->setFields(['lead_id']);
                $leadList = $leadListObj->find();
                if($leadList){
                    $leadIds = array_column($leadList,'lead_id')??[];
                    //获取线索的数据
                    $leadCustomerList = \common\library\lead\Helper::getLeadCustomerByLeadIds($this->clientId,$leadIds);
                }
            }
        }else{
            $leadCustomerList = $privateLeadCustomerList;
        }

        //线索绑定在新跟进人下面，仅仅更新最近联系时间时间
        if(!$leadCustomerList){

            $newOwnerLeadCustomerList = \common\library\lead\Helper::getPrivateLeadByCustomerIds($this->clientId,$this->assignImquiryUserId,$leadCustomerIds);
            if(!$newOwnerLeadCustomerList){
                return false;
            }
            $leadCustomerList = $newOwnerLeadCustomerList;
            $this->inquiryIsAssignedFlag = true;
        }

        //分配询盘新账号没有对应小满user， 或者是跟进人没有创建线索权限
        if(!$this->assignImquiryUserId || (!\common\library\privilege_v3\Helper::hasPermission($this->clientId, $this->assignImquiryUserId, PrivilegeConstants::PRIVILEGE_CRM_LEAD_CREATE) && !$this->inquiryIsAssignedFlag)){
            return false;
        }
        $this->assignImquiryLeadList = $leadCustomerList;
        return true;
    }

    //检查买家id是否被建档本人私海客户/私海线索
    protected function checkBuyerIdExistPrivate()
    {
        list($customer, $leadCustomer, $alibabaTradeRelationListResult) = \common\library\alibaba\trade\Helper::getPrivateDataByMemberId($this->clientId,$this->opUserId,$this->buyerAccountId);
        $this->alibabaTradeRelationListResult = $alibabaTradeRelationListResult;

        //对应客户/线索的来源店铺不为空，且不等于该买家的来源店铺，走创建线索逻辑
        foreach (($customer??[]) as $item){
            $aliStoreIds =  PgsqlUtil::trimArray($item['ali_store_id']);
            if($aliStoreIds && in_array($this->storeId,$aliStoreIds)){
                $this->buyIdPrivateCompanyList = $customer;
                return true;
            }
        }
        foreach (($leadCustomer??[]) as $item){
            if($item['store_id'] == $this->storeId){
                $this->buyIdPrivateLeadList = $leadCustomer;
                return true;
            }
        }
        return false;
    }

    // 获取阿里买家信息
    public function getAlibabaBuyerInfo()
    {
        if($this->accessToken){
            $this->buyerMemberInfo = CustomerSyncHelper::getAlibabaBuyerInfo($this->accessToken, $this->buyerAccountId);
        }

        return true;
    }

    //是否获取到买家邮箱
    public function checkBuyerEmail(){
        if(!($this->params['buyer_email']??"")){
            $alibabaCustomerInfo =  \common\library\alibaba\customer\CustomerSyncHelper::getSyncAlibabaCustomerInfo($this->clientId, $this->buyerAccountId);
            if($alibabaCustomerInfo && isset($alibabaCustomerInfo['buyer_email']) && $alibabaCustomerInfo['buyer_email']){
                $this->params['buyer_email'] = $alibabaCustomerInfo['buyer_email'];
            } elseif (!empty($this->buyerMemberInfo['alibaba_buyer_account_id']) && $this->buyerAccountId == $this->buyerMemberInfo['alibaba_buyer_account_id']) {
                $this->params['buyer_email'] = $this->buyerMemberInfo['email'] ?? '';
            }
        }
        if (empty($this->params['buyer_email'])) {
            return false;
        }
        if(!\EmailUtil::isEmail($this->params['buyer_email'])){
            return false;
        }
        $this->buyerEmail = $this->params['buyer_email'];

        return true;
    }

    //获取不到阿里买家邮箱跟联系人信息，放入延时队列24小时后重试
    public function pushSyncCustomerDelayApply(){

        $delayTime = 60*60*24; //24小时

        //$delayTime = 60*5;//1分钟，test
        $delayMessageService = new DelayMessageService($this->clientId,$this->opUserId,$this->storeId);
        $this->params['store_id'] = $this->storeId;
        $params = json_encode($this->params);
        $params = [
            'params' => $params,
            '__process_until' => time()+$delayTime,
            'handler'    => 'LeadSyncCustomerHandler',
            'max_retry_count' =>1, //表示失败要重试的次数
            'delay_retry' => $this->params['delay_retry'] ?? 0 // 表示消费失败重新推进延时队列的次数
        ];
        $delayMessageService->pushQueue(Constant::DELAY_MESSAGE_TYPE_SYNC_CUSTOMER_FOR_LEAD,$params,$delayTime);

        $tradeId = $this->saveTrade();


        return ['buyer_account_id' => $this->buyerAccountId,'seller_account_id' => $this->sellerAccountId,'trade_id' => $tradeId];
    }


    //获取联系人姓名or公司名称or官网or手机or座机or社交平台
    public function checkBuyerContactInfo(){
        if(!$this->buyerMemberInfo){
            return false;
        }
        if($this->buyerMemberInfo['name'] || $this->buyerMemberInfo['company_name'] || $this->buyerMemberInfo['homepage'] || $this->buyerMemberInfo['fax'] ||
            $this->buyerMemberInfo['contact']|| $this->buyerMemberInfo['tel_list']){
            return true;
        }
        return false;
    }


    //检查线索是否开启自动化创建权限，阿里账号线索自动化创建功能
    public function checkLeadCreateClassifyPrivilege() {
       return \common\library\lead\Helper::checkLeadCreateClassifyPrivilege($this->clientId,$this->opUserId,$this->sellerAccountId,$this->messageType, $this->buyerMemberInfo ?? [], $this->accessToken ?? '', $this->buyerAccountId ?? '');
    }

    //客户来源店铺为空，或者来源店铺一致，更新客户。客户来源店铺不为空并且不一致，新建线索
    protected function checkCompanyExistSameOrEmptyStore(){
        $flag = false;
        $emptyStoreCompany = [];

        foreach ($this->privateCompanyList as $item){
            $aliStoreId = is_array($item['ali_store_id']) ? $item['ali_store_id']:PgsqlUtil::trimArray($item['ali_store_id']);
            if(!$emptyStoreCompany && !$aliStoreId){
                $emptyStoreCompany = $item;
            }
            if($aliStoreId && in_array($this->storeId,$aliStoreId)){
                $this->companyId = $item['company_id'];
                $this->customerId = $item['customer_id'];
                $flag = true;
                return $flag;
            }
        }
        if(!$flag && $emptyStoreCompany){
            $this->companyId = $emptyStoreCompany['company_id'];
            $this->customerId = $emptyStoreCompany['customer_id'];
            $flag = true;
            return $flag;
        }
        return $flag;
    }


    //检查邮箱是否被建档本人私海线索
    protected function checkBuyerEmailExistLead()
    {
        $leadList = \common\library\lead\Helper::getPrivateLeadCustomer($this->clientId,$this->opUserId, $this->params['buyer_email']);

        if($leadList){
            $this->privateLeadList = $leadList;
            return true;
        }
        return false;
    }

    /**
     * 未开启线索重复自动合并 检查邮箱是否被建档本人私海线索
     * 开启线索重复自动合并 检查买家信息是否被建档为线索
     * @return bool
     */
    protected function checkBuyerInfoExistLead()
    {
        $leadList = \common\library\lead\Helper::getPrivateLeadCustomer($this->clientId,$this->opUserId, $this->params['buyer_email']);

        if($leadList){
            $this->privateLeadList = $leadList;
            return true;
        }

        $buyerMemberInfo = [];
        //获取阿里买家信息
        if(!$this->buyerMemberInfo && $this->accessToken){
            $buyerMemberInfo = CustomerSyncHelper::getAlibabaBuyerInfo($this->accessToken,$this->buyerAccountId);
        }else if($this->buyerMemberInfo){
            $buyerMemberInfo = $this->buyerMemberInfo;
        }

        [$originId,] = $this->getOriginInfo($buyerMemberInfo);

        $handler = new ConvertHandler($this->clientId, $this->opUserId, $originId);
        $duplicateFlag = $handler->getDuplicateFlag();
        if (!$duplicateFlag) {
            return false;
        }

        //线索重复自动合并需求 按照来源流转配置字段扩大匹配范围
        [, $data] = $this->buildConvertData(0, 0, '', $buyerMemberInfo);
        $lead = $handler->matchLeadByField($data);
        if (!empty($lead)) {
            $lead['lead_customer_id'] = $lead['main_customer'];
            $this->privateLeadList = [$lead];
            return true;
        }
        return false;
    }


    //原本的线索来源店铺为空，或者是来源店铺一致 ，就去更新线索联系人信息
    protected function checkLeadExistSameOrEmptyStore(){
        $flag = false;
        if(!$this->privateLeadList && !$this->enterpriseLeadList){
            return $flag;
        }
        $leadList = $this->enterpriseLeadList?$this->enterpriseLeadList:$this->privateLeadList;
        $emptyStoreLead = [];
        foreach ($leadList as $item){
            if(!$emptyStoreLead && !$item['store_id']){
                $emptyStoreLead = $item;
            }
            if($item['store_id'] && $item['store_id'] == $this->storeId){
                $this->leadId = $item['lead_id'];
                $this->leadCustomerId = $item['lead_customer_id'];
                $flag = true;
                return $flag;
            }
        }
        if(!$flag && $emptyStoreLead){
            $this->leadId = $emptyStoreLead['lead_id'];
            $this->leadCustomerId = $emptyStoreLead['lead_customer_id'];
            $flag = true;
            return $flag;
        }
        return $flag;
    }

    //检查企业邮箱是否已经建档
    protected function checkEnterpriseMailArchive(){

        if(!$this->buyerEmail){
            return false;
        }
        $domain = \EmailUtil::getDomain($this->buyerEmail);
        $this->analyzeResult['data']['buyer_email_domain'] = $domain;

        //没匹配中常规邮箱，就是企业邮箱
        if(!\common\library\email\CommonDomain::check($domain)){
            $leadList = \common\library\lead\Helper::getPrivateLeadCustomer($this->clientId,$this->opUserId, $this->buyerEmail,'domain');
            if($leadList){
                $this->enterpriseLeadList = $leadList;
                return true;
            }
        }
        return false;
    }

    //添加客户联系人邮箱
    public function saveCompanyCustomer($companyId){

        if(!$this->buyerEmail){
            return false;
        }
        $customerId = 0;
        try{
            $buyerMemberInfo = [];
            if($this->accessToken){
                $buyerMemberInfo = CustomerSyncHelper::getAlibabaBuyerInfo($this->accessToken,$this->buyerAccountId);
            }
            $company = new Company($this->clientId,$companyId);
            if($company->isNew()){
                throw  new RuntimeException('company not exits!');
            }
            if(empty($company->ali_store_id)){
                $company->ali_store_id = [$this->storeId];
            }
            $customer = new Customer($this->clientId);
            $customer = $customer->loadByEmail($this->buyerEmail,$companyId);
            if(!$customer->isNew()){
                throw  new RuntimeException("customer:{$customer->customer_id} is already exits same email!");
            }

            $customer->name = $buyerMemberInfo['name']??"";
            $customer->gender = $buyerMemberInfo['gender']??"";
            $customer->email =  $this->buyerEmail??"";
            $customer->post =  $buyerMemberInfo['post']??"";
            if($buyerMemberInfo['avatar_url']??""){
                $imageList=[];
                if(  $imageFileId = Helper::downloadImage($this->clientId, $this->storeId??0,$this->buyerMemberInfo['avatar_url']??''))
                {
                    $imageList[] = $imageFileId;
                }
                $customer->image_list = $imageList;
            }
            $customer->contact = $buyerMemberInfo['contact']??"";
            $customer->main_customer_flag  =  $buyerMemberInfo['main_customer_flag']??0;
            $customer->tel_list = $buyerMemberInfo['tel_list']??[];
	        $customer->growth_level = Api::growthLevel(\Constants::TYPE_COMPANY)->getExtraIdByName($buyerMemberInfo['growth_level'] ?? '');
            $company->addCustomer($customer);
            $company->save();
            $customerId = $customer->customer_id;
        }catch (\Exception $e){
            \LogUtil::info("saveCompanyCustomerError [company_id:{$companyId}] error:{$e->getMessage()} params:".json_encode($this->params));
        }

        return $customerId;
    }

    private function updateRelationTimeFieldForLead() {
        $clientId = $this->clientId ?? 0;
        $buyerAccountId = $this->buyerAccountId ?? 0;
        $messageType = $this->messageType ?? 0;
        $tradeType = $this->tradeType ?? 0;
        $messageTime = $this->messageTime ?? date('Y-m-d H:i:s');

        $list = $this->alibabaTradeRelationListResult;
        // echo "list\n";
        // var_dump($list);
        if (empty($list)) {
            \LogUtil::info("针对 buyerAccountId[$buyerAccountId] 没有查到对应的 relation 记录");
            return;
        }
        // 只需要买家 id 关联的 leadIds
        $leadIds = array_filter(array_column($list,'lead_id'));
        // var_dump($leadIds);
        if (empty($leadIds)) {
            return;
        }
        //过滤非业务消息
        if (!in_array( $messageType,Constant::$messageTypeList)) {
            return;
        }
        if (empty($clientId) || empty($buyerAccountId) || empty($tradeType)) {
            return;
        }

        if (empty($messageTime)) {
            $messageTime = date('Y-m-d H:i:s');
        }
        \common\library\lead\Helper::updateTradeAndTmTime($clientId, $leadIds, $messageTime, $tradeType, $messageType);
    }

    public function leadAliTmTimeUpdateApply() {
        $this->updateRelationTimeFieldForLead();
        return true;
    }

    //将买家ID关联到客户, 线索的的对应联系人

	/**
	 * @throws \ProcessException
	 */
	public function leadAndCompanyUpdateApply()
    {
//        $companyId = 0;
//        $customerId = 0;
//        $leadId = 0;
//        $leadCustomerId = 0;
//        $insertCustomerFlag = true;
//        $newCustomerEmail = '';
//
//        //若同时匹配到客户和线索，则取客户
//        if($this->buyIdPrivateCompanyList){
//            foreach ($this->buyIdPrivateCompanyList as $item){
//                $companyId = $item['company_id']??0;
//                $customerId = $item['customer_id']??0;
//                //匹配邮箱是否存在
//                if($this->buyerEmail && $item['email'] == $this->buyerEmail){
//                    $insertCustomerFlag = false;
//                }
//            }
//        }
//        if(!$this->buyIdPrivateCompanyList && $this->buyIdPrivateLeadList){
//            foreach ($this->buyIdPrivateLeadList as $item){
//                //匹配邮箱是否存在
//                if($this->buyerEmail && $item['email'] == $this->buyerEmail){
//                    $insertCustomerFlag = false;
//                }
//
//                if($item['store_id'] != $this->storeId){
//                    continue;
//                }
//                $leadId = $item['lead_id']??0;
//                $leadCustomerId = $item['lead_customer_id']??0;
//
//            }
//            if($insertCustomerFlag){
//                $newCustomerEmail = $this->buyerEmail;
//            }
//        }

        //买家修改了邮箱，新建联系人
//        if($companyId && $insertCustomerFlag){
//            $customerId = $this->saveCompanyCustomer($companyId);
//        }else if($leadId && $insertCustomerFlag){
//            $leadData = $this->saveLead($leadId,$leadCustomerId,$newCustomerEmail);
//            //新增了邮箱，记录最新的邮箱的关系
//            if($newCustomerEmail && ($leadData['lead_customer_id']??0)){
//                $leadCustomerId = $leadData['lead_customer_id'];
//            }
//        }

		$data = $this->saveLead();

		if(!$data){
			return false;
		}

        $tradeId = $this->saveTrade();

        if(!$tradeId){
            return false;
        }
        $this->saveTradeRelation($tradeId, $this->companyId, $this->customerId, $this->leadId, $this->leadCustomerId);

        $this->updateOrderTime($this->companyId, $this->customerId, $this->leadId);

        $this->syncTrail($this->companyId, $this->customerId, $this->leadId, $this->leadCustomerId);

        $this->browserPush();

        return ['company_id' => $this->companyId,'customer_id' => $this->customerId,'lead_id' => $this->leadId,'lead_customer_id' => $this->leadCustomerId,'tread_id' => $tradeId];
    }


    //将买家ID关联到该客户的对应联系人
    public function companyUpdateApply()
    {

        $company = new Company($this->clientId,$this->companyId);
        if($company->isNew()){
            return false;
        }

        if(empty($company->ali_store_id)){
            $company->ali_store_id = [$this->storeId];
        }
        $company->save();
        //保存询盘结果
        $tradeId = $this->saveTrade();

        if(!$tradeId){
            return false;
        }
        $this->saveTradeRelation($tradeId, $this->companyId,$this->customerId);

        $this->updateOrderTime($this->companyId,$this->customerId);

        $this->syncTrail( $this->companyId,$this->customerId,0,0);

        $this->browserPush();

        return ['company_id' =>  $this->companyId,'customer_id' => $this->customerId,'tread_id' => $tradeId];
    }


    public function updateOrderTimeApply()
    {
        if(!$this->buyerEmail){
            return false;
        }
        $companyId = 0;
        $leadId = 0;
        $customerId = 0;
        $leadCustomerId = 0;
        if(!$this->updateCustomerOrderTimeFlag){
            $companyData = \common\library\customer\Helper::getPrivateCustomer($this->clientId,$this->opUserId, $this->buyerEmail,$this->storeId);
            $companyId = $companyData[0]['company_id']??0;
            $customerId = $companyData[0]['customer_id']??0;

        }
        if(!$this->updateLeadOrderTimeFlag){
            $leadData = \common\library\lead\Helper::getPrivateLeadCustomerByStoreId($this->clientId,$this->opUserId, $this->buyerEmail,$this->storeId);
            $leadId = $leadData['lead_id']??0;
            $leadCustomerId = $leadData['customer_id']??0;
        }
        if(!$customerId && !$leadCustomerId){
            $this->saveTrade();
            $this->browserPush();
            return false;
        }
        $tradeId = $this->saveTrade();
        if(!$tradeId){
            return false;
        }
        $this->saveTradeRelation($tradeId, $companyId,$customerId,$leadId,$leadCustomerId);

        $this->updateOrderTime($companyId,$customerId,$leadId);


        $this->syncTradeTrail($companyId,$customerId,$leadId,$leadCustomerId);

        $this->browserPush();

        return ['company_id' => $companyId,'customer_id' => $customerId,'lead_id' => $leadId,'lead_customer_id' => $leadCustomerId];
    }

    //推送消息
    public function sendNewMessageApply()
    {
        if(!$this->sendMessageFlag){
            $this->browserPush();
        }else{
            $this->browserPush(Browser::TYPE_NEW_ALIBABA_MESSAGE);
        }
        //推送桌面端
        $this->desktopPush();

        return true;
    }

    public function syncDynamicApply()
    {
        $companyData = \common\library\customer\Helper::getPrivateCustomer($this->clientId,$this->opUserId, $this->buyerEmail,$this->storeId);
        $companyId = $companyData[0]['company_id']??0;
        $customerId = $companyData[0]['customer_id']??0;

        $leadData = \common\library\lead\Helper::getPrivateLeadCustomerByStoreId($this->clientId,$this->opUserId, $this->buyerEmail,$this->storeId);
        $leadId = $leadData['lead_id']??0;
        $leadCustomerId = $leadData['customer_id']??0;

        $tradeId = $this->saveTrade();
        if(!$customerId && !$leadCustomerId){
            return false;
        }
        if(!$tradeId){
            return false;
        }
        $this->saveTradeRelation($tradeId, $companyId,$customerId,$leadId,$leadCustomerId);

        $this->updateOrderTime($companyId,$customerId,$leadId);

        $this->syncTradeTrail($companyId,$customerId,$leadId,$leadCustomerId);

        return ['company_id' => $companyId,'customer_id' => $customerId,'lead_id' => $leadId,'lead_customer_id' => $leadCustomerId];

    }

    public function replyBuyerTradeApply()
    {

        $companyId = 0;
        $customerId = 0;
        $leadId = 0;
        $leadCustomerId = 0;

        list($customer,$leadCustomer) = \common\library\alibaba\trade\Helper::getPrivateDataByMemberId($this->clientId,$this->opUserId,$this->buyerAccountId, $this->storeId);

        //对应客户/线索的来源店铺不为空，更新最近联系时间
        foreach (($customer??[]) as $item){
            $aliStoreIds =  PgsqlUtil::trimArray($item['ali_store_id']);
            if($aliStoreIds && in_array($this->storeId,$aliStoreIds)){
                $companyId = $item['company_id'];
                $customerId = $item['customer_id'];
            }
        }
        foreach (($leadCustomer??[]) as $item){
            if($item['store_id'] == $this->storeId){
                $leadId = $item['lead_id'];
                $leadCustomerId = $item['lead_customer_id'];
            }
        }
        $tradeId = $this->saveTrade();
        if(!$tradeId){
            return false;
        }
        if(!$customerId &&  !$leadCustomerId){
            return false;
        }
        $this->updateOrderTime($companyId,$customerId,$leadId);
        $this->syncMessageTrail($companyId,$customerId,$leadId,$leadCustomerId);

        if (!in_array( $this->messageType,Constant::$updateOrderTimeMap)) {

            $this->saveTradeRelation($tradeId, $companyId,$customerId,$leadId,$leadCustomerId);

            $this->syncTradeTrail($companyId,$customerId,$leadId,$leadCustomerId);
        }

        return ['company_id' => $companyId,'customer_id' => $customerId,'lead_id' => $leadId,'lead_customer_id' => $leadCustomerId];

    }


    //将买家ID更新到已有线索的对应联系人（若有多个线索满足条件，则取更新时间最晚的线索）
    public function leadUpdateApply()
    {

        $leadData = $this->saveLead();

        if(!$leadData){
            return false;
        }
        //保存询盘结果
        $tradeId = $this->saveTrade();

        $this->saveTradeRelation($tradeId,$this->companyId,$this->customerId,$this->leadId,$this->leadCustomerId);

        $this->updateOrderTime($this->companyId,$this->customerId,$this->leadId);

        $this->syncTrail($this->companyId,$this->customerId,$this->leadId,$this->leadCustomerId);

        $this->browserPush();

        return ['lead_id' => $this->leadId,'lead_customer_id' => $this->leadCustomerId,'tread_id' => $tradeId];
    }

	/**
	 * @throws \ProcessException
	 */
	public function saveLead(){

		$companyId = 0;
		$customerId = 0;
		$leadId = 0;
		$leadCustomerId = 0;
		$insertCustomerFlag = true;
		$newCustomerEmail = '';

		list($customerList, $leadCustomerList, , $tempCustomerIds) = \common\library\alibaba\trade\Helper::getPrivateDataByMemberId($this->clientId,$this->opUserId,$this->buyerAccountId);


		//若同时匹配到客户和线索，则取客户
		if($customerList){
			foreach ($customerList as $item){
				$companyId = $item['company_id']??0;
				$customerId = $item['customer_id']??0;
				//匹配邮箱是否存在
				if($this->buyerEmail && $item['email'] == $this->buyerEmail){
					$insertCustomerFlag = false;
				}
			}
		}
		if($leadCustomerList){
			foreach ($leadCustomerList as $item){
				//匹配邮箱是否存在
				if($this->buyerEmail && $item['email'] == $this->buyerEmail){
					$insertCustomerFlag = false;
				}

				if($item['store_id'] != $this->storeId){
					continue;
				}
				$leadId = $item['lead_id']??0;
				$leadCustomerId = $item['lead_customer_id']??0;

			}
			if($insertCustomerFlag){
				$newCustomerEmail = $this->buyerEmail;
			}
		}
        //如果线索邮箱是黑名单禁止新建
        if($this->buyerEmail){
            $isBlackMail = \common\library\customer\blacklist\Helper::match($this->clientId, $this->buyerEmail);
            if($isBlackMail){
                \LogUtil::info("leadIsBlack params:".json_encode($this->params));
                return false;
            }
        }

        $buyerMemberInfo = [];
        //获取阿里买家信息
        if(!$this->buyerMemberInfo && $this->accessToken){
            $buyerMemberInfo = CustomerSyncHelper::getAlibabaBuyerInfo($this->accessToken,$this->buyerAccountId);
        }else if($this->buyerMemberInfo){
            $buyerMemberInfo = $this->buyerMemberInfo;
        }

		//todo (hotfix处理，后续回来优化调整)
		$alibabaOriginList = [Origin::SYS_ORIGIN_ALIBABA_INQUIRY, Origin::SYS_ORIGIN_ALIBABA_NAME_CARD, Origin::SYS_ORIGIN_ALIBABA_RFQ, Origin::SYS_ORIGIN_ALIBABA_TM, Origin::SYS_ORIGIN_ALIBABA_ALIBABA, Origin::SYS_ORIGIN_EXHIBITION, Origin::SYS_ORIGIN_INTERNET];
		$sourceList = $buyerMemberInfo['source_list'] ?? [];
		$sourceList = \common\library\lead\Helper::findOrigin($sourceList);
		$originId = Origin::SYS_ORIGIN_ALIBABA_ALIBABA;
		foreach ($alibabaOriginList as $id){
			if(in_array($id, $sourceList)){
				$originId = $id;
				break;
			}
		}

		if(empty($originId)){
			$originId = Origin::SYS_ORIGIN_ALIBABA_ALIBABA;
		}

		if (empty($customerId)) {
			$configInfo = (new ConfigService($this->clientId, [$originId]))->configInfo();
			if (!empty($configInfo) && ($configInfo['convert_module'] == \Constants::TYPE_COMPANY)) {
				$tempCustomerList = \common\library\customer\Helper::getNotOneSelfCustomerByIds($this->clientId, $this->opUserId, $tempCustomerIds);
				if (!empty($tempCustomerList)) {
					return false;
				}
			}
		}

//		$customerList = [];
//		$lead = new Lead($this->clientId, $leadId);
//		if ($lead->isNew()) {
//			$lead->setDuplicateCheckScene(\common\library\lead\Constant::DUPLICATE_AUTO_SCENE);
//			$lead->setOperatorUserId($this->opUserId);
//			$lead->addUser($this->opUserId);
//			$lead->origin_list = [\common\library\setting\library\origin\Origin::SYS_ORIGIN_ALIBABA];
//			$lead->client_id = $this->clientId;
//			//新客户检查潜客运营来源
//			$service = new \common\library\alibaba\customer\TMAlibabaCustomerService($this->clientId, $this->opUserId);
//			if ($service->checkFromVisitorMarketing($this->params['store_id'], $this->params['buyer_account_id'])) {
//				$originList = $lead->origin_list ?? [];
//				array_push($originList, Origin::SYS_ORIGIN_SYSTEM_MARKETING);
//				$lead->origin_list = array_unique(array_filter($originList));
//			}
//			$lead->create_user_id = $this->opUserId;
//			$lead->is_archive = 1;
//		}
//		$domainName = \EmailUtil::getEmailPrefix($this->buyerEmail);
//
//		if ($buyerMemberInfo) {
//			$buyerMemberInfo['country'] = mb_strlen($buyerMemberInfo['country']) <= 16 ? $buyerMemberInfo['country'] : '';
//
//			//主页过长过滤掉参数部分
//			if (mb_strlen($buyerMemberInfo['homepage']) >= 255) {
//				$parseUrl = parse_url($buyerMemberInfo['homepage']);
//				$buyerMemberInfo['homepage'] = ($parseUrl['scheme'] ?? '') . ($parseUrl['host'] ?? '') . ($parseUrl['path'] ?? '');
//			}
//
//			$lead->name = $lead->name ? $lead->name : $buyerMemberInfo['name'];
//			$lead->company_name = $lead->company_name ? $lead->company_name : $buyerMemberInfo['company_name'];
//			$lead->country = $lead->country ? $lead->country : $buyerMemberInfo['country'];
//			$lead->province = $lead->province ? $lead->province : $buyerMemberInfo['province'];
//			$lead->city = $lead->city ? $lead->city : $buyerMemberInfo['city'];
//			$lead->address = $lead->address ? $lead->address : $buyerMemberInfo['address'];
//			$lead->fax = $lead->fax ? $lead->fax : $buyerMemberInfo['full_fax'];
//			$lead->homepage = $lead->homepage ? $lead->homepage : $buyerMemberInfo['homepage'];
//			$lead->tel_full = $lead->tel_full ? $lead->tel_full : $buyerMemberInfo['fax'];
//			$customer = (new LeadCustomer($this->clientId, $leadCustomerId));
//			if (!$newCustomerEmail) {
//				$customer->name = $customer->name ? $customer->name : $buyerMemberInfo['name'];
//				$customer->gender = $customer->gender ? $customer->gender : $buyerMemberInfo['gender'];
//				$customer->email = $customer->email ? $customer->email : $this->buyerEmail;
//				$customer->post = $customer->post ? $customer->post : $buyerMemberInfo['post'];
//
//				if (!$customer->image_list && $buyerMemberInfo['avatar_url']) {
//					$imageList = [];
//					if ($imageFileId = Helper::downloadImage($this->clientId, $this->storeId ?? 0, $buyerMemberInfo['avatar_url'] ?? '')) {
//						$imageList[] = $imageFileId;
//					}
//
//					$customer->image_list = $imageList;
//				}
//				$customer->contact = $customer->contact ?: $buyerMemberInfo['contact'];
//				$customer->main_customer_flag = $buyerMemberInfo['main_customer_flag'];
//				$customer->tel_list = $customer->tel_list ?: $buyerMemberInfo['tel_list'];
//			}
//
//			$customer->growth_level = Api::growthLevel(\Constants::TYPE_COMPANY)->getExtraIdByName($buyerMemberInfo['growth_level'] ?? '');
//
//			$customerList[] = $customer;
//
//		} else {
//			$lead->name = $lead->name ?? $this->buyerEmail;
//			$customer = (new LeadCustomer($this->clientId, $leadCustomerId));
//			if ($customer->isNew()) {
//				$customerData = [
//					'main_customer_flag' => 1,
//					'name' => $domainName,
//					'email' => $this->buyerEmail,
//					'tel_list' => [],
//					'post' => ""
//				];
//				$customer->name = $customerData['name'] ?? '';
//				$customer->email = $customerData['email'];
//				$customer->post = $customerData['post'] ?? '';
//				$customer->main_customer_flag = 1;
//				$customer->tel_list = $customerData['tel_list'] ?? [];
//			}
//			$customerList[] = $customer;
//		}
//
//		if ($newCustomerEmail) {
//			$customer = (new LeadCustomer($this->clientId));
//
//			$customer->growth_level = Api::growthLevel(\Constants::TYPE_COMPANY)->getExtraIdByName($buyerMemberInfo['growth_level'] ?? '');
//
//			if ($customer->isNew()) {
//				$customer->email = $newCustomerEmail;
//				$customer->name = $buyerMemberInfo['name'] ?? $domainName;
//				$customer->gender = $buyerMemberInfo['gender'] ?? "";
//				$customer->post = $buyerMemberInfo['post'] ?? "";
//				if ($buyerMemberInfo['avatar_url'] ?? "") {
//					$imageList = [];
//					if ($imageFileId = Helper::downloadImage($this->clientId, $this->storeId ?? 0, $this->buyerMemberInfo['avatar_url'] ?? '')) {
//						$imageList[] = $imageFileId;
//					}
//
//					$customer->image_list = $imageList;
//				}
//				$customer->contact = $buyerMemberInfo['contact'] ?? "";
//				$customer->tel_list = $buyerMemberInfo['tel_list'] ?? [];
//				$customerList[] = $customer;
//			}
//		}
//
//		$lead->store_id = $this->storeId ?: $lead->store_id;
//		$lead->archive_type = Lead::ARCHIVE_TYPE_AI;
//		$lead->setFieldEditType(AIFieldData::FIELD_EDIT_TYPE_BY_AI);
//		$lead->setCustomerList($customerList);
//
//		if ($lead->getDuplicateCheckScene() == \common\library\lead\Constant::DUPLICATE_AUTO_SCENE) {
//			$logLeadType = in_array($this->messageType, \common\library\alibaba\Constant::$insertLeadMessageTypeMap) ? \common\library\lead\Constant::LEAD_TYPE_TM_INQUIRY : \common\library\lead\Constant::LEAD_TYPE_TM_OTHER;
//			$lead->setAutoRecordId(\common\library\lead_v3\LeadWriteService::saveAutoSystemCreateLog($lead, $logLeadType, $this->opUserId, $lead->getMainCustomerEmail()));
//		}
//
//		$res = $lead->save();

		try {
			list($lead, $company) = $this->archiveByConvert($leadId, $leadCustomerId, $newCustomerEmail, $buyerMemberInfo, $companyId, $customerId);
		} catch (\Exception $exception) {
			\LogUtil::info("client_id:{$this->params['client_id']} user_id:{$this->params['op_user_id']} buyer_account_id:{$this->params['buyer_account_id']}  buyer_email:{$this->buyerEmail}  error:" . $exception->getMessage());
		}

		if (empty($lead) && empty($company)) {
			\LogUtil::info("leadInsertError client_id:{$this->params['client_id']} user_id:{$this->params['op_user_id']} buyer_account_id:{$this->params['buyer_account_id']}  buyer_email:{$this->buyerEmail}");
			return [];
		}

        $newLeadCustomerId = !empty($lead) ? \common\library\lead\Helper::getLeadCustomerId($this->clientId,$lead->lead_id,$this->buyerEmail) : 0;
		$newCustomerId = !empty($company) ? \common\library\lead\Helper::getCustomerId($this->clientId,$company->company_id,$this->buyerEmail) : 0;

		//更新客户中间表
		if(!empty($company)){
			$this->TMArchivingCustomer($company, $this->buyerAccountId, $newCustomerId);
		}

        $this->leadId = $lead->lead_id ?? 0;
        $this->leadCustomerId = $newLeadCustomerId ?? 0;
		$this->companyId = $company->company_id ?? 0;
		$this->customerId = $newCustomerId ?? 0;


		if($this->accessToken && !$buyerMemberInfo && $this->leadId){
            //获取不到阿里买家信息，放入延时队列
            $delayTime = 300; //5分钟
            // 加入延时队列
            $delayMessageService = new DelayMessageService($this->clientId,$this->opUserId,$this->storeId);
            $params = [
                'lead_id'    => $this->leadId,
                'lead_customer_id' => $newLeadCustomerId,
                'client_id'  => $this->clientId,
                'user_id'    => $this->opUserId,
                'store_id'   => $this->storeId,
                'buyer_account_id' => $this->buyerAccountId,
                'seller_account_id' => $this->sellerAccountId,
                '__process_until' => time() + $delayTime,
                'handler'    => 'LeadHandler',
                'max_retry_count' =>1, //表示失败要重试的次数
            ];
            $delayMessageService->pushQueue(Constant::DELAY_MESSAGE_TYPE_LEAD,$params,$delayTime);
        }

        // 上报神策
        if(!empty($lead) && $lead->isNew()) {
            //同步阿里询盘新建线索上报
            $sensorsEvent = new EventCreateCompany($this->clientId, $this->opUserId);
            $sensorsEvent->setParams([
                'platform_type' => EventCreateCompany::PLATFORM_WEB,
                'company_number' => 1,
                'action_type' => EventCreateCompany::ACTION_TYPE_ALI,
            ]);
            $sensorsEvent->report();
        }

        $data = [
            'lead_id' =>  $this->leadId,
            'lead_customer_id' => $this->leadCustomerId,
            'lead_name' => $lead->name ?? ($buyerMemberInfo['name'] ?? ''),
            'res' => !empty($lead),
        ];

        return $data;

    }

    //检查买家邮箱是否被建档于本人私海客户列表
    public function checkBuyerEmailExistCompany(){

        $customer = \common\library\customer\Helper::getPrivateCustomer($this->clientId,$this->opUserId, $this->buyerEmail);

        if($customer){
            $this->privateCompanyList = $customer;
            return true;
        }
        return false;
    }

    /**
     * 未开启线索重复自动合并 检查邮箱是否被建档本人私海客户
     * 开启线索重复自动合并 检查买家信息是否被建档为客户
     * @return bool
     */
    public function checkBuyerInfoExistCompany()
    {
        $customer = \common\library\customer\Helper::getPrivateCustomer($this->clientId,$this->opUserId, $this->buyerEmail);

        if($customer){
            $this->privateCompanyList = $customer;
            return true;
        }

        $buyerMemberInfo = [];
        //获取阿里买家信息
        if(!$this->buyerMemberInfo && $this->accessToken){
            $buyerMemberInfo = CustomerSyncHelper::getAlibabaBuyerInfo($this->accessToken,$this->buyerAccountId);
        }else if($this->buyerMemberInfo){
            $buyerMemberInfo = $this->buyerMemberInfo;
        }

        [$originId,] = $this->getOriginInfo($buyerMemberInfo);

        $handler = new ConvertHandler($this->clientId, $this->opUserId, $originId);
        $duplicateFlag = $handler->getDuplicateFlag();
        if (!$duplicateFlag) {
            return false;
        }

        //线索重复自动合并需求 按照来源流转配置字段扩大匹配范围
        [, $data] = $this->buildConvertData(0, 0, '', $buyerMemberInfo);
        $company = $handler->matchCompanyByField($data);
        if (!empty($company)) {
            $company['customer_id'] = $company['main_customer'];
            $this->privateCompanyList = [$company];
            return true;
        }
        return false;
    }


    //主账号分配自己询盘，转移询盘逻辑
    public function assignImquiryApply(){

        $leadIds = array_column($this->assignImquiryLeadList,'lead_id');

        //询盘是否已经分配给新跟进人
        if(!$this->inquiryIsAssignedFlag){
            //该私海线索分配给对应接收人并且发通知
            $operator = new LeadBatchOperator($this->opUserId);//原来线索的跟进人
            $operator->setParams(['lead_ids' => $leadIds]);
            $operator->getList()->setUserNum([1,2]);
            $operator->getList()->showAll(true, PrivilegeConstants::PRIVILEGE_CRM_LEAD_TRANSFER);
            //线索的新跟进人
            $operator->transfer($this->assignImquiryUserId, true, false, true);
        }
        $tradeId = $this->saveTrade();
        foreach ($this->assignImquiryLeadList as $item){
            //保存询盘关系
            $this->saveTradeRelation($tradeId,0,0,$item['lead_id'],$item['lead_customer_id']);

            if($this->inquiryIsAssignedFlag){
                $this->updateOrderTime(0,0,$item['lead_id']);
            }

        }

        // 推送app消息
        $this->appPush();

        return ['tread_id' => $tradeId,'assign_imquiry_lead' => $this->assignImquiryLeadList,'inquir_is_assigned_new_owner' => $this->inquiryIsAssignedFlag];

    }

    //新建线索，将买家ID关联到该线索的对应联系人
    public function leadInsertApply()
    {
        $leadData = $this->saveLead();
        if (!$leadData) {
            return false;
        }

		//保存询盘结果
		$tradeId = $this->saveTrade();
		//保存询盘关系
		$this->saveTradeRelation($tradeId,$this->companyId,$this->customerId,$this->leadId,$this->leadCustomerId);

		$this->updateOrderTime($this->companyId,$this->customerId,$this->leadId);
		//同步邮件动态
		$this->syncTrail($this->companyId,$this->customerId,$this->leadId,$this->leadCustomerId);

		if(!empty($this->leadId)){
//            //保存询盘结果
//            $tradeId = $this->saveTrade();
//            //保存询盘关系
//            $this->saveTradeRelation($tradeId,0,0,$this->leadId,$this->leadCustomerId);
//
//            $this->updateOrderTime(0,0,$this->leadId);
//
            //记录线索对应的Capture Card
            $this->saveCaptureCard($this->leadId,$leadData['lead_name'],$tradeId);
//            //同步邮件动态
//            $this->syncTrail(0,0,$this->leadId,$this->leadCustomerId);

            $pendingLeadCount = \common\library\lead\Helper::getPendingLeadCount($this->clientId,$this->opUserId);

            $pushLeadData = ['lead_id' => $this->leadId,'pending_lead_count' => $pendingLeadCount];

            $this->browserPush(Browser::TYPE_AI_CLASSIFY_NEW_LEAD,$pushLeadData);
        }
        return ['lead_id' => $this->leadId,'lead_customer_id' => $this->leadCustomerId,'tread_id' => $tradeId];
    }

    //记录线索对应的Capture Card
    protected function saveCaptureCard($leadId,$leadName,$tradeId){

        $capture = new CaptureCardLog($this->clientId, $leadId);
        $capture->biz_type = CaptureCard::BIZ_TYPE_LEAD;
        $capture->classify_type = Classify::MAIL_CLASSIFY_TYPE_INQUIRY;
        $capture->status = CaptureCard::STATUS_APPLY;
        $capture->type = CaptureCard::REASON_TYPE_BY_MAIL_CONTENT;
        $capture->setResult([
            'refer_type' => CaptureCard::REFER_TYPE_LEAD,
            'method' => CaptureCard::METHOD_CREATE,
            'name' => $leadName,
            'trade_id' => $tradeId,//表示是阿里询盘AI自动化创建
            'user_id' => $this->opUserId
        ]);
        $capture->save();

    }

    protected function saveTradeRelation($trade_id,$company_id = 0,$customer_id =0,$lead_id = 0,$lead_customer_id = 0){
        $nowTime = date('Y-m-d H:i:s',time());
        $alibabaTradeRelation = new \common\library\alibaba\trade\AlibabaTradeRelation($this->clientId);
        if(!$customer_id && !$lead_customer_id){
            \LogUtil::info("relationParamEmpty:".json_encode($this->params));
            return false;
        }
        if($company_id && $customer_id){
            $alibabaTradeRelation = $alibabaTradeRelation->loadByCustomerIdAndEventId($company_id, $customer_id, $this->businessId);
        }else if($lead_id && $lead_customer_id){
            $alibabaTradeRelation = $alibabaTradeRelation->loadByLeadCustomerIdAndEventId($lead_id, $lead_customer_id,$this->businessId);
        }

        if($alibabaTradeRelation->isNew()){
            $alibabaTradeRelation->event_id =  $this->businessId??0;
            $alibabaTradeRelation->relation_id = \ProjectActiveRecord::produceAutoIncrementId();
            $alibabaTradeRelation->create_time = $nowTime;
        }

        $alibabaTradeRelation->trade_id = $trade_id;

        $alibabaTradeRelation->lead_relate_time = ($lead_id && $alibabaTradeRelation->lead_id != $lead_id) ? $nowTime : $alibabaTradeRelation->lead_relate_time;
        $alibabaTradeRelation->company_relate_time = ($company_id && $alibabaTradeRelation->company_id != $company_id) ? $nowTime : $alibabaTradeRelation->company_relate_time;

        //如果有更新就更新
        $alibabaTradeRelation->company_id =  $company_id?$company_id:$alibabaTradeRelation->company_id;
        $alibabaTradeRelation->company_id = $alibabaTradeRelation->company_id??0;
        $alibabaTradeRelation->customer_id = $customer_id?$customer_id:$alibabaTradeRelation->customer_id;
        $alibabaTradeRelation->customer_id = $alibabaTradeRelation->customer_id??0;
        $alibabaTradeRelation->lead_id = $lead_id?$lead_id:$alibabaTradeRelation->lead_id;
        $alibabaTradeRelation->lead_id = $alibabaTradeRelation->lead_id??0;
        $alibabaTradeRelation->lead_customer_id = $lead_customer_id?$lead_customer_id:$alibabaTradeRelation->lead_customer_id;
        $alibabaTradeRelation->lead_customer_id = $alibabaTradeRelation->lead_customer_id??0;

        $alibabaTradeRelation->buyer_account_id = $this->params['buyer_account_id'];
        $alibabaTradeRelation->buyer_email = $this->params['buyer_email'];
        $alibabaTradeRelation->buyer_account_encrypt = $this->params['buyer_account_id_encrypt'];
        $alibabaTradeRelation->seller_account_id = $this->params['seller_account_id'];
        $alibabaTradeRelation->user_id = $this->opUserId;
        $alibabaTradeRelation->client_id = $this->clientId;

        // 记录阿里CompanyID
        if (!$alibabaTradeRelation->ali_company_id || !$alibabaTradeRelation->ali_customer_id) {
            $buyerMemberInfo = $this->buyerMemberInfo;
            if(!$buyerMemberInfo && $this->accessToken){
                $buyerMemberInfo = CustomerSyncHelper::getAlibabaBuyerInfo($this->accessToken, $this->buyerAccountId);
                !$buyerMemberInfo && \LogUtil::info("[saveTradeRelation] buyerMemberInfo empty, clientId: {$this->params['client_id']}, buyerAccountId: {$this->buyerAccountId}, accessToken: {$this->accessToken}");
            }
            $alibabaTradeRelation->ali_company_id  = $buyerMemberInfo['alibaba_company_id'] ?? 0;
            $alibabaTradeRelation->ali_customer_id = $buyerMemberInfo['alibaba_customer_id'] ?? 0;
            $alibabaTradeRelation->ali_company_detail_url = $buyerMemberInfo['detail_url'] ?? '';
            $alibabaTradeRelation->owner_account_id = $buyerMemberInfo['owner_account_id'] ?? 0;
            $alibabaTradeRelation->owner_email = $buyerMemberInfo['owner_email'] ?? '';
            $alibabaTradeRelation->ali_company_name = $buyerMemberInfo['company_name'] ?? '';
            $alibabaTradeRelation->growth_level = $buyerMemberInfo['growth_level'] ?? '';
        }

        $alibabaTradeRelation->store_id = $this->storeId??0;
        $alibabaTradeRelation->update_time = $nowTime;
        $alibabaTradeRelation->enable_flag = \common\library\alibaba\Constant::TYPE_ENABLE;
        $alibabaTradeRelation->visitor_scene = in_array($this->params['visitor_scene']??'', Constant::$visitorMarketingSceneMap)?$this->params['visitor_scene']:$alibabaTradeRelation->visitor_scene;
        $alibabaTradeRelation->visitor_mark_time = ($alibabaTradeRelation->visitor_mark_time == '1970-01-01 00:00:01' && in_array($this->params['visitor_scene']??'', Constant::$visitorMarketingSceneMap))?$nowTime:$alibabaTradeRelation->visitor_mark_time;
        $alibabaTradeRelation->sec_token = $this->params['sec_token'] ?? $buyerMemberInfo['sec_token'] ?? '';
        $alibabaTradeRelation->save();

        \LogUtil::info("saveTradeRelation client_id:{$this->params['client_id']} user_id:{$this->params['op_user_id']} relation_id:{$alibabaTradeRelation->relation_id} trade_id:{$trade_id} alibaba_company_id: {$alibabaTradeRelation->ali_company_id} growth_level: {$alibabaTradeRelation->growth_level} visitor_scene: {$alibabaTradeRelation->visitor_scene} sec_token: {$alibabaTradeRelation->sec_token}");
    }


    //保存阿里推送询盘消息
    protected function saveTrade()
    {
        $nowTime = date('Y-m-d H:i:s',time());
        $businessId =  $this->businessId??0;
        $secTradeId =  $this->businessIdEncrypt??"";
        $alibabaTrade = new \common\library\alibaba\trade\AlibabaTrade($this->clientId);
        if($secTradeId){
            $alibabaTrade->loadBySecTradeId($secTradeId);
        }
        $alibabaTrade->sec_trade_id= $secTradeId;
        $alibabaTrade->client_id= $this->clientId;
        $alibabaTrade->user_id = $this->opUserId;
        $alibabaTrade->business_id = $businessId;
        $alibabaTrade->business_type = $this->params['business_type'];
        $alibabaTrade->message_type =  $this->messageType;
        $alibabaTrade->buyer_account_id = $this->buyerAccountId??0;
        $alibabaTrade->buyer_account_encrypt = $this->params['buyer_account_id_encrypt'];
        $alibabaTrade->buyer_email = $this->buyerEmail;
        $alibabaTrade->seller_account_id = $this->sellerAccountId;
        $alibabaTrade->seller_email = $this->sellerEmail ??"";
        $alibabaTrade->store_id = $this->storeId??0;
        if($alibabaTrade->isNew()){
            $alibabaTrade->trade_id = \ProjectActiveRecord::produceAutoIncrementId();
            $alibabaTrade->create_time = $nowTime;
        }
        //阿里推送到top的时间
        if(isset($this->params['pub_time']) && $this->params['pub_time']){
            $createTimeStamp = strtotime($this->params['pub_time']);
        }else{
            $createTimeStamp = time();
        }

        $alibabaTrade->create_time_stamp = $createTimeStamp;
        $alibabaTrade->update_time = $nowTime;
        $alibabaTrade->enable_flag = \common\library\alibaba\Constant::TYPE_ENABLE;


        $alibabaTrade->save();

        $tradeId = $alibabaTrade->trade_id??0;
        $this->tradeId = $tradeId;

        //询盘沟通记录
        if(in_array($this->messageType,[Constant::MESSAGE_TYPE_IM,Constant::MESSAGE_TYPE_CARD_EXCHANGE])){

            \common\library\alibaba\statistics\Helper::saveAliInquiryStatistics($this->clientId,$this->opUserId, $this->sellerAccountId);

            //询盘通知，同一个账号一小时之内只发送一次
            $key = Constant::KEY_PREFIX_ALIBABA_APP_INQUIRY_MESSAGE.'_'.$this->opUserId.'_'.$this->sellerAccountId;
            if(\Yii::app()->cache->get($key)){
                \LogUtil::info("{$this->sellerAccountId} has sent a message within an hour");
            }else{
                // 推送app消息
                $this->appPush();

                \Yii::app()->cache->set($key,json_encode($this->params), 60*60);

            }
        }

        \LogUtil::info("saveTrade client_id:{$this->params['client_id']} user_id:{$this->params['op_user_id']} buyer_email:{$this->buyerEmail} trade_id:{$tradeId}");

        return $tradeId;

    }

    /**
     * @param string $type 推送消息类型
     * @param array $param
     */
    protected function browserPush($type = Browser::TYPE_NEW_ALIBABA_TRADE,array $param = [])
    {
        $data = [];
        switch ($type){
            //阿里询盘IM，提交表单，创建名片消息
            case Browser::TYPE_NEW_ALIBABA_TRADE:
    
                if (isset($this->tradeType) && $this->tradeType == Constant::FROM_SELLER_MESSAGE_FLAG) {
        
                    break;
                }
                
                $pushData = [
                    'seller_email' =>  $this->params['seller_account_email'],
                    'seller_account_id' =>  $this->params['seller_account_id']??0,
                    'buyer_account_id' => $this->params['buyer_account_id']??0,
                    'buyer_account_encrypt' => $this->params['buyer_account_id_encrypt']??"",
                    'sec_token' => $this->params['sec_token']??''
                ];
                $url =  Constant::ICBU_SERVICE_DOMAIN.Constant::ALI_PWD_URL.'?activeAccountId='.$pushData['buyer_account_id'].'&relevanceAccountId='.
                    $pushData['seller_account_id'] . '&activeAccountIdEncrypt=' . $pushData['buyer_account_encrypt'] . '&chatToken=' . $pushData['sec_token']
                    .'&from=xiaoman';

                $data = [
                    'url' => $url,
                    'seller_email' =>  $this->params['seller_account_email'],
                    'ali_username' => $this->params['ali_username'] ?? "",
                ];

                break;

            //自动化创建线索
            case Browser::TYPE_AI_CLASSIFY_NEW_LEAD:

                $data = [
                    'seller_email' =>  $this->params['seller_account_email'],
                    'lead_id' => $param['lead_id'],
                    'pending_lead_count' => $param['pending_lead_count'],
                    'sec_token' => $this->params['sec_token']??""
                ];
                break;

            //通知消息（非询盘，非系统消息）
            case Browser::TYPE_NEW_ALIBABA_MESSAGE:
    
                if (isset($this->tradeType) && $this->tradeType == Constant::FROM_SELLER_MESSAGE_FLAG) {
        
                    break;
                }
                
                
                $url = Constant::ICBU_SERVICE_DOMAIN . Constant::ALI_PWD_URL . '?relevanceAccountId=' . $this->params['seller_account_id'] . '&chatToken=' . ($this->params['sec_token'] ?? '')
                    .'&from=xiaoman';
                $data = [
                    'ali_username' => $this->params['ali_username'] ?? "",
                    'seller_email' =>  $this->params['seller_account_email'],
                    'url' => $url
                ];
                break;

            // 潜客运营消息
            case Browser::TYPE_VISITOR_MARKETING_NOTIFY:
                $url = Constant::ICBU_SERVICE_DOMAIN . Constant::ALI_PWD_URL . '?activeAccountId=' . $this->params['buyer_account_id'] . '&relevanceAccountId=' . $this->params['seller_account_id'] . '&activeAccountIdEncrypt=' . $this->params['buyer_account_id_encrypt'] . '&chatToken=' . ($this->params['sec_token'] ?? '')
                    .'&from=xiaoman';
                $data = [
                    'seller_email' =>  $this->params['seller_account_email'],
                    'url' => $url,
                    'pending_session_count' => $param['pending_session_count'],
                    'timeout_flag' => $param['timeout_flag'],
                ];
                break;

            default:
                break;

        }

        if(!empty($this->params['is_delay_message']) && in_array($type,[Browser::TYPE_NEW_ALIBABA_TRADE,Browser::TYPE_NEW_ALIBABA_MESSAGE])){
            $pushData = json_encode($data);
            \LogUtil::info("delay message intercept push data client_id:{$this->params['client_id']} user_id:{$this->params['op_user_id']} type:{$type} data:{$pushData}");
            return ;
        }

        $this->sendMessageFlag = true;
        !empty($data) && Browser::push($this->opUserId, $type, $data);

        $pushData = json_encode($data);
        \LogUtil::info("push data client_id:{$this->params['client_id']} user_id:{$this->params['op_user_id']} type:{$type} data:{$pushData}");
    }

    /**
     * 推送消息到桌面端
     */
    protected function desktopPush()
    {
        $data = [
            'type' => DesktopPush::ALIBABA_TYPE_NEW_MESSAGE,
            'seller_account_id' => $this->params['seller_account_id'],
            'seller_email' => $this->params['seller_account_email'],
            'pwa_url' => Constant::ICBU_SERVICE_DOMAIN . Constant::ALI_PWD_URL . '?relevanceAccountId=' . $this->params['seller_account_id'] . '&chatToken=' . ($this->params['sec_token'] ?? '')
                .'&from=xiaoman',
        ];
        DesktopPush::pushAlibabaData($this->clientId, $this->opUserId, $data);

        $pushData = json_encode($data);
        \LogUtil::info("DesktopPush data client_id:{$this->params['client_id']} user_id:{$this->params['op_user_id']} data:{$pushData}");
    }

    /**
     * 推送app消息
     * @param string $type 推送消息类型
     */
    protected function appPush($type = \common\library\notification\Constant::APP_TYPE_INQUIRY)
    {
        $pushParams = [];
        switch ($type){
            //阿里询盘消息
            case \common\library\notification\Constant::APP_TYPE_INQUIRY:

                $pushParams = [
                    'client_id' => $this->clientId,
                    'user_id' => $this->opUserId,
                    'type' => $type,
                    'title' => \Yii::t('alibaba', '你有新的阿里巴巴国际站消息'),
                    'content' => $this->sellerEmail,
                    'data' => [
                        'seller_account_id' => $this->sellerAccountId,
                        'buyer_id' => $this->buyerAccountId
                    ]
                ];

                break;

            default:
                break;

        }

        $settingKey = \common\library\setting\user\UserSetting::NOTIFICATION_APP_ALIBABA_SETTING;
        $setting = new \common\library\setting\user\UserSetting($this->clientId, $this->opUserId, $settingKey);
        $data = $setting->getValue();
        if(($data['inquiry_notice_switch']??0) && ($data['account_list']??[])){
            $accountMap = array_column($data['account_list'],'switch','seller_account_id');
            if(($accountMap[$this->sellerAccountId]??0)){

                PushHelper::pushAppAlibaba($pushParams);

                $pushData = json_encode($data);
                \LogUtil::info("appPush data client_id:{$this->clientId} user_id:{$this->opUserId} type:{$type} data:{$pushData}");
            }
        }

    }

    protected function updateOrderTime($companyId =0,$customerId = 0,$leadId =0){

        $checkRefer = '';
        $checkRecentFollowUpRefer = '';
        if($this->params['trade_type'] == Constant::FROM_BUYER_MESSAGE_FLAG){
            $checkRefer = 'refer_contact_receive_message';
        }else{
            $checkRefer = 'refer_contact_send_message';
            $checkRecentFollowUpRefer = 'refer_follow_contact_send_message';
        }
        //其他操作，已经更新客户联系时间，跳过不更新
        if($companyId && !$this->updateCustomerOrderTimeFlag){
            $this->updateCustomerOrderTimeFlag = true;
            $this->performanceCompanyIds = $companyId;
            \common\library\customer\Helper::updateOrderTime($this->clientId,$this->opUserId,$companyId,$customerId,$checkRefer,null,false,false,false);
        }

        //其他操作，已经更新线索联系时间，跳过不更新
        if($leadId && !$this->updateLeadOrderTimeFlag){
            $this->updateLeadOrderTimeFlag = true;
            \common\library\lead\Helper::updateOrderTime($this->clientId, $leadId, $checkRefer,null,$this->opUserId);
        }

        if (!empty($checkRecentFollowUpRefer)) {
            $this->performanceCompanyIds = $companyId;
            \common\library\customer\Helper::updateRecentFollowUpTime($this->clientId, $this->opUserId, $companyId, $checkRecentFollowUpRefer, null, false);
        }

        if ($checkRecentFollowUpRefer == 'refer_follow_contact_send_message' && \CustomerOptionService::checkRecentFollowUpReference($this->clientId, 'refer_follow_contact_send_message')) {
            \common\library\task\Helper::finishTask($this->clientId, [$companyId]);
        }
    }

    protected function updateCompanysOrderTime($companyIds, $customerId = 0, $time = null)
    {
        if ($this->params['trade_type'] == Constant::FROM_BUYER_MESSAGE_FLAG) {
            $checkRefer = 'refer_contact_receive_message';
        } else {
            $checkRefer = 'refer_contact_send_message';
        }

        // 更新客户联系时间
        if (!empty($companyIds))
        {
            \common\library\customer\Helper::updateOrderTime($this->clientId, $this->opUserId, $companyIds, $customerId, $checkRefer, $time, false, false, false);

            \LogUtil::info('updateCompanysOrderTimeSuccess', [
                'client_id' => $this->clientId,
                'user_id' => $this->opUserId,
                'company_ids' => $companyIds,
                'customer_id' => $customerId,
                'time' => $time,
                'check_refer' => $checkRefer,
            ]);
        }
    }

    protected function syncTradeTrail($company_id = 0,$customer_id = 0,$lead_id =0,$lead_customer_id = 0)
    {
        if(!$company_id && !$lead_id){
            return false;
        }
        $customerName = "";
        if($company_id && $customer_id){
            $customerName = \common\library\customer\Helper::getCustomerName($this->clientId,$company_id,$customer_id);
        } else if($lead_id && $lead_customer_id){
            $customerName =  \common\library\lead\Helper::getLeadCustomerName($this->clientId,$lead_id,$lead_customer_id);
        }
        $sellerName = \User::getUserObject($this->opUserId,$this->clientId)->getNickname();
        $store = new AlibabaStore($this->clientId,$this->storeId);
        $storeName = $store->store_name ?? '';

        //语音+视频
        if (in_array($this->messageType,Constant::$videoMap)){

            $accountIdMap = [$this->params['seller_account_id'],$this->params['buyer_account_id']];
            sort($accountIdMap);
            $accountIdStr = join('_',$accountIdMap);
            $key = Constant::KEY_PREFIX_ALIBABA_DYNAMIC_VIDEO.'_'.$accountIdStr;
            //如果一分钟之内有互相通讯，不更新动态
            if(\Yii::app()->cache->get($key)){
                \LogUtil::info("skipSaveDynamic:".$accountIdStr);
                return false;
            }
            $cacheData = json_encode($this->params);
            \Yii::app()->cache->set($key, json_encode($cacheData), 60);

            if($this->params['trade_type'] == Constant::FROM_BUYER_MESSAGE_FLAG){
                $content = "{$customerName}<{$this->buyerEmail}> 给 {$sellerName}<{$this->params['seller_account_email']}> 发起了语音/视频通话";
            }else{
                $content = "{$sellerName}<{$this->params['seller_account_email']}> 给 {$customerName}<{$this->buyerEmail}> 发起了语音/视频通话";
            }
            $trailType = TrailConstants::TYPE_REMARK_TEL;
        }else if(in_array( $this->messageType ,[Constant::MESSAGE_TYPE_IM,Constant::MESSAGE_TYPE_QUOTATION_CARD])){
            $mail = $this->params['seller_account_email'];
            if($this->params['trade_type'] == Constant::FROM_BUYER_MESSAGE_FLAG){
                $content = "买家【$customerName <{$this->buyerEmail}>】给【{$sellerName}】发来了一条询盘";
                $trailType = TrailConstants::TYPE_ALIBABA_RECEIVED_TRADE;
            }else{
                $content = "【{$sellerName}】回复了买家{$customerName} <{$this->buyerEmail}> 的询盘";
                $trailType = TrailConstants::TYPE_ALIBABA_REPLY_TRADE;
            }
        }else if($this->messageType == Constant::MESSAGE_TYPE_MARKETING_INQUIRY){
            $content = "{$sellerName} <{$this->params['seller_account_email']}> 发送了一个营销询盘给 {$customerName} <{$this->buyerEmail}>";
            $trailType = TrailConstants::TYPE_ALIBABA_MARKETING_TRADE;
        }else {
            return false;
        }

        $data = [
            'content' => $content,
            'seller_account_id' => $this->params['seller_account_id'],
            'buyer_account_id' => $this->params['buyer_account_id'],
            'buyer_account_encrypt' => $this->params['buyer_account_id_encrypt'],
            'business_id' => $this->businessId,
            'store_name' => $storeName
        ];

        if($company_id && $customer_id){

            $event = new AlibabaEvents();
            $event->setType($trailType);
            $event->setClientId($this->clientId);
            $event->setCreateUser($this->opUserId);
            $event->setCompanyId($company_id);
            $event->setCustomerId($customer_id);
            $event->setUserId($this->opUserId);
            $event->setReferId($this->tradeId);
            $event->setData($data);
            $event->run();
            $event->getTrailId();

        }
        if($lead_id && $lead_customer_id){
            $event = new AlibabaEvents();
            $event->setType($trailType);
            $event->setClientId($this->clientId);
            $event->setCreateUser($this->opUserId);
            $event->setLeadId($lead_id);
            $event->setUserId($this->opUserId);
            $event->setReferId($this->tradeId);
            $event->setLeadCustomerId($lead_customer_id);
            $event->setData($data);
            $event->run();
            $event->getTrailId();
        }

    }
    protected function syncTrail($company_id = 0,$customer_id = 0,$lead_id =0,$lead_customer_id = 0){

        try{
            $this->syncTradeTrail($company_id,$customer_id,$lead_id,$lead_customer_id);
            $this->syncMailTrail($company_id,$customer_id,$lead_id,$lead_customer_id);
            $this->syncMessageTrail($company_id,$customer_id,$lead_id,$lead_customer_id);

        }catch (\Exception $exception){
            \LogUtil::info("syncTrailError  user_id:{$this->params['op_user_id']}
            company_id:{$company_id} customer_id:{$customer_id} lead_id:{$lead_id} lead_customer_id:{$lead_customer_id} error:{$exception->getMessage()}");
        }
    }

    protected function syncMailTrail($company_id = 0,$customer_id = 0,$lead_id =0,$lead_customer_id = 0){

        $mailId =0;
        if( !empty($this->businessIdEncrypt) )
        {
            $mailId = Helper::getMailIdBySecTradeId($this->clientId,$this->businessIdEncrypt);
        }

        if( !$mailId )
        {
            $mailId = Helper::getMailIdByTradeEmail($this->clientId,$this->sellerEmail,$this->buyerEmail);
        }

        if(!$mailId)
        {
            return false;
        }

        \LogUtil::info("syncMailTrail client_id:{$this->params['client_id']} user_id:{$this->params['op_user_id']} mail_id:{$mailId} company_id: $company_id lead_id:$lead_id ");


        $mail = new Mail($mailId);
        if($mail->isNew()) {
            \LogUtil::info("MailInfoEmpty client_id:{$this->params['client_id']} user_id:{$this->params['op_user_id']} mail_id:{$mailId} ");
            return false;
        }
        $aiMode = false;
        $hasOpportunity = \common\library\privilege_v3\PrivilegeService::getInstance($this->clientId)->hasFunctional(\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_OPPORTUNITY);
        if($company_id && $customer_id){
            //查询客户是否已经同步过邮件动态
            $list = new \common\library\trail\CompanyDynamicList($this->clientId,$company_id);
            $list->setOperatorUserId($this->opUserId);
            $list->setReferId($mailId);
            $list->setType(TrailConstants::TYPE_MAIL_RECEIVER);
            $companyDynamicList = $list->find();
            if(!$companyDynamicList){
                $trail = new \common\library\trail\events\MailEvents();
                $trail->setType( TrailConstants::TYPE_MAIL_RECEIVER);
                $trail->setClientId($this->clientId);
                $trail->setCreateUser($this->opUserId);
                $trail->setCompanyId($company_id);
                $trail->setCustomerId($customer_id);
                $trail->setReferId($mailId);
                $trail->setUserId($this->opUserId);
                $trail->setCreateTime($mail->receive_time);
                $trail->setReplyToMailId($mail->reply_to_mail_id);
                $trail->setMail($mail);
                $trail->setAiMode($aiMode);
                if (!$hasOpportunity) {
                    $trail->setSaveCustomerTrail(true);
                }
                $trail->run();
            }
        }
        if($lead_id && $lead_customer_id){
            //查询线索是否已经同步过邮件动态
            $list = new \common\library\trail\LeadDynamicList($this->clientId,$lead_id);
            $list->setOperatorUserId($this->opUserId);
            $list->setReferId($mailId);
            $list->setType(TrailConstants::TYPE_MAIL_RECEIVER);
            $leadDynamicList = $list->find();
            if(!$leadDynamicList){
                $trail = new \common\library\trail\events\MailEvents();
                $trail->setType( TrailConstants::TYPE_MAIL_RECEIVER);
                $trail->setClientId($this->clientId);
                $trail->setCreateUser($this->opUserId);
                $trail->setLeadId($lead_id);
                $trail->setLeadCustomerId($lead_customer_id);
                $trail->setReferId($mailId);
                $trail->setUserId($this->opUserId);
                $trail->setCreateTime($mail->receive_time);
                $trail->setReplyToMailId($mail->reply_to_mail_id);
                $trail->setMail($mail);
                $trail->setAiMode($aiMode);
                if (!$hasOpportunity) {
                    $trail->setSaveCustomerTrail(true);
                }
                $trail->run();

            }
        }
    }

    protected function syncMessageTrail($company_id = 0, $customer_id = 0, $lead_id = 0, $lead_customer_id = 0)
    {
        $sellerAccountId = $this->params['seller_account_id'];
        $buyerAccountId = $this->params['buyer_account_id'];
        $messageTime = $this->params['message_time'];

        // crc32 算法发生冲突的概率是1/2^32，即大约40亿分之一，在这个场景中应该不会产生重复数据
        $trailReferId = intval(base_convert(hash('crc32', "{$sellerAccountId}{$buyerAccountId}"), 16, 10));

        $data = [
            'seller_account_id' => $this->params['seller_account_id'],
            'seller_account_email' => $this->params['seller_account_email'],
            'buyer_account_id' => $this->params['buyer_account_id'],
            'buyer_email' => $this->params['buyer_email'],
            'trade_type' => $this->params['trade_type'],
            'store_id' => $this->params['store_id'],
            'message_time' => $this->params['message_time']
        ];


        $buyerMemberInfo = [];
        //获取阿里买家信息
        if(!$this->buyerMemberInfo && $this->accessToken){
            $buyerMemberInfo = CustomerSyncHelper::getAlibabaBuyerInfo($this->accessToken,$this->buyerAccountId);
        }else if($this->buyerMemberInfo){
            $buyerMemberInfo = $this->buyerMemberInfo;
        }

        [$originId,] = $this->getOriginInfo($buyerMemberInfo);

        $handler = new ConvertHandler($this->clientId, $this->opUserId, $originId);
        $duplicateFlag = $handler->getDuplicateFlag();

        if ($company_id && ($customer_id || $duplicateFlag)) {
            try {
                $trail = new \common\library\trail\events\ContactMessageEvents();
                $trail->setType(TrailConstants::TYPE_CONTACT_MESSAGE_TM);
                $trail->setClientId($this->clientId);
                $trail->setCreateUser($this->opUserId);
                $trail->setCompanyId($company_id);
                $trail->setCustomerId([$customer_id]);
                $trail->setReferId($trailReferId);
                $trail->setData($data);
                $trail->setUserId($this->opUserId);
                $trail->setCreateTime($messageTime);
                $trail->run();
            } catch (\RuntimeException $e) {
                \LogUtil::info("客户聊天记录动态生成失败，seller_account_id={$sellerAccountId} , buyer_account_id={$buyerAccountId} " . $e->getMessage());
            }
        }

        if ($lead_id && $lead_customer_id) {
            try {
                $trail = new \common\library\trail\events\ContactMessageEvents();
                $trail->setType(TrailConstants::TYPE_CONTACT_MESSAGE_TM);
                $trail->setClientId($this->clientId);
                $trail->setCreateUser($this->opUserId);
                $trail->setLeadId($lead_id);
                $trail->setLeadCustomerId($lead_customer_id);
                $trail->setReferId($trailReferId);
                $trail->setData($data);
                $trail->setUserId($this->opUserId);
                $trail->setCreateTime($messageTime);
                $trail->run();
            } catch (\RuntimeException $e) {
                \LogUtil::info("客户聊天记录动态生成失败，seller_account_id={$sellerAccountId} , buyer_account_id={$buyerAccountId} " . $e->getMessage());
            }
        }

    }

    //潜客运营接待提醒
    public function visitorMarketingReceptionNotify(): array
    {
        $buyerIds = \common\library\alibaba\Helper::getPendingBuyerIds($this->clientId,$this->sellerAccountId,$this->params['message_time']);

        $pendingCount = in_array($this->buyerAccountId, $buyerIds) ? count($buyerIds) : count($buyerIds)+1;

        $pushData = ['buyer_account_id' => $this->buyerAccountId, 'pending_session_count' => $pendingCount, 'timeout_flag' => false];

        $this->browserPush(Browser::TYPE_VISITOR_MARKETING_NOTIFY,$pushData);

        //放入延时队列
        $delayTime = 300; //5分钟
        $delayMessageService = new DelayMessageService($this->clientId,$this->opUserId,$this->storeId);
        $params = [
            'client_id'  => $this->clientId,
            'user_id'    => $this->opUserId,
            'seller_account_id' => $this->sellerAccountId,
            'seller_account_email' => $this->params['seller_account_email'],
            'buyer_account_id' => $this->buyerAccountId,
            'sec_token' => $this->params['sec_token'] ?? '',
            'message_time' => $this->params['message_time'],
            '__process_until' => time() + $delayTime,
            'handler'    => 'VisitorMarketingNotifyHandler',
            'max_retry_count' =>1, //表示失败要重试的次数
        ];

        $delayMessageService->pushQueue(Constant::DELAY_MESSAGE_TYPE_VISITOR_MARKETING_NOTIFY, $params, $delayTime);

        return ['buyer_account_id' => $this->buyerAccountId,'pending_session_count' => $pendingCount];
    }


	/**
	 * @throws \ProcessException
	 */
	public function archiveByConvert($leadId = 0, $leadCustomerId = 0, $newCustomerEmail = '', $buyerMemberInfo = [], $companyId = 0, $customerId = 0)
	{

        list($originId, $data, $call, $cover) = $this->buildConvertData($leadId, $leadCustomerId, $newCustomerEmail, $buyerMemberInfo, $companyId, $customerId);
        $convertObjectList = (new ConvertService($this->clientId, $this->opUserId))->convert($originId, [$this->opUserId], $data, $call, $cover);

		return [$convertObjectList[\Constants::TYPE_LEAD] ?? null, $convertObjectList[\Constants::TYPE_COMPANY] ?? null];
	}


    public function getOriginInfo($buyerMemberInfo)
    {
        $alibabaOriginList = [Origin::SYS_ORIGIN_ALIBABA_INQUIRY, Origin::SYS_ORIGIN_ALIBABA_NAME_CARD, Origin::SYS_ORIGIN_ALIBABA_RFQ, Origin::SYS_ORIGIN_ALIBABA_TM, Origin::SYS_ORIGIN_ALIBABA_ALIBABA, Origin::SYS_ORIGIN_EXHIBITION, Origin::SYS_ORIGIN_INTERNET];
        $sourceList = $buyerMemberInfo['source_list'] ?? [];
        $sourceList = \common\library\lead\Helper::findOrigin($sourceList);
        $originId = Origin::SYS_ORIGIN_ALIBABA_ALIBABA;
        foreach ($alibabaOriginList as $id){
            if(in_array($id, $sourceList)){
                $originId = $id;
                break;
            }
        }

        if(empty($originId)){
            $originId = Origin::SYS_ORIGIN_ALIBABA_ALIBABA;
        }

        $originList = $sourceList ?? [];
        $service = new \common\library\alibaba\customer\TMAlibabaCustomerService($this->clientId, $this->opUserId);
        if($service->checkFromVisitorMarketing($this->params['store_id'],$this->params['buyer_account_id'])){
            $originList[] = Origin::SYS_ORIGIN_SYSTEM_MARKETING;
        }

        //会不会命中其他来源？
        if(empty($originList)) {
            $originList = [$originId];
        }

        $originList = in_array(Origin::SYS_ORIGIN_ALIBABA, $originList) ? [Origin::SYS_ORIGIN_ALIBABA] : $originList;

        return [$originId, $originList ?? []];
    }

    public function buildConvertData($leadId = 0, $leadCustomerId = 0, $newCustomerEmail = '', $buyerMemberInfo = [], $companyId = 0, $customerId = 0)
    {
        $customerList = [];
        $companyMap = [];

        list($originId, $originList) = $this->getOriginInfo($buyerMemberInfo);

        $companyMap['origin_list'] = array_unique(array_filter($originList));
        $companyMap['ali_store_id'] = [$this->storeId ?? 0];
        $domainName = \EmailUtil::getEmailPrefix($this->buyerEmail);

		if($buyerMemberInfo){
            [$tmpCompanyMap, $customerMap] = Helper::getLeadDataByAliAccountInfo($this->clientId, $this->storeId??0, $buyerMemberInfo);
            $companyMap = array_merge($tmpCompanyMap, $companyMap);
			$customerMap['lead_customer_id'] = $leadCustomerId;
			$customerMap['customer_id'] = $customerId;
			if(!$newCustomerEmail){
				$customerMap['email'] =  $this->buyerEmail;
			}
			$customerList[] = $customerMap;
		}else{
			$companyMap['name'] = $this->buyerEmail;
			$customerMap['lead_customer_id'] = $leadCustomerId;
			$customerMap['customer_id'] = $customerId;
			$customerMap['main_customer_flag'] = 1;
			$customerMap['name'] = $domainName;
			$customerMap['email'] = $this->buyerEmail;
			$customerMap['tel_list'] = [];
			$customerMap['post'] = '';
			$customerList[] = $customerMap;
		}

        if($newCustomerEmail){
            $customer = (new LeadCustomer($this->clientId));
            $customerMap = [];
            $customerMap['growth_level'] = Api::growthLevel(\Constants::TYPE_COMPANY)->getExtraIdByName($buyerMemberInfo['growth_level'] ?? '');

            if($customer->isNew()){
                $customerMap['email'] = $newCustomerEmail;
                $customerMap['name'] = $buyerMemberInfo['name']??$domainName;
                $customerMap['gender'] = $buyerMemberInfo['gender']??"";
                $customerMap['post'] = $buyerMemberInfo['post']??"";
                if($buyerMemberInfo['avatar_url']??""){
                    $imageList=[];
                    if(  $imageFileId = Helper::downloadImage($this->clientId, $this->storeId??0,$buyerMemberInfo['avatar_url']??''))
                    {
                        $imageList[] = $imageFileId;
                    }
                    $customerMap['image_list'] = $imageList;
                }
                $customerMap['contact'] = $buyerMemberInfo['contact']??"";
                $customerMap['tel_list'] = $buyerMemberInfo['tel_list']??[];
                $customerList[] = $customerMap;
            }
        }

        $companyMap['store_id'] = array($this->storeId ?? 0);
        $companyMap['archive_type'] = Lead::ARCHIVE_TYPE_AI;
        $companyMap['create_user_id'] = $this->opUserId;

        if ($leadId) {
            $companyMap['lead_id'] = $leadId;
        }

        if ($companyId) {
            $companyMap['company_id'] = $companyId;
        }


        $companyMap['source_detail'] = [
            'type'        => ConvertHandler::SOURCE_TYPE_TM,
            'relate_info' => [
                'buyer_id'          => $this->buyerAccountId,
                'store_id'          => $this->storeId,
                'seller_account_id' => $this->sellerAccountId,
            ],
        ];


        $data = [
            'company'  => $companyMap,
            'customer' => $customerList,
        ];

        $call = [
            7 => [
                'setFieldEditType' => [AIFieldData::FIELD_EDIT_TYPE_BY_AI],
            ],

            4 => [
                'setFieldEditType' => [AIFieldData::FIELD_EDIT_TYPE_BY_AI],
                'setCreateScene' => [Company::CREATE_SCENE_AUTO]
            ],
        ];

        $cover = [
            'customer' => [
                'growth_level',
            ],
        ];

        return [$originId, $data ,$call, $cover];
    }


	/**
	 * @throws \Exception
	 */
	public function TMArchivingCustomer($company, $buyerAccountId, $customerId=0)
	{
		if (empty($company->getCustomerInfoList())){
			return ;
		}

		$customerId = $customerId ?: $company->getCustomerInfoList()[0]['customer_id'] ?? 0;

		if (empty($buyerAccountId) || empty($this->sellerAccountId) || empty($this->storeId) || empty($customerId)) {
			return ;
		}

		$aliCustomerInfo = $this->getAliCustomerInfo($this->storeId,$buyerAccountId);

		$growthLevel = $aliCustomerInfo['growth_level'] ?? '';

		$date = date('Y-m-d H:i:s');

		if(!empty($aliCustomerInfo['alibaba_company_id'])){

			$alibabaCompanyRelation = new AlibabaCompanyRelation($this->clientId);
			$alibabaCompanyRelation->loadByAlibabaCompanyId($aliCustomerInfo['alibaba_company_id']);
			if ($alibabaCompanyRelation->isNew()) {
				$alibabaCompanyRelation->client_id = $this->clientId;
				$alibabaCompanyRelation->alibaba_company_id = $aliCustomerInfo['alibaba_company_id'] ?? '';
				$alibabaCompanyRelation->company_id = $company->company_id;
				$alibabaCompanyRelation->store_id = $this->storeId;
				$alibabaCompanyRelation->buyer_account_id = $buyerAccountId;
				$alibabaCompanyRelation->owner_email = $aliCustomerInfo['owner_email'] ?? '';
				$alibabaCompanyRelation->owner_account_id = $aliCustomerInfo['owner_account_id'] ?? '';
				$alibabaCompanyRelation->alibaba_company_name = $aliCustomerInfo['company_name'] ?? '';
				$alibabaCompanyRelation->basic_info_allowable = $aliCustomerInfo['basic_info_allowable'] ?? '';
				$alibabaCompanyRelation->contact_info_allowable = $aliCustomerInfo['contact_info_allowable'] ?? '';
				$alibabaCompanyRelation->create_time = $date;
				$alibabaCompanyRelation->update_time = $date;
				$alibabaCompanyRelation->sync_time = $date;
				$alibabaCompanyRelation->is_add = 1;
				$alibabaCompanyRelation->sync_count = 0;
				$alibabaCompanyRelation->sync_status = Constant::SYNC_STATUS_FINISH;
				$alibabaCompanyRelation->is_protecting = $aliCustomerInfo['is_protecting'] ?? 0;
				$alibabaCompanyRelation->detail_url = $aliCustomerInfo['detail_url'] ?? '';
				$alibabaCompanyRelation->save();
			} else {

				//客户被删除了才更新关联
				$checkCompany = new \common\library\customer_v3\company\orm\Company($this->clientId, $alibabaCompanyRelation->company_id);
				if (!$checkCompany->isExist()) {
					$alibabaCompanyRelation->company_id = $company->company_id;
					$alibabaCompanyRelation->buyer_account_id = $buyerAccountId;
					$alibabaCompanyRelation->update_time = $date;
					$alibabaCompanyRelation->sync_count++;
					$alibabaCompanyRelation->sync_time = $date;
					$alibabaCompanyRelation->save();
				} else {
					$alibabaCompanyRelation->buyer_account_id = $buyerAccountId;
					$alibabaCompanyRelation->save();
				}
			}
		} else {
			//客户通侧还不是客户情况
			$alibabaCompanyRelation = new AlibabaCompanyRelation($this->clientId);
			$alibabaCompanyRelation->loadByCompanyId($company->company_id);
			if ($alibabaCompanyRelation->isNew()) {
				$alibabaCompanyRelation->client_id = $this->clientId;
				$alibabaCompanyRelation->alibaba_company_id = 0;
				$alibabaCompanyRelation->company_id = $company->company_id;
				$alibabaCompanyRelation->store_id = $this->storeId;
				$alibabaCompanyRelation->buyer_account_id = $buyerAccountId;
				$alibabaCompanyRelation->create_time = $date;
				$alibabaCompanyRelation->update_time = $date;
				$alibabaCompanyRelation->sync_time = $date;
				$alibabaCompanyRelation->is_add = 1;
				$alibabaCompanyRelation->sync_count = 0;
				$alibabaCompanyRelation->sync_status = Constant::SYNC_STATUS_FINISH;
				$alibabaCompanyRelation->save();
			}
		}

		// 保存customer_relation
		$alibabaCustomerRelation = new \common\library\alibaba\customer\AlibabaCustomerRelation($this->clientId);
		$alibabaCustomerRelation->loadByCustomerId($customerId);
		if ($alibabaCustomerRelation->isNew()) {
			$alibabaCustomerRelation->client_id = $this->clientId;
			$alibabaCustomerRelation->buyer_account_id = $buyerAccountId;
			$alibabaCustomerRelation->store_id = $this->storeId;
			$alibabaCustomerRelation->is_add = 1;
			$alibabaCustomerRelation->create_time = $date;
			$alibabaCustomerRelation->update_time = $date;
			$alibabaCustomerRelation->sync_count = 0;
			$alibabaCustomerRelation->alibaba_customer_id = 0;
			$alibabaCustomerRelation->customer_id = $customerId;
			$alibabaCustomerRelation->growth_level = $growthLevel;
			$alibabaCustomerRelation->save();
		}

		//记录买家跟卖家往来记录
		$alibabaChatSummary = new \common\library\alibaba\trade\AlibabaChatSummary($this->clientId);
		$alibabaChatSummary->loadBySellerAccountAndBuyerAccount($this->sellerAccountId, $buyerAccountId, $this->storeId);
		if ($alibabaChatSummary->isNew()) {
			$alibabaChatSummary->client_id = $this->clientId;
			$alibabaChatSummary->summary_id = \ProjectActiveRecord::produceAutoIncrementId($this->clientId);
			$alibabaChatSummary->seller_account_id = $this->sellerAccountId;
			$alibabaChatSummary->seller_email = "";
			$alibabaChatSummary->buyer_account_id = $buyerAccountId;
			$alibabaChatSummary->buyer_email = "";
			$alibabaChatSummary->store_id = $this->storeId;
			$alibabaChatSummary->count = 1;
			$alibabaChatSummary->create_time = $date;
			$alibabaChatSummary->update_time = $date;
			$alibabaChatSummary->save();
		}

		\LogUtil::info("[TM save Customer] client [$this->clientId] store [$this->storeId] seller_account_id [$this->sellerAccountId] buyer_account_id [$buyerAccountId] customer relation [$alibabaCustomerRelation->relation_id] chat summary [$alibabaChatSummary->summary_id]  growth_level [$alibabaCustomerRelation->growth_level] ");
	}


	/**
	 * @param $storeId
	 * @param $buyerAccountId
	 * @return array
	 */
	public function getAliCustomerInfo($storeId, $buyerAccountId) :array
	{
		if (empty($storeId) || empty($buyerAccountId)){
			return [];
		}

		$alibabaStore = new AlibabaStore($this->clientId);
		$alibabaStore->loadByStoreId($this->clientId, $storeId);
		$accessToken = $alibabaStore->access_token;

		if (empty($accessToken)) {
			return [];
		}

		$data = CustomerSyncHelper::getAlibabaBuyerInfo($accessToken, $buyerAccountId);

		//客户阶段映射
		if(!empty($data['trail_status'])){
			$alibabaStore = new \common\library\alibaba\store\AlibabaStore($this->clientId, $storeId);
			$customerSyncSetting = $alibabaStore->getCustomerSyncObj();
			$customerStatus =  json_decode($customerSyncSetting->customer_status, true)?:[];
			$customerStatusMap = array_column($customerStatus, 'status', 'ali_status');
			$statusId = $customerStatusMap[$data['trail_status']]??'';
		}
		$data['status_id'] = $statusId??'';

		return $data;

	}


	//公海客户需要沉淀聊天动态
	/**
	 * @throws \ProcessException
	 */
	public function TMSaveCompanyPoolMessage() :void
	{
		$companyId = $customerId = $leadId = $leadCustomerId = 0;

		//根据买家id查询，阿里推送询盘关联过私海线索/客户
		list($customerList, $leadCustomerList) = \common\library\alibaba\trade\Helper::getPoolCompanyDataByMemberId($this->clientId, $this->opUserId, $this->buyerAccountId, $this->storeId);
		//对应客户/线索的来源店铺不为空，更新最近联系时间
		foreach (($customerList??[]) as $item){
            $aliStoreIds = $item['ali_store_id'];
            // 兼容ali_store_id为array的情况
            if (is_string($aliStoreIds))
            {
                $aliStoreIds =  PgsqlUtil::trimArray($item['ali_store_id']);
            }

			if($aliStoreIds && in_array($this->storeId,$aliStoreIds)){
				$companyId = $item['company_id'];
				$customerId = $item['customer_id'];
				break;
			}
		}
		foreach (($leadCustomerList??[]) as $item){
			if($item['store_id'] == $this->storeId){
				$leadId = $item['lead_id'];
				$leadCustomerId = $item['lead_customer_id'];
				break;
			}
		}

		if (empty($customerId)) {
			return;
		}

		$tradeId = $this->tradeId ?? 0;
		if (empty($tradeId)) {
			$tradeId = $this->saveTrade();
		}

		$this->saveTradeRelation($tradeId,$companyId,$customerId,$leadId,$leadCustomerId);

		$this->updateOrderTime($companyId,$customerId,$leadId);

		$this->syncTrail($companyId,$customerId,$leadId,$leadCustomerId);

		$this->browserPush();

	}



    public function updateCompanyTimeField()
    {
        $clientId = $this->clientId;
        $buyerAccountId = $this->buyerAccountId;
        $messageType = $this->messageType;
        $tradeType = !empty($this->tradeType) ? $this->tradeType : 0;
        $messageTime = $this->messageTime;
        $storeId = $this->storeId;

        //过滤非业务消息
        if (!in_array( $messageType,Constant::$messageTypeList)) {
            \LogUtil::info("[updateCompanyRelationTimeField] Filter non business messages, message_type: {$messageType}");
            return false;
        }

        if (empty($clientId) || empty($buyerAccountId) || empty($tradeType)) {
            return false;
        }

        if (empty($messageTime)) {
            $messageTime = date('Y-m-d H:i:s');
        }

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $aliCompanyAccountSql = "select company_id,  buyer_account_id from tbl_alibaba_company_relation where client_id =$clientId and buyer_account_id = $buyerAccountId";
        if ($storeId) {
            $aliCompanyAccountSql .= " and store_id={$storeId}";
        }
        $aliCompanyData = $db->createCommand($aliCompanyAccountSql)->queryAll();
        if (empty($aliCompanyData)) {
//            \LogUtil::info("buyer_account-company_id"."aliCompanyData数据为空");
            return false;
        }

        $companyIds = array_filter(array_column($aliCompanyData,'company_id'));
        if (empty($companyIds)) {
//            \LogUtil::info("buyer_account-company_id"."companyId 数据为空");
            return false;
        }


        \common\library\customer\Helper::updateTradeAndTmTime($clientId, $companyIds, $messageTime, $tradeType, $messageType);

        $this->updateCompanysOrderTime($companyIds, $this->customerId, $messageTime);
    }


    public function saveAlibabaMessageStatistics()
    {
        try {
            \common\library\statistics\Helper::saveAlibabaMessageStatistics($this->clientId, $this->opUserId, $this->sellerAccountId, $this->tradeType);
        } catch (\Exception $e) {
            \LogUtil::error("tm统计eror:" . "$this->clientId, $this->opUserId,$this->sellerAccountId,{$this->tradeType}" . $e->getMessage());
        }
    }

    /*
     * 跟进建议作废埋点
     */
    public function abandonedFollowSuggestion()
    {
        try{
            $buyerAccountId = $this->params['buyer_account_id'] ?? '';
            $storeId = $this->storeId;

            \LogUtil::info("abandonedFollowSuggestion_Sns" . json_encode(['client_id' => $this->clientId,'sns_id' => $buyerAccountId, 'user_sns_id' => $storeId]));

            $messageService = new TmMessageService($this->clientId, $this->opUserId);
            $messageService->loadUserContact($buyerAccountId,$storeId,[]);
            $contact = $messageService->contact;
            $userId = $contact->getAttributes()['user_id'] ?? "";
            $customerId = $contact->getAttributes()['customer_id'] ?? "";
            $companyId = $contact->getAttributes()['company_id'] ?? "";
            $messageTime = $this->params['message_time'] ?? date("Y-m-d H:i:s");

            if(!empty($userId) && !empty($customerId) && !empty($companyId)) {
                \common\library\ai_agent\company_quality_check\Helper::abandonedFollowSuggestion($this->clientId, AiAgentConstants::AI_PORTRAIT_ABANDONED_TYPE_SNS,['send_time' => $messageTime],$userId,[$customerId],$companyId);
            }

        }catch (\Throwable $e){
            \LogUtil::error("abandonedFollowSuggestion_Error",[
                'client_id' => $this->clientId,
                'buyer_account_id' => $this->params['buyer_account_id'] ?? '',
                'store_id' => $this->storeId,
                'msg' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
    protected function buildSellerToBuyerMessageMap($messages, $sellerAccountId)
    {
        $sellerToBuyerMap = [];

        foreach ($messages as $message)
        {
            $fromAccountId = $message['from_account_id'];
            $toAccountId = $message['to_account_id'];
            $sellerAccountId = $fromAccountId === $sellerAccountId ? $fromAccountId : $toAccountId;

            if (!isset($sellerToBuyerMap[$sellerAccountId])) {
                $sellerToBuyerMap[$sellerAccountId] = [];
            }
            $sellerToBuyerMap[$sellerAccountId][] = $message;

        }
        return $sellerToBuyerMap;
    }

}
