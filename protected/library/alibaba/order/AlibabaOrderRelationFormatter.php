<?php
/**
 * Created by PhpStorm.
 * User: amu
 * Date: 2020-11-20
 * Time: 10:27
 */

namespace common\library\alibaba\order;


use common\library\account\UserInfo;
use common\library\alibaba\store\AlibabaStoreList;
use common\library\alibaba\store\AlibabaStoreService;
use common\library\object\field\formatter\Factory as FormatterFactory;
use common\library\setting\library\fund\relation\CostItemInvoiceRelationApi;
use common\library\util\PgsqlUtil;

class AlibabaOrderRelationFormatter extends \ListItemFormatter
{


    protected $clientId;
    protected $specifyFields;
    protected $showStoreName;

    public function __construct($clientId)
    {
        $this->clientId = $clientId;
    }

    public function setShowStoreName($showStoreName)
    {
        $this->showStoreName = $showStoreName;
    }


    public function setSpecifyFields($specifyFields)
    {
        $this->specifyFields = $specifyFields;
    }


    public function listInfoSetting()
    {
        $this->setSpecifyFields([
            'relation_id',
            'order_id',
            'store_id',
            'buyer_account_id',
            'owner_email',
            'owner_account_id',
            'buyer_account_id',
            'buyer_email',
            'sync_ali_order_flag',
            'amount',
            'currency',
            'product_total_amount',
            'product_currency',
            'shipment_insurance_fee',
            'shipment_insurance_currency',
            'shipment_fee_amount',
            'shipment_fee_currency',
            'fulfillment_channel',
            'alibaba_trade_id',
            'sync_ali_order_config',
            'shipping_address',
            'shipment_date',
            'initial_amount',
            'create_type',
            'ali_master_order_id',
            'trade_assurance_type',
            'cost_list',
            'sub_order_id',
            'service_fee_currency',
            'service_fee_amount',
            'service_fee_amount_rmb',
            'service_fee_amount_usd',
        ]);
        $this->setShowStoreName(true);

    }


    protected function format($data)
    {
        $result = $this->buildFieldsInfo($data);

        if($this->showStoreName){
            $result['store_name'] = $this->getMapData('store_map', ($data['store_id']??0))['store_name']??"";
        }
        //页面显示时候需要转移字符串
        if(isset($result['alibaba_trade_id'])){
            $result['alibaba_trade_id'] = strval($result['alibaba_trade_id'])??"";
        }
        if(isset($result['sync_ali_order_config'])){
            $result['sync_ali_order_config'] =is_array($result['sync_ali_order_config'])?$result['sync_ali_order_config']:PgsqlUtil::trimArray($result['sync_ali_order_config']);
        }

        if (isset($result['shipping_address'])) {
            $result['shipping_address'] = is_array($result['shipping_address']) ? $result['shipping_address'] : json_decode($result['shipping_address'], true);
        }

        if (isset($result['shipment_date'])) {
            $result['shipment_date'] = is_array($result['shipment_date']) ? $result['shipment_date'] : json_decode($result['shipment_date'], true);
        }

        if (isset($result['cost_list'])) {
            $cost_map = $this->mapData['cost_list_map'] ?? [];
            $result['cost_list'] = is_array($result['cost_list']) ? $result['cost_list'] : json_decode($result['cost_list'], true);
            foreach ($result['cost_list'] as &$cost) {
                $cost['cost'] = is_numeric($cost['cost'] ?? 0) ? ($cost['cost'] ?? 0) : 0;
                $cost['cost'] = $this->specialFieldFormat('cost', $cost['cost']);
                $cost['percent_amount'] = $this->specialFieldFormat('percent_of_total_amount', $cost['percent_amount']);
                if (!empty($cost['cost_item_relation_id'])) {
                    $costItemRelationInfo = $cost_map[$cost['cost_item_relation_id']] ?? [];
                    $cost['cost_name'] = $costItemRelationInfo['item_name'] ?? '';
                }
            }
        }
        return $result;
    }

    /**
     * @param $data
     * @return array
     * 返回数据格式的构建
     */
    protected function buildFieldsInfo($data)
    {

        if ($this->specifyFields == []) {
            $this->specifyFields = array_keys($data);
        }

        $result = \ArrayUtil::columns($this->specifyFields, $data, '');

        return $result;
    }

    public function buildMapData()
    {
        $mapData = [];

        $storeMap = AlibabaStoreService::getStoreInfoMaps($this->clientId,[],true);

        $costItemInvoiceRelationList = CostItemInvoiceRelationApi::getCostItemRelationByInvoice(\Constants::TYPE_ORDER, 1);
        $costItemRelationMap = array_column($costItemInvoiceRelationList, null, 'relation_id');

        $mapData = [
            'store_map' => $storeMap,
            'cost_list_map' => $costItemRelationMap
        ];
        $this->setMapData($mapData);
    }

    public function specialFieldFormat($field, $value)
    {
        if (!isset($this->fieldSetting[$field])) {
            return $value;
        }
        $formatter = FormatterFactory::MakeForSpecialField($this->clientId, $this->fieldSetting[$field]);

        if (!($formatter instanceof \common\library\object\field\formatter\FieldValueFormatter)) {
            return $value;
        }

        if ($formatter->filter($value)) {
            return $value;
        }

        return $formatter->format($value);
    }
}