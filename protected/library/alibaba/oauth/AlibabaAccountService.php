<?php
/**
 * Created by PhpStorm.
 * User: ganyaoyao
 * Date: 2020/7/3
 * Time: 14:48
 */
namespace common\library\alibaba\oauth;
use common\library\account\service\LoginService;
use \common\library\alibaba\Constant;
use common\library\alibaba\Helper;
use common\library\alibaba\services\AlibabaGopClient;
use common\library\alibaba\services\AlibabaTopClient;
use common\library\alibaba\store\AlibabaStore;
use common\library\DomainHelper;
use common\library\okki_personal\ClientService;
use common\library\okki_personal\PersonalAccountApi;
use RuntimeException;
use function Xiaoman\Sidekick\now;

class AlibabaAccountService {

    protected $clientId;
    protected $userId;
    protected $taobaoUserId;
    protected $accessToken;
    protected $refreshToken;
    protected $expiresIn;
    protected $sellerAccountId;
    protected $sellerEmail;
    protected $storeId;
    protected $storeName;
    protected $sellerNickname;
    protected $isAdmin;
    protected $oauthFlag;
    protected $redisKey;

    public const KYE_PREFIX = 'alibaba_store_auth_';

    public const ORIGIN_TYPE_CGS_UP = 'cgs_up';
    public const GOP_CLIENT_TAIL_KEY = 'key:gop_client_tail_key';


    protected $redirectUrl;
    protected $callbackUrl;

    public function __construct($clientId,$userId)
    {
        $this->clientId = $clientId;
        $this->userId = $userId;
    }

    public function setStoreId($storeId){
        $this->storeId = $storeId;
    }

    public function setStoreName($storeName){
        $this->storeName = $storeName;
    }

    public function setOauthFlag ($oauthFlag){
        $this->oauthFlag = $oauthFlag;
    }


    /**
     * cgs 授权优化使用了这个方法，可以不用通过getMemberInfo获取这些字段数据
     * @param $sellerId
     * @param $sellerEmail
     * @param $loginId
     * @param $storeId
     * @param $storeName
     * @param $isAdmin
     * @return void
     */
    public function setCgsUpField($sellerId,$sellerEmail,$loginId,$storeId,$storeName,$isAdmin): void
    {
        $this->sellerAccountId = $sellerId;
        $this->sellerEmail = $sellerEmail;
        $this->storeId = $storeId;
        $this->storeName = $storeName;
        $this->sellerNickname = $loginId;
        $this->isAdmin = $isAdmin;
    }

    /** 检查用户授权
     * @param $sellerAccountId
     * @param int $type 1 cgs 2 ggs
     * @return array
     */
    public function checkUserAuth($sellerAccountId, $checkCreditCodeFlag = 1, int $type = 1){
        $alibabaAccount = new AlibabaAccount($this->clientId,$this->userId);
        if($sellerAccountId){
            $alibabaAccount->loadBySellAccountId($sellerAccountId);
        }
        $alibabaAccount = $alibabaAccount->getAttributes();
        //如果绑定+授权，返回信息
        if($alibabaAccount && $alibabaAccount['oauth_flag'] == Constant::OAUTH_FLAG_BIND_AND_AUTH){
            $return = [
                'store_id' => $alibabaAccount['store_id'],
                'store_name' => $alibabaAccount['store_name'],
                'oauth_flag' => $alibabaAccount['oauth_flag'],
                'seller_account_id' => $alibabaAccount['seller_account_id'],
                'seller_email' => $alibabaAccount['seller_email']

            ];
            return $return;
       //返回绑定账号url
        }else {
            $this->getDoUserAuthUrl($sellerAccountId,$checkCreditCodeFlag, $type);
            $oauthFlag = $alibabaAccount['oauth_flag']??Constant::OAUTH_FLAG_INVALID;
            $return = [
                'redirect_url' => $this->redirectUrl,
                'callback_url' => $this->callbackUrl,
                'oauth_flag' => intval($oauthFlag),
                'key' => $this->redisKey
            ];
            return $return ;
        }

    }

    public  function getAuthUrl($isLogin = 1, $sellerEmail = '', $companyName = '', $creditCode='', int $type = 1) {

        $config = \Yii::app()->params['alibaba_config'];

        $encryptValue = $this->userId.','.$sellerEmail.','.$companyName.','.$creditCode;

        $req = \SecurityUtil::blowfishEncrypt($encryptValue,Constant::TRIPLE_DES_KEY);
        $req = urlencode($req);

        $key = Constant::COOKIES_KEY_BIND_ALI_ACCOUNT.":{$this->userId}:".time();
        $this->redisKey = self::KYE_PREFIX.md5($key);

        // state 参数会在授权回来之后带到回调链接里面
        // e.g.: /api/alibabaWrite/userAuthOauth?code=0_awfdPbbeJwLxjhoyUDdRGJW767653&state=*********
        // client_id|user_id
        $state = \SecurityUtil::base64Encrypt($this->clientId . '|' . $this->userId . '|' . $this->redisKey, Constant::TRIPLE_DES_KEY);

        $callBackUrl = Constant::ALIBABA_OAUTH_DOMAIN.'/authorize?response_type=code&client_id='.$config['app_key'].'&force_login=true&sp=icbu&state='.$state;

        $this->callbackUrl = $callBackUrl;

        $callBackUrl = urlencode($callBackUrl);

        $this->redirectUrl = Constant::LOGIN_ALIBABA_DOMAIN.'?from=xiaoman&version=1&req='.$req.'&return_url='.$callBackUrl;

        // 2 ggs，目前不放开，等会员侧发布了后放开
        if($type == 2){
            $this->redirectUrl = $this->redirectUrl.'&lang=en_US&flag=ggs';
        }
        $oauthFlag = Constant::OAUTH_FLAG_INVALID;

        $return = [];
        //绑定新账号时候 @todo 不管是新绑定还是重新绑定都设置缓存
//        if(!$sellerAccountId){
        $data =
            [
                'status' => Constant::COOKIES_STATUS_INIT,
                'error_code' => 0,
                'store_id' => 0,
                'store_name' => "",
                'oauth_flag' => $oauthFlag,
                'seller_account_id' => 0,
                'seller_email' => "",
                'client_id' => $this->clientId,
                'user_id' => $this->userId,
                'host' => DomainHelper::getCrmDomainUrl(),//@todo 来自internal接口请求的不一样对
                'is_login' => $isLogin,
                'type' => $type,
            ];
        \Yii::app()->cache->set($this->redisKey, json_encode($data), Constant::COOKIES_EXPIRE_TIME);
        // setCrmCookie会根据访问或者账号替换cookie设置顶级域名
        DomainHelper::setCrmCookie(Constant::COOKIES_KEY_BIND_ALI_ACCOUNT, $this->redisKey, time()+ Constant::COOKIES_EXPIRE_TIME, '/', true, false, 'None');
        // setcookie(Constant::COOKIES_KEY_BIND_ALI_ACCOUNT, $this->redisKey, time()+ Constant::COOKIES_EXPIRE_TIME, '/', LoginService::$cookieDomain);
        $return['key'] =  $this->redisKey;
//        }

        $return['redirect_url'] =  $this->redirectUrl;
        $return['callback_url'] =  $this->callbackUrl;
        $return['status'] =   Constant::COOKIES_STATUS_INIT;
        return $return;
    }


    //生成用户在线授权URL
    public function getDoUserAuthUrl($sellerAccountId, $checkCreditCodeFlag = 1, int $type =1) {

        $userInfo = \User::getUserObject($this->userId);

        $clientInfo = [];

        !empty($this->clientId) && $clientInfo = Helper::getClientVerificationInfo($this->clientId);

        $companyName = $clientInfo['company_name']??"";
        $creditCode = $clientInfo['credit_code']??"";

        if($checkCreditCodeFlag && (!$clientInfo || !$companyName || !$creditCode)){
            \LogUtil::info("getCreditInfoEmpty client_id:{$this->clientId} user_id:{$this->userId} clientInfo:".json_encode($clientInfo));
            throw  new RuntimeException(\Yii::t('alibaba', 'Failed to obtain business license'),\ErrorCode::CODE_ALIBABA_CREDIT_CODE_ERROR);
        }

        return $this->getAuthUrl(0, $userInfo->getEmail(), $companyName, $creditCode, $type);
    }

    //根据code 换取 access_token
    public function getAccessToken($code){
        $config = \Yii::app()->params['alibaba_config'];
        if (($config['platform'] ?? '') == 'gop' &&
            (\RedisService::cache()->exists(self::GOP_CLIENT_TAIL_KEY) && \RedisService::cache()->sIsMember(self::GOP_CLIENT_TAIL_KEY, $this->clientId % 10))) {
            return $this->getAccessTokenByGopClient($code);
        } else {
            return $this->getAccessTokenByTopClient($code);
        }
    }

    public function getAccessTokenByTopClient($code)
    {
        $client = AlibabaTopClient::getInstance();
        if (\Yii::app()->params['env'] == 'test') {
            $client->setClientTimeOut(10, 10);
        }
        $return = $client->createToken($code);

        if(!$return || !isset($return['token_result']) || !$return['token_result']){
            \LogUtil::info("getAccessTokenByTopClient getAccessTokenError client_id:{$this->clientId} user_id:{$this->userId} code:{$code} res:".json_encode($return));
            throw  new RuntimeException(\Yii::t('alibaba', 'Failed to obtain Taobao token'),\ErrorCode::CODE_ALIBABA_GET_TOKEN_ERROR);
        }
        $result = json_decode($return['token_result'],true);

        $expireTime = substr($result['expire_time'],0,10);
        $this->taobaoUserId = $result['user_id'];
        $this->refreshToken = $result['refresh_token'];
        $this->accessToken = $result['access_token'];
        $this->expiresIn = $expireTime;//过期时间戳

        \LogUtil::info("getAccessTokenByTopClient getAccessToken client_id:{$this->clientId} user_id:{$this->userId} code:{$code} res:".json_encode($result));

        return $result;
    }

    public function getAccessTokenByGopClient($code){

        $client = AlibabaGopClient::getInstance();
        $return = $client->createToken($code);

        if(!$return || !isset($return['access_token']) || !$return['access_token'] || !isset($return['country_user_info']['user_id'])){
            \LogUtil::info("getAccessTokenByGopClient getAccessTokenError client_id:{$this->clientId} user_id:{$this->userId} code:{$code} res:".json_encode($return));
            throw  new RuntimeException(\Yii::t('alibaba', 'Failed to obtain Taobao token'),\ErrorCode::CODE_ALIBABA_GET_TOKEN_ERROR);
        }

        $expireTime = time() + intval($return['expires_in'] ?? 0);
        $this->taobaoUserId = $return['country_user_info']['user_id'];
        $this->refreshToken = $return['refresh_token'];
        $this->accessToken = $return['access_token'];
        $this->expiresIn = $expireTime;//过期时间戳

        \LogUtil::info("getAccessTokenByGopClient getAccessToken client_id:{$this->clientId} user_id:{$this->userId} code:{$code} res:".json_encode($return));

        return $return;
    }


    /** 刷新token
     * @param $id
     * @return bool
     * @throws RuntimeException
     */
    public function refreshToken($id)
    {
        $config = \Yii::app()->params['alibaba_config'];
        if (($config['platform'] ?? '') == 'gop') {
            return $this->refreshTokenByGopClient($id);
        } else {
            return $this->refreshTokenByTopClient($id);
        }
    }

    public function refreshTokenByTopClient($id)
    {
        $res = false;
        $alibabaAccount = new \common\library\alibaba\oauth\AlibabaAccount($this->clientId,$this->userId);
        $alibabaAccount->loadById($id);
        if($alibabaAccount->isNew()){
            throw new \RuntimeException(\Yii::t('alibaba', 'Alibaba seller account does not exist'));
        }
        $token = $alibabaAccount->getAttributes(['refresh_token'])['refresh_token'];
        if(!$token){
            throw new \RuntimeException(\Yii::t('alibaba', 'Ali failed to return token'));
        }
        $return = AlibabaTopClient::getInstance()->refreshToken($token);

        if(empty($return['token_result'])){
            \LogUtil::error("refreshTokenByTopClient alibaba refreshToken id:{$id} return:". json_encode($return));
        }

        if($return && !empty($return['token_result'])){
            $result = json_decode($return['token_result'],true);
            $alibabaAccount->refresh_token = $result['refresh_token'];
            $alibabaAccount->access_token = $result['access_token'];
            $expireTime = substr($result['expire_time'],0,10);
            //$expireTime = time()+ Constant::EXPIRE_TIME;
            $alibabaAccount->expire_time = $expireTime;
            $alibabaAccount->update_time = date('Y-m-d H:i:s',time());
            $res =  $alibabaAccount->save();
        }
        return $res;
    }
    public function refreshTokenByGopClient($id)
    {
        $res = false;
        $alibabaAccount = new \common\library\alibaba\oauth\AlibabaAccount($this->clientId,$this->userId);
        $alibabaAccount->loadById($id);
        if($alibabaAccount->isNew()){
            throw new \RuntimeException(\Yii::t('alibaba', 'Alibaba seller account does not exist'));
        }
        $token = $alibabaAccount->getAttributes(['refresh_token'])['refresh_token'];
        if(!$token){
            throw new \RuntimeException(\Yii::t('alibaba', 'Ali failed to return token'));
        }
        $return = AlibabaGopClient::getInstance()->refreshToken($token);

        if(empty($return) || !isset($return['access_token'])){
            \LogUtil::error("refreshTokenByGopClient alibaba refreshToken id:{$id} return:". json_encode($return));
        }

        if($return && !empty($return['access_token'])){
            $alibabaAccount->refresh_token = $return['refresh_token'];
            $alibabaAccount->access_token = $return['access_token'];
            $expireTime = time() + intval($return['expires_in'] ?? 0);
            //$expireTime = time()+ Constant::EXPIRE_TIME;
            $alibabaAccount->expire_time = $expireTime;
            $alibabaAccount->update_time = date('Y-m-d H:i:s',time());
            $res =  $alibabaAccount->save();
        }
        return $res;
    }



    //根据taobao user id 获取阿里卖家信息
    public function getTaobaoMemberInfo(){

        $services = AlibabaTopClient::getInstance();
        if (\Yii::app()->params['env'] == 'test') {
            $services->setClientTimeOut(3, 3);
        }
        $memberInfo = $services->getMemberInfo($this->accessToken);

        if(!$memberInfo){
            \LogUtil::info("getMemberInfoError user_id:{$this->userId} token:{$this->accessToken}  ali_user_id:{$this->taobaoUserId}");
            throw  new RuntimeException(\Yii::t('alibaba', 'Failed to obtain Alibaba seller information'),\ErrorCode::CODE_ALIBABA_GET_MEMBER_INFO_ERROR);
        }

        \LogUtil::info("getMemberInfo user_id:{$this->userId} token:{$this->accessToken}  ali_user_id:{$this->taobaoUserId} res:".json_encode($memberInfo));

        $this->sellerAccountId = $memberInfo['account_id'];
        $this->sellerEmail = $memberInfo['email']??'';
        $this->storeId = $memberInfo['company_id'];
        $this->storeName = $memberInfo['company_name']??'';
        $this->sellerNickname = $memberInfo['login_id']??'';

        $this->isAdmin = $memberInfo['admin'] ? Constant::ALI_ADMIN_FLAG : Constant::ALI_USER_FLAG;

    }

    public function checkSellerAccountIdExist(){

        $bindStatusMap = [Constant::OAUTH_FLAG_BIND,Constant::OAUTH_FLAG_BIND_AND_AUTH];
        $bindStatusMap = join(',',$bindStatusMap);

        $sql = "select count(id) as total from tbl_alibaba_account where seller_account_id=  {$this->sellerAccountId} and oauth_flag in({$bindStatusMap}) and user_id != {$this->userId}";

        $count = \Yii::app()->db->createCommand($sql)->queryScalar();

        if(intval($count) >0){
            \LogUtil::info("accountExist client_id:{$this->clientId}  seller_account_id:{$this->sellerAccountId} user_id:{$this->userId}  count:{$count}");
            throw  new RuntimeException(\Yii::t('alibaba', 'Failed to obtain Alibaba seller information'),\ErrorCode::CODE_ALIBABA_ACCOUNT_EXIST_ERROR);
        }
    }

    public function checkStoreAuth($originType){
    
        if (($this->isAdmin == Constant::ALI_USER_FLAG) && ($originType != \AlibabaWriteController::ORIGIN_TYPE_ALICRM)) {
            
            $bindStatusMap = [Constant::OAUTH_FLAG_BIND,Constant::OAUTH_FLAG_BIND_AND_AUTH];
            $bindStatusMap = join(',',$bindStatusMap);
            $sql = "select count(id) as total from tbl_alibaba_account where client_id=  {$this->clientId} and store_id = {$this->storeId} and oauth_flag in({$bindStatusMap}) and is_admin =".Constant::ALI_ADMIN_FLAG;

            $count = \Yii::app()->db->createCommand($sql)->queryScalar();
            if(!$count){
                $unbindRes = [];
                if($this->sellerAccountId){
                    $client = \common\library\alibaba\services\AlibabaTopClient::getInstance();
                    $unbindRes = $client->unbindAccount($this->sellerAccountId,'');
                }
                $unbindRes = json_encode($unbindRes);
                \LogUtil::info("storeAuthError client_id:{$this->clientId}  user_id:{$this->userId}  store_id:{$this->storeId} seller_account_id:{$this->sellerAccountId} unbindRes:{$unbindRes}");
                throw  new RuntimeException(\Yii::t('alibaba', 'Alibaba store authorization does not exist. You need to guide the main account to authorize first'),\ErrorCode::CODE_ALIBABA_STORE_AUTH_NOT_EXITS);
            }
        }
    }


    public function authUserByCode($code) {

        $this->getAccessToken($code);
        $this->getTaobaoMemberInfo();
        if(!$this->sellerEmail) {
            throw new RuntimeException('login failed, email empty');
        }
        $personalAccountApi = new PersonalAccountApi();
        $data = $personalAccountApi->loginByAlibabaAccount(
            $this->storeId,$this->sellerAccountId,
            'login_gid',$this->storeName ?: 'empty_store_name',
            $this->sellerEmail,$this->sellerNickname,
            $this->isAdmin,$this->taobaoUserId);
        if(empty($data['token'])) {
            throw new RuntimeException('login failed, token empty');
        }
        return $data['token'];
    }



    /** 阿里卖家绑定
     * @param $code
     * @return array
     * @throws RuntimeException
     */
    public function activateUserAuth($code, $originType = \AlibabaWriteController::ORIGIN_TYPE_WEB){

        $this->getAccessToken($code);

        if($originType != self::ORIGIN_TYPE_CGS_UP) {
            //根据taobao user id 获取阿里卖家信息
            $this->getTaobaoMemberInfo();
            //检查店铺关联的主账号有没有授权
            $this->checkStoreAuth($originType);
        }

        //检查卖家id是否被授权
        $this->checkSellerAccountIdExist();

        //根据seller_account_id找到client id 下数据，变更user_id
        $alibabaAccount = new AlibabaAccount($this->clientId);
        $alibabaAccount->loadBySellAccountId($this->sellerAccountId);

        \LogUtil::info('activateUserAuth_AlibabaAccount', [
            $this->clientId, $this->sellerAccountId,
            $alibabaAccount->isNew() ? 'new' : 'old',
        ]);

        $store = new AlibabaStore($this->clientId);
        $store->loadBySellerAccountId($this->sellerAccountId);
        \LogUtil::info("store oauth_flag {$store->oauth_flag}");
        if (!$store->isNew() && $store->expire_time < time()) {
            // 只请求续约，不修改店铺过期时间，防止关闭窗口导致店铺过期时间已更新但是账号授权状态没更新的情况
            // 在阿里回调中会根据阿里传入过期时间值统一更新
            $client = AlibabaTopClient::getInstance();
            $client->extendAuthTime($store->access_token);
            \LogUtil::info("auto extend auth time, client_id: {$this->clientId} origin_expire_time: {$store->expire_time}");
        }

        $nowTime = date('Y-m-d H:i:s',time());
        $alibabaAccount->client_id = $this->clientId;
        $alibabaAccount->user_id = $this->userId;
        $alibabaAccount->seller_account_id = $this->sellerAccountId;
        $alibabaAccount->seller_email = $this->sellerEmail;
        $alibabaAccount->seller_nickname = $this->sellerNickname;
        $alibabaAccount->expire_time = $this->expiresIn;//过期时间
        if($alibabaAccount->isNew()){
            $alibabaAccount->create_time = $nowTime;
        }
        $alibabaAccount->enable_flag = Constant::AUTH_TYPE_ENABLE;
        $alibabaAccount->bind_time = $nowTime;
        //$alibabaAccount->account_expire_time =   strtotime('+'.Constant::EXPIRE_ACCOUNT_MONTH.' month',strtotime($nowTime));
        $alibabaAccount->update_time = $nowTime;
        if($this->storeId){
            $alibabaAccount->store_id = $this->storeId;
        }
        if($this->storeName){
            $alibabaAccount->store_name = $this->storeName;
        }
        $alibabaAccount->is_admin =  $this->isAdmin;
        //如果oauth_flag 不是等于3.绑定先把状态置为1，回调再更新授权成功
        if($alibabaAccount->oauth_flag  != Constant::OAUTH_FLAG_BIND_AND_AUTH){
            $alibabaAccount->oauth_flag = Constant::OAUTH_FLAG_BIND;
        }
        //因为cgs优化授权不需要走跳转，拿到code换token后直接认为授权成功
        if($originType == self::ORIGIN_TYPE_CGS_UP) {
            $alibabaAccount->oauth_flag = Constant::OAUTH_FLAG_BIND_AND_AUTH;
            $alibabaAccount->expire_time = $this->expiresIn;
        }
        $alibabaAccount->taobao_user_id = $this->taobaoUserId;
        $alibabaAccount->oauth_time = $nowTime;
        $alibabaAccount->access_token = $this->accessToken;
        $alibabaAccount->refresh_token = $this->refreshToken;
        $alibabaAccount->disable_flag = Constant::DISABLE_FLAG;
        $alibabaAccount->setUserId($this->userId);
        $alibabaAccount->save();

        // 授权成功后，触发同步TM历史聊天记录
        try {
            \common\library\ai_agent\Helper::addToSyncTmMessageList($this->clientId, [
                $this->sellerAccountId => $this->storeId
            ]);
        } catch (\Throwable $e) {
            \LogUtil::error("addToSyncTmMessageList_Error", [
                'clientId' => $this->clientId,
                'sellerAccountId' => $this->sellerAccountId,
                'exception' => $e->getMessage(),
            ]);
        }

        return $alibabaAccount->getAttributes();
    }

    /** 授权流程, access_token 失败
     * @param $token
     * @return int|void
     * @throws \Exception
     */
    public function accessTokenAuthInitFail($token)
    {
        if(!$token){
            return;
        }
        $alibabaAccount = new AlibabaAccount($this->clientId,$this->userId);
        $alibabaAccount->loadByClientAndAccessToken($token);
        if($alibabaAccount->isNew())
        {
            \LogUtil::info(sprintf("updateAuthAccountEmpty user_id[%s] token[%s]", $this->userId,$token));
            return;
        }
        $alibabaAccount->oauth_flag = Constant::OAUTH_FLAG_INVALID;
        $res = $alibabaAccount->save();
        if($alibabaAccount->is_admin == Constant::ALI_ADMIN_FLAG){
            $alibabaStore = new AlibabaStore($alibabaAccount->client_id, $alibabaAccount->store_id);
            $alibabaStore->cancelAuth(true);
        }
        \LogUtil::info(sprintf("updateAuthError user_id[%s] seller_account_id[%s] token[%s] res[%s]", $this->userId,$alibabaAccount->seller_account_id,$token,$res));
        return $alibabaAccount->seller_account_id;
    }

    /**
     * token失效处理
     * @param $accessToken
     */
    public  static  function accessTokenInvalid($accessToken)
    {
        $alibabaAccount =  new AlibabaAccount();
        $alibabaAccount = $alibabaAccount->loadByAccessToken($accessToken);
        if($alibabaAccount->isNew())
        {
            return ;
        }
        $alibabaAccount->cancelAuth();
        if($alibabaAccount->is_admin == Constant::ALI_ADMIN_FLAG)
        {
            //如果是管理员解除授权，店铺下的成员解除
            $alibabaStore  = new AlibabaStore($alibabaAccount->client_id,$alibabaAccount->store_id);
            $alibabaStore->invalidAuth();
        }

        \LogUtil::error("unbindAuthByToken:{$accessToken} user_id:{$alibabaAccount->user_id} seller_account_id:{$alibabaAccount->seller_account_id} is_admin:{$alibabaAccount->is_admin}");
    }

    /**
     * 绑定关系失效处理
     * @param $accessToken
     */
    public static function bindInvalid($accessToken)
    {
        $alibabaAccount =  new AlibabaAccount();
        $alibabaAccount = $alibabaAccount->loadByAccessToken($accessToken);
        if($alibabaAccount->isNew())
        {
            return ;
        }

        $alibabaAccount->cancelBind();
    }

    /** 解绑client绑定的阿里账号
     * @param $clientId
     * @param int $userId
     * @throws \CDbException
     * @throws \Exception
     * @throws \ProcessException
     */
    public static function cancelClientBind($clientId,$userId=0){

        $alibabaAccountList = new AlibabaAccountList($clientId);
        if($userId){
            $alibabaAccountList->setUserId($userId);
        }
        $alibabaAccountList->setFields(['id','user_id','oauth_flag','seller_account_id','is_admin','store_id']);
        $alibabaAccountList->setOauthFlag([Constant::OAUTH_FLAG_BIND,Constant::OAUTH_FLAG_AUTH,Constant::OAUTH_FLAG_BIND_AND_AUTH]);
        $list = $alibabaAccountList->find();
        foreach ($list as $item)
        {
            if( !$item['store_id'] )
                continue;
            $storeRes = null;
            $invalidFlag = true;//阿里账号回到原始状态
            $alibabaAccount = new AlibabaAccount($clientId,$item['user_id'],$item['id']);


            //主账号，取消店铺授权
            if( $item['is_admin'] == Constant::ALI_ADMIN_FLAG ){
                $alibabaStore = new \common\library\alibaba\store\AlibabaStore($clientId, $item['store_id']);
                $storeRes  = $alibabaStore->cancelAuth();
            }

            //子账号仅解绑，不需要取消授权
            if($item['is_admin'] != Constant::ALI_ADMIN_FLAG){
                $invalidFlag = false;
            }
            $alibabaAccount->cancelBind($invalidFlag);

            \LogUtil::info("[cancelClientBind {$clientId}] seller_account_id：{$alibabaAccount->seller_account_id} user_id:{$item['user_id']} id:{$item['id']} store_id:{$item['store_id']} store_res:{$storeRes}");

        }
    }

    public static function cancelClientStoreAuth($clientId,$storeId,$sellerAccountId){

        $alibabaAccountList = new AlibabaAccountList($clientId);
        $alibabaAccountList->setFields(['id','user_id','oauth_flag','seller_account_id','is_admin','store_id']);
        $alibabaAccountList->setStoreId($storeId);
        $alibabaAccountList->setOauthFlag([Constant::OAUTH_FLAG_BIND,Constant::OAUTH_FLAG_AUTH,Constant::OAUTH_FLAG_BIND_AND_AUTH]);
        $list = $alibabaAccountList->find();

        foreach ($list as $item)
        {
            $storeRes = null;
            $alibabaAccount = new AlibabaAccount($clientId,$item['user_id'],$item['id']);
            //主账号，取消店铺授权
            if( $item['is_admin'] == Constant::ALI_ADMIN_FLAG  && $item['seller_account_id'] == $sellerAccountId){
                $alibabaStore = new \common\library\alibaba\store\AlibabaStore($clientId, $item['store_id']);
                $storeRes  = $alibabaStore->cancelAuth();
            }
            //店铺管理员主账号取消绑定
            if($item['is_admin'] == Constant::ALI_ADMIN_FLAG && $item['seller_account_id'] == $sellerAccountId){
                $invalidFlag = true;//阿里账号回到原始状态
                $alibabaAccount->cancelBind($invalidFlag);
            }else{
                $alibabaAccount->cancelAuth();
            }
            \LogUtil::info("[cancelClientBind {$clientId}] seller_account_id：{$alibabaAccount->seller_account_id} user_id:{$item['user_id']} id:{$item['id']} store_id:{$item['store_id']} store_res:{$storeRes}");
        }
    }



    public function bindAliAccountFail($token,$sellerAccountId){
        //绑定授权失败，解绑crm 账号
        if($token){
            $sellerAccountId = $this->accessTokenAuthInitFail($token);
        }else if($sellerAccountId){
            $alibabaAccount = new \common\library\alibaba\oauth\AlibabaAccount($this->clientId,$this->userId);
            $alibabaAccount = $alibabaAccount->loadBySellAccountId($sellerAccountId);
            $alibabaAccount->cancelBind(true);
        }
        //解绑阿里账号
        if($sellerAccountId){
            $alibabaClient = \common\library\alibaba\services\AlibabaTopClient::getInstance();
            $unbindRes = $alibabaClient->unbindAccount($sellerAccountId,'');
            $unbindRes = json_encode($unbindRes)??"";
            \LogUtil::info("[cancelAliBind {$this->clientId}] seller_account_id：{$sellerAccountId} user_id:{$this->userId} res:{$unbindRes}");

        }
        return $sellerAccountId;
    }

    //获取有效（未过期）主账号
    public function getValidMainAccount($storeId)
    {
        $alibabaAccount = new \common\library\alibaba\oauth\AlibabaAccount($this->clientId,$this->userId);
        $alibabaAccount = $alibabaAccount->loadAdminByStoreId($storeId);
        if($alibabaAccount->expire_time > time()){
            return $alibabaAccount->getAttributes();
        }
        return [];
    }

    /**
     * @return mixed
     */
    public function getTaobaoUserId() {

        return $this->taobaoUserId;
    }

}
