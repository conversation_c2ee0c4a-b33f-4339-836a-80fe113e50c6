<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2020/11/13
 * Time: 11:25 AM
 */

namespace common\library\alibaba\services\queue;


use common\library\alibaba\Constant;
use common\library\alibaba\product\AlibabaProductSyncTask;
use common\library\CommandRunner;
use common\library\privilege_v3\PrivilegeService;

class QueueHelper
{
    const DELAY_QUEUE_KEY = 'alibaba:delay:handle:queue';
    const GREY_CLIENT_CUSTOM_HANDLER_QUEUE_KEY = 'alibaba:custom:handle:queue';
    const GREY_DEMOTION_CLIENT_CUSTOM_HANDLER_QUEUE_KEY = 'alibaba:custom:demotion:handle:queue:grey';

    const DEMOTION_CLIENT_CUSTOM_HANDLER_QUEUE_KEY = 'alibaba:custom:demotion:handle:queue';
    const SYNC_PRODUCT_QUEUE_KEY = 'alibaba:product:sync:store:list';
    const SYNC_CUSTOMER_QUEUE_KEY = 'alibaba:customer:sync:store:list';
    const SYNC_ORDER_QUEUE_KEY = 'alibaba:order:delay:sync:handle:queue';


    public static function getRedis($persistent=false,$reConstruct=false)
    {
        if( $persistent )
        {
            return \RedisService::queuePersistent($reConstruct);
        }else
        {
            $reConstruct  &&  \RedisService::release('redis_queue');
            return  \RedisService::queue();
        }
    }

    /**
     * 队列同步阿里客户
     * @param $clientId
     * @param $userId
     * @param $storeId
     * @param $buyerAccountId  int  买家id
     * @param $orderId int 传的话, 会自动关联订单
     * @param $alibabaCompanyId int 阿里客户id , 如果传了买家id,这个可以不传
     * @param $delayTime
     * @param $suffix
     * @param $syncType
     * @param $event
     * @param $messageId
     */
    public static function pushCustomerDelayHandle(
        $clientId,
        $userId,
        $storeId,
        $buyerAccountId,
        $orderId,
        $alibabaCompanyId,
        $delayTime,
        $suffix,
        $syncType = Constant::SYNC_TYPE_ORDER_CUSTOMER,
        $event = Constant::OKKI_SYNC_ORDER_CUSTOMER,
        $messageId = 0
    )
    {

        $processDelayTime = time() + $delayTime;
        $data = json_encode([
            'message_id' => $messageId,
            'order_id'    => $orderId,
            'client_id'  => $clientId,
            'user_id'    => $userId,
            'store_id'    => $storeId,
            'buyer_account_id' => $buyerAccountId,
            'alibaba_company_id' => $alibabaCompanyId,
            '__process_until' => $processDelayTime,
            'handler'    => 'CustomerDelayHandler',
            'max_retry_count' => 2,   //表示失败要重试的次数
            'sync_type' => $syncType,
            'event' => $event,
            'suffix' => $suffix
        ]);

        // 放入延时队列中
        self::getRedis()->zadd(self::DELAY_QUEUE_KEY,  [$data => $processDelayTime]);
    }

    public static function pushLeadDelayHandle( $clientId, $userId,$storeId, $leadId,$leadCustomerId, $buyerAccountId, $sellerAccountId, $delayTime, $suffix, $messageId = 0)
    {

        $processDelayTime = time() + $delayTime;
        $data = json_encode([
            'message_id' => $messageId,
            'lead_id'    => $leadId,
            'lead_customer_id' => $leadCustomerId,
            'client_id'  => $clientId,
            'user_id'    => $userId,
            'store_id'  => $storeId,
            'buyer_account_id' => $buyerAccountId,
            'seller_account_id' => $sellerAccountId,
            '__process_until' => $processDelayTime,
            'handler'    => 'LeadHandler',
            'max_retry_count' =>1, //表示失败要重试的次数
            'suffix' => $suffix,
        ]);

        // 放入延时队列中
        self::getRedis()->zadd(self::DELAY_QUEUE_KEY,  [$data => $processDelayTime]);
    }

    //获取不到阿里买家邮箱跟联系人信息，放入延时队列24小时后重试
    public static function pushSyncCustomerForLeadHandle($params, $delayTime, $suffix, $retry, $messageId = 0)
    {

        $processDelayTime = time() + $delayTime;
        $params = json_encode($params);
        $data = json_encode([
            'message_id' => $messageId,
            'params' => $params,
            '__process_until' => $processDelayTime,
            'handler'    => 'LeadSyncCustomerHandler',
            'max_retry_count' =>1, //表示失败要重试的次数
            'delay_retry' => $retry, // 表示消费失败重新推进延时队列的次数
            'suffix' => $suffix,
        ]);
        // 放入延时队列中
        self::getRedis()->zadd(self::DELAY_QUEUE_KEY,  [$data => $processDelayTime]);
    }

    public static function pushOrderRetryHandle($params)
    {
        $data = json_encode($params);
        // 放入延时队列中
        self::getRedis()->zadd(self::DELAY_QUEUE_KEY, [$data => $params['processDelayTime']]);
    }

    public static function pushCustomerSyncHandle($clientId, $userId, $storeId, $taskId)
    {
        if( !$userId )
        {
            $userId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        }
        $log = '/tmp/ali_customer_sync_task_'.$clientId;
        $params = [
            'client_id' => $clientId,
            'user_id' => $userId,
            'store_id' => $storeId,
            'task_id' => $taskId,
        ];
        list($exec, $output, $return) = CommandRunner::run(
            'alibaba',
            'syncCustomer',
            $params,
            $log,
            1,
        );

        \LogUtil::info("pushCustomerSyncHandle: client $clientId, user $userId, store $storeId, task $taskId out:".\json_encode($output));

//        $data = ['handler'=> 'CustomerSyncTaskHandler', 'client_id' => $clientId, 'user_id' => $userId, 'store_id' => $storeId, 'task_id' => $taskId];
//        $data = json_encode($data);
//        self::getRedis()->lpush(self::SYNC_CUSTOMER_QUEUE_KEY, [$data]);

        return true;
    }

    public static function pushProductSyncHandle($clientId, $userId, $storeId, $taskType= AlibabaProductSyncTask::TASK_TYPE_SYSTEM, $checkLastSyncTime = 1)
    {
        $lock = self::lockProductSyncTask($clientId, $storeId);
        if( !$lock )
            return false;

        if( !$userId )
        {
            $userId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        }

        $log = '/tmp/ali_product_sync_task_'.$clientId;
        list($exec, $output, $return) = CommandRunner::run(
            'alibaba',
            'syncProduct',
            [
                'clientId' => $clientId,
                'userId' => $userId,
                'storeId' => $storeId,
                'taskType' => $taskType,
                'checkLastSyncTime' => $checkLastSyncTime,
            ],
            $log,
            1,
        );

        \LogUtil::info("pushProductSyncHandle: client $clientId, user $userId, store $storeId, taskType $taskType, checkLastSyncTime $checkLastSyncTime out:".\json_encode($output));


//        $data = ['handler'=> 'ProductSyncTaskHandler','client_id' => $clientId, 'user_id' => $userId, 'store_id' => $storeId, 'task_type' => $taskType, 'check_last_sync_time' => $checkLastSyncTime];
//        $data = json_encode($data);
//        self::getRedis()->lpush(self::SYNC_PRODUCT_QUEUE_KEY, [$data]);

        return true;
    }

    public static function pushVisitorMarketingNotifyHandle(
        $clientId,
        $userId,
        $sellerAccountId,
        $sellerAccountEmail,
        $buyerAccountId,
        $messageTime,
        $delayTime,
        $suffix,
        $messageId = 0,
        $secToken = ''
    )
    {

        $processDelayTime = time() + $delayTime;
        $data = json_encode([
            'message_id' => $messageId,
            'client_id'  => $clientId,
            'user_id'    => $userId,
            'seller_account_id' => $sellerAccountId,
            'seller_account_email'    => $sellerAccountEmail,
            'buyer_account_id' => $buyerAccountId,
            'sec_token' => $secToken,
            'message_time' => $messageTime,
            '__process_until' => $processDelayTime,
            'handler'    => 'VisitorMarketingNotifyHandler',
            'max_retry_count' => 1,   //表示失败要重试的次数
            'suffix' => $suffix
        ]);
        // 放入延时队列中
        self::getRedis()->zadd(self::DELAY_QUEUE_KEY,  [$data => $processDelayTime]);
    }

    public static function lockProductSyncTask($clientId, $storeId)
    {
        //client_id + store_id频率限制60s可提交一次刷新任务
        $lockKey = "submit_alibaba_product_sync_limit_{$clientId}_{$storeId}";
        return self::getRedis()->set($lockKey, 1, 'Ex', 60,'NX');
    }

    public static function unlockProductSyncTask($clientId, $storeId)
    {
        $lockKey = "submit_alibaba_product_sync_limit_{$clientId}_{$storeId}";
        return self::getRedis()->del([$lockKey]);
    }


    public static function lockProductSyncTaskSync($clientId, $storeId)
    {
        //client_id + store_id限制 最长执行时间一小时
        $lockKey = "submit_alibaba_product_task_sync_limit_{$clientId}_{$storeId}";
        return self::getRedis()->set($lockKey, 1, 'Ex', 3600,'NX');
    }

    public static function unlockProductSyncTaskSync($clientId, $storeId)
    {
        $lockKey = "submit_alibaba_product_task_sync_limit_{$clientId}_{$storeId}";
        return self::getRedis()->del([$lockKey]);
    }



    public static function lockOrderSync($clientId, $aliOrderId)
    {
        //client_id + ali_order_id频率限制5s可提交一次刷新
        $lockKey = "sync_alibaba_order_limit_{$clientId}_{$aliOrderId}";
        return self::getRedis()->set($lockKey, 1, 'Ex', 5,'NX');
    }

    public static function unlockOrderSync($clientId, $aliOrderId)
    {
        $lockKey = "sync_alibaba_order_limit_{$clientId}_{$aliOrderId}";
        return self::getRedis()->del([$lockKey]);
    }

    public static function lockCustomerPushSync($clientId, $storeId, $aliCompanyId)
    {
        $lockKey = self::getCustomerPushLockKey($clientId, $storeId, $aliCompanyId);
        return self::getRedis()->set($lockKey, 1, 'Ex', 5,'NX');
    }


    public static function getProductSyncLockKey($clientId, $storeId, $aliProductId)
    {
        $lockKey = "sync_product_sync_limit_{$clientId}_{$storeId}_$aliProductId";
        return $lockKey;
    }

    public static function lockProductSync($clientId, $storeId, $aliProductId)
    {
        $lockKey = self::getProductSyncLockKey($clientId, $storeId, $aliProductId);
        return self::getRedis()->set($lockKey, 1, 'Ex', 5,'NX');
    }


    public static function unlockProductSync($clientId, $storeId, $aliProductId)
    {
        $lockKey = self::getProductSyncLockKey($clientId, $storeId, $aliProductId);
        return self::getRedis()->del([$lockKey]);
    }

    public static function unlockCustomerPushSync($clientId, $storeId, $aliCompanyId)
    {
        $lockKey = self::getCustomerPushLockKey($clientId, $storeId, $aliCompanyId);
        return self::getRedis()->del([$lockKey]);
    }

    public static function getCustomerPushLockKey($clientId, $storeId, $aliCompanyId)
    {
        $lockKey = "sync_customer_push_limit_{$clientId}_{$storeId}_$aliCompanyId";
        return $lockKey;
    }

    public static function pushCustomHandle($queueKey, $data)
    {
        if( empty($data) )
            return false;

        $data = json_encode($data);
        self::getRedis()->lpush($queueKey, [$data]);
        return true;
    }

    /**
     * @param $handler
     * @return BaseHandler
     */
    public static function createHandler($handler)
    {
        if( strpos($handler,'common\\library\\alibaba\\services\\queue\\') ===false )
        {
            $className = ucfirst($handler);
            $class = "common\\library\\alibaba\\services\\queue\\" . $className;
        }else
        {
            $class = $handler;
        }

        $handlerObject = new $class();

        return $handlerObject;
    }

    public static function getProductQueueLength()
    {
        $count = self::getRedis()->llen(self::SYNC_PRODUCT_QUEUE_KEY);
        return $count;
    }

    public static function getQueueStat()
    {
       $redis =  self::getRedis();
       $data = [
           [
               'key' => self::SYNC_CUSTOMER_QUEUE_KEY,
               'value' => $redis->llen(self::SYNC_CUSTOMER_QUEUE_KEY),
           ],
           [
               'key' => self::SYNC_PRODUCT_QUEUE_KEY,
               'value' => $redis->llen(self::SYNC_PRODUCT_QUEUE_KEY),
           ],
           [
               'key' => self::GREY_CLIENT_CUSTOM_HANDLER_QUEUE_KEY,
               'value' => $redis->llen(self::GREY_CLIENT_CUSTOM_HANDLER_QUEUE_KEY),
           ],
           [
	           'key'   => self::DELAY_QUEUE_KEY,
	           'value' => $redis->zcard(self::DELAY_QUEUE_KEY),
	           'delay' => $redis->zlexcount(QueueHelper::DELAY_QUEUE_KEY, '[0', '[' . time()),
           ],
           [
               'key' => self::DEMOTION_CLIENT_CUSTOM_HANDLER_QUEUE_KEY,
               'value' => $redis->llen(self::DEMOTION_CLIENT_CUSTOM_HANDLER_QUEUE_KEY),
           ],
           [
               'key' => self::GREY_DEMOTION_CLIENT_CUSTOM_HANDLER_QUEUE_KEY,
               'value' => $redis->llen(self::GREY_DEMOTION_CLIENT_CUSTOM_HANDLER_QUEUE_KEY),
           ],
        ];

       return $data;
    }

}
