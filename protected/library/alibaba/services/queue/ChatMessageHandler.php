<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2020/11/13
 * Time: 6:32 PM
 */

namespace common\library\alibaba\services\queue;


use AlibabaAccount as AlibabaAccountModel;
use AlibabaStoreAccount;
use common\library\account\Client;
use common\library\alibaba\Constant;
use common\library\alibaba\Helper;
use common\library\alibaba\oauth\AlibabaAccount;
use common\library\alibaba\track\TrackService;
use common\library\alibaba\trade\AlibabaChatSummary;
use common\library\exp\ExpAlibabaService;
use common\library\privilege_v3\PrivilegeService;
use common\library\swarm\SwarmService;
use common\library\task\TaskConstant;
use common\library\task\TaskList;
use common\library\todo\Feed;
use common\library\todo\TodoConstant;
use common\library\workflow\WorkflowConstant;
use common\library\workflow\WorkflowRuleList;
use function AlibabaCloud\Client\json;
use common\library\alibaba\trade\AlibabaTradeRelationList;


class ChatMessageHandler extends BaseHandler
{
    protected $tradeMessageType;
    /**
     * @var AlibabaAccount
     */
    protected $sellerAccount;

    protected $toAccountId; //接收方
    protected $fromAccountId; //发送方
    protected $uuid;
    protected $params=[];

    protected $newOwnerAccount;
    protected $toAccount;
    protected $fromAccount;


    public function initData($data)
    {
        $this->data = $data;
        $this->addProcessData('message_data',$this->data);
        \LogUtil::info("initData: ".$data['content']);
        $content = json_decode($data['content'], true);

        $this->addProcessData('uuid',$content['uuid'] ?? "");
        $this->addProcessData('business_id',$content['business_id']??0);
        $this->addProcessData('business_type',$content['business_type']??"");
        $this->addProcessData('message_type',$content['message_type']);
        $this->addProcessData('message_body', $content['content'] ?? '');    // 消息内容

        $this->uuid = $content['uuid'];

        $toAccountId = $content['to_account_id'];
        $fromAccountId = $content['from_account_id'];
        $newOwnerAccountId =  $content['new_owner_account_id']??0;//分配询盘的人
        //分配询盘
        if(in_array($content['message_type'],Constant::$assignInquiryMap)){
            //接收人跟新分配人一样，需要找到原来店铺管理人
            if($toAccountId == $newOwnerAccountId){
                $adminSellerAccountId = Helper::getBindAdminSellerAccountId($newOwnerAccountId);
                if(!$adminSellerAccountId){
                    \LogUtil::info("ChatMessageHandler adminSellerAccountId unbind: ".$toAccountId.':'.$fromAccountId);
                    return;
                }
                $toAccountId = $content['to_account_id'] = $adminSellerAccountId;//询盘的原跟进人
            }
        }
        $selectAccountIds = [$toAccountId, $fromAccountId];
        if (!empty($newOwnerAccountId)) $selectAccountIds[] = $newOwnerAccountId;
        $alibabaAccountMap = $this->getAlibabaAccountMap($selectAccountIds);
        if(empty($alibabaAccountMap)){
            \LogUtil::info("account empty: ".$toAccountId.':'.$fromAccountId);
            return;
        }
        $this->fromAccount = $alibabaAccountMap[$fromAccountId] ?? null;
        $this->toAccount = $alibabaAccountMap[$toAccountId] ?? null;
        $this->newOwnerAccount = $alibabaAccountMap[$newOwnerAccountId] ?? null;
        $buyerAccountIdEncrypt = '';

        $tradeMessageType = $this->data['message_type'] ?? (isset($alibabaAccountMap[$toAccountId]) ? Constant::FROM_BUYER_MESSAGE_FLAG : Constant::FROM_SELLER_MESSAGE_FLAG);


//        聊天双方均绑定+第一次进
        if (count($alibabaAccountMap) == 2 && empty($data['message_type'])) {

            $data['message_type'] = Constant::FROM_SELLER_MESSAGE_FLAG;

            (new DemotionLimiterHandler(true))
                ->setIsGrey(\Yii::app()->params['env'] == 'grey')
                ->handle($data);
        }

        //买家发送信息
        if (($tradeMessageType == Constant::FROM_BUYER_MESSAGE_FLAG) && isset($alibabaAccountMap[$toAccountId])) {

            $sellerAccount = $alibabaAccountMap[$toAccountId];
            $buyerAccountId = $content['from_account_id'];
            $buyerAccountEmail = $content['from_email']??''; //from_email一定是会有的
            $buyerAccountIdEncrypt = $content['from_account_id_encrypt']??'';
        }else
        {
            //卖家发送信息
            $tradeMessageType = Constant::FROM_SELLER_MESSAGE_FLAG;
            if( isset($alibabaAccountMap[$fromAccountId]))
            {
                $sellerAccount = $alibabaAccountMap[$fromAccountId];
            }elseif( $newOwnerAccountId && isset($alibabaAccountMap[$newOwnerAccountId])) //转移询盘的情况, 要检查
            {
                $sellerAccount = $alibabaAccountMap[$newOwnerAccountId];
            }else
            {
                \LogUtil::info("sellerAccount empty: ".$toAccountId.':'.$fromAccountId.' new owner:'.$newOwnerAccountId);
                return;
            }
            $buyerAccountId = $content['to_account_id'];
            $buyerAccountEmail = $content['to_email']??'';  //to_email不一定
            //$buyerAccountIdEncrypt = $content['from_account_id_encrypt']??'';
        }
        $aliStoreAccount = AlibabaStoreAccount::model()->find('client_id = :cid and seller_account_id = :sid', [
            ':cid' => $sellerAccount->client_id,
            ':sid' => $sellerAccount->seller_account_id,
        ]);
        $ali_username = '';
        if ($aliStoreAccount) {
            $ali_username = "{$aliStoreAccount->first_name} {$aliStoreAccount->last_name}";
        }

        $params = [

            'client_id' => $sellerAccount->client_id,
            'op_user_id' =>  $sellerAccount->user_id,
            'seller_account_id' => $sellerAccount->seller_account_id,//卖家id
            'seller_account_email' => $sellerAccount->seller_email,//卖email
            'ali_username' => $ali_username,//卖家firstname lastname
            'buyer_account_id' => $buyerAccountId,//买家id
            'buyer_email' => $buyerAccountEmail,//买家邮箱
            'buyer_account_id_encrypt' => $buyerAccountIdEncrypt,
            'time_stamp' => $content['time_stamp'],//时间戳
            'business_id_encrypt' => $content['business_id_encrypt']??'',//业务加密id
            'business_type' => $content['business_type']??"",//业务类型
            'message_type' => $content['message_type'],//消息类型
            'message_body' => $content['content'] ?? '', //消息体
            'business_id' => $content['business_id']??0,//业务id
            'uuid' => $content['uuid'], //消息id
            'trade_type' => $tradeMessageType,//1收到买家回复信息 2收到卖家回复信息
            'new_owner_account_id' => $newOwnerAccountId,
            'store_id' => $sellerAccount->store_id,
            'message_time' => $this->data['pub_time'],
            'visitor_scene' => $content['entrance']??'',//到店营销的场景来源 visiter_marktig_live/visiter_markting_shopDetail/visiter_markting_productDetail
            'sec_token' => $content['token'] ?? '',
        ];

        $this->params = $params;
        $this->userId = $sellerAccount->user_id;
        $this->clientId = $sellerAccount->client_id;
        $this->toAccountId = $toAccountId;
        $this->fromAccountId = $fromAccountId;
        $this->tradeMessageType = $tradeMessageType;
        $this->sellerAccount = $sellerAccount;


        $this->addProcessData('client_id',$this->clientId);
        $this->addProcessData('user_id',$this->userId);
        $this->addProcessData('store_id',$this->params['store_id']);
        $this->addProcessData('seller_account_id',$this->params['seller_account_id']);
        $this->addProcessData('seller_account_email',$this->params['seller_account_email']);
        $this->addProcessData('buyer_account_id',$this->params['buyer_account_id']);
        $this->addProcessData('buyer_email',$this->params['buyer_email']);
        $this->addProcessData('trade_type',$this->params['trade_type']);
        $this->addProcessData('message_time',$this->data['pub_time']);
        $this->addProcessData('handler_params',$this->params);
        $this->addProcessData('visitor_scene',$this->params['visitor_scene']);
        $this->addProcessData('buyer_account_encrypt',$buyerAccountIdEncrypt);
        $this->addProcessData('sec_token', $this->params['sec_token']);
    }

    /**
     * @param array $aliAccountIds
     * @return AlibabaAccount[]
     */
    public  function getAlibabaAccountMap(array  $aliAccountIds)
    {
        // 过滤空值
        $aliAccountIds = array_filter($aliAccountIds);
        $map = [];
        if(!empty($aliAccountIds))
        {
            $sql = 'seller_account_id in ('.implode(',',$aliAccountIds). ') and oauth_flag in ('.implode(',',Constant::BIND_STATUS_MAP).')';
            $list =   AlibabaAccountModel::model()->findAll($sql)?:[];
            foreach($list as $item )
            {
                $account = new AlibabaAccount();
                $account->setModel($item);
                $map[$item['seller_account_id']] = $account;
            }
        }

        return $map;
    }

    public function before(): bool
    {
        return parent::before() && $this->checkClientExpire();
    }

    public function switchLogin()
    {
        //这里不需要设置登录态, 后续代码会设置
        //清理静态缓存
        \User::cleanUserMap();
        Client::cleanCacheMap();
        \ProjectActiveRecord::resetConnection();
        \PgActiveRecord::resetConnection();
    }


    public function process()
    {
        if( !$this->sellerAccount)
        {
            \LogUtil::info("[ChatMessageHandler] error  sellerAccount  empty: {$this->clientId} {$this->userId} {$this->uuid} ".($this->params['buyer_account_id']??''));
            return false;

        }

        $trackService = new TrackService($this->params);
        $res =  $trackService->apply();

        $this->addProcessData('handler_result',$res);

        \LogUtil::info("[ChatMessageHandler] finish {$this->clientId} {$this->userId} {$this->uuid} {$this->params['buyer_account_id']} res: ".json_encode($res));

        if($this->checkCanHandleTask())
        {
            $this->handleTask();
        }

        try {
            \common\library\statistics\Helper::saveAlibabaMessageStatistics($this->clientId, $this->userId, $this->params['seller_account_id'], $this->params['trade_type']);
        } catch (\Exception $e) {
            $sellerAccountId = $this->params['seller_account_id'];
            \LogUtil::error("tm统计eror:" . "$this->clientId, $this->userId,$sellerAccountId,{$this->params['trade_type']}" . $e->getMessage());
        }

//        $this->updateCompanyRelationTimeField($this->params, $trackService);



        return true;
    }

    protected function handleTask()
    {
        // 买家发给卖家
        if ($this->params['trade_type'] == Constant::FROM_BUYER_MESSAGE_FLAG) {
            $this->processTask();

            //当消息发送方和接收方都为卖家时，检查发送方是否有任务需要完成
            if ($this->fromAccount && $this->toAccount) {
                !empty($this->toAccount->seller_account_id) && \common\library\task\Helper::finishTask($this->fromAccount->client_id, [$this->toAccount->seller_account_id], [$this->fromAccount->user_id]);
                \LogUtil::info("完成任务[from seller to seller] -- clientId[{$this->fromAccount->client_id} -- referId[{$this->toAccount->seller_account_id}] -- userId[{$this->fromAccount->user_id}]");
            }
        } else {
            // 卖家发给买家
            if (!($this->fromAccount && $this->newOwnerAccount)) {
                !empty($this->params['buyer_account_id']) && \common\library\task\Helper::finishTask($this->clientId, [$this->params['buyer_account_id']], [$this->userId]);
                \LogUtil::info("完成任务 -- clientId[{$this->clientId} -- referId[{$this->params['buyer_account_id']}] -- userId[$this->userId]");
            } else {
                //卖家发给卖家
                // 这种情况是转移询盘 或 分配询盘 的时候 A转移给B 对应为 fromAccountId 转移给 $newOwnerAccountId
                $ownerAccountUserId = empty($this->newOwnerAccount) ? $this->userId : $this->newOwnerAccount->user_id;
                $fromAccountUserId = empty($this->fromAccount) ? 0 : $this->fromAccount->user_id;
                if (in_array($this->params['message_type'], [Constant::MESSAGE_TYPE_TRANSFER_IM_IMQUIRY, Constant::MESSAGE_TYPE_ASSIGN_IM_IMQUIRY]) && $ownerAccountUserId != $fromAccountUserId) {
                    \common\library\task\Helper::deleteTask($this->clientId, [$this->params['buyer_account_id']], [$fromAccountUserId]);
                }
            }
        }
    }



    protected function processTask()
    {
        if ($this->newOwnerAccount && $this->newOwnerAccount->client_id != $this->clientId) {
            \LogUtil::info("client异常 -- clientId[{$this->clientId}] -- newClientId[{$this->newOwnerAccount->client_id}] -- buyerAccountId[{$this->params['buyer_account_id']}]");
            return;
        }
        $ownerAccountUserId = empty($this->newOwnerAccount) ? $this->userId : $this->newOwnerAccount->user_id;

        // 查询有无已经创建的记录
        $taskList = new TaskList($this->clientId, $ownerAccountUserId);
        $taskList->setUserIds($ownerAccountUserId);
        $taskList->setReferIds([$this->params['buyer_account_id']]);
        $taskList->setStatus(TaskConstant::TASK_STATUS_INCOMPLETE);
        $taskList->setAllowDuplicate(0);
        $taskCount = $taskList->count();
        if ($taskCount) return;

        $taskRuleList = new WorkflowRuleList($this->clientId);
        $taskRuleList->setRuleType(WorkflowConstant::RULE_TYPE_TASK);
        $taskRuleList->setReferType(\Constants::TYPE_TM);
        $taskRuleList->setOrderBy('create_time');
        $taskRuleList->setOrder('desc');
        $taskRuleList->setEnableFlag(true);
        $taskRuleList->setDisableFlag(0);
        $taskRuleList->setLimit(1000);
        $ruleList = $taskRuleList->find();
        $ruleIds = array_column($ruleList, 'rule_id');
        if (empty($ruleIds)) return;

        foreach ($ruleList as $ruleInfo)
        {
            if ($this->checkIsMatchRules($ruleInfo)) {
                try {
                    $timeLimit = $ruleInfo['handlers'][0]['config']['time_limit'];
                    $task = new \common\library\task\Task($this->clientId, $ownerAccountUserId);
                    $task->type = TaskConstant::TASK_TYPE_REPLY_TM;
                    $task->trigger_type = TaskConstant::TASK_TRIGGER_TYPE_RECEIVE_TM;
                    $task->status = TaskConstant::TASK_STATUS_INCOMPLETE;
                    $task->refer_id = $this->params['buyer_account_id'];
                    $task->end_time = date('Y-m-d H:i:s', (time() + $timeLimit));
                    $task->user_id = $ownerAccountUserId;
                    $task->rule_id = $ruleInfo['rule_id'];
                    $task->create_time = date("Y-m-d H:i:s");
                    $task->update_time = date("Y-m-d H:i:s");
                    $task->allow_duplicate = 0;
                    $task->save();
                    $feedType = TodoConstant::TODO_TYPE_TASK_REPLY_TM . $ruleInfo['rule_id'];
                    $feed = new Feed(TodoConstant::OBJECT_TYPE_TASK, $feedType);
                    $feed->setClientId($this->clientId);
                    $feed->setUserId($ownerAccountUserId);
                    $feed->pushFeed([$task->task_id]);
                    \LogUtil::info("新建任务 ruleId[{$ruleInfo['rule_id']}] -- taskId[{$task->task_id}] feedType[{$feedType}]");
                    break;
                } catch (\Throwable $t) {
                    \LogUtil::error("Error Msg -- {$t->getMessage()} -- refer_id[{$this->params['buyer_account_id']}] -- rule_id[{$ruleInfo['rule_id']}]");
                }

            }
        }
    }

    protected function checkIsMatchRules($ruleInfo)
    {
        $handlers = $ruleInfo['handlers'];
        $taskHandlerConfig = [];
        foreach ($handlers as $handler)
        {
            if ($handler['type'] == 'task' && !empty($handler['config'])) {
                $taskHandlerConfig = $handler['config'];
            }
        }
        if (empty($taskHandlerConfig)) return false;

        //判断是否是执行人
        $roleIds = $taskHandlerConfig['role_id'] ?? [];
        if (!empty($roleIds)){
            //执行人为角色
            $privilegeService = PrivilegeService::getInstance($this->clientId);
            $userRoleIds = $privilegeService->getUserPrivilege()->getUserRoleIds($this->userId);
            if (empty(array_intersect($userRoleIds,$roleIds))){
                return false;
            }
        }else{
            //执行人为同事
            $opUserIds = $taskHandlerConfig['user_id'] ?? [];
            if (empty($opUserIds) || !in_array($this->userId, $opUserIds)) {
                return false;
            }
        }

        $tmType = $taskHandlerConfig['tm_type'] ?? [];
        // 正常来说是一个数组，因为这里是前端给的，不信任，做下判断
        if (empty($tmType) || !is_array($tmType)) {
            return false;
        } else if ((count(array_diff($tmType, [0])) == 0) && !in_array($this->params['message_type'], Constant::$tmOtherMessageTypes)) {
            // 只有 0 其他
            return false;
        } else if ((count(array_diff($tmType, [1])) == 0) && !in_array($this->params['message_type'], Constant::$tmInquiryMessageTypeMap)) {
            // 只有 1 询盘
            return false;
        } else if ((count(array_diff($tmType, [0, 1])) == 0) && !in_array($this->params['message_type'], array_merge(
                Constant::$tmOtherMessageTypes, Constant::$tmInquiryMessageTypeMap
            ))) {
            // 目前最多支持 0 其他 + 1询盘
            return false;
        } else if ((count(array_diff($tmType, [0, 1])) > 0)) {
            // @todo 最后会隐含一个逻辑就是 目前最多支持 0 其他 + 1询盘 但是存在其他选择，这里先阻止生成任务，有需要在开启
            // 正常走不到这里，打个日志给后面大哥好排查
            \LogUtil::info('ChatMessageHandler_checkIsMatchRules', [
                'tm_type' => $tmType, '出现了其他选择，注意是否要调整上面限制'
            ]);
            return false;
        }

        //接收tm消息时 增加接收时间判断
        if (isset($taskHandlerConfig['tm_receive_date']) && !in_array(date('w'),$taskHandlerConfig['tm_receive_date'])) {
            return false;
        }
        if (isset($taskHandlerConfig['tm_receive_time_start']) && strtotime(date('Y-m-d H:i')) < strtotime(date('Y-m-d '.$taskHandlerConfig['tm_receive_time_start']))) {
            return false;
        }
        if (isset($taskHandlerConfig['tm_receive_time_end']) && strtotime(date('Y-m-d H:i')) > strtotime(date('Y-m-d '.$taskHandlerConfig['tm_receive_time_end']))) {
            return false;
        }

        return true;
    }

    protected function after()
    {
        //exp 处理
        if($this->sellerAccount && $this->sellerAccount->seller_account_id){
            $expCache = ExpAlibabaService::getExpSellerAccountCache($this->sellerAccount->seller_account_id);
            if ($expCache)
            {
                $params['-setNo'] = $expCache['set_no'];
                //保存成功后，提交到后台异步处理计划任务
                $log = '/tmp/exp_handle_chat_message.log';
                \common\library\CommandRunner::run(
                    'alibaba',
                    'handleExpChatMessage',
                    $params,
                    $log,
                    1,
                    0,
                    'yiic-exp'

                );
                \LogUtil::info("[ChatExpMessageHandler] exp :".json_encode($this->params));
            }
        }



    }

    //检查是否可以创建/完成任务
    protected function checkCanHandleTask()
    {
        if (empty($this->params['client_id']) || empty($this->params['seller_account_id']) || empty($this->params['buyer_account_id']) || empty($this->params['store_id'])) {
            return false;
        }

        if (in_array( $this->params['message_type'],Constant::$taskFilterMessageType)) {
            \LogUtil::info("[checkCanHandleTask] message_type: {$this->params['message_type']}".json_encode($this->data));
            return false;
        }

        $alibabaChatSummary = new AlibabaChatSummary($this->params['client_id']);
        $alibabaChatSummary->loadBySellerAccountAndBuyerAccount($this->params['seller_account_id'],$this->params['buyer_account_id'],$this->params['store_id']);
        if(!$alibabaChatSummary->isNew()){
            if ($this->params['trade_type'] == Constant::FROM_BUYER_MESSAGE_FLAG  && strtotime($this->data['pub_time']) <=  strtotime($alibabaChatSummary->last_send_time)) {
                \LogUtil::info("[checkCanHandleTask] trade_type: {$this->params['trade_type']}".json_encode($this->data));
                return false;
            }

        }
        return true;
    }

    //保存消息
    protected function saveMessage()
    {
        $message = new \common\library\alibaba\services\message\ChatMessage($this->processData);
        $message->save();
    }


    /**
     * @deprecated 废弃，迁移至trade_lead_apply.yaml中的
     * @param $data
     * @return false|void
     * @throws \CDbException
     * @throws \CException
     * @throws \ProcessException
     */
    //更新buyer_account_id对应的company_id数据
    //最新收到阿里TM消息时间'latest_receive_ali_tm_time',
    //最新发送阿里TM消息时间'latest_send_ali_tm_time',
    //最新收到阿里询盘消息时间'latest_receive_ali_trade_time',
    //最新回复阿里询盘消息时间'latest_reply_ali_trade_time',
    private function updateCompanyRelationTimeField($data)
    {
        $clientId = $data['client_id'] ?? 0;
        $buyerAccountId = $data['buyer_account_id'] ?? 0;
        $messageType = $data['message_type'] ?? 0;
        $tradeType = $data['trade_type'] ?? 0;
        $messageTime = $data['message_time'] ?? date('Y-m-d H:i:s');

        //过滤非业务消息
        if (!in_array( $messageType,Constant::$messageTypeList)) {
            \LogUtil::info("[updateCompanyRelationTimeField] Filter non business messages, message_type: {$messageType}");
            return false;
        }

        if (empty($clientId) || empty($buyerAccountId) || empty($tradeType)) {
            return false;
        }

        if (empty($messageTime)) {
            $messageTime = date('Y-m-d H:i:s');
        }

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $aliCompanyAccountSql = "select company_id,  buyer_account_id from tbl_alibaba_company_relation where client_id =$clientId and buyer_account_id = $buyerAccountId";
        if (isset($data['store_id'])) {
            $aliCompanyAccountSql .= " and store_id={$data['store_id']}";
        }
        $aliCompanyData = $db->createCommand($aliCompanyAccountSql)->queryAll();
        if (empty($aliCompanyData)) {
//            \LogUtil::info("buyer_account-company_id"."aliCompanyData数据为空");
            return false;
        }

        $companyIds = array_filter(array_column($aliCompanyData,'company_id'));
        if (empty($companyIds)) {
//            \LogUtil::info("buyer_account-company_id"."companyId 数据为空");
            return false;
        }


        \common\library\customer\Helper::updateTradeAndTmTime($clientId, $companyIds, $messageTime, $tradeType, $messageType);
    }

}
