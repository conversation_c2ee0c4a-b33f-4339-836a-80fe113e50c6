<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2020/11/13
 * Time: 6:09 PM
 */

namespace common\library\alibaba\services;


use common\library\alibaba\Constant;
use common\library\alibaba\services\message\DelayMessageService;
use common\library\alibaba\services\queue\CancelAuthHandler;
use common\library\alibaba\services\queue\CancelStoreAuthHandler;
use common\library\alibaba\services\queue\ChatMessageHandler;
use common\library\alibaba\services\queue\ClientLimiter;
use common\library\alibaba\services\queue\CustomerNoteModifiedHandler;
use common\library\alibaba\services\queue\CustomerModifiedPushHandler;
use common\library\alibaba\services\queue\DemotionLimiterHandler;
use common\library\alibaba\services\queue\CustomerOfflineDataChangedHandler;
use common\library\alibaba\services\queue\OrderNotifyHandler;
use common\library\alibaba\services\queue\ProductSyncTaskHandler;
use common\library\alibaba\services\queue\QueueHelper;
use common\library\alibaba\services\queue\UnbindHandler;
use LogUtil;
use Predis\ClientException;
use Predis\Connection\ConnectionException;
use Swoole\Process\Manager;
use Swoole\Process\Pool;
use Xiaoman\Sidekick\Trace\SpanKind;
use Xiaoman\Sidekick\Trace\TracerProvider;

class AlibabaConsumer
{
    protected $processManager;
    protected $topTmcHandlerMap=[];
    protected $processConfigs=[];
    protected $limiter;

    const MAX_SLEEP_COUNT = 100;
    const MAX_EXEC_COUNT = 100;

    public  function serverName()
    {
        $env = \Yii::app()->params['env'] ? ' '.\Yii::app()->params['env'] : '';
        return get_called_class().$env;
    }

    public function __construct($withProcessManager = true)
    {
        $withProcessManager && $this->processManager = new Manager();
        $this->init();
    }

    /**
     * @param mixed $limiter
     */
    public function setLimiter(ClientLimiter $limiter)
    {
        $this->limiter = $limiter;
    }

    /**
     * @return array
     */
    public function getTopTmcHandlerMap(): array
    {
        return $this->topTmcHandlerMap;
    }

    /**
     * @return array
     */
    public function getProcessConfigs(): array
    {
        return $this->processConfigs;
    }

    /**
     * @param array $processConfigs
     */
    public function setProcessConfigs(array $processConfigs)
    {
        $this->processConfigs = $processConfigs;
    }



    public function start()
    {
        PHP_OS == 'Linux' && swoole_set_process_name( $this->serverName() . ' manager');

        foreach ($this->processConfigs as $config)
        {
            if( empty($config['worker_count']))
                continue;

            $this->processManager->addBatch($config['worker_count'], [$this, $config['process']]);
        }

        $this->processManager->start();
    }

    public function startGrey()
    {
        $processConfigs = [
            'processGreyClientQueue' => [
                'worker_count' => getenv('CUSTOM_CONSUMER_COUNT')=== false?40: getenv('CUSTOM_CONSUMER_COUNT'),
                'process' => 'processGreyClientQueue',
            ],
            'processGreyDemotionClientQueue' => [
                'worker_count' => getenv('DEMOTION_CONSUMER_COUNT')=== false?2: getenv('DEMOTION_CONSUMER_COUNT'),
                'process' => 'processGreyDemotionClientQueue',
            ]
        ];

        PHP_OS == 'Linux' && swoole_set_process_name( $this->serverName() . ' manager');

        foreach ($processConfigs as $config) {
            if (empty($config['worker_count']))
                continue;

            $this->processManager->addBatch($config['worker_count'], [$this, $config['process']]);
        }

        $this->processManager->start();
    }


    protected function init()
    {
        //注册阿里消息处理器
        $this->registerTopTmcHandler(AlibabaTopClient::TOPIC_ICBU_MEMBER_UNBIND, UnbindHandler::class);
        $this->registerTopTmcHandler(AlibabaTopClient::TOPIC_ICBU_CHAT_MESSAGE_TIP, ChatMessageHandler::class);
        $this->registerTopTmcHandler(AlibabaTopClient::TOPIC_TOP_AUTH_CANCEL, CancelAuthHandler::class);
        $this->registerTopTmcHandler(AlibabaTopClient::TOPIC_ICBU_AUTH_CANCEL, CancelStoreAuthHandler::class);
        $this->registerTopTmcHandler(AlibabaTopClient::TOPIC_ICBU_ORDER_NOTIFY, OrderNotifyHandler::class);
        $this->registerTopTmcHandler(AlibabaTopClient::TOPIC_ICBU_CUSTOMER_NOTE, CustomerNoteModifiedHandler::class);
        $this->registerTopTmcHandler(AlibabaTopClient::TOPIC_ICBU_CUSTOMER_PUSH, CustomerModifiedPushHandler::class);
        $this->registerTopTmcHandler(AlibabaTopClient::TOPIC_ICBU_ALICRM_OFFLINE_DATA_CHANGED, CustomerOfflineDataChangedHandler::class);


        //减少进程数, 起多个服务
        $this->processConfigs= [
            'processAlibabaAuthQueue' => [
                'worker_count' => getenv('AUTH_CONSUMER_COUNT')=== false?1: getenv('AUTH_CONSUMER_COUNT'),
                'process' => 'processAlibabaAuthQueue'
            ],
            'processAlibabaChatQueue' => [
                'worker_count' => getenv('CHAT_CONSUMER_COUNT')=== false?25: getenv('CHAT_CONSUMER_COUNT'),
                'process' => 'processAlibabaChatQueue'
            ],
            'processAlibabaOrderQueue' => [
                'worker_count' => getenv('ORDER_CONSUMER_COUNT')=== false?25: getenv('ORDER_CONSUMER_COUNT'),
                'process' => 'processAlibabaOrderQueue'
            ],
            'processDelayQueue' => [
                'worker_count' => getenv('DELAY_CONSUMER_COUNT')=== false?10: getenv('DELAY_CONSUMER_COUNT'),
                'process' => 'processDelayQueue'
            ],
            'processSyncTaskQueue' => [
                'worker_count' => getenv('TASK_CONSUMER_COUNT')=== false?1: getenv('TASK_CONSUMER_COUNT'),
                'process' => 'processSyncTaskQueue'
            ],
            'processAlibabaCustomerPushQueue' => [
                'worker_count' => getenv('CUSTOMER_CONSUMER_COUNT')=== false?40: getenv('CUSTOMER_CONSUMER_COUNT'),
                'process' => 'processAlibabaCustomerPushQueue'
            ],
            'processAlibabaCustomerNoteQueue' => [
                'worker_count' => getenv('NOTE_CONSUMER_COUNT')=== false?20: getenv('NOTE_CONSUMER_COUNT'),
                'process' => 'processAlibabaCustomerNoteQueue'
            ],
            'processDemotionClientQueue' => [
                'worker_count' => getenv('DEMOTION_CONSUMER_COUNT')=== false?2: getenv('DEMOTION_CONSUMER_COUNT'),
                'process' => 'processDemotionClientQueue'
            ],
            'processAlibabaOfflineDataChangedQueue' => [
                'worker_count' => getenv('OFFLINE_CONSUMER_COUNT') === false ? 20 : getenv('OFFLINE_CONSUMER_COUNT'),
                'process'      => 'processAlibabaOfflineDataChangedQueue',
            ],
        ];
    }

    public function registerTopTmcHandler($topic, $handler)
    {
        $this->topTmcHandlerMap[$topic] = $handler;
    }


    public function processAlibabaChatQueue(Pool $pool, int $workerId)
    {
        PHP_OS == 'Linux' && swoole_set_process_name( $this->serverName() . ' process alibaba chat');
        \LogUtil::initCustom('consumer_log','AlibabaConsumer', 'processAlibabaChatQueue');
        $this->processTopTmcQueue($pool, $workerId, AlibabaTopClient::GROUP_CHAT,100, $this->processConfigs['processAlibabaChatQueue']['worker_count']);
    }


    public function processAlibabaAuthQueue(Pool $pool, int $workerId)
    {
        PHP_OS == 'Linux' && swoole_set_process_name( $this->serverName() . ' process alibaba auth');
        \LogUtil::initCustom('consumer_log','AlibabaConsumer', 'processAlibabaAuthQueue');
        $this->processTopTmcQueue($pool, $workerId, AlibabaTopClient::GROUP_AUTH, 100,$this->processConfigs['processAlibabaAuthQueue']['worker_count']);
    }

    public function processAlibabaOrderQueue(Pool $pool, int $workerId)
    {
        PHP_OS == 'Linux' && swoole_set_process_name( $this->serverName() . ' process alibaba order');
        \LogUtil::initCustom('consumer_log','AlibabaConsumer', 'processAlibabaOrderQueue');
        $this->processTopTmcQueue($pool, $workerId, AlibabaTopClient::GROUP_ORDER, 20, $this->processConfigs['processAlibabaOrderQueue']['worker_count']);
    }

    public function processAlibabaCustomerPushQueue(Pool $pool, int $workerId)
    {
        PHP_OS == 'Linux' && swoole_set_process_name( $this->serverName() . ' process alibaba customer push');
        \LogUtil::initCustom('consumer_log','AlibabaConsumer', 'processAlibabaCustomerPushQueue');
        $this->processTopTmcQueue($pool, $workerId, AlibabaTopClient::GROUP_CUSTOMER, 100, $this->processConfigs['processAlibabaCustomerPushQueue']['worker_count']);
    }

    public function processAlibabaCustomerNoteQueue(Pool $pool, int $workerId)
    {
        PHP_OS == 'Linux' && swoole_set_process_name( $this->serverName() . ' process alibaba note push');
        \LogUtil::initCustom('consumer_log','AlibabaConsumer', 'processAlibabaCustomerNoteQueue');
        $this->processTopTmcQueue($pool, $workerId, AlibabaTopClient::GROUP_NOTE, 100, $this->processConfigs['processAlibabaCustomerNoteQueue']['worker_count']);
    }
    
    public function processAlibabaOfflineDataChangedQueue(Pool $pool, int $workerId) {
        
        PHP_OS == 'Linux' && swoole_set_process_name($this->serverName() . ' process alibaba offline data changed push');
     
        \LogUtil::initCustom('consumer_log', 'AlibabaConsumer', 'processAlibabaOfflineDataChangedQueue');
        
        $needCheck = !empty(getenv('OFFLINE_LIMIT') ?? 1);
        
        do {
            
            $hour = date('H');
            
            $this->log("[processAlibabaOfflineDataChangedQueue-{$workerId}-{" . AlibabaTopClient::GROUP_OFFLINE_DATA . "}] handler sleep:  60s");
            
            sleep(60);
            
        } while ($needCheck && $hour >= 8 && $hour < 18);
        
        $this->processTopTmcQueue($pool, $workerId, AlibabaTopClient::GROUP_OFFLINE_DATA, 100, $this->processConfigs['processAlibabaOfflineDataChangedQueue']['worker_count']);
    }

    
    public function processTopTmcQueue(Pool $pool, int $workerId, $group,$quantity = 100, $workerCount=1)
    {
        $method = __METHOD__;
        AlibabaTopClient::releaseInstance();
        $client = AlibabaTopClient::getInstance();
        //更新消息组配置,这种数据不经常改, 改为脚本处理
//        if( !empty(AlibabaTopClient::TOPIC_GROUP_MAP[$group]) )
//        {
//            $client->routeGroupTmcMessage($group, AlibabaTopClient::TOPIC_GROUP_MAP[$group]);
//        }

        $runFlag = true;
        $consumeCount = 0;
        $sleepCount = 0;
        $minSleep = 3;
        $maxSleep = $minSleep+$workerCount;
    
        $processStartTime = time();

        while ($runFlag)
        {
            do
            {
                $rsp = $client->consumeTmcMessage($group, $quantity);
                $tmcMessages = $rsp['messages']['tmc_message']??[];
                $confirmMessageIds = [];
                $startTime = microtime(true);
                foreach ($tmcMessages as $tmcMessage)
                {
                    $this->log("[{$method}-{$workerId}] message:".json_encode($tmcMessage));
                    $topic = $tmcMessage['topic'];
                    $handlerClass = $this->topTmcHandlerMap[$topic]??null;
                    if(empty($handlerClass))
                    {
                        $this->log("[{$method}-{$workerId}-{$group}] topic {$topic} handler not exist :".json_encode($tmcMessage));
                        continue;
                    }

                    try
                    {
                        $startTime = microtime(true);
                        $span = TracerProvider::getTracer()->spanBuilder($handlerClass)
                            ->setNoParent()
                            ->setSpanKind(SpanKind::KIND_CONSUMER)
                            ->startSpan();

                        LogUtil::withContext([
                            'traceId' => $span->getContext()->getTraceId(),
                            'span_id' => $span->getContext()->getSpanId(),
                            'message_id' => $tmcMessage['id'],
                        ]);

                        $handler = QueueHelper::createHandler($handlerClass);
                        $handler->initData($tmcMessage);
                        $handler->setLimiter($this->limiter);
                        $handler->run();
                        $confirmMessageIds[] = $tmcMessage['id'];

                        unset($handler);
                    } catch (\Exception $e)
                    {
                        //出错仅记录日志,后续待优化
                        $this->log("[{$method}-{$workerId}-{$group}] handler error data: ".json_encode($tmcMessage)." error:" . $e->getMessage() . $e->getTraceAsString());

                        //错误上报
                        if(! ($e instanceof \RuntimeException))
                        {
                            \common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());
                        }
                        //redis连接一次, 结束进程
                        if( ($e instanceof ConnectionException) || ($e instanceof ClientException))
                        {
                            $runFlag = false;
                            break ;
                        }
                    } finally {
                        $endTime = microtime(true);
                        $costTime = round($endTime - $startTime,4);
                        if (!empty($span)) {
                            $span->setAttributes([
                                'class' => $this::class,
                                'processTime' => $costTime,
                                'handler' => $handlerClass,
                                'message_status' => isset($e) ? 'error' : 'success',
                                'message_time' => $data['message_time'] ?? '',
                                'message_end_time' => $endTime
                            ]);
                            $span->end();
                            unset($span);
                        }
                    }
                }

                //批量确认, 减少api调用次数
                if( !empty($confirmMessageIds))
                {
                   $ret =  $client->confirmTmcMessage($confirmMessageIds, $group);
                   $handlerTime = microtime(true) - $startTime;
                   $memoryUsage = round(memory_get_usage(true) / (1024 * 1024), 2) . ' MB';
                    $this->log("[{$method}-{$workerId}-{$group}] handler message count:".count($tmcMessages). ' confirm:'.count($confirmMessageIds).' ret:'.($ret?1:0).' handle time '.$handlerTime.'memory:'.$memoryUsage);
                }
                
                //消费一定次数就退出
                if ((++$consumeCount > self::MAX_EXEC_COUNT) || !$runFlag || (time() - $processStartTime) >= 600) {
                    
                    $runFlag = false;
                    break;
                }

            }while(!empty($tmcMessages));

            //消费时如果没有返回消息，建议做控制，不要一直调api，浪费应用的流量。如对程序做好优化，若没有消息则，sleep
            $seconds = rand($minSleep,$maxSleep);
            $this->log("[{$method}-{$workerId}-{$group}] handler sleep:  {$seconds} s");
            sleep($seconds);
            //休眠一定次数退出
            if (++$sleepCount > self::MAX_SLEEP_COUNT || (time() - $processStartTime) >= 600) {
                
                $runFlag = false;
            }
        }

        $this->log("[{$method}-{$workerId}-{$group}] process finish sleepCount: {$sleepCount} consumeCount: {$consumeCount}");
        AlibabaTopClient::releaseInstance();
    }


    public function processDelayQueue(Pool $pool, int $workerId)
    {
        PHP_OS == 'Linux' && swoole_set_process_name( $this->serverName() . ' process delay');
        \LogUtil::initCustom('consumer_log','AlibabaConsumer', 'processDelayQueue');

        $method = __METHOD__;
        $redis = QueueHelper::getRedis(true, true);
        $runFlag = true;
        $consumeCount = 0;
        $sleepCount = 0;

        while ($runFlag)
        {
            // zset 返回小于当前时间的消息
            $ret = $redis->zrangebyscore(QueueHelper::DELAY_QUEUE_KEY, 0, time(), ['limit' => [0,1]]);

            // 如果重试的延迟队列是空, 就休眠等待
            if( empty($ret ))
            {
                sleep(3 + $sleepCount);

                if( (++$sleepCount) > self::MAX_SLEEP_COUNT )
                {
                    $runFlag = false;
                    break;
                }

                continue;
            }

            //获取消息 && 从队列中移除该消息
            $member = $ret[0];
            $msgRet = $redis->zrem(QueueHelper::DELAY_QUEUE_KEY, $member);

            //因为有了多进程并发的可能，最终只会有一个进程可以抢到消息
            if (!$msgRet) {
                $this->log("continue  zrem {$msgRet}  member {$member} ");
                continue;
            }

            $data = json_decode($member, true);
            // 对延时队列是否为 处理器是否为 LeadSyncCustomerHandler 进行判断
            $params = empty($data['params']) ? $data : json_decode($data['params'],true);
            $userId = empty($params['user_id']) ? $params['op_user_id'] : $params['user_id'];
            $delayMessageService = new DelayMessageService($params['client_id'],$userId,$params['store_id']??0);
            $messageId = $data['message_id']??0;
            $suffix = $data['suffix'] ?? date('Ym');
            $delayMessageService->getDelayMessage()->setSuffix($suffix);
            if(!empty($messageId) &&  $data['handler'] == 'LeadSyncCustomerHandler'){
                // 查询延迟消息消费状态
                if($delayMessageService->judgeConsumeById($messageId,$data['__process_until'])){
                    \LogUtil::info("handler[LeadSyncCustomerHandler] message [$messageId] has been consume");
                    continue ;
                }
            }


            try {

                $startTime = microtime(true);
                $span = TracerProvider::getTracer()->spanBuilder($data['handler'])
                    ->setNoParent()
                    ->setSpanKind(SpanKind::KIND_CONSUMER)
                    ->startSpan();

                LogUtil::withContext([
                    'traceId' => $span->getContext()->getTraceId(),
                    'span_id' => $span->getContext()->getSpanId(),
                    'message_id' => $messageId,
                ]);


                $handler = QueueHelper::createHandler($data['handler']);
                $handler->initData($data);
                $handler->setLimiter($this->limiter);
                $handler->run();

                unset($handler);

                if (!empty($messageId)){
                    // 保存延迟消息消费状态
                    $tableName = $delayMessageService->getDelayMessage()->getTableName();
                    $status = Constant::MESSAGE_CONSUMED;
                    $delayMessageService->getDelayMessage()->setStatus($status);
                    $delayMessageService->getDelayMessage()->updateStatus($messageId);

                    \LogUtil::info("[AlibabaConsumer] tableName [$tableName] message [$messageId] status [$status]");
                }


            } catch (\Exception $e) {
                //出错仅记录日志,后续待优化
                \LogUtil::error("[{$method}-{$workerId}] handler error data {$member} error:" . $e->getMessage() . $e->getTraceAsString());

                //错误上报
                if(! ($e instanceof \RuntimeException))
                {
                    \common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());
                }

                //错误重试
                $maxRetryCount = $data['max_retry_count']??0;
                $currentRetryCount = $data['current_retry_count']??0;
                if ( ++$currentRetryCount <= $maxRetryCount )
                {
                    $data['current_retry_count'] = $currentRetryCount;
                    $data['last_error'] = mb_substr($e->getMessage(),0, 100);
                    $redis->zadd(QueueHelper::DELAY_QUEUE_KEY,[json_encode($data) => time()+rand(600,1800)]);
                }

                //redis连接一次, 结束进程
                if( ($e instanceof ConnectionException) || ($e instanceof ClientException))
                {
                    $runFlag = false;
                    break ;
                }
            } finally {
                $endTime = microtime(true);
                $costTime = round($endTime - $startTime,4);
                if (!empty($span)) {
                    $span->setAttributes([
                        'class' => $this::class,
                        'processTime' => $costTime,
                        'handler' => $data['handler'] ?? '',
                        'message_status' => isset($e) ? 'error' : 'success',
                        'message_time' => $data['message_time'] ?? '',
                        'message_end_time' => $endTime
                    ]);
                    $span->end();
                    unset($span);
                }
            }


            $memoryUsage = round(memory_get_usage(true) / (1024 * 1024), 2) . ' MB';
            \LogUtil::info("[{$method}-{$workerId}] finish ".($data['handler']??'') .' memory: '.$memoryUsage);
            //消费达到一定次退出
            if( (++$consumeCount > self::MAX_EXEC_COUNT) || !$runFlag)
            {
                $runFlag = false;
                break;
            }

        }
    }

    public function processSyncTaskQueue(Pool $pool, int $workerId)
    {
        PHP_OS == 'Linux' && swoole_set_process_name( $this->serverName() . ' process syncTask');
        \LogUtil::initCustom('consumer_log','AlibabaConsumer', 'processSyncTaskQueue');

        $method = __METHOD__;
        $redis = QueueHelper::getRedis(true, true);
        $startTime = microtime(true);
        while (true) {
            try {

                $result = $redis->brpop([QueueHelper::SYNC_CUSTOMER_QUEUE_KEY, QueueHelper::SYNC_PRODUCT_QUEUE_KEY], 0);

                $this->log("[{$method}-{$workerId}] result:" . var_export($result, true));
                $data =null;
                if (array_key_exists(1, $result))
                {
                    $data = json_decode($result[1], true);
                }elseif (array_key_exists(2, $result))
                {
                    $data = json_decode($result[2], true);
                }

                if( empty($data))
                {
                    continue;
                }
                $span = TracerProvider::getTracer()->spanBuilder($data['handler'])
                    ->setNoParent()
                    ->setSpanKind(SpanKind::KIND_CONSUMER)
                    ->startSpan();

                LogUtil::withContext([
                    'traceId' => $span->getContext()->getTraceId(),
                    'span_id' => $span->getContext()->getSpanId(),
                ]);

                //兼容历史版本数据
                if( !isset($data['handler']))
                {
                    $data['handler'] = ProductSyncTaskHandler::class;
                }
                $handler = QueueHelper::createHandler($data['handler']);
                $handler->initData($data);
                $handler->setLimiter($this->limiter);
                $handler->run();

            } catch (\Exception $e)
            {
                //出错仅记录日志,后续待优化
                $this->log("[{$method}-{$workerId}] handler error data: ".json_encode($data)." error:" . $e->getMessage() . $e->getTraceAsString());

                //错误上报
                if(! ($e instanceof \RuntimeException))
                {
                    \common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());
                }
            } finally {
                $endTime = microtime(true);
                $costTime = round($endTime - $startTime,4);
                if (!empty($span)) {
                    $span->setAttributes([
                        'class' => $this::class,
                        'processTime' => $costTime,
                        'handler' => $data['handler'],
                        'message_status' => isset($e) ? 'error' : 'success',
                        'message_time' => $data['message_time'] ?? '',
                        'message_end_time' => $endTime
                    ]);
                    $span->end();
                    unset($span);
                }
            }

            $memoryUsage = round(memory_get_usage(true) / (1024 * 1024), 2) . ' MB';
            \LogUtil::info("[{$method}-{$workerId}] finish {$data['handler']} ".($data['store_id']??'') .' memory: '.$memoryUsage);
        }
    }

    public function processGreyClientQueue(Pool $pool, int $workerId)
    {
        PHP_OS == 'Linux' && swoole_set_process_name( $this->serverName() . ' process alibaba grey client');
        \LogUtil::initCustom('consumer_log','AlibabaConsumer', 'processGreyClientQueue');
        $this->processCustomHandlerQueue($pool, $workerId, QueueHelper::GREY_CLIENT_CUSTOM_HANDLER_QUEUE_KEY);
    }

    public function processDemotionClientQueue(Pool $pool, int $workerId)
    {
        PHP_OS == 'Linux' && swoole_set_process_name( $this->serverName() . ' process alibaba demotion client');
        \LogUtil::initCustom('consumer_log','AlibabaConsumer', 'processDemotionClientQueue');
        if ($this->limiter && is_a($this->limiter, ClientLimiter::class)) {
            $this->limiter->removeHandler(DemotionLimiterHandler::class);
        }
        $this->processCustomHandlerQueue($pool, $workerId, QueueHelper::DEMOTION_CLIENT_CUSTOM_HANDLER_QUEUE_KEY, 5);
    }

    public function processGreyDemotionClientQueue(Pool $pool, int $workerId)
    {
        PHP_OS == 'Linux' && swoole_set_process_name( $this->serverName() . ' process alibaba demotion client');
        if ($this->limiter && is_a($this->limiter, ClientLimiter::class)) {
            $this->limiter->removeHandler(DemotionLimiterHandler::class);
        }
        \LogUtil::initCustom('consumer_log','AlibabaConsumer', 'processGreyDemotionClientQueue');
        $this->processCustomHandlerQueue($pool, $workerId, QueueHelper::GREY_DEMOTION_CLIENT_CUSTOM_HANDLER_QUEUE_KEY, 5);
    }

    public function processCustomHandlerQueue(Pool $pool, int $workerId, string $queueKey, $blocked = 0)
    {
//        PHP_OS == 'Linux' && swoole_set_process_name( $this->serverName() . ' process  custom handler');
//        \LogUtil::initCustom('consumer_log','AlibabaConsumer', 'processCustomHandlerQueue');

        $method = __METHOD__;
        $redis = QueueHelper::getRedis(true, true);
        $runFlag = true;
        $consumeCount = 0;
        $startRunTime = time();
        while ($runFlag)
        {
            try
            {
                $startTime = microtime(true);
                $result = $redis->brpop([$queueKey], $blocked);
                if ($result === null) {
                    if ($blocked > 0) {
                        if (in_array($queueKey, [QueueHelper::GREY_DEMOTION_CLIENT_CUSTOM_HANDLER_QUEUE_KEY, QueueHelper::DEMOTION_CLIENT_CUSTOM_HANDLER_QUEUE_KEY])) {
                            $this->log("demotion queue {$queueKey} is empty for {$blocked} seconds");
                            (new DemotionLimiterHandler(true))->setIsGrey(QueueHelper::GREY_DEMOTION_CLIENT_CUSTOM_HANDLER_QUEUE_KEY == $queueKey)->cleanMatched();
                        }
                    }
                    continue;
                }

                if (!array_key_exists(1, $result))
                    continue;

                $data = json_decode($result[1], true);



                $this->log("[{$method}-{$workerId}] message:".json_encode($data));

                if( !isset($data['handler']) && isset($data['topic']))
                {
                    $topic = $data['topic'];
                    $handlerClass = $this->topTmcHandlerMap[$topic]??null;
                }else
                {
                    $handlerClass = $data['handler']??'';
                }

                if(empty($handlerClass))
                {
                    $this->log("[{$method}-{$workerId}}]  handler not exist :".json_encode($data));
                    continue;
                }

                $span = TracerProvider::getTracer()->spanBuilder($handlerClass)
                    ->setNoParent()
                    ->setSpanKind(SpanKind::KIND_CONSUMER)
                    ->startSpan();

                LogUtil::withContext([
                    'traceId' => $span->getContext()->getTraceId(),
                    'span_id' => $span->getContext()->getSpanId(),
                ]);

                $handler = QueueHelper::createHandler($handlerClass);
                $handler->initData($data);
                $handler->setLimiter($this->limiter);
                $handler->run();

            } catch (\Exception $e)
            {
                //出错仅记录日志,后续待优化
                $this->log("[{$method}-{$workerId}] handler error data: ".json_encode($data??[])." error:" . $e->getMessage() . $e->getTraceAsString());

                //错误上报
                if(! ($e instanceof \RuntimeException))
                {
                    \common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());
                }
            } finally {
                $endTime = microtime(true);
                $costTime = round($endTime - $startTime,4);
                if (!empty($span)) {
                    $span->setAttributes([
                        'class' => $this::class,
                        'processTime' => $costTime,
                        'handler' => $handlerClass ?? '',
                        'message_status' => isset($e) ? 'error' : 'success',
                        'message_time' => $data['message_time'] ?? '',
                        'message_end_time' => $endTime
                    ]);
                    $span->end();
                    unset($span);
                }
            }

            $memoryUsage = round(memory_get_usage(true) / (1024 * 1024), 2) . ' MB';
            \LogUtil::info("[{$method}-{$workerId}] finish ".($data['store_id']??'') .' memory: '.$memoryUsage);

            //消费一定次数就退出
            if( ++$consumeCount > self::MAX_EXEC_COUNT || time() - $startRunTime > 1200)
            {
                $runFlag = false;
                break;
            }
        }

        $this->log("[{$method}-{$workerId}] process finish  consumeCount: {$consumeCount}");
    }

    protected function log($msg )
    {
//        echo  $msg.PHP_EOL;
        \LogUtil::info($msg);
    }

}
