<?php
/**
 * The file is part of the php-crm.
 *
 * (c) anhoder <<EMAIL>>.
 *
 * 2021/9/14 2:47 下午
 */

namespace common\library\alibaba\product;

use common\components\BaseObject;
use common\library\alibaba\AlibabaSyncProcessor;
use common\library\alibaba\Constant;
use common\library\alibaba\order\AlibabaSyncDelayOrderConfig;
use common\library\alibaba\services\AlibabaProduct;
use common\library\alibaba\services\queue\QueueHelper;
use common\library\alibaba\store\AlibabaStore;
use common\library\platform_product\sku\PlatformSkuFilter;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\Helper as PrivilegeHelper;
use common\library\report\error\ErrorReport;
use common\library\platform_product\match\MatchSettingAPI;
use common\library\platform_product\match\pattern\AbstractMatchPattern;
use common\library\platform_product\sku\PlatformSku;
use common\library\platform_product\sku\PlatformSkuAPI;
use common\library\platform_product\sync\PlatformProductSync;
use common\library\platform_product\PlatformProduct;
use common\library\platform_product\PlatformProductConstants;
use LogUtil;
use RuntimeException;

class AlibabaProductSyncProcessor extends AlibabaSyncProcessor
{
    public const PRICE_TYPE_FOB    = 1;
    public const PRICE_TYPE_LADDER = 2;
    public const PRICE_TYPE_SKU    = 3;

    public const PRICE_TYPE_MAP = [
        'fob_price'    => self::PRICE_TYPE_FOB,
        'ladder_price' => self::PRICE_TYPE_LADDER,
        'sku_price'    => self::PRICE_TYPE_SKU,
    ];

    public const STATUS_MAP = [
        'sketch'   => PlatformProductConstants::STATUS_SKETCH,
        'approved' => PlatformProductConstants::STATUS_APPROVED,
        'tbd'      => PlatformProductConstants::STATUS_TBD,
        'new'      => PlatformProductConstants::STATUS_AUDITING,
        'modified' => PlatformProductConstants::STATUS_AUDITING,
    ];

    protected $clientId;
    protected $store;
    protected $opUserId;
    protected $lastProductModifiedTime;
    protected $fromTaskId;
    protected $thirdProductMatchSetting;
    protected $overrideExistsProduct = true;  // 是否覆盖已存在产品（平台产品目前始终覆盖更新）

    protected $checkLastSyncTime  = true;  // 检查最后同步时间

    /** @var ExistsItemMap */
    protected $existsMap;

    /** @var AlibabaProductFailureCollector */
    protected $failureCollector;

    /** @var MatchSettingAPI */
    protected $matchApi;

    protected $error;

    public function __construct($clientId, AlibabaStore $store, AlibabaProductFailureCollector $failureCollector = null)
    {
        parent::__construct($clientId, $store->store_id);
        $this->store = $store;

        $this->failureCollector = $failureCollector ?: new AlibabaProductFailureCollector($clientId);
        $this->matchApi         = new MatchSettingAPI($clientId);
    }

    public function getMutexKey($id): string
    {
        return "sync_product_sync_limit_{$this->clientId}_{$this->storeId}_{$id}";
    }

    protected function handleMutexError(string $error)
    {
        $this->failureCollector->fail($this->storeId, '', '', AlibabaProductFailureCollector::OP_TYPE_ERROR, $error);
    }

    public function setOverrideExistProduct(bool $flag)
    {
        $this->overrideExistsProduct = $flag;
    }

    public function checkLastSyncTime(bool $flag)
    {
        $this->checkLastSyncTime = $flag;
    }

    public function setExistsMap(ExistsItemMap $existsMap)
    {
        $this->existsMap = $existsMap;
    }

    private function getExistsMap()
    {
        if (!$this->existsMap) {
            $this->existsMap = new ExistsItemMap();
        }
        return $this->existsMap;
    }

    public function setOpUserId($opUserId)
    {
        $this->opUserId = $opUserId;
        $this->matchApi->setUserId($opUserId);
    }

    public function setFromTaskId($taskId)
    {
        $this->fromTaskId = $taskId;
    }

    protected function setLastProductModifiedTime($time)
    {
        if (empty($this->lastProductModifiedTime) || $this->lastProductModifiedTime < $time) {
            $this->lastProductModifiedTime = $time;
            return true;
        }
        return false;
    }

    public function getLastProductModifiedTime()
    {
        return $this->lastProductModifiedTime;
    }

    protected function getMatchSetting()
    {
        if (!$this->thirdProductMatchSetting) {
            $api = new MatchSettingAPI($this->clientId);

            $this->thirdProductMatchSetting = $api->getMatchSetting();
        }

        return $this->thirdProductMatchSetting;
    }

    public function getMutexExpire(): int
    {
        return 20;
    }

    /**
     * 同步产品
     * @param $cipherId
     * @param mixed ...$args
     * @return false|array
     * @throws \xiaoman\orm\exception\OrmException
     */
    public function process($cipherId, ...$args)
    {
        $isRTS = $args[0] ?? null;
        $isAdd          = false;
        $unModifiedFlag = false;
        $processTime    = date('Y-m-d H:i:s');

        $productService = new AlibabaProduct(AlibabaProductSyncHelper::getAccessTokenFromStore($this->store), $cipherId);
        $aliProduct     = $productService->getInfo();
        if (empty($aliProduct['product'])) {
            $platformProduct = new PlatformProduct($this->clientId);
            $platformProduct->loadByCipherProductId($cipherId, PlatformProductConstants::PLATFORM_ALIBABA);
            if (!$platformProduct->isNew()) {
                $platformProduct->getOperator()->markThirdDelete();
            }

            $this->failureCollector->fail($this->store->store_id, $cipherId, '', AlibabaProductFailureCollector::OP_TYPE_ERROR, '获取产品详情失败，产品不存或已删除');
            return false;
        }

        $aliProduct = $aliProduct['product'];

        $existsProduct = $this->getExistsMap()->findByCipherId($cipherId);
        //产品最后更新时间,时间未变更则跳过
        if ($this->checkLastSyncTime
            && isset($existsProduct['last_modified_time'])
            && $existsProduct['last_modified_time'] >= $aliProduct['gmt_modified']
        ) {
            $unModifiedFlag = true;
        }

        try {
            //获取aliProduct明文ID, crm中没记录则调用接口获取
            $aliProductPlainTextId = $existsProduct['third_product_id'] ?? $productService->getDecryptProductId();
            if (empty($aliProductPlainTextId)) {
                $this->failureCollector->fail($this->store->store_id, $cipherId, '', AlibabaProductFailureCollector::OP_TYPE_ERROR, '获取产品详情失败，产品不存或已删除1');
                return false;
            }

            //$recoverFlag  = false;
            $thirdProduct = new PlatformProduct($this->clientId);
            $thirdProduct->loadByThirdProductId($aliProductPlainTextId, PlatformProductConstants::PLATFORM_ALIBABA);
            $thirdProduct->setDomainHandler(\User::getUserObject($this->opUserId, $this->clientId));
            if ($thirdProduct->isNew()) {
                $thirdProduct->client_id   = $this->clientId;
                $thirdProduct->create_user = $this->opUserId;
                $thirdProduct->platform    = PlatformProductConstants::PLATFORM_ALIBABA;
                $thirdProduct->create_time = date('Y-m-d H:i:s');

                $isAdd = true;
            }
            //else {
            // 暂时没有删除逻辑
            //已删除
            //if ($thirdProduct->enable_flag == ProductConstant::ENABLE_FLAG_FALSE) {
            //    //永久删除 产品需求恢复到产品列表
            //    if (!(API::isExist($this->clientId, Recycle::PRODUCT, $thirdProduct->product_id))) {
            //        $recoverFlag = true;
            //    }
            //
            //    $isAdd = true;
            //}
            //}

            $subject     = $aliProduct['subject'] ?? '';
            $productType = empty($aliProduct['product_sku']['sku_attributes']) ? PlatformProductConstants::PRODUCT_TYPE_NO_SP : PlatformProductConstants::PRODUCT_TYPE_MULTI_SP;

            //未删除、回收箱 是否更新覆盖
            if (!$thirdProduct->isNew() && !$this->overrideExistsProduct) {
                $this->failureCollector->fail($this->store->store_id, $aliProduct['product_id'], $subject, AlibabaProductFailureCollector::OP_TYPE_IGNORE, '产品已存在不覆盖，忽略');
                return ['product_type' => $productType, 'platform_product_id' => $thirdProduct->platform_product_id, 'third_product_id' => $thirdProduct->third_product_id];
            }
            //阿里没有返回产品名称
            if (!$subject && !$thirdProduct->name) {
                $this->failureCollector->fail($this->store->store_id, $cipherId, '', AlibabaProductFailureCollector::OP_TYPE_ERROR, '获取阿里产品名称失败');
                return false;
            }

            //if ($recoverFlag) {
            //    $crmProduct->create_user = $this->opUserId;
            //    $crmProduct->create_time = $processTime;
            //}

            // 产品同步信息
            $productSync = new PlatformProductSync($this->clientId);
            $productSync->loadByThirdProductId($aliProductPlainTextId, PlatformProductConstants::PLATFORM_ALIBABA);
            if ($productSync->isNew()) {
                $productSync->client_id        = $this->clientId;
                $productSync->create_time      = $processTime;
                $productSync->platform         = PlatformProductConstants::PLATFORM_ALIBABA;
                $productSync->sync_count       = 0;
                $productSync->check_delete_ver = 0;
            }
            $productSync->sync_time   = $processTime;
            $productSync->update_time = $processTime;

            $thirdProduct->update_time = $thirdProduct->sync_time = $processTime;

            //最后更新时间未改变 且 不是疑似删除状态 且未被删除，跳过
            if ($isAdd == false && $unModifiedFlag && $thirdProduct->third_delete != PlatformProductConstants::THIRD_DELETED && $thirdProduct->enable_flag == BaseObject::ENABLE_FLAG_TRUE) {
                // 未变更也匹配
                $thirdSkuApi = new PlatformSkuAPI($this->clientId, $this->opUserId);
                $skus        = $aliProduct['product_sku']['skus']['sku_definition'] ?? [];
                foreach ($skus as $sku) {
                    if (empty($sku['sku_id']) || $sku['sku_id'] == -1) continue;

                    $thirdSku = new PlatformSku($this->clientId);
                    $thirdSku->loadByThirdSkuId(PlatformProductConstants::PLATFORM_ALIBABA, $sku['sku_id'], $thirdProduct->platform_product_id);
                    if ($thirdSku->isNew()) continue;

                    // 匹配本地SKU
                    try {
                        $skuId = $this->matchSku($thirdSku);

                        $skuId && $thirdSkuApi->matchTo($thirdSku, $skuId);
                    } catch (\Exception $e) {
                        $skuId = $skuId ?? 0;
                        LogUtil::error("[sync product] platform_sku_id: {$thirdSku->platform_sku_id}, sku_id: {$skuId} ,match error: {$e->getMessage()}");
                        continue;
                    }
                }

                $thirdProduct->update();
                $productSync->isNew() ? $productSync->create() : $productSync->update();

                $this->failureCollector->fail($this->store->store_id, $aliProduct['product_id'], $subject, AlibabaProductFailureCollector::OP_TYPE_IGNORE, '产品信息未变更，忽略');
                return ['product_type' => $productType, 'platform_product_id' => $thirdProduct->platform_product_id, 'third_product_id' => $thirdProduct->third_product_id];
            }

            $skuAttributes                = $aliProduct['product_sku']['sku_attributes']['sku_attribute'] ?? [];
            $newAttributes                = AlibabaProductSyncHelper::formatAliSkuAttributes($skuAttributes);
            $thirdProduct->sku_attributes = AlibabaProductSyncHelper::mergeSkuAttributes($newAttributes, $thirdProduct->sku_attributes ?? []);


            $thirdProduct->name              = $subject ?: $thirdProduct->name;
            $thirdProduct->third_product_id  = $aliProductPlainTextId;
            $thirdProduct->cipher_product_id = $cipherId;
            $thirdProduct->third_store_id    = $this->store->store_id;
            $thirdProduct->from_url          = $aliProduct['pc_detail_url'] ?? '';
            $thirdProduct->enable_flag       = BaseObject::ENABLE_FLAG_TRUE;
            $thirdProduct->third_delete      = PlatformProductConstants::THIRD_NOT_DELETED;

            if ($isRTS !== null)
                $thirdProduct->is_rts = $isRTS ? 1 : 0;

            $thirdProduct->groups      = AlibabaProductSyncHelper::getGroupsById($this->store, $aliProduct['group_id'] ?? 0);
            $thirdProduct->category_id = $aliProduct['category_id'] ?? 0;
            $thirdProduct->status      = self::STATUS_MAP[$aliProduct['status']] ?? 0;

            $infoJson = [];
            foreach ($aliProduct['attributes']['product_attribute'] ?? [] as $productAttribute) {
                $infoJson[] = [
                    'id'    => 0,
                    'name'  => $productAttribute['attribute_name']??'',
                    'type'  => 'input',
                    'value' => $productAttribute['value_name'] ?? '',
                ];

                //产品型号
                if (isset($productAttribute['attribute_name'])
                    &&
                    (strcasecmp($productAttribute['attribute_name'], "Model Number") == 0)
                ){
                    $model = $productAttribute['value_name'] ?? '';
                    if ($model) {
                        $model = mb_substr($model, 0, 255);
                    }
                    $thirdProduct->model = $model;
                }
            }
            $thirdProduct->info_json = $infoJson;

            $imageIds = [];

            foreach ($aliProduct['main_image']['images']['string'] ?? [] as $imageUrl) {
                $image = AlibabaProductSyncHelper::downloadImage($this->clientId, $this->store->store_id, $imageUrl);
                if ($image)
                    $imageIds[] = $image['id'];
            }
            $thirdProduct->images = $imageIds;

            $thirdProduct->price_type = self::PRICE_TYPE_MAP[strtolower($aliProduct['price_type'])] ?? 0;

            if ($aliProduct['product_type'] == 'sourcing') {
                $tradeName                       = 'sourcing_trade';
                $thirdProduct->currency          = $aliProduct[$tradeName]['fob_currency'] ?? '';
                $thirdProduct->min_price         = floatval($aliProduct[$tradeName]['fob_min_price'] ?? 0);
                $thirdProduct->min_price         = round($thirdProduct->min_price, 5);
                $thirdProduct->max_price         = floatval($aliProduct[$tradeName]['fob_max_price'] ?? 0);
                $thirdProduct->max_price         = round($thirdProduct->max_price, 5);
                $thirdProduct->unit              = $aliProduct[$tradeName]['fob_unit_type'] ?? '';
                $thirdProduct->min_quantity      = $aliProduct[$tradeName]['min_order_quantity'] ?? '';
                $thirdProduct->min_quantity_unit = $aliProduct[$tradeName]['min_order_unit_type'] ?? '';
                $thirdProduct->package_remark    = $aliProduct[$tradeName]['packaging_desc'] ?? '';
                //$crmProduct->description = !empty($aliProduct['description']) ? \Util::plainText($aliProduct['description']) : '';//数据量较大
            } else if ($aliProduct['product_type'] == 'wholesale') {
                $tradeName                  = 'wholesale_trade';
                $thirdProduct->unit         = $aliProduct[$tradeName]['unit_type'] ?? '';
                $thirdProduct->currency     = 'USD';
                $thirdProduct->min_price    = 0;
                $thirdProduct->max_price    = $aliProduct[$tradeName]['price'] ?? 0;
                $thirdProduct->max_price    = round($thirdProduct->max_price, 5);
                $thirdProduct->min_quantity = $aliProduct[$tradeName]['min_order_quantity'] ?? 0;

                $thirdProduct->package_volume       = $aliProduct[$tradeName]['volume'] ?? 0;
                $thirdProduct->package_gross_weight = $aliProduct[$tradeName]['weight'] ?? 0;
                $packageSize                        = explode('X', $aliProduct[$tradeName]['package_size'] ?? '');
                $thirdProduct->package_size_length  = !empty($packageSize[0]) ? intval($packageSize[0]) : 0;//长
                $thirdProduct->package_size_weight  = !empty($packageSize[1]) ? intval($packageSize[1]) : 0;//宽
                $thirdProduct->package_size_height  = !empty($packageSize[2]) ? intval($packageSize[2]) : 0;// 高
            }

            // 数量阶梯定价
            $gradientPrices = [];
            $prices         = $aliProduct['product_sku']['skus']['sku_definition'][0]['bulk_discount_prices']['bulk_discount_price'] ?? [];
            foreach ($prices as $price) {
                $gradientPrices[] = [
                    'price'    => $price['price'],
                    'quantity' => $price['start_quantity'],
                ];
            }
            $thirdProduct->gradient_price = $gradientPrices;

            if($thirdProduct->isNew()){
                $createSucc = $thirdProduct->createIfNotExist();
                if(!$createSucc){       // 说明在创建产品前已有其他并发的进程赶在该进程之前把该产品建好了
                    return false;
                }
            }else{
                $thirdProduct->update();
            }

            // 处理sku信息
            $this->processSkus($thirdProduct, $aliProduct['product_sku'] ?? []);

            //保存同步信息
            $productSync->isNew() && $productSync->platform_product_id = $thirdProduct->platform_product_id;    // 如果是更新，则不更新本地的平台产品id避免唯一键冲突报错
            $productSync->store_id            = $this->store->store_id;
            $productSync->third_product_id    = $aliProductPlainTextId;
            $productSync->cipher_product_id   = $cipherId;
            $productSync->last_modified_time  = $aliProduct['gmt_modified'];
            $productSync->sync_count++;
            $productSync->from_task_id = $this->fromTaskId ?: 0;
            $productSync->is_add       = $productSync->is_add ?: ($isAdd ? 1 : 0);
            $productSync->isNew() ? $productSync->checkAndCreate() : $productSync->update();

            $this->getExistsMap()->appendExistItem($cipherId, [
                'ali_product_cipher_id' => $productSync->cipher_product_id,
                'ali_product_id'        => $productSync->third_product_id,
                'last_modified_time'    => $productSync->last_modified_time,
            ]);

            //if ($recoverFlag)
            //    API::recover($this->clientId, $this->opUserId, Recycle::PRODUCT, [$crmProduct->product_id]);

            $this->setLastProductModifiedTime($aliProduct['gmt_modified']);

            LogUtil::info("[store {$this->store->store_id}] sync product: platform_product_id: {$thirdProduct->platform_product_id} name: {$thirdProduct->name}  ali_product_id: {$aliProductPlainTextId}");

            return ['product_type' => $productType, 'platform_product_id' => $thirdProduct->platform_product_id, 'third_product_id' => $thirdProduct->third_product_id];
        } catch (\Exception $exception) {
            LogUtil::info("[ProductSyncProcessor] error: {$exception->getMessage()}, {$exception->getTraceAsString()}");
            AlibabaSyncDelayOrderConfig::getInstance()::setError(Constant::SYNC_ALIBABA_ORDER_PRODUCT);
            $message = $exception->getMessage();
            //错误上报
            if (!($exception instanceof RuntimeException)) {
                ErrorReport::phpError(new \CExceptionEvent(null, $exception), $exception->getTrace());
            }

            $this->failureCollector->fail($this->store->store_id, $aliProduct['product_id'], $aliProduct['subject'] ?? '', AlibabaProductFailureCollector::OP_TYPE_ERROR, $message);
            return false;

        }

    }

    /**
     * 同步产品下sku
     * @param PlatformProduct $thirdProduct
     * @param array $productSku
     * @return void
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public function processSkus(PlatformProduct $thirdProduct, array $productSku)
    {
        $skus          = $productSku['skus']['sku_definition'] ?? [];
        $skuAttributes = AlibabaProductSyncHelper::formatAliSkuAttributes($productSku['sku_attributes']['sku_attribute'] ?? [], true);

        $syncNoSp = false;

        // 无规格产品保存为sku_id=0的数据
        if (empty($skus) || empty($skuAttributes)) {
            $syncNoSp = true;
        }

        // 疑似删除
        $thirdDeleted = AlibabaProductSyncHelper::getSkuIdsOfProduct($this->clientId, $thirdProduct->platform_product_id, true);

        $thirdSkuApi = new PlatformSkuAPI($this->clientId, $this->opUserId);

        // 多规格
        foreach ($skus as $sku) {
            if (empty($sku['sku_id']) || $sku['sku_id'] == -1) {
                // 无规格产品
                $syncNoSp = true;
                continue;
            }

            unset($thirdDeleted[$sku['sku_id']]);

            $thirdSku = new PlatformSku($this->clientId);
            $thirdSku->loadByThirdSkuId(PlatformProductConstants::PLATFORM_ALIBABA, $sku['sku_id'], $thirdProduct->platform_product_id);
            $now = date('Y-m-d H:i:s');
            if ($thirdSku->isNew()) {
                $thirdSku->enable_flag         = BaseObject::ENABLE_FLAG_TRUE;
                $thirdSku->platform            = PlatformProductConstants::PLATFORM_ALIBABA;
                $thirdSku->create_user         = $this->opUserId;
                $thirdSku->create_time         = $now;
                $thirdSku->third_sku_id        = $sku['sku_id'];
                $thirdSku->third_product_id    = $thirdProduct->third_product_id;
                $thirdSku->platform_product_id = $thirdProduct->platform_product_id;
                $thirdSku->is_match            = PlatformProductConstants::UNMATCHED;
            }
            $thirdSku->third_sku_code = $sku['sku_code'] ?? '';
            $thirdSku->attributes     = AlibabaProductSyncHelper::formatAliSku($sku);
            $thirdSku->fob_price      = $sku['bulk_discount_prices']['bulk_discount_price'][0]['price'] ?? 0;
            $thirdSku->third_store_id = $this->store->store_id;
            $thirdSku->enable_flag    = BaseObject::ENABLE_FLAG_TRUE;
            $thirdSku->third_delete   = PlatformProductConstants::THIRD_NOT_DELETED;

            // 获取第一个sku的图片
            if ($thirdSku->attributes) {
                foreach ($thirdSku->attributes as $key => $attribute) {
                    if (!empty($skuAttributes[$key]['values'][$attribute['value_id']]['image_url'])) {
                        $image = AlibabaProductSyncHelper::downloadImage($this->clientId, $this->store->store_id, $skuAttributes[$key]['values'][$attribute['value_id']]['image_url']);
                        if (!$image)
                            continue;
                        $thirdSku->sku_images = [$image['id']];
                        break;
                    }
                }
            }
            $thirdSku->update_user = $this->opUserId;
            $thirdSku->update_time = $now;
            $thirdSku->isNew() ? $thirdSku->create() : $thirdSku->update();

            // 匹配本地SKU
            try {
                $skuId = $this->matchSku($thirdSku);

                $skuId && $thirdSkuApi->matchTo($thirdSku, $skuId);
            } catch (\Exception $e) {
                $skuId = $skuId ?? 0;
                LogUtil::error("[sync product] platform_sku_id: {$thirdSku->platform_sku_id}, sku_id: {$skuId} ,match error: {$e->getMessage()}");
                continue;
            }
        }

        // 疑似删除
        if ($thirdDeleted) {
            $thirdSkuApi->markThirdDeleted(array_values($thirdDeleted));
        }

        if (!$syncNoSp) {
            // 无规格产品标记为疑似删除
            $filter = new PlatformSkuFilter($this->clientId);
            $filter->third_sku_id = 0;
            $filter->platform_product_id = $thirdProduct->platform_product_id;
            $batch = $filter->find();
            $batch->getOperator()->markThirdDeleted();

            return;
        }

        // 无规格(不需要匹配)
        $thirdSku = new PlatformSku($this->clientId);
        $thirdSku->loadByThirdSkuId(PlatformProductConstants::PLATFORM_ALIBABA, 0, $thirdProduct->platform_product_id);
        if ($thirdSku->isNew()) {
            $thirdSku->enable_flag         = BaseObject::ENABLE_FLAG_TRUE;
            $thirdSku->platform            = PlatformProductConstants::PLATFORM_ALIBABA;
            $thirdSku->platform_product_id = $thirdProduct->platform_product_id;
            $thirdSku->third_product_id    = $thirdProduct->third_product_id;
            $thirdSku->third_sku_id        = 0;
            $thirdSku->third_store_id      = $this->store->store_id;
            $thirdSku->is_match            = PlatformProductConstants::UNMATCHED;
            $thirdSku->create_user         = $thirdSku->update_user = $this->opUserId;
            $thirdSku->create_time         = $thirdSku->update_time = date('Y-m-d H:i:s');

            $thirdSku->create();
        }

        if ($thirdSku->enable_flag == BaseObject::ENABLE_FLAG_FALSE || $thirdSku->third_delete == PlatformProductConstants::THIRD_DELETED) {
            $thirdSku->third_delete = PlatformProductConstants::THIRD_NOT_DELETED;
            $thirdSku->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
            $thirdSku->update_user = $this->opUserId;
            $thirdSku->update_time = date('Y-m-d H:i:s');
            $thirdSku->update();
        }
    }

    /**
     * @param PlatformSku $thirdSku
     * @return false|int
     */
    protected function matchSku(PlatformSku $thirdSku)
    {
        // 匹配
        if ($thirdSku->is_match == PlatformProductConstants::MATCHED) {
            LogUtil::info("[processSkus] sku已经是匹配状态, thirdSkuId: $thirdSku->third_sku_id}");
            return false;
        }

        $matchSetting = $this->getMatchSetting();
        if (empty($matchSetting['auto_match_flag'])) {
            LogUtil::info("[processSkus] 未开启自动匹配，clientId: {$this->clientId}, storeId: {$this->store->store_id}");
            return false;
        }

        $pattern = AbstractMatchPattern::produceMatchPattern($this->clientId, $matchSetting['match_pattern']);
        $pattern->setMatchConfig($matchSetting['match_config']);
        if (!$thirdSku->third_sku_id) {
            LogUtil::info("[processSkus] 无规格产品不进行匹配，clientId: {$this->clientId}, thirdSkuId: {$thirdSku->third_sku_id}");
            return false;
        }
        $res         = $pattern->match([$thirdSku->third_sku_code]);
        $localSku    = $res[$thirdSku->third_sku_code] ?? [];

        return $localSku['sku_id'] ?? 0;
    }
}
