<?php
/**
 * Created by PhpStorm.
 * User: tony
 * Date: 2019-02-20
 * Time: 16:46
 */

namespace common\library\risk;

use common\library\account\service\LoginService;
use common\library\cache\CacheAble;
use common\library\cache\CacheAbleTrait;

/**
 *
 * 每位用户同一时间只允许拥有一个有效的psKey
 * psKey UserId Redis Data
 * $list = [
 *    'psKey' => $psKey,
 *    'time' => $time
 * ]
 *
 * 相同的psKey可能拥有多个自动过期的Token
 * psKey Token Redis Data
 * $list = [
 *     'token1' => $time,
 *     'token2' => $time
 * ]
 *
 * Class SecurityUserToken
 * @package common\library\risk
 */
class SecurityUserToken implements CacheAble
{
    use CacheAbleTrait;

    const PREFIX_OF_TOKEN = 'dx:risk:csrf';
    const CACHE_TYPE_OF_USER = 'user';
    const CACHE_TYPE_OF_PSKEY = 'pskey';
    const PSKEY_BUFFER_TIME = 600;
    const TOKEN_BUFFER_TIME = 300;

    private $clientId = null;
    private $userId = null;
    private $psKey = null;

    public function __construct()
    {
        $user = \User::getLoginUser();

        $this->clientId = $user->getClientId();
        $this->userId = $user->getUserId();
        $this->psKey = LoginService::getSkeyValue();
    }

    /**
     * 抢占并生成有效的安全令牌 Token
     *
     * @return array
     */
    public function contend() : array
    {
        $cacheKey = $this->getCacheKey(self::CACHE_TYPE_OF_USER);
        $list = $this->getCache()->hget($cacheKey, $this->userId);

        $token = '';
        $time = 0;

        // 当前有不同的psKey在占用Token，并且还未达到可以抢占Token的时间要求，生成和返回空Token
        if ($list && $list['psKey'] != $this->psKey && time() - $list['time'] < self::TOKEN_BUFFER_TIME) {
            $service = new SecurityToken();
            list($token, $time) = $service->generate();
            $this->generatePsKey($token, $time);
            return [$token, $time];
        }

        // 白名单用户直接处理
        if (service\SecurityTokenService::isWhiteListUser($this->userId)) {
            $service = new SecurityToken();
            list($token, $time) = $service->generate();
            $this->generatePsKey($token, $time);
            return [$token, $time];
        }

        // 抢占psKey，并生成有效Token
        if (empty($token)) {
            list($token, $time) = $this->generateToken();
        }

        return [$token, $time];
    }

    /**
     * 生成 PSKEY 关联的 Token 列表
     *
     * @param $token
     * @param $time
     */
    private function generatePsKey($token, $time)
    {
        $cacheKey = $this->getCacheKey(self::CACHE_TYPE_OF_PSKEY) . ":{$this->psKey}";
        $list = $this->getCache()->get($cacheKey);

        if ($list) {
            foreach ($list as $key => $item) {
                if ($time - $item >= self::TOKEN_BUFFER_TIME) {
                    unset($list[$key]);
                }
            }
        }

        $list[$token] = $time;
        $this->getCache()->set($cacheKey, $list, 'EX', self::PSKEY_BUFFER_TIME);
    }

    /**
     * 生成有效的安全令牌
     *
     * @return array($token, $timestamp)
     */
    private function generateToken() : array
    {
        $time = time();
        $cacheKey = $this->getCacheKey(self::CACHE_TYPE_OF_USER);
        $data = $this->getCache()->hget($cacheKey, $this->userId);
        $service = new SecurityToken();
        list($token, $timestamp) = $service->generate();
        $this->generatePsKey($token, $time);

        $list = [
            'psKey' => $this->psKey,
            'time' => $time
        ];

        if ($data && $data['psKey'] == $this->psKey) {
            $list['time'] = $data['time'];
        }

        $this->getCache()->hset($cacheKey, $this->userId, $list);
        return [$token, $timestamp];
    }

    /**
     * 验证安全令牌是否有效
     *
     * @param string $token
     * @return bool
     */
    public function verifyToken(string $token) : bool
    {
        $time = time();
        $cacheKey = $this->getCacheKey(self::CACHE_TYPE_OF_USER);
        $list = $this->getCache()->hget($cacheKey, $this->userId);

        if (empty($list) || $list['psKey'] != $this->psKey) {
            return false;
        }

        $cacheKey = $this->getCacheKey(self::CACHE_TYPE_OF_PSKEY) . ":{$this->psKey}";
        $list = $this->getCache()->get($cacheKey);

        if (empty($list)) {
            return false;
        }

        foreach ($list as $key => $item) {
            if ($key === $token && $time - $item <= self::TOKEN_BUFFER_TIME) {
                return true;
            }
        }

        return false;
    }

    /*
    |--------------------------------------------------------------------------
    | Cache
    |--------------------------------------------------------------------------
    |
    |
    */

    /**
     *
     * @return string
     */
    public function getCacheKeyPrefix()
    {
        return SecurityToken::PREFIX_OF_TOKEN;
    }

    /**
     * 清除数据
     */
    public function flush()
    {
        $cacheKey = $this->getCacheKey(self::CACHE_TYPE_OF_USER);
        $list = $this->getCache()->hget($cacheKey, $this->userId);

        if ($list && $list['psKey'] === $this->psKey) {
            $this->getCache()->hdel($cacheKey, [$this->userId]);
        }
    }
}