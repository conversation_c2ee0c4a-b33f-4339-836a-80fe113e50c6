<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2018/9/13
 * Time: 下午5:42
 */

namespace common\library\risk;


use common\components\BaseObject;
use common\library\mail\setting\white\MailWhiteListService;
use common\library\risk\service\RiskFeedbackService;
use common\library\util\SqlBuilder;

/**
 * Class RiskEvent
 * @package common\library\risk
 * @method RiskEventFormatter getFormatter()
 * @property string $event_id
 * @property integer $client_id
 * @property integer $user_id
 * @property integer $level
 * @property integer $type
 * @property string $refer_id
 * @property integer $reason
 * @property integer $risk_id
 * @property integer $notice_flag
 * @property string $create_time
 * @property string $update_time
 * @property array $data
 * @property array $reason_list
 * @property integer $feedback_flag
 * @property array $blacklist
 * @property array $whitelist
 */
class RiskEvent extends BaseObject
{

    /**
     * @return \RiskEvent|string
     */
    public function getModelClass()
    {
        return \RiskEvent::class;
    }

    public function __construct($clientId, $id=null)
    {
        $this->client_id = $clientId;

        $this->_formatter = new RiskEventFormatter($this->client_id);
        $this->_formatter->setNeedStrip(false);

        if( $id)
            $this->loadById($id);
    }

    public function loadById($id)
    {
        $model = $this->getModelClass()::model()->find('event_id = :event_id', [':event_id' => $id]);
        $this->setModel($model);
        return $this;
    }

    public function loadByUnique($clientId, $type, $referId)
    {
        $model = $this->getModelClass()::model()->find('client_id= :client_id and type=:type and refer_id=:refer_id ',
            [':client_id' => $clientId, ':type'=>$type, ':refer_id'=>$referId]);
        $this->setModel($model);
        return $this;
    }

    public function loadByReason($clientId, $type, $referId, $reason)
    {

        $reasonJson = json_encode([['reason' => $reason]]);
        $sql = "client_id= :client_id and type=:type and refer_id=:refer_id and reason_list @>'{$reasonJson}' order by create_time desc ";
        $params = [':client_id' => $clientId, ':type'=>$type, ':refer_id'=>$referId];
        $model = $this->getModelClass()::model()->find($sql, $params);
        $this->setModel($model);
        return $this;
    }

    public function existReason($reason)
    {
        if( $this->isNew() )
        {
            return false;
        }

        if( in_array($reason, array_column($this->reason_list, 'reason')))
        {
            return true;
        }

        return false;
    }

    public function addReason($reason, $risk_id, $level)
    {
        $item = compact('reason', 'risk_id', 'level');

        $reasonList =  $this->reason_list ? : [];
        $exist = false;
        foreach ( $reasonList as $k => $value )
        {
            if( $value['reason'] == $reason )
            {
                $reasonList[$k] = $item;
                $exist = true;
                break;

            }
        }

        !$exist  && $reasonList[] = $item;

        $this->reason_list = $reasonList;
    }


    protected function beforeSave()
    {
        $this->_attributes['data'] = str_replace("'", "''", json_encode((object)$this->_attributes['data']??[], JSON_UNESCAPED_UNICODE));
        $this->_attributes['reason_list'] = json_encode($this->_attributes['reason_list']??[]);
        $this->_attributes['blacklist'] = json_encode($this->_attributes['blacklist']??[]);
        $this->_attributes['whitelist'] = json_encode($this->_attributes['whitelist']??[]);
        $this->_attributes['update_time'] = $this->_attributes['update_time'] ?? date('Y-m-d H:i:s');

        if ($this->isNew()) {
            $this->_attributes['create_time'] = $this->_attributes['create_time'] ?? date('Y-m-d H:i:s');
        }

        $result = parent::beforeSave();
        return $result;
    }

    public function mailFeedback( $type, array  $emails, $userId=0)
    {
        $userId = $userId?:$this->user_id;

        if( $this->type != RiskConstants::TYPE_MAIL_RECEIVE  && $this->type != RiskConstants::TYPE_MAIL_SEND )
        {
            throw new \RuntimeException('不支持的反馈类型');
        }

        $this->feedback_flag = 1;

        $blacklist = $this->blacklist;
        $whitelist = $this->whitelist;

        if( $type == RiskConstants::RISK_FEEDBACK_BLACKLIST)
        {
            $blacklist = array_unique(array_unique(array_merge($blacklist, $emails)));
            \common\library\mail\setting\black\Help::batchSaveMailBlack($this->client_id,$userId,1,$emails);
            $whitelist = array_values(array_diff($blacklist, $whitelist));
            MailWhiteListService::batchDelete($this->client_id, $userId, [], $emails);
            $result = 0;

            \LogUtil::error("风险反馈 blacklist: event_id: {$this->event_id} user_id: {$userId} email:".json_encode($emails));
        }elseif ( $type == RiskConstants::RISK_FEEDBACK_WHITELIST )
        {
            $whitelist = array_values(array_unique(array_merge($whitelist, $emails)));
            $blacklist = array_values(array_diff($whitelist, $blacklist ));
            \common\library\mail\setting\black\Help::batchDelete($this->client_id,$userId,[],$emails);
            MailWhiteListService::batchSaveMailWhite($this->client_id, $userId, 1, $emails);
            $result = 1;
            \LogUtil::error("风险反馈 whitelist: event_id: {$this->event_id} user_id: {$userId} email:".json_encode($emails));


        }else
        {
            throw new \RuntimeException('不支持的反馈类型');
        }

        $this->blacklist = $blacklist;
        $this->whitelist = $whitelist;

        //汇总到风控中心
        $dictionaryValues = array_map(function ($email)use($result){
            return ['type' => RiskConstants::RISK_FEEDBACK_TYPE_EMAIL, 'value' => $email, 'result' => $result ];
        }, $emails);

        RiskFeedbackService::feedback($this->client_id, $this->user_id, $dictionaryValues);

        $this->save();
    }

    public function getLatestOne($startDate = '', $endDate = '')
    {
        $sql = "client_id = :client_id";
        $params[':client_id'] = $this->client_id;
        if (!empty($startDate) && !empty($endDate)){
            SqlBuilder::buildDateRange('', 'create_time', $startDate, $endDate,$sql, $params);
        }
        $sql .= " order by event_id desc ";
        return $this->getModelClass()::model()->find($sql, $params);
    }

}
