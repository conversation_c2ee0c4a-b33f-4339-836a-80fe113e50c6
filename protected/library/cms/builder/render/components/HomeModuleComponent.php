<?php
/**
 * Created by <PERSON><PERSON><PERSON>tor<PERSON>.
 * User: bing
 * Date: 2020/9/7
 * Time: 15:24
 */

namespace common\library\cms\builder\render\components;

use common\components\BaseObject;
use common\library\cms\builder\render\BaseComponent;
use common\library\cms\CmsConstants;
use common\library\cms\Helper;
use common\library\cms\material\MaterialService;
use common\library\cms\setting\PageSetting;
use common\library\cms\setting\PageSettingService;
use common\library\cms\setting\process\Homepage;
use common\library\setting\item\ItemSettingConstant;

class HomeModuleComponent extends BaseComponent {

    protected $_pageSetting;

    const MODULE_FUNCTION_MAP = [
        Homepage::SETTING_HOME_AD => 'banners',
        Homepage::SETTING_HOME_PRODUCT_GROUP => 'moduleProductGroup',
        Homepage::SETTING_HOME_PRODUCT_LIST => 'moduleProductList',
        Homepage::SETTING_HOME_ARTICLE_LIST => 'moduleArticleList',
        Homepage::SETTING_HOME_COLLECTION_LIST => 'moduleImageCollectionList',
        Homepage::SETTING_HOME_ENTERPRISE => 'moduleEnterprise'
    ];

    /**
     * @param string $item_id
     * @return mixed
     */
    public function getPageSetting($item_id = '')
    {
        if( $this->_pageSetting === null ){
            $pageSetting = new PageSetting($this->clientId, $this->siteId, PageSettingService::SETTING_CUSTOM_HOME_PAGE);
            $this->_pageSetting = $pageSetting->getValue();
        }

        if($item_id) {
            $setting = [];
            foreach($this->_pageSetting as $item) {
                if($item_id == $item['item_id']) {
                    $setting = $item;
                    break;
                }
            }
            return $setting;
        }

        return $this->_pageSetting;

    }

    /**
     * 横幅广告
     * @param $item_id
     * @return array
     */
    public function banners($item_id)
    {
        $bannerSetting = $this->getPageSetting($item_id);
        $data = [];
        if($bannerSetting ?? []) {
            foreach($bannerSetting['item_format'] as $item) {
                $data[] = [
                    'title' => $item['title'],
                    'link' => $item['link'],
                    'type' => $bannerSetting['type'],
                    'image_info' => [
                        'file_id' => $item['file_id'] ?? 0,
                        'file_url' => $item['file_url'] ?? '',
                        'file_name' => $item['file_name'] ?? '',
                        'file_size' => $item['file_size'] ?? 0,
                        'material_id' => $item['material_id']?? 0,
                        'enable_flag' => $item['enable_flag'] ?? 1,
                        'tag_code' => $item['tag_code'] ?? '',
                        'origin' => $item['origin'],
                        'cover_url' => $item['cover_url'] ?? ''
                    ]
                ];
            }
        }

        $bannerSetting['item_format'] = $data;

        return $bannerSetting;
    }

    /**
     * 产品分组
     * @param $item_id
     * @return mixed
     */
    public function moduleProductGroup($item_id)
    {
        $productGroupPageSetting = $this->getPageSetting($item_id);
        $data = null;
        $groupIds = $productGroupPageSetting['item'] ?? [];
        if($groupIds)
        {
            $group = new GroupListComponent($this->render);
            $group->setGroupType(\Constants::TYPE_CMS_PRODUCT);
            $group->setResultType(ItemSettingConstant::RESULT_TYPE_LIST);
            $group->setGroupId($groupIds);
            $group->setShowImageInfo(true);
            $group->setIncludeRoot(false);
            $data = $group->renderData();
        }
        $productGroupPageSetting['item_format'] = $data;
        return $productGroupPageSetting;
    }

    /**
     * 产品列表
     * @param $item_id
     * @return mixed
     */
    public function moduleProductList($item_id)
    {
        $productListPageSetting = $this->getPageSetting($item_id);
        //没数据返回null
        $data = null;
        $productIds = $productListPageSetting['item'] ?? [];

        if(is_array($productIds) && !empty($productIds)) {
            $productList = new ProductListComponent($this->render);
            $productList->setProductIds($productIds);
            $productList->setDisableFlag(BaseObject::ENABLE_FLAG_FALSE);
            $data = $productList->renderData();
        }
        $productListPageSetting['item_format'] = $data;
        return $productListPageSetting;
    }

    /**
     * 文章列表
     * @param $item_id
     * @param int $count
     * @return mixed
     */
    public function moduleArticleList($item_id, $count = 5)
    {
        $articleListPageSetting = $this->getPageSetting($item_id);
        //没数据返回null
        $data = null;
        $articleIds = $articleListPageSetting['item'] ?? [];
        if(is_array($articleIds) && !empty($articleIds) )
        {
            $articleList = new ArticleListComponent($this->render);
            $articleList->setArticleIds($articleIds);
            $articleList->setShowFlag(BaseObject::ENABLE_FLAG_TRUE);
            $articleList->setPageSize($count);
            $data = $articleList->renderData();
        }
        $articleListPageSetting['item_format'] = $data;
        return $articleListPageSetting;
    }


    /**
     * 图集列表
     * @param $item_id
     * @param int $count
     * @return array|null
     */
    public function moduleImageCollectionList($item_id, $count=5)
    {
        $collectionListPageSetting = $this->getPageSetting($item_id);
        //没数据返回null
        $data = null;
        $imageCollectionIds = $collectionListPageSetting['item'] ?? [];
        if(is_array($imageCollectionIds) && !empty($imageCollectionIds) )
        {
            $imageCollectionList = new ImageCollectionListComponent($this->render);
            $imageCollectionList->setImageCollectionIds($imageCollectionIds);
            $imageCollectionList->setShowFlag(BaseObject::ENABLE_FLAG_TRUE);
            $imageCollectionList->setPageSize($count);
            $data = $imageCollectionList->renderData();
        }

        $collectionListPageSetting['item_format'] = $data;
        return $collectionListPageSetting;
    }

    /**
     * 企业信息
     * @param $item_id
     * @return array
     */
    public function moduleEnterprise($item_id)
    {
        $enterprisePageSetting = $this->getPageSetting($item_id);
        $itemFormat = [];
        if(!isset($enterprisePageSetting['item_format']) || !empty($enterprisePageSetting['item_format'])) {
            $materialIds = $enterprisePageSetting['item'] ?? [];
            if ($materialIds) {
                $fileInfos = Helper::getMaterialMapFileInfo($this->clientId, $materialIds);
                foreach ($fileInfos as $materialId => &$item) {
                    if($item['enable_flag'] == BaseObject::ENABLE_FLAG_FALSE) {
                        unset($fileInfos[$materialId]);
                        continue;
                    }
                }

                // 视频，需要获取封面的coverUrl
                if($enterprisePageSetting['type'] == MaterialService::MATERIAL_TYPE_VIDEO) {
                    $coverFileInfos = Helper::getMaterialMapCoverInfo($this->clientId, $materialIds);
                }
            }
            foreach ($materialIds as $p => $materialId) {
                if (!isset($fileInfos[$materialId])) {
                    unset($enterprisePageSetting['item'][$p]);
                    continue;
                }
                $newItem = $fileInfos[$materialId];
                $newItem['cover_url'] = '';
                if(isset($coverFileInfos[$materialId])) {
                    $newItem['cover_url'] = $coverFileInfos[$materialId]['file_url'] ?? '';
                }
                $itemFormat[] = $newItem;
            }
        }

        $enterprisePageSetting['url'] = '';
        if(isset($enterprisePageSetting['relate_type'])) {
            if($enterprisePageSetting['relate_type'] == 1 && $enterprisePageSetting['relate_id'] > 0) {
                $articleList = new ArticleListComponent($this->render);
                $articleList->setArticleIds($enterprisePageSetting['relate_id']);
                $result = $articleList->renderData();
                $mapList = array_column($result['list']?? [],null, 'article_id');
                $data = $mapList[$enterprisePageSetting['relate_id']] ?? [];
                // 文章被删除了
                if(empty($data)) {
                    $enterprisePageSetting['relate_type'] = $enterprisePageSetting['relate_id'] = 0;
                    $enterprisePageSetting['description'] = '';
                }else {
                    $enterprisePageSetting['url'] = $data['url'] ?? '';
                    $enterprisePageSetting['article_title'] = $data['title'] ?? '';
                    $enterprisePageSetting['description'] = \Util::plainText($data['content']);
                }
            }
        }else {
            $enterprisePageSetting['relate_type'] = $enterprisePageSetting['relate_id'] = 0;
            $enterprisePageSetting['description'] = '';
        }

        $enterprisePageSetting['item_format'] = $itemFormat;

        return $enterprisePageSetting;
    }

    public function renderData()
    {
        $settings = $this->getPageSetting();
        $data = [];
        if(is_array($settings) && !empty($settings)) {
            $moduleKeys = array_column($settings, 'key','item_id');
            foreach($moduleKeys as $itemId=>$key) {
                if(isset(self::MODULE_FUNCTION_MAP[$key])) {
                    $function = self::MODULE_FUNCTION_MAP[$key];
                    $data[] = call_user_func([$this, $function], $itemId);
                }
            }
        }
        return $data;
    }
}
