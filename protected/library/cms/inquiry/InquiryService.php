<?php
/**
 * Created by <PERSON>p<PERSON>tor<PERSON>.
 * User: bing
 * Date: 2020/04/27
 * Time: 18:34
 */

namespace common\library\cms\inquiry;

use CmsCrmClientRelation;
use common\components\BaseObject;
use common\library\api\InnerApi;
use common\library\cms\setting\PageSettingService;
use common\library\customer_convert\ConvertHandler;
use common\library\google_ads\ga\GaSite;
use common\library\google_ads\lead\SiteSessionLead;
use common\library\google_ads\track\Params;
use common\library\google_ads\track\TrackService;
use common\library\lead\Constant;
use common\library\lead\Lead;
use common\library\matomo\MatomoVisitService;
use common\library\queue_v2\job\ShopInquiryQualityEvalJob;
use common\library\queue_v2\job\SyncCmsInquiryStatusToInconsistentClientJob;
use common\library\queue_v2\job\SyncCmsInquiryToInconsistentClientJob;
use common\library\report\error\ErrorReport;
use common\library\setting\library\origin\Origin;
use common\library\setting\library\status\Status;
use common\library\setting\library\stage\Stage;
use common\library\shops\Constants;
use common\library\shops\Helper;
use common\library\shops\inquiry\ShopsInquiryQualityEvalApply;
use common\library\shops\inquiry\ShopsInquiryQualityEvalParams;
use common\library\trail\events\SiteTrackEvents;
use common\library\trail\TrailConstants;
use common\library\util\SqlBuilder;
use common\models\shop\AimsLanguage;
use LeadInquiryDataModel;
use RuntimeException;

class InquiryService {

    // 模块类型
    const MODULE_TYPE_COMMON = TrackService::MODULE_OTHER;
    const MODULE_TYPE_CME = TrackService::MODULE_CMS; //CMS

    // 询盘类型
    const INQUIRY_TYPE_FORM = 1;//表单
    const INQUIRY_TYPE_EMAIL = 2;//订阅
    const INQUIRY_TYPE_CHAT = 3;//聊天
    const INQUIRY_TYPE_MAIL = 4;//邮件
    const INQUIRY_TYPE_REGISTER = 5;//注册账号
    const INQUIRY_TYPE_DOWNLOAD = 6;//下载文件

    const INQUIRY_TYPE_MAP = [
        self::INQUIRY_TYPE_FORM => 'Form',
        self::INQUIRY_TYPE_EMAIL => 'Email Subscribe',
        self::INQUIRY_TYPE_CHAT => 'Live Chat',
        self::INQUIRY_TYPE_MAIL => 'Mail',
        self::INQUIRY_TYPE_REGISTER => 'Register',
        self::INQUIRY_TYPE_DOWNLOAD => 'Download',
        self::ORIGIN_TYPE_UNKNOWN => 'Other'
    ];

    /**
     * 访客来源类型定义
     */
    const ORIGIN_TYPE_UNKNOWN = 0;//未知
    const ORIGIN_TYPE_DAS = 1;//广告
    const ORIGIN_TYPE_ORGANIC_SEARCH = 2;//自然搜索
    const ORIGIN_TYPE_SOCIAL = 3;//社交网站
    const ORIGIN_TYPE_DIRECT = 4;//直接访问
    const ORIGIN_TYPE_MAIL = 5; //邮件
    const ORIGIN_TYPE_CUSTOMER_SERVICE = 6;//okki shop 代表小二创建的询盘 stayreal创建
    const ORIGIN_TYPE_CUSTOMER = 7;//okki shop 代表买家创建的询盘
    const ORIGIN_TYPE_CUSTOMER_WEB_CHAT = 8;//okki shop 代表小二创建的询盘 站群创建
    const ORIGIN_TYPE_CUSTOMER_CONVERSION = 9; //okki shop 代表会话表单创建

    /**
     * 询盘访客来源
     */
    const INQUIRY_ORIGIN_TYPE_MAP = [
        self::ORIGIN_TYPE_UNKNOWN => 'Other',//未知
        self::ORIGIN_TYPE_DAS => 'Ad serving',//广告
        self::ORIGIN_TYPE_ORGANIC_SEARCH => 'Natural flow',//自然流量
        self::ORIGIN_TYPE_SOCIAL => 'Social network',//社交网站
        self::ORIGIN_TYPE_DIRECT => 'Direct',//直接访问
        self::ORIGIN_TYPE_MAIL => 'Other',//邮件 改为未知
//        self::ORIGIN_TYPE_MAIL => 'Origin Mail',//邮件 改为未知
    ];

    /**
     * Shops 来源表单定义
     */
    const INQUIRY_FORM_TYPE_NODE = 0; //模板默认表单
    const INQUIRY_FORM_TYPE_CONVERSION = 1; //对话表单
    const INQUIRY_FORM_TYPE_CUSTOM = 2; //自定义表单

    /**
     * 定义细分自然流量和社交流量来源
     */
    const ORIGIN_MAP = [
        // 自然流量细分类型,sub_origin=>[regular_domain=>'','name'=>'']
        self::ORIGIN_TYPE_ORGANIC_SEARCH => [
            // 美国google
            'google' => [
                'regular_domain' => '.google.',
                'name' => 'Google Search',
            ],
            // 美国微软bing
            'bing' => [
                'regular_domain' => '.bing.',
                'name' => 'Bing Search',
            ],
            // 美国雅虎
            'yahoo' => [
                'regular_domain' => '.yahoo.',
                'name' => 'Yahoo Search',
            ],
            // 俄罗斯yandex
            'yandex' => [
                'regular_domain' => '.yandex.',
                'name' => 'Yandex Search',
            ],
            // 中国
            'baidu' => [
                'regular_domain' => '.baidu.com',
                'name' => 'Baidu Search',
            ],
            // 法国搜索引擎qwant
            'qwant' => [
                'regular_domain' => '.qwant.com',
                'name' => 'Qwant Search',
            ],
            // 韩国naver
            'naver' => [
                'regular_domain' => '.naver.com',
                'name' => 'Qaver Search',
            ],
            // 日本goo
            'goo' => [
                'regular_domain' => '.goo.ne.jp',
                'name' => 'Goo Search',
            ],
            // 捷克seznam
            'seznam' => [
                'regular_domain' => '.seznam.cz',
                'name' => 'Seznam Search',
            ],
            // 葡萄牙
            'sapo' => [
                'regular_domain' => '.sapo.pt',
                'name' => 'Sapo Search',
            ],
            // 英国
            'ask' => [
                'regular_domain' => '.ask.co.',
                'name' => 'Ask Search',
            ],
            // 中国搜狗
            'sogou' => [
                'regular_domain' => '.sogou.com',
                'name' => 'Sogou Search',
            ],

        ],

        // 社交网站细分
        self::ORIGIN_TYPE_SOCIAL => [
            'facebook' => [
                'regular_domain' => '.facebook.',
                'name' => 'Facebook',
            ],
            'twitter' => [
                'regular_domain' => '.twitter.',
                'name' => 'Twitter',
            ],
            'Linkedin' => [
                'regular_domain' => '.linkedin.',
                'name' => 'Linkedin',
            ],
            'instagram' => [
                'regular_domain' => '.instagram.',
                'name' => 'Instagram',
            ],
            'youtube' => [
                'regular_domain' => '.youtube.',
                'name' => 'Youtube',
            ],
            'wechat' => [
                'regular_domain' => '.wechat.com',
                'name' => 'Wechat',
            ],
            'vk' => [
                'regular_domain' => '.vk.com',
                'name' => 'Vk',
            ],
            'reddit' => [
                'regular_domain' => '.reddit.com',
                'name' => 'Reddit',
            ],
            'quora' => [
                'regular_domain' => '.quora.com',
                'name' => 'Quora',
            ],
            'pinterest' => [
                'regular_domain' => '.pinterest.com',
                'name' => 'Pinterest',
            ],
        ],
    ];

    /**
     * 询盘阶段、状态
     */
    const INQUIRY_STATUS_STAGE_LEAD         = 'inquiry_stage_lead';
    const INQUIRY_STATUS_STAGE_OPPORTUNITY  = 'inquiry_stage_opportunity';
    const INQUIRY_STATUS_STAGE_ORDER        = 'inquiry_stage_order';

    const INQUIRY_STATUS_INVALID                = 0; //无效
    const INQUIRY_STATUS_WAIT_HANDLE            = 1; //待处理
    const INQUIRY_STATUS_COMPLETE_INFO          = 2; //完善信息
    const INQUIRY_STATUS_INITIATIVE_TO_CONTACT  = 3; //初次触达
    const INQUIRY_STATUS_INTERKNIT              = 4; //联系互动
    const INQUIRY_STATUS_CONVERSION             = 5; //转化为客户
    const INQUIRY_STATUS_OPPORTUNITY_GOING      = 6; //转化为商机,进行中
    const INQUIRY_STATUS_OPPORTUNITY_GOT        = 7; //转化为赢单商机
    const INQUIRY_STATUS_ORDER                  = 8; //转化为订单

    const INQUIRY_STATUS_MAP = [
        self::INQUIRY_STATUS_INVALID                => 'Invalid',
        self::INQUIRY_STATUS_WAIT_HANDLE            => 'Wait for handle',
        self::INQUIRY_STATUS_COMPLETE_INFO          => 'Complete info',
        self::INQUIRY_STATUS_INITIATIVE_TO_CONTACT  => 'Initiative to contact',
        self::INQUIRY_STATUS_INTERKNIT              => 'Interknit',
        self::INQUIRY_STATUS_CONVERSION             => 'Conversion to customer',
        self::INQUIRY_STATUS_OPPORTUNITY_GOING      => 'Opportunity going',
        self::INQUIRY_STATUS_OPPORTUNITY_GOT        => 'Opportunity got',
        self::INQUIRY_STATUS_ORDER                  => 'Conversion to order',
    ];

    const INQUIRY_STATUS = [
        self::INQUIRY_STATUS_STAGE_LEAD         => [
            \common\library\setting\library\status\Status::SYS_STATUS_INVALID                => self::INQUIRY_STATUS_INVALID,
            \common\library\setting\library\status\Status::SYS_STATUS_NONE                   => self::INQUIRY_STATUS_WAIT_HANDLE,
            \common\library\setting\library\status\Status::SYS_STATUS_NEW                    => self::INQUIRY_STATUS_WAIT_HANDLE,
            \common\library\setting\library\status\Status::SYS_STATUS_COMPLETE_INFO          => self::INQUIRY_STATUS_COMPLETE_INFO,
            \common\library\setting\library\status\Status::SYS_STATUS_INITIATIVE_TO_CONTACT  => self::INQUIRY_STATUS_INITIATIVE_TO_CONTACT,
            \common\library\setting\library\status\Status::SYS_STATUS_INTERKNIT              => self::INQUIRY_STATUS_INTERKNIT,
            \common\library\setting\library\status\Status::SYS_STATUS_CONVERSION             => self::INQUIRY_STATUS_CONVERSION,
        ],
        self::INQUIRY_STATUS_STAGE_OPPORTUNITY  => [
            Stage::STAGE_ON_GOING_STATUS             => self::INQUIRY_STATUS_OPPORTUNITY_GOING,
            Stage::STAGE_WIN_STATUS                  => self::INQUIRY_STATUS_OPPORTUNITY_GOT,
        ],
        self::INQUIRY_STATUS_STAGE_ORDER        => self::INQUIRY_STATUS_ORDER
    ];

    /**
     * 询盘回传状态
     */
    const CONVERSION_UPLOAD_STATUS_NOT_UPLOADED = 0; // 未回传
    const CONVERSION_UPLOAD_STATUS_UPLOADED = 1; // 已回传
    const CONVERSION_UPLOAD_STATUS_FORBIDDEN = -1; // 禁止回传

    const COUNTRY_CODE_MAP =  array("AL" => "Albania", "DZ" => "Algeria", "AF" => "Afghanistan", "AR" => "Argentina", "AZ" => "Azerbaijan", "AE" => "United Arab Emirates", "AW" => "Aruba", "OM" => "Oman", "EG" => "Egypt", "ET" => "Ethiopia", "IE" => "Ireland", "EE" => "Estonia", "AD" => "Andorra", "AO" => "Angola", "AI" => "Angola", "AG" => "Antigua and Barbuda", "AT" => "Austria", "AU" => "Australia", "BB" => "Barbados", "PG" => "Papua,Territory of", "BS" => "Bahamas", "PK" => "Pakistan", "PY" => "Paraguay", "BH" => "Bahrain", "PA" => "Panama", "BR" => "Brazil", "BY" => "Byelorussian SSR", "BM" => "Bermuda", "BG" => "Bulgaria", "BJ" => "Benin", "BE" => "Belgium", "IS" => "Iceland", "PR" => "Puerto Rico", "PL" => "Poland", "BA" => "Bosnia Hercegovina", "BO" => "Bolivia", "BZ" => "Belize", "BW" => "Botswana", "BT" => "Bhutan", "VI" => "Vigin Islands(U.S.)", "VG" => "Virgin Islands(British)", "BF" => "Burkina Faso", "BI" => "Burundi", "BV" => "Bouvet Island", "KP" => "North Korea", "GQ" => "Equatorial Guinea", "DK" => "Denmark", "DE" => "Germany", "TP" => "East Timor", "TG" => "Togo", "DO" => "Dominica", "DM" => "Gominica", "RU" => "Russia", "EC" => "Ecuador", "FR" => "France", "PF" => "French Polynesia", "GF" => "French Guiana", "TF" => "French Southern Territoties", "VA" => "Vatican", "PH" => "Philippines", "FJ" => "Fiji", "FI" => "Finland", "CV" => "Cape Verde,Republic of", "FK" => "Falkland Islands", "GM" => "Gambia", "CG" => "Congo", "CO" => "Colombia", "CR" => "Costa rica", "GD" => "Grenada", "GL" => "Greenland", "GE" => "Georgia", "CU" => "Cuba", "GP" => "Guadeloupe", "GU" => "Guam", "GY" => "Guyana", "KZ" => "Kazakstan", "HT" => "Haiti", "KR" => "Korea", "NL" => "Netherlands", "HN" => "Honduras", "KI" => "Kiribati", "DJ" => "Djibouti", "KG" => "Kyrgyzstan", "GN" => "Guinea", "GW" => "Guinea-Bissau", "CA" => "Canada", "GH" => "Ghana", "GA" => "Gabon", "KH" => "Cambodia", "CZ" => "Czech Republic", "ZW" => "Zimbabwe", "CM" => "Cameroon", "QA" => "Qatar", "KY" => "Cayman Islands", "KM" => "Comoros", "KW" => "kuwait", "CC" => "COCOS Islands", "HR" => "Croatia", "KE" => "Kenya", "CK" => "Cook Island", "LV" => "Latvia", "LS" => "Lesotho", "LA" => "Laos", "LB" => "Lebanon", "LT" => "Lithuania", "LR" => "Liberia", "LY" => "Libya", "LI" => "Liechtenstein", "LU" => "Luxembourg", "RW" => "Rwanda", "RO" => "Romania", "MG" => "Malagasy", "MV" => "Maldives", "MT" => "Malta", "MW" => "Malawi", "MY" => "Malaysia", "ML" => "Mali", "MH" => "Marshall Islands", "MU" => "Mauritius", "MR" => "Mauritania", "US" => "America", "UM" => "American Samoa", "MN" => "Mongolia", "BD" => "Bangladesh", "PE" => "Peru", "FM" => "Micronesia", "MM" => "Burma", "MD" => "Moldova,Republic of", "MA" => "Morocco", "MC" => "Monaco", "MZ" => "Mozambique", "MX" => "Mexico", "NA" => "Namibia", "ZA" => "South Africa", "AQ" => "Antarctica", "YU" => "Yugoslavia", "NR" => "Naura", "NP" => "Nepal", "NI" => "Nicaragua", "NE" => "Niger", "NG" => "Nigeria", "NU" => "Niue", "NO" => "Norway", "PW" => "Palau", "PN" => "Pitcairn Islands", "PT" => "Portugal", "JP" => "Japan", "SE" => "Sweden", "CH" => "Switzerland", "SV" => "El Salvador", "SL" => "Sierra leone", "SN" => "Senegal", "CY" => "Cyprus", "SC" => "Seychelles", "SA" => "Saudi Arabia", "CX" => "Christmas Island", "ST" => "Sao Tome and Principe", "SH" => "St.Helena", "LC" => "St. Lucia", "SM" => "San Marino", "LK" => "Sri Lanka", "SK" => "Slovakia", "SI" => "Slovenia", "SZ" => "Swaziland", "SD" => "Sudan", "SR" => "Surinam", "SU" => "USSR(formerly)", "SB" => "Solomon Islands", "SO" => "Somali", "TJ" => "Tsjikistan", "TH" => "Thailand", "TZ" => "Tanzania", "TO" => "Tonga", "TT" => "Trinidad and Tobago", "TN" => "Tunisia", "TV" => "Tuvalu", "TR" => "Turkey", "TM" => "Turkomanstan", "TK" => "Tokela", "GT" => "Guatemala", "VE" => "Venezuela", "BN" => "Brunei Darussalam", "UG" => "Uganda", "UA" => "Ukraine", "UY" => "uruguay", "UZ" => "Uzbekstan", "ES" => "Spain", "EH" => "West Sahara", "WS" => "Western Samoa", "GR" => "Greece", "CI" => "Lvory Coast", "SG" => "Singapore", "NC" => "New Caledonia", "NZ" => "New Zealand", "HU" => "Hungary", "SY" => "Syria", "JM" => "Jamaica", "AM" => "Armenia", "YE" => "Yemen", "IQ" => "Iraq", "IR" => "Iran", "IL" => "Israel", "IT" => "Italy", "IN" => "India", "ID" => "Indonesia", "GB" => "United Kingdom", "UK" => "England", "IO" => "British Indian Ocean Territory", "JO" => "Jordan", "VN" => "Vietnam", "ZM" => "Zambia", "ZR" => "Zaire", "TD" => "Chad", "GI" => "Gibraltar", "CL" => "Chile", "CF" => "The Central African Republic", "CN" => "China", "MO" => "Macao", "TW" => "Taiwan", "HK" => "Hong Kong", "CW"=>"Curacao");


    /**
     * @param $clientId
     * @param $gsSiteId
     * @param $type
     * @param string $startTime
     * @param string $endTime
     * @return bool|\CDbDataReader|int|mixed|string
     */
    public static function countInquiry($clientId, $gsSiteId, $startTime='', $endTime='') {
        $inquiryList = new \common\library\cms\inquiry\InquiryList($clientId);
        $inquiryList->setGaSiteId($gsSiteId);
        $inquiryList->setModule(null);
        $inquiryList->setStartTime($startTime);
        $inquiryList->setEndTime($endTime);

        return $inquiryList->count() ?? 0;
    }

    /**
     * @param $clientId
     * @param array $gclids
     * @return array
     */
    public static function getAdsCampaignInfo($clientId, array $gclids) {
        if(empty(array_filter($gclids))) {
            return [];
        }

        $gclidsArray = array_map(function($elem){
            $elem = \Util::escapeDoubleQuoteSql($elem);
            return "'{$elem}'";
        }, $gclids);

        $gclidstr = implode(',', $gclidsArray);

        $sql = "SELECT A.gclid,A.criteria_parameters as keywords,a.ad_group_id as ad_group,B.campaign_name,B.ad_group_id,B.ad_group_name FROM tbl_google_ads_gclid AS A LEFT JOIN tbl_google_ads_campaign AS B ON A.client_id=B.client_id AND A.campaign_id=B.campaign_id WHERE A.client_id=:client_id AND A.gclid in ({$gclidstr})";

        $data = \ProjectActiveRecord::getDbByClientId($clientId)->createCommand($sql)->queryAll(true,[
            ':client_id' => $clientId,
        ]);

        $result = [];
        foreach ($data as $item) {
            $result[$item['gclid']][] = $item;
        }

        return $result;

    }

    /**
     * 计算某个表单或者订阅表单的关联询盘数量
     * @param $clientId
     * @param $siteId
     * @param $formId
     * @return bool|\CDbDataReader|mixed|string
     */
    public static function countInquiryNum($clientId, $siteId, $formId) {
        $inquiryList = new \common\library\cms\inquiry\InquiryList($clientId);
        $inquiryList->setSiteId($siteId);
        $inquiryList->setFormId($formId);
        $count = $inquiryList->count();
        return $count;
    }

    /**
     * @param array $sendEmails
     * @param $buildHtmlData
     * @return bool|void
     */
    public static function send(array $sendEmails, $buildHtmlData ,$eventType)
    {
        try {
            if(empty($sendEmails) || empty($buildHtmlData)) {
                \LogUtil::info("sendEmails || buildHtmlData empty");
                return;
            }

            $mailTemplate = \common\library\ames\AmesConstants::$dtcMailTemplateMap[$eventType] ?? 'get_inquiry';
            $email = $buildHtmlData['form_info']['email'] ?? '';
            $subject = '【官网询盘通知】';
            if($eventType == Params::EVENT_TYPE_DTC_ACCOUNT_REGISTER){
                $accountMap = ['email','name'];
                foreach ($buildHtmlData['form_info']['data'] as $formKey=>$formValue){
                    if(!in_array($formKey,$accountMap)){
                        unset($buildHtmlData['form_info']['data'][$formKey]);
                    }
                }
                if($email) {
                    $subject = "【官网询盘通知】{$email}注册了网站会员";
                }
            }else{
                $countryObj = \CountryService::getCountryByAlpha2($buildHtmlData['ip_country']);
                $countryName = $countryObj->country_name ?? '未知国家';
                if($email && $countryName) {
                    $subject = "【官网询盘通知】来自{$countryName}的{$email}提交了询盘";
                }
            }

            \LogUtil::info("start to build template");
            $api = new InnerApi('subscribe_statistic_report');
            $api->setHttpMethod(InnerApi::HTTP_METHOD_POST_JSON);
            $api->setTimeout(5);
            $response = $api->call($mailTemplate, $buildHtmlData);
            \LogUtil::info('sendMail START');

            //$attachList = $buildHtmlData['form_info']['data']['attachment']['value'] ?? [];

            //if($eventType == Params::EVENT_TYPE_DTC_ACCOUNT_REGISTER || $eventType == Params::EVENT_TYPE_DTC_FORM  || $eventType == Params::EVENT_TYPE_AMES_MKT_FORM ){
            if(in_array($eventType,\common\library\ames\AmesConstants::$dtcMailMap)){
                //\SystemNotifyMailUtil::pushMailQueue($subject, $response['data'], $sendEmails,$email,\common\library\util\SystemMailUtil::SYSTEM_MAIL_TYPE_ICBU);
                \SystemNotifyMailUtil::sendIcbuAmesMail($sendEmails, $subject, $response['data'], $email);
            }else{
                \SystemNotifyMailUtil::pushMailQueue($subject, $response['data'], $sendEmails);
            }

            \LogUtil::info('sendMail ----END----');
            \LogUtil::info("send Mail: sendEmail:".json_encode($sendEmails));

        }catch (\Exception $e) {
            \LogUtil::info("get inquiry fail, data=".json_encode($buildHtmlData).",error msg: {$e->getMessage()}");
            return false;
        }
        return true;
    }





    /**
     * @param $clientId
     * @param $inquiryId
     * @return array
     */
    public static function buildHtmlData($clientId, $inquiryId, $leadPrivilege ,$eventType='' ,$eventData='') {
        try {
            $buildHtmlData = [];
            $inquiry = new \common\library\cms\inquiry\Inquiry($clientId, $inquiryId);
            if(!$inquiry->isExist()) {
                return [];
            }
            $inquiry->getFormatter()->baseInfoSetting();
            $buildHtmlData = $inquiry->getAttributes();
        }catch(\Exception $e) {
            \LogUtil::info("build inquiry data error, msg=".$e->getMessage());
        }

        if(empty($buildHtmlData)) {
            return $buildHtmlData;
        }

        // DTC发送邮件，要求数据为空也发送   labels包含所有表单描述，用labels补齐字段
        //if($eventType == Params::EVENT_TYPE_DTC_FORM || $eventType == Params::EVENT_TYPE_AMES_MKT_FORM ){
        if(in_array($eventType,\common\library\ames\AmesConstants::$eventFormMap)){
            $dtcForm = json_decode($buildHtmlData['form_info']['labels']);
            $buildHtmlDataNew = [];
            foreach ($dtcForm as $dtcKey => $dtcItem){
                if(isset($buildHtmlData['form_info']['data'][$dtcKey])){
                    $buildHtmlDataNew[$dtcKey] = $buildHtmlData['form_info']['data'][$dtcKey];
                    continue;
                }
                if($dtcKey == 'attachment')
                     continue;

                $buildHtmlDataNew[$dtcKey] = [
                    'name' => $dtcItem,
                    'value' => '',
                    'field_name' => '',
                    'field_id' => '',
                    'type' => ''
                ];

            }
            $buildHtmlData['form_info']['data'] = $buildHtmlDataNew;
            $buildHtmlData['site_title'] = urldecode(!empty($eventData['page_title']) ? $eventData['page_title'] : (!empty($eventData['page_name_desc']) ? $eventData['page_name_desc'] : ($eventData['page_name'] ?? '')));
        }

        $buildHtmlData['inquiry_url'] = '';
        $crmDomain = self::getCrmDomain();
        $mktDomain = self::getMktDomain();
        if(isset($buildHtmlData['inquiry_id']))
            $buildHtmlData['inquiry_url'] = $mktDomain.'/cms/interact/inquiry/details?inquiry_id='.$buildHtmlData['inquiry_id'];

        if(isset($buildHtmlData['lead_info']['lead_id']) && $buildHtmlData['lead_info']['lead_id'] > 0) {
            $buildHtmlData['lead_info']['lead_url'] = $crmDomain.'/leads/detail?id='.$buildHtmlData['lead_info']['lead_id'];
        }

        $cid = self::getCidByInquiryId($clientId, $inquiryId);

        $buildHtmlData['chat_url'] = '';
        if($cid) {
            $buildHtmlData['chat_url'] = $mktDomain.'cms/liveChat/session?session-id='.$cid;
        }
        // 是否有线索模块
        $buildHtmlData['lead_privilege'] = (int)$leadPrivilege;

        $buildHtmlData['mail_url'] = '';
        if($buildHtmlData['type'] == self::INQUIRY_TYPE_MAIL && isset($buildHtmlData['form_info']['data'])) {
            if(isset($buildHtmlData['form_info']['data']['mail_id'])) {
                $mail_id = $buildHtmlData['form_info']['data']['mail_id'];
                $mail_id = is_array($mail_id) ? ($mail_id['value'] ?? 0) : $mail_id;
                $buildHtmlData['mail_url'] = $crmDomain."/pro/mail/detail?name=mailProUnread&mail_id={$mail_id}";
            }
        }

        return $buildHtmlData;

    }

    public static function getShopsEmailContent($clientId, $siteId, $leadId, $leadPrivilege, $inquiryType=1, $eventData=[])
    {
        $lead = new \common\library\lead\Lead($clientId, $leadId);
        $gaSite = new GaSite($clientId, $siteId);
        if (!$lead->isExist() || !$gaSite->isExist()) {
            return [];
        }
        $lead->getFormatter()->setSpecifyFields([
            'name',
            'company_name',
            'create_time',
            'inquiry_message',
            'main_customer_email',

        ]);
        $leadData = $lead->getAttributes();

        $crmDomain = self::getCrmDomain();

        $data['name'] = [
            'name' => 'Name',
            'value' => $leadData['name'] ?? '',
        ];
        $data['email'] = [
            'name' => 'Email',
            'value' => $leadData['main_customer_email'] ?? '',
        ];
        $data['company'] =  [
            'name' => 'Company',
            'value' => $leadData['company_name'] ?? '',
        ];
        $data['message'] = [
            'name' => 'Message',
            'value' => $leadData['inquiry_message'] ?? '',
        ];

        $formInfo['data'] = $data;
        $formInfo['email'] = $leadData['main_customer_email']??'';

        $leadInfo['lead_id'] = $leadId;
        $leadInfo['lead_url'] =  $crmDomain.'/leads/detail?id='. $leadId;

        $siteInfo['ga_site_id'] = $siteId;
        $siteInfo['web_site_url'] = $gaSite->web_site_url;
        $siteInfo['web_site_name'] = $gaSite->web_site_name;

        $originInfo['origin'] = 0;
        $originInfo['origin_name'] = '其他';

        $htmlData['type'] = $inquiryType;
        $htmlData['type_name'] = \Yii::t('cms', self::INQUIRY_TYPE_MAP[$inquiryType]) ?? 'Unknown';
        $htmlData['form_info'] = $formInfo;
        $htmlData['lead_info'] = $leadInfo;
        $htmlData['site_info'] = $siteInfo;
        $htmlData['origin_info'] = $originInfo;
        $htmlData['create_time'] = $leadData['create_time'];
        $htmlData['lead_privilege'] = (int)$leadPrivilege;

        $inquiryData = \LeadInquiryDataModel::model()->find('client_id=:client_id AND lead_id=:lead_id', [
            ':client_id' => $clientId,
            ':lead_id' => $leadId
        ]);
        $inquiryData = !empty($inquiryData) ? json_decode($inquiryData->inquiry_data, true) : [];

        if (!empty($inquiryData)) {
            $htmlData['user_language'] = '';
            $htmlData['url'] = $inquiryData['page_link'];
            $htmlData['ip'] = $inquiryData['guest_ip'];
            $htmlData['ip_country'] = $inquiryData['country']??'ZZ';
        }

        $htmlData['site_title'] = urldecode(!empty($eventData['page_title']) ? $eventData['page_title'] : (!empty($eventData['page_name_desc']) ? $eventData['page_name_desc'] : ($eventData['page_name'] ?? '')));

        return $htmlData;
    }


    /**
     * @param $clientId
     * @param $siteId
     */
    public static function getNoticeEmails($clientId, $siteId ,$eventType='' ,$eventData='') {

        //if($eventType == Params::EVENT_TYPE_DTC_ACCOUNT_REGISTER || $eventType == Params::EVENT_TYPE_DTC_FORM  || $eventType == Params::EVENT_TYPE_AMES_MKT_FORM ){
        if(in_array($eventType,\common\library\ames\AmesConstants::$dtcMailMap)){
            $opUserId = $eventData['okki_user_id'] ?? 0;
            $sendEmails = [];
            if($opUserId){
                $sendEmails[] = \User::getUserObject($opUserId)->getRealEmail();
                \LogUtil::info("DTC NoticeEmails,clientId={$clientId},siteId={$siteId} email:".json_encode($sendEmails));
            }else{
                \LogUtil::info("获取DTC询盘通知邮箱失败,clientId={$clientId},siteId={$siteId}");
            }
            return $sendEmails;
        }

        try {
            $emails = PageSettingService::getAllEmailNotices($clientId, PageSettingService::SETTING_NOTICE_EMAIL);
            $sendEmails = $staffEmails = $recordEmails = [];
            $staffEmails = array_column($emails['notice_staff_email'],'email');
            $recordEmails = $emails['notice_record_email'] ?? [];
            $sendEmails = array_unique(array_merge($staffEmails, $recordEmails));
        }catch (\Exception $e) {
            \LogUtil::info("获取询盘通知邮箱失败,clientId={$clientId},siteId={$siteId},error=".$e->getMessage());
        }
        return $sendEmails;

    }

    protected static function getCidByInquiryId($clientId, $inquiryId) {
        $sql = "select B.conversation_id FROM tbl_cms_session_inquiry AS A left join tbl_track_site_session AS B on A.site_session_id=B.site_session_id WHERE A.inquiry_id={$inquiryId}";
        return \ProjectActiveRecord::getDbByClientId($clientId)->createCommand($sql)->queryScalar();
    }

    protected static function getCrmDomain() {
        $env = \Yii::app()->params['env'];

        if(in_array($env, ['test'])) {
            $doMain = \Yii::app()->params['host']['crm_url'];
        }else if(in_array($env, ['beta'])) {
            $doMain = 'https://omgk.xiaoman.cn';
        }else {
            $doMain = \Yii::app()->params['host']['crm_url'];
        }
        return $doMain;
    }

    protected static function getMktDomain() {
        $env = \Yii::app()->params['env'];

        if(in_array($env, ['test'])) {
            $doMain = 'http://'.($_SERVER['HTTP_HOST']??\Yii::app()->params['host']['mkt_domain']);
        }else if(in_array($env, ['beta'])) {
            $doMain = 'https://omgm.xiaoman.cn';
        }else {
            $doMain = \Yii::app()->params['host']['mkt_url'];
        }
        return $doMain;
    }

    public static function asyncSend($clientId, $userId, $siteId, $inquiryId) {
        $log = '/tmp/ads_log_' . date("Ymd", strtotime("now")).'.log';
        \LogUtil::info("询盘新增邮件client={$clientId},inquiryId={$inquiryId}");
        list($exec, $output, $return) = \common\library\CommandRunner::run(
            'googletask',
            'SendEmail',
            [
                'clientId' => $clientId,
                'userId' => $userId,
                'siteId' => $siteId,
                'inquiryId' => $inquiryId
            ],
            $log,
            1
        );
    }

    /**
     * 获取询盘message
     * @param $formData
     * @return string
     */
    public static function getInquiryMessageByFormData($formData){
        $message = '';
        foreach ($formData as $key => $item) {
            strtolower($key) == 'message' && $message = $item['value'] ?? '';
            if ((strtolower($item['field_id'] ?? '')) == 'remark' && $item['value'] ?? false) {
                return $item['value'];
            }
        }
        return $message;
    }

    public static function getCountGroupByStatus($clientId)
    {
        $sql = "select inquiry_status, count(1) as count from tbl_inquiry where client_id =:client_id and enable_flag =:enable_flag group by inquiry_status;";
        $res = \PgActiveRecord::getDbByClientId($clientId)->createCommand($sql)->queryAll(true, [':client_id' => $clientId, ':enable_flag' => BaseObject::ENABLE_FLAG_TRUE]);
        $counts = array_column($res, 'count', 'inquiry_status');
        $sum = 0;
        foreach (self::INQUIRY_STATUS_MAP as $key => $val) {
            $sum += $counts[$key] ?? 0;
            $result[$key]['inquiry_status'] = $key;
            $result[$key]['inquiry_status_name'] = \Yii::t('cms', self::INQUIRY_STATUS_MAP[$key] ?? 'Unknown');
            $result[$key]['count'] = $counts[$key] ?? 0;
        }
        $result[] = [
            'inquiry_status' => '',
            'inquiry_status_name' => \Yii::t('cms', 'All'),
            'count' => $sum,
        ];
        return $result ?? [];

    }


    public static function getInquiryTrackEventInfo($clientId, array $inquiryIds)
    {
        if (empty($inquiryIds)) {
            return [];
        }
        $inquiryIdsStr = implode(',', $inquiryIds);
        $mysqlDb = \ProjectActiveRecord::getDbByClientId($clientId);
        $sql = "select inquiry_id, last_event_id from tbl_cms_session_inquiry where inquiry_id in ({$inquiryIdsStr})";
        $eventIdRes = $mysqlDb->createCommand($sql)->queryAll();
        if (empty($eventIdRes)) {
            return [];
        }
        $eventIds = array_column($eventIdRes, 'last_event_id', 'inquiry_id');
        $eventIdsStr = implode(',', $eventIds);

        $sql = "select event_id, title from tbl_track_site_session_event where event_id in ({$eventIdsStr})";
        $titleRes = $mysqlDb->createCommand($sql)->queryAll();
        if (empty($titleRes)) {
            return [];
        }
        $titleRes = array_column($titleRes, 'title', 'event_id');
        foreach ($eventIds as $inquiryId => $eventId) {
            $res[$inquiryId]['title'] = $titleRes[$eventId] ?? '';
        }
        return $res ?? [];

    }

    /**
     * 获取回传状态
     * @param $clientId
     * @param $inquiryIds
     * @return array
     * @throws \ProcessException
     */
    public static function getConversionUploadStatus($clientId, $inquiryIds)
    {
        if (empty($inquiryIds)) {
            return [];
        }

        //过滤非数字的值
        $inquiryIds = array_filter($inquiryIds, function ($value) {
            return is_numeric($value);
        });
        //强制类型转换
        array_walk($inquiryIds,function (&$value){
            $value = intval($value);
        });

        $inquiryIdsStr = implode(',', $inquiryIds);
        $mysqlDb = \ProjectActiveRecord::getDbByClientId($clientId);
        $sql = "select inquiry_id, gclid, conversion_upload_status from tbl_cms_session_inquiry where inquiry_id in ({$inquiryIdsStr})";
        $list = $mysqlDb->createCommand($sql)->queryAll();

        $data = [];
        foreach ($list as $item) {
            $data[$item['inquiry_id']] = empty($item['gclid']) ? self::CONVERSION_UPLOAD_STATUS_FORBIDDEN : $item['conversion_upload_status'];
        }

        return $data;
    }

    public static function getInquiryGclids(int $clientId, array $inquiryIds)
    {
        if (empty($inquiryIds)) {
            return [];
        }
        $sessionInquiryList = new SessionInquiryList($clientId);
        $sessionInquiryList->setInquiryId($inquiryIds);
        $sessionInquiryList->setFields(['inquiry_id', 'gclid']);
        $sessionInquiryList->setConversionUploadStatus(0);
        $list = $sessionInquiryList->find();

        return !empty($list) ? array_column($list, 'gclid', 'inquiry_id') : [];
    }

    public static function setInquiryConversionUploadSuccess(int $clientId, array $inquiryIds)
    {
        //过滤非数字的值
        $inquiryIds = array_filter($inquiryIds, function ($value) {
            return is_numeric($value);
        });
        //强制类型转换
        array_walk($inquiryIds,function (&$value){
            $value = intval($value);
        });

        $inquiryIdsStr = implode(",", $inquiryIds);
        $mysqlDb = \ProjectActiveRecord::getDbByClientId($clientId);
        $table = \CmsSessionInquiry::model()->tableName();
        $sql = "update {$table} set conversion_upload_status=:conversion_upload_status where inquiry_id in (:inquiry_id)";
        return $mysqlDb->createCommand($sql)->execute([':conversion_upload_status' => self::CONVERSION_UPLOAD_STATUS_UPLOADED, ':inquiry_id' => $inquiryIdsStr]);
    }

    public static function country2code($country, $map = 0)
    {
        $country_code_map = self::COUNTRY_CODE_MAP;
        if ($map == 1) {
            return $country_code_map;
        }
        foreach ($country_code_map as $ck => $cv) {
            if (strtolower($cv) == strtolower($country)) {
                return strtolower($ck);
            }
            if (strtolower($ck) == strtolower($country)) {
                return strtolower($ck);
            }
        }
        $ctr2code = ['UNITED STATES'=>"US", "THE NETHERLANDS"=>"NL", "TüRKIYE" => 'TR',  'CURACAO'=>"CW"];
        if (array_key_exists(strtoupper( $country), $ctr2code)) {
            return $ctr2code[strtoupper( $country)];
        }
        return '';
    }

    /**
     * country code to country name
     * @param $countryCode
     * @param $map
     * @return array|mixed
     */
    public static function code2country($countryCode, $map = 0)
    {
        $country_code_map = self::COUNTRY_CODE_MAP;
        if ($map == 1) {
            return $country_code_map;
        }
        return $country_code_map[$countryCode] ?? $countryCode;
    }

    public static function getGuestCountry(string $guestCountry, $country2code = true)
    {
        $flag = $country2code ? self::country2code($guestCountry) : $guestCountry;
        if ($flag == "") {
            $flag = $guestCountry;
        }

        if ($flag == "") {
            $flag = "cn";
        }

        if (strlen($flag) > 5) {
            $flag = "images/flag/unknow.png";
        } else {
            $flag = "images/flag/" . strtolower($flag) . ".png";
        }
        return $flag;
    }

    public static function formatShopsInquiryInfo($data)
    {
        if (!empty($data['shop_form_info'])) {
            $ret = [
                'is_form' => InquiryService::INQUIRY_FORM_TYPE_CUSTOM,
                'list' => array_merge($data['shop_form_info']['form_base_info'], $data['shop_form_info']['form_field_info']),
                'lead_id' => $data['lead_id'],
            ];
            unset($data['shop_form_info']);
            foreach ($data as $key => $value) {
                if (is_array($value)) {
                    $ret = array_merge($ret, $value);
                } else {
                    $ret[$key] = $value;
                }
            }
            isset($ret['lead_id']) && $ret['id'] = $ret['lead_id'];
            $data['inquiry_from_list'] = $ret['list'];
            return $data;
        }

        $ret = ['is_form' => InquiryService::INQUIRY_FORM_TYPE_NODE, 'ori' => $data];
        $formField = [];

        isset($data['inquiry_form_field']) && $ret['is_form'] = InquiryService::INQUIRY_FORM_TYPE_CONVERSION && $formField = $data['inquiry_form_field'];
        $ret['formMap'] = array_reduce($formField, function ($result, $item) {
            if (!isset($item['map_field_id'])) return $result;

            $result[$item['map_field_id']] = ['name' => $item['name'] ?? '-'];

            if (isset($item['ext_info']['items']) && isset($item['field_type']) && $item['field_type'] == 3) {
                $result[$item['map_field_id']]['map'] = $item['ext_info']['items'];
            }

            return $result;
        }, []);

        unset($data['inquiry_form_field']);

        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $ret = array_merge($ret, $value);
            } else {
                $ret[$key] = $value;
            }
        }
        // 老询盘 重新merge inquiry_form_info
        if ($ret['is_form'] == InquiryService::INQUIRY_FORM_TYPE_NODE) {
            $ret = array_merge($ret, $data['inquiry_form_info'] ?? []);
        }
        if (isset($ret['external_field_data'])) {
            foreach ($ret['external_field_data'] as $value) {
                $ret[$value['id']] = $value['value'][0] ?? '-';
            }
        }
        isset($ret['lead_id']) && $ret['id'] = $ret['lead_id'];

        $map = array(
            'page_link' => ['name' => '来源页面',],
            'page_title' => ['name' => '来源页面标题'],
            'guest_ip' => ['name' => '来源IP'],
            'create_time' => ['name' => '访问时间'],
            'customer_name' => ['name' => '公司名称'],
            'guest_country' => ['name' => 'IP国家'],
            'name' => ['name' => '客户名称'],
            'message' => ['name' => '询盘内容'],
            'guest_agent' => ['name' => '访客平台'],
            'tel' => ['name' => '电话'],
            'email' => ['name' => '邮箱'],
        );
        $inquiryFromList = [];

        $ret['is_form'] = intval($ret['is_form']);
        switch($ret['is_form']) {
            case InquiryService::INQUIRY_FORM_TYPE_CUSTOM:
                $inquiryFromList = $ret['list'];
                break;

            case InquiryService::INQUIRY_FORM_TYPE_NODE:
            case InquiryService::INQUIRY_FORM_TYPE_CONVERSION:
                $map = !empty($ret['formMap']) ? $ret['formMap'] : $map;
                foreach ($map as $key => $label) {
                    $tmp = [
                        'key' => $key,
                        'label' => $label['name'],
                    ];
                    $items = $label['map'] ?? [];
                    $value = $ret[$key] ?? '-';

                    if (!empty($items)) {
                        $tmp['value'] = $items[$value] ?? '';
                    } else {
                        if (isset($ret[$key]) && is_array($ret[$key])) {
                            $tmp['value'] = $ret[$key][0]['value'] ?? '-';
                        } else if ($ret['is_form'] == InquiryService::INQUIRY_FORM_TYPE_CONVERSION && (empty($ret[$key])) && !empty($label['name']) && isset($ret[$label['name']])) {
                            // 处理mkt自定义表单数据，自定义表单没有map key因为没有和询盘表单字段映射
                            $tmp['value'] = $ret[$label['name']] ?? '-';
                        } else {
                            $tmp['value'] = $ret[$key] ?? '-';
                        }
                    }
                    if ($key == 'guest_country') {
                        $tmp['value'] = \CountryService::getCnNameByCode(self::country2code($tmp['value'])) . '(' . $tmp['value'] . ')';
                    }

                    if ($key == 'tel' && isset($data['customers'][0]['tel_list'][0])) {
                        $tmp['value'] = implode('', $data['customers'][0]['tel_list'][0] ?? []);
                    }

                    $inquiryFromList[] = $tmp;
                }

                if (!empty($ret['guest_country'] ?? '')) {
                    $ret['guest_country'] = \CountryService::getCnNameByCode(self::country2code($ret['guest_country'])) . '(' . $ret['guest_country'] . ')';
                    $ret['country'] = $ret['guest_country'];
                    $ret['guest_info']['guest_country'] = $ret['guest_country'];
                }

                break;
        }

        $data['inquiry_from_list'] = $inquiryFromList;

        return $data;
    }

    public static function formatShopsInquiry($data)
    {
        if (empty($data)) {
            return null;
        } else {
            $data['guest_country_flag'] = \common\library\cms\inquiry\InquiryService::getGuestCountry($data['guest_country'] ?? '');
            if (isset($data['guest_agent']) && !empty($data['guest_agent'])) {
                $data['guest_device'] = preg_match('/ios|android|mobile/is', $data['guest_agent']) ? '移动端' : 'PC端';
            } else {
                $data['guest_device'] = '';
            }

        }

        return $data;
    }

    /**
     * 保存Shops询盘
     * @throws \ProcessException
     */
    public static function saveLead($leadData, $crmClientId = 0, $cmsInquiryId = 0)
    {
        $data = json_decode($leadData, true);
        if (json_last_error() != JSON_ERROR_NONE) {
            throw new RuntimeException('data 不是正确的json格式');
        }

        $systemInfo = $data['system_info']??[];
        $guestInfo = $data['guest_info']??[];
        $inquiryFormInfo = $data['inquiry_form_info']??[];
        $inquiryProductInfo = $data['inquiry_product_info']??[];
        $inquiryCompanyInfo = $data['inquiry_company_info']??[];
        $leadCustomerInfo = $data['inquiry_lead_customer']??[];
        $leadInfo = $data['inquiry_lead_info']??[];
        $shopFormInfo = $data['shop_form_info'] ?? [];
        $inquiryOriginEntrance = $data['origin_entrance'] ?? \common\library\cms\inquiry\Constant::INQUIRY_ORIGIN_ENTRANCE_SITE;

        if (empty($systemInfo)) {
            throw new RuntimeException('system_info不能为空');
        }

        extract($systemInfo);
        extract($guestInfo);
        extract($inquiryFormInfo);
        extract($inquiryProductInfo);
        extract($inquiryCompanyInfo);

        $siteSessionId = $inquiryFormInfo['site_session_id'] ?? '';
        $trackSessionNo = $inquiryFormInfo['track_session_no'] ?? 0;
        $gclid = $inquiryFormInfo['gclid'] ?? '';

        //数据保存到其他绑定的CRM的client上
        $cmsClientId = 0;
        if (!empty($crmClientId)) {
            \LogUtil::info('origin cms client data save to crm relation client, cms_client_id:'.$client_id.
                ' crm_relation_client_id:'.$crmClientId);
            $cmsClientId = $client_id;
            $client_id = $crmClientId;
        }

        if (!$client_id) {
            throw new RuntimeException("client_id不能为空");
        }

        if (empty($email)) { //兼容会话提交的询盘
            $email = $leadCustomerInfo['email'] ?? '';
        }
        // 不存在会话ID默认0
        if (empty($conversation_id)) {
            $conversation_id = 0;
        }
        //登陆超管
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($client_id)->getAdminUserId();
        if ($adminUserId) {
            \User::setLoginUserById($adminUserId);
        } else {
            throw new RuntimeException("client_id不正确，无法获取超管账号");
        }

        $user = \User::getLoginUser();
        if (!$user) {
            throw new RuntimeException("登陆失败");
        }
        $params = new ShopsInquiryQualityEvalParams(
            $client_id,
            $user->getUserId(),
            $conversation_id,
            'inquiry_risk_control_apply',
            [
                'guest_info' => $guestInfo,
                'system_info' => $systemInfo,
                'inquiry_form_info' => $inquiryFormInfo,
                'blacklist_type' => \common\library\shops\Constants::BLACKLIST_TYPE_INQUIRY,
            ],
        );
        $apply = new ShopsInquiryQualityEvalApply($params);
        $result = $apply->apply();
        if (!empty($result) && $result['check']['result']) {
            \LogUtil::info('lead save fail! the risk control rule rejects the input! data:'. json_encode($data));
            throw new RuntimeException('命中风控，不保存询盘');
        }
        //保存线索
        $lead = new \common\library\lead\Lead($client_id);
        $lead->is_archive = 1;

        $lead->setOperatorUserId($user->getUserId());
        $lead->client_id = $client_id;
        $lead->name = $customer_name??'';
        $lead->company_name =  $customer_name??'';
        $lead->short_name = $company_brief ??'';
        $lead->create_time = empty($create_time)?date('Y-m-d H:i:s'):$create_time;
        $lead->status = $status??Status::SYS_STATUS_NEW;;
        $lead->homepage = $company_site??'';
        $lead->fax = $company_fax??'';
        $lead->tel = $company_landline??'';
        $lead->address = $company_detail_address??'';
        $lead->create_user_id = $user->getUserId();
        //增加垃圾询盘标识
        $lead->trash_flag = Constant::NORMAL_INQUIRY;

        if (!empty($guest_country)) {
            $guest_country = strtoupper($guest_country);
            $countryModel = \CountryService::checkNameInTable($guest_country);
            // 能找到对应的国家映射关系
            if ( !empty($countryModel)) {
                $lead->country =  $countryModel['alpha2'] ?? '';
            }
        }

        //做映射
        if (!empty($source) && $source == 1) {
            $inquiryOrigin = self::ORIGIN_TYPE_CUSTOMER_SERVICE;
        } else if (!empty($source) && $source == 2) {
            $inquiryOrigin = self::ORIGIN_TYPE_CUSTOMER;
        } else if (!empty($source) && $source == 3) {
            $inquiryOrigin = self::ORIGIN_TYPE_CUSTOMER_WEB_CHAT;
        } else if (!empty($source) && $source == 4) {
            $inquiryOrigin = self::ORIGIN_TYPE_CUSTOMER_CONVERSION;
        } else {
            $inquiryOrigin = self::ORIGIN_TYPE_UNKNOWN;
        }

        $lead->inquiry_origin =  $inquiryOrigin;
        $lead->origin_list = [Origin::SYS_ORIGIN_ID_OKKI_SHOP];

        //查询绑定的GaSite
        $gaSite = new \common\library\google_ads\ga\GaSite($client_id);
        $gaSite->loadByCmsSiteId($site_id ?? 0 );
        $gaSiteId = 0;
        if ($gaSite->isExist()) {
            $gaSiteId = $gaSite->site_id;
        }
        //新增字段
        $lead->site_id = $site_id??0;
        $lead->inquiry_message = $message??'';
        // 维护source_detail,例如提交询盘的具体入口
        $sourceDetailTypeKey = ConvertHandler::SOURCE_DETAIL_TYPE_ID_KEYS[ConvertHandler::SOURCE_TYPE_SHOP];
        $relateInfo = [
            $sourceDetailTypeKey => $gaSiteId,
            'origin_entrance' => $inquiryOriginEntrance,
            "guest_ip" => $guest_ip,
            "guest_country"=> $guest_country,
            "page_title"=> trim($page_title),
            "page_link"=> trim($page_link),
            "view" => 0,
        ];
        // 询盘附件保存
        $attachments = $inquiryFormInfo['attachments'] ?? [];
        if (!empty($attachments)) {
            $relateInfo['attachments'] = $attachments;
        }
        $inquirySourceDetail = [
            'type' => \common\library\customer_convert\ConvertHandler::SOURCE_TYPE_SHOP,
            'relate_info' => $relateInfo
        ];
        $lead->addSourceDetail($inquirySourceDetail);

        //跟进人默认管理员，若设置了询盘分配后，线索跟进人为分配人
        $userId = $adminUserId;
        if ($gaSite->isExist() && !empty($gaSite->lead_owner??0)) {
            $userId = $gaSite->lead_owner;
        }
        if (!empty($userId)) {
            $lead->addUser($userId);
        }
        \common\library\lead\Helper::setShopLead($lead, $leadInfo);
        // 有些tel是在$leadInfo(inquiry_lead_info)中传递的
        $tel = $lead->tel ?: (!empty($tel) ? $tel : '');
        //保存联系人
        $customerList = [];
        $customer = null;
        // 邮箱为空就不建主要联系人了
        if (!empty($email) || !empty($tel)) {//shop侧保证邮箱和电话至少有一个不为空
            $customer = new \common\library\lead\LeadCustomer($client_id);
            $customer->email = $email;
            $customer->main_customer_flag = 1;
            $customer->name = $name??'';
            $customer->post = $company_position??'';
            if (!empty($tel)) {
                $customer->tel_list = [[
                    '', $tel ?: ''
                ]];
            }

            if (!empty($gender)) {
                $customer->gender = $gender == 'male' ? 1 : 2;
            }

            \common\library\lead\Helper::setShopLeadCustomer($customer, $leadCustomerInfo);

            $customerList[] = $customer;
            $lead->setCustomerList($customerList);

            $lead->save();

        } else {
            \LogUtil::info('lead save fail! email is empty or tel is empty! data:'. json_encode($data));
            throw new RuntimeException('缺少邮箱、电话信息，不保存询盘');
        }
        $context = [
            'scope' => 'shops-inquiry',
            'client_id' => $lead->client_id, 'site_id' => $lead->site_id,
            'gclid' => $gclid,
            'site_session_id' => $siteSessionId,
            'origin' => $inquiryOriginEntrance ?? null,
        ];
        \common\library\cms\Helper::counterMetrics('inquiry-counter', 1, $context);
        //保存线索拓展表数据
        $inquiryData = [
            "mid" => $mid,
            "source" => $source,
            "guest_ip" => $guest_ip,
            "guest_country"=> $guest_country,
            "guest_agent"=> $guest_agent,
            "page_title"=> trim($page_title),
            "page_link"=> trim($page_link),
            "country" => $country??'',
            "product_type" => $product_type,
            "product_count"=> $product_count,
            "product_desc"=>$product_desc,
            'company_scale' => $company_scale,
        ];

        $pushLeadFlag = !$lead->isNew() && !$gaSite->isNew() && isset($inquiryFormInfo['form_id']);
        if ($pushLeadFlag) {
            $inquiryData['inquiry_form_id'] = $inquiryFormInfo['form_id'];
            $inquiryData['identity_id'] = $inquiryFormInfo['identity_id'];
            $inquiryData['conversation_id'] = $inquiryFormInfo['conversation_id'];
            $inquiryData['inquiry_field_info'] = $inquiryFormInfo['form_data'] ?? [];
        }
        if (!empty($shopFormInfo['form_id'])) {
            $inquiryData['shop_inquiry_form_id'] = $shopFormInfo['form_id'];
            $inquiryData['shop_inquiry_base_info'] = $shopFormInfo['form_base_info'] ?? [];
            $inquiryData['shop_inquiry_field_info'] = $shopFormInfo['form_field_info'] ?? [];
        }

        // 先保存inquiryData，再把询盘数量推送到chat服务
        $inquiryDataStr = json_encode($inquiryData);

        $leadInquiryDataModel = new LeadInquiryDataModel();
        $leadInquiryDataModel->lead_id = $lead->lead_id;
        $leadInquiryDataModel->client_id = $lead->client_id;
        $leadInquiryDataModel->inquiry_type = Origin::SYS_ORIGIN_ID_OKKI_SHOP;
        $leadInquiryDataModel->inquiry_data = $inquiryDataStr;

        // 保存双写的询盘来源
        if (!empty($cmsClientId) && !empty($cmsInquiryId)) {
            $leadInquiryDataModel->cms_client_id = $cmsClientId;
            $leadInquiryDataModel->cms_inquiry_id = $cmsInquiryId;
        }

        $leadInquiryDataModel->save();

        //创建线索表单类型动态
        $dynamicEvent = new SiteTrackEvents();
        $data = [
            'title' => $page_link?:'',
            'url' => $page_link?:'',
            'site_session_id' => $siteSessionId,
            'event_id' => '',
        ];

        if (isset($inquiryFormInfo['form_id'])) {
            list($data['form_data'],$data['extract_data']) = \common\library\lead\Helper::buildSiteTrackDynamicForm(array_column($inquiryFormInfo['form_data']??[], 'value', 'name'));
        } elseif (isset($shopFormInfo['form_id'])) {
            list($data['form_data'],$data['extract_data']) = \common\library\lead\Helper::buildSiteTrackDynamicForm(array_column(array_merge($shopFormInfo['form_base_info'] ?? [], $shopFormInfo['form_field_info'] ?? []), 'value', 'label'));
        } else {
            list($data['form_data'],$data['extract_data']) = \common\library\lead\Helper::buildSiteTrackDynamicForm($inquiryFormInfo);
        }

        $dynamicEvent->setSource(Origin::SYS_ORIGIN_ID_OKKI_SHOP);
        $dynamicEvent->setUserId($adminUserId);
        $dynamicEvent->setLeadId($lead->lead_id);
        $dynamicEvent->setType(TrailConstants::TYPE_SITE_TRACK_FORM);
        $dynamicEvent->setCreateUser($adminUserId);
        if ($customer) {
            $dynamicEvent->setLeadCustomerId($customer->customer_id);
        }
        $dynamicEvent->setTrackSessionNo($trackSessionNo);
        $dynamicEvent->setClientId($client_id);
        $dynamicEvent->setCreateTime(date("Y-m-d H:i:s"));
        $dynamicEvent->setData($data);
        $dynamicEvent->run();

        // 保存site track session lead
        $stsData = [
            'lead_id' => $lead->lead_id,
            'site_session_id' => $siteSessionId,
            'client_id' => $lead->client_id,
            'site_id' => $lead->site_id,
            'track_session_no' => $trackSessionNo,
            'gclid' => $gclid,
        ];
        if ($customer) {
            $stsData['lead_customer_id'] = $customer->customer_id;
        }
        Helper::saveSiteSessionLead($stsData);

        // 询盘双写非Shops来的询盘不通知
        if ($pushLeadFlag && empty($cmsClientId)) {
            $conversationId = $inquiryFormInfo['conversation_id'];
            $formMessageId = $inquiryFormInfo['form_message_id'];
            \common\library\cms\conversation\CmsConversationService::pushInquiryFormMessage($client_id, $gaSite->site_id, $conversationId, $formMessageId, [
                'form_id' => $inquiryFormInfo['form_id'],
                'form_name' => $inquiryFormInfo['form_name'],
                'lead_id' => $lead->lead_id,
                'fields' => $inquiryFormInfo['form_data']
            ], \common\library\cms\CmsConstants::SITE_TYPE_SHOP, $site_id);
        }
        // 是否需要通知shop询盘评估结果
        $evalNotice = true;
        // 同步询盘到其他绑定client
        if (empty($cmsClientId) && empty($cmsInquiryId)) {
            $crmClientId = \CmsCrmClientRelation::model()->getClientIdByCmsClientId($client_id);
            if (!empty($crmClientId)) {
                // 多绑client无需通知shop
                $evalNotice = false;
                \LogUtil::info('submit inquiry data to crm client, clientId:'. $crmClientId);
                $job = new SyncCmsInquiryToInconsistentClientJob($crmClientId, $lead->lead_id, $site_id, $leadData);
                \common\library\queue_v2\QueueService::dispatch($job);
            }
        }
        // 询盘异步评估
        $evalJob = new ShopInquiryQualityEvalJob($lead->client_id, $lead->lead_id, $leadData, $evalNotice);
        \common\library\queue_v2\QueueService::later($evalJob, $evalJob->getDelay());

        return $lead->lead_id;
    }


    public static function updateInconsistentClientInquiryData($clientId, array $leadIds, int $status)
    {
        if (empty($leadIds) || empty($status)) {
            return;
        }

        try {
            // 绑定的Shops账号clientId
            $cmsClientId = \CmsCrmClientRelation::model()->getCmsClientIdByClientId($clientId);

            if (!empty($cmsClientId)) {

                $db = \PgActiveRecord::getDbByClientId($clientId);
                $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();

                $list = new \common\library\lead_v3\LeadList($adminUserId);
                $list->setIsArchive([Lead::LEAD_TYPE_ARCHIVE, Lead::LEAD_TYPE_NEW]);
                $list->paramsMapping([
                    'origin_list' => [Origin::SYS_ORIGIN_ID_OKKI_SHOP]
                ]);
                $list->setSkipFilterStatus(true);
                $list->setSkipPrivilege(true);
                $list->setAlias('A');

                $table = LeadInquiryDataModel::model()->tableName();
                $alias = 'B';
                $join = " LEFT JOIN $table as $alias ON A.client_id=$alias.client_id AND A.lead_id=$alias.lead_id ";

                list($where, $sqlParams) = $list->buildParams();
                $sqlParams[':cms_client_id'] = $cmsClientId;

                $crmLeadIds = implode(',', SqlBuilder::buildInWhere($leadIds, $sqlParams));

                $where .= " AND B.cms_client_id =:cms_client_id AND A.lead_id in ({$crmLeadIds}) ";

                $sql = " SELECT Distinct B.cms_inquiry_id FROM tbl_lead A $join WHERE {$where} ";

                //查询cms主询盘ID
                $leadList = $db->createCommand($sql)->queryAll(true, $sqlParams);

                if (!empty($leadList)) {
                    $cmsInquiryIds = array_column($leadList, 'cms_inquiry_id');
                    $job = new SyncCmsInquiryStatusToInconsistentClientJob($cmsClientId, $cmsInquiryIds, $status);
                    \common\library\queue_v2\QueueService::dispatch($job);
                    \LogUtil::info('update inquiry status for cms client, client_id:'. $cmsClientId. ' status:'. $status. ' lead_count:'. count($cmsInquiryIds));
                }
            }

            // 绑定的CRM账号clientId
            $crmClientId = \CmsCrmClientRelation::model()->getClientIdByCmsClientId($clientId);

            if (!empty($crmClientId)) {

                $db = \PgActiveRecord::getDbByClientId($crmClientId);
                $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($crmClientId)->getAdminUserId();

                $list = new \common\library\lead_v3\LeadList($adminUserId);
                $list->setIsArchive([Lead::LEAD_TYPE_ARCHIVE, Lead::LEAD_TYPE_NEW]);
                $list->paramsMapping([
                    'origin_list' => [Origin::SYS_ORIGIN_ID_OKKI_SHOP]
                ]);                $list->setSkipFilterStatus(true);
                $list->setSkipPrivilege(true);
                $list->setAlias('A');

                $table = LeadInquiryDataModel::model()->tableName();
                $alias = 'B';
                $join = " LEFT JOIN $table as $alias ON A.client_id=$alias.client_id AND A.lead_id=$alias.lead_id ";

                list($where, $sqlParams) = $list->buildParams();
                $sqlParams[':cms_client_id'] = $clientId;

                $cmsInquiryIds = implode(',', SqlBuilder::buildInWhere($leadIds, $sqlParams));

                $where .= " AND B.cms_client_id =:cms_client_id AND B.cms_inquiry_id in ({$cmsInquiryIds})";

                $sql = " SELECT A.lead_id FROM tbl_lead A $join WHERE {$where} ";

                //查询crm子询盘ID
                $leadList = $db->createCommand($sql)->queryAll(true, $sqlParams);

                if (!empty($leadList)) {
                    $crmLeadIds = array_column($leadList, 'lead_id');
                    $job = new SyncCmsInquiryStatusToInconsistentClientJob($crmClientId, $crmLeadIds, $status);
                    \common\library\queue_v2\QueueService::dispatch($job);
                    \LogUtil::info('update inquiry status for crm client, client_id:'. $crmClientId. ' status:'. $status. ' lead_count:'. count($crmLeadIds));
                }
            }
        } catch (\Exception $e) {
            \LogUtil::log('info', 'update inconsistent client inquiry data error, msg=' . $e->getMessage(),[
                'clientId' => $clientId,
                'status' => $status,
                'leadIds' => json_encode($leadIds),
            ]);
        }
    }

    /**
     * lead 回收原因text
     * @param int $reason
     * @return string
     */
    public static function getTrashReasonText(int $reason): string
    {
        return Constant::TRASH_TYPE_REASON_TEXT[$reason] ?? '';
    }

    /**
     * lead 回收原因prompt
     * @param int $reason
     * @return string
     */
    public static function getTrashReasonPrompt(int $reason): string
    {
        return Constant::TRASH_TYPE_REASON_PROMPT[$reason] ?? '';
    }

    /**
     * lead 回收原因remark
     * @param int $reason
     * @return string
     */
    public static function getTrashReasonRemark(int $reason): string
    {
        return Constant::TRASH_TYPE_REASON_REMARK[$reason] ?? '';
    }

    /**
     * 获取询盘评分质量等级
     * @param int|float $score
     * @return string
     */
    public static function getInquiryGrade(int|float $score): string
    {
        foreach (Constant::LEAD_INQUIRY_SCORE_GRADE_RANGE as $grade => $range) {
            if (($score >= $range['lower'] || $range['lower'] == Constant::LEAD_INQUIRY_SCORE_RANGE_NO_LIMIT) &&
                ($score <= $range['upper'] || $range['upper'] == Constant::LEAD_INQUIRY_SCORE_RANGE_NO_LIMIT)) {
                return $grade;
            }
        }
        return Constant::LEAD_INQUIRY_SCORE_GRADE_NORMAL;
    }

    /**
     * 获取询盘评分质量等级text
     * @param int $grade
     * @return string
     */
    public static function getInquiryGradeText(int $grade): string
    {
        return Constant::LEAD_INQUIRY_SCORE_GRADE_TEXT[$grade];
    }


    public static function getInquiryTrashTypeBySource(array $sourceDetail): string
    {
        $originDetailObj = self::getOriginDetailObj($sourceDetail);
        if (!empty($originDetailObj)) {
            $originEntrance = $originDetailObj['relate_info']['trash_type'] ?? \common\library\cms\inquiry\Constant::INQUIRY_ORIGIN_ENTRANCE_CATEGORY_UNKNOWN;
            return self::getOriginEntranceText($originEntrance);
        }
        return '';
    }

    /**
     * 从source_detail中找到询盘来源
     * @param array $sourceDetail
     * @return string
     */
    public static function getOriginEntranceTextBySource(array $sourceDetail): string
    {
        $originDetailObj = self::getOriginDetailObj($sourceDetail);
        if (!empty($originDetailObj)) {
            $originEntrance = $originDetailObj['relate_info']['origin_entrance'] ?? \common\library\cms\inquiry\Constant::INQUIRY_ORIGIN_ENTRANCE_CATEGORY_UNKNOWN;
            return self::getOriginEntranceText($originEntrance);
        }
        return '';
    }

    /**
     * 转换为询盘来源
     * @param int $originEntrance
     * @return string
     */
    public static function getOriginEntranceText(int $originEntrance): string
    {
        $category = \common\library\cms\inquiry\Constant::INQUIRY_ORIGIN_ENTRANCE_MAP[$originEntrance] ?? \common\library\cms\inquiry\Constant::INQUIRY_ORIGIN_ENTRANCE_CATEGORY_UNKNOWN;
        return \common\library\cms\inquiry\Constant::INQUIRY_ORIGIN_ENTRANCE_CATEGORY_TEXT[$category];
    }

    /**
     * 从source_detail中找到符合type的source obj
     * @param array $sourceDetail
     * @param int $type
     * @return array
     */
    public static function getOriginDetailObj(array $sourceDetail, int $type=\common\library\customer_convert\ConvertHandler::SOURCE_TYPE_SHOP): array
    {
        $shopsDetailObjs = array_filter($sourceDetail, function ($item) use ($type) {
            return isset($item['type']) && $item['type'] == $type;
        });
        return $shopsDetailObjs ? array_shift($shopsDetailObjs) : [];
    }

    /**
     * 获取shop服务的询盘数据
     * @param int|string $clientId
     * @param array $leadIds
     * @return array
     */
    public static function getOriginEntranceFromShop(int|string $clientId, array $leadIds = [])
    {
        $client = new InnerApi('cms_api');
        $client->setHttpMethod(InnerApi::HTTP_METHOD_POST);
        $params = [
            'client_id' => $clientId,
            'mids' => $leadIds,
        ];
        // 默认为空，非关键数据，请求接口失败不影响业务
        $entranceData = $resp = [];
        try {
            \LogUtil::info('getShopInquiryBatch params ' . json_encode($params));
            $resp = $client->call("getShopInquiryBatch", $params);
            \LogUtil::info('request getShopInquiryBatch to shop-api success,params: resp: ' . json_encode($resp, true));
        } catch (\Exception $exception) {
            \LogUtil::warning('request getShopInquiryBatch failure: '.$exception->getMessage());
            \LogUtil::info($exception->getTraceAsString());
        }
        if (!empty($resp) && !empty($resp['data'])) {
            $entranceData = array_column($resp['data'], 'origin', 'mid');
        }
        return $entranceData;
    }

    /**
     * 询盘质量评估
     * @param $leadData
     * @param $clientId
     * @param $cmsInquiryId
     * @return int
     * @throws \Exception
     */
    public function qualityEval($leadData, $clientId, $cmsInquiryId, $notice)
    {
        //登陆超管
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        if ($adminUserId) {
            \User::setLoginUserById($adminUserId);
        } else {
            throw new RuntimeException("client_id不正确，无法获取超管账号");
        }
        $lead = new \common\library\lead\Lead($clientId, $cmsInquiryId);
        if (!$lead->lead_id) {
            throw new RuntimeException(\Yii::t('lead', $cmsInquiryId.' The inquiry does not exist'));
        }
        $systemInfo = $leadData['system_info'] ?? [];
        $guestInfo = $leadData['guest_info'] ?? [];
        $inquiryFormInfo = $leadData['inquiry_form_info'] ?? [];
        $conversationId = $inquiryFormInfo['conversation_id'] ?? 0;
        // eval apply
        $shopSite = AimsLanguage::model()->find('client_id=:client_id AND site_id=:site_id',
            [':client_id' => $lead->client_id, ':site_id' => $lead->site_id]);

        $evalSetting = InquiryService::getInquiryEvalSetting($shopSite ? $shopSite->inquiry_setting : '');
        $params = new ShopsInquiryQualityEvalParams(
            $clientId,
            $lead->create_user_id,
            $conversationId,
            'inquiry_apply',
            [
                'message' => $lead->inquiry_message,
                'guest_info' => $guestInfo,
                'system_info' => $systemInfo,
                'inquiry_form_info' => $inquiryFormInfo,
                'blacklist_type' => \common\library\shops\Constants::BLACKLIST_TYPE_INQUIRY,
            ],
            $lead->lead_id,
            $evalSetting,
        );
        $apply = new ShopsInquiryQualityEvalApply($params);
        $result = $apply->apply();

        if (!empty($result)) {
            if ($result['check']['result']) {
                //增加垃圾询盘标识
                $lead->trash_flag = Constant::TRASH_INQUIRY;
                $lead->trash_time = date('Y-m-d H:i:s');
                $lead->trash_reason = $result['check']['reason'];
                $lead->trash_remark = $result['check']['remark'];
            }
            $inquiryScore = $result['score'] ?? 0;
            $lead->addScore(['inquiry_score' => $inquiryScore]);

            $lead->save();
        }
        $trashData = [
            'is_trash' => $lead->trash_flag,
        ];
        // shops询盘counter metrics
        $siteSessionId = $inquiryFormInfo['site_session_id'] ?? '';
        $gclid = $inquiryFormInfo['gclid'] ?? '';
        $context = [
            'scope' => 'shops-inquiry',
            'client_id' => $lead->client_id, 'site_id' => $lead->site_id,
            'service_provider' => 0,
            'is_marketing' => 0,
            'gclid' => $gclid,
            'site_session_id' => $siteSessionId,
            'origin' => $leadData['origin_entrance'] ?? \common\library\cms\inquiry\Constant::INQUIRY_ORIGIN_ENTRANCE_SITE,
            'is_trash' => $lead->trash_flag,
        ];
        if (!empty($shopSite)) {
            $context['service_provider'] = $shopSite->service_provider;
            $matomoService = new MatomoVisitService($lead->client_id);
            $visitorGclids = [];
            if (!empty($siteSessionId)) {
                $visitorGclids = $matomoService->getMatomoVisitorGclid($siteSessionId, $shopSite->matomo_site_id);
            }
            $context['is_marketing'] = intval(!empty($visitorGclids) || !empty($gclid));
        }
        $metricsKey = $notice ? 'inquiry-eval-counter' : 'mult-bind-inquiry-eval-counter';
        \common\library\cms\Helper::counterMetrics($metricsKey, 1, $context);
        if ($notice) {
            // 通知shop trash状态
            $this->updateInquiryDataShop($clientId, $cmsInquiryId, $trashData);
        }
        return $lead->lead_id;
    }

    /**
     * 更新询盘信息
     * @param int|string $clientId
     * @param int|string $leadId
     * @param array $data
     * @return bool
     */
    public static function updateInquiryDataShop(int|string $clientId,int|string $leadId,array $data)
    {
        $client = new InnerApi('cms_api');
        $client->setHttpMethod(InnerApi::HTTP_METHOD_POST);
        $params = [
            'client_id' => $clientId,
            'mid' => $leadId,
            'data' => $data,
        ];
        try {
            \LogUtil::info('updateShopInquiry params ' . json_encode($params));
            $resp = $client->call("updateShopInquiry", $params);
            \LogUtil::info('request updateShopInquiry to shop-api success,params: resp: ' . json_encode($resp, true));
        } catch (\Exception $exception) {
            \LogUtil::error('request updateShopInquiry failure: '.$exception->getMessage());
            ErrorReport::phpError(new \CExceptionEvent(null,$exception), $exception->getTrace());
        }
        if (!empty($resp) && !empty($resp['data'])) {
            return true;
        }
        return false;
    }

    /**
     * format下载数据
     * @param $leadList
     * @return array|array[]
     */
    public static function downloadDataFormat($leadList)
    {
        $titleMap = [
            'lead_id' => '访客编号',
            'name' => '访客名称',
            'main_customer_email' => '邮箱',
            'inquiry_message' => '询盘信息',
            'inquiry_data' => [
                'guest_ip' => '访客IP',
                'guest_country' => '访客国家',
                'guest_device' => '访客设备',
                'page_link' => '来源网站',
            ],
            'origin_entrance' => '询盘来源',
            'inquiry_grade' => '询盘质量',
            'status' => '状态',
            'create_time' => '提交询盘时间',
        ];
        $dataList = [];
        foreach ($leadList as $lead) {
            $data = self::filterArrayByKeys($titleMap, $lead);
            if (!empty($data)) {
                $dataList[] = $data;
            }
        }
        $title = self::flattenArrayValues($titleMap);
        return array_merge([$title], $dataList);
    }

    /**
     * 过滤出b数组中和a数组key相交的数据，结果并转换为一维数组
     * @param $a
     * @param $b
     * @return array
     */
    protected static function filterArrayByKeys($a, $b) {
        $result = [];
        foreach ($a as $key => $value) {
            if (array_key_exists($key, $b)) {
                if (is_array($value) && is_array($b[$key])) {
                    // 如果是多维数组，递归调用
                    $result = array_merge($result, self::filterArrayByKeys($value, $b[$key]));
                } else {
                    // 否则，直接保存
                    $result[] = $b[$key];
                }
            } else {
                $result[] = '-';
            }
        }
        return $result;
    }

    /**
     * 更新询盘查看状态
     * @param Lead $lead
     * @return void
     */
    public static function updateInquiryView(\common\library\lead\Lead $lead)
    {
        if (!empty($lead->source_detail)) {
            $originDetailObj = InquiryService::getOriginDetailObj($lead->source_detail);
            if (isset($originDetailObj['relate_info']['view']) && $originDetailObj['relate_info']['view'] == 0) {
                $lead = new \common\library\lead\Lead($lead->client_id, $lead->lead_id);
                $lead->source_detail = self::modifySourceDetailObj($lead->source_detail,
                    \common\library\customer_convert\ConvertHandler::SOURCE_TYPE_SHOP, 'view', 1);
                $lead->save();
            }
        }
    }

    /**
     * 修改source_detail中的值
     * @param array $sourceDetailArr
     * @param int $type
     * @param string $fieldName
     * @param mixed $fieldValue
     * @return array
     */
    public static function modifySourceDetailObj(array $sourceDetailArr, int $type, string $fieldName, mixed $fieldValue)
    {
        if (!is_array($sourceDetailArr)) {
            return $sourceDetailArr;
        }
        foreach ($sourceDetailArr as &$sourceDetail) {
            if (isset($sourceDetail['type']) && $sourceDetail['type'] == $type && isset($sourceDetail['relate_info'][$fieldName])) {
                $sourceDetail['relate_info'][$fieldName] = $fieldValue;
            }
        }
        return $sourceDetailArr;
    }

    /**
     * 多维数组转一维
     * @param $array
     * @return array
     */
    protected static function flattenArrayValues($array) {
        $result = [];
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $result = array_merge($result, self::flattenArrayValues($value));
            } else {
                $result[] = $value;
            }
        }
        return $result;
    }

    /**
     * 邮箱查询询盘信息
     * @param $client_id
     * @param $site_id
     * @param array $emails
     * @return array
     * @throws \xiaoman\orm\exception\QueryException
     */
    public static function getLeadByEmail($client_id, $site_id, array $emails)
    {
        $filter = new \common\library\lead_v2\LeadFilter($client_id);
        $filter->source_detail = function (){
            $sourceTypeCondition = [[
                'type' => \common\library\customer_convert\ConvertHandler::SOURCE_TYPE_SHOP,
            ]];
            $sourceTypeConditionStr = json_encode($sourceTypeCondition);
            return " AND source_detail @> '{$sourceTypeConditionStr}' ";
        };
        $filter->client_id = $client_id;
        $filter->wheres(['site_id' => $site_id]);
        $filter->main_customer_email = new \xiaoman\orm\database\data\In($emails);
        $filter->select([
            'main_customer_email' => function ($column) {
                return "DISTINCT ON ({$column}) {$column} AS email";
            },
            'lead_id' => function ($column) {
                return $column;
            }
        ]);
        $filter->order('main_customer_email');
        $filter->order('create_time','DESC');
        $list = $filter->find();
        $list->getFormatter()->displayFields(['email', 'lead_id']);
        return $list->getListAttributes();
    }

    /**
     * 获取询盘配置
     * @param $inquirySetting
     * @return array|int[]
     */
    public static function getInquiryEvalSetting($inquirySetting)
    {
        $inquiryDefaultSettings = Constants::INQUIRY_EVAL_SETTING_DEFAULT;
        $inquirySettings = json_decode($inquirySetting, true) ?: [];
        // 配置项存在则应用，不存在时取默认配置，过滤掉非eval配置
        return array_intersect_key(array_merge($inquiryDefaultSettings, $inquirySettings), $inquiryDefaultSettings);
    }

    /**
     * 查询询盘对应的访客信息
     * @param $clientId
     * @param $leadId
     * @return string[]
     */
    public static function getInquiryVisitorInfo($clientId, $leadId)
    {
        $visitorInfo = [
            'guest_first_url' => '',
            'guest_first_url_referer' => '',
        ];
        $siteSessionLead = (new SiteSessionLead($leadId))->loadByClientLeadId($clientId, $leadId);
        if (!$siteSessionLead->lead_id || !$siteSessionLead->site_session_id) {
            return $visitorInfo;
        }
        $matomoService = new MatomoVisitService($siteSessionLead->client_id);
        $idSite = $matomoService->getAimsLanguageMatomoSiteId($siteSessionLead->client_id, $siteSessionLead->site_id);
        $visitFirst = $matomoService->getMatomoLogVisitFirst($siteSessionLead->site_session_id, $idSite);
        if (!$visitFirst) {
            return $visitorInfo;
        }
        $idActions = [
            $visitFirst->visit_entry_idaction_url ?: 0,
        ];
        if ($visitFirst->visit_entry_idaction_url) {
            $matomoLogActionMap = $matomoService->getMatomoLogAction($idActions);
            /** @var \common\models\matomo\MatomoLogAction $matomoLogAction */
            $matomoLogAction = $matomoLogActionMap[$visitFirst->visit_entry_idaction_url] ?? null;
            $visitorInfo['guest_first_url'] = $matomoLogAction ? self::parseDomainAndPath($matomoLogAction->name) : '';
        }
        $visitorInfo['guest_first_url_referer'] = $visitFirst->referer_url;
        return $visitorInfo;
    }

    /**
     * 提取url域名和路径
     * @param string $url
     * @return string
     */
    public static function parseDomainAndPath(string $url): string {
        // 解析URL
        $parsedUrl = parse_url($url);
        // 提取域名和路径，使用空字符串作为默认值
        $domain = $parsedUrl['host'] ?? '';
        $path = $parsedUrl['path'] ?? '';
        // 返回组合后的结果
        return $domain . $path;
    }

    /**
     * 查询询盘访客行为
     * @param $clientId
     * @param $userId
     * @param $leadId
     * @param $curPage
     * @param $pageSize
     * @return array
     * @throws \CException
     */
    public static function getInquiryLogVisitList($clientId, $userId, $leadId, $curPage, $pageSize)
    {
        $logVisitData = [
            'count' => 0,
            'list' => [],
        ];
        // 访客日志list
        $logVisitList = [];
        $siteSessionLead = (new SiteSessionLead())->loadByClientLeadId($clientId, $leadId);
        if ($siteSessionLead->lead_id && $siteSessionLead->site_session_id) {
            // 查询matomo访客行为
            $matomoService = new MatomoVisitService($siteSessionLead->client_id);
            $idSite = $matomoService->getAimsLanguageMatomoSiteId($siteSessionLead->client_id, $siteSessionLead->site_id);
            $logVisitCount = $matomoService->getMatomoLogVisitCount(hex2bin($siteSessionLead->site_session_id), $idSite);
            $logVisitData['count'] = $logVisitCount;
            if ($logVisitCount > 0) {
                $logVisitList = $matomoService->getMatomoVisitLogListByIdVisitor($siteSessionLead->site_session_id, $idSite, $curPage, $pageSize);
            }
        }
        // 查询Trail访客行为
        $trailPageSize = 10; // 表单提交动作先设定为10个限制，后面移到matomo
        $list = new \common\library\trail\LeadDynamicList($clientId, $leadId);
        $list->setOperatorUserId($userId);
        $list->setType([\common\library\trail\TrailConstants::TYPE_SITE_TRACK_FORM]);
        $list->setOffset(($curPage - 1) * $trailPageSize);
        $list->setLimit($trailPageSize);
        $trailList = $list->find();
        $trailVisitActionList = self::formatTrailToVisitActionList($trailList);
        usort($trailVisitActionList, function ($a, $b) {
            return $a['server_time'] <=> $b['server_time'];
        });
        // 合并matomo访客行为和trail
        $logVisitData['list'] = self::mergeLogVisitList($logVisitList, $trailVisitActionList);
        if (empty($logVisitList)) {
            // 如果只有trail合并算一个visit会话
            $logVisitData['count'] = count($logVisitData['list']);
        }
        return $logVisitData;
    }

    /**
     * 合并访客行为
     * @param array $logVisitList
     * @param array $trailVisitActionList
     * @return array
     */
    public static function mergeLogVisitList(array $logVisitList, array $trailVisitActionList)
    {
        if (!empty($logVisitList) && !empty($trailVisitActionList)) {
            // 合并matomo访客行为和trail
            foreach ($trailVisitActionList as $trailVisitAction) {
                $inserted = false;
                $trailActionTime = $trailVisitAction['server_time'];
                $closestVisit = null;
                $closestTimeDiff = PHP_INT_MAX;
                foreach ($logVisitList as &$logVisit) {
                    $firstActionTime = $logVisit['visit_first_action_time'];
                    $lastActionTime = $logVisit['visit_last_action_time'];
                    // 检查新动作的时间是否在当前访问的时间范围内
                    if ($trailActionTime >= $firstActionTime && $trailActionTime <= $lastActionTime) {
                        $logVisit['action_list'][] = $trailVisitAction;
                        $inserted = true;
                        break;
                    }
                    // 计算与 visit_last_action_time 的时间差
                    $timeDiff = abs(strtotime($lastActionTime) - strtotime($trailActionTime));
                    if ($timeDiff < $closestTimeDiff) {
                        $closestTimeDiff = $timeDiff;
                        $closestVisit = &$logVisit;
                    }
                }
                // 如果没有插入，插入到最近的访问记录中
                if (!$inserted && $closestVisit !== null) {
                    $closestVisit['action_list'][] = $trailVisitAction;
                }
            }

            // 对所有 action_list 进行排序
            unset($logVisit);
            foreach ($logVisitList as &$logVisit) {
                // 计算visit时长
                $logVisit['spend_time'] = ($logVisit['visit_first_action_time'] && $logVisit['visit_last_action_time']) ? strtotime($logVisit['visit_last_action_time']) - strtotime($logVisit['visit_first_action_time']) : 0;
                usort($logVisit['action_list'], function ($a, $b) {
                    return $a['server_time'] <=> $b['server_time'];
                });
            }

        } else if (empty($logVisitList) && !empty($trailVisitActionList)) {
            $firstAction = reset($trailVisitActionList);
            $endAction = end($trailVisitActionList);
            // 处理matomo没有访客记录的情况
            $logVisitTemp['idvisit'] = 0;
            $logVisitTemp['visit_first_action_time'] = $firstAction['server_time'];
            $logVisitTemp['visit_last_action_time'] = $endAction['server_time'];
            // 计算visit时长
            $logVisitTemp['spend_time'] = ($logVisitTemp['visit_first_action_time'] && $logVisitTemp['visit_last_action_time']) ? strtotime($logVisitTemp['visit_last_action_time']) - strtotime($logVisitTemp['visit_first_action_time']) : 0;
            $logVisitTemp['action_list'] = $trailVisitActionList;
            $logVisitList[] = $logVisitTemp;
            return $logVisitList;
        }
        return $logVisitList;
    }

    /**
     * trail转换成matomo访客事件格式
     * @param array $trailList
     * @return array
     */
    public static function formatTrailToVisitActionList(array $trailList)
    {
        if (empty($trailList)) {
            return [];
        }
        $trailVisitActionList = [];
        foreach ($trailList as $trail) {
            $data = $trail['data'] ?? [];
            if(empty($data)){
                continue;
            }
            $actionUrlTmp['idaction_url_name'] = $data['url'] ?? '';
            $actionUrlTmp['server_time'] = $trail['create_time'] ?? '';
            $actionUrlTmp['idaction_url_type'] = $trail['type'] ?? TrailConstants::TYPE_SITE_TRACK_FORM;
            $actionUrlTmp['idaction_url_type_text'] = $trail['node_type_name'] ?? '';
            $actionUrlTmp['extract_data'] = $data['extract_data'] ?? null;
            $trailVisitActionList[] = $actionUrlTmp;
        }
        return $trailVisitActionList;

    }

}
