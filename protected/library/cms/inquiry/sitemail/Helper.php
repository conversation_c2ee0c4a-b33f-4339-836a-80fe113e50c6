<?php
/**
 * Created by PhpStorm.
 * User: amu
 * Date: 2021-01-11
 * Time: 17:54
 */

namespace common\library\cms\inquiry\sitemail;

use common\components\BaseObject;
use common\library\account\Client;

class Helper
{
    /**
     * 获取被绑定了自建站或第三方的邮箱id数组
     * @param $clientId
     * @param int $siteId
     * @return array
     */
    public static function getSiteBoundMails($clientId, $siteId = 0)
    {
        //绑定了自建站的邮箱
        $userMails = $siteId ? [] : array_column(\common\library\account\Helper::getClientSettingValue($clientId, Client::SETTING_KEY_MARKETING_IGNORE_AI_USER_MAIL_LIST) ?: [], 'user_mail_id');

        //绑定了第三方的邮箱
        $listObj = new SiteMailList($clientId);
        $listObj->setFields(['site_id', 'user_mail_ids']);
        $listObj->setEnableFlag(BaseObject::ENABLE_FLAG_TRUE);
        $siteId && $listObj->setSiteId($siteId);
        $list = $listObj->find() ?? [];
        array_map(function ($v) use (&$userMails) {
            $userMailIds = json_decode($v['user_mail_ids'], true) ?? [];
            !empty($userMailIds) && $userMails = array_merge($userMails, $userMailIds);
        }, $list);

        return array_unique($userMails);
    }


    /**
     * 判断邮箱是否已经被绑定在第三方或自建网站下接收询盘，返回被绑定了的邮箱id或false
     * @param $userMailId
     * @param $clientId
     * @param int $siteId
     * @return array|bool
     */
    public static function isBindingSite($userMailId, $clientId, $siteId = 0)
    {
        $mailIds = self::getSiteBoundMails($clientId, $siteId);
        $bindingMails = [];
        if (is_array($userMailId) || $userMailId = [$userMailId]) {
            foreach ($userMailId as $item) {
                in_array($item, $mailIds) && $bindingMails[] = $item;
            }
        }
        return empty($bindingMails) ? false : $bindingMails;
    }


    /**
     * 获取绑定了指定邮箱的第三方网站site_id
     * @param $userMailId
     * @param $clientId
     * @return int
     */
    public static function getSiteIdByUserMailBound($userMailId, $clientId)
    {
        $listObj = new SiteMailList($clientId);
        $listObj->setEnableFlag(BaseObject::ENABLE_FLAG_TRUE);
        $listObj->setFields(['site_id', 'user_mail_ids']);
        $list = $listObj->find();
        foreach ($list as $item) {
            $userMailIds = json_decode($item['user_mail_ids']);
            if (in_array($userMailId, $userMailIds)) {
                return $item['site_id'] ?? 0;
            }
        }
        return 0;

    }

    public static function getUserMailBindTime($clientId, $userMailId, $siteId) {
        $listObj = new SiteMailList($clientId);
        $listObj->setEnableFlag(BaseObject::ENABLE_FLAG_TRUE);
        $listObj->setFields(['site_id', 'user_mail_ids', 'update_time']);
        $list = $listObj->find();
        foreach ($list as $item) {
            $userMailIds = json_decode($item['user_mail_ids'], true);
            if (in_array($userMailId, $userMailIds) && $item['site_id'] == $siteId) {
                return $item['update_time'];
            }
        }
        return false;
    }
}