<?php


namespace common\library\cms\conversation;


use common\library\cms\CmsConstants;
use common\library\cms\domain\DomainList;
use common\library\cms\form\FormList;
use common\library\google_ads\ga\GaSiteList;
use common\library\util\PgsqlUtil;

class CmsConversationFormatter extends \ListItemFormatter
{

    protected $clientId;

    protected $showUserInfo;
    protected $showSiteInfo;

    protected $showFormInfo;
    protected $showDomainInfo;
    protected $specifyFields;
    protected $showFieldsInfo = true;

    public function __construct($clientId)
    {
        $this->clientId = $clientId;
    }

    public function strip($data)
    {
        isset($data['staff_user_id']) && $data['staff_user_id'] = is_array($data['staff_user_id']) ? $data['staff_user_id'] : PgsqlUtil::trimArray($data['staff_user_id']);
        isset($data['phrase']) && $data['phrase'] = is_array($data['phrase']) ? $data['phrase'] : json_decode($data['phrase'], true);

        return $data;
    }

    public function buildMapData()
    {
        $list = $this->batchFlag ? $this->listData : [$this->data];

        $userIds = [];
        $siteIds = [];
        $formIds = [];
        foreach ($list as $elem) {
            if ($this->batchFlag) {
                $elem = $this->strip($elem);
            }
            if ($this->showUserInfo) {
                $userIds = array_merge($elem['staff_user_id'], $userIds);
                $userIds[] = $elem['update_user'];
            }
            if ($this->showSiteInfo || $this->showDomainInfo) {
                $siteIds[] = $elem['site_id'];
            }
            if ($this->showFormInfo) {
                if ($elem['greeting_reply_type'] == CmsConversationSetting::REPLY_TYPE_OF_FORM) {
                    $formIds[] = $elem['greeting_reply_content'];
                }
                if ($elem['offline_reply_type'] == CmsConversationSetting::REPLY_TYPE_OF_FORM) {
                    $formIds[] = $elem['offline_reply_content'];
                }
            }
        }

        $userIds = array_filter($userIds);
        $userMap = [];
        if (!empty($userIds)) {
            $userList = \common\library\account\Helper::getBatchUserInfo($this->clientId, $userIds);
            $userList = array_map(function ($elem) {
                return ['user_id' => $elem->user_id, 'nickname' => $elem->nickname, 'avatar' => $elem->avatar];
            }, $userList);
            $userMap = array_combine(array_column($userList, 'user_id'), $userList);
        }

        $siteMap = [];
        $domainMap = [];
        if (!empty($siteIds)) {
            $siteList = new GaSiteList($this->clientId);
            $siteList->setSiteIds($siteIds);
            $siteList->setFields(['site_id', 'web_site_name', 'cms_site_id', 'web_site_url']);
            $siteListData = $siteList->find();
            $siteMap = \ArrayUtil::index($siteListData, 'site_id');
            if ($this->showDomainInfo) {
                $domainList = new DomainList($this->clientId);
                $domainList->setType(CmsConstants::DOMAIN_TYPE_SYSTEM);
                $domainList->setSiteId(array_column($siteListData, 'cms_site_id'));
                $domainList->setFields(['site_id', 'domain', 'https_flag']);
                $cmsDomainMap = [];
                foreach ($domainList->find() as $item) {
                    $header = '';
                    if (strpos($item['domain'], 'http') !== 0) {
                        $header = $item['https_flag'] ? 'https://' : 'http://';
                    }
                    $cmsDomainMap[$item['site_id']] = $header . $item['domain'];
                }
                foreach ($siteListData as $siteListDatum) {
                    $domainMap[$siteListDatum['site_id']] = $cmsDomainMap[$siteListDatum['cms_site_id']] ?? $siteListDatum['web_site_url'];
                }
            }
        }

        $formMap = [];
        $formIds = array_filter($formIds, 'intval');
        if (!empty($formIds)) {
            $formList = new FormList($this->clientId);
            $formList->setFormId($formIds);
            $formList->setFields(['form_id', 'form_name']);
            $formMap = array_column($formList->find(), 'form_name', 'form_id');
        }

        $this->mapData = [
            'user'   => $userMap,
            'site'   => $siteMap,
            'domain' => $domainMap,
            'form'   => $formMap,
        ];
    }

    public function infoListSetting()
    {
        $this->specifyFields = [
            'id',
            'setting_no',
            'staff_user_id',
            'client_id',
            'site_id',
            'disable_flag',
            'enable_flag',
            'create_time',
            'update_time',
            'update_user',
        ];
        $this->setShowUserInfo(true);
        $this->setShowDomainInfo(true);
        $this->setShowSiteInfo(true);
    }

    public function settingList()
    {
        $this->specifyFields = [
            'id',
            'setting_no',
            'staff_user_id',
            'client_id',
            'site_id',
            'disable_flag',
            'enable_flag',
        ];
    }

    public function infoDetailSetting()
    {
        $this->specifyFields = [
            'id',
            'setting_no',
            'staff_user_id',
            'client_id',
            'slogan',
            'site_id',
            'greeting_reply_flag',
            'greeting_reply_type',
            'greeting_reply_content',
            'offline_reply_flag',
            'offline_reply_type',
            'offline_reply_content',
            'disable_flag',
            'enable_flag',
            'create_time',
            'update_time',
            'update_user',
            'phrase',
            'box_color',
            'stay_time',
            'form_flag',
            'auto_reply_flag',
			'reception_type'
        ];
        $this->setShowUserInfo(true);
        $this->setShowSiteInfo(true);
        $this->setShowDomainInfo(true);
        $this->setShowFormInfo(true);
    }

    /**
     * @param mixed $showSiteInfo
     */
    public function setShowSiteInfo($showSiteInfo)
    {
        $this->showSiteInfo = $showSiteInfo;
    }

    /**
     * @param mixed $showUserInfo
     */
    public function setShowUserInfo($showUserInfo)
    {
        $this->showUserInfo = $showUserInfo;
    }

    /**
     * @param mixed $showDomainInfo
     */
    public function setShowDomainInfo($showDomainInfo)
    {
        $this->showDomainInfo = $showDomainInfo;
    }

    /**
     * @param mixed $showFormInfo
     */
    public function setShowFormInfo($showFormInfo)
    {
        $this->showFormInfo = $showFormInfo;
    }

    protected function buildFieldsInfo($data)
    {
        $result = [];

        if (!$this->showFieldsInfo) {
            return $result;
        }

        $specifyFields = $this->specifyFields;
        if ($this->specifyFields === null) {
            $specifyFields = array_keys($data);
        }

        foreach ($specifyFields as $field) {
            $result[$field] = $data[$field] ?? '';
            if (in_array($field, [
                'id',
                'setting_no',
                'client_id',
                'site_id',
                'greeting_reply_flag',
                'greeting_reply_type',
                'offline_reply_flag',
                'offline_reply_type',
                'disable_flag',
                'update_user',
                'enable_flag',
            ])) {
                $result[$field] = intval($result[$field]);
            } elseif ($field == 'staff_user_id') {
                $result[$field] = array_map('intval', $result[$field]);
            }
        }

        return $result;
    }

    protected function format($data)
    {
        $result = $this->buildFieldsInfo($data);

        if ($this->showSiteInfo) {
            $result['site_name'] = $this->getMapData('site', $data['site_id'])['web_site_name'] ?? '';
            $result['site_url'] = $this->getMapData('domain', $data['site_id']);
        }

        if ($this->showDomainInfo) {
            $result['domain_name'] = $this->getMapData('domain', $data['site_id']);
        }

        if ($this->showUserInfo) {
            foreach ($data['staff_user_id'] as $staffUserId) {
                $result['staff_user_info'][] = $this->getMapData('user', $staffUserId);
            }
            $result['update_user_info'] = $this->getMapData('user', $data['update_user']);
        }

        if ($this->showFormInfo) {
            if ($data['greeting_reply_type'] == CmsConversationSetting::REPLY_TYPE_OF_FORM) {
                $result['greeting_reply_form'] = $this->getMapData('form', $data['greeting_reply_content']);
            }
            if ($data['offline_reply_type'] == CmsConversationSetting::REPLY_TYPE_OF_FORM) {
                $result['offline_reply_form'] = $this->getMapData('form', $data['offline_reply_content']);
            }
        }

        return $result;
    }
}