<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2020/4/15
 * Time: 10:08 AM
 */

namespace common\library\cms\template;


use common\library\util\SqlBuilder;

/**
 * Class SiteTemplateList
 * @package common\library\cms\template
 * @method SiteTemplateFormatter getFormatter()
 */
class SiteTemplateList extends \MysqlList
{
    protected $clientId;
    protected $siteId;
    protected $templateId;
    protected $enableFlag;
    protected $templateType;

    protected $fields;
    /**
     * SiteTemplateList constructor.
     * @param $clientId
     */
    public function __construct($clientId)
    {
        $this->clientId = $clientId;
        $this->formatter = new SiteTemplateFormatter($this->clientId);
    }


    /**
     * @param mixed $siteId
     */
    public function setSiteId($siteId)
    {
        $this->siteId = $siteId;
    }

    /**
     * @param mixed $templateId
     */
    public function setTemplateId($templateId)
    {
        $this->templateId = $templateId;
    }

    /**
     * @param mixed $enableFlag
     */
    public function setEnableFlag($enableFlag)
    {
        $this->enableFlag = $enableFlag;
    }

    /**
     * @param mixed $templateType
     */
    public function setTemplateType($templateType)
    {
        $this->templateType = $templateType;
    }

    /**
     * @param mixed $fields
     */
    public function setFields(array $fields)
    {
        $this->fields = $fields;
    }

    public function buildParams()
    {
        $alias ='';
        $params = [':client_id'=> $this->clientId];
        $sql = "client_id =:client_id";

        if( $this->siteId )
        {
            SqlBuilder::buildIntWhere($alias, 'site_id', $this->siteId, $sql, $params);
        }

        if( $this->templateId )
        {
            SqlBuilder::buildIntWhere($alias, 'template_id', $this->templateId, $sql, $params);
        }

        if( $this->templateType )
        {
            SqlBuilder::buildIntWhere($alias, 'template_type', $this->templateType, $sql, $params);
        }

        if( $this->enableFlag !== null )
        {
            SqlBuilder::buildIntWhere($alias, 'enable_flag', $this->enableFlag, $sql, $params);
        }

        return [$sql, $params];
    }

    protected function buildFields()
    {

        if( empty($this->fields ) )
        {
            return  '*';
        }

        return implode(',', $this->fields);
    }


    public function find()
    {
        list($sql,$params) = $this->buildParams();

        $db =  \ProjectActiveRecord::getDbByClientId($this->clientId);
        $table = \CmsSiteTemplate::model()->tableName();

        $limit = $this->buildLimit();
        $order = $this->buildOrderBy();
        $fields =  $this->buildFields();

        $sql = "select {$fields} from $table  where $sql $order $limit";
        $result = $db->createCommand($sql)->queryAll(true, $params);

        if( empty($result) )
            return $result;

        if($this->formatter && empty($this->fields))
        {
            $this->formatter->setListData($result);
            $result = $this->formatter->result();
        }

        return $result;
    }

    public function count()
    {
        list($sql,$params) = $this->buildParams();

        $db =  \ProjectActiveRecord::getDbByClientId($this->clientId);
        $table = \CmsSiteTemplate::model()->tableName();

        $sql = "select count(1) as count from $table  where $sql ";
        $count = $db->createCommand($sql)->queryScalar($params);
        return $count;
    }
}
