<?php
/**
 * Created by PhpStorm.
 * User: bing
 * Date: 2020/04/20
 * Time: 18:34
 */

namespace common\library\cms\article;

use common\components\BaseObject;
use common\library\setting\library\group\Group;
use common\library\util\SqlBuilder;

/**
 * Class ArticleList
 * @package common\library\cms\article
 * @method ArticleFormatter getFormatter()
 */
class ArticleList extends \MysqlList {

    protected $clientId;
    protected $groupId;
    protected $siteId;
    protected $articleId;
    protected $showFlag;
    protected $enableFlag = 1;
    protected $keyword;
    protected $includeSystemGroup = true;


    protected $fields;

    public function __construct($clientId) {
        $this->clientId = $clientId;
        $this->formatter = new ArticleFormatter($clientId);
    }


    public function setFields($fields)
    {
        if(is_array($fields)) {
            $this->fields = implode(',', $fields);
        }else {
            $this->fields = $fields;
        }
    }


    public function setGroupId($groupId) {
       $this->groupId  = $groupId;
    }

    public function setSiteId($siteId) {
        $this->siteId = $siteId;
    }

    public function setArticleId($articleId) {
        $this->articleId = $articleId;
    }

    public function getArticleId() {
        return $this->articleId;
    }

    public function setEnableFlag($enableFlag) {
        $this->enableFlag = $enableFlag;
    }

    public function setShowFlag($showFlag) {
        $this->showFlag = $showFlag;
    }

    public function setKeyword($keyword) {
        $this->keyword = $keyword;
    }

    /**
     * @param bool|int $includeSystemGroup
     */
    public function setIncludeSystemGroup($includeSystemGroup)
    {
        $this->includeSystemGroup = $includeSystemGroup;
    }


    public function buildParams()
    {
        $alias = '';
        $sql = "client_id=:client_id";
        $params = [':client_id' => $this->clientId];

        if($this->groupId !== null && $this->groupId !== '') {
            SqlBuilder::buildIntWhere($alias, 'group_id', $this->groupId, $sql, $params);
        }

        if ($this->siteId) {
            $sql .= " AND site_id=:site_id";
            $params[':site_id'] = $this->siteId;
        }

        if ($this->articleId) {
            SqlBuilder::buildIntWhere($alias, 'article_id', $this->articleId, $sql, $params);
        }

        if($this->enableFlag !== null) {
            $sql .= " AND enable_flag=:enable_flag";
            $params[':enable_flag'] = $this->enableFlag;
        }

        if($this->showFlag !== null && $this->showFlag !== '') {
            $sql .= " AND show_flag=:show_flag";
            $params[':show_flag'] = $this->showFlag;
        }

        if( !$this->includeSystemGroup)
        {
            $sql .= " AND group_id !=".Group::SYS_UNCLASSIFIED_GROUP_ID;
        }

        if($this->keyword) {
            $keyword = \Util::escapeDoubleQuoteSql($this->keyword);
            $sql .= " AND `title` LIKE '%{$keyword}%' ";
        }

        return [$sql, $params];
    }

    public function find()
    {
        list($where, $params) = $this->buildParams();
        $orderBy = $this->buildOrderBy();
        $limit = $this->buildLimit();
        $fields = $this->fields ?? '*';

        $table = \CmsSiteArticle::model()->tableName();

        $sql = "SELECT {$fields} FROM {$table} WHERE {$where} {$orderBy} {$limit}";

        $db = \ProjectActiveRecord::getDbByClientId($this->clientId);

        $result = $db->createCommand($sql)->queryAll(true, $params);

        if ($this->formatter && empty($this->fields)) {
            $this->formatter->setListData($result);
            $result = $this->formatter->result();
        }

        return $result;
    }

    public function count() {
        list($where, $params) = $this->buildParams();
        $table = \CmsSiteArticle::model()->tableName();
        $sql = "SELECT count(1) as count FROM {$table} WHERE {$where}";
        $db = \ProjectActiveRecord::getDbByClientId($this->clientId);
        $count = $db->createCommand($sql)->queryScalar($params);
        return $count;
    }
}
