<?php
/**
 * Created by PhpStorm.
 * User: beenzhang
 * Date: 2020/10/26
 * Time: 2:05 PM
 */

namespace common\library\lock;

use common\library\custom_field\FieldList;

/**
 * Class FieldLock
 * @package common\library\lock
 * client维度
 * 字段+值 锁，防止并发导致需要唯一的字段数据重复（例：company->name,customer->email）
 */
class FieldLock
{
    const PREFIX = 'field_value_lock';

    /**
     * @var FieldLock
     */
    static private $instance = null;
    static private $filedMap = null;

    protected $moduleType;
    protected $clientId;

    public static function instance($clientId, $moduleType)
    {
        if (self::$instance == null || self::$instance->clientId != $clientId)
            self::$instance = new self($clientId, $moduleType);

        return self::$instance;
    }

    protected function __construct($clientId, $moduleType)
    {
        $this->clientId = $clientId;
        $this->moduleType = $moduleType;
    }

    protected function getModule()
    {
        if (empty($this->moduleType))
        {
            throw new \ProcessException('请指定模块');
        }

        return $this->moduleType;
    }

    protected function getFieldMap($refresh = false)
    {
        if (self::$filedMap === null || $refresh == true)
        {
            $customFieldList = new FieldList($this->clientId);
            $customFieldList->setType($this->moduleType);
            $customFieldList->setEnableFlag(null);
            $customFieldList->setFields(['id', 'name', 'field_type', 'relation_field', 'relation_field_type']);
            $fieldMapList = $customFieldList->find();
            $fieldMap = array_column($fieldMapList, null, 'id');

            self::$filedMap = $fieldMap;
        }

        return self::$filedMap;
    }

    protected function buildKey($field, $value)
    {
        if (empty($value))
            return false;
        if ($field == 'external_field_data')
            return false;

        if (is_array($value))
            $value = json_encode($value);

        $value = md5($value);

        return self::PREFIX . ':' .$this->clientId. ':' . $this->getModule() . ':' . $field .':'. $value;
    }

    /**
     * @param $field
     * @param $value
     * @param int $expireTime
     * @return bool|null|string 正常情况返回字符串，如已设置过锁则返回null
     */
    public function lock($field, $value, int $expireTime = 30)
    {
        $lockKey = $this->buildKey($field, $value);
        if (!$lockKey)
            return '';

        try {
            /**
             * @var $cache \CRedisCache
             */
            $cache = \Yii::app()->cache;
            $setSuccess = $cache->executeCommand('SET', [$lockKey, 1, 'EX', $expireTime, 'NX']);
            if (!$setSuccess)
                return null;
        } catch (\Exception $e) {
            \LogUtil::info('lock fail!'.$e->getMessage());
            return '';
        }

        return $lockKey;
    }

    public static function unlock($lockKey)
    {
        if (empty($lockKey))
            return true;

        $lockKey = is_array($lockKey) ? $lockKey : [$lockKey];
        $lockKey = array_values(array_filter($lockKey));
        if (empty($lockKey))
            return true;

        /**
         * @var $cache \CRedisCache
         */
        $cache = \Yii::app()->cache;
        $ret = $cache->executeCommand('DEL', $lockKey);
        if (!$ret) {
            \LogUtil::info('del field lock fail!!!'.json_encode($lockKey));
            return false;
        }
        return true;
    }
}