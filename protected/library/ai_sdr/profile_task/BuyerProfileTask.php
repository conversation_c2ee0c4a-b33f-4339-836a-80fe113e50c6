<?php
/**
 *  实时生成卖家画像任务
 * @property string $task_id
 * @property int $client_id
 * @property int $user_id
 * @property array $pages
 * @property array $primary_products
 * @property int $status
 * @property array $err_code
 * @property array $err_msg
 * @property string $create_time
 * @property string $update_time
 * @property int $inherited_id
 * @property int $enable_flag
 * @property array $ai_record_ids
 */

namespace common\library\ai_sdr\profile_task;

use common\library\ai_agent\AiSdrBuyerProfileAgent;
use common\library\ai_sdr\Helper;
use common\library\Crawler;

/**
 * @deprecated
 */
class BuyerProfileTask extends \common\components\BaseObject
{
    protected array $errCode = [];
    protected array $errMsg = [];

    static public function newTask($taskId, $clientId, $userId, $pages, $inheritedId)
    {
        #先将历史任务置为无效
        $buyerList = new BuyerProfileTaskList($clientId, $userId);
        $buyerList->disableHistoricalTasks($inheritedId);
        #再新建任务，确保一个总任务下只有一个自任务
        $task = new BuyerProfileTask($taskId);
        $task->client_id = $clientId;
        $task->user_id = $userId;
        $task->pages = $pages;
        $task->status = TaskStatus::STATUS_PENDING;
        $task->inherited_id = $inheritedId;
        return $task;
    }

    public function __construct($task_id)
    {
        $this->task_id = $task_id;
        $this->_formatter = new BuyerProfileTaskFormatter();
        if ($this->task_id) {
            $this->loadById($this->task_id);
        }
    }

    public function getModelClass()
    {
        return \common\models\pg_admin\TblAiSdrLiveBuyerProfileTask::class;
    }

    protected function beforeSave()
    {
        if ($this->isNew()) {
            $this->create_time = $this->update_time = date('Y-m-d H:i:s');
        } else {
            $this->update_time = date('Y-m-d H:i:s');
        }
        return parent::beforeSave();
    }

    public function loadById($taskId)
    {
        $model = $this->getModelClass()::model()->findByPk($taskId);
        if ($model) {
            $this->setModel($model);
        }
    }

    public function generate($limit = 3)
    {
        $this->reset();

        $this->doGenerate($limit);

        if (empty($this->primary_products) && !empty($this->errCode)) {
            $this->status = TaskStatus::STATUS_FAILED;
        } else {
            $this->status = TaskStatus::STATUS_COMPLETED;
        }
        $this->err_code = $this->errCode;
        $this->err_msg = $this->errMsg;
        $this->save();
    }

    public function doGenerate($limit = 3)
    {
        if (empty($this->client_id) || empty($this->user_id)) {
            \LogUtil::info(sprintf('ai_sdr task_id %s missing client_id or user_id', $this->task_id));
            $this->errCode[] = ErrorCode::ERR_CODE_MISSING_CLIENT_ID;
            $this->errMsg[] = 'missing client_id or user_id';
            return;
        }

        $pages = array_slice($this->pages, 0, $limit);

        foreach ($pages as $page) {
            #数据爬取
            $content = $this->crawl($page);
            #数据清洗
            if (!empty($content)) {
                $aiRes = $this->aiWash($content);
            } else {
                $this->errCode[] = ErrorCode::ERR_CODE_BUYER_CRAWLER_RESULT_EMPTY;
                $this->errCode = array_unique($this->errCode);
                $this->errMsg[] = "crawler result is empty ,nothing for ai_wash ,url:" . $page;
            }
            #获取结果
            $this->primary_products = Helper::mergeSet($this->primary_products, $aiRes['primary_products'] ?? []);
        }
    }

    protected function reset()
    {
        $this->primary_products = [];
        $this->status = TaskStatus::STATUS_WORKING;
        $this->err_code = [];
        $this->err_msg = [];
        $this->save();
    }

    protected function crawl($page)
    {
        if (empty($page)) {
            return '';
        }
        $content = '';
        $payload = [
            'client_id' => $this->client_id,
            'user_id' => $this->user_id,
            'task_id' => $this->task_id
        ];
        $crawler = new Crawler();
        #爬取主页
        try {
            $page = Helper::getUrl($page);
            $payload = array_merge($payload, ['stage' => 'ai_sdr_buyer_page']);
            $content = $crawler->getHtmlAndParse($page, $payload);
        } catch (\Exception $e) {
            \LogUtil::info(sprintf('ai_sdr task_id %s buyer page crawl failed,url:%s,code:%s,msg:%s', $this->task_id, $page, $e->getCode(), $e->getMessage()));
            $this->errCode[] = ErrorCode::ERR_CODE_BUYER_PAGE_CRAWLER_FAILED;
            $this->errCode = array_unique($this->errCode);
            $this->errMsg[] = sprintf('buyer page crawl failed,url:%s,code:%s,msg:%s', $page, $e->getCode(), $e->getMessage());
        }

        return $content;
    }

    protected function aiWash($content)
    {
        try {
            $agent = new AiSdrBuyerProfileAgent((int)$this->client_id, (int)$this->user_id);
            $params = [
                'page_content' => $content
            ];
            $aiResp = $agent->process($params);
            $this->record_id = array_merge($this->record_id, [$aiResp->recordId]);
        } catch (\Exception $e) {
            \LogUtil::info(sprintf('ai_sdr task_id %s buyer ai wash failed,url:%s,code:%s,msg:%s', $this->task_id, $this->page, $e->getCode(), $e->getMessage()));
            $this->errCode[] = ErrorCode::ERR_CODE_BUYER_AI_WASH_FAILED;
            $this->errCode = array_unique($this->errCode);
            $this->errMsg[] = sprintf('buyer ai wash failed,url:%s,code:%s,msg:%s', $this->page, $e->getCode(), $e->getMessage());
        }
        return json_decode($aiResp->answer ?? '', true);
    }
}