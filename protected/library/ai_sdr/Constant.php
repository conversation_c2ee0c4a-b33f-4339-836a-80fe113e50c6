<?php
/**
 * Created by Php<PERSON>torm.
 * Author: <PERSON>(<PERSON><PERSON><PERSON>)
 * Date: 2025/3/26
 * Time: 15:48
 */

namespace common\library\ai_sdr;

use common\library\tag\v2\Tag;

class Constant
{

    const PRODUCT_USAGE_DISABLE_TYPE_SYSTEM = 0; // 系统触发（后台删除、去除等）
    const PRODUCT_USAGE_DISABLE_TYPE_USER = 1; // 用户行为触发
    const DAILY_LIMIT = 70; // 每日上限 100，单个任务限制挖掘数量
    const DAILY_LIMIT_FOR_LITE_FREE = 35;
    const DAILY_LIMIT_FOR_SMART_FREE = 70;
    const DAILY_LIMIT_FOR_TRIAL = 20;

    const TOTAL_LIMIT_UNLIMITED = -1;
    const TOTAL_LIMIT_FOR_TRIAL = 50;
    const TOTAL_LIMIT_FOR_FREE_WITH_LITE = 2000;
    const TOTAL_LIMIT_FOR_FREE_WITH_SMART_PRO = 4000;

    const TASK_DAILY_LIMIT_CACHE_KEY  = 'sdr:task:limit:{%s}:{%s}';
    const DIG_THRESHOLD = 1000; // 挖掘阈值, 小于阈值触发挖掘
    const MATCH_RULE_MAP = [
        self::MATCH_TYPE_INDUSTRY => self::MATCH_RULE_INDUSTRY,
        self::MATCH_TYPE_PRODUCT_USAGE => self::MATCH_RULE_PRODUCT,
    ];
    const MATCH_TYPE_INDUSTRY = 1;
    const MATCH_TYPE_PRODUCT_USAGE = 2;
    const MATCH_RULE_PRODUCT = 'match_product';
    const MATCH_RULE_INDUSTRY = 'match_industry';
    const MIN_THRESHOLD = 0.15;
    const MIN_THRESHOLD_INDUSTRY = 0.40;
    const MIN_THRESHOLD_PRODUCT = 0.25;

    // 0 待处理 1 已标准化 2 标准化失败 3 正在推荐数据 4 已推荐完成
    const USAGE_STATUS_WAITING = 0;
    const USAGE_STATUS_STANDARDIZED = 1;
    const USAGE_STATUS_STANDARDIZED_FAILED = 2;
    const USAGE_STATUS_RECOMMENDING = 3;
    const USAGE_STATUS_FINISH_RECOMMEND = 4;

    const AI_SDR_TASK_STATUS_DRAFT = 0; // 草稿
    const AI_SDR_TASK_STATUS_PROCESSING = 1; // 处理中
    const AI_SDR_TASK_STATUS_PAUSED = 2; // 暂停
    const AI_SDR_TASK_STATUS_FINISHED = 3; // 已完成

    const AI_SDR_GRAPH_STATUS_INIT = 0; // 待处理
    const AI_SDR_GRAPH_STATUS_PROCESSING = 1; // 处理中
    const AI_SDR_GRAPH_STATUS_FINISHED = 2; // 已完成

    const AI_SDR_STAGE_DIG_FUNCTION = 'dig';
    const AI_SDR_STAGE_REACHABLE_FUNCTION = 'backgroundChecking';
    const AI_SDR_STAGE_MARKETING_FUNCTION = 'marketing';
    const AI_SDR_STAGE_EFFECTIVE_FUNCTION = 'effectiveLeads';
    const AI_SDR_STAGE_HIGHVALUE_FUNCTION = 'highValueLeads';


    // ai_sdr 交付阶段
    const AI_SDR_STAGE_NOT_DEFINED = -1;
    const AI_SDR_STAGE_DIG = 0; //挖掘、企业背调（线索孵化）
    const AI_SDR_STAGE_REACHABLE = 1;   //可触达（潜客挖掘）
    const AI_SDR_STAGE_MARKETING = 2;   //营销
    const AI_SDR_STAGE_EFFECTIVE = 3;   //有效
    const AI_SDR_STAGE_HIGHVALUE = 4;   //高价值

    const AI_SDR_STAGE_FUNCTION = [
        self::AI_SDR_STAGE_DIG => self::AI_SDR_STAGE_DIG_FUNCTION,
        self::AI_SDR_STAGE_REACHABLE => self::AI_SDR_STAGE_REACHABLE_FUNCTION,
        self::AI_SDR_STAGE_MARKETING => self::AI_SDR_STAGE_MARKETING_FUNCTION,
    ];
    const AI_SDR_STAGE_MAP = [
        self::AI_SDR_STAGE_NOT_DEFINED => 'waiting', //  待处理阶段没有对应 function
        self::AI_SDR_STAGE_DIG => 'all',
        self::AI_SDR_STAGE_REACHABLE => 'reachable',
        self::AI_SDR_STAGE_MARKETING => 'marketing',
        self::AI_SDR_STAGE_EFFECTIVE => 'effective',
        self::AI_SDR_STAGE_HIGHVALUE => 'highValue',
    ];

    const AI_SDR_STAGE_ARRAY = [
        self::AI_SDR_STAGE_NOT_DEFINED,
        self::AI_SDR_STAGE_DIG,
        self::AI_SDR_STAGE_REACHABLE,
        self::AI_SDR_STAGE_MARKETING,
        self::AI_SDR_STAGE_EFFECTIVE,
        self::AI_SDR_STAGE_HIGHVALUE,
    ];

    const TASK_SOURCE_SYSTEM = -1; // 系统预置 task
    const TASK_SOURCE_AI_SDR = 1; // AI SDR 自动挖掘
    const TASK_SOURCE_IMPORT = 2; // 手动导入线索
    const TASK_SOURCE_CRM_EP = 3; // 双保效

    const LEAD_QUALITY_UNKNOWN = 0; // 潜客质量未打标
    const LEAD_QUALITY_HIGH = 3; // 高质量潜客
    const LEAD_QUALITY_MEDIUM = 2; // 中等质量潜客
    const LEAD_QUALITY_LOW = 1; // 低质量潜客


    const LEAD_QUALITY_MAP = [
        'high' => self::LEAD_QUALITY_HIGH,
        'medium' => self::LEAD_QUALITY_MEDIUM,
        'low' => self::LEAD_QUALITY_LOW,
    ];

    const LEAD_TAG_QUALITY_MAP = [
        self::LEAD_QUALITY_LOW => \common\library\setting\library\tag\Tag::TAG_LOW_VALUE,
        self::LEAD_QUALITY_MEDIUM => \common\library\setting\library\tag\Tag::TAG_MIDDLE_VALUE,
        self::LEAD_QUALITY_HIGH => \common\library\setting\library\tag\Tag::TAG_HIGH_VALUE,
    ];

    const RECORD_TYPE_ADD_LEAD = 0;
    const RECORD_TYPE_BACKGROUND_CHECK = 1;
    const RECORD_TYPE_ANALYZE_QUALITY = 2;
    const RECORD_TYPE_CHECK_CONTACTS = 3;
    const RECORD_TYPE_CREATE_MARKET_PLAN = 4;
    const RECORD_TYPE_EXECUTE_MARKET_PLAN = 5;
    const RECORD_TYPE_HATCH_SUCCESS = 6;
    const RECORD_TYPE_HATCH_FAILED = 7;
    const RECORD_TYPE_DELIVERY_SUCCESS = 8;
    const RECORD_TYPE_DELIVERY_FAILED = 9;
    const RECORD_TYPE_HATCH_ABORT = 10; // 放弃孵化
    const RECORD_TYPE_UPDATE_LEAD_READ_FLAG = 11;

    // 0 添加到潜客池 1 潜客分层 2开始背调 3背调完成 4校验联系方式 5指定营销计划 6 执行营销 -1 错误
    const DETAIL_STATUS_ADD = 0;
    const DETAIL_STATUS_LABEL = 1;
    const DETAIL_STATUS_BACKGROUND_CHECKING = 2;
    const DETAIL_STATUS_BACKGROUND_CHECKED = 3;
    const DETAIL_STATUS_VALIDATE_CONTACTS = 4;
    const DETAIL_STATUS_CREATE_MARKETING_PLAN = 5;
    const DETAIL_STATUS_EXECUTE_MARKETING_PLAN = 6;
    const DETAIL_STATUS_EFFECTIVE_LEADS = 7;
    const DETAIL_STATUS_HIGH_VALUE_LEADS = 8;
    const DETAIL_STATUS_ERROR = -1; // 错误 无法继续孵化


    const SELLER_PROFILE_TASK_TYPE_TASK_EXISTED = 'seller_profile_task_existed';
    const SELLER_PROFILE_TASK_TYPE_PROFILE_EXISTED = 'seller_profile_task_profile_existed';
    const SELLER_PROFILE_TASK_TYPE_LIVING_GENERATED = 'seller_profile_task_asynchronous_execution';

    const DEFAULT_RECORD = [
        'addLead' => ['type' => Constant::RECORD_TYPE_ADD_LEAD, 'status' => 0],
        'backgroundCheck' => ['type' => Constant::RECORD_TYPE_BACKGROUND_CHECK, 'status' => 0],
        'analyzeQuality' => ['type' => Constant::RECORD_TYPE_ANALYZE_QUALITY, 'status' => 0],
        'checkContacts' => ['type' => Constant::RECORD_TYPE_CHECK_CONTACTS, 'status' => 0],
        'createMarketingPlan' => ['type' => Constant::RECORD_TYPE_CREATE_MARKET_PLAN, 'status' => 0],
        'marketingRound1' => ['type' => Constant::RECORD_TYPE_EXECUTE_MARKET_PLAN, 'status' => 0, 'marketingReach' => ['round' => 1, 'contactList' => [], 'list' => []]],
        'marketingRound2' => ['type' => Constant::RECORD_TYPE_EXECUTE_MARKET_PLAN, 'status' => 0, 'marketingReach' => ['round' => 2, 'contactList' => [], 'list' => []]],
        'marketingRound3' => ['type' => Constant::RECORD_TYPE_EXECUTE_MARKET_PLAN, 'status' => 0, 'marketingReach' => ['round' => 3, 'contactList' => [], 'list' => []]],
        'hatchSuccess' => ['type' => Constant::RECORD_TYPE_HATCH_SUCCESS, 'status' => 0],
//        'deliverySuccess' => ['type' => Constant::RECORD_TYPE_DELIVERY_SUCCESS, 'status' => 0],
    ];

    const RECORD_REFER_TYPE_EDM = 3; // 关联 edm 任务
    const RECORD_REFER_TYPE_LEAD = 1; // 交付线索 id
    const RECORD_REFER_TYPE_CONTACT = 2; // 交付客户 id

    const AI_SDR_TASK_DELIVERY_TYPE_LEAD = 0;   //交付为线索
    const AI_SDR_TASK_DELIVERY_TYPE_COMPANY = 1;    //交付为客户

    // 潜客来源
    const DETAIL_SOURCE_DIG = 1;    //挖掘
    const DETAIL_SOURCE_LEAD = 2;   //线索
    const DETAIL_SOURCE_COMPANY = 3;    //客户

    const REDIS_CACHE_TASK_KEY = "sdr:task:{%s}:{%s}";
    const REDIS_FILTER_TIPS_TIMES_KEY = "sdr:filter_tips_times:{%s}:{%s}:{%s}:{%s}";
    const REDIS_AI_SDR_TASK_END_STAGE_KEY = "sdr:task:end_stage:{%s}:{%s}";
    const REDIS_AI_SDR_TASK_DIG_PROCESSING_KEY = "sdr:task:dig_processing:{%s}:{%s}";

    const AI_SDR_POTENTIAL_CUSTOMER_COUNT_LIMIT = 10000;   //潜客数量限制
    const AI_SDR_DRAFT_POTENTIAL_CUSTOMER_LIMIT = 50; // 草稿任务潜客数量限制

    /** 交互消息类型保持与前端枚举值一致 */
    // 纯文本类消息
    const SDR_MESSAGE_TYPE_TEXT = 0;
    // 工作汇报
    const SDR_MESSAGE_TYPE_WORKS_REPORT = 1;
    //操作指南
    const SDR_MESSAGE_TYPE_OPERATION_GUIDE = 2;
    //任务暂停
    const SDR_MESSAGE_TYPE_PAUSED = 3;
    //工作计划
    const SDR_MESSAGE_WORKS_PLAN = 4;
    //工作成果
    const SDR_MESSAGE_WORK_STATS = 5;
    //潜客移除原因
    const SDR_MESSAGE_TYPE_REMOVE_LEADS_REASON = 6;
    //筛选提示
    const SDR_MESSAGE_TYPE_FILTER_TIP = 7;
    //资产盘活
    const SDR_MESSAGE_TYPE_VITALIZE = 8;
    //新增资产提示
    const SDR_MESSAGE_TYPE_ADD_TIP = 9;
    //当前资产列表已全部盘完
    const SDR_MESSAGE_TYPE_ALL_VITALIZED = 10;
    const SDR_MESSAGE_TYPE_OPERATION_GUIDE_DETAIL = 11;
    const SDR_MESSAGE_TYPE_REMOVE_LEADS_REASON_END = 12;
    const SDR_MESSAGE_TYPE_FILTER_TIP_NOTIFICATION = 13;
    const SDR_MESSAGE_TYPE_FILTER_TIP_NOTIFICATION_END = 14;

    //盘活场景筛选潜客卡片
    const AI_SDR_CARD_LEAD_SLEEP = 1;
    const AI_SDR_CARD_COMPANY_HIGH_INTENTION = 2;
    const AI_SDR_CARD_COMPANY_NEW = 3;
    const AI_SDR_CARD_COMPANY_HIGH_QUALITY = 4;

    const AI_SDR_CARD_MAP = [
        self::AI_SDR_CARD_LEAD_SLEEP => [
            'card_type' => self::AI_SDR_CARD_LEAD_SLEEP,
            'title' => '30天前移入公海的线索',
            'sub_title' => '沉睡资产盘活',
            'source' => self::DETAIL_SOURCE_LEAD,
            'filters' => [
                [
                    'field' => 'public_time',
                    'field_type' => 10,
                    'operator' => 'earlier',
                    'value' => -30,
                    'unit' => '天',
                    'value_type' => 'before_current',
                    'date_type' => 3,
                    'refer_type' => 7,
                    'name' => '最近进入公海时间',
                ]
            ],
            'criteria_type' => 1,
            'criteria' => '',
        ],
        self::AI_SDR_CARD_COMPANY_HIGH_INTENTION => [
            'card_type' => self::AI_SDR_CARD_COMPANY_HIGH_INTENTION,
            'title' => '有商机记录但>30天未联系的公海客户',
            'sub_title' => '高意向资产盘活',
            'source' => self::DETAIL_SOURCE_COMPANY,
            'filters' => [
                [
                    'field' => 'success_opportunity_count',
                    'value' => 1,
                    'name' => '赢单商机数',
                    'operator' => '>=',
                    'object_name' => 'objCompany',
                    'field_type' => 5,
                    'array_flag' => 0,
                    'refer_type' => 4,
                    'filter_no' => 1,
                ],
                [
                    'field' => 'ongoing_opportunity_count',
                    'value' => 1,
                    'name' => '进行中的商机数',
                    'operator' => '>=',
                    'object_name' => 'objCompany',
                    'field_type' => 5,
                    'array_flag' => 0,
                    'refer_type' => 4,
                    'filter_no' => 2,
                ],
                [
                    'field' => 'order_time',
                    'value' => -30,
                    'name' => '最近联系时间',
                    'operator' => 'earlier',
                    'object_name' => 'objCompany',
                    'field_type' => 10,
                    'array_flag' => 0,
                    'unit' => '天',
                    'value_type' => 'before_current',
                    'refer_type' => 4,
                    'filter_no' => 3,
                ],
            ],
            'criteria_type' => 3,
            'criteria' => '(1 or 2) and 3'
        ],
        self::AI_SDR_CARD_COMPANY_NEW => [
            'card_type' => self::AI_SDR_CARD_COMPANY_NEW,
            'title' => '近7天移入公海的客户',
            'sub_title' => '新增资产盘活',
            'source' => self::DETAIL_SOURCE_COMPANY,
            'filters' => [
                [
                    'field' => 'public_time',
                    'value' => -7,
                    'name' => '最近进入公海时间',
                    'operator' => 'later',
                    'object_name' => 'objCompany',
                    'field_type' => 10,
                    'array_flag' => 0,
                    'unit' => '天',
                    'value_type' => 'before_current',
                    'refer_type' => 4,
                ]
            ],
            'criteria_type' => 1,
            'criteria' => '',
        ],
        self::AI_SDR_CARD_COMPANY_HIGH_QUALITY => [
            'card_type' => self::AI_SDR_CARD_COMPANY_HIGH_QUALITY,
            'title' => '国际站L3/L4的公海客户',
            'sub_title' => '高质量资产盘活',
            'source' => self::DETAIL_SOURCE_COMPANY,
            'filters' => [
                [
                    'field' => 'growth_level',
                    'value' => [3, 4],
                    'name' => '阿里买家身份',
                    'operator' => 'in',
                    'object_name' => 'objCompany',
                    'field_type' => 3,
                    'array_flag' => 0,
                    'refer_type' => 4,
                ]
            ],
            'criteria_type' => 1,
            'criteria' => '',
        ],
    ];

    const AI_SDR_MESSAGE_FILTER_TIPS_TYPE_PRODUCT = 'product';
    const AI_SDR_MESSAGE_FILTER_TIPS_TYPE_COMPANY_TYPE = 'type';

    const CRM_BUYER_PORTRAIT = 1;
    const LEADS_BUYER_PORTRAIT = 2;

    const TASK_DETAIL_STAGE_TIME_MAP = [
        self::AI_SDR_STAGE_DIG => 'stage_dig_time',
        self::AI_SDR_STAGE_REACHABLE => 'stage_reachable_time',
        self::AI_SDR_STAGE_MARKETING => 'stage_marketing_time',
        self::AI_SDR_STAGE_EFFECTIVE => 'stage_effective_time',
        self::AI_SDR_STAGE_HIGHVALUE => 'stage_highvalue_time',
    ];

    const DETAIL_DELIVERY_STATUS_HATCH_SUCCESS = 1;
    const DETAIL_DELIVERY_STATUS_HATCH_FAILED = 2;
    const DETAIL_DELIVERY_STATUS_DELIVERY_SUCCESS = 3;
    const DETAIL_DELIVERY_STATUS_DELIVERY_FAILED = 4;

    const RECORD_TYPE_DELIVERY_STATUS_MAP = [
        self::RECORD_TYPE_HATCH_SUCCESS => self::DETAIL_DELIVERY_STATUS_HATCH_SUCCESS,
        self::RECORD_TYPE_HATCH_FAILED => self::DETAIL_DELIVERY_STATUS_HATCH_FAILED,
        self::RECORD_TYPE_DELIVERY_SUCCESS => self::DETAIL_DELIVERY_STATUS_DELIVERY_SUCCESS,
        self::RECORD_TYPE_DELIVERY_FAILED => self::DETAIL_DELIVERY_STATUS_DELIVERY_FAILED,
    ];

}