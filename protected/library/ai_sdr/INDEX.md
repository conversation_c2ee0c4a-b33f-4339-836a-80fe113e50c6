# AI SDR 文档索引

## 文档概览

本目录包含AI SDR (Sales Development Representative) 智能销售开发系统的完整技术文档。

## 文档结构

### 📚 主要文档

#### [README.md](./README.md) - 完整设计文档
- **内容**: 系统架构、核心模块、业务流程、ORM使用、扩展指南
- **适用人群**: 开发人员、架构师、技术负责人
- **阅读时间**: 30-45分钟

#### [QUICK_REFERENCE.md](./QUICK_REFERENCE.md) - 快速参考指南
- **内容**: 常用类方法、常量定义、调试技巧、性能优化
- **适用人群**: 开发人员、运维人员
- **阅读时间**: 10-15分钟

### 🎯 核心模块

#### 任务管理 (task/)
- `AiSdrTask.php` - 任务实体模型
- `AiSdrTaskFilter.php` - 任务查询过滤器
- `AiSdrTaskFormatter.php` - 任务数据格式化
- `AiSdrTaskOperator.php` - 任务操作器

#### 任务详情 (task_detail/)
- `AiSdrTaskDetail.php` - 任务详情实体
- `AiSdrTaskDetailFilter.php` - 详情查询过滤器
- `AiSdrTaskDetailFormatter.php` - 详情数据格式化
- `AiSdrTaskDetailOperator.php` - 详情操作器

#### 任务记录 (task_record/)
- `AiSdrTaskRecord.php` - 任务记录实体
- `AiSdrTaskRecordFilter.php` - 记录查询过滤器
- `AiSdrTaskRecordFormatter.php` - 记录数据格式化
- `builder/` - 记录构建器

#### 消息系统 (message/)
- `Message.php` - 消息实体
- `MessageService.php` - 消息服务
- `message_extends/` - 消息扩展类型

### 🔧 核心服务

#### [AISdrService.php](./AISdrService.php) - 核心业务服务
- 任务处理调度
- 潜客挖掘逻辑
- 线索创建管理
- 统计数据更新

#### [SdrDetailExecutor.php](./SdrDetailExecutor.php) - 状态机执行器
- Symfony Workflow集成
- 状态转换验证
- AI服务调用
- 异常处理机制

#### [Constant.php](./Constant.php) - 常量定义
- 任务状态常量
- 阶段定义
- 记录类型
- 缓存键模式

### 🤖 AI集成

#### 买家画像 (buyer_portrait/)
- `CrmBuyerPortrait.php` - 买家画像实体
- `CrmBuyerPortraitFormatter.php` - 画像格式化
- `BuyerPortraitService.php` - 画像服务

#### 客户档案 (profile/)
- `ClientProfile.php` - 客户档案
- `ClientProfileFormatter.php` - 档案格式化

### 📊 数据管理

#### 挖掘记录 (dig_record/)
- `AiSdrDigRecord.php` - 挖掘记录实体
- `AiSdrDigRecordFilter.php` - 挖掘记录过滤器

#### 使用记录 (usage_record/)
- `AiProductUsageRecord.php` - 产品使用记录
- `AiProductUsageRecordFilter.php` - 使用记录过滤器

### ⚙️ 配置文件

#### [config/workflow.yaml](./config/workflow.yaml) - 状态机配置
- 状态定义
- 转换规则
- 流程控制

### 🔄 异步任务 (jobs/)
- `AiSdrLiveBuyerProfileJob.php` - 买家画像任务
- `AiSdrLiveSellerProfileJob.php` - 卖家画像任务
- `DeliveryFrontedStageDetailsJob.php` - 交付阶段任务

## 快速导航

### 🚀 新手入门
1. 阅读 [README.md](./README.md) 的"概述"和"系统架构"部分
2. 查看 [QUICK_REFERENCE.md](./QUICK_REFERENCE.md) 的"核心概念"
3. 运行示例代码理解基本用法

### 🔍 开发指南
1. **创建新任务**: 参考 README.md 的"任务创建流程"
2. **状态机扩展**: 查看 SdrDetailExecutor.php 和 workflow.yaml
3. **AI服务集成**: 参考 README.md 的"AI服务集成"部分
4. **数据库操作**: 学习 xiaoman/orm 框架使用模式

### 🐛 问题排查
1. 查看 [QUICK_REFERENCE.md](./QUICK_REFERENCE.md) 的"调试技巧"
2. 参考 README.md 的"故障排查"部分
3. 检查日志和Redis状态

### 📈 性能优化
1. 阅读 [QUICK_REFERENCE.md](./QUICK_REFERENCE.md) 的"性能优化"
2. 参考 README.md 的"批量操作"和"缓存策略"
3. 监控关键指标

## 架构图表

### 系统架构图
展示了AI SDR系统的整体架构，包括用户界面层、业务服务层、AI服务层、数据访问层和基础设施层的关系。

### 状态机流程图
详细描述了潜客从添加到高价值线索的完整状态转换流程，包括各个状态之间的转换条件。

### 数据流图
展示了数据在AI SDR系统中的流向，从输入到处理再到输出的完整过程。

## 版本信息

- **当前版本**: v1.1
- **最后更新**: 2024年1月
- **维护状态**: 活跃开发中

## 贡献指南

### 文档更新
1. 修改相应的.md文件
2. 更新INDEX.md中的相关链接
3. 确保代码示例的准确性

### 代码贡献
1. 遵循现有的代码风格
2. 添加适当的注释和文档
3. 更新相关的测试用例

## 联系方式

如有问题或建议，请联系：
- 技术负责人: [技术团队]
- 文档维护: [文档团队]

## 相关资源

### 内部文档
- [xiaoman/orm框架文档](../../../vendor/xiaoman/orm/README.md)
- [队列系统文档](../../queue_v2/README.md)
- [AI Agent文档](../../ai_agent/README.md)

### 外部依赖
- [Symfony Workflow](https://symfony.com/doc/current/workflow.html)
- [PostgreSQL文档](https://www.postgresql.org/docs/)
- [Redis文档](https://redis.io/documentation)

---

**注意**: 本文档会随着系统的发展持续更新，建议定期查看最新版本。
