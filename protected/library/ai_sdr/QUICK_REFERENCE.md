# AI SDR 快速参考指南

## 核心概念

### 任务状态
- `0` - 草稿 (DRAFT)
- `1` - 处理中 (PROCESSING) 
- `2` - 暂停 (PAUSED)
- `3` - 已完成 (FINISHED)

### 任务阶段
- `0` - 挖掘 (DIG)
- `1` - 可触达 (REACHABLE)
- `2` - 营销 (MARKETING)
- `3` - 有效 (EFFECTIVE)
- `4` - 高价值 (HIGHVALUE)

### 详情状态
- `0` - 添加到潜客池 (ADD)
- `1` - 潜客分层 (LABEL)
- `2` - 开始背调 (BACKGROUND_CHECKING)
- `3` - 背调完成 (BACKGROUND_CHECKED)
- `4` - 校验联系方式 (VALIDATE_CONTACTS)
- `5` - 指定营销计划 (CREATE_MARKETING_PLAN)
- `6` - 执行营销 (EXECUTE_MARKETING_PLAN)
- `-1` - 错误 (ERROR)

## 常用类和方法

### 1. 任务管理

```php
// 创建任务服务
$service = new AISdrService($clientId, $userId);

// 获取任务列表
$tasks = $service->getTaskList();

// 处理任务
$service->processTask($taskId);

// 创建任务
$task = new AiSdrTask($clientId);
$task->source = Constant::TASK_SOURCE_AI_SDR;
$task->end_stage = Constant::AI_SDR_STAGE_MARKETING;
$task->create();
```

### 2. 查询过滤

```php
// 任务查询
$filter = new AiSdrTaskFilter($clientId);
$filter->task_status = new NotEqual(Constant::AI_SDR_TASK_STATUS_PAUSED);
$filter->order('create_time', 'desc');
$tasks = $filter->find();

// 详情查询
$detailFilter = new AiSdrTaskDetailFilter($clientId);
$detailFilter->task_id = $taskId;
$detailFilter->status = new In([0, 1, 2]);
$details = $detailFilter->find();
```

### 3. 批量操作

```php
// 批量更新详情
$batchDetail = new BatchAiSdrTaskDetail($clientId);
$batchDetail->getOperator()->batchUpdate([
    $detailId1 => ['status' => 1],
    $detailId2 => ['status' => 2]
]);

// 批量创建记录
$batchRecord = new BatchAiSdrTaskRecord($clientId);
$batchRecord->initFromData($recordData);
$batchRecord->getOperator()->create();
```

### 4. 状态机操作

```php
// 创建执行器
$executor = new SdrDetailExecutor($clientId, $userId);
$executor->setTask($task);
$executor->setClientProfile($clientProfile);

// 处理状态转换
$detailLeads = SdrLeadDetail::initFromArray($clientId, $detailList);
$executor->process($detailLeads, Constant::DETAIL_STATUS_LABEL);
```

## 常用常量

### 任务来源
```php
Constant::TASK_SOURCE_SYSTEM = -1;    // 系统预置
Constant::TASK_SOURCE_AI_SDR = 1;     // AI SDR自动挖掘
Constant::TASK_SOURCE_IMPORT = 2;     // 手动导入
Constant::TASK_SOURCE_CRM_EP = 3;     // 双保效
```

### 潜客质量
```php
Constant::LEAD_QUALITY_UNKNOWN = 0;   // 未知
Constant::LEAD_QUALITY_LOW = 1;       // 低质量
Constant::LEAD_QUALITY_MEDIUM = 2;    // 中等质量
Constant::LEAD_QUALITY_HIGH = 3;      // 高质量
```

### 记录类型
```php
Constant::RECORD_TYPE_ADD_LEAD = 0;           // 添加线索
Constant::RECORD_TYPE_BACKGROUND_CHECK = 1;   // 背景调研
Constant::RECORD_TYPE_ANALYZE_QUALITY = 2;    // 质量分析
Constant::RECORD_TYPE_CHECK_CONTACTS = 3;     // 校验联系人
Constant::RECORD_TYPE_CREATE_MARKET_PLAN = 4; // 创建营销计划
Constant::RECORD_TYPE_EXECUTE_MARKET_PLAN = 5; // 执行营销计划
```

## 缓存键模式

```php
// 任务锁
sprintf(Constant::REDIS_CACHE_TASK_KEY, $clientId, $taskId)
// "sdr:task:{client_id}:{task_id}"

// 每日限制
sprintf(Constant::TASK_DAILY_LIMIT_CACHE_KEY, $clientId, $taskId)
// "sdr:task:limit:{client_id}:{task_id}"

// 挖掘处理锁
sprintf(Constant::REDIS_AI_SDR_TASK_DIG_PROCESSING_KEY, $clientId, $taskId)
// "sdr:task:dig_processing:{client_id}:{task_id}"
```

## 队列任务

```php
// 处理任务
$job = new AiSdrProcessTaskJob($clientId, $taskId);
QueueService::dispatch($job);

// 挖掘任务
$job = new AiSdrDigTaskJob($clientId, $taskId);
QueueService::dispatch($job);

// 质量分析
$job = new AiSdrLeadQualityAnalyzeJob($clientId, $detailId);
QueueService::dispatch($job);
```

## AI服务调用

```php
// 质量分析
$agent = new SdrLeadQualityAnalysisAgent($clientId, $userId);
$result = $agent->process([
    'client_profile' => json_encode($clientProfile),
    'buyer_profile' => json_encode($buyerProfile)
]);

// 营销内容生成
$agent = new SdrEdmWriteAgent($clientId, $userId);
$emailContents = $agent->process([
    'client_profile' => json_encode($clientProfile),
    'buyer_profile' => json_encode($buyerProfile)
]);

// 背景调研
$aiBackgroundService = new AiBackgroundService();
$result = $aiBackgroundService->createTask($clientId, $userId, $referType, $detailId, $params);
```

## 常见操作

### 创建线索
```php
$leadAutoArchive = new LeadAutoArchive($clientId, $userId);
$leads = $leadAutoArchive->archiveByBatchDomain([$domain], true);
$lead = $leads[$domain] ?? false;
```

### 添加标签
```php
AISdrService::addSdrTagToLead($lead, $task, $leadQuality);
```

### 更新统计
```php
AISdrService::updateStatTotal($clientId, $taskId, $increment);
```

### 发送消息
```php
$message = new Text($clientId, $taskId);
$message->setText('消息内容');
$message->create();
```

## 调试技巧

### 查看任务状态
```sql
SELECT task_id, current_stage, task_status, update_time 
FROM ai_sdr_task 
WHERE client_id = ? AND task_id = ?;
```

### 查看详情分布
```sql
SELECT stage, status, COUNT(*) as count
FROM ai_sdr_task_detail 
WHERE task_id = ? 
GROUP BY stage, status 
ORDER BY stage, status;
```

### 查看处理记录
```sql
SELECT type, COUNT(*) as count, 
       AVG(EXTRACT(EPOCH FROM (executed_time - estimate_time))) as avg_delay
FROM ai_sdr_task_record 
WHERE task_id = ? AND executed_time IS NOT NULL
GROUP BY type;
```

### 清理Redis锁
```bash
redis-cli DEL "sdr:task:{client_id}:{task_id}"
redis-cli DEL "sdr:task:dig_processing:{client_id}:{task_id}"
```

### 查看日志
```bash
tail -f /var/log/ai_sdr.log | grep "task_id:{task_id}"
tail -f /var/log/ai_sdr.log | grep "client_id {client_id}"
```

## 性能优化

### 批量查询
```php
// 避免N+1查询
$filter->select(['id', 'task_id', 'lead_id', 'status']);
$details = $filter->find();

// 预加载关联数据
$leadIds = array_column($details->getAttributes(), 'lead_id');
$leads = $this->loadLeadsByIds($leadIds);
```

### 分页处理
```php
$pageSize = 100;
$offset = 0;

do {
    $filter->limit($pageSize)->offset($offset);
    $batch = $filter->find();
    
    if ($batch->count() > 0) {
        $this->processBatch($batch);
        $offset += $pageSize;
    }
} while ($batch->count() == $pageSize);
```

### 缓存使用
```php
$cacheKey = "sdr:task:stats:{$clientId}:{$taskId}";
$stats = $redis->get($cacheKey);

if (!$stats) {
    $stats = $this->calculateStats($taskId);
    $redis->setex($cacheKey, 300, json_encode($stats)); // 5分钟缓存
}
```

## 错误处理

### 常见异常
- `RuntimeException` - 业务逻辑错误
- `AiAgentException` - AI服务调用失败
- `ConvertException` - 数据转换错误

### 错误恢复
```php
try {
    $this->processDetail($detail);
} catch (AiAgentException $e) {
    // AI服务异常，标记为需要重试
    $this->markForRetry($detail, $e->getMessage());
} catch (\Exception $e) {
    // 其他异常，标记为错误状态
    $this->markAsError($detail, $e->getMessage());
}
```

## 监控指标

### 关键指标
- 任务处理速度: 每小时处理的潜客数量
- 成功率: 成功处理的潜客比例
- AI服务响应时间: 平均响应时间
- 队列积压: 待处理任务数量

### 告警阈值
- 错误率 > 5%
- 平均处理时间 > 30秒
- 队列积压 > 1000个任务
- AI服务超时 > 30秒
