<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2018/3/19
 * Time: 下午4:26
 */

namespace common\library\account;

use AlibabaCloud\Vcs\V20200515\VcsApiResolver;
use common\components\CacheObject;
use common\library\CommandRunner;
use common\library\history\setting\ItemSettingHistoryBuilder;
use common\library\history\setting\ItemSettingHistoryCompare;
use common\library\performance\PerformanceSetting;
use common\library\privilege_v3\AmesHelper;
use common\library\privilege_v3\ClientPrivilegeService;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\setting\library\origin\OriginMetadata;
use common\library\version\ClientModuleVersion;
use common\library\version\Constant;
use CustomerOptionService;
use LogUtil;
use Throwable;
use User;

/**
 * Class Client
 * @package common\library\account
 * @property string $client_id
 * @property string $name
 * @property string $full_name
 * @property string $homepage
 * @property string $logo
 * @property string $address
 * @property string $tel
 * @property string $fax
 * @property string $email
 * @property string $linkman
 * @property string $country
 * @property string $province
 * @property string $city
 * @property string $category_ids
 * @property string $more_info
 * @property string $master_account
 * @property string $valid_from
 * @property string $valid_to
 * @property integer $is_vip
 * @property integer $client_type
 * @property integer $max_customer_count
 * @property string $user_num
 * @property string $user_coordination_num
 * @property integer $edm_count
 * @property integer $edm_used_count
 * @property string $disk_space
 * @property string $used_space
 * @property string $comment
 * @property string $business_license
 * @property string $taxpayer_id
 * @property integer $mysql_set_id
 * @property integer $mongo_set_id
 * @property integer $pgsql_set_id
 * @property string $create_time
 * @property integer $create_user
 * @property integer $create_by
 * @property string $update_time
 * @property integer $update_user
 * @property integer $status
 * @property integer $enable_flag
 * @property string $customer_hide_query_fields
 * @property integer $hide_freeze_user_flag
 * @property integer $forbidden_flag
 * @property integer $activated_flag
 * @property string $activated_time
 * @property integer $hr_flag
 * @property string $products
 * @property string $version
 * @property string $translated_name
 * @property string $dc_id
 * @property integer $is_oversea
 * @property integer $currency_id
 * @property string $is_backup
 * @property int $eid
 * @property string $use_zone
 */
class Client extends CacheObject
{
    const CACHE_PREFIX ='crm:table:client';
    const CACHE_EXTENT_ATTR_PREFIX ='crm:extent_attr:client';
    const CACHE_SETTING_ATTR_PREFIX ='crm:setting_attr:client';
    const CACHE_EXP_CLIENT ='crm:exp:client';
    const CACHE_SETTING_SEARCH_PREFIX = 'crm:setting_search:client';

    const CACHE_KA_INFO_CLIENT_PREFIX = 'crm:ka_info:client';
    const CACHE_ADVISER_INFO_CLIENT_PREFIX = 'crm:adviser:info:client';

    const CACHE_PREFIX_CAN_DELETE_BY_INTERNAL_LIST = [
      self::CACHE_ADVISER_INFO_CLIENT_PREFIX
    ];

    const USE_ZONE_CN = 'CN';
    const USE_ZONE_TW = 'TW';
    const USE_ZONE_HK = 'HK';
    const USE_ZONE_JP = 'JP';

    protected static $cacheClient = [];
    protected static $cachePrivilege = [];
    protected static $cacheExpClientIds;

    const EXTERNAL_KEY_USED_USER_NUM = "client_used_num";//已用账号数
    const EXTERNAL_KEY_USED_USER_NUM_CRM = "client_used_num_crm";//crm已用账号数
    const EXTERNAL_KEY_USED_USER_NUM_OKKI_SHOP = "client_used_num_okki_shop";//okki_shop已用账号数
    const EXTERNAL_KEY_CONTRACT_VERSION = 'client_contract_version';//合同版本
    const EXTERNAL_KEY_CUSTOMER_SHOW_PUBLIC_CONTACT ='customer_public_contact'; //是否显示公海联系人
    /**
     * @deprecated
     */
    const EXTERNAL_KEY_CUSTOMER_EDIT_PUBLIC ='customer_edit_public'; //是否允许编辑公海联系人
    const EXTERNAL_KEY_CRM_VERSION = 'crm_version_id';  //crm 版本字段  //线上client没维护好该key，使用需要做一些兼容
    const EXTERNAL_KEY_SHOP_VERSION = 'shop_version_id'; // shop 版本字段
    const EXTERNAL_KEY_MAIL_VERSION = 'mail_version_id'; //邮件版本字段
    const EXTERNAL_KEY_RELATIVE_USER ='relative_user'; //关联账号功能使用情况. 1未使用 2:使用未超量 3: 使用已超量 4: 新开通的client
    const EXTERNAL_KEY_DISK_OVER_LIMIT_BUFFER_DATE = 'disk_over_limit_buffer_date'; //空间超量缓冲日期
    const EXTERNAL_KEY_DISK_CLOUD_USED_SPACE = 'disk_cloud_used_space'; //云空间使用空间
    const EXTERNAL_KEY_MAIL_ATTACH_USED_SPACE = 'disk_mail_attach_used_space'; // 云空间邮件附件使用空间
    const EXTERNAL_KEY_CLOUD_SPACE_CALCULATE_DATETIME = 'disk_cloud_space_calculate_datetime'; // 云空间最新计算时间
    const EXTERNAL_KEY_DISK_CLOUD_USED_PUBLIC_SPACE = 'disk_cloud_used_public_space';   //云盘-共享文档
    const EXTERNAL_KEY_TRAIL_TYPE_SWITCH = 'trail_type_switch'; // 动态跟进类型开关（世绑公司专用）
    const EXTERNAL_KEY_IMPORT_ADDITION_USER = 'import_addition_user'; // 导入客户允许指定用户（世绑公司专用）
    const EXTERNAL_KEY_OPPORTUNITY_SWITCH = 'opportunity_switch'; // 是否启动商机
    const EXTERNAL_KEY_CUSTOMER_POOL_SWITCH = 'customer_pool_switch'; //是否启用公海分组
    const EXTERNAL_KEY_ORDER_SWITCH = 'order_switch'; // 订单开关，
    const EXTERNAL_KEY_QUOTATION_SWITCH = 'quotation_switch'; // 单据开关，
    const EXTERNAL_KEY_PURCHASE_BY_ORDER_SWITCH = 'purchase_by_order_switch';//以销定购开关
    const EXTERNAL_KEY_IMPORTED_FROM_FU_TONG = 'imported_from_fu_tong';//是否已经从ft迁移来客户
    const EXTERNAL_KEY_SHUFFLING_FIGURE='shuffling_figure';//轮播图
    const EXTERNAL_KEY_GROUP_DISABLED = 'customer_group_disabled';//客群迭代后新签客户关闭分组功能，原客户默认开启
    const EXTERNAL_KEY_COMPANY_SWARM = 'company_swarm';//客群功能,默认不开启
    const EXTERNAL_KEY_USE_NEW_PUBLIC_RULE = 'public_rule';//客群功能,默认不开启
    const EXTERNAL_KEY_COMPANY_SWARM_SWITCH = 'company_swarm_switch';//是否支持新旧客户切换，默认支持
    const EXTERNAL_KEY_SALES_GUIDE_SWITCH = 'sales_guide_switch';//成单指南开关,默认不开启
    const EXTERNAL_KEY_FACEBOOK_SWITCH = 'facebook_switch';//facebook,默认不开启
    const EXTERNAL_KEY_WECOM_SWITCH = 'wecom_switch';//wecom,企业微信,默认不开启
    const EXTERNAL_KEY_LEADS_V2_MSG_SWITCH = 'leads_v2_msg_switch';  // //Leads2.0（10%放量）前通知条,默认不开启
    const EXTERNAL_KEY_INIT_SALES_FLOW = 'init_sales_flow'; //成单指南销售流程初始化标识
    const EXTERNAL_KEY_SPECIAL_SETTING_FIELD_SHOW = 'special_setting_field_show'; //设置管理-特殊权限展示
    const EXTERNAL_KEY_BIND_EMAIL_NUM_LIMIT = 'bind_email_num_limit'; //绑定邮箱数量限制
    const EXTERNAL_KEY_ALLOW_BIND_REGISTERED_EMAIL = 'allow_bind_registered_email'; //是否允许绑定已注册小满邮箱
    const EXTERNAL_KEY_USER_FOLLOWUP_ADD_NUM_LIMIT = 'user_followup_add_num_limit';//用户添加跟进条数限制
    const EXTERNAL_KEY_ALLOW_BIND_GMAIL = 'allow_bind_gmail';   //是否支持绑定 gmail 邮箱
    const EXTERNAL_KEY_AMES_FIELDS_LIMIT = 'ames_fields_limit'; // ames限制展示字段
    const EXTERNAL_KEY_PER_COMPANY_MAX_USER_NUM = 'per_company_max_user_num'; //单个客户的跟进人数量上限
    const EXTERNAL_KEY_ALLOW_EDIT_FOLLOWUP_QUICKTEXT = 'allow_edit_followup_quicktext'; //是否支持自定义客户跟进快捷文本
    const EXTERNAL_KEY_AMES_AUTH_STATUS = 'ames_auth_status'; // ames授权状态
    const EXTERNAL_KEY_AMES_CAN_AUTH = 'ames_can_auth';
    const EXTERNAL_KEY_AMES_SPECIAL_CRM_SHOW_FLAG = 'ames_special_crm_show_flag';  //专用于crm和plus版本 允许展示ames授权
    const EXTERNAL_KEY_AMES_IS_ALI_BLACK = 'ames_is_ali_black'; // 是否是阿里黑名单client
    const EXTERNAL_KEY_SHOW_PAYPAL = 'show_paypal'; //菜单重构一期是否展示PayPal
    const EXTERNAL_KEY_SHOW_YHX_CUSTOMERS = 'show_yhx_customer'; //菜单重构一期是否展示报关
    const EXTERNAL_KEY_MANAGE_VIEW_SWITCH = 'manage_view_switch'; //是否有app管理视角功能
    const EXTERNAL_KEY_AI_WRITE_SWITCH = 'ai_write_switch'; //是否有AI邮件开关
    const EXTERNAL_KEY_AI_SUBJECT_SWITCH = 'ai_subject_switch'; //是否有AI群发单显邮件开关
    const EXTERNAL_KEY_AI_REPLY_SWITCH = 'ai_reply_switch'; //是否有AI回信开关
    const EXTERNAL_KEY_AI_WRITE_MODEL = 'ai_write_model'; //AI邮件服务模型
    const EXTERNAL_KEY_MAIL_SEARCH_V2_SWITCH = 'mail_search_v2_switch'; //AI邮件服务模型
    const EXTERNAL_KEY_AI_INSIGHT_SWITCH = 'ai_insight_switch'; // 统计分析Insight开关
    const EXTERNAL_KEY_AI_INSIGHT_MODEL = 'ai_insight_model'; // 统计分析Insight模型

    const EXTERNAL_KEY_AI_ASSISTANT_TM_SWITCH = 'ai_assistant_tm_switch'; //AI销售助手TM端开关,默认不开启

    const EXTERNAL_KEY_APP_ASSISTANT_SWITCH = 'app_assistant_switch';//app销售助手开关,默认不开启
    const EXTERNAL_KEY_WECOM_ASSISTANT_SWITCH = 'wecom_assistant_switch';//企微销售助手开关,默认不开启

    const EXTERNAL_KEY_AI_ASSISTANT_SWITCH = 'ai_assistant_switch'; // 统计分析Ai助手开关

    const EXTERNAL_KEY_CUSTOMER_DRAWER = 'customer_drawer'; //新版客户抽屉页
    const EXTERNAL_KEY_CUSTOMER_INFO = 'customer_info'; // 新版客户详情页
    const EXTERNAL_KEY_CUSTOMER_PAAS_SWITCH = 'customer_paas_switch'; // 是否启用新版Paas客户
    const EXTERNAL_KEY_WORK_JOURNAL_FUNCTION_SWITCH = 'work_journal_function_switch'; //根据该开关是否开启工作报告功能 todo 功能已经全量，后续可以逐步下架

    const EXTERNAL_KEY_ASSESS_DIMENSION_SWITCH = 'assess_dimension_switch'; // 新版评分————大客户专属权益
    const EXTERNAL_KEY_OPPORTUNITY_V2_SWITCH = 'opportunity_v2_switch'; // 新版商机
    const EXTERNAL_KEY_LEAD_V2_SWITCH = 'lead_v2_switch'; // 新版线索
    const EXTERNAL_KEY_CUSTOMER_PUBLIC_V2_SWITCH = 'customer_public_v2_switch'; // 新版公海客户
    const EXTERNAL_KEY_MAIL_CONVERSATION_FUNCTION_SWITCH = 'mail_conversation_function_switch'; //会话功能放量开关
    const EXTERNAL_KEY_MAIL_INDEXEDDB_SWITCH = 'mail_indexeddb_switch'; //邮件IndexDb功能放量开关

    const EXTERNAL_KEY_INQUIRY_DATA_SWITCH = 'inquiry_data_switch'; //询盘库功能放量开关
    const EXTERNAL_KEY_AI_COMPANY_QC = 'ai_company_qc'; // AI客户质检开关
    const EXTERNAL_KEY_AI_COMPANY_QC_PREHEAT_TIME = 'ai_company_qc_preheat_time'; //AI谈单监测冷启动次数
    const EXTERNAL_KEY_PRIVILEGE_SCOPE_USER_SWITCH = 'privilege_scope_user_switch'; //权限一期，功能放量开关
    const EXTERNAL_KEY_AI_MAIL_AUTO_SUMMARY = 'ai_mail_auto_summary';
    const EXTERNAL_KEY_AI_SDR_LEAD_ACTIVATION_ENABLE = 'ai_sdr_leads_activation_enable';
    const EXTERNAL_KEY_AI_WORKBENCH_SWITCH = 'ai_workbench_switch'; // AI工作台白名单开关


    const EXTERNAL_KEY_AI_SDR_DAILY_LIMIT = 'ai_sdr_daily_limit'; // 见 ai_sdr/Constant::DAILY_LIMIT
    const EXTERNAL_KEY_AI_SDR_TOTAL_LIMIT = 'ai_sdr_total_limit';
    const EXTERNAL_KEY_CRM_EP_AI_SDR_TOTAL_LIMIT = 'crm_ep_ai_sdr_total_limit';
    const EXTERNAL_KEY_CRM_EP_AI_SDR_DAILY_LIMIT = 'crm_ep_ai_sdr_daily_limit';

    /**
     * AI计费相关
     */

    const EXTERNAL_KEY_K_COIN_STANDARD_TOTAL_AMOUNT = 'k_coin_standard_total_amount'; // 套餐标准k币总量
    const EXTERNAL_KEY_K_COIN_STANDARD_AVAILABLE_AMOUNT = 'k_coin_standard_available_amount'; // 标准k币可用量
    const EXTERNAL_KEY_K_COIN_ADDITIONAL_AVAILABLE_AMOUNT = 'k_coin_additional_available_amount'; // 增购k币可用量
    const EXTERNAL_KEY_K_COIN_ASSIGNABLE_TOTAL_AMOUNT = 'k_coin_assignable_total_amount';  // client下可分配k币的量

    /**
     * @deprecated 会话数按月限制数量2023年6月1号废弃，老客户写const定义里
     */
    const EXTERNAL_KEY_WABA_CONVERSATION_MONTH_LIMIT = 'waba_conversation_month_limit'; //waba 会话数按月限制数量

    const EXTERNAL_KEY_PERFORMANCE_RULE_LIMIT = 'performance_rule_limit'; // 规则默认上限
    /**
     * @deprecated WABA体验官已下线
     */
    const EXTERNAL_KEY_WABA_CONVERSATION_DAY_LIMIT = 'waba_conversation_day_limit'; //waba 会话数按天限制数量
    const EXTERNAL_KEY_WABA_CHANNEL_NUM_LIMIT = 'waba_channel_num_limit'; //waba 频道限制数量
    const EXTERNAL_KEY_ERP_SERVICE_SWITCH = 'erp_service_switch'; // erp服务
    const EXTERNAL_KEY_ERP_SERVICE_NOT_SHOW = 'erp_service_not_show'; // erp服务不展示配置
    const EXTERNAL_KEY_ERP_GALAXY_SHOW = 'erp_service_galaxy_show'; // 云星空展示配置
    const EXTERNAL_KEY_MOBILE_MAIL_LIST_SWITCH = 'mobile_mail_list_switch';//是否支持新旧app同步方案，默认不支持
    const EXTERNAL_KEY_APP_MAIL_CONVERSATION_SWITCH = 'mobile_mail_conversation_switch';//是否开启邮件会话模式，默认不开启
    const EXTERNAL_KEY_COMPANY_NEW_SEARCH_SWITCH = 'customer_new_search';//客户是否开启新版搜索，默认开启
    const EXTERNAL_KEY_COMPANY_PRIVATE_LIST_SEARCH = 'customer_private_search';//客户私海列表是否使用搜索数据，默认不开启
    const EXTERNAL_KEY_COMPANY_PUBLIC_LIST_SEARCH = 'customer_public_search';//客户公海海列表是否使用搜索数据，默认不开启
    const EXTERNAL_KEY_COMPANY_PUBLIC_LIST_SEARCH_APPEND = 'customer_public_search_append';//客户公海海列表搜索数据向后不全数
    const EXTERNAL_KEY_COMPANY_SKIP_USE_ID_FLAG = 'customer_skip_user_id_flag';//客户私海列表查看全部默认不走userId筛选，默认值1
    const EXTERNAL_KEY_COMPANY_SWARM_JOIN_FLAG = 'customer_swarm_join_flag';//客户客群列表走关联表查询，默认值0
    const EXTERNAL_KEY_PUBLIC_COMPANY_SWARM_JOIN_FLAG = 'public_customer_swarm_join_flag';//公海客户客群列表走关联表查询，默认值0
    const SETTING_KEY_COMPANY_PUBLIC_REASON_REQUIRE_SWITCH          = 'customer_public_reason_require_switch';//客户移入公海原因是否必填，默认值0
    const EXTERNAL_KEY_WHATSAPP_CLOUD_NORMAL_ACCOUNT_LIMIT = 'whatsapp_cloud_normal_account_limit'; //waba 频道限制数量
    const EXTERNAL_KEY_WHATSAPP_CLOUD_MARKETING_ACCOUNT_LIMIT = 'whatsapp_cloud_marketing_account_limit'; //waha 频道限制数量

    const EXTERNAL_KEY_LEAD_NEW_SEARCH_SWITCH         = 'lead_new_search';//线索是否开启新版搜索，默认开启
    const EXTERNAL_KEY_LEAD_PRIVATE_LIST_SEARCH       = 'lead_private_search';//线索私海列表是否使用搜索数据，默认不开启
    const EXTERNAL_KEY_LEAD_PUBLIC_LIST_SEARCH        = 'lead_public_search';//线索公海海列表是否使用搜索数据，默认不开启
    const EXTERNAL_KEY_LEAD_PUBLIC_LIST_SEARCH_APPEND = 'lead_public_search_append';//线索公海海列表搜索数据向后不全数
    const EXTERNAL_KEY_LEAD_SKIP_USE_ID_FLAG          = 'lead_skip_user_id_flag';//线索私海列表查看全部默认不走userId筛选，默认值1

	const EXTERNAL_KEY_LEAD_DUPLICATE_FLAG = 'lead_duplicate_flag'; // 线索字段判重设置是否变更
    const EXTERNAL_KEY_COMPANY_DUPLICATE_FLAG = 'company_duplicate_flag'; // 客户字段判重设置是否变更

	const EXTERNAL_KEY_CUSTOMER_POOL_DUPLICATE_SWITCH = 'customer_pool_duplicate_switch'; // 客户池判重是否开启
	const EXTERNAL_KEY_CUSTOMER_FIELD_DUPLICATE_SWITCH = 'customer_field_duplicate_switch'; // 新建客户与已建档客户判重开关

    const EXTERNAL_KEY_AI_CLASSIFY_V2_SWITCH = 'ai_classify_v2_switch';  // 新版自动化
    const EXTERNAL_KEY_AI_CLASSIFY_V2_DEFAULT_LEAD = 'ai_classify_v2_default_lead';  // 新版自动化，默认线索
	const EXTERNAL_KEY_ORIGIN_SETTING_SORT_MODE = 'origin_setting_sort_mode'; //来源排序方式
    const EXTERNAL_KEY_MOBILE_FLUTTER_PRODUCT_DETAIL_SWITCH = 'mobile_flutter_product_detail_switch'; // 移动端产品详情页flutter化开关 默认开启
    const EXTERNAL_KEY_MOBILE_FLUTTER_OPPORTUNITY_EDITABLE_SWITCH = 'mobile_flutter_opportunity_editable_switch'; // 移动端商机编辑页flutter化开关 默认开启
    const EXTERNAL_KEY_BUSINESS_CARD_CLOUD_API_SWITCH = 'business_card_cloud_api_switch'; // 名片识别使用云端api开关 默认开启


	const EXTERNAL_KEY_LEAD_SWITCH = 'lead_switch'; // 线索开关，

    const EXTERNAL_KEY_ALI_MAIL_FLAG = 'ali_mail_flag'; // 是否有阿里邮箱组织是否开通, 0-无 1-未配置 2-已配置 3-已过期 4-系统默认配置(用户视角看是未配置)
    const EXTERNAL_KEY_MARKETING_AUTOMATION_DIG_TASK_LIMIT = 'marketing_automation_dig_task_limit'; // 营销自动化挖掘任务数量限制

    const EXTERNAL_KEY_WHATSAPP_CLOUD_FUNCTIONAL_REMOVE_TIME = 'whatsapp_cloud_functional_remove_time'; //云端权限移除时间

    protected static $defaultValue = [
        self::EXTERNAL_KEY_CUSTOMER_SHOW_PUBLIC_CONTACT => '1',
        self::SETTING_KEY_CURRENCY                      => 'CNY',
        self::EXTERNAL_KEY_TRAIL_TYPE_SWITCH            => '0',
        self::EXTERNAL_KEY_IMPORT_ADDITION_USER         => '0',
        self::EXTERNAL_KEY_CUSTOMER_POOL_SWITCH         => '0',
        self::EXTERNAL_KEY_OPPORTUNITY_SWITCH           => '1',
        self::EXTERNAL_KEY_ORDER_SWITCH                 => '1',
        self::EXTERNAL_KEY_QUOTATION_SWITCH             => '1',
        self::SETTING_KEY_ORDER_ALLOW_EXPORT_DRAFT      => '1',
        self::SETTING_KEY_PURCHASE_ORDER_ALLOW_EXPORT_DRAFT      => '1',
        self::SETTING_KEY_STATISTICS_VIEW_RANGE_SETTINGS_SWITCH => '0',
        self::SETTING_KEY_PDCA_START_MONTH => '1',
        self::EXTERNAL_KEY_PURCHASE_BY_ORDER_SWITCH => '1',
        self::SETTING_KEY_ORDER_EXPORT_FILENAME => '0',
        self::EXTERNAL_KEY_GROUP_DISABLED => '0',
        self::EXTERNAL_KEY_SHUFFLING_FIGURE => '0',
        self::EXTERNAL_KEY_COMPANY_SWARM => '0',
        self::SETTING_KEY_COMPANY_MOVE_TO_PUBLIC_IGNORE_FROZEN_USER => '1', // 默认开启
        self::SETTING_KEY_COMPANY_MOVE_TO_PUBLIC_NOTIFY_DAYS => '7',
        self::EXTERNAL_KEY_USE_NEW_PUBLIC_RULE => '0',
        self::EXTERNAL_KEY_COMPANY_SWARM_SWITCH => '1',
        self::EXTERNAL_KEY_SALES_GUIDE_SWITCH => '0',
        self::EXTERNAL_KEY_INIT_SALES_FLOW  => '1',
        self::EXTERNAL_KEY_BIND_EMAIL_NUM_LIMIT => 10,
        self::EXTERNAL_KEY_ALLOW_BIND_REGISTERED_EMAIL => 0,
        self::EXTERNAL_KEY_USER_FOLLOWUP_ADD_NUM_LIMIT => '0',
        self::EXTERNAL_KEY_ALLOW_BIND_GMAIL => 1,
        self::EXTERNAL_KEY_PER_COMPANY_MAX_USER_NUM => '0',
        self::EXTERNAL_KEY_SPECIAL_SETTING_FIELD_SHOW => 1,
        self::EXTERNAL_KEY_ALLOW_EDIT_FOLLOWUP_QUICKTEXT => '1',
        self::EXTERNAL_KEY_MANAGE_VIEW_SWITCH => '1',
        self::EXTERNAL_KEY_AI_WRITE_SWITCH => '0',
        self::EXTERNAL_KEY_AI_SUBJECT_SWITCH => '0',
        self::EXTERNAL_KEY_AI_REPLY_SWITCH => '0',
        self::EXTERNAL_KEY_MAIL_SEARCH_V2_SWITCH => '0',
        self::EXTERNAL_KEY_MOBILE_MAIL_LIST_SWITCH => '0',
        self::EXTERNAL_KEY_CUSTOMER_DRAWER => '1',
        self::EXTERNAL_KEY_WABA_CHANNEL_NUM_LIMIT => '1',
        self::EXTERNAL_KEY_CUSTOMER_INFO => '0',
        self::EXTERNAL_KEY_CUSTOMER_PAAS_SWITCH => '0',
        self::SETTING_KEY_WHATSAPP_MARKETING => '{}',
        self::SETTING_KEY_MARKETING_I18N => '{}',
        self::SETTING_KEY_CUSTOMER_POOL_MOVE_PRIVATE_OPPORTUNITY => '0',
        self::EXTERNAL_KEY_COMPANY_NEW_SEARCH_SWITCH => '1',
        self::EXTERNAL_KEY_COMPANY_PUBLIC_LIST_SEARCH => '0',
        self::EXTERNAL_KEY_COMPANY_PRIVATE_LIST_SEARCH => '0',
        self::EXTERNAL_KEY_COMPANY_PUBLIC_LIST_SEARCH_APPEND => 100,
        self::EXTERNAL_KEY_COMPANY_SKIP_USE_ID_FLAG => '1',
        self::EXTERNAL_KEY_ASSESS_DIMENSION_SWITCH => '0',
        self::EXTERNAL_KEY_COMPANY_SWARM_JOIN_FLAG => '0',
        self::EXTERNAL_KEY_PUBLIC_COMPANY_SWARM_JOIN_FLAG => '0',
        self::EXTERNAL_KEY_PERFORMANCE_RULE_LIMIT => 20,
        self::EXTERNAL_KEY_OPPORTUNITY_V2_SWITCH => '0',
        self::SETTING_KEY_FOLLOWUP_TIME => '0',//默认关闭
        self::EXTERNAL_KEY_MAIL_CONVERSATION_FUNCTION_SWITCH => '1',//默认开启
        self::EXTERNAL_KEY_APP_MAIL_CONVERSATION_SWITCH => '0',
        self::EXTERNAL_KEY_MAIL_INDEXEDDB_SWITCH => '1',
        self::SETTING_KEY_DESKTOP_EXPOSE_MAIL_LIMIT => 0,
        self::EXTERNAL_KEY_LEAD_NEW_SEARCH_SWITCH => '1',
        self::EXTERNAL_KEY_LEAD_PRIVATE_LIST_SEARCH => '0',
        self::EXTERNAL_KEY_LEAD_PUBLIC_LIST_SEARCH => '0',
        self::EXTERNAL_KEY_LEAD_PUBLIC_LIST_SEARCH_APPEND => 100,
        self::EXTERNAL_KEY_LEAD_SKIP_USE_ID_FLAG => '1',
        self::SETTING_KEY_COMPANY_PUBLIC_REASON_REQUIRE_SWITCH => '0',
        self::EXTERNAL_KEY_APP_ASSISTANT_SWITCH => '0',
        self::EXTERNAL_KEY_LEAD_V2_SWITCH => '0',
        self::EXTERNAL_KEY_CUSTOMER_PUBLIC_V2_SWITCH => '0',
        self::EXTERNAL_KEY_K_COIN_STANDARD_TOTAL_AMOUNT => 0,
        self::EXTERNAL_KEY_K_COIN_STANDARD_AVAILABLE_AMOUNT => 0,
        self::EXTERNAL_KEY_K_COIN_ADDITIONAL_AVAILABLE_AMOUNT => 0,
        self::EXTERNAL_KEY_K_COIN_ASSIGNABLE_TOTAL_AMOUNT=> 0,
		self::EXTERNAL_KEY_LEAD_DUPLICATE_FLAG => 0,
		self::EXTERNAL_KEY_COMPANY_DUPLICATE_FLAG => 0,
        self::EXTERNAL_KEY_CLOUD_SPACE_CALCULATE_DATETIME => '',
        self::EXTERNAL_KEY_ALI_MAIL_FLAG => 0,
        self::SETTING_KEY_LEADS_SWITCH => 0,
		self::EXTERNAL_KEY_CUSTOMER_POOL_DUPLICATE_SWITCH => 0,
		self::EXTERNAL_KEY_CUSTOMER_FIELD_DUPLICATE_SWITCH => '1',
        self::EXTERNAL_KEY_AI_CLASSIFY_V2_SWITCH => '1',
        self::EXTERNAL_KEY_AI_CLASSIFY_V2_DEFAULT_LEAD => 0,
		self::EXTERNAL_KEY_ORIGIN_SETTING_SORT_MODE => OriginMetadata::CLIENT_SORT_MODE,
		self::EXTERNAL_KEY_LEAD_SWITCH => 0,
		self::EXTERNAL_KEY_INQUIRY_DATA_SWITCH => 1,
        self::EXTERNAL_KEY_MOBILE_FLUTTER_PRODUCT_DETAIL_SWITCH => 1,
        self::EXTERNAL_KEY_MOBILE_FLUTTER_OPPORTUNITY_EDITABLE_SWITCH => 1,
        self::EXTERNAL_KEY_PRIVILEGE_SCOPE_USER_SWITCH => 1,
        self::EXTERNAL_KEY_BUSINESS_CARD_CLOUD_API_SWITCH => 1,
        self::EXTERNAL_KEY_WHATSAPP_CLOUD_FUNCTIONAL_REMOVE_TIME => '',
        self::EXTERNAL_KEY_AI_SDR_LEAD_ACTIVATION_ENABLE=>0,
        self::EXTERNAL_KEY_AI_SDR_DAILY_LIMIT => \common\library\ai_sdr\Constant::DAILY_LIMIT,
        self::EXTERNAL_KEY_AI_SDR_TOTAL_LIMIT => \common\library\ai_sdr\Constant::TOTAL_LIMIT_UNLIMITED,
        self::EXTERNAL_KEY_CRM_EP_AI_SDR_TOTAL_LIMIT => \common\library\ai_sdr\Constant::TOTAL_LIMIT_FOR_FREE_WITH_LITE,
        self::EXTERNAL_KEY_CRM_EP_AI_SDR_DAILY_LIMIT => \common\library\ai_sdr\Constant::DAILY_LIMIT_FOR_LITE_FREE,
    ];


    protected static $arrayValue = [
        self::SETTING_KEY_ORDER_PERFORMANCE_FIELD,
        self::SETTING_KEY_OPPORTUNITY_PERFORMANCE_FIELD,
        self::SETTING_KEY_CASH_COLLECTION_PERFORMANCE_FIELD,
        self::SETTING_KEY_PERFORMANCE_SETTING,
        self::SETTING_KEY_MARKETING_IGNORE_AI_USER_MAIL_LIST,
        self::SETTING_KEY_PDCA_TEAM_WALL_SETTING,
        self::SETTING_KEY_CUSTOMER_HIDE_QUERY_FIELDS,
        self::SETTING_KEY_EXAMPLE_COMPANY_ID,
        self::SETTING_KEY_PUBLIC_POOL_WHITE_LIST,
        self::SETTING_KEY_MARKETING_I18N,
        self::SETTING_KEY_ALI_TRANSLATE_MAP,
        self::SETTING_KEY_CLIENT_TRANSLATE_SWITCH_MAP,
        self::SETTING_KEY_CLIENT_CUSTOMER_LIMIT_CONFIG,
    ];

    public static $privilegeMap = [
        \common\library\account\Client::SETTING_KEY_CURRENCY => PrivilegeConstants::PRIVILEGE_SETTING_EXCHANGE_MANAGE,
        self::SETTING_KEY_CUSTOM_LOGIN_PAGE_FIELD => PrivilegeConstants::PRIVILEGE_CUSTOM_LOGIN_PAGE,
        self::SETTING_KEY_WHATSAPP_MARKETING => PrivilegeConstants::PRIVILEGE_CRM_WABA_MANAGE_MARKETING,
        self::SETTING_KEY_MARKETING_I18N => PrivilegeConstants::PRIVILEGE_MARKETING_SETTING_I18N,
    ];

    public static $functionalMap = [
        \common\library\account\Client::SETTING_KEY_ORDER_PERFORMANCE_FIELD => PrivilegeConstants::FUNCTIONAL_PERFORMANCE_ORDER_CUSTOM_FIELD,
        \common\library\account\Client::EXTERNAL_KEY_OPPORTUNITY_SWITCH => PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
    ];

    protected $_extentAttributes;
    protected $_oldExtentAttributes;

    const SETTING_KEY_EDM_DAY_FOR_PREVENT_HARASS= 'setting_edm_day_for_prevent_harass'; //内已被发送过营销邮件的联系人不再发送
    const SETTING_KEY_EDM_PREVENT_CUSTOMER_CONFLICT = 'setting_edm_prevent_customer_conflict';//不发送给冲突客户联系人
    const SETTING_KEY_EDM_CLIENT_PROFILE = 'setting_edm_client_profile';//公司的基本信息及联系方式，用于营销邮件主题模板的内容填充
    const SETTING_KEY_EDM_FREEZE_FLAG = 'setting_edm_client_freeze_flag'; //os发起退款将通知crm设置冻结标志，冻结edm使用
    const SETTING_KEY_CUSTOMER_POOL_MOVE_PRIVATE_DAY = 'setting_customer_pool_move_private_day';//业务员捞取公海客户时间限制
    const SETTING_KEY_CUSTOMER_DYNAMIC_TRAIL = 'setting_customer_dynamic_trail'; // 客户动态配置开关
    const REDIS_SETTING_SEARCH_ADVANCE= "statistic_cache_key"; //商机销售推进转化
    const REDIS_SETTING_CMS_ROAS = "cms_roas_cache_key"; //roas商机缓存
    const REDIS_SETTING_SEARCH_MAP = [self::REDIS_SETTING_SEARCH_ADVANCE, self::REDIS_SETTING_CMS_ROAS];
    const SETTING_KEY_PRODUCT_EXPORT_LANGUAGE = 'setting_product_export_language';//导出产品pdf语音设置
    const SETTING_KEY_CURRENCY ='client_currency'; //客户主货币，目前支持 CNY、USD
    const SETTING_KEY_OPPORTUNITY_PERFORMANCE_FIELD = 'setting_opportunity_performance_field'; // 统计口径字段
    const SETTING_KEY_ORDER_PERFORMANCE_FIELD = 'setting_order_performance_field'; // 统计口径字段
    const SETTING_KEY_CASH_COLLECTION_PERFORMANCE_FIELD = 'setting_cash_collection_performance_field'; // 统计口径字段
    const SETTING_KEY_PERFORMANCE_SETTING = 'setting_performance_setting'; // 业绩设置
    const SETTING_KEY_CUSTOMER_DEAL_TIME_UPDATE_TYPE = 'setting_customer_deal_time_update_type'; // 客户最近成交日期字段更新设置
    const SETTING_KEY_PRODUCT_IMPORT_UPDATE_TYPE = 'setting_product_import_update_type'; // 阿里导入产品更新类型 0 不更新 1 更新
    const SETTING_KEY_CMS_PRODUCT_IMPORT_UPDATE_TYPE = 'setting_cms_product_import_update_type'; // cms系统阿里导入产品更新类型 0 不更新 1 更新
    const SETTING_KEY_PRODUCT_ALI_REMOVE_DUPLICATE_SWITCH = 'setting_product_ali_remove_duplicate_switch'; //阿里导入产品，一键去重
    const SETTING_KEY_PRODUCT_GLOBAL_REMOVE_DUPLICATE_SWITCH = 'setting_product_global_remove_duplicate_switch'; //环球资源导入产品，一键去重
    const SETTING_KEY_COMPANY_SUBGROUP_MUST_SELECT_SWITCH = 'setting_key_company_subgroup_must_select_switch'; //
    const SETTING_KEY_ORDER_ALLOW_EXPORT_DRAFT = 'setting_order_export_allow_draft';//是否允许导出状态是草稿的订单 0:否、 1:是、 null:是
    const SETTING_KEY_PURCHASE_ORDER_ALLOW_EXPORT_DRAFT = 'setting_purchase_order_export_allow_draft';//是否允许导出状态是草稿的采购订单 0:否、 1:是、 null:是
    const SETTING_KEY_MARKETING_IGNORE_AI_USER_MAIL_LIST = 'marketing_ignore_ai_user_mail_id'; // marketing联系邮箱，关闭ai自动话
    const SETTING_KEY_STATISTICS_VIEW_RANGE_SETTINGS_SWITCH = 'setting_key_statistics_view_range_settings_switch'; // 统计分析查看范围设置
    const SETTING_KEY_PDCA_START_MONTH = 'setting_key_pdca_start_month'; //pdca起始月份设置
    const SETTING_KEY_PDCA_TEAM_WALL_SETTING = 'setting_key_pdca_team_wall_setting'; // 团队墙设置
    const SETTING_KEY_ALI_SALES_RACE = 'setting_key_ali_sales_race'; // 阿里圈战活动设置
    const SETTING_KEY_ALI_SYNC_SERVICE_ACCESS = 'setting_key_ali_sync_service_access'; // 是否开启阿里数据同步授权服务
    const SETTING_KEY_ORDER_EXPORT_FILENAME = 'setting_order_export_filename'; // 订单导出默认文件名设置
    const SETTING_KEY_CUSTOMER_HIDE_QUERY_FIELDS = 'setting_customer_hide_query_fields'; // 客户设置 - 客户查重设置
    const SETTING_KEY_EXAMPLE_COMPANY_ID = 'setting_example_company_id'; // 实例客户ID列表
    const SETTING_KEY_COMPANY_MOVE_TO_PUBLIC_IGNORE_FROZEN_USER = 'setting_move_public_ignore_frozen_user'; // 移入公海跳过冻结用户
    const SETTING_KEY_COMPANY_MOVE_TO_PUBLIC_NOTIFY_DAYS = 'setting_move_public_notify_days'; // 移入公海跳过冻结用户
    const SETTING_KEY_CUSTOM_LOGIN_PAGE_FIELD = "setting_custom_login_page";
    const SETTING_KEY_PUBLIC_POOL_WHITE_LIST = 'setting_public_pool_white_list';
    const SETTING_KEY_CUSTOMER_POOL_MOVE_PRIVATE_OPPORTUNITY = 'setting_customer_pool_move_private_opportunity';
    const SETTING_KEY_WHATSAPP_MARKETING = 'setting_whatsapp_marketing'; // WABA营销设置
    const PERFORMANCE_V2_DISPLAY_CONFIG = 'performance_v2_display_config'; //目标完成情况设置人员不显示
    const SETTING_KEY_FOLLOWUP_TIME = 'setting_followup_time';

    const SETTING_KEY_MARKETING_I18N = 'setting_marketing_i18n'; // marketing多语种设置

    const SETTING_KEY_ORDER_LINK_TAB = 'setting_order_link_tab'; // 开启销售订单「环节待办」tab

    const SETTING_KEY_CASH_COLLECTION_CREATE = 'setting_cash_collection_create'; // 开启回款单
    const SETTING_KEY_TRANSFER_ALI_ORDER = 'setting_transfer_ali_order'; // 开启信保订单

    //是否允许导出状态是草稿的订单 0:否、 1:是、 null:是
    const SETTING_KEY_OVER_ALLOCATE_SETTING = "setting_over_allocate";
    const SETTING_KEY_PRODUCT_INVENTORY_LIMIT_SETTING = "product_inventory_limit_setting";

    const SETTING_KEY_DESKTOP_EXPOSE_MAIL_LIMIT = 'desktop_expose_mail_limit';//部分客户的桌面端群发单显的邮箱发件次数

    const SETTING_KEY_OPPORTUNITY_STAGE_CREATE_ORDER_SWITCH = 'setting_opportunity_stage_create_order_switch'; // 商机销售阶段创建销售订单开关

    const SETTING_KEY_OPPORTUNITY_STAGE_CREATE_ORDER_CONFIG = 'setting_opportunity_stage_create_order_config'; // 商机销售阶段创建销售订单配置

    const SETTING_KEY_OPPORTUNITY_AUTO_SYNC_AMOUNT_SWITCH = 'setting_opportunity_auto_sync_amount_switch'; // 商机销售金额自动同步订单金额开关

    const SETTING_KEY_OPPORTUNITY_AUTO_SYNC_AMOUNT_REFER_TYPE = 'setting_opportunity_auto_sync_amount_refer_type'; // 商机销售金额自动同步订单金额触发类型（回款金额/订单金额）
    const SETTING_KEY_CUSTOMER_DUPLICATE_RULE_TYPE = 'setting_key_customer_duplicate_rule_type'; // 客户判重类型

    const SETTING_KEY_CUSTOMER_DUPLICATE_RULE_TYPE_SWITCH_NOTICE = 'setting_key_customer_duplicate_rule_type_switch_notice'; // 客户判重类型替换通知
    const SETTING_KEY_CUSTOMER_DUPLICATE_CONFLICT_DETECTING = 'setting_key_customer_duplicate_conflict_detecting'; // 客户池判重检测中
	const SETTING_KEY_CUSTOMER_DUPLICATE_CONFLICT_DETECTING_ONE_SUCCESS_FLAG = 'setting_key_customer_duplicate_conflict_detecting_one_success_flag'; // 客户池判重检测是否一次成功

    const SETTING_KEY_CUSTOM_CONVERT_SET_SAME_RULE = 'setting_key_custom_convert_set_same_rule';

    const SETTING_KEY_LEADS_SWITCH = 'setting_leads_switch';

    const SETTING_KEY_PAYMENT_INVOICE_PAYMENT_RANGE = 'setting_key_payment_invoice_payment_range'; // 付款单-超额付款设置

//    todo：↓这俩没看到实际的用途
    const SETTING_KEY_ALI_TRANSLATE_SWITCH = 'setting_ali_translate_switch'; //阿里机器翻译开关

    const SETTING_KEY_ALI_TRANSLATE_MAP = 'setting_ali_translate_map'; //阿里机器翻译Map
//    todo：↑

    const SETTING_KEY_CLIENT_TRANSLATE_SWITCH_MAP = 'setting_client_translate_switch_map';//客户翻译开关map
    const SETTING_KEY_CLIENT_AI_BASIC_UPGRADE_FLAG = 'setting_client_ai_basic_upgrade_flag';// 阿里免费版升级标志位
    const SETTING_KEY_CLIENT_ALI_BASIC_CREATE_SOURCE = 'setting_client_ali_basic_create_source';// 阿里免费版client创建来源
    const SETTING_KEY_CLIENT_AI_REPORT_GENERATE = 'client_ai_report_generate'; // ai年度运营账单开关
    const SETTING_KEY_CLIENT_AI_SDR_ORIGIN = 'setting_client_ai_sdr_origin'; // AI SDR来源开关
    const SETTING_KEY_CLIENT_LANGUAGE = 'client_language'; // client支持的语言设置
    const SETTING_KEY_CLIENT_CUSTOMER_LIMIT_CONFIG = 'client_customer_limit_config'; // client级别的客户数量限制配置

    const STATUS_OF_NONE = 0;
    const STATUS_OF_NORMAL = 1;
    const STATUS_OF_TRIAL = 2;

    const SCENE_OF_SALE_RACE = 1;

    const SHUFFLING_FIGURE = 'shuffling_figure';//轮播图
    const CLIENT_TYPE_EXTERNAL =1; //外部client
    const CLIENT_TYPE_INTERNAL = 2; //内部client

    const CLIENT_TYPE_PERSONAL = 4; //外部个人 client

    const ENTERPRISE_CAN_SAVE_FIELDS = [
        'translated_name',
        'name',
        'logo',
        'tel',
        'fax',
        'homepage',
        'address',
        'email',
        'taxpayer_id',
        'business_license',
        'is_oversea',
        'currency_id',
    ];


    protected $_settingAttributes;
    protected $_oldSettingAttributes;
    protected $_opUserId;

    /**
     * @param $clientId
     * @return Client
     */
    public static function getClient($clientId)
    {
        if (!array_key_exists($clientId, self::$cacheClient))
        {
            self::$cacheClient[$clientId] = new self($clientId);
        }

        return self::$cacheClient[$clientId];
    }

    /**
     * @param Client $client
     */
    public static function setClient(Client $client)
    {
        self::$cacheClient[$client->client_id] = $client;
    }

    public static function cleanCacheMap($clientId = null)
    {
        if($clientId)
        {
            if(isset(self::$cacheClient[$clientId])) {
                unset(self::$cacheClient[$clientId]);
            }
        }else {
            self::$cacheClient =[];
        }

    }

    public static function getExpClientIds()
    {
        if( self::$cacheExpClientIds === null )
        {
            $clientIds = self::getCache()->get(self::CACHE_EXP_CLIENT);
            $clientIds = json_decode($clientIds, true);
            if( !is_array($clientIds) )
            {
                $clientIds = \Client::getExpClientIds();
                self::getCache()->set(self::CACHE_EXP_CLIENT, json_encode($clientIds));
            }

            self::$cacheExpClientIds = $clientIds;

        }

        return self::$cacheExpClientIds;
    }

    public function __construct($clientId=null)
    {
        if( $clientId )
            $this->loadById($clientId);
    }

    public function getModelClass()
    {
       return \Client::class;
    }


    public function getCacheKey($clientId=0): string
    {
       if(!$clientId)
            $clientId = $this->client_id;

        return  self::CACHE_PREFIX.":{{$clientId}}";
    }


    protected function getCachePkFiled(): string
    {
        return 'client_id';
    }

    protected function getCacheForeignField(): array
    {
        return [];
    }

    public function getCacheExtentAttributesKey($clientId=0)
    {
        if(!$clientId)
            $clientId = $this->client_id;

        return  self::CACHE_EXTENT_ATTR_PREFIX.":{{$clientId}}";
    }

    public function loadById($clientId)
    {

        $this->client_id = $clientId;
        if( $model = $this->getCacheModel($clientId) )
        {
            $this->setModel($model);
        }elseif( $model = \Client::findByClientId($clientId))
        {
            $this->setModel($model);
            $this->updateCache();
        }

        return $this;

    }

    public function loadByModel($model, $updateCache=false)
    {
        if( !empty($model) )
        {
            $this->client_id = $model->client_id;
            $this->setModel($model);
            if( $updateCache )
                $this->updateCache();
        }

        return $this;
    }

    /**
     * @param $cache
     * @param array|string $extentAttributesCache
     * @param array|string $settingAttributesCache
     * @return $this
     */
    public function loadByCache($cache, $extentAttributesCache=[], $settingAttributesCache=[])
    {

        if( !is_array($cache) )
            $cache = json_decode($cache, true);

        if(!is_array($extentAttributesCache))
            $extentAttributesCache = json_decode($extentAttributesCache,true);

        $model = \Client::model()->populateRecord($cache);
        $this->loadByModel($model);

        if( !empty($extentAttributesCache) ) {
            // okki_leads升级为ames，且为ames访问，增加ames external attributes
            if (AmesHelper::isFromAmesWithOkkiLeads($this->client_id)) {
                $extentAttributesCache = array_merge($extentAttributesCache, AmesHelper::OKKI_LEAD_AMES_SETTINGS);
            }
            $this->_extentAttributes = $this->_oldExtentAttributes = $extentAttributesCache;
        }

        if( !empty($settingAttributesCache) )
            $this->_settingAttributes = $this->_oldSettingAttributes = $settingAttributesCache;

        return $this;
    }

    public function setExtentAttributes(array $attributes)
    {
        if( $this->_extentAttributes ==null)
            $this->getExtentAttributes();

        foreach ( $attributes as $name => $value)
            $this->_extentAttributes[$name] = $value;

    }


    public function getExtentAttributes($names=null)
    {
        if( $this->_extentAttributes ===null )
        {
            $extentAttributes = $this->cacheHashAllGet($this->getCacheExtentAttributesKey());
            if( empty( $extentAttributes) )
            {
                $extentAttributes = \ClientInfoExternal::getValues($this->client_id);
                if(!empty($extentAttributes) )
                    $this->cacheHashMSet($this->getCacheExtentAttributesKey(), $extentAttributes);
            }

            // okki_leads升级为ames，且为ames访问，增加ames external attributes
            if (AmesHelper::isFromAmesWithOkkiLeads($this->client_id)) {
                $extentAttributes = array_merge($extentAttributes, AmesHelper::OKKI_LEAD_AMES_SETTINGS);
            }

            $this->_extentAttributes = $this->_oldExtentAttributes = $extentAttributes;
        }

        if(is_array($names))
        {
            $attrs= [];
            foreach($names as $name)
                $attrs[$name]= $this->_extentAttributes[$name] ?? self::$defaultValue[$name]??null;

            return $attrs;
        }
        else
        {
            return $this->_extentAttributes;
        }

    }

    public function getExtentAttribute($name, $default = null)
    {
        return $this->getExtentAttributes([$name])[$name] ?? $default;
    }

    public function saveExtentAttributes()
    {
        $attrs = [];
        foreach ( $this->_extentAttributes ?? [] as $name => $value )
        {
            if( !isset($this->_oldExtentAttributes[$name]) || $value !== $this->_oldExtentAttributes[$name] )
                $attrs[$name] = $value;;
        }

        if (empty($attrs)) {
            return true;
        }

        $result  = \ClientInfoExternal::setValues($this->client_id, $attrs);

        if( $result ){
            $this->cacheHashMSet($this->getCacheExtentAttributesKey(), $attrs);
            $this->_oldExtentAttributes = $this->_extentAttributes;
            $version = new ClientModuleVersion($this->client_id);
            $version->setModule(Constant::CLIENT_MODULE_PRIVILEGE);
            $version->add();
        }

        return $result;
    }

    public function deleteExtentAttributes(array $attributes)
    {
        if (empty($attributes))
        {
            return true;
        }
        $result = \ClientInfoExternal::deleteClientExtentAttributesByKeys($this->client_id,$attributes);
        if ($result)
        {
            $this->_oldExtentAttributes = $this->_extentAttributes;
            $this->cacheHashDel($this->getCacheExtentAttributesKey(), $attributes);
            $version = new ClientModuleVersion($this->client_id);
            $version->setModule(Constant::CLIENT_MODULE_PRIVILEGE);
            $version->add();
        }
        return $result;
    }
    public function deleteRelevanceCache()
    {
        parent::deleteRelevanceCache();
        $this->cacheDel([$this->getCacheExtentAttributesKey(),$this->getCacheSettingAttributesKey() ]);
    }

    public function getClientPrivilege()
    {
        //获取client privilege
        if (!isset(self::$cachePrivilege[$this->client_id]))
        {
            self::$cachePrivilege[$this->client_id] = (new ClientPrivilegeService($this->client_id))->getFunctionalIds();
        }

        return self::$cachePrivilege[$this->client_id];

    }

    public function hasPrivilege($privilege)
    {
        return in_array($privilege, $this->getClientPrivilege());
    }

    /**
     * 获取已使用的子账号数
     * @return number
     */
    public function getUsedUserNum()
    {

        $realUserIds = Helper::getRealUserIds($this->client_id, true);
        $userCount = count($realUserIds);
        return $userCount;

    }

    /**
     * 获取用户账号数（普通账号+协同账号）
     * @return number
     */
    public function getSystemNum()
    {
        $userNumList = \common\library\privilege_v3\PrivilegeService::getInstance($this->client_id)->getSystemUserNum(PrivilegeConstants::SYSTEM_MODULE_CRM);
        $userCount =  ($userNumList['user_num']??0)+($userNumList['user_coordination_num']??0);

        return $userCount;

    }

    public function getUsedUserNumBySystemModule($systemModule = PrivilegeConstants::SYSTEM_MODULE_CRM)
    {
        $realUserIds = Helper::getRealUserIdsBySystemModule($this->client_id, $systemModule,true);
        $userCount = count($realUserIds);
        return $userCount;
    }

    /**
     * 更新使用账号数
     * @param null|int $usedNum 为null会自动计算
     * @return bool
     */
    public  function updateUsedNum($usedNum=null, $systemModule = null)
    {
        if($usedNum === null)
            $usedNum = $this->getUsedUserNum();

        if ($systemModule != null)
        {
            if ($systemModule == PrivilegeConstants::SYSTEM_MODULE_CRM)
            {
                $usedNumCrm = $this->getUsedUserNumBySystemModule($systemModule);
                $this->setExtentAttributes([self::EXTERNAL_KEY_USED_USER_NUM_CRM =>$usedNumCrm]);
                \LogUtil::info(date('Y-m-d H:i:s'). " --client:{$this->client_id}; updateUsedNumCrm：{$usedNumCrm}");
            }

            if ($systemModule == PrivilegeConstants::SYSTEM_MODULE_SHOP)
            {
                $usedNumOkkiShop = $this->getUsedUserNumBySystemModule($systemModule);
                $this->setExtentAttributes([self::EXTERNAL_KEY_USED_USER_NUM_OKKI_SHOP => $usedNumOkkiShop]);
                \LogUtil::info(date('Y-m-d H:i:s'). " --client:{$this->client_id}; updateUsedNumOkkiShop：{$usedNumOkkiShop}");
            }
        }

        \LogUtil::info(date('Y-m-d H:i:s'). " --client:{$this->client_id}; updateUsedNum：{$usedNum}");
        $this->setExtentAttributes([self::EXTERNAL_KEY_USED_USER_NUM =>$usedNum]);
        return $this->saveExtentAttributes();
    }

    /**
     * 获取剩余天数
     * @return number
     */
    public function getRemainDays()
    {
        $validTime = strtotime($this->valid_to);
        $now = time();
        $restDays = ceil(($validTime - $now) / (3600 * 24));
        return empty($restDays) ? 1 :$restDays;

    }

    /**
     * @return \User
     */
    public function getMasterUser()
    {
        try
        {
            $adminUserId = PrivilegeService::getInstance($this->client_id)->getAdminUserId();
        }catch (\Exception $e)
        {
            $adminUserId = 0;
        }
        $masterUser = $adminUserId ? \User::getUserObject($adminUserId, $this->client_id) : \User::getUserObjectByEmail($this->master_account, $this->client_id);

        return $masterUser;
    }

    /**
     * 扣除公司edm营销
     * @param $count
     * @return bool|int
     */
    public function deductEdmCount($count)
    {
        if ($count ==0 || $count > $this->edm_count )
            return false;

        //?之前代码也没维护edm_used_count
        $result =  $this->saveCounters(['edm_count' => -$count]);
        return $result;
    }

    /**
     * 给公司添加edm营销数
     * @param $count
     * @return bool
     */
    public function addEdmCount($count)
    {
        if( $count ==0)
            return false;

        //?之前代码也没维护edm_used_count
        $result = $this->saveCounters(['edm_count' => $count]);
        return $result;
    }

    public function setSettingAttributes(array $attributes)
    {
        if( $this->_settingAttributes ==null)
            $this->getSettingAttributes();

        foreach ( $attributes as $name => $value)
            $this->_settingAttributes[$name] = $value;

    }

    public function getCacheSettingAttributesKey($clientId=0)
    {
        if(!$clientId)
            $clientId = $this->client_id;

        return  self::CACHE_SETTING_ATTR_PREFIX.":{{$clientId}}";
    }

    public function getSettingAttributes($names=null)
    {
        if( $this->_settingAttributes ===null )
        {
            $settingAttributes = $this->cacheHashAllGet($this->getCacheSettingAttributesKey());
            if( empty( $settingAttributes) )
            {
                $settingAttributes = \ClientSetting::getValues($this->client_id);
                if(!empty($settingAttributes) )
                   $this->cacheHashMSet($this->getCacheSettingAttributesKey(), $settingAttributes);
            }

            $this->_settingAttributes = $this->_oldSettingAttributes = $settingAttributes;
        }

        foreach ($this->_settingAttributes as $name => $attribute) {
            if (in_array($name, self::$arrayValue) && is_string($attribute)) {
                $this->_settingAttributes[$name] = json_decode($attribute, true);
            }
        }
        if(is_array($names))
        {
            $attrs= [];
            foreach($names as $name)
                $attrs[$name] = $this->_settingAttributes[$name] ?? self::getSettingDefaultValue($name);
            return $attrs;
        }
        else
        {
            return $this->_settingAttributes;
        }

    }

    public function saveSettingAttributes()
    {
        $attrs = [];
        foreach ( $this->_settingAttributes as $name => $value )
        {
            if (gettype($value) === 'array') {
                if( !isset($this->_oldSettingAttributes[$name]) || json_encode($value) !== $this->_oldSettingAttributes[$name] ) {
                    $attrs[$name] = in_array($name, self::$arrayValue) ? (is_array($value) ? json_encode($value) : $value) : $value;
                }
            } else {
                if( !isset($this->_oldSettingAttributes[$name]) || $value !== $this->_oldSettingAttributes[$name] ) {
                    $attrs[$name] = in_array($name, self::$arrayValue) ? (is_array($value) ? json_encode($value) : $value) : $value;
                }
            }
        }

        if (empty($attrs)) {
            return true;
        }

        $result  = \ClientSetting::setValues($this->client_id, $attrs);
        if( $result ){
            $this->cacheHashMSet($this->getCacheSettingAttributesKey(), $attrs);
            //增加操作日志
            if (isset($attrs['client_currency'])){
                // 切换主币种的时候需要重算一下绩效
                CommandRunner::run(
                    'performanceV2',
                    'ReRunPerformanceAssociatedMainCurrency',
                    [
                        'clientId' => $this->client_id,
                    ]
                );
            }

            $oldData = $this->_oldSettingAttributes;
            $newData = $this->_settingAttributes;

            $userId = $this->_opUserId;
            if (!$userId) {
                $user = User::getLoginUser();
                if ($user) {
                    $userId = $user->getUserId();
                }
            }

            // 改变了的 key-value 需要记录操作历史
            // 每一个 key-value 单独记录一条操作历史, 为了能够给操作历史展示工单工具做归类
            $builder = new ItemSettingHistoryBuilder();
            foreach($attrs as $key => $value)
            {
                try {
                    $diffData = [
                        'old' => isset($oldData[$key]) ? (is_string($oldData[$key]) ? (json_decode($oldData[$key], true) ?? ($oldData[$key] ?? ''))  : $oldData[$key]) : '',
                        'new' => isset($newData[$key]) ? (is_string($newData[$key]) ? (json_decode($newData[$key], true) ?? ($newData[$key] ?? ''))  : $newData[$key]) : '',
                    ];

                    $builder->setModule(ItemSettingHistoryCompare::SETTING_TYPE_CLIENT_SETTING);
                    $builder->build(
                        $this->client_id,
                        $userId,
                        0,
                        ['refer_id' => $this->client_id, 'refer_key' => $key],
                        $diffData,
                        true
                    );
                } catch (Throwable $throwable) {
                    LogUtil::info("history collect fail - ClientSetting, clientId: {$this->client_id}, userId: {$userId}, refer_key: {$key}, valueNew: `{$diffData['new']}`, valueOld: `{$diffData['old']}`");
                    LogUtil::info("throwable message:{$throwable->getMessage()}");
                }
            }

            $this->_oldSettingAttributes = $this->_settingAttributes;
        }

        return $result;
    }

    public function issetSettingAttributes($name)
    {
        $settingAttributes = \ClientSetting::getValues($this->client_id);
        return isset($settingAttributes[$name]);
    }

    /**
     * @desc 获取客户上限配置
     * @return array
     */
    public function getCustomerLimitConfig(): array
    {
        $clientSetting = $this->getSettingAttributes([Client::SETTING_KEY_CLIENT_CUSTOMER_LIMIT_CONFIG]);
        return CustomerOptionService::getCustomerLimitConfig($this->client_id, $clientSetting[Client::SETTING_KEY_CLIENT_CUSTOMER_LIMIT_CONFIG] ?? []);
    }

    /**
     * 是否开启某项设置
     *
     * @param $key
     *
     * @return bool
     */
    public function enabledSetting($key)
    {
        return $this->getExtentAttribute($key) ? true : false;
    }

    public function getMainCurrency()
    {
        if ($this->mysql_set_id > 0) {
            return $this->getSettingAttributes([self::SETTING_KEY_CURRENCY])[self::SETTING_KEY_CURRENCY] ?? self::$defaultValue[self::SETTING_KEY_CURRENCY];
        }
        return self::$defaultValue[self::SETTING_KEY_CURRENCY];
    }

    public function getSettingDefaultValue($name)
    {
        $value = null;
        switch ($name)
        {
            case self::SETTING_KEY_CUSTOMER_DEAL_TIME_UPDATE_TYPE:
                //根据业绩管理设置指定默认值，类型为回款单时使用商机
                $value = (new PerformanceSetting($this->client_id))->getType();
                return ($value && $value != \Constants::TYPE_CASH_COLLECTION) ? $value : \Constants::TYPE_OPPORTUNITY;
                break;
            case self::SETTING_KEY_PRODUCT_EXPORT_LANGUAGE:
                $value = \Constants::LANGUAGE_EN;
                break;
            default:
                if (array_key_exists($name, \CustomerOptionService::$clientDefaultReferenceField)) {
                    $value = \CustomerOptionService::$clientDefaultReferenceField[$name];
                } elseif (array_key_exists($name, \CustomerOptionService::$clientDefaultRecentFollowUpReferenceField)) {
                    $value = \CustomerOptionService::$clientDefaultRecentFollowUpReferenceField[$name];
                } else {
                    $value = self::$defaultValue[$name]??null;
                }
                break;
        }

        return $value;
    }

    public function isTrial()
    {
        return $this->status == self::STATUS_OF_TRIAL;
    }

    public function setOpUserId($userId){
        $this->_opUserId = $userId;
    }

    /**
     * 获取已过期天数
     * @return int 过期当天=0， 已过期返回大于0的天数，未过期返回负数
     * @throws \Exception
     */
    public function getExpiredDays(): int
    {
        $validToDate = date('Y-m-d', strtotime($this->valid_to));
        $validToDate = new \DateTime($validToDate);
        $today = new \DateTime(date('Y-m-d'));
        return $validToDate->diff($today)->format("%r%a");
    }

    /**
     * 数据是否已经被归档备份
     * @return bool
     */
    public function isBackup():bool
    {
        return (bool)$this->is_backup;
    }

    /**
     * 企业中心保存企业信息
     *
     * @param $infos
     * @return void
     */
    public function saveEnterpriseInfo($infos)
    {
        foreach ($infos as $k => $v) {
            if (in_array($k, self::ENTERPRISE_CAN_SAVE_FIELDS)) {
                if ($k == 'taxpayer_id' && $this->taxpayer_id) {
                    continue;
                }
                if ($k == 'business_license' && $this->business_license) {
                    continue;
                }
                $this->$k = $v;
            }
        }
        if (!$this->save()) {
            throw new \RuntimeException(\Yii::t('enterprise', '公司信息保存失败'));
        }
    }

    public function isOversea() {

        if( !empty($this->use_zone) && $this->use_zone != self::USE_ZONE_CN )
            return true;

        // 台湾企业
        if (($this->currency_id ?? '') == \Client::CURRENCY_TYPE_TWD) {
            return true;
        }
        // 海外leads ai版本特别判断
        if (!empty($this->client_id)
            && in_array(PrivilegeConstants::OKKI_PERSONAL_LEADS_AI_GLOBAL_BASIC_ID, PrivilegeService::getInstance($this->client_id)->getAllMainSystemId())) {
            return true;
        }
        return false;
    }

    public function isTWVersion()
    {
        if ($this->use_zone == self::USE_ZONE_TW){
            return true;
        }
        return  false;
    }

    public function isHKVersion()
    {
        if ($this->use_zone == self::USE_ZONE_HK){
            return true;
        }
        return  false;
    }

    public function isJpVersion()
    {
        if ($this->use_zone == self::USE_ZONE_JP){
            return true;
        }
        return  false;
    }

    public function isCNVersion()
    {
        if ($this->use_zone == self::USE_ZONE_CN){
            return true;
        }
        return  false;
    }


}
