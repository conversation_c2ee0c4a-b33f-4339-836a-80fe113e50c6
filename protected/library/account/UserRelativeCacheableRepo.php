<?php

namespace common\library\account;

use common\library\cache\CacheableListRepo;
use common\library\cache\CacheConstant;
use common\library\util\SqlBuilder;

class UserRelativeCacheableRepo extends CacheableListRepo
{

    protected function buildListByDb($id = null, $fields = '*')
    {
        $where = "client_id = :client_id and enable_flag = 1";
        $params = [
            ':client_id' => $this->clientId,
        ];
        if ($id) {
            SqlBuilder::buildIntWhere('', $this->getIdName(), $id, $where, $params);
        }

        $sql = "select * from {$this->getTableName()} where {$where}";

        return [$sql, $params];
    }

    protected function getTableName()
    {
        return 'tbl_user_relative';
    }

    public function build()
    {
        return [1, []];
    }

    protected function getDbConnection()
    {
        return \UserRelative::model()->getDbConnection();
    }

    protected function getIdName()
    {
        return 'id';
    }
}