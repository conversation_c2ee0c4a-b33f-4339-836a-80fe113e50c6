<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2018/9/13
 * Time: 上午11:16
 */

namespace common\library\account;



use common\library\ai\translate\TranslateService;
use common\library\risk\RiskConstants;
use common\library\risk\RiskEventList;

class LoginEventFormatter extends \ListItemFormatter
{

    protected $specifyFields;
    protected $externalFields;
    protected $formatClientType = false;
    protected $showRiskFlag  = false;

    protected $clientId;

    /**
     * @param mixed $specifyFields
     */
    public function setSpecifyFields(array $specifyFields)
    {
        $this->specifyFields = $specifyFields;
    }

    /**
     * @param mixed $externalFields
     */
    public function setExternalFields(array $externalFields)
    {
        $this->externalFields = $externalFields;
    }

    /**
     * @param bool $formatClientType
     */
    public function setFormatClientType(bool $formatClientType)
    {
        $this->formatClientType = $formatClientType;
    }

    public function listInfoSetting()
    {
        $this->setSpecifyFields([
            'id',
            'account',
            'client_type',
            'client_ip',
            'country',
            'province',
            'city',
            'login_time'
        ]);

        $this->setExternalFields([
            \LoginEventExternal::KEY_PLATFORM,
            \LoginEventExternal::KEY_BROWSER,
            \LoginEventExternal::KEY_FEEDBACK_FLAG,
        ]);

        $this->setFormatClientType(true);
    }

    /**
     * @param $clientId
     * @param bool $showRiskFlag
     */
    public function setShowRiskFlag($clientId, bool $showRiskFlag)
    {
        $this->showRiskFlag = $showRiskFlag;
        $this->clientId = $clientId;
    }


    public function riskEventInfoSetting()
    {
        $this->setSpecifyFields([
            'account',
            'client_type',
            'client_ip',
            'reason',
            'device_id',
            'country',
            'province',
            'city',
            'login_time'
        ]);

        $this->setExternalFields([
            \LoginEventExternal::KEY_PLATFORM,
            \LoginEventExternal::KEY_BROWSER,
        ]);

        return  array_merge($this->specifyFields, $this->externalFields);
    }

    public function riskEventDetailSetting()
    {
        $this->setExternalFields([
            \LoginEventExternal::KEY_PLATFORM,
            \LoginEventExternal::KEY_BROWSER,
        ]);
    }

    public function buildMapData()
    {
        $list  = $this->batchFlag?$this->listData:[$this->data];
        $ids = array_column($list, 'id');
        $externalMap = [];
        if( !empty($this->externalFields))
        {
            $externalMap = \LoginEventExternal::getValueMap($ids, $this->externalFields);
        }

        $riskFlagMap=[];
        if( $this->showRiskFlag && $this->clientId )
        {
            $riskList = new RiskEventList($this->clientId);
            $riskList->setType(RiskConstants::TYPE_LOGIN);
            $riskList->setReferId($ids);
            $riskList->setFields(['1 as risk_flag', 'refer_id']);
            $riskFlagMap  = array_column($riskList->find(), 'risk_flag', 'refer_id');
        }

        $this->setMapData([
            'external_field' => $externalMap,
            'risk_flag' => $riskFlagMap,
            'dict' => $this->makeDict($list),
        ]);

    }


    /**
     * 构建指定字段
     * @param $data
     * @return array
     */
    protected function buildFieldsInfo($data){
        $specifyFields = $this->specifyFields;
        if ( $this->specifyFields === null )
        {
            $specifyFields = array_keys($data);
        }

        $result = \ArrayUtil::columns($specifyFields,$data,'');

        return $result;
    }

    protected function format($data)
    {
        $loginEventId = $data['id'];
        $result = $this->buildFieldsInfo($data);

        if( $this->formatClientType && isset($result['client_type']) )
        {
            $result['client_type'] =  self::formatClientType($result['client_type']);
        }

        if( !empty($this->externalFields) )
        {
            $result = array_merge($result, \ArrayUtil::columns($this->externalFields, $this->getMapData('external_field', $loginEventId)??[], ''));
        }

        if( $this->showRiskFlag )
        {
            $result['risk_flag'] = $this->getMapData('risk_flag', $loginEventId)?1:0;
        }

        $result['country'] = $this->getMapData('dict', $result['country']) ?? $result['country'];
        $result['province'] = $this->getMapData('dict', $result['province']) ?? $result['province'];
        $result['city'] = $this->getMapData('dict', $result['city']) ?? $result['city'];

        return $result;
    }

    public static function formatClientType($clientType)
    {
        return \Yii::t('common', $clientType);
    }

    /**
     * 根据当前语言生成地名词典
     * @example return ['深圳' => 'Shenzhen']
     * @param array<array> $loginEvents
     * @return array
     */
    private function makeDict(array $loginEvents): array
    {
        $raw = [];
        foreach ($loginEvents as $v) {
            $raw[] = $v['country'];
            $raw[] = $v['province'];
            $raw[] = $v['city'];
        }
        $raw = array_unique($raw);
        $raw = array_filter($raw, function ($e) {return $e != '';});

        $translated = (new TranslateService())
            ->setStrategy('IcbuTranslate')
            ->setSource('loginEvent')
            ->setTarget(\Yii::app()->language)
            ->setContentList($raw)
            ->setUseCache(true)
            ->translate();

        return array_combine(
            $raw,
            array_map(function ($item) { return $item['content'] ?? null; }, $translated)
        );
    }
}
