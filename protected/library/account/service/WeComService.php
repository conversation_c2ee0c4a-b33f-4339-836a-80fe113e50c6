<?php


namespace common\library\account\service;

use common\library\wecom\oauth\WecomAccount;
use ErrorCode;
use Exception;
use LogUtil;
use RuntimeException;
use User;

class WeComService
{

    protected $userId;
    protected $userInfo;

    /**
     * @var WecomAccount
     */
    protected $wecomAccount;
    protected $wecomOpenUserid;
    protected $origin;
    protected $accountList;
    protected $authStatus;
    protected $token;

    protected $account;
    protected $redirectUrl;
    protected $key;
    protected $targetId;
    protected $selfId;
    protected $appVersion;
    protected $code;

    const ORIGIN_WECOM_PC  = 'pc';// //来源桌面端
    const ORIGIN_WECOM_APP  = 'app';// //来源app端

    const WECOM_ORIGIN_LIST = [
        self::ORIGIN_WECOM_PC,
        self::ORIGIN_WECOM_APP
    ];

    const AUTH_FLAG_INVALID = 0; //账号状态异常
    const AUTH_FLAG_NORMAL  = 1; //账号状态正常

    const TOKEN_EXPIRED = 5 * 60;// token过期时长   // TODO
    const WECOM_LOGIN_EXPIRED = 7 * 24 * 60 * 60;// 免登时长

    //免登路径
    const WECOM_LOGIN_PATH = '/api/assistant/UserRead/weComLogin';
    const WECOM_LOGIN_PATH_APP = '/api/stormsFury/UserRead/weComLogin';

    //wecom页面 （包含错误引导页、开通页、数据展示页）  todo
    const WECOM_DEFAULT_PATH = '/tm/extension';

    const WECOM_DESKTOP_PATH = '/tm/wxwork';
    const WECOM_MOBILE_PATH = '/assistant_subapp/';
    const WECOM_404_PATH = '/tm/wxwork/404';
    const WECOM_BIND_EXCEPTION_PATH = '/tm/wxwork';

    //限流
    const KEY_PREFIX_WECOM_OPEN_USER_LIMIT  = 'wecom:wecom_open_userid:limit:';

    //国际站账号token缓存
    const KEY_PREFIX_WECOM_ACCOUNT = 'wecom:token';

    const AUTH_TYPE_NO_BIND        = 1; //账号未绑定到小满账号
    const AUTH_TYPE_EXPIRED        = 2; //合同到期
    const AUTH_TYPE_VERSION        = 3; //版本不符合
    const AUTH_TYPE_USER_ABNORMAL  = 4; //账号绑定的OKKICRM账号状态异常

    const TOKEN_SOURCE_WECOM     = 'wecom';// 来源企微
    const TOKEN_SOURCE_XIAOMAN = 'xiaoman';// 来源小满




    const APP_CARD_TYPE_MAP = [
        1 => '私海客户',
        2 => '同事客户',
        3 => '公海客户',
        4 => '陌生人',
        5 => '陌生人',
        6 => '陌生人',
        7 => '公海客户',
        8 => '同事客户',
        9 => '私海线索',
        10 => '陌生人',
        11 => '同事线索',
        12 => '公海线索'
    ];

    /**
     * @param $wecomOpenUserid
     * @return void
     */
    public function setWecomOpenUserid($wecomOpenUserid)
    {
        $this->wecomOpenUserid = $wecomOpenUserid;
    }


    /**
     * @param $origin
     * @return void
     */
    public function setOrigin($origin)
    {
        $this->origin = $origin;
    }

    /**
     * @param $code
     * @return void
     */
    public function setCode($code)
    {
        $this->code = $code;
    }

    /**
     * @param $wecomOpenUserid
     * @return void
     */
    public function loadWecomAccount($wecomOpenUserid)
    {
        $wecomAccount = new WecomAccount();
        $wecomAccount->loadByWecomOpenUserid($wecomOpenUserid);
        if($wecomAccount->isNew()){
            LogUtil::info("[wecomLogin-loadWecomAccount] wecom_open_userid:{$wecomOpenUserid} not exist " );
            return false;
        }
        $this->wecomAccount = $wecomAccount;
        $this->userId = $wecomAccount->user_id;
    }


    /**
     * 生成免登url
     * @return array
     */
    public function generateLoginUrl()
    {
        $result  = [
            'show_flag' => false,
            'auth_flag' => false,
            'url'       => '',
            'expire'    => 0,
        ];

        //没有购买小满，或小满服务已过期，不展示插件     todo
        if(!$this->checkShowOkki()){
            return $result;
        }

        $wecomAccountService = new \common\library\wecom\oauth\WecomAccountService(0,0);
        $data = $wecomAccountService->getWecomUserInfo($this->code);
        $this->wecomOpenUserid = $data['userid'] ?: $this->wecomOpenUserid ;   // $data['open_userid']  服务商user_id明文，无法解析密文wecom_userid  调整兼容明文ID

        $this->loadWecomAccount($this->wecomOpenUserid);
        if(empty($this->wecomAccount)){
            throw new RuntimeException("企微用户未绑定，无法登录crm");
        }

        //后续流程需要展示插件
        $result['show_flag'] = true;
        $result['expire'] = time() + self::TOKEN_EXPIRED;

        //检查账号状态
        $this->authStatus = $this->getAuthStatus($this->wecomOpenUserid)['status'] ?? false;

        if (in_array($this->origin,self::WECOM_ORIGIN_LIST)) {

            if($this->authStatus){
                $result['auth_flag'] = true;
                //限流
                if($this->currentLimit()){
                    LogUtil::error("[generateLoginUrl-currentLimit] wecomOpenUserid:{$this->wecomOpenUserid}");
                    throw new RuntimeException(\Yii::t('account', 'Operating too often'));
                }else{
                    //生成免登url
                    $result['url'] = $this->generateWecomUrl();
                }

            }

            $result['token'] = $this->token ?? '';
            return $result;
        }

        //检查国际站账号状态
        if (!$this->authStatus) {
            //跳转错误引导页
            $result['url'] = $this->getHost().self::WECOM_DEFAULT_PATH.'?authFlag='.self::AUTH_FLAG_INVALID;

        }else{
            $result['auth_flag'] = true;
            //生成免登url
            $result['url'] = $this->generateWecomUrl();
        }

        $result['token'] = $this->token ?? '';
        return $result;
    }


    public function getUrl(): string
    {
        return $this->getHost().self::WECOM_DEFAULT_PATH;
    }

    /**
     * 限流  TODO
     * @return boolean
     */
    protected function currentLimit()
    {
        $key = self::KEY_PREFIX_WECOM_OPEN_USER_LIMIT.$this->wecomOpenUserid;

        $lua = <<<LUA
        local i = redis.call("INCR", KEYS[1]) 
        if i > 40 then
          return 1
        else
          if i == 1
          then
            redis.call("expire", KEYS[1], KEYS[2])
          end
          return 0
        end
        LUA;
        return \RedisService::cache()->eval($lua, 2, $key, 60);

    }

    /**
     * 是否展示插件
     * @return boolean
     */
    private function checkShowOkki()
    {
        // todo
        return true;
    }


    /**
     * 获取数据企微对应所有client数据
     * @param $wecomOpenUserid
     * @return void
     */
    protected function getAccountList($wecomOpenUserid)
    {

        if( $this->accountList === null )
        {
            $sql = "select * FROM tbl_wecom_account where wecom_open_userid = :wecom_open_userid";
            $this->accountList = \Yii::app()->db->createCommand($sql)->queryAll(true, [':wecom_open_userid' => $wecomOpenUserid]);

        }

        return $this->accountList;
    }


    /**
     * 检查账号状态
     * @param $wecomOpenUserId
     * @return boolean
     */
    private function getAuthStatus($wecomOpenUserId)
    {
        $return = [
            'status' => true,
            'auth_detail' => []
        ];
        //  账号及绑定检查  TODO

        //账号未绑定到小满账号

        //合同到期

        //版本不符合

        //企微账号绑定的OKKICRM账号状态异常

        //账号在OKKICRM绑定异常

        //授权异常

        // OKKI销售助手开关为“关闭”；(开关状态=关闭)

        // 销售助手开关

        return $return;
    }


    /**
     * 生成免登url
     * @return string
     */
    private function generateWecomUrl()
    {
        $this->generateToken();

        $userInfo = [
            'id'        => $this->wecomAccount->id,
            'client_id'         => $this->wecomAccount->client_id,
            'user_id'           => $this->wecomAccount->user_id,
            'wecom_open_userid' => $this->wecomAccount->wecom_open_userid,
            'corpid'          => $this->wecomAccount->corpid,
            'from'              => self::TOKEN_SOURCE_WECOM
        ];
        $this->setToken($this->token, $userInfo);

        $authFlag = $this->authStatus ? self::AUTH_FLAG_NORMAL : self::AUTH_FLAG_INVALID;

        $host = $this->getHost();

        $path = $this->origin == self::ORIGIN_WECOM_APP ? self::WECOM_LOGIN_PATH_APP : self::WECOM_LOGIN_PATH;

        $url = $host.$path.'?token='.$this->token.'&origin='.$this->origin.'&authFlag='.$authFlag;
        LogUtil::info("[WecomLogin-generateTWecomUrl] success, id: {$this->wecomAccount->id}, wecom_open_userid: {$this->wecomAccount->wecom_open_userid}, client_id: {$this->wecomAccount->client_id}, user_id:{$this->wecomAccount->user_id}, corpid: {$this->wecomAccount->corpid} token: {$this->token}");

        return $url;
    }

    /**
     * 生成token
     * @param $token
     * @param $userInfo
     * @return void
     */
    private function setToken($token, $userInfo)
    {
        $redis = \RedisService::sf();

        $redis->set($token, json_encode($userInfo), 'EX', self::TOKEN_EXPIRED);

        $this->setTokenCache($userInfo['wecom_open_userid'], $token);
    }

    /**
     * 生成token
     * @return void
     */
    private function generateToken()
    {
        if (!$this->userId) {
            return false;
        }

        $account = (new \common\library\account\Account())->loadById($this->userId);
        $this->token = hash('sha256', $account->user_id . $account->account. time() );
    }

    /**
     * 生成登录态
     * @return boolean
     */
    public function login()
    {
        if (empty($this->userInfo)){
            LogUtil::info("[wecomLogin]:userInfo empty, token:{$this->token}");
            return false;
        }
        if($this->checkSkey()){
            return true;
        }


        try {
            $clientType = User::LOGIN_CUSTOM_DATA_CLIENT_TYPE_WECOM;
            $customData = [
                'wecom_open_userid' => $this->userInfo['wecom_open_userid']
            ];

            $logVisible = $this->userInfo['from'] == self::TOKEN_SOURCE_XIAOMAN;

            $loginInfo = LoginService::produceSkey($this->account->account, $clientType, self::WECOM_LOGIN_EXPIRED, '', $logVisible, $customData);

            LogUtil::info("wecomLogin:parameters-{$this->account->account},{$clientType},return-" . json_encode($loginInfo));

            if($loginInfo['cookie']??[]){
                $cookie = LoginService::assembleCookies($loginInfo['cookie']??[]);

                LoginService::$prefix =  '';
                foreach ($cookie as &$value)
                {
                    $value['key'] = LoginService::$prefix.$value['key'];
                }

                LoginService::setSkeyCookie($cookie);
            }

        } catch (Exception $exception) {
            LogUtil::error("[wecomLogin] token:{$this->token},account:{$this->account->account} 生成登录态失败: " . $exception->getMessage());
            throw new RuntimeException(\Yii::t('account','Login free failed'));
        }
    }

    /**
     * 检查登录态是否存在
     * @return boolean
     */
    public function checkSkey()
    {
        try {
            LoginService::$prefix =  $this->origin == self::ORIGIN_WECOM_APP ? '' : LoginService::ASSISTANT_PREFIX;
            $loginData = LoginService::checkSkey();
            if(!empty($loginData)){
                if (isset($this->userInfo['user_id']) && intval($this->userInfo['user_id']) != intval($loginData['userId'])) {
                    return false;
                }
            }
            return true;
        } catch (Exception $exception) {
            LogUtil::info($exception->getMessage());
        }
        return false;
    }

    /**
     * 跳转
     * @return string
     */
    public function redirectUrl()
    {
        if (!isset($this->wecomOpenUserid)) {
            return false;
        }
        $host = $this->getHost();

        $this->loadWecomAccount($this->wecomOpenUserid);

        $authFlag = $this->getAuthStatus($this->wecomOpenUserid)['status'] ? self::AUTH_FLAG_NORMAL : self::AUTH_FLAG_INVALID;

        return $host.self::WECOM_DEFAULT_PATH.'?targetId='.$this->targetId.'&selfId='.$this->wecomOpenUserid;

    }

    /**
     * web跳转
     * @return string
     */
    public function webRedirect()
    {
        return $this->redirectUrl;
    }




    /**
     * host
     * @return string
     */
    private function getHost()
    {
//        if( \Yii::app()->params['env'] == 'test')
//        {
//            $redis = \RedisService::getInstance('redis_openapi_limit_config');
//            $key = 'wecomOpenUserid:route:setting';
//            $url = $redis->hget($key,$this->wecomOpenUserid);
//            if($url)
//                return $url;
//        }
        // TODO
        return "https://".$_SERVER['HTTP_HOST']; //\Yii::app()->params['host']['assistant_url'];
    }

    /**
     * 设置账号token
     * @param $wecomOpenUserid
     * @param $token
     * @return void
     */
    protected function setTokenCache($wecomOpenUserid, $token)
    {
        $redisKey = self::KEY_PREFIX_WECOM_ACCOUNT.':{'.$wecomOpenUserid.'}';
        $redis = \RedisService::sf();

        $redis->set($redisKey, $token,'EX', self::TOKEN_EXPIRED);
    }

    /**
     * 清除账号token
     * @param $wecomOpenUserid
     * @return void
     */
    public function delTokenCache($wecomOpenUserid)
    {
        $redisKey = self::KEY_PREFIX_WECOM_ACCOUNT.':{'.$wecomOpenUserid.'}';
        $redis = \RedisService::sf();
        $token = $redis->get($redisKey);
        if($token)
        {
            $redis->del([$token]);
        }
    }


    /**
     * token
     * @param $token
     * @return void
     */
    public function loadToken($token)
    {
        $redis = \RedisService::sf();

        $userInfo = $redis->get($token);
        if ($userInfo) {
            $this->userInfo = json_decode($userInfo, true);
            $this->loadAccount();
        }

    }

    public function getGreyEnv()
    {
        return \Yii::app()->params['env'] == 'grey';
    }

    /**
     * 账号信息
     * @return void
     */
    private function loadAccount()
    {
        if(!isset($this->userInfo['user_id'])) {
            return false;
        }
        $this->account = (new \common\library\account\Account())->loadById($this->userInfo['user_id']);

        if (empty($this->account)) {
            LogUtil::info("[wecomLogin-loadAccount] user_id:{$this->userInfo['user_id']},account not exist " );
            throw new RuntimeException(\Yii::t('account', 'User not found'));
        }
    }


    public function checkSkeyByUserId(int $userId): bool
    {
        if (empty($userId)) {
            return false;
        }
        try {
            $loginData = LoginService::checkSkey();
            if (!empty($loginData)) {
                if ($userId && $userId != intval($loginData['userId'])) {
                    return false;
                }
            }
            return true;
        } catch (Exception $exception) {
            LogUtil::info($exception->getMessage());
        }
        return false;
    }

    public function loginByUserId(int $userId, string $weComOpenUserId): bool
    {
        if ($this->checkSkeyByUserId($userId)) {
            return true;
        }
        $account = (new \common\library\account\Account())->loadById($userId);
        if (empty($account)) {
            LogUtil::info("[wecomLogin-loginByUserId] user_id:{$userId},account not exist ");
            throw new RuntimeException(\Yii::t('account', 'User not found'));
        }
        try {
            $clientType = User::LOGIN_CUSTOM_DATA_CLIENT_TYPE_WECOM;
            $customData = [
                'wecom_open_userid' => $weComOpenUserId
            ];
            $logVisible = self::TOKEN_SOURCE_XIAOMAN;
            $loginInfo = LoginService::produceSkey($account->account, $clientType, self::WECOM_LOGIN_EXPIRED,
                '', $logVisible, $customData);
            LogUtil::info("wecomLogin:parameters-{$account->account},{$clientType},return-" . json_encode($loginInfo));
            if ($loginInfo['cookie'] ?? []) {
                $cookie = LoginService::assembleCookies($loginInfo['cookie'] ?? []);
                LoginService::setSkeyCookie($cookie);
            }
        } catch (Exception $exception) {
            LogUtil::error("[wecomLogin] account:{$account->account} 生成登录态失败: " . $exception->getMessage());
            throw new RuntimeException(\Yii::t('account', 'Login free failed'));
        }
        return true;
    }


    public function redirectDeskTopUrl(string $weComOpenUserId, string $corpId = ''): string
    {
        $host = $this->getHost();
        return $host . self::WECOM_DESKTOP_PATH . '?selfId=' . $weComOpenUserId . '&corpId=' . $corpId;
    }

    public function redirectMobileUrl(string $weComOpenUserId, string $corpId = ''): string
    {
        $host = $this->getHost();
        return $host . self::WECOM_MOBILE_PATH . '?relevance-account-id=' . $weComOpenUserId;
    }

    public function redirect404Url(): string
    {
        $host = $this->getHost();
        return $host . self::WECOM_404_PATH;
    }

    public function redirectLoginExceptionUrl(string $message): string
    {
        $host = $this->getHost();
        return $host . self::WECOM_BIND_EXCEPTION_PATH . '?message=' . $message . '&errorCode=' . ErrorCode::CODE_WECOM_LOGIN_CRM_ERROR;
    }

    public function redirectBindExceptionUrl(array $arr): string
    {
        $host = $this->getHost();
        return $host . self::WECOM_BIND_EXCEPTION_PATH . '?'. http_build_query($arr);
    }

}
