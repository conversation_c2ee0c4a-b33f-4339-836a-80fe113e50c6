<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2018/3/23
 * Time: 上午11:13
 */

namespace common\library\account;


use common\library\account\external\UserInfoExternal;
use common\library\account\external\UserInfoExternalList;
use common\components\BaseObject;
use common\library\account\service\LoginService;
use common\library\alibaba\AlibabaService;
use common\library\CompanyQuotaManager;
use common\library\department\DepartmentService;
use common\library\okki_chat\ChatService;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\whatsapp_cloud\account\WhatsappCloudAccountFilter;
use UserMail;
use UserMailList;
use UserRelative;

class UserInfoFormatter extends \ListItemFormatter
{
    protected $userId;
    protected $clientId;
    protected $adminType;
    protected $viewUserId;//当前查看的用户,$showCanManageFlag==1时,必须传

    protected $showInClientFlag = false;
    protected $showAdminType = false;
    protected $showFreezeFlag = false;
    protected $showFrozenable = false;
    protected $showGlobalFreezeFlag = false;
    protected $showWxBindFlag = false;
    protected $showSafeLoginFlag = false;
    protected $showBindingEmailInfo = false;
    protected $showBindStatus = false;
    protected $showSecuritySettingOpenFlag = false;
    protected $showBindUser = false;
    protected $showBindingUserList = false;
    protected $showUserDepartmentList = false;
    protected $showRecursionDepartmentList = false;
    protected $showUserSystem = false;
    protected $showUserSystemAvailable = false;

    protected $showCanManageFlag = false;
    protected $showCanViewDetailFlag = false;
    protected $showUserMailList = false;
    protected $showEdmCount= false;
    protected $showCompanyCountData = false;
    protected $filterBindingUserFlag =false;
    protected $showRolesFlag = false;
    protected $showAlibabaAccountInfoFlag = false;
    protected $systemModule = PrivilegeConstants::SYSTEM_MODULE_CRM;

    protected $specifyFields;
    protected $showFieldsInfo = true;
    protected $externalFields;
    protected $showAiBillingInfo = false;

    protected $showSwitchSafeLoginAvailable = false;
    // 新企业中心二期不再兼容ames展示
//    protected $showCompatAmes = true;

    protected $showUserUsedSpace = false;

    protected $showWhatsappCloudChannels = false;
    public function __construct($data = null)
    {
        if ($data !== null)
            $this->initData($data);

        $this->baseListSetting();
    }


    public function initData($data)
    {
        if( empty($data) )
            return;

        $this->userId = $data['user_id'];
        $this->clientId = $data['client_id'];
        $this->data = $data;
    }

    public function setData($data)
    {
        $this->initData($data);
        parent::setData($data);

    }

    /**
     * @param mixed $clientId
     */
    public function setClientId($clientId)
    {
        $this->clientId = $clientId;
    }

    /**
     * @param boolean $showInClientFlag
     */
    public function setShowInClientFlag($showInClientFlag)
    {
        $this->showInClientFlag = $showInClientFlag;
    }

    /**
     * @param mixed $adminType
     */
    public function setAdminType($adminType)
    {
        $this->adminType = $adminType;
    }

    /**
     * @param boolean $showAdminType
     */
    public function setShowAdminType($showAdminType)
    {
        $this->showAdminType = $showAdminType;
    }

    /**
     * @param boolean $showFreezeFlag
     */
    public function setShowFreezeFlag($showFreezeFlag)
    {
        $this->showFreezeFlag = $showFreezeFlag;
    }

    /**
     * @param boolean $showFrozenable
     */
    public function setShowFrozenable($showFrozenable)
    {
        $this->showFrozenable = $showFrozenable;
    }

    /**
     * @param boolean $showGlobalFreezeFlag
     */
    public function setShowGlobalFreezeFlag($showGlobalFreezeFlag)
    {
        $this->showGlobalFreezeFlag = $showGlobalFreezeFlag;
    }

    /**
     * @param boolean $showWxBindFlag
     */
    public function setShowWxBindFlag($showWxBindFlag)
    {
        $this->showWxBindFlag = $showWxBindFlag;
    }


    /**
     * @return boolean
     */
    public function isShowSafeLoginFlag(): bool
    {
        return $this->showSafeLoginFlag;
    }

    /**
     * @param boolean $showSafeLoginFlag
     */
    public function setShowSafeLoginFlag(bool $showSafeLoginFlag)
    {
        $this->showSafeLoginFlag = $showSafeLoginFlag;
    }



    /**
     * @param boolean $showBindingEmailInfo
     */
    public function setShowBindingEmailInfo($showBindingEmailInfo)
    {
        $this->showBindingEmailInfo = $showBindingEmailInfo;
    }

    /**
     * @param boolean $showBindStatus
     */
    public function setShowBindStatus($showBindStatus)
    {
        $this->showBindStatus = $showBindStatus;
    }


    /**
     * @param boolean $showBindUser
     */
    public function setShowBindUser($showBindUser)
    {
        $this->showBindUser = $showBindUser;
    }

    /**
     * @return boolean
     */
    public function getShowWxBindFlag()
    {
        return $this->showWxBindFlag;
    }

    /**
     * @return boolean
     */
    public function getShowBindingEmailInfo()
    {
        return $this->showBindingEmailInfo;
    }

    /**
     * @param boolean $showBindingUserList
     */
    public function setShowBindingUserList($showBindingUserList)
    {
        $this->showBindingUserList = $showBindingUserList;
    }

    /**
     * @return boolean
     */
    public function getShowBindUser()
    {
        return $this->showBindUser;
    }

    /**
     * @return boolean
     */
    public function getShowBindingUserList()
    {
        return $this->showBindingUserList;
    }

    /**
     * @param boolean $showUserDepartmentList
     */
    public function setShowUserDepartmentList($showUserDepartmentList)
    {
        $this->showUserDepartmentList = $showUserDepartmentList;
    }

    public function setShowRecursionDepartmentList($showRecursionDepartmentList)
    {
        $this->showRecursionDepartmentList = $showRecursionDepartmentList;
    }

    public function setShowUserSystem($showUserSystem)
    {
        $this->showUserSystem = $showUserSystem;
    }

    public function setShowUserSystemAvailable($showUserSystemAvailable)
    {
        $this->showUserSystemAvailable = $showUserSystemAvailable;
    }

    /**
     * @param bool $showUserUsedSpace
     */
    public function setShowUserUsedSpace(bool $showUserUsedSpace): void
    {
        $this->showUserUsedSpace = $showUserUsedSpace;
    }

    public function getShowUserDepartmentList()
    {
        return $this->showUserDepartmentList;
    }

    /**
     * @param boolean $showCanManageFlag
     */
    public function setShowCanManageFlag($showCanManageFlag)
    {
        $this->showCanManageFlag = $showCanManageFlag;
    }

    /**
     * @param mixed $viewUserId
     */
    public function setViewUserId($viewUserId)
    {
        $this->viewUserId = $viewUserId;
    }

    /**
     * @param boolean $showCanViewDetailFlag
     */
    public function setShowCanViewDetailFlag($showCanViewDetailFlag)
    {
        $this->showCanViewDetailFlag = $showCanViewDetailFlag;
    }

    /**
     * @param boolean $showUserMailList
     */
    public function setShowUserMailList($showUserMailList)
    {
        $this->showUserMailList = $showUserMailList;
    }

    /**
     * @param  $showEdmCount
     */
    public function setShowEdmCount($showEdmCount)
    {
        $this->showEdmCount = $showEdmCount;
    }

    /**
     * @param bool $filterBindingUserFlag
     */
    public function setFilterBindingUserFlag(bool $filterBindingUserFlag)
    {
        $this->filterBindingUserFlag = $filterBindingUserFlag;
    }

    /**
     * @param mixed $specifyFields
     */
    public function setSpecifyFields($specifyFields)
    {
        $this->specifyFields = $specifyFields;
    }

    /**
     * @param bool $showFieldsInfo
     */
    public function setShowFieldsInfo(bool $showFieldsInfo)
    {
        $this->showFieldsInfo = $showFieldsInfo;
    }

    /**
     * @param mixed $externalFields
     */
    public function setExternalFields($externalFields)
    {
        $this->externalFields = $externalFields;
    }

    /**
     * @param bool $showCompanyCountData
     */
    public function setShowCompanyCountData(bool $showCompanyCountData)
    {
        $this->showCompanyCountData = $showCompanyCountData;
    }

    /**
     * @param bool   $showRolesFlag
     * @param string $systemModule
     */
    public function setShowRolesFlag(bool $showRolesFlag, $systemModule = PrivilegeConstants::SYSTEM_MODULE_CRM)
    {
        $this->showRolesFlag = $showRolesFlag;
        $this->systemModule = $systemModule;
    }

    /**
     * @param bool $showAlibabaAccountInfoFlag
     */
    public function setShowAlibabaAccountInfoFlag(bool $showAlibabaAccountInfoFlag)
    {
        $this->showAlibabaAccountInfoFlag = $showAlibabaAccountInfoFlag;
    }


    public function setShowAiBillingInfo(bool $showAiBillingInfo) {
        $this->showAiBillingInfo = $showAiBillingInfo;
    }

    /**
     * @param bool $showSwitchSafeLoginAvailable
     */
    public function setShowSwitchSafeLoginAvailable(bool $showSwitchSafeLoginAvailable): void
    {
        $this->showSwitchSafeLoginAvailable = $showSwitchSafeLoginAvailable;
    }

    // 新企业中心二期不再兼容ames展示
//    public function setShowCompatAmes(bool $showCompatAmes): void
//    {
//        $this->showCompatAmes = $showCompatAmes;
//    }

    public function getProperties()
    {
        $properties = [];
        foreach ( $this as $k => $v)
            $properties[$k] = $v;

        return $properties;
    }

    public function setProperties(array $properties)
    {
        foreach ( $properties as $k => $v )
        {
            if( property_exists($this,$k))
                $this->{$k} = $v;
        }

    }


    public function getInviteUserFormatter()
    {
        $formatter = new InviteUserFormatter();
        $formatter->setProperties($this->getProperties());
        return $formatter;
    }

    public function getInviteUser($userId)
    {
        if( !isset($this->mapData['invite_user'][$userId]) )
        {
            $this->mapData['invite_user'][$userId] = InviteUserList::findInviteUser($this->clientId,$userId);
        }
        return $this->mapData['invite_user'][$userId]??null;
    }


    public function  getUserInfo($userId){

        if( !isset($this->mapData['user'][$userId]) )
        {
            $userInfo = new UserInfo($userId, $this->clientId);
            $this->mapData['user'][$userId] = $userInfo->isNew() ? null: $userInfo->getAttributes();
        }
        return $this->mapData['user'][$userId]??null;

    }


    public function getUserAccount($userId)
    {

        if( !isset($this->mapData['user_account'][$userId]) )
        {
            $this->mapData['user_account'][$userId] = (new Account())->loadById($userId)->getAttributes();
        }

        return $this->mapData['user_account'][$userId]??null;
    }

    public function buildMapData()
    {
        $list = $this->batchFlag ? $this->listData : [$this->data];
        $userIds = array_column($list, 'user_id');

        if (!$this->clientId) {
            throw  new \ProcessException('need param clientId');
        }

        $userMailMap = [];
        if ($this->showUserMailList || $this->showBindingEmailInfo) {
            $userMailListObj = new UserMailList();
            $userMailListObj->setClientId($this->clientId);
            $userMailListObj->setUserId($userIds);
            $userMailList = $userMailListObj->find();
            $userMailMap = array_reduce($userMailList, function ($carry, $item) {
                $carry[$item['user_id']][] = $item;
                return $carry;
            }, []);
        }

        $userAccountMap = [];
        if ($this->showWxBindFlag) {
            $userAccountList = Helper::getBatchAccount($userIds);
            $userAccountList = array_map(function ($account) {
                return $account->getAttributes();
            }, $userAccountList);

            $userAccountMap = array_combine(array_column($userAccountList ?? [], 'user_id'), $userAccountList);
        }

        $bindedUserMap = [];     //被绑定
        $bindingUserMap = [];    //绑定的
        $relativeUserIds = [];
        $allUserIds = $userIds;
        $userMap = array_column($list, null, 'user_id');
        if ($this->showBindUser || $this->showBindingUserList || $this->filterBindingUserFlag) {
            $relativeList = UserRelative::getBindedUserList($this->clientId);
            foreach ($relativeList as $relative) {
                $bindedUserMap[$relative['binding_user']] = $relative['binded_user'];
                $bindingUserMap[$relative['binded_user']][] = $relative['binding_user'];
                $relativeUserIds[] = $relative['binded_user'];
                $relativeUserIds[] = $relative['binding_user'];
            }

            $allUserIds = array_merge($allUserIds, $relativeUserIds);
            $allUserIds = array_values(array_unique($allUserIds));
            $notIncludeUserIds = array_diff($relativeUserIds, $userIds);
            if (!empty($notIncludeUserIds)) {
                $userInfoList = Helper::getBatchUserInfo($this->clientId, $notIncludeUserIds);
                foreach ($userInfoList as $info) {
                    $userMap[$info->user_id] = $info->getAttributes();
                }
            }
        }


        $freezeUserMap = [];
        if ($this->showFreezeFlag) {
//            $freezeUserIds = Helper::getFreezeUserIds($this->clientId);
//            $freezeUserMap = array_combine($freezeUserIds,$freezeUserIds);
            $freezeUserMap = [];
        }

        $clientUserMap = [];
        if ($this->showInClientFlag) {
            $clientUserIds = Helper::getActivationUserIds($this->clientId);
            $clientUserMap = array_combine($clientUserIds, $clientUserIds);
        }

        $adminTypeMap = [];
        if ($this->showAdminType) {
            $departmentMemberAdminUserIds = \common\library\department\Helper::getDepartmentMemberAdminList($this->clientId, $allUserIds);
            $adminTypeMap = array_reduce($userIds, function ($carry, $userId) use ($departmentMemberAdminUserIds) {
                $carry[$userId] = in_array($userId, $departmentMemberAdminUserIds) ? 1 : 0;
                return $carry;
            }, []);

            $adminUserId = PrivilegeService::getInstance($this->clientId, $this->viewUserId)->getAdminUserId();
            $adminTypeMap[$adminUserId] = 2;
        }

        $userDepartmentMap = [];
        if ($this->showUserDepartmentList) {
            $departmentService = new DepartmentService($this->clientId);
            $departmentService->setRecursionFlag($this->showRecursionDepartmentList);
            $userDepartmentMap = $departmentService->batchGetUserDepartmentList($allUserIds);
        }

        $userSystemMap = [];
        if ($this->showUserSystem) {
            $userSystemMap = PrivilegeService::getInstance($this->clientId)->getUserPrivilege()->getUserSystems($allUserIds);
        }

        $inviteUserMap =[];
        $inviteUserIds =  array_diff($allUserIds,array_keys($userMap));
        if( !empty($inviteUserIds) )
        {
            $inviteUserList = InviteUserList::findInviteByUserIds($this->clientId,$inviteUserIds);
            $inviteUserMap = array_combine(array_column($inviteUserList,'user_id'),$inviteUserList);
        }

        $externalFieldsMap =[];
        if( !empty($this->externalFields) )
        {
            $externalList = \UserInfoExternal::getExternalValueList($this->clientId,$allUserIds,$this->externalFields);

            foreach ($externalList as $item) {
                $externalFieldsMap[$item['user_id']][$item['key']] = $item['value'];
            }
        }

        $rolesMap = [];
        if ( !empty($this->showRolesFlag) )
        {
            $rolesMap = PrivilegeService::getInstance($this->clientId)->getUserRoleList($userIds, true, $this->systemModule);
        }

        $userKCoinMap = [];
        if ($this->showAiBillingInfo) {
            $userKCoinInfoList =  \UserInfoExternal::getExternalValueList($this->clientId,$allUserIds,[UserInfoExternal::EXTERNAL_KEY_K_COIN_AMOUNT]);
            $userIdToKCoinMap = array_column($userKCoinInfoList,'value','user_id');

            foreach ($allUserIds as $kCoinUserId) {
                $userKCoinMap[$kCoinUserId] = $userIdToKCoinMap[$kCoinUserId] ?? 0;
            }
        }

        $switchSafeLoginMap = [];
        if ($this->showSwitchSafeLoginAvailable)
        {
            $adminUserId = PrivilegeService::getInstance($this->clientId)->getAdminUserId();
            $user = \User::getLoginUser();
            $opUserId = $user->getUserId();

            $allAccountSafeLoginConfig = LoginService::getLoginSetting($this->clientId, 0, LoginService::LOGIN_SETTING_KEY_ALL_ALL_ACCOUNT_SAFE_LOGIN);

            // 未开启所有账号安全登录 || 主账号 则 允许自由开关安全登录
            if (($allAccountSafeLoginConfig['open_flag']?? 0) == 0 || $adminUserId == $opUserId)
            {
                $switchSafeLoginMap = array_fill_keys($userIds, BaseObject::ENABLE_FLAG_TRUE);
            }else
            {
                // 先 all_user => 0 再 replace exclude_user_ids => 1
                $switchSafeLoginMap = array_replace(
                    array_fill_keys($userIds, BaseObject::ENABLE_FLAG_FALSE),
                    array_fill_keys($allAccountSafeLoginConfig['exclude_user_ids'] ?? [], BaseObject::ENABLE_FLAG_TRUE)
                );
            }
        }

        $userUsedSpaceMap = [];
        if ($this->showUserUsedSpace) {
            // 多user 单key
            $externalList = \UserInfoExternal::getExternalValueList($this->clientId, $allUserIds, [UserInfoExternal::EXTERNAL_KEY_DISK_CLOUD_USED_SPACE, UserInfoExternal::EXTERNAL_KEY_MAIL_ATTACH_USED_SPACE]);

            $externalFieldsMap = [];
            foreach ($externalList as $item) {
                $externalFieldsMap[$item['user_id']][$item['key']] = intval($item['value']);
            }

            foreach ($allUserIds as $userId) {

                $userUsedSpaceMap[$userId] = [
                    UserInfoExternal::EXTERNAL_KEY_MAIL_ATTACH_USED_SPACE => $externalFieldsMap[$userId][UserInfoExternal::EXTERNAL_KEY_MAIL_ATTACH_USED_SPACE] ?? 0,
                    UserInfoExternal::EXTERNAL_KEY_DISK_CLOUD_USED_SPACE => $externalFieldsMap[$userId][UserInfoExternal::EXTERNAL_KEY_DISK_CLOUD_USED_SPACE] ?? 0,
                    'disk_total_used' => ($externalFieldsMap[$userId][UserInfoExternal::EXTERNAL_KEY_MAIL_ATTACH_USED_SPACE] ?? 0) + ($externalFieldsMap[$userId][UserInfoExternal::EXTERNAL_KEY_DISK_CLOUD_USED_SPACE] ?? 0)
                ];
            }
        }

        $userWhatsappCloudChannelsMap = [];
        if ($this->showWhatsappCloudChannels) {
            // 获取account
            $accountFilter = new WhatsappCloudAccountFilter($this->clientId);
            $accountFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
            $batchAccount = $accountFilter->find();
            $batchAccount->getFormatter()->baseListSetting();
            $result = $batchAccount->getAttributes();

            $userWhatsappCloudChannelsMap = array_reduce($result, function ($carry, $item) {
                $assignUserIds = $item['assign_users'];
                foreach ($assignUserIds as $userId) {
                    if (!isset($carry[$userId])) {
                        $carry[$userId] = [];
                    }
                    $carry[$userId][] = $item;
                }
                return $carry;
            },[]);
        }

        if ($this->showCompanyCountData) {
            $companyQuotaManager = new CompanyQuotaManager($this->clientId);
            $companyCountMap = $companyQuotaManager->getCompanyCountDataByUserIds($userIds);
        }

        $map = [
            'user_mail' =>$userMailMap,
            'user_account' =>$userAccountMap,
            'binded_user' => $bindedUserMap,
            'binding_user' => $bindingUserMap,
            'freeze_user' => $freezeUserMap,
            'client_user' => $clientUserMap,
            'admin_type' => $adminTypeMap,
            'user_department' =>$userDepartmentMap,
            'invite_user' => $inviteUserMap,
            'external_fields' =>$externalFieldsMap,
            'roles' => $rolesMap,
            'user_system' => $userSystemMap,
            'user_k_coin_map' => $userKCoinMap,
            'switch_safe_login' => $switchSafeLoginMap,
            'user_used_space_map' => $userUsedSpaceMap,
            'user_whatsapp_cloud_channels_map' => $userWhatsappCloudChannelsMap,
            'company_count_map' => $companyCountMap ?? [],
        ];

        $this->setMapData($map);

        parent::buildMapData();
    }

    public function isShowWhatsappCloudChannels(): bool
    {
        return $this->showWhatsappCloudChannels;
    }

    public function setShowWhatsappCloudChannels(bool $showWhatsappCloudChannels): void
    {
        $this->showWhatsappCloudChannels = $showWhatsappCloudChannels;
    }


    public function getCanManageUserFlag($userId,$departmentId=null)
    {
        if(!$this->viewUserId){
            throw new \RuntimeException("canMangeUserFlag need viewUser Params");
        }

        //超级管理员,只能自己管理自己
        $adminUserId = PrivilegeService::getInstance($this->clientId, $this->viewUserId)->getAdminUserId();
        if($adminUserId == $userId){
            return $adminUserId == $this->viewUserId ? 1:0;
        }

        $isOrganizationAdmin = PrivilegeService::getInstance($this->clientId, $this->viewUserId)->isOrganizationAdmin();
        //if 超级管理员|| 具有成员管理权限 ,都能管理这个用户
        return $isOrganizationAdmin?1:0;

    }

    public function getCanViewDetailFlag($userId)
    {
        if(!$this->viewUserId)
        {
            throw new \RuntimeException("canMangeUserFlag need viewUser Params");
        }
        //超级管理员能看所有,包括已禁用
        $admin = PrivilegeService::getInstance($this->clientId, $this->viewUserId)->getAdminUserId();
        if($admin == $this->viewUserId)
        {
            return 1;
        }

        if($userId == $admin)
        {
            if($this->viewUserId == $userId)
            {
                return 1;
            }else{
                return 0;
            }
        }

        // 只有部门管理员||超级管理员 才能查看该用户的具体分析数据
        $userIds = \User::getUserObject($this->viewUserId, $this->clientId)->getAllManageableUserIds(true);

        return in_array($userId,$userIds)?1:0;
    }


    /**
     * 构建指定字段
     * @param $data
     * @return array
     */
    protected function buildFieldsInfo($data)
    {
        $result = [];
        $userId = $data['user_id'];
        if( !$this->showFieldsInfo ) return $result;

        $specifyFields = $this->specifyFields;
        if ( $this->specifyFields === null )
        {
            $specifyFields = array_keys($data);
        }
        // 新企业中心二期不再兼容ames展示
//       if ($this->showCompatAmes)
//       {
//           $data['email'] = ($data['ames_email'] ?? '') ?: ($data['email'] ?? '');
//           $data['user_mobile'] = ($data['ames_mobile'] ?? '') ?: ($data['user_mobile'] ?? '');
//       }

        foreach ($specifyFields as $field ){
            switch ( $field ){
                case 'nickname':
                    $result[$field] =  (empty($data['nickname']) || $data['nickname'] == 'null') ? $data['email'] : $data['nickname'];
                    break;
                case 'real_name':
                    $result[$field] =  $data['family_name'].$data['second_name'];
                    break;
                case 'position':
                    $result['position'] = is_array($data['position']??'')?$data['position']??'':explode(';', $data['position']??'');
                    break;
                default :
                    $result[$field] = $data[$field]??'';
                    break;
            }
        }

        if( !empty($this->externalFields ) ) {

            $userExternalFields = $this->getMapData('external_fields', $userId)??[];
            foreach ($this->externalFields as $key) {
                $result[$key] = $userExternalFields[$key]??'';
            }
        }

        return $result;
    }

    /**
     * 默认基础字段
     */
    public function baseListSetting()
    {
        $this->setSpecifyFields([
            'email',
            'nickname',
            'real_nickname',
            'avatar',
            'family_name',
            'second_name',
            'real_name',
            'gender',
            'position',
            'birthday',
            'user_mobile',
            'ames_mobile',
            'enable_flag',
            'mobile_verify',
            'country_code',
            'user_status',
            'type',
        ]);
    }

    /**
     * me.xiaoman.cn userInfo 额外返回字段
     * @return void
     */
    public function personInfoSetting()
    {
        $this->specifyFields = array_merge($this->specifyFields, ['version', 'client_id', 'safe_login']);
    }

    /**
     * 客户数量限制用户列表
     */
    public function customerLimitListSetting()
    {
        $this->setSpecifyFields([
            'email',
            'nickname',
            'customer_limit',
        ]);

        $this->setShowUserDepartmentList(true);
        $this->setShowCompanyCountData(true);
    }

    /**
     * edm分配的用户列表
     */
    public function edmAllocateListSetting()
    {

        $this->baseListSetting();
        $this->setShowEdmCount(true);
        $this->setShowUserDepartmentList(true);

    }

    public function desktopListSetting()
    {
        $this->setSpecifyFields(
            [
                'email',
                'nickname',
                'avatar',
                'family_name',
                'second_name',
                'gender',
                'position',
                'user_mobile',
                'enable_flag',
            ]
        );
        $this->setShowUserDepartmentList(true);
    }

    public function detailInfoSetting($viewUserId)
    {
        $this->baseListSetting();

        $this->setShowFrozenable(1);
        $this->setShowGlobalFreezeFlag(1);
        $this->setShowBindingEmailInfo(1);
        $this->setShowWxBindFlag(1);
        $this->setShowSafeLoginFlag(1);
        $this->setShowBindStatus(1);
        $this->setShowBindUser(1);
        $this->setShowFreezeFlag(1);
        $this->setViewUserId($viewUserId);
        $this->setShowCanManageFlag(1);
        $this->setShowAdminType(1);
        $this->setShowCanViewDetailFlag(1);
        $this->setShowUserMailList(1);
        $this->setShowUserDepartmentList(true);
    }


    public function format($data=null)
    {
        if($data===null){
            $data = $this->data;
        }

        $userId = $data['user_id'];
        $result = [
            'user_id' => $data['user_id'],
            'user_info_id' => $data['user_info_id'],
        ];

        $result = array_merge($result,$this->buildFieldsInfo($data));
        $result['inviting_flag'] = 0;
        $result['employee_no'] = $data['employee_no'] ?? '';
        $result['type'] = $result['type']??\Constants::TYPE_STANDARD_ACCOUNT;


        if($this->showEdmCount){
            $result['current_count_company'] = $data['current_count_company'];
        }

        if($this->showInClientFlag) {
            $result['in_client_flag'] =  $this->getMapData('client_user',$userId);
        }

        if($this->showAdminType) {
            $result['admin_type'] = $this->getMapData('admin_type',$userId);
        }

        if($this->showFreezeFlag) {
            $result['freeze_flag'] =  $data['enable_flag'] == UserInfo::ENABLE_FLASE ? 1 : 0;;
        }

        if($this->showBindUser ) {
            $nickname = '';
            $bindedUserId = $this->getMapData('binded_user',$userId);
            if ($bindedUserId) {
                $bindedUserInfo = $this->getUserInfo($bindedUserId)??$this->getInviteUser($bindedUserId);
                $nickname = $bindedUserInfo && $bindedUserInfo['nickname'] ? $bindedUserInfo['nickname'] : $bindedUserInfo['email'];
            }

            $result['binding'] =  $nickname;
        }

        if($this->showBindingUserList)
        {
            $this->setShowBindingUserList(false);
            $bindingUserInfoList = [];
            $bindingUserIds = $this->getMapData('binding_user',$userId)??[];
            foreach($bindingUserIds as $bindingUserId)
            {
                if($userId == $bindingUserId) continue;
                $userInfo = $this->getUserInfo($bindingUserId);
                if( !empty($userInfo) )
                {
                    $bindingUserInfoList[]  = $this->format($userInfo);
                }else{
                    $inviteUserInfo = $this->getInviteUser($bindingUserId);
                    if( !empty($inviteUserInfo) )
                        $bindingUserInfoList[]  = $this->formatInviteUser($inviteUserInfo);
                }
            }
            $result['binding'] = $bindingUserInfoList;
            $this->setShowBindingUserList(true);
        }


        if($this->showFrozenable)
        {
            $result['frozenable'] =  !empty($this->getMapData('binding_user',$userId)) ? Account::FROZENABLE_FALSE : Account::FROZENABLE_TRUE;
        }

        if($this->showGlobalFreezeFlag)
        {
            $result['global_freeze_flag'] =  $data['enable_flag'] == UserInfo::ENABLE_FLASE ? 1 : 0;
        }

        if($this->showWxBindFlag)
        {
            $userAccount = $this->getUserAccount($userId);
            $result['wx_openid'] = '';
            $result['wx_nickname'] = '';
            $result['wx_bind'] =  !empty($userAccount['wx_openid']);
            $result['wechat_bind_status'] = $result['wx_bind'];
            if ($result['wx_bind']) {
                $result['wx_nickname'] = $userAccount['wx_nickname'];
            }
        }

        if($this->showSafeLoginFlag)
        {
            $result['safe_login'] = ($data['safe_login'] == 1);
        }

        if($this->showBindingEmailInfo)
        {
            //冻结和邀请中默认不检测
            if ($data['enable_flag'] == UserInfo::ENABLE_FLASE || $data['user_status'] == UserInfo::USER_STATUS_UNACTIVE) {
                $validFlag = UserMail::VALID_FLAG_SUCCESS;
                $bindStatus = Account::BIND_STATUS_OK;
            }else{
                $userMailList = $this->getMapData('user_mail',$userId)??[];
                $validFlag = UserMail::VALID_FLAG_UNCHECK;
                $bindStatus = Account::BIND_STATUS__NOT;
                foreach ($userMailList as $userMailItem) {
                    $bindStatus = Account::BIND_STATUS_OK;
                    if($userMailItem['valid_flag'] == UserMail::VALID_FLAG_ERROR || $userMailItem['valid_flag'] == UserMail::VALID_FLAG_UNCHECK){
                        $validFlag = $userMailItem['valid_flag'];
                        break;//只要有一个邮箱异常即视为异常
                    }else{
                        $validFlag = $userMailItem['valid_flag'];
                    }
                }
            }

            $result['email_address'] =  '';
            $result['email_valid_flag'] = $validFlag;
            $result['bind_status'] = $bindStatus;
            $result['user_status'] = (int)$data['user_status'];
        }

        if($this->showUserDepartmentList)
        {
            $result['department'] = $this->getMapData('user_department',$userId)??[];
        }

        if ($this->showUserSystem)
        {
            $result['user_system'] = $this->getMapData('user_system', $userId) ?? [];
        }

        if ($this->showUserSystemAvailable)
        {
            $result['user_system_available'] = array_values(array_diff(PrivilegeService::getInstance($this->clientId)->getClientPrivilege()->getSystemModules(), $this->getMapData('user_system', $userId) ?? []));
        }

        if($this->showCanManageFlag)
        {
            $result['can_manage_flag'] = $this->getCanManageUserFlag($this->userId);
        }

        //能管理就能看用户分析数据
        if($this->showCanViewDetailFlag)
        {
            $result['can_view_detail_flag'] = $this->getCanViewDetailFlag($this->userId);
        }

        if($this->showUserMailList)
        {
            $result['user_mail_list'] = $this->getMapData('user_mail',$userId)??[];
        }

        if ($this->showCompanyCountData) {
            $result = array_merge($result, $this->getMapData('company_count_map', $userId) ?? []);
        }

        if ($this->showRolesFlag) {
            $result['roles'] = array_values($this->getMapData('roles', $userId) ?? []);
        }
        if ($this->showAlibabaAccountInfoFlag)
        {
           $result['alibaba_data'] =  AlibabaService::getInstance($this->clientId,$this->userId)->getAccountList();
        }

        if ($this->showAiBillingInfo) {
            $result['ai_billing_info'] = [
                'user_k_coin' => $this->getMapData('user_k_coin_map',$userId) ?? 0
            ];
        }

        if ($this->showSwitchSafeLoginAvailable)
        {
            $result['switch_safe_login_available'] = intval($this->getMapData('switch_safe_login', $userId));
        }

        if ($this->showUserUsedSpace)
        {
            $userUsedSpace = $this->getMapData('user_used_space_map', $userId) ?? [];
            $result[UserInfoExternal::EXTERNAL_KEY_DISK_CLOUD_USED_SPACE] = $userUsedSpace[UserInfoExternal::EXTERNAL_KEY_DISK_CLOUD_USED_SPACE] ?? 0;
            $result[UserInfoExternal::EXTERNAL_KEY_MAIL_ATTACH_USED_SPACE] = $userUsedSpace[UserInfoExternal::EXTERNAL_KEY_MAIL_ATTACH_USED_SPACE] ?? 0;
            $result['disk_total_used'] = $userUsedSpace['disk_total_used'] ?? 0;
        }

        if ($this->showWhatsappCloudChannels) {
            $result['whatsapp_channels'] = $this->getMapData('user_whatsapp_cloud_channels_map', $userId) ?? [];
        }

        return $result;

    }



    public function result()
    {
        $result = [];
        $this->buildMapData();

        if (!$this->batchFlag)
        {
            $data = $this->data;
            if ($this->needStrip)
                $data = $this->strip($data);

            $result = $this->format($data);
            return $result;
        }

        foreach ($this->listData as $data)
        {
            if ($this->needStrip)
                $data = $this->strip($data);

            //如果,设置过滤已被绑定的用户
            if($this->filterBindingUserFlag){
                $bindedUser = $this->getMapData('binded_user',$data['user_id']);
                //被绑定了&&不是自己绑定自己
                if(!empty($bindedUser) && $data['user_id'] != $bindedUser)
                    continue;
            }

            $this->constructListResult($result, $this->format($data));
        }

        return $result;

    }

    public function formatInviteUser($data){

        return $this->getInviteUserFormatter()->format($data);

    }


}
