<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2020/9/3
 * Time: 3:52 PM
 */

namespace common\library\report\monitor;

use AlibabaCloud\Client\AlibabaCloud;
use common\library\coroutine\support\Parallel;
use Swoole\Coroutine\Http\Client;
use Swoole\Runtime;
class MonitorHelper
{
    const GRAFANA_TOKEN = 'eyJrIjoiZjlxakdaQ3piSFhrUzNpSlhxODNjTGdDbjJYQlFHRFgiLCJuIjoiY2F5bGV5IiwiaWQiOjF9';
    const GRAFANA_HOST = 'gp.xiaoman.cc';

    const REPORT_URL = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1fd0cf34-7a71-42dc-a339-0811b4696921';


    public static function getGranfanaStat($workPath)
    {
        Runtime::enableCoroutine(SWOOLE_HOOK_ALL | SWOOLE_HOOK_CURL ^ SWOOLE_HOOK_SLEEP);

        $dashboardSlugs = [
            'jfYDnZqiz', //fpm机器性能
            '-5vYj13mk', //callback机器性能
            'havS4NiZk', // pgsql 库
//            'L6Vtme5Wz', //php接口性能
        ];

        $parallel = new Parallel();

        foreach ( $dashboardSlugs as $dashboardSlug)
        {
//              self::downloadGranfanaDashboard($workPath, $dashboardSlug);
            $parallel->add(function ()use($workPath, $dashboardSlug){
               return  self::downloadGranfanaDashboard($workPath, $dashboardSlug);
            });
        }

        $result = $parallel->wait(  false);
        return $result;
    }

    public static function downloadGranfanaDashboard($workPath, $dashboardSlug)
    {
        $data = [
            'slug' => $dashboardSlug,
            'url' => 'http://'.self::GRAFANA_HOST,
            'title' => '',
            'image' => ''
        ];

        $host = self::GRAFANA_HOST;
        $httpClient = new Client($host, 80);
        $httpClient->setHeaders([
            'Authorization' => 'Bearer '.self::GRAFANA_TOKEN,
        ]);

        $httpClient->get("/api/dashboards/uid/{$dashboardSlug}");

        $json = $httpClient->body;
        $dashboardRes = json_decode($json, true);
        if(empty($dashboardRes) || !isset($dashboardRes['meta']))
            return $data;

        $data['title'] = $dashboardRes['meta']['folderTitle'];
        $data['url'] = $data['url'].$dashboardRes['meta']['url'];
        $dashboardUrl = str_replace('/d/','/d-solo/',$dashboardRes['meta']['url']);

        $panelList = [];
        $imageW = 0;
        $imageH = 0;

        $folder = "{$workPath}/cache/{$dashboardSlug}";
        if(!file_exists($folder))
        {
            mkdir($folder, 0755, true);
        }

        $parallel = new Parallel(5);
        foreach ($dashboardRes['dashboard']['panels'] as $item) {
            $item['gridPos']['x'] = $item['gridPos']['x'] * 100;
            $item['gridPos']['y'] = $item['gridPos']['y'] * 100;
            $item['gridPos']['w'] = $item['gridPos']['w'] * 100;
            $item['gridPos']['h'] = $item['gridPos']['h'] * 100;
            $panel = ['id' => $item['id'], 'title' => $item['title'], 'gridPos' => $item['gridPos'], 'url' => "/render/{$dashboardUrl}?kiosk=tv&panelId={$item['id']}&width={$item['gridPos']['w']}&height={$item['gridPos']['h']}", 'pngPath' => "{$folder}/{$dashboardSlug}_panel_{$item['id']}.png"];
            $imageW = max($imageW, $item['gridPos']['x'] + $item['gridPos']['w']);
            $imageH = max($imageH, $item['gridPos']['y'] + $item['gridPos']['h']);
            $panelList[] = $panel;
            $parallel->add(function () use ($host, $panel) {
                $httpClient = new Client($host, 80);
                $httpClient->setHeaders([
                    'Authorization' => 'Bearer ' . self::GRAFANA_TOKEN,
                ]);

                $httpClient->download($panel['url'], $panel['pngPath']);
            });

        }

        $parallel->wait(false);

        $filename = $folder . '/' . $dashboardSlug . '.png';
        $image = imagecreate($imageW, $imageH); // 背景图片
//        $image = imagecreatetruecolor($imageW, $imageH); // 背景图片
//            $color   = imagecolorallocate($image, 202, 201, 201); // 为真彩色画布创建白色背景，再设置为透明
//            imagefill($image, 0, 0, $color);
//            imageColorTransparent($image, $color);

        foreach ($panelList as $panel) {
            $panelImage = @imagecreatefrompng($panel['pngPath']);
            if (empty($panelImage))
            {
                @imagedestroy($panelImage);
                continue;
            }


            imagecopyresized($image, $panelImage, $panel['gridPos']['x'], $panel['gridPos']['y'], 0, 0, $panel['gridPos']['w'], $panel['gridPos']['h'], $panel['gridPos']['w'], $panel['gridPos']['h']);
            imagedestroy($panelImage);
        }

        imagepng($image, $filename);
        imagedestroy($image);
        $data['image'] = $filename;

        return $data;
    }


    public static function getDbStat()
    {

        //阿里云基于 GuzzleHttp  ,默认是使用curl ,不开启协程环境配置, 默认curl是不支持协程的
//        Runtime::enableCoroutine(SWOOLE_HOOK_ALL | SWOOLE_HOOK_CURL ^ SWOOLE_HOOK_SLEEP);
        // init client options
        AlibabaCloud::accessKeyClient(
            'LTAI4GCkA7DkSKLUdbB68tJy',
            '******************************'
        )->regionId('cn-hangzhou') // replace regionId as you need
        ->asDefaultClient();

        $query = [
            'RegionId' => 'cn-hangzhou',
            'Engine' => 'MySQL',
            'DBInstanceType' => 'Primary',
            'PageSize' => 100,
        ];

        $response = AlibabaCloud::rpc()
            ->regionId('cn-hangzhou')
            ->product('rds')
            ->host('rds.aliyuncs.com')
            ->version('2014-08-15')
            ->format('JSON')
            ->method('GET')
            ->action('DescribeDBInstances')
            ->options(['query' => $query])
            ->request();


        $parallel = new Parallel();
        $nowTime = time();
        $query = [
            'Key' => 'MySQL_ThreadStatus,MySQL_MemCpuUsage,MySQL_IOPS',
            'StartTime' => gmdate('Y-m-d\TH:i\Z', $nowTime-60),
            'EndTime' => gmdate('Y-m-d\TH:i\Z', $nowTime)
        ];

        foreach ($response->Items->DBInstance as $item )
        {
            if(strpos($item->DBInstanceDescription,'v4_') ===false
                && strpos($item->DBInstanceDescription,'v5_') ===false
                && strpos($item->DBInstanceDescription,'pro_') ===false
            )
                continue;

            $dbInstance = [
                'DBInstanceId' => $item->DBInstanceId,
                'DBInstanceDescription' => $item->DBInstanceDescription
            ];

            $parallel->add(function ()use($dbInstance, $query)
            {
                $query['DBInstanceId'] = $dbInstance['DBInstanceId'];
                $infoResponse = AlibabaCloud::rpc()
                    ->regionId('cn-hangzhou')
                    ->product('rds')
                    ->host('rds.aliyuncs.com')
                    ->version('2014-08-15')
                    ->format('JSON')
                    ->method('GET')
                    ->action('DescribeDBInstancePerformance')
                    ->options(['query' => $query])
                    ->request();

                $dbInstance['PerformanceKeys'] = [];
                foreach ($infoResponse->PerformanceKeys as $elem)
                {
                    foreach ($elem as $v)
                    {
                        $performanceValue = end($v->Values->PerformanceValue);
                        $dbInstance['PerformanceKeys'][$v->Key] = $performanceValue->Value??'';
                    }
                }

                return $dbInstance;

            });


        }

        $dbInstances =  $parallel->wait(false);
        return $dbInstances;
    }

    public static function sendStartMonitorReport($reqData)
    {
        $version = $reqData['info']['version'];
        $system = $version['system'];
        $branch = $version['branch'];
        $commitId = $version['commit_id'];
        $publicDate =  date('Y-m-d H:i:s');
        $monitorTime  = ceil($reqData['time']/(60*1000)) .' 分钟';

        $content = <<<MD
##  **[发布新版本通知](http://c.xiaoman.cc/task)**
>系统: <font color="warning">[{$system}] </font>
>branch: [{$branch}](http://git.xiaoman.co/projects/CRM/repos/php-crm/browse?at=refs%2Fheads%2Ffeature%2Fseptember.k9)
>commit: [{$commitId}](http://git.xiaoman.co/projects/CRM/repos/php-crm/commits/d73e90367ee5c2b986a62e36a7d4ccbf0a2eac18)
>发布时间: {$publicDate}
>监控时间: {$monitorTime}
MD;
        $message = [
            'msgtype' => 'markdown',
            'markdown' => [
                'content' => $content
            ]
        ];

        self::sendMessage($message);
    }



    public static function sendVersionMonitorReport($workPath, $version,$modules, $timeInfo)
    {
        $t1 = time();

        $system = $version['system'];
        $branch = $version['branch'];
        $commitId = $version['commit_id'];
        $publicDate =  date('Y-m-d H:i:s', $timeInfo['begin_time']);
        $monitorTime  = ceil($timeInfo['time']/(60*1000)) .' 分钟';
        $frequencyTime = ceil($timeInfo['ms']/(60*1000)) .' 分钟';

        $content = <<<MD
##  **[发版监控信息](http://c.xiaoman.cc/task)**
>系统: <font color="warning">[{$system}] </font>
>branch: [{$branch}](http://git.xiaoman.co/projects/CRM/repos/php-crm/browse?at=refs%2Fheads%2Ffeature%2Fseptember.k9)
>commit: [{$commitId}](http://git.xiaoman.co/projects/CRM/repos/php-crm/commits/d73e90367ee5c2b986a62e36a7d4ccbf0a2eac18)
>发布时间: {$publicDate}
>监控时间: {$monitorTime}
>监控频率: {$frequencyTime}

MD;

        if( $modules['rds']?? true)
        {
            $dbStats = self::getDbStat();
            $content .=<<<MD
#### <font color="warning">RDS性能概况</font>

MD;
            $content .= "> <font color='comment'>".str_pad('实例名称',25, ' ').str_pad('内存/cpu使用率',25).str_pad('活跃/总连接数',25).str_pad('IOPS',25)."</font>\n";

            foreach ($dbStats as $dbStat )
            {
                $content .= "> <font color='comment'>" .str_pad($dbStat['DBInstanceDescription'], 25).str_pad($dbStat['PerformanceKeys']['MySQL_MemCpuUsage'], 25).str_pad($dbStat['PerformanceKeys']['MySQL_ThreadStatus'], 25).str_pad(strval($dbStat['PerformanceKeys']['MySQL_IOPS']), 25) ."</font> \n";
            }

            $message = [
                'msgtype' => 'markdown',
                'markdown' => [
                    'content' => $content
                ]
            ];

            self::sendMessage($message);

        }



        if( $modules['ecs']?? true )
        {
            $content =<<<MD
#### <font color="warning">ECS性能概况</font>

> [fpm机器性能](http://gp.xiaoman.cc/d/jfYDnZqiz/fpmji-qi-xing-neng?orgId=1)
> [fpm-callback-script机器性能](http://gp.xiaoman.cc/d/-5vYj13mk/fpm-callback-scriptji-qi-xing-neng-jian-kong?orgId=1&refresh=5s)
> [pgsq机器性能](http://gp.xiaoman.cc/d/havS4NiZk/postgre_cpu_summary?orgId=1)
> [php接口性能](http://gp.xiaoman.cc/d/L6Vtme5Wz/php-jie-kou-xing-neng?orgId=1)
MD;

            $message = [
                'msgtype' => 'markdown',
                'markdown' => [
                    'content' => $content
                ]
            ];

            self::sendMessage($message);
        }

        if( $modules['grafana']?? true )
        {
            $granfanaStats =  self::getGranfanaStat($workPath);
            foreach ( $granfanaStats as $granfanaStat)
            {

                $imagePath = $granfanaStat['image'];
                if( !file_exists($imagePath))
                    continue;

                $message = [
                    'msgtype' => 'image',
                    'image' => [
                        'base64' => base64_encode(file_get_contents($imagePath)),
                        'md5' => md5_file($imagePath)
                    ]
                ];

                self::sendMessage($message);

            }
        }


       var_dump(time()-$t1);

    }

    public static function sendMessage($data)
    {

        //先关闭curl的协程, 因为编译没配置ssl
        Runtime::enableCoroutine(SWOOLE_HOOK_ALL  ^ SWOOLE_HOOK_SLEEP);
        if (is_array($data)) {
            $data = json_encode($data, JSON_UNESCAPED_UNICODE);
        }
        $header = ["content-type:application/json"];

        //http post 请求连接地址
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, self::REPORT_URL);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER  , $header);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        $ret = curl_exec($ch);
        if ($ret === false) {
            $error = curl_error($ch);
            $errorCode = curl_errno($ch);
            $error .= ' error_no:'.$errorCode;
        }
        curl_close($ch);
        var_dump($ret);
        Runtime::enableCoroutine(SWOOLE_HOOK_ALL | SWOOLE_HOOK_CURL ^ SWOOLE_HOOK_SLEEP);
        return $ret;
    }
}
