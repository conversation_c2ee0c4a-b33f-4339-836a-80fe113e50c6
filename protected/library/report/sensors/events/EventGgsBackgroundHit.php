<?php

/**
 * 神策平台数据上报
 * Created by PhpStorm.
 * User: yoyo
 * Date: 2021/5/11
 * Time: 14:49
 */

namespace common\library\report\sensors\events;

class EventGgsBackgroundHit extends AbstractEvent
{

    const EVENT_GGS_BACKGROUND_HIT = 'event_ggs_background_hit';//ggs背调数据命中

    public function setParams(array $params)
    {
        $this->params = $params;
    }

    public function report()
    {
        $eventData = [];
        try {
            $params = array_merge($this->defaultParams(), $this->params);
            $eventData = [
                'event' => self::EVENT_GGS_BACKGROUND_HIT,
                'client_id' => $this->clientId,
                'user_id' => $this->userId,
                'params' => $params
            ];

            $this->pushQueue($eventData);
            \LogUtil::info("EventGgsBackgroundHit report, eventData:" . json_encode($eventData));
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg = $e->getMessage();
            $paramString = json_encode($eventData);
            \LogUtil::info("EventGgsBackgroundHit Error track pushqueue:code={$code}, clientId={$this->clientId}, userId={$this->userId}, eventData={$paramString},msg={$msg}");
        }
    }

}