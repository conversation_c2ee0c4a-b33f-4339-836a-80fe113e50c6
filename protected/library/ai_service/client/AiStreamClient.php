<?php

namespace common\library\ai_service\client;

use common\library\ai_agent\AiAgentConstants;
use common\library\ai_agent\api\AIClient;
use common\library\ai_agent\exception\AiAgentException;
use common\library\ai_agent\Helper;
use common\library\ai_agent\message\Card;
use common\library\ai_agent\message\ChatQualityCard;
use common\library\ai_agent\message\component\Button;
use common\library\ai_service\AiServiceConstant;
use common\library\util\sse\SseFrame;
use common\library\util\sse\SseResponse;

class AiStreamClient
{
    // 实例
    protected static $instance;

    protected $model;
    protected $prompt;
    protected $userContent;
    protected $interface;
    protected $extraReturn = [];
    protected $assistantContent;
    protected $lastUserContent;


    public function setModel($model)
    {
        $this->model = $model;
    }

    public function setPrompt($prompt)
    {
        $this->prompt = $prompt;
    }

    public function setUserContent($userContent)
    {
        $this->userContent = $userContent;
    }

    public function setAssistantContent($assistantContent, $lastUserContent) {
        $this->assistantContent = $assistantContent;
        $this->lastUserContent = $lastUserContent;
    }

    public function setInterface($interface)
    {
        $this->interface = $interface;
    }

    public function setExtraReturn($extraReturn)
    {
        $this->extraReturn = $extraReturn;
    }

    public static function getInstance()
    {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function getUrl($interface)
    {
        $config = \Yii::app()->params['inner_api'][AiServiceConstant::GPT_API_MODULE];
        $host = $config['host'];
        $interface = $config['interface'][$interface];

        return $host . $interface;
    }

    public function buideParams()
    {
        $prompt = $this->prompt;
        $userContent = $this->userContent;
        $model = $this->model;

        if (empty($prompt) || empty($userContent) || empty($model)) {
            throw new \RuntimeException("参数错误！");
        }

        $supportSystemRole = !str_starts_with($this->model, 'qwen'); // 阿里千问系列不支持system角色
        $gptMessage = [
            [
                'role' => $supportSystemRole ? 'system' : 'user',
                'content' => $prompt
            ],
        ];
        if (!empty($this->assistantContent) && !empty($this->lastUserContent)) {
            $gptMessage = array_merge($gptMessage, [
                [
                    'role' => 'user',
                    'content' => $this->lastUserContent
                ], [
                    'role' => 'assistant',
                    'content' => $this->assistantContent,
                ], [
                    'role' => 'user',
                    'content' => $this->userContent
                ]
            ]);
        } else {
            $gptMessage[] = [
                'role' => 'user',
                'content' => $this->userContent
            ];
        }

        return match ($model) {
            AiServiceConstant::AZURE_OPENAI_GPT_THREE,
            AiServiceConstant::AZURE_OPENAI_GPT_THREE_MORE,
            AiServiceConstant::AZURE_OPENAI_GPT_FOUR,
            AIClient::AZURE_OPENAI_GPT_FOUR_TURBO,
            AiServiceConstant::SERVICE_QWEN_TURBO,
            AiServiceConstant::SERVICE_QWEN_MAX,
            AiServiceConstant::SERVICE_QWEN_PLUS,
            AiServiceConstant::SERVICE_QWEN_MAX_LONGCONTEXT => [
                'service' => $model,
                'messages' => $gptMessage,
            ],
            AiServiceConstant::AZURE_OPENAI_CODE_DAVINCI => [
                'service' => $model,
                'prompt' => $prompt . $userContent,
                'max_tokens' => 2048
            ],
            AiServiceConstant::XUN_FEI => [
                "service" => $model,
                "header" => [
                    "app_id" => "12345", # 随便填，底层会自动填充
                    "uid" => "12345"
                ],
                "parameter" => [
                    "chat" => [
                        "domain" => "general",
                        "temperature" => 0.5,
                        "max_tokens" => 4096
                    ]
                ],
                "payload" => [
                    "message" => [
                        "text" => [
                            ["role" => "user", "content" => $prompt . $userContent]
                        ]
                    ]
                ]
            ],
            AiServiceConstant::WEN_XIN => [
                'service' => $model,
                "messages" => [
                    ['role' => 'user', 'content' => $prompt . $userContent]
                ]
            ],
            AiServiceConstant::GOOGLE => [
                'service' => $model,
                'instances' => [
                    ['prompt' => $prompt.$userContent]
                ],
                'parameters' => [
                    'temperature' => 0.2,
                    'maxOutputTokens' => 256,
                    'topK' => 40,
                    'topP' => 0.95
                ]
            ],
            AiServiceConstant::ICBU => [
                'service' => $model,
                'payload' => [
                    'content' => $prompt.$userContent
                ]
            ],
            default => [],
        };
    }

    public function streamAiReplyFormat($format, $clientId, $userId, $aiAgentType, $conversationId, $aiRiskLanguage, $trimPrefix = '')
    {
        $response = new SseResponse();

        $allContents = '';
        $aiReplyList = [];
        $trimPrefixLen = strlen($trimPrefix);
        $prefixBuf = '';
        foreach ($this->iteratorOfAiReply() as $item) {
            $content = $item['content'] ?? '';
            if ($trimPrefix && $trimPrefixLen > strlen($prefixBuf)) {
                $prefixBuf .= $content;
                continue;
            }
            if ($prefixBuf && str_starts_with($prefixBuf, $trimPrefix)) {
                $content = substr($prefixBuf, $trimPrefixLen) . $content;
                $item['content'] = $content;
                $prefixBuf = '';
                $trimPrefix = '';
            }
            $aiReplyList[] = $item;
            $allContents .= $item['content'] ?? '';
        }
        $aigcRiskInfo = \common\library\ai_agent\Helper::getAigcRiskInfo(
            $clientId,
            $userId,
            $allContents,
            $aiAgentType,
            $conversationId,
            $conversationId,
            AiAgentConstants::AI_AGENT_ROLE_TYPE_SYSTEM,
            $aiRiskLanguage
        );

        if (!Helper::isRiskPassed($aigcRiskInfo)) {
            $defaultResponse = [
                'context' => [
                    'content' => "抱歉，我暂不支持回答此问题，请重新输入"
                ],
                'status' => 1
            ];

            $format = (new \common\library\ai_agent\message\Text())->withConversationId($conversationId);
            \common\library\ai_agent\Helper::ResponseSse($defaultResponse, $format);
            return [
                'gpt_answer' => $allContents,
                'return_answer' => '抱歉，我暂不支持回答此问题，请重新输入'
            ];
        }

        $initMsg = $format->getSkeletonMessage();
        if (null !== $initMsg) {
            $response->writeJson(array_merge($initMsg, $this->extraReturn));
        }

        foreach ($aiReplyList as $item) {
            $status = $item['status'] ?? 0;
            if ($status != AiAgentException::MESSAGE_STATUS_ERROR) {
                $item = array_merge($format->formatMessage($item), $this->extraReturn);
            } else {
                $item = array_merge($format->formatErrorMessage($item), $this->extraReturn);
            }

            $response->writeJson($item);
        }

        $item = $format->formatAllContent($allContents);
        if (null !== $item) {
            $response->writeJson(array_merge($item, $this->extraReturn));
        }

        $closeMessage = $format->getCloseMessage();
        if (null !== $closeMessage) {
            $response->writeJson($closeMessage);
        }
        $response->complete();

        return [
            'gpt_answer' => $allContents,
            'return_answer' => $allContents
        ];
    }

    public function iteratorOfAiReply(): \Generator
    {
        $client = new \GuzzleHttp\Client([
            // 设置超时时间为60秒
            'timeout' => 60,
        ]);
        $config = \Yii::app()->params['inner_api'][AiServiceConstant::GPT_API_MODULE];
        $host = $config['host'];

        $params = $this->buideParams();
        $params['stream'] = true;

        try {
            $response = $client->request('POST', $this->getUrl($this->interface), [
                'headers' => [
                    'Host' => $host,
                    'User-Agent' => 'Chrome/49.0.2587.3',
                    'Accept' => 'text/event-stream',
                    'Cache-Control' => 'no-cache',
                    'Connection' => 'keep-alive',
                ],
                'json' => $params,
            ]);
        } catch (\Exception $exception) {
            \LogUtil::info("interface {$this->interface} params: ".json_encode($params));
            \LogUtil::info($exception->getTraceAsString());

            yield [
                'status' => AiAgentException::MESSAGE_STATUS_ERROR,
                'content' => \Yii::t('common', 'System busy, please try again later')
            ];
            return;
        }

        while (!$response->getBody()->eof()) {
            $line = \GuzzleHttp\Psr7\Utils::readLine($response->getBody());
            if (str_starts_with($line, 'data: ')) {
                $line = substr($line, 6);
            }

            if (empty($line)) {
                continue;
            }

            $result = json_decode($line, true);
            if (!empty($this->extraReturn)) {
                $result = array_merge($result, $this->extraReturn);
            }

            yield $result;

            if (!empty($resData['status']) && $resData['status'] == 2) {
                \LogUtil::error('iteratorOfAIReply finish', ['resData' => json_encode($resData)]);
                break;
            }
        }
    }

    /**
     * @param int $conversationId
     * @param string $gptAnswer
     * @return string[]
     */
    protected function returnFail(int $conversationId, string $gptAnswer): array
    {
        $defaultResponse = [
            'context' => [
                'content' => "抱歉，我暂不支持回答此问题，请重新输入"
            ],
            'status' => 1
        ];

        $format = (new \common\library\ai_agent\message\Text())->withConversationId($conversationId);
        \common\library\ai_agent\Helper::ResponseSse($defaultResponse, $format);
        return [
            'gpt_answer' => $gptAnswer,
            'return_answer' => '抱歉，我暂不支持回答此问题，请重新输入'
        ];
    }


}
