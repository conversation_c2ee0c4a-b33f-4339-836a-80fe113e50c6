<?php
/**
 * Created by PhpStorm.
 * User: czzhengkw
 * Date: 2017/4/20
 * Time: 下午3:28
 */

namespace common\library\custom_field;

use common\library\setting\library\fund\relation\CostItemInvoiceRelationApi;
use Constants;

class FieldExportService
{
    //订单
    const ORDER_GROUP_BASIC = 1;
    const ORDER_GROUP_CLIENT = 2;
    const ORDER_GROUP_COMPANY = 3;
    const ORDER_GROUP_CUSTOMER = 4;
    const ORDER_GROUP_PRODUCT = 5;
    const ORDER_GROUP_EXCHANGE_PRODUCT = 6;
    const ORDER_GROUP_PRODUCT_PAGE = 7;
    const ORDER_GROUP_ADDITION_FEE = 8;
    const ORDER_GROUP_EXTERNAL_INFO = 9;
    const ORDER_GROUP_ENTERPRISE_INFO = 10;
    const ORDER_GROUP_BANK_INFO = 11;

    //阿里订单产品
    const ALIBABA_ORDER_PRODUCT = 1;

    //报价单
    const QUOTATION_GROUP_BASIC = 1;
    const QUOTATION_GROUP_CLIENT = 2;
    const QUOTATION_GROUP_COMPANY = 3;
    const QUOTATION_GROUP_CUSTOMER = 4;
    const QUOTATION_GROUP_PRODUCT = 5;
    const QUOTATION_GROUP_EXCHANGE_PRODUCT = 6;
    const QUOTATION_GROUP_PRODUCT_PAGE = 7;
    const QUOTATION_GROUP_ADDITION_FEE = 8;
    const QUOTATION_GROUP_EXTERNAL_INFO = 9;
    const QUOTATION_GROUP_ENTERPRISE_INFO = 10;

    //采购单
    const PURCHASE_ORDER_GROUP_BASIC = 1;
    const PURCHASE_ORDER_GROUP_SUPPLIER = 2;
    const PURCHASE_ORDER_GROUP_SUPPLIER_INFO = 3;
    const PURCHASE_ORDER_GROUP_SUPPLIER_CONTACT = 4;
    const PURCHASE_ORDER_GROUP_PRODUCT = 6;
    const PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT = 5;
    const PURCHASE_ORDER_GROUP_PRODUCT_PAGE = 7;
    const PURCHASE_ORDER_GROUP_ADDITION_FEE = 8;
    const PURCHASE_ORDER_GROUP_EXTERNAL_INFO = 9;
    const PURCHASE_ORDER_GROUP_ENTERPRISE_INFO = 10;
    const PURCHASE_ORDER_GROUP_SUPPLIER_PAYMENT_INFO = 11;

    //采购入库单
    const PURCHASE_INBOUND_INVOICE_GROUP_BASIC = 1;
    const PURCHASE_INBOUND_INVOICE_GROUP_SUPPLIER = 2;
    const PURCHASE_INBOUND_INVOICE_GROUP_SUPPLIER_INFO = 3;
    const PURCHASE_INBOUND_INVOICE_GROUP_SUPPLIER_CONTACT = 4;
    const PURCHASE_INBOUND_INVOICE_GROUP_PRODUCT = 5;
    const PURCHASE_INBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT = 6;
    const PURCHASE_INBOUND_INVOICE_GROUP_PRODUCT_PAGE = 7;
    const PURCHASE_INBOUND_INVOICE_GROUP_EXTERNAL_INFO = 9;
    const PURCHASE_INBOUND_INVOICE_GROUP_ENTERPRISE_INFO = 10;

    //其他入库单
    const OTHER_INBOUND_INVOICE_GROUP_BASIC = 1;
    const OTHER_INBOUND_INVOICE_GROUP_PRODUCT = 5;
    const OTHER_INBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT = 6;
    const OTHER_INBOUND_INVOICE_GROUP_PRODUCT_PAGE = 7;
    const OTHER_INBOUND_INVOICE_GROUP_EXTERNAL_INFO = 9;
    const OTHER_INBOUND_INVOICE_GROUP_ENTERPRISE_INFO = 10;


    //销售出库单
    const SALE_OUTBOUND_INVOICE_GROUP_BASIC = 1;
    const SALE_OUTBOUND_INVOICE_GROUP_CLIENT = 2;
    const SALE_OUTBOUND_INVOICE_GROUP_COMPANY = 3;
    const SALE_OUTBOUND_INVOICE_GROUP_CUSTOMER = 4;
    const SALE_OUTBOUND_INVOICE_GROUP_PRODUCT = 5;
    const SALE_OUTBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT = 6;
    const SALE_OUTBOUND_INVOICE_GROUP_PRODUCT_PAGE = 7;
    const SALE_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO = 9;
    const SALE_OUTBOUND_INVOICE_GROUP_ENTERPRISE_INFO = 10;

    //其他出库单
    const OTHER_OUTBOUND_INVOICE_GROUP_BASIC = 1;
    const OTHER_OUTBOUND_INVOICE_GROUP_PRODUCT = 5;
    const OTHER_OUTBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT = 6;
    const OTHER_OUTBOUND_INVOICE_GROUP_PRODUCT_PAGE = 7;
    const OTHER_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO = 9;
    const OTHER_OUTBOUND_INVOICE_GROUP_ENTERPRISE_INFO = 10;

    //采购退货单
    const PURCHASE_RETURN_INVOICE_GROUP_BASIC = 1;
    const PURCHASE_RETURN_INVOICE_GROUP_SUPPLIER = 2;
    const PURCHASE_RETURN_INVOICE_GROUP_SUPPLIER_INFO = 3;
    const PURCHASE_RETURN_INVOICE_GROUP_SUPPLIER_CONTACT = 4;
    const PURCHASE_RETURN_INVOICE_GROUP_PRODUCT = 5;
    const PURCHASE_RETURN_INVOICE_GROUP_EXCHANGE_PRODUCT = 6;
    const PURCHASE_RETURN_INVOICE_GROUP_PRODUCT_PAGE = 7;
    const PURCHASE_RETURN_INVOICE_GROUP_EXTERNAL_INFO = 9;
    const PURCHASE_RETURN_INVOICE_GROUP_ENTERPRISE_INFO = 10;

    //订单毛利
    const ORDER_PROFIT_GROUP_BASIC = 1;

    //出运单
    const SHIPPING_INVOICE_GROUP_BASIC = 1; //基本信息
    const SHIPPING_INVOICE_GROUP_SHIPPING_TRANSPORT = 2; //运输信息
    const SHIPPING_INVOICE_GROUP_TIME_INFO = 3; //时间相关信息
    const SHIPPING_INVOICE_GROUP_PRODUCT_BASE = 4; //出运明细信息
    const SHIPPING_INVOICE_GROUP_PRODUCT_ADDITION_FEE = 5; //附加费用
    const SHIPPING_INVOICE_GROUP_EXCHANGE_PRODUCT = 6;
    const SHIPPING_INVOICE_GROUP_PRODUCT_PAGE = 7;
    const SHIPPING_INVOICE_GROUP_EXTERNAL_INFO = 9;
    const SHIPPING_INVOICE_GROUP_ENTERPRISE_INFO = 10;
    const SHIPPING_INVOICE_GROUP_CLIENT = 12;
    const SHIPPING_INVOICE_GROUP_COMPANY = 13;
    const SHIPPING_INVOICE_GROUP_CUSTOMER = 14;

    const SHIPPING_INVOICE_GROUP_PACKING_BASE = 15; //出运组装明细信息
    const SHIPPING_INVOICE_GROUP_CUSTOM_DECLARATION_BASE = 16; //出运报关明细信息


    //询价导出相关分组:
    const INQUIRY_COLLABORATION_GROUP_BASIC = 1;//询价基本信息
    const INQUIRY_COLLABORATION_GROUP_EXCHANGE_PRODUCT = 2;//询价产品-询价产品;
    const INQUIRY_COLLABORATION_GROUP_SYS = 3;//系统信息;
    const INQUIRY_COLLABORATION_GROUP_STATISTICS = 4;//统计信息;
    const INQUIRY_COLLABORATION_GROUP_CLIENT = 5;//客户信息;
    const INQUIRY_COLLABORATION_GROUP_COMPANY = 6;
    const INQUIRY_COLLABORATION_GROUP_CUSTOMER = 8;
    const INQUIRY_COLLABORATION_GROUP_PRODUCT_PAGE = 7;//询价产品-产品单页;
    const INQUIRY_COLLABORATION_GROUP_EXTERNAL_INFO = 9;//人员信息;
    const INQUIRY_COLLABORATION_GROUP_ENTERPRISE_INFO = 10;//企业信息;

        //付款单
    const PAYMENT_INVOICE_GROUP_BASIC = 1; //基本信息
    const PAYMENT_INVOICE_GROUP_SYSTEM = 2;//系统信息
    const PAYMENT_INVOICE_GROUP_RECORD = 3; //付款明细
    const PAYMENT_INVOICE_GROUP_PAYMENT = 5;//资金信息
    const PAYMENT_INVOICE_GROUP_CLIENT = 4;//客户信息;
    const PAYMENT_INVOICE_GROUP_COMPANY = 6; //客户
    const PAYMENT_INVOICE_GROUP_CUSTOMER = 8; //联系人
    const PAYMENT_INVOICE_GROUP_EXTERNAL_INFO = 9;//人员信息;
    const PAYMENT_INVOICE_GROUP_ENTERPRISE_INFO = 10;//企业信息;
    const PAYMENT_INVOICE_GROUP_SUPPLIER = 11;//供应商
    const PAYMENT_INVOICE_GROUP_SUPPLIER_INFO = 12;// 供应商
    const PAYMENT_INVOICE_GROUP_SUPPLIER_CONTACT = 13;// 供应商联系人


    //供应商分组 历史就是3、4
    const SUPPLIER_GROUP_BASIC = 3; //基础信息
    const SUPPLIER_GROUP_CONTACT = 4;//联系人信息

    const COMPANY_GROUP_BASIC= 3;
    const CUSTOMER_GROUP_BASIC= 4;

    private static $scenario_group_map = [
        Constants::TYPE_ORDER => [
            self::ORDER_GROUP_BASIC => ['name' => '订单基本信息', 'prefix' => '', 'is_list' => 0],
            self::ORDER_GROUP_CLIENT => [
                'name' => '客户信息',
                'prefix' => '',
                'is_list' => 0,
                'sub' => [self::ORDER_GROUP_COMPANY, self::ORDER_GROUP_CUSTOMER]
            ],
            self::ORDER_GROUP_COMPANY => [
                'name' => '公司信息',
                'prefix' => '公司_',
                'is_list' => 0,
                'parent' => self::ORDER_GROUP_CLIENT
            ],
            self::ORDER_GROUP_CUSTOMER => [
                'name' => '联系人信息',
                'prefix' => '联系人_',
                'is_list' => 0,
                'parent' => self::ORDER_GROUP_CLIENT
            ],
//            self::ORDER_GROUP_PRODUCT => [
//                'name' => '订单产品信息',
//                'prefix' => '',
//                'is_list' => 0,
//                'sub' => [self::ORDER_GROUP_EXCHANGE_PRODUCT, self::ORDER_GROUP_PRODUCT_PAGE]
//            ],
            self::ORDER_GROUP_EXCHANGE_PRODUCT => [
//                'name' => '交易产品字段',
                'name' => '订单产品-交易产品',
                'prefix' => '交易产品_',
                'is_list' => 1,
//                'parent' => self::ORDER_GROUP_PRODUCT
            ],
            self::ORDER_GROUP_PRODUCT_PAGE => [
//                'name' => '产品单页字段',
                'name' => '订单产品-产品单页',
                'prefix' => '产品_',
                'is_list' => 1,
//                'parent' => self::ORDER_GROUP_PRODUCT
            ],
            self::ORDER_GROUP_ADDITION_FEE => ['name' => '附加费用信息', 'prefix' => '费用_', 'is_list' => 1],
            self::ORDER_GROUP_EXTERNAL_INFO=> ['name' => '人员信息', 'prefix' => '导出人_', 'is_list' => 0],
            self::ORDER_GROUP_ENTERPRISE_INFO=> ['name' => '企业信息', 'prefix' => '企业_', 'is_list' => 0],
            self::ORDER_GROUP_BANK_INFO=> ['name' => '银行信息', 'prefix' => '银行信息_', 'is_list' => 0],
        ],
        Constants::TYPE_QUOTATION => [
            self::QUOTATION_GROUP_BASIC => ['name' => '报价单基本信息', 'prefix' => '', 'is_list' => 0],
            self::QUOTATION_GROUP_CLIENT => [
                'name' => '客户信息',
                'prefix' => '',
                'is_list' => 0,
                'sub' => [self::QUOTATION_GROUP_COMPANY, self::QUOTATION_GROUP_CUSTOMER]
            ],
            self::QUOTATION_GROUP_COMPANY => [
                'name' => '公司信息',
                'prefix' => '公司_',
                'is_list' => 0,
                'parent' => self::QUOTATION_GROUP_CLIENT
            ],
            self::QUOTATION_GROUP_CUSTOMER => [
                'name' => '联系人信息',
                'prefix' => '联系人_',
                'is_list' => 0,
                'parent' => self::QUOTATION_GROUP_CLIENT
            ],
//            self::QUOTATION_GROUP_PRODUCT => [
//                'name' => '报价单产品信息',
//                'prefix' => '',
//                'is_list' => 0,
//                'sub' => [self::QUOTATION_GROUP_EXCHANGE_PRODUCT, self::QUOTATION_GROUP_PRODUCT_PAGE]
//            ],
            self::QUOTATION_GROUP_EXCHANGE_PRODUCT => [
//                'name' => '报价产品字段',
                'name' => '报价产品-报价产品',
                'prefix' => '报价产品_',
                'is_list' => 1,
//                'parent' => self::QUOTATION_GROUP_PRODUCT
            ],
            self::QUOTATION_GROUP_PRODUCT_PAGE => [
//                'name' => '产品单页字段',
                'name' => '报价产品-产品单页',
                'prefix' => '产品_',
                'is_list' => 1,
//                'parent' => self::QUOTATION_GROUP_PRODUCT
            ],
            self::QUOTATION_GROUP_ADDITION_FEE => ['name' => '附加费用信息', 'prefix' => '费用_', 'is_list' => 1],
            self::QUOTATION_GROUP_EXTERNAL_INFO=> ['name' => '人员信息', 'prefix' => '导出人_', 'is_list' => 0],
            self::QUOTATION_GROUP_ENTERPRISE_INFO=> ['name' => '企业信息', 'prefix' => '企业_', 'is_list' => 0],
        ],
        //采购订单map
        Constants::TYPE_PURCHASE_ORDER => [
            self::PURCHASE_ORDER_GROUP_BASIC => [
                'name' => '采购订单基本信息',
                'prefix' => '',
                'is_list' => 0
            ],
            self::PURCHASE_ORDER_GROUP_SUPPLIER => [
                'name' => '供应商信息',
                'prefix' => '',
                'is_list' => 0,
                'sub' => [self::PURCHASE_ORDER_GROUP_SUPPLIER_INFO, self::PURCHASE_ORDER_GROUP_SUPPLIER_CONTACT, self::PURCHASE_ORDER_GROUP_SUPPLIER_PAYMENT_INFO]
            ],
            self::PURCHASE_ORDER_GROUP_SUPPLIER_INFO => [
                'name' => '基本信息',
                'prefix' => '供应商_',
                'is_list' => 0,
                'parent' => self::PURCHASE_ORDER_GROUP_SUPPLIER
            ],
            self::PURCHASE_ORDER_GROUP_SUPPLIER_CONTACT => [
                'name' => '联系人信息',
                'prefix' => '供应商联系人_',
                'is_list' => 0,
                'parent' => self::PURCHASE_ORDER_GROUP_SUPPLIER
            ],
            self::PURCHASE_ORDER_GROUP_SUPPLIER_PAYMENT_INFO => [
                'name' => '资金信息',
                'prefix' => '供应商_',
                'is_list' => 0,
                'parent' => self::PURCHASE_ORDER_GROUP_SUPPLIER
            ],
//            self::PURCHASE_ORDER_GROUP_PRODUCT => [
//                'name' => '采购订单产品信息',
//                'prefix' => '采购产品_',
//                'is_list' => 1,
//                'sub' => [self::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT, self::PURCHASE_ORDER_GROUP_PRODUCT_PAGE]
//            ],
            self::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT => [
//                'name' => '采购产品字段',
                'name' => '采购产品-采购产品',
                'prefix' => '采购产品_',
                'is_list' => 1,
//                'parent' => self::PURCHASE_ORDER_GROUP_PRODUCT
            ],
            self::PURCHASE_ORDER_GROUP_PRODUCT_PAGE => [
//                'name' => '产品单页字段',
                'name' => '采购产品-产品单页',
                'prefix' => '产品_',
                'is_list' => 1,
//                'parent' => self::PURCHASE_ORDER_GROUP_PRODUCT
            ],
            self::PURCHASE_ORDER_GROUP_ADDITION_FEE => ['name' => '附加费用信息', 'prefix' => '费用_', 'is_list' => 1],
            self::PURCHASE_ORDER_GROUP_EXTERNAL_INFO=> ['name' => '人员信息', 'prefix' => '导出人_', 'is_list' => 0],
            self::PURCHASE_ORDER_GROUP_ENTERPRISE_INFO=> ['name' => '企业信息', 'prefix' => '企业_', 'is_list' => 0],
        ],
        //采购入库单
        Constants::TYPE_PURCHASE_INBOUND_INVOICE => [
            self::PURCHASE_INBOUND_INVOICE_GROUP_BASIC => ['name' => '采购入库基本信息', 'prefix' => '', 'is_list' => 0],
            self::PURCHASE_INBOUND_INVOICE_GROUP_SUPPLIER => [
                'name' => '供应商信息',
                'prefix' => '',
                'is_list' => 0,
                'sub' => [self::PURCHASE_INBOUND_INVOICE_GROUP_SUPPLIER_INFO, self::PURCHASE_INBOUND_INVOICE_GROUP_SUPPLIER_CONTACT]
            ],
            self::PURCHASE_INBOUND_INVOICE_GROUP_SUPPLIER_INFO => [
                'name' => '基本信息',
                'prefix' => '供应商_',
                'is_list' => 0,
                'parent' => self::PURCHASE_INBOUND_INVOICE_GROUP_SUPPLIER
            ],
            self::PURCHASE_INBOUND_INVOICE_GROUP_SUPPLIER_CONTACT => [
                'name' => '联系人信息',
                'prefix' => '供应商联系人_',
                'is_list' => 0,
                'parent' => self::PURCHASE_INBOUND_INVOICE_GROUP_SUPPLIER
            ],
//            self::PURCHASE_INBOUND_INVOICE_GROUP_PRODUCT => [
//                'name' => '采购入库产品信息',
//                'prefix' => '',
//                'is_list' => 0,
//                'sub' => [self::PURCHASE_INBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT, self::PURCHASE_INBOUND_INVOICE_GROUP_PRODUCT_PAGE]
//            ],
            self::PURCHASE_INBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT => [
                'name' => '入库产品-入库明细',
                'prefix' => '入库明细_',
                'is_list' => 1,
            ],
            self::PURCHASE_INBOUND_INVOICE_GROUP_PRODUCT_PAGE => [
                'name' => '入库产品-产品单页',
                'prefix' => '产品_',
                'is_list' => 1,
            ],
            self::PURCHASE_INBOUND_INVOICE_GROUP_EXTERNAL_INFO=> ['name' => '人员信息', 'prefix' => '导出人_', 'is_list' => 0],
            self::PURCHASE_INBOUND_INVOICE_GROUP_ENTERPRISE_INFO=> ['name' => '企业信息', 'prefix' => '企业_', 'is_list' => 0],
        ],
        //其他入库单
        Constants::TYPE_OTHER_INBOUND_INVOICE => [
            self::OTHER_INBOUND_INVOICE_GROUP_BASIC => ['name' => '其他入库基本信息', 'prefix' => '', 'is_list' => 0],
//            self::OTHER_INBOUND_INVOICE_GROUP_PRODUCT => [
//                'name' => '产品信息',
//                'prefix' => '',
//                'is_list' => 0,
//                'sub' => [self::OTHER_INBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT, self::OTHER_INBOUND_INVOICE_GROUP_PRODUCT_PAGE]
//            ],
            self::OTHER_INBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT => [
                'name' => '入库产品-入库明细',
                'prefix' => '入库明细_',
                'is_list' => 1,
            ],
            self::OTHER_INBOUND_INVOICE_GROUP_PRODUCT_PAGE => [
                'name' => '入库产品-产品单页',
                'prefix' => '产品_',
                'is_list' => 1,
            ],
            self::OTHER_INBOUND_INVOICE_GROUP_EXTERNAL_INFO=> ['name' => '人员信息', 'prefix' => '导出人_', 'is_list' => 0],
            self::OTHER_INBOUND_INVOICE_GROUP_ENTERPRISE_INFO=> ['name' => '企业信息', 'prefix' => '企业_', 'is_list' => 0],
        ],
        //销售出库单
        Constants::TYPE_SALE_OUTBOUND_INVOICE => [
            self::SALE_OUTBOUND_INVOICE_GROUP_BASIC => ['name' => '销售出库基本信息', 'prefix' => '', 'is_list' => 0],
            self::SALE_OUTBOUND_INVOICE_GROUP_CLIENT => [
                'name' => '客户信息',
                'prefix' => '',
                'is_list' => 0,
                'sub' => [self::SALE_OUTBOUND_INVOICE_GROUP_COMPANY, self::SALE_OUTBOUND_INVOICE_GROUP_CUSTOMER]
            ],
            self::SALE_OUTBOUND_INVOICE_GROUP_COMPANY => [
                'name' => '公司信息',
                'prefix' => '公司_',
                'is_list' => 0,
                'parent' => self::SALE_OUTBOUND_INVOICE_GROUP_CLIENT
            ],
            self::SALE_OUTBOUND_INVOICE_GROUP_CUSTOMER => [
                'name' => '联系人信息',
                'prefix' => '联系人_',
                'is_list' => 0,
                'parent' => self::SALE_OUTBOUND_INVOICE_GROUP_CLIENT
            ],
//            self::SALE_OUTBOUND_INVOICE_GROUP_PRODUCT => [
//                'name' => '产品信息',
//                'prefix' => '',
//                'is_list' => 0,
//                'sub' => [self::SALE_OUTBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT, self::SALE_OUTBOUND_INVOICE_GROUP_PRODUCT_PAGE]
//            ],
            self::SALE_OUTBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT => [
                'name' => '出库产品-出库明细',
                'prefix' => '出库明细_',
                'is_list' => 1,
            ],
            self::SALE_OUTBOUND_INVOICE_GROUP_PRODUCT_PAGE => [
                'name' => '出库产品-产品单页',
                'prefix' => '产品_',
                'is_list' => 1,
            ],
            self::SALE_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO=> ['name' => '人员信息', 'prefix' => '导出人_', 'is_list' => 0],
            self::SALE_OUTBOUND_INVOICE_GROUP_ENTERPRISE_INFO=> ['name' => '企业信息', 'prefix' => '企业_', 'is_list' => 0],
        ],
        //其他出库单
        Constants::TYPE_OTHER_OUTBOUND_INVOICE => [
            self::OTHER_OUTBOUND_INVOICE_GROUP_BASIC => ['name' => '其他出库基本信息', 'prefix' => '', 'is_list' => 0],
//            self::OTHER_OUTBOUND_INVOICE_GROUP_PRODUCT => [
//                'name' => '产品信息',
//                'prefix' => '',
//                'is_list' => 0,
//                'sub' => [self::OTHER_OUTBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT, self::OTHER_OUTBOUND_INVOICE_GROUP_PRODUCT_PAGE]
//            ],
            self::OTHER_OUTBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT => [
                'name' => '出库产品-出库明细',
                'prefix' => '出库明细_',
                'is_list' => 1,
            ],
            self::OTHER_OUTBOUND_INVOICE_GROUP_PRODUCT_PAGE => [
                'name' => '出库产品-产品单页',
                'prefix' => '产品_',
                'is_list' => 1,
            ],
            self::OTHER_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO=> ['name' => '人员信息', 'prefix' => '导出人_', 'is_list' => 0],
            self::OTHER_OUTBOUND_INVOICE_GROUP_ENTERPRISE_INFO=> ['name' => '企业信息', 'prefix' => '企业_', 'is_list' => 0],
        ],
        //采购退货单
        Constants::TYPE_PURCHASE_RETURN_INVOICE => [
            self::PURCHASE_RETURN_INVOICE_GROUP_BASIC => ['name' => '采购退货基本信息', 'prefix' => '', 'is_list' => 0],
            self::PURCHASE_RETURN_INVOICE_GROUP_SUPPLIER => [
                'name' => '供应商信息',
                'prefix' => '',
                'is_list' => 0,
                'sub' => [self::PURCHASE_RETURN_INVOICE_GROUP_SUPPLIER_INFO, self::PURCHASE_RETURN_INVOICE_GROUP_SUPPLIER_CONTACT]
            ],
            self::PURCHASE_RETURN_INVOICE_GROUP_SUPPLIER_INFO => [
                'name' => '基本信息',
                'prefix' => '供应商_',
                'is_list' => 0,
                'parent' => self::PURCHASE_RETURN_INVOICE_GROUP_SUPPLIER
            ],
            self::PURCHASE_RETURN_INVOICE_GROUP_SUPPLIER_CONTACT => [
                'name' => '联系人信息',
                'prefix' => '供应商联系人_',
                'is_list' => 0,
                'parent' => self::PURCHASE_RETURN_INVOICE_GROUP_SUPPLIER
            ],
//            self::PURCHASE_RETURN_INVOICE_GROUP_PRODUCT => [
//                'name' => '产品信息',
//                'prefix' => '',
//                'is_list' => 0,
//                'sub' => [self::PURCHASE_RETURN_INVOICE_GROUP_EXCHANGE_PRODUCT, self::PURCHASE_RETURN_INVOICE_GROUP_PRODUCT_PAGE]
//            ],
            self::PURCHASE_RETURN_INVOICE_GROUP_EXCHANGE_PRODUCT => [
                'name' => '退货产品-退货明细',
                'prefix' => '退货明细_',
                'is_list' => 1,
            ],
            self::PURCHASE_RETURN_INVOICE_GROUP_PRODUCT_PAGE => [
                'name' => '退货产品-产品单页',
                'prefix' => '产品_',
                'is_list' => 1,
            ],
            self::PURCHASE_RETURN_INVOICE_GROUP_EXTERNAL_INFO=> ['name' => '人员信息', 'prefix' => '导出人_', 'is_list' => 0],
            self::PURCHASE_RETURN_INVOICE_GROUP_ENTERPRISE_INFO=> ['name' => '企业信息', 'prefix' => '企业_', 'is_list' => 0],
        ],
        //出运单
        \Constants::TYPE_SHIPPING_INVOICE => [
            self::SHIPPING_INVOICE_GROUP_BASIC => ['name' => '出运基本信息', 'prefix' => '', 'is_list' => 0],
            self::SHIPPING_INVOICE_GROUP_SHIPPING_TRANSPORT=> ['name' => '运输信息', 'prefix' => '', 'is_list' => 0],
            self::SHIPPING_INVOICE_GROUP_TIME_INFO=> ['name' => '日期信息', 'prefix' => '', 'is_list' => 0],
            self::SHIPPING_INVOICE_GROUP_PRODUCT_BASE => [
                'name' => '出运产品-出运明细',
                'prefix' => '出运明细_',
                'is_list' => 1,
            ],
            self::SHIPPING_INVOICE_GROUP_PACKING_BASE => [
                'name' => '出运产品-装箱明细',
                'prefix' => '装箱明细_',
                'is_list' => 1,
            ],
            self::SHIPPING_INVOICE_GROUP_CUSTOM_DECLARATION_BASE => [
                'name' => '出运产品-报关明细',
                'prefix' => '报关明细_',
                'is_list' => 1,
            ],
            self::SHIPPING_INVOICE_GROUP_PRODUCT_ADDITION_FEE => ['name' => '附加费用信息', 'prefix' => '费用_', 'is_list' => 1],
            self::SHIPPING_INVOICE_GROUP_EXTERNAL_INFO=> ['name' => '人员信息', 'prefix' => '导出人_', 'is_list' => 0],
            self::SHIPPING_INVOICE_GROUP_ENTERPRISE_INFO=> ['name' => '企业信息', 'prefix' => '企业_', 'is_list' => 0],
            self::SHIPPING_INVOICE_GROUP_CLIENT => [
                'name' => '客户信息',
                'prefix' => '',
                'is_list' => 0,
                'sub' => [self::SHIPPING_INVOICE_GROUP_COMPANY, self::SHIPPING_INVOICE_GROUP_CUSTOMER]
            ],
            self::SHIPPING_INVOICE_GROUP_COMPANY => [
                'name' => '公司信息',
                'prefix' => '公司_',
                'is_list' => 0,
                'parent' => self::SHIPPING_INVOICE_GROUP_CLIENT
            ],
            self::SHIPPING_INVOICE_GROUP_CUSTOMER => [
                'name' => '联系人信息',
                'prefix' => '联系人_',
                'is_list' => 0,
                'parent' => self::SHIPPING_INVOICE_GROUP_CLIENT
            ],
        ],
        //询价协同
        \Constants::TYPE_INQUIRY_COLLABORATION => [
            self::INQUIRY_COLLABORATION_GROUP_BASIC => ['name' => '询价基本信息', 'prefix' => '', 'is_list' => 0],
            self::INQUIRY_COLLABORATION_GROUP_STATISTICS => ['name' => '合计信息', 'prefix' => '', 'is_list' => 0],
            self::INQUIRY_COLLABORATION_GROUP_SYS => ['name' => '系统信息', 'prefix' => '', 'is_list' => 0],
            self::INQUIRY_COLLABORATION_GROUP_CLIENT=> ['name' => '客户信息',
                'prefix' => '',
                'is_list' => 0,
                'sub' => [self::INQUIRY_COLLABORATION_GROUP_COMPANY, self::INQUIRY_COLLABORATION_GROUP_CUSTOMER]],
            self::INQUIRY_COLLABORATION_GROUP_COMPANY => [
                'name' => '公司信息',
                'prefix' => '公司_',
                'is_list' => 0,
                'parent' => self::INQUIRY_COLLABORATION_GROUP_CLIENT
            ],
            self::INQUIRY_COLLABORATION_GROUP_CUSTOMER => [
                'name' => '联系人信息',
                'prefix' => '联系人_',
                'is_list' => 0,
                'parent' => self::INQUIRY_COLLABORATION_GROUP_CLIENT
            ],
            self::INQUIRY_COLLABORATION_GROUP_EXCHANGE_PRODUCT=> ['name' => '询价产品-询价产品', 'prefix' => '询价产品_', 'is_list' => 1],
            self::INQUIRY_COLLABORATION_GROUP_PRODUCT_PAGE => ['name' => '询价产品-产品单页', 'prefix' => '产品_', 'is_list' => 1],
            self::INQUIRY_COLLABORATION_GROUP_EXTERNAL_INFO => ['name' => '人员信息', 'prefix' => '导出人_', 'is_list' => 0],
            self::INQUIRY_COLLABORATION_GROUP_ENTERPRISE_INFO => ['name' => '企业信息', 'prefix' => '企业_', 'is_list' => 0]
        ],
        //付款单
        Constants::TYPE_PAYMENT_INVOICE => [
            self::PAYMENT_INVOICE_GROUP_BASIC => ['name' => '付款单-基本信息', 'prefix' => '', 'is_list' => 0],
            self::PAYMENT_INVOICE_GROUP_PAYMENT => [
                'name' => '付款单-收款信息',
                'prefix' => '收款信息_',
                'is_list' => 0,
            ],
            self::PAYMENT_INVOICE_GROUP_RECORD => [
                'name' => '付款单-付款明细',
                'prefix' => '付款明细_',
                'is_list' => 1,
            ],
            self::PAYMENT_INVOICE_GROUP_SYSTEM => [
                'name' => '付款单-系统信息',
                'prefix' => '',
                'is_list' => 0,
            ],
            self::PAYMENT_INVOICE_GROUP_CLIENT => ['name' => '客户信息',
                'prefix' => '',
                'is_list' => 0,
                'sub' => [self::INQUIRY_COLLABORATION_GROUP_COMPANY, self::INQUIRY_COLLABORATION_GROUP_CUSTOMER]],
            self::PAYMENT_INVOICE_GROUP_COMPANY => [
                'name' => '公司信息',
                'prefix' => '公司_',
                'is_list' => 0,
                'parent' => self::PAYMENT_INVOICE_GROUP_CLIENT
            ],
            self::PAYMENT_INVOICE_GROUP_CUSTOMER => [
                'name' => '联系人信息',
                'prefix' => '联系人_',
                'is_list' => 0,
                'parent' => self::PAYMENT_INVOICE_GROUP_CLIENT
            ],
            self::PAYMENT_INVOICE_GROUP_SUPPLIER => [
                'name' => '供应商信息',
                'prefix' => '',
                'is_list' => 0,
                'sub' => [self::PAYMENT_INVOICE_GROUP_SUPPLIER_INFO, self::PAYMENT_INVOICE_GROUP_SUPPLIER_CONTACT]
            ],
            self::PAYMENT_INVOICE_GROUP_SUPPLIER_INFO => [
                'name' => '基本信息',
                'prefix' => '供应商_',
                'is_list' => 0,
                'parent' => self::PAYMENT_INVOICE_GROUP_SUPPLIER
            ],
            self::PAYMENT_INVOICE_GROUP_SUPPLIER_CONTACT => [
                'name' => '联系人信息',
                'prefix' => '供应商联系人_',
                'is_list' => 0,
                'parent' => self::PAYMENT_INVOICE_GROUP_SUPPLIER
            ],
            self::PAYMENT_INVOICE_GROUP_EXTERNAL_INFO => ['name' => '人员信息', 'prefix' => '导出人_', 'is_list' => 0],
            self::PAYMENT_INVOICE_GROUP_ENTERPRISE_INFO => ['name' => '企业信息', 'prefix' => '企业_', 'is_list' => 0],
        ],
    ];

    private static $special_rule = [
        'cost_with_tax' => [['name' => '含税成本价', 'code' => '含税成本价']],
        'other_cost' => [['name' => '其他费用', 'code' => '其他费用']],
        'cost_amount' => [['name' => '金额小计', 'code' => '金额小计']],
        'count_per_package' => [['name' => '每包装产品数量', 'code' => '每包装产品数量']],
        'package_volume' => [['name' => '包装体积', 'code' => '包装体积']],
        'package_volume_amount' => [['name' => '包装体积总计', 'code' => '包装体积总计']],
        'package_gross_weight' => [['name' => '包装毛重', 'code' => '包装毛重']],
        'package_gross_weight_amount' => [['name' => '包装毛重总计', 'code' => '包装毛重总计']],
        'product_total_count' => [['name' => '产品总数量', 'code' => '产品总数量']],
        'product_total_amount' => [['name' => '产品总金额', 'code' => '产品总金额']],
        'addition_cost_amount' => [['name' => '附加费用总金额', 'code' => '附加费用总金额']],
        'info_json' => [['name' => '产品属性', 'code' => '产品属性']],
        'gross_profit_margin' => [
            ['index' => 1, 'name' => '毛利润率（带%，格式示例：10%）', 'code' => '毛利润率（带%）'],
            ['index' => 2, 'name' => '毛利润率（不带%，格式示例：10）', 'code' => '毛利润率（不带%）']
        ],
        'images' => [
            ['index' => 1, 'name' => '产品图片（1）', 'code' => '产品图片（1）'],
            ['index' => 2, 'name' => '产品图片（2）', 'code' => '产品图片（2）'],
            ['index' => 3, 'name' => '产品图片（3）', 'code' => '产品图片（3）'],
            ['index' => 4, 'name' => '产品图片（4）', 'code' => '产品图片（4）'],
            ['index' => 5, 'name' => '产品图片（5）', 'code' => '产品图片（5）'],
            ['index' => 6, 'name' => '产品图片（6）', 'code' => '产品图片（6）'],
        ],
        'percent_of_total_amount' => [
            ['index' => 1, 'name' => '占产品金额百分比（带%，格式示例：10%）', 'code' => '占产品金额百分比（带%）'],
            ['index' => 2, 'name' => '占产品金额百分比（不带%，格式示例：10）', 'code' => '占产品金额百分比（不带%）'],
        ],
        'users' => [
            ['name' => '业绩归属人及业绩占比', 'code' => '业绩归属人及业绩占比']
        ],
        'shipping_mode' => [
            ['index' => 0, 'name' => '运输方式', 'code' => '运输方式'],
            ['index' => 1, 'name' => '运输方式(英文)', 'code' => '运输方式(英文)']
        ]
    ];

    //主配产品功能，用array_merge ,合并$special_rule 中的数据；展示层看是否有权限进行合并覆盖
    private static $parst_product_special_rule = [
        'product_total_count' => [['name' => '产品总数量（含配件）', 'code' => '产品总数量（含配件）']],
        'product_total_count_no_parts' => [['name' => '产品总数量', 'code' => '产品总数量']],//导出时 默认理解产品总数量是不含配件
        'parts_total_count' => [['name' => '配件总数量', 'code' => '配件总数量']],
    ];

    private static $special_rule_price_count = [
        'unit_price' => [['name' => '单价', 'code' => '单价']],
        'count' => [['name' => '数量', 'code' => '数量']],
    ];
    /**
     * 产品要求加这个字段，放在订单
     */
    private $common_static_rule = [
        \Constants::TYPE_ORDER => [
            [
                'groups' => [0, self::ORDER_GROUP_BASIC],
                'group' => self::ORDER_GROUP_BASIC,
                'fields' => [
                    ['base' => 1, 'columns' => [], 'show_name' => '导出日期', 'code' => '#导出日期#', 'id' => 'export_date', 'group_id' => self::ORDER_GROUP_BASIC, 'type' => Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['company_id'], 'show_name' => '客户', 'code' => '#客户#', 'id' => 'company_id', 'group_id' => self::ORDER_GROUP_BASIC, 'type' => Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['customer_id'], 'show_name' => '联系人', 'code' => '#联系人#', 'id' => 'customer_id', 'group_id' => self::ORDER_GROUP_BASIC, 'type' => Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['seller_account_id'], 'show_name' => '阿里业务员', 'code' => '#阿里业务员#', 'id' => 'seller_account_id','group_id' => self::ORDER_GROUP_BASIC, 'type' => Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['fulfillment_channel'], 'show_name' => '出口方式', 'code' => '#出口方式#', 'id' => 'fulfillment_channel','group_id' => self::ORDER_GROUP_BASIC, 'type' => Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['source_type'], 'show_name' => '订单类型', 'code' => '#订单类型#', 'id' => 'source_type','group_id' => self::ORDER_GROUP_BASIC, 'type' => Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['ali_order_id'], 'show_name' => '阿里订单ID', 'code' => '#阿里订单ID#', 'id' => 'ali_order_id','group_id' => self::ORDER_GROUP_BASIC, 'type' => Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['ali_status_id'], 'show_name' => '阿里订单状态', 'code' => '#阿里订单状态#', 'id' => 'ali_status_id','group_id' => self::ORDER_GROUP_BASIC, 'type' => Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['ali_store_id'], 'show_name' => '来源店铺', 'code' => '#来源店铺#', 'id' => 'ali_store_id','group_id' => self::ORDER_GROUP_BASIC, 'type' => Constants::TYPE_ORDER],

                    //20230412 下述字段不在列表展示，只在导出时可选 https://www.tapd.cn/21404721/prong/stories/view/1121404721001044504
                    ['base' => 1, 'columns' => ['cost_with_tax_total_cny'], 'show_name' => '含税成本价总金额（CNY）', 'code' => '#含税成本价总金额（CNY）#', 'id' => 'cost_with_tax_total_cny','group_id' => self::ORDER_GROUP_BASIC, 'type' => Constants::TYPE_ORDER, 'field_type' => CustomFieldService::FIELD_TYPE_NUMBER],
                    ['base' => 1, 'columns' => ['cost_with_tax_total_usd'], 'show_name' => '含税成本价总金额（USD）', 'code' => '#含税成本价总金额（USD）#', 'id' => 'cost_with_tax_total_usd','group_id' => self::ORDER_GROUP_BASIC, 'type' => Constants::TYPE_ORDER, 'field_type' => CustomFieldService::FIELD_TYPE_NUMBER],
                    ['base' => 1, 'columns' => ['order_gross_margin_cny'], 'show_name' => '产品毛利总计（CNY）', 'code' => '#产品毛利总计（CNY）#', 'id' => 'order_gross_margin_cny','group_id' => self::ORDER_GROUP_BASIC, 'type' => Constants::TYPE_ORDER, 'field_type' => CustomFieldService::FIELD_TYPE_NUMBER],
                    ['base' => 1, 'columns' => ['order_gross_margin_usd'], 'show_name' => '产品毛利总计（USD）', 'code' => '#产品毛利总计（USD）#', 'id' => 'order_gross_margin_usd','group_id' => self::ORDER_GROUP_BASIC, 'type' => Constants::TYPE_ORDER, 'field_type' => CustomFieldService::FIELD_TYPE_NUMBER],
                ]
            ]
        ],
        \Constants::TYPE_QUOTATION => [
            [
                'groups' => [0, self::QUOTATION_GROUP_BASIC],
                'group' => self::QUOTATION_GROUP_BASIC,
                'fields' => [
                    ['base' => 1, 'columns' => [], 'show_name' => '导出日期', 'code' => '#导出日期#', 'id' => 'export_date', 'group_id' => self::QUOTATION_GROUP_BASIC, 'type' => Constants::TYPE_QUOTATION],
                    ['base' => 1, 'columns' => ['company_id'], 'show_name' => '客户', 'code' => '#客户#', 'id' => 'company_id', 'group_id' => self::QUOTATION_GROUP_BASIC, 'type' => Constants::TYPE_QUOTATION],
                    ['base' => 1, 'columns' => ['customer_id'], 'show_name' => '联系人', 'code' => '#联系人#', 'id' => 'customer_id', 'group_id' => self::QUOTATION_GROUP_BASIC, 'type' => Constants::TYPE_QUOTATION],
                    ['base' => 1, 'columns' => ['exchange_rate'], 'show_name' => '汇率', 'code' => '#汇率#', 'id' => 'exchange_rate', 'group_id' => self::QUOTATION_GROUP_BASIC, 'type' => Constants::TYPE_QUOTATION],
                    ['base' => 1, 'columns' => ['quotation_no'], 'show_name' => '报价单号', 'code' => '#报价单号#', 'id' => 'quotation_no', 'group_id' => self::QUOTATION_GROUP_BASIC, 'type' => Constants::TYPE_QUOTATION],
                    ['base' => 1, 'columns' => ['create_time'], 'show_name' => '创建日期', 'code' => '#创建日期#', 'id' => 'create_time', 'group_id' => self::QUOTATION_GROUP_BASIC, 'type' => Constants::TYPE_QUOTATION],
                    ['base' => 1, 'columns' => ['update_time'], 'show_name' => '更新日期', 'code' => '#更新日期#', 'id' => 'update_time', 'group_id' => self::QUOTATION_GROUP_BASIC, 'type' => Constants::TYPE_QUOTATION],
                    ['base' => 1, 'columns' => ['shipment_deadline_remark'], 'show_name' => '装运期限说明', 'code' => '#装运期限说明#', 'id' => 'shipment_deadline_remark', 'group_id' => self::QUOTATION_GROUP_BASIC, 'type' => Constants::TYPE_QUOTATION],
                    ['base' => 1, 'columns' => ['company_name'], 'show_name' => '客户名称', 'code' => '#客户名称#', 'id' => 'company_name', 'group_id' => self::QUOTATION_GROUP_BASIC, 'type' => Constants::TYPE_QUOTATION],
                ]
            ]
        ],
        \Constants::TYPE_PURCHASE_ORDER => [
            [
                'groups' => [0, self::PURCHASE_ORDER_GROUP_BASIC],
                'group' => self::PURCHASE_ORDER_GROUP_BASIC,
                'fields' => [
                    ['base' => 1, 'columns' => [], 'show_name' => '导出日期', 'code' => '#导出日期#', 'id' => 'export_date', 'group_id' => self::PURCHASE_ORDER_GROUP_BASIC, 'type' => Constants::TYPE_PURCHASE_ORDER],
                    ['base' => 1, 'columns' => ['amount_traditional'], 'show_name' => '订单金额（繁体）', 'code' => '#订单金额_繁体#', 'id' => 'amount_traditional', 'group_id' => self::PURCHASE_ORDER_GROUP_BASIC, 'type' => Constants::TYPE_PURCHASE_ORDER],
                    ['base' => 1, 'columns' => ['product_total_amount_traditional'], 'show_name' => '产品总金额（繁体）', 'code' => '#产品总金额_繁体#', 'id' => 'product_total_amount_traditional', 'group_id' => self::PURCHASE_ORDER_GROUP_BASIC, 'type' => Constants::TYPE_PURCHASE_ORDER],
                    ['base' => 1, 'columns' => ['phone'], 'show_name' => '联系人手机号', 'code' => '#供应商联系人手机号#', 'id' => 'phone', 'group_id' => self::PURCHASE_ORDER_GROUP_BASIC, 'type' => Constants::TYPE_PURCHASE_ORDER],
                ]
            ]
        ],
        \Constants::TYPE_PURCHASE_INBOUND_INVOICE => [],
        \Constants::TYPE_OTHER_INBOUND_INVOICE => [],
        \Constants::TYPE_SALE_OUTBOUND_INVOICE => [],
        \Constants::TYPE_OTHER_OUTBOUND_INVOICE => [],
        \Constants::TYPE_PURCHASE_RETURN_INVOICE => [],
        \Constants::TYPE_SHIPPING_INVOICE => [],
        \Constants::TYPE_INQUIRY_COLLABORATION => [],
        \Constants::TYPE_PAYMENT_INVOICE => [],

    ];

    private $static_rule = [
        Constants::TYPE_ORDER => [
            self::ORDER_GROUP_EXCHANGE_PRODUCT => [
                'groups' => [0, self::ORDER_GROUP_PRODUCT, self::ORDER_GROUP_EXCHANGE_PRODUCT],
                'group' => self::ORDER_GROUP_EXCHANGE_PRODUCT,
                'fields' => [['base' => 1, 'columns' => [], 'show_name' => '序号', 'code' => '#交易产品_序号#','id'=>'invoice_product_no','group_id'=>self::ORDER_GROUP_EXCHANGE_PRODUCT,'type'=> Constants::TYPE_ORDER]]
            ],
            self::ORDER_GROUP_EXTERNAL_INFO =>[
                'groups' => [0, self::ORDER_GROUP_EXTERNAL_INFO],
                'group' => self::ORDER_GROUP_EXTERNAL_INFO,
                'fields' => [
                    ['base' => 1, 'columns' => ['nickname'], 'show_name' => '导出人昵称', 'code' => '#导出人_昵称#','id'=>'nickname','group_id' => self::ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['external_email'], 'show_name' => '导出人邮箱', 'code' => '#导出人_邮箱#','id'=>'external_email','group_id' => self::ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['external_mobile'], 'show_name' => '导出人电话', 'code' => '#导出人_电话#','id'=>'external_mobile','group_id' => self::ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['external_fax'], 'show_name' => '导出人传真', 'code' => '#导出人_传真#','id'=>'external_fax','group_id' => self::ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['external_address'], 'show_name' => '导出人联系地址', 'code' => '#导出人_联系地址#','id'=>'external_address','group_id' => self::ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['external_other'], 'show_name' => '导出人其他信息', 'code' => '#导出人_其他信息#','id'=>'external_other','group_id' => self::ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['create_user_nickname'], 'show_name' => '创建人昵称', 'code' => '#创建人_昵称#','id'=>'create_user_nickname','group_id' => self::ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['create_user_email'], 'show_name' => '创建人邮箱', 'code' => '#创建人_邮箱#','id'=>'create_user_email','group_id' => self::ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['create_user_mobile'], 'show_name' => '创建人电话', 'code' => '#创建人_电话#','id'=>'create_user_mobile','group_id' => self::ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['create_user_fax'], 'show_name' => '创建人传真', 'code' => '#创建人_传真#','id'=>'create_user_fax','group_id' => self::ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['create_user_address'], 'show_name' => '创建人联系地址', 'code' => '#创建人_联系地址#','id'=>'create_user_address','group_id' => self::ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['create_user_other'], 'show_name' => '创建人其他信息', 'code' => '#创建人_其他信息#','id'=>'create_user_other','group_id' => self::ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_ORDER],
                ]
            ],
            self::ORDER_GROUP_ENTERPRISE_INFO =>[
                'groups' => [0, self::ORDER_GROUP_ENTERPRISE_INFO],
                'group' => self::ORDER_GROUP_ENTERPRISE_INFO,
                'fields' => [
                    ['base' => 1, 'columns' => ['short_name'], 'show_name' => '简称', 'code' => '#企业_简称#','id'=>'short_name','group_id' => self::ORDER_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['tel'], 'show_name' => '电话', 'code' => '#企业_电话#','id'=>'tel','group_id' => self::ORDER_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['homepage'], 'show_name' => '主页', 'code' => '#企业_主页#','id'=>'homepage','group_id' => self::ORDER_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['email'], 'show_name' => '邮箱', 'code' => '#企业_邮箱#','id'=>'email','group_id' => self::ORDER_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['address'], 'show_name' => '地址', 'code' => '#企业_地址#','id'=>'address','group_id' => self::ORDER_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_ORDER],
                ]
            ],
        ],
        Constants::TYPE_QUOTATION => [
            self::QUOTATION_GROUP_EXCHANGE_PRODUCT => [
                'groups' => [0, self::QUOTATION_GROUP_PRODUCT, self::QUOTATION_GROUP_EXCHANGE_PRODUCT],
                'group' => self::QUOTATION_GROUP_EXCHANGE_PRODUCT,
                'fields' => [['base' => 1, 'columns' => ['invoice_product_no'], 'show_name' => '序号', 'code' => '#报价产品_序号#','id'=>'invoice_product_no','group_id'=>self::ORDER_GROUP_EXCHANGE_PRODUCT,'type'=> Constants::TYPE_QUOTATION]]
            ],
            self::QUOTATION_GROUP_EXTERNAL_INFO =>[
                'groups' => [0, self::QUOTATION_GROUP_EXTERNAL_INFO],
                'group' => self::QUOTATION_GROUP_EXTERNAL_INFO,
                'fields' => [
                    ['base' => 1, 'columns' => ['nickname'], 'show_name' => '导出人昵称', 'code' => '#导出人_昵称#','id'=>'nickname','group_id' => self::QUOTATION_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_QUOTATION],
                    ['base' => 1, 'columns' => ['external_email'], 'show_name' => '导出人邮箱', 'code' => '#导出人_邮箱#','id'=>'external_email','group_id' => self::QUOTATION_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_QUOTATION],
                    ['base' => 1, 'columns' => ['external_mobile'], 'show_name' => '导出人电话', 'code' => '#导出人_电话#','id'=>'external_mobile','group_id' => self::QUOTATION_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_QUOTATION],
                    ['base' => 1, 'columns' => ['external_fax'], 'show_name' => '导出人传真', 'code' => '#导出人_传真#','id'=>'external_fax','group_id' => self::QUOTATION_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_QUOTATION],
                    ['base' => 1, 'columns' => ['external_address'], 'show_name' => '导出人联系地址', 'code' => '#导出人_联系地址#','id'=>'external_address','group_id' => self::QUOTATION_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_QUOTATION],
                    ['base' => 1, 'columns' => ['external_other'], 'show_name' => '导出人其他信息', 'code' => '#导出人_其他信息#','id'=>'external_other','group_id' => self::QUOTATION_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_QUOTATION],
                    ['base' => 1, 'columns' => ['create_user_nickname'], 'show_name' => '创建人昵称', 'code' => '#创建人_昵称#','id'=>'create_user_nickname','group_id' => self::QUOTATION_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_QUOTATION],
                    ['base' => 1, 'columns' => ['create_user_email'], 'show_name' => '创建人邮箱', 'code' => '#创建人_邮箱#','id'=>'create_user_email','group_id' => self::QUOTATION_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_QUOTATION],
                    ['base' => 1, 'columns' => ['create_user_mobile'], 'show_name' => '创建人电话', 'code' => '#创建人_电话#','id'=>'create_user_mobile','group_id' => self::QUOTATION_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_QUOTATION],
                    ['base' => 1, 'columns' => ['create_user_fax'], 'show_name' => '创建人传真', 'code' => '#创建人_传真#','id'=>'create_user_fax','group_id' => self::QUOTATION_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_QUOTATION],
                    ['base' => 1, 'columns' => ['create_user_address'], 'show_name' => '创建人联系地址', 'code' => '#创建人_联系地址#','id'=>'create_user_address','group_id' => self::QUOTATION_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_QUOTATION],
                    ['base' => 1, 'columns' => ['create_user_other'], 'show_name' => '创建人其他信息', 'code' => '#创建人_其他信息#','id'=>'create_user_other','group_id' => self::QUOTATION_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_QUOTATION],
                ]
            ],
            self::QUOTATION_GROUP_ENTERPRISE_INFO =>[
                'groups' => [0, self::QUOTATION_GROUP_ENTERPRISE_INFO],
                'group' => self::QUOTATION_GROUP_ENTERPRISE_INFO,
                'fields' => [
                    ['base' => 1, 'columns' => ['short_name'], 'show_name' => '简称', 'code' => '#企业_简称#','id'=>'short_name','group_id' => self::QUOTATION_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['tel'], 'show_name' => '电话', 'code' => '#企业_电话#','id'=>'tel','group_id' => self::QUOTATION_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['homepage'], 'show_name' => '主页', 'code' => '#企业_主页#','id'=>'homepage','group_id' => self::QUOTATION_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['email'], 'show_name' => '邮箱', 'code' => '#企业_邮箱#','id'=>'email','group_id' => self::QUOTATION_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['address'], 'show_name' => '地址', 'code' => '#企业_地址#','id'=>'address','group_id' => self::QUOTATION_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_ORDER],
                ]
            ],
        ],

        Constants::TYPE_PURCHASE_ORDER => [
            self::PURCHASE_ORDER_GROUP_BASIC => [
                'groups' => [0, self::PURCHASE_ORDER_GROUP_BASIC],
                'group' => self::PURCHASE_ORDER_GROUP_BASIC,
                'fields' => [
                    ['base' => 1, 'columns' => ['purchase_sum_count'], 'show_name' => '采购数量总计', 'code' => '#采购数量总计#', 'id' => 'purchase_sum_count', 'group_id' => self::PURCHASE_ORDER_GROUP_BASIC, 'type' => Constants::TYPE_PURCHASE_ORDER],
                ],
            ],
            self::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT => [
                'groups' => [0, self::PURCHASE_ORDER_GROUP_PRODUCT, self::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT],
                'group' => self::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT,
                'fields' => [
                    ['base' => 1, 'columns' => [], 'show_name' => '序号', 'code' => '#采购产品_序号#', 'id' => 'purchase_product_no', 'group_id' => self::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_PURCHASE_ORDER],
                ],
            ],
            self::PURCHASE_ORDER_GROUP_EXTERNAL_INFO =>[
                'groups' => [0, self::PURCHASE_ORDER_GROUP_EXTERNAL_INFO],
                'group' => self::PURCHASE_ORDER_GROUP_EXTERNAL_INFO,
                'fields' => [
                    ['base' => 1, 'columns' => ['nickname'], 'show_name' => '导出人昵称', 'code' => '#导出人_昵称#','id'=>'nickname','group_id' => self::PURCHASE_ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_ORDER],
                    ['base' => 1, 'columns' => ['external_email'], 'show_name' => '导出人邮箱', 'code' => '#导出人_邮箱#','id'=>'external_email','group_id' => self::PURCHASE_ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_ORDER],
                    ['base' => 1, 'columns' => ['external_mobile'], 'show_name' => '导出人电话', 'code' => '#导出人_电话#','id'=>'external_mobile','group_id' => self::PURCHASE_ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_ORDER],
                    ['base' => 1, 'columns' => ['external_fax'], 'show_name' => '导出人传真', 'code' => '#导出人_传真#','id'=>'external_fax','group_id' => self::PURCHASE_ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_ORDER],
                    ['base' => 1, 'columns' => ['external_address'], 'show_name' => '导出人联系地址', 'code' => '#导出人_联系地址#','id'=>'external_address','group_id' => self::PURCHASE_ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_ORDER],
                    ['base' => 1, 'columns' => ['external_other'], 'show_name' => '导出人其他信息', 'code' => '#导出人_其他信息#','id'=>'external_other','group_id' => self::PURCHASE_ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_ORDER],
                    ['base' => 1, 'columns' => ['create_user_nickname'], 'show_name' => '创建人昵称', 'code' => '#创建人_昵称#','id'=>'create_user_nickname','group_id' => self::PURCHASE_ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_ORDER],
                    ['base' => 1, 'columns' => ['create_user_email'], 'show_name' => '创建人邮箱', 'code' => '#创建人_邮箱#','id'=>'create_user_email','group_id' => self::PURCHASE_ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_ORDER],
                    ['base' => 1, 'columns' => ['create_user_mobile'], 'show_name' => '创建人电话', 'code' => '#创建人_电话#','id'=>'create_user_mobile','group_id' => self::PURCHASE_ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_ORDER],
                    ['base' => 1, 'columns' => ['create_user_fax'], 'show_name' => '创建人传真', 'code' => '#创建人_传真#','id'=>'create_user_fax','group_id' => self::PURCHASE_ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_ORDER],
                    ['base' => 1, 'columns' => ['create_user_address'], 'show_name' => '创建人联系地址', 'code' => '#创建人_联系地址#','id'=>'create_user_address','group_id' => self::PURCHASE_ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_ORDER],
                    ['base' => 1, 'columns' => ['create_user_other'], 'show_name' => '创建人其他信息', 'code' => '#创建人_其他信息#','id'=>'create_user_other','group_id' => self::PURCHASE_ORDER_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_ORDER],
                ]
            ],
            self::PURCHASE_ORDER_GROUP_ENTERPRISE_INFO =>[
                'groups' => [0, self::PURCHASE_ORDER_GROUP_ENTERPRISE_INFO],
                'group' => self::PURCHASE_ORDER_GROUP_ENTERPRISE_INFO,
                'fields' => [
                    ['base' => 1, 'columns' => ['short_name'], 'show_name' => '简称', 'code' => '#企业_简称#','id'=>'short_name','group_id' => self::PURCHASE_ORDER_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['tel'], 'show_name' => '电话', 'code' => '#企业_电话#','id'=>'tel','group_id' => self::PURCHASE_ORDER_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['homepage'], 'show_name' => '主页', 'code' => '#企业_主页#','id'=>'homepage','group_id' => self::PURCHASE_ORDER_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['email'], 'show_name' => '邮箱', 'code' => '#企业_邮箱#','id'=>'email','group_id' => self::PURCHASE_ORDER_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_ORDER],
                    ['base' => 1, 'columns' => ['address'], 'show_name' => '地址', 'code' => '#企业_地址#','id'=>'address','group_id' => self::PURCHASE_ORDER_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_ORDER],
                ]
            ],
            self::PURCHASE_ORDER_GROUP_SUPPLIER => [
                'groups' => [0, self::PURCHASE_ORDER_GROUP_SUPPLIER, self::PURCHASE_ORDER_GROUP_SUPPLIER_PAYMENT_INFO],
                'group' => self::PURCHASE_ORDER_GROUP_SUPPLIER_PAYMENT_INFO,
                'fields' => [
                    ['base' => 1, 'columns' => ['bank_account'], 'show_name' => '收款账号', 'code' => '#供应商_收款账号#', 'id' => 'bank_account', 'group_id' => self::PURCHASE_ORDER_GROUP_SUPPLIER_PAYMENT_INFO, 'type' => Constants::TYPE_SUPPLIER],
                    ['base' => 1, 'columns' => ['account_name'], 'show_name' => '收款户名', 'code' => '#供应商_收款户名#', 'id' => 'account_name', 'group_id' => self::PURCHASE_ORDER_GROUP_SUPPLIER_PAYMENT_INFO, 'type' => Constants::TYPE_SUPPLIER],
                    ['base' => 1, 'columns' => ['bank_name'], 'show_name' => '收款银行', 'code' => '#供应商_收款银行#', 'id' => 'bank_name', 'group_id' => self::PURCHASE_ORDER_GROUP_SUPPLIER_PAYMENT_INFO, 'type' => Constants::TYPE_SUPPLIER],
                ]
            ],
        ],
        Constants::TYPE_PURCHASE_INBOUND_INVOICE => [
            self::PURCHASE_INBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT => [
                'groups' => [0, self::PURCHASE_INBOUND_INVOICE_GROUP_PRODUCT, self::PURCHASE_INBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT],
                'group' => self::PURCHASE_INBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT,
                'fields' => [['base' => 1, 'columns' => ['invoice_product_no'], 'show_name' => '序号', 'code' => '#入库明细_序号#','id'=>'invoice_product_no','group_id'=>self::PURCHASE_INBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT,'type'=> Constants::TYPE_PURCHASE_INBOUND_INVOICE]]
            ],
            self::PURCHASE_INBOUND_INVOICE_GROUP_EXTERNAL_INFO =>[
                'groups' => [0, self::PURCHASE_INBOUND_INVOICE_GROUP_EXTERNAL_INFO],
                'group' => self::PURCHASE_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,
                'fields' => [
                    ['base' => 1, 'columns' => ['nickname'], 'show_name' => '导出人昵称', 'code' => '#导出人_昵称#','id'=>'nickname','group_id' => self::PURCHASE_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['external_email'], 'show_name' => '导出人邮箱', 'code' => '#导出人_邮箱#','id'=>'external_email','group_id' => self::PURCHASE_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['external_mobile'], 'show_name' => '导出人电话', 'code' => '#导出人_电话#','id'=>'external_mobile','group_id' => self::PURCHASE_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['external_fax'], 'show_name' => '导出人传真', 'code' => '#导出人_传真#','id'=>'external_fax','group_id' => self::PURCHASE_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['external_address'], 'show_name' => '导出人联系地址', 'code' => '#导出人_联系地址#','id'=>'external_address','group_id' => self::PURCHASE_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['external_other'], 'show_name' => '导出人其他信息', 'code' => '#导出人_其他信息#','id'=>'external_other','group_id' => self::PURCHASE_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_nickname'], 'show_name' => '创建人昵称', 'code' => '#创建人_昵称#','id'=>'create_user_nickname','group_id' => self::PURCHASE_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_email'], 'show_name' => '创建人邮箱', 'code' => '#创建人_邮箱#','id'=>'create_user_email','group_id' => self::PURCHASE_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_mobile'], 'show_name' => '创建人电话', 'code' => '#创建人_电话#','id'=>'create_user_mobile','group_id' => self::PURCHASE_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_fax'], 'show_name' => '创建人传真', 'code' => '#创建人_传真#','id'=>'create_user_fax','group_id' => self::PURCHASE_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_address'], 'show_name' => '创建人联系地址', 'code' => '#创建人_联系地址#','id'=>'create_user_address','group_id' => self::PURCHASE_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_other'], 'show_name' => '创建人其他信息', 'code' => '#创建人_其他信息#','id'=>'create_user_other','group_id' => self::PURCHASE_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_INBOUND_INVOICE],
                ]
            ],
            self::PURCHASE_INBOUND_INVOICE_GROUP_ENTERPRISE_INFO =>[
                'groups' => [0, self::PURCHASE_INBOUND_INVOICE_GROUP_ENTERPRISE_INFO],
                'group' => self::PURCHASE_INBOUND_INVOICE_GROUP_ENTERPRISE_INFO,
                'fields' => [
                    ['base' => 1, 'columns' => ['short_name'], 'show_name' => '简称', 'code' => '#企业_简称#','id'=>'short_name','group_id' => self::PURCHASE_INBOUND_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_PURCHASE_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['tel'], 'show_name' => '电话', 'code' => '#企业_电话#','id'=>'tel','group_id' => self::PURCHASE_INBOUND_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_PURCHASE_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['homepage'], 'show_name' => '主页', 'code' => '#企业_主页#','id'=>'homepage','group_id' => self::PURCHASE_INBOUND_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_PURCHASE_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['email'], 'show_name' => '邮箱', 'code' => '#企业_邮箱#','id'=>'email','group_id' => self::PURCHASE_INBOUND_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_PURCHASE_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['address'], 'show_name' => '地址', 'code' => '#企业_地址#','id'=>'address','group_id' => self::PURCHASE_INBOUND_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_PURCHASE_INBOUND_INVOICE],
                ]
            ],
        ],
        Constants::TYPE_OTHER_INBOUND_INVOICE => [
            self::OTHER_INBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT => [
                'groups' => [0, self::OTHER_INBOUND_INVOICE_GROUP_PRODUCT, self::OTHER_INBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT],
                'group' => self::OTHER_INBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT,
                'fields' => [['base' => 1, 'columns' => ['invoice_product_no'], 'show_name' => '序号', 'code' => '#入库明细_序号#','id'=>'invoice_product_no','group_id'=>self::OTHER_INBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT,'type'=> Constants::TYPE_OTHER_INBOUND_INVOICE]]
            ],
            self::OTHER_INBOUND_INVOICE_GROUP_EXTERNAL_INFO =>[
                'groups' => [0, self::OTHER_INBOUND_INVOICE_GROUP_EXTERNAL_INFO],
                'group' => self::OTHER_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,
                'fields' => [
                    ['base' => 1, 'columns' => ['nickname'], 'show_name' => '导出人昵称', 'code' => '#导出人_昵称#','id'=>'nickname','group_id' => self::OTHER_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['external_email'], 'show_name' => '导出人邮箱', 'code' => '#导出人_邮箱#','id'=>'external_email','group_id' => self::OTHER_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['external_mobile'], 'show_name' => '导出人电话', 'code' => '#导出人_电话#','id'=>'external_mobile','group_id' => self::OTHER_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['external_fax'], 'show_name' => '导出人传真', 'code' => '#导出人_传真#','id'=>'external_fax','group_id' => self::OTHER_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['external_address'], 'show_name' => '导出人联系地址', 'code' => '#导出人_联系地址#','id'=>'external_address','group_id' => self::OTHER_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['external_other'], 'show_name' => '导出人其他信息', 'code' => '#导出人_其他信息#','id'=>'external_other','group_id' => self::OTHER_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_nickname'], 'show_name' => '创建人昵称', 'code' => '#创建人_昵称#','id'=>'create_user_nickname','group_id' => self::OTHER_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_email'], 'show_name' => '创建人邮箱', 'code' => '#创建人_邮箱#','id'=>'create_user_email','group_id' => self::OTHER_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_mobile'], 'show_name' => '创建人电话', 'code' => '#创建人_电话#','id'=>'create_user_mobile','group_id' => self::OTHER_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_fax'], 'show_name' => '创建人传真', 'code' => '#创建人_传真#','id'=>'create_user_fax','group_id' => self::OTHER_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_address'], 'show_name' => '创建人联系地址', 'code' => '#创建人_联系地址#','id'=>'create_user_address','group_id' => self::OTHER_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_other'], 'show_name' => '创建人其他信息', 'code' => '#创建人_其他信息#','id'=>'create_user_other','group_id' => self::OTHER_INBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_INBOUND_INVOICE],
                ]
            ],
            self::OTHER_INBOUND_INVOICE_GROUP_ENTERPRISE_INFO =>[
                'groups' => [0, self::OTHER_INBOUND_INVOICE_GROUP_ENTERPRISE_INFO],
                'group' => self::OTHER_INBOUND_INVOICE_GROUP_ENTERPRISE_INFO,
                'fields' => [
                    ['base' => 1, 'columns' => ['short_name'], 'show_name' => '简称', 'code' => '#企业_简称#','id'=>'short_name','group_id' => self::OTHER_INBOUND_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_OTHER_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['tel'], 'show_name' => '电话', 'code' => '#企业_电话#','id'=>'tel','group_id' => self::OTHER_INBOUND_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_OTHER_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['homepage'], 'show_name' => '主页', 'code' => '#企业_主页#','id'=>'homepage','group_id' => self::OTHER_INBOUND_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_OTHER_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['email'], 'show_name' => '邮箱', 'code' => '#企业_邮箱#','id'=>'email','group_id' => self::OTHER_INBOUND_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_OTHER_INBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['address'], 'show_name' => '地址', 'code' => '#企业_地址#','id'=>'address','group_id' => self::OTHER_INBOUND_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_OTHER_INBOUND_INVOICE],
                ]
            ],
        ],
        Constants::TYPE_SALE_OUTBOUND_INVOICE => [
            self::SALE_OUTBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT => [
                'groups' => [0, self::SALE_OUTBOUND_INVOICE_GROUP_PRODUCT, self::SALE_OUTBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT],
                'group' => self::SALE_OUTBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT,
                'fields' => [['base' => 1, 'columns' => ['invoice_product_no'], 'show_name' => '序号', 'code' => '#出库明细_序号#','id'=>'invoice_product_no','group_id'=>self::SALE_OUTBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT,'type'=> Constants::TYPE_SALE_OUTBOUND_INVOICE]]
            ],
            self::SALE_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO =>[
                'groups' => [0, self::SALE_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO],
                'group' => self::SALE_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,
                'fields' => [
                    ['base' => 1, 'columns' => ['nickname'], 'show_name' => '导出人昵称', 'code' => '#导出人_昵称#','id'=>'nickname','group_id' => self::SALE_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_SALE_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['external_email'], 'show_name' => '导出人邮箱', 'code' => '#导出人_邮箱#','id'=>'external_email','group_id' => self::SALE_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_SALE_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['external_mobile'], 'show_name' => '导出人电话', 'code' => '#导出人_电话#','id'=>'external_mobile','group_id' => self::SALE_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_SALE_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['external_fax'], 'show_name' => '导出人传真', 'code' => '#导出人_传真#','id'=>'external_fax','group_id' => self::SALE_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_SALE_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['external_address'], 'show_name' => '导出人联系地址', 'code' => '#导出人_联系地址#','id'=>'external_address','group_id' => self::SALE_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_SALE_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['external_other'], 'show_name' => '导出人其他信息', 'code' => '#导出人_其他信息#','id'=>'external_other','group_id' => self::SALE_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_SALE_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_nickname'], 'show_name' => '创建人昵称', 'code' => '#创建人_昵称#','id'=>'create_user_nickname','group_id' => self::SALE_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_SALE_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_email'], 'show_name' => '创建人邮箱', 'code' => '#创建人_邮箱#','id'=>'create_user_email','group_id' => self::SALE_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_SALE_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_mobile'], 'show_name' => '创建人电话', 'code' => '#创建人_电话#','id'=>'create_user_mobile','group_id' => self::SALE_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_SALE_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_fax'], 'show_name' => '创建人传真', 'code' => '#创建人_传真#','id'=>'create_user_fax','group_id' => self::SALE_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_SALE_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_address'], 'show_name' => '创建人联系地址', 'code' => '#创建人_联系地址#','id'=>'create_user_address','group_id' => self::SALE_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_SALE_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_other'], 'show_name' => '创建人其他信息', 'code' => '#创建人_其他信息#','id'=>'create_user_other','group_id' => self::SALE_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_SALE_OUTBOUND_INVOICE],
                ]
            ],
            self::SALE_OUTBOUND_INVOICE_GROUP_ENTERPRISE_INFO =>[
                'groups' => [0, self::SALE_OUTBOUND_INVOICE_GROUP_ENTERPRISE_INFO],
                'group' => self::SALE_OUTBOUND_INVOICE_GROUP_ENTERPRISE_INFO,
                'fields' => [
                    ['base' => 1, 'columns' => ['short_name'], 'show_name' => '简称', 'code' => '#企业_简称#','id'=>'short_name','group_id' => self::SALE_OUTBOUND_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_SALE_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['tel'], 'show_name' => '电话', 'code' => '#企业_电话#','id'=>'tel','group_id' => self::SALE_OUTBOUND_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_SALE_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['homepage'], 'show_name' => '主页', 'code' => '#企业_主页#','id'=>'homepage','group_id' => self::SALE_OUTBOUND_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_SALE_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['email'], 'show_name' => '邮箱', 'code' => '#企业_邮箱#','id'=>'email','group_id' => self::SALE_OUTBOUND_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_SALE_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['address'], 'show_name' => '地址', 'code' => '#企业_地址#','id'=>'address','group_id' => self::SALE_OUTBOUND_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_SALE_OUTBOUND_INVOICE],
                ]
            ],
        ],
        Constants::TYPE_OTHER_OUTBOUND_INVOICE => [
            self::OTHER_OUTBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT => [
                'groups' => [0, self::OTHER_OUTBOUND_INVOICE_GROUP_PRODUCT, self::OTHER_OUTBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT],
                'group' => self::OTHER_OUTBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT,
                'fields' => [['base' => 1, 'columns' => ['invoice_product_no'], 'show_name' => '序号', 'code' => '#出库明细_序号#','id'=>'invoice_product_no','group_id'=>self::OTHER_OUTBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT,'type'=> Constants::TYPE_OTHER_OUTBOUND_INVOICE]]
            ],
            self::OTHER_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO =>[
                'groups' => [0, self::OTHER_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO],
                'group' => self::OTHER_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,
                'fields' => [
                    ['base' => 1, 'columns' => ['nickname'], 'show_name' => '导出人昵称', 'code' => '#导出人_昵称#','id'=>'nickname','group_id' => self::OTHER_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['external_email'], 'show_name' => '导出人邮箱', 'code' => '#导出人_邮箱#','id'=>'external_email','group_id' => self::OTHER_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['external_mobile'], 'show_name' => '导出人电话', 'code' => '#导出人_电话#','id'=>'external_mobile','group_id' => self::OTHER_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['external_fax'], 'show_name' => '导出人传真', 'code' => '#导出人_传真#','id'=>'external_fax','group_id' => self::OTHER_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['external_address'], 'show_name' => '导出人联系地址', 'code' => '#导出人_联系地址#','id'=>'external_address','group_id' => self::OTHER_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['external_other'], 'show_name' => '导出人其他信息', 'code' => '#导出人_其他信息#','id'=>'external_other','group_id' => self::OTHER_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_nickname'], 'show_name' => '创建人昵称', 'code' => '#创建人_昵称#','id'=>'create_user_nickname','group_id' => self::OTHER_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_email'], 'show_name' => '创建人邮箱', 'code' => '#创建人_邮箱#','id'=>'create_user_email','group_id' => self::OTHER_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_mobile'], 'show_name' => '创建人电话', 'code' => '#创建人_电话#','id'=>'create_user_mobile','group_id' => self::OTHER_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_fax'], 'show_name' => '创建人传真', 'code' => '#创建人_传真#','id'=>'create_user_fax','group_id' => self::OTHER_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_address'], 'show_name' => '创建人联系地址', 'code' => '#创建人_联系地址#','id'=>'create_user_address','group_id' => self::OTHER_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_other'], 'show_name' => '创建人其他信息', 'code' => '#创建人_其他信息#','id'=>'create_user_other','group_id' => self::OTHER_OUTBOUND_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_OTHER_OUTBOUND_INVOICE],
                ]
            ],
            self::OTHER_OUTBOUND_INVOICE_GROUP_ENTERPRISE_INFO =>[
                'groups' => [0, self::OTHER_OUTBOUND_INVOICE_GROUP_ENTERPRISE_INFO],
                'group' => self::OTHER_OUTBOUND_INVOICE_GROUP_ENTERPRISE_INFO,
                'fields' => [
                    ['base' => 1, 'columns' => ['short_name'], 'show_name' => '简称', 'code' => '#企业_简称#','id'=>'short_name','group_id' => self::OTHER_OUTBOUND_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_OTHER_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['tel'], 'show_name' => '电话', 'code' => '#企业_电话#','id'=>'tel','group_id' => self::OTHER_OUTBOUND_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_OTHER_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['homepage'], 'show_name' => '主页', 'code' => '#企业_主页#','id'=>'homepage','group_id' => self::OTHER_OUTBOUND_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_OTHER_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['email'], 'show_name' => '邮箱', 'code' => '#企业_邮箱#','id'=>'email','group_id' => self::OTHER_OUTBOUND_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_OTHER_OUTBOUND_INVOICE],
                    ['base' => 1, 'columns' => ['address'], 'show_name' => '地址', 'code' => '#企业_地址#','id'=>'address','group_id' => self::OTHER_OUTBOUND_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_OTHER_OUTBOUND_INVOICE],
                ]
            ],
        ],
        Constants::TYPE_PURCHASE_RETURN_INVOICE => [
            self::PURCHASE_RETURN_INVOICE_GROUP_EXCHANGE_PRODUCT => [
                'groups' => [0, self::PURCHASE_RETURN_INVOICE_GROUP_PRODUCT, self::PURCHASE_RETURN_INVOICE_GROUP_EXCHANGE_PRODUCT],
                'group' => self::PURCHASE_RETURN_INVOICE_GROUP_EXCHANGE_PRODUCT,
                'fields' => [['base' => 1, 'columns' => ['invoice_product_no'], 'show_name' => '序号', 'code' => '#退货明细_序号#','id'=>'invoice_product_no','group_id'=>self::PURCHASE_RETURN_INVOICE_GROUP_EXCHANGE_PRODUCT,'type'=> Constants::TYPE_PURCHASE_RETURN_INVOICE]]
            ],
            self::PURCHASE_RETURN_INVOICE_GROUP_EXTERNAL_INFO =>[
                'groups' => [0, self::PURCHASE_RETURN_INVOICE_GROUP_EXTERNAL_INFO],
                'group' => self::PURCHASE_RETURN_INVOICE_GROUP_EXTERNAL_INFO,
                'fields' => [
                    ['base' => 1, 'columns' => ['nickname'], 'show_name' => '导出人昵称', 'code' => '#导出人_昵称#','id'=>'nickname','group_id' => self::PURCHASE_RETURN_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_RETURN_INVOICE],
                    ['base' => 1, 'columns' => ['external_email'], 'show_name' => '导出人邮箱', 'code' => '#导出人_邮箱#','id'=>'external_email','group_id' => self::PURCHASE_RETURN_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_RETURN_INVOICE],
                    ['base' => 1, 'columns' => ['external_mobile'], 'show_name' => '导出人电话', 'code' => '#导出人_电话#','id'=>'external_mobile','group_id' => self::PURCHASE_RETURN_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_RETURN_INVOICE],
                    ['base' => 1, 'columns' => ['external_fax'], 'show_name' => '导出人传真', 'code' => '#导出人_传真#','id'=>'external_fax','group_id' => self::PURCHASE_RETURN_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_RETURN_INVOICE],
                    ['base' => 1, 'columns' => ['external_address'], 'show_name' => '导出人联系地址', 'code' => '#导出人_联系地址#','id'=>'external_address','group_id' => self::PURCHASE_RETURN_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_RETURN_INVOICE],
                    ['base' => 1, 'columns' => ['external_other'], 'show_name' => '导出人其他信息', 'code' => '#导出人_其他信息#','id'=>'external_other','group_id' => self::PURCHASE_RETURN_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_RETURN_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_nickname'], 'show_name' => '创建人昵称', 'code' => '#创建人_昵称#','id'=>'create_user_nickname','group_id' => self::PURCHASE_RETURN_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_RETURN_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_email'], 'show_name' => '创建人邮箱', 'code' => '#创建人_邮箱#','id'=>'create_user_email','group_id' => self::PURCHASE_RETURN_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_RETURN_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_mobile'], 'show_name' => '创建人电话', 'code' => '#创建人_电话#','id'=>'create_user_mobile','group_id' => self::PURCHASE_RETURN_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_RETURN_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_fax'], 'show_name' => '创建人传真', 'code' => '#创建人_传真#','id'=>'create_user_fax','group_id' => self::PURCHASE_RETURN_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_RETURN_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_address'], 'show_name' => '创建人联系地址', 'code' => '#创建人_联系地址#','id'=>'create_user_address','group_id' => self::PURCHASE_RETURN_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_RETURN_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_other'], 'show_name' => '创建人其他信息', 'code' => '#创建人_其他信息#','id'=>'create_user_other','group_id' => self::PURCHASE_RETURN_INVOICE_GROUP_EXTERNAL_INFO,'type'=> Constants::TYPE_PURCHASE_RETURN_INVOICE],
                ]
            ],
            self::PURCHASE_RETURN_INVOICE_GROUP_ENTERPRISE_INFO =>[
                'groups' => [0, self::PURCHASE_RETURN_INVOICE_GROUP_ENTERPRISE_INFO],
                'group' => self::PURCHASE_RETURN_INVOICE_GROUP_ENTERPRISE_INFO,
                'fields' => [
                    ['base' => 1, 'columns' => ['short_name'], 'show_name' => '简称', 'code' => '#企业_简称#','id'=>'short_name','group_id' => self::PURCHASE_RETURN_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_PURCHASE_RETURN_INVOICE],
                    ['base' => 1, 'columns' => ['tel'], 'show_name' => '电话', 'code' => '#企业_电话#','id'=>'tel','group_id' => self::PURCHASE_RETURN_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_PURCHASE_RETURN_INVOICE],
                    ['base' => 1, 'columns' => ['homepage'], 'show_name' => '主页', 'code' => '#企业_主页#','id'=>'homepage','group_id' => self::PURCHASE_RETURN_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_PURCHASE_RETURN_INVOICE],
                    ['base' => 1, 'columns' => ['email'], 'show_name' => '邮箱', 'code' => '#企业_邮箱#','id'=>'email','group_id' => self::PURCHASE_RETURN_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_PURCHASE_RETURN_INVOICE],
                    ['base' => 1, 'columns' => ['address'], 'show_name' => '地址', 'code' => '#企业_地址#','id'=>'address','group_id' => self::PURCHASE_RETURN_INVOICE_GROUP_ENTERPRISE_INFO,'type'=> Constants::TYPE_PURCHASE_RETURN_INVOICE],
                ]
            ],
        ],
        Constants::TYPE_SHIPPING_INVOICE => [
            self::SHIPPING_INVOICE_GROUP_EXCHANGE_PRODUCT => [
                'groups' => [0, self::SHIPPING_INVOICE_GROUP_PRODUCT_BASE, self::SHIPPING_INVOICE_GROUP_EXCHANGE_PRODUCT],
                'group' => self::SHIPPING_INVOICE_GROUP_EXCHANGE_PRODUCT,
                'fields' => [['base' => 1, 'columns' => ['invoice_product_no'], 'show_name' => '序号', 'code' => '#出运明细_序号#', 'id' => 'invoice_product_no', 'group_id' => self::SHIPPING_INVOICE_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_SHIPPING_INVOICE]]
            ],
            self::SHIPPING_INVOICE_GROUP_PRODUCT_BASE => [
                'groups' => [0, self::SHIPPING_INVOICE_GROUP_PRODUCT_BASE],
                'group' => self::SHIPPING_INVOICE_GROUP_PRODUCT_BASE,
                'fields' => [
                    ['base' => 1, 'columns' => [], 'show_name' => '序号', 'code' => '#出运明细_序号#', 'id' => 'shipping_product_no', 'group_id' => self::SHIPPING_INVOICE_GROUP_PRODUCT_BASE, 'type' => Constants::TYPE_SHIPPING_INVOICE],
                ],
            ],
            self::SHIPPING_INVOICE_GROUP_PACKING_BASE => [
                'groups' => [0, self::SHIPPING_INVOICE_GROUP_PACKING_BASE],
                'group' => self::SHIPPING_INVOICE_GROUP_PACKING_BASE,
                'fields' => [
                    ['base' => 1, 'columns' => [], 'show_name' => '序号', 'code' => '#装箱明细_序号#', 'id' => 'shipping_packing_no', 'group_id' => self::SHIPPING_INVOICE_GROUP_PACKING_BASE, 'type' => Constants::TYPE_SHIPPING_INVOICE],
                ],
            ],
            self::SHIPPING_INVOICE_GROUP_CUSTOM_DECLARATION_BASE => [
                'groups' => [0, self::SHIPPING_INVOICE_GROUP_CUSTOM_DECLARATION_BASE],
                'group' => self::SHIPPING_INVOICE_GROUP_CUSTOM_DECLARATION_BASE,
                'fields' => [
                    ['base' => 1, 'columns' => [], 'show_name' => '序号', 'code' => '#报关明细_序号#', 'id' => 'shipping_custom_declaration_no', 'group_id' => self::SHIPPING_INVOICE_GROUP_CUSTOM_DECLARATION_BASE, 'type' => Constants::TYPE_SHIPPING_INVOICE],
                ],
            ],
            self::SHIPPING_INVOICE_GROUP_EXTERNAL_INFO => [
                'groups' => [0, self::SHIPPING_INVOICE_GROUP_EXTERNAL_INFO],
                'group' => self::SHIPPING_INVOICE_GROUP_EXTERNAL_INFO,
                'fields' => [
                    ['base' => 1, 'columns' => ['nickname'], 'show_name' => '导出人昵称', 'code' => '#导出人_昵称#', 'id' => 'nickname', 'group_id' => self::SHIPPING_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_SHIPPING_INVOICE],
                    ['base' => 1, 'columns' => ['external_email'], 'show_name' => '导出人邮箱', 'code' => '#导出人_邮箱#', 'id' => 'external_email', 'group_id' => self::SHIPPING_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_SHIPPING_INVOICE],
                    ['base' => 1, 'columns' => ['external_mobile'], 'show_name' => '导出人电话', 'code' => '#导出人_电话#', 'id' => 'external_mobile', 'group_id' => self::SHIPPING_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_SHIPPING_INVOICE],
                    ['base' => 1, 'columns' => ['external_fax'], 'show_name' => '导出人传真', 'code' => '#导出人_传真#', 'id' => 'external_fax', 'group_id' => self::SHIPPING_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_SHIPPING_INVOICE],
                    ['base' => 1, 'columns' => ['external_address'], 'show_name' => '导出人联系地址', 'code' => '#导出人_联系地址#', 'id' => 'external_address', 'group_id' => self::SHIPPING_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_SHIPPING_INVOICE],
                    ['base' => 1, 'columns' => ['external_other'], 'show_name' => '导出人其他信息', 'code' => '#导出人_其他信息#', 'id' => 'external_other', 'group_id' => self::SHIPPING_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_SHIPPING_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_nickname'], 'show_name' => '创建人昵称', 'code' => '#创建人_昵称#', 'id' => 'create_user_nickname', 'group_id' => self::SHIPPING_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_SHIPPING_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_email'], 'show_name' => '创建人邮箱', 'code' => '#创建人_邮箱#', 'id' => 'create_user_email', 'group_id' => self::SHIPPING_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_SHIPPING_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_mobile'], 'show_name' => '创建人电话', 'code' => '#创建人_电话#', 'id' => 'create_user_mobile', 'group_id' => self::SHIPPING_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_SHIPPING_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_fax'], 'show_name' => '创建人传真', 'code' => '#创建人_传真#', 'id' => 'create_user_fax', 'group_id' => self::SHIPPING_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_SHIPPING_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_address'], 'show_name' => '创建人联系地址', 'code' => '#创建人_联系地址#', 'id' => 'create_user_address', 'group_id' => self::SHIPPING_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_SHIPPING_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_other'], 'show_name' => '创建人其他信息', 'code' => '#创建人_其他信息#', 'id' => 'create_user_other', 'group_id' => self::SHIPPING_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_SHIPPING_INVOICE],
                ]
            ],
            self::SHIPPING_INVOICE_GROUP_ENTERPRISE_INFO => [
                'groups' => [0, self::SHIPPING_INVOICE_GROUP_ENTERPRISE_INFO],
                'group' => self::SHIPPING_INVOICE_GROUP_ENTERPRISE_INFO,
                'fields' => [
                    ['base' => 1, 'columns' => ['short_name'], 'show_name' => '简称', 'code' => '#企业_简称#', 'id' => 'short_name', 'group_id' => self::SHIPPING_INVOICE_GROUP_ENTERPRISE_INFO, 'type' => Constants::TYPE_SHIPPING_INVOICE],
                    ['base' => 1, 'columns' => ['tel'], 'show_name' => '电话', 'code' => '#企业_电话#', 'id' => 'tel', 'group_id' => self::SHIPPING_INVOICE_GROUP_ENTERPRISE_INFO, 'type' => Constants::TYPE_SHIPPING_INVOICE],
                    ['base' => 1, 'columns' => ['homepage'], 'show_name' => '主页', 'code' => '#企业_主页#', 'id' => 'homepage', 'group_id' => self::SHIPPING_INVOICE_GROUP_ENTERPRISE_INFO, 'type' => Constants::TYPE_SHIPPING_INVOICE],
                    ['base' => 1, 'columns' => ['email'], 'show_name' => '邮箱', 'code' => '#企业_邮箱#', 'id' => 'email', 'group_id' => self::SHIPPING_INVOICE_GROUP_ENTERPRISE_INFO, 'type' => Constants::TYPE_SHIPPING_INVOICE],
                    ['base' => 1, 'columns' => ['address'], 'show_name' => '地址', 'code' => '#企业_地址#', 'id' => 'address', 'group_id' => self::SHIPPING_INVOICE_GROUP_ENTERPRISE_INFO, 'type' => Constants::TYPE_SHIPPING_INVOICE],
                ]
            ],
        ],

        Constants::TYPE_INQUIRY_COLLABORATION => [
            self::INQUIRY_COLLABORATION_GROUP_EXCHANGE_PRODUCT => [
                'groups' => [0, self::INQUIRY_COLLABORATION_GROUP_EXCHANGE_PRODUCT],
                'group' => self::INQUIRY_COLLABORATION_GROUP_EXCHANGE_PRODUCT,
                'fields' => [
                    ['base' => 1,
                    'columns' => ['invoice_product_no'],
                    'show_name' => '序号',
                    'code' => '#询价产品_序号#',
                    'id'=>'invoice_product_no',
                    'group_id'=>self::INQUIRY_COLLABORATION_GROUP_EXCHANGE_PRODUCT,
                    'type'=> Constants::TYPE_INQUIRY_COLLABORATION
                    ]
                ]
            ],
            self::INQUIRY_COLLABORATION_GROUP_EXTERNAL_INFO => [
                'groups' => [0, self::INQUIRY_COLLABORATION_GROUP_EXTERNAL_INFO],
                'group' => self::INQUIRY_COLLABORATION_GROUP_EXTERNAL_INFO,
                'fields' => [
                    ['base' => 1, 'columns' => ['nickname'], 'show_name' => '导出人昵称', 'code' => '#导出人_昵称#', 'id' => 'nickname', 'group_id' => self::INQUIRY_COLLABORATION_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_INQUIRY_COLLABORATION],
                    ['base' => 1, 'columns' => ['external_email'], 'show_name' => '导出人邮箱', 'code' => '#导出人_邮箱#', 'id' => 'external_email', 'group_id' => self::INQUIRY_COLLABORATION_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_INQUIRY_COLLABORATION],
                    ['base' => 1, 'columns' => ['external_mobile'], 'show_name' => '导出人电话', 'code' => '#导出人_电话#', 'id' => 'external_mobile', 'group_id' => self::INQUIRY_COLLABORATION_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_INQUIRY_COLLABORATION],
                    ['base' => 1, 'columns' => ['external_fax'], 'show_name' => '导出人传真', 'code' => '#导出人_传真#', 'id' => 'external_fax', 'group_id' => self::INQUIRY_COLLABORATION_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_INQUIRY_COLLABORATION],
                    ['base' => 1, 'columns' => ['external_address'], 'show_name' => '导出人联系地址', 'code' => '#导出人_联系地址#', 'id' => 'external_address', 'group_id' => self::INQUIRY_COLLABORATION_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_INQUIRY_COLLABORATION],
                    ['base' => 1, 'columns' => ['external_other'], 'show_name' => '导出人其他信息', 'code' => '#导出人_其他信息#', 'id' => 'external_other', 'group_id' => self::INQUIRY_COLLABORATION_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_INQUIRY_COLLABORATION],
                    ['base' => 1, 'columns' => ['create_user_nickname'], 'show_name' => '创建人昵称', 'code' => '#创建人_昵称#', 'id' => 'create_user_nickname', 'group_id' => self::INQUIRY_COLLABORATION_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_INQUIRY_COLLABORATION],
                    ['base' => 1, 'columns' => ['create_user_email'], 'show_name' => '创建人邮箱', 'code' => '#创建人_邮箱#', 'id' => 'create_user_email', 'group_id' => self::INQUIRY_COLLABORATION_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_INQUIRY_COLLABORATION],
                    ['base' => 1, 'columns' => ['create_user_mobile'], 'show_name' => '创建人电话', 'code' => '#创建人_电话#', 'id' => 'create_user_mobile', 'group_id' => self::INQUIRY_COLLABORATION_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_INQUIRY_COLLABORATION],
                    ['base' => 1, 'columns' => ['create_user_fax'], 'show_name' => '创建人传真', 'code' => '#创建人_传真#', 'id' => 'create_user_fax', 'group_id' => self::INQUIRY_COLLABORATION_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_INQUIRY_COLLABORATION],
                    ['base' => 1, 'columns' => ['create_user_address'], 'show_name' => '创建人联系地址', 'code' => '#创建人_联系地址#', 'id' => 'create_user_address', 'group_id' => self::INQUIRY_COLLABORATION_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_INQUIRY_COLLABORATION],
                    ['base' => 1, 'columns' => ['create_user_other'], 'show_name' => '创建人其他信息', 'code' => '#创建人_其他信息#', 'id' => 'create_user_other', 'group_id' => self::INQUIRY_COLLABORATION_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_INQUIRY_COLLABORATION],
                ]
            ],
            self::INQUIRY_COLLABORATION_GROUP_ENTERPRISE_INFO => [
                'groups' => [0, self::INQUIRY_COLLABORATION_GROUP_ENTERPRISE_INFO],
                'group' => self::INQUIRY_COLLABORATION_GROUP_ENTERPRISE_INFO,
                'fields' => [
                    ['base' => 1, 'columns' => ['short_name'], 'show_name' => '简称', 'code' => '#企业_简称#', 'id' => 'short_name', 'group_id' => self::INQUIRY_COLLABORATION_GROUP_ENTERPRISE_INFO, 'type' => Constants::TYPE_INQUIRY_COLLABORATION],
                    ['base' => 1, 'columns' => ['tel'], 'show_name' => '电话', 'code' => '#企业_电话#', 'id' => 'tel', 'group_id' => self::INQUIRY_COLLABORATION_GROUP_ENTERPRISE_INFO, 'type' => Constants::TYPE_INQUIRY_COLLABORATION],
                    ['base' => 1, 'columns' => ['homepage'], 'show_name' => '主页', 'code' => '#企业_主页#', 'id' => 'homepage', 'group_id' => self::INQUIRY_COLLABORATION_GROUP_ENTERPRISE_INFO, 'type' => Constants::TYPE_INQUIRY_COLLABORATION],
                    ['base' => 1, 'columns' => ['email'], 'show_name' => '邮箱', 'code' => '#企业_邮箱#', 'id' => 'email', 'group_id' => self::INQUIRY_COLLABORATION_GROUP_ENTERPRISE_INFO, 'type' => Constants::TYPE_INQUIRY_COLLABORATION],
                    ['base' => 1, 'columns' => ['address'], 'show_name' => '地址', 'code' => '#企业_地址#', 'id' => 'address', 'group_id' => self::INQUIRY_COLLABORATION_GROUP_ENTERPRISE_INFO, 'type' => Constants::TYPE_INQUIRY_COLLABORATION],
                ]
            ],
        ],

        Constants::TYPE_PAYMENT_INVOICE => [
            self::PAYMENT_INVOICE_GROUP_RECORD => [
                'groups' => [0, self::PAYMENT_INVOICE_GROUP_RECORD],
                'group' => self::PAYMENT_INVOICE_GROUP_RECORD,
                'fields' => [
                    ['base' => 1,
                        'columns' => ['invoice_product_no'],
                        'show_name' => '序号',
                        'code' => '#付款明细_序号#',
                        'id' => 'invoice_product_no',
                        'group_id' => self::PAYMENT_INVOICE_GROUP_RECORD,
                        'type' => Constants::TYPE_PAYMENT_INVOICE
                    ]
                ]
            ],
            self::PAYMENT_INVOICE_GROUP_EXTERNAL_INFO => [
                'groups' => [0, self::PAYMENT_INVOICE_GROUP_EXTERNAL_INFO],
                'group' => self::PAYMENT_INVOICE_GROUP_EXTERNAL_INFO,
                'fields' => [
                    ['base' => 1, 'columns' => ['nickname'], 'show_name' => '导出人昵称', 'code' => '#导出人_昵称#', 'id' => 'nickname', 'group_id' => self::PAYMENT_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_PAYMENT_INVOICE],
                    ['base' => 1, 'columns' => ['external_email'], 'show_name' => '导出人邮箱', 'code' => '#导出人_邮箱#', 'id' => 'external_email', 'group_id' => self::PAYMENT_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_PAYMENT_INVOICE],
                    ['base' => 1, 'columns' => ['external_mobile'], 'show_name' => '导出人电话', 'code' => '#导出人_电话#', 'id' => 'external_mobile', 'group_id' => self::PAYMENT_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_PAYMENT_INVOICE],
                    ['base' => 1, 'columns' => ['external_fax'], 'show_name' => '导出人传真', 'code' => '#导出人_传真#', 'id' => 'external_fax', 'group_id' => self::PAYMENT_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_PAYMENT_INVOICE],
                    ['base' => 1, 'columns' => ['external_address'], 'show_name' => '导出人联系地址', 'code' => '#导出人_联系地址#', 'id' => 'external_address', 'group_id' => self::PAYMENT_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_PAYMENT_INVOICE],
                    ['base' => 1, 'columns' => ['external_other'], 'show_name' => '导出人其他信息', 'code' => '#导出人_其他信息#', 'id' => 'external_other', 'group_id' => self::PAYMENT_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_PAYMENT_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_nickname'], 'show_name' => '创建人昵称', 'code' => '#创建人_昵称#', 'id' => 'create_user_nickname', 'group_id' => self::PAYMENT_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_PAYMENT_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_email'], 'show_name' => '创建人邮箱', 'code' => '#创建人_邮箱#', 'id' => 'create_user_email', 'group_id' => self::PAYMENT_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_PAYMENT_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_mobile'], 'show_name' => '创建人电话', 'code' => '#创建人_电话#', 'id' => 'create_user_mobile', 'group_id' => self::PAYMENT_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_PAYMENT_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_fax'], 'show_name' => '创建人传真', 'code' => '#创建人_传真#', 'id' => 'create_user_fax', 'group_id' => self::PAYMENT_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_PAYMENT_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_address'], 'show_name' => '创建人联系地址', 'code' => '#创建人_联系地址#', 'id' => 'create_user_address', 'group_id' => self::PAYMENT_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_PAYMENT_INVOICE],
                    ['base' => 1, 'columns' => ['create_user_other'], 'show_name' => '创建人其他信息', 'code' => '#创建人_其他信息#', 'id' => 'create_user_other', 'group_id' => self::PAYMENT_INVOICE_GROUP_EXTERNAL_INFO, 'type' => Constants::TYPE_PAYMENT_INVOICE],
                ]
            ],
            self::PAYMENT_INVOICE_GROUP_ENTERPRISE_INFO => [
                'groups' => [0, self::PAYMENT_INVOICE_GROUP_ENTERPRISE_INFO],
                'group' => self::PAYMENT_INVOICE_GROUP_ENTERPRISE_INFO,
                'fields' => [
                    ['base' => 1, 'columns' => ['short_name'], 'show_name' => '简称', 'code' => '#企业_简称#', 'id' => 'short_name', 'group_id' => self::PAYMENT_INVOICE_GROUP_ENTERPRISE_INFO, 'type' => Constants::TYPE_PAYMENT_INVOICE],
                    ['base' => 1, 'columns' => ['tel'], 'show_name' => '电话', 'code' => '#企业_电话#', 'id' => 'tel', 'group_id' => self::PAYMENT_INVOICE_GROUP_ENTERPRISE_INFO, 'type' => Constants::TYPE_PAYMENT_INVOICE],
                    ['base' => 1, 'columns' => ['homepage'], 'show_name' => '主页', 'code' => '#企业_主页#', 'id' => 'homepage', 'group_id' => self::PAYMENT_INVOICE_GROUP_ENTERPRISE_INFO, 'type' => Constants::TYPE_PAYMENT_INVOICE],
                    ['base' => 1, 'columns' => ['email'], 'show_name' => '邮箱', 'code' => '#企业_邮箱#', 'id' => 'email', 'group_id' => self::PAYMENT_INVOICE_GROUP_ENTERPRISE_INFO, 'type' => Constants::TYPE_PAYMENT_INVOICE],
                    ['base' => 1, 'columns' => ['address'], 'show_name' => '地址', 'code' => '#企业_地址#', 'id' => 'address', 'group_id' => self::PAYMENT_INVOICE_GROUP_ENTERPRISE_INFO, 'type' => Constants::TYPE_PAYMENT_INVOICE],
                ]
            ],
        ],
    ];

    private $export_type;
    private $client_id;
    private $repository;
    private $skip_export_field = ['cost_item_relation_id', 'extra_cost_item'];
    private $skip_parts_product_export_field = ['product_total_count_no_parts', 'parts_total_count'];//主配产品特有字段

    //别名修改规则
    private $nickname_rule = [
        \Constants::TYPE_QUOTATION => [
            'cost' => '费用金额'
        ],
        \Constants::TYPE_ORDER => [
            'cost' => '费用金额'
        ],
        \Constants::TYPE_PURCHASE_ORDER => [
            'cost' => '费用金额'
        ]
    ];

    //提示
    private $tips_map = [
        \Constants::TYPE_ORDER => [
            'product_total_count_no_parts' => '不含配件',
        ],
        \Constants::TYPE_QUOTATION => [
            'product_total_count_no_parts' => '不含配件',
        ],
        \Constants::TYPE_PURCHASE_ORDER => [
            'product_total_count_no_parts' => '不含配件',
        ],
        \Constants::TYPE_SALE_OUTBOUND_INVOICE => [
            'product_total_count_no_parts' => '不含配件',
        ],
        \Constants::TYPE_SHIPPING_INVOICE => [
            'product_total_count_no_parts' => '不含配件',
        ],
    ];

    //拥有配件产品的单据
    private $parts_product_invoice = [
        \Constants::TYPE_ORDER => self::ORDER_GROUP_EXCHANGE_PRODUCT,
        \Constants::TYPE_QUOTATION => self::QUOTATION_GROUP_EXCHANGE_PRODUCT,
        \Constants::TYPE_PURCHASE_ORDER => self::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT,
//        \Constants::TYPE_PURCHASE_INBOUND_INVOICE => self::PURCHASE_INBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT,
        \Constants::TYPE_SALE_OUTBOUND_INVOICE => self::SALE_OUTBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT,
        \Constants::TYPE_SHIPPING_INVOICE => self::SHIPPING_INVOICE_GROUP_PRODUCT_BASE,
//        \Constants::TYPE_INQUIRY_COLLABORATION => self::INQUIRY_COLLABORATION_GROUP_EXCHANGE_PRODUCT,
    ];

    //主配产品小计统计
    private $parts_total_field = [
        \Constants::TYPE_ORDER => [
//            ['base' => 1, 'columns' => ['parts_total_product_total'], 'show_name' => '产品数量', 'code' => '#交易产品_主配小计_产品数量#', 'id' => 'parts_total_product_total', 'group_id' => self::ORDER_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_ORDER],
            ['base' => 1, 'columns' => ['parts_total_unit_price'], 'show_name' => '产品单价（单个主配）', 'code' => '#交易产品_主配小计_产品单价（单个主配）#', 'id' => 'parts_total_unit_price', 'group_id' => self::ORDER_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_ORDER],
            ['base' => 1, 'columns' => ['parts_total_cost'], 'show_name' => '产品成本（单个主配）', 'code' => '#交易产品_主配小计_产品成本（单个主配）#', 'id' => 'parts_total_cost', 'group_id' => self::ORDER_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_ORDER],
            ['base' => 1, 'columns' => ['parts_total_gross_profit'], 'show_name' => '产品毛利（单个主配）', 'code' => '#交易产品_主配小计_产品毛利（单个主配）#', 'id' => 'parts_total_gross_profit', 'group_id' => self::ORDER_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_ORDER],
            ['base' => 1, 'columns' => ['parts_total_amount'], 'show_name' => '产品金额（全部主配）', 'code' => '#交易产品_主配小计_产品金额（全部主配）#', 'id' => 'parts_total_amount', 'group_id' => self::ORDER_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_ORDER],
            ['base' => 1, 'columns' => ['parts_total_all_gross_profit'], 'show_name' => '产品毛利（全部主配）', 'code' => '#交易产品_主配小计_产品毛利（全部主配）#', 'id' => 'parts_total_all_gross_profit', 'group_id' => self::ORDER_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_ORDER],
        ],
        \Constants::TYPE_QUOTATION => [
            ['base' => 1, 'columns' => ['parts_total_unit_price'], 'show_name' => '产品单价（单个主配）', 'code' => '#报价产品_主配小计_产品单价（单个主配）#', 'id' => 'parts_total_unit_price', 'group_id' => self::QUOTATION_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_QUOTATION],
            ['base' => 1, 'columns' => ['parts_total_cost'], 'show_name' => '产品成本（单个主配）', 'code' => '#报价产品_主配小计_产品成本（单个主配）#', 'id' => 'parts_total_cost', 'group_id' => self::QUOTATION_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_QUOTATION],
//            ['base' => 1, 'columns' => ['parts_total_gross_profit'], 'show_name' => '产品毛利（单个主配）', 'code' => '#报价产品_主配小计_产品毛利（单个主配）#', 'id' => 'parts_total_gross_profit', 'group_id' => self::QUOTATION_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_QUOTATION],
            ['base' => 1, 'columns' => ['parts_total_amount'], 'show_name' => '产品金额（全部主配）', 'code' => '#报价产品_主配小计_产品金额（全部主配）#', 'id' => 'parts_total_amount', 'group_id' => self::QUOTATION_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_QUOTATION],
//            ['base' => 1, 'columns' => ['parts_total_all_gross_profit'], 'show_name' => '产品毛利（全部主配）', 'code' => '#报价产品_主配小计_产品毛利（全部主配）#', 'id' => 'parts_total_all_gross_profit', 'group_id' => self::QUOTATION_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_QUOTATION],
        ],
        \Constants::TYPE_PURCHASE_ORDER => [
            ['base' => 1, 'columns' => ['parts_total_unit_price'], 'show_name' => '采购单价（单个主配）', 'code' => '#采购产品_主配小计_采购单价（单个主配）#', 'id' => 'parts_total_unit_price', 'group_id' => self::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_PURCHASE_ORDER],
            ['base' => 1, 'columns' => ['parts_total_amount'], 'show_name' => '采购金额（全部主配）', 'code' => '#采购产品_主配小计_产品金额（全部主配）#', 'id' => 'parts_total_amount', 'group_id' => self::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_PURCHASE_ORDER],
        ],
        \Constants::TYPE_SHIPPING_INVOICE => [
            ['base' => 1, 'columns' => ['parts_total_unit_price'], 'show_name' => '产品单价（单个主配）', 'code' => '#出运明细_主配小计_产品单价（单个主配）#', 'id' => 'parts_total_unit_price', 'group_id' => self::SHIPPING_INVOICE_GROUP_PRODUCT_BASE, 'type' => Constants::TYPE_SHIPPING_INVOICE],
            ['base' => 1, 'columns' => ['parts_total_amount'], 'show_name' => '出运产品金额（全部主配）', 'code' => '#出运明细_主配小计_产品金额（全部主配）#', 'id' => 'parts_total_amount', 'group_id' => self::SHIPPING_INVOICE_GROUP_PRODUCT_BASE, 'type' => Constants::TYPE_SHIPPING_INVOICE],
        ],
    ];

    //导出时-需要的字段，在模板设置不需要展示
    private $parts_show_special_field = [
        \Constants::TYPE_ORDER => [
            self::ORDER_GROUP_EXCHANGE_PRODUCT => [
                ['base' => 1, 'columns' => ['unique_id'], 'show_name' => '订单产品id', 'code' => '#交易产品_订单产品id#', 'id' => 'unique_id', 'group_id' => self::ORDER_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_ORDER],
                ['base' => 1, 'columns' => ['is_master_product'], 'show_name' => '是否主产品', 'code' => '#交易产品_是否主产品#', 'id' => 'is_master_product', 'group_id' => self::ORDER_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_ORDER],
                ['base' => 1, 'columns' => ['master_id'], 'show_name' => '归属产品id', 'code' => '#交易产品_归属产品id#', 'id' => 'master_id', 'group_id' => self::ORDER_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_ORDER]
            ],
        ],
        \Constants::TYPE_QUOTATION => [
            self::QUOTATION_GROUP_EXCHANGE_PRODUCT => [
                ['base' => 1, 'columns' => ['unique_id'], 'show_name' => '订单产品id', 'code' => '#报价产品_订单产品id#', 'id' => 'unique_id', 'group_id' => self::QUOTATION_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_QUOTATION],
                ['base' => 1, 'columns' => ['is_master_product'], 'show_name' => '是否主产品', 'code' => '#报价产品_是否主产品#', 'id' => 'is_master_product', 'group_id' => self::QUOTATION_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_QUOTATION],
                ['base' => 1, 'columns' => ['master_id'], 'show_name' => '归属产品id', 'code' => '#报价产品_归属产品id#', 'id' => 'master_id', 'group_id' => self::QUOTATION_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_QUOTATION]
            ],
        ],
        \Constants::TYPE_PURCHASE_ORDER => [
            self::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT => [
                ['base' => 1, 'columns' => ['purchase_order_product_id'], 'show_name' => '采购产品id', 'code' => '#采购产品_采购产品id#', 'id' => 'purchase_order_product_id', 'group_id' => self::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_PURCHASE_ORDER],
                ['base' => 1, 'columns' => ['is_master_product'], 'show_name' => '是否主产品', 'code' => '#采购产品_是否主产品#', 'id' => 'is_master_product', 'group_id' => self::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_PURCHASE_ORDER],
                ['base' => 1, 'columns' => ['master_id'], 'show_name' => '归属产品id', 'code' => '#采购产品_归属产品id#', 'id' => 'master_id', 'group_id' => self::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_PURCHASE_ORDER]
            ],
        ],

        \Constants::TYPE_SALE_OUTBOUND_INVOICE => [
            self::SALE_OUTBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT => [
                ['base' => 1, 'columns' => ['outbound_record_id'], 'show_name' => '产品id', 'code' => '#出库明细_产品id#', 'id' => 'outbound_record_id', 'group_id' => self::SALE_OUTBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_SALE_OUTBOUND_INVOICE],
                ['base' => 1, 'columns' => ['is_master_product'], 'show_name' => '是否主产品', 'code' => '#出库明细_是否主产品#', 'id' => 'is_master_product', 'group_id' => self::SALE_OUTBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_SALE_OUTBOUND_INVOICE],
                ['base' => 1, 'columns' => ['master_id'], 'show_name' => '归属产品id', 'code' => '#出库明细_归属产品id#', 'id' => 'master_id', 'group_id' => self::SALE_OUTBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_SALE_OUTBOUND_INVOICE]
            ],
        ],

        \Constants::TYPE_SHIPPING_INVOICE => [
            self::SHIPPING_INVOICE_GROUP_PRODUCT_BASE => [
                ['base' => 1, 'columns' => ['shipping_record_id'], 'show_name' => '产品id', 'code' => '#出运明细_产品id#', 'id' => 'shipping_record_id', 'group_id' => self::SHIPPING_INVOICE_GROUP_PRODUCT_BASE, 'type' => Constants::TYPE_SHIPPING_INVOICE],
                ['base' => 1, 'columns' => ['is_master_product'], 'show_name' => '是否主产品', 'code' => '#出运明细_是否主产品#', 'id' => 'is_master_product', 'group_id' => self::SHIPPING_INVOICE_GROUP_PRODUCT_BASE, 'type' => Constants::TYPE_SHIPPING_INVOICE],
                ['base' => 1, 'columns' => ['master_id'], 'show_name' => '归属产品id', 'code' => '#出运明细_归属产品id#', 'id' => 'master_id', 'group_id' => self::SHIPPING_INVOICE_GROUP_PRODUCT_BASE, 'type' => Constants::TYPE_SHIPPING_INVOICE]
            ],
        ],
    ];

    //主配产品，展示特有字段：配比
    private $parts_special_field = [
        \Constants::TYPE_ORDER => [
            ['base' => 1, 'columns' => ['ratio'], 'show_name' => '配比', 'code' => '#交易产品_配件产品_配比#', 'id' => 'ratio', 'group_id' => self::ORDER_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_ORDER,'after_field'=>'count'],
        ],
        \Constants::TYPE_QUOTATION => [
            ['base' => 1, 'columns' => ['ratio'], 'show_name' => '配比', 'code' => '#报价产品_配件产品_配比#', 'id' => 'ratio', 'group_id' => self::QUOTATION_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_QUOTATION,'after_field'=>'count'],
        ],
        \Constants::TYPE_PURCHASE_ORDER => [
            ['base' => 1, 'columns' => ['ratio'], 'show_name' => '配比', 'code' => '#采购产品_配件产品_配比#', 'id' => 'ratio', 'group_id' => self::PURCHASE_ORDER_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_PURCHASE_ORDER,'after_field'=>'count'],
        ],
        \Constants::TYPE_SALE_OUTBOUND_INVOICE => [
            ['base' => 1, 'columns' => ['ratio'], 'show_name' => '配比', 'code' => '#出库明细_配件产品_配比#', 'id' => 'ratio', 'group_id' => self::SALE_OUTBOUND_INVOICE_GROUP_EXCHANGE_PRODUCT, 'type' => Constants::TYPE_SALE_OUTBOUND_INVOICE,'after_field'=>'outbound_count'],
        ],
        \Constants::TYPE_SHIPPING_INVOICE => [
            ['base' => 1, 'columns' => ['ratio'], 'show_name' => '配比', 'code' => '#出运明细_配件产品_配比#', 'id' => 'ratio', 'group_id' => self::SHIPPING_INVOICE_GROUP_PRODUCT_BASE, 'type' => Constants::TYPE_SHIPPING_INVOICE, 'after_field'=>'shipping_count'],
        ],
    ];

    const SPECIAL_EXPORT_GROUP_MAP = [
        Constants:: TYPE_SHIPPING_INVOICE => [
            Constants::TYPE_COMPANY => [
                self::ORDER_GROUP_COMPANY => self::SHIPPING_INVOICE_GROUP_COMPANY,
                self::ORDER_GROUP_CUSTOMER => self::SHIPPING_INVOICE_GROUP_CUSTOMER,
            ],
            Constants::TYPE_CUSTOMER => [
                self::ORDER_GROUP_COMPANY => self::SHIPPING_INVOICE_GROUP_COMPANY,
                self::ORDER_GROUP_CUSTOMER => self::SHIPPING_INVOICE_GROUP_CUSTOMER,
            ],
        ], Constants:: TYPE_INQUIRY_COLLABORATION => [
            Constants::TYPE_COMPANY => [
                self::ORDER_GROUP_COMPANY => self::INQUIRY_COLLABORATION_GROUP_COMPANY,
                self::ORDER_GROUP_CUSTOMER => self::INQUIRY_COLLABORATION_GROUP_CUSTOMER,

            ],
            Constants::TYPE_CUSTOMER => [
                self::ORDER_GROUP_COMPANY => self::INQUIRY_COLLABORATION_GROUP_COMPANY,
                self::ORDER_GROUP_CUSTOMER => self::INQUIRY_COLLABORATION_GROUP_CUSTOMER,
            ],
            Constants::TYPE_PRODUCT => [
                self::ORDER_GROUP_PRODUCT_PAGE => self::INQUIRY_COLLABORATION_GROUP_PRODUCT_PAGE,
            ],
        ], Constants::TYPE_PAYMENT_INVOICE => [
            //tips
            //这里上边为什么都用订单分组 包括脚本写数据 其实是巧合 订单分组3-4 和 客户字段的export_group刚好一致 所以不会有问题
            //需要注意脚本 与系统初始化其他对象的tbl_system_field的分组是否对得上
            Constants::TYPE_COMPANY => [
                self::COMPANY_GROUP_BASIC => self::PAYMENT_INVOICE_GROUP_COMPANY,
                self::CUSTOMER_GROUP_BASIC => self::PAYMENT_INVOICE_GROUP_CUSTOMER,
            ],
            Constants::TYPE_CUSTOMER => [
                self::COMPANY_GROUP_BASIC => self::PAYMENT_INVOICE_GROUP_COMPANY,
                self::CUSTOMER_GROUP_BASIC => self::PAYMENT_INVOICE_GROUP_CUSTOMER,
            ],
            Constants::TYPE_SUPPLIER => [
                self::SUPPLIER_GROUP_BASIC => self::PAYMENT_INVOICE_GROUP_SUPPLIER_INFO,
                self::SUPPLIER_GROUP_CONTACT => self::PAYMENT_INVOICE_GROUP_SUPPLIER_CONTACT,
            ]
        ]
    ];


    /**
     * FieldExportService constructor.
     * @param $export_type
     * @param $client_id
     */
    public function __construct(int $client_id, int $export_type)
    {
        $this->export_type = $export_type;
        $this->client_id = $client_id;
        $this->repository = new FieldExportRepository($client_id, $export_type);
    }

    public function groupMap()
    {
        return static::$scenario_group_map[$this->export_type];
    }

    public function formatMap()
    {
        return FieldMoreFormats::$formats_map[$this->export_type] ?? [];
    }

    public function getMoreFormateOfMultiple($key,$format_map,$fieldName): array {
        $moreFormats = [];
        if(array_key_exists($key,$format_map) && array_key_exists($fieldName, $format_map[$key])){
            $country_format_map = $format_map[$key];
            foreach ($country_format_map[$fieldName] as $countryMap){
                $moreFormats[] = $countryMap;
            }
        }
        return $moreFormats;
    }

    public function getMoreFormateOfSingle($key,$format_map,$fieldName): array {
        $moreFormats = [];
        if(array_key_exists($key,$format_map) && array_key_exists($fieldName, $format_map[$key])){
              $moreFormats[] = $format_map[$key][$fieldName];
        }
        return $moreFormats;
    }

    public function exportGroups()
    {
        $results = [];
        foreach ($this->groupMap() as $id => $group_info) {
            $info = [
                'id' => $id,
                'name' => \Yii::t('invoice',$group_info['name'])
            ];
            if (isset($group_info['parent'])) {
                $info['path'] = $group_info['parent'] . '-' . $info['id'];
                $results[$group_info['parent']]['subs'][] = $info;
            } else {
                $info['path'] = (string)$info['id'];
                $results[$id] = isset($results[$id]) ? $results + $info : $info;
            }
        }
        return array_values($results);
    }

    public function scenarios()
    {
        return array_keys(static::$scenario_group_map);
    }

    public function generateDictionary()
    {
        $fields = $this->repository->exportDictionaryByGroup();
        Helper::getOriginFieldInfos($this->client_id, $fields);
        $groupMap = $this->groupMap();
        $groupTree = $this->groupTree();

        //导出业务层：不需要考虑是否有主配产品功能，进行合并覆盖。
        $special_rule = array_merge(static::$special_rule, static::$parst_product_special_rule);
        $special_rules = $this->export_type != \Constants::TYPE_PURCHASE_ORDER ? array_merge($special_rule, static::$special_rule_price_count) : $special_rule;

        foreach ($fields as $k => $field) {

            //过滤已经禁用或者删除的字段
            if($field['disable_flag'] ==1
                || $field['is_exportable']==0
                || !in_array($field['type'],[
                    Constants::TYPE_ORDER,
                    Constants::TYPE_QUOTATION,
                    Constants::TYPE_COMPANY,
                    Constants::TYPE_CUSTOMER,
                    Constants::TYPE_PRODUCT,
                    Constants::TYPE_PURCHASE_ORDER,
                    Constants::TYPE_SUPPLIER,
                    Constants::TYPE_PURCHASE_INBOUND_INVOICE,
                    Constants::TYPE_OTHER_INBOUND_INVOICE,
                    Constants::TYPE_SALE_OUTBOUND_INVOICE,
                    Constants::TYPE_OTHER_OUTBOUND_INVOICE,
                    Constants::TYPE_PURCHASE_RETURN_INVOICE,
                    Constants::TYPE_SHIPPING_INVOICE,
                    Constants::TYPE_INQUIRY_COLLABORATION,
                    Constants::TYPE_PAYMENT_INVOICE,
                ])) {
                continue;
            }

            $field['columns'] = json_decode($field['columns'], true);

            // 需要将关联/引用字段中图片字段的field_type变从关联/引用(field_type=9/23)类型改为图片类型，否则java不会输出图片而是输出url
            if(
                $field['relation_field_type'] == CustomFieldService::FIELD_TYPE_IMAGE ||
                (isset($field['relation_origin_field_info']['field_type']) && $field['relation_origin_field_info']['field_type'] == CustomFieldService::FIELD_TYPE_IMAGE)
            ){
                $field['field_type'] = strval(CustomFieldService::FIELD_TYPE_IMAGE);
                $fields[$k] = $field;
            }

            //销售订单-产品图片 product_image 特殊处理
            if (in_array($this->export_type, [\Constants::TYPE_ORDER, \Constants::TYPE_QUOTATION]) && ($field['id'] == 'product_image')) {
                $field['field_type'] =  strval(CustomFieldService::FIELD_TYPE_IMAGE);
            }

            if(!isset($groupMap[$field['group_id']])){
                continue;
            }
            $group_info = $groupMap[$field['group_id']];


            if (array_key_exists($field['id'], $special_rules)) {
                $special_rule = $special_rules[$field['id']];
                foreach ($special_rule as &$item) {
                    $item['code'] = '#' . $group_info['prefix'] . $item['code'] . '#';
                }
                $rules = $special_rule;
            } else {
                $rules = [['name' => $field['name'], 'code' => '#' . $group_info['prefix'] . $field['name'] . '#']];
            }

            foreach ($rules as $rule) {
                if (isset($rule['index'])) {
                    $field['columns'] = [$field['id'] . '_' . $rule['index']];
                }
                $field['show_name'] = $rule['name'];
                $field['code'] = $rule['code'];
                if (isset($group_info['parent'])) {
                    $groupTree[$group_info['parent']]['subs'][$field['group_id']]['fields'][] = $field;
                } else {
                    $groupTree[$field['group_id']]['fields'][] = $field;
                }
            }
        }


        foreach ($groupTree as &$group) {
            foreach ($this->common_static_rule[$this->export_type] as $rule) {
                if ($group['id'] == $rule['group']) {
                    $group['fields'] = array_merge_recursive($group['fields'], $rule['fields']);
                }
            }
            foreach ($this->static_rule[$this->export_type] as $rule) {
                if ($group['id'] == $rule['group']) {
                    $group['fields'] = array_merge_recursive($group['fields'], $rule['fields']);
                }
            }
            if (isset($group['subs'])) {
                $group['subs'] = array_values($group['subs']);
                foreach ($group['subs'] as &$sub) {
                    foreach ($this->common_static_rule[$this->export_type] as $rule) {
                        if ($sub['id'] == $rule['group']) {
                            $sub['fields'] = array_merge_recursive($sub['fields'], $rule['fields']);
                        }
                    }
                    foreach ($this->static_rule[$this->export_type] as $rule) {
                        if ($sub['id'] == $rule['group']) {
                            $sub['fields'] = array_merge_recursive($sub['fields'], $rule['fields']);
                        }
                    }
                }
            }
        }

        //获取交易产品分组，然后进行配件产品字段插入、主配产品小计字段插入; 需要给特定的一些字段 进行主配渲染
        if (in_array($this->export_type, array_keys($this->parts_product_invoice))) {
            $groupId = $this->parts_product_invoice[$this->export_type];
            $productGroup = $groupTree[$groupId] ?? [];
            $partsProductField = [];
            $groupPrefix = $productGroup['prefix'] ?? '';

            foreach ($productGroup['fields'] ?? [] as $field) {
                //收集配件产品字段
                if (($field['group_id'] == $groupId) && !empty($field['name'])) {
                    $field['code'] = '#' .$groupPrefix .'配件产品_'. $field['name'] . '#';
                    $partsProductField[] = $field;
                }
            }

            //统计字段
            $partsTotalField = $this->parts_total_field[$this->export_type] ?? [];
            //组装产品-导出模板特别需要的字段
            $partsShowSpecialField = $this->parts_show_special_field[$this->export_type][$groupId] ?? [];
            $partsSpecialField = $this->parts_special_field[$this->export_type] ?? [];

            $groupTree[$groupId]['fields'] = array_merge($groupTree[$groupId]['fields'], $partsProductField, $partsTotalField, $partsShowSpecialField, $partsSpecialField);
        }

        $groupTree = array_values($groupTree);
        return $groupTree;
    }

    public function generateDictionaryByGroups(int $group_id)
    {
        $group_map = $this->groupMap();
        $groups = [];
        if (array_key_exists($group_id, $group_map)) {
            $groups = array_merge_recursive([$group_id], $group_map[$group_id]['sub']??[]);
        }
        $hasProductPartFunctionalFlag = \common\library\privilege_v3\Helper::checkHasProductPartFunctional($this->client_id);

        $special_rule = static::$special_rule;
        if ($hasProductPartFunctionalFlag) {//主配产品功能：展示层，需要做处理，在导出业务处理时，不需要考虑是否有主配产品功能
            $special_rule = array_merge($special_rule, static::$parst_product_special_rule);
        }
        $special_rules = $this->export_type != \Constants::TYPE_PURCHASE_ORDER ? array_merge($special_rule, static::$special_rule_price_count) : $special_rule;
        $fields = $this->repository->exportDictionaryByGroup($groups);
        $results = [];


        //处理特殊字段 special_rules
        foreach ($fields as $field) {
            //跳过不展示字段
            if (in_array($field['id'], $this->skip_export_field)) {
                continue;
            }

            //过滤已经禁用或者删除的字段
            if($field['disable_flag'] ==1
                || $field['is_exportable']==0
            || !in_array($field['type'],[
                    Constants::TYPE_ORDER,
                    Constants::TYPE_QUOTATION,
                    Constants::TYPE_COMPANY,
                    Constants::TYPE_CUSTOMER,
                    Constants::TYPE_PRODUCT,
                    Constants::TYPE_PURCHASE_ORDER,
                    Constants::TYPE_SUPPLIER,
                    Constants::TYPE_PURCHASE_INBOUND_INVOICE,
                    Constants::TYPE_OTHER_INBOUND_INVOICE,
                    Constants::TYPE_SALE_OUTBOUND_INVOICE,
                    Constants::TYPE_OTHER_OUTBOUND_INVOICE,
                    Constants::TYPE_PURCHASE_RETURN_INVOICE,
                    Constants::TYPE_SHIPPING_INVOICE,
                    Constants::TYPE_INQUIRY_COLLABORATION,
                    Constants::TYPE_PAYMENT_INVOICE,
                ])) {
                continue;
            }

            //额外字段不会有关联字段 但是没有设置relation_origin_field_type
            if (($field['relation_origin_field_type'] ?? 0) == CustomFieldService::FIELD_TYPE_ATTACH
                ||
                $field['field_type'] == CustomFieldService::FIELD_TYPE_ATTACH
            ) {
                continue;
            }

            //下游产品关联销售单产品图片，设置为图片类型
            if (is_numeric($field['id']) && ($field['relation_origin_type'] ?? 0) == \Constants::TYPE_ORDER && ($field['relation_origin_field'] ?? "") == 'product_image') {
                $field['field_type'] = CustomFieldService::FIELD_TYPE_IMAGE;
            }

            if (!$hasProductPartFunctionalFlag && in_array($field['id'], $this->skip_parts_product_export_field)) {
                continue;
            }

            $field['columns'] = json_decode($field['columns'], true);
            $group_info = $group_map[$field['group_id']] ?? [];
            if(empty($group_info)){
                continue;
            }

            if (array_key_exists($field['id'], $special_rules)) {
                $special_rule = $special_rules[$field['id']];
                foreach ($special_rule as &$item) {
                    $item['code'] = '#' . $group_info['prefix'] . $item['code'] . '#';
                }
                $rules = $special_rule;
            } else {
                //别名处理
                $nicknameRule = $this->nickname_rule[$this->export_type] ?? [];
                $nicknameFieldIds = array_keys($nicknameRule);
                if (in_array($field['id'], $nicknameFieldIds)) {
                    $fieldName = $nicknameRule[$field['id']] ?? $field['name'];
                    $rules = [['name' => $fieldName, 'code' => '#' . $group_info['prefix'] . $field['name'] . '#']];
                    $field['name'] = $fieldName;
                } else {
                    $rules = [['name' => $field['name'], 'code' => '#' . $group_info['prefix'] . $field['name'] . '#']];
                }
            }

            // 针对某些字段添加更多格式
            $format_map = $this->formatMap();
            $field['more_formats'] = $this-> getMoreFormateOfSingle("uppercase_format",$format_map,$field['id']);
            if ($field['group_id'] == self::ORDER_GROUP_BASIC){
                $field['more_formats'] = array_merge($field['more_formats'],$this-> getMoreFormateOfMultiple("country_format",$format_map,$field['id']));
            }
            foreach ($rules as $rule) {
                if (isset($rule['index'])) {
                $field['columns'] = [$field['id'] . '_' . $rule['index']];
            }
            $field['show_name'] = $rule['name'];
            $field['code'] = $rule['code'];
            if (isset($group_info['parent'])) {
                $groupTree[$group_info['parent']]['subs'][$field['group_id']]['fields'][] = $field;
            } else {
                $groupTree[$field['group_id']]['fields'][] = $field;
            }
            $results[] = $field;
            }
        }

        //处理common_rule
        foreach ($this->common_static_rule[$this->export_type] as $rule) {
            if (in_array($group_id, $rule['groups'])) {
                $results = array_merge_recursive($results, $rule['fields']);
            }
        }

        //处理static_rule
        foreach ($this->static_rule[$this->export_type] as $rule) {
            if (in_array($group_id, $rule['groups'])) {
                $results = array_merge_recursive($results, $rule['fields']);
            }
        }

        //字段自定义排序
        $results = $this->repository->sortExportFieldByCustomOrder($results, $groups, $this->export_type);

        //有主配产品功能，导出字段展示相关字段；如果没有该功能，需要过滤相关字段
        if ($hasProductPartFunctionalFlag) {
            $results = $this->buildPartProductExportField($results, $group_id, $group_map);
        }


        return $results;
    }

    public function groupTree()
    {
        $results = [];
        foreach ($this->groupMap() as $id => $group_info) {
            $info = compact('id') + $group_info + ['fields' => []];
            if (isset($group_info['parent'])) {
                $results[$group_info['parent']]['subs'][$id] = $info;
            } else {
                $results[$id] = isset($results[$id]) ? $results + $info : $info;
            }
        }
        return $results;
    }

    public function exportFields(int $group_id)
    {
        $group_map = $this->groupMap();
        $groups = [];
        if (array_key_exists($group_id, $group_map)) {
            $groups = array_merge_recursive([$group_id], $group_map[$group_id]['sub']??[]);
        }
        return array_map(function ($field) {
            $field['name'] = \Yii::t('field', $field['name']);
            return $field;
        }, $this->repository->exportFieldByGroup($groups));
    }

    /**
     * 更新字段导出设置
     *
     * @param $id
     * @param $is_exportable
     * @param $type
     * @return bool
     */
    public function updateExportFieldSetting($id, $is_exportable, $type)
    {
        $data = compact('id', 'is_exportable', 'type');
        $data['client_id'] = $this->client_id;
        $data['export_type'] = $this->export_type;
        return $this->repository->updateExportFieldSetting($data);
    }

    /**
     * 获取导出占用的特别字段名
     * @return array
     */
    public function getSpecialFieldName()
    {
        $specialFieldName = [];
        foreach ($this->common_static_rule[$this->export_type]??[] as $rule) {
            $specialFieldName = array_merge($specialFieldName, array_column($rule['fields'] ?? [], 'show_name'));
        }
        return $specialFieldName;
    }

    /**
     * 构建配件产品、主配产品的导出字段
     */
    protected function buildPartProductExportField($results,$group_id,$group_map)
    {
        if (in_array($this->export_type, array_keys($this->parts_product_invoice))) {
            //给字段提示语、新增字段类型，支持二级导出字段、新增字段类型，特定总计字段
            $tipsMap = $this->tips_map[$this->export_type] ?? [];
            $partsProductField = [];
            $groupId = $this->parts_product_invoice[$this->export_type];
            if (!empty($group_id) && ($group_id != $groupId)) {
                //不是交易产品的不用返回 //主要在导出设置默认值时,会调用到这里 php-crm/protected/library/invoice/export/AbstractInvoiceExport.php:883
                return $results;
            }
            $groupPrefix = $group_map[$groupId]['prefix'] ?? '';
            foreach ($results as $index => $result) {
                //提示语插入
                isset($tipsMap[$result['id']]) && $results[$index]['tips'] = $tipsMap[$result['id']];

                //收集配件产品字段
                if (($result['group_id'] == $groupId) && !empty($result['name'])) {
                    $result['code'] = '#' .$groupPrefix .'配件产品_'. $result['name'] . '#';
                    $partsProductField[] = $result;
                }
            }

            //插入配件产品特有字段, 配比字段插入到数量后
            foreach ($this->parts_special_field[$this->export_type] ??[] as $specialField) {
                if (!empty($specialField['after_field'])) {
                    //插入到指定字段后
                    $index = array_search($specialField['after_field'], array_column($partsProductField, 'id'));
                    if ($index !== false) {
                        array_splice($partsProductField, $index + 1, 0, [$specialField]);
                    } else {
                        $partsProductField = array_merge($partsProductField, [$specialField]);
                    }

                } else {
                    $partsProductField = array_merge($partsProductField, [$specialField]);
                }
            }

            //插入到交易产品中
            $results[] = [
                'base' => 1,
                'id' => 'parts_product',
                'show_name' => "配件产品",
                'group_id' => $groupId,
                'type' => $this->export_type,
                'sub_fields' => $partsProductField
            ];

            //组装 主配产品小计 ; tips:销售出库单没有小计，跳过该配置
            if ($this->export_type != \Constants::TYPE_SALE_OUTBOUND_INVOICE) {
                $results[] = ['base' => 1,
                    'id' => 'parts_product_subtotal',
                    'show_name' => "主配产品小计",
                    'group_id' => $groupId,
                    'type' => $this->export_type,
                    'sub_fields' => $this->parts_total_field[$this->export_type] ?? []
                ];
            }
        }

        return $results;
    }

}
