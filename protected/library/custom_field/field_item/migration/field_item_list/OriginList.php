<?php
/**
 * Created by <PERSON><PERSON><PERSON>tor<PERSON>.
 * User: beenzhang
 * Date: 2018/8/20
 * Time: 上午12:13
 */

namespace common\library\custom_field\field_item\migration\field_item_list;

use common\library\custom_field\field_item\migration\FieldItemSetting;

class OriginList extends SettingList{
    //系统来源
    const SYS_ORIGIN_MAP = [
        \common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_DISCOVERY => ['item_name' => '小满发现', 'item_id' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_DISCOVERY, 'field_type' => FieldItemSetting::FIELD_TYPE_SYS, 'enable_flag' => 1, 'color' => '#cccccc', 'order_rank' => '', 'expand_field' => '', 'update_user' => 0],
        \common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_MARKETING => ['item_name' => '小满营销邮件', 'item_id' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_MARKETING, 'field_type' => FieldItemSetting::FIELD_TYPE_SYS, 'enable_flag' => 1, 'color' => '#cccccc', 'order_rank' => '', 'expand_field' => '', 'update_user' => 0],
        \common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_AI_RECOMMEND => ['item_name' => 'AI推荐', 'item_id' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_AI_RECOMMEND, 'field_type' => FieldItemSetting::FIELD_TYPE_SYS, 'enable_flag' => 1, 'color' => '#cccccc', 'order_rank' => '', 'expand_field' => '', 'update_user' => 0],
        \common\library\setting\library\origin\Origin::SYS_ORIGIN_ALIBABA => ['item_name' => '阿里巴巴', 'item_id' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_ALIBABA, 'field_type' => FieldItemSetting::FIELD_TYPE_SYS, 'enable_flag' => 1, 'color' => '#cccccc', 'order_rank' => '', 'expand_field' => '', 'update_user' => 0],
        \common\library\setting\library\origin\Origin::SYS_ORIGIN_GLOBAL_SOURCES => ['item_name' => '环球资源', 'item_id' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_GLOBAL_SOURCES, 'field_type' => FieldItemSetting::FIELD_TYPE_SYS, 'enable_flag' => 1, 'color' => '#cccccc', 'order_rank' => '', 'expand_field' => '', 'update_user' => 0],
        \common\library\setting\library\origin\Origin::SYS_ORIGIN_GLOBAL_MARKET => ['item_name' => '环球市场', 'item_id' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_GLOBAL_MARKET, 'field_type' => FieldItemSetting::FIELD_TYPE_SYS, 'enable_flag' => 1, 'color' => '#cccccc', 'order_rank' => '', 'expand_field' => '', 'update_user' => 0],
        \common\library\setting\library\origin\Origin::SYS_ORIGIN_MADE_IN_CHINA => ['item_name' => '中国制造', 'item_id' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_MADE_IN_CHINA, 'field_type' => FieldItemSetting::FIELD_TYPE_SYS, 'enable_flag' => 1, 'color' => '#cccccc', 'order_rank' => '', 'expand_field' => '', 'update_user' => 0],
        \common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_ASSISTANT => ['item_name' => '小满助手', 'item_id' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_ASSISTANT, 'field_type' => FieldItemSetting::FIELD_TYPE_SYS, 'enable_flag' => 1, 'color' => '#cccccc', 'order_rank' => '', 'expand_field' => '', 'update_user' => 0],
        \common\library\setting\library\origin\Origin::SYS_ORIGIN_WEBSITE => ['item_name' => '官网询盘 (OKKI Marketing)', 'item_id' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_WEBSITE, 'field_type' => FieldItemSetting::FIELD_TYPE_SYS, 'enable_flag' => 1, 'color' => '#cccccc', 'order_rank' => '', 'expand_field' => '', 'update_user' => 0],
        \common\library\setting\library\origin\Origin::SYS_ORIGIN_SOCIAL_PLATFORM => ['item_name' => '社交平台', 'item_id' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_SOCIAL_PLATFORM, 'field_type' => FieldItemSetting::FIELD_TYPE_SYS, 'enable_flag' => 1, 'color' => '#cccccc', 'order_rank' => '', 'expand_field' => '', 'update_user' => 0],
        \common\library\setting\library\origin\Origin::SYS_ORIGIN_ELECTRICITY_PLATFORM => ['item_name' => '电商平台', 'item_id' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_ELECTRICITY_PLATFORM, 'field_type' => FieldItemSetting::FIELD_TYPE_SYS, 'enable_flag' => 1, 'color' => '#cccccc', 'order_rank' => '', 'expand_field' => '', 'update_user' => 0],
        \common\library\setting\library\origin\Origin::SYS_ORIGIN_SYSTEM_MARKETING => ['item_name' => '潜客运营', 'item_id' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_SYSTEM_MARKETING, 'field_type' => FieldItemSetting::FIELD_TYPE_SYS, 'enable_flag' => 1, 'color' => '#cccccc', 'order_rank' => '', 'expand_field' => '', 'update_user' => 0],
//        新增系统来源要同步到lead->archiveReport方法（用于os上报）
        ];

    protected function getItemType()
    {
        return \common\library\setting\item\ItemSettingConstant::MIGRATION_ITEM_TYPE_OF_ORIGIN;
    }

    protected function init()
    {
        $this->systemDefaultMap = self::SYS_ORIGIN_MAP;
    }

    public function includeUnknownOriginInfo()
    {
        $this->systemDefaultMap = array_merge($this->systemDefaultMap, [
            \common\library\setting\library\origin\Origin::SYS_ORIGIN_UNKNOWN => ['item_name' => '未知来源', 'item_id' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_UNKNOWN, 'field_type' => FieldItemSetting::FIELD_TYPE_SYS, 'enable_flag' => 1, 'color' => '#cccccc', 'order_rank' => '', 'expand_field' => '', 'update_user' => 0],
        ]);
    }
}
