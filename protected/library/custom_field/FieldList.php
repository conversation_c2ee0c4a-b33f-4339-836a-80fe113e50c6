<?php
/**
 * Created by PhpStorm.
 * User: andy
 * Date: 2017/6/20
 * Time: 15:43
 */

namespace common\library\custom_field;

use common\library\privilege_v3\object_service\UserObjectPrivilegeService;
use common\library\APIConstant;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\util\SqlBuilder;

/**
 * Class FieldList
 * @var CustomFieldFormatter formatter
 * @method  CustomFieldFormatter getFormatter()
 */
class FieldList extends \MysqlList
{
    const TBL_CUSTOM_FIELD = 'tbl_custom_field';
    const TBL_FIELD_GROUP = 'tbl_field_group';

    const ORDER_PC = 'order';
    const ORDER_APP = 'app_order';

    protected $fields;

    protected $clientId;
    protected $type;
    protected $base;
    protected $fieldType;
    protected $require;
    protected $editRequired;
    protected $disableFlag;
    protected $editHide;
    protected $editDefault;
    protected $editHint;
    protected $isEditable;
    protected $isList;
    protected $relationFlag = 0;
    protected $enableFlag = 1;
    protected $readonly;
    protected $id;
    protected $groupId;
    protected $excludeGroupId;
    protected $excludeId = [];
    protected $excludeFlag = true;
    protected $extraMap = [];
    protected $excludeFieldType = [];
    protected $needList = false;
    protected $showDisableFlagByFalseField = [];
    protected $scene = 'list';
    protected $headerSort = false;
    protected $filterDefaultValue = false;

    protected $userId;
    protected $relationFieldType;
    protected $relationType;
    protected $relationField;
    protected $relationOriginField;
    protected $relationOriginType;
    protected $relationOriginFieldType;
    protected $rawWhere = [];

    protected $functionalId;
    protected $hideValueForDisable = false;
    protected $ignorePrivilege = false;     // 是否忽略字段权限
    private $tableAlias = [
        self::TBL_CUSTOM_FIELD => 'A',
        self::TBL_FIELD_GROUP => 'B'
    ];

    /**
     * @param string $scene
     */
    public function setScene(string $scene): void
    {
        $this->scene = $scene;
    }


    private $fieldMap = [
        self::TBL_CUSTOM_FIELD => [
            'client_id', 'id', 'type', 'base', 'name', 'field_type', 'ext_info', 'require', 'edit_required',
            'disable_flag', 'edit_hide', 'default', 'edit_default', 'hint', 'edit_hint', 'is_editable', 'is_list',
            'columns', 'order', 'app_order', 'enable_flag', 'create_time', 'update_time', 'relation_type', 'relation_field_type', 'relation_field', 'readonly',
            'unique_check', 'unique_prevent', 'unique_message','relation_field_name','relation_origin_type','relation_origin_field','relation_origin_field_type'
        ],
        self::TBL_FIELD_GROUP => [
            'group_id'
        ]
    ];

    //在特殊场景下 只允许展示的字段
    const SPECIFIC_SHOW_FIELD_MAP =[
        \Constants::TYPE_PURCHASE_ORDER => [
            'cost_amount',
            'count',
            'have_inbound_count',
            'have_return_count',
            'order_no',
            'product_cn_name',
            'product_image',
            'product_model',
            'product_name',
            'product_no',
            'product_remark',
            'sku_id',
            'supplier_product_name',
            'supplier_product_no',
            'unit',
            'unit_price',
            'wait_inbound_count',
            'transfer_invoice_serial_id'
        ],
        \Constants::TYPE_PRODUCT_TRANSFER_INBOUND => [
            'product_cn_name',
            'product_image',
            'product_model',
            'product_name',
            'product_no',
            'sku_id',
            'product_unit',
            'purchase_count',
            'description',
            'record_remark',
            'to_inbound_count',
            'task_inbound_count',
            'have_inbound_count',
            'reach_count',
        ]
    ];
    
    const ORIGIN_FIELD = ['origin', 'origin_list'];


//    产品需求，字段限定范围：邮箱、社媒、电话、职位、关联联系人
    const SPECIFIC_DISABLE_FIELD_MAP = [
        \Constants::TYPE_CUSTOMER      => [
            'email',
            'contact',
            'tel_list',
            'post',
        ],
        \Constants::TYPE_LEAD_CUSTOMER => [
            'email',
            'contact',
            'tel_list',
            'post',
        ],
        \Constants::TYPE_OPPORTUNITY   => [
            'customer_id',
        ],
    ];
    
    
    //模块对应的创建权限
    private $type2CreatePrivilegeMap = [
        \Constants::TYPE_PURCHASE_INBOUND_INVOICE => [
            PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_INBOUND_CREATE,
            ],
        \Constants::TYPE_SALE_OUTBOUND_INVOICE => [
            PrivilegeConstants::PRIVILEGE_CRM_SALES_OUTBOUND_CREATE,
        ],
    ];
    protected $ignoreFormat = false;
    
    public function __construct($clientId)
    {
        $this->clientId = $clientId;
        $this->formatter = new CustomFieldFormatter();
        $this->formatter->setClientId($clientId);
        $this->setOrderBy(self::ORDER_PC);
    }

    /**
     * @param bool $hideValueForDisable
     */
    public function setHideValueForDisable($hideValueForDisable)
    {
        $this->hideValueForDisable = $hideValueForDisable;
    }

    public function setPrivilegeInfo($userId, $functionalId = null)
    {
        $this->userId = $userId;
        if ($functionalId) {
            $this->functionalId = $functionalId;
        } elseif (!is_array($this->type)) {
            $this->functionalId = \common\library\privilege_v3\Helper::getFunctionalIdByRefer($this->type);
        }
    }

    public function setIgnorePrivilege($flag){
        $this->ignorePrivilege = $flag;
    }

    /**
     * @param mixed $id
     */
    public function setId($id) {
        
        $id = (array)$id;
        
        if (array_intersect($id, \common\library\custom_field\FieldList::ORIGIN_FIELD)) {
            
            $id = array_merge($id, \common\library\custom_field\FieldList::ORIGIN_FIELD);
        }
        $this->id = $id;
    }

    public function setExcludeId($id)
    {
        $this->excludeId = array_filter(is_array($id) ? $id : [$id]);
    }
    public function setExcludeFlag($flag)
    {
        $this->excludeFlag = $flag;
    }

    public function setExcludeFieldType($fieldType) {
        $this->excludeFieldType = $fieldType;
    }

    public function setChangeToCanAddCustomField(bool $changeToCanAddCustomField)
    {
        $this->changeToCanAddCustomField = $changeToCanAddCustomField;
    }

    public function setShowSearchable(){
        $this->getFormatter()->setShowSearchable();

        if(!empty($this->fields)){
            $this->fields = array_merge($this->fields, ['field_type','is_list','disable_flag','relation_origin_type','relation_origin_field', 'type']);
            $this->fields = array_unique($this->fields);
        }
    }

    /**
     * 字段类型，参考 Constants::TYPE_* 字段
     *
     * @param $type
     */
    public function setType($type)
    {
        $this->type = $type;
        $this->formatter->setType($type);
    }

    public function setRelationFieldType($fieldTypes){
        $this->relationFieldType = $fieldTypes;
    }

    public function setRelationType($relationTypes){
        $this->relationType = is_array($relationTypes) ? $relationTypes : [$relationTypes];
    }

    public function setRelationField($relationFields){
        $this->relationField = is_array($relationFields) ? $relationFields : [$relationFields];
    }

    public function setRelationOriginField($relationOriginFields){
        $this->relationOriginField = is_array($relationOriginFields) ? $relationOriginFields : [$relationOriginFields];
    }

    public function setRelationOriginType($relationOriginType){
        $this->relationOriginType = is_array($relationOriginType) ? $relationOriginType : [$relationOriginType];
    }

    public function setRelationOriginFieldType($relationOriginFieldType){
        $this->relationOriginFieldType = is_array($relationOriginFieldType) ? $relationOriginFieldType : [$relationOriginFieldType];
    }

    public function addRawWhere($where){
        $where = trim(trim($where), 'and');
        $this->rawWhere[] = $where;
    }

    /**
     * 系统字段
     * null：所有字段，0：自定义字段，1：系统字段
     *
     * @param mixed $base
     */
    public function setBase($base)
    {
        $this->base = $base;
    }

    /**
     * 是否必填
     *
     * @param mixed $require
     */
    public function setRequire($require)
    {
        $this->require = $require;
    }

    /**
     * 过滤掉没有默认值的字段：true-过滤，false-不过滤
     *
     * @param $flag
     */
    public function setFilterDefaultValue($flag)
    {
        $this->filterDefaultValue = $flag;
    }

    /**
     * 筛选分组
     *
     * @param mixed $groupId
     * @return $this
     */
    public function setGroupId($groupId)
    {
        if( !empty($groupId) )
        {
            $groupId = is_array($groupId)?array_filter($groupId,'is_numeric'): intval($groupId);
        }
        $this->groupId = $groupId;
        return $this;
    }

    /**
     *
     * @param mixed $excludeGroupId
     */
    public function setExcludeGroupId($excludeGroupId)
    {
        if( !empty($excludeGroupId) )
        {
            $excludeGroupId = is_array($excludeGroupId)?array_filter($excludeGroupId,'is_numeric'): intval($excludeGroupId);
        }
        $this->excludeGroupId = $excludeGroupId;
    }

    /**
     * 是否查询引用字段的源目标字段情况
     *
     * @param $relation
     */
    public function setRelationFlag($relation)
    {
        $this->relationFlag = $relation;
    }

    /**
     * client_id
     *
     * @param int $clientId
     */
    public function setClientId($clientId)
    {
        $this->clientId = $clientId;
    }

    /**
     * 设置默认值：null-所有，0-不可设置，1-可设置
     *
     * @param mixed $editDefault
     * @return $this
     */
    public function setEditDefault($editDefault)
    {
        $this->editDefault = $editDefault;
        return $this;
    }

    /**
     * 是否隐藏：null-不区分，0-显示，1-隐藏
     *
     * @param mixed $disableFlag
     * @return $this
     */
    public function setDisableFlag($disableFlag)
    {
        $this->disableFlag = $disableFlag;
        return $this;
    }

    /**
     * 字段启用状态：null-所有，0-禁用，1-启用
     *
     * @param mixed $enableFlag
     * @return $this
     */
    public function setEnableFlag($enableFlag)
    {
        $this->enableFlag = $enableFlag;
        return $this;
    }

    /**
     * 过滤列表属性：null-不过滤，0-非列表字段，1-列表字段
     *
     * @param mixed $isList
     * @return $this
     */
    public function setIsList($isList)
    {
        $this->isList = $isList;
        return $this;
    }

    /**
     * 展示的字段列表
     *
     * @param $fields
     * @return $this
     */
    public function setFields($fields)
    {
    
        $this->fields = array_values(array_unique(array_merge($fields, ['disable_flag', 'type', 'field_type','relation_origin_type','relation_origin_field'])));
        
        return $this;
    }

    /**
     * 表单字段类型：0-特殊，1-短文本，2-长文本，3-下拉选项，4-日期，5-数字，6-图片
     *
     * @param mixed $fieldType
     * @return $this
     */
    public function setFieldType($fieldType)
    {
        $this->fieldType = $fieldType;
        return $this;
    }

    /**
     * 设置字段是否必填：null-所有，0-不能设置，1-可以设置
     *
     * @param mixed $editRequired
     * @return $this
     */
    public function setEditRequired($editRequired)
    {
        $this->editRequired = $editRequired;
        return $this;
    }

    /**
     * 编辑字段隐藏属性：null-所有，0-不能设置，1-可以设置
     *
     * @param mixed $editHide
     * @return $this
     */
    public function setEditHide($editHide)
    {
        $this->editHide = $editHide;
        return $this;
    }

    /**
     * 编辑字段提示信息：null-所有，0-不能设置，1-可以设置
     *
     * @param mixed $editHint
     * @return $this
     */
    public function setEditHint($editHint)
    {
        $this->editHint = $editHint;
        return $this;
    }

    /**
     * 只读字段，字段值由系统自动生成：null-所有，0-非只读，1-只读
     *
     * @param mixed $readonly
     * @return $this
     */
    public function setReadonly($readonly)
    {
        $this->readonly = $readonly;
        return $this;
    }

    /**
     * @param $type
     * @return $this
     * @throws \ProcessException
     */
    public function setFormatterType($type)
    {
        $this->formatter->setFormatType($type);
        return $this;
    }

    /**
     * @param $scene
     * @return $this
     */
    public function setFormatterScene($scene)
    {
        $this->scene = $scene;
        $this->formatter->setScene($scene);
        return $this;
    }

    /**
     * @param $showHeaderSort
     * @return $this
     */
    public function setFormatterHeaderSort($showHeaderSort)
    {
        $this->headerSort = $showHeaderSort;
        $this->formatter->setHeaderSort($showHeaderSort);
        return $this;
    }

    public function setFormatterExtraFieldHandler($extraFieldHandler){
        $this->formatter->setExtraFieldHandler($extraFieldHandler);
    }

    /**
     * @param array $showDisableFlagByFalseField
     */
    public function setShowDisableFlagByFalseField(array $showDisableFlagByFalseField): void
    {
        $this->showDisableFlagByFalseField = $showDisableFlagByFalseField;
    }

    /**
     * 设置排序字段
     *
     * @param $orderBy
     */
    public function setOrderBy($orderBy)
    {
        if ($orderBy == self::ORDER_PC || $orderBy == self::ORDER_APP) {
            $this->orderBy = [
                "`{$this->tableAlias[self::TBL_CUSTOM_FIELD]}`.`$orderBy`" => 'DESC',
                "`{$this->tableAlias[self::TBL_CUSTOM_FIELD]}`.`create_time`" => 'ASC',
            ];
        } else {
            parent::setOrderBy($orderBy);
        }
        return $this;
    }

    public function setBaseOrderBy($orderBy)
    {
        if ($orderBy == self::ORDER_PC || $orderBy == self::ORDER_APP) {
            $this->orderBy = [
                "`{$this->tableAlias[self::TBL_CUSTOM_FIELD]}`.`base`" => 'DESC',
                "`{$this->tableAlias[self::TBL_CUSTOM_FIELD]}`.`$orderBy`" => 'DESC',
                "`{$this->tableAlias[self::TBL_CUSTOM_FIELD]}`.`create_time`" => 'ASC',
            ];
        } else {
            parent::setOrderBy($orderBy);
        }
        return $this;
    }

    public function formatGroupId($flag)
    {
        $this->formatGroupId = $flag;
    }

    public function getTableAlias($table)
    {
        return $this->tableAlias[$table] ?? '';
    }

    /**
     * @return mixed
     */
    public function getType()
    {
        return $this->type;
    }

    public function buildParams()
    {
        $A = $this->tableAlias[self::TBL_CUSTOM_FIELD].'.';
        $B = $this->tableAlias[self::TBL_FIELD_GROUP].'.';


        $sql = "{$A}client_id=:client_id";
        $params = [':client_id' => $this->clientId];

        if ($this->type !== null)
        {
            SqlBuilder::buildIntWhere($A, 'type',$this->type, $sql, $params );
        }

        if ($this->base !== null)
        {
            SqlBuilder::buildIntWhere($A, 'base',$this->base, $sql, $params );
        }

        if ($this->fieldType != null) {
            SqlBuilder::buildIntWhere($A, 'field_type',$this->fieldType, $sql, $params );
        }

        if ($this->require !== null)
        {
            SqlBuilder::buildIntWhere($A, 'require',$this->require, $sql, $params );
        }

        if ($this->editRequired !== null)
        {
            SqlBuilder::buildIntWhere($A, 'edit_required',$this->editRequired, $sql, $params );
        }

        if ($this->relationFieldType){
            SqlBuilder::buildIntWhere($A, 'relation_field_type',$this->relationFieldType, $sql, $params );
        }

        if ($this->relationOriginField){
            SqlBuilder::buildIntWhere($A, 'relation_origin_field',$this->relationOriginField, $sql, $params );
        }

        if ($this->relationOriginType){
            SqlBuilder::buildIntWhere($A, 'relation_origin_type',$this->relationOriginType, $sql, $params );
        }

        if ($this->relationOriginFieldType){
            SqlBuilder::buildIntWhere($A, 'relation_origin_field_type',$this->relationOriginFieldType, $sql, $params );
        }


        if ($this->relationType){
            SqlBuilder::buildIntWhere($A, 'relation_type',$this->relationType, $sql, $params );
        }

        if ($this->relationField){
            SqlBuilder::buildIntWhere($A, 'relation_field',$this->relationField, $sql, $params );
        }

        if ($this->disableFlag !== null)
        {
            $showDisableFlagByFalseFields = array_map(function ($item) {
                return \Util::escapeDoubleQuoteSql($item);
            }, $this->showDisableFlagByFalseField);
            $showDisableFlagByFalseFieldSql = '';
            if ($showDisableFlagByFalseFields) {
                $showDisableFlagByFalseFieldSql = " or {$A}`id` in (\"".implode('", "',$showDisableFlagByFalseFields)."\")";
            }

            $sql .= " and ({$A}`disable_flag`=:disable_flag {$showDisableFlagByFalseFieldSql} )";
            $params[':disable_flag'] = $this->disableFlag;
        }

        if ($this->editHide !== null)
        {
            SqlBuilder::buildIntWhere($A, 'edit_hide',$this->editHide, $sql, $params );
        }

        if ($this->editDefault !== null)
        {
            SqlBuilder::buildIntWhere($A, 'edit_default',$this->editDefault, $sql, $params );
        }

        if ($this->editHint !== null)
        {
            SqlBuilder::buildIntWhere($A, 'edit_hint',$this->editHint, $sql, $params );
        }

        if ($this->isEditable !== null)
        {
            // 统计字段不允许编辑框，但又需要在新建编辑展示
            $calculateType = CustomFieldService::FIELD_TYPE_CALCULATE;
            $sql .= " and (({$A}`is_editable`=:is_editable and {$A}`base`=1) or base in (0,2))";
            $params[':is_editable'] = $this->isEditable;
        }

        if ( $this->isList !== null )
        {
            SqlBuilder::buildIntWhere($A, 'is_list',$this->isList, $sql, $params );
        }

        if ($this->enableFlag !== null)
        {
            SqlBuilder::buildIntWhere($A, 'enable_flag',$this->enableFlag, $sql, $params );
        }

        if ($this->readonly === 0)
        {
            SqlBuilder::buildIntWhere($A, 'readonly',$this->readonly, $sql, $params );
        }

        if ($this->groupId !== null && !empty($this->groupId)) {
            SqlBuilder::buildIntWhere($B, 'group_id',$this->groupId, $sql, $params );
        }

        if ($this->excludeGroupId !== null && !empty($this->excludeGroupId)) {
            SqlBuilder::buildNotIntWhere($B, 'group_id',$this->excludeGroupId, $sql, $params );
        }

        // 未指定group时，根据type获取全部group，先开放客户和线索
        if (empty($this->excludeGroupId) && empty($this->groupId) && in_array($this->type, [\Constants::TYPE_COMPANY, \Constants::TYPE_LEAD]) && !empty(CustomFieldService::GROUP_MAP[$this->type])) {
            $fieldGroupIds = array_keys(CustomFieldService::GROUP_MAP[$this->type]);
            SqlBuilder::buildIntWhere($B, 'group_id',$fieldGroupIds, $sql, $params );
        }

        if (!empty($this->excludeId)) {
            $excludeIds = array_map(function ($item) {
                return \Util::escapeDoubleQuoteSql($item);
            }, $this->excludeId);
            $sql .= " and {$A}id not in (\"".implode('", "',$excludeIds)."\")";
        }

        if(!empty($this->excludeFieldType)) {
            $excludeFieldTypes = array_map(function ($item) {
                return \Util::escapeDoubleQuoteSql($item);
            }, $this->excludeFieldType);
            $sql .= " and {$A}`field_type` not in (\"".implode('", "',$excludeFieldTypes)."\")";
        }

        if (!empty($this->id)) {
            if (is_array($this->id))
            {
                $id = array_map(function ($item) {
                    return \Util::escapeDoubleQuoteSql($item);
                }, $this->id);
                $idStr = '\'' . implode("','", $id) . '\'';

                $sql .= " and {$A}id in ($idStr)";

            }else{
                $sql .= " and {$A}id =:id";
                $params[':id'] = $this->id;
            }
        }


        if ($this->filterDefaultValue)
        {
            $sql .= " and {$A}`default` != ''";
        }

        if (!empty($this->rawWhere)){
            $sql .= ' and '.implode(' and ', $this->rawWhere);
        }

        return [$sql, $params];
    }

    public function rawData(){
        list($where, $params) = $this->buildParams();

        $db = \ProjectActiveRecord::getDbByClientId($this->clientId);

        $A = $this->tableAlias[self::TBL_CUSTOM_FIELD];
        $B = $this->tableAlias[self::TBL_FIELD_GROUP];

        $field = $this->getFields();

        $order = $this->buildOrderBy();

        $sql = "SELECT {$field} FROM `tbl_custom_field` AS `{$A}` INNER JOIN `tbl_field_group` AS `{$B}` " .
            "ON `{$A}`.`client_id`=`{$B}`.`client_id` " .
            "AND `{$A}`.`type`=`{$B}`.`type` " .
            "AND `{$A}`.`id`=`{$B}`.`id` " .
            "WHERE " . $where . $order;

        return $db->createCommand($sql)->queryAll(true,$params);
    }

    /**
     * @return mixed
     */
    public function find()
    {
        $listData = $this->rawData();

//        echo  $sql;
//        print_r($params);
//        print_r($listData);exit();

        // 检查引用字段的源目标字段情况
        if ($this->relationFlag) {
            $listData = $this->checkRelationField($listData);
        }

        if (!empty($this->extraMap)) {
            foreach ($this->extraMap as $extraInfo) {
                if (in_array($extraInfo['id'], $this->excludeId) && $this->excludeFlag) {
                    continue;
                }
                $extraInfo['name'] = $extraInfo['name'] ?? \Yii::t('field', "extra.{$this->type}." . $extraInfo['id']);
                $listData[] = $extraInfo;
            }
        }
    
        if ($this->ignoreFormat) {
    
            return $listData;
        }
//        $listData = $this->filterByLimitFieldSetting($listData);
        $listData = $this->formatFieldSetting($listData);
        $listData = $this->filterByRelationDisableFlagFieldSetting($listData);
        $listData = $this->formatByPrivilege($listData);
        $this->formatter->setListData($listData);
        return $this->formatter->result();
    }

    public function formatFieldSetting($listData){
        //特殊处理
        if ($this->type == \Constants::TYPE_SHIPPING_INVOICE) {
            foreach ($listData as &$item) {
                if ($item['id'] == 'cost_remark') {
                    $item['name'] = \Yii::t('field', "备注");
                }
                if ($item['id'] == 'cost_order_no') {
                    $item['name'] = \Yii::t('field',"销售订单编号");
                }
            }
            unset($item);
        }


        if (in_array($this->type, [\Constants::TYPE_QUOTATION, \Constants::TYPE_ORDER])) {
            $funDisableMap = PrivilegeConstants::PRIVILEGE_FUNCTIONAL_BE_IMPACTED_DISABLE_MAP[$this->type] ?? [];
            $funDisableFieldsMap = [];
            foreach ($funDisableMap as $functional => $funDisableField) {
                if (!\common\library\privilege_v3\Helper::hasFunctional($this->clientId, $functional)) {
                    $funDisableFieldsMap[$this->type] = $funDisableField;
                }
            }
            foreach ($listData as $k => $datum) {
                if (empty($datum['type'] )) {
                    continue;
                }

                if (in_array($datum['id'], $funDisableFieldsMap[$this->type] ?? [])) {
                    unset($listData[$k]);
                }
            }
        }

        //openapi不支持核销添加回款单自定义字段
        if ($this->type == \Constants::TYPE_CASH_COLLECTION_INVOICE && !in_array($this->scene, ['openapi', 'module_filter','approval_flow'])) {
            $step = 0;
            $fieldList = new FieldList($this->clientId);
//            $fieldList->setBase(0);
            $fieldList->setDisableFlag(0);
            $fieldList->setType(\Constants::TYPE_CASH_COLLECTION);
            $list = $fieldList->find();
            foreach ($listData as $item) {
                if ($item['id'] == 'cash_collection_comment') {
                    break;
                }
                $step++;
            }
            $newList = array();
            foreach ($list as $cashItem) {
                if($cashItem['base'] == 1 && $cashItem['id'] != 'payee'){
                    continue;
                }
                $cashItem['group_id'] = CustomFieldService::CASH_COLLECTION_INVOICE_GROUP_RECORD;
                $cashItem['type'] = \Constants::TYPE_CASH_COLLECTION_INVOICE;
                $cashItem['is_list'] = 1;
                $newList[] = $cashItem;
            }
            array_splice($listData, $step + 1, 0, $newList);
        }
        //库存预警表头增加自定义字段
        if ($this->type == \Constants::TYPE_PRODUCT_INVENTORY_WARNING && !in_array($this->scene, ['openapi', 'module_filter','info'])) {
            $fieldList = new FieldList($this->clientId);
            $fieldList->setBase(0);
            $fieldList->setType(\Constants::TYPE_PRODUCT);
            $list = $fieldList->find();
            foreach ($list as &$warningItem) {
                $warningItem['group_id'] = CustomFieldService::PRODUCT_INVENTORY_WARNING_BASIC;
                $warningItem['type'] = \Constants::TYPE_PRODUCT_INVENTORY_WARNING;
                $warningItem['is_list'] = 0;
            }
            unset($cashItem);
            array_splice($listData, 44, 0, $list);
        }


        if ($this->type == \Constants::TYPE_PURCHASE_ORDER && $this->scene == APIConstant::SCENE_PURCHASE_PRODUCT_LIST) {

            $listData = array_filter($listData, function($item) {
                $excludeIds = [
                    'refer_order_id',
                    'product_total_count',
                    'enable_count',
                    'parts_total_count',
                    'product_total_count_no_parts',
                ];
                return !in_array($item['id'] ?? '', $excludeIds);
            });

        }


        return $listData;
    }
    public function filterByLimitFieldSetting($listData)
    {
        $limitFieldSetting = \CustomerOptionService::getLimitFieldByLimitSetting($this->clientId, \Constants::TYPE_COMPANY);
        $limitFields = $limitFieldSetting['field'] ?? [];
        if (empty($listData)) {
            return $listData;
        }
        foreach ($listData as $index => $item)
        {
            if (!isset($item['id'])) {
                continue;
            }
            if (in_array($item['id'], $limitFields))
            {
                unset($listData[$index]);
            }
        }
        return $listData;
    }

    //根据关联字段，关闭对应的字段信息
    public function filterByRelationDisableFlagFieldSetting($listData)
    {
        $filterFieldSetting = CustomFieldService::RELATION_DISPLAY_FLAG_FALSE_FIELD_MAP;
        if (empty($listData)) {
            return $listData;
        }

        $disableFlagField = [];
        foreach ($listData as $item) {
            if (array_key_exists($item['id'], $filterFieldSetting[$item['type']??0] ?? [])
                && ($item['disable_flag']??0 == \Constants::DELETE_FLAG_TRUE)
            ) {
                $disableFlagField = array_merge([$item['id']], $disableFlagField, $filterFieldSetting[$item['type']][$item['id']]);
            }
        }
        if ($disableFlagField) {
            foreach ($listData as &$fieldItem){
                if (in_array($fieldItem['id'], $disableFlagField))
                {
                    $fieldItem['disable_flag'] = CustomFieldService::FIELD_DISABLE;
                }
            }
        }

        return $listData;
    }

    public function addExtra($extraMap)
    {
        $this->extraMap = $extraMap;
    }

    /**
     * @return mixed
     */
    public function findByIds()
    {

        //需要传递id和类型
        if(!$this->id || !$this->type){
            return false;
        }
        $conditions = "client_id=:client_id";
        $params = [':client_id' => $this->clientId];

        if (is_array($this->id)) {
            $id = array_map(function ($item) {
                return \Util::escapeDoubleQuoteSql($item);
            }, $this->id);
            $idStr = '\'' . implode("','", $id) . '\'';

            $conditions .= ' and id in (' . $idStr . ')';
        }else{
            $conditions .= " and id =:id";
            $params[':id'] = $this->id;
        }

        if (is_array($this->type)) {
            $conditions .= " and `type` in ( " . implode(',', $this->type) . ")";
        } else {
            $conditions .= " and `type`=:type";
            $params[':type'] = $this->type;
        }

        $db = \ProjectActiveRecord::getDbByClientId($this->clientId);

        $sql = "SELECT * FROM `tbl_custom_field`  WHERE $conditions";

        $listData = $db->createCommand($sql)->queryAll(true,$params);

        $listData = $this->formatByPrivilege($listData);

        return $listData;
    }

    /**
     * @return mixed
     */
    public function count()
    {
        list($where, $params) = $this->buildParams();

        $db = \ProjectActiveRecord::getDbByClientId($this->clientId);

        return $db->createCommand("select count(1) from tbl_custom_field where $where")->queryScalar($params);
    }

    protected function buildOrderBy()
    {
        if (empty($this->orderBy)) {
            return '';
        }

        $order_fields = [];
        foreach ($this->orderBy as $field => $order) {
            $order_fields[] = "{$field} {$order}";
        }
        return ' ORDER BY ' . implode(',', $order_fields);
    }

    /**
     * 筛选是否可编辑：null-不筛选， 0-筛选不可编辑， 1-筛选可编辑
     *
     * @param mixed $isEditable
     */
    public function setIsEditable($isEditable)
    {
        $this->isEditable = $isEditable;
    }

    /**
     * @param bool $needList
     * @return $this
     */
    public function setNeedList(bool $needList)
    {
        $this->formatter->setNeedList($needList);
        return $this;
    }

    private function getFields()
    {
        if (empty($this->fields)) {
            return '*';
        }
        $result = [];
        foreach ($this->fields as $field) {
            foreach ($this->fieldMap as $table=> $fields) {
                if (in_array($field, $fields)) {
                    $result[]= "`{$this->tableAlias[$table]}`.`$field`";
                    break;
                }
            }
        }
        if (empty($result)) {
            return '*';
        }
        return implode(',', $result);
    }

    /**
     * 检查引用字段的源目标字段情况
     *
     * @param $data
     */
    private function checkRelationField($data)
    {
        foreach ($data as $key => $val) {
            // 默认源目标字段不存在
            $data[$key]['relation'] = CustomFieldService::FIELD_RELATION_DELETE;

            if (
                $val['field_type'] == CustomFieldService::FIELD_TYPE_FIELDS ||
                $val['field_type'] == CustomFieldService::FIELD_TYPE_QUOTE_FIELDS
            ) {

                // 产品图片字段特殊处理
                if (preg_match('/^images_[0-9]$/', $val['relation_field'])) {
                    $data[$key]['relation'] = CustomFieldService::FIELD_RELATION_ENABLE;
                    $data[$key]['relation_field_name'] = \yii::t('field','产品图片');
                    continue;
                }

                $service = new CustomFieldService($this->clientId, $val['relation_type']);
                $result = $service->fieldsByIds([$val['relation_field']]);

                if (count($result)) {
                    // 源目标字段可正常使用
                    $data[$key]['relation'] = CustomFieldService::FIELD_RELATION_ENABLE;
                    $data[$key]['relation_field_name'] = \yii::t('field',current($result)['name']);

                    if (current($result)['disable_flag'] == 1) {
                        // 源目标字段已被设置为隐藏
                        $data[$key]['relation'] = CustomFieldService::FIELD_RELATION_DISABLE;
                    }
                }
            }
        }

        return $data;
    }

    public function formatByPrivilege($listData)
    {
        if($this->ignorePrivilege){
            return $listData;
        }
        $noEditableMap = [
            \Constants::TYPE_PURCHASE_ORDER => [
                'supplier_product_no', 'supplier_product_name'
            ],
            \Constants::TYPE_ORDER => [
                'gross_profit_margin'
            ],
            \Constants::TYPE_COMPANY => [
                 'next_follow_up_time'
            ],
        ];

        if ($this->userId && array_key_exists($this->functionalId, PrivilegeConstants::FIELD_PRIVILEGE_FUNCTIONAL_MAP)) {
            $privilegeService = PrivilegeService::getInstance($this->clientId, $this->userId);

            if(!array_intersect((array)$this->type,  \common\library\privilege_v3\object_service\ObjPrivilegeService::NEW_FIELD_PRIVILEGE_REFER_TYPE)){    // 未接入paas权限的模块临时兼容
                $fieldScopeMap = $privilegeService->getFieldScopeMap($this->functionalId);
            }else{
                // 由于 tbl_privilege_field.scope 不再采用和维护，目前维护的是 tbl_privilege_field.is_view/is_create/is_update/is_export，因此获取字段权限需要调用paas组的字段权限方法
                $objectPrivilegeService = new UserObjectPrivilegeService($this->clientId);
                $objectPrivilegeService->setUserId($this->userId);
                $objectPrivilegeService->setFunctionalId($this->functionalId);
                $objectPrivilegeService->setFieldPrivilegeScene($this->scene);
                $fieldScopeMap = $objectPrivilegeService->getFieldScope();
            }

            $hasModuleCreatePrivilegeFlag = [];
            //$this->type  需要注意：可能是数组类型 获取交集，然后分别去获取对应的模块是是否有创建权限 数据类型[28=>1];
            $types = is_array($this->type) ? $this->type : [$this->type];
            $hasModuleCreatePrivilegeType = array_intersect($types, array_keys($this->type2CreatePrivilegeMap));
            if (!empty($hasModuleCreatePrivilegeType)) {
                foreach ($hasModuleCreatePrivilegeType as $type) {
                    if ($privilegeService->hasPrivilege($this->type2CreatePrivilegeMap[$type]??'',false)) {
                        $hasModuleCreatePrivilegeFlag[$type] = 1;
                    }
                }
            }

            $disableFieldMap = [];
            foreach ($listData as $k => &$datum) {
    
                if (empty($datum['type'])) {
                    continue;
                }

                $fieldId = $datum['id'];
                //供应商产品起订量合并为gradient_price字段权限
                /*
                 * 接入权限paas之后，字段不再特殊化
                if ($datum['type'] == \Constants::TYPE_SUPPLIER_PRODUCT && in_array($fieldId, ['quantity', 'price'])) {
                    $fieldId = 'gradient_price';
                }
                */
                if ($scope = $fieldScopeMap[$datum['type'] . '-' . $fieldId] ?? false) {
                    //有相关创建权限的，将必填字段设置为读写
                    if(!in_array($this->type,  \common\library\privilege_v3\object_service\ObjPrivilegeService::NEW_FIELD_PRIVILEGE_REFER_TYPE)){
                        if (($hasModuleCreatePrivilegeFlag[$datum['type']] ?? 0) && $datum['require'] == 1) {
                            $scope = PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_READ_WRITE;
                        }
                    }

                    switch ($scope) {
                        case PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_DISABLE:
                            if ($this->hideValueForDisable) {
                                unset($listData[$k]);
                            } else {
                                $datum['disable_flag'] = CustomFieldService::FIELD_DISABLE;
                                $datum['is_editable'] = 0;
                            }
                            $disableFieldMap[$datum['type']][]=$datum['id'];
                            break;
                        case PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_READONLY:
                            $datum['is_editable'] = 0;
                            break;
                        case PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_READ_WRITE:
                            if(!isset($datum['field_type']) || $datum['field_type'] != CustomFieldService::FIELD_TYPE_QUOTE_FIELDS){
                                $datum['disable_flag'] = CustomFieldService::FIELD_ENABLE;
                                $datum['is_editable'] = 1;
                            }
                            break;
                        default:
                            break;
                    }
                }
                $noEditableField = $noEditableMap[$datum['type']] ?? [];
                if(in_array($fieldId,$noEditableField)) {
                    $datum['is_editable'] = 0;
                }
            }

            unset($datum);
            $changeBeImpactedFieldsMap = [];
            foreach ($types as $type) {
                $beImpactedFields = PrivilegeConstants::PRIVILEGE_FIELD_BE_IMPACTED_DISABLE_MAP[$type] ?? [];
                foreach ($beImpactedFields as $disableField => $beImpactedField) {
                    $canDisableFlag = empty(array_diff($beImpactedField, $disableFieldMap[$type] ?? []));
                    if ($canDisableFlag) {
                        $changeBeImpactedFieldsMap[$type][] = $disableField;
                    }
                }
            }

            foreach ($listData as $k => &$datum) {
                if (empty($datum['type'] )) {
                    continue;
                }
                $changeBeImpactedFields = $changeBeImpactedFieldsMap[$datum['type']] ?? [];
                if (in_array($datum['id'], $changeBeImpactedFields)) {
                    if ($this->hideValueForDisable) {
                        unset($listData[$k]);
                    } else {
                        $datum['disable_flag'] = CustomFieldService::FIELD_DISABLE;
                    }
                }
            }
            unset($datum);

            //字段隐藏设置：省份、城市同国家字段一致
            if(in_array($this->functionalId, [
                PrivilegeConstants::FUNCTIONAL_COMPANY_POOL,
                PrivilegeConstants::FUNCTIONAL_CUSTOMER,
                PrivilegeConstants::FUNCTIONAL_LEAD_POOL,
                PrivilegeConstants::FUNCTIONAL_LEAD,
            ])
            ) {
                $map = [];
                foreach ($listData as $index => $item) {
                    if (empty($item['type'] )) {
                        continue;
                    }
                    $item['list_index'] = $index;
                    $map[$item['type']][$item['id']] = $item;
                }
                foreach ($map as $type => $typeFieldList) {
                    $country =  isset($typeFieldList['country']) ? $typeFieldList['country'] : null;
                    if ($country) {
                        $countryListDataIndex = $typeFieldList['country']['list_index'];
                        if (isset($typeFieldList['province']))
                        {
                            $provinceListDataIndex = $typeFieldList['province']['list_index'];
                            $listData[$provinceListDataIndex]['disable_flag'] = $listData[$countryListDataIndex]['disable_flag'];
                            $listData[$provinceListDataIndex]['is_editable'] = $listData[$countryListDataIndex]['is_editable'];
                        }
                        if (isset($map['city']))
                        {
                            $cityListDataIndex = $typeFieldList['city']['list_index'];
                            $listData[$cityListDataIndex]['disable_flag'] = $listData[$countryListDataIndex]['disable_flag'];
                            $listData[$cityListDataIndex]['is_editable'] = $listData[$countryListDataIndex]['is_editable'];
                        }
                    }
                }
            }
        }

        return $listData;
    }

    public function paramsMapping($setting)
    {
        isset($setting['type']) && $this->setType($setting['type']);
        isset($setting['group_id']) && $this->setGroupId($setting['group_id']);
        isset($setting['disable_flag']) && $this->setDisableFlag((int)$setting['disable_flag']);
        isset($setting['is_editable']) && $this->setIsEditable((int)$setting['is_editable']);
        isset($setting['need_list']) && $this->setNeedList((int)$setting['need_list']);
        isset($setting['field_type']) && $this->setFieldType((int)$setting['field_type']);
        isset($setting['base']) && $setting['base'] != -1 && $this->setBase($setting['base']);
        isset($setting['is_list']) && !$setting['is_list'] && $this->setIsList(0);
        isset($setting['hide_readonly']) && $setting['hide_readonly'] && $this->setReadonly(0);

        !empty($setting['privilege_info']) && $this->setPrivilegeInfo($setting['privilege_info']);
    }

    public function setAddCustomFeeFieldDisable(bool $addCustomFeeFieldDisable = false)
    {
        $this->formatter->setAddCustomFeeFieldDisable($addCustomFeeFieldDisable);
    }
    
    /**
     * @param bool $ignoreFormat
     */
    public function setIgnoreFormat(bool $ignoreFormat): void {
        
        $this->ignoreFormat = $ignoreFormat;
    }
}
