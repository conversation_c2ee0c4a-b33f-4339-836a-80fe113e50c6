<?php
/**
 * Created by <PERSON>p<PERSON><PERSON><PERSON>.
 * User: czzhengkw
 * Date: 2017/3/30
 * Time: 下午4:18
 */

namespace common\library\custom_field;


use common\library\account\Client;
use common\library\cache\ClassCacheRepository;
use common\library\cash_collection\CashCollection;
use common\library\CommandRunner;
use common\library\custom_field\quote_field\QuoteFieldConstant;
use common\library\customer\field_unique\DuplicateFlagBuilder;
use common\library\duplicate\CustomPoolDuplicateRule;
use common\library\duplicate\DuplicateConstants;
use common\library\invoice\InvoiceValidator;
use common\library\layout\init\Factory;
use common\library\object\field\Field;
use common\library\object\field\field_setting\FieldSettingFactory;
use common\library\object\field\FieldConstant;
use common\library\object\field\Helper as FieldHelper;
use common\library\object\object_define\Constant as ObjConstant;
use common\library\oms\common\OmsConstant;
use common\library\oms\task\formatter\CompanyFieldTask;
use common\library\oms\task\formatter\CustomerFieldTask;
use common\library\oms\task\formatter\ForwardFieldTask;
use common\library\oms\task\formatter\OpportunityFieldTask;
use common\library\oms\task\formatter\OrderFieldTask;
use common\library\oms\task\formatter\ProductFieldTask;
use common\library\opportunity\Opportunity;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeField;
use common\library\privilege_v3\PrivilegeFieldV2;
use common\library\privilege_v3\PrivilegeService;
use common\library\privilege_v3\UserRolePrivilegeService;
use common\library\push\Browser;
use common\library\setting\item\ItemSettingConstant;
use common\library\setting\library\origin\Origin;
use common\library\workflow\WorkflowConstant;
use Constants;
use http\Exception\RuntimeException;

class CustomFieldService
{
    const FIELD_TYPE_OTHER = 0;             //特殊
    const FIELD_TYPE_TEXT = 1;              //单行文本
    const FIELD_TYPE_TEXTAREA = 2;          //多行文本
    const FIELD_TYPE_SELECT = 3;            //单选
    const FIELD_TYPE_DATE = 4;              //日期
    const FIELD_TYPE_NUMBER = 5;            //数值
    const FIELD_TYPE_IMAGE = 6;             //图片
    const FIELD_TYPE_MULTIPLE_SELECT = 7;   //多选
    const FIELD_TYPE_ATTACH = 8;            //附件
    const FIELD_TYPE_FIELDS = 9;            //关联字段
    const FIELD_TYPE_DATETIME = 10;         //日期 - 时间
    const FIELD_TYPE_FORMULA = 11;          //公式
    const FIELD_TYPE_CALCULATE = 12;        //汇总
    const FIELD_TYPE_BOOLEAN = 13;        //布尔
    const FIELD_TYPE_RECEIVER = 14;        //收件人
    const FIELD_TYPE_SENDER = 15;        //发件人
    const FIELD_TYPE_RECEIVER_DOMAIN = 16;  //收件人域
    const FIELD_TYPE_STATUS = 17;  //状态
    const FIELD_TYPE_STAGE = 18;  //阶段
    const FIELD_TYPE_DATE_RANGE = 21;        //日期范围
    const FIELD_TYPE_RICH_TEXT = 22;        //富文本
    const FIELD_TYPE_QUOTE_FIELDS = 23;        //引用字段

    const GROUP_SYSTEM_INFO = 0; // 系统信息
    //产品分组
    const PRODUCT_GROUP_BASIC = 1;          //基本信息
    const PRODUCT_GROUP_PRICE = 2;          //价格信息
    const PRODUCT_GROUP_PACKAGE = 3;        //包装信息
    const PRODUCT_GROUP_DESCRIBE = 4;       //产品描述
    const PRODUCT_GROUP_SKU = 5;            //产品规格
    const PRODUCT_GROUP_CREATE_TYPE = 6;    //线上有这样的数据 创建方式 常量值防止被占用
    const PRODUCT_GROUP_SIZE = 7;           //尺寸信息
    const PRODUCT_GROUP_CUSTOM = 8;           //报关信息
    const PRODUCT_GROUP_CARTON = 9;           //外箱信息
    const PRODUCT_GROUP_CHARACTER = 10;       //产品特性
    const PRODUCT_GROUP_PART      = 11;       //配件清单
    const PRODUCT_GROUP_INVENTORY = 12;       //库存tab

    //产品库存预警
    const PRODUCT_INVENTORY_WARNING_BASIC = 1; //预警信息
    const PRODUCT_INVENTORY_WARNING_PRODUCT = 2;//产品信息

    //marketing 产品分组
    const CMS_PRODUCT_GROUP_BASIC = 1;          //基本信息
    const CMS_PRODUCT_GROUP_DESCRIBE = 2;       //价格信息
    const CMS_PRODUCT_GROUP_PRICE = 3;          //交易信息
    const CMS_PRODUCT_GROUP_DETAIL = 4;         //详细描述

    //订单分组
    const ORDER_GROUP_BASIC = 1;            //基本信息
    const ORDER_GROUP_PRODUCT = 2;          //交易产品
    const ORDER_GROUP_FEE = 3;              //附加费用信息
    const ORDER_GROUP_BUYER = 4;            //买方信息
    const ORDER_GROUP_TRANSPORT = 5;        //货运信息
    const ORDER_GROUP_BANK = 6;             //银行信息
    const ORDER_GROUP_SYSTEM = 7;           //系统信息

    //信保订单产品分组
    const ORDER_GROUP_ALIBABA_PRODUCT_BASIC = 1;           //阿里产品信息


    //报价单分组
    const QUOTATION_GROUP_BASIC = 1;        //基本信息
    const QUOTATION_GROUP_PRODUCT = 2;      //交易产品
    const QUOTATION_GROUP_FEE = 3;          //费用信息
    const QUOTATION_GROUP_BUYER = 4;        //买方信息
    const QUOTATION_GROUP_TRANSPORT = 5;    //货运信息
    const QUOTATION_GROUP_SYSTEM = 6;       //系统信息

    //公司分组
    const COMPANY_GROUP_ALL = 0; // 客户所有分组
    const COMPANY_GROUP_BASIC = 1;
    const COMPANY_GROUP_CHARACTERISTIC = 2;
    const COMPANY_GROUP_MANAGE = 3;
    const COMPANY_GROUP_CONTACT = 4;
    const COMPANY_GROUP_OTHER = 5;

    //联系人分组
    const CUSTOMER_GROUP_BASIC = 1;

    //线索分组
    const LEAD_GROUP_ALL = 0; // 线索所有分组
    const LEAD_GROUP_BASIC = 1;
    const LEAD_GROUP_MANAGE = 2;
    const LEAD_GROUP_OTHER = 3;

    //商机分组
    const OPPORTUNITY_GROUP_BASIC = 1;
    const OPPORTUNITY_GROUP_PRODUCT = 2;

    //回款单分组
    const CASH_COLLECTION_GROUP_BASIC = 1;

    //回款登记分组
    const CASH_COLLECTION_INVOICE_GROUP_BASIC = 1; //基本信息
    const CASH_COLLECTION_INVOICE_GROUP_SYSTEM = 2;//系统信息
    const CASH_COLLECTION_INVOICE_GROUP_RECORD = 3; //回款分配明细

    //采购单分组
    const PURCHASE_ORDER_GROUP_BASIC = 1;
    const PURCHASE_ORDER_GROUP_PRODUCT = 2;
    const PURCHASE_ORDER_GROUP_CHARGE = 3;
    const PURCHASE_ORDER_GROUP_SYSTEM = 4;

    //供应商分组
    const SUPPLIER_GROUP_BASIC = 1; //基础信息
    const SUPPLIER_GROUP_ATTACH = 2; //附件信息
    const SUPPLIER_GROUP_CONTACT = 3;//联系人
    const SUPPLIER_GROUP_SYSTEM = 4;//系统信息
    const SUPPLIER_GROUP_PAYMENT = 5;//资金信息

    //供应商产品分组
    const SUPPLIER_PRODUCT_GROUP_SUPPLIER_BASIC = 1; //供应商信息
    const SUPPLIER_PRODUCT_GROUP_QUOTE = 2; //报价信息
    const SUPPLIER_PRODUCT_GROUP_PRODUCT = 3; //供应商产品信息
    const SUPPLIER_PRODUCT_GROUP_SYSTEM = 4; //供应商系统信息


    //订单毛利
    const ORDER_PROFIT_GROUP_ORDER = 1; //订单毛利信息

    const ORDER_PROFIT_GROUP_ORDER_FUND = 2; //订单附加费用项
    const ORDER_PROFIT_GROUP_ORDER_COST_INVOICE_FUND = 3; //成本费用项
    const ORDER_PROFIT_GROUP_ORDER_FORMULA = 4; // 公式字段

    //货代分组
    const FORWARDER_GROUP_BASIC = 1; //基础信息
    const FORWARDER_GROUP_ATTACH = 2; //附件信息
    const FORWARDER_GROUP_CONTACT = 3;//联系人
    const FORWARDER_GROUP_SYSTEM = 4;//系统信息
    const FORWARDER_GROUP_PAYMENT = 5;//资金信息


    //----------------erp 单据分组定义 start----------------

    const ERP_INVOICE_GROUP_SYSTEM = 2;

    //采购入库单
    const PURCHASE_INBOUND_GROUP_BASIC = 1; //基本信息
    const PURCHASE_INBOUND_GROUP_SYSTEM = self::ERP_INVOICE_GROUP_SYSTEM;//系统信息
    const PURCHASE_INBOUND_GROUP_PRODUCT = 3; //入库明细

    //其他入库单
    const OTHER_INBOUND_GROUP_BASIC = 1; //基本信息
    const OTHER_INBOUND_GROUP_SYSTEM = self::ERP_INVOICE_GROUP_SYSTEM;//系统信息
    const OTHER_INBOUND_GROUP_PRODUCT = 3; //入库明细

    //销售出库单
    const SALE_OUTBOUND_GROUP_BASIC = 1; //基本信息
    const SALE_OUTBOUND_GROUP_SYSTEM = self::ERP_INVOICE_GROUP_SYSTEM;//系统信息
    const SALE_OUTBOUND_GROUP_PRODUCT = 3; //出库明细

    //其他出库单
    const OTHER_OUTBOUND_GROUP_BASIC = 1; //基本信息
    const OTHER_OUTBOUND_GROUP_SYSTEM = self::ERP_INVOICE_GROUP_SYSTEM;//系统信息
    const OTHER_OUTBOUND_GROUP_PRODUCT = 3; //入库明细

    //采购退货单
    const PURCHASE_RETURN_GROUP_BASIC = 1; //基本信息
    const PURCHASE_RETURN_GROUP_SYSTEM = self::ERP_INVOICE_GROUP_SYSTEM;//系统信息
    const PURCHASE_RETURN_GROUP_PRODUCT = 3; //入库明细

    //采购任务
    const PRODUCT_TRANSFER_PURCHASE_GROUP_BASIC = 1; //基本信息
    const PRODUCT_TRANSFER_PURCHASE_GROUP_SYSTEM = self::ERP_INVOICE_GROUP_SYSTEM;//系统信息
    const PRODUCT_TRANSFER_PURCHASE_GROUP_PRODUCT = 3; //采购任务明细

    //入库任务
    const PRODUCT_TRANSFER_INBOUND_GROUP_BASIC = 1; //基本信息
    const PRODUCT_TRANSFER_INBOUND_GROUP_SYSTEM = self::ERP_INVOICE_GROUP_SYSTEM;//系统信息
    const PRODUCT_TRANSFER_INBOUND_GROUP_PRODUCT = 3; //入库任务明细

    //出库任务
    const PRODUCT_TRANSFER_OUTBOUND_GROUP_BASIC = 1; //基本信息
    const PRODUCT_TRANSFER_OUTBOUND_GROUP_SYSTEM = self::ERP_INVOICE_GROUP_SYSTEM;//系统信息
    const PRODUCT_TRANSFER_OUTBOUND_GROUP_PRODUCT = 3; //出库任务明细

    //单据任务
    const PRODUCT_TRANSFER_OTHER_GROUP_BASIC = 1; //基本信息
    const PRODUCT_TRANSFER_OTHER_GROUP_SYSTEM = self::ERP_INVOICE_GROUP_SYSTEM;//系统信息

    //付款单
    const PAYMENT_INVOICE_GROUP_BASIC = 1; //基本信息
    const PAYMENT_INVOICE_GROUP_SYSTEM = self::ERP_INVOICE_GROUP_SYSTEM;//系统信息
    const PAYMENT_INVOICE_GROUP_PRODUCT = 3; //付款明细
    const PAYMENT_INVOICE_GROUP_PAYMENT = 5;//资金信息

    //费用单
    const COST_INVOICE_GROUP_BASIC = 1; //基本信息
    const COST_INVOICE_GROUP_SYSTEM = self::ERP_INVOICE_GROUP_SYSTEM;//系统信息
    const COST_INVOICE_GROUP_RECORD = 3; // 费用明细
    const COST_INVOICE_GROUP_PAYMENT = 5;//资金信息


    //出运单

    const SHIPPING_INVOICE_GROUP_BASIC = 1; //基本信息
    const SHIPPING_INVOICE_GROUP_SHIPPING_TRANSPORT = 2; //运输信息
    const SHIPPING_INVOICE_GROUP_TIME_INFO = 3; //时间相关信息
    const SHIPPING_INVOICE_GROUP_PRODUCT_BASE = 4; //出运明细信息
    const SHIPPING_INVOICE_GROUP_PRODUCT_ADDITION_FEE = 5; //附加费用
    const SHIPPING_INVOICE_GROUP_SYSTEM  = 6; //系统信息

    // 工作报告
    const WORK_JOURNAL_GROUP_BASIC = 1; //基本信息

    //询价协同
    const TYPE_INQUIRY_COLLABORATION_BASIC = 1; //基本信息
    const TYPE_INQUIRY_COLLABORATION_PRODUCT = 2; //产品信息
    const TYPE_INQUIRY_COLLABORATION_SYS = 3; //系统信息
    const TYPE_INQUIRY_COLLABORATION_SUMMATION = 4; //合计信息




    //询价反馈
    const TYPE_INQUIRY_FEEDBACK_BASIC = 1; //基本信息


    //------------------------end----------------------


    //附加费用类型
    const ADDITION_FEE_TYPE_PLUS_VALUE = 0;             //+绝对值
    const ADDITION_FEE_TYPE_SUBTRACT_VALUE = 2;         //-绝对值
    const ADDITION_FEE_TYPE_PLUS_PERCENTAGE = 1;        // +百分比
    const ADDITION_FEE_TYPE_SUBTRACT_PERCENTAGE = 3;    //减百分比

    //引用字段的源目标字段情况
    const FIELD_RELATION_DELETE = 0;        //引用源字段已删除
    const FIELD_RELATION_ENABLE = 1;        //引用源字段可用
    const FIELD_RELATION_DISABLE = 2;       //引用源字段已隐藏

    const FIELD_BASE_OF_SYSTEM = 1;
    const FIELD_BASE_OF_CUSTOM_EXTERNAL = 0;
    const FIELD_BASE_OF_CUSTOM_ATTRIBUTE = 2;

    const FIELD_CUSTOM_KEY_MAP = [
        self::FIELD_BASE_OF_CUSTOM_EXTERNAL => 'external_field_data',
        self::FIELD_BASE_OF_CUSTOM_ATTRIBUTE => 'info_json',
    ];

    const RELATION_IGNORE_GROUP_MAP= [
        Constants::TYPE_PURCHASE_ORDER => [
            Constants::TYPE_ORDER => [
//                self::ORDER_GROUP_BASIC,
                self::ORDER_GROUP_FEE,
            ],
        ],
    ];

    const GROUP_MAP = [
        Constants::TYPE_PRODUCT => [
            self::PRODUCT_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::PRODUCT_GROUP_CHARACTER => [
                'name' => '产品特性',
                'can_add_custom_field' => false,
                'is_hide' => false,
            ],
            self::PRODUCT_GROUP_PRICE => [
                'name' => '价格信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::PRODUCT_GROUP_SIZE => [
                'name' => '尺寸信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::PRODUCT_GROUP_PACKAGE => [
                'name' => '包装信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::PRODUCT_GROUP_CARTON=>[
                'name'=>'外箱信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::PRODUCT_GROUP_CUSTOM => [
                'name' => '报关信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::PRODUCT_GROUP_PART => [
                'name' => '配件清单',
                'can_add_custom_field' => false,
                'is_hide' => false,
            ],
             self::PRODUCT_GROUP_DESCRIBE => [
                'name' => '更多信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::PRODUCT_GROUP_SKU => [
                'name' => '产品规格',
                'can_add_custom_field' => false,
                'is_hide' => true,
            ],
            self::PRODUCT_GROUP_INVENTORY => [
                'name' => '库存信息',
                'can_add_custom_field' => false,
                'is_hide' => true,
            ],
            self::GROUP_SYSTEM_INFO => [
                'name' => '系统信息',
                'can_add_custom_field' => false,
                'is_hide' => true,
            ],
        ],
        Constants::TYPE_CMS_PRODUCT => [
            self::CMS_PRODUCT_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::CMS_PRODUCT_GROUP_DESCRIBE => [
                'name' => '产品橱窗',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::CMS_PRODUCT_GROUP_PRICE => [
                'name' => '交易信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::CMS_PRODUCT_GROUP_DETAIL => [
                'name' => '详细描述',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
        ],
        Constants::TYPE_PRODUCT_INVENTORY_WARNING => [
            self::PRODUCT_INVENTORY_WARNING_BASIC => [
                'name' => '预警信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::PRODUCT_INVENTORY_WARNING_PRODUCT => [
                'name' => '产品信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ]
        ],
        Constants::TYPE_ORDER => [
            self::ORDER_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::ORDER_GROUP_PRODUCT => [
                'name' => '交易产品',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::ORDER_GROUP_FEE => [
                'name' => '费用信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::ORDER_GROUP_BUYER => [
                'name' => '买方信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::ORDER_GROUP_TRANSPORT => [
                'name' => '货运信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::ORDER_GROUP_BANK => [
                'name' => '银行信息',
                'can_add_custom_field' => false,
                'is_hide' => false,
            ],
            self::ORDER_GROUP_SYSTEM => [
                'name' => '系统信息',
                'can_add_custom_field' => false,
                'is_hide' => false,
            ],
        ],
        Constants::TYPE_QUOTATION => [
            self::QUOTATION_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::QUOTATION_GROUP_PRODUCT => [
                'name' => '报价产品',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::QUOTATION_GROUP_FEE => [
                'name' => '价格条款',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::QUOTATION_GROUP_BUYER => [
                'name' => '买方信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::QUOTATION_GROUP_TRANSPORT => [
                'name' => '货运信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::QUOTATION_GROUP_SYSTEM => [
                'name' => '系统信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
        ],
        Constants::TYPE_COMPANY => [
            self::COMPANY_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::COMPANY_GROUP_CHARACTERISTIC => [
                'name' => '特征信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::COMPANY_GROUP_MANAGE => [
                'name' => '管理信息',
                'can_add_custom_field' => false,
                'is_hide' => false,
            ],
            self::COMPANY_GROUP_CONTACT => [
                'name' => '联系信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::COMPANY_GROUP_OTHER => [
                'name' => '其他信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ]
        ],
        Constants::TYPE_CUSTOMER => [
            self::CUSTOMER_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ]
        ],
        Constants::TYPE_LEAD => [
            self::LEAD_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ]
        ],
        Constants::TYPE_LEAD_CUSTOMER => [
            self::LEAD_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ]
        ],
        Constants::TYPE_OPPORTUNITY => [
            self::OPPORTUNITY_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::OPPORTUNITY_GROUP_PRODUCT => [
                'name' => '商机产品',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
        ],
        Constants::TYPE_CASH_COLLECTION => [
            self::CASH_COLLECTION_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ]
        ],
        Constants::TYPE_CASH_COLLECTION_INVOICE => [
            self::CASH_COLLECTION_INVOICE_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::CASH_COLLECTION_INVOICE_GROUP_SYSTEM => [
                'name' => '系统信息',
                'can_add_custom_field' => false,
                'is_hide' => true,
            ],
            self::CASH_COLLECTION_INVOICE_GROUP_RECORD => [
                'name' => '关联回款单',
                'can_add_custom_field' => false,
                'is_hide' => true,
            ],
        ],
        Constants::TYPE_ORDER_PROFIT=>[
            self::ORDER_PROFIT_GROUP_ORDER => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::ORDER_PROFIT_GROUP_ORDER_FUND => [
                'name' => '订单附加费用',
                'can_add_custom_field' => false,
                'is_hide' => false,
            ],
            self::ORDER_PROFIT_GROUP_ORDER_FORMULA => [
                'name' => '公式字段',
                'can_add_custom_field' => false,
                'is_hide' => false,
            ],
        ],
        Constants::TYPE_SHIPPING_INVOICE => [
            self::SHIPPING_INVOICE_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::SHIPPING_INVOICE_GROUP_SHIPPING_TRANSPORT => [
                'name' => '运输信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::SHIPPING_INVOICE_GROUP_TIME_INFO => [
                'name' => '日期信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::SHIPPING_INVOICE_GROUP_PRODUCT_BASE => [
                'name' => '出运明细信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::SHIPPING_INVOICE_GROUP_PRODUCT_ADDITION_FEE => [
                'name' => '附加费用',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::SHIPPING_INVOICE_GROUP_SYSTEM=>[
                'name' => '系统信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ]
        ],
        Constants::TYPE_PURCHASE_ORDER => [
            self::PURCHASE_ORDER_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::PURCHASE_ORDER_GROUP_PRODUCT => [
                'name' => '交易产品',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::PURCHASE_ORDER_GROUP_CHARGE => [
                'name' => '费用信息',
                'can_add_custom_field' => false,
                'is_hide' => false,
            ],
            self::PURCHASE_ORDER_GROUP_SYSTEM => [
                'name' => '系统信息',
                'can_add_custom_field' => false,
                'is_hide' => true,
            ],
        ],
        Constants::TYPE_SUPPLIER => [
            self::SUPPLIER_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::SUPPLIER_GROUP_ATTACH => [
                'name' => '附件',
                'can_add_custom_field' => false,
                'is_hide' => true,
            ],
            self::SUPPLIER_GROUP_PAYMENT => [
                'name' => '资金信息',
                'can_add_custom_field' => false,
                'is_hide' => true,
            ],
            self::SUPPLIER_GROUP_CONTACT => [
                'name' => '联系人信息',
                'can_add_custom_field' => true,
                'is_hide' => true,
            ],
            self::SUPPLIER_GROUP_SYSTEM => [
                'name' => '系统信息',
                'can_add_custom_field' => false,
                'is_hide' => true,
            ],
        ],
        Constants::TYPE_SUPPLIER_PRODUCT => [
            self::SUPPLIER_PRODUCT_GROUP_SUPPLIER_BASIC => [
                'name' => '供应商信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::SUPPLIER_PRODUCT_GROUP_PRODUCT => [
                'name' => '产品信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::SUPPLIER_PRODUCT_GROUP_QUOTE => [
                'name' => '报价信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::SUPPLIER_PRODUCT_GROUP_SYSTEM => [
                'name' => '系统信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
        ],
        Constants::TYPE_PURCHASE_INBOUND_INVOICE => [
            self::PURCHASE_INBOUND_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::PURCHASE_INBOUND_GROUP_SYSTEM => [
                'name' => '系统信息',
                'can_add_custom_field' => false,
                'is_hide' => true,
            ],
            self::PURCHASE_INBOUND_GROUP_PRODUCT => [
                'name' => '入库明细',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
        ],
        Constants::TYPE_OTHER_INBOUND_INVOICE => [
            self::OTHER_INBOUND_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::OTHER_INBOUND_GROUP_SYSTEM => [
                'name' => '系统信息',
                'can_add_custom_field' => false,
                'is_hide' => true,
            ],
            self::OTHER_INBOUND_GROUP_PRODUCT => [
                'name' => '入库明细',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
        ],
        Constants::TYPE_SALE_OUTBOUND_INVOICE => [
            self::SALE_OUTBOUND_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::SALE_OUTBOUND_GROUP_SYSTEM => [
                'name' => '系统信息',
                'can_add_custom_field' => false,
                'is_hide' => true,
            ],
            self::SALE_OUTBOUND_GROUP_PRODUCT => [
                'name' => '出库明细',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
        ],
        Constants::TYPE_OTHER_OUTBOUND_INVOICE => [
            self::OTHER_OUTBOUND_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::OTHER_OUTBOUND_GROUP_SYSTEM => [
                'name' => '系统信息',
                'can_add_custom_field' => false,
                'is_hide' => true,
            ],
            self::OTHER_OUTBOUND_GROUP_PRODUCT => [
                'name' => '出库明细',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
        ],
        Constants::TYPE_PURCHASE_RETURN_INVOICE => [
            self::PURCHASE_RETURN_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::PURCHASE_RETURN_GROUP_SYSTEM => [
                'name' => '系统信息',
                'can_add_custom_field' => false,
                'is_hide' => true,
            ],
            self::PURCHASE_RETURN_GROUP_PRODUCT => [
                'name' => '退货明细',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
        ],
        Constants::TYPE_PRODUCT_TRANSFER_PURCHASE => [
            self::PRODUCT_TRANSFER_PURCHASE_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => false,
                'is_hide' => false,
            ],
            self::PRODUCT_TRANSFER_PURCHASE_GROUP_PRODUCT => [
                'name' => '任务明细',
                'can_add_custom_field' => false,
                'is_hide' => false,
            ],
            self::PRODUCT_TRANSFER_PURCHASE_GROUP_SYSTEM => [
                'name' => '系统信息',
                'can_add_custom_field' => false,
                'is_hide' => true,
            ],
        ],
        Constants::TYPE_PRODUCT_TRANSFER_INBOUND => [
            self::PRODUCT_TRANSFER_INBOUND_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => false,
                'is_hide' => false,
            ],
            self::PRODUCT_TRANSFER_INBOUND_GROUP_PRODUCT => [
                'name' => '任务明细',
                'can_add_custom_field' => false,
                'is_hide' => false,
            ],
            self::PRODUCT_TRANSFER_INBOUND_GROUP_SYSTEM => [
                'name' => '系统信息',
                'can_add_custom_field' => false,
                'is_hide' => true,
            ],
        ],
        Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND => [
            self::PRODUCT_TRANSFER_OUTBOUND_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => false,
                'is_hide' => false,
            ],
            self::PRODUCT_TRANSFER_OUTBOUND_GROUP_PRODUCT => [
                'name' => '任务明细',
                'can_add_custom_field' => false,
                'is_hide' => false,
            ],
            self::PRODUCT_TRANSFER_OUTBOUND_GROUP_SYSTEM => [
                'name' => '系统信息',
                'can_add_custom_field' => false,
                'is_hide' => true,
            ],
        ],
        Constants::TYPE_PAYMENT_INVOICE => [
            self::PAYMENT_INVOICE_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::PAYMENT_INVOICE_GROUP_PAYMENT => [
                'name' => '收款信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::PAYMENT_INVOICE_GROUP_SYSTEM => [
                'name' => '系统信息',
                'can_add_custom_field' => false,
                'is_hide' => true,
            ],
            self::PAYMENT_INVOICE_GROUP_PRODUCT => [
                'name' => '付款明细',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
        ],
        Constants::TYPE_PRODUCT_TRANSFER_OTHER => [
            self::PRODUCT_TRANSFER_PURCHASE_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => false,
                'is_hide' => false,
            ],
            self::PRODUCT_TRANSFER_PURCHASE_GROUP_SYSTEM => [
                'name' => '系统信息',
                'can_add_custom_field' => false,
                'is_hide' => true,
            ]
        ],
        Constants::TYPE_COST_INVOICE => [
            self::COST_INVOICE_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::COST_INVOICE_GROUP_PAYMENT => [
                'name' => '收款信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::COST_INVOICE_GROUP_SYSTEM => [
                'name' => '系统信息',
                'can_add_custom_field' => false,
                'is_hide' => true,
            ],
            self::COST_INVOICE_GROUP_RECORD => [
                'name' => '费用明细',
                'can_add_custom_field' => false,
                'is_hide' => false,
            ],
        ],
        Constants::TYPE_FORWARDER => [
            self::FORWARDER_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::FORWARDER_GROUP_ATTACH => [
                'name' => '附件',
                'can_add_custom_field' => false,
                'is_hide' => true,
            ],
            self::FORWARDER_GROUP_PAYMENT => [
                'name' => '资金信息',
                'can_add_custom_field' => false,
                'is_hide' => false,
            ],
            self::FORWARDER_GROUP_CONTACT => [
                'name' => '联系人信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::FORWARDER_GROUP_SYSTEM => [
                'name' => '系统信息',
                'can_add_custom_field' => false,
                'is_hide' => true,
            ],
        ],
        \Constants::TYPE_WORK_JOURNAL => [
            self::WORK_JOURNAL_GROUP_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ]
        ],
        \Constants::TYPE_INQUIRY_COLLABORATION => [
            self::TYPE_INQUIRY_COLLABORATION_BASIC => [
                'name' => '基本信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::TYPE_INQUIRY_COLLABORATION_PRODUCT => [
                'name' => '询价产品',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::TYPE_INQUIRY_COLLABORATION_SUMMATION => [
                'name' => '合计信息',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ],
            self::TYPE_INQUIRY_COLLABORATION_SYS => [
                'name' => '系统信息',
                'can_add_custom_field' => false,
                'is_hide' => true,
            ],
        ],
        \Constants::TYPE_INQUIRY_FEEDBACK => [
            self::TYPE_INQUIRY_FEEDBACK_BASIC => [
                'name' => '询价反馈',
                'can_add_custom_field' => true,
                'is_hide' => false,
            ]
        ]
    ];

    const DEFAULT_NOT_ENABLE = [
        Constants::TYPE_COMPANY => [
            'pool_id',
            'star', // 等客群全量后开放
            'group_id', // 等客群全量后开放
            'category_ids',// 对新用户屏蔽掉主营产品
        ],
        Constants::TYPE_ORDER => [
            'opportunity_id',
        ],
        Constants::TYPE_QUOTATION => [
            'opportunity_id'
        ],
        Constants::TYPE_LEAD => [
            'trail_status', 'star', 'tag', 'cus_tag', 'timezone', 'annual_procurement', 'intention_level', 'biz_type'
        ],
        Constants::TYPE_PRODUCT => [
            'package_unit',
        ],
        Constants::TYPE_OPPORTUNITY => [
            'cus_tag',
            'handler',
            'main_user',
        ],
    ];

    const FUNCTIONAL_DEFAULT_ENABLE = [
        PrivilegeConstants::FUNCTIONAL_OPPORTUNITY => [
            Constants::TYPE_ORDER => [
                'opportunity_id'
            ],
            Constants::TYPE_QUOTATION => [
                'opportunity_id'
            ],
        ],
        PrivilegeConstants::FUNCTIONAL_OPPORTUNITY_TAG => [
            Constants::TYPE_OPPORTUNITY => [
//                'cus_tag',
            ],
        ],
    ];

    const FORMAT_VALUE_FLAG_PC = false; //字段可用
    const FORMAT_VALUE_FLAG_APP = true; //普通APP格式
    const FORMAT_VALUE_FLAG_APP_MULTI = 2; //APP格式字段字段多选

    const FIELD_ENABLE = '0';        //字段可用
    const FIELD_DISABLE = '1';        //字段禁用

    const COMPANY_TYPES = [Constants::TYPE_LEAD, Constants::TYPE_COMPANY];
    const CUSTOMER_TYPES = [Constants::TYPE_LEAD_CUSTOMER, Constants::TYPE_CUSTOMER];

    const DEFAULT_VALUE_CURRENT_TIME = 'moment';

    const USER_TYPE_OF_OS = 1;
    const USER_TYPE_OF_CLIENT = 0;

    //custom
    const FIELD_SYS_FLAG = 1; //系统字段
    const FIELD_CUSTOM_FLAG = 0; //自定义字段

    const FIELD_DELETE_FLAG = 0; //字段-删除，全局不可见
    const FIELD_NORMAL_FLAG = 1; //字段-正常


    // unique
    const UNIQUE_CHECK_DISABLE = 0;
    const UNIQUE_CHECK_ENABLE = 1;
    const UNIQUE_PARTIAL_CHECK_ENABLE = 2;
    const UNIQUE_ALL_CHECK_ENABLE = 3;

    const UNIQUE_PREVENT_SAVE_ENABLE = 1;
    const UNIQUE_PREVENT_SAVE_DISABLE = 0;//@troy 注意！实际业务场景保存的是2，是否阻止保存时用unique_prevent==1?1:0判断

    // 引用字段相关
    const MAX_QUOTE_FIELD_LAYER = 3;

    const FIELD_NAME_SETTING_TO_TABLE_MAP = [
        \Constants::TYPE_ORDER => [
            'customer_emial' => 'customer_email',
        ],
    ];

	const DUPLICATE_FIELD_TYPE = [
		Constants::TYPE_COMPANY => [
			Constants::TYPE_COMPANY, Constants::TYPE_CUSTOMER
		],
		Constants::TYPE_LEAD => [
			Constants::TYPE_LEAD, Constants::TYPE_LEAD_CUSTOMER
		],
	];

    const DUPLICATE_FIELD_ID_RELATE_MAP = [
        Constants::TYPE_LEAD => [
            'company_name' => 'name'
        ],
    ];

    const DUPLICATE_FIELD_TYPE_RELATE_MAP = [
        Constants::TYPE_LEAD => [
            Constants::TYPE_LEAD => Constants::TYPE_COMPANY,
            Constants::TYPE_LEAD_CUSTOMER => Constants::TYPE_CUSTOMER
        ],
    ];

    const HISTORY_EXTRA_MASK_FIELD_MAP = [
        Constants::TYPE_LEAD => [
            'cus_tag' => ['client_tag_list'], // if `cus_tag` is disabled, `client_tag_list` should also be disabled
        ],
        Constants::TYPE_COMPANY => [
            'cus_tag' => ['client_tag_list'],
        ],
        Constants::TYPE_OPPORTUNITY => [
            'cus_tag' => ['client_tag_list'],
        ],
    ];

    const FIELD_UNIQUE_CHECK_MAP = [
        \Constants::TYPE_COMPANY => [
            'name',
            'serial_id',
            'short_name',
            'fax',
            'address',
            'homepage',
            'tel'
        ],
        \Constants::TYPE_CUSTOMER => [
            'contact',
            'email',
            'tel_list'
        ],
        \Constants::TYPE_LEAD => [
            'company_name',
//            'serial_id',
//            'fax',
//            'address',
            'homepage',
            'tel'
        ],
        \Constants::TYPE_LEAD_CUSTOMER => [
            'contact',
            'email',
            'tel_list'
        ]
    ];

    const FIELD_EDIT_CHECK_MAP = [
        \Constants::TYPE_COMPANY => [
            'name',
            'serial_id',
            'short_name',
            'fax',
            'address',
            'homepage',
            'tel'
        ],
        \Constants::TYPE_CUSTOMER => [
            'contact',
            'email',
            'tel_list'
        ]
    ];

    const IGNORE_FIELD_EDIT_UNIQUE_CHECK_MAP = [
        \Constants::TYPE_COMPANY => [
            'name',
            'serial_id',
            'tel'
        ],
        \Constants::TYPE_CUSTOMER => [
            'email',
            'tel_list'
        ]
    ];

    const IGNORE_FIELD_EDIT_UNIQUE_MESSAGE_MAP = [
        \Constants::TYPE_COMPANY => [
            'serial_id',
            'homepage',
            'tel'
        ],
        \Constants::TYPE_CUSTOMER => [
            'email',
            'tel_list'
        ]
    ];

    const IGNORE_FIELD_EDIT_UNIQUE_PREVENT_MAP = [
        \Constants::TYPE_COMPANY => [
            'name',
            'serial_id',
        ],
        \Constants::TYPE_CUSTOMER => [
        ],
    ];

    const RELATION_IGNORE_FIELD_MAP = [
        \Constants::TYPE_ORDER => [
            \Constants::TYPE_PRODUCT => [
                self::PRODUCT_GROUP_PRICE=>[
                    'cost_with_tax',
                ],
                self::PRODUCT_GROUP_CHARACTER => [
                    'is_parts'
                ],
            ]
        ],
        \Constants::TYPE_QUOTATION => [
            \Constants::TYPE_PRODUCT => [
                self::PRODUCT_GROUP_CHARACTER => [
                    'is_parts'
                ],
            ],
        ],
        \Constants::TYPE_PURCHASE_ORDER => [
            \Constants::TYPE_ORDER => [
                self::ORDER_GROUP_BASIC => [
                    'currency',
                    'exchange_rate',
                    'users',
                    'departments',
                    'order_no',
                    'status',
                    'amount_rmb',
                    'amount_usd',
                    'refer_quotation_no'
                ],
                self::ORDER_GROUP_PRODUCT => [
                    'info_json'
                ],
            ],
            \Constants::TYPE_SUPPLIER => [
                self::SUPPLIER_GROUP_BASIC => [
                    'supplier_id',
                ]
            ],
            \Constants::TYPE_PRODUCT => [
                self::PRODUCT_GROUP_DESCRIBE => [
//                    'info_json'
                ],
                self::PRODUCT_GROUP_CHARACTER => [
                    'is_parts'
                ],
            ]
        ],
        \Constants::TYPE_SHIPPING_INVOICE => [
            \Constants::TYPE_ORDER => [
                self::ORDER_GROUP_BASIC => [
                    'company_name',
                    'customer_name',
                    'users',
                    'departments',
                    'currency',
                    'exchange_rate',
                    'users',
                    'departments',
                    'order_no',
                    'status',
                    'amount_rmb',
                    'amount_usd',
                ],
                self::ORDER_GROUP_PRODUCT => [
                    'sku_id',
                    'description',
                    'product_remark',
                    'package_unit',
                    'count_per_package',
                    'product_package_remark',
//                    'cost_with_tax',
                    'unit',
                    'local_product_no',
                    'package_volume_amount',
                    'package_gross_weight_amount',
                    'product_total_count',
                    'product_total_amount',
                    'info_json'
                ]
            ],
            \Constants::TYPE_PRODUCT => [
                self::PRODUCT_GROUP_DESCRIBE=>[
//                    'info_json'
                ],
                self::PRODUCT_GROUP_SKU => [
                    'product_net_weight'
                ],
                self::PRODUCT_GROUP_PRICE=>[
//                    'cost_with_tax',
                ],
                self::PRODUCT_GROUP_PACKAGE => [
                    'package_gross_weight',
                    'package_size',
                    'package_volume'
                ],
                self::PRODUCT_GROUP_SIZE => [
                    'product_size'
                ],
                self::PRODUCT_GROUP_CUSTOM => [
                    'hs_code',
                    'customs_name',
                    'customs_cn_name',
                ],
                self::PRODUCT_GROUP_CARTON=>[
                    'carton_size'
                ],
                self::PRODUCT_GROUP_CHARACTER => [
                    'is_parts'
                ],
            ],
            \Constants::TYPE_FORWARDER => [
                self::FORWARDER_GROUP_BASIC => [
                    'name',
                ],
                self::FORWARDER_GROUP_CONTACT => [
                    'contact_name',
                ]
            ],
            \Constants::TYPE_CUSTOMER => [
                self::CUSTOMER_GROUP_BASIC => [
                    'main_customer_flag'
                ]
            ]
        ],
        \Constants::TYPE_INQUIRY_COLLABORATION => [
            \Constants::TYPE_PRODUCT => [
                self::PRODUCT_GROUP_BASIC=>[
                    'product_no',
                    'images',
                    'name',
                    'cn_name',
                    'model',
                    'sku_attributes',
                    'unit',
                    'description',
                    'product_remark',
                ],
                self::PRODUCT_GROUP_CHARACTER => [
                    'is_parts'
                ],
            ],
        ]
    ];

    const RELATION_DISPLAY_FLAG_FALSE_FIELD_MAP = [
        \Constants::TYPE_ORDER => [
            'capital_account_id' => [
                'capital_name',
                'capital_bank',
                'bank_account',
                'capital_account_address',
                'capital_account_remark',
            ],
        ],
        \Constants::TYPE_SALE_OUTBOUND_INVOICE => [
            'cost_subtotal_rmb' => [
                'cost_total_rmb',
            ],
            'cost_unit_price' => [
                'cost_total_rmb',
            ]
        ],
        \Constants::TYPE_OTHER_OUTBOUND_INVOICE => [
            'cost_subtotal_rmb' => [
                'cost_total_rmb',
            ],
            'cost_unit_price' => [
                'cost_total_rmb',
            ]
        ],
    ];

    const RELATION_FIELD_MAP = [
        \Constants::TYPE_PURCHASE_ORDER => [
            \Constants::TYPE_ORDER => [
                self::ORDER_GROUP_PRODUCT => [
                    'only' => [
                        'product_name',
                        'product_image',
                        'product_model',
                        'description',
                        'other_cost',
                        'unit',
                        'cost_with_tax',
                        'package_unit',
                        'count_per_package',
                        'package_volume',
                        'package_gross_weight',
                        'product_remark',
                        'product_package_remark',
                        'external_field_data'
                    ]

                ]
            ],
            \Constants::TYPE_PRODUCT => [
                self::CMS_PRODUCT_GROUP_BASIC => [
                    'only' => [
                        'images',
                        'group_id',
                        'description',
                        'from_url',
                        'external_field_data'
                    ]
                ]
            ],
        ],
        \Constants::TYPE_ORDER => [
            \Constants::TYPE_PRODUCT => [
                self::PRODUCT_GROUP_BASIC => [
                    'exclude' => [
                        'unit',
                        'product_no',
                        'name',
                        'product_remark',
                    ]

                ],
                self::PRODUCT_GROUP_PACKAGE => [
                    'exclude' => [
                        'package_remark',
                        'package_volume',
                        'package_gross_weight',
                        'count_per_package',
                    ]

                ],
                self::PRODUCT_GROUP_DESCRIBE => [
                    'exclude' => [
                        'category_ids',
                    ]

                ],
            ],
        ],
    ];

    const SPECIFIC_FIELD_LIST = [];

    const SPECIFIC_FIELD_MAP = [
        PrivilegeConstants::CRM_AMES_BASIC_SYSTEM_ID => ['source_detail']
    ];

    //工作流的某些字段是通过组合条件实现的
    const FIELD_EXTRA_FILTER = [
        \Constants::TYPE_ORDER => [
            'order_status_update_time' => [
                [
                    'field' => 'status',
                    'value' => null,
                    'operator' => WorkflowConstant::FILTER_OPERATOR_UPDATE,
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'refer_type' => \Constants::TYPE_ORDER,
                ]
            ],
        ],
    ];
    //允许进行多级关联的模块，即设置字段关联一个关联类型的字段
    const RELATE_FIELD_TYPE_FIELDS_MAP = [
        \Constants::TYPE_PURCHASE_ORDER => [
            \Constants::TYPE_ORDER,
        ],
        \Constants::TYPE_SHIPPING_INVOICE => [
            \Constants::TYPE_ORDER,
        ],
    ];
    /** 开启设置公式默认值的模块 */
    const ENABLE_FORMULA_DEFAULT_VALUE_MODULE_LIST = [
        \Constants::TYPE_ORDER,
        \Constants::TYPE_PURCHASE_ORDER,
        \Constants::TYPE_INQUIRY_COLLABORATION,
        \Constants::TYPE_QUOTATION,
        \Constants::TYPE_INQUIRY_FEEDBACK
    ];

    const ENABLE_STATISTIC_FIELD_MODULE_LIST = [
        \Constants::TYPE_ORDER,
        \Constants::TYPE_OPPORTUNITY,
        \Constants::TYPE_QUOTATION,
    ];


    public const SUPPORT_RELATION_FIELD_TYPE_MAP = [
        \Constants::TYPE_SHIPPING_INVOICE => [
            \Constants::TYPE_ORDER => OrderFieldTask::class,
            \Constants::TYPE_COMPANY => CompanyFieldTask::class,
            \Constants::TYPE_CUSTOMER => CustomerFieldTask::class,
            \Constants::TYPE_FORWARDER => ForwardFieldTask::class,
            \Constants::TYPE_PRODUCT => ProductFieldTask::class,
        ],
        \Constants::TYPE_QUOTATION => [
            \Constants::TYPE_PRODUCT => ProductFieldTask::class,
        ],
        \Constants::TYPE_INQUIRY_COLLABORATION => [
            \Constants::TYPE_PRODUCT => ProductFieldTask::class,
            \Constants::TYPE_COMPANY => CompanyFieldTask::class,
            \Constants::TYPE_OPPORTUNITY => OpportunityFieldTask::class,
        ],

         \Constants::TYPE_INQUIRY_COLLABORATION_PRODUCT => [
            \Constants::TYPE_PRODUCT => ProductFieldTask::class,
        ]
    ];

    const IS_LIST = 1;
    const IS_NOT_LIST = 0;

    protected $skipCheckName = false;

    public static function getExternalTypes()
    {
        return array_merge(static::COMPANY_TYPES, static::CUSTOMER_TYPES);
    }

    public static function isCompanyExternal(array $type)
    {
        return count(array_intersect($type, static::COMPANY_TYPES));
    }

    public static function isCustomerExternal(array $type)
    {
        return count(array_intersect($type, static::CUSTOMER_TYPES));
    }

    private $client_id;
    private $type;
    /** @var CustomFieldRepository */
    private $repository;
    protected $userId;
    protected $userType = self::USER_TYPE_OF_CLIENT; // 0-
    public static $clientCustomerRequire = true;
    public static $fieldClientId;

    /**
     * @param $clientId
     * @return bool
     */
    public static function getClientCustomerRequire($clientId)
    {
        if ($clientId == self::$fieldClientId) return self::$clientCustomerRequire;
        $fieldInfo = \common\library\custom_field\Helper::getFieldListInfo($clientId, 0, \Constants::TYPE_CUSTOMER, 1, ['email']);
        self::$clientCustomerRequire = (array_column($fieldInfo, 'require', 'id')['email'] ?? 1) ? true : false;
        self::$fieldClientId = $clientId;
        return self::$clientCustomerRequire;
    }

    /**
     * FieldService constructor.
     * @param int $client_id
     * @param int $type
     */
    public function __construct(int $client_id, int $type)
    {
        $this->client_id = $client_id;
        $this->repository = new CustomFieldRepository($client_id, $type);
        $this->setType($type);
    }

    public function setType($type)
    {
        $this->type = $type;
        $this->repository->setType($type);
    }

    public function getType()
    {
        return $this->type;
    }

    public function getRepository() {
        return $this->repository;
    }

    public function setUserId($userId, $userType = self::USER_TYPE_OF_CLIENT)
    {
        $this->userId = $userId;
        $this->userType = $userType;
        $this->repository->setUserId($userId, $userType);
    }

    /**
     * 根据$id获得字段信息
     *
     * @param string $id
     * @return array
     */
    public function field(string $id)
    {
        return $this->repository->fieldById($id);
    }

    /**
     * 更新字段设置
     *
     * @param array $attributes
     * @return bool
     * @throws \Exception
     */
    public function updateField(array $attributes)
    {
        $attributes['name'] = trim($attributes['name']);
        
        $this->checkName($attributes['name'], $attributes['id']);
        if (isset($attributes['group_id'])) {
            $this->checkGroup($attributes['group_id']);
        }

        // 下拉单选和下拉多选，要限制菜单名字符 Max 长度为255个字符
        if (in_array($attributes['field_type'], [CustomFieldService::FIELD_TYPE_SELECT, CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT])
            && is_array($attributes['ext_info'])) {

            foreach ($attributes['ext_info'] as &$item) {
                if (is_array($item)){
                    continue;
                }

	            $item = trim($item);
	            if (mb_strlen($item) > InvoiceValidator::FIELD_LENGTH_SELECT) {
                    throw new \ProcessException('下拉列表不能超过255个字符');
                }
            }
        }

        if (in_array($attributes['field_type'], [CustomFieldService::FIELD_TYPE_FIELDS, CustomFieldService::FIELD_TYPE_QUOTE_FIELDS])) {
            //兼容图片字段
            if(str_contains($attributes['relation_field'], 'images_')){
                [$attributes['relation_field'], $suffix] = explode('_', $attributes['relation_field']);
                if(isset($suffix[0])){
                    $attributes['ext_info'] = '{"index":'. $suffix[0] .'}';
                }
            }

            $relationFieldInfo = $this->getRelationFieldInfo($this->client_id, $attributes['relation_type'], $attributes['relation_field']);
            $relationOriginFieldInfo = \common\library\custom_field\Helper::getOriginFieldInfo($this->client_id, $relationFieldInfo);
            $attributes['relation_origin_field'] = $relationOriginFieldInfo['id'];
            $attributes['relation_origin_field_type']  = $relationOriginFieldInfo['field_type'];
            $attributes['relation_origin_type']  = $relationOriginFieldInfo['type'];

            if($attributes['field_type'] == CustomFieldService::FIELD_TYPE_QUOTE_FIELDS){
                $attributes['relation_field_name'] = $relationFieldInfo['name'];
                $attributes['require'] = $attributes['edit_required'] = $attributes['is_editable'] =0;
            }
        }

		if (!(in_array($this->type, [\Constants::TYPE_LEAD, \Constants::TYPE_LEAD_CUSTOMER]) && is_numeric($attributes['id'])) && \common\library\custom_field\Helper::allowCheckUnique($attributes['id'], $this->type, $attributes['field_type'])) {
			$customPoolDuplicateRule = new CustomPoolDuplicateRule($this->client_id, $this->type, $attributes['id']);
			$customPoolDuplicateRule->disable_flag = $attributes['disable_flag'];
			$customPoolDuplicateRule->unique_check = $attributes['unique_check'];
			$customPoolDuplicateRule->unique_message = $attributes['unique_message'];
			$customPoolDuplicateRule->unique_prevent = $attributes['unique_prevent'];
			$customPoolDuplicateRule->save();
		}


		$uniqueCheckFieldIds = $notUniqueCheckFieldIds = [];
		if (in_array($this->type, [\Constants::TYPE_COMPANY, \Constants::TYPE_CUSTOMER])) {
			[$uniqueCheckFieldIds, $notUniqueCheckFieldIds] = \common\library\custom_field\Helper::getChangeFieldsWithUnique($this->client_id, \Constants::TYPE_COMPANY, [$attributes]);
		}


        $result = $this->repository->updateField($attributes);
        if (!$result) {
            throw new \ProcessException('自定义字段更新失败');
        }

        (new PrivilegeField($this->client_id))->flushReferFieldPrivilege($this->type);

        // 字段从非必填->必填 或者 隐藏禁用字段时需要重算
        (new PrivilegeFieldV2($this->client_id))->refreshUserRoleFieldPrivilegeByReferType($this->type);


		if (in_array($this->type, [\Constants::TYPE_COMPANY, \Constants::TYPE_CUSTOMER])) {

			$uniqueCheckFieldIds = array_filter($uniqueCheckFieldIds, function ($v) {
				return !is_numeric($v);
			});
			$notUniqueCheckFieldIds = array_filter($notUniqueCheckFieldIds, function ($v) {
				return !is_numeric($v);
			});

			if (!empty($uniqueCheckFieldIds) || !empty($notUniqueCheckFieldIds)) {

				\common\library\account\Helper::setClientExternalValue($this->client_id, DuplicateFlagBuilder::DUPLICATE_EXTERNAL_MAP[\Constants::TYPE_COMPANY], 1);
				Browser::push($this->userId, DuplicateFlagBuilder::DUPLICATE_BROWSER_MAP[\Constants::TYPE_COMPANY], 1);
				//todo
				\common\library\CommandRunner::run(
					'fieldDuplicate',
					'rebuildDuplicateFlagByFields',
					[
						'client_id' => $this->client_id,
						'user_id' => $this->userId,
						'type' => \Constants::TYPE_COMPANY,
						'unique_check_field_ids' => implode(',', $uniqueCheckFieldIds),
						'not_unique_check_field_ids' => implode(',', $notUniqueCheckFieldIds),
					],
				);
			}
		}

        $this->syncFieldToLayout($attributes);
        return $result;
    }

	/**
	 * 添加自定义字段
	 * @param array $attributes
	 * @return bool
	 * @throws \ProcessException
	 * @throws \Exception
	 */
    public function insertField(array $attributes)
    {
        $attributes['name'] = trim($attributes['name']);
        $this->checkName($attributes['name'],'',0);
        $this->checkGroup($attributes['group_id']);

        // 下拉单选和下拉多选，要限制菜单名字符 Max 长度为255个字符
        if (in_array($attributes['field_type'], [CustomFieldService::FIELD_TYPE_SELECT, CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT])
            && is_array($attributes['ext_info'])) {

            foreach ($attributes['ext_info'] as &$item) {

	            $item = trim($item);

                if (mb_strlen($item) > InvoiceValidator::FIELD_LENGTH_SELECT) {
                    throw new \RuntimeException(\Yii::t('customer', 'Drop-down list cannot exceed 255 characters'));
                }
            }
        }

        // 如果是引用字段，需要检验该字段是否允许被引用
        if(
            $attributes['field_type'] == CustomFieldService::FIELD_TYPE_QUOTE_FIELDS &&
            !empty($attributes['relation_type']) && !empty($attributes['relation_field']) &&
            !is_numeric($attributes['relation_field']) &&
            isset(QuoteFieldConstant::MODULE_MAP[$attributes['relation_type']])
        ){
            $objectName = FieldHelper::getOldCustomFieldObjName($this->client_id, $attributes['relation_type'], $attributes['relation_field']);
            if(empty($objectName)){
                throw new \RuntimeException(\Yii::t('field', 'field not quotable'));
            }

            $quoteFieldSetting = FieldSettingFactory::make($this->client_id, $objectName);
            if(empty($quoteFieldSetting)){
                throw new \RuntimeException(\Yii::t('field', 'field not quotable'));
            }

            $quotableFields = $quoteFieldSetting->quotableFields();
            $relationField = FieldHelper::getTransferFieldConfig($attributes['relation_type'])['field'][$attributes['relation_field']]['field'] ?? $attributes['relation_field'];
            if(empty($quotableFields[$relationField])){
                throw new \RuntimeException(\Yii::t('field', 'field not quotable'));
            }
        }

        if (in_array($attributes['field_type'], [CustomFieldService::FIELD_TYPE_FIELDS, CustomFieldService::FIELD_TYPE_QUOTE_FIELDS])) {
            //兼容图片字段
            if(str_contains($attributes['relation_field'], 'images_')){
                [$attributes['relation_field'], $suffix] = explode('_', $attributes['relation_field']);
                if(isset($suffix[0])){
                    $attributes['ext_info'] = '{"index":'. $suffix[0] .'}';
                }
            }

            $relationFieldInfo = $this->getRelationFieldInfo($this->client_id, $attributes['relation_type'], $attributes['relation_field']);
            $relationOriginFieldInfo = \common\library\custom_field\Helper::getOriginFieldInfo($this->client_id, $relationFieldInfo);
            $attributes['relation_origin_field'] = $relationOriginFieldInfo['id'];
            $attributes['relation_origin_type']  = $relationOriginFieldInfo['type'];
            $attributes['relation_origin_field_type']  = $relationOriginFieldInfo['field_type'];
            $attributes['relation_field_name'] = $relationFieldInfo['name'];

            if(in_array($attributes['field_type'], [CustomFieldService::FIELD_TYPE_QUOTE_FIELDS, CustomFieldService::FIELD_TYPE_FORMULA, CustomFieldService::FIELD_TYPE_CALCULATE])){
//                引用字段不可编辑（is_editable = 0），不可勾选必填（edit_required = 0），不可必填（require = 0），且需要记录父对象源字段和顶级源字段（relation_type,relation_field,relation_field_type,relation_field_name,relation_origin_type,relation_origin_field）
                $attributes['require'] = $attributes['edit_required'] = $attributes['is_editable'] = 0;
            }
        }

//        if (in_array($attributes['relation_field'], PrivilegeField::$referFieldNotReadAndWriteFieldSetting[$this->type][$attributes['relation_type']] ?? [])) {
//            $attributes['is_editable'] = 0;
//        }

		$result = $this->repository->insertField($attributes);

        (new PrivilegeField($this->client_id))->flushReferFieldPrivilege($this->type);
        (new PrivilegeFieldV2($this->client_id))->refreshUserRoleFieldPrivilegeByReferType($this->type);

		if (in_array($attributes['field_type'], [CustomFieldService::FIELD_TYPE_TEXT, CustomFieldService::FIELD_TYPE_TEXTAREA]) && in_array($this->type, [\Constants::TYPE_COMPANY, \Constants::TYPE_CUSTOMER])) {
//客户模块才需要判断客户池
			$client = Client::getClient($this->client_id);
			$detectKey = DuplicateConstants::MODULE_MAP[\Constants::TYPE_COMPANY]['detecting'];
			$ruleTypeKey = DuplicateConstants::MODULE_MAP[\Constants::TYPE_COMPANY]['rule_type'];
			$duplicateRuleType = (($client->getSettingAttributes()[$detectKey] ?? 0) == \common\library\duplicate\DuplicateConstants::DUPLICATE_CONFLICT_DETECTING) ? DuplicateFlagBuilder::DUPLICATE_ALL_RULE_TYPE : ($client->getSettingAttributes()[$ruleTypeKey] ?? 0);
			$customPoolDuplicateRule = new CustomPoolDuplicateRule($this->client_id, $this->type);
			$customPoolDuplicateRule->client_id = $this->client_id;
			$customPoolDuplicateRule->rule_type = $duplicateRuleType;
			$customPoolDuplicateRule->field_id = $result->id;
			$customPoolDuplicateRule->module = $this->type;
			$customPoolDuplicateRule->pool_id = 0;
			$customPoolDuplicateRule->name = $result->name;
			$customPoolDuplicateRule->field_base = $result->base;
			$customPoolDuplicateRule->field_type = $result->field_type;
			$customPoolDuplicateRule->unique_check = $result->unique_check;
			$customPoolDuplicateRule->unique_prevent = $result->unique_prevent;
			$customPoolDuplicateRule->system_flag = 0;

			$customPoolDuplicateRule->save();
		}

        $this->syncFieldToLayout($result->getAttributes());

        return $result;
    }

    protected function getRelationFieldInfo($clientId, $type, $field)
    {
        $extraList = array_column(self::getExtraFieldList($type, WorkflowConstant::RULE_TYPE_QUOTE_FIELD, $clientId), null, 'id');
        if (isset($extraList[$field])) {
            return $extraList[$field];
        }
        return CustomFieldService::getField($clientId, $type, $field);
    }

    /**
     * 删除指定id的字段设置
     *
     * @param string $id
     * @param bool   $deleteFailException
     *
     * @return bool
     * @throws \Exception
     */
    public function deleteField(string $id, bool $deleteFailException = true)
    {
        $fieldInfo = $this->field($id);

        if (empty($fieldInfo)){
            if ($deleteFailException) {
                throw new \RuntimeException(\Yii::t('field', 'field not exist'));
            } else {
                return false;
            }
        }

        if ($fieldInfo['base'] == 1) {
            throw new \RuntimeException(\Yii::t('customer', 'System field cannot be deleted'));
        }

        \common\library\custom_field\CustomFieldService::getDownNextFields($fieldInfo, true);

        $result = $this->repository->deleteField($id);

		if (in_array($fieldInfo['field_type'], [CustomFieldService::FIELD_TYPE_TEXT, CustomFieldService::FIELD_TYPE_TEXTAREA]) && in_array($this->type, [\Constants::TYPE_COMPANY, \Constants::TYPE_CUSTOMER])) {
			$customPoolDuplicateRule = new CustomPoolDuplicateRule($this->client_id, $this->type, $fieldInfo['id']);
			$customPoolDuplicateRule->enable_flag = 0;
			$customPoolDuplicateRule->update(['enable_flag']);
		}

        if ($deleteFailException && !$result) {
            throw new \RuntimeException(\Yii::t('customer', 'Failed to delete custom field'));
        }
        $this->syncFieldToLayout($fieldInfo);

        (new PrivilegeField($this->client_id))->flushReferFieldPrivilege($this->type);
        (new PrivilegeFieldV2($this->client_id))->deleteFieldPrivilege($this->type, $id);

        return $result;
    }

    /**
     * @param string $name
     * @param string $except
     */
    public function checkName(string $name, string $except = '', $base = 1)
    {
        if(trim($name) === ''){
            throw new \RuntimeException(\Yii::t('customer', 'Field names cannot be empty'));
        }
        if (!preg_match('/^[^#_]*$/', $name)) {
            throw new \RuntimeException(\Yii::t('customer', 'Field names cannot be empty and #_ are not allowed'));
        }
        if (mb_strlen($name) >= 64) {
            throw new \RuntimeException(\Yii::t('customer', 'Field name cannot exceed 64 characters'));
        }
        if ($this->repository->isNameExist($name, empty($except) ? [] : [$except])) {
            throw new \RuntimeException(\Yii::t('customer', '{module} This field name has been used', ['{module}' => \Yii::t('common', "module.{$this->type}")]));
        }

        $fieldExportService = new FieldExportService($this->client_id, $this->type);
        //如果是自定义字段 创建编辑时候 不允许为导出占用对字段
        if (in_array($name, $fieldExportService->getSpecialFieldName())
            &&
            (!empty($except) && is_numeric($except) || $base == 0)
        ) {
            throw new \RuntimeException(\Yii::t('customer', 'This field name is system export field name'));
        }
    }

    /**
     * 获得分组信息
     *
     * @return array
     */
    private function groupMap()
    {
        return self::GROUP_MAP[$this->type];
    }

    /**
     * 检查分组是否支持添加自定义字段
     *
     * @param $group
     */
    private function checkGroup($group)
    {
        $groups = $this->groupMap();
        if (!isset($groups[$group])) {
            throw new \RuntimeException(\Yii::t('customer', 'Group does not exist'));
        }
        if (!$groups[$group]['can_add_custom_field']) {
            throw new \RuntimeException(\Yii::t('customer', 'This group cannot add custom fields'));
        }
    }

    public function fieldsByIds(array $ids)
    {
        if (empty($ids)) {
            return [];
        }
        return array_map(function (\CActiveRecord $field) {
            return $field->getAttributes();
        }, $this->repository->all($ids));
    }

    public static function getGroupNameMap($type)
    {
        static $map = [];

        if (!array_key_exists($type, $map)) {
            $map[$type] = array_map(function ($n) {
                return $n['name'];
            }, self::GROUP_MAP[$type] ?? []);
        }

        return $map[$type];
    }

    /**
     * 获取单据可引用的字段列表
     *
     * @param int $type 功能模块ID
     * @param array $group_id 分组ID
     * @return array
     */
    public function getRelationFieldList(int $type = Constants::TYPE_PRODUCT, array $group_id = [])
    {
        return $this->repository->getRelationFieldList($type, $group_id);
    }

    public function getQuoteFieldList($type, $group_id=[])
    {
        return $this->repository->getQuoteFieldList($type, $group_id);
    }

    public static function getExternalField($clientId, $id)
    {
        $customFieldList = new FieldList($clientId);
        $customFieldList->setId($id);
        $customFieldList->setType(CustomFieldService::getExternalTypes());
        $customFieldList->setBase(0);
        $result = $customFieldList->find();
        if (!($field = current($result))) {
            return null;
        }
        return current($result);
    }
    public static function getExternalFieldByIds($clientId, $id,$type)
    {
        $customFieldList = new FieldList($clientId);
        $customFieldList->setId($id);
        $customFieldList->setType($type);
        $list = $customFieldList->findByIds();
        if (!$list) {
            return null;
        }
        $result = array();
        foreach ($list as $item){
            $result[$item['id']] = $item;
        }
        return $result;
    }

    public static function getExternalFieldTypeMap($clientId, $type)
    {
        return ClassCacheRepository::instance($clientId, static::class)->remember('getExternalFieldTypeMap:' . $type, function () use ($clientId, $type) {
            $fieldListObj = new FieldList($clientId);
            $fieldListObj->setType($type);
            $fieldListObj->setBase(0);
            $fieldListObj->setFields(['id','field_type']);
            $fieldList = $fieldListObj->find();
            return array_column($fieldList,'field_type','id');
        });
    }


    public static function getFieldListByType($clientId, $base, $type, $fieldType)
    {
        $cacheRepository = ClassCacheRepository::instance($clientId, static::class);
        $cacheKey = implode('_', [is_null($base) ? 'null' : $base, $type, json_encode($fieldType)]);
        $list = $cacheRepository->get($cacheKey);
        if (is_null($list)) {
            $customFieldList = new FieldList($clientId);
            $customFieldList->setFieldType($fieldType);
            $customFieldList->setType($type);
            $customFieldList->setBase($base);
            $list = $customFieldList->find();
            $cacheRepository->set($cacheKey, $list);
        }

        return $list;
    }

    public static function isListField($type, $field_type, $group_id)
    {
        if (($type == Constants::TYPE_ORDER && $group_id == CustomFieldService::ORDER_GROUP_PRODUCT)
            || ($type == Constants::TYPE_QUOTATION && $group_id == CustomFieldService::QUOTATION_GROUP_PRODUCT)
            || ($type == Constants::TYPE_OPPORTUNITY && $group_id == CustomFieldService::OPPORTUNITY_GROUP_PRODUCT)) {
            return 1;
        }
        return 0;
    }

    public static function showField($clientId, $type, $fieldId)
    {
        if ($field = self::getField($clientId, $type, $fieldId)) {
            $field->disable_flag = 0;
            return $field->save();
        }
        return false;
    }

    public static function hideField($clientId, $type, $fieldId)
    {
        if ($field = self::getField($clientId, $type, $fieldId)) {
            $field->disable_flag = 1;
            return $field->save();
        }
        return false;
    }

    public static function disableField($clientId, $type, $fieldId)
    {
        if ($field = self::getField($clientId, $type, $fieldId)) {
            $field->disable_flag = 1;
            $field->enable_flag = 0;
            return $field->save();
        }
        return false;
    }

    public static function enableField($clientId, $type, $fieldId)
    {
        if ($field = self::getField($clientId, $type, $fieldId)) {
            $field->disable_flag = 0;
            $field->enable_flag = 1;
            return $field->save();
        }
        return false;
    }

    public static function getField($clientId, $type, $fieldId)
    {
        return \common\models\client\CustomField::model()->findByAttributes([
            'client_id' => $clientId,
            'type'      => $type,
            'id'        => $fieldId
        ]);
    }


    /** 改动到这里会影响到 WorkflowFilterRunner 目前影响到 绩效 任务 客群 工作流
     * 谨慎修改
     * @param $type
     * @param $scene
     * @param $clientId
     * @return array
     */
    public static function  getExtraFieldList($type, $scene, $clientId)
    {
        /*
         * $scene需要声明使用场景
         */
        $extraSettings = [
            Constants::TYPE_COMPANY => [
                [
                    'id'         => 'user_id',
                    'name'       => \Yii::t('field', '跟进人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'public_type',
                    'name'       => \Yii::t('field', '移入公海方式'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'score',
                    'name'       => \Yii::t('field', '客户评分'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'order_time',
                    'name'       => \Yii::t('field', '最近联系时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'archive_time',
                    'name'       => \Yii::t('field', '创建时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'archive_type',
                    'name' => \Yii::t('field', '创建方式'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base' => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'success_opportunity_count',
                    'name' => \Yii::t('field', '赢单商机数'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base' => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'performance_order_count',
                    'name' => \Yii::t('field', '成交订单数'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base' => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'deal_time',
                    'name' => \Yii::t('field', '最近成交日期'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base' => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'create_user',
                    'name' => \Yii::t('field', '创建人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'type' => Constants::TYPE_COMPANY,
                    'base' => 1,
                ],
                [
                    'id' => 'last_edit_user',
                    'name' => \Yii::t('field', '最近修改人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'type' => Constants::TYPE_COMPANY,
                    'base' => 1,
                ],
                [
                    'id' => 'edit_time',
                    'name' => \Yii::t('field', '资料更新时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'type' => Constants::TYPE_COMPANY,
                    'base' => 1,
                ],
                [
                    'id' => 'private_time',
                    'name' => \Yii::t('field', '最近进入私海时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'type' => Constants::TYPE_COMPANY,
                    'base' => 1,
                ],
                [
                    'id' => 'public_time',
                    'name' => \Yii::t('field', '最近进入公海时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'type' => Constants::TYPE_COMPANY,
                    'base' => 1,
                ],
                [
                    'id' => 'release_count',
                    'name' => \Yii::t('field', '进入公海次数'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'type' => Constants::TYPE_COMPANY,
                    'base' => 1,
                ],
//                [
//                    'id' => 'will_public',
//                    'name' => \Yii::t('field', '即将移入公海客户'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
//                    'type' => Constants::TYPE_COMPANY,
//                    'base' => 1,
//                ],
                [
                    'id' => 'next_move_to_public_date',
                    'name' => \Yii::t('field', '下次移入公海日期'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                    'type' => Constants::TYPE_COMPANY,
                    'base' => 1,
                ],
                [
                    'id'         => 'recent_follow_up_time',
                    'name'       => \Yii::t('field', '最近跟进时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'type'       => Constants::TYPE_COMPANY,
                    'base'       => 1,
                ],
                [
                    'id' => 'transaction_order_amount',
                    'name' => \Yii::t('field', '成交订单金额'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base' => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'success_opportunity_amount_cny',
                    'name' => \Yii::t('field', '赢单商机金额（CNY）'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base' => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'success_opportunity_amount_usd',
                    'name' => \Yii::t('field', '赢单商机金额（USD）'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base' => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'last_owner',
                    'name'       => \Yii::t('field', '原跟进人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'client_tag_list',
                    'name'       => \Yii::t('field', '公司标签'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'base'       => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'ali_store_id',
                    'name'       => \Yii::t('field', '来源详情'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'base'       => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'transaction_order_amount_avg',
                    'name'       => \Yii::t('field', '成交订单均价'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                    'unitType'   => '',
                    'unit_type'  => 'currency',
                ],
                [
                    'id'         => 'success_opportunity_amount_avg_cny',
                    'name'       => \Yii::t('field', '赢单商机均价（CNY）'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'success_opportunity_amount_avg_usd',
                    'name'       => \Yii::t('field', '赢单商机均价（USD）'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'ongoing_opportunity_count',
                    'name'       => \Yii::t('field', '进行中的商机数'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'customer_count',
                    'name'       => \Yii::t('field', '联系人数'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'user_count',
                    'name'       => \Yii::t('field', '跟进人数'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_WORKFLOW,
                        WorkflowConstant::RULE_TYPE_TASK,
                        WorkflowConstant::RULE_TYPE_PERFORMANCE,
                    ],
                ],
                [
                    'id'         => 'transaction_order_first_time',
                    'name'       => \Yii::t('field', '首次成交订单日期'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'latest_write_follow_up_time',
                    'name'       => \Yii::t('field', '最近「写跟进」时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'send_mail_time',
                    'name'       => \Yii::t('field', '最近发件时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'receive_mail_time',
                    'name'       => \Yii::t('field', '最近收件时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'latest_receive_ali_trade_time',
                    'name'       => \Yii::t('field', '最近收到阿里TM询盘时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'edm_time',
                    'name'       => \Yii::t('field', '最近发EDM时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'latest_transaction_order_time',
                    'name'       => \Yii::t('field', '最近成交订单日期'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'        => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'latest_success_opportunity_time',
                    'name'       => \Yii::t('field', '最近赢单日期'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'latest_receive_ali_tm_time',
                    'name'       => \Yii::t('field', '最近收到阿里TM消息时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'latest_send_ali_tm_time',
                    'name'       => \Yii::t('field', '最近发送阿里TM消息时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'latest_whatsapp_time',
                    'name'       => \Yii::t('field', '最近WhatsApp沟通时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'growth_level',
                    'name'       => \Yii::t('field', '阿里买家身份'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'base'       => 0,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'next_follow_up_time',
                    'name'       => \Yii::t('field', '下次日程时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 0,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'success_opportunity_first_time',
                    'name'       => \Yii::t('field', '首次赢单日期'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                    'base'       => 0,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_WORKFLOW,
                        WorkflowConstant::RULE_TYPE_PERFORMANCE,
                    ],
                ],
            ],
            Constants::TYPE_LEAD => [
                [
                    'id'         => 'user_id',
                    'name'       => \Yii::t('field', '跟进人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'order_time',
                    'name'       => \Yii::t('field', '最近联系时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'archive_time',
                    'name'       => \Yii::t('field', '创建时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'update_time',
                    'name'       => \Yii::t('field', '最近修改时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'renew_time',
                    'name'       => \Yii::t('field', '更新时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'create_user_id',
                    'name'       => \Yii::t('field', '创建人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'is_archive',
                    'name'       => \Yii::t('field', '归档'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'follow_count',
                    'name'       => \Yii::t('field', '跟进数'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'transfer_count',
                    'name'       => \Yii::t('field', '转移次数'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'release_count',
                    'name'       => \Yii::t('field', '进入公海次数'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'create_user',
                    'name' => \Yii::t('field', '创建人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base' => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'last_edit_user',
                    'name' => \Yii::t('field', '最近修改人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base' => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'edit_time',
                    'name' => \Yii::t('field', '资料更新时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base' => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'private_time',
                    'name' => \Yii::t('field', '最近进入私海时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base' => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'public_time',
                    'name' => \Yii::t('field', '最近进入公海时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base' => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'fail_type',
                    'name'       => \Yii::t('field', '线索无效理由'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'type'       => Constants::TYPE_LEAD,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'store_id',
                    'name'       => \Yii::t('field', '来源详情'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'type'       => Constants::TYPE_LEAD,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'duplicate_flag',
                    'name'       => \Yii::t('field', '是否疑似重复'),
                    'field_type' => CustomFieldService::FIELD_TYPE_BOOLEAN,
                    'base'       => 1,
                    'type'       => Constants::TYPE_LEAD,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_WORKFLOW,
                    ],
                ],
                [
                    'id'         => 'growth_level',
                    'name'       => \Yii::t('field', '阿里买家身份'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'base'       => 0,
                    'type'       => Constants::TYPE_LEAD,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_WORKFLOW,
                    ],
                ],
                [
                    'id'         => 'client_tag_list',
                    'name'       => \Yii::t('field', '公司标签'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'base'       => 1,
                    'type'       => Constants::TYPE_LEAD,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_WORKFLOW,
                    ],
                ],
                [
                    'id'         => 'send_mail_time',
                    'name'       => \Yii::t('field', '最近发件时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'type'       => Constants::TYPE_LEAD,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'receive_mail_time',
                    'name'       => \Yii::t('field', '最近收件时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'type'       => Constants::TYPE_LEAD,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'mail_time',
                    'name'       => \Yii::t('field', '最近收发件时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'type'       => Constants::TYPE_LEAD,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'follow_up_time',
                    'name'       => \Yii::t('field', '最近「写跟进」时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'type'       => Constants::TYPE_LEAD,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'conversion_time',
                    'name'       => \Yii::t('field', '转化成客户时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'type'       => Constants::TYPE_LEAD,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'edm_time',
                    'name'       => \Yii::t('field', '最近发EDM时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'type'       => Constants::TYPE_LEAD,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_WORKFLOW,
                    ],
                ],
                [
                    'id'         => 'latest_receive_ali_trade_time',
                    'name'       => \Yii::t('field', '最近收到阿里TM询盘时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'type'       => Constants::TYPE_LEAD,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_WORKFLOW,
                    ],
                ],
                [
                    'id'         => 'latest_send_ali_tm_time',
                    'name'       => \Yii::t('field', '最近发送阿里TM消息时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'type'       => Constants::TYPE_LEAD,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_WORKFLOW,
                    ],
                ],
                [
                    'id'         => 'latest_receive_ali_tm_time',
                    'name'       => \Yii::t('field', '最近收到阿里TM消息时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_WORKFLOW,
                    ],
                ],

            ],
            Constants::TYPE_OPPORTUNITY => [
//                [
//                    'id'         => 'main_user',
//                    'name'       => \Yii::t('field', '负责人'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
//                    'base'       => 1,
//                    'ext_info'   => '{}',
//                ],
//                [
//                    'id'         => 'handler',
//                    'name'       => \Yii::t('field', '团队成员'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
//                    'base'       => 1,
//                    'ext_info'   => '{}',
//                ],
                [
                    'id'         => 'create_user',
                    'name'       => \Yii::t('field', '创建人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'account_date',
                    'name'       => \Yii::t('field', '结束日期'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'trail_time',
                    'name'       => \Yii::t('field', '动态更新时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'edit_time',
                    'name'       => \Yii::t('field', '资料更新时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'succeed_time',
                    'name'       => \Yii::t('field', '赢单时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'update_time',
                    'name'       => \Yii::t('field', '更新时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'serial_id',
                    'name'       => \Yii::t('field', '商机编号'),
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'stage_type',
                    'name'       => \Yii::t('field', '商机状态'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'fail_stage',
                    'name'       => \Yii::t('field', '输单阶段'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'create_type',
                    'name' => \Yii::t('field', '创建方式'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base' => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => CashCollection::FIELD_STATUS,
                    'name'       => \Yii::t('field', '回款状态'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => CashCollection::FIELD_COLLECT_AMOUNT,
                    'name'       => \Yii::t('field', '已回款金额'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_WORKFLOW,
                        WorkflowConstant::RULE_TYPE_TASK,
                        WorkflowConstant::RULE_TYPE_PERFORMANCE,
                    ],
                ],
                [
                    'id'         => CashCollection::FIELD_NOT_COLLECT_AMOUNT,
                    'name'       => \Yii::t('field', '待回款金额'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_WORKFLOW,
                        WorkflowConstant::RULE_TYPE_TASK,
                        WorkflowConstant::RULE_TYPE_PERFORMANCE,
                    ],
                ],
                [
                    'id'         => 'success_rate',
                    'name'       => \Yii::t('field', '赢率'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'stage_edit_time',
                    'name'       => \Yii::t('field', '当前阶段停留天数'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_WORKFLOW,
                        WorkflowConstant::RULE_TYPE_TASK,
                        WorkflowConstant::RULE_TYPE_PERFORMANCE,
                    ],
                ],
                [
                    'id' => 'create_time',
                    'name'       => \Yii::t('field', '创建时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'cash_collection_status',
                    'name' => \Yii::t('field', '回款状态'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_WORKFLOW,
                        WorkflowConstant::RULE_TYPE_TASK,
                        WorkflowConstant::RULE_TYPE_PERFORMANCE,
                    ],
                ],

//                [
//                    'id' => 'approval_status',
//                    'name'       => \Yii::t('field', '审批状态'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
//                    'base'       => 1,
//                    'ext_info'   => '{}',
//                ],
                [
                    'id' => 'disable_flag',
                    'name' => \Yii::t('field', '归档状态'),
                    'field_type' => CustomFieldService::FIELD_TYPE_BOOLEAN,
                    'base'       => 1,
                    'type'       => Constants::TYPE_OPPORTUNITY,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_WORKFLOW,
                        WorkflowConstant::RULE_TYPE_PERFORMANCE,
                        WorkflowConstant::RULE_TYPE_STATISTIC,
                    ],
                ],
                [
                    'id'         => 'client_tag_list',
                    'name'       => \Yii::t('field', '商机标签'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'base'       => 1,
                    'type'       => Constants::TYPE_OPPORTUNITY,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_WORKFLOW,
                        WorkflowConstant::RULE_TYPE_PERFORMANCE,
                        WorkflowConstant::RULE_TYPE_STATISTIC,
                    ],
                ],
            ],
            Constants::TYPE_ORDER => [
                [
                    'id'         => 'users',
                    'name'       => \Yii::t('field', '业绩归属人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'handler',
                    'name'       => \Yii::t('field', '当前处理人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'create_user',
                    'name'       => \Yii::t('field', '创建人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => CashCollection::FIELD_STATUS,
                    'name'       => \Yii::t('field', '回款状态'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => CashCollection::FIELD_COLLECT_AMOUNT,
                    'name'       => \Yii::t('field', '已回款金额'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => CashCollection::FIELD_NOT_COLLECT_AMOUNT,
                    'name'       => \Yii::t('field', '待回款金额'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'create_time',
                    'name'       => \Yii::t('field', '创建时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'update_time',
                    'name'       => \Yii::t('field', '更新时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
//                [
//                    'id'         => 'ali_store_id',
//                    'name'       => \Yii::t('field', '来源店铺'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
//                    'base'       => 1,
//                    'ext_info'   => '{}',
//                ],
                // ali_status_id 废弃，使用 ali_status_name
//                [
//                    'id'         => 'ali_status_id',
//                    'name'       => \Yii::t('field', '阿里订单状态'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
//                    'base'       => 1,
//                    'ext_info'   => '{}',
//                ],
//                [
//                    'id'         => 'ali_status_name',
//                    'name'       => \Yii::t('field', '阿里订单状态'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
//                    'base'       => 1,
//                    'ext_info'   => '{}',
//                ],
                //暂不支持, 等下个版本
//                [
//                    'id'         => 'seller_account_id',
//                    'name'       => \Yii::t('field', '阿里业务员'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
//                    'base'       => 1,
//                    'ext_info'   => '{}',
//                ],
                [
                    'id'         => 'source_type',
                    'name'       => \Yii::t('field', '订单类型'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'capital_account_id',
                    'name'       => \Yii::t('field', '资金账户'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
//                [
//                    'id'         => 'archive_type',
//                    'name'       => \Yii::t('field', '创建方式'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
//                    'base'       => 1,
//                    'ext_info'   => '{}',
//                ],
                [
                    'id'         => 'first_order_flag',
                    'name'       => \Yii::t('field', '是否为成交首单'),
                    'field_type' => CustomFieldService::FIELD_TYPE_BOOLEAN,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'shipment_fee_amount',
                    'name'       => \Yii::t('field', '运费金额'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                    'unitType'   => '',
                    'unit_type'  => 'currency',
                ],
                [
                    'id'         => 'order_status_update_time',     //数据库不存在的字段 根据order.status变更时比较update_time
                    'name'       => \Yii::t('field', '订单状态变更时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_WORKFLOW,
                        WorkflowConstant::RULE_TYPE_PERFORMANCE,
                    ],
                    'enable_handler' => false, //由于执行动作禁用
                ],
                [
                    'id'         => 'departments',
                    'name'       => \Yii::t('field', '业绩归属部门'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_WORKFLOW,
                        WorkflowConstant::RULE_TYPE_PERFORMANCE,
                    ],
                ],
                [
                    'id'         => 'purchase_status_time',
                    'name'       => \Yii::t('field', '进入以销定购状态时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_WORKFLOW,
                        WorkflowConstant::RULE_TYPE_PERFORMANCE,
                    ],
                    'functional' => PrivilegeConstants::FUNCTIONAL_PURCHASE_ORDER,
                ],
                [
                    'id'         => 'first_collection_date',
                    'name'       => \Yii::t('field', '首次回款日期'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                    'base'       => 1,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_WORKFLOW,
                        WorkflowConstant::RULE_TYPE_PERFORMANCE,
                        WorkflowConstant::RULE_TYPE_STATISTIC,
                    ],
                ],
                [
                    'id'         => 'last_collection_date',
                    'name'       => \Yii::t('field', '最近回款日期'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                    'base'       => 1,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_WORKFLOW,
                        WorkflowConstant::RULE_TYPE_PERFORMANCE,
                        WorkflowConstant::RULE_TYPE_STATISTIC,
                    ],
                ]
            ],
            Constants::TYPE_QUOTATION => [
                [
                    'id'         => 'create_user',
                    'name'       => \Yii::t('field', '创建人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'create_time',
                    'name'       => \Yii::t('field', '创建时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'update_time',
                    'name'       => \Yii::t('field', '更新时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
//                [
//                    'id' => 'approval_status',
//                    'name'       => \Yii::t('field', '审批状态'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
//                    'base'       => 1,
//                    'ext_info'   => '{}',
//                ],
            ],
            Constants::TYPE_CASH_COLLECTION => [
                [
                    'id'         => 'create_user_id',
                    'name'       => \Yii::t('field', '创建人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'cash_collection_no',
                    'name'       => \Yii::t('field', '回款单号'),
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'create_time',
                    'name'       => \Yii::t('field', '创建时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'update_time',
                    'name'       => \Yii::t('field', '最后修改时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'real_amount',
                    'name'       => \Yii::t('field', '实到账金额'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                    'unitType'   => '',
                    'unit_type'  => 'currency',
                ],
                [
                    'id'         => 'cash_collection_invoice_bank_charge',
                    'name'       => \Yii::t('field', '手续费'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                    'unitType'   => '',
                    'unit_type'  => 'currency',
                ],
                [
                    'id'         => 'create_type',
                    'name'       => \Yii::t('field', '创建方式'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
            ],
            \Constants::TYPE_CASH_COLLECTION_INVOICE => [
                [
                    'id'         => 'handler',
                    'name'       => \Yii::t('field', '认领业务员'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'create_user',
                    'name'       => \Yii::t('field', '创建人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'update_user',
                    'name'       => \Yii::t('field', '修改人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
            ],
            Constants::TYPE_MAIL => [
                [
                    'id'         => 'user_id',
                    'name'       => \Yii::t('field', '发件人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SENDER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'receiver',
                    'name'       => \Yii::t('field', '收件人邮箱'),
                    'field_type' => CustomFieldService::FIELD_TYPE_RECEIVER,
                    'base'       => 1,
                    'ext_info'   => '{}'
                ],
                [
                    'id' => 'receiver_domain',
                    'name'       => \Yii::t('field', '收件人域名'),
                    'field_type' => CustomFieldService::FIELD_TYPE_RECEIVER_DOMAIN,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
            ],
            Constants::TYPE_PRODUCT => [
                [
                    'id'         => 'ali_store_id',
                    'name'       => \Yii::t('field', '来源店铺'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'source_type',
                    'name'       => \Yii::t('field', '创建方式'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'disable_flag',
                    'name'       => \Yii::t('field', '状态'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
            ],
            \Constants::TYPE_FOLLOWUP => [
                [
                    'id'         => 'content',
                    'name'       => \Yii::t('field', '跟进文本'),
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_PERFORMANCE,
                    ],
                ],
                [
                    'id'         => 'type',
                    'name'       => \Yii::t('field', '跟进动态类型'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_PERFORMANCE,
                    ],
                ],
                [
                    'id'         => 'create_time',
                    'name'       => \Yii::t('field', '创建时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_PERFORMANCE,
                    ],
                ],
                [
                    'id'         => 'create_user',
                    'name'       => \Yii::t('field', '创建人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_PERFORMANCE,
                    ],
                ],
            ],
            \Constants::TYPE_TASK => [
                [
                    'id'         => 'rule_id',
                    'name'       => \Yii::t('field', '任务规则'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_PERFORMANCE,
                    ],
                ],
                [
                    'id'         => 'create_time',
                    'name'       => \Yii::t('field', '任务创建时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_PERFORMANCE,
                    ],
                ],
                [
                    'id'         => 'end_time',
                    'name'       => \Yii::t('field', '任务截止时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                    'scene'      => [
                        WorkflowConstant::RULE_TYPE_PERFORMANCE,
                    ],
                ],
            ],
            \Constants::TYPE_PURCHASE_ORDER => [
                [
                    'id'         => 'creator',
                    'name'       => \Yii::t('field', '创建人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'modifier',
                    'name'       => \Yii::t('field', '修改人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'create_type',
                    'name'       => \Yii::t('field', '创建方式'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'inbound_status',
                    'name'       => \Yii::t('field', '入库状态'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'status',
                    'name'       => \Yii::t('field', '采购状态'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
            ],
            \Constants::TYPE_SUPPLIER => [
                [
                    'id'         => 'user_ids',
                    'name'       => \Yii::t('field', '跟进人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'archive_user',
                    'name'       => \Yii::t('field', '创建人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'update_user',
                    'name'       => \Yii::t('field', '修改人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
            ],
        ];

        $privilegeService = PrivilegeService::getInstance($clientId);

        //format
        $settingList = $extraSettings[$type] ?? [];

        //新版商机将main_user、handler写入系统字段表中，但是旧版商机仍需要提供这两个字段
        if ($type == \Constants::TYPE_OPPORTUNITY) {
            $client = new \common\library\account\Client($clientId);
            if (!$client->getExtentAttribute(\common\library\account\Client::EXTERNAL_KEY_OPPORTUNITY_V2_SWITCH)) {
                $hideFieldList = [
                    [
                        'id'         => 'main_user',
                        'name'       => \Yii::t('field', '负责人'),
                        'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                        'base'       => 1,
                        'ext_info'   => '{}',
                    ],
                    [
                        'id'         => 'handler',
                        'name'       => \Yii::t('field', '团队成员'),
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'base'       => 1,
                        'ext_info'   => '{}',
                    ],
                ];
                $settingList = array_merge($settingList, $hideFieldList);
            }
        }

        if ($scene == WorkflowConstant::RULE_TYPE_QUOTE_FIELD) {
            foreach ($settingList as &$extraSetting) {
                $extraSetting = array_replace([
                    'type' => $type,
                    'group_id' => 1,
                    'disable_flag' => 0,
                ], $extraSetting);
            }
        }

        $result = [];
        foreach ($settingList as $fieldItem)
        {
            $fieldItem['type'] = $type;
            //无权限不返回
            if (!empty($fieldItem['functional']) && !$privilegeService->hasFunctional($fieldItem['functional'])) {
                continue;
            }
            //字段有声明使用场景的需要filter
            if (!empty($fieldItem['scene']) && !in_array($scene, $fieldItem['scene'])) {
                continue;
            }
            unset($fieldItem['scene']);
            $result[] = $fieldItem;
        }

        return array_values($result);
    }

    public static function getExtraFieldListForApprovalFlow($type)
    {
        $extraSettings = [
            Constants::TYPE_COMPANY => [
                [
                    'id'         => 'user_id',
                    'name'       => \Yii::t('field', '跟进人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'score',
                    'name'       => \Yii::t('field', '客户评分'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'order_time',
                    'name'       => \Yii::t('field', '最近联系时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'archive_time',
                    'name'       => \Yii::t('field', '创建时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'archive_type',
                    'name' => \Yii::t('field', '创建方式'),
                    'field_type' => CustomFieldService::FIELD_TYPE_BOOLEAN,
                    'base' => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'success_opportunity_count',
                    'name' => \Yii::t('field', '赢单商机数'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base' => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'performance_order_count',
                    'name' => \Yii::t('field', '计入业绩订单数'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base' => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'deal_time',
                    'name' => \Yii::t('field', '最近成交日期'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                    'base' => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'growth_level',
                    'name'       => \Yii::t('field', '阿里买家身份'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'base'       => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'next_follow_up_time',
                    'name'       => \Yii::t('field', '下次日程时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'success_opportunity_first_time',
                    'name'       => \Yii::t('field', '首次赢单日期'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                    'base'       => 0,
                    'type'       => Constants::TYPE_COMPANY,
                    'ext_info'   => '{}',
                ],
            ],
            Constants::TYPE_LEAD => [
                [
                    'id'         => 'user_id',
                    'name'       => \Yii::t('field', '跟进人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'order_time',
                    'name'       => \Yii::t('field', '最近联系时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'archive_time',
                    'name'       => \Yii::t('field', '创建时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'update_time',
                    'name'       => \Yii::t('field', '最近修改时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'renew_time',
                    'name'       => \Yii::t('field', '更新时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'create_user_id',
                    'name'       => \Yii::t('field', '创建人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'follow_count',
                    'name'       => \Yii::t('field', '跟进数'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'transfer_count',
                    'name'       => \Yii::t('field', '转移次数'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'release_count',
                    'name'       => \Yii::t('field', '进入公海次数'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
            ],
            Constants::TYPE_OPPORTUNITY => [
//                [
//                    'id'         => 'main_user',
//                    'name'       => \Yii::t('field', '负责人'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
//                    'base'       => 1,
//                    'ext_info'   => '{}',
//                ],
//                [
//                    'id'         => 'handler',
//                    'name'       => \Yii::t('field', '团队成员'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
//                    'base'       => 1,
//                    'ext_info'   => '{}',
//                ],
                [
                    'id'         => 'create_user',
                    'name'       => \Yii::t('field', '创建人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'account_date',
                    'name'       => \Yii::t('field', '结束日期'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'succeed_time',
                    'name'       => \Yii::t('field', '赢单时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'serial_id',
                    'name'       => \Yii::t('field', '商机编号'),
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'stage_type',
                    'name'       => \Yii::t('field', '商机状态'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'fail_stage',
                    'name'       => \Yii::t('field', '输单阶段'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'create_type',
                    'name' => \Yii::t('field', '创建方式'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base' => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'success_rate',
                    'name'       => \Yii::t('field', '赢率'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'stage_edit_time',
                    'name'       => \Yii::t('field', '当前阶段停留天数'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'disable_flag',
                    'name' => \Yii::t('field', '归档状态'),
                    'field_type' => CustomFieldService::FIELD_TYPE_BOOLEAN,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'client_tag_list',
                    'name'       => \Yii::t('field', '商机标签'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
            ],
            Constants::TYPE_ORDER => [
                [
                    'id'         => 'users',
                    'name'       => \Yii::t('field', '业绩归属人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'handler',
                    'name'       => \Yii::t('field', '当前处理人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'create_user',
                    'name'       => \Yii::t('field', '创建人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
//                [
//                    'id' => 'approval_status',
//                    'name'       => \Yii::t('field', '审批状态'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
//                    'base'       => 1,
//                    'ext_info'   => '{}',
//                ],
                [
                    'id' => 'create_time',
                    'name'       => \Yii::t('field', '创建时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'update_time',
                    'name'       => \Yii::t('field', '更新时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
//                [
//                    'id'         => CashCollection::FIELD_STATUS,
//                    'name'       => \Yii::t('field', '回款状态'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
//                    'base'       => 1,
//                    'ext_info'   => '{}',
//                ],
//                [
//                    'id'         => CashCollection::FIELD_COLLECT_AMOUNT,
//                    'name'       => \Yii::t('field', '已回款金额'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//                    'base'       => 1,
//                    'ext_info'   => '{}',
//                ],
//                [
//                    'id'         => CashCollection::FIELD_NOT_COLLECT_AMOUNT,
//                    'name'       => \Yii::t('field', '待回款金额'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//                    'base'       => 1,
//                    'ext_info'   => '{}',
//                ],
                // ali_status_id 废弃，使用 ali_status_name
//                [
//                    'id'         => 'ali_status_id',
//                    'name'       => \Yii::t('field', '阿里订单状态'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
//                    'base'       => 1,
//                    'ext_info'   => '{}',
//                ],
                [
                    'id'         => 'ali_status_name',
                    'name'       => \Yii::t('field', '阿里订单状态'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'ali_store_id',
                    'name'       => \Yii::t('field', '来源店铺'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                //暂不支持, 等下个版本
//                [
//                    'id'         => 'seller_account_id',
//                    'name'       => \Yii::t('field', '阿里业务员'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
//                    'base'       => 1,
//                    'ext_info'   => '{}',
//                ],
                [
                    'id'         => 'source_type',
                    'name'       => \Yii::t('field', '订单类型'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'archive_type',
                    'name'       => \Yii::t('field', '创建方式'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
//应该是多写了，注释掉
//                [
//                    'id' => 'disable_flag',
//                    'name' => \Yii::t('field', '归档状态'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_BOOLEAN,
//                    'base'       => 1,
//                    'ext_info'   => '{}',
//                ],
//                [
//                    'id'         => 'client_tag_list',
//                    'name'       => \Yii::t('field', '商机标签'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
//                    'base'       => 1,
//                    'ext_info'   => '{}',
//                ],
            ],
            Constants::TYPE_QUOTATION => [
                [
                    'id'         => 'create_user',
                    'name'       => \Yii::t('field', '创建人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'create_time',
                    'name'       => \Yii::t('field', '创建时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'update_time',
                    'name'       => \Yii::t('field', '更新时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
//                [
//                    'id' => 'approval_status',
//                    'name'       => \Yii::t('field', '审批状态'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
//                    'base'       => 1,
//                    'ext_info'   => '{}',
//                ],
            ],
            Constants::TYPE_CASH_COLLECTION => [
                [
                    'id'         => 'create_user_id',
                    'name'       => \Yii::t('field', '创建人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'cash_collection_no',
                    'name'       => \Yii::t('field', '回款单号'),
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'create_time',
                    'name'       => \Yii::t('field', '创建时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id' => 'update_time',
                    'name'       => \Yii::t('field', '最后修改时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
            ],
            Constants::TYPE_MAIL => [
                [
                    'id'         => 'user_id',
                    'name'       => \Yii::t('field', '发件人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SENDER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'receiver',
                    'name'       => \Yii::t('field', '收件人邮箱'),
                    'field_type' => CustomFieldService::FIELD_TYPE_RECEIVER,
                    'base'       => 1,
                    'ext_info'   => '{}'
                ],
                [
                    'id' => 'receiver_domain',
                    'name'       => \Yii::t('field', '收件人域名'),
                    'field_type' => CustomFieldService::FIELD_TYPE_RECEIVER_DOMAIN,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'cc',
                    'name'       => \Yii::t('field', '抄送'),
                    'field_type' => CustomFieldService::FIELD_TYPE_RECEIVER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'bcc',
                    'name'       => \Yii::t('field', '密送'),
                    'field_type' => CustomFieldService::FIELD_TYPE_RECEIVER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
            ],
            Constants::TYPE_PRODUCT => [
                [
                    'id'         => 'ali_store_id',
                    'name'       => \Yii::t('field', '来源店铺'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'source_type',
                    'name'       => \Yii::t('field', '创建方式'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'disable_flag',
                    'name'       => \Yii::t('field', '状态'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
            ],
            \Constants::TYPE_PURCHASE_ORDER=>[
                [
                    'id'         => 'handler',
                    'name'       => \Yii::t('field', '当前处理人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'creator',
                    'name'       => \Yii::t('field', '创建人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'modifier',
                    'name'       => \Yii::t('field', '最新修改人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
            ],
            \Constants::TYPE_CASH_COLLECTION_INVOICE => [
                [
                    'id'         => 'handler',
                    'name'       => \Yii::t('field', '认领业务员'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'create_user',
                    'name'       => \Yii::t('field', '创建人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'update_user',
                    'name'       => \Yii::t('field', '修改人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],

            ],
            \Constants::TYPE_FOLLOWUP => [
            ],
            \Constants::TYPE_TASK => [
            ],
            \Constants::TYPE_INQUIRY_COLLABORATION => [
                [
                    'id'         => 'handler',
                    'name'       => \Yii::t('field', '处理人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'create_user',
                    'name'       => \Yii::t('field', '创建人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'update_user',
                    'name'       => \Yii::t('field', '修改人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'create_schedule',
                    'name'       => \Yii::t('field', '是否创建日程'),
                    'field_type' => CustomFieldService::FIELD_TYPE_BOOLEAN,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'status',
                    'name'       => \Yii::t('field', '任务状态'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'base'       => 1,
                    'ext_info'   => json_encode(\ArrayUtil::combineByFields([
                        'label' => array_values(OmsConstant::INQUIRY_COLLABORATIONS_TRANSLATE_MAP),
                        'value' => array_keys(OmsConstant::INQUIRY_COLLABORATIONS_TRANSLATE_MAP),
                    ])),
                ],
            ]
        ];

        return $extraSettings[$type] ?? [];
    }

    public function getFieldsInfo($fields=[], $ids = [], $conds=[]){
        $fieldList = new FieldList($this->client_id);
        $fieldList->setType($this->type);
        $fieldList->setEnableFlag(true);
        if(!empty($ids)){
            $fieldList->setId($ids);
        }
        if(!empty($fields)){
            $baseFields = ['id', 'base', 'name'];
            $fields = array_unique(array_merge($baseFields,$fields));
            $fieldList->setFields($fields);
        }
        !empty($conds['field_type']) && $fieldList->setFieldType($conds['field_type']);
        isset($conds['base']) && $fieldList->setBase($conds['base']);
        $fields = $fieldList->find();
        return $fields;
    }

    // 获取某字段的所有下游引用字段
    public static function getRelationFields($clientId, $fieldId, $conds = [], $selectFields=[]){
        $fieldList = new FieldList($clientId);
        $fieldList->setEnableFlag(true);
        $fieldList->setRelationField($fieldId);
        !empty($conds['relation_type']) && $fieldList->setRelationType($conds['relation_type']);
        isset($conds['disable_flag']) && $fieldList->setDisableFlag($conds['disable_flag']);
        isset($conds['field_type']) && $fieldList->setFieldType($conds['field_type']);
        $baseFields = ['id', 'base', 'name', 'type'];
        $fields = array_unique(array_merge($baseFields,$selectFields));
        $fieldList->setFields($fields);
        return $fieldList->find();
    }

    public static function getDownNextFields($fieldInfo, $throw = false, $adjacency = null) : array
    {
        $user = \User::getLoginUser();
        $language = $user->getLanguage();
        $adjacency = (new \common\library\object\field\Api())->getAdjacency([], true,  [FieldConstant::FUNCTION_TYPE_QUOTE, FieldConstant::FUNCTION_TYPE_FORMULA, FieldConstant::FUNCTION_TYPE_CALCULATE, FieldConstant::FUNCTION_TYPE_LINK]);
        $objName = \common\library\object\object_define\Helper::getTransferObjName($fieldInfo['type'], $fieldInfo);
        $indexKey = $objName . '_' . $fieldInfo['id'];
        $downNextFields = [];
        foreach ($adjacency as $downField => $relationFieldList) {
            if (in_array($indexKey, $relationFieldList)) {
                $downNextFields[] = $downField;
            }
        }
        if ($throw && !empty($downNextFields)) {
            $objectNameDescToFieldNamesMap = [];
            foreach ($downNextFields as $downNextField) {
                $fieldObjName = explode('_', $downNextField)[0];
                $fieldId = str_replace("{$fieldObjName}_", '', $downNextField);
                $fieldObj = new Field($user->getClientId());
                $fieldObj->loadField($fieldObjName, $fieldId);
                $objectNameDesc = \common\library\object\object_define\Helper::getName($fieldObjName, $language);
                $objectNameDescToFieldNamesMap[$objectNameDesc][] =  $fieldObj->field_name;
            }

            $placeholder = '';
            foreach ($objectNameDescToFieldNamesMap as $objectNameDesc => $fieldNames) {
                foreach ($fieldNames as $fieldName) {
                    $placeholder .= "「{$objectNameDesc}-{$fieldName}」、";
                }
            }
            $placeholder = rtrim($placeholder, '、');
            $message =  \Yii::t('field', '该字段不可删除！已被字段{placeholder}使用', ['{placeholder}' => $placeholder]);
            throw new \RuntimeException($message);
        }
        return $downNextFields;
    }

    public function getAllFieldsByParams($params)
    {
        $params = array_filter($params, function ($item) {
            $item = trim($item);
            return $item !== '';
        });
        $fieldList = new FieldList($this->client_id);
        $fieldList->setGroupId($params['groupId'] ?? []);
        $fieldList->setBase($params['base'] ?? null);
        $fieldList->setDisableFlag($params['disableFlag'] ?? null);
        $fieldList->setIsEditable($params['isEditable'] ?? null);
        $fieldList->setReadonly($params['readonly'] ?? null);
        $fieldList->setType($params['type'] ?? null);
        $fieldList->setId($params['id'] ?? null);
        $field = $fieldList->find();
        return $field;
    }

    public static function getAmesLimitFields()
    {
        return [
            \Constants::TYPE_COMPANY => [
                'field' => [
                    'score', // 评分
                    'last_owner_name', // 原跟进人
                    'pool_id', // 公海分组
                    'next_follow_up_time', // 下次跟进时间
                    'deal_time', // 最近成交日期
                    'success_opportunity_count', // 赢单商机数
                    'performance_order_count', // 计入业绩销售订单数
                    'private_time', // 最近进入私海时间
                    'public_time', // 最近进入公海时间
                    'release_count', // 进入公海次数
                    'duplicate_flag', // 是否疑似重复
                    'transaction_order_amount_avg', // 成交订单均价
                    'transaction_order_amount', // 成交订单金额
                    'ongoing_opportunity_count', // 进行中的商机数
                    'success_opportunity_amount_cny', // 赢单商机金额(CNY)
                    'success_opportunity_amount_usd', // 赢单商机金额(USD)
                    'success_opportunity_amount_avg_cny', // 赢单商机均价(CNY)
                    'success_opportunity_amount_avg_usd', // 赢单商机均价(USD)
                    'will_public', // 即将移入公海客户
                    'users', // 跟进人数Ï
                    'latest_write_follow_up_time', // 最近[写跟进]时间
                    'send_mail_time', // 最近发件时间
                    'receive_mail_time', // 最近收件时间
                    'latest_receive_ali_tm_time', // 最近收到阿里TM消息时间
                    'latest_send_ali_tm_time', // 最近发送阿里TM消息时间
                    'latest_receive_ali_trade_time', // 最近收到阿里询盘消息时间
                    'transaction_order_first_time', // 首次成交订单日期
                    'latest_transaction_order_time', // 最近成交订单日期
                    'success_opportunity_first_time', // 首次赢单日期
                    'latest_success_opportunity_time', // 最近赢单日期
                    'edm_time', // 最近发edm时间
                    'tips_latest_update_time', // tips最近更新时间
                    'main_lead_id', // 来源线索,
                    'next_move_to_public_date', //下次移入公海日期
                    'order_time', // 最近联系时间
                    'recent_follow_up_time', // 最近跟进时间
                    'last_owner_info', //原跟进人信息，
                    'last_owner', //原跟进人
//                    'ali_store_id', //国际站店铺，
                ],
                'item_setting' => [
                    ItemSettingConstant::ITEM_TYPE_ORIGIN => [
                        Origin::SYS_ORIGIN_ID_DISCOVERY,
                        Origin::SYS_ORIGIN_ID_OKKI_LEADS,
                        Origin::SYS_ORIGIN_ID_MARKETING,
                        Origin::SYS_ORIGIN_ID_AI_RECOMMEND,
                        Origin::SYS_ORIGIN_ID_ASSISTANT,
                    ]
                ],
            ],
        ];
    }

    /**
     * 部分字段跟模块功能有关，
     * 需要根据模块功能来判断是否需要展示
     * @return string[]
     */
    public function getExcludeIds()
    {
        $excludeIds = [];
        switch ($this->type) {
            case Constants::TYPE_PRODUCT:
                if (!\common\library\privilege_v3\Helper::checkHasProductPartFunctional($this->client_id)) {
                    $excludeIds = ['is_parts', 'parts_list_count'];
                }
                break;
            default:
                break;
        }
        return $excludeIds;
    }

    public function batchCheckNameExit(array $names, array $excepts)
    {
        foreach ($names as $name)
        {
            $isExist = $this->repository->isNameExist($name,$excepts);
            if ($isExist)
            {
                $sameName = explode(".",$name,2)[1];

                throw new \RuntimeException(\Yii::t('customer', '{sameName} Field name is duplicated',['{sameName}'=>$sameName]));
            }
        }
    }

    /**
     * 查询系统模块的必填字段
     * @return array
     */
    public function getRequiredCustomFieldsMap(): array
    {
        $customFieldList = new FieldList($this->client_id);
        $customFieldList->setType($this->type);
        $customFieldList->setEnableFlag(true);
        $customFieldList->setRequire(true);
        $customFieldList->setDisableFlag(false);
        $customFieldList->setPrivilegeInfo($this->userId);
        $customFieldList->setExcludeId(['main_customer_flag']);
        $list = $customFieldList->find();

        return array_column($list, 'name', 'id');
    }

    /**
     * 检查线索联系人邮箱必填性
     * @param $clientId
     * @return bool
     */
    public static function checkClientLeadCustomerRequire($clientId)
    {
        $fieldInfo = \common\library\custom_field\Helper::getFieldListInfo($clientId, 0, \Constants::TYPE_LEAD_CUSTOMER, 1, ['email']);

        if (empty($fieldInfo)) {
            return false;
        }
        return (bool)((array_column($fieldInfo, 'require', 'id')['email'] ?? 1));
    }

    //字段同步到默认布局
    public function syncFieldToLayout($fieldAttributes)
    {
        $objectName = ObjConstant::OBJ_MAP[$this->type] ?? null;
        if(empty($objectName)){
            return;
        }
        $mainObjName = \common\library\object\object_relation\Helper::getMainObjectName($objectName);

        if (!isset(Factory::INIT_LAYOUT_OBJECT_MAP[$objectName]) && !isset(Factory::INIT_LAYOUT_OBJECT_MAP[$mainObjName])) {
            return;
        }

        if (in_array(\Yii::app()->params['env'], ['dev','test'])) {
            $obj = new \common\library\layout\SyncFieldToLayout();
            $obj->syncFieldToLayout($this->client_id, $this->type, $fieldAttributes['id']);
        } else {
            CommandRunner::run('Layout', 'syncFieldToLayout', [
                'client_id' => $this->client_id,
                'type' => $this->type,
                'id' => $fieldAttributes['id']
            ]);
        }
    }

}
