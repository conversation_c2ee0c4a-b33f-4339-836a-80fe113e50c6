<?php

namespace common\library\search\field;

use common\library\custom_field\CustomFieldService;
use common\library\field\Constant;
use common\library\search\builder\BooleanBuilder;
use common\library\search\builder\RangeBuilder;
use common\library\search\builder\TermBuilder;
use common\library\search\SearchConstant;
use common\library\workflow\Helper;
use common\library\workflow\WorkflowConstant;

class DateField extends AbstractField
{

    public $fieldType = Constant::FIELD_TYPE_DATE;

    protected function getFilters()
    {
    }

    protected function getAnalyzer()
    {
    }

    protected function getCharFilters()
    {
    }

    protected function getProperty()
    {
        return [
            'type' => SearchConstant::PROPERTY_TYPE_DATE,
            'ignore_malformed' => true,
            'format' => 'yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis',
        ];
    }

    protected function getNormalizer()
    {
    }

    protected function getTokenizer()
    {
    }

    public function getBuilder($value, $operator, $valueType = null)
    {
        switch ($operator) {
            case WorkflowConstant::FILTER_OPERATOR_GREATER:
                $builder = new RangeBuilder($this->fieldId);
                $builder->gt($this->buildValue($value, $operator));
                break;
            case WorkflowConstant::FILTER_OPERATOR_LATER:
            case WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL:
                $builder = new RangeBuilder($this->fieldId);
                $builder->gte($this->buildValue($value, $operator));
                break;
            case WorkflowConstant::FILTER_OPERATOR_LESS:
                $builder = new RangeBuilder($this->fieldId);
                $builder->lt($this->buildValue($value, $operator));
                break;
            case WorkflowConstant::FILTER_OPERATOR_EARLIER:
            case WorkflowConstant::FILTER_OPERATOR_LESS_OR_EQUAL:
                $builder = new RangeBuilder($this->fieldId);
                $builder->lte($this->buildValue($value, $operator));
                break;
            case WorkflowConstant::FILTER_OPERATOR_EQUAL:
            case WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL:
                $builder = new RangeBuilder($this->fieldId);
                if (is_array($value)) {
                    [$start, $end] = $this->extractRange($value);
                } else {
                    if (strtotime($value) === false || is_string($value)) {
                        [$start, $end] = $this->extractRange($value);
                    } else {
                        $start = $this->buildValue($value, WorkflowConstant::FILTER_OPERATOR_LESS_OR_EQUAL);
                        $end = $this->buildValue($value, WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL);
                    }
                }
        
                $builder->gte($start);
        
                $builder->lte($end);
        
                if ($operator === WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL) {
            
                    $builder = (new BooleanBuilder())->mustNot($builder);
                }

                break;
            case WorkflowConstant::FILTER_OPERATOR_RANGE:
                [$start, $end] = $this->extractRange($value);
                $builder = (new RangeBuilder($this->fieldId))->lte($end)->gte($start);
                break;
            case WorkflowConstant::FILTER_OPERATOR_IS_NULL:
                $builder = new RangeBuilder($this->fieldId);
                $builder->lte($this->buildValue('1970-01-02', $operator));
                break;
            case WorkflowConstant::FILTER_OPERATOR_NOT_NULL:
                $builder = new RangeBuilder($this->fieldId);
                $builder->gte($this->buildValue('1970-01-02', $operator));
                break;
        }

        if (isset($builder) && in_array($operator, [WorkflowConstant::FILTER_OPERATOR_EARLIER, WorkflowConstant::FILTER_OPERATOR_LESS])) {
            $builder->gt('1970-01-01');
        }

        return $builder ?? null;
    }

    protected function buildValue($value, $operator)
    {
        if (is_numeric($value)) {
            return Helper::buildDateValueByDays($value, $this->fieldType, $operator);
        }

        return $value;
    }

    protected function extractRange($value)
    {
        if (is_string($value)) {
            return Helper::getDynamicDateRange($value, $this->fieldType);
        }
        $start = $value[0] ?? null;
        if ($start) {
            if ($this->fieldType == CustomFieldService::FIELD_TYPE_DATETIME) {
                $start = is_numeric($start) ? date('Y-m-d H:i:s', $start) : $start;
            } else {
                $start = is_numeric($start) ? date('Y-m-d', $start) : date('Y-m-d', strtotime($start));
            }
        }

        $end = $value[1] ?? null;
        if ($end) {
            if ($this->fieldType == CustomFieldService::FIELD_TYPE_DATETIME) {
                $end = is_numeric($end) ? date('Y-m-d H:i:s', $end) : $end;
            } else {
                $end = is_numeric($end) ? date('Y-m-d', $end) : date('Y-m-d', strtotime($end));
            }
        }

        return [$start, $end];
    }
}