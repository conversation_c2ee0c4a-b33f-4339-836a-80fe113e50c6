<?php
/**
 * Created by PhpStorm.
 * User: troy.li
 * Date: 2018-08-06
 * Time: 4:36 PM
 */

namespace common\library\search;

trait ESIndexTrait
{

    public function indexOneData($clientId, $item, $strip = true)
    {
        return $this->indexData($clientId, [$item], $strip);
    }

    public function removeOneData($clientId, $item)
    {
        return $this->removeData($clientId, [$item]);
    }

    public function removeData($clientId, array $list)
    {
        $primaryIdName = $this->dbPrimaryKey();
        $bulk = [];
        foreach ($list as $item) {
            $id = is_numeric($item) ? $item : $item[$primaryIdName];
            $bulk[] = [
                'delete' => [
                    '_index'   => static::index(),
                    '_type'    => static::type(),
                    '_id'      => $id,
                    '_routing' => $clientId,
                ]
            ];
        }
        $params = [
            'index' => static::index(),
            'type'  => static::type(),
            'body'  => $bulk
        ];

        $result = self::model()->getDbConnection()->bulk($params);
        self::model()->getDbConnection()->indices()->refresh(['index' => static::index()]);

        return $result['items'] ?? [];
    }

    public function indexData($clientId, array $list, $strip = true, $fields = [])
    {
        if (empty($list)) {
            return [];
        }
        $primaryIdName = $this->dbPrimaryKey();
        $formatter = $this->getFormatter($clientId, true);
        $formatter->setNeedStrip($strip);
        $bulk = [];
        if (!empty($fields)) {
            $formatter->setIndexFields($fields);
        }
        $formatter->setListData($list);
        $result = $formatter->result();
        foreach ($result as $indexData) {
            if (!empty($indexData)) {
                $bulk[] = [
                    'index' => [
                        '_index'   => static::index(),
                        '_type'    => static::type(),
                        '_routing' => $clientId,
                        '_id'      => $indexData[$primaryIdName],
                    ]
                ];
                unset($indexData[$primaryIdName]);
                $bulk[] = empty($fields) ? $indexData : ['doc' => $indexData];
            }
        }

        $params = [
            'index' => static::index(),
            'type'  => static::type(),
            'body'  => $bulk
        ];

        $result = self::model()->getDbConnection()->bulk($params);
        self::model()->getDbConnection()->indices()->refresh(['index' => static::index()]);

        return $result['items'] ?? [];
    }

    public function partialIndexData($clientId, $originData, $updateData)
    {
        $indexData = [];
        foreach ($originData as $updateItem) {
            $indexData[] = array_merge([
                $this->dbPrimaryKey() => $updateItem[$this->dbPrimaryKey()],
            ], $updateData);
        }
        return $this->indexData($clientId, $indexData, false, static::indexKeysByDbKeys(array_keys($updateData)));
    }

    abstract public static function dbPrimaryKey();

    /**
     *
     * @return ESFormatterTrait::class
     */
    abstract public function getFormatterName();

    /**
     * @param      $clientId
     * @param bool $withPrimaryKey
     *
     * @return ESFormatterTrait | \ListItemFormatter
     */
    protected function getFormatter($clientId, $withPrimaryKey = false)
    {
        $formatterClass = $this->getFormatterName();
        $formatter = new $formatterClass($clientId);
        $formatter->setRenderForIndex(true, $withPrimaryKey);
        return $formatter;
    }

    /**
     * @return array [ es_field => db_field ]
     */
    abstract public static function sourceMapping();

    public static function indexKeysByDbKeys($dbKeys)
    {
        $map = array_flip(static::sourceMapping());
        $dbKeys = is_array($dbKeys) ? $dbKeys : [$dbKeys];
        $keys = [];
        foreach ($map as $db => $es) {
            if (in_array($db, $dbKeys)) {
                $keys[] = $es;
            }
        }
        return $keys;
    }

    public static function dbKeyByIndexKey($esKeys)
    {
        $map = static::sourceMapping();
        $esKeys = is_array($esKeys) ? $esKeys : [$esKeys];
        $keys = [];
        foreach ($map as $es => $db) {
            if (in_array($es, $esKeys)) {
                $keys[] = $db;
            }
        }
        return $keys;
    }
}