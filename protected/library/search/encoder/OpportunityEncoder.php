<?php

namespace common\library\search\encoder;

use common\library\util\PgsqlUtil;
use common\library\util\SqlBuilder;

class OpportunityEncoder extends SearchEncoder
{

    public function getReferType()
    {
        return \Constants::TYPE_OPPORTUNITY;
    }

    public function getTableName()
    {
        return 'tbl_opportunity';
    }

    /*protected function init()
    {
        parent::init();
        $this->fields = array_filter($this->fields, function ($item) {
            return !in_array($item,['company_name','customer_list']);
        });
    }*/

    public function buildParams()
    {
        $sql = "client_id={$this->clientId}";
        $params = [];

        SqlBuilder::buildIntWhere('', 'enable_flag', $this->filterDelete ? 0 : 1, $sql, $params);

        if ($this->ids) {
            SqlBuilder::buildIntWhere('', 'opportunity_id', $this->ids, $sql, $params);
        } else {
            if (!$this->refreshAll) {
                $sql = '0=1';
                return [$sql, $params];
            }
        }

        return [$sql, $params];
    }

    public function buildBulkingParams()
    {
        $sql = '';
        $params = [];
        if ($this->bulkingDate) {
            $sql .= "(update_time >= '{$this->bulkingDate}')";
        }

        return [$sql, $params];
    }

    /*
    public function result($nestedList = [])
    {
        $result = [];
        //商机主键
        //获取company_name,商机的customer_list
        $primaryKey = $this->getPrimaryKey();

        $companyIds = [];
        $customerIds = [];
        $opportunityCompanyMap = [];
        $opportunityCustomerMap = [];
        foreach($this->dataList as $k => $value) {
            $value[$primaryKey] = $value[$primaryKey] ?? $k;
            if (isset($value['company_id']) && $value['company_id'] > 0) {
                $companyIds[] = $value['company_id'];
                $opportunityCompanyMap[$value[$primaryKey]] = $value['company_id'];
            }
            if (isset($value['customer_id']) && (is_string($value['customer_id']) && (str_starts_with($value['customer_id'], '{') || str_starts_with($value['customer_id'], '[')))) {
                $value['customer_id'] = PgsqlUtil::trimArray($value['customer_id']) ?? [];
                $customerIds = array_merge($customerIds, empty($value['customer_id']) ? [] : $value['customer_id']);
                $opportunityCustomerMap[$value[$primaryKey]] = $value['customer_id'];
            }
        }

        $adminUserId =  \common\library\privilege_v3\PrivilegeService::getInstance($this->clientId)->getAdminUserId();
        $companyIds = array_values(array_unique($companyIds));
        $list = new CompanyList($adminUserId);
        $list->setSkipPrivilege(true);
        $list->setIsArchive(null);
        $list->setCompanyIds($companyIds);
        $list->getFormatter()->setSpecifyFields(['company_id', 'name']);
        $companyList = $list->find();
        $companyMap = array_column($companyList,null,'company_id');

        $customerIds = array_values(array_unique($customerIds));
        $list = new CustomerList($this->clientId);
        $list->setCustomerId($customerIds);
        $list->setIsArchive(null);
        $list->getFormatter()->setSpecifyFields(['customer_id', 'name', 'email']);
        $customerList = $list->find();
        $customerMap = array_column($customerList,null,'customer_id');

        foreach ($this->dataList as $k => $value) {
            $value[$primaryKey] = $value[$primaryKey] ?? $k;
            if ($this->nestedParentKey) {
                $result[$value[$this->nestedParentKey]][] = $this->encode($value);
            } else {
                foreach ($nestedList as $path => $nestedData) {
                    $value[$path] = array_values($nestedData['data'][$value[$nestedData['key']]] ?? []);
                }
                $value['company_name'] = $companyMap[$opportunityCompanyMap[$value[$primaryKey]] ?? 0]['name'] ?? '';
                $value['customer_list'] = [];
                foreach ($opportunityCustomerMap[$value[$primaryKey]] as $customerId) {
                    if (isset($customerMap[$customerId])) {
                        $value['customer_list'][] = $customerMap[$customerId];
                    }
                }

                $result[$value[$primaryKey]] = $this->encode($value);
            }
        }

        return $result;
    }*/

    protected function encodeField($data, $key)
    {
        switch ($key) {
            case 'user_id':
            case 'handler':
            case 'customer_id':
                if (is_string($data[$key]) && (str_starts_with($data[$key], '{') || str_starts_with($data[$key], '['))) {
                    $result = PgsqlUtil::trimArray($data[$key]) ?? $data[$key];
                } else {
                    $result = $data[$key];
                }
                break;
            default:
                $result = isset($data[$key]) ? (is_string($data[$key]) ? trim($data[$key]) : $data[$key]) : null;
                break;
        }

        return $result;
    }
}