<?php

namespace common\library\search\builder;

use common\library\search\metadata\LeadMetadata;
use common\library\search\query\SearchQuery;
use common\library\search\SearchConstant;
use common\library\workflow\WorkflowConstant;

class LeadFieldBuilder extends FieldBuilder {
    
    
    public function getCustomBuilder() {
        
        switch ($this->fieldId) {
            case SearchConstant::SCENE_TOP_SEARCH:
                $allFields = $this->valueType;
                $matchType = $this->operator ?: SearchConstant::MULTI_FIELDS_TYPE_BEST_FIELDS;
                $keyword = $this->value;
                preg_match('/(\d+)/', $keyword, $matchNumeric);
                
                $isEmail = filter_var($keyword, FILTER_VALIDATE_EMAIL) !== false;
                $isEmailDomain = !$isEmail && preg_match('/^(?!www)@?(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?$/', $keyword);
                $isUrl = $isEmailDomain || filter_var($keyword, FILTER_VALIDATE_URL) !== false || preg_match("/^www\.[\w\.\-]+$/", $keyword);
                
                if (!$matchNumeric) {
                    $allFields = array_values(array_diff($allFields, [
                        'customer_list.tel',
                        'tel',
                    ]));
                } else {
                    if (in_array('customer_list.tel', $allFields)) {
                        $allFields[] = 'customer_list.tel.numeric';
                    }
                    if (in_array('tel', $allFields)) {
                        $allFields[] = 'tel.numeric';
                    }
                    if (in_array('customer_list.contact.value', $allFields)) {
                        $allFields[] = 'customer_list.contact.value.tel';
                    }
                    if (in_array('customer_list.contact.value.tel', $allFields)) {
                        $allFields[] = 'customer_list.contact.value.keyword';
                    }
                }
                
                if (empty($allFields)) {
                    return null;
                }
                
                if (!array_diff($allFields, ['name', 'short_name'])) {
                    $matchType = WorkflowConstant::FILTER_OPERATOR_MATCH;
                }
                
                $boostSettingKey = 'default';
                $analyzer = null;
                //搜索全部
                if (count($allFields) > 1) {
                    if ($isEmail) {
                        //搜索全部时，识别keyword为邮箱时只查询name、email字段
                        $boostSettingKey = 'is_email';
                        $allFields = array_intersect([
                            'name',
                            'customer_list.email.domain',
                            'customer_list.contact.value',
                        ], $allFields);
                        $keyword = strtolower($keyword);
                        $analyzer = SearchConstant::BUILD_IN_ANALYZER_KEYWORD;
                    } elseif ($isEmailDomain) {
                        // 精确时不转义
                        $boostSettingKey = 'is_email';
                        $keyword = str_replace('@', '', $keyword);
                        $analyzer = SearchConstant::BUILD_IN_ANALYZER_KEYWORD;
                    } elseif ($isUrl) {
                        $boostSettingKey = 'is_url';
                    }
                    
                    if (in_array($matchType, [SearchConstant::MULTI_FIELDS_TYPE_BEST_FIELDS])) {
                        $allFields = $this->boostField($boostSettingKey, $allFields);
                    }
                    $fieldsPathMap = SearchQuery::instance(LeadMetadata::class)->collapseFieldsByPath($allFields);
                    $builder = new BooleanBuilder();
                    $flatFields = $allFields;
                    
                    if (in_array($matchType, [SearchConstant::MULTI_FIELDS_TYPE_PHRASE_PREFIX])) {
                        $realNestedFieldList = [];
                        foreach ($fieldsPathMap as $path => $nestedFields) {
                            foreach ($nestedFields as $nestedField) {
                                $realNestedField = $this->getKeywordField($nestedField, $path);
                                if (in_array($realNestedField, $realNestedFieldList)) {
                                    continue;
                                }
                                $realNestedFieldList[] = $realNestedField;
                                $builder->should((new NestedQueryBuilder($path, (new PrefixBuilder($realNestedField, $keyword))))->setInnerHit((bool)$this->highlight, $this->highlight, $realNestedField));
                            }
                            $flatFields = array_diff($flatFields, $nestedFields);
                        }
                        if ($flatFields) {
                            foreach ($flatFields as $flatField) {
                                $builder->should((new PrefixBuilder($this->getKeywordField($flatField), $keyword)));
                            }
                        }
                        $builder->minimumShouldMatch(1);
                    } else {
                        if (WorkflowConstant::FILTER_OPERATOR_MATCH == $matchType) {
                            foreach ($flatFields as $flatField) {
                                $builder->should(SearchQuery::instance(LeadMetadata::class)->translateToBuilder($flatField, $keyword, $matchType));
                            }
                        } else {
                            foreach ($fieldsPathMap as $path => $nestedFields) {
                                $builder->should((new NestedQueryBuilder($path, (new MultiMatchBuilder($nestedFields, $keyword, $matchType))->setAnalyzer($analyzer)))->setInnerHit((bool)$this->highlight, $this->highlight));
                                $flatFields = array_diff($flatFields, $nestedFields);
                            }
                            if ($flatFields) {
                                $builder->should(new MultiMatchBuilder($flatFields, $keyword, $matchType));
                            }
                        }
                        $builder->minimumShouldMatch(1);
                    }
                    return $builder;
                } else {
                    $matchType = WorkflowConstant::FILTER_OPERATOR_MATCH;
                    //指定字段
                    $searchField = current($allFields);
                    switch ($searchField) {
                        case 'customer_list.tel':
                            $keyword = trim(preg_replace('/\D/', ' ', $keyword));
                            break;
                        case 'customer_list.email':
                            if ($isEmail || $isEmailDomain) {
                                $matchType = WorkflowConstant::FILTER_OPERATOR_EQUAL;
                            }
                            break;
                        case 'customer_list.contact.value':
                            break;
                        case 'customer_list.email.domain':
                            $keyword = explode('@', $keyword)[1] ?? $keyword;
                            $matchType = WorkflowConstant::FILTER_OPERATOR_EQUAL;
                            $searchField = 'customer_list.email';
                            break;
                    }
                    return SearchQuery::instance(LeadMetadata::class)->translateToBuilder($searchField, $keyword, $matchType);
                }
        }
    }
    
    protected function getKeywordField($field, $path = '') {
        
        if ($path) {
            $field = str_starts_with($field, $path . '.') ? substr($field, strlen($path) + 1, strlen($field)) : $field;
        }
        if ($field == 'serial_id') {
            return 'serial_id';
        } elseif (in_array($field, ['email.domain', 'email'])) {
            return 'email.domain';
        } else {
            return $field . '.keyword';
        }
    }
    
    protected function boostField($boostSettingKey, $fields) {
        
        $boostFieldMap = [
            'is_email' => [
                'customer_list.contact.value' => '^4',
                'homepage'                    => '^4',
                'customer_list.email'         => '^4',
            ],
            'is_url'   => [
                'customer_list.contact.value' => '^2',
                'homepage'                    => '^2',
                'customer_list.email'         => '^2',
            ],
            'default'  => [
                'name'               => '^2',
                'customer_list.name' => '^2',
            ],
        ];
        
        foreach ($fields as &$field) {
            $field = $field . ($boostFieldMap[$boostSettingKey][$field] ?? '');
        }
        
        return $fields;
    }
}