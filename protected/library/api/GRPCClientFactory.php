<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2019/8/22
 * Time: 9:56 AM
 */

namespace common\library\api;


use common\library\api\components\StandardCallInvoker;
use Grpc\BaseStub;
use Grpc\ChannelCredentials;
use Infra\Ai\Domain\SemInfoExtractServiceClient;
use Infra\Ai\Recommend\V2\RecommendServiceClient;
use Infra\Ai\Tag\AutomatedKeywordServiceClient;
use Infra\Common\Whois\WhoisServiceClient;
use Infra\Unifiedspider\Core\DomainServiceClient;
use Infra\Unifiedspider\Core\EmailSearchServiceClient;

class GRPCClientFactory
{
    /**
     * @param $clientClass
     * @param $options
     * @return object|BaseStub
     */
    public static function createClient($clientClass, array $options=[])
    {
        $reflection = new \ReflectionClass($clientClass);
        if( !$reflection->isSubclassOf(BaseStub::class))
        {
            throw new \ProcessException('class error');
        }
        $serviceName= strtolower(str_replace('\\','.',$clientClass));
        if( $pos = strripos($serviceName, 'client'))
            $serviceName = substr($serviceName,0, $pos);

        $config = \Yii::app()->params['inner_api']['grpc'][$serviceName]??[];
        if( empty($config) )
        {
            throw new \ProcessException('config empty');
        }

        $urlArr = parse_url($config['host']);
        if( isset($urlArr['host']) )
        {
            $config['host'] = $urlArr['host'];
        }

        $hostname= $config['host'].':'.$config['port'];
        $defaultOptions = [
            'credentials' => ChannelCredentials::createInsecure()
        ];
        $options = array_merge($defaultOptions, $config['options']??[],$options);

        $options['grpc_call_invoker'] = new StandardCallInvoker();

        $channel = null;

        $client = $reflection->newInstance($hostname,$options, $channel);

        return $client;
    }

    /**
     * @param array $options
     * @return BaseStub|RecommendServiceClient
     */
    public static function recommendServiceClient(array $options=[])
    {
        $clientClass = RecommendServiceClient::class;
       return  self::createClient($clientClass, $options);
    }

    /**
     * @param array $options
     * @return BaseStub|DomainServiceClient
     * @throws \ProcessException
     */
    public static function DomainServiceClient(array $options = [])
    {
        $clientClass = DomainServiceClient::class;
        return  self::createClient($clientClass, $options);
    }


    /**
     * @param array $options
     * @return BaseStub|AutomatedKeywordServiceClient
     * @throws \ProcessException
     */
    public static function AutomatedKeywordClient(array $options = [])
    {
        $clientClass = AutomatedKeywordServiceClient::class;
        return  self::createClient($clientClass, $options);
    }

    /**
     * @param array $options
     * @return BaseStub|whoisServiceClient
     * @throws \ProcessException
     */
    public static  function whoisServiceClient(array $options=[])
    {
        $clientClass = whoisServiceClient::class;
        return  self::createClient($clientClass, $options);
    }

    /**
     * @param array $options
     * @return BaseStub|EmailSearchServiceClient
     * @throws \ProcessException
     */
    public static function digEmailsClient(array $options = [])
    {
        $clientClass = EmailSearchServiceClient::class;
        return  self::createClient($clientClass, $options);
    }

    /**
     * @param array $options
     * @return BaseStub|object|SemInfoExtractServiceClient
     * @throws \ProcessException
     */
    public static function semInfoExtractServiceClient(array $options =[])
    {
        $clientClass = SemInfoExtractServiceClient::class;
        return  self::createClient($clientClass, $options);
    }

}
