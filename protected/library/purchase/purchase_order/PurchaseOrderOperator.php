<?php
/**
 * Created by PhpStorm.
 * User: beenzhang
 * Date: 2021/3/8
 * Time: 2:30 PM
 */

namespace common\library\purchase\purchase_order;


use common\components\BaseObject;
use common\library\approval_flow\signal\interrupt\PurchaseOrderChangeHandlerInterrupt;
use common\library\approval_flow\signal\interrupt\PurchaseOrderChangeStatusInterrupt;
use common\library\approval_flow\traits\OperateTriggerApproval;
use common\library\history\base\Builder;
use common\library\history\purchase_order\BatchPurchaseOrderBuilder;
use common\library\object\field\service\ScopeUserFieldService;
use common\library\object\object_define\post_action\common\HistoryAction;
use common\library\invoice\trigger\RefreshOrderHasPartialPurchaseTrigger;
use common\library\oms\common\OmsField;
use common\library\oms\order_link\trigger\PurchaseOrderTrigger;
use common\library\oms\order_profit\trigger\PurchaseInvoiceProductTrigger;
use common\library\oms\order_profit\trigger\PurchaseOrderProductAmountTrigger;
use common\library\oms\order_profit\trigger\TaxRefundAmountTrigger;
use common\library\oms\payable_invoice\PayableInvoiceApi;
use common\library\oms\payment_invoice\PaymentInvoiceApi;
use common\library\oms\payable_invoice\PayableInvoice;
use common\library\oms\payable_invoice\PayableInvoiceFilter;
use common\library\oms\product_transfer\purchase\relation\PurchaseProductTransferRelationFilter;
use common\library\oms\product_transfer\traits\RelationUpdateTransferTrait;
use common\library\orm\pipeline\operator\HistoryOperatorTask;
use common\library\history\purchase_order\PurchaseOrderSetting;
use common\library\invoice\status\InvoiceStatusService;
use common\library\orm\pipeline\operator\UpdateIndexTask;
use common\library\privilege_v3\field\PrivilegeFieldService;
use common\library\privilege_v3\Helper;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\purchase\purchase_order_product\PurchaseOrderProduct;
use common\library\purchase\purchase_order_product\PurchaseOrderProductFilter;
use common\library\recycle\Recycle;
use common\library\setting\library\order_profit\tax_refund\TaxRefundApi;
use common\library\setting\library\order_profit\tax_refund\TaxRefundConstants;
use common\library\workflow\trigger\WorkflowBatchOperatorTrait;
use xiaoman\orm\common\OperatorV2;
use xiaoman\orm\database\data\Equal;
use xiaoman\orm\database\data\In;
use common\library\approval_flow\Builder as  ApprovalBuilder;
use common\library\approval_flow\setting\purchase_order\PurchaseOrderSetting as  PurchaseOrderApprovalSetting;
/**
 * Class PurchaseOrderOperator
 * @package common\library\purchase\purchase_order
 */
class PurchaseOrderOperator extends OperatorV2
{
    use RelationUpdateTransferTrait;
    use InitPurchaseOrderMetadata;
    use OperateTriggerApproval;
    use WorkflowBatchOperatorTrait;


    /**
     * @var PurchaseOrder $object
     */
    protected $object;

    protected $oldListData = [];
    protected $listData = [];
    protected $failOrderIds = [];
    protected $succedOrderIds = [];
    protected $failOrder = [];

    protected $refreshAmount = false; //重算基础金额字段

    const TASK_LIST = [
        'history' => [
            'task_class' => HistoryOperatorTask::class
        ],
        'after_delete' => [
            'require_fields' => ['purchase_order_id'],
            'method' => 'afterDelete',
        ],
        'after_recover' => [
            'require_fields' => ['purchase_order_id'],
            'method' => 'afterRecover',
        ],
        'update_index' => [
            'require_fields' => ['purchase_order_id'],
            'module_type' => \Constants::TYPE_PURCHASE_ORDER,
            'task_class' => UpdateIndexTask::class,
        ],
    ];

    const POST_ACTIONS = [
        HistoryAction::class => true,
//        ...
    ];
    
    public function getWorkflowReferType()
    {
        return \Constants::TYPE_PURCHASE_ORDER;
    }
    
    public function buildBatchHistory($clientId, $userId, $historyType, $map)
    {
        $history = new BatchPurchaseOrderBuilder();
        if ($this->editReferId) {
            $history->setEditInfo($this->editReferType, null, $this->editReferId);
            $userId = 0;
        }
        $history->buildByMap($clientId, $userId, $map, $historyType);
    }

    /**
     * @param bool $refreshAmount
     */
    public function setRefreshAmount(bool $refreshAmount): void
    {
        $this->refreshAmount = $refreshAmount;
    }

    /**
     * @return array
     */
    public function getFailOrderIds(): array
    {
        return $this->failOrderIds;
    }

    /**
     * @return array
     */
    public function getSuccedOrderIds(): array
    {
        return $this->succedOrderIds;
    }

    /**
     * @return array
     */
    public function getFailOrder(): array
    {
        return $this->failOrder;
    }

    public function addAttachments($opUserId, array $fileIds)
    {
        $this->onlySingle();

        $opUser = $this->object->getDomainHandler();
        $data = $this->get(['handler','attachments','purchase_order_id', 'scope_user_ids']);

        if (!Helper::hasPermission($opUser->getClientId(), $opUser->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_EDIT)
            || !Helper::canManageAnyUsers($opUser->getClientId(), $opUser->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_EDIT,$data['handler'])
        )
        {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'),\ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
        }

        if(!\common\library\privilege_v3\Helper::canOperateRecord($opUser->getClientId(), $opUser->getUserId(), $data['scope_user_ids'], PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_EDIT)) {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'));
        }
        
        $fileList = [];
        foreach ($fileIds as $fileId) {
            $file = \UploadFile::findByFileId($fileId);
            if (!$file) {
                throw new \RuntimeException(\Yii::t('file', 'Failed to add file, file does not exist or has been deleted') . ',file_id:' . $fileId);
            }
            $fileList[] = [
                'user_id' => $opUserId,
                'file_id' => $fileId,
                'create_time' => date('Y-m-d H:i:s'),
            ];
        }

        $attachments = array_merge($data['attachments'], $fileList);
        $updateData = [
            'attachments' => array_values($attachments),
            'modifier' => $this->object->getDomainHandler()->getUserId(),
            'update_time' => date('Y-m-d H:i:s')
        ];
        $return = $this->execute($updateData);

        (new Builder(new PurchaseOrderSetting()))
            ->setType(PurchaseOrderSetting::TYPE_ADD_ATTACHMENTS)
            ->setClientId($this->object->getDomainHandler()->getClientId())
            ->setUpdateUser($this->object->getDomainHandler()->getUserId())
            ->initFromRawData($updateData)
            ->setOldAttributes($data)
            ->build();

        return $return;
    }

    public function removeAttachments(array $fileIds)
    {
        $this->onlySingle();

        $opUser = $this->object->getDomainHandler();
        $data = $this->get(['handler','attachments','purchase_order_id', 'scope_user_ids']);

        if (!Helper::hasPermission($opUser->getClientId(), $opUser->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_EDIT)
            || !Helper::canManageAnyUsers($opUser->getClientId(), $opUser->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_EDIT,$data['handler'])
        )
        {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'),\ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
        }

        if(!\common\library\privilege_v3\Helper::canOperateRecord($opUser->getClientId(), $opUser->getUserId(), $data['scope_user_ids'], PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_EDIT)) {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'));
        }

        $flag = false;
        $attachments = $data['attachments']??'[]';
        $attachments = is_array($attachments) ? $attachments : json_decode($attachments, true);

        foreach ($attachments as $key => $item) {
            if (in_array($item['file_id'], $fileIds)) {
                unset($attachments[$key]);
                $flag = true;
            }
        }

        if ($flag) {
            $attachments = array_values($attachments);
            $updateData = [
                'attachments' => $attachments,
                'modifier' => $this->object->getDomainHandler()->getUserId(),
                'update_time' => date('Y-m-d H:i:s')
            ];

            $return = $this->execute($updateData);

            (new Builder(new PurchaseOrderSetting()))
                ->setType(PurchaseOrderSetting::TYPE_REMOVE_ATTACHMENTS)
                ->setClientId($this->object->getDomainHandler()->getClientId())
                ->setUpdateUser($this->object->getDomainHandler()->getUserId())
                ->initFromRawData($updateData)
                ->setOldAttributes($data)
                ->build();

            return $return;
        }
    }

    /**
     * @param $opUserId
     * @param array $fileIds
     * @return int
     */
    public function setAttachments($opUserId, array $fileIds)
    {
        $this->onlySingle();

        $opUser = $this->object->getDomainHandler();
        $data = $this->get(['handler']);

        if (!Helper::hasPermission($opUser->getClientId(), $opUser->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_EDIT)
            || !Helper::canManageAnyUsers($opUser->getClientId(), $opUser->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_EDIT,$data['handler'])
        )
        {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'),\ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
        }

        $fileList = [];
        foreach ($fileIds as $fileId) {
            $file = \UploadFile::findByFileId($fileId);
            if (!$file) {
                throw new \RuntimeException(\Yii::t('file', 'Failed to add file, file does not exist or has been deleted') . ',file_id:' . $fileId);
            }
            $fileList[] = [
                'user_id' => $opUserId,
                'file_id' => $fileId,
                'create_time' => date('Y-m-d H:i:s'),
            ];
        }

        $fileList = array_values($fileList);
        return $this->execute(['attachments' => $fileList]);
    }

    public function setStatus($statusId)
    {
        $this->onlySingle();

        $purchaseOrderData = $this->get(['client_id','purchase_order_id','supplier_id','status','handler','purchase_order_no']);

        $opUser = $this->object->getDomainHandler();

        if ((!Helper::hasPermission($opUser->getClientId(), $opUser->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_EDIT)
                && !Helper::hasPermission($opUser->getClientId(), $opUser->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_CHANGE))
            || (!Helper::canManageAnyUsers($opUser->getClientId(), $opUser->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_EDIT,$purchaseOrderData['handler'])
                && !Helper::canManageAnyUsers($opUser->getClientId(), $opUser->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_CHANGE,$purchaseOrderData['handler']))
        )
        {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'),\ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
        }

        if ($purchaseOrderData['status'] == $statusId)
            return 1;

        $oldStatus = $purchaseOrderData['status'];

        $statusService = new InvoiceStatusService($this->object->getClientId(), \Constants::TYPE_PURCHASE_ORDER);

        if (!$statusService->isStatusExist($statusId))
            throw new \RuntimeException(\Yii::t('status','状态不存在'));

        $updateData = [
            'status' => $statusId,
            'modifier' => $this->object->getDomainHandler()->getUserId(),
            'update_time' => date('Y-m-d H:i:s')
        ];

        //审批
        $approveBuilder = new ApprovalBuilder($this->object->getDomainHandler(),new PurchaseOrderApprovalSetting());
        $approveBuilder->wakeupParams([
            'status_id' => $statusId,
        ])->initFormSetValue(['status' => $statusId])->oldAttributes($this->get((new PurchaseOrderApprovalSetting())->approvalFiled(true)),false)->signalClass(PurchaseOrderChangeStatusInterrupt::class)->build();


        $rows = $this->execute($updateData);

        //操作历史
        $historyBuilder = new Builder(new PurchaseOrderSetting());
        $historyBuilder->setClientId($this->object->getClientId())->setType(PurchaseOrderSetting::TYPE_EDIT_STATUS)
            ->setUpdateUser($this->object->getDomainHandler()->getUserId())
            ->setUpdateType($this->editReferType ?? 0)
            ->setUpdateRefer($this->editReferId ?? 0)
            ->initFromRawData($updateData)
            ->setOldAttributes($purchaseOrderData)
            ->build();

//        //"已确认"状态不一致时触发重算订单环节状态
//        $isDiffEndingStatus = false;
//        if($statusService->isEndingStatus($statusId) !== $statusService->isEndingStatus($oldStatus)){
//            $isDiffEndingStatus = true;
//        }

        //"草稿"状态变更触发重算订单产品毛利
        $isEditBeginStatus = false;
        $draftStatusId = $statusService->beginStatus()['id'];
        if ($statusId == $draftStatusId || $oldStatus == $draftStatusId) {
            $isEditBeginStatus = true;
        }

        $purchaseProductFilter = new PurchaseOrderProductFilter($this->object->getDomainHandler()->getClientId());
        $purchaseProductFilter->select(['order_id']);
        $purchaseProductFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $purchaseProductFilter->purchase_order_id = $purchaseOrderData['purchase_order_id'];
        $purchaseProduct = $purchaseProductFilter->rawData();

        $this->triggerReferOrderLink(array_column($purchaseProduct, 'order_id'));
        $this->triggerReferPurchaseProductTransferInvoice([$purchaseOrderData]);

        if($isEditBeginStatus) {
                //产品毛利埋点：采购单新变更草稿状态，触发刷新产品毛利
                $this->triggerReferInvoiceProduct(array_column($purchaseProduct,'order_id'));

                //退税金额埋点：采购单新变更草稿状态，触发刷新退税金额
                $this->triggerReferTaxRefund(array_column($purchaseProduct,'order_id'));
        }
        
        $this->runWorkflow($this->object->getClientId(), $this->object->getObjectId(), array_keys($updateData));

        return $rows;
    }

    public function batchSetStatus($statusId)
    {
        $statusService = new InvoiceStatusService($this->object->getClientId(), \Constants::TYPE_PURCHASE_ORDER);

        if (!$this->object->getStatusService()->isStatusExist($statusId))
            throw new \RuntimeException(\Yii::t('status', '状态不存在'));

        $this->listData = $this->get(['client_id', 'purchase_order_id', 'purchase_order_no', 'status', 'handler', 'modifier']);

        $opUser = $this->object->getDomainHandler();
        if (!Helper::hasPermission($opUser->getClientId(), $opUser->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_BATCH_SET_STATUS))
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'),\ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_MANAGE);

        //只能修改下属单据
        $this->checkInvoiceEditPermission();
        //判断状态流转流程
        $this->checkStatusProcess($statusId);

        //审批流中断
        if (!PrivilegeService::getInstance($this->clientId)->checkFunctional(PrivilegeConstants::FUNCTIONAL_DISABLE_APPROVAL))
            $this->approverFlowInterrupt(['status' => $statusId], PurchaseOrderChangeStatusInterrupt::class);

        if (empty($this->listData))
            return 0;

        //批量修改数据
        $this->batchUpdateStatus($statusId);
        $this->refreshOrderLinkByStatusChange($statusId);
        $this->refreshInvoiceProductByStatusChange($statusId);
        //History
        $historyBuilder = new Builder(new PurchaseOrderSetting());
        $historyBuilder->setClientId($this->object->getClientId())->setType(PurchaseOrderSetting::TYPE_EDIT_STATUS)
                       ->setUpdateUser($this->object->getDomainHandler()->getUserId())
                       ->initFromSetValue(['status' => $statusId])
                       ->setOldAttributes(array_values($this->listData))
                       ->build();
        $this->succedOrderIds = array_column($this->listData, 'purchase_order_id');
        //执行工作流
        $this->runWorkflow($this->object->getClientId(), $this->succedOrderIds, ['status']);
    }

    /**
     * 校验单据编辑权限
     * @return void
     */
    public function checkInvoiceEditPermission()
    {
        $opUser = $this->object->getDomainHandler();
        foreach ($this->listData as $k => $listDatum) {
            try {
                //单据权限校验
                if (
                    (!Helper::hasPermission($opUser->getClientId(), $opUser->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_EDIT)
                        && !Helper::hasPermission($opUser->getClientId(), $opUser->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_CHANGE))
                    || (!Helper::canManageAnyUsers($opUser->getClientId(), $opUser->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_EDIT, $listDatum['handler'])
                        && !Helper::canManageAnyUsers($opUser->getClientId(), $opUser->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_CHANGE, $listDatum['handler']))
                ) {
                    $this->setFailOrderMessage(\common\library\invoice\batch\Constant::MISSING_PERMISSION_TYPE, $listDatum['purchase_order_id'], \Yii::t('privilege', 'Missing permission'));
                }
            } catch (\Exception $e) {
                unset($this->listData[$k]);
                $this->failOrderIds[] = $listDatum['purchase_order_id'];
            }
        }
    }

    /**
     * 校验状态流转
     * @param $statusId
     * @return false|void
     */
    public function checkStatusProcess($statusId)
    {
        $statusInfo = $this->object->getStatusService()->list();
        if (empty($statusInfo)) {
            return false;
        }
        $nextStatus = array_column($statusInfo, 'next_status', 'id');

        foreach ($this->listData as $k => $listDatum) {
            try {
                //状态流转校验
                if ($listDatum['status'] == $statusId) {
                    $this->setFailOrderMessage(\common\library\invoice\batch\Constant::STATUS_PROCESS_TYPE, $listDatum['purchase_order_id'], '采购订单不符合状态流转规则');
                }

                if (!in_array($statusId, $nextStatus[$listDatum['status']])) {
                    $this->setFailOrderMessage(\common\library\invoice\batch\Constant::STATUS_PROCESS_TYPE, $listDatum['purchase_order_id'], '采购订单不符合状态流转规则');
                }
            } catch (\Exception $e) {
                unset($this->listData[$k]);
                $this->failOrderIds[] = $listDatum['purchase_order_id'];
            }
        }
    }

    public function setFailOrderMessage($failType, $invoiceId, $message = '')
    {
        if (!array_key_exists($failType, $this->failOrder)) {
            $this->failOrder[$failType] = [
                'message' => \Yii::t('purchase_order', \common\library\invoice\batch\Constant::BATCH_FAIL_PURCHASE_ORDER_MESSAGE_MAP[$failType]),
                'purchase_order_ids' => [$invoiceId]
            ];
        } else {
            $this->failOrder[$failType]['purchase_order_ids'][] = $invoiceId;
        }
        \LogUtil::info('client_id[' . $this->clientId . ']purchase_order_id[' . $invoiceId . ']批量修改采购订单失败，失败原因：' . $message);
        throw new \RuntimeException($message);
    }

    /**
     * 审批流中断
     * @param $params
     * @param $interruptClass
     * @param $isSetSameValue
     * @return void
     */
    public function approverFlowInterrupt($params, $interruptClass, $isSetSameValue = true)
    {
        if (empty($this->listData)) {
            return;
        }
        //审批
        $approveBuilder = new ApprovalBuilder(\User::getLoginUser(), new PurchaseOrderApprovalSetting());
        $approveBuilder->openForApprovalFlow();
        !$isSetSameValue && $approveBuilder->isSetSameValue($isSetSameValue);
        $approveBuilder->wakeupParams([])->initFormSetValue($params)->oldAttributes($this->listData)->signalClass($interruptClass)->interruptScene()->build();
        $approvingResult = $approveBuilder->getApprovingResult();
        $interruptResult = $approveBuilder->getInterruptResult();
        foreach ($this->listData as $k => $listDatum) {
            try {
                //审批流校验
                if (isset($approvingResult[$listDatum['purchase_order_id']]) && $approvingResult[$listDatum['purchase_order_id']]) {
                    $this->setFailOrderMessage(\common\library\invoice\batch\Constant::APPROVAL_FLOW_TYPE, $listDatum['purchase_order_id'], '采购订单处于审批中');
                }
                if (isset($interruptResult[$listDatum['purchase_order_id']]) && $interruptResult[$listDatum['purchase_order_id']]) {
                    $this->setFailOrderMessage(\common\library\invoice\batch\Constant::APPROVAL_FLOW_TYPE, $listDatum['purchase_order_id'], '采购订单审批流中断');
                }
            } catch (\Exception $e) {
                unset($this->listData[$k]);
                $this->failOrderIds[] = $listDatum['purchase_order_id'];
            }
        }
    }

    /**
     * 批量更新采购订单状态值
     * @param $statusId
     * @return mixed
     * @throws \ProcessException
     */
    public function batchUpdateStatus($statusId)
    {
        $params = [
            ':status'      => $statusId,
            ':modifier'    => $this->object->getDomainHandler()->getUserId(),
            ':update_time' => date('Y-m-d H:i:s'),
        ];
        $idSql = implode(',', array_column($this->listData, 'purchase_order_id'));
        $sql = "update tbl_purchase_order set status=:status, modifier=:modifier, update_time=:update_time where client_id = " . $this->clientId . " and purchase_order_id in (" . $idSql . ")";
        $db = \PgActiveRecord::getDbByClientId($this->clientId);
        $count = $db->createCommand($sql)->execute($params);
        \LogUtil::info('client_id[' . $this->clientId . ']批量修改采购订单purchase_order_id[' . $idSql . ']成功' . $count . '条');

        return $count;
    }

    /**
     * 状态更新后重算订单环节状态
     * @param $statusId
     * @return true|void
     */
    public function refreshOrderLinkByStatusChange($statusId)
    {
        $refreshObjectId = [];
        $statusService = $this->object->getStatusService();
        foreach ($this->listData as $k => $listDatum) {
            //"已确认"状态不一致时触发重算订单环节状态
            if($statusService->isEndingStatus($statusId) !== $statusService->isEndingStatus($listDatum['status'])) {
                $refreshObjectId[] = $listDatum['purchase_order_id'];
            }
        }

        if (empty($refreshObjectId)) {
            return true;
        }
        $purchaseProductFilter = new PurchaseOrderProductFilter($this->object->getDomainHandler()->getClientId());
        $purchaseProductFilter->select(['order_id']);
        $purchaseProductFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $purchaseProductFilter->purchase_order_id = $refreshObjectId;
        $purchaseProduct = $purchaseProductFilter->rawData();
        $this->triggerReferOrderLink(array_filter(array_unique(array_column($purchaseProduct,'order_id'))));
        $this->triggerReferPurchaseProductTransferInvoice($this->listData);
    }

    /**
     * 状态更新后触发重算订单产品毛利
     * @param $statusId
     * @return true|void
     */
    public function refreshInvoiceProductByStatusChange($statusId)
    {
        $refreshObjectId = [];
        $statusService = $this->object->getStatusService();
        $draftStatusId = $statusService->beginStatus()['id'];
        foreach ($this->listData as $k => $listDatum) {
            //"草稿"状态变更触发重算订单产品毛利
            if ($statusId == $draftStatusId || $listDatum['status'] == $draftStatusId) {
                $refreshObjectId[] = $listDatum['purchase_order_id'];
            }
        }

        if (empty($refreshObjectId)) {
            return true;
        }
        $purchaseProductFilter = new PurchaseOrderProductFilter($this->object->getDomainHandler()->getClientId());
        $purchaseProductFilter->select(['order_id']);
        $purchaseProductFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $purchaseProductFilter->purchase_order_id = $refreshObjectId;
        $purchaseProduct = $purchaseProductFilter->rawData();
        $orderIds = array_filter(array_unique(array_column($purchaseProduct,'order_id')));

        //产品毛利埋点：采购单新变更草稿状态，触发刷新产品毛利
        $this->triggerReferInvoiceProduct($orderIds);

        //退税金额埋点：采购单新变更草稿状态，触发刷新退税金额
        $this->triggerReferTaxRefund($orderIds);
    }

    /**
     * 记录操作历史
     * @param $updateData
     * @return void
     */
    public function history($updateFieldData, $historyType = PurchaseOrderSetting::TYPE_EDIT)
    {
        $map = [];
        foreach ($this->oldListData as $elem) {
            if (empty($updateFieldData[$elem['purchase_order_id']])) {
                continue;
            }
            foreach ($updateFieldData[$elem['purchase_order_id']] as $field => $newFieldVal) {
                if (isset($elem[$field])) {
                    if ($newFieldVal != $elem[$field]) {
                        $map[$elem['purchase_order_id']][] = [
                            'id' => $field,
                            'base' => 1,
                            'new' => $newFieldVal,
                            'old' => $elem[$field]
                        ];
                    }
                } elseif (isset($elem['external_field_data'][$field])) {
                    if ($newFieldVal != $elem['external_field_data'][$field]) {
                        $map[$elem['purchase_order_id']][] = [
                            'id' => $field,
                            'base' => 0,
                            'new' => $newFieldVal,
                            'old' => $elem['external_field_data'][$field]
                        ];
                    }
                }
            }
        }
        $history = new BatchPurchaseOrderBuilder();
        $history->buildByMap($this->object->getClientId(), $this->object->getDomainHandler()->getUserId(), $map, $historyType);
    }

    public function changeHandler($userId)
    {
        $this->onlySingle();

        if (empty($userId))
            throw new \RuntimeException('参数错误');

        $opUser = $this->object->getDomainHandler();
        $data = $this->get(['handler','status','purchase_order_id', 'scope_user_ids']);

        
        if ((!Helper::hasPermission($opUser->getClientId(), $opUser->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_EDIT)
                && !Helper::hasPermission($opUser->getClientId(), $opUser->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_CHANGE))
            || (!Helper::canManageAnyUsers($opUser->getClientId(), $opUser->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_EDIT,$data['handler'])
                && !Helper::canManageAnyUsers($opUser->getClientId(), $opUser->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_CHANGE,$data['handler']))
        )
        {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'),\ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
        }

        $handler = is_array($userId)?$userId:[$userId];
        $updateData = [
            'handler' => $handler,
            'modifier' => $this->object->getDomainHandler()->getUserId(),
            'update_time' => date('Y-m-d H:i:s')
        ];

        //审批
        $approveBuilder = new ApprovalBuilder($this->object->getDomainHandler(),new PurchaseOrderApprovalSetting());
        $approveBuilder->wakeupParams([
            'user_id' => $handler,
        ])->initFormSetValue(['handler' => $handler])->oldAttributes($this->get((new PurchaseOrderApprovalSetting())->approvalFiled(true)),false)->signalClass(PurchaseOrderChangeHandlerInterrupt::class)->build();


        $ret = $this->execute($updateData);

        $historyBuilder = new Builder(new PurchaseOrderSetting());
        $historyBuilder->setClientId($this->object->getClientId())->setType(PurchaseOrderSetting::TYPE_EDIT_HANDLER)
            ->setUpdateUser($this->object->getDomainHandler()->getUserId())
            ->initFromRawData($updateData)
            ->setOldAttributes($data)
            ->setUpdateType($this->editReferType ?? 0)
            ->setUpdateRefer($this->editReferId ?? 0)
            ->build();

        $this->runWorkflow($this->object->getClientId(), $this->object->getObjectId(), array_keys($updateData));
        
        return $ret;
    }

    /**
     * 批量变更处理人
     * @param $type
     * @param $oldUserId
     * @param $newUserId
     * @return void
     */
    public function batchChangeHandler($type, $oldUserId, $newUserId = 0)
    {
        $this->listData = array_column($this->get(['client_id', 'purchase_order_id', 'purchase_order_no', 'handler', 'modifier', 'scope_user_ids']), null, 'purchase_order_id');

        $opUser = $this->object->getDomainHandler();
        if (!Helper::hasPermission($opUser->getClientId(), $opUser->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_BATCH_CHANGE_HANDLER))
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'),\ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_MANAGE);

        //只能修改下属单据
        $this->checkInvoiceEditPermission();
        //判断状态流转流程
        $updateFieldData = $this->checkHandler($type, $oldUserId, $newUserId);

        //审批流中断
        if (!PrivilegeService::getInstance($this->clientId)->checkFunctional(PrivilegeConstants::FUNCTIONAL_DISABLE_APPROVAL))
            $this->approverFlowInterrupt($updateFieldData, PurchaseOrderChangeHandlerInterrupt::class, false);

        if (empty($this->listData))
            return;

        $this->batchUpdateHandler($type, $oldUserId, $newUserId);
        // 重算记录的scopeUserId
        $purchaseOrderIds = array_column($this->listData, 'purchase_order_id');
        if (!empty($purchaseOrderIds)) {
            $scopeUserService = new ScopeUserFieldService($opUser->getClientId(), $this->getMetadata());
            $scopeUserService->refreshScopeUserIdsByPids($purchaseOrderIds);
        }
        //History
        $this->oldListData = $this->listData;
        $updateFieldData = self::formatUpdateFieldData($updateFieldData);
        $this->history($updateFieldData, PurchaseOrderSetting::TYPE_EDIT_HANDLER);
        $this->succedOrderIds = array_column($this->listData, 'purchase_order_id');

        //todo 触发审批

        //执行工作流
        $this->runWorkflow($this->clientId, $this->succedOrderIds, ['handler']);
    }

    public static function formatUpdateFieldData($updateFieldData)
    {
        $newUpdateFieldData = [];
        foreach ($updateFieldData as $field => $updateDataList) {
            foreach ($updateDataList as $invoiceId => $datum) {
                $newUpdateFieldData[$invoiceId][$field] = $updateFieldData[$field][$invoiceId];
            }
        }
        return $newUpdateFieldData;
    }

    /**
     * 批量更新处理人
     * @param $type
     * @param $oldUserId
     * @param $newUserId
     * @return mixed
     * @throws \ProcessException
     */
    public function batchUpdateHandler($type, $oldUserId, $newUserId)
    {
        $params = [
            ':modifier'    => $this->object->getDomainHandler()->getUserId(),
            ':update_time' => date('Y-m-d H:i:s'),
        ];
        if ($type == \common\library\invoice\batch\Constant::HANDLER_REMOVE) {
            $handlerSql = "handler=array_remove(ARRAY(SELECT UNNEST(handler))::bigint[], $oldUserId::bigint)";
        } else {
            $handlerSql = "handler = array_replace(
                                        ARRAY(
                                                SELECT UNNEST(handler) 
                                                EXCEPT
                                                SELECT UNNEST(ARRAY [{$newUserId}]::bigint[])
                                        )::bigint[],
                                        $oldUserId::bigint,
                                        $newUserId::bigint
                                    )";
        }
        $idSql = implode(',', array_column($this->listData, 'purchase_order_id'));
        $sql = "update tbl_purchase_order set $handlerSql, modifier=:modifier, update_time=:update_time where client_id = " . $this->clientId . " and purchase_order_id in (" . $idSql . ") and handler @> ARRAY[$oldUserId]::bigint[]";
        $db = \PgActiveRecord::getDbByClientId($this->clientId);
        $count = $db->createCommand($sql)->execute($params);
        \LogUtil::info('client_id[' . $this->clientId . ']批量修改采购订单操作人purchase_order_id[' . $idSql . ']成功' . $count . '条');

        return $count;
    }

    /**
     * 操作人变更校验
     * @param $type
     * @param $oldUserId
     * @param $newUserId
     * @return array
     */
    public function checkHandler($type = null, $oldUserId = null, $newUserId = null)
    {
        $editData = [];
        foreach ($this->listData as $invoiceId => $listDatum) {
            try {
                if (empty($oldUserId)) {
                    //设置为指定处理人
                    if ($type == \common\library\invoice\batch\Constant::HANDLER_REPLACE) {
                        $editData['handler'][$invoiceId] = [$newUserId];
                    }
                    continue;
                }

                if (!in_array($oldUserId, $listDatum['handler'])) {
                    $this->setFailOrderMessage(\common\library\invoice\batch\Constant::NOT_MATCH_HANDLER_TYPE, $listDatum['purchase_order_id'], '采购订单未匹配到原处理人');
                }
                switch ($type) {
                    case \common\library\invoice\batch\Constant::HANDLER_REPLACE:
                        $editData['handler'][$invoiceId] = array_values(array_unique(array_merge(array_diff($listDatum['handler'], [$oldUserId]), [$newUserId])));
                        break;
                    case \common\library\invoice\batch\Constant::HANDLER_REMOVE:
                        if (count($listDatum['handler']) <= 1) {
                            $this->setFailOrderMessage(\common\library\invoice\batch\Constant::HAS_ONE_HANDLER_TYPE, $listDatum['purchase_order_id'], '采购订单仅有一个处理人不支持移除');
                        }
                        $editData['handler'][$invoiceId] = array_values(array_diff($listDatum['handler'], [$oldUserId]));
                        break;
                }
            } catch (\Exception $e) {
                unset($this->listData[$invoiceId]);
                if (isset($editData['handler'][$invoiceId])) {
                    unset($editData['handler'][$invoiceId]);
                }
                $this->failOrderIds[] = $listDatum['purchase_order_id'];
            }
        }
        return $editData;
    }

    public function delete()
    {
        $opUser = $this->object->getDomainHandler();

        if (!Helper::hasPermission($opUser->getClientId(), $opUser->getUserId(),
            PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_REMOVE)
        ) {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'),\ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
        }

        $data = $this->get(['handler','purchase_order_id', 'enable_flag', 'scope_user_ids']);
        if (empty($data))
            return false;

        /*
        $canMangeUser = Helper::getPermissionScopeUser($opUser->getClientId(),$opUser->getUserId(),
            PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_REMOVE);

        //非超管
        if ($canMangeUser != Helper::CAN_MANAGE_ALL_USER)
        {
            $canMangeUser = array_unique($canMangeUser);
            foreach ($data as $item)
            {
                if (empty(array_intersect($canMangeUser, $item['handler'])))
                    throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'),\ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
            }
        }
        */
        if(!\common\library\privilege_v3\Helper::canOperateRecord($opUser->getClientId(), $opUser->getUserId(), $data[0]['scope_user_ids'], PrivilegeConstants::PRIVILEGE_CRM_PURCHASE_ORDER_REMOVE)) {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'));
        }

        if ($this->hasRelateDownstreamInvoice()) {
            throw new \RuntimeException('关联下游单据，无法删除');
        }

        $updateData = [
            'enable_flag' => 0,
            'modifier' => $this->object->getDomainHandler()->getUserId(),
            'update_time' => date('Y-m-d H:i:s')
        ];

        return $this->standardProcess($updateData,[
            'history' => (new HistoryOperatorTask($opUser->getClientId(),$opUser->getUserId(), PurchaseOrderSetting::TYPE_REMOVE, new PurchaseOrderSetting()))->recordDelete(),
            'update_index' => ['update_type' =>  \Constants::PURCHASE_ORDER_INDEX_TYPE_DELETE, 'user_id' => $opUser->getUserId()],
            'after_delete'
        ]);
    }

    protected function afterDelete(array $data, array $setting)
    {
        //删除应付款单数据
        $this->deletePayableInvoice();

        $purchaseOrderIds = array_column($data, 'purchase_order_id');
        if (empty($purchaseOrderIds))
            return false;

        $transferInvoiceRelationFilter = new PurchaseProductTransferRelationFilter($this->clientId);
        $transferInvoiceRelationFilter->select(['transfer_invoice_id']);
        $transferInvoiceRelationFilter->refer_id = $purchaseOrderIds;
        $transferInvoiceRelationFilter->enable_flag = 1;
        $transferInvoiceIds = array_column($transferInvoiceRelationFilter->rawData(),'transfer_invoice_id');

        //删除产品、产品订单采购数enable_flag=2
        $purchaseProductFilter = new PurchaseOrderProductFilter($this->object->getDomainHandler()->getClientId());
        $purchaseProductFilter->enable_flag = new Equal(1);
        $purchaseProductFilter->purchase_order_id = new In($purchaseOrderIds);
        $purchaseProductList = $purchaseProductFilter->rawData();
        $purchaseProduct = $purchaseProductFilter->find();
        $purchaseProduct->getOperator()->delete(PurchaseOrderProduct::ENABLE_FLAG_CLOSE);

        //更新订单环节
        $this->triggerReferOrderLink();

        //销售订单是否有分采订单埋点：采购单删除触发刷新
        $this->triggerReferOrderHasPartialPurchase(array_column($purchaseProductList, 'order_id'));

        //销售订单利润埋点：采购单删除触发刷新订单利润
        $this->triggerReferOrderProfit();

        //产品毛利埋点：采购单新删除触发刷新产品毛利
        $this->triggerReferInvoiceProduct();

        //退税金额埋点：采购单新删除触发刷退税金额
        $this->triggerReferTaxRefund();

        \LogUtil::info('$purchaseOrderIds'.print_r($purchaseOrderIds,true));
        \LogUtil::info('$transferInvoiceIds'.print_r($transferInvoiceIds,true));
        //采购单删除触发刷新关联采购任务状态
        $this->triggerReferPurchaseProductTransferInvoice($data,$transferInvoiceIds);

        // 刷新关联订单是否已经分采
        $this->updateHasPartialPurchaseOrder();
        //回收箱
        return \common\library\recycle\API::add(
            $this->object->getDomainHandler()->getClientId(),
            $this->object->getDomainHandler()->getUserId(),
            Recycle::PURCHASE_ORDER,
            $purchaseOrderIds,
            false
        );
    }

    public function updateHasPartialPurchaseOrder()
    {
        // 如果关联的采购订单下没有一个分采模式的采购订单，就把它置为整采。
        // 没有refer id不必继续向下执行
        if (empty($this->object->refer_order_id)) {
            return;
        }

        $purchaseOrderFilter = new PurchaseOrderFilter($this->clientId);
        $purchaseOrderFilter->select([
            'refer_order_id',
            'order_count' => function ()  {
                return "count(*) as order_count";
            },
        ]);
        $purchaseOrderFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        // 查询分采的采购订单
        $purchaseOrderFilter->purchase_type = PurchaseOrder::PURCHASE_TYPE_PARTIAL;
        $purchaseOrderFilter->refer_order_id = $this->object->refer_order_id;
        $purchaseOrderFilter->groupBy("refer_order_id");
        $purchaseOrderProductList = $purchaseOrderFilter->rawData();
        $orderIdToCount = array_column($purchaseOrderProductList, 'order_count', 'refer_order_id');

        $orderPartialPurchaseMap = [];
        // 如果这个order_id查不到采购类型为分采的，就把它的"是否已分采过"（has_partial_purchase_order）置为0
        if (empty($orderIdToCount[$this->object->refer_order_id])) {
           $orderPartialPurchaseMap[$this->object->refer_order_id] = 0;
        }
        if (empty($orderPartialPurchaseMap)) {
            return;
        }


        $db = \PgActiveRecord::getDbByClientId($this->clientId);
        $caseStr = "";
        foreach ($orderPartialPurchaseMap as $orderId => $value) {
            $caseStr .= " when order_id={$orderId} then {$value} ";
        }
        $orderIdStr = implode(',', array_keys($orderPartialPurchaseMap));
        //批量刷新订单
        $updateSql = "update tbl_order set has_partial_purchase_order = case {$caseStr} end  where order_id in ($orderIdStr) and client_id={$this->clientId}";
        $caseStr && $db->createCommand($updateSql)->execute();
    }

    public function editByGroupInfo(array $data, array $attachments)
    {
        $this->onlySingle();

        //object update会重写oldData，需要提前拿出
        $oldAttributes = $this->object->getOldAttributes();

        $purchaseOrderData = $this->get(['client_id', 'purchase_order_id', 'supplier_id', 'status', 'attachments', 'purchase_order_no', 'amount', 'currency', 'exchange_rate', 'exchange_rate_usd','product_total_amount_rmb']);


        //临时处理：两种解包方式为了解决附件字段默认填充user_id,create_time的问题，后续迭代优化和前端改遍数据交互形式后可以去掉unFormatInfoGroup
        $purchaseOrderField = OmsField::make($this->clientId, \Constants::TYPE_PURCHASE_ORDER);
        $attributes = $purchaseOrderField->unpackFormData($data);

        $privilegeFieldService = new PrivilegeFieldService($this->object->getDomainHandler()->getUserId(), PrivilegeConstants::FUNCTIONAL_PURCHASE_ORDER);
        $attributes = $privilegeFieldService->handlePrivilegeFieldsForUpdateWriteBack($attributes, $this->object->getOldAttributes());

        $productList = $attributes['product_list'] ?? null;
        unset($attributes['product_list']);

        //不允许修改状态、处理人
        unset($attributes['status']);
        unset($attributes['inbound_status']);

        //汇率、货币变更时重算换算金额
        $attributes['currency'] = $attributes['currency'] ?? 'CNY';
        $this->object->checkChangeRateVaule($attributes['exchange_rate'] ?? 0, $attributes['exchange_rate_usd'] ?? 0);
        $attributes['purchase_type'] = !empty($attributes['purchase_type']) ? $attributes['purchase_type'] : PurchaseOrder::PURCHASE_TYPE_FULL;

        //不允许修改待付款金额
        unset($attributes['payment_wait_amount']);

        //空值不修改，有值进行编号查重
        if (empty($attributes['purchase_order_no'])) {
            unset($attributes['purchase_order_no']);
        }else{
            //限制编号的长度
            if (mb_strlen($attributes['purchase_order_no']) > 100) {
                throw new \RuntimeException(\Yii::t('invoice', 'The order number must not exceed 100 characters'));
            }

            if ($this->object->checkHasOrderNo($attributes['purchase_order_no'])) {
                throw new \RuntimeException(\Yii::t('invoice', 'Duplicate Order Number'));
            }
        }
        if (isset($attributes['supplier_contact']))
        {
            $attributes['supplier_contact'] = intval($attributes['supplier_contact']);
        }

        //编辑cost_item_relation_id 会被传过来，剔除掉
        unset($attributes['cost_item_relation_id']);

        $attributes['attachments'] = $attachments;

        $this->object->updateParams = ['data' => $data, 'attachments' => $attachments];
        $this->object->bindAttrbuties($attributes);
        $this->object->setProductList($productList);
        $this->object->update();

        $newAttributes = $this->object->getRawAttributes();

        $domainHandler = $this->object->getDomainHandler();
        $setting = new PurchaseOrderSetting();
        $setting->setExtraCompareFields($setting->getCustomFieldId());
        $historyBuilder = new Builder($setting);
        $historyBuilder->setClientId($domainHandler->getClientId())->setType(PurchaseOrderSetting::TYPE_EDIT)
            ->setUpdateUser($domainHandler->getUserId())
            ->initFromRawData($setting->sortAttributes($newAttributes), false)
            ->setOldAttributes($oldAttributes)
            ->build();

        if (!empty($historyBuilder->getBuildData()) && $this->object->getHasRecordDiff()) {
            $this->updateTransferInvoice(true);
        }

        //采购单编辑 触发刷新订单环节
        $this->triggerReferOrderLink();

        //销售订单是否有分采订单埋点：采购单编辑触发刷新
        $this->triggerReferOrderHasPartialPurchase();

        //采购单编辑触发刷新关联采购任务状态
        $this->triggerReferPurchaseProductTransferInvoice([$purchaseOrderData]);

        //刷新应付款单数据
        $this->updatePayableInvoiceAmount($newAttributes);

        //刷新入库状态
        $this->object->refreshInboundStatus();

        //销售订单利润埋点：采购单编辑触发刷新订单利润。若采购订单金额、币种、汇率变更，包含的销售订单利润都需要更新
        $referProfitOrderIds = [];
        if (
            (floatval($purchaseOrderData['amount']) != floatval($newAttributes['amount']))
            ||
            ($purchaseOrderData['currency'] != $newAttributes['currency'])
            ||
            (floatval($purchaseOrderData['exchange_rate']) != floatval($newAttributes['exchange_rate']))
            ||
            (floatval($purchaseOrderData['exchange_rate_usd']) != floatval($newAttributes['exchange_rate_usd']))
            ||
            (floatval($purchaseOrderData['product_total_amount_rmb']) != floatval($newAttributes['product_total_amount_rmb']))
            ||
            ($this->object->getHasRecordChange())
        )
        {
            $referProfitOrderIds = array_filter(array_unique(array_column($productList, 'order_id')));
        }
        $this->triggerReferOrderProfit($referProfitOrderIds);

        //产品毛利埋点：采购单编辑 触发刷新产品毛利。若采购订单币种汇率发生改变，仍需更新
        $referInvoiceProductOrderIds = [];
        if(floatval($purchaseOrderData['exchange_rate']) != floatval($newAttributes['exchange_rate'])){
            $referInvoiceProductOrderIds = array_filter(array_unique(array_column($productList, 'order_id')));
        }
        $this->triggerReferInvoiceProduct($referInvoiceProductOrderIds);

        //退税金额埋点：采购单编辑 触发刷新退税金额。若采购订单币种汇率发生改变，仍需更新
        $referTaxRefundOrderIds = [];
        if (
            ($purchaseOrderData['currency'] != $newAttributes['currency'])
            ||
            (floatval($purchaseOrderData['exchange_rate']) != floatval($newAttributes['exchange_rate']))
            ||
            (floatval($purchaseOrderData['exchange_rate_usd']) != floatval($newAttributes['exchange_rate_usd']))
            ||
            (floatval($purchaseOrderData['product_total_amount_rmb']) != floatval($newAttributes['product_total_amount_rmb']))
            ||
            ($this->object->getHasRecordChange())
        ) {
            $referTaxRefundOrderIds = array_filter(array_unique(array_column($productList, 'order_id')));
        }
        $this->triggerReferTaxRefund($referTaxRefundOrderIds);

        return true;
    }

    //过滤表不存在的字段
    protected function filterField($data)
    {
        $objectField = $this->object->getMetadata()->columnFields();
        return  array_filter($data, function ($k) use ($objectField) {
            return in_array($k, $objectField);
        },ARRAY_FILTER_USE_KEY);
    }

    public function updateTransferInvoice($diffData){
        if(!$diffData){
            return;
        }
        $transferParams = [
            'refer_id' => [$this->object->purchase_order_id],
            'refer_type' => \Constants::TYPE_PURCHASE_ORDER,
//            'history_ids' => $diffData,
        ];
        $this->upstreamRelateUpdateTransfer($this->clientId, $this->object->getDomainHandler()->getUserId(), $transferParams, $diffData);
    }

    public function recover()
    {
        $clientId = $this->object->getDomainHandler()->getClientId();
        $opUserId = $this->object->getDomainHandler()->getUserId();

        $data = $this->get(['purchase_order_id', 'enable_flag','purchase_order_no']);
        if (empty($data))
            return 0;

        $purchase_order_no = $data[0]['purchase_order_no'];
        if ($this->object->checkHasOrderNo($purchase_order_no)) {
            throw new \RuntimeException(\Yii::t('purchase_order', 'Purchase order no existed, recovery is not supported'));
        }
        $updateData = [
            'enable_flag' => 1,
            'modifier' => $opUserId,
            'update_time' => date('Y-m-d H:i:s')
        ];

        //tbl_purchase_order
        //tbl_purchase_order_product
        return $this->standardProcess($updateData,[
            'history' => (new HistoryOperatorTask($clientId, $opUserId, PurchaseOrderSetting::TYPE_RECOVER, new PurchaseOrderSetting()))->recordRecover(),
            'update_index' => ['update_type' =>  \Constants::PURCHASE_ORDER_INDEX_TYPE_UPDATE, 'user_id' => $opUserId],
            'after_recover'
        ]);
    }

    protected function afterRecover(array $data, array $setting)
    {
        //恢复应付款单
        $this->recoverPayableInvoice();

        $purchaseOrderIds = array_column($data, 'purchase_order_id');
        if (empty($purchaseOrderIds))
            return false;

        //恢复产品、产品订单采购数enable_flag=2
        $purchaseProductFilter = new PurchaseOrderProductFilter($this->object->getDomainHandler()->getClientId());
        $purchaseProductFilter->enable_flag = PurchaseOrderProduct::ENABLE_FLAG_CLOSE;
        $purchaseProductFilter->purchase_order_id = new In($purchaseOrderIds);
        $purchaseProductList = $purchaseProductFilter->rawData();
        $purchaseProduct = $purchaseProductFilter->find();
        $purchaseProduct->getOperator()->revcover();

        $this->triggerReferOrderLink();

        //销售订单是否有分采订单埋点：采购单恢复，触发刷新
        $this->triggerReferOrderHasPartialPurchase(array_column($purchaseProductList, 'order_id'));

        //销售订单利润埋点：采购单恢复，触发刷新订单利润
        $this->triggerReferOrderProfit();

        //产品毛利埋点：采购单恢复，触发刷新订单利润
        $this->triggerReferInvoiceProduct();

        //采购单恢复触发刷新关联采购任务状态
        $this->triggerReferPurchaseProductTransferInvoice($data);
    }

    public function addTriggerReferOrderHasPartialPurchase($order_ids = [])
    {
        $trigger = RefreshOrderHasPartialPurchaseTrigger::make($this->object->getClientId());
        $trigger->addOrderIds($order_ids);
    }

    public function triggerReferOrderHasPartialPurchase($order_id = [])
    {
        $trigger = RefreshOrderHasPartialPurchaseTrigger::make($this->object->getClientId());
        $trigger->addOrderIds($order_id);
        $trigger->trigger();
    }

    public function triggerReferOrderLink($order_id = [])
    {
        $trigger = PurchaseOrderTrigger::make($this->object->getClientId());
        $trigger->addOrderIds($order_id);
        $trigger->trigger();
    }

    public function triggerReferOrderProfit($order_id = [])
    {
        $trigger = PurchaseOrderProductAmountTrigger::make($this->object->getClientId());
        $trigger->addOrderIds($order_id);
        $trigger->trigger();
    }

    public function triggerReferInvoiceProduct($order_id = [])
    {
        $trigger = PurchaseInvoiceProductTrigger::make($this->object->getClientId());
        $trigger->addOrderIds($order_id);
        $trigger->trigger();
    }

    public function triggerReferTaxRefund($order_id = [])
    {
        $taxRefundApi = new TaxRefundApi($this->clientId);
        $calculateType = $taxRefundApi->getCalculateType(TaxRefundConstants::TAX_REFUND_FIELD);
        if($calculateType == TaxRefundConstants::TAX_REFUND_CALCULATE_BY_PURCHASE){
            $trigger = TaxRefundAmountTrigger::make($this->object->getClientId());
            $trigger->addOrderIds($order_id);
            $trigger->trigger();
        }
    }

    public function triggerReferPurchaseProductTransferInvoice($attributes = [], $transferInvoiceIds = [])
    {
        $trigger = \common\library\oms\product_transfer\purchase\trigger\PurchaseOrderTrigger::make($this->object->getClientId());
        $purchaseOrderId = array_column($attributes, 'purchase_order_id');
        $trigger->setAttributes($attributes);
        $trigger->addPurchaseOrderId($purchaseOrderId);
        $trigger->addTransferInvoiceIds($transferInvoiceIds);
        $trigger->trigger();
    }

    //生成应付款单
    public function createPayableInvoice()
    {
        $payableInvoice = new PayableInvoice($this->object->getClientId());
        $opUser = $this->object->getDomainHandler();
        $payableInvoice->calculateAmountAndExchangeRate($this->object->currency, $this->object->amount, $this->object->exchange_rate, $this->object->exchange_rate_usd);
        $bindData = [
            'supplier_id' => $this->object->supplier_id,//deprecated
            'receive_refer_type' => \Constants::TYPE_SUPPLIER,
            'receive_refer_id' => $this->object->supplier_id,
            'refer_id' => $this->object->purchase_order_id,
            'refer_type' => \Constants::TYPE_PURCHASE_ORDER,
            'handler' => [$opUser->getUserId()],
        ];
        $payableInvoice->bindAttrbuties($bindData);
        $payableInvoice->create();
    }

    /**
     * 修改付款信息
     * 可以下游刷新，也可以自身模型修改
     * 自身模型 通过noUpdatePaymentAmount、refreshDownStream 来控制修改
     * @param $payment_status
     * @param $payment_amount
     * @return void
     */
    public function editPaymentStatusAndAmount($payment_status,$payment_amount, $payment_amount_rmb, $payment_amount_usd)
    {
        $this->onlySingle();
        $updateData = [
            'payment_status' => $payment_status,
            'payment_amount' => $payment_amount,
            'payment_amount_rmb' => $payment_amount_rmb,
            'payment_amount_usd' => $payment_amount_usd,
        ];

        if ($this->object->noUpdatePaymentAmount) {
            unset($updateData['payment_amount'], $updateData['payment_amount_rmb'], $updateData['payment_amount_usd']);
        }

        $this->execute($updateData);

        //源单状态变化时，刷新应付款单状态
        if ($this->object->refreshDownStream) {
            $payableInvoiceFilter = new PayableInvoiceFilter($this->object->getClientId());
            $payableInvoiceFilter->refer_id = $this->object->getObjectId();
            $payableInvoiceFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
            $payableInvoice = $payableInvoiceFilter->find();
            $payableInvoice->getOperator()->upstreamRefreshStatus($payment_status);
        }
        
        // 下面代码对比下字段是否真的有变更，有变更运行工作流
        $runWorkflow = false;
        if (!empty($this->object)) {
            // todo $updateData增加更改字段的话，这里需要增加对比规格
            if ($this->object->payment_status != $payment_status) {
                $runWorkflow = true;
            }
            if (!$this->object->noUpdatePaymentAmount) {
                foreach (['payment_amount', 'payment_amount_rmb', 'payment_amount_usd'] as $fieldKey) {
                    if (isset($this->object->{$fieldKey}) && (round($this->object->{$fieldKey}, 4) != round($$fieldKey, 4))) {
                        $runWorkflow = true;
                        break;
                    }
                }
            }
        }
        //触发下工作流
        $runWorkflow && $this->runWorkflow($this->object->getClientId(), $this->object->getObjectId(), array_keys($updateData));
    }

    //应付款单付款金额修改
    protected function updatePayableInvoiceAmount($updateData)
    {
        $updateData['receive_refer_id'] = $updateData['supplier_id'];
        $updateData['receive_refer_type'] = \Constants::TYPE_SUPPLIER;
        (new PayableInvoiceApi())->updateByRefer($this->clientId, $this->object->getObjectId(), \Constants::TYPE_PURCHASE_ORDER, $updateData);
    }

    /**
     * 删除应付款单
     */
    public function deletePayableInvoice()
    {
        (new PayableInvoiceApi())->deleteByRefer($this->clientId, $this->object->getObjectId(), \Constants::TYPE_PURCHASE_ORDER);
    }

    /**
     * 恢复应付款单
     */
    public function recoverPayableInvoice()
    {
        (new PayableInvoiceApi())->recoverByRefer($this->clientId, $this->object->getObjectId(), \Constants::TYPE_PURCHASE_ORDER);
    }

    //单个情况下才可调用
    protected function hasRelateDownstreamInvoice()
    {
        $paymentInvoiceSumAmountMap = (new PaymentInvoiceApi())->ReferPaymentSumAmount($this->clientId, $this->object->purchase_order_id);
        $referProductTransferAndInboundMap = (new API())->referProductTransferAndInbound($this->clientId, $this->object->purchase_order_id);

        return !empty($paymentInvoiceSumAmountMap) || !empty($referProductTransferAndInboundMap);
    }

    public function editInboundStatus($inbound_status)
    {
        $this->onlySingle();
        $updateData = [
            'inbound_status' => $inbound_status,
        ];
        $this->execute($updateData);
        // 入库状态触发工作流
        if ($this->object->inbound_status != $inbound_status) {
            $this->runWorkflow($this->object->getClientId(), $this->object->getObjectId(), array_keys($updateData));
        }
    }
}