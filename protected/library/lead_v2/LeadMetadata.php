<?php
/**
 * Created by PhpStorm.
 * User: andy
 * Date: 2021/07/28
 * Time: 15:09
 */

namespace common\library\lead_v2;

use common\library\object\object_define\Constant;
use common\library\object\traits\BizObjectClass;
use xiaoman\orm\metadata\Metadata;

class LeadMetadata extends Metadata
{
    use BizObjectClass;

    protected $columns = [
        'lead_id' => [
            'type' => 'bigint',
            'name' => 'lead_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'client_id' => [
            'type' => 'bigint',
            'name' => 'client_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'user_id' => [
            'type' => 'array',
            'name' => 'user_id',
            'nullable' => 0,
            'php_type' => 'array',
            'filter' => [ 'enable' => true, 'array' => true,]
        ],
        'scope_user_ids' => [
            'type' => 'array',
            'name' => 'scope_user_ids',
            'nullable' => 0,
            'php_type' => 'array',
            'filter' => [ 'enable' => true, 'array' => true,]
        ],
        'name' => [
            'type' => 'character',
            'name' => 'name',
            'nullable' => 0,
            'php_type' => '',
            'filter' => []
        ],
        'company_name' => [
            'type' => 'character',
            'name' => 'company_name',
            'nullable' => 0,
            'php_type' => '',
            'filter' => []
        ],
        'short_name' => [
            'type' => 'character',
            'name' => 'short_name',
            'nullable' => 0,
            'php_type' => '',
            'filter' => []
        ],
        'company_id' => [
            'type' => 'bigint',
            'name' => 'company_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'origin' => [
            'type' => 'bigint',
            'name' => 'origin',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'serial_id' => [
            'type' => 'character',
            'name' => 'serial_id',
            'nullable' => 0,
            'php_type' => '',
            'filter' => []
        ],
        'scale_id' => [
            'type' => 'integer',
            'name' => 'scale_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'group_id' => [
            'type' => 'bigint',
            'name' => 'group_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'biz_type' => [
            'type' => 'character',
            'name' => 'biz_type',
            'nullable' => 0,
            'php_type' => '',
            'filter' => []
        ],
        'category_ids' => [
            'type' => 'jsonb',
            'name' => 'category_ids',
            'nullable' => 0,
            'php_type' => 'array',
            'filter' => [ 'enable' => true, 'json' => true,]
        ],
        'country' => [
            'type' => 'character',
            'name' => 'country',
            'nullable' => 0,
            'php_type' => '',
            'filter' => []
        ],
        'province' => [
            'type' => 'character',
            'name' => 'province',
            'nullable' => 0,
            'php_type' => '',
            'filter' => []
        ],
        'city' => [
            'type' => 'character',
            'name' => 'city',
            'nullable' => 0,
            'php_type' => '',
            'filter' => []
        ],
        'address' => [
            'type' => 'character',
            'name' => 'address',
            'nullable' => 0,
            'php_type' => '',
            'filter' => []
        ],
        'homepage' => [
            'type' => 'character',
            'name' => 'homepage',
            'nullable' => 0,
            'php_type' => '',
            'filter' => []
        ],
        'fax' => [
            'type' => 'character',
            'name' => 'fax',
            'nullable' => 0,
            'php_type' => '',
            'filter' => []
        ],
        'tel' => [
            'type' => 'character',
            'name' => 'tel',
            'nullable' => 0,
            'php_type' => '',
            'filter' => []
        ],
        'tel_area_code' => [
            'type' => 'character',
            'name' => 'tel_area_code',
            'nullable' => 0,
            'php_type' => '',
            'filter' => []
        ],
        'tel_full' => [
            'type' => 'character',
            'name' => 'tel_full',
            'nullable' => 0,
            'php_type' => '',
            'filter' => []
        ],
        'main_customer' => [
            'type' => 'bigint',
            'name' => 'main_customer',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'main_customer_email' => [
            'type' => 'character',
            'name' => 'main_customer_email',
            'nullable' => 0,
            'php_type' => '',
            'filter' => []
        ],
        'star' => [
            'type' => 'integer',
            'name' => 'star',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'trail_status' => [
            'type' => 'bigint',
            'name' => 'trail_status',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'image_list' => [
            'type' => 'array',
            'name' => 'image_list',
            'nullable' => 0,
            'php_type' => 'array',
            'filter' => [ 'enable' => true, 'array' => true,]
        ],
        'external_field_data' => [
            'type' => 'jsonb',
            'name' => 'external_field_data',
            'nullable' => 0,
            'php_type' => 'array',
            'filter' => [ 'enable' => true, 'json' => true,]
        ],
        'score' => [
            'type' => 'jsonb',
            'name' => 'score',
            'nullable' => 0,
            'php_type' => 'array',
            'filter' => [ 'enable' => true, 'json' => true,]
        ],
        'tag' => [
            'type' => 'jsonb',
            'name' => 'tag',
            'nullable' => 0,
            'php_type' => 'array',
            'filter' => [ 'enable' => true, 'json' => true,]
        ],
        'user_data' => [
            'type' => 'jsonb',
            'name' => 'user_data',
            'nullable' => 0,
            'php_type' => 'array',
            'filter' => [ 'enable' => true, 'json' => true,]
        ],
        'remark' => [
            'type' => 'text',
            'name' => 'remark',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [ 'enable' => false,]
        ],
        'follow_product_list' => [
            'type' => 'array',
            'name' => 'follow_product_list',
            'nullable' => 0,
            'php_type' => 'array',
            'filter' => [ 'enable' => true, 'array' => true,]
        ],
        'follow_product_group_list' => [
            'type' => 'array',
            'name' => 'follow_product_group_list',
            'nullable' => 0,
            'php_type' => 'array',
            'filter' => [ 'enable' => true, 'array' => true,]
        ],
        'follow_product_category_list' => [
            'type' => 'array',
            'name' => 'follow_product_category_list',
            'nullable' => 0,
            'php_type' => 'array',
            'filter' => [ 'enable' => true, 'array' => true,]
        ],
        'client_tag_list' => [
            'type' => 'array',
            'name' => 'client_tag_list',
            'nullable' => 0,
            'php_type' => 'array',
            'filter' => [ 'enable' => true, 'array' => true,]
        ],
        'last_owner' => [
            'type' => 'bigint',
            'name' => 'last_owner',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'create_user_id' => [
            'type' => 'bigint',
            'name' => 'create_user_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'follow_count' => [
            'type' => 'integer',
            'name' => 'follow_count',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'transfer_count' => [
            'type' => 'integer',
            'name' => 'transfer_count',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'release_count' => [
            'type' => 'integer',
            'name' => 'release_count',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'receive_mail_count' => [
            'type' => 'integer',
            'name' => 'receive_mail_count',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'send_mail_count' => [
            'type' => 'integer',
            'name' => 'send_mail_count',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'is_archive' => [
            'type' => 'smallint',
            'name' => 'is_archive',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'create_time' => [
            'type' => 'timestamp',
            'name' => 'create_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [ 'enable' => true, 'range' => true,]
        ],
        'update_time' => [
            'type' => 'timestamp',
            'name' => 'update_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [ 'enable' => true, 'range' => true,]
        ],
        'archive_time' => [
            'type' => 'timestamp',
            'name' => 'archive_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [ 'enable' => true, 'range' => true,]
        ],
        'order_time' => [
            'type' => 'timestamp',
            'name' => 'order_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [ 'enable' => true, 'range' => true,]
        ],
        'follow_up_time' => [
            'type' => 'timestamp',
            'name' => 'follow_up_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [ 'enable' => true, 'range' => true,]
        ],
        'edm_time' => [
            'type' => 'timestamp',
            'name' => 'edm_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [ 'enable' => true, 'range' => true,]
        ],
        'send_mail_time' => [
            'type' => 'timestamp',
            'name' => 'send_mail_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [ 'enable' => true, 'range' => true,]
        ],
        'receive_mail_time' => [
            'type' => 'timestamp',
            'name' => 'receive_mail_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [ 'enable' => true, 'range' => true,]
        ],
        'mail_time' => [
            'type' => 'timestamp',
            'name' => 'mail_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [ 'enable' => true, 'range' => true,]
        ],
        'remark_time' => [
            'type' => 'timestamp',
            'name' => 'remark_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [ 'enable' => true, 'range' => true,]
        ],
        'edit_time' => [
            'type' => 'timestamp',
            'name' => 'edit_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [ 'enable' => true, 'range' => true,]
        ],
        'open_edm_flag' => [
            'type' => 'smallint',
            'name' => 'open_edm_flag',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'reply_edm_flag' => [
            'type' => 'smallint',
            'name' => 'reply_edm_flag',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'open_edm_url_flag' => [
            'type' => 'smallint',
            'name' => 'open_edm_url_flag',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'company_hash_id' => [
            'type' => 'character',
            'name' => 'company_hash_id',
            'nullable' => 0,
            'php_type' => '',
            'filter' => []
        ],
        'ai_read_flag' => [
            'type' => 'integer',
            'name' => 'ai_read_flag',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'renew_time' => [
            'type' => 'timestamp',
            'name' => 'renew_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [ 'enable' => true, 'range' => true,]
        ],
        'ad_keyword' => [
            'type' => 'character',
            'name' => 'ad_keyword',
            'nullable' => 0,
            'php_type' => '',
            'filter' => []
        ],
        'archive_type' => [
            'type' => 'integer',
            'name' => 'archive_type',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'read_flag' => [
            'type' => 'integer',
            'name' => 'read_flag',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'status' => [
            'type' => 'integer',
            'name' => 'status',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'status_edit_type' => [
            'type' => 'integer',
            'name' => 'status_edit_type',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'fail_status' => [
            'type' => 'integer',
            'name' => 'fail_status',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'fail_reason' => [
            'type' => 'text',
            'name' => 'fail_reason',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [ 'enable' => false,]
        ],
        'fail_type' => [
            'type' => 'integer',
            'name' => 'fail_type',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'inquiry_origin' => [
            'type' => 'smallint',
            'name' => 'inquiry_origin',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'inquiry_country' => [
            'type' => 'character',
            'name' => 'inquiry_country',
            'nullable' => 0,
            'php_type' => '',
            'filter' => []
        ],
        'public_time' => [
            'type' => 'timestamp',
            'name' => 'public_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [ 'enable' => true, 'range' => true,]
        ],
        'private_time' => [
            'type' => 'timestamp',
            'name' => 'private_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [ 'enable' => true, 'range' => true,]
        ],
        'last_edit_user' => [
            'type' => 'bigint',
            'name' => 'last_edit_user',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'create_user' => [
            'type' => 'bigint',
            'name' => 'create_user',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'store_id' => [
            'type' => 'integer',
            'name' => 'store_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'open_mail_flag' => [
            'type' => 'smallint',
            'name' => 'open_mail_flag',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'reply_mail_flag' => [
            'type' => 'smallint',
            'name' => 'reply_mail_flag',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'duplicate_flag' => [
            'type' => 'bigint',
            'name' => 'duplicate_flag',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'next_follow_up_time' => [
            'type' => 'timestamp',
            'name' => 'next_follow_up_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [ 'enable' => true, 'range' => true,]
        ],
        'site_id' => [
            'type' => 'integer',
            'name' => 'site_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'origin_list' => [
            'type' => 'array',
            'name' => 'origin_list',
            'nullable' => 0,
            'php_type' => 'array',
            'filter' => [ 'enable' => true, 'array' => true,]
        ],
        'trash_flag' => [
            'type' => 'integer',
            'name' => 'trash_flag',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'trash_time' => [
            'type' => 'timestamp',
            'name' => 'trash_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [ 'enable' => true, 'range' => true,]
        ],
        'trash_reason' => [
            'type' => 'integer',
            'name' => 'trash_reason',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'layer_id' => [
            'type' => 'integer',
            'name' => 'layer_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'latest_inquiry_status' => [
            'type' => 'integer',
            'name' => 'latest_inquiry_status',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
//        'layer_integrity' => [
//            'type' => 'integer',
//            'name' => 'layer_integrity',
//            'nullable' => 0,
//            'php_type' => 'int',
//            'filter' => [ 'enable' => true, 'batch' => true,]
//        ],
        'source_detail' => [
            'type' => 'jsonb',
            'name' => 'source_detail',
            'nullable' => 0,
            'php_type' => 'array',
            'filter' => [ 'enable' => true, 'json' => true,]
        ],
    ];


    public static function table()
    {
        return 'tbl_lead';
    }

    public static function dataSource()
    {
        return Metadata::DATA_SOURCE_POSTGRESQL_CLIENT;
    }

    public static function singeObject()
    {
        return Lead::class;
    }

    public static function batchObject()
    {
        return BatchLead::class;
    }

    public static function filter()
    {
        return LeadFilter::class;
    }

    public static function operator()
    {
        return LeadOperator::class;
    }

    public static function formatter()
    {
        return LeadFormatter::class;
    }

    public static function objectIdKey()
    {
        return 'lead_id';
    }

    public static function objectName()
    {
        return Constant::OBJ_LEAD;
    }

    public static function validFlagKey()
    {
        return 'is_archive';
    }
    
    public static function validFlagValue()
    {
        return \Constants::ENABLE_FLAG_TRUE;
    }    
}
