<?php

namespace common\library\oms_v2\shipping_invoice_record;

use common\library\object\object_define\Constant;
use xiaoman\orm\metadata\MetadataV2;
use xiaoman\orm\metadata\Metadata;
use common\library\object\traits\BizObjectClass;
use common\library\object\field\util\FieldTransferUtil;

class ShippingInvoiceRecordMetadata extends MetadataV2
{
    use BizObjectClass;
    protected $columns = [
    'shipping_record_id' => [
        'type' => 'int',
        'name' => 'shipping_record_id',
        'nullable' => '0',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'shipping_invoice_id' => [
        'type' => 'int',
        'name' => 'shipping_invoice_id',
        'nullable' => '0',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'order_id' => [
        'type' => 'int',
        'name' => 'order_id',
        'nullable' => '0',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'client_id' => [
        'type' => 'int',
        'name' => 'client_id',
        'nullable' => '0',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'invoice_product_id' => [
        'type' => 'int',
        'name' => 'invoice_product_id',
        'nullable' => '0',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'package_unit' => [
        'type' => 'string',
        'name' => 'package_unit',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'count_per_package' => [
        'type' => 'float',
        'name' => 'count_per_package',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'package_remark' => [
        'type' => 'string',
        'name' => 'package_remark',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'shipping_count' => [
        'type' => 'float',
        'name' => 'shipping_count',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'product_net_weight' => [
        'type' => 'float',
        'name' => 'product_net_weight',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'package_gross_weight' => [
        'type' => 'float',
        'name' => 'package_gross_weight',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'package_volume' => [
        'type' => 'string',
        'name' => 'package_volume',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'package_size_length' => [
        'type' => 'float',
        'name' => 'package_size_length',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'package_size_weight' => [
        'type' => 'float',
        'name' => 'package_size_weight',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'package_size_height' => [
        'type' => 'float',
        'name' => 'package_size_height',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'product_size_length' => [
        'type' => 'float',
        'name' => 'product_size_length',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'product_size_weight' => [
        'type' => 'float',
        'name' => 'product_size_weight',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'product_size_height' => [
        'type' => 'float',
        'name' => 'product_size_height',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'carton_size_length' => [
        'type' => 'float',
        'name' => 'carton_size_length',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'carton_size_weight' => [
        'type' => 'float',
        'name' => 'carton_size_weight',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'carton_size_height' => [
        'type' => 'float',
        'name' => 'carton_size_height',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'description' => [
        'type' => 'string',
        'name' => 'description',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'remark' => [
        'type' => 'string',
        'name' => 'remark',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'count_per_carton' => [
        'type' => 'float',
        'name' => 'count_per_carton',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'container_type' => [
        'type' => 'string',
        'name' => 'container_type',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'container_no' => [
        'type' => 'string',
        'name' => 'container_no',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'container_seal_number' => [
        'type' => 'string',
        'name' => 'container_seal_number',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'hs_code' => [
        'type' => 'string',
        'name' => 'hs_code',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'customs_name' => [
        'type' => 'string',
        'name' => 'customs_name',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'customs_cn_name' => [
        'type' => 'string',
        'name' => 'customs_cn_name',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'external_field_data' => [
        'type' => 'jsonb',
        'name' => 'external_field_data',
        'nullable' => '0',
        'php_type' => 'array',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'enable_flag' => [
        'type' => 'int',
        'name' => 'enable_flag',
        'nullable' => '0',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'create_time' => [
        'type' => 'dateTime',
        'name' => 'create_time',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'update_time' => [
        'type' => 'dateTime',
        'name' => 'update_time',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'product_id' => [
        'type' => 'int',
        'name' => 'product_id',
        'nullable' => '1',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'sku_id' => [
        'type' => 'int',
        'name' => 'sku_id',
        'nullable' => '1',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'product_type' => [
        'type' => 'int',
        'name' => 'product_type',
        'nullable' => '0',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'combine_record_id' => [
        'type' => 'int',
        'name' => 'combine_record_id',
        'nullable' => '0',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'sort' => [
        'type' => 'int',
        'name' => 'sort',
        'nullable' => '0',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'package_type' => [
        'type' => 'string',
        'name' => 'package_type',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'carton_gross_weight' => [
        'type' => 'float',
        'name' => 'carton_gross_weight',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'carton_volume' => [
        'type' => 'float',
        'name' => 'carton_volume',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'product_volume' => [
        'type' => 'float',
        'name' => 'product_volume',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'is_master_product' => [
        'type' => 'int',
        'name' => 'is_master_product',
        'nullable' => '0',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'master_id' => [
        'type' => 'int',
        'name' => 'master_id',
        'nullable' => '0',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'carton_net_weight' => [
        'type' => 'float',
        'name' => 'carton_net_weight',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'product_image' => [
        'type' => 'jsonb',
        'name' => 'product_image',
        'nullable' => '0',
        'php_type' => 'array',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'product_name' => [
        'type' => 'string',
        'name' => 'product_name',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'product_cn_name' => [
        'type' => 'string',
        'name' => 'product_cn_name',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'product_model' => [
        'type' => 'string',
        'name' => 'product_model',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'unit' => [
        'type' => 'string',
        'name' => 'unit',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'sale_price' => [
        'type' => 'float',
        'name' => 'sale_price',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'carton_count' => [
        'type' => 'float',
        'name' => 'carton_count',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ]
];

    public static function table()
    {
        return 'tbl_shipping_record';
    }

    public static function dataSource()
    {
        return Metadata::DATA_SOURCE_POSTGRESQL_CLIENT;
    }

    public static function singeObject()
    {
        return ShippingInvoiceRecord::class;
    }

    public static function batchObject()
    {
        return BatchShippingInvoiceRecord::class;
    }

    public static function objectIdKey()
    {
        return 'shipping_record_id';
    }

    public static function filter()
    {
        return ShippingInvoiceRecordFilter::class;
    }

    public static function formatter()
    {
        return ShippingInvoiceRecordFormatter::class;
    }

    public static function operator()
    {
        return ShippingInvoiceRecordOperator::class;
    }
    
    public static function getModuleType()
    {
        return \Constants::TYPE_SHIPPING_INVOICE;
    }
    
    public static function fieldTransfer()
    {
        return FieldTransferUtil::class;
    }
    
    public static function objectName()
    {
        return Constant::OBJ_SHIPPING_RECORD;
    }
    
    public static function autoLoadExtendData(): bool
    {
        return  true;
    }

    public static function defaultJoinSetting():array
    {
        return [
            'tbl_shipping_record|tbl_extend' => [
                ['shipping_record_id','id'],
                ['client_id','client_id'],
                ['object_name',static::objectName(),'tbl_extend']//只有三个参数表示赋值模式,第三个参数表示来自的仓储
            ]

        ];
    }
}