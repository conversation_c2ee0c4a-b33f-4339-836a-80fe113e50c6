<?php
/**
 * Created by PhpStorm.
 * User: andy
 * Date: 2021/01/26
 * Time: 16:21
 */

namespace common\library\recycle;

use common\library\APIConstant;
use common\library\customer_v3\company\list\CompanyList;
use common\library\common_file\file\CommonFileFilter;
use common\library\invoice\Order;
use common\library\invoice\OrderList;
use common\library\invoice\QuotationList;
use common\library\orm\pipeline\formatter\UserInfoTask;
use common\library\product_v2\ProductAPI;
use common\library\purchase\purchase_order\PurchaseOrderFilter;
use common\library\supplier\SupplierFilter;
use xiaoman\orm\common\Formatter;
use xiaoman\orm\database\data\In;

/**
 * Class RecycleFormatter
 * @package common\library\recycle
 * @method displayReferList(bool $flag)
 * @method displayReferInfo(bool $flag)
 * @method displayDeleteTime(bool $flag)
 * @method displayDeleteUserInfo(bool $flag)
 */
class RecycleFormatter extends Formatter
{
    protected $displayUserId;

    const MAPPING_SETTING = [
        'refer_list' => [
            'build_function' => 'buildReferList',
            'mapping_function' => 'mapReferList',
        ],
        'refer_info' => [
            'mapping_function' => 'mapReferInfo',
        ],
        'delete_time' => ['mapping_function' => 'mapAlias'],
        'delete_user_info' => [
            'task_class' => UserInfoTask::class,
            'user_fields' => ['delete_user_info' => 'user_id']
        ]
    ];

    public function listSetting()
    {
        $this->displayUserId = \User::getLoginUser()->getUserId();
        $this->displayReferList(true);
        $this->displayDeleteTime(true);
        $this->displayDeleteUserInfo(true);
    }

    public function fileListSetting()
    {
        $this->displayUserId = \User::getLoginUser()->getUserId();
        $this->displayReferList(true);
        $this->displayDeleteTime(true);
        $this->displayDeleteUserInfo(true);
    }

    public function fileRecoverSetting()
    {
        $this->displayUserId = \User::getLoginUser()->getUserId();
        $this->displayFields([
            'refer_id',
            'type'
        ]);
        $this->displayReferList(true);
    }

    public function infoSetting()
    {
        $this->displayUserId = \User::getLoginUser()->getUserId();
        $this->displayFields([
            'refer_id',
            'type'
        ]);
        $this->displayReferInfo(true);
        $this->displayDeleteTime(true);
    }

    protected function mapAlias(&$result, $key, $data)
    {
        switch ($key)
        {
            case 'delete_time':
                $result['delete_time'] = $data['create_time'];
                break;
        }
    }

    protected function mapReferInfo(&$result, $key, $data)
    {
        switch ($data['type'])
        {
            case Recycle::PRODUCT:
                $productApi = new ProductAPI($this->clientId, $this->displayUserId);
                $deleteSkuIds = json_decode($data['remark'], true);
                $info = $productApi->recycleInfo($data['refer_id'], $deleteSkuIds);
                break;
            default:
                throw new \ProcessException('The object was not implemented with type:'.$data['type']);
        }
        $result['info'] = $info;
    }

    protected function mapReferList(&$result, $key, $data)
    {
        $result += $this->getPipelinePrepareData($key)[$data['refer_id']] ?? [];
    }

    protected function buildReferList($key, array $data)
    {
        $this->checkFieldsExist(['type', 'refer_id'], $data);

        $map = array_reduce($data, function ($carry, $item)
        {
            $carry[$item['type']][] = $item['refer_id'];
            return $carry;
        }, []);

        $result = [];

        foreach ($map as $type => $ids)
        {
            $processData = [];

            switch ($type)
            {
            case Recycle::MAIL:
                throw new \ProcessException('mail recycle list should use MailFilter join RecycleFilter');
                break;
            case Recycle::CUSTOMER:
                $processData = $this->formatCompany($ids);
                break;
            case Recycle::PRODUCT:
                $processData = $this->formatProduct($ids);
                break;
            case Recycle::DISK_FILE:
                $processData = $this->formatFile($ids);
                break;
            case Recycle::DISK_FOLDER:
            case Recycle::DISK_DOM:
                break;
            case Recycle::ORDER:
                $processData = $this->formatOrder($ids);
                break;
            case Recycle::QUOTATION:
                $processData = $this->formatQuotation($ids);
                break;
            case Recycle::LEAD: // TODO set alias for delete_time
                $processData = $this->formatLead($ids);
                break;
            case Recycle::OPPORTUNITY:
                $processData = $this->formatOpportunity($ids);
                break;
            case Recycle::CASH_COLLECTION:
                $processData = $this->formatCashCollection($ids);
                break;
            // cms
            case Recycle::CMS_ARTICLE:
                $processData = $this->formatArticle($ids);
                break;
            case Recycle::CMS_IMAGE_COLLECTION:
                $processData = $this->formatImageCollection($ids);
                break;
            case Recycle::CMS_MATERIAL_IMAGE:
            case Recycle::CMS_MATERIAL_VIDEO:
            case Recycle::CMS_MATERIAL_FILE:
                $processData = $this->formatMaterial($type, $ids);
                break;
            case Recycle::CMS_PRODUCT:
                $siteId = $data[array_key_last($data)]['site_id'] ?? 0; // 默认数据都是由一个site_id出 如果场景变更 这里也要改
                $processData = $this->formatCmsProduct($siteId, $ids);
                break;
            case Recycle::CMS_INQUIRY:
                $processData = $this->formatInquiry($ids);
                break;
            case Recycle::PURCHASE_ORDER:
                $processData = $this->formatPurchaseOrder($ids);
                break;
            case Recycle::SUPPLIER:
                $processData = $this->formatSupplier($ids);
                break;
            }

            $result += $processData; // if using array_merge the numeric key will be renumbered
        }

        return $result;
    }

    protected function formatCompany(array $ids)
    {
        $list = new CompanyList($this->displayUserId);
        $list->setCompanyIds($ids);
        $list->setIsArchive(0);
        $list->setSkipPrivilege(true);
        $list->getFormatter()->setSpecifyFields(['company_id', 'serial_id', 'name', 'star', 'country', 'order_time', 'trail_status',
            'group_name', 'tel', 'origin_name', 'biz_type', 'address', 'category_ids', 'fax', 'remark', 'external_field_data']);
        return array_column($list->find(), null, 'company_id');
    }

    protected function formatProduct(array $ids)
    {
        $productApi = new ProductAPI($this->clientId, $this->displayUserId);
        $items = $productApi->deleteItems($ids, APIConstant::SCENE_RECYCLE);
        return array_column($items, null, 'product_id');
    }

    protected function formatOrder(array $ids)
    {
        $list = new OrderList($this->displayUserId);
        $list->setOrderIds($ids);
        $list->setSkipPermissionCheck(true);
        $list->setEnableFlag(Order::ENABLE_FLAG_FALSE);
        $list->formatter->listInfoSetting();
        return array_column($list->find(), null, 'order_id');
    }

    protected function formatQuotation(array $ids)
    {
        $list = new QuotationList($this->displayUserId);
        $list->setQuotationIds($ids);
        $list->setSkipPermissionCheck(true);
        $list->setEnableFlag(Order::ENABLE_FLAG_FALSE);
        $list->formatter->listInfoSetting();
        return array_column($list->find(), null, 'quotation_id');
    }

    protected function formatLead(array $ids)
    {
        $leadList = new \common\library\lead\LeadList($this->displayUserId);
        $leadList->setIds($ids);
        $leadList->getFormatter()->setRenderList();
        $leadList->setSkipPrivilege(true);
        $leadList->setShowAllStatusFlag(true);
        $leadList->setIsArchive(null);
        $leadList->setOrderBy('order_time');
        return array_column($leadList->find(), null, 'lead_id');
    }

    protected function formatOpportunity(array $ids)
    {
        $list = new \common\library\opportunity\OpportunityList($this->clientId);
        $list->setViewingUserId($this->displayUserId);
        $list->setOpportunityIds($ids);
        $list->setSkipPermissionCheck(true);
        $list->setEnableFlag(\common\library\opportunity\Opportunity::ENABLE_FLAG_DELETE);
        $list->getFormatter()->listInfoSetting();
        return array_column($list->find(), null, 'opportunity_id');
    }

    protected function formatCashCollection(array $ids)
    {
        $list = new \common\library\cash_collection\CashCollectionList($this->clientId);
        $list->setViewingUserId($this->displayUserId);
        $list->setCashCollectionIds($ids);
        $list->setSkipPermission(true);
        $list->setEnableFlag(\common\library\cash_collection\CashCollection::ENABLE_FLAG_DELETE);
        $list->getFormatter()->listInfoSetting();
        return array_column($list->find(), null, 'cash_collection_id');
    }

    protected function formatArticle(array $ids)
    {
        $articleList = new \common\library\cms\article\ArticleList($this->clientId);
        $articleList->setArticleId($ids);
        $articleList->setEnableFlag(null);
        $articleList->getFormatter()->listInfoSetting();
        return array_column($articleList->find(), null, 'article_id');
    }

    protected function formatImageCollection(array $ids)
    {
        $collectionList = new \common\library\cms\imagecollection\ImageCollectionList($this->clientId);
        $collectionList->setImageCollectionIds($ids);
        $collectionList->setEnableFlag(null);
        $collectionList->getFormatter()->listInfoSetting();
        return array_column($collectionList->find(), null, 'image_collection_id');
    }

    protected function formatMaterial($type, array $ids)
    {
        $mapType = array_flip(\common\library\cms\material\MaterialService::MapType);
        $materialType = $mapType[$type];

        $materialList = new \common\library\cms\material\MaterialList($this->clientId);
        $materialList->setMaterialId($ids);
        $materialList->setType($materialType);
        $materialList->setEnableFlag(null);
        $materialList->getFormatter()->listInfoSetting();
        return array_column($materialList->find(), null, 'material_id');
    }


    protected function formatCmsProduct($siteId, array $ids)
    {
        $productApi = new \common\library\cms\product_v2\ProductAPI($this->clientId, $this->displayUserId);
        $params = [
            'product_id' => $ids,
            'relate_id' => $siteId,
        ];
        $products = $productApi->deletedProducts($params);
        return array_column($products, null, 'product_id');
    }

    protected function formatInquiry(array $ids)
    {
        $inquiryList = new \common\library\cms\inquiry\InquiryList($this->clientId);
        $inquiryList->setInquiryId($ids);
        $inquiryList->setEnableFlag(null);
        $inquiryList->getFormatter()->listInfoSetting();
        return array_column($inquiryList->find(), null, 'inquiry_id');
    }

    protected function formatPurchaseOrder(array $ids)
    {
        $purchaseFilter = new PurchaseOrderFilter($this->clientId);
        $purchaseFilter->purchase_order_id = new In($ids);
        $purchaseOrder = $purchaseFilter->find();
        $purchaseOrder->getFormatter()->listSetting();
        $list = $purchaseOrder->getListAttributes();
        return array_column($list, null, 'purchase_order_id');
    }

    protected function formatSupplier(array $ids)
    {
        $supplierFilter = new SupplierFilter($this->clientId);
        $supplierFilter->supplier_id = new In($ids);
        $supplier = $supplierFilter->find();
        $supplier->getFormatter()->recycleListSetting();
        $list = $supplier->getListAttributes();
        return array_column($list, null, 'supplier_id');
    }


    protected function formatFile(array $ids)
    {
        $commonFileFilter = new CommonFileFilter($this->clientId);
        $commonFileFilter->file_id = new In($ids);
        $commonFile = $commonFileFilter->find();
        $commonFile->getFormatter()->recycleListSetting();
        $list = $commonFile->getListAttributes();
        return array_column($list, null, 'file_id');
    }
}
