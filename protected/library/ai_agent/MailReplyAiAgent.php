<?php

namespace common\library\ai_agent;

use common\library\ai_agent\api\AIClient;
use common\library\ai_agent\api\AiStreamClient;
use common\library\ai_agent\api\DeepThinkingAiStreamClient;
use common\library\ai_agent\BaseAiAgent;
use common\library\ai_agent\exception\AiAgentException;
use common\library\ai_agent\message\AbstractMessageFormat;
use common\library\ai_agent\message\Card;
use common\library\ai_agent\message\component\Button;
use common\library\ai_agent\message\component\FeedBack;
use common\library\ai_service\AiAgentConversationHistoryList;
use common\library\prompt\AiServiceProcessRecordList;
use common\components\BaseObject;
use common\library\ai_agent\message\component\IconText;
use common\library\ai_agent\message\MailReplyOptionsCard;
use common\library\email\Util;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\ai_agent\utils\IncompleteJsonFixer;

class MailReplyAiAgent extends BaseAiAgent
{
    use ChatTrait;

    protected int $mailId = 0;
    protected $mailInfo = [];
    public bool $stream = true;
    public string $lastFixedJson;
    public string $replyLanguage = '';



    public int $questionType = 0;  // 问题类型：1：生成策略 2：使用策略 3：追问
    const QUESTION_TYPE_GENERATE_STRATEGY = 1; // 生成策略
    const QUESTION_TYPE_USE_STRATEGY = 2; // 使用策略

    const QUESTION_TYPE_CONTINUE_ASK = 3; // 追问

    public int $strategyId = 0;
    protected $hookFlag = false;

    protected string $responseContent = '';

    const MAX_MAIL_LENGTH = 4000; // 邮件最大长度，超过则截断


    public function setHookFlag($hookFlag)
    {
        $this->hookFlag = $hookFlag;
    }

    public function getAgentSceneType(): int
    {
        return \AiAgent::AI_AGENT_SCENE_TYPE_MAIL_REPLY;
    }


    public function getMailInfo()
    {
        if (!empty($this->mailInfo)) {
            return $this->mailInfo;
        }
        //获取邮件详情
        $mail = new \common\library\mail\Mail($this->mailId);
        $mail->getFormatter()->webInfoSetting();

        $this->mailInfo = $mail->getAttributes();

        return $this->mailInfo;

    }

    public function getMailType()
    {
        $mail = new \common\library\mail\Mail($this->mailId);
        $mail->getFormatter()->webInfoSetting();

        return $mail->getMailType();
    }

    // 复写该方法，因为ai回信打开首先出策略，其次再是生成回信内容，如果获取第一条历史记录的参数是不完整的，因此要倒序取最新的
    public function loadParams($conversationId)
    {
        $conversationId = empty($conversationId) ? $this->conversationId : $conversationId;
        if (empty($conversationId)) return $this->requestParams;

        $historyList = new AiAgentConversationHistoryList($this->clientId, $this->userId, $this->agentId);
        $historyList->setBusinessType($this->businessType);
        $historyList->setConversationId($conversationId);
        $historyList->setFilterEmptyParams(true);
        $historyList->setOrderBy("history_id");
        $historyList->setOrder("desc");
        $historyList->setLimit(1);
        $list = $historyList->find();

        $lastItem = $list[0] ?? [];

        return $lastItem['params'] ?? [];
    }


    public function setQuestionType(): void
    {
        // question = 0 表示生成策略
        if (empty($this->question)) {
            $this->questionType = self::QUESTION_TYPE_GENERATE_STRATEGY;
        } else {
            $aiProcessRecordList = new AiServiceProcessRecordList($this->clientId, $this->userId, $this->sceneType);
            $aiProcessRecordList->setConversationId($this->conversationId);
            $aiProcessRecordList->setStatus(BaseObject::ENABLE_FLAG_TRUE);
            $aiProcessRecordList->setOrderBy('create_time');
            $aiProcessRecordList->setOrder('desc');
            $recordCount = $aiProcessRecordList->count();
            // 第一条是生成策略，第二条是生成回信。第三条之后都是追问
            // 已经落库的有1条表示当前是第二条
            if ($recordCount == 1) {
                $this->questionType = self::QUESTION_TYPE_USE_STRATEGY;
            } else {
                $this->questionType = self::QUESTION_TYPE_CONTINUE_ASK;
            }

        }
    }

    public function MailReply()
    {

        $this->agentProcessParams = $this->makeAgentProcessParams();

        $this->systemPrompt = iconv('UTF-8', 'UTF-8//IGNORE', $this->buildMailReplySystemPrompt());
        $this->question = iconv('UTF-8', 'UTF-8//IGNORE', $this->buildMailReplyUserPrompt());

        $language = \Yii::app()->language ?? 'zh-CN';
        if ($language != 'zh-CN' && !$this->deepThinking){
            $this->llmModel = $this->promptConfig['other_language_model'] ?? 'azure-openai-gpt-4o';
            $this->llmService->setModel($this->llmModel);
        }


        if ($this->questionType == self::QUESTION_TYPE_GENERATE_STRATEGY) {
            // 生成策略的时候需要开json mode
            $this->llmService->setResponseFormat(['type' => 'json_object']);
        } else if ($this->questionType == self::QUESTION_TYPE_USE_STRATEGY) {
            // 使用策略的时候把question置为0，因为使用策略生成回信的prompt包含了question
            // $this->question = '';
            // 使用策略的时候不需要加载历史记录
            $this->maxHistoryLength = 0;
        }

        $res = $this->callLlm($this->question);

        if ($this->questionType == self::QUESTION_TYPE_GENERATE_STRATEGY) {
            $response =  $this->initAiAgentProcessResponse($res, __FUNCTION__, AiAgentConstants::AI_AGENT_MESSAGE_TYPE_MAIL_REPLY, false);
        } else {
            $response =  $this->initAiAgentProcessResponse($res, __FUNCTION__, AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CARD, false);
        }


        if ($this->stream) {
            $response->setAnswer('');
        } else {
            $this->lastFixedJson = $response->answer;
        }


        return $response;
    }


    public function buildMailReplySystemPrompt(): string
    {
        if ($this->deepThinking) {
            $this->llmService->setModel($this->promptConfig['deepSeekModel'] ?? AIClient::VOLCANO_DEEPSEEK_R1);
            return '';
        }

        $modelKey = $this->hookFlag ? 'hookModel' : 'chatModel';
        $mailType = $this->getMailType();

        if ($this->questionType == self::QUESTION_TYPE_GENERATE_STRATEGY) {
            $this->systemPrompt = $mailType == \Mail::MAIL_TYPE_RECEIVE ? $this->systemPromptMap['customerReplyStrategyPrompt'] : $this->systemPromptMap['salesmanReplyStrategyPrompt'];
            $modelKey = $this->hookFlag ? 'hookModel' : 'chatModel';
            $this->llmService->setModel($this->promptConfig[$modelKey]['replyStrategyModel']);
        } else if ($this->questionType == self::QUESTION_TYPE_USE_STRATEGY && $mailType  == \Mail::MAIL_TYPE_RECEIVE) {
            $this->systemPrompt = $this->systemPromptMap['generateMailPrompt'] ?? '';
            $this->llmService->setModel($this->promptConfig[$modelKey]['generateMailModel']);
        } else if ($this->questionType == self::QUESTION_TYPE_USE_STRATEGY && $mailType == \Mail::MAIL_TYPE_SEND){
            $this->systemPrompt = $this->systemPromptMap['RecoveryCustomerPrompt'] ?? '';
            $this->llmService->setModel($this->promptConfig[$modelKey]['RecoveryCustomerModel']);
        } else if ($this->questionType == self::QUESTION_TYPE_CONTINUE_ASK) {
            $this->systemPrompt = $this->systemPromptMap['continueAskPrompt'] ?? '';
        }

        return $this->replacePlaceholders($this->systemPrompt, $this->buildPromptConfigValueMap());
    }

    public function buildMailReplyUserPrompt(): string
    {
        $mailType = $this->getMailType();
        if ($this->questionType == self::QUESTION_TYPE_GENERATE_STRATEGY) {
            if ($this->deepThinking) {
                $this->userPrompt = $mailType == \Mail::MAIL_TYPE_RECEIVE ? $this->userPromptMap['customerReplyStrategyDeepSeekPrompt'] : $this->userPromptMap['salesmanReplyStrategyDeepSeekPrompt'];
            }else {
                $this->userPrompt = $this->userPromptMap['replyStrategyPrompt'] ?? '';
            }
        } else if ($this->questionType == self::QUESTION_TYPE_USE_STRATEGY && $mailType  == \Mail::MAIL_TYPE_RECEIVE) {
            $this->userPrompt = $this->deepThinking ? $this->userPromptMap['generateMailDeepSeekPrompt'] : $this->userPromptMap['generateMailPrompt'];
        } else if ($this->questionType == self::QUESTION_TYPE_USE_STRATEGY && $mailType == \Mail::MAIL_TYPE_SEND){
            $this->userPrompt = $this->deepThinking ? $this->userPromptMap['RecoveryCustomerDeepSeekPrompt'] : $this->userPromptMap['RecoveryCustomerPrompt'];
        } else if ($this->questionType == self::QUESTION_TYPE_CONTINUE_ASK) {
            $this->userPrompt = $this->deepThinking ? $this->userPromptMap['continueAskDeepSeekPrompt'] : $this->userPromptMap['continueAskPrompt'];
        }

        return $this->replacePlaceholders($this->userPrompt, $this->buildPromptConfigValueMap());
    }

    public function cutMailText(string $plainText): string
    {
        $mailLength = mb_strlen($plainText);
        if ($mailLength > self::MAX_MAIL_LENGTH) {
            $plainText = mb_substr($plainText, 0, self::MAX_MAIL_LENGTH, 'UTF-8');
        }
        return $plainText;
    }
    public function getPromptSpecialValue($key, $value)
    {
        switch ($key)
        {
            case 'mailContentRecord':
                $plainText = $this->getMailInfo()['plain_text'] ?? '';
                $mailContent = $this->cutMailText($plainText);

                // 格式化聊天记录
                $value = $mailContent ."\n";
                break;
            case 'getAdvanceInfo':
                $sender  = strtolower(Util::getPureEmail($this->mailInfo['sender']));
                $receiver = strtolower(Util::getPureEmail($this->mailInfo['receiver']));
                $value = "
Sender:{$sender}
Receiver:{$receiver}
";
                break;
        }

        return $value;
    }

    /**
     * @inheritDoc
     */
    public function process(array $params = [], string $function = '')
    {
        $chatParams = $params['params'] ?? [];
        $this->preCheckParams($chatParams);
        $this->setQuestionType();

        if ($chatParams['is_deep_seek'] ?? 0) {
            $this->llmService = new DeepThinkingAiStreamClient();
            $this->llmService->setTemperature(0.6);
        }elseif(!empty($chatParams['disable_stream'])) {
            $this->stream=false;
            $this->llmService = new AIClient();
            $this->llmService->setModel($this->llmModel);
        }

        $this->agentProcessParams = $params;

        try {
            \common\library\privilege_v3\Helper::checkPermission($this->clientId, $this->userId, PrivilegeConstants::PRIVILEGE_EMAIL_VIEW);
        } catch (\Throwable $exception) {
            throw new AiAgentException(\Yii::t('ai','您没有邮件查看的权限，无法进行邮件质检'), AiAgentException::ERR_NOT_HAS_EMAIL_VIEW_PERMISSION);
        }

        if (empty($function)) {
            $function = 'MailReply';
        }

        $AiAgentProcessResponse = new AiAgentProcessResponse();
        if (method_exists($this, $function)) {
            $aiAgentProcessResponse = $this->$function($params);
        }
        if (empty($params['params']['skip_handle_billing']) || !$params['params']['skip_handle_billing']) {
            $this->handleBilling();
        }

        return $aiAgentProcessResponse;
    }

    public function preCheckParams(array $params) {
        if (empty($params['mail_id'])) {
            throw new AiAgentException(\Yii::t('ai','请选择邮件进行回复'),AiAgentException::ERR_NOT_MAIL_TO_REPLY);
        }
        $this->mailId = $params['mail_id'] ?? 0;
        $this->replyLanguage = empty($params['language']) ? 'en' : $params['language'];
        $this->strategyId = $params['strategy_id'] ?? 0;
        $this->deepThinking = $params['is_deep_seek'] ?? 0;
        // 读参数之后，设置一下QuestionType，QuestionType用来表示是 生成策略、使用策略还是追问
    }

    public function makeAgentProcessParams(): array
    {
        $chatParams = $this->requestParams['params'] ?? [];
        $this->preCheckParams($chatParams);
        $mailInfo = $this->getMailInfo();
        return [
            'mail_id' => $mailInfo['mail_id'] ?? 0,
            'user_mail_id' => $mailInfo['user_mail_id'] ?? 0,
            'user_id' => $mailInfo['user_id'] ?? 0,
            'language' => $this->replyLanguage,
            'sender' => strtolower(Util::getPureEmail($this->mailInfo['sender'])),
            'receiver' => strtolower(Util::getPureEmail($this->mailInfo['receiver'])),
            'question_type' => $this->questionType,
            'lengthOption' => $chatParams['lengthOption'] ?? "Short",
        ];
    }

    public function getMailReplySseObj(AiAgentProcessResponse $aiAgentProcessResponse)
    {
        $answerStr = $this->lastFixedJson ?? '';
        $answerStr = str_replace('```json', '', $answerStr);
        $answerStr = str_replace('```', '', $answerStr);
        $answer = json_decode($answerStr, true);
        $aiAgentProcessResponse->context = [
            'options' => [],
            'language' => $answer['replyLanguage'] ?? '',
            'length_option' => 'Short',
        ];
//        \Yii::app()->language = 'ja-JP';
        $strategies = $answer['replyStrategy'] ?? [];
        // $strategyId一定不能从0开始，因为使用策略生成回信的时候前端会把$strategyId传给后端，后端根据strategyId来决定选用生成回信还是追问的prompt
        $strategyId = 1;
        foreach ($strategies as $strategy) {
            $aiAgentProcessResponse->context['options'][] = [
                'strategy_id' => $strategyId,
                'text' => $strategy['strategyContent'] ?? '',
                'name' => $strategy['strategyName'] ?? '',
            ];
            $strategyId++;
        }

        $aiAgentProcessResponse->context['content'] =  $answer['summaryAnalysis'] ?? '';

        $mailReplyOptionsCard = new MailReplyOptionsCard();
        $mailReplyOptionsCard->enableWatermark = false; // 没有“由AI”生成提示
        $mailReplyOptionsCard->setTitle(new IconText(icon: Button::ICON_AI_MAIL_REPLY, text: \Yii::t('ai','回信')));
        $mailReplyOptionsCard->setSubtitle(new IconText(text: \Yii::t('ai','意图分析')));
        $mailReplyOptionsCard->setContent($aiAgentProcessResponse->context['content'] ?? '');
        $mailReplyOptionsCard->setOptions($aiAgentProcessResponse->context['options'] ?? []);
        if (empty($aiAgentProcessResponse->context['language'])) {
            $language = 'en';
        } else {
            $language = $aiAgentProcessResponse->context['language'];
            $languageCodeList = array_column($mailReplyOptionsCard::getLanguageList(), 'value');
            if (in_array($language, $languageCodeList)) {
                $mailReplyOptionsCard->setLanguage($language);
            }
        }
        $mailReplyOptionsCard->setLanguage($language);
        $mailReplyOptionsCard->setLengthOption($aiAgentProcessResponse->context['length_option'] ?? 0);
        $mailReplyOptionsCard->withRecordId($aiAgentProcessResponse->recordId);
        $mailReplyOptionsCard->withConversationId($this->conversationId);


        $card = new Card();
        $card->setTipsWords(AiAgentConstants::AGENT_COMMENT_AI_MAIL_REPLY_TIPS['title'], '', AiAgentConstants::AGENT_COMMENT_AI_MAIL_REPLY_TIPS['list']);
        $card->enableWatermark = false; // 没有“由AI”生成提示
        $card->setHeaderLeft([
            new Button(type: 'translate', icon: '', text: "", event: '', params: []),
        ]);
        $card->setLeftFooter([
            new Button(icon: 'copy', event: Button::EVENT_COPY),
            new Button(icon: 'Back', text: \Yii::t('ai',"填入邮件"), event: Button::EVENT_MAIL_WRITE_BACK),
        ]);
        $card->setRightFooter([
            new FeedBack(icon: 'feedback', event: FeedBack::EVENT_FEEDBACK, params: ['record_id' => $aiAgentProcessResponse->recordId]),
        ]);

        if (Helper::checkCreateSpeechcraftFlag($this->clientId, $this->userId))
        {
            $card->setOutSideFooterLeft([
                new Button(text: \Yii::t('ai','录入谈单指南'), event: Button::EVENT_SAVE_TO_GUIDE, params: ['needCleanChatList' => false, 'content' => ''])
            ]);
        }

        if (!$this->deepThinking && $this->questionType != self::QUESTION_TYPE_CONTINUE_ASK) {
            $card->setOutSideFooterLeft(array_merge($card->outsideFooterLeft, [
                 new Button(
                text: \Yii::t('ai', 'DeepSeek-R1 深度分析'),
                event: Button::EVENT_OPEN_DEEPSEEK_R1,
                params: [
                    'recordId' => $aiAgentProcessResponse->recordId,
                    'sceneType' => $this->getAgentSceneType(),
                    'options' => $this->agentProcessParams,
                ],
                history: false
            )
            ]));
        }

        $card->withRecordId($aiAgentProcessResponse->recordId);
        $card->withConversationId($this->conversationId);
        return [
            AiAgentConstants::AI_AGENT_MESSAGE_TYPE_MAIL_REPLY => $mailReplyOptionsCard,
            AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CARD => $card,
        ];
    }

    public function getMailText()
    {
        $plainText = $this->getMailInfo()['plain_text'] ?? '';
        $mailContent = $this->cutMailText($plainText);

        // 格式化聊天记录
        $value = $mailContent ."\n";
        return $value;
    }

    public function getSalemanEmail()
    {
        $receiver = strtolower(Util::getPureEmail($this->mailInfo['mail_type'] == \Mail::MAIL_TYPE_RECEIVE ? $this->mailInfo['receiver'] : $this->mailInfo['sender']));
        return $receiver;
    }

    public function getReceiver()
    {
        return strtolower(Util::getPureEmail($this->mailInfo['receiver']));
    }

    public function getSender()
    {
        return strtolower(Util::getPureEmail($this->mailInfo['sender']));
    }

    public function getReceiveTime()
    {
        return $this->mailInfo['receive_time'];
    }

    public function getQuestion()
    {
        return $this->question;
    }

    public function getSubject()
    {
        return $this->mailInfo['subject'];
    }

    public function getRole()
    {
        return $this->mailInfo['mail_type'] == \Mail::MAIL_TYPE_RECEIVE ? 'customer' : 'salesman';
    }

    public function getCustomerEmail()
    {
        $sender  = strtolower(Util::getPureEmail($this->mailInfo['mail_type'] == \Mail::MAIL_TYPE_RECEIVE ? $this->mailInfo['sender'] : $this->mailInfo['receiver']));
        return $sender;
    }

    public function getReplyStrategy()
    {
        return $this->question;
    }

    public function getReplySummary()
    {
        // 获取这个会话的第一条记录
        $aiProcessRecordList = new AiServiceProcessRecordList($this->clientId, $this->userId, $this->sceneType);
        $aiProcessRecordList->setConversationId($this->conversationId);
        $aiProcessRecordList->setStatus(BaseObject::ENABLE_FLAG_TRUE);
        $aiProcessRecordList->setOrderBy('create_time');
        $aiProcessRecordList->setOrder('asc');
        $aiProcessRecordList->setLimit(1);
        $aiProcessRecordList = $aiProcessRecordList->find();
        $latestProcessRecord = reset($aiProcessRecordList);

        $answerStr = $latestProcessRecord['answer'] ?? '';
        $answer = $this->tryParseJson($answerStr);

        return $answer['summaryAnalysis'] ?? '';
    }
    public function getLanguage(){
        // 兼容app传入中文的情况
        $language = $this->agentProcessParams['language'] ?? '英文';
        $languageCode = $this->agentProcessParams['language'] ?? 'en';
        foreach (AiAgentConstants::languageList as $languageInfo) {
            if (!empty($languageInfo['code']) && !empty($languageInfo['en']) && ($languageCode == $languageInfo['code'] || $languageCode == $languageInfo['en']) ) {
                $language =  $languageInfo['en'];
                break;
            }
        }

        return $language;
    }

    public function needTranslateContent() :bool
    {
        if (!$this->deepThinking)
            return false;
        // 非回复策略无需翻译
        if ($this->questionType == self::QUESTION_TYPE_GENERATE_STRATEGY)
            return false;
        //目标语言为英文，无需翻译
        if (in_array($this->getLanguage(), ['英文', 'en', 'English']))
            return false;

        return true;
    }

    public function getReplyStyle()
    {
        // 获取这个会话的第一条记录
        $aiProcessRecordList = new AiServiceProcessRecordList($this->clientId, $this->userId, $this->sceneType);
        $aiProcessRecordList->setConversationId($this->conversationId);
        $aiProcessRecordList->setStatus(BaseObject::ENABLE_FLAG_TRUE);
        $aiProcessRecordList->setOrderBy('create_time');
        // 历史bug，AiServiceProcessRecordList构造方法中已经写desc，不写asc默认取到最新的
        $aiProcessRecordList->setOrder('asc');
        $aiProcessRecordList->setLimit(1);
        $aiProcessRecordList = $aiProcessRecordList->find();
        $latestProcessRecord = reset($aiProcessRecordList);

        $answerStr = $latestProcessRecord['answer'] ?? '';

        $answer =$this->tryParseJson($answerStr);

        return $answer['responseStyle'] ?? '';
    }

    public function getlengthLevel()
    {
        return $this->agentProcessParams['lengthOption'] ?? 'Short';
    }

    public function getContent() :string
    {
        return $this->responseContent;
    }

    public function getCurrentTime() :string
    {
        return date("Y-m-d H:i:s");
    }

    public function getTradeStage() :string
    {
        // 获取这个会话的第一条记录
        $aiProcessRecordList = new AiServiceProcessRecordList($this->clientId, $this->userId, $this->sceneType);
        $aiProcessRecordList->setConversationId($this->conversationId);
        $aiProcessRecordList->setStatus(BaseObject::ENABLE_FLAG_TRUE);
        $aiProcessRecordList->setOrderBy('create_time');
        // 历史bug，AiServiceProcessRecordList构造方法中已经写desc，不写asc默认取到最新的
        $aiProcessRecordList->setOrder('asc');
        $aiProcessRecordList->setLimit(1);
        $aiProcessRecordList = $aiProcessRecordList->find();
        $latestProcessRecord = reset($aiProcessRecordList);

        $answerStr = $latestProcessRecord['answer'] ?? '';

        $answer = $this->tryParseJson($answerStr);

        return $answer['tradeStage'] ?? '';
    }
    
    public function onReceiveStream(int $i, AbstractMessageFormat $message, string $str, AiAgentProcessResponse $response, string $fullText): void
    {
        if ($this->deepThinking) {
            $this->handleDeepSeekStream($i, $message, $str, $response, $fullText);
        }else {
            $this->handleStream($i, $message, $str, $response, $fullText);
        }
    }


    public function handleDeepSeekStream(int $i, AbstractMessageFormat $message, string $str, AiAgentProcessResponse $response, string $fullText)
    {
        /** @see self::getMailReplySseObj() */
        $messages = $this->getMessageObj($response);
        $card = $messages[$response->messageType];
        $card->setAnswerHistoryId($response->historyId);
        $skeletonMessage = $card->getSkeletonMessage(); // 空卡片

        // 发送初始化消息
        if ($i === 0) {
            $skeletonMessage['status'] = AiAgentConstants::MESSAGE_STATUS_INIT;
            $this->sseResponse->writeJson($skeletonMessage);
        }

        $res = [];
        usleep(25000);
        // 匹配思考过程
        $pattern = "/<REASONING_CONTENT_START>(.*?)<REASONING_CONTENT_END>/s";
        preg_match($pattern, $str, $matches);
        if (isset($matches[1])) {
            $reasoningContent = $matches[1];
            $res = $card->formatMessage([]);
            $res['context']['in_deep_thinking'] = 1;
            $res['context']['reasoning_content'] = $reasoningContent;
            $this->sseResponse->writeJson($res);
        }

        if ($response->messageType == AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CARD) {
            // 匹配邮件内容
            $pattern = '/<MAIN_CONTENT_START>(.*?)<MAIN_CONTENT_END>/s';
            preg_match($pattern, $str, $matches);
            if (isset($matches[1])) {

                $content = $matches[1];
                $this->responseContent .= $content;
                // 不需要翻译则直接流式输出，否则先不输出，在beforeCloseMessage翻译后输出
                if (!$this->needTranslateContent()) {
                    $res['context']['in_deep_thinking'] = 0;
                    $res['status'] = 1;
                    $res['context']['content'] = $content;
                    $this->sseResponse->writeJson($res);
                }
            }

        }elseif ($response->messageType == AiAgentConstants::AI_AGENT_MESSAGE_TYPE_MAIL_REPLY) {

            // 匹配回复策略
            $pattern = '/<MAIN_CONTENT_START>(.*?)<MAIN_CONTENT_END>/s';
            preg_match($pattern, $str, $matches);
            if (isset($matches[1])) {
                $content = $matches[1];
                $this->responseContent .= $content;
                // 后续处理与原有逻辑一致
                $this->handleStream($i, $message, $str, $response, $this->responseContent);
            }
        }
    }

    public function beforeCloseMessage(AiAgentProcessResponse $response, string $fullText): void
    {
        if (!$this->needTranslateContent()) return;

        $text = '';
        $configValueMap = $this->buildPromptConfigValueMap();
        $systemPrompt = $this->replacePlaceholders($this->promptConfig['translateSystemPrompt'] ?? '', $configValueMap);
        $userPrompt = $this->replacePlaceholders($this->promptConfig['translateUserPrompt'] ?? '', $configValueMap);
        $streamClient = new AiStreamClient();
        $streamClient->setModel(AiAgentConstants::AI_MODEL_VOLCANO_DEEPSEEK_V3);
        $streamClient->setSystemPrompt($systemPrompt);
        $streamClient->setQuestion($userPrompt);
        $generator = $streamClient->chatCompletions();

        $messages = $this->getMessageObj($response);
        $message = $messages[$response->messageType];

        foreach ($generator as $i => $characters) {
            $text .= $characters;
            // 因为翻译再输出理论上不算第一个字符输出，因此i不能为0否则会初始化卡片，前端会渲染异常
            $this->handleStream($i+1, $message, $characters, $response, $text);
        }

       $this->responseContent = $streamClient->getGptResponse();
    }

    public function generateHistoryContent(AiAgentProcessResponse $aiAgentProcessResponse): string
    {
      $historyContent = parent::generateHistoryContent($aiAgentProcessResponse);
      $historyContent = json_decode($historyContent, true);
      // dk-r1历史记录保存思考过程
      if ($this->deepThinking) {
          if (in_array($this->questionType, [self::QUESTION_TYPE_USE_STRATEGY, self::QUESTION_TYPE_CONTINUE_ASK])) {
              $historyContent['content'] = $this->responseContent;
          }
          $historyContent['reasoning_content'] = $this->llmService->getReasoningContent();
      }
      return json_encode($historyContent, JSON_UNESCAPED_UNICODE);
    }

    public function handleStream(int $i, AbstractMessageFormat $message, string $str, AiAgentProcessResponse $response, string $fullText)
    {
        if ($response->messageType == AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CARD) {
            if ($i === 0) {
                $initMsg = $message->getSkeletonMessage();
                if (null !== $initMsg) {
                    $this->sseResponse->writeJson($initMsg);
                }
            }
            $responseFormat = [
                'context' => [
                    'content' => $str,
                    'in_deep_thinking' => 0
                ],
                'status' => AiAgentConstants::MESSAGE_STATUS_PROCESSING
            ];
            usleep(25000);  // 25ms
            $this->sseResponse->writeJson($responseFormat);
            return;
        }

        $fullText =  str_replace('```json', '', $fullText);
        $fullText =  str_replace('```', '', $fullText);
        $fixer = new IncompleteJsonFixer($fullText);
        $fixedJson = $fixer->fixIncompleteJson();
        $isValidJson = json_decode($fixedJson) !== null;
//        $fullText = $this->tryParseJson($fullText);
//        $fullText = $fixer->fixIncompleteJson();
//        $isValidJson = !empty($fullText);
        if ($isValidJson) {
            $this->lastFixedJson = $fixedJson; // 保存当前修复后的 JSON 作为上一次的返回结果
        }

        $messages = $this->getMessageObj($response);
        $card = $messages[AiAgentConstants::AI_AGENT_MESSAGE_TYPE_MAIL_REPLY];
        $card->setAnswerHistoryId($response->historyId);
        $card->setStreamType('overwrite');
        $skeletonMessage = $card->getSkeletonMessage();

        $status = $i == 0 ? AiAgentConstants::MESSAGE_STATUS_INIT : AiAgentConstants::MESSAGE_STATUS_PROCESSING;

        $skeletonMessage['status'] = $status;
        $skeletonMessage['context']['in_deep_thinking'] = 0;

        $this->sseResponse->writeJson($skeletonMessage);
    }

    public function getPreMessageList(array $params)
    {
        return [
            [
                'role' => AiAgentConstants::AI_AGENT_ROLE_TYPE_USER,
                'message_type' => AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT,
                'context' => [
                    'content' => \Yii::t('ai', AiAgentConstants::mailReplyImmediateSendContent),
                ],
                'continue_question' => true,
            ],
        ];
    }

    public function checkAigcRiskInfo(string $content, int $role, int $historyId, int $processRecordId = 0, string $contentLanguage = AiAgentConstants::AI_ICBU_RISK_LANGUAGE_ZH)
    {
        // TODO 回信追问接入风控，其它场景暂时不风控,等产品后续调整后再移除此逻辑
        if($this->questionType == self::QUESTION_TYPE_CONTINUE_ASK){
            parent::checkAigcRiskInfo($content, $role, $historyId, $processRecordId, $contentLanguage);
        }
    }

};
