<?php

namespace common\library\ai_agent;

use ArrayUtil;
use common\library\ai_agent\exception\AiAgentException;
use common\library\ai_agent\message\Card;
use common\library\ai_agent\message\component\Button;
use common\library\ai_agent\message\component\FeedBack;
use common\library\ai_agent\message\component\IconText;
use common\library\chat_assistant\identity\Identity;
use common\library\chat_assistant\identity\IdentityAPI;
use common\library\sns\customer\CustomerContactService;
use InnerApiException;
use LogUtil;

class ChatArchiveAiAgent extends BaseAiAgent
{
    use ChatTrait;

    // 描述信息
    const KEY_DESC_MAPPING = [
        'lead' => [
            'company_name'  => '公司',
            'homepage'      => '官网',
            'country'       => '国家地区',
            'address'       => '地址',
        ],
        'customer' => [
            'name'          => '姓名',
            'email'         => '邮箱',
            'tel_list'      => '联系电话',
            'post'          => '职位',
            'contact'       => [
                'facebook'      => 'Facebook',
                'linkedin'      => 'LinkedIn',
                'skype'         => 'Skype',
                'instagram'     => 'Instagram',
                'whatsapp'      => 'WhatsApp',
                'twitter'       => 'Twitter',
            ],
        ],
    ];

    const FIELD_MAPPING = [
        'companyName'   => 'lead.company_name',
        'companyHashId' => 'companyHashId',
        'website'       => 'lead.homepage',
        'country'       => 'lead.country',
        'address'       => 'lead.address',

        'customerName'  => 'customer.name',
        'email'         => 'customer.email',
        'telephone'     => 'customer.tel_list',
        'facebook'      => 'customer.contact.facebook',
        'linkIn'        => 'customer.contact.linkedin',
        'skype'         => 'customer.contact.skype',
        'instagram'     => 'customer.contact.instagram',
        'whatsApp'      => 'customer.contact.whatsapp',
        'twitter'       => 'customer.contact.twitter',
    ];

    const KEY_VALUE_WRAPPER = <<<'HTML'
<div class='flex mb-8px last:mb-0'><span class='w-100px text-medium-8'>%s: </span><span class='w-[calc(100%%-100px)] text-medium-10'>%s</span></div>
HTML;

    const RESPONSE_FORMAT_RICH_TEXT = 'rich_text';
    const RESPONSE_FORMAT_KEY_LABEL = 'key_label';

    protected string $responseFormat = self::RESPONSE_FORMAT_RICH_TEXT;

    public function setResponseFormat(string $responseFormat)
    {
        $this->responseFormat = $responseFormat;
    }

    public function getAgentSceneType(): int
    {
        return \AiAgent::AI_AGENT_SCENE_TYPE_CHAT_ARCHIVE;
    }

    public function process(array $params = [], string $function = ''): AiAgentProcessResponse
    {
        $this->checkChatParams($params);
        if (empty($function)) {
            $function = "chatArchive";
        }

        $aiAgentProcessResponse = new AiAgentProcessResponse();
        if (method_exists($this, $function)) {
            $aiAgentProcessResponse = $this->$function($params);
        }
        $this->handleBilling();
        return $aiAgentProcessResponse;
    }

    public function chatArchive($params)
    {
        $chatParams = $params['params'] ?? [];
        $this->agentProcessParams = $chatParams;
        $this->agentProcessParams['question'] = $this->question;

        $question = $this->buildChatArchivePrompt();
        [$snsId, $userSnsId] = Helper::convertChatParams($this->clientId, $this->agentProcessParams);

        $response = $this->callLlm($question);

        $response = $this->handleResponse($response, $snsId);

        $result = $this->initAiAgentProcessResponse($response, __FUNCTION__, AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CARD, false);

        $result->context = [
            'lead' => $response['lead'] ?? [],
            'customer' => $response['customer'] ?? [],
        ];

        return $result;

    }

    /**
     * 尝试从沟通渠道获取特定客户信息(优先于LLM返回), 从leads数据库匹配信息
     * @param int $clientId
     * @param int $userId
     * @param array $lead
     * @param array $customer
     * @return array
     */
    public static function fillChannelInfoAndLeadsData(int $clientId, int $userId, array $lead, array $customer)
    {
        // NOTE: 尝试从leads数据库中查询其他信息
        if (!empty($lead['company_name']) || !empty($lead['homepage']) || !empty($customer['email']) || !empty($customer['tel_list'])) {

            $companyName = $lead['company_name'] ?? '';
            $email = $customer['email'] ?? '';
            $telList = $customer['tel_list'] ?? '';
            $domain = '';

            // 从官网中获取域名
            if (!empty($company['homepage'])) {
                $info = parse_url($company['homepage']);
                $domain = $info['host'] ?? '';
            }

            // 获取邮箱后缀作为域名(过滤掉公共邮箱域名)
            if (empty($domain) && !empty($email)) {
                $emailDomain = \EmailUtil::getDomain($email);
                if (!\common\library\email\CommonDomain::check($emailDomain)) {
                    $domain = $emailDomain;
                }
            }

            if (
                !empty($email) || !empty($telList) || !empty($companyName) || !empty($domain)
            ) {
                $companies = [];
                try {
                    $companyApi = new \common\library\discovery\api\Company($clientId, $userId);
                    $companyApi->setRetryTime(0); // 不重试，避免超时
                    $companies = $companyApi->getCompaniesByCondition($companyName, $domain, $email);

                } catch (InnerApiException $e) {
                    // 降级处理，不影响主流程
                    LogUtil::getLogger("[AI一键建档] 查询leads数据库失败，err: {$e->getMessage()}, companyName: {$companyName}, domain: {$domain}, email: {$email}, telList: " . json_encode($telList));
                }

                if ($companies) {
                    // 取第一个数据
                    $companyInfo = $companies['companyInfo'][0] ?? [];

                    empty($lead['company_name']) && !empty($companyInfo['companyName']) && $lead['company_name'] = $companyInfo['companyName'];
                    empty($lead['company_hash_id']) && !empty($companyInfo['companyHashId']) && $lead['company_hash_id'] = $companyInfo['companyHashId'];
                    empty($lead['country']) & !empty($companyInfo['country']) && $lead['country'] = $companyInfo['country'];
                    empty($lead['homepage']) && !empty($companyInfo['homepage']) && $lead['homepage'] = $companyInfo['homepage'];

                    if (empty($lead['address']) && !empty($companyInfo['address'])) {
                        $lead['address'] = ($companyInfo['address'] ?? '')
                            . ($companyInfo['city'] ?? '')
                            . ($companyInfo['province'] ?? '')
                            . ($companyInfo['postcode'] ?? '')
                            . ($companyInfo['country'] ?? '');
                    }

                    // NOTE: 域名匹配 或 公司名匹配，不使用联系人信息
                    $matchReason = $companies['matchReason'] ?? '';
                    if ($matchReason != 'DOMAIN' && !str_starts_with($matchReason, 'COMPANY_NAME')) {
                        $contactInfo = $companyInfo['contact'] ?? [];
                        empty($customer['name']) && !empty($contactInfo['name']) && $customer['name'] = $contactInfo['name'];
                        empty($customer['email']) && !empty($contactInfo['email'][0]) && $customer['email'] = $contactInfo['email'][0];
                        empty($customer['post']) && !empty($contactInfo['jobTitle']) && $customer['post'] = $contactInfo['jobTitle'];
                        empty($customer['tel_list']) && !empty($contactInfo['phones']) && $customer['tel_list'] = $contactInfo['phones']?'':$contactInfo['phones'][0];
                    }
                }
            }
        }

        return ['lead' => $lead, 'customer' => $customer];
    }

    public function handleResponse($response, $snsId)
    {
        // 未获取新建信息，提示异常信息
        ['lead' => $lead, 'customer' => $customer] = Helper::parseChatAIArchiveAnswer($response['answer']);

        // 尝试从沟通渠道获取特定信息(优先于LLM返回), 从leads数据库匹配信息
        ['lead' => $lead, 'customer' => $customer] = self::fillChannelInfoAndLeadsData($this->clientId, $this->userId, $lead, $customer);

        if (empty($lead) && empty($customer)) {
//            throw new AiAgentException(AiAgentException::ERROR_MSG_MAP[AiAgentException::ERR_CHAT_ARCHIVE_NO_CUSTOMER_INFO], AiAgentException::ERR_CHAT_ARCHIVE_NO_CUSTOMER_INFO);
            throw new AiAgentException(\Yii::t('ai', AiAgentException::ERROR_MSG_MAP[AiAgentException::ERR_CHAT_ARCHIVE_NO_CUSTOMER_INFO]), AiAgentException::ERR_CHAT_ARCHIVE_NO_CUSTOMER_INFO);
        }

        self::updateIdentity($snsId, $lead);

        switch ($this->responseFormat) {
            case self::RESPONSE_FORMAT_KEY_LABEL:
                $response['answer'] = $this->buildLabelResponse($lead, $customer);
                break;
            case self::RESPONSE_FORMAT_RICH_TEXT:
            default:
                $response['answer'] = $this->buildRickTextResponse($lead, $customer);
                break;
        }

        // 补充表单数据
        $response['lead'] = $lead;
        $response['customer'] = $customer;

        return $response;
    }

    public function buildLabelResponse(array $lead, array $customer)
    {
        $labels = [];
        foreach ($lead as $key => $value) {
            if (empty(self::KEY_DESC_MAPPING['lead'][$key])) {
                continue;
            }
            $labels[] = self::KEY_DESC_MAPPING['lead'][$key];
        }
        foreach ($customer as $key => $value) {
            if (empty(self::KEY_DESC_MAPPING['customer'][$key])) {
                continue;
            }
            if ($key == 'contact') {
                foreach ($value as $k => $v) {
                    if (empty(self::KEY_DESC_MAPPING['customer']['contact'][$v['type']])) {
                        continue;
                    }
                    $labels[] = self::KEY_DESC_MAPPING['customer']['contact'][$v['type']];
                }
                continue;
            }
            $labels[] = self::KEY_DESC_MAPPING['customer'][$key];
        }
        $answer = implode('】【', $labels);
        if ($answer) {
            $answer = \Yii::t('ai', 'AI填充信息') . '：【' . $answer . '】';
        }
        return $answer;
    }

    public function buildRickTextResponse(array $lead, array $customer)
    {
        // 格式化
        $reply = [];

        foreach ($lead as $key => $value) {
            if (empty(self::KEY_DESC_MAPPING['lead'][$key])) {
                continue;
            }

            if ($key == 'country') {
                $value = \Util::removeCountryFlagEmoji($value);
                $country = \CountryService::checkNameInTable(trim($value));
                if (empty($country) || in_array($country['alpha2'], ['HK', ' MO', 'TW'])) {
                    continue;
                }
                $value = $country['country_name'];
            }

            is_array($value) && $value = implode(',', $value);
            $reply[] = sprintf(self::KEY_VALUE_WRAPPER, self::KEY_DESC_MAPPING['lead'][$key], $value);
        }
        foreach ($customer as $key => $value) {
            if (empty(self::KEY_DESC_MAPPING['customer'][$key])) {
                continue;
            }

            // 社交账号特殊处理
            if ($key == 'contact') {
                foreach ($value as $item) {
                    $reply[] = sprintf(self::KEY_VALUE_WRAPPER, self::KEY_DESC_MAPPING['customer'][$key][$item['type']], $item['value']);
                }
                continue;
            }

            // 电话号码
            if ($key == 'tel_list' && !empty($value) && is_array($value)) {
                $value = $value[0];
            }

            is_array($value) && $value = implode(',', $value);
            $reply[] = sprintf(self::KEY_VALUE_WRAPPER, self::KEY_DESC_MAPPING['customer'][$key], $value);
        }
        return implode('', $reply);
    }

    public function buildChatArchivePrompt()
    {
        return $this->replacePlaceholders($this->userPrompt, $this->buildPromptConfigValueMap());
    }

    public function getPromptSpecialValue($key, $value)
    {
        switch ($key)
        {
            case 'chattingRecordPrompt':
                // 格式化聊天记录
                $channelConversationId = $this->agentProcessParams['conversation_id'] ?? 0;

                $messageList = Helper::getChatPrompt($this->clientId, $this->userId, $channelConversationId, $this->agentProcessParams, 50);
                $messageList = array_filter($messageList, fn ($item) => !str_starts_with($item, 'Salesman:'));
                if (empty($messageList)) {
                    throw new AiAgentException(\Yii::t('ai', AiAgentException::ERROR_CODE_TO_USER_MSG[AiAgentException::ERR_CHAT_ARCHIVE_NO_CUSTOMER_INFO]) ?? '', AiAgentException::ERR_CHAT_ARCHIVE_NO_CUSTOMER_INFO);
                }

                $value = implode("\n", $messageList);
        }

        return $value;
    }

    public function makeAgentProcessParams(): array
    {
        return $this->agentProcessParams;
    }

    public function getChatArchiveSseObj(AiAgentProcessResponse $aiAgentProcessResponse): array
    {
        // 流式输出
        $card = new Card();
        $card->setTitle(new IconText(icon: Button::ICON_ARCHIVE_CUSTOMER, text: \Yii::t('ai', 'AI新建询盘')));
        $card->setSubtitle(new IconText(text: \Yii::t('ai', '根据沟通内容及OKKI Leads大数据，已为你生成以下访客询盘信息')));
        $card->setLeftFooter([
            new Button(event: Button::EVENT_COPY),
            new Button(text: \Yii::t('ai', '新建询盘'), event: Button::EVENT_CHAT_ARCHIVE_INQUIRY,
                params: ['record_id' => $aiAgentProcessResponse->recordId, 'context' => $aiAgentProcessResponse->context]),
        ]);
        $card->setRightFooter([
            new FeedBack(icon: 'feedback', event: FeedBack::EVENT_FEEDBACK, params: ['record_id' => $aiAgentProcessResponse->recordId]),
        ]);
        $card->withRecordId($aiAgentProcessResponse->recordId);
        $card->withConversationId($this->conversationId);

        return [AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CARD => $card];
    }

    public function getPreMessageList(array $params)
    {
        return [
            [
                'role' => AiAgentConstants::AI_AGENT_ROLE_TYPE_USER,
                'message_type' => AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT,
                'context' => [
                    'content' => \Yii::t('ai', AiAgentConstants::AI_ARCHIVE_IN_PRESET_MESSAGE),
                ],
                'continue_question' => true
            ],
        ];
    }

    /**
     * 更新访客关联的身份信息
     * @param $identifier
     * @param $lead
     * @param int $platform
     * @return void
     * @throws \xiaoman\orm\exception\OrmException
     */
    public function updateIdentity($identifier, $lead , int $platform = 6)
    {
        $user = \User::getLoginUser();

        $identity = new Identity($user->getClientId());
        $identity->loadByIdentifier($platform, $identifier);

        if ($identity->isNew()) {
            return;
        }

        $updateData = [];
        if (!empty($lead['company_name']) || !empty($lead['company_hash_id'])) {
            $updateData = [
                'company_name'      => $lead['company_name'] ?? '',
                'company_hash_id'   => $lead['company_hash_id'] ?? '',
            ];
        }

        $api = new IdentityAPI($user->getClientId(), $user->getUserId());
        $api->update($identity->identity_id, $updateData);

        LogUtil::info('update identity', ['identity' => $identity->identity_id, 'updateData' => $updateData]);
    }

}
