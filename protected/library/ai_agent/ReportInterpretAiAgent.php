<?php

namespace common\library\ai_agent;


use AiServiceProcessModel;
use common\library\ai_agent\analysis_record\AnalysisRecordAPI;
use common\library\ai_agent\api\AIClient;
use common\library\ai_agent\config\AssetAnalysisConfig;
use common\library\ai_agent\exception\AiAgentException;
use common\library\ai_agent\interface\AsyncAnalysisInterface;
use common\library\ai_agent\jobs\AsyncAnalysisJob;
use common\library\ai_agent\trace\TraceConstant;
use common\library\ai_agent\trait\AsyncAnalysisTrait;
use common\library\ai_agent\trait\ReportAnalysisTrait;
use common\library\ai_service\AiServiceRecordFeedback;
use common\library\alibaba\customer\AlibabaCompanyRelation;
use common\library\async_task\AsyncTask;
use common\library\customer\Company;
use common\library\prompt\AiServiceProcessRecordList;
use common\library\queue_v2\job\BaseJob;
use PgActiveRecord;

class ReportInterpretAiAgent extends BaseAiAgent implements AsyncAnalysisInterface
{
    use ReportAnalysisTrait;
    use AsyncAnalysisTrait;

    protected $companyId;
    public int $taskId;
    protected bool $stream = true;
    protected bool $deepThinking = true;
    protected int $supportDeepAnalysis = 0;
    protected array $config;
    protected $skipRichnessCheck = false;

    protected $triggerType;
    protected $richnessReportKeys = [];

    protected $dryRun = true;
    const NOT_ENOUGH_ASSET_ANALYSIS_DATA_ROW = 2;

    const NOT_ENOUGH_ASSET_ANALYSIS_DATA_PERCENT = 0.5;
    public string $language;

    public $selllerAccountId = 0;

    public $alibabaCompanyId = 0;


    //1张报表丰富才算丰富
    const RICHNESS_COUNT_LIMIT = 1;

    const DEFAULT_PROMPT = 'default';
    const CUSTOMER_ANALYSIS_PROMPT = 'customerAnalysis';
    const QUESTION_RECOMMEND_PROMPT = 'questionRecommend';

    const CUSTOMER_FLAG = 'customFlag';
    const PRODUCT_FLAG = 'productFlag';
    const EMAIL_FLAG = 'emailFlag';
    const EDM_FLAG = 'edmFlag';
    const FOLLOW_UP_FLAG = 'followUpFlag';
    const ALIBABA_CUSTOMER_FLAG = 'alibabaCustomerFlag'; // 阿里国际站数据

    // feed_id
    const CUSTOMER_TRANSLATION_TREND_FEED_ID = 3;

    const TREND_STATUS_UP = 1;

    protected array $richnessSceneMap = [
        self::CUSTOMER_FLAG => false,
        self::PRODUCT_FLAG => false,
        self::EMAIL_FLAG => false,
        self::EDM_FLAG => false,
        self::FOLLOW_UP_FLAG => false,
        self::ALIBABA_CUSTOMER_FLAG => false
    ];

    // 报表数据是否全部稀疏
    protected $isAllSparse = true;

    // 在这个map下 某个场景需要满足以下report_key 有一个是丰富的 即可查询
    const CHECK_REPORT_KEY_MAP = [
        self::CUSTOMER_FLAG => ['kh33', 'kh34'],
        self::PRODUCT_FLAG => ['cp1', 'cp25', 'cp23', 'cp2', 'cp3', 'cp27', 'cp28', 'cp29', 'cp30', 'cp31', 'cp32', 'cp33', 'cp34', 'cp35', 'cp36', 'cp14', 'cp26', 'cp24', 'cp15', 'cp16'],
        self::EMAIL_FLAG => ['yx1'],
        self::EDM_FLAG => ['khedm1'],
        self::FOLLOW_UP_FLAG => ['khgj2'],
        self::ALIBABA_CUSTOMER_FLAG => [] //没有报表
    ];

    protected $translateFlag = false;

    protected $customerProfileData = [];

    public function __construct(int $clientId, int $userId)
    {
        parent::__construct($clientId, $userId);
        \User::getLoginUser();
        $this->config = $this->getInterpretConfig();

        //reset system prompt
        if (!empty($this->systemPrompt))
        {
            $systemPromptJson = json_decode($this->systemPrompt, true);

            if ($systemPromptJson != null) {
                $this->systemPromptMap = $systemPromptJson;
                $this->systemPrompt = $this->systemPromptMap[self::DEFAULT_PROMPT] ?? '';
            }
        }

        if (!empty($this->userPrompt))
        {
            $userPromptJson = json_decode($this->userPrompt, true);

            if ($userPromptJson != null) {
                $this->userPromptMap = $userPromptJson;
            }
        }
        $this->language = $this->getUserLanguage();
        $this->translateFlag = !in_array($this->language,['zh-CN','zh-TW']);
    }

    public function getAgentSceneType(): int
    {
        return \AiAgent::AI_AGENT_SCENE_TYPE_REPORT_INTERPRET;
    }

    public function process(array $params = [], string $function = '')
    {
        if (empty($function)) {
            $function = "dataAnalysis";
        }

        $aiAgentProcessResponse = $this->$function($params);

        $this->readFromLLM($aiAgentProcessResponse);
        if ($this->triggerType == AiAgentConstants::REPORT_ANALYSIS_TRIGGER_TYPE_MANUAL)
        {
            $this->handleBilling();
        }

        return $aiAgentProcessResponse;

    }

    public function setSellerAccountId($sellerAccountId)
    {
        $this->selllerAccountId = $sellerAccountId;
    }

    public function setAlibabaCompanyId($alibabaCompanyId)
    {
        $this->alibabaCompanyId = $alibabaCompanyId;
    }

    public function setSupportDeepAnalysis($supportDeepAnalysis)
    {    
        $this->supportDeepAnalysis = $supportDeepAnalysis;
    }

    public function makeAgentProcessParams(): array
    {
        return $this->agentProcessParams;
    }

    public function createTask(array $params): int
    {
        $task = new AsyncTask($this->clientId);
        $extInfo = [
            'seller_account_id' => $this->selllerAccountId,
            'alibaba_company_id' => $this->alibabaCompanyId,
            'support_deep_analysis' => $this->supportDeepAnalysis
        ];
        if (!empty($this->customerProfileData)) {
            $extInfo['customer_profile_data'] = $this->customerProfileData;
        }

        $task->getOperator()->create($this->clientId, \Constants::TYPE_OKKI_AI, $this->sceneType, $this->companyId, extInfo: $extInfo);
        return $task->task_id;
    }

    public function checkCreateTask($params): bool
    {
        $createFlag = false;
        $sellerAccountId = $this->selllerAccountId;


        if (empty($this->companyId)) {
            throw new \RuntimeException("请选择客户");
        }
        $profileData = [];

        if (!empty($this->alibabaCompanyId) && !empty($this->selllerAccountId))
        {
            // 对比阿里国际站数据
            $alibabaCompanyRelation = new AlibabaCompanyRelation($this->clientId);
            $companyRelation = $alibabaCompanyRelation->loadByAlibabaCompanyId($this->alibabaCompanyId);

            if (!$companyRelation->isNew() && !empty($sellerAccountId)) {
                $alibabaCustomerPortraitService = new \common\library\customer\service\AlibabaCustomerPortraitService();

                try {
                    // 直接从数据库中获取
                    $profileData = $alibabaCustomerPortraitService->getAlibabaProfileData(\User::getLoginUser(), $this->companyId, $sellerAccountId, $this->alibabaCompanyId);

                    if (!($profileData['is_auth_normal'] ?? false))
                    {
                        \LogUtil::info('store auth fail', [
                            'profileData' => $profileData,
                            'sellerAccountId' => $sellerAccountId,
                            'alibabaCompanyId' => $this->alibabaCompanyId,
                            'companyId' => $this->companyId
                        ]);
                        $profileData = [];
                    }
                } catch (\Throwable $exception) {
                    \LogUtil::info('getAlibabaProfileDataError', [
                        'errorInfo' => $exception->getMessage(),
                        'profileData' => $profileData,
                        'sellerAccountId' => $sellerAccountId,
                        'alibabaCompanyId' => $this->alibabaCompanyId,
                        'companyId' => $this->companyId
                    ]);

                    // 找不到就加载以前的数据
                    $profileData = $alibabaCustomerPortraitService->loadProfileData($this->alibabaCompanyId);
                }

                $this->setCustomerProfileData($profileData);
            }
        }


        if ($this->triggerType == AiAgentConstants::REPORT_ANALYSIS_TRIGGER_TYPE_AUTO)
        {
            // 如果当天生成过任务 那么则不再进行生成
            $lastTaskInfo = $this->getLastTaskInfoByCompanyId($this->companyId);
            $lastAnalysisTime = $lastTaskInfo['create_time'] ?? '1970-01-01 00:00:00';
            $extInfo = $lastTaskInfo['ext_info'] ?? '[]';
            $extInfo = json_decode($extInfo, true);
            // 直接从数据库中获取
            $originProfileData = $extInfo['customer_profile_data'] ?? [];

            // 如果当天生成过分析
            if (date('Y-m-d', strtotime($lastAnalysisTime)) == date('Y-m-d')) {
                return false;
            }

            $company = new Company($this->clientId, $this->companyId);

            if ($company->isNew()) {
                throw new \RuntimeException("该客户暂未建档，不支持分析");
            }


            if (strtotime($company->order_time) > strtotime($lastAnalysisTime)) {
                $createFlag = true;
            }
            if (strtotime($company->deal_time) > strtotime($lastAnalysisTime)) {
                $createFlag = true;
            }

            if (!empty($this->alibabaCompanyId) && !empty($this->selllerAccountId)) {
                // 对比阿里国际站数据
                $alibabaCompanyRelation = new AlibabaCompanyRelation($this->clientId);
                $companyRelation = $alibabaCompanyRelation->loadByAlibabaCompanyId($this->alibabaCompanyId);


                if (!$companyRelation->isNew() && !empty($sellerAccountId)) {
                    $createFlag = $this->checkProfileDataChange($originProfileData, $profileData) ? true : $createFlag;
                }
            }

        } else {
            $createFlag = true;
        }


        if ($createFlag && !$this->getLock()) {
            return false;
        }

        return $createFlag;


    }

    public function dataAnalysis($params)
    {
        $this->agentProcessParams = $params;
        $this->referId = $this->companyId;
        $processRecord = $this->saveProcessRecord('');
        $this->context['record_id'] = $processRecord->record_id;
        $this->context['triggerType'] = $this->triggerType;
        $this->context['in_deep_thinking'] = 1;

        $retried = false;
        while (1) {
            try {
                if ($this->supportDeepAnalysis == 0) {
                    $this->llmService->setModel($this->promptConfig['alternative_model'] ?? "azure-openai-gpt-4o-mini");
                    $this->systemPrompt = $this->promptConfig['alternative_system_prompt_default'] ?? $this->systemPrompt;
                    \LogUtil::info("OKKI_AI_ReportInterpretAiAgent_调用方未升级版本，默认使用普通模型",
                        [
                            'clientId' => $this->clientId,
                            'userId' => $this->userId,
                            'recordId' => $processRecord->record_id,
                        ]
                    );
                }
                $this->buildPrompt();
                $start = microtime(true);
                $response = $this->callLlm("-");
                $end = microtime(true);
                $costTime = ($end - $start);
    
                $this->addTraceLog('callLLM', [
                    'costTime' => $costTime,
                ]);
                break;
            } catch (AiAgentException $exception) {
                $context = array_merge($exception->getContext(), [
                    'message' => $exception->getMessage()
                ]);
                $this->updateAiServiceProcessRecord($processRecord->record_id,['error_code' => $exception->getCode(),'error_msg' => $exception->getMessage()]);
                if ($exception->getCode() == AiAgentException::CALL_DEEPSEEK_R1_ERROR) {
                    if ($retried) {
                        return $this->handleException($exception->getMessage(), $exception->getCode(), $context);
                    }
                    // 调用方降级模型再次请求
                    $retried = true;
                    $this->llmService->setModel($this->promptConfig['alternative_model'] ?? "azure-openai-gpt-4o-mini");
                    $this->systemPrompt = $this->promptConfig['alternative_system_prompt_default'] ?? $this->systemPrompt;
                    $this->context['in_deep_thinking'] = 0;
                    \LogUtil::info("OKKI_AI_ReportInterpretAiAgent_调用方降级模型再次请求",
                        [
                            'clientId' => $this->clientId,
                            'userId' => $this->userId,
                            'recordId' => $processRecord->record_id,
                        ]
                    );
                } else {
                    return $this->handleException($exception->getMessage(), $exception->getCode(), $context);
                }
            }
        }

        $this->updateAiServiceProcessRecord($processRecord->record_id, ['request_data' => json_encode($this->llmService->buildParams()), 'params' => json_encode($this->makeAgentProcessParams())]);

        return $this->initAiAgentProcessResponse($response, __FUNCTION__, AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT);
    }


    public function getLastTaskInfoByCompanyId($companyId)
    {
        $type = \Constants::TYPE_OKKI_AI;
        $db = PgActiveRecord::getDbByClientId($this->clientId);
        $sql = "select * from tbl_async_task where scene = {$this->sceneType} and type = {$type} and client_id = {$this->clientId} and update_user = {$this->userId} and refer_id = {$companyId} order by task_id desc limit 1";
        $result = $db->createCommand($sql)->queryAll();
        return empty($result) ? [] : $result[0];
    }


    public function setCompanyId($companyId)
    {
        $this->companyId = $companyId;
    }


    public function getReportCacheKey()
    {
        if (empty($this->companyId)) {
            throw new \RuntimeException("请选择客户");
        }
        return "report_analysis:{$this->clientId}:{$this->userId}:{$this->companyId}";
    }

    public function getJob(): BaseJob
    {
        return new AsyncAnalysisJob($this->clientId, $this->userId, $this->taskId, $this->sceneType,$this->triggerType);
    }


    public function setTaskId($taskId)
    {
        $this->taskId = $taskId;
    }

    public function getDataAnalysisSseObj(AiAgentProcessResponse $aiAgentProcessResponse): array
    {

        $text = new \common\library\ai_agent\message\Text();
        $text->withRecordId($aiAgentProcessResponse->recordId);
        $text->withConversationId($this->conversationId);

        return [AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT => $text];
    }

    public function getAnalysisReferKey()
    {
        return $this->companyId;
    }


    public function getInterpretConfig()
    {
        return [
            [
                'reportKey' => 'kh33',
                'title' => '客户销售趋势',
                'data_source_type' => AiAgentConstants::REPORT_ANALYSIS_DATA_SOURCE_TYPE_ORDER,
                // 传入的是公共参数，但实际报表的参数不是公共参数的命名，因此在这里做一层转换
                'transferParams' => [
                    'common.date' => 'order.performance_date'
                ],
                'cut_out' => 1000,
            ],
            [
                'reportKey' => 'kh34',
                'title' => '客户销售趋势',
                'data_source_type' => AiAgentConstants::REPORT_ANALYSIS_DATA_SOURCE_TYPE_OPPORTUNITY,
                // 传入的是公共参数，但实际报表的参数不是公共参数的命名，因此在这里做一层转换
                'transferParams' => [
                    'common.date' => 'opportunity.account_date'
                ],
                'cut_out' => 1000,
            ],
            [
                'reportKey' => 'cp1',
                'title' => '产品销售排行_产品',
                'data_source_type' => AiAgentConstants::REPORT_ANALYSIS_DATA_SOURCE_TYPE_ORDER,
                'transferParams' => [
                    'common.date' => 'order.performance_date'
                ],
                'order' => [
                    [
                        'key' => 'order.performance_date',
                        'field' => 'order.performance_date',
                        'order' => 'desc',
                    ],
                    [
                        'key' => 'sum-invoice_product.count',
                        'field' => 'sum-invoice_product.count',
                        'order' => 'desc',
                    ]
                ],
                'cut_out' => 100,
            ],
            [
                'reportKey' => 'cp14',
                'title' => '产品销售排行_产品',
                'data_source_type' => AiAgentConstants::REPORT_ANALYSIS_DATA_SOURCE_TYPE_OPPORTUNITY,
                'transferParams' => [
                    'common.date' => 'opportunity.account_date'
                ],
                'order' => [
                    [
                        'key' => 'opportunity.account_date',
                        'field' => 'opportunity.account_date',
                        'order' => 'desc',
                    ],
                    [
                        'key' => 'sum-opportunity_product.count',
                        'field' => 'sum-opportunity_product.count',
                        'order' => 'desc',
                    ]
                ],
                'cut_out' => 100,
            ],
            [
                'reportKey' => 'cp23',
                'title' => '产品销售排行_产品型号',
                'data_source_type' => AiAgentConstants::REPORT_ANALYSIS_DATA_SOURCE_TYPE_ORDER,
                'transferParams' => [
                    'common.date' => 'order.performance_date'
                ],
                'order' => [
                    [
                        'key' => 'order.performance_date',
                        'field' => 'order.performance_date',
                        'order' => 'desc',
                    ],
                    [
                        'key' => 'sum-invoice_product.count',
                        'field' => 'sum-invoice_product.count',
                        'order' => 'desc',
                    ]
                ],
                'cut_out' => 100,
            ],[
                'reportKey' => 'cp24',
                'title' => '产品销售排行_产品型号',
                'data_source_type' => AiAgentConstants::REPORT_ANALYSIS_DATA_SOURCE_TYPE_OPPORTUNITY,
                'transferParams' => [
                    'common.date' => 'opportunity.account_date'
                ],
                'order' => [
                    [
                        'key' => 'opportunity.account_date',
                        'field' => 'opportunity.account_date',
                        'order' => 'desc',
                    ],
                    [
                        'key' => 'sum-opportunity_product.count',
                        'field' => 'sum-opportunity_product.count',
                        'order' => 'desc',
                    ]
                ],
                'cut_out' => 100,
            ],
            [
                'reportKey' => 'cp2',
                'title' => '产品销售排行_产品类目',
                'data_source_type' => AiAgentConstants::REPORT_ANALYSIS_DATA_SOURCE_TYPE_ORDER,
                'transferParams' => [
                    'common.date' => 'order.performance_date'
                ],
                'order' => [
                    [
                        'key' => 'order.performance_date',
                        'field' => 'order.performance_date',
                        'order' => 'desc',
                    ],
                    [
                        'key' => 'sum-invoice_product.count',
                        'field' => 'sum-invoice_product.count',
                        'order' => 'desc',
                    ]
                ],
                'cut_out' => 100,
            ],
            [
                'reportKey' => 'cp15',
                'title' => '产品销售排行_产品类目',
                'data_source_type' => AiAgentConstants::REPORT_ANALYSIS_DATA_SOURCE_TYPE_OPPORTUNITY,
                'transferParams' => [
                    'common.date' => 'opportunity.account_date'
                ],
                'order' => [
                    [
                        'key' => 'opportunity.account_date',
                        'field' => 'opportunity.account_date',
                        'order' => 'desc',
                    ],
                    [
                        'key' => 'sum-opportunity_product.count',
                        'field' => 'sum-opportunity_product.count',
                        'order' => 'desc',
                    ]
                ],
                'cut_out' => 100,
            ],
            [
                'reportKey' => 'cp3',
                'title' => '产品销售排行_产品分组',
                'data_source_type' => AiAgentConstants::REPORT_ANALYSIS_DATA_SOURCE_TYPE_ORDER,
                'transferParams' => [
                    'common.date' => 'order.performance_date'
                ],
                'order' => [
                    [
                        'key' => 'order.performance_date',
                        'field' => 'order.performance_date',
                        'order' => 'desc',
                    ],
                    [
                        'key' => 'sum-invoice_product.count',
                        'field' => 'sum-invoice_product.count',
                        'order' => 'desc',
                    ]
                ],
                'cut_out' => 100,
            ],
            [
                'reportKey' => 'cp16',
                'title' => '产品销售排行_产品分组',
                'data_source_type' => AiAgentConstants::REPORT_ANALYSIS_DATA_SOURCE_TYPE_OPPORTUNITY,
                'transferParams' => [
                    'common.date' => 'opportunity.account_date'
                ],
                'order' => [
                    [
                        'key' => 'opportunity.account_date',
                        'field' => 'opportunity.account_date',
                        'order' => 'desc',
                    ],
                    [
                        'key' => 'sum-opportunity_product.count',
                        'field' => 'sum-opportunity_product.count',
                        'order' => 'desc',
                    ]
                ],
                'cut_out' => 100,
            ],
            [
                'reportKey' => 'cp27',
                'title' => '产品销售趋势对比_产品',
                'data_source_type' => AiAgentConstants::REPORT_ANALYSIS_DATA_SOURCE_TYPE_ORDER,
                'transferParams' => [
                    'common.date' => 'order.performance_date'
                ],
                'order' => [
                    [
                        'key' => 'order.performance_date',
                        'field' => 'order.performance_date',
                        'order' => 'desc',
                    ],
                    [
                        'key' => 'sum-invoice_product.count',
                        'field' => 'sum-invoice_product.count',
                        'order' => 'desc',
                    ]
                ],
                'cut_out' => 1000,
            ],
            [
                'reportKey' => 'cp32',
                'title' => '产品销售趋势对比_产品',
                'data_source_type' => AiAgentConstants::REPORT_ANALYSIS_DATA_SOURCE_TYPE_OPPORTUNITY,
                'transferParams' => [
                    'common.date' => 'opportunity.account_date'
                ],
                'order' => [
                    [
                        'key' => 'opportunity.account_date',
                        'field' => 'opportunity.account_date',
                        'order' => 'desc',
                    ],
                    [
                        'key' => 'sum-opportunity_product.count',
                        'field' => 'sum-opportunity_product.count',
                        'order' => 'desc',
                    ]
                ],
                'cut_out' => 1000,
            ],
            [
                'reportKey' => 'cp29',
                'title' => '产品销售趋势对比_产品型号',
                'data_source_type' => AiAgentConstants::REPORT_ANALYSIS_DATA_SOURCE_TYPE_ORDER,
                'transferParams' => [
                    'common.date' => 'order.performance_date'
                ],
                'order' => [
                    [
                        'key' => 'order.performance_date',
                        'field' => 'order.performance_date',
                        'order' => 'desc',
                    ],
                    [
                        'key' => 'sum-invoice_product.count',
                        'field' => 'sum-invoice_product.count',
                        'order' => 'desc',
                    ]
                ],
                'cut_out' => 1000,
            ],
            [
                'reportKey' => 'cp34',
                'title' => '产品销售趋势对比_产品型号',
                'data_source_type' => AiAgentConstants::REPORT_ANALYSIS_DATA_SOURCE_TYPE_OPPORTUNITY,
                'transferParams' => [
                    'common.date' => 'opportunity.account_date'
                ],
                'order' => [
                    [
                        'key' => 'opportunity.account_date',
                        'field' => 'opportunity.account_date',
                        'order' => 'desc',
                    ],
                    [
                        'key' => 'sum-opportunity_product.count',
                        'field' => 'sum-opportunity_product.count',
                        'order' => 'desc',
                    ]
                ],
                'cut_out' => 1000,
            ],
            [
                'reportKey' => 'cp30',
                'title' => '产品销售趋势对比_产品类目',
                'data_source_type' => AiAgentConstants::REPORT_ANALYSIS_DATA_SOURCE_TYPE_ORDER,
                'transferParams' => [
                    'common.date' => 'order.performance_date'
                ],
                'order' => [
                    [
                        'key' => 'order.performance_date',
                        'field' => 'order.performance_date',
                        'order' => 'desc',
                    ],
                    [
                        'key' => 'sum-invoice_product.count',
                        'field' => 'sum-invoice_product.count',
                        'order' => 'desc',
                    ]
                ],
                'cut_out' => 1000,
            ],
            [
                'reportKey' => 'cp35',
                'title' => '产品销售趋势对比_产品类目',
                'data_source_type' => AiAgentConstants::REPORT_ANALYSIS_DATA_SOURCE_TYPE_OPPORTUNITY,
                'transferParams' => [
                    'common.date' => 'opportunity.account_date'
                ],
                'order' => [
                    [
                        'key' => 'opportunity.account_date',
                        'field' => 'opportunity.account_date',
                        'order' => 'desc',
                    ],
                    [
                        'key' => 'sum-opportunity_product.count',
                        'field' => 'sum-opportunity_product.count',
                        'order' => 'desc',
                    ]
                ],
                'cut_out' => 1000,
            ],
            [
                'reportKey' => 'cp31',
                'title' => '产品销售趋势对比_产品分组',
                'data_source_type' => AiAgentConstants::REPORT_ANALYSIS_DATA_SOURCE_TYPE_ORDER,
                'transferParams' => [
                    'common.date' => 'order.performance_date'
                ],
                'order' => [
                    [
                        'key' => 'order.performance_date',
                        'field' => 'order.performance_date',
                        'order' => 'desc',
                    ],
                    [
                        'key' => 'sum-invoice_product.count',
                        'field' => 'sum-invoice_product.count',
                        'order' => 'desc',
                    ]
                ],
                'cut_out' => 1000,
            ],
            [
                'reportKey' => 'cp36',
                'title' => '产品销售趋势对比_产品分组',
                'data_source_type' => AiAgentConstants::REPORT_ANALYSIS_DATA_SOURCE_TYPE_OPPORTUNITY,
                'transferParams' => [
                    'common.date' => 'opportunity.account_date'
                ],
                'order' => [
                    [
                        'key' => 'opportunity.account_date',
                        'field' => 'opportunity.account_date',
                        'order' => 'desc',
                    ],
                    [
                        'key' => 'sum-opportunity_product.count',
                        'field' => 'sum-opportunity_product.count',
                        'order' => 'desc',
                    ]
                ],
                'cut_out' => 1000,
            ],

            [
                'reportKey' => 'khedm1',
                'title' => '客户EDM发送趋势',
                'cut_out' => 1000,
            ],
            [
                'reportKey' => 'yx1',
                'title' => '邮件收发趋势',
                'transferParams' => [
                    'common.date' => 'mail.receive_time'
                ],
                'cut_out' => 1000,
            ],
            [
                'reportKey' => 'khgj2',
                'title' => '客户跟进趋势',
                // 传入的是公共参数，但实际报表的参数不是公共参数的命名，因此在这里做一层转换
                'transferParams' => [
                    'common.date' => 'common.date'
                ],
                'cut_out' => 1000,
            ],

        ];
    }

    public function buildReportPrompt()
    {
        $start = microtime(true);
        $richnessCount = 0;

        foreach ($this->config as $configItem)
        {
            $reportKey = $configItem['reportKey'];

            if (!$this->checkReportAnalysable($reportKey)) continue;

            $params = $this->translateParams($reportKey,$this->agentProcessParams);
            $params = array_map(function ($item) {
                return is_array($item) ? json_encode($item) : $item;
            }, $params);

            $title = $configItem['title'];
            $order = $configItem['order'] ?? [];
            $cutOut = $configItem['cut_out'] ?? null;
            $report = new \common\library\statistics\render\report\Report($this->clientId, $reportKey, $params, []);
            $report->setViewUserId($this->userId);
            $report->setForceRefresh(1);
            $report->setShowDetail(1);
            $report->setShowData(1);
            $report->setScene('companyDetail');

            // 重新排序
            if (!empty($order)) {
                $report->getReportConfig()->getFormatConfig()->resetOrder($order);
            }

            $reportData = $report->format();

            $isRichness = $this->checkRichness($reportData);
            if (!$isRichness) continue;

            // 截取长度
            if (!empty($cutOut)) {
                $reportData['data'] = array_slice($reportData['data'], 0, $cutOut);
            }

            $this->richnessReportKeys[$reportKey] = $reportData;
            $this->richnessReportKeys[$reportKey]['reportTitle'] = $title;

            $richnessCount ++ ;
        }

        $alibabaProfileDataPrompt = '';
        // 阿里国际站数据
        $sellerAccountId = $this->selllerAccountId;
        $alibabaCompanyId = $this->alibabaCompanyId;
        if (!empty($sellerAccountId) && !empty($alibabaCompanyId))
        {
            $alibabaCompanyRelation = new AlibabaCompanyRelation($this->clientId);
            $companyRelation = $alibabaCompanyRelation->loadByAlibabaCompanyId($alibabaCompanyId);

            if (! $companyRelation->isNew())
            {
                \User::setLoginUserById($this->userId);
                $service = new \common\library\customer\service\AlibabaCustomerPortraitService();
                $alibabaProfileData = $service->getLatestProfileData($this->companyId, $sellerAccountId, $companyRelation->alibaba_company_id);
                \LogUtil::info("alibabaProfileData",['data' => json_encode($alibabaProfileData,JSON_UNESCAPED_UNICODE)]);
                if (!empty($alibabaProfileData))
                {
                    $alibabaProfileData = $service->processReturnData($alibabaProfileData);
                    // 处理掉隐藏字段 数值类型返回-1，或者对象数组返回*，表示该字段是隐藏字段
                    array_walk_recursive($alibabaProfileData, function(&$value, $key) {
                        if(is_array($value) && isset($value['hidden']) && $value['hidden'] === true) {
                            if(is_numeric($value['value'])) {
                                $value['value'] = -1;
                            } else {
                                $value['value'] = '*';
                            }
                        }
                    });

                    $this->richnessSceneMap[self::ALIBABA_CUSTOMER_FLAG] = true;
                    $alibabaProfileData = json_encode($alibabaProfileData,JSON_UNESCAPED_UNICODE);

                    $alibabaProfileDataPrompt .= "
### 阿里国际站行为json数据
{$alibabaProfileData}
";
                }

            }
        }
        if (!empty($alibabaProfileDataPrompt)) {
            $fieldTranslations = [
                # 顶级类别
                "footprint" => "客户活动记录",
                "behavior" => "客户互动指标",
                "is_auth_normal" => "账户验证状态",
                # Footprint部分
                "latest_visit_time" => "最近访问店铺时间",
                "is_fans" => "是否为粉丝",
                "focus_time" => "关注时间",
                "latest_visit_products" => "最近访问产品",
                "footprint_ext_values" => "额外客户活动信息",
                # Behavior部分
                "product_view_count" => "产品浏览数",
                "valid_inquiry_count" => "有效询盘数",
                "valid_rfq_count" => "报价请求数量",
                "login_days" => "登陆天数",
                "spam_inquiry_marked_by_supplier_count" => "垃圾询盘数",
                "added_to_blacklist_count" => "被加入黑名单数",
                "total_order_volume" => "总订单金额",
                "total_order_count" => "总订单总数",
                "trade_supplier_count" => "交易供应商数",
                "latest_search_words" => "最近搜索关键词",
                "preferred_industries" => "最常采购行业",
                "latest_inquiry_products" => "最近询盘产品",
                "latest_sync_time" => "数据的最近同步时间",
                "update_time" => "数据生成时间",
                # 通用字典字段
                "hidden" => "是否隐藏",
                "value" => "数值",
                # 新增字段
                "main_image_url" => "产品主图链接",
                "product_url" => "产品详情链接",
                # 布尔值转换
                "true" => "1",
                "false" => "0",
                # 空值转换
                "null" => "空值",
            ];
            // 按键长度降序排序fieldTranslations，避免部分替换问题
            uksort($fieldTranslations, function($a, $b) {
                return strlen($b) - strlen($a);
            });

            foreach ($fieldTranslations as $key => $value) {
                $alibabaProfileDataPrompt = str_replace($key, $value, $alibabaProfileDataPrompt);
            }
        }

        if ($richnessCount < self::RICHNESS_COUNT_LIMIT && empty($alibabaProfileDataPrompt)) {
            throw new AiAgentException(AiAgentException::ERROR_CODE_TO_USER_MSG[AiAgentException::ERROR_NOT_ENOUGH_REPORT_RICHNESS], AiAgentException::ERROR_NOT_ENOUGH_REPORT_RICHNESS);
        }

        // 丰富度判断
        foreach (self::CHECK_REPORT_KEY_MAP as $scene => $reportKeys)
        {
            $richnessReportKeys = array_keys($this->richnessReportKeys);
            if (!empty(array_intersect($reportKeys,$richnessReportKeys))) {
                $this->richnessSceneMap[$scene] = true;
                $this->isAllSparse = false;
            }
        }

        $reportPrompt = '';
        $analysisPrompt = $this->getAnalysisPrompt();

        if (!$this->isAllSparse)
        {
            $reportPrompt = "报表数据\n```data\n{{reportData}}\n```\n\n###";

            $subPrompt = '';
            foreach ($this->richnessSceneMap as $scene => $flag)
            {
                if (!$flag) continue;
                $sceneReportKeys = self::CHECK_REPORT_KEY_MAP[$scene] ?? [];
                foreach ($this->richnessReportKeys as $reportKey => $reportData)
                {
                    if (!in_array($reportKey, $sceneReportKeys)) continue;
                    $reportText = $this->reportText($reportData);
                    $title = $reportData['reportTitle'];
                    $subPrompt .= "
title:{$title}
{$reportText}
";
                }

            }
            $reportPrompt = str_replace("{{reportData}}",$subPrompt,$reportPrompt);
        }


        if (!empty($alibabaProfileDataPrompt)) {
            $reportPrompt .= $alibabaProfileDataPrompt;
        }


        $end = microtime(true);
        $costTime = ($end - $start);
        $this->addTraceLog(TraceConstant::REPORT_INTERPRET_GET_REPORT_DATA, [
            'costTime' => $costTime,
            'richnessCount' => $richnessCount,
            'richnessMap' => $this->richnessSceneMap
        ]);


        return $analysisPrompt . $reportPrompt;

    }


    /**
     * Prompt构建函数：获取当前时间
     */
    public function getCurrentTimePrompt()
    {
        return "当前时间: ". date("Y-m-d H:i:s");
    }

    /**
     * 获取报表丰富度 - 跑数据用
     * @return array
     * @throws AiAgentException
     * @throws \ProcessException
     */
    public function getReportRichness($params): array
    {
        $this->agentProcessParams = $params;
        $richnessMax = 0;
        $data = [];
        foreach ($this->config as $configItem)
        {
            $reportKey = $configItem['reportKey'];

            if (!$this->checkReportAnalysable($reportKey)) continue;

            $params = $this->translateParams($reportKey,$params);
            $params = array_map(function ($item) {
                return is_array($item) ? json_encode($item) : $item;
            }, $params);

            $order = $configItem['order'] ?? [];
            $report = new \common\library\statistics\render\report\Report($this->clientId, $reportKey, $params, []);
            $report->setViewUserId($this->userId);
            $report->setForceRefresh(1);
            $report->setShowDetail(1);
            $report->setShowData(1);
            $report->setScene('companyDetail');

            // 重新排序
            if (!empty($order)) {
                $report->getReportConfig()->getFormatConfig()->resetOrder($order);
            }

            $reportData = $report->format();

            $richness = $this->getRichness($reportData);
            $data[$reportKey] = $richness;

            if($richness > $richnessMax){
                $richnessMax = $richness;
            }
        }

        return ['richness' => $richnessMax,'data' => $data];
    }

    public function getRichness(array $reportData): float
    {
        if (empty($reportData)) return 0;

        $dataList = $reportData['data'] ?? [];

        if (empty($dataList)) return 0;

        $dataNumber = 0;
        $notEnoughNumber = 0;
        foreach ($dataList as $data)
        {
            if (count($dataList) < self::NOT_ENOUGH_ASSET_ANALYSIS_DATA_ROW) {
                return 0;
            }

            foreach ($data as $datum)
            {
                $summaries = $datum['summaries'] ?? [];
                foreach ($summaries as $summary)
                {
                    $dataNumber++;
                    $value = $summary['value'] ?? 0;

                    if ($value == 0){
                        $notEnoughNumber++;
                    }
                }
            }
        }

        if ($dataNumber == 0) {
            return 0;
        }

        $enoughNumber = $dataNumber - $notEnoughNumber;

        return round(($enoughNumber / $dataNumber) * 100,2);
    }

    public function buildPrompt()
    {
        $promptValueConfig = $this->buildPromptConfigValueMap();

        $this->systemPrompt = $this->replacePlaceholders($this->systemPrompt, $promptValueConfig);
        return $this->replacePlaceholders($this->userPrompt, $promptValueConfig);
    }

    public function translateParams($reportKey, $params)
    {
        $config = array_column($this->config,null,'reportKey');
        $config = $config[$reportKey];

        $transferParams = $config['transferParams'] ?? [];
        if (empty($transferParams)) return $params;
        foreach ($params as &$param)
        {
            $trans = $param['field'] ?? '';
            if (empty($transferParams[$trans])) continue;
            $param['field'] = $transferParams[$trans];
        }
        return $params;
    }

    /**
     * 销售情况类报表需要区分基于商机还是基于销售订单进行分析
     * @param $reportKey
     * @return bool
     */
    public function checkReportAnalysable($reportKey)
    {
        $params = array_column($this->agentProcessParams, 'value', 'field');
        $dataSourceValue = $params['common.data_source'] ?? '';
        $reportDataSourceValue = array_column($this->config, null, 'reportKey')[$reportKey]['data_source_type'] ?? '';
        return empty($reportDataSourceValue) || $dataSourceValue == $reportDataSourceValue;
    }

    public function dealStreamContent($content)
    {
        // 处理缓冲区内容，移除markdown标记、json标记、plaintext标记、text标记
        if (stripos($content, '```markdown') !== false) {
            $content = preg_replace('/```markdown\s*/i', '', $content);
        }
        if (stripos($content, '```json') !== false) {
            $content = preg_replace('/```json\s*/i', '', $content);
        }
        if (stripos($content, '```plaintext') !== false) {
            $content = preg_replace('/```plaintext\s*/i', '', $content);
        }
        if (stripos($content, '```text') !== false) {
            $content = preg_replace('/```text\s*/i', '', $content);
        }
        if (stripos($content, '```') !== false) {
            $content = preg_replace('/```\s*$/', '', $content);
        }
        $content = str_replace(" *", "*", $content);
        // 处理无须列表格式错误
        $content = str_replace("-**", "- **", $content, $count);
        // 替换带有千位分隔符的货币markdown加粗格式为HTML加粗标签
        $content = preg_replace('/\*\*([¥￥]\d{1,3}(,\d{3})*\.?\d*)\*\*/u', '<strong>$1</strong>', $content);
        $content = preg_replace('/\*\*(\$\d{1,3}(,\d{3})*\.?\d*)\*\*/u', '<strong>$1</strong>', $content);
        return $content;
    }


    public function getAnalysisResultFromDB()
    {
        $analysisRecord = new AnalysisRecordAPI($this->clientId,$this->userId);
        $columns = ['record_id', 'client_id', 'task_id', 'scene_type', 'refer_data','refer_key', 'refer_data', 'user_id', 'status', 'analysis_result', 'process_record_id', 'update_time'];

        $analysisRecordList = $analysisRecord->records([
            'scene_type' => $this->sceneType,
            'page' => 0,
            'page_size' => 1,
            'refer_key' => $this->getAnalysisReferKey()
        ],$columns,'record_id','desc');

        foreach ($analysisRecordList as $recordItem)
        {
            $this->context['record_id'] = $recordItem['process_record_id'];
            $this->context['finish_time'] = $recordItem['update_time'];
            $this->context['params'] = $recordItem['refer_data'];

            $analysisStatus = $recordItem['status'] ?? \AiAnalysisRecord::STATUS_SUCCESS;
            // 初始化
            $this->responseAnalysisResult('', AiAgentConstants::MESSAGE_STATUS_INIT);
            // 进行中
            $this->responseAnalysisResult(
                $this->dealStreamContent($recordItem['analysis_result'] ?? ''),
                $analysisStatus == \AiAnalysisRecord::STATUS_SUCCESS ? AiAgentConstants::MESSAGE_STATUS_PROCESSING : AiAgentConstants::MESSAGE_STATUS_ERROR
            , processingStreamFlag: false);
            //结束
            $this->responseAnalysisResult('', AiAgentConstants::MESSAGE_STATUS_CLOSE, $this->buildResponseAnalysisExtInfo());
        }

    }


    public function buildResponseAnalysisExtInfo()
    {
        $recordId = $this->context['record_id'] ?? 0;
        $finishTime = $this->context['finish_time'];
        $params = $this->context['params'];
        $extInfo = [
            'record_id' => $recordId,
            'finish_time' => $finishTime,
            'params' => $params
        ];
        //获取反馈
        $aiServiceRecordFeedbackPdo = new \common\library\ai_service\AiServiceRecordFeedbackList($this->clientId,$this->userId);
        $aiServiceRecordFeedbackPdo->setUserId($this->userId);
        $aiServiceRecordFeedbackPdo->setRecordId($recordId);
        $aiServiceRecordFeedbackList = $aiServiceRecordFeedbackPdo->find();
        $aiServiceRecordFeedbackKeyByRecordId = array_column($aiServiceRecordFeedbackList,null,'record_id');
        $feedbackInfo = $aiServiceRecordFeedbackKeyByRecordId[$recordId] ?? [];
        if (!empty($feedbackInfo))
        {
            $favorite = $feedbackInfo['favorite'] ?? 0;
            $tagList = $feedbackInfo['tag_list'] ?? [];
            $formatFeedBackInfo = [
                'favorite' => $favorite,
                'tag_list' => $tagList
            ];
            $extInfo['feedback'] = $formatFeedBackInfo;
        }

        return $extInfo;
    }

    public function setTriggerType($triggerType)
    {
        $this->triggerType = $triggerType;
    }

    /**
     * 丰富度校验
     * 规则：「数据为空或者为0的比例 < 50%」并且「数据报表行数 > 2行」则为丰富
     *
     * @param array $reportData
     * @return bool
     */
    public function checkRichness(array $reportData): bool
    {
        if (empty($reportData)) return false;
        if ($this->skipRichnessCheck)  return true;

        $dataList = $reportData['data'] ?? [];

        $dataNumber = 0;
        $notEnoughNumber = 0;
        foreach ($dataList as $data)
        {
            if (count($dataList) < self::NOT_ENOUGH_ASSET_ANALYSIS_DATA_ROW) {
                return false;
            }

            foreach ($data as $datum)
            {
                $summaries = $datum['summaries'] ?? [];
                foreach ($summaries as $summary)
                {
                    $dataNumber++;
                    $value = $summary['value'] ?? 0;

                    if ($value == 0){
                        $notEnoughNumber++;
                    }
                }
            }
        }

        if ($dataNumber == 0 || ((float)$notEnoughNumber / $dataNumber) > self::NOT_ENOUGH_ASSET_ANALYSIS_DATA_PERCENT) {
            return false;
        }
        return true;
    }

    public function handleTimeout()
    {
        $data = [
            'status' => AiAgentConstants::REPORT_INTERPRET_MESSAGE_STATUS_RETRY,
        ];
        $this->sseResponse->writeJson($data);
    }


    // 获取分析框架
    public function getAnalysisPrompt()
    {
        $prompt = "### 每个报表表名或json数据包含信息\n{{descs}}\n###";
        $replace = "";
        foreach ($this->richnessSceneMap as $scene => $flag) {
            if (!$flag) continue;
            switch ($scene) {
                case self::CUSTOMER_FLAG:
                    $replace .= "- 客户成交趋势：分析该客户的成交趋势，了解客户购买行为变化。\n";
                    break;
                case self::PRODUCT_FLAG:
                    $replace .= "- 产品销售排行：在与该客户的成交中，查看产品根据销售额/销量在产品/产品类目/产品分组/产品型号上的排行\n";
                    $replace .= "- 产品销售趋势对比:在与该客户的成交中，查看不同产品/产品型号/产品类目/产品分组在销售额/销量上的趋势\n";
                    break;
                case self::EMAIL_FLAG:
                    $replace .= "- 邮件收发趋势:收发邮件趋势，基于现有邮件列表数据统计，帮助了解与该客户收发邮件工作的概况。\n";
                    break;
                case self::EDM_FLAG:
                    $replace .= "- 客户EDM发送趋势:通过统计分析过往给该客户发送EDM以及该客户打开或回复EDM的情况，了解客户对EDM的互动和响应趋势。\n";
                    break;
                case self::FOLLOW_UP_FLAG:
                    $replace .= "- 跟进趋势表描述：分析客户在不同沟通渠道（聊天/邮件/EDM/文本跟进）上的互动趋势，统计各渠道的客户触达频率与方式偏好变化，帮助评估跟进策略执行效果及客户响应规律。\n";
                    break;
                case self::ALIBABA_CUSTOMER_FLAG:
                    $replace .= "- 阿里国际站行为json数据:分析客户在国际站上的行为数据，包括店铺访问、产品浏览、询盘、RFQ、订单等，以了解客户的购买行为和偏好，为营销策略提供数据支持。\n";
                    break;

            }
        }

        return str_replace("{{descs}}",$replace, $prompt);
    }


    /**
     * 根据场景获取输出格式
     * @return string
     */
    public function getOutPutFormat()
    {
        // 切到 DeepSeek-R1 模型后，此方法不再使用
        $customerFlag = $this->richnessSceneMap[self::CUSTOMER_FLAG];
        $productFlag = $this->richnessSceneMap[self::PRODUCT_FLAG];
        $emailFlag = $this->richnessSceneMap[self::EMAIL_FLAG];
        $edmFlag = $this->richnessSceneMap[self::EDM_FLAG];
        $followUpFlag = $this->richnessSceneMap[self::FOLLOW_UP_FLAG];
        $alibabaCustomerFlag = $this->richnessSceneMap[self::ALIBABA_CUSTOMER_FLAG];
        $prompt = "";
        if ($customerFlag || $productFlag)
        {
            $prompt .= "## 销售情况分析\n";
            if ($customerFlag) {
                $prompt .= "### 成交趋势分析
[根据分析框架，在1-2段话内给出分析及结论，请注意分析的条理性，要求加粗重要结论]
";
            }
            if ($productFlag) {
                $prompt .= "
### 产品销售分析
- <span style='color: #333; font-weight: 400;'>产品编号分析</span>：[描述该客户购买不同产品随时间的变化趋势，指出存在明显波动的产品，再结合产品销售量分析产品前景，要求加粗重要结论，如果报表数据中不包含相关报表，请返回相关数据缺失]
- <span style='color: #333; font-weight: 400;'>产品型号分析</span>：...
- <span style='color: #333; font-weight: 400;'>产品类目分析</span>：...
- <span style='color: #333; font-weight: 400;'>产品分组分析</span>：...";
            }
        }

        if ($emailFlag || $edmFlag)
        {
            $prompt .= "## 沟通情况分析\n";

            if ($emailFlag) {
                $prompt .= "
### 邮件收发趋势分析
[根据分析框架，在1-2段话内给出分析及结论，要求加粗重要结论,如果报表数据中不包含相关报表，请返回相关数据缺失]";

            }
            if ($edmFlag) {
                $prompt .= "
### 客户EDM发送趋势分析
[根据分析框架，在1-2段话内给出分析及结论，要求加粗重要结论,如果报表数据中不包含相关报表，请返回相关数据缺失]
";
            }
        }

        if ($followUpFlag)
        { 
            $prompt .= "
### 跟进趋势分析
[根据分析框架，在1-2段话内给出分析及结论，请注意分析的条理性，要求加粗重要结论]
";
        }

        if ($alibabaCustomerFlag)
        {
            $prompt .= <<<PROMPT
## 国际站行为数据分析
### 客户长期行为分析
### 客户近期行为分析
[根据分析框架，在1-2段话内给出分析及结论，要求加粗重要结论,如果报表数据中不包含相关报表，请返回相关数据缺失。
注意：1.当搜索词关键词为“*”时，直接表述近期没有搜索行为，不要出现“*”。2.分析中不要出现url。]
PROMPT;

        }
        return $prompt;
    }


    public function getAnalyticalFramework()
    {
        // 切到 DeepSeek-R1 模型后，此方法不再使用
        $customerFlag = $this->richnessSceneMap[self::CUSTOMER_FLAG];
        $productFlag = $this->richnessSceneMap[self::PRODUCT_FLAG];
        $emailFlag = $this->richnessSceneMap[self::EMAIL_FLAG];
        $edmFlag = $this->richnessSceneMap[self::EDM_FLAG];
        $followUpFlag = $this->richnessSceneMap[self::FOLLOW_UP_FLAG];
        $alibabaCustomerFlag = $this->richnessSceneMap[self::ALIBABA_CUSTOMER_FLAG];

        $analyticalFramework = "";

        if ($customerFlag || $productFlag) {
            $analyticalFramework .= "### 销售情况分析\n";

            if ($customerFlag) {
                $analyticalFramework .= <<<EOT
#### 成交趋势分析
请注意你的分析需要有利于营销策略的制定，如发现用户成交的季节性，发现用户成交趋势的变化。
请注意所有成交数据都是用户与其中一个客户的数据。
根据下面分析思路分析：
- 首先，根据长期数据分析下面信息
    - 分析该客户的成交数据中是否存在明显的季节性波动
    - 分析该客户是否存在周期性购买行为，如季度性高峰或低谷
- 然后，根据近期数据分析下面信息
    - 分析该客户购买金额、单量、平均成交额、购买频率的变化趋势，同时结合各产品成交情况，预测客户未来一段时间（几天、几个月）的购买情况。

EOT;
            }

            if ($productFlag) {
                $analyticalFramework .= <<<EOT
#### 产品销售分析
请注意，产品销售会给出产品销售排行、产品销售趋势对比两张报表，每张报表都有四张子表，分别是由同一组数据按产品编号、产品型号、产品类目、产品分组进行聚合得到的。
**请注意所有产品成交数据都是用户与其中一个客户的数据**，在分析中注意考虑。
请分别从产品编号、产品型号、产品类目、产品分组四个方面分析。
以产品编号为例，分析思路如下：
请先根据产品销售趋势表分析该客户对不同产品编号购买情况随时间的变化趋势，指出销售量或销售金额出现明显波动的产品，并分析其中原因，如季节性波动。
然后，分析该客户购买该产品的金额、单量、平均成交额、购买频率的变化趋势，同时结合各产品成交情况，预测客户未来一段时间（几天、几个月）对该产品的购买情况。

请注意：通过报表能很容易得到的信息不需要指出，如销量最高的是什么产品，能从产品排行表得到。

EOT;
            }
        }

        if ($emailFlag || $edmFlag) {
            $analyticalFramework .= "### 沟通情况分析\n";

            if ($emailFlag) {
                $analyticalFramework .= <<<EOT
#### 邮件收发趋势分析
- **客户回复积极性**：分析客户对邮件的回复率，识别出客户对沟通的积极性。
- **员工跟进积极性**：分析我方员工的回复率，判断员工的跟进效率。
请注意：收到邮件数指的是公司业务员收到邮件数、发送邮件数指的是公司业务员发送邮件数、打开率指的是客户打开率、对方回复率指的是客户回复率、我方回复率指的是公司业务员回复

EOT;
            }

            if ($edmFlag) {
                $analyticalFramework .= <<<EOT
#### 客户EDM发送趋势分析  
- **EDM营销效果**：分析EDM的发送、打开、送达和回复情况，评估EDM营销的效果。
- **客户互动趋势**：识别出客户对EDM的互动趋势，判断EDM内容和发送时间的有效性。
请注意：已发送edm数指的是公司业务员发送edm数、已发送数和送达数指的是公司业务员的已发送和送达数edm数、打开数指的是客户打开edm数、回复数指的是客户回复edm数。

EOT;
            }

        }

        if ($followUpFlag) {
            $analyticalFramework .= <<<EOT
#### 跟进趋势分析
请注意所有数据都是用户与其中一个客户的数据。
根据下面分析思路分析：
- 长期数据分析
    - 季节性波动
        - 分析各渠道（邮件/EDM/聊天）季度触达量规律性波动（如Q4邮件触达量激增30%）
        - 识别客户固定行为周期（如每年8月沟通量下降50%）
    - 渠道演变趋势
        - 观察渠道使用衰减规律（如EDM触达量连续3季度下降20%）
        - 发现新兴渠道渗透趋势（如文本跟进季度使用量翻倍）
- 近期数据分析
    - 渠道效能变化
        - 对比近几天/周各渠道响应率波动（如邮件打开率下降至8%）
        - 定位高价值沟通时段（如周四下午聊天回复率提升15%）
    - 策略预警优化
        - 预测未来有效触达量（例：EDM渠道按当前衰减率将在Q3失效）
        - 识别低效触达场景（如连续3天高频聊天触达零响应）

EOT;
        }

        if ($alibabaCustomerFlag) {
            $analyticalFramework .= <<<EOT
#### 客户行为分析
请注意你的分析需要有利于营销策略的制定，如发现客户的购买偏好、行为习惯变化等。
请注意所有行为数据都是客户在国际站上的行为数据。
根据下面分析思路分析：
- 首先，根据长期数据分析下面信息
  - 分析客户的访问频率、浏览产品类型、询盘和RFQ的提交情况，了解客户的购买偏好和需求。
  - 分析客户的订单金额、订单数量、交易供应商数量，了解客户的购买能力和交易习惯。
  - 分析客户的最近搜索关键词和最常采购行业，了解客户的兴趣点和潜在需求。
- 然后，根据近期数据分析下面信息
  - 分析客户最近访问的产品和询盘产品，了解客户的近期需求和购买意向。
  - 分析客户的登录天数、产品浏览数、有效询盘数、有效RFQ数等行为数据的变化趋势，预测客户未来的购买行为。
  - 结合客户的垃圾询盘数和被加入黑名单数，评估客户的信誉度和潜在风险。
注意：
- 当搜索词关键词为“*”时，表示近期没有搜索行为。分析中不需要出现描述这一点。
- 在描述结论时，给出具体支持结论的数据。如：给出具体的日期。
- 分析结论较长时，请分段编号输出。
EOT;

        }

        return $analyticalFramework;
    }

    public function checkUsageRestriction()
    {
        if ($this->triggerType == AiAgentConstants::REPORT_ANALYSIS_TRIGGER_TYPE_AUTO) {
            return ;
        }
        return parent::checkUsageRestriction(); // TODO: Change the autogenerated stub
    }

    public function handleBilling()
    {
        if ($this->triggerType == AiAgentConstants::REPORT_ANALYSIS_TRIGGER_TYPE_AUTO) return;
        parent::handleBilling();
    }


    // 销售情况监测
    public function customerSalesMonitor($companyInfos = [])
    {
        if (empty($companyInfos)) {
            \LogUtil::info("empty companyInfo");
            return;
        }

        // 需要查询出原有的CompanyId进行覆盖式更新
        $lastCompanyIdToRecordIdMap = $this->getLastCustomerSalesMonitorCompanyIds();

        $lastCompanyIds = array_keys($lastCompanyIdToRecordIdMap);
        $lastRecordIds = array_values($lastCompanyIdToRecordIdMap);

        $allCompanyIds = array_unique(array_values(array_filter(array_merge(array_column($companyInfos,'company_id'), $lastCompanyIds))));
        $companyTrendsMap = $this->checkTransactionOrderTimeTrend($allCompanyIds);

        $recordIds = [];
        $needDeleteRecordIds = [];

        foreach ($companyInfos as $companyInfo)
        {
            $this->companyId = $companyInfo['company_id'] ?? 0;

            $trend = $companyTrendsMap[$this->companyId] ?? null;
            // 如果系数变为平稳，那么则不分析该数据
            if ($trend == null) {
                $needDeleteRecordIds[] = $lastCompanyIdToRecordIdMap[$this->companyId] ?? 0;
                continue;
            }

            // 如果上个周期推送过了，那么这个周期就进行覆盖，把以前那个给删了
            if (in_array($this->companyId,$lastCompanyIds)) {
                $needDeleteRecordIds[] = $lastCompanyIdToRecordIdMap[$this->companyId] ?? 0;
            }

            // 这里不用流式输出
            $this->agentProcessParams = $companyInfo['params'];
            $this->referId = $this->companyId;

            $this->stream = false;
            $this->llmService = new AIClient();
            $this->llmModel = AIClient::AZURE_OPENAI_GPT_FOUR_O_MINI;
            $this->llmService->setModel($this->llmModel);

            $this->config = array_values(array_filter($this->config,function ($v) {
                return in_array($v['reportKey'],['cp27','kh33'])  ;
            }));

            $this->systemPrompt = $this->systemPromptMap[self::CUSTOMER_ANALYSIS_PROMPT] ?? '';

            try {

                $this->buildPrompt();
                // 算法说要把报表数据放在user_prompt里面
                $this->userPrompt = $this->systemPrompt;
                $this->systemPrompt = '-';

                \LogUtil::info("customerSalesMonitor构建数据", ['prompt' => $this->userPrompt]);


                $record = $this->saveProcessRecord('-');
                $this->context['record_id'] = $record->record_id;

                $standardResponse = $this->callLlm($this->userPrompt);
                $answer = $this->tryParseJson($standardResponse['answer']);
                if (empty($answer)) {
                    throw new AiAgentException(sprintf("json解析错误-%s", $answer),AiAgentException::ERR_JSON_FORMAT_ERROR);
                }

                $translateAnswer['answer_' . $this->language] = $this->translateAnswer($answer);
                $translateAnswer['answer'] = $answer;
                $standardResponse['answer'] = json_encode($translateAnswer);

                $this->updateAiServiceProcessRecord($record->record_id,['llm_api_standard_response' => $standardResponse,
                    'status' => AiServiceProcessModel::STATUS_SUCCESS,'context' => ['reportKeys' => ['cp27','kh33'],'trend' => $trend,'prompt' => $this->userPrompt]]);
                $recordIds[] = $record->record_id;


            } catch (AiAgentException $exception) {
                \LogUtil::info("customerSalesMonitorErr",[
                    'message' => $exception->getMessage(),
                    'code' => $exception->getCode(),
                    'record_id' => $record->record_id ?? 0
                ]);
                if (!empty($record->record_id)) {
                    $this->updateAiServiceProcessRecord($record->record_id,['error_code' => $exception->getCode(),'error_msg' => $exception->getMessage()]);
                }
            }
        }

        if (!$this->dryRun)
        {
            $allRecordIds = array_merge($recordIds,$lastRecordIds);
            $filterRecordIds = array_filter($allRecordIds,function ($v) use ($needDeleteRecordIds) {
                return !in_array($v,$needDeleteRecordIds);
            });

            if (empty($filterRecordIds)) {
                \LogUtil::info("NotPushCustomerSalesMonitorFeed");
                return ;
            }
            \LogUtil::info("pushCustomerSalesMonitorFeed", [
                'allRecordIds' => $allRecordIds,
                'recordIds' => $filterRecordIds,
                'deleteRecordIds' => $needDeleteRecordIds
            ]);
            $feedPdo = new \common\library\todo\Feed(\common\library\todo\TodoConstant::OBJECT_TYPE_STATISTIC, \common\library\todo\TodoConstant::TODO_TYPE_STATISTIC_INSIGHT);
            $feedPdo->setClientId($this->clientId);
            $feedPdo->setUserId($this->userId);
            $feedPdo->setData(['record_ids' => $filterRecordIds]);
            $feedPdo->pushFeed([self::CUSTOMER_TRANSLATION_TREND_FEED_ID]);
        }



    }
    public function setDruRun($dryRun)
    {
        $this->dryRun = $dryRun;
    }


    public function setSkipRichnessCheck($flag)
    {
        $this->skipRichnessCheck = $flag;
    }


    /**
     * 从gpt的响应中解析json
     *
     * @param string $aiResponse
     * @return array
     */
    public function tryParseJson(string $aiResponse): string
    {
        // 1. gpt返回的就是json
        $data = json_decode($aiResponse, true);
        if(!empty($data)) {
            return $data;
        }

        // 2. gpt返回的结果包含文字，需要提取```JSON...```中的内容
        $json = preg_replace('/^.*?```json(.*)```.*?$/si', '$1', $aiResponse);
        $data = json_decode($json, true);
        if(!empty($data)) {
            return $json;
        }

        // 3. gpt返回的结果包含文字，需要提取```...```中的内容
        $json = preg_replace('/^.*?```(.*)```.*?$/si', '$1', $aiResponse);
        $data = json_decode($json, true);
        if(!empty($data)) {
            return $json;
        }

        return '';
    }


    public function checkTransactionOrderTimeTrend($companyIds)
    {
        $startDate = date('Y-m-d 00:00:00', strtotime('-1 year', time()));
        $endDate = date('Y-m-d 23:59:59', time());

        $pgDb = PgActiveRecord::getDbByClientId($this->clientId);
        $companyIdsStr = implode(",", $companyIds);
        $dealOrderSql = "select company_id, account_date,SUM(indicator_value) as indicator_value
                                    from tbl_performance_v2_record
                                    where client_id = {$this->clientId}
                                        and company_id in ({$companyIdsStr}) and record_type = 2
                                      and rule_id = (
                                      select rule_id from tbl_performance_v2_rule where tbl_performance_v2_rule.client_id = {$this->clientId} and type = 2 LIMIT 1
                                      ) and account_date >= '{$startDate}' and account_date <= '{$endDate}' group by company_id, account_date order by account_date asc";
        $distinctPerformanceRecordList = $pgDb->createCommand($dealOrderSql)->queryAll();
        // 处理组装的逻辑
        $companyIdToAccountDateMap = [];
        foreach ($distinctPerformanceRecordList as $item) {
            $itemCompanyId = $item['company_id'];
            $itemAccountDate = $item['account_date'];
            $value = $item['indicator_value'];
            if (!isset($companyIdToAccountDateMap[$itemCompanyId])) {
                $companyIdToAccountDateMap[$itemCompanyId] = [];
            }
            $companyIdToAccountDateMap[$itemCompanyId][$itemAccountDate] = $value;
        }


        $analysis = function ($data) {
            $results = [];

            foreach ($data as $companyId => $dates)
            {
                // 至少需要4个数据点才能判断趋势
                if (count($dates) < 3) {
                    $results[$companyId] = null;
                    continue;
                }

                // 按日期排序
                ksort($dates);

                // 准备数据
                $y = array_values($dates); // 订单金额
                $x = range(0, count($dates) - 1); // 序号 [0,1,2,...]

                // 计算平均值
                $n = count($x);
                $meanX = array_sum($x) / $n;
                $meanY = array_sum($y) / $n;

                // 计算斜率
                $numerator = 0; // 分子
                $denominator = 0; // 分母

                for ($i = 0; $i < $n; $i++) {
                    $numerator += ($x[$i] - $meanX) * ($y[$i] - $meanY);
                    $denominator += ($x[$i] - $meanX) ** 2;
                }

                // 避免除以0
                if ($denominator == 0) {
                    $results[$companyId] = null;
                    continue;
                }

                // 计算斜率
                $slope = $numerator / $denominator;

                // 计算相对斜率
                $relativeSlope = $meanY != 0 ? $slope / $meanY : 0;

                // 判断趋势
                if ($relativeSlope >= 0.2) {
                    $results[$companyId] = 'up';
                } else if ($relativeSlope <= -0.2) {
                    $results[$companyId] = 'down';
                } else {
                    $results[$companyId] = null;
                }
            }

            return $results;
        };
        // 开始计算上升或者下降
        return $analysis($companyIdToAccountDateMap);
    }


    public function getLastCustomerSalesMonitorCompanyIds()
    {
        $feedPdo = new \common\library\todo\Feed(\common\library\todo\TodoConstant::OBJECT_TYPE_STATISTIC, \common\library\todo\TodoConstant::TODO_TYPE_STATISTIC_INSIGHT);
        $feedData = $feedPdo->getFeedByObjectId(self::CUSTOMER_TRANSLATION_TREND_FEED_ID);
        $feedData = $feedData['data'] ?? [];
        $objectFeed = $feedData['objectFeeds'] ?? [];
        $objectFeed = $objectFeed[0] ?? [];
        // 上一次推送的结果
        $recordData = $objectFeed['data'] ?? [];
        if (empty($recordData)) return [];
        $recordData = json_decode($recordData,true);
        $recordIds = $recordData['record_ids'] ?? [];
        if (empty($recordIds)) return [];

        $recordList = new AiServiceProcessRecordList($this->clientId,$this->userId,$this->sceneType);
        $recordList->setProcessRecordIds($recordIds);
        return array_column($recordList->find(),'record_id', 'refer_id');

    }


    public function questionRecommend($recordId = 0, $params = [])
    {
        // 不触发扣费逻辑
        $this->triggerType = AiAgentConstants::REPORT_ANALYSIS_TRIGGER_TYPE_AUTO;
        $this->stream = false;
        $this->llmService = new AIClient();
        $this->llmService->setModel(AIClient::AZURE_OPENAI_GPT_FOUR_O_MINI);
        $questionList = $this->loadQuestionListByRecordId($recordId);
        if (!empty($questionList)) return $this->formatQuestionList($questionList);

        $record = new \common\library\ai_agent\record\AiServiceProcessRecord($recordId);
        $this->referId = $recordId;
        $answer = $record->answer;

        if ($record->error_code != 0)
        {
            return [];
        }

        // 分析结果少于50个字符则不进行问题推荐
        if (strlen($answer) < 50) {
            return [];
        }

        $generateSqlAgent = new GenerateDataSqlAiAgent($this->clientId,$this->userId);
        $this->getQuestionRecommendPrompt($answer);

        // 保存历史记录
        $aiServiceProcessModel = parent::saveProcessRecord('-');
        $this->context['record_id'] = $aiServiceProcessModel->record_id;
        $standardResponse = $this->callLlm('-');
        $requestRespond = $standardResponse;

        $respondAnswer = $this->tryParseJson($standardResponse['answer']);
        $originQuestionList = json_decode($respondAnswer,true);
        $questionList = $generateSqlAgent->checkQuestionRecommend($originQuestionList, 6);
        $standardResponse['answer'] = json_encode($questionList,JSON_UNESCAPED_UNICODE);

        $this->updateAiServiceProcessRecord($aiServiceProcessModel->record_id,['llm_api_standard_response' => $standardResponse,
            'context' => [
                'originQuestionList' => $originQuestionList
            ],
            'request_response' => json_encode($requestRespond,JSON_UNESCAPED_UNICODE)
        ]);
        return $this->formatQuestionList($questionList);
    }


    public function loadQuestionListByRecordId($recordId)
    {
        $recordList = new AiServiceProcessRecordList($this->clientId,$this->userId,$this->sceneType);
        $recordList->setReferId($recordId);
        $recordList->setOrder("desc");
        $recordList->setOrderBy("record_id");
        $recordList->setLimit(1);
        $list = $recordList->find();
        if (empty($list)) return [];
        $record = $list[0];
        return json_decode($record['answer'],true);
    }


    public function getQuestionRecommendPrompt($answer)
    {
        $this->systemPrompt = $this->systemPromptMap[self::QUESTION_RECOMMEND_PROMPT] ?? '';

        $getTableFunction = function ($analysisResult) {
            $prompt = '';
            if (str_contains($analysisResult, '### 成交趋势分析')) {
                $prompt .= <<<EOT
- 客户成交趋势：分析该客户的成交趋势，了解客户购买行为变化。

EOT;
            }

            if (str_contains($analysisResult, '### 产品销售分析')) {
                $prompt .= <<<EOT
- 产品销售排行：在与该客户的成交中，查看产品根据销售额/销量在产品/产品类目/产品分组/产品型号上的排行
- 产品销售趋势对比:在与该客户的成交中，查看不同产品/产品型号/产品类目/产品分组在销售额/销量上的趋势

EOT;
            }

            if (str_contains($analysisResult, '### 邮件收发趋势分析')) {
                $prompt .= <<<EOT
- 邮件收发趋势：分析该客户收发邮件的频率和趋势，了解客户沟通情况。

EOT;
            }
            return $prompt;
        };

        $getExamplesFunction = function ($analysisResult) {
            $prompt = '';
            if (str_contains($analysisResult, '### 成交趋势分析')) {
                $prompt .= <<<EOT
### 成交趋势分析
如果分析结论中包含'成交趋势分析'，可以考虑根据下面几个问题进行推荐，否则不要推荐相关问题：
- 客户【xxx】近三个月订单数量对比
- 11月客户【xxx】订单来源渠道分析
- 客户【xxx】7月和12月的成交总金额
- 客户【xxx】7月和12月的订单状态分析
- 10月客户【xxx】高成交金额订单列表

EOT;
            }

            if (str_contains($analysisResult, '### 产品销售分析')) {
                $prompt .= <<<EOT
### 产品销售分析
如果分析结论中包含'产品销售分析'，可以考虑根据下面几个问题进行推荐，否则不要推荐相关问题：
- 10月客户【xxx】高成交金额订单中产品列表
- 客户【xxx】在11月份购买的产品类别及其购买金额和次数
- 客户【xxx]下半年每月采购金额趋势
- 客户【xxx】产品编号【产品编号】销售金额和数量的月度对比
- 客户【xxx】产品编号【产品编号】总销售数据
- 客户【xxx】产品类目【产品类目】类目的月度销售趋势

注意：【产品编号】和【产品类目】都需要替换成真实的产品编号、产品类目
例如：客户【xxx】MX2N-70FH-44MT产品的总销售数据，客户【xxx】的MX2N-43FH-24MT产品的月度销售对比

EOT;
            }

            if (str_contains($analysisResult, '### 邮件收发趋势分析')) {
                $prompt .= <<<EOT
### 邮件收发趋势分析
如果分析结论中包含'邮件收发趋势分析'，可以考虑根据下面几个问题进行推荐，否则不要推荐相关问题：
- 客户【xxx】未回复邮件主题统计
- 客户【xxx】哪些主题邮件回复率较高？
- 客户【xxx】有多少发送失败的邮件？
- 客户【xxx】最近一周的邮件收发情况？
- 客户【xxx】群发单显子邮件的回复率是多少？
- 客户【xxx】在不同月份的邮件打开率。

EOT;
            }

            if (str_contains($analysisResult , '国际站行为数据分析')) {

                $prompt .= <<<EOT
### 国际站行为数据分析
如果分析结论中包含‘国际站行为数据分析’，请根据下面几个问题模版进行推荐，否则不要推荐相关问题：
- 客户【xxx】过去一年内的订单总成交金额是多少？
- 客户【xxx】各产品类目订单数量统计
- 客户【xxx】是否购买产品类目【产品类目】类目的产品？
- 客户【xxx】过去半年内邮件未回复率是多少？
- 客户【xxx】产品类目【产品类目】类目的具体型号有哪些？
- 客户【xxx】产品类目【产品类目】类目的月度销售趋势
- 客户【xxx】历史订单中客单价最高的产品类别是什么？
- 上个月客户【xxx】高成交金额订单中产品列表

注意：【产品类目】都需要替换成真实的产品类目。不要推荐涉及浏览、登录、rfq、询盘、查看等相关的问题，不支持查询。
例如：客户【xxx】是否购买服装类品类产品？
EOT;

            }
            return $prompt;
        };

        $this->systemPrompt = str_replace('{{tables}}', $getTableFunction($answer), $this->systemPrompt);
        $this->systemPrompt = str_replace('{{examples}}', $getExamplesFunction($answer), $this->systemPrompt);
        $this->systemPrompt = str_replace('{{answer}}',$answer,$this->systemPrompt);

        return $this->systemPrompt;

    }

    public function formatQuestionList($questionList, $fromDB = true)
    {
        foreach ($questionList as &$questionItem)
        {
            $questionItem = str_replace("客户【xxx】", '',$questionItem);
            $questionItem = str_replace("【客户名称】", '',$questionItem);
            $questionItem = str_replace("客户【xxx】的", '',$questionItem);
        }

        return $questionList;
    }

    /**
     * 对比新旧阿里国际站店铺数据
     * @param $originProfileData
     * @param $profileData
     * @return bool
     */
    private function checkProfileDataChange($originProfileData, $profileData)
    {
        $compareKeys = [
            'is_fans','latest_visit_products','action_time','focus_time','footprint_ext_values','product_view_count',
            'valid_inquiry_count','valid_rfq_count','login_days','spam_inquiry_marked_by_supplier_count',
            'added_to_blacklist_count','total_order_volume','total_order_count','trade_supplier_count',
            'preferred_industries','latest_inquiry_products'
        ];

        if (empty($originProfileData) && !empty($profileData)) return true;
        if (!empty($originProfileData) && empty($profileData)) return true;
        if (empty($originProfileData) && empty($profileData)) return false;


        $hiddenFields = [];
        if (is_string($profileData['hidden_fields'] ?? [])) {
            $hiddenFields = json_decode($profileData['hidden_fields'],true);
        }

        if ($hiddenFields == ['*']) return false;

        $compareKeys = array_filter($compareKeys, function ($v) use ($hiddenFields) {
            return !in_array($v,$hiddenFields);
        });

        $flag = false;

        foreach ($compareKeys as $compareKey)
        {
            $originData = $originProfileData[$compareKey] ?? [];
            $data = $profileData[$compareKey] ?? [];
            if (empty($originData) && empty($data)) continue;

            if ($originData != $data) {
                $flag = true;
                break;
            }
        }

        return $flag;
    }


    /**
     * @param array $customerProfileData
     */
    public function setCustomerProfileData(array $customerProfileData)
    {
        $this->customerProfileData = $customerProfileData;
    }


}