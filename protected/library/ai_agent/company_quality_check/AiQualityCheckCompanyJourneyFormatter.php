<?php

namespace common\library\ai_agent\company_quality_check;

use AiQualityCheckCompanyModel;
use common\library\account\UserList;
use common\library\customer_v3\customer\CustomerList;
use common\library\ai_agent\AiAgentConstants;
use common\library\async_task\AsyncTaskConstant;
use common\library\async_task\AsyncTaskFilter;
use common\library\sns\customer\CustomerContactHelper;
use common\library\social_auth\Constant;
use common\library\trail\CompanyDynamicList;
use common\library\trail\TrailConstants;
use common\library\util\PgsqlUtil;

class AiQualityCheckCompanyJourneyFormatter extends \ListItemFormatter
{
    protected $clientId;

    protected $showCompanyId;

    /**
     * @param $clientId
     */
    public function __construct($clientId, $showCompanyId = 0)
    {
        $this->clientId = $clientId;
        $this->showCompanyId = $showCompanyId;
    }

    public function buildMapData()
    {
        $list = $this->batchFlag ? $this->listData : [$this->data];

        if (empty($list)) {
            return;
        }

        $journeyIds = array_column($list, 'journey_id');
        $conversationList = new AiQualityCheckCompanyConversationList($this->clientId);
        $conversationList->setJourneyId($journeyIds);
        $conversationList->setFields(['journey_id','refer_ids','sns_type']);
        $conversationList = $conversationList->find();
        $referIdMap = [];
        $snsTypeMap = [];
        $contactMap = [];
        foreach ($conversationList as $item){
            $referIds = PgsqlUtil::trimArray($item['refer_ids']) ?? [];
            $snsType = $item['sns_type'] ? [$item['sns_type']] : [];

            if((!empty($item['sns_type']))) {
                $referIdMap[$item['journey_id']][$item['sns_type']] = array_unique(array_merge($referIds, $referIdMap[$item['journey_id']][$item['sns_type']] ?? []));
            }
//            $referIdMap[$item['journey_id']] = array_unique(array_merge($referIds, $referIdMap[$item['journey_id']] ?? []));
            $snsTypeMap[$item['journey_id']] = array_unique(array_merge($snsType, $snsTypeMap[$item['journey_id']] ?? []));
        }

        foreach ($list as &$entity){
//            $entity['refer_ids'] = PgsqlUtil::formatArray($referIdMap[$entity['journey_id']] ?? []);
            $entity['sns_type'] = implode(',',$snsTypeMap[$entity['journey_id']] ?? []);
            $entity['refer_ids'] = $referIdMap[$entity['journey_id']] ?? [];
        }
        unset($entity);

        $list = Helper::filterQCJourneyList($list);
        $this->listData = $list;

        //journey_id 绑定 客户信息、消息条数、聊天记录/邮件跳转、关联文件
        $journeyIdToChatInfoMap = [];
        $mailInfoMap = [];
        $customerIds = [];
        $userIds = [];
        //聊天会话信息
        foreach ($list as $entity) {
            $referIdMap = $entity['refer_ids'];
            $params = [];
            $companyId = $entity['company_id'] ?? '';


            //FIXME 🤔MVP版本是否统计邮件待定
            /**
             * @use self::buildUserMail
             * @use self::buildUserContact
             */
            foreach ($referIdMap as $sceneType => $refer_ids) {
                if (empty($refer_ids))
                    continue;
                if ($sceneType == 'mail') {
                    $function = 'buildUserMail';
                    $contactTime = date('Y-m-d', strtotime($entity['contact_date']));
                    $params['start_time'] = $contactTime.' 00:00:00';
                    $params['end_time'] = $contactTime.' 23:59:59';
                    $params['mail_ids'] = $refer_ids;
                    $params['company_id'] = $companyId;
                    $result = call_user_func([$this, $function], $params);
                    if (empty($result)) {
                        continue;
                    }
                    $mailInfoMap[$entity['journey_id']] = $result;

                    //兼容v2.1版本
                    if (!empty($entity['user_id'])) {
                        $userIds[] = $entity['user_id'];
                    }
                    if (!empty($entity['customer_id'])) {
                        $customerIds[] = $entity['customer_id'] ?? 0;
                    }
                }else{
                    //whatsapp,tm,facebook...
                    $function = 'buildUserContact';
                    $params['user_contact_id'] = $refer_ids[0] ?? $entity['refer_id'];
                    $contactTime = date('Y-m-d', strtotime($entity['contact_date']));
                    $params['contact_start_time'] = $contactTime.' 00:00:00';
                    $params['contact_end_time'] = $contactTime.' 23:59:59';
                    $chatData = call_user_func([$this, $function], $params);
                    if (empty($chatData)) {
                        continue;
                    }
                    $journeyIdToChatInfoMap[$entity['journey_id']][$sceneType] = $chatData;
                    $customerIds[] = $chatData['customer_id'];
                    $userIds[] = $chatData['user_id'];
                }
            }
        }

        $stickingPointMap = [];
        if (!empty($list)) {
            $list = new AiQualityCheckStickingPointList($this->clientId, $user = \User::getLoginUser()->getUserId());
            $list->setJourneyIds($journeyIds);
            $data = $list->find();
            foreach ($journeyIds as $journeyId){
                $stickingPointMap[$journeyId] = [];
                foreach ($data as $item){
                    $journeyIdList = $item['journey_ids'] ?? [];
                    if(in_array($journeyId,$journeyIdList)){
                        $stickingPointMap[$journeyId][] = $item;
                    }
                }
            }
        }


        //user_id和nickname映射
        $userIdToNicknameMap = [];
        if (!empty($userIds)) {
            $userList = new UserList();
            $userList->setUserIds($userIds);
            $userList->setFields(['user_id', 'nickname']);
            $userList->setEnableFlag(null);
            $userList->setExcludeDeleteUserFlag(true);
            $userInfoList = $userList->find();
            $userIdToNicknameMap = array_column($userInfoList, null, 'user_id');
        }

        $customerIdToNicknameMap = [];
        if (!empty($customerIds)) {
            $customerIds = array_unique($customerIds);
            $customerList = new CustomerList($this->clientId);
            $customerList->setCustomerId($customerIds);
            $customerList->setFields(['customer_id', 'name']);
            $customerInfoList = $customerList->find();
            $customerIdToNicknameMap = array_column($customerInfoList, null, 'customer_id');
        }

        $map = [
            'userIdToNicknameMap' => $userIdToNicknameMap,
            'journeyIdToChatInfoMap' => $journeyIdToChatInfoMap,
            'customerIdToNicknameMap' => $customerIdToNicknameMap,
            'mailInfoMap' => $mailInfoMap,
            'stickingPointMap' => $stickingPointMap,
        ];

        $this->mapData = $map;
    }

    /**
     * 获取用户会话信息,返回sns_type,sns_id,user_sns_id,用于前端跳转
     * @param array params
     * @return array
     */
    private function buildUserContact(array $params): array
    {
        $res = [];
        $userContactId = $params['user_contact_id'];
        //CustomerContactList不支持主键做参数,参考动态页的SQL
        $tableName = 'tbl_user_customer_contact';
        $sql = "select * from {$tableName} where client_id = {$this->clientId}" . " and user_contact_id = " . $userContactId;
        $db = \PgActiveRecord::getDbByClientId($this->clientId);
        $userCustomerContactRecord = $db->createCommand($sql)->queryRow();
        if (empty($userCustomerContactRecord)) {
            \LogUtil::info("refer_id查不到userCustomerContac");
            return [];
        }
        $snsType = $userCustomerContactRecord['sns_type'];
        $userCustomerContactRecord['show_detail'] = 1;
        //是否展示详情(解绑了,或者绑定给另外的company,不展示详情),具体逻辑请找gavingao
        if ($snsType == \AiQualityCheckChatJourneyModel::SNS_TYPE_WHATSAPP) {
            $userCustomerContactRecord['show_detail'] = ($this->showCompanyId && $userCustomerContactRecord['company_id'] > 0 && $userCustomerContactRecord['company_id'] != $this->showCompanyId) ? 0 : 1;
        }
        $res['user_customer_contact_record'] = $userCustomerContactRecord;
        $res['customer_id'] = $userCustomerContactRecord['customer_id'];

        $startTime = $params['contact_start_time'];
        $endTime = $params['contact_end_time'];
        $tableName = 'tbl_user_customer_contact_message';
        $sql = "select * from {$tableName} where client_id = {$this->clientId}" . " and user_contact_id = " . $userContactId . " and send_time >= '{$startTime}' and send_time <= '{$endTime}' order by send_time desc";
        $customerContactMessageList = $db->createCommand($sql)->queryAll(true);
        //取当天最后一个人的send_id
        foreach ($customerContactMessageList as $messageItem)
        {
            //tm的send_id保证是业务员的user_id
            if($snsType == \AiQualityCheckChatJourneyModel::SNS_TYPE_TM) {
                $res['user_id'] = $messageItem['send_id'];
                break;
            }else{
                //其他渠道的send_id可能是买家的sns_id
                if($messageItem['send_type'] == CustomerContactHelper::SEND_TYPE_BY_USER) {
                    $res['user_id'] = $messageItem['send_id'];
                    break;
                }
            }
        }

        //1.send_id在message中为0/null,表示历史同步数据,没有小满的user_id
        //2.只有买家发消息,业务员没有回复
        if(empty($res['user_id'] ?? 0)) {
            $res['user_id'] = $userCustomerContactRecord['user_id'];
        }

        //聊天包含的文件
        $fileList = [];
        $hasFileMessage = array_filter($customerContactMessageList, function ($item) {
            return $item['type'] == 'file';
        });
        if (!empty($hasFileMessage)) {
            foreach ($hasFileMessage as $message) {
                $fileList[] = json_decode($message['body'], true);
            }
        }
        $res['file_list'] = $fileList;

        //聊天条数
        $res['customer_send_count'] = count(array_filter($customerContactMessageList, function ($item) {
            return $item['send_type'] == CustomerContactHelper::SEND_TYPE_BY_CUSTOMER;
        }));
        $res['user_send_count'] = count(array_filter($customerContactMessageList, function ($item) {
            return $item['send_type'] == CustomerContactHelper::SEND_TYPE_BY_USER;
        }));

        //tm的聊天记录跳转需要阿里店铺信息
        if ($snsType == \AiQualityCheckChatJourneyModel::SNS_TYPE_TM) {
            $tmData = [];
            [$sendId, $sendTime] = self::getSendIdInMessageList($customerContactMessageList);
            if (!empty($sendId)) {
                $buyerAccountId = $userCustomerContactRecord['sns_id'];
                $storeId = $userCustomerContactRecord['user_sns_id'];
                $sellerAccountInfo = Helper::getSellerAccountIdByUserContact($this->clientId, $sendId, $storeId);
                $date = \DateTime::createFromFormat('Y-m-d H:i:s', $sendTime);
                $tmData = [
                    'buyer_account_id' => $buyerAccountId,
                    'seller_account_id' => $sellerAccountInfo['seller_login_id'] ?? '',
                    'seller_login_id' => $sellerAccountInfo['seller_login_id'] ?? '',
                    'seller_account_email' => $sellerAccountInfo['seller_account_email'] ?? '',
                    'start_time' => strtotime($date->format('Y-m-d 00:00:00')) . '000',
                    'end_time' => strtotime($date->format('Y-m-d 23:59:59')) . '000'
                ];
            }
            $res['tm_data'] = $tmData;
        }

        $dynamicList = new CompanyDynamicList($this->clientId, $this->showCompanyId);
        $dynamicList->setOperatorUserId(\User::getloginUser()->getUserId());
        $dynamicList->setBeginCreateTime($params['contact_start_time']);
        $dynamicList->setEndCreateTime($params['contact_end_time']);
        $dynamicList->setModuleIds([TrailConstants::TYPE_CONTACT_MESSAGE_FACEBOOK,TrailConstants::TYPE_CONTACT_MESSAGE_WHATSAPP,
            TrailConstants::TYPE_CONTACT_MESSAGE_WHATSAPP_BUSINESS, TrailConstants::TYPE_CONTACT_MESSAGE_TM,TrailConstants::TYPE_CONTACT_MESSAGE_WHATSAPP_GROUP,
            TrailConstants::TYPE_CONTACT_MESSAGE_WECOM, TrailConstants::TYPE_CONTACT_MESSAGE_INSTAGRAM]);
        $dynamicList->setOrderBy('create_time');
        $dynamicList->setOrder('asc');
        $dynamicList->getFormatter()->setShowCompanyId($this->showCompanyId);
        $dynamicList->getFormatter()->setShowCaptureCard(true);
        $dynamicList->getFormatter()->setShowAdjustEmailDynamic(false);
        $dynamicList->getFormatter()->setShowCommentList(true);
        $dynamicList->getFormatter()->setShowOpportunityInfo(true);
        $ret = $dynamicList->find();
        $snsId = $userCustomerContactRecord['sns_id'];
        $userSnsId = $userCustomerContactRecord['user_sns_id'];

        foreach ($ret as $item){
            $data = $item['data'];
            $dynamicSnsId = $data['sns_id'] ?? ($data['buyer_account_id'] ?? '');
            $dynamicUserSnsId = $data['user_sns_id'] ?? ($data['store_id'] ?? '');
            if ((string)$dynamicSnsId == (string)$snsId && (string)$dynamicUserSnsId == (string)$userSnsId) {
                $item['opportunity_info'] && $res['opportunityInfo'] = $item['opportunity_info'];
                break;
            }
        }

        return $res;
    }

    /**
     * 获取邮件信息
     * @param $mailId
     *
     * @return array
     */
    private function buildUserMail($params)
    {
        $dynamicList = new CompanyDynamicList($this->clientId, $this->showCompanyId);
        $dynamicList->setOperatorUserId(\User::getloginUser()->getUserId());
        $dynamicList->setBeginTime($params['start_time']);
        $dynamicList->setEndTime($params['end_time']);
        $dynamicList->setCompanyId($params['company_id']);
//        $dynamicList->setModuleIds([202, 201]);
        $dynamicList->setType([202, 201]);

        $dynamicList->setOrderBy('create_time');
        $dynamicList->setOrder('asc');
        $dynamicList->getFormatter()->setShowCompanyId($this->showCompanyId);
        $dynamicList->getFormatter()->setShowCaptureCard(true);
        $dynamicList->getFormatter()->setShowAdjustEmailDynamic(false);
        $dynamicList->getFormatter()->setShowCommentList(true);
        $dynamicList->getFormatter()->setShowOpportunityInfo(true);
        $ret = $dynamicList->find();
        $dataList = array_column($ret,'data');
        $mailIds = [];
        foreach ($dataList as $data){
            $mailIds[] = $data['mail_id'];
        }
        \LogUtil::info('buildUserMail_mail_ids',['params' => $params,'mailIds' => $mailIds]);

        // 组装邮件信息列表
        $mailDataList = array_column($ret,'data');
        $mailList = [];
        $opportunityList = [];
        //文件列表
        $mailFileList = [];
        foreach ($ret as $item){
            $data = $item['data'];
            if(in_array($data['mail_id'] , $params['mail_ids'])){
                $mailList[] = $data;
                $opportunityList[$item['opportunity_id']] = $item['opportunity_info'];
                if (!empty($data['attachment_list'])) {
                    foreach ($data['attachment_list'] as $file){
                        $mailFileList[] = [
                            'file_name' => $file['file_name'] ?? '',
                            'download_url' => $file['file_url'] ?? '',
                            'file_size' => $file['file_size'] ?? 0,
                            'file_preview_url' => $file['preview_url'] ?? '',
                        ];
                    }
                }
            }
        }
        if(empty($mailList)){
            return [];
        }

        $user_send_count = 0;
        $customer_send_count = 0;
        foreach ($mailList as $mail){
            if($mail['mail_type'] == 1){
                $customer_send_count++;
            }else if($mail['mail_type'] == 2){
                $user_send_count++;
            }
        }

        // 组装邮件联系人列表
        $customerList = array_column($ret,'customer_info');
        $leadCustomerList = array_column($ret,'lead_customer_info');
        $customerList = array_filter(array_merge($customerList,$leadCustomerList));
        $customerInfoList = [];
        if(!empty($customerList)){
            foreach ($customerList as $customerInfo){
                foreach ($customerInfo as $customer){
                    $res = [];
                    if(array_key_exists($customer['customer_id'],$customerInfoList)){
                        continue;
                    }
                    $res['customer_id'] = $customer['customer_id'];
                    $res['name'] = $customer['name'];
                    $res['email'] = $customer['email'];
                    $customerInfoList[$customer['customer_id']] = $res;
                }
            }
        }
        $customerInfoList = array_values($customerInfoList);

        // // 组装邮件跟进人列表
        $userInfoList = [];
        $userList = array_column($ret,'create_user_info');
        foreach ($userList as $userInfo){
            $res = [];
            if(array_key_exists($userInfo['user_id'],$userInfoList)){
                continue;
            }
            $res['user_id'] = $userInfo['user_id'];
            $res['name'] = $userInfo['name'];
            $userInfoList[$userInfo['user_id']] = $res;

        }
        $userInfoList = array_values($userInfoList);

        $result['mailList'] = $mailList;
        $result['customerInfoList'] = $customerInfoList;
        $result['userInfoList'] = $userInfoList;
        $result['user_send_count'] = $user_send_count;
        $result['customer_send_count'] = $customer_send_count;
        $result['opportunityInfo'] = array_values($opportunityList);
        $result['fileList'] = $mailFileList;
        return $result;
    }

    private function getSendIdInMessageList($userCustomerContactMessageList)
    {
        $sendId = '';
        $sendTime = '';
        foreach ($userCustomerContactMessageList as $record) {
            if ($record['send_type'] == CustomerContactHelper::SEND_TYPE_BY_USER) {
                $sendId = $record['send_id'];
                $sendTime = $record['send_time'];
                break;
            }
        }

        return [$sendId, $sendTime];
    }


    protected function format($data)
    {
        //user and customer info
        $journeyIdToChatInfoMap = $this->getMapData('journeyIdToChatInfoMap', $data['journey_id']);
        $mailInfo = $this->getMapData('mailInfoMap', $data['journey_id']);
        $data['channel_detail'] = [];

        if (!empty($data['user_id'])) {
            $userInfoMap = $this->getMapData('userIdToNicknameMap', $data['user_id']);
            $data['user_info'] = [
                'user_id' => $data['user_id'],
                'nickname' => empty($userInfoMap) ? '' : $userInfoMap['nickname'] ?? '',
            ];
        }
        if (!empty($data['customer_id'])) {
            $customerInfoMap = $this->getMapData('customerIdToNicknameMap', $data['customer_id']);
            $data['customer_info'] = [
                'customer_id' => $data['customer_id'],
                'nickname' => empty($customerInfoMap) ? '' : $customerInfoMap['name'] ?? '',
            ];
        }

        $keywordList = $data['keywords'] ? PgsqlUtil::trimArray($data['keywords']) : [];
        $keywords = [];
        foreach ($keywordList as $item){
            // 不展示'谈单卡点'
            if($item == 13) {
                continue;
            }
            $keyword['label'] = AiAgentConstants::OCCURRED_EVENT_ENUM_TO_NAME_MAP[$item] ?? '';
            $keyword['label'] = \Yii::t('ai',$keyword['label']);
            $keyword['value'] = $item;
            $keywords[] = $keyword;
        }
        $data['keywords'] = $keywords;

        $fileList = [];
        // 邮件信息
        if(!empty($mailInfo)){
            $mailDetail = [];
            $mailDetail['mail_list'] = $mailInfo['mailList'] ?? [];
            $mailDetail['customer_info_list'] = $mailInfo['customerInfoList'] ?? [];
            $mailDetail['user_info_list'] = $mailInfo['userInfoList'] ?? [];
            $mailDetail['user_send_count'] = $mailInfo['user_send_count'];
            $mailDetail['customer_send_count'] = $mailInfo['customer_send_count'];
            $mailDetail['node_type'] = \AiQualityCheckChatJourneyModel::SNS_TYPE_TO_NODE_TYPE_MAP['mail'] ?? 0;
            $mailDetail['opportunity_info'] = empty($mailInfo['opportunityInfo']) ? [] : $mailInfo['opportunityInfo'][0];
            $fileList = array_merge($mailInfo['fileList'], $fileList);

            //V2.1 兼容代码
            $mailUserId = $data['user_id'] ?? 0;
            $mailCustomerId = $data['customer_id'] ?? 0;
            if(!empty($mailUserId) && !empty($this->getMapData('userIdToNicknameMap',$mailUserId))) {
                $userInfoMap = $this->getMapData('userIdToNicknameMap', $mailUserId);
                $mailDetail['user_info_list'] = [];
                $mailDetail['user_info_list'][] = [
                    'user_id' => $mailUserId,
                    'name' => empty($userInfoMap) ? '' : $userInfoMap['nickname'] ?? '',
                ];
            }

            if(!empty($mailCustomerId) && !empty($this->getMapData('customerIdToNicknameMap',$mailCustomerId))){
                $customerInfoMap = $this->getMapData('customerIdToNicknameMap', $mailCustomerId);
                $mailDetail['customer_info_list'] = [];
                $emails = $mailInfo['customerInfoList'] ?? [];
                $email = '';
                if(!empty($emails)) {
                    foreach ($emails as $item)
                    {
                        if($item['customer_id'] == $mailCustomerId) {
                            $email = $item['email'] ?? '';
                            break;
                        }
                    }
                }

                $mailDetail['customer_info_list'][] = [
                    'customer_id' => $mailCustomerId,
                    'name' => empty($customerInfoMap) ? '' : $customerInfoMap['name'] ?? '',
                    'email' => $email
                ];
            }
            $data['channel_detail'][Constant::CHANNEL_NAME_TYPE_MAP['mail']] = $mailDetail;
        }
        // 社媒信息
        if (!empty($journeyIdToChatInfoMap)) {
            foreach ($journeyIdToChatInfoMap as $sceneType => $chatInfo) {
                $userId = $chatInfo['user_id'] ?? 0;
                $customerId = $chatInfo['customer_id'] ?? 0;
                if (!empty($userId)) {
                    $userInfoMap = $this->getMapData('userIdToNicknameMap', $userId);
                    $data['channel_detail'][Constant::CHANNEL_NAME_TYPE_MAP[$sceneType]]['user_info'] = [
                        'user_id'  => $userId,
                        'nickname' => empty($userInfoMap) ? '' : $userInfoMap['nickname'] ?? '',
                    ];
                }
                if (!empty($customerId)) {
                    $customerInfoMap = $this->getMapData('customerIdToNicknameMap', $customerId);
                    $data['channel_detail'][Constant::CHANNEL_NAME_TYPE_MAP[$sceneType]]['customer_info'] = [
                        'customer_id' => $data['customer_id'],
                        'nickname' => empty($customerInfoMap) ? '' : $customerInfoMap['name'] ?? '',
                    ];
                }
                $data['channel_detail'][Constant::CHANNEL_NAME_TYPE_MAP[$sceneType]]['user_send_count'] = $chatInfo['user_send_count'];
                $data['channel_detail'][Constant::CHANNEL_NAME_TYPE_MAP[$sceneType]]['customer_send_count'] = $chatInfo['customer_send_count'];

                //chat info
                $snsFileList = [];
                foreach ($chatInfo['file_list'] as $file) {
                    if (empty($file['fileSize'] ?? '')) {
                        continue;
                    }
                    $snsFileList[] = [
                        'file_name' => $file['fileName'],
                        'download_url' => $file['url'],
                        'file_size' => $file['fileSize']
                    ];
                }
                $fileList = array_merge($snsFileList,$fileList);

                $snsType = $chatInfo['user_customer_contact_record']['sns_type'];
                $data['channel_detail'][Constant::CHANNEL_NAME_TYPE_MAP[$sceneType]]['node_type'] = \AiQualityCheckChatJourneyModel::SNS_TYPE_TO_NODE_TYPE_MAP[$snsType] ?? 0;
                $data['channel_detail'][Constant::CHANNEL_NAME_TYPE_MAP[$sceneType]]['user_contact_id'] = $chatInfo['user_customer_contact_record']['user_contact_id'];
                $data['channel_detail'][Constant::CHANNEL_NAME_TYPE_MAP[$sceneType]]['show_detail'] = $chatInfo['user_customer_contact_record']['show_detail'];
                $data['channel_detail'][Constant::CHANNEL_NAME_TYPE_MAP[$sceneType]]['chat_record'] = [
                    'channel_type' => $snsType,
                    'sns_id' => $chatInfo['user_customer_contact_record']['sns_id'],
                    'user_sns_id' => $chatInfo['user_customer_contact_record']['user_sns_id'],
                    'user_sns_nickname' => $chatInfo['user_customer_contact_record']['user_sns_nickname'],
                    'sns_nickname' => $chatInfo['user_customer_contact_record']['sns_nickname'],
                ];
                if ($snsType == \AiQualityCheckChatJourneyModel::SNS_TYPE_TM) {
                    $tmData = $chatInfo['tm_data'];
                    $data['channel_detail'][Constant::CHANNEL_NAME_TYPE_MAP[$sceneType]]['chat_record'] = $tmData;
                }
                !empty($chatInfo['opportunityInfo']) && $data['opportunity_info'] = $chatInfo['opportunityInfo'];
            }
        }
        $data['file_list'] = $fileList;

        $data['sticking_point_info'] = [];
        $stickingPointMap = key_exists('journey_id',$data) ? $this->getMapData('stickingPointMap', $data['journey_id']) : [];
        if(!empty($stickingPointMap)){
            $data['sticking_point_info'] = [];
            foreach ($stickingPointMap as $stickingPoint){
                $point = [];
                $point['sticking_point_id'] = $stickingPoint['sticking_point_id'] ?? '';
                $point['issue_description'] = $stickingPoint['issue_description'] ?? '';
                $data['sticking_point_info'][] = $point;
            }

        }

        $data['chat_time'] = date('Y-m-d', strtotime($data['contact_date']));

        // 多语言兼容
//        $contactProgressSummary = json_decode($data['contact_progress_summary'], true);
//        $contactSummary = '';
//
//        if (isset($contactProgressSummary['summary']))
//        {
//            $lang = \common\library\ai_agent\language\Helper::getLanguageKey(\AiAgent::AI_AGENT_SCENE_TYPE_COMPANY_QUALITY_CHECK, \Yii::app()->language);
//            $contactSummary = $contactProgressSummary['summary'][$lang] ?? $contactProgressSummary['summary']['cn'];
//        }

        // 兼容代码，历史数据中sticking_point与contact_stage都存在contact_summary中，新数据是分开存放
//        if(is_array(json_decode($data['contact_summary'], true))){
//            $data['contact_summary'] = !empty($contactSummary) ? $contactSummary : json_decode($data['contact_summary'], true);
//        }else{
//            $tmp = [];
//            $tmp['summary'] = !empty($contactSummary) ? $contactSummary : json_decode($data['contact_summary'],true);
//            $tmp['sticking_point'] = json_decode($data['sticking_point'],true);
//            $data['contact_summary'] = $tmp;
//        }
        $tmp = [];
        $tmp['summary'] = $data['summary'] ?? '';
        $tmp['sticking_point'] = $data['sticking_point_info'];
        $data['contact_summary'] = $tmp;

        // 阶段调整了，历史数据contact_stage可能与contact_summary['stage']不对应，统一取contact_stage
        $keyWords = $data['keywords'] ? array_column($data['keywords'], 'label') : [];
//        $keyWords = array_map(function ($item){
//            return AiQualityCheckCompanyModel::CONTACT_KEY_WORD_TO_NAME_MAP[$item] ?? '';
//        },$keyWords);
        $data['contact_keywords'] = array_values(array_filter($keyWords));

//        $data['contact_summary']['stage'] = AiQualityCheckCompanyModel::CONTACT_STAGE_TO_NAME_MAP[$data['contact_stage']] ?? '';

        $data['sns_type'] = \AiQualityCheckChatJourneyModel::SNS_TYPE_FORMAT_MAP[$data['sns_type']] ?? $data['sns_type'];

        foreach ($data['channel_detail'] as $key => $channelDetail){
            $userSendCount = $channelDetail['user_send_count'] ? intval($channelDetail['user_send_count']) : 0;
            $customerSendCount = $channelDetail['customer_send_count'] ? intval($channelDetail['customer_send_count']) : 0;
            $data['channel_detail'][$key]['message_count'] = $userSendCount + $customerSendCount;
        }


        return $data;
    }
}
