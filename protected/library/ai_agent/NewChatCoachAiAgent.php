<?php

namespace common\library\ai_agent;

use AiAgent;
use CDbDataReader;
use common\components\BaseObject;
use common\library\ai_agent\api\AIClient;
use common\library\ai_agent\api\DeepThinkingAiStreamClient;
use common\library\ai_agent\communication\Constants;
use common\library\ai_agent\communication\message_hub\Message;
use common\library\ai_agent\communication\message_hub\MessageHub;
use common\library\ai_agent\communication\summary\ChatService;
use common\library\ai_agent\exception\AiAgentException;
use common\library\ai_agent\message\AbstractMessageFormat;
use common\library\ai_agent\message\component\Button;
use common\library\ai_agent\message\component\FeedBack;
use common\library\ai_agent\message\component\IconText;
use common\library\ai_agent\message\CutResponseCard;
use common\library\ai_agent\message\Text;
use common\library\ai_agent\message\Card;
use common\library\ai_agent\message\ChatCoachcard;
use common\library\ai_agent\record\AiServiceProcessRecord;
use common\library\ai_agent\utils\ProtobufWebsocketResponse;
use common\library\prompt\AiServiceProcessRecordList;
use common\library\prompt\PromptConstant;
use Hyperf\Utils\Collection;

class NewChatCoachAiAgent extends BaseAiAgent
{
    use ChatTrait;

    protected bool $stream = true;

    protected bool $deepThinking = false;

    protected int $streamRiskBufferSize = 15;

    public string $systemPrompt = 'You are a veteran cross-border B2B e-commerce expert with extensive experience in international trade between China and foreign countries.';

    public int $maxHistoryLength = 20;

    protected array $stockPoint = [
        'issue' => '',
        'salesman_proposition' => '',
        'customer_attitude' => '',
        'resolved' => '',
    ];
    protected Message $lastVisitorMessage;
    protected array $aiReply = [
        'analysis' => '',
        'strategy' => [],
    ];
    /**
     * @var bool 是否成功
     */
    protected bool $isSuccess = false;


    public function getAgentSceneType(): int
    {
        return AiAgent::AI_AGENT_SCENE_TYPE_CHAT_COACH;
    }
    public function makeAgentProcessParams(): array
    {
        return $this->requestParams;
    }

    public function process(array $params = [], string $function = ''): AiAgentProcessResponse
    {
        $this->agentProcessParams = $params['params'] ?? [];
        $this->agentProcessParams['question'] = $this->question;

        // 判断是否加载 deepThink
        $this->deepThinking = $this->loadDeepSeek();
        if ($this->deepThinking) {
            $this->llmService->setModel($this->promptConfig['deep_seek_model'] ?? AIClient::VOLCANO_DEEPSEEK_R1);
        }

        // 获取聊天记录
        $messageHub = $this->makeMessageHub($params);
        $messages = $messageHub->getMessages();
        if ($messages->isEmpty()) {
            // 没有访客的消息
            throw new AiAgentException('', AiAgentException::ERR_NO_CHAT_HISTORY);
        }

        // 计算流程
        if ($this->isContinueAsk()) {
            // 追问删除systemPrompt
            $this->systemPrompt = '';
            // 追问逻辑
            $processType = NewChatReplyAiAgent::TYPE_CONTINUE_ASK;
            $prompt = $this->getContinueAskPrompt($this->agentProcessParams['question']);
        } else {

            if ($this->deepThinking) {
                $processType = NewChatReplyAiAgent::TYPE_DEEP_SEEK;
            } else {
                $processType = $this->determineChatCoachProcessType($messageHub, $messages, ($this->promptConfig['identify_stock_point_service'] ?? AIClient::AZURE_OPENAI_GPT_FOUR_O_LATEST));
            }

            $prompt = $this->getPromptTemplate($processType, $messages);
        }

        // 记录调用的prompt模块
        $aiProcessRecordPdo = new AiServiceProcessRecord($this->context['record_id']);
        $aiProcessRecordPdo->appendContext(['process_type' => $processType]);
        $aiProcessRecordPdo->save();

        $this->llmService->setTemperature(0.2);
        $gptResponse = $this->callLlm($prompt);

        if ($this->sseResponse instanceof ProtobufWebsocketResponse) {
            // 适配App端
            $cardType = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_COACH_CARD;
        } elseif ($this->deepThinking || $this->isContinueAsk()) {
            $cardType = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CUT_RESPONSE_CARD;
        } else {
            $cardType = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CARD;
        }

        return $this->initAiAgentProcessResponse(
            $gptResponse,
            __FUNCTION__,
            $cardType
        );
    }

    public function loadDeepSeek()
    {
        $isDeepSeek = $this->requestParams['params']['is_deep_seek'] ?? 0;

        $isDeepSeek = !($isDeepSeek == 0);

        // 初始化的时候，还没有获取到具体的deepSeek权限，在这覆盖 AiClient
        if ($isDeepSeek)
        {
            // 深度思考不要 system prompt
            $this->systemPrompt = '';
            $this->llmService = new DeepThinkingAiStreamClient();
        }

        return $isDeepSeek;
    }

    public function onReceiveStream(int $i, AbstractMessageFormat $message, string $str, AiAgentProcessResponse $response, string $fullText): void
    {
        if ($this->deepThinking) {
            $this->handleDeepSeekStream($i, $message, $str, $response, $fullText);
        } else {
            $this->handleStream($i, $message, $str, $response, $fullText);
        }
    }

    /**
     * DeepSeek 流式帧处理，需要解析思考过程
     */
    public function handleDeepSeekStream(int $i, AbstractMessageFormat $message, string $str, AiAgentProcessResponse $response, string $fullText)
    {
        /** @see self::getProcessSseObj() */
        $messages = $this->getMessageObj($response);
        $card = $messages[AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CUT_RESPONSE_CARD];

        $card->setAnswerHistoryId($response->historyId);
        $skeletonMessage = $card->getSkeletonMessage(); // 空卡片

        // 发送初始化消息
        if ($i === 0)
        {
            $skeletonMessage['status'] = AiAgentConstants::MESSAGE_STATUS_INIT;
            $this->sseResponse->writeJson($skeletonMessage);
        }

        $res = [];

        // 匹配思考过程
        $pattern = "/<REASONING_CONTENT_START>(.*?)<REASONING_CONTENT_END>/s";
        preg_match($pattern, $str, $matches);
        if (isset($matches[1]))
        {
            $reasoningContent = $matches[1];

            $res = $card->formatMessage([]);
            $res['context']['in_deep_thinking'] = 1;
            $res['context']['reasoning_content'] = $reasoningContent;
        }

        // 匹配正常内容回复
        $pattern = '/<MAIN_CONTENT_START>(.*?)<MAIN_CONTENT_END>/s';
        preg_match($pattern, $str, $matches);
        if (isset($matches[1]))
        {
            $content = $matches[1];

            $res = $card->formatMessage([]);
            $res['context']['in_deep_thinking'] = 0;
            $res['context']['content'] = $content;
        }

        usleep(25000);
        if (!empty($res)) {
            $this->sseResponse->writeJson($res);
        }
    }

    /**
     * web端追问逻辑处理，展示和深度思考一致
     */
    public function handleContinueAskStream(int $i, string $str, AiAgentProcessResponse $response)
    {
        /** @see self::getProcessSseObj() */
        $messages = $this->getMessageObj($response);
        $card = $messages[AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CUT_RESPONSE_CARD];

        $card->setAnswerHistoryId($response->historyId);
        $skeletonMessage = $card->getSkeletonMessage(); // 空卡片

        // 发送初始化消息
        if ($i === 0)
        {
            $skeletonMessage['status'] = AiAgentConstants::MESSAGE_STATUS_INIT;
            $this->sseResponse->writeJson($skeletonMessage);
        }

        $res = $card->formatMessage([]);
        $res['context']['content'] = $str;

        usleep(25000);
        if (!empty($res)) {
            $this->sseResponse->writeJson($res);
        }
    }


    public function handleStream(int $i, AbstractMessageFormat $message, string $str, AiAgentProcessResponse $response, string $fullText)
    {
        // web端的追问跟深度思考逻辑一致
        if ($this->isContinueAsk() && !($this->sseResponse instanceof ProtobufWebsocketResponse))
        {
            $this->handleContinueAskStream($i, $str, $response);
            return;
        }

        if ($this->isContinueAsk()) {
            $result = [
                'analysis' => $fullText,
                'strategy' => []
            ];
        } else {
            $result = $this->tryParseChatCoachReply($fullText);
            $result = [
                'analysis' => $result[\Yii::t('ai', '沟通分析')] ?? '',
                'strategy' => $result[\Yii::t('ai', '回复策略')] ?? []
            ];
        }

        /** @see self::getProcessSseObj() */
        $messages = $this->getMessageObj($response);

        if ($this->sseResponse instanceof ProtobufWebsocketResponse) {
            // 适配App端
            $card = $messages[AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_COACH_CARD];
        } else {
            $card = $messages[AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CARD];
        }

        $card->setAnswerHistoryId($response->historyId);
        $skeletonMessage = $card->getSkeletonMessage(); // 空卡片

        if ($this->aiReply !== $result)
        {
            if (empty($this->aiReply['analysis']) && empty($this->aiReply['strategy']))
            {
                // 发送初始化消息
                $skeletonMessage['status'] = AiAgentConstants::MESSAGE_STATUS_INIT;
                $skeletonMessage['stream_type'] = 'overwrite';
                $this->sseResponse->writeJson($skeletonMessage);
            }

            // 实时发送增量回复结果
            $this->aiReply = $result;
            $response->context = $result;

            if ($this->sseResponse instanceof ProtobufWebsocketResponse) {
                // 适配App端
                $res = $skeletonMessage;
                $res['status'] = AiAgentConstants::MESSAGE_STATUS_PROCESSING;
                $res['context']['analysis'] = $this->aiReply['analysis'];
                $res['context']['strategy'] = $this->aiReply['strategy'];
                $res['context']['stream_type'] = 'overwrite';
            } else {
                $html = $this->formatSSEResponse($this->aiReply);
                $res = $card->formatMessage([]);
                $res['context']['stream_type'] = 'overwrite';
                $res['context']['content'] = $html;
            }

            usleep(25000);  // 25ms
            $this->sseResponse->writeJson($res);
        }
    }

    public function beforeCloseMessage(AiAgentProcessResponse $response, string $fullText): void
    {
        // 埋点，假如存在话术
        if (str_contains($fullText, '<Response>')) {
            $this->updateAiProcessRecordContext($response->recordId, ['has_strategy' => 1]);
        }

        $this->isSuccess = true;
        $this->handleBilling();
    }

    /**
     * 生成历史记录
     * @param AiAgentProcessResponse $aiAgentProcessResponse
     * @return string
     */
    public function generateHistoryContent(AiAgentProcessResponse $aiAgentProcessResponse): string
    {
        if (!empty($aiAgentProcessResponse->function))
        {
            $function = $aiAgentProcessResponse->function;
            $function = "get{$function}SseObj";
            $messages = $this->$function($aiAgentProcessResponse);
            $message = $messages[$aiAgentProcessResponse->messageType] ?? new Text();
        } else {
            $message = new Text();
        }
        $history = $message->getSkeletonMessage();  // 空卡片
        $history = $history['context'] ?? [];

        // 获取消息体
        if (!empty($aiAgentProcessResponse->answer)) {
            // 有异常的时候会走到这里
            $history['content'] = $aiAgentProcessResponse->answer;
        } elseif ($this->isContinueAsk() && !$this->deepThinking) {
            // 追问是纯文本
            $history['content'] = $this->llmService->getGptResponse();
            $history = array_merge($history, $this->aiReply); // 兼容app端
        } elseif ($this->deepThinking && ($this->llmService instanceof DeepThinkingAiStreamClient)) {
            // 追问是纯文本
            $history['content'] = $this->llmService->getContent();
            $history['reasoning_content'] = $this->llmService->getReasoningContent();
        } else {
            // 首次提问
            $history['content'] = $this->formatSSEResponse($this->aiReply); // HTML
            $history = array_merge($history, $this->aiReply);
        }

        return json_encode($history, JSON_UNESCAPED_UNICODE);
    }

    public function formatSSEResponse(array $res): string
    {
        if (empty($res['analysis']) && empty($res['strategy'])) {
            throw new AiAgentException(AiAgentException::ERROR_CODE_TO_USER_MSG[AiAgentException::ERR_CHAT_COACH_FAILED], AiAgentException::ERR_CHAT_COACH_FAILED);
        }

        $html = '<div style="color: #141414; white-space: normal">';
        $communicationAnalysis = \Yii::t('ai', '沟通分析');
        $actionSuggestion = \Yii::t('ai', '行动建议');
        if (!empty($res['analysis'])) {
            $html .= '<div style="font-weight: 600">'. $communicationAnalysis .'：</div>';
            $html .= "<p style=\"font-weight: 400; margin-top: 4px\">{$res['analysis']}</p>";
        }

        if (!empty($res['strategy'])) {
            $html .= "<div style=\"font-weight: 600; margin-top: 16px\">{$actionSuggestion}：</div>";
            $html .= '<ul style="font-weight: 400; margin-top: 4px; padding-left: 1.5em">';
            foreach ($res['strategy'] as $item) {
                $html .= "<li style=\"list-style-type: disc\">{$item}</li>";
            }
            $html .= '</ul>';
        }
        $html .= '</div>';

        return $html;
    }

    protected function getPromptTemplate(string $type, Collection $messages): string
    {
        // 深度思考
        if ($type === NewChatReplyAiAgent::TYPE_DEEP_SEEK) {
            $prompt = $this->userPromptMap['deep_seek'] ?? '';
            $recentMessages = $messages->slice(-20, 20);
            $text = ChatService::buildChatMessages($recentMessages, (int)$this->agentProcessParams['channel_type']);
            $text .= $this->buildLastMessageTail($messages);    // 加上时间判断
            return str_replace(
                array(
                    '{RecentConversations}',
                    '{CurrentTime}',
                    '{CurrentWeekday}',
                ),
                array(
                    $text,
                    date('Y-m-d H:i:s'),
                    date('l'),
                ),
                $prompt);
        }

        // 卡点识别
        if ($type === NewChatReplyAiAgent::TYPE_STOCK) {
            $prompt = $this->userPromptMap['stock_identify'] ?? '';
            $recentMessages = $messages->slice(-10, 10);
            $text = ChatService::buildChatMessages($recentMessages, (int) $this->agentProcessParams['channel_type']);
            $text .= $this->buildLastMessageTail($messages);    // 加上时间判断
            return str_replace('{RecentConversations}', $text, $prompt);
        }

        // 引导回复
        if ($type === NewChatReplyAiAgent::TYPE_GUIDE) {
            $prompt = $this->userPromptMap['guide'] ?? '';
            $recentMessages = $messages->slice(-20, 20);
            $text = ChatService::buildChatMessages($recentMessages, (int) $this->agentProcessParams['channel_type']);
            $text .= $this->buildLastMessageTail($messages);    // 加上时间判断
            return str_replace('{RecentConversations}', $text, $prompt);
        }

        // 客户挽回
        if ($type === NewChatReplyAiAgent::TYPE_RETRIEVING) {
            $prompt = $this->userPromptMap['retrieving'] ?? '';
            $recentMessages = $messages->slice(-20, 20);
            $text = ChatService::buildChatMessages($recentMessages, (int) $this->agentProcessParams['channel_type']);
            $text .= $this->buildLastMessageTail($messages);    // 加上时间判断
            return str_replace('{RecentConversations}', $text, $prompt);
        }

        // 客户再激活
        if ($type === NewChatReplyAiAgent::TYPE_REACTIVE) {
            $prompt = $this->userPromptMap['reactive'] ?? '';

            $recentMessages = $messages->slice(-20, 20);
            $text = ChatService::buildChatMessages($recentMessages, (int) $this->agentProcessParams['channel_type']);
            $text .= $this->buildLastMessageTail($messages);    // 加上时间判断
            return str_replace(
                array('{RecentConversations}', '{CurrentDate}'),
                array($text, date('Y-m-d')),
                $prompt);
        }

        // 客户说服
        if ($type === NewChatReplyAiAgent::TYPE_PERSUADE) {
            $prompt = $this->userPromptMap['persuade'] ?? '';
            $recentMessages = $messages->slice(-20, 20);
            $text = ChatService::buildChatMessages($recentMessages, (int) $this->agentProcessParams['channel_type']);
            $text .= $this->buildLastMessageTail($messages);    // 加上时间判断
            return str_replace(
                array(
                    '{RecentConversations}',
                    '{CurrentDate}',
                    '{MainIssue}',
                    '{Proposition}',
                    '{Attitude}'
                ),
                array(
                    $text,
                    date('Y-m-d'),
                    $this->stockPoint['issue'],
                    $this->stockPoint["salesman_proposition"],
                    $this->stockPoint["customer_attitude"]
                ),
                $prompt);
        }

        // 卡点解决
        if ($type === NewChatReplyAiAgent::TYPE_RESOLVE) {
            $prompt = $this->userPromptMap['resolve'] ?? '';
            $recentMessages = $messages->slice(-20, 20);
            $text = ChatService::buildChatMessages($recentMessages, (int) $this->agentProcessParams['channel_type']);
            $text .= $this->buildLastMessageTail($messages);    // 加上时间判断
            return str_replace(
                array('{RecentConversations}', '{MainIssue}'),
                array($text, $this->stockPoint['issue']),
                $prompt);
        }


        return '';
    }

    /**
     * 生成追问的prompt
     */
    public function getContinueAskPrompt(string $question): string
    {
        $prompt = $this->userPromptMap['continue_ask'] ?? '';
        return str_replace('{question}', $question, $prompt);
    }


    /**
     * 获取第一次生成的沟通建议
     * @return array
     */
    public function getFirstResult(): array
    {
        // 获取第一次生成的沟通建议
        $latestProcessRecord = $this->getProcessRecordByOrder("ASC");
        $response = $latestProcessRecord['request_response'] ?? '{}';

        // 解析gpt响应
        $gptResponse = json_decode($response, true)['content'] ?? '';
        return $this->tryParseChatCoachReply($gptResponse);
    }

    /**
     * AI教练-沟通建议的小卡片
     *
     * @param AiAgentProcessResponse $aiAgentProcessResponse
     * @return array
     */
    public function getProcessSseObj(AiAgentProcessResponse $aiAgentProcessResponse): array
    {
        if ($this->deepThinking)
        {
            $card = new CutResponseCard(
                enableWatermark: true,
            );
            $card->setTitle(new IconText(icon: Button::ICON_CHAT_COACH, text: \Yii::t('ai', '沟通建议')));
            $card->setRightFooter([
                new FeedBack(icon: 'feedback', event: 'feedback', params: ['record_id' => $aiAgentProcessResponse->recordId])
            ]);
            $card->withRecordId($aiAgentProcessResponse->recordId);
            $card->withConversationId($this->conversationId);
            return [AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CUT_RESPONSE_CARD => $card];
        }

        if (($this->sseResponse instanceof ProtobufWebsocketResponse)) {
            // 适配App端
            $card = new ChatCoachcard(
                enableWatermark: true,
            );
        } elseif ($this->isContinueAsk()) {
            $card = new CutResponseCard(
                enableWatermark: true,
            );
        } else {
            $card = new Card(
                enableWatermark: true,
            );
        }

        // 左下角按钮需要根据渠道类型和时间判断是否显示
        $channelType = $this->agentProcessParams['channel_type'];
        $outsideFooterLeft = [
            new Button(
                text: \Yii::t('ai', '辅助回复'),
                event: Button::EVENT_SET_SCENE_TYPE,
                params: [
                    'recordId' => $aiAgentProcessResponse->recordId,
                    'separatorTitle' => \Yii::t('ai', '沟通建议已结束'),
                    'sceneType' => PromptConstant::SCENE_TYPE_AI_SERVICE_SCENE_AI_CHAT_REPLY,
                    'options' => $this->agentProcessParams,
                    'needCleanChatList' => false,
                    'lastSceneType' => PromptConstant::SCENE_TYPE_AI_SERVICE_SCENE_AI_CHAT_COACH
                ],
                history: false
            )
        ];

        // 新增开启deepseek按钮
        if (!$this->deepThinking && !$this->isContinueAsk())
        {
            $outsideFooterLeft = array_merge($outsideFooterLeft, [
                new Button(
                    text: \Yii::t('ai', 'DeepSeek-R1 深度分析'),
                    event: Button::EVENT_OPEN_DEEPSEEK_R1,
                    params: [
                        'recordId' => $aiAgentProcessResponse->recordId,
                        'sceneType' => \AiAgent::AI_AGENT_SCENE_TYPE_CHAT_COACH,
                        'options' => $this->agentProcessParams,
                    ],
                    history: false
                )
            ]);
        }

        if (in_array($channelType, [Constants::CHANNEL_TYPE_WABA, Constants::CHANNEL_TYPE_FACEBOOK]) && !empty($this->messageHub)) {
            /** @var TableStoreMessageHub $messageHub */
            $messageHub = $this->messageHub;
            $conversationInfo = method_exists($messageHub, 'getConversationInfo') ? $messageHub->getConversationInfo() : [];
            if (time() - ($conversationInfo['visitor_reply_time'] ?? 0) > 86400) {
                // 过期不显示辅助回复按钮
                $outsideFooterLeft = [];
            }
        }

        $card->setTitle(new IconText(text: \Yii::t('ai','沟通建议'), icon: Button::ICON_CHAT_COACH));
        $card->setLeftFooter([
            new Button(icon: 'copy', event: Button::EVENT_COPY, params: ['content' => ''])
        ]);
        $card->setRightFooter([
            new FeedBack(icon: 'feedback', event: 'feedback', params: ['record_id' => $aiAgentProcessResponse->recordId])
        ]);
        $card->setOutsideFooterLeft($outsideFooterLeft);
        $card->withRecordId($aiAgentProcessResponse->recordId);
        $card->withConversationId($this->conversationId);

        if ($this->sseResponse instanceof ProtobufWebsocketResponse) {
            return [AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_COACH_CARD => $card];
        } elseif ($this->isContinueAsk()) {
            return [AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CUT_RESPONSE_CARD => $card];
        }

        return [AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CARD => $card];
    }


    /**
     * 沟通建议的流程
     * @param MessageHub $messageHub
     * @param Collection $messages
     * @return string
     * @throws AiAgentException
     */
    public function determineChatCoachProcessType(
        communication\message_hub\MessageHub $messageHub,
        Collection $messages,
        string $model = AIClient::AZURE_OPENAI_GPT_FOUR_TURBO_LATEST
    ): string
    {
        $lastVisitorMessage = $messageHub->getLastVisitorMessage($messages);
        if (!$lastVisitorMessage) {
            $days = 999;
        } else {
            $days = $lastVisitorMessage->daysSinceSent();   // 距离买家上次发送消息的天数
        }

        if ($days >= 30) {
            // 客户再激活
            return NewChatReplyAiAgent::TYPE_REACTIVE;
        }

        if ($days >= 3) {
            // 客户挽回模块
            return NewChatReplyAiAgent::TYPE_RETRIEVING;
        }

        // 卡点识别
        $stockPoint = $this->identifyStockPoint($messages, $model);

        if (!$this->hasStockPoint()) {
            // 无卡点或者卡点已解决: 引导回复模块
            return NewChatReplyAiAgent::TYPE_GUIDE;
        }

        if (empty($stockPoint['salesman_proposition'])) {
            // 销售没有提出立场: 卡点解决模块
            return NewChatReplyAiAgent::TYPE_RESOLVE;
        }

        // 销售提出立场: 客户说服模块
        return NewChatReplyAiAgent::TYPE_PERSUADE;
    }

    /**
     * @param AIClient $aiClient
     * @param $data
     * @return array
     */
    public function parseStockPoint(AIClient $aiClient, $data): array
    {
        $standardResponse = $aiClient->convertResponse($data ?? []);
        $result = $this->tryParseJson($standardResponse['answer']);

        // 统一格式
        $result = [
            'issue' => $result["issue"] ?? 'None',
            "salesman_proposition" => $result["salesman's proposition"] ?? 'None',
            "customer_attitude" => $result["customer's attitude"] ?? 'None',
            'resolved' => $result['resolved'] ?? 'No',
        ];
        $result['resolved_bool'] = !in_array(strtolower($result['resolved']), ['no', '', 'none'], true);
        return $result;
    }


    public function getPreMessageList(array $params)
    {
        return [
            [
                'role' => AiAgentConstants::AI_AGENT_ROLE_TYPE_USER,
                'message_type' => AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT,
                'context' => [
                    'content' => \Yii::t('ai', AiAgentConstants::AI_CHAT_COACH_PRESET_MESSAGE),
                ],
                'continue_question' => true
            ],
        ];
    }


    /**
     * @return array
     */
    protected function getCloseMessageInfo(): array
    {
        return ['success_flag' => $this->isSuccess];
    }
}