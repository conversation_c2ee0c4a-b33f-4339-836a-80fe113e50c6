<?php

namespace common\library\ai_agent\analysis_record;

use common\library\ai_agent\AiAgentConstants;
use common\library\customer_v3\company\list\CompanyList;
use function DeepCopy\deep_copy;

class AnalysisRecordAPI
{
    public function __construct(
        private $clientId,
        private $userId = null
    ) {
    }

    /**
     * 批量获取列表
     *
     * @param array $params 
     * @param array $columns 
     * @return 
     */
    public function records(array $params, array $fields = [], $orderBy = '', $order = 'desc')
    {
        $filter = $this->buildFilter($params);
        if (!empty($order) && !empty($orderBy)) {
            $filter->order($orderBy, $order);
        }
        $filter->select($fields);
        return $filter->find()->getAttributes($fields);
    }

    /**
     * 批量插入
     *
     * @param array<AnalysisRecord> $records 
     * @return int
     */
    public function batchCreate(array $records)
    {
        $task = new AnalysisRecord($this->clientId);
        return $task->getOperator()->batchCreate($records);
    }

    public function buildFilter(array $params)
    {
        $filter = new AnalysisRecordFilter($this->clientId);

        if (isset($params['task_id'])) {
            $params['task_id'] === [] ? $filter->alwaysEmpty() : $filter->task_id = $params['task_id'];
        }

        // 'user_id', 'channel_type', 'customer_name', 'quality_params'
        if (isset($params['channel_type'])) {
            $filter->channelTypeIn((array)$params['channel_type']);
        }

        if (isset($params['user_id'])) {
            $filter->senderIdIn((array)$params['user_id']);
        }

        if (!empty($params['quality_params'])) {
            $filter->scoreRanges($params['quality_params']);
        }

        if (isset($params['status'])) {
            $filter->status = $params['status'];
        }

        if (isset($params['page']) && isset($params['page_size'])) {
            $filter->limit($params['page_size'], $params['page']);
        }

        if (isset($params['keyword']) && $params['keyword'] !== '') {
            $companyIds = deep_copy($filter)->select(['company_id' => fn () => "refer_data#>>'{company_id}' as company_id"])->rawData();
            $companyIds = array_column($companyIds, 'company_id');
            if (empty($companyIds)) {
                $filter->alwaysEmpty();
            } else {
                $companyList = new CompanyList($this->userId);
                $companyList->setSearchFields(['name', 'customer_list.name']);
                $companyList->setKeyword($params['keyword']);
                $companyList->setFields(['company_id']);
                $companyList->setCompanyIds($companyIds);
                $companyIds = array_column($companyList->find(), 'company_id');
                empty($companyIds) ? $filter->alwaysEmpty() : $filter->companyIdIn($companyIds);
            }
        }

        if (isset($params['scene_type'])) {
            $filter->scene_type = $params['scene_type'];
        }

        if (!empty($params['refer_key'])) {
            $filter->refer_key = $params['refer_key'];
        }

        return $filter;
    }

    /**
     * 更新analysis_record状态
     *
     * @param int $recordId 
     * @param int $status 
     */
    public function updateStatus(int $recordId, int $status, array $data = null)
    {
        $filter = new AnalysisRecordFilter($this->clientId);
        $filter->record_id = $recordId;
        $operator = $filter->find()->getOperator();
        $operator->updateStatus($status, $data);
    }

    /**
     * AI批量质检报告
     *
     * @param int $taskId
     * @return array
     */
    public function chatQualityReport(int $taskId)
    {
        $filterParams['task_id'] = $taskId;
        $filter = $this->buildFilter($filterParams);
        $batch = $filter->find();
        $batch->getFormatter()->chatQualityReportSetting();
        $records = $batch->getAttributes();

        $keys = array_values(AiAgentConstants::CHAT_QUALITY_FIELD_KEY_MAP);
        $nameMap = array_flip(AiAgentConstants::CHAT_QUALITY_FIELD_KEY_MAP);

        $teamPerformance = [];
        foreach ($keys as $key) {
            $teamPerformance[$key] = [
                'key' =>  $key,
                'name' => $nameMap[$key],
                'conversation_num' => 0,       // 会话总数
                'valid_conversation_num' => 0, // 排除分数为-1的会话总数
                'average' => -1,
                'poor_conversation_num' => 0,
                'poor_percentage' => 0,
            ];
        }
        $userPerformance = [];

        $lastBCScale = bcscale();
        bcscale(6);
        foreach ($records as $record) {
            // if ($record['status'] != AnalysisRecordConstant::STATUS_SUCCEED) {
            //     continue;
            // }
            $result = $record['analysis_result'] ?? [];
            $userId = $record['user_id'];

            if (empty($userPerformance[$userId])) {
                $userPerformance[$userId] = array_fill_keys($keys, -1);
                $userPerformance[$userId] = array_merge($userPerformance[$userId], [
                    'conversation_num' => 0, // 会话总数
                    'valid_conversation_num' => array_fill_keys($keys, 0), // 排除分数为-1的会话总数
                    'user_id' => $userId,
                    'user_info' => $record['user_info'] ?? [],
                ]);
            }
            $userItem = &$userPerformance[$userId];
            $userItem['conversation_num']++;
 
            foreach ($keys as $key) {
                // ====== 团队维度 ====== 
                $teamItem = &$teamPerformance[$key];
                $teamItem['conversation_num']++;

                if (empty($result[$key]) || $result[$key]['value'] < 0) {
                    continue;
                }

                $teamItem['valid_conversation_num']++;
                $userItem['valid_conversation_num'][$key]++;

                // 递推求均值
                $value = $result[$key]['value'];
                $teamItem['average'] < 0 && $teamItem['average'] = 0; // 默认为-1，第一次赋值为0

                // $teamItem['average'] += ($value - $teamItem['average']) / $teamItem['valid_conversation_num'];
                $teamItem['average'] = (float) bcadd($teamItem['average'], bcdiv(bcsub($value, $teamItem['average']), $teamItem['valid_conversation_num']));

                if ($value < 70) {
                    $teamItem['poor_conversation_num']++;
                }

                // ====== 用户维度 ======
                // 递推求均值
                $userItem[$key] < 0 && $userItem[$key] = 0; // 默认为-1，第一次赋值为0
                $userItem[$key] = (float) bcadd($userItem[$key], bcdiv(bcsub($value, $userItem[$key]), $userItem['valid_conversation_num'][$key]));
            }

            // 计算问题率
            foreach ($keys as $key) {
                $teamItem = &$teamPerformance[$key];
                $teamItem['poor_percentage'] = round($teamItem['poor_conversation_num'] * 100 / $teamItem['conversation_num'], 2);
            }
        }
        bcscale($lastBCScale);

        // 统一格式化, 保留两位小数
        foreach ($teamPerformance as $k => $item) {
            $teamPerformance[$k]['average'] = round($item['average'], 2);
        }
        foreach ($userPerformance as $k => $item) {
            foreach ($keys as $key) {
                $userPerformance[$k][$key] = round($item[$key], 2);
            }
        }

        return [
            'user_performance' => array_values($userPerformance),
            'team_performance' => array_values($teamPerformance),
        ];
    }

    /**
     * AI批量质检会话明细
     *
     * @param int $taskId
     * @param array $filterParams
     */
    public function chatQualityDetails(int $taskId, array $filterParams = [])
    {
        $filterParams['task_id'] = $taskId;
        // $filterParams['status'] = AnalysisRecordConstant::STATUS_SUCCEED;
        $filter = $this->buildFilter($filterParams);

        $batch = $filter->find();
        $batch->getFormatter()->chatQualityDetailsSetting();
        $records = $batch->getAttributes();

        $count = $filter->count();

        return [
            'conversations' => $records,
            'count' => $count,
        ];
    }
}
