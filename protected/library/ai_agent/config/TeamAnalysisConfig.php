<?php

namespace common\library\ai_agent\config;

class TeamAnalysisConfig
{

    private array $config = [];
    private static $instance;

    const NOT_RICHNESS = 0;
    const RICHNESS = 1;

    // 数据丰富度阀值
    const NOT_ENOUGH_ASSET_ANALYSIS_DATA_ROW = 2;
    const NOT_ENOUGH_ASSET_ANALYSIS_DATA_PERCENT = 0.8;


    // 主场景
    const WORK_PROCESS_SITUATION = 'work_process_situation'; //工作过程情况
    const FOLLOW_UP_WITH_CUSTOMERS = 'follow_up_with_customers'; // 新老客户跟进
    const TASK_COMPLETION_STATUS = 'task_completion_status'; // 任务完成情况
    const OPPORTUNITY_FOLLOW_UP_SITUATION = 'opportunity_follow_up_situation'; // 商机转化情况


    // 子场景
    // --工作过程情况
    const CUSTOMER_EMAIL_SENDING_AND_RECEIVING_SITUATION = 'customer_email_sending_and_receiving_situation'; //客户邮件收发情况
    const WORKLOAD_STATISTICS = 'workload_statistics'; //工作量统计
    const CUSTOMER_FOLLOW_UP_UPDATES = 'customer_follow_up_updates'; // 客户跟进动态

    const CUSTOMER_FOLLOW_UP_TREND = 'customer_follow_up_trend';

    // -- 新老客户跟进
    const TRENDS_IN_THE_NUMBER_OF_NEW_CUSTOMERS = 'trends_in_the_number_of_new_customers'; //新增客户数变化趋势
    const NEW_CUSTOMER_CONVERSION = 'new_customer_conversion'; // 新客户转换
    const OLD_CUSTOMERS_REPURCHASE = 'old_customers_repurchase'; //老客户复购


    // -- 任务完成情况
    const CUSTOMER_FOLLOW_UP_STATUS = 'customer_follow_up_status'; //客户是否及时跟进
    const MAIL_REPLY_STATUS = 'mail_reply_status'; //邮件是否及时回复
    const TM_REPLY_STATUS = 'tm_reply_status'; //TM是否及时回复

    // 商机转化情况
    const SALES_FUNNEL_ANALYSIS = 'sales_funnel_analysis'; // 销售漏斗分析
    const ANALYSIS_OF_FAIL_OPPORTUNITY_REASONS = 'analysis_of_fail_opportunity_reasons'; // 输单原因分析


    const PROMPT_FUNCTION_MAP = [
        self::CUSTOMER_EMAIL_SENDING_AND_RECEIVING_SITUATION => 'buildAnyOnePrompt',
        self::WORKLOAD_STATISTICS => 'buildAnyOnePrompt',
        self::CUSTOMER_FOLLOW_UP_UPDATES => 'buildAnyOnePrompt',
        self::CUSTOMER_FOLLOW_UP_TREND => 'buildAnyOnePrompt',


        self::TRENDS_IN_THE_NUMBER_OF_NEW_CUSTOMERS => 'buildAnyOnePrompt',
        self::NEW_CUSTOMER_CONVERSION => 'buildAnyOnePrompt',
        self::OLD_CUSTOMERS_REPURCHASE => 'buildAnyOnePrompt',


        self::CUSTOMER_FOLLOW_UP_STATUS => 'buildAnyOnePrompt',
        self::MAIL_REPLY_STATUS => 'buildAnyOnePrompt',
        self::TM_REPLY_STATUS => 'buildAnyOnePrompt',


        self::SALES_FUNNEL_ANALYSIS => 'buildAnyOnePrompt',
        self::ANALYSIS_OF_FAIL_OPPORTUNITY_REASONS => 'buildAnyOnePrompt',

    ];

    //以子场景构建总分析使用
    const PROMPT_KEY_MAP = [
        self::CUSTOMER_EMAIL_SENDING_AND_RECEIVING_SITUATION => [
            'list' => [
                [
                    'content' => [
                        "101_INSIGHT" => '- 客户邮件收发情况:  ##101_INSIGHT##',
                    ]
                ]
            ],
        ],
        self::WORKLOAD_STATISTICS => [
            'list' => [
                [
                    'content' => [
                        "102_INSIGHT" => '- 工作量统计:  ##102_INSIGHT##',
                    ]
                ]
            ],
        ],
        self::CUSTOMER_FOLLOW_UP_UPDATES => [
            'list' => [
                [
                    'content' => [
                        "103_INSIGHT" => '- 客户跟进动态:  ##103_INSIGHT##',
                    ]
                ]
            ],
        ],


        self::TRENDS_IN_THE_NUMBER_OF_NEW_CUSTOMERS => [
            'list' => [
                [
                    'content' => [
                        "104_INSIGHT" => '- 新增客户数变化趋势: ##104_INSIGHT##',
                    ]
                ]
            ],
        ],
        self::NEW_CUSTOMER_CONVERSION => [
            'list' => [
                [
                    'content' => [
                        "105_INSIGHT" => '- 新客户转化:  ##105_INSIGHT##',
                    ]
                ]
            ],
        ],
        self::OLD_CUSTOMERS_REPURCHASE => [
            'list' => [
                [
                    'content' => [
                        "106_INSIGHT" => '- 老客户复购:  ##106_INSIGHT##',
                    ]
                ]
            ],
        ],

        self::CUSTOMER_FOLLOW_UP_STATUS => [
            'list' => [
                [
                    'content' => [
                        "107_INSIGHT" => '- 客户是否及时跟进: ##107_INSIGHT##',
                    ]
                ]
            ],
        ],
        self::MAIL_REPLY_STATUS => [
            'list' => [
                [
                    'content' => [
                        "108_INSIGHT" => '- 邮件是否及时回复:  ##108_INSIGHT##',
                    ]
                ]
            ],
        ],
        self::TM_REPLY_STATUS => [
            'list' => [
                [
                    'content' => [
                        "109_INSIGHT" => '- TM是否及时回复: ##109_INSIGHT##',
                    ]
                ]
            ],
        ],

        self::SALES_FUNNEL_ANALYSIS => [
            'list' => [
                [
                    'content' => [
                        "110_INSIGHT" => '- 销售漏斗分析:  ##110_INSIGHT##',
                    ]
                ]
            ],
        ],
        self::ANALYSIS_OF_FAIL_OPPORTUNITY_REASONS => [
            'list' => [
                [
                    'content' => [
                        "111_INSIGHT" => '- 输单原因分析:  ##111_INSIGHT##',
                    ]
                ]
            ],
        ],

        self::CUSTOMER_FOLLOW_UP_TREND => [
            'list' => [
                [
                    'content' => [
                        "112_INSIGHT" => '- 客户跟进情况:  ##112_INSIGHT##',
                    ]
                ]
            ],
        ],

    ];


    private function initConfig()
    {
        $this->config = [
            self::WORK_PROCESS_SITUATION => [

                'title' => \Yii::t('ai','工作过程情况'),

                'analysis_list' => [

                    self::WORKLOAD_STATISTICS => [

                        'title' => '',

                        'sub_analysis_list' => [
                            [
                                'list' => [
                                    102 => [
                                        'report_key' => 'xs1',
                                        'sub_title' => '工作量统计',
                                        'key' => 102,
                                        'cut_out' => 200,
                                        'out_put_field' => 'INSIGHT',
                                        'date_field' => 'common.date',
                                        'report_data_format_function' => ['filterEmptyColum'],

                                        'order' => [
                                            [
                                                'key' => 'sum-user.company_add_count',
                                                'field' => 'sum-user.company_add_count',
                                                'order' => 'desc',
                                            ],
                                            [
                                                'key' => 'sum-user.customer_remark_count',
                                                'field' => 'sum-user.customer_remark_count',
                                                'order' => 'desc',
                                            ]
                                        ],
                                        'field' => [],
                                        'params' => [
                                            ['field' => 'common.date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')]]
                                        ],
                                    ],
                                ],
                                'prompts' => [
                                    'system_prompt' => <<<SYSTEM_PROMPT

You are an excellent Data Analyst working in a Business company. Based on the provided data, analyze the performance of different employees. Identify which employees are performing well, which are underperforming, and explain the reasons behind these conclusions. 
Here is the provided data(Employee workload statistics, horizontal comparison of employee workloads to understand the overall work situation of employees) in CSV format delimited by triple quotes below:

##102_xs1_report_data##

After reviewing the data, summarize your findings in three sentences, focusing on employee performance and the rationale for your assessment. Include specific performance metrics and numbers to make your points more persuasive.
In your findings, Avoid using extreme terms such as "highest", "lowest", "most", or "least" to prevent fostering unhealthy competition among employees. Instead, use balanced language that encourages a constructive work environment.

Output your findings in JSON format as follows:
{{
  "INSIGHT": "Your summarized findings in Chinese here."
}}

SYSTEM_PROMPT,
                                    'user_prompt' => <<<USER_PROMPT
Begin! no explanation.
Take a deep breath and work on this problem step-by-step.
USER_PROMPT
                                ],
                            ],
                        ]
                    ],

                    self::CUSTOMER_FOLLOW_UP_TREND => [

                        'title' => '',

                        'sub_analysis_list' => [
                            [
                                'list' => [
                                    112 => [
                                        'report_key' => 'khgj3',
                                        'sub_title' => '客户跟进情况',
                                        'key' => 112,
                                        'cut_out' => 200,
                                        'out_put_field' => 'INSIGHT',
                                        'date_field' => 'common.date',
                                        'report_data_format_function' => ['filterEmptyColum'],
                                        'filter_field_condition' => [
                                            'follow.user_name' => ['空', '未知', '无']
                                        ],
                                        'order' => [
                                            [
                                                'key' => 'sum-send.message_count',
                                                'field' => 'sum-send.message_count',
                                                'order' => 'desc',
                                            ],
                                            [
                                                'key' => 'sum-send.message_company_count',
                                                'field' => 'sum-send.message_company_count',
                                                'order' => 'desc',
                                            ],
                                            [
                                                'key' => 'sum-send.mail_count',
                                                'field' => 'sum-send.mail_count',
                                                'order' => 'desc',
                                            ],
                                            [
                                                'key' => 'sum-send.mail_company_count',
                                                'field' => 'sum-send.mail_company_count',
                                                'order' => 'desc',
                                            ],
                                            [
                                                'key' => 'sum-send.edm_count',
                                                'field' => 'sum-send.edm_count',
                                                'order' => 'desc',
                                            ],
                                            [
                                                'key' => 'sum-send.edm_company_count',
                                                'field' => 'sum-send.edm_company_count',
                                                'order' => 'desc',
                                            ],
                                            [
                                                'key' => 'sum-send.edm_company_count',
                                                'field' => 'sum-send.edm_company_count',
                                                'order' => 'desc',
                                            ],
                                            [
                                                'key' => 'sum-follow.record_count',
                                                'field' => 'sum-follow.record_count',
                                                'order' => 'desc',
                                            ],
                                            [
                                                'key' => 'sum-follow.record_company_count',
                                                'field' => 'sum-follow.record_company_count',
                                                'order' => 'desc',
                                            ],
                                        ],
                                        'params' => [
                                            ['field' => 'common.date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')]]
                                        ],
                                    ],
                                ],
                                'prompts' => [
                                    'system_prompt' => <<<SYSTEM_PROMPT

You are an excellent Data Analyst working in a Business company. Based on the provided data, analyze the performance of different employees. Identify which employees are performing well, which are underperforming, and explain the reasons behind these conclusions. 
Here is the provided data(Follow-up trend data, based on existing follow-up statistics, to analyze employee communication behaviors during client follow-ups, including sending chats, emails, EDM, and writing follow-up records.) in CSV format delimited by triple quotes below:

##112_khgj3_report_data##

After reviewing the data, summarize your findings in three sentences, focusing on employee performance and the rationale for your assessment. Include specific performance metrics and numbers to make your points more persuasive.
In your findings, Avoid using extreme terms such as "highest", "lowest", "most", or "least" to prevent fostering unhealthy competition among employees. Instead, use balanced language that encourages a constructive work environment.

Output your findings in JSON format as follows:
{{
  "INSIGHT": "Your summarized findings in Chinese here."
}}
SYSTEM_PROMPT,
                                    'user_prompt' => <<<USER_PROMPT
Begin! no explanation.
Take a deep breath and work on this problem step-by-step.
USER_PROMPT
                                ],
                            ],
                        ]
                    ],

                    self::CUSTOMER_EMAIL_SENDING_AND_RECEIVING_SITUATION => [

                        'title' => '',

                        'sub_analysis_list' => [
                            [
                                'list' => [
                                    101 => [
                                        'report_key' => 'xs14',
                                        'key' => 101,
                                        'sub_title' => '客户邮件收发情况',
                                        'cut_out' => 100,
                                        'out_put_field' => 'INSIGHT',
                                        'date_field' => 'common.date',
                                        'report_data_format_function' => ['filterEmptyColum'],
                                        'order' => [
                                            [
                                                'key' => 'sum-user.send',
                                                'field' => 'sum-user.send',
                                                'order' => 'desc',
                                            ],
                                            [
                                                'key' => 'sum-user.receive',
                                                'field' => 'sum-user.receive',
                                                'order' => 'desc',
                                            ]
                                        ],
                                        'params' => [
                                            ['field' => 'common.date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')]]
                                        ]
                                    ],
                                ],
                                'prompts' => [
                                    'system_prompt' => <<<SYSTEM_PROMPT
You are an excellent Data Analyst working in a Business company. Based on the provided data, analyze the performance of different employees. Identify which employees are performing well, which are underperforming, and explain the reasons behind these conclusions. 
Here is the provided data(Employee and client email communication data, based on existing mailing list statistics, to understand the usage of the employee email module) in CSV format delimited by triple quotes below:

##101_xs14_report_data##

After reviewing the data, summarize your findings in three sentences, focusing on employee performance and the rationale for your assessment. Include specific performance metrics and numbers to make your points more persuasive.
In your findings, Avoid using extreme terms such as "highest", "lowest", "most", or "least" to prevent fostering unhealthy competition among employees. Instead, use balanced language that encourages a constructive work environment.

Output your findings in JSON format as follows:
{{
  "INSIGHT": "Your summarized findings in Chinese here."
}}

SYSTEM_PROMPT,
                                    'user_prompt' => <<<USER_PROMPT
Begin! no explanation.
Take a deep breath and work on this problem step-by-step.

USER_PROMPT
                                ],
                            ]
                        ]
                    ],

                    self::CUSTOMER_FOLLOW_UP_UPDATES => [

                        'title' => '',

                        'sub_analysis_list' => [
                            [
                                'list' => [
                                    103 => [
                                        'report_key' => 'xs12',
                                        'sub_title' => '客户跟进动态',
                                        'key' => 103,
                                        'cut_out' => 200,
                                        'out_put_field' => 'INSIGHT',
                                        'date_field' => 'common.date',
                                        'report_data_format_function' => ['filterEmptyColum'],
                                        'filter_field_condition' => [
                                            'company.create_user_name' => ['空', '未知', '无']
                                        ],
                                        'order' => [
                                            [
                                                'key' => 'sum-user_trail.remark_add',
                                                'field' => 'sum-user_trail.remark_add',
                                                'order' => 'desc',
                                            ],
                                            [
                                                'key' => 'sum-user_trail.remark_relate_mail',
                                                'field' => 'sum-user_trail.remark_relate_mail',
                                                'order' => 'desc',
                                            ]
                                        ],
                                        'params' => [
                                            ['field' => 'common.date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')]]
                                        ],
                                    ],
                                ],
                                'prompts' => [
                                    'system_prompt' => <<<SYSTEM_PROMPT

You are an excellent Data Analyst working in a Business company. Based on the provided data, analyze the performance of different employees. Identify which employees are performing well, which are underperforming, and explain the reasons behind these conclusions. 
Here is the provided data(Employee customer follow-up dynamic data, to understand the overall dynamics of employees) in CSV format delimited by triple quotes below:

##103_xs12_report_data##

After reviewing the data, summarize your findings in three sentences, focusing on employee performance and the rationale for your assessment. Include specific performance metrics and numbers to make your points more persuasive.
In your findings, Avoid using extreme terms such as "highest", "lowest", "most", or "least" to prevent fostering unhealthy competition among employees. Instead, use balanced language that encourages a constructive work environment.

Output your findings in JSON format as follows:
{{
  "INSIGHT": "Your summarized findings in Chinese here."
}}
SYSTEM_PROMPT,
                                    'user_prompt' => <<<USER_PROMPT
Begin! no explanation.
Take a deep breath and work on this problem step-by-step.
USER_PROMPT
                                ],
                            ],
                        ]
                    ],



                ],

                'prompts' => [
                    'system_prompt' => <<<SYSTEM_PROMPT
You are a highly skilled Team Leader in a business company. You will be provided with employee performance findings for different employees. Your task is to summarize the performance of employee in three sentences, focusing on key points. Then, provide three sentences of specific action suggestions to enhance their work performance. Include specific performance metrics and numbers to make your points more persuasive.
Here is the employees performance findings:
\'\'\'
- 客户邮件收发情况: {{customer_email_sending_and_receiving_situation}}
- 工作量统计: {{workload_statistics}}
- 客户跟进动态: {{customer_follow_up_updates}}
- 客户跟进情况: {{customer_follow_up_trend}}
\'\'\'

Use the following JSON format for your response:
{{
  "SUMMARIES": "Your summaries in chinese here."
  "SUGGESTIONS": "Your suggestions in chinese here."
}}

SYSTEM_PROMPT,
                    'user_prompt' => <<<USER_PROMPT
Begin! no explanation.
Take a deep breath and work on this problem step-by-step.
USER_PROMPT
                ]

            ],

            self::FOLLOW_UP_WITH_CUSTOMERS => [

                'title' => '新老客户跟进',

                'analysis_list' => [

                    self::TRENDS_IN_THE_NUMBER_OF_NEW_CUSTOMERS => [

                        'title' => '',

                        'sub_analysis_list' => [
                            [
                                'list' => [
                                    104 => [
                                        'report_key' => 'khts2',
                                        'sub_title' => '新增客户数变化趋势',
                                        'key' => 104,
                                        'out_put_field' => 'INSIGHT',
                                        'date_field' => 'company.select_date',
//                                        'field' => [
//                                            'company.date',
//                                            'company.create_user_name',
//                                            'mergeCount-company.create_ids',
//                                            'monthOnMonthIncreaseNum-company.create_ids',
//                                            'monthOnMonthIncrease-company.create_ids',
//                                            'yearOnYearIncreaseNum-company.create_ids',
//                                            'yearOnYearIncrease-company.create_ids',
//                                        ],

                                        'special_order' => [
                                            'type' => 'date',
                                            'key' => 'company.date',
                                            'select_cycle' => 'week'
                                        ],
                                        // 特殊逻辑 转换khts2列排序
                                        'report_data_format_function' => ['buildKhts2ReportData'],

                                        'filter_field_condition' => [
                                            'company.create_user_name' => ['空', '未知', '无']
                                        ],
                                        'params' => [
                                            ['field' => 'common.select_cycle', 'type' => 'select', 'value' => 'month'],
                                            ['field' => 'company.select_date', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')]]
                                        ]
                                    ]
                                ],
                                'prompts' => [
                                    'system_prompt' => <<<SYSTEM_PROMPT
You are an excellent Data Analyst working in a Business company. Based on the provided data, analyze the performance of different employees. Identify which employees are performing well, which are underperforming, and explain the reasons behind these conclusions. 
Here is the provided data(Trend in the number of new customers added by employees) in CSV format delimited by triple quotes below:
'''
##104_khts2_report_data##
'''

After reviewing the data, summarize your findings in three sentences, focusing on employee performance and the rationale for your assessment. Include specific performance metrics and numbers to make your points more persuasive.
In your findings, Avoid using extreme terms such as "highest", "lowest", "most", or "least" to prevent fostering unhealthy competition among employees. Instead, use balanced language that encourages a constructive work environment.

Output your findings in JSON format as follows:
{{
  "INSIGHT": "Your summarized findings in Chinese here."
}}

Begin! no explanation.

SYSTEM_PROMPT,
                                    'user_prompt' => <<<USER_PROMPT
USER_PROMPT
                                ]
                            ],
                        ]
                    ],

                    self::NEW_CUSTOMER_CONVERSION => [

                        'title' => '',

                        'sub_analysis_list' => [
                            [
                                'list' => [
                                    105 => [
                                        'report_key' => 'kh15',
                                        'sub_title' => '新客户转化',
                                        'out_put_field' => 'INSIGHT',
                                        'date_field' => 'company.archive_time',
                                        'key' => 105,
                                        'params' => [
                                            ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')]]
                                        ],
                                        'filter_field_condition' => [
                                            'company.user_id' => ['空', '未知', '无',0]
                                        ],
                                        'order' => [
                                            [
                                                'key' => 'company.company_id',
                                                'field' => 'row-company.company_id',
                                                'order' => 'desc',
                                            ],
                                            [
                                                'key' => 'performance.company_id',
                                                'field' => 'row-performance.company_id',
                                                'order' => 'desc',
                                            ]
                                        ],
                                    ]
                                ],
                                'prompts' => [
                                    'system_prompt' => <<<SYSTEM_PROMPT
You are an excellent Data Analyst working in a Business company. Based on the provided data, analyze the performance of different employees. Identify which employees are performing well, which are underperforming, and explain the reasons behind these conclusions. 
Here is the provided data(The conversion situation of new customers by different employees, understanding the new customer conversion rate and the average transaction amount per customer) in CSV format delimited by triple quotes below:
'''
##105_kh15_report_data##
'''

After reviewing the data, summarize your findings in three sentences, focusing on employee performance and the rationale for your assessment. Include specific performance metrics and numbers to make your points more persuasive.
In your findings, Avoid using extreme terms such as "highest", "lowest", "most", or "least" to prevent fostering unhealthy competition among employees. Instead, use balanced language that encourages a constructive work environment.

Output your findings in JSON format as follows:
{{
  "INSIGHT": "Your summarized findings in Chinese here."
}}

Begin! no explanation.
SYSTEM_PROMPT,
                                    'user_prompt' => <<<USER_PROMPT
Response in Chinese, Let's think step by step
USER_PROMPT
                                ]
                            ],
                        ]

                    ],

                    self::OLD_CUSTOMERS_REPURCHASE => [

                        'title' => '',

                        'sub_analysis_list' => [
                            [
                                'list' => [
                                    106 => [
                                        'report_key' => 'kh20',
                                        'sub_title' => '老客户复购',
                                        'out_put_field' => 'INSIGHT',
                                        'key' => 106,
                                        'date_field' => 'company.archive_time',
                                        'filter_field_condition' => [
                                            'company.user_id' => ['空', '未知', '无',0]
                                        ],

                                        'order' => [
                                            [
                                                'key' => 'company.new_customers_list',
                                                'field' => 'row-company.new_customers_list',
                                                'order' => 'desc',
                                            ],
                                            [
                                                'key' => 'company.repeat_customers_list',
                                                'field' => 'row-company.repeat_customers_list',
                                                'order' => 'desc',
                                            ]
                                        ],
                                        'params' => [
                                            ['field' => 'company.archive_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')]]
                                        ],

                                    ]
                                ],
                                'prompts' => [
                                    'system_prompt' => <<<SYSTEM_PROMPT
You are an excellent Data Analyst working in a Business company. Based on the provided data, analyze the performance of different employees. Identify which employees are performing well, which are underperforming, and explain the reasons behind these conclusions. 
Here is the provided data(Customer repurchase data, understanding the number of new customers closed, the number of repurchasing customers, the proportion of repurchasing customers, and the repurchase cycle for different employees) in CSV format delimited by triple quotes below:
'''
##106_kh20_report_data##
'''

After reviewing the data, summarize your findings in three sentences, focusing on employee performance and the rationale for your assessment. Include specific performance metrics and numbers to make your points more persuasive.
In your findings, Avoid using extreme terms such as "highest", "lowest", "most", or "least" to prevent fostering unhealthy competition among employees. Instead, use balanced language that encourages a constructive work environment.

Output your findings in JSON format as follows:
{{
  "INSIGHT": "Your summarized findings in Chinese here."
}}

Begin! no explanation.
SYSTEM_PROMPT,
                                    'user_prompt' => <<<USER_PROMPT
Response in Chinese, Let's think step by step
USER_PROMPT
                                ]
                            ]
                        ]
                    ],

                ],
                'prompts' => [
                    'system_prompt' => <<<SYSTEM_PROMPT
You are a highly skilled Team Leader in a business company. You will be provided with employee performance findings for different employees. Your task is to summarize the performance of employee in three sentences, focusing on key points. Then, provide three sentences of specific action suggestions to enhance their work performance. Include specific performance metrics and numbers to make your points more persuasive.
Here is the employees performance findings:
'''
{{trends_in_the_number_of_new_customers}}
{{new_customer_conversion}}
{{old_customers_repurchase}}
'''

Use the following JSON format for your response:
{{
  "SUMMARIES": "Your summaries in chinese here.",
  "SUGGESTIONS": "Your suggestions in chinese here."
}}

Begin! no explanation.

SYSTEM_PROMPT,
                    'user_prompt' => <<<USER_PROMPT
Take a deep breath and work on this problem step-by-step.
USER_PROMPT

                ],

            ],

            self::TASK_COMPLETION_STATUS => [
                'title' => '任务完成情况',

                'analysis_list' => [
                    self::CUSTOMER_FOLLOW_UP_STATUS => [

                        'title' => '',

                        'sub_analysis_list' => [
                            [
                                'list' => [
                                    107 => [
                                        'report_key' => 'task1',
                                        'sub_title' => '客户是否及时跟进',
                                        'key' => 107,
                                        'cut_out' => 200,
                                        'out_put_field' => 'INSIGHT',
                                        'date_field' => 'common.deadline',

                                        'filter_field_condition' => [
                                            'task.user_id' => ['空', '未知', '无']
                                        ],

                                        'order' => [
                                            [
                                                'key' => 'task.target_company_follow_count',
                                                'field' => 'sum-task.target_company_follow_count',
                                                'order' => 'desc',
                                            ],
                                            [
                                                'key' => 'task.target_company_follow_count',
                                                'field' => 'sum-task.target_company_follow_count',
                                                'order' => 'desc',
                                            ]
                                        ],
                                        'params' => [
                                            ['field' => 'common.deadline', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')]]
                                        ],
                                    ],
                                ],
                                'prompts' => [
                                    'system_prompt' => <<<SYSTEM_PROMPT
You are an excellent Data Analyst working in a Business company. Based on the provided data, analyze the performance of different employees. Identify which employees are performing well, which are underperforming, and explain the reasons behind these conclusions. 
Here is the provided data(Data on the completion of customer follow-up tasks by different employees, assessing task completion status) in CSV format delimited by triple quotes below:
'''
##107_task1_report_data##
'''

After reviewing the data, summarize your findings in three sentences, focusing on employee performance and the rationale for your assessment. Include specific performance metrics and numbers to make your points more persuasive.
In your findings, Avoid using extreme terms such as "highest", "lowest", "most", or "least" to prevent fostering unhealthy competition among employees. Instead, use balanced language that encourages a constructive work environment.

Output your findings in JSON format as follows:
{{
  "INSIGHT": "Your summarized findings in Chinese here."
}}

Begin! no explanation.
SYSTEM_PROMPT,
                                    'user_prompt' => <<<USER_PROMPT
Response in Chinese, Let's think step by step
USER_PROMPT
                                ]
                            ],
                        ]
                    ],
                    self::MAIL_REPLY_STATUS => [

                        'title' => '',

                        'sub_analysis_list' => [
                            [
                                'list' => [
                                    108 => [
                                        'report_key' => 'task2',
                                        'sub_title' => '邮件是否及时回复',
                                        'key' => 108,
                                        'cut_out' => 200,
                                        'out_put_field' => 'INSIGHT',
                                        'date_field' => 'common.deadline',

                                        'filter_field_condition' => [
                                            'task.user_id' => ['空', '未知', '无']
                                        ],

                                        'order' => [
                                            [
                                                'key' => 'task.target_reply_mail_count',
                                                'field' => 'sum-task.target_reply_mail_count',
                                                'order' => 'desc',
                                            ],
                                            [
                                                'key' => 'task.in_time_reply_mail_count',
                                                'field' => 'sum-task.in_time_reply_mail_count',
                                                'order' => 'desc',
                                            ]
                                        ],
                                        'params' => [
                                            ['field' => 'common.deadline', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')]]
                                        ],
                                    ],
                                ],
                                'prompts' => [
                                    'system_prompt' => <<<SYSTEM_PROMPT
You are an excellent Data Analyst working in a Business company. Based on the provided data, analyze the performance of different employees. Identify which employees are performing well, which are underperforming, and explain the reasons behind these conclusions. 
Here is the provided data(Data on the completion of email reply tasks by different employees, assessing the task completion status) in CSV format delimited by triple quotes below:
'''
##108_task2_report_data##
'''

After reviewing the data, summarize your findings in three sentences, focusing on employee performance and the rationale for your assessment. Include specific performance metrics and numbers to make your points more persuasive.
In your findings, Avoid using extreme terms such as "highest", "lowest", "most", or "least" to prevent fostering unhealthy competition among employees. Instead, use balanced language that encourages a constructive work environment.

Output your findings in JSON format as follows:
{{
  "INSIGHT": "Your summarized findings in Chinese here."
}}

Begin! no explanation.
SYSTEM_PROMPT,
                                    'user_prompt' => <<<USER_PROMPT
Take a deep breath and work on this problem step-by-step.
USER_PROMPT
                                ]
                            ]
                        ]
                    ],
                    self::TM_REPLY_STATUS => [
                        'title' => '',

                        'sub_analysis_list' => [
                            [
                                'list' => [
                                    109 => [
                                        'report_key' => 'task3',
                                        'sub_title' => 'TM是否及时回复',
                                        'key' => 109,
                                        'cut_out' => 200,
                                        'out_put_field' => 'INSIGHT',

                                        'filter_field_condition' => [
                                            'task.user_id' => ['空', '未知', '无']
                                        ],

                                        'order' => [
                                            [
                                                'key' => 'task.target_reply_tm_count',
                                                'field' => 'sum-task.target_reply_tm_count',
                                                'order' => 'desc',
                                            ],
                                            [
                                                'key' => 'task.in_time_reply_tm_count',
                                                'field' => 'sum-task.in_time_reply_tm_count',
                                                'order' => 'desc',
                                            ]
                                        ],
                                        'date_field' => 'common.deadline',

                                        'params' => [
                                            ['field' => 'common.deadline', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')]]
                                        ],

                                    ],
                                ],
                                'prompts' => [
                                    'system_prompt' => <<<SYSTEM_PROMPT
You are an excellent Data Analyst working in a Business company. Based on the provided data, analyze the performance of different employees. Identify which employees are performing well, which are underperforming, and explain the reasons behind these conclusions. 
Here is the provided data(Data on the completion of TM(TradeManager, TM is a fast and effective instant messaging tool on Alibaba.com) reply tasks by different employees, assessing the task completion status.) in CSV format delimited by triple quotes below:
'''
##109_task3_report_data##
'''

After reviewing the data, summarize your findings in three sentences, focusing on employee performance and the rationale for your assessment. Include specific performance metrics and numbers to make your points more persuasive.
In your findings, Avoid using extreme terms such as "highest", "lowest", "most", or "least" to prevent fostering unhealthy competition among employees. Instead, use balanced language that encourages a constructive work environment.

Output your findings in JSON format as follows:
{{
  "INSIGHT": "Your summarized findings in Chinese here."
}}

Begin! no explanation.
SYSTEM_PROMPT,
                                    'user_prompt' => <<<USER_PROMPT
Response in Chinese, Let's think step by step
USER_PROMPT
                                ]
                            ]
                        ]
                    ]
                ],

                'prompts' => [
                    'system_prompt' => <<<SYSTEM_PROMPT
You are a highly skilled Team Leader in a business company. You will be provided with employee performance findings for different employees. Your task is to summarize the performance of employee in three sentences, focusing on key points. Then, provide three sentences of specific action suggestions to enhance their work performance. Include specific performance metrics and numbers to make your points more persuasive.
Here is the employees performance findings:
'''
{{customer_follow_up_status}}
{{mail_reply_status}}
{{tm_reply_status}}
'''

Use the following JSON format for your response:
{{
  "SUMMARIES": "Your summaries in chinese here.",
  "SUGGESTIONS": "Your suggestions in chinese here."
}}

Begin! no explanation.

SYSTEM_PROMPT,
                    'user_prompt' => <<<USER_PROMPT
Take a deep breath and work on this problem step-by-step.
USER_PROMPT
                ]
            ],

            self::OPPORTUNITY_FOLLOW_UP_SITUATION => [
                'title' => '商机转化情况',

                'analysis_list' => [
                    self::SALES_FUNNEL_ANALYSIS => [

                        'title' => '',

                        'sub_analysis_list' => [
                            [
                                'list' => [
                                    110 => [
                                        'report_key' => 'sjts3',
                                        'sub_title' => '销售漏斗分析',
                                        'key' => 110,
                                        'cut_out' => 100,
                                        'out_put_field' => 'INSIGHT',
                                        'date_field' => 'opportunity.create_time',
                                        'order' => [
                                            [
                                                'key' => 'user.opportunity_count',
                                                'field' => 'sum-user.opportunity_count',
                                                'order' => 'desc',
                                            ],
                                            [
                                                'key' => 'user.win_number',
                                                'field' => 'sum-user.win_number',
                                                'order' => 'desc',
                                            ],
                                        ],
                                        'params' => [
                                            ['field' => 'opportunity.create_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')]]
                                        ],
                                        'filter_field_condition' => [
                                            'user.user_id' => ['空', '未知', '无']
                                        ],
                                    ],
                                ],
                                'prompts' => [
                                    'system_prompt' => <<<SYSTEM_PROMPT
You are an excellent Data Analyst working in a Business company. Based on the provided data, analyze the performance of different employees. Identify which employees are performing well, which are underperforming, and explain the reasons behind these conclusions. 
Here is the provided data(Employee sales funnel data, horizontal comparison of employee sales capabilities) in CSV format delimited by triple quotes below:
'''
##110_sjts3_report_data##
'''

After reviewing the data, summarize your findings in three sentences, focusing on employee performance and the rationale for your assessment. Include specific performance metrics and numbers to make your points more persuasive.
In your findings, Avoid using extreme terms such as "highest", "lowest", "most", or "least" to prevent fostering unhealthy competition among employees. Instead, use balanced language that encourages a constructive work environment.

Output your findings in JSON format as follows:
{{
  "INSIGHT": "Your summarized findings in Chinese here."
}}

Begin! no explanation.
SYSTEM_PROMPT,
                                    'user_prompt' => <<<USER_PROMPT
Take a deep breath and work on this problem step-by-step.
USER_PROMPT
                                ]
                            ],
                        ]
                    ],
                    self::ANALYSIS_OF_FAIL_OPPORTUNITY_REASONS => [

                        'title' => '',

                        'sub_analysis_list' => [
                            [
                                'list' => [
                                    111 => [
                                        'report_key' => 'sj16',
                                        'sub_title' => '输单原因分析',
                                        'key' => 111,
                                        'cut_out' => 200,
                                        'out_put_field' => 'INSIGHT',
                                        'date_field' => 'opportunity.create_time',
                                        'params' => [
                                            ['field' => 'opportunity.create_time', 'type' => 'date', 'value' => ['start' => date('Y-m-d', strtotime('-3 months')), 'end' => date('Y-m-d')]]
                                        ],

                                        'filter_field_condition' => [
                                            'user.user_id' => ['空', '未知', '无']
                                        ],
                                    ],
                                ],
                                'prompts' => [
                                    'system_prompt' => <<<SYSTEM_PROMPT
You are an excellent Data Analyst working in a Business company. Based on the provided data, analyze the performance of different employees. Identify which employees are performing well, which are underperforming, and explain the reasons behind these conclusions. 
Here is the provided data(Distribution of reasons for lost opportunities, examining the distribution of reasons for lost opportunities among different employees) in CSV format delimited by triple quotes below:
'''
##111_sj16_report_data##
'''
Please note that only the records of employees who lose opportunities will appear in the above data, hence the greater the total sales amount and record count, the poorer the employee performance.

After reviewing the data, summarize your findings in three sentences, focusing on employee performance and the rationale for your assessment. Include specific performance metrics and numbers to make your points more persuasive.
In your findings, Avoid using extreme terms such as "highest", "lowest", "most", or "least" to prevent fostering unhealthy competition among employees. Instead, use balanced language that encourages a constructive work environment.

Output your findings in JSON format as follows:
{{
  "INSIGHT": "Your summarized findings in Chinese here."
}}

Begin! no explanation.
SYSTEM_PROMPT,
                                    'user_prompt' => <<<USER_PROMPT
Response in Chinese, Let's think step by step
USER_PROMPT
                                ]
                            ],
                        ]
                    ],

                ],

                'prompts' => [
                    'system_prompt' => <<<SYSTEM_PROMPT
You are a highly skilled Team Leader in a business company. You will be provided with employee performance findings for different employees. Your task is to summarize the performance of employee in three sentences, focusing on key points. Then, provide three sentences of specific action suggestions to enhance their work performance. Include specific performance metrics and numbers to make your points more persuasive.
Here is the employees performance findings:
"""
{{sales_funnel_analysis}}
{{analysis_of_fail_opportunity_reasons}}
"""

Use the following JSON format for your response:
{{
  "SUMMARIES": "Your summaries in chinese here.",
  "SUGGESTIONS": "Your suggestions in chinese here."
}}

Begin! no explanation.

SYSTEM_PROMPT,
                    'user_prompt' => <<<USER_PROMPT
Take a deep breath and work on this problem step-by-step.
USER_PROMPT
                ]
            ]
        ];
    }

    public static function getInstance($clientId)
    {
        if (null === static::$instance) {
            static::$instance = new static($clientId);
        }

        return static::$instance;
    }


    /**
     * @return array
     */
    public function getConfig(): array
    {
        $this->initConfig();

        return $this->config;
    }

    public function buildAnyOnePrompt(array $params)
    {
        $analysisKey = $params['analysis_key'];
        $analysisResultKeyByKey = $params['analysis_result_key_by_key'];

        $promptInfos = TeamAnalysisConfig::PROMPT_KEY_MAP[$analysisKey];

        $prompt = '';

        // 拼接header
        $header = $promptInfos['header'] ?? '';

        $end = $promptInfos['end'] ?? '';


        // 拼接list
        $list = $promptInfos['list'] ?? [];
        $listPrompt = '';

        foreach ($list as $item) {
            $content = $item['content'] ?? [];

            // 插入content
            $insertGroup = false;
            $contentPrompt = '';
            foreach ($content as $key => $value) {
                $toBeReplacedPrompt = '';

                if (isset($analysisResultKeyByKey[$key])) {
                    $insertGroup = true;
                    $toBeReplacedPrompt = $value;
                    $toBeReplacedPrompt = str_replace("##{$key}##", $analysisResultKeyByKey[$key], $toBeReplacedPrompt);
                }

                if (!empty($toBeReplacedPrompt)) {
                    $contentPrompt .= "{$toBeReplacedPrompt}\n";
                }
            }

            // 插入分组
            $group = $item['group'] ?? '';
            if ($insertGroup && !empty($group)) {
                $contentPrompt = "{$group}\n{$contentPrompt}";
            }

            if (!empty($contentPrompt)) {
                $listPrompt .= "$contentPrompt\n";
            }
        }

        $listPrompt = trim($listPrompt, "\n");

        // anyOne 表示任意一条即可，假如一条都没有，那么返回空
        if (!empty($listPrompt)) {
            $filterSymbol = $promptInfos['filter_symbol'] ?? false;
            if ($filterSymbol) {
                $prompt = <<<PROMPT
{$listPrompt}
PROMPT;
            } else {
                $prompt = <<<PROMPT
{$header}
"""
{$listPrompt}
"""

{$end}

PROMPT;
            }
        }

        return $prompt;
    }


    public static function getDefaultAssetAnalysisList()
    {
        return [
            [
                "key" => self::WORK_PROCESS_SITUATION,
                "title" => \Yii::t('ai','工作过程情况'),
                'richness' => 1,
                "conclusion" => \Yii::t('ai', "员工C在客户邮件互动方面的表现为零，表明可能需要建立通信渠道或增强客户互动。员工B的邮件回复率虽低，但客户打开率高，显示其内容吸引客户关注。员工A在邮件互动和工作量方面表现突出，显示了高效的沟通和工作活跃度。"),
                "suggestion" => \Yii::t('ai', "针对员工C，建议提供邮件沟通和客户管理的培训，并监督其建立有效的客户通信渠道。对于员工B，应鼓励提高回复率，例如设定每日回复客户邮件的目标，并优化邮件内容以提高客户互动。对员工A，可以设定更高的业绩目标，并考虑将其作为案例分享给团队，以提升整体工作效率。"),
                'list' => [
                    [
                        'key' => self::CUSTOMER_EMAIL_SENDING_AND_RECEIVING_SITUATION,
                        "title" => "",
                        "list" => [
                            [
                                'richness' => 1,
                                'sub_key' => 101,
                                "title" => \Yii::t('ai', "客户邮件收发情况"),
                                "conclusion" => \Yii::t('ai', "从提供的数据来看，员工C的客户邮件发送数和收到数均为0，表明可能存在通信渠道未建立或客户互动缺失的情况。员工B的客户邮件发送数为53，收到数为328，我方回复率为7.93%，尽管对方回复数为0，但对方打开率达到100%，显示出客户对邮件内容有较高的关注度。员工A的客户邮件发送数为259，收到数为488，我方回复数为27，对方回复数为22，显示出与客户的互动较为频繁，且对方回复率为11.39%，对方打开率为58.46%，表明其邮件沟通效果相对较好。"),
                                'data_type' => 1,
                                'report_detail_data' => [
                                    "key" => "xs14",
                                    "title" => \Yii::t('report', "邮件"),
                                    "name" => "邮件",
                                    "desc" => \Yii::t('report', "员工邮件数据概览，基于现有邮件列表数据统计,掌握员工邮件模块使用情况 数据范围：你有权限查看的人员的邮件数据"),
                                    "type" => "group",
                                    "relevance" => [
                                        [
                                            "key" => "xs4",
                                            "name" => "所有邮件"
                                        ],
                                        [
                                            "key" => "xs14",
                                            "name" => "客户邮件"
                                        ]
                                    ],
                                    "data" => [
                                        [
                                            [
                                                "key" => "user.user_id",
                                                "type" => "field",
                                                "label" => \Yii::t('report','员工C'),
                                                "value" => "0",
                                                "summaries" => [
                                                    "sum-user.send" => [
                                                        "key" => "sum-user.send",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "mailList",
                                                    ],
                                                    "sum-user.receive" => [
                                                        "key" => "sum-user.receive",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "mailList",
                                                    ],
                                                    "sum-user.reply" => [
                                                        "key" => "sum-user.reply",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "mailList",
                                                    ],
                                                    "percent-user.reply_rate" => [
                                                        "key" => "percent-user.reply_rate",
                                                        "value" => 0,
                                                        "method" => "percent",
                                                    ],

                                                    "sum-user.reply_to" => [
                                                        "key" => "sum-user.reply_to",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "mailList",
                                                    ],
                                                    "percent-user.open_rate" => [
                                                        "key" => "percent-user.open_rate",
                                                        "value" => 0,
                                                        "method" => "percent",
                                                    ],

                                                    "percent-user.reply_to_rate" => [
                                                        "key" => "percent-user.reply_to_rate",
                                                        "value" => 0,
                                                        "method" => "percent",
                                                    ],
                                                ],
                                            ]
                                        ],

                                        [
                                            [
                                                "key" => "user.user_id",
                                                "type" => "field",
                                                "label" => \Yii::t('report','员工B'),
                                                "value" => "1",
                                                "summaries" => [
                                                    "sum-user.send" => [
                                                        "key" => "sum-user.send",
                                                        "value" => 53,
                                                        "method" => "sum",
                                                        "refer_list" => "mailList",
                                                    ],
                                                    "sum-user.receive" => [
                                                        "key" => "sum-user.receive",
                                                        "value" => 328,
                                                        "method" => "sum",
                                                        "refer_list" => "mailList",
                                                    ],
                                                    "sum-user.reply_to" => [
                                                        "key" => "sum-user.reply_to",
                                                        "value" => 26,
                                                        "method" => "percent",
                                                        "refer_list" => "mailList",
                                                    ],

                                                    "percent-user.reply_to_rate" => [
                                                        "key" => "percent-user.reply_to_rate",
                                                        "value" => 7.93,
                                                        "method" => "percent",
                                                    ],

                                                    "sum-user.reply" => [
                                                        "key" => "sum-user.reply",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "mailList",
                                                    ],

                                                    "percent-user.reply_rate" => [
                                                        "key" => "percent-user.reply_rate",
                                                        "value" => 0,
                                                        "method" => "percent",
                                                    ],
                                                    "percent-user.open_rate" => [
                                                        "key" => "percent-user.open_rate",
                                                        "value" => 100,
                                                        "method" => "percent",
                                                    ],
                                                ],
                                            ]
                                        ],
                                        [
                                            [
                                                "key" => "user.user_id",
                                                "type" => "field",
                                                "label" => \Yii::t('report','员工A'),
                                                "value" => "2",
                                                "summaries" => [
                                                    "sum-user.send" => [
                                                        "key" => "sum-user.send",
                                                        "value" => 259,
                                                        "method" => "sum",
                                                        "refer_list" => "mailList",
                                                    ],
                                                    "sum-user.receive" => [
                                                        "key" => "sum-user.receive",
                                                        "value" => 488,
                                                        "method" => "sum",
                                                        "refer_list" => "mailList",
                                                    ],
                                                    "sum-user.reply_to" => [
                                                        "key" => "sum-user.reply_to",
                                                        "value" => 27,
                                                        "method" => "percent",
                                                        "refer_list" => "mailList",
                                                    ],

                                                    "percent-user.reply_to_rate" => [
                                                        "key" => "percent-user.reply_to_rate",
                                                        "value" => 5.53,
                                                        "method" => "percent",
                                                    ],

                                                    "sum-user.reply" => [
                                                        "key" => "sum-user.reply",
                                                        "value" => 22,
                                                        "method" => "sum",
                                                        "refer_list" => "mailList",
                                                    ],

                                                    "percent-user.reply_rate" => [
                                                        "key" => "percent-user.reply_rate",
                                                        "value" => 11.39,
                                                        "method" => "percent",
                                                    ],
                                                    "percent-user.open_rate" => [
                                                        "key" => "percent-user.open_rate",
                                                        "value" => 58.46,
                                                        "method" => "percent",
                                                    ],
                                                ],
                                            ]
                                        ],
                                    ],
                                    "total" => [
                                        'sum-user.send' => [
                                            'key' => 'sum-user.send',
                                            'value' => 312,
                                            'method' => 'sum',
                                        ],
                                        'sum-user.receive' => [
                                            'key' => 'sum-user.receive',
                                            'value' => 816,
                                            'method' => 'sum',
                                        ],
                                        'sum-user.reply_to' => [
                                            'key' => 'sum-user.reply_to',
                                            'value' => 53,
                                            'method' => 'sum',
                                        ],
                                        'percent-user.reply_to_rate' => [
                                            'key' => 'percent-user.reply_to_rate',
                                            'value' => 6.5,
                                            'method' => 'percent',
                                        ],
                                        'sum-user.reply' => [
                                            'key' => 'sum-user.reply',
                                            'value' => 22,
                                            'method' => 'sum',
                                        ],
                                        'percent-user.reply_rate' => [
                                            'key' => 'percent-user.reply_rate',
                                            'value' => 11.39,
                                            'method' => 'percent',
                                        ],
                                        'percent-user.open_rate' => [
                                            'key' => 'percent-user.open_rate',
                                            'value' => 65.2,
                                            'method' => 'percent',
                                        ],
                                    ],
                                    "count" => 0,
                                    "config" => [
                                        "field" => [
                                            [
                                                "key" => "user.user_id",
                                                "type" => "field",
                                                "field_type" => "",
                                                "label" => \Yii::t('report', "员工(员工)"),
                                                "tip" => ""
                                            ]
                                        ],
                                        "group" => [
                                            [
                                                "key" => "user.user_id",
                                                "type" => "row",
                                                "field_type" => "",
                                                "label" => "user.user_id",
                                                "order" => "",
                                                "extra" => []
                                            ]
                                        ],
                                        "order" => [
//                                            [
//                                                "key" => "user.user_id",
//                                                "field" => null,
//                                                "type" => "asc",
//                                                "label" => "user.user_id"
//                                            ]
                                        ],
                                        "summaries" => [
                                            [
                                                "key" => "sum-user.send",
                                                "type" => "sum",
                                                "field_type" => "",
                                                "refer_list" => "mailList",
                                                "label" => \Yii::t('report', "客户邮件发送数"),
                                                "tip" => \Yii::t('report', "客户邮件：私海、公海、同事客户;当为群发邮件时，其中收件人含有客户的，则判定为客户邮件;基于现有邮件列表数据统计"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "sum-user.receive",
                                                "type" => "sum",
                                                "field_type" => "",
                                                "refer_list" => "mailList",
                                                "label" => \Yii::t('report', "客户邮件收到数"),
                                                "tip" => \Yii::t('report', "客户邮件：私海、公海、同事客户;当为群发邮件时，其中收件人含有客户的，则判定为客户邮件;基于现有邮件列表数据统计"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "sum-user.reply_to",
                                                "type" => "sum",
                                                "field_type" => "",
                                                "refer_list" => "mailList",
                                                "label" => \Yii::t('report', "我方回复数"),
                                                "tip" => "",
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "percent-user.reply_to_rate",
                                                "type" => "percent",
                                                "field_type" => "",
                                                "refer_list" => "",
                                                "label" => \Yii::t('report', "我方回复率"),
                                                "tip" => "",
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "sum-user.reply",
                                                "type" => "sum",
                                                "field_type" => "",
                                                "refer_list" => "mailList",
                                                "label" => \Yii::t('report', "对方回复数"),
                                                "tip" => "",
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "percent-user.reply_rate",
                                                "type" => "percent",
                                                "field_type" => "",
                                                "refer_list" => "",
                                                "label" => \Yii::t('report', "对方回复率"),
                                                "tip" => \Yii::t('report', "回复率的计算基数是已被追踪的邮件"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "percent-user.open_rate",
                                                "type" => "percent",
                                                "field_type" => "",
                                                "refer_list" => "",
                                                "label" => \Yii::t('report', "对方打开率"),
                                                "tip" => \Yii::t('report', "打开率的计算基数是已被追踪的邮件"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ]
                                        ],
                                        "total" => [
                                            "subtotal" => [
                                                "switch" => true
                                            ],
                                            "total" => [
                                                "switch" => true
                                            ],
                                            "hide_zero" => [
                                                "switch" => true,
                                                "default" => true
                                            ],
                                            "detail" => [
                                                "switch" => true,
                                                "default" => false
                                            ]
                                        ],
                                        "query" => [
                                            [
                                                "field" => "common.visible",
                                                "comment" => "查看范围",
                                                "type" => "select_visible_user_id",
                                                "multiple" => 1,
                                                "value" => [],
                                                "field_type" => 7,
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "common.date",
                                                "comment" => "时间",
                                                "type" => "date",
                                                "value" => [
                                                    "start" => "2024-05-01",
                                                    "end" => "2024-05-27"
                                                ],
                                                "multiple" => 0,
                                                "period" => "m",
                                                "default" => 1,
                                                "continuous" => 1,
                                                "maxDateRange" => 15724800,
                                                "field_type" => 4,
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "mail.spe_include_folder_id",
                                                "comment" => "包含",
                                                "type" => "select_mail_include_folder",
                                                "value" => [
                                                    5,
                                                    6,
                                                    9
                                                ],
                                                "multiple" => 1,
                                                "field_type" => 7,
                                                "label" => ""
                                            ]
                                        ],
                                        "detail" => []
                                    ],
                                    "chart" => [],
                                    "client_currency" => "",
                                    "subscribe_flag" => false,
                                    "language" => "zh-CN",
                                    "can_setting_filter_field" => false
                                ],
                                'data' => [
                                    'content' => [
                                        [
                                            "user.user_id" => \Yii::t('report','员工C'),
                                            "sum-user.send" => 0,
                                            "sum-user.receive" => 0,
                                            "sum-user.reply" => 0,
                                            "percent-user.reply_rate" => 0,
                                            "sum-user.reply_to" => 0,
                                            "percent-user.open_rate" => 0,
                                            "percent-user.reply_to_rate" => 0
                                        ],

                                        [
                                            "user.user_id" => \Yii::t('report','员工B'),
                                            "sum-user.send" => 53,
                                            "sum-user.receive" => 328,
                                            "sum-user.reply_to" => 26,
                                            "percent-user.reply_to_rate" => 7.93,
                                            "sum-user.reply" => 0,
                                            "percent-user.reply_rate" => 0,
                                            "percent-user.open_rate" => 100,
                                        ],
                                        [
                                            "user.user_id" => \Yii::t('report','员工A'),
                                            "sum-user.send" => 259,
                                            "sum-user.receive" => 488,
                                            "sum-user.reply_to" => 27,
                                            "percent-user.reply_to_rate" => 5.53,
                                            "sum-user.reply" => 22,
                                            "percent-user.reply_rate" => 11.39,
                                            "percent-user.open_rate" => 58.46,
                                        ]
                                    ],
                                    'config' => [
                                        "XAxis" => [
                                            [
                                                "name" => \Yii::t('report','员工(员工)'),
                                                "field" => "user.user_id"
                                            ]
                                        ],
                                        "YAxis" => [
                                            [
                                                "name" => \Yii::t('report','客户邮件发送数'),
                                                "field" => "sum-user.send"
                                            ],
                                            [
                                                "name" => \Yii::t('report','客户邮件收到数'),
                                                "field" => "sum-user.receive"
                                            ],
                                            [
                                                "name" => \Yii::t('report','我方回复数'),
                                                "field" => "sum-user.reply_to"
                                            ],
                                            [
                                                "name" => \Yii::t('report','我方回复率'),
                                                "field" => "percent-user.reply_to_rate",
                                                "unit" => "percent"
                                            ],
                                            [
                                                "name" => \Yii::t('report','对方回复数'),
                                                "field" => "sum-user.reply"
                                            ],
                                            [
                                                "name" => \Yii::t('report','对方回复率'),
                                                "field" => "percent-user.reply_rate",
                                                "unit" => "percent"
                                            ],
                                            [
                                                "name" => \Yii::t('report','对方打开率'),
                                                "field" => "percent-user.open_rate",
                                                "unit" => "percent"
                                            ]
                                        ],
                                        "charType" => [
                                            [
                                                "label" => "表格",
                                                "value" => "table"
                                            ],
                                            [
                                                "label" => "条形图",
                                                "value" => "horizontal-bar"
                                            ],
                                            [
                                                "label" => "折线图",
                                                "value" => "line"
                                            ],
                                            [
                                                "label" => "柱状图",
                                                "value" => "bar"
                                            ]
                                        ],
                                        "chatFlag" => true
                                    ],
                                    'title' => [
                                        'text' => '数据详情'
                                    ],
                                ]
                            ],
                        ],
                    ],
                    [
                        'key' => self::WORKLOAD_STATISTICS,
                        "title" => "",
                        "list" => [
                            [
                                'report_key' => 'xs1',
                                "title" => \Yii::t('ai', "工作量统计"),
                                'sub_key' => 102,
                                'richness' => 1,
                                "conclusion" => \Yii::t('ai', "员工A在新建客户数、新建跟进、新建商机数、新建订单数、动态数、邮件发送数和营销发送数方面的表现较为突出，显示了较高的工作活跃度和成果。员工B在登录天数上表现优秀，反映了较强的工作参与度。而员工C的各项指标相对较低，可能需要进一步的支持和培训以提升工作效率。"),
                                'data_type' => 1,
                                "report_detail_data" => [
                                    'key' => 'xs1',
                                    'title' => '工作量统计',
                                    'name' => '工作量统计',
                                    'desc' => '员工工作量横向对比，帮您了解员工总体工作情况  数据范围：你有权限查看的人员的工作量',
                                    'type' => 'group',
                                    'relevance' => [],
                                    'report_object_name' => '',
                                    'permission' => [
                                        'crm.quotation.view',
                                        'crm.order.view',
                                        'crm.opportunity.view',
                                        'crm.company.private.view',
                                        'crm.mail.view',
                                        'crm.edm.view',
                                    ],
                                    'data' => [
                                        [
                                            [
                                                "key" => 'user.user_id',
                                                "type" => 'type',
                                                "label" => \Yii::t('report','员工C'),
                                                "value" => 1,
                                                "summaries" => [
                                                    'sum-user.lead_add_count' => [
                                                        'key' => 'sum-user.company_add_count',
                                                        'value' => 14,
                                                        'method' => 'sum',
                                                        'refer_list' => 'companyList',
                                                    ],
                                                    'sum-user.company_add_count' => [
                                                        'key' => 'sum-user.company_add_count',
                                                        'value' => 0,
                                                        'method' => 'sum',
                                                        'refer_list' => 'companyList',
                                                    ],
                                                    'sum-user.customer_remark_count' => [
                                                        'key' => 'sum-user.customer_remark_count',
                                                        'value' => 0,
                                                        'method' => 'sum',
                                                        'refer_list' => 'followupList',
                                                    ],
                                                    'sum-user.opportunity_add_count' => [
                                                        'key' => 'sum-user.opportunity_add_count',
                                                        'value' => 0,
                                                        'method' => 'sum',
                                                        'refer_list' => 'opportunityList',
                                                    ],
                                                    'sum-user.order_add_count' => [
                                                        'key' => 'sum-user.order_add_count',
                                                        'value' => 0,
                                                        'method' => 'sum',
                                                        'refer_list' => 'orderList',
                                                    ],
                                                    'sum-user.dynamic_count' => [
                                                        'key' => 'sum-user.dynamic_count',
                                                        'value' => 25,
                                                        'method' => 'sum'
                                                    ],
                                                    'sum-user.mail_send_count' => [
                                                        'key' => 'sum-user.mail_send_count',
                                                        'value' => 0,
                                                        'method' => 'sum',
                                                        'refer_list' => 'mailList',
                                                    ],
                                                    'sum-user.edm_send_count' => [
                                                        'key' => 'sum-user.edm_send_count',
                                                        'value' => 0,
                                                        'method' => 'sum',
                                                        'refer_list' => 'edmList'
                                                    ],
                                                    'sum-user.edm_finish_count' => [
                                                        'key' => 'sum-user.edm_finish_count',
                                                        'value' => 0,
                                                        'method' => 'sum'
                                                    ],
                                                    'sum-user.login_count' => [
                                                        'key' => 'sum-user.login_count',
                                                        'value' => 94,
                                                        'method' => 'sum'
                                                    ]
                                                ]
                                            ]
                                        ],
                                        [
                                            [
                                                "key" => 'user.user_id',
                                                "type" => 'type',
                                                "label" => \Yii::t('report','员工A'),
                                                "value" => 2,
                                                "summaries" => [
                                                    'sum-user.lead_add_count' => [
                                                        'key' => 'sum-user.company_add_count',
                                                        'value' => 763,
                                                        'method' => 'sum',
                                                        'refer_list' => 'companyList',
                                                    ],
                                                    'sum-user.company_add_count' => [
                                                        'key' => 'sum-user.company_add_count',
                                                        'value' => 477,
                                                        'method' => 'sum',
                                                        'refer_list' => 'companyList',
                                                    ],
                                                    'sum-user.customer_remark_count' => [
                                                        'key' => 'sum-user.customer_remark_count',
                                                        'value' => 10392,
                                                        'method' => 'sum',
                                                        'refer_list' => 'followupList',
                                                    ],
                                                    'sum-user.opportunity_add_count' => [
                                                        'key' => 'sum-user.opportunity_add_count',
                                                        'value' => 127,
                                                        'method' => 'sum',
                                                        'refer_list' => 'opportunityList',
                                                    ],
                                                    'sum-user.order_add_count' => [
                                                        'key' => 'sum-user.order_add_count',
                                                        'value' => 47,
                                                        'method' => 'sum',
                                                        'refer_list' => 'orderList',
                                                    ],
                                                    'sum-user.dynamic_count' => [
                                                        'key' => 'sum-user.dynamic_count',
                                                        'value' => 29196,
                                                        'method' => 'sum'
                                                    ],
                                                    'sum-user.mail_send_count' => [
                                                        'key' => 'sum-user.mail_send_count',
                                                        'value' => 309,
                                                        'method' => 'sum',
                                                        'refer_list' => 'mailList',
                                                    ],
                                                    'sum-user.edm_send_count' => [
                                                        'key' => 'sum-user.edm_send_count',
                                                        'value' => 14583,
                                                        'method' => 'sum',
                                                        'refer_list' => 'edmList'
                                                    ],
                                                    'sum-user.edm_finish_count' => [
                                                        'key' => 'sum-user.edm_finish_count',
                                                        'value' => 74,
                                                        'method' => 'sum'
                                                    ],
                                                    'sum-user.login_count' => [
                                                        'key' => 'sum-user.login_count',
                                                        'value' => 103,
                                                        'method' => 'sum'
                                                    ]
                                                ]
                                            ]
                                        ],
                                        [
                                            [
                                                "key" => 'user.user_id',
                                                "type" => 'type',
                                                "label" => \Yii::t('report','员工B'),
                                                "value" => 3,
                                                "summaries" => [
                                                    'sum-user.lead_add_count' => [
                                                        'key' => 'sum-user.company_add_count',
                                                        'value' => 972,
                                                        'method' => 'sum',
                                                        'refer_list' => 'companyList',
                                                    ],
                                                    'sum-user.company_add_count' => [
                                                        'key' => 'sum-user.company_add_count',
                                                        'value' => 255,
                                                        'method' => 'sum',
                                                        'refer_list' => 'companyList',
                                                    ],
                                                    'sum-user.customer_remark_count' => [
                                                        'key' => 'sum-user.customer_remark_count',
                                                        'value' => 117,
                                                        'method' => 'sum',
                                                        'refer_list' => 'followupList',
                                                    ],
                                                    'sum-user.opportunity_add_count' => [
                                                        'key' => 'sum-user.opportunity_add_count',
                                                        'value' => 58,
                                                        'method' => 'sum',
                                                        'refer_list' => 'opportunityList',
                                                    ],
                                                    'sum-user.order_add_count' => [
                                                        'key' => 'sum-user.order_add_count',
                                                        'value' => 74,
                                                        'method' => 'sum',
                                                        'refer_list' => 'orderList',
                                                    ],
                                                    'sum-user.dynamic_count' => [
                                                        'key' => 'sum-user.dynamic_count',
                                                        'value' => 32350,
                                                        'method' => 'sum'
                                                    ],
                                                    'sum-user.mail_send_count' => [
                                                        'key' => 'sum-user.mail_send_count',
                                                        'value' => 387,
                                                        'method' => 'sum',
                                                        'refer_list' => 'mailList',
                                                    ],
                                                    'sum-user.edm_send_count' => [
                                                        'key' => 'sum-user.edm_send_count',
                                                        'value' => 17313,
                                                        'method' => 'sum',
                                                        'refer_list' => 'edmList'
                                                    ],
                                                    'sum-user.edm_finish_count' => [
                                                        'key' => 'sum-user.edm_finish_count',
                                                        'value' => 47,
                                                        'method' => 'sum'
                                                    ],
                                                    'sum-user.login_count' => [
                                                        'key' => 'sum-user.login_count',
                                                        'value' => 131,
                                                        'method' => 'sum'
                                                    ]
                                                ]
                                            ]
                                        ],
                                    ],
                                    'total' => [
                                        "sum-user.lead_add_count" => [
                                            "key" => "sum-user.lead_add_count",
                                            "value" => 1749,
                                            "method" => "sum"
                                        ],
                                        "sum-user.company_add_count" => [
                                            "key" => "sum-user.company_add_count",
                                            "value" => 732,
                                            "method" => "sum"
                                        ],
                                        "sum-user.customer_remark_count" => [
                                            "key" => "sum-user.customer_remark_count",
                                            "value" => 10509,
                                            "method" => "sum"
                                        ],
                                        "sum-user.opportunity_add_count" => [
                                            "key" => "sum-user.opportunity_add_count",
                                            "value" => 185,
                                            "method" => "sum"
                                        ],
                                        "sum-user.order_add_count" => [
                                            "key" => "sum-user.order_add_count",
                                            "value" => 121,
                                            "method" => "sum"
                                        ],
                                        "sum-user.dynamic_count" => [
                                            "key" => "sum-user.dynamic_count",
                                            "value" => 61571,
                                            "method" => "sum"
                                        ],
                                        "sum-user.mail_send_count" => [
                                            "key" => "sum-user.mail_send_count",
                                            "value" => 696,
                                            "method" => "sum"
                                        ],
                                        "sum-user.edm_send_count" => [
                                            "key" => "sum-user.edm_send_count",
                                            "value" => 31896,
                                            "method" => "sum"
                                        ],
                                        "sum-user.edm_finish_count" => [
                                            "key" => "sum-user.edm_finish_count",
                                            "value" => 121,
                                            "method" => "sum"
                                        ],
                                        "sum-user.login_count" => [
                                            "key" => "sum-user.login_count",
                                            "value" => 328,
                                            "method" => "sum"
                                        ]
                                    ],
                                    'count' => 0,
                                    'config' => [
                                        'field' => [
                                            [
                                                'key' => 'user.user_id',
                                                'type' => 'field',
                                                'field_type' => '',
                                                'label' => \Yii::t('report', '员工(员工)'),
                                                'tip' => '',
                                            ],
                                        ],
                                        'group' => [
                                            [
                                                'key' => 'user.user_id',
                                                'type' => 'row',
                                                'field_type' => '',
                                                'label' => 'user.user_id',
                                                'order' => '',
                                                'extra' => [],
                                            ],
                                        ],
                                        'order' => [
//                                            [
//                                                'key' => 'user.user_id',
//                                                'field' => null,
//                                                'type' => 'asc',
//                                                'label' => 'user.user_id',
//                                            ],
                                        ],
                                        'summaries' => [
                                            [
                                                'key' => 'sum-user.lead_add_count',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => 'companyList',
                                                'label' => \Yii::t('report', '新建线索数'),
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'sum-user.company_add_count',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => 'companyList',
                                                'label' => \Yii::t('report', '新建客户数'),
                                                'tip' => \Yii::t('report', '不包含已删除客户'),
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'sum-user.customer_remark_count',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => 'followupList',
                                                'label' => \Yii::t('report', '新建跟进'),
                                                'tip' => \Yii::t('report', '包含线索、客户、商机的跟进'),
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'sum-user.opportunity_add_count',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => 'opportunityList',
                                                'label' => \Yii::t('report', '新建商机数'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'sum-user.quotation_add_count',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => 'quotationList',
                                                'label' => \Yii::t('report', '新建报价单数'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'sum-user.order_add_count',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => 'orderList',
                                                'label' => \Yii::t('report', '新建订单数'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'sum-user.dynamic_count',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => '',
                                                'label' => \Yii::t('report', '动态数'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'sum-user.mail_send_count',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => 'mailList',
                                                'label' => \Yii::t('report', '邮件发送数'),
                                                'tip' => \Yii::t('report', '邮件实际发出数量，包含发送成功、已删除的邮件'),
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'sum-user.edm_send_count',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => 'edmList',
                                                'label' => \Yii::t('report', '营销发送数'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'sum-user.edm_finish_count',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => '',
                                                'label' => \Yii::t('report', '营销任务数'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'sum-user.login_count',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => '',
                                                'label' => \Yii::t('report', '登录天数'),
                                                'tip' => \Yii::t('report', '包含移动端、桌面端、web端登录，按照每天进入系统进行天数统计'),
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                        ],
                                        'total' => [
                                            'subtotal' => [
                                                'switch' => true,
                                            ],
                                            'total' => [
                                                'switch' => true,
                                            ],
                                            'hide_zero' => [
                                                'switch' => true,
                                                'default' => true,
                                            ],
                                            'detail' => [
                                                'switch' => true,
                                                'default' => false,
                                            ],
                                        ],
                                        'query' => [
                                            [
                                                'field' => 'common.visible',
                                                'comment' => '查看范围',
                                                'type' => 'select_visible_user_id',
                                                'multiple' => 1,
                                                'value' => [],
                                                'field_type' => 7,
                                                'label' => '',
                                            ],
                                            [
                                                'field' => 'common.date',
                                                'comment' => '时间',
                                                'type' => 'date',
                                                'value' => [
                                                    'start' => '2024-05-01',
                                                    'end' => '2024-05-28',
                                                ],
                                                'multiple' => 0,
                                                'period' => 'm',
                                                'default' => 1,
                                                'continuous' => 1,
                                                'field_type' => 4,
                                                'label' => '',
                                            ],
                                        ],
                                        'detail' => [],
                                    ],
                                    'client_currency' => '',
                                    'subscribe_flag' => false,
                                    'language' => 'zh-CN',
                                    'can_setting_filter_field' => false,
                                ],
                                'data' => [
                                    'content' => [
                                        [
                                            "user.user_id" => \Yii::t('report','员工C'),
                                            "sum-user.lead_add_count" => 14,
                                            "sum-user.company_add_count" => 0,
                                            "sum-user.customer_remark_count" => 0,
                                            "sum-user.opportunity_add_count" => 0,
                                            "sum-user.order_add_count" => 0,
                                            "sum-user.dynamic_count" => 25,
                                            "sum-user.mail_send_count" => 0,
                                            "sum-user.edm_send_count" => 0,
                                            "sum-user.edm_finish_count" => 0,
                                            "sum-user.login_count" => 94,
                                        ],
                                        [
                                            "user.user_id" => \Yii::t('report','员工A'),
                                            "sum-user.lead_add_count" => 763,
                                            "sum-user.company_add_count" => 477,
                                            "sum-user.customer_remark_count" => 10392,
                                            "sum-user.opportunity_add_count" => 127,
                                            "sum-user.order_add_count" => 47,
                                            "sum-user.dynamic_count" => 29196,
                                            "sum-user.mail_send_count" => 309,
                                            "sum-user.edm_send_count" => 14583,
                                            "sum-user.edm_finish_count" => 74,
                                            "sum-user.login_count" => 103,
                                        ],
                                        [
                                            "user.user_id" => \Yii::t('report','员工B'),
                                            "sum-user.lead_add_count" => 972,
                                            "sum-user.company_add_count" => 255,
                                            "sum-user.customer_remark_count" => 117,
                                            "sum-user.opportunity_add_count" => 58,
                                            "sum-user.order_add_count" => 74,
                                            "sum-user.dynamic_count" => 32350,
                                            "sum-user.mail_send_count" => 387,
                                            "sum-user.edm_send_count" => 17313,
                                            "sum-user.edm_finish_count" => 47,
                                            "sum-user.login_count" => 131,
                                        ]


                                    ],
                                    'config' => [
                                        "XAxis" => [
                                            [
                                                "name" => \Yii::t('report','员工(员工)'),
                                                "field" => "user.user_id"
                                            ]
                                        ],
                                        "YAxis" => [
                                            [
                                                "name" => \Yii::t('report','新建线索数'),
                                                "field" => "sum-user.lead_add_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','新建客户数'),
                                                "field" => "sum-user.company_add_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','新建跟进'),
                                                "field" => "sum-user.customer_remark_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','新建商机数'),
                                                "field" => "sum-user.opportunity_add_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','新建报价单数'),
                                                "field" => "sum-user.quotation_add_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','新建订单数'),
                                                "field" => "sum-user.order_add_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','动态数'),
                                                "field" => "sum-user.dynamic_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','邮件发送数'),
                                                "field" => "sum-user.mail_send_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','营销发送数'),
                                                "field" => "sum-user.edm_send_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','营销任务数'),
                                                "field" => "sum-user.edm_finish_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','登录天数'),
                                                "field" => "sum-user.login_count"
                                            ]
                                        ],
                                        "charType" => [
                                            [
                                                "label" => "表格",
                                                "value" => "table"
                                            ],
                                            [
                                                "label" => "条形图",
                                                "value" => "horizontal-bar"
                                            ],
                                            [
                                                "label" => "折线图",
                                                "value" => "line"
                                            ],
                                            [
                                                "label" => "柱状图",
                                                "value" => "bar"
                                            ]
                                        ],
                                        "chatFlag" => true
                                    ],
                                    'title' => [
                                        'text' => '数据详情'
                                    ],

                                ],
                            ],
                        ]
                    ],
                    [
                        'key' => self::CUSTOMER_FOLLOW_UP_UPDATES,
                        "title" => "",
                        "list" => [
                            [
                                'report_key' => 'xs12',
                                "title" => \Yii::t('ai', "客户跟进动态"),
                                'sub_key' => 103,
                                'richness' => 1,
                                "conclusion" => \Yii::t('ai', "从提供的数据来看，员工B和员工A在快速记录、邮件、EDM、销售订单和商机方面的表现较为活跃，显示出较好的工作动态。员工C的各项指标相对较低，可能需要进一步的支持和培训以提升工作效率。整体而言，员工的表现有显著的差异，建议对数据进行更深入的分析以了解背后的原因，并采取相应的措施。"),
                                'data_type' => 1,
                                "report_detail_data" => [
                                    "key" => "xs12",
                                    "title" => "动态-客户",
                                    "name" => "动态-客户",
                                    "desc" => "员工动态数据概览，了解员工整体动态；数据范围：你有权限查看的人员的动态数据",
                                    "type" => "group",
                                    "relevance" => [
                                        [
                                            "key" => "xs12",
                                            "name" => "客户"
                                        ],
                                        [
                                            "key" => "xs13",
                                            "name" => "商机"
                                        ]
                                    ],
                                    "report_object_name" => "",
                                    "permission" => [
                                        "crm.company.private.view"
                                    ],
                                    "data" => [
                                        [
                                            [
                                                "key" => "user_trail.user_id",
                                                "type" => "field",
                                                "value" => "1",
                                                "label" => \Yii::t('report','员工C'),
                                                "summaries" => [
                                                    "sum-user_trail.remark_add" => [
                                                        "key" => "sum-user_trail.remark_add",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "followupList",
                                                    ],
                                                    "sum-user_trail.remark_schedule" => [
                                                        "key" => "sum-user_trail.remark_schedule",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "followupList",
                                                    ],
                                                    "sum-user_trail.mail" => [
                                                        "key" => "sum-user_trail.mail",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                    ],
                                                    "sum-user_trail.edm" => [
                                                        "key" => "sum-user_trail.edm",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                    ],
                                                    "sum-user_trail.order" => [
                                                        "key" => "sum-user_trail.order",
                                                        "value" => 1,
                                                        "method" => "sum",
                                                    ],
                                                    "sum-user_trail.opportunity" => [
                                                        "key" => "sum-user_trail.opportunity",
                                                        "value" => 3,
                                                        "method" => "sum",
                                                    ],
                                                ]
                                            ]
                                        ],
                                        [
                                            [
                                                "key" => "user_trail.user_id",
                                                "type" => "field",
                                                "value" => "2",
                                                "label" => \Yii::t('report','员工B'),
                                                "summaries" => [
                                                    "sum-user_trail.remark_add" => [
                                                        "key" => "sum-user_trail.remark_add",
                                                        "value" => 118,
                                                        "method" => "sum",
                                                        "refer_list" => "followupList",
                                                    ],
                                                    "sum-user_trail.remark_schedule" => [
                                                        "key" => "sum-user_trail.remark_schedule",
                                                        "value" => 2,
                                                        "method" => "sum",
                                                        "refer_list" => "followupList",
                                                    ],
                                                    "sum-user_trail.mail" => [
                                                        "key" => "sum-user_trail.mail",
                                                        "value" => 138,
                                                        "method" => "sum",
                                                    ],
                                                    "sum-user_trail.edm" => [
                                                        "key" => "sum-user_trail.edm",
                                                        "value" => 10782,
                                                        "method" => "sum",
                                                    ],
                                                    "sum-user_trail.order" => [
                                                        "key" => "sum-user_trail.order",
                                                        "value" => 219,
                                                        "method" => "sum",
                                                    ],
                                                    "sum-user_trail.opportunity" => [
                                                        "key" => "sum-user_trail.opportunity",
                                                        "value" => 97,
                                                        "method" => "sum",
                                                    ],
                                                ]
                                            ]
                                        ],
                                        [
                                            [
                                                "key" => "user_trail.user_id",
                                                "type" => "field",
                                                "value" => "3",
                                                "label" => \Yii::t('report','员工A'),
                                                "summaries" => [
                                                    "sum-user_trail.remark_add" => [
                                                        "key" => "sum-user_trail.remark_add",
                                                        "value" => 8193,
                                                        "method" => "sum",
                                                        "refer_list" => "followupList",
                                                    ],
                                                    "sum-user_trail.remark_schedule" => [
                                                        "key" => "sum-user_trail.remark_schedule",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "followupList",
                                                    ],
                                                    "sum-user_trail.mail" => [
                                                        "key" => "sum-user_trail.mail",
                                                        "value" => 489,
                                                        "method" => "sum",
                                                    ],
                                                    "sum-user_trail.edm" => [
                                                        "key" => "sum-user_trail.edm",
                                                        "value" => 4779,
                                                        "method" => "sum",
                                                    ],
                                                    "sum-user_trail.order" => [
                                                        "key" => "sum-user_trail.order",
                                                        "value" => 278,
                                                        "method" => "sum",
                                                    ],
                                                    "sum-user_trail.opportunity" => [
                                                        "key" => "sum-user_trail.opportunity",
                                                        "value" => 135,
                                                        "method" => "sum",
                                                    ],
                                                ]
                                            ]
                                        ]
                                    ],
                                    "total" => [
                                        'sum-user_trail.remark_add' => [
                                            'key' => 'sum-user_trail.remark_add',
                                            'value' => 8311,
                                            'method' => 'sum',
                                        ],
                                        'sum-user_trail.remark_schedule' => [
                                            'key' => 'sum-user_trail.remark_schedule',
                                            'value' => 2,
                                            'method' => 'sum',
                                        ],
                                        'sum-user_trail.mail' => [
                                            'key' => 'sum-user_trail.mail',
                                            'value' => 627,
                                            'method' => 'sum',
                                        ],
                                        'sum-user_trail.edm' => [
                                            'key' => 'sum-user_trail.edm',
                                            'value' => 15561,
                                            'method' => 'sum',
                                        ],
                                        'sum-user_trail.order' => [
                                            'key' => 'sum-user_trail.order',
                                            'value' => 498,
                                            'method' => 'sum',
                                        ],
                                        'sum-user_trail.opportunity' => [
                                            'key' => 'sum-user_trail.opportunity',
                                            'value' => 235,
                                            'method' => 'sum',
                                        ],

                                    ],
                                    "count" => 0,
                                    "config" => [
                                        "field" => [
                                            [
                                                "key" => "user_trail.user_id",
                                                "type" => "field",
                                                "field_type" => "",
                                                "label" => \Yii::t('report', "员工(员工动态)"),
                                                "tip" => ""
                                            ]
                                        ],
                                        "group" => [
                                            [
                                                "key" => "user_trail.user_id",
                                                "type" => "row",
                                                "field_type" => "",
                                                "label" => "user_trail.user_id",
                                                "order" => "",
                                                "extra" => []
                                            ]
                                        ],
                                        "order" => [
//                                            [
//                                                "key" => "user_trail.user_id",
//                                                "field" => null,
//                                                "type" => "asc",
//                                                "label" => "user_trail.user_id"
//                                            ]
                                        ],
                                        "summaries" => [
                                            [
                                                'key' => 'sum-user_trail.remark_add',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => 'followupList',
                                                'label' => \Yii::t('report', '快速记录'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0
                                            ],
                                            [
                                                'key' => 'sum-user_trail.remark_schedule',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => 'followupList',
                                                'label' => \Yii::t('report', '日程跟进'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0
                                            ],
                                            [
                                                'key' => 'sum-user_trail.mail',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => '',
                                                'label' => \Yii::t('report', '邮件'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0
                                            ],
                                            [
                                                'key' => 'sum-user_trail.edm',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => '',
                                                'label' => \Yii::t('report','EDM'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0
                                            ],
                                            [
                                                'key' => 'sum-user_trail.order',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => '',
                                                'label' => \Yii::t('report', '销售订单'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0
                                            ],
                                            [
                                                'key' => 'sum-user_trail.opportunity',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => '',
                                                'label' => \Yii::t('report', '商机'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0
                                            ]
                                        ],
                                        "total" => [
                                            "subtotal" => [
                                                "switch" => true
                                            ],
                                            "total" => [
                                                "switch" => true
                                            ],
                                            "hide_zero" => [
                                                "switch" => true,
                                                "default" => true
                                            ],
                                            "detail" => [
                                                "switch" => true,
                                                "default" => false
                                            ]
                                        ],
                                        "query" => [],
                                        "detail" => []
                                    ],
                                    "chart" => [],
                                    "client_currency" => "",
                                    "subscribe_flag" => false,
                                    "language" => "zh-CN",
                                    "can_setting_filter_field" => false
                                ],
                                "data" => [
                                    'content' => [
                                        [
                                            "user_trail.user_id" => \Yii::t('report','员工C'),
                                            "sum-user_trail.remark_add" => 0,
                                            "sum-user_trail.remark_schedule" => 0,
                                            "sum-user_trail.mail" => 0,
                                            "sum-user_trail.edm" => 0,
                                            "sum-user_trail.order" => 1,
                                            "sum-user_trail.opportunity" => 3,
                                        ],
                                        [
                                            "user_trail.user_id" => \Yii::t('report','员工B'),
                                            "sum-user_trail.remark_add" => 118,
                                            "sum-user_trail.remark_schedule" => 2,
                                            "sum-user_trail.mail" => 138,
                                            "sum-user_trail.edm" => 10782,
                                            "sum-user_trail.order" => 219,
                                            "sum-user_trail.opportunity" => 97,
                                        ],
                                        [
                                            "user_trail.user_id" => \Yii::t('report','员工A'),
                                            "sum-user_trail.remark_add" => 8193,
                                            "sum-user_trail.remark_schedule" => 0,
                                            "sum-user_trail.mail" => 489,
                                            "sum-user_trail.edm" => 4779,
                                            "sum-user_trail.order" => 278,
                                            "sum-user_trail.opportunity" => 135,
                                        ]



                                    ],
                                    'config' => [
                                        "XAxis" => [
                                            [
                                                "name" => \Yii::t('report', "员工(员工动态)"),
                                                "field" => "user_trail.user_id"
                                            ]
                                        ],
                                        "YAxis" => [
                                            [
                                                "name" => \Yii::t('report','快速记录'),
                                                "field" => "sum-user_trail.remark_add"
                                            ],
                                            [
                                                "name" => \Yii::t('report','日程跟进'),
                                                "field" => "sum-user_trail.remark_schedule"
                                            ],
                                            [
                                                "name" => \Yii::t('report','邮件'),
                                                "field" => "sum-user_trail.mail"
                                            ],
                                            [
                                                "name" => \Yii::t('report','EDM'),
                                                "field" => "sum-user_trail.edm"
                                            ],
                                            [
                                                "name" => \Yii::t('report','销售订单'),
                                                "field" => "sum-user_trail.order"
                                            ],
                                            [
                                                "name" => \Yii::t('report','商机'),
                                                "field" => "sum-user_trail.opportunity"
                                            ]
                                        ],
                                        "charType" => [
                                            [
                                                "label" => "表格",
                                                "value" => "table"
                                            ],
                                            [
                                                "label" => "条形图",
                                                "value" => "horizontal-bar"
                                            ],
                                            [
                                                "label" => "折线图",
                                                "value" => "line"
                                            ],
                                            [
                                                "label" => "柱状图",
                                                "value" => "bar"
                                            ]
                                        ],
                                        "chatFlag" => true
                                    ],
                                    'title' => [
                                        'text' => '数据详情'
                                    ],

                                ]
                            ],
                        ]
                    ],
                    [
                        'key' => self::CUSTOMER_FOLLOW_UP_TREND,
                        "title" => "",
                        "list" => [
                            [
                                'report_key' => 'khgj3',
                                "title" => \Yii::t('ai', "客户跟进情况"),
                                'sub_key' => 112,
                                'richness' => 1,
                                "conclusion" => \Yii::t('ai', "从提供的数据来看，员工B和员工A在快速记录、邮件、EDM、销售订单和商机方面的表现较为活跃，显示出较好的工作动态。员工C的各项指标相对较低，可能需要进一步的支持和培训以提升工作效率。整体而言，员工的表现有显著的差异，建议对数据进行更深入的分析以了解背后的原因，并采取相应的措施。"),
                                'data_type' => 1,
                                "report_detail_data" => [
                                    "key" => "khgj3",
                                    "title" => "客户跟进趋势",
                                    "name" => "客户跟进趋势",
                                    "desc" => "展示发送聊天消息/发送邮件/发送EDM/写跟进记录等跟进数量，展示历史对客户跟进的动态变化。数据范围：你有权限查看的所有客户。",
                                    "type" => "group",
                                    "relevance" => [
                                        [
                                            "key" => "khgj3",
                                            "name" => "跟进人"
                                        ],
                                        [
                                            "key" => "khgj2",
                                            "name" => "跟进时间"
                                        ]
                                    ],
                                    "report_object_name" => "",
                                    "permission" => [
                                        "crm.company.private.view"
                                    ],
                                    "data" => [
                                        [
                                            [
                                                "key" => "follow.user_id",
                                                "type" => "field",
                                                "value" => "1",
                                                "label" => \Yii::t('report','员工C'),
                                                "summaries" => [
                                                    "sum-send.message_count" => [
                                                        "key" => "sum-send.message_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                    ],
                                                    "sum-send.message_company_count" => [
                                                        "key" => "sum-send.message_company_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "companyList",
                                                    ],
                                                    "sum-send.mail_count" => [
                                                        "key" => "sum-send.mail_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "mailList",
                                                    ],
                                                    "sum-send.mail_company_count" => [
                                                        "key" => "sum-send.mail_company_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "companyList",
                                                    ],
                                                    "sum-send.edm_count" => [
                                                        "key" => "sum-send.edm_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                    ],
                                                    "sum-send.edm_company_count" => [
                                                        "key" => "sum-send.edm_company_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "companyList",
                                                    ],
                                                    "sum-follow.record_count" => [
                                                        "key" => "sum-follow.record_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "followupList",
                                                    ],
                                                    "sum-follow.record_company_count" => [
                                                        "key" => "sum-follow.record_company_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "companyList",
                                                    ],
                                                ]
                                            ]
                                        ],
                                        [
                                            [
                                                "key" => "follow.user_id",
                                                "type" => "field",
                                                "value" => "1",
                                                "label" => \Yii::t('report','员工B'),
                                                "summaries" => [
                                                    "sum-send.message_count" => [
                                                        "key" => "sum-send.message_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                    ],
                                                    "sum-send.message_company_count" => [
                                                        "key" => "sum-send.message_company_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "companyList",
                                                    ],
                                                    "sum-send.mail_count" => [
                                                        "key" => "sum-send.mail_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "mailList",
                                                    ],
                                                    "sum-send.mail_company_count" => [
                                                        "key" => "sum-send.mail_company_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "companyList",
                                                    ],
                                                    "sum-send.edm_count" => [
                                                        "key" => "sum-send.edm_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                    ],
                                                    "sum-send.edm_company_count" => [
                                                        "key" => "sum-send.edm_company_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "companyList",
                                                    ],
                                                    "sum-follow.record_count" => [
                                                        "key" => "sum-follow.record_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "followupList",
                                                    ],
                                                    "sum-follow.record_company_count" => [
                                                        "key" => "sum-follow.record_company_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "companyList",
                                                    ],
                                                ]
                                            ]
                                        ],
                                        [
                                            [
                                                "key" => "follow.user_id",
                                                "type" => "field",
                                                "value" => "1",
                                                "label" => \Yii::t('report','员工A'),
                                                "summaries" => [
                                                    "sum-send.message_count" => [
                                                        "key" => "sum-send.message_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                    ],
                                                    "sum-send.message_company_count" => [
                                                        "key" => "sum-send.message_company_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "companyList",
                                                    ],
                                                    "sum-send.mail_count" => [
                                                        "key" => "sum-send.mail_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "mailList",
                                                    ],
                                                    "sum-send.mail_company_count" => [
                                                        "key" => "sum-send.mail_company_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "companyList",
                                                    ],
                                                    "sum-send.edm_count" => [
                                                        "key" => "sum-send.edm_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                    ],
                                                    "sum-send.edm_company_count" => [
                                                        "key" => "sum-send.edm_company_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "companyList",
                                                    ],
                                                    "sum-follow.record_count" => [
                                                        "key" => "sum-follow.record_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "followupList",
                                                    ],
                                                    "sum-follow.record_company_count" => [
                                                        "key" => "sum-follow.record_company_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "companyList",
                                                    ],
                                                ]
                                            ]
                                        ],


                                    ],
                                    "total" => [
                                        "sum-send.message_count" => [
                                            "key" => "sum-send.message_count",
                                            "value" => 0,
                                            "method" => "sum",
                                        ],
                                        "sum-send.message_company_count" => [
                                            "key" => "sum-send.message_company_count",
                                            "value" => 0,
                                            "method" => "sum",
                                        ],
                                        "sum-send.mail_count" => [
                                            "key" => "sum-send.mail_count",
                                            "value" => 0,
                                            "method" => "sum",
                                        ],
                                        "sum-send.mail_company_count" => [
                                            "key" => "sum-send.mail_company_count",
                                            "value" => 0,
                                            "method" => "sum",
                                        ],
                                        "sum-send.edm_count" => [
                                            "key" => "sum-send.edm_count",
                                            "value" => 0,
                                            "method" => "sum",
                                        ],
                                        "sum-send.edm_company_count" => [
                                            "key" => "sum-send.edm_company_count",
                                            "value" => 0,
                                            "method" => "sum",
                                        ],
                                        "sum-follow.record_count" => [
                                            "key" => "sum-follow.record_count",
                                            "value" => 0,
                                            "method" => "sum",
                                        ],
                                        "sum-follow.record_company_count" => [
                                            "key" => "sum-follow.record_company_count",
                                            "value" => 0,
                                            "method" => "sum",
                                        ]
                                    ],
                                    "count" => 0,
                                    "config" => [
                                        "field" => [
                                            [
                                                "key" => "follow.user_id",
                                                "type" => "field",
                                                "field_type" => "",
                                                "label" => \Yii::t('report', "跟进人"),
                                                "tip" => ""
                                            ]
                                        ],
                                        "group" => [
                                            [
                                                "key" => "follow.user_id",
                                                "type" => "row",
                                                "field_type" => "",
                                                "label" => "follow.user_id",
                                                "order" => "",
                                                "extra" => []
                                            ]
                                        ],
                                        "order" => [
//                                            [
//                                                "key" => "user_trail.user_id",
//                                                "field" => null,
//                                                "type" => "asc",
//                                                "label" => "user_trail.user_id"
//                                            ]
                                        ],
                                        "summaries" => [
                                            [
                                                'key' => 'sum-user_trail.remark_add',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => 'followupList',
                                                'label' => \Yii::t('report', '快速记录'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0
                                            ],
                                            [
                                                'key' => 'sum-user_trail.remark_schedule',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => 'followupList',
                                                'label' => \Yii::t('report', '日程跟进'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0
                                            ],
                                            [
                                                'key' => 'sum-user_trail.mail',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => '',
                                                'label' => \Yii::t('report', '邮件'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0
                                            ],
                                            [
                                                'key' => 'sum-user_trail.edm',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => '',
                                                'label' => \Yii::t('report','EDM'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0
                                            ],
                                            [
                                                'key' => 'sum-user_trail.order',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => '',
                                                'label' => \Yii::t('report', '销售订单'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0
                                            ],
                                            [
                                                'key' => 'sum-user_trail.opportunity',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => '',
                                                'label' => \Yii::t('report', '商机'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0
                                            ]
                                        ],
                                        "total" => [
                                            "subtotal" => [
                                                "switch" => true
                                            ],
                                            "total" => [
                                                "switch" => true
                                            ],
                                            "hide_zero" => [
                                                "switch" => true,
                                                "default" => true
                                            ],
                                            "detail" => [
                                                "switch" => true,
                                                "default" => false
                                            ]
                                        ],
                                        "query" => [],
                                        "detail" => []
                                    ],
                                    "chart" => [],
                                    "client_currency" => "",
                                    "subscribe_flag" => false,
                                    "language" => "zh-CN",
                                    "can_setting_filter_field" => false
                                ],
                                "data" => [
                                    'content' => [
                                        [
                                            "follow.user_id" => \Yii::t('report','员工C'),
                                            "sum-send.message_count" => 0,
                                            "sum-send.message_company_count" => 0,
                                            "sum-send.mail_count" => 0,
                                            "sum-send.mail_company_count" => 0,
                                            "sum-send.edm_count" => 0,
                                            "sum-send.edm_company_count" => 0,
                                            "sum-follow.record_count" => 0,
                                            "sum-follow.record_company_count" => 0,
                                        ],
                                        [
                                            "follow.user_id" => \Yii::t('report','员工B'),
                                            "sum-send.message_count" => 118,
                                            "sum-send.message_company_count" => 2,
                                            "sum-send.mail_count" => 138,
                                            "sum-send.mail_company_count" => 10782,
                                            "sum-send.edm_count" => 219,
                                            "sum-send.edm_company_count" => 97,
                                            "sum-follow.record_count" => 0,
                                            "sum-follow.record_company_count" => 0,
                                        ],
                                        [
                                            "follow.user_id" => \Yii::t('report','员工A'),
                                            "sum-send.message_count" => 8193,
                                            "sum-send.message_company_count" => 0,
                                            "sum-send.mail_count" => 489,
                                            "sum-send.mail_company_count" => 4779,
                                            "sum-send.edm_count" => 278,
                                            "sum-send.edm_company_count" => 135,
                                            "sum-follow.record_count" => 0,
                                            "sum-follow.record_company_count" => 0,
                                        ]
                                    ],
                                    'config' => [
                                        "XAxis" => [
                                            [
                                                "name" => \Yii::t('report', "客户跟进趋势"),
                                                "field" => "follow.user_id"
                                            ]
                                        ],
                                        "YAxis" => [
                                            [
                                                "name" => \Yii::t('report','发送聊天数'),
                                                "field" => "sum-send.message_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','发送聊天消息客户数'),
                                                "field" => "sum-send.message_company_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','发送邮件数'),
                                                "field" => "sum-send.mail_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','发送邮件客户数'),
                                                "field" => "sum-send.mail_company_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','发送EDM数'),
                                                "field" => "sum-send.edm_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','发送EDM客户数'),
                                                "field" => "sum-send.edm_company_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','写跟进记录数'),
                                                "field" => "sum-follow.record_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','写跟进记录客户数'),
                                                "field" => "sum-follow.record_company_count"
                                            ]
                                        ],
                                        "charType" => [
                                            [
                                                "label" => "表格",
                                                "value" => "table"
                                            ],
                                            [
                                                "label" => "条形图",
                                                "value" => "horizontal-bar"
                                            ],
                                            [
                                                "label" => "折线图",
                                                "value" => "line"
                                            ],
                                            [
                                                "label" => "柱状图",
                                                "value" => "bar"
                                            ]
                                        ],
                                        "chatFlag" => true
                                    ],
                                    'title' => [
                                        'text' => '数据详情'
                                    ],

                                ]
                            ],
                        ]
                    ]
                ]

            ],
            [
                "key" => self::FOLLOW_UP_WITH_CUSTOMERS,
                "title" => \Yii::t('ai', "新老客户跟进"),
                'richness' => 1,
                "suggestion" => \Yii::t('ai', "为提升员工A的新客户增长和转化率，建议其学习员工B的客户跟进和成交策略，并针对潜在高消费客户群体制定特定方案。员工B应继续保持其优秀的客户转化和客单价表现，同时可以通过缩短复购周期来进一步提升客户忠诚度。两位员工都应定期分析客户数据，识别复购率低的原因，并制定改进措施，如提供个性化服务或优惠活动，以增强客户粘性。 "),
                "conclusion" => \Yii::t('ai', "员工A在2023年底的新客户增长趋势较为平稳，但2024年初出现下降，整体波动较大；员工B在2023年12月新客户增长显著，但2024年初下降后趋于稳定，且新客户转化率和客单价均高于员工A。员工B的新客户转化率为13.72%，客单价为771.33元，而员工A的转化率为5.66%，客单价为623.31元。两位员工在老客户复购方面表现相近，但员工A的复购周期略短，表明其在促进客户复购方面略显高效。 "),
                'list' => [
                    [
                        'key' => self::TRENDS_IN_THE_NUMBER_OF_NEW_CUSTOMERS,
                        "title" => "",
                        "list" => [
                            [
                                'report_key' => 'khts2',
                                "title" => \Yii::t('ai', "新增客户数变化趋势"),
                                'sub_key' => 104,
                                'richness' => 1,
                                "conclusion" => \Yii::t('ai', "员工A在2023年10月至12月的新客户增长表现稳定，但2024年1月出现了显著下降，随后有所回升但整体波动较大。员工B在2023年12月的新客户增长显著，但在2024年1月和2月有所下降，3月份保持稳定。两位员工的业绩均有波动，但员工A的整体增长趋势较为平稳，而员工B的业绩则显示出更大的波动性。"),
                                'data_type' => 1,
                                "report_detail_data" => [
                                    "key" => "khts2",
                                    "title" => "新增客户数变化趋势",
                                    "name" => "新增客户数变化趋势",
                                    "desc" => "统计客户管理的关键行为数据 你可以查看不同时间区间内某部门/人员的客户管理关键行为  数据范围：你有权限查看的所有的记录流水",
                                    "type" => "group",
                                    "relevance" => [
                                        [
                                            "key" => "khts1",
                                            "name" => "整体",
                                        ],
                                        [
                                            "key" => "khts2",
                                            "name" => \Yii::t('report','创建人'),
                                        ],
                                        [
                                            "key" => "khts3",
                                            "name" => "国家地区",
                                        ],
                                        [
                                            "key" => "khts4",
                                            "name" => "客户来源",
                                        ],
                                        [
                                            "key" => "khts5",
                                            "name" => "来源店铺",
                                        ],
                                    ],
                                    "permission" => [
                                        "crm.company.private.view",
                                    ],

                                    "data" => [
                                        [
                                            [
                                                "key" => "company.date",
                                                "type" => "field",
                                                "value" => "2023/10",
                                                "label" => "2023/10",
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 87,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => 61,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => 198,57,
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => -26,
                                                    ],
                                                ],
                                            ],
                                            [
                                                "key" => "company.create_user_name",
                                                "type" => "field",
                                                "value" => \Yii::t('report','员工A'),
                                                "label" => \Yii::t('report','员工A'),
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 75,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => 54,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => 257.14,
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => -9,
                                                    ],
                                                ],
                                            ],
                                        ],
                                        [
                                            [
                                                "key" => "company.date",
                                                "type" => "field",
                                                "value" => "2023/11",
                                                "label" => "2023/11",
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 100,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => 13,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => 23.17,
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => -69,
                                                    ],
                                                ],
                                            ],
                                            [
                                                "key" => "company.create_user_name",
                                                "type" => "field",
                                                "value" => \Yii::t('report','员工A'),
                                                "label" => \Yii::t('report','员工A'),
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 91,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => 16,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => 21.33,
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => -46,
                                                    ],
                                                ],
                                            ],
                                        ],
                                        [
                                            [
                                                "key" => "company.date",
                                                "type" => "field",
                                                "value" => "2023/12",
                                                "label" => "2023/12",
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 186,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => 86,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => 387.67,
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => 19.5,
                                                    ],
                                                ],
                                            ],
                                            [
                                                "key" => "company.create_user_name",
                                                "type" => "field",
                                                "value" => \Yii::t('report','员工A'),
                                                "label" => \Yii::t('report','员工A'),
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 109,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => 18,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => 19.78,
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => 50,
                                                    ],
                                                ],
                                            ],
                                        ],
                                        [
                                            [
                                                "key" => "company.date",
                                                "type" => "field",
                                                "value" => "2024/01",
                                                "label" => "2024/01",
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 92,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => -94,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => -46.17,
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => -54,
                                                    ],
                                                ],
                                            ],
                                            [
                                                "key" => "company.create_user_name",
                                                "type" => "field",
                                                "value" => \Yii::t('report','员工A'),
                                                "label" => \Yii::t('report','员工A'),
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 31,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => -78,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => -71.56,
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => -81,
                                                    ],
                                                ],
                                            ],
                                        ],
                                        [
                                            [
                                                "key" => "company.date",
                                                "type" => "field",
                                                "value" => "2024/02",
                                                "label" => "2024/02",
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 102,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => 10,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => 36.81,
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => 17.5,
                                                    ],
                                                ],
                                            ],
                                            [
                                                "key" => "company.create_user_name",
                                                "type" => "field",
                                                "value" => \Yii::t('report','员工A'),
                                                "label" => \Yii::t('report','员工A'),
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 67,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => 36,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => 116.13,
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => 31,
                                                    ],
                                                ],
                                            ],
                                        ],
                                        [
                                            [
                                                "key" => "company.date",
                                                "type" => "field",
                                                "value" => "2024/03",
                                                "label" => "2024/03",
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 69,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => -33,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => -24.63,
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => -6,
                                                    ],
                                                ],
                                            ],
                                            [
                                                "key" => "company.create_user_name",
                                                "type" => "field",
                                                "value" => \Yii::t('report','员工A'),
                                                "label" => \Yii::t('report','员工A'),
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 34,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => -33,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => -49.25,
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => -43,
                                                    ],
                                                ],
                                            ],
                                        ],
                                        [
                                            [
                                                "key" => "company.date",
                                                "type" => "field",
                                                "value" => "2023/10",
                                                "label" => "2023/10",
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 12,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => 7,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => 140,
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => -43,
                                                    ],
                                                ],
                                            ],
                                            [
                                                "key" => "company.create_user_name",
                                                "type" => "field",
                                                "value" => \Yii::t('report','员工B'),
                                                "label" => \Yii::t('report','员工B'),
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 12,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => 7,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => 140,
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => -43,
                                                    ],
                                                ],
                                            ],
                                        ],
                                        [
                                            [
                                                "key" => "company.date",
                                                "type" => "field",
                                                "value" => "2023/11",
                                                "label" => "2023/11",
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 9,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => -3,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => -25,
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => -92,
                                                    ],
                                                ],
                                            ],
                                            [
                                                "key" => "company.create_user_name",
                                                "type" => "field",
                                                "value" => \Yii::t('report','员工B'),
                                                "label" => \Yii::t('report','员工B'),
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 9,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => -3,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => -25,
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => -92,
                                                    ],
                                                ],
                                            ],
                                        ],
                                        [
                                            [
                                                "key" => "company.date",
                                                "type" => "field",
                                                "value" => "2023/12",
                                                "label" => "2023/12",
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 77,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => 68,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => 755.56
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => -11,
                                                    ],
                                                ],
                                            ],
                                            [
                                                "key" => "company.create_user_name",
                                                "type" => "field",
                                                "value" => \Yii::t('report','员工B'),
                                                "label" => \Yii::t('report','员工B'),
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 77,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => 68,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => 755.56
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => -11,
                                                    ],
                                                ],
                                            ],
                                        ],
                                        [
                                            [
                                                "key" => "company.date",
                                                "type" => "field",
                                                "value" => "2024/01",
                                                "label" => "2024/01",
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 61,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => -16,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => -20.78,
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => -27,
                                                    ],
                                                ],
                                            ],
                                            [
                                                "key" => "company.create_user_name",
                                                "type" => "field",
                                                "value" => \Yii::t('report','员工B'),
                                                "label" => \Yii::t('report','员工B'),
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 61,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => -16,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => -20.78,
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => -27,
                                                    ],
                                                ],
                                            ],
                                        ],
                                        [
                                            [
                                                "key" => "company.date",
                                                "type" => "field",
                                                "value" => "2024/02",
                                                "label" => "2024/02",
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 35,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => -26,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => -42.62,
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => 4,
                                                    ],
                                                ],
                                            ],
                                            [
                                                "key" => "company.create_user_name",
                                                "type" => "field",
                                                "value" => \Yii::t('report','员工B'),
                                                "label" => \Yii::t('report','员工B'),
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 35,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => -26,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => -42.52,
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => 4,
                                                    ],
                                                ],
                                            ],
                                        ],
                                        [
                                            [
                                                "key" => "company.date",
                                                "type" => "field",
                                                "value" => "2024/03",
                                                "label" => "2024/03",
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 35,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => 0,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => 0,
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => 31,
                                                    ],
                                                ],
                                            ],
                                            [
                                                "key" => "company.create_user_name",
                                                "type" => "field",
                                                "value" => \Yii::t('report','员工B'),
                                                "label" => \Yii::t('report','员工B'),
                                                "summaries" => [
                                                    "mergeCount-company.create_ids" => [
                                                        "key" => "mergeCount-company.create_ids",
                                                        "value" => 35,
                                                    ],
                                                    "monthOnMonthIncreaseNum-company.create_ids" => [
                                                        "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                        "value" => 0,
                                                    ],
                                                    "monthOnMonthIncrease-company.create_ids" => [
                                                        "key" => "monthOnMonthIncrease-company.create_ids",
                                                        "value" => 0,
                                                    ],

                                                    "yearOnYearIncrease-company.create_ids" => [
                                                        "key" => "yearOnYearIncrease-company.create_ids",
                                                        "value" => 31,
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    "total" => [
                                        'mergeCount-company.create_ids' => [
                                            'key' => 'mergeCount-company.create_ids',
                                            'method' => 'mergeCount',
                                            'value' => 636
                                        ]
                                    ],
                                    "count" => 0,
                                    "config" => [
                                        "field" => [
                                            [
                                                "key" => "company.date",
                                                "type" => "field",
                                                "field_type" => "date",
                                                "label" => \Yii::t('report', "日期(客户)"),
                                                "tip" => "",
                                            ],
                                            [
                                                "key" => "company.create_user",
                                                "type" => "field",
                                                "field_type" => "int",
                                                "label" => \Yii::t('report', "创建人(客户)"),
                                                "tip" => "",
                                            ],
                                            [
                                                "key" => "company.create_user_name",
                                                "type" => "field",
                                                "field_type" => "string",
                                                "label" => \Yii::t('report', "创建人"),
                                                "tip" => "",
                                            ],
                                        ],
                                        "group" => [
                                            [
                                                "key" => "company.date",
                                                "type" => "row",
                                                "field_type" => "date",
                                                "label" => "company.date",
                                                "order" => "asc",
                                                "extra" => [],
                                            ],
                                            [
                                                "key" => "company.create_user_name",
                                                "type" => "row",
                                                "field_type" => "",
                                                "label" => "company.create_user_name",
                                                "order" => "",
                                                "extra" => [],
                                            ],
                                        ],
                                        "order" => [],
                                        "summaries" => [
                                            [
                                                "key" => "mergeCount-company.create_ids",
                                                "type" => "mergeCount",
                                                "field_type" => "",
                                                "refer_list" => "companyList",
                                                "label" => \Yii::t('report', "新建客户数"),
                                                "tip" => "",
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0,
                                            ],
                                            [
                                                "key" => "monthOnMonthIncreaseNum-company.create_ids",
                                                "type" => "monthOnMonthIncreaseNum",
                                                "field_type" => "",
                                                "refer_list" => "",
                                                "label" => \Yii::t('report', "环比新增客户数"),
                                                "tip" => "",
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0,
                                            ],
                                            [
                                                "key" => "monthOnMonthIncrease-company.create_ids",
                                                "type" => "monthOnMonthIncrease",
                                                "field_type" => "",
                                                "refer_list" => "",
                                                "label" => \Yii::t('report', "环比增加比例"),
                                                "tip" => "",
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0,
                                            ],
                                            [
                                                "key" => "yearOnYearIncrease-company.create_ids",
                                                "type" => "yearOnYearIncrease",
                                                "field_type" => "",
                                                "refer_list" => "",
                                                "label" => \Yii::t('report', "同比增长比例"),
                                                "tip" => "",
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0,
                                            ],
                                        ],
                                        "total" => [
                                            "subtotal" => [
                                                "switch" => true,
                                            ],
                                            "total" => [
                                                "switch" => true,
                                            ],
                                            "hide_zero" => [
                                                "switch" => true,
                                                "default" => true,
                                            ],
                                            "detail" => [
                                                "switch" => true,
                                                "default" => false,
                                            ],
                                            "showSummaries" => [
                                                "mergeCount-company.create_ids",
                                                "monthOnMonthIncreaseNum-company.create_ids",
                                                "monthOnMonthIncrease-company.create_ids",
                                                "yearOnYearIncreaseNum-company.create_ids",
                                                "yearOnYearIncrease-company.create_ids",
                                            ],
                                        ],
                                        "query" => [
                                            [
                                                "field" => "common.visible",
                                                "comment" => "查看范围",
                                                "type" => "select_visible_user_id",
                                                "multiple" => 1,
                                                "value" => [],
                                                "field_type" => 7,
                                                "label" => "",
                                            ],
                                            [
                                                "field" => "company.select_date",
                                                "comment" => "建档时间",
                                                "type" => "date",
                                                "value" => [
                                                    "start" => "2024-05-01",
                                                    "end" => "2024-05-29",
                                                ],
                                                "multiple" => 0,
                                                "period" => "m",
                                                "default" => 1,
                                                "continuous" => 1,
                                                "field_type" => 4,
                                                "label" => "",
                                            ],
                                            [
                                                "field" => "common.select_cycle",
                                                "comment" => "周期",
                                                "type" => "select",
                                                "options" => [
                                                    [
                                                        "label" => "按天",
                                                        "value" => "day",
                                                    ],
                                                    [
                                                        "label" => "按周",
                                                        "value" => "week",
                                                    ],
                                                    [
                                                        "label" => "按月",
                                                        "value" => "month",
                                                    ],
                                                    [
                                                        "label" => "按季度",
                                                        "value" => "season",
                                                    ],
                                                    [
                                                        "label" => "按半年",
                                                        "value" => "half_a_year",
                                                    ],
                                                ],
                                                "value" => "day",
                                                "multiple" => 0,
                                                "field_type" => 3,
                                                "label" => "",
                                            ],
                                        ],
                                        "detail" => [],
                                    ],
                                    "chart" => [
                                        "chartList" => [
                                            [
                                                "chartType" => "line",
                                                "group" => [
                                                    "company.date",
                                                ],
                                                "summaries" => [],
                                                "option" => [],
                                                "showChartType" => [
                                                    "line",
                                                    "vertical-bar",
                                                ],
                                            ],
                                        ],
                                    ],
                                    "client_currency" => "",
                                    "subscribe_flag" => false,
                                    "language" => "zh-CN",
                                    "can_setting_filter_field" => true,
                                ],
                                'data' => [
                                    'content' => [
                                        [
                                            "company.create_user_name" => \Yii::t('report','员工A'),
                                            "mergeCount-company.create_ids" => 75,
                                            "monthOnMonthIncreaseNum-company.create_ids" => 54,
                                            "monthOnMonthIncrease-company.create_ids" => 257.14,
                                            "yearOnYearIncrease-company.create_ids" => -9,
                                            "company.date" => '2023/10'
                                        ],
                                        [
                                            "company.create_user_name" => \Yii::t('report','员工B'),
                                            "mergeCount-company.create_ids" => 12,
                                            "monthOnMonthIncreaseNum-company.create_ids" => 7,
                                            "monthOnMonthIncrease-company.create_ids" => 140,
                                            "yearOnYearIncrease-company.create_ids" => -43,
                                            "company.date" => '2023/10'
                                        ],

                                        [
                                            "company.create_user_name" => \Yii::t('report','员工A'),
                                            "mergeCount-company.create_ids" => 91,
                                            "monthOnMonthIncreaseNum-company.create_ids" => 16,
                                            "monthOnMonthIncrease-company.create_ids" => 21.33,
                                            "yearOnYearIncrease-company.create_ids" => -46,
                                            "company.date" => '2023/11'
                                        ],
                                        [
                                            "company.create_user_name" => \Yii::t('report','员工B'),
                                            "mergeCount-company.create_ids" => 9,
                                            "monthOnMonthIncreaseNum-company.create_ids" => -3,
                                            "monthOnMonthIncrease-company.create_ids" => -25,
                                            "yearOnYearIncrease-company.create_ids" => -92,
                                            "company.date" => '2023/11'
                                        ],

                                        [
                                            "company.create_user_name" => \Yii::t('report','员工A'),
                                            "mergeCount-company.create_ids" => 109,
                                            "monthOnMonthIncreaseNum-company.create_ids" => 18,
                                            "monthOnMonthIncrease-company.create_ids" => 19.78,
                                            "yearOnYearIncrease-company.create_ids" => 50,
                                            "company.date" => '2023/12'
                                        ],
                                        [
                                            "company.create_user_name" => \Yii::t('report','员工B'),
                                            "mergeCount-company.create_ids" => 77,
                                            "monthOnMonthIncreaseNum-company.create_ids" => 68,
                                            "monthOnMonthIncrease-company.create_ids" => 755.56,
                                            "yearOnYearIncrease-company.create_ids" => -11,
                                            "company.date" => '2023/12'
                                        ],

                                        [
                                            "company.create_user_name" => \Yii::t('report','员工A'),
                                            "mergeCount-company.create_ids" => 31,
                                            "monthOnMonthIncreaseNum-company.create_ids" => -78,
                                            "monthOnMonthIncrease-company.create_ids" => -71.56,
                                            "yearOnYearIncrease-company.create_ids" => -81,
                                            "company.date" => '2024/01'
                                        ],
                                        [
                                            "company.create_user_name" => \Yii::t('report','员工B'),
                                            "mergeCount-company.create_ids" => 61,
                                            "monthOnMonthIncreaseNum-company.create_ids" => -16,
                                            "monthOnMonthIncrease-company.create_ids" => -20.78,
                                            "yearOnYearIncrease-company.create_ids" => -27,
                                            "company.date" => '2024/01'
                                        ],
                                        [
                                            "company.create_user_name" => \Yii::t('report','员工A'),
                                            "mergeCount-company.create_ids" => 67,
                                            "monthOnMonthIncreaseNum-company.create_ids" => 36,
                                            "monthOnMonthIncrease-company.create_ids" => 116.13,
                                            "yearOnYearIncrease-company.create_ids" => 31,
                                            "company.date" => '2024/02'
                                        ],
                                        [
                                            "company.create_user_name" => \Yii::t('report','员工B'),
                                            "mergeCount-company.create_ids" => 35,
                                            "monthOnMonthIncreaseNum-company.create_ids" => -26,
                                            "monthOnMonthIncrease-company.create_ids" => -42.52,
                                            "yearOnYearIncrease-company.create_ids" => 4,
                                            "company.date" => '2024/02'
                                        ],

                                        [
                                            "company.create_user_name" => \Yii::t('report','员工A'),
                                            "mergeCount-company.create_ids" => 34,
                                            "monthOnMonthIncreaseNum-company.create_ids" => -33,
                                            "monthOnMonthIncrease-company.create_ids" => -49.25,
                                            "yearOnYearIncrease-company.create_ids" => -43,
                                            "company.date" => '2024/03'
                                        ],
                                        [
                                            "company.create_user_name" => \Yii::t('report','员工B'),
                                            "mergeCount-company.create_ids" => 35,
                                            "monthOnMonthIncreaseNum-company.create_ids" => 0,
                                            "monthOnMonthIncrease-company.create_ids" => 0,
                                            "yearOnYearIncrease-company.create_ids" => 31,
                                            "company.date" => '2024/03'
                                        ]
                                    ],
                                    'config' => [
                                        "XAxis" => [
                                            [
                                                "name" => \Yii::t('report', "日期(客户)"),
                                                "field" => "company.date"
                                            ],
                                            [
                                                "name" => \Yii::t('report', "创建人(客户)"),
                                                "field" => "company.create_user"
                                            ],
                                            [
                                                "name" => \Yii::t('report', "创建人"),
                                                "field" => "company.create_user_name"
                                            ]
                                        ],
                                        "YAxis" => [
                                            [
                                                "name" => \Yii::t('report', "新建客户数"),
                                                "field" => "mergeCount-company.create_ids"
                                            ],
                                            [
                                                "name" => \Yii::t('report', "环比新增客户数"),
                                                "field" => "monthOnMonthIncreaseNum-company.create_ids"
                                            ],
                                            [
                                                "name" => \Yii::t('report', "环比增加比例"),
                                                "field" => "monthOnMonthIncrease-company.create_ids",
                                                "unit" => "percent"
                                            ],
                                            [
                                                "name" => \Yii::t('report', "同比增长比例"),
                                                "field" => "yearOnYearIncrease-company.create_ids",
                                                "unit" => "percent"
                                            ]
                                        ],
                                        "charType" => [
                                            [
                                                "label" => "表格",
                                                "value" => "table"
                                            ],
                                            [
                                                "label" => "条形图",
                                                "value" => "horizontal-bar"
                                            ],
                                            [
                                                "label" => "折线图",
                                                "value" => "line"
                                            ],
                                            [
                                                "label" => "柱状图",
                                                "value" => "bar"
                                            ]
                                        ],
                                        "chatFlag" => true
                                    ],
                                    'title' => [
                                        'text' => '数据详情'
                                    ],

                                ]

                            ],
                        ],

                    ],
                    [
                        'key' => self::NEW_CUSTOMER_CONVERSION,
                        "title" => "",
                        "list" => [
                            [
                                'report_key' => 'kh15',
                                "title" => \Yii::t('ai', "新客户转化"),
                                'sub_key' => 105,
                                'richness' => 1,
                                "conclusion" => \Yii::t('ai', "员工B和员工A的新客户转化率分别为13.72%和5.66%，表明员工B在转化新客户方面表现更为出色。同时，员工B的客单价为771.33元，而员工A的客单价为623.31元，说明员工B的平均交易金额也较高。这些数据可能反映了员工B在客户跟进和成交方面的策略更为有效，或者她管理的客户群体消费能力更强。"),
                                'data_type' => 1,
                                "report_detail_data" => [
                                    "key" => "kh15",
                                    "title" => "新客户成交转化情况",
                                    "name" => "新客户成交转化情况",
                                    "desc" => "了解新客户成交转化率、客单价",
                                    "type" => "group",
                                    "relevance" => [
                                        [
                                            "key" => "kh14",
                                            "name" => "时间"
                                        ],
                                        [
                                            "key" => "kh15",
                                            "name" => "跟进人"
                                        ],
                                        [
                                            "key" => "kh16",
                                            "name" => "国家地区"
                                        ],
                                        [
                                            "key" => "kh17",
                                            "name" => "客户来源"
                                        ],
                                        [
                                            "key" => "kh18",
                                            "name" => "来源店铺"
                                        ]
                                    ],
                                    "report_object_name" => "",
                                    "permission" => [
                                        "crm.company.private.view"
                                    ],
                                    "data" => [
                                        [
                                            [
                                                "key" => "company.user_id",
                                                "type" => "field",
                                                "value" => 0,
                                                "label" => \Yii::t('report','员工B'),
                                                "summaries" => [
                                                    "row-company.company_id" => [
                                                        "key" => "row-company.company_id",
                                                        "value" => 255,
                                                        "method" => "row",
                                                    ],
                                                    "row-performance.company_id" => [
                                                        "key" => "row-performance.company_id",
                                                        "value" => 35,
                                                        "method" => "row",
                                                        "refer_list" => "companyList",
                                                    ],
                                                    "sum-performance.indicator_value" => [
                                                        "key" => "sum-performance.indicator_value",
                                                        "value" => 26996.4,
                                                        "method" => "sum"
                                                    ],
                                                    "formula-transform" => [
                                                        "key" => "transform",
                                                        "value" => 13.72
                                                    ],
                                                    "formula-avg_company_amount" => [
                                                        "key" => "avg_company_amount",
                                                        "value" => 771.33
                                                    ]
                                                ]

                                            ]
                                        ],
                                        [
                                            [
                                                "key" => "company.user_id",
                                                "type" => "field",
                                                "value" => 1,
                                                "label" => \Yii::t('report','员工A'),
                                                "summaries" => [
                                                    "row-company.company_id" => [
                                                        "key" => "row-company.company_id",
                                                        "value" => 477,
                                                        "method" => "row",
                                                    ],
                                                    "row-performance.company_id" => [
                                                        "key" => "row-performance.company_id",
                                                        "value" => 27,
                                                        "method" => "row",
                                                        "refer_list" => "companyList",
                                                    ],
                                                    "sum-performance.indicator_value" => [
                                                        "key" => "sum-performance.indicator_value",
                                                        "value" => 16829.4,
                                                        "method" => "sum"
                                                    ],
                                                    "formula-transform" => [
                                                        "key" => "transform",
                                                        "value" => 5.66
                                                    ],
                                                    "formula-avg_company_amount" => [
                                                        "key" => "avg_company_amount",
                                                        "value" => 623.31
                                                    ]
                                                ]

                                            ]
                                        ]
                                    ],
                                    "total" => [
                                        "row-company.company_id" => [
                                            "key" => "row-company.company_id",
                                            "value" => 732,
                                            "method" => "row"
                                        ],
                                        "formula-transform" => [
                                            "key" => "transform",
                                            "value" => 8.47
                                        ],
                                        "formula-avg_company_amount" => [
                                            "key" => "avg_company_amount",
                                            "value" => 706.87
                                        ],
                                        "row-performance.company_id" => [
                                            "key" => "row-performance.company_id",
                                            "value" => 62,
                                            "method" => "row"
                                        ],
                                        "sum-performance.indicator_value" => [
                                            "key" => "sum-performance.indicator_value",
                                            "value" => 43825.8,
                                            "method" => "sum"
                                        ]
                                    ],
                                    "count" => 0,
                                    "config" => [
                                        "field" => [
                                            [
                                                "key" => "company.user_id",
                                                "type" => "field",
                                                "field_type" => "int",
                                                "label" => \Yii::t('report', "跟进人(客户)"),
                                                "tip" => ""
                                            ]
                                        ],
                                        "group" => [
                                            [
                                                "key" => "company.user_id",
                                                "type" => "row",
                                                "field_type" => "",
                                                "label" => "company.user_id",
                                                "order" => "",
                                                "extra" => []
                                            ]
                                        ],
                                        "order" => [],
                                        "summaries" => [
                                            [
                                                "key" => "row-company.company_id",
                                                "type" => "row",
                                                "field_type" => "",
                                                "refer_list" => "companyList",
                                                "label" => \Yii::t('report', "新建客户数"),
                                                "tip" => "",
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "row-performance.company_id",
                                                "type" => "row",
                                                "field_type" => "",
                                                "refer_list" => "companyList",
                                                "label" => \Yii::t('report', "成交订单客户数"),
                                                "tip" => \Yii::t('report', "统计符合目标规则【成交订单金额】的销售订单关联的客户"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "sum-performance.indicator_value",
                                                "type" => "sum",
                                                "field_type" => "currency",
                                                "refer_list" => "",
                                                "label" => \Yii::t('report', "成交订单金额"),
                                                "tip" => \Yii::t('report', "统计符合目标规则【成交订单金额】的销售订单金额"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "formula-transform",
                                                "type" => "percent",
                                                "field_type" => "percent",
                                                "refer_list" => "",
                                                "label" => \Yii::t('report', "转化率"),
                                                "tip" => \Yii::t('report', "成交订单客户数/新建客户数"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "formula-avg_company_amount",
                                                "type" => "formula",
                                                "field_type" => "float",
                                                "refer_list" => "",
                                                "label" => \Yii::t('report', "客单价"),
                                                "tip" => \Yii::t('report', "成交订单金额/成交订单客户数"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ]
                                        ],
                                        "total" => [
                                            "subtotal" => [
                                                "switch" => true
                                            ],
                                            "total" => [
                                                "switch" => true
                                            ],
                                            "hide_zero" => [
                                                "switch" => true,
                                                "default" => true
                                            ],
                                            "detail" => [
                                                "switch" => true,
                                                "default" => false
                                            ]
                                        ],
                                        "query" => [
                                            [
                                                "field" => "common.visible",
                                                "comment" => "查看范围",
                                                "type" => "select_visible_user_id",
                                                "multiple" => 1,
                                                "value" => [],
                                                "field_type" => 7,
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "company.archive_time",
                                                "comment" => "建档时间",
                                                "type" => "date",
                                                "value" => [
                                                    "start" => "2024-05-01",
                                                    "end" => "2024-05-29"
                                                ],
                                                "multiple" => 0,
                                                "period" => "m",
                                                "default" => 1,
                                                "continuous" => 1,
                                                "field_type" => 4,
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "company.ali_store_id",
                                                "comment" => "来源店铺",
                                                "type" => 7,
                                                "value" => "",
                                                "field_type" => 7,
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "company.trail_status",
                                                "comment" => "客户阶段",
                                                "type" => "select_trail_status",
                                                "value" => [],
                                                "multiple" => 1,
                                                "field_type" => 7,
                                                "options" => [],
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "company.biz_type",
                                                "comment" => "客户类型",
                                                "type" => "select_biz_type",
                                                "value" => "",
                                                "multiple" => 0,
                                                "field_type" => 3,
                                                "options" => [],
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "company.country",
                                                "comment" => "国家地区",
                                                "type" => "select_country",
                                                "value" => [],
                                                "multiple" => 1,
                                                "field_type" => 7,
                                                "options" => [],
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "company.origin_list",
                                                "comment" => "客户来源",
                                                "type" => "select_origin_list",
                                                "value" => [],
                                                "multiple" => 1,
                                                "field_type" => 7,
                                                "options" => [],
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "company.star",
                                                "comment" => "客户星级",
                                                "type" => "select_star",
                                                "value" => [],
                                                "multiple" => 1,
                                                "field_type" => 7,
                                                "options" => [],
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "company.group_id",
                                                "comment" => "客户分组",
                                                "type" => "select_company_group",
                                                "value" => [],
                                                "multiple" => 1,
                                                "field_type" => 7,
                                                "options" => [],
                                                "label" => ""
                                            ]
                                        ],
                                        "detail" => []
                                    ],
                                    "chart" => [
                                        "chartList" => [
                                            [
                                                "chartType" => "horizontal-bar",
                                                "group" => [
                                                    "company.user_id"
                                                ],
                                                "summaries" => [
                                                    "row-company.company_id"
                                                ],
                                                "option" => []
                                            ]
                                        ]
                                    ],
                                    "client_currency" => "",
                                    "subscribe_flag" => false,
                                    "language" => "zh-CN",
                                    "can_setting_filter_field" => true
                                ],
                                "data" => [
                                    'content' => [
                                        [
                                            "company.user_id" => \Yii::t('report','员工B'),
                                            "row-company.company_id" => 255,
                                            "row-performance.company_id" => 35,
                                            "sum-performance.indicator_value" => 26996.4,
                                            "formula-transform" => 13.72,
                                            "formula-avg_company_amount" => 771.33
                                        ],
                                        [
                                            "company.user_id" => \Yii::t('report','员工A'),
                                            "row-company.company_id" => 477,
                                            "row-performance.company_id" => 27,
                                            "sum-performance.indicator_value" => 16829.4,
                                            "formula-transform" => 5.66,
                                            "formula-avg_company_amount" => 623.31
                                        ]

                                    ],
                                    'config' => [
                                        "XAxis" => [
                                            [
                                                "name" => \Yii::t('report', "跟进人(客户)"),
                                                "field" => "company.user_id"
                                            ]
                                        ],
                                        "YAxis" => [
                                            [
                                                "name" => \Yii::t('report', "新建客户数"),
                                                "field" => "row-company.company_id"
                                            ],
                                            [
                                                "name" => \Yii::t('report', "成交订单客户数"),
                                                "field" => "row-performance.company_id"
                                            ],
                                            [
                                                "name" => \Yii::t('report', "成交订单金额"),
                                                "field" => "sum-performance.indicator_value"
                                            ],
                                            [
                                                "name" => \Yii::t('report', "转化率"),
                                                "field" => "formula-transform",
                                                "unit" => "percent"
                                            ],
                                            [
                                                "name" => \Yii::t('report', "客单价"),
                                                "field" => "formula-avg_company_amount"
                                            ]
                                        ],
                                        "charType" => [
                                            [
                                                "label" => "表格",
                                                "value" => "table"
                                            ],
                                            [
                                                "label" => "条形图",
                                                "value" => "horizontal-bar"
                                            ],
                                            [
                                                "label" => "折线图",
                                                "value" => "line"
                                            ],
                                            [
                                                "label" => "柱状图",
                                                "value" => "bar"
                                            ]
                                        ],
                                        "chatFlag" => true
                                    ],
                                    'title' => [
                                        'text' => '数据详情'
                                    ],

                                ]
                            ],
                        ]
                    ],
                    [
                        'key' => self::OLD_CUSTOMERS_REPURCHASE,
                        "title" => "",
                        "list" => [
                            [
                                'report_key' => 'kh20',
                                "title" => \Yii::t('ai', "老客户复购"),
                                'sub_key' => 106,
                                'richness' => 1,
                                "conclusion" => \Yii::t('ai', "根据提供的数据，员工A和员工B的表现都显示出了他们在客户复购方面的能力。员工A的复购客户占比为31.25%，而员工B的为28.95%，这表明他们都能维持一定比例的客户复购。员工A的复购周期稍短，为25.38天，而员工B的为34.27天，这可能意味着员工A在促进客户复购方面略微高效。"),
                                'data_type' => 1,
                                "report_detail_data" => [
                                    "key" => "kh20",
                                    "title" => "客户复购情况（基于销售订单）",
                                    "name" => "客户复购情况（基于销售订单）",
                                    "desc" => "了解不同维度的新成交客户数、复购客户数、复购客户占比、复购周期",
                                    "type" => "group",
                                    "relevance" => [
                                        [
                                            "key" => "kh20",
                                            "name" => "跟进人"
                                        ],
                                        [
                                            "key" => "kh21",
                                            "name" => "国家地区"
                                        ],
                                        [
                                            "key" => "kh22",
                                            "name" => "客户来源"
                                        ],
                                        [
                                            "key" => "kh23",
                                            "name" => "来源店铺"
                                        ]
                                    ],
                                    "report_object_name" => "",
                                    "permission" => [
                                        "crm.company.private.view"
                                    ],
                                    "data" => [
                                        [
                                            [
                                                "key" => "company.user_id",
                                                "type" => "field",
                                                "value" => 1,
                                                "label" => \Yii::t('report','员工A'),
                                                "summaries" => [
                                                    "row-company.new_customers_list" => [
                                                        "key" => "row-company.new_customers_list",
                                                        "value" => 22,
                                                        "method" => "row",
                                                        "refer_list" => "companyList",
                                                    ],
                                                    "row-company.repeat_customers_list" => [
                                                        "key" => "row-company.repeat_customers_list",
                                                        "value" => 10,
                                                        "method" => "row",
                                                        "refer_list" => "companyList",
                                                    ],
                                                    "formula-company.avg_repurchase_cycle" => [
                                                        "key" => "company.avg_repurchase_cycle",
                                                        "value" => 25.38
                                                    ],
                                                    "formula-company.repeat_customer_rate" => [
                                                        "key" => "company.repeat_customer_rate",
                                                        "value" => 30
                                                    ]
                                                ]
                                            ]
                                        ],
                                        [
                                            [
                                                "key" => "company.user_id",
                                                "type" => "field",
                                                "value" => 2,
                                                "label" => \Yii::t('report','员工B'),
                                                "summaries" => [
                                                    "row-company.new_customers_list" => [
                                                        "key" => "row-company.new_customers_list",
                                                        "value" => 27,
                                                        "method" => "row",
                                                        "refer_list" => "companyList",
                                                    ],
                                                    "row-company.repeat_customers_list" => [
                                                        "key" => "row-company.repeat_customers_list",
                                                        "value" => 11,
                                                        "method" => "row",
                                                        "refer_list" => "companyList",
                                                    ],
                                                    "formula-company.avg_repurchase_cycle" => [
                                                        "key" => "company.avg_repurchase_cycle",
                                                        "value" => 34.27
                                                    ],
                                                    "formula-company.repeat_customer_rate" => [
                                                        "key" => "company.repeat_customer_rate",
                                                        "value" => 28.95
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ],
                                    "total" => [
                                        "row-company.new_customers_list" => [
                                            "key" => "row-company.new_customers_list",
                                            "value" => 49,
                                            "method" => "row"
                                        ],
                                        "formula-company.avg_repurchase_cycle" => [
                                            "key" => "company.avg_repurchase_cycle",
                                            "value" => 29.83
                                        ],
                                        "formula-company.repeat_customer_rate" => [
                                            "key" => "company.repeat_customer_rate",
                                            "value" => 30.1
                                        ],
                                        "row-company.repeat_customers_list" => [
                                            "key" => "row-company.repeat_customers_list",
                                            "value" => 21,
                                            "method" => "row"
                                        ],
                                    ],
                                    "count" => 0,
                                    "config" => [
                                        "field" => [
                                            [
                                                "key" => "company.user_id",
                                                "type" => "field",
                                                "field_type" => "int",
                                                "label" => \Yii::t('report', "跟进人(客户)"),
                                                "tip" => ""
                                            ]
                                        ],
                                        "group" => [
                                            [
                                                "key" => "company.user_id",
                                                "type" => "row",
                                                "field_type" => "",
                                                "label" => "company.user_id",
                                                "order" => "",
                                                "extra" => []
                                            ]
                                        ],
                                        "order" => [],
                                        "summaries" => [
                                            [
                                                "key" => "row-company.new_customers_list",
                                                "type" => "row",
                                                "field_type" => "",
                                                "refer_list" => "companyList",
                                                "label" => \Yii::t('report', "新成交客户数"),
                                                "tip" => \Yii::t('report', "初次成交客户数：成交订单数=1的客户，仅统计符合目标规则【成交订单金额】的销售订单"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "row-company.repeat_customers_list",
                                                "type" => "row",
                                                "field_type" => "",
                                                "refer_list" => "companyList",
                                                "label" => \Yii::t('report', "复购客户数"),
                                                "tip" => \Yii::t('report', "多次成交客户数：成交订单数>=2的客户，仅统计符合目标规则【成交订单金额】的销售订单"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "formula-company.avg_repurchase_cycle",
                                                "type" => "formula",
                                                "field_type" => "float",
                                                "refer_list" => "",
                                                "label" => \Yii::t('report', "复购周期（天）"),
                                                "tip" => \Yii::t('report', "仅统计符合目标规则【成交订单金额】的计入日期平均间隔天数"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "formula-company.repeat_customer_rate",
                                                "type" => "percent",
                                                "field_type" => "percent",
                                                "refer_list" => "",
                                                "label" => \Yii::t('report', "复购客户占比"),
                                                "tip" => \Yii::t('report', "复购率=多次成交客户数/（初次成交客户数+多次成交客户数），仅统计符合目标规则【成交订单金额】的销售订单"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ]
                                        ],
                                        "total" => [
                                            "subtotal" => [
                                                "switch" => true
                                            ],
                                            "total" => [
                                                "switch" => true
                                            ],
                                            "hide_zero" => [
                                                "switch" => true,
                                                "default" => true
                                            ],
                                            "detail" => [
                                                "switch" => true,
                                                "default" => false
                                            ]
                                        ],
                                        "query" => [
                                            [
                                                "field" => "common.visible",
                                                "comment" => "查看范围",
                                                "type" => "select_visible_user_id",
                                                "multiple" => 1,
                                                "value" => [],
                                                "default" => null,
                                                "field_type" => 7,
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "company.archive_time",
                                                "comment" => "建档时间",
                                                "type" => "date",
                                                "value" => [
                                                    "start" => "2024-05-01",
                                                    "end" => "2024-05-29"
                                                ],
                                                "multiple" => 0,
                                                "period" => "m",
                                                "default" => 1,
                                                "continuous" => 1,
                                                "field_type" => 4,
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "company.group_id",
                                                "name" => "客户分组",
                                                "base" => "1",
                                                "field_type" => "3",
                                                "ext_info" => [],
                                                "disable_flag" => "0",
                                                "enable_flag" => "1",
                                                "functional" => [
                                                    "query",
                                                    "bucket",
                                                    "detail",
                                                    "group"
                                                ],
                                                "comment" => "客户分组（客户）",
                                                "type" => "3",
                                                "options" => [],
                                                "label" => "",
                                                "value" => ""
                                            ],
                                            [
                                                "field" => "company.intention_level",
                                                "name" => "采购意向",
                                                "base" => "1",
                                                "field_type" => "3",
                                                "ext_info" => [],
                                                "disable_flag" => "0",
                                                "enable_flag" => "1",
                                                "functional" => [
                                                    "query",
                                                    "bucket",
                                                    "detail",
                                                    "group"
                                                ],
                                                "comment" => "采购意向（客户）",
                                                "type" => "3",
                                                "options" => [],
                                                "label" => "",
                                                "value" => ""
                                            ],
                                            [
                                                "field" => "company.remark",
                                                "name" => "备注",
                                                "base" => "1",
                                                "field_type" => "2",
                                                "ext_info" => [],
                                                "disable_flag" => "0",
                                                "enable_flag" => "1",
                                                "functional" => [
                                                    "query",
                                                    "detail",
                                                    "group"
                                                ],
                                                "comment" => "备注（客户）",
                                                "type" => "2",
                                                "label" => "",
                                                "value" => ""
                                            ],
                                        ],
                                        "detail" => []
                                    ],
                                    "chart" => [
                                        "chartList" => [
                                            [
                                                "chartType" => "vertical-bar",
                                                "group" => [
                                                    "company.user_id"
                                                ],
                                                "summaries" => [
                                                    "row-company.new_customers_list",
                                                    "row-company.repeat_customers_list",
                                                    "formula-company.avg_repurchase_cycle"
                                                ],
                                                "option" => [],
                                                "showChartType" => [
                                                    "vertical-bar",
                                                    "line"
                                                ]
                                            ]
                                        ]
                                    ],
                                    "client_currency" => "",
                                    "subscribe_flag" => false,
                                    "language" => "zh-CN",
                                    "can_setting_filter_field" => true
                                ],
                                'data' => [
                                    'content' => [
                                        [
                                            "company.user_id" => \Yii::t('report','员工A'),
                                            "row-company.new_customers_list" => 22,
                                            "row-company.repeat_customers_list" => 10,
                                            "formula-company.avg_repurchase_cycle" => 25.38,
                                            "formula-company.repeat_customer_rate" => 30
                                        ],
                                        [
                                            "company.user_id" => \Yii::t('report','员工B'),
                                            "row-company.new_customers_list" => 27,
                                            "row-company.repeat_customers_list" => 11,
                                            "formula-company.avg_repurchase_cycle" => 34.27,
                                            "formula-company.repeat_customer_rate" => 28.95
                                        ]


                                    ],
                                    'config' => [
                                        "XAxis" => [
                                            [
                                                "name" => \Yii::t('report', "跟进人(客户)"),
                                                "field" => "company.user_id"
                                            ]
                                        ],
                                        "YAxis" => [
                                            [
                                                "name" => \Yii::t('report', "新成交客户数"),
                                                "field" => "row-company.new_customers_list"
                                            ],
                                            [
                                                "name" => \Yii::t('report', "复购客户数"),
                                                "field" => "row-company.repeat_customers_list"
                                            ],
                                            [
                                                "name" => \Yii::t('report', "复购周期（天）"),
                                                "field" => "formula-company.avg_repurchase_cycle"
                                            ],
                                            [
                                                "name" => \Yii::t('report', "复购客户占比"),
                                                "field" => "formula-company.repeat_customer_rate",
                                                "unit" => "percent"
                                            ]
                                        ],
                                        "charType" => [
                                            [
                                                "label" => "表格",
                                                "value" => "table"
                                            ],
                                            [
                                                "label" => "条形图",
                                                "value" => "horizontal-bar"
                                            ],
                                            [
                                                "label" => "折线图",
                                                "value" => "line"
                                            ],
                                            [
                                                "label" => "柱状图",
                                                "value" => "bar"
                                            ]
                                        ],
                                        "chatFlag" => true
                                    ],
                                    'title' => [
                                        'text' => '数据详情'
                                    ],

                                ]
                            ],
                        ]
                    ],
                ]
            ],
            [
                "key" => self::TASK_COMPLETION_STATUS,
                "title" => \Yii::t('ai', "任务完成情况"),
                'richness' => 1,
                "suggestion" => \Yii::t('ai', "针对员工A，建议制定邮件管理计划，优先处理未回复的18封邮件，并设定目标将邮件及时回复率提升至30%以上。对于员工B，建议进行时间管理培训，以减少邮件平均回复时间，并设定目标将邮件及时回复率提升至20%以上。对于员工C，需要确认客户跟进和邮件回复的数据是否准确，若为新员工，则应提供必要的培训和指导，确保其在所有领域都能达到团队标准。"),
                "conclusion" => \Yii::t('ai', "员工A在客户跟进方面表现出色，及时跟进率高达98.1%，但在邮件回复方面存在未处理的邮件，及时回复率为19.2%。员工B在任务数量上表现强劲，客户跟进率为82.5%，邮件回复率较低，为15.4%，TM回复率为91.70%，但平均回复时间较长。员工C在TM回复方面表现优异，及时回复率100%，平均回复时间为38秒，但在其他方面的数据显示为零，需要进一步调查。 "),
                'list' => [
                    [
                        'key' => self::CUSTOMER_FOLLOW_UP_STATUS,
                        "title" => "",
                        "list" => [
                            [
                                'report_key' => 'task1',
                                'sub_key' => 107,
                                'richness' => 1,
                                "title" => \Yii::t('ai', "客户是否及时跟进"),
                                'data_type' => 1,
                                "conclusion" => \Yii::t('ai', "从数据来看，员工A的客户及时跟进率达到了98.1%，显示出较高的工作效率和客户管理能力。员工B的及时跟进率为82.5%，尽管略低于员工A，但完成的任务数量较多，表现出了较强的工作能力和稳定性。员工C的数据显示为零，可能是新员工或者数据录入有误，需要进一步信息来评估表现。 "),
                                "report_detail_data" => [
                                    "key" => "task1",
                                    "title" => "任务完成情况",
                                    "name" => "任务完成情况",
                                    "desc" => \Yii::t('ai', "考核任务完成情况；数据范围：你有权限查看的人员的任务数据"),
                                    "type" => "group",
                                    "relevance" => [
                                        [
                                            "key" => "task1",
                                            "name" => "跟进客户"
                                        ],
                                        [
                                            "key" => "task2",
                                            "name" => "回复邮件"
                                        ],
                                        [
                                            "key" => "task3",
                                            "name" => "回复TM"
                                        ]
                                    ],
                                    "report_object_name" => "",
                                    "permission" => [],
                                    "data" => [
                                        [
                                            [
                                                "key" => "task.user_id",
                                                "type" => "field",
                                                "value" => \Yii::t('report','员工C'),
                                                "label" => \Yii::t('report','员工C'),
                                                "summaries" => [
                                                    "sum-task.target_company_follow_count" => [
                                                        "key" => "sum-task.target_company_follow_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList",
                                                    ],
                                                    "sum-task.in_time_company_follow_count" => [
                                                        "key" => "sum-task.in_time_company_follow_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ],
                                                    "sum-task.timeout_company_follow_count" => [
                                                        "key" => "sum-task.timeout_company_follow_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList",
                                                    ],
                                                    "sum-task.todo_company_follow_count" => [
                                                        "key" => "sum-task.todo_company_follow_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ],
                                                    "percent-task.in_time_company_follow_count" => [
                                                        "key" => "percent-task.in_time_company_follow_count",
                                                        "value" => 0,
                                                        "method" => "percent"
                                                    ],
                                                    "secondTime-task.average_company_follow_time" => [
                                                        "key" => "secondTime-task.average_company_follow_time",
                                                        "value" => "0",
                                                        "method" => "secondTime"
                                                    ],
                                                    "sum-task.ignore_task_count" => [
                                                        "key" => "sum-task.ignore_task_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList",
                                                    ]
                                                ]
                                            ]
                                        ],
                                        [
                                            [
                                                "key" => "task.user_id",
                                                "type" => "field",
                                                "value" => \Yii::t('report','员工B'),
                                                "label" => \Yii::t('report','员工B'),
                                                "summaries" => [
                                                    "sum-task.target_company_follow_count" => [
                                                        "key" => "sum-task.target_company_follow_count",
                                                        "value" => 542,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList",
                                                    ],
                                                    "sum-task.in_time_company_follow_count" => [
                                                        "key" => "sum-task.in_time_company_follow_count",
                                                        "value" => 447,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ],
                                                    "sum-task.timeout_company_follow_count" => [
                                                        "key" => "sum-task.timeout_company_follow_count",
                                                        "value" => 93,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList",
                                                    ],
                                                    "sum-task.todo_company_follow_count" => [
                                                        "key" => "sum-task.todo_company_follow_count",
                                                        "value" => 2,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ],
                                                    "percent-task.in_time_company_follow_count" => [
                                                        "key" => "percent-task.in_time_company_follow_count",
                                                        "value" => 82.5, // This is a percentage, make sure it is handled correctly in your application
                                                        "method" => "percent"
                                                    ],
                                                    "secondTime-task.average_company_follow_time" => [
                                                        "key" => "secondTime-task.average_company_follow_time",
                                                        "value" => "29天5小时26分钟23秒",
                                                        "method" => "secondTime"
                                                    ],
                                                    "sum-task.ignore_task_count" => [
                                                        "key" => "sum-task.ignore_task_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList",
                                                    ]
                                                ]
                                            ]
                                        ],
                                        [
                                            [
                                                "key" => "task.user_id",
                                                "type" => "field",
                                                "value" => \Yii::t('report','员工A'),
                                                "label" => \Yii::t('report','员工A'),
                                                "summaries" => [
                                                    "sum-task.target_company_follow_count" => [
                                                        "key" => "sum-task.target_company_follow_count",
                                                        "value" => 1299,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList",
                                                    ],
                                                    "sum-task.in_time_company_follow_count" => [
                                                        "key" => "sum-task.in_time_company_follow_count",
                                                        "value" => 1274,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ],
                                                    "sum-task.timeout_company_follow_count" => [
                                                        "key" => "sum-task.timeout_company_follow_count",
                                                        "value" => 2,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList",
                                                    ],
                                                    "sum-task.todo_company_follow_count" => [
                                                        "key" => "sum-task.todo_company_follow_count",
                                                        "value" => 7,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ],
                                                    "percent-task.in_time_company_follow_count" => [
                                                        "key" => "percent-task.in_time_company_follow_count",
                                                        "value" => 98.1,
                                                        "method" => "percent"
                                                    ],
                                                    "secondTime-task.average_company_follow_time" => [
                                                        "key" => "secondTime-task.average_company_follow_time",
                                                        "value" => "12天3小时28分钟36秒",
                                                        "method" => "secondTime"
                                                    ],
                                                    "sum-task.ignore_task_count" => [
                                                        "key" => "sum-task.ignore_task_count",
                                                        "value" => 1,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList",
                                                    ]
                                                ]
                                            ]
                                        ],
                                    ],
                                    "total" => [
                                        "sum-task.target_company_follow_count" => [
                                            "key" => "sum-task.target_company_follow_count",
                                            "value" => 1841,
                                            "method" => "sum"
                                        ],
                                        "sum-task.in_time_company_follow_count" => [
                                            "key" => "sum-task.in_time_company_follow_count",
                                            "value" => 1721,
                                            "method" => "sum"
                                        ],
                                        "sum-task.timeout_company_follow_count" => [
                                            "key" => "sum-task.timeout_company_follow_count",
                                            "value" => 95,
                                            "method" => "sum"
                                        ],
                                        "sum-task.todo_company_follow_count" => [
                                            "key" => "sum-task.todo_company_follow_count",
                                            "value" => 9,
                                            "method" => "sum"
                                        ],
                                        "percent-task.in_time_company_follow_count" => [
                                            "key" => "percent-task.in_time_company_follow_count",
                                            "value" => 93.48,
                                            "method" => "percent"
                                        ],
                                        "secondTime-task.average_company_follow_time" => [
                                            "key" => "secondTime-task.average_company_follow_time",
                                            "value" => "20天4小时23分钟21秒",
                                            "method" => "secondTime"
                                        ],
                                        "sum-task.ignore_task_count" => [
                                            "key" => "sum-task.ignore_task_count",
                                            "value" => 1,
                                            "method" => "sum"
                                        ]
                                    ],
                                    "count" => 0,
                                    "config" => [
                                        "field" => [
                                            [
                                                "key" => "task.user_id",
                                                "type" => "field",
                                                "field_type" => "int",
                                                "label" => \Yii::t('report', "成员(任务)"),
                                                "tip" => ""
                                            ]
                                        ],
                                        "group" => [
                                            [
                                                "key" => "task.user_id",
                                                "type" => "row",
                                                "field_type" => "",
                                                "label" => "task.user_id",
                                                "order" => "",
                                                "extra" => []
                                            ]
                                        ],
                                        "order" => [],
                                        "summaries" => [
                                            [
                                                "key" => "sum-task.target_company_follow_count",
                                                "type" => "sum",
                                                "field_type" => "",
                                                "refer_list" => "taskList",
                                                "label" => \Yii::t('report', "应跟进客户次数"),
                                                "tip" => \Yii::t('report', "「跟进客户」任务类型所触发的任务数"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "sum-task.in_time_company_follow_count",
                                                "type" => "sum",
                                                "field_type" => "",
                                                "refer_list" => "taskList",
                                                "label" => \Yii::t('report', "及时跟进客户次数"),
                                                "tip" => \Yii::t('report', "任务截止时间前完成的任务数"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "sum-task.timeout_company_follow_count",
                                                "type" => "sum",
                                                "field_type" => "",
                                                "refer_list" => "taskList",
                                                "label" => \Yii::t('report', "超时跟进客户次数"),
                                                "tip" => \Yii::t('report', "任务截止时间后完成的任务数"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "sum-task.todo_company_follow_count",
                                                "type" => "sum",
                                                "field_type" => "",
                                                "refer_list" => "taskList",
                                                "label" => \Yii::t('report', "待跟进客户次数"),
                                                "tip" => \Yii::t('report', "暂未完成的任务数"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "percent-task.in_time_company_follow_count",
                                                "type" => "percent",
                                                "field_type" => "",
                                                "refer_list" => "",
                                                "label" => \Yii::t('report', "客户及时跟进率"),
                                                "tip" => \Yii::t('report', "及时跟进客户数÷应跟进客户数"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "secondTime-task.average_company_follow_time",
                                                "type" => "secondTime",
                                                "field_type" => "",
                                                "refer_list" => "",
                                                "label" => \Yii::t('report', "平均跟进客户间隔时间"),
                                                "tip" => \Yii::t('report', "距离上次跟进客户间隔的平均时长"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "sum-task.ignore_task_count",
                                                "type" => "sum",
                                                "field_type" => "",
                                                "refer_list" => "taskList",
                                                "label" => \Yii::t('report', "忽略任务数"),
                                                "tip" => "",
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ]
                                        ],
                                        "total" => [
                                            "subtotal" => [
                                                "switch" => true
                                            ],
                                            "total" => [
                                                "switch" => true
                                            ],
                                            "hide_zero" => [
                                                "switch" => true,
                                                "default" => true
                                            ],
                                            "showSummaries" => [
                                                "sum-task.target_company_follow_count",
                                                "sum-task.in_time_company_follow_count",
                                                "sum-task.timeout_company_follow_count",
                                                "sum-task.todo_company_follow_count",
                                                "percent-task.in_time_company_follow_count",
                                                "secondTime-task.average_company_follow_time",
                                                "sum-task.ignore_task_count"
                                            ]
                                        ],
                                        "query" => [
                                            [
                                                "field" => "common.visible",
                                                "comment" => "查看范围",
                                                "type" => "select_visible_user_id",
                                                "multiple" => 1,
                                                "value" => [],
                                                "default" => null,
                                                "field_type" => 7,
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "common.visible.department_id",
                                                "comment" => "查看部门id",
                                                "type" => "int",
                                                "multiple" => 1,
                                                "value" => [],
                                                "default" => null,
                                                "field_type" => 7,
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "common.task_rule_id",
                                                "comment" => "任务规则",
                                                "multiple" => 1,
                                                "value" => [],
                                                "type" => "",
                                                "field_type" => 7,
                                                "label" => ""
                                            ]
                                        ],
                                        "detail" => []
                                    ],
                                    "chart" => [
                                        "chartList" => [
                                            [
                                                "chartType" => "vertical-bar",
                                                "group" => [
                                                    "task.user_id"
                                                ],
                                                "summaries" => [
                                                    "sum-task.target_company_follow_count",
                                                    "sum-task.in_time_company_follow_count"
                                                ],
                                                "option" => []
                                            ]
                                        ]
                                    ],
                                    "client_currency" => "",
                                    "subscribe_flag" => true,
                                    "language" => "zh-CN",
                                    "can_setting_filter_field" => false
                                ],
                                'data' => [
                                    'content' => [
                                        [
                                            "task.user_id" => \Yii::t('report','员工C'),
                                            "sum-task.target_company_follow_count" => 0,
                                            "sum-task.in_time_company_follow_count" => 0,
                                            "sum-task.timeout_company_follow_count" => 0,
                                            "sum-task.todo_company_follow_count" => 0,
                                            "percent-task.in_time_company_follow_count" => 0,
                                            "secondTime-task.average_company_follow_time" => "0",
                                            "sum-task.ignore_task_count" => 0
                                        ],
                                        [
                                            "task.user_id" => \Yii::t('report','员工B'),
                                            "sum-task.target_company_follow_count" => 542,
                                            "sum-task.in_time_company_follow_count" => 447,
                                            "sum-task.timeout_company_follow_count" => 93,
                                            "sum-task.todo_company_follow_count" => 2,
                                            "percent-task.in_time_company_follow_count" => 82.5,
                                            "secondTime-task.average_company_follow_time" => "29天5小时26分钟23秒",
                                            "sum-task.ignore_task_count" => 0
                                        ],
                                        [
                                            "task.user_id" => \Yii::t('report','员工A'),
                                            "sum-task.target_company_follow_count" => 1299,
                                            "sum-task.in_time_company_follow_count" => 1274,
                                            "sum-task.timeout_company_follow_count" => 2,
                                            "sum-task.todo_company_follow_count" => 7,
                                            "percent-task.in_time_company_follow_count" => 98.1,
                                            "secondTime-task.average_company_follow_time" => "12天3小时28分钟36秒",
                                            "sum-task.ignore_task_count" => 1
                                        ]
                                    ],
                                    'config' => [
                                        "XAxis" => [
                                            [
                                                "name" => \Yii::t('report','成员(任务'),
                                                "field" => "task.user_id"
                                            ]
                                        ],
                                        "YAxis" => [
                                            [
                                                "name" => \Yii::t('report','应跟进客户次数'),
                                                "field" => "sum-task.target_company_follow_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','及时跟进客户次数'),
                                                "field" => "sum-task.in_time_company_follow_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','超时跟进客户次数'),
                                                "field" => "sum-task.timeout_company_follow_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','待跟进客户次数'),
                                                "field" => "sum-task.todo_company_follow_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','客户及时跟进率'),
                                                "field" => "percent-task.in_time_company_follow_count",
                                                "unit" => "percent"
                                            ],
                                            [
                                                "name" => \Yii::t('report','平均跟进客户间隔时间'),
                                                "field" => "secondTime-task.average_company_follow_time"
                                            ],
                                            [
                                                "name" => \Yii::t('report','忽略任务数'),
                                                "field" => "sum-task.ignore_task_count"
                                            ]
                                        ],
                                        "charType" => [
                                            [
                                                "label" => "表格",
                                                "value" => "table"
                                            ],
                                            [
                                                "label" => "条形图",
                                                "value" => "horizontal-bar"
                                            ],
                                            [
                                                "label" => "折线图",
                                                "value" => "line"
                                            ],
                                            [
                                                "label" => "柱状图",
                                                "value" => "bar"
                                            ]
                                        ],
                                        "chatFlag" => true
                                    ],
                                    'title' => [
                                        'text' => '数据详情'
                                    ],

                                ]
                            ],
                        ],
                    ],
                    [
                        'key' => self::MAIL_REPLY_STATUS,
                        "title" => "",
                        "list" => [
                            [
                                'report_key' => 'task2',
                                'sub_key' => 108,
                                'richness' => 1,
                                "title" => \Yii::t('ai', "邮件是否及时回复"),
                                'data_type' => 1,
                                "conclusion" => \Yii::t('ai', "从提供的数据来看，员工B的邮件及时回复率为15.4%，平均回复时间为1天10小时23分钟35秒，而员工A的邮件及时回复率为19.2%，平均回复时间为1天7小时48分钟25秒，但员工A有18封邮件未处理。员工C的数据显示为零，可能是因为没有分配任务或数据缺失。整体来看，两位有数据的成员的邮件回复及时率有待提高，同时员工A的未处理邮件数量也需要关注。"),
                                "report_detail_data" => [
                                    "key" => "task2",
                                    "title" => "任务完成情况",
                                    "name" => "任务完成情况",
                                    "desc" => \Yii::t('ai', "考核任务完成情况；数据范围：你有权限查看的人员的任务数据"),
                                    "type" => "group",
                                    "relevance" => [
                                        [
                                            "key" => "task1",
                                            "name" => "跟进客户"
                                        ],
                                        [
                                            "key" => "task2",
                                            "name" => "回复邮件"
                                        ],
                                        [
                                            "key" => "task3",
                                            "name" => "回复TM"
                                        ]
                                    ],
                                    "report_object_name" => "",
                                    "permission" => [],
                                    "data" => [
                                        [
                                            [
                                                "key" => "task.user_id",
                                                "type" => "field",
                                                "value" => "1", // 递增值，从1开始
                                                "label" => \Yii::t('report','员工C'),
                                                "summaries" => [
                                                    "sum-task.target_reply_mail_count" => [
                                                        "key" => "sum-task.target_reply_mail_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ],
                                                    "sum-task.in_time_reply_mail_count" => [
                                                        "key" => "sum-task.in_time_reply_mail_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ],
                                                    "sum-task.timeout_reply_mail_count" => [
                                                        "key" => "sum-task.timeout_reply_mail_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ],
                                                    "sum-task.todo_reply_mail_count" => [
                                                        "key" => "sum-task.todo_reply_mail_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ],
                                                    "percent-task.in_time_reply_mail_count" => [
                                                        "key" => "percent-task.in_time_reply_mail_count",
                                                        "value" => "-",
                                                        "method" => "percent"
                                                    ],
                                                    "secondTime-task.average_reply_mail_time" => [
                                                        "key" => "secondTime-task.average_reply_mail_time",
                                                        "value" => "-",
                                                        "method" => "secondTime"
                                                    ],
                                                    "sum-task.ignore_task_count" => [
                                                        "key" => "sum-task.ignore_task_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ]
                                                ]
                                            ]
                                        ],
                                        [
                                            [
                                                "key" => "task.user_id",
                                                "type" => "field",
                                                "value" => "2", // 递增值，为员工B
                                                "label" => \Yii::t('report','员工B'),
                                                "summaries" => [
                                                    "sum-task.target_reply_mail_count" => [
                                                        "value" => 26
                                                    ],
                                                    "sum-task.in_time_reply_mail_count" => [
                                                        "value" => 4
                                                    ],
                                                    "sum-task.timeout_reply_mail_count" => [
                                                        "value" => 4
                                                    ],
                                                    "sum-task.todo_reply_mail_count" => [
                                                        "value" => 17
                                                    ],
                                                    "percent-task.in_time_reply_mail_count" => [
                                                        "value" => 15.4
                                                    ],
                                                    "secondTime-task.average_reply_mail_time" => [
                                                        "value" => "1天10小时23分钟35秒"
                                                    ],
                                                    "sum-task.ignore_task_count" => [
                                                        "value" => 0
                                                    ]
                                                ]
                                            ]
                                        ],
                                        // 员工A的数据
                                        [
                                            [
                                                "key" => "task.user_id",
                                                "type" => "field",
                                                "value" => "3", // 递增值，为员工A
                                                "label" => \Yii::t('report','员工A'),
                                                "summaries" => [
                                                    "sum-task.target_reply_mail_count" => [
                                                        "value" => 78
                                                    ],
                                                    "sum-task.in_time_reply_mail_count" => [
                                                        "value" => 15
                                                    ],
                                                    "sum-task.timeout_reply_mail_count" => [
                                                        "value" => 7
                                                    ],
                                                    "sum-task.todo_reply_mail_count" => [
                                                        "value" => 59
                                                    ],
                                                    "percent-task.in_time_reply_mail_count" => [
                                                        "value" => 19.2
                                                    ],
                                                    "secondTime-task.average_reply_mail_time" => [
                                                        "value" => "1天7小时48分钟25秒"
                                                    ],
                                                    "sum-task.ignore_task_count" => [
                                                        "value" => 18
                                                    ]
                                                ]
                                            ]
                                        ],

                                    ],
                                    "total" => [
                                        "sum-task.target_reply_mail_count" => [
                                            "key" => "sum-task.target_reply_mail_count",
                                            "value" => 104,
                                            "method" => "sum"
                                        ],
                                        "sum-task.in_time_reply_mail_count" => [
                                            "key" => "sum-task.in_time_reply_mail_count",
                                            "value" => 19,
                                            "method" => "sum"
                                        ],
                                        "sum-task.timeout_reply_mail_count" => [
                                            "key" => "sum-task.timeout_reply_mail_count",
                                            "value" => 11,
                                            "method" => "sum"
                                        ],
                                        "sum-task.todo_reply_mail_count" => [
                                            "key" => "sum-task.todo_reply_mail_count",
                                            "value" => 76,
                                            "method" => "sum"
                                        ],
                                        "percent-task.in_time_reply_mail_count" => [
                                            "key" => "percent-task.in_time_reply_mail_count",
                                            "value" => 18.27,
                                            "method" => "percent"
                                        ],
                                        "secondTime-task.average_reply_mail_time" => [
                                            "key" => "secondTime-task.average_reply_mail_time",
                                            "value" => "1天8小时56分钟31秒",
                                            "method" => "secondTime"
                                        ],
                                        "sum-task.ignore_task_count" => [
                                            "key" => "sum-task.ignore_task_count",
                                            "value" => 18,
                                            "method" => "sum"
                                        ],
                                    ],
                                    "count" => 0,
                                    "config" => [
                                        "field" => [
                                            [
                                                "key" => "task.user_id",
                                                "type" => "field",
                                                "field_type" => "int",
                                                "label" => \Yii::t('report', "成员(任务)"),
                                                "tip" => ""
                                            ]
                                        ],
                                        "group" => [
                                            [
                                                "key" => "task.user_id",
                                                "type" => "row",
                                                "field_type" => "",
                                                "label" => "task.user_id",
                                                "order" => "",
                                                "extra" => []
                                            ]
                                        ],
                                        "order" => [],
                                        "summaries" => [
                                            [
                                                "key" => "sum-task.target_reply_mail_count",
                                                "type" => "sum",
                                                "field_type" => "",
                                                "refer_list" => "taskList",
                                                "label" => \Yii::t('report', "应回复邮件数"),
                                                "tip" => \Yii::t('report', "「回复邮件」任务类型所触发的任务数"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "sum-task.in_time_reply_mail_count",
                                                "type" => "sum",
                                                "field_type" => "",
                                                "refer_list" => "taskList",
                                                "label" => \Yii::t('report', "及时回复邮件数"),
                                                "tip" => \Yii::t('report', "任务截止时间前完成的任务数"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "sum-task.timeout_reply_mail_count",
                                                "type" => "sum",
                                                "field_type" => "",
                                                "refer_list" => "taskList",
                                                "label" => \Yii::t('report', "超时回复邮件数"),
                                                "tip" => \Yii::t('report', "任务截止时间后完成的任务数"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "sum-task.todo_reply_mail_count",
                                                "type" => "sum",
                                                "field_type" => "",
                                                "refer_list" => "taskList",
                                                "label" => \Yii::t('report', "待回复邮件数"),
                                                "tip" => \Yii::t('report', "暂未完成的任务数"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "percent-task.in_time_reply_mail_count",
                                                "type" => "percent",
                                                "field_type" => "",
                                                "refer_list" => "",
                                                "label" => \Yii::t('report', "邮件及时回复率"),
                                                "tip" => \Yii::t('report', "及时回复邮件数÷应回复邮件数"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "secondTime-task.average_reply_mail_time",
                                                "type" => "secondTime",
                                                "field_type" => "",
                                                "refer_list" => "",
                                                "label" => \Yii::t('report', "平均回复邮件时间"),
                                                "tip" => \Yii::t('report', "完成任务平均所需时间"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "sum-task.ignore_task_count",
                                                "type" => "sum",
                                                "field_type" => "",
                                                "refer_list" => "taskList",
                                                "label" => \Yii::t('report', "忽略任务数"),
                                                "tip" => "",
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ]
                                        ],
                                        "total" => [
                                            "subtotal" => [
                                                "switch" => true
                                            ],
                                            "total" => [
                                                "switch" => true
                                            ],
                                            "hide_zero" => [
                                                "switch" => true,
                                                "default" => true
                                            ],
                                            "showSummaries" => [
                                                "sum-task.target_reply_mail_count",
                                                "sum-task.in_time_reply_mail_count",
                                                "sum-task.timeout_reply_mail_count",
                                                "sum-task.todo_reply_mail_count",
                                                "percent-task.in_time_reply_mail_count",
                                                "secondTime-task.average_reply_mail_time",
                                                "sum-task.ignore_task_count"
                                            ]
                                        ],
                                        "query" => [
                                            [
                                                "field" => "common.visible",
                                                "comment" => "查看范围",
                                                "type" => "select_visible_user_id",
                                                "multiple" => 1,
                                                "value" => [],
                                                "default" => null,
                                                "field_type" => 7,
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "common.visible.department_id",
                                                "comment" => "查看部门id",
                                                "type" => "int",
                                                "multiple" => 1,
                                                "value" => [],
                                                "default" => null,
                                                "field_type" => 7,
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "common.deadline",
                                                "comment" => "任务截止时间",
                                                "type" => "date",
                                                "value" => [
                                                    "start" => "2024-01-01",
                                                    "end" => "2024-12-31"
                                                ],
                                                "multiple" => 0,
                                                "period" => "y",
                                                "default" => 1,
                                                "continuous" => 0,
                                                "maxDateRange" => 31536000,
                                                "field_type" => 4,
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "common.create_time",
                                                "comment" => "任务创建时间",
                                                "type" => "date",
                                                "value" => [
                                                    "start" => null,
                                                    "end" => null
                                                ],
                                                "multiple" => 0,
                                                "continuous" => 1,
                                                "field_type" => 4,
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "common.task_rule_id",
                                                "comment" => "任务规则",
                                                "multiple" => 1,
                                                "value" => [],
                                                "type" => "",
                                                "field_type" => 7,
                                                "label" => ""
                                            ]
                                        ],
                                        "detail" => []
                                    ],
                                    "chart" => [
                                        "chartList" => [
                                            [
                                                "chartType" => "vertical-bar",
                                                "group" => [
                                                    "task.user_id"
                                                ],
                                                "summaries" => [
                                                    "sum-task.target_reply_mail_count",
                                                    "sum-task.in_time_reply_mail_count"
                                                ],
                                                "option" => []
                                            ]
                                        ]
                                    ],
                                    "client_currency" => "",
                                    "subscribe_flag" => true,
                                    "language" => "zh-CN",
                                    "can_setting_filter_field" => false
                                ],
                                'data' => [
                                    'content' => [
                                        [
                                            "task.user_id" => "1",
                                            "sum-task.target_reply_mail_count" => 0,
                                            "sum-task.in_time_reply_mail_count" => 0,
                                            "sum-task.timeout_reply_mail_count" => 0,
                                            "sum-task.todo_reply_mail_count" => 0,
                                            "percent-task.in_time_reply_mail_count" => "-",
                                            "secondTime-task.average_reply_mail_time" => "-",
                                            "sum-task.ignore_task_count" => 0
                                        ],
                                        [
                                            "task.user_id" => \Yii::t('report','员工B'),
                                            "sum-task.target_reply_mail_count" => 26,
                                            "sum-task.in_time_reply_mail_count" => 4,
                                            "sum-task.timeout_reply_mail_count" => 4,
                                            "sum-task.todo_reply_mail_count" => 17,
                                            "percent-task.in_time_reply_mail_count" => 15.4,
                                            "secondTime-task.average_reply_mail_time" => "1天10小时23分钟35秒",
                                            "sum-task.ignore_task_count" => 0
                                        ],
                                        [
                                            "task.user_id" => \Yii::t('report','员工A'),
                                            "sum-task.target_reply_mail_count" => 78,
                                            "sum-task.in_time_reply_mail_count" => 15,
                                            "sum-task.timeout_reply_mail_count" => 7,
                                            "sum-task.todo_reply_mail_count" => 59,
                                            "percent-task.in_time_reply_mail_count" => 19.2,
                                            "secondTime-task.average_reply_mail_time" => "1天7小时48分钟25秒",
                                            "sum-task.ignore_task_count" => 18
                                        ]
                                    ],
                                    'config' => [
                                        "XAxis" => [
                                            [
                                                "name" => \Yii::t('report','成员(任务'),
                                                "field" => "task.user_id"
                                            ]
                                        ],
                                        "YAxis" => [
                                            [
                                                "name" => \Yii::t('report','应回复邮件数'),
                                                "field" => "sum-task.target_reply_mail_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','及时回复邮件数'),
                                                "field" => "sum-task.in_time_reply_mail_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','超时回复邮件数'),
                                                "field" => "sum-task.timeout_reply_mail_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','待回复邮件数'),
                                                "field" => "sum-task.todo_reply_mail_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','邮件及时回复率'),
                                                "field" => "percent-task.in_time_reply_mail_count",
                                                "unit" => "percent"
                                            ],
                                            [
                                                "name" => \Yii::t('report','平均回复邮件时间'),
                                                "field" => "secondTime-task.average_reply_mail_time"
                                            ],
                                            [
                                                "name" => \Yii::t('report','忽略任务数'),
                                                "field" => "sum-task.ignore_task_count"
                                            ]
                                        ],
                                        "charType" => [
                                            [
                                                "label" => "表格",
                                                "value" => "table"
                                            ],
                                            [
                                                "label" => "条形图",
                                                "value" => "horizontal-bar"
                                            ],
                                            [
                                                "label" => "折线图",
                                                "value" => "line"
                                            ],
                                            [
                                                "label" => "柱状图",
                                                "value" => "bar"
                                            ]
                                        ],
                                        "chatFlag" => true
                                    ],
                                    'title' => [
                                        'text' => '数据详情'
                                    ],

                                ]
                            ],
                        ]
                    ],
                    [
                        'key' => self::TM_REPLY_STATUS,
                        "title" => "",
                        "list" => [
                            [
                                'report_key' => 'task3',
                                'sub_key' => 109,
                                'richness' => 1,
                                "title" => \Yii::t('ai', "TM是否及时回复"),
                                'data_type' => 1,
                                "conclusion" => \Yii::t('ai', "从数据来看，员工C的TM及时回复率为100%，平均回复时间为38秒，表现出色。员工B的及时回复率为91.70%，平均回复时间较长，为6小时58分钟36秒，但回复数量较多，说明工作量大，整体表现稳定。员工A的及时回复率为97.83%，平均回复时间为25分钟19秒，且忽略任务数较少，显示出高效的工作能力。"),
                                "report_detail_data" => [
                                    "key" => "task3",
                                    "title" => "任务完成情况",
                                    "name" => "任务完成情况",
                                    "desc" => \Yii::t('ai', "考核任务完成情况；数据范围：你有权限查看的人员的任务数据"),
                                    "type" => "group",
                                    "relevance" => [
                                        [
                                            "key" => "task1",
                                            "name" => "跟进客户"
                                        ],
                                        [
                                            "key" => "task2",
                                            "name" => "回复邮件"
                                        ],
                                        [
                                            "key" => "task3",
                                            "name" => "回复TM"
                                        ]
                                    ],
                                    "report_object_name" => "",
                                    "permission" => [],
                                    "data" => [
                                        [
                                            [
                                                "key" => "task.user_id",
                                                "type" => "field",
                                                "value" => "1",
                                                "label" => \Yii::t('report','员工C'),
                                                "summaries" => [
                                                    "sum-task.target_reply_tm_count" => [
                                                        "key" => "sum-task.target_reply_tm_count",
                                                        "value" => 7,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ],
                                                    "sum-task.in_time_reply_tm_count" => [
                                                        "key" => "sum-task.in_time_reply_tm_count",
                                                        "value" => 7,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ],
                                                    "sum-task.timeout_reply_tm_count" => [
                                                        "key" => "sum-task.timeout_reply_tm_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ],
                                                    "sum-task.todo_reply_tm_count" => [
                                                        "key" => "sum-task.todo_reply_tm_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ],
                                                    "percent-task.in_time_reply_tm_count" => [
                                                        "key" => "percent-task.in_time_reply_tm_count",
                                                        "value" => "100",
                                                        "method" => "percent"
                                                    ],
                                                    "secondTime-task.average_reply_tm_time" => [
                                                        "key" => "secondTime-task.average_reply_tm_time",
                                                        "value" => "38秒",
                                                        "method" => "secondTime"
                                                    ],
                                                    "sum-task.ignore_task_count" => [
                                                        "key" => "sum-task.ignore_task_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ]
                                                ]
                                            ]
                                        ],
                                        [
                                            [
                                                "key" => "task.user_id",
                                                "type" => "field",
                                                "value" => "2",
                                                "label" => \Yii::t('report','员工B'),
                                                "summaries" => [
                                                    "sum-task.target_reply_tm_count" => [
                                                        "key" => "sum-task.target_reply_tm_count",
                                                        "value" => 2687,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ],
                                                    "sum-task.in_time_reply_tm_count" => [
                                                        "key" => "sum-task.in_time_reply_tm_count",
                                                        "value" => 2464,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ],
                                                    "sum-task.timeout_reply_tm_count" => [
                                                        "key" => "sum-task.timeout_reply_tm_count",
                                                        "value" => 152,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ],
                                                    "sum-task.todo_reply_tm_count" => [
                                                        "key" => "sum-task.todo_reply_tm_count",
                                                        "value" => 81,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ],
                                                    "percent-task.in_time_reply_tm_count" => [
                                                        "key" => "percent-task.in_time_reply_tm_count",
                                                        "value" => "91.70",
                                                        "method" => "percent"
                                                    ],
                                                    "secondTime-task.average_reply_tm_time" => [
                                                        "key" => "secondTime-task.average_reply_tm_time",
                                                        "value" => "6小时58分钟36秒",
                                                        "method" => "secondTime"
                                                    ],
                                                    "sum-task.ignore_task_count" => [
                                                        "key" => "sum-task.ignore_task_count",
                                                        "value" => 0,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ]
                                                ]
                                            ]
                                        ],
                                        [
                                            [
                                                "key" => "task.user_id",
                                                "type" => "field",
                                                "value" => "3",
                                                "label" => \Yii::t('report','员工A'),
                                                "summaries" => [
                                                    "sum-task.target_reply_tm_count" => [
                                                        "key" => "sum-task.target_reply_tm_count",
                                                        "value" => 3912,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ],
                                                    "sum-task.in_time_reply_tm_count" => [
                                                        "key" => "sum-task.in_time_reply_tm_count",
                                                        "value" => 3827,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ],
                                                    "sum-task.timeout_reply_tm_count" => [
                                                        "key" => "sum-task.timeout_reply_tm_count",
                                                        "value" => 74,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ],
                                                    "sum-task.todo_reply_tm_count" => [
                                                        "key" => "sum-task.todo_reply_tm_count",
                                                        "value" => 19,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ],
                                                    "percent-task.in_time_reply_tm_count" => [
                                                        "key" => "percent-task.in_time_reply_tm_count",
                                                        "value" => 97.83,
                                                        "method" => "percent"
                                                    ],
                                                    "secondTime-task.average_reply_tm_time" => [
                                                        "key" => "secondTime-task.average_reply_tm_time",
                                                        "value" => "25分钟19秒",
                                                        "method" => "secondTime"
                                                    ],
                                                    "sum-task.ignore_task_count" => [
                                                        "key" => "sum-task.ignore_task_count",
                                                        "value" => 3,
                                                        "method" => "sum",
                                                        "refer_list" => "taskList"
                                                    ]
                                                ]
                                            ]
                                        ],
                                    ],
                                    "total" => [
                                        'sum-task.target_reply_tm_count' => [
                                            'key' => 'sum-task.target_reply_tm_count',
                                            'value' => 6606,
                                            'method' => 'sum'
                                        ],
                                        'sum-task.in_time_reply_tm_count' => [
                                            'key' => 'sum-task.in_time_reply_tm_count',
                                            'value' => 6298,
                                            'method' => 'sum'
                                        ],
                                        'sum-task.timeout_reply_tm_count' => [
                                            'key' => 'sum-task.timeout_reply_tm_count',
                                            'value' => 226,
                                            'method' => 'sum'
                                        ],
                                        'sum-task.todo_reply_tm_count' => [
                                            'key' => 'sum-task.todo_reply_tm_count',
                                            'value' => 100,
                                            'method' => 'sum'
                                        ],
                                        'percent-task.in_time_reply_tm_count' => [
                                            'key' => 'percent-task.in_time_reply_tm_count',
                                            'value' => '95.34',
                                            'method' => 'percent'
                                        ],
                                        'secondTime-task.average_reply_tm_time' => [
                                            'key' => 'secondTime-task.average_reply_tm_time',
                                            'value' => '3小时23分钟42秒',
                                            'method' => 'secondTime'
                                        ],
                                        'sum-task.ignore_task_count' => [
                                            'key' => 'sum-task.ignore_task_count',
                                            'value' => 3,
                                            'method' => 'sum'
                                        ]
                                    ],
                                    "count" => 0,
                                    "config" => [
                                        "field" => [
                                            [
                                                "key" => "task.user_id",
                                                "type" => "field",
                                                "field_type" => "int",
                                                "label" => \Yii::t('report', "成员(任务)"),
                                                "tip" => ""
                                            ]
                                        ],
                                        "group" => [
                                            [
                                                "key" => "task.user_id",
                                                "type" => "row",
                                                "field_type" => "",
                                                "label" => "task.user_id",
                                                "order" => "",
                                                "extra" => []
                                            ]
                                        ],
                                        "order" => [],
                                        "summaries" => [
                                            [
                                                "key" => "sum-task.target_reply_tm_count",
                                                "type" => "sum",
                                                "field_type" => "",
                                                "refer_list" => "taskList",
                                                "label" => \Yii::t('report', "应回复TM数"),
                                                "tip" => \Yii::t('report', "「回复TM」任务类型所触发的任务数"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "sum-task.in_time_reply_tm_count",
                                                "type" => "sum",
                                                "field_type" => "",
                                                "refer_list" => "taskList",
                                                "label" => \Yii::t('report', "及时回复TM数"),
                                                "tip" => \Yii::t('report', "任务截止时间前完成的任务数"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "sum-task.timeout_reply_tm_count",
                                                "type" => "sum",
                                                "field_type" => "",
                                                "refer_list" => "taskList",
                                                "label" => \Yii::t('report', "超时回复TM数"),
                                                "tip" => \Yii::t('report', "任务截止时间后完成的任务数"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "sum-task.todo_reply_tm_count",
                                                "type" => "sum",
                                                "field_type" => "",
                                                "refer_list" => "taskList",
                                                "label" => \Yii::t('report', "待回复TM数"),
                                                "tip" => \Yii::t('report', "暂未完成的任务数"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "percent-task.in_time_reply_tm_count",
                                                "type" => "percent",
                                                "field_type" => "",
                                                "refer_list" => "",
                                                "label" => \Yii::t('report', "TM及时回复率"),
                                                "tip" => \Yii::t('report', "及时回复TM数÷应回复TM数"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "secondTime-task.average_reply_tm_time",
                                                "type" => "secondTime",
                                                "field_type" => "",
                                                "refer_list" => "",
                                                "label" => \Yii::t('report', "平均回复TM时间"),
                                                "tip" => \Yii::t('report', "完成任务平均所需时间"),
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "sum-task.ignore_task_count",
                                                "type" => "sum",
                                                "field_type" => "",
                                                "refer_list" => "taskList",
                                                "label" => \Yii::t('report', "忽略任务数"),
                                                "tip" => "",
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ]
                                        ],
                                        "total" => [
                                            "subtotal" => [
                                                "switch" => true
                                            ],
                                            "total" => [
                                                "switch" => true
                                            ],
                                            "hide_zero" => [
                                                "switch" => true,
                                                "default" => true
                                            ],
                                            "showSummaries" => [
                                                "sum-task.target_reply_tm_count",
                                                "sum-task.in_time_reply_tm_count",
                                                "sum-task.timeout_reply_tm_count",
                                                "sum-task.todo_reply_tm_count",
                                                "percent-task.in_time_reply_tm_count",
                                                "secondTime-task.average_reply_tm_time",
                                                "sum-task.ignore_task_count"
                                            ]
                                        ],
                                        "query" => [
                                            [
                                                "field" => "common.visible",
                                                "comment" => "查看范围",
                                                "type" => "select_visible_user_id",
                                                "multiple" => 1,
                                                "value" => [],
                                                "default" => null,
                                                "field_type" => 7,
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "common.visible.department_id",
                                                "comment" => "查看部门id",
                                                "type" => "int",
                                                "multiple" => 1,
                                                "value" => [],
                                                "default" => null,
                                                "field_type" => 7,
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "common.deadline",
                                                "comment" => "任务截止时间",
                                                "type" => "date",
                                                "value" => [
                                                    "start" => "2024-01-01",
                                                    "end" => "2024-12-31"
                                                ],
                                                "multiple" => 0,
                                                "period" => "y",
                                                "default" => 1,
                                                "continuous" => 0,
                                                "maxDateRange" => 31536000,
                                                "field_type" => 4,
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "common.create_time",
                                                "comment" => "任务创建时间",
                                                "type" => "date",
                                                "value" => [
                                                    "start" => null,
                                                    "end" => null
                                                ],
                                                "multiple" => 0,
                                                "continuous" => 1,
                                                "field_type" => 4,
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "common.task_rule_id",
                                                "comment" => "任务规则",
                                                "multiple" => 1,
                                                "value" => [],
                                                "type" => "",
                                                "field_type" => 7,
                                                "label" => ""
                                            ]
                                        ],
                                        "detail" => []
                                    ],
                                    "chart" => [
                                        "chartList" => [
                                            [
                                                "chartType" => "vertical-bar",
                                                "group" => [
                                                    "task.user_id"
                                                ],
                                                "summaries" => [
                                                    "sum-task.target_reply_tm_count",
                                                    "sum-task.in_time_reply_tm_count"
                                                ],
                                                "option" => []
                                            ]
                                        ]
                                    ],
                                    "client_currency" => "",
                                    "subscribe_flag" => true,
                                    "language" => "zh-CN",
                                    "can_setting_filter_field" => false
                                ],
                                'data' => [
                                    'content' => [
                                        [
                                            "task.user_id" => \Yii::t('report','员工C'),
                                            "sum-task.target_reply_tm_count" => 7,
                                            "sum-task.in_time_reply_tm_count" => 7,
                                            "sum-task.timeout_reply_tm_count" => 0,
                                            "sum-task.todo_reply_tm_count" => 0,
                                            "percent-task.in_time_reply_tm_count" => 100,
                                            "secondTime-task.average_reply_tm_time" => "38秒",
                                            "sum-task.ignore_task_count" => 0
                                        ],
                                        [
                                            "task.user_id" => \Yii::t('report','员工B'),
                                            "sum-task.target_reply_tm_count" => 2687,
                                            "sum-task.in_time_reply_tm_count" => 2464,
                                            "sum-task.timeout_reply_tm_count" => 152,
                                            "sum-task.todo_reply_tm_count" => 81,
                                            "percent-task.in_time_reply_tm_count" => "91.70",
                                            "secondTime-task.average_reply_tm_time" => "6小时58分钟36秒",
                                            "sum-task.ignore_task_count" => 0
                                        ],
                                        [
                                            "task.user_id" => \Yii::t('report','员工A'),
                                            "sum-task.target_reply_tm_count" => 3912,
                                            "sum-task.in_time_reply_tm_count" => 3827,
                                            "sum-task.timeout_reply_tm_count" => 74,
                                            "sum-task.todo_reply_tm_count" => 19,
                                            "percent-task.in_time_reply_tm_count" => 97.83,
                                            "secondTime-task.average_reply_tm_time" => "25分钟19秒",
                                            "sum-task.ignore_task_count" => 3
                                        ]
                                    ],
                                    'config' => [
                                        "XAxis" => [
                                            [
                                                "name" => \Yii::t('report','成员(任务'),
                                                "field" => "task.user_id"
                                            ]
                                        ],
                                        "YAxis" => [
                                            [
                                                "name" => \Yii::t('report','应回复TM数'),
                                                "field" => "sum-task.target_reply_tm_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','及时回复TM数'),
                                                "field" => "sum-task.in_time_reply_tm_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','超时回复TM数'),
                                                "field" => "sum-task.timeout_reply_tm_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','待回复TM数'),
                                                "field" => "sum-task.todo_reply_tm_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','TM及时回复率'),
                                                "field" => "percent-task.in_time_reply_tm_count",
                                                "unit" => "percent"
                                            ],
                                            [
                                                "name" => \Yii::t('report','平均回复TM时间'),
                                                "field" => "secondTime-task.average_reply_tm_time"
                                            ],
                                            [
                                                "name" => \Yii::t('report','忽略任务数'),
                                                "field" => "sum-task.ignore_task_count"
                                            ]
                                        ],
                                        "charType" => [
                                            [
                                                "label" => "表格",
                                                "value" => "table"
                                            ],
                                            [
                                                "label" => "条形图",
                                                "value" => "horizontal-bar"
                                            ],
                                            [
                                                "label" => "折线图",
                                                "value" => "line"
                                            ],
                                            [
                                                "label" => "柱状图",
                                                "value" => "bar"
                                            ]
                                        ],
                                        "chatFlag" => true
                                    ],
                                    'title' => [
                                        'text' => '数据详情'
                                    ],

                                ]
                            ],
                        ]
                    ]
                ]
            ],
            [
                "key" => self::OPPORTUNITY_FOLLOW_UP_SITUATION,
                "title" => \Yii::t('ai', "商机转化情况"),
                'richness' => 1,
                "suggestion" => \Yii::t('ai', "针对员工A，建议维持当前销售策略并分享其成功经验给团队，以提升整体销售业绩。对于员工B，建议进行定价策略培训，并通过模拟销售情景来提高其PI转化率，同时探索除价格外的其他输单原因。对于员工C，建议提供更多销售培训和指导，确保其快速融入团队，并通过跟踪其日常活动和业绩来监控进步。"),
                "conclusion" => \Yii::t('ai', "员工A在销售漏斗分析中表现卓越，商机数和赢单数均为129，赢单率100%，赢单金额达96089.47元，显示出色的销售成绩。员工B虽然询盘转化率和报价转化率均为100%，但PI转化率为0，显示其在成交阶段存在问题，且输单原因分析显示所有失去的商机金额均因价格因素，需要调整定价策略。员工C在此期间没有商机和赢单记录，需要分析原因，可能是新加入团队还未有数据累积。 "),
                'list' => [
                    [
                        'key' => self::SALES_FUNNEL_ANALYSIS,
                        "title" => "",
                        "list" => [
                            [
                                'report_key' => 'sjts3',
                                'sub_key' => 110,
                                'richness' => 1,
                                "title" => \Yii::t('ai', "销售漏斗分析"),
                                'data_type' => 1,
                                "conclusion" => \Yii::t('ai', "员工A的表现在这些数据中显得尤为突出，商机数和赢单数均为127，赢单率达到了100%，同时赢单金额为95929.5元，显示了很高的销售能力。员工B的数据显示她有1个商机但尚未转化为赢单，尽管她的重点询盘转化率和报价转化率均为100%，但PI转化率为0，表明在最终成交阶段可能需要进一步的提升。员工C的数据显示在这段时间内没有商机和赢单，可能需要进一步分析原因，或者是他/她新加入团队，还没有数据累积。"),
                                "report_detail_data" => [
                                    'key' => 'sjts3',
                                    'title' => '员工销售漏斗',
                                    'name' => '员工销售漏斗',
                                    'desc' => \Yii::t('ai', '员工销售漏斗数据，横向对比员工销售能力 数据范围：你有权限查看的员工的商机，可进一步通过筛选缩小数据范围'),
                                    'type' => 'group',
                                    'relevance' => [],
                                    'report_object_name' => '',
                                    'permission' => [
                                        'crm.opportunity.view',
                                    ],
                                    'data' => [
                                        [
                                            [
                                                "key" => "user.user_id",
                                                "type" => "field",
                                                "value" => 1,
                                                "label" => \Yii::t('report','员工C'),
                                                "summaries" => [
                                                    "sum-user.opportunity_count" => [
                                                        "key" => "sum-user.opportunity_count",
                                                        "value" => 0,
                                                        "method" => 'sum'
                                                    ],
                                                    "sum-user.win_number" => [
                                                        "key" => "sum-user.win_number",
                                                        "value" => 0,
                                                        "method" => 'sum'
                                                    ],
                                                    "percent-user.win_scale" => [
                                                        "key" => "percent-user.win_scale",
                                                        "value" => 0,
                                                        "method" => 'percent'
                                                    ],
                                                    "text-user.success_amount" => [
                                                        "key" => "text-user.success_amount",
                                                        "value" => 0,
                                                        "method" => "text"
                                                    ],
                                                    "text-user.success_avg_amount" => [
                                                        "key" => "text-user.success_avg_amount",
                                                        "value" => 0,
                                                        "method" => "sum"
                                                    ],
                                                    "text-user.success_avg_sale_time" => [
                                                        "key" => "text-user.success_avg_sale_time",
                                                        "value" => "0小时",
                                                        "method" => "text"
                                                    ],
                                                    "percent-user.conversion_scale_1" => [
                                                        "key" => "percent-user.conversion_scale_1",
                                                        "value" => 0,
                                                        "method" => 'percent'
                                                    ],
                                                    "percent-user.conversion_scale_2" => [
                                                        "key" => "percent-user.conversion_scale_2",
                                                        "value" => 0,
                                                        "method" => 'percent'
                                                    ],
                                                    "percent-user.conversion_scale_3" => [
                                                        "key" => "percent-user.conversion_scale_3",
                                                        "value" => 0,
                                                        "method" => 'percent'
                                                    ],
                                                    "percent-user.conversion_scale_4" => [
                                                        "key" => "percent-user.conversion_scale_4",
                                                        "value" => 0,
                                                        "method" => 'percent'
                                                    ],
                                                    "percent-user.lost_scale_1" => [
                                                        "key" => "percent-user.lost_scale_1",
                                                        "value" => 0,
                                                        "method" => 'percent'
                                                    ],
                                                    "percent-user.lost_scale_2" => [
                                                        "key" => "percent-user.lost_scale_2",
                                                        "value" => 0,
                                                        "method" => 'percent'
                                                    ],
                                                    "percent-user.lost_scale_3" => [
                                                        "key" => "percent-user.lost_scale_3",
                                                        "value" => 0,
                                                        "method" => 'percent'
                                                    ],
                                                    "percent-user.lost_scale_4" => [
                                                        "key" => "percent-user.lost_scale_4",
                                                        "value" => 0,
                                                        "method" => 'percent'
                                                    ],
                                                    "text-user.avg_stay_time_1" => [
                                                        "key" => "text-user.avg_stay_time_1",
                                                        "value" => "0小时",
                                                        "method" => "text"
                                                    ],
                                                    "text-user.avg_stay_time_2" => [
                                                        "key" => "text-user.avg_stay_time_2",
                                                        "value" => "0小时",
                                                        "method" => "text"
                                                    ],
                                                    "text-user.avg_stay_time_3" => [
                                                        "key" => "text-user.avg_stay_time_3",
                                                        "value" => "0小时",
                                                        "method" => "text"
                                                    ],
                                                    "text-user.avg_stay_time_4" => [
                                                        "key" => "text-user.avg_stay_time_4",
                                                        "value" => "0小时",
                                                        "method" => "text"
                                                    ]
                                                ]
                                            ]
                                        ],
                                        [
                                            [
                                                "key" => "user.user_id",
                                                "type" => "field",
                                                "value" => 2,
                                                "label" => \Yii::t('report','员工B'),
                                                "summaries" => [
                                                    "sum-user.opportunity_count" => [
                                                        "key" => "sum-user.opportunity_count",
                                                        "value" => 1,
                                                        "method" => 'sum'
                                                    ],
                                                    "sum-user.win_number" => [
                                                        "key" => "sum-user.win_number",
                                                        "value" => 0,
                                                        "method" => 'sum'
                                                    ],
                                                    "percent-user.win_scale" => [
                                                        "key" => "percent-user.win_scale",
                                                        "value" => 0,
                                                        "method" => 'percent'
                                                    ],
                                                    "text-user.success_amount" => [
                                                        "key" => "text-user.success_amount",
                                                        "value" => 0,
                                                        "method" => "sum"
                                                    ],
                                                    "text-user.success_avg_amount" => [
                                                        "key" => "text-user.success_avg_amount",
                                                        "value" => 2327,
                                                        "method" => "sum"
                                                    ],
                                                    "text-user.success_avg_sale_time" => [
                                                        "key" => "text-user.success_avg_sale_time",
                                                        "value" => "0小时",
                                                        "method" => "text"
                                                    ],
                                                    "percent-user.conversion_scale_1" => [
                                                        "key" => "percent-user.conversion_scale_1",
                                                        "value" => 100,
                                                        "method" => 'percent'
                                                    ],
                                                    "percent-user.conversion_scale_2" => [
                                                        "key" => "percent-user.conversion_scale_2",
                                                        "value" => 100,
                                                        "method" => 'percent'
                                                    ],
                                                    "percent-user.conversion_scale_3" => [
                                                        "key" => "percent-user.conversion_scale_3",
                                                        "value" => 0,
                                                        "method" => 'percent'
                                                    ],
                                                    "percent-user.conversion_scale_4" => [
                                                        "key" => "percent-user.conversion_scale_4",
                                                        "value" => 0,
                                                        "method" => 'percent'
                                                    ],
                                                    "percent-user.lost_scale_1" => [
                                                        "key" => "percent-user.lost_scale_1",
                                                        "value" => 0,
                                                        "method" => 'percent'
                                                    ],
                                                    "percent-user.lost_scale_2" => [
                                                        "key" => "percent-user.lost_scale_2",
                                                        "value" => 0,
                                                        "method" => 'percent'
                                                    ],
                                                    "percent-user.lost_scale_3" => [
                                                        "key" => "percent-user.lost_scale_3",
                                                        "value" => 0,
                                                        "method" => 'percent'
                                                    ],
                                                    "percent-user.lost_scale_4" => [
                                                        "key" => "percent-user.lost_scale_4",
                                                        "value" => 0,
                                                        "method" => 'percent'
                                                    ],
                                                    "text-user.avg_stay_time_1" => [
                                                        "key" => "text-user.avg_stay_time_1",
                                                        "value" => "2天9小时",
                                                        "method" => "text"
                                                    ],
                                                    "text-user.avg_stay_time_2" => [
                                                        "key" => "text-user.avg_stay_time_2",
                                                        "value" => "0小时",
                                                        "method" => "text"
                                                    ],
                                                    "text-user.avg_stay_time_3" => [
                                                        "key" => "text-user.avg_stay_time_3",
                                                        "value" => "0小时",
                                                        "method" => "text"
                                                    ],
                                                    "text-user.avg_stay_time_4" => [
                                                        "key" => "text-user.avg_stay_time_4",
                                                        "value" => "0小时",
                                                        "method" => "text"
                                                    ]
                                                ]
                                            ]
                                        ],
                                        [
                                            [
                                                "key" => "user.user_id",
                                                "type" => "field",
                                                "value" => 3,
                                                "label" => \Yii::t('report','员工A'),
                                                "summaries" => [
                                                    "sum-user.opportunity_count" => [
                                                        "key" => "sum-user.opportunity_count",
                                                        "value" => 127,
                                                        "method" => 'sum'
                                                    ],
                                                    "sum-user.win_number" => [
                                                        "key" => "sum-user.win_number",
                                                        "value" => 127,
                                                        "method" => 'sum'
                                                    ],
                                                    "percent-user.win_scale" => [
                                                        "key" => "percent-user.win_scale",
                                                        "value" => 100,
                                                        "method" => 'sum'
                                                    ],
                                                    "text-user.success_amount" => [
                                                        "key" => "text-user.success_amount",
                                                        "value" => 95929.5,
                                                        "method" => 'percent'
                                                    ],
                                                    "text-user.success_avg_amount" => [
                                                        "key" => "text-user.success_avg_amount",
                                                        "value" => 755.35,
                                                        "method" => 'percent'
                                                    ],
                                                    "text-user.success_avg_sale_time" => [
                                                        "key" => "text-user.success_avg_sale_time",
                                                        "value" => "16小时",
                                                        "method" => "text"
                                                    ],
                                                    "percent-user.conversion_scale_1" => [
                                                        "key" => "percent-user.conversion_scale_1",
                                                        "value" => 100,
                                                        "method" => 'percent'
                                                    ],
                                                    "percent-user.conversion_scale_2" => [
                                                        "key" => "percent-user.conversion_scale_2",
                                                        "value" => 100,
                                                        "method" => 'percent'
                                                    ],
                                                    "percent-user.conversion_scale_3" => [
                                                        "key" => "percent-user.conversion_scale_3",
                                                        "value" => 100,
                                                        "method" => 'percent'
                                                    ],
                                                    "percent-user.conversion_scale_4" => [
                                                        "key" => "percent-user.conversion_scale_4",
                                                        "value" => 0,
                                                        "method" => 'percent'
                                                    ],
                                                    "percent-user.lost_scale_1" => [
                                                        "key" => "percent-user.lost_scale_1",
                                                        "value" => 0,
                                                        "method" => 'percent'
                                                    ],
                                                    "percent-user.lost_scale_2" => [
                                                        "key" => "percent-user.lost_scale_2",
                                                        "value" => 0,
                                                        "method" => 'percent'
                                                    ],
                                                    "percent-user.lost_scale_3" => [
                                                        "key" => "percent-user.lost_scale_3",
                                                        "value" => 0,
                                                        "method" => 'percent'
                                                    ],
                                                    "percent-user.lost_scale_4" => [
                                                        "key" => "percent-user.lost_scale_4",
                                                        "value" => 0,
                                                        "method" => 'percent'
                                                    ],
                                                    "text-user.avg_stay_time_1" => [
                                                        "key" => "text-user.avg_stay_time_1",
                                                        "value" => "16小时",
                                                        "method" => "text"
                                                    ],
                                                    "text-user.avg_stay_time_2" => [
                                                        "key" => "text-user.avg_stay_time_2",
                                                        "value" => "0小时",
                                                        "method" => "text"
                                                    ],
                                                    "text-user.avg_stay_time_3" => [
                                                        "key" => "text-user.avg_stay_time_3",
                                                        "value" => "0小时",
                                                        "method" => "text"
                                                    ],
                                                    "text-user.avg_stay_time_4" => [
                                                        "key" => "text-user.avg_stay_time_4",
                                                        "value" => "0小时",
                                                        "method" => "text"
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ],
                                    'total' => [
                                        "sum-user.opportunity_count" => [
                                            "key" => "sum-user.opportunity_count",
                                            "value" => 128,
                                            "method" => "sum"
                                        ],
                                        "sum-user.win_number" => [
                                            "key" => "sum-user.win_number",
                                            "value" => 127,
                                            "method" => "sum"
                                        ],
                                        "percent-user.win_scale" => [
                                            "key" => "percent-user.win_scale",
                                            "value" => '-',
                                            "method" => "percent"
                                        ],
                                        "text-user.success_amount" => [
                                            "key" => "text-user.success_amount",
                                            "value" => '-',
                                            "method" => "text"
                                        ],
                                        "text-user.success_avg_amount" => [
                                            "key" => "text-user.success_avg_amount",
                                            "value" => '-',
                                            "method" => "text"
                                        ],
                                        "text-user.success_avg_sale_time" => [
                                            "key" => "text-user.success_avg_sale_time",
                                            "value" => '-',
                                            "method" => "text"
                                        ],
                                        "percent-user.conversion_scale_1" => [
                                            "key" => "percent-user.conversion_scale_1",
                                            "value" => '-',
                                            "method" => "percent"
                                        ],
                                        "percent-user.conversion_scale_2" => [
                                            "key" => "percent-user.conversion_scale_2",
                                            "value" => '-',
                                            "method" => "percent"
                                        ],
                                        "percent-user.conversion_scale_3" => [
                                            "key" => "percent-user.conversion_scale_3",
                                            "value" => '-',
                                            "method" => "percent"
                                        ],
                                        "percent-user.lost_scale_1" => [
                                            "key" => "percent-user.lost_scale_1",
                                            "value" => '-',
                                            "method" => "percent"
                                        ],
                                        "percent-user.lost_scale_2" => [
                                            "key" => "percent-user.lost_scale_2",
                                            "value" => '-',
                                            "method" => "percent"
                                        ],
                                        "percent-user.lost_scale_3" => [
                                            "key" => "percent-user.lost_scale_3",
                                            "value" => '-',
                                            "method" => "percent"
                                        ],
                                        "text-user.avg_stay_time_1" => [
                                            "key" => "text-user.avg_stay_time_1",
                                            "value" => '-',
                                            "method" => "text"
                                        ],
                                        "text-user.avg_stay_time_2" => [
                                            "key" => "text-user.avg_stay_time_2",
                                            "value" => '-',
                                            "method" => "text"
                                        ],
                                        "text-user.avg_stay_time_3" => [
                                            "key" => "text-user.avg_stay_time_3",
                                            "value" => '-',
                                            "method" => "text"
                                        ]
                                    ],
                                    'count' => 0,
                                    'config' => [
                                        'field' => [
                                            [
                                                'key' => 'user.user_id',
                                                'type' => 'field',
                                                'field_type' => '',
                                                'label' => \Yii::t('report', '员工(员工)'),
                                                'tip' => '',
                                            ],
                                        ],
                                        'group' => [
                                            [
                                                'key' => 'user.user_id',
                                                'type' => 'row',
                                                'field_type' => '',
                                                'label' => 'user.user_id',
                                                'order' => '',
                                                'extra' => [],
                                            ],
                                        ],
                                        'order' => [],
                                        'summaries' => [
                                            [
                                                'key' => 'sum-user.opportunity_count',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => 'opportunityList',
                                                'label' => \Yii::t('report', '商机数'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'sum-user.win_number',
                                                'type' => 'sum',
                                                'field_type' => '',
                                                'refer_list' => 'opportunityList',
                                                'label' => \Yii::t('report', '赢单数'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'percent-user.win_scale',
                                                'type' => 'percent',
                                                'field_type' => '',
                                                'refer_list' => '',
                                                'label' => \Yii::t('report', '赢单率'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'text-user.success_amount',
                                                'type' => 'text',
                                                'field_type' => 'currency',
                                                'refer_list' => '',
                                                'label' => \Yii::t('report', '赢单金额'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'text-user.success_avg_amount',
                                                'type' => 'text',
                                                'field_type' => 'currency',
                                                'refer_list' => 'opportunityList',
                                                'label' => \Yii::t('report', '商机均价'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'text-user.success_avg_sale_time',
                                                'type' => 'text',
                                                'field_type' => '',
                                                'refer_list' => '',
                                                'label' => \Yii::t('report', '平均销售周期'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'percent-user.conversion_scale_1',
                                                'type' => 'percent',
                                                'field_type' => '',
                                                'refer_list' => '',
                                                'label' => \Yii::t('report', '重点询盘转化率'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'percent-user.conversion_scale_2',
                                                'type' => 'percent',
                                                'field_type' => '',
                                                'refer_list' => '',
                                                'label' => \Yii::t('report', '报价转化率'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'percent-user.conversion_scale_3',
                                                'type' => 'percent',
                                                'field_type' => '',
                                                'refer_list' => '',
                                                'label' => \Yii::t('report', 'PI转化率'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'percent-user.conversion_scale_4',
                                                'type' => 'percent',
                                                'field_type' => '',
                                                'refer_list' => '',
                                                'label' => \Yii::t('report', '重点询盘流失率'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'percent-user.lost_scale_1',
                                                'type' => 'percent',
                                                'field_type' => '',
                                                'refer_list' => '',
                                                'label' => \Yii::t('report', '报价流失率'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'percent-user.lost_scale_2',
                                                'type' => 'percent',
                                                'field_type' => '',
                                                'refer_list' => '',
                                                'label' => \Yii::t('report', 'PI流失率'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'text-user.avg_stay_time_1',
                                                'type' => 'text',
                                                'field_type' => '',
                                                'refer_list' => '',
                                                'label' => \Yii::t('report', '重点询盘平均停留时间'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'text-user.avg_stay_time_2',
                                                'type' => 'text',
                                                'field_type' => '',
                                                'refer_list' => '',
                                                'label' => \Yii::t('report', '报价平均停留时间'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                            [
                                                'key' => 'text-user.avg_stay_time_3',
                                                'type' => 'text',
                                                'field_type' => '',
                                                'refer_list' => '',
                                                'label' => \Yii::t('report', 'PI平均停留时间'),
                                                'tip' => '',
                                                'order' => '',
                                                'value_type' => '',
                                                'hidden' => 0,
                                            ],
                                        ],
                                        'total' => [
                                            'subtotal' => [
                                                'switch' => true,
                                            ],
                                            'total' => [
                                                'switch' => true,
                                            ],
                                            'hide_zero' => [
                                                'switch' => true,
                                                'default' => true,
                                            ],
                                            'detail' => [
                                                'switch' => true,
                                                'default' => true,
                                            ],
                                        ],
                                        'query' => [
                                            [
                                                'field' => 'common.visible',
                                                'comment' => '查看范围',
                                                'type' => 'select_visible_user_id',
                                                'multiple' => 1,
                                                'value' => [],
                                                'field_type' => 7,
                                                'label' => '',
                                            ],
                                            [
                                                'field' => 'opportunity.create_time',
                                                'comment' => '创建日期',
                                                'type' => 'date',
                                                'value' => [
                                                    'start' => '2024-05-01',
                                                    'end' => '2024-05-29',
                                                ],
                                                'multiple' => 0,
                                                'period' => 'm',
                                                'default' => 1,
                                                'continuous' => 1,
                                                'field_type' => 4,
                                                'label' => '',
                                            ],
                                            [
                                                'field' => 'opportunity.flow_id',
                                                'comment' => '销售流程',
                                                'type' => 'int',
                                                'value' => 3163429534,
                                                'multiple' => 0,
                                                'default' => 'main_flow',
                                                'field_type' => '3',
                                                'options' => [],
                                                'label' => '外贸销售标准流程',
                                            ],
                                        ],
                                        'detail' => [],
                                    ],
                                    'chart' => [],
                                    'client_currency' => '',
                                    'subscribe_flag' => false,
                                    'language' => 'zh-CN',
                                    'can_setting_filter_field' => false,
                                ],
                                'data' => [
                                    'content' => [
                                        [
                                            "user.user_id" => \Yii::t('report','员工C'),
                                            "sum-user.opportunity_count" => 0,
                                            "sum-user.win_number" => 0,
                                            "percent-user.win_scale" => 0,
                                            "text-user.success_amount" => 0,
                                            "text-user.success_avg_amount" => 0,
                                            "text-user.success_avg_sale_time" => "0小时",
                                            "percent-user.conversion_scale_1" => 0,
                                            "percent-user.conversion_scale_2" => 0,
                                            "percent-user.conversion_scale_3" => 0,
                                            "percent-user.conversion_scale_4" => 0,
                                            "percent-user.lost_scale_1" => 0,
                                            "percent-user.lost_scale_2" => 0,
                                            "percent-user.lost_scale_3" => 0,
                                            "percent-user.lost_scale_4" => 0,
                                            "text-user.avg_stay_time_1" => "0小时",
                                            "text-user.avg_stay_time_2" => "0小时",
                                            "text-user.avg_stay_time_3" => "0小时",
                                            "text-user.avg_stay_time_4" => "0小时"
                                        ],
                                        [
                                            "user.user_id" => \Yii::t('report','员工B'),
                                            "sum-user.opportunity_count" => 1,
                                            "sum-user.win_number" => 0,
                                            "percent-user.win_scale" => 0,
                                            "text-user.success_amount" => 0,
                                            "text-user.success_avg_amount" => 2327,
                                            "text-user.success_avg_sale_time" => "0小时",
                                            "percent-user.conversion_scale_1" => 100,
                                            "percent-user.conversion_scale_2" => 100,
                                            "percent-user.conversion_scale_3" => 0,
                                            "percent-user.conversion_scale_4" => 0,
                                            "percent-user.lost_scale_1" => 0,
                                            "percent-user.lost_scale_2" => 0,
                                            "percent-user.lost_scale_3" => 0,
                                            "percent-user.lost_scale_4" => 0,
                                            "text-user.avg_stay_time_1" => "2天9小时",
                                            "text-user.avg_stay_time_2" => "0小时",
                                            "text-user.avg_stay_time_3" => "0小时",
                                            "text-user.avg_stay_time_4" => "0小时"
                                        ],
                                        [
                                            "user.user_id" => \Yii::t('report','员工A'),
                                            "sum-user.opportunity_count" => 127,
                                            "sum-user.win_number" => 127,
                                            "percent-user.win_scale" => 100,
                                            "text-user.success_amount" => 95929.5,
                                            "text-user.success_avg_amount" => 755.35,
                                            "text-user.success_avg_sale_time" => "16小时",
                                            "percent-user.conversion_scale_1" => 100,
                                            "percent-user.conversion_scale_2" => 100,
                                            "percent-user.conversion_scale_3" => 100,
                                            "percent-user.conversion_scale_4" => 0,
                                            "percent-user.lost_scale_1" => 0,
                                            "percent-user.lost_scale_2" => 0,
                                            "percent-user.lost_scale_3" => 0,
                                            "percent-user.lost_scale_4" => 0,
                                            "text-user.avg_stay_time_1" => "16小时",
                                            "text-user.avg_stay_time_2" => "0小时",
                                            "text-user.avg_stay_time_3" => "0小时",
                                            "text-user.avg_stay_time_4" => "0小时"
                                        ]
                                    ],
                                    'config' => [
                                        "XAxis" => [
                                            [
                                                "name" => \Yii::t('report','员工(员工)'),
                                                "field" => "user.user_id"
                                            ]
                                        ],
                                        "YAxis" => [
                                            [
                                                "name" => \Yii::t('report','商机数'),
                                                "field" => "sum-user.opportunity_count"
                                            ],
                                            [
                                                "name" => \Yii::t('report','赢单数'),
                                                "field" => "sum-user.win_number"
                                            ],
                                            [
                                                "name" => \Yii::t('report','赢单率'),
                                                "field" => "percent-user.win_scale",
                                                "unit" => "percent"
                                            ],
                                            [
                                                "name" => \Yii::t('report','赢单金额'),
                                                "field" => "text-user.success_amount"
                                            ],
                                            [
                                                "name" => \Yii::t('report','商机均价'),
                                                "field" => "text-user.success_avg_amount"
                                            ],
                                            [
                                                "name" => \Yii::t('report','平均销售周期'),
                                                "field" => "text-user.success_avg_sale_time"
                                            ],
                                            [
                                                "name" => \Yii::t('report','重点询盘转化率'),
                                                "field" => "percent-user.conversion_scale_1",
                                                "unit" => "percent"
                                            ],
                                            [
                                                "name" => \Yii::t('report','报价转化率'),
                                                "field" => "percent-user.conversion_scale_2",
                                                "unit" => "percent"
                                            ],
                                            [
                                                "name" => \Yii::t('report','PI转化率'),
                                                "field" => "percent-user.conversion_scale_3",
                                                "unit" => "percent"
                                            ],
                                            [
                                                "name" => \Yii::t('report','重点询盘流失率'),
                                                "field" => "percent-user.conversion_scale_4",
                                                "unit" => "percent"
                                            ],
                                            [
                                                "name" => \Yii::t('report','报价流失率'),
                                                "field" => "percent-user.lost_scale_1",
                                                "unit" => "percent"
                                            ],
                                            [
                                                "name" => \Yii::t('report','PI流失率'),
                                                "field" => "percent-user.lost_scale_2",
                                                "unit" => "percent"
                                            ],
                                            [
                                                "name" => \Yii::t('report','重点询盘平均停留时间'),
                                                "field" => "text-user.avg_stay_time_1"
                                            ],
                                            [
                                                "name" => \Yii::t('report','报价平均停留时间'),
                                                "field" => "text-user.avg_stay_time_2"
                                            ],
                                            [
                                                "name" => \Yii::t('report','PI平均停留时间'),
                                                "field" => "text-user.avg_stay_time_3"
                                            ]
                                        ],
                                        "charType" => [
                                            [
                                                "label" => "表格",
                                                "value" => "table"
                                            ],
                                            [
                                                "label" => "条形图",
                                                "value" => "horizontal-bar"
                                            ],
                                            [
                                                "label" => "折线图",
                                                "value" => "line"
                                            ],
                                            [
                                                "label" => "柱状图",
                                                "value" => "bar"
                                            ]
                                        ],
                                        "chatFlag" => true
                                    ],
                                    'title' => [
                                        'text' => '数据详情'
                                    ],

                                ]
                            ],
                        ],
                    ],
                    [
                        'key' => self::ANALYSIS_OF_FAIL_OPPORTUNITY_REASONS,
                        "title" => "",
                        "list" => [
                            [
                                'report_key' => 'sj16',
                                'sub_key' => 111,
                                'richness' => 1,
                                "title" => \Yii::t('ai', "输单原因分析"),
                                'data_type' => 1,
                                "conclusion" => \Yii::t('ai', "根据提供的数据，员工B在商机方面的表现有待提高。她在价格因素上输掉的商机金额总和为569，占她总失去商机金额的100%，而在其它原因上虽然没有输掉金额，但也有一次记录，占25%。这表明员工B可能需要在定价策略上做出调整，并探索输单的其他潜在原因。"),
                                "report_detail_data" => [
                                    "key" => "sj12",
                                    "title" => "商机输单原因分布",
                                    "name" => "商机输单原因分布",
                                    "desc" => "查看输单商机根据不同人员的销售金额总和/商机计数在输单原因上的分布 数据范围：你有权限查看的所有输单商机，可进一步通过筛选缩小数据范围",
                                    "type" => "group",
                                    "relevance" => [
                                        [
                                            "key" => "sj12",
                                            "name" => "整体"
                                        ],
                                        [
                                            "key" => "sj16",
                                            "name" => "员工"
                                        ]
                                    ],
                                    "report_object_name" => "",
                                    "permission" => [
                                        "crm.opportunity.view"
                                    ],
                                    "data" => [
                                        [
                                            [
                                                "key" => "opportunity.main_user",
                                                "type" => "field",
                                                "value" => 1, // 假设的值，实际应用中应从数据源获取
                                                "label" => \Yii::t('report','员工B'),
                                                "summaries" => [
                                                    "sum-opportunity.amount" => [
                                                        "key" => "sum-opportunity.amount",
                                                        "value" => 569, // 销售金额(商机)-总和
                                                        "method" => "sum"
                                                    ],
                                                    "sumPercent-opportunity.amount" => [
                                                        "key" => "sumPercent-opportunity.amount",
                                                        "value" => 100, // 总和占比
                                                        "method" => "sumPercent"
                                                    ],
                                                    "row-opportunity.opportunity_id" => [
                                                        "key" => "row-opportunity.opportunity_id",
                                                        "value" => 4, // 记录-计数
                                                        "method" => "row",
                                                        "refer_list" => "opportunityList",
                                                    ],
                                                    "rowPercent-opportunity.opportunity_id" => [
                                                        "key" => "rowPercent-opportunity.opportunity_id",
                                                        "value" => 100, // 计数百分比
                                                        "method" => "rowPercent"
                                                    ]
                                                ]
                                            ],
                                            [
                                                "key" => "opportunity.fail_type",
                                                "type" => "field",
                                                "value" => 1, // 假设的值，实际应用中应从数据源获取
                                                "label" => \Yii::t('report','价格因素'),
                                                "summaries" => [
                                                    "sum-opportunity.amount" => [
                                                        "key" => "sum-opportunity.amount",
                                                        "value" => 569, // 销售金额(商机)-总和
                                                        "method" => "sum"
                                                    ],
                                                    "sumPercent-opportunity.amount" => [
                                                        "key" => "sumPercent-opportunity.amount",
                                                        "value" => 100, // 总和占比
                                                        "method" => "sumPercent"
                                                    ],
                                                    "row-opportunity.opportunity_id" => [
                                                        "key" => "row-opportunity.opportunity_id",
                                                        "value" => 3, // 记录-计数
                                                        "method" => "row",
                                                        "refer_list" => "opportunityList",
                                                    ],
                                                    "rowPercent-opportunity.opportunity_id" => [
                                                        "key" => "rowPercent-opportunity.opportunity_id",
                                                        "value" => 75, // 计数百分比
                                                        "method" => "rowPercent"
                                                    ]
                                                ]
                                            ]
                                        ],
                                        [
                                            [
                                                "key" => "opportunity.main_user",
                                                "type" => "field",
                                                "value" => 1, // 假设的值，实际应用中应从数据源获取
                                                "label" => \Yii::t('report','员工B'),
                                                "summaries" => [
                                                    "sum-opportunity.amount" => [
                                                        "key" => "sum-opportunity.amount",
                                                        "value" => 0, // 销售金额(商机)-总和
                                                        "method" => "sum"
                                                    ],
                                                    "sumPercent-opportunity.amount" => [
                                                        "key" => "sumPercent-opportunity.amount",
                                                        "value" => 0, // 总和占比
                                                        "method" => "sumPercent"
                                                    ],
                                                    "row-opportunity.opportunity_id" => [
                                                        "key" => "row-opportunity.opportunity_id",
                                                        "value" => 1, // 记录-计数
                                                        "method" => "row",
                                                        "refer_list" => "opportunityList",

                                                    ],
                                                    "rowPercent-opportunity.opportunity_id" => [
                                                        "key" => "rowPercent-opportunity.opportunity_id",
                                                        "value" => 25, // 计数百分比
                                                        "method" => "rowPercent"
                                                    ]
                                                ]
                                            ],
                                            [
                                                "key" => "opportunity.fail_type",
                                                "type" => "field",
                                                "value" => 2, // 假设的值，实际应用中应从数据源获取
                                                "label" => \Yii::t('report','其他'),
                                                "summaries" => [
                                                    "sum-opportunity.amount" => [
                                                        "key" => "sum-opportunity.amount",
                                                        "value" => 0, // 销售金额(商机)-总和
                                                        "method" => "sum"
                                                    ],
                                                    "sumPercent-opportunity.amount" => [
                                                        "key" => "sumPercent-opportunity.amount",
                                                        "value" => 0, // 总和占比
                                                        "method" => "sumPercent"
                                                    ],
                                                    "row-opportunity.opportunity_id" => [
                                                        "key" => "row-opportunity.opportunity_id",
                                                        "value" => 1, // 记录-计数
                                                        "method" => "row",
                                                        "refer_list" => "opportunityList",
                                                    ],
                                                    "rowPercent-opportunity.opportunity_id" => [
                                                        "key" => "rowPercent-opportunity.opportunity_id",
                                                        "value" => 25, // 计数百分比
                                                        "method" => "rowPercent"
                                                    ]
                                                ]
                                            ]
                                        ],

                                    ],
                                    "total" => [
                                        "sum-opportunity.amount" => [
                                            "key" => "sum-opportunity.amount",
                                            "value" => 569, // 销售金额(商机)-总和
                                            "method" => "sum"
                                        ],
                                        "sumPercent-opportunity.amount" => [
                                            "key" => "sumPercent-opportunity.amount",
                                            "value" => "-", // 总和占比（无法计算，因为没有整体参照值）
                                            "method" => "sumPercent"
                                        ],
                                        "row-opportunity.opportunity_id" => [
                                            "key" => "row-opportunity.opportunity_id",
                                            "value" => 4, // 记录-计数
                                            "method" => "row"
                                        ],
                                        "rowPercent-opportunity.opportunity_id" => [
                                            "key" => "rowPercent-opportunity.opportunity_id",
                                            "value" => "-", // 计数百分比（无法计算，因为没有整体参照值）
                                            "method" => "rowPercent"
                                        ]
                                    ],
                                    "count" => 0,
                                    "config" => [
                                        "field" => [
                                            [
                                                "key" => "opportunity.fail_type",
                                                "type" => "field",
                                                "field_type" => "3",
                                                "label" => \Yii::t('report', "输单原因(商机)"),
                                                "tip" => ""
                                            ],
                                            [
                                                "key" => "opportunity.main_user",
                                                "type" => "field",
                                                "field_type" => 1,
                                                "label" => \Yii::t('report', "负责人(商机)"),
                                                "tip" => ""
                                            ]
                                        ],
                                        "group" => [
                                            [
                                                "key" => "opportunity.main_user",
                                                "type" => "row",
                                                "field_type" => "",
                                                "label" => "opportunity.main_user",
                                                "order" => "",
                                                "extra" => []
                                            ],
                                            [
                                                "key" => "opportunity.fail_type",
                                                "type" => "row",
                                                "field_type" => "",
                                                "label" => "opportunity.fail_type",
                                                "order" => "",
                                                "extra" => []
                                            ]
                                        ],
                                        "order" => [],
                                        "summaries" => [
                                            [
                                                "key" => "sum-opportunity.amount",
                                                "type" => "sum",
                                                "field_type" => "currency",
                                                "refer_list" => "",
                                                "label" => \Yii::t('report', "销售金额(商机)-总和"),
                                                "tip" => "",
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "sumPercent-opportunity.amount",
                                                "type" => "sumPercent",
                                                "field_type" => "currency",
                                                "refer_list" => "",
                                                "label" => \Yii::t('report', "总和占比"),
                                                "tip" => "",
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "row-opportunity.opportunity_id",
                                                "type" => "row",
                                                "field_type" => "",
                                                "refer_list" => "opportunityList",
                                                "label" => \Yii::t('report', "记录-计数"),
                                                "tip" => "",
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ],
                                            [
                                                "key" => "rowPercent-opportunity.opportunity_id",
                                                "type" => "rowPercent",
                                                "field_type" => "",
                                                "refer_list" => "",
                                                "label" => \Yii::t('report', "计数百分比"),
                                                "tip" => "",
                                                "order" => "",
                                                "value_type" => "",
                                                "hidden" => 0
                                            ]
                                        ],
                                        "total" => [
                                            "subtotal" => [
                                                "switch" => true
                                            ],
                                            "total" => [
                                                "switch" => true
                                            ],
                                            "hide_zero" => [
                                                "switch" => true,
                                                "default" => true
                                            ],
                                            "detail" => [
                                                "switch" => true,
                                                "default" => false
                                            ]
                                        ],
                                        "query" => [
                                            [
                                                "field" => "common.visible",
                                                "comment" => "查看范围",
                                                "type" => "select_visible_user_id",
                                                "multiple" => 1,
                                                "value" => [],
                                                "field_type" => 7,
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "opportunity.create_time",
                                                "comment" => "创建时间",
                                                "type" => "date",
                                                "value" => [
                                                    "start" => "2024-01-01",
                                                    "end" => "2024-05-29"
                                                ],
                                                "multiple" => 0,
                                                "period" => "y",
                                                "default" => 1,
                                                "continuous" => 1,
                                                "field_type" => 4,
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "opportunity.flow_id",
                                                "comment" => "销售流程",
                                                "type" => "int",
                                                "value" => [],
                                                "multiple" => 1,
                                                "field_type" => 7,
                                                "options" => [],
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "opportunity.main_user",
                                                "comment" => "负责人",
                                                "type" => 7,
                                                "value" => [],
                                                "multiple" => 1,
                                                "field_type" => 7,
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "opportunity.origin_list",
                                                "comment" => "商机来源",
                                                "type" => "int",
                                                "value" => [],
                                                "multiple" => 1,
                                                "field_type" => 7,
                                                "options" => [],
                                                "label" => ""
                                            ],
                                            [
                                                "field" => "opportunity.type",
                                                "comment" => "商机类型",
                                                "type" => "int",
                                                "value" => [],
                                                "multiple" => 1,
                                                "field_type" => 7,
                                                "options" => [],
                                                "label" => ""
                                            ]
                                        ],
                                        "detail" => []
                                    ],
                                    "chart" => [
                                        "chartList" => [
                                            [
                                                "chartType" => "horizontal-bar",
                                                "group" => [
                                                    "opportunity.main_user",
                                                    "opportunity.fail_type"
                                                ],
                                                "summaries" => [
                                                    "sum-opportunity.amount"
                                                ],
                                                "option" => [
                                                    "horizontal-bar"
                                                ]
                                            ]
                                        ]
                                    ],
                                    "client_currency" => "",
                                    "subscribe_flag" => false,
                                    "language" => "zh-CN",
                                    "can_setting_filter_field" => true
                                ],
                                'data' => [
                                    'content' => [
                                        [
                                            "opportunity.fail_type" => \Yii::t('report','价格因素'),
                                            "sum-opportunity.amount" => 569,
                                            "sumPercent-opportunity.amount" => 100,
                                            "row-opportunity.opportunity_id" => 3,
                                            "rowPercent-opportunity.opportunity_id" => 75,
                                            "opportunity.main_user" => '员工B'
                                        ],
                                        [
                                            "opportunity.fail_type" => \Yii::t('report','其他'),
                                            "sum-opportunity.amount" => 0,
                                            "sumPercent-opportunity.amount" => 0,
                                            "row-opportunity.opportunity_id" => 1,
                                            "rowPercent-opportunity.opportunity_id" => 25,
                                            "opportunity.main_user" => '员工B'
                                        ]


                                    ],
                                    'config' => [
                                        "XAxis" => [
                                            [
                                                "name" => \Yii::t('report','输单原因(商机)'),
                                                "field" => "opportunity.fail_type"
                                            ],
                                            [
                                                "name" => \Yii::t('report','负责人(商机)'),
                                                "field" => "opportunity.main_user"
                                            ]
                                        ],
                                        "YAxis" => [
                                            [
                                                "name" => \Yii::t('report','销售金额(商机)-总和'),
                                                "field" => "sum-opportunity.amount"
                                            ],
                                            [
                                                "name" => \Yii::t('report','总和占比'),
                                                "field" => "sumPercent-opportunity.amount",
                                                "unit" => "percent"
                                            ],
                                            [
                                                "name" => \Yii::t('report','记录-计数'),
                                                "field" => "row-opportunity.opportunity_id"
                                            ],
                                            [
                                                "name" => \Yii::t('report','计数百分比'),
                                                "field" => "rowPercent-opportunity.opportunity_id",
                                                "unit" => "percent"
                                            ]
                                        ],
                                        "charType" => [
                                            [
                                                "label" => "表格",
                                                "value" => "table"
                                            ],
                                            [
                                                "label" => "条形图",
                                                "value" => "horizontal-bar"
                                            ],
                                            [
                                                "label" => "折线图",
                                                "value" => "line"
                                            ],
                                            [
                                                "label" => "柱状图",
                                                "value" => "bar"
                                            ],
                                        ],
                                        "chatFlag" => true


                                    ],
                                    'title' => [
                                        'text' => '数据详情'
                                    ],
                                ],
                            ],
                        ]
                    ],
                ],
            ]
        ];
    }

}

