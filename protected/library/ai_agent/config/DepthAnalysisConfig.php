<?php

namespace common\library\ai_agent\config;

class DepthAnalysisConfig
{
    public array $config = [];

    // 主分析场景
    const TEAM_WORK_SITUATION = 'team_work_situation'; //团队工作情况
    const BUSINESS_OPPORTUNITY_CONVERSION_SITUATION = 'opportunity_conversion_situation_analysis'; //商机转换情况
    const SUBORDINATE_EMAIL = 'subordinate_email'; // 下属邮件


    // 子场景
    const CUSTOMER_EMAIL_SENDING_AND_RECEIVING_STATUS = 'customer_email_sending_and_receiving_status'; // 客户邮件收发趋势
    const WORKLOAD_STATISTICS = 'workload_statistics'; // 工作量统计
    const CUSTOMER_FOLLOW_UP_UPDATES = 'customer_follow_up_updates'; // 客户跟进动态

    const TOTAL_SALES_FUNNEL_ANALYSIS = 'total_sales_funnel_analysis'; //总的销售漏斗分析
    const SALES_FUNNEL_ANALYSIS = 'sales_funnel_analysis'; //员工销售漏斗分析
    const FAIL_REASONS_OVERALL = 'fail_reasons_overall'; // 商机输单原因分布-整体
    const FAIL_REASONS_EMPLOYEE = 'fail_reasons_employee'; //商机输单原因分布-员工

    const CUSTOMER_RESPONSE_ANALYSIS = 'customer_response_analysis'; //客户回复情况分析
    const SALESPERSON_RESPONSE_ANALYSIS = 'salesperson_response_analysis' ;//业务员回复情况分析


    const ANSWER_KEY_MAP = [
        self::TEAM_WORK_SITUATION => [
            '工作量统计分析' => self::WORKLOAD_STATISTICS ,
            '客户邮件收发情况分析' => self::CUSTOMER_EMAIL_SENDING_AND_RECEIVING_STATUS ,
            '客户跟进动态分析' => self::CUSTOMER_FOLLOW_UP_UPDATES,
            'Workload Statistical Analysis' => self::WORKLOAD_STATISTICS ,
            'Customer Email Communication Analysis' => self::CUSTOMER_EMAIL_SENDING_AND_RECEIVING_STATUS ,
            'Customer Follow-up Dynamic Analysis' => self::CUSTOMER_FOLLOW_UP_UPDATES,
            '業務量統計分析' => self::WORKLOAD_STATISTICS ,
            '顧客メールの送受信状況分析' => self::CUSTOMER_EMAIL_SENDING_AND_RECEIVING_STATUS ,
            '顧客フォローアップ動態分析' => self::CUSTOMER_FOLLOW_UP_UPDATES,
        ],
        self::BUSINESS_OPPORTUNITY_CONVERSION_SITUATION => [
            'Total Sales Funnel Analysis' => self::TOTAL_SALES_FUNNEL_ANALYSIS,
            'Sales Funnel Analysis' => self::SALES_FUNNEL_ANALYSIS,
            'Reasons-Overall' => self::FAIL_REASONS_OVERALL,
            'Reasons-Employee' => self::FAIL_REASONS_EMPLOYEE,
        ],
        self::SUBORDINATE_EMAIL => [
            'Customer Response Analysis' => self::CUSTOMER_RESPONSE_ANALYSIS,
            'Salesperson Response Analysis' => self::SALESPERSON_RESPONSE_ANALYSIS
        ]
    ];

    const ANSWER_FORMAT_MAP = [
        self::TEAM_WORK_SITUATION => [
            '团队整体总结' => '团队工作情况概况',
            '客户邮件收发情况分析' => '客户邮件收发情况',
            '工作量统计分析' => '工作量统计',
            '客户跟进动态分析' => '客户跟进动态',
        ],
        self::BUSINESS_OPPORTUNITY_CONVERSION_SITUATION => [
            'Overview Summary' => '商机转化情况概况',
            'Total Sales Funnel Analysis' => '销售漏斗',
            'Sales Funnel Analysis' => '不同员工的销售漏斗',
            'Reasons-Overall' => '整体商机输单原因',
            'Reasons-Employee' => '不同员工的商机输单原因',
        ],
        self::SUBORDINATE_EMAIL => [
            'Overview Summary' => '下属邮件概况',
            'Customer Response Analysis' => '客户回复情况分析',
            'Salesperson Response Analysis' => '业务员回复情况分析'
        ]
    ];

    public function __construct()
    {
        $this->initConfig();
    }

    public function getConfig()
    {
        return $this->config;
    }

    // 后续会有批量的场景 因此一次性拿出来
    public function initConfig()
    {

        $this->config = [
            self::TEAM_WORK_SITUATION => [
                'title' => \Yii::t('ai', '团队工作情况'),
                'desc' => \Yii::t('ai', '追踪团队进展提高效率'),
                'icon' => 'AiTeamAnalysis', // 前端展示使用，不在计算场景中使用
                'subKeyInfo' => [
                    self::CUSTOMER_EMAIL_SENDING_AND_RECEIVING_STATUS => [
                        'name' => \Yii::t('ai', '客户邮件收发情况'),
                        'report_key' => 'xs14',
                        'report_data_format_function' => ['filterEmptyColum'],
                    ],
                    self::WORKLOAD_STATISTICS => [
                        'name' => \Yii::t('ai', '工作量统计'),
                        'report_key' => 'xs1',
                        'report_data_format_function' => ['filterEmptyColum'],
                    ],
                    self::CUSTOMER_FOLLOW_UP_UPDATES => [
                        'name' => \Yii::t('ai', '客户跟进动态'),
                        'report_key' => 'xs12',
                        'report_data_format_function' => ['filterEmptyColum'],
                    ],
                ],
                'params' => [
                    [
                        'field' => 'common.date',
                        'label' => \Yii::t('ai', '分析时间区间'),
                        'type' => 'date',
                        'value' => [
                            'start' => date('Y-m-d', strtotime('-30 days')),
                            'end' => date('Y-m-d')
                        ]
                    ]
                ]
            ],


            self::BUSINESS_OPPORTUNITY_CONVERSION_SITUATION => [
                'title' => \Yii::t('ai', '商机转化情况'),
                'desc' => \Yii::t('ai', '挖掘潜在价值提高转化'),
                'icon' => 'AiRecord', // 前端展示使用，不在计算场景中使用
                'subKeyInfo' => [
                    self::TOTAL_SALES_FUNNEL_ANALYSIS => [
                        'name' => \Yii::t('ai', '销售漏斗'),
                        'report_key' => 'sjts2',
                        'filter_field_condition' => [
                            'company.create_user_name' => ['空', '未知', '无']
                        ],
                    ],
                    self::SALES_FUNNEL_ANALYSIS => [
                        'name' => \Yii::t('ai', '不同员工的销售漏斗'),
                        'report_key' => 'sjts3',
                        'filter_field_condition' => [
                            'company.create_user_name' => ['空', '未知', '无']
                        ],

                    ],
                    self::FAIL_REASONS_OVERALL => [
                        'name' => \Yii::t('ai', '整体商机输单原因'),
                        'report_key' => 'sj12',
                        'filter_field_condition' => [
                            'company.create_user_name' => ['空', '未知', '无']
                        ],
                    ],
                    self::FAIL_REASONS_EMPLOYEE => [
                        'name' => \Yii::t('ai', '不同员工的商机输单原因'),
                        'report_key' => 'sj16',
                        'filter_field_condition' => [
                            'company.create_user_name' => ['空', '未知', '无']
                        ],
                    ]
                ],
                'params' => [
                    [
                        'field' => 'opportunity.create_time',
                        'type' => 'date',
                        'label' => \Yii::t('ai', '创建日期'),
                        'value' => [
                            'start' => date('Y-m-d', strtotime('-30 days')),
                            'end' => date('Y-m-d')
                        ]
                    ],
                    [
                        'field' => 'opportunity.flow_id',
                        'type' => 'int',
//                        'value' => ""   // 让报表自动生成
                    ],
                ]
            ],

            self::SUBORDINATE_EMAIL => [
                'title' => \Yii::t('ai', '下属邮件分析'),
                'desc' => \Yii::t('ai', '洞察邮件动态优化策略'),
                'icon' => 'Email', // 前端展示使用，不在计算场景中使用
                'subKeyInfo' => [
                    self::CUSTOMER_RESPONSE_ANALYSIS => [
                        'name' => \Yii::t('ai', '客户回复情况分析'),
                        'report_key' => 'xs14',
                        'filter_field_condition' => [
                            'company.create_user_name' => ['空', '未知', '无']
                        ],
                    ],
                    self::SALESPERSON_RESPONSE_ANALYSIS => [
                        'name' => \Yii::t('ai', '业务员回复情况分析'),
                        'report_key' => 'xs14',
                        'filter_field_condition' => [
                            'company.create_user_name' => ['空', '未知', '无']
                        ],

                    ],
                ],
                'params' => [
                    [
                        'field' => 'common.date',
                        'type' => 'date',
                        'label' => \Yii::t('ai', '邮件收发时间区间'),
                        'value' => [
                            'start' => date('Y-m-d', strtotime('-30 days')),
                            'end' => date('Y-m-d')
                        ]
                    ],
                    [
                        'field' => 'mail.expose_flag',
                        'type' => 'int',
                        'value' => 0   // 让报表自动生成
                    ],
                ]
            ]
        ];
    }






}