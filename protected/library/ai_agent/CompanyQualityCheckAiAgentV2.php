<?php

namespace common\library\ai_agent;

use AiQualityCheckCompanyModel;
use common\library\ai\translate\TranslateService;
use common\library\ai_agent\api\AIClient;
use common\library\ai_agent\company_quality_check\AiQualityCheckAgentAnalysis;
use common\library\ai_agent\company_quality_check\AiQualityCheckAgentAnalysisList;
use common\library\ai_agent\company_quality_check\AiQualityCheckChatJourney;
use common\library\ai_agent\company_quality_check\AiQualityCheckChatJourneyList;
use common\library\ai_agent\company_quality_check\AiQualityCheckCompany;
use common\library\ai_agent\company_quality_check\AiQualityCheckCompanyConversation;
use common\library\ai_agent\company_quality_check\AiQualityCheckCompanyConversationList;
use common\library\ai_agent\company_quality_check\AiQualityCheckCompanyJourney;
use common\library\ai_agent\company_quality_check\AiQualityCheckCompanyJourneyList;
use common\library\ai_agent\company_quality_check\AiQualityCheckCompanyList;
use common\library\ai_agent\company_quality_check\AiQualityCheckCompanyTags;
use common\library\ai_agent\company_quality_check\AiQualityCheckStickingPoint;
use common\library\ai_agent\company_quality_check\AiQualityCheckStickingPointList;
use common\library\ai_agent\company_quality_check\Helper;
use common\library\ai_agent\exception\AiAgentException;
use common\library\ai_agent\api\AiStreamClient;
use common\library\ai_agent\record\AiServiceProcessRecord;
use common\library\ai_portrait_analysis\AiPortraitAnalysisApi;
use common\library\ai_portrait_analysis\AiPortraitAnalysisDetail;
use common\library\ai_portrait_analysis\AiPortraitAnalysisDetailApi;
use common\library\ai_portrait_analysis\AiPortraitAnalysisDetailFilter;
use common\library\ai_portrait_analysis\AiPortraitAnalysisFilter;
use common\library\ai_service\AiServiceConstant;
use common\library\CommandRunner;
use common\library\customer_v3\customer\CustomerList;
use common\library\mail\MailList;
use common\library\privilege_v3\PrivilegeService;
use common\library\sns\customer\CustomerContactHelper;
use common\library\sns\customer\UserCustomerContactMessageList;
use common\library\sns\message\MessageBuilder;
use common\library\social_auth\Constant;
use common\library\todo\TodoConstant;
use common\library\util\PgsqlUtil;
use common\library\util\SqlBuilder;
use common\models\client\AiQualityCheckStickingPointModel;
use common\modules\app\Constants;
use xiaoman\orm\database\data\JsonColumn;
use function Clue\StreamFilter\fun;
use function GuzzleHttp\Psr7\str;
use function GuzzleHttp\Psr7\uri_for;

class CompanyQualityCheckAiAgentV2 extends BaseAiAgent
{
    public string $startTime;

    public string $endTime;

    public string $dateTime;

    public int $companyId;

    /**
     * 批量/增量,影响翻译和生成聊天旅程
     * @var string $mode
     */
    public string $mode;

    //批量
    public const QUALITY_CHECK_MODE_BATCH = "batch";

    //增量
    public const QUALITY_CHECK_MODE_SINGLE = "single";

    public string $lastDate = "";

    public bool $fromAuto = false;

    /**
     * 添加在{@link tbl_ai_service_process_record}中的journey_id
     * 慎重修改,在代码中显式修改后,显式移除,避免污染
     * @use self::addJourneyIdInProcessParams()
     * @use self::removeJourneyIdInProcessParams()
     */
    private array $journeyIdsInProcessParams = [];

    private string $recordUserId;

    private string $recordCustomerId;

    private string $recordAgentId;

    private string $translateDate;

    public const QUALITY_CHECK_FUNCTION_PROCESS_DATE_TASK = 'processDateTask';

    public const QUALITY_CHECK_FUNCTION_PROCESS_STREAM_TASK = 'processStreamTask';

    //谈单监测V3 Multi Agents
    public const QUALITY_CHECK_FUNCTION_V3_PROCESS_DATE_TASK = 'processDateTaskV3';

    //谈单监测V3 后台监测
    public const QUALITY_CHECK_FUNCTION_BACKSTAGE_PROCESS_DATE_TASK = 'processBackstageTask';

    public const QUALITY_CHECK_EXTERNAL_FUNCTION_PROCESS_STICKING_POINT = 'processStickingPoint'; //扩展模块-买家问题

    public const QUALITY_CHECK_EXTERNAL_FUNCTION_PROCESS_TAG = 'processTag'; //扩展模块-新询盘标签

    public const QUALITY_CHECK_EXTERNAL_FUNCTION_ANALYSIS = 'analysis'; //扩展模块-分析模块

    public const QUALITY_CHECK_PROCESS_FUNCTION_LIST = [
        self::QUALITY_CHECK_FUNCTION_PROCESS_DATE_TASK,
        self::QUALITY_CHECK_FUNCTION_PROCESS_STREAM_TASK,
        self::QUALITY_CHECK_EXTERNAL_FUNCTION_PROCESS_STICKING_POINT,
        self::QUALITY_CHECK_FUNCTION_V3_PROCESS_DATE_TASK,
        self::QUALITY_CHECK_FUNCTION_BACKSTAGE_PROCESS_DATE_TASK,
    ];

    //模块映射
    public const FUNCTION_TO_MODULES_MAP = [
        self::QUALITY_CHECK_FUNCTION_PROCESS_DATE_TASK => [
            'pretreatment',//预处理模块
            'keywordAndSummaryV2',//打标模块 & 总结模块
            'updateCompanyV2', //更新tbl_ai_quality_check_company
        ],
        self::QUALITY_CHECK_FUNCTION_PROCESS_STREAM_TASK => [
            'contactAnalyze',//谈单分析模块(stream)
        ],
        //v3流程
        self::QUALITY_CHECK_FUNCTION_V3_PROCESS_DATE_TASK => [
            'buildChatRecords',//数据处理
            'chatUnderstandAndAnnotation', //Event Annotation Agent(关键事件标注)
//            'sentimentAnalysis', //Sentiment Analysis Agent(情感分析)
//            'journeyGeneration', //Journey Generation Agent(生成聊天旅程)
            'journeyGenerationV2',
            'contentDisplay', //Content Display Agent(生成聊天旅程页面展示内容)
        ],
        //后台监测池流程
        self::QUALITY_CHECK_FUNCTION_BACKSTAGE_PROCESS_DATE_TASK => [
            'buildChatRecords',//数据处理
            'chatUnderstandAndAnnotation', //Event Annotation Agent(关键事件标注)
//            'sentimentAnalysis', //Sentiment Analysis Agent(情感分析)
//            'journeyGeneration', //Journey Generation Agent(生成聊天旅程)
            'journeyGenerationV2',
            'createBackStageJourney', //后台监测池需要生成旅程
        ]
    ];

    /*谈单监测V3,子Agent列表*/
    /****************************************************/
    public const QC_EVENT_ANNOTATION_AGENT_ID = AiAgentConstants::QC_EVENT_ANNOTATION_AGENT_ID;

    public const QC_OPPORTUNITY_TRACKING_AGENT_ID = AiAgentConstants::QC_OPPORTUNITY_TRACKING_AGENT_ID;

    public const QC_SENTIMENT_ANALYSIS_AGENT_ID = AiAgentConstants::QC_SENTIMENT_ANALYSIS_AGENT_ID;

    public const QC_ISSUE_TRACKING_AGENT_ID = AiAgentConstants::QC_ISSUE_TRACKING_AGENT_ID;

    public const QC_JOURNEY_GENERATION_AGENT_ID = AiAgentConstants::QC_JOURNEY_GENERATION_AGENT_ID;

    public const QC_CONTENT_DISPLAY_AGENT_ID = AiAgentConstants::QC_CONTENT_DISPLAY_AGENT_ID;

    public const QC_SUGGESTION_AGENT_ID = AiAgentConstants::QC_SUGGESTION_AGENT_ID;

    public const QC_BUSINESS_STATUS_ID = AiAgentConstants::QC_BUSINESS_STATUS_ID;

    //fixme 兼容谈单监测v2的打标,临时使用
    public const QC_V2_EVENTS_AGENT_ID = AiAgentConstants::QC_V2_EVENTS_AGENT_ID;
    /****************************************************/

    //转换渠道名称
    public const CONVERT_SNS_TYPE_TO_STD_TYPE_MAP = [
        "tm" => "Alibaba TM",
        "whatsapp" => "WhatsApp",
        "whatsapp_business" => "WhatsApp Business",
        "facebook" => "Facebook Messenger",
        "facebook_page" => "Facebook Page",
        "instagram" => "Instagram",
        "mail" => "Email",
    ];

    //情感分析等级
    public const SENTIMENT_LEVEL_POSITIVE = "positive";

    public const SENTIMENT_LEVEL_NEUTRAL = "neutral";

    public const SENTIMENT_LEVEL_NEGATIVE = "negative";

    public const SENTIMENT_LEVEL_NOT_RESPONSE = "no_response";

    public const OCCURRED_EVENT_NAME_TO_ENUM_MAP = [
        "New inquiry" => 1,
        "Sample request" => 2,
        "Product quotation" => 3,
        "Logistics quotation" => 4,
        "Proforma invoice" => 5,
        "Customer payment" => 6,
        "Order in production update" => 7,
        "Order in transit update" => 8,
        "Product arrival" => 9,
        "Repurchase intention" => 10,
        "Order intention" => 11,
        "Product Shipment" => 12,
        "Sticking" => 13,
    ];

    public const OCCURRED_EVENT_CN_TO_ENG_MAP = [
        "新询盘" => "New inquiry",
        "样品申请" => "Sample request",
        "产品报价" => "Product quotation",
        "运费报价" => "Logistics quotation",
        "PI" => "Proforma invoice",
        "客户付款" => "Customer payment",
        "生产进度更新" => "Order in production update",
        "运输信息更新" => "Order in transit update",
        "客户收货" => "Product arrival",
        "复购意向" => "Repurchase intention",
        "订单意向" => "Order intention",
        "产品发货" => "Product Shipment",
        "谈单卡点" => "Sticking"
    ];

    //todo 需要适配多语言
    public const OCCURRED_EVENT_ENUM_TO_NAME_MAP = [
        1 => "新询盘",
        2 => "样品申请",
        3 => "产品报价",
        4 => "运费报价",
        5 => "PI",
        6 => "客户付款",
        7 => "生产进度更新",
        8 => "运输信息更新",
        9 => "客户收货",
        10 => "复购意向",
        11 => '订单意向',
        12 => '产品发货',
        13 => '谈单卡点'
    ];

    public function __construct(int $clientId, int $userId)
    {
        $this->useGreyVersion = true;
        parent::__construct($clientId, $userId);
        //reset system prompt
        if (!empty($this->systemPrompt) && !empty($this->userPrompt)) {
            $systemPromptJson = json_decode($this->systemPrompt, true);
            $userPromptJson = json_decode($this->userPrompt, true);

            if ($systemPromptJson != null && $userPromptJson != null) {
                $this->systemPromptMap = $systemPromptJson;
                $this->userPromptMap = $userPromptJson;
                $this->systemPrompt = $this->systemPromptMap['default'] ?? '';
                $this->userPrompt = $this->userPromptMap['default'] ?? '';
            }
        }
    }


    public function getAgentSceneType(): int
    {
        return \AiAgent::AI_AGENT_SCENE_TYPE_COMPANY_QUALITY_CHECK;
    }

    public function process(array $params = [], string $function = '')
    {
        //流式调用
        if(empty($function)){
            $function = self::QUALITY_CHECK_FUNCTION_PROCESS_STREAM_TASK;
            $params = $this->requestParams;
        }

        if(in_array($function , self::QUALITY_CHECK_PROCESS_FUNCTION_LIST)){
            return call_user_func([$this, $function], $params);
        }else{
            \LogUtil::error("AiCompanyQualityCheck Error,invalid arguments,function:{$function}");
            throw new AiAgentException("谈单检测参数异常");
        }
    }

    /**
     * 处理谈单分析流式生成
     * @param array $params
     * @return AiAgentProcessResponse
     */
    private function processStreamTask(array $params): AiAgentProcessResponse
    {
        if(empty($params) || empty($params['companyId'])){
            throw new AiAgentException("生成谈单分析失败,参数异常");
        }

        $this->companyId = $params['companyId'];

        $language = \Yii::app()->language ?? 'zh-CN';
        $languageKey = "qc.generate.analysis.key.{$language}";
        $systemPrompt = $this->systemPromptMap[$languageKey] ?? ($this->systemPromptMap['qc.generate.analysis.key'] ?? '');

        $llmModel = $this->promptConfig['qc.generate.analysis.key'] ?? '';
        $question = '';

        //最新的聊天旅程时间,最新的谈单分析时间
        $latestContactTime = Helper::getLatestContactTimeByCompanyId($this->clientId, $this->companyId);
        $latestContactTime = empty($latestContactTime) ? '1970-01-01' : date('Y-m-d', strtotime($latestContactTime));

        $latestAnalyzeInfo = Helper::getLatestAnalyzeInfoByCompanyId($this->clientId, $this->companyId);
        $latestAnalyzeTime = empty($latestAnalyzeInfo['analyze_time']) ? '1970-01-01' : date('Y-m-d', strtotime($latestAnalyzeInfo['analyze_time']));

        if (strcmp($latestContactTime, $latestAnalyzeTime) <= 0) {
            //理论不可能
            throw new AiAgentException("生成谈单分析失败");
        }

        //查询最近一个月
        $low = date('Y-m-d 00:00:00', strtotime('-30 day', strtotime($latestContactTime)));
        $high = date('Y-m-d 23:59:59', strtotime($latestContactTime));

        //最近一个月的聊天旅程
        $journeyList = new AiQualityCheckCompanyJourneyList($this->clientId, $this->companyId);
        $journeyList->setCompanyId($this->companyId);
        $journeyList->setStartContactDate($low);
        $journeyList->setEndContactDate($high);
        $ret = $journeyList->find();

        //对应的company_conversation和agent_analysis
        $number = 1;
        foreach ($ret as $journey)
        {
            $journeyId = $journey['journey_id'] ?? '';
            if(empty($journey)) continue;

            $conversationList = new AiQualityCheckCompanyConversationList($this->clientId);
            $conversationList->setCompanyId($this->companyId);
            $conversationList->setJourneyId([$journeyId]);
            $conversations = $conversationList->find();
            $snsTypes = array_column($conversations, 'sns_type');
            $snsTypes = implode(",", $snsTypes);

            $agentAnalysisList = new AiQualityCheckAgentAnalysisList($this->clientId);
            $agentAnalysisList->setCompanyId($this->companyId);
            $agentAnalysisList->setJourneyId($journeyId);
            $agentAnalysisList->setAgentId(self::QC_JOURNEY_GENERATION_AGENT_ID);
            $agentAnalysis = $agentAnalysisList->find();
            if(empty($agentAnalysis)) continue;
            $agentAnalysis = array_values($agentAnalysis)[0];
            $agentAnalysis = json_decode($agentAnalysis['analyze_result'] ?? '', true);
            $summary = $agentAnalysis['summary'] ?? "";

            $date = $journey['contact_date'] ?? "";

            $userPrompt = $this->userPromptMap['qc.generate.analysis.not.contain.name.key'] ?? '';
            $questionParams = [
                'number' => $number++,
                'sns_type' => $snsTypes,
                'date' => $date,
                'summary' => $summary,
            ];
            $text = $this->replacePlaceholders($userPrompt, $questionParams);
            $question = $question . PHP_EOL . $text;
        }

        $gptResponse = $this->callByParams(0 , $question , $systemPrompt , $llmModel , 0.0 , true);
        return $this->initAiAgentProcessResponse($gptResponse, '', AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT);
    }

    public function sseResponse(AiAgentProcessResponse $aiAgentProcessResponse)
    {
        parent::sseResponse($aiAgentProcessResponse);
        //记录流式结果
        $journeyList = new AiQualityCheckCompanyJourneyList($this->clientId,$this->companyId);
        $journeyList->setCompanyId($this->companyId);
        $journeyList->setOrderBy('contact_date');
        $journeyList->setOrder('desc');
        $journeyList->setLimit(1);
        $ret = $journeyList->find();
        if(!empty($ret)) {
            $journey = array_values($ret)[0];
            $journeyId = $journey['journey_id'];
            $analysis = $this->llmService->getGptResponse();
            $analysis = pg_escape_string($analysis);
            $analyzeTime = date('Y-m-d', time());

            $pg = \PgActiveRecord::getDbByClientId($this->clientId);
            $sql = "update tbl_ai_quality_check_company_journey set analysis = '{$analysis}' , analyze_time = '{$analyzeTime}' where journey_id = {$journeyId}";
            $pg->createCommand($sql)->execute();
        }
    }


    /**
     * 处理定时任务
     * @param $param
     * @return void
     */
    private function processDateTask($params): void
    {
        \LogUtil::info("AiCompanyQualityCheck,执行谈单监测",['params' => $params,'client_id' => $this->clientId]);
        if (empty($params) || empty($params['company_id']) || empty($params['start_time']) || empty($params['end_time'])) {
            \LogUtil::error("AiCompanyQualityCheck-{$this->clientId},参数错误,无效的params输入");
            throw new AiAgentException("谈单检测执行失败,参数异常");
        }

        $this->dateTime = date('Y-m-d', strtotime($params['start_time']));
        $this->companyId = $params['company_id'];
        $this->startTime = $params['start_time'];
        $this->endTime = $params['end_time'];

        try {
            $modules = self::FUNCTION_TO_MODULES_MAP[self::QUALITY_CHECK_FUNCTION_PROCESS_DATE_TASK] ?? [];

            //执行各模块
            $runContext = [];
            foreach ($modules as $module) {
                \LogUtil::info("AiCompanyQualityCheck-{$this->clientId}-{$this->companyId}-{$this->dateTime},执行模块{$module}");
                [$runContext, $needBreak] = call_user_func([$this, $module], $runContext);
                //只接管正常退出,异常情况直接抛异常清数据重跑
                //正常退出,如果journey已经有记录,可在退出前手动清除
                if ($needBreak) {
                    \LogUtil::info("AiCompanyQualityCheck-{$this->clientId}-{$this->companyId}-{$this->dateTime},跳过执行,模块:{$module},msg:{$runContext['error']}");
                    return;
                }
            }

            \LogUtil::info("AiCompanyQualityCheck-{$this->clientId}-{$this->companyId}-{$this->dateTime},执行任务" . self::QUALITY_CHECK_FUNCTION_PROCESS_DATE_TASK ."完毕");
        } catch (\Throwable $exception) {
            \LogUtil::error("AiCompanyQualityCheck-{$this->clientId}-{$this->companyId}-{$this->dateTime} fail,error : {$exception->getTraceAsString()}");
            print_r($exception->getTraceAsString());

            //删除当日的聊天旅程
            $pg = \PgActiveRecord::getDbByClientId($this->clientId);
            $sql = "SELECT journey_id FROM tbl_ai_quality_check_chat_journey WHERE client_id = {$this->clientId} AND contact_time >= '{$this->startTime}' AND contact_time <= '{$this->endTime}'";
            $needDeleteJourneyIds = $pg->createCommand($sql)->queryColumn();
            if (!empty($needDeleteJourneyIds)) {
                $deleteJourneyIds = implode(",", $needDeleteJourneyIds);
                \LogUtil::info("AiCompanyQualityCheck-{$this->clientId}-{$this->companyId}-{$this->dateTime} 删除聊天旅程,journey_id: {$deleteJourneyIds}");
                $sql = "DELETE FROM tbl_ai_quality_check_chat_journey WHERE client_id = {$this->clientId} AND journey_id IN ($deleteJourneyIds)";
                $pg->createCommand($sql)->execute();
            }

            //TODO 群监控告警
            //TODO 实现自动重试
            throw new AiAgentException("谈单检测执行失败,exception:{$exception->getMessage()}");
        }
        \LogUtil::info("AiCompanyQualityCheck,执行谈单监测执行完成",['params' => $params,'client_id' => $this->clientId]);

    }

    private function updateAiQualityCheckJourney($updateData): array
    {
        $journeyId = $updateData['journeyId'];
        $journey = new AiQualityCheckChatJourney($journeyId);

        if (!empty($updateData['context'] ?? '')) {
            $journey->context = $updateData['context'];
        }

        if (!empty($updateData['summaryAndStuckPoints'] ?? '')) {
            $journey->contact_summary = empty($updateData['summaryAndStuckPoints']['summary'] ?? '[]') ? '[]' : json_encode($updateData['summaryAndStuckPoints']['summary']);
            $journey->sticking_point = empty($updateData['summaryAndStuckPoints']['sticking_point'] ?? '') ? '[]' : json_encode($updateData['summaryAndStuckPoints']['sticking_point']);
        }

        if (!empty($updateData['contactProgressSummary'] ?? '')) {
            $journey->contact_progress_summary = json_encode($updateData['contactProgressSummary']);
        }

        if (!empty($updateData['contactStage'] ?? 0)) {
            $journey->contact_stage = $updateData['contactStage'];
        }

        $journey->save();
        \LogUtil::info("CompanyQualityCheckAiAgentV2 更新tbl_ai_quality_check_chat_journey成功, journey_id : {$journeyId}, client_id : {$this->clientId}, userId : {$this->userId}");

        return [$updateData, false];
    }

    private function formatResponseAnswer(string $answer): string
    {
        //直接解析json
        $summary = json_decode($answer, true);
        if ($summary != null) {
            return $answer;
        }

        //正则匹配
        if (preg_match('/```json\s*(\{.*?\})\s*```/', $answer, $matches)) {
            return $matches[1];
        }
        if (preg_match('/```\s*(\{.*?\})\s*```/', $answer, $matches)) {
            return $matches[1];
        }

        //暴力匹配
        if (str_starts_with($answer, '```json') && str_ends_with($answer, '```')) {
            //去除```json
            $answer = substr($answer, strlen("```json"), strlen($answer));
            //去除结尾的```
            $answer = substr($answer, 0, strlen($answer) - 3);
            if (json_decode($answer) != null) {
                return $answer;
            }
        } else if (str_starts_with($answer, '```') && str_ends_with($answer, '```')) {
            //去除```json
            $answer = substr($answer, strlen("```"), strlen($answer));
            //去除结尾的```
            $answer = substr($answer, 0, strlen($answer) - 3);
            if (json_decode($answer) != null) {
                return $answer;
            }
        }

        //无法匹配
        return '';
    }

    public function makeAgentProcessParams(): array
    {
        //Implement makeAgentProcessParams() method.
        $agentProcessParams = [
            'client_id' => $this->clientId,
            'user_id' => $this->userId ?? '',
            'company_id' => $this->companyId ?? '',
            'start_time' => $this->startTime ?? '',
            'end_time' => $this->endTime ?? '',
        ];

        if (!empty($this->journeyIdsInProcessParams)) {
            $agentProcessParams['journey_id'] = $this->journeyIdsInProcessParams;
        }

        if (!empty($this->recordUserId)) {
            $agentProcessParams['record_user_id'] = $this->recordUserId;
        }

        if (!empty($this->recordCustomerId)) {
            $agentProcessParams['record_customer_id'] = $this->recordCustomerId;
        }

        if (!empty($this->recordAgentId)) {
            $agentProcessParams['record_agent_id'] = $this->recordAgentId;
        }

        return $agentProcessParams;
    }

    private function addJourneyIdInProcessParams($_journeyIds): void
    {
        if (empty($this->journeyIdsInProcessParams)) {
            $this->journeyIdsInProcessParams = $_journeyIds;
        }
        return;
    }

    private function removeJourneyIdInProcessParams(): void
    {
        if (!empty($this->journeyIdsInProcessParams)) {
            $this->journeyIdsInProcessParams = [];
        }
        return;
    }

    public function loadHistoryMessage()
    {
        //只有翻译需要使用历史记录
        if(empty($this->recordAgentId) || $this->recordAgentId != self::QC_CONTENT_DISPLAY_AGENT_ID) {
            return [];
        }

        //高亮不需要使用历史记录
        if($this->maxHistoryLength == 0) {
            return [];
        }

        $userId = $this->recordUserId ?? 0;
        $customerId = $this->recordCustomerId ?? 0;
        $contactDate = $this->translateDate ?? "";

        if(empty($userId) || empty($customerId) || empty($contactDate)) {
            return [];
        }

        $range = 30;
        $limit = 3;

        //寻找最近30天的3次翻译结果
        //使用user-assistant对话方式
        $analysisList = new AiQualityCheckAgentAnalysisList($this->clientId);
        $analysisList->setCompanyId($this->companyId);
        $analysisList->setUserId($userId);
        $analysisList->setCustomerId($customerId);
        $analysisList->setStartContactDate(date('Y-m-d',strtotime("-{$range} day",strtotime($contactDate))));
        $analysisList->setEndContactDate(date('Y-m-d',strtotime('-1 day',strtotime($contactDate))));
        $analysisList->setAgentId(self::QC_JOURNEY_GENERATION_AGENT_ID);
        $analysisList->setFields(['analyze_result','contact_date']);
        $analysisList->setOrderBy('contact_date');
        $analysisList->setOrder('desc');
        $analysisList->setLimit($limit);
        $summaryList = $analysisList->find();
        $dateToSummaryList = array_column($summaryList, 'analyze_result', 'contact_date');
        $dateToSummaryList = array_map(function ($item) {
            $summary = json_decode($item, true);
            return $summary['summary'] ?? "";
        }, $dateToSummaryList);

        $analysisList->setAgentId(self::QC_CONTENT_DISPLAY_AGENT_ID);
        $translationList = $analysisList->find();
        $dateToTranslationList = array_column($translationList, 'analyze_result', 'contact_date');
        $dateToTranslationList = array_map(function ($item) {
            $translation = json_decode($item, true);
            return $translation['translation'] ?? "";
        },$dateToTranslationList);

        $res = [];
        foreach ($dateToSummaryList as $date => $summary)
        {
            if(empty($dateToTranslationList[$date] ?? "")) {
                continue;
            }
            $res[] = [
                'role' => AiAgentConstants::AI_AGENT_ROLE_USER,
                'content' => $summary,
            ];
            $res[] = [
                'role' => AiAgentConstants::AI_AGENT_ROLE_ASSISTANT,
                'content' => $dateToTranslationList[$date] ?? "",
            ];
        }

        return $res;
    }

    /**
     * @throws AiAgentException
     */
    private function callByParams($referId, $question, $systemPrompt, $llmModel, $temperature = 0.0, $stream = false, $jsonObject = false)
    {
        $this->systemPrompt = $systemPrompt;
        $this->llmModel = $llmModel;
        if ($stream) {
            $this->llmService = new AiStreamClient();
            $this->stream = true;
        }
        $this->llmService->setModel($llmModel);
        $this->llmService->setTemperature(0.1);

        //设置systemPrompt,userPrompt,model,temperature
        //save process record
        $this->referId = $referId;
        $processRecord = $this->saveProcessRecord($question);
        $llmContext = [
            'record_id' => $processRecord->record_id,
        ];
        $this->setContext($llmContext);

        if ($temperature) {
            $this->llmService->setTemperature($temperature);
        }

        try{
            if($jsonObject) {
                $this->llmService->setResponseFormat(['type' => 'json_object']);
            }
            $question = mb_strcut($question, 0, 75000);
            return $this->callLlm($question);
        }catch (\Throwable $exception){
            if(str_starts_with($llmModel,'qwen')) {
                \LogUtil::error("AiCompanyQualityCheck-{$this->clientId}-{$this->companyId} 调用qwen失败,使用deepseek重试,exception:{$exception->getTraceAsString()}");
                $this->llmModel = AiAgentConstants::AI_MODEL_VOLCANO_DEEPSEEK_V3;
                $this->llmService->setModel(AiAgentConstants::AI_MODEL_VOLCANO_DEEPSEEK_V3);
                return $this->callLlm($question);
            }
            if(str_starts_with($llmModel,'deepseek')) {
                $this->llmModel = AIClient::AZURE_OPENAI_GPT_FOUR_O_LATEST;
                $this->llmService->setModel(AIClient::AZURE_OPENAI_GPT_FOUR_O_LATEST);
                return $this->callLlm($question);
            }
        } finally {
            if($jsonObject){
                $this->llmService->setResponseFormat([]);
            }
        }
    }

    public function getFormattedMessages($messageList, $snsType = '')
    {
        $formattedMessages = '';

//        if (count($messageList) > 0) {
//            if (!empty($messageList[0]['send_time'])) {
//                $this->recentContactTime = $messageList[0]['send_time'];
//            }
//        }
        $channelMap = [
            'whatsapp' => Constant::CHANNEL_TYPE_WHATSAPP,
            'tm' => Constant::CHANNEL_TYPE_TM,
            'facebook_page' => Constant::CHANNEL_TYPE_FACEBOOK,
            'instagram' => Constant::CHANNEL_TYPE_INSTAGRAM,
            'whatsapp_business' => Constant::CHANNEL_TYPE_WABA,
        ];
        foreach ($messageList as $message) {
            if (!isset($channelMap[$snsType])) {
                continue;
            }
            $messageBody = MessageBuilder::loadMessage($message['type'], $message['body']);
//            $messageBody = MessageBody::make($message['type'], $message['body']);
            // 根据send_type决定是Customer还是Salesman
            $senderType = $message['send_type'] == 1 ? 'Salesperson' : 'Customer';

            $body = trim($messageBody->getPromptText($channelMap[$snsType]));
            if (empty($body)) {
                continue;
            }
            // 组装输出的字符串
            $formattedMessages .= $senderType . ':  ' . $body . PHP_EOL;
        }

        return $formattedMessages;
    }

    public function checkAigcRiskInfo(string $content, int $role, int $historyId, int $processRecordId = 0, string $contentLanguage = AiAgentConstants::AI_ICBU_RISK_LANGUAGE_ZH)
    {
        \LogUtil::info("AiCompanyQualityCheck-{$this->clientId}-{$this->companyId},跳过风控");
        return;
    }


    /**
     * 预处理模块
     * @param $runContext array context
     * @return array context
     */
    public function pretreatment(array $runContext): array
    {
        //幂等处理
        if ($this->checkNeedSkip()) {
            $runContext['error'] = "{$this->dateTime}已生成聊天旅程";
            return [$runContext, true];
        }

        $runContext['formatInfo'] = [];

        //获取往来邮件 + 格式化
        $emailChatInfo = Helper::getEmailChatInfoV2($this->clientId, $this->companyId, $this->startTime, $this->endTime);
        $formatMailInfo = $this->formatMailQualityCheckInfo($emailChatInfo);
        $runContext['formatInfo'] = array_merge($runContext['formatInfo'], $formatMailInfo);

        //拉取Sns聊天记录 + 格式化
        $snsChatInfo = Helper::getSnsChatInfo($this->clientId, $this->companyId, $this->startTime, $this->endTime);
        $formatSnsInfo = $this->formatSnsQualityCheckInfo($snsChatInfo);
        $runContext['formatInfo'] = array_merge($runContext['formatInfo'], $formatSnsInfo);

        //插表tbl_ai_quality_check_chat_journey
        $runContext['formatInfo'] = array_values($runContext['formatInfo']);

        if (!empty($runContext['formatInfo'])) {
            foreach ($runContext['formatInfo'] as &$item) {
                $journey = new AiQualityCheckChatJourney();
                $journey->company_id = $this->companyId;
                $journey->client_id = $this->clientId;
                $journey->refer_ids = PgsqlUtil::formatArray($item['referIds']);
                $journey->customer_ids = PgsqlUtil::formatArray([$item['customerId']]);
                $journey->customer_id = $item['customerId'];
                $journey->user_id = $item['userId'];
                $journey->sns_type = $item['snsType'];
                $journey->context = iconv("UTF-8" , "UTF-8//IGNORE" ,mb_convert_encoding($item['content'] ?? '' , 'UTF-8' , array('UTF-8','ISO-8859-1','GBK','CP1252','Windows-1252')));
                $journey->contact_time = $item['recentContactTime'];
                $journey->contact_begin_time = $item['firstContactTime'];
                $journey->save();
            }
        } else {
            $runContext['error'] = "没有聊天信息&往来邮件";
            return [$runContext, true];
        }

        //release memory
        $runContext['pairs'] = array_map(function ($item) {
            $userId = $item['userId'];
            $customerId = $item['customerId'];
            return "{$userId}-{$customerId}";
        }, $runContext['formatInfo']);
        unset($runContext['formatInfo']);


        return [$runContext, false];
    }

    public function checkNeedSkip(): bool
    {
        $low = date('Y-m-d 00:00:00', strtotime($this->startTime));
        $high = date('Y-m-d 23:59:59', strtotime($this->startTime));

        $qcJourneyList = new AiQualityCheckChatJourneyList($this->clientId, $this->userId);
        $qcJourneyList->setCompanyId($this->companyId);
        $qcJourneyList->setBeginTime($low);
        $qcJourneyList->setEndTime($high);
        $count = $qcJourneyList->count();

        return $count > 0;
    }

    /**
     * 格式化"往来邮件"内容
     * @param $emailChatInfo
     * @return array
     */
    public function formatMailQualityCheckInfo(array $emailChatInfo): array
    {
        $res = [];
        if(empty($emailChatInfo)) return $res;

        $pattern = '/[\s\S]*?(?=^[-_\w]*\s*From:\s*|----- Original ------|^[-_\w]*\s*发件人:\s*|^[-_\w]*\s*发件人\s*|^[-_\w]*\s*From\s*|^[-_\w]*\s*在[^\n]*写道|[\s\S]{0,100}-{5,}[\s\S]*原始[\s\S]{0,50}-{5,})/';

        //对动态列表按照user_id + customer_id进行切分
        $mailInfoWithPairMap = [];
        foreach ($emailChatInfo as $item)
        {
            $mailId = $item['mailId'] ?? 0;
            $receiveTime = $item['receiveTime'] ?? '';
            $mailContent = $item['plainText'] ?? '';
            $mailContent = preg_replace('/\n\s*\n/', PHP_EOL . PHP_EOL, $mailContent);
            // 不正则,直接根据1000截断
            $mailContent = substr($mailContent, 0, 1000);

            $role = ($item['mailType'] == \Mail::MAIL_TYPE_SEND) ? 'Salesperson' : 'Customer';
            //附件列表
            $attachment = !empty($item['attachment']) ? "[" . implode("] [",$item['attachment']) . "]" : '';
            $formatContent = <<<EOF
$role: {$receiveTime}
FROM: {$item['sender']}
TO: {$item['receiver']}
SUBJECT: "{$item['subject']}"
---CONTENT START---
{$mailContent}
---CONTENT END---
EOF;
            if(!empty($attachment)) {
                $formatContent .= PHP_EOL . <<<EOF
---ATTACHMENT START---
{$attachment}
---ATTACHMENT END---
EOF;
            }

            $userId = $item['userId'];
            $customerId = $item['customerId'];

            $pair = "{$userId}-{$customerId}";
            $pre = $mailInfoWithPairMap[$pair] ?? [];

            if (empty($pre)) {
                $mailInfoWithPairMap[$pair] = [
                    'mailId' => [$mailId],
                    'receiveTime' => $receiveTime,
                    'firstTime' => $receiveTime,
                    'content' => $formatContent,
                ];
            }else{
                $pre['content'] .= PHP_EOL . $formatContent;
                $pre['receiveTime'] = max($pre['receiveTime'], $receiveTime);
                $pre['firstTime'] = min($pre['firstTime'], $receiveTime);
                $pre['mailId'] = array_values(array_unique(array_merge($pre['mailId'],[$mailId])));
                $mailInfoWithPairMap[$pair] = $pre;
            }
        }

        foreach ($mailInfoWithPairMap as $pair => $info)
        {
            $ss = explode("-", $pair);
            $userId = intval($ss[0]);
            $customerId = intval($ss[1]);
            $res[] = [
                'snsType' => \AiQualityCheckChatJourneyModel::SNS_TYPE_MAIL,
                'referIds' => $info['mailId'] ?? [],
                'content' => $info['content'] ?? '',
                'recentContactTime' => $info['receiveTime'] ?? '',
                'firstContactTime' => $info['firstTime'] ?? '',
                'customerId' => $customerId,
                'userId' => $userId,
            ];
        }

        return $res;
    }

    public function formatSnsQualityCheckInfo(array $snsChatInfo): array
    {
        $res = [];

        //按user_contact_id聚合
        foreach ($snsChatInfo as $snsType => $userContactInfo) {
            if (empty($userContactInfo)) continue;
            //所有的user_contact_id
            foreach ($userContactInfo as $userContactId => $userContactMessageList) {
                if (empty($userContactMessageList) || empty($userContactMessageList['message'] || empty($userContactMessageList['customerIds']))) continue;
                $messageList = $userContactMessageList['message'];
                $customerId = $userContactMessageList['customerId'];
                $userId = $userContactMessageList['userId'];
                $message = $this->getFormattedMessages($messageList, $snsType);
                if(empty(trim($message))){
                    continue;
                }
                $recentContactTime = $messageList[count($messageList) - 1]['send_time'] ?? '';
                $firstContactTime = $messageList[0]['send_time'] ?? '';
                $res[] = [
                    'snsType' => $snsType,
                    'referIds' => is_array($userContactId) ? $userContactId : [$userContactId],
                    'content' => $message,
                    'recentContactTime' => $recentContactTime,
                    'firstContactTime' => $firstContactTime,
                    'customerId' => $customerId,
                    'userId' => $userId ?? 0,
                ];
            }
        }

        return $res;
    }

    /**
     * 打标与总结 模块
     * @param array $runContext
     * @return array
     */
    public function keywordAndSummary(array $runContext): array
    {
        //qwen-plus
        $llmModel = $this->promptConfig['qc.generate.keyword.and.summary.key'] ?? 'qwen-plus';

        $chatInfo = $runContext['formatInfo'];

        $callGPTList = [];

        //邮件只输入聊天记录
        $mailFormatInfo = array_values(array_filter($chatInfo, function ($item) {
            return $item['snsType'] == \AiQualityCheckChatJourneyModel::SNS_TYPE_MAIL;
        }));
        if (!empty($mailFormatInfo)) {
            $mailSystemPrompt = $this->systemPromptMap['qc.mail.generate.keyword.and.summary.key'] ?? '';
            $mailUserPrompt = $this->userPromptMap['qc.mail.generate.keyword.and.summary.key'] ?? '';
            //邮件不考虑历史总结
            foreach ($mailFormatInfo as $mailInfo) {
                $journeyId = $mailInfo['journeyId'];
                $recentContactTime = $mailInfo['recentContactTime'];
                $content = $mailInfo['content'];
                $question = $this->replacePlaceholders($mailUserPrompt, [
                    'date 2' => $recentContactTime,
                    'mail_info' => $content,
                ]);

                $callGPTList[] = [
                    'systemPrompt' => $mailSystemPrompt,
                    'question' => $question,
                    'model' => $llmModel,
                    'referId' => $journeyId,
                    'afterProcess' => 'keywordsAndSummaryAfterProcess',
                    'afterProcessParams' => [
                        'journeyId' => $journeyId,
                    ]
                ];
            }
        }

        //sns输入聊天记录 + 以往的总结
        $snsFormatInfo = array_values(array_filter($chatInfo, function ($item) {
            return $item['snsType'] != \AiQualityCheckChatJourneyModel::SNS_TYPE_MAIL;
        }));
        if (!empty($snsFormatInfo)) {
            $snsSystemPrompt = $this->systemPromptMap['qc.sns.generate.keyword.and.summary.key'] ?? '';
            //按sns_type来
            foreach ($snsFormatInfo as $snsInfo) {
                //sns类型的聊天旅程,只有一个refer_id(user_contact_id)
                $userContactId = is_array($snsInfo['referIds']) ? $snsInfo['referIds'][0] : $snsInfo['referIds'];
                $contactTime = $snsInfo['recentContactTime'];
                $content = $snsInfo['content'];
                $journeyId = $snsInfo['journeyId'];

                //查询最近一天,相同user_contact_id的summary
                $journeyList = Helper::getLatestJourneyByReferIds($this->clientId, $this->companyId, $userContactId,$journeyId);
                $latestSummary = empty($journeyList) ? '' : $journeyList['summary'];
                $latestContactTime = empty($journeyList) ? '' : $journeyList['contact_time'];

                $questionParams = [
                    'date 2' => date('Y-m-d', strtotime($contactTime)),
                    'chat_records' => $content,
                ];
                if (!empty($latestSummary) && !empty($latestContactTime)) {
                    $snsUserPrompt = $this->userPromptMap['qc.sns.generate.keyword.and.summary.contain.history.key'] ?? '';
                    $questionParams['date 1'] = date('Y-m-d', strtotime($latestContactTime));
                    $questionParams['summary'] = $latestSummary;
                } else {
                    $snsUserPrompt = $this->userPromptMap['qc.sns.generate.keyword.and.summary.not.contain.history.key'];
                }

                $question = $this->replacePlaceholders($snsUserPrompt, $questionParams);

                $callGPTList[] = [
                    'systemPrompt' => $snsSystemPrompt,
                    'question' => $question,
                    'model' => $llmModel,
                    'referId' => $journeyId,
                    'afterProcess' => 'keywordsAndSummaryAfterProcess',
                    'afterProcessParams' => [
                        'journeyId' => $journeyId,
                    ]
                ];
            }
        }

        $this->callGPTLoop($callGPTList);
        return [$runContext, false];
    }

    private function keywordAndSummaryV2(array $runContext) : array
    {
        //谈单监测v2.1 打标模块 & 总结模块
        $pairs = $runContext['pairs'] ?? [];

        //1.获取pair对的所有记录
        $totalJourneyList = Helper::loadJourneyByUserAndCustomer($this->clientId, $this->companyId, $pairs);
        usort($totalJourneyList , function($a,$b){
            return strcmp($a['contact_time'], $b['contact_time']);
        });

        //2.切分
        $journeyWithPairList = Helper::splitJourneyListByUserAndCustomer($totalJourneyList);

        foreach ($journeyWithPairList as $pair => $journeyList)
        {
            //当天的记录
            $dateJourneyList = array_values(array_filter($journeyList, function ($item) {
                $contactTime = $item['contact_time'];
                $contactTime = date('Y-m-d H:i:s', strtotime($contactTime));
                return strcmp($contactTime, $this->startTime) >= 0 && strcmp($contactTime, $this->endTime) <= 0;
            }));

            //最近沟通日上边界
            $latestContactDay = self::getLatestContactDay($journeyList);
            //1.没有最近沟通日:no history
            //2.有最近沟通日,判断是否是当天第一条
            //2.1.当天第一条:[latestContactDay,this->startTime]
            //2.2.当天第N条:[latestContactDay,journey['contact_time]]
            for($index = 0;$index < count($dateJourneyList);$index++)
            {
                $journeyId = $dateJourneyList[$index]['journey_id'];
                //打标
                self::doCreateKeywords($journeyId, $pair, $latestContactDay, $index==0);
                //总结
                self::doCreateSummary($journeyId, $pair, $latestContactDay, $index == 0);
            }
        }

        return [$runContext, false];
    }

    //真·打标模块
    private function  doCreateKeywords($journeyId, $pair, $latestContactDay, bool $firstOfDay) : void
    {
        $journey = Helper::getJourneyByPrimaryKey($this->clientId, $journeyId);
        if(empty($latestContactDay)) {
            //no history
            $question = self::buildKeywordsPrompt($journey);
        }else{
            $leftTime = date('Y-m-d 00:00:00' , strtotime($latestContactDay));
            $rightTime = $firstOfDay ? $this->startTime : $journey['contact_time'];
            $latestJourneyList = Helper::loadJourneyByUserAndCustomer($this->clientId, $this->companyId, [$pair], [$leftTime, $rightTime]);
            $question = self::buildKeywordsPrompt($journey, $latestJourneyList);
        }

        $this->callGPTLoop([[
            'referId' => $journey['journey_id'],
            'systemPrompt' => $this->systemPromptMap['qc.v2.keywords.system.prompt'] ?? '',
            'question' => $question,
            'model' => $this->promptConfig['qc.v2.keywords.service'] ?? '',
            'afterProcess' => 'keywordsAfterProcess',
            'afterProcessParams' => [
                'journeyId' => $journey['journey_id'],
            ]
        ]]);
    }

    //真·总结模块
    private function doCreateSummary($journeyId, $pair, $latestContactDay, bool $firstOfDay) : void
    {
        $journey = Helper::getJourneyByPrimaryKey($this->clientId, $journeyId);
        if(empty($latestContactDay)){
            $question = self::buildSummaryPrompt($journey);
        }else{
            $leftTime = date('Y-m-d 00:00:00' , strtotime($latestContactDay));
            $rightTime = $firstOfDay ? $this->startTime : $journey['contact_time'];
            $latestJourneyList = Helper::loadJourneyByUserAndCustomer($this->clientId, $this->companyId, [$pair], [$leftTime, $rightTime]);
            $question = self::buildSummaryPrompt($journey, $latestJourneyList);
        }

        $this->callGPTLoop([[
            'referId' => $journey['journey_id'],
            'systemPrompt' => $this->systemPromptMap['qc.v2.summary.system.prompt'] ?? '',
            'question' => $question,
            'model' => $this->promptConfig['qc.v2.summary.service'] ?? '',
            'afterProcess' => 'summaryAfterProcess',
            'afterProcessParams' => [
                'journeyId' => $journey['journey_id']
            ]
        ]]);
    }

    private function buildSummaryPrompt($journey, $latestJourneyList = []) : string
    {
        $userPrompt = $this->userPromptMap['qc.v2.summary.user.prompt'] ?? '';

        $dateToday = date('Y-m-d', strtotime($journey['contact_time']));
        $channel = $journey['sns_type'] ?? '';
        $chatRecord = $journey['context'] ?? '';
        $keyEvents = Helper::convertKeywordsToString(PgsqlUtil::trimArray($journey['contact_keywords'] ?? ''));

        $recentSummary = "No summary of the recent communication.";
        if(!empty($latestJourneyList))
        {
            $historyPrompt = $this->userPromptMap['qc.v2.summary.history.user.prompt'] ?? '';
            $historyQuestion = '';
            foreach (array_values($latestJourneyList) as $seq => $latestJourney)
            {
                $historyQuestion .= $this->replacePlaceholders($historyPrompt , [
                        'seq' => $seq + 1,
                        'date_today' => date('Y-m-d' , strtotime($latestJourney['contact_time'] ?? '')),
                        'channel' => $latestJourney['sns_type'] ?? '',
                        'summary_en' => $latestJourney['summary'] ?? '',
                        'summary_cn' => json_decode($latestJourney['contact_summary'] ?? ''),
                    ]) . PHP_EOL;
            }
            $historyQuestion = trim($historyQuestion);
            if(!empty($historyQuestion)) $recentSummary = $historyQuestion;
        }

        return $this->replacePlaceholders($userPrompt , [
            'recent_summary' => $recentSummary,
            'date_today' => $dateToday,
            'channel' => $channel,
            'key_events' => empty($keyEvents) ? "None" : $keyEvents,
            'chat_record' => $chatRecord,
        ]);
    }

    //生成“标注模块”的userPrompt
    private function buildKeywordsPrompt($journey , $latestJourneyList = []) : string
    {
        $userPrompt = $this->userPromptMap['qc.v2.keywords.user.prompt'] ?? '';

        //channel,date,context
        $channel = $journey['sns_type'] ?? '';
        $dateToday = date('Y-m-d', strtotime($journey['contact_time'] ?? ''));
        $context = $journey['context'] ?? '';

        $recentSummary = "No summary of the recent communication.";
        if(!empty($latestJourneyList)){
            //历史的channel,date,summary,key_events
            $historyPrompt = $this->userPromptMap['qc.v2.keywords.history.user.prompt'] ?? '';
            $historyQuestion = '';
            $latestJourneyList = array_values($latestJourneyList);
            foreach ($latestJourneyList as $seq => $latestJourney)
            {
                $keyEvents = Helper::convertKeywordsToString(PgsqlUtil::trimArray($latestJourney['contact_keywords'] ?? ''));
                $historyQuestion .= $this->replacePlaceholders($historyPrompt , [
                        'seq' => $seq + 1,
                        'date_today' => date('Y-m-d', strtotime($latestJourney['contact_time'] ?? '')),
                        'channel' => $latestJourney['sns_type'] ?? '',
                        'summary' => $latestJourney['summary'] ?? '',
                        'events' => empty($keyEvents) ? "None" : $keyEvents,
                    ]) . PHP_EOL;
            }
            $historyQuestion = trim($historyQuestion);
            if(!empty($historyQuestion)) $recentSummary = $historyQuestion;
        }

        return $this->replacePlaceholders($userPrompt , [
            'date_today' => $dateToday,
            'channel' => $channel,
            'chat_record' => $context,
            'recent_summary' => $recentSummary,
        ]);
    }

    private function getLatestContactDay($journeyList) : string
    {
        //1.获取1个自然月内的记录
        $lastMonthTime = strtotime('-1 month', strtotime($this->startTime));
        $journeyList = array_values(array_filter($journeyList , function ($item) use ($lastMonthTime){
            $currentTime = strtotime($item['contact_time']);
            return $currentTime >= $lastMonthTime && $currentTime < strtotime($this->startTime);
        }));

        //2.获取最近3个沟通日的记录
        $contactDates = array_column($journeyList , 'contact_time');
        $contactDates = array_map(function($item) {
            return date('Y-m-d', strtotime($item));
        },$contactDates);
        $contactDates = array_values($contactDates);

        return $contactDates[max(0, count($contactDates) - 3)] ?? '';
    }

    public function formatKeywordsAndSummary(array $answer): array
    {
        $keywords = [];
        $events = $answer['key events'] ?? [];
        foreach ($events as $event)
        {
            if(isset(\AiQualityCheckChatJourneyModel::KEYWORD_ENUM_MAP[strtolower($event)])){
                $keywords[] = \AiQualityCheckChatJourneyModel::KEYWORD_ENUM_MAP[strtolower($event)];
            }
        }

        return $keywords;
    }

    /**
     * 卡点模块
     * v2.1 卡点模块只考虑sns,不考虑邮件
     * @param array $runContext
     * @return array
     */
    public function buildStuckPoints(array $runContext): array
    {
        $stickingPointSystemPrompt = $this->systemPromptMap['qc.generate.sticking.point.key'] ?? '';
        $llmModel = $this->promptConfig['qc.generate.sticking.point.key'] ?? '';

        //过滤掉邮件
        $snsFormatInfo = $runContext['formatInfo'];
        $snsFormatInfo = array_values(array_filter($snsFormatInfo, function ($item) {
            return $item['snsType'] != \AiQualityCheckChatJourneyModel::SNS_TYPE_MAIL;
        }));

        $callGPTList = [];
        foreach ($snsFormatInfo as $snsInfo) {
            $journeyId = $snsInfo['journeyId'];
            $userContactId = is_array($snsInfo['referIds']) ? $snsInfo['referIds'][0] : $snsInfo['referIds'];
            $recentContactTime = $snsInfo['recentContactTime'];
            $content = $snsInfo['content'];

            //如果当前标签不包含Q12,不跑卡点
            $journeyRecord = Helper::getJourneyByPrimaryKey($this->clientId, $journeyId);
            $keywords = empty($journeyRecord) ? [] : PgsqlUtil::trimArray($journeyRecord['contact_keywords']);
            if(empty($keywords) || !in_array(\AiQualityCheckChatJourneyModel::CONTAIN_STICKING_POINT_KEYWORD , $keywords)) {
                continue;
            }

            //相同user_contact_id最近是否存在卡点
            $journeyList = Helper::getLatestJourneyByReferIds($this->clientId, $this->companyId, $userContactId,$journeyId);
            $latestStickingPoint = empty($journeyList) ? '' : $journeyList['sticking_point'];
            $latestContactTime = empty($journeyList) ? '' : $journeyList['contact_time'];

            $questionParam = [
                'date' => date('Y-m-d', strtotime($recentContactTime)),
                'chat_records' => $content,
            ];
            $stickingPointUserPrompt = $this->userPromptMap['qc.generate.sticking.point.not.contain.history.key'];
            $question = $this->replacePlaceholders($stickingPointUserPrompt, $questionParam);


            //最近的未解决的卡点
            $stickingPoints = json_decode($latestStickingPoint, true);
            if (!empty($latestStickingPoint) && !empty($latestContactTime) && !empty($stickingPoints) && !empty($stickingPoints['existsIssues'])) {
                $extraUserPrompt = "## Unresolved Issues From The Last Communication";
                $extraBody = $this->userPromptMap['qc.generate.sticking.point.contain.history.key'];
                $stickingPoints = $stickingPoints['issues'] ?? [];
                //没有被解决的卡点
                $stickingPoints = array_values(array_filter($stickingPoints, function ($item) {
                    return $item['resolved'] == false;
                }));
                for ($i = 0; $i < count($stickingPoints); ++$i) {
                    $extraUserPrompt .= PHP_EOL;
                    $extraUserPrompt .= $this->replacePlaceholders($extraBody, [
                        'number' => $i + 1,
                        'issue' => $stickingPoints[$i]['issue']['eng'],
                        'solution' => $stickingPoints[$i]['solution']['eng'],
                        'customerFeedback' => $stickingPoints[$i]['customerFeedback']['eng'],
                    ]);
                }
                $question = $extraUserPrompt . PHP_EOL . $question;
            }
            $callGPTList[] = [
                'systemPrompt' => $stickingPointSystemPrompt,
                'question' => $question,
                'model' => $llmModel,
                'referId' => $journeyId,
                'afterProcess' => 'stickingPointsAfterProcess',
                'afterProcessParams' => [
                    'journeyId' => $journeyId
                ]
            ];
        }

        $this->callGPTLoop($callGPTList);

        return [$runContext, false];
    }

    private function formatStickingPoint(array $answer): array
    {
        $result = [];

        $issues = $answer['issues'] ?? [];
        $issuesArray = [];
        foreach ($issues as $item) {
            $obj = [];
            foreach ($item as $key => $value) {
                if ($key == 'resolved') {
                    $obj[$key] = $value;
                    continue;
                }
                $obj[$key]['eng'] = $value['eng'];
                $obj[$key]['zh'] = $value['zh'];
            }
            $issuesArray[] = $obj;
        }
        $result['issues'] = $issuesArray;

        $result['existsIssues'] = $answer['existsIssues'] ?? false;
        return $result;
    }

    /**
     * 英译中模块
     * @param array $runContext
     * @return array
     * @throws \Exception
     */
    public function translate(array $runContext): array
    {
        $chatInfo = $runContext['formatInfo'];
        $callGPTList = [];
        foreach ($chatInfo as $info) {
            $journeyId = $info['journeyId'];
            $snsType = $info['snsType'];
            $journeyObj = Helper::getJourneyByPrimaryKey($this->clientId, $journeyId);
            $summary = empty($journeyObj) ? '' : $journeyObj['summary'];

            //总结英译中
            $translateSummarySystemPrompt = $this->systemPromptMap['qc.translate.summary.key'] ?? '';
            $translateSummaryUserPrompt = $this->userPromptMap['qc.translate.summary.key'] ?? '';
            $translateSummaryLLMModel = $this->promptConfig['qc.translate.summary.key'] ?? '';

            if (!empty($summary)) {
                $question = $this->replacePlaceholders($translateSummaryUserPrompt, [
                    'summary' => $summary,
                ]);

                //sns需要获取上次总结的翻译
                if ($snsType != \AiQualityCheckChatJourneyModel::SNS_TYPE_MAIL) {
                    $userContactId = is_array($info['referIds']) ? $info['referIds'][0] : $info['referIds'];

                    $translateSummaryContainHistoryPrompt = $this->userPromptMap['qc.translate.summary.contain.history.key'] ?? '';
                    $journey = Helper::getLatestJourneyByReferIds($this->clientId, $this->companyId, $userContactId , $journeyId);
                    $latestSummaryENG = empty($journey) ? '' : json_encode($journey['summary']);
                    $latestSummaryZH = empty($journey) ? '' : json_decode($journey['contact_summary'] ?? '');

                    if (!empty($latestSummaryZH)) {
                        $historyQuestion = $this->replacePlaceholders($translateSummaryContainHistoryPrompt, [
                            'history_zh' => $latestSummaryZH,
                            'history_eng' => $latestSummaryENG,
                            'summary' => $summary,
                        ]);
                        $question = $historyQuestion;
                    }
                }

                $callGPTList[] = [
                    'systemPrompt' => $translateSummarySystemPrompt,
                    'question' => $question,
                    'model' => $translateSummaryLLMModel,
                    'referId' => $journeyId,
                    'afterProcess' => 'translateAfterProcess',
                    'afterProcessParams' => [
                        'journeyId' => $journeyId
                    ]
                ];
            }
        }

        $this->callGPTLoop($callGPTList);
        return [$runContext, false];
    }

    /**
     * @throws \Exception
     */
    public function updateJourneyById($journeyId, array $updateData)
    {
        $journey = new AiQualityCheckChatJourney($journeyId);

        if (!empty($updateData['summary'])) {
            $journey->summary = $updateData['summary'];
        }

        if (!empty($updateData['keywords'])) {
            $journey->contact_keywords = PgsqlUtil::formatArray($updateData['keywords']);
        }

        if (!empty($updateData['stickingPoint'])) {
            $journey->sticking_point = json_encode($updateData['stickingPoint']);
        }

        if (!empty($updateData['contactSummary'])) {
            $journey->contact_summary = json_encode($updateData['contactSummary']);
        }

        if(!empty($updateData['analysis']) && !empty($updateData['analyzeTime'])){
            $journey->analysis = $updateData['analysis'];
            $journey->analyze_time = $updateData['analyzeTime'];
        }

        if (!empty($updateData['contact_progress_summary'])) {
            $contactProgressSummary = json_decode($journey->contact_progress_summary, true);
            $contactProgressSummary = $contactProgressSummary + $updateData['contact_progress_summary'];
            $journey->contact_progress_summary = json_encode($contactProgressSummary, JSON_UNESCAPED_UNICODE);
        }

        $journey->save();
    }

    /**
     * 根据callGPTList参数,循环调用gpt
     * @param array $callGPTList
     * @return void
     * @throws AiAgentException
     */
    private function callGPTLoop(array $callGPTList): void
    {
        if (empty($callGPTList)) return;
        foreach ($callGPTList as $gptInfo) {
            $referId = $gptInfo['referId'];
            $systemPrompt = $gptInfo['systemPrompt'];
            $question = $gptInfo['question'];
            $llmModel = $gptInfo['model'];
            $afterProcess = $gptInfo['afterProcess'] ?? '';
            $afterProcessParams = $gptInfo['afterProcessParams'] ?? [];

            try {
                $gptResponse = $this->callByParams($referId, $question, $systemPrompt, $llmModel);
                $answer = $this->formatResponseAnswer($gptResponse['answer'] ?? '');
                if (empty($answer)) {
                    \LogUtil::info("AiCompanyQualityCheck-{$this->clientId}-{$this->companyId}-{$this->dateTime},GPT生成answer无法解析,使用gpt-4o重试");
                    $gptResponse = $this->callByParams($referId, $question, $systemPrompt, AIClient::AZURE_OPENAI_GPT_FOUR_O);
                    $answer = $this->formatResponseAnswer($gptResponse['answer'] ?? '');
                }

                $answer = !empty($answer) ? json_decode($answer, true) : [];

                if (!empty($afterProcess)) {
                    //各模块处理answer的方法
                    call_user_func([$this, $afterProcess], [
                        'answer' => $answer,
                        'params' => $afterProcessParams,
                    ]);
                }
            } catch (\Throwable $exception) {
                \LogUtil::error("AiCompanyQualityCheck-{$this->clientId}-{$this->companyId}-{$this->dateTime},调用GPT失败,exception:{$exception->getMessage()}");
                throw new AiAgentException($exception);
            }
        }
    }

    private function keywordsAfterProcess(array $params)
    {
        $answer = $params['answer'] ?? [];
        $params = $params['params'] ?? [];

        $journeyId = $params['journeyId'];
        $keywords = [];
        if (!empty($answer) && !empty($journeyId)) {
            $keywords = self::formatKeywordsAndSummary($answer);
        }

        $this->updateJourneyById($journeyId, [
            'keywords' => $keywords
        ]);
    }

    private function stickingPointsAfterProcess(array $params)
    {
        $answer = $params['answer'] ?? [];
        $params = $params['params'] ?? [];

        $journeyId = $params['journeyId'];
        $stickingPointList = [];
        if (!empty($answer) && !empty($journeyId)) {
            $stickingPointList = $this->formatStickingPoint($answer);
        }

        $updateData = [];
        $updateData['stickingPoint'] = $stickingPointList;

        //如果存在卡点,打上标签Q13
        if(!empty($stickingPointList) && !empty($stickingPointList['existsIssues'])){
            $record = Helper::getJourneyByPrimaryKey($this->clientId, $journeyId);
            $oldKeywords = empty($record['contact_keywords']) ? [] : PgsqlUtil::trimArray($record['contact_keywords']);
            $newKeywords = array_values(array_merge($oldKeywords, [\AiQualityCheckChatJourneyModel::CONTAIN_VALID_STICKING_POINT_KEYWORD]));
            $updateData['keywords'] = $newKeywords;
        }

        $this->updateJourneyById($journeyId, $updateData);
    }

    private function translateAfterProcess(array $params)
    {
        $answer = $params['answer'] ?? [];
        $params = $params['params'] ?? [];

        $journeyId = $params['journeyId'];
        $contactSummary = '';
        if (!empty($answer)) {
            $contactSummary = $answer['今日沟通总结中文翻译'] ?? '';
        }

        $this->updateJourneyById($journeyId, [
            'contactSummary' => $contactSummary,
        ]);
    }

    private function summaryAfterProcess(array $params)
    {
        $answer = $params['answer'] ?? [];
        $params = $params['params'] ?? [];

        $journeyId = $params['journeyId'];
        $summaryEng = '';
        $summaryCn = '';
        $summary = $answer['summary'] ?? [];
        if (!empty($summary)) {
            $summaryEng = $summary['en'] ?? '';
            $summaryCn = $summary['cn'] ?? '';
        }

        $contactProgressSummary = $this->summaryTranslate($summary);

        $this->updateJourneyById($journeyId, [
            'contactSummary' => $summaryCn,
            'summary' => $summaryEng,
            'contact_progress_summary' => ['summary' => $contactProgressSummary],
        ]);
    }

    private function summaryTranslate($summary)
    {
        $userSetting = new \common\library\setting\user\UserSetting($this->clientId, $this->userId, \common\library\setting\user\UserSetting::USER_LANGUAGE);
        $lang = $userSetting->getValue();
        $lang = \common\library\ai_agent\language\Helper::getLanguageKey(\AiAgent::AI_AGENT_SCENE_TYPE_COMPANY_QUALITY_CHECK, $lang);

        if (!isset($summary[$lang]))
        {
            $translate = \common\library\ai_agent\language\Helper::translateListByModel(['summary' => $summary['cn']], $lang);

            if (isset($translate['summary'])) {
                $summary[$lang] = $translate['summary'];
            }
        }

        return $summary;
    }

    private function updateCompanyV2(array $runContext)
    {
        $latestJourney = Helper::loadJourneyByCompanyIds($this->clientId, [$this->companyId], true);
        if(empty($latestJourney)) {
            return [$runContext, true];
        }
        $latestJourney = array_values($latestJourney)[0];

        $recentContactTime = $latestJourney['contact_time'] ?? '';
        $recentSnsType = $latestJourney['sns_type'] ?? '';
        $recentKeywords = $latestJourney['contact_keywords'] ?? '';

        $company = new AiQualityCheckCompany($this->companyId);
        $company->recent_contact_time = $recentContactTime;
        $company->report_update_time = $recentContactTime;
        $company->update_time = date('Y-m-d H:i:s', time());
        $company->recent_sns_type = $recentSnsType;
        $company->recent_contact_keywords = $recentKeywords;

        $company->save();

        return [$runContext , false];
    }


    /**
     * 买家问题模块
     * 1.初始化买家问题
     * 2.买家问题进展更新
     * 3.买家问题关闭
     * @param $params
     * @return void
     */
    public function processStickingPoint($params) : void
    {
        $this->clientId = $params['clientId'];
        $this->companyId = $params['companyId'];
        $this->dateTime = $params['dateTime'];

        //当天的所有聊天旅程
        $journeyIds = $params['journeyIds'];

        //Fixme 每个子流程的执行时间
        $logStartTime = microtime(true);

        //1.初始化买家问题
        \LogUtil::info("AiCompanyQuality -- processStickingPoint -- 初始化买家问题");
        $journeyList = Helper::loadJourneyByPrimaryIds($this->clientId , $journeyIds);
        //有卡点trigger
        $containIssueList = array_filter($journeyList, function($item){
            $keyWords = $item['contact_keywords'];
            $keyWords = PgsqlUtil::trimArray($keyWords);
            return in_array(\AiQualityCheckChatJourneyModel::CONTAIN_STICKING_POINT_KEYWORD, $keyWords);
        });
        $this->newStickingPoint($containIssueList);
        $logEndTime1 = microtime(true);

        //2.买家问题进展更新
        \LogUtil::info("AiCompanyQuality -- processStickingPoint -- 买家问题进展更新");
        $this->updateProgress($journeyList);
        $logEndTime2 = microtime(true);

        //3.关闭买家问题
        $this->closeStickingPoint();
        \LogUtil::info("AiCompanyQuality -- processStickingPoint -- 关闭买家问题");
        $logEndTime3 = microtime(true);

        // 翻译卡点
        try {
            $this->translateStickingPoint($containIssueList);
        } catch (\Throwable $throwable) {
            \LogUtil::info('translateStickPointError', [
                'client_id' => $this->clientId,
                'user_id' => $this->userId,
                'list' => $containIssueList,
                'msg' => $throwable->getMessage(),
            ]);
        }

        \LogUtil::info("AiCompanyQuality -- processStickingPoint -- Run Success -- Cost Time : " , [
            'new_sticking_point' => $logEndTime1 - $logStartTime,
            'update_progress' => $logEndTime2 - $logEndTime1,
            'close_sticking_point' => $logEndTime3 - $logEndTime2,
        ]);
    }

    private function translateStickingPoint($containIssueList)
    {
        // 假如不是其他语言不用翻译
        $userSetting = new \common\library\setting\user\UserSetting($this->clientId, $this->userId, \common\library\setting\user\UserSetting::USER_LANGUAGE);
        $lang = $userSetting->getValue();
        if ($lang == 'zh-CN' || $lang == 'en' || $lang == 'zh-TW') {
            return;
        }

        $journeyIds = array_column($containIssueList, 'journey_id');

        if (empty($journeyIds)) {
            return;
        }

        $journeyIds = implode(",", $journeyIds);
        $sql = <<<SQL
SELECT * FROM tbl_ai_quality_check_sticking_point WHERE client_id = {$this->clientId} AND journey_ids && ARRAY[{$journeyIds}]::bigint[] 
SQL;
        $stickingPointList = \PgActiveRecord::getDbByClientId($this->clientId)->createCommand($sql)->queryAll();

        $stickingInfoKeyByPointId = array_column($stickingPointList, null, 'sticking_point_id');

        // 没有卡点退出
        if (empty($stickingInfoKeyByPointId)) {
            return;
        }

        $translateContentList = [];
        foreach ($stickingPointList as $stickingPoint)
        {
            $stickingPointId = $stickingPoint['sticking_point_id'];

            $translateIssueDescription = json_decode($stickingPoint['issue_description'], true);
            $translateIssueDescription = $translateIssueDescription['cn'];
            $translateContentList["{$stickingPointId}|||issue_description"] = $translateIssueDescription;

            $progressHistoryList = json_decode($stickingPoint['progress_history'], true);
            foreach ($progressHistoryList as $date => $progressHistory)
            {
                if (isset($progressHistory['progress']['cn'])) {
                    $translateContentList["{$stickingPointId}|||{$date}|||progress"] = $progressHistory['progress']['cn'];
                }
            }

            $translateResolutionSummary = json_decode($stickingPoint['resolution_summary'], true);
            $translateResolutionSummary = $translateResolutionSummary['cn'];
            $translateContentList["{$stickingPointId}|||resolution_summary"] = $translateResolutionSummary;
        }

        $lang = \common\library\ai_agent\language\Helper::getLanguageKey(\AiAgent::AI_AGENT_SCENE_TYPE_COMPANY_QUALITY_CHECK, $lang);

        // 已经支持英语和中文
        if ($lang != 'cn' && $lang != 'en')
        {
            $caseWhenIssueDescriptionList = $caseWhenResolutionSummaryList = $caseWhenProgressHistoryList = $needUpdateIds = [];
            $translate = \common\library\ai_agent\language\Helper::translateListByModel($translateContentList, $lang);

            foreach ($stickingInfoKeyByPointId as $pointId => $stickingInfo)
            {
                $stickingPointId = $stickingInfo['sticking_point_id'];

                if (isset($translate["{$stickingPointId}|||issue_description"]) && isset($translate["{$stickingPointId}|||resolution_summary"]))
                {
                    $needUpdateIds[] = $stickingInfo['sticking_point_id'];

                    if (isset($translate["{$stickingPointId}|||issue_description"]))
                    {
                        $issueDescription = json_decode($stickingInfo['issue_description'], true);
                        $issueDescription[$lang] = $translate["{$stickingPointId}|||issue_description"];
                        $issueDescription = json_encode($issueDescription, JSON_UNESCAPED_UNICODE);
                        $caseWhenIssueDescriptionList[] = "WHEN sticking_point_id = {$stickingPointId} THEN '{$issueDescription}'";
                    }

                    if (isset($translate["{$stickingPointId}|||resolution_summary"]))
                    {
                        $resolutionSummary = json_decode($stickingInfo['resolution_summary'], true);
                        $resolutionSummary[$lang] = $translate["{$stickingPointId}|||resolution_summary"];
                        $resolutionSummary = json_encode($resolutionSummary, JSON_UNESCAPED_UNICODE);
                        $caseWhenResolutionSummaryList[] = "WHEN sticking_point_id = {$stickingPointId} THEN '{$resolutionSummary}'";
                    }

                    $progressHistoryList = json_decode($stickingInfo['progress_history'], true);
                    $translateProgressHistoryFlag = false;
                    foreach ($progressHistoryList as $date => &$progressHistoryInfo)
                    {
                        if (isset($translate["{$stickingPointId}|||{$date}|||progress"]))
                        {
                            $translateProgressHistoryFlag = true;
                            $progressHistoryInfo['progress'][$lang] = $translate["{$stickingPointId}|||{$date}|||progress"];
                        }
                    }

                    if ($translateProgressHistoryFlag)
                    {
                        $progressHistory = json_encode($progressHistoryList, JSON_UNESCAPED_UNICODE);
                        $caseWhenProgressHistoryList[] = "WHEN sticking_point_id = {$stickingPointId} THEN '{$progressHistory}'";
                    }
                }
            }

            if (!empty($caseWhenIssueDescriptionList) && !empty($caseWhenResolutionSummaryList))
            {
                $caseWhenResolutionSummary = implode("\n", $caseWhenResolutionSummaryList);
                $caseWhenIssueDescription = implode("\n", $caseWhenIssueDescriptionList);
                $caseWhenProgressHistory = implode("\n", $caseWhenProgressHistoryList);
                $needUpdateIds = implode(",", $needUpdateIds);

                $sql = <<<SQL
UPDATE tbl_ai_quality_check_sticking_point
SET 
    issue_description = CASE 
                 {$caseWhenIssueDescription}
                 ELSE issue_description
              END,
    resolution_summary = CASE 
                 {$caseWhenResolutionSummary}
                 ELSE resolution_summary
              END,
    progress_history = CASE 
                 {$caseWhenProgressHistory}
                 ELSE progress_history
              END
WHERE sticking_point_id IN ($needUpdateIds);
SQL;
                \PgActiveRecord::getDbByClientId($this->clientId)->createCommand($sql)->execute();
            }
        }
    }

    private function newStickingPoint(array $journeyList)
    {
        $systemPrompt = $this->systemPromptMap['qc.v2.new.sticking.point.system.prompt'] ?? '';
        $llmModel = $this->promptConfig['qc.v2.new.sticking.point.service'] ?? '';

        //按照(user_id,customer_id)切分
        $journeyWithPair = Helper::splitJourneyListByUserAndCustomer($journeyList);

        $callGptList = [];
        foreach ($journeyWithPair as $pair => $journeyObjs)
        {
            $ss = explode("-" , $pair);
            $userId = $ss[0];
            $customerId = $ss[1];

            $userPrompt = $this->userPromptMap['qc.v2.new.sticking.point.user.prompt'] ?? '';
            $chatRecordAllDay = "";
            $chatRecordUserPrompt = $this->userPromptMap['qc.v2.new.sticking.point.chat.record.user.prompt'] ?? '';

            $journeyObjs = array_values($journeyObjs);
            foreach ($journeyObjs as $seq => $journeyObj)
            {
                $chatRecordAllDay .= $this->replacePlaceholders($chatRecordUserPrompt , [
                        'seq' => $seq + 1,
                        'channel' => $journeyObj['sns_type'] ?? '',
                        'summary_eng' => $journeyObj['summary'] ?? '',
                        'summary_cn' => json_decode($journeyObj['contact_summary']) ?? '',
                        'chat_records' => $journeyObj['context'] ?? '',
                    ]) . PHP_EOL;
            }
            $chatRecordAllDay = trim($chatRecordAllDay);

            //1.没有ongoing中的买家问题
            $ongoingIssues = "No ongoing issues.";

            //2.有ongoing中的买家问题
            //$ongoingIssues = ...
            $ongoingList = Helper::loadStickingPointByUserAndCustomer($this->clientId, [[$userId, $customerId]]);
            $ongoingList = array_values(array_filter($ongoingList , function($item) {
                return isset($item['issue_state']) && $item['issue_state'] == 1;
            }));
            if(!empty($ongoingList)) {
                $ongoingUserPrompt = $this->userPromptMap['qc.v2.new.sticking.point.ongoing.issue.user.prompt'] ?? '';
                $ongoingQuestion = "";
                foreach ($ongoingList as $seq => $ongoingItem)
                {
                    $progressHistory = json_decode($ongoingItem['progress_history'], true);
                    $progressHistoryStrArray = [];
                    foreach ($progressHistory as $progressDate => $progressItem)
                    {
                        $progressDate = date('Y-m-d', strtotime($progressDate));
                        $progress = json_encode($progressItem['progress'] ?? '' , JSON_UNESCAPED_UNICODE);
                        $progressHistoryStrArray[] = "{$progressDate}: {$progress}";
                    }
                    $progressHistoryStr = "{" . implode(",", $progressHistoryStrArray) . "}";
                    $ongoingQuestion = $this->replacePlaceholders($ongoingUserPrompt , [
                            'seq' => $seq + 1,
                            'start_date' => date('Y-m-d' , strtotime($ongoingItem['start_date'] ?? '')),
                            'issue_description' => $ongoingItem['issue_description'] ?? '',
                            'progress_history' => $progressHistoryStr,
                        ]) . PHP_EOL;
                }
                $ongoingQuestion = trim($ongoingQuestion);
                if(!empty($ongoingQuestion)) $ongoingIssues = $ongoingQuestion;
            }

            $question = $this->replacePlaceholders($userPrompt,[
                'ongoing_issues' => $ongoingIssues,
                'date_today' => $this->dateTime,
                'chat_record_all_day' => $chatRecordAllDay
            ]);


            $callGptList[] = [
                'systemPrompt' => $systemPrompt,
                'question' => $question,
                'model' => $llmModel,
                'referId' => 0,
                'afterProcess' => 'newStickingPointAfterProcess',
                'afterProcessParams' => [
                    'userId' => $userId,
                    'customerId' => $customerId,
                    'journeyIds' => array_column($journeyObjs, 'journey_id'),
                ]
            ];
        }

        $this->callGPTLoop($callGptList);
    }

    private function newStickingPointAfterProcess($param)
    {
        $answer = $param['answer'];
        $param = $param['params'];

        $userId = $param['userId'];
        $customerId = $param['customerId'];
        $journeyIds = $param['journeyIds'];

        $existNewIssues = $answer['exist_new_issues'] ?? false;
        $newIssues = $answer['new_issues'] ?? [];

        if($existNewIssues && !empty($newIssues)) {
            $currentTime = time();
            foreach ($newIssues as $issue)
            {
                $stickingPointDO = new AiQualityCheckStickingPoint();
                $stickingPointDO->client_id = $this->clientId;
                $stickingPointDO->company_id = $this->companyId;
                $stickingPointDO->user_id = $userId;
                $stickingPointDO->customer_id = $customerId;
                $stickingPointDO->start_date = $this->dateTime;
                $stickingPointDO->update_date = $this->dateTime;
                $stickingPointDO->issue_description = json_encode($issue['issue_description'] ?? '' , JSON_UNESCAPED_UNICODE);
                $progressHistory = [];
                $progressHistory[$this->dateTime] = [
                    'progress' =>  $issue['today_progress'] ?? [],
                    'is_directly_related' => "true", //初始化第一天为true @zhuxiran
                ];
                $stickingPointDO->progress_history = json_encode($progressHistory, JSON_UNESCAPED_UNICODE);
                $stickingPointDO->journey_ids = PgsqlUtil::formatArray($journeyIds);

                if($issue['is_resolved']) {
                    $stickingPointDO->issue_state = AiQualityCheckStickingPointModel::ISSUE_STATE_CLOSED;
                    $stickingPointDO->resolution_status = AiQualityCheckStickingPointModel::ISSUE_RESOLVED;
                    $stickingPointDO->resolution_summary = json_encode($issue['resolution_reasoning'] ?? '' , JSON_UNESCAPED_UNICODE);
                    $stickingPointDO->end_date = date('Y-m-d H:i:s', $currentTime);
                }else{
                    $stickingPointDO->issue_state = AiQualityCheckStickingPointModel::ISSUE_STATE_ON_GOING;
                    $stickingPointDO->resolution_status = AiQualityCheckStickingPointModel::ISSUE_UNSOLVED;
                }
                $stickingPointDO->create_time = date('Y-m-d H:i:s', $currentTime);
                $stickingPointDO->update_time = date('Y-m-d H:i:s', $currentTime);
                $stickingPointDO->save();
            }

            //存在新的卡点,为journey_ids添加关键词"买家问题"
            foreach ($journeyIds as $journeyId)
            {
                $journey = new AiQualityCheckChatJourney($journeyId);
                $keywords = $journey->contact_keywords;
                $keywords = PgsqlUtil::trimArray($keywords);
                $keywords[] = \AiQualityCheckChatJourneyModel::CONTAIN_VALID_STICKING_POINT_KEYWORD;
                $journey->contact_keywords = PgsqlUtil::formatArray($keywords);
                $journey->save();
            }
        }
    }

    private function updateProgress($dateJourneyList)
    {
        $systemPrompt = $this->systemPromptMap['qc.v2.update.progress.system.prompt'] ?? '';
        $llmModel = $this->promptConfig['qc.v2.update.progress.service'] ?? '';
        $userPrompt = $this->userPromptMap['qc.v2.update.progress.user.prompt'] ?? '';
        $chatRecordAllDayUserPrompt = $this->userPromptMap['qc.v2.update.progress.chat.record.user.prompt'] ?? '';
        $issueUserPrompt = $this->userPromptMap['qc.v2.update.progress.chat.issue.user.prompt'] ?? '';

        //按照pair切割
        $journeyWithPairMap = Helper::splitJourneyListByUserAndCustomer($dateJourneyList);

        //查找所有pair对的ongoing的卡点
        $pairs = array_map(function ($item) {
            return explode("-", $item);
        },array_keys($journeyWithPairMap));
        $stickingPointList = Helper::loadStickingPointByUserAndCustomer($this->clientId, $pairs);
        $stickingPointWithPairMap = Helper::splitJourneyListByUserAndCustomer($stickingPointList);

        $callGPTList = [];
        foreach ($journeyWithPairMap as $pair => $journeyList)
        {
            $pairStickingPointList = $stickingPointWithPairMap[$pair] ?? [];
            if(empty($pairStickingPointList))
            {
                \LogUtil::info("AiCompanyQualityCheck -- updateProgress -- Pair Not contain Sticking Point" , [
                    'client_id' => $this->clientId,
                    'company_id' => $this->companyId,
                    'date_time' => $this->dateTime,
                    'user_customer_id' => $pair,
                ]);
                continue;
            }
            $pairStickingPointList = array_values(array_filter($pairStickingPointList , function ($item) {
                return $item['issue_state'] == AiQualityCheckStickingPointModel::ISSUE_STATE_ON_GOING;
            }));

            if(empty($pairStickingPointList)) continue;
            //当日聊天记录
            $chatRecordAllDay = "";
            foreach (array_values($journeyList) as $seq => $item) {
                $chatRecordAllDay .= $this->replacePlaceholders($chatRecordAllDayUserPrompt , [
                        'seq' => $seq + 1,
                        'channel' => $item['sns_type'] ?? '',
                        'summary_eng' => $item['summary'] ?? '',
                        'summary_cn' => json_decode($item['contact_summary'] ?? ''),
                        'chat_records' => $item['context'],
                    ]) . PHP_EOL;
            }
            $chatRecordAllDay = trim($chatRecordAllDay);

            foreach ($pairStickingPointList as $stickingPoint)
            {
                //判断是否是当日的卡点
                $createInCurrentDay = $this->dateTime == date('Y-m-d' , strtotime($stickingPoint['start_date']));
                $issue = $this->replacePlaceholders($issueUserPrompt , [
                    'start_date' => date('Y-m-d', strtotime($stickingPoint['start_date'] ?? '')),
                    'issue_description' => $stickingPoint['issue_description'] ?? '',
                    'progress_history' => $createInCurrentDay ? "No progress history" : $stickingPoint['progress_history'],
                ]);
                $question = $this->replacePlaceholders($userPrompt , [
                    'issue' => $issue,
                    'date_today' => $this->dateTime,
                    'chat_record_all_day' => $chatRecordAllDay,
                ]);
                $callGPTList[] = [
                    'systemPrompt' => $systemPrompt,
                    'question' => $question,
                    'model' => $llmModel,
                    'referId' => $stickingPoint['sticking_point_id'],
                    'afterProcess' => 'updateProgressAfterProcess',
                    'afterProcessParams' => [
                        'stickingPointId' => $stickingPoint['sticking_point_id'],
                        //如果没有“解决”,使用相同的question,不同的systemPrompt再次调用GPT
                        'question' => $question,
                        'journeyIds' => array_column($journeyList, 'journey_id'),
                    ]
                ];
            }
        }
        $this->callGPTLoop($callGPTList);
    }

    private function updateProgressAfterProcess($params)
    {
        $answer = $params['answer'] ?? [];
        $params = $params['params'] ?? [];

        $pId = $params['stickingPointId'];
        $question = $params['question'];
        $journeyIds = $params['journeyIds'];

        $resolved = $answer['is_resolved'] ?? false;
        $stickingPointDO = new AiQualityCheckStickingPoint($pId);
        if($resolved) {
            //更新问题进展
            $resolutionProgress = $answer['resolution_progress'] ?? [];
            $progressHistory = json_decode($stickingPointDO->progress_history , true);
            $progressHistory[$this->dateTime] = [
                'progress' => $resolutionProgress,
                'is_directly_related' => true,
            ];
            $stickingPointDO->progress_history = json_encode($progressHistory, JSON_UNESCAPED_UNICODE);
            $stickingPointDO->resolution_status = AiQualityCheckStickingPointModel::ISSUE_RESOLVED;
        }
        $stickingPointDO->resolution_summary = json_encode($answer['reasoning'] ?? [] , JSON_UNESCAPED_UNICODE);
        //添加journey_ids
        $oldJourneyIds = PgsqlUtil::trimArray($stickingPointDO->journey_ids);
        $newJourneyIds = array_unique(array_merge($oldJourneyIds , $journeyIds));
        $stickingPointDO->journey_ids = PgsqlUtil::formatArray($newJourneyIds);
        $stickingPointDO->save();

        if(!$resolved) {
            //未解决问题更新
            $systemPrompt = $this->systemPromptMap['qc.v2.update.progress.unresolved.system.prompt'] ?? '';
            $llmModel = $this->promptConfig['qc.v2.update.progress.service'] ?? '';
            $this->callGPTLoop([[
                'systemPrompt' => $systemPrompt,
                'question' => $question,
                'model' => $llmModel,
                'referId' => $pId,
                'afterProcess' => 'unresolvedAfterProcess',
                'afterProcessParams' => [
                    'stickingPointId' => $pId,
                ]
            ]]);
        }
    }

    private function unresolvedAfterProcess($params)
    {
        $answer = $params['answer'];
        $params = $params['params'];

        $pId = $params['stickingPointId'];
        if(!empty($answer['progress']) && !empty($answer['progress']['en'])) {
            $stickingPointDO = new AiQualityCheckStickingPoint($pId);
            $progressHistory = json_decode($stickingPointDO->progress_history , true);
            $progressHistory[$this->dateTime] = [
                'is_directly_related' => $answer['is_directly_related'] ?? "false",
                'progress' => $answer['progress'],
            ];
            $stickingPointDO->progress_history = json_encode($progressHistory , JSON_UNESCAPED_UNICODE);
            $stickingPointDO->update_date = $this->dateTime;
            $stickingPointDO->save();
        }
    }

    private function closeStickingPoint() : void
    {
        //关闭买家问题
        $onGoingList = Helper::loadStickingPointByCompanyIds($this->clientId, $this->companyId, AiQualityCheckStickingPointModel::ISSUE_STATE_ON_GOING);
        $updateList = [];
        foreach ($onGoingList as $stickingPoint)
        {
            $resolved = $stickingPoint['resolution_status'];
            //1.resolution_status被设置为resolved的issue，将issue_status设置为closed
            if($resolved == AiQualityCheckStickingPointModel::ISSUE_RESOLVED) {
                $updateList[] = $stickingPoint['sticking_point_id'];
                continue;
            }else{
                //2.如果某个进行中的问题3个沟通日，或30个自然日没有更新进展，则issue_status直接设置为“closed”
                $progressHistory = json_decode($stickingPoint['progress_history'] ?? [] , true);
                if(empty($progressHistory)) continue;
                krsort($progressHistory);

                //3个工作日没有关联进展
                $cnt = 0;
                $notRelated = false;
                foreach ($progressHistory as $progress)
                {
                    $relate = $progress['is_directly_related'] ?? false;
                    if(!$relate) {
                        $cnt++;
                        if($cnt >= 3)
                        {
                            $notRelated = true;
                            break;
                        }
                    }else{
                        $cnt = 0;
                    }
                }
                if($notRelated) {
                    $updateList[] = $stickingPoint['sticking_point_id'];
                    continue;
                }

                //30个自然日
                $progressDates = array_keys($progressHistory);
                $maxProgressDate = array_values($progressDates)[0];
                $progressDateTime = new \DateTime($maxProgressDate);
                $currentDateTime = new \DateTime($this->dateTime);
                $interval = $currentDateTime->diff($progressDateTime);
                if(abs($interval->days) > 30) {
                    $updateList[] = $stickingPoint['sticking_point_id'];
                    continue;
                }
            }
        }

        if(!empty($updateList)) {
            $db = \PgActiveRecord::getDbByClientId($this->clientId);
            $newState = AiQualityCheckStickingPointModel::ISSUE_STATE_CLOSED;
            $updateIdSQL = implode(",", $updateList);
            $endDate = date('Y-m-d H:i:s', strtotime($this->dateTime));
            $sql = "UPDATE tbl_ai_quality_check_sticking_point SET issue_state = {$newState} , end_date = '{$endDate}' WHERE client_id = {$this->clientId} AND sticking_point_id IN ({$updateIdSQL})";
            echo "将卡点" . $updateIdSQL . "关闭, Date Time : " . $this->dateTime . PHP_EOL;
            $db->createCommand($sql)->execute();
        }

    }

    /* 新询盘标签处理逻辑 */
    public function processTag($params)
    {
        $this->clientId = $params['clientId'];
        $this->companyId = $params['companyId'];
        $this->dateTime = $params['dateTime'];

        // 新增名单控制
        $privilegeService = PrivilegeService::getInstance($this->clientId);
        if (!$privilegeService->hasFunctional(\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_COMPANY_AI_PORTRAIT))
        {
            \LogUtil::info('analysisNotPrivilege', [
                'clientId' => $this->clientId,
                'companyId' => $this->companyId,
                'dateTime' => $this->dateTime,
            ]);
            return;
        }

        $contactDate = date('Y-m-d', strtotime($this->dateTime));
        $journeyList = Helper::getJourneyListOrderByLastMessageTime($this->clientId, $this->companyId, $contactDate);

        foreach ($journeyList as $journey)
        {
            try {
                $this->newInquiryTag($journey);
                $this->paymentTag($journey);
                $this->requirementTag($journey);
                $this->keyCharacter($journey);
            } catch (\Throwable $throwable) {
                \LogUtil::info('handleProcessTagError', [
                    'client_id' => $this->clientId,
                    'journey' => $journey,
                    'code' => $throwable->getCode(),
                    'msg' => $throwable->getMessage()
                ]);
            }
        }
    }

    public function newInquiryTag($journey)
    {
        list($chatRecord, $contactBeginTime, $contactEndTime, $snsTypes) = $this->generateChatRecord(
            $journey['customer_id'],
            \AiQualityCheckCompanyTagsModel::TAG_TYPE_NEW_INQUIRY,
            \AiQualityCheckChatJourneyModel::CONTAIN_NEW_INQUIRY_KEYWORD
        );

        if (empty(array_filter($chatRecord))) {
            return;
        }

        $chatRecord = json_encode($chatRecord, JSON_UNESCAPED_UNICODE);
        $systemPrompt = $this->systemPromptMap['qc.v2.new.inquiry.tag.system.prompt'] ?? '';
        $userPrompt = $this->userPromptMap['qc.v2.new.inquiry.tag.user.prompt'] ?? '';
        $userPrompt = str_replace('{{newInquiryTagChatRecord}}', $chatRecord, $userPrompt);

        $this->generateTags(($this->promptConfig['qc.v2.new.inquiry.tag.service'] ?? AIClient::QWEN_PLUS_LATEST), $systemPrompt, $userPrompt, $journey['customer_id'], $snsTypes, $contactBeginTime, $contactEndTime, \AiQualityCheckCompanyTagsModel::TAG_TYPE_NEW_INQUIRY);
    }

    /* 支付标签处理逻辑 */
    public function paymentTag($journey)
    {
        list($chatRecord, $contactBeginTime, $contactEndTime, $snsTypes) = $this->generateChatRecord(
            $journey['customer_id'],
            \AiQualityCheckCompanyTagsModel::TAG_TYPE_PAYMENT,
            \AiQualityCheckChatJourneyModel::CONTAIN_PRODUCT_CUSTOMER_PAYMENT
        );

        if (empty(array_filter($chatRecord))) {
            return;
        }

        $chatRecord = json_encode($chatRecord, JSON_UNESCAPED_UNICODE);
        $systemPrompt = $this->systemPromptMap['qc.v2.payment.tag.system.prompt'] ?? '';
        $userPrompt = $this->userPromptMap['qc.v2.payment.tag.user.prompt'] ?? '';
        $userPrompt = str_replace('{{paymentTagChatRecord}}', $chatRecord, $userPrompt);

        $this->generateTags(($this->promptConfig['qc.v2.payment.tag.service'] ?? AIClient::QWEN_PLUS_LATEST), $systemPrompt, $userPrompt, $journey['customer_id'], $snsTypes, $contactBeginTime, $contactEndTime, \AiQualityCheckCompanyTagsModel::TAG_TYPE_PAYMENT);
    }

    /* 需求标签处理逻辑 */
    public function requirementTag($journey)
    {
        list($chatRecord, $contactBeginTime, $contactEndTime, $snsTypes) = $this->generateChatRecord(
            $journey['customer_id'],
            \AiQualityCheckCompanyTagsModel::TAG_TYPE_REQUIREMENT,
            \AiQualityCheckChatJourneyModel::CONTAIN_PRODUCT_CUSTOMER_PAYMENT
        );

        if (empty(array_filter($chatRecord))) {
            return;
        }

        $chatRecord = json_encode($chatRecord, JSON_UNESCAPED_UNICODE);
        $systemPrompt = $this->systemPromptMap['qc.v2.requirement.tag.system.prompt'] ?? '';
        $userPrompt = $this->userPromptMap['qc.v2.requirement.tag.user.prompt'] ?? '';
        $userPrompt = str_replace('{{requirementTagChatRecord}}', $chatRecord, $userPrompt);

        $this->generateTags(($this->promptConfig['qc.v2.requirement.tag.service'] ?? AIClient::QWEN_PLUS_LATEST), $systemPrompt, $userPrompt, $journey['customer_id'], $snsTypes, $contactBeginTime, $contactEndTime, \AiQualityCheckCompanyTagsModel::TAG_TYPE_REQUIREMENT);
    }

    /* 重要特征标签处理逻辑 */
    public function keyCharacter($journey)
    {
        list($chatRecord, $contactBeginTime, $contactEndTime, $snsTypes) = $this->generateChatRecord(
            $journey['customer_id'],
            \AiQualityCheckCompanyTagsModel::TAG_TYPE_KEY_CHARACTER,
            \AiQualityCheckChatJourneyModel::CONTAIN_PRODUCT_CUSTOMER_PAYMENT
        );

        if (empty(array_filter($chatRecord))) {
            return;
        }

        $chatRecord = json_encode($chatRecord, JSON_UNESCAPED_UNICODE);
        $systemPrompt = $this->systemPromptMap['qc.v2.key.character.tag.system.prompt'] ?? '';
        $userPrompt = $this->userPromptMap['qc.v2.key.character.tag.user.prompt'] ?? '';
        $userPrompt = str_replace('{{keyCharacterTagChatRecord}}', $chatRecord, $userPrompt);

        $this->generateTags(($this->promptConfig['qc.v2.key.character.tag.service'] ?? AIClient::QWEN_PLUS_LATEST), $systemPrompt, $userPrompt, $journey['customer_id'], $snsTypes, $contactBeginTime, $contactEndTime, \AiQualityCheckCompanyTagsModel::TAG_TYPE_KEY_CHARACTER);
    }

    public function generateTags($model, $systemPrompt, $userPrompt, $customerId, $snsTypes, $contactBeginTime, $contactEndTime, $tagType)
    {
        $requestBeginTime = microtime(true);

        $aiClient = new AIClient();
        $className = basename(str_replace('\\', '/', get_class($this)));
        $aiClient->setTrace("OKKI_AI", $className);
        $aiClient->setModel($model);
        $aiClient->setSystemPrompt($systemPrompt);
        $aiClient->setQuestion($userPrompt);
        $aiClient->setResponseFormat(['type' => 'json_object']);
        $originResponse = $aiClient->chatCompletions();

        $requestEndTime = microtime(true);

        $response = $aiClient->convertResponse($originResponse['data']);
        $response = $this->formatJson($response);

        // 保存 record 记录
        $aiServiceProcessRecordPdo = new AiServiceProcessRecord();
        $aiServiceProcessRecordPdo->params = '{}';
        $aiServiceProcessRecordPdo->scene_type = $this->sceneType;
        $aiServiceProcessRecordPdo->refer_id = $this->companyId;
        $aiServiceProcessRecordPdo->question = $userPrompt;
        $aiServiceProcessRecordPdo->answer = $response['answer'];
        $aiServiceProcessRecordPdo->client_id = $this->clientId;
        $aiServiceProcessRecordPdo->create_user = $this->userId;
        $aiServiceProcessRecordPdo->status = 1;
        $aiServiceProcessRecordPdo->ai_model = $model;
        $aiServiceProcessRecordPdo->prompt_tokens = $response['promptTokens'];
        $aiServiceProcessRecordPdo->completion_tokens = $response['completionTokens'];
        $aiServiceProcessRecordPdo->total_tokens = $response['totalTokens'];
        $aiServiceProcessRecordPdo->agent_version_id = $this->versionId;
        $aiServiceProcessRecordPdo->request_data = json_encode($aiClient->buildParams());
        $aiServiceProcessRecordPdo->request_response = json_encode($originResponse, JSON_UNESCAPED_UNICODE);
        $aiServiceProcessRecordPdo->request_time = $requestEndTime - $requestBeginTime;
        $aiServiceProcessRecordPdo->save();

        // 保存标签内容
        $aiQualityCheckCompanyTagsPdo = new AiQualityCheckCompanyTags();
        $aiQualityCheckCompanyTagsPdo->client_id = $this->clientId;
        $aiQualityCheckCompanyTagsPdo->company_id = $this->companyId;
        $aiQualityCheckCompanyTagsPdo->user_id = $this->userId;
        $aiQualityCheckCompanyTagsPdo->customer_id = $customerId;
        $aiQualityCheckCompanyTagsPdo->record_id = $aiServiceProcessRecordPdo->record_id;
        $aiQualityCheckCompanyTagsPdo->tag_result = $response['answer'];
        $aiQualityCheckCompanyTagsPdo->task_name = \AiQualityCheckCompanyTagsModel::TAG_TYPE_TASK_MAP[$tagType];
        $aiQualityCheckCompanyTagsPdo->sns_type = json_encode($snsTypes);
        $aiQualityCheckCompanyTagsPdo->contact_begin_time = $contactBeginTime;
        $aiQualityCheckCompanyTagsPdo->contact_end_time = $contactEndTime;
        $aiQualityCheckCompanyTagsPdo->save();
    }

    public function formatJson($response)
    {
        $response['answer'] = str_replace('```json', '', $response['answer']);
        $response['answer'] = str_replace('```', '', $response['answer']);

        json_decode($response['answer']);
        if (json_last_error() != JSON_ERROR_NONE)
        {
            // 假如失败，尽可能获取json格式
            preg_match('/\{(?:[^{}]|(?R))*\}/', $response['answer'], $matches);

            json_decode($matches[0], true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $response['answer'] = $matches[0];
            } else {
                $response['answer'] = '{}';
            }
        }

        return $response;
    }

    /**
     * ● 时间范围：只考虑过去一个月内的聊天记录
     * ● 选择记录：从这些筛选出的记录中，选择最近六个聊天日（有聊天的天）聊天记录
     */
    public function generateChatRecord($customerId, $type, $contactKeyword)
    {
        // 创建一个当前日期的 DateTime 对象
        $currentDate = new \DateTime($this->dateTime);

        // 克隆当前日期对象，然后减去一个月
        $lastMonthDate = clone $currentDate;
        $lastMonthDate->modify('-1 month');

        // 格式化日期为字符串（可选）
        $startTime = $lastMonthDate->format('Y-m-d 00:00:00');
        $endTime = $currentDate->format('Y-m-d 23:59:59');

        $journeys = $this->getJourneys($type, $startTime, $endTime, $customerId, $contactKeyword);

        $chatRecords = $snsTypes = [];
        $contactBeginTime = date('Y-m-d H:i:s');
        $contactEndTime = '1970-01-01 00:00:01';

        $journeyIds = array_column($journeys, 'journey_id');

        $conversationList = [];
        if (!empty($journeyIds))
        {
            $conversationListPdo = new AiQualityCheckCompanyConversationList($this->clientId);
            $conversationListPdo->setJourneyId($journeyIds);
            $conversationListPdo->setOrderBy('day_last_message_time');
            $conversationListPdo->setOrder('desc');
            $conversationList = $conversationListPdo->find();
        }

        foreach ($conversationList as $conversation)
        {
            $chatRecords[] = [
                'date' => $conversation['contact_date'],
                'chat_records' => $conversation['content'],
                'channel' => $conversation['sns_type']
            ];

            // 获取第一次发消息的时间
            if ($contactBeginTime >= $conversation['day_first_message_time']) {
                $contactBeginTime = $conversation['day_first_message_time'];
            }

            // 获取最后消息的时间
            if ($contactEndTime <= $conversation['day_last_message_time']) {
                $contactEndTime = $conversation['day_last_message_time'];
            }

            $snsTypes[] = $conversation['sns_type'];
        }

        return [$chatRecords, $contactBeginTime, $contactEndTime, array_unique($snsTypes)];
    }

    public function getJourneys($type, $startTime, $endTime, $customerId, $contactKeyword)
    {
        $db = \PgActiveRecord::getDbByClientId($this->clientId);

        $journeys = [];
        switch ($type)
        {
            case \AiQualityCheckCompanyTagsModel::TAG_TYPE_KEY_CHARACTER:
                /**
                 * 取数逻辑： 需要获取（新询盘记录前两天）- 客户支付之间的数据
                 */
                $sql = <<<SQL
SELECT * 
FROM tbl_ai_quality_check_company_journey 
WHERE client_id = {$this->clientId}
  AND customer_id IN ({$customerId}) 
  AND company_id IN ({$this->companyId}) 
  AND 6 = ANY (keywords) 
LIMIT 1
SQL;
                $customerPayment = $db->createCommand($sql)->queryAll();
                $customerPayment = $customerPayment[0] ?? [];

                if (empty($customerPayment)) {
                    break;
                }

                $endContactTime = "{$customerPayment['contact_date']} 23:59:59";

                $sql = <<<SQL
SELECT * 
FROM tbl_ai_quality_check_company_journey 
WHERE client_id = {$this->clientId}
  AND customer_id IN ({$customerId}) 
  AND company_id IN ({$this->companyId}) 
  AND contact_date <= '{$endContactTime}'
  AND 1 = ANY (keywords) 
LIMIT 1
SQL;
                $newInquiry = $db->createCommand($sql)->queryAll();
                $newInquiry = $newInquiry[0] ?? [];

                if (empty($newInquiry)) {
                    // 没新询盘的话，往前去10个沟通日
                    $date = new \DateTime($customerPayment['contact_date']);
                    $date->modify('-10 days');
                    $startTime = $date->format('Y-m-d 00:00:00');

                    // 获取这两个记录中间的全部记录
                    $endTime = "{$customerPayment['contact_date']} 23:59:59";
                } else {
                    // 获取新询盘的前两天的时间
                    $date = new \DateTime($newInquiry['contact_date']);
                    $date->modify('-2 days');
                    $startTime = $date->format('Y-m-d 00:00:00');

                    // 获取这两个记录中间的全部记录
                    $endTime = "{$customerPayment['contact_date']} 23:59:59";
                }

                $journeyPdo = new AiQualityCheckCompanyJourneyList($this->clientId);
                $journeyPdo->setCustomerId($customerId);
                $journeyPdo->setCompanyId($this->companyId);
                $journeyPdo->setStartContactDate($startTime);
                $journeyPdo->setEndContactDate($endTime);
                $journeyPdo->setOrderBy('contact_time');
                $journeys = $journeyPdo->find();
                break;
            default:
                // 先取出6个沟通日，然后根据每个沟通日，然后获取第一个沟通日的零点和最后一个沟通日的23:59:59s之间的聊天记录
                $sql = <<<SQL
SELECT DISTINCT contact_date AS communication_date
FROM tbl_ai_quality_check_company_journey
WHERE contact_date >= '{$startTime}'
  AND contact_date <= '{$endTime}'
  AND company_id = {$this->companyId}
  AND customer_id = {$customerId}
  AND {$contactKeyword} = ANY (keywords) 
ORDER BY communication_date DESC
LIMIT 6
SQL;
                $communicationDates = \PgActiveRecord::getDbByClientId($this->clientId)->createCommand($sql)->queryAll();

                $firstCommunicationDate = reset($communicationDates);
                $firstCommunicationDate = !empty($firstCommunicationDate['communication_date']) ? $firstCommunicationDate['communication_date'] : '';

                $endCommunicationDate = end($communicationDates);
                $endCommunicationDate = !empty($endCommunicationDate['communication_date']) ? $endCommunicationDate['communication_date'] : '';


                if (!empty($firstCommunicationDate) && !empty($endCommunicationDate))
                {
                    $firstCommunicationDate = date('Y-m-d 00:00:00', strtotime($firstCommunicationDate));
                    $endCommunicationDate = date('Y-m-d 23:59:59', strtotime($endCommunicationDate));


                    $sql = <<<SQL
SELECT * 
FROM tbl_ai_quality_check_company_journey 
WHERE client_id = {$this->clientId}
  AND customer_id IN ({$customerId}) 
  AND company_id IN ({$this->companyId}) 
  AND contact_date >= '{$firstCommunicationDate}' 
  AND contact_date <= '{$endCommunicationDate}' 
  AND {$contactKeyword} = ANY (keywords) 
ORDER BY contact_date 
SQL;
                    $journeys = array_merge($journeys, $db->createCommand($sql)->queryAll());
                }
                break;
        }

        return $journeys;
    }

    /**
     * 分析模块
     */
    public function analysis($params)
    {
        $this->clientId = $params['clientId'];
        $this->companyId = $params['companyId'];
        $this->dateTime = $params['dateTime'];

        // 新增名单控制
        $privilegeService = PrivilegeService::getInstance($this->clientId);
        if (!$privilegeService->hasFunctional(\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_COMPANY_AI_PORTRAIT))
        {
            \LogUtil::info('analysisNotPrivilege', [
                'clientId' => $this->clientId,
                'companyId' => $this->companyId,
                'dateTime' => $this->dateTime,
            ]);
            return;
        }


        // 对今天创建的的谈单检测进行特征生成，生成日程的时候，需要全部的customer_id
        $contactDate = date('Y-m-d', strtotime($this->dateTime));

        try
        {
            //业务状态生成
            $this->handleBusinessStatus($this->clientId, $this->companyId, $contactDate);
            //跟进建议生成
            $this->handleFollowSuggestion($this->clientId, $this->companyId, $contactDate);
        }catch (\Throwable $exception)
        {
            \LogUtil::error("FollowSuggestion_ERROR",[
                'client_id' => $this->clientId,
                'company_id' => $this->companyId,
                'contact_date' => $contactDate,
                'msg' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString()
            ]);
        }
    }

    public function newBusinessStatus($clientId,$companyId,$contactDate) : void
    {
        /// 今日有沟通且没有历史业务状态的(user,customer),直接调用GPT,结束
        $noHistoryPairs = Helper::getContactButNotContainBusinessStatusPair($clientId, $companyId, $contactDate);
        foreach ($noHistoryPairs as $pair)
        {
            $userId = $pair[0];
            $customerId = $pair[1];

            //调用GPT生成一个业务状态
            $this->generateBusinessStatus($clientId, $companyId, $userId, $customerId, $contactDate);
        }
    }

    public function updateBusinessStatus($clientId, $companyId, $contactDate) : void
    {
        /// 查询出company所有的业务状态,根据(user,customer)分组,查看是否在当日有聊天的user+customer中
        /// 过滤掉当日新生成的业务状态
        /// 营销状态->商机状态 / 商机状态->营销状态,状态转移
        $businessStatusPdo = new AiQualityCheckAgentAnalysisList($clientId);
        $businessStatusPdo->setCompanyId($companyId);
        $businessStatusPdo->setEndContactDate(date('Y-m-d',strtotime('-1 day',strtotime($contactDate)))); //排除当日新生成的
        $businessStatusPdo->setAgentId(self::QC_BUSINESS_STATUS_ID);
        $businessStatusPdo->setFields(['user_id','customer_id','analyze_result','analysis_id']);
        $businessStatusPdo->setOrderBy('create_time'); //取每个user+customer最新的
        $businessStatusPdo->setOrder('asc');
        $businessStatusList = $businessStatusPdo->find();

        //获取每个user+customer最近的业务状态
        $latestBusinessStatusWithPair = [];
        foreach ($businessStatusList as $businessStatusInfo)
        {
            $analyzeResult = $businessStatusInfo['analyze_result'];
            $analyzeResult = json_decode($analyzeResult, true);
            $userId = $businessStatusInfo['user_id'];
            $customerId = $businessStatusInfo['customer_id'];

            $key = "{$userId}-{$customerId}";
            $businessStatusInfo['analyze_result'] = $analyzeResult;
            $latestBusinessStatusWithPair[$key] = $businessStatusInfo;
        }

        foreach ($latestBusinessStatusWithPair as $key => $businessStatusInfo)
        {
            $analysisId = $businessStatusInfo['analysis_id'];
            $userId = $businessStatusInfo['user_id'];
            $customerId = $businessStatusInfo['customer_id'];
            $oldUnansweredTimes = $businessStatusInfo['analyze_result']['consecutive_unanswered_times'] ?? 0;
            $oldNokeywordsTimes = $businessStatusInfo['analyze_result']['consecutive_no_keyword_times'] ?? 0;

            //如果今日没有聊天记录,直接判断上一个沟通日距离当日是否超过60日
            //上一次距离今日的沟通间隔
            $contactGap = 0;
            $conversationPdo = new AiQualityCheckCompanyConversationList($clientId);
            $conversationPdo->setCompanyId($companyId);
            $conversationPdo->setUserId($userId);
            $conversationPdo->setCustomerId($customerId);
            $conversationPdo->setEndContactDate(date('Y-m-d',strtotime('-1 day',strtotime($contactDate))));
            $conversationPdo->setOrderBy('contact_date');
            $conversationPdo->setOrder('desc');
            $conversationPdo->setLimit(1);
            $conversation = $conversationPdo->find();
            if(!empty($conversation)) {
                $lastDateTime = array_values($conversation)[0]['contact_date'];
                $dateTime1 = new \DateTime($lastDateTime);
                $dateTime2 = new \DateTime($contactDate);
                $interval = $dateTime1->diff($dateTime2);
                $contactGap = abs($interval->days);
            }

            //当日沟通
            $conversationPdo = new AiQualityCheckCompanyConversationList($clientId);
            $conversationPdo->setCompanyId($companyId);
            $conversationPdo->setUserId($userId);
            $conversationPdo->setCustomerId($customerId);
            $conversationPdo->setContactDate($contactDate);
            $conversation = $conversationPdo->count();
            if($conversation == 0) {
                //间隔大于60天直接转变为营销状态
                if($contactGap >= 60) {
                    Helper::createNewBusinessStatus($clientId, $companyId, $userId, $customerId, $contactDate, \common\library\ai_portrait_analysis\Constant::BUSINESS_STATUS_MARKING_PHASE, $oldUnansweredTimes + 1, $oldNokeywordsTimes + 1);
                }else{
                    Helper::updateBusinessStatusTimes($analysisId,$oldUnansweredTimes + 1, $oldNokeywordsTimes + 1);
                }
                //没有聊天记录,结束
                continue;
            }

            $status = $businessStatusInfo['analyze_result']['status'] ?? 0;
            if($status == \common\library\ai_portrait_analysis\Constant::BUSINESS_STATUS_ACTIVE_OPPORTUNITY) {
                //商机阶段
                [$unansweredTimes, $noKeywordsTime] = Helper::findUnansweredAndNoKeywordsTimes($clientId, $companyId, $userId, $customerId, $contactDate, $contactDate);
                if($unansweredTimes + $oldUnansweredTimes >= 3 || $noKeywordsTime + $oldNokeywordsTimes >=5 || $contactGap >= 30) {
                    //调用gpt生成新的业务状态
                    $this->generateBusinessStatus($clientId, $companyId, $userId, $customerId, $contactDate, $unansweredTimes + $oldUnansweredTimes, $noKeywordsTime + $oldNokeywordsTimes);
                }else{
                    //更新累加值
                    Helper::updateBusinessStatusTimes($analysisId,$unansweredTimes + $oldUnansweredTimes,$noKeywordsTime + $oldNokeywordsTimes);
                }
            }else if ($status == \common\library\ai_portrait_analysis\Constant::BUSINESS_STATUS_MARKING_PHASE) {
                //营销阶段
                //有客户回复 且 有关键词 <===> 累计未回复为0 且 累计没有关键词为0
                [$unansweredTimes, $noKeywordsTime] = Helper::findUnansweredAndNoKeywordsTimes($clientId, $companyId, $userId, $customerId, $contactDate, $contactDate);
                if($unansweredTimes == 0 && $noKeywordsTime == 0) {
                    $this->generateBusinessStatus($clientId, $companyId, $userId, $customerId, $contactDate,0,0);
                }else{
                    //更新累加值
                    Helper::updateBusinessStatusTimes($analysisId,$unansweredTimes + $oldUnansweredTimes,$noKeywordsTime + $oldNokeywordsTimes);
                }
            }
        }
    }

    public function handleBusinessStatus($clientId, $companyId, $contactDate) : void
    {
        //新建业务状态
        $this->newBusinessStatus($this->clientId, $this->companyId, $contactDate);
        //更新业务状态
        $this->updateBusinessStatus($this->clientId, $this->companyId, $contactDate);
    }

    public function handleFollowSuggestion($clientId, $companyId, $contactDate) : void
    {
        //新建跟进建议
        $this->newFollowSuggestion($clientId, $companyId, $contactDate);
        //过期建议重新生成
        $this->reopenFollowSuggestion($clientId, $companyId, $contactDate);
    }

    public function newFollowSuggestion($clientId, $companyId, $contactDate) : void
    {
        //有聊天记录且处于商机阶段
        $companyConversationPdo = new AiQualityCheckCompanyConversationList($clientId);
        $companyConversationPdo->setCompanyId($companyId);
        $companyConversationPdo->setContactDate($contactDate);
        $companyConversationPdo->setFields(['user_id','customer_id']);
        $companyConversationPdo->setSplitByUserAndCustomer(true);
        $conversationList = $companyConversationPdo->find();
        if(empty($conversationList)) return;

        foreach ($conversationList as $pair => $conversations)
        {
            $pair = explode("-", $pair);
            $userId = $pair[0];
            $customerId = $pair[1];

            $aiPortraitBusinessStatusPdo = new AiQualityCheckAgentAnalysisList($clientId);
            $aiPortraitBusinessStatusPdo->setCompanyId($companyId);
            $aiPortraitBusinessStatusPdo->setUserId($userId);
            $aiPortraitBusinessStatusPdo->setCustomerId($customerId);
            $aiPortraitBusinessStatusPdo->setAgentId(self::QC_BUSINESS_STATUS_ID);
            $aiPortraitBusinessStatusPdo->setOrderBy('contact_date');
            $aiPortraitBusinessStatusPdo->setOrder('desc');
            $aiPortraitBusinessStatusPdo->setLimit(1);
            $businessStatusInfo = $aiPortraitBusinessStatusPdo->find();
            if(empty($businessStatusInfo) || empty($businessStatusInfo[0]['analyze_result']) || json_decode($businessStatusInfo[0]['analyze_result'],true)['status'] != \common\library\ai_portrait_analysis\Constant::BUSINESS_STATUS_ACTIVE_OPPORTUNITY) {
                continue;
            }

            //如果已经有行动建议,将原本的行动建议废弃
            $aiPortraitAnalysisDetailPdo = new AiPortraitAnalysisDetailFilter($clientId);
            $aiPortraitAnalysisDetailPdo->wheres([
                'create_user' => $userId,
                'refer_id' => $companyId,
                'biz_type' => \common\library\ai_portrait_analysis\Constant::SUGGESTIONS_BIZ_TYPE_COMMUNICATE,
                'analyze_type' => \common\library\ai_portrait_analysis\Constant::ANALYZE_TYPE_ACTION_SUGGESTIONS,
            ]);
            $details = $aiPortraitAnalysisDetailPdo->find();
            $details->getFormatter()->displayFields(['detail_id']);
            $detailIds = $details->getAttributes() ?? [];
            $detailIds = array_column($detailIds, 'detail_id');

            //常规生成建议
            $this->normalGenerateFollowSuggestion($clientId, $companyId, $userId, $customerId, $contactDate, $detailIds);
        }
    }


    public function reopenFollowSuggestion($clientId, $companyId, $contactDate) : void
    {
        //查询company对应的所有行动建议
        $aiPortraitAnalysisDetailFilter = new AiPortraitAnalysisDetailFilter($clientId);
        $aiPortraitAnalysisDetailFilter->module = \Constants::TYPE_COMPANY;
        $aiPortraitAnalysisDetailFilter->biz_type = \common\library\ai_portrait_analysis\Constant::SUGGESTIONS_BIZ_TYPE_COMMUNICATE;
        $aiPortraitAnalysisDetailFilter->analyze_type = \common\library\ai_portrait_analysis\Constant::ANALYZE_TYPE_ACTION_SUGGESTIONS;
        $aiPortraitAnalysisDetailFilter->biz_status = \common\library\ai_portrait_analysis\Constant::SUGGESTIONS_BIZ_STATUS_UNCOMPLETED;
        $aiPortraitAnalysisDetailFilter->refer_id = $companyId;
        $batch = $aiPortraitAnalysisDetailFilter->find();
        $batch->getFormatter()->displayFields(["data","detail_id"]);
        $data = $batch->getAttributes() ?? [];

        if (!empty($data)) {
            //过滤出follow_up_time == contact_data - 1
            $dateToday = strtotime($contactDate);
            $targetTime = strtotime('-1 day', $dateToday);
            $expiredSuggestion = array_values(array_filter($data,function ($item) use ($targetTime) {
                $followUpTime = $item['data']['follow_up_time'];
                return $followUpTime == $targetTime;
            }));
            if(empty($expiredSuggestion)) return;

            foreach ($expiredSuggestion as $item)
            {
                $journeyId = $item['data']['journey_id'];
                $companyJourneyPdo = new AiQualityCheckCompanyJourneyList($clientId);
                $companyJourneyPdo->setCompanyId($companyId);
                $companyJourneyPdo->setJourneyIds([$journeyId]);
                $companyJourneyPdo->setSkipQcPoolCheck(true);
                $companyJourneyPdo->setFields(['user_id','customer_id','journey_id','contact_date']);
                $companyJourneyPdo->setLimit(1);
                $journeyObj = $companyJourneyPdo->find();
                if(empty($journeyObj)) continue;

                $userId = $journeyObj[0]['user_id'];
                $customerId = $journeyObj[0]['customer_id'];

                //条件1:今日没有沟通
                $companyConversationPdo = new AiQualityCheckCompanyConversationList($clientId);
                $companyConversationPdo->setCompanyId($companyId);
                $companyConversationPdo->setUserId($userId);
                $companyConversationPdo->setCustomerId($customerId);
                $companyConversationPdo->setContactDate($contactDate);
                $count = $companyConversationPdo->count();
                if($count > 0) continue;

                //条件2:没有触发过重新生成
                $agentAnalysisPdo = new AiQualityCheckAgentAnalysisList($clientId);
                $agentAnalysisPdo->setCompanyId($companyId);
                $agentAnalysisPdo->setJourneyId($journeyId);
                $agentAnalysisPdo->setAgentId(self::QC_SUGGESTION_AGENT_ID);
                $agentAnalysisPdo->setFields(['analyze_result', 'contact_date']);
                $agentAnalysisPdo->setLimit(1);
                $agentAnalysisObj = $agentAnalysisPdo->find();
                if(empty($agentAnalysisObj)) continue;
                $agentAnalysisObj = $agentAnalysisObj[0];
                $analyzeResult = json_decode($agentAnalysisObj['analyze_result'], true);
                if(!empty($analyzeResult["rebuild_follow_suggestion"])) continue;

                $lastContactDate = $agentAnalysisObj['contact_date'];
                $detailId = $item["detail_id"];
                $this->reGenerateFollowSuggestion($clientId, $companyId, $userId, $customerId, $contactDate,$journeyId,$lastContactDate,$detailId);
            }
        }
    }

    /**
     * 生成业务状态
     * @return string "business_status" 业务状态
     */
    public function generateBusinessStatus($clientId, $companyId, $userId, $customerId,$contactDate , $oldUnansweredTimes = null, $oldNoKeywordsTime = null) : void
    {
        $systemPrompt = $this->systemPromptMap["business.status.system.prompt"] ?? "";
        if(empty($systemPrompt)) {
            $systemPrompt = <<<TEXT
# TASK
You are an experienced B2B international trade expert. Your task is to analyze the recent communications between the salesperson and the customer, determine the current business status between the parties, and explain the basis for your judgment.

# BUSINESS STATUS OPTIONS
- Marketing Phase: There is no active transaction between the buyer and seller; the salesperson is attempting to establish new business contacts.
- Active Opportunity: There is a clearly ongoing business transaction (opportunity/order) between both parties.

# CONSTRAINTS
1. The returned business_status must be either "marketing_phase" or "active_opportunity".
2. In the reasoning, briefly explain the basis for your judgment in 50 words or less.
3. You must return the result in JSON format shown in the <OUTPUT FORMAT>, without any additional content.

# OUTPUT FORMAT
{
  "business_status": str,
  "reasoning": str
}
TEXT;
        }

        $userPrompt = $this->userPromptMap["business.status.user.prompt"] ?? "";
        if(empty($userPrompt)) {
            $userPrompt = <<<TEXT
{{summaryList}}
TEXT;
        }

        $llmModel = $this->promptConfig["business.status.service"] ?? "qwen-max-latest";

        //最近60天内的英文总结
        $todayTime = strtotime($contactDate);
        $agentAnalysisPdo = new AiQualityCheckAgentAnalysisList($clientId);
        $agentAnalysisPdo->setCompanyId($companyId);
        $agentAnalysisPdo->setUserId($userId);
        $agentAnalysisPdo->setCustomerId($customerId);
        $agentAnalysisPdo->setStartContactDate(date('Y-m-d',strtotime('-60 day',$todayTime)));
        $agentAnalysisPdo->setEndContactDate($contactDate);
        $agentAnalysisPdo->setAgentId(self::QC_JOURNEY_GENERATION_AGENT_ID);
        $agentAnalysisPdo->setOrderBy('contact_date');
        $agentAnalysisPdo->setOrder('asc');
        $agentAnalysisList = $agentAnalysisPdo->find();

        if(empty($agentAnalysisList)) {
            \LogUtil::error("AiCompanyQualityCheck_generateBusinessStatus_NoJourneyId" , [
                "client_id" => $clientId,
                "company_id" => $companyId,
                "user_id" => $userId,
                "customer_id" => $customerId,
                "contact_date" => $contactDate
            ]);
            return ;
        }

        $summaryList = [];
        foreach ($agentAnalysisList as $item)
        {
            $analyzeResult = $item["analyze_result"] ?? "";
            $analyzeResult = json_decode($analyzeResult, true);
            $summary = $analyzeResult["summary"] ?? "";
            $itemDate = $item['contact_date'] ?? "";
            $summaryList[] = <<<TEXT
{$itemDate}:$summary
TEXT;
        }
        $summaryList = implode(PHP_EOL, $summaryList);
        $journeyId = array_values($agentAnalysisList)[count($agentAnalysisList) - 1]['journey_id'];
        $question = $this->replacePlaceholders($userPrompt, [
            "summaryList" => $summaryList
        ]);

        //调用GPT
        $response = $this->generateAnalysis($llmModel, $systemPrompt, $question, ["journey_id" => $journeyId, "user_id" => $userId, "customer_id" => $customerId, "contact_date" => $contactDate], "business_status");
        $answer = $this->formatResponseAnswer($response["answer"]);
        $answer = json_decode($answer, true);

        $status =  !empty($answer) && !empty($answer["business_status"]) ? $answer["business_status"] : "";
        $status = \common\library\ai_portrait_analysis\Constant::CONVERT_STRING_TO_BUSINESS_STATUS_MAP[$status] ?? 0;

        if($status != 0){
            if($oldUnansweredTimes == null || $oldNoKeywordsTime == null) {
                $leftTime = date('Y-m-d',strtotime('-60 day',strtotime($contactDate)));
                [$unansweredTimes, $noKeywordsTime] = Helper::findUnansweredAndNoKeywordsTimes($clientId, $companyId, $userId, $customerId, $leftTime,$contactDate);
            }else{
                $unansweredTimes = $oldUnansweredTimes;
                $noKeywordsTime = $oldNoKeywordsTime;
            }
            Helper::createNewBusinessStatus($clientId,$companyId,$userId,$customerId,$contactDate,$status,$unansweredTimes,$noKeywordsTime);
        }
    }

    //常规生成跟进建议
    public function normalGenerateFollowSuggestion($clientId, $companyId, $userId, $customerId, $contactDate, $oldDetailIds = []) : void
    {
        //60天内(不包括今天)的summary
        $agentAnalysisPdo = new AiQualityCheckAgentAnalysisList($clientId);
        $agentAnalysisPdo->setCompanyId($companyId);
        $agentAnalysisPdo->setUserId($userId);
        $agentAnalysisPdo->setCustomerId($customerId);
        $agentAnalysisPdo->setAgentId(self::QC_JOURNEY_GENERATION_AGENT_ID);
        $agentAnalysisPdo->setStartContactDate(date('Y-m-d',strtotime('-60 day',strtotime($contactDate))));
        $agentAnalysisPdo->setEndContactDate(date('Y-m-d',strtotime('-1 day',strtotime($contactDate))));
        $agentAnalysisPdo->setOrderBy('contact_date');
        $agentAnalysisPdo->setOrder('asc');
        $agentAnalysisList = $agentAnalysisPdo->find();
        $summaryList = [];
        foreach ($agentAnalysisList as $agentAnalysis)
        {
            $analysisDate = $agentAnalysis['contact_date'] ?? "";
            $analyzeResult = $agentAnalysis['analyze_result'] ?? "";
            $analyzeResult = json_decode($analyzeResult, true);
            $summary = $analyzeResult['summary'] ?? "";
            $summaryList[] = <<<TEXT
{$analysisDate}:{$summary}
TEXT;
        }
        $summaryList = implode(PHP_EOL, $summaryList);

        //今日的半结构化数据
        $segments = [];
        $journeyId = 0;
        $agentAnalysisPdo = new AiQualityCheckAgentAnalysisList($clientId);
        $agentAnalysisPdo->setCompanyId($companyId);
        $agentAnalysisPdo->setUserId($userId);
        $agentAnalysisPdo->setCustomerId($customerId);
        $agentAnalysisPdo->setAgentId(self::QC_EVENT_ANNOTATION_AGENT_ID);
        $agentAnalysisPdo->setContactDate($contactDate);
        $agentAnalysis = $agentAnalysisPdo->find();
        if(!empty($agentAnalysis)) {
            $journeyId = $agentAnalysis[0]['journey_id'];
            $analyzeResult = json_decode($agentAnalysis[0]['analyze_result'], true);
            $segments = $analyzeResult['conversation_content'] ?? "";
            $segments = json_encode($segments, JSON_UNESCAPED_UNICODE);
        }

        $weekday = date("l");
        $systemPrompt = <<<TEXT
# 任务
你是国际贸易领域的专家，你的任务是基于近期的沟通总结和今日的沟通内容，提供跟进建议指导销售下次如何跟进该客户。

# 跟进建议要求
你生成的跟进建议需要包括:
  - 订单进展：分析当前整体订单进展情况
  - 跟进事项：下次跟进客户需要优先沟通的事项
  - 跟进期限：建议跟进期限，格式为"yyyy-mm-dd"
  - 理由：基于近期沟通总结和今日沟通内容，说明为什么需要优先沟通该事项
  - 沟通策略：具体的沟通策略，如何开启话题并引导进入正题，推进订单进展

# 跟进事项选择原则
1. 优先选择与订单进展直接相关的事项，如报价确认、提供订单所需信息等，避免选择与业务无关的事项或已经充分讨论过的话题
2. 不要针对客户已经提供的信息进行反复确认，除非客户明确要求或情况发生变化,避免招致客户反感
3. 避免建议销售要求客户澄清已经可见的信息，如聊天中的图片内容、已发送的文件或链接等，销售能够直接获取这些信息
4. 如果当前订单进展顺利，没有明确需要跟进的事项，应避免创造不必要的沟通话题，建议在合理宽松的时间范围内触达客户并询问客户是否需要帮助即可
5. 对于已进入履约阶段（生产、运输等）的订单，可定期主动向客户同步最新进展（如生产状态、物流情况等），以维持良好的沟通频率和客户信任度

# 跟进期限设置原则
1. 优先遵循对话中明确提及的时间期限
2. 若对话中未明确时间期限，则根据事项紧急程度和重要性设置：
    - 跟进事项阻塞客户下单流程，且上下文明确体现客户急需回复：1天内跟进
    - 跟进事项阻塞客户下单流程，但上下文未明确体现客户急需回复：3天内跟进
    - 事项不阻塞客户下单流程：7天内跟进
    - 订单进展顺利，没有明确需要跟进的事项：14天进行一次跟进
3. 若订单已进入履约阶段，除紧急事件外，14天进行一次跟进

# 沟通策略生成原则
1. 对于非常紧急的事项，直入主题，不要拐弯抹角；非紧急事项，则可以适当铺垫，不要显得过于急迫
2. 沟通策略应避免增加销售负担，避免提出销售难以完成的要求，例如"打电话"，"提供详细文件"等，而应该侧重于销售下次跟进客户时应该如何沟通
3. 避免在沟通策略中包含具体的沟通渠道，如"邮件"，"电话"，"Whatsapp"等
4. 沟通策略必须是针对客户的直接沟通内容，不要将针对第三方的工作（如询问工厂生产情况、与物流公司确认发货等）作为与客户的沟通策略，你应当假定销售已经完成所有第三方的准备工作

# 限制条件
1. 不要虚构任何沟通中没有提及的内容
2. 理由中不要包含和跟进事项无关的内容
3. 避免在理由中使用"今日"、"昨日"、"上周"等相对时间表述，应统一使用"5月10日"等具体日期，确保时间参考明确
4. 跟进事项应简洁明了，控制在20字以内；理由应具体详实，控制在100字以内；跟进策略应清晰明确，控制在100字以内
5. 以<输出格式>所示的JSON格式返回结果，不要包含任何其他内容

# 输出格式
{
  "订单进展": str,
  "跟进事项": str,
  "跟进期限": str,
  "理由": str,
  "沟通策略": str
}

# 工作流程
1. 基于销售和客户的近期沟通内容，分析当前整体的订单进展情况
2. 确定当前为推动订单进展需要优先跟进的事项，给出跟进期限和理由
3. 基于选定事项，思考销售下一次跟进客户应该怎么沟通，并给出落地性强的沟通策略
TEXT;
        $userPrompt = <<<TEXT
# RECENT COMMUNICATIONS
{{recent_summaries_excluding_today}}

# TODAY'S COMMUNICATIONS
-date: {{date_today}}
-weekday: {{week_day}}
-content: {{conversation_segments_today}}
TEXT;
        $question = $this->replacePlaceholders($userPrompt,[
            "recent_summaries_excluding_today" => $summaryList,
            "date_today" => $contactDate,
            "week_day" => $weekday,
            "conversation_segments_today" => $segments
        ]);

        $llmModel = "qwen-max-latest";

        $response = $this->generateAnalysis($llmModel, $systemPrompt, $question,["journey_id" => $journeyId,"user_id" => $userId,"customer_id" => $customerId,"contact_date" => $contactDate],"normal_follow_suggestion");
        $answer = $this->formatResponseAnswer($response['answer']);
        $answer = json_decode($answer, true);
        if(!empty($answer)) {
            $data = $this->buildFollowSuggestionData($answer, $clientId, $companyId, $contactDate);
            $data += ["journey_id" => $journeyId];
            $this->saveDetail($companyId,$userId,$data,\common\library\ai_portrait_analysis\Constant::SUGGESTIONS_BIZ_TYPE_COMMUNICATE,date('Y-m-d',$data["follow_up_time"]));

            //原本的行动建议需要作废
            if(!empty($oldDetailIds)) {
                foreach ($oldDetailIds as $oldDetailId) {
                    $aiPortraitAnalysisDetailApi = new AiPortraitAnalysisDetailApi($clientId, \Constants::TYPE_COMPANY);
                    $aiPortraitAnalysisDetailApi->updateBizStatus($oldDetailId, \common\library\ai_portrait_analysis\Constant::SUGGESTIONS_BIZ_STATUS_ABANDONED);
                }
                //删除Java工作台数据
                $feedPdo = new \common\library\todo\Feed(\common\library\todo\TodoConstant::OBJECT_TYPE_AI_COMPANY_FOLLOW_SUGGESTION, \common\library\todo\TodoConstant::TODO_TYPE_COMPANY_FOLLOW_SUGGESTION);
                $feedPdo->setUserId($userId);
                $feedPdo->setClientId($clientId);
                $feedPdo->updateFeedStatusByFeedId(TodoConstant::FEED_STATUS_DELETE,$oldDetailIds);
            }
        }
    }

    //过期重生成跟进建议
    public function reGenerateFollowSuggestion($clientId, $companyId, $userId, $customerId, $contactDate, $journeyId, $lastContactDate, $detailId) : void
    {
        //最近60天聊天旅程
        $recentCommunications = [];
        $agentAnalysisPdo = new AiQualityCheckAgentAnalysisList($clientId);
        $agentAnalysisPdo->setCompanyId($companyId);
        $agentAnalysisPdo->setUserId($userId);
        $agentAnalysisPdo->setCustomerId($customerId);
        $agentAnalysisPdo->setAgentId(self::QC_JOURNEY_GENERATION_AGENT_ID);
        $agentAnalysisPdo->setStartContactDate(date('Y-m-d',strtotime('-60 day', strtotime($contactDate))));
        $agentAnalysisPdo->setEndContactDate($contactDate);
        $agentAnalysisPdo->setFields(['analyze_result','contact_date']);
        $agentAnalysisPdo->setOrder('contact_date');
        $agentAnalysisPdo->setOrder('asc');
        $agentAnalysisList = $agentAnalysisPdo->find();
        foreach ($agentAnalysisList as $agentAnalysis)
        {
            $chatDay = $agentAnalysis['contact_date'];
            $analyzeResult = $agentAnalysis['analyze_result'];
            $analyzeResult = json_decode($analyzeResult, true);
            $summary = $analyzeResult["summary"] ?? "";
            $recentCommunications[] = "{$chatDay}:$summary";
        }
        $recentCommunications = implode(PHP_EOL, $recentCommunications);

        //上次生成的跟进建议完整结构体
        $previousFollowUpSuggestion = [];
        $agentAnalysisPdo = new AiQualityCheckAgentAnalysisList($clientId);
        $agentAnalysisPdo->setCompanyId($companyId);
        $agentAnalysisPdo->setUserId($userId);
        $agentAnalysisPdo->setCustomerId($customerId);
        $agentAnalysisPdo->setAgentId(self::QC_SUGGESTION_AGENT_ID);
        $agentAnalysisPdo->setJourneyId($journeyId);
        $agentAnalysisPdo->setFields(['analyze_result']);
        $agentAnalysisPdo->setOrderBy('contact_date');
        $agentAnalysisPdo->setOrder('desc');
        $agentAnalysisPdo->setLimit(1);
        $agentAnalysisInfo = $agentAnalysisPdo->find();
        if(!empty($agentAnalysisInfo)) {
            $agentAnalysisInfo = $agentAnalysisInfo[0];
            $analyzeResult = json_decode($agentAnalysisInfo['analyze_result'], true);
            $previousFollowUpSuggestion = $analyzeResult["normal_follow_suggestion"] ?? [];
        }
        $previousFollowUpSuggestion = json_encode($previousFollowUpSuggestion,JSON_UNESCAPED_UNICODE);

        $systemPrompt = <<<TEXT
# 任务
你是一位专业的外贸销售顾问，负责在跟进建议到期后（销售与客户在此期间未有新的交流）生成新的跟进建议。

# 背景
- 之前的跟进建议已经过期，但销售尚未与客户进行沟通
- 需要基于最新情况重新评估并生成新的跟进建议，以确保销售能够及时跟进客户

# 输入
你将收到以下信息：
1. 近期沟通总结：包含销售与客户之间的历史沟通记录摘要
2. 之前的跟进建议：包含上次生成的跟进事项、期限和理由

# 考虑因素
1. 时间流逝对业务的影响：
    - 长时间未沟通可能导致客户兴趣降低或转向竞争对手
    - 订单进展可能因缺乏跟进而停滞
    - 客户可能已经自行解决问题或改变需求

2. 跟进策略调整：
    - 考虑是否需要提高跟进优先级
    - 评估是否需要改变沟通方式或内容
    - 思考如何重新激活客户兴趣

3. 跟进期限重新设置：
    - 考虑到已经过期的情况，新的期限应更加紧迫
    - 对于已经延误的重要事项，应建议尽快跟进

# 跟进事项选择原则
1. 优先选择与订单进展直接相关的事项，如报价确认、提供订单所需信息等，避免选择与业务无关的事项或已经充分讨论过的话题
2. 不要针对客户已经提供的信息进行反复确认，除非客户明确要求或情况发生变化,避免招致客户反感
3. 避免建议销售要求客户澄清已经可见的信息，如聊天中的图片内容、已发送的文件或链接等，销售能够直接获取这些信息
4. 如果当前订单进展顺利，没有明确需要跟进的事项，应避免创造不必要的沟通话题，建议在合理宽松的时间范围内触达客户并询问客户是否需要帮助即可
5. 对于已进入履约阶段（生产、运输等）的订单，可定期主动向客户同步最新进展（如生产状态、物流情况等），以维持良好的沟通频率和客户信任度

# 跟进期限设置原则
1. 考虑到之前的跟进建议已经过期，新的跟进期限应更加紧迫：
    - 对于重要且紧急的事项：建议1-2天内跟进
    - 对于重要但不紧急的事项：建议3-5天内跟进
    - 对于常规维护性质的跟进：建议7天内跟进
2. 若订单已进入履约阶段，除紧急事件外，7-10天进行一次跟进

# 沟通策略生成原则
1. 针对长时间未沟通的情况，提供重新开启对话的自然方式，避免让客户感到突兀
2. 对于非常紧急的事项，建议直入主题；非紧急事项，则可以适当铺垫
3. 沟通策略应避免增加销售负担，避免提出销售难以完成的要求
4. 避免在沟通策略中包含具体的沟通渠道，如"邮件"，"电话"，"Whatsapp"等
5. 沟通策略必须是针对客户的直接沟通内容，不要将针对第三方的工作作为与客户的沟通策略

# 限制条件
1. 不要虚构任何沟通中没有提及的内容
2. 理由中不要包含和跟进事项无关的内容
3. 避免在理由中使用"今日"、"昨日"、"上周"等相对时间表述，应统一使用具体日期
4. 跟进事项应简洁明了，控制在20字以内；理由应具体详实，控制在100字以内；跟进策略应清晰明确，控制在100字以内
5. 以<输出格式>所示的JSON格式返回结果，不要包含任何其他内容

# 输出格式
{
  "订单进展": str,
  "跟进事项": str,
  "跟进期限": str,
  "理由": str,
  "沟通策略": str
}

# 工作流程
1. 分析销售和客户的历史沟通内容，评估当前订单进展情况
2. 考虑长时间未沟通的影响，确定新的优先跟进事项
3. 设置更紧迫的跟进期限
4. 提供适合重新开启对话的沟通策略
5. 按照输出格式生成新的跟进建议
TEXT;
        $userPrompt = <<<TEXT
# RECENT COMMUNICATIONS
{{recent_communications}}

# PREVIOUS FOLLOW-UP SUGGESTION
{{previous_follow_up_suggestion}}

# TODAY'S DATE
{{today_date}}
TEXT;
        $question = $this->replacePlaceholders($userPrompt, [
            "recent_communications" => $recentCommunications,
            "previous_follow_up_suggestion" => $previousFollowUpSuggestion,
            "today_date" => $contactDate
        ]);
        $llmModel = "qwen-max-latest";

        //调用GPT
        $response = $this->generateAnalysis($llmModel, $systemPrompt, $question, ["journey_id" => $journeyId, "user_id" => $userId, "customer_id" => $customerId, "contact_date" => $lastContactDate], 'rebuild_follow_suggestion');
        $answer = $this->formatResponseAnswer($response['answer']);
        $answer = json_decode($answer, true);
        if(!empty($answer)) {
            $data = $this->buildFollowSuggestionData($answer, $clientId, $companyId, $contactDate) + ["journey_id" => $journeyId];
            $this->saveDetail($companyId,$userId,$data,\common\library\ai_portrait_analysis\Constant::SUGGESTIONS_BIZ_TYPE_COMMUNICATE,date('Y-m-d',$data["follow_up_time"]));

            //旧detail作废
            $aiPortraitAnalysisDetailApi = new AiPortraitAnalysisDetailApi($clientId, \Constants::TYPE_COMPANY);
            $aiPortraitAnalysisDetailApi->updateBizStatus($detailId, \common\library\ai_portrait_analysis\Constant::SUGGESTIONS_BIZ_STATUS_ABANDONED);

            $feedPdo = new \common\library\todo\Feed(\common\library\todo\TodoConstant::OBJECT_TYPE_AI_COMPANY_FOLLOW_SUGGESTION, \common\library\todo\TodoConstant::TODO_TYPE_COMPANY_FOLLOW_SUGGESTION);
            $feedPdo->setUserId($userId);
            $feedPdo->setClientId($clientId);
            $feedPdo->updateFeedStatusByFeedId(TodoConstant::FEED_STATUS_DELETE,[$detailId]);
        }
    }

    private function buildFollowSuggestionData($answer, $clientId, $companyId, $contactDate) : array
    {
        //优先级
        $ddl = $answer["跟进期限"] ?? "";
        $dateTime1 = new \DateTime($ddl);
        $dateTime2 = new \DateTime($contactDate);
        $interval = $dateTime1->diff($dateTime2);
        $diff = abs($interval->days);
        if($diff <= 3) {
            $urgencyDegree = AiAgentConstants::AI_PORTRAIT_URGENCY_DEGREE_HIGH;
        }else if ($diff <= 7) {
            $urgencyDegree = AiAgentConstants::AI_PORTRAIT_URGENCY_DEGREE_MIDDLE;
        }else  {
            $urgencyDegree = AiAgentConstants::AI_PORTRAIT_URGENCY_DEGREE_LOW;
        }

        //跟进事项
        $details["cn"] = $answer["跟进事项"] ?? "";

        //buyer_quality_score
        $buyerQualityScore = 0;
        $portraitAnalysisPdo = new AiPortraitAnalysisApi($clientId,\Constants::TYPE_COMPANY);
        $analyzeInfo = $portraitAnalysisPdo->getAnalysisInfo($companyId);
        if(!empty($analyzeInfo)) {
            $analyzeInfo = $analyzeInfo[0];
            $buyerQualityScore = $analyzeInfo['analyze_info']['rank_info']['buyer_quality_score'] ?? 0;
        }

        //沟通策略
        $strategyInfo = [
            "cn" => $answer["沟通策略"] ?? ""
        ];

        return [
            "follow_up_suggestions" => $details,
            "strategy" => $strategyInfo,
            "urgency_degree" => $urgencyDegree,
            "recommend_reason" => $answer["理由"] ?? "",
            "follow_up_time" => strtotime(date('Y-m-d 00:00:00',strtotime($answer["跟进期限"] ?? "1970-01-01"))),
            'buyer_quality_score' => intval($buyerQualityScore * 100),
        ];
    }

    /**
     * 行动建议分析处理
     */
    public function handleSuggestionAnalysis($journey)
    {
        list($recentCommunication, $lastMessages, $chatRecords, $lastFollowSuggestion) = \common\library\ai_agent\Helper:: generateNewRecords($this->clientId, $journey);

        // 谈单进展分析模块
        $negotiationProgressMemory = $this->negotiationProgress($recentCommunication, $lastMessages, $chatRecords, $lastFollowSuggestion, $journey);

        // 客户意图分析模块
        $suggestionReflectionMemory = $this->suggestionReflection($recentCommunication, $lastMessages, $chatRecords, $lastFollowSuggestion, $journey, $negotiationProgressMemory);

        // 跟进建议分析模块
        $followSuggestion = $this->followSuggestion($recentCommunication, $lastMessages, $chatRecords, $lastFollowSuggestion, $journey, $negotiationProgressMemory, $suggestionReflectionMemory);

        $scheduleGenerationAnswer = is_array($followSuggestion['answer']) ? $followSuggestion['answer'] : json_decode($followSuggestion['answer'], true);
        if (!empty($scheduleGenerationAnswer))
        {
            $data = $scheduleGenerationAnswer + ['journey_id' => $journey['journey_id']];

            // 先保存主表，然后保存子表
            $this->saveDetail($journey['company_id'], $journey['user_id'], $data, \common\library\ai_portrait_analysis\Constant::SUGGESTIONS_BIZ_TYPE_COMMUNICATE);
        }
    }

    /**
     * 日程代办分析处理
     */
    public function handleScheduleAnalysis($journey)
    {
        $chatRecords = Helper::generateTodayRecords($journey);

        // 代办生成模块
        $scheduleGeneration = $this->scheduleGeneration($chatRecords, $journey);

        // 生成代办&入库
        $scheduleGenerationAnswer = is_array($scheduleGeneration['answer']) ? $scheduleGeneration['answer'] : json_decode($scheduleGeneration['answer'], true);
        if (!empty($scheduleGenerationAnswer['future_task']['date']))
        {
            $convertDate = $this->convertDate($scheduleGenerationAnswer['future_task']['date'], $journey);

            if (empty($convertDate)) {
                return;
            }

            $data = $scheduleGenerationAnswer + ['journey_id' => $journey['journey_id']];

            // 先保存主表，然后保存子表
            $this->saveDetail($journey['company_id'], $journey['user_id'], $data, \common\library\ai_portrait_analysis\Constant::SUGGESTIONS_BIZ_TYPE_SCHEDULE, $convertDate);
        }
    }

    public function saveDetail($companyId, $userId, $data, $bizType, $convertDate = '1970-01-01 00:00:00')
    {
        // 处理翻译
        try {
            $translateData = $this->translateResult($userId, $data);

            if (!empty($translateData)) {
                $data = $translateData;
            }
        } catch (\Throwable $e) {
            \LogUtil::info('saveDetailTranslateError', [
                'msg' => $e->getMessage(),
                'company_id' => $companyId,
                'user_id' => $userId,
                'data' => $data,
            ]);
        }

        $aiPortraitAnalysisApi = new AiPortraitAnalysisApi($this->clientId, \Constants::TYPE_COMPANY);
        $aiPortraitAnalysis = $aiPortraitAnalysisApi->getFollowSuggestion($companyId);

        if (empty($aiPortraitAnalysis)) {
            $analysisId = $aiPortraitAnalysisApi->createAiPortraitAnalysisByReferAndType($companyId, \common\library\ai_portrait_analysis\Constant::ANALYZE_TYPE_ACTION_SUGGESTIONS);
        } else {
            $analysisId = $aiPortraitAnalysis['analysis_id'];
        }

        $aiPortraitAnalysisDetailApi = new AiPortraitAnalysisDetailApi($this->clientId, \Constants::TYPE_COMPANY);
        $aiPortraitAnalysisDetailApi->createAiPortraitAnalysisDetail($companyId, \common\library\ai_portrait_analysis\Constant::ANALYZE_TYPE_ACTION_SUGGESTIONS, $analysisId, $bizType, $userId, $data, $convertDate);
    }

    public function translateResult($userId, array $data)
    {
        if (empty($data)) {
            return [];
        }

        $userSetting = new \common\library\setting\user\UserSetting($this->clientId, $userId, \common\library\setting\user\UserSetting::USER_LANGUAGE);
        $lang = $userSetting->getValue();
        $lang = \common\library\ai_agent\language\Helper::getLanguageKey(\AiAgent::AI_AGENT_SCENE_TYPE_COMPANY_QUALITY_CHECK, $lang);

        // 繁体不兼容
        if ($lang == 'zh-TW') {
            return [];
        }

        // 处理行动建议
        if (isset($data['follow_up_suggestions']) && !isset($data['follow_up_suggestions'][$lang]))
        {
            $followUpSuggestions = $data['follow_up_suggestions'];
            $translate =\common\library\ai_agent\language\Helper::translateListByModel(['follow_up_suggestions' => $followUpSuggestions['cn']], $lang);

            if (empty($translate['follow_up_suggestions'] ?? ''))
            {
                \LogUtil::info('translateSuggestionsError', [
                    'client_id' => $this->clientId,
                    'user_id' => $this->userId,
                    'company_id' => $this->companyId,
                ]);
                return $data;
            }

            $data['follow_up_suggestions'][$lang] = $translate['follow_up_suggestions'];
        }

        // 处理日程消息
        if ((isset($data['future_task']['details']['cn']) && !isset($data['future_task']['details'][$lang])) || (isset($data['future_task']['description']['cn']) && !isset($data['future_task']['description'][$lang])))
        {
            $needTranslateMap = [
                'details' => $data['future_task']['details']['cn'],
                'description' => $data['future_task']['description']['cn'],
            ];

            $scheduleTranslate = \common\library\ai_agent\language\Helper::translateListByModel($needTranslateMap, $lang);

            if (isset($scheduleTranslate['details'])) {
                $data['future_task']['details'][$lang] = $scheduleTranslate['details'];
            }

            if (isset($scheduleTranslate['description'])) {
                $data['future_task']['description'][$lang] = $scheduleTranslate['description'];
            }
        }

        return $data;
    }

    /**
     * 谈单进展分析模块
     */
    public function negotiationProgress($recentCommunication, $lastMessages, $chatRecords, $lastFollowSuggestion, $journey)
    {
        $systemPrompt = <<<SYSTEM_PROMPT
# Instruction
You are a veteran cross-border B2B e-commerce expert with extensive experience in international trade between China and foreign countries. Your task is to analyze today's business communication between the salesperson and customer, and break down the progress made on each key B2B topic.

# Key B2B Topics
- Product and Requirements
  * Product names and models
  * Product specifications and technical parameters
  * Material requirements
  * Customization requests
  * Purchase quantities
  * Sample requirements
  * Purchase purpose and target market
  * Quality standard requirements
  * Certification and test report requirements

- Logistics Solution
  * Transportation methods
  * Delivery terms (DDP/FOB/EXW etc.)
  * Loading/destination ports
  * Detailed destination information
  * Special logistics requirements

- Quotation and Costs
  * Product unit price and total price
  * Shipping cost quotation
  * Other fee details
  * Discount plans
  * Total order amount
  * Minimum Order Quantity (MOQ)

- Contract and Payment
  * Proforma invoice processing
  * Formal contract signing
  * Payment methods
  * Payment terms
  * Payment amounts and nature (deposit/balance/shipping fee etc.)
  * Payment milestones

- Production and Delivery
  * Production timeline
  * Production progress updates
  * Quality control measures
  * Shipping schedule
  * Transportation status tracking
  * Delivery confirmation
  * Delivery exception handling

- Customer Feedback
  * Product quality evaluation
  * Service satisfaction
  * Specific issue feedback
  * Improvement suggestions
  * Competitive comparison
  * Repurchase intention

# Constraints
- IMPORTANT: When analyzing the chat records, you MUST clearly distinguish between messages sent by the salesperson and those sent by the customer. Never confuse or mix up who said what. Messages are typically marked as:
  * Salesperson messages: "Salesperson: [message content]"
  * Customer messages: "Customer: [message content]"
Maintaining this clear distinction is crucial for understanding the chat records.
- The chat records for today may contain messages from multiple communication channels between both parties, such as TM (Note: TM is the chat tool used for communication between buyers and sellers on alibaba.com), WhatsApp, email, etc.
- In the chat records, placeholders in the form of '#xxx#' represent special non-text messages. For example, '#Image#' represents an image, '#File#' represents a file. Notably, '#Inquiry#' is a special card message representing a product inquiry.
- For each key B2B topic discussed today, you MUST analyze:
  1. What was discussed - The specific content and details of the discussion that happened TODAY ONLY
  2. What was agreed upon - Any clear agreements or conclusions reached TODAY ONLY
- IMPORTANT: You must focus ONLY on today's progress and developments. Do not include discussions or agreements from previous days, even if they are mentioned in today's chat. Only analyze new information and progress made today.

# Output in JSON Format as follow:
{
  "thinking_process": [
    {
      "topic": "Product Information",
      "discussed": "Detail what was discussed about product specifications, features, materials, etc. TODAY",
      "agreed": "List any agreements reached on product details TODAY",
    },
    // Repeat for each key B2B topic
  ]
}

# Workflow
1. Review the recent communication summary and last 10 messages from previous conversation for context only
2. Analyze today's chat records focusing on each key B2B topic
3. For each topic:
   - Document what was specifically discussed TODAY
   - Identify clear agreements reached TODAY
4. Generate structured thinking process showing analysis for each topic
5. Output in json format as shown above
6. Let's work this out step by step to ensure accuracy

# Input
SYSTEM_PROMPT;

        $userPrompt = $this->userPromptMap['qc.v2.negotiation.process.user.prompt'];
        $userPrompt = str_replace('{{recentCommunication}}', $recentCommunication, $userPrompt);
        $userPrompt = str_replace('{{lastMessages}}', $lastMessages, $userPrompt);
        $userPrompt = str_replace('{{lastFollowSuggestion}}', $lastFollowSuggestion, $userPrompt);
        $userPrompt = str_replace('{{chatRecords}}', $chatRecords, $userPrompt);

        $response = $this->generateAnalysis($this->promptConfig['qc.v2.negotiation.process.service'], $systemPrompt, $userPrompt, $journey, 'negotiation_progress');

        $memory = <<<MEMORY
Here are today's key progress in B2B international trade communication between salesperson and customer:
MEMORY;

        $thinkingProcessList = json_decode($response['answer'],true);
        $thinkingProcessList = $thinkingProcessList['thinking_process'];
        foreach ($thinkingProcessList as $thinkingProcess)
        {
            $memory .= <<<MEMORY

## {$thinkingProcess['topic']}
- What was discussed
{$thinkingProcess['discussed']}
- What was aggreed upon
{$thinkingProcess['agreed']}
## Logistics Solution
===========
MEMORY;
        }

        return $memory;
    }

    /**
     * 跟进建议反思
     */
    public function suggestionReflection($recentCommunication, $lastMessages, $chatRecords, $lastFollowSuggestion, $journey, $negotiationProgressMemory)
    {
        $systemPrompt = <<<SYSTEM_PROMPT
# Instruction
You are a reflective AI assistant tasked with evaluating whether today's communication between the salesperson and customer followed the previous follow-up suggestions. If not, analyze the reasons from the sales perspective, considering that not following suggestions likely indicates an intentional choice. Your task is to provide ONE key reflection on the previous suggestions.

# Output in JSON Format as follow:
{
    "reflection": "ONE clear insight about whether and why the previous suggestions were intentionally not followed, analyzing from the sales perspective"
}

# Examples of Good Reflections
1. "The previous suggestion to follow up on packaging damage issues was deliberately not executed as today's conversation focused on new order inquiries. From the sales perspective, this indicates a strategic choice to prioritize new business opportunities over resolving past issues, likely because the potential new orders present greater value."
2. "The previous suggestion to offer a discount was intentionally not followed, with the salesperson maintaining the original quotation. From the sales perspective, this suggests a deliberate pricing strategy, possibly due to healthy profit margins being essential for the business model or having other interested buyers at the current price point."
3. "The previous suggestion to confirm delivery status was successfully executed as the customer confirmed receipt. However, there was no further discussion, suggesting the salesperson saw no strategic value in pursuing that conversation thread further."

# Constraints
- IMPORTANT: When analyzing the chat records, you MUST clearly distinguish between messages sent by the salesperson and those sent by the customer. Messages are typically marked as:
  * Salesperson messages: "Salesperson: [message content]"
  * Customer messages: "Customer: [message content]"
Maintaining this clear distinction is crucial for understanding the chat records.
- The chat records for today may contain messages from multiple communication channels between both parties, such as TM (Note: TM is the chat tool used for communication between buyers and sellers on alibaba.com), WhatsApp, email, etc.
- In the chat records, placeholders in the form of '#xxx#' represent special non-text messages. For example, '#Image#' represents an image, '#File#' represents a file. Notably, '#Inquiry#' is a special card message representing a product inquiry.

# Workflow
1. Review the recent communication summary and last 10 messages from previous conversation to understand the context
2. Review the previous follow-up suggestions and today's chat records
3. Analyze whether suggestions were intentionally not followed and the strategic reasons from the sales perspective
4. Output in json format as shown in <Output Format> without any extra characters
5. Let's work this out step by step to ensure accuracy

# Input
SYSTEM_PROMPT;

        $userPrompt = $this->userPromptMap['qc.v2.suggestion.reflection.user.prompt'];
        $userPrompt = str_replace('{{recentCommunication}}', $recentCommunication, $userPrompt);
        $userPrompt = str_replace('{{lastMessages}}', $lastMessages, $userPrompt);
        $userPrompt = str_replace('{{lastFollowSuggestion}}', $lastFollowSuggestion, $userPrompt);
        $userPrompt = str_replace('{{chatRecords}}', $chatRecords, $userPrompt);
        $userPrompt = str_replace('{{negotiationProgressMemory}}', $negotiationProgressMemory, $userPrompt);

        $response = $this->generateAnalysis($this->promptConfig['qc.v2.suggestion.reflection.service'], $systemPrompt, $userPrompt, $journey, 'suggestion_reflection');

        $reflection = json_decode($response['answer'],true);
        $reflection = $reflection['reflection'] ?? '';

        $memory = <<<MEMORY
Here is the reflection on the effectiveness of the previous suggestion:
{$reflection}
===========
MEMORY;

        return $memory;
    }

    /**
     * 跟进建议生成模块
     */
    public function followSuggestion($recentCommunication, $lastMessages, $chatRecords, $lastFollowSuggestion, $journey, $negotiationProgressMemory, $suggestionReflectionMemory)
    {
        $systemPrompt = <<<SYSTEM_PROMPT
# Instruction
You are a veteran cross-border B2B e-commerce expert with extensive experience in international trade between China and foreign countries. Based on today's chat records, your task is to:
1. Carefully analyze the conversation between salesperson and customer
2. Think from a strategic sales perspective about the current deal stage and next steps
3. Generate ONE clear and actionable follow-up suggestion

# Output in JSON Format as follow:
{
  "strategic_analysis": {
    "current_stage": "Analysis of current deal stage and key developments from today's conversation",
    "customer_status": "Analysis of customer's current focus, needs and potential concerns", 
    "sales_priorities": "Analysis of what the salesperson should prioritize based on current situation",
    "action_value": "Analysis of how the suggested follow-up action will advance the deal"
  },
  "follow_up_suggestions": {
    "en": "ONE specific suggestion for what to discuss in next online chat, with clear rationale based on today's conversation",
    "cn": "一条具体的下次线上沟通建议，需基于今天对话内容给出明确理由"
  }
}

# Requirements
Strategic analysis must:
- Focus on key developments from today's conversation
- Evaluate current deal stage and progress
- Assess customer's immediate needs and concerns
- Consider sales objectives and priorities
- Analyze how suggested action advances the deal

# Good Examples of Follow-up Suggestions
Example 1:
{
  "strategic_analysis": {
    "current_stage": "Production phase - Order confirmed and manufacturing in progress at 80% completion. Today discussed final production timeline",
    "customer_status": "Customer shows active interest in production progress and has upcoming delivery deadline to meet",
    "sales_priorities": "Ensure smooth transition from production to delivery phase while maintaining customer confidence",
    "action_value": "Proactive logistics planning will prevent delivery delays and demonstrate professional service capability"
  },
  "follow_up_suggestions": {
    "en": "Since production will complete this weekend, propose to discuss shipping arrangements now to ensure smooth delivery. This proactive approach helps avoid any potential delays.",
    "cn": "由于本周末生产即将完成，建议现在开始讨论物流安排以确保顺利交付。这种主动的方式可以避免潜在的延误。"
  }
}

Example 2:
{
  "strategic_analysis": {
    "current_stage": "Sample testing phase - Samples delivered last week, today customer mentioned beginning of testing",
    "customer_status": "Customer actively testing product samples with specific focus on material durability",
    "sales_priorities": "Gather initial feedback while demonstrating technical support capability",
    "action_value": "Early feedback collection enables quick response to any concerns and increases chances of securing bulk order"
  },
  "follow_up_suggestions": {
    "en": "As the customer has started testing our samples, politely check if they have any initial feedback about material durability. This shows our attention to their specific testing focus while offering technical support if needed.",
    "cn": "由于客户已开始测试样品，礼貌询问是否对材料耐用性有初步反馈。这表明我们关注他们的具体测试重点，同时可以在需要时提供技术支持。"
  }
}

Follow-up suggestion must:
- Align with the strategic analysis
- Be ONE specific suggestion for next online chat
- Be based on clear evidence from today's conversation
- Be immediately actionable in next chat 
- Include clear rationale explaining why this is the priority
- Note:
  * Avoid suggesting phone calls, video meetings or other communication methods that require high language proficiency
  * Do NOT suggest refunds, replacements, offering discounts or other concessions without clear context from the conversation
  * Do NOT include example responses or message templates
  * Do NOT repeat suggestions similar to previous ones that were not covered in today's conversation, as this indicates they do not align with the current sales strategy

# Bad Examples of Follow-up Suggestions
Example 1 - Excessive promises without context:
❌ "Offer a full refund and send 3 sets of free replacement samples to resolve any concerns."
Analysis: Making significant concessions like refunds or free replacements without clear context from the conversation can set unrealistic expectations and harm business relationships.

Example 2 - Impractical communication suggestions:
❌ "Schedule a 2-hour video conference call tomorrow to go through all technical specifications in detail."
Analysis: Suggesting video calls or phone meetings requires high language proficiency and may create communication barriers, especially in cross-border B2B contexts.

Example 3 - Overly demanding costs:
❌ "Prepare a comprehensive quality analysis report with lab test results and certification documents to address customer's quality concerns."
Analysis: Proposing tasks that require extensive time and resources without clear business justification can overwhelm the sales team and delay more important priorities.


# Workflow
1. Analyze today's conversation and current deal stage
2. Evaluate customer's current status and priorities
3. Consider sales strategic priorities
4. Determine most valuable next action that differs from unsuccessful previous suggestions
5. Generate ONE clear and actionable suggestion
6. Output in json format as shown above

# Input
SYSTEM_PROMPT;

        $userPrompt = $this->userPromptMap['qc.v2.follow.suggestion.user.prompt'];
        $userPrompt = str_replace('{{recentCommunication}}', $recentCommunication, $userPrompt);
        $userPrompt = str_replace('{{lastMessages}}', $lastMessages, $userPrompt);
        $userPrompt = str_replace('{{lastFollowSuggestion}}', $lastFollowSuggestion, $userPrompt);
        $userPrompt = str_replace('{{chatRecords}}', $chatRecords, $userPrompt);
        $userPrompt = str_replace('{{negotiationProgressMemory}}', $negotiationProgressMemory, $userPrompt);
        $userPrompt = str_replace('{{suggestionReflectionMemory}}', $suggestionReflectionMemory, $userPrompt);

        return $this->generateAnalysis($this->promptConfig['qc.v2.follow.suggestion.service'], $systemPrompt, $userPrompt, $journey, 'follow_suggestion');
    }

    /**
     * 待办生成模块
     */
    public function scheduleGeneration($chatRecords, $journey)
    {
        $systemPrompt = <<<SYSTEM_PROMPT
# Instruction
You are a B2B sales management expert. Your task is to extract the MOST IMPORTANT future follow-up task from today's conversation. A future follow-up task is defined as any matter that:
1. Requires follow-up at a specific future date (MUST have explicit date/time mentioned in conversation)
2. Does NOT need immediate action
3. Has clear business value for maintaining customer relationship or deal progress

# Output in JSON Format as follow:
{
  "thinking_process": "Your thinking process for extracting the most important future follow-up task",
  "future_task": {
    "description": {
      "en": "What the salesperson needs to do on the follow-up date",
      "cn": "销售在跟进日期需要做什么"
    },
    "details": {
      "en": "On [date], salesperson informed customer [context]. Need to follow up on [date]",
      "cn": "在[日期]，销售告知客户[上下文]。需要在[日期]跟进"
    },
    "date": "Natural language description of when to follow up (e.g. 'next Monday', 'in two weeks')"
  }
}

# Requirements
For the task:
- Must have a SPECIFIC future date/time mentioned in today's conversation
- Must NOT be something requiring immediate action
- Must be extracted from actual conversation content, not assumptions
- Must be the SINGLE MOST IMPORTANT task - do not include multiple tasks
- Task description should focus on what the salesperson needs to DO on the follow-up date
- If no valid task is found, return empty object {}

# Examples of Good Future Tasks
1. {
  "thinking_process": "In today's conversation, the salesperson informed the customer that production is expected to complete next weekend. This creates a clear need to check and update the customer about production status on that date, making it an important future follow-up task.",
  "future_task": {
    "description": {
      "en": "Check and inform customer about latest production status, especially whether production has completed",
      "cn": "检查并告知客户最新的生产进展，特别是生产是否已经完成"
    },
    "details": {
      "en": "On 2024-01-15, salesperson informed customer that production is expected to complete next weekend. Need to follow up on next weekend",
      "cn": "在2024年1月15日，销售告知客户生产预计在下周末完成。需要在下周末跟进"
    },
    "date": "next weekend"
  }
}

2. {
  "thinking_process": "The customer mentioned they will conduct product testing and the results will be available in two weeks. This creates a clear timing for the salesperson to follow up on the testing results.",
  "future_task": {
    "description": {
      "en": "Contact customer to inquire about product testing results",
      "cn": "联系客户询问产品测试结果"
    },
    "details": {
      "en": "On 2024-01-15, customer said they will complete product testing in two weeks. Need to follow up in two weeks",
      "cn": "在2024年1月15日，客户表示将在两周内完成产品测试。可以考虑在两周后跟进"
    },
    "date": "in two weeks"
  }
}

# Workflow
1. Review today's conversation to identify matters that:
   - Have specific future follow-up dates
   - Do not require immediate action
   - Have clear business value
2. Record thinking process:
   - Document key points from conversation analysis
   - Explain why this matter requires future follow-up
   - Justify the business importance
3. For the selected task:
   - Write description focusing on what salesperson needs to DO
   - Include context and timeline in details
   - Extract specific follow-up date
4. Format output according to specified structure
5. If no valid task is found, return {}

# Input
SYSTEM_PROMPT;

        $userPrompt = $this->userPromptMap['qc.v2.schedule.generate.user.prompt'];
        $userPrompt = str_replace('{{chatRecords}}', $chatRecords, $userPrompt);

        return $this->generateAnalysis($this->promptConfig['qc.v2.schedule.generate.service'], $systemPrompt, $userPrompt, $journey, 'schedule_generate');
    }

    /**
     * 日期转化模块
     */
    public function convertDate($targetDate, $journey)
    {
        $systemPrompt = <<<SYSTEM_PROMPT
# Instruction
You are a date conversion expert. Your task is to convert a natural language date description into a specific date in "yyyy-mm-dd" format, based on a given reference date.

# Input Format
{
    "reference_date": "2024-01-15",  // Today's date in yyyy-mm-dd format
    "reference_weekday": "Monday",    // Today's weekday
    "target_date": "next Friday"      // Natural language date description to convert
}

# Output Format
Output in JSON Format.

# Example Cases
Case 1:
Input: 
{
    "reference_date": "2024-01-24",
    "reference_weekday": "Monday", 
    "target_date": "next Friday"
}
Output:
{
    "thinking_process": [
        "Step 1: Reference date is 2024-01-24, reference weekday is Monday",
        "Step 2: Target is 'next Friday', meaning the first Friday after the reference date",
        "Step 3: Need to add 4 days from Monday to reach Friday",
        "Step 4: 2024-01-24 + 4 days = 2024-01-28",
        "Step 5: Date is already in yyyy-mm-dd format: 2024-01-28"
    ],
    "result": "2024-01-28"
}

Case 2:
Input:
{
    "reference_date": "2024-01-24",
    "reference_weekday": "Monday",
    "target_date": "in two weeks"
}
Output:
{
    "thinking_process": [
        "Step 1: Reference date is 2024-01-24, reference weekday is Monday",
        "Step 2: Target is 'in two weeks', meaning 14 days from reference date",
        "Step 3: Need to add 14 days to reference date",
        "Step 4: 2024-01-24 + 14 days = 2024-02-07",
        "Step 5: Date is already in yyyy-mm-dd format: 2024-02-07"
    ],
    "result": "2024-02-07"
}

Case 3:
Input:
{
    "reference_date": "2024-01-24",
    "reference_weekday": "Monday",
    "target_date": "end of this month"
}
Output:
{
    "thinking_process": [
        "Step 1: Reference date is 2024-01-24, reference month is January 2024",
        "Step 2: Target is 'end of this month', meaning the last day of January 2024",
        "Step 3: Need to determine last day of January 2024 which has 31 days",
        "Step 4: Last day will be 2024-01-31",
        "Step 5: Date is already in yyyy-mm-dd format: 2024-01-31"
    ],
    "result": "2024-01-31"
}

# Output Format
{
    "thinking_process": [
        "Step 1: [Analyze input reference date and weekday]",
        "Step 2: [Parse natural language target date description]",
        "Step 3: [Determine calculation method for target date]",
        "Step 4: [Perform date calculation]", 
        "Step 5: [Format date output]"
    ],
    "result": "yyyy-mm-dd"
}

# Workflow
1. Analyze input reference date and weekday
2. Parse natural language target date description  
3. Determine calculation method for target date
4. Perform date calculation
5. Format date output

# Input
SYSTEM_PROMPT;

        $date = new \DateTime();
        // 修改日期为前一天
        $date->modify('-1 day');
        // 获取前一天的日期
        $yesterday = $date->format('Y-m-d');
        // 获取前一天对应的星期几
        $weekday = $date->format('l'); // 'l' 小写字母L，表示完整的星期几名称

        $userPrompt = $this->userPromptMap['qc.v2.convert.date.user.prompt'];
        $userPrompt = str_replace('{{yesterday}}', $yesterday, $userPrompt);
        $userPrompt = str_replace('{{weekday}}', $weekday, $userPrompt);
        $userPrompt = str_replace('{{targetDate}}', $targetDate, $userPrompt);

        $convertDate = $this->generateAnalysis($this->promptConfig['qc.v2.convert.date.service'], $systemPrompt, $userPrompt, $journey, 'convert_date');

        $convertDate = is_array($convertDate['answer']) ? $convertDate['answer'] : json_decode($convertDate['answer'], true);
        if (empty($convertDate['result'])) {
            return '';
        }

        /* 如果日期转化模块最后转化出的日期不是"yyyy-mm-dd"的格式，或者date_convert-date_today<=3（这说明该事件需要快速解决，没必要建日程），那么对应的待办日程不返回 */
        // Step 1: Parse the input date string to DateTime object
        $date = \DateTime::createFromFormat('Y-m-d', $convertDate['result']);

        // Check if the date is in the correct format
        if (!$date || $date->format('Y-m-d') !== $convertDate['result']) {
            return ''; // The date is not in 'yyyy-mm-dd' format
        }

        // Step 2: Get today's date
        $today = new \DateTime(date('Y-m-d 00:00:00'));

        // Step 3: Calculate the difference between the input date and today's date
        $interval = $today->diff($date);

        // Step 4: Check if the difference in days is less than or equal to 3
        if ($interval->days <= 3 && !$interval->invert) {
            return ''; // The event is within 3 days and needs quick resolution
        }

        // If all conditions are satisfied, return true
        return $convertDate['result'];
    }


    public function generateAnalysis($model, $systemPrompt, $userPrompt, $journey, $analysisType)
    {
        $requestBeginTime = microtime(true);

        $aiClient = new AIClient();

        $className = basename(str_replace('\\', '/', get_class($this)));
        $aiClient->setTrace("OKKI_AI", $className);
        $aiClient->setModel($model);
        $aiClient->setSystemPrompt($systemPrompt);
        $aiClient->setQuestion($userPrompt);
        $aiClient->setResponseFormat(['type' => 'json_object']);
        $originResponse = $aiClient->chatCompletions();

        $requestEndTime = microtime(true);

        $response = $aiClient->convertResponse($originResponse['data']);
        $response = $this->formatJson($response);

        // 保存 record 记录
        $aiServiceProcessRecordPdo = new AiServiceProcessRecord();
        $aiServiceProcessRecordPdo->params = '{}';
        $aiServiceProcessRecordPdo->scene_type = $this->sceneType;
        $aiServiceProcessRecordPdo->refer_id = $this->companyId;
        $aiServiceProcessRecordPdo->question = $userPrompt;
        $aiServiceProcessRecordPdo->answer = $response['answer'];
        $aiServiceProcessRecordPdo->client_id = $this->clientId;
        $aiServiceProcessRecordPdo->create_user = $this->userId;
        $aiServiceProcessRecordPdo->status = 1;
        $aiServiceProcessRecordPdo->ai_model = $model;
        $aiServiceProcessRecordPdo->prompt_tokens = $response['promptTokens'];
        $aiServiceProcessRecordPdo->completion_tokens = $response['completionTokens'];
        $aiServiceProcessRecordPdo->total_tokens = $response['totalTokens'];
        $aiServiceProcessRecordPdo->agent_version_id = $this->versionId;
        $aiServiceProcessRecordPdo->request_data = json_encode($aiClient->buildParams());
        $aiServiceProcessRecordPdo->request_response = json_encode($originResponse, JSON_UNESCAPED_UNICODE);
        $aiServiceProcessRecordPdo->request_time = $requestEndTime - $requestBeginTime;
        $aiServiceProcessRecordPdo->save();

        $aiQualityCheckAgentAnalysisListPdo = new AiQualityCheckAgentAnalysisList($this->clientId);
        $aiQualityCheckAgentAnalysisListPdo->setJourneyId($journey['journey_id']);
        $aiQualityCheckAgentAnalysisListPdo->setUserId($journey['user_id']);
        $aiQualityCheckAgentAnalysisListPdo->setAgentId(self::QC_SUGGESTION_AGENT_ID);
        $aiQualityCheckAgentAnalysisListPdo->setContactDate($journey['contact_date']);
        $aiQualityCheckAgentAnalysisList = $aiQualityCheckAgentAnalysisListPdo->find();

        $aiQualityCheckAgentAnalysis = $aiQualityCheckAgentAnalysisList[0] ?? [];

        $analysisResult = [];
        if (!empty($aiQualityCheckAgentAnalysis))
        {
            $analysisResult = $aiQualityCheckAgentAnalysis['analyze_result'];
            $analysisResult = is_array($analysisResult) ? $analysisResult : json_decode($analysisResult, true);
        }

        // 埋入对应ID，可追溯
        $answer = is_array($response['answer']) ? $response['answer'] : json_decode($response['answer'], true);
        $answer += [
            'journey_id' => $journey['journey_id'],
            'record_id' => $aiServiceProcessRecordPdo->record_id
        ];

        // 更新分析模块
        switch ($analysisType)
        {
            case 'negotiation_progress':
                $analysisResult['negotiation_progress'] = $answer;
                break;
            case 'suggestion_reflection':
                $analysisResult['suggestion_reflection'] = $answer;
                break;
            case 'convert_date':
                $analysisResult['convert_date'] = $answer;
                break;
            case 'schedule_generate':
                $analysisResult['schedule_generate'] = $answer;
                break;
            case 'business_status':
                $analysisResult['business_status'] = $answer;
                break;
            case 'normal_follow_suggestion':
                $analysisResult['normal_follow_suggestion'] = $answer;
                break;
            case 'rebuild_follow_suggestion':
                $analysisResult['rebuild_follow_suggestion'] = $answer;
                break;
            default:
                $analysisResult['follow_suggestion'] = $answer;
                break;
        }

        //agent_id=7不记表
        if(!empty($analysisType) && $analysisType == "business_status") {
            return $response;
        }

        if (empty($aiQualityCheckAgentAnalysis)) {
            $aiQualityCheckAgentAnalysisPdo = new AiQualityCheckAgentAnalysis();
            $aiQualityCheckAgentAnalysisPdo->client_id = $this->clientId;
            $aiQualityCheckAgentAnalysisPdo->company_id = $this->companyId;
            $aiQualityCheckAgentAnalysisPdo->user_id = $journey['user_id'];
            $aiQualityCheckAgentAnalysisPdo->customer_id = $journey['customer_id'];
            $aiQualityCheckAgentAnalysisPdo->contact_date = $journey['contact_date'];
            $aiQualityCheckAgentAnalysisPdo->journey_id = $journey['journey_id'];
            $aiQualityCheckAgentAnalysisPdo->analyze_result = json_encode($analysisResult, JSON_UNESCAPED_UNICODE);
            $aiQualityCheckAgentAnalysisPdo->agent_id = self::QC_SUGGESTION_AGENT_ID;
            $aiQualityCheckAgentAnalysisPdo->save();
        } else {
            $aiQualityCheckAgentAnalysisPdo = new AiQualityCheckAgentAnalysis($aiQualityCheckAgentAnalysis['analysis_id']);
            $aiQualityCheckAgentAnalysisPdo->analyze_result = json_encode($analysisResult, JSON_UNESCAPED_UNICODE);
            $aiQualityCheckAgentAnalysisPdo->save();
        }

        return $response;
    }

    /**
     * 谈单监测V3,取数并格式化
     * @return array context
     */
    public function buildChatRecords(array $runContext) : array
    {
        if ($this->checkNeedSkipV3()) {
            $runContext['error'] = "防重入";
            return [$runContext, true];
        }

        //获取往来邮件 + 格式化
        $emailChatInfo = Helper::getEmailChatInfoV2($this->clientId, $this->companyId, $this->startTime, $this->endTime);
        $formatMailInfo = $this->formatMailQualityCheckInfo($emailChatInfo);

        //拉取Sns聊天记录 + 格式化
        $snsChatInfo = Helper::getSnsChatInfo($this->clientId, $this->companyId, $this->startTime, $this->endTime);
        $formatSnsInfo = $this->formatSnsQualityCheckInfo($snsChatInfo);

        //存表tbl_ai_quality_check_company_conversation
        $formatChatInfo = array_merge($formatMailInfo, $formatSnsInfo);

        if (!empty($formatChatInfo))
        {
            $batchInsertConversations = [];
            foreach ($formatChatInfo as $chatInfo)
            {
                $conversation = new AiQualityCheckCompanyConversation();
                $conversation->client_id = $this->clientId;
                $conversation->company_id = $this->companyId;
                $conversation->user_id = $chatInfo['userId'];
                $conversation->customer_id = $chatInfo['customerId'];
                $conversation->contact_date = date('Y-m-d', strtotime($chatInfo['recentContactTime']));
                $conversation->day_first_message_time = $chatInfo['firstContactTime'];
                $conversation->day_last_message_time = $chatInfo['recentContactTime'];
                $conversation->sns_type = $chatInfo['snsType'];
                $conversation->content = iconv("UTF-8" , "UTF-8//IGNORE" ,mb_convert_encoding($chatInfo['content'] ?? '' , 'UTF-8' , array('UTF-8','ISO-8859-1','GBK','CP1252','Windows-1252')));
                $conversation->refer_ids = PgsqlUtil::formatArray($chatInfo['referIds']);
                $batchInsertConversations[] = $conversation;
            }

            //根据(client_id,company_id,user_id,customer_id,sns_type,contact_date)幂等插入
            Helper::batchInsertOnConflict($batchInsertConversations);
        }else{
            $runContext['error'] = "没有往来邮件&聊天信息";
            return [$runContext, true];
        }

        //当日的所有聊天记录
        $conversationList = new AiQualityCheckCompanyConversationList($this->clientId);
        $conversationList->setCompanyId($this->companyId);
        $conversationList->setContactDate($this->dateTime);
        $conversationList->setSplitByUserAndCustomer(true);
        //key:"{userId}-{customerId}"
        $conversations = $conversationList->find();
        $runContext['conversations'] = $conversations;

        //FIXME 最近x天的y条聊天旅程,动态配置
        $redis = \RedisService::cache();
        $range = $redis->get("ai.qc.recent.day.range");
        if(empty($range)){
            //默认30天
            $range = 30;
            $redis->set("ai.qc.recent.range", $range);
        }
        $runContext['range'] = intval($range);

        $limit = $redis->get("ai.qc.recent.day.limit");
        if (empty($limit)) {
            $limit = 5;
            $redis->set("ai.qc.recent.day.limit", $limit);
        }
        $runContext['limit'] = intval($limit);

        //最近一次沟通中,begin_time最晚的z条消息
        $messageNum = $redis->get('ai.qc.recent.message.num');
        if(empty($messageNum)){
            $messageNum = 10;
            $redis->set('ai.qc.recent.message.num', $messageNum);
        }
        $runContext['messageNum'] = intval($messageNum);

        return [$runContext, false];
    }

    /**
     * 谈单监测V3
     * Event Annotation Agent
     * 1.判断今日沟通是否有实质性内容
     * 2.半结构化聊天数据
     * 3.关键事件标注
     */
    public function eventAnnotation(array $runContext) : array
    {
        $companyConversations = $runContext['conversations'] ?? [];
        $range = $runContext['range'];
        $limit = $runContext['limit'];
        $messageNum = $runContext['messageNum'];

        foreach ($companyConversations as $pair => $conversations)
        {
            $ss = explode("-", $pair);
            $userId = $ss[0];
            $customerId = $ss[1];

            $this->recordUserId = $userId;
            $this->recordCustomerId = $customerId;
            $this->recordAgentId = self::QC_EVENT_ANNOTATION_AGENT_ID;

            if ($this->checkNeedSkipV3(self::QC_EVENT_ANNOTATION_AGENT_ID, $userId, $customerId)) {
                \LogUtil::info("AiCompanyQualityCheck_V3_EventAnnotationAgent: Skip By Idempotent",[
                    "clientId" => $this->clientId,
                    "companyId" => $this->companyId,
                    "date" => $this->dateTime,
                    "pair" => $pair,
                ]);
                continue;
            }

            //沟通是否有实质内容
            $isSubstantial = $this->checkIsSubstantial($conversations);
            if(!$isSubstantial) {
                \LogUtil::info("AiCompanyQualityCheck_V3_EventAnnotationAgent:ChatRecord_Is_Not_Substantial",[
                    'clientId' => $this->clientId,
                    'companyId' => $this->companyId,
                    'userId' => $userId,
                    'customerId' => $customerId,
                    'date' => $this->dateTime,
                ]);
                $this->saveAgentMemories($this->clientId,$this->companyId,$userId,$customerId,self::QC_EVENT_ANNOTATION_AGENT_ID,[
                    'is_substantial' => false,
                    'conversation_segments' => [],
                    'event_annotations' => [],
                    'occurred_events' => [],
                ]);
                continue;
            }

            //最近range天的limit条聊天旅程
            $recentSummaries = $this->buildRecentSummary($this->clientId, $userId, $customerId, $range, $limit);

            //最近range天的最近一条聊天旅程中,begin_time最晚的渠道的messageNum条记录
            $immediateContext = $this->buildImmediateContext($this->clientId, $userId, $customerId, $range, $messageNum);

            //今日的聊天记录
            $todayChatRecords = $this->buildTodayChatRecords($conversations);

            //沟通内容半结构化
            $conversationSegments = $this->segmentCommunicationToTopic($todayChatRecords, $recentSummaries, $immediateContext);

            //关键事件标注
            [$eventAnnotations, $occurredEvents] = $this->annotation($todayChatRecords, $recentSummaries, $immediateContext, $conversationSegments);

            $this->saveAgentMemories($this->clientId,$this->companyId,$userId,$customerId,self::QC_EVENT_ANNOTATION_AGENT_ID , [
                'is_substantial' => true,
                'conversation_segments' => $conversationSegments,
                'event_annotations' => $eventAnnotations,
                'occurred_events' => $occurredEvents,
            ]);

            $this->recordUserId = '';
            $this->recordCustomerId = '';
            $this->recordAgentId = 0;
        }
        return [$runContext, false];
    }

    /// 沟通理解并生成关键事件
    public function chatUnderstandAndAnnotation(array $runContext) : array
    {
        $todayConversations = $runContext['conversations'] ?? [];
        foreach ($todayConversations as $conversations)
        {
            if(empty($conversations)) continue;
            // 沟通理解.邮件和sns分开理解,对理解的结果进行合并,给后续agent使用
            $understanding = $this->conversationUnderstand($conversations);
            $agentAnalysis = [
                "conversation_content" => $understanding,
            ];

            $clientId = $conversations[0]['client_id'];
            $companyId = $conversations[0]['company_id'];
            $userId = $conversations[0]['user_id'];
            $customerId = $conversations[0]['customer_id'];

            // 关键事件标注
            $agentAnalysis['occurred_events'] = $this->annotationByUnderstanding($companyId,$understanding);

            // save
            $this->saveAgentMemories($clientId,$companyId,$userId,$customerId,self::QC_EVENT_ANNOTATION_AGENT_ID, $agentAnalysis);
        }

        return [$runContext, false];
    }

    private function buildSnsUnderstandingQuestion($conversation) : string
    {
        $clientId = $conversation['client_id'];
        $referId = PgsqlUtil::trimArray($conversation['refer_ids'])[0];
        $snsType = $conversation['sns_type'];
        if(empty($clientId) || empty($referId) || empty($snsType)) {
            return "";
        }

        $userPrompt = <<<EOF
<Context>
{{context}}
</Context>
<Today's Conversation>
--- {{date_today}} ---
{{conversation}}
</Today's Conversation>
EOF;

        // 查询出最近2个月的最近20条信息
        $messagePdo = new UserCustomerContactMessageList($clientId);
        $messagePdo->setUserContactId($referId);
        $messagePdo->setStartTime(date('Y-m-d 00:00:00',strtotime('-2 month',strtotime($this->dateTime))));
        $messagePdo->setEndTime(date('Y-m-d 23:59:59',strtotime('-1 day',strtotime($this->dateTime))));
        $messagePdo->setOrderBy('send_time');
        $messagePdo->setOrder('desc');
        $messagePdo->setLimit(20);
        $messageList = $messagePdo->find();

        // 按照时间切分
        $messageList = array_reduce($messageList, function ($result, $item) {
            // 从 send_time 中提取日期部分
            $date = date('Y-m-d', strtotime($item['send_time']));

            // 如果该日期的键还不存在，则创建它
            if (!isset($result[$date])) {
                $result[$date] = [];
            }

            // 将当前项添加到对应日期的数组中
            $result[$date][] = $item;

            return $result;
        }, []);

        //格式转换
        $context = [];
        foreach ($messageList as $date => $messages)
        {
            $message = $this->getFormattedMessages($messages, $snsType);
            $context[] = <<<EOF
--- {$date} ---
$message
EOF;
        }
        $context = array_reverse($context);

        return $this->replacePlaceholders($userPrompt, [
            'context' => trim(implode(PHP_EOL, $context)),
            'date_today' => $this->dateTime,
            'conversation' => trim($conversation['content'])
        ]);
    }

    private function buildEmailUnderstandingQuestion($conversation) : string {
        $userPrompt  = <<<EOF
<Today's Conversation>
--- {{date_today}} ---
{{email_conversation}}
</Today's Conversation>
EOF;
        return $this->replacePlaceholders($userPrompt,[
            'date_today' => $conversation['contact_date'],
            'email_conversation' => trim($conversation['content'])
        ]);
    }

    private function conversationUnderstand($conversations) : array
    {
        $understanding = [];
        if(empty($conversations)) {
            return $understanding;
        }

        $snsSystemPrompt = $this->systemPromptMap['conversationUnderstand.sns.systemPrompt'];
        $mailSystemPrompt = $this->systemPromptMap['conversationUnderstand.mail.systemPrompt'];
        $model = $this->promptConfig['conversationUnderstand.model'] ?? AiAgentConstants::AI_MODEL_VOLCANO_DEEPSEEK_V3;
        foreach ($conversations as $conversation) {
            $snsType = $conversation['sns_type'];
            if ($snsType != AiAgentConstants::AI_QC_CHANNEL_TYPE_MAIL) {
                $systemPrompt = $snsSystemPrompt;
                $historyMessage = $this->getChatUnderstandingHistoryMessage(AiAgentConstants::AI_QC_CHANNEL_TYPE_SNS);
                $question = $this->buildSnsUnderstandingQuestion($conversation);
            }else{
                $systemPrompt = $mailSystemPrompt;
                $historyMessage = $this->getChatUnderstandingHistoryMessage(AiAgentConstants::AI_QC_CHANNEL_TYPE_MAIL);
                $question = $this->buildEmailUnderstandingQuestion($conversation);
            }

            //重试两次
            $retry = 2;
            $understandingWithSnsType = [];
            do {
                try{
                    $understandingWithSnsType = $this->generateUnderstanding($systemPrompt, $question, $model, $historyMessage);
                    break;
                }catch (\Throwable $e) {
                    \LogUtil::error("AiCompanyQualityCheck_Error:Deepseek调用失败,重试次数:{$retry}",[
                        'clientId' => $this->clientId,
                        'companyId' => $this->companyId
                    ]);
//                    echo "AiCompanyQualityCheck_Error:Deepseek调用失败,重试" . PHP_EOL;
                    $retry--;
                }
            }while($retry > 0);

            $formatSnsType = self::CONVERT_SNS_TYPE_TO_STD_TYPE_MAP[$snsType];
            $understanding[$formatSnsType] = $understandingWithSnsType;
        }

        return $understanding;
    }

    /*
     * 手动设置historyMessage,调用ai-connector
     * snsType : {@see self::CONVERT_SNS_TYPE_TO_STD_TYPE_MAP}
     */
    private function generateUnderstanding($systemPrompt, $question, $model, array $historyMessage = []) {
        $requestBeginTime = microtime(true);
        $aiClient = new AIClient();
        $className = basename(str_replace('\\', '/', get_class($this)));
        $aiClient->setTrace("OKKI_AI", $className);
        $aiClient->setModel($model);
        $aiClient->setSystemPrompt($systemPrompt);
        $aiClient->setQuestion($question);
        $aiClient->setHistoryMessages($historyMessage);
        $aiClient->setResponseFormat(['type' => 'json_object']);
        $aiClient->setTrace("OKKI_AI",basename(str_replace('\\', '/', get_class($this))));
        $aiClient->setTemperature(0.1);
        $originResponse = $aiClient->chatCompletions();

        $requestEndTime = microtime(true);

        $response = $aiClient->convertResponse($originResponse['data']);
        $response = $this->formatJson($response);

        // 保存 record 记录
        $aiServiceProcessRecordPdo = new AiServiceProcessRecord();
        $aiServiceProcessRecordPdo->params = '{}';
        $aiServiceProcessRecordPdo->scene_type = $this->sceneType;
        $aiServiceProcessRecordPdo->refer_id = $this->companyId;
        $aiServiceProcessRecordPdo->question = $question;
        $aiServiceProcessRecordPdo->answer = $response['answer'];
        $aiServiceProcessRecordPdo->client_id = $this->clientId;
        $aiServiceProcessRecordPdo->create_user = $this->userId;
        $aiServiceProcessRecordPdo->status = 1;
        $aiServiceProcessRecordPdo->ai_model = $model;
        $aiServiceProcessRecordPdo->prompt_tokens = $response['promptTokens'];
        $aiServiceProcessRecordPdo->completion_tokens = $response['completionTokens'];
        $aiServiceProcessRecordPdo->total_tokens = $response['totalTokens'];
        $aiServiceProcessRecordPdo->agent_version_id = $this->versionId;
        $aiServiceProcessRecordPdo->request_data = json_encode($aiClient->buildParams());
        $aiServiceProcessRecordPdo->request_response = json_encode($originResponse, JSON_UNESCAPED_UNICODE);
        $aiServiceProcessRecordPdo->request_time = ($requestEndTime - $requestBeginTime) * 1000;
        $aiServiceProcessRecordPdo->save();

        return json_decode($response['answer'], true);
    }

    private function annotationByUnderstanding($companyId,$understanding) : array
    {
        if(empty($understanding)) {
            return [];
        }

        //'annotationByUnderstanding.systemPrompt'
        $systemPrompt = $this->systemPromptMap['annotationByUnderstanding.systemPrompt'];
        $userPrompt = <<<EOF
请分析以下今日买卖家之间的业务沟通内容，并判断系统提示中列出的各项命题是否为真：
```
{{communication_today}}
```
EOF;
        $model = AiServiceConstant::SERVICE_QWEN_MAX_LATEST;
        $question = [];
        foreach ($understanding as $channel => $item){
            $content = json_encode($item['conversation_content'] ?? [], JSON_UNESCAPED_UNICODE);
            $question[] = <<<EOF
$channel: $content
EOF;
        }
        $question = implode(PHP_EOL, $question);
        $question = $this->replacePlaceholders($userPrompt, ['communication_today' => $question]);

        $gptResponse = $this->callByParams($companyId, $question, $systemPrompt, $model, 0.1, jsonObject: true);
        $answer = $this->formatResponseAnswer($gptResponse['answer']);
        $answer = json_decode($answer, true);

        $events = [];
        foreach ($answer as $eventName => $flag)
        {
            if($flag && isset(self::OCCURRED_EVENT_CN_TO_ENG_MAP[$eventName])) {
                // 兼容老数据的格式
                $events[] = ['event_name' => self::OCCURRED_EVENT_CN_TO_ENG_MAP[$eventName]];
            }
        }
        return $events;
    }

    /**
     * 判断当日是否有实质内容
     * @param array $conversations, user+customer当日聊天记录
     * @return bool true-有实质内容;false-无实质内容
     */
    private function checkIsSubstantial(mixed $conversations) : bool
    {
        if(empty($conversations)) {
            return false;
        }

        //sns_type类型大于1
        if(count($conversations) > 1) {
            return true;
        }

        //聊天消息数量大于10
        $matches = [];
        $msgCnt = preg_match_all("/(?:Customer|Salesperson):/", $conversations[0]['content'] ?? '',$matches);
        if($msgCnt > 10) {
            return true;
        }

        //gpt判断
        $content = $conversations[0]['content'] ?? '';
        $systemPrompt = $this->systemPromptMap['is_substantial_prompt'] ?? '';
        if(empty($systemPrompt)) {
            $systemPrompt = <<<EOF
# ROLE
You are an expert B2B international trade communication analyst with extensive experience in cross-border trade communications. Your core responsibility is to analyze conversations between salespeople and customers, extracting and categorizing key business information. You excel at identifying critical business signals and accurately understanding both technical terminology and subtle implications in international trade communications.

# TASK
Your task is to determine if today's conversation between the salesperson and customer contains no substantial content and requires no further analysis.

# EVALUATION CRITERIA
Non-substantial content ONLY includes:
1. Pure greetings without follow-up (e.g., "Good morning", "How are you")
2. Generic holiday wishes (e.g., "Happy New Year", "Enjoy your holiday")
3. Simple acknowledgments (e.g., "OK", "Got it", "Thanks")
4. Unanswered greetings (e.g., "Hello?" with no reply)
5. Vague postponement messages only (e.g., "Will contact you later", "Will get back to you soon")

Any conversation containing other content, including but not limited to:
- Business discussions
- Schedule information
- Status updates
- Questions about products/orders
Should be considered substantial and passed to subsequent analysis.

# OUTPUT FORMAT

Respond in JSON format:
{
  "thinking": string,  // brief thinking process (max 50 words)
  "is_substantial": boolean,  // true if conversation needs further analysis
}
EOF;
        }

        $userPrompt = "# Input\n```\n{$content}\n```";
        $model = $this->promptConfig['is_substantial'] ?? AIClient::QWEN_PLUS_LATEST;

        for ($retries = 0; $retries < 3; $retries++)
        {
            $gptResponse = $this->callByParams($this->companyId,$userPrompt,$systemPrompt,$model,0.1);
            $answer = $this->formatResponseAnswer($gptResponse['answer'] ?? '');
            $answer = !empty($answer) ? json_decode($answer, true) : [];

            if(isset($answer['thinking']) && isset($answer['is_substantial'])) {
                return $answer['is_substantial'];
            }
        }

        return false;
    }

    /**
     * 保存Agent记忆
     * @table tbl_ai_quality_check_agent_analysis
     * @params
     * @return void
     */
    private function saveAgentMemories($clientId, $companyId, $userId, $customerId,$agentId, array $analyze_result, $journeyId = 0) : void {
        //gpt记忆
        $analysis = new AiQualityCheckAgentAnalysis();
        $analysis->client_id = $clientId;
        $analysis->company_id = $companyId;
        $analysis->user_id = $userId;
        $analysis->customer_id = $customerId;
        $analysis->agent_id = $agentId;
        $analysis->contact_date = $this->dateTime;
        $analysis->analyze_result = json_encode($analyze_result, JSON_UNESCAPED_UNICODE);
        if (!empty($journeyId)) {
            $analysis->journey_id = $journeyId;
        }

        //(client_id,company_id,user_id,customer_id,agent_id,contact_date)幂等
        $analysisDO = $analysis->getAttributes();

        list($sql, $param) = SqlBuilder::buildMultiInsert("tbl_ai_quality_check_agent_analysis", [$analysisDO], false);
        $sql .= " on conflict (client_id,company_id,user_id,customer_id,agent_id,contact_date) do nothing ";

        \AiQualityCheckAgentAnalysisModel::model()->getDbConnection()->createCommand($sql)->execute($param);
    }

    /**
     * 聊天内容半结构化
     * @return array
     */
    private function segmentCommunicationToTopic($todayChatRecords, $recentSummaries, $immediateContext) : array
    {
        $systemPrompt = $this->systemPromptMap['communication_segmentation_prompt'] ?? '';
        if(empty($systemPrompt)) {
            $systemPrompt = <<<EOF
# ROLE
You are an expert B2B international trade communication analyst with extensive experience in cross-border trade communications. Your core responsibility is to analyze conversations between salespeople and customers, extracting and categorizing key business information. You excel at identifying critical business signals and accurately understanding both technical terminology and subtle implications in international trade communications.

# TASK
Analyze today's chat records by segmenting conversations into distinct business-related topics, and systematically extract essential information from each segment to create a comprehensive structured overview of the communication.

# ANALYSIS REQUIREMENTS
1. Strictly distinguish between salesperson and customer messages.
2. Focus only on today's conversations.
3. Analyze each topic separately.
4. Ensure all extracted information accurately reflects the original conversation.
5. Do not speculate or add information not explicitly mentioned in the conversation.

# OUTPUT FORMAT
Output in JSON format, with each topic as a separate object:
{
  "conversation_segments": [
    {
      "topic": "Brief description of the topic discussed",
      "key_information": "Key information exchanged during today's discussion, including specific details shared (e.g., prices, quantities, specifications, requirements, timelines, etc.)",
      "actions_taken": "Substantive actions taken today by either salesperson or customer to advance business progress (e.g., files sent, solutions provided, payments made)",
      "agreements": "Specific agreements, decisions, or conclusions mutually confirmed by both parties during today's conversation (e.g., confirmed prices, agreed delivery terms, finalized specifications)",
      "pending_items": "Pending matters or topics from today's discussion that require further action, follow-up or agreement (e.g., items awaiting customer decision, matters requiring internal review, topics needing additional discussion)"
    }
    // repeat for each topic
  ]
}

# EXAMPLES

## Example 1
Input:
```
Salesperson: Hello, regarding the product specifications we discussed last time, our engineers have confirmed we can customize according to your requirements.
Customer: Great, what about the price?
Salesperson: Considering the customization, it will be 10% more expensive than the standard version. I've sent the detailed quotation to your email
Customer: Received, I'll check the quotation. Can you guarantee the delivery time?
Salesperson: Normally 45 days, we can expedite to 30 days if urgent, but that will incur an additional 5% fee.
Customer: I see, let me discuss with my team and get back to you tomorrow.
```

Output:
{
  "conversation_segments": [
    {
      "topic": "Product customization specifications and quotation",
      "key_information": "Engineers confirmed ability to meet customization requirements; Salesperson informed 10% price increase for custom version; Salesperson proposed delivery options: 45 days standard or 30 days expedited with 5% fee",
      "actions_taken": "Salesperson has sent detailed quotation to customer's email",
      "agreements": "Engineering team confirmed feasibility of customer specifications",
      "pending_items": "Customer will discuss pricing and delivery timeline with team, response expected tomorrow"
    }
  ]
}

## Example 2
Input:
```
Salesperson: Hi, I'm following up on the sample we sent last week. Have you received and tested it?
Customer: Yes, we got it. The quality looks good but we found some minor issues with the packaging.
Salesperson: Could you please share more details about the packaging issues?
Customer: The box is a bit loose and some products were scratched during shipping.
Salesperson: I understand. I'll discuss with our packaging team to improve it. For the next order, would you like us to:
1. Use stronger boxes
2. Add more protective padding
3. Both
Customer: Let's go with both options. Also, can you send me a sample of the improved packaging?
Salesperson: Sure, I'll arrange that. We'll prepare a new sample with enhanced packaging next week.
```

Output:
{
  "conversation_segments": [
    {
      "topic": "Sample feedback and packaging improvement",
      "key_information": "Customer received and tested sample; Customer reported good product quality but packaging issues - loose box causing product scratches; Salesperson proposed solutions: stronger boxes and additional protective padding",
      "actions_taken": "Salesperson discussed packaging improvements with customer",
      "agreements": "Customer approved both packaging improvements; Salesperson committed to providing new sample with enhanced packaging",
      "pending_items": "New sample with improved packaging to be prepared and delivered next week"
    }
  ]
}
EOF;
        }

        $userPrompt = $this->userPromptMap['communication_segmentation_prompt'] ?? '';
        if(empty($userPrompt)){
            $userPrompt = <<<EOF
# IMPORTANT NOTES ON CHAT RECORD FORMAT
- When analyzing the chat records, you MUST clearly distinguish between messages sent by the salesperson and those sent by the customer. Never confuse or mix up who said what. Messages are typically marked as:
  * Salesperson messages: "Salesperson: [message content]"
  * Customer messages: "Customer: [message content]"
- The chat records may contain messages from multiple communication channels between both parties, such as TM (Note: TM is the chat tool used for communication between buyers and sellers on alibaba.com), WhatsApp, email, etc.
- In the chat records, placeholders in the form of '#xxx#' represent special non-text messages. For example, '#Image#' represents an image, '#File#' represents a file. Notably, '#Inquiry#' is a special card message representing a product inquiry.

# BACKGROUND INFORMATION

## TODAY'S DATE
Today's date is: {{date_today}}
Today is: {{weekday}}

## RECENT COMMUNICATION SUMMARIES (LAST 30 DAYS)
Here are the key points from recent conversations between the salesperson and customer within the past 30 days:
```
{{recent_summaries}}
```

## IMMEDIATE CONTEXT (LAST 30 DAYS)
Below are the last 10 messages from the previous conversation within the past 30 days, which provide direct context for today's chat:
```
{{immediate_context}}
```

# TODAY'S CHAT RECORDS
Here are the chat records for today:

{{today_chat_records}}
EOF;
        }

        $llmModel = $this->promptConfig['communication_segmentation.service'] ?? AIClient::QWEN_PLUS_LATEST;

        $dateToday = $this->dateTime;
        $weekday = \DateTime::createFromFormat('Y-m-d', $this->dateTime)->format('l'); //星期几
        $question = $this->replacePlaceholders($userPrompt,[
            'date_today' => $dateToday,
            'weekday' => $weekday,
            'recent_summaries' => $recentSummaries,
            'immediate_context' => $immediateContext,
            'today_chat_records' => $todayChatRecords,
        ]);

        $gptResponse = $this->callByParams($this->companyId, $question, $systemPrompt, $llmModel, 0.1,jsonObject: true);
        $answer = $this->formatResponseAnswer($gptResponse['answer'] ?? '');
        $answer = !empty($answer) ? json_decode($answer, true) : [];

        if (!empty($answer) && !empty($answer['conversation_segments']))
        {
            return $answer['conversation_segments'];
        }

        return [];
    }

    /**
     * 标注关键事件
     * @return array
     */
    private function annotation($todayChatRecords, $recentSummaries, $immediateContext, $conversationSegments) : array
    {
        $systemPrompt = $this->systemPromptMap['annotation'] ?? '';
        if(empty($systemPrompt)) {
            $systemPrompt = <<<EOF
# ROLE
You are an expert B2B international trade communication analyst with extensive experience in cross-border trade communications. Your core responsibility is to analyze conversations between salespeople and customers, extracting and categorizing key business information. You excel at identifying critical business signals and accurately understanding both technical terminology and subtle implications in international trade communications.

# TASK
Your task is to carefully analyze today's chat records and identify any key business events that occurred.

# EVENT DEFINITIONS
Key events in international trade communications are defined as follows:
```
{
  "New inquiry": "CUSTOMER expressed interest in specific products today.",
  "Sample request": "CUSTOMER requested a product sample for evaluation. This event occurs when the customer explicitly asks for a sample to assess product quality, specifications, and suitability before proceeding with a bulk order, which is a common practice in international trade communications.",
  "Product quotation": "SALESPERSON provided or updated a product quotation today, either through text messages, images (#Image#) or files (#File#). This refers to the initial product pricing stage where the salesperson provides prices for specific products, not discussions about order amounts or payment terms that occur after the quotation phase.",      
  "Logistics quotation": "SALESPERSON provided or updated a logistics quotation today, which may include domestic freight (e.g. cost for delivery to freight forwarder), international shipping fees (e.g. cost for ocean freight), customs clearance charges, or other transportation expenses.",
  "Proforma invoice": "SALESPERSON provided or updated the PI (proforma invoice) today.",
  "Customer payment": "CUSTOMER notified that they had made a payment today, or SALESPERSON confirmed receipt of payment from the customer today. Due to the nature of cross-border B2B trade, the actual payment action and receipt confirmation may occur on different days, so both customer's payment notification and salesperson's receipt confirmation are considered as payment events.",
  "Order in production update": "SALESPERSON provides latest production progress information for an order that is currently in production today. The information must be new and substantive for customer. For example: 'Your order has entered the assembly stage, 50% completed' is substantive, while 'Production is ongoing' is not substantive.",
  "Order in transit update": "SALESPERSON provides latest shipping progress information for an order that is currently in transit today. The information must be new and substantive for customer. For example: 'Your shipment has arrived at Hamburg port on Jan 5th' is substantive, while 'Shipping is in progress' is not substantive.",
  "Product arrival": "CUSTOMER reported the arrival of the samples or products today, marking the end of the shipping process.",
  "Repurchase intention": "CUSTOMER expressed clear intention to place repeat orders for previously purchased products today. This must be an explicit expression of repurchase interest, not just general satisfaction with previous orders or vague future possibilities."
}
```

# ANNOTATION REQUIREMENTS
1. Carefully analyze today's communication content and gain deep understanding of interactions between salesperson and customer:
    - Review each conversation segment in detail
    - Accurately grasp current business progress and status
2. Analyze each event separately
3. Always think through the evidence and reasoning before making a final conclusion
4. For an event to be marked as occurred=true, it must have definitively happened TODAY, not in the past or planned for the future.
5. The output must include ALL events defined in <EVENT DEFINITIONS>


# OUTPUT FORMAT
Respond in JSON format:
{
"event_annotations": [
  {
    "event_name": string, // Event name, must be one of the events defined in EVENT DEFINITIONS
    "thinking": string, // evidence and reasoning for determining if the event occurred today
    "occurred": boolean, // true if the event occurred today, false otherwise
  }
  // Repeat the above structure for each event defined in EVENT DEFINITIONS
  ]
}
EOF;
        }

        $userPrompt = $this->userPromptMap['annotation'] ?? '';
        if(empty($userPrompt)) {
            $userPrompt = <<<EOF
# IMPORTANT NOTES ON CHAT RECORD FORMAT
- When analyzing the chat records, you MUST clearly distinguish between messages sent by the salesperson and those sent by the customer. Never confuse or mix up who said what. Messages are typically marked as:
  * Salesperson messages: "Salesperson: [message content]"
  * Customer messages: "Customer: [message content]"
- The chat records may contain messages from multiple communication channels between both parties, such as TM (Note: TM is the chat tool used for communication between buyers and sellers on alibaba.com), WhatsApp, email, etc.
- In the chat records, placeholders in the form of '#xxx#' represent special non-text messages. For example, '#Image#' represents an image, '#File#' represents a file. Notably, '#Inquiry#' is a special card message representing a product inquiry.

# BACKGROUND INFORMATION

## TODAY'S DATE
Today's date is: {{date_today}}
Today is: {{weekday}}

## RECENT COMMUNICATION SUMMARIES (LAST 30 DAYS)
Here are the key points from recent conversations between the salesperson and customer within the past 30 days:
```
{{recent_summaries}}
```

## IMMEDIATE CONTEXT (LAST 30 DAYS)
Below are the last 10 messages from the previous conversation within the past 30 days, which provide direct context for today's chat:
```
{{immediate_context}}
```

# TODAY'S CHAT RECORDS
Here are the chat records for today:

{{today_chat_records}}

# OTHER AGENTS' ANALYSIS RESULTS
Below are the analysis results from other agents regarding today's conversation:

## COMMUNICATION SEGMENTATION AGENT
- Role: Communication Segmentation Assistant
- Task: Analyze today's chat records by segmenting conversations into distinct business-related topics, and systematically extract essential information from each segment to create a comprehensive structured overview of the communication.
- Result:
```
{{conversation_segments}}
```
EOF;
        }

        $llmModel = $this->promptConfig['annotation'] ?? AIClient::QWEN_PLUS_LATEST;


        $dateToday = $this->dateTime;
        $weekday = \DateTime::createFromFormat('Y-m-d', $this->dateTime)->format('l'); //星期几
        $question = $this->replacePlaceholders($userPrompt,[
            'date_today' => $dateToday,
            'weekday' => $weekday,
            'recent_summaries' => $recentSummaries,
            'immediate_context' => $immediateContext,
            'today_chat_records' => $todayChatRecords,
            'conversation_segments' => json_encode($conversationSegments,JSON_UNESCAPED_UNICODE),
        ]);

        $gptResponse = $this->callByParams($this->companyId, $question, $systemPrompt, $llmModel, 0.1, jsonObject: true);
        $answer = $this->formatResponseAnswer($gptResponse['answer'] ?? '');
        $answer = !empty($answer) ? json_decode($answer, true) : [];

        $eventAnnotations = [];
        $occurredEvents = [];
        if (!empty($answer) && !empty($answer['event_annotations']))
        {
            $eventAnnotations = $answer['event_annotations'];
            $occurredEvents = array_values(array_filter($eventAnnotations, function ($event) {
                return isset($event['occurred']) && $event['occurred'] == 'true';
            }));
        }

        return [
            $eventAnnotations,
            $occurredEvents
        ];
    }

    /**
     * 最近range天内最近一次沟通,begin_time最晚的沟通渠道的最后messageNum条消息
     * @params $range 时间窗口
     * @params $messageNum 消息条数
     * @return string immediate_context
     */
    private function buildImmediateContext($clientId, $userId, $customerId, $range, $messageNum) : string
    {
        //默认返回值(双方近期无沟通)
        $defaultContext = "No chat history available within the past 30 days";

        //查询conversation
        $conversationList = new AiQualityCheckCompanyConversationList($clientId);
        $conversationList->setCompanyId($this->companyId);
        $conversationList->setUserId($userId);
        $conversationList->setCustomerId($customerId);
        $conversationList->setStartContactDate(date('Y-m-d',strtotime("-{$range} day",strtotime($this->dateTime))));
        $conversationList->setEndContactDate(date('Y-m-d',strtotime('-1 day',strtotime($this->dateTime))));
        $conversations = $conversationList->find();

        if (!empty($conversations)) {
            //根据begin_time倒排
            usort($conversations, function ($a, $b) {
                return strcmp($b['day_first_message_time'], $a['day_first_message_time']);
            });

            //最近messageNum条
            $latestConversation = $conversations[0];
            $content = $latestConversation['content'] ?? '';
            $immediateContexts = [];
            //正则匹配,单测看着效果可以
            $pattern = '/(?:Customer:|Salesperson:)\s*(.+?)(?=(?:\n(?:Customer:|Salesperson:)|$))/s';
            if (preg_match_all($pattern, $content, $matches)) {
                $count = count($matches[1]);
                //从后向前找messageNum条
                for ($i = $count - 1; $i >= max(0, $count - $messageNum); $i--) {
                    $immediateContexts[] = $matches[1][$i];
                }
            }

            //反转
            $immediateContexts = array_reverse($immediateContexts);

            $immediateContext = implode(PHP_EOL, $immediateContexts);

            return empty($immediateContext) ? $defaultContext : $immediateContext;
        }

        return $defaultContext;
    }

    private function buildRecentSummary($clientId, $userId, $customerId, $range, $limit) : string {
        $defaultRecentSummaries = "No communication summaries available within the past 30 days";

        $analysisList = new AiQualityCheckAgentAnalysisList($clientId);
        $analysisList->setUserId($userId);
        $analysisList->setCustomerId($customerId);
        $analysisList->setStartContactDate(date('Y-m-d',strtotime("-{$range} day",strtotime($this->dateTime))));
        $analysisList->setEndContactDate(date('Y-m-d',strtotime('-1 day',strtotime($this->dateTime))));
        $analysisList->setAgentId(self::QC_JOURNEY_GENERATION_AGENT_ID);
        $analysisList->setOrderBy('contact_date');
        $analysisList->setOrder('desc');
        $analysisList->setLimit($limit);
        $list = $analysisList->find();

        $unitTemplate = <<<EOF
    {
        "date":"{{date}}",
        "communication_summary":"{{communicationSummary}}"
    }
EOF;

        $recentSummariesTemplate = <<<EOF
[
{{units}}
]
EOF;

        if(!empty($list)) {
            $units = [];
            foreach ($list as $item)
            {
                $analyzeResult = json_decode($item['analyze_result'] ?? "" , true);
                $date = $item['contact_date'];
                $communicationSummary = $analyzeResult['summary'] ?? "";
                $units[] = $this->replacePlaceholders($unitTemplate, [
                    'date' => $date,
                    'communicationSummary' => $communicationSummary
                ]);
            }

            $units = implode(PHP_EOL, $units);
            return $this->replacePlaceholders($recentSummariesTemplate,[
                'units' => $units,
            ]);
        }

        return $defaultRecentSummaries;
    }

    /**
     * 将聊天内容转换为user_prompt中的today_chat_records
     * @param $conversations
     * @return string today_chat_records占位符
     */
    private function buildTodayChatRecords($conversations) : string
    {
        $todayChatRecords = [];
        $todayCharRecordsPrompt = <<<EOF
## {{channel_name}} (Start at: {{begin_time}})
```
{{content}}
```
EOF;

        foreach ($conversations as $conversation)
        {
            $content = $conversation['content'] ?? '';
            $snsType = $conversation['sns_type'] ?? '';
            $channelName = self::CONVERT_SNS_TYPE_TO_STD_TYPE_MAP[$snsType] ?? $snsType;
            $beginTime = $conversation['day_first_message_time'] ?? '1970-01-01';
            $todayChatRecords[] = $this->replacePlaceholders($todayCharRecordsPrompt, [
                'channel_name' => $channelName,
                'begin_time' => $beginTime,
                'content' => $content,
            ]);
        }

        return implode(PHP_EOL, $todayChatRecords);
    }

    /**
     * 情感分析Agent
     * @return array
     */
    public function sentimentAnalysis(array $runContext) : array
    {
        $range = $runContext['range'];
        $limit = $runContext['limit'];
        $msgNum = $runContext['messageNum'];

        $userPrompt = $this->userPromptMap['sentiment_analysis_prompt'] ?? '';
        if(empty($userPrompt))
        {
            $userPrompt = <<<EOF
# IMPORTANT NOTES ON CHAT RECORD FORMAT
- When analyzing the chat records, you MUST clearly distinguish between messages sent by the salesperson and those sent by the customer. Never confuse or mix up who said what. Messages are typically marked as:
  * Salesperson messages: "Salesperson: [message content]"
  * Customer messages: "Customer: [message content]"
- The chat records may contain messages from multiple communication channels between both parties, such as TM (Note: TM is the chat tool used for communication between buyers and sellers on alibaba.com), WhatsApp, email, etc.
- In the chat records, placeholders in the form of '#xxx#' represent special non-text messages. For example, '#Image#' represents an image, '#File#' represents a file. Notably, '#Inquiry#' is a special card message representing a product inquiry.

# BACKGROUND INFORMATION

## TODAY'S DATE
Today's date is: {{date_today}}
Today is: {{weekday}}

## RECENT COMMUNICATION SUMMARIES AND SENTIMENT ANALYSIS (LAST 30 DAYS)
Here are the key points and customer sentiment analysis from recent conversations between the salesperson and customer within the past 30 days:
```
{{recent_summaries}}
```

## IMMEDIATE CONTEXT (LAST 30 DAYS)
Below are the last 10 messages from the previous conversation within the past 30 days, which provide direct context for today's chat:
```
{{immediate_context}}
```

# TODAY'S CHAT RECORDS
Here are the chat records for today:

{{today_chat_records}} 
EOF;
        }

        $llmModel = $this->promptConfig['sentiment_analysis'] ?? AIClient::QWEN_PLUS_LATEST;

        //正则匹配'Customer:',判断是否含有来自客户发送的消息
        $pattern = "/(?:Customer):/";
        $conversations = $runContext['conversations'] ?? [];
        $dateToday = $this->dateTime;
        $weekday = \DateTime::createFromFormat('Y-m-d', $this->dateTime)->format('l');
        foreach($conversations as $pair => $conversation)
        {
            $ss = explode("-", $pair);
            $userId = $ss[0];
            $customerId = $ss[1];

            $this->recordUserId = $userId;
            $this->recordCustomerId = $customerId;
            $this->recordAgentId = self::QC_SENTIMENT_ANALYSIS_AGENT_ID;

            if ($this->checkNeedSkipV3(self::QC_SENTIMENT_ANALYSIS_AGENT_ID, $userId, $customerId)) {
                \LogUtil::info("AiCompanyQualityCheck_V3_SentimentAnalysisAgent: Skip By Idempotent",[
                    "clientId" => $this->clientId,
                    "companyId" => $this->companyId,
                    "date" => $this->dateTime,
                    "pair" => $pair,
                ]);
                continue;
            }

            $recentSummaries = $this->buildRecentSummary($this->clientId, $userId, $customerId, $range, $limit);

            $immediateContext = $this->buildImmediateContext($this->clientId, $userId, $customerId, $range, $msgNum);

            $todayChatRecords = $this->buildTodayChatRecords($conversation);

            $allContent = implode("\n", array_column($conversation, 'content'));

            //userPrompt与客户是否发送消息无关
            $question = $this->replacePlaceholders($userPrompt,[
                'date_today' => $dateToday,
                'weekday' => $weekday,
                'recent_summaries' => $recentSummaries,
                'immediate_context' => $immediateContext,
                'today_chat_records' => $todayChatRecords,
            ]);

            $cnt = preg_match_all($pattern, $allContent, $matches);
            if($cnt > 0){
                //1.客户有发送消息
                $systemPrompt = $this->systemPromptMap['sentiment_analysis_prompt'] ?? '';
                if(empty($systemPrompt))
                {
                    $systemPrompt = <<<EOF
# ROLE
You are an analyst specializing in customer sentiment analysis in B2B international trade scenarios. You excel at accurately detecting customers' underlying emotional states from their messages, including subtle hints, tone changes, and implicit attitudes that may not be immediately obvious. Your deep understanding of customer psychology and communication patterns enables you to identify both explicit and implicit emotional signals.

# TASK
Analyze today's conversation between the customer and salesperson, focusing specifically on the customer's emotional state in their final messages of the conversation. This final sentiment is crucial as it represents the customer's most recent attitude.

# SENTIMENT LEVEL DEFINITIONS
- Positive:
* Shows clear willingness to cooperate in final response
* Expresses satisfaction with products or services
* Responds positively to salesperson's latest suggestions 
* Actively advances the transaction process
* Uses positive and friendly tone in closing messages

- Neutral:
* Maintains professional and objective communication until the end
* Shows neither clearly positive nor negative signals in final response
* Communicates in a fact-oriented manner
* Takes a reserved stance on products or services
* Needs more information for decision-making

- Negative:
* Expresses dissatisfaction or disappointment in final messages
* Questions the value of products or services
* Rejects salesperson's latest proposals
* Raises serious issues or complaints without resolution
* Uses negative or impatient tone in closing response
* Shows significantly decreased willingness to cooperate

# OUTPUT FORMAT
Respond in JSON format:
{
"sentiment_level": "positive/neutral/negative",
"reasoning": "Explanation of this judgment, focusing on customer's final messages and citing specific conversation content as evidence"
}
EOF;
                }

                for ($i = 0; $i < 3; $i++) {
                    $gptResponse = $this->callByParams($this->companyId, $question, $systemPrompt, $llmModel, temperature: 0.1, jsonObject: true);
                    $answer = $this->formatResponseAnswer($gptResponse['answer'] ?? '');
                    $answer = !empty($answer) ? json_decode($answer, true) : [];

                    if(!empty($answer) && !empty($answer['sentiment_level']) && in_array($answer['sentiment_level'],[self::SENTIMENT_LEVEL_POSITIVE,self::SENTIMENT_LEVEL_NEUTRAL, self::SENTIMENT_LEVEL_NEGATIVE])){
                        $this->saveAgentMemories($this->clientId,$this->companyId,$userId,$customerId,self::QC_SENTIMENT_ANALYSIS_AGENT_ID,$answer);
                        break;
                    }
                }
            }else{
                //2.客户没有发送消息
                $systemPrompt = $this->systemPromptMap['no_response_analysis_prompt'] ?? '';
                if(empty($systemPrompt)){
                    $systemPrompt = <<<EOF
# ROLE
You are an analyst specializing in B2B international trade communication analysis.

# TASK
Analyze what key information or questions the salesperson has provided/asked today, given that the customer has not responded.


# OUTPUT FORMAT
Respond in JSON format:
{
"pending_response_points": "Summary of key information/questions provided by the salesperson today"
}

# EXAMPLES
Example 1:
```
Salesperson: Hi John, following up on our discussion about the steel pipes. I've attached the detailed specifications and pricing for the 3 models we discussed. Let me know if you need any clarification.
Salesperson: #File#
```
{
"pending_response_points": "Salesperson provided product specifications and pricing information for steel pipes, awaiting customer's review and feedback on these details"
}

Example 2:
```
Salesperson: Good morning! Just checking if you've had a chance to review the quotation I sent yesterday? We can adjust the MOQ if needed.
```
{
"pending_response_points": "Salesperson followed up on a previous quotation and offered flexibility on MOQ terms, waiting for customer's response on these points"
}

Example 3:
```
Salesperson: Hello Ms. Zhang, regarding your inquiry about delivery time - we can ship within 25 days after order confirmation. Also, would you prefer FOB or CIF terms?
```
{
"pending_response_points": "Salesperson provided delivery timeline information and requested customer's preference on shipping terms (FOB vs CIF), waiting for customer's response on these points"
}

Example 4:
```
Salesperson:  #Image#
Salesperson:  #Image#
Salesperson:  #Image#
Salesperson:  #Image#
Salesperson:  Hello, I would like to introduce our newly launched product - candle cans. Please feel free to contact us if you are interested.
```
{
"pending_response_points": "Salesperson proactively attempted to generate customer interest by introducing a new product (candle cans) with multiple product images, waiting for customer's response on whether this product matches their needs"
}
EOF;
                }

                $gptResponse = $this->callByParams($this->companyId, $question, $systemPrompt, $llmModel, temperature: 0.1, jsonObject: true);
                $answer = $this->formatResponseAnswer($gptResponse['answer'] ?? '');
                $answer = !empty($answer) ? json_decode($answer, true) : [];

                if(!empty($answer) && !empty($answer['pending_response_points'])) {
                    $this->saveAgentMemories($this->clientId,$this->companyId,$userId,$customerId,self::QC_SENTIMENT_ANALYSIS_AGENT_ID,[
                        "sentiment_level" => self::SENTIMENT_LEVEL_NOT_RESPONSE,
                        "reasoning" => $answer['pending_response_points']
                    ]);
                }
            }

            $this->recordUserId = '';
            $this->recordCustomerId = '';
            $this->recordAgentId = 0;

        }

        return [$runContext, false];
    }

    /**
     * @depracated 已废弃
     * @see CompanyQualityCheckAiAgentV2::journeyGenerationV2
     * Journey Generation Agent
     * @return array
     */
    public function journeyGeneration(array $runContext) : array
    {
        $systemPrompt = $this->systemPromptMap['chat_journey_agent'] ?? '';
        if(empty($systemPrompt)){
            $systemPrompt = <<<EOF
# ROLE
You are a seasoned expert in B2B cross-border trade with extensive knowledge in international business operations, trade practices, and industry standards. You have deep understanding of the entire export process including product sourcing, quotation, negotiation, order fulfillment, logistics and payment terms.

# TASK
Based on today's chat records between the salesperson and customer, generate a clear and comprehensive summary.

# CONSTRAINTS
- Strictly distinguish between salesperson and customer messages.
- Focus only on today's conversations.
- Ensure the summary of today's business progress accurately reflects the original conversation.
- Never fabricate or infer meanings that are not explicitly expressed by either the salesperson or customer in the conversation. Stick strictly to what was actually communicated.

# SUMMARY REQUIREMENTS
- Use concise and clear language to summarize the business progress, while ensuring comprehensive coverage of all critical business details.
- Do not include any placeholders (e.g. #Image#, #File#, #Inquiry#) in the summary, instead describe them in natural language (e.g. "Customer sent a product image", "Salesperson shared a product catalog file", "Customer submitted a product inquiry").
- Focus on describing what actually occurred in the conversation, rather than what did not happen. Avoid ending summaries with statements about missing elements or lack of progress(e.g. "No further business details or actions were discussed."), as these detract from the professional tone and add no value to understanding the interaction.

# OUTPUT FORMAT
Output in JSON format:
{
  "summary": "Summary of today's business progress"
}

# EXAMPLES
Below are reference examples demonstrating effective summary writing:

## Examples of Summaries with Substantial Business Progress
The following examples illustrate how to capture key business developments clearly and professionally:
1. Salesperson and customer discussed shipping options for the order of 500 units of custom-made leather shoes. Salesperson suggested DHL Express with an estimated delivery time of 7-10 days, and customer agreed to proceed with this option.
2. Customer tested the received samples and found satisfactory product quality while noting some packaging issues. Salesperson proposed a comprehensive solution including reinforced boxes and extra protective padding. Customer approved both improvements, and salesperson committed to delivering enhanced packaging samples the following week.
3. Salesperson informed customer after technical review, engineering team confirmed feasibility of customer's customization requirements. Salesperson provided updated quotation with 10% price increase for customization. Customer is reviewing pricing and delivery options (standard 45 days vs expedited 30 days with 5% premium).
4. Customer placed initial trial order for 1000 units of leash. Key terms agreed: unit price $5.20, 30% deposit payment, balance before shipment, delivery in 35 days. Salesperson sent PI and awaiting customer's deposit payment.
5. Salesperson and customer finalized product specifications with three customization requirements: material changed to 316L stainless steel, surface finish upgraded to mirror polish, and carton packaging capacity increased to 200 pieces. Customer confirmed acceptance and will submit purchase order within this week.

## Examples of Summaries for Basic Interactions
The following examples demonstrate how to summarize brief or one-sided communications:
1. Salesperson greeted customer and asked about their recent status. No response received from customer.
2. Salesperson and customer exchanged New Year greetings with no further business discussion.
3. Customer sent a product availability inquiry. No response received from salesperson.
4. Salesperson sent a follow-up message regarding previous quotation. No response received from customer.
EOF;
        }

        $userPrompt = $this->userPromptMap['chat_journey_agent'] ?? '';
        if(empty($userPrompt)) {
            $userPrompt = <<<EOF
# IMPORTANT NOTES ON CHAT RECORD FORMAT
- When analyzing the chat records, you MUST clearly distinguish between messages sent by the salesperson and those sent by the customer. Never confuse or mix up who said what. Messages are typically marked as:
  * Salesperson messages: "Salesperson: [message content]"
  * Customer messages: "Customer: [message content]"
- The chat records may contain messages from multiple communication channels between both parties, such as TM (Note: TM is the chat tool used for communication between buyers and sellers on alibaba.com), WhatsApp, email, etc.
- In the chat records, placeholders in the form of '#xxx#' represent special non-text messages. For example, '#Image#' represents an image, '#File#' represents a file. Notably, '#Inquiry#' is a special card message representing a product inquiry.

# BACKGROUND INFORMATION

## TODAY'S DATE
Today's date is: {{date_today}}
Today is: {{weekday}}

## RECENT COMMUNICATION SUMMARIES (LAST 30 DAYS)
Here are the key points from recent conversations between the salesperson and customer within the past 30 days:
```
{{recent_summaries}}
```

## IMMEDIATE CONTEXT (LAST 30 DAYS)
Below are the last 10 messages from the previous conversation within the past 30 days, which provide direct context for today's chat:
```
{{immediate_context}}
```

# TODAY'S CHAT RECORDS
Here are the chat records for today:

{{today_chat_records}}
{{format_analysis_result}}
EOF;
        }

        $llmModel = $this->promptConfig['chat_journey_agent'] ?? AiServiceConstant::SERVICE_QWEN_MAX_LATEST;

        $conversations = $runContext['conversations'] ?? [];
        $range = $runContext['range'];
        $limit = $runContext['limit'];
        $msgNum = $runContext['messageNum'];

        $dateToday = $this->dateTime;
        $weekday = \DateTime::createFromFormat('Y-m-d', $this->dateTime)->format('l');

        foreach ($conversations as $pair => $conversation)
        {
            $ss = explode("-", $pair);
            $userId = $ss[0];
            $customerId = $ss[1];

            $this->recordUserId = $userId;
            $this->recordCustomerId = $customerId;
            $this->recordAgentId = self::QC_JOURNEY_GENERATION_AGENT_ID;

            if ($this->checkNeedSkipV3(self::QC_JOURNEY_GENERATION_AGENT_ID, $userId, $customerId)) {
                \LogUtil::info("AiCompanyQualityCheck_V3_JourneyGenerationAgent: Skip By Idempotent",[
                    "clientId" => $this->clientId,
                    "companyId" => $this->companyId,
                    "date" => $this->dateTime,
                    "pair" => $pair,
                ]);
                continue;
            }

            $recentSummaries = $this->buildRecentSummary($this->clientId, $userId, $customerId, $range, $limit);

            $immediateContext = $this->buildImmediateContext($this->clientId, $userId, $customerId, $range, $msgNum);

            $todayChatRecords = $this->buildTodayChatRecords($conversation);

            //Event Annotation Agent中间结果
            $analysisList = new AiQualityCheckAgentAnalysisList($this->clientId);
            $analysisList->setCompanyId($this->companyId);
            $analysisList->setUserId($userId);
            $analysisList->setCustomerId($customerId);
            $analysisList->setContactDate($this->dateTime);
            $analysisList->setAgentId(self::QC_EVENT_ANNOTATION_AGENT_ID);
            $analysisResult = $analysisList->find();
            $analysisResult = !empty($analysisResult) ? $analysisResult[0]['analyze_result'] : '';
            $analysisResult = json_decode($analysisResult,true);

            $formatConversationSegments = '';
            $formatOccurredEvents = '';
            if(!empty($analysisResult['conversation_segments'])){
                $conversationSegments = json_encode(array_map(
                    fn($segment) => [
                        'topic' => $segment['topic'],
                        'key_information' => $segment['key_information']
                    ],
                    $analysisResult['conversation_segments']
                ), JSON_UNESCAPED_UNICODE);

                $conversationSegmentTemplate = <<<EOF
## COMMUNICATION SEGMENTATION AGENT
- Role: Communication Segmentation Assistant
- Task: Analyze today's chat records by segmenting conversations into distinct business-related topics, and systematically extract essential information from each segment to create a comprehensive structured overview of the communication.
- Result:
```
{{conversation_segments}}
```
EOF;
                $formatConversationSegments = $this->replacePlaceholders($conversationSegmentTemplate,[
                    'conversation_segments' => $conversationSegments
                ]);
            }

            if(!empty($analysisResult['occurred_events'])){
                $occurredEvents = json_encode($analysisResult['occurred_events'], JSON_UNESCAPED_UNICODE);
                $occurredEventsTemplate = <<<EOF
## EVENT ANNOTATION AGENT
- Role: Event Annotation Assistant
- Task: Analyze today's chat records to identify key business events that occurred today.
- Result:
```
{{occurred_events}}
```
EOF;
                $formatOccurredEvents = $this->replacePlaceholders($occurredEventsTemplate,[
                    'occurred_events' => $occurredEvents,
                ]);
            }

            //Sentiment Analysis Agent中间结果
            $formatSentimentAnalysis = '';
            $analysisList->setAgentId(self::QC_SENTIMENT_ANALYSIS_AGENT_ID);
            $analysisResult = $analysisList->find();
            $analysisResult = !empty($analysisResult) ? $analysisResult[0]['analyze_result'] : '';
            $analysisResult = json_decode($analysisResult,true);

            //结果不为"neutral"
            if($analysisResult['sentiment_level'] != self::SENTIMENT_LEVEL_NEUTRAL){
                $sentimentAnalysisTemplate = <<<EOF
## SENTIMENT ANALYSIS AGENT
- Role: Customer Sentiment Analysis Assistant
- Task: Analyze today's chat records to evaluate the customer's emotional state and sentiment, focusing on their final messages and overall communication tone.
- Result:
```
{{sentiment_analysis}}
```
EOF;
                $formatSentimentAnalysis = $this->replacePlaceholders($sentimentAnalysisTemplate, [
                    'sentiment_analysis' => json_encode($analysisResult, JSON_UNESCAPED_UNICODE),
                ]);
            }

            $formatAnalysisResult = '';
            if (!empty(array_filter([$formatConversationSegments, $formatOccurredEvents, $formatSentimentAnalysis]))) {
                $analysisResultTemplate = <<<EOF
# OTHER AGENTS' ANALYSIS RESULTS
Below are the analysis results from other agents regarding today's conversation:

{{format_conversation_segments}}

{{format_occurred_events}}

{{format_sentiment_analysis}}
EOF;
                $formatAnalysisResult = $this->replacePlaceholders($analysisResultTemplate,[
                    'format_conversation_segments' => $formatConversationSegments,
                    'format_occurred_events' => $formatOccurredEvents,
                    'format_sentiment_analysis' => $formatSentimentAnalysis
                ]);
            }


            $question = $this->replacePlaceholders($userPrompt, [
                'date_today' => $dateToday,
                'weekday' => $weekday,
                'recent_summaries' => $recentSummaries,
                'immediate_context' => $immediateContext,
                'today_chat_records' => $todayChatRecords,
                'format_analysis_result' => $formatAnalysisResult,
            ]);

            $gptResponse = $this->callByParams($this->companyId, $question, $systemPrompt, $llmModel, temperature: 0.1, jsonObject: true);
            $answer = $this->formatResponseAnswer($gptResponse['answer'] ?? '');
            $answer = !empty($answer) ? json_decode($answer, true) : [];

            if (!empty($answer) && !empty($answer['summary'])) {
                $this->saveAgentMemories($this->clientId, $this->companyId, $userId, $customerId, self::QC_JOURNEY_GENERATION_AGENT_ID,$answer);
            }

            $this->recordUserId = '';
            $this->recordCustomerId = '';
            $this->recordAgentId = 0;

        }
        return [$runContext, false];
    }

    /**
     * content Content Display Agent
     * @param $runContext
     * @return array
     */
    public function contentDisplay($runContext) : array
    {
        $language = \Yii::app()->language ?? 'zh-CN';
        $languageList = array_column(AiAgentConstants::languageList, 'en','code');
        $language = $languageList[$language] ?? 'Simplified Chinese';

        $translateSingleSystemPrompt = $this->systemPromptMap['contentDisplay.single.translate'] ?? '';
        if(empty($translateSingleSystemPrompt)) {
            $translateSingleSystemPrompt = <<<EOF
You are an expert in international B2B trade with extensive knowledge and experience in cross-border commerce. You excel at understanding global business communications and trade practices across different markets and cultures. Your task is to translate the following summary into {{language}}:
EOF;
        }
        $translateSingleSystemPrompt = $this->replacePlaceholders($translateSingleSystemPrompt,[
            'language' => $language
        ]);

        $singleTranslateUserPrompt = $this->userPromptMap['contentDisplay.single.translate'] ?? '';
        if(empty($singleTranslateUserPrompt)) {
            $singleTranslateUserPrompt = <<<EOF
{{summary_to_be_translated}}
EOF;
        }

        //高亮system_prompt
        $highlightSystemPrompt = $this->systemPromptMap['contentDisplay.highlight'] ?? '';
        if(empty($highlightSystemPrompt)) {
            $highlightSystemPrompt = <<<EOF
# ROLE
You are an expert in international B2B trade with extensive knowledge and experience in cross-border commerce, and you are proficient in multiple languages.

# TASK
Your task is to analyze communication summaries and highlight critical business developments by adding ** markers around key points that require management attention. You will process summaries in any language to identify and emphasize significant business progress, enabling international trade managers to quickly grasp important developments. Focus on highlighting concrete business progress points that demonstrate tangible advancement in negotiations, orders, payments, deliveries or customer relationships. The goal is to make critical information immediately visible while preserving the original text's integrity and meaning.

# REQUIREMENTS
- Highlight key points that require management attention, including but not limited to:
   * Product names and requirements
   * Order details (quantities, specifications, pricing)
   * Payment terms and status
   * Delivery schedules and shipping arrangements
   * Customer feedback and concerns
   * Important commitments and deadlines
   * Quality issues or improvement requests
   * Significant changes in business terms
- Return the original text unchanged if there are no key progress points to highlight (refer to example 2), otherwise only add ** markers around key progress points
- Use highlight markers correctly:
   * Always pair opening and closing ** markers
   * Format highlights consistently as **highlighted text**
- Process summaries in any language with the same highlighting criteria and standards

# OUTPUT FORMAT
Return the original text with ** markers added around key business progress points only, with no other modifications.

# EXAMPLES (English examples for illustration)

# Example 1
Input: Salesperson and customer discussed shipping options for the order of 500 units. Customer agreed to proceed with DHL Express shipping with 7-10 days delivery time.
Output: Salesperson and customer discussed **shipping options for the order of 500 units**. Customer agreed to proceed with **DHL Express shipping with 7-10 days delivery time**.

# Example 2
Input: Salesperson greeted customer and asked about their recent status. No response received from customer.
Output: Salesperson greeted customer and asked about their recent status. No response received from customer.
EOF;
        }

        //高亮user_prompt
        $highlightUserPrompt = <<<EOF
{{input}}
EOF;

        $llmModel = $this->promptConfig['contentDisplay.translate'] ?? AiServiceConstant::SERVICE_QWEN_MAX_LATEST;

        //生成content_display_agent
        $conversations = $runContext['conversations'] ?? [];
        foreach ($conversations as $pair => $conversation)
        {
            $ss = explode("-", $pair);
            $userId = $ss[0];
            $customerId = $ss[1];

            $this->recordUserId = $userId;
            $this->recordCustomerId = $customerId;
            $this->recordAgentId = self::QC_CONTENT_DISPLAY_AGENT_ID;

            if ($this->checkNeedSkipV3(self::QC_CONTENT_DISPLAY_AGENT_ID, $userId, $customerId)) {
                \LogUtil::info("AiCompanyQualityCheck_V3_ContentDisplayAgent: Skip By Idempotent",[
                    "clientId" => $this->clientId,
                    "companyId" => $this->companyId,
                    "date" => $this->dateTime,
                    "pair" => $pair,
                ]);
                continue;
            }

            //关键词映射
            $agentResultList = new AiQualityCheckAgentAnalysisList($this->clientId);
            $agentResultList->setCompanyId($this->companyId);
            $agentResultList->setUserId($userId);
            $agentResultList->setCustomerId($customerId);
            $agentResultList->setContactDate($this->dateTime);
            $agentResultList->setAgentId(self::QC_EVENT_ANNOTATION_AGENT_ID);
            $ret = $agentResultList->find();
            $analysisResult = !empty($ret) ? $ret[0] : [];
            if(empty($analysisResult)) continue;
            $agentResult = json_decode($analysisResult['analyze_result'],true);

            $keywords = [];
            $occurredEvents = $agentResult['occurred_events'] ?? [];
            foreach ($occurredEvents as $event)
            {
                $eventName = $event['event_name'];
                $eventType = self::OCCURRED_EVENT_NAME_TO_ENUM_MAP[$eventName] ?? 0;
                if(!empty($eventType)){
                    $keywords[] = $eventType;
                }
            }

            //翻译+高亮
            $translation = "";
            $highLightText = "";
            if ($this->mode == self::QUALITY_CHECK_MODE_SINGLE) {
                $this->translateDate = $this->dateTime;

                //待翻译的内容
                $agentResultList->setAgentId(self::QC_JOURNEY_GENERATION_AGENT_ID);
                $ret = $agentResultList->find();
                $analysisResult = !empty($ret) ? $ret[0] : [];
                $agentResult = json_decode($analysisResult['analyze_result'] ?? "", true);
                $summaryToBeTranslated = $agentResult['summary'] ?? "";

                $question = $this->replacePlaceholders($singleTranslateUserPrompt, [
                    'summary_to_be_translated' => $summaryToBeTranslated,
                ]);

                $this->maxHistoryLength = 3;
                $gptResponse = $this->callByParams($this->companyId, $question, $translateSingleSystemPrompt, $llmModel, temperature: 0.3, jsonObject: false);
                $answer = $gptResponse['answer'] ?? '';
                $translation = $answer;

                if(!empty($translation)) {
                    //处理繁体
                    if(\Yii::app()->language == 'zh-TW') {
                        $translation = $this->translateToTw($translation);
                    }
                    $question = $this->replacePlaceholders($highlightUserPrompt, ['input' => $translation]);

                    $this->maxHistoryLength = 0;
                    $gptResponse = $this->callByParams($this->companyId, $question, $highlightSystemPrompt, $llmModel, temperature: 0.1, jsonObject: false);
                    $answer = $gptResponse['answer'] ?? '';
                    $text = $answer;

                    $highLightText = !empty($text) ? $this->checkHighlightFormat($text) : "";
                }

                $this->translateDate = "";
            }

            $this->saveAgentMemories($this->clientId, $this->companyId, $userId, $customerId , self::QC_CONTENT_DISPLAY_AGENT_ID , [
                'keywords' => $keywords,
                'translation' => $translation,
                'hightlight_text' => $highLightText,
            ]);

            $this->recordUserId = '';
            $this->recordCustomerId = '';
            $this->recordAgentId = 0;
        }

        return [$runContext, false];
    }

//    private function buildRecentTranslations($userId, $customerId, $range, $limit) : string
//    {
//        $analysisList = new AiQualityCheckAgentAnalysisList($this->clientId);
//        $analysisList->setCompanyId($this->companyId);
//        $analysisList->setUserId($userId);
//        $analysisList->setCustomerId($customerId);
//        $analysisList->setStartContactDate(date('Y-m-d',strtotime("-{$range} day",strtotime($this->dateTime))));
//        $analysisList->setEndContactDate(date('Y-m-d',strtotime('-1 day',strtotime($this->dateTime))));
//        $analysisList->setAgentId(self::QC_JOURNEY_GENERATION_AGENT_ID);
//        $analysisList->setFields(['analyze_result','contact_date']);
//        $analysisList->setOrderBy('contact_date');
//        $analysisList->setOrder('desc');
//        $analysisList->setLimit($limit);
//        $summaryList = $analysisList->find();
//        $dateToSummaryList = array_column($summaryList, 'analyze_result', 'contact_date');
//        $dateToSummaryList = array_map(function ($item) {
//            $summary = json_decode($item, true);
//            return $summary['summary'] ?? "";
//        }, $dateToSummaryList);
//
//        $analysisList->setAgentId(self::QC_CONTENT_DISPLAY_AGENT_ID);
//        $translationList = $analysisList->find();
//        $dateToTranslationList = array_column($translationList, 'analyze_result', 'contact_date');
//        $dateToTranslationList = array_map(function ($item) {
//            $translation = json_decode($item, true);
//            return $translation['translation'] ?? "";
//        },$dateToTranslationList);
//
//        $res = [];
//        foreach ($dateToSummaryList as $date => $summary)
//        {
//            $res[] = [
//                "date" => $date,
//                "summary" => $summary,
//                "translation" => $dateToTranslationList[$date] ?? "",
//            ];
//        }
//
//        if(empty($res)) {
//            return "No communication summaries available within the past {days} days";
//        }
//
//        $res = array_reverse($res);
//
//        return json_encode($res, JSON_UNESCAPED_UNICODE);
//    }

    public function checkBatchDates(array $batchDates, array $batchTranslations): bool {
        // 遍历日期和翻译结果，检查是否一致
        foreach ($batchDates as $index => $date) {
            if (!isset($batchTranslations[$index]['date']) || $batchTranslations[$index]['date'] !== $date) {
                // 如果发现不一致，输出错误信息并返回 false
                $translationDates = array_map(function ($translation) {
                    return $translation['date'] ?? 'N/A';
                }, $batchTranslations);

                \LogUtil::error("AiCompanyQualityCheck_ContentDisplay:翻译结果的日期与摘要日期不一致",[
                    "clientId" => $this->clientId,
                    "companyId" => $this->companyId,
                    "date" => $this->dateTime,
                    "原始摘要日期" => implode(",", $batchDates),
                    "翻译结果日期" => implode(", ", $translationDates),
                ]);
                return false;
            }
        }

        // 如果所有日期都一致，返回 true
        return true;
    }

    /**
     * 批量修改Content Display Agent中的translate
     * @return void
     */
    private function batchUpdateTranslate(string $userId, string $customerId, array $translations) : void
    {
        //找出translations中每个date对应的记录
        $list = new AiQualityCheckAgentAnalysisList($this->clientId);
        $list->setCompanyId($this->companyId);
        $list->setUserId($userId);
        $list->setCustomerId($customerId);
        $list->setAgentId(self::QC_CONTENT_DISPLAY_AGENT_ID);
        $list->setEndContactDate($this->lastDate);
        $list->setOrderBy('contact_date');
        $list->setOrder('asc');
        $analysisList = $list->find();

        $analysisIdToResultMap = [];
        foreach ($analysisList as $analysis)
        {
            $analysisId = $analysis['analysis_id'] ?? 0;
            $contactDate = $analysis['contact_date'] ?? "";

            $oldAnalyzeResult = json_decode($analysis['analyze_result'] ?? '', true);
            $translationItem = $translations[$contactDate] ?? [];
            $oldAnalyzeResult['translation'] = $translationItem['translation'] ?? "";
            $oldAnalyzeResult['hightlight_text'] = $translationItem['highlightedText'] ?? "";

            $analysisIdToResultMap[$analysisId] = json_encode($oldAnalyzeResult,JSON_UNESCAPED_UNICODE);
        }

        //批量更新
        $case = "";
        foreach ($analysisIdToResultMap as $id => $analyzeResult)
        {
            $analyzeResult = pg_escape_string($analyzeResult);
            $case .= " WHEN analysis_id = {$id} THEN '{$analyzeResult}'::jsonb ";
        }

        $ids = array_keys($analysisIdToResultMap);
        $ids = implode(",", $ids);

        if(!empty($case) && !empty($ids)){
            $sql = <<<EOF
update tbl_ai_quality_check_agent_analysis
set analyze_result = case
{$case}
else analyze_result
end
where client_id = {$this->clientId}
and company_id = {$this->companyId}
and analysis_id in ({$ids})
EOF;
            \AiQualityCheckAgentAnalysisModel::model()->getDbConnection()->createCommand($sql)->execute();
        }
    }


    public function processDateTaskV3($params)
    {
        if (empty($params) || empty($params['company_id']) || empty($params['start_time']) || empty($params['end_time'] || empty($params['mode']))) {
            \LogUtil::error("AiCompanyQualityCheck_V3_ERROR: Invalid Params",[
                "clientId" => $this->clientId,
                "params" => json_encode($params, JSON_UNESCAPED_UNICODE),
            ]);
            throw new AiAgentException("谈单检测执行失败,参数异常");
        }

        $this->dateTime = date('Y-m-d', strtotime($params['start_time']));
        $this->companyId = $params['company_id'];
        $this->startTime = $params['start_time'];
        $this->endTime = $params['end_time'];
        $this->mode = $params['mode'];
        if($this->mode == self::QUALITY_CHECK_MODE_BATCH)
        {
            $this->lastDate = $params['last_date'];
        }
        //如果从后台监测池转为手动添加,需要补上agent_id=6
        if(isset($params['fromAuto'])) {
            $this->fromAuto = $params['fromAuto'] == 1;
        }

        try{
            $modules = self::FUNCTION_TO_MODULES_MAP[self::QUALITY_CHECK_FUNCTION_V3_PROCESS_DATE_TASK];

            $runContext = [];
            foreach ($modules as $module)
            {
                \LogUtil::info("AiCompanyQualityCheck_V3_Module_Start",[
                    "clientId" => $this->clientId,
                    "companyId" => $this->companyId,
                    "date" => $this->dateTime,
                    "module" => $module,
                ]);

                [$runContext, $needBreak] = call_user_func([$this, $module], $runContext);

                if($needBreak) {
                    \LogUtil::info("AiCompanyQualityCheck_V3_Module_Break",[
                        "clientId" => $this->clientId,
                        "companyId" => $this->companyId,
                        "date" => $this->dateTime,
                        "breakReason" => $runContext['error'] ?? "",
                    ]);
                    break;
                }
            }

            $this->handleBatchMode();

            $this->updateCompanyJourneyV3();

            $this->updateCompanyV3(); //更新谈单监测池状态

        }catch (\Throwable $exception){
            \LogUtil::error("AiCompanyQualityCheck_V3_Error: {$exception->getMessage()}",[
                "clientId" => $this->clientId,
                "companyId" => $this->companyId,
                "date" => $this->dateTime,
                "error" => $exception->getTraceAsString(),
            ]);

            print_r($exception->getTraceAsString());

            //todo 为了避免中间态
            throw new AiAgentException($exception);
        }
    }

    /**
     * 幂等性校验
     * @return bool
     */
    private function checkNeedSkipV3($agentId = 0, $userId = 0, $customerId = 0) : bool
    {
        //tbl_ai_quality_check_company_journey中不存在client_id+company_id+date
        if(empty($agentId)) {
            $journeyList = new AiQualityCheckCompanyJourneyList($this->clientId, $this->companyId);
            $journeyList->setCompanyId($this->companyId);
            $journeyList->setContactDate($this->dateTime);
            $journeyCnt = $journeyList->count();

            return $journeyCnt != 0 && !$this->fromAuto;
        }

        //判断client+company+user+customer+date在该agent中是否已经生成结果
        $analysisList = new AiQualityCheckAgentAnalysisList($this->clientId);
        $analysisList->setCompanyId($this->companyId);
        $analysisList->setContactDate($this->dateTime);
        $analysisList->setUserId($userId);
        $analysisList->setCustomerId($customerId);
        $analysisList->setAgentId($agentId);
        $analysisCnt = $analysisList->count();
        return $analysisCnt != 0;
    }

    private function checkHighlightFormat(string $highlightedText) : string
    {
        //todo 检查markdown格式是否正常返回
        if (substr_count($highlightedText,"**") % 2 != 0 ) {
            throw new AiAgentException("Markdown粗体标签(**)格式错误");
        }

        $pos = 0;
        $result = "";
        $isBold = false;
        $length = strlen($highlightedText);
        while ($pos < $length) {
            if (substr($highlightedText, $pos, 2) == "**") {
                $result .= $isBold ? "</strong>" : "<strong>";
                $isBold = !$isBold;
                $pos += 2;
            } else {
                $result .= $highlightedText[$pos];
                $pos += 1;
            }
        }

        return $result;
    }

    private function updateCompanyJourneyV3() : void
    {
        $analysisList = new AiQualityCheckAgentAnalysisList($this->clientId);
        $analysisList->setCompanyId($this->companyId);
        $analysisList->setAgentId(self::QC_CONTENT_DISPLAY_AGENT_ID);
        $analysisList->setFields(['user_id','customer_id','contact_date','analyze_result']);
        $analysisList->setOrderBy('contact_date');
        $analysisList->setOrder('asc');

        $agentAnalysis = [];
        if ($this->mode == self::QUALITY_CHECK_MODE_BATCH && !empty($this->lastDate) && $this->lastDate == $this->dateTime) {
            //批量模式下,将期间所有的agent中间结果都生成company_journey
            $analysisList->setEndContactDate($this->lastDate);
            $agentAnalysis = $analysisList->find();
        }else if ($this->mode == self::QUALITY_CHECK_MODE_SINGLE) {
            //增量模式下,新增当日的数据
            $analysisList->setContactDate($this->dateTime);
            $agentAnalysis = $analysisList->find();
        }

        foreach ($agentAnalysis as $analysis)
        {
            $userId = $analysis['user_id'] ?? 0;
            $customerId = $analysis['customer_id'] ?? 0;
            $contactDate = $analysis['contact_date'] ?? "";
            $analyzeResult = json_decode($analysis['analyze_result'] ?? "", true);

            $keywords = PgsqlUtil::formatArray($analyzeResult['keywords'] ?? []);
            $translation = $analyzeResult['translation'] ?? "";
            $highlightText = $analyzeResult['hightlight_text'] ?? "";

            $journey = [
                'client_id' => $this->clientId,
                'company_id' => $this->companyId,
                'user_id' => $userId,
                'customer_id' => $customerId,
                'contact_date' => $contactDate,
                'keywords' => $keywords,
                'summary' => $translation,
                'highlight_text' => $highlightText,
            ];

            Helper::insertOrUpdateCompanyJourney($journey);
        }
    }


    private function updateCompanyV3() : void
    {
        //更新谈单监测池数据
        $companyList = new AiQualityCheckCompanyList($this->clientId);
        $companyList->setType([AiQualityCheckCompanyModel::TYPE_MANUAL,AiQualityCheckCompanyModel::TYPE_AUTO]);
        $companyList->setCompanyId($this->companyId);
        $companyList->setType([AiQualityCheckCompanyModel::TYPE_MANUAL, AiQualityCheckCompanyModel::TYPE_AUTO]);
        $companyList->setSkipVisibleCheck(true);
        $companies = $companyList->find();
        if(!empty($companies)) {
            //最后一条聊天
            $conversationList = new AiQualityCheckCompanyConversationList($this->clientId);
            $conversationList->setCompanyId($this->companyId);
            $conversationList->setContactDate($this->dateTime);
            $conversationList->setOrderBy('day_last_message_time');
            $conversationList->setOrder('desc');
            $conversationList->setLimit(1);
            $conversation = $conversationList->find();

            if(!empty($conversation)) {
                $recentContactTime = $conversation[0]['day_last_message_time'] ?? "";
                $recentSnsType = $conversation[0]['sns_type'] ?? "";
                $recentUserId = $conversation[0]['user_id'] ?? 0;
                $recentCustomerId = $conversation[0]['customer_id'] ?? 0;

                $journeyList = new AiQualityCheckCompanyJourneyList($this->clientId, $this->companyId);
                $journeyList->setCompanyId($this->companyId);
                $journeyList->setUserId($recentUserId);
                $journeyList->setCustomerId($recentCustomerId);
                $journeyList->setContactDate($this->dateTime);
                $journeyObjs = $journeyList->find();
                $journeyObjs = !empty($journeyObjs) ? $journeyObjs[0] : [];
                $recentContactKeywords = json_decode($journeyObjs['keywords'] ?? "",true);

                $company = $companies[0];
                $companyId = $company['company_id'] ?? 0;

                $companyObj = new AiQualityCheckCompany($companyId);
                $companyObj->report_update_time = $recentContactTime;
                $companyObj->recent_contact_keywords = PgsqlUtil::formatArray($recentContactKeywords);
                $companyObj->recent_contact_time = $recentContactTime;
                $companyObj->recent_sns_type = $recentSnsType;
                $companyObj->update_time = date('Y-m-d H:i:s', time());
                $companyObj->save();
            }

        }
    }

    private function handleBatchMode() : void
    {
        $language = \Yii::app()->language ?? 'zh-CN';
        $languageList = array_column(AiAgentConstants::languageList, 'en','code');
        $language = $languageList[$language] ?? 'Simplified Chinese';

        //翻译user_prompt
        $batchTranslateUserPrompt = $this->userPromptMap['contentDisplay.batch.translate'] ?? '';
        if(empty($batchTranslateUserPrompt)) {
            $batchTranslateUserPrompt = <<<EOF
{{summary}}
EOF;
        }

        //翻译system_prompt
        $batchTranslateSystemPrompt = $this->systemPromptMap['contentDisplay.batch.translate'] ?? "";
        if(empty($batchTranslateSystemPrompt)) {
            $batchTranslateSystemPrompt = <<<EOF
You are an expert in international B2B trade with extensive knowledge and experience in cross-border commerce. You excel at understanding global business communications and trade practices across different markets and cultures. Your task is to translate the following summary into {{language}}:
EOF;
        }
        $batchTranslateSystemPrompt = $this->replacePlaceholders($batchTranslateSystemPrompt,[
            'language' => $language,
        ]);

        //高亮system_prompt
        $highlightSystemPrompt = $this->systemPromptMap['contentDisplay.highlight'] ?? '';
        if(empty($highlightSystemPrompt)) {
            $highlightSystemPrompt = <<<EOF
# ROLE
You are an expert in international B2B trade with extensive knowledge and experience in cross-border commerce, and you are proficient in multiple languages.

# TASK
Your task is to analyze communication summaries and highlight critical business developments by adding ** markers around key points that require management attention. You will process summaries in any language to identify and emphasize significant business progress, enabling international trade managers to quickly grasp important developments. Focus on highlighting concrete business progress points that demonstrate tangible advancement in negotiations, orders, payments, deliveries or customer relationships. The goal is to make critical information immediately visible while preserving the original text's integrity and meaning.

# REQUIREMENTS
- Highlight key points that require management attention, including but not limited to:
   * Product names and requirements
   * Order details (quantities, specifications, pricing)
   * Payment terms and status
   * Delivery schedules and shipping arrangements
   * Customer feedback and concerns
   * Important commitments and deadlines
   * Quality issues or improvement requests
   * Significant changes in business terms
- Return the original text unchanged if there are no key progress points to highlight (refer to example 2), otherwise only add ** markers around key progress points
- Use highlight markers correctly:
   * Always pair opening and closing ** markers
   * Format highlights consistently as **highlighted text**
- Process summaries in any language with the same highlighting criteria and standards

# OUTPUT FORMAT
Return the original text with ** markers added around key business progress points only, with no other modifications.

# EXAMPLES (English examples for illustration)

# Example 1
Input: Salesperson and customer discussed shipping options for the order of 500 units. Customer agreed to proceed with DHL Express shipping with 7-10 days delivery time.
Output: Salesperson and customer discussed **shipping options for the order of 500 units**. Customer agreed to proceed with **DHL Express shipping with 7-10 days delivery time**.

# Example 2
Input: Salesperson greeted customer and asked about their recent status. No response received from customer.
Output: Salesperson greeted customer and asked about their recent status. No response received from customer.
EOF;
        }

        //高亮user_prompt
        $highlightUserPrompt = <<<EOF
{{input}}
EOF;

        $llmModel = AiServiceConstant::SERVICE_QWEN_MAX_LATEST;

        //批量翻译
        if($this->mode == self::QUALITY_CHECK_MODE_BATCH && !empty($this->lastDate) && $this->lastDate == $this->dateTime){
            //获取所有tbl_ai_quality_check_agent_analysis中的summary
            $agentAnalysisList = new AiQualityCheckAgentAnalysisList($this->clientId);
            $agentAnalysisList->setCompanyId($this->companyId);
            $agentAnalysisList->setAgentId(self::QC_JOURNEY_GENERATION_AGENT_ID);
            $agentAnalysisList->setEndContactDate($this->lastDate);
            $agentAnalysisList->setOrderBy('contact_date');
            $agentAnalysisList->setOrder('asc');
            $agentAnalysis = $agentAnalysisList->find();

            //fixme 目前批量也采用逐日翻译(deepseek-chat部署后修改回来)
            foreach ($agentAnalysis as $item)
            {
                $userId = $item['user_id'] ?? 0;
                $customerId = $item['customer_id'] ?? 0;
                $contactDate = $item['contact_date'] ?? "";

                $analysisResult = $item['analyze_result'] ?? '';
                $analysisResult = json_decode($analysisResult, true);
                $summary = $analysisResult['summary'] ?? "";

                $this->recordAgentId = self::QC_CONTENT_DISPLAY_AGENT_ID;
                $this->recordUserId = $userId;
                $this->recordCustomerId = $customerId;
                $this->translateDate = $contactDate;

                //翻译
                $this->maxHistoryLength = 3;
                $question = $this->replacePlaceholders($batchTranslateUserPrompt, [
                    'summary' => $summary,
                ]);

                $gptResponse = $this->callByParams($this->companyId, $question, $batchTranslateSystemPrompt, $llmModel, 0.3,stream:false,jsonObject: false);
                $translation = $gptResponse['answer'] ?? "";
                if(\Yii::app()->language == 'zh-TW') {
                    $translation = $this->translateToTw($translation);
                }

                //高亮
                $this->maxHistoryLength = 0;//跳过assistant
                $question = $this->replacePlaceholders($highlightUserPrompt,[
                    'input' => $translation,
                ]);
                $gptResponse = $this->callByParams($this->companyId,$question,$highlightSystemPrompt, $llmModel, 0.1,stream:false,jsonObject: false);
                $text = $gptResponse['answer'] ?? "";
                $highLightText = !empty($text) ? $this->checkHighlightFormat($text) : "";

                //fixme 更新当前agent,无法批量更新(否则loadHistoryMessage找不到对应数据)
                AiQualityCheckAgentAnalysis::updateTranslationByIdsAndDate($this->clientId,$this->companyId,$userId,$customerId,$contactDate,[
                    'translation' => $translation,
                    'highLightText' => $highLightText
                ]);

                $this->recordUserId = '';
                $this->recordCustomerId = '';
                $this->recordAgentId = 0;
                $this->translateDate = "";
            }
        }
    }

    public function processBackstageTask($params)
    {
        if (empty($params) || empty($params['company_id']) || empty($params['start_time']) || empty($params['end_time'] || empty($params['mode']))) {
            \LogUtil::error("AiCompanyQualityCheck_V3_ERROR: Invalid Params",[
                "clientId" => $this->clientId,
                "params" => json_encode($params, JSON_UNESCAPED_UNICODE),
            ]);
            throw new AiAgentException("谈单检测执行失败,参数异常");
        }

        $this->dateTime = date('Y-m-d', strtotime($params['start_time']));
        $this->companyId = $params['company_id'];
        $this->startTime = $params['start_time'];
        $this->endTime = $params['end_time'];
        $this->mode = $params['mode'];
        if($this->mode == self::QUALITY_CHECK_MODE_BATCH)
        {
            $this->lastDate = $params['last_date'];
        }

        try{
            $modules = self::FUNCTION_TO_MODULES_MAP[self::QUALITY_CHECK_FUNCTION_BACKSTAGE_PROCESS_DATE_TASK];

            $runContext = [];
            foreach ($modules as $module)
            {
                \LogUtil::info("AiCompanyQualityCheck_V3_Module_Start",[
                    "clientId" => $this->clientId,
                    "companyId" => $this->companyId,
                    "date" => $this->dateTime,
                    "module" => $module,
                ]);

                [$runContext, $needBreak] = call_user_func([$this, $module], $runContext);

                if($needBreak) {
                    \LogUtil::info("AiCompanyQualityCheck_V3_Module_Break",[
                        "clientId" => $this->clientId,
                        "companyId" => $this->companyId,
                        "date" => $this->dateTime,
                        "breakReason" => $runContext['error'] ?? "",
                    ]);
                    break;
                }
            }
        }catch (\Throwable $exception){
            throw new AiAgentException($exception);
        }
    }

    public function createBackStageJourney()
    {
        //生成后台监测池的聊天旅程
        $analysisList = new AiQualityCheckAgentAnalysisList($this->clientId);
        $analysisList->setCompanyId($this->companyId);
        $analysisList->setAgentId(self::QC_JOURNEY_GENERATION_AGENT_ID);
        $analysisList->setFields(['user_id','customer_id','contact_date','analyze_result']);
        $analysisList->setContactDate($this->dateTime);
        $agentAnalysis = $analysisList->find();

        foreach ($agentAnalysis as $analysis)
        {
            $userId = $analysis['user_id'] ?? 0;
            $customerId = $analysis['customer_id'] ?? 0;
            $contactDate = $analysis['contact_date'] ?? "";
            $analyzeResult = json_decode($analysis['analyze_result'] ?? "", true);

            $keywords = PgsqlUtil::formatArray($analyzeResult['keywords'] ?? []);
            $summary = $analyzeResult['summary'] ?? "";

            $journey = [
                'client_id' => $this->clientId,
                'company_id' => $this->companyId,
                'user_id' => $userId,
                'customer_id' => $customerId,
                'contact_date' => $contactDate,
                'keywords' => $keywords,
                'summary' => $summary,
                'highlight_text' => $summary,
            ];
            Helper::insertOrUpdateCompanyJourney($journey);
        }

    }

    /**
     * 谈单检测v3,兼容买家问题
     * fixme 后续需要修改流程,该方法仅临时兼容
     * @return void
     */
    public function processV3StickingPoint($clientId, $companyId, string $dateTime) : void
    {
        //使用谈单监测v3的summary获取v2的打标结果
        $this->clientId = $clientId;
        $this->companyId = $companyId;
        $dateTime = date('Y-m-d',strtotime($dateTime));
        $this->dateTime = $dateTime;

        //获取client+company的所有聊天,按照user_id+customer_id分组
        $conversationList = new AiQualityCheckCompanyConversationList($clientId);
        $conversationList->setCompanyId($companyId);
        $conversationList->setContactDate($dateTime);
        $conversationList->setSplitByUserAndCustomer(true);
        $conversations = $conversationList->find();

        if(empty($conversations)) return;

        //打标
        $this->buildV2KeywordsByV3($conversations);

        //生成/更新/关闭 买家问题
        $this->buildV2StickingByV3($conversations);
    }

    //fixme 过度方案,等算法出v3流程的买家问题流程
    public function buildV2KeywordsByV3($conversations) : void
    {
        foreach ($conversations as $pair => $conversation)
        {
            $ss = explode("-", $pair);
            $userId = $ss[0];
            $customerId = $ss[1];

            //获取相同user_id+customer_id最近1个自然月内的最近3个沟通日的记录
            $lastConversationList = new AiQualityCheckCompanyConversationList($this->clientId);
            $lastConversationList->setCompanyId($this->companyId);
            $lastConversationList->setUserId($userId);
            $lastConversationList->setCustomerId($customerId);
            $lastConversationList->setStartContactDate(date('Y-m-d', strtotime('-1 month', strtotime($this->dateTime))));
            $lastConversationList->setEndContactDate(date('Y-m-d',strtotime('-1 day',strtotime($this->dateTime))));
            $lastConversationList->setOrderBy('contact_date');
            $lastConversationList->setOrder('desc');
            $lastConversations = $lastConversationList->find();

            $preContactDate = '';
            $historyConversation = [];
            $dateCount = 0;
            foreach ($lastConversations as $item)
            {
                $currentDate = $item['contact_date'];
                if($currentDate != $preContactDate) {
                    if($dateCount >= 3) {
                        break;
                    }
                    $dateCount++;
                    $preContactDate = $currentDate;
                }
                $historyConversation[] = $item;
            }

            //使用大模型进行打标
            $insertKeys = [];
            foreach ($conversation as $chatRecord)
            {
                $channel = $chatRecord['sns_type'] ?? "";
                $dateToday = $this->dateTime;
                $context = $chatRecord['content'] ?? "";

                //1.没有历史记录
                //2.有历史记录,需要关联对应的summary
                $recentSummary = "No summary of the recent communication.";
                if(!empty($historyConversation))
                {
                    //历史的channel,date,summary,key_events
                    $historyPrompt = $this->userPromptMap['qc.v2.keywords.history.user.prompt'] ?? '';
                    $historyQuestion = "";
                    $historyConversation = array_values($historyConversation);
                    foreach ($historyConversation as $seq => $historyInfo)
                    {
                        $historyDate = $historyInfo['contact_date'];
                        //查询historyDate对应的summary,events
                        $agentAnalysisList = new AiQualityCheckAgentAnalysisList($this->clientId);
                        $agentAnalysisList->setCompanyId($this->companyId);
                        $agentAnalysisList->setUserId($userId);
                        $agentAnalysisList->setCustomerId($customerId);
                        $agentAnalysisList->setContactDate($historyDate);
                        $agentAnalysisList->setAgentId(self::QC_JOURNEY_GENERATION_AGENT_ID);
                        $agentAnalysis = $agentAnalysisList->find();
                        if(empty($agentAnalysis)) continue;
                        $agentAnalysis = array_values($agentAnalysis)[0];
                        $agentResult = json_decode($agentAnalysis['analyze_result'] ?? "", true);
                        $historySummary = $agentResult['summary'] ?? "";

                        $agentAnalysisList->setAgentId(self::QC_V2_EVENTS_AGENT_ID);
                        $agentAnalysis = $agentAnalysisList->find();
                        if(empty($agentAnalysis)) continue;
                        $agentAnalysis = array_values($agentAnalysis)[0];
                        $agentResult = json_decode($agentAnalysis['analyze_result'] ?? "", true);
                        $historyEvents = $agentResult['events'] ?? [];
                        $historyEvents = Helper::convertKeywordsToString($historyEvents);

                        //构建prompt
                        $historyQuestion .= $this->replacePlaceholders($historyPrompt,[
                                'seq' => $seq + 1,
                                'date_today' => $historyDate,
                                'channel' => $historyInfo['sns_type'],
                                'summary' => $historySummary,
                                'events' => empty($historyEvents) ? "None" : $historyEvents
                            ]) . PHP_EOL;
                    }
                    $historyQuestion = trim($historyQuestion);
                    if(!empty($historyQuestion)) $recentSummary = $historyQuestion;
                }

                $userPrompt = $this->userPromptMap['qc.v2.keywords.user.prompt'] ?? "";
                $question = $this->replacePlaceholders($userPrompt,[
                    'date_today' => $dateToday,
                    'channel'  => $channel,
                    'chat_record' => $context,
                    'recent_summary' => $recentSummary,
                ]);
                $systemPrompt = $this->systemPromptMap['qc.v2.keywords.system.prompt'] ?? '';
                $gptResponse = $this->callByParams($this->companyId, $question, $systemPrompt, $this->promptConfig['qc.v2.keywords.service'] ?? '');
                $gptResponse = $this->formatResponseAnswer($gptResponse['answer'] ?? '');
                $answer = json_decode($gptResponse,true);
                if(!empty($answer) && isset($answer['key events']))
                {
                    $events = $answer['key events'];
                    $keywords = [];
                    foreach ($events as $event)
                    {
                        if(isset(\AiQualityCheckChatJourneyModel::KEYWORD_ENUM_MAP[strtolower($event)])){
                            $keywords[] = \AiQualityCheckChatJourneyModel::KEYWORD_ENUM_MAP[strtolower($event)];
                        }
                    }
                    $insertKeys = array_merge($insertKeys, $keywords);
                }
            }

            //存储v2打标
            $insertKeys = array_unique($insertKeys);
            $agentAnalysis = new AiQualityCheckAgentAnalysis();
            $agentAnalysis->client_id = $this->clientId;
            $agentAnalysis->company_id = $this->companyId;
            $agentAnalysis->user_id = $userId;
            $agentAnalysis->customer_id = $customerId;
            $agentAnalysis->contact_date = $this->dateTime;
            $analyzeResult['events'] = $insertKeys;
            $agentAnalysis->analyze_result = json_encode($analyzeResult, JSON_UNESCAPED_UNICODE);
            $agentAnalysis->agent_id = self::QC_V2_EVENTS_AGENT_ID;
            $agentAnalysis->save();
        }
    }

    //fixme 过度方案,等算法出新流程
    public function buildV2StickingByV3($conversations) : void
    {
        //生成当日买家问题
        $this->generateStickingPointV3($conversations);

        //更新买家问题
        $this->updateStickingPointV3($conversations);

        //关闭买家问题
        $this->closeStickingPoint();
    }

    private function generateStickingPointV3($conversations) : void
    {
        $systemPrompt = $this->systemPromptMap['qc.v2.new.sticking.point.system.prompt'] ?? '';
        $userPrompt = $this->userPromptMap['qc.v2.new.sticking.point.user.prompt'] ?? '';
        $chatRecordUserPrompt = $this->userPromptMap['qc.v2.new.sticking.point.chat.record.user.prompt'] ?? '';

        //查看agent_id=-1且events包含12的user+customer
        $list = new AiQualityCheckAgentAnalysisList($this->clientId);
        $list->setCompanyId($this->companyId);
        $list->setContactDate($this->dateTime);
        $list->setAgentId(self::QC_V2_EVENTS_AGENT_ID);
        $ret = $list->find();

        //生成今日买家问题
        foreach ($ret as $item)
        {
            //判断当日是否有新卡点
            $analyzeResult = json_decode($item['analyze_result'] ?? "", true);
            $events = $analyzeResult['events'] ?? [];
            if(!in_array(\AiQualityCheckChatJourneyModel::CONTAIN_STICKING_POINT_KEYWORD,$events)) continue;

            $userId = $item['user_id'];
            $customerId = $item['customer_id'];
            //查看该user+customer当日的所有聊天
            $key = "{$userId}-{$customerId}";
            if(empty($conversations[$key] ?? [])) continue;

            $chatRecordAllDay = "";
            $conversationList = array_values($conversations[$key]);
            foreach ($conversationList as $seq => $conversationItem)
            {
                //查询summary和translation
                $agentAnalysisList = new AiQualityCheckAgentAnalysisList($this->clientId);
                $agentAnalysisList->setCompanyId($this->companyId);
                $agentAnalysisList->setUserId($userId);
                $agentAnalysisList->setCustomerId($customerId);
                $agentAnalysisList->setContactDate($this->dateTime);
                $agentAnalysisList->setAgentId(self::QC_JOURNEY_GENERATION_AGENT_ID);
                $agentAnalysis = $agentAnalysisList->find();
                if(empty($agentAnalysis)) continue;
                $agentAnalysis = array_values($agentAnalysis)[0];
                $analyzeResult = json_decode($agentAnalysis['analyze_result'] ?? "", true);
                $summaryEng = $analyzeResult['summary'] ?? "";

                $agentAnalysisList->setAgentId(self::QC_CONTENT_DISPLAY_AGENT_ID);
                $agentAnalysis = $agentAnalysisList->find();
                if(empty($agentAnalysis)) continue;
                $agentAnalysis = array_values($agentAnalysis)[0];
                $analyzeResult = json_decode($agentAnalysis['analyze_result'] ?? "", true);
                $summaryCn = $analyzeResult['translation'] ?? "";

                $chatRecordAllDay .= $this->replacePlaceholders($chatRecordUserPrompt , [
                        'seq' => $seq + 1,
                        'channel' => $conversationItem['sns_type'] ?? '',
                        'summary_eng' => $summaryEng,
                        'summary_cn' => $summaryCn,
                        'chat_records' => $conversationItem['content'] ?? '',
                    ]) . PHP_EOL;
            }
            $chatRecordAllDay = trim($chatRecordAllDay);

            //进行中的买家问题
            $ongoingIssues = $this->getOngoingIssue($userId, $customerId);

            $question = $this->replacePlaceholders($userPrompt,[
                'ongoing_issues' => $ongoingIssues,
                'date_today' => $this->dateTime,
                'chat_record_all_day' => $chatRecordAllDay
            ]);

            //调用GPT生成今日的买家问题
            $gptResponse = $this->callByParams($this->companyId, $question, $systemPrompt, AIClient::AZURE_OPENAI_GPT_FOUR_O_LATEST,jsonObject: true);
            $answer = $this->formatResponseAnswer($gptResponse['answer'] ?? "");
            $answer = json_decode($answer, true);
            if(!empty($answer) && isset($answer['exist_new_issues']) && isset($answer['new_issues']))
            {
                $existNewIssues = $answer['exist_new_issues'];
                $newIssues = $answer['new_issues'];
                if($existNewIssues && !empty($newIssues))
                {
                    $currentTime = time();
                    //对应的聊天旅程(user+customer+contact_date)
                    $journeyList = new AiQualityCheckCompanyJourneyList($this->clientId, $this->companyId);
                    $journeyList->setCompanyId($this->companyId);
                    $journeyList->setUserId($userId);
                    $journeyList->setCustomerId($customerId);
                    $journeyList->setContactDate($this->dateTime);
                    $journey = $journeyList->find();
                    if(empty($journey)) continue;
                    $journeyIds = array_column($journey, 'journey_id');

                    foreach ($newIssues as $issue)
                    {
                        $stickingPointDO = new AiQualityCheckStickingPoint();
                        $stickingPointDO->client_id = $this->clientId;
                        $stickingPointDO->company_id = $this->companyId;
                        $stickingPointDO->user_id = $userId;
                        $stickingPointDO->customer_id = $customerId;
                        $stickingPointDO->start_date = $this->dateTime;
                        $stickingPointDO->update_date = $this->dateTime;
                        $stickingPointDO->issue_description = json_encode($issue['issue_description'] ?? '' , JSON_UNESCAPED_UNICODE);
                        $progressHistory = [];
                        $progressHistory[$this->dateTime] = [
                            'progress' =>  $issue['today_progress'] ?? [],
                            'is_directly_related' => "true",
                        ];
                        $stickingPointDO->progress_history = json_encode($progressHistory, JSON_UNESCAPED_UNICODE);
                        $stickingPointDO->journey_ids = PgsqlUtil::formatArray($journeyIds);

                        if($issue['is_resolved']) {
                            $stickingPointDO->issue_state = AiQualityCheckStickingPointModel::ISSUE_STATE_CLOSED;
                            $stickingPointDO->resolution_status = AiQualityCheckStickingPointModel::ISSUE_RESOLVED;
                            $stickingPointDO->resolution_summary = json_encode($issue['resolution_reasoning'] ?? '' , JSON_UNESCAPED_UNICODE);
                            $stickingPointDO->end_date = date('Y-m-d H:i:s', $currentTime);
                        }else{
                            $stickingPointDO->issue_state = AiQualityCheckStickingPointModel::ISSUE_STATE_ON_GOING;
                            $stickingPointDO->resolution_status = AiQualityCheckStickingPointModel::ISSUE_UNSOLVED;
                        }
                        $stickingPointDO->create_time = date('Y-m-d H:i:s', $currentTime);
                        $stickingPointDO->update_time = date('Y-m-d H:i:s', $currentTime);
                        $stickingPointDO->save();
                    }
                }
            }
        }
    }

    private function getOngoingIssue($userId,$customerId) : string
    {
        //1.没有ongoing中的买家问题
        $ongoingIssues = "No ongoing issues.";

        //2.有ongoing中的买家问题
        $ongoingList = Helper::loadStickingPointByUserAndCustomer($this->clientId, [[$userId, $customerId]]);
        $ongoingList = array_values(array_filter($ongoingList , function($item) {
            return isset($item['issue_state']) && $item['issue_state'] == 1;
        }));
        if(!empty($ongoingList)) {
            $ongoingUserPrompt = $this->userPromptMap['qc.v2.new.sticking.point.ongoing.issue.user.prompt'] ?? '';
            $ongoingQuestion = "";
            foreach ($ongoingList as $seq => $ongoingItem)
            {
                $progressHistory = json_decode($ongoingItem['progress_history'], true);
                $progressHistoryStrArray = [];
                foreach ($progressHistory as $progressDate => $progressItem)
                {
                    $progressDate = date('Y-m-d', strtotime($progressDate));
                    $progress = json_encode($progressItem['progress'] ?? '' , JSON_UNESCAPED_UNICODE);
                    $progressHistoryStrArray[] = "{$progressDate}: {$progress}";
                }
                $progressHistoryStr = "{" . implode(",", $progressHistoryStrArray) . "}";
                $ongoingQuestion = $this->replacePlaceholders($ongoingUserPrompt , [
                        'seq' => $seq + 1,
                        'start_date' => date('Y-m-d' , strtotime($ongoingItem['start_date'] ?? '')),
                        'issue_description' => $ongoingItem['issue_description'] ?? '',
                        'progress_history' => $progressHistoryStr,
                    ]) . PHP_EOL;
            }
            $ongoingQuestion = trim($ongoingQuestion);
            if(!empty($ongoingQuestion)) $ongoingIssues = $ongoingQuestion;
        }

        return $ongoingIssues;
    }

    private function updateStickingPointV3($conversations) : void
    {
        $systemPrompt = $this->systemPromptMap['qc.v2.update.progress.system.prompt'] ?? '';
        $llmModel = $this->promptConfig['qc.v2.update.progress.service'] ?? '';
        $userPrompt = $this->userPromptMap['qc.v2.update.progress.user.prompt'] ?? '';
        $chatRecordAllDayUserPrompt = $this->userPromptMap['qc.v2.update.progress.chat.record.user.prompt'] ?? '';
        $issueUserPrompt = $this->userPromptMap['qc.v2.update.progress.chat.issue.user.prompt'] ?? '';

        foreach ($conversations as $pair => $conversationsList)
        {
            $ss = explode("-", $pair);
            $userId = $ss[0];
            $customerId = $ss[1];

            //ongoing中的买家问题
            $issueList = Helper::loadStickingPointByUserAndCustomer($this->clientId,[[$userId,$customerId]]);
            $ongoingIssues = array_values(array_filter($issueList,function ($item) {
                return $item['issue_state'] == AiQualityCheckStickingPointModel::ISSUE_STATE_ON_GOING;
            }));
            if(empty($ongoingIssues)) continue;

            $chatRecordAllDay = "";
            foreach ($conversationsList as $seq => $conversationItem)
            {
                $agentAnalysisList = new AiQualityCheckAgentAnalysisList($this->clientId);
                $agentAnalysisList->setCompanyId($this->companyId);
                $agentAnalysisList->setUserId($userId);
                $agentAnalysisList->setCustomerId($customerId);
                $agentAnalysisList->setContactDate($this->dateTime);
                $agentAnalysisList->setAgentId(self::QC_JOURNEY_GENERATION_AGENT_ID);
                $agentAnalysis = $agentAnalysisList->find();
                if(empty($agentAnalysis)) continue;
                $agentAnalysis = array_values($agentAnalysis)[0];
                $analyzeResult = json_decode($agentAnalysis['analyze_result'] ?? "", true);
                $summaryEng = $analyzeResult['summary'] ?? "";

                $agentAnalysisList->setAgentId(self::QC_CONTENT_DISPLAY_AGENT_ID);
                $agentAnalysis = $agentAnalysisList->find();
                if(empty($agentAnalysis)) continue;
                $agentAnalysis = array_values($agentAnalysis)[0];
                $analyzeResult = json_decode($agentAnalysis['analyze_result'] ?? "", true);
                $summaryCn = $analyzeResult['translation'] ?? "";

                $chatRecordAllDay .= $this->replacePlaceholders($chatRecordAllDayUserPrompt , [
                        'seq' => $seq + 1,
                        'channel' => $conversationItem['sns_type'] ?? '',
                        'summary_eng' => $summaryEng,
                        'summary_cn' => $summaryCn,
                        'chat_records' => $conversationItem['content'],
                    ]) . PHP_EOL;
            }
            $chatRecordAllDay = trim($chatRecordAllDay);

            foreach ($ongoingIssues as $stickingPoint)
            {
                //判断是否是当日的卡点
                $createInCurrentDay = $this->dateTime == date('Y-m-d' , strtotime($stickingPoint['start_date']));
                $issue = $this->replacePlaceholders($issueUserPrompt , [
                    'start_date' => date('Y-m-d', strtotime($stickingPoint['start_date'] ?? '')),
                    'issue_description' => $stickingPoint['issue_description'] ?? '',
                    'progress_history' => $createInCurrentDay ? "No progress history" : $stickingPoint['progress_history'],
                ]);
                $question = $this->replacePlaceholders($userPrompt , [
                    'issue' => $issue,
                    'date_today' => $this->dateTime,
                    'chat_record_all_day' => $chatRecordAllDay,
                ]);
                $callGPTList[] = [
                    'systemPrompt' => $systemPrompt,
                    'question' => $question,
                    'model' => $llmModel,
                    'referId' => $stickingPoint['sticking_point_id'],
                    'afterProcess' => 'updateProgressAfterProcess',
                    'afterProcessParams' => [
                        'stickingPointId' => $stickingPoint['sticking_point_id'],
                        //如果没有“解决”,使用相同的question,不同的systemPrompt再次调用GPT
                        'question' => $question,
                        'journeyIds' => array_column($conversationsList, 'journey_id'),
                    ]
                ];
            }
        }

        if(!empty($callGPTList)) {
            $this->callGPTLoop($callGPTList);
        }
    }

    /**
     * 新版 聊天旅程生成
     */
    public function journeyGenerationV2($runContext) : array
    {
        $conversations = $runContext['conversations'] ?? [];
        $systemPrompt = $this->systemPromptMap['journeyGenerationV2.systemPrompt'];
        $userPrompt = <<<EOF
<Today's Communication>
--- {{date_today}} ---
{{communication_today}}
</Today's Communication>
EOF;
        $recentSummaryPrompt = <<<EOF
<Recent Communication Summary>
{{recent_communication_summary}}
</Recent Communication Summary>
EOF;
        $model = AiServiceConstant::SERVICE_QWEN_MAX_LATEST;

        foreach ($conversations as $pair => $_conversation) {
            $ss = explode("-", $pair);
            $userId = $ss[0];
            $customerId = $ss[1];

            // 最近60天内的三次沟通总结
            $agentAnalysisPdo = new AiQualityCheckAgentAnalysisList($this->clientId);
            $agentAnalysisPdo->setCompanyId($this->companyId);
            $agentAnalysisPdo->setUserId($userId);
            $agentAnalysisPdo->setCustomerId($customerId);
            $agentAnalysisPdo->setStartContactDate(date('Y-m-d',strtotime('-2 month',strtotime($this->dateTime))));
            $agentAnalysisPdo->setEndContactDate(date('Y-m-d',strtotime('-1 day',strtotime($this->dateTime))));
            $agentAnalysisPdo->setAgentId(self::QC_JOURNEY_GENERATION_AGENT_ID);
            $agentAnalysisPdo->setOrderBy('contact_date');
            $agentAnalysisPdo->setOrder('desc');
            $agentAnalysisPdo->setLimit(3);
            $agentAnalysisList = $agentAnalysisPdo->find();
            $recentSummary = "";
            if(!empty($agentAnalysisList)) {
                $agentAnalysisList = array_reverse($agentAnalysisList);
                $recentSummarys = array_map(function ($item){
                    $analysisResult = $item['analyze_result'] ?? "";
                    $analysisResult = json_decode($analysisResult, true);
                    $summary = $analysisResult['summary'] ?? "";
                    $contactDate = $item['contact_date'];
                    return <<<EOF
$contactDate: $summary
EOF;
                },$agentAnalysisList);
                $recentSummary = implode(PHP_EOL, $recentSummarys);
            }

            // 当日沟通理解的输出
            $agentAnalysisPdo = new AiQualityCheckAgentAnalysisList($this->clientId);
            $agentAnalysisPdo->setCompanyId($this->companyId);
            $agentAnalysisPdo->setUserId($userId);
            $agentAnalysisPdo->setCustomerId($customerId);
            $agentAnalysisPdo->setAgentId(self::QC_EVENT_ANNOTATION_AGENT_ID);
            $agentAnalysisPdo->setContactDate($this->dateTime);
            $agentAnalysisPdo->setLimit(1);
            $agentAnalysis = $agentAnalysisPdo->find();
            if(empty($agentAnalysis)) {
                continue;
            }

            $agentAnalysis = $agentAnalysis[0];
            $analyzeResult = json_decode($agentAnalysis['analyze_result'], true);
            $understandings = $analyzeResult['conversation_content'] ?? [];
            $communicationToday = [];
            foreach ($understandings as $channel => $contents) {
                $content = $contents['conversation_content'] ?? [];
                $content = json_encode($content, JSON_UNESCAPED_UNICODE);
                $communicationToday[] = <<<EOF
$channel: $content
EOF;
            }
            $communicationToday = implode(PHP_EOL, $communicationToday);

            // 调用gpt
            $question = $this->replacePlaceholders($userPrompt, [
                'date_today' => $this->dateTime,
                'communication_today' => $communicationToday,
            ]);
            if(!empty($recentSummary)) {
                $recentSummary = $this->replacePlaceholders($recentSummaryPrompt, ['recent_communication_summary' => $recentSummary]);
                $question = $recentSummary . PHP_EOL . $question;
            }
            $gptResponse = $this->callByParams($this->companyId, $question, $systemPrompt, $model, 0.1, jsonObject: false);
            $todaySummary = $gptResponse['answer'] ?? "";

            $this->saveAgentMemories($this->clientId, $this->companyId, $userId, $customerId, self::QC_JOURNEY_GENERATION_AGENT_ID,['summary' => $todaySummary]);
        }

        return [$runContext, false];
    }

    private function getChatUnderstandingHistoryMessage($snsType) : array{
        $ret = [];
        $key = "understanding.sample.{$snsType}";
        $samples = $this->promptConfig[$key];
        foreach ($samples as $sample)
        {
            foreach ($sample as $role => $content) {
                $ret[] = [
                    'role' => $role,
                    'content' => $content,
                ];
            }
        }

        return $ret;
    }

    private function translateToTw(string $text)
    {
        $result = (new TranslateService())
            ->setSource('auto')
            ->setStrategy('IcbuTranslate')
            ->setTarget('zh-tw')
            ->setContent($text)
            ->translate();

        return $result[0]['content'] ?? "";
    }
}