<?php

namespace common\library\ai_agent;

use AiAgent;
use ArrayUtil;
use common\library\ai_agent\api\AIClient;
use common\library\ai_agent\api\AiStreamClient;
use common\library\ai_agent\communication\Constants;
use common\library\ai_agent\communication\message_hub\Message;
use common\library\ai_agent\communication\message_hub\TableStoreMessageHub;
use common\library\ai_agent\communication\summary\ChatService;
use common\library\ai_agent\company_quality_check\AiQualityCheckChatJourney;
use common\library\ai_agent\exception\AiAgentException;
use common\library\ai_agent\message\AbstractMessageFormat;
use common\library\ai_agent\message\Card;
use common\library\ai_agent\message\component\Button;
use common\library\ai_agent\message\component\FeedBack;
use common\library\ai_agent\message\component\IconText;
use common\library\ai_agent\message\OptionsCard;
use common\library\ai_agent\message\Text;
use common\library\ai_agent\message\TranslateCard;
use common\library\ai_agent\message\ChatReplyCard;
use common\library\ai_agent\message\ChatReplyOtherStrategyListCard;
use common\library\ai_agent\message\ChatReplyOtherStrategyReplyCard;
use common\library\ai_agent\message\ChatExpiredErrorCard;
use common\library\ai_agent\record\AiServiceProcessRecord;
use common\library\ai_portrait_analysis\AiPortraitAnalysisDetail;
use common\library\ai_agent\utils\ProtobufWebsocketResponse;
use common\library\ai_service\AiAgentConversationHistory;
use common\library\prompt\PromptConstant;
use Hyperf\Utils\Collection;
use RedisService;

class NewChatReplyAiAgent extends BaseAiAgent
{
    use ChatTrait;

    protected bool $stream = true;
    protected int $streamRiskBufferSize = 15;

    protected array $followSuggestionExtInfo = [];

    public string $systemPrompt = 'You are a veteran cross-border B2B e-commerce expert with extensive experience in international trade between China and foreign countries.';

    public int $maxHistoryLength = 0;   // 不需要历史消息


    public const TYPE_STOCK = 'stock_identify';    // 卡点识别
    public const TYPE_REACTIVE = 'reactive'; // 客户再激活
    public const TYPE_RETRIEVING = 'retrieving';   // 客户挽回
    public const TYPE_GUIDE = 'guide';    // 引导回复
    public const TYPE_RESOLVE = 'resolve';  // 解决问题
    public const TYPE_PERSUADE = 'persuade'; // 说服客户

    public const TYPE_FOLLOW_UP = 'follow_up';    // 客户跟进

    public const TYPE_CHANGE_STRATEGY = 'change_strategy';  // 重新选择策略

    public const TYPE_CONTINUE_ASK = 'continue_ask';    // 追问

    public const TYPE_DEEP_SEEK = 'deep_seek';    // 深度思考



    // gpt识别的卡点信息
    protected array $stockPoint = [
        'issue' => '',
        'stance' => '',
        'feedback' => '',
        'resolved' => false,
    ];
    protected Message|null $lastVisitorMessage = null;

    // gpt返回的结果
    protected array $aiReply = [
        'strategy' => '',
        'reply' => ''
    ];


    // 不同卡点的策略
    public const STRATEGY_LISTS = [
        'The customer believes the MOQ is too high' => [
            ['strategy_id' => 1000, 'text' => '坚持不改变MOQ，从产品质量与成本的角度解释moq的合理性，我们真诚希望与客户合作'],
            ['strategy_id' => 1001, 'text' => '考虑折中方案：将MOQ调整至[x pcs]，这既顾及了成本也展现了我们的诚意'],
            ['strategy_id' => 1002, 'text' => '坚持底线，这是我们能接受的最低数量'],
            ['strategy_id' => 1003, 'text' => '不改变MOQ，如果客户下单可以提供[x%]的优惠'],
            ['strategy_id' => 1004, 'text' => '如果客户考虑发展长期合作关系，首次订单可以不限制MOQ'],
            ['strategy_id' => 1005, 'text' => '提供灵活的解决方案，接受较小的订单但单品单价会有所提升'],
            ['strategy_id' => 1006, 'text' => '向客户推荐一个相似但MOQ较低的库存商品，并提供[商品链接]'],
            ['strategy_id' => 1007, 'text' => '发送产品目录，客户可以选择其它感兴趣产品从而达到MOQ'],
            ['strategy_id' => 1008, 'text' => '表示尊重客户的想法，虽然这次无法达成一致，希望未来有机会合作'],
        ],
        'The customer believes the product quote is too high' => [
            ['strategy_id' => 2000, 'text' => '从产品质量和成本角度解释价格，询问客户的预期价格，展现解决问题的态度'],
            ['strategy_id' => 2001, 'text' => '坚持不改变价格，当前价格保障了产品的高质量，符合客户的长期利益'],
            ['strategy_id' => 2002, 'text' => '坚持不改变价格，价格是好产品的唯一缺点，如果客户尝试购买就会发现它们物超所值'],
            ['strategy_id' => 2003, 'text' => '强调价格过低的产品可能带来的风险，我们的产品质量好，长远来看的成本更低'],
            ['strategy_id' => 2004, 'text' => '坚持底线，这已经是我们的底价且非常接近成本，真诚希望客户与我们合作'],
            ['strategy_id' => 2005, 'text' => '考虑折中方案：将价格调整至[x USD]，这既顾及了成本也展现了我们的诚意'],
            ['strategy_id' => 2006, 'text' => '展现出对客户的重视，表示将去和老板沟通申请特别优惠'],
            ['strategy_id' => 2007, 'text' => '向客户提供一个[x%]的特别优惠'],
            ['strategy_id' => 2008, 'text' => '如果客户增加订单数量，可以提供数量优惠，从而降低客户的单位成本'],
            ['strategy_id' => 2009, 'text' => '如果客户愿意成为我们的长期客户，可以提供针对长期客户的特别优惠'],
            ['strategy_id' => 2010, 'text' => '向客户推荐一个价格更低的相似产品并提供[商品链接]'],
            ['strategy_id' => 2011, 'text' => '理解并尊重客户的观点，虽然这次无法达成一致，希望未来有机会与客户合作'],
        ],
        'The customer believes the sample fee is too high' => [
            ['strategy_id' => 3000, 'text' => '从产品质量和成本角度解释价格，询问客户的预期价格，展现解决问题的态度'],
            ['strategy_id' => 3001, 'text' => '坚持不改变样品价格，如果客户尝试了样品就会发现它们物超所值'],
            ['strategy_id' => 3002, 'text' => '坚持不改变样品价格，但可以为客户后续的订单提供折扣'],
            ['strategy_id' => 3003, 'text' => '坚持不改变样品价格，但样品费用可以在后续大额订单中抵扣'],
            ['strategy_id' => 3004, 'text' => '考虑折中方案：将价格调整至[x USD]，这既顾及了成本也展现了我们的诚意'],
            ['strategy_id' => 3005, 'text' => '向客户推荐一个价格更低的相似产品并提供[商品链接]'],
            ['strategy_id' => 3006, 'text' => '展现出对客户的重视，表示将去和老板沟通申请特别优惠'],
            ['strategy_id' => 3007, 'text' => '向客户提供一个[x%]的特别优惠'],
            ['strategy_id' => 3008, 'text' => '理解并尊重客户的观点，虽然这次无法达成一致，希望未来有机会与客户合作'],
        ],
        'The customer believes the taxes are too high' => [
            ['strategy_id' => 4000, 'text' => '向客户解释所有中国供应商都面临相同的税费问题，但我们的产品质量和服务是最好的'],
            ['strategy_id' => 4001, 'text' => '强调税费是政府规定的，我们无法控制，表达进一步协商的意愿'],
            ['strategy_id' => 4002, 'text' => '展现出对客户的重视，表示将去和老板沟通申请特别优惠'],
            ['strategy_id' => 4003, 'text' => '向客户提供一个[x%]的特别优惠'],
            ['strategy_id' => 4004, 'text' => '理解并尊重客户的观点，虽然这次无法合作，希望未来有机会与客户合作'],
        ],
        'The customer believes the shipping cost is too high' => [
            ['strategy_id' => 5000, 'text' => '向客户解释运费是由物流公司收取，我们无法控制，希望客户理解'],
            ['strategy_id' => 5001, 'text' => '询问客户的物流需求，如预期运费和物流时间'],
            ['strategy_id' => 5002, 'text' => '询问客户能否接受更慢的物流方案，如果可以将减少运费'],
            ['strategy_id' => 5003, 'text' => '向客户推荐[物流方案]，并从成本和时效性方面解释推荐理由'],
            ['strategy_id' => 5004, 'text' => '强调运费是由物流公司固定收取，我们无法控制，希望客户理解'],
            ['strategy_id' => 5005, 'text' => '向客户解释最近是物流高峰期，因此物流费用相对较高'],
            ['strategy_id' => 5006, 'text' => '建议客户改变交货时间至[推荐时间]，从而避开高峰期节约物流成本'],
            ['strategy_id' => 5007, 'text' => '建议客户增加订单数量，从而降低每件产品的运输成本'],
            ['strategy_id' => 5008, 'text' => '询问买家是否有合作的货代，如果有，提供EXW或FOB条款让买家使用自己的物流资源'],
            ['strategy_id' => 5009, 'text' => '强调我们对客户的重视，如果客户愿意成为长期伙伴，可以在后续大额订单中抵扣本次的运费'],
            ['strategy_id' => 5010, 'text' => '理解并尊重客户的观点，虽然这次无法达成一致，希望未来有机会与客户合作'],
        ],
        'The salesman urges the customer to make payment' => [
            ['strategy_id' => 6000, 'text' => '催促客户付款，并表示一旦收到款项，将立即安排发货'],
            ['strategy_id' => 6001, 'text' => '催促客户付款，否则可能会延误生产从而影响交货'],
            ['strategy_id' => 6002, 'text' => '催促客户付款，避免产生额外滞港费用'],
        ],
        'The customer is not satisfied with the samples received' => [
            ['strategy_id' => 7000, 'text' => '表明解决问题的态度，深入了解客户对哪些细节不满意'],
            ['strategy_id' => 7001, 'text' => '根据客户反馈提出产品调整方案'],
            ['strategy_id' => 7002, 'text' => '承诺在未来的订单中解决问题，如果客户需要可以提供修订样品'],
            ['strategy_id' => 7003, 'text' => '承诺解决问题，为表歉意将为客户的下一次订单提供折扣'],
            ['strategy_id' => 7004, 'text' => '承诺解决问题，为表歉意将在随下次订单附赠小礼物补偿客户'],
            ['strategy_id' => 7005, 'text' => '根据客户需求提供替代选项'],
            ['strategy_id' => 7006, 'text' => '与客户讨论退款或换货服务'],
        ]
    ];

    //不同的卡点策略-英语
    public const STRATEGY_ENG_LISTS = [
        'The customer believes the MOQ is too high' => [
            ['strategy_id' => 1000, 'text' => 'Explain the rationale behind not changing the MOQ from the perspective of product quality and cost. We sincerely hope to cooperate with the customer.'],
            ['strategy_id' => 1001, 'text' => 'Consider a compromise: adjust the MOQ to [x pcs], which takes into account costs and demonstrates our sincerity.'],
            ['strategy_id' => 1002, 'text' => 'Stand firm on the minimum quantity; this is the lowest amount we can accept.'],
            ['strategy_id' => 1003, 'text' => 'Do not change the MOQ, but offer a discount of [x%] if the customer places an order.'],
            ['strategy_id' => 1004, 'text' => 'If the customer is considering a long-term partnership, we can waive the MOQ for the first order.'],
            ['strategy_id' => 1005, 'text' => 'Provide flexible solutions by accepting smaller orders with a higher unit price.'],
            ['strategy_id' => 1006, 'text' => 'Recommend a similar product with a lower MOQ and provide the product link.'],
            ['strategy_id' => 1007, 'text' => 'Send the product catalog, allowing the customer to choose other products of interest to meet the MOQ.'],
            ['strategy_id' => 1008, 'text' => "Express respect for the customer's perspective, and although we cannot reach an agreement this time, we hope for opportunities to collaborate in the future."],
        ],
        'The customer believes the product quote is too high' => [
            ['strategy_id' => 2000, 'text' => "Explain the price from the perspective of product quality and cost, inquire about the customer's expected price, and demonstrate a problem-solving attitude."],
            ['strategy_id' => 2001, 'text' => "Insist on not changing the price; the current price ensures high product quality, which aligns with the customer's long-term interests."],
            ['strategy_id' => 2002, 'text' => "Maintain the price, as it is the only downside to a good product, and if the customer tries it, they will find it offers great value for money."],
            ['strategy_id' => 2003, 'text' => "Emphasize the risks associated with products priced too low and highlight that our product's quality leads to lower long-term costs."],
            ['strategy_id' => 2004, 'text' => 'Stand firm on the price, as it is already our bottom line and very close to cost; we sincerely hope the customer will cooperate with us.'],
            ['strategy_id' => 2005, 'text' => 'Consider a compromise: adjust the price to [x USD], which takes costs into account and shows our sincerity.'],
            ['strategy_id' => 2006, 'text' => 'Show the importance we place on the customer by expressing the intention to discuss with the boss to apply for a special discount.'],
            ['strategy_id' => 2007, 'text' => 'Offer the customer a special discount of [x%].'],
            ['strategy_id' => 2008, 'text' => 'If the customer increases the order quantity, we can offer a volume discount to reduce their unit cost.'],
            ['strategy_id' => 2009, 'text' => 'If the customer is willing to become a long-term client, we can provide special discounts for long-term customers.'],
            ['strategy_id' => 2010, 'text' => 'Recommend a similar product with a lower price and provide the product link.'],
            ['strategy_id' => 2011, 'text' => "Understand and respect the customer's perspective, and although we cannot reach an agreement this time, we hope for future opportunities to collaborate."],
        ],
        'The customer believes the sample fee is too high' => [
            ['strategy_id' => 3000, 'text' => "Explain the price from the perspective of product quality and cost, inquire about the customer's expected price, and demonstrate a problem-solving attitude."],
            ['strategy_id' => 3001, 'text' => "Stand firm on the sample price; if the customer tries the samples, they will find they offer excellent value for money."],
            ['strategy_id' => 3002, 'text' => "Maintain the sample price but offer discounts for the customer's subsequent orders."],
            ['strategy_id' => 3003, 'text' => 'Keep the sample price unchanged, but the sample cost can be deducted from subsequent large orders.'],
            ['strategy_id' => 3004, 'text' => "Consider a compromise: adjust the price to [x USD], which takes costs into account and shows our sincerity."],
            ['strategy_id' => 3005, 'text' => "Recommend a similar product with a lower price and provide the product link."],
            ['strategy_id' => 3006, 'text' => "Show the importance we place on the customer by expressing the intention to discuss with the boss to apply for a special discount."],
            ['strategy_id' => 3007, 'text' => "Offer the customer a special discount of [x%]."],
            ['strategy_id' => 3008, 'text' => "Understand and respect the customer's perspective, and although we cannot reach an agreement this time, we hope for future opportunities to collaborate."],
        ],
        'The customer believes the taxes are too high' => [
            ['strategy_id' => 4000, 'text' => "All suppliers face the same tax issues, but our product quality and service are the best."],
            ['strategy_id' => 4001, 'text' => "Emphasize that taxes are government-mandated and beyond our control, while expressing willingness for further negotiation"],
            ['strategy_id' => 4002, 'text' => "Show how much we value the customer by expressing intention to discuss with management for special discount approval"],
            ['strategy_id' => 4003, 'text' => "Offer the customer a special discount of [x%]"],
            ['strategy_id' => 4004, 'text' => "Understand and respect the customer's perspective, and although we cannot work together this time, we hope for future opportunities to collaborate"],
        ],
        'The customer believes the shipping cost is too high' => [
            ['strategy_id' => 5000, 'text' => "Explain to the customer that shipping costs are charged by the logistics company and are beyond our control. We hope for their understanding."],
            ['strategy_id' => 5001, 'text' => "Inquire about the customer's logistics needs, such as expected shipping costs and delivery time."],
            ['strategy_id' => 5002, 'text' => "Ask the customer if they can accept a slower shipping option, which would reduce shipping costs."],
            ['strategy_id' => 5003, 'text' => "Recommend [logistics solution] to the customer, explaining the reasons for the recommendation in terms of cost and timeliness."],
            ['strategy_id' => 5004, 'text' => "Emphasize that shipping costs are fixed by the logistics company and are beyond our control. We hope for their understanding."],
            ['strategy_id' => 5005, 'text' => "Explain to the customer that it is currently a peak season for logistics, which is why shipping costs are relatively high."],
            ['strategy_id' => 5006, 'text' => "Suggest that the customer change the delivery time to [recommended time] to avoid peak periods and save on shipping costs."],
            ['strategy_id' => 5007, 'text' => "Recommend that the customer increase the order quantity to reduce the shipping cost per product."],
            ['strategy_id' => 5008, 'text' => "Ask the buyer if they have a freight forwarder they work with. If so, offer EXW or FOB terms to allow the buyer to use their own logistics resources."],
            ['strategy_id' => 5009, 'text' => "Emphasize our commitment to the customer and propose that if they are willing to become a long-term partner, we can offset this shipping cost in future large orders."],
            ['strategy_id' => 5010, 'text' => "Acknowledge and respect the customer's perspective. Even though we cannot reach an agreement this time, we hope for the opportunity to work together in the future."],
        ],
        'The salesman urges the customer to make payment' => [
            ['strategy_id' => 6000, 'text' => "Urge the customer to make the payment, and indicate that we will arrange for immediate shipment upon receipt of the payment."],
            ['strategy_id' => 6001, 'text' => "Encourage the customer to make the payment; otherwise, production might be delayed, affecting the delivery schedule."],
            ['strategy_id' => 6002, 'text' => "Prompt the customer to make the payment to avoid incurring additional demurrage charges."],
        ],
        'The customer is not satisfied with the samples received' => [
            ['strategy_id' => 7000, 'text' => "Express a willingness to resolve any issues and seek to understand which details the customer is dissatisfied with."],
            ['strategy_id' => 7001, 'text' => "Propose product adjustment solutions based on customer feedback."],
            ['strategy_id' => 7002, 'text' => "Commit to resolving the issue in future orders, and offer to provide revised samples if the customer needs them."],
            ['strategy_id' => 7003, 'text' => "Pledge to resolve the issue and, as an apology, offer a discount on the customer's next order."],
            ['strategy_id' => 7004, 'text' => "Promise to address the problem and, as a gesture of apology, include a small gift with the next order as compensation."],
            ['strategy_id' => 7005, 'text' => "Provide alternative options based on the customer's needs."],
            ['strategy_id' => 7006, 'text' => "Discuss refund or exchange services with the customer."],
        ]
    ];

    // 销售助手移动端需要区分【正常的辅助回复】和【其他策略回复】的小卡片类型
    private bool $isForOtherStrategyReply = false;

    public function getAgentSceneType(): int
    {
        return AiAgent::AI_AGENT_SCENE_TYPE_CHAT_REPLY;
    }

    public function process(array $params = [], string $function = ''): AiAgentProcessResponse
    {
        $this->agentProcessParams = $params['params'] ?? [];
        $this->agentProcessParams['question'] = $this->question;

        // 获取辅助回复最后切换的语言
        $info = Helper::getLanguageSetting($params, $this->clientId, $this->agentId, $this->userId);
        $info = json_decode($info['ext_info'], true);

        $this->languageMap = ['language_list' => Helper::getLanguageList(), 'language' => $info['language'] ?? 'English'];
        $this->language = $info['language'] ?? 'English';

        if (isset($params['params']['is_follow_suggestion']) && $params['params']['is_follow_suggestion'] == 1) {
            return $this->generateResponseTemplates($params['params']);
        }

        if(isset($params['params']['switch_language_flag']) && $params['params']['switch_language_flag']){
            return $this->initAiAgentProcessResponse(
                $this->retryByLanguage($params),
                '',
                AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT
            );
        }

        // 获取后要再保存一次，因为最新的历史记录因为被当前这次的消息被覆盖
//        $this->saveLanguageSetting($params,$this->language);

        //追问多语言
        if(in_array($this->question,['Help me generate other response strategies','帮我生成其他回复策略','幫我生成其他回復策略'])) {
            return $this->returnStrategyList();
        }

        // 获取聊天记录
        $messageHub = $this->makeMessageHub($params);
        $messages = $messageHub->getMessages($this->businessType);
        $this->lastVisitorMessage = $messageHub->getLastVisitorMessage($messages);

        $channelType = (int)$this->agentProcessParams['channel_type'];
        if (in_array($channelType, [
            Constants::CHANNEL_TYPE_WABA,
            Constants::CHANNEL_TYPE_FACEBOOK,
            Constants::CHANNEL_TYPE_INS,
        ], true)) {
            /** @var TableStoreMessageHub $messageHub */
            $conversationInfo = method_exists($messageHub, 'getConversationInfo')
                ? $messageHub->getConversationInfo()
                : [];
            if (time() - ($conversationInfo['visitor_reply_time'] ?? 0) > 86400) {
                throw new AiAgentException(\Yii::t('ai', '抱歉，当前会话已结束，无法生成辅助回复。'), AiAgentException::ERR_CHAT_EXPIRED);
            }
        }

        // 计算流程
        if(!empty($this->agentProcessParams['strategy_id'])){
            // 用户重新选择策略，也算追问逻辑
            $processType = self::TYPE_CHANGE_STRATEGY;
            $this->isForOtherStrategyReply = true;
        } elseif ($this->isContinueAsk()) {
            // 追问逻辑
            $processType = self::TYPE_CONTINUE_ASK;
            $this->isForOtherStrategyReply = true;
        } else {
            // 不同的模块
            $processType = $this->determineProcessType($messageHub, $messages, ($this->promptConfig['identify_stock_point_service'] ?? AIClient::AZURE_OPENAI_GPT_FOUR_O_LATEST));
            $this->isForOtherStrategyReply = false;
        }
        // 生成prompt
        $prompt = $this->getPromptTemplate($processType, $messages, $this->agentProcessParams['question']);
        $model = $this->promptConfig["{$processType}.service"] ?? $this->llmModel;

        // 记录消息时间
        $recentMessages = $messages->slice(-20, 20);
        $sendTimes = ChatService::getMessagesSendTime($recentMessages);

        // 记录调用的prompt模块
        $aiProcessRecordPdo = new AiServiceProcessRecord($this->context['record_id']);
        $aiProcessRecordPdo->appendContext([
            'process_type' => $processType,
            'send_time' => $sendTimes,
        ]);
        if (!empty($this->agentProcessParams['strategy_id'])) {
            $aiProcessRecordPdo->appendContext(['strategy_id' => (int) $this->agentProcessParams['strategy_id']]);
        }
        $aiProcessRecordPdo->save();

        // 调用llm
        $this->llmService->setTemperature(0.2);
        $this->llmService->setModel($model);
        $this->llmModel = $model;
        $gptResponse = $this->callLlm($prompt);


        if ($this->sseResponse instanceof ProtobufWebsocketResponse) {
            // 适配App端
            // 销售助手移动端需要区分【正常的辅助回复】和【其他策略回复】的小卡片类型
            if ($this->isForOtherStrategyReply) {
                $cardType = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_REPLY_OTHER_STRATEGY_REPLY_CARD;
            } else {
                $cardType = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_REPLY_CARD;
            }
        } else {
            $cardType = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TRANSLATE_CARD;
        }
        return $this->initAiAgentProcessResponse(
            $gptResponse,
            __FUNCTION__,
            $cardType
        );
    }

    public function isContinueAsk()
    {
        $question = $this->agentProcessParams['question'] ?? '';
        return (!empty($question) && $question != \common\library\ai_agent\AiAgentConstants::AI_CHAT_REPLY_PRESET_MESSAGE);
    }

    public function onReceiveStream(int $i, AbstractMessageFormat $message,
                                    string $str, AiAgentProcessResponse $response, string $fullText): void
    {
        $responseStrategy = \Yii::t('ai', '回复策略');
        $responseContent = \Yii::t('ai', '回复内容');
        if ($this->isContinueAsk()) {
            if ($this->sseResponse instanceof ProtobufWebsocketResponse) {
                // 适配App端
                $result = $this->tryParseAiReply($fullText); // App端追问时只返回回复内容，但需要去除"回复内容"前缀
                if (empty($result[$responseContent])) {
                    // 解析失败，直接返回完整的回复内容
                    $result[$responseContent] = $fullText; // App端追问时只返回回复内容
                    $result[$responseStrategy] = '';
                }
            } else {
                $result = [$responseContent => $fullText]; // Web端追问时只返回回复内容
            }
        } elseif ($this->isFollowSuggestion()) {
            $result[$responseStrategy] = $this->followSuggestionExtInfo[$responseStrategy];
            $result[$responseContent] = $fullText;
        } else {
            $result = $this->tryParseAiReply($fullText, [$responseStrategy,$responseContent]);
        }

        $result = [
            'strategy' => $result[$responseStrategy] ?? '',
            'reply' => $result[$responseContent] ?? '',
        ];

        $messages = $this->getMessageObj($response);    /** @uses self::getProcessSseObj()  */
        if ($this->sseResponse instanceof ProtobufWebsocketResponse) {
            // 适配App端
            if ($this->isForOtherStrategyReply) {
                $card = $messages[AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_REPLY_OTHER_STRATEGY_REPLY_CARD];
            } else {
                $card = $messages[AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_REPLY_CARD];
            }
        } else {
            $card = $messages[AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TRANSLATE_CARD];
        }
        $card->setAnswerHistoryId($response->historyId);
        $skeletonMessage = $card->getSkeletonMessage(); // 空卡片
        if ($this->aiReply !== $result) {
            if (empty($this->aiReply['strategy']) && empty($this->aiReply['reply'])) {
                // 发送初始化消息
                $skeletonMessage['status'] = AiAgentConstants::MESSAGE_STATUS_INIT;
                $skeletonMessage['stream_type'] = 'overwrite';
                $this->sseResponse->writeJson($skeletonMessage);
            }

            // 实时发送增量回复结果
            $this->aiReply = $result;
            $response->context = $result;
            $skeletonMessage['status'] = AiAgentConstants::MESSAGE_STATUS_PROCESSING;
            // $skeletonMessage['context'] = [
            //     'content' => $this->aiReply['reply'],
            //     'strategy' => $this->aiReply['strategy'],
            //     'stream_type' => 'overwrite'
            // ];
            // 不能覆盖整个context，否则会导致其他字段丢失
            $skeletonMessage['context']['content'] = $this->aiReply['reply'];
            $skeletonMessage['context']['strategy'] = $this->aiReply['strategy'];
            $skeletonMessage['context']['stream_type'] = 'overwrite';
            usleep(25000);  // 25ms
            $this->sseResponse->writeJson($skeletonMessage);
        }
    }

    public function isFollowSuggestion()
    {
        return isset($this->agentProcessParams['is_follow_suggestion']);
    }



    /**
     * 发送翻译文本
     * @param AiAgentProcessResponse $response
     * @param string $fullText gpt返回的完整字符串
     * @return void
     */
    protected function beforeCloseMessage(AiAgentProcessResponse $response, string $fullText): void
    {
        // 最后返回翻译的文本
        $messages = $this->getMessageObj($response);
        $message = $messages[$response->messageType] ?? (new Text());

        // 如果有卡点，就增加一个按钮“选择其它策略”
        if ($this->hasStockPoint()) {
            $button = new Button(icon: '', text: \Yii::t('ai','其他策略'), event: Button::EVENT_CHOOSE_STRATEGY, history: false);
            $response->context['actions'][] = $button->toArray();
        }

        // 返回消息需要带上该字段，用于前端埋点
        // 失败的信息不会在 agent 中生成 message 对象，所以需要统一设置
        $message->setQuestionHistoryId($this->context['question_history_id'] ?? 0);
        $message->setAnswerHistoryId($this->context['answer_history_id'] ?? 0);
        $message->withRecordId($this->context['record_id'] ?? 0);
        $message->withConversationId($this->conversationId);

        if(!$this->isContinueAsk())
        {
            $response->context['language'] = $this->agentProcessParams['language'] ?? 'en';
            $response->context['language_list'] = $this->languageMap['language_list'] ?? '';
        }

        $item = $message->formatAllContent($this->aiReply['reply'], $response->context);

        $this->sseResponse->writeJson($item);

        $this->handleBilling();
    }

    /**
     * 生成历史记录
     * @param AiAgentProcessResponse $aiAgentProcessResponse
     * @return string
     */
    public function generateHistoryContent(AiAgentProcessResponse $aiAgentProcessResponse): string
    {
        if (!empty($aiAgentProcessResponse->function))
        {
            $function = $aiAgentProcessResponse->function;
            $function = "get{$function}SseObj";
            $messages = $this->$function($aiAgentProcessResponse);
            $message = $messages[$aiAgentProcessResponse->messageType] ?? new Text();
        } else {
            $message = new Text();
        }

        // 获取消息体
        if (!empty($aiAgentProcessResponse->answer)) {
            // 有异常的时候会走到这里
            $history = $message->getSkeletonMessage();  // 空卡片
            $history = $history['context'] ?? [];
            $history['content'] = $aiAgentProcessResponse->answer;
        } else {
            $history = [];
            // 这一步会返回完整的小卡片，不需要先生成空卡片，不然会导致存在多个【内容由AI生成】
            $item = $message->formatAllContent($this->aiReply['reply'], $aiAgentProcessResponse->context);
            if (!empty($item['context'])) {
                $history = array_replace_recursive($history, $item['context']);
            }

            // 获取消息体，图文返回结构体
            $history['content'] = $this->aiReply['reply'];
            $history['strategy'] = $this->aiReply['strategy'];
        }

        return json_encode($history, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 获取上一次生成的辅助回复
     * @return array
     */
    public function getPreviousResult(): array
    {
        $latestProcessRecord = $this->getProcessRecordByOrder();
        $response = $latestProcessRecord['request_response'] ?? '{}';

        // 解析gpt响应
        $gptResponse = json_decode($response, true)['content'] ?? '';
        $result = $this->tryParseAiReply($gptResponse);
        if (empty($result['回复策略']) && empty($result['回复内容'])) { // 兼容不同格式的gpt输出
            $result['回复内容'] = $gptResponse;
        }
        return $result;
    }

    /**
     * 获取此前的卡点识别结果
     * @return array
     */
    public function getPreviousStock(): array
    {
        $latestProcessRecord = $this->getProcessRecordByOrder('asc');
        $response = $latestProcessRecord['context'] ?? '{}';

        $stockPoint = json_decode($response, true)['stockPoint'] ?? [];
        return array_merge($this->stockPoint, $stockPoint);
    }

    /**
     * AI辅助回复的小卡片
     *
     * @param AiAgentProcessResponse $aiAgentProcessResponse
     * @return array
     */
    public function getProcessSseObj(AiAgentProcessResponse $aiAgentProcessResponse): array
    {
        $outsideFooterLeft = [
            new Button(
                text: \Yii::t('ai','详细沟通建议'),
                event: Button::EVENT_SET_SCENE_TYPE,
                params: [
                    'recordId' => $aiAgentProcessResponse->recordId,
                    'separatorTitle' => \Yii::t('ai','辅助回复已结束'),
                    'sceneType' => PromptConstant::SCENE_TYPE_AI_SERVICE_SCENE_AI_CHAT_COACH,
                    'options' => $this->agentProcessParams,
                    'needCleanChatList' => false,
                    'lastSceneType' => PromptConstant::SCENE_TYPE_AI_SERVICE_SCENE_AI_CHAT_REPLY
                ],
            ),
            new Button(text: \Yii::t('ai','录入谈单指南'), event: Button::EVENT_SAVE_TO_GUIDE, params: ['needCleanChatList' => false, 'content' => '']),
        ];
        if (!empty($this->requestParams['params']['is_follow_suggestion'])) {
            $leftFooter = [new Button(icon: 'LeadsSendOutline', text: \Yii::t('ai', "复制并发送"), event: Button::EVENT_COPY_AND_SEND, params: ($this->requestParams['params']['action_params'] ?? []))];
            // 详细沟通建议在该场景不需要
            unset($outsideFooterLeft[0]);
        } else {
            $leftFooter = [
                new Button(icon: 'copy', event: Button::EVENT_COPY, params: ['content' => $this->aiReply['reply']]),
                new Button(icon: 'Back', event: Button::EVENT_FILL_IN_SEND_BOX, text: \Yii::t('ai','插入输入框'))
            ];
        }

        $channelType = (int)($this->agentProcessParams['channel_type'] ?? 0);
        if ($this->sseResponse instanceof ProtobufWebsocketResponse) {
            // 适配App端
            if ($this->isForOtherStrategyReply) {
                $card = new ChatReplyOtherStrategyReplyCard(
                    enableWatermark: true,
                    referMessage: $this->isShowVisitorMessage() && isset($this->lastVisitorMessage)
                    ? ($this->lastVisitorMessage->messageBody?->getPromptText($channelType))
                    : ''
                );
            } else {
                $card = new ChatReplyCard(
                    enableWatermark: true,
                    referMessage: $this->isShowVisitorMessage() && isset($this->lastVisitorMessage)
                    ? ($this->lastVisitorMessage->messageBody?->getPromptText($channelType))
                    : ''
                );
            }
        } else {
            $card = new TranslateCard(
                enableWatermark: true,
                referMessage: $this->isShowVisitorMessage() && isset($this->lastVisitorMessage)
                    ? ($this->lastVisitorMessage->messageBody?->getPromptText($channelType))
                    : ''
            );
        }

        $card->setTitle(new IconText(text: \Yii::t('ai','辅助回复'), icon: Button::ICON_CHAT_REPLY));
        $card->setLeftFooter($leftFooter);
        $card->setRightFooter([
            new FeedBack(icon: 'feedback', event: 'feedback', params: ['record_id' => $aiAgentProcessResponse->recordId])
        ]);
        $card->setOutsideFooterLeft($outsideFooterLeft);
        $card->withRecordId($aiAgentProcessResponse->recordId);
        $card->withConversationId($this->conversationId);

        if ($this->sseResponse instanceof ProtobufWebsocketResponse) {
            // 适配App端
            if ($this->isForOtherStrategyReply) {
                return [AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_REPLY_OTHER_STRATEGY_REPLY_CARD => $card];
            }
            return [AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_REPLY_CARD => $card];
        }
        return [AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TRANSLATE_CARD => $card];
    }

    public function makeAgentProcessParams(): array
    {
        return $this->requestParams;
    }

    protected function getPromptTemplate(string $type, Collection $messages, string $question = ''): string
    {
        // 卡点识别
        if ($type === self::TYPE_STOCK) {
            $prompt = $this->userPromptMap['stock_identify'] ?? '';
            $recentMessages = $messages->slice(-10, 10);
            $text = ChatService::buildChatMessages($recentMessages, (int) $this->agentProcessParams['channel_type']);
            $text .= $this->buildLastMessageTail($messages);    // 加上时间判断
            return str_replace('{RecentConversations}', $text, $prompt);
        }

        // 引导回复
        if ($type === self::TYPE_GUIDE) {
            /* @var Message $lastMessage */
            $lastMessage = $messages->last();
            if ($lastMessage->sendType === Constants::FROM_TYPE_OKKI) {
                // last message from seller
                $prompt = $this->userPromptMap['guide.from_type_okki'] ?? '';
                $latestMessages = ChatService::getLatestMessage($messages, Constants::FROM_TYPE_OKKI);
            } else {
                // last message from buyer
                $prompt = $this->userPromptMap['guide.from_type_visitor'] ?? '';
                $latestMessages = ChatService::getLatestMessage($messages, Constants::FROM_TYPE_VISITOR);
            }
            $latestMessagePrompt = ChatService::buildChatMessages($latestMessages, (int) $this->agentProcessParams['channel_type']);

            $recentMessages = $messages->slice(-20, 20);
            $text = ChatService::buildChatMessages($recentMessages, (int) $this->agentProcessParams['channel_type']);
            $text .= $this->buildLastMessageTail($messages);    // 加上时间判断
            return str_replace(
                ['{Language}','{RecentConversations}', '{LatestSalesMessages}', '{LatestCustomerMessages}'],
                [$this->language,$text, $latestMessagePrompt, $latestMessagePrompt],
                $prompt
            );
        }

        // 客户挽回
        if ($type === self::TYPE_RETRIEVING) {
            $prompt = $this->userPromptMap['retrieving'] ?? '';
            $recentMessages = $messages->slice(-20, 20);
            $text = ChatService::buildChatMessages($recentMessages, (int) $this->agentProcessParams['channel_type']);
            $text .= $this->buildLastMessageTail($messages);    // 加上时间判断
            return str_replace(['{Language}','{RecentConversations}'], [$this->language,$text], $prompt);
        }

        // 客户再激活
        if ($type === self::TYPE_REACTIVE) {
            $prompt = $this->userPromptMap['reactive'] ?? '';
            $recentMessages = $messages->slice(-20, 20);
            $text = ChatService::buildChatMessages($recentMessages, (int) $this->agentProcessParams['channel_type']);
            $text .= $this->buildLastMessageTail($messages);    // 加上时间判断
            return str_replace(
                array('{Language}','{RecentConversations}', '{CurrentDate}'),
                array($this->language,$text, date('Y-m-d')),
                $prompt);
        }

        // 客户说服
        if ($type === self::TYPE_PERSUADE) {
            $prompt = $this->userPromptMap['persuade'] ?? '';
            $recentMessages = $messages->slice(-20, 20);
            $text = ChatService::buildChatMessages($recentMessages, (int) $this->agentProcessParams['channel_type']);
            $text .= $this->buildLastMessageTail($messages);    // 加上时间判断
            return str_replace(
                array(
                    '{Language}',
                    '{RecentConversations}',
                    '{CurrentDate}',
                    '{MainIssue}',
                    '{Stance}',
                    '{Feedback}'
                ),
                array(
                    $this->language,
                    $text,
                    date('Y-m-d'),
                    $this->stockPoint['issue'],
                    $this->stockPoint["stance"],
                    $this->stockPoint["feedback"]
                ),
                $prompt);
        }

        // 卡点解决
        if ($type === NewChatReplyAiAgent::TYPE_RESOLVE) {
            $prompt = $this->userPromptMap['resolve'] ?? '';
            $recentMessages = $messages->slice(-20, 20);
            $text = ChatService::buildChatMessages($recentMessages, (int) $this->agentProcessParams['channel_type']);
            $text .= $this->buildLastMessageTail($messages);    // 加上时间判断
            return str_replace(
                array(
                    '{Language}',
                    '{RecentConversations}',
                    '{MainIssue}',
                    '{ReferenceStrategy}',
                ),
                array(
                    $this->language,
                    $text,
                    $this->stockPoint['issue'],
                    (\Yii::app()->language ?? 'zh-CN') == 'zh-CN' ? self::STRATEGY_LISTS[$this->stockPoint['issue']][0]['text'] ?? '' : self::STRATEGY_ENG_LISTS[$this->stockPoint['issue']][0]['text'] ?? '',
                ),
                $prompt);
        }

        if ($type === self::TYPE_FOLLOW_UP) {   // 客户跟进模块
            $prompt = $this->userPromptMap['follow_up'] ?? '';
            $recentMessages = $messages->slice(-20, 20);
            $text = ChatService::buildChatMessages($recentMessages, (int) $this->agentProcessParams['channel_type']);
            $text .= $this->buildLastMessageTail($messages);    // 加上时间判断
            return str_replace(
                array('{Language}','{RecentConversations}'),
                array($this->language,$text),
                $prompt);
        }

        if ($type === self::TYPE_CHANGE_STRATEGY) {
            $prompt = $this->userPromptMap['change_strategy'] ?? '';
            $stockPoint = $this->getPreviousStock();

            $recentMessages = $messages->slice(-20, 20);
            $text = ChatService::buildChatMessages($recentMessages, (int) $this->agentProcessParams['channel_type']);
            $text .= $this->buildLastMessageTail($messages);    // 加上时间判断
            return str_replace(
                array(
                    '{Language}',
                    '{RecentConversations}',
                    '{MainIssue}',
                    '{ReferenceStrategy}',
                ),
                array(
                    $this->language,
                    $text,
                    $stockPoint['issue'] ?? '',
                    $this->question,
                ),
                $prompt);
        }

        if ($type === self::TYPE_CONTINUE_ASK) {
            $previousResult = $this->getPreviousResult();

            // TODO: 处理获取不到上一次回复的情况

            $prompt = $this->userPromptMap['continue_ask'] ?? '';

            $recentMessages = $messages->slice(-20, 20);
            $text = ChatService::buildChatMessages($recentMessages, (int) $this->agentProcessParams['channel_type']);
            $text .= $this->buildLastMessageTail($messages);    // 加上时间判断
            return str_replace(
                array(
                    '{Language}',
                    '{RecentConversations}',
                    '{OriginalReply}',
                    '{Feedback}',
                ),
                array(
                    $this->language,
                    $text,
                    $previousResult['回复内容'] ?? '',
                    $question,
                ),
                $prompt);
        }

        return '';
    }

    public function handleException(string $errorMsg, int $errorCode, array $context = []): AiAgentProcessResponse
    {
        $aiAgentProcessResponse = parent::handleException($errorMsg, $errorCode, $context);
        if ($errorCode === AiAgentException::ERR_CHAT_EXPIRED) {
            $aiAgentProcessResponse = new AiAgentProcessResponse();
            if ($this->sseResponse instanceof ProtobufWebsocketResponse) {
                // 适配App端
                $cardType = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_EXPIRED_ERROR_CARD;
            } else {
                $cardType = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CARD;
            }
            $aiAgentProcessResponse->setMessageType($cardType);
            $aiAgentProcessResponse->setFunction('ConversationOver');   /* @use self::getConversationOverSseObj() */
            $aiAgentProcessResponse->setAnswer($errorMsg);
            $aiAgentProcessResponse->setStream(false);
        }
        return $aiAgentProcessResponse;
    }

    public function getConversationOverSseObj(AiAgentProcessResponse $aiAgentProcessResponse): array
    {
        if ($this->sseResponse instanceof ProtobufWebsocketResponse) {
            $card = new ChatExpiredErrorCard(
                enableWatermark: false,
            );
        } else {
            $card = new Card(
                enableWatermark: false,
            );
        }
        $card->setLeftFooter([
            new IconText(text: \Yii::t('ai', '可以尝试使用AI生成后续沟通的建议')),
            new Button(text: \Yii::t('ai', '立即生成'), event: Button::EVENT_SET_SCENE_TYPE, params: [
                'sceneType' => PromptConstant::SCENE_TYPE_AI_SERVICE_SCENE_AI_CHAT_COACH,
                'options' => $this->agentProcessParams,
                'needCleanChatList' => true,
                'lastSceneType' => PromptConstant::SCENE_TYPE_AI_SERVICE_SCENE_AI_CHAT_REPLY
            ]),
        ]);
        $card->withRecordId($aiAgentProcessResponse->recordId);
        $card->withConversationId($this->conversationId);

        if ($this->sseResponse instanceof ProtobufWebsocketResponse) {
            return [
                AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_EXPIRED_ERROR_CARD => $card,
            ];
        }
        return [
            AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CARD => $card,
        ];
    }

    protected function returnStrategyList():  AiAgentProcessResponse
    {
        // 获取第一次调用的卡点信息
        $latestProcessRecord = $this->getProcessRecordByOrder('asc');
        $stockPoint = json_decode($latestProcessRecord['context'], true)['stockPoint'] ?? [];
        $stockPoint = array_merge($this->stockPoint, $stockPoint);

        // 根据卡点获取策略列表
        $lang = \Yii::app()->language ?? 'zh-CN';
        $strategyList = $lang == 'zh-CN' ? self::STRATEGY_LISTS[$stockPoint['issue']] ?? [] : self::STRATEGY_ENG_LISTS[$stockPoint['issue']] ?? [];
        if (empty($strategyList)) {
            $this->logAgentProcessInfo("根据卡点获取策略列表失败", [
                'stock_info' => $stockPoint
            ]);
        }

        if ($this->sseResponse instanceof ProtobufWebsocketResponse) {
            // 适配App端
            $cardType = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_REPLY_OTHER_STRATEGY_LIST_CARD;
        } else {
            $cardType = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_OPTIONS_CARD;
        }
        $response = $this->initAiAgentProcessResponse(
            [],
            __FUNCTION__,   /* @uses self::getReturnStrategyListSseObj()  */
            $cardType
        );
        $response->context = [
            'options' => $strategyList,
        ];

        // 返回策略列表后修改状态为成功
        $this->updateAiServiceProcessRecord($this->context['record_id'], ['status' => \AiServiceProcessModel::STATUS_SUCCESS]);
        return $response;
    }

    public function getReturnStrategyListSseObj(AiAgentProcessResponse $aiAgentProcessResponse): array
    {
        if ($this->sseResponse instanceof ProtobufWebsocketResponse) {
            // 适配App端
            $card = new ChatReplyOtherStrategyListCard();
        } else {
            $card = new OptionsCard();
        }
        $card->enableWatermark = false; // 没有“由AI”生成提示
        $card->setTitle(new IconText(icon: Button::ICON_CHAT_REPLY, text: \Yii::t('ai','辅助回复')));
        $card->setSubtitle(new IconText(text: \Yii::t('ai','其他回复策略')));
        $card->setOptions($aiAgentProcessResponse->context['options'] ?? []);
        if ($this->sseResponse instanceof ProtobufWebsocketResponse) {
            // 适配App端
            return [
                AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_REPLY_OTHER_STRATEGY_LIST_CARD => $card
            ];
        }
        return [
            AiAgentConstants::AI_AGENT_MESSAGE_TYPE_OPTIONS_CARD => $card
        ];
    }
    /**
     * 是否展示买家消息的逻辑
     */
    protected function isShowVisitorMessage(): bool
    {
        // 指定聊天消息时，需要展示
        if (!empty($this->agentProcessParams['anchor'])) {
            return true;
        }
        return false;
    }

    public function getPreMessageList(array $params)
    {
        if (!empty($params['params']['is_follow_suggestion'])) {
            return [];
        }

        return [
            [
                'role' => AiAgentConstants::AI_AGENT_ROLE_TYPE_USER,
                'message_type' => AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT,
                'context' => [
                    'content' => \Yii::t('ai', AiAgentConstants::AI_CHAT_REPLY_PRESET_MESSAGE),
                ],
                'continue_question' => true
            ],
        ];
    }

    /**
     * Desc : 辅助回复、沟通润色切换语言后重新生成内容
     *
     * @throws \common\library\ai_agent\exception\AiAgentException
     */
    public function retryByLanguage(array $params):array
    {
        $language = Helper::getLanguageMap()[$params['params']['language']] ?? 'English';

        // 存储用户切换的语言，下次使用辅助回复时候默认使用设置的语言
        Helper::saveLanguageSetting($params, $language,$this->clientId, $this->agentId,$this->userId);

        $prompt =  <<<PROMPT
# Task
Your task is to assist Chinese foreign trade salespeople by translating messages into {Language}.

# Original Message
{OriginalMessage}

# Your Translation
PROMPT;
        $historyId = $params['params']['history_id'] ?? '';
        $this->context['answer_history_id'] = $historyId;
        $agentConversationHistory = new \common\library\ai_service\AiAgentConversationHistory($this->clientId, $this->agentId, $historyId);
        $content = json_decode($agentConversationHistory->content,true);
        if(empty($content['content'])){
            return [];
        }

        $prompt = str_replace([
            '{Language}',
            '{OriginalMessage}'
        ], [
            $language,
            $content['content']
        ], $prompt);

        $this->systemPrompt = <<<PROMPT
You are a veteran cross-border B2B e-commerce expert with extensive experience in international trade between China and foreign countries.
PROMPT;
        $this->stream = false;
        $this->llmService = new AIClient();
        $this->llmService->setModel('azure-openai-gpt-4o');
        $response = $this->callLlm($prompt);
        $content['content'] = $response['answer'];
        $content['language'] = $params['params']['language'] ?? 'en';
        $agentConversationHistory->content = json_encode($content, JSON_UNESCAPED_UNICODE);
        $agentConversationHistory->save();
        $this->conversationId = 0;
        return $response;
    }

    /**
     * 跟进建议->生成回复话术
     */
    public function generateResponseTemplates($params)
    {
        $journeyId = $params['journey_id'];
        $detailId = $params['detail_id'];

        $language = empty($params['language']) ? 'en' : $params['language'];
        $language = Helper::getLanguageMap()[$language] ?? 'English';

        $aiPortraitAnalysisDetail = new AiPortraitAnalysisDetail($this->clientId, $detailId);

        // 多语言处理
        $responseStrategy = \Yii::t('ai', '回复策略');
        $followSuggestion = is_array($aiPortraitAnalysisDetail->data) ? $aiPortraitAnalysisDetail->data : json_decode($aiPortraitAnalysisDetail->data, true);
        if ($responseStrategy == '回复策略') {
            $this->followSuggestionExtInfo[$responseStrategy] = $followSuggestion['strategy']['cn'] ?? '';
        } else {
            $this->followSuggestionExtInfo[$responseStrategy] = $followSuggestion['strategy']['en'] ?? '';
        }

        list($recentCommunication, $lastMessages, $chatRecords, $lastFollowSuggestion) = \common\library\ai_agent\company_quality_check\Helper::getJourneyChat($this->clientId, $journeyId);

        $systemPrompt = $this->promptConfig['response_templates']['system_prompt'];
        $systemPrompt = str_replace('{{language}}', $language, $systemPrompt);

        $userPrompt = $this->promptConfig['response_templates']['user_prompt'];
        $userPrompt = str_replace('{{recentCommunication}}', $recentCommunication, $userPrompt);
        $userPrompt = str_replace('{{lastMessages}}', $lastMessages, $userPrompt);
        $userPrompt = str_replace('{{lastFollowSuggestion}}', $lastFollowSuggestion, $userPrompt);

        $this->systemPrompt = $systemPrompt;
        $this->llmService->setModel($this->promptConfig['response_templates']['model']);
        $this->llmService->setTemperature($this->promptConfig['response_templates']['temperature']);
        $response = $this->callLlm($userPrompt);

        if (isset($params['params']['switch_language_flag']) && $params['params']['switch_language_flag']) {
            return $this->initAiAgentProcessResponse($response, '', AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT);
        } else {
            return $this->initAiAgentProcessResponse($response, 'process', AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TRANSLATE_CARD);
        }
    }

}
