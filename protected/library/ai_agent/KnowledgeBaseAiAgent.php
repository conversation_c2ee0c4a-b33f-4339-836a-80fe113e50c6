<?php

namespace common\library\ai_agent;

use AiAgent;
use common\library\ai_agent\api\AIClient;
use common\library\ai_agent\api\AiStreamClient;
use common\library\ai_agent\exception\AiAgentException;
use common\library\ai_agent\jobs\KnowledgeBaseDataCleanJob;
use common\library\ai_agent\knowledge_base\DiskFileKnowledgeBaseList;
use common\library\ai_agent\knowledge_base\Helper as KnowledgeHelper;
use common\library\ai_agent\message\AbstractMessageFormat;
use common\library\ai_agent\message\component\Button;
use common\library\ai_agent\message\component\FeedBack;
use common\library\ai_agent\message\ReferCard;
use common\library\ai_agent\message\Text;
use common\library\ai_agent\record\AiServiceProcessRecord;
use common\library\ai_agent\vector\EmbeddingService;
use common\library\ai_agent\vector\VectorDocumentList;
use common\library\ai_agent\vector\VectorService;
use common\library\ai_agent\vector\VectorReferList;
use common\library\ai_agent\vector\VectorReferDetailList;
use common\library\ai_service\AiAgentConversationHistoryList;
use common\library\disk\FileFormatter;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\product_v2\ProductAPI;
use common\library\queue_v2\QueueService;
use common\models\vector\Vector;
use common\models\vector\VectorRefer;

class KnowledgeBaseAiAgent extends BaseAiAgent
{
    public const DEFAULT_ANSWER = '你好，知识库中没有找到可以回答该问题的相关信息，请去知识库中补充';
    public const NO_PRIV_DEFAULT_ANSWER = '你好，知识库中没有找到可以回答该问题的相关信息~请提醒管理员上传相关资料';

    private const DOCUMENT_SOURCE = 'document_source';
    private const PRODUCT_SOURCE = 'product_source';

    protected bool $stream = true;
    protected int $streamRiskBufferSize = 15;
    public int $maxHistoryLength = 0;

    private ?bool $onlyDocSource = null; // 是否只返回知识库的回答
    private ?bool $onlyProductSource = null; // 是否只返回产品库的回答
    public array $entities = []; // 针对提问提取的实体关键词
    /* 知识库问答相关 */
    public array $docDetails = []; // 文档向量化召回
    public array $entityDocDetails = []; // 文档实体召回
    public int $entityDocCount = 0; // 实体召回的文档数量
    public int $firstPickEntityDocId = 0; // 第一个实体召回的文档id
    public int $secondPickEntityDocId = 0; // 第二个实体召回的文档id
    protected array $referFileDetails = []; // 排序后的文档信息，用于封装prompt
    public string $docUserPrompt = ''; // 知识库问答封装的prompt
    public array $refers = []; // 引用的文档
    /* 产品库问答相关 */
    public array $productDetails = []; // 产品向量化召回
    public array $entityProductDetails = []; // 产品实体召回
    protected array $referProductDetails = []; // 排序后的产品信息，用于封装prompt
    public string $productUserPrompt = ''; // 产品库问答封装的prompt
    public array $referProducts = []; // 引用的产品
    /* 融合多方问答结果 */
    public string $finalPrompt = ''; // 最终封装的prompt

    private bool $canAnswer = true;
    private bool $docCanAnswer = true;
    private bool $productCanAnswer = true;
    protected String $outputText = ""; // 知识库此次回答已输出的文本
    protected String $hiddenText = ""; // 知识库此次回答已输出的文本(该变量的是已隐藏题注)
    protected array $numberMap = []; // 题注重排序map，key是原文本的数值，value是重排序后的数值

    private bool $skipProcessRecord = false;
    public array $debugInfo = [];

    private ?VectorService $vectorService = null;

    public function getAgentSceneType(): int
    {
        return AiAgent::AI_AGENT_SCENE_TYPE_KNOWLEDGE_BASE;
    }

    public function setSkipProcessRecord(bool $skip = true): void
    {
        $this->skipProcessRecord = $skip;
    }

    public function setStreamMode(bool $stream = true): void
    {
        $this->stream = $stream;
        // 改变流式模式时，重新初始化 LLM 请求服务
        $this->llmService = $this->stream ? new AiStreamClient() : new AIClient();
        $this->llmService->setPrompt($this->systemPrompt);
        $this->llmService->setModel($this->llmModel);
    }

    public function setOnlyDocSource(bool $onlyDocSource = true): void
    {
        $this->onlyDocSource = $onlyDocSource;
    }

    public function setOnlyProductSource(bool $onlyProductSource = true): void
    {
        $this->onlyProductSource = $onlyProductSource;
    }

    public function process(array $params = [], string $function = ''): AiAgentProcessResponse
    {
        // 判断某客户是否有知识库、产品库
        $knowledgeSource = $this->checkKnowledgeBaseSource();
        if (
            $knowledgeSource[self::DOCUMENT_SOURCE] === false &&
            $knowledgeSource[self::PRODUCT_SOURCE] === false
        ) {
            $privilegeService = PrivilegeService::getInstance($this->clientId);
            $errorCode = $privilegeService->hasPrivilege(PrivilegeConstants::PRIVILEGE_SETTING_AI_KNOWLEDGE_BASE_MANAGE) ?
                AiAgentException::ERR_KNOWLEDGE_NO_FILE : AiAgentException::ERR_KNOWLEDGE_NO_FILE_NOT_UPLOAD_PRIVILEGE;
            throw new AiAgentException(AiAgentException::ERROR_CODE_TO_USER_MSG[$errorCode], $errorCode);
        }

        $hasDocSource = $knowledgeSource[self::DOCUMENT_SOURCE] === true;
        $onlyDocSource = $this->onlyDocSource ?? $knowledgeSource[self::DOCUMENT_SOURCE] === true && $knowledgeSource[self::PRODUCT_SOURCE] === false;
        $hasProductSource = $knowledgeSource[self::PRODUCT_SOURCE] === true;
        $onlyProductSource = $this->onlyProductSource ?? $knowledgeSource[self::DOCUMENT_SOURCE] === false && $knowledgeSource[self::PRODUCT_SOURCE] === true;

        // 初始化向量服务
        $this->vectorService = new VectorService($this->clientId, $this->userId);
        $this->vectorService->callerScene = class_basename($this);
        $this->vectorService->traceData = [
            'record_id' => $this->context['record_id'] ?? 0
        ];

        // 提取提问中的实体关键词
        $this->entities = $this->extractEntityFromQuery($this->question);
        $this->addDebugInfo('entities', $this->entities);

        if ($hasDocSource) {
            // 执行知识库问答流程
            $docResponse = $this->processDocQA($onlyDocSource);
            if ($onlyDocSource) {
                // 直接流式返回知识库的回答
                /* @uses self::getProcessSseObj() */
                return $this->initAiAgentProcessResponse(
                    $docResponse,
                    __FUNCTION__,
                    AiAgentConstants::AI_AGENT_MESSAGE_TYPE_REFER_ANSWER,
                    stream: $this->stream
                );
            }
        }
        if ($hasProductSource) {
            // 执行产品库问答流程
            $productResponse = $this->processProductQA($onlyProductSource);
            if ($onlyProductSource) {
                // 直接流式返回产品库的回答
                /* @uses self::getProcessSseObj() */
                return $this->initAiAgentProcessResponse(
                    $productResponse,
                    __FUNCTION__,
                    AiAgentConstants::AI_AGENT_MESSAGE_TYPE_REFER_ANSWER,
                    stream: $this->stream
                );
            }
        }

        // 对知识库的回答和产品库的回答分别做有效性判断，判断依据：是否存在“对不起”等字样。
        // 如果知识库可以回答，但产品库不可以回答，那给用户直接返回知识库的答案；
        // 反之，给用户直接返回产品库的答案。如果两者都无法回答，则返回兜底话术。
        // 如果两者都可以回答，则返回综合后的答案。
        $docCanNotAnswer = preg_match('/对不起|无法回答|回答不了|不清楚|不知道/', $docResponse['answer']);
        $this->docCanAnswer = empty($docCanNotAnswer);
        $productCanNotAnswer = preg_match('/对不起|无法回答|回答不了|不清楚|不知道/', $productResponse['answer']);
        $this->productCanAnswer = empty($productCanNotAnswer);
        if ($this->docCanAnswer && !$this->productCanAnswer) {
            /* @uses self::getProcessSseObj() */
            return $this->initAiAgentProcessResponse(
                $docResponse,
                __FUNCTION__,
                AiAgentConstants::AI_AGENT_MESSAGE_TYPE_REFER_ANSWER,
                stream: false,
            );
        } elseif (!$this->docCanAnswer && $this->productCanAnswer) {
            /* @uses self::getProcessSseObj() */
            return $this->initAiAgentProcessResponse(
                $productResponse,
                __FUNCTION__,
                AiAgentConstants::AI_AGENT_MESSAGE_TYPE_REFER_ANSWER,
                stream: false,
            );
        } elseif (!$this->docCanAnswer && !$this->productCanAnswer) {
            /* @uses self::getProcessSseObj() */
            return $this->initAiAgentProcessResponse(
                $docResponse,
                __FUNCTION__,
                AiAgentConstants::AI_AGENT_MESSAGE_TYPE_REFER_ANSWER,
                stream: false,
                useDefaultAnswer: true,
                defaultAnswer: self::DEFAULT_ANSWER,
            );
        }

        $finalResponse = $this->processFinalAnswer($docResponse['answer'], $productResponse['answer']);
        /* @uses self::getProcessSseObj() */
        return $this->initAiAgentProcessResponse(
            $finalResponse,
            __FUNCTION__,
            AiAgentConstants::AI_AGENT_MESSAGE_TYPE_REFER_ANSWER,
            stream: $this->stream
        );
    }

    /**
     * 判断某客户是否有知识库、产品库
     * @return array<string, bool>
     */
    private function checkKnowledgeBaseSource(): array
    {
        return [
            self::DOCUMENT_SOURCE => $this->checkDocumentSource(),
            self::PRODUCT_SOURCE => $this->checkProductSource(),
        ];
    }

    /**
     * 判断某客户是否有知识库
     * @return bool
     */
    private function checkDocumentSource(): bool
    {
        $diskFileKnowledgeBaseListPdo = new DiskFileKnowledgeBaseList($this->clientId);
        $diskFileKnowledgeBaseListPdo->setAnalysisStatus(\DiskFileKnowledgeBase::STATUS_SUCCEED);
        $diskFileKnowledgeBaseListPdo->setEnableFlag(\DiskFileKnowledgeBase::ENABLE_FLAG_NORMAL);
        return $diskFileKnowledgeBaseListPdo->count() > 0;
    }

    /**
     * 判断某客户是否有产品库
     * @return bool
     */
    private function checkProductSource(): bool
    {
        $vectorReferListPdo = new VectorReferList($this->clientId);
        $vectorReferListPdo->setReferType(\Constants::TYPE_PRODUCT);
        $vectorReferListPdo->setAnalysisStatus(VectorRefer::STATUS_SUCCEED);
        $vectorReferListPdo->setEnableFlag(VectorRefer::ENABLE_FLAG_NORMAL);
        return $vectorReferListPdo->count() > 0;
    }

    /**
     * 从查询中提取实体
     * @param string $query 查询
     * @return array
     */
    public function extractEntityFromQuery(string $query): array
    {
        $prompt = $this->promptConfig['entity_extract']['user_prompt'] ?? '';
        $prompt = str_replace('{query}', $query, $prompt);
        
        $llmService = new AIClient();
        $llmService->setSystemPrompt($this->promptConfig['entity_extract']['system_prompt'] ?? 'You are a helpful assistant.');
        $llmService->setTemperature($this->promptConfig['entity_extract']['temperature'] ?? 0);
        $llmService->setQuestion($prompt);
        $llmService->setModel($this->promptConfig['entity_extract']['model'] ?? "azure-openai-gpt-4o-mini");
        $llmService->setResponseFormat(['type' => 'json_object']);
        $className = basename(str_replace('\\', '/', get_class($this)));
        $llmService->setTrace("OKKI_AI", $className);
        $llmResponse = $llmService->chatCompletions();
        $response = $llmService->convertResponse($llmResponse['data'] ?? []);

        if (!$this->skipProcessRecord) {
            // 记录实体提取结果到 ProcessRecord 表
            $aiProcessRecordPdo = new AiServiceProcessRecord($this->context['record_id']);
            $aiProcessRecordPdo->setContext([
                'entity_extract_response' => $response['answer']
            ]);
            $aiProcessRecordPdo->save();
        }

        $entities = [];
        if (trim($response['answer']) !== '') {
            $decoded = json_decode($response['answer'], true);
            if (json_last_error() == JSON_ERROR_NONE && !empty($decoded['entities'])) {
                $entities = $decoded['entities'];
            }
        }

        return $entities;
    }

    /**
     * 获取知识库的文档ID
     * @return array<int>
     */
    public function getKnowledgeBaseDocIds(): array
    {
        $diskFileKnowledgeBaseListPdo = new DiskFileKnowledgeBaseList($this->clientId);
        $diskFileKnowledgeBaseListPdo->setAnalysisStatus(\DiskFileKnowledgeBase::STATUS_SUCCEED);
        $diskFileKnowledgeBaseListPdo->setEnableFlag(\DiskFileKnowledgeBase::ENABLE_FLAG_NORMAL);
        $diskFileKnowledgeBaseListPdo->setFields('doc_id, disk_file_id');
        $rows = $diskFileKnowledgeBaseListPdo->find();
        return array_unique(array_column($rows, 'doc_id'));
    }

    /**
     * 执行知识库问答流程
     * @param bool $onlyDocSource 是否只返回知识库的回答
     * @return array
     */
    public function processDocQA(bool $onlyDocSource = false): array
    {
        $docIds = $this->getKnowledgeBaseDocIds();
        $detailChunkLimit = $this->promptConfig['detail_chunk_num'] ?? 40;

        // 向量化召回
        $beginTime = round(microtime(true) * 1000, 2);
        $this->docDetails = $this->vectorService->search(
            $this->question,
            $docIds,
            $detailChunkLimit,
            EmbeddingService::MODEL_TYPE_BGE_M3,
            "meta->>'type'='detail'"
        );
        $this->docDetails = array_filter($this->docDetails, function($detail) {
            return !empty(trim($detail->text));
        });
        $endTime = round(microtime(true) * 1000, 2);
        $this->addNodeCostTime('recallDocumentVectorDetails', '知识库向量化召回', $beginTime, $endTime);
        $this->addDebugInfo('docDetails', $this->docDetails);

        // 实体召回
        $beginTime = round(microtime(true) * 1000, 2);
        $entityDocIds = $this->processDocQAMatchEntity();
        $entityDocIds = $this->processDocQARankEntityMatchedDocs($entityDocIds);
        $this->entityDocDetails = $this->processDocQARecallEntityDetails($entityDocIds);
        $this->entityDocDetails = array_filter($this->entityDocDetails, function($detail) {
            return !empty(trim($detail->text));
        });
        $endTime = round(microtime(true) * 1000, 2);
        $this->addNodeCostTime('recallDocumentEntityMatchedVectorDetails', '知识库实体召回', $beginTime, $endTime);
        $this->addDebugInfo('entityDocDetails', $this->entityDocDetails);
        $this->addDebugInfo('entityDocIds', $entityDocIds);

        // 合并并排序向量化召回和实体召回的文档
        $this->referFileDetails = $this->processDocQAMergeDetails(
            $this->docDetails,
            $this->entityDocDetails,
            $this->entities,
            $this->entityDocCount
        );
        $this->addDebugInfo('referFileDetails', $this->referFileDetails);
        if (!$this->skipProcessRecord) {
            $this->updateAiProcessRecordContext($this->context['record_id'], $this->debugInfo);
        }

        $prompt = $this->processDocQABuildPrompt();
        $this->docUserPrompt = $prompt;
        if ($onlyDocSource) {
            if (!$this->skipProcessRecord) {
                $this->llmService->setTemperature($this->promptConfig['temperature'] ?? 0);
                $response = $this->callLlm($prompt, stream: $this->stream);
            } else {
                $llmService = new AIClient();
                $llmService->setSystemPrompt($this->systemPrompt);
                $llmService->setTemperature($this->promptConfig['temperature'] ?? 0);
                $llmService->setQuestion($prompt);
                $llmService->setModel($this->llmService->getModel());
                $className = basename(str_replace('\\', '/', get_class($this)));
                $llmService->setTrace("OKKI_AI", $className);
                $llmResponse = $llmService->chatCompletions();
                $response = $llmService->convertResponse($llmResponse['data'] ?? []);
            }
        } else {
            try {
                $llmService = new AIClient();
                $llmService->setSystemPrompt($this->systemPrompt);
                $llmService->setTemperature($this->promptConfig['temperature'] ?? 0);
                $llmService->setQuestion($prompt);
                $llmService->setModel($this->llmService->getModel());
                $className = basename(str_replace('\\', '/', get_class($this)));
                $llmService->setTrace("OKKI_AI", $className);
                $llmResponse = $llmService->chatCompletions();
                $response = $llmService->convertResponse($llmResponse['data'] ?? []);
            } catch (\Exception $exception) {
                throw new AiAgentException($exception->getMessage(), $exception->getCode(), null, []);
            }
        }
        return $response;
    }

    /**
     * 执行产品库问答流程
     * @param bool $onlyProductSource 是否只返回产品库的回答
     * @return array
     */
    public function processProductQA(bool $onlyProductSource = false): array
    {
        $detailChunkLimit = $this->promptConfig['refer_product']['detail_chunk_num'] ?? 100;
        $entityChunkLimit = $this->promptConfig['refer_product']['entity_chunk_num'] ?? 40;

        // 向量化召回
        $beginTime = round(microtime(true) * 1000, 2);
        $this->productDetails = $this->vectorService->searchRefer(
            $this->question,
            \Constants::TYPE_PRODUCT,
            $detailChunkLimit
        );
        $this->productDetails = array_filter($this->productDetails, function($detail) {
            return !empty(trim($detail->detailInfo));
        });
        // 过滤掉没有查看权限的产品和产品字段
        $this->productDetails = $this->processProductQAFilterCanNotViewProducts($this->productDetails);
        $productIds = array_column($this->productDetails, 'referId');
        $productIdToDetailMap = array_column($this->productDetails, null, 'referId');
        // 按照相似度分数降序排序后，取Top 10
        $this->productDetails = array_slice($this->productDetails, 0, 10);
        $endTime = round(microtime(true) * 1000, 2);
        $this->addNodeCostTime('recallProductVectorDetails', '产品库向量化召回', $beginTime, $endTime);
        $this->addDebugInfo('productDetails', $this->productDetails);

        // 实体召回
        $beginTime = round(microtime(true) * 1000, 2);
        $entityProductIds = $this->processProductQAMatchEntity($productIds);
        $entityProductIds = array_slice($entityProductIds, 0, $entityChunkLimit);
        $this->entityProductDetails = [];
        foreach ($entityProductIds as $productId) {
            if (isset($productIdToDetailMap[$productId])) {
                $this->entityProductDetails[] = $productIdToDetailMap[$productId];
            }
        }
        // 根据距离倒序排序
        usort($this->entityProductDetails, function ($a, $b) {
            return $a->distance <=> $b->distance;
        });
        // 按照相似度分数降序排序后，取Top 5
        $this->entityProductDetails = array_slice($this->entityProductDetails, 0, 5);
        $endTime = round(microtime(true) * 1000, 2);
        $this->addNodeCostTime('recallProductEntityMatchedVectorDetails', '产品库实体召回', $beginTime, $endTime);
        $this->addDebugInfo('entityProductDetails', $this->entityProductDetails);
        $this->addDebugInfo('entityProductIds', $entityProductIds);

        // 合并并排序向量化召回和实体召回的文档
        $this->referProductDetails = $this->processProductQAMergeDetails(
            $this->productDetails,
            $this->entityProductDetails,
            $this->entities,
        );
        $this->addDebugInfo('referProductDetails', array_values($this->referProductDetails));
        if (!$this->skipProcessRecord) {
            $this->updateAiProcessRecordContext($this->context['record_id'], $this->debugInfo);
        }

        $prompt = $this->processProductQABuildPrompt();
        $this->productUserPrompt = $prompt;
        // 不要通过 $this->llmService->setSystemPrompt() 设置，因为 $this->callLlm 内部会再次设置
        $this->systemPrompt = $this->promptConfig['refer_product']['system_prompt'] ?? 'You are a helpful assistant.';
        if ($onlyProductSource) {
            if (!$this->skipProcessRecord) {
                $this->llmService->setModel($this->promptConfig['refer_product']['model'] ?? "azure-openai-gpt-4o-mini");
                $this->llmService->setTemperature($this->promptConfig['refer_product']['temperature'] ?? 0);
                $response = $this->callLlm($prompt, stream: $this->stream);
            } else {
                $llmService = new AIClient();
                $llmService->setSystemPrompt($this->systemPrompt);
                $llmService->setTemperature($this->promptConfig['refer_product']['temperature'] ?? 0);
                $llmService->setQuestion($prompt);
                $llmService->setModel($this->promptConfig['refer_product']['model'] ?? "azure-openai-gpt-4o-mini");
                $className = basename(str_replace('\\', '/', get_class($this)));
                $llmService->setTrace("OKKI_AI", $className);
                $llmResponse = $llmService->chatCompletions();
                $response = $llmService->convertResponse($llmResponse['data'] ?? []);
            }
        } else {
            try {
                $llmService = new AIClient();
                $llmService->setSystemPrompt($this->systemPrompt);
                $llmService->setTemperature($this->promptConfig['refer_product']['temperature'] ?? 0);
                $llmService->setQuestion($prompt);
                $llmService->setModel($this->promptConfig['refer_product']['model'] ?? "azure-openai-gpt-4o-mini");
                $llmResponse = $llmService->chatCompletions();
                $className = basename(str_replace('\\', '/', get_class($this)));
                $llmService->setTrace("OKKI_AI", $className);
                $response = $llmService->convertResponse($llmResponse['data'] ?? []);
            } catch (\Exception $exception) {
                throw new AiAgentException($exception->getMessage(), $exception->getCode(), null, []);
            }
        }
        return $response;
    }

    private function processProductQAFilterCanNotViewProducts(array $productDetails): array
    {
        $productIds = array_column($productDetails, 'referId');
        $productIdToProductDetailMap = array_column($productDetails, null, 'referId');
        $skuIds = array_column($productDetails, 'referSubId');
        $productApi = new ProductAPI($this->clientId, $this->userId);
        $productInfoList = $productApi->getApiProductList(
            $this->clientId,
            $this->userId,
            $productIds,
            $skuIds,
        );

        $canViewProductDetails = [];
        foreach ($productInfoList as $idx => $productInfo) {
            if (empty($productInfo)) {
                // 该产品没有查看权限
                continue;
            }
            $productId = $productInfo['product_id'] ?? 0;
            if (empty($productId)) {
                // 无效的产品
                continue;
            }

            // 将原始的产品信息格式转换为算法侧的产品信息格式
            $productInfo = $this->processProductQAConvertProductInfo($productInfo);
            $detailInfo = json_decode($productIdToProductDetailMap[$productId]->detailInfo, true);
            $embeddingField = json_decode($detailInfo['embedding_field'], true);
            foreach ($embeddingField as $key => $value) {
                if (!isset($productInfo[$key])) {
                    // 该产品字段没有查看权限或该产品字段为空值
                    unset($embeddingField[$key]);
                }
            }
            if (empty($embeddingField)) {
                // 该产品所有字段都没有查看权限
                continue;
            }

            $detailInfo['embedding_field'] = json_encode($embeddingField);
            $productIdToProductDetailMap[$productId]->detailInfo = json_encode($detailInfo);
            $canViewProductDetails[] = $productIdToProductDetailMap[$productId];
        }

        return $canViewProductDetails;
    }

    private function processProductQAConvertProductInfo(array $productInfo): array
    {
        // 转换规则详见：https://xmkm.yuque.com/armee3/wg6g9f/gc6zcs4zkwhi3e1w
        if (isset($productInfo['product_no'])) {
            $productInfo['Product Number'] = $productInfo['product_no'];
            unset($productInfo['product_no']);
        }
        if (isset($productInfo['name'])) {
            $productInfo['Product Name'] = $productInfo['name'];
            unset($productInfo['name']);
        }
        if (isset($productInfo['cn_name'])) {
            $productInfo['Chinese Product Name'] = $productInfo['cn_name'];
            unset($productInfo['cn_name']);
        }
        if (isset($productInfo['model'])) {
            $productInfo['Product Model'] = $productInfo['model'];
            unset($productInfo['model']);
        }
        if (isset($productInfo['group_id'])) {
            $productInfo['Product Group'] = $productInfo['group_id'];
            unset($productInfo['group_id']);
        }
        if (isset($productInfo['description'])) {
            $productInfo['Product Description'] = $productInfo['description'];
            unset($productInfo['description']);
        }
        if (isset($productInfo['brand'])) {
            $productInfo['Product Brand'] = $productInfo['brand'];
            unset($productInfo['brand']);
        }
        if (isset($productInfo['product_remark'])) {
            $productInfo['Product Notes'] = $productInfo['product_remark'];
            unset($productInfo['product_remark']);
        }
        if (isset($productInfo['hs_code'])) {
            $productInfo['Customs Code（HS Code）'] = $productInfo['hs_code'];
            unset($productInfo['hs_code']);
        }
        if (isset($productInfo['customs_name'])) {
            $productInfo['Customs English Name'] = $productInfo['customs_name'];
            unset($productInfo['customs_name']);
        }
        if (isset($productInfo['customs_cn_name'])) {
            $productInfo['Customs Chinese Name'] = $productInfo['customs_cn_name'];
            unset($productInfo['customs_cn_name']);
        }
        if (isset($productInfo['quantity'])) {
            $productInfo['Minimum Order Quantity'] = $productInfo['quantity'];
            unset($productInfo['quantity']);
        }
        if (isset($productInfo['price_currency'])) {
            $priceCurrency = $productInfo['price_currency'];
            unset($productInfo['price_currency']);
        } else {
            $priceCurrency = '';
        }
        if (isset($productInfo['fob_price'])) {
            $productInfo['FOB Price'] = $productInfo['fob_price'] . ' ' . $priceCurrency;
            unset($productInfo['fob_price']);
        }
        if (isset($productInfo['price_min'])) {
            $productInfo['Minimum FOB Price'] = $productInfo['price_min'] . ' ' . $priceCurrency;
            unset($productInfo['price_min']);
        }
        if (isset($productInfo['price_max'])) {
            $productInfo['Maximum FOB Price'] = $productInfo['price_max'] . ' ' . $priceCurrency;
            unset($productInfo['price_max']);
        }
        if (isset($productInfo['cost_currency'])) {
            $costCurrency = $productInfo['cost_currency'];
            unset($productInfo['cost_currency']);
        } else {
            $costCurrency = '';
        }
        if (isset($productInfo['cost'])) {
            $productInfo['Cost Price with Tax'] = $productInfo['cost'] . ' ' . $costCurrency;
            unset($productInfo['cost']);
        }
        if (isset($productInfo['product_volume'])) {
            $productInfo['Product Volume (m³)'] = $productInfo['product_volume'];
            unset($productInfo['product_volume']);
        }
        if (isset($productInfo['product_net_weight'])) {
            $productInfo['Product Net Weight (kg)'] = $productInfo['product_net_weight'];
            unset($productInfo['product_net_weight']);
        }
        if (isset($productInfo['package_volume'])) {
            $productInfo['Package Volume (m³)'] = $productInfo['package_volume'];
            unset($productInfo['package_volume']);
        }
        if (isset($productInfo['package_gross_weight'])) {
            $productInfo['Package Gross Weight (kg)'] = $productInfo['package_gross_weight'];
            unset($productInfo['package_gross_weight']);
        }
        if (isset($productInfo['count_per_package'])) {
            $productInfo['Quantity per Package'] = $productInfo['count_per_package'];
            unset($productInfo['count_per_package']);
        }
        if (isset($productInfo['package_remark'])) {
            $productInfo['Package Description'] = $productInfo['package_remark'];
            unset($productInfo['package_remark']);
        }
        if (isset($productInfo['carton_volume'])) {
            $productInfo['Volume per carton (m³)'] = $productInfo['carton_volume'];
            unset($productInfo['carton_volume']);
        }
        if (isset($productInfo['carton_net_weight'])) {
            $productInfo['Net weight per carton (kg)'] = $productInfo['carton_net_weight'];
            unset($productInfo['carton_net_weight']);
        }
        if (isset($productInfo['carton_gross_weight'])) {
            $productInfo['Gross weight per carton (kg)'] = $productInfo['carton_gross_weight'];
            unset($productInfo['carton_gross_weight']);
        }
        if (isset($productInfo['count_per_carton'])) {
            $productInfo['Number of products per carton'] = $productInfo['count_per_carton'];
            unset($productInfo['count_per_carton']);
        }
        if (isset($productInfo['product_size_length']) && isset($productInfo['product_size_weight']) && isset($productInfo['product_size_height'])) {
            $l = $productInfo['product_size_length'];
            $w = $productInfo['product_size_weight'];
            $h = $productInfo['product_size_height'];
            $productInfo['Product size (length*width*height cm)'] = "$l x $w x $h";
            unset($productInfo['product_size_length']);
            unset($productInfo['product_size_weight']);
            unset($productInfo['product_size_height']);
        }
        if (isset($productInfo['package_size_length']) && isset($productInfo['package_size_weight']) && isset($productInfo['package_size_height'])) {
            $l = $productInfo['package_size_length'];
            $w = $productInfo['package_size_weight'];
            $h = $productInfo['package_size_height'];
            $productInfo['Package Dimensions (cm)'] = "$l x $w x $h";
            unset($productInfo['package_size_length']);
            unset($productInfo['package_size_weight']);
            unset($productInfo['package_size_height']);
        }
        if (isset($productInfo['carton_size_length']) && isset($productInfo['carton_size_weight']) && isset($productInfo['carton_size_height'])) {
            $l = $productInfo['carton_size_length'];
            $w = $productInfo['carton_size_weight'];
            $h = $productInfo['carton_size_height'];
            $productInfo['Single box size (length*width*height cm)'] = "$l x $w x $h";
            unset($productInfo['carton_size_length']);
            unset($productInfo['carton_size_weight']);
            unset($productInfo['carton_size_height']);
        }
        return $this->unsetEmptyValue($productInfo);
    }

    private function unsetEmptyValue(mixed $arr): array
    {
        if (!is_array($arr)) {
            return $arr;
        }
        foreach ($arr as $key => $value) {
            if (is_array($value)) {
                $arr[$key] = $this->unsetEmptyValue($value);
                if (empty($arr[$key])) {
                    unset($arr[$key]);
                }
            } elseif (empty($value)) {
                unset($arr[$key]);
            }
        }
        return $arr;
    }

    /**
     * 综合知识库和产品库的回答，生成最终答案
     * @param string $documentAnswer 知识库的回答
     * @param string $productAnswer 产品库的回答
     * @return array
     */
    public function processFinalAnswer(string $documentAnswer, string $productAnswer): array
    {
        $prompt = str_replace(
            ['{question}', '{rag_answer}', '{product_answer}'],
            [$this->question, $documentAnswer, $productAnswer],
            $this->promptConfig['final']['user_prompt']
        );
        $this->finalPrompt = $prompt;
        // 不要通过 $this->llmService->setSystemPrompt() 设置，因为 $this->callLlm 内部会再次设置
        $this->systemPrompt = $this->promptConfig['final']['system_prompt'] ?? 'You are a helpful assistant.';
        if (!$this->skipProcessRecord) {
            $this->llmService->setModel($this->promptConfig['final']['model'] ?? "azure-openai-gpt-4o-mini");
            $this->llmService->setTemperature($this->promptConfig['final']['temperature'] ?? 0);
            $response = $this->callLlm($prompt, stream: $this->stream);
        } else {
            $llmService = new AIClient();
            $llmService->setSystemPrompt($this->systemPrompt);
            $llmService->setTemperature($this->promptConfig['final']['temperature'] ?? 0);
            $llmService->setQuestion($prompt);
            $llmService->setModel($this->promptConfig['final']['model'] ?? "azure-openai-gpt-4o-mini");
            $className = basename(str_replace('\\', '/', get_class($this)));
            $llmService->setTrace("OKKI_AI", $className);
            $llmResponse = $llmService->chatCompletions();
            $response = $llmService->convertResponse($llmResponse['data'] ?? []);
        }
        return $response;
    }

    /**
     * 对知识库进行实体匹配
     * @return array
     */
    public function processDocQAMatchEntity(): array
    {
        if (empty($this->entities)) {
            return [];
        }

        // 有些实体可能是 GL3s'd-C-10 这种格式，导致拼出错误的 SQL 语句
        $entities = array_map(function($entity) {
            return \Util::escapeDoubleQuoteSql($entity);
        }, $this->entities);
        $entities = implode(",", array_map(function($item) { return "'".$item."'"; }, $entities));
        $sql = <<<SQL
SELECT tbl.doc_id
FROM tbl_vector_detail tbl
JOIN (SELECT unnest(ARRAY[$entities]) AS list_elem) el ON (tbl.summary_text ILIKE '%' || el.list_elem || '%')
WHERE tbl.refer_info->>'type' = 'meta' and client_id = $this->clientId
ORDER BY tbl.create_time DESC
LIMIT 10;
SQL;
        return Vector::model()->getDbConnection()->createCommand($sql)->queryColumn();
    }

    /**
     * 对产品库进行实体精确匹配
     * @param array $productIds 产品ID列表
     * @return array
     */
    public function processProductQAMatchEntity(array $productIds): array
    {
        if (empty($this->entities) || empty($productIds)) {
            return [];
        }

        $vectorReferDetailListPdo = new VectorReferDetailList($this->clientId);
        $vectorReferDetailListPdo->setReferIds($productIds);
        $vectorReferDetailListPdo->setReferType(\Constants::TYPE_PRODUCT);
        $vectorReferDetailListPdo->setFields('refer_id, detail_info');
        $rows = $vectorReferDetailListPdo->find();
        $productIdToDetailInfoMap = array_column($rows, 'detail_info', 'refer_id');

        $scoreList = [];
        foreach ($productIdToDetailInfoMap as $productId => $detailInfoStr) {
            foreach ($this->entities as $entity) {
                $detailInfo = json_decode($detailInfoStr, true);
                if (str_contains(strtolower($detailInfo['precise_match_field'] ?? ''), strtolower($entity))) {
                    if (!isset($scoreList[$productId])) {
                        $scoreList[$productId] = 0;
                    }
                    $scoreList[$productId] += 1;
                }
            }
        }
        arsort($scoreList);

        return array_keys($scoreList);
    }

    /**
     * 对知识库实体匹配的文档进行排序
     * @param array $entityDocIds 实体匹配的文档ID列表
     * @return array
     */
    public function processDocQARankEntityMatchedDocs(array $entityDocIds): array
    {
        if (empty($entityDocIds)) {
            return [];
        }

        $vectorDocumentPdo = new VectorDocumentList($this->clientId);
        $vectorDocumentPdo->setDocIds($entityDocIds);
        $vectorDocumentPdo->setFields("doc_id, doc_info");
        $documentList = $vectorDocumentPdo->find();
        $docIdToDocInfoMap = array_column($documentList, 'doc_info', 'doc_id');

        $scoreList = [];
        foreach ($docIdToDocInfoMap as $docId => $docInfoStr) {
            $scoreList[$docId] = 0;

            $docInfo = json_decode($docInfoStr, true);
            $fileMeta = $docInfo['file_meta'] ?? [];
            if (empty($fileMeta)) {
                continue;
            }
            
            $summaryText = strtolower($fileMeta['original_language_summary'] ?? '');
            $keyWords = $fileMeta['original_language_keywords'] ?? '';
            $keyWordsStr = strtolower(is_array($keyWords) ? implode(', ', $keyWords) : $keyWords);
            foreach ($this->entities as $entity) {
                $entity = strtolower($entity);
                if (str_contains($summaryText, $entity)) {
                    $scoreList[$docId] += 1.0;
                }
                if (str_contains($keyWordsStr, $entity)) {
                    $scoreList[$docId] += 0.5;
                }
            }
        }
        arsort($scoreList);

        return array_keys($scoreList);
    }

    /**
     * 对知识库实体匹配的文档进行向量化分块召回
     * @param array $entityDocIds 实体匹配的文档ID列表
     * @return array
     */
    public function processDocQARecallEntityDetails(array $entityDocIds): array
    {
        if (empty($entityDocIds)) {
            return [];
        }

        $this->entityDocCount = count($entityDocIds);
        // 指定文档id，进行向量化分块召回
        // 召回策略：
        //   1. 返回 1 个候选文档：召回 20 个分块
        //   2. 返回 2 个候选文档，每个文档召回 10 个分块
        //   3. 返回 3 个及以上候选文档
        //      3.1 前 2 个文档各召回 10 个分块
        //      3.2 后面的文档，共召回 10 个分块
        $entityChunks = ($this->entityDocCount === 1) ? 20 : 10;

        $entityDetails = [];
        $entityDocIds2D = [];
        $entityDocIds2D[] = array_slice($entityDocIds, 0, 1);
        $entityDocIds2D[] = array_slice($entityDocIds, 1, 1);
        $entityDocIds2D[] = array_slice($entityDocIds, 2);
        foreach ($entityDocIds2D as $idx => $_entityDocIds) {
            if (empty($_entityDocIds)) {
                continue;
            }
            if ($idx === 0) {
                $this->firstPickEntityDocId = $_entityDocIds[0];
            } elseif ($idx === 1) {
                $this->secondPickEntityDocId = $_entityDocIds[0];
            }
            $entityDetails = array_merge($entityDetails, $this->vectorService->search($this->question, $_entityDocIds, $entityChunks, EmbeddingService::MODEL_TYPE_BGE_M3, "meta->>'type'='detail'"));
        }
        $entityDetails = array_filter($entityDetails, function($detail) {
            return !empty(trim($detail->text));
        });

        return $entityDetails;
    }

    /**
     * 对知识库实体召回的文档和向量化召回的文档进行合并
     * @param array $chunkDetails 向量化召回的文档
     * @param array $entityDetails 实体召回的文档
     * @param array $entities 实体列表
     * @param int $entityDocCount 实体召回的文档数量
     * @return array
     */
    public function processDocQAMergeDetails(
        array $chunkDetails,
        array $entityDetails,
        array $entities,
        int $entityDocCount
    ): array
    {
        // 为每个 detail 添加 source 标识
        foreach ($entityDetails as $index => $detail) {
            $entityDetails[$index]->source = 'entity';
        }
        foreach ($chunkDetails as $index => $detail) {
            $chunkDetails[$index]->source = 'chunk';
        }
        // 合并两个列表
        $allDetails = array_merge($entityDetails, $chunkDetails);

        // 修改分数
        foreach ($allDetails as $index => $detail) {
            $detail->score = 1 - ($detail->distance ?? 1);
            $weight_score = 1.0;
            if ($detail->source == 'entity') {
                $weight_score += 0.4;
            }

            $text = !empty($detail->text) ? strtolower($detail->text) : '';
            foreach ($entities as $entity) {
                $docBreakFlag = false;
                $textBreakFlag = false;

                /** @var string $entity */
                if (!$docBreakFlag && stripos($detail->document->doc_name, $entity)) {
                    $weight_score += 0.3;
                    $docBreakFlag = true;
                }

                if (!$textBreakFlag && stripos($text, strtolower($entity))) {
                    $weight_score += 0.2;
                    $textBreakFlag = true;
                }

                if($docBreakFlag && $textBreakFlag){
                    break;
                }
            }

            $allDetails[$index]->score *= $weight_score;
        }

        // 先按照 score 降序排序，再把相同 score 但属于 entity 类的 detail 放在前面
        $sortFunc = function($a, $b) {
            if ($a->score == $b->score) {
                return strcmp($b->source, $a->source);
            }
            return $b->score - $a->score;
        };
        usort($allDetails, $sortFunc);

        // 按照 text 字段去重，保留每个唯一 text 的第一个结果，优先保留 entity
        $unique_details = [];
        foreach ($allDetails as $detail) {
            $text = $detail->text ?? '';
            if (!isset($unique_details[$text])) {
                $unique_details[$text] = $detail;
            }
        }
        $details = array_values($unique_details);
        usort($details, $sortFunc);

        $detailsWithScore = [];
        foreach ($details as $detail) {
            $detailsWithScore[] = ['score' => $detail->score, 'detail' => $detail];
        }

        $this->addDebugInfo('docDetailsWithScore', $detailsWithScore);
        return $this->processDocQASortAndGroupChunks($detailsWithScore, $entityDocCount);
    }

    private function processDocQAPickAndGroupChunks(
        array $detailsWithScore,
        int $entityDocCount
    ): array
    {
        // 实体召回文档按文档ID进行分组聚合，组内的分块按照分数倒续排序
        $detailsForEntityDoc1 = [];
        $detailsForEntityDoc2 = [];
        $detailsForEntityDoc3toN = [];
        // 非实体召回文档的所有分块聚集到一起，按照分数倒续排序
        $detailsForChunk = [];
        foreach ($detailsWithScore as $item) {
            $detail = $item['detail'];

            if ($detail->source === 'chunk') {
                $detailsForChunk[] = $detail;
                continue;
            }

            if ($detail->docId === $this->firstPickEntityDocId) {
                $detailsForEntityDoc1[] = $detail;
            } elseif ($detail->docId === $this->secondPickEntityDocId) {
                $detailsForEntityDoc2[] = $detail;
            } else {
                $detailsForEntityDoc3toN[] = $detail;
            }
        }

        // 截断策略
        $pickedDocuments = [];
        $maxTotalChunks = $this->promptConfig['max_total_chunks'] ?? 15;
        $pick = 0;
        if ($entityDocCount === 1) {
            // 如果只有一个实体召回文档，则从该文档中选取至多10个分块
            $pickedDocuments[$this->firstPickEntityDocId] = array_slice($detailsForEntityDoc1, 0, 10);
            $pick += count($pickedDocuments[$this->firstPickEntityDocId]);
        } elseif ($entityDocCount >= 2) {
            // 否则，优先从候选文档1中选取至多5个分块，再从候选文档2中选取至多5个分块
            $pickedDocuments[$this->firstPickEntityDocId] = array_slice($detailsForEntityDoc1, 0, 5);
            $pick += count($pickedDocuments[$this->firstPickEntityDocId]);
            $pickedDocuments[$this->secondPickEntityDocId] = array_slice($detailsForEntityDoc2, 0, 5);
            $pick += count($pickedDocuments[$this->secondPickEntityDocId]);   
        }
        if ($pick < $maxTotalChunks) {
            // 再从候选文档{3...N}中选取至多5个分块
            $detailsForEntityDoc3toN = array_slice($detailsForEntityDoc3toN, 0, 5);
            foreach ($detailsForEntityDoc3toN as $idx => $detail) {
                $pickedDocuments[$detail->docId][] = $detail;
            }
            $pick += count($detailsForEntityDoc3toN);
        }
        if ($pick < $maxTotalChunks) {
            // 如果总分块数不足15个，则从 $detailsForChunk 中选取至多5个分块
            $detailsForChunk = array_slice($detailsForChunk, 0, $maxTotalChunks - $pick);
            foreach ($detailsForChunk as $idx => $detail) {
                $pickedDocuments[$detail->docId][] = $detail;
            }
        }

        return $pickedDocuments;
    }

    private function processDocQASortChunksByGroupScore(array $documents): array {
        // 对截断选取并分组后的文档分块再次按照组内分块最高分数进行降序排序
        $sortedDocuments = [];
        foreach ($documents as $docId => $details) {
            $maxScore = 0;
            foreach ($details as $detail) {
                $maxScore = max($maxScore, $detail->score);
            }
            $sortedDocuments[$docId] = $maxScore;
        }
        arsort($sortedDocuments);
        $pickedDocuments = [];
        foreach ($sortedDocuments as $docId => $score) {
            $pickedDocuments[$docId] = $documents[$docId];
        }
        return $pickedDocuments;
    }

    private function processDocQASortChunksByDocIndex(array $documents): array {
        // 对每一个文档，按照分块在文档中出现的顺序进行升序排序
        foreach ($documents as $docId => $details) {
            usort($details, static function ($a, $b) {
                $indexA = $a->referInfo['index'] ?? 0;
                $indexB = $b->referInfo['index'] ?? 0;
                if ($indexA === $indexB) {
                    return 0;
                }
                return ($indexA > $indexB) ? 1 : -1;
            });
            $documents[$docId] = $details;
        }
        return $documents;
    }

    private function processDocQASortAndGroupChunks(array $detailsWithScore, int $entityDocCount): array
    {
        // 根据分数倒续排序
        usort($detailsWithScore, static function ($a, $b) {
            if ($a['score'] === $b['score']) {
                return 0;
            }
            return ($a['score'] < $b['score']) ? 1 : -1;
        });

        $pickedDocuments = $this->processDocQAPickAndGroupChunks($detailsWithScore, $entityDocCount);
        $pickedDocuments = $this->processDocQASortChunksByGroupScore($pickedDocuments);
        $pickedDocuments = $this->processDocQASortChunksByDocIndex($pickedDocuments);

        return $pickedDocuments;
    }

    /**
     * 对实体库实体召回的文档和向量化召回的文档进行合并
     * @param array $chunkDetails 向量化召回的文档
     * @param array $entityDetails 实体召回的文档
     * @param array $entities 实体列表
     * @return array
     */
    public function processProductQAMergeDetails(
        array $chunkDetails,
        array $entityDetails,
        array $entities,
    ): array
    {
        // 为每个 detail 添加 source 标识
        foreach ($entityDetails as $idx => $detail) {
            $entityDetails[$idx]->source = 'entity';
        }
        foreach ($chunkDetails as $idx => $detail) {
            $chunkDetails[$idx]->source = 'chunk';
        }
        $allDetails = array_merge($entityDetails, $chunkDetails);

        // 修改分数
        foreach ($allDetails as $idx => $detail) {
            $detail->score = 1 - ($detail->distance ?? 1);
            $weight = 1.0;
            // 如果属于实体召回，增加 0.5 的权重
            if ($detail->source == 'entity') {
                $weight += 0.5;
            }
            // 如果文本中包含任何实体词，增加 0.3 的权重
            foreach ($entities as $entity) {
                $detailInfo = json_decode($detail->detailInfo, true);
                if (str_contains(strtolower($detailInfo['embedding_field'] ?? ''), strtolower($entity))) {
                    $weight += 0.3;
                    break;
                }
            }
            $allDetails[$idx]->score *= $weight;
        }

        // 先按照 score 降序排序，再把相同 score 但属于 entity 类的 detail 放在前面
        $sortFunc = function($a, $b) {
            if ($a->score == $b->score) {
                return strcmp($b->source, $a->source);
            }
            return $b->score - $a->score;
        };
        usort($allDetails, $sortFunc);

        // 按照 detailInfo 字段去重，保留每个唯一 detailInfo 的第一个结果，优先保留 entity
        $uniqueDetails = [];
        foreach ($allDetails as $detail) {
            $text = $detail->detailInfo ?? '';
            if (!isset($uniqueDetails[$text])) {
                $uniqueDetails[$text] = $detail;
            }
        }
        $details = array_values($uniqueDetails);
        usort($details, $sortFunc);

        return $details;
    }

    private function processDocQAFormatDocContent($content): string
    {
        if (empty($content)) {
            return "No content available.";
        }

        $sections = $content;
        $formattedContent = "";
        $citationNumber = 1;

        foreach ($sections as $section) {
            if (trim($section)) {
                $lines = explode("\n", $section, 3);

                // 提取文件名
                preg_match('/文件名：(.*?) 标题：/', $lines[0], $matches);
                $fileName = isset($matches[1]) && $matches[1] != "" ? trim($matches[1]) : "Unknown file";

                // 提取标题
                preg_match('/标题：(.*?) 文档类型：/', $lines[0], $matches);
                $title = isset($matches[1]) && $matches[1] != "" ? trim($matches[1]) : "Unknown title";

                $formattedSection = "[[$citationNumber]] File: $fileName\n";
                $formattedSection .= "Title: $title\n";

                if (count($lines) > 1) {
                    $formattedSection .= trim($lines[1])."\n";
                    if (count($lines) > 2) {
                        $formattedSection .= trim($lines[2])."\n";
                    }
                }

                $formattedContent .= $formattedSection."\n";
                $citationNumber++;
            }
        }

        return trim($formattedContent);
    }

    /**
     * 构建知识库问答的prompt
     * @return string
     */
    public function processDocQABuildPrompt(): string
    {
        $formattedFiles = [];
        foreach ($this->referFileDetails as $details) {
            /* @var array<VectorDetail> $details */
            if (empty($details)) {
                continue;
            }
            $firstDetail = $details[0];
            $fileMeta = json_decode($firstDetail->document->doc_info, true)['file_meta'] ?? [];
            $title = $fileMeta['标题'] ?? '';
            $docType = $fileMeta['文档类型'] ?? '';
            $topic = $fileMeta['主要主题'] ?? '';
            $identifiers = $fileMeta['相关标识符'] ? (is_array($fileMeta['相关标识符']) ? implode(', ', $fileMeta['相关标识符']) : $fileMeta['相关标识符']) :'';

            $formattedFile = <<<TEXT
文件名：{$firstDetail->document->doc_name} 标题：$title 文档类型：$docType 主要主题：$topic 相关标识符：$identifiers
TEXT;
            foreach ($details as $i => $detail) {
                $n = $i + 1;
                $detailText = mb_substr($detail->text, 0, 4096);
                $formattedFile .= "\n对应分块$n: $detailText";
            }

            $formattedFiles[] = $formattedFile;
        }

        $formattedFiles =  $this->processDocQAFormatDocContent($formattedFiles);
        return str_replace(
            ['{referDocs}', '{question}'],
            [$formattedFiles, $this->question],
            $this->userPrompt
        );
    }

    private function processProductQAFormatProductContent(array $formattedProducts): string
    {
        if (empty($formattedProducts)) {
            return "No content available.";
        }
        $formattedContent = "";
        foreach ($formattedProducts as $formattedProduct) {
            $formattedContent = $formattedContent . $formattedProduct."\n";
        }
        return $formattedContent;
    }

    /**
     * 构建产品库问答的prompt
     * @return string
     */
    public function processProductQABuildPrompt(): string
    {
        $formattedProducts = [];
        foreach ($this->referProductDetails as $idx => $detail) {
            /* @var VectorReferDetail $detail */
            $num = $idx + 1;

            $detailInfo = json_decode($detail->detailInfo, true);
            try {
                $productInfo = json_decode($detailInfo['embedding_field'] ?? '', true);
                $productNumber = $productInfo['Product Number'] ?? 0;
            } catch (\Exception) {
                continue;
            }

            if (empty($productNumber)) {
                $formattedProduct = <<<TEXT
[[$num]] Product ID: {$detail->referId}
Corresponding Product Information\n
TEXT;
            } else {
                $formattedProduct = <<<TEXT
[[$num]] Product Number: {$productNumber}
Corresponding Product Information\n
TEXT;
                unset($productInfo['Product Number']);
            }

            foreach ($productInfo as $key => $value) {
                $formattedProduct .= "    $key: $value\n";
            }

            $formattedProducts[] = $formattedProduct;
        }

        $formattedContent = $this->processProductQAFormatProductContent($formattedProducts);
        return str_replace(
            ['{query}', '{formatted_results}'],
            [$this->question, $formattedContent],
            $this->promptConfig['refer_product']['user_prompt']
        );
    }

    public function makeAgentProcessParams(): array
    {
        return [];
    }

    /**
     * 封装引用的文档
     */
    public function loadReferDocs(): void
    {
        if (!empty($this->refers)) {
            // 已经加载过引用文档，不再加载
            return;
        }
        if (empty($this->referFileDetails)) {
            // 没有召回文档，不用返回引用文档
            return;
        }
        if (!$this->docCanAnswer) {
            // 文档知识库无法回答问题，不用返回引用文档
            return;
        }

        $fileIds = [];
        foreach ($this->referFileDetails as $details) {
            if (empty($details)) {
                continue;
            }
            $detail = $details[0];
            $doc = $detail->document;
            $docInfo = json_decode($doc->doc_info, true);
            if (!empty($docInfo['file_id']) && !in_array($docInfo['file_id'], $fileIds, true)) {
                $fileIds[] = $docInfo['file_id'];
            }
        }
        if (empty($fileIds)) {
            return;
        }

        $fileIdStr = implode(',', $fileIds);
        $diskFiles = \DiskFile::model()->findAll("file_id in ($fileIdStr)");
        $fileIdToDiskFileMap = array_column($diskFiles, null, 'file_id');

        $refers = [];
        $docIds = []; // 用于文档去重
        $n = 1; // 文档引用编号
        foreach ($this->referFileDetails as  $details) {
            if (empty($details)) {
                continue;
            }

            $detail = $details[0];
            $doc = $detail->document;
            if (in_array($doc->doc_id, $docIds, true)) {
                continue;
            }
            $docIds[] = $doc->doc_id;
            $docInfo = json_decode($doc->doc_info, true);
            $fileId = $docInfo['file_id'] ?? 0;
            if (isset($fileIdToDiskFileMap[$fileId]) && $fileIdToDiskFileMap[$fileId] instanceof \DiskFile) {
                $formatter = new FileFormatter();
                $previewUrl = $formatter->getPreviewUrl($fileIdToDiskFileMap[$fileId]->upload_file_id, $fileIdToDiskFileMap[$fileId]->file_name);
            } else {
                $previewUrl = $docInfo['file_url'];
            }

            $refers[] = [
                'refer_no' => $n++, // 引用编号从1开始
                'file_id' => $docInfo['file_id'] ?? 0,
                'file_name' => $doc->doc_name,
                'file_url' => $docInfo['file_url'] ?? '',
                'previewer_url' =>$previewUrl,
                'file_size' => $docInfo['file_size'] ?? 0,
                'file_ext' => \FileUtil::getFileExt($doc->doc_name)
            ];
        }
        if (!$this->canAnswer) {
            $this->refers = $refers;
            return;
        }

        // // 重排序引用文档
        // $sortedRefers = [];
        // foreach ($this->numberMap as $key => $value) {
        //     $filterRefer = null;
        //     foreach ($refers as $refer) {
        //         if ($refer['refer_no'] == $key) {
        //             $filterRefer = $refer;
        //             $filterRefer['refer_no'] = $value;
        //             break;
        //         }
        //     }
        //     if (empty($filterRefer)) {
        //         continue;
        //     }
        //     $sortedRefers[] = $filterRefer;
        // }

        $this->refers = $refers;
        $this->addDebugInfo('refers', $this->refers);
    }

    /**
     * 封装引用的产品
     */
    public function loadReferProducts(): void
    {
        if (!empty($this->referProducts)) {
            // 已经加载过引用产品，不再加载
            return;
        }
        if (empty($this->referProductDetails)) {
            // 没有召回产品，不用返回引用产品
            return;
        }
        if (!$this->productCanAnswer) {
            // 产品知识库无法回答问题，不用返回引用产品
            return;
        }

        $refers = [];
        foreach ($this->referProductDetails as $idx => $detail) {
            $detailInfo = json_decode($detail->detailInfo, true);
            $extInfo = json_decode($detailInfo['ext_field'], true);
            $refers[] = [
                'refer_no' => $idx + 1, // 引用编号从1开始
                'product_id' => $detail->referId,
                'product_no' => $extInfo['refer_no'] ?? 0,
                'product_name' => $extInfo['refer_name'] ?? '',
                'product_image_url' => $extInfo['refer_image_url'] ?? ''
            ];
        }

        $this->referProducts = $refers;
        $this->addDebugInfo('referProducts', $this->referProducts);
    }

    /**
     * @param string $question
     * @return array{answer:string, generator:Generator, recordId:int, historyId:int}
     * @throws AiAgentException
     */
    public function callLlm(string $question, bool $stream = true): array
    {
        $className = basename(str_replace('\\', '/', get_class($this)));
        $this->llmService->setClientId($this->clientId);
        $this->llmService->setUserId($this->userId);
        $traceData = array_merge($this->context, [
            'agentName' => $this->agentName,
        ]);
        $this->llmService->setTrace("OKKI_AI", $className, $traceData);
        if($stream) {
            return $this->callLlmStream($question);
        } else {
            return $this->callLlmSync($question);
        }
    }

    /**
     * @param $response array{answer:string, generator:Generator, recordId:int, historyId:int}
     * @param string $function
     * @param string $messageType
     * @param bool $stream
     * @param bool $useDefaultAnswer
     * @param string $defaultAnswer
     * @return AiAgentProcessResponse
     */
    public function initAiAgentProcessResponse(
        array $response,
        string $function,
        string $messageType,
        bool $stream = true,
        bool $useDefaultAnswer = false,
        string $defaultAnswer = '',
    ): AiAgentProcessResponse
    {
        $aiAgentProcessResponse = new AiAgentProcessResponse();
        if (!$stream && $useDefaultAnswer) {
            $aiAgentProcessResponse->setAnswer($defaultAnswer);
            $aiAgentProcessResponse->setGenerator(null);
        } else {
            if (!$stream) {
                $response['answer'] = $this->beforeReturnSyncAnswer($response['answer'] ?? '');
            }
            $aiAgentProcessResponse->setAnswer($response['answer'] ?? '');
            $aiAgentProcessResponse->setGenerator($response['generator'] ?? null);
        }
        $aiAgentProcessResponse->setRecordId($response['recordId'] ?? 0);
        $aiAgentProcessResponse->setHistoryId($response['historyId'] ?? 0);
        $aiAgentProcessResponse->setFunction($function);
        $aiAgentProcessResponse->setStream($stream);
        $aiAgentProcessResponse->setMessageType($messageType);

        return $aiAgentProcessResponse;
    }

    public function onReceiveStream(
        int $i,
        AbstractMessageFormat $message,
        string $str,
        AiAgentProcessResponse $response,
        string $fullText
    ): void
    {
        if ($i === 0) {
            $initMsg = $message->getSkeletonMessage();
            if (!empty($initMsg)) {
                $this->sseResponse->writeJson($initMsg);
            }
            $this->timeLog['sse_begin'] = microtime(true);
        }

        // 生成3个字符前暂不返回
        if (mb_strlen($fullText) < 3){
            return;
        }
        // 截取 $fullText 前三个字符，如果为“对不起”则返回兜底话术
        if (mb_substr($fullText, 0, 3) === '对不起') {
            if ($this->canAnswer) {
                $privilegeService = \common\library\privilege_v3\PrivilegeService::getInstance($this->clientId);
                $defaultAnswer = $privilegeService->hasPrivilege(\common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_SETTING_AI_KNOWLEDGE_BASE_MANAGE) ? self::DEFAULT_ANSWER : self::NO_PRIV_DEFAULT_ANSWER;
                $defaultAnswer = \Yii::t('ai', $defaultAnswer);
                $this->hiddenText = $defaultAnswer;
                $result = [
                    'context' => [
                        'content' => $defaultAnswer
                    ],
                    'status' => 1
                ];
                $this->sseResponse->writeJson($result);
                $this->canAnswer = false;
            }
            return;
        }

        // 正则匹配 [[数字]]，取出数字在 $this->numberMap 中对应的 value 回填进 $fullText
        $pattern = "/\[\[\d+\]\]/";
        $fullText = preg_replace_callback($pattern, function($matches) {
            $key = trim($matches[0], '[]');
            return '[[' . ($this->numberMap[$key] ?? $key) . ']]';
        }, $fullText);
        // 比较 $fullText 与 $this->ouputText，取出 $fullText 中比 $this->outputText 多出的部分，若两者长度相当，则代表未返回新生成的文字，直接提出
        if (strlen($fullText) - strlen($this->outputText) <= 0) {
            return;
        }
        // 去除所有 [、[[、[[数字、[[数字、[[数字] 的情况，这个正则不会匹配 [[数字]]
        $fullText = preg_replace('/\[{1,2}(\d+)?\]?$|^\[\[\d+\]$/', '', $fullText);
        $result = substr($fullText, strlen($this->outputText));
        if (empty($result)) {
            return;
        }

        // 将所有新生成的题注进行重排序加入到 $this->numberMap 中，并回填到 $fullText 中
        preg_replace_callback($pattern, function($matches) {
            $key = trim($matches[0], '[]');

            // 找出所有value中的最大值，从maxValue+1 开始赋值
            $values = array_values($this->numberMap);
            $maxValue = !empty($values) ? max($values) : 0;
            if (!in_array($key, $this->numberMap)) {
                $maxValue = $maxValue + 1;
                $this->numberMap[$key] = $maxValue;
                return '[[' . $maxValue . ']]';
            }
        }, $result);
        $this->outputText = $this->outputText . $result;

        // 隐藏所有题注 ，下一期不隐藏后删除此段代码
        $result = preg_replace($pattern, '', $result);
        if (empty($result)) {
            return;
        }
        $this->hiddenText = $this->hiddenText . $result;

        $responseFormat = [
            'context' => [
                'content' => $result
            ],
            'status' => AiAgentConstants::MESSAGE_STATUS_PROCESSING
        ];
        $this->sseResponse->writeJson($responseFormat);
    }

    /**
     * 发送同步调用结果前调用
     * @param string $answer
     * @return string
     */
    protected function beforeReturnSyncAnswer(string $answer): string
    {
        // 截取 $answer 前三个字符，如果为“对不起”则返回兜底话术
        if (mb_substr($answer, 0, 3) === '对不起') {
            $privilegeService = \common\library\privilege_v3\PrivilegeService::getInstance($this->clientId);
            $defaultAnswer = $privilegeService->hasPrivilege(\common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_SETTING_AI_KNOWLEDGE_BASE_MANAGE) ? self::DEFAULT_ANSWER : self::NO_PRIV_DEFAULT_ANSWER;
            $defaultAnswer = \Yii::t('ai', $defaultAnswer);
            $this->hiddenText = $defaultAnswer;
            $this->canAnswer = false;
            return $defaultAnswer;
        }

        // 去除所有 [、[[、[[数字、[[数字、[[数字] 的情况，这个正则不会匹配 [[数字]]
        $answer = preg_replace('/\[{1,2}(\d+)?\]?$|^\[\[\d+\]$/', '', $answer);
        // 隐藏所有题注
        $answer = preg_replace("/\[\[\d+\]\]/", '', $answer);
        return $answer;
    }

    /**
     * 发送 closeMessage 前调用
     * @param AiAgentProcessResponse $response
     * @param string $fullText gpt返回的完整字符串
     * @return void
     */
    protected function beforeCloseMessage(
        AiAgentProcessResponse $response,
        string $fullText
    ): void
    {
        $messages = $this->getMessageObj($response);
        $card = $messages[$response->messageType];
        if ($card instanceof ReferCard) {
            // 加载引用文档
            $this->loadReferDocs();
            $card->withRefers($this->refers);
            // 加载引用产品
            $this->loadReferProducts();
            $card->withReferProducts($this->referProducts);

            $skeletonMessage = $card->getSkeletonMessage();
            $skeletonMessage['status'] = AiAgentConstants::MESSAGE_STATUS_PROCESSING;
            $skeletonMessage['context']['content'] = '';
            $this->sseResponse->writeJson($skeletonMessage);
        }

        $response->answer = $this->hiddenText;
    }

    /**
     * 获取处理知识库问答小卡片
     * @param AiAgentProcessResponse $aiAgentProcessResponse
     * @return array
     */
    public function getProcessSseObj(AiAgentProcessResponse $aiAgentProcessResponse): array
    {
        $card = new ReferCard();
        $card->leftFooter = [
            new Button(icon: 'copy', event: Button::EVENT_COPY, params: ['content' => '']),
        ];
        $card->rightFooter = [
            new FeedBack(icon: 'feedback', event: 'okki_ai_feedback', params: ['record_id' => $aiAgentProcessResponse->recordId]),
        ];
        $card->withRecordId($aiAgentProcessResponse->recordId);
        $card->withConversationId($this->conversationId);
        if (!$aiAgentProcessResponse->stream) {
            // beforeCloseMessage 只会在流式输出场景调用，所以在这里需要加载引用文档和引用产品
            $this->loadReferDocs();
            $this->loadReferProducts();
        }
        $card->withRefers($this->refers);
        $card->withReferProducts($this->referProducts);

        $text = new Text();
        $text->withRecordId($aiAgentProcessResponse->recordId);
        $text->withConversationId($this->conversationId);

        return [
            AiAgentConstants::AI_AGENT_MESSAGE_TYPE_REFER_ANSWER => $card,
            AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT => $text,
        ];
    }

    /**
     * 获取预设知识库问题
     * @param array $params 请求参数
     * @return array
     */
    public function getPreMessageList(array $params): array
    {
        // 假如存在历史聊天记录，则不返回预设信息
        $conversationHistoryListPdo = new AiAgentConversationHistoryList($this->clientId, $this->userId, $this->agentId);
        $conversationHistoryListPdo->setLimit(1);
        $historyCnt = $conversationHistoryListPdo->count();

        $isExistFile = \DiskFile::checkHasKnowledgeFile($this->clientId, \DiskFile::KNOWLEDGE_FLAG_TURE) ? 1 : 0;
        if ($historyCnt >= 1 && $isExistFile === 1) {
            return [];
        }

        $preMessageList = KnowledgeHelper::getExampleQuestions($this->clientId);
        if (empty($preMessageList)) {
            $preMessageList = [
                \Yii::t('ai', '我们有什么产品？'),
                \Yii::t('ai', '我们的产品优势是？'),
            ];
        }

        $list = [];
        foreach ($preMessageList as $preMessage)
        {
            $list[] = [
                'event_name' => AiAgentConstants::FE_EVENT_OKKI_AI_SEND_CHAT_CONTENT_FOR_RAG,
                'event_params' => [
                    'content' => $preMessage
                ],
                'name' => $preMessage,
            ];
        }

        return [
            [
                'role' => AiAgentConstants::AI_AGENT_ROLE_TYPE_SYSTEM,
                'message_type' => AiAgentConstants::AI_AGENT_MESSAGE_TYPE_PRE_MESSAGE_KNOWLEDGE,
                'is_exist_file' => $isExistFile, // 是否存在知识库文件
                'context' => [
                    'list' => $list,
                    'title' => [
                        'text' => \Yii::t('ai', '今天能帮你些什么？')
                    ]
                ]
            ]
        ];
    }

    private function addDebugInfo(string $key, mixed $debugInfo): void
    {
        $this->debugInfo[$key] = $debugInfo;
    }

    private function logInfo(string $message, array $fields): void
    {
        \LogUtil::info('KnowledgeBaseAiAgent ' . $message, $fields);
    }

    public static function BatchRemoveProductRelatedData(
        int $clientId,
        int $userId,
        array $productIds = [],
        array $skuIds = []
    )
    {
        if (empty($productIds) && empty($skuIds)) {
            throw new \Exception('产品ID和SKU ID不能同时为空');
        }
        if (!empty($productIds) && !empty($skuIds)) {
            throw new \Exception('产品ID和SKU ID不能同时传入');
        }
        // 异步删除产品相关数据
        $job = new KnowledgeBaseDataCleanJob($clientId, $userId, $productIds, $skuIds);
        QueueService::dispatch($job);
    }
}
