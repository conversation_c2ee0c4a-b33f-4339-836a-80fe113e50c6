<?php

namespace common\library\ai_agent\sql_handle;

use common\library\account\Client;
use common\library\ai_agent\AiAgentConstants;
use common\library\ai_agent\exception\AiAgentException;
use common\library\ai_agent\GenerateDataSqlAiAgent;
use common\library\ai_agent\Helper as AgentHelper;
use common\library\ai_agent\parser\SQLParserService;
use common\library\ai_agent\sql_handle\Helper as SqlHelper;
use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldList;
use common\library\customer\CompanyList;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeField;
use common\library\privilege_v3\UserPrivilegeService;
use common\library\setting\item\ItemSettingConstant;
use common\library\statistics\foundation\store\ReportGroupCache;
use Constants;
use common\library\workflow\WorkflowConstant;

abstract class BaseProcessor
{
    public string $sql;
    public string $originSql;

    public string $question;

    public array $parsed;

    public SQLParserService $service;

    protected SQLParserService $originService;

    public int $questionType;
    public int $originQuestionType;

    public array $data;

    public string $chartType;
    public array $redirectInfo = [];

    public array $aiSwarmList = [];

    public int $userId;
    public int $clientId;
    public string $withSql = '';

    //相关对象主键信息
    public array $referIdList = [];

    const CUT_OFF_SUBTREE_FUNCTION = [
        Constant::GENERATE_DATA_RETURN_STATISTIC_AGG_CASE_WHEN => 'reconstructCaseWhenSubTree',
        Constant::GENERATE_DATA_RETURN_STATISTIC_AGG_FILTER => 'reconstructFilterSubTree',
    ];

    // 定义需要处理的字段
    const DEAL_VALUE_MAP = [
        'create_user_name' => [
            'function' => 'strtolower'
        ],
        'user_name' => [
            'function' => 'strtolower'
        ],
        'last_owner_name' => [
            'function' => 'strtolower'
        ],
        'handler_name' => [
            'function' => 'strtolower'
        ],
        'main_user_name' => [
            'function' => 'strtolower'
        ],
        'users_name' => [
            'function' => 'strtolower'
        ],
    ];



    public function __construct(string $sql, int $questionType)
    {
        $user = \User::getLoginUser();
        $this->clientId = $user->getClientId();
        $this->userId = $user->getUserId();
        $this->sql = $sql;
        $this->originSql = $sql;
        $this->service = new SQLParserService($sql);
        $this->parsed = $this->service->parsed;
        $this->originService = new SQLParserService($sql);
        $this->questionType = $questionType;
        $this->originQuestionType = $questionType;
    }

    abstract public function buildChartConfig();

    abstract public function getCharts();

    abstract public function checkAiSwarmInit();

    abstract public function checkViewDetailComponent();

    abstract public function checkHighLight($field);


    public function buildJumpUrlData(): array
    {
        $data = $this->data;
        $fieldTableRelationMap = array_column($this->originService->buildFieldTableRelation(), null, 'alias');
        $primaryKeyList = $res = $allIds = [];
        $field2CompanyIdMap = [];
        $allCompanyIds = [];
        $userId = \User::getLoginUser()->getUserId();
        $clientId = \User::getLoginUser()->getClientId();

        foreach ($data as $index => $fieldInfo) {
            foreach ($fieldInfo as $field => $value) {
                if (str_contains($field, Constant::FIELD_SUFFIX)) {
                    $prefix = explode(Constant::FIELD_SUFFIX, $field);
                    $primaryKeyList[$prefix[0]] = $value;
                    continue;
                }

                // field是别名
                $field = strtolower($field);
                $reportUniqueKey = '';

                if (!empty($fieldTableRelationMap[$field])) {
                    $ids = $primaryKeyList[$field] ?? '';
                    $idsArr = array_values(array_unique(array_filter(explode(',', $ids))));
                    $allIds = array_slice(array_merge($allIds, $idsArr), 0, 1000);
                    $table = $fieldTableRelationMap[$field]['table'];
                    $this->checkHighLight($field) && $reportUniqueKey = $this->creatReportItemUniqueKey($ids, $table, $clientId, $userId);
                    $referList = AgentHelper::getTableKeyInfo($table, 'refer_list');

                    // 获取company信息
                    if ($table == 'tbl_company') {
                        $field2CompanyIdMap[$index][$field] = $idsArr;
                        $allCompanyIds = array_unique(array_merge($allCompanyIds, $idsArr));
                    }

                    // 若是对象字段需要跳转到详情页
                    $objectNames = AgentHelper::getTableKeyInfo($table, 'object_name') ?: [];
                    $referDetail  = in_array($field, $objectNames) || (in_array($fieldTableRelationMap[$field]['field'], $objectNames) && $fieldTableRelationMap[$field]['expr_type'] == 'colref') ? AgentHelper::getTableKeyInfo($table, 'refer_detail') : '';

                    // 校验是否满足【查看详情】组件
                    if ($this->checkViewDetailComponent() && $index == (count($data) - 1)) {
                        $this->redirectInfo[$referList] = ['refer_list' => $referList, 'report_item_unique_key' => $this->creatReportItemUniqueKey($allIds, $table, $clientId, $userId)];
                    }
                }

                $res[$index][$field]['value'] = $value;
                $res[$index][$field]['field'] = $field;
                $res[$index][$field]['report_item_unique_key'] = $this->isZero($value) ? '' : $reportUniqueKey;
                $res[$index][$field]['refer_list'] = $referList ?? '';
                $res[$index][$field]['refer_detail'] = $referDetail ?? '';
                $res[$index][$field]['id'] = empty($referDetail) ? '' : implode(',', array_unique(explode(',', $ids)));

                if (!isset(AiAgentConstants::FIELD_NAME_MAP[$field]) && !(preg_match('/[\x{4e00}-\x{9fa5}]+/u', $field))) {
                    unset($res[$index][$field]);
                }
            }
        }

        // 以下是区分公私海客户的逻辑，让用户准确跳到公/私海列表
        if ($allCompanyIds) {

            $privateCompanyList = array_column($this->getObjectInfos($allCompanyIds, ['userNum' => [1,2]],\Constants::TYPE_COMPANY), 'company_id');
            $privateTotalNUms = count($privateCompanyList);
            $publicTotalNUms = count(array_unique($allCompanyIds)) - $privateTotalNUms;

            foreach ($field2CompanyIdMap as $index => $item) {

                foreach ($item as $field => $companyIds) {

                    $res[$index][$field]['refer_msg'] = ['public' => 0, 'private' => 0];

                    if (empty($companyIds)) continue;

                    $privateCompanyNums = count(array_intersect($privateCompanyList, $companyIds));
                    $publicCompanyNums = count($companyIds) - $privateCompanyNums;


                    $res[$index][$field]['refer_msg'] = ['public' => $publicCompanyNums, 'private' => $privateCompanyNums];
                }
            }
            $this->checkViewDetailComponent() && $this->redirectInfo[AiAgentConstants::GenerateDataCompanyList]['refer_msg'] = ['public' => $publicTotalNUms, 'private' => $privateTotalNUms];
            $this->referIdList = [\Constants::TYPE_COMPANY => $allCompanyIds];
        }

        return $res;
    }

    public function setSql($sql)
    {
        $this->sql = $sql;
        $this->service = new SQLParserService($sql);
        $this->parsed = $this->service->parsed;
        return $this;
    }

    public function setWithSql($withSql)
    {
        $this->withSql = $withSql;
    }

    /**
     * 校验是否设置过ai客群
     * @return int
     */
    public function checkAiSwarm()
    {
        if (!$this->checkAiSwarmInit()) {

            return Constant::AI_SWARM_TYPE_NOT_SUPPORT;
        }

        // 查询公海客户不支持设为客群
        if ($this->originService->checkWhereValue($this->originService->parsed, 'user_name', WorkflowConstant::FILTER_OPERATOR_EQUAL, '{}')) {

            return Constant::AI_SWARM_TYPE_NOT_SUPPORT;
        }

        if ($this->aiSwarmList) {
            $swarmList = $this->aiSwarmList;
        }else {
            $api = \common\library\swarm\SwarmService::getSwarmApi(ItemSettingConstant::ITEM_TYPE_SWARM_AI, $this->clientId, $this->userId);
            $swarmList = $api->swarmList();
        }
        $swarmList = array_column($swarmList, null, 'swarm_name');
        return !empty($swarmList[$this->question]) ? Constant::AI_SWARM_TYPE_ALREADY_SETTING : Constant::AI_SWARM_TYPE_SUPPORT;
    }


    public function getObjectInfos(array $referIds, $params, $module = \Constants::TYPE_COMPANY)
    {
        $objectInfos = [];

        if (empty($referIds)) return [];

        switch ($module) {
            case \Constants::TYPE_COMPANY:
                $companyList = new CompanyList($this->userId);

                if (!empty($params['userNum'])) {
                    $companyList->setUserNum($params['userNum']);
                }

                $companyList->setCompanyIds($referIds);
                $companyList->showAll(true);
                $companyList->setFields(['company_id', 'name']);
                $objectInfos = $companyList->find();
                break;
            default:
                break;
        }

        return $objectInfos;
    }

    public function setAiSwarmList($list)
    {
        $this->aiSwarmList = $list;
        return $this;
    }

    public function getPrimaryKeyName($tableName)
    {
        $tableInfos = array_column($this->getTables(), null, 'table');
        $tableInfo = $tableInfos[$tableName] ?? '';
        $primaryKey = AgentHelper::getTableKeyInfo($tableName, 'primary_key');
        if (!empty($tableInfo['alias'])) {
            $primaryKey = str_replace($tableName, $tableInfo['alias'], $primaryKey);
        }
        return $primaryKey;
    }

    public function addPrimaryKey(string $primaryKey, string $primaryKeyAlis, int $skipType, $index = null): void
    {

        switch ($skipType) {
            case Constant::SKIP_TYPE_NORMAL:
                $this->addNormalPrimaryKey($primaryKey, $primaryKeyAlis);
                break;
            case Constant::SKIP_TYPE_STRING_AGG:
                $this->addStringAggPrimaryKey($primaryKey, $primaryKeyAlis);
                break;
            case Constant::SKIP_TYPE_FILTER_AGG:
                $function = self::CUT_OFF_SUBTREE_FUNCTION[$this->questionType] ?? '';
                // 预期之外 不进行处理
                if (empty($function)) break;
                $subtree = $this->originService->reconstructSubTree($function, $index);
                $this->addFilterAggPrimaryKey($primaryKey, $primaryKeyAlis, subtree: $subtree);
                break;
        }
    }


    public function setQuestion($question)
    {
        $this->question = $question;
        return $this;
    }

    public function setData($data)
    {
        $this->data = $data;
        return $this;
    }


    public function getFields($sql): array
    {
        $service = new SQLParserService($sql);
        return $service->fields(true);
    }

    public function convertFieldName($dataKey)
    {
        $name = $dataKey;

        // 数据表字段名进行转译
        $name = AiAgentConstants::FIELD_NAME_MAP[$dataKey] ?? $name;

        return $name;
    }

    public function covertCurrencyLabel($dataKey): string
    {
        $currency = $this->getFieldCurrency($dataKey);
        return AiAgentConstants::CURRENCY_LABEL_MAP[$currency] ?? '';
    }

    /**
     * 根据字段判断哪些字段需要加币种标识
     * @param $dataKey
     * @return mixed
     */
    public function getFieldCurrency($dataKey): mixed
    {
        $user = \User::getLoginUser();
        $mainCurrency = '';
        $fields = array_column($this->originService->fields(true), null, 'alias');

        // 金额字段需要新增单位
        if (isset($fields[$dataKey])) {
            $amountField = $fields[$dataKey]['base'] ?? '';
            $amountFieldAlias = $fields[$dataKey]['alias'] ?? '';
            (in_array($amountField, AiAgentConstants::ARRAY_AMOUNT_FIELDS)
            || in_array($amountFieldAlias, AiAgentConstants::ARRAY_AMOUNT_FIELDS)
            || ((str_contains($dataKey, '金额') || str_contains($dataKey, '均价') || str_contains($dataKey, '销售额')) && !str_contains($dataKey, '数')))
            && $mainCurrency = \common\library\account\Client::getClient($user->getClientId())->getMainCurrency();
        }

        return $mainCurrency;
    }

    public function getDefaultChartConfig(array $charts): array
    {
        $companyIds = $this->referIdList[\Constants::TYPE_COMPANY] ?? [];
        $companyInfos = $this->getObjectInfos($companyIds, ['userNum' => [0,1,2]], \Constants::TYPE_COMPANY);

        return [
            'code' => 0,
            'desc' => $this->question,
            'question_type' => $this->questionType,
            'chatFlag' => true,
            'charType' => $charts,
            'fieldInfo' => [],
            'XAxis' => [],
            'YAxis' => [],
            'series' => [],
            'aiSwarmFlag' => $this->checkAiSwarm(),
            'companyInfos' => $companyInfos
        ];
    }

    public function buildJumpUrlSql(array $referTable = []): string
    {
        foreach ($this->originService->buildFieldTableRelation() as $index => $tableInfo) {
            $table = $tableInfo['table'];
            $tableAlias = $tableInfo['tableAlias'];
            $alias = $tableInfo['alias'];
            //指定仅支持跳转哪张表
            if (!empty($referTable) && !in_array($table, $referTable)) continue;

            $primaryKey = AgentHelper::getTableKeyInfo($table, 'primary_key');
            $primaryKeyAlias = $alias . Constant::FIELD_SUFFIX;

            if (empty($primaryKey)) continue;

            if (!empty($tableAlias)) {
                // 别名情况对agg进行替换
                $primaryKey = str_replace($table, $tableAlias, $primaryKey);
            }

            $this->addPrimaryKey($primaryKey, $primaryKeyAlias, $this->getSelectType($index), $index);
        }

        return $this->sql;
    }

    public function getTables(): array
    {
        $tables = [];
        foreach ($this->originService->parsed['FROM'] ?? [] as $fromInfo) {

            $alias = $fromInfo['alias']['name'] ?? '';
            $table = $fromInfo['table'] ?? '';

            if (empty($table)) continue;

            $tables[] = [
                'alias' => $alias,
                'table' => $table
            ];

        }

        if ($this->withSql) {
            $tablesFromWithSql = (new SQLParserService($this->withSql))->getTablesFromWithSql();
            $tables = array_merge($tables, $tablesFromWithSql);
        }

        return array_values(array_column($tables, null, 'table'));
    }

    public function addNormalPrimaryKey(string $primaryKey, string $primaryKeyAlias): void
    {
        $select = $this->parsed['SELECT'];

        $addPrimaryKeyInfo = $this->service->PrimaryKeyConstruction($primaryKey, $primaryKeyAlias);

        if (isset($select[0]) && $select[0]['expr_type'] == 'reserved' && $select[0]['base_expr'] == 'DISTINCT') {
            $this->parsed["SELECT"] = array_merge([array_shift($select)], [$addPrimaryKeyInfo], $select);
        } else {
            $this->parsed["SELECT"] = array_merge([$addPrimaryKeyInfo], $select);
        }
        $this->service->parsed = $this->parsed;
        $this->sql = $this->service->create();
    }

    public function addStringAggPrimaryKey($primaryKey, $primaryKeyAlias): void
    {
        $select = $this->parsed['SELECT'];
        $addPrimaryKeyInfo = $this->service->stringAggPrimaryKeyConstruction($primaryKey, $primaryKeyAlias);

        if (isset($select[0]) && $select[0]['expr_type'] == 'reserved' && $select[0]['base_expr'] == 'DISTINCT') {
            $this->parsed["SELECT"] = array_merge([array_shift($select)], [$addPrimaryKeyInfo], $select);
        } else {
            $this->parsed["SELECT"] = array_merge([$addPrimaryKeyInfo], $select);
        }

        $this->service->parsed = $this->parsed;
        $this->sql = $this->service->create();
    }

    public function addFilterAggPrimaryKey($primaryKey, $primaryKeyAlias, $subtree): void
    {
        $select = $this->parsed['SELECT'];
        $addPrimaryKeyInfo = $this->service->FilterPrimaryKeyConstruction($primaryKey, $primaryKeyAlias, $subtree);

        if (isset($select[0]) && $select[0]['expr_type'] == 'reserved' && $select[0]['base_expr'] == 'DISTINCT') {
            $this->parsed["SELECT"] = array_merge([array_shift($select)], [$addPrimaryKeyInfo], $select);
        } else {
            $this->parsed["SELECT"] = array_merge([$addPrimaryKeyInfo], $select);
        }

        $this->service->parsed = $this->parsed;
        $this->sql = $this->service->create();
    }

    public function getSelectType($index = null): int
    {
        // 因为需要根据每一字段的查询条件取组装，因此每一个select都需要判断
        $select = !empty($this->originService->parsed['SELECT'][$index]) ? [$this->originService->parsed['SELECT'][$index]] : [];
        // 这里的判断顺序不能改，需要从大范围判断到小范围
        $type = Constant::SKIP_TYPE_NORMAL;
        ($this->originService->haveAggregationFunction($select) || $this->haveGroup()) && $type = Constant::SKIP_TYPE_STRING_AGG;
        $this->originService->haveFilterCondition($select) && $type = Constant::SKIP_TYPE_FILTER_AGG;
        $this->originService->haveCaseWhenCondition($select) && $type = Constant::SKIP_TYPE_FILTER_AGG;

        return $type;
    }

    public function haveGroup(): bool
    {
        return !empty($this->parsed['GROUP']);
    }

    public function creatReportItemUniqueKey($ids, $table, $clientId, $userId)
    {

        if (empty($ids)) return '';
        $ids = is_array($ids) ? implode(',', $ids) : $ids;

        $uniqueKeyPre = "{$clientId}:{$userId}:";
        $uniqueKey = $uniqueKeyPre . md5($ids);
        $reportKey = AgentHelper::getTableKeyInfo($table, 'report_key');
        $reportGroupCache = new ReportGroupCache($userId);
        $reportGroupCache->setReportKey($reportKey);
        $reportGroupCache->setReportUniqueKey($reportKey);
        $groupData[$uniqueKey] = explode(',', $ids);

        !empty($groupData) && $cacheGroupMap = $reportGroupCache->setData($groupData);
        return $cacheGroupMap[$uniqueKey] ?? '';
    }

    function isZero($value): bool
    {
        // value是空字符串/0
        if (empty($value)) return true;
        // value是0.00
        if (is_numeric($value)) return floatval($value) == 0;

        return false;
    }

    protected function getReferTypeList()
    {
        $referTypeList = [];
        $table = $this->getTables();
        foreach ($table as $item) {
            $referType = AgentHelper::getTableKeyInfo($item['table'], 'refer_type');
            !empty($referType) && $referTypeList[] = $referType;
        }
        return array_unique($referTypeList);
    }

    public function getExtraFields()
    {
        $fieldInfos = $extraFields = [];
        foreach ($this->getReferTypeList() as $referType) {
            $fieldQuery = new FieldList($this->clientId);
            $fieldQuery->setType($referType);
            $fieldQuery->setBase(0);
            $fieldQuery->setIsList(0);
            $fieldQuery->setExcludeFieldType([\common\library\custom_field\CustomFieldService::FIELD_TYPE_IMAGE,\common\library\custom_field\CustomFieldService::FIELD_TYPE_ATTACH]);
            $fieldQuery->setDisableFlag(0);
            $fieldQuery->setFields(['id', 'name', 'field_type','default']);
            $data = $fieldQuery->find();
            $fieldInfos = array_merge($fieldInfos, $data);
        }
        $fieldInfos = array_column($fieldInfos,null,'id');

        foreach ($fieldInfos as $id => $fieldInfo) {
            $fieldName = "external_field_data_{$id}";

            if ($fieldInfo['field_type'] == \common\library\custom_field\CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT) {
                $fieldName = "external_field_data_array_{$id}";
            }
            $extraFields[] =  [
                'id' => $id,
                'field_name' => $fieldName,
                'field_type' => $fieldInfo['field_type']
            ];;
        }
        return $extraFields;
    }

    public function handleCustomField($sql = '')
    {
        $sql = $sql ?: $this->sql;

        foreach ($this->getExtraFields() as $extraField)
        {
            if (str_contains($sql, $extraField['field_name']))
            {
                switch ($extraField['field_type']) {
                    case \common\library\custom_field\CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT:
                        $field = " external_field_data_array -> '{$extraField['id']}' ";
                        break;
                    default:
                        $field = " external_field_data ->> '{$extraField['id']}' ";
                        break;
                }
                $sql = str_replace($extraField['field_name'], $field, $sql);
            }
        }
        $this->sql = $sql;

        return $sql;
    }

    public function buildPermissionSql($tables, $sql)
    {
        $userId = $this->userId;
        $clientId = $this->clientId;
        $getPermissionUserIds = function ($data) use (&$getPermissionUserIds) {
            $userIds = [];

            // 如果是第一层，直接获取 member 中的 user_id
            if (isset($data['member'])) {
                $userIds = array_column($data['member'], 'user_id');
            }

            // 如果存在 node，递归获取 node 下的 member 中的 user_id
            if (isset($data['node']))
            {
                foreach ($data['node'] as $node) {
                    $userIds = array_merge($userIds, $getPermissionUserIds($node));
                }
            }

            return $userIds;
        };

        $privilegeService = \common\library\privilege_v3\PrivilegeService::getInstance($clientId);
        $adminUser = $privilegeService->getAdminUserId();
        $isAdmin = $userId == $adminUser;
        // 权限兼容
        $permissionSql = [];
        $permissions = [];
        $tablePermissionUserIds = [];

        // 获取需要校验的权限数组
        foreach (AiAgentConstants::GENERATE_DATA_SUPPORT_TABLES as $tableName)
        {
            if (in_array($tableName, $tables) || str_contains($sql, $tableName)) {
                $permissions[$tableName] = AiAgentConstants::GENERATE_DATA_TABLE_PERMISSION_MAP[$tableName];
            }
        }

        if (!$isAdmin) {
            foreach ($permissions as $table => $permission) {
                // 新增user校验
                $permissionUserInfos = \common\library\department\Helper::getPermissionTree($clientId, $userId, [$permission], 1, 1, 1);
                $permissionUserIds = $getPermissionUserIds($permissionUserInfos);
                $tablePermissionUserIds[$table] = $permissionUserIds;
            }
        }

        // 拼接 with 语句
        foreach (AiAgentConstants::GENERATE_DATA_SUPPORT_TABLES as $index => $tableName)
        {
            if (!in_array($tableName, $tables) && !str_contains($sql, $tableName)) {
                continue;
            }

            $permissionUserIds = implode(",",$tablePermissionUserIds[$tableName] ?? []);

            $headSql = empty($permissionSql) ? "WITH {$tableName} AS" : "{$tableName} AS";

            $enableFlagInfos = AiAgentConstants::GENERATE_DATA_TABLE_DELETE_FLAG[$tableName];
            $pk = AiAgentConstants::GENERATE_DATA_TABLE_PK_MAP[$tableName];
            $enableFlagSql = '';
            foreach ($enableFlagInfos as $enableFlagInfo)
            {
                $operator = $enableFlagInfo['operator'] ?? '=';
                if (is_array($enableFlagInfo['value'])) {
                    $value = implode(",",$enableFlagInfo['value']);
                    $enableFlagSql .= " AND {$tableName}.{$enableFlagInfo['field']} {$operator} ( {$value} ) ";
                } else {
                    $enableFlagSql .= " AND {$tableName}.{$enableFlagInfo['field']} {$operator} {$enableFlagInfo['value']} ";
                }
            }

            if (empty($permissionUserIds))
            {
                switch ($tableName) {
                    case "tbl_mail":
                        $permission = AiAgentConstants::GENERATE_DATA_TABLE_PERMISSION_MAP[$tableName];
                        // 邮件表单独处理
                        $permissionUserInfos = \common\library\department\Helper::getPermissionTree($clientId, $userId, [$permission], 1, 1, 1);
                        $permissionUserIds = $getPermissionUserIds($permissionUserInfos);
                        $permissionUserIds = implode(',', $permissionUserIds);
                        $permissionSql[] = <<<PERMISSIONSQL
$headSql (
   SELECT *
    FROM {$tableName}
    WHERE {$tableName}.client_id = {$clientId}
      $enableFlagSql
      AND {$tableName}.user_id in ({$permissionUserIds})
)

PERMISSIONSQL;
                        break;
                    case "tbl_department_info":
                        $permissionSql[] = <<<PERMISSIONSQL
$headSql (
   SELECT *
    FROM {$tableName}
    WHERE {$tableName}.client_id = {$clientId}
)

PERMISSIONSQL;

                        break;
                    default:
                        $permissionSql[] = <<<PERMISSIONSQL
$headSql (
   SELECT *
    FROM {$tableName}
    WHERE {$tableName}.client_id = {$clientId}
     $enableFlagSql
)
PERMISSIONSQL;
                }
            } else {
                // 测试环境不支持array_contains
                if (\Yii::app()->params['env'] == 'test') {
                    // 客户表支持查询公海客户
                    switch ($tableName) {
                        case "tbl_mail":
                            $permission = AiAgentConstants::GENERATE_DATA_TABLE_PERMISSION_MAP[$tableName];
                            // 邮件表单独处理
                            $permissionUserInfos = \common\library\department\Helper::getPermissionTree($clientId, $userId, [$permission], 1, 1, 1);
                            $permissionUserIds = $getPermissionUserIds($permissionUserInfos);
                            $permissionUserIds = implode(',', $permissionUserIds);
                            $permissionSql[] = <<<PERMISSIONSQL
$headSql (
   SELECT *
    FROM {$tableName}
    WHERE {$tableName}.client_id = {$clientId}
      $enableFlagSql
      AND {$tableName}.user_id in ({$permissionUserIds})
)

PERMISSIONSQL;
                            break;
                        case "tbl_department_info":
                            $permissionSql[] = <<<PERMISSIONSQL
$headSql (
   SELECT *
    FROM {$tableName}
    WHERE {$tableName}.client_id = {$clientId}
)

PERMISSIONSQL;
                            break;
                        case 'tbl_company':
                            if (\common\library\privilege_v3\Helper::hasPermission($this->clientId, $this->userId, PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_VIEW)) {
                                $poolSwitchStatus = \common\library\privilege_v3\Helper::getSwitchStatus($clientId, Client::EXTERNAL_KEY_CUSTOMER_POOL_SWITCH);
                                $poolIds = $poolSwitchStatus ? implode(',', \common\library\customer\pool\Helper::getUserPoolIds($this->clientId, $this->userId)) : 0;
                                $permissionSql[] = <<<PERMISSIONSQL
$headSql (
   SELECT *
    FROM {$tableName}
    WHERE {$tableName}.client_id = {$clientId}
      $enableFlagSql
      AND ({$tableName}.user_id && ARRAY [{$permissionUserIds}]::BIGINT[] OR ({$tableName}.user_id = '{}' AND {$tableName}.pool_id IN ({$poolIds})))
)
PERMISSIONSQL;
                                break;
                            }
                        default:
                            $permissionSql[] = <<<PERMISSIONSQL
$headSql (
   SELECT *
    FROM {$tableName}
    WHERE {$tableName}.client_id = {$clientId}
      $enableFlagSql
      AND {$tableName}.user_id && ARRAY [{$permissionUserIds}]::BIGINT[] 
)
PERMISSIONSQL;
                            break;

                    }

                } else {
                    switch ($tableName) {
                        case "tbl_mail":
                            $permission = AiAgentConstants::GENERATE_DATA_TABLE_PERMISSION_MAP[$tableName];
                            // 邮件表单独处理
                            $permissionUserInfos = \common\library\department\Helper::getPermissionTree($clientId, $userId, [$permission], 1, 1, 1);
                            $permissionUserIds = $getPermissionUserIds($permissionUserInfos);
                            $permissionUserIds = implode(',', $permissionUserIds);
                            $permissionSql[] = <<<PERMISSIONSQL
$headSql (
   SELECT *
    FROM {$tableName}
    WHERE {$tableName}.client_id = {$clientId}
      $enableFlagSql
      AND {$tableName}.user_id in ({$permissionUserIds})
)

PERMISSIONSQL;
                            break;
                        case "tbl_department_info":
                            $permissionSql[] = <<<PERMISSIONSQL
$headSql (
   SELECT *
    FROM {$tableName}
    WHERE {$tableName}.client_id = {$clientId}
)

PERMISSIONSQL;
                            break;
                        case 'tbl_company': // 这里一定要放到default前面，位置不能乱
                            if (\common\library\privilege_v3\Helper::hasPermission($this->clientId, $this->userId, PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_VIEW)) {
                                $poolSwitchStatus = \common\library\privilege_v3\Helper::getSwitchStatus($clientId, Client::EXTERNAL_KEY_CUSTOMER_POOL_SWITCH);
                                $poolIds = $poolSwitchStatus ? implode(',', \common\library\customer\pool\Helper::getUserPoolIds($this->clientId, $this->userId)) : 0;
                                $permissionSql[] = <<<PERMISSIONSQL
$headSql (
  select * from ( 
   SELECT *,ROW_NUMBER() OVER (PARTITION BY {$pk} ORDER BY permission_user_id) AS rn
 FROM ( 
   SELECT *,unnest(user_id) permission_user_id
    FROM {$tableName}
    WHERE {$tableName}.client_id = {$clientId}
       $enableFlagSql
   UNION ALL
   SELECT *,NULL permission_user_id
    FROM {$tableName}
    WHERE {$tableName}.client_id = {$clientId}
    AND user_id = '{}'
       $enableFlagSql
    ) as t{$index} where  array_contains(
    ARRAY [{$permissionUserIds}]::BIGINT[],
    t{$index}.permission_user_id
   ) OR (t{$index}.permission_user_id IS NULL AND t{$index}.pool_id IN ({$poolIds}))
    )  AS ranked_{$tableName}
   where rn = 1
)
PERMISSIONSQL;
                                break;
                            }
                        default:
                            $permissionSql[] = <<<PERMISSIONSQL
$headSql (
  select * from ( 
   SELECT *,ROW_NUMBER() OVER (PARTITION BY {$pk} ORDER BY permission_user_id) AS rn
 FROM ( 
   SELECT *,unnest(user_id) permission_user_id
    FROM {$tableName}
    WHERE {$tableName}.client_id = {$clientId}
       $enableFlagSql
    ) as t{$index} where  array_contains(
    ARRAY [{$permissionUserIds}]::BIGINT[],
    t{$index}.permission_user_id
   ) )  AS ranked_{$tableName}
   where rn = 1
)
PERMISSIONSQL;
                            break;

                    }
                }

            }

        }
        // 处理复杂问题的with语句
        if ($this->withSql) {
            $withSql = str_replace('WITH', '', $this->withSql);
            $permissionSql[] = $withSql;
        }
        $permissionSqlStr = implode(',', $permissionSql);
        return $permissionSqlStr;
    }

    public function handleSql($sql = '')
    {
        $sql = $sql ?: $this->sql;
        // 兼容HOLO DB做语法转译，Group By 数组转换
        $sqlParserService = new SQLParserService($sql);
        $sqlParserService->adjustParsed();
        $sql = $sqlParserService->create();
        $permissionSqlStr = $this->buildPermissionSql(array_column($this->getTables(), 'table'), $sql);
        $sql = <<<SQL
{$permissionSqlStr}
{$sql}
SQL;

        if (\Yii::app()->params['env'] == 'test') {
            $sql = str_replace('tbl_company', 'tbl_company_dsl_test', $sql);
            $sql = str_replace('tbl_order', 'tbl_order_dsl_test', $sql);
            $sql = str_replace('tbl_opportunity', 'tbl_opportunity_dsl_test', $sql);
            $sql = str_replace('tbl_lead', 'tbl_lead_dsl_test', $sql);
            $sql = str_replace('tbl_mail', 'tbl_mail_dsl_test', $sql);
            $sql = str_replace('tbl_invoice_product_record', 'tbl_invoice_product_record_dsl_test', $sql);
            $sql = str_replace('tbl_department_info', 'dwd_ai_tbl_department_info', $sql);
        } else {
            $sql = str_replace('tbl_company', 'dwd_ai_tbl_company', $sql);
            $sql = str_replace('tbl_order', 'dwd_ai_tbl_order', $sql);
            $sql = str_replace('tbl_opportunity', 'dwd_ai_tbl_opportunity', $sql);
            $sql = str_replace('tbl_lead', 'dwd_ai_tbl_lead', $sql);
            $sql = str_replace('tbl_mail', 'dwd_ai_tbl_mail', $sql);
            $sql = str_replace('tbl_invoice_product_record', 'dwd_ai_tbl_invoice_product_record', $sql);
            $sql = str_replace('tbl_department_info', 'dwd_ai_tbl_department_info', $sql);
        }

        $this->sql = $sql;

        return $sql;
    }

    public function translation($sql = '')
    {
        $sql = $sql ?: $this->sql;
        // CURRENT_DATE - INTERVAL 语法处理
        $pattern = "/CURRENT_DATE - INTERVAL '(\d+)\s*([a-zA-Z]+)'/";

        // 执行正则匹配
        preg_match_all($pattern, $sql, $matches);

        $needTranslation = [];
        $indexNameMap = [
            0 => 'translationSql',
            1 => 'number',
            2 => 'timeType'
        ];

        for ($i = 0; $i < count($matches); $i++)
        {
            for ($j = 0; $j < count($matches[$i]); $j++) {
                $needTranslation[$j][$indexNameMap[$i]] = $matches[$i][$j];
            }
        }

        foreach ($needTranslation as $translation) {
            $sql = str_replace($translation['translationSql'], "'" . date('Y-m-d H:i:s', strtotime("-{$translation['number']} {$translation['timeType']}")) . "'::date", $sql);
        }

        // 根据配置处理Sql value 字段 注意 这段逻辑不需要修改 只需要修改配置就可以了
        $sqlParse = new SQLParserService($sql);
        $dealSqlInfo = $sqlParse->dealWhereValue($sqlParse->parsed,self::DEAL_VALUE_MAP);
        $sqlParse->parsed = $dealSqlInfo;

        $this->sql = $sqlParse->create();

        // 更新基类的parsed
        $this->parsed = $sqlParse->parsed;
        $this->service = $sqlParse;
        return $this->sql;
    }

    /**
     * 确定是否有查询表权限
     * 确定是否有查询所有字段的权限
     * @return void
     * @throws AiAgentException
     */
    public function checkTablePrivilege()
    {
        $privilegeService = \common\library\privilege_v3\PrivilegeService::getInstance($this->clientId, $this->userId);
        if ($this->userId == $privilegeService->getAdminUserId()) return ;

        $redis = \RedisService::sf();
        $tableFieldInfos = $redis->get(GenerateDataSqlAiAgent::GENERATE_DATA_TABLE_INFO_CACHE_KEY);
        $tableFieldInfos = json_decode($tableFieldInfos,true);

        $privilegeField = new PrivilegeField($this->clientId);
        $userPrivilege = new UserPrivilegeService($this->clientId);
        $roleIds = $userPrivilege->getUserRoleIds($this->userId);

        foreach ($this->getTables() as $tableInfo)
        {
            $tableName = $tableInfo['table'];
            $privilege = AiAgentConstants::GENERATE_DATA_TABLE_PERMISSION_MAP[$tableName] ?? '';
            if (empty($privilege)) continue;
            if (! $privilegeService->hasPrivilege($privilege)) {
                throw new AiAgentException("暂无{$tableName}查询权限", AiAgentException::NO_HAS_GENERATE_DATA_PRIVILEGE);
            }

            $tableFieldInfo = $tableFieldInfos[$tableName] ?? [];
            $referType = AgentHelper::getTableKeyInfo($tableName, 'field_refer_type');
            $functional = AgentHelper::getTableKeyInfo($tableName, 'functional');

            if (empty($referType) || empty($functional) || empty($tableFieldInfo)) continue;

            // 获取权限字段
            $fieldQuery = new FieldList($this->clientId);
            $fieldQuery->setType($referType);
            $fieldQuery->setIsList(0);
            $fieldQuery->setExcludeFieldType([CustomFieldService::FIELD_TYPE_IMAGE, CustomFieldService::FIELD_TYPE_ATTACH]);
            $fieldQuery->setFields(['id', 'name', 'field_type','default', 'type']);
            $fieldList = $fieldQuery->find();
            $fieldList = array_column($fieldList,null,'id');

            if (empty($fieldList)) continue;

            $fieldMappingInfo = $tableFieldInfo['columns'];
            $fieldNamesList = array_column($fieldMappingInfo, 'name', 'name');
            $fieldMappingInfo = array_column($fieldMappingInfo, 'mapping', 'name'); // 宽表字段与原表字段映射

            // 不是所有的模块都有字段权限
            if (isset(PrivilegeConstants::FIELD_PRIVILEGE_FUNCTIONAL_MAP[$functional])) {
                $fieldScope = $privilegeField->getBatchRoleFieldScope($functional, $roleIds) ?? [];
            } else {
                $fieldScope = [];
            }

            foreach ($this->getFields($this->originSql) as $fieldInfo) {
                $tableField = $fieldInfo['base'];
                // 自定义字段特殊处理
                if (str_contains($tableField, 'external_field_data_')) {
                    $tableField = str_replace('external_field_data_', '', $tableField);
                }
                if ((!empty($fieldNamesList[$tableField])) || is_numeric($tableField)) {
                    $field = !empty($fieldMappingInfo[$tableField]) ? $fieldList[$fieldMappingInfo[$tableField]] ?? [] : $fieldList[$tableField] ?? [];
                    if (empty($field)) continue;
                    // 隐藏字段不支持查询，用户属于多个角色，字段权限取并集，取同字段在多个角色中最小权限
                    foreach ($roleIds as $roleId) {
                        if (((($fieldScope[$functional][$roleId][$field['type'] . '-' . $field['id']]) ?? PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_READONLY) == PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_DISABLE)
                        || $field['disable_flag']) {
                            throw new AiAgentException("暂无字段【{$field['name']}】查询权限", AiAgentException::NO_FIELD_QUERY_PERMISSION);
                        }
                    }
                }
            }
        }
    }

    public function checkComplexProblemPrivilege($withSql)
    {
        if (empty($withSql)) return ;
        $tables = [];
        $service = new SQLParserService($withSql);
        $tables = $service->getTablesFromWithSql();

    }

    public function handleComplexSql($sql = '')
    {
        return $sql;
    }

    public function execSqlProcess($sql)
    {
        $sql = $this->translation($sql);
        $sql = $this->handleCustomField($sql);
        $sql = $this->setSql($sql)->buildJumpUrlSql();
        $sql = $this->handleSql($sql);
        return $sql;
    }

    public function csvSqlProcess($sql)
    {
        $sql = $this->translation($sql);
        $sql = $this->handleCustomField($sql);
        $sql = $this->handleSql($sql);
        return $sql;
    }

    public function withSqlProcess($sql)
    {
        $this->setWithSql($sql);
        $sql = $this->handleCustomField($sql);
        return $sql;
    }



}