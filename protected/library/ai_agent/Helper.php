<?php

namespace common\library\ai_agent;

use AiAgent;
use AiAgentConversation;
use AiAgentConversationHistory;
use AlibabaAccount;
use AliPay\Exception;
use ArrayUtil;
use CDbException;
use CException;
use common\components\BaseObject;
use common\components\TmMysqlConnection;
use common\library\account\Client;
use common\library\account\UserInfo;
use common\library\account\UserList;
use common\library\ai_agent\agent\AiAgentList;
use common\library\ai_agent\api\AIClient;
use common\library\ai_agent\communication\message_body\MessageBody;
use common\library\ai_agent\communication\summary\ChatService;
use common\library\ai_agent\company_quality_check\AiQualityCheckAgentAnalysisList;
use common\library\ai_agent\company_quality_check\AiQualityCheckChatJourneyList;
use common\library\ai_agent\company_quality_check\AiQualityCheckCompanyConversation;
use common\library\ai_agent\company_quality_check\AiQualityCheckCompanyConversationList;
use common\library\ai_agent\company_quality_check\AiQualityCheckCompanyJourneyList;
use common\library\ai_agent\config\AssetAnalysisConfig;
use common\library\ai_agent\config\TeamAnalysisConfig;
use common\library\ai_agent\exception\AiAgentException;
use common\library\ai_agent\message\AbstractMessageFormat;
use common\library\ai_agent\message\Card;
use common\library\ai_agent\message\ComplexProblemCard;
use common\library\ai_agent\message\complexProblemErrorCard;
use common\library\ai_agent\message\component\Button;
use common\library\ai_agent\message\MailReplyOptionsCard;
use common\library\ai_agent\message\QuestionRecommendCard;
use common\library\ai_agent\message\StatisticCard;
use common\library\ai_agent\parser\SQLParserService;
use common\library\ai_agent\proxy\AiAgentProxy;
use common\library\ai_agent\record\AiServiceProcessRecord;
use common\library\ai_agent\utils\DslSqlConvertor;
use common\library\ai_agent\vector\CommonVectorDocument;
use common\library\ai_agent\vector\EmbeddingService;
use common\library\ai_service\AiAgentConversationHistoryList;
use common\library\ai_service\AiServiceConstant;
use common\library\alibaba\customer\TMAlibabaCustomerService;
use common\library\alibaba\jobs\TmMessageSyncJob;
use common\library\alibaba\services\AlibabaTopClient;
use common\library\api\InnerApi;
use common\library\async_task\AsyncTaskConstant;
use common\library\auto_market\task_history\TaskHistoryFormatter;
use common\library\cms\inquiry\SessionInquiryList;
use common\library\custom_field\FieldList;
use common\library\customer_v3\company\list\CompanyList;
use common\library\customer_v3\company\orm\Company;
use common\library\customer_v3\customer\orm\Customer;
use common\library\customer_v3\customer\CustomerList;

//use common\library\customer\Company;
//use common\library\customer\CompanyList;
//use common\library\customer\Customer;
//use common\library\customer\CustomerList;

use common\library\department\DepartmentCacheableRepo;
use common\library\email\Util;
use common\library\invoice\Order;
use common\library\invoice\status\InvoiceStatusService;
use common\library\lead\Lead;
use common\library\lead\LeadList;
use common\library\mail\MailContentHelper;
use common\library\mail\MailList;
use common\library\okki_chat\MessageService;
use common\library\opportunity\stage\migration\OpportunityStage;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\privilege_v3\UserPrivilegeService;
use common\library\prompt\AiServiceProcessRecordList;
use common\library\prompt\PromptConstant;
use common\library\queue_v2\QueueService;
use common\library\report\dingtalk\DingTalkRobot;
use common\library\setting\library\flow\FlowApi;
use common\library\setting\library\pool\PoolApi;
use common\library\setting\library\stage\StageApi;
use common\library\setting\user\UserSetting;
use common\library\sns\Constants;
use common\library\sns\customer\CustomerContactHelper;
use common\library\sns\customer\UserCustomerContract;
use common\library\sns\message\MessageBuilder;
use common\library\sns\UserContactMessageService;
use common\library\social_auth\Constant;
use common\library\statistics\subscribe\AiSubscribeList;
use common\library\util\PgsqlUtil;
use common\library\whatsapp_cloud\WhatsappCloudMessageService;
use common\modules\prometheus\library\other_platform\AccountList;
use Constants as libraryConstants;
use CustomerOptionService;
use DataWorkActiveRecord;
use Hyperf\Utils\Arr;
use JetBrains\PhpStorm\ArrayShape;
use LogUtil;
use PgActiveRecord;
use protobuf\OkkiAi\PBAgentSceneType;
use protobuf\OkkiAi\PBAiAgentChatCompletionDeepAnalysisData;
use protobuf\OkkiAi\PBAiAgentChatCompletionQueryLogicData;
use protobuf\OkkiAi\PBAiAgentChatCompletionQuestionRecommendData;
use protobuf\OkkiAi\PBAiAxisType;
use protobuf\OkkiAi\PBAnalysisData;
use protobuf\OkkiAi\PBAnalysisList;
use protobuf\OkkiAi\PBAnalysisStrategyData;
use protobuf\OkkiAi\PBCommonAnalysisInfo;
use protobuf\OkkiAi\PBCommonMessageInfo;
use protobuf\OkkiAi\PBComponentButtonCopyParams;
use protobuf\OkkiAi\PBConversationMessage;
use protobuf\OkkiAi\PBDataArr;
use protobuf\OkkiAi\PBDataAssistantExtraData;
use protobuf\OkkiAi\PBDeepAnalysisData;
use protobuf\OkkiAi\PBEchoParamItem;
use protobuf\OkkiAi\PBGenerateDataItem;
use protobuf\OkkiAi\PBGenerateDataList;
use protobuf\OkkiAi\PBGenerationDataAxisConfig;
use protobuf\OkkiAi\PBGenerationDataAxisInfo;
use protobuf\OkkiAi\PBMailQualityConfig;
use protobuf\OkkiAi\PBMailQualityConfigEnum;
use protobuf\OkkiAi\PBMailQualityConfigInfo;
use protobuf\OkkiAi\PBMailQualityDataItem;
use protobuf\OkkiAi\PBMailQualityList;
use protobuf\OkkiAi\PBMailReplyExtraData;
use protobuf\OkkiAi\PBMailReplyOption;
use protobuf\OkkiAi\PBMessageComponent;
use protobuf\OkkiAi\PBMessageComponentButtonParams;
use protobuf\OkkiAi\PBMessageComponentFeedbackParams;
use protobuf\OkkiAi\PBMessageComponentParams;
use protobuf\OkkiAi\PBMessageComponentSelectParams;
use protobuf\OkkiAi\PBMessageComponentSelectParamsItem;
use protobuf\OkkiAi\PBMessageConfig;
use protobuf\OkkiAi\PBMessageData;
use protobuf\OkkiAi\PBMessageExtra;
use protobuf\OkkiAi\PBMultipleConversationMessage;
use protobuf\OkkiAi\PBQuestionRecommendExtraData;
use protobuf\OkkiAi\PBReportInfo;
use protobuf\OkkiAi\PBSubAnalysisData;
use protobuf\OkkiAi\PBThirdAnalysisData;

use protobuf\OkkiAi\PBChatReplyExtraData;
use protobuf\OkkiAi\PBChatReplyExtraDataReplyStrategy;
use protobuf\OkkiAi\PBChatReplyExtraDataOtherStrategyButton;
use protobuf\OkkiAi\PBChatReplyExtraDataReply;
use protobuf\OkkiAi\PBTranslateButton;
use protobuf\OkkiAi\PBTranslateButtonOption;
use protobuf\OkkiAi\PBChatReplyOtherStrategyList;
use protobuf\OkkiAi\PBChatReplyStrategy;
use protobuf\OkkiAi\PBChatReplyOtherStrategyReplyExtraData;
use protobuf\OkkiAi\PBChatCoachExtraData;
use protobuf\OkkiAi\PBChatCoachExtraDataAnalysis;
use protobuf\OkkiAi\PBChatCoachExtraDataStrategy;
use protobuf\OkkiAi\PBChatPolishExtraData;
use protobuf\OkkiAi\PBChatPolishExtraDataOriginalReply;
use protobuf\OkkiAi\PBChatPolishExtraDataPolishedReply;
use protobuf\OkkiAi\PBOpportunityFollowUpTextExtraData;
use protobuf\OkkiAi\PBCustomerFollowUpTextExtraData;
use protobuf\OkkiAi\PBComponentButtonSetSceneTypeParams;
use protobuf\OkkiAi\PBComponentButtonRecordFollowUpParams;
use protobuf\OkkiAi\PBComponentButtonCreateOpportunityParams;
use protobuf\OkkiAi\PBOpportunityList;
use protobuf\OkkiAi\PBFollowUpOpportunity;

use RuntimeException;
use User;
use xiaoman\AlibabaSdk\taobao\top\domain\EnvInfo;
use xiaoman\AlibabaSdk\taobao\top\domain\EventData;
use xiaoman\AlibabaSdk\taobao\top\request\AlibabaIcbuRiskSendRequest;
use Yii;

class Helper
{
    public static MessageService $messageService;


    public static function getReportData(int $clientId, int $userId, array $reportParams = [])
    {
        $searchParams = $reportParams['params'] ?? [];
        $reportKey = $reportParams['key'] ?? '';
        $uniqueKey = $reportParams['unique_key'] ?? '';
        $visibleUserIds = array_filter($reportParams['user_id'] ?? [], 'is_numeric');
        $visibleDepartmentIds = array_filter($reportParams['department_id'] ?? [], 'is_numeric');
        $refresh = $reportParams['refresh'] ?? 0;
        $showDetail = $reportParams['show_detail'] ?? 0;
        $showData = $reportParams['show_data'] ?? 1;
        $scene = $reportParams['scene'] ?? '';
        $extendParams = [
            'user_id' => $visibleUserIds,
            'department_id' => $visibleDepartmentIds,
        ];

        $extendParams = array_filter($extendParams, function ($item){
            return $item !== null && $item !== '' && $item !== [];
        });

        $searchParams = array_map(function ($item) {
            return is_array($item) ? json_encode($item) : $item;
        }, $searchParams);

        $report = new \common\library\statistics\render\report\Report($clientId, $reportKey, $searchParams, $extendParams);
        $report->setViewUserId($userId);
        $report->setForceRefresh($refresh);
        $report->setUniqueKey($uniqueKey);
        $report->setShowDetail($showDetail);
        $report->setShowData($showData);
        $report->setScene($scene);
        return $report->format();
    }

    // todo 上线前下掉
    public static function exchangeLanguage()
    {
       if (Yii::app()->params['env'] == 'test') {
           $user = User::getLoginUser();
           // 使用简中则切换日语
           if ($user->getLanguage() == 'zh-CN') {
               Yii::app()->language = 'ja';
           }
       }
    }

    public static function getReprotUserContentForAi(int $clientId, int $userId, array $reportParams = [])
    {
        $reportData = Helper::getReportData($clientId, $userId, $reportParams);

        return \common\library\statistics\Helper::formatReportData($reportData);
    }

    public static function getDataAnalysisAgentPrompt($question = '', array $params = [], array $aiAgentVersionInfo = [])
    {
        // 组装[当前所在页面的报表名称]
        $reportName = $params['name'] ?? '';

        // 填充组装参数
        $promptReplaceParams = [
            '#用户输入#' => $question,
            '#报表名称#' => $reportName,
        ];

        // 替换prompt
        $prompt = $aiAgentVersionInfo['prompt'] ?? AiAgentConstants::AI_DATA_DISTRIBUTION_PROMPT;
        foreach ($promptReplaceParams as $search => $replace)
        {
            $prompt = str_replace($search, $replace, $prompt);
            $prompt = str_replace('\n', "\n", $prompt);
        }

        return $prompt;
    }


    /**
     * 从 AI 建档的回复中提取客户信息
     * @param string $answer
     * @return array
     */
    public static function extractCustomerFromAIArchiveAnswer(int $clientId, int $userId, string $answer, array $params): array
    {
        $company = [];
        $customer = [];

        // 未获取客户新建信息，提示异常信息
        ['company' => $company, 'customer' => $customer] = Helper::parseAIArchiveAnswer($answer);

        // 尝试从沟通渠道获取特定客户信息(优先于LLM返回), 从leads数据库匹配信息
        if(!empty($params['channel_type']) && $params['channel_type'] != Constant::CHANNEL_TYPE_MAIL) {
            ['company' => $company, 'customer' => $customer] = FastArchiveAiAgent::fillChannelInfoAndLeadsData($clientId, $userId, $company, $customer, $params);
        }

        // leads 查询出来的客户信息，ZZ 代表的是无国家的意思
        if (!empty($company['country']) && ($company['country'] != 'ZZ')) {
            $value = \Util::removeCountryFlagEmoji($company['country']);
            $country = \CountryService::checkNameInTable(trim($value));
            if (empty($country) || in_array($country['alpha2'], ['HK', ' MO', 'TW'])) {
                unset($company['country']);
            } else {
                $company['country'] = $country['alpha2'];
            }
        }

        return [$company, $customer];
    }

    /**
     * @param string $answer
     * @return array
     */
    #[ArrayShape(['company' => 'array', 'customer' => 'array'])]
    public static function parseAIArchiveAnswer(string $answer): array
    {
        $data = json_decode($answer ?: '[]', true) ?? [];
        if (!$data) {
            // 正则匹配兼容回复内容
            preg_match('/```(json)?((.|\s)*)```/', $answer, $matches);
            if (!empty($matches[2])) {
                $data = json_decode($matches[2], true) ?? [];
            }
        }
        $res = [
            'company' => [],
            'customer' => [],
        ];
        foreach ($data as $k => $v) {
            if (is_array($v) || empty($v)) {
                continue;
            }
            $v = trim($v);
            if (empty($v) || in_array(strtolower($v), ['unknown', 'n/a', '未知'])) {
                continue;
            }

            if (empty(FastArchiveAiAgent::FIELD_MAPPING[$k])) {
                continue;
            }
            $k = FastArchiveAiAgent::FIELD_MAPPING[$k];

            // 联系方式
            if (str_starts_with($k, 'customer.contact.')) {
                $k = substr($k, strlen('customer.contact.'));
                $res['customer']['contact'][] = [
                    'type' => $k,
                    'value' => $v,
                ];
                continue;
            }

            //联系电话是列表
            if($k == 'customer.tel_list') {
                $v = [['',$v]];
            }

            Arr::set($res, $k, $v);
        }
        return $res;
    }


    /**
     * 聊天使用AI自动建线索
     * @param string $answer
     * @return array
     */
    public static function parseChatAIArchiveAnswer(string $answer)
    {
        $data = json_decode($answer ?: '[]', true) ?? [];
        if (!$data) {
            preg_match('/```(json)?((.|\s)*)```/', $answer, $matches);
            if (!empty($matches[2])) {
                $data = json_decode($matches[2], true) ?? [];
            }
        }
        $res = [
            'lead' => [],
            'customer' => [],
        ];

        foreach ($data as $k => $v) {
            if (is_array($v) || empty($v)) {
                continue;
            }
            $v = trim($v);
            if (empty($v) || in_array(strtolower($v), ['unknown', 'n/a', '未知'])) {
                continue;
            }

            if (empty(ChatArchiveAiAgent::FIELD_MAPPING[$k])) {
                continue;
            }
            $k = ChatArchiveAiAgent::FIELD_MAPPING[$k];

            if (str_starts_with($k, 'customer.contact.')) {
                $k = substr($k, strlen('customer.contact.'));
                $res['customer']['contact'][] = [
                    'type' => $k,
                    'value' => $v,
                ];
                continue;
            }

            Arr::set($res, $k, $v);
        }
        return $res;
    }

    public static function parseFollowupContent(string $answer) {
        $start = strpos($answer, '{"content"');
        $answer = substr($answer, $start);
        $answer = json_decode($answer, true);
        $answer = $answer['content']??'';
        return $answer;
    }

    public static function convertChatParams(int $clientId, array $params): array
    {
        $snsId = ArrayUtil::fallbackByKeys($params, 'sns_id', 'buyer_account_id') ?? '';
        $userSnsId = ArrayUtil::fallbackByKeys($params, 'user_sns_id', 'store_id', 'channel_open_id') ?? '';

        // TM渠道特殊处理：前端没有传store_id, 需要把seller_account_id转为store_id
        if ((int)($params['channel_type']) === Constant::CHANNEL_TYPE_TM && empty($params['store_id'])) {
            $accountInfo = \common\library\alibaba\Helper::getBindAccountInfoBySellerId($params['seller_account_id']);
            if ((int)($accountInfo['client_id'] ?? -1) === $clientId) {
                $userSnsId = $accountInfo['store_id'];
            } else {
                // 查询不到店铺信息，可能是授权状态不对，或被其它client绑定
                throw new AiAgentException(
                    AiAgentException::ERROR_CODE_TO_USER_MSG[AiAgentException::ERR_NO_CHAT_HISTORY],
                    AiAgentException::ERR_NO_CHAT_HISTORY
                );
            }
        }

        return [$snsId, $userSnsId];
    }

    public static function getChatPrompt($clientId, $userId, $conversationId, $params, $limit = 30)
    {
        [$snsId, $userSnsId] = self::convertChatParams($clientId, $params);

        $messageList = self::getMessageList($params['channel_type'], $clientId, $userId, $conversationId, $limit, $snsId, $userSnsId);
        if (!empty($params['message_list'])) {
            $messageList = self::mergeRecentMessages($params['channel_type'], $params['message_list'], $messageList);
        }
        if (empty($messageList)) {
            throw new AiAgentException(AiAgentException::ERROR_CODE_TO_USER_MSG[AiAgentException::ERR_NO_CHAT_HISTORY] ?? '', AiAgentException::ERR_NO_CHAT_HISTORY);
        }
        $channelType = (int) $params['channel_type'];
        return match ($channelType) {
            Constant::CHANNEL_TYPE_WABA,
            Constant::CHANNEL_TYPE_FACEBOOK,
            Constant::CHANNEL_TYPE_LIVECHAT,
            Constant::CHANNEL_TYPE_INSTAGRAM,
            Constant::CHANNEL_TYPE_WHATSAPP_CLOUD,
            => self::formatMessageToPrompt($messageList, $channelType),
            default => self::formatContactMessageToPrompt($messageList, $channelType),
        };
    }

    public static function getMailChatPrompt($clientId, $userId, $mailId)
    {
        //获取该邮件的日期
        $db = \ProjectActiveRecord::getDbByClientId($clientId);
        $sql = "select * from tbl_email_relation where client_id = {$clientId} and mail_id =  {$mailId}";
        $mail = $db->createCommand($sql)->queryRow();
        if(empty($mail)) return [];

        //获取当天时间
        $mailTime = strtotime($mail['mail_time']);
        $startTime = date('Y-m-d 00:00:00',$mailTime);
        $endTime = date('Y-m-d 23:59:59',$mailTime);

        //email_id和user_mail_id在[startTime,endTime]内的所有邮件
        $emailId = $mail['email_id'];
        $userMailId = $mail['user_mail_id'];
        $sql = "select * from tbl_email_relation where client_id = {$clientId} and email_id = {$emailId} and user_mail_id = {$userMailId} and mail_time >= '{$startTime}' and mail_time <= '{$endTime}'";
        $mailList = $db->createCommand($sql)->queryAll();
        if(empty($mailList)) return [];

        $mailIds = array_column($mailList, 'mail_id');

        //获取mail_id对应的正文
        $params = [];
        foreach ($mailIds as $mailId)
        {
            $params[] = [
                'mailId' => $mailId,
                'userMailId' => $userMailId,
                'clientId' => $clientId
            ];
        }
        $mailPlainTextWithMailId = [];
        foreach (array_chunk($params,40) as $subParams)
        {
            $content = MailContentHelper::getContentAndPlainTextList($subParams);
            $mailPlainTextWithMailId = array_merge($mailPlainTextWithMailId, $content);
        }
        $mailPlainTextWithMailId = array_column($mailPlainTextWithMailId,'plainText','mailId');

        //获取mail_id对应信息
        $mailQueryList = new MailList($clientId,$userId);
        $mailQueryList->setMailIds($mailIds);
        $mailQueryList->setOrderBy(['receive_time']);
        $mailQueryList->setOrder('asc');
        $mailInfoList = $mailQueryList->find();

        //格式化
        $result = [];
        foreach ($mailInfoList as $mailInfo)
        {
            $sender = $mailInfo['sender'] ?? '';
            $receiver = $mailInfo['receiver'] ?? '';
            $subject = $mailInfo['subject'] ?? '';
            $role = $mailInfo['mail_type'] == \Mail::MAIL_TYPE_SEND ? 'Salesperson' : 'Customer';
            $receiveTime = $mailInfo['receive_time'] ?? '';
            $content = $mailPlainTextWithMailId[$mailInfo['mail_id']] ?? '';

            $result[] = <<<EOF
{$role}: {$receiveTime} From:{$sender} To:{$receiver} Subject:"{$subject}"
Content: {$content}
EOF;
        }

        return $result;
    }

    /**
     * 获取最新1条消息往前30天内的消息
     * @param int $clientId
     * @param int $userId
     * @param $conversationId
     * @param array $params
     * @param int $limit
     * @param array $recentMessageList
     * @return array
     * @throws \ProcessException
     */
    public static function getChatMessagesWithTimeLimit(int   $clientId, int $userId, $conversationId,
                                                        array $params, int   $limit = 30,
                                                        array $recentMessageList = []
    ): array
    {
        [$snsId, $userSnsId] = self::convertChatParams($clientId, $params);
        $channelType = (int)$params['channel_type'];
        $messageList = self::getMessageList($channelType, $clientId, $userId, $conversationId, $limit, $snsId, $userSnsId);

        // 如果是whatsapp，需要合并前端传过来的最近的消息
        $messageList = self::mergeRecentMessages($channelType, $recentMessageList, $messageList);

        return self::filterAndFormatChatMessages($messageList, $channelType, $conversationId, $limit);
    }

    public static function filterAndFormatChatMessages(array $messageList, int $channelType, $conversationId = '', $limit = 30, $dateLimit = 1)
    {
        $lastMessage = $messageList[0] ?? null;
        if (!$lastMessage) {
            return [];
        }
        // 兼容不同的时间格式
        $lastMessageTime = is_numeric($lastMessage['send_time']) ? ($lastMessage['send_time'] / 1_000_000) : strtotime($lastMessage['send_time']);

        // 过滤掉最新消息之前15天之外的消息
        if($dateLimit) {
            $messageList = array_filter($messageList, function ($message) use ($lastMessageTime) {
                $sendTime = is_numeric($message['send_time']) ? ($message['send_time'] / 1_000_000) : strtotime($message['send_time']);
                return $sendTime >= ($lastMessageTime - 30 * 24 * 60 * 60);
            });
            // 取前50条
            $messageList = array_slice($messageList, 0, $limit);
        }


        if ($conversationId && in_array($channelType, [Constant::CHANNEL_TYPE_WABA, Constant::CHANNEL_TYPE_FACEBOOK, Constant::CHANNEL_TYPE_INSTAGRAM, Constant::CHANNEL_TYPE_LIVECHAT, Constant::CHANNEL_TYPE_WHATSAPP_CLOUD])) {
            return self::formatMessageToPrompt($messageList, $channelType);
        } else {
            return self::formatContactMessageToPrompt($messageList, $channelType);
        }
    }

    /**
     * @return Company
     */
    public static function getCompanyByChat($clientId, $userId, $channelType, $snsId = '', $userSnsId = '', $identityId = '') {
        $companyIds = self::getCompanyIdsByChat($clientId, $userId, $channelType, $snsId, $userSnsId, $identityId);
        if (count($companyIds) != 1) {
            throw new AiAgentException("对方尚未建为客户，无法生成跟进", AiAgentException::ERR_NOT_ARCHIVE_COMPANY);
        }
        return new Company($clientId, $companyIds[0]);
    }
    public static function getCompanyIdsByChat($clientId, $userId, $channelType, $snsId = '', $userSnsId = '', $identityId = '') {
        $companyIds = [];
        switch ($channelType) {
            case Constant::CHANNEL_TYPE_LIVECHAT:
                $siteSessionList = new SessionInquiryList($clientId);
                $siteSessionList->setIdentityId($identityId);
                $siteSessionList->setFields(['lead_id']);
                $result = $siteSessionList->find();

                $leads = [];
                if ($result) {
                    $leadList = new LeadList($userId);
                    $leadList->setLeadId(array_column($result, 'lead_id'));
                    $leadList->setFields('company_id');
                    $leadList->setIsArchive([Lead::ARCHIVE_HIDDEN]);
                    $leads = $leadList->find();
                }
                $companyIds = array_column($leads, 'company_id');
                break;
            case Constant::CHANNEL_TYPE_TM:
                $companyIds = [];
                if ($snsId && $userSnsId) {
                    $service = new TMAlibabaCustomerService($clientId, $userId);
                    $data = $service->getRelationByStoreAndBuyer($userSnsId, $snsId, true);
                    if (!empty($data['customer_id'])) {
                        $customer = new Customer($clientId, $data['customer_id']);
                        $customer->isExist() && $companyIds[] = $customer->company_id;
                    }
                }
                break;
            case Constant::CHANNEL_TYPE_FACEBOOK:
            case Constant::CHANNEL_TYPE_WABA:
            case Constant::CHANNEL_TYPE_WHATSAPP:
            case Constant::CHANNEL_TYPE_WHATSAPP_CLOUD:
            default:
                $map = [
                    Constant::CHANNEL_TYPE_FACEBOOK => Constants::SNS_CLIENT_FACEBOOK_PAGE,
                    Constant::CHANNEL_TYPE_WABA => Constants::SNS_CLIENT_WHATSAPP_BUSINESS,
                    Constant::CHANNEL_TYPE_WHATSAPP => Constants::SNS_CLIENT_WHATSAPP,
                    Constant::CHANNEL_TYPE_WHATSAPP_CLOUD => Constants::SNS_CLIENT_WHATSAPP,
                    Constant::CHANNEL_TYPE_TM => 'tm',  // TODO: 替换成常量,
                    Constant::CHANNEL_TYPE_INSTAGRAM => Constants::SNS_CLIENT_INSTAGRAM,
                ];
                $snsType = $map[$channelType]??'';
                $contactService = new UserCustomerContract($clientId, $userId);
                $contactService->loadBySns($snsType, $snsId, $userSnsId);
                $companyIds[] = $contactService->company_id;
                break;
        }

        // 过滤已删除数据
        if ($companyIds) {
            $list = new CompanyList($userId);
            $list->setCompanyIds($companyIds);
            $list->setFields(['company_id']);
            $list->setSkipPrivilege(true);
            $companies = $list->find();
            $companyIds = array_column($companies, 'company_id');
        }

        return array_filter($companyIds);
    }

    public static function getCompanyIdsByMail($clientId, $mailId)
    {
        if(!$clientId || !$mailId) {
            throw new RuntimeException("参数错误");
        }
        //根据client_id + mail_id从tbl_email_relation获取company_id
        $db = \ProjectActiveRecord::getDbByClientId($clientId);
        $sql = "SELECT customer_id FROM tbl_email_relation WHERE client_id = {$clientId} AND mail_id = {$mailId}";
        $customerIds = $db->createCommand($sql)->queryColumn();

        $customerList = new CustomerList($clientId);
        $customerList->setCustomerId($customerIds);
        $customerList->setFields(['company_id']);
        $ret = $customerList->find();
        $companyIds = array_column($ret, 'company_id');

        return array_unique(array_filter($companyIds));
    }

    public static function checkAiAgentPriviledge($clientId, $userId, $sceneType)
    {
        $privilege = true;

        switch ($sceneType)
        {
            case \common\library\prompt\PromptConstant::SCENE_TYPE_AI_SERVICE_SCENE_AI_DATA_DISTRIBUTION:
            case \AiAgent::AI_AGENT_SCENE_TYPE_STATISTICAL_ANALYSIS_INSIGHT:
            case \AiAgent::AI_AGENT_SCENE_TYPE_ASSET_ANALYSIS:
                $privilegeService = \common\library\privilege_v3\PrivilegeService::getInstance($clientId, $userId);
                $hasStatisticFunctional = $privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_STATISTIC);
                $hasStatisticPrivilege = $privilegeService->hasPrivilege(PrivilegeConstants::PRIVILEGE_CRM_STATISTIC_VIEW);
                if (!$hasStatisticFunctional || !$hasStatisticPrivilege) {
                    $privilege = false;
                }
                break;
            case \AiAgent::AI_AGENT_SCENE_TYPE_TEAM_ANALYSIS:

                $privilegeService = \common\library\privilege_v3\PrivilegeService::getInstance($clientId, $userId);
                $hasStatisticFunctional = $privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_STATISTIC);
                $hasStatisticPrivilege = $privilegeService->hasPrivilege(PrivilegeConstants::PRIVILEGE_CRM_STATISTIC_VIEW);

                $customerPrivilege = $privilegeService->getPrivilegeScope(PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW) > PrivilegeConstants::PRIVILEGE_SCOPE_OWNER;

                if (!$hasStatisticFunctional || !$hasStatisticPrivilege || !$customerPrivilege) {
                    $privilege = false;
                }
                break;

            case \AiAgent::AI_AGENT_SCENE_TYPE_CHAT_QUALITY: // 单个质检也需要检验私海客户权限
            case \AiAgent::AI_AGENT_SCENE_TYPE_BATCH_CHAT_QUALITY:
                $privilegeService = \common\library\privilege_v3\PrivilegeService::getInstance($clientId, $userId);
                $privilege = $privilegeService->getPrivilegeScope(PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW) > PrivilegeConstants::PRIVILEGE_SCOPE_OWNER;
                break;
            case \AiAgent::AI_AGENT_SCENE_TYPE_MAIL_QUALITY:
            case \AiAgent::AI_AGENT_SCENE_TYPE_MAIL_BATCH_QUALITY:
                $privilegeService = \common\library\privilege_v3\PrivilegeService::getInstance($clientId, $userId);
                $privilege = $privilegeService->getPrivilegeScope(PrivilegeConstants::PRIVILEGE_EMAIL_VIEW) > PrivilegeConstants::PRIVILEGE_SCOPE_OWNER;
                break;
            case \AiAgent::AI_AGENT_SCENE_TYPE_COMPANY_QUALITY_CHECK:
//                $client = Client::getClient($clientId);
//                $privilege = !empty($client->getExtentAttributes([Client::EXTERNAL_KEY_AI_COMPANY_QC])['ai_company_qc']);

                $privilegeService = PrivilegeService::getInstance($clientId, $userId);
                $systemId = $privilegeService->getMainSystemId();
                $hasFunc = true;
                if (!in_array($systemId, AiAgentConstants::AI_QC_SYSTEM_IDS)) {
                    $hasFunc = false;
                }
                $privilege = $privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_OKKI_AI_QC);
                $privilege = $hasFunc & $privilege;
                break;
            case \AiAgent::AI_AGENT_SCENE_TYPE_KNOWLEDGE_BASE:
                $privilegeService = PrivilegeService::getInstance($clientId, $userId);
                $privilege = $privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_OKKI_AI_KNOWLEDGE);
                break;
        }

        return $privilege;
    }

    /**
     * 判断是否有非本人的权限
     * @param int|string $clientId
     * @param int $userId
     * @return bool
     */
    public static function hasAdminScope(int|string $clientId, int $userId): bool
    {
        $userPrivilege = new UserPrivilegeService($clientId, $userId);
        $roles = $userPrivilege->getUserRoleInfoMap($userId);
        foreach (($roles[$userId] ?? []) as $role) {
            if ($role['scope'] != PrivilegeConstants::PRIVILEGE_SCOPE_OWNER) {
                return true;
            }
        }
        return false;
    }

    public static function ResponseSse(array $data, AbstractMessageFormat $format, bool $completeFlag = true)
    {
        $response = new \common\library\util\sse\SseResponse();
        $initMsg = $format->getSkeletonMessage();
        if (null !== $initMsg) {
            $response->writeJson(array_merge($initMsg, []));
        }

        $response->writeJson($data);

        $closeMessage = $format->getCloseMessage();
        if (null !== $closeMessage) {
            $response->writeJson($closeMessage);
        }

        if ($completeFlag) {
            $response->complete();
        }
    }

    public static function calculateSpeed($textSize)
    {
        if ($textSize == 0) {
            return 0;
        }
        // 最慢5s输出
        $maxOutputMillisecond = 5000;
        $maxInterval = 40;

        // 获取每毫秒需要输出多少个字
        $baseInterval = $maxOutputMillisecond / $textSize;

        // 最大的时间间隔是40ms
        $speed = min($baseInterval, $maxInterval);

        return $speed * 1000;
    }

    public static function bindCustomerToSocialMedia($clientId, $userId, $companyId, $customerId, \AiServiceProcessModel $aiRecord): bool
    {
        $params = json_decode($aiRecord->params, true);
        if (empty($params['channel_type'])) {
            LogUtil::error('channel_type is empty, ai_record_id: ' . $aiRecord->record_id);
            return false;
        }
        switch ($params['channel_type']) {
            case Constant::CHANNEL_TYPE_WABA:
            case Constant::CHANNEL_TYPE_FACEBOOK:
            case Constant::CHANNEL_TYPE_WHATSAPP_CLOUD:
                $userSnsId = $params['user_sns_id'] ?? $params['channel_open_id'] ?? 0;
                if (empty($params['sns_id']) || empty($userSnsId)) {
                    LogUtil::error('sns_id or user_sns_id is empty, ai_record_id: ' . $aiRecord->record_id);
                    return false;
                }
                $snsType = match (intval($params['channel_type'])) {
                    Constant::CHANNEL_TYPE_WABA => Constants::SNS_CLIENT_WHATSAPP_BUSINESS,
                    Constant::CHANNEL_TYPE_FACEBOOK => Constants::SNS_CLIENT_FACEBOOK_PAGE,
                    Constant::CHANNEL_TYPE_WHATSAPP => Constants::SNS_CLIENT_WHATSAPP,
                    Constant::CHANNEL_TYPE_WHATSAPP_CLOUD => Constants::SNS_CLIENT_WHATSAPP,
                };
                $userContact = new UserCustomerContract($clientId, $userId);
                $userContact->loadBySns($snsType, $params['sns_id'], $userSnsId);
                if ($userContact->isNew() || $userContact->customer_id != $customerId) {
                    $userContact->bind($customerId, $userId);
                }
                break;
            case Constant::CHANNEL_TYPE_TM:
                $storeId = $params['store_id'] ?? 0;
                $buyerAccountId = $params['buyer_account_id'] ?? 0;
                $sellerAccountId = $params['seller_account_id'] ?? 0;
                if (empty($storeId) || empty($buyerAccountId || empty($sellerAccountId))) {
                    LogUtil::error('store_id or buyer_account_id is empty or seller_account_id, ai_record_id: ' . $aiRecord->record_id);
                    return false;
                }
                $company = new Company($clientId, $companyId);
                if (!$company->isExist()) {
                    return false;
                }
                $service = new TMAlibabaCustomerService($clientId, $userId);
                $service->setAlibabaAccount($sellerAccountId, $storeId);
                $service->TMArchivingCustomer($company, $buyerAccountId);
                break;
        }
        return true;
    }

    public static function getAiAgentInfo($clientId, $agentId)
    {
        $aiAgentClientSettingPdo = new \common\library\ai_agent\setting\AiAgentClientSettingList($clientId);
        $aiAgentClientSettingPdo->setAgentId($agentId);
        $aiAgentClientSettingInfos = $aiAgentClientSettingPdo->find();

        $aiAgentClientSetting = $aiAgentClientSettingInfos[0] ?? [];
        $versionId = $aiAgentClientSetting['version_id'] ?? 0;


        $aiAgent = new \common\library\ai_agent\agent\AiAgent($agentId);
        $aiAgent = $aiAgent->getAttributes();
        if (empty($versionId)) {
            $versionId = $aiAgent['default_version'];
        }

        $aiAgentVersionModel = [];
        if (!empty($versionId)) {
            $aiAgentVersionModel = new \common\library\ai_agent\version\AiAgentVersion($versionId);
            $aiAgentVersionModel = $aiAgentVersionModel->getAttributes();
        }

        return [
            'agent_id' => $aiAgent['agent_id'],
            'version_id' => $aiAgentVersionModel['version_id'] ?? 0,
            'agent_name' => $aiAgent['agent_name'],
            'agent_desc' => $aiAgent['desc'],
            'scene_type' => $aiAgent['scene_type'],
            'default_version' => $aiAgent['default_version'],
            'prompt' => $aiAgentVersionModel['prompt'] ?? '',
            'model' => $aiAgentVersionModel['model'] ?? AIClient::AZURE_OPENAI_GPT_THREE,
            'stream_flag' => $aiAgentVersionModel['stream_flag'] ?? 0,
            'publish_flag' => $aiAgentVersionModel['publish_flag'] ?? 1,
        ];
    }

    public static function getAigcRiskInfo(int $clientId, int $userId, string $text, int $sceneType, int $conversationId, int $historyId, int $role, string $language)
    {
        $riskInfo = [
            'text' => $text,
            'callbackId' => $historyId,
        ];
        if (empty($clientId) || empty($userId) || empty($text)) {
            return $riskInfo;
        }

        $sceneType = AiAgentConstants::AI_SCENE_TYPE_TO_RISK_SCENE_MAP[$sceneType] ?? AiAgentConstants::AI_ICBU_RISK_SCENE_TYPE_AI_IM;
        $publishRole = ($role == AiAgentConstants::AI_AGENT_ROLE_TYPE_SYSTEM) ? AiAgentConstants::AI_ICBU_RISK_PUBLISH_ROLE_SYSTEM : AiAgentConstants::AI_ICBU_RISK_PUBLISH_ROLE_USER;
        $publishType = ($role == AiAgentConstants::AI_AGENT_ROLE_TYPE_SYSTEM) ? AiAgentConstants::AI_ICBU_RISK_PUBLISH_TYPE_ANSWER : AiAgentConstants::AI_ICBU_RISK_PUBLISH_TYPE_QUESTION;
        $contentTypeList = [AiAgentConstants::AI_ICBU_RISK_CONTENT_TYPE_TEXT];
        try {
            $topClient = \common\library\ames\Helper::getTopClient();
            $req = new AlibabaIcbuRiskSendRequest();
            $envInfo = new EnvInfo();

            $eventData = new EventData();
            $eventData->event_item = $sceneType;
            $eventData->event_item_id = $historyId;
            $eventData->callback_id = $historyId;
            [$msec, $sec] = explode(' ', microtime());
            $msectime = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);
            $eventData->event_time = $msectime;
            $eventData->event_code = AiAgentConstants::AI_ICBU_AIGC_RISK_EVENT_CODE;
            $eventData->env_info = $envInfo;

            $extendInfo = [
                'groupId' => (string)$conversationId, // 父信息ID,必填,这里填会话id
                'infoId' => (string)$historyId, // 信息id,必填,这里填recordId
                'sceneType' => $sceneType, // 一级产品名称,这里根据aiagentType取不同的值,必填
                'publishRole' => (string)$publishRole, // 二级产品名称,发布人身份,必填,1（商家）/2（系统）,必填
                'publishType' => (string)$publishType, // 三级产品名称,问答模式区分.1（问）/2（答）,必填
                'contentType' => implode(',', $contentTypeList), // 四级产品名称,内容类型（1-文字，2-图片，3-视频，4-直播，5-音频）,"1,2"表示同时有文字和图片,必填
                'domainType' => AiAgentConstants::AI_ICBU_RISK_DOMAIN_TYPE, // 五级产品名称.写死(L3),必填
                'whiteFlag' => (string)AiAgentConstants::AI_ICBU_RISK_WHITE_FLAG_FALSE, // 1-是，2-不是（1代表不用过风控扫描） --必填
                'language' => $language, // 语种(EN/ZH) ,当前业务页面会话的语种，目前只支持中英 --必填
                'textContent' => $text, // 文本内容，用户输入的原文，或者aigc回复的原文 --有文本时必填
                'textPromptContent' => "", // 文本内容，业务根据原文textContent做了prompt加工，加工后的文本内容 --无业务加工时可不传
                'textContentMd5' => md5($text), // 文本内容MD5 --有文本时必填
            ];
            $eventData->extend = $extendInfo;
            $req->setEventData(json_encode($eventData));

            $getRiskReason = function ($riskType) {
                $map = AiAgentConstants::AI_ICBU_RISK_DESC_MAP;
                if (is_numeric($riskType) && $riskType > 10000) {
                    return "命中绿网策略";
                } elseif (array_key_exists($riskType, $map)) {
                    return $map[$riskType];
                } else {
                    return "未知原因";
                }
            };

            $response =  AlibabaTopClient::object2array($topClient->execute($req));

            $resultCode = $response['result_code'] ?? 0;
            $riskInfo['resultCode'] = $resultCode;
            $riskInfo['resultDesc'] = AiAgentConstants::AI_ICBU_RISK_RESULT_CODE_TO_DESC_MAP[$resultCode] ?? '未知原因';
            if ($resultCode == AiAgentConstants::AI_ICBU_RISK_RESULT_CODE_REJECT) {
                $responseData = $response['data'] ?? [];
                $responseDataRejectItems = $responseData['reject_items']['reject_item'] ?? [];
                foreach ($responseDataRejectItems as $responseDataRejectItem) {
                    $responseDataRejectItemRiskType = $responseDataRejectItem['risk_type'] ?? 0;
                    $responseDataRejectItemRiskReason = $getRiskReason($responseDataRejectItemRiskType);
                    $riskInfo['rejectItems'][] = [
                        'riskType' => $responseDataRejectItemRiskType,
                        'riskReason' => $responseDataRejectItemRiskReason,
                    ];
                }
                LogUtil::info("okki_ai_risk_error", [$clientId, $userId, $text, $conversationId, $historyId, $role, $language, $riskInfo, $extendInfo]);
            }
        } catch (\Exception $e) {
            LogUtil::info("okki_ai_risk_api_error,msg[{$e->getMessage()}]", [$clientId, $userId, $text, $conversationId, $historyId, $role, $language]);
        }
        return $riskInfo;
    }

    public static function getAigcRiskInfoCallInfo(int $clientId, int $userId, string $text, int $sceneType, int $conversationId, int $historyId, int $role, string $language)
    {
        $riskInfo = [
            'text' => $text,
            'callbackId' => $historyId,
        ];
        if (empty($clientId) || empty($userId) || empty($text)) {
            return $riskInfo;
        }

        $requestInfo = $responseInfo = '';

        $sceneType = AiAgentConstants::AI_SCENE_TYPE_TO_RISK_SCENE_MAP[$sceneType] ?? AiAgentConstants::AI_ICBU_RISK_SCENE_TYPE_AI_IM;
        $publishRole = ($role == AiAgentConstants::AI_AGENT_ROLE_TYPE_SYSTEM) ? AiAgentConstants::AI_ICBU_RISK_PUBLISH_ROLE_SYSTEM : AiAgentConstants::AI_ICBU_RISK_PUBLISH_ROLE_USER;
        $publishType = ($role == AiAgentConstants::AI_AGENT_ROLE_TYPE_SYSTEM) ? AiAgentConstants::AI_ICBU_RISK_PUBLISH_TYPE_ANSWER : AiAgentConstants::AI_ICBU_RISK_PUBLISH_TYPE_QUESTION;
        $contentTypeList = [AiAgentConstants::AI_ICBU_RISK_CONTENT_TYPE_TEXT];
        try {
            $topClient = \common\library\ames\Helper::getTopClient();
            $req = new AlibabaIcbuRiskSendRequest();
            $envInfo = new EnvInfo();

            $eventData = new EventData();
            $eventData->event_item = $sceneType;
            $eventData->event_item_id = $historyId;
            $eventData->callback_id = $historyId;
            [$msec, $sec] = explode(' ', microtime());
            $msectime = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);
            $eventData->event_time = $msectime;
            $eventData->event_code = AiAgentConstants::AI_ICBU_AIGC_RISK_EVENT_CODE;
            $eventData->env_info = $envInfo;

            $extendInfo = [
                'groupId' => (string)$conversationId, // 父信息ID,必填,这里填会话id
                'infoId' => (string)$historyId, // 信息id,必填,这里填recordId
                'sceneType' => $sceneType, // 一级产品名称,这里根据aiagentType取不同的值,必填
                'publishRole' => (string)$publishRole, // 二级产品名称,发布人身份,必填,1（商家）/2（系统）,必填
                'publishType' => (string)$publishType, // 三级产品名称,问答模式区分.1（问）/2（答）,必填
                'contentType' => implode(',', $contentTypeList), // 四级产品名称,内容类型（1-文字，2-图片，3-视频，4-直播，5-音频）,"1,2"表示同时有文字和图片,必填
                'domainType' => AiAgentConstants::AI_ICBU_RISK_DOMAIN_TYPE, // 五级产品名称.写死(L3),必填
                'whiteFlag' => (string)AiAgentConstants::AI_ICBU_RISK_WHITE_FLAG_FALSE, // 1-是，2-不是（1代表不用过风控扫描） --必填
                'language' => $language, // 语种(EN/ZH) ,当前业务页面会话的语种，目前只支持中英 --必填
                'textContent' => $text, // 文本内容，用户输入的原文，或者aigc回复的原文 --有文本时必填
                'textPromptContent' => "", // 文本内容，业务根据原文textContent做了prompt加工，加工后的文本内容 --无业务加工时可不传
                'textContentMd5' => md5($text), // 文本内容MD5 --有文本时必填
            ];
            $eventData->extend = $extendInfo;
            $req->setEventData(json_encode($eventData));

            $getRiskReason = function ($riskType) {
                $map = AiAgentConstants::AI_ICBU_RISK_DESC_MAP;
                if (is_numeric($riskType) && $riskType > 10000) {
                    return "命中绿网策略";
                } elseif (array_key_exists($riskType, $map)) {
                    return $map[$riskType];
                } else {
                    return "未知原因";
                }
            };

            $requestInfo = $extendInfo;
            $responseInfo =  $topClient->execute($req);
            $response =  AlibabaTopClient::object2array($responseInfo);

            $resultCode = $response['result_code'] ?? 0;
            $riskInfo['resultCode'] = $resultCode;
            $riskInfo['resultDesc'] = AiAgentConstants::AI_ICBU_RISK_RESULT_CODE_TO_DESC_MAP[$resultCode] ?? '未知原因';
            if ($resultCode == AiAgentConstants::AI_ICBU_RISK_RESULT_CODE_REJECT) {
                $responseData = $response['data'] ?? [];
                $responseDataRejectItems = $responseData['reject_items']['reject_item'] ?? [];
                foreach ($responseDataRejectItems as $responseDataRejectItem) {
                    $responseDataRejectItemRiskType = $responseDataRejectItem['risk_type'] ?? 0;
                    $responseDataRejectItemRiskReason = $getRiskReason($responseDataRejectItemRiskType);
                    $riskInfo['rejectItems'][] = [
                        'riskType' => $responseDataRejectItemRiskType,
                        'riskReason' => $responseDataRejectItemRiskReason,
                    ];
                }
                LogUtil::info("okki_ai_risk_error", [$clientId, $userId, $text, $conversationId, $historyId, $role, $language, $riskInfo, $extendInfo]);
            }
        } catch (\Exception $e) {
            LogUtil::info("okki_ai_risk_api_error,msg[{$e->getMessage()}]", [$clientId, $userId, $text, $conversationId, $historyId, $role, $language]);
        }

        return [$requestInfo, $responseInfo, $riskInfo];
    }


    /**
     * 判断风控是否通过
     *
     * @param array $aigcRiskInfo
     * @return bool
     */
    public static function isRiskPassed(array $aigcRiskInfo): bool
    {
        $resultCode = (int) ($aigcRiskInfo['resultCode'] ?? AiAgentConstants::AI_ICBU_RISK_RESULT_CODE_PASS);

        if ($resultCode === AiAgentConstants::AI_ICBU_RISK_RESULT_CODE_REJECT) {
            return false;
        }

        if (!in_array($resultCode, [
            AiAgentConstants::AI_ICBU_RISK_RESULT_CODE_PENDING,
            AiAgentConstants::AI_ICBU_RISK_RESULT_CODE_PASS,
            AiAgentConstants::AI_ICBU_RISK_RESULT_CODE_UNKNOWN,
        ], true)) {
            LogUtil::error("风控返回未知code", ['aigcRiskInfo' => $aigcRiskInfo]);
        }

        return true;
    }

    public static function checkAiAgentRiskFromText($clientId, $userId, $params)
    {
        $text = $params['text'];
        try {
            $ret[$text] = '无敏感词';
            $rsp = \common\library\discovery\Helper::checkRiskOfText($clientId, $userId, $text);
        } catch (Exception $e) {
            $ret[$text] = "存在敏感词";
            return false;
        }
        return true;
    }

    /**
     * 报表生成历史数据结构转换成最新结构
     * @param $data
     * @return mixed
     */
    public static function reconstructionHistoryList($data,  $sceneType, $old2new = true)
    {
        switch ($sceneType) {
            case \AiAgent::AI_AGENT_SCENE_TYPE_GENERATION_DATA:

                foreach ($data as $index => &$item) {
                    if (!is_array($item['context']['content'])) continue;
                    self::reconstruct2Array($item['context']['content']);
                }
                break;
            default:
                break;
        }

        return $data;
    }

    /**
     * 历史结构
     * {
     * "员工名称": "pro-jolieTest",
     * "创建的客户数": "4",
     * "report_item_unique_key": "4840384fa7a201287702e948fad8c5b1",
     * "refer_list": "orderList"
     * }
     *
     * 新结构
     * {
     * "员工名称": {
     * "value": "jolie0906客户1",
     * "report_item_unique_key": "34347bdhjbcc201287702e948fad8c334",
     * "refer_list": "companyList"
     * },
     * "创建的客户数": {
     * "value": 4,
     * "report_item_unique_key": "4840384fa7a201287702e948fad8c5b1",
     * "refer_list": "orderList"
     * }
     * }
     * @param $data
     * @return void
     */
    public static function reconstruct2Array(&$data)
    {
        $newData = [];
        foreach ($data ?? [] as $k => $v) {
            foreach ($v as $field => $value) {

                // 若是数组表明是新结构，就不需要继续循环，直接跳到最外层
                if (is_array($v[$field])) {
                    $newData = $data;
                    break 2;
                }

                if (!preg_match('/[\x{4e00}-\x{9fa5}]+/u', $field)) {
                    unset($v[$field]);
                    continue;
                }

                $newData[$k][$field] = [
                    'value' => $value,
                    'refer_list' => $v['refer_list'] ?? '',
                    'report_item_unique_key' => $v['report_item_unique_key'] ?? '',
                ];

            }
        }
        $data = $newData;
    }

    public static function getRiskInfo($clientId, $creditCode)
    {

        $top = \common\library\ames\Helper::getTopClient();
        $req = new \xiaoman\AlibabaSdk\taobao\top\request\AlibabaIcbuRiskSendRequest();
        $eventData = new \xiaoman\AlibabaSdk\taobao\top\domain\EventData();
        $envInfo = new \xiaoman\AlibabaSdk\taobao\top\domain\EnvInfo();

        $eventData->event_code="icbu_ames_seller_add";
        $eventData->extend="{'identity':'ames','region':'CN','regType':'xiaoman'}";
        $eventData->event_item_id = $creditCode;
        $eventData->callback_id = $clientId;
        $eventData->event_item = "creditCode";
        $eventData->event_time = time();

        $req->setEventData(json_encode($eventData));
        $res = \common\library\alibaba\services\AlibabaTopClient::object2array($top->execute($req));

        if(!isset($res['result_code'])){
            $res_json = json_encode($res);
            \LogUtil::info("aliRisk service clientId:$clientId  error:$res_json" );
        }
        return $res;
    }

    public static function checkUserNeedRun($clientId, $userId)
    {
        // 1.判断用户是否屏蔽管理台Insight代办，假如关闭，则不推送
        $setting = new \common\library\setting\user\UserSetting($clientId, $userId, \common\library\setting\user\UserSetting::TODO_BLOCK_FEED_TYPES);
        $todoBlockFeedTypes = $setting->getValue();
        if (!empty($todoBlockFeedTypes) && in_array(\common\library\todo\TodoConstant::TODO_TYPE_STATISTIC_INSIGHT, $todoBlockFeedTypes)) {
            return false;
        }

        // 2.判断是否存在权限查看统计分析报表数据
        $privilegeService = \common\library\privilege_v3\PrivilegeService::getInstance($clientId, $userId);
        // 没有统计分析查看权限, 不能看报告
        if (!$privilegeService->hasPrivilege(PrivilegeConstants::PRIVILEGE_CRM_STATISTIC_VIEW)) {
            return false;
        }

        return true;
    }


    public static function getDslExamples() {
        // 请帮我查询下过单的客户数量
        $dsl3 = [
            'desc' => '下过单的客户数量',
            'from' => [
                'tables' => [['name' => 'tbl_order']],
            ],
            'fields' => [
                [
                    'function' => 'count',
                    'distinct' => true,
                    'field' => 'tbl_order.company_id',
                ],
            ],
        ];

        // 平均每个员工创建了多少个客户（复杂的查询字段）
        $dsl4 = [
            'desc' => '平均员工创建数量',
            'from' => [
                'tables' => [['name' => 'tbl_company']],
            ],
            'fields' => [
                [
                    'operation' => '/',
                    'operands' => [
                        [
                            'function' => 'count',
                            'distinct' => true,
                            'field' => 'tbl_company.company_id',
                        ],
                        [
                            'function' => 'count',
                            'field' => 'tbl_company.create_user',
                            'distinct' => true,
                        ],
                    ],
                ],
            ],
        ];

        // 最近5年，张三每年创建的客户数(Group by)
        $dsl5 = [
            'desc' => '近五年张三每年创建的客户数',
            'from' => [
                'tables' => [['name' => 'tbl_company']],
            ],
            'fields' => [
                [
                    'field' => 'tbl_company.create_time',
                    'function' => 'TO_CHAR',
                    'params' => ['YYYY'],
                    'alias' => 'year'
                ],
                [
                    'function' => 'count',
                    'field' => 'tbl_company.company_id',
                    'alias' => 'company_id'
                ],
            ],
            'where' =>
                [
                    [
                        'operator' => 'and',
                        'operands' => [
                            [
                                'operator' => '=',
                                'field' => 'tbl_company.create_user',
                                'value' => '张三',
                            ],
                            [
                                'operator' => '>=',
                                'field' => 'tbl_company.create_time',
                                'value' => '2018-01-01 00:00:00',
                            ]
                        ],
                    ],
                ],
            'group_by' => [
                'year',
            ],
            'order_by' => [
                'year' => 'asc'
            ],
            'limit' => 5
        ];

        $examples = [
            '请帮我查询下过单的客户数量' => $dsl3,
            '平均每个员工创建了多少个客户' => $dsl4,
            '最近5年，张三每年创建的客户数' => $dsl5,
        ];
        $prompt = '';

        foreach ($examples as $text => $json) {
            $prompt .= "- example\n";
            $prompt .= "User: $text\n";
            $prompt .= "Assistant: dsl " . json_encode($json, JSON_UNESCAPED_UNICODE) . "\n\n";
        }

        return $prompt;
    }

    public static function getAIGenerationDataPrompt($clientId,$question = '')
    {
        $datetime = date("Y-m-d H:i:s");
        $examples = self::getDslExamples();
        $tableInfo = self::getTableFieldInfo($clientId);
        $systemPrompt = <<<PROMPT
## Profile
你叫okki，是小满科技开发的AI数据统计助手。

## 注意事项
- 请不要说自己是chatgpt，或者基于chatgpt，也不要提到OPENAI.
- 如果问题跟数据查询无关，请回答：“我只能回答关于数据分析的问题”
- 数据库是postgres, 支持的函数有：COUNT/MAX/MIN/SUM/AVG/TO_CHAR
- 根据表格结构和用户的需求，生成JSON格式的DSL。
- 注意不要查询下面表格结构中不存在的表格、字段。
- 对于“上周”、“本月”等时间查询，不要使用函数，而是转换成具体的时间，如：2018-01-01 00:00:00
- 现在的时间是：$datetime

## 输出格式
- 输出DSL时，以固定的字符串"dsl "开头，不要添加任何解释和提示内容。

以下是输出例子：
dsl {"select": {"function": "count", "field": ["id"]}}

## 表格结构
格式: 字段 : 字段类型 : 字段注释
PROMPT;

        foreach ($tableInfo as $tableName => $fieldInfos) {
            $tableDesc = self::getTableDesc($tableName);
            $systemPrompt .= <<<PROMPT

表中文名: {$tableDesc}
表名:{$tableName}
PROMPT;
            foreach ($fieldInfos as $fieldName => $fieldInfo) {
                $fieldType = $fieldInfo['fieldType'] ?? '';
                $fieldDesc = $fieldInfo['desc'] ?? '';
                if (empty($fieldType)) continue;
                $systemPrompt .= <<<PROMPT

{$fieldName} : {$fieldType} : {$fieldDesc}
PROMPT;
                if (isset($fieldInfo['keys']) && is_array($fieldInfo['keys']))
                {
                    // 特殊key的含义需要追加在prompt中
                    foreach ($fieldInfo['keys'] as $name => $info) {
                        $fieldType = $info['fieldType'] ?? '';
                        $fieldDesc = $info['desc'] ?? '';
                        $extentFieldName = $fieldName . '_' . $name;
                        if (empty($fieldType)) continue;
                        $systemPrompt .= <<<PROMPT

{$extentFieldName} : {$fieldType} : {$fieldDesc}
PROMPT;
                    }
                }
            }

        }

        $systemPrompt .= <<<PROMPT


## Examples
- example
User: 可以帮我编写一个PHP代码吗？
Assistant: 抱歉，我只能帮你查询数据。
User: 你好，请问我可以查询客户总数吗？
Assistant: dsl {"desc":"客户总数","from":{"tables":[{"name":"tbl_company"}]},"fields":[{"function":"count","field":"tbl_company.company_id"}]}
User: 不好意思，我想查询的是邮箱不为空的客户数量
Assistant: dsl {"desc":"邮箱不为空的客户数量","from":{"tables":[{"name":"tbl_company"}]},"fields":[{"function":"count","field":"tbl_company.company_id"}], "where": [{"operator": "!=", "field": "tbl_company.main_customer_email", "value": ""}]}
User: 请帮我查询业务员的邮箱和密码
Assistant: 不支持这个查询。

$examples

PROMPT;

        if ($question) {
            $systemPrompt .= <<<PROMPT
## 用户需求如下:
{$question}

PROMPT;

        }
        return $systemPrompt;

    }



    public static function getTableDesc($tableName)
    {
        $map = [
            'tbl_company' => '客户表',
            'tbl_order' => '订单表',
            'tbl_opportunity' => '商机表'
        ];
        return $map[$tableName] ?? '';
    }
    public static function getTableFieldInfo($clientId='')
    {
        $tableToReferMap = [
            'tbl_company' => \Constants::TYPE_COMPANY,
            'tbl_order' => \Constants::TYPE_ORDER,
            'tbl_opportunity' => \Constants::TYPE_OPPORTUNITY
        ];

        $map =  [
            'tbl_company' => [
                'is_archive' => [
                    'fieldType' => 'int',
                    'desc' => '是否建档',
                    'callFunction' => 'getDefaultValue',
                    'realFieldType' => 'int'
                ],
                'company_id' => [
                    'fieldType' => 'int',
                    'desc' => '客户名称',
                    'callFunction' => 'getCompanyName',
                    'realFieldType' => 'int',
                    'not_show' => true,
                ],
                'name' => [
                    'fieldType' => 'string',
                    'desc' => '公司名称',
                    'realFieldType' => 'string'
                ],
                'short_name' => [
                    'fieldType' => 'string',
                    'desc' => '公司简称',
                    'realFieldType' => 'string'
                ],
                'user_id' => [
                    'fieldType' => 'string',
                    'desc' => '跟进人',
                    'callFunction' => 'getUserIds',
                    'realFieldType' => 'bigint[]'
                ],
                'group_id' => [
                    'fieldType' => 'string',
                    'desc' => '客户所属分组id',
                    'callFunction' => 'getCompanyGroupIds',
                    'realFieldType' => 'bigint'
                ],
                'country' => [
                    'fieldType' => 'string',
                    'desc' => '客户所属国家或地区',
                    'callFunction' => 'getCountrys',
                    'realFieldType' => 'string'
                ],
                'category_ids' => [
                    'fieldType' => 'string',
                    'desc' => '客户主营产品',
                    'callFunction' => 'getCategoryIds',
                    'realFieldType' => 'jsonb'
                ],
                'remark' => [
                    'fieldType' => 'text',
                    'desc' => '公司备注',
                    'realFieldType' => 'string'
                ],
                'biz_type' => [
                    'fieldType' => 'string',
                    'desc' => '客户类型',
                    'callFunction' => 'getBizTypes',
                    'realFieldType' => 'string'
                ],
                'star' => [
                    'fieldType' => 'int',
                    'desc' => '客户星级',
                    'realFieldType' => 'int'
                ],
                'trail_status' => [
                    'fieldType' => 'string',
                    'desc' => '客户阶段',
                    'callFunction' => 'getTrailStatus',
                    'realFieldType' => 'bigint'
                ],
                'external_field_data' => [
                    'fieldType' => 'string',
                    'desc' => '自定义信息',
                    'callFunction' => 'getExternalFieldData',
                    'realFieldType' => 'jsonb'
                ],
                'serial_id' => [
                    'fieldType' => 'string',
                    'desc' => '客户编号',
                    'realFieldType' => 'string'
                ],
                'scale_id' => [
                    'fieldType' => 'integer',
                    'desc' => '规模',
                    'callFunction' => 'getEnumByField',
                    'realFieldType' => 'string'
                ],
                'create_time' => [
                    'fieldType' => 'timestamp without time zone',
                    'desc' => '创建时间',
                    'realFieldType' => 'timestamp'
                ],
                'order_time' => [
                    'fieldType' => 'timestamp without time zone',
                    'desc' => '最近联系时间',
                    'realFieldType' => 'timestamp'
                ],
                'edm_time' => [
                    'fieldType' => 'timestamp without time zone',
                    'desc' => '最近发送edm时间',
                    'realFieldType' => 'timestamp'
                ],
                'send_mail_time' => [
                    'fieldType' => 'timestamp without time zone',
                    'desc' => '最近发送邮件时间',
                    'realFieldType' => 'timestamp'
                ],
                'receive_mail_time' => [
                    'fieldType' => 'timestamp without time zone',
                    'desc' => '最近收件时间',
                    'realFieldType' => 'timestamp'
                ],
                'mail_time' => [
                    'fieldType' => 'timestamp without time zone',
                    'desc' => '最近往来邮件时间',
                    'realFieldType' => 'timestamp'
                ],
                'follow_up_time' => [
                    'fieldType' => 'timestamp without time zone',
                    'desc' => '最近跟进时间',
                    'realFieldType' => 'timestamp'
                ],
                'client_tag_list' => [
                    'fieldType' => 'string',
                    'desc' => '客户公司标签',
                    'realFieldType' => 'bigint[]',
                    'callFunction' => 'getClientTagList'
                ],
                'pool_id' => [
                    'fieldType' => 'string',
                    'desc' => '公海分组id',
                    'callFunction' => 'getPoolId',
                    'realFieldType' => 'bigint',
                ],
                'public_time' => [
                    'fieldType' => 'timestamp without time zone',
                    'desc' => '进入公海时间',
                    'realFieldType' => 'timestamp'
                ],
                'archive_type' => [
                    'fieldType' => 'string',
                    'desc' => '创建方式',
                    'callFunction' => 'getArchiveType',
                    'realFieldType' => 'int'
                ],
                'performance_order_count' => [
                    'fieldType' => 'int',
                    'desc' => '成交订单数',
                    'realFieldType' => 'int'
                ],
                'success_opportunity_count' => [
                    'fieldType' => 'int',
                    'desc' => '赢单商机数',
                    'realFieldType' => 'int'
                ],
                'deal_time' => [
                    'fieldType' => 'timestamp without time zone',
                    'desc' => '最近成交时间',
                    'realFieldType' => 'timestamp'
                ],
                'edit_time' => [
                    'fieldType' => 'timestamp without time zone',
                    'desc' => '最近编辑时间',
                    'realFieldType' => 'timestamp'
                ],
                'private_time' => [
                    'fieldType' => 'timestamp without time zone',
                    'desc' => '进入私海时间',
                    'realFieldType' => 'timestamp'
                ],
                'create_user' => [
                    'fieldType' => 'string',
                    'desc' => '创建人',
                    'callFunction' => 'getUserIds',
                    'realFieldType' => 'bigint'
                ],
//                'annual_procurement' => [
//                    'fieldType' => 'int',
//                    'desc' => '年采购额',
//                    'callFunction' => 'getEnumByField',
//                    'realFieldType' => 'int'
//                ],
                'ali_store_id' => [
                    'fieldType' => 'string',
                    'desc'=> '阿里来源店铺',
                    'realFieldType' => 'bigint[]',
                    'callFunction' => 'getAliStoreIds'
                ],
                'next_follow_up_time' => [
                    'fieldType' => 'timestamp without time zone',
                    'desc' => '下次跟进时间',
                    'realFieldType' => 'timestamp'
                ],
                'recent_follow_up_time' => [
                    'fieldType' => 'timestamp without time zone',
                    'desc' => '最近跟进时间',
                    'realFieldType' => 'timestamp'
                ],
                'latest_write_follow_up_time' => [
                    'fieldType' => 'timestamp without time zone',
                    'desc' => '最新「写跟进」时间',
                    'realFieldType' => 'timestamp'
                ],
                'latest_transaction_order_time' => [
                    'fieldType' => 'timestamp without time zone',
                    'desc' => '最新成交订单日期',
                    'realFieldType' => 'timestamp'
                ],
                'latest_success_opportunity_time' => [
                    'fieldType' => 'timestamp without time zone',
                    'desc' => '最新商机赢单日期',
                    'realFieldType' => 'timestamp'
                ],
                'transaction_order_amount' => [
                    'fieldType' => 'int',
                    'desc' => '成交订单金额',
                    'realFieldType' => 'int'
                ],
                'transaction_order_amount_avg' => [
                    'fieldType' => 'int',
                    'desc' => '成交订单均价',
                    'realFieldType' => 'int'
                ],
                'success_opportunity_amount_cny' => [
                    'fieldType' => 'int',
                    'desc' => '赢单商机金额(CNY)',
                    'realFieldType' => 'int'
                ],
                'success_opportunity_amount_usd' => [
                    'fieldType' => 'int',
                    'desc' => '赢单商机金额(USD)',
                    'realFieldType' => 'int'
                ],
                'success_opportunity_amount_avg_cny' => [
                    'fieldType' => 'int',
                    'desc' => '赢单商机均价(CNY)',
                    'realFieldType' => 'int'
                ],
                'success_opportunity_amount_avg_usd' => [
                    'fieldType' => 'int',
                    'desc' => '赢单商机均价(USD)',
                    'realFieldType' => 'int'
                ],
                'ongoing_opportunity_count' => [
                    'fieldType' => 'int',
                    'desc' => '进行中的商机数',
                    'realFieldType' => 'int'
                ],
                'transaction_order_first_time' => [
                    'fieldType' => 'timestamp without time zone',
                    'desc' => '首次成交订单日期',
                    'realFieldType' => 'timestamp'
                ],
                'success_opportunity_first_time' => [
                    'fieldType' => 'timestamp without time zone',
                    'desc' => '首次商机赢单日期',
                    'realFieldType' => 'timestamp'
                ],
                'next_move_to_public_date' => [
                    'fieldType' => 'date',
                    'desc' => '下次移入公海日期',
                    'realFieldType' => 'timestamp'
                ],
                'latest_whatsapp_time' => [
                    'fieldType' => 'timestamp without time zone',
                    'desc' => '最近WhatsApp沟通时间',
                    'realFieldType' => 'timestamp'
                ],
                'customer_count' => [
                    'fieldType' => 'int',
                    'desc' => '联系人数',
                    'realFieldType' => 'int'
                ],
                'growth_level' => [
                    'fieldType' => 'string',
                    'desc' => '阿里买家身份',
                    'realFieldType' => 'integer[]',
                    'callFunction' => 'getGrowthLevel'
                ],
                'origin_list' => [
                    'fieldType' => 'string',
                    'desc' => '客户来源',
                    'realFieldType' => 'bigint[]',
                    'callFunction' => 'getOriginList'
                ],
                'transaction_order_first_amount' => [
                    'fieldType' => 'int',
                    'desc' => '首次成交订单金额',
                    'realFieldType' => 'int'
                ],
                'opportunity_flag' => [
                    'fieldType' => 'int',
                    'desc' => '是否有进行中商机',
                    'callFunction' => 'getEnumByField',
                    'realFieldType' => 'int'
                ],
            ],
            'tbl_order' => [
                'order_id' => [
                    'fieldType' => 'int',
                    'desc' => '客户名称',
                    'callFunction' => 'getOrderName',
                    'realFieldType' => 'int'
                ],
                'order_no' => [
                    'fieldType' => 'string',
                    'desc' => '订单编号',
                    'realFieldType' => 'string'
                ],
                'name' => [
                    'fieldType' => 'string',
                    'desc' => '订单名称',
                    'realFieldType' => 'string'
                ],
                'users' => [
                    'fieldType' => 'string',
                    'desc' => '订单业绩归属人',
                    'callFunction' => 'getUserIds',
                    'realFieldType' => 'jsonb',
                    // 需要特殊组装在prompt里面的key
                    'keys' => [
                        'rate' => [
                            'fieldType' => 'int',
                            'desc' => '订单业绩归属人_归属比例'
                        ],
                        'user_id' => [
                            'fieldType' => 'int',
                            'desc' => '订单业绩归属人_业绩归属人',
                            'callFunction' => 'getUserIds'
                        ]
                    ]
                ],
                'handler' => [
                    'fieldType' => 'string',
                    'desc' => '处理人',
                    'realFieldType' => 'bigint[]',
                    'callFunction' => 'getUserIds'
                ],
                'create_user' => [
                    'fieldType' => 'string',
                    'desc' => '创建人',
                    'callFunction' => 'getUserIds',
                    'realFieldType' => 'bigint',
                ],
                'status' => [
                    'fieldType' => 'string',
                    'desc' => '订单状态',
                    'callFunction' => 'getOrderStatus',
                    'realFieldType' => 'bigint',
                ],
                'company_id' => [
                    'fieldType' => 'bigint',
                    'desc' => '客户id',
                    'callFunction' => 'getCompanyName'
                ],
                'create_time' => [
                    'fieldType' => 'timestamp',
                    'desc' => '创建时间',
                    'realFieldType' => 'timestamp',
                ],
                'update_time' => [
                    'fieldType' => 'timestamp',
                    'desc' => '更新时间',
                    'realFieldType' => 'timestamp',
                ],
                'remark' => [
                    'fieldType' => 'string',
                    'desc' => '备注',
                    'realFieldType' => 'string',
                ],
                'currency' => [
                    'fieldType' => 'string',
                    'desc' => '币种',
                    'realFieldType' => 'string',
                ],
                'exchange_rate' => [
                    'fieldType' => 'int',
                    'desc' => '汇率',
                    'realFieldType' => 'int',
                ],
                'amount' => [
                    'fieldType' => 'int',
                    'desc' => '订单金额',
                    'realFieldType' => 'int',
                ],
                'external_field_data' => [
                    'fieldType' => 'string',
                    'desc' => '自定义字段',
                    'callFunction' => 'getExternalFieldData',
                    'realFieldType' => 'jsonb',
                ],
//                'addition_cost_amount' => [
//                    'fieldType' => 'int',
//                    'desc' => '附加费用总额',
//                    'realFieldType' => 'int',
//                ],
                'customer_name' => [
                    'fieldType' => 'string',
                    'desc' => '联系人名称',
                    'realFieldType' => 'string',
                ],
                'archive_type' => [
                    'fieldType' => 'string',
                    'desc' => '订单创建方式',
                    'callFunction' => 'getOrderArchiveType',
                    'realFieldType' => 'int',
                ],
                'source_type' => [
                    'fieldType' => 'string',
                    'desc' => '订单来源类型',
                    'callFunction' => 'getEnumByField',
                    'realFieldType' => 'int',
                ],
                'ali_store_id' => [
                    'fieldType' => 'string',
                    'desc' => '来源店铺',
                    'callFunction' => 'getAliStoreIds',
                    'realFieldType' => 'int',
                ],
                'ali_status_id' => [
                    'fieldType' => 'string',
                    'desc' => '阿里订单状态',
                    'callFunction' => 'getAliStatusIds',
                    'realFieldType' => 'int',
                ],
                'country' => [
                    'fieldType' => 'string',
                    'desc' => '国家地区',
                    'callFunction' => 'getCountrys',
                    'realFieldType' => 'string',
                ],
                'ali_status_name' => [
                    'fieldType' => 'string',
                    'desc' => '阿里订单状态名称',
                    'realFieldType' => 'string',
                ],
                'cost_with_tax_total' => [
                    'fieldType' => 'int',
                    'desc' => '含税成本价总金额',
                    'realFieldType' => 'int',
                ],
                'first_order_flag' => [
                    'fieldType' => 'string',
                    'desc' => '是否为成交首单',
                    'realFieldType' => 'int',
                ],
                'first_collection_date' => [
                    'fieldType' => 'timestamp',
                    'desc' => '首次回款时间',
                    'realFieldType' => 'timestamp',
                ],
                'last_collection_date' => [
                    'fieldType' => 'timestamp',
                    'desc' => '最近回款时间',
                    'realFieldType' => 'timestamp',

                ],
                'finish_collection_date' => [
                    'fieldType' => 'timestamp',
                    'desc' => '回款完成时间',
                    'realFieldType' => 'timestamp',
                ],
                'last_order_status_update_time' => [
                    'fieldType' => 'timestamp',
                    'desc' => '最近订单状态变更时间',
                    'realFieldType' => 'timestamp',
                ],
                'account_date' => [
                    'fieldType' => 'timestamp',
                    'desc' => '业务结算日期',
                    'realFieldType' => 'timestamp',
                ]
            ],
            'tbl_opportunity' => [
                'opportunity_id' => [
                    'fieldType' => 'string',
                    'desc' => '商机名称',
                    'realFieldType' => 'string',
                    'callFunction' => 'getOpportunityName'
                ],
                'serial_id' => [
                    'fieldType' => 'string',
                    'desc' => '商机编号',
                    'realFieldType' => 'string',
                ],
                'name' => [
                    'fieldType' => 'string',
                    'desc' => '商机名称',
                    'realFieldType' => 'string',
                ],
                'handler' => [
                    'fieldType' => 'string',
                    'desc' => '跟进人',
                    'realFieldType' => 'bigint[]',
                    'callFunction' => 'getUserIds'
                ],
                'main_user' => [
                    'fieldType' => 'string',
                    'desc' => '负责人',
                    'callFunction' => 'getUserIds',
                    'realFieldType' => 'bigint',

                ],
                'create_user' => [
                    'fieldType' => 'string',
                    'desc' => '创建人',
                    'callFunction' => 'getUserIds',
                    'realFieldType' => 'bigint',
                ],
                'company_id' => [
                    'fieldType' => 'bigint',
                    'desc' => '客户id',
                    'realFieldType' => 'bigint',
                    'callFunction' => 'getCompanyName'
                ],
                'currency' => [
                    'fieldType' => 'string',
                    'desc' => '币种',
                    'realFieldType' => 'string',
                ],
                'amount' => [
                    'fieldType' => 'int',
                    'desc' => '商机金额',
                    'realFieldType' => 'int',
                ],
                'fail_type' => [
                    'fieldType' => 'string',
                    'desc' => '失败类型',
                    'callFunction' => 'getFailTypes',
                    'realFieldType' => 'bigint',
                ],
                'fail_stage' => [
                    'fieldType' => 'string',
                    'desc' => '输单阶段',
                    'callFunction' => 'getFailStages',
                    'realFieldType' => 'bigint',
                ],
                'origin' => [
                    'fieldType' => 'string',
                    'desc' => '商机来源',
                    'realFieldType' => 'bigint',
                    'callFunction' => 'getOriginList'
                ],
                'stage_type' => [
                    'fieldType' => 'string',
                    'desc' => '商机状态',
                    'realFieldType' => 'bigint',
                    'callFunction' => 'getEnumByField'
                ],
                'stage' => [
                    'fieldType' => 'string',
                    'desc' => '销售阶段',
                    'realFieldType' => 'bigint',
                    'callFunction' => 'getOpportunityStage'
                ],
                'remark' => [
                    'fieldType' => 'string',
                    'realFieldType' => 'string',
                    'desc' => '备注',
                ],
                'external_field_data' => [
                    'fieldType' => 'string',
                    'realFieldType' => 'jsonb',
                    'desc' => '自定义信息',
                    'callFunction' => 'getExternalFieldData'
                ],
                'account_date' => [
                    'fieldType' => 'timestamp',
                    'desc' => '商机结束日期',
                    'realFieldType' => 'timestamp',

                ],
                'edit_time' => [
                    'fieldType' => 'timestamp without time zone',
                    'desc' => '商机最近编辑时间',
                    'realFieldType' => 'timestamp',

                ],
                'succeed_time' => [
                    'fieldType' => 'timestamp',
                    'desc' => '赢单时间',
                    'realFieldType' => 'timestamp',

                ],
                'create_time' => [
                    'fieldType' => 'timestamp',
                    'desc' => '创建时间',
                    'realFieldType' => 'timestamp',
                ],
                'department' => [
                    'fieldType' => 'string',
                    'desc' => '业绩归属部门',
                    'callFunction' => 'getDepartmentIds',
                    'realFieldType' => 'int',

                ],
                'flow_id' => [
                    'fieldType' => 'string',
                    'desc' => '商机所属流程id',
                    'callFunction' => 'getFlowIds',
                    'realFieldType' => 'bigint',
                ],
                'disable_flag' => [
                    'fieldType' => 'integer',
                    'desc' => '是否归档状态',
                    'callFunction' => 'getEnumByField',
                    'realFieldType' => 'bigint',
                ],
                'client_tag_list' => [
                    'fieldType' => 'string',
                    'desc' => '商机标签',
                    'realFieldType' => 'bigint[]',
                    'callFunction' => 'getClientTagList'
                ]
            ],
        ];
        foreach ($map as $tableName => $fieldArr) {
            foreach ($fieldArr as $fieldName => $item) {
                // 自定义字段实现
                if ($fieldName == 'external_field_data') {
                    if (!isset($tableToReferMap[$tableName])) continue;
                    $referType = $tableToReferMap[$tableName];
                    $fieldQuery = new FieldList($clientId);
                    $fieldQuery->setType($referType);
                    $fieldQuery->setBase(0);
                    $fieldQuery->setIsList(0);
                    $fieldQuery->setDisableFlag(0);
                    $fieldQuery->setLimit(100);
                    $fieldQuery->setFields(['id', 'name','field_type']);
                    $data = $fieldQuery->find();
                    foreach ($data as $datum) {
                        $map[$tableName][$fieldName]['keys'][$datum['id']] = [
                            'fieldType' => 'string',
                            'realFieldType' => 'string',
                            'desc' => $datum['name'],
                            'callFunction' => 'getExternalFieldData'
                        ];
                    }
                }
            }
        }
        return $map;
    }


    public static function getOrderStatus($field, $value) {
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $list = CustomerOptionService::getCustomerStatusList($clientId);
        $map = array_column($list,'status_id','status_name');
        return $map[$value] ?? $value;
    }
    public static function getCountrys($field, $value) {
        $list = array_map(function ($item) {
            return [
                'code' => $item['alpha2'],
                'name' => $item['country_name'],
            ];
        }, \CountryService::getAllCountrys()
        );
        $map = array_column($list,'code','name');
        return $map[$value] ?? $value;

    }

    public static function getEnumByField($field, $value) {
        switch ($field) {
            case 'disable_flag':
                $map = [
                    '已归档' => 1,
                    '未归档' => 2,
                ];
                break;
            case 'stage_type':
                $map = [
                    '进行中' =>   OpportunityStage::STAGE_FAIL_STATUS,
                    '赢单' =>   OpportunityStage::STAGE_WIN_STATUS,
                    '输单' =>   OpportunityStage::STAGE_FAIL_STATUS,
                ];
                break;
            case 'source_type':
                $map = [
                    'CRM订单' => Order::TYPE_CRM_ORDER,
                    '信保订单' => Order::TYPE_ALI_ORDER,
                    'e收汇订单' => Order::TYPE_DIRECT_PAY_ORDER,
                    '轻易云同步' => Order::TYPE_ERP,
                ];
                break;
            case 'opportunity_flag':
                $map = [
                    '是' => 1,
                    '否' => 0
                ];
                break;
            case 'annual_procurement':
                $list = \CustomerOptionService::annualProcurementMap();
                $map = [];
                foreach ($list as $key => $item) {
                    $map[$item] = $key;
                }
                break;
            case 'scale_id':
                $scaleIdMap = \common\library\customer_v3\company\orm\Company::SCALE_MAP;
                $map = [];
                foreach ($scaleIdMap as $key => $item) {
                    $map[$item] = $key;
                }
                break;
            default:
                $map = [];
        }
        return $map[$value] ?? $value;
    }

    public static function getOpportunityStage($field, $value) {
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $api = new StageApi($clientId);
        $list = $api->listAll();
        $map = array_column($list,'stage_id','name');
        return $map[$value] ?? $value;
    }


    public static function getOriginList($field, $value) {
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $list = \CustomerOptionService::getOriginTree($clientId, 0);
        $map = array_column($list,'origin_id','origin_name');
        return $map[$value] ?? $value;
    }


    public static function getFailStages($field, $value) {
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $api = new StageApi($clientId);
        $api->setStageType( OpportunityStage::STAGE_FAIL_STATUS);
        $list = $api->listAll();
        // todo 可能会有重名的情况
        $map = array_column($list,'stage_id','name');
        return $map[$value] ?? $value;

    }

    public static function getFailTypes($field, $value) {
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $list = \common\library\opportunity\stage\Helper::getFailReasonListMap($clientId);
        $map = array_column($list,'reason_id','name');
        return $map[$value] ?? $value;
    }


    public static function getAliStatusIds($field, $value) {
        $list = \common\library\invoice\Helper::getAliStatusMaps();
        $map = array_column($list,'ali_status_id','status_name');
        return $map[$value] ?? $value;
    }

    public static function getAliStoreIds($field, $value)
    {
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $alibabaStore = new \common\library\alibaba\store\AlibabaStoreList($clientId);
        $alibabaStore->setFields(['store_id', 'store_name']);
        $list = $alibabaStore->find();
        $map = array_column($list,'store_id','store_name');
        return $map[$value] ?? $value;

    }

    public static function getOrderArchiveType($field,$value) {
        $map = [
            '手动创建' => Order::ARCHIVE_TYPE_NORMAL,
            'AI创建' => Order::ARCHIVE_TYPE_AI,
            '智能导入' => Order::ARCHIVE_TYPE_IMPORT,
            'open_api同步' => Order::ARCHIVE_TYPE_OPEN_API,
            '阿里巴巴同步' => Order::ARCHIVE_TYPE_ALIBABA,
            '小满-轻易云同步金蝶云星辰订单创建' => Order::ARCHIVE_TYPE_KINGDEE,
            '小满-轻易云同步畅捷通订单创建' => Order::ARCHIVE_TYPE_CHANJET,
            '小满-轻易云同步金蝶云星空订单创建' => Order::ARCHIVE_TYPE_GALAXY,
            '粘贴录入' => Order::ARCHIVE_TYPE_PASTE_INPUT,
            '批量导入' => Order::ARCHIVE_TYPE_BATCH_IMPORT,
        ];
        return $map[$value] ?? $value;
    }

    public static function getArchiveType($field, $value)
    {
        $map = [
            '手动创建' => Company::ARCHIVE_TYPE_NORMAL,
            '自动创建' => Company::ARCHIVE_TYPE_AI,
            '自动化建档建议' => Company::ARCHIVE_TYPE_ADVICE,
            '客户通同步' => Company::ARCHIVE_TYPE_ALIBABA,
            '文件导入' => Company::ARCHIVE_TYPE_IMPORT,
            'OKKI名片夹创建' => Company::ARCHIVE_TYPE_CARD,
        ];
        return $map[$value] ?? $value;
    }

    public static function getPoolId($field, $value) {
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $list = (new PoolApi($clientId))->list(\common\library\customer\pool\Helper::getUserPoolIds($clientId, $userId, true), true);
        $map = array_column($list,'pool_id','name');

        return $map[$value] ?? $value;

    }


    public static function getClientTagList($field,$value) {
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();
        $tag = (new \common\library\setting\library\tag\TagApi($clientId, \Constants::TYPE_COMPANY, $userId));
        $tag->setOwnerUser([0],1);
        $list = $tag->list([]);
        $map = array_column($list,'tag_id','tag_name');
        return $map[$value] ?? $value;
    }

    public static function getExternalFieldData($field,$value)
    {
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $fieldQuery = new \common\library\custom_field\FieldList($clientId);
        $fieldQuery->setIsList(0);
        $fieldQuery->setFields(['id','name']);
        $fieldQuery->setDisableFlag(0);
        $list  = $fieldQuery->find();
        $map = array_column($list,'id','name');
        return $map[$value] ?? $value;

    }

    public static function getTrailStatus($field, $value) {
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $name = \CustomerOptionService::getStatusByName($clientId, $value);
        if (empty($name)) {
            return $value;
        }
        return $name;
    }

    /**
     * 获取不同渠道的消息列表
     * @param int $channelType
     * @param $clientId
     * @param $userId
     * @param $conversationId
     * @param mixed $limit
     * @return mixed
     * @throws \ProcessException
     */
    protected static function getMessageList(int $channelType, $clientId, $userId, $conversationId, int $limit, string $snsId = '', string $userSnsId = ''): mixed
    {
        $messageService = self::getMessageService($channelType, $clientId, $userId, $snsId, $userSnsId, (int) $conversationId);
        self::$messageService = $messageService;

        if (!$conversationId) {
            $messageList = $messageService->getRecentMessages(0, $limit, channelType: $channelType);
        } else {
            $messageList = $messageService->getRecentMessages($conversationId, $limit, channelType: $channelType)['list'] ?? [];
        }
        return $messageList;
    }

    /**
     * 格式化消息为prompt格式，即"Customer: xxx   Salesman: yyy"
     * @param array $messageList
     * @return array
     */
    protected static function formatMessageToPrompt(array $messageList, ?int $channelType = null): array
    {
        $validMessageList = [];
        $lastMessageTime = 0;
        $messageList = array_reverse($messageList);
        foreach ($messageList as $message) {
            if (!isset($message['message_type']) || !isset($message['message_body'])) {
                continue;
            }
            if (!isset($message['from'])) {
                continue;
            }
            // 系统发送且失败
            if ($message['from'] === MessageService::FROM_TYPE_OKKI && !empty($message['error_code'])) {
                continue;
            }
            $messageBody = MessageBody::make($message['message_type'], $message['message_body']);
            $role = $message['from'] === MessageService::FROM_TYPE_OKKI ? 'Salesman' : 'Customer';
            $promptText = $messageBody->getPromptText($channelType);
            $sendTime = $message['send_time'] / 1_000_000;

            if ($promptText) {
                // 如果上条消息已经超过1天，插入标记
                if ($dayDiff = ChatService::getDateDiffText($lastMessageTime, $sendTime)) {
                    $validMessageList[] = $dayDiff;
                }

                $validMessageList[] = "{$role}: $promptText";
                $lastMessageTime = $sendTime;
            }
        }

        return $validMessageList;
    }

    protected static function formatContactMessageToPrompt(array $messageList, ?int $channelType = null): array
    {
        $messageList = array_reverse($messageList);
        $validMessageList = [];
        $lastMessageTime = 0;
        foreach ($messageList as $message) {
            if (!isset($message['type']) || !isset($message['body'])) {
                continue;
            }
            $sendTime = strtotime($message['send_time']);
            $messageBody = MessageBuilder::loadMessage(trim($message['type']), $message['body']);

            $role = $message['send_type'] == CustomerContactHelper::SEND_TYPE_BY_USER ? 'Salesman' : 'Customer';
            $promptText = $messageBody->getPromptText($channelType);
            if (!empty($promptText)) {
                // 如果上条消息已经超过1天，插入标记
                if ($dayDiff = ChatService::getDateDiffText($lastMessageTime, $sendTime)) {
                    $validMessageList[] = $dayDiff;
                }
                $validMessageList[] = "$role: $promptText";
                $lastMessageTime = $sendTime;
            }
        }

        return $validMessageList;
    }

    /**
     * @param int $channelType
     * @param array $recentMessageList
     * @param mixed $messageList
     * @return array|mixed
     */
    protected static function mergeRecentMessages(int $channelType, array $recentMessageList, mixed $messageList): mixed
    {
        if ($channelType === Constant::CHANNEL_TYPE_WHATSAPP) {
            $messageList = array_merge($recentMessageList, $messageList);
            // 按照message_id去重
            $messageIds = [];
            foreach ($messageList as $key => $message) {
                if (in_array($message['message_id'], $messageIds)) {
                    unset($messageList[$key]);
                } else {
                    $messageIds[] = $message['message_id'];
                }
            }
            // 按照时间排序
            usort($messageList, function ($a, $b) {
                return strtotime($a['send_time']) < strtotime($b['send_time']) ? 1 : -1;
            });
        }
        return $messageList;
    }

    /**
     * @param int $channelType
     * @param $clientId
     * @param $userId
     * @param string $snsId
     * @param string $userSnsId
     * @return \common\library\facebook\page\FacebookMessageService|UserContactMessageService|\common\library\visitor_marketing\LiveChatMessageService|\common\library\waba\WabaMessageService|\common\library\facebook\page\InstagramMessageService
     */
    public static function getMessageService(int $channelType, $clientId, $userId, string $snsId, string $userSnsId, int $conversationId = 0)
    {
        $channelMap = [
            Constant::CHANNEL_TYPE_WHATSAPP => 'whatsapp',
            Constant::CHANNEL_TYPE_TM => 'tm',
            Constant::CHANNEL_TYPE_FACEBOOK => 'facebook_page',
            Constant::CHANNEL_TYPE_INSTAGRAM => 'instagram',
            Constant::CHANNEL_TYPE_WABA => 'whatsapp_business',
            Constant::CHANNEL_TYPE_WHATSAPP_CLOUD => 'whatsapp_cloud',
        ];
        if(!$conversationId &&
            in_array($channelType, [Constant::CHANNEL_TYPE_FACEBOOK, Constant::CHANNEL_TYPE_INSTAGRAM, Constant::CHANNEL_TYPE_WABA])) {
            $messageService = new UserContactMessageService($clientId, $userId);
            $messageService->setSnsId($snsId);
            $messageService->setUserSnsId($userSnsId);
            $messageService->setChannelType($channelMap[$channelType]);
            return $messageService;
        }

        switch ($channelType) {
            case Constant::CHANNEL_TYPE_WABA:
                $messageService = new \common\library\waba\WabaMessageService($clientId, $userId);
                break;
            case Constant::CHANNEL_TYPE_FACEBOOK:
                $messageService = new \common\library\facebook\page\FacebookMessageService($clientId, $userId);
                break;
            case Constant::CHANNEL_TYPE_INSTAGRAM:
                $messageService = new \common\library\facebook\page\InstagramMessageService($clientId, $userId);
                break;
            case Constant::CHANNEL_TYPE_LIVECHAT:
                $messageService = new \common\library\visitor_marketing\LiveChatMessageService($clientId, $userId);
                break;
            case Constant::CHANNEL_TYPE_WHATSAPP_CLOUD:
                $messageService = new WhatsappCloudMessageService($clientId, $userId);
                break;
            case Constant::CHANNEL_TYPE_WHATSAPP:
            case Constant::CHANNEL_TYPE_TM:
                $messageService = new UserContactMessageService($clientId, $userId);
                $messageService->setSnsId($snsId);
                $messageService->setUserSnsId($userSnsId);
                $messageService->setChannelType($channelMap[$channelType]);
                break;
            default:
                throw new \RuntimeException("暂不支持该沟通场景");
        }
        return $messageService;
    }

    /**
     * 前端 agent 分组折叠配置
     * @param mixed $sceneType
     * @return array|null
     */
    public static function getGroupConfig(int $sceneType): array|null
    {
        foreach (AiAgentConstants::AI_AGENT_GROUPS as $groupConfig) {
            if (in_array($sceneType, $groupConfig['scene_types'], true)) {
                return $groupConfig;
            }
        }
        return null;
    }

    /**
     * @param int $conversation_id
     * @param User $user
     * @param array $record_ids
     * @param int $favorite
     * @param string $remark
     * @param array $tag_list
     * @param array $events
     * @return void
     * @throws \Exception
     */
    public static function feedback(
        int $conversation_id, User $user, array $record_ids, int $favorite,
        string $remark, array $tag_list, array $events): void
    {
        // 假如存在会话ID，获取会话ID，假如无会话ID，那么是insight场景
        $agentVersionId = 0;
        if (!empty($conversation_id)) {
            $agentInfo = Helper::getAiAgentInfoByConversationId($user->getClientId(), $conversation_id);
            $agentVersionId = $agentInfo['version_id'];
            $agentId = $agentInfo['agent_id'];
            $sceneType = $agentInfo['scene_type'];
            $businessType = $agentInfo['business_type'];
        } else {
            // 通过record_id反查sceneType
            $firstRecordId = $record_ids[0];

            $aiProcessRecord = new \common\library\ai_agent\record\AiServiceProcessRecord($firstRecordId);
            $sceneType = $aiProcessRecord->scene_type;
            $businessType = $aiProcessRecord->business_type;

            $agentInfo = \AiAgent::findBySceneType($sceneType);

            if (empty($agentInfo)) {
                throw new \RuntimeException("不支持的AI Agent");
            }

            $agentId = $agentInfo->agent_id;
            $sceneType = $agentInfo->scene_type;
        }

        // 获取历史Id，假如recordId大于2，那么是insight场景，无historyId
        $historyId = 0;
        if (!in_array($sceneType, [\AiAgent::AI_AGENT_SCENE_TYPE_STATISTICAL_ANALYSIS_INSIGHT, \AiAgent::AI_AGENT_SCENE_TYPE_ASSET_ANALYSIS])) {
            $recordId = $record_ids[0];
            $aiAgentConversationHistory = AiAgentConversationHistory::findHistoryByRecordIdAndRole(
                $user->getClientId(),
                $recordId,
                \common\library\ai_agent\AiAgentConstants::AI_AGENT_ROLE_TYPE_SYSTEM
            );
            $historyId = $aiAgentConversationHistory->history_id ?? 0;
        }

        // 查询已经存在的反馈信息，差集则为需要插入的
        $aiServiceRecordFeedBackPdo = new \common\library\ai_service\AiServiceRecordFeedbackList($user->getClientId(), $user->getUserId());
        $aiServiceRecordFeedBackPdo->setRecordId($record_ids);
        $updateRecordIds = $aiServiceRecordFeedBackPdo->find();

        $updateRecordInfos = array_column($updateRecordIds, null, 'record_id');
        $updateRecordIds = array_column($updateRecordIds, 'record_id');

        foreach ($record_ids as $recordId) {
            if (in_array($recordId, $updateRecordIds)) {
                $feedbackId = $updateRecordInfos[$recordId]['feedback_id'];
                $aiServiceRecordFeedBackPdo = new \common\library\ai_service\AiServiceRecordFeedback($user->getClientId(), $user->getUserId(), $feedbackId);
            } else {
                $aiServiceRecordFeedBackPdo = new \common\library\ai_service\AiServiceRecordFeedback($user->getClientId(), $user->getUserId());
                $aiServiceRecordFeedBackPdo->setRecordId($recordId);
            }

            $aiServiceRecordFeedBackPdo->setFavorite($favorite);
            $aiServiceRecordFeedBackPdo->setRemark($remark);
            $aiServiceRecordFeedBackPdo->setTagList($tag_list);
            $aiServiceRecordFeedBackPdo->setAgentVersionId($agentVersionId);
            $aiServiceRecordFeedBackPdo->setHistoryId($historyId);
            $aiServiceRecordFeedBackPdo->setConversationId($conversation_id);
            $aiServiceRecordFeedBackPdo->setAgentId($agentId);
            $aiServiceRecordFeedBackPdo->setSceneType($sceneType);
            $aiServiceRecordFeedBackPdo->setBusinessType($businessType);

            $recordInfo = $updateRecordInfos[$recordId] ?? [];
            foreach ($events as $eventInfo) {
                $event = $eventInfo['event'] ?? '';
                $params = $eventInfo['params'] ?? [];

                switch ($event) {
                    case 'click_event':
                        $oldCount = $recordInfo['click_count'] ?? 0;
                        $newCount = $params['count'] ?? 0;
                        $aiServiceRecordFeedBackPdo->setClickCount(($oldCount + $newCount));
                        break;
                    case 'copy_event':
                        $oldCount = $recordInfo['copy_count'] ?? 0;
                        $newCount = $params['count'] ?? 0;
                        $aiServiceRecordFeedBackPdo->setCopyCount(($oldCount + $newCount));
                        break;
                }
            }

            $aiServiceRecordFeedBackPdo->save();
        }
    }

    /**
     * @param int $clientId
     * @param int $userId
     * @param int $referId
     * @param int $favorite
     * @param string $comment
     * @param int $backgroundCheckType
     * @return void
     * @throws \Exception
     */
    public static function backgroundCheckFeedback
    (
        int $clientId,
        int $userId,
        int $referId,
        int $favorite,
        string $comment,
        int $backgroundCheckType
    ) {
        // 查询已经存在的反馈信息，如果存在则更新，不存在则插入
        $aiServiceRecordFeedBackPdo = new \common\library\ai_service\AiServiceRecordFeedbackList($clientId, $userId);
        $aiServiceRecordFeedBackPdo->setReferId($referId);
        $aiServiceRecordFeedBackPdo->setSceneType([$backgroundCheckType]);
        $updateReferIds = $aiServiceRecordFeedBackPdo->find();
        $updateRecordInfos = array_column($updateReferIds, null, 'refer_id');
        $feedbackId = $updateRecordInfos[$referId]['feedback_id'] ?? null;
        $aiServiceRecordFeedBackPdo = new \common\library\ai_service\AiServiceRecordFeedback($clientId, $userId, $feedbackId);
        $aiServiceRecordFeedBackPdo->setReferId($referId);
        // DDL中并未设置 record_id 的默认值，所以这里需要设置为0，其他不需要主动设置的字段都有默认值
        $aiServiceRecordFeedBackPdo->setRecordId(0);
        $aiServiceRecordFeedBackPdo->setFavorite($favorite);
        // 使用现有的remark字段来存储反馈的评论信息
        $aiServiceRecordFeedBackPdo->setRemark($comment);
        // 使用现有的scene_type字段来存储反馈的背景调查类型，36代表联系人，37代表公司
        $aiServiceRecordFeedBackPdo->setSceneType($backgroundCheckType);
        $aiServiceRecordFeedBackPdo->save();
    }

    /**
     * @param int $clientId
     * @param int $userId
     * @param int $referId
     * @return array
     */
    public static function getBackgroundCheckFeedback
    (
        int $clientId,
        int $userId,
        int $referId
    ): array {
        $aiServiceRecordFeedBackPdo = new \common\library\ai_service\AiServiceRecordFeedbackList($clientId, $userId);
        $aiServiceRecordFeedBackPdo->setReferId($referId);
        $toFetchReferIds = $aiServiceRecordFeedBackPdo->find();
        $toFetchRecordInfos = array_column($toFetchReferIds, null, 'scene_type');
        $result = [
            AiAgent::AI_AGENT_SCENE_TYPE_BACKGROUND_CHECK_CONTACTS => [
                'favorite' => $toFetchRecordInfos[AiAgent::AI_AGENT_SCENE_TYPE_BACKGROUND_CHECK_CONTACTS]['favorite'] ?? 0,
                'comment' => $toFetchRecordInfos[AiAgent::AI_AGENT_SCENE_TYPE_BACKGROUND_CHECK_CONTACTS]['remark'] ?? ''
            ],
            AiAgent::AI_AGENT_SCENE_TYPE_BACKGROUND_CHECK_COMPANY => [
                'favorite' => $toFetchRecordInfos[AiAgent::AI_AGENT_SCENE_TYPE_BACKGROUND_CHECK_COMPANY]['favorite'] ?? 0,
                'comment' => $toFetchRecordInfos[AiAgent::AI_AGENT_SCENE_TYPE_BACKGROUND_CHECK_COMPANY]['remark'] ?? ''
            ],
            AiAgent::AI_AGENT_SCENE_TYPE_BACKGROUND_CHECK_EMAIL => [
                'favorite' => $toFetchRecordInfos[AiAgent::AI_AGENT_SCENE_TYPE_BACKGROUND_CHECK_EMAIL]['favorite'] ?? 0,
                'comment' => $toFetchRecordInfos[AiAgent::AI_AGENT_SCENE_TYPE_BACKGROUND_CHECK_EMAIL]['remark'] ?? ''
            ]
        ];
        return $result;
    }

    public static function getAiPortraitFeedback(int $clientId, int $userId, int $recordId): array
    {
        $aiServiceRecordFeedBackPdo = new \common\library\ai_service\AiServiceRecordFeedbackList($clientId, $userId);
        $aiServiceRecordFeedBackPdo->setRecordId($recordId);
        $toFetchReferIds = $aiServiceRecordFeedBackPdo->find();

        if (empty($toFetchReferIds)) {
            return ['favorite' => 0];
        }

        $toFetchRecordInfos = array_column($toFetchReferIds, null, 'scene_type');
        return [
            'favorite' => $toFetchRecordInfos[AiAgent::AI_AGENT_SCENE_TYPE_COMMUNICATE_ANALYSIS]['favorite'] ?? 0,
            'comment' => $toFetchRecordInfos[AiAgent::AI_AGENT_SCENE_TYPE_COMMUNICATE_ANALYSIS]['remark'] ?? ''
        ];
    }

    /**
     * @param int $agentId
     * @param mixed $scene_type
     * @param int $conversation_id
     * @param int|string $clientId
     * @param int $userId
     * @param mixed $conversationId
     * @param int $page_size
     * @param int $page
     * @param int $history_id
     * @param array $params
     * @param int $agent_id
     * @return array
     */
    public static function getConversationHistory(
        int $agentId, int $scene_type, int $conversation_id, int $clientId,
        int $userId, int $conversationId, int $page_size, int $page,
        int $history_id, array $params, int $agent_id,
        int $businessType = AiAgentConstants::BUSINESS_TYPE_CRM): array
    {
        if (!empty($agentId)) {
            $aiAgentModel = \AiAgent::model()->findByPk($agentId);
            $scene_type = $aiAgentModel->scene_type;
        }

        if (empty($conversation_id)) {
            $latestConversationInfo = Helper::getLatestConversationInfoBySceneType($clientId, $userId, $scene_type, $businessType);

            if (empty($latestConversationInfo)) {
                return [];
            }

            $agentId = $latestConversationInfo['agent_id'];
            $conversationId = $latestConversationInfo['conversation_id'];
        }


        $historyList = new AiAgentConversationHistoryList($clientId, $userId, $agentId);
        $historyList->setBusinessType($businessType);
        $historyList->setLimit($page_size);
        $historyList->setOffset(($page - 1) * $page_size);
        $historyList->getFormatter()->showFeedbackInfo(true);
        $history_id && $historyList->setLessThanHistoryId($history_id, true);
        $historyList->setOrderBy(['conversation_id', 'history_id']);
        $historyList->setOrder('desc');

        if (in_array($scene_type, PromptConstant::SCENE_TYPE_HISTORY_OWNERBY_COMMUNICATION) && empty($params)) {
            $extInfoParams = ['user_sns_id', 'sns_id'];

            if (in_array($scene_type, PromptConstant::SCENE_TYPE_HISTORY_OWNERBY_MAIL_ID)) {
                $extInfoParams = ['mail_id'];
            }
            // 沟通场景 按 [UserID+agentID+沟通渠道账号+会话联系人] 进行区分
            $lastHistory = Helper::getLastHistoryOfConversation($clientId, $userId, $agent_id, $conversationId, $extInfoParams);
            if (!$lastHistory) {
                return [];
            }
            $params = json_decode($lastHistory['ext_info'], true) ?? [];
        }

        $historyList->setLessThanConversationId($conversationId); // 必须小于当前会话

        if (in_array($scene_type, PromptConstant::SCENE_TYPE_HISTORY_OWNERBY_COMMUNICATION))
        {
            $historyList->setUserSnsId($params['user_sns_id'] ?? 0);
            $historyList->setSnsId($params['sns_id'] ?? 0);
        }

        if (in_array($scene_type, PromptConstant::SCENE_TYPE_HISTORY_OWNERBY_MAIL_ID)) {
            $historyList->setMailId($params['mail_id'] ?? 0);
        }

        $list = $historyList->find();

        // 反转数组 最新的记录应该在最下面
        $list = array_reverse($list);
        return $list;
    }

    public static function chatCompletions(
        User $user, $conversationId, int $scene_type, string $question = '',
        array $params = [], bool $fromAssistant = false, int $businessType = AiAgentConstants::BUSINESS_TYPE_CRM)
    {
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        // 兼容逻辑，判断是否是历史版本， 历史版本会直接传conversation_id过来 导致没办法保存预置信息
        $aiAgent = AiAgentFactory::createAgent($scene_type, $clientId, $userId, $businessType);

        $aiAgent->requestParams = $params = empty($params) ? $aiAgent->loadParams($conversationId) : $params;

        $oldVersionFirstQuestionFlag = false;

        if (!isset($params['params'])) {
            $oldVersionFirstQuestionFlag = empty($question) && !empty($params) && !empty($conversationId);
            $params['params'] = $params;
        }

        if (empty($scene_type) && empty($conversationId)) {
            throw new \RuntimeException("参数错误");
        }

        // 报表生成不保存记录·
        $savePreMessageFlag = $oldVersionFirstQuestionFlag && $scene_type != \AiAgent::AI_AGENT_SCENE_TYPE_GENERATION_DATA;

        if (empty($conversationId) || $oldVersionFirstQuestionFlag) {
            $aiAgentModel = \AiAgent::model()::findBySceneType($scene_type);
            $agentId = $aiAgentModel->agent_id;

            if (empty($conversationId)) {
                $agentConversation = new AiAgentConversation();
                $agentConversation->conversation_id = PgActiveRecord::produceAutoIncrementId(1);
                $agentConversation->client_id = $clientId;
                $agentConversation->user_id = $userId;
                $agentConversation->agent_id = $aiAgentModel->agent_id;
                $agentConversation->business_type = $businessType;
                $agentConversation->insert();
                $conversationId = $agentConversation->conversation_id;
            }


            $aiAgent->setConversationId($conversationId);

            // 旧版本报表生成不保存历史记录
            if (($oldVersionFirstQuestionFlag && $scene_type != \AiAgent::AI_AGENT_SCENE_TYPE_GENERATION_DATA) || empty($question)) {
                $aiAgent->savePressMessageHistory($params);
            }

        } else {
            // 追问场景
            $conversation = AiAgentConversation::model()->findByPk($conversationId);

            if (empty($conversation)) {
                throw new \RuntimeException("会话不存在");
            }

            $agentId = $conversation->agent_id;
        }

        $client = \Client::findByClientId($clientId);
        LogUtil::info("chatCompletionAfterCheckConversation client_type_{$client->client_type}", ['conversation_id' => $conversationId, 'agent_id' => $agentId]);

        $aiAgentModel = \AiAgent::model()->findByPk($agentId);

        if (empty($aiAgentModel)) {
            throw new \RuntimeException("不支持的AI Agent");
        }

        LogUtil::info("chatCompletionAfterCheckAiAgent", ['conversation_id' => $conversationId, 'agent_id' => $agentId]);


        // 报表生成获取是否再次开启分析配置
        $openReAnalysis = $aiAgent->promptConfig['reanalysisConfig']['openReAnalysis'] ?? 0;

        $aiAgent->setConversationId($conversationId);
        $aiAgent->setQuestion($question);

        if ($fromAssistant) {
            $aiAgent->setAsync(false);
            $aiAgent instanceof FastArchiveAiAgent && $aiAgent->setResponseFormat(FastArchiveAiAgent::RESPONSE_FORMAT_KEY_LABEL);
        }

        $aiAgent->timeLog = ['start' => microtime(true)];

        try {
            $processRecord = $aiAgent->saveProcessRecord($question);

            $context = [
                'question_history_id' => \PgActiveRecord::produceAutoIncrementId(),
                'answer_history_id' => \PgActiveRecord::produceAutoIncrementId(),
                'record_id' => $processRecord->record_id
            ];

            $aiAgent->setContext($context);

            // 先返回 record_id 给前端，用于埋点
            $aiAgent->reponseRecordId();

            LogUtil::info("chatCompletionAfterResponseRecordId", ['conversation_id' => $conversationId, 'record_id' => $processRecord->record_id]);

            // 预先计费校验
            $aiAgent->checkUsageRestriction();

            LogUtil::info("chatCompletionAfterCheckUsageRestriction", ['conversation_id' => $conversationId, 'record_id' => $processRecord->record_id]);

            if ($aiAgent->isAsync()) {

                LogUtil::info("chatCompletionAfterIsAsync", ['conversation_id' => $conversationId, 'record_id' => $processRecord->record_id]);

                // 通知前端异步执行
                $aiAgent->sseResponse->writeJson(['protocol' => 'websocket', 'record_id' => $processRecord->record_id]);
                $aiAgent->timeLog['enter_queue'] = microtime(true);
                $jobParams = [
                    'client_id' => $clientId,
                    'user_id' => $userId,
                    'conversation_id' => $conversationId,
                    'process_record_id' => $processRecord->record_id,
                    'scene_type' => $aiAgent->sceneType,
                    'question' => $question,
                    'context' => $context,
                    'params' => $params,
                    'time_log' => $aiAgent->timeLog,
                ];
                $aiAgent->runAsync($jobParams);
            } else {
                $aiAgentProcessResponse = $aiAgent->process($params);
                $aiAgent->sseResponse($aiAgentProcessResponse);
            }
        } catch (\Throwable $throwable) {

            $aiAgentProcessResponse = Helper::handleException($aiAgent, $throwable);

            LogUtil::info("chatCompletionAfterException", ['conversation_id' => $conversationId]);

        } finally {

            LogUtil::info("chatCompletionBeforeFinish", ['conversation_id' => $conversationId]);

            // 重新开启缓冲区，在Controller.php中会关闭缓冲区
            if (!$openReAnalysis) {
                $aiAgent->sseResponse->finish();
            }

            LogUtil::info("chatCompletionAfterFinish", ['conversation_id' => $conversationId]);

            if (!$aiAgent->isAsync()) {
                if (empty($aiAgentProcessResponse)) {
                    // 问答
                    $aiAgentProcessResponse = new AiAgentProcessResponse();
                    $aiAgentProcessResponse->setAnswer(AiAgentException::ERROR_MSG);
                    $aiAgentProcessResponse->setMessageType(AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT);
                }

                LogUtil::info("chatCompletionAfterIsAsyncGenerateHistory", ['conversation_id' => $conversationId]);

                // 获取历史记录
                $history = $aiAgent->generateHistoryContent($aiAgentProcessResponse);
                LogUtil::info("chatCompletionAfterGenerateHistory", ['conversation_id' => $conversationId]);
                // 保存历史聊天记录
                $aiAgent->saveConversation($history, $question, $aiAgentProcessResponse->recordId, $aiAgentProcessResponse->messageType, $params);
                LogUtil::info("chatCompletionAfterSaveConversation", ['conversation_id' => $conversationId]);

                $timeLog = $aiAgent->timeLog;
                $timeLog['api_end'] = microtime(true);
                $aiAgent->timeLog = $timeLog;

                $aiAgent->logAgentProcessInfo("timeLog", $aiAgent->timeLog);
            }
        }

        $aiAgent->afterChat();
    }

    public function getBizTypes($field, $value) {
        $list = \common\library\customer\Helper::getBizTypeList();
        $map = array_column($list,'id','name');
        return $map[$value] ?? $value;
    }

    public static function getCategoryIds($field, $value) {
        // todo 查询量有点大 需要确认后再写
        return $value;

    }

    public static function getFlowIds($field, $value) {
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        //获取商机流程
        $flowApi = new FlowApi($clientId);
        $flowApi->setWithDisabled(1);
        $list = $flowApi->flowList();
        $map = array_column($list,'flow_id','name');
        return $map[$value] ?? $value;

    }

    // select * from tbl_company where user_id = :tbl_company.user_id.Amy

    public static function getDepartmentIds($field, $value) {
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $list = DepartmentCacheableRepo::instance($clientId)->findAllByConditions(['client_id' => $clientId, 'enable_flag' => 1], 'id,name');
        $departmentMap = array_column($list,'id','name');
        return $departmentMap[$value] ?? $value;
    }

    public static function getUserIds ($field,$value) {
        $user = \User::getLoginUser();

        $userList = new UserList();
        $userList->setClientId($user->getClientId());
        $userArr = $userList->find();

        $map = array_column($userArr,'user_id','nickname');
        return $map[$value] ?? $value;
    }

    public static function getClientId($field,$value) {
        $user = \User::getLoginUser();
        return $user->getClientId();
    }

    public static function getDefaultValue($field,$value) {
        return BaseObject::ENABLE_FLAG_TRUE;
    }

    public static function getCompanyGroupIds($field,$value) {
        $user = \User::getLoginUser();
        $api = new \common\library\setting\library\group\GroupApi($user->getClientId(),\Constants::TYPE_COMPANY);
        $list = $api->list(0);
        $map =  array_column($list,'id','name');
        return $map[$value] ?? $value;
    }

    public static function convertValue($field, $value, $function) {
        if (empty($value)) return $value;
        return call_user_func(array(Helper::class,$function),$field,$value);
    }

    public static function checkoutResponse($answer)
    {
        // GPT返回格式不对、报错
        if (!str_contains($answer, 'dsl')) {
            throw new \RuntimeException('response format error！', AiAgentException::REPORT_GENERATION_RESPONSE_FORMAT_ERROR);
        }

        // GPT生成的json解析错误
        $dsl = str_replace('dsl ', '', $answer);
        json_decode($dsl);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \RuntimeException('json format error！', AiAgentException::REPORT_GENERATION_JSON_FORMAT_ERROR);
        }
    }

    public static function analysisDsl($clientId,$dsl)
    {
        $dsl = json_decode($dsl,true);
        $mainCurrency = Client::getClient($clientId)->getMainCurrency();
        if (empty($dsl)) {
            return [];
        }
        $defaultCharType = [
            [
                'label' => '条形图',
                'value' => "horizontal-bar"
            ],
            [
                'label' => '折线图',
                'value' => "line"
            ],
            [
                'label' => '柱状图',
                'value' => "bar"
            ],
            [
                'label' => '表格',
                'value' => "table"
            ]
        ];

        $fieldArr = $dsl['fields'] ?? [];
        $fieldCount = count($fieldArr);
        $groupByArr = $dsl['group_by'] ?? [];
        $haveGroupByFieldFlag = false;

        if (count($groupByArr) > 2) {
            return [
                'code' => AiAgentException::REPORT_GENERATION_ANALYSIS_GROUP_BY_ERROR
            ];
        }

        // 如果没有Group by 那么就用文本显示
        if (empty($groupByArr)) {
            $fieldInfo = [];
            foreach ($fieldArr as $field) {
                $currency = '';
                $function = $field['function'] ?? '';

                if (!empty($function)) {
                    // 转为小写
                    $alias = strtolower($function);
                }

                if (!empty($field['alias'] ?? '')) {
                    $alias = $field['alias'];
                }

                $fieldName = !empty($alias) ? $alias : ($field['field'] ?? '');
                if (str_contains(($field['field'] ?? ''), 'amount')) {
                    $currency = $mainCurrency == 'USD' ? '$' : '¥';
                }
                $fieldInfo[$fieldName] = [
                    'field' => $fieldName,
                    'currency' => $currency
                ];
            }
            return [
                'code' => 0,
                'desc' => $dsl['desc'] ?? '',
                'charType' => [],
                'chatFlag' => false ,
                'fieldInfo' => $fieldInfo,
            ];
        }


        // 有group by 的情况 若查询字段超过2个 那么就有不同分组的情况 则不展示饼图
        if ($fieldCount > 2) {
            $chartType = $defaultCharType;
        } else {
            $char = [
                [
                    'label' => '饼图',
                    'value' => "pie"
                ],
            ];
            $chartType = array_merge($char,$defaultCharType);
        }

        $analysisInfo = [
            'code' => 0,
            'desc' => $dsl['desc'] ?? '',
            'chatFlag' => true ,
            'charType' => $chartType
        ];

        $groupBy = $groupByArr[0] ?? '';

        foreach ($fieldArr as $field) {
            $currency = '';
            $function = $field['function'] ?? '';
            $tmpField = $field['field'] ?? '';

            // table.field的情况  数据库查询出来的结果是field
            $tmpFieldArr = explode('.',$tmpField);
            if (count($tmpFieldArr) > 1) {
                $alias = $tmpFieldArr[1] ?? '';
            }

            if (!empty($function)) {
                $alias = strtolower($function);
            }

            if (!empty($field['alias'] ?? '')) {
                $alias = $field['alias'];
            }

            $fieldName = !empty($alias) ? $alias: ($field['field'] ?? '');
            $transFieldName = $fieldName;
            if (str_contains( ($field['field'] ?? ''),'amount')) {
                // 金额字段都默认转换
                $transFieldName = 'amount';
                $currency = $mainCurrency == 'USD' ? '$' : '¥';
            }


            if ($function == 'count') {
                // 总数转换
                $transFieldName = 'count';
            }

            $info = [
                'field' => $fieldName,
                'name' => \Yii::t('aiGenerationData',$transFieldName),
                'currency' => $currency
            ];

            if ($fieldName == $groupBy) {
                // x轴 没有X轴认为是失败
                $analysisInfo['XAxis'][] = $info;
                $haveGroupByFieldFlag = true;
            } else {
                $analysisInfo['YAxis'][] = $info;
            }
        }
        if (!$haveGroupByFieldFlag) {
            $analysisInfo['code'] = AiAgentException::REPORT_GENERATION_ANALYSIS_GROUP_BY_ERROR;
        }

        return $analysisInfo;
    }


    public static function execDslSql($clientId, $userId,$dsl,$sql)
    {
        if (!is_array($dsl)) {
            $dsl = json_decode($dsl,true);
        }
        $client = \common\library\account\Client::getClient($clientId);
        $currency = $client->getMainCurrency();

        $dslFields = $dsl['fields'] ?? [];

        $defaultTable = $dsl['from']['tables'][0]['name'];
        // 记忆每一个映射对应到那张表
        $aliasToFieldMap = [];
        // 查询字段数组 用于判断是否有金额字段
        $selectFieldArr = [];
        // 组装 alias => field 和 查询字段对应
        foreach ($dslFields as $dslField) {
            $field = $dslField['field'] ?? '';
            $alias = $dslField['alias'] ?? $field;
            $function = $dslField['function'] ?? '';
            $fieldArr = explode(".",$field);
            $selectField = $fieldArr[1] ?? $field;
            $selectFieldArr[] = $selectField;
            if (!empty($function) && $function == 'count') continue;
            if (empty($alias) && empty($function)) continue;

            // 出了count的其他function需要考虑别名用于后续计算逻辑
            if (!empty($function)) {
                $alias = strtolower($function);
            }
            if (!empty($dslField['alias'])) {
                $alias = $dslField['alias'];
            }

            $aliasToFieldMap[$alias] = $field;
        }

        $tableInfo = Helper::getTableFieldInfo($clientId);
        $tableNameArr = array_keys($tableInfo);

        $convertor = DslSqlConvertor::getInstance($clientId,$userId);
        $tableInfo = Helper::getTableFieldInfo($clientId);
        $tableNameArr = array_keys($tableInfo);

        $convertor = DslSqlConvertor::getInstance($clientId, $userId);
        $table = $defaultTable;

        try {
            $db = DataWorkActiveRecord::getDbByClientId($clientId);
            $res = $db->createCommand($sql)->queryAll();
        } catch (\Exception $exception) {
            \LogUtil::info("exec_dsl_sql_err: sql:[{$sql}],clientId:[{$clientId}] userId:[{$userId}] ");
            throw new AiAgentException('exec dsl sql error！', AiAgentException::REPORT_GENERATION_EXEC_DSL_SQL_ERROR);
        }

        if (empty($res)) {
            throw new AiAgentException("empty data",AiAgentException::ERR_GENERATION_DATA_EMPTY);
        }


        foreach ($res as $index => $info) {
            foreach ($info as $key => $item) {
                $fieldKey = $aliasToFieldMap[$key] ?? $key;
                $tmpTableInfo = $tableInfo[$table] ?? [];
                $fieldInfo = $tmpTableInfo[$key] ?? [];
                $realField = '';
                if ($fieldKey != $key) {
                    // 有别名的情况 需要转换
                    $fieldKeyArr = explode(".",$fieldKey);
                    $table = $fieldKeyArr[0] ?? $defaultTable;
                    if (!in_array($tableInfo,$tableNameArr)) {
                        $table = $defaultTable;
                    }
                    $realField = $fieldKeyArr[1] ?? '';
                }

                if (empty($callFunction)) {
                    $fieldInfo = $tmpTableInfo[$realField] ?? [];
                }

                $callFunction = $fieldInfo['callFunction'] ?? '';
                $fieldDesc = $fieldInfo['desc'] ?? '';
                $convertValue = $convertor->convertValue($key,$item,$callFunction,DslSqlConvertor::TRANSLATE_ID_TO_NAME,$fieldDesc);
                $res[$index][$key] = $convertValue;
                if (in_array($key,['external_field_data','score','user_data'])) {
                    $res[$index][$key] = json_decode($item,true);
                }
                if ($key == 'country') {
                    if ($res[$index][$key] == '') {
                        $res[$index][$key] = '未知';
                    }
                }
                if (is_numeric($convertValue)) {
                    // 保留两位小数
                    $convertValue = round($convertValue, 2);
                    $res[$index][$key] = $convertValue;
                }

            }
        }
        return $res;
    }

    public static function jsonbFieldInfos($clientId)
    {
        $jsonbFieldInfos = [];
        $tableInfo = self::getTableFieldInfo($clientId);

        foreach ($tableInfo as $tableName => $fieldInfos)
        {
            foreach ($fieldInfos as $fieldName => $fieldInfo)
            {
                // keys 为 jsonb 格式的 key，此类数据需要打平
                // 例如：users 中含有 user_id，那么需要转化字段为 users_user_id
                if (isset($fieldInfo['keys']) && is_array($fieldInfo['keys']))
                {
                    foreach ($fieldInfo['keys'] as $name => $info)
                    {
                        $extentFieldName = $fieldName . '_' . $name;
                        $jsonbFieldInfos[$extentFieldName] = [
                            'field' => $fieldName,
                            'jsonb_key' => $name
                        ];
                    }
                }
            }
        }

        return $jsonbFieldInfos;
    }

    public static function getDslByRecordId($clientId,$recordId) {
        $pgDB = PgActiveRecord::getDbByClientId($clientId);
        $sql = "select answer,params from tbl_ai_service_process_record where client_id = {$clientId} and record_id = {$recordId}";
        $res = $pgDB->createCommand($sql)->queryAll();
        if (empty($res)) {
            return [];
        }
        $info = $res[0];
        $dsl = $info['answer'] ?? '';

        json_decode($dsl, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            $dsl = json_decode($dsl, true);
            $dsl = $dsl['DSL'] ?? $dsl;
        } else {
            $dsl = str_replace('dsl ', '', $dsl);
            $dsl = json_decode($dsl,true);
        }

        return $dsl;

    }

    /**
     * 获取最前的一条record
     */
    public static function loadConversationFirstRecord($clientId, $userId, $sceneType, $conversationId)
    {
        $aiServiceProcessRecordPdo = new AiServiceProcessRecordList($clientId, $userId, $sceneType);
        $aiServiceProcessRecordPdo->setConversationId($conversationId);
        $aiServiceProcessRecordPdo->setOrderBy('record_id');
        $aiServiceProcessRecordPdo->setOrder('asc');
        $aiServiceProcessRecordPdo->setLimit(1);
        $aiServiceProcessRecordList = $aiServiceProcessRecordPdo->find();

        return $aiServiceProcessRecordList[0] ?? [];
    }

    public static function getAgentListBySystemId($systemId)
    {
        $list = [];
        switch ($systemId) {
            case PrivilegeConstants::OKKI_ALI_BASIC_ID:
            case PrivilegeConstants::OKKI_PERSONAL_LEADS_AI_BASIC_ID:
            case PrivilegeConstants::OKKI_PERSONAL_LEADS_AI_SUBSCRIBE_ID:
                $list = [
                    \AiAgent::AI_AGENT_SCENE_TYPE_MAIL_WRITE,
                    \AiAgent::AI_AGENT_SCENE_TYPE_MAIL_POLISH,
                ];
                break;
            case PrivilegeConstants::CRM_LITE_SYSTEM_ID:
            case PrivilegeConstants::CRM_LITE_2021_ID:
            case PrivilegeConstants::CRM_LITE_2023_ID:
            case PrivilegeConstants::TW_LITE_AI_ID:
            case PrivilegeConstants::HK_LITE_AI_ID:
            case PrivilegeConstants::OKKI_LEADS_AI_ID:
            case PrivilegeConstants::OKKI_PERSONAL_LEADS_AI_ID:
            case PrivilegeConstants::OKKI_PERSONAL_LEADS_AI_BASIC_ID:
            case PrivilegeConstants::OKKI_LEADS_AI_JP_ID:
            case PrivilegeConstants::OKKI_PERSONAL_LEADS_AI_GLOBAL_BASIC_ID:
                $list = [
                    \AiAgent::AI_AGENT_SCENE_TYPE_MAIL_WRITE,
                    \AiAgent::AI_AGENT_SCENE_TYPE_MAIL_POLISH,
                    \AiAgent::AI_AGENT_SCENE_TYPE_CHAT_REPLY,
                    \AiAgent::AI_AGENT_SCENE_TYPE_CHAT_COACH,
                    \AiAgent::AI_AGENT_SCENE_TYPE_FAST_ARCHIVE,
                    \AiAgent::AI_AGENT_SCENE_TYPE_CUSTOMER_FOLLOW_UP,
                    \AiAgent::AI_AGENT_SCENE_TYPE_MAIL_REPLY,
                    \AiAgent::AI_AGENT_SCENE_TYPE_MAIL_SUMMARY,
                    \AiAgent::AI_AGENT_SCENE_TYPE_CHAT_POLISH,
                ];
                break;
            case PrivilegeConstants::CRM_SMART_SYSTEM_ID:
            case PrivilegeConstants::TW_SMART_AI_ID:
            case PrivilegeConstants::HK_SMART_AI_ID:
                $list = [
                    \AiAgent::AI_AGENT_SCENE_TYPE_MAIL_WRITE,
                    \AiAgent::AI_AGENT_SCENE_TYPE_MAIL_POLISH,
                    \AiAgent::AI_AGENT_SCENE_TYPE_CHAT_REPLY,
                    \AiAgent::AI_AGENT_SCENE_TYPE_CHAT_COACH,
                    \AiAgent::AI_AGENT_SCENE_TYPE_FAST_ARCHIVE,
                    \AiAgent::AI_AGENT_SCENE_TYPE_CUSTOMER_FOLLOW_UP,
                    \AiAgent::AI_AGENT_SCENE_TYPE_OPPORTUNITY_FOLLOW_UP,
                    \AiAgent::AI_AGENT_SCENE_TYPE_GENERATION_DATA,
                    \AiAgent::AI_AGENT_SCENE_TYPE_CHAT_QUALITY,
                    \AiAgent::AI_AGENT_SCENE_TYPE_DATA_ANALYSIS,
                    \AiAgent::AI_AGENT_SCENE_TYPE_BATCH_CHAT_QUALITY,
                    \AiAgent::AI_AGENT_SCENE_TYPE_ASSET_ANALYSIS,
                    \AiAgent::AI_AGENT_SCENE_TYPE_MAIL_QUALITY,
                    \AiAgent::AI_AGENT_SCENE_TYPE_CHAT_POLISH,
                    \AiAgent::AI_AGENT_SCENE_TYPE_MAIL_SUMMARY,
                    \AiAgent::AI_AGENT_SCENE_TYPE_MAIL_REPLY,
                    \AiAgent::AI_AGENT_SCENE_TYPE_TEAM_ANALYSIS,
                    \AiAgent::AI_AGENT_SCENE_TYPE_COMPANY_QUALITY_CHECK,
                    \AiAgent::AI_AGENT_SCENE_TYPE_STATISTICAL_ANALYSIS_INSIGHT,
                    \AiAgent::AI_AGENT_SCENE_TYPE_DATA_ASSISTANT_PROXY
                ];
                break;

            case PrivilegeConstants::CRM_PRO_SYSTEM_ID:
            case PrivilegeConstants::OKKI_PRO_SYSTEM_ID:
            case PrivilegeConstants::HK_PRO_AI_ID:
                $list =[
                    \AiAgent::AI_AGENT_SCENE_TYPE_MAIL_WRITE,
                    \AiAgent::AI_AGENT_SCENE_TYPE_MAIL_POLISH,
                    \AiAgent::AI_AGENT_SCENE_TYPE_CHAT_REPLY,
                    \AiAgent::AI_AGENT_SCENE_TYPE_CHAT_COACH,
                    \AiAgent::AI_AGENT_SCENE_TYPE_FAST_ARCHIVE,
                    \AiAgent::AI_AGENT_SCENE_TYPE_CUSTOMER_FOLLOW_UP,
                    \AiAgent::AI_AGENT_SCENE_TYPE_GENERATION_DATA,
                    \AiAgent::AI_AGENT_SCENE_TYPE_DATA_ANALYSIS,
                    \AiAgent::AI_AGENT_SCENE_TYPE_OPPORTUNITY_FOLLOW_UP,
                    \AiAgent::AI_AGENT_SCENE_TYPE_CHAT_QUALITY,
                    \AiAgent::AI_AGENT_SCENE_TYPE_BATCH_CHAT_QUALITY,
                    \AiAgent::AI_AGENT_SCENE_TYPE_ASSET_ANALYSIS,
                    \AiAgent::AI_AGENT_SCENE_TYPE_MAIL_QUALITY,
                    \AiAgent::AI_AGENT_SCENE_TYPE_MAIL_SUMMARY,
                    \AiAgent::AI_AGENT_SCENE_TYPE_MAIL_REPLY,
                    \AiAgent::AI_AGENT_SCENE_TYPE_TEAM_ANALYSIS,
                    \AiAgent::AI_AGENT_SCENE_TYPE_CHAT_POLISH,
                    \Aiagent::AI_AGENT_SCENE_TYPE_COMPANY_QUALITY_CHECK,
                    \AiAgent::AI_AGENT_SCENE_TYPE_STATISTICAL_ANALYSIS_INSIGHT,
                    \AiAgent::AI_AGENT_SCENE_TYPE_DATA_ASSISTANT_PROXY
                ];
                break;
            default:
                break;
        }
        return $list;
    }

    public static function getAiAgentCostInfo($startDate = '',$endDate = '')
    {
        $where = '';
        $condition = [];
        if (!empty($startDate)) {
            $startDate = date('Y-m-d 00:00:00', strtotime($startDate));
            $condition[] = " create_time >= '{$startDate}'";
        }
        if (!empty($endDate)) {
            $endDate = date('Y-m-d 23:59:59', strtotime($endDate));
            $condition[] = " create_time <= '{$endDate}'";
        }
        if (!empty($condition)) {
            $where = " where " . implode(' and ', $condition);
        }

        $sql = "SELECT  scene_type
                    ,COUNT(1) AS total_call
                    ,SUM(prompt_tokens) AS sum_prompt_tokens
                    ,SUM(completion_tokens) AS sum_completion_tokens
                    ,sum(total_tokens) AS sum_total_tokens
                    ,MAX(prompt_tokens) AS max_prompt_tokens
                    ,MAX(completion_tokens) AS max_completion_tokens
                    ,MAX(total_tokens) AS max_total_tokens
                    ,ROUND(AVG(prompt_tokens),2) AS avg_prompt_tokens
                    ,ROUND(AVG(completion_tokens),2) AS avg_completion_tokens
                    ,ROUND(AVG(total_tokens),2) AS avg_total_tokens
            FROM    tbl_ai_service_process_record
            {$where}
            GROUP BY scene_type";

        // 指定client_id;
        if (\Yii::app()->params['env'] == 'test') {
            $clientId = 1;
        } else {
            $clientId = 3;
        }

        $holoDb = DataWorkActiveRecord::getDbByClientId($clientId);
        $costDataList = $holoDb->createCommand($sql)->queryAll();

        $sceneCostInfo = array_column($costDataList, null, 'scene_type');
        $usdToRmbCurrency = 7.09;
        $rmbCurrency = 1;
        $modelCostConfig = [
            AIClient::AZURE_OPENAI_GPT_THREE => [
                'costInfo' => [
                    'prompt_cost' => 0.0015,
                    'completion_cost' => 0.002,
                ],
                'currency' => $usdToRmbCurrency
            ],
            AIClient::AZURE_OPENAI_GPT_FOUR_TURBO => [
                'costInfo' => [
                    'prompt_cost' => 0.01,
                    'completion_cost' => 0.03,
                ],
                'currency' => $usdToRmbCurrency
            ],
            AIClient::AZURE_OPENAI_GPT_THREE_MORE => [
                'costInfo' => [
                    'prompt_cost' => 0.003,
                    'completion_cost' => 0.004,
                ],
                'currency' => $usdToRmbCurrency
            ],
            AIClient::AZURE_OPENAI_GPT_THREE_TURBO => [
                'costInfo' => [
                    'prompt_cost' => 0.0015,
                    'completion_cost' => 0.002,
                ],
                'currency' => $usdToRmbCurrency
            ],
            AIClient::AZURE_OPENAI_GPT_FOUR => [
                'costInfo' => [
                    'prompt_cost' => 0.03,
                    'completion_cost' => 0.06,
                ],
                'currency' => $usdToRmbCurrency
            ],
            AIClient::AZURE_OPENAI_GPT_FOUR_32K => [
                'costInfo' => [
                    'prompt_cost' => 0.06,
                    'completion_cost' => 0.12,
                ],
                'currency' => $usdToRmbCurrency
            ],
            AiServiceConstant::SERVICE_QWEN_TURBO => [
                'costInfo' => [
                    'prompt_cost' => 0.008,
                    'completion_cost' => 0.008,
                ],
                'currency' => $rmbCurrency
            ],
            AiServiceConstant::SERVICE_QWEN_MAX => [
                'costInfo' => [
                    'prompt_cost' => 0.002,
                    'completion_cost' => 0.002,
                ],
                'currency' => $rmbCurrency
            ]
        ];
        $costNameToDbKeysMap = [
            'prompt_cost' => [
                'sum_prompt_tokens',
                'max_prompt_tokens',
                'avg_prompt_tokens',
            ],
            'completion_cost' => [
                'sum_completion_tokens',
                'max_completion_tokens',
                'avg_completion_tokens',
            ],
        ];

        /**
         * @var CDbConnection $adminDb
         */
        $adminDb = \Yii::app()->db;
        $agentList = new AiAgentList();
        $agentList->setAgentType([\AiAgent::AGENT_TYPE_CUSTOMER_MANAGER, \AiAgent::AGENT_TYPE_DECISION_MANAGER]);
        $agentList->setAggregateProxyAgent(false);
        $agentListData = $agentList->find();
        $aiSceneCostConfig = AiAgentConstants::AI_SCENE_COST_MAP;
        $oneKCoinCost = 0.2; // 500K币*N=100元*N
        $result = [];
        // 每个agent，可以理解为每个场景
        foreach ($agentListData as $agentItem)
        {
            $agentId = $agentItem['agent_id'];
            $agentSceneType = $agentItem['scene_type'];
            $agentName = $agentItem['agent_name'];
            $agentCost = $aiSceneCostConfig[$agentSceneType] ?? 1;
            $kCoinCost = $oneKCoinCost * $agentCost; //单次使用k币成本

            $defaultInfo =  [
                'agent_id' => $agentId,
                'agent_name' => $agentName,
                'cost' => $agentCost,
                'kCoinCostRmb' => $kCoinCost,
                'scene_type' => $agentSceneType,
            ];
            $defaultNullData = [
                "total_call"=>0,
                "sum_prompt_tokens"=>0,
                "sum_completion_tokens"=>0,
                "sum_total_tokens"=>0,
                "max_prompt_tokens"=>0,
                "max_completion_tokens"=>0,
                "max_total_tokens"=>0,
                "avg_prompt_tokens"=>0,
                "avg_completion_tokens"=>0,
                "avg_total_tokens"=>0
            ];
            $defaultInfo = array_merge($defaultInfo, $sceneCostInfo[$agentSceneType] ?? $defaultNullData);
            $currentModelCostInfo = [];
            foreach ($modelCostConfig as $model => $costList)
            {
                $currentModelCostInfo[$model] = [];
                $currentModelCostInfo[$model]['model_name'] = $model;
                $currency = $costList['currency'] ?  $costList['currency']:1;
                $costInfo = $costList['costInfo'];
                foreach ($costInfo as $costName => $value)
                {
                    foreach ($costNameToDbKeysMap[$costName] as $dbKey) {
                        $currentModelCostInfo[$model][$dbKey."_cost"] = round(($defaultInfo[$dbKey] * $currency * $value / 1000), 4);
                    }
                }
                $currentModelCostInfo[$model]['avg_conversation_cost'] = round(($currentModelCostInfo[$model]['avg_prompt_tokens_cost'] + $currentModelCostInfo[$model]['avg_completion_tokens_cost']), 4);

                $currentModelCostInfo[$model]['max_conversation_cost'] = round(($currentModelCostInfo[$model]['max_prompt_tokens_cost'] + $currentModelCostInfo[$model]['max_completion_tokens_cost']), 4);

                $currentModelCostInfo[$model]['avg_conversation_cost_percent'] = ($kCoinCost == 0) ? 0 : round(($currentModelCostInfo[$model]['avg_conversation_cost'] / $kCoinCost * 100), 2);
                $currentModelCostInfo[$model]['max_conversation_cost_percent'] = ($kCoinCost == 0) ? 0 : round(($currentModelCostInfo[$model]['max_conversation_cost'] / $kCoinCost * 100), 2);

                $compareModel = AIClient::AZURE_OPENAI_GPT_THREE;
                $currentModelCostInfo[$model]['avg_conversation_cost_percent_change'] = round(($currentModelCostInfo[$model]['avg_conversation_cost_percent'] - $currentModelCostInfo[$compareModel]['avg_conversation_cost_percent']), 2);
                $currentModelCostInfo[$model]['max_conversation_cost_percent_change'] = round(($currentModelCostInfo[$model]['max_conversation_cost_percent'] - $currentModelCostInfo[$compareModel]['max_conversation_cost_percent']), 2);
            }
            $defaultInfo['model_cost'] = array_values($currentModelCostInfo);
            $result[] = $defaultInfo;
        }
        return $result;
    }


    /**
     * insight权限校验
     */
    public static function hasInsightPermission(int $clientId): bool
    {
        // 校验是否含OKKI AI权限
        $privilegeService = PrivilegeService::getInstance($clientId);
        if (!$privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_OKKI_AI)) {
            return false;
        }

        // insight 只对 smart & pro 开放
        $systemId = $privilegeService->getMainSystemId();
        if (!in_array($systemId, [PrivilegeConstants::CRM_SMART_SYSTEM_ID, PrivilegeConstants::CRM_PRO_SYSTEM_ID, PrivilegeConstants::OKKI_PRO_SYSTEM_ID])) {
            return false;
        }
        if ($privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_ALI_SALES_RACE)) {
            return false;
        }

        return true;
    }

    /**
     * 客户质检权限校验
     */
    public static function hasCompanyQualityCheckPermission(int $clientId): bool
    {
        // 校验是否含OKKI AI权限
        $privilegeService = PrivilegeService::getInstance($clientId);
        if (!$privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_OKKI_AI)) {
            return false;
        }

        //校验是否含有OKKI_AI_QC权限
        if (!$privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_OKKI_AI_QC)) {
            return false;
        }

        // insight 只对 smart & pro 开放
        $systemId = $privilegeService->getMainSystemId();
        if (!in_array($systemId, AiAgentConstants::AI_QC_SYSTEM_IDS)) {
            return false;
        }

//        $client = Client::getClient($clientId);
//        if(empty($client) || empty($client->getExtentAttributes([Client::EXTERNAL_KEY_AI_COMPANY_QC])['ai_company_qc'])){
//            return false;
//        }

        $user = \User::getLoginUser();
        $userId = $user->getUserId();
        $privilegeService = PrivilegeService::getInstance($clientId, $userId);
        //存在可见范围为本人,但手动赋予了privilege的情况
//        if($privilegeService->getPrivilegeScope(PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW) <= PrivilegeConstants::PRIVILEGE_SCOPE_OWNER){
//            return false;
//        }
        //read是base privilege
        if(!$privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_OKKI_AI_QC)){
            return false;
        }

        return true;
    }

    public static function handleException(BaseAiAgent|AiAgentProxy $aiAgent, \Throwable $throwable)
    {
        // 日志记录
        $aiAgent->logAgentProcessInfo("所有异常", ['file' => $throwable->getFile(),'line' => $throwable->getLine(),'errorCode' => $throwable->getCode(), 'errorMsg' => $throwable->getMessage(), 'trace' => $throwable->getTrace()]);

        // 需要捕捉全部错误，存储process_record，方便数据导出
        if ($throwable instanceof AiAgentException) {

            $errorCode = $throwable->getCode();

        } elseif ($throwable instanceof \RuntimeException) {

            $errorCode = AiAgentException::RUNTIME_EXCEPTION_ERROR;

        } else {

            $errorCode = AiAgentException::THROWABLE_EXCEPTION_ERROR;

        }

        // 处理AI错误（有些Agent场景的异常会返回定制的小卡片给App端）
        $aiAgentProcessResponse = $aiAgent->handleException($throwable->getMessage(), $throwable->getCode());
        $aiAgent->sseResponse($aiAgentProcessResponse);

        // 错误信息记录
        $errorMsg = $throwable->getMessage();

        // 入库
        if (!empty($aiAgent->context['record_id']))
        {
            $status = \AiServiceProcessModel::STATUS_FAIL;

            // 只有该数组中的错误才需要设置失败
            if (!in_array($errorCode, AiAgentException::STATUS_FAIL_ERRORS)) {
                $status = \AiServiceProcessModel::STATUS_SUCCESS;
            }

            $aiAgent->updateAiServiceProcessRecord($aiAgent->context['record_id'], ['error_code' => $errorCode, 'error_msg' => $errorMsg, 'status' => $status]);
        }

        if (in_array($errorCode, AiAgentException::STATUS_FAIL_ERRORS) || $errorCode == AiAgentException::ERR_VERSION_NOT_SUPPORT_FUNCTION) {
            try {
                self::notifyToDingTalk($aiAgent, $throwable, [], $errorCode);
            } catch (\Throwable $exception) {
                \LogUtil::error($exception->getMessage());
            }
        }

        return $aiAgentProcessResponse;
    }

    public static function getAiAgentInfoByConversationId($clientId, $conversationId): array
    {
        // 获取agentId
        $conversationInfo = new \common\library\ai_service\AiAgentConversation($clientId, $conversationId);
        $agentId = $conversationInfo->agent_id;

        // 获取agent信息
        $agentModel = new \common\library\ai_agent\agent\AiAgent($agentId);

        // 获取agent信息
        $agentClientSetting = \AiAgentClientSetting::findClientAgentSetting($clientId, $agentId);

        return [
            'version_id' => empty($agentClientSetting) ? $agentModel->default_version : $agentClientSetting->version_id,
            'agent_id' => $agentId,
            'scene_type' => $agentModel->scene_type,
            'business_type' => $conversationInfo->business_type,
        ];
    }


    public static function buildPBCommonMessageInfo(array $sseObject, AiAgentProcessResponse $processResponse)
    {
        $now = strtotime(date("Y-m-d H:i:s")) * 1000;

        $ans = new \protobuf\OkkiAi\PBConversationMessage();
        $ans->setRole(\protobuf\OkkiAi\PBRole::ROLE_SYSTEM);
        $ans->setMessageType($processResponse->messageType);
        $ans->setRecordId($processResponse->recordId);
        $ans->setCreateTime($now);

        $PBMessageInfo = self::constructMessageInfo($sseObject, $processResponse);
        $ans->setMessageInfo($PBMessageInfo);

        return $ans;
    }

    public static function constructMessageInfo(array $sseObject, AiAgentProcessResponse $processResponse): \protobuf\OkkiAi\PBCommonMessageInfo
    {
        /**
         * @var $singleSseObject AbstractMessageFormat
         */

        $singleSseObject = $sseObject[$processResponse->messageType];
        return self::constructMessageInfoFromSingleSseObject($singleSseObject->getSkeletonMessage(), $processResponse->toArray());
    }

    public static function constructMessageInfoFromSingleSseObject($singleSseObject, array $arrayProcessResponse, bool $fromHistory = false): \protobuf\OkkiAi\PBCommonMessageInfo
    {
        // AiAgentProcessResponse $processResponse 需要message type和answer
        // $fromHistory 为true时，表示从历史记录中获取的数据，需要特殊处理, 例如：在历史记录中需要隐藏翻译按钮
        $PBMessageInfo = new \protobuf\OkkiAi\PBCommonMessageInfo();

        $context = $singleSseObject['context'] ?? [];
        switch ($arrayProcessResponse['messageType'] ?? AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT) {
            case AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CARD:
                /**
                 * @var $messageInfo Card
                 */

                $componentMessageArr = [];
                $headLeft = $context['header_left'] ?? [];
                $headRight = $context['header_right'] ?? [];
                $footerLeft = $context['footer_left'] ?? [];
                $footerRight = $context['footer_right'] ?? [];
                $outsideHeaderLeft = $context['outside_header_left'] ?? [];
                if (!empty($context['query_logic']))
                {
                    $PBExtra = new \protobuf\OkkiAi\PBMessageExtra();
                    $PBQueryLogic = new \protobuf\OkkiAi\PBAiAgentChatCompletionQueryLogicData();
                    $PBQueryLogic->setText($context['query_logic']);
                    $PBExtra->setQueryLogic($PBQueryLogic);
                    $PBMessageInfo->setExtra($PBExtra);
                }

                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headLeft,1));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headRight,2));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerLeft,3));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerRight,4));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderLeft,5));
                $PBMessageInfo->setComponentList($componentMessageArr);
                $PBMessageInfo->setContent($arrayProcessResponse['answer'] ?? '');
                break;
            case AiAgentConstants::AI_AGENT_MESSAGE_REPORT_GENERATION_CHART:
                /**
                 * @var $messageInfo StatisticCard
                 */

                $title = $context['title'] ?? [];
                $subtitle = $context['subtitle'] ?? [];

                $footerLeft = $context['footer_left'] ?? [];
                $footerRight = $context['footer_right'] ?? [];
                $headRight = $context['header_right'] ?? [];
                $headLeft = $context['header_left'] ?? [];
                $outsideHeaderLeft = $context['outside_header_left'] ?? [];

                $PBMessageInfo->setTitle($title['text'] ?? '');
                $PBMessageInfo->setSubtitle($subtitle['text'] ?? '');

                $PBMessageConfig = new PBMessageConfig();
                $analysisInfo = $context['config'] ?? [];
                $XAxis = $analysisInfo['XAxis'] ?? [];
                $YAxis = $analysisInfo['YAxis'] ?? [];
                $series = $analysisInfo['series'] ?? [];
                $configArr = [];
                $configArr = array_merge($configArr,self::buildPBConfig($XAxis,PBAiAxisType::TYPE_X));
                $configArr =  array_merge($configArr,self::buildPBConfig($YAxis,PBAiAxisType::TYPE_Y));
                $configArr =  array_merge($configArr,self::buildPBConfig($series,PBAiAxisType::TYPE_SERIES));
                $PBGenerationDataAxisConfig = new PBGenerationDataAxisConfig();
                $PBGenerationDataAxisConfig->setAxis($configArr);
                $PBMessageConfig->setAxisConfig($PBGenerationDataAxisConfig);
                $PBMessageInfo->setConfig($PBMessageConfig);

                $componentMessageArr = [];
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headLeft,1));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headRight,2));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerLeft,3));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerRight,4));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderLeft,5));
                $PBMessageInfo->setComponentList($componentMessageArr);

                if (!empty($context['query_logic']))
                {
                    $PBExtra = new \protobuf\OkkiAi\PBMessageExtra();
                    $PBQueryLogic = new \protobuf\OkkiAi\PBAiAgentChatCompletionQueryLogicData();
                    $PBQueryLogic->setText($context['query_logic']);
                    $PBExtra->setQueryLogic($PBQueryLogic);
                    $PBMessageInfo->setExtra($PBExtra);
                }


                $PBMessageInfo->setData(self::buildPBGenerateDataMessageData($context['content']));
                break;
            case AiAgentConstants::AI_AGENT_MESSAGE_REPORT_GENERATION_DETAIL_CHART:
                // 构造title开始

                $title = $context['title']['text'] ?? '';
                // 构造title结束
                $PBMessageInfo->setTitle($title);

                // 构造config 开始
                $PBMessageConfig = new PBMessageConfig();
                $analysisInfo = $context['config'] ?? [];
                $XAxis = $analysisInfo['XAxis'] ?? [];
                $YAxis = $analysisInfo['YAxis'] ?? [];
                $series = $analysisInfo['series'] ?? [];
                $configArr = [];
                $configArr = array_merge($configArr,self::buildPBConfig($XAxis,PBAiAxisType::TYPE_X));
                $configArr =  array_merge($configArr,self::buildPBConfig($YAxis,PBAiAxisType::TYPE_Y));
                $configArr =  array_merge($configArr,self::buildPBConfig($series,PBAiAxisType::TYPE_SERIES));
                $PBGenerationDataAxisConfig = new PBGenerationDataAxisConfig();
                $PBGenerationDataAxisConfig->setAxis($configArr);
                $PBMessageConfig->setAxisConfig($PBGenerationDataAxisConfig);
                // 构造config结束
                $PBMessageInfo->setConfig($PBMessageConfig);

                // 构造ComponentList开始
                $componentMessageArr = [];
                $headLeft = $context['header_left'] ?? [];
                $headRight = $context['header_right'] ?? [];
                $footerLeft = $context['footer_left'] ?? [];
                $footerRight = $context['footer_right'] ?? [];
                $outsideHeaderLeft = $context['outside_header_left'] ?? [];


                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headLeft,1));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headRight,2));

                // 当类型是AiAgentConstants::AI_AGENT_MESSAGE_REPORT_GENERATION_DETAIL_CHART 的时候，content也放到$componentList里。
//                $content = $context['content'];

//                $detail = new Detail(dataParams: $content);
//
//                $footerLeft[] = $detail->toArray();
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerLeft,3));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerRight,4));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderLeft,5));
                // 构造ComponentList结束
                $PBMessageInfo->setComponentList($componentMessageArr);

                // 构造data开始
                $content = $context['content'];
                if (is_array($content)) {
                    $generateDataMessageData = self::buildPBGenerateDataMessageData($content);
                    $PBMessageInfo->setData($generateDataMessageData);
                }

                if (!empty($context['query_logic']))
                {
                    $PBExtra = new \protobuf\OkkiAi\PBMessageExtra();
                    $PBQueryLogic = new \protobuf\OkkiAi\PBAiAgentChatCompletionQueryLogicData();
                    $PBQueryLogic->setText($context['query_logic']);
                    $PBExtra->setQueryLogic($PBQueryLogic);
                    $PBMessageInfo->setExtra($PBExtra);
                }
                // 构造data结束

                break;
            case AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT:
                $content = $context['content'] ?? '';
                if (is_string($content)) {
                    $PBMessageInfo->setContent($content);
                }
                if (!empty($context['query_logic']))
                {
                    $PBExtra = new \protobuf\OkkiAi\PBMessageExtra();
                    $PBQueryLogic = new \protobuf\OkkiAi\PBAiAgentChatCompletionQueryLogicData();
                    $PBQueryLogic->setText($context['query_logic']);
                    $PBExtra->setQueryLogic($PBQueryLogic);
                    $PBMessageInfo->setExtra($PBExtra);
                }
                break;

            case AiAgentConstants::AI_AGENT_MESSAGE_TYPE_MAIL_REPLY :
                /**
                 * @var $messageInfo MailReplyOptionsCard
                 */
                // 构造title开始

                $title = $context['title']['text'] ?? '';
                // 构造title结束
                $PBMessageInfo->setTitle($title);

                $subtitle = $context['subtitle']['text'] ?? '';
                $PBMessageInfo->setSubtitle($subtitle);

                $content = $context['content'] ?? '';
                $PBMessageInfo->setContent($content);

                $extra = $context['extra'] ?? [];
                $PBExtraData = new PBMessageExtra();

                $PBMailExtraData = new PBMailReplyExtraData();
                $PBMailExtraData->setStreamType($context['stream_type'] ?? '');

                $replyOptions = $extra['options'] ?? [];

                $optionsRes = [];
                foreach ($replyOptions as $option) {
                    $optionTitle = $option['name'] ?? '';
                    $optionContent = $option['text'] ?? '';
                    $optionStrategyId = $option['strategy_id'] ?? 0;

                    $PBMailReplyOption = new PBMailReplyOption();
                    $PBMailReplyOption->setTitle($optionTitle);
                    $PBMailReplyOption->setContent($optionContent);
                    $PBMailReplyOption->setStrategyId($optionStrategyId);

                    $optionsRes[] = $PBMailReplyOption;
                }
                $PBMailExtraData->setOptions($optionsRes);

                $PBExtraData->setMailReplyExtraData($PBMailExtraData);

                $PBMessageInfo->setExtra($PBExtraData);

                break;

            case AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_QUALITY:

                $configMap = [
                    [
                        'name' => "质检指标",
                        'type' => PBMailQualityConfigEnum::PB_MAIL_QUALITY_STRING,
                        'key' => 'name'
                    ],
                    [
                        'name' => "情况",
                        'type' => PBMailQualityConfigEnum::PB_MAIL_QUALITY_VALUE,
                        'key' => 'value'
                    ],
                    [
                        'name' => "评价",
                        'type' => PBMailQualityConfigEnum::PB_MAIL_QUALITY_EVALUATE,
                        'key' => 'evaluate'
                    ]
                ];

                $PBMailQualityConfig = new PBMailQualityConfig();
                $configArr = [];
                foreach ($configMap as $config) {
                    $PBConfigInfo = new PBMailQualityConfigInfo();
                    $PBConfigInfo->setName($config['name']);
                    $PBConfigInfo->setType($config['type']);
                    $PBConfigInfo->setKey($config['key']);
                    $configArr[] = $PBConfigInfo;
                }
                $PBMailQualityConfig->setConfigInfo($configArr);

                // 构造config 开始
                $PBMessageConfig = new PBMessageConfig();
                $PBMessageConfig->setMailQualityConfig($PBMailQualityConfig);
                $PBMessageInfo->setConfig($PBMessageConfig);

                // 构造data
                $PBMessageData = new PBMessageData();
                $pbMailQualityData = new PBMailQualityList();
                $PBMailQualityDatas = [];

                $dataList = $context['content'] ?? [];

                foreach ($dataList as $dataItem)
                {
                    $name = $dataItem['name'] ?? '';
                    $value = $dataItem['value'] ?? 0;
                    $evaluate = $dataItem['evaluate'] ?? '';

                    $PBDataItem = new PBMailQualityDataItem();
                    $PBDataItem->setName($name);
                    $PBDataItem->setValue($value);
                    $PBDataItem->setEvaluate($evaluate);

                    $PBMailQualityDatas[] = $PBDataItem;
                }

                $pbMailQualityData->setDataItem($PBMailQualityDatas);

                $PBMessageData->setMailQualityList($pbMailQualityData);

                $PBMessageInfo->setData($PBMessageData);


                $title = $context['title']['text'] ?? '';
                // 构造title结束
                $PBMessageInfo->setTitle($title);

                // subtitle
                $subtitle = $context['subtitle'] ?? [];
                $PBMessageInfo->setSubtitle($subtitle['text'] ?? '');


                // 构造ComponentList开始
                $componentMessageArr = [];
                $headLeft = $context['header_left'] ?? [];
                $headRight = $context['header_right'] ?? [];
                $footerLeft = $context['footer_left'] ?? [];
                $footerRight = $context['footer_right'] ?? [];

                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headLeft,1));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headRight,2));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerLeft,3));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerRight,4));
                $PBMessageInfo->setComponentList($componentMessageArr);
                break;
            case AiAgentConstants::AI_AGENT_MESSAGE_REPORT_GENERATION_QUESTION_RECOMMEND:
                /**
                 * @var $messageInfo QuestionRecommendCard
                 */
                $componentMessageArr = [];
                $headLeft = $context['header_left'] ?? [];
                $headRight = $context['header_right'] ?? [];
                $footerLeft = $context['footer_left'] ?? [];
                $footerRight = $context['footer_right'] ?? [];
                $outsideHeaderLeft = $context['outside_header_left'] ?? [];
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headLeft,1));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headRight,2));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerLeft,3));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerRight,4));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderLeft,5));

                $PBExtraData = new PBMessageExtra();
                $PBRecommendExtraData = new PBQuestionRecommendExtraData();
                $questionList = $context['question_list'] ?? [];
                $title = $context['title']['text'] ?? '';
                $subtitle = $context['subtitle']['text'] ?? '';

                $PBRecommendExtraData->setQuestionList($questionList);
                $PBExtraData->setRecommendQuestionList($PBRecommendExtraData);

                $PBMessageInfo->setExtra($PBExtraData);
                $PBMessageInfo->setTitle($title);
                $PBMessageInfo->setSubtitle($subtitle);
                $PBMessageInfo->setComponentList($componentMessageArr);

                break;
            case AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_REPLY_CARD:
                // 辅助回复小卡片
                /**
                 * @var $messageInfo ChatReplyCard
                 */
                $title = $context['title']['text'] ?? '';
                $PBMessageInfo->setTitle($title);
                $subtitle = $context['subtitle']['text'] ?? '';
                $PBMessageInfo->setSubtitle($subtitle);

                // 设置组件，头部左侧、头部右侧、底部左侧、底部右侧、外部头部左侧、外部头部右侧、外部底部左侧
                $componentMessageArr = [];
                $headLeft = $context['header_left'] ?? [];
                $headRight = $context['header_right'] ?? [];
                $footerLeft = $context['footer_left'] ?? [];
                $footerRight = $context['footer_right'] ?? [];
                $outsideHeaderLeft = $context['outside_header_left'] ?? [];
                $outsideHeaderRight = $context['outside_header_right'] ?? [];
                $outsideFooterLeft = $context['outside_footer_left'] ?? [];
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headLeft, 1));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headRight, 2));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerLeft, 3));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerRight, 4));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderLeft, 5));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderRight, 6));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideFooterLeft, 7));
                $PBMessageInfo->setComponentList($componentMessageArr);

                // 设置卡片内容
                $PBExtraData = new PBMessageExtra();
                $pbChatReplyExtraData = new PBChatReplyExtraData();
                // 设置【回复策略】部分
                $pbChatReplyExtraDataReplyStrategy = new PBChatReplyExtraDataReplyStrategy();
                $pbChatReplyExtraDataReplyStrategy->setTitle(\Yii::t('ai', "回复策略"));
                $pbChatReplyExtraDataReplyStrategy->setStrategy($context['strategy'] ?? '');
                $pbChatReplyExtraData->setReplyStrategy($pbChatReplyExtraDataReplyStrategy);
                // 设置【其他策略】按钮部分
                $doOtherStratey = $context['actions'] ?? []; // 【其他策略】按钮
                $pbChatReplyExtraDataOtherStrategyButton = new PBChatReplyExtraDataOtherStrategyButton;
                if (empty($doOtherStratey)) {
                    $pbChatReplyExtraDataOtherStrategyButton->setShow(0); // 不显示【其他策略】按钮
                } else {
                    if ($fromHistory) { // 历史消息不显示【其他策略】按钮
                        $pbChatReplyExtraDataOtherStrategyButton->setShow(0);
                    } else {
                        $pbChatReplyExtraDataOtherStrategyButton->setShow(1);
                        $pbChatReplyExtraDataOtherStrategyButton->setName(\Yii::t('ai', "其他策略"));
                    }
                }
                $pbChatReplyExtraData->setOtherStrategyButton($pbChatReplyExtraDataOtherStrategyButton);
                // 设置【回复内容】部分
                $pbChatReplyExtraDataReply = new PBChatReplyExtraDataReply;
                $pbChatReplyExtraDataReply->setTitle(\Yii::t('ai', "回复内容"));
                $pbChatReplyExtraDataReply->setReply($context['content'] ?? '');
                $translatedReply = $context['translate'] ?? ''; // 翻译后的回复内容
                if (!empty($translatedReply)) {
                    // 流式结束的时候才会有翻译后的回复内容
                    $pbChatReplyExtraDataReply->setTranslatedReply($translatedReply);
                }
                $pbChatReplyExtraData->setReply($pbChatReplyExtraDataReply);
                if (!empty($translatedReply)) {
                    // 设置【英语】按钮部分
                    // 流式结束的时候才会设置【英语】按钮，并且追问的时候不显示【英语】按钮
                    $language = $context['language'] ?? ''; // 默认的翻译语种 - 英语
                    $languageList = $context['language_list'] ?? []; // 可选的翻译语种下拉列表
                    $pbChatReplyExtraDataTranslateButton = new PBTranslateButton;
                    if ($fromHistory) { // 历史消息不显示【翻译】按钮
                        $pbChatReplyExtraDataTranslateButton->setShow(0);
                    } else if (empty($languageList)) {
                        $pbChatReplyExtraDataTranslateButton->setShow(0); // 不显示【翻译】按钮
                    } else {
                        $pbChatReplyExtraDataTranslateButton->setShow(1);
                        $pbChatReplyExtraDataTranslateButton->setName($language);
                        $translateButtonOptions = [];
                        foreach ($languageList as $languageItem) {
                            $pbChatReplyExtraDataTranslateButtonOption = new PBTranslateButtonOption;
                            $pbChatReplyExtraDataTranslateButtonOption->setLanguage($languageItem['zh']);
                            $pbChatReplyExtraDataTranslateButtonOption->setLanguageEn($languageItem['en']);
                            $pbChatReplyExtraDataTranslateButtonOption->setLanguageCode($languageItem['code']);
                            $translateButtonOptions[] = $pbChatReplyExtraDataTranslateButtonOption;
                        }
                        $pbChatReplyExtraDataTranslateButton->setOptions($translateButtonOptions);
                    }
                    $pbChatReplyExtraData->setTranslateButton($pbChatReplyExtraDataTranslateButton);
                }
                // 设置流输出类型
                $pbChatReplyExtraData->setStreamType($context['stream_type'] ?? '');
                $PBExtraData->setChatReplyExtraData($pbChatReplyExtraData);
                $PBMessageInfo->setExtra($PBExtraData);

                break;
            case AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_REPLY_OTHER_STRATEGY_LIST_CARD:
                // 辅助回复小卡片 - 【其他回复策略列表】小卡片
                /**
                 * @var $messageInfo ChatReplyOtherStrategyListCard
                 */
                $title = $context['title']['text'] ?? '';
                $PBMessageInfo->setTitle($title);
                $subtitle = $context['subtitle']['text'] ?? '';
                $PBMessageInfo->setSubtitle($subtitle);

                // 设置组件，头部左侧、头部右侧、底部左侧、底部右侧、外部头部左侧、外部头部右侧、外部底部左侧
                $componentMessageArr = [];
                $headLeft = $context['header_left'] ?? [];
                $headRight = $context['header_right'] ?? [];
                $footerLeft = $context['footer_left'] ?? [];
                $footerRight = $context['footer_right'] ?? [];
                $outsideHeaderLeft = $context['outside_header_left'] ?? [];
                $outsideHeaderRight = $context['outside_header_right'] ?? [];
                $outsideFooterLeft = $context['outside_footer_left'] ?? [];
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headLeft, 1));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headRight, 2));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerLeft, 3));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerRight, 4));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderLeft, 5));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderRight, 6));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideFooterLeft, 7));
                $PBMessageInfo->setComponentList($componentMessageArr);

                // 设置卡片内容
                $PBExtraData = new PBMessageExtra();
                // 设置【其他回复策略】列表
                $PBChatReplyStrategyList = new PBChatReplyOtherStrategyList();
                $PBChatReplyStrategyList->setTitle(\Yii::t('ai', "其他回复策略"));
                $strategyList = $context['options'] ?? [];
                $optionsRes = [];
                foreach ($strategyList as $option) {
                    $PBChatReplyOption = new PBChatReplyStrategy();
                    $PBChatReplyOption->setStrategyId($option['strategy_id'] ?? 0);
                    $PBChatReplyOption->setContent($option['text'] ?? '');
                    $optionsRes[] = $PBChatReplyOption;
                }
                $PBChatReplyStrategyList->setOptions($optionsRes);
                if ($fromHistory) { // 历史消息显示【其他回复策略】列表，但控制其状态为不可点击
                    $PBChatReplyStrategyList->setShow(0);
                } else {
                    $PBChatReplyStrategyList->setShow(1);
                }
                $PBExtraData->setChatReplyOtherStrategyList($PBChatReplyStrategyList);
                $PBMessageInfo->setExtra($PBExtraData);

                break;
            case AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_REPLY_OTHER_STRATEGY_REPLY_CARD:
                // 辅助回复小卡片 - 【其他回复策略回复】小卡片
                /**
                 * @var $messageInfo ChatReplyOtherStrategyReplyCard
                 */
                $title = $context['title']['text'] ?? '';
                $PBMessageInfo->setTitle($title);
                $subtitle = $context['subtitle']['text'] ?? '';
                $PBMessageInfo->setSubtitle($subtitle);

                // 设置组件，头部左侧、头部右侧、底部左侧、底部右侧、外部头部左侧、外部头部右侧、外部底部左侧
                $componentMessageArr = [];
                $headLeft = $context['header_left'] ?? [];
                $headRight = $context['header_right'] ?? [];
                $footerLeft = $context['footer_left'] ?? [];
                $footerRight = $context['footer_right'] ?? [];
                $outsideHeaderLeft = $context['outside_header_left'] ?? [];
                $outsideHeaderRight = $context['outside_header_right'] ?? [];
                $outsideFooterLeft = $context['outside_footer_left'] ?? [];
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headLeft, 1));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headRight, 2));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerLeft, 3));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerRight, 4));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderLeft, 5));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderRight, 6));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideFooterLeft, 7));
                $PBMessageInfo->setComponentList($componentMessageArr);

                // 设置卡片内容
                $PBExtraData = new PBMessageExtra();
                $pbChatReplyOtherStrategyReplyExtraData = new PBChatReplyOtherStrategyReplyExtraData();
                // 设置【回复内容】部分
                $pbChatReplyExtraDataReply = new PBChatReplyExtraDataReply;
                $pbChatReplyExtraDataReply->setTitle(\Yii::t('ai', "回复内容"));
                $pbChatReplyExtraDataReply->setReply($context['content'] ?? '');
                $translatedReply = $context['translate'] ?? ''; // 翻译后的回复内容
                if (!empty($translatedReply)) {
                    // 流式结束的时候才会有翻译后的回复内容
                    $pbChatReplyExtraDataReply->setTranslatedReply($translatedReply);
                }
                $pbChatReplyOtherStrategyReplyExtraData->setReply($pbChatReplyExtraDataReply);
                // 设置流输出类型
                $streamType = $context['stream_type'] ?? '';
                $pbChatReplyOtherStrategyReplyExtraData->setStreamType($streamType);
                $PBExtraData->setChatReplyOtherStrategyReplyExtraData($pbChatReplyOtherStrategyReplyExtraData);
                $PBMessageInfo->setExtra($PBExtraData);

                break;
            case AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_COACH_CARD:
                // 沟通建议小卡片
                /**
                 * @var $messageInfo ChatCoachCard
                 */
                $title = $context['title']['text'] ?? '';
                $PBMessageInfo->setTitle($title);
                $subtitle = $context['subtitle']['text'] ?? '';
                $PBMessageInfo->setSubtitle($subtitle);

                // 设置组件，头部左侧、头部右侧、底部左侧、底部右侧、外部头部左侧、外部头部右侧、外部底部左侧
                $componentMessageArr = [];
                $headLeft = $context['header_left'] ?? [];
                $headRight = $context['header_right'] ?? [];
                $footerLeft = $context['footer_left'] ?? [];
                $footerRight = $context['footer_right'] ?? [];
                $outsideHeaderLeft = $context['outside_header_left'] ?? [];
                $outsideHeaderRight = $context['outside_header_right'] ?? [];
                $outsideFooterLeft = $context['outside_footer_left'] ?? [];
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headLeft, 1));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headRight, 2));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerLeft, 3));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerRight, 4));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderLeft, 5));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderRight, 6));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideFooterLeft, 7));
                $PBMessageInfo->setComponentList($componentMessageArr);

                // 设置卡片内容
                $PBExtraData = new PBMessageExtra();
                $pbChatCoachExtraData = new PBChatCoachExtraData();
                // 设置【沟通分析】部分
                $pbChatCoachExtraDataAnalysis = new PBChatCoachExtraDataAnalysis;
                $pbChatCoachExtraDataAnalysis->setTitle(\Yii::t('ai', "沟通分析"));
                $pbChatCoachExtraDataAnalysis->setAnalysis($context['analysis'] ?? '');
                $pbChatCoachExtraData->setAnalysis($pbChatCoachExtraDataAnalysis);
                // 设置【行动建议】部分
                // 追问的时候不显示【行动建议】部分
                $strategy = $context['strategy'] ?? [];
                if (!empty($strategy)) {
                    $pbChatCoachExtraDataStrategy = new PBChatCoachExtraDataStrategy;
                    $pbChatCoachExtraDataStrategy->setTitle(\Yii::t('ai', "行动建议"));
                    $pbChatCoachExtraDataStrategy->setStrategyList($strategy);
                    $pbChatCoachExtraData->setStrategy($pbChatCoachExtraDataStrategy);
                }
                // 设置流输出类型
                $streamType = $context['stream_type'] ?? '';
                $pbChatCoachExtraData->setStreamType($streamType);
                $PBExtraData->setChatCoachExtraData($pbChatCoachExtraData);
                $PBMessageInfo->setExtra($PBExtraData);

                break;
            case AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_POLISH_CARD:
                // 沟通润色小卡片
                /**
                 * @var $messageInfo ChatPolishCard
                 */
                $title = $context['title']['text'] ?? '';
                $PBMessageInfo->setTitle($title);
                $subtitle = $context['subtitle']['text'] ?? '';
                $PBMessageInfo->setSubtitle($subtitle);

                // 设置组件，头部左侧、头部右侧、底部左侧、底部右侧、外部头部左侧、外部头部右侧、外部底部左侧
                $componentMessageArr = [];
                $headLeft = $context['header_left'] ?? [];
                $headRight = $context['header_right'] ?? [];
                $footerLeft = $context['footer_left'] ?? [];
                $footerRight = $context['footer_right'] ?? [];
                $outsideHeaderLeft = $context['outside_header_left'] ?? [];
                $outsideHeaderRight = $context['outside_header_right'] ?? [];
                $outsideFooterLeft = $context['outside_footer_left'] ?? [];
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headLeft, 1));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headRight, 2));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerLeft, 3));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerRight, 4));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderLeft, 5));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderRight, 6));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideFooterLeft, 7));
                $PBMessageInfo->setComponentList($componentMessageArr);

                // 设置卡片内容
                $PBExtraData = new PBMessageExtra();
                $pbChatPolishExtraData = new PBChatPolishExtraData();
                // 设置【润色原文】部分
                $originalReply = $context['original_reply'] ?? '';
                $pbChatPolishExtraDataOriginalReply = new PBChatPolishExtraDataOriginalReply();
                if (empty($originalReply)) {
                    // 追问的时候不显示【润色原文】
                    $pbChatPolishExtraDataOriginalReply->setShow(0);
                } else {
                    $pbChatPolishExtraDataOriginalReply->setShow(1);
                    $pbChatPolishExtraDataOriginalReply->setTitle(\Yii::t('ai', "润色原文"));
                    $pbChatPolishExtraDataOriginalReply->setOriginalReply($originalReply);
                }
                $pbChatPolishExtraData->setOriginalReply($pbChatPolishExtraDataOriginalReply);
                // 设置【润色结果】部分
                $pbChatPolishExtraDataReply = new PBChatPolishExtraDataPolishedReply;
                $pbChatPolishExtraDataReply->setTitle(\Yii::t('ai', "润色结果"));
                $pbChatPolishExtraDataReply->setPolishedReply($context['content'] ?? '');
                $translatedReply = $context['translate'] ?? ''; // 翻译的润色结果
                if (!empty($translatedReply)) {
                    // 流式结束的时候才会有翻译后的润色结果
                    $pbChatPolishExtraDataReply->setTranslatedReply($translatedReply);
                }
                $pbChatPolishExtraData->setPolishedReply($pbChatPolishExtraDataReply);
                if (!empty($translatedReply)) {
                    // 设置【英语】按钮部分
                    // 流式结束的时候才会设置【英语】按钮，并且追问的时候不显示【英语】按钮
                    $language = $context['language'] ?? ''; // 默认的翻译语种 - 英语
                    $languageList = $context['language_list'] ?? []; // 可选的翻译语种下拉列表
                    $pbChatPolishExtraDataTranslateButton = new PBTranslateButton;
                    if ($fromHistory) { // 历史消息不显示【翻译】按钮
                        $pbChatPolishExtraDataTranslateButton->setShow(0);
                    } else if (empty($languageList)) {
                        $pbChatPolishExtraDataTranslateButton->setShow(0); // 不显示【翻译】按钮
                    } else {
                        $pbChatPolishExtraDataTranslateButton->setShow(1);
                        $pbChatPolishExtraDataTranslateButton->setName($language);
                        $translateButtonOptions = [];
                        foreach ($languageList as $languageItem) {
                            $pbChatPolishExtraDataTranslateButtonOption = new PBTranslateButtonOption;
                            $pbChatPolishExtraDataTranslateButtonOption->setLanguage($languageItem['zh']);
                            $pbChatPolishExtraDataTranslateButtonOption->setLanguageEn($languageItem['en']);
                            $pbChatPolishExtraDataTranslateButtonOption->setLanguageCode($languageItem['code']);
                            $translateButtonOptions[] = $pbChatPolishExtraDataTranslateButtonOption;
                        }
                        $pbChatPolishExtraDataTranslateButton->setOptions($translateButtonOptions);
                    }
                    $pbChatPolishExtraData->setTranslateButton($pbChatPolishExtraDataTranslateButton);
                }
                // 设置流输出类型
                $pbChatPolishExtraData->setStreamType($context['stream_type'] ?? '');
                $PBExtraData->setChatPolishExtraData($pbChatPolishExtraData);
                $PBMessageInfo->setExtra($PBExtraData);

                break;
            case AiAgentConstants::AI_AGENT_MESSAGE_TYPE_OPPORTUNITY_FOLLOW_UP_CARD:
                // 商机跟进小卡片
                /**
                 * @var $messageInfo OpportunityFollowUpCard
                 */
                $title = $context['title']['text'] ?? '';
                $PBMessageInfo->setTitle($title);
                $subtitle = $context['subtitle']['text'] ?? \Yii::t('ai', '根据沟通内容，已为你生成以下AI商机跟进内容');
                $PBMessageInfo->setSubtitle($subtitle);

                // 设置组件，头部左侧、头部右侧、底部左侧、底部右侧、外部头部左侧、外部头部右侧、外部底部左侧
                $componentMessageArr = [];
                $headLeft = $context['header_left'] ?? [];
                $headRight = $context['header_right'] ?? [];
                $footerLeft = $context['footer_left'] ?? [];
                $footerRight = $context['footer_right'] ?? [];
                $outsideHeaderLeft = $context['outside_header_left'] ?? [];
                $outsideHeaderRight = $context['outside_header_right'] ?? [];
                $outsideFooterLeft = $context['outside_footer_left'] ?? [];
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headLeft, 1));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headRight, 2));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerLeft, 3));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerRight, 4));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderLeft, 5));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderRight, 6));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideFooterLeft, 7));
                $PBMessageInfo->setComponentList($componentMessageArr);

                // 设置卡片内容
                $PBMessageInfo->setContent($context['content'] ?? '');
                break;
            case AiAgentConstants::AI_AGENT_MESSAGE_TYPE_OPPORTUNITY_LIST_CARD:
                // 多个商机列表小卡片
                /**
                 * @var $messageInfo OpportunityListCard
                 */
                $title = $context['title']['text'] ?? '';
                $PBMessageInfo->setTitle($title);
                $subtitle = $context['subtitle']['text'] ?? '';
                $PBMessageInfo->setSubtitle($subtitle);

                // 设置组件，头部左侧、头部右侧、底部左侧、底部右侧、外部头部左侧、外部头部右侧、外部底部左侧
                $componentMessageArr = [];
                $headLeft = $context['header_left'] ?? [];
                $headRight = $context['header_right'] ?? [];
                $footerLeft = $context['footer_left'] ?? [];
                $footerRight = $context['footer_right'] ?? [];
                $outsideHeaderLeft = $context['outside_header_left'] ?? [];
                $outsideHeaderRight = $context['outside_header_right'] ?? [];
                $outsideFooterLeft = $context['outside_footer_left'] ?? [];
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headLeft, 1));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headRight, 2));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerLeft, 3));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerRight, 4));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderLeft, 5));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderRight, 6));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideFooterLeft, 7));
                $PBMessageInfo->setComponentList($componentMessageArr);

                // 设置卡片内容
                $PBExtraData = new PBMessageExtra();
                // 设置【其他回复策略】列表
                $PBOpportunityList = new PBOpportunityList();
                $PBOpportunityList->setTitle($context['content'] ?? \Yii::t('ai', '当前客户存在多条商机，请选择要跟进的商机：'));
                $opportunityList = $context['options'] ?? [];
                $optionsRes = [];
                foreach ($opportunityList as $option) {
                    $PBFollowUpOpportunity = new PBFollowUpOpportunity();
                    $PBFollowUpOpportunity->setCompanyId($option['company_id'] ?? 0);
                    $PBFollowUpOpportunity->setOpportunityId($option['opportunity_id'] ?? 0);
                    $PBFollowUpOpportunity->setOpportunityName($option['opportunity_name'] ?? '');
                    $optionsRes[] = $PBFollowUpOpportunity;
                }
                $PBOpportunityList->setOpportunities($optionsRes);
                $PBOpportunityList->setSelectedOpportunityId($context['selected_opportunity_id'] ?? 0);
                $PBOpportunityList->setAiOpportunityContentRecordId($context['ai_opportunity_content_record_id'] ?? 0);
                $PBExtraData->setOpportunityList($PBOpportunityList);
                $PBMessageInfo->setExtra($PBExtraData);
                
                break;
            case AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CUSTOMER_FOLLOW_UP_CARD:
                // 客户跟进小卡片
                /**
                 * @var $messageInfo CustomerFollowUpCard
                 */
                $title = $context['title']['text'] ?? '';
                $PBMessageInfo->setTitle($title);
                $subtitle = $context['subtitle']['text'] ?? \Yii::t('ai', '根据沟通内容，已为你生成以下AI客户跟进内容');
                $PBMessageInfo->setSubtitle($subtitle);

                // 设置组件，头部左侧、头部右侧、底部左侧、底部右侧、外部头部左侧、外部头部右侧、外部底部左侧
                $componentMessageArr = [];
                $headLeft = $context['header_left'] ?? [];
                $headRight = $context['header_right'] ?? [];
                $footerLeft = $context['footer_left'] ?? [];
                $footerRight = $context['footer_right'] ?? [];
                $outsideHeaderLeft = $context['outside_header_left'] ?? [];
                $outsideHeaderRight = $context['outside_header_right'] ?? [];
                $outsideFooterLeft = $context['outside_footer_left'] ?? [];
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headLeft, 1));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headRight, 2));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerLeft, 3));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerRight, 4));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderLeft, 5));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderRight, 6));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideFooterLeft, 7));
                $PBMessageInfo->setComponentList($componentMessageArr);

                // 设置卡片内容
                $PBMessageInfo->setContent($context['content'] ?? '');
                break;
            case AiAgentConstants::AI_AGENT_MESSAGE_TYPE_FOLLOW_UP_TEXT:
                // 商机/客户跟进文本消息
                /**
                 * @var $messageInfo FollowUpText
                 */
                $title = $context['title']['text'] ?? '';
                $PBMessageInfo->setTitle($title);
                $subtitle = $context['subtitle']['text'] ?? '';
                $PBMessageInfo->setSubtitle($subtitle);

                // 设置组件，头部左侧、头部右侧、底部左侧、底部右侧、外部头部左侧、外部头部右侧、外部底部左侧
                $componentMessageArr = [];
                $headLeft = $context['header_left'] ?? [];
                $headRight = $context['header_right'] ?? [];
                $footerLeft = $context['footer_left'] ?? [];
                $footerRight = $context['footer_right'] ?? [];
                $outsideHeaderLeft = $context['outside_header_left'] ?? [];
                $outsideHeaderRight = $context['outside_header_right'] ?? [];
                $outsideFooterLeft = $context['outside_footer_left'] ?? [];
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headLeft, 1));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headRight, 2));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerLeft, 3));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerRight, 4));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderLeft, 5));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderRight, 6));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideFooterLeft, 7));
                $PBMessageInfo->setComponentList($componentMessageArr);

                // 设置卡片内容
                $PBExtraData = new PBMessageExtra();
                $sceneType = $context['json']['scene_type'] ?? 0;
                if ($sceneType === \AiAgent::AI_AGENT_SCENE_TYPE_OPPORTUNITY_FOLLOW_UP) {
                    $pbOpportunityFollowUpTextExtraData = new PBOpportunityFollowUpTextExtraData();
                    $pbOpportunityFollowUpTextExtraData->setContent($context['json']['message_content'] ?? '');
                    $pbOpportunityFollowUpTextExtraData->setCheckDetailLinkName($context['json']['check_detail_content'] ?? '');
                    $pbOpportunityFollowUpTextExtraData->setOpportunityId($context['json']['opportunity_id'] ?? 0); // 该字段为空的话，说明是异常情况消息卡片复用该结构来返回
                    $PBExtraData->setOpportunityFollowUpTextExtraData($pbOpportunityFollowUpTextExtraData);
                } else if ($sceneType === \AiAgent::AI_AGENT_SCENE_TYPE_CUSTOMER_FOLLOW_UP) {
                    $pbCustomerFollowUpTextExtraData = new PBCustomerFollowUpTextExtraData();
                    $pbCustomerFollowUpTextExtraData->setContent($context['json']['message_content'] ?? '');
                    $pbCustomerFollowUpTextExtraData->setCheckDetailLinkName($context['json']['check_detail_content'] ?? '');
                    $pbCustomerFollowUpTextExtraData->setCompanyId($context['json']['company_id'] ?? 0);
                    $PBExtraData->setCustomerFollowUpTextExtraData($pbCustomerFollowUpTextExtraData);
                }
                $PBMessageInfo->setExtra($PBExtraData);

                // 设置卡片内容（商机/客户跟进场景下触发的异常情况返回的消息卡片的错误内容复用该字段）
                $PBMessageInfo->setContent($context['content'] ?? '');
                break;
            case AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_FOLLOW_UP_ERROR_CARD:
                // 客户跟进/商机跟进的跟进错误（错误类型：ERR_NOT_ARCHIVE_COMPANY）卡片
                /**
                 * @var $messageInfo FollowUpErrorCard
                 */
                $title = $context['title']['text'] ?? '';
                $PBMessageInfo->setTitle($title);
                $subtitle = $context['subtitle']['text'] ?? '';
                $PBMessageInfo->setSubtitle($subtitle);

                // 设置组件，头部左侧、头部右侧、底部左侧、底部右侧、外部头部左侧、外部头部右侧、外部底部左侧
                $componentMessageArr = [];
                $headLeft = $context['header_left'] ?? [];
                $headRight = $context['header_right'] ?? [];
                $footerLeft = $context['footer_left'] ?? [];
                $footerRight = $context['footer_right'] ?? [];
                $outsideHeaderLeft = $context['outside_header_left'] ?? [];
                $outsideHeaderRight = $context['outside_header_right'] ?? [];
                $outsideFooterLeft = $context['outside_footer_left'] ?? [];
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headLeft, 1));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headRight, 2));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerLeft, 3));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerRight, 4));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderLeft, 5));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderRight, 6));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideFooterLeft, 7));
                $PBMessageInfo->setComponentList($componentMessageArr);

                // 设置卡片内容（错误信息）
                $PBMessageInfo->setContent($context['content'] ?? '');
                break;
            case AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_NOT_IN_PROGRESS_CARD:
                // 客户跟进/商机跟进的跟进错误（错误类型：ERR_NOT_IN_PROGRESS_OPPORTUNITY、ERR_NOT_PERMISSION_FOLLOW_UP_CUSTOMER）卡片
                /**
                 * @var $messageInfo FollowUpNotInProgressCard
                 */
                $title = $context['title']['text'] ?? '';
                $PBMessageInfo->setTitle($title);
                $subtitle = $context['subtitle']['text'] ?? '';
                $PBMessageInfo->setSubtitle($subtitle);

                // 设置组件，头部左侧、头部右侧、底部左侧、底部右侧、外部头部左侧、外部头部右侧、外部底部左侧
                $componentMessageArr = [];
                $headLeft = $context['header_left'] ?? [];
                $headRight = $context['header_right'] ?? [];
                $footerLeft = $context['footer_left'] ?? [];
                $footerRight = $context['footer_right'] ?? [];
                $outsideHeaderLeft = $context['outside_header_left'] ?? [];
                $outsideHeaderRight = $context['outside_header_right'] ?? [];
                $outsideFooterLeft = $context['outside_footer_left'] ?? [];
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headLeft, 1));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headRight, 2));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerLeft, 3));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerRight, 4));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderLeft, 5));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderRight, 6));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideFooterLeft, 7));
                $PBMessageInfo->setComponentList($componentMessageArr);

                // 设置卡片内容（错误信息）
                $PBMessageInfo->setContent($context['content'] ?? '');
                break;
            case AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_EXPIRED_ERROR_CARD:
                // 辅助回复的沟通过期错误（错误类型：ERR_CHAT_EXPIRED）卡片
                /**
                 * @var $messageInfo ChatExpiredErrorCard
                 */
                $title = $context['title']['text'] ?? '';
                $PBMessageInfo->setTitle($title);
                $subtitle = $context['subtitle']['text'] ?? '';
                $PBMessageInfo->setSubtitle($subtitle);

                // 设置组件，头部左侧、头部右侧、底部左侧、底部右侧、外部头部左侧、外部头部右侧、外部底部左侧
                $componentMessageArr = [];
                $headLeft = $context['header_left'] ?? [];
                $headRight = $context['header_right'] ?? [];
                $footerLeft = $context['footer_left'] ?? [];
                $footerRight = $context['footer_right'] ?? [];
                $outsideHeaderLeft = $context['outside_header_left'] ?? [];
                $outsideHeaderRight = $context['outside_header_right'] ?? [];
                $outsideFooterLeft = $context['outside_footer_left'] ?? [];
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headLeft, 1));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headRight, 2));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerLeft, 3));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerRight, 4));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderLeft, 5));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderRight, 6));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideFooterLeft, 7));
                $PBMessageInfo->setComponentList($componentMessageArr);

                // 设置卡片内容（错误信息）
                $PBMessageInfo->setContent($context['content'] ?? '');
                break;
            case AiAgentConstants::AI_AGENT_MESSAGE_TYPE_DATA_ASSISTANT_COMPLEX_PROBLEM_ERROR:
                /**
                 * @var $messageInfo ComplexproblemErrorCard
                 */
                $title = $context['title'] ?? [];
                $subtitle = $context['subtitle'] ?? [];
                $content = $context['content'] ?? '';
                $PBMessageInfo->setContent($content['text'] ?? '');
                $PBMessageInfo->setTitle($title['text'] ?? '');
                $PBMessageInfo->setSubtitle($subtitle['text'] ?? '');
                break;
            case AiAgentConstants::AI_AGENT_MESSAGE_TYPE_DATA_ASSISTANT_COMPLEX_PROBLEM:
                /**
                 * @var $messageInfo ComplexProblemCard
                 */
                $title = $context['title'] ?? [];
                $subtitle = $context['subtitle'] ?? [];
                $data = $context['data'] ?? [];
                $footerLeft = $context['footer_left'] ?? [];
                $footerRight = $context['footer_right'] ?? [];
                $headRight = $context['header_right'] ?? [];
                $headLeft = $context['header_left'] ?? [];
                $outsideHeaderLeft = $context['outside_header_left'] ?? [];
                $PBMessageInfo->setTitle($title['text'] ?? '');
                $PBMessageInfo->setSubtitle($subtitle['text'] ?? '');
                $componentMessageArr = [];
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headLeft, 1));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headRight, 2));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerLeft, 3));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerRight, 4));
                $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderLeft, 5));
                $PBMessageInfo->setComponentList($componentMessageArr);

                if (!empty($data)) {
                    $multipleConversationInfo = [];
                    $PBMultipleMessageInfo = new PBMultipleConversationMessage();
                    $PBMessageData = new PBMessageData();

                    foreach ($data as $cardDatum) {
                        $PBConversationInfo = new PBConversationMessage();
                        $commonMessageInfo = self::constructMessageInfoFromSingleSseObject($cardDatum, ['messageType' => $cardDatum['message_type'], 'answer' => $cardDatum['context']['answer'] ?? ''], $fromHistory);
                        $PBConversationInfo->setMessageInfo($commonMessageInfo);
                        $PBConversationInfo->setMessageType($cardDatum['message_type']);
                        $multipleConversationInfo[] = $PBConversationInfo;
                    }
                    $PBMultipleMessageInfo->setConversationMessages($multipleConversationInfo);
                    $PBMessageData->setMultipleConversationMessage($PBMultipleMessageInfo);
                    $PBMessageInfo->setData($PBMessageData);
                }


                $PBExtra = new PBMessageExtra();
                $PBDataAssistantExtra = new PBDataAssistantExtraData();
                if (!empty($context['deep_analysis'])) {
                    $PBDeepAnalysis = new PBDeepAnalysisData();
                    $PBDeepAnalysis->setText($context['deep_analysis']);
                    $PBDataAssistantExtra->setDeepAnalysis($PBDeepAnalysis);
                    $PBExtra->setDataAssistantExtraData($PBDataAssistantExtra);
                }
                if (!empty($context['analysis_strategy'])) {
                    $PBAnalysisStrategy = new PBAnalysisStrategyData();
                    $PBAnalysisStrategy->setText($context['analysis_strategy']);
                    $PBDataAssistantExtra->setAnalysisStrategy($PBAnalysisStrategy);
                    $PBExtra->setDataAssistantExtraData($PBDataAssistantExtra);
                }
                $PBMessageInfo->setExtra($PBExtra);
                break;
            default:
                break;
        }
        return $PBMessageInfo;
    }

    public static function buildPBConfig($configInfo,$configType = PBAiAxisType::TYPE_SERIES) {
        if (empty($configInfo)) return [];
        $res = [];
        foreach ($configInfo as $item) {
            $axisInfo = new PBGenerationDataAxisInfo();
            $axisInfo->setAxisType($configType);
            $axisInfo->setFieldName($item['name']);
            $axisInfo->setFieldId($item['field']);
            $axisInfo->setCurrency($item['currency'] ?? '');
            $axisInfo->setHighLight($item['high_light'] ?? false);
            $res[] = $axisInfo;
        }
        return $res;
    }

    public static function buildPBMessageComponent($componentInfo, $componentType) {
        if (empty($componentInfo)) return [];
        $res = [];
        foreach ($componentInfo as $item) {
            $dropIt = false;

            $type = $item['type'] ?? '';
            $componentContent = $item['text'] ?? '';
            // 有些是text有些又是content - -!
            $componentContent = empty($componentContent) ? $item['content'] ?? '' : $componentContent;
            $PBComponentInfoItem = new  PBMessageComponent();
            $PBComponentInfoItem->setComponentType($componentType);
            $PBComponentInfoItem->setContent($componentContent);
            $PBComponentInfoItem->setOption($item['type'] ?? '');
            $PBMessageComponentParams = new PBMessageComponentParams();
            switch ($type) {
                // 为了兼容Web端和App端两者间的消息协议，卡片封装层会封装完整的小卡片，此处再做过滤。
                case "select":
                    $selectParams = $item['params'] ?? [];
                    $PBParams = new PBMessageComponentSelectParams();
                    $PBParams->setSelectType($item['value'] ?? 'bar');
                    $itemArr = [];
                    foreach ($selectParams as $param) {
                        $PBItem = new PBMessageComponentSelectParamsItem();
                        $PBItem->setLabel($param['label'] ?? '');
                        $PBItem->setValue($param['value'] ?? '');
                        $itemArr[] = $PBItem;
                    }
                    $PBParams->setSelectItem($itemArr);
                    $PBMessageComponentParams->setSelectParams($PBParams);
                    break;
                case "feedback":
                    $feedbackParams = $item['params'] ?? [];
                    $PBParams = new PBMessageComponentFeedbackParams();
                    $recordId = $feedbackParams['record_id'] ?? 0;
                    $favorite = $feedbackParams['favorite'] ?? 0;
                    $remark = $feedbackParams['remark'] ?? '';
                    $PBParams->setRecordId($recordId);
                    $PBParams->setFavorite($favorite);
                    $PBParams->setRemark($remark);
                    $PBMessageComponentParams->setFeedbackParams($PBParams);
                    break;
                case "detail":
                    $detail = new \protobuf\OkkiAi\PBMessageComponentDetailParams();
                    $PBReferList = [];
                    $dataParams = $item['dataParams'];
//                    self::reconstruct2Array($dataParams);
                    // 这里要对unique key做去重
                    $uniqueKeySet = [];
                    foreach ($dataParams as $dataItem) {

                        $referItem = new \protobuf\OkkiAi\PBReferItem();
                        $uniqueKey = $dataItem['report_item_unique_key'] ?? '';
                        if (in_array($uniqueKey, $uniqueKeySet)) {
                            continue;
                        }
                        $uniqueKeySet[] = $uniqueKey;
                        $referItem->setUniqueKey($uniqueKey);
                        $referItem->setReferTitle($dataItem['refer_title'] ?? '');
                        $referList = $dataItem['refer_list'] ?? '';
                        $referType = self::referListToReferType($referList);
                        $referItem->setReferType($referType);
                        $PBReferList[] = $referItem;

                    }
                    $detail->setReferList($PBReferList);
                    $PBMessageComponentParams = new \protobuf\OkkiAi\PBMessageComponentParams();
                    $PBMessageComponentParams->setDetailParams($detail);
                    break;
                case "button":
                    $event = $item['event'] ?? '';
                    $params = $item['params'] ?? [];
                    $PBMessageComponentParams = new \protobuf\OkkiAi\PBMessageComponentParams();
                    $PBButtonParams = new PBMessageComponentButtonParams();

                    switch ($event) {
                        case Button::EVENT_COPY:
                            $content = $params['content'] ?? '';
                            $PBCopyParams = new PBComponentButtonCopyParams();
                            $PBCopyParams->setContent($content);
                            $PBButtonParams->setCopyParams($PBCopyParams);
                            break;
                        case Button::EVENT_SET_SCENE_TYPE:
                            $PBSetSceneTypeParams = new PBComponentButtonSetSceneTypeParams();
                            $PBSetSceneTypeParams->setSceneType($params['sceneType'] ?? 0);
                            $PBSetSceneTypeParams->setRecordId($params['recordId'] ?? 0);
                            $PBSetSceneTypeParams->setLastSceneType($params['lastSceneType'] ?? 0);
                            $PBButtonParams->setSetSceneTypeParams($PBSetSceneTypeParams);
                            break;
                        case Button::EVENT_SAVE_TO_GUIDE:
                            // App端不需要显示【录入谈单指南】按钮
                            $dropIt = true;
                            break;
                        case Button::EVENT_REPLACE_POLISH_CONTENT:
                            // App端不需要显示【替换】按钮
                            $dropIt = true;
                            break;
                        case Button::EVENT_CREATE_OPPORTUNITY:
                            $companyId = $params['company_id'] ?? 0;
                            $companyName = $params['company_name'] ?? '';
                            $PBCreateOpportunityParams = new PBComponentButtonCreateOpportunityParams();
                            $PBCreateOpportunityParams->setCompanyId($companyId);
                            $PBCreateOpportunityParams->setCompanyName($companyName);
                            $PBButtonParams->setCreateOpportunityParams($PBCreateOpportunityParams);
                            break;
                        default:
                            break;
                    }

                    $PBMessageComponentParams->setButtonParams($PBButtonParams);
                    break;

                default:
                    break;

            }
            $PBComponentInfoItem->setParams($PBMessageComponentParams);
            if ($dropIt) {
                continue;
            }
            $res[] = $PBComponentInfoItem;
        }
        return $res;
    }

    // 构建数据
    public static function buildPBGenerateDataMessageData($data) {
        $PBMessageData = new PBMessageData();
        if (empty($data)) {
            return $PBMessageData;
        }
        // 统一转换成数组结构
        self::reconstruct2Array($data);
        $PBGenerateDataList = new PBGenerateDataList();
        $res = [];
        foreach ($data as $dataItem) {
            $dataItemObj = $dataItemArrList = [];
            $PBDataItem = new PBGenerateDataItem();
            foreach ($dataItem as $key => $item) {
                $item['value'] === null && $item['value'] = '';
                $item['value'] == 0 && $item['value'] = '0';
                $item['refer_list'] = $item['refer_list'] ?? '';
                $item['report_item_unique_key'] = $item['report_item_unique_key'] ?? '';
                $item['refer_type'] = self::referListToReferTypeString($item['refer_list']);
                $item['id'] = $item['id'] ?? 0;

                $dataItemObj[$key] = $item['value'];
                $dataItemObj['refer_list'] = $item['refer_list'];
                $dataItemObj['report_item_unique_key'] = $item['report_item_unique_key'];
                $dataItemObj['refer_type'] = self::referListToReferTypeString($item['refer_list']);
                $dataItemObj['id'] = $item['id'];

                $dataItemArr = new PBDataArr();
                $dataItemArr->setKey($item['key'] ?? $key);
                $dataItemArr->setValue($item['value']);
                $dataItemArr->setReferType($item['refer_type']);
                $dataItemArr->setReportItemUniqueKey($item['report_item_unique_key']);
                $dataItemArr->setReferId($item['id']);
                $dataItemArrList[] = $dataItemArr;
            }

            $PBDataItem->setDataItem($dataItemObj);
            $PBDataItem->setDataItemArr($dataItemArrList);
            $res[] = $PBDataItem;
        }
        $PBGenerateDataList->setGenerateDataItem($res);
        $PBMessageData->setGenerateDataList($PBGenerateDataList);
        return $PBMessageData;
    }

    /**
     * 获取最新一条task任务信息
     */
    public static function getLastTask(int $clientId, int $userId, array $scene = [AsyncTaskConstant::SCENE_AI_ASSET_ANALYSIS], array $status = [AsyncTaskConstant::TASK_STATUS_SUCCEED], int $taskId = 0)
    {
        // 获取最新一条task任务信息
        $taskFilter = new \common\library\async_task\AsyncTaskFilter($clientId);
        $taskFilter->type = \Constants::TYPE_OKKI_AI;
        $taskFilter->update_user = $userId;
        $taskFilter->scene = $scene;
        if (!empty($status)) {
            $taskFilter->status = $status;
        }
        if (!empty($taskId)) {
            $taskFilter->task_id = $taskId;
        }
        $taskFilter->order('task_id', 'desc');
        $taskFilter->limit(1);
        $taskList = $taskFilter->rawData();

        return $taskList[0] ?? [];
    }

    public static function formatReportDataToChartStruct(array $reportData)
    {
        // 格式化 x,y 轴
        $config = $reportData['config'] ?? [];
        $field = $config['group'] ?? []; // 获取x轴
        $summaries = $config['summaries'] ?? []; //获取y轴
        $chart = $reportData['chart']['chartList'] ?? [];
        $label = array_column($config['field'] ?? [], 'label', 'key');
        // 初始化X轴
        $XAxis = [];
        foreach ($field as $item)
        {
            $key = $item['key'] ?? '';
            $XAxis[] = [
                'field' => $key,
                'name' => $label[$key] ?? ''
            ];
        }

        // 初始化Y轴
        $YAxis = [];
        $fieldType = [];
        foreach ($summaries as $item)
        {
            $YAxis[] = [
                'field' => $item['key'] ?? '',
                'name' => $item['label'] ?? ''
            ];

            $fieldType[$item['key']] =  $item['type'];
        }

        // 初始化配置
        $config = [
            'XAxis' => $XAxis,
            'YAxis' => $YAxis,
            'charType' => AiAgentConstants::CHAR_TYPES,
            'chatFlag' => true,
            'fieldType' => $fieldType,
            'chartType' => $chart[0]['chartType'] ?? 'table',
        ];

        $exitOneMoreGroup = count($XAxis) > 1;
        $dataList = $reportData['data'] ?? [];

        // 处理二维分组
        $exitOneMoreGroup && $groupList = array_map(function ($item){
            return reset($item);
        }, $dataList);

        // 初始化数据
        $dataList = array_map(function ($item) {
            return end($item);
        }, $dataList);


        $content = [];
        foreach ($dataList as $index => $data)
        {
            $dataContent = [];

            $dataSummaryList = $data['summaries'] ?? [];

            foreach ($dataSummaryList as $summaryKey => $summaryItem)
            {
                $summaryItemValue = (($summaryItem['value'] === '-') || empty($summaryItem['value'])) ? 0 : $summaryItem['value'];
                $method = $summaryItem['method'] ?? '';
                if ($method == 'percent' && !$summaryItemValue) {
                    $summaryItemValue = (string) $summaryItemValue . '%';
                }
                $dataContent[$summaryKey] = $summaryItemValue;
            }

            $key = $data['key'] ?? '';
            $dataContent[$key] = $data['label'];
            $exitOneMoreGroup && $dataContent[$groupList[$index]['key']] = $groupList[$index]['label'];

            $content[] = $dataContent;
        }

        // 初始化标题
        $title = [
            'text' => '数据详情'
        ];

        return [
            'config' => $config,
            'content' => $content,
            'title' => $title
        ];
    }

    public static function getAnalysisNumber($clientId, $scene = AsyncTaskConstant::SCENE_AI_ASSET_ANALYSIS)
    {
        switch ($scene) {
            case AsyncTaskConstant::SCENE_AI_TEAM_ANALYSIS:
                $configInstance = TeamAnalysisConfig::getInstance($clientId);
                break;
            default:
                // 获取Config
                $configInstance = \common\library\ai_agent\config\AssetAnalysisConfig::getInstance($clientId);
        }

        $config = $configInstance->getConfig();
        $analysisList = array_column($config,'analysis_list');

        $analysisNumber = count($analysisList);
        foreach ($analysisList as $item)
        {
            $subAnalysisList = array_column($item,'sub_analysis_list');

            foreach ($subAnalysisList as $subAnalysis)
            {
                $listCount = array_map(function ($analysis) {
                    return count($analysis['list']);
                }, $subAnalysis);
                $analysisNumber += array_sum($listCount);
            }
        }

        return $analysisNumber;
    }

    public static function getDataSourceType($params)
    {

        foreach ($params as $index => $value)
        if ($value['type'] == 'data_source_type') {
            return $value['value'];
        }
        // 历史数据都是基于订单分析
        return AiAgentConstants::COMPANY_ANALYSIS_DATA_SOURCE_TYPE_ORDER;
    }


    /**
     * 加入MySQL中间库待同步TM消息 账号表
     * @see https://xmkm.yuque.com/giz7cq/apseko/aoaylimv9krwg81l
     * @param int $clientId
     * @param array $alibabaAccountIds 阿里账户ID、对应的店铺ID
     * @return void
     */
    public static function addToSyncTmMessageList(int $clientId, array $alibabaAccountIds = []): void
    {
        // 判断是否开通OKKI AI
        $privilegeService = new \common\library\privilege_v3\ClientPrivilegeService($clientId, 0);
        if (!$privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_OKKI_AI)) {
            return;
        }

        if (empty($alibabaAccountIds)) {
            // 查询该client的全部正常授权状态的阿里账户ID、对应的店铺ID
            $alibabaAccounts = AlibabaAccount::findByClientId($clientId) ?? [];
            $alibabaAccountIds = array_column($alibabaAccounts, 'store_id', 'seller_account_id');
        }

        // 写入中间库MySQL
        foreach ($alibabaAccountIds as $alibabaAccountId => $storeId) {
            self::addSyncAccount($clientId, 0, (int) $alibabaAccountId, (int) $storeId);

            // 后台脚本同步消息
            $job = new TmMessageSyncJob($clientId, $alibabaAccountId);
            QueueService::dispatch($job);
        }
    }


    /**
     * 写入需要同步消息的阿里账号
     * @param int $clientId
     * @param int $userId
     * @param int $alibabaAccountId
     * @param int $storeId
     * @return void
     * @throws CDbException
     * @throws CException
     */
    public static function addSyncAccount(int $clientId, int $userId, int $alibabaAccountId, int $storeId): void
    {
        // 先判断是否已经有记录
        $sql = "SELECT * FROM tbl_alibaba_account WHERE client_id=$clientId AND account_id=$alibabaAccountId AND company_id=$storeId";

        $db = Yii::app()->tm_message_db;
        if ($db->createCommand($sql)->queryAll()) {
            // 已经有记录，不用再写入
            LogUtil::info("addSyncAccount skip", ['ali_account_id' => $alibabaAccountId, 'store_id' => $storeId,]);
            return;
        }

        // 先删除，再插入
        $sql1 = "DELETE FROM tbl_alibaba_account WHERE account_id=$alibabaAccountId";

        $sql2 = "INSERT INTO tbl_alibaba_account (`client_id`, `user_id`, `account_id`, `company_id`, `sync_finish_flag`)" .
            "VALUES ($clientId, $userId, $alibabaAccountId, $storeId, 0)";

        $connection = $db->beginTransaction();
        try {
            $db->createCommand($sql1)->execute();
            $db->createCommand($sql2)->execute();
            $connection->commit();
        } catch (\Throwable $e) {
            LogUtil::error("addSyncAccountFail", [
                'ali_account_id' => $alibabaAccountId,
                'store_id' => $storeId,
                'error' => $e->getMessage(),
            ]);
        }

        LogUtil::info("addSyncAccount", ['ali_account_id' => $alibabaAccountId, 'store_id' => $storeId,]);
    }

    /**
     * 获取会话的最后一条记录
     *
     * @param int $clientId
     * @param int $userId
     * @param int $conversationId
     * @param bool $mustHasChatInfo
     * @return
     */
    public static function getLastHistoryOfConversation(
        int $clientId, int $userId, int $agentId, int $conversationId,
        array $mustHasExtInfo = [], int $businessType = AiAgentConstants::BUSINESS_TYPE_CRM)
    {

        $sql = 'SELECT * FROM tbl_ai_agent_conversation_history
                    WHERE conversation_id=:conversation_id and client_id=:client_id
                      and business_type=:business_type
                      and user_id=:user_id and agent_id=:agent_id';

        foreach ($mustHasExtInfo as $info) {
            $sql .= " AND (ext_info->>'{$info}'::text) IS NOT NULL ";
        }

        $sql .= ' ORDER BY history_id DESC LIMIT 1';

        $params = [
            ':conversation_id' => $conversationId,
            ':client_id' => $clientId,
            ':user_id' => $userId,
            ':agent_id' => $agentId,
            ':business_type' => $businessType,
        ];
        $db = PgActiveRecord::getDbByClientId($clientId);
        return $db->createCommand($sql)->queryRow(true, $params);
    }

    public static function getOriginNameKeyById(int $clientId, int $originScope): array
    {
        // 获取全部来源信息
        $api = new \common\library\setting\library\origin\OriginApi($clientId);
        $api->setApiFields(['item_id', 'item_name', 'node']);
        $data = $api->tree(0, true, true);

        // 格式化Id指向名称
        $originNameKeyById = [];
        foreach ($data as $datum)
        {
            // 假如筛选一级，那么二级需要指向一级
            $nodeList = $datum['node'] ?? [];
            foreach ($nodeList as $item)
            {
                $nodeItemId = $item['item_id'] ?? 0;
                $nodeItemName = $item['item_name'] ?? 0;
                $itemName = $datum['item_name'] ?? '';

                $originNameKeyById[$nodeItemId] = ($originScope == \common\library\statistics\decorator\field_type\OriginList::ORIGIN_SCOPE_ONE) ? $itemName : $nodeItemName;
            }

            $itemId = $datum['item_id'] ?? 0;
            $originNameKeyById[$itemId] = $datum['item_name'] ?? '';
        }

        return $originNameKeyById;
    }

    /**
     * 客户资产分析返回列表，字段需要新增单位，前端用户展示"%"等
     */
    public static function AssetAnalysisConfigAssignUnit($analysisRecordInfo)
    {

        $referData = $analysisRecordInfo['refer_data'] ?? '';
        $referData = json_decode($referData,true);
        $data =  $referData['data'] ?? [];

        // 处理X轴数据，赋值单位
        $XAxis = $data['config']['XAxis'] ?? [];

        foreach ($XAxis as $key => $XAxi)
        {
            $field = $XAxi['field'] ?? '';
            $unit = AssetAnalysisConfig::FIELD_UNIT_MAP[$field] ?? '';

            if (!empty($unit)) {
                $XAxis[$key]['unit'] = $unit;
            }
        }

        $data['config']['XAxis'] = $XAxis;

        // 处理Y轴数据，赋值单位
        $YAxis = $data['config']['YAxis'] ?? [];

        foreach ($YAxis as $key => $YAxi)
        {
            $field = $YAxi['field'] ?? '';
            $unit = AssetAnalysisConfig::FIELD_UNIT_MAP[$field] ?? '';

            if (!empty($unit)) {
                $YAxis[$key]['unit'] = $unit;
            }
        }

        $data['config']['YAxis'] = $YAxis;


        $referData['data'] = $data;
        $analysisRecordInfo['refer_data'] = json_encode($referData);

        return $analysisRecordInfo;
    }


    /**
     * 钉钉告警
     * @param BaseAiAgent|AiAgentProxy $agent
     * @param \Throwable $throwable
     * @return void
     */
    public static function notifyToDingTalk(BaseAiAgent|AiAgentProxy $agent, \Throwable $throwable, array $debugInfo = [], $errorCode = 0)
    {
        $env = \Yii::app()->params['env'];
        //测试环境不告警
        if ($env == 'test') return ;

        $clientId = $agent->clientId;

        $agentId = $agent->agentId;

        $dingTalkMobile = '';
        $dingTalkUserId = '';

        if(in_array($errorCode,AiAgentException::DE_NOISE_ERRORS)){
            $dingTalk = new DingTalkRobot(null,'SEC562752422f5048a5eb8db441bcb1c87cc590fc28c3f76c8cd35d969b23afd82f');
            $dingTalk->setWebhook(\common\modules\prometheus\library\dingtalk\DingtalkConstant::GROUP_AI_WARNING_DE_NOISE);
        }else{
            $dingTalk = new DingTalkRobot(null,'SECe73d6f7d49adeb200864b51445ad7ee453ef7172bf3dc1e24ca8d43bf9ddcafc');
            $dingTalk->setWebhook(\common\modules\prometheus\library\dingtalk\DingtalkConstant::GROUP_AI_WARNING);

            $redis = \RedisService::getInstance('redis');
            $warningSetting = $redis->get(\common\library\ai_service\AiServiceConstant::AI_SERVICE_WARNING_SETTING_CACHE_KEY);
            $warningSetting = json_decode($warningSetting, true);
            $dingTalkMobile = $warningSetting[$agentId]['dingTalkMobile'] ?? '';
            $dingTalkUserId = $warningSetting[$agentId]['dingTalkUserId'] ?? '';
        }

        $title = $agent->agentName."异常告警 -".$env;
        $traces = $throwable->getTrace();
        $topTrace = $traces[0] ?? [];
        $markdownStr = $title . "
===\n";

        $userInfo = new UserInfo($agent->userId);
        $account = $userInfo->email;

        $file = $topTrace['file'] ?? '';
        $line = $topTrace['line'] ?? 0;
        $class = $topTrace['class'] ?? '';
        $function = $topTrace['function'] ?? '';
        $args = json_encode($topTrace['args'] ?? [],JSON_UNESCAPED_UNICODE);
        $traceId = \Xiaoman\Sidekick\getCurrentTraceId();
        $message = $throwable->getMessage();
        $recordId = $agent->context['record_id'] ?? 0;
        $question = '';
        if ($agent->sceneType == \AiAgent::AI_AGENT_SCENE_TYPE_GENERATION_DATA) {
            $question = $agent->question;
        }


        $markdownStr .= "## 辅助信息
env: {$env} \n
告警处理人: @{$dingTalkMobile} \n
record_id: {$recordId} \n
trace_id: {$traceId} \n
问题: {$question} \n";
        foreach ($debugInfo as $key => $value) {
            $markdownStr .= "$key: $value\n\n";
        }
        $markdownStr .= "\n--- \n\n";

        $markdownStr .= "## 用户信息:
clientId: {$clientId} \n
userId : {$agent->userId} \n
account : {$account} \n

 \n \n ";
        $markdownStr .= "--- \n\n";
        $markdownStr .= "## 报错路径
{$file} -> {$function} \n\n";
        $markdownStr .= "--- \n";
        $markdownStr .= "## 类
{$class} : ** {$line} ** \n\n";
        $markdownStr .= "--- \n";
        $markdownStr .= "## 参数
{$args} \n\n";
        $markdownStr .= "--- \n";
        $markdownStr .= "## 错误信息
{$message}  \n\n";
        $markdownStr .= "--- \n";


        $at = [
            "at" => [
                "isAtAll" => false,
                "atUserIds"=> [
                    $dingTalkUserId
                ],
                "atMobiles"=> [
                    $dingTalkMobile
                ]
            ]
        ];

        $dingTalk->markdown($title,$markdownStr, $at);
    }

    public static function PBParamsToArray(\protobuf\OkkiAi\PBAiAgentChatCompletionsReqParams $params, int $sceneType): array
    {
        $res = [];
        switch ($sceneType){
            case PBAgentSceneType::SCENE_TYPE_MAIL_WRITE:
                $mailWriteParams = $params->getMailWriteParams();
                $res['language'] = $mailWriteParams->getLanguage();
                $res['language_en'] = $mailWriteParams->getLanguageEn();
                $res['tone'] = $mailWriteParams->getTone();
                $res['type'] = $mailWriteParams->getType();
                $res['company_name'] = $mailWriteParams->getCompanyName();
                $res['product_name'] = $mailWriteParams->getProductName();
                $res['extra_desc'] = $mailWriteParams->getExtraDesc();
                $res['must_contains'] = $mailWriteParams->getMustContains();
                break;
            case PBAgentSceneType::SCENE_TYPE_MAIL_POLISH:
                $mailPolishParams = $params->getMailPolishParams();
                $res['language_en'] = $mailPolishParams->getLanguageEn();
                $res['language'] = $mailPolishParams->getLanguage();
                $res['tone'] = $mailPolishParams->getTone();
                $res['content'] = $mailPolishParams->getContent();
                $res['extra_desc'] = $mailPolishParams->getExtraDesc();
                break;
            case PBAgentSceneType::SCENE_TYPE_GENERATION_DATA:
                $dataDistributionParams = $params->getDataDistributionParams();
                if (!empty($dataDistributionParams)) {
                    $res['company_id'] = $dataDistributionParams->getCompanyId();
                    $res['answerableQuestionRecommendFlag'] = $dataDistributionParams->getShowAnswerableQuestionRecommend();
                    $res['dataInterpretationFlag'] = $dataDistributionParams->getShowDataInterpretation();
                }
                break;
            case PBAgentSceneType::SCENE_TYPE_MAIL_SUMMARY:
                $dataMailSummaryParams = $params->getMailSummaryParams();
                if (!empty($dataMailSummaryParams)) {
                    $res['mail_id'] = $dataMailSummaryParams->getMailId();
                }
                break;
            case PBAgentSceneType::SCENE_TYPE_MAIL_REPLY:
                $dataMailReplyParams = $params->getMailReplyParams();
                if (!empty($dataMailReplyParams)) {
                    $res['mail_id'] = $dataMailReplyParams->getMailId();
                    $res['language'] = $dataMailReplyParams->getLanguage();
                    $res['lengthOption'] = $dataMailReplyParams->getLengthOption();
                    $res['strategy_id'] = $dataMailReplyParams->getStrategyId();
                }
                break;
            case PBAgentSceneType::SCENE_TYPE_MAIL_QUALITY:
                $dataMailQualityParams = $params->getMailQualityParams();
                if (!empty($dataMailQualityParams)) {
                    $res['mail_id'] = $dataMailQualityParams->getMailId();
                }
                break;
            case PBAgentSceneType::SCENE_TYPE_CHAT_REPLY;
                $chatReplyParams = $params->getChatReplyParams();
                if (!empty($chatReplyParams)) {
                    $res['channel_type'] = $chatReplyParams->getChannelType();
                    if ($chatReplyParams->getChannelType() == \common\library\ai_agent\communication\Constants::CHANNEL_TYPE_TM) {
                        // TM渠道不要设置'user_sns_id'和'sns_id', -.-!!!
                        $res['seller_account_id'] = $chatReplyParams->getSellerAccountId();
                        $res['buyer_account_id'] = $chatReplyParams->getBuyerAccountId();
                    } else if ($chatReplyParams->getChannelType() == \common\library\ai_agent\communication\Constants::CHANNEL_TYPE_WHATSAPP_CLOUD) {
                        $res['user_sns_id'] = $chatReplyParams->getSellerAccountId();
                        $res['sns_id'] = $chatReplyParams->getBuyerAccountId();
                        $res['conversation_id'] = $chatReplyParams->getConversationId();
                        $res['anchor'] = [
                            'message_id' => $chatReplyParams->getAnchorMessageId(),
                            'send_time' => $chatReplyParams->getAnchorSendTime(),
                        ];
                    } else {
                        $res['seller_account_id'] = $chatReplyParams->getSellerAccountId();
                        $res['buyer_account_id'] = $chatReplyParams->getBuyerAccountId();
                    }
                    // 更换回复策略要使用的参数
                    $res['strategy_id'] = $chatReplyParams->getStrategyId();
                }
                break;
            case PBAgentSceneType::SCENE_TYPE_CHAT_COACH;
                $chatCoachParams = $params->getChatCoachParams();
                if (!empty($chatCoachParams)) {
                    $res['channel_type'] = $chatCoachParams->getChannelType();
                    if ($chatCoachParams->getChannelType() == \common\library\ai_agent\communication\Constants::CHANNEL_TYPE_TM) {
                        // TM渠道不要设置'user_sns_id'和'sns_id', -.-!!!
                        $res['seller_account_id'] = $chatCoachParams->getSellerAccountId();
                        $res['buyer_account_id'] = $chatCoachParams->getBuyerAccountId();
                    } else if ($chatCoachParams->getChannelType() == \common\library\ai_agent\communication\Constants::CHANNEL_TYPE_WHATSAPP_CLOUD) {
                        $res['user_sns_id'] = $chatCoachParams->getSellerAccountId();
                        $res['sns_id'] = $chatCoachParams->getBuyerAccountId();
                        $res['conversation_id'] = $chatCoachParams->getConversationId();
                    } else {
                        $res['seller_account_id'] = $chatCoachParams->getSellerAccountId();
                        $res['buyer_account_id'] = $chatCoachParams->getBuyerAccountId();
                    }
                }
                break;
            case PBAgentSceneType::SCENE_TYPE_CHAT_POLISH;
                $chatPolishParams = $params->getChatPolishParams();
                if (!empty($chatPolishParams)) {
                    $res['channel_type'] = $chatPolishParams->getChannelType();
                    if ($chatPolishParams->getChannelType() == \common\library\ai_agent\communication\Constants::CHANNEL_TYPE_TM) {
                        // TM渠道不要设置'user_sns_id'和'sns_id', -.-!!!
                        $res['seller_account_id'] = $chatPolishParams->getSellerAccountId();
                        $res['buyer_account_id'] = $chatPolishParams->getBuyerAccountId();
                    } else if ($chatPolishParams->getChannelType() == \common\library\ai_agent\communication\Constants::CHANNEL_TYPE_WHATSAPP_CLOUD) {
                        $res['user_sns_id'] = $chatPolishParams->getSellerAccountId();
                        $res['sns_id'] = $chatPolishParams->getBuyerAccountId();
                        $res['conversation_id'] = $chatPolishParams->getConversationId();
                    } else {
                        $res['seller_account_id'] = $chatPolishParams->getSellerAccountId();
                        $res['buyer_account_id'] = $chatPolishParams->getBuyerAccountId();
                    }
                    // 针对哪条回复消息进行修改
                    $res['original_reply'] = $chatPolishParams->getOriginalReply();
                }
                break;
            case PBAgentSceneType::SCENE_TYPE_OPPORTUNITY_FOLLOW_UP:
                $opportunityFollowUpParams = $params->getOpportunityFollowUpParams();
                if (!empty($opportunityFollowUpParams)) {
                    $res['channel_type'] = $opportunityFollowUpParams->getChannelType();
                    if ($opportunityFollowUpParams->getChannelType() == \common\library\ai_agent\communication\Constants::CHANNEL_TYPE_TM) {
                        // TM渠道不要设置'user_sns_id'和'sns_id', -.-!!!
                        $res['seller_account_id'] = $opportunityFollowUpParams->getSellerAccountId();
                        $res['buyer_account_id'] = $opportunityFollowUpParams->getBuyerAccountId();
                    } else if ($opportunityFollowUpParams->getChannelType() == \common\library\ai_agent\communication\Constants::CHANNEL_TYPE_WHATSAPP_CLOUD) {
                        $res['user_sns_id'] = $opportunityFollowUpParams->getSellerAccountId();
                        $res['sns_id'] = $opportunityFollowUpParams->getBuyerAccountId();
                        $res['conversation_id'] = $opportunityFollowUpParams->getConversationId();
                    } else {
                        $res['seller_account_id'] = $opportunityFollowUpParams->getSellerAccountId();
                        $res['buyer_account_id'] = $opportunityFollowUpParams->getBuyerAccountId();
                    }
                    // 用于捞出AI生成的商机跟进内容
                    $res['record_id'] = $opportunityFollowUpParams->getRecordId();
                    // 用于判断是否需要创建商机跟进记录
                    $res['need_create'] = $opportunityFollowUpParams->getNeedCreate();
                    // 用于判断是否需要跳过商机检查
                    $res['skip_check_opportunity'] = $opportunityFollowUpParams->getSkipCheckOpportunity();
                    // 用于判断是否需要返回多商机列表小卡片
                    $res['show_opportunity_list'] = $opportunityFollowUpParams->getShowOpportunityList();
                    // 待创建商机跟进记录的商机 ID
                    $res['opportunity_id'] = $opportunityFollowUpParams->getOpportunityId();
                    // 商机列表这条聊天记录的 ID
                    $res['opportunity_list_history_id'] = $opportunityFollowUpParams->getOpportunityListHistoryId();
                }
                break;
            case PBAgentSceneType::SCENE_TYPE_CUSTOMER_FOLLOW_UP:
                $customerFollowUpParams = $params->getCustomerFollowUpParams();
                if (!empty($customerFollowUpParams)) {
                    $res['channel_type'] = $customerFollowUpParams->getChannelType();
                    if ($customerFollowUpParams->getChannelType() == \common\library\ai_agent\communication\Constants::CHANNEL_TYPE_TM) {
                        // TM渠道不要设置'user_sns_id'和'sns_id', -.-!!!
                        $res['seller_account_id'] = $customerFollowUpParams->getSellerAccountId();
                        $res['buyer_account_id'] = $customerFollowUpParams->getBuyerAccountId();
                    } else if ($customerFollowUpParams->getChannelType() == \common\library\ai_agent\communication\Constants::CHANNEL_TYPE_WHATSAPP_CLOUD) {
                        $res['user_sns_id'] = $customerFollowUpParams->getSellerAccountId();
                        $res['sns_id'] = $customerFollowUpParams->getBuyerAccountId();
                        $res['conversation_id'] = $customerFollowUpParams->getConversationId();
                    } else {
                        $res['seller_account_id'] = $customerFollowUpParams->getSellerAccountId();
                        $res['buyer_account_id'] = $customerFollowUpParams->getBuyerAccountId();
                    }
                    // 用于捞出AI生成的客户跟进内容
                    $res['record_id'] = $customerFollowUpParams->getRecordId();
                    // 用于判断是否需要创建客户跟进记录
                    $res['need_create'] = $customerFollowUpParams->getNeedCreate();
                }
                break;

        }
        return $res;
    }

    public static function ArrayToPBParams(array $paramsArr, int $sceneType): \protobuf\OkkiAi\PBAiAgentChatCompletionsReqParams {
        $PBParams = new \protobuf\OkkiAi\PBAiAgentChatCompletionsReqParams();
        switch ($sceneType){
            case PBAgentSceneType::SCENE_TYPE_MAIL_WRITE:
                $mailParams = new \protobuf\OkkiAi\PBAiAgentMailWriteChatReqParams();
                try {
                    $mailParams->mergeFromJsonString(json_encode($paramsArr), true);
                } catch (\Throwable $e) {
                    \LogUtil::info("mergeFromJsonString error", ["exception" => $e]);
                }
                $PBParams->setMailWriteParams($mailParams);
                break;
            case PBAgentSceneType::SCENE_TYPE_MAIL_POLISH:
                $polishParams = new \protobuf\OkkiAi\PBAiAgentMailPolishChatReqParams();
                try {
                    $polishParams->mergeFromJsonString(json_encode($paramsArr), true);
                } catch (\Throwable $e) {
                    \LogUtil::info("mergeFromJsonString error", ["exception" => $e]);
                }
                $PBParams->setMailPolishParams($polishParams);

        }
        return $PBParams;
    }

    public static function getTableKeyInfo($table, $key)
    {
         $keyMap = [
           'tbl_company' => [
               'report_key' => 'CompanyAiAgentChatCompletionList:',
               'primary_key' => 'tbl_company.company_id',
               'refer_list' => AiAgentConstants::GenerateDataCompanyList,
               'refer_detail' => AiAgentConstants::GenerateDataCompanyDetail,
               'view_permission' => \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW,
               'functional' => PrivilegeConstants::FUNCTIONAL_CUSTOMER,
               'primary_key_alias' => 'tbl_company_primary_key',
               'refer_type' => \Constants::TYPE_COMPANY,
               'field_refer_type' => \Constants::TYPE_COMPANY,
               'object_name' => ['company_name', '客户公司名称'],
           ],
             'tbl_order' => [
                 'report_key' =>  'OrderAiAgentChatCompletionList:',
                 'primary_key' => 'tbl_order.order_id',
                 'refer_list' => AiAgentConstants::GenerateDataOrderList,
                 'refer_detail' => AiAgentConstants::GenerateDataOrderDetail,
                 'view_permission' => \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_CRM_ORDER_VIEW,
                 'functional' => PrivilegeConstants::FUNCTIONAL_ORDER,
                 'primary_key_alias' => 'tbl_order_primary_key',
                 'refer_type' => \Constants::TYPE_ORDER,
                 'field_refer_type' => \Constants::TYPE_ORDER,
                 'object_name' => ['order_name', '订单名称', 'order_no', '订单编号']
             ],
             'tbl_opportunity' => [
                 'report_key' =>  'OpportunityAiAgentChatCompletionList:',
                 'primary_key' => 'tbl_opportunity.opportunity_id',
                 'refer_list' => AiAgentConstants::GenerateDataOpportunityList,
                 'refer_detail' => AiAgentConstants::GenerateDataOpportunityDetail,
                 'view_permission' => \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_VIEW,
                 'functional' => PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
                 'primary_key_alias' => 'tbl_opportunity_primary_key',
                 'refer_type' => \Constants::TYPE_OPPORTUNITY,
                 'field_refer_type' => \Constants::TYPE_OPPORTUNITY,
                 'object_name' => ['opportunity_name', '商机名称', 'serial_id', '商机编号']
             ],
             'tbl_lead' => [
                 'report_key' => 'LeadAiAgentChatCompletionList:',
                 'primary_key' => 'tbl_lead.lead_id',
                 'refer_list' => AiAgentConstants::GenerateDataLeadList,
                 'refer_detail' => AiAgentConstants::GenerateDataLeadDetail,
                 'view_permission' => \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_CRM_LEAD_VIEW,
                 'primary_key_alias' => 'tbl_lead_primary_key',
                 'refer_type' => \Constants::TYPE_LEAD,
                 'object_name' => ['name', '线索名称'],
                 'functional' => PrivilegeConstants::FUNCTIONAL_LEAD,
                 'field_refer_type' => \Constants::TYPE_LEAD,


             ],
             'tbl_mail' => [
                 'report_key' => 'MailAiAgentChatCompletionList:',
                 'primary_key' => 'tbl_mail.mail_id',
                 'refer_list' => AiAgentConstants::GenerateDataMailList,
                 'refer_detail' => AiAgentConstants::GenerateDataMailDetail,
                 'view_permission' => \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_EMAIL_VIEW,
                 'primary_key_alias' => 'tbl_mail_primary_key',
                 'refer_type' => \Constants::TYPE_MAIL,
                 'object_name' => ['subject','主题','邮件主题'],
                 'functional' => PrivilegeConstants::FUNCTIONAL_MAIL,
                 'field_refer_type' =>  \Constants::TYPE_MAIL


             ],
             'tbl_invoice_product_record' => [
                 'report_key' => 'InvoiceProductAiAgentChatCompletionList:',
                 'primary_key' => 'tbl_invoice_product_record.refer_id',
                 'refer_list' => AiAgentConstants::GenerateDataOrderList,
                 'refer_detail' => AiAgentConstants::GenerateDataOrderDetail,
                 'view_permission' => \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_CRM_ORDER_VIEW,
                 'primary_key_alias' => 'tbl_invoice_product_record_primary_key',
                 'refer_type' => \Constants::TYPE_ORDER,
                 'functional' => PrivilegeConstants::FUNCTIONAL_PRODUCT,
                 'field_refer_type' =>  \Constants::TYPE_PRODUCT
//                 'object_name' => ['name', '线索名称'],
             ],
         ];
         return $keyMap[$table][$key] ?? '';
    }

    public static function covertTableName($table, $origin2alias = true)
    {

        $tableMap = [
            'tbl_company' => [
                'dev' => 'tbl_company_dsl_test',
                'default' => 'dwd_ai_tbl_company',
                'originTable' => 'tbl_company'
            ],
            'tbl_order' => [
                'dev' => 'tbl_order_dsl_test',
                'default' => 'dwd_ai_tbl_order',
                'originTable' => 'tbl_order'
            ],
            'tbl_opportunity' => [
                'dev' => 'tbl_opportunity_dsl_test',
                'default' => 'dwd_ai_tbl_opportunity',
                'originTable' => 'tbl_opportunity'
            ]
        ];

        if (\Yii::app()->params['env'] == 'test') {
            $tableMap = $origin2alias ? array_column($tableMap, 'dev', 'originTable') : array_column($tableMap, 'originTable', 'dev');
        } else {
            $tableMap = $origin2alias ? array_column($tableMap, 'default', 'originTable') : array_column($tableMap, 'originTable', 'default');
        }

        return $tableMap[$table] ?? '';
    }

    public static function referListToReferType(string $referList): int
    {
        $referListToReferType = [
            AiAgentConstants::GenerateDataCompanyList => libraryConstants::TYPE_COMPANY,
            AiAgentConstants::GenerateDataOrderList => libraryConstants::TYPE_ORDER,
            AiAgentConstants::GenerateDataOpportunityList => libraryConstants::TYPE_OPPORTUNITY,
            AiAgentConstants::GenerateDataLeadList => libraryConstants::TYPE_LEAD,
            AiAgentConstants::GenerateDataMailList => libraryConstants::TYPE_MAIL,
        ];

        return $referListToReferType[$referList] ?? 0;
    }

    public static function referListToReferTypeString(string $referList): string
    {
        $res = self::referListToReferType($referList);

        return "{$res}";
    }

    public static function getPresetQuestionList($getSuccessRateFlag = false): array
    {
        $redis = \RedisService::aiCache();
        $clientId = User::getLoginUser()->getClientId();
        $presetQuestionList = $redis->hgetall(\common\library\ai_agent\AiAgentConstants::CACHE_GENERATE_DATA_PRESET_QUESTION);
        $presetQuestionOrder = $redis->lrange(\common\library\ai_agent\AiAgentConstants::CACHE_GENERATE_DATA_PRESET_ORDER, 0, -1);

        $list = [];
        $index = 0;
        $translateKeys = ['desc_en','desc_ja','desc_sp','desc_ft','covertName_en','covertName_ja','covertName_sp','covertName_ft'];
        foreach ($presetQuestionOrder as $order => $question)
        {
            if (isset($presetQuestionList[$question]))
            {
                $sqlInfo = json_decode($presetQuestionList[$question], true);

                $listItem = [
                    'desc' => $question,
                    'covertName' => Yii::t('ai', Helper::covertPreQuestionPlaceholder($question, $clientId)),
                    'sql' => $sqlInfo['sql'] ?? '',
                    'covertSql' => Helper::covertAmountFieldSuffix($sqlInfo['sql'] ?? '', $clientId),
                    'greyClientIds' => $sqlInfo['greyClientIds'] ?? '',
                    'clickCount' => $sqlInfo['clickCount'] ?? '',
                    'indexValue' => $index,
                    'ownerType' => $sqlInfo['ownerType'] ?? 0,
                    'tag' => $sqlInfo['tag'] ?? '',
                    'showTag' => empty($sqlInfo['showTag']) ? ['all'] : $sqlInfo['showTag'],
                    'questionId' => $sqlInfo['questionId'] ?? 0
                ];

                foreach ($translateKeys as $key)
                {
                    if (empty($sqlInfo[$key])) continue;
                    $listItem[$key] = $sqlInfo[$key];
                }

                $list[] = $listItem;

                $index++;
            }

            unset($presetQuestionList[$question]);
        }

        foreach ($presetQuestionList as $desc => $dslInfo)
        {
            $sqlInfo = json_decode($dslInfo, true);
            $listItem = [
                'desc' => $desc,
                'covertName' => Helper::covertPreQuestionPlaceholder($desc, $clientId),
                'sql' => $sqlInfo['sql'] ?? '',
                'covertSql' => Helper::covertAmountFieldSuffix($sqlInfo['sql'] ?? '', $clientId),
                'greyClientIds' => $sqlInfo['greyClientIds'] ?? '',
                'clickCount' => $sqlInfo['clickCount'] ?? 0,
                'indexValue' => ++$index,
                'ownerType' => $sqlInfo['ownerType'] ?? 0,
                'tag' => $sqlInfo['tag'] ?? '',
                'showTag' => empty($sqlInfo['showTag']) ? ['all'] : $sqlInfo['showTag'],
                'question_id' => $sqlInfo['question_id'] ?? 0
            ];

            foreach ($translateKeys as $key)
            {
                if (empty($sqlInfo[$key])) continue;
                $listItem[$key] = $sqlInfo[$key];
            }

            $list[] = $listItem;

        }
        if($getSuccessRateFlag){
            $successRateList = self::getSuccessRate($list);
            $successRateMap = array_column($successRateList, 'success_rate', 'question');
            foreach ($list as &$item){
                if(!empty($successRateMap[$item['desc']])){
                    $item['success_rate'] = round($successRateMap[$item['desc']], 2);
                }
            }
            unset($item);
        }

        return $list;
    }


    public static function getSuccessRate($questionList)
    {
        $result = [];
        $questions =  [];
        foreach ($questionList as $item){
            if(empty($item['sql'])){
                $questions[] = $item['desc'];
            }
        }

        if(empty($questions)){
            return $result;
        }

        $questionSql = implode(',', array_map(function($item) {
            return "'" . $item . "'";
        }, $questions));
        $sql = "SELECT question,
       SUM(CASE WHEN is_failed THEN 0 ELSE 1 END) AS success_count,
       COUNT(*) AS total_count,
       (SUM(CASE WHEN is_failed THEN 0 ELSE 1 END)::FLOAT / COUNT(*)) * 100 AS success_rate
FROM (
         SELECT t1.record_id,
                t1.info->>'question' AS question,
                EXISTS (
                    SELECT 1
                    FROM tbl_ai_trace_log t2
                    WHERE t1.record_id = t2.record_id
                      AND t2.info->> 'error_message' is not null
                ) AS is_failed
         FROM tbl_ai_trace_log t1
         WHERE t1.info->>'question' IN ($questionSql)
     ) AS RecordStatus
GROUP BY question";

        return \PgActiveRecord::getDbByClientId(User::getLoginUser()->getClientId())->createCommand($sql)->queryAll();
    }

    public static function getPresetQuestion($questionId = 0, $questionName = '')
    {
        $redis = \RedisService::getInstance('redis');
        $clientId = User::getLoginUser()->getClientId();
        $presetQuestionStr = $redis->hget(\common\library\ai_agent\AiAgentConstants::CACHE_GENERATE_DATA_PRESET_QUESTION, $questionName);
        $presetQuestionArr = json_decode($presetQuestionStr, true);

        // 根据name获取不到就getall所有预置问题根据id取
        if (empty($presetQuestionArr)) {
            LogUtil::info('get_presetQuestion_empty');
            $list = self::getPresetQuestionList();
            $listIndexById = array_column($list, null, 'question_id');

            if (!empty($questionId)) {
                return $listIndexById[$questionId] ?? [];
            }

            if (!empty($questionName)) {
                // 构建所有语言版本的名称映射
                $nameMap = [];
                foreach ($list as $item) {
                    $nameMap[$item['covertName']] = $item;
                    !empty($item['covertName_en']) && $nameMap[$item['covertName_en']] = $item;
                    !empty($item['covertName_ja']) && $nameMap[$item['covertName_ja']] = $item;
                    !empty($item['covertName_sp']) && $nameMap[$item['covertName_sp']] = $item;
                }

                return $nameMap[$questionName] ?? [];
            }
        }

        $presetQuestion =  [
            'desc' => $presetQuestionArr['desc'],
            'covertName' => Yii::t('ai', Helper::covertPreQuestionPlaceholder($presetQuestionArr['desc'], $clientId)),
            'sql' => $presetQuestionArr['sql'] ?? '',
            'covertSql' => Helper::covertAmountFieldSuffix($presetQuestionArr['sql'] ?? '', $clientId),
            'greyClientIds' => $presetQuestionArr['greyClientIds'] ?? '',
            'clickCount' => $presetQuestionArr['clickCount'] ?? '',
            'tag' => $presetQuestionArr['tag'] ?? '',
            'ownerType' => $presetQuestionArr['ownerType'] ?? 0,
            'showTag' => empty($presetQuestionArr['showTag']) ? ['all'] : $presetQuestionArr['showTag'],
            'questionId' => $presetQuestionArr['questionId'] ?? 0
        ];

        return $presetQuestion;




    }

    public static function resolveResponse($originAnswer)
    {
        $originAnswer = str_replace('```json', '', $originAnswer);
        $originAnswer = str_replace('```sql', '', $originAnswer);
        $originAnswer = str_replace('```', '', $originAnswer);
        $answer = json_decode($originAnswer,true);
        // 这里可能会出一条sql语句，也可能会出来一个数组，数组是复杂sql的结构
        return $answer['PostgreSQL'] ?? $answer;
    }


    public static function checkSqlParserEnable($sql): bool
    {
        try {
            // 判断是否是中文开头
            $parrtern = "/^\p{Han}/u";
            if (preg_match($parrtern, $sql)) return false;
            // 是否是select开头
            if (!preg_match("/^select/i", $sql)) return false;
            //判断是否含有sql关键字,可能会过滤掉部分子查询，仅含有select的sql，但是不影响
            $sqlKeyWordMap = ['select', 'where', 'from'];
            $parsed = (new SQLParserService($sql))->parsed;
            $parserKeyList = array_map('strtolower',array_keys($parsed));
            if (count(array_intersect($sqlKeyWordMap, $parserKeyList)) != count($sqlKeyWordMap)) return false;
        } catch (\Throwable $throwable) {
            return false;
        }
        return true;
    }

    /**
     * 有些问题需要根据客户主币种变化，可以在这里转换
     * 以后有其他占位符转换，也可以在这里加
     * @param $question
     * @return array|mixed|string|string[]
     */
    public static function covertPreQuestionPlaceholder($question, $clientId): mixed
    {
        $currencyLabel = '{currency}';
        if (str_contains($question, $currencyLabel)) {
            $mainCurrency = Client::getClient($clientId)->getMainCurrency();
            $question = str_replace($currencyLabel, AiAgentConstants::CURRENCY_NAME_MAP[$mainCurrency] ?? "元", $question);
        }
        return $question;
    }

    /**
     * 针对sql查询金额字段与主币种不一致需要替换成对应主币种字段
     * @param $sql
     */
    public static function covertAmountFieldSuffix($sql, $clientId)
    {
        $mainCurrency = Client::getClient($clientId)->getMainCurrency();

        if ($mainCurrency == AiAgentConstants::CURRENCY_USD) {
            $sql = str_replace('_rmb', '_usd', $sql);
            $sql = str_replace('_cny', '_usd', $sql);
        }

        if ($mainCurrency == AiAgentConstants::CURRENCY_CNY) {
            str_contains($sql, '_usd') && (str_contains($sql, 'success_opportunity_amount') || str_contains($sql, 'success_opportunity_amount_avg')) && $sql = str_replace('_usd', '_cny', $sql);
            str_contains($sql, '_usd') && $sql = str_replace('_usd', '_rmb', $sql);
        }

        return $sql;

    }

    public static function syncSqlExp($setNo, $sql)
    {
        if ($setNo) {
            $setNos[] = $setNo;
        } else {
            $setNosSql = "SELECT set_no from tbl_exp_client where enable_flag = 1 and locked = 1 order by set_no asc limit 100000";
            $setNos = \Yii::app()->db->createCommand($setNosSql)->queryColumn();
        }

        // 批量插入体验库
        foreach ($setNos as $setNo)
        {
            $clientDbName = "v5_client_exp_online_{$setNo}";

            $v5ClientExpOnlineSet = \common\library\account\service\DbService::getDbByTypeAndName(1, $clientDbName);
            $mysqlDb = \ProjectActiveRecord::getDbByDbSetId($v5ClientExpOnlineSet['set_id']);
            $mysqlDb->createCommand($sql)->execute();

            isset($mysqlDb) && $mysqlDb->setActive(false);
        }
    }

    public static function getOriginNames($clientId)
    {
        // 获取全部来源信息
        $api = new \common\library\setting\library\origin\OriginApi($clientId);
        $api->setApiFields(['item_id', 'item_name', 'node']);
        $data = $api->tree(0, true, true);

        // 格式化Id指向名称
        $originNameIdInfos = [];
        foreach ($data as $datum)
        {
            $nodeList = $datum['node'] ?? [];
            foreach ($nodeList as $item)
            {
                $originNameIdInfos[] = [
                    'origin_id' => $item['item_id'] ?? '',
                    'origin_name' => $item['item_name'] ?? 0
                ];
            }

            $originNameIdInfos[] = [
                'origin_id' => $datum['item_id'] ?? '',
                'origin_name' => $datum['item_name'] ?? 0
            ];
        }

        return $originNameIdInfos;
    }

    public static function tipsWordConvert($word)
    {
        $user = \User::getLoginUser();
        $client = Client::getClient($user->getClientId());
        switch ($word)
        {
            case "loginUserNickName":
                $userNickName = $user->getNickname();
                $word = empty($userNickName) ? '#当前用户昵称#' : $userNickName;
                break;
            case "currency":
                $currency = $client->getMainCurrency();
                $word = $currency == "CNY" ? \Yii::t("ai","人民币") : \Yii::t("ai","美元");
                break;
        }

        return $word;
    }

    public static function getLatestConversationInfoBySceneType($clientId, $userId,$sceneType,
                                                                int $businessType = AiAgentConstants::BUSINESS_TYPE_CRM, $fromAssistant = false)
    {
        $agentIds = [];
        $agentId = 0;
        if ($fromAssistant) {
            $agentList = new AiAgentList();
            $agentList->setSceneType(PromptConstant::SCENE_TYPE_HISTORY_OWNERBY_COMMUNICATION);
            $list = $agentList->find();
            $agentIds = array_column($list,'agent_id');

        } else {
            $aiAgent = \AiAgent::model()::findBySceneType($sceneType);

            if (empty($aiAgent)) {
                throw new \RuntimeException('不支持的AI Agent');
            }

            $agentId = $aiAgent->agent_id;

        }
        $conversationList = new \common\library\ai_service\AiAgentConversationList($clientId, $userId, $agentId);
        $conversationList->setExternalAgentId($agentIds);
        $conversationList->setOrderBy("conversation_id");
        $conversationList->setOrder("desc");
        $conversationList->setAggregateProxyAgent(true);
        $conversationList->setBusinessType($businessType);
        $conversationList->setLimit(1);
        $conversationInfo = $conversationList->find();
        return  $conversationInfo[0] ?? [];

    }

    public static  function getDocumentModelType(int $docId, $defaultModel = EmbeddingService::MODEL_TYPE_BGE_M3)
    {
        $vectorDocument = new CommonVectorDocument($docId);
        return $vectorDocument->model_type ?: $defaultModel;
    }

    public static function getTipsWords($sceneType):array
    {
        // 存储Redis，方便配置关键词
        $redis = \common\library\cache\RedisCache::getCache();
        $key = AiAgentConstants::CACHE_TIPS_WORD_LIST;
        $agentTips = $redis->hget($key, $sceneType);
        if (empty($agentTips)) {
            $agentTips = AiAgentConstants::DEFAULT_AGENT_TIPS_WORD_LIST[$sceneType] ?? [];

            $list = $agentTips['list'] ?? [];
            foreach ($list as $key => $item)
            {
                $content = \Yii::t('ai' , $item['content']);
                $list[$key]['tips'] = \Yii::t('ai' , $item['tips']);
                $list[$key]['content'] = $content;

                preg_match_all('/{{(.*?)}}/', $content, $matches);
                $needReplaceStr = $matches[0] ?? '';

                foreach ($needReplaceStr as $value)
                {
                    $replaceStr = str_replace(array("{{", "}}"), "", $value);
                    $list[$key]['content'] = str_replace($value, Helper::tipsWordConvert($replaceStr), $content);
                }
            }
            $agentTips['list'] = $list;
        }
        $agentTips['count'] = count($agentTips['list'] ?? []);

        return $agentTips;
    }

    /**
     * 根据邮件信息 按照user => email 进行分组
     * @param array $companyIds
     * @param $startTime
     * @param $endTime
     * @return array
     */
    public static function getMailConversationMap(array $companyIds,  $startTime, $endTime, $userId = []) :array
    {
        $userIdToResultMap = [];
        $emailToCustomerId = [];
        $customerIdToCompanyId = [];
        $customerIdToMailTime = [];

        $userMailList = new \UserMailList(\User::getLoginUser()->getClientId());
        $userMailList->setUserId($userId);
        $userMailListInfo = $userMailList->find();

        $userMailIdToEmailMap = array_column($userMailListInfo,'email_address','user_mail_id');
        $userEmails = array_column($userMailListInfo,'email_address');


        $list = new \common\library\customer_v3\customer\CustomerList(\User::getLoginUser()->getClientId());
        $list->setCompanyId($companyIds);
        $list->setFields(['email','company_id','customer_id','mail_time']);
        // 获取到用户到email
        $customerInfo = $list->find();

        foreach ($customerInfo as $customerItem) {
            $email = $customerItem['email'];
            $companyId = $customerItem['company_id'];
            $customerId = $customerItem['customer_id'];
            $mailTime = $customerItem['mail_time'];

            if (!isset($emailToCompanyId[$email])) {
                $emailToCompanyId[$email] = [];
            }
            $emailToCompanyId[$email] = array_merge($emailToCompanyId[$email],[$companyId]);
            $emailToCustomerId[$email] = $customerId;
            $customerIdToCompanyId[$customerId] = $companyId;
            $customerIdToMailTime[$customerId] = $mailTime;
        }

        if (empty($emailToCompanyId)) return [];

        $mailList = new \common\library\mail\MailList();
        $mailList->setSearchModel(\Constants::SEARCH_MODEL_SEARCHER);
        $mailList->searcher->setSearchDate($startTime, $endTime);
        $mailList->searcher->setCustomerEmails(array_keys($emailToCompanyId));
        $mailList->searcher->setUserMailIds(array_keys($userMailIdToEmailMap));
        $mailList->setOrder("desc");
        $mailList->setOrderBy('receive_time');
        $mailList->searcher->setLimit(5000);
        $mailInfoList = $mailList->find();

        foreach ($mailInfoList as $info)
        {
            $userId = $info['user_id'];
            $userMailId = $info['user_mail_id'];
            $conversationId = $info['conversation_id'] ?? 0;
            $sender = strtolower(Util::getPureEmail($info['sender']));
            $receiveTime = $info['receive_time'];
            $cc = $info['cc'];
            $bcc = $info['bcc'];
            $ccArr = \common\library\email\Util::findAllEmails($cc);
            $bccArr = \common\library\email\Util::findAllEmails($bcc);
            $receiverArr = \common\library\email\Util::findAllEmails($info['receiver']);

            $allReceiveEmails = array_values(array_unique(array_merge($ccArr, $bccArr, $receiverArr)));

            foreach ($allReceiveEmails as $receiver)
            {
                // 业务员之间互相发邮件不算会话
                if (in_array($receiver, $userEmails) && in_array($sender,$userEmails)) continue;

                // 客户联系人之间互相发邮件不算会话
                if (!in_array($receiver,$userEmails) && !in_array($sender,$userEmails)) continue;


                $userEmailAddress = $userMailIdToEmailMap[$userMailId] ?? '';

                $userEmail = $sender == $userEmailAddress ? $sender : $receiver;
                $customerEmail = $sender == $userEmailAddress ? $receiver : $sender;

                $customerId = $emailToCustomerId[$customerEmail] ?? 0;

                if (empty($customerId)) continue;

                $companyId = $customerIdToCompanyId[$customerId] ?? 0;
                $mailTime = $customerIdToMailTime[$customerId] ?? '1970-01-01 00:00:00';
                $lastMessageTime = strtotime($mailTime) > strtotime($receiveTime) ? $mailTime : $receiveTime;

                if (empty($companyId)) continue;

                if (!isset($userIdToResultMap[$userId][$userMailId][$customerId]))
                {
                    $userIdToResultMap[$userId][$userMailId][$customerId] = [
                        'send_mail_count' => 0,
                        'receive_mail_count' => 0,
                        'company_id' => $companyId,
                        'conversation_id' => [],
                        'user_mail_id' => $userMailId,
                        'customer_emails' => $customerEmail,
                        'last_message_time' => $lastMessageTime,
                        'user_email' => $userEmailAddress
                    ];
                }

                $sender == $userEmailAddress && $userIdToResultMap[$userId][$userMailId][$customerId]['send_mail_count'] += 1;
                $receiver == $userEmailAddress && $userIdToResultMap[$userId][$userMailId][$customerId]['receive_mail_count'] += 1;

                $userIdToResultMap[$userId][$userMailId][$customerId]['conversation_id'] = array_unique(array_merge($userIdToResultMap[$userId][$userMailId][$customerId]['conversation_id'], [$conversationId]));

                $tmpMessageTime = $userIdToResultMap[$userId][$userMailId][$customerId]['last_message_time'];
                $lastMessageTime =  date("Y-m-d H:i:s",max(strtotime($tmpMessageTime), strtotime($lastMessageTime)));
                $userIdToResultMap[$userId][$userMailId][$customerId]['last_message_time'] = $lastMessageTime;
            }
        }
        return $userIdToResultMap;
    }

    public static function syncWideTable($clientId, $type = 0, $companyId = 0, $orderId = 0, $opportunityId = 0, $dryRun = 1)
    {
        ini_set("memory_limit", "2048M");

        if (empty($clientId)) {
            throw new \RuntimeException(\Yii::t('common','{param} cannot be empty',['{param}'=>'clientId']));
        }

        $pgDb = PgActiveRecord::getDbByClientId($clientId);

        $privilegeService = PrivilegeService::getInstance($clientId);
        $userId = $privilegeService->getAdminUserId();

        \User::setLoginUserById($userId);

        // 同步客户表
        if (!empty($companyId)) {
            $selectSql = "select * from tbl_company where client_id = {$clientId} and company_id = {$companyId}";
        } else {
            $selectSql = "select * from tbl_company where client_id = {$clientId}";
        }

        if ($type == 0 || $type == 1) {
            $companyInfos = $pgDb->createCommand($selectSql)->queryAll();
        } else {
            $companyInfos = [];
        }

        $userList = new \common\library\account\UserList();
        $userList->setClientId($clientId);
        $userListInfo = $userList->find();
        $userIdMap = array_column($userListInfo,'nickname','user_id');

        $groupApi = new \common\library\setting\library\group\GroupApi($clientId,\Constants::TYPE_COMPANY);
        $groupList = $groupApi->list(0);
        $groupMap = array_column($groupList,'name','id');

        $countryList = array_map(function ($item) {
            return [
                'code' => $item['alpha2'],
                'name' => $item['country_name'],
            ];
        }, \CountryService::getAllCountrys());

        $countryMap = array_column($countryList,'name','code');

        $bizList = \common\library\customer\Helper::getBizTypeList();
        $bizMap = array_column($bizList,'name','id');

        $api = new \common\library\setting\library\status\StatusApi($clientId, \Constants::TYPE_COMPANY);
        $trailStatusList = $api->listAll();
        $trailStatusMap = array_column($trailStatusList,'item_name','item_id');

        $tag = (new \common\library\setting\library\tag\TagApi($clientId, \Constants::TYPE_COMPANY, $userId));
        $tag->setOwnerUser([0],1);
        $clientTagList = $tag->list([]);
        $clientTagMap = array_column($clientTagList,'tag_name','tag_id');

        $list = (new \common\library\setting\library\pool\PoolApi($clientId))->list(\common\library\customer\pool\Helper::getUserPoolIds($clientId, $userId, true), true);
        $poolList = $list;
        $poolMap = array_column($poolList,'name','pool_id');

        $archiveTypeMap = [
            \common\library\customer_v3\company\orm\Company::ARCHIVE_TYPE_NORMAL => '手动创建',
            \common\library\customer_v3\company\orm\Company::ARCHIVE_TYPE_AI => '自动创建',
            \common\library\customer_v3\company\orm\Company::ARCHIVE_TYPE_ADVICE => '自动化建档建议',
            \common\library\customer_v3\company\orm\Company::ARCHIVE_TYPE_ALIBABA => '客户通同步',
            \common\library\customer_v3\company\orm\Company::ARCHIVE_TYPE_IMPORT => '文件导入',
        ];

        $alibabaStore = new \common\library\alibaba\store\AlibabaStoreList($clientId);
        $alibabaStore->setFields(['store_id', 'store_alias']);
        $aliStoreList = $alibabaStore->find();

        $aliStoreMap = array_column($aliStoreList,'store_alias','store_id');

        $growthMap = \common\library\setting\item\Api::growthLevel(\Constants::TYPE_COMPANY)->getExtraDataMap();

        $originList = Helper::getOriginNames($clientId);
        $originMap = array_column($originList, 'origin_name', 'origin_id');

        $categoryList = \common\library\product_v2\category\CategoryCacheableRepo::instance($clientId)->setEnableCache(false)->findAllByConditions( [], "id, prefix,cn_name,en_name");

        // 客户数据处理
        foreach ($companyInfos as &$companyInfo)
        {
            $followUserIds = \common\library\util\PgsqlUtil::trimArray($companyInfo['user_id']);
            $categoryIds = \common\library\util\PgsqlUtil::trimArray($companyInfo['category_ids']);
            $clientTag = \common\library\util\PgsqlUtil::trimArray($companyInfo['client_tag_list']);
            $growthLevels = \common\library\util\PgsqlUtil::trimArray($companyInfo['growth_level']);
            $origins = \common\library\util\PgsqlUtil::trimArray($companyInfo['origin_list']);

            $categoryIdArr = [];
            foreach ($categoryIds as $fieldArr) {
                $id = end($fieldArr);
                $value = $id;
                $map = array_column($categoryList,'cn_name','id');
                $categoryIdArr[] = $map[$value] ?? $value;
            }

            foreach ($followUserIds as $key => $followUserId) {
                $followUserIds[$key] = $userIdMap[$followUserId] ?? '未知';
            }

            $clientTagArr = [];
            foreach ($clientTag as $tag) {
                $clientTagArr[] = $clientTagMap[$tag] ?? '未知';
            }

            $growthLevelArr = [];
            foreach ($growthLevels as $growthLevel) {
                $growthLevelArr[] = $growthMap[$growthLevel] ?? '未知';
            }

            $originArr = [];
            foreach ($origins as $origin) {
                $originArr[] = $originMap[$origin];
            }

            $companyInfo['user_name'] = \common\library\util\PgsqlUtil::formatArray($followUserIds);
            $companyInfo['group_name'] = $groupMap[$companyInfo['group_id']] ?? '';
            $companyInfo['country_name'] = $countryMap[$companyInfo['country']] ?? '';
            $companyInfo['category_name'] = $categoryIdArr;
            $companyInfo['biz_name'] = $bizMap[$companyInfo['biz_type']] ?? '';
            $companyInfo['trail_status_name'] = $trailStatusMap[$companyInfo['trail_status']] ?? '{}';
            $companyInfo['client_tag_list_name'] = \common\library\util\PgsqlUtil::formatArray($clientTagArr);
            $companyInfo['pool_name'] = $poolMap[$companyInfo['pool_id']] ?? '公海分组';
            $companyInfo['archive_type_name'] = $archiveTypeMap[$companyInfo['archive_type']] ?? '';
            $companyInfo['ali_store_name'] = $aliStoreMap[$companyInfo['ali_store_id']] ?? '{}';
            $companyInfo['growth_level_name'] = \common\library\util\PgsqlUtil::formatArray($growthLevelArr);
            $companyInfo['origin_name'] = \common\library\util\PgsqlUtil::formatArray($originArr);
            $companyInfo['create_user_name'] = $userIdMap[$companyInfo['create_user']] ?? '';

            $setClause = '';
            foreach ($companyInfo as $column => $value)
            {
                if ($column == 'category_name') {
                    $valueArr = [];
                    foreach ($value as $arr) {
                        $valueArr[] = [$arr];
                    }
                    $formattedValue = "'".json_encode($valueArr,JSON_UNESCAPED_UNICODE) . "'";
                } else if (is_array($value)) {
                    $formattedValue = \common\library\util\PgsqlUtil::formatArray($value);
                } else {
                    // 如果不是数组，直接使用字符串值，并确保值不为 null
                    $formattedValue = ($value !== null) ? "'" . addslashes($value) . "'" : 'NULL';
                }

                if ($column == 'name') {
                    $setColumn = 'company_name';
                } else {
                    $setColumn = $column;
                }

                $companyInfo[$column] = $formattedValue;
                $setClause .= ($setClause === '' ? '' : ', ') . "{$setColumn} = {$formattedValue}";
            }

            $sqlValuesString = str_replace('\"', '"', implode(',', $companyInfo));
            $setClause = str_replace('\"', '"', $setClause);
            $sql = "INSERT INTO tbl_company_dsl_test VALUES ({$sqlValuesString}) ON CONFLICT (company_id) DO UPDATE SET {$setClause}";

            if (!$dryRun) {
                $pgDb->getPdoInstance()->prepare($sql)->execute();
            }
        }

        // 同步订单表
        if (!empty($orderId)) {
            $selectSql = "select * from tbl_order where client_id = {$clientId} and order_id = {$orderId}";
        } else {
            $selectSql = "select * from tbl_order where client_id = {$clientId}";
        }

        if ($type == 0 || $type == 2) {
            $orderInfos = $pgDb->createCommand($selectSql)->queryAll();
        } else {
            $orderInfos = [];
        }

        $archiveTypeMap = [
            \common\library\invoice\Order::ARCHIVE_TYPE_NORMAL => '手动创建',
            \common\library\invoice\Order::ARCHIVE_TYPE_AI => 'AI创建',
            \common\library\invoice\Order::ARCHIVE_TYPE_IMPORT => '智能导入',
            \common\library\invoice\Order::ARCHIVE_TYPE_OPEN_API => 'open_api同步',
            \common\library\invoice\Order::ARCHIVE_TYPE_ALIBABA => '阿里巴巴同步',
            \common\library\invoice\Order::ARCHIVE_TYPE_KINGDEE => '小满-轻易云同步金蝶云星辰订单创建',
            \common\library\invoice\Order::ARCHIVE_TYPE_CHANJET => '小满-轻易云同步畅捷通订单创建',
            \common\library\invoice\Order::ARCHIVE_TYPE_GALAXY => '小满-轻易云同步金蝶云星空订单创建',
            \common\library\invoice\Order::ARCHIVE_TYPE_PASTE_INPUT => '粘贴录入',
            \common\library\invoice\Order::ARCHIVE_TYPE_BATCH_IMPORT => '批量导入',
        ];

        $sourceTypeMap = [
            \common\library\invoice\Order::TYPE_CRM_ORDER =>  'CRM订单',
            \common\library\invoice\Order::TYPE_ALI_ORDER =>  '信保订单',
            \common\library\invoice\Order::TYPE_DIRECT_PAY_ORDER =>  'e收汇订单',
            \common\library\invoice\Order::TYPE_ERP =>  'ERP同步'
        ];

        $departmentList = \common\library\department\DepartmentCacheableRepo::instance($clientId)->findAllByConditions(['client_id' => $clientId, 'enable_flag' => 1], 'id,name');
        $departmentMap = array_column($departmentList,'name','id');

        foreach ($orderInfos as &$orderInfo)
        {
            $users = json_decode($orderInfo['users'],true);
            $userArr = [];
            foreach ($users as  $user)
            {
                if (in_array('user_id',array_keys($user))) {
                    $user['user_id'] = $userIdMap[$user['user_id']] ?? '未知';
                } else {
                    $user['department_id'] = $departmentMap[$user['department_id']] ?? '未知';
                }
                $userArr[] = $user;
            }

            $handler = \common\library\util\PgsqlUtil::trimArray($orderInfo['handler']);
            $handlerArr = [];
            foreach ($handler as $handle)
            {
                $handlerArr[] = $userIdMap[$handle] ?? '未知';
            }

            $orderInfo['ali_status_name'] = $aliStatusMap[$orderInfo['ali_status_id']] ?? '未知';
            $orderInfo['users_name'] = $userArr;
            $orderInfo['handler_name'] = \common\library\util\PgsqlUtil::formatArray($handlerArr);
            $orderInfo['create_user_name'] = $userIdMap[$orderInfo['create_user']] ?? '未知';
            $orderInfo['status_name'] = $orderStatusMap[$orderInfo['status']] ?? '未知';
            $orderInfo['currency_name'] = $orderInfo['currency'];
            $orderInfo['archive_type_name'] = $archiveTypeMap[$orderInfo['archive_type']] ?? '找不到archive_type';
            $orderInfo['source_type_name'] = $sourceTypeMap[$orderInfo['source_type']] ?? '未知';
            $orderInfo['country_name'] = $countryMap[$orderInfo['country']] ?? '未知';

            $setClause = '';
            foreach ($orderInfo as $column => $value)
            {
                if ($column == 'users_name') {
                    $valueArr = [];
                    foreach ($value as $arr) {
                        $valueArr[] = [$arr];
                    }
                    $formattedValue = "'".json_encode($valueArr,JSON_UNESCAPED_UNICODE) . "'";
                } else if (is_array($value)) {
                    $formattedValue = \common\library\util\PgsqlUtil::formatArray($value);
                } else {
                    // 如果不是数组，直接使用字符串值，并确保值不为 null
                    $formattedValue = ($value !== null) ? "'" . addslashes($value) . "'" : 'NULL';
                }

                if ($column == 'name') {
                    $setColumn = 'order_name';
                } else {
                    $setColumn = $column;
                }

                // 添加到 setClause 字符串中
                $setClause .= ($setClause === '' ? '' : ', ') . "$setColumn = $formattedValue";

                $orderInfo[$column] = $formattedValue;
            }

            $sqlValuesString = str_replace('\"', '"', implode(',', $orderInfo));
            $setClause = str_replace('\"', '"', $setClause);
            $sql = "INSERT INTO tbl_order_dsl_test VALUES ({$sqlValuesString}) ON CONFLICT (order_id) DO UPDATE SET {$setClause}";

            if (!$dryRun) {
                $pgDb->getPdoInstance()->prepare($sql)->execute();
            }
        }

        // 同步商机表
        if (!empty($opportunityId)) {
            $selectSql = "select * from tbl_opportunity where client_id = {$clientId} and opportunity_id = {$opportunityId}";
        } else {
            $selectSql = "select * from tbl_opportunity where client_id = {$clientId}";
        }

        if ($type == 0 || $type == 3) {
            $opportunityInfos = $pgDb->createCommand($selectSql)->queryAll();
        } else {
            $opportunityInfos = [];
        }

        $failTypeList = \common\library\opportunity\stage\Helper::getFailReasonListMap($clientId);
        $failTypeMap = array_column($failTypeList,'name','reason_id');

        $opportunityStageList = \common\library\opportunity\stage\Helper::getStageListMap($clientId, 0);
        $stageMap = array_column($opportunityStageList,'name','stage_id');

        $stageTypeNameMap = [
            1 => '进行中',
            2 => '赢单',
            3 => '输单'
        ];

        $tag = (new \common\library\setting\library\tag\TagApi($clientId, \Constants::TYPE_OPPORTUNITY, $userId));
        $tag->setOwnerUser([0],1);
        $clientTagList = $tag->list([]);
        $clientTagMap = array_column($clientTagList,'tag_name','tag_id');

        $flowApi = new \common\library\setting\library\flow\FlowApi($clientId);
        $flowApi->setWithDisabled(1);
        $flowList = $flowApi->flowList();
        $flowMap = array_column($flowList,'name','flow_id');

        foreach ($opportunityInfos as &$opportunityInfo)
        {
            $handler = \common\library\util\PgsqlUtil::trimArray($opportunityInfo['handler']);
            $handlerArr = [];
            foreach ($handler as $handle)
            {
                $handlerArr[] = $userIdMap[$handle] ?? '未知';
            }

            $tags = json_decode($opportunityInfo['tag'],true);
            $tagArr = [];
            foreach ($tags as  $userId => $tagList)
            {
                foreach ($tagList as $tag) {
                    $tag = $clientTagMap[$tag] ?? '未知';
                    $tagArr[$userIdMap[$userId] ?? '未知'] = array_merge($tagArr[$userId], [$tag]);
                }
            }

            $opportunityInfo['handler_name'] = \common\library\util\PgsqlUtil::formatArray($handlerArr);
            $opportunityInfo['main_user_name'] = $userIdMap[$opportunityInfo['main_user']] ?? '';
            $opportunityInfo['create_user_name'] = $userIdMap[$opportunityInfo['create_user']] ?? '';
            $opportunityInfo['currency_name'] = $opportunityInfo['stage_type'] == 3 ? $failTypeMap[$opportunityInfo['fail_type']] ?? '失败原因未填写' :  '找不到失败原因';
            $opportunityInfo['fail_type_name'] = $opportunityInfo['currency'];
            $opportunityInfo['fail_stage_name'] = $opportunityInfo['stage_type'] == 3 ? $stageMap[$opportunityInfo['fail_stage']] ?? '找不到该流程' : '不是输单';
            $opportunityInfo['origin_name'] = $originMap[$opportunityInfo['origin']] ?? '';
            $opportunityInfo['stage_type_name'] = $stageTypeNameMap[$opportunityInfo['stage_type']] ?? '';
            $opportunityInfo['stage_name'] = $stageMap[$opportunityInfo['stage']] ?? '';
            $opportunityInfo['department_name'] = $departmentMap[$opportunityInfo['department']] ?? '';
            $opportunityInfo['flow_name'] = $flowMap[$opportunityInfo['flow_id']] ?? '';
            $opportunityInfo['tag_name'] = $tagArr;

            $setClause = '';
            foreach ($opportunityInfo as $column => $value)
            {
                if ($column == 'tag_name') {
                    $valueArr = [];
                    foreach ($value as $arr) {
                        $valueArr[] = [$arr];
                    }
                    $formattedValue = "'".json_encode($valueArr,JSON_UNESCAPED_UNICODE) . "'";
                } else if (is_array($value)) {
                    $formattedValue = \common\library\util\PgsqlUtil::formatArray($value);
                } else {
                    // 如果不是数组，直接使用字符串值，并确保值不为 null
                    $formattedValue = ($value !== null) ? "'" . addslashes($value) . "'" : 'NULL';
                }

                if ($column == 'name') {
                    $setColumn = 'opportunity_name';
                } else {
                    $setColumn = $column;
                }

                // 添加到 setClause 字符串中
                $setClause .= ($setClause === '' ? '' : ', ') . "$setColumn = $formattedValue";

                $opportunityInfo[$column] = $formattedValue;
            }

            $sqlValuesString = str_replace('\"', '"', implode(',', $opportunityInfo));
            $setClause = str_replace('\"', '"', $setClause);
            $sql = "INSERT INTO tbl_opportunity_dsl_test VALUES ({$sqlValuesString}) ON CONFLICT (opportunity_id) DO UPDATE SET {$setClause}";

            if (!$dryRun) {
                $pgDb->getPdoInstance()->prepare($sql)->execute();
            }
        }
    }


    public static function groupMessagesByUserCustomerEmail(array $mailConversation): array
    {
        $mailConversations = [];
        if (!empty($mailConversation)) {
            foreach ($mailConversation as $userId => $userMailIdToCustomerInfo)
            {
                foreach ($userMailIdToCustomerInfo as $userMailId => $customerInfo)
                {
                    foreach ($customerInfo as $customerId => $info)
                    {
                        if (empty($info['conversation_id'])) continue;

                        $sendMailCount = $info['send_mail_count'] ?? 0;
                        $receiveMailCount = $info['receive_mail_count'] ?? 0;

                        if ($sendMailCount < 1 || $receiveMailCount < 1) {
                            continue;
                        }

                        $companyId = $info['company_id'] ?? 0;

                        $conversationKey = implode('|',$info['conversation_id']);
                        $conversationKey .= "|".$userId."|".Constant::CHANNEL_TYPE_MAIL;
                        $customerEmails = $info['customer_emails'] ?? '';
                        $lastMessageTime = $info['last_message_time'];
                        $userEmail = $info['user_email'] ?? '';

                        $result = [
                            'user_id' => $userId,
                            'send_mail_count' => $sendMailCount,
                            'receive_mail_count' => $receiveMailCount,
                            'company_id' => $companyId,
                            'email' => $userEmail,
                            'channel_type' => Constant::CHANNEL_TYPE_MAIL,
                            'conversation_key' => $conversationKey,
                            'customer_id' => $customerId,
                            'user_mail_id' => $userMailId,
                            'customer_emails' => $customerEmails,
                            'last_message_time' => $lastMessageTime
                        ];
                        $mailConversations[] = $result;
                    }
                }
            }
        }
        return $mailConversations;
    }

    public static function sendEmail($email, $templateData, $module, $interface, $debug = 0)
    {
        try {
            LogUtil::info($module.'_'.$interface.'START');
            $api = new InnerApi($module);
            $api->setHttpMethod(InnerApi::HTTP_METHOD_POST_JSON);
            $api->setTimeout(5);
            $response = $api->call($interface, $templateData);
            LogUtil::info($interface. " response:" . json_encode($response));
            LogUtil::info('sendEmail --START--');
            \SystemNotifyMailUtil::sendMail($templateData['emailTitle'], $response['data'], [$email]);
            LogUtil::info('sendEmail --END--');
            LogUtil::info("send email address: {$email}");
        }catch (\Exception $exception) {
            LogUtil::info("get html fail!{$exception->getMessage()}");
            LogUtil::info("templateData:" . json_encode($templateData));
            if ($debug)
                throw new \Exception($exception->getMessage());
        }
    }

    public static function filterConversationKeysByChannelType($conversationKeys,$channelType)
    {
        if (is_array($channelType)) {
            $collections = array_filter($conversationKeys, function($item) use ($channelType) {
                $parts = explode('|', $item);
                $type = end($parts);
                return in_array($type,$channelType);
            });
        } else {
            $collections = array_filter($conversationKeys, function($item) use ($channelType) {
                $parts = explode('|', $item);
                $type = end($parts);
                return $type == $channelType;
            });
        }
        return $collections;
    }
    public static function getDefaultQuestion($question, $companyId = 0, $clientId=0):array {
        $now   = date("Y-m-d H:i:s");

        // 计算三个月前的时间
        $threeMonthsAgo = date("Y-m-d 00:00:00", strtotime("-3 months", strtotime($now)));
        $filterStatusNames = ['已作废', '售后', '交易取消'];
        $invoiceStatusService = new InvoiceStatusService($clientId, \Constants::TYPE_ORDER);
        $endOrderStatus = $invoiceStatusService->filterEndOrderStatus($clientId, $filterStatusNames);
        $endOrderStatuNames = array_column($endOrderStatus, 'name');
        $map  = [
            GenerateDataAiAgent::PRESET_QUESTION_DIFFERENT_CUSTOMER => [
                'desc' => '过去3个月中不同员工创建的客户数量',
                'from' => [
                    'tables' => [['name' => 'tbl_company']],
                ],
                'fields' => [
                    [
                        'field' => 'tbl_company.create_user',
                        'alias' => 'employee'
                    ],
                    [
                        'function' => 'count',
                        'field' => 'tbl_company.company_id',
                        'alias' => 'company_count'
                    ],
                ],
                'where' => [
                    [
                        'operator' => '>=',
                        'field' => 'tbl_company.create_time',
                        'value' => $threeMonthsAgo,
                    ],
                ],
                'group_by' => [
                    'employee',
                ],
            ],
            GenerateDataAiAgent::PRESET_QUESTION_CUSTOMERS_CREATED_PER_WEEK_LAST_THREE_MONTHS  =>  [
                'desc' => '过去3个月中每周创建的客户数',
                'from' => [
                    'tables' => [['name' => 'tbl_company']],
                ],
                'fields' => [
                    [
                        'function' => 'TO_CHAR',
                        'field' => "date_trunc('week', tbl_company.create_time)",
                        'params' => ['YYYY-MM-DD'],
                        'alias' => 'week'
                    ],
                    [
                        'function' => 'count',
                        'field' => 'tbl_company.company_id',
                        'alias' => 'company_id'
                    ],
                ],
                'where' => [
                    [
                        "operator" => ">=",
                        "field" => "tbl_company.create_time",
                        "value" => $threeMonthsAgo,
                    ]
                ],
                'group_by' => [
                    'week',
                ],
                'order_by' => [
                    'week' => 'asc'
                ],
            ],
            GenerateDataAiAgent::PRESET_QUESTION_TOTAL_SALES_ORDER_AMOUNT_THIS_MONTH  => [
                'desc' => '本月销售订单总金额',
                'from' => [
                    'tables' => [['name' => 'tbl_order']],
                ],
                'fields' => [
                    [
                        'function' => 'sum',
                        'field' => 'tbl_order.amount',
                    ],
                ],
                'where' => [
                    [
                        "operator" => ">=",
                        "field" => "tbl_order.create_time",
                        "value" =>  date('Y-m-01 00:00:00'),
                    ],
                    [
                        "operator" => "<",
                        "field" => "tbl_order.create_time",
                        "value" => date('Y-m-t'),
                    ]
                ],
            ],
            GenerateDataAiAgent::PRESET_QUESTION_OPPORTUNITIES_CREATED_THIS_MONTH  => [
                'desc' => '本月创建的商机数量',
                'from' => [
                    'tables' => [['name' => 'tbl_opportunity']],
                ],
                'fields' => [
                    [
                        'function' => 'count',
                        'field' => 'tbl_opportunity.opportunity_id',
                    ],
                ],
                'where' => [
                    [
                        "operator" => ">=",
                        "field" => "tbl_opportunity.create_time",
                        "value" =>  date('Y-m-01 00:00:00'),
                    ],
                    [
                        "operator" => "<",
                        "field" => "tbl_opportunity.create_time",
                        "value" => date('Y-m-t 23:59:59'),
                    ]
                ],
            ],
        ];

        $regexMatches = [
            // select sum(amount_#主币种#） from tbl_order where company_name=#客户详情页的公司名称# and YEAR(account_date)=YEAR(CURDATE()) and status= #获取订单的所有终止状态，过滤掉「已作废」、「售后」、「 交易取消」的状态#
            // todo: 待补充： status= #获取订单的所有终止状态，过滤掉「已作废」、「售后」、「 交易取消」的状态#
            "/客户(.+?)的今年累计成交金额是多少？/" => [
                'desc' => '今年累计成交金额',
                'from' => [
                    'tables' => [['name' => 'tbl_order']],
                ],
                'fields' => [
                    [
                        'function' => 'sum',
                        'field' => 'tbl_order.amount',
                    ],
                ],
                'where' => [
                    [
                        "operator" => ">=",
                        "field" => "tbl_order.account_date",
                        "value" =>  date('Y-01-01 00:00:00'),
                    ],
                    [
                        "operator" => "<",
                        "field" => "tbl_order.account_date",
                        "value" => date('Y-12-31 23:59:59'),
                    ],
                    [
                        "operator" => "=",
                        "field" => "tbl_order.company_id",
                        "value" => $companyId,
                    ],
                    [
                        "operator" => '=',
                        'field' => 'tbl_order.status',
                        "value" => $endOrderStatuNames
                    ]
                ]
            ],
            // select sum(amount_#主币种#)/count(distinct order_id) from tbl_order where company_name=#客户详情页的公司名称# and status= #获取订单的所有终止状态，过滤掉「已作废」、「售后」、「 交易取消」的状态#
            // todo: 订单状态
            "/客户(.+?)的平均订单金额是多少？/" => [
                'desc' => '平均订单金额',
                'from' => [
                    'tables' => [['name' => 'tbl_order']],
                ],
                'fields' => [
                    [
                        'function' => 'avg',
                        'field' => 'tbl_order.amount',
                    ],
                ],
                'where' => [
                    [
                        "operator" => "=",
                        "field" => "tbl_order.company_id",
                        "value" => $companyId,
                    ],
                    [
                        "operator" => '=',
                        'field' => 'tbl_order.status',
                        "value" => $endOrderStatuNames
                    ]
                ],
            ],
            "/客户(.+?)的今年赢单金额是多少？/" => [
                'desc' => '今年赢单金额',
                'from' => [
                    'tables' => [['name' => 'tbl_opportunity']],
                ],
                'fields' => [
                    [
                        'function' => 'sum',
                        'field' => 'tbl_opportunity.amount',
                    ],
                ],
                'where' => [
                    [
                        "operator" => ">=",
                        "field" => "tbl_opportunity.account_date",
                        "value" =>  date('Y-01-01 00:00:00'),
                    ],
                    [
                        "operator" => "<",
                        "field" => "tbl_opportunity.account_date",
                        "value" => date('Y-12-31 23:59:59'),
                    ],
                    [
                        "operator" => "=",
                        "field" => "tbl_opportunity.stage_type",
                        "value" => '赢单'
                    ],
                    [
                        "operator" => "=",
                        "field" => "tbl_opportunity.company_id",
                        "value" => $companyId,
                    ]

                ],
            ],

            "/客户(.+?)的平均赢单商机金额是多少？/" => [
                'desc' => '平均赢单商机金额',
                'from' => [
                    'tables' => [['name' => 'tbl_opportunity']],
                ],
                'fields' => [
                    [
                        'function' => 'avg',
                        'field' => 'tbl_opportunity.amount',
                    ],
                ],
                'where' => [
                    [
                        "operator" => "=",
                        "field" => "tbl_opportunity.stage_type",
                        "value" => '赢单'
                    ],
                    [
                        "operator" => "=",
                        "field" => "tbl_opportunity.company_id",
                        "value" => $companyId,
                    ]

                ],
            ],
        ];

        foreach ($regexMatches as $expression => $value) {
            if (preg_match($expression, $question, $matches)) {
                return $value;
            }
        }

        return $map[$question] ?? [];
    }

    /**
     * @param int $recordId
     * @return int
     * @throws \RuntimeException
     */
    public static function getBusinessType(int $recordId): int
    {
        return (new AiServiceProcessRecord($recordId))->business_type;
    }
    public static function buildPBAiAgentChatCompletionsWebSocketRsp(array $data): \protobuf\OkkiAi\PBAiAgentChatCompletionsWebSocketRsp
    {
        $status = $data['status'] ?? AiAgentConstants::MESSAGE_STATUS_ERROR;
        $rsp = new \protobuf\OkkiAi\PBAiAgentChatCompletionsWebSocketRsp();
        $messageType = $data['message_type'] ?? \protobuf\OkkiAi\PBAiMessageType::MESSAGE_TYPE_TEXT;

        $answer = $data['context']['content'] ?? '';
        if (is_array($answer)) {
            $answer = '';
        }
        $ArrayProcessResponse = [
            'messageType' => $messageType,
            'answer' => $answer
        ];

        // 确定是否是在主流程beforeCloseSSEPipeline 里面执行的方法
        if (self::checkIsProcessingFunctionRespond($data))
        {
            $messageInfo = self::constructProcessingFunctionMessage($data);
            $rsp->setProcessData($messageInfo);
            $rsp->setStatus($data['status'] ?? 1);
            return $rsp;

        } else if ($status == AiAgentConstants::MESSAGE_STATUS_TIP) {
            // 中间态format
            $messageInfo = self::constructProcessingMessage($data);
        } else {
            $messageInfo = self::constructMessageInfoFromSingleSseObject($data, $ArrayProcessResponse);
        }

        if ($data['has_more'] ?? false) {
            $rsp->setHasMore(true);
        }
        $rsp->setMessageInfo($messageInfo);

        $rsp->setMessageType($messageType);

        $rsp->setStatus($status);
        return $rsp;
    }

    public static function formatCompanyName(string $companyName)
    {
        return "客户【{$companyName}】的";
    }

    public static function isOpenAiReportSwitch(): bool
    {
        $redis = \RedisService::getInstance('redis');
        $redisKey = 'ai-report-switch';
        $status = $redis->get($redisKey);
        if ($status == 'on') {
            return true;
        } else {
            return false;
        }
    }

    public static function checkDataMonitorPermission($clientId)
    {
        if (!Helper::hasInsightPermission($clientId)) {
            throw new RuntimeException(Yii::t('ai','没有AI数据监测相关权限'), 403);
        }
    }

    public static function getPBHistoryParamsBySceneType(\protobuf\OkkiAi\PBConversationHistoryParams $PBParams, int $sceneType): array
    {
        $params = [];
        switch ($sceneType) {
            case \AiAgent::AI_AGENT_SCENE_TYPE_MAIL_QUALITY:
            case \AiAgent::AI_AGENT_SCENE_TYPE_MAIL_SUMMARY:
            case \AiAgent::AI_AGENT_SCENE_TYPE_MAIL_REPLY:
                $mailParams = $PBParams->getMailParams();
                $mailId = $mailParams->getMailId();
                $params['mail_id'] = $mailId;
                break;
            case \AiAgent::AI_AGENT_SCENE_TYPE_CUSTOMER_FOLLOW_UP:
            case \AiAgent::AI_AGENT_SCENE_TYPE_OPPORTUNITY_FOLLOW_UP:
            case \AiAgent::AI_AGENT_SCENE_TYPE_CHAT_REPLY:
            case \AiAgent::AI_AGENT_SCENE_TYPE_CHAT_COACH:
            case \AiAgent::AI_AGENT_SCENE_TYPE_CHAT_POLISH:
                $communicationParams = $PBParams->getCommunicationParams();
                $params['channel_type'] = $communicationParams->getChannelType();
                $params['user_sns_id'] = $communicationParams->getSellerAccountId();
                $params['sns_id'] = $communicationParams->getBuyerAccountId();
                break;
            default:

        }
        return $params;
    }

    public static function getExcelFileContent(int $taskId, string $url): array
    {
        // 使用 file_get_contents 获取远程文件内容
        $fileContent = file_get_contents($url);
        if ($fileContent === false) {
            throw new Exception("无法访问远程文件: $url");
        }

        // 创建一个临时文件
        $tempFilePath = "/tmp/runAutoEval_{$taskId}";
        file_put_contents($tempFilePath, $fileContent);

        // 加载 Excel 文件
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($tempFilePath);
        // 获取第一个工作表
        $worksheet = $spreadsheet->getActiveSheet();

        $data = [];
        // 从第一行开始读取数据
        foreach ($worksheet->getRowIterator() as $row) {
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(false);

            $rowData = [];
            foreach ($cellIterator as $cell) {
                $rowData[] = $cell->getValue();
            }

            // 只取前两列的数据
            if (count($rowData) >= 2) {
                $data[] = [$rowData[0], $rowData[1]];
            }
        }

        // 删除临时文件
        unlink($tempFilePath);

        return $data;
    }

    public static function saveExcel(string $savePath, array $insert)
    {
        // 创建新的 Spreadsheet 对象
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();

        // 获取当前活动的工作表
        $sheet = $spreadsheet->getActiveSheet();

        foreach ($insert as $rowIndex => $row)
        {
            foreach ($row as $columnIndex => $item)
            {
                if (is_array($item)) {
                    LogUtil::info('saveExcelArray', [
                        'item' => $item
                    ]);
                    $insertContent = 0;
                } else {
                    $insertContent = $item;
                }

                // PHPExcel 库中的列是从 1 开始的，而行是从 1 开始的
                $sheet->setCellValueByColumnAndRow($columnIndex + 1, $rowIndex + 1, $insertContent);
            }
        }

        // 创建一个写入器来保存 Spreadsheet 对象到文件
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save($savePath);
    }

    public static function getArrayMiddle(array $operationData): float
    {
        // 对数组进行排序
        sort($operationData);

        $length = count($operationData);
        $middle = floor($length / 2);

        if ($length % 2) {
            // 如果数组元素数量为奇数，中位数就是中间的元素
            $keyMiddle = $operationData[$middle];
        } else {
            // 如果数组元素数量为偶数，中位数就是中间两个元素的平均值
            $keyMiddle = ($operationData[$middle - 1] + $operationData[$middle]) / 2;
        }

        return $keyMiddle;
    }

    public static function getExternalFieldDataBySceneType($sceneType)
    {
        $user = User::getLoginUser();
        $data = AiAgentConstants::AI_SCENE_TYPE_EXTERNAL_FIELD_DATA_MAP[$sceneType] ?? [];

        if (in_array($sceneType, [\AiAgent::AI_AGENT_SCENE_TYPE_ASSET_ANALYSIS, \AiAgent::AI_AGENT_SCENE_TYPE_TEAM_ANALYSIS])) {
           $subscribeType =  match (intval($sceneType)) {
               \AiAgent::AI_AGENT_SCENE_TYPE_ASSET_ANALYSIS => AiSubscribeList::AI_SUBSCRIBE_TYPE_ASSET_ANALYSIS,
               \AiAgent::AI_AGENT_SCENE_TYPE_TEAM_ANALYSIS => AiSubscribeList::AI_SUBSCRIBE_TYPE_TEAM_ANALYSIS
            };
            $subscribeList = new AiSubscribeList($user->getUserId());
            $subscribeList->setSubcribeType($subscribeType);
            $subscribeList->setEnableFlag(BaseObject::ENABLE_FLAG_TRUE);

            if ($subscribeList->count() != 0) {
                $data['recommended_subscribe'] = false;
            }
        }
        return $data;
    }

    public static function getConsumeModeUseFrequency($clientId = 0)
    {
        $redis = \RedisService::aiCache();

        // Client配置后，不取默认的，Client纬度最高
        if (!empty($clientId))
        {
            $moduleUseFrequency = $redis->get(\common\library\ai_agent\AiAgentConstants::CACHE_CONSUME_MODE_USE_FREQUENCY_CLIENT_PREFIX . "{$clientId}");

            if (!empty($moduleUseFrequency)){
                return json_decode($moduleUseFrequency, true);
            }
        }

        $moduleUseFrequency = $redis->get(\common\library\ai_agent\AiAgentConstants::CACHE_CONSUME_MODE_USE_FREQUENCY);

        if (empty($moduleUseFrequency)) {
            $moduleUseFrequency = \common\library\ai_agent\AiAgentConstants::CONSUME_MODE_USE_FREQUENCY;
            $redis->set(\common\library\ai_agent\AiAgentConstants::CACHE_CONSUME_MODE_USE_FREQUENCY, json_encode($moduleUseFrequency));
        } else {
            $moduleUseFrequency = json_decode($moduleUseFrequency, true);
        }

        return $moduleUseFrequency;
    }

    public static function getConsumeModeUseFrequencyByClientId($clientId)
    {
        $key = AiAgentConstants::CACHE_CONSUME_MODE_USE_FREQUENCY_CLIENT_PREFIX . "{$clientId}";

        $redis = \RedisService::aiCache();
        $moduleUseFrequency = $redis->get($key);

        if (empty($moduleUseFrequency)) {
            $moduleUseFrequency = \common\library\ai_agent\AiAgentConstants::CONSUME_MODE_USE_FREQUENCY;
        } else {
            $moduleUseFrequency = json_decode($moduleUseFrequency, true);
        }

        return $moduleUseFrequency;
    }

    public static function getDefaultTimeLogMap($timeLogEnum)
    {
        $rs = [];
        foreach ($timeLogEnum as $key => $value) {

            if (str_contains($key, '_begin')) {
                $key = explode('_begin', $key)[0];
                $rs[$key] = $value;
            }

            if (str_contains($key, '_end')) {
                $key = explode('_end', $key)[0];
                $rs[$key] = $value;
            }
        }

        return $rs;
    }

    public static function getUseFrequency($clientId, $userId, $consumeMode)
    {
        $key = AiAgentConstants::CACHE_USE_FREQUENCY_PREFIX . "{$consumeMode}:{$clientId}";
        return \RedisService::aiCache()->hget($key, $userId);
    }

    public static function getUseFrequencyAll($clientId, $consumeMode)
    {
        $key = AiAgentConstants::CACHE_USE_FREQUENCY_PREFIX . "{$consumeMode}:{$clientId}";
        return \RedisService::aiCache()->hgetall($key);
    }

    public static function saveUseFrequency($clientId, $userId, $consumeMode, $frequency)
    {
        $key = AiAgentConstants::CACHE_USE_FREQUENCY_PREFIX . "{$consumeMode}:{$clientId}";

        if ($consumeMode == 'daily_consumption') {
            $time = strtotime('tomorrow midnight') - time();
        } else {
            $time = strtotime('first day of next month midnight') - time();
        }

        $redis = \RedisService::aiCache();

        $result = $redis->hset($key, $userId, $frequency);
        $redis->expire($key, $time);

        return $result;
    }

    public static function incrUseFrequency($clientId, $userId, $consumeMode)
    {
        if ($consumeMode == 'daily_consumption') {
            $time = strtotime('tomorrow midnight') - time();
        } else {
            $time = strtotime('first day of next month midnight') - time();
        }

        $key = AiAgentConstants::CACHE_USE_FREQUENCY_PREFIX . "{$consumeMode}:{$clientId}";
        $redis = \RedisService::aiCache();

        $count = $redis->hincrby($key,$userId,1);
        $redis->expire($key, $time);

        return $count;
    }

    public static function saveClientUseFrequency($clientId, $UseFrequency)
    {
        $key = AiAgentConstants::CACHE_CONSUME_MODE_USE_FREQUENCY_CLIENT_PREFIX . "{$clientId}";
        return \RedisService::aiCache()->set($key, json_encode($UseFrequency));
    }

    /**
     * 系统报表结构转换成报表生成接口结构
     * @param $reportData
     * @return PBCommonMessageInfo $commonMessageInfo
     */
    public static function buildGenerateData($reportData)
    {
        $commonMessageInfo  = new PBCommonMessageInfo();
        $commonMessageInfo->setTitle($reportData['title']['text'] ?? '');
        $generationDataAxisInfoList = [];
        foreach ($reportData['config']['XAxis'] ?? [] as $value)
        {
            $generationDataAxisInfo = new PBGenerationDataAxisInfo();
            $generationDataAxisInfo->setAxisType(PBAiAxisType::TYPE_X);
            $generationDataAxisInfo->setFieldId($value['field']);
            $generationDataAxisInfo->setFieldName($value['name']);
            $generationDataAxisInfoList[] = $generationDataAxisInfo;
        }

        foreach ($reportData['config']['YAxis'] ?? [] as $value)
        {
            $generationDataAxisInfo = new PBGenerationDataAxisInfo();
            $generationDataAxisInfo->setAxisType(PBAiAxisType::TYPE_Y);
            $generationDataAxisInfo->setFieldId($value['field']);
            $generationDataAxisInfo->setFieldName($value['name']);
            $generationDataAxisInfoList[] = $generationDataAxisInfo;
        }
        $generationDataAxisConfig = new PBGenerationDataAxisConfig();
        $messageConfig = new PBMessageConfig();
        $generationDataAxisConfig->setAxis($generationDataAxisInfoList);
        $messageConfig->setAxisConfig($generationDataAxisConfig);
        $commonMessageInfo->setConfig($messageConfig);


        $charTypeItemList = [];
        foreach (AiAgentConstants::CHAR_TYPES as $value)
        {
            $charTypeItem = new PBMessageComponentSelectParamsItem();
            $charTypeItem->setValue($value['value']);
            $charTypeItem->setLabel($value['label']);
            $charTypeItemList[] = $charTypeItem;
        }

        $selectChartType = $reportData['config']['chartType'] ?? 'table';
        $chartType = ($selectChartType == 'vertical-bar') ? 'bar' : $selectChartType;
        $charTypeList = new PBMessageComponentSelectParams();
        $charTypeList->setSelectType($chartType);
        $charTypeList->setSelectItem($charTypeItemList);
        $reportCharType = new PBMessageComponentParams();
        $reportCharType->setSelectParams($charTypeList);

        $reportComponent = new PBMessageComponent();
        $reportComponent->setParams($reportCharType);
        $reportComponent->setOption('select');
        $reportComponent->setComponentType(2);
        $commonMessageInfo->setComponentList([$reportComponent]);

        // 存在是空的情况
        $fieldTypeMap = $reportData['config']['fieldType'] ?? [];
        $generateDataItemList = [];
        foreach ($reportData['content'] ?? [] as $item)
        {
            $item = self::addPercentLabel($item, $fieldTypeMap);
            $item = array_map('strval', $item);
            $generateDataItem = new PBGenerateDataItem();
            $generateDataItem->setDataItem($item);
            $generateDataItemList[] = $generateDataItem;
        }

        $generateDataList = new PBGenerateDataList();
        $messageData = new PBMessageData();
        $generateDataList->setGenerateDataItem($generateDataItemList);
        $messageData->setGenerateDataList($generateDataList);
        $commonMessageInfo->setData($messageData);

        return $commonMessageInfo;
    }

    public static function addPercentLabel($items, $format)
    {
        // 需要显示百分号的列
        $percentMap = [
            'percent',
            'rowPercent',
            'sumPercent',
            'yearOnYear',
            'yearOnYearIncrease',
            'monthOnMonth',
            'monthOnMonthIncrease',
        ];

        foreach ($items as $key => &$value) {

            if ( empty($value) || str_contains($value, '%')) continue;

            if (empty($format)) {
                $needPercent = array_filter($percentMap, function ($k) use ($key) {
                    return str_contains($key, $k);
                });

                $needPercent && $value = $value . '%';
            } else {
                !empty($format[$key]) && in_array($format[$key], $percentMap) && $value = $value . '%';
            }
        }
        return $items;
    }

    public static function buildDefaultAnalysisList($defaultData)
    {
        $list = new PBAnalysisList();
        foreach ($defaultData as $analysis)
        {
            $commonAnalysisInfo1 = new PBCommonAnalysisInfo();
            $commonAnalysisInfo1->setSuggestion($analysis['suggestion']);
            $commonAnalysisInfo1->setConclusion($analysis['conclusion']);
            $commonAnalysisInfo1->setTitle($analysis['title']);

            $subAnalysisList = [];
            foreach ($analysis['list'] as $subAnalysis)
            {
                $commonAnalysisInfo2 = new PBCommonAnalysisInfo();
                $commonAnalysisInfo2->setTitle($subAnalysis['title']);

                $thirdAnalysisList = [];
                foreach ($subAnalysis['list'] as $thirdAnalysis)
                {
                    $commonAnalysisInfo3 = new PBCommonAnalysisInfo();
                    $commonAnalysisInfo3->setRichness(1);
                    $commonAnalysisInfo3->setTitle($thirdAnalysis['title']);
                    $commonAnalysisInfo3->setConclusion($thirdAnalysis['conclusion']);


                    $data = !empty($thirdAnalysis['report_detail_data']) ? Helper::formatReportDataToChartStruct($thirdAnalysis['report_detail_data']) : $thirdAnalysis['data'];
                    $reportData = self::buildGenerateData($data);
                    $thirdAnalysisData = new PBThirdAnalysisData();
                    $thirdAnalysisData->setCommonAnalysisInfo($commonAnalysisInfo3);
                    $thirdAnalysisData->setData($reportData);

                    $reportInfo = new PBReportInfo();
                    $reportInfo->setName($thirdAnalysis['title']);
                    $reportInfo->setReportKey($thirdAnalysis['report_detail_data']['key']);
                    $reportInfo->setParams('');
                    $thirdAnalysisData->setReport($reportInfo);
                    $thirdAnalysisList[] = $thirdAnalysisData;
                }

                $subAnalysisData = new PBSubAnalysisData();
                $subAnalysisData->setCommonAnalysisInfo($commonAnalysisInfo2);
                $subAnalysisData->setList($thirdAnalysisList);
                $subAnalysisList[] = $subAnalysisData;
            }

            $analysisData = new PBAnalysisData();
            $analysisData->setCommonAnalysisInfo($commonAnalysisInfo1);
            $analysisData->setList($subAnalysisList);
            $analysisList[] = $analysisData;
        }
        $list->setAnalysisData($analysisList);
        return $list;
    }

    public static function buildPBEchoParams($echo_params)
    {
        $echoParamItemList = [];
        foreach ($echo_params as $item)
        {
            $echoParamItem = new PBEchoParamItem();
            $echoParamItem->setParamItem($item);
            $echoParamItemList[] = $echoParamItem;
        }
        return $echoParamItemList;
    }

    /**
     * sql 准确率评测
     */
    public static function sqlAccuracyMeasurement($sqlSystemPrompt, $sqlUserPrompt, $sql, $result)
    {
        $systemPrompt = <<<SYSTEM_PROMPT
You are an experienced SQL engineer tasked with evaluating the correctness of a generated SQL query. Your goal is to determine if the generated SQL query is semantically equivalent to the provided correct SQL example, which reflects the accurate business logic and requirements. Follow these steps:

1. Review the user question to understand the expected output and any specific constraints. The question will also contain the necessary SQL requirements and schema information.
2. Examine the schema information within the question to grasp the structure of the tables and their relationships.
3. Carefully analyze the correct SQL example provided by the business expert. This example reflects the correct business logic and should be considered as the golden standard.
4. Compare the generated SQL with the correct SQL example to identify any semantic discrepancies or errors. Focus on the equivalence of the SQL queries in terms of their meaning and execution results.
5. If your understanding of the requirements differs from the provided correct SQL, defer to the correct SQL as it represents the accurate business logic.
6. Evaluate the semantic correctness of the generated SQL considering the requirements, question, schema, and the provided correct SQL example.
7. Be lenient in your evaluation. Differences such as column names, non-required sorting order resulting in different result order, alternative syntax, or other minor discrepancies that do not affect the core meaning and results should be considered acceptable and semantically equivalent.
8. Assign a SQL score between 0 and 5, where 5 represents a perfectly correct SQL query and 0 represents a completely incorrect one. Use your judgment to assign an appropriate score based on the semantic equivalence and correctness of the generated SQL.

Focus on the semantic equivalence and accuracy of the generated SQL in fulfilling the given requirements and matching the provided correct SQL example. The correct SQL example provided by the business expert is the definitive reference for the desired business logic.

Please provide your evaluation in json in the following format:
{
"isCorrect": "0 or 1, cannot be empty",
"sqlScore": "0-5",
"explanation": "Concise explanation of your evaluation, confirming the semantic equivalence or highlighting any significant errors"

}
Output only the JSON object, without any additional text or formatting.
SYSTEM_PROMPT;


        $userPrompt = <<<USER_PROMPT
[Start of User Question]
{$sqlSystemPrompt}
{$sqlUserPrompt}
[End of User Question]

[Start of Correct SQL Example]
sql is:
SELECT COUNT(*) AS 新建公海客户数 FROM tbl_company WHERE user_name = '{}' AND create_time >= date_trunc('month', current_date) AND create_time < date_trunc('month', current_date) + interval '1 month' LIMIT 1000;
executed result:
[{'新建公海客户数': 2}]
[End of Correct SQL Example]

[Start of Generated SQL]
sql is:
{$sql}
executed result:
{$result}
[End of Generated SQL]
USER_PROMPT;

        $aiClient = new AIClient();
        $aiClient->setModel(AIClient::AZURE_OPENAI_GPT_FOUR_O);
        $aiClient->setSystemPrompt($systemPrompt);
        $aiClient->setQuestion($userPrompt);
        $aiClient->setTrace("OKKI_AI", 'sqlAccuracyMeasurement');
        $originResponse = $aiClient->chatCompletions();
        $response = $aiClient->convertResponse($originResponse['data']);

        $data = json_decode($response['answer'], true);
        if (empty($data)) {
            $data = [];
        }

        return [$aiClient->buildParams(), $originResponse, $response, $data['isCorrect'] ?? 0];
    }

    //获取用户的okki ai谈单检测相关权限列表
    public static function getQualityCheckPrivileges(int $clientId , int $scope , array $currentPrivileges) : array
    {
        $privilegeService = PrivilegeService::getInstance($clientId);
        //depends OKKI_AI
        if(!$privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_OKKI_AI)){
            return [];
        }
        $qcPrivilegeIds = [PrivilegeConstants::PRIVILEGE_CRM_OKKI_AI_QC_INSERT, PrivilegeConstants::PRIVILEGE_CRM_OKKI_AI_QC_REMOVE];
        $qcPrivileges = array_filter($currentPrivileges , function($item) use ($qcPrivilegeIds){
           return in_array($item , $qcPrivilegeIds);
        });
        if(!empty($qcPrivileges)){
            //提交过db,以db为准
            return $qcPrivileges;
        }
        //角色可见范围大于本人
        return  $scope > PrivilegeConstants::PRIVILEGE_SCOPE_OWNER ? $qcPrivilegeIds : [];
    }

    /**
     * ai客群版本权限
     * smart_ai，pro_ai，tw_smart_ai(待定)
     * @param $clientId
     * @return bool
     */
    public static function checkAiSwarmPrivileges($clientId): bool
    {
        $privilegeService = PrivilegeService::getInstance($clientId);

        if (!$privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_OKKI_AI)) {
           return false;
        }

        $systemId = $privilegeService->getMainSystemId();
        if (!in_array($systemId, [PrivilegeConstants::CRM_SMART_SYSTEM_ID, PrivilegeConstants::CRM_PRO_SYSTEM_ID, PrivilegeConstants::OKKI_PRO_SYSTEM_ID, PrivilegeConstants::TW_SMART_AI_ID])) {
            return false;
        }

        return true;
    }


    /**
     * 重试
     * @param $callback
     * @param int $maxRetryTimes
     * @param $retryExceptions
     * @param int $sleepTimeInUs 休眠时间，单位微秒
     * @return mixed
     * @throws \Throwable
     */
    public static function retry($callback, int $maxRetryTimes = 3, $retryExceptions = [], $sleepTimeInUs = 0)
    {
        $retryTimes = 1;
        while (true) {
            try {
                return $callback();
            } catch (\Throwable $e) {
                if ($retryTimes >= $maxRetryTimes) {
                    throw $e;
                }
                if (!empty($retryExceptions)) {
                    $isRetry = false;
                    foreach ($retryExceptions as $retryException) {
                        if ($e instanceof $retryException) {
                            $isRetry = true;
                            break;
                        }
                    }
                    if (!$isRetry) {
                        throw $e;
                    }
                }
                if ($sleepTimeInUs > 0) {
                    usleep($sleepTimeInUs);
                }
                $retryTimes++;
            }
        }
    }

    public static function getLanguageList (){
        return [
            ['code' => 'en', 'zh' => '英语', 'en' => 'English'],
            ['code' => 'fr', 'zh' => '法语', 'en' => 'French'],
            ['code' => 'es', 'zh' => '西班牙语', 'en' => 'Spanish'],
            ['code' => 'ru', 'zh' => '俄语', 'en' => 'Russian'],
            ['code' => 'zh', 'zh' => '简体中文', 'en' => 'Simplified Chinese'],
            ['code' => 'ar', 'zh' => '阿拉伯语', 'en' => 'Arabic'],
            ['code' => 'de', 'zh' => '德语', 'en' => 'German'],
            ['code' => 'ko', 'zh' => '韩语', 'en' => 'Korean'],
            ['code' => 'ms', 'zh' => '马来语', 'en' => 'Malay'],
            ['code' => 'pt', 'zh' => '葡萄牙语', 'en' => 'Portuguese'],
            ['code' => 'ja', 'zh' => '日语', 'en' => 'Japanese'],
            ['code' => 'th', 'zh' => '泰语', 'en' => 'Thai'],
            ['code' => 'tr', 'zh' => '土耳其语', 'en' => 'Turkish'],
            ['code' => 'it', 'zh' => '意大利语', 'en' => 'Italian'],
            ['code' => 'id', 'zh' => '印尼语', 'en' => 'Indonesian'],
            ['code' => 'vi', 'zh' => '越南语', 'en' => 'Vietnamese'],
            ['code' => 'he', 'zh' => '希伯来语', 'en' => 'Hebrew'],
        ];
    }

    public static function getLanguageMap() {
        $languageList = self::getLanguageList();
        return array_column($languageList, 'en', 'code');
    }

    public static function saveLanguageSetting($params,$language,$clientId,$agentId,$userId): void
    {
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $snsId = ArrayUtil::fallbackByKeys($params['params'], 'sns_id', 'buyer_account_id') ?? '';
        $userSnsId = ArrayUtil::fallbackByKeys($params['params'], 'user_sns_id', 'store_id', 'seller_account_id', 'channel_open_id') ?? '';
        if(empty($snsId) || empty($userSnsId) || empty($agentId) || empty($userId) || empty($clientId)){
            return;
        }

        $sql = "SELECT history_id,ext_info FROM tbl_ai_agent_conversation_history WHERE client_id = {$clientId} AND agent_id={$agentId} AND user_id={$userId}
AND ext_info->>'sns_id'::text = '$snsId'
AND ext_info->>'user_sns_id'::text = '$userSnsId'
order by conversation_id desc,history_id desc limit 1";
        $info = $db->createCommand($sql)->queryRow();
        if(!$info || !isset($info['history_id'])){
            return;
        }
        $historyId = $info['history_id'];
        $info = json_decode($info['ext_info'], true);
        $info['language'] = $language;

        $sql = "UPDATE tbl_ai_agent_conversation_history SET ext_info = '".json_encode($info)."'  WHERE client_id = {$clientId}
          AND history_id = {$historyId}";
        $db->createCommand($sql)->execute();
    }

    public static function getLanguageSetting($params,$clientId,$agentId,$userId)
    {

        $defaultValue = ['ext_info' => json_encode(['language' => 'English'])];
        $snsId = ArrayUtil::fallbackByKeys($params['params'], 'sns_id', 'buyer_account_id') ?? '';
        $userSnsId = ArrayUtil::fallbackByKeys($params['params'], 'user_sns_id', 'store_id', 'seller_account_id', 'channel_open_id') ?? '';
        if(empty($snsId) || empty($userSnsId) || empty($agentId) || empty($userId) || empty($clientId)){
            return $defaultValue;
        }

        $sql = "SELECT history_id,ext_info FROM tbl_ai_agent_conversation_history WHERE client_id = {$clientId} AND agent_id={$agentId} AND user_id={$userId}
AND ext_info->>'sns_id'::text = '$snsId'
AND ext_info->>'user_sns_id'::text = '$userSnsId' AND ext_info->>'language' IS NOT NULL
order by conversation_id desc,history_id desc limit 1
";

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $result =  $db->createCommand($sql)->queryRow();
        if(!$result){
            return $defaultValue;
        }
        else{
            return $result;
        }
    }

    public static function checkCreateSpeechcraftFlag($clientId, $userId)
    {
        try {
            $check = \common\library\privilege_v3\Helper::checkPermission($clientId, $userId, \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_CRM_SPEECHCRAFT_CREATE);
        } catch (\Exception $exception) {
            $check = false;
        }

        return $check;
    }

    public static function constructProcessingMessage($processingData)
    {
        $PBMessageInfo = new \protobuf\OkkiAi\PBCommonMessageInfo();
        $context = $processingData['context'] ?? [];

        $componentMessageArr = [];
        $headLeft = $context['header_left'] ?? [];
        $headRight = $context['header_right'] ?? [];
        $footerLeft = $context['footer_left'] ?? [];
        $footerRight = $context['footer_right'] ?? [];
        $outsideHeaderLeft = $context['outside_header_left'] ?? [];
        $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headLeft,1));
        $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($headRight,2));
        $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerLeft,3));
        $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($footerRight,4));
        $componentMessageArr = array_merge($componentMessageArr,self::buildPBMessageComponent($outsideHeaderLeft,5));
        $PBMessageInfo->setComponentList($componentMessageArr);


        $PBMessageInfo->setContent($context['content'] ?? '');
        $PBMessageInfo->setTitle(\Yii::t('ai',$context['title'] ?? ''));
        return $PBMessageInfo;


    }

    public static function convertCardTypeForAiAgentConversationHistory(array $conversationHistoryData, int $sceneType, bool $showForApp = false): array
    {
        // web端和app端的消息小卡片互相转换（只针对辅助回复、沟通建议、沟通润色、客户跟进、商机跟进）
        if (in_array($sceneType, [
                AiAgent::AI_AGENT_SCENE_TYPE_CHAT_REPLY,
                AiAgent::AI_AGENT_SCENE_TYPE_CHAT_COACH,
                AiAgent::AI_AGENT_SCENE_TYPE_CHAT_POLISH,
                AiAgent::AI_AGENT_SCENE_TYPE_CUSTOMER_FOLLOW_UP,
                AiAgent::AI_AGENT_SCENE_TYPE_OPPORTUNITY_FOLLOW_UP,
            ])) {
            if ($showForApp) {
                switch ($sceneType) {
                    case AiAgent::AI_AGENT_SCENE_TYPE_CHAT_REPLY:
                        if ($conversationHistoryData['message_type'] == AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TRANSLATE_CARD) {
                            if (empty(trim($conversationHistoryData['context']['strategy']) ?? '')) {
                                // 追问模式下没有【回复策略】
                                $conversationHistoryData['message_type'] = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_REPLY_OTHER_STRATEGY_REPLY_CARD;
                            } else {
                                $conversationHistoryData['message_type'] = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_REPLY_CARD;
                            }
                        } else if ($conversationHistoryData['message_type'] == AiAgentConstants::AI_AGENT_MESSAGE_TYPE_OPTIONS_CARD) {
                            $conversationHistoryData['message_type'] = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_REPLY_OTHER_STRATEGY_LIST_CARD;
                        } else if ($conversationHistoryData['message_type'] == AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CARD) {
                            $conversationHistoryData['message_type'] = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_EXPIRED_ERROR_CARD;
                        }
                        break;
                    case AiAgent::AI_AGENT_SCENE_TYPE_CHAT_COACH:
                        if ($conversationHistoryData['message_type'] == AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CARD) {
                            $conversationHistoryData['message_type'] = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_COACH_CARD;
                        }
                        break;
                    case AiAgent::AI_AGENT_SCENE_TYPE_CHAT_POLISH:
                        if ($conversationHistoryData['message_type'] == AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TRANSLATE_CARD) {
                            $conversationHistoryData['message_type'] = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_POLISH_CARD;
                        }
                        break;
                    case AiAgent::AI_AGENT_SCENE_TYPE_CUSTOMER_FOLLOW_UP:
                        if ($conversationHistoryData['message_type'] == AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CARD) {
                            if (trim($conversationHistoryData['context']['footer_left'][0]['text'] ?? '') == \Yii::t('ai', '建议使用AI')) {
                                $conversationHistoryData['message_type'] = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_FOLLOW_UP_ERROR_CARD;
                            } else {
                                $conversationHistoryData['message_type'] = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CUSTOMER_FOLLOW_UP_CARD;
                            }
                        }
                        break;
                    case AiAgent::AI_AGENT_SCENE_TYPE_OPPORTUNITY_FOLLOW_UP:
                        if ($conversationHistoryData['message_type'] == AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CARD) {
                            if (trim($conversationHistoryData['context']['footer_left'][0]['text'] ?? '') == \Yii::t('ai', '建议使用AI')) {
                                $conversationHistoryData['message_type'] = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_FOLLOW_UP_ERROR_CARD;
                            } else if (trim($conversationHistoryData['context']['footer_left'][0]['text'] ?? '') == \Yii::t('ai', '是否继续生成商机跟进')) {
                                $conversationHistoryData['message_type'] = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_NOT_IN_PROGRESS_CARD;
                            } else {
                                $conversationHistoryData['message_type'] = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_OPPORTUNITY_FOLLOW_UP_CARD;
                            }
                        }
                        break;
                }
            } else {
                switch ($sceneType) {
                    case AiAgent::AI_AGENT_SCENE_TYPE_CHAT_REPLY:
                        if ($conversationHistoryData['message_type'] == AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_REPLY_CARD
                            || $conversationHistoryData['message_type'] == AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_REPLY_OTHER_STRATEGY_REPLY_CARD) {
                            $conversationHistoryData['message_type'] = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TRANSLATE_CARD;
                        } else if ($conversationHistoryData['message_type'] == AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_REPLY_OTHER_STRATEGY_LIST_CARD) {
                            $conversationHistoryData['message_type'] = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_OPTIONS_CARD;
                        } else if ($conversationHistoryData['message_type'] == AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_EXPIRED_ERROR_CARD) {
                            $conversationHistoryData['message_type'] = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CARD;
                        }
                        break;
                    case AiAgent::AI_AGENT_SCENE_TYPE_CHAT_COACH:
                        if ($conversationHistoryData['message_type'] == AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_COACH_CARD) {
                            $conversationHistoryData['message_type'] = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CARD;
                        }
                        break;
                    case AiAgent::AI_AGENT_SCENE_TYPE_CHAT_POLISH:
                        if ($conversationHistoryData['message_type'] == AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_POLISH_CARD) {
                            $conversationHistoryData['message_type'] = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TRANSLATE_CARD;
                        }
                        break;
                    case AiAgent::AI_AGENT_SCENE_TYPE_CUSTOMER_FOLLOW_UP:
                        if ($conversationHistoryData['message_type'] == AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CUSTOMER_FOLLOW_UP_CARD
                            || $conversationHistoryData['message_type'] == AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_FOLLOW_UP_ERROR_CARD) {
                            $conversationHistoryData['message_type'] = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CARD;
                        }
                        break;
                    case AiAgent::AI_AGENT_SCENE_TYPE_OPPORTUNITY_FOLLOW_UP:
                        if ($conversationHistoryData['message_type'] == AiAgentConstants::AI_AGENT_MESSAGE_TYPE_OPPORTUNITY_FOLLOW_UP_CARD
                            || $conversationHistoryData['message_type'] == AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_FOLLOW_UP_ERROR_CARD
                            || $conversationHistoryData['message_type'] == AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CHAT_NOT_IN_PROGRESS_CARD) {
                            $conversationHistoryData['message_type'] = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_CARD;
                        }
                        break;
                }
            }
        }
        return $conversationHistoryData;
    }
    /**
     * 1. 获取前一个月三个最近沟通日的聊天记录
     * 2. 获取今天上一次最后一个渠道的聊天记录取十条
     * 3. 获取今天的聊天日志进行聚合
     *
     * 注意：这里有个逻辑，每天都会拿最后一个 journey 进行行动建议的分析。
     *      所以行动建议的过程数据，也就保存在这个journey里面
     */
    public static function generateRecords($clientId, $journey)
    {
        // 获取和customer 最近的一个月的三个沟通日的数据
        $date = new \DateTime($journey['contact_time']);
        $date->modify('-1 month');
        $monthTime = $date->format('Y-m-d 00:00:00');

        $sql = <<<SQL
SELECT DISTINCT DATE_TRUNC('day', contact_time) AS communication_date
FROM tbl_ai_quality_check_chat_journey
WHERE contact_time >= '{$monthTime}'
  AND contact_time <= '{$journey['contact_time']}'
  AND company_id = {$journey['company_id']}
  AND customer_id = {$journey['customer_id']}
ORDER BY communication_date DESC
LIMIT 3
SQL;
        $communicationDates = \PgActiveRecord::getDbByClientId($clientId)->createCommand($sql)->queryAll();
        $communicationDates = array_column($communicationDates, 'communication_date');

        // 注意：array_column 提取某一列的值，返回的新数组中的元素顺序将与输入数组中这些元素的顺序相同，所以可以直接取第一位作为今天
        $todayDate = date('Y-m-d', strtotime(reset($communicationDates)));
        $lastDate = isset($communicationDates[1]) ? date('Y-m-d', strtotime($communicationDates[1])) : '';
        $endDate = date('Y-m-d', strtotime(end($communicationDates)));

        $recentCommunication = $lastMessages = $chatRecords = $lastJourney = [];
        $endContactTime = "{$todayDate} 23:59:59";
        $startContactTime = "{$endDate} 00:00:00";

        $journeyPdo = new AiQualityCheckChatJourneyList($clientId);
        $journeyPdo->setCompanyId($journey['company_id']);
        $journeyPdo->setCustomerId($journey['customer_id']);
        $journeyPdo->setStartContactTime($startContactTime);
        $journeyPdo->setEndContactTime($endContactTime);
        $journeyPdo->setOrderBy('contact_time');
        $journeyPdo->setOrder('desc');
        $journeyList = $journeyPdo->find();

        foreach ($journeyList as $itemJourney)
        {
            // 今天日期数据获取
            if ($todayDate == (date('Y-m-d', strtotime($itemJourney['contact_time']))))
            {
                $chatRecords[$itemJourney['contact_begin_time']] = [
                    'channel' => $itemJourney['sns_type'],
                    'chat_records' => $itemJourney['context']
                ];
            }

            // 隔天数据获取，获取最近一条的十条，因为上面是倒序的，所以取最近一个渠道即可，即 lastJourney 不为空
            if (empty($lastJourney) && $lastDate == (date('Y-m-d', strtotime($itemJourney['contact_time']))))
            {
                $context = $itemJourney['context'];

                // 使用正则表达式匹配每条消息，匹配以Customer:或Salesperson:开头的消息
                preg_match_all('/(?:Customer|Salesperson): .*?(?=(?:\nCustomer:|\nSalesperson:|$))/s', $context, $matches);
                $messages = $matches[0];

                // 否则返回最后10条消息
                $lastTenChats = array_slice($messages, -10);
                $lastTenChats = implode("\n", $lastTenChats);

                $lastMessages[$itemJourney['contact_begin_time']] = [
                    'channel' => $itemJourney['sns_type'],
                    'chat_records' => $lastTenChats
                ];

                $lastJourney = $itemJourney;
            }

            // 上个三个渠道日聊天信息
            $recentCommunication[$itemJourney['contact_begin_time']] = [
                'channel' => $itemJourney['sns_type'],
                'chat_records' => $itemJourney['context']
            ];
        }

        // 获取昨天是否有意图分析
        $followSuggestion = $lastJourney['follow_suggestion'] ?? '';

        return [json_encode($recentCommunication, JSON_UNESCAPED_UNICODE), json_encode($lastMessages, JSON_UNESCAPED_UNICODE), json_encode($chatRecords, JSON_UNESCAPED_UNICODE), $followSuggestion];
    }

    /**
     * 1. 获取前一个月三个最近沟通日的聊天记录
     * 2. 获取今天上一次最后一个渠道的聊天记录取十条
     * 3. 获取今天的聊天日志进行聚合
     *
     * 注意：这里有个逻辑，每天都会拿最后一个 journey 进行行动建议的分析。
     *      所以行动建议的过程数据，也就保存在这个journey里面
     */
    public static function generateNewRecords($clientId, $journey)
    {
        // 获取和customer 最近的一个月的三个沟通日的数据
        $date = new \DateTime($journey['contact_date']);
        $date->modify('-1 month');
        $startTime = $date->format('Y-m-d 00:00:00');
        $endTime = "{$journey['contact_date']} 23:59:59";

        $sql = <<<SQL
SELECT DISTINCT contact_date AS communication_date
FROM tbl_ai_quality_check_company_conversation
WHERE day_last_message_time >= '{$startTime}'
  AND day_last_message_time <= '{$endTime}'
  AND company_id = {$journey['company_id']}
  AND customer_id = {$journey['customer_id']}
  AND user_id = {$journey['user_id']}
ORDER BY communication_date DESC
LIMIT 3
SQL;
        $communicationDates = \PgActiveRecord::getDbByClientId($clientId)->createCommand($sql)->queryAll();
        $communicationDates = array_column($communicationDates, 'communication_date');

        // 注意：array_column 提取某一列的值，返回的新数组中的元素顺序将与输入数组中这些元素的顺序相同，所以可以直接取第一位作为今天
        $todayDate = date('Y-m-d', strtotime(reset($communicationDates)));
        $lastDate = isset($communicationDates[1]) ? date('Y-m-d', strtotime($communicationDates[1])) : '';
        $endDate = date('Y-m-d', strtotime(end($communicationDates)));

        $recentCommunication = $lastMessages = $chatRecords = $firstContactTimes = [];
        $endContactTime = "{$todayDate} 23:59:59";
        $startContactTime = "{$endDate} 00:00:00";

        $conversationListPdo = new AiQualityCheckCompanyConversationList($clientId);
        $conversationListPdo->setCompanyId($journey['company_id']);
        $conversationListPdo->setCustomerId($journey['customer_id']);
        $conversationListPdo->setStartDayLastMessageTime($startContactTime);
        $conversationListPdo->setEndDayLastMessageTime($endContactTime);
        $conversationListPdo->setUserId($journey['user_id']);
        $conversationListPdo->setOrderBy('day_last_message_time');
        $conversationListPdo->setOrder('desc');
        $conversationList = $conversationListPdo->find();

        $lastJourneyId = 0;
        foreach ($conversationList as $itemConversation)
        {
            // 今天日期数据获取
            if ($todayDate == (date('Y-m-d', strtotime($itemConversation['day_last_message_time']))))
            {
                $chatRecords[$itemConversation['day_first_message_time']] = [
                    'channel' => $itemConversation['sns_type'],
                    'chat_records' => $itemConversation['content']
                ];
            }

            // 隔天数据获取，获取最近一条的十条
            if ($lastDate == (date('Y-m-d', strtotime($itemConversation['day_last_message_time']))))
            {
                $context = $itemConversation['content'];

                // 使用正则表达式匹配每条消息，匹配以Customer:或Salesperson:开头的消息
                preg_match_all('/(?:Customer|Salesperson): .*?(?=(?:\nCustomer:|\nSalesperson:|$))/s', $context, $matches);
                $messages = $matches[0];

                // 否则返回最后10条消息
                $lastTenChats = array_slice($messages, -10);
                $lastTenChats = implode("\n", $lastTenChats);

                $lastMessages[$itemConversation['day_first_message_time']] = [
                    'channel' => $itemConversation['sns_type'],
                    'chat_records' => $lastTenChats
                ];

                $lastJourneyId = $itemConversation['journey_id'];
            }

            if (!isset($firstContactTimes[$itemConversation['journey_id']])) {
                $firstContactTimes[$itemConversation['journey_id']] = '1970:01:01 00:00:01';
            }

            if (strtotime($itemConversation['day_first_message_time']) >= strtotime($firstContactTimes[$itemConversation['journey_id']])) {
                $firstContactTimes[$itemConversation['journey_id']] = $itemConversation['day_first_message_time'];
            }
        }

        // 获取上一次是否存在行动建议
        $followSuggestion = [];

        if (!empty($journey["journey_id"]))
        {
            $agentAnalysisListPdo = new AiQualityCheckAgentAnalysisList($clientId);
            $agentAnalysisListPdo->setJourneyId($journey["journey_id"]);
            $agentAnalysisListPdo->setContactDate($journey['contact_date']);
            $agentAnalysisListPdo->setAgentId(CompanyQualityCheckAiAgentV2::QC_SUGGESTION_AGENT_ID);
            $agentAnalysisList = $agentAnalysisListPdo->find();
            $agentAnalysis = $agentAnalysisList[0] ?? [];

            $analysisResult = $agentAnalysis['analyze_result'] ?? '';
            $analysisResult = is_array($analysisResult) ? $analysisResult : json_decode($analysisResult, true);
            $suggestion = !empty($analysisResult['rebuild_follow_suggestion']) ? $analysisResult['rebuild_follow_suggestion']['沟通策略'] ?? [] : $analysisResult['normal_follow_suggestion']['沟通策略'] ?? [];
            $followSuggestion = [
                'follow_up_suggestion' => $suggestion,
            ];
        }

        // 上个三个渠道日总结信息
        $journeyIds = array_column($conversationList, 'journey_id');

        if (!empty($journeyIds))
        {
            $companyJourneyListPdo = new AiQualityCheckCompanyJourneyList($clientId, $journey['company_id']);
            $companyJourneyListPdo->setJourneyIds($journeyIds);
            $companyJourneyList = $companyJourneyListPdo->find();

            $agentResultListPdo = new AiQualityCheckAgentAnalysisList($clientId);
            $agentResultListPdo->setAgentId(CompanyQualityCheckAiAgentV2::QC_JOURNEY_GENERATION_AGENT_ID);
            $agentResultListPdo->setJourneyIds($journeyIds);
            $agentResultListPdo->setUserId($journey['user_id']);
            $agentResultListPdo->setCustomerId($journey['customer_id']);
            $agentResultListPdo->setCompanyId($journey['company_id']);
            $agentResultListPdo->setContactDate($journey['contact_date']);
            $agentResultList = $agentResultListPdo->find();

            $agentResult = $agentResultList[0] ?? [];
            $analyzeResult = $agentResult['analyze_result'] ?? '';
            $analyzeResult = json_decode($analyzeResult, true);

            foreach ($companyJourneyList as $itemJourney)
            {
                $keywords = [];
                $keywordList = $itemJourney['keywords'] ? PgsqlUtil::trimArray($itemJourney['keywords']) : [];
                foreach ($keywordList as $item) {
                    $keywords[] = AiAgentConstants::OCCURRED_EVENT_ENUM_TO_NAME_MAP[$item] ?? '';
                }

                // 上个三个渠道日聊天信息
                $recentCommunication[$firstContactTimes[$itemJourney['journey_id']]] = [
                    'summary' => [
                        'cn' => $itemJourney['summary'],
                        'en' => $analyzeResult['summary'] ?? ''
                    ],
                ];

                if (!empty($keywords)) {
                    $recentCommunication[$firstContactTimes[$itemJourney['journey_id']]]['occurred_events'] = $keywords;
                }
            }
        }

        return [json_encode($recentCommunication, JSON_UNESCAPED_UNICODE), json_encode($lastMessages, JSON_UNESCAPED_UNICODE), json_encode($chatRecords, JSON_UNESCAPED_UNICODE), json_encode($followSuggestion, JSON_UNESCAPED_UNICODE)];
    }


    // 目前这个方法只有报表生成会使用，用于校验是否是beforeClose的pipeline中的方法
    public static function checkIsProcessingFunctionRespond($data) : bool
    {
        $context = $data['context'] ?? [];
        if (!empty($data['message_type'])) return false;
        if (empty($context)) return false;
        $key = array_keys($context);
        if (array_intersect($key,['query_logic','question_list', 'deep_analysis', 'analysis_strategy'])) {
            return true;
        }
        return false;
    }

    public static function constructProcessingFunctionMessage($data)
    {
        $PBMessageInfo = new \protobuf\OkkiAi\PBAiAgentChatCompletionProcessData();
        $context = $data['context'] ?? [];
        foreach ($context as $key => $item)
        {
            switch ($key) {
                case 'query_logic':
                    $PBMessage = new PBAiAgentChatCompletionQueryLogicData();
                    $PBMessage->setLoading($item['loading'] ?? 1);
                    $PBMessage->setText($item['text'] ?? '');
                    $PBMessageInfo->setQueryLogic($PBMessage);
                    break;
                case 'question_list':
                    $PBMessage = new PBAiAgentChatCompletionQuestionRecommendData();
                    $PBMessage->setQuestionList($item);
                    $PBMessageInfo->setQuestionRecommend($PBMessage);
                    break;
                case 'analysis_strategy':
                    $PBMessage = new PBAnalysisStrategyData();
                    $PBMessage->setText($item['text'] ?? '');
                    $PBMessage->setLoading($item['loading'] ?? 1);
                    $PBMessageInfo->setAnalysisStrategy($PBMessage);
                    break;
                case 'deep_analysis':
                    $PBMessage = new PBDeepAnalysisData();
                    $PBMessage->setText($item['text'] ?? '');
                    $PBMessage->setLoading($item['loading'] ?? 1);
                    $PBMessageInfo->setDeepAnalysis($PBMessage);
                    break;
                default:
                    $PBMessage = null;
                    break;
            }
        }
        return $PBMessageInfo;
    }

    /**
     * 格式化"往来邮件"内容
     * @param $emailChatInfo
     * @return array
     */
    public static function formatMailContentToPromptText(array $emailChatInfo): array
    {
        $res = [];
        if(empty($emailChatInfo)) return $res;

        $pattern = '/[\s\S]*?(?=^[-_\w]*\s*From:\s*|----- Original ------|^[-_\w]*\s * 发件人:\s*|^[-_\w]*\s * 发件人 \s*|^[-_\w]*\s*From\s*|^[-_\w]*\s * 在 [^\n]* 写道 |[\s\S]{0,100}-{5,}[\s\S]* 原始 [\s\S]{0,50}-{5,})/';

        // 对动态列表按照 user_id + customer_id 进行切分
        $mailInfoWithPairMap = [];
        foreach ($emailChatInfo as $item)
        {
            $mailType = $item['mailType'] ?? 0;
            $mailId = $item['mailId'] ?? 0;
            $receiveTime = $item['receiveTime'] ?? '';
            $mailContent = $item['plainText'] ?? '';
            $mailContent = preg_replace('/\n\s*\n/', PHP_EOL . PHP_EOL, $mailContent);
            $role = ($mailType == \Mail::MAIL_TYPE_SEND) ? 'Salesperson' : 'Customer';
            $userId = $item['userId'];
            $customerId = $item['customerId'];

            if (empty($mailType)) continue;

            // 截取邮件正文内容
            if (preg_match($pattern, $mailContent, $matches)) {
                $matchContent = $matches[0];
                // 正则匹配取前 1500, 截断
                $mailContent = substr($matchContent, 0, min(1500, strlen($matchContent)));
            } else {
                // 正则不匹配取根据 1000 截断
                $mailContent = substr($mailContent, 0, 1000);
            }

            // 附件列表
            $attachmentPromptText = '';
            if (!empty($item['attachment'])) {
                $attachmentImplodeStr = implode("] [",$item['attachment']);
                $attachmentPromptText = "-attachment:[{$attachmentImplodeStr}]";
            }
            $formatContent = <<<EOF
{$role}: {$receiveTime} From:{$item['sender']} To:{$item['receiver']} Subject:"{$item['subject']}"
Content: {$mailContent}
{$attachmentPromptText}
EOF;

            $pair = "{$userId}-{$customerId}";
            $pre = $mailInfoWithPairMap[$pair] ?? [];

            if (empty($pre)) {
                $mailInfoWithPairMap[$pair] = [
                    'mailId' => [$mailId],
                    'receiveTime' => $receiveTime,
                    'firstTime' => $receiveTime,
                    'content' => $formatContent,
                ];
            } else {
                $pre['content'] .= PHP_EOL . $formatContent;
                $pre['receiveTime'] = max($pre['receiveTime'], $receiveTime);
                $pre['firstTime'] = min($pre['firstTime'], $receiveTime);
                $pre['mailId'] = array_values(array_unique(array_merge($pre['mailId'],[$mailId])));
                $mailInfoWithPairMap[$pair] = $pre;
            }
        }

        foreach ($mailInfoWithPairMap as $pair => $info)
        {
            $ss = explode("-", $pair);
            $userId = intval($ss[0]);
            $customerId = intval($ss[1]);
            $res[] = [
                'snsType' => \AiQualityCheckChatJourneyModel::SNS_TYPE_MAIL,
                'referIds' => $info['mailId'] ?? [],
                'content' => $info['content'] ?? '',
                'recentContactTime' => $info['receiveTime'] ?? '',
                'firstContactTime' => $info['firstTime'] ?? '',
                'customerId' => $customerId,
                'userId' => $userId,
            ];
        }
        return $res;
    }

    static public function extractLargestJson($text): string
    {
        $stack = [];
        $startIndex = null;
        $maxJsonStr = '';

        $textLength = strlen($text);
        for ($i = 0; $i < $textLength; $i++) {
            $char = $text[$i];
            if ($char === '{') {
                if (empty($stack)) {
                    $startIndex = $i;
                }
                array_push($stack, $char);
            } elseif ($char === '}') {
                if (!empty($stack)) {
                    array_pop($stack);
                    if (empty($stack)) {
                        $jsonStr = substr($text, $startIndex, $i - $startIndex + 1);
                        if (strlen($jsonStr) > strlen($maxJsonStr)) {
                            $maxJsonStr = $jsonStr;
                        }
                    }
                }
            }
        }

        return $maxJsonStr;
    }

    static function extractKeyword($dataList, $keyword): array
    {
        $parsed = [];
        if (empty($dataList)) {
            return [];
        }
        foreach ($dataList as $item) {
            $res = $item[$keyword] ?? '';
            if (!empty($res)) {
                $parsed[] = $res;
            }
        }
        return $parsed;
    }

    public static function editAiWorkbenchSwitch($clientId, $flag) : void
    {
        $client = \common\library\account\Client::getClient($clientId);
        $client->setExtentAttributes([\common\library\account\Client::EXTERNAL_KEY_AI_WORKBENCH_SWITCH => $flag]);
        $client->saveExtentAttributes();
    }
}
