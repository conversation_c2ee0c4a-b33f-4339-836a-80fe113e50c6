<?php
/**
 * Created by PhpStorm.
 * User: troy.li
 * Date: 2018-03-19
 * Time: 5:26 PM
 */

namespace common\library\lead;

use common\components\BaseObject;
use common\library\account\Client;
use common\library\ai\classify\ai_field_data\AIFieldData;
use common\library\ai\classify\setting\ApplySettings;
use common\library\ai\service\EventsReport;
use common\library\alibaba\customer\CustomerSyncHelper;
use common\library\behavior\BehaviorService;
use common\library\cms\form\FormFieldService;
use common\library\cms\inquiry\InquiryService;
use common\library\custom_field\company_field\CompanyField;
use common\library\custom_field\company_field\CustomerField;
use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldList;
use common\library\custom_field\lead_field\LeadCustomerField;
use common\library\custom_field\lead_field\LeadField;
use common\library\customer_v3\customer\CustomerList;
use common\library\customer\rule_config\RuleConfigConstants;
use common\library\customer\rule_config\RuleConfigTrait;
use common\library\customer_convert\convert_config\ConfigService;
use common\library\customer_convert\ConvertHandler;
use common\library\history\lead\BatchLeadBuilder;
use common\library\history\lead\LeadBuilder;
use common\library\import\ImportMap;
use common\library\lead_v2\LeadMetadata;
use common\library\object\field\service\ScopeUserFieldService;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeFieldV2;
use common\library\privilege_v3\PrivilegeService;
use common\library\queue_v2\job\LeadsCompanyAutoFilledBackgroundInfoJob;
use common\library\queue_v2\job\LeadsCompanySearchSyncJob;
use common\library\queue_v2\job\PerformanceV2RecordJob;
use common\library\queue_v2\QueueService;
use common\library\server\es_search\SearchQueueService;
use common\library\setting\item\ItemSettingConstant;
use common\library\setting\library\group\Group;
use common\library\setting\library\group\GroupApi;
use common\library\setting\library\origin\Origin;
use common\library\setting\library\origin\OriginApi;
use common\library\setting\library\origin\OriginMetadata;
use common\library\setting\library\status\Status;
use common\library\setting\library\status\StatusApi;
use common\library\setting\user\UserSetting;
use common\library\swarm\SwarmService;
use common\library\trail\DynamicTrailBatchOperator;
use common\library\trail\LeadDynamicList;
use common\library\util\PgsqlUtil;
use common\library\trail\events\RemarkEvents;
use common\library\trail\TrailConstants;
use common\library\workflow\WorkflowConstant;
use common\models\client\LeadHistory;
use Constants;
use CustomerOptionService;
use LogUtil;
use ProjectActiveRecord;
use User;
use function AlibabaCloud\Client\json;

class Helper
{
    use RuleConfigTrait;

	public static $fieldSettings = [

		Constants::TYPE_LEAD  => [
			'tag'               => '线索标签',
		],
	];

    const MAX_BATCH_OPERATOR_REMARK_COUNT = 100;

    protected static function formatFields($clientId, $fieldType, $data = null, $mapGroup = false, $orderBy = FieldList::ORDER_PC)
    {
        $fieldInit = [
            'image_list' => [],
        ];
        $fieldFormatter = [
            'image_list' => [\common\library\customer\Helper::class, 'formatImageList'],
        ];

        $fieldList = new FieldList($clientId);
        $fieldList->setOrderBy($orderBy);
        $fieldList->setType($fieldType);
        $fieldList->setNeedList(0);
        $fieldList->setEnableFlag(1);
        $fields = $fieldList->find();

        $result = [];
        foreach ($fields as $fieldData) {
            $item = [];
            $item['id'] = $fieldData['id'];
            $item['name'] = $fieldData['name'];
            $item['base'] = $fieldData['base'];
            $item['require'] = $fieldData['require'];
            $item['group_id'] = $fieldData['group_id'];
            $item['hint'] = $fieldData['hint'];
            $item['field_type'] = $fieldData['field_type'];
            $item['ext_info'] = $fieldData['ext_info'];
            $item['default'] = $fieldData['default'];
            $item['disable_flag'] = $fieldData['disable_flag'];
            $item['relation_type'] = $fieldData['relation_type'];
            $item['relation_field'] = $fieldData['relation_field'];

            $item['value'] = $fieldInit[$fieldData['id']] ?? '';
            $result[] = $item;
        }

        if ($data) {
            foreach ($result as &$field) {
                $isSystemField = $field['base'] == 1;
                if ($isSystemField) {
                    $field['value'] = isset($fieldFormatter[$field['id']])
                        ? call_user_func(
                            $fieldFormatter[$field['id']],
                            $data[$field['id']] ?? ($fieldInit[$field['id']] ?? ''))
                        : '';
                } else {
                    $field['value'] = $data['external_field_data'][$field['id']] ?? '';
                }
            }
        }

        if ($mapGroup) {
            $result = self::formatGroupField($result);
        }

        return $result;
    }

    public static function getLeadFormatFields($clientId, $data = null, $mapGroup = false)
    {
        return static::formatFields($clientId, Constants::TYPE_LEAD, $data, $mapGroup);
    }

    public static function getLeadCustomerFormatFields($clientId, $data = null, $mapGroup = false)
    {
        return static::formatFields($clientId, Constants::TYPE_LEAD_CUSTOMER, $data, $mapGroup);
    }

    public static function query($clientId, $userId, $queryWord, $searchField, $offset, $limit)
    {
        $result = [
            'list'      => [],
            'totalItem' => 0,
        ];

        if (empty($queryWord)) {
            return $result;
        }

        $highlight = "<b>{$queryWord}</b>";
        $queryWordLength = strlen($queryWord);

        $listObj = new LeadList($clientId, $userId);
        $listObj->getSearcher()->setCacheSearchResult(true);
        $listObj->setKeyword($queryWord);
        if (empty($searchField)) {
            $searchField = [
                'company_name',
                'customer_name',
                'homepage',
                'email',
                'contact.value'
            ];
        }

        $listObj->setSearchFields($searchField);
        $listObj->setSkipPrivilege(true);
        $listObj->setFields([
            'lead_id',
            'name',
            'customer_name',
            'company_name',
            'create_time',
        ]);
        $listObj->setLimit($limit);
        $listObj->setOffset($offset);
        $leadList = $listObj->find();

        if (empty($leadList)) {
            return $result;
        }

        $count = count($leadList);

        $searchResult = $listObj->getSearcher()->getLastSearchResult();
        $resultMap = empty($searchResult) ? [] : array_combine(array_column($searchResult, '_id'), $searchResult);

        $list = [];

        $queryWord = htmlentities($queryWord);
        foreach ($leadList as $elem) {
            $isOwner = $elem['user_id'] == $userId;
            $isPublic = $elem['user_id'] == 0;
            $leadId = $elem['lead_id'];
            $appLeadName = $elem['name'];
            $companyName = str_ireplace($queryWord, $highlight, htmlentities($elem['company_name']));

            $tel = $elem['tel'];
            $contact = $elem['contact'];

            $qwPos = strpos($elem['email'], $queryWord);
            $atPos = strpos($elem['email'], '@');
            $suffixPos = strrpos($elem['email'], '.');
            $email = str_pad('', $suffixPos, '*') . substr($elem['email'], $suffixPos);
            $email[$atPos] = '@';

            if ($qwPos !== false) {
                $email = substr($email, 0, $qwPos) . $queryWord . substr($email, $qwPos + $queryWordLength);
                $email = str_ireplace($queryWord, $highlight, $email);
                $customerName = $elem['customer_name'];
            } else {
                if (stripos($elem['customer_name'], $queryWord) !== false) {
                    $email = '***@***.***';
                    $customerName = str_ireplace($queryWord, $highlight, htmlentities($elem['customer_name']));
                } else {
                    $customerName = $elem['customer_name'];
                    $email = '***@***.***';
                }
            }

            $telHighlightResult = array_reduce($tel, function ($carry, $v) use ($queryWord, $highlight) {
                if (strpos($v, $queryWord) !== false) {
                    $carry[] = str_ireplace($queryWord, $highlight, htmlentities($v));
                }
                return $carry;
            }, []);

            if (empty($telHighlightResult)) {
                $telHighlightResult[] = $tel[0] ?? '';
            }

            $telStr = implode(';', $telHighlightResult);
            $telStr = $isOwner ? $telStr : str_pad('', strlen($telStr), '*');

            $contactHighlightResult = array_reduce($contact, function ($carry, $v) use ($queryWord, $highlight) {
                if (strpos($v['value'], $queryWord) !== false) {
                    $carry[] = $v['type'] . ': ' . str_ireplace($queryWord, $highlight, $v['value']);
                }
                return $carry;
            }, []);

            if (empty($contactHighlightResult) && count($contact) > 0) {
                $contactHighlightResult[] = $contact[0]['type'] . ': ' . $contact[0]['value'];
            }

            $contactStr = implode(';', $contactHighlightResult);
            $contactStr = $isOwner ? $contactStr : str_pad('', strlen($contactStr), '*');

            $user = \User::getUserObject($elem['user_id']);
            $owner = ['公海线索'];
            if ($user->hasInfo()) {
                $owner[] = $user->getNickname();
            }

            $list[] = array(
                'name'             => $elem['name'],
                'lead_id'          => $leadId,
                'company_name'     => $companyName,
                'app_company_name' => $appLeadName,
                'customer_name'    => $customerName,
                'email'            => $email,
                'owner'            => $owner,
                'tel'              => $telStr,
                'contact'          => $contactStr,
                'create_time'      => $elem['create_time'],
                'is_owner'         => $isOwner, //app 端使用
                'is_public'        => $isPublic, //app 端使用
            );
        }

        $result['list'] = $list;
        $result['totalItem'] = $count;

        return $result;
    }

    public static function formatInputLeadFields($data)
    {
        $result = [];
        foreach ($data as $groupData) {
            $fields = $groupData['fields'];
            foreach ($fields as $field) {
                if ($field['base'] == 1) {
                    switch ($field['id']) {
                        case 'image_list':
                            if (!empty($field['value'])) {
                                foreach ($field['value'] as $image) {
                                    $result['image_list'][] = is_array($image) ? $image['file_id'] : intval($image);
                                }
                            }
                            break;
                        default:
                            $value = $field['value'] ?? '';
                            if (is_string($value)) {
                                $value = trim($value);
                            }
                            $result[$field['id']] = $value;
                    }
                } else {
                    $value = $field['value'] ?? '';
                    $result['external_field_data'][$field['id']] = is_string($value) ? trim($value) : $value;
                }
            }
        }

        return $result;
    }

    /**
     * @throws \CDbException
     * @throws \ProcessException
     */
    public static function updateOrderTime($clientId, $leadId, $checkRefer = null, $time = null, $opUserId=0)
    {
        if ($checkRefer && !\CustomerOptionService::checkReference($clientId, $checkRefer)) {
            return false;
        }

        $time = $time ?? date('Y-m-d H:i:s');

        if (!is_array($leadId)) {
            $leadId = [$leadId];
        }

        $leadIdsSql = implode(',', $leadId);

        $db = \PgActiveRecord::getDbByClientId($clientId);

        $set = "order_time=(CASE WHEN order_time < '{$time}'::timestamp THEN '{$time}' ELSE order_time END)";
        //更新renew_time
        $set .= ", renew_time=(CASE WHEN renew_time < '{$time}'::timestamp THEN '{$time}' ELSE renew_time END)";

        $isFollowUpRefer = [
            'refer_lead_remark',
        ];
        if (in_array($checkRefer, $isFollowUpRefer)) {
            $set .= ", follow_count=follow_count+1, follow_up_time=(CASE WHEN follow_up_time='1970-01-01 00:00:00' THEN '{$time}' ELSE follow_up_time END)";
        }

        $sql = "update tbl_lead set {$set} where lead_id in ({$leadIdsSql}) and client_id=:client_id";

        self::batchUpdateLeadStatus($leadId,$clientId,$opUserId);
        \common\library\performance_v2\Helper::runPerformance($clientId, \Constants::TYPE_LEAD, $leadId);

        $result =  $db->createCommand($sql)->execute([':client_id' => $clientId]);
    
        SearchQueueService::pushLeadQueue(\User::getLoginUser() ? \User::getLoginUser()->getUserId() : 0, $clientId, (array)$leadId, \Constants::SEARCH_INDEX_TYPE_UPDATE);
    
        return $result;
    }

    /**
     * @param $result
     * @param $groupResult
     * @param $fieldGroup
     *
     * @return array
     */
    protected static function formatGroupField($result)
    {
        $fieldGroup = CustomFieldService::getGroupNameMap(Constants::TYPE_LEAD);
        $groupResult = [];
        foreach ($result as $field) {
            $groupId = $field['group_id'];
            if (!isset($groupResult[$groupId])) {
                $groupResult[$groupId] = [
                    'group_id' => $groupId,
                    'name'     => $fieldGroup[$groupId],
                    'fields'   => []
                ];
            }
            $groupResult[$groupId]['fields'][] = $field;
        }

        return $groupResult;
    }

    public static function getLeadMailList($clientId, $leadId)
    {
        $customerList = new LeadCustomerList($clientId);
        $customerList->setEntityId($leadId);
        $customerList->setFields('email');
        $emailList = $customerList->find();
        return array_column($emailList, 'email');
    }

    public static function getStarList()
    {
        $starList = [
            ['star_id' => 0, 'star_name' => \Yii::t('customer', 'star_0')],
            ['star_id' => 1, 'star_name' => \Yii::t('customer', 'star_1')],
            ['star_id' => 2, 'star_name' => \Yii::t('customer', 'star_2')],
            ['star_id' => 3, 'star_name' => \Yii::t('customer', 'star_3')],
            ['star_id' => 4, 'star_name' => \Yii::t('customer', 'star_4')],
            ['star_id' => 5, 'star_name' => \Yii::t('customer', 'star_5')],
        ];

        return $starList;
    }

    /**
     * 提交跟进类型动态
     *
     * @param $userId
     * @param $leadId
     * @param $content
     * @param $remarkType
     * @param $leadCustomerId
     * @param $fileIds
     * @param $remarkTime
     * @param $address,
     * @param $longitude
     * @param $latitude
     * @param string $nextFollowUpTime
     * @return RemarkEvents
     */
    public static function remark(
        $userId,
        $leadId,
        $content,
        $remarkType,
        $leadCustomerId,
        $fileIds,
        $remarkTime,
        $address = '',
        $longitude = 0,
        $latitude = 0,
        $nextFollowUpTime = '',
        $checkOwner = true
    )
    {
        $user = \User::getUserObject($userId);
        $clientId = $user->getClientId();

        $remarkTime = date('Y-m-d H:i:s', empty($remarkTime) ? time() : strtotime($remarkTime));

        $remarkTypeMap = [
            TrailConstants::TYPE_REMARK_ADD,
            TrailConstants::TYPE_REMARK_TEL,
            TrailConstants::TYPE_REMARK_MEETING,
            TrailConstants::TYPE_REMARK_CONTACT,
            TrailConstants::TYPE_REMARK_VISIT_HEADQUARTERS,
            TrailConstants::TYPE_REMARK_VISIT_OFFICE,
            TrailConstants::TYPE_REMARK_COME_TO_VISIT_HEADQUARTERS,
            TrailConstants::TYPE_REMARK_COME_TO_VISIT_OFFICE,
            TrailConstants::TYPE_REMARK_MAIL,
            TrailConstants::TYPE_REMARK_VISIT,
        ];

        if (!in_array($remarkType, $remarkTypeMap)) {
            throw new \RuntimeException(\Yii::t('common', 'Parameter error'));
        }

        if (empty($leadCustomerId)) {
            $leadCustomerId = 0;
        }

        $lead = new Lead($clientId, $leadId);
        if (!$lead->checkOwner($user, $checkOwner)) {
            throw new \RuntimeException(\Yii::t('lead', 'Not your own leads'));
        }

        $customer = null;
        if ($leadCustomerId) {
            try {
                $customer = new LeadCustomer($clientId, $leadCustomerId);
                if ($customer->lead_id != $leadId) {
                    throw new \RuntimeException(\Yii::t('trail', 'Contact does not belong to the company'));
                }
            } catch (\ProcessException $e) {
                throw new \RuntimeException($e->getMessage());
            }
        }

        $data = [
            'content' => $content,
            'file_ids' => $fileIds,
            'address' => $address,
            'longitude' => round(floatval($longitude), 6),
            'latitude' => round(floatval($latitude), 6)
        ];

        $event = new RemarkEvents();
        $event->setType($remarkType);
        $event->setLeadId($leadId);
        $event->setLeadCustomerId($leadCustomerId);
        $event->setClientId($clientId);
        $event->setCreateUser($userId);
        $event->setCreateTime($remarkTime);
        $event->setUserId($userId);
        $event->setData($data);
        $event->run();

        $remarkType == TrailConstants::TYPE_REMARK_ADD && Helper::updateEditTime($clientId, $leadId, $remarkTime);
        Helper::updateRemarkTime($clientId, $leadId, $remarkType, $remarkTime);

        if (!empty($nextFollowUpTime))
        {
            $lead->next_follow_up_time = $nextFollowUpTime;
            $lead->update(['next_follow_up_time']);
        }

        \common\library\performance_v2\Helper::runPerformance($clientId,\Constants::TYPE_LEAD,$leadId);
        return $event;
    }


    public static function batchRemark($clientId, $userId, $params)
    {
        $count = count($params['lead_ids']);

        if ($count > self::MAX_BATCH_OPERATOR_REMARK_COUNT) {
            throw  new \RuntimeException("本次选中线索{$count}个，超过100个上限，不支持批量操作");
        }

        $db = \PgActiveRecord::getDbByClientId($clientId);

        $leadIdsSql = implode(",", $params['lead_ids']);
        $leadSql = "select lead_id, main_customer, user_id from tbl_lead where client_id = {$clientId} and lead_id in ({$leadIdsSql})";
        $leadList = $db->createCommand($leadSql)->queryAll();

        $leadIds = array_column($leadList, 'lead_id');
        $leadCustomerIds = array_column($leadList, 'main_customer');

        foreach($leadCustomerIds as &$leadCustomerId){
            $leadCustomerId = [$leadCustomerId];
        }

        $trailData['data'] = [];
        foreach($leadIds as $key => $leadId){
            $trailData['data'][$key] = [
                'content'   => $params['content'],
                'file_ids'  => $params['file_ids'],
                'address'   => '',
                'longitude' => 0,
                'latitude'  => 0,
            ];
        }
        $createTime = date('Y-m-d H:i:s');
        $trail = new \common\library\trail\events\BatchTrailEvents();
        $trail->setType($params['remark_type']);
        $trail->setClientId($clientId);
        $trail->setCreateUser($userId);
        $trail->setUserId($userId);
        $trail->setLeadId($leadIds);
        $trail->setData($trailData['data']);
        $trail->setLeadCustomerId($leadCustomerIds);
        $trail->setCreateTime($createTime);
        $trail->setSkipValidatorMap([$params['remark_type']]);
        $trail->run();


        //更新最联系时间
        \common\library\lead\Helper::updateOrderTime($clientId, $leadIds, 'refer_lead_remark');

        //行为记录
        $behavior = new BehaviorService();
        $behavior->setReferId($leadIds);
        $behavior->setType(\common\library\behavior\Helper::convertCustomerTrailType($params['remark_type']));
        $behavior->add();
        \StatisticsService::dynamicRemarkAdd($clientId, $userId, count($leadIds));


        $params['remark_type'] == TrailConstants::TYPE_REMARK_ADD && Helper::updateEditTime($clientId, $leadIds, $params['remark_time']);
        Helper::updateRemarkTime($clientId, $leadIds, $params['remark_type'], $params['remark_time']);

        if (!empty($params['next_follow_up_time']))
        {
            Helper::updateNextFollowUpTime($clientId, $leadIds, $params['next_follow_up_time']);
        }

        \common\library\performance_v2\Helper::runPerformance($clientId,\Constants::TYPE_LEAD,$leadIds);
        return count($leadIds);
    }

    /**
     * 有静态缓存
     * @param $clientId
     * @param $customerId
     * @return LeadCustomer
     */
    public static function getCustomer($clientId, $customerId)
    {
        static $map = [];

        $key = $clientId . '_' . $customerId;

        if (!array_key_exists($key, $map))
        {
            $map[$key] = new LeadCustomer($clientId, $customerId);
        }

        return $map[$key];
    }


    public static function trailStatisticByUser($userId, $clientId, $begin, $end)
    {
        $list = new \common\library\trail\DynamicListAbstract($clientId);
        $list->setCreateUser($userId);
        $list->setOperatorUserId($userId);
        $list->setBeginTime($begin);
        $list->setEndTime($end);
        $data = $list->getAllStatistics('lead');
        return $data;
    }

    public static function trailStatisticByLead($clientId, $leadIds)
    {
        $leadIds = array_chunk($leadIds, 100);

        $data = [];
        foreach ($leadIds as $ids) {
            $list = new \common\library\trail\DynamicListAbstract($clientId);
            $list->setLeadIds($ids);
            $list = $list->getAllStatisticsByLead();
            $data = array_merge($data, $list);
        }

        $return = [];
        foreach ($data as $item) {
            $leadId = $item['lead_id'];
            $nodeType = $item['type'];
            $return[$leadId][$nodeType] = $item['count'];
        }

        return $return;
    }


    public static function transferTrail($clientId, $userId, $leadId, $companyId)
    {
        $lead = new Lead($clientId, $leadId);

        if(!$lead->isArchive())
        {
            return false;
        }

        if($lead->company_id != $companyId)
        {
            return false;
        }

        $operator = new \common\library\trail\DynamicTrailBatchOperator($userId);
        $operator->setParams([
            'client_id' => $clientId,
            'lead_id' => $leadId
        ]);
        $res = $operator->updateMany([
            'company_id' => $companyId
        ]);
        if(!$res)
        {
            return false;
        }

        $leadCustomerList = new LeadCustomerList($clientId);
        $leadCustomerList->setLeadId($leadId);
        $leadCustomerList->setIsArchive(Lead::ARCHIVE_HIDDEN);
        $leadCustomerList->setFields('customer_id, company_customer_id');
        $list = $leadCustomerList->find();
        foreach ($list as $item) {
            if($item['company_customer_id'] > 0 && $item['customer_id']  > 0)
            {
                $operator = new \common\library\trail\DynamicTrailBatchOperator($userId);
                $operator->setParams([
                    'client_id' => $clientId,
                    'lead_id' => $leadId,
                    'lead_customer_id' => $item['customer_id']
                ]);
                $operator->updateMany([
                    'customer_id' => PgsqlUtil::formatArray((array) $item['company_customer_id'])
                ]);
            }
        }

        // 清除移入到客户里的重复referId的线索邮件动态
        $operator = new \common\library\trail\DynamicTrailBatchOperator($userId);
        $operator->setParams([
            'client_id' => $clientId,
            'company_id' => $companyId
        ]);
        $operator->clearLeadMail($companyId);

		\common\library\trail\Helper::resetCompanyLastTrailId($clientId, $companyId);

        return true;
    }

    //检查线索是否开启自动化创建权限，阿里账号线索自动化创建功能
    public static function checkLeadCreateClassifyPrivilege($clientId,$opUserId,$sellerAccountId,$messageType, $buyerMemberInfo = [], $accessToken = '', $buyerAccountId = '') {


		//获取阿里买家信息
		if(!$buyerMemberInfo && $accessToken){
			$buyerMemberInfo = CustomerSyncHelper::getAlibabaBuyerInfo($accessToken,$buyerAccountId);
		}

		$alibabaOriginList = [Origin::SYS_ORIGIN_ALIBABA_INQUIRY, Origin::SYS_ORIGIN_ALIBABA_NAME_CARD, Origin::SYS_ORIGIN_ALIBABA_RFQ, Origin::SYS_ORIGIN_ALIBABA_TM, Origin::SYS_ORIGIN_ALIBABA_ALIBABA, Origin::SYS_ORIGIN_EXHIBITION, Origin::SYS_ORIGIN_INTERNET];

		$sourceList = $buyerMemberInfo['source_list'] ?? [];
		$sourceList = static::findOrigin($sourceList);
		$originId = Origin::SYS_ORIGIN_ALIBABA_ALIBABA;

		foreach ($alibabaOriginList as $id){
			if(in_array($id, $sourceList)){
				$originId = $id;
				break;
			}
		}

		if (empty($originId)) {
			return false;
		}

		//$configInfo = (new ConfigService($clientId, [$originId]))->configInfo();
		$configInfoList = array_column((new ConfigService($clientId, [$originId, 0]))->configList(1)['list'], null, 'origin_id');
		$configInfo = $configInfoList[$originId] ?? [];

		if (empty($configInfo)) {
			return false;
		}

		$defaultConfigInfo = $configInfoList[0] ?? [];
        //检查阿里巴巴账号是否关闭了线索创建自动化功能
        if(!empty($defaultConfigInfo['filter_config'] ?? []) && isset($defaultConfigInfo['filter_config'][\Constants::TYPE_LEAD]) && in_array($sellerAccountId,($defaultConfigInfo['filter_config'][\Constants::TYPE_LEAD]['ignore_alibaba_account'] ?? []))){
            return false;
        }
        //检查类型的TM咨询触发，线索创建自动化规则中是否有勾选其他
//        if(!in_array($messageType,\common\library\alibaba\Constant::$insertLeadMessageTypeMap) && !empty($configInfo['filter_config'] ?? []) && !in_array(
//            ApplySettings::ALIBABA_OTHER,($configInfo['filter_config']['lead_trade_behavior']??[]))){
//            return false;
//        }
        return true;
    }

    /**
     * web端前端表示这个目前params_info无使用场景
     * @param \User $user
     * @return array
     */
    public static function getParamsInfo(\User $user)
    {
        $clientId = $user->getClientId();
//        $listObj = new \common\library\group\GroupList($clientId, Constants::TYPE_COMPANY);
//        $listObj->setIncludeSystemGroup(true);
//        $listObj->getFormatter()->groupConfigListSetting();
//        $group_list = $listObj->find();

        $api = new GroupApi($clientId, Constants::TYPE_COMPANY);
        $group_list = $api->listAll(true, [
            Group::External_KEY_PUBLIC_TIME,
            Group::External_KEY_START_PUBLIC_TIME,
            Group::External_KEY_PUBLIC_IGNORE_FORZEN_USER,
            Group::External_KEY_OWNER_ID
        ]);


        $status_list = (new StatusApi($clientId, \Constants::TYPE_LEAD))->listExtraData();
        $origin_list = CustomerOptionService::getOriginList($clientId);
        $star_list = static::getStarList();

//        $userList = \common\library\privilege_v2\Helper::PermissionDepartmentTree($user->getClientId(), $user->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_LEAD_VIEW);
//            $userList = \common\library\department\Helper::getAllManageableUser($user);
        return [
            'group_list'  => $group_list,
            'status_list' => $status_list,
            'origin_list' => $origin_list,
            'star_list'   => $star_list,
        ];
    }


    public static function getLeadCustomerId($clientId, $leadId, $email)
    {
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $sql = "SELECT customer_id FROM tbl_lead_customer WHERE client_id={$clientId} AND lead_id='{$leadId}' AND lower(email)='{$email}' AND is_archive in(1,2) ORDER  BY update_time DESC ";
        $leadCustomer = $db->createCommand($sql)->queryRow();
        return $leadCustomer?$leadCustomer['customer_id']:0;
    }

	public static function getCustomerId($clientId, $companyId, $email)
	{
		$db = \PgActiveRecord::getDbByClientId($clientId);
		$sql = "SELECT customer_id FROM tbl_customer WHERE client_id={$clientId} AND company_id='{$companyId}' AND lower(email)='{$email}' AND is_archive = 1 ORDER  BY update_time DESC ";
		$customer = $db->createCommand($sql)->queryRow();
		return $customer ? $customer['customer_id'] : 0;
	}


    /**
     * 通过邮箱获取线索联系人信息
     *
     * @param $clientId
     * @param $emails
     * @return array|\CDbDataReader|mixed
     */
    public static function getLeadCustomersByEmails($clientId, $emails)
    {
        if (empty($emails)) {
            return [];
        }

        $customerList = new \common\library\lead\LeadCustomerList($clientId);
        $customerList->setEmail($emails);
        $customerList->setFields(['is_archive', 'customer_id', 'company_id', 'lead_id', 'name', 'email']);
        $customerList->setIsArchive(\common\library\lead\Lead::ARCHIVE_OK);
        return $customerList->find();

    }

    /**
     * 更新询盘消息时间、最近发送/接收tm消息时间
     * @param $clientId
     * @param $leadIds
     * @param $messageTime
     * @param int $tradeType
     * @param int $messageType
     * @return void
     * @throws \CDbException
     * @throws \ProcessException
     */
    public static function updateTradeAndTmTime($clientId, $leadIds, $messageTime=null, int $tradeType=0, int $messageType=0) {
        if (empty($messageTime)) {
            $messageTime = date('Y-m-d H:i:s');
        }
        //区分询盘
        $updateTradeTimeFlag = 0;
        if($tradeType == 1 && in_array($messageType,[\common\library\alibaba\Constant::MESSAGE_TYPE_IM, \common\library\alibaba\Constant::MESSAGE_TYPE_ASSIGN_IM_IMQUIRY,\common\library\alibaba\Constant::MESSAGE_TYPE_TRANSFER_IM_IMQUIRY])) {
            $updateTradeTimeFlag = 1;
        }

        //消费队列有时消费顺序会有问题，当前消息时间比数据库消息时间更大时才更新
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $leadIdStr = implode(',',$leadIds);
        $updateSql = "UPDATE tbl_lead
					SET latest_receive_ali_trade_time = CASE WHEN latest_receive_ali_trade_time < '$messageTime' AND {$updateTradeTimeFlag} = 1 THEN '$messageTime' ELSE latest_receive_ali_trade_time END,
					    latest_receive_ali_tm_time         = CASE WHEN latest_receive_ali_tm_time < '$messageTime' AND {$tradeType} = 1 THEN '$messageTime' ELSE latest_receive_ali_tm_time END,
					    latest_send_ali_tm_time        = CASE WHEN latest_send_ali_tm_time < '$messageTime' AND {$tradeType} = 2 THEN '$messageTime' ELSE latest_send_ali_tm_time END
					     where lead_id in($leadIdStr)";

        $db->createCommand($updateSql)->execute();

        //更新线索分层信息
        \common\library\layer\Helper::updateLeadLayer($clientId, $leadIds);
      
        SearchQueueService::pushLeadQueue(\User::getLoginUser() ? \User::getLoginUser()->getUserId() : 0, $clientId, (array)$leadIds, \Constants::SEARCH_INDEX_TYPE_UPDATE);

        \LogUtil::info("[ updateTradeAndTmTime ] client_id: {$clientId}, leadIds:".json_encode($leadIds));
    }

    //返回私海线索联系人
    public static function getPrivateLeadCustomer($clientId, $userId, $email,$searchType = 'email')
    {
        if(!$email){
            return false;
        }
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $isArchive = Lead::ARCHIVE_OK;
        if($searchType == 'email'){
            $sql = "SELECT lead.lead_id,lead.name,lead.update_time,lead.order_time,lead.store_id,c.company_id,c.customer_id as lead_customer_id FROM tbl_lead lead
                        left join tbl_lead_customer c
                        on c.lead_id = lead.lead_id
                        WHERE c.client_id={$clientId}
                        AND c.user_id = '{{$userId}}'
                        AND lower(c.email) = lower('{$email}')
                        AND lead.is_archive = {$isArchive}
                        AND c.is_archive = 1
                        AND c.email <> ''
                        ORDER  BY lead.update_time DESC";

        }else{
            $domain = \EmailUtil::getDomain($email);
            $sql = "SELECT lead.lead_id,lead.name,lead.update_time,lead.store_id,lead.order_time,c.company_id,c.customer_id as lead_customer_id  FROM tbl_lead lead
                        left join tbl_lead_customer c
                        on c.lead_id = lead.lead_id
                        WHERE c.client_id={$clientId}
                        AND c.user_id = '{{$userId}}'
                        AND lower(c.email) like '%{$domain}'
                        AND lead.is_archive = {$isArchive}
                        AND c.is_archive = 1
                        ORDER  BY lead.update_time DESC";
        }

        $leadCustomers = $db->createCommand($sql)->queryAll();

        return $leadCustomers;
    }

    //根据线索联系人id获取是否本人私海线索
    public static function getPrivateLeadByCustomerIds($clientId, $userId,$leadCustomerIds)
    {
        if(!$leadCustomerIds){
            return [];
        }
        $db = \PgActiveRecord::getDbByClientId($clientId);

        $isArchive = Lead::ARCHIVE_OK;
        $leadCustomerIdStr = join(',',$leadCustomerIds);
        $sql = "SELECT lead.lead_id,lead.name,lead.update_time,lead.order_time,lead.store_id,c.company_id,lower(c.email) as email,c.customer_id as lead_customer_id
                    FROM tbl_lead lead
                    left join tbl_lead_customer c
                    on c.lead_id = lead.lead_id
                    WHERE c.client_id={$clientId}
                    AND c.user_id = '{{$userId}}'
                    AND c.customer_id in ($leadCustomerIdStr)
                    AND lead.is_archive = {$isArchive}
                    AND c.is_archive = {$isArchive}
                    ORDER  BY lead.update_time ASC";


        $leadCustomerList = $db->createCommand($sql)->queryAll();

        return $leadCustomerList;
    }



	public static function getPoolLeadByCustomerIds($clientId, $userId,$leadCustomerIds)
	{
		if(!$leadCustomerIds){
			return [];
		}
		$db = \PgActiveRecord::getDbByClientId($clientId);

		$isArchive = Lead::ARCHIVE_OK;
		$leadCustomerIdStr = join(',',$leadCustomerIds);
		$sql = "SELECT lead.lead_id,lead.name,lead.update_time,lead.order_time,lead.store_id,c.company_id,lower(c.email) as email,c.customer_id as lead_customer_id
                    FROM tbl_lead lead
                    left join tbl_lead_customer c
                    on c.lead_id = lead.lead_id
                    WHERE c.client_id={$clientId}
                    AND c.user_id = '{}'
                    AND c.customer_id in ($leadCustomerIdStr)
                    AND lead.is_archive = {$isArchive}
                    AND c.is_archive = {$isArchive}
                    ORDER  BY lead.update_time ASC";


		$leadCustomerList = $db->createCommand($sql)->queryAll();

		return $leadCustomerList;
	}


    public static function getLeadCustomerByLeadIds($clientId,$leadIds)
    {
        if(!$leadIds){
            return [];
        }
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $isArchive = Lead::ARCHIVE_OK;
        $leadIdStr = join(',',$leadIds);
        $sql = "SELECT lead.lead_id,lead.name,lead.update_time,lead.order_time,lead.store_id,c.company_id,lower(c.email) as email,c.customer_id as lead_customer_id
                    FROM tbl_lead lead
                    left join tbl_lead_customer c
                    on c.lead_id = lead.lead_id
                    WHERE c.client_id={$clientId}
                    AND lead.lead_id in ($leadIdStr)
                    AND lead.is_archive = {$isArchive}
                    AND c.is_archive = {$isArchive}
                    ORDER  BY lead.update_time ASC";

        $leadCustomerList = $db->createCommand($sql)->queryAll();

        return $leadCustomerList;
    }


    public static function getPrivateLeadCustomerByStoreId($clientId, $userId,$email,$storeId)
    {
        if(!$email){
            return false;
        }
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $isArchive = Lead::ARCHIVE_OK;
        $sql = "SELECT lead.lead_id,lead.name,lead.update_time,lead.order_time,lead.store_id,c.company_id,c.customer_id FROM tbl_lead lead
                    left join tbl_lead_customer c
                    on c.lead_id = lead.lead_id
                    WHERE c.client_id={$clientId}
                    AND c.user_id = '{{$userId}}'
                    AND lower(c.email)='{$email}'
                    AND c.email <> ''
                    AND lead.store_id = {$storeId}
                    AND lead.is_archive = {$isArchive}
                    AND c.is_archive = {$isArchive}
                    ORDER  BY lead.update_time DESC";


        $leadCustomers = $db->createCommand($sql)->queryRow();

        return $leadCustomers;
    }


    public static function getPendingLeadCount($clientId,$userId){

        if(!$clientId || !$userId){
            \LogUtil::info("leadParamEmpty client_id:{$clientId} user_id:{$userId}");
            return 0;
        }
        $db = \PgActiveRecord::getDbByClientId($clientId);
        if(!$db){
            \LogUtil::info("leadDbEmpty client_id:{$clientId} user_id:{$userId}");
            return 0;
        }
        $sql = "select count(1) from tbl_lead where client_id={$clientId}  and user_id='{" . $userId . "}' AND is_archive= 1 AND status =". Status::SYS_STATUS_NEW;
        $pendingLeadCount = $db->createCommand($sql)->queryScalar();
        return $pendingLeadCount;
    }

    public static function getAllExist($clientId, $userId, $type, $keyword)
    {
        $company = null;
        $leads = [];
        $leadFields = ['lead_id', 'name', 'company_name', 'is_public', 'user_id', 'owner'];
        switch ($type) {
            case 'name':
                $company = (new \common\library\customer_v3\company\orm\Company($clientId))->loadByName($keyword);
                $leads = \Lead::model()->findAll('client_id=:client_id and lower(company_name)=:company_name and is_archive=1',
                    ['client_id' => $clientId, ':company_name' => mb_strtolower($keyword,'UTF-8')]);
                break;
            case 'email':
                $customerList = new CustomerList($clientId);
                $customerList->setIsArchive(true);
                $customerList->setFields(['customer_id', 'company_id']);
                $customerList->setEmail($keyword);
                $customerListData = $customerList->find();
                $companyId = empty($customerListData) ? 0 : reset($customerListData)['company_id'];
                if ($companyId) {
                    $company = new \common\library\customer_v3\company\orm\Company($clientId, $companyId);
                }

                $leadCustomers = \LeadCustomer::model()->findAll('client_id=:client_id and lower(email)=:email and email <> \'\' and is_archive=1',
                [':client_id'=>$clientId, ':email' => strtolower(trim($keyword))]);
                $leadIds = array_column($leadCustomers, 'lead_id');

                if (count($leadIds)) {
                    $leads = \Lead::model()->findAll('lead_id in (' . implode(',', $leadIds) .') AND is_archive=1');
                }
                break;
            case 'tel':
                $customer = (new \common\library\customer_v3\customer\orm\Customer($clientId))->loadByTel($keyword);
                if ($customer->isExist()) {
                    $company = new \common\library\customer_v3\company\orm\Company($clientId, $customer->company_id);
                }

                if (is_array($keyword)) {
                    $leadCustomers = \LeadCustomer::model()->findAll('client_id=:client_id and full_tel_list && ARRAY[:tel]::text[] and is_archive=1 AND full_tel_list <> \'{}\'::text[]',
                        [':client_id'=>$clientId, ':tel'=>implode(',', $keyword)]);
                } else {
                    // is_archive=1是为了使用索引 理论上没有什么实际影响 使用层需要注意
                    $leadCustomers = \LeadCustomer::model()->findAll('client_id=:client_id and full_tel_list @> ARRAY[:tel]::text[] and is_archive=1 AND full_tel_list <> \'{}\'::text[]',
                        [':client_id'=>$clientId, ':tel'=>$keyword]);
                }

                $leadIds = array_column($leadCustomers, 'lead_id');
                if (count($leadIds)) {
                    $leads = \Lead::model()->findAll('lead_id in (' . implode(',', $leadIds) .') AND is_archive=1');
                }
                break;
            default:
                break;
        }

        if ($company) {
            if ($company->isExist()) {
                $company->getFormatter()->setSpecifyFields(['company_id', 'name', 'owner', 'user_id', 'is_public']);
                $company = $company->getAttributes();
            } else {
                $company = null;
            }
        }

        if (count($leads)) {
            $formatter = new LeadFormatter($clientId, $userId);
            $formatter->setListData($leads);
            $formatter->setSpecifyFields($leadFields);
            $leads = $formatter->result();
        }

        return [$company, $leads];
    }

    /**
     * @param $clientId
     * @param array $leadId
     * @param $remarkType
     * @param null $remarkTime
     * 目前 社交/会面/电话跟进 更新时间
     */
    public static function updateRemarkTime($clientId, $leadId, $remarkType, $remarkTime = null){
        if(!in_array($remarkType, [TrailConstants::TYPE_REMARK_TEL, TrailConstants::TYPE_REMARK_MEETING, TrailConstants::TYPE_REMARK_CONTACT])){
            return;
        }

        if($remarkTime == null)
            $remarkTime = date('Y-m-d H:i:s');


        if (!is_array($leadId)) {
            $leadId = [$leadId];
        }

        $leadIdsSql = implode(',', $leadId);

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $sql = "update tbl_lead set remark_time = '$remarkTime' where lead_id in ({$leadIdsSql}) and client_id=$clientId and remark_time < '$remarkTime'";
        $db->createCommand($sql)->execute();

        self::batchUpdateLeadStatus($leadId,$clientId);
        \common\library\performance_v2\Helper::runPerformance($clientId,\Constants::TYPE_LEAD,$leadId);

    }

    /**
     * @param $clientId
     * @param array $leadId
     * @param null $time
     */
    public static function updateLatestWriteFollowUpTime($clientId, $leadId, $time = null){
        if($time == null) {
            $time = date('Y-m-d H:i:s');
        }

        if (!is_array($leadId)) {
            $leadId = [$leadId];
        }

        $leadIdsSql = implode(',', $leadId);

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $sql = "update tbl_lead set latest_write_follow_up_time = '$time' where lead_id in ({$leadIdsSql}) and client_id=$clientId and latest_write_follow_up_time < '$time'";
        $db->createCommand($sql)->execute();
    
        SearchQueueService::pushLeadQueue(\User::getLoginUser() ? \User::getLoginUser()->getUserId() : 0, $clientId, (array)$leadId, \Constants::SEARCH_INDEX_TYPE_UPDATE);
    
    }

    /**
     * @param $clientId
     * @param array $leadId
     * @param null $time
     */
    public static function updateNextFollowUpTime($clientId, $leadIds, $time = null){
        if (empty($time)) {
            return;
        }

        if (!is_array($leadIds)) {
            $leadIds = [$leadIds];
        }

        $leadIdsSql = implode(',', $leadIds);

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $sql = "update tbl_lead set next_follow_up_time = '$time' where lead_id in ({$leadIdsSql}) and client_id = $clientId and next_follow_up_time < '$time'";
        $db->createCommand($sql)->execute();
    
        SearchQueueService::pushLeadQueue(\User::getLoginUser() ? \User::getLoginUser()->getUserId() : 0, $clientId, (array)$leadIds, \Constants::SEARCH_INDEX_TYPE_UPDATE);
    
    }

    /** 编辑/快速跟进时间
     * @param $clientId
     * @param $leadId
     * @param null $editTime
     * @param int $operatorUserId
     * @param bool $referLeadStatusFlag 刷新线索ai状态标识
     * @throws \CDbException
     * @throws \ProcessException
     */
    public static function updateEditTime($clientId, $leadId, $editTime = null,$operatorUserId = 0,$referLeadStatusFlag = false){
        if($editTime == null)
            $editTime = date('Y-m-d H:i:s');

        $set = "edit_time=(CASE WHEN edit_time < '{$editTime}'::timestamp THEN '{$editTime}' ELSE edit_time END)";
        $set .= ", renew_time=(CASE WHEN renew_time < '{$editTime}'::timestamp THEN '{$editTime}' ELSE renew_time END)";

        $db = \PgActiveRecord::getDbByClientId($clientId);

        if (!is_array($leadId)) {
            $leadId = [$leadId];
        }
        $leadIdsSql = implode(',', $leadId);

        $sql = "update tbl_lead set $set where lead_id in ({$leadIdsSql}) and client_id = $clientId and edit_time < '$editTime'";
        $db->createCommand($sql)->execute();

        if($referLeadStatusFlag){
            self::batchUpdateLeadStatus($leadId,$clientId,$operatorUserId);
        }
        \common\library\performance_v2\Helper::runPerformance($clientId, \Constants::TYPE_LEAD, $leadId);
    
        SearchQueueService::pushLeadQueue(\User::getLoginUser() ? \User::getLoginUser()->getUserId() : 0, $clientId, (array)$leadId, \Constants::SEARCH_INDEX_TYPE_UPDATE);
    }

    public static function updateEdmTime($clientId, $userId, array $emails, $mailTime = null){
        $data = self::getLeadIdCustomerIdFromEmail($clientId, $userId, $emails);
        $leadIds = $data['lead_ids']??[];
        $leadCustomerIds = $data['lead_customer_ids']??[];
        if(empty($leadIds) && empty($leadCustomerIds)){
            return;
        }

        if($mailTime == null)
            $mailTime = date('Y-m-d H:i:s');

        $db = \PgActiveRecord::getDbByClientId($clientId);

        if(!empty($leadIds)) {
            $leadIdString = implode(',', $leadIds);
            $sql = "update tbl_lead set edm_time = '$mailTime' where lead_id IN ($leadIdString) and client_id = $clientId and edm_time < '$mailTime'";

            $db->createCommand($sql)->execute();

            self::batchUpdateLeadStatus($leadIds,$clientId,$userId);
        }

        if(!empty($leadCustomerIds)) {
            $customerIdString = implode(',', $leadCustomerIds);
            $sql = "update tbl_lead_customer set edm_time = '$mailTime' where customer_id IN ($customerIdString) and client_id = $clientId and edm_time < '$mailTime'";
            $db->createCommand($sql)->execute();
        }
        \common\library\performance_v2\Helper::runPerformance($clientId, \Constants::TYPE_LEAD, $leadIds);
    
        SearchQueueService::pushLeadQueue(\User::getLoginUser() ? \User::getLoginUser()->getUserId() : 0, $clientId, (array)$leadIds, \Constants::SEARCH_INDEX_TYPE_UPDATE);
    
    }


    //同步leads智能获客openSearch
    public static function syncLeadsSearchLead($clientId, $userIds, $companyHashIds, $opt, $lead_ids, $emails=[]): void
    {
        $userId = $userIds[0] ?? 0;
        \LogUtil::info('syncLeadsSearchLead for ' . $clientId, ['userId' => $userId, 'companyHashIds' => $companyHashIds, 'opt' => $opt,'lead_ids' => $lead_ids]);
        if(empty($clientId) || empty($userId) || empty($companyHashIds) || empty($opt)){
            return;
        }
        if (!is_array($companyHashIds)){
            $companyHashIds = [$companyHashIds];
        }

        QueueService::dispatch(new LeadsCompanySearchSyncJob([
            'clientId' => $clientId,
            'userId' => $userId,
            'companyHashIds' => $companyHashIds,
            'type' => 'lead',
            'action' => $opt, // add | del
            'ids' => $lead_ids,
            'emails' => $emails
        ]));
    }

    /**
     * 线索分层-从Leads自动填充背调信息
     * @param $clientId
     * @param $userIds
     * @param $lead
     * @return void
     */
    public static function autoFilledLeadFromLeads($clientId, $userIds, $lead)
    {
        $userId = $userIds[0] ?? 0;
        $leadId = $lead['lead_id'] ?? 0;

        \LogUtil::info('autoFilledLeadFromLeads for ' . $clientId, ['leadId' => $leadId]);

        if (!self::queryLeadAutoFilledSwitch($clientId, $userId)) {
            \LogUtil::info('auto filled switch is off, autoFilledLeadFromLeads end', [
                'clientId' => $clientId,
                'userId' => $userId,
                'leadId' => $leadId
            ]);
            return;
        }

        if (empty($clientId) || empty($userId) || empty($leadId)) {
            \LogUtil::info('params empty,autoFilledLeadFromLeads end', [
                'clientId' => $clientId,
                'userId' => $userId,
                'leadId' => $leadId
            ]);
            return;
        }

        if (!empty($lead['auto_filled'])) {
            \LogUtil::info('the lead has been auto filled,autoFilledLeadFromLeads end', [
                'clientId' => $clientId,
                'userId' => $userId,
                'leadId' => $leadId
            ]);
            return;
        }

        $job = new LeadsCompanyAutoFilledBackgroundInfoJob([
            'clientId' => $clientId,
            'userId' => $userId,
            'objectId' => $leadId,
            'referType' => Constants::TYPE_LEAD,
        ]);

        QueueService::dispatch($job);
    }



    /** 批量刷新线索的状态
     * @param $leadIds 线索列表ids
     * @param $clientId
     * @param int $operatorUserId 操作user_id
     * @return bool
     * @throws \CDbException
     * @throws \ProcessException
     */
    public static function batchUpdateLeadStatus($leadIds,$clientId,$operatorUserId = 0){

        if(!$leadIds){
            return false;
        }
        if(!is_array($leadIds)){
            $leadIds = [$leadIds];
        }
        if(!$operatorUserId){
            $adminUserId =  \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
            if(!$adminUserId){
                \LogUtil::error("[client_id{$clientId}] admin user empty");
                throw new \ProcessException('admin user empty:'.$clientId);
            }
            $userId = $adminUserId;
        }else{
            $userId = $operatorUserId;
        }
        $list = new \common\library\lead\LeadList($userId);
        $list->setClientId($clientId);
        $list->setLeadId($leadIds);
        $list->setSkipPrivilege(true);
        $list->setStatusEditType(Constant::STATUS_EDIT_TYPE_SYS); //系统修改的
        $list->getFormatter()->setReferLeadStatusSetting();
        $count = $list->count();
        if(!$count){
            return false;
        }
        $leadList = $list->find();
        if(!$leadList){
            return false;
        }
        $statusLeadMaps = [];
        $historyMap= [];
        $statusInquiryMaps = [];
        foreach ($leadList as $lead){
            //ai 状态有变更
            if($lead['ai_status_id'] > $lead['status_id']){
                $statusLeadMaps[$lead['ai_status_id']][] = $lead['lead_id'];
                $historyMap[$lead['lead_id']] = [
                    [
                        'id'   => 'status',
                        'base' => 1,
                        'new'  =>$lead['ai_status_id'],
                        'old'  => $lead['status_id']
                    ]
                ];

                $lead['origin_list'] = (array)(isset($lead['origin_list']) ? (is_array($lead['origin_list']) ? $lead['origin_list'] : PgsqlUtil::trimArray($lead['origin_list']) ?? []) : [($lead['origin'] ?? 0)]);

                if (in_array(Origin::SYS_ORIGIN_WEBSITE, $lead['origin_list'] ?? []) && !empty(InquiryService::INQUIRY_STATUS[InquiryService::INQUIRY_STATUS_STAGE_LEAD][$lead['ai_status_id']])) {
                    $statusInquiryMaps[$lead['ai_status_id']][] = $lead['lead_id'];
                }
            }
        }
        if(!$statusLeadMaps){
            return false;
        }
        $now = date('Y-m-d H:i:s',time());
        $db = \PgActiveRecord::getDbByClientId($clientId);

        //跟进AI分类更新线索
        foreach ($statusLeadMaps as $statusId => $leadIds){
            $leadIds = array_unique($leadIds);
            $leadIdStr = implode(',', $leadIds);
            $updateSql = "UPDATE tbl_lead SET status = {$statusId},update_time='{$now}' WHERE  client_id = {$clientId} AND lead_id IN ($leadIdStr)";
            $affect_count = $db->createCommand($updateSql)->execute();
            \LogUtil::info("batchUpdateLeadStatus client_id:{$clientId} lead_ids:{$leadIdStr}  new_status:{$statusId} affect_count:{$affect_count} \n");

            if (!empty($statusInquiryMaps[$statusId])) {
                $leadIds = array_unique($statusInquiryMaps[$statusId]);
                $leadIdStr = implode(',', $leadIds);
                $inquiryStatus = InquiryService::INQUIRY_STATUS[InquiryService::INQUIRY_STATUS_STAGE_LEAD][$statusId];
                $sql = "update tbl_inquiry set inquiry_status={$inquiryStatus},update_time='{$now}' where client_id={$clientId} and lead_id IN ($leadIdStr)";
                $affect_count = $db->createCommand($sql)->execute();
                \LogUtil::info("batchUpdateInquiryStatus client_id:{$clientId} lead_ids:{$leadIdStr}  new_status:{$inquiryStatus} affect_count:{$affect_count} \n");
            }

        }
        //更新操作历史
        $history = new BatchLeadBuilder();
        $history->setEditInfo(BaseObject::FIELD_EDIT_TYPE_BY_AI);
        $history->buildByMap($clientId, $userId, LeadHistory::TYPE_EDIT_STATUS, $historyMap);
    
        SearchQueueService::pushLeadQueue(\User::getLoginUser() ? \User::getLoginUser()->getUserId() : 0, $clientId, (array)array_column($leadList, 'lead_id'), \Constants::SEARCH_INDEX_TYPE_UPDATE);
    }

    private static function getLeadIdCustomerIdFromEmail($clientId, $userId, array $emails){

        if (empty($emails))
        {
            return [
                'lead_ids' => [],
                'lead_customer_ids' => [],
                'email_list' => [],
            ];
        }

        $leadCustomerListObj = new LeadCustomerList($clientId);
        $leadCustomerListObj->setEmail($emails);
        $leadCustomerListObj->setUserId($userId);
        $leadCustomerListObj->setFields('lead_id, customer_id, email');
        $leadCustomerList = $leadCustomerListObj->find();

        $leadIds = [];
        $leadCustomerIds = [];
        $existsEmails = [];
        foreach ($leadCustomerList as $item) {
            $leadIds[] = $item['lead_id'];
            $leadCustomerIds[] = $item['customer_id'];
            $existsEmails[] = $item['email'];
        }

        $leadIds = array_unique($leadIds);
        $leadCustomerIds = array_unique($leadCustomerIds);

        return [
            'lead_ids' => $leadIds,
            'lead_customer_ids' => $leadCustomerIds,
            'email_list' => $existsEmails,
        ];
    }

    public static function updateSendMailTime($clientId, $userId, array $emails, $mailTime = null){
        $data = self::getLeadIdCustomerIdFromEmail($clientId, $userId, $emails);
        $leadIds = $data['lead_ids']??[];
        $leadCustomerIds = $data['lead_customer_ids']??[];
        if(empty($leadIds) && empty($leadCustomerIds)){
            return;
        }

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $mailTime = !empty($mailTime) ? $mailTime : date('Y-m-d H:i:s');
        if(!empty($leadIds)) {
            $leadIdString = implode(',', $leadIds);
            $sql = "update tbl_lead set send_mail_time='$mailTime',mail_time='$mailTime'
where lead_id IN ($leadIdString) and client_id=$clientId and send_mail_time < '$mailTime'";
            $db->createCommand($sql)->execute();

            self::batchUpdateLeadStatus($leadIds,$clientId,$userId);
        }

        if(!empty($leadCustomerIds)) {
            $customerIdString = implode(',', $leadCustomerIds);
            $sql = "update tbl_lead_customer set send_mail_time='$mailTime',mail_time='$mailTime'
where customer_id IN ($customerIdString) and client_id=$clientId and send_mail_time < '$mailTime'";
            $db->createCommand($sql)->execute();
        }
        \common\library\performance_v2\Helper::runPerformance($clientId, \Constants::TYPE_LEAD, $leadIds);
    
        SearchQueueService::pushLeadQueue(\User::getLoginUser() ? \User::getLoginUser()->getUserId() : 0, $clientId, (array)$leadIds, \Constants::SEARCH_INDEX_TYPE_UPDATE);
    }

    public static function getLeadCustomerName($clientId,$leadId,$customerId){
        $leadCustomerList = new LeadCustomerList($clientId);
        $leadCustomerList->setLeadId($leadId);
        $leadCustomerList->setCustomerId($customerId);
        $leadCustomerList->setFields(['name']);
        $leadCustomer = $leadCustomerList->find();
        $leadCustomerName = "";
        if($leadCustomer){
            $leadCustomerName = $leadCustomer[0]['name']??"";
        }
        return $leadCustomerName;
    }

    public static function updateReceiveMailTime($clientId, $userId, array $emails, $receiveMailTime = null){
        $data = self::getLeadIdCustomerIdFromEmail($clientId, $userId, $emails);
        $leadIds = $data['lead_ids']??[];
        $leadCustomerIds = $data['lead_customer_ids']??[];
        if(empty($leadIds) && empty($leadCustomerIds)){
            return;
        }

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $receiveMailTime = !empty($receiveMailTime) ? $receiveMailTime : date('Y-m-d H:i:s');
        if (!empty($leadIds)) {
            $leadIdString = implode(',', $leadIds);
            $set = "receive_mail_time=(CASE WHEN receive_mail_time < '{$receiveMailTime}'::timestamp THEN '{$receiveMailTime}' ELSE receive_mail_time END)";
            $set .= ", mail_time=(CASE WHEN mail_time < '{$receiveMailTime}'::timestamp THEN '{$receiveMailTime}' ELSE mail_time END)";
            $set .= ", renew_time=(CASE WHEN renew_time < '{$receiveMailTime}'::timestamp THEN '{$receiveMailTime}' ELSE renew_time END)";
            $sql = "update tbl_lead set {$set} where lead_id IN ($leadIdString) and client_id=$clientId";

            $db->createCommand($sql)->execute();

            self::batchUpdateLeadStatus($leadIds,$clientId,$userId);
        }

        if (!empty($leadCustomerIds)) {
            $customerIdString = implode(',', $leadCustomerIds);
            $sql = "update tbl_lead_customer set receive_mail_time='$receiveMailTime',mail_time='$receiveMailTime'
where customer_id IN ($customerIdString) and client_id=$clientId and receive_mail_time < '$receiveMailTime'";
            $db->createCommand($sql)->execute();
        }
        \common\library\performance_v2\Helper::runPerformance($clientId, \Constants::TYPE_LEAD, $leadIds);
   
        SearchQueueService::pushLeadQueue(\User::getLoginUser() ? \User::getLoginUser()->getUserId() : 0, $clientId, (array)$leadIds, \Constants::SEARCH_INDEX_TYPE_UPDATE);

    }

    /**
     * @param $clientId
     * @param $leadId
     * @return array
     * 用于智库、dx、AI获取hashId或domain
     */
    public static function getDomain($clientId, $leadId){
        $result = ['company_hash_id' => '', 'domain' => ''];
        $lead = new \common\library\lead\Lead($clientId, $leadId);
        if (!$lead->isExist()){
            return $result;
        }
        //TODO //暂未关联线索
        $email = $lead->main_customer_email;
        if (empty($email)) {
            $lead->getFormatter()->setShowCustomer(1);
            $data = $lead->getAttributes();
            $emails = array_filter(array_column($data['customers'] ?? [], 'email'));
            $email = !empty($emails) ? array_shift($emails) : '';
        }
        $domain = !empty($email) ? \EmailUtil::getDomain($email) : '';
        if (empty($domain) && !empty($lead->homepage)) {
            $domain = \Util::getUrlBaseDomain($lead->homepage);
        }
        $result['domain'] = \common\library\email\CommonDomain::check($domain) ? '' : $domain;
        $result['company_hash_id'] = $lead->company_hash_id;
        return $result;
    }

    /**
     * @param $userId
     * @param array $leadIds
     * @param bool $referLeadStatusFlag 刷新线索更新时间标识
     * @return bool
     * 重新计算lead AI字段相关时间，更新AI标签、AI状态对应字段
     */
    public static function aiFieldRebuild($userId, array $leadIds){
        $key = 'lead_ai_fields_rebuild_'.$userId.'_'.time();
        $data = ['user_id' => $userId, 'lead_ids' => $leadIds];
        \Yii::app()->cache->set($key, $data, 86400);

        $log = '/tmp/lead_ai_field_rebuild.log';
        \common\library\CommandRunner::run(
            'lead',
            'aIFieldRebuildByTask',
            [
                'rebuild_task_id' => $key,
            ],
            $log
        );
    }

    /**
     * @param $clientId
     * @return array
     * 根据clientId返回相应拥有的跟进类型列表
     */
    public static function trailTypeList($clientId){
        $remarkType = [
            ['type_id' => TrailConstants::TYPE_REMARK_ADD , 'type_name' => \Yii::t('trail', '快速记录')],
            ['type_id' => TrailConstants::TYPE_REMARK_TEL , 'type_name' => \Yii::t('trail', '电话')],
            ['type_id' => TrailConstants::TYPE_REMARK_MEETING , 'type_name' => \Yii::t('trail', '会面')],
            ['type_id' => TrailConstants::TYPE_REMARK_CONTACT , 'type_name' => \Yii::t('trail', '社交平台')],
            ['type_id' => TrailConstants::TYPE_REMARK_VISIT , 'type_name' => \Yii::t('trail', '客户拜访')],
            ['type_id' => TrailConstants::TYPE_REMARK_MAIL , 'type_name' => \Yii::t('trail', '邮件')],
            ['type_id' => TrailConstants::TYPE_REMARK_SCHEDULE, 'type_name' => \Yii::t('trail', '日程')]
        ];

        $client = Client::getClient($clientId);
        //动态跟进类型开关（世绑公司专用)
        if($client->enabledSetting(Client::EXTERNAL_KEY_TRAIL_TYPE_SWITCH))
        {
            $spacialRemarkType = [
                ['type_id' => TrailConstants::TYPE_REMARK_VISIT_HEADQUARTERS , 'type_name' => \Yii::t('trail','总部拜访')],
                ['type_id' => TrailConstants::TYPE_REMARK_VISIT_OFFICE , 'type_name' => \Yii::t('trail','办事处拜访')],
                ['type_id' => TrailConstants::TYPE_REMARK_COME_TO_VISIT_HEADQUARTERS , 'type_name' => \Yii::t('trail','来访总部')],
                ['type_id' => TrailConstants::TYPE_REMARK_COME_TO_VISIT_OFFICE , 'type_name' => \Yii::t('trail','来访办事处')],
            ];

            $remarkType = array_merge($remarkType, $spacialRemarkType);
        }


        return $remarkType;
    }

    public static function updateOpenEdmFlagByEmail($clientId, $userId, array $emails, $taskId)
    {
        $emails = array_map(function ($email) {
            return \EmailUtil::findEmail($email);
        }, $emails);

        $data = self::getLeadIdCustomerIdFromEmail($clientId, $userId, $emails);
        $leadIds = $data['lead_ids']??[];

        $return = false;
        if (!empty($leadIds))
        {
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $leadIdsString = implode(',', $leadIds);
            $sql = "update tbl_lead set open_edm_flag = 1 where client_id = $clientId and lead_id IN ($leadIdsString)";
            $return = $db->createCommand($sql)->execute();

            self::updateRenewTime($clientId, array_unique($leadIds));
        }

        //回复邮件建档为线索
        $existsEmails = $data['email_list']??[];
        $archiveEmails = array_diff($emails, $existsEmails);
        $mailTask = \GroupMailTask::model()->find('task_id=:task_id', [':task_id' => $taskId]);
        $sender = $mailTask ? $mailTask->sender_mail : '';

        $handler = new ConvertHandler($clientId, $userId, \common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_MARKETING);

        if ($handler->getDuplicateFlag()) {
            !empty($emails) && self::edmEmailBuildToLead($clientId, $userId, $emails, ApplySettings::EDM_OPEN, $sender, $taskId);
        } else {
            !empty($archiveEmails) && self::edmEmailBuildToLead($clientId, $userId, $archiveEmails, ApplySettings::EDM_OPEN, $sender, $taskId);
        }

        //更新线索分层信息
        \common\library\layer\Helper::updateLeadLayer($clientId, $leadIds);
        return $return;
    }

    public static function updateOpenEdmUrlFlagByEmail($clientId, $userId, array $emails, $taskId)
    {
        $emails = array_map(function ($email) {
            return \EmailUtil::findEmail($email);
        }, $emails);

        $data = self::getLeadIdCustomerIdFromEmail($clientId, $userId, $emails);
        $leadIds = $data['lead_ids']??[];

        $return = false;
        if (!empty($leadIds))
        {
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $leadIdsString = implode(',', $leadIds);
            $sql = "update tbl_lead set open_edm_url_flag = 1 where client_id = $clientId and lead_id IN ($leadIdsString)";
            $return = $db->createCommand($sql)->execute();

            self::updateRenewTime($clientId, array_unique($leadIds));
        }

        //回复邮件建档为线索
        $existsEmails = $data['email_list']??[];
        $archiveEmails = array_diff($emails, $existsEmails);
        $mailTask = \GroupMailTask::model()->find('task_id=:task_id', [':task_id' => $taskId]);
        $sender = $mailTask ? $mailTask->sender_mail : '';

        $handler = new ConvertHandler($clientId, $userId, \common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_MARKETING);

        if ($handler->getDuplicateFlag()) {
            !empty($emails) && self::edmEmailBuildToLead($clientId, $userId, $emails, ApplySettings::EDM_CLICK, $sender, $taskId);
        } else {
            !empty($archiveEmails) && self::edmEmailBuildToLead($clientId, $userId, $archiveEmails, ApplySettings::EDM_CLICK, $sender, $taskId);
        }

        return $return;
    }

    public static function updateReplyEdmFlagByEmail($clientId, $userId, array $emails, $userMailId, $taskId)
    {
        $emails = array_map(function ($email) {
            return \EmailUtil::findEmail($email);
        }, $emails);

        $data = self::getLeadIdCustomerIdFromEmail($clientId, $userId, $emails);
        $leadIds = $data['lead_ids']??[];

        $return = false;
        if (!empty($leadIds))
        {
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $leadIdsString = implode(',', $leadIds);
            $sql = "update tbl_lead set reply_edm_flag = 1 where client_id = $clientId and lead_id IN ($leadIdsString)";
            $return = $db->createCommand($sql)->execute();

            self::updateRenewTime($clientId, array_unique($leadIds));
        }

        //回复邮件建档为线索
        $existsEmails = $data['email_list']??[];
        $archiveEmails = array_diff($emails, $existsEmails);
        $userMail = \UserMail::findByUserMailId($userId, $clientId, $userMailId);
        $sender = $userMail ? $userMail->email_address : '';

        $handler = new ConvertHandler($clientId, $userId, \common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_MARKETING);

        if ($handler->getDuplicateFlag()) {
            !empty($emails) && self::edmEmailBuildToLead($clientId, $userId, $emails, ApplySettings::EDM_REPLY, $sender, $taskId);
        } else {
            !empty($archiveEmails) && self::edmEmailBuildToLead($clientId, $userId, $archiveEmails, ApplySettings::EDM_REPLY, $sender, $taskId);
        }

        //更新线索分层信息
        \common\library\layer\Helper::updateLeadLayer($clientId, $leadIds);

        return $return;
    }

    public static function updateReplyMailFlag($clientId, $userId, array $emails)
    {
        $emails = array_map(function ($email) {
            return \EmailUtil::findEmail($email);
        }, $emails);

        $data = self::getLeadIdCustomerIdFromEmail($clientId, $userId, $emails);
        $leadIds = $data['lead_ids']??[];

        $return = false;
        if (!empty($leadIds))
        {
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $leadIdsString = implode(',', $leadIds);
            $sql = "update tbl_lead set reply_mail_flag = 1 where client_id = $clientId and lead_id IN ($leadIdsString)";
            $return = $db->createCommand($sql)->execute();

            self::updateRenewTime($clientId, array_unique($leadIds));
        }

        //更新线索分层信息
        \common\library\layer\Helper::updateLeadLayer($clientId, $leadIds);
        return $return;
    }

    public static function updateOpenMailFlag($clientId, $userId, array $emails)
    {
        $emails = array_map(function ($email) {
            return \EmailUtil::findEmail($email);
        }, $emails);

        $data = self::getLeadIdCustomerIdFromEmail($clientId, $userId, $emails);
        $leadIds = $data['lead_ids']??[];

        $return = false;
        if (!empty($leadIds))
        {
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $leadIdsString = implode(',', $leadIds);
            $sql = "update tbl_lead set open_mail_flag = 1 where client_id = $clientId and lead_id IN ($leadIdsString)";
            $return = $db->createCommand($sql)->execute();

            self::updateRenewTime($clientId, array_unique($leadIds));
        }

        //更新线索分层信息
        \common\library\layer\Helper::updateLeadLayer($clientId, $leadIds);

        return $return;
    }

    //线索更新时间，让线索记录置顶,批量更新
    public static function updateRenewTime($clientId, $leadIds, $renewTime = null)
    {
        if(empty($leadIds))
            return false;

        if (is_array($leadIds))
            $leadIdsString = implode(',', $leadIds);

        if($renewTime == null)
            $renewTime = date('Y-m-d H:i:s');

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $sql = "update tbl_lead set renew_time = '$renewTime' where client_id = $clientId and lead_id  IN ($leadIdsString)
and is_archive = 1 and  renew_time < '$renewTime'";

        $result =  $db->createCommand($sql)->execute();
    
        SearchQueueService::pushLeadQueue(\User::getLoginUser() ? \User::getLoginUser()->getUserId() : 0, $clientId, (array)$leadIds, \Constants::SEARCH_INDEX_TYPE_UPDATE);
    
        return $result;
    }

    public static function updateRenewTimeByEmails($clientId, $userId, array $emails, $renewTime = null) {
        if(empty($emails))
            return false;

        $data = self::getLeadIdCustomerIdFromEmail($clientId, $userId, $emails);
        $leadIds = $data['lead_ids']??[];

        return self::updateRenewTime($clientId, $leadIds, $renewTime);
    }

    //营销邮件产生追踪则建为档为线索或更新时间
    public static function edmEmailBuildToLead($client_id, $user_id , array $email, $behavior, $sender, $taskId)
    {
        if (empty($email))
            return false;

		//$configInfo = (new ConfigService($client_id, (array)\common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_MARKETING))->configInfo();
		$configInfoList = array_column((new ConfigService($client_id, [\common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_MARKETING, 0]))->configList(1)['list'], null, 'origin_id');
		$configInfo = $configInfoList[\common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_MARKETING] ?? [];
        if (empty($configInfo) || empty($behavior) || empty($configInfo['filter_config']) || !in_array($behavior, $configInfo['filter_config']['edm_behavior'] ?? []))
            return false;

        $userMailList = new \UserMailList();
        $userMailList->setClientId($client_id);
        $userMailList->setUserId($user_id);
        $userMailList->setEmailAddress($sender);
        $userMail = array_column($userMailList->find(), 'user_mail_id', 'email_address');

		$defaultConfigInfo = $configInfoList[0] ?? [];
        if (isset($userMail[$sender]) && !empty($defaultConfigInfo['filter_config'] ?? []) && isset($defaultConfigInfo['filter_config'][\Constants::TYPE_LEAD]) && in_array($userMail[$sender], $defaultConfigInfo['filter_config'][\Constants::TYPE_LEAD]['ignore_user_mail_id'] ?? [] )) {
            return false;
        }

        $emailString = implode(',', $email);
//        $yiic = \Yii::app()->params['yiic'];
//        $yiic = \Yii::getPathOfAlias('application') . "/$yiic";
//        $log = '/tmp/edm_email_build_to_lead.log';
//        $path = "{$yiic} lead EdmEmailBuildToLead --user_id={$user_id} --email={$emailString} --taskId={$taskId}";
//        $exec = "nohup $path >> {$log} 2>&1 &";
//        $output = array();
//        $return = 0;
//        \LogUtil::info($exec);
//        exec($exec, $output, $return);

        $log = '/tmp/edm_email_build_to_lead.log';
        \common\library\CommandRunner::run(
            'lead',
            'EdmEmailBuildToLead',
            [
                'user_id' => $user_id,
                'email' => $emailString,
                'taskId' => $taskId,
                'behavior' => $behavior,
            ],
            $log
        );

        return true;
    }

    public static function aiEventReport(array $leads, $eventType, $userId, $clientId, $setUserId = false)
    {
        /*
        $originList = new OriginList($clientId);
        $originList->setUserId($userId);
        $originList->getFormatter()->setSpecifyFields(['id', 'name']);
        $result = $originList->find();
        $originMap = array_column($result, 'name', 'id');
        */

        $originApi = new OriginApi($clientId);
        $originApi->setApiFields(['id','name']);
        $result = $originApi->listAll(true);
        $originMap = array_column($result, 'name', 'id');

        $eventReport = new EventsReport($clientId, $userId, \common\library\ai\service\EventsReport::REFER_TYPE_LEAD);

        foreach ($leads as $lead) {

            if ($setUserId) {
                //暂时没有多个用户共享一个线索
                $userId = \common\library\util\PgsqlUtil::trimArray($lead['user_id'])[0] ?? 0;
                $eventReport->setUserId($userId);
            }

            $eventReport->setReferId($lead['lead_id'] ?? 0);
            $eventReport->setEvent($eventType);

            $lead['origin_list'] = (array)(isset($lead['origin_list']) ? (is_array($lead['origin_list']) ? $lead['origin_list'] : PgsqlUtil::trimArray($lead['origin_list']) ?? []) : [($lead['origin'] ?? 0)]);

            $originId = $lead['origin_list'][0] ?? 0;

            //来源名字
            $originName = $originMap[$originId] ?? '';

            //共同的值
            $extraData = [
                'create_type' => EventsReport::CREATE_TYPE_MANUAL,
                'email' => $lead['main_customer_email'] ?? '',
                'origin' => $originId,
                'origin_name' => $originName,
                'group_id' => $lead['group_id'] ?? 0,
                'company_name' => $lead['company_name'] ?? '',
                'homepage' => $lead['homepage'] ?? '',
                'country' => $lead['country'] ?? '',
                'scale_id' => $lead['scale_id'] ?? 0,
            ];

            switch ($originId) {
                case Origin::SYS_ORIGIN_ID_MARKETING:
                case Origin::SYS_ORIGIN_ALIBABA :
                case Origin::SYS_ORIGIN_GLOBAL_SOURCES:
                case Origin::SYS_ORIGIN_GLOBAL_MARKET:
                case Origin::SYS_ORIGIN_MADE_IN_CHINA:
                    $extraData['create_type'] = EventsReport::CREATE_TYPE_AUTO;
                    break;

                case Origin::SYS_ORIGIN_ID_DISCOVERY:
                case Origin::SYS_ORIGIN_ID_OKKI_LEADS:
                case Origin::SYS_ORIGIN_ID_AI_RECOMMEND:
                    $extraData['company_hash_id'] = $lead['company_hash_id'] ?? 0;
                    if ($originId == Origin::SYS_ORIGIN_ID_AI_RECOMMEND) {
                        $extraData['pin'] = 1;
                    }
                    break;
                default:
                    break;
            }

            if ($eventType == EventsReport::EVENT_DELETE) {
                unset($extraData['create_type']);
            }

            $eventReport->setExtraInfo($extraData);
            $eventReport->addBodyItem();
        }

        return $eventReport->report();
    }


    //线索线索自动状态
    public static function referLeadStatus($clientId,$userId,$data,$updateEditTimeFlag = false){

        $defaultTime = '1970-01-01 08:00:00';
        $edmFlag = $data['edm_time'] > $defaultTime ? 1 : 0;
        if($data['edit_time'] > $defaultTime || $updateEditTimeFlag){
            $editFlag = 1;
        }else{
            $editFlag = 0;
        }
        $remarkFlag = $data['remark_time'] > $defaultTime ? 1 : 0;
        $sendMailFlag = $data['send_mail_time'] > $defaultTime ? 1 : 0;
        $receiveMailFlag = $data['receive_mail_time'] > $defaultTime ? 1 : 0;

        // 2020-07-14前 send_mail_time和receive_mail_time 会保存相同的时间值，导致线索这里的判断不准，需要做特殊处理
        if ($receiveMailFlag && $data['send_mail_time'] == $data['receive_mail_time']) {
            $receiveMailFlag = 0;
        }

        $leadId = $data['lead_id'];
        $status = $data['status'];
        $aiStatusId = Status::SYS_STATUS_NEW;
        //已转化
        if($data['is_archive'] == Lead::LEAD_TYPE_ARCHIVE){
            $aiStatusId = Status::SYS_STATUS_CONVERSION;
        } else if ((($sendMailFlag || $edmFlag) && $receiveMailFlag) || $remarkFlag) {
            $aiStatusId = Status::SYS_STATUS_INTERKNIT;
        } elseif (($sendMailFlag || $edmFlag) && !$receiveMailFlag) {
            $aiStatusId = Status::SYS_STATUS_INITIATIVE_TO_CONTACT;
        } elseif ($editFlag) {
            $aiStatusId = Status::SYS_STATUS_COMPLETE_INFO;
        }
        if($aiStatusId > $status){
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $sql = "update tbl_lead set status = {$aiStatusId} where client_id = {$clientId} AND lead_id = {$leadId}";
            $count = $db->createCommand($sql)->execute();
            if($count){

                $data['origin_list'] = (array)(isset($data['origin_list']) ? (is_array($data['origin_list']) ? $data['origin_list'] : PgsqlUtil::trimArray($data['origin_list']) ?? []) : [($data['origin'] ?? 0)]);

                // 更新关联的MKT询盘状态
                if (in_array(Origin::SYS_ORIGIN_WEBSITE, $data['origin_list']) && !empty(InquiryService::INQUIRY_STATUS[InquiryService::INQUIRY_STATUS_STAGE_LEAD][$aiStatusId])) {
                    $inquiryStatus = InquiryService::INQUIRY_STATUS[InquiryService::INQUIRY_STATUS_STAGE_LEAD][$aiStatusId];
                    $sql = "update tbl_inquiry set inquiry_status={$inquiryStatus} where client_id={$clientId} and lead_id={$leadId}";
                    $db->createCommand($sql)->execute();
                }

                $leadHistory = new LeadBuilder();
                $diffData = [
                    ["id" => "status", "new" => $aiStatusId, "old" => $status, "base" => 1]
                ];
                $leadHistory->setEditInfo(AIFieldData::FIELD_EDIT_TYPE_BY_AI);
                $leadHistory->build(
                    $clientId,
                    $userId,
                    LeadHistory::TYPE_EDIT_STATUS,
                    ['lead_id' => $leadId],
                    $diffData

                );
            }
        }
    
        SearchQueueService::pushLeadQueue(\User::getLoginUser() ? \User::getLoginUser()->getUserId() : 0, $clientId, (array)$leadId, \Constants::SEARCH_INDEX_TYPE_UPDATE);
    
    }

    public static function failReasonList(){
        $list = [
            Constant::FAIL_REASON_TYPE_1 => ['fail_type' => Constant::FAIL_REASON_TYPE_1,'field_type' => Constant::FAIL_TYPE_SYS, 'fail_reason' => \Yii::t('lead', '联系不上客户')],
            Constant::FAIL_REASON_TYPE_2 => ['fail_type' => Constant::FAIL_REASON_TYPE_2,'field_type' => Constant::FAIL_TYPE_SYS, 'fail_reason' => \Yii::t('lead', '线索重复')],
            Constant::FAIL_REASON_TYPE_3 => ['fail_type' => Constant::FAIL_REASON_TYPE_3,'field_type' => Constant::FAIL_TYPE_SYS, 'fail_reason' => \Yii::t('lead', '不符合客户要求')],
            Constant::FAIL_REASON_TYPE_4 => ['fail_type' => Constant::FAIL_REASON_TYPE_4,'field_type' => Constant::FAIL_TYPE_USER, 'fail_reason' => \Yii::t('lead', '自定义理由')],

        ];
        return $list;
    }


    //客户对应的线索来源店铺id
    public static function getLeadStoreIdByIdCompany($clientId, $companyId)
    {
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $sql = "SELECT store_id FROM tbl_lead WHERE client_id=:client_id AND company_id =:company_id";
        $data = $db->createCommand($sql)->queryRow(true, [':client_id'=> $clientId, ':company_id'=> $companyId]);
        return $data['store_id']??0;
    }

    public static function batchInitLeadTransformTask(int $clientId, $leadIds)
    {
        if (empty($leadIds)) return;
        if (count($leadIds) > 1000) throw new \RuntimeException("The number of conversion leads cannot exceed 1000");

        $db = \ProjectActiveRecord::getDbByClientId($clientId);
        $leadCounts = count($leadIds);
        $endIncrementTaskId = \ProjectActiveRecord::produceAutoIncrementId($leadCounts);
        $startIncrementTaskId = $endIncrementTaskId - $leadCounts + 1;
        $sql = "insert into tbl_lead_transform_task (`task_id`, `lead_id`, `status`, `company_id`, `create_time`, `update_time`, `client_id`) values ";
        $insertInfoList = [];
        $initTime = date('Y-m-d H:i:s');
        foreach ($leadIds as $leadId) {
            $initStatus = \LeadTransformTask::STATUS_INIT;
            $leadId = \Util::escapeDoubleQuoteSql($leadId);
            $insertInfoList[] = "({$startIncrementTaskId}, {$leadId}, {$initStatus}, 0, '{$initTime}', '{$initTime}','{$clientId}')";
            $startIncrementTaskId++;
        }
        $sql .= implode(',', $insertInfoList);
        $ret = $db->getPdoInstance()->prepare($sql)->execute();
    }

    public static function getRunningTransformTaskLeadIds($clientId, $leadIds)
    {
        if (empty($leadIds)) return [];
        $leadIds = array_map(function ($item){
            return \Util::escapeDoubleQuoteSql($item);
        }, $leadIds);
        $db = \ProjectActiveRecord::getDbByClientId($clientId);
        return $db->createCommand("select lead_id from tbl_lead_transform_task where lead_id in (" . implode(',', $leadIds) . ") and status = 2")->queryColumn();
    }

    /**
     * 批量修改company_id group_id，用于线索合并（慎用）
     * @param $fromLeadId
     * @param $toLeadId
     * @param $userId
     * @return mixed
     */
    public static function batchChangeLeadId($fromLeadId, $toLeadId, $userId)
    {
        $user = \User::getUserObject($userId);

        $operator = new DynamicTrailBatchOperator($user->getUserId());
        $operator->setParams([
            'client_id' => $user->getClientId(),
            'lead_id' => $fromLeadId
        ]);
        $res = $operator->updateMany([
            'lead_id' => $toLeadId
        ]);
        return $res;
    }

    /**
     * 批量修改customer_id，用于线索合并（慎用）
     * @param $leadId
     * @param $fromCustomerId
     * @param $toCustomerId
     * @param $userId
     * @return int
     */
    public static function batchChangeCustomerId($leadId, $fromCustomerId, $toCustomerId, $userId)
    {
        $user = \User::getUserObject($userId);

        $operator = new DynamicTrailBatchOperator($user->getUserId());
        $operator->setParams([
            'client_id' => $user->getClientId(),
            'lead_id' => $leadId,
            'lead_customer_id' => $fromCustomerId
        ]);
        $res = $operator->updateMany([
            'lead_customer_id' => PgsqlUtil::formatArray([$toCustomerId]),
        ]);
        return $res;
    }


	public static function getGrowthLevel($clientId, $leadId) {

		$db = \PgActiveRecord::getDbByClientId($clientId);

		$sql = 'SELECT client_id, lead_id, customer_id, growth_level
				FROM tbl_lead_customer
				WHERE client_id = ' . $clientId . '
				  AND is_archive = 1
				  AND lead_id = ' . $leadId . '
				  AND growth_level > 0';

		$result = $db->createCommand($sql)->queryAll();

		$growthLevel = [];

		array_walk($result, function ($item) use (&$growthLevel) {

			!isset($growthLevel[$item['growth_level']]) && $growthLevel[] = $item['growth_level'];
		});

		return $growthLevel;
	}

	/**
	 * 更新线索表阿里买家身份
	 *
	 * @param $clientId
	 * @param $leadId
	 * @return void
	 * @throws \CDbException
	 * @throws \CException
	 * @throws \ProcessException
	 */
	public static function updateLeadGrowthLevel($clientId, $leadId) {

		$db = \PgActiveRecord::getDbByClientId($clientId);

		$growthLevel = self::getGrowthLevel($clientId, $leadId);

		$sql = 'UPDATE tbl_lead
				SET growth_level = \'' . PgsqlUtil::formatArray($growthLevel) . '\'
				WHERE client_id = ' . $clientId . '
				  AND is_archive = 1
				  AND lead_id = ' . $leadId;

		$result = $db->createCommand($sql)->execute();
	}

    public static function getExportMapList($userId, $clientId)
    {
        //如果已经存在映射关系，先加载目前的映射
        $setting = new UserSetting($clientId, $userId, UserSetting::LEAD_EXPORT_MAP);
        $leadExportMap = $setting->getValue();
        $leadExportMap = json_decode($leadExportMap,true);
        if (empty($leadExportMap)) {
            //返回默认客户导出字段
            $leadExportMap = \common\library\export\LeadExport::$defaultLeadFieldMap;
        }
        return $leadExportMap;
    }

    public static function getExportFieldList($userId, $clientId)
    {
        $result = [];

        $fullFieldList = \common\library\customer\Helper::getFullFieldList($clientId, $userId, Constants::TYPE_LEAD, false, '', true);

        $groupFieldList = [];
        foreach ($fullFieldList as $item)
        {
            foreach ($item as $field)
            {
                if (!in_array($field['type'],[Constants::TYPE_LEAD, Constants::TYPE_LEAD_CUSTOMER]) || $field['field_type'] == CustomFieldService::FIELD_TYPE_IMAGE)
                {
                    continue;
                }
				$field['name'] = Helper::$fieldSettings[$field['type']][$field['id']] ?? $field['name'];
                $groupFieldList[$field['type']][] = $field;
            }
        }

        $group_info = [Constants::TYPE_LEAD => '线索信息', Constants::TYPE_LEAD_CUSTOMER => '联系人信息'];

        foreach ($group_info as $type => $groupName)
        {
            $result[] = [
              'group_type' => $type,
                'group_name' => $groupName,
                'group_list' => $groupFieldList[$type]
            ];
        }
        return $result;
    }


    /**
     * @param $clientId
     * @param $userId
     * @param $leadIds
     * @param $leadCustomerIds
     * @return void
     * @throws \CDbException
     * @throws \ProcessException
     * 更新最近发送edm时间，同时更新首次跟进时间以及最近跟进时间
     */
    public static function updateLatestEdmTime($clientId, $userId, $leadIds, $leadCustomerIds){
        if(empty($leadIds) && empty($leadCustomerIds)){
            return;
        }

        $mailTime = date('Y-m-d H:i:s');

        $set = " latest_edm_time=(CASE WHEN latest_edm_time < '{$mailTime}'::timestamp THEN '{$mailTime}' ELSE latest_edm_time END) ";
        if ( \CustomerOptionService::checkReference($clientId, 'refer_edm')
            || \CustomerOptionService::checkReference($clientId, 'refer_follow_edm')) {
            $set .= ", order_time=(CASE WHEN order_time < '{$mailTime}'::timestamp THEN '{$mailTime}' ELSE order_time END)";
            $set .= ", renew_time=(CASE WHEN renew_time < '{$mailTime}'::timestamp THEN '{$mailTime}' ELSE renew_time END)";
        }

        $db = \PgActiveRecord::getDbByClientId($clientId);

        if(!empty($leadIds)) {
            $leadIdString = implode(',', $leadIds);
            $sql = "update tbl_lead set {$set} where lead_id IN ($leadIdString) and client_id = $clientId";
            $db->createCommand($sql)->execute();
        }

        if(!empty($leadCustomerIds)) {
            $customerIdString = implode(',', $leadCustomerIds);
            $sql = "update tbl_lead_customer set latest_edm_time = '$mailTime' where customer_id IN ($customerIdString) and client_id = $clientId and latest_edm_time < '$mailTime'";
            $db->createCommand($sql)->execute();
        }
        \common\library\performance_v2\Helper::runPerformance($clientId, \Constants::TYPE_LEAD, $leadIds);
    
        SearchQueueService::pushLeadQueue(\User::getLoginUser() ? \User::getLoginUser()->getUserId() : 0, $clientId, (array)$leadIds, \Constants::SEARCH_INDEX_TYPE_UPDATE);
    
    }

    /**
     * @var $lead \common\library\lead\Lead
    */
    public static function setShopLead(&$lead, $inquiryLeadData) {
        if (($inquiryLeadData['name']??'') !== '') {
            $lead->name = $inquiryLeadData['name'];
        }

        if (($inquiryLeadData['company']??'') !== '') {
            $lead->company_name = $inquiryLeadData['company'];
        }

        if (($inquiryLeadData['country']??'') !== '') {
            $lead->country = $inquiryLeadData['country'];
        }

        if (($inquiryLeadData['homepage']??'') !== '') {
            $lead->homepage = $inquiryLeadData['homepage'];
        }

        if (($inquiryLeadData['address']??'') !== '') {
            $lead->address = $inquiryLeadData['address'];
        }

        if (($inquiryLeadData['remark']??'') !== '') {
            $lead->remark = $inquiryLeadData['remark'];
        }

        if (($inquiryLeadData['short_name']??'') !== '') {
            $lead->short_name = $inquiryLeadData['short_name'];
        }

        if (($inquiryLeadData['fax']??'') !== '') {
            $lead->fax = $inquiryLeadData['fax'];
        }

        if (($inquiryLeadData['tel']??'') !== '') {
            $lead->tel = $inquiryLeadData['tel'];
        }

        if (($inquiryLeadData['ip_country']??'') !== '') {
            $lead->inquiry_country = $inquiryLeadData['ip_country'];
        }

        if (isset($inquiryLeadData['scale_id']) && !empty($inquiryLeadData['scale_id']) && isset(Lead::SCALE_MAP[$inquiryLeadData['scale_id']])) {
            $lead->scale_id = $inquiryLeadData['scale_id'];
        }

        if (($inquiryLeadData['ad_keyword']??'') !== '') {
            $lead->ad_keyword = $inquiryLeadData['ad_keyword'];
        }

        if (($inquiryLeadData['external_field_data']??'') !== '') {
            $lead->external_field_data = is_array($inquiryLeadData['external_field_data'])? $inquiryLeadData['external_field_data']: json_decode($inquiryLeadData['external_field_data'], true);
        }
    }

    /**
     * @var $leadCustomer \common\library\lead\LeadCustomer
     */
    public static function setShopLeadCustomer(&$leadCustomer, $inquiryCustomerData) {
        if (($inquiryCustomerData['email']??'') !== '') {
            $leadCustomer->email = $inquiryCustomerData['email'];
        }

        if (($inquiryCustomerData['customer_name']??'') !== '') {
            $leadCustomer->name = $inquiryCustomerData['customer_name'];
        }

        if(($inquiryCustomerData['customer_remark']??'') !== '') {
            $leadCustomer->remark = $inquiryCustomerData['customer_remark'];
        }

        if (($inquiryCustomerData['contact']??'') !== '') {
            $contact = is_array($inquiryCustomerData['contact'])?$inquiryCustomerData['contact']:json_decode($inquiryCustomerData['contact'], true);
            if ($contact) {
                $leadCustomer->contact = $contact;
            }
        }

        if (($inquiryCustomerData['tel_list']??'') !== '') {
            $telList = is_array($inquiryCustomerData['tel_list'])?$inquiryCustomerData['tel_list']:json_decode($inquiryCustomerData['tel_list'], true);
            if ($telList) {
                $leadCustomer->tel_list = $telList;
            }
        }

        if (($inquiryCustomerData['post_grade']??'') !== '') {
            $leadCustomer->post_grade = $inquiryCustomerData['post_grade'];
        }

        if (($inquiryCustomerData['post']??'') !== '') {
            $leadCustomer->post = $inquiryCustomerData['post'];
        }

        if (($inquiryCustomerData['gender']??'') !== '') {
            $leadCustomer->gender = $inquiryCustomerData['gender'];
        }

        if (($inquiryCustomerData['external_field_data']??'') !== '') {
            $leadCustomer->external_field_data = is_array($inquiryCustomerData['external_field_data'])?
                $inquiryCustomerData['external_field_data']:json_decode($inquiryCustomerData['external_field_data']);
        }
    }

    public static function formatOkkiShopInquiryData($clientId, $leadItem,$mainCustomer,$leadInquiryData)
    {

        $sourceMap = [
            \common\library\cms\inquiry\InquiryService::ORIGIN_TYPE_CUSTOMER_SERVICE => 1,
            \common\library\cms\inquiry\InquiryService::ORIGIN_TYPE_CUSTOMER => 2,
            \common\library\cms\inquiry\InquiryService::ORIGIN_TYPE_CUSTOMER_WEB_CHAT => 3,
        ];

        $systemInfo = [
            "site_id" => $leadItem['site_id'] ?? 0,
            "create_time" => $leadItem['create_time'] ?? date('Y-m-d H:i:s'),
            "status" => $leadItem['status'] ?? 0,
            "mid" => $leadInquiryData['mid'] ?? 0,
            "source" => $sourceMap[$leadItem['inquiry_origin']??0] ?? 0,
        ];
        $guestInfo = [
            "guest_country" => $leadInquiryData['guest_country'] ?? '',
            "guest_ip" => $leadInquiryData['guest_ip'] ?? '',
            "guest_agent" => $leadInquiryData['guest_agent'] ?? '',
            "page_title" => $leadInquiryData['page_title'] ?? '',
            "page_link" => $leadInquiryData['page_link'] ?? ''
        ];

        if (!empty($guestInfo['guest_agent'])) {
            $guestInfo['guest_agent'] = preg_match('/ios|android|mobile/is', $guestInfo['guest_agent']) ? '移动端' : 'PC端';
        }

        $tel = '';
        $tel_list = $mainCustomer['tel_list']??[];
        if (!empty($tel_list))
        {
            $tel = implode('',reset($tel_list));
        }

        $inquiryFormInfo = [
            "name" => $mainCustomer['name'] ?? '',
            "customer_name" => $leadItem['name'] ?? '',
            "email" => $mainCustomer['email'] ?? '',
            "customer_email" => $mainCustomer['email'] ?? '',
            "tel" => $tel ?: '',
            "country" => empty($leadInquiryData['country'])?$leadItem['country']??'':$leadInquiryData['country'],
            "message" => $leadItem['inquiry_message'] ?? ''
        ];
        $inquiryFormInfo['country'] = \common\library\cms\inquiry\InquiryService::code2country($inquiryFormInfo['country']);

        $inquiryProductInfo = [
            "product_type" => $leadInquiryData['product_type'] ?? 0,
            "product_count" => $leadInquiryData['product_count'] ?? 0,
            "product_desc" => $leadInquiryData['product_desc'] ?? [],
        ];

        $inquiryCompanyInfo = [
            "company_brief" => empty($leadItem['short_name']) ? $leadItem['company_name'] ?? '':$leadItem['short_name'],
            "company_site" => $leadItem['homepage'] ?? '',
            "company_detail_address" => $leadItem['address'] ?? '',
            "company_scale" => $leadInquiryData['company_scale'] ?? '',
            "company_fax" => $leadItem['fax'] ?? '',
            "company_landline" => $leadItem['tel'] ?? '',
            "gender" => ($mainCustomer['gender'] ?? 1) == 1?0:1,
            "company_position" => $mainCustomer['post'] ?? '',
            'company_name' => $leadItem['company_name']??'',
        ];
        unset($leadItem['customers']);
        unset($leadItem['inquiry_data']);
        foreach ($leadItem as $key => $value) {
            $inquiryCompanyInfo[$key] = $value;
        }
        foreach ($mainCustomer?:[] as $key => $value) {
            $inquiryCompanyInfo["customer_{$key}"] = $value;
        }

        $formId = $leadInquiryData['inquiry_form_id']??0;
        if ($formId) {
            $defaultForm = FormFieldService::getDefaultForm()[0];
            if ($formId == $defaultForm['form_id']) {
                $fieldItem = $defaultForm['field_item'];
            } else {
                $form = new \common\library\cms\form\Form($clientId, $formId);
                $fieldItem = $form->isNew() ?
                    [\common\library\cms\form\FormFieldService::getFormMapCrm()['email'],\common\library\cms\form\FormFieldService::getFormMapCrm()['name']]
                    : $form->getFieldItem($texts[$formId] ?? []);
            }
            foreach ($fieldItem as &$item) {
                if (isset($item['map_field_type']) &&$item['map_field_type'] == Constants::TYPE_LEAD_CUSTOMER) {
                    $key = $item['map_field_id'];
                    $item['map_field_id'] = "customer_{$key}";
                }
            }
            // 自定义表单数据,补充到询盘数据
            $inquiryFieldInfo = $leadInquiryData['inquiry_field_info'] ?? [];
            foreach ($inquiryFormInfo as $formField => &$formValue) {
                if (isset($inquiryFieldInfo[$formField]) && empty($formValue)) {
                    $formValue = $inquiryFieldInfo[$formField]['value'] ?? '';
                }
            }
            $inquiryFieldInfo = array_column($leadInquiryData['inquiry_field_info'] ?? [], 'value', 'name');
            $inquiryFormInfo = $inquiryFormInfo + $inquiryFieldInfo;
        }
        if (!empty($leadInquiryData['shop_inquiry_form_id'])) {
            $shopInquiryFieldInfo = [
                'form_id' => $leadInquiryData['shop_inquiry_form_id'],
                'form_base_info' => $leadInquiryData['shop_inquiry_base_info']??[],
                'form_field_info' => $leadInquiryData['shop_inquiry_field_info']??[],
            ];
        }
        return [
            'lead_id' => $leadItem['lead_id'],
            'trash_flag' => $leadItem['trash_flag'] ?? 0,
            'system_info' => $systemInfo,
            'guest_info' => $guestInfo,
            'inquiry_form_info' => $inquiryFormInfo,
            'inquiry_product_info' => $inquiryProductInfo,
            'inquiry_company_info' => $inquiryCompanyInfo,
            'inquiry_form_field' => $fieldItem??[],
            'shop_form_info' => $shopInquiryFieldInfo ?? []
        ];
    }

    public static function buildSiteTrackDynamicForm($allFormData)
    {
        $formData = $extraData = [];
        foreach ($allFormData as $key => $value) {
            $key = self::formatFormItem($key);
            $formData[$key] = [
                'name' => $key,
                'value' => $value,
            ];
            $extraData[$key] = $value;
        }
        return [$formData, $extraData];
    }


    public static function formatFormItem($item)
    {
        if (\Util::containChinese($item)) {
            return $item;
        }
        return ucwords(strtolower(str_replace('_', ' ', $item)));
    }


    public static function getFieldsSort() {

        return [

            \common\library\customer\rule_config\RuleConfigConstants::RULE_TYPE_LEAD          => \common\library\customer\rule_config\RuleConfigConstants::LEAD_FIELDS,
            \common\library\customer\rule_config\RuleConfigConstants::RULE_TYPE_LEAD_CUSTOMER => \common\library\customer\rule_config\RuleConfigConstants::LEAD_CUSTOMER_FIELDS,
            \common\library\customer\rule_config\RuleConfigConstants::RULE_TYPE_DATE          => \common\library\customer\rule_config\RuleConfigConstants::LEAD_DATE_FIELDS,
        ];
    }

    public static function refreshLatestWriteFollowUpTimeByLeadIds($clientId, array $leadIds) {
        // 避免没有限定条件查了全表，危险
        if(empty($leadIds)) {
            return;
        }

        // 登录超管
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getAdminUserId();
        if(!$adminUserId){
            echo "error: 没有 adminUserId\n";
        }
        User::setLoginUserById($adminUserId, $clientId);

        // 找出每条 lead 的最近一条跟进动态
        $list = new LeadDynamicList($clientId);
        $list->setSimpleData(true);
        $list->setOperatorUserId($adminUserId);
        $list->setCheckOwner(false);
        $list->setLeadIds($leadIds);
        $list->setFields("distinct on (lead_id) lead_id,create_time");
        $list->setModuleId(TrailConstants::MODULE_REMARK);
        $list->setOrder('desc');
        $list->setOrderBy(['lead_id', 'create_time']);
        $list->setLimit(count($leadIds));
        $latestFollowDynamicList = $list->find();
        $latestWriteFollowTimeList = array_column($latestFollowDynamicList, 'create_time','lead_id');

        $updateValues = [];
        foreach ($leadIds as $leadId)
        {
            $latest_write_followup_time = key_exists($leadId, $latestWriteFollowTimeList) ? $latestWriteFollowTimeList[$leadId] : '1970-01-01 00:00:00';
            $updateValues[] = '('.implode(',', [$leadId, "'".$latest_write_followup_time."'"]).')';
        }

        $values = implode(',', $updateValues);
        $sql = "UPDATE tbl_lead as t SET latest_write_follow_up_time = new_table.new_value::timestamp FROM (Values {$values}) AS new_table(lead_id,new_value)
                WHERE t.lead_id=new_table.lead_id";
        $pg = \PgActiveRecord::getDbByClientId($clientId);
        $pg->getCommandBuilder()->createSqlCommand($sql)->execute();
    
        SearchQueueService::pushLeadQueue(\User::getLoginUser() ? \User::getLoginUser()->getUserId() : 0, $clientId, (array)$leadIds, \Constants::SEARCH_INDEX_TYPE_UPDATE);
    
    }


    public static function getBatchOperatorFieldList($clientId, $userId)
    {
        $fieldQuery = new FieldList($clientId);
        $fieldQuery->setType(Constants::TYPE_LEAD);
        $fieldQuery->setIsList(0);
        $fieldQuery->setFields(['id', 'group_id', 'type', 'field_type', 'name', 'ext_info', 'base', 'order', 'disable_flag']);
        $fieldQuery->setDisableFlag(0);
        $fieldQuery->setScene(PrivilegeFieldV2::SCENE_OF_UPDATE);
        $fieldQuery->setPrivilegeInfo($userId, PrivilegeConstants::FUNCTIONAL_LEAD);
        $fieldQuery->setExcludeId([
            'address',
            'ad_keyword',
            'ai_status',
            'ai_tags',
            'annual_procurement',
            'biz_type',
            'category_ids',
            'city',
            'company_name',
            'cus_tag',
            'fax',
            'group_id',
            'homepage',
            'image_list',
            'inquiry_country',
            'inquiry_origin',
            'lonlat',
            'name',
            'next_follow_up_time',
            'province',
            'remark',
            'serial_id',
            'short_name',
            'star',
            'status',
            'tel',
            'trail_status',
        ]);
        $fieldQuery->setFieldType([
            CustomFieldService::FIELD_TYPE_SELECT,
            CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
            CustomFieldService::FIELD_TYPE_DATE,
            CustomFieldService::FIELD_TYPE_DATETIME,
            CustomFieldService::FIELD_TYPE_NUMBER,
        ]);
        $fieldQuery->setOrderBy(FieldList::ORDER_PC);
        $list = array_values(array_filter($fieldQuery->find(), function($item) {
            if ($item['field_type'] == CustomFieldService::FIELD_TYPE_NUMBER) {
                return $item['id'] == 'star';
            } else {
                return true;
            }
        }));

        usort($list, function($item1, $item2) {
            if ($item1['group_id'] == $item2['group_id']) {
                return $item1['order'] == $item2['order'] ? 0 : ($item1['order'] > $item2['order'] ? 0 : 1);
            } else {
                return $item1['group_id'] <=> $item2['group_id'];
            }
        });

        return $list;
    }
    public static function appendDXEmployeeToLead($clientId, $userId, Lead $lead, int $employee_id): int
    {
        $employeeApi = new \common\library\discovery\api\Employee($clientId, $userId);
        $employeeList = $employeeApi->getEmployeeDetailsByIds([$employee_id]);
        $employee = $employeeList[0] ?? [];
        $emails = $employee['emails'] ?? [];
        if (empty($employee) || empty($emails)){
            throw new \RuntimeException(\Yii::t('lead', 'Contact does not exist or has been deleted'));
        }
        $contact = [];
        if(!empty($employee['facebook_url'])){
            $contact[] = [
                'type' => 'facebook', 'value' => $employee['facebook_url']
            ];
        }
        if(!empty($employee['linkedin_url'])){
            $contact[] = [
                'type' => 'linkedin', 'value' => $employee['linkedin_url']
            ];
        }
        if(!empty($employee['twitter_url'])){
            $contact[] = [
                'type' => 'twitter', 'value' => $employee['twitter_url']
            ];
        }
        $data = [];
        foreach ($emails as $email){
            $item = [];
            $item['email'] = $email;
            $item['name'] = $employee['name'] ?? '';
            $item['post'] = $employee['title'] ?? '';
            $item['contact'] = $contact;
            $item['tel_list'] = $employee['phone_numbers'] ?? [];
            $data[] = $item;
        }

        return self::appendDXCustomersToLead($clientId, $lead, $data);
    }

    public static function appendDXCustomersToLead($clientId, Lead $lead, array $data): int
    {
        if (!$data) {
            return 0;
        }
        $allCount = count($data);

        $models = \LeadCustomer::model()->findAll('lead_id=:lead_id and client_id=:client_id', [':lead_id' => $lead->lead_id, ':client_id' => $clientId]);
        $existsEmails = [];
        $customers = [];
        foreach ($models as $model) {
            $customer = new LeadCustomer($clientId);
            $customer->setModel($model);
            $customers[] = $customer;
            $existsEmails[$model->email] = true;
        }

        $v = new \Validator();
        $v->setConfig(\CustomerOptionService::getValidatorRule());

        $count = 0;
        foreach ($data as $contact) {
            if (empty($contact['email'])) {
                continue;
            }
            if (isset($existsEmails[$contact['email']])) {
                continue;
            }
            $existsEmails[$contact['email']] = true;

            $v->setInputData($contact);
            $v->validateConfig($v->getConfig(\CustomerOptionService::VALIDATE_RULE_OF_LEAD_CUSTOMER));
            $customer = new LeadCustomer($clientId);
            $customer->name = $contact['name'] ?? '';
            $customer->email = $contact['email'];
            $customer->post = $contact['post'] ?? '';
            $customer->tel_list = $contact['tel_list'] ?? [];
            $customer->contact = $contact['contact'] ?? [];
            $customers[] = $customer;
            $count++;
        }
        if ($count > 40) {
            throw new \RuntimeException(\Yii::t('lead', 'The number of customers exceeds the limit'));
        }

        $lead->setCustomerList($customers);
        $lead->save();
        return $allCount - $count;
    }
    
    
    public static function leadV2Field($clientId, $enable) {


//        TODO：这里的纯sql在全量后下线。
        $db = ProjectActiveRecord::getDbByClientId($clientId);

//        老用户标签
        $sql = "UPDATE tbl_custom_field
                SET id                    = 'cus_tag',
                    field_type            = 7,
                    columns               = '[\"cus_tag\"]',
                    relation_field        = CASE WHEN relation_field =  'tag' THEN 'cus_tag' ELSE relation_field END,
                    relation_origin_field = CASE WHEN relation_origin_field = 'tag' THEN 'cus_tag' ELSE relation_origin_field END,
                    disable_flag = 0,
                    enable_flag = 1,
                    edit_required  = 0
                WHERE client_id = {$clientId}
                  AND base = 1
                  AND type = 7
                  AND id = 'tag'";
        
        $result = $db->createCommand($sql)->execute();
        
        $sql = "UPDATE tbl_custom_field
                SET columns               = CASE WHEN '[\"tag\"]' THEN '[\"cus_tag\"]' ELSE columns END,
                    relation_field        = CASE WHEN relation_field =  'tag' THEN 'cus_tag' ELSE relation_field END,
                    relation_origin_field = CASE WHEN relation_origin_field = 'tag' THEN 'cus_tag' ELSE relation_origin_field END
                WHERE client_id = {$clientId}
                  AND base = 0
                  AND type = 7
                  AND (relation_field = 'tag' or relation_origin_field = 'tag')
                  ";
        
        $result = $db->createCommand($sql)->execute();
        
        $sql = "UPDATE tbl_field_group
                SET id                    = 'cus_tag'
                WHERE client_id = {$clientId}
                  AND type = 7
                  AND id = 'tag'";
        
        $result = $db->createCommand($sql)->execute();
        
        
        $sql = "UPDATE tbl_custom_field
                SET enable_flag = 0
                WHERE client_id = {$clientId}
                  AND base = 1
                  AND type = 8
                  AND id = 'tag'";
        
        $result = $db->createCommand($sql)->execute();
        
        
        // 开关新线索字段
        $leadNewFields = [
            [
                'id'                  => 'timezone',
                'type'                => \Constants::TYPE_LEAD,
                'group_id'            => CustomFieldService::LEAD_GROUP_BASIC,
                'base'                => 1,
                'name'                => '时区',
                'order'               => 29,
                'app_order'           => 29,
                'field_type'          => CustomFieldService::FIELD_TYPE_SELECT,
                'edit_required'       => 1,
                'edit_hide'           => 1,// 是否可编辑，1可以，0否
                'require'             => 0,
                'disable_flag'        => 0,//前端是否隐藏，1隐藏，0不隐藏
                'default'             => '',
                'edit_default'        => 0,
                'hint'                => '',
                'edit_hint'           => 1,
                'is_exportable'       => 1,
                'is_editable'         => 1,
                'is_list'             => 0,
                'columns'             => 'timezone',
                'relation_type'       => 0,
                'relation_field'      => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario'     => '',
                'export_group'        => '0',
                'readonly'            => 0,
                'create_time'         => date('Y-m-d H:i:s'),
                'update_time'         => date('Y-m-d H:i:s'),
            ],
            [
                'id'                  => 'annual_procurement',
                'type'                => \Constants::TYPE_LEAD,
                'group_id'            => CustomFieldService::LEAD_GROUP_BASIC,
                'base'                => 1,
                'name'                => '年采购额',
                'order'               => 30,
                'app_order'           => 30,
                'field_type'          => CustomFieldService::FIELD_TYPE_SELECT,
                'edit_required'       => 1,
                'edit_hide'           => 1,// 是否可编辑，1可以，0否
                'require'             => 0,
                'disable_flag'        => 0,//前端是否隐藏，1隐藏，0不隐藏
                'default'             => '',
                'edit_default'        => 0,
                'hint'                => '',
                'edit_hint'           => 1,
                'is_exportable'       => 1,
                'is_editable'         => 1,
                'is_list'             => 0,
                'columns'             => 'annual_procurement',
                'relation_type'       => 0,
                'relation_field'      => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario'     => '',
                'export_group'        => '0',
                'readonly'            => 0,
                'create_time'         => date('Y-m-d H:i:s'),
                'update_time'         => date('Y-m-d H:i:s'),
            ],
            [
                'id'                  => 'intention_level',
                'type'                => \Constants::TYPE_LEAD,
                'group_id'            => CustomFieldService::LEAD_GROUP_BASIC,
                'base'                => 1,
                'name'                => '采购意向',
                'order'               => 31,
                'app_order'           => 31,
                'field_type'          => CustomFieldService::FIELD_TYPE_SELECT,
                'edit_required'       => 1,
                'edit_hide'           => 1,// 是否可编辑，1可以，0否
                'require'             => 0,
                'disable_flag'        => 0,//前端是否隐藏，1隐藏，0不隐藏
                'default'             => '',
                'edit_default'        => 0,
                'hint'                => '',
                'edit_hint'           => 1,
                'is_exportable'       => 1,
                'is_editable'         => 1,
                'is_list'             => 0,
                'columns'             => 'intention_level',
                'relation_type'       => 0,
                'relation_field'      => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario'     => '',
                'export_group'        => '0',
                'readonly'            => 0,
                'create_time'         => date('Y-m-d H:i:s'),
                'update_time'         => date('Y-m-d H:i:s'),
            ],
            [
                'id'                  => 'biz_type',
                'type'                => \Constants::TYPE_LEAD,
                'group_id'            => CustomFieldService::LEAD_GROUP_BASIC,
                'base'                => 1,
                'name'                => '公司类型',
                'order'               => 32,
                'app_order'           => 32,
                'field_type'          => CustomFieldService::FIELD_TYPE_SELECT,
                'edit_required'       => 1,
                'edit_hide'           => 1,// 是否可编辑，1可以，0否
                'require'             => 0,
                'disable_flag'        => 0,//前端是否隐藏，1隐藏，0不隐藏
                'default'             => '',
                'edit_default'        => 0,
                'hint'                => '',
                'edit_hint'           => 1,
                'is_exportable'       => 1,
                'is_editable'         => 1,
                'is_list'             => 0,
                'columns'             => 'biz_type',
                'relation_type'       => 0,
                'relation_field'      => '',
                'relation_field_type' => 0,
                'relation_field_name' => '',
                'export_scenario'     => '',
                'export_group'        => '0',
                'readonly'            => 0,
                'create_time'         => date('Y-m-d H:i:s'),
                'update_time'         => date('Y-m-d H:i:s'),
            ],
        ];
        
        // 重新开启新字段
        $leadUpdateFields = [
            ['condition' => ['type' => \Constants::TYPE_LEAD, 'id' => 'timezone'], 'update' => ['enable_flag' => 1,]],
            ['condition' => ['type' => \Constants::TYPE_LEAD, 'id' => 'annual_procurement'], 'update' => ['enable_flag' => 1, 'name' => '年采购额']],
            ['condition' => ['type' => \Constants::TYPE_LEAD, 'id' => 'intention_level'], 'update' => ['enable_flag' => 1]],
            ['condition' => ['type' => \Constants::TYPE_LEAD, 'id' => 'biz_type'], 'update' => ['enable_flag' => 1]],
            ['condition' => ['type' => \Constants::TYPE_LEAD, 'id' => 'cus_tag'], 'update' => ['enable_flag' => 1]],
            ['condition' => ['type' => \Constants::TYPE_LEAD, 'id' => 'next_follow_up_time'], 'update' => ['name' => '下次日程时间']],
            ['condition' => ['type' => \Constants::TYPE_LEAD, 'id' => 'country'], 'update' => ['name' => '国家地区']],
            ['condition' => ['type' => \Constants::TYPE_LEAD, 'id' => 'origin_list'], 'update' => ['name' => '线索来源']],
            ['condition' => ['type' => \Constants::TYPE_LEAD, 'id' => 'cus_tag'], 'update' => ['name' => '线索标签']],
        ];
        
        // 关闭新字段
        $leadDisableFields = [
            ['condition' => ['type' => \Constants::TYPE_LEAD, 'id' => 'timezone'], 'update' => ['enable_flag' => 0]],
            ['condition' => ['type' => \Constants::TYPE_LEAD, 'id' => 'annual_procurement'], 'update' => ['enable_flag' => 0]],
            ['condition' => ['type' => \Constants::TYPE_LEAD, 'id' => 'intention_level'], 'update' => ['enable_flag' => 0]],
            ['condition' => ['type' => \Constants::TYPE_LEAD, 'id' => 'biz_type'], 'update' => ['enable_flag' => 0]],
            ['condition' => ['type' => \Constants::TYPE_LEAD, 'id' => 'cus_tag'], 'update' => ['enable_flag' => 0]],
        ];
        
        if ($enable) {
            if (!in_array('biz_type', \common\library\custom_field\Helper::getDeletedFieldIds($clientId, \Constants::TYPE_LEAD, 1))) {
                \common\library\custom_field\Helper::addField($clientId, $leadNewFields);
            }
            \common\library\custom_field\Helper::updateField($clientId, $leadUpdateFields);
        } else {
            \common\library\custom_field\Helper::updateField($clientId, $leadDisableFields);
        }
        
        $privilegeField = new \common\library\privilege_v3\PrivilegeField($clientId);
        $privilegeField->flushCache();
    }

    /**
     * @param $clientId
     * @param $leadId
     * @return int|mixed
     */
    public static function getMaxCustomerOrderRank($clientId, $leadId) {

        if (!$leadId) {

            return 1;
        }

        $leadCustomerList = new LeadCustomerList($clientId);

        $leadCustomerList->setLeadId($leadId);

        $leadCustomerList->setFields('max(order_rank) as order_rank');

        $leadCustomerList->setOrder('');

        $leadCustomerList->setOrderBy('');

        return ($leadCustomerList->find())[0]['order_rank'] ?? 1;
    }

	/**
	 * @throws \ProcessException
	 * @throws \CDbException
	 * @throws \Exception
	 */
	public static function getLeadAllStatistic($clientId, $userId, $params){

		$constantsFields = ['status' => '线索状态', 'origin_list' => '线索来源', 'ai_tags' => '系统标签', 'completed_status' => '已完成线索', 'unread' => '未读线索'];
        $constantsFields = array_map(function ($item) {
            return \Yii::t('field', $item);
        }, $constantsFields);

		$completedStatusMap = [Status::SYS_STATUS_INVALID => '无效', Status::SYS_STATUS_CONVERSION => '已转化'];
        $completedStatusMap = array_map(function ($item) {
            return \Yii::t('field', $item);
        }, $completedStatusMap);

		$aiTagsMap = ['打开了营销', '回复了营销', '点击了链接', '打开了邮件', '回复了邮件'];
        $aiTagsMap = array_map(function ($item) {
            return \Yii::t('field', $item);
        }, $aiTagsMap);

		$statusMap = [Status::SYS_STATUS_NEW => '待处理', Status::SYS_STATUS_COMPLETE_INFO => '完善信息', Status::SYS_STATUS_INITIATIVE_TO_CONTACT => '初步触达', Status::SYS_STATUS_INTERKNIT => '联系互动'];
        $statusMap = array_map(function ($item) {
            return \Yii::t('field', $item);
        }, $statusMap);

		$list = new \common\library\lead_v3\LeadList($userId);
		$list->paramsMapping($params);
		[$where, $sqlParams] = $list->buildParams();

		$db = \PgActiveRecord::getDbByClientId($clientId);

		$result = [];

		foreach ($constantsFields as $key => $name){
			switch ($key) {
				case "status":
					$statisticStatusCountSql = "select status,count(1) as count from tbl_lead where {$where} group by status";
					$statusCountInfo = $db->createCommand($statisticStatusCountSql)->queryAll(true, $sqlParams);
					$data = array_column($statusCountInfo, 'count', 'status');
					$nodes = [];
					$count = 0;
					foreach($data as $id => $item){
						if(isset($statusMap[$id])) {
							$nodes[] = ['id' => $id, 'name' => $statusMap[$id], 'count' => $item];
							$count += $item;
						}
					}
					$result[$key] = ['id' => $key, 'name' => $name, 'count' => $count, 'node' => $nodes];
					break;
				case "origin_list":
					$client = Client::getClient($clientId);
					$mode = $client->getExtentAttributes()[Client::EXTERNAL_KEY_ORIGIN_SETTING_SORT_MODE] ?? OriginMetadata::USER_SORT_MODE;

					$show_all = ($mode == OriginMetadata::USER_SORT_MODE) ? 1 : 0;
					$originList =  \CustomerOptionService::getOriginTree($clientId, ($show_all ? $userId : 0));
					$statisticOriginSql = "select origin_list, count(1) as count from tbl_lead where {$where} group by origin_list";
					$originInfo = $db->createCommand($statisticOriginSql)->queryAll(true, $sqlParams);
					$nodes = [];
					$sum = 0;
					foreach ($originList as $item) {
						$nodesSub = [];
                        // 先防止一下异常二级来源数据
                        if (!isset($item['origin_id'])) {
                            continue;
                        }
						foreach ($item['node'] ?? [] as $nodeItem) {
							$temp = array_filter($originInfo, function ($v) use ($nodeItem) {
								return in_array($nodeItem['origin_id'], PgsqlUtil::trimArray($v['origin_list'], true));
							});
							$count = 0;
							foreach ($temp as $v){
								$count += $v['count'];
							}
							$nodesSub[] = ['id' => $nodeItem['origin_id'], 'name' => $nodeItem['origin_name'], 'count' => $count ?? 0];
						}
						$temp = array_filter($originInfo, function ($v) use ($item) {
							$temp = array_merge([$item['origin_id']], array_column( $item['node'] ?? [], 'origin_id'));
							return !empty(array_intersect($temp,  PgsqlUtil::trimArray($v['origin_list'], true)));
						});
						$subCount = 0;
						foreach ($temp as $v){
							$subCount += $v['count'];
						}
						$sum += $subCount;
						$nodes[] = ['id' => $item['origin_id'], 'name' => $item['origin_name'], 'count' => $subCount, 'node' => $nodesSub];
					}
					$result[$key] = ['id' => $key, 'name' => $name, 'count' => $sum, 'node' => $nodes];
					break;
				case "ai_tags":
					$sumSql = ' SUM (open_edm_flag) AS "1",SUM (reply_edm_flag) AS "2",SUM (open_edm_url_flag) AS "3",SUM (open_mail_flag) AS "4",SUM (reply_mail_flag) AS "5" ';
					$aiTagsSql = ' and (open_edm_flag=1 OR reply_edm_flag=1 OR open_edm_url_flag=1 OR open_mail_flag=1 OR reply_mail_flag=1) ';
					$statisticAiTagsCountSql = "SELECT {$sumSql} FROM tbl_lead WHERE {$where} {$aiTagsSql}";
					$data = $db->createCommand($statisticAiTagsCountSql)->queryRow(true, $sqlParams);
					$nodes = [];
					$count = 0;
					foreach($data as $id => $item){
						$nodes[] = ['id' => $id, 'name' => $aiTagsMap[$id - 1], 'count' => $item];
						$count += $item;
					}
					$result[$key] = ['id' => $key, 'name' => $name, 'count' => $count, 'node' => $nodes];
					break;
				case "completed_status":
					$completedParams = $params;
					$completedStatusList = new \common\library\lead_v3\LeadList($userId);
					$completedStatusList->setSkipFilterStatus(true);
					$completedParams['status_id'] = [\common\library\setting\library\status\Status::SYS_STATUS_INVALID,\common\library\setting\library\status\Status::SYS_STATUS_CONVERSION];
					$completedStatusList->paramsMapping($completedParams);
					[$where, $sqlParams] = $completedStatusList->buildParams();
					$statisticStatusCountSql = "select status,count(1) as count from tbl_lead where {$where} group by status";
					$statusCountInfo = $db->createCommand($statisticStatusCountSql)->queryAll(true, $sqlParams);
					$data = array_column($statusCountInfo, 'count', 'status');
					$nodes = [];
					$count = 0;
					foreach($data as $id => $item){
						$nodes[] = ['id' => $id, 'name' => $completedStatusMap[$id], 'count' => $item];
						$count += $item;
					}
					$result[$key] = ['id' => $key, 'name' => $name, 'count' => $count, 'node' => $nodes];
					break;
				case "unread":
					$unreadParams = $params;
					$unreadList = new \common\library\lead_v3\LeadList($userId);;
					$unreadParams['read_flag'] = 0;
					$unreadParams['status_id'] = [\common\library\setting\library\status\Status::SYS_STATUS_NONE,\common\library\setting\library\status\Status::SYS_STATUS_NEW, \common\library\setting\library\status\Status::SYS_STATUS_COMPLETE_INFO,\common\library\setting\library\status\Status::SYS_STATUS_INITIATIVE_TO_CONTACT, \common\library\setting\library\status\Status::SYS_STATUS_INTERKNIT];
					$unreadList->paramsMapping($unreadParams);
					$count = $unreadList->count();
					$result[$key] = ['id' => $key, 'name' => $name, 'count' => $count, 'node' => []];
					break;
				default:
					break;
			}
		}

		$data = [];
		$sortFields = ['unread' => '未读线索', 'status' => '线索状态', 'completed_status' => '已完成线索', 'ai_tags' => '系统标签', 'origin_list' => '线索来源'];
		foreach ($sortFields as $key => $item) {
			$data[] = $result[$key];
		}

		return $data;
	}

	public static function findOrigin(array $sourceList) {

		// 默认为 alibaba
		$originId = Origin::SYS_ORIGIN_ALIBABA;

		if (empty($sourceList)) {
			return [$originId];
		}

		$originList = [];


		foreach ($sourceList as $item) {

			if (!isset(Origin::ORIGIN_MAP[strtoupper($item)])) {

				continue;
			}

			$origin = Origin::ORIGIN_MAP[strtoupper($item)];

			$originList[] = $origin;
		}

		return array_values(array_unique(array_filter($originList)));
	}

	public static function telListFormatFullTelList(array $telList){
		$fullTelList = [];
		foreach ($telList as $elem) {
			$areaCode = \Util::numericString($elem[0] ?? '');
			$tel = \Util::numericString($elem[1] ?? '');

			if (empty($areaCode) && empty($tel))
				continue;

			$fullTel = $areaCode . $tel;
			$fullTelList[] = $fullTel;
		}
		return $fullTelList;
	}

    /**
     * 更新线索联系人触达时间
     * @param $clientId
     * @param $userId
     * @param $companyId
     * @param $customerId
     * @param $relateId
     * @param $reachStatus
     * @param $reachStatusTime
     * @param $addReachCount
     * @param $addSuccessCount
     * @param $reachType *mail/edm
     * @return void
     */
    public static function updateLeadCustomerReachStatus($clientId, $userId, $leadIds, $customerIds, $relateId, $reachStatus = null, $updateReachStatusTime = false, $addReachCount = false, $addSuccessCount = false, $reachType = 'mail')
    {
        if (empty($customerIds)) return;
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $set = '';
        if ($reachStatus !== null) {
            $set .= ",reach_status = $reachStatus";
        }

        if ($updateReachStatusTime) {
            $reachStatusTime = date('Y-m-d H:i:s');
            $set .= ",reach_status_time = '$reachStatusTime'";
        }

        if ($addReachCount) {
            $set .= ",reach_count = reach_count + 1";
        }

        if ($addSuccessCount) {
            $set .= ",reach_success_count = reach_success_count + 1";
        }

        $subWhere = '';
        if ($userId)
        {
            if ($reachType == 'mail') {
                //将非客户跟进人与客户联系人的往来邮件生成客户动态的配置开关-关闭后要检查跟进人
                $dynamicTrailCheck = \common\library\customer\Helper::DynamicTrailCheck($clientId);
                if (!$dynamicTrailCheck) {
                    $subWhere .= " and (user_id='{}' or user_id && ARRAY[$userId]::bigint[])";
                }
            } elseif ($reachType == 'edm') {
                //edm只匹配个人私海里面的客户联系人
                $subWhere .= " and user_id && ARRAY[$userId]::bigint[]";
            }



            $timeKey = match($reachStatus) {
                  \common\library\customer_v3\customer\orm\Customer::MAIL_REACH_STATUS_SENDED => 'reach_delivery_time',
                  \common\library\customer_v3\customer\orm\Customer::MAIL_REACH_STATUS_OPEN => 'reach_open_time',
                  default => 'reach_sending_time'
              };
              $buildSet = \common\library\customer\Helper::buildUserData('user_data', $userId, [$timeKey => date('Y-m-d H:i:s')]);
              $set .= ",user_data = $buildSet";
        }

        $sql = "update tbl_lead_customer set reach_relate_id=$relateId $set where  customer_id in (" . implode(',', $customerIds) . ")  and client_id=$clientId $subWhere";
        LogUtil::info('【updateLeadCustomerReachStatus】 addReachCount='.$addReachCount.'addSuccessCount='.$addSuccessCount.'reachStatus='.$reachStatus.' updateReachStatusTime='.$updateReachStatusTime.' set='.$set.'relate_id='.$relateId.'subWhere='.$subWhere.'leadCustomerIds='.json_encode($customerIds).'leadIds='.json_encode($leadIds));

        $db->createCommand($sql)->execute();
        //更新ES
        \common\library\server\es_search\SearchQueueService::pushLeadQueue(PrivilegeService::getInstance($clientId)->getAdminUserId(), $clientId, $leadIds, \Constants::SEARCH_INDEX_TYPE_UPDATE);
    }

    /**
     * 获取分层行动建议
     * @param array $hasMaPermission
     * @param int $layerId
     * @return array|int[]
     */
    public static function getLeadLayerSuggestionActions(array $permissionList = [], int $layerId = 0, int $opUserId = 0)
    {
        if ($layerId) {
            $result = ['layer_id' => $layerId];
            $suggestionActions = Constant::LAYER_SUGGESTION_ACTION_MAP[$layerId];

            // immediate_marketing 特殊权限判断
            if ($layerId == Constant::LAYER_ID_COMMON) {
                $filterItem = $permissionList['hasMaPermission'] ? 'immediate_marketing' : 'write_email';
                foreach ($suggestionActions as $action => $status)
                {
                    if ($action != $filterItem) {
                        unset($suggestionActions[$action]);
                    }
                }
            }

            // 转化客户禁用判断
            if ($layerId == Constant::LAYER_ID_PREMIUM) {
                foreach ($suggestionActions as $action => $status)
                {
                    if ($action === 'convert_company') {
                        $lockKeyArr = [
                            'query_params' => [
                                'layer_id' => Constant::LAYER_ID_PREMIUM
                            ],
                            'module' => 'lead',
                            'type' => 'transform_lead',
                            'method' => null,
                            'operator' => $opUserId
                        ];
                        $searchExportKey = md5(serialize($lockKeyArr));
                        if (\Yii::app()->redis->get($searchExportKey)) {
                           $suggestionActions[$action] = \common\library\lead\Constant::DISABLE_STATUS;
                        }

                    }


                    if ($action === 'fail_lead') {
                        if (!$permissionList['isProAIFlag']) {
                            unset($suggestionActions[$action]);
                        }
                    }
                }
            }

            $result['actions'] = $suggestionActions;
            return $result;
        }

        $result = [];
        $allLayers = Constant::LAYER_ID_MAP;
        foreach ($allLayers as $layerId) {
            $result[] = self::getLeadLayerSuggestionActions($permissionList, $layerId, $opUserId);
        }
        return $result;
    }

    public static function getLeadLayerBestRecommends($clientId, $userId, int $layerId = 0)
    {
        $list = new \common\library\lead_v3\LeadList($userId);
        $list->setClientId($clientId);
        $list->setIsArchive(\common\library\lead\Lead::LEAD_TYPE_ARCHIVE);

        $params = [
            'show_all' => 1,
            'user_num' => [1],
        ];

        $latestSevenDays = date('Y-m-d', strtotime('-7 days'));

        $filters = [
            [
                'field'      => 'send_mail_time',
                'operator'   => WorkflowConstant::FILTER_OPERATOR_LATER,
                'value'      => $latestSevenDays,
                'refer_type' => Constants::TYPE_LEAD,
                'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
            ],
            [
                'field'      => 'latest_edm_time',
                'operator'   => WorkflowConstant::FILTER_OPERATOR_LATER,
                'value'      => $latestSevenDays,
                'refer_type' => Constants::TYPE_LEAD,
                'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
            ],
            [
                'field'      => 'latest_send_ali_tm_time',
                'operator'   => WorkflowConstant::FILTER_OPERATOR_LATER,
                'value'      => $latestSevenDays,
                'value_type' => 'specific',
                'refer_type' => Constants::TYPE_LEAD,
                'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
            ],
            [
                'field'      => 'latest_write_follow_up_time',
                'operator'   => WorkflowConstant::FILTER_OPERATOR_LATER,
                'value'      => $latestSevenDays,
                'value_type' => 'specific',
                'refer_type' => Constants::TYPE_LEAD,
                'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
            ],
        ];

        $params['criteria_type'] = WorkflowConstant::CRITERIA_TYPE_OR;
        $params['filters'] = $filters;

        $list->paramsMapping($params);
        $list->setLayerId($layerId);
        $list->setOrderBy('order_time');
        $list->setOrder('desc');
        $list->setLimit(3);

        $list->getFormatter()->setSpecifyFields(['lead_id', 'name']);

        return $list->find();
    }

    /**
     * 查询自动填充开关
     * @param $clientId
     * @param $userId
     * @return bool
     */
    public static function  queryLeadAutoFilledSwitch($clientId, $userId)
    {
        $setting = new UserSetting($clientId, $userId,UserSetting::LEAD_LAYER_AUTO_FILLED_SWITCH);

        if (isset($setting->enable_flag) && $setting->enable_flag == 0) {
            return false;
        }

        $value = $setting->getValue();

        if ($value === null) {
            return false;
        }

        return $value == 1;
    }

    public static function updatePlanCount($clientId, $customerIds)
    {
        $customerIds = implode(',', $customerIds);
        $sql = "update tbl_lead_customer set plan_count = plan_count + 1 where client_id =:client_id and customer_id in ({$customerIds})";
        $db = \PgActiveRecord::getDbByClientId($clientId);
        return $db->createCommand($sql)->execute([':client_id' => $clientId]);
    }

	/**
	 * @throws \ProcessException
	 * @throws \Exception
	 */
	public static function updateLeadInfo($client_id, $lead_id, $data): void
	{

		$lead = new Lead($client_id, $lead_id);
		$lead->tel_full = $data['tel'] ?: $lead->tel_full;
		$lead->tel = $data['tel'] ?: $lead->tel;
		$lead->name = $data['name'] ?: $lead->name;
		$lead->company_name = $data['name'] ?: $lead->company_name;
		$lead->main_customer_email = $data['email'] ?: $lead->main_customer_email;
		$lead->inquiry_message = $data['message'] ?: $lead->inquiry_message;

		$list = new LeadCustomerList($client_id);
		$list->setFields(['customer_id']);
		$list->setEntityId($lead_id);
		$customerLeadIds = array_column($list->find(), 'customer_id');
		/**
		 * @var LeadCustomer $leadCustomer
		 */
		$customers = [];
		foreach ($customerLeadIds as $customerLeadId) {
			$leadCustomer = new LeadCustomer($client_id, $customerLeadId);
			if(empty($leadCustomer->main_customer_flag)){
				$customers[] = $leadCustomer;
				continue;
			}

			$leadCustomer->email = $data['email'] ?: $leadCustomer->email;

			$customerTelList = $leadCustomer->tel_list ?? [];
			if (!empty($data['tel'])) {
				$insertFlag = true;
				foreach ($customerTelList as $existTelList) {
					$existTelListStr = implode('', $existTelList);
					if ($existTelListStr == $data['tel']) {
						$insertFlag = false;
					}
				}
				if ($insertFlag) {
					$customerTelList[] = ['', $data['tel']];
				}
			}
			$leadCustomer->tel_list = $customerTelList;
			$contacts = $leadCustomer->contact ?? [];
			if (!empty($data['contact'])) {
				foreach ($data['contact'] as $c) {
					$exist = false;
					if($c['value'] || $c['type']){
						continue;
					}
					foreach ($contacts as &$contact) {
						if ($contact['type'] == $c['type']) {
							$exist = true;
							$contact['value'] = $c['value'] ?: $contact['value'];
						}
					}
					if (!$exist) {
						$contacts[] = $c;
					}
				}

			}
			$leadCustomer->contact = $contacts;
			$customers[] = $leadCustomer;
		}

		$lead->setCustomerList($customers);
		$lead->save();
	}
    
    public static function buildUpdateScopeUsers(int $clientId, array $row, $returnAsPgStr = false): string|array|null
    {
        $scopeUserService = new ScopeUserFieldService($clientId, new LeadMetadata($clientId));
        $result = $scopeUserService->buildScopeUserIds([$row]);
        LogUtil::info('[buildUpdateScopeUsersSql] lead_scope_user_ids, result: ' . json_encode($result));
        
        $scopeUsers = reset($result)['scope_user_ids'] ?? null;
        
        if (is_null($scopeUsers))    {
            return null;
        }
        
        return $returnAsPgStr ? "'" . PgsqlUtil::formatArray($scopeUsers) . "'" : $scopeUsers;
    }



    /**
     * 更新latest_whatsapp_time
     * @param $clientId
     * @param $leadId
     * @param $sendTime
     * @param $receiveTime
     * @return false|int
     * @throws \ProcessException
     */
    public static function updateLatestWhatsappTime($clientId, $leadId, $sendTime, $receiveTime): bool|int
    {
        $now = date('Y-m-d H:i:s');
        $latestWhatsappTime = max($sendTime, $receiveTime);
        $set = [];
        $params = [];
        if ($latestWhatsappTime && $latestWhatsappTime <= $now) {
            $set['latest_whatsapp_time'] = "latest_whatsapp_time=(CASE
                     WHEN latest_whatsapp_time > :latest_whatsapp_time THEN latest_whatsapp_time
                     ELSE :latest_whatsapp_time
                END)";
            $params[':latest_whatsapp_time'] = $latestWhatsappTime;
        }
        if ($receiveTime && $receiveTime <= $now) {
            $set['latest_whatsapp_receive_time'] = "latest_whatsapp_receive_time=(CASE
                     WHEN latest_whatsapp_receive_time > :latest_whatsapp_receive_time THEN latest_whatsapp_receive_time
                     ELSE :latest_whatsapp_receive_time
                END)";
            $params[':latest_whatsapp_receive_time'] = $receiveTime;
        }

        if ($sendTime && $sendTime <= $now) {
            $set['latest_whatsapp_send_time'] = "latest_whatsapp_send_time=(CASE
                     WHEN latest_whatsapp_send_time > :latest_whatsapp_send_time THEN latest_whatsapp_send_time
                     ELSE :latest_whatsapp_send_time
                END)";
            $params[':latest_whatsapp_send_time'] = $sendTime;
        }

        if (empty($set)) {
            return 0;
        }

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $setSql = implode(',', $set);

        $sql = "update tbl_lead set {$setSql} where lead_id = :lead_id and client_id=:client_id";
        $params[':lead_id'] = $leadId;
        $params[':client_id'] = $clientId;

        $res = $db->createCommand($sql)->execute($params);

        return $res;
    }

}
