<?php
/**
 * Created by PhpStorm.
 * User: troy.li
 * Date: 2018-04-02
 * Time: 3:07 PM
 */

namespace common\library\lead;

use common\components\BaseObject;
use common\library\ai\classify\ai_field_data\AIFieldData;
use common\library\ai\classify\ai_field_data\LeadCustomerAIFieldData;
use common\library\custom_field\company_field\duplicate\FieldUniqueValidator;
use common\library\email_identity\sync\LeadSync;
use common\library\history\lead\CustomerCompare;
use common\library\history\lead\CustomerEditCompare;
use common\library\history\lead\LeadBuilder;
use common\library\lead_v3\LeadWriteService;
use common\library\privilege_v3\field\BaseObjectPrivilegeField;
use common\library\privilege_v3\field\PrivilegeFieldService;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\report\error\ErrorReport;
use common\library\server\es_search\SearchQueueService;
use common\library\util\PgsqlUtil;
use common\library\util\SqlBuilder;
use common\library\validation\ValidationException;
use common\library\version\LeadVersion;
use common\models\client\LeadHistory;

/**
 * Class LeadCustomer
 * @package common\library\lead
 * @property string $customer_id
 * @property string $client_id
 * @property array $user_id
 * @property string $lead_id
 * @property string $name
 * @property string $email
 * @property integer $main_customer_flag
 * @property integer $post_grade
 * @property string $post
 * @property string $tel
 * @property string $tel_area_code
 * @property string $tel_full
 * @property string $tel_list
 * @property string $full_tel_list
 * @property integer $is_archive
 * @property string $birth
 * @property integer $gender
 * @property string $contact
 * @property string $group_id
 * @property string $tag
 * @property string $user_data
 * @property string $image_list
 * @property string $external_field_data
 * @property string $remark
 * @property string $create_time
 * @property string $update_time
 * @property string $archive_time
 * @property string $order_time
 * @property string $edm_time
 * @property string $send_mail_time
 * @property string $receive_mail_time
 * @property string $mail_time
 * @property integer $growth_level
 * @property integer $order_rank
 * @property integer $reach_count
 * @property integer $reach_status
 * @property string $reach_status_time
 * @property integer $reach_success_count
 * @property integer $reach_relate_id
 * @property integer $suspected_invalid_email_flag
 *
 * @method LeadCustomerFormatter getFormatter()
 */
class LeadCustomer extends BaseObject
{
    use BaseObjectPrivilegeField;

    //图片数量限制
    const IMAGE_LIMIT = 5;

    protected static $_requiredEmail = false;
    protected static $_hasSingleTel = false;

    protected $_allowDuplicateTel = true;
    protected $_updateSearchIndex = true;
    protected $_allowDuplicateEmail = true;

    protected $_clientId;
    protected $_isValidated = false;
    protected $_operatorUserId;

    protected $_fieldEditType = AIFieldData::FIELD_EDIT_TYPE_BY_USER;//字段更新类型
    protected $_fieldEditReferId;//当更新时需要将相应referId记录到操作历史
    protected $_fieldEditReferType;//referId 对于的类型
    protected $_historyTypeMerge = false;
	protected  $duplicateCheckScene = Constant::DUPLICATE_SKIP_SCENE;//跳过判重检测，之前一直默认保存的，默认值为true;
	protected $_skipDuplicateCheck = false;//跳过判重检测，客户合并场景使用
	protected $duplicateMessage = [];//['id' => 'email','message' => '邮箱已经存在'];

    //触达状态 与客户联系人共用一套
/*    const REACH_STATUS_DEFAULT = 0;//默认
    const MAIL_REACH_STATUS_SENDED = 101;//邮件送达
    const MAIL_REACH_STATUS_OPEN = 102;//邮件打开

    const EDM_REACH_STATUS_SENDED = 201;//EDM送达
    const EDM_REACH_STATUS_OPEN = 202;//EDM打开*/

    /**
     * Customer constructor.
     *
     * @param int|null $id
     * @param int|null $clientId
     *
     * @throws \ProcessException
     */
    public function __construct($clientId, $id = null)
    {
        $this->_clientId = $clientId;

        $this->_formatter = new LeadCustomerFormatter($clientId);
        $this->_formatter->setNeedStrip(false);

        if ($id) {
            $this->loadById($id);
        }
        
        $this->setSkipPrivilegeField(false); // 保持旧逻辑，默认为false
    }

    public function getModelClass()
    {
        return \LeadCustomer::class;
    }

    public function setOperatorUserId($userId)
    {
        $this->_operatorUserId = $userId;
    }

    public function setFieldEditType($type)
    {
        $this->_fieldEditType = $type;
    }

    public function setFieldEditRefer($referId, $referType)
    {
        $this->_fieldEditReferId = $referId;
        $this->_fieldEditReferType = $referType;
    }

    public function setHistoryTypeMerge($flag)
    {
        $this->_historyTypeMerge = $flag;
    }

	public function setSkipDuplicateCheck(bool $bool)
	{
		$this->_skipDuplicateCheck = $bool;
	}

	public function setDuplicateCheckScene($flag)
	{
		$this->duplicateCheckScene = $flag;
	}

	public function getDuplicateCheckScene()
	{
		return $this->duplicateCheckScene;
	}

	public function setDuplicateMessage(array $message)
	{
		$this->duplicateMessage = $message;
	}

	public function getDuplicateMessage()
	{
		return $this->duplicateMessage;
	}

    public function isExist()
    {
        return !$this->isNew() && $this->is_archive;
    }

    public function loadById($id)
    {
        $model = static::getModelClass()::model()->find('customer_id=:id and client_id=:client_id',
            [':id' => $id, ':client_id' => $this->_clientId]);

        $this->setModel($model);

        return $this;
    }

    public function loadByTel($tel)
    {
        $tel = is_array($tel) ? array_filter($tel) : $tel;
        if (empty($tel)) {
            $this->setModel(null);
            return $this;
        }

        if (is_array($tel)) {
            $model = static::getModelClass()::model()->find('client_id=:client_id and full_tel_list && ARRAY[:tel]::text[] and is_archive=1 AND full_tel_list <> \'{}\'::text[] order by create_time asc',
                [':client_id' => $this->_clientId, ':tel' => implode(',', $tel)]);
        } else {
            // is_archive=1是为了使用索引 理论上没有什么实际影响 使用层需要注意
            $model = static::getModelClass()::model()->find('client_id=:client_id and full_tel_list @> ARRAY[:tel]::text[] and is_archive=1 AND full_tel_list <> \'{}\'::text[] order by create_time asc',
                [':client_id' => $this->_clientId, ':tel' => $tel]);
        }


        // 老数据没有跑脚本迁移 代码层兼容 在数据迁移完成后可以去除
        if (!$model && !is_array($tel))
            $model = static::getModelClass()::model()->find('client_id=:client_id and tel_full=:tel and is_archive=1 order by create_time asc',
                [':client_id' => $this->_clientId, ':tel' => $tel]);


        $this->setModel($model);

        return $this;
    }

	/**
	 * @throws \ProcessException
	 */
	public function validate()
    {
        $clientId = $this->_clientId;

        $this->_attributes['email'] = strtolower(trim($this->_attributes['email'] ?? ''));
        if (static::$_hasSingleTel) {
            $this->_attributes['tel'] = trim($this->_attributes['tel'] ?? '');
            $this->_attributes['tel_area_code'] = trim($this->_attributes['tel_area_code'] ?? '');
            $this->_attributes['tel_full'] = $this->_attributes['tel_area_code'] . $this->_attributes['tel'];
        }

        if (\common\library\customer\blacklist\Helper::match($clientId, $this->_attributes['email'])) {
            throw new \RuntimeException($this->_attributes['email'] . "在建档黑名单中");
        }

        if ($this->isNew()) {
            $email = $this->_attributes['email'];

            $checkCustomer = (new static($clientId))->loadByEmail($email);

            if (static::$_requiredEmail && $checkCustomer->isExist())
                throw new \RuntimeException($this->_attributes['email'] . ' 邮箱已存在');
        } else {
            if (static::$_requiredEmail) {
                if (!empty($this->_oldAttributes['email']) && strcasecmp($this->_oldAttributes['email'], $this->_attributes['email']))
                    throw new \ProcessException("不能修改已经存在的customer的email");
            }
        }

        //		contact格式校验
        //线索转客户通过校验
        if (($this->_attributes['is_archive'] ?? 0) == Lead::ARCHIVE_OK) {
            if (!empty($this->_attributes['contact']) && $this->_attributes['contact'] != \common\library\object\field\FieldConstant::FIELD_VALUE_MASK) {

                array_walk($this->_attributes['contact'], function ($item) {

                    if (empty($item['type']) || empty($item['value']) || is_array($item['type']) || is_array($item['value'])) {

                        throw new \RuntimeException('社交平台格式错误');
                    }
                });
            }
        }

        // 处理电话
        $this->_attributes['tel_list'] = $this->_attributes['tel_list'] ?? [];
        // 兼容前端提交错误的情况
        if (!is_array($this->_attributes['tel_list'])) {
            \LogUtil::warning("tel_list value type error . " . var_export($this->_attributes['tel_list'], true));
            $this->_attributes['tel_list'] = empty($this->_attributes['tel_list']) ? [] : [['', $this->_attributes['tel_list']]];
        }

        $telList = [];
        $fullTelList = [];

        foreach ($this->_attributes['tel_list'] as $elem) {
            $areaCode = \Util::numericString($elem[0] ?? '');
            $tel = \Util::numericString($elem[1] ?? '');

            if (empty($areaCode) && empty($tel))
                continue;

            $fullTel = $areaCode . $tel;

            $telList[] = [$areaCode, $tel];
            $fullTelList[] = $fullTel;
        }

        $this->_attributes['tel_list'] = $telList;
        $this->_attributes['full_tel_list'] = $fullTelList;

        if (count($this->_attributes['tel_list']) > 10)
            throw new \RuntimeException(\Yii::t('customer', 'No more than {num} contact numbers', ['{num}' => 10]));

        $diffTelList = array_diff($fullTelList, $this->_oldAttributes['full_tel_list'] ?? []);

		if (!$this->_skipDuplicateCheck) {
			if (($this->_attributes['is_archive'] ?? 1) && in_array($this->duplicateCheckScene, [Constant::DUPLICATE_NORMAL_SCENE, Constant::DUPLICATE_AUTO_SCENE, Constant::DUPLICATE_LEADS_SCENE])) {
				$this->duplicateValidate();
			}
		}

        if (!$this->_allowDuplicateTel && !empty($diffTelList)) {
            foreach ($diffTelList as $fullTel) {
                $checkCustomer = (new LeadCustomer($clientId))->loadByTel($fullTel);
                if ($checkCustomer->isExist() && $checkCustomer->lead_id != $this->lead_id) {
                    if ($checkCustomer->isExist())
                        throw new \RuntimeException($fullTel . '号码已建档');


                    if (!$checkCustomer->isNew() && !$checkCustomer->is_archive) {
                        $checkCustomer->tel = '';
                        $checkCustomer->save();
                    }
                    //已建档的联系人电话不可以被另一个客户座机建档
                    $checkCompany = (new Lead($clientId))->loadByTel($fullTel);
                    if ($checkCompany->isExist() && $checkCompany->lead_id != $this->lead_id)
                        throw new \RuntimeException(\Yii::t('customer', 'The deskphone already exists, please re-enter'));

                    if (!$checkCompany->isNew() && !$checkCompany->is_archive) {
                        $checkCompany->tel = '';
                        $checkCompany->save();
                    }

                }
            }

        }

        // 这个别乱移 一定要放在最后面
        $this->_isValidated = true;
    }

    public function beforeSave()
    {
        //$this->handlePrivilegeFields();
        $this->handlePrivilegeFieldsByWriteBack();

        if (!$this->_isValidated)
            $this->validate();


        if ($this->_attributes['is_archive'] && empty($this->_attributes['lead_id']))
            throw new \RuntimeException(\Yii::t('customer', 'Company id could not be empty'));

        if (static::$_requiredEmail && empty($this->_attributes['email']))
            throw new \RuntimeException(\Yii::t('customer', 'Email could not be empty'));

        //不仅仅 前端传入数据需要处理，数据导入等业务也可能会调用 此处需要对image_list的数据格式过滤
        $this->_attributes['image_list'] = empty($this->_attributes['image_list']) ? [] : $this->_attributes['image_list'];


        $date = date('Y-m-d H:i:s');
        $this->_attributes['update_time'] = $date;
        
        if ($this->isNew()) {
            $this->_attributes['is_archive'] = $this->_attributes['is_archive'] ?? 1;
            $this->_attributes['post_grade'] = $this->_attributes['post_grade'] ?? 0;
            $this->_attributes['create_time'] = $this->_attributes['create_time'] ?? $date;
            $this->_attributes['user_data'] = $this->_attributes['user_data'] ?? new \stdClass();
            $this->_attributes['user_id'] = $this->_attributes['user_id'] ?? [];
            $this->_attributes['contact'] = $this->_attributes['contact'] ?? [];
            $this->_attributes['external_field_data'] = $this->_attributes['external_field_data'] ?? [];
            $this->_attributes['tag'] = $this->_attributes['tag'] ?? [];
            $this->_attributes['image_list'] = $this->_attributes['image_list'] ?? [];
            $this->_attributes['gender'] = $this->_attributes['gender'] ?? 0;

            if ($this->_attributes['is_archive']) {
                $this->_attributes['archive_time'] = $this->_attributes['create_time'] ?? $date;
                $this->_attributes['order_time'] = $this->_attributes['create_time'] ?? $date;
            }
        } else {
            if ($this->_attributes['is_archive'] && !$this->_oldAttributes['is_archive']) {
                $this->_attributes['archive_time'] = $date;
            }
        }

        //图片数量检测
        if (!empty($this->_attributes['image_list'])) {
            if (count($this->_attributes['image_list']) > self::IMAGE_LIMIT) {
                throw new \RuntimeException(\Yii::t('customer', 'Contact pictures can only be saved up to {num}', ['{num}' => 5]));
            }
        }

        // 清空这里面的数据
        //$this->_attributes['tel'] = \Util::numericString($this->_attributes['tel']);
        //$this->_attributes['tel_area_code'] = \Util::numericString($this->_attributes['tel_area_code']);
        //$this->_attributes['tel_full'] = $this->_attributes['tel_area_code'] . $this->_attributes['tel'];
        if (static::$_hasSingleTel) {
            $this->_attributes['tel'] = '';
            $this->_attributes['tel_area_code'] = '';
            $this->_attributes['tel_full'] = '';
        }

        $this->_attributes['user_id'] = PgsqlUtil::formatArray($this->_attributes['user_id']);
        $this->_attributes['contact'] = empty($this->_attributes['contact']) ? '{}' : json_encode($this->_attributes['contact']);
        $this->_attributes['external_field_data'] = empty($this->_attributes['external_field_data']) ? '{}' : json_encode($this->_attributes['external_field_data']);
        $this->_attributes['tag'] = empty($this->_attributes['tag']) ? '{}' : json_encode($this->_attributes['tag']);;
        $this->_attributes['user_data'] = empty($this->_attributes['user_data']) ? '{}' : json_encode($this->_attributes['user_data']);
        $this->_attributes['client_id'] = $this->_clientId;
        $this->_attributes['gender'] = intval($this->_attributes['gender']);
        $this->_attributes['post_grade'] = intval($this->_attributes['post_grade']);
        $this->_attributes['main_customer_flag'] = (isset($this->_attributes['main_customer_flag']) && $this->_attributes['main_customer_flag']) ? 1 : 0;
        $this->_attributes['image_list'] = PgsqlUtil::formatArray(array_filter($this->_attributes['image_list']));
        $this->_attributes['full_tel_list'] = PgsqlUtil::formatArray($this->_attributes['full_tel_list']);
        $this->_attributes['tel_list'] = empty($this->_attributes['tel_list']) ? '{}' : json_encode($this->_attributes['tel_list']);

        $this->_attributes['is_archive'] = $this->_attributes['is_archive'] ?? 1;

        return parent::beforeSave();
    }

    public function afterSave()
    {
        parent::afterSave();

        $extraData = [
            'lead_id' => $this->_attributes['lead_id'],
            'customer_id' => $this->_attributes['customer_id'],
        ];

        if ($this->_fieldEditReferId) {
            $extraData['refer_id'] = $this->_fieldEditReferId;
            $extraData['refer_type'] = $this->_fieldEditReferType;
            $extraData['user_id'] = $this->_operatorUserId;
        }

        if (($this->isNew() || !$this->_oldAttributes['is_archive']) && $this->_attributes['lead_id']) {
            $history = new CustomerCompare($this->_clientId);
            $history->setEditInfo($this->_fieldEditType);
            $history->setExtraData($extraData);
            $history->setType(LeadHistory::TYPE_NEW_CUSTOMER);
            $history->setData($this->_attributes, []);
            $history->build($this->_operatorUserId);
        } else {

            if (!$this->isNew() && $this->_attributes['lead_id']) {

                $historyType = $this->_historyTypeMerge ? LeadHistory::TYPE_MERGE_LEAD_CUSTOMER_INFO : LeadHistory::TYPE_EDIT_CUSTOMER;

                $history = new CustomerEditCompare($this->_clientId);
                $history->setEditInfo($this->_fieldEditType);
                $history->setExtraData($extraData);
                $history->setType($historyType);
                $history->setData($this->_attributes, $this->_oldAttributes);
                $history->build($this->_operatorUserId);
            } else {
                if (!$this->isNew() && !$this->_attributes['is_archive'] && $this->_oldAttributes['is_archive']) {
                    $history = new CustomerCompare($this->_clientId);
                    $history->setEditInfo($this->_fieldEditType);
                    $history->setExtraData($extraData);
                    $history->setType(LeadHistory::TYPE_DELETE_CUSTOMER);
                    $history->setData($this->_attributes, []);
                    $history->build($this->_operatorUserId);
                }
            }

        }

        // 保存线索联系人变更历史到ai更新表
        if ($history && !empty($compareResult = $history->getProductDiffData())) {
            $compareFields = array_column($compareResult, 'id');
            $aiFieldDataClass = new LeadCustomerAIFieldData($this->_clientId, $this->customer_id);
            $aiFieldDataClass->setFieldDataByType($compareFields, $this->_fieldEditType);
            $aiFieldDataClass->save();
        }
        //同步邮箱身份
        if (
            $this->isNew() ||
            $this->_attributes['is_archive'] != $this->_oldAttributes['is_archive'] ||
            $this->_attributes['user_id'] != $this->_oldAttributes['user_id']
        ) {
            (new LeadSync($this->_clientId))->loadDataFromEmail([$this->email])->sync();
        }

        try {
            // 更新customer email id
            \common\library\email\Helper::updateLeadCustomerEmailId($this->client_id, $this->_attributes['customer_id'], $this->_attributes['email']);
        } catch (\Exception $e) {
            \LogUtil::error($e->getMessage());
            ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace(), $this->user_id);
        }

        if ($this->isNew()) {
            // 新建线索联系人
            $leadVersion = new LeadVersion($this->client_id, $this->lead_id);
            $leadVersion->setType(\common\library\version\Constant::LEAD_MODULE_ADD_CUSTOMER);
            $leadVersion->add();;
        } elseif ($this->_attributes['is_archive'] == 0 && $this->_oldAttributes['is_archive'] != 0) {
            // 删除线索联系人
            $leadVersion = new LeadVersion($this->client_id, $this->_oldAttributes['lead_id']);
            $leadVersion->setType(\common\library\version\Constant::LEAD_MODULE_DELETE_CUSTOMER);
            $leadVersion->add();;
        } elseif ($this->_attributes['is_archive'] != 0 && $this->_oldAttributes['is_archive'] == 0) {
            // 恢复线索联系人
            $leadVersion = new LeadVersion($this->client_id, $this->lead_id);
            $leadVersion->setType(\common\library\version\Constant::LEAD_MODULE_RECOVER_CUSTOMER);
            $leadVersion->add();;
        } elseif (($this->_attributes['name'] != $this->_oldAttributes['name']) || ($this->_attributes['email'] != $this->_oldAttributes['email'])) {
            // 编辑线索联系人[名称 邮箱]
            $leadVersion = new LeadVersion($this->client_id, $this->lead_id);
            $leadVersion->setType(\common\library\version\Constant::LEAD_MODULE_EDIT_CUSTOMER);
            $leadVersion->add();;
        }
    }

    /**
     * @param bool $allowRemoveMain
     */
    public function remove($allowRemoveMain = false)
    {
        if (!$allowRemoveMain && $this->_attributes['main_customer_flag'])
            throw new \RuntimeException(\Yii::t('customer', 'Cannot delete primary contact'));

        $this->_attributes['is_archive'] = 0;
        $this->_attributes['lead_id'] = 0;
        $this->_attributes['main_customer_flag'] = 0;
        $this->_attributes['user_id'] = [];

        $result = $this->save();

        if (!$allowRemoveMain) {
            SearchQueueService::pushLeadQueue($this->user_id, $this->client_id, [$this->_attributes['lead_id']],
                \Constants::SEARCH_INDEX_TYPE_DELETE);
        }

        if ($result) {
            (new LeadSync($this->_clientId))->loadDataFromEmail([$this->email])->sync();
        }

        return $result;
    }

    public function archive($company_id, $customer_id)
    {
        $this->_attributes['company_id'] = $company_id;
        $this->_attributes['company_customer_id'] = $customer_id;
        $this->_attributes['is_archive'] = Lead::ARCHIVE_HIDDEN;
        $result = $this->save();
        if ($result) {
            (new LeadSync($this->_clientId))->loadDataFromEmail([$this->email])->sync();
        }
        return $result;
    }

    public function loadByEmail($email, $leadId = 0)
    {
        $email = strtolower($email);
        if (!empty($email))
        {
            $sql = "client_id=:client_id and email <> '' and is_archive = 1 and lower(email)=:email";
            $params = [':client_id'=>$this->_clientId, ':email'=>$email];

            if ($leadId > 0)
            {
                $sql .= ' and lead_id=:lead_id';
                $params[':lead_id'] = $leadId;
            }

            $model = static::getModelClass()::model()->find($sql,$params);
            $this->setModel($model);
        }

        return $this;
    }

    public function loadEmailsByLeadIds(array $leadIds)
    {
        $params = [':client_id' => $this->_clientId];
        $prepareParams = implode(',', SqlBuilder::buildInWhere($leadIds, $params));

        $sql = "client_id=:client_id and is_archive = 1 and lead_id in ({$prepareParams})";

        $models = static::getModelClass()::model()->findAll($sql, $params);
        $emails = [];

        foreach ($models as $model) {
            $emails[] = $model->email;
        }

        return $emails;
    }

    public function getPrivilegeFieldFunctionalId()
    {
        return (empty($this->user_id) || $this->user_id == '{}') ? PrivilegeConstants::FUNCTIONAL_LEAD_POOL : PrivilegeConstants::FUNCTIONAL_LEAD;
    }

    public function getClientId()
    {
        return $this->_clientId;
    }

    public function getPrivilegeFieldReferType()
    {
        return \Constants::TYPE_LEAD_CUSTOMER;
    }


    public function getPrivilegeFieldUserId()
    {
        return $this->_operatorUserId;
    }

	/**
	 * @throws \ProcessException
	 */
	public function duplicateValidate()
	{

		$this->duplicateMessage = (new FieldUniqueValidator($this->getClientId(), \Constants::TYPE_CUSTOMER, []))
			->setMessageOnly(true)
			->setDuplicateLeadSettingFlag(true)
			->setAttributes($this->_attributes)
			->setFilterMatchRuleMode(\common\library\custom_field\company_field\duplicate\MatchRules::FILTER_MATCH_RULE_MODE_SINGLE)
			->validate();
	}

    public function skipDuplicateField(array $data):array
    {
        if (empty($data)) {
            return [];
        }
        $skipField = [];

        $referIds = $this->isNew() ? [] : [$this->customer_id];
        $attributes = $this->getAttributes();

        //特殊处理电话
        $data['full_tel_list'] = Helper::telListFormatFullTelList($data['tel_list'] ?? []);

        $messages = (new FieldUniqueValidator($this->getClientId(), \Constants::TYPE_CUSTOMER, $referIds))
            ->setMessageOnly(true)
            ->setPreventOnly(true)
            ->setDuplicateLeadSettingFlag(true)
            ->setAttributes(array_replace($attributes, $data))
            ->setFilterMatchRuleMode(\common\library\custom_field\company_field\duplicate\MatchRules::FILTER_MATCH_RULE_MODE_ALL)
            ->validate();

        foreach ($messages as $message) {
            if (!empty($data[$message['field']])) {

                if ($message['field'] == 'tel_list') {
                    unset($data['full_tel_list']);
                }
                $skipField['field'] = $message['field'];
                $skipField['value'] = $data[$message['field']];
                unset($data[$message['field']]);
            }
        }
        return [$data,$skipField];
    }


    /**
     * AI更新线索联系人数据
     * @param array $data
     * @return void
     * @throws \Exception
     */
    public function autFilledLeadCustomerData(array $data) {
        $this->setSkipPrivilegeField(true);
        
        $this->setFieldEditType(AIFieldData::FIELD_EDIT_TYPE_BY_AI);
        foreach ($data as $key => $value) {
            // 仅更新支持AI更新字段
            if (in_array($key, array_keys(LeadCustomerAIFieldData::DEFAULT_FIELD_DATA))) {
                $this->_attributes[$key] = $value;
            }
        }
        $this->save();
    }
}
