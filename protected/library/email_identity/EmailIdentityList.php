<?php
/**
 * Created by PhpStorm.
 * User: beenzhang
 * Date: 2018/6/28
 * Time: 下午2:40
 */

namespace common\library\email_identity;

/**
 * Class EmailIdentityList
 * @package common\library\email_identity
 * @method EmailIdentityFormatter getFormatter()
 */
class EmailIdentityList extends \MysqlList {
    //筛选身份条件
    const EMAIL_TYPE_STRANGER = 1;              //陌生人：不属于上述任一一种情况的
    const EMAIL_TYPE_OWNER_CUSTOMER = 2;        //我的客户：邮箱已存在于我的私海客户中
    const EMAIL_TYPE_PUBLIC_CUSTOMER = 3;       //公海客户：邮箱已存在于公海客户
    const EMAIL_TYPE_CONFLICTS_CUSTOMER = 4;    //冲突客户：邮箱已被同事建档为客户
    const EMAIL_TYPE_OWNER_LEAD = 5;            //我的线索：邮箱已存在于我的线索私海中
    const EMAIL_TYPE_PUBLIC_LEAD = 6;           //公海线索：邮箱已存在于公海线索中，且同时不在我的线索私海中
    const EMAIL_TYPE_CONTACT = 7;               //通讯录联系人：邮箱已存在于通讯录中，且不属于上述情况的

    const EMAIL_TYPE_PRIVATE_CUSTOMER = 100;    //私海客户：有跟进人
    const EMAIL_TYPE_ARCHIVE_CUSTOMER = 101;       //已建档为客户

    protected $userId;
    protected $clientId;
    protected $emails;
    protected $emailType;
    protected $domains;

    protected $db;
    protected $dataReader = false;
    protected $checkPoolDuplicateSwitchFlag = false; //是否检查公共公海分组判重

    protected $fields;

    /**
     * @var EmailIdentityFormatter
     */
    protected $formatter;

    public function __construct($userId)
    {
        $this->userId = $userId;
        $user = \User::getUserObject($userId);
        $this->clientId = $user->getClientId();

        $this->db = \PgActiveRecord::getDbByClientId($this->clientId);

        $this->formatter = new EmailIdentityFormatter($userId);
    }

    public function setEmails($emails){
        $this->emails = is_array($emails) ? $emails : [$emails];
    }

    public function setEmailType($emailType)
    {
        $this->emailType = intval($emailType);
    }


    public function setDomains($domains){
        $this->domains = is_array($domains) ? $domains : [$domains];
    }

    public function setCheckPoolDuplicateSwitchFlag($checkPoolDuplicateSwitchFlag)
    {
        if ($checkPoolDuplicateSwitchFlag && \common\library\customer\pool\Helper::getCheckPoolDuplicateSwitch($this->clientId)) {
            $this->checkPoolDuplicateSwitchFlag = true;
        }
    }

    public function setDataReader($flag)
    {
        $this->dataReader = $flag;
    }

    public function setFields($fields)
    {
        if (is_array($fields)) {
            $fields = implode(',', $fields);
        }

        $this->fields = $fields;
    }

    public function buildParams(){
        $sql = ' client_id = :client_id ';
        $params = [':client_id' => $this->clientId];

        if(!empty($this->emails)){
            $emailStringArray = array_map(function($elem){
                $elem = \Util::escapeDoubleQuoteSql($elem);
                return "'$elem'";
            }, $this->emails);
            $emailString = implode(',', $emailStringArray);
            $sql .= " AND email IN ({$emailString})";
        }

        if (!empty($this->domains)){
            $domainStringArray = array_map(function($elem){
                $elem = \Util::escapeDoubleQuoteSql($elem);
                return "'$elem'";
            }, $this->domains);
            $domainString = implode(',', $domainStringArray);
            $sql .= " AND domain IN ({$domainString})";
        }

        if($this->emailType){
            switch ($this->emailType){
                case self::EMAIL_TYPE_STRANGER:
                    $sql .= " AND customer_archive=0 AND lead_customer_archive=0 AND contact_user = '{}'";
                    break;
                case self::EMAIL_TYPE_OWNER_CUSTOMER:
                    $sql .= " AND customer_user @> ARRAY[:user_id]::bigint[] AND customer_archive=1";
                    break;
                case self::EMAIL_TYPE_PUBLIC_CUSTOMER:
                    $sql .= " AND customer_user = '{}' AND customer_archive=1 ";
                    break;
                case self::EMAIL_TYPE_CONFLICTS_CUSTOMER:
                    $sql .= " AND customer_user != '{}' AND NOT ( customer_user @> ARRAY[{$this->userId}]::bigint[]) AND customer_archive=1";
                    break;
                case self::EMAIL_TYPE_OWNER_LEAD:
                    $sql .= " AND lead_customer_user @> ARRAY[{$this->userId}]::bigint[] AND lead_customer_archive=1";
                    break;
                case self::EMAIL_TYPE_PUBLIC_LEAD:
                    $sql .= " AND lead_customer_public = 1 AND lead_customer_archive=1";
                    break;
                case self::EMAIL_TYPE_CONTACT:
                    $sql .= " AND contact_user && ARRAY[{$this->userId}, 0]::bigint[]";
                    break;
                case self::EMAIL_TYPE_PRIVATE_CUSTOMER:
                    $sql .= " AND customer_user != '{}' AND customer_archive=1 ";
                    break;
                case self::EMAIL_TYPE_ARCHIVE_CUSTOMER:
                    $sql .= " AND customer_archive=1 ";
                    break;
            }

            if ($this->checkPoolDuplicateSwitchFlag && \User::getUserObject($this->userId)->getAdminType() != \common\library\account\UserInfo::ADMIN_TYPE_SUPER) {
                $poolList = \common\library\customer\pool\Helper::getCustomerPoolList();
                if (!empty($poolList)) {
                    $poolList = array_filter(array_column($poolList, 'pool_id'));
                    $sql .= " AND customer_pool_ids && ARRAY[". implode(',', $poolList) ."]::bigint[]";
                }
            }
        }

        return [$sql, $params];
    }

    public function find(){
        list($where, $params) = $this->buildParams();
        $field = $this->fields ?? '*';
        $order = $this->buildOrderBy();
        $limit = $this->buildLimit();

        $sql = "SELECT {$field} FROM tbl_email_identity WHERE {$where} {$order} {$limit}";
        $command = $this->db->createCommand($sql);

        if ($this->dataReader) {
            $list = $command->query($params);
            return $list;
        } else {
            $list = $command->queryAll(true, $params);
        }

        if (empty($this->fields)) {
            $this->formatter->setListData($list);
            $list = $this->formatter->result();
        }

        return $list;
    }

    public function count(){
        list($where, $params) = $this->buildParams();
        $sql = "SELECT count(1) FROM tbl_email_identity WHERE {$where}";
        return $this->db->createCommand($sql)->queryScalar($params);
    }
}
