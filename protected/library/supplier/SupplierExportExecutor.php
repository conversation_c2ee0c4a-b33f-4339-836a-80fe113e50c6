<?php
namespace common\library\supplier;

use common\library\APIConstant;
use common\library\CommandRunner;
use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldList;
use common\library\export_v2\ExportExecutor;
use common\library\export_v2\ExportFile;
use common\library\object\field\FieldConstant;
use common\library\privilege_v3\Helper;
use common\library\privilege_v3\PrivilegeConstants;

class SupplierExportExecutor extends  ExportExecutor
{
    protected $supplierFields= [];
    protected $mainContactFields= [];

    public function runAsync()
    {
        CommandRunner::run('supplierExport', 'export', [
            'user_id' => $this->export->user_id,
            'export_id' => $this->export->export_id
        ], '/tmp/supplier_export.log');
    }
    public function access()
    {
        if(!$this->export->hasExportAccess(PrivilegeConstants::PRIVILEGE_CRM_SUPPLIER_VIEW)) {
            throw new \RuntimeException('当前用户无供应商导出权限');
        }
    }

    public function getExportFilename()
    {
        static $filename = null;

        if(is_null($filename)) {
            $str = \Yii::t('field', '供应商导出');
            $filename = $str.'-'.date('YmdHis').'.xlsx';

        }
        return $filename;
    }

    public function getExportFileHeader()
    {
        static $orderFiles;
        if(is_null($orderFiles)) {
            //获取当前用户的所有定义的字段id  name
            $service = new FieldList($this->export->client_id);
            $service->setType($this->export->type);
            $service->setId($this->export->params['fields']);
            $fields = $service->find();
            if (empty($fields)) {
                throw new \RuntimeException('获取导出文件头部字段错误');
            }

            //字段分组 成供应商 和  主联系人的字段
            foreach ($fields as $field) {
                if (in_array($field['group_id'], [
                    CustomFieldService::SUPPLIER_GROUP_BASIC,
                    CustomFieldService::SUPPLIER_GROUP_ATTACH,
                    CustomFieldService::SUPPLIER_GROUP_SYSTEM
                ])) {
                    $this->supplierFields[] = $field['id'];
                } else {
                    $this->mainContactFields = $field['id'];
                }
            }

            $fieldsMap = array_column($fields, 'name', 'id');
            $orderFiles = [];

            foreach ($this->export->params['fields'] as $field) {
                $orderFiles[$field] = $fieldsMap[$field];
            }
        }

        return $orderFiles;
    }

    public function initExportFile()
    {
        $this->exportFile = new ExportFile();
        $this->exportFile->setFileHeader($this->getExportFileHeader());
        $this->exportFile->setFileName($this->getExportFilename());
        $this->exportFile->setFileType($this->export->file_type);
    }

    public function getExportData()
    {
        \User::setLoginUserById($this->export->user_id);
        $params = $this->export->params['query_params'];
        $params['scene'] = APIConstant::SCENE_EXPORT;
        $api = new SupplierApi($this->getClientId(), $this->getUserId());
        list($data, $_) = $api->getList($params);

        //数据映射
        $dataMap = [];
        $data = Helper::formatDisableField($data, PrivilegeConstants::FUNCTIONAL_PURCHASE_SUPPLIER);
        $replacement = \Yii::t('privilege', FieldConstant::FIELD_VALUE_EXPORT_MASK);
        foreach ($data as $datum) {
            $disableFields = $datum['field_privilege_stats'][0]['disable'] ?? [];
            $temp = [];
            foreach ($this->getExportFileHeader() as $field=>$name) {
                //特殊字段处理
                switch ($field) {
                    case 'user_ids':
                        if ($replacement == $datum['user_ids']) {
                            $userName = $replacement;
                        } else {
                            if (is_array($datum['user']) && isset($datum['user'][0])) {
                                $userNameArr = array_column($datum['user'], 'nickname');
                                $userName = implode(';', $userNameArr);
                            }
                        }
                        $temp['user_ids'] = $userName ?? '';
                        continue 2;
                    case 'rate_id':
                        if ($replacement == $datum['rate_id']) {
                            $temp['rate_id'] = $replacement;
                        } else {
                            $temp['rate_id'] = $datum['rate'];
                        }
                        continue 2;
                    case 'social_platform':
                        if (in_array($field, $disableFields)) {
                            $socialPlatform = $replacement;
                        } else {
                            $socialPlatform = '';
                            if(isset($datum['main_contact']['social_platform']) && is_array($datum['main_contact']['social_platform'])) {
                                $socialPlatformMap = [];
                                foreach ($datum['main_contact']['social_platform'] as  $item) {
                                     $socialPlatformMap[]= $item['platform'].':'.$item['account'];
                                }
                                $socialPlatform = implode(';',$socialPlatformMap);
                            }
                        }
                        $temp['social_platform'] = $socialPlatform;
                        continue 2;
                    case 'archive_user':
                        if ($replacement == $datum['archive_user']) {
                            $temp['archive_user'] = $replacement;
                        } else {
                            $temp['archive_user'] = $datum['archive_user']['nickname'] ?? '';
                        }
                        continue 2;
                    case 'update_user':
                        if ($replacement == $datum['update_user']) {
                            $temp['update_user'] = $replacement;
                        } else {
                            $temp['update_user'] = $datum['update_user']['nickname'] ?? '';
                        }
                        continue 2;
                    case 'delivery_date':
                        if ($replacement == $datum['delivery_date']) {
                            $temp['delivery_date'] = $replacement;
                        } else {
                            $temp['delivery_date'] = $datum['custom_delivery_date'] ?? '';
                        }
                        continue 2;
                    case 'partner_account_name':
                        if (in_array($field, $disableFields)) {
                            $temp['partner_account_name'] = $replacement;
                        } else {
                            $temp['partner_account_name'] = $datum['main_partner_account']['partner_account_name'] ?? '';
                        }
                        continue 2;
                    case 'bank_name':
                        if (in_array($field, $disableFields)) {
                            $temp['bank_name'] = $replacement;
                        } else {
                            $temp['bank_name'] = $datum['main_partner_account']['bank_name'] ?? '';
                        }
                        continue 2;
                    case 'account_name':
                        if (in_array($field, $disableFields)) {
                            $temp['account_name'] = $replacement;
                        } else {
                            $temp['account_name'] = $datum['main_partner_account']['account_name'] ?? '';
                        }
                        continue 2;
                    case 'bank_account':
                        if (in_array($field, $disableFields)) {
                            $temp['bank_account'] = $replacement;
                        } else {
                            $temp['bank_account'] = $datum['main_partner_account']['bank_account'] ?? '';
                        }
                        continue 2;
                    case 'account_image':
                        if (in_array($field, $disableFields)) {
                            $temp['account_image'] = '';
                        } else {
                            $temp['account_image'] = $datum['main_partner_account']['account_image'][0]['file_url'] ?? '';
                        }
                        continue 2;
                }

                //常规字段处理
                if(isset($datum[$field])) {
                    $temp[$field] = $datum[$field];
                    continue;
                }

                if(isset($datum['main_contact'][$field])) {
                    if (in_array($field, $disableFields)) {
                        $temp[$field] = $replacement;
                    } else {
                        $temp[$field] = $datum['main_contact'][$field];
                    }
                    continue;
                }

                //扩展字段处理
                $supplierExternalFileData = $datum['external_field_data'];
                foreach ($supplierExternalFileData as  $supplierExternalFileDatum) {
                    if($supplierExternalFileDatum['id'] == $field) {

                        if($supplierExternalFileDatum['field_type'] == CustomFieldService::FIELD_TYPE_IMAGE || $supplierExternalFileDatum['field_type'] == CustomFieldService::FIELD_TYPE_ATTACH) {
                            $value = implode(';',array_column($supplierExternalFileDatum['value'],'file_url'));
                        }elseif($supplierExternalFileDatum['field_type'] == CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT){
                            $value = is_array($supplierExternalFileDatum['value']) ? implode(';',$supplierExternalFileDatum['value']) : $supplierExternalFileDatum['value'];
                        }else{
                            $value = $supplierExternalFileDatum['value'];
                        }
                        if (in_array($field, $disableFields)) {
                            $temp[$field] = $replacement;
                        } else {
                            $temp[$field] = $value;
                        }
                        continue 2;
                    }

                }

                $contactExternalFileData = $datum['main_contact']['external_field_data'] ?? [];
                foreach ($contactExternalFileData as  $contactExternalFileDatum) {
                    if($contactExternalFileDatum['id'] == $field) {

                        if($contactExternalFileDatum['field_type'] == CustomFieldService::FIELD_TYPE_IMAGE || $contactExternalFileDatum['field_type'] == CustomFieldService::FIELD_TYPE_ATTACH) {
                            $value = implode(';',array_column($contactExternalFileDatum['value'],'file_url'));
                        }elseif($contactExternalFileDatum['field_type'] == CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT){
                            $value = is_array($contactExternalFileDatum['value']) ? implode(';',$contactExternalFileDatum['value']) : $contactExternalFileDatum['value'];
                        }else{
                            $value = $contactExternalFileDatum['value'];
                        }
                        if (in_array($field, $disableFields)) {
                            $temp[$field] = $replacement;
                        } else {
                            $temp[$field] = $value;
                        }
                        continue 2;
                    }

                }

                $temp[$field] = null;
            }
            $dataMap[] = $temp;
        }

        return  $dataMap;
    }

    public function writeDataToExportFile()
    {
        $data = $this->getExportData();
        $this->exportFile->setData($data);
        return $this->exportFile->getNewWriter()->writer();
    }

    public function uploadExportFile($tempFilePath)
    {
        return $upload = \UploadService::uploadRealFile($tempFilePath, $this->getExportFilename(), \UploadService::getFileKey($tempFilePath));
    }

    public function export()
    {
        $this->initExportFile();
        $tempFile = $this->writeDataToExportFile();
        $upload = $this->uploadExportFile($tempFile);

        $data=[
            'result_file_id' => $upload->getFileId(),
            'finish_time' => xm_function_now(),
            'total' =>  count($this->exportFile->getData())
        ];

        $this->export->getOperator()->success($data);

        return true;
    }
}

