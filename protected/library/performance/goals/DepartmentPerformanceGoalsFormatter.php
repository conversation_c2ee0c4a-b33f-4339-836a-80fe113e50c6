<?php
/**
 * Created by PhpStorm.
 * User: troyli
 * Date: 2019-03-14
 * Time: 19:31
 */

namespace common\library\performance\goals;


use common\library\department\DepartmentMember;
use common\library\department\DepartmentPermission;
use common\library\department\DepartmentRedis;
use common\library\department\Helper;
use common\library\performance\PerformanceConstant;
use common\library\performance\PerformanceGoals;
use common\library\performance\PerformanceSetting;
use common\library\performance\record\DepartmentPerformanceRecordList;
use common\library\privilege_v3\PrivilegeConstants;

class DepartmentPerformanceGoalsFormatter extends \ListItemFormatter
{

    protected $clientId;
    protected $fillByMember;
    protected $showFinish = false;
    protected $showLastYearFinish = false;
    protected $depth;

    protected $startAccountDate;
    protected $endAccountDate;
    protected $viewingUser;

    public function __construct($clientId, $viewingUser = 0)
    {
        $this->viewingUserId = $viewingUser;
        $this->clientId = $clientId;
    }

    public function setStartAccountDate($startAccountDate)
    {
        $this->startAccountDate = $startAccountDate;
    }

    public function setEndAccountDate($endAccountDate)
    {
        $this->endAccountDate = $endAccountDate;
    }

    public function setShowFinish($showFinish, $showLastYearFinish, $depth = 2)
    {
        $this->showFinish = $showFinish;
        $this->showLastYearFinish = $showLastYearFinish;
        $this->depth = $depth;
    }

    public function buildMapData()
    {
        $listData = $this->batchFlag ? $this->listData : [$this->data];

        $amount = [];
        $departmentIds = array_unique(array_column($listData, 'department_id'));
        if ($this->fillByMember) {
            if (!empty($departmentIds)) {
//                $departmentUsers = [];
                $year = current($listData)['year'];
                $userIds = [];
                $departmentUsers = (new DepartmentRedis())->getDepartmentUserRecursiveMap($this->clientId, $departmentIds);
                foreach ($departmentIds as $departmentId) {
//                    $departmentUsers[$departmentId] = (new DepartmentMember($this->clientId))->getMemberUserIds($departmentId, true);
                    $userIds = array_merge($userIds, $departmentUsers[$departmentId] ?? []);
                }

                $list = new UserPerformanceGoalsList($this->clientId);
                $list->setSkipPrivilege(true);
                $list->setYear($year);
                $list->setOwner($userIds);
                $userGoalList = $list->find();

                foreach ($departmentUsers as $departmentId => $userIds) {
                    $amount[$departmentId] = [];
                    foreach ($userIds as $userId) {
                        foreach ($userGoalList as $userGoal) {
                            if ($userGoal['user_id'] == $userId) {
                                if (isset($amount[$departmentId][$userGoal['cycle_num']])) {
                                    $amount[$departmentId][$userGoal['cycle_num']] += $userGoal['amount'];
                                } else {
                                    $amount[$departmentId][$userGoal['cycle_num']] = $userGoal['amount'];
                                }
                            }
                        }
                    }
                }
            }
        }

        $cycleFinishAmount = [];
        if (count($listData) && $this->showFinish) {
            $performanceSetting = PerformanceSetting::instance($this->clientId);
            $cycle = $performanceSetting->getCycle();

            // 获取查询时间范围，当前仅支持当年
            if ($this->startAccountDate || $this->endAccountDate) {
                $startDate = $this->startAccountDate ?? date("Y-m-d");
                $endDate = $this->endAccountDate ?? date("Y-m-d");
            } else {
                list($startDate, $endDate) = PerformanceGoals::getTimeRangeByList($listData);
            }

            $list = new DepartmentPerformanceRecordList($this->clientId, $this->viewingUser);
            $list->setSkipOwnerCheck(true);
            $list->setStartAccountDate($startDate);
            $list->setEndAccountDate($endDate);
            $list->setAssociateLastYear($this->showLastYearFinish);
            $list->setOwnerId($departmentIds);
            $departmentPerformanceRecords = $list->statistic($cycle);

            foreach ($departmentPerformanceRecords as $departmentPerformanceRecord) {
                $key = implode('_', [
                    $departmentPerformanceRecord['department_id'],
                    $departmentPerformanceRecord['year'],
                    $departmentPerformanceRecord['cycle_num'],
                ]);

                $cycleFinishAmount[$key] = $departmentPerformanceRecord['sum_amount'];
            }
        }

        $this->mapData = [
            'amount' => $amount,
            'finish' => $cycleFinishAmount,
        ];
    }

    public function setFillByMember($fillByMember)
    {
        $this->fillByMember = $fillByMember;
    }

    protected function format($data)
    {
        if ($this->fillByMember) {
            $data['amount'] = $this->getMapData('amount', $data['department_id'])[$data['cycle_num']] ?? PerformanceConstant::EMPTY_GOAL_AMOUNT;
        }

        $data['amount'] = floatval(round($data['amount'], 2));
        $data['cycle_num'] = intval($data['cycle_num']);

        if ($this->showFinish) {
            $data['finish_amount'] = $this->getMapData(
                    'finish',
                    implode('_', [$data['department_id'], $data['year'], $data['cycle_num']]))
                ?? 0;
            if ($this->showLastYearFinish) {
                $data['last_year_finish_amount'] = $this->getMapData(
                        'finish',
                        implode('_', [$data['department_id'], $data['year'] - 1, $data['cycle_num']]))
                    ?? 0;
            }
        }

        return $data;
    }

    public function result()
    {
        $result = parent::result();

        if ($this->depth) {
            $result = $this->mergeDepth($result);
        }

        return $result;
    }

    protected function mergeDepth($list)
    {
        $departmentPrefix = [];

        $depth = $this->depth;
        if ($depth && count($list) > 1) {
            $departmentIds = array_unique(array_column($list, 'department_id'));
            $departments = Helper::getBatchDepartment($this->clientId, $departmentIds);
            $topLayer = 999;
            foreach ($departments as $department) {
                $departmentPrefix[$department['id']] = array_filter(explode('-', $department['prefix']), function ($v) {return $v != '';});
                $topLayer = min(count($departmentPrefix[$department['id']]), $topLayer);
            }
            $depth += $topLayer;
        }

        $mapList = [];
        foreach ($list as $item) {
            $key = implode('_', [
                $item['department_id'],
                $item['year'],
                $item['cycle_num'],
            ]);
            $mapList[$key] = $item;
        }

        foreach ($mapList as $k => $item) {
            if ($prefix = $departmentPrefix[$item['department_id']] ?? '') {
                if (count($prefix) >= $depth) {
                    $departmentId = $prefix[$depth - 1];
                    $parentKey = implode('_', [
                        $departmentId,
                        $item['year'],
                        $item['cycle_num'],
                    ]);
                    if (isset($mapList[$parentKey])) {
                        if ($this->showFinish) {
                            $mapList[$parentKey]['finish_amount'] += $item['finish_amount'];
                            if ($this->showLastYearFinish) {
                                $mapList[$parentKey]['last_year_finish_amount'] += $item['last_year_finish_amount'];
                            }
                        }
                        unset($mapList[$k]);
                    }
                }
            }
        }

        return array_values($mapList);
    }
}