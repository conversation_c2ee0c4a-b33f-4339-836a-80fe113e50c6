<?php
/**
 * Created by PhpStorm.
 * User: troyli
 * Date: 2019-03-05
 * Time: 11:29
 */

namespace common\library\performance;

use common\library\custom_field\CustomFieldService;
use common\library\performance\record\DepartmentPerformanceRecordList;
use common\library\performance\record\UserPerformanceRecordList;
use common\library\performance\source\PerformanceSourceAbstract;
use common\library\util\PgsqlUtil;
use DepartmentPerformance;
use UserPerformance;

class PerformanceRecorder
{

    /**
     * @var PerformanceSourceAbstract
     */
    protected $source;
    protected $setting;

    /**
     * PerformanceRecord constructor.
     *
     * @param PerformanceSourceAbstract $source
     */
    public function __construct(PerformanceSourceAbstract $source)
    {
        $this->source = $source;
    }

    public function run()
    {
        /**
         * 1. 清除旧记录
         * 2. 记录用户绩效
         * 3. 记录部门绩效
         */
        if ($this->source->needClean()) {
            $this->clean();
        }

        if ($this->source->needAccount() && $this->source->needCreate()) {
            $this->create();
        }
    }

    public function clean()
    {
        self::cleanBySource(
            $this->source->getClientId(),
            $this->source->getSourceType(),
            $this->source->getSourceId()
        );
    }

    public static function cleanBySource($clientId, $sourceType, $sourceId)
    {
        $userList = new UserPerformanceRecordList($clientId);
        $userList->setSkipOwnerCheck(true);
        $userList->setSourceType($sourceType);
        $userList->setSourceId($sourceId);
        $userList->delete();

        $departmentList = new DepartmentPerformanceRecordList($clientId);
        $departmentList->setSkipOwnerCheck(true);
        $departmentList->setSourceType($sourceType);
        $departmentList->setSourceId($sourceId);
        $departmentList->delete();
    }

    public function create()
    {
        \LogUtil::info("Performance create for {$this->source->getSourceId()}");

        $this->createPerformances(UserPerformance::model(), $this->source->getUserRates());
        $this->createPerformances(DepartmentPerformance::model(), $this->source->getDepartmentRates());
    }

    protected function createPerformances(\PgActiveRecord $model, $rates)
    {
        $records = [];
        $performanceFieldCurrencyMap = [
            'amount_rmb' => 'CNY',
            'amount_usd' => 'USD',
        ];
        $primaryFieldMap = [
            'amount_rmb' => 'primary_amount_rmb',
            'amount_usd' => 'primary_amount_usd',
        ];

        $primaryFieldName = $this->source->getPrimaryPerformanceField();

        foreach ($rates as $belongsTo => $rate) {
            $record = [
                'client_id' => $this->source->getClientId(),
                'source_type' => $this->source->getSourceType(),
                'source_id' => $this->source->getSourceId(),
                'company_id' => $this->source->getCompanyId() ?? 0,
                'account_date' => $this->source->getAccountDate(),
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
            ];
            $rate = floatval($rate);
            foreach ($performanceFieldCurrencyMap as $performanceField => $currency) {
                foreach ($this->source->getPerformanceAmount($currency) as $field => $amount) {
                    $record[$performanceField][$field] = $amount * $rate / 100;
                    if (!isset($record['expression'][$field])) {
                        $record['expression'][$field] = \common\library\formula\Helper::getFieldExpression($this->source->getClientId(), $this->source->getSourceType(), $field);
                    }
                }
                $record[$primaryFieldMap[$performanceField]] = $record[$performanceField][$primaryFieldName];
                $record[$performanceField] = json_encode($record[$performanceField]);
            }

            if ($model instanceof UserPerformance) {
                $record['user_id'] = $belongsTo;
            } elseif ($model instanceof DepartmentPerformance) {
                $record['department_id'] = $belongsTo;
            }

            $record['expression'] = json_encode($record['expression']);
            $records[] = $record;
        }

        if (empty($records)) {
            return 0;
        }

        return $model->getDbConnection()
            ->getCommandBuilder()
            ->createMultipleInsertCommand($model->tableName(), $records)->execute();
    }

}
