<?php
/**
 * This file is part of the xiaoman/crm.
 *
 * (c) sevenshi <<EMAIL>>
 *
 */

namespace common\library\statistics\event\handler;


use common\library\custom_field\CustomFieldService;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\statistics\builder\BuilderConstant;
use common\library\statistics\config\ReportConfig;
use common\library\statistics\parser\ObjectYamlParser;
use common\library\statistics\util\SqlBuilderUtil;

class SelectAdvanceHandler extends Handler
{
    const SELECT_ADVANCE_SUPPORT_OBJECT = [
        'company',
        'customer',
        'lead',
        'opportunity',
        'order',
        'product',
        'quotation',
        'cash_collection',
//        'mail',
    ];

    const SELECT_ADVANCE_SUPPORT_FIELD_TYPE = [
        CustomFieldService::FIELD_TYPE_SELECT,
        CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
        CustomFieldService::FIELD_TYPE_DATE,
        CustomFieldService::FIELD_TYPE_DATETIME,
        CustomFieldService::FIELD_TYPE_TEXT,
        CustomFieldService::FIELD_TYPE_TEXTAREA,
        CustomFieldService::FIELD_TYPE_NUMBER,
        CustomFieldService::FIELD_TYPE_BOOLEAN,
    ];

    public function handle($clientId, $userId, ReportConfig $reportConfig)
    {
        $builder = new \common\library\statistics\builder\ReportBuilder($clientId);
        $variable = $reportConfig->getQueryConfig()->getVariable();
        $variable = array_column($variable,null,'field');

        $flowConfig = $reportConfig->getQueryConfig()->getFlow();
        $flowConfig = $flowConfig['set'][0]['node'] ?? [];

        $customQuery = $reportConfig->getQueryConfig()->getCustomQuery();
        //目前先只支持flow查询的筛选条件补充
        if (empty($flowConfig) || !empty($customQuery)) {
            return;
        }

        $data = [];
        foreach ($flowConfig as $flowConfigItem) {
            if (in_array($flowConfigItem['object'], self::SELECT_ADVANCE_SUPPORT_OBJECT)) {
                $data = array_merge($builder->getFieldList($flowConfigItem['object']), $data);
            }
        }

        $data = array_combine(array_column($data, 'field'), $data);


        $client = \common\library\account\Client::getClient($clientId);
        //公海分组字段隐藏 公海开关隐藏或者没有公海分组权限 需要隐藏
        if(!$client->getExtentAttribute(\common\library\account\Client::EXTERNAL_KEY_CUSTOMER_POOL_SWITCH)
            || !\common\library\privilege_v3\Helper::hasFunctional($clientId,PrivilegeConstants::FUNCTIONAL_COMPANY_POOL_SETTING)){
            unset($variable['company.pool_id']);
        }

        $externalVariable = [];

        //报表的query字段作为默认查询参数，其他字段作为flow节点的扩展参数
        foreach ($data as $objectField => $fieldInfo) {
            $fieldId = explode(':', $objectField)[1] ?? '';
            if (in_array($fieldInfo['field_type'], self::SELECT_ADVANCE_SUPPORT_FIELD_TYPE) && !in_array($fieldId, $variable)) {
                $externalVariable[] = $fieldInfo;
            }
        }

        if (!empty($externalVariable)) {
            $externalVariable = array_column($externalVariable,null,'field');
            $variable = array_values(array_merge($variable,$externalVariable));
        }

        $reportConfig->getQueryConfig()->setVariable($variable);

        $externalVariableFields = array_column($externalVariable,null,'field');
        $externalFlowNodeConfig = $this->buildFlowConfig($externalVariableFields);
        foreach ($flowConfig as &$objectConfig) {
            if (isset($externalFlowNodeConfig[$objectConfig['object']])) {
                //node对象的outer防空处理
                $objectConfig['limit']['filter']['outer'] = array_merge($objectConfig['limit']['filter']['outer'] ?? [] , $externalFlowNodeConfig[$objectConfig['object']]['limit']['filter']['outer'] ?? []);
            }
        }
        $flowConfig = [
            'set' => [
                [
                    'node' => $flowConfig
                ]
            ]
        ];
        $reportConfig->getQueryConfig()->setFlow($flowConfig);
    }

    private function buildFlowConfig($queryConfig)
    {

        $baseQueryConfig =[];
        $flowNodeConfig = [];

        foreach ( $queryConfig as &$item )
        {
            list($objectKey, $id ) = explode('.', $item['field']);
            $objectConfig = ObjectYamlParser::parser($objectKey);

            if( $fieldAttr =  $objectConfig->getAttributes()[$id]??false )
            {
                $item['type'] = $fieldAttr['type']??$queryConfig[$item['field']]['field_type']??0;
                $item['comment'] = $fieldAttr['comment']??($queryConfig[$item['field']]['name']??'');
                $item['multiple'] = $fieldAttr['multiple']??0;
                $item['search_flag'] = $fieldAttr['search'] ?? 0;
            }else
            {
                $fieldAttr = $queryConfig[$item['field']];
                $item['type'] = $fieldAttr['field_type'];
                $item['search_flag'] = $fieldAttr['search_flag'] ?? 0;
            }

            if( !isset($item['multiple']) || !$item['multiple'])
                $item['multiple'] = $item['type'] == CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT? 1: 0;

            //默认本周期
            if( SqlBuilderUtil::isDateFieldType($item['type']) && ($item['period']??''))
            {
                if( !($item['default']??false) )
                    $item['default'] = 1;
            }

            $baseQueryConfig[$objectKey][] = $item;
        }
        unset($item);

        $count = 1;
        foreach ( $baseQueryConfig as $objectKey => $objectInfo )
        {
            $objectConfig = ObjectYamlParser::parser($objectKey);

            $nodeLimit = [];
            $setLimit = [];

            $mergeMode = BuilderConstant::MERGE_MODE_INTERSECTION;
            $mergeConnect = ['parentNodeId' => '', 'currentNodeId' => ''];

            if( !empty($lastNodeObject) )
            {

                $relativeObjectConfig = $objectConfig->getRelatives()[$lastNodeObject['object']] ?? false;

                //例如 产品-订单-客户,其中产品和客户是没有直接关联关系的
                if (!$relativeObjectConfig) {
                    continue;
                }
                $currentNodeId = array_keys($relativeObjectConfig)[0];
                $parentNodeId = array_values($relativeObjectConfig)[0];

                $nodeLimit['id'] = str_replace("{$objectKey}.", '', $currentNodeId);
                $nodeLimit['range'] =  $parentNodeId;

                $mergeConnect['parentNodeId'] = $parentNodeId;
                $mergeConnect['currentNodeId'] = $currentNodeId;

            }

            $filter= [ 'outer' => [] , 'inner'=>[]];

            foreach ( $baseQueryConfig[$objectKey]??[] as $item )
            {
                $filter['outer'][] = [
                    'field' => str_replace("{$objectKey}.", '', $item['field']),
                    'operation' => BuilderConstant::FILTER_OPERATOR_EQ,
                    'referenceValue' => ':variable',
                    'logic' => BuilderConstant::FILTER_LOGIC_AND
                ];
            }

            $limit =  [
                'nodeLimit'=> $nodeLimit,
                'setLimit'=> $setLimit,
                'filter' => $filter
            ];

            $merge = [
                'mode' => $mergeMode,
                'connect' => $mergeConnect
            ];

            $lastNodeObject = $nodeObject = [
                'object' => $objectKey,
                'type' => $count == 1 ? BuilderConstant::FLOW_NODE_OBJECT_TYPE_BASE:BuilderConstant::FLOW_NODE_OBJECT_TYPE_REFER ,
                'limit' => $limit,
                'merge' => $merge
            ];

            $count ++;
            $flowNodeConfig[$objectKey] = $nodeObject;
        }

        return $flowNodeConfig;
    }
}
