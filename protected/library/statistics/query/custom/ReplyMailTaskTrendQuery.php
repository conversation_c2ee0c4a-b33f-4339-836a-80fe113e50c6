<?php
/**
 * This file is part of the xiaoman/xiaoman-crm.
 *
 * (c) sevenshi <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled.
 */


namespace common\library\statistics\query\custom;


use common\library\statistics\Constant;
use common\library\statistics\data_adapter\AbstractDataAdapter;
use common\library\statistics\data_adapter\Arr;
use common\library\statistics\foundation\store\CacheKey;
use common\library\statistics\Helper;
use common\library\statistics\Locator;
use common\library\statistics\query\AbstractQuery;
use common\library\task\TaskConstant;
use common\library\task\TaskList;
use common\library\workflow\WorkflowConstant;

class ReplyMailTaskTrendQuery extends AbstractQuery
{

    /**
     * @return AbstractDataAdapter
     */
    public function query()
    {
        $locator = Locator::instance();
        $clientId = $locator->getClientId();
        $userId = $locator->getUserId();

        $params = $locator->getReportConfig()->getQueryConfig()->getParams(1);
        $filterFreezeUserFlag = $locator->getFilterFreezeUserFlag();

        $visibleUserId = $params['common.visible']->getValue() ?: [];

        if (empty($visibleUserId)) {
            $visibleUserId = Helper::getManageAbleUserId($clientId,$userId,$filterFreezeUserFlag);
        }


        $taskRuleIds = $params['common.task_rule_id']->getValue() ?: [];


        $startDate = $params['common.deadline']->getValue()['start'] ?: date("Y-m-d", time());
        $endDate = $params['common.deadline']->getValue()['end'] ?: date("Y-m-d", time());
        $createStartDate = $params['common.create_time']->getValue()['start'];
        $createEndDate = $params['common.create_time']->getValue()['end'];

        $selectCycle = ($params['common.select_cycle']->getValue() ?? 'day') ?: 'week';

        if (empty($taskRuleIds)) {
            $listQuery = new \common\library\workflow\WorkflowRuleList($clientId);
            $listQuery->setReferType(\Constants::TYPE_MAIL);
            $listQuery->setDisableFlag(null);
            $listQuery->setSkipPrivilege(true);
            $listQuery->setRuleType(WorkflowConstant::RULE_TYPE_TASK);
            $ruleList = $listQuery->find();

            $taskRuleIds = array_column($ruleList, 'rule_id');
        }

        if (empty($taskRuleIds)) {
            return new Arr([]);
        }

        $taskList = new TaskList($clientId, $userId);
        $taskList->setType(TaskConstant::TASK_TYPE_REPLY_MAIL);
        $taskList->setRuleIds($taskRuleIds);
        $taskList->setUserIds($visibleUserId);
        $taskList->setBeginEndTime($startDate);
        $taskList->setNextEndTime($endDate);
        $taskList->setFields('*');
        $taskList->setCreateStartTime($createStartDate);
        $taskList->setCreateEndTime($createEndDate);
        $tasks = $taskList->find();


        $taskGroups = [];
        foreach ($tasks as &$item) {
            list($date, $startTime, $endTime) = $this->formatFieldDate($selectCycle, date("Y-m-d", strtotime($item['end_time'])));
            $taskGroups[$date][] = $item;
        }

        unset($tasks);

        $data = [];
        $otherData = [];
        foreach ($taskGroups as $date => $taskGroup) {
            $completedTaskMap = [];
            $inTimeTaskMap = [];
            $timeOutTaskMap = [];
            $unFinishTaskMap = [];
            $ignoreTaskMap = [];
            $allTaskTime = [];
            //需要处理的用户
            $processUserIds = [];

            foreach ($taskGroup as $taskItem)
            {
                $taskItemUserId = $taskItem['user_id'];
                $taskItemId = $taskItem['task_id'];
                $taskItemEndTime = $taskItem['end_time'];
                $taskItemFinishTime = $taskItem['finish_time'];
                $taskItemStatus = $taskItem['status'];
                $taskItemCreateTime = $taskItem['create_time'];
                $processUserIds[] = $taskItemUserId;

                if (in_array($taskItemStatus, [TaskConstant::TASK_STATUS_INCOMPLETE, TaskConstant::TASK_STATUS_COMPLETED]))
                {
                    if ($taskItemStatus == TaskConstant::TASK_STATUS_COMPLETED)
                    {
                        // 已完成的任务
                        $completedTaskMap[$taskItemUserId][] = $taskItemId;

                        // 任务完成超时/未超时
                        if (strtotime($taskItemFinishTime) <= strtotime($taskItemEndTime)) {
                            $inTimeTaskMap[$taskItemUserId][] = $taskItemId;
                        } else {
                            $timeOutTaskMap[$taskItemUserId][] = $taskItemId;
                        }

                        //任务完成时间
                        if (isset($allTaskTime[$taskItemUserId])) {
                            $allTaskTime[$taskItemUserId] += strtotime($taskItemFinishTime) - strtotime($taskItemCreateTime);
                        } else {
                            $allTaskTime[$taskItemUserId] = strtotime($taskItemFinishTime) - strtotime($taskItemCreateTime);
                        }

                    }

                    //未完成任务数
                    if ($taskItemStatus == TaskConstant::TASK_STATUS_INCOMPLETE) {
                        $unFinishTaskMap[$taskItemUserId][] = $taskItemId;
                    }
                }

                //忽略任务数
                if ($taskItemStatus == TaskConstant::TASK_STATUS_IGNORE) {
                    $ignoreTaskMap[$taskItemUserId][] = $taskItemId;
                }

            }

            $processUserIds = array_values(array_unique($processUserIds));
            \LogUtil::info(sprintf("[ReplyMailTaskTrendQuery] clientId[$clientId] 处理的用户数是[%s] 总数是[%s]", count($processUserIds), count($visibleUserId)));
            foreach ($processUserIds as $userId) {

                $completeTaskCount = count($completedTaskMap[$userId] ?? []);

                $data[] = [
                    'task.user_id' => $userId,
                    'task.end_time' => $date,
                    'task.target_reply_mail_count' => count($completedTaskMap[$userId] ?? []) + count($unFinishTaskMap[$userId] ?? []),
                    'task.in_time_reply_mail_count' => count($inTimeTaskMap[$userId] ?? []),
                    'task.timeout_reply_mail_count' => count($timeOutTaskMap[$userId] ?? []),
                    'task.todo_reply_mail_count' => count($unFinishTaskMap[$userId] ?? []),
                    'task.average_reply_mail_time' => $completeTaskCount == 0 ? 0 : $allTaskTime[$userId] ?? 0,
                    'task.ignore_task_count' => count($ignoreTaskMap[$userId] ?? []),
                    'task.complete_task_count' => $completeTaskCount
                ];

                $summateKey = CacheKey::productSummateKey('sum-task.target_reply_mail_count', CacheKey::productGroupKey('task.user_id', $userId, CacheKey::productGroupKey('task.end_time', $date)));
                $otherData['summate'][$summateKey] = array_merge($completedTaskMap[$userId] ?? [], $unFinishTaskMap[$userId] ?? []);
                $otherData['summate_type'][$summateKey] = Constant::DATA_TYPE_ARRAY;

                $summateKey = CacheKey::productSummateKey('sum-task.in_time_reply_mail_count', CacheKey::productGroupKey('task.user_id', $userId, CacheKey::productGroupKey('task.end_time', $date)));
                $otherData['summate'][$summateKey] = $inTimeTaskMap[$userId] ?? [];
                $otherData['summate_type'][$summateKey] = Constant::DATA_TYPE_ARRAY;

                $summateKey = CacheKey::productSummateKey('sum-task.timeout_reply_mail_count', CacheKey::productGroupKey('task.user_id', $userId, CacheKey::productGroupKey('task.end_time', $date)));
                $otherData['summate'][$summateKey] = $timeOutTaskMap[$userId] ?? [];
                $otherData['summate_type'][$summateKey] = Constant::DATA_TYPE_ARRAY;

                $summateKey = CacheKey::productSummateKey('sum-task.todo_reply_mail_count', CacheKey::productGroupKey('task.user_id', $userId, CacheKey::productGroupKey('task.end_time', $date)));
                $otherData['summate'][$summateKey] = $unFinishTaskMap[$userId] ?? [];
                $otherData['summate_type'][$summateKey] = Constant::DATA_TYPE_ARRAY;

                $summateKey = CacheKey::productSummateKey('sum-task.ignore_task_count', CacheKey::productGroupKey('task.user_id', $userId, CacheKey::productGroupKey('task.end_time', $date)));
                $otherData['summate'][$summateKey] = $ignoreTaskMap[$userId] ?? [];
                $otherData['summate_type'][$summateKey] = Constant::DATA_TYPE_ARRAY;
            }

            unset($completedTaskMap);
            unset($inTimeTaskMap);
            unset($timeOutTaskMap);
            unset($unFinishTaskMap);
            unset($allTaskTime);
            unset($ignoreTaskMap);
        }

        $dataAdapter = new Arr($data);
        $dataAdapter->setOtherData($otherData);
        return $dataAdapter;
    }

    protected function formatFieldDate($selectCycle, $date)
    {
        $dateTime = strtotime($date);
        $format = $date;
        $startTime = '';
        $endTime = '';
        switch ($selectCycle )
        {
            case  'week':
                if( strlen($date) ==10)
                {
                    $date = date('Y-W', $dateTime);
                }

                list($year , $week) = explode('-', $date);
                $day = $week * 7;
                $time = strtotime(" +{$day} day", strtotime($year.'/01/01'));
                $w = date('w',$time);
                $startDate = date('Y/m/d',strtotime(' -'.($w ? $w - 1 : 6).' days', $time));
                $endDate = date('Y/m/d',strtotime("$startDate +6 days"));
                $format =  $startDate.' ~ '.$endDate;

                $startTime = $startDate.' 00:00:00';
                $endTime = $endDate.' 23:59:59';

                break;
            case  'month':
                if( strlen($date) ==10)
                {
                    $format =  date('Y/m', $dateTime);
                }

                $startTime = date('Y/m/01', $dateTime).' 00:00:00';
                $endTime = date('Y/m/t', $dateTime).' 23:59:59';

                break;
            case 'day':
                $format = $date = date('Y/m/d', strtotime($date));
                $startTime = $date.' 00:00:00';
                $endTime = $date.' 23:59:59';
                break;
            case  'season':

                $season = ceil(date('n', $dateTime) /3); //获取月份的季度
                $year = date('Y', $dateTime);

                switch ( $season )
                {
                    case '1':
                        $startTime = date('Y/01/01', $dateTime);
                        $endTime = date('Y/03/t', strtotime($year.'03'));
                        break;
                    case '2':
                        $startTime = date('Y/04/01', $dateTime);
                        $endTime = date('Y/06/t', strtotime($year.'06'));
                        break;
                    case '3':
                        $startTime = date('Y/07/01', $dateTime);
                        $endTime = date('Y/09/t', strtotime($year.'09'));
                        break;
                    case '4':
                        $startTime = date('Y/10/01', $dateTime);
                        $endTime = date('Y/12/t', strtotime($year.'12'));
                        break;
                    default:
                        $startTime = date('Y/01/01', $dateTime);
                        $endTime = date('Y/03/t', strtotime($year.'03'));
                        break;
                }

                $format = "$year Q$season";
                $startTime = $startTime.' 00:00:00';
                $endTime = $endTime.' 23:59:59';

                break;
            case  'half_a_year':

                $dateTime = strtotime($date);
                $halfYear = ceil(date('n', $dateTime)/6);

                $startMonth = $halfYear*6-5;
                $startMonth = $startMonth >= 10 ? $startMonth : "0{$startMonth}";
                $startTime = date("Y/{$startMonth}/01", $dateTime);


                $year = date('Y', $dateTime);
                $endMonth = $halfYear*6;
                $endMonth = $endMonth >= 10 ? $endMonth : "0{$endMonth}";
                $endTime = date("Y/{$endMonth}/t", strtotime("$year-$endMonth-01"));

                $format =  "$year H$halfYear";
                $startTime .= ' 00:00:00';
                $endTime .= ' 23:59:59';

                break;
            default:
                break;
        }

        return [$format, $startTime, $endTime];

    }


}