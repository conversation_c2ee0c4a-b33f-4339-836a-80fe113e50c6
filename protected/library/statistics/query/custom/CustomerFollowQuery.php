<?php

namespace common\library\statistics\query\custom;

use common\library\mail\MailList;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\statistics\Constant;
use common\library\statistics\data_adapter\Arr;
use common\library\statistics\Factory;
use common\library\statistics\foundation\store\CacheKey;
use common\library\statistics\Helper;
use common\library\statistics\list_adapter\CompanyListAdapter;
use common\library\statistics\Locator;
use common\library\statistics\query\AbstractQuery;
use common\library\util\PgsqlUtil;
use DataWorkActiveRecord;
use DateTime;

class CustomerFollowQuery extends AbstractQuery
{

    public function query()
    {

        $locator = Locator::instance();
        $locator->setCommonReportFlag(1);
        $clientId = $locator->getClientId();
        $userId  = $locator->getUserId();
        $params = $locator->getReportConfig()->getQueryConfig()->getParams();

        // 获取可见范围用户ID
        $filterFreezeUserFlag = $locator->getFilterFreezeUserFlag();
        $visibleUserId = $params['common.visible']->getValue() ?: [];
        // userId为空时去取部门下的UserId
        empty($visibleUserId) && $visibleUserId = Helper::getSelectUserIds(PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW, $clientId, $userId, $visibleUserId, $filterFreezeUserFlag);

        if (empty($visibleUserId)) {
            $visibleUserId = [$userId];
        }

        // 获取常用筛选项条件
        $filters = Helper::getCompanyCommonFilter($params);


        if(!empty($filters)){
            // 获取筛选字段
            $fields = $this->getSearchFields();

            // 获取客户信息
            $listAdapter = Factory::createListAdapter('company', $filters, $fields);
            $listAdapter->setRefer(false);
            /**
             * @var CompanyListAdapter $listAdapter
             */
            $listAdapter->getList()->setUserId($visibleUserId);
            [$sqlWhere ,$sqlParams] = $listAdapter->buildQueryParams();
            $sql = "select company_id from tbl_company where $sqlWhere limit 100000";
            $companyInfos = \PgActiveRecord::getDbByClientId($clientId)->createCommand($sql)->queryAll(true,$sqlParams);
            $companyIds = array_column($companyInfos,'company_id');
            if(empty($companyIds))
                return new Arr([]);
            $companyIdsStr = implode(',', $companyIds);
        }else{
            $companyIdsStr = '';
        }


        $startDate = $params['common.date']->getValue()['start'] ?: date("Y-m-01"); // 当前月份的第一天
        $endDate = $params['common.date']->getValue()['end'] ?: date("Y-m-d"); // 当前日期

        // 获取包含的邮件文件夹
        $speIncludeFolderId = empty($params['mail.spe_include_folder_id']) ? [] : $params['mail.spe_include_folder_id']->getValue();
        $exposeFlag  = empty($params['mail.expose_flag']) ? null : $params['mail.expose_flag']->getValue() ?? '';
        //邮件系统标签
        $systemTag = empty($params['mail.mail_tag.system_tag']) ? [] : $params['mail.mail_tag.system_tag']->getValue() ?? [];
        if (!empty($systemTag)) {
            $systemTag = is_array($systemTag) ? $systemTag : [$systemTag];
        }

        // 邮件类型
        $mailType = empty($params['mail.mail_type']) ? '' : $params['mail.mail_type']->getValue() ?? '';
        if($mailType == '3'){
            $mailType = '';
        }

        $filterMailFlag = !empty($speIncludeFolderId) || !empty($exposeFlag) || !empty($systemTag) || $mailType != '';

        $visibleUserIdStr = implode(',', $visibleUserId);
        $holoDb = \DataWorkActiveRecord::getDbByClientId($clientId, \DataWorkActiveRecord::DATA_WORK_AI_DB);

        $countSql = "
SELECT 
    user_id,
    SUM(CASE WHEN follow_type = 1 THEN 1 ELSE 0 END) AS send_message_count,
    SUM(CASE WHEN follow_type = 2 THEN 1 ELSE 0 END) AS send_mail_count,
    SUM(CASE WHEN follow_type = 3 THEN 1 ELSE 0 END) AS send_edm_count,
    SUM(CASE WHEN follow_type = 4 THEN 1 ELSE 0 END) AS send_follow_record_count,
    COUNT(DISTINCT company_id) FILTER (WHERE follow_type = 1) AS send_message_company_count,
    COUNT(DISTINCT company_id) FILTER (WHERE follow_type = 2) AS send_mail_company_count,
    COUNT(DISTINCT company_id) FILTER (WHERE follow_type = 3) AS send_edm_company_count,
    COUNT(DISTINCT company_id) FILTER (WHERE follow_type = 4) AS send_follow_company_count
FROM 
    tbl_customer_follow_record_full
WHERE 
    client_id = $clientId 
    AND user_id IN ($visibleUserIdStr) 
    AND follow_date >= '$startDate' 
    AND follow_date <= '$endDate'"
   .(empty($companyIdsStr) ? '' : "AND company_id IN ($companyIdsStr)").
"GROUP BY 
    user_id
ORDER BY 
    user_id DESC";

        $countResult = $holoDb->createCommand($countSql)->queryAll();

        $result = [];
        foreach ($countResult as $item){
            $key = $item['user_id'];
            if (!isset($result[$key]))
            {
                $result[$key] = [
                    'follow.user_id' => $key,
                    'send.message_count' => 0,
                    'send.message_company_count' => 0,
                    'send.mail_count' => 0,
                    'send.mail_company_count' => 0,
                    'send.edm_count' => 0,
                    'send.edm_company_count' => 0,
                    'follow.record_count' => 0,
                    'follow.record_company_count' => 0,
                    'send.sum_count' => 0,
                ];
            }
            $result[$key]['send.message_count'] = $item['send_message_count'] ?? 0;
            $result[$key]['send.message_company_count'] = $item['send_message_company_count'] ?? 0;
            $result[$key]['send.mail_count'] = $item['send_mail_count'] ?? 0;
            $result[$key]['send.mail_company_count'] = $item['send_mail_company_count'] ?? 0;
            $result[$key]['send.edm_count'] = $item['send_edm_count'] ?? 0;
            $result[$key]['send.edm_company_count'] = $item['send_edm_company_count'] ?? 0;
            $result[$key]['follow.record_count'] = $item['send_follow_record_count'] ?? 0;
            $result[$key]['follow.record_company_count'] = $item['send_follow_company_count'] ?? 0;
        }


        // 构造跳转数据

        $allFollowRecordList = []; // 所有的跟进列表，不区分员工

        $allMailIdList = []; // 所有的邮件列表，不区分员工

        $referMailList = [];  // 所有的邮件列表，用员工做key，value为mail_ids

        $followRecordList = []; // 所有的跟进列表，用员工做key,value为follow_ids

        $messageCompanyList = []; // 发送聊天的公司列表，用员工做key，value为company_ids

        $mailCompanyList = []; // 发送邮件的公司列表，用员工做key，value为company_ids

        $edmCompanyList = []; // 发送edm的公司列表，用员工做key，value为company_ids

        $followCompanyList = [];  // 创建跟进的公司列表，用员工做key，value为company_ids

        $sql = "
SELECT
    user_id,
    ARRAY_AGG(follow_id) FILTER (WHERE follow_type = 2)::text AS email_follow_ids,
    ARRAY_AGG(follow_id) FILTER (WHERE follow_type = 4)::text AS follow_follow_ids,
    ARRAY_AGG(DISTINCT company_id) FILTER (WHERE follow_type = 1)::text AS company_ids_type_message,
    ARRAY_AGG(DISTINCT company_id) FILTER (WHERE follow_type = 2)::text AS company_ids_type_mail,
    ARRAY_AGG(DISTINCT company_id) FILTER (WHERE follow_type = 3)::text AS company_ids_type_edm,
    ARRAY_AGG(DISTINCT company_id) FILTER (WHERE follow_type = 4)::text AS company_ids_type_follow
FROM xiaoman_ods_prod.ai_dwd.tbl_customer_follow_record_full 
WHERE
 client_id = $clientId 
    AND user_id IN ($visibleUserIdStr) 
    AND follow_date >= '$startDate' 
    AND follow_date <= '$endDate'"
               .(empty($companyIdsStr) ? '' : "AND company_id IN ($companyIdsStr)").
               "GROUP BY 
    user_id
ORDER BY 
    user_id DESC
     ";

        $res = $holoDb->createCommand($sql)->queryAll();
        foreach ($res as $item){
            $key = $item['user_id'];
            if (!isset($result[$key]))
            {
                continue;
            }
            $allMailIdList = array_merge($allMailIdList, empty($item['email_follow_ids']) ? [] : PgsqlUtil::trimArray($item['email_follow_ids']));
            $referMailList[$key] = empty($item['email_follow_ids']) ? [] : PgsqlUtil::trimArray($item['email_follow_ids']);
            $followRecordList[$key] = empty($item['follow_follow_ids']) ? [] :  PgsqlUtil::trimArray($item['follow_follow_ids']);
            $mailCompanyList[$key] = empty($item['company_ids_type_mail']) ? [] : PgsqlUtil::trimArray($item['company_ids_type_mail']);
            $messageCompanyList[$key] = empty($item['company_ids_type_message']) ? [] : PgsqlUtil::trimArray($item['company_ids_type_message']);
            $edmCompanyList[$key] = empty($item['company_ids_type_edm']) ? [] : PgsqlUtil::trimArray($item['company_ids_type_edm']);
            $followCompanyList[$key] = empty($item['company_ids_type_follow']) ? [] : PgsqlUtil::trimArray($item['company_ids_type_follow']);
            $allFollowRecordList = array_merge($allFollowRecordList, empty($item['follow_follow_ids']) ? [] : PgsqlUtil::trimArray($item['follow_follow_ids']));

        }


        if(!empty($allFollowRecordList)){
            $allFollowRecordList = array_map('intval', $allFollowRecordList);
            $trailIds = implode(',', $allFollowRecordList);
            $sql = "select follow_up_id, trail_id from tbl_follow_up where trail_id in ($trailIds) and client_id = $clientId";
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $followUpList = $db->createCommand($sql)->queryAll();
            $followUpMap = array_column($followUpList, 'follow_up_id','trail_id');
            foreach ($followRecordList as $key => $item){
                $followRecordList[$key] = array_map(function($item) use ($followUpMap){
                    return $followUpMap[$item] ?? $item;
                }, $item);
            }
        }


        // 勾选了包含邮件文件夹选项，回查tbl_mail

        if($filterMailFlag && !empty($allMailIdList)){
            $db = \ProjectActiveRecord::getDbByClientId($clientId);
            $buildMailList = new MailList($clientId);
            $buildMailList->setRelateCompanyFlag(true);
            $buildMailList->statisticUserIdSetting($visibleUserId);
            $buildMailList->statisticSetting($speIncludeFolderId);
            $buildMailList->setBeginReceiveTime($startDate);
            $buildMailList->setMailType(intval($mailType));
            $buildMailList->setEndReceiveTime($endDate);

            [$where, $sqlParams] = $buildMailList->buildParams();

            $mailIds = implode(',', $allMailIdList);

            $sql = "SELECT user_id,mail_id FROM tbl_mail WHERE {$where} AND mail_id IN ($mailIds) AND user_id IN ($visibleUserIdStr)";

            if (!empty($systemTag)) {
                $whereMap = explode('and', $where);
                foreach ($whereMap as &$item) {
                    $item = 'a.' . trim($item);
                }
                unset($item);
                $where = implode(' and ', $whereMap);
                // fixme 观察一下该SQL是否数据量过大
                $sql = "select a.user_id AS user_id, a.mail_id as mail_id from tbl_mail as a left join tbl_mail_tag_assoc as b on a.mail_id = b.mail_id where {$where}  AND a.mail_id IN ($mailIds) AND a.user_id IN ($visibleUserIdStr) and b.tag_id in (" . implode(',', $systemTag) . ')';
            }

            $mailRecords = $db->createCommand($sql)->queryAll(true, $sqlParams);
            $sendMailList = [];

            // 判断群发单显
            if ($exposeFlag) {
                foreach ($mailRecords as $item) {
                    $mailType = $item['mail_type'];
                    $statusBitFlag = $item['status_bitmap_flag'];

                    if ($mailType == \Mail::MAIL_TYPE_SEND) {
                        // 如果不满足群发单显条件 那么不统计该邮件
                        switch (true) {
                            case $exposeFlag === 0:
                                if (($statusBitFlag & 0b00000001) !== 0) {
                                    continue 2;
                                }
                                break;
                            case $exposeFlag === 1:
                                if (($statusBitFlag & 0b00000001) === 0) {
                                    continue 2;
                                }
                                break;
                        }
                    }
                    $sendMailList[] = $item;
                }
            }else{
                $sendMailList = $mailRecords;
            }

            if(!empty($sendMailList)){
                $userToMailMap = [];
                foreach ($sendMailList as $item){
                    $key = $item['user_id'];
                    $userToMailMap[$key][] = $item;
                }

                $allMailIdList = [];
                foreach ($userToMailMap as $key => $subMailIdList){
                    isset($result[$key]) && $result[$key]['send.mail_count'] = count($subMailIdList);
                    $referMailList[$key] = array_column($subMailIdList, 'mail_id');
                    $allMailIdList = array_merge($allMailIdList, array_column($subMailIdList, 'mail_id'));
                }

                if(!empty($allMailIdList)) {
                    $allMailIdStr = implode(',', $allMailIdList);
                    $sql = "select user_id, ARRAY_AGG(company_id) AS company_ids from tbl_customer_follow_record_full where follow_id in ($allMailIdStr) and client_id = $clientId and follow_type = 2";
                    $allMailCompanyList = $db->createCommand($sql)->queryAll();
                    $mailToCompanyMap = array_column($allMailCompanyList, 'company_ids', 'user_id');
                    foreach ($mailToCompanyMap as $key => $companyIds){
                        isset($result[$key]) && $mailCompanyList[$key] = $companyIds;
                    }
                }
            }
        }

        $userIdList = array_keys($result);
        $userList = new \common\library\account\UserList();
        $userList->setClientId($clientId);
        $userList->setUserIds($userIdList);
        $userList->setFields(['nickname', 'user_id']);
        $userList->setExcludeDeleteUserFlag(false);
        $users = $userList->find();
        $userNameMap = array_column($users, 'nickname', 'user_id');

        $nameResult = [];
        foreach ($result as $index => $item){
            $name = $userNameMap[$index] ?? '';
            $item['follow.user_name'] = $name;
            $item['send.sum_count'] = $item['send.message_count'] + $item['send.mail_count'] + $item['send.edm_count'] + $item['follow.record_count'];
            $nameResult[$name] = $item;
        }


        $otherData = [];
        foreach ($nameResult as $item){
            $key = $item['follow.user_id'];

            $groupKey = CacheKey::productGroupKey('follow.user_name', $item['follow.user_name']);

            $sumMateFollowKey = CacheKey::productSummateKey('sum-follow.record_count', $groupKey);
            $otherData['summate'][$sumMateFollowKey] = array_values($followRecordList[$key] ?? []);
            $otherData['summate_type'][$sumMateFollowKey] = Constant::DATA_TYPE_ARRAY;

            $sumMateSendMailKey = CacheKey::productSummateKey('sum-send.mail_count', $groupKey);
            $otherData['summate'][$sumMateSendMailKey] = array_values($referMailList[$key] ?? []);
            $otherData['summate_type'][$sumMateSendMailKey] = Constant::DATA_TYPE_ARRAY;

            $sumMateMessageCompanyKey = CacheKey::productSummateKey('sum-send.message_company_count', $groupKey);
            $otherData['summate'][$sumMateMessageCompanyKey] = array_values($messageCompanyList[$key] ?? []);
            $otherData['summate_type'][$sumMateMessageCompanyKey] = Constant::DATA_TYPE_ARRAY;

            $sumMateMailCompanyKey = CacheKey::productSummateKey('sum-send.mail_company_count', $groupKey);
            $otherData['summate'][$sumMateMailCompanyKey] = array_values($mailCompanyList[$key] ?? []);
            $otherData['summate_type'][$sumMateMailCompanyKey] = Constant::DATA_TYPE_ARRAY;

            $sumMateEdmCompanyKey = CacheKey::productSummateKey('sum-send.edm_company_count', $groupKey);
            $otherData['summate'][$sumMateEdmCompanyKey] = array_values($edmCompanyList[$key] ?? []);
            $otherData['summate_type'][$sumMateEdmCompanyKey] = Constant::DATA_TYPE_ARRAY;

            $sumMateFollowCompanyKey = CacheKey::productSummateKey('sum-follow.record_company_count', $groupKey);
            $otherData['summate'][$sumMateFollowCompanyKey] = array_values($followCompanyList[$key] ?? []);
            $otherData['summate_type'][$sumMateFollowCompanyKey] = Constant::DATA_TYPE_ARRAY;
        }

        $arr = new Arr($nameResult);
        $arr->setOtherData($otherData);
        return $arr;


    }

    public function getSearchFields()
    {
        return ['company_id'];
    }

}