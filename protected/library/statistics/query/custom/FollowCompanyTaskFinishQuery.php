<?php
/**
 * This file is part of the xiaoman/xiaoman-crm.
 *
 * (c) sevenshi <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled.
 */


namespace common\library\statistics\query\custom;


use common\library\department\DepartmentMember;
use common\library\statistics\Constant;
use common\library\statistics\data_adapter\AbstractDataAdapter;
use common\library\statistics\data_adapter\Arr;
use common\library\statistics\foundation\store\CacheKey;
use common\library\statistics\Helper;
use common\library\statistics\Locator;
use common\library\statistics\query\AbstractQuery;
use common\library\task\TaskConstant;
use common\library\task\TaskList;
use common\library\workflow\WorkflowConstant;

class FollowCompanyTaskFinishQuery extends AbstractQuery
{

    /**
     * @return AbstractDataAdapter
     */
    public function query()
    {
        $locator = Locator::instance();
        $clientId = $locator->getClientId();
        $userId = $locator->getUserId();

        $params = $locator->getReportConfig()->getQueryConfig()->getParams(1);
        $filterFreezeUserFlag = $locator->getFilterFreezeUserFlag();
        $visibleUserId = $params['common.visible']->getValue() ?: [];
        $visibleDepartmentId = $params['common.visible.department_id']->getValue() ?: [];

        // userId为空时去取部门下的UserId
        if (empty($visibleUserId)) {
            $visibleUserId = \common\library\statistics\Helper::getManageAbleUserIdByDepartmentId($clientId, $userId, $filterFreezeUserFlag, $visibleDepartmentId);
        }

        if (empty($visibleUserId)) {
            $visibleUserId = [$userId];
        }


        $taskRuleIds = $params['common.task_rule_id']->getValue() ?: [];
        $startDate = $params['common.deadline']->getValue()['start'] ?: date("Y-m-d", time());
        $endDate = $params['common.deadline']->getValue()['end'] ?: date("Y-m-d", time());
        $createStartDate = $params['common.create_time']->getValue()['start'];
        $createEndDate = $params['common.create_time']->getValue()['end'];



        if (empty($taskRuleIds)) {
            $listQuery = new \common\library\workflow\WorkflowRuleList($clientId);
            $listQuery->setReferType(\Constants::TYPE_COMPANY);
            $listQuery->setDisableFlag(null);
            $listQuery->setSkipPrivilege(true);
            $listQuery->setRuleType(WorkflowConstant::RULE_TYPE_TASK);
            $ruleList = $listQuery->find();

            $taskRuleIds = array_column($ruleList, 'rule_id');
        }

        if (empty($taskRuleIds)) {
            return new Arr([]);
        }

        $taskList = new TaskList($clientId, $userId);
        $taskList->setType(TaskConstant::TASK_TYPE_FOLLOW_CUSTOMER);
        $taskList->setRuleIds($taskRuleIds);
        $taskList->setUserIds($visibleUserId);
        $taskList->setBeginEndTime($startDate);
        $taskList->setNextEndTime($endDate);
        $taskList->setFields('*');
        $taskList->setCreateStartTime($createStartDate);
        $taskList->setCreateEndTime($createEndDate);
        $tasks = $taskList->find();

        //目标跟进客户次数
        $completedTaskMap = [];
        //及时跟进客户次数
        $inTimeTaskMap = [];
        //在任务截止时间后完成的任务数
        $timeOutTaskMap = [];
        //未完成任务数
        $unFinishTaskMap = [];
        //忽略任务数
        $ignoreTaskMap = [];
        //平均完成率
        $allTaskTime = [];

        foreach ($tasks as $taskItem) {

            if ($taskItem['status'] == TaskConstant::TASK_STATUS_INCOMPLETE ||
                $taskItem['status'] == TaskConstant::TASK_STATUS_COMPLETED) {

                //跟进客户类型的任务总数 未完成与已经完成的
                if ($taskItem['status'] == TaskConstant::TASK_STATUS_COMPLETED) {

                    //已完成的任务数据
                    $completedTaskMap[$taskItem['user_id']][] = $taskItem['task_id'];

                    //在任务截止时间前完成的任务数
                    if (strtotime($taskItem['finish_time']) <= strtotime($taskItem['end_time'])) {
                        $inTimeTaskMap[$taskItem['user_id']][] = $taskItem['task_id'];
                        //在任务截止时间后完成的任务数
                    } else {
                        $timeOutTaskMap[$taskItem['user_id']][] = $taskItem['task_id'];
                    }

                    //任务完成时间
                    $recentFollowUpTime = ($taskItem['recent_follow_up_time'] == '1970-01-01 00:00:00') ? $taskItem['create_time'] : $taskItem['recent_follow_up_time'];
                    if (isset($allTaskTime[$taskItem['user_id']])) {
                        $allTaskTime[$taskItem['user_id']] += strtotime($taskItem['finish_time']) - strtotime($recentFollowUpTime);
                    } else {
                        $allTaskTime[$taskItem['user_id']] = strtotime($taskItem['finish_time']) - strtotime($recentFollowUpTime);
                    }

                }

                //未完成任务数
                if ($taskItem['status'] == TaskConstant::TASK_STATUS_INCOMPLETE) {
                    $unFinishTaskMap[$taskItem['user_id']][] = $taskItem['task_id'];
                }
            }

            //忽略任务数
            if ($taskItem['status'] == TaskConstant::TASK_STATUS_IGNORE) {
                $ignoreTaskMap[$taskItem['user_id']][] = $taskItem['task_id'];
            }
        }


        $data = [];
        //跳转
        $otherData = [];
        foreach ($visibleUserId as $userId) {

            $completeTaskCount = count($completedTaskMap[$userId] ?? []);

            $data[] = [
                'task.user_id' => $userId,
                'task.target_company_follow_count' => count($completedTaskMap[$userId] ?? []) + count($unFinishTaskMap[$userId] ?? []),
                'task.in_time_company_follow_count' => count($inTimeTaskMap[$userId] ?? []),
                'task.timeout_company_follow_count' => count($timeOutTaskMap[$userId] ?? []),
                'task.todo_company_follow_count' => count($unFinishTaskMap[$userId] ?? []),
                'task.average_company_follow_time' => $completeTaskCount == 0 ? 0 : $allTaskTime[$userId] ?? 0,
                'task.ignore_task_count' => count($ignoreTaskMap[$userId] ?? []),
                'task.complete_task_count' => $completeTaskCount
            ];

            $summateKey =  CacheKey::productSummateKey('sum-task.target_company_follow_count', CacheKey::productGroupKey('task.user_id',$userId));
            $otherData['summate'][$summateKey] = array_merge($completedTaskMap[$userId]??[],$unFinishTaskMap[$userId] ?? []);
            $otherData['summate_type'][$summateKey] = Constant::DATA_TYPE_ARRAY;

            $summateKey =  CacheKey::productSummateKey('sum-task.in_time_company_follow_count', CacheKey::productGroupKey('task.user_id',$userId));
            $otherData['summate'][$summateKey] = $inTimeTaskMap[$userId]??[];
            $otherData['summate_type'][$summateKey] = Constant::DATA_TYPE_ARRAY;

            $summateKey =  CacheKey::productSummateKey('sum-task.timeout_company_follow_count', CacheKey::productGroupKey('task.user_id',$userId));
            $otherData['summate'][$summateKey] = $timeOutTaskMap[$userId]??[];
            $otherData['summate_type'][$summateKey] = Constant::DATA_TYPE_ARRAY;

            $summateKey =  CacheKey::productSummateKey('sum-task.todo_company_follow_count', CacheKey::productGroupKey('task.user_id',$userId));
            $otherData['summate'][$summateKey] =$unFinishTaskMap[$userId]??[];
            $otherData['summate_type'][$summateKey] = Constant::DATA_TYPE_ARRAY;

            $summateKey =  CacheKey::productSummateKey('sum-task.ignore_task_count', CacheKey::productGroupKey('task.user_id',$userId));
            $otherData['summate'][$summateKey] = $ignoreTaskMap[$userId]??[];
            $otherData['summate_type'][$summateKey] = Constant::DATA_TYPE_ARRAY;

        }
        $dataAdapter =  new Arr($data);
        $dataAdapter->setOtherData($otherData);
        return $dataAdapter;

    }
}