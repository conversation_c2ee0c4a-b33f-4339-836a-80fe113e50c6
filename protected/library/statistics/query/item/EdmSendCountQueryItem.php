<?php
/**
 * Created by PhpStorm.
 * User: shisiying
 * Date: 2019-11-18
 * Time: 10:17
 */

namespace common\library\statistics\query\item;
use common\library\edm\EdmTaskList;
use common\library\statistics\Constant;
use common\library\statistics\Helper;

class EdmSendCountQueryItem extends AbstractQueryItem
{

    function query()
    {
        if(empty($this->selectUserIds)) {
            return [[],[]];
        }

        $userIds = $this->selectUserIds;
        $db = \ProjectActiveRecord::getDbByClientId($this->clientId);

        //营销发送数 发送的就算
        $edmTaskList = new EdmTaskList($this->clientId);
        $edmTaskList->setUserId($userIds);
        $edmTaskList->setStartBeginTime($this->startDate);
        $edmTaskList->setStartEndTime($this->endDate);

        list($where, $sqlParams) = $edmTaskList->buildParams();


	    $dateFormatVar = Helper::dateFormatVar($this->groupReportDateType);

	    $reportField = $this->groupReportKey == 'report_date' ? ' DATE_FORMAT(start_time, \'' . $dateFormatVar . '\') AS report_date' : 'user_id as statistic_user_id';

	    $reportGroup = $this->groupReportKey == 'user_id' ? 'statistic_user_id' : $this->groupReportKey;

	    $sql  = "select sum(send_to_count) as send_to_count, {$reportField} from tbl_group_mail_task  as task  where {$where} group by {$reportGroup}";
        $list = $db->createCommand($sql)->queryAll(true, $sqlParams);
        $data = array_column($list, 'send_to_count', $reportGroup);

        $totalSql  = "select sum(send_to_count) as send_to_count from tbl_group_mail_task  as task  where {$where}";
        $this->total = $db->createCommand($totalSql)->queryScalar($sqlParams)?:0;

        return [$data,$data];
    }

	protected function buildItemReferData($groupKey, $groupData)
    {

	    if (empty($groupData))
		    return;


	    $user = $this->selectUserIds;


	    if ($this->groupReportKey == 'user_id') {

		    $user = [$groupKey];
	    }

        $sql = " statistics_alias.client_id={$this->clientId} and  statistics_alias.user_id in(" . implode(',', $user) . ")";

	    if ($this->groupReportKey == 'report_date') {

		    [$start, $end] = Helper::formatedDate($this->groupReportDateType, $groupKey);

		    $sql .= " AND statistics_alias.create_time >= '{$start}'";

		    $sql .= " AND statistics_alias.create_time <'{$end}' ";

	    }else {

		    if ($this->startDate) {
			    $start = date('Y-m-d 00:00:00', strtotime($this->startDate));
			    $sql .= " AND statistics_alias.create_time >= '{$start}'";
		    }

		    if ($this->endDate) {
			    $end = date('Y-m-d 00:00:00', strtotime('+1 day', strtotime($this->endDate)));
			    $sql .= " AND statistics_alias.create_time <'{$end}' ";
		    }

	    }

	    $summateKey = $this->getSummateKey($this->groupReportKey == 'report_date' ? Helper::formatFieldDate($this->groupReportDateType, $groupKey) : $groupKey);
        $this->referData['summate'][$summateKey] = $sql;
        $this->referData['summate_type'][$summateKey] =  Constant::DATA_TYPE_SQL;

    }

    protected function buildTotalReferData()
    {
        $sql = " statistics_alias.client_id={$this->clientId} and statistics_alias.user_id in (".implode(',',$this->selectUserIds).")";

        if( $this->startDate )
        {
            $start = date('Y-m-d 00:00:00',strtotime($this->startDate));
            $sql .= " AND statistics_alias.create_time >= '{$start}'";
        }

        if( $this->endDate )
        {
            $end = date('Y-m-d 00:00:00',strtotime('+1 day',strtotime($this->endDate)));
            $sql .= " AND statistics_alias.create_time <'{$end}' ";
        }

        $totalKey = $this->getTotalKey();
        $this->referData['total'][$totalKey] = $sql;
        $this->referData['total_type'][$totalKey] =  Constant::DATA_TYPE_SQL;
    }
}
