<?php
/**
 * Copyright (c) 2012 - 2019 Xiao<PERSON>.All Rights Reserved
 * Author: nuxse
 * Data: 2019/3/13
 */

namespace common\library\statistics\data_adapter;

/**
 * Class Sql
 * @package common\library\statistics\data_adapter
 *
 * 数据适配 sql 模式
 */
class Sql extends AbstractDataAdapter
{

    protected $sql;

    protected $field;
    protected $table;
    protected $groupBy;
    protected $orderBy;
    protected $where;
    protected $params;

    public function getData()
    {
        $sql = "select {$this->field} from {$this->table} where {$this->where} ";
        $sql .= $this->groupBy ? "group by {$this->groupBy} " : '';
        $sql .= $this->orderBy ? "order by {$this->orderBy} " : '';
        return $sql;
    }

    /**
     * @param mixed $field
     */
    public function setField($field)
    {
        if(is_array($field)) {
            $field = implode(',',$field);
        }
        $this->field = $field;
    }

    /**
     * @param mixed $table
     */
    public function setTable($table)
    {
        $this->table = $table;
    }

    /**
     * @param mixed $groupBy
     */
    public function setGroupBy($groupBy)
    {
        $this->groupBy = $groupBy;
    }

    /**
     * @param mixed $orderBy
     */
    public function setOrderBy($orderBy)
    {
        $this->orderBy = $orderBy;
    }

    /**
     * @param mixed $where
     */
    public function setWhere($where)
    {
        $this->where = $where;
    }

    /**
     * @param mixed $field
     */
    public function addField($field)
    {
        $this->field .= ',' . $field;
    }

    /**
     * @param mixed $field
     */
    public function addGroup($field)
    {
        $this->groupBy .= ',' . $field;
    }

    /**
     * @param mixed $params
     */
    public function setParams($params)
    {
        $this->params = $params;
    }

    /**
     * @return mixed
     */
    public function getParams()
    {
        return $this->params;
    }
}
