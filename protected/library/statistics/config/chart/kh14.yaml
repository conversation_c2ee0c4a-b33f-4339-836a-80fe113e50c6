reportObject: company:order
attributes: # 后续可能保存在数据库中
  key: kh14
  title: '新客户成交转化情况'
  desc: '了解新客户成交转化率、客单价'
  type: group
  name: '新客户成交转化情况'
  relevance:
    - key: kh14
      name: 时间
    - key: kh15
      name: 跟进人
    - key: kh16
      name: 国家地区
    - key: kh17
      name: 客户来源
    - key: kh18
      name: 来源店铺
  exchangeRelevance:
    - key: kh14
      tab_key: kh14
      type: order
      type_name: 基于销售订单
    - key: kh24
      tab_key: kh14
      type: opportunity
      type_name: 基于商机


fieldConfig:
  renderFields:
    company.archive_time:
      type: date
      show: true
      name: 建档时间
    order.amount:
      type: currency
  queryFields: [company.company_id,company.client_id,company.user_id,company.archive_time,performance.company_id,performance.indicator_value,performance.refer_id]

queryConfig:
  variable:
    - field:  common.visible
      comment: 查看范围
      type: select_visible_user_id
      multiple: 1
      value: []

    - field: company.archive_time
      comment: 建档时间
      type: date
      value:
        start:
        end:
      multiple: 0
      period: d
      default: 30
      continuous: 1

    - field: common.select_cycle
      comment: 周期
      type: select
      options:
        - label: 按天
          value: day
        - label: 按周
          value: week
        - label: 按月
          value: month
        - label: 按季度
          value: season
        - label: 按半年
          value: half_a_year
      value: day
      multiple: 0

    - field: company.ali_store_id
      comment: 来源店铺
      type: 7
      value:

    - field: company.trail_status
      comment: 客户阶段
      type: select_trail_status
      value:
      multiple: 1
    - field: company.biz_type
      comment: 客户类型
      type: select_biz_type
      value:
      multiple: 0
    - field: company.country
      comment: 国家地区
      type: select_country
      value:
      multiple: 1
    - field: company.origin_list
      comment: 客户来源
      type: select_origin_list
      value:
      multiple: 1
    - field: company.star
      comment: 客户星级
      type: select_star
      value:
      multiple: 1
    - field: company.group_id
      comment: 客户分组
      type: select_company_group
      value:
      multiple: 1
    - field: company.pool_id
      comment: 公海分组
      type: select_company_pool
      value:
      multiple: 1

  flow:
    set:
      - node:
          - object: company
            type: base
            limit:
              nodeLimit:
              setLimit:
              filter:
                outer:
                  - field: start_archive_time
                    operation: gt
                    referenceValue: :variable
                    logic: AND

                  - field: end_archive_time
                    operation: lt
                    referenceValue: :variable
                    logic: AND

                  - field: ali_status_id
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: visible.user_id
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                inner:
            merge:
              mode:
              connect:
                parentNodeId:
                currentNodeId:

          - object: performance
            type: refer
            limit:
              nodeLimit:
                id: company_id
                range: company.company_id
              setLimit:
              filter:
                outer:

                inner:
            merge:
              mode: union
              connect:
                parentNodeId: company.company_id
                currentNodeId: performance.company_id

formatConfig:
  decoratorConfig:
    - type: field
      field: company.archive_time
      field_type: dateRange
      extra:
        format: select
    - type: field
      field: performance.indicator_value
      field_type: currency
      extra:
        currencyField: order.currency # 币种关联字段
        currency:  # 币种
        exchangeRate:  # 汇率
          CNY:  # 折人民币汇率数值*100
          USD:  # 折美元汇率数值*100
        exchangeRateField:  # 汇率关联字段
          CNY: order.exchange_rate # 折人民币汇率字段
          USD: order.exchange_rate_usd # 折美元汇率字段

  groupConfig:
    - type: row # 组行 , 组列
      field: company.archive_time
      field_type: date

  orderConfig:
    - key: company.archive_time
      field:
      order: desc

  summateConfig:
    - method: row # 计数
      title: '新建客户数'
      field:  company.company_id
      unique: company.company_id
      unique_strict: true
      referListId: company.company_id
      referList: companyList
    - method: row # 计数
      title: '成交订单客户数'
      field:  performance.company_id
      unique: performance.company_id
      unique_strict: true
      referListId: performance.company_id
      referList: companyList
      tip: '统计符合目标规则【成交订单金额】的销售订单关联的客户'
    - method: sum #
      field: performance.indicator_value
      title: '成交订单金额'
      tip: '统计符合目标规则【成交订单金额】的销售订单金额'
    - method: formula
      field: transform
      title: 转化率
      tip: '成交订单客户数/新建客户数'
      field_type: percent
      formula: '{row-performance.company_id} / {row-company.company_id}'
      formula_fields: [row-performance.company_id,row-company.company_id]
      percentPrecision: 2
    - method: formula
      field: avg_company_amount
      title: 客单价
      tip: '成交订单金额/成交订单客户数'
      field_type: float
      formula: '{sum-performance.indicator_value} / {row-performance.company_id}'
      formula_fields: [ sum-order.amount,row-performance.company_id]
      percentPrecision: 2


chartConfig:
  chartList:
    - chartType: 'horizontal-bar'
      group:
        - company.archive_time
      summaries:
        - row-company.company_id
      option: []




