reportObject: invoice_product
attributes: # 后续可能保存在数据库中
  key: cp1
  title: '产品销售排行'
  desc: '按产品/产品规格/产品类目/产品分组/产品型号各维度的销售额/销量排行。数据范围：你有权限查看的销售订单。'
  type: group
  name: '产品销售排行'
  relevance:
  - key: cp1
    name: 产品
  - key: cp25
    name: 产品规格
  - key: cp23
    name: 产品型号
  - key: cp2
    name: 产品类目
  - key: cp3
    name: 产品分组
  exchangeRelevance:
    - key: cp1
      tab_key: cp1
      type: order
      type_name: 基于销售订单
    - key: cp14
      tab_key: cp1
      type: opportunity
      type_name: 基于商机

sceneConfig:
  companyDetail:
    skip_permission: true


fieldConfig:
  renderFields:
    order.performance_date:
      type: date
      show: true
      name:
      replace: 2 # 0：不替换 1:仅替换名称 2:替换名称和查询字段
      tip: '目标规则【成交订单金额】的计入时间依据，点击查看'
    product.product_no:
      type: int #字段类型
      show: true #是否显示 默认false
      refer:
        type: detail
        refer_id: product_id
       #type: list
    product.name:
      type:
    invoice_product.count:
      type: int #字段类型
    invoice_product.amount:
      type: int
    order.order_id:
      type: int
#    order.account_date:
#      type: date
    order.exchange_rate_usd:
      type:
    order.exchange_rate:
      type:
    order.currency:
      type: currency

  queryFields: [invoice_product.product_id,invoice_product.type,invoice_product.refer_id,invoice_product.count,invoice_product.invoice_amount,invoice_product.amount,invoice_product.user_id,invoice_product.client_id,order.order_id,order.performance_date,order.order_no,order.company_id,order.exchange_rate_usd,order.exchange_rate,order.currency,order.user_id,order.client_id,company.company_id,company.archive_time,company.client_id,company.user_id,product.product_id,product.serial_id,product.product_no,product.name,product.group_id,product.category_ids,product.client_id]
#  queryFields: [invoice_product.product_id,invoice_product.refer_id,invoice_product.count,invoice_product.amount,order.order_id,order.company_id,order.exchange_rate_usd,order.exchange_rate,order.currency,company.company_id,product.product_id,product.serial_id,product.product_no,product.name]

queryConfig:
  variable:
  - field:  common.visible
    comment: 查看范围
    type: select_visible_user_id
    multiple: 1
    value: []

  - field: order.performance_date
    type: date
    multiple: 0
    value:
      start:
      end:
    period: d
    default: 365
    continuous: 1

  - field: order.status
    comment: 订单状态(销售订单)
    type: 3
    value: []
    multiple: 1
    continuous: 1

  - field: order.currency
    type: 3
    value:
    continuous: 1

  - field: order.create_time
    type: date
    value:
      start:
      end:
    continuous: 1

  - field: order.country
    type: 3
    value:
    continuous: 1

  - field: order.ali_store_id
    type: 7
    value:
    continuous: 1

  - field: order.archive_type
    type: 3
    value:
    continuous: 1

  - field: order.ali_status_name
    comment: 阿里订单状态(销售订单)
    type: 7
    value: []
    multiple: 1
    continuous: 1

  - field: order.source_type
    type: 3
    value:
    continuous: 1

  - field: order.price_contract
    type: 3
    value:
    continuous: 1

  - field: order.receive_remittance_way
    type: 3
    value:
    continuous: 1

  - field: order.fulfillment_channel
    type: 3
    value:
    continuous: 1

  - field: invoice_product.product_id
    comment: 产品
    type: select_product
    multiple: 0
    value:
  - field: product.category_ids
    comment: 产品类目
    type: select_category_id
    multiple: 1
    value:
  - field: product.group_id
    comment: 产品分组
    type: select_product_group
    multiple: 0
    value:

  - field: company.origin_list
    comment: 客户来源
    type: select_origin_list
    value:
    multiple: 0

  - field: company.star
    comment: 客户星级
    type: select_star
    value:
    multiple: 1

  - field: company.group_id
    comment: 客户分组
    type: select_company_group
    value:
    multiple: 0

  - field: company.serial_id
    type: 1
    value:
    continuous: 1

  - field: company.name
    type: 1
    value:
    continuous: 1

  flow: # 描述整个查询流程
    set:
     - node: # 定义每个查询节点
        - object: order
          type: base #refer 引用  base基础
          limit: #数据限制
            nodeLimit:
              id:
              range:
            setLimit:
              id:
              range:
            filter: #过滤器限制
              outer:
                - field: visible.user_id
                  operation: eq
                  referenceValue: :variable
                  logic: AND
                - field: start_account_date
                  operation: gt
                  referenceValue: :variable
                  logic: AND
                - field: end_account_date
                  operation: lt
                  referenceValue: :variable
                  logic: AND
                - field: status
                  operation: eq
                  referenceValue: :variable
                  logic: AND

                - field: currency
                  operation: eq
                  referenceValue: :variable
                  logic: AND

                - field: start_create_time
                  operation: gt
                  referenceValue: :variable
                  logic: AND

                - field: end_create_time
                  operation: lt
                  referenceValue: :variable
                  logic: AND

                - field: start_performance_date
                  operation: gt
                  referenceValue: :variable
                  logic: AND

                - field: end_performance_date
                  operation: lt
                  referenceValue: :variable
                  logic: AND

                - field: country
                  operation: eq
                  referenceValue: :variable
                  logic: AND

                - field: receive_remittance_way
                  operation: eq
                  referenceValue: :variable
                  logic: AND

                - field: fulfillment_channel
                  operation: eq
                  referenceValue: :variable
                  logic: AND

                - field: price_contract
                  operation: eq
                  referenceValue: :variable
                  logic: AND

                - field: source_type
                  operation: eq
                  referenceValue: :variable
                  logic: AND

                - field: ali_status_name
                  operation: eq
                  referenceValue: :variable
                  logic: AND

                - field: archive_type
                  operation: eq
                  referenceValue: :variable
                  logic: AND

                - field: ali_store_id
                  operation: eq
                  referenceValue: :variable
                  logic: AND
              inner:
          merge:
            mode:
            connect:
              parentNodeId:
              currentNodeId:
        - object: invoice_product
          type: base      #不可使用refer,防止后续可能出现union查询
          limit:
            nodeLimit: #来自node结点的数据限制
              id: refer_id
              range: order.order_id
            setLimit:  #来自set节点的数据限制
              id:
              range:
            filter: #过滤器限制
              outer:
                - field: product_id
                  operation: eq
                  referenceValue: :variable
                  logic: AND
              inner:
                - field: type
                  operation: eq
                  referenceValue: 2 #订单
                  logic: AND
                - field: combine_record_id
                  operation: eq
                  referenceValue: 0
                  logic: AND
                - field: sku_id
                  operation: gt
                  referenceValue: 0
                  logic: AND

          merge:
            mode: intersection
            connect:
              parentNodeId: order.order_id
              currentNodeId: invoice_product.refer_id
        - object: company
          type: refer
          limit:
            nodeLimit: #来自外部节点的数据限制
              id: company_id
              range: order.company_id
            setLimit: #来自内部结点的数据限制
            filter: #过滤器限制
              outer:
                - field: origin_list
                  operation: eq
                  referenceValue: :variable
                  logic: AND

                - field: star
                  operation: eq
                  referenceValue: :variable
                  logic: AND

                - field: group_id
                  operation: eq
                  referenceValue: :variable
                  logic: AND

                - field: name
                  operation: eq
                  referenceValue: :variable
                  logic: AND

                - field: serial_id
                  operation: eq
                  referenceValue: :variable
                  logic: AND


              inner:
          merge:
            mode: intersection
            connect:
              parentNodeId: order.company_id
              currentNodeId: company.company_id

        - object: product
          type: refer
          limit:
            nodeLimit: #来自node节点的数据限制
              id: product_id
              range: invoice_product.product_id
            setLimit: #来自set结点的数据限制
            filter: #过滤器限制
              outer:
              - field: category_ids
                operation: eq
                referenceValue: :variable
                logic: AND
              - field: group_id
                operation: eq
                referenceValue: :variable
                logic: AND
              inner:
          merge:
            mode: intersection
            connect:
              parentNodeId: invoice_product.product_id
              currentNodeId: product.product_id

formatConfig:
  decoratorConfig:
  - type: field
    field: product.product_no
    field_type: serialId
    extra:
      nameField: product.name
  - type: field
    field: invoice_product.amount
    field_type: currency
    extra:
      currencyField: order.currency # 币种关联字段
      currency:  # 币种
      exchangeRate:  # 汇率
        CNY:  # 折人民币汇率数值*100
        USD:  # 折美元汇率数值*100
      exchangeRateField:  # 汇率关联字段
        CNY: order.exchange_rate # 折人民币汇率字段
        USD: order.exchange_rate_usd # 折美元汇率字段

  groupConfig:
  - type: row # 组行 , 组列
    field: product.product_no
    field_type:
    referId: product.product_id
    referDetail: productDetail

  orderConfig:
  - key: product.product_no
    field: sum-invoice_product.amount
    order: desc


  summateConfig:
  - method: sum #
    field: invoice_product.count
    title: 产品数量
  - method: sumPercent # 总和占比
    field:  invoice_product.count
    title: 产品数量占比
  - method: sum #
    field: invoice_product.amount
    title: 产品金额
  - method: sumPercent # 总和占比ß
    field:  invoice_product.amount
    title: 产品金额占比
  - method: row # 计数
    title: 关联销售订单数
    field: order.order_id
    unique: order.order_id   #去重字段
    referListId: order.order_id
    referList: orderList

chartConfig:
  chartList:
  - chartType: 'horizontal-bar' #柱状图：vertical-bar，条形图：horizontal-bar
    group:
    - product.product_no
    summaries:
    - sum-invoice_product.amount
    option: []

eventConfig:
  - event: onInit
    handler: SelectAdvance








