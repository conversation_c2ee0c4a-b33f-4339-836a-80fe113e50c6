reportObject: order_profit
attributes: # 后续可能保存在数据库中
  key: opp1
  title: '毛利-产品分布情况'
  desc: '按产品或产品相关属性查看毛利分布情况'
  name: '毛利-产品分布情况'
  type: group
  currency: CNY
  relevance:
    - key: opp1
      name: 产品
    - key: opp2
      name: 产品规格
    - key: opp3
      name: 产品型号
    - key: opp4
      name: 产品类目
    - key: opp5
      name: 产品分组


fieldConfig:
  renderFields:
    product.product_no:
      type: int #字段类型
      show: true #是否显示 默认false
      refer:
        type: detail
        refer_id: product_id
        #type: list
    product.name:
      type:
    invoice_product.count:
      type: int #字段类型
    order.order_id:
      type: int
    order.account_date:
      type: date

  queryFields: [invoice_product.product_id,invoice_product.refer_id,invoice_product.unit_price,invoice_product.count,invoice_product.gross_margin_cny,order.order_id,order.company_id,order.exchange_rate,company.company_id,product.product_id,product.serial_id,product.product_no,product.name]

queryConfig:
  variable:
    - field:  common.visible
      comment: 查看范围
      type: select_visible_user_id
      multiple: 1
      value: []

    - field: order.account_date
      comment: 订单日期
      type: date
      multiple: 0
      value:
        start:
        end:
      period: y
      default: 1
      continuous: 1

    - field: order.status
      comment: 订单状态(销售订单)
      type: 3
      value: []
      multiple: 1
      continuous: 1

    - field: order.currency
      type: 3
      value:
      continuous: 1

    - field: order.create_time
      type: date
      value:
        start:
        end:
      continuous: 1

    - field: order.country
      type: 3
      value:
      continuous: 1

    - field: order.ali_store_id
      type: 7
      value:
      continuous: 1

    - field: order.archive_type
      type: 3
      value:
      continuous: 1

    - field: order.ali_status_name
      comment: 阿里订单状态(销售订单)
      type: 7
      value: []
      multiple: 1
      continuous: 1

    - field: order.tax_refund_type
      type: 3
      value: []
      multiple: 1
      continuous: 1

    - field: order.source_type
      type: 3
      value:
      continuous: 1

    - field: order.price_contract
      type: 3
      value:
      continuous: 1

    - field: order.receive_remittance_way
      type: 3
      value:
      continuous: 1

    - field: order.fulfillment_channel
      type: 3
      value:
      continuous: 1

    - field: invoice_product.product_id
      comment: 产品
      type: select_product
      multiple: 0
      value:
    - field: product.category_ids
      comment: 产品类目
      type: select_category_id
      multiple: 1
      value:
    - field: product.group_id
      comment: 产品分组
      type: select_product_group
      multiple: 0
      value:

    - field: company.origin
      comment: 客户来源
      type: select_origin
      value:
      multiple: 0

    - field: company.star
      comment: 客户星级
      type: select_star
      value:
      multiple: 1

    - field: company.group_id
      comment: 客户分组
      type: select_company_group
      value:
      multiple: 0

    - field: company.serial_id
      type: 1
      value:
      continuous: 1

    - field: company.name
      type: 1
      value:
      continuous: 1

  flow: # 描述整个查询流程
    set:
      - node: # 定义每个查询节点
          - object: order
            type: base #refer 引用  base基础
            limit: #数据限制
              nodeLimit:
                id:
                range:
              setLimit:
                id:
                range:
              filter: #过滤器限制
                outer:
                  - field: visible.user_id
                    operation: eq
                    referenceValue: :variable
                    logic: AND
                  - field: start_account_date
                    operation: gt
                    referenceValue: :variable
                    logic: AND
                  - field: end_account_date
                    operation: lt
                    referenceValue: :variable
                    logic: AND
                  - field: status
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: currency
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: start_create_time
                    operation: gt
                    referenceValue: :variable
                    logic: AND

                  - field: end_create_time
                    operation: lt
                    referenceValue: :variable
                    logic: AND

                  - field: country
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: receive_remittance_way
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: fulfillment_channel
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: price_contract
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: source_type
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: ali_status_name
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: archive_type
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: ali_store_id
                    operation: eq
                    referenceValue: :variable
                    logic: AND
                inner:
            merge:
              mode:
              connect:
                parentNodeId:
                currentNodeId:
          - object: invoice_product
            type: base      #不可使用refer,防止后续可能出现union查询
            limit:
              nodeLimit: #来自node结点的数据限制
                id: refer_id
                range: order.order_id
              setLimit:  #来自set节点的数据限制
                id:
                range:
              filter: #过滤器限制
                outer:
                  - field: product_id
                    operation: eq
                    referenceValue: :variable
                    logic: AND
                inner:
                  - field: type
                    operation: eq
                    referenceValue: 2 #订单
                    logic: AND
                  - field: combine_record_id
                    operation: eq
                    referenceValue: 0
                    logic: AND
                  - field: sku_id
                    operation: gt
                    referenceValue: 0
                    logic: AND

            merge:
              mode: intersection
              connect:
                parentNodeId: order.order_id
                currentNodeId: invoice_product.refer_id

          - object: company
            type: refer
            limit:
              nodeLimit: #来自外部节点的数据限制
                id: company_id
                range: order.company_id
              setLimit: #来自内部结点的数据限制
              filter: #过滤器限制
                outer:
                  - field: origin
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: star
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: group_id
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: name
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: serial_id
                    operation: eq
                    referenceValue: :variable
                    logic: AND


                inner:
            merge:
              mode: intersection
              connect:
                parentNodeId: order.company_id
                currentNodeId: company.company_id

          - object: product
            type: refer
            limit:
              nodeLimit: #来自node节点的数据限制
                id: product_id
                range: invoice_product.product_id
              setLimit: #来自set结点的数据限制
              filter: #过滤器限制
                outer:
                  - field: category_ids
                    operation: eq
                    referenceValue: :variable
                    logic: AND
                  - field: group_id
                    operation: eq
                    referenceValue: :variable
                    logic: AND
                inner:
            merge:
              mode: intersection
              connect:
                parentNodeId: invoice_product.product_id
                currentNodeId: product.product_id

formatConfig:
  decoratorConfig:
    - type: field
      field: product.product_no
      field_type: serialId
      extra:
        nameField: product.name
    - type: bucket
      title: invoice_product.sale_amount
      field:
      bucket_type: formula
      extra:
        formula: '{invoice_product.unit_price} * {invoice_product.count} * {order.exchange_rate} / 100'
        formula_fields: [invoice_product.unit_price,invoice_product.count,order.exchange_rate]
        minValue:
    - type: bucket
      title: invoice_product.gross_margin_cny
      field:
      bucket_type: formula
      extra:
        formula: '{invoice_product.gross_margin_cny} * {invoice_product.count}'
        formula_fields: [invoice_product.gross_margin_cny]
        minValue:

  groupConfig:
    - type: row # 组行 , 组列
      field: product.product_no
      field_type:
      referId: product.product_id
      referDetail: productDetail

  orderConfig:
    - key: product.product_no
      field: sum-invoice_product.gross_margin_cny
      order: desc


  summateConfig:
    - method: sum
      title: 产品毛利总和
      field: invoice_product.gross_margin_cny
      field_type: currency
    - method: sum
      title: 订单产品数量
      field: invoice_product.count
    - method: divider
      title: 产品毛利均值
      tip: '∑订单产品毛利/∑订单产品数量'
      field: invoice_product.gross_margin_cny
      denominator_field: invoice_product.count
      field_type: currency
    - method: sum
      title: 订单产品销售金额
      field: invoice_product.sale_amount
      field_type: currency
    - method: percent
      title: 产品毛利率
      tip: '产品毛利总和/订单产品销售金额总和'
      field: invoice_product.gross_margin_cny
      denominator_field: invoice_product.sale_amount
    - method: row # 计数
      title: 订单数
      field: order.order_id
      unique: order.order_id    #去重字段
      referListId: order.order_id
      referList: orderList

  totalConfig:
    detail:
      switch: true
      default: false
    subtotal:
      switch: true
    total:
      switch: true
    hide_zero:
      switch: true
      default: true
    showSummaries: [sum-invoice_product.gross_margin_cny, sum-invoice_product.count, divider-invoice_product.gross_margin_cny, sum-invoice_product.sale_amount, percent-invoice_product.gross_margin_cny, row-order.order_id]

chartConfig:
  chartList:
    - chartType: 'horizontal-bar' #柱状图：vertical-bar，条形图：horizontal-bar
      group:
        - product.product_no
      summaries:
        - sum-invoice_product.gross_margin_cny
      option: []

eventConfig:
  - event: onInit
    handler: SelectAdvance
