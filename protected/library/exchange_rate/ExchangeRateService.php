<?php
/**
 * Created by PhpStorm.
 * User: rivers
 * Date: 2019/3/21
 * Time: 11:28 AM
 */

namespace common\library\exchange_rate;
use common\library\account\Client;
use common\library\account\UserInfo;
use common\library\exchange_rate\anti_corruption_layer\ExchangeRateParams;
use common\library\history\setting\Helper;
use common\library\history\setting\ItemSettingHistoryCompare;
use common\library\util\Arr;
use common\models\admin\CustomExchangeRate;
use ExchangeRate;
use LogUtil;
use RuntimeException;

/**
 * Class ExchangeRateService1
 * @package common\library\exchange_rate
 */

class ExchangeRateService
{
    const CNY = 'CNY';
    const USD = 'USD';

    public static $currencyMap = [
        'CNY' => [
            'USD' => ['name' => '美元','country_code' => 'US'],
            'EUR' => ['name' => '欧元','country_code' => 'EU'],
            'HKD' => ['name' => '港币','country_code' => 'HK'],
            'JPY' => ['name' => '日元','country_code' => 'JP'],
            'GBP' => ['name' => '英镑','country_code' => 'GB'],
            'AUD' => ['name' => '澳大利亚元','country_code' => 'AU'],
            'CAD' => ['name' => '加拿大元','country_code' => 'CA'],
            'THB' => ['name' => '泰国铢','country_code' => 'TH'],
            'SGD' => ['name' => '新加坡元','country_code' => 'SG'],
            'DKK' => ['name' => '丹麦克朗','country_code' => 'DK'],
            'NOK' => ['name' => '挪威克朗','country_code' => 'NO'],
            'SEK' => ['name' => '瑞典克朗','country_code' => 'SE'],
            'CHF' => ['name' => '瑞士法郎','country_code' => 'CH'],
            'NTD' => ['name' => '新台币','country_code' => 'TW'],
            'ZAR' => ['name' => '南非兰特','country_code' => 'ZA'],
            'RUB' => ['name' => '卢布','country_code' => 'RU'],
            'PHP' => ['name' => '菲律宾比索','country_code' => 'PH'],
            'MYR' => ['name' => '林吉特','country_code' => 'MY'],
            'MOP' => ['name' => '澳门元','country_code' => 'MO'],
            'KRW' => ['name' => '韩国元','country_code' => 'KR'],
            'NZD' => ['name' => '新西兰元','country_code' => 'NZ'],
            'BRL' => ['name' => '巴西雷亚尔','country_code' => 'BR'],
            'IDR' => ['name' => '印尼盾','country_code' => 'ID'],
            'VND' => ['name' => '越南盾','country_code' => 'VN'],
            'MXN' => ['name' => '墨西哥元','country_code' => 'MEX'],

            'ALL' => ['name' => '阿尔巴尼亚列克', 'country_code' => 'AL'],
            'AMD' => ['name' => '亚美尼亚德拉姆', 'country_code' => 'AM'],
            'ANG' => ['name' => '荷属安的列斯盾', 'country_code' => 'ANT'],
            'AOA' => ['name' => '安哥拉宽扎', 'country_code' => 'AO'],
            'ARS' => ['name' => '阿根廷比索', 'country_code' => 'AR'],
            'AWG' => ['name' => '阿鲁巴弗罗林', 'country_code' => 'AW'],
            'AZN' => ['name' => '阿塞拜疆马纳特', 'country_code' => 'AZ'],
            'BAM' => ['name' => '波黑可兑换马克', 'country_code' => 'BA'],
            'BBD' => ['name' => '巴巴多斯元', 'country_code' => 'BB'],
            'BDT' => ['name' => '孟加拉塔卡', 'country_code' => 'BD'],
            'BGN' => ['name' => '保加利亚列弗', 'country_code' => 'BG'],
            'BHD' => ['name' => '巴林第纳尔', 'country_code' => 'BH'],
            'BIF' => ['name' => '布隆迪法郎', 'country_code' => 'BI'],
            'BMD' => ['name' => '百慕大元', 'country_code' => 'BM'],
            'BND' => ['name' => '文莱元', 'country_code' => 'BN'],
            'BOB' => ['name' => '玻利维亚诺', 'country_code' => 'BO'],
            'BOV' => ['name' => '玻利维亚Mvdol', 'country_code' => 'BO'],
            'BSD' => ['name' => '巴哈马元', 'country_code' => 'BS'],
            'BTN' => ['name' => '不丹努尔特鲁姆', 'country_code' => 'BT'],
            'BWP' => ['name' => '博茨瓦纳普拉', 'country_code' => 'BW'],
            'BYN' => ['name' => '白俄罗斯卢布', 'country_code' => 'BY'],
            'BZD' => ['name' => '伯利兹元', 'country_code' => 'BZ'],
            'CDF' => ['name' => '刚果法郎', 'country_code' => 'CD'],
            'XAF' => ['name' => '中非法郎', 'country_code' => 'CF'],
            'CHE' => ['name' => 'WIR欧元', 'country_code' => 'CH'],
            'CHW' => ['name' => 'WIR法郎', 'country_code' => 'CH'],
            'CLP' => ['name' => '智利比索', 'country_code' => 'CL'],
            'CLF' => ['name' => '智利发展单位', 'country_code' => 'CL'],
            'COP' => ['name' => '哥伦比亚比索', 'country_code' => 'CO'],
            'COU' => ['name' => '哥伦比亚实际单位', 'country_code' => 'CO'],
            'CRC' => ['name' => '哥斯达黎加科朗', 'country_code' => 'CR'],
            'CUP' => ['name' => '古巴比索', 'country_code' => 'CU'],
            'CUC' => ['name' => '古巴可兑换比索', 'country_code' => 'CU'],
            'CVE' => ['name' => '佛得角埃斯库多', 'country_code' => 'CV'],
            'CZK' => ['name' => '捷克克朗', 'country_code' => 'CZ'],
            'DJF' => ['name' => '吉布提法郎', 'country_code' => 'DJ'],
            'DOP' => ['name' => '多米尼加比索', 'country_code' => 'DO'],
            'DZD' => ['name' => '阿尔及利亚第纳尔', 'country_code' => 'DZ'],
            'EGP' => ['name' => '埃及镑', 'country_code' => 'EG'],
            'ERN' => ['name' => '厄立特里亚纳克法', 'country_code' => 'ER'],
            'ETB' => ['name' => '埃塞俄比亚比尔', 'country_code' => 'ET'],
            'FJD' => ['name' => '斐济元', 'country_code' => 'FJ'],
            'FKP' => ['name' => '福克兰群岛镑', 'country_code' => 'FK'],
            'GEL' => ['name' => '格鲁吉亚拉里', 'country_code' => 'GE'],
            'GHS' => ['name' => '加纳塞地', 'country_code' => 'GH'],
            'GIP' => ['name' => '直布罗陀镑', 'country_code' => 'GI'],
            'GMD' => ['name' => '冈比亚达拉西', 'country_code' => 'GM'],
            'GNF' => ['name' => '几内亚法郎', 'country_code' => 'GN'],
            'GTQ' => ['name' => '危地马拉格查尔', 'country_code' => 'GT'],
            'GYD' => ['name' => '圭亚那元', 'country_code' => 'GY'],
            'HNL' => ['name' => '洪都拉斯伦皮拉', 'country_code' => 'HN'],
            'HTG' => ['name' => '海地古德', 'country_code' => 'HT'],
            'HUF' => ['name' => '匈牙利福林', 'country_code' => 'HU'],
            'ILS' => ['name' => '以色列新谢克尔', 'country_code' => 'IL'],
            'INR' => ['name' => '印度卢比', 'country_code' => 'IN'],
            'IQD' => ['name' => '伊拉克第纳尔', 'country_code' => 'IQ'],
            'IRR' => ['name' => '伊朗里亚尔', 'country_code' => 'IR'],
            'ISK' => ['name' => '冰岛克朗', 'country_code' => 'IS'],
            'JMD' => ['name' => '牙买加元', 'country_code' => 'JM'],
            'JOD' => ['name' => '约旦第纳尔', 'country_code' => 'JO'],
            'KES' => ['name' => '肯尼亚先令', 'country_code' => 'KE'],
            'KGS' => ['name' => '吉尔吉斯斯坦索姆', 'country_code' => 'KG'],
            'KHR' => ['name' => '柬埔寨瑞尔', 'country_code' => 'KH'],
            'KMF' => ['name' => '科摩罗法郎', 'country_code' => 'KM'],
            'KPW' => ['name' => '朝鲜圆', 'country_code' => 'KP'],
            'KWD' => ['name' => '科威特第纳尔', 'country_code' => 'KW'],
            'KYD' => ['name' => '开曼群岛元', 'country_code' => 'KY'],
            'KZT' => ['name' => '哈萨克斯坦坚戈', 'country_code' => 'KZ'],
            'LAK' => ['name' => '老挝基普', 'country_code' => 'LA'],
            'LBP' => ['name' => '黎巴嫩镑', 'country_code' => 'LB'],
            'LKR' => ['name' => '斯里兰卡卢比', 'country_code' => 'LK'],
            'LRD' => ['name' => '利比里亚元', 'country_code' => 'LR'],
            'LSL' => ['name' => '莱索托洛蒂', 'country_code' => 'LS'],
            'LYD' => ['name' => '利比亚第纳尔', 'country_code' => 'LY'],
            'MAD' => ['name' => '摩洛哥迪尔汗', 'country_code' => 'MA'],
            'MDL' => ['name' => '摩尔多瓦列伊', 'country_code' => 'MD'],
            'MGA' => ['name' => '马达加斯加阿里亚里', 'country_code' => 'MG'],
            'MKD' => ['name' => '马其顿代纳尔', 'country_code' => 'MK'],
            'MMK' => ['name' => '缅元', 'country_code' => 'MM'],
            'MNT' => ['name' => '蒙古图格里克', 'country_code' => 'MN'],
            'MRU' => ['name' => '毛里塔尼亚乌吉亚', 'country_code' => 'MR'],
            'MUR' => ['name' => '毛里求斯卢比', 'country_code' => 'MU'],
            'MVR' => ['name' => '马尔代夫拉菲亚', 'country_code' => 'MV'],
            'MWK' => ['name' => '马拉维克瓦查', 'country_code' => 'MW'],
            'MXV' => ['name' => '墨西哥发展单位', 'country_code' => 'MX'],
            'MZN' => ['name' => '莫桑比克梅蒂卡尔', 'country_code' => 'MZ'],
            'NAD' => ['name' => '纳米比亚元', 'country_code' => 'NA'],
            'NGN' => ['name' => '尼日利亚奈拉', 'country_code' => 'NG'],
            'NIO' => ['name' => '尼加拉瓜科多巴', 'country_code' => 'NI'],
            'NPR' => ['name' => '尼泊尔卢比', 'country_code' => 'NP'],
            'OMR' => ['name' => '阿曼里亚尔', 'country_code' => 'OM'],
            'PAB' => ['name' => '巴拿马巴波亚', 'country_code' => 'PA'],
            'PEN' => ['name' => '秘鲁索尔', 'country_code' => 'PE'],
            'PGK' => ['name' => '巴布亚新几内亚基那', 'country_code' => 'PG'],
            'PKR' => ['name' => '巴基斯坦卢比', 'country_code' => 'PK'],
            'PLN' => ['name' => '波兰兹罗提', 'country_code' => 'PL'],
            'PYG' => ['name' => '巴拉圭瓜拉尼', 'country_code' => 'PY'],
            'QAR' => ['name' => '卡塔尔里亚尔', 'country_code' => 'QA'],
            'RON' => ['name' => '罗马尼亚列伊', 'country_code' => 'RO'],
            'RSD' => ['name' => '塞尔维亚第纳尔', 'country_code' => 'RS'],
            'RWF' => ['name' => '卢旺达法郎', 'country_code' => 'RW'],
            'SAR' => ['name' => '沙特里亚尔', 'country_code' => 'SA'],
            'SBD' => ['name' => '所罗门群岛元', 'country_code' => 'SB'],
            'SCR' => ['name' => '塞舌尔卢比', 'country_code' => 'SC'],
            'SDG' => ['name' => '苏丹镑', 'country_code' => 'SD'],
            'SHP' => ['name' => '圣赫勒拿镑', 'country_code' => 'SH'],
            'SLL' => ['name' => '塞拉利昂利昂', 'country_code' => 'SL'],
            'SLE' => ['name' => '塞拉利昂利昂', 'country_code' => 'SL'],
            'SOS' => ['name' => '索马里先令', 'country_code' => 'SO'],
            'SRD' => ['name' => '苏里南元', 'country_code' => 'SR'],
            'SSP' => ['name' => '南苏丹镑', 'country_code' => 'SS'],
            'STN' => ['name' => '圣多美和普林西比多布拉', 'country_code' => 'ST'],
            'SVC' => ['name' => '萨尔瓦多科朗', 'country_code' => 'SV'],
            'SYP' => ['name' => '叙利亚镑', 'country_code' => 'SY'],
            'SZL' => ['name' => '斯威士兰里兰吉尼', 'country_code' => 'SZ'],
            'TJS' => ['name' => '塔吉克斯坦索莫尼', 'country_code' => 'TJ'],
            'TMT' => ['name' => '土库曼斯坦马纳特', 'country_code' => 'TM'],
            'TND' => ['name' => '突尼斯第纳尔', 'country_code' => 'TN'],
            'TOP' => ['name' => '汤加潘加', 'country_code' => 'TO'],
            'TRY' => ['name' => '土耳其里拉', 'country_code' => 'TR'],
            'TTD' => ['name' => '特立尼达和多巴哥元', 'country_code' => 'TT'],
            'TWD' => ['name' => '新台币', 'country_code' => 'TW'],
            'TZS' => ['name' => '坦桑尼亚先令', 'country_code' => 'TZ'],
            'UAH' => ['name' => '乌克兰格里夫纳', 'country_code' => 'UA'],
            'UGX' => ['name' => '乌干达先令', 'country_code' => 'UG'],
            'USN' => ['name' => '美元（次日）', 'country_code' => 'US'],
            'UYU' => ['name' => '乌拉圭比索', 'country_code' => 'UY'],
            'UZS' => ['name' => '乌兹别克斯坦索姆', 'country_code' => 'UZ'],
            'VES' => ['name' => '委内瑞拉玻利瓦尔', 'country_code' => 'VE'],
            'VED' => ['name' => '委内瑞拉玻利瓦尔', 'country_code' => 'VE'],
            'VUV' => ['name' => '瓦努阿图瓦图', 'country_code' => 'VU'],
            'WST' => ['name' => '萨摩亚塔拉', 'country_code' => 'WS'],
            'YER' => ['name' => '也门里亚尔', 'country_code' => 'YE'],
            'ZMW' => ['name' => '赞比亚克瓦查', 'country_code' => 'ZM'],
            'ZWL' => ['name' => '津巴布韦元', 'country_code' => 'ZW'],
            'XCD' => ['name' => '东加勒比元', 'country_code' => ''],
            'XOF' => ['name' => '西非法郎', 'country_code' => ''],
            'XPF' => ['name' => '太平洋法郎', 'country_code' => ''],
            'AED' => ['name' => '阿联酋迪拉姆', 'country_code' => 'AE'],
        ],
        'USD' => [
            'CNY' => ['name' => '人民币','country_code' => 'CN'],
            'EUR' => ['name' => '欧元','country_code' => 'EU'],
            'HKD' => ['name' => '港币','country_code' => 'HK'],
            'JPY' => ['name' => '日元','country_code' => 'JP'],
            'GBP' => ['name' => '英镑','country_code' => 'GB'],
            'AUD' => ['name' => '澳大利亚元','country_code' => 'AU'],
            'CAD' => ['name' => '加拿大元','country_code' => 'CA'],
            'THB' => ['name' => '泰国铢','country_code' => 'TH'],
            'SGD' => ['name' => '新加坡元','country_code' => 'SG'],
            'DKK' => ['name' => '丹麦克朗','country_code' => 'DK'],
            'NOK' => ['name' => '挪威克朗','country_code' => 'NO'],
            'SEK' => ['name' => '瑞典克朗','country_code' => 'SE'],
            'CHF' => ['name' => '瑞士法郎','country_code' => 'CH'],
            'NTD' => ['name' => '新台币','country_code' => 'TW'],
            'ZAR' => ['name' => '南非兰特','country_code' => 'ZA'],
            'RUB' => ['name' => '卢布','country_code' => 'RU'],
            'PHP' => ['name' => '菲律宾比索','country_code' => 'PH'],
            'MYR' => ['name' => '林吉特','country_code' => 'MY'],
            'MOP' => ['name' => '澳门元','country_code' => 'MO'],
            'KRW' => ['name' => '韩国元','country_code' => 'KR'],
            'NZD' => ['name' => '新西兰元','country_code' => 'NZ'],
            'BRL' => ['name' => '巴西雷亚尔','country_code' => 'BR'],
            'IDR' => ['name' => '印尼盾','country_code' => 'ID'],
            'VND' => ['name' => '越南盾','country_code' => 'VN'],
            'MXN' => ['name' => '墨西哥元','country_code' => 'MEX'],

            'ALL' => ['name' => '阿尔巴尼亚列克', 'country_code' => 'AL'],
            'AMD' => ['name' => '亚美尼亚德拉姆', 'country_code' => 'AM'],
            'ANG' => ['name' => '荷属安的列斯盾', 'country_code' => 'ANT'],
            'AOA' => ['name' => '安哥拉宽扎', 'country_code' => 'AO'],
            'ARS' => ['name' => '阿根廷比索', 'country_code' => 'AR'],
            'AWG' => ['name' => '阿鲁巴弗罗林', 'country_code' => 'AW'],
            'AZN' => ['name' => '阿塞拜疆马纳特', 'country_code' => 'AZ'],
            'BAM' => ['name' => '波黑可兑换马克', 'country_code' => 'BA'],
            'BBD' => ['name' => '巴巴多斯元', 'country_code' => 'BB'],
            'BDT' => ['name' => '孟加拉塔卡', 'country_code' => 'BD'],
            'BGN' => ['name' => '保加利亚列弗', 'country_code' => 'BG'],
            'BHD' => ['name' => '巴林第纳尔', 'country_code' => 'BH'],
            'BIF' => ['name' => '布隆迪法郎', 'country_code' => 'BI'],
            'BMD' => ['name' => '百慕大元', 'country_code' => 'BM'],
            'BND' => ['name' => '文莱元', 'country_code' => 'BN'],
            'BOB' => ['name' => '玻利维亚诺', 'country_code' => 'BO'],
            'BOV' => ['name' => '玻利维亚Mvdol', 'country_code' => 'BO'],
            'BSD' => ['name' => '巴哈马元', 'country_code' => 'BS'],
            'BTN' => ['name' => '不丹努尔特鲁姆', 'country_code' => 'BT'],
            'BWP' => ['name' => '博茨瓦纳普拉', 'country_code' => 'BW'],
            'BYN' => ['name' => '白俄罗斯卢布', 'country_code' => 'BY'],
            'BZD' => ['name' => '伯利兹元', 'country_code' => 'BZ'],
            'CDF' => ['name' => '刚果法郎', 'country_code' => 'CD'],
            'XAF' => ['name' => '中非法郎', 'country_code' => 'CF'],
            'CHE' => ['name' => 'WIR欧元', 'country_code' => 'CH'],
            'CHW' => ['name' => 'WIR法郎', 'country_code' => 'CH'],
            'CLP' => ['name' => '智利比索', 'country_code' => 'CL'],
            'CLF' => ['name' => '智利发展单位', 'country_code' => 'CL'],
            'COP' => ['name' => '哥伦比亚比索', 'country_code' => 'CO'],
            'COU' => ['name' => '哥伦比亚实际单位', 'country_code' => 'CO'],
            'CRC' => ['name' => '哥斯达黎加科朗', 'country_code' => 'CR'],
            'CUP' => ['name' => '古巴比索', 'country_code' => 'CU'],
            'CUC' => ['name' => '古巴可兑换比索', 'country_code' => 'CU'],
            'CVE' => ['name' => '佛得角埃斯库多', 'country_code' => 'CV'],
            'CZK' => ['name' => '捷克克朗', 'country_code' => 'CZ'],
            'DJF' => ['name' => '吉布提法郎', 'country_code' => 'DJ'],
            'DOP' => ['name' => '多米尼加比索', 'country_code' => 'DO'],
            'DZD' => ['name' => '阿尔及利亚第纳尔', 'country_code' => 'DZ'],
            'EGP' => ['name' => '埃及镑', 'country_code' => 'EG'],
            'ERN' => ['name' => '厄立特里亚纳克法', 'country_code' => 'ER'],
            'ETB' => ['name' => '埃塞俄比亚比尔', 'country_code' => 'ET'],
            'FJD' => ['name' => '斐济元', 'country_code' => 'FJ'],
            'FKP' => ['name' => '福克兰群岛镑', 'country_code' => 'FK'],
            'GEL' => ['name' => '格鲁吉亚拉里', 'country_code' => 'GE'],
            'GHS' => ['name' => '加纳塞地', 'country_code' => 'GH'],
            'GIP' => ['name' => '直布罗陀镑', 'country_code' => 'GI'],
            'GMD' => ['name' => '冈比亚达拉西', 'country_code' => 'GM'],
            'GNF' => ['name' => '几内亚法郎', 'country_code' => 'GN'],
            'GTQ' => ['name' => '危地马拉格查尔', 'country_code' => 'GT'],
            'GYD' => ['name' => '圭亚那元', 'country_code' => 'GY'],
            'HNL' => ['name' => '洪都拉斯伦皮拉', 'country_code' => 'HN'],
            'HTG' => ['name' => '海地古德', 'country_code' => 'HT'],
            'HUF' => ['name' => '匈牙利福林', 'country_code' => 'HU'],
            'ILS' => ['name' => '以色列新谢克尔', 'country_code' => 'IL'],
            'INR' => ['name' => '印度卢比', 'country_code' => 'IN'],
            'IQD' => ['name' => '伊拉克第纳尔', 'country_code' => 'IQ'],
            'IRR' => ['name' => '伊朗里亚尔', 'country_code' => 'IR'],
            'ISK' => ['name' => '冰岛克朗', 'country_code' => 'IS'],
            'JMD' => ['name' => '牙买加元', 'country_code' => 'JM'],
            'JOD' => ['name' => '约旦第纳尔', 'country_code' => 'JO'],
            'KES' => ['name' => '肯尼亚先令', 'country_code' => 'KE'],
            'KGS' => ['name' => '吉尔吉斯斯坦索姆', 'country_code' => 'KG'],
            'KHR' => ['name' => '柬埔寨瑞尔', 'country_code' => 'KH'],
            'KMF' => ['name' => '科摩罗法郎', 'country_code' => 'KM'],
            'KPW' => ['name' => '朝鲜圆', 'country_code' => 'KP'],
            'KWD' => ['name' => '科威特第纳尔', 'country_code' => 'KW'],
            'KYD' => ['name' => '开曼群岛元', 'country_code' => 'KY'],
            'KZT' => ['name' => '哈萨克斯坦坚戈', 'country_code' => 'KZ'],
            'LAK' => ['name' => '老挝基普', 'country_code' => 'LA'],
            'LBP' => ['name' => '黎巴嫩镑', 'country_code' => 'LB'],
            'LKR' => ['name' => '斯里兰卡卢比', 'country_code' => 'LK'],
            'LRD' => ['name' => '利比里亚元', 'country_code' => 'LR'],
            'LSL' => ['name' => '莱索托洛蒂', 'country_code' => 'LS'],
            'LYD' => ['name' => '利比亚第纳尔', 'country_code' => 'LY'],
            'MAD' => ['name' => '摩洛哥迪尔汗', 'country_code' => 'MA'],
            'MDL' => ['name' => '摩尔多瓦列伊', 'country_code' => 'MD'],
            'MGA' => ['name' => '马达加斯加阿里亚里', 'country_code' => 'MG'],
            'MKD' => ['name' => '马其顿代纳尔', 'country_code' => 'MK'],
            'MMK' => ['name' => '缅元', 'country_code' => 'MM'],
            'MNT' => ['name' => '蒙古图格里克', 'country_code' => 'MN'],
            'MRU' => ['name' => '毛里塔尼亚乌吉亚', 'country_code' => 'MR'],
            'MUR' => ['name' => '毛里求斯卢比', 'country_code' => 'MU'],
            'MVR' => ['name' => '马尔代夫拉菲亚', 'country_code' => 'MV'],
            'MWK' => ['name' => '马拉维克瓦查', 'country_code' => 'MW'],
            'MXV' => ['name' => '墨西哥发展单位', 'country_code' => 'MX'],
            'MZN' => ['name' => '莫桑比克梅蒂卡尔', 'country_code' => 'MZ'],
            'NAD' => ['name' => '纳米比亚元', 'country_code' => 'NA'],
            'NGN' => ['name' => '尼日利亚奈拉', 'country_code' => 'NG'],
            'NIO' => ['name' => '尼加拉瓜科多巴', 'country_code' => 'NI'],
            'NPR' => ['name' => '尼泊尔卢比', 'country_code' => 'NP'],
            'OMR' => ['name' => '阿曼里亚尔', 'country_code' => 'OM'],
            'PAB' => ['name' => '巴拿马巴波亚', 'country_code' => 'PA'],
            'PEN' => ['name' => '秘鲁索尔', 'country_code' => 'PE'],
            'PGK' => ['name' => '巴布亚新几内亚基那', 'country_code' => 'PG'],
            'PKR' => ['name' => '巴基斯坦卢比', 'country_code' => 'PK'],
            'PLN' => ['name' => '波兰兹罗提', 'country_code' => 'PL'],
            'PYG' => ['name' => '巴拉圭瓜拉尼', 'country_code' => 'PY'],
            'QAR' => ['name' => '卡塔尔里亚尔', 'country_code' => 'QA'],
            'RON' => ['name' => '罗马尼亚列伊', 'country_code' => 'RO'],
            'RSD' => ['name' => '塞尔维亚第纳尔', 'country_code' => 'RS'],
            'RWF' => ['name' => '卢旺达法郎', 'country_code' => 'RW'],
            'SAR' => ['name' => '沙特里亚尔', 'country_code' => 'SA'],
            'SBD' => ['name' => '所罗门群岛元', 'country_code' => 'SB'],
            'SCR' => ['name' => '塞舌尔卢比', 'country_code' => 'SC'],
            'SDG' => ['name' => '苏丹镑', 'country_code' => 'SD'],
            'SHP' => ['name' => '圣赫勒拿镑', 'country_code' => 'SH'],
            'SLL' => ['name' => '塞拉利昂利昂', 'country_code' => 'SL'],
            'SLE' => ['name' => '塞拉利昂利昂', 'country_code' => 'SL'],
            'SOS' => ['name' => '索马里先令', 'country_code' => 'SO'],
            'SRD' => ['name' => '苏里南元', 'country_code' => 'SR'],
            'SSP' => ['name' => '南苏丹镑', 'country_code' => 'SS'],
            'STN' => ['name' => '圣多美和普林西比多布拉', 'country_code' => 'ST'],
            'SVC' => ['name' => '萨尔瓦多科朗', 'country_code' => 'SV'],
            'SYP' => ['name' => '叙利亚镑', 'country_code' => 'SY'],
            'SZL' => ['name' => '斯威士兰里兰吉尼', 'country_code' => 'SZ'],
            'TJS' => ['name' => '塔吉克斯坦索莫尼', 'country_code' => 'TJ'],
            'TMT' => ['name' => '土库曼斯坦马纳特', 'country_code' => 'TM'],
            'TND' => ['name' => '突尼斯第纳尔', 'country_code' => 'TN'],
            'TOP' => ['name' => '汤加潘加', 'country_code' => 'TO'],
            'TRY' => ['name' => '土耳其里拉', 'country_code' => 'TR'],
            'TTD' => ['name' => '特立尼达和多巴哥元', 'country_code' => 'TT'],
            'TWD' => ['name' => '新台币', 'country_code' => 'TW'],
            'TZS' => ['name' => '坦桑尼亚先令', 'country_code' => 'TZ'],
            'UAH' => ['name' => '乌克兰格里夫纳', 'country_code' => 'UA'],
            'UGX' => ['name' => '乌干达先令', 'country_code' => 'UG'],
            'USN' => ['name' => '美元（次日）', 'country_code' => 'US'],
            'UYU' => ['name' => '乌拉圭比索', 'country_code' => 'UY'],
            'UZS' => ['name' => '乌兹别克斯坦索姆', 'country_code' => 'UZ'],
            'VES' => ['name' => '委内瑞拉玻利瓦尔', 'country_code' => 'VE'],
            'VED' => ['name' => '委内瑞拉玻利瓦尔', 'country_code' => 'VE'],
            'VUV' => ['name' => '瓦努阿图瓦图', 'country_code' => 'VU'],
            'WST' => ['name' => '萨摩亚塔拉', 'country_code' => 'WS'],
            'YER' => ['name' => '也门里亚尔', 'country_code' => 'YE'],
            'ZMW' => ['name' => '赞比亚克瓦查', 'country_code' => 'ZM'],
            'ZWL' => ['name' => '津巴布韦元', 'country_code' => 'ZW'],
            'XCD' => ['name' => '东加勒比元', 'country_code' => ''],
            'XOF' => ['name' => '西非法郎', 'country_code' => ''],
            'XPF' => ['name' => '太平洋法郎', 'country_code' => ''],
            'AED' => ['name' => '阿联酋迪拉姆', 'country_code' => 'AE'],
        ]
    ];

    // 支持更新实时汇率的币种
    public static $online_update_currency = [
        'USD',
        'CNY',
        'EUR',
        'HKD',
        'JPY',
        'GBP',
        'AUD',
        'CAD',
        'THB',
        'SGD',
        'DKK',
        'NOK',
        'SEK',
        'CHF',
        'NTD',
        'ZAR',
        'RUB',
        'PHP',
        'MYR',
        'MOP',
        'KRW',
        'NZD',
        'AED'
    ];

    //系统默认币种
    public static $default_currency = [
        'USD',
        'CNY',
        'EUR',
        'AUD',
        'CAD',
        'DKK',
        'NOK',
        'SEK',
        'CHF',
        'GBP',
        'HKD',
        'JPY',
        'NTD',
        'NZD',
        'SGD',
        'BRL',
        'ZAR',
        'RUB',
        'PHP',
        'KRW',
        'MYR',
        'THB',
        'MOP',
        'IDR',
        'VND',
        'MXN',
        'AED',
    ];

    //新添加汇率注意排序
    public static $valid_currency = [
        'USD',
        'CNY',
        'EUR',
        'AUD',
        'CAD',
        'DKK',
        'NOK',
        'SEK',
        'CHF',
        'GBP',
        'HKD',
        'JPY',
        'NTD',
        'NZD',
        'SGD',
        'BRL',
        'ZAR',
        'RUB',
        'PHP',
        'KRW',
        'MYR',
        'THB',
        'MOP',
        'IDR',
        'VND',
        'MXN',

        'ALL',
        'AMD',
        'ANG',
        'AOA',
        'ARS',
        'AWG',
        'AZN',
        'BAM',
        'BBD',
        'BDT',
        'BGN',
        'BHD',
        'BIF',
        'BMD',
        'BND',
        'BOB',
        'BOV',
        'BSD',
        'BTN',
        'BWP',
        'BYN',
        'BZD',
        'CDF',
        'XAF',
        'CHE',
        'CHW',
        'CLP',
        'CLF',
        'COP',
        'COU',
        'CRC',
        'CUP',
        'CUC',
        'CVE',
        'CZK',
        'DJF',
        'DOP',
        'DZD',
        'EGP',
        'ERN',
        'ETB',
        'FJD',
        'FKP',
        'GEL',
        'GHS',
        'GIP',
        'GMD',
        'GNF',
        'GTQ',
        'GYD',
        'HNL',
        'HTG',
        'HUF',
        'ILS',
        'INR',
        'IQD',
        'IRR',
        'ISK',
        'JMD',
        'JOD',
        'KES',
        'KGS',
        'KHR',
        'KMF',
        'KPW',
        'KWD',
        'KYD',
        'KZT',
        'LAK',
        'LBP',
        'LKR',
        'LRD',
        'LSL',
        'LYD',
        'MAD',
        'MDL',
        'MGA',
        'MKD',
        'MMK',
        'MNT',
        'MRU',
        'MUR',
        'MVR',
        'MWK',
        'MXV',
        'MZN',
        'NAD',
        'NGN',
        'NIO',
        'NPR',
        'OMR',
        'PAB',
        'PEN',
        'PGK',
        'PKR',
        'PLN',
        'PYG',
        'QAR',
        'RON',
        'RSD',
        'RWF',
        'SAR',
        'SBD',
        'SCR',
        'SDG',
        'SHP',
        'SLL',
        'SLE',
        'SOS',
        'SRD',
        'SSP',
        'STN',
        'SVC',
        'SYP',
        'SZL',
        'TJS',
        'TMT',
        'TND',
        'TOP',
        'TRY',
        'TTD',
        'TWD',
        'TZS',
        'UAH',
        'UGX',
        'USN',
        'UYU',
        'UZS',
        'VES',
        'VED',
        'VUV',
        'WST',
        'YER',
        'ZMW',
        'ZWL',
        'XCD',
        'XOF',
        'XPF',
        'AED',
    ];

    public static $transform_currency = [
        'RMB' => 'CNY'
    ];

    public static $special_currency = [
        'VND','IDR','BRL','MXN'
    ];

    // 特殊汇率币种换算
     public static $special_exchange_currency = [
        'AED',
    ];

    const MONETARY_TYPE_ENUM_MAPS = [
        0 => 'f_buy_pri',//现汇买入价
        1 => 'f_sell_pri',//现汇卖出价
        2 => 'm_buy_pri',//现钞买入价
        3 => 'm_sell_pri',//现钞卖出价
        4 => 'bank_conversion_pri',//银行折算价/中间价
        5 => 'custom_rate'//自定义汇率
    ];
    /**
     * 主货币列表
     * @var \string[][]
     */
    public static $mainCurrencyLists = [
        "USD" => ["name" => "美元", "currency" => "USD", "country_code" => "US"],
        "CNY" => ["name" => "人民币", "currency" => "CNY", "country_code" => "CN"]
    ];

    /**
     * @var string 语言
     */
    public $language;

    protected $mainCurrency;
    protected $clientId;
    protected static $usableCurrency;   //当前用户设置的可用币种

    /**
     * ExchangeRateService constructor.
     * @param $clientId
     */
    public function __construct($clientId = null, bool $setMainCurrency = true)
    {
        // 这里的主币种需要和clientId关联
        if ($clientId) {
            $this->clientId = $clientId;
            $this->setMainCurrencyByClientId($clientId);
        } elseif (\User::getLoginUser()) {
            $this->clientId = \User::getLoginUser()->getClientId();
            if ($setMainCurrency){
                $this->setMainCurrencyByClientId($this->clientId);
            }
        } else {
            // 不设置住币种和clientId 给命令行使用
        }
    }

    /**
     * @param $mainCurrency
     */
    public function setMainCurrency($mainCurrency)
    {
        self::checkMainCurrency($mainCurrency);
        $this->mainCurrency = $mainCurrency;
    }

    public function setMainCurrencyByClientId(int $clientId)
    {
        $this->mainCurrency = Client::getClient($clientId)->getMainCurrency();
    }

    public static function checkMainCurrency($mainCurrency)
    {
        if ($mainCurrency != self::CNY && $mainCurrency != self::USD) {
            throw new RuntimeException(\Yii::t('common', 'Not exist'));
        }
    }

    /**
     * 更新在线汇率数据
     *
     * @use crm
     * @param array $exchanges
     * @return int 插入数量
     */
    public function updateOnlineRates(array $exchanges = [])
    {
        if (empty($exchanges)) {
            return 0;
        }

        $isUpdate = ExchangeRate::model()->exists('`update_time`>=:update_time', [
            'update_time' => $exchanges[0]['update_time']
        ]);

        if (! $isUpdate ) {
            $values = [];
            foreach ($exchanges as $item) {
                $values[] = "('" . implode("','", $item) . "')";
            }
            $sql = "REPLACE INTO " . ExchangeRate::model()->tableName()
                . "( `name`,`cn_name`,`update_time`,`bank_conversion_pri`,`f_buy_pri`,`f_sell_pri`,`m_buy_pri`,`m_sell_pri`,`type`) VALUES "
                . implode(',', $values);
            LogUtil::info($sql);
            $updated =  ExchangeRate::model()->getCommandBuilder()->createSqlCommand($sql)->execute();

            ExchangeRateCacheableRepo::instance(0)->refreshCache();

            return $updated;
        }

        return 0;
    }

    /**
     * 将外币转换成人民币
     * @use crm, boss
     * @param string $currency
     * @param double $amount
     * @return float
     */
    public function exchangeForCNY(string $currency, $amount)
    {
        $currency = $this->transformCurrency($currency);
        if (empty(trim($currency)) || $currency === 'CNY') {
            return $amount;
        }
        if (!in_array($currency, self::$valid_currency)) {
            throw new RuntimeException('不支持的货币：' . $currency);
        }
        $rate = $this->cnyRateForCurrency($currency);
        if (!$rate) {
            throw new RuntimeException('获取汇率失败');
        }
        return (float)$amount * $rate / 100;
    }

    /**
     * 将外币转换成美元
     * @use crm, boss
     * @param string $currency
     * @param double $amount
     * @return float
     */
    public function exchangeForUSD(string $currency, $amount)
    {
        $currency = $this->transformCurrency($currency);
        if (empty(trim($currency)) || $currency === 'USD') {
            return $amount;
        }
        if (!in_array($currency, self::$valid_currency)) {
            throw new RuntimeException('不支持的货币：' . $currency);
        }
        $rate = $this->usdRateForCurrency($currency);
        if (!$rate) {
            throw new RuntimeException('获取汇率失败');
        }
        return (float)$amount * $rate / 100;
    }

    /**
     * 获取对应货币对人民币的汇率
     * 若用户未设置对应货币的汇率，则返回线上汇率
     *
     * @use boss
     * @param string $currency
     * @return float
     */
    public function cnyRateForCurrency(string $currency)
    {
        $type = 'CNY';
        $currency = $this->transformCurrency($currency);
        if ($currency == 'CNY') {
            return 100;
        }
        $rate = $this->customRateForCurrency($currency, $type);
        return (int)$rate === -1 ? self::onlineRateForCurrency($currency, $type) : $rate;
    }

    public function cnyRateForCurrencies(array $currencies)
    {
        return $this->customRateForCurrencies($currencies, 'CNY');
    }

    /**
     * 获取对应货币对美元的汇率
     * 若用户未设置对应货币的汇率，则返回线上汇率
     *
     * @use boss
     * @param string $currency
     * @return float
     */
    public function usdRateForCurrency(string $currency)
    {
        $type = 'USD';
        $currency = $this->transformCurrency($currency);
        if ($currency == 'USD') {
            return 100;
        }
        $rate = $this->customRateForCurrency($currency, $type);
        return (int)$rate === -1 ? self::onlineRateForCurrency($currency, $type) : $rate;
    }


    public function usdRateForCurrencies(array $allCurrency)
    {
        return $this->customRateForCurrencies($allCurrency, 'USD');
    }
    /***
     * @param string $origin_currency
     * @param string $target_currency
     * @return float|int
     * 获取当前货币对指定货币的汇率 若用户未设置对应货币的汇率，则返回线上汇率
     */
    public function originRateForCurrency(string $origin_currency, string $target_currency)
    {
        $origin_currency = $this->transformCurrency($origin_currency);
        if ($origin_currency == $target_currency) {
            return 100;
        }
        $rate = $this->customRateForCurrency($origin_currency, $target_currency);
        return (int)$rate === -1 ? self::onlineRateForCurrency($origin_currency, $target_currency) : $rate;
    }

    /**
     * 获得货币配置
     * 获取用户自己再汇率设置里面自己设定的汇率 没有设置则返回汇率-1
     * @use boss
     * @param string $currency
     * @param string $type
     * @return float
     */
    public function customRateForCurrency(string $currency, string $type)
    {
        $clientId = $this->clientId;
        $rate = CustomExchangeRate::model()->find('client_id=:client_id and currency=:currency and type=:type', [
            ':client_id' => $clientId,
            ':currency' => $currency,
            ':type' => $type,
        ]);

        return isset($rate) ? $rate->rate : -1;
    }

    public function customRateForCurrencies(array $currencies, string $type)
    {
        $clientId = $this->clientId;

        $currencies = Arr::uniqueFilterValues($currencies);
        if (empty($currencies)){
            return [];
        }
        $currenciesStr = implode("','", $currencies);
        $rate = CustomExchangeRate::model()->findAll("client_id=:client_id  and type=:type and currency in ('$currenciesStr') ", [
            ':client_id' => $clientId,
            ':type' => $type,
        ]);
        $rateMap = array_column($rate, null, 'currency');
        $res = [];
        foreach ($currencies as $currency) {
            if ($type == $currency) {
                $res[$currency] = 100;
                continue;
            }

            if (empty($rateMap[$currency]['rate']) || $rateMap[$currency]['rate'] == -1) {
                $res[$currency] = self::onlineRateForCurrency($currency, $type);
            } else {
                $res[$currency] = $rateMap[$currency]['rate'];
            }
        }

        return $res;
    }

    /**
     * 获得某货币的在线汇率
     * 根据主币种获取某货币对应主币种的汇率
     * @use boss
     * @param string $currency
     * @param string $type
     * @return float|int
     */
    public static function onlineRateForCurrency($currency, $type)
    {
//        $rate = ExchangeRate::model()->find('name=:name AND type=:type', [
//            'name' => $currency,
//            'type' => $type,
//        ]);
        $rate = ExchangeRateCacheableRepo::instance(0)->findOneByConditions([
            'name' => $currency,
            'type' => $type
        ]);


        if ($rate) {
            $rate = (int)$rate['f_buy_pri'] === 0 ? $rate['m_buy_pri'] : $rate['f_buy_pri'];
            return $rate;
        }
        return 0;
    }

    public function rankRate($ratesMaps){
        $result = [];
        foreach (self::$valid_currency as $item){
            if(isset($ratesMaps[$item])){
                $result[] = $ratesMaps[$item];
            }

        }
        return $result;
    }

    /**
     * 根据主币种获取对应的各币种的汇率
     * @use boss
     * @return array
     */
    public function onlineRates($format_online_rate=false, $only_main_currency = true)
    {
        $type = $this->mainCurrency;
        $clientId = $this->clientId;

//        $list = ExchangeRate::model()->findAll('type=:type', [
//            'type' => $type
//        ]);
        $where = $only_main_currency ? ['type' => $type] : [];
        $list = ExchangeRateCacheableRepo::instance(0)->findAllByConditions($where);
        $list = ExchangeRate::model()->populateRecords($list);

        if($list && is_array($list)) {
            foreach ($list as &$row) {
                if(isset($row['cn_name']) && isset($row['name'])){
                    $row['cn_name'] = \Yii::t('country', $row['name']);
                }
            }
        }

        $online_rates = array_reduce($list, function (array $carry, ExchangeRate $rate) {
            if (!in_array($rate->name, self::$valid_currency)) {
                return $carry;
            }
            $carry[] = $rate->getAttributes();
            return $carry;
        }, []);

        $where = $only_main_currency ? ['type' => $type, 'client_id' => $clientId] : ['client_id' => $clientId];
        $customRates = CustomExchangeRate::model()->findAllByAttributes($where);

        $custom_rates = array_reduce($customRates,
            function ($carry, CustomExchangeRate $rate) {
                $carry[$rate->type][$rate->currency] = ['rate' => $rate->rate, 'update_time' => $rate->update_time];
                return $carry;
            }, []);

        return array_values(array_filter(array_map(function ($rate) use ($custom_rates, $format_online_rate) {
            if (isset($custom_rates[$rate['type']][$rate['name']])) {
                $custom_rate = $custom_rates[$rate['type']][$rate['name']];
                $rate['custom_rate'] = $custom_rate['rate'];
                $rate['update_time'] = $custom_rate['update_time'];
            }else{
                $rate['custom_rate'] = -1;
            }
            if($format_online_rate){
                $rate['online_rate'] = (int)$rate['f_buy_pri'] === 0 ? $rate['m_buy_pri'] : $rate['f_buy_pri'];
                return $rate;
            }

            $rate['disabled_real_time'] = !in_array($rate['name'], self::$online_update_currency) ? 1 : 0;
            $rate['base'] = in_array($rate['name'], self::$default_currency) ? 1 : 0;

            //移除未自定义汇率的币种
            if (!$rate['base'] && $rate['custom_rate'] == -1) {
                return null;
            }

            return $rate;
        }, $online_rates)));
    }

    /**
     * 根据主币种获取对应的各币种的汇率, 如果用户设置的汇率不存在则使用在线汇率
     * @use boss
     * @return array
     */
    public function getFormatRates(){
        $formatOnlineRate = true;
        $exchangeRates = $this->onlineRates($formatOnlineRate);
        $formatRates = [];
        foreach($exchangeRates as $rateInfo) {
            $rate = (int)$rateInfo['custom_rate'] > 0 ? $rateInfo['custom_rate'] : $rateInfo['online_rate'];
            $rate /= 100;
            $formatRates[$rateInfo['name']] = [
                'name' => $rateInfo['name'],
                'cn_name'=>$rateInfo['cn_name'],
                'type' => $rateInfo['type'],
                'rate'=> $rate
            ];
        }
        $formatRates[$this->mainCurrency] = [
            'name' => $this->mainCurrency,
            'cn_name'=>$this->mainCurrency == 'CNY' ? '人民币' : '美元',
            'type' => $this->mainCurrency,
            'rate'=> 1
        ];
        return $formatRates;
    }

    public function fullRates()
    {
        $rates = $this->onlineRates();
        $extraRates[] = [
            'name' => 'CNY',
            'cn_name' => \Yii::t('country', 'RMB'),
            'bank_conversion_pri' => '100.00000',
            'f_buy_pri' => '100.00000',
            'f_sell_pri' => '100.00000',
            'm_buy_pri' => '100.00000',
            'm_sell_pri' => '100.00000',
            'custom_rate' => -1,
            'update_time' => date('Y-m-d H:i:s'),
        ];

        if ($this->mainCurrency == 'USD') {
            $extraRates[0]['name'] = 'USD';
            $extraRates[0]['cn_name'] = \Yii::t('country', 'USD');
        }

        $rates = array_merge($rates, $extraRates);
        return $rates;
    }

    /**
     * 获取可兑换的货币列表，包括主货币信息
     * @return array [
     *      'from_currency_lists' => [
     *          ["name" => "美元", "key" => "USD", "country_code" => "US"]
     *      ],
     *      'to_currency_lists' => [
     *          ["name" => "美元", "key" => "USD", "country_code" => "US"]
     *      ]
     * ]
     */
    public function exchangeCurrencyLists()
    {
        static::checkMainCurrency($this->mainCurrency);
        $mainLists = static::$mainCurrencyLists;
        foreach ($mainLists as &$item)
        {
            $item['name'] = \Yii::t('country', $item['currency'], [], null, $this->language);
        }
        unset($item);

        $fromLists =[];
        foreach (self::$currencyMap['CNY'] as $currency => $item)
        {
            $fromLists[$currency]  = [
                'currency' => $currency,
                'name' => \Yii::t('country', $currency, [], null, $this->language),
                'country_code' => $item['country_code']
            ];
        }

        $returnData = [
            'from_currency_lists' => array_values($fromLists),
            'to_currency_lists' => []
        ];
        //在使用的主货币放在第一位
        $returnData['to_currency_lists'][] = $mainLists[$this->mainCurrency];
        foreach ($mainLists as $mainCurrency => $info) {
            if ($mainCurrency == $this->mainCurrency) {
                continue;
            }
            $returnData['to_currency_lists'][] = $info;
        }

        return $returnData;
    }

    /**
     * 获取汇率兑换数据
     * @param ExchangeRateParams $request
     * @param UserInfo $userInfo
     * @return array|false
     */
    public function exchangeRate(ExchangeRateParams $request, UserInfo $userInfo)
    {
        $this->setMainCurrency($request->to_currency);
        $exchangeRate = $this->rateInfoForCurrency($request->from_currency);

        if (empty($exchangeRate)) {
            return [];
        }

        $userInfo->setExtentAttributes([
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_DEFAULT_CURRENCY => $request->from_currency,
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_DEFAULT_EXCHANGE => $request->monetary_type
        ]);
        $userInfo->saveExtentAttributes();
        return $exchangeRate;
    }

    /**
     * 更新自定义汇率
     *
     * @use boss
     * @param array $setting
     * @return int
     */
    public function updateCustomRate(array $setting,$clientId,$userId,$referId)
    {
        $setting['create_time'] = date('Y-m-d H:i:s');
        $setting['update_time'] = date('Y-m-d H:i:s');
        $setting['type'] = $this->mainCurrency;

        $oldRate = $this->getOldRate($setting['currency']);
        $oldData = $setting;
        $oldData['rate'] = $oldRate;

        CustomExchangeRate::model()->setAttributes($setting);
        if (! CustomExchangeRate::model()->validate(CustomExchangeRate::model())) {
            throw new \RuntimeException(CustomExchangeRate::model()->getFirstErrorDesc());
        }

        $sql = "REPLACE INTO "
            . CustomExchangeRate::model()->tableName()
            . "(`"
            . implode("`,`", array_keys($setting))
            . "`) VALUES ('"
            . implode("','", $setting)
            . "');";

        $res = CustomExchangeRate::model()->getCommandBuilder()->createSqlCommand($sql)->execute();
        if (!$res){
            throw new \RuntimeException('更新汇率失败！');
        }

        //增加操作日志
        $compareEditSetting = new ItemSettingHistoryCompare($clientId);
        $compareEditSetting->setModule(ItemSettingHistoryCompare::SETTING_TYPE_ITEM_SETTING);
        $compareEditSetting->setData($setting, $oldData);
        $compareEditSetting->setExtraData(['refer_id' => $referId]);
        $compareEditSetting->setType(ItemSettingHistoryCompare::SETTING_CURRENCY_RATE);
        $compareEditSetting->build($userId);

        return $res;
    }

    /**
     * @param $currency
     * @return int 获取修改前的比例
     */
    public function getOldRate($currency){
        $customRate = CustomExchangeRate::model()->find('`client_id`=:client_id AND `currency`=:currency AND `type`=:type', [
            ':client_id' => $this->clientId,
            ':currency' => $currency,
            ':type' => $this->mainCurrency,
        ]);

        return $customRate ? $customRate->rate : -1;
    }
    /**
     * 获得某货币的汇率,包括在线和自定义汇率
     * @use crm
     * @param $name
     * @return array
     */
    public function rateInfoForCurrency($name)
    {
        $type =  $this->mainCurrency;
        $client_id = $this->clientId;

        $name = $this->transformCurrency($name);
        if (!in_array($name, self::$valid_currency)) {
            throw new RuntimeException("不支持该货币");
        }
//        $onlineRate = ExchangeRate::model()->find('name=:name AND type=:type', [
//            'name' => $name,
//            'type' => $type
//        ]);
        $onlineRate = ExchangeRateCacheableRepo::instance(0)->findOneByConditions([
            'name' => $name,
            'type' => $type
        ]);
        $onlineRate = $onlineRate ? ExchangeRate::model()->populateRecord($onlineRate) : null;


        if (!$onlineRate) {
            return [];
        }
        $data = $onlineRate->getAttributes();

        $customRate = CustomExchangeRate::model()->find('client_id=:client_id and currency=:currency and type=:type',[
            ':client_id' => $client_id,
            ':currency' => $name,
            ':type' => $type,
        ]);

        if ($customRate) {
            $data['custom_rate'] = $customRate->rate;
        }else{
            $data['custom_rate'] = -1;
        }
        return $data;
    }

    /**
     * 删除自定义汇率
     *
     * @use boss
     * @param $currency
     * @return int
     */
    public function deleteCustomRate($currency,$userId,$referId)
    {
        $setting['create_time'] = date('Y-m-d H:i:s');
        $setting['update_time'] = date('Y-m-d H:i:s');
        $setting['type'] = $this->mainCurrency;

        $oldRate = $this->getOldRate($currency);
        $oldData = $setting;
        $oldData['rate'] = $oldRate;

        $clientId = $this->clientId;
        $res = CustomExchangeRate::model()->deleteAll('`client_id`=:client_id AND `currency`=:currency AND `type`=:type', [
            'client_id' => $clientId,
            'currency' => $currency,
            'type' => $this->mainCurrency,
        ]);
        LogUtil::info('client_id='.$clientId.';currency='.$currency.';type='.$this->mainCurrency);

        if (false === $res)
        {
            throw new \RuntimeException('删除汇率失败！');
        }

        $newData = $oldData;
        $newData['rate'] = -1;

        //增加操作日志
        $compareEditSetting = new ItemSettingHistoryCompare($clientId);
        $compareEditSetting->setModule(ItemSettingHistoryCompare::SETTING_TYPE_ITEM_SETTING);
        $compareEditSetting->setData($newData, $oldData);
        $compareEditSetting->setExtraData(['refer_id' => $referId]);
        $compareEditSetting->setType(ItemSettingHistoryCompare::SETTING_CURRENCY_RATE);
        $compareEditSetting->build($userId);

        return $res;
    }

    /**
     * 获得所有自定义汇率
     *
     * @use boss
     * @return array
     */
    public function customRates()
    {
        $clientId = $this->clientId;
        $customRates = CustomExchangeRate::model()->findAll('`client_id`=:client_id', [
            'client_id' => $clientId,
        ]);

        return array_map(function (CustomExchangeRate $item) {
            return $item->getAttributes();
        }, $customRates);
    }

    public function transformCurrency($currency)
    {
        return array_key_exists($currency,
            self::$transform_currency) ? self::$transform_currency[$currency] : $currency;
    }

    public static function checkCurrency($currency, $clientId)
    {
        return (in_array($currency, self::$valid_currency) && in_array($currency, self::getClientCurrency($clientId)));
    }

    /**
     * 获取client已配置的货币币种
     * @param $clientId
     * @return array
     */
    public static function getClientCurrency($clientId)
    {
        if (empty(self::$usableCurrency)) {
            $exchange_rate_service = new ExchangeRateService($clientId);
            self::$usableCurrency = array_column($exchange_rate_service->fullRates(),'name');
        }

        return self::$usableCurrency;
    }

    /**
     * 检查币种是否已被使用
     * @param $currency
     * @return bool
     * @throws \ProcessException
     */
    public function checkCurrencyHasRefer($currency): bool
    {
        $checkModule = [
            \Constants::TYPE_CASH_COLLECTION_INVOICE => \common\library\oms\cash_collection_invoice\CashCollectionInvoiceMetadata::table(),
            \Constants::TYPE_COST_INVOICE            => \common\library\oms\cost_invoice\CostInvoiceMetadata::table(),
            \Constants::TYPE_PURCHASE_RETURN_INVOICE => \common\library\oms\warehouse_return_invoice\purchase\PurchaseReturnInvoiceMetadata::table(),
            \Constants::TYPE_SUPPLIER                => \common\library\supplier\product\SupplierProductMetadata::table(),
            \Constants::TYPE_PURCHASE_ORDER          => \common\library\purchase\purchase_order\PurchaseOrderMetadata::table(),
            \Constants::TYPE_INBOUND_INVOICE         => \common\library\oms\inbound_invoice\InboundInvoiceMetadata::table(),
            \Constants::TYPE_OUTBOUND_INVOICE        => \common\library\oms\outbound_invoice\OutboundInvoiceMetadata::table(),
            \Constants::TYPE_PAYMENT_INVOICE         => \common\library\oms\payment_invoice\PaymentInvoiceMetadata::table(),
            \Constants::TYPE_QUOTATION               => \common\models\client\Quotation::model()->tableName(),
            \Constants::TYPE_OPPORTUNITY             => \Opportunity::model()->tableName(),
            \Constants::TYPE_CASH_COLLECTION         => \common\models\client\CashCollection::model()->tableName(),
            \Constants::TYPE_PRODUCT                 => \common\library\product_v2\ProductMetadata::table(),
            \Constants::TYPE_ORDER                   => \common\models\client\Order::model()->tableName(),
        ];
        $clientId = $this->clientId;
        $db = \PgActiveRecord::getDbByClientId($clientId);

        foreach ($checkModule as $type => $tableName) {
            $where = "client_id={$clientId} and currency = '{$currency}'";
            switch ($type) {
                case \Constants::TYPE_PRODUCT:
                    $where = "client_id={$clientId} and (price_currency = '{$currency}' or cost_currency = '{$currency}')  and enable_flag = 1";
                    break;
                case \Constants::TYPE_INBOUND_INVOICE:
                case \Constants::TYPE_OUTBOUND_INVOICE:
                case \Constants::TYPE_PURCHASE_RETURN_INVOICE:
                    $where .= " and delete_flag = 0";
                    break;
            	default:
                    $where .= " and enable_flag = 1";
            		break;
            }
            $sql = "select create_time from $tableName where {$where} order by create_time desc limit 1";

            $exist = $db->createCommand($sql)->queryScalar();
            if ($exist)
                return true;
        }

        return false;
    }

    /**
     * 更新货币的自定义汇率
     * @param float $rate 货币汇率
     * @param string $currency 货币编号
     * @param string $mainCurrency 换汇目标货币
     * @return bool
     */
    public function updateCurrencyCustomRate($rate, $currency, $mainCurrency)
    {
        if (!in_array($mainCurrency, array_keys(self::$mainCurrencyLists))) {
            return false;
        }

        //不能自定义本币种汇率
        if ($currency == $mainCurrency) {
            return true;
        }

        $user      = \User::getLoginUser();
        $userId    = $user->getUserId();
        $client_id = $user->getClientId();
        $referId   = array_search($currency, ExchangeRateService::$valid_currency);
        $setting   = compact('client_id', 'currency', 'rate');
        
        $this->setMainCurrency($mainCurrency);
        if ($setting['rate'] == -1) {
            $this->deleteCustomRate($currency, $userId, $referId);
            return true;
        }

        if (!$this->updateCustomRate($setting, $client_id, $userId, $referId)) {
            throw new RuntimeException(\Yii::t('edm', 'Failed to update rate'));
        }

        return true;
    }

    public function currencyAllRate()
    {
        $rateList = $this->onlineRates(false, false);
        $currencyRateList = [];
        foreach ($rateList as $rateItem) {

            $currency = $rateItem['name'];
            switch ($currency) {
                case 'USD':
                    $currencyRateList[$currency]['exchange_rate_usd'] = 100;
                    break;
                case 'CNY':
                    $currencyRateList[$currency]['exchange_rate'] = 100;
                    break;
            }

            $rateType = $rateItem['type'] == 'CNY' ? 'exchange_rate' : 'exchange_rate_usd';
            //NTD 特殊处理
            $fBuyPri = $rateItem['name'] == 'NTD' ? 'm_buy_pri' : 'f_buy_pri';
            $currencyRateList[$currency][$rateType] = $rateItem['custom_rate'] === -1 ? $rateItem[$fBuyPri] : $rateItem['custom_rate'];
        }

        //根据 $valid_currency 排序
        $return = [];
        foreach (self::$valid_currency as $currency) {
            if (isset($currencyRateList[$currency])) {
                $return[] = array_merge($currencyRateList[$currency], ['currency' => $currency]);
            }
        }

        return $return;
    }

}
