<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 17/8/14
 * Time: 下午3:23
 */

namespace common\library\group\migration;


use common\models\client\ClientProduct;

class GroupFormatter extends \ListItemFormatter
{
    const RESULT_TYPE_LIST = 1;
    const RESULT_TYPE_TREE = 2;
    const CUSTOMER_COUNT_TYPE_ALL =0;
    const CUSTOMER_COUNT_TYPE_PRIVATE =1;
    const CUSTOMER_COUNT_TYPE_PUBLIC =2;

    protected  $clientId;
    protected  $userId;
    protected  $groupType;
    protected  $showFieldsInfo = true;
    protected  $specifyFields;
    protected  $externalFields;
    protected  $showProductCount = false;
    protected  $showCustomerCount = false;
    protected  $showRecursiveCustomerCount =false;
    protected  $showCustomerCountType= 0;
    protected  $showSystemGroupFlag = false;
    protected  $resultType;
    protected  $includeRoot = false;
    protected  $relateId;

    public function __construct($clientId,$groupType)
    {
        $this->clientId = $clientId;
        $this->groupType = $groupType;
    }

    public function setRelateId($relateId)
    {
        $this->relateId = $relateId;
    }

    /**
     * @param mixed $userId
     */
    public function setUserId($userId)
    {
        $this->userId = $userId;
    }


    /**
     * @param bool $showFieldsInfo
     */
    public function setShowFieldsInfo(bool $showFieldsInfo)
    {
        $this->showFieldsInfo = $showFieldsInfo;
    }

    /**
     * @param mixed $specifyFields
     */
    public function setSpecifyFields($specifyFields)
    { $this->specifyFields = $specifyFields;
    }

    /**
     * @param mixed $showProductCount
     */
    public function setShowProductCount($showProductCount)
    {
        $this->showProductCount = $showProductCount;
    }

    /**
     * @param mixed $resultType
     */
    public function setResultType($resultType)
    {
        $this->resultType = $resultType;
    }

    /**
     * @param bool $includeRoot
     */
    public function setIncludeRoot(bool $includeRoot)
    {
        $this->includeRoot = $includeRoot;
    }

    /**
     * @param bool $showSystemGroupFlag
     */
    public function setShowSystemGroupFlag(bool $showSystemGroupFlag)
    {
        $this->showSystemGroupFlag = $showSystemGroupFlag;
    }

    /**
     * @param array $externalFields
     */
    public function setExternalFields(array $externalFields)
    {
        $this->externalFields = $externalFields;
    }

    /**
     * @param bool $showCustomerCount
     */
    public function setShowCustomerCount(bool $showCustomerCount)
    {
        $this->showCustomerCount = $showCustomerCount;
    }

    /**
     * @param bool $showRecursiveCustomerCount
     */
    public function setShowRecursiveCustomerCount(bool $showRecursiveCustomerCount)
    {
        $this->showRecursiveCustomerCount = $showRecursiveCustomerCount;
    }

    /**
     * @param int $showCustomerCountType
     */
    public function setShowCustomerCountType(int $showCustomerCountType)
    {
        $this->showCustomerCountType = $showCustomerCountType;
    }



    public function buildMapData()
    {
        $list = $this->batchFlag ? $this->listData : [$this->data];
        $groupIds = array_column($list,'id');

        $productCountMap = [];
        if( $this->showProductCount ){
            $productCountMap = ClientProduct::getMapGroupIdVsNumList($this->clientId,$groupIds, $this->relateId);
        }

        $systemGroupMap = [];
        if( $this->showSystemGroupFlag ){
            $systemMap = \common\library\setting\library\group\GroupMetadata::getExtraDataMap($this->groupType);
            foreach ( $groupIds as $groupId ){
                $systemGroupMap[$groupId] = isset($systemMap[$groupId]) ?1:0;
            }
        }

        $groupExternalFieldMap = [];
        if( !empty($this->externalFields) ){
            $externalField = $this->externalFields;
            $userIds = $this->userId?[0,$this->userId]:[0];

            $settingList = new GroupSettingList($this->clientId);
            $settingList->setType($this->groupType);
            $settingList->setUserIds($userIds);
            $settingList->setKeys($this->externalFields);
            $groupSettingList = $settingList->find();

            $groupExternalFieldMap = array_reduce($groupSettingList,function ($carry,$item)use($externalField){
                $carry[$item['group_id']][] = $item;
                return $carry;
            },[]);
        }

        $customerCountMap = [];

        if( $this->showCustomerCount || $this->showRecursiveCustomerCount){

            if($this->showCustomerCountType == self::CUSTOMER_COUNT_TYPE_ALL)
                $userNum =[0,1,2];
            elseif ($this->showCustomerCountType == self::CUSTOMER_COUNT_TYPE_PRIVATE)
                $userNum =[1,2];
            else
                $userNum =[0];
            $customerCountMap = \common\library\customer\Helper::getGroupCount($this->clientId,$this->userId,$userNum);
        }

        $map = [
            'product_count' =>$productCountMap,
            'system_group' =>$systemGroupMap,
            'external_field' => $groupExternalFieldMap,
            'customer_count' =>$customerCountMap,
        ];

        $this->setMapData($map);

        parent::buildMapData();
    }

    protected function buildFieldsInfo($data)
    {
        $result = [];

        if( !$this->showFieldsInfo ) return $result;

        $specifyFields = $this->specifyFields;
        if ($this->specifyFields === null) {
            $specifyFields = array_keys($data);
        }

        $result = \ArrayUtil::columns($specifyFields, $data, '');

        return $result;
    }

    public function renderListInfoSetting(){

        $this->setSpecifyFields([
            'name',
            'prefix',
            'parent_id',
            'rank',
            'layer',
            'description'
        ]);
        $this->setShowSystemGroupFlag(true);
    }

    public function listInfoSetting(){

        $this->setSpecifyFields([
            'name',
            'prefix',
            'parent_id',
            'rank',
            'layer'
        ]);
        $this->setShowSystemGroupFlag(true);
    }

    public function appTreeListInfoSetting(){

        $this->setSpecifyFields([
            'name',
            'prefix',
            'parent_id',
            'rank',
            'layer'
        ]);
        $this->setShowSystemGroupFlag(true);
    }

    public function appListInfoSetting(){

        $this->setSpecifyFields([
            'name',
        ]);
        $this->setResultType(self::RESULT_TYPE_LIST);
    }

    public function groupConfigListSetting(){
        $this->setSpecifyFields([
            'name',
            'rank',
            'prefix',
        ]);
        $this->setExternalFields([
            Group::External_KEY_PUBLIC_TIME,
            Group::External_KEY_START_PUBLIC_TIME,
            Group::External_KEY_PUBLIC_IGNORE_FORZEN_USER,
            Group::External_KEY_OWNER_ID
        ]);
        $this->setShowSystemGroupFlag(true);
    }

    public function cmsDetailSetting()
    {
        $this->setExternalFields([
            Group::EXTERNAL_KEY_IMAGE_MATERIAL_ID,
        ]);
    }

    public function userGroupConfigListSetting(){
        $this->setSpecifyFields([
            'name',
            'layer'
        ]);
        $this->setExternalFields([
            Group::External_KEY_SHOW_FLAG,
            Group::External_KEY_ORDER_RANK,
            Group::External_KEY_PUBLIC_TIME,
            Group::External_KEY_START_PUBLIC_TIME,
            Group::External_KEY_PUBLIC_IGNORE_FORZEN_USER,
            Group::External_KEY_OWNER_ID
        ]);
        $this->setShowSystemGroupFlag(true);
    }


    public function getCustomerCount($data,$recursive=false){
        $count = $this->getMapData('customer_count',$data['id'])??0;
        if($recursive &&$data['id']){   //不包含未分组
            $list = $this->batchFlag ? $this->listData : [$this->data];
            foreach ($list as $item){

                $prefix = trim(substr($item['prefix'], 2), '-');
                $parentIds = explode('-', $prefix);
                if(in_array($data['id'],$parentIds))
                    $count+=$this->getMapData('customer_count',$item['id'])??0;
            }
        }

        return $count;
    }


    public function format($data)
    {
        $groupId = intval($data['id']);

        $result =[
            'id' => $groupId,
        ];

        $result = array_merge($result,$this->buildFieldsInfo($data));
        if( !empty($this->externalFields) ){
            $externalField = $this->getMapData('external_field',$groupId)??[];
            $externalFieldMap = array_column($externalField,'value','key');
            foreach ( $this->externalFields as $key){
                switch ( $key ) {
                    case Group::External_KEY_SHOW_FLAG:
                        //自定义显示，默认显示的
                        $result[$key] = $externalFieldMap[$key]??'1';
                        break;
                    case Group::External_KEY_PUBLIC_IGNORE_FORZEN_USER:
                        // 之前设置过公海规则默认不跳过冻结用户，新增公海规则默认跳过冻结用户
                        if (isset($externalFieldMap[Group::External_KEY_PUBLIC_TIME])) {
                            $result[$key] = intval($externalFieldMap[$key] ?? '0');
                        } else {
                            $result[$key] = intval($externalFieldMap[$key] ?? '1');
                        }
                        break;
                    case Group::External_KEY_OWNER_ID:
                        $result[$key] = $externalFieldMap[$key]??[];
                        break;
                    default:
                        $result[$key] = $externalFieldMap[$key]??'';
                }

            }

        }

        if( $this->showSystemGroupFlag ){
            $result['is_sys'] = $this->getMapData('system_group',$groupId)??0;
        }

        if( $this->showProductCount ){
            $result['product_count'] = $this->getMapData('product_count',$groupId)??0;
        }

        if( $this->showCustomerCount ){
            $result['customer_count'] = $this->getCustomerCount($data,false);
        }

        if( $this->showRecursiveCustomerCount ){
            $result['recursive_customer_count'] = $this->getCustomerCount($data,true);
        }


        return $result;
    }

    public function result()
    {
        $result = [];
        $this->buildMapData();
        if (!$this->batchFlag)
        {
            $data = $this->data;
            if ($this->needStrip)
                $data = $this->strip($data);

            $result = $this->format($data);
            return $result;
        }

        $first = reset($this->listData);
        $initLayer = $first ? $first['layer'] : 0;

        foreach ($this->listData as $data)
        {
            if ($this->needStrip)
                $data = $this->strip($data);

            if( $this->resultType == self::RESULT_TYPE_TREE ){

                if ($initLayer == $data['layer'])
                {
                    $result['node'][$data['id']] = $this->format($data);
                }
                else if ($initLayer < $data['layer']) {
                    $prefix = trim(substr($data['prefix'], 2), '-');
                    $depTree = explode('-', $prefix);

                    $origin = &$result;

                    for ($i = $initLayer; $i < $data['layer']; ++$i) {
                        $process = &$origin['node'][$depTree[$i - 1]];

                        unset($origin);

                        $origin = &$process;

                        unset($process);
                    }

                    if (!isset($origin['node'])) {
                        $origin['node'] = [];
                    }

                    $origin['node'][$data['id']] = $this->format($data);
                }
            }else{
                $this->constructListResult($result, $this->format($data));
            }

        }


        if($this->resultType == self::RESULT_TYPE_TREE ){

            if( $this->includeRoot ){
                $result['node'] = array_values($result['node'] ?? []);
                $this->removeNodeKey($result);
            } else{
                $result = array_values($result['node'] ?? []);
                $this->removeNodeKey($result);
            }

        }

        return $result;

    }


    private function removeNodeKey(&$data)
    {
        if (!is_array($data))
            return;

        foreach ($data as &$elem)
        {
            if (isset($elem['node']) && is_array($elem['node']))
            {
                $elem['node'] = array_values($elem['node']);
                $this->removeNodeKey($elem['node']);
            }
        }
    }


}