<?php

/**
 * This file is part of xiaoman-crm.
 *
 * Copyright © 2012 - 2020 Xiaoman. All Rights Reserved.
 *
 * Created by <PERSON><PERSON><PERSON><PERSON> <she<PERSON><PERSON><PERSON>@xiaoman.cn>, on 2020/02/13.
 */

namespace common\library\queue_v2\job;

use common\library\queue_v2\middleware\Cleanup;
use common\library\queue_v2\middleware\JobLogger;
use common\library\queue_v2\middleware\JobRecorder;
use common\library\queue_v2\QueueConstant;
use common\library\queue_v2\QueueService;
use <PERSON><PERSON>\Queue\Job\JobFactory;
use <PERSON><PERSON>\Queue\Exception\QueueException;
use <PERSON><PERSON>\Queue\Message\Message;
use <PERSON><PERSON>\Queue\Middleware\WithoutOverlapping;

abstract class BaseJob
{
    public $jobName;

    public $maxTries = 288;
    public $tag = QueueConstant::CONSUMER_PRIORITY_HIGH;
    public $channel = QueueConstant::CONNECTION_NAME_DEFAULT;
    public $sharing = '';

    public $isDeadLetter = false;
    public $key;

    public $maxExceptions = 3;

    public $timeout = 300;

    // 超过该时长会终止消费
    public $maxTimeout = 1800;

    public $expiredAt = 0;

    public $released = 0;
    
    public $isDelay = false;
//    public $retryUntil = null;

    public function setShareKey($key)
    {
        $this->sharing = $key;
    }

//    abstract function handle();

    /**
     * After failed, this function will be called.
     *
     * @param int $id
     * @param array $payload
     *
     * @throws \Throwable
     */
    public function failed(\Throwable $exception, Message $message)
    {
        \LogUtil::exception($exception);
        if ($exception instanceof QueueException) {
            \LogUtil::exception($exception, $exception->getErrorInfo());
        } else {
            \LogUtil::exception($exception);
        }
    }

    public function release(Message $message)
    {
        $messageKey = method_exists($message, 'getMessageKey') ? $message->getMessageKey() : '';
        if ($message->expired()) {
            \LogUtil::info("id=" . $message->getMessageId() . " key=" . ($messageKey) . " is expired, skip release back ");
        }
        $this->key = $messageKey;
        if (!$this->released) {
            $this->released += 1;
            $newMessageId = QueueService::dispatch($this);
            \LogUtil::info("id=" . $message->getMessageId() . " key=" . ($messageKey) . " release back, new message id={$newMessageId}");
            $message->delete();
        }


        return $newMessageId ?? null;
    }

    public static function createPayload($job)
    {
        return JobFactory::createPayload($job);
    }

    public static function getJob($payload)
    {
        return JobFactory::getJob($payload);
    }

    public function initByMessage(Message $message)
    {
        \LogUtil::withContext(array_replace([
            'name' => $message->getName(),
            'id' => $message->getMessageId(),
            'key' => method_exists($message, 'getMessageKey') ? $message->getMessageKey() : '',
            'publish_time' => method_exists($message, 'getPublishTime') ? $message->getPublishTime() : '',
        ], method_exists($message, 'getProperties') ? $message->getProperties() : []));
    }

    /**
     * Get the middleware the job should pass through.
     *
     * @return array
     */
    final public function middleware()
    {
        $middlewares = [];

        $middlewares[] = (new Cleanup());

        if ($loggerName = $this->loggerName()) {
            $middlewares[] = (new JobLogger())->setName($loggerName);
        }

        if ($this->needRecord()) {
            $middlewares[] = new JobRecorder();
        }

        $additionMiddlewares = $this->additionMiddleware() ?: [];
        $middlewares = array_merge($middlewares, $additionMiddlewares);

        if ($this->withoutOverlapping()) {
            $middlewares[] = (new WithoutOverlapping($this->withoutOverlappingKey(), $this->releaseAtIfOverlapping(), $this->maxTimeout));
        }

        usort($middlewares, function($a, $b) {
            return (array_flip(QueueConstant::MIDDLEWARE_SORT)[get_class($a)] ?? PHP_INT_MAX) >= (array_flip(QueueConstant::MIDDLEWARE_SORT)[get_class($b)] ?? PHP_INT_MAX) ? 1 : -1;
        });

        return $middlewares;
    }

    public function additionMiddleware()
    {
        return [];
    }

    public function needRecord()
    {
        return false;
    }

    protected function withoutOverlapping()
    {
        return false;
    }

    protected function withoutOverlappingKey()
    {
        return '';
    }

    protected function withoutOverlappinpExpiredAt()
    {
        return $this->timeout;
    }

    protected function releaseAtIfOverlapping()
    {
        // null-delete int-release
        return 0;
    }

    public function getDelay()
    {
        return 0;
    }

    public function loggerName()
    {
        return null;
    }

    public function getName()
    {
        return $this->jobName ?? static::class;
    }
    
    /**
     * @return bool
     */
    public function isDelay(): bool {
        
        return $this->isDelay;
    }
    
    /**
     * @param bool $isDelay
     */
    public function setIsDelay(bool $isDelay): void {
        
        $this->isDelay = $isDelay;
    }
    public function merge(BaseJob $job) {
        
        return $this;
    }

    public function forceAsync() : bool {
        return false;
    }
}
