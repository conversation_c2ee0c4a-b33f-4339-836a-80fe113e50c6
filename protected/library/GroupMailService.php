<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 14-11-11
 * Time: 下午4:52
 */

use common\library\email\Util;
use common\library\email_identity\cards\Card;
use common\library\email_identity\EmailIdentity;
use common\library\trail\events\EdmEvents;
use common\library\trail\TrailConstants;

class GroupMailService
{

    public static function batchInsert($taskId, $userId, array $customerList, $setStatus = 1)
    {
        $user = User::getUserObject($userId);
        $clientId = $user->getClientId();

        $paramsArray = array();
        $time = time();
        $nowTime = date('Y-m-d H:i:s', $time);
        $failCount = 0;

        $groupList = array();
        $starList = array();
        $countryList = array();
        $originList = array();
        $scoreList = array();

        $task = GroupMailTaskService::findById($taskId, $userId);

        $count = count($customerList);
        $maxId = ProjectActiveRecord::produceAutoIncrementId($count);

        $id = $maxId - $count;

        foreach($customerList as $c)
        {
            ++$id;

            $failType = 0;
            $status = 0;

            $email = Util::escapeDoubleQuoteSql($c['email']);
            $name = Util::escapeDoubleQuoteSql($c['name']);
            if (empty($name))
                $name = substr($email, 0, strpos($email, '@'));

            $paramsArray[] = "($id, $taskId, '$name', '$email',$status,$failType,'$nowTime', $userId,$clientId, \"{$c['customer_id']}\")";

            // 客户线索
            if (isset($c['company_id']) && $c['company_id']) {
                // EDM动态事件（新）
                try {
                    $trail = new EdmEvents();
                    $trail->setType(TrailConstants::TYPE_EDM_SEND);
                    $trail->setClientId($user->getClientId());
                    $trail->setCreateUser($user->getUserId());
                    $trail->setUserId($user->getUserId());
                    $trail->setCompanyId($c['company_id']);
                    $trail->setCustomerId($c['customer_id']);
                    $trail->setReferId($taskId);
                    $trail->run();
                } catch (\RuntimeException $e) {
                    \LogUtil::info("EDM发件动态生成失败，task_id={$taskId} " . $e->getMessage());
                }
            }

            $c['group_id'] = intval($c['group_id'] ?? 0);
            $c['star'] = intval($c['star'] ?? 0);
            $c['country'] = $c['country'] ?? 0;
            $c['origin'] = intval($c['origin'] ?? 0);
            $score = intval(ceil(intval($c['score'] ?? 0) / 10));

            $groupList[$c['group_id']] = ($groupList[$c['group_id']] ?? 0) + 1;
            $starList[$c['star']] = ($starList[$c['star']] ?? 0) + 1;
            $countryList[$c['country']] = ($countryList[$c['country']] ?? 0) + 1;
            $originList[$c['origin']] = ($originList[$c['origin']] ?? 0) + 1;
            $scoreList[$score] = ($scoreList[$score] ?? 0) + 1;

            }

        $countCustomerList = count($customerList);
        StatisticsService::edmSend($user->getClientId(), $user->getUserId(), $countCustomerList, $groupList, $starList, $countryList, $originList,$scoreList);

        $sql = 'insert into tbl_group_mail(group_mail_id, task_id, send_to_name, send_to,status,fail_type,create_time,user_id,client_id,customer_id) values ';

        //分割500执行一次插入
        $paramsArray =  array_chunk($paramsArray,500);
        $sqlArr = array();
        foreach($paramsArray as $value){
            $valuesStr =implode(',',$value);
            $sqlArr[] = $sql.$valuesStr;
        }
        $transaction = GroupMail::model()->dbConnection->beginTransaction();
        try{
            $con = GroupMail::model()->dbConnection;
            $count = 0;
            foreach($sqlArr as $sqlStr){
                $count_item = $con->createCommand($sqlStr)->execute();
                $count +=$count_item;
            }
            $task = GroupMailTaskService::findById($taskId, $userId);
            // 如果邮件数量少于100，直接通过
            if (count($customerList) <= 100)
            {
                $task->level = GroupMailTaskService::LEVEL_GOOD;
            }
            //更新task表的status为分解完成
            $task->status = $setStatus;
            $task->send_to_count = $count;
            if($failCount > 0){
                $task->finish_count = $failCount;
                $task->fail_count = $failCount;
                $task->invalid_count = $failCount;
            }
            if(!$task->save()){
                throw new \RuntimeException(\Yii::t('mail', 'Update task failed'));
            }
            $transaction->commit();
        }catch (Exception $e){
            $transaction->rollback();
            throw new RuntimeException($e->getMessage());
        }
    }

    /**
     * @param $taskId
     * @param $userId
     * @param int $offset
     * @param int $pageSize
     * @return GroupMail[]
     */
    public static function findOpenedMail($taskId, $userId, $offset=0, $pageSize=10)
    {
        return GroupMail::model()->findAll('task_id=:taskId and user_id=:userId and view_count>0 order by last_view_time desc limit :offset,:pageSize', array(
            ':taskId'=>$taskId,
            ':userId'=>$userId,
            ':offset'=>$offset,
            ':pageSize'=>$pageSize
        ));
    }

    public static function getOpenedCountByTaskId($taskId, $userId){
        return GroupMail::model()->count('task_id=:taskId and user_id=:userId and view_count>0 and enable_flag=1',
            array(':taskId'=>$taskId, ':userId'=>$userId)
        );
    }

    public static function getOpenedInfo($taskId, $userId, $offset = 0, $pageSize = 10, $cardDirectOwner = true)
    {
        $groupMails = self::findOpenedMail($taskId, $userId, $offset, $pageSize);
        $result = array();

        $emailList = array_map(function($elem){return $elem->send_to;}, $groupMails);
        $stat = array();

        $emailInfo = EmailInfo::query($emailList);
        if ($emailInfo === false)
        {
            $emailInfo = array();
            LogUtil::error(__METHOD__." 获取java后台邮件信息失败");
        }

        foreach ($emailInfo as $info)
        {
            $stat[$info['_id']] = EmailInfo::stat($info);
        }

        $findEmails = [];
        foreach ($groupMails as $groupMail) {
            $pureEmail = Util::getPureEmail($groupMail->send_to);
            $findEmails[$groupMail->group_mail_id] = $pureEmail;
        }

        $cardMap = [];
        if (!empty($findEmails)){
            $card = new Card($userId);
            $cardMap = $card->cardTypeMap($findEmails, $cardDirectOwner);
        }

        foreach ($groupMails as $g)
        {
            $item = array(
                'customer_id' => $g->customer_id,
                'location' => $g->last_view_country . ' ' . $g->last_view_province . ' ' . $g->last_view_city,
                'mail' => $g->send_to,
                'date' => $g->last_view_time,
                'view_count' => (int)$g->view_count
            );

            $pureEmail = $findEmails[$g->group_mail_id] ?? Util::getPureEmail($g->send_to);
            $item['card_type'] = $cardMap[$pureEmail] ?? EmailIdentity::CARD_TYPE_STRANGER;

            $result[] = array_merge($item, array_key_exists($g->send_to, $stat) ? $stat[$g->send_to] : array('active'=>0, 'popular'=>0));
        }
        return $result;
    }

    //获取针对一个客户所有营销的发送次数
    public static function getSendCount($userId, $customerId){
        return GroupMail::model()->count('user_id=:userId and customer_id=:customerId',
            array(':userId'=>$userId, ':customerId'=>$customerId)
        );
    }
    //获取针对一个客户所有营销的送达次数
    public static function getSuccessCount($userId, $customerId){
        return GroupMail::model()->count('user_id=:userId and customer_id=:customerId and isdelivered=1',
            array(':userId'=>$userId, ':customerId'=>$customerId)
        );
    }
    //获取针对一个客户所有营销的失败次数
    public static function getFailedCount($userId, $customerId){
        return GroupMail::model()->count('user_id=:userId and customer_id=:customerId and (status=4 or fail_type!=0)',
            array(':userId'=>$userId, ':customerId'=>$customerId)
        );
    }
    //获取针对一个客户所有营销的打开次数
    public static function getOpenedCount($userId, $customerId){
        return GroupMail::model()->count('user_id=:userId and customer_id=:customerId and view_count>0',
            array(':userId'=>$userId, ':customerId'=>$customerId)
        );
    }
    //获取针对一个客户所有营销的回复次数
    public static function getReplyCountByCustomer($userId, $customerId){
        return GroupMail::model()->count('user_id=:userId and customer_id=:customerId and reply_mail_id>0',
            array(':userId'=>$userId, ':customerId'=>$customerId)
        );
    }
    public static function getReplyCount($taskId){
        $lists = GroupMail::model()->findAll("task_id=:taskId and reply_mail_id > 0", array(':taskId'=>$taskId));
        return count($lists);
    }

    public static function findFailedMail($taskId, $userId, $offset=0, $pageSize=10){
        return GroupMail::model()->findAll('task_id=:taskId and user_id=:userId and ((status=3 and fail_type>0 and isdelivered=0) or status>3) limit :offset,:pageSize', array(
            ':taskId'=>$taskId,
            ':userId'=>$userId,
            ':offset'=>$offset,
            ':pageSize'=>$pageSize
        ));
    }
    public static function getFailedMailCount($taskId, $userId){
        $sql = "SELECT COUNT(*) AS faild_count FROM tbl_group_mail WHERE task_id=:taskId and user_id=:userId and ((status=3 and fail_type>0 and isdelivered=0) or status>3)";
        return GroupMail::model()->getDbConnection()->createCommand($sql)->queryScalar(array(':taskId'=>$taskId, ':userId'=>$userId));
    }
    
    //已回复邮件
    public static function getReplyedMailCountByTask($taskId, $userId){
        $c=new CDbCriteria();
        $c->addCondition('task_id='.$taskId);
        $c->addCondition('user_id='.$userId);
        $c->addCondition('enable_flag=1');
        $c->addCondition('reply_mail_id > 0');
        return GroupMail::model()->count($c);
    }

    public static function getGroupMailByTaskIdAndSendTo($taskId, $userId, $sendToList)
    {
        $prams = [
            ':taskId' => $taskId,
            ':userId' => $userId,
        ];
        $prepareParams = \common\library\util\SqlBuilder::buildInWhere($sendToList,$prams);
        $sql = 'task_id=:taskId and user_id=:userId and enable_flag=1 and send_to in (' . implode(',', $prepareParams). ')';

        return GroupMail::model()->findAll($sql, $prams);
    }

}