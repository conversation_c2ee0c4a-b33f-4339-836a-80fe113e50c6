<?php
/**
 * Created by PhpStorm.
 * User: andy
 * Date: 2017/4/11
 * Time: 14:20
 */

namespace common\library\customer;

use common\library\alibaba\customer\AlibabaCustomerRelationList;
use common\library\alibaba\customer\AlibabaCustomerService;
use common\library\custom_field\company_field\CustomerField;
use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldList;
use common\library\privilege_v3\field\FormatterFieldPrivilegeTrait;
use common\library\privilege_v3\PrivilegeService;
use common\library\trail\CompanyDynamicList;
use common\library\util\PgsqlUtil;
use Constants;

/**
 * @deprecated
 *
 * Class CustomerFormatter
 * @package common\library\customer
 */
class CustomerFormatter extends \ListItemFormatter
{
    use FormatterFieldPrivilegeTrait;

    protected $clientId;

    protected $specifyFields;
    protected $showInfoGroup = false;
    protected $showCloudStat = false;
    protected $showLastMail = false;
    protected $trailUserId;
    //勿直接调用 用getCompanyFieldFormatter()按需加载
    protected $fieldFormatter;
    protected $showCompanyInfo;
    protected $flattenCompanyFields = [];
    protected $excludeBoundAliBuyer;

    protected $translateLanguageMap = [];

    protected $hideInfo = false;

    protected $userId;

    protected $filterDisableFields = false;
    
    
    public function __construct($clientId)
    {
        $this->clientId = $clientId;
    }

    protected $visitFrom;

    /**
     * @param mixed $visitFrom
     */
    public function setVisitFrom($visitFrom): void
    {
        $this->visitFrom = $visitFrom;
    }


    public function buildMapData()
    {
        $list = $this->batchFlag ? $this->listData : [$this->data];
        $clientId = $this->clientId;
        $loginUser = \User::getLoginUser();
        $user_id = $loginUser->getUserId();

        $externalFieldDatas = array();
        // 部分查询设置了查询字段setFields，未包括external_field_data字段，然后直接formatter->result()获取结果
        array_map(function ($elem) use (&$externalFieldDatas){
            $externalFieldDatas[] = (isset($elem['external_field_data']) && is_array($elem['external_field_data'])) ? $elem['external_field_data'] : json_decode($elem['external_field_data'] ?? '{}', true);
        },$list);

        $companyList = [];
        if (is_array($this->specifyFields) && array_intersect(['company_name'], $this->specifyFields) || ($this->showCompanyInfo && !isset($this->mapData['company_info']))) {
            $companyIds = array_column($list, 'company_id');
            if (array_filter($companyIds)) {
                $companyListObj = new CompanyList($user_id);
                $companyListObj->getFormatter()->setFilterDisableFields($this->filterDisableFields);
                $companyListObj->setCompanyIds($companyIds);
                $companyListObj->setSkipPrivilege(true);
                if ($this->showCompanyInfo) {
                    $companyListObj->getFormatter()->setSpecifyFields([
                        'name',
                        'short_name',
                        'company_id',
                        'archive_type',
                        'owner',
                        'serial_id',
                        'order_time',
                        'tel_full',
                        'homepage',
                        'fax',
                        'address',
                    ]);
                } else {
                    $companyListObj->getFormatter()->setSpecifyFields([
                        'name',
                    ]);
                }
                $companyList = $companyListObj->find();
            }
        }

        $customerIds = array_column($list, 'customer_id');
        if ($this->excludeBoundAliBuyer && $customerIds) {
            $list = new AlibabaCustomerRelationList($clientId);
            $list->setCustomerId($customerIds);
            $list->existsBuyerAccountId(true);
            $list->setFields(['customer_id']);
            $this->mapData['bound_buyer_customers'] = array_column($list->find(), 'customer_id', 'customer_id');
        }

        !isset($this->mapData['company_name']) && $this->mapData['company_name'] = array_column($companyList, 'name', 'company_id');
        !isset($this->mapData['company_info']) && $this->mapData['company_info'] = array_column($companyList, null, 'company_id');
    
        if ($this->filterDisableFields) {
            
            $this->mapData['disableFields'] = array_filter(array_column((isset($companyListObj) ? $companyListObj->getFormatter()->getPrivilegeFieldStats() : $this->getPrivilegeFieldStats()), 'disable', 'refer_type'))[$this->getFieldReferType()] ?? [];
        }

//        TODO:????????????????
        // 处理图片文件
        if ($this->visitFrom!='app'){
            $filterFieldIds = [];
            $fileFieldTypes = [CustomFieldService::FIELD_TYPE_IMAGE, CustomFieldService::FIELD_TYPE_ATTACH];
            $fileInfoMap = [];
            $customFields = Helper::getExternalFieldSetting($clientId, Constants::TYPE_CUSTOMER);
            foreach ($customFields as $field) {
                $customFieldType = $field['field_type'];
                $upstreamFieldType = $field['relation_origin_field_type'] ?? 0;
                if (in_array($customFieldType, $fileFieldTypes) || in_array($upstreamFieldType, $fileFieldTypes)) {
                    $filterFieldIds[] = $field['id'];
                }
            }

            $filterFieldIds = array_unique($filterFieldIds);
            $externalFileIds = [];
            foreach ($externalFieldDatas as $id => $data) {
                foreach ($filterFieldIds as $id) {
                    if (!empty($data[$id])) {
                        $externalFileIds = array_unique(array_merge(array_column($data[$id], 'file_id'),$externalFileIds));
                    }
                }
            }
            $objArray = \UploadFile::findByIds($externalFileIds);
            if (!empty($objArray)) {
                foreach ($objArray as $obj) {
                    $upload = new \AliyunUpload();
                    $upload->loadByObject($obj);
                    $preview_url = $upload->getPreview();
                    $fileInfoMap[$obj->file_id] = array(
                        'file_id' => $upload->getFileId(),
                        'file_url' => $upload->getFileUrl(),
                        'download_url' => $upload->generatePresignedUrl(),
                        'preview_url' => $preview_url,
                        'file_preview_url' => $preview_url,//前端需要这个字段，老组件无法修改，影响范围很大
                        'file_name' => $upload->getFileName(),
                        'file_ext' => $upload->getFileExt(),
                        'file_size' => $upload->getFileSize(),
                    );
                }
            }
            $this->mapData['fileInfo'] = $fileInfoMap;
        }
    }

    public function setUserId($userId)
    {
        $this->userId = $userId;
        $this->setFieldUserId($userId);
    }

    public function setSpecifyFields($fields)
    {
        $this->specifyFields = $fields;
    }

    public function setShowCloudStat($flag)
    {
        $this->showCloudStat = $flag;
    }

    public function setShowInfoGroup($flag)
    {
        $this->showInfoGroup = $flag;
    }

    public function setCompanyNameMap($map)
    {
        $this->mapData['company_name'] = $map;
    }

    public function setUserLastMail($userId)
    {
        $this->trailUserId = $userId;
        $this->showLastMail = $userId ? 1 : 0;
    }

    /**
     * @param mixed $showCompanyInfo
     */
    public function setShowCompanyInfo($showCompanyInfo)
    {
        $this->showCompanyInfo = $showCompanyInfo;
    }

    public function flattenCompanyFields(array $companyField)
    {
        $this->flattenCompanyFields = $companyField;
    }

    /**
     * 排除绑定了阿里买家ID的联系人
     * @return void
     */
    public function excludeBoundAliBuyer($flag)
    {
        $this->excludeBoundAliBuyer = $flag;
    }

    /**
     * @param bool $hideInfo
     */
    public function setHideInfo(bool $hideInfo)
    {
        $this->hideInfo = $hideInfo;
    }
    
    /**
     * @param bool $filterDisableFields
     */
    public function setFilterDisableFields(bool $filterDisableFields): void {
        
        $this->filterDisableFields = $filterDisableFields;
    }

    public function getFieldFormatter(){
        if(empty($this->fieldFormatter)){
            $this->initFieldPrivilegeForFormatter();
            $this->fieldFormatter = new CustomerField($this->clientId);
            $this->fieldFormatter->setFieldUserId($this->getFieldUserId());
            $this->fieldFormatter->setFieldFunctionalId($this->getFieldFunctionalId());
        }
        return $this->fieldFormatter;
    }

    public function strip($data)
    {
        $data['external_field_data'] = json_decode($data['external_field_data'] ?? '{}', true);

        //特殊兼容之前的数据
        if (strlen($data['contact']) > 2 && substr($data['contact'], 0, 1) == '{') {
            $data['contact'] = '[' . $data['contact'] . ']';
        } elseif ($data['contact'] == '""' || $data['contact'] == "''") {
            $data['contact'] = '[]';
        }
        $data['contact'] = json_decode($data['contact'], true);

	    $data['contact'] = array_values(array_filter($data['contact'], function ($item) {

		    return (!empty($item['type']) && !empty($item['value']) && !is_array($item['type']) && !is_array($item['value']));
	    }));

        $data['user_data'] = json_decode($data['user_data'], true);
        $data['tag'] = json_decode($data['tag'], true);
        $data['user_id'] = PgsqlUtil::trimArray($data['user_id']);
        $data['image_list'] = PgsqlUtil::trimArray($data['image_list']);
        $fullTelList = str_replace(['{', '}'], ['', ''], $data['full_tel_list']);
        $fullTelList = explode(',', $fullTelList);
        $data['full_tel_list'] = $fullTelList;
        $data['tel_list'] = json_decode($data['tel_list'], true);
        return $data;
    }

    public function defaultSetting()
    {

    }

    public function baseInfoSetting()
    {
        $this->setSpecifyFields([
            'customer_id',
            'company_id',
            'name',
            'email',
            'tel_list',
            'contact',
            'post',
            'main_customer_flag',
        ]);
    }

    public function mainCustomerInfoSetting()
    {
        $this->setSpecifyFields([
            'customer_id',
            'company_id',
            'name',
            'email',
            'tel_list',
            'post',
            'contact',
            'main_customer_flag',
            'growth_level',
            'suspected_invalid_email_flag',
            'forbidden_flag'
        ]);
    }


    public function detailInfoSetting()
    {
        $this->setSpecifyFields(['customer_id', 'company_id', 'name', 'email', 'main_customer_flag', 'growth_level','suspected_invalid_email_flag','forbidden_flag']);
        $this->setShowInfoGroup(true);
    }

    public function appBaseInfoSetting()
    {
        $this->setSpecifyFields([
            'customer_id',
            'company_id',
            'name',
            'email',
            'birth',
            'tel_list',
            'post',
            'post_grade',
            'post_grade_name',
            'remark',
            'gender',
            'gender_name',
            'external_field_data',
            'contact',
            'image_list',
            'main_customer_flag',
            'growth_level',
            'suspected_invalid_email_flag',
            'forbidden_flag'
        ]);
    }

    public function appDetailInfoSetting()
    {
        $this->setSpecifyFields(['customer_id', 'company_id', 'name', 'email', 'main_customer_flag', 'growth_level']);
        $this->setShowInfoGroup(true);
    }

    public function cardInfoSetting()
    {
        $this->setSpecifyFields([
            'customer_id',
            'company_id',
            'name',
            'email',
            'tel_list',
            'post',
            'contact',
            'main_customer_flag',
            'remark',
        ]);
    }

    protected function produceBaseInfo($data)
    {
        $result = [];

        $specifyFields = $this->specifyFields;

        if ($this->specifyFields === null) {
            $specifyFields = array_keys($data);
        }

        $genderArr = ['未知', '男', '女'];
        $postGradeArr = ['', '普通职员', '中层管理者', '高层管理者'];
        $userList = $data['user_id'];

        foreach ($specifyFields as $field) {
            switch ($field) {
                case 'tel_list':
                    if (!empty($data['tel']) || !empty($data['tel_area_code'])) {
                        $data['tel_list'][] = [$data['tel_area_code'], $data['tel']];
                    }
                    $result['tel_list'] = $data['tel_list'];
                    break;
                case 'full_tel_list':
                    if (!empty($data['tel_full'])) {
                        $data['full_tel_list'][] = $data['tel_full'];
                    }
                    $result['full_tel_list'] = $data['full_tel_list'];
                    break;
                case 'external_field_data':
                    $efd = $data['external_field_data'] ?? [];

                    $externalFields = Helper::getExternalFieldSetting($this->clientId, \Constants::TYPE_CUSTOMER, $this->getFieldUserId());

                    $externalFieldData = [];
                    foreach ($externalFields as &$eField) {
                        if (array_key_exists($eField['id'], $efd)) {
                            $eField['value'] = $efd[$eField['id']];
                        }
                        $externalFieldData[] = $eField;
                    }

                    $result['external_field_data'] = $externalFieldData;
                    break;

                case 'post_grade_name':
                    $result['post_grade_name'] = \Yii::t('field', $postGradeArr[$data['post_grade']] ?? '');
                    break;

                case 'gender_name':
                    $result['gender_name'] = \Yii::t('field', $genderArr[$data['gender']] ?? $genderArr[0]);
                    break;

                case 'company_name':
                    $result['company_name'] = $this->hasMapData('company_name', $data['company_id']) ?
                        $this->getMapData('company_name',
                            $data['company_id']) : ($data['company_id'] ? (new Company($this->clientId,
                            $data['company_id']))->name : '');
                    break;

                default:
                    $result[$field] = $data[$field];
            }
        }

        if ($this->hideInfo) {
            $hideValue = '***';
            foreach ($result as $key => &$item) {
                if (in_array($key, ['company_id', 'customer_id', 'main_customer_flag', 'growth_level', 'suspected_invalid_email_flag', 'forbidden_flag'])) {
                    continue;
                }
                switch ($key) {
                    case 'external_field_data':
                        foreach ($item as &$externalItem) {
                            $externalItem['value'] = $hideValue;
                        }
                        break;
                    case 'tel_list':
                        foreach ($item as &$telItem) {
                            $telItem = [$hideValue, $hideValue];
                        }
                        break;
                    case 'contact':
                        foreach ($item as &$contactItem) {
                            $contactItem['value'] = $hideValue;
                        }
                        break;
                    default:
                        if (is_array($item)) {

                        } else {
                            $item = $hideValue;
                        }
                }
            }
        }


        return $result;
    }

    protected function constructListResult(array &$result, $item)
    {
        if ($this->excludeBoundAliBuyer && $this->getMapData('bound_buyer_customers', $item['customer_id'])) {
            return;
        }

        if (isset($item['main_customer_flag']) && $item['main_customer_flag']) {
            array_unshift($result, $item);
        } else {
            $result[] = $item;
        }
    }

    protected function format($data)
    {
        $result = [];
        $result = array_merge($result, $this->produceBaseInfo($data));
        if ($this->visitFrom!='app' && isset($result['external_field_data']) and is_array($result['external_field_data'])){
            foreach ($result['external_field_data'] as &$field){
                if ($field['field_type'] == CustomFieldService::FIELD_TYPE_IMAGE
                    || $field['field_type'] == CustomFieldService::FIELD_TYPE_ATTACH) {
                    if (!empty($field['value']) && is_array($field['value'])) {
                        $fileIds = array_column($field['value'], 'file_id');
                        foreach ($fileIds as $fileId) {
                            $fileInfo[$fileId] = $this->getMapData('fileInfo', $fileId);
                        }
                    }
                    $field['value'] =array_values($fileInfo ?? []);
                    unset($fileInfo);
                }
            }
        }
        if ($this->showInfoGroup) {
            $this->getFieldFormatter()->setShowInfoData(false);
            $result['fields'] = $this->getFieldFormatter()->format($data);
        }

        if ($this->showCloudStat) {
            $result['cloud_stat'] = $this->getMapData('cloud_stat', $data['email']) ?? ['active' => 0, 'popular' => 0];
        }

        if ($this->showLastMail) {
            $list = new CompanyDynamicList($this->clientId);
            $list->setOperatorUserId($this->trailUserId);
            $list->setModuleId(\common\library\trail\TrailConstants::MODULE_MAIL);
            $list->setCompanyId($data['company_id']);
            $list->setCustomerId($data['customer_id']);
            $list->setOrder('desc');
            $list->setOrderBy('create_time');
            $list->setLimit(1);
            $list->setOffset(0);
            $list->setCreateUser($this->trailUserId);
            $lastTrail = $list->find();
            $lastTrail = reset($lastTrail);

            $result['last_trail'] = $lastTrail ? $lastTrail : [];
        }

        if ($this->showCompanyInfo) {
            $result['company_info'] = $this->getMapData('company_info', $data['company_id']);
            if ($this->flattenCompanyFields) {
                foreach ($this->flattenCompanyFields as $key => $field) {
                    if (is_numeric($key)) {
                        $result[$field] = $result['company_info'][$field] ?? '';
                    } else {
                        $result[$field] = $result['company_info'][$key];
                    }
                }
            }
        }
    
        if ($this->filterDisableFields && !empty($this->mapData['disableFields'])) {
        
            $filterCustomerFields = array_intersect($this->mapData['disableFields'], FieldList::SPECIFIC_DISABLE_FIELD_MAP[\Constants::TYPE_CUSTOMER] ?? []);
        
            $result = array_diff_key($result, array_flip($filterCustomerFields));
        }
        
        return $result;
    }

    public function ApiBaseInfoSetting()
    {
        $this->setSpecifyFields([
            'customer_id',
            'company_id',
            'name',
            'email',
            'gender',
            'remark',
            'birth',
            'tel_list',
            'post_grade',
            'post',
            'image_list',
            'contact',
            'main_customer_flag',
            'external_field_data'
        ]);
    }

    public function edmAddressInfoSetting()
    {
        $this->setSpecifyFields(['customer_id', 'name', 'email', 'company_id', 'company_name']);
    }

    public function snsInfoSetting()
    {
        $this->setSpecifyFields(['customer_id', 'name', 'order_time', 'create_time', 'email', 'company_id', 'company_name', 'tel_list', 'full_tel_list', 'contact']);
    }

    public function snsListSetting()
    {
        $this->setSpecifyFields(['customer_id', 'name', 'order_time', 'create_time', 'email', 'company_id', 'company_name', 'tel_list', 'full_tel_list', 'contact', 'user_id']);
        $this->setShowCompanyInfo(true);
        $this->flattenCompanyFields(['short_name']);
    }

    public function wecomListSetting() {
        $this->setSpecifyFields(['customer_id', 'name', 'order_time', 'create_time', 'email', 'company_id', 'company_name', 'tel_list', 'full_tel_list', 'contact', 'main_customer_flag', 'user_id']);
        $this->setShowCompanyInfo(true);
        $this->flattenCompanyFields(['name' => 'company_name', 'tel_full', 'company_id', 'homepage', 'fax', 'address', 'owner', 'short_name']);
    }

    public function tmRecommendSetting()
    {
        $this->setSpecifyFields([
            'customer_id', 'name', 'email', 'tel_list', 'full_tel_list', 'contact', 'create_time', 'user_id',
        ]);
        $this->setShowCompanyInfo(true);
        $this->flattenCompanyFields(['name' => 'company_name', 'tel_full', 'company_id', 'homepage', 'fax', 'address', 'owner', 'short_name']);
        $this->excludeBoundAliBuyer(true);
    }

    public function getFieldReferType()
    {
        return \Constants::TYPE_CUSTOMER;
    }

}
