<?php
/**
 * Created by PhpStor<PERSON>.
 * User: andy
 * Date: 2017/4/11
 * Time: 14:17
 */

namespace common\library\customer;


use CDbException;
use CException;
use common\components\BaseObject;
use common\library\account\Client;
use common\library\account\UserInfo;
use common\library\account\UserList;
use common\library\ai\service\EventsReport;
use common\library\alibaba\customer\CustomerSyncHelper;
use common\library\behavior\BehaviorConstant;
use common\library\behavior\BehaviorService;
use common\library\common_file\CommonFileService;
use common\library\custom_field\company_field\duplicate\FieldUniqueValidator;
use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldList;
use common\library\customer\field_unique\DuplicateFlagBuilder;
use common\library\customer_v3\company\list\CompanyList;
use common\library\customer_v3\company\orm\Company;
use common\library\customer_v3\company\orm\CompanyMetadata;
use common\library\customer_v3\customer\CustomerList;
use common\library\customer_v3\customer\orm\Customer;
use common\library\duplicate\conflict_record\ConflictConstants;
use common\library\duplicate\conflict_record\ConflictService;
use common\library\duplicate\DuplicateConstants;
use common\library\email_identity\sync\CustomerSync;
use common\library\history\customer\BatchCompanyBuilder;
use common\library\history\customer\CompanyEditCompare;
use common\library\notification\Notification;
use common\library\notification\NotificationHelper;
use common\library\notification\PushHelper;
use common\library\object\field\service\ScopeUserFieldService;
use common\library\okki_chat\ChatService;
use common\library\opportunity\OpportunityBatchOperator;
use common\library\performance_v2\PerformanceV2Constant;
use common\library\performance_v2\record\PerformanceBatchOperatorTrait;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeFieldV2;
use common\library\privilege_v3\PrivilegeService;
use common\library\queue_v2\job\BatchDelete507TodoFeedJob;
use common\library\queue_v2\job\BatchDeleteTodoFeedJob;
use common\library\queue_v2\job\BatchPushTodoFeedJob;
use common\library\queue_v2\job\TipsPushTodoJob;
use common\library\queue_v2\QueueService;
use common\library\recycle;
use common\library\report\mail_unread\Report;
use common\library\server\es_search\SearchQueueService;
use common\library\setting\library\common\PublicTypeMetadata;
use common\library\setting\library\group\GroupApi;
use common\library\setting\library\origin\OriginApi;
use common\library\setting\library\tag\TagApi;
use common\library\statistics\CompanyHelper;
use common\library\swarm\handler\CompanyHandler;
use common\library\swarm\SwarmService;
use common\library\todo\TodoConstant;
use common\library\trail\TrailConstants;
use common\library\util\PgsqlUtil;
use common\library\util\Speed;
use common\library\version\CompanyVersion;
use common\library\version\Constant;
use common\library\workflow\trigger\WorkflowBatchOperatorTrait;
use common\models\client\CompanyHistoryPg;
use LogUtil;
use ProcessException;
use RuntimeException;

/**
 * Class CompanyBatchOperator
 * @package common\library\customer
 *
 * @property CompanyList $listObject
 */
class CompanyBatchOperator extends \BatchOperator
{

    use WorkflowBatchOperatorTrait,PerformanceBatchOperatorTrait;

    const MAX_BATCH_OPERATOR_COUNT = 20000;
	const MAX_POOL_RULE_TYPE_BATCH_OPERATOR_COUNT = 100;
    const MAX_UPDATE_FIELD_BATCH_OPERATOR_COUNT = 1000;
    const MAX_BATCH_USER_COUNT = 5;

	const POOL_SETTING_OPERATE = 'setting';//公海管理变更公海分组操作

    protected $companyIds = [];
    protected $privateReceiveRules = [];
    protected $failCount = 0;
    protected $noPermissionCompanyIds = [];

    protected function init()
    {
        $this->listObject = new CompanyList($this->userId);

        $this->db = \PgActiveRecord::getDbByUserId($this->userId);
    }

    /**
     * @return CompanyList
     */
    public function getList()
    {
        return $this->listObject;
    }

    public function getCompanyIds()
    {
        return $this->companyIds;
    }

    public function getNoPermissionCompanyIds()
    {
        return $this->noPermissionCompanyIds;
    }

	public function getFailCount(){
		return $this->failCount;
	}

    /**
     * @param $count
     */
    protected function checkMaxOperateCount($count)
    {
        if ($count > self::MAX_BATCH_OPERATOR_COUNT) {
            throw new \RuntimeException(\Yii::t('customer', 'Selected leads') . $count . \Yii::t('customer', 'Do not support batch operations'));
        }
    }

    /**
     * 如果是boss操作，调用这个类，就不检查公司的owner
     * 目前这一步不严谨，是能操作所有客户的，如果真的有非常强的安全需求，就把这个类的实现改为下属客户
     */
    public function boss()
    {
        $this->listObject->setSkipPrivilege(true);
    }

    protected function paramsMapping()
    {
    
        !empty($this->params['user_num']) && $this->listObject->setUserNum($this->params['user_num']);
        
        $this->listObject->paramsMapping($this->params);
    }

    /**
     * 删除跟进人
     * @param null $removedUserId 不传删自己
     * @param string $privilege
     * @return int count
     * @throws CDbException
     * @throws CException
     * @throws \Throwable
     */
    public function removeUser($removedUserId = null)
    {
        $opUser = \User::getUserObject($this->userId);

        $privilege = PrivilegeConstants::PRIVILEGE_CRM_COMPANY_RELEASE_SPECIFY_USER;
        if (!$removedUserId)
        {
            $removedUserId = $this->userId;
            $privilege = PrivilegeConstants::PRIVILEGE_CRM_COMPANY_RELEASE;
        }

        $this->paramsMapping();

        // 根据权限过滤
        if (!$this->listObject->getSkipPrivilege()) {
            [$companyIds, $this->noPermissionCompanyIds] = $this->listObject->fetchEditableObjIds(['private_company_privilege' => $privilege]);
            if (!is_null($companyIds)) {
                if (!$companyIds) {
                    return 0;
                }
                $this->listObject->setCompanyIds($companyIds);
            }
        }

        [$where,$params] = $this->listObject->buildParams();

        $alias = empty($this->listObject->getAlias()) ? '' :$this->listObject->getAlias() . '.';

        $from = $this->listObject->getFrom();

        $sql = "select {$alias}company_id, {$alias}user_id,{$alias}name,{$alias}group_id,{$alias}main_customer_email,{$alias}homepage,{$alias}country,".
            "{$alias}scale_id,{$alias}company_hash_id,{$alias}archive_type,{$alias}origin $from $where and {$alias}user_id @> ARRAY[$removedUserId]::bigint[]";

        $list = $this->db->createCommand($sql)->queryAll(true, $params);

        if (empty($list))
            return 0;

        $companyIds = array_column($list, 'company_id');
        $companyIdsSql = implode(',', $companyIds);
        $publicList = [];
        $privateList = [];

        $count = count($companyIds);
        if( $count > self::MAX_BATCH_OPERATOR_COUNT ){
            throw  new \RuntimeException(\Yii::t('customer', 'Selected customer') . $count . \Yii::t('customer', 'Do not support batch operations'));
        }

        foreach ($list as $elem)
        {
            $companyId = $elem['company_id'];
            $userIds = PgsqlUtil::trimArray($elem['user_id'])??[];
            $count = count($userIds);
            
            if ($count == 1)
                $publicList[] = $companyId;
            else if ($count > 1)
                $privateList[] = $companyId;
        }

        $removedUserIdCast = "$removedUserId::bigint";

        $nowTime = date('Y-m-d H:i:s');

        $companySet = ",edit_time='{$nowTime}',update_time='{$nowTime}'";
        if ($opUser->getUserId()) {
            $companySet .= ",last_edit_user={$opUser->getUserId()}";
        }

        $sql = "update tbl_company set user_id=array_remove(user_id, $removedUserIdCast) $companySet where  client_id=:client_id and company_id in ($companyIdsSql) and user_id @> ARRAY[$removedUserId]::bigint[]";
        $count = $this->db->createCommand($sql)->execute([':client_id'=>$opUser->getClientId()]);

        // 更新scope_user_ids
//        if ($companyIds) {
//            $scopeUserService = new ScopeUserFieldService($opUser->getClientId(), new CompanyMetadata($opUser->getClientId()));
//            $scopeUserService->refreshScopeUserIdsByPids($companyIds);
//        }

        $sql = "update tbl_customer set user_id=array_remove(user_id, $removedUserIdCast) where client_id=:client_id and company_id in ($companyIdsSql) and is_archive=1 and user_id @> ARRAY[$removedUserId]::bigint[]";
        $this->db->createCommand($sql)->execute([':client_id'=>$opUser->getClientId()]);

        (new CustomerSync($opUser->getClientId()))->setFindCompanyId($companyIds)->sync($removedUserId);

        // 操作历史
        if (!empty($publicList))
        {
            $idsSql = implode(',', $publicList);
            $publicType = PublicTypeMetadata::PUBLIC_TYPE_NORMAL;
            $sql = "update tbl_company set last_owner=$removedUserId,release_count=release_count+1,next_move_to_public_date='1970-01-01',public_type={$publicType},public_time= '{$nowTime}' where company_id in ($idsSql)";
            $this->db->createCommand($sql)->execute();

            $history = new BatchCompanyBuilder();
            $history->build($opUser->getClientId(), $opUser->getUserId(), CompanyHistoryPg::TYPE_MOVE_TO_PUBLIC,
                $publicList, [['id'=>'user_id', 'base' => 1, 'old' => [$removedUserId], 'new' => []]]);


	        $userIds = [$this->userId];
            
            //记录移入公海的时间
            \common\library\customer\public_record\Helper::batchSavePublicRecord($opUser->getClientId(),$removedUserId,$publicList);
	        
            //移入公海统计记录
	        CompanyHelper::IncCompanyKeyCount($opUser->getClientId(), $userIds, 'move_to_public_company_count', $publicList);

	        CompanyHelper::IncCompanyKeyCount($opUser->getClientId(), $userIds, 'owner_move_to_public_company_count', $publicList);

            CompanyHandler::cleanupForCompanyIds($opUser->getClientId(), $publicList);
            
            (new SwarmService($opUser->getClientId()))->setSkipUpdateSearch()->refreshByRefer($publicList);
        }

        SearchQueueService::pushCompanyQueue($this->userId,$opUser->getClientId(),$companyIds,\Constants::SEARCH_INDEX_TYPE_UPDATE);

        if (!empty($privateList))
        {
            $history = new BatchCompanyBuilder();
            $history->build($opUser->getClientId(), $opUser->getUserId(), CompanyHistoryPg::TYPE_CANCEL_FOLLOW,
                $privateList, [['id'=>'user_id', 'base' => 1, 'old' => [$removedUserId], 'new' => []]]);
        }

        // 插入feed
        if (count($list) == 1)
        {
            $node_type = TrailConstants::TYPE_CUSTOMER_CANCEL_FOLLOW;

            $data = [
                'company_name' => Helper::getCompanyName($companyIds[0], $this->userId),
                'company_ids' => $companyIds,
            ];
        }
        else
        {
            $node_type = TrailConstants::TYPE_CUSTOMER_BATCH_CANCEL_FOLLOW;

            $data = [
                'company_count' => $count,
                'company_ids' => $companyIds,
            ];
        }

        $feed = new \Feed();
        $feed->setClientId($opUser->getClientId());
        $feed->setUserId($opUser->getUserId());
        $feed->setCreateUser($opUser->getUserId());
        $feed->setNodeType($node_type);
        $feed->setData($data);
        $feed->setReferId(0);
        $feed->save();



        //向影响到的跟进人发送消息
        if( $removedUserId != $this->userId ) {
            $notification = new Notification($opUser->getClientId(), \common\library\notification\Constant::NOTIFICATION_TYPE_CUSTOMER_CANCEL);
            $notification->create_user_id = $this->userId;
            $notification->user_id = $removedUserId;
            $notification->setSourceData([
                'company_obj_list' => $list,
            ]);
            PushHelper::pushNotification($opUser->getClientId(), $removedUserId, $notification);
        }


        $companyVersion = new CompanyVersion($opUser->getClientId(), $companyIds);
        $companyVersion->setType(Constant::COMPANY_MODULE_EDIT);
        $companyVersion->add();

        $updateCompanyIds = array_column($list, 'company_id');
        $this->runWorkflow($opUser->getClientId(), $updateCompanyIds, ['user_id', 'last_owner', 'release_count', 'next_move_to_public_date', 'public_type', 'public_time', 'edit_time']);
        $this->runPerformance($opUser->getClientId(), $opUser->getUserId(), \Constants::TYPE_COMPANY, $updateCompanyIds);

        //ai推荐事件上报
        Helper::aiEventReport($list, EventsReport::EVENT_DELETE, $opUser->getUserId(), $opUser->getClientId(),true);

	    //如果是自己，则是取消跟进,公海领取返还额度
		//(私海）取消跟进，不更新公海领取客户上限额度
	    /*if ($removedUserId==$this->userId) {
		    \common\library\customer\public_record\Helper::returnPublicPoolReceiveLimits($opUser->getUserId(), $opUser->getClientId(), $companyIds);
	    }*/

        $this->reportCompanyMailChange($removedUserId);

        // 删除相关[跟进客户]任务
        \common\library\task\Helper::deleteTask($opUser->getClientId(), $companyIds, [$removedUserId]);

        // 关闭共享客户邮件
        \DistributeCustomerMailRule::disableCompanyId($opUser->getClientId(), $opUser->getUserId(), $companyIds, [$removedUserId]);

        //消除501待办
        $batchDeleteJob = new BatchDeleteTodoFeedJob($opUser->getClientId(), TodoConstant::OBJECT_TYPE_COMPANY, TodoConstant::TODO_TYPE_COMPANY_TRANSFER_TO_ME, [$removedUserId => $list]);
        QueueService::dispatch($batchDeleteJob);

        //消除503待办
        $batchDeleteJob = new BatchDeleteTodoFeedJob($opUser->getClientId(), TodoConstant::OBJECT_TYPE_COMPANY, TodoConstant::TODO_TYPE_COMPANY_DATA_UPDATE, [$removedUserId => $list]);
        QueueService::dispatch($batchDeleteJob);

        //消除504待办
        $batchDeleteJob = new BatchDeleteTodoFeedJob($opUser->getClientId(), TodoConstant::OBJECT_TYPE_COMPANY, TodoConstant::TODO_TYPE_COMPANY_TRAIL_UPDATE, [$removedUserId => $list]);
        QueueService::dispatch($batchDeleteJob);

        //消除508待办
        $batchDeleteJob = new BatchDeleteTodoFeedJob($opUser->getClientId(), TodoConstant::OBJECT_TYPE_COMPANY, TodoConstant::TODO_TYPE_COMPANY_CUSTOMS_UPDATE, [$removedUserId => $list]);
        QueueService::dispatch($batchDeleteJob);

        // 更新聊天服务归属人
        $newOwnerInfo = [];
        foreach ($list as $company) {
            $owner = PgsqlUtil::trimArray($company['user_id']) ?? [];
            $owner = array_diff($owner, [$removedUserId]);
            $newOwnerInfo[$company['company_id']] = $owner;
        }
        ChatService::getInstance($opUser->getClientId(), $opUser->getUserId())->transferContactsAsync($newOwnerInfo);

        return $count;
    }

    public function moveToPublic($publicType = PublicTypeMetadata::PUBLIC_TYPE_NORMAL, $publicReasonId = 0, $poolId = 0, $changePoolFlag = 0, $isWorkflowRollback = false) {

        $opUser = \User::getUserObject($this->userId);

        if (!is_numeric($publicReasonId) || ($changePoolFlag && !is_numeric($poolId))) {

            throw new \RuntimeException('request params wrong');
        }

        $this->paramsMapping();

        // 根据权限过滤
        if (!$this->listObject->getSkipPrivilege()) {
            [$companyIds, $this->noPermissionCompanyIds] = $this->listObject->fetchEditableObjIds(['private_company_privilege' => PrivilegeConstants::PRIVILEGE_CRM_COMPANY_MOVE_POOL]);
            if (!is_null($companyIds)) {
                if (!$companyIds) {
                    return 0;
                }
                $this->listObject->setCompanyIds($companyIds);
            }
        }

        [$where,$params] = $this->listObject->buildParams();
        \LogUtil::info("[moveToPublic] where: {$where} params: ".json_encode($params)." publicReasonId:{$publicReasonId} changePoolflag: {$changePoolFlag}");

        $alias = empty($this->listObject->getAlias()) ? '' :$this->listObject->getAlias() . '.';

        $from = $this->listObject->getFrom();

        $sql = "select {$alias}company_id, {$alias}user_id,{$alias}name,{$alias}group_id,{$alias}main_customer_email,{$alias}homepage,{$alias}country,{$alias}scale_id,{$alias}company_hash_id,{$alias}archive_type,".
            "{$alias}origin, public_reason_id, pool_id $from $where and {$alias}user_id!='{}'";

        $list = $this->db->createCommand($sql)->queryAll(true, $params);

        if (empty($list))
            return 0;

		if (!\common\library\privilege_v3\Helper::hasPermission($opUser->getClientId(), $this->userId, PrivilegeConstants::PRIVILEGE_CRM_COMMON_POOL_COMPANY_POOL)) {
			$sum = count($list);
			$list = array_values(array_filter($list, function ($item) {
				return $item['pool_id'] != 0;
			}));
			$this->failCount = $sum - count($list);
		}

        $companyIds = array_column($list, 'company_id');
		if(empty($companyIds)){
			return 0;
		}
        $companyIdsSql = implode(',', $companyIds);

        \LogUtil::info("[moveToPublic] companyIdsSql: {$companyIdsSql}");
        $map = [];
        $historyMap = [];

        $count = count($companyIds);
        if( $count > self::MAX_BATCH_OPERATOR_COUNT ){
            throw  new \RuntimeException(\Yii::t('customer', 'Selected customer') . $count . \Yii::t('customer', 'Do not support batch operations'));
        }

        // 删除507代办需要的customerList 作为objectIds传入
        $customerList = new CustomerList($opUser->getClientId());
        $customerList->setCompanyId($companyIds);
        $customerList->setFields(['customer_id','user_id','company_id']);
        $customerListData = $customerList->find();

        foreach ($list as $elem)
        {
            $companyId = $elem['company_id'];
            $userIds = PgsqlUtil::trimArray($elem['user_id'])??[];
            $map[$companyId] = $userIds;
            $historyMap[$companyId] = [
                [
                    'id'   => 'user_id',
                    'base' => 1,
                    'new'  => [],
                    'old'  => $userIds,
                ],
            ];

            ($publicReasonId != $elem['public_reason_id']) && $historyMap[$companyId][] = [
                'id'   => 'public_reason_id',
                'base' => 1,
                'new'  => $publicReasonId,
                'old'  => $elem['public_reason_id'],
            ];

            \common\library\customer\public_record\Helper::batchSavePublicRecord($opUser->getClientId(),$userIds,$companyId);
        }
        $set = '';
        if ($opUser->getUserId()) {
            $set .= ",last_edit_user={$opUser->getUserId()}";
        }

        // 工作流撤回的时候 不变更原跟进人
        if (!$isWorkflowRollback) {
            $set .= ",last_owner=user_id[1]";
        }

        $nowTime = date('Y-m-d H:i:s');
        $set .= ",update_time='{$nowTime}'";
        $set .= ",public_type={$publicType}";
        $set .= ",public_reason_id={$publicReasonId} ";
        $sql = "update tbl_company set user_id='{}',public_time ='{$nowTime}',release_count=release_count+1,edit_time='{$nowTime}',next_move_to_public_date='1970-01-01' $set  where client_id=:client_id and company_id in ($companyIdsSql)  and user_id != '{}'";

        $count = $this->db->createCommand($sql)->execute([':client_id'=>$opUser->getClientId()]);

//        // 更新scope_user_ids
//        $scopeUserService = new ScopeUserFieldService($opUser->getClientId(), new CompanyMetadata($opUser->getClientId()));
//        $scopeUserService->refreshScopeUserIdsByPids($companyIds);

        \LogUtil::info("[moveToPublic] count: {$count}");

        $sql = "update tbl_customer set user_id='{}' where client_id=:client_id and company_id in ($companyIdsSql) and is_archive=1";
        $this->db->createCommand($sql)->execute([':client_id'=>$opUser->getClientId()]);
    
        
        if ($changePoolFlag) {

//            覆盖下真正移入公海的
            $this->listObject->setCompanyIds($companyIds);
        
            $this->setPoolId($poolId);
        }
        
        //清理客群
        CompanyHandler::cleanupForCompanyIds($opUser->getClientId(), $companyIds);
       
        (new SwarmService($opUser->getClientId()))->setSkipUpdateSearch()->refreshByRefer($this->companyIds, [], true);

        //获取邮箱用于更新tbl_email_identity表
        (new CustomerSync($opUser->getClientId()))->setFindCompanyId($companyIds)->sync();

        \common\library\server\es_search\SearchQueueService::pushCompanyQueue($this->userId,$opUser->getClientId(),$companyIds,\Constants::SEARCH_INDEX_TYPE_UPDATE);

        $historyType = CompanyHistoryPg::TYPE_MOVE_TO_PUBLIC;
        if ($publicType == PublicTypeMetadata::PUBLIC_TYPE_ALIBABA) {
            $this->editReferType = BaseObject::FIELD_EDIT_TYPE_BY_ALIBABA_CUSTOMER_SYNC;
            $historyType = CompanyHistoryPg::TYPE_ALI_MOVE_TO_PUBLIC;
        }
        // 操作历史
        $this->buildBatchHistory($opUser->getClientId(), $opUser->getUserId(), $historyType, $historyMap);

        // 关闭共享客户邮件
        \DistributeCustomerMailRule::disableCompanyId($opUser->getClientId(), $opUser->getUserId(), $companyIds);

        // 插入feed
        $nodeType = TrailConstants::TYPE_CUSTOMER_BATCH_TO_PUBLIC;

        $data = [
            'company_count' => $count,
            'company_ids' => $companyIds,
        ];

        $companyVersion = new CompanyVersion($opUser->getClientId(), $companyIds);
        $companyVersion->setType(Constant::COMPANY_MODULE_EDIT);
        $companyVersion->add();
        $updateCompanyIds = array_column($list, 'company_id');

        $this->runWorkflow($opUser->getClientId(),$updateCompanyIds , ['user_id', 'public_time', 'edit_time', 'last_owner', 'release_count', 'next_move_to_public_date', 'public_type', 'public_reason_id', 'release_count']);
        $lastOwnerMap = [];
        foreach ($historyMap as $companyId => $items)
        {
            $lastOwnerIds = array_column(array_filter($items, function($item) {
                return $item['id'] == 'user_id';
            }),'old');
            // 处理一下 变成一维数组
            $lastOwnerIds = array_merge(...$lastOwnerIds);
            $lastOwnerMap[$companyId]['last_owner'] = $lastOwnerIds;
        }

        $this->runPerformance($opUser->getClientId(), $opUser->getUserId(), \Constants::TYPE_COMPANY, $updateCompanyIds,false,['lastOwnerMap' => $lastOwnerMap],[],PerformanceV2Constant::PERFORMANCE_RECORD_JOB_SCENE_TRANSFER);

        //ai推荐上报
        $ret = Helper::aiEventReport($list, EventsReport::EVENT_DELETE, $opUser->getUserId(), $opUser->getClientId(),true);
        if ($ret) {
            Helper::aiEventReport($list, EventsReport::EVENT_CREATE, 0, $opUser->getClientId());
        }

	    //存在规则需要返还额度
		//（私海）移入公海，不更新公海领取客户上限额度
	    //\common\library\customer\public_record\Helper::returnPublicPoolReceiveLimits($opUser->getUserId(),$opUser->getClientId() , $companyIds);

        $this->reportCompanyMailChange($this->userId);

        // 删除相关[跟进客户]任务
        \common\library\task\Helper::deleteTask($opUser->getClientId(), $companyIds);

        //向影响到的跟进人发送消息 增加移入公海记录
        $sendMsgData = [];
        foreach ($list as $item) {
            $userIds = PgsqlUtil::trimArray($item['user_id'])??[];
            foreach ($userIds as $uid) {
                $sendMsgData[$uid][] = ['company_id' => $item['company_id'], 'name' => $item['name'], 'group_id' => $item['group_id']];
            }
        }

        //消除501待办
        $batchDeleteJob = new BatchDeleteTodoFeedJob($opUser->getClientId(), TodoConstant::OBJECT_TYPE_COMPANY, TodoConstant::TODO_TYPE_COMPANY_TRANSFER_TO_ME, $sendMsgData);
        QueueService::dispatch($batchDeleteJob);

        //消除503待办
        $batchDeleteJob = new BatchDeleteTodoFeedJob($opUser->getClientId(), TodoConstant::OBJECT_TYPE_COMPANY, TodoConstant::TODO_TYPE_COMPANY_DATA_UPDATE, $sendMsgData);
        QueueService::dispatch($batchDeleteJob);

        //消除504待办
        $batchDeleteJob = new BatchDeleteTodoFeedJob($opUser->getClientId(), TodoConstant::OBJECT_TYPE_COMPANY, TodoConstant::TODO_TYPE_COMPANY_TRAIL_UPDATE, $sendMsgData);
        QueueService::dispatch($batchDeleteJob);

        $batchDeleteJob = new BatchDeleteTodoFeedJob($opUser->getClientId(), TodoConstant::OBJECT_TYPE_COMPANY, TodoConstant::TODO_TYPE_COMPANY_CUSTOMS_UPDATE, $sendMsgData);
        QueueService::dispatch($batchDeleteJob);

        // 消除507代办
        $batchDeleteJob = new BatchDeleteTodoFeedJob($opUser->getClientId(),TodoConstant::OBJECT_TYPE_COMPANY, TodoConstant::TODO_TYPE_COMPANY_EMAIL_INVALID, $sendMsgData);
        QueueService::dispatch($batchDeleteJob);

        // 更新聊天服务归属人
        ChatService::getInstance($opUser->getClientId(), $opUser->getUserId())->transferContactsAsync(array_fill_keys($companyIds, []));

        if (!$isWorkflowRollback)
        {
            $feed = new \Feed();
            $feed->setClientId($opUser->getClientId());
            $feed->setUserId($opUser->getUserId());
            $feed->setCreateUser($opUser->getUserId());
            $feed->setNodeType($nodeType);
            $feed->setData($data);
            $feed->setReferId(0);
            $feed->save();

            foreach ($list as $item) {
                //移入公海统计记录
                CompanyHelper::IncCompanyKeyCount($opUser->getClientId(), $userIds, 'move_to_public_company_count', [$item['company_id']]);
                CompanyHelper::IncCompanyKeyCount($opUser->getClientId(), $userIds, 'owner_move_to_public_company_count', [$item['company_id']]);
            }

            foreach ($customerListData as $item)
            {
                $userIds = PgsqlUtil::trimArray($item['user_id']) ?? [];
                $userIds = array_unique($userIds);
                foreach ($userIds as $uid)
                {
                    if (!array_key_exists($uid, $sendMsgData)) continue;
                    foreach ($sendMsgData[$uid] as $index => $value)
                    {
                        if ($value['company_id'] == $item['company_id'])
                        {
                            $sendMsgData[$uid][$index]['customer_id'][] = $item['customer_id'];
                        }
                    }
                }
            }


            foreach ($sendMsgData as $uid => $msgData) {
                if( $uid == $this->userId)
                    continue;
                $notification = new Notification($opUser->getClientId(), \common\library\notification\Constant::NOTIFICATION_TYPE_CUSTOMER_CANCEL);
                $notification->create_user_id = $this->userId;
                $notification->user_id = $uid;
                $notification->setSourceData([
                    'company_obj_list' => $msgData,
                ]);
                PushHelper::pushNotification($opUser->getClientId(), $uid, $notification);
            }
        }


        return $count;
    }

    /**
     * @param $blacklistType
     * @param $alibabaSyncFlag
     * @param $deleteAll
     * @return int|void
     * @throws \Exception
     */
    public function delete($blacklistType = '', $alibabaSyncFlag=1, $deleteAll = false)
    {
        $opUser = \User::getUserObject($this->userId);
        $opClientId = $opUser->getClientId();

        $this->paramsMapping();

        // 根据权限过滤
        if (!$this->listObject->getSkipPrivilege()) {
            [$companyIds, $this->noPermissionCompanyIds] = $this->listObject->fetchEditableObjIds([
                'private_company_privilege' => PrivilegeConstants::PRIVILEGE_CRM_COMPANY_REMOVE,
                'public_company_privilege' => PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_REMOVE,
            ]);
            if (!is_null($companyIds)) {
                if (!$companyIds) {
                    return 0;
                }
                $this->listObject->setCompanyIds($companyIds);
            }
        }

        [$where,$params] = $this->listObject->buildParams();

        $alias = empty($this->listObject->getAlias()) ? '' :$this->listObject->getAlias() . '.';

        $from = $this->listObject->getFrom();

        $sql = "select {$alias}company_id, {$alias}user_id,{$alias}name,{$alias}group_id,{$alias}main_customer_email,{$alias}homepage,{$alias}country,{$alias}scale_id,{$alias}company_hash_id,".
            "{$alias}archive_type,{$alias}origin,{$alias}create_time,{$alias}create_user $from $where ";
        if (!$deleteAll) {
            $sql .= " and {$alias}user_id='{}'";
        }

        $companyIdsData = $this->db->createCommand($sql)->queryAll(true,$params);

        $companyIds = array_column($companyIdsData, 'company_id');
        $companyHashIds = array_unique(array_filter(array_column($companyIdsData, 'company_hash_id')));

        if (empty($companyIds))
            return 0;

        $count = count($companyIds);
        if( $count > self::MAX_BATCH_OPERATOR_COUNT ){
            throw  new \RuntimeException(\Yii::t('customer', 'Selected customer') . $count . \Yii::t('customer', 'Do not support batch operations'));
        }

        $companyIdsSql = implode(',', $companyIds);
        $sql = "select customer_id, company_id, email, client_id from tbl_customer where client_id=:client_id and is_archive=1 and company_id in ($companyIdsSql)";
        $customerList = $this->db->createCommand($sql)->queryAll(true, [':client_id'=>$opUser->getClientId()]);

        $map = [];
        $customerEmailList = [];
        foreach ($customerList as $elem){
            $map[$elem['company_id']][] = $elem['customer_id'];
            if(!empty($elem['email']))
            {
                $customerEmailList[] = $elem['email'];
            }
        }

        if (! empty($blacklistType) && !empty($customerEmailList)) {
            \common\library\customer\blacklist\Helper::batchInsert($opUser->getClientId(), $opUser->getUserId(), $customerEmailList, $blacklistType);
        }

        //批量更新客户邮件关系
        \common\library\email\Helper::bactchUpdateCustomerEmailRelation($opClientId, $customerList, false);

        $sql = "update tbl_company set is_archive=0,user_id='{}' where client_id=:client_id and is_archive=1 and company_id in ($companyIdsSql)";
        if (!$deleteAll) {
            $sql .= " and user_id = '{}'";
        }
        $count = $this->db->createCommand($sql)->execute([':client_id'=>$opUser->getClientId()]);

        $sql = "update tbl_customer set is_archive=0,user_id='{}',company_id=0 where client_id=:client_id and company_id in ($companyIdsSql) and is_archive=1";
        $this->db->createCommand($sql)->execute([':client_id'=>$opUser->getClientId()]);
        
//        // 更新scope_user_ids
//        $scopeUserService = new ScopeUserFieldService($opUser->getClientId(), new CompanyMetadata($opUser->getClientId()));
//        $scopeUserService->refreshScopeUserIdsByPids($companyIds);
        
        //入队列，更新搜索索引
        \common\library\server\es_search\SearchQueueService::pushCompanyQueue($this->userId,$opUser->getClientId(),$companyIds,\Constants::SEARCH_INDEX_TYPE_DELETE);
        //更新邮箱身份
        if( !empty($customerEmailList))
        {
            (new CustomerSync($opUser->getClientId()))->setFindEmail($customerEmailList)->sync();
        }
        // 清除绑定海关数据
        $mysql = \ProjectActiveRecord::getDbByUserId($this->userId);
        $mysql->createCommand("delete from tbl_company_bind_ciq where client_id=:client_id and company_id in ($companyIdsSql)")->execute([':client_id'=>$opUser->getClientId()]);

        if (PrivilegeService::getInstance($opClientId)->hasFunctional(PrivilegeConstants::FUNCTIONAL_COMPANY_POOL)) {
            recycle\API::add($opUser->getClientId(), $this->userId, recycle\Recycle::CUSTOMER, $map, true);
        } else {
            // 没有回收箱，直接删除
            \common\library\trail\Helper::removeAllTrailByCompanyId($opUser->getClientId(), $companyIds);
        }

        // 客户文档操作逻辑 删除客户 客户文档进入回收箱
        try {
            $service = new CommonFileService($opUser->getClientId(), $this->userId, \common\library\object\object_define\Constant::OBJ_COMPANY);
            $service->deleteFileByObjectIds($companyIds);
        } catch (\Exception $e) {
            \LogUtil::info(__CLASS__ . ' 回收文档失败 company_ids ' . json_encode($companyIds) . ' error:' . $e->getMessage());
        }

        // 操作历史
        $history = new BatchCompanyBuilder();
        $history->build($opUser->getClientId(), $opUser->getUserId(), CompanyHistoryPg::TYPE_DELETE, $companyIdsData, []);

        $companyVersion = new CompanyVersion($opUser->getClientId(), $companyIds);
        $companyVersion->setType(Constant::COMPANY_MODULE_DELETE);
        $companyVersion->add();

        $this->runWorkflow($opUser->getClientId(), $companyIds, ['is_archive', 'user_id']);

        //ai推荐事件上报
        Helper::aiEventReport($companyIdsData,EventsReport::EVENT_DELETE,$opUser->getUserId(),$opUser->getClientId(),true);

        // 上报udp
        $this->reportCompanyMailChange($this->buildReportUserId($companyIdsData));

        //解除阿里客户关联关系
        CustomerSyncHelper::batchUnbindCompanyId($opClientId, $companyIds, intval($alibabaSyncFlag));
        CustomerSyncHelper::batchUnbindCustomerId($opClientId, array_column($customerList, 'customer_id'));

        //上报待办
        $feed = new \common\library\todo\Feed(TodoConstant::OBJECT_TYPE_COMPANY);
        $feed->deleteFeedByObjectId($companyIds);

        // 507
        $feed = new \common\library\todo\Feed(TodoConstant::OBJECT_TYPE_COMPANY);
        $feed->deleteFeedByObjectId(array_column($customerList, 'customer_id'));

        // 删除相关[跟进客户]任务
        \common\library\task\Helper::deleteTask($opClientId, $companyIds);
        $this->runPerformance($opUser->getClientId(), $opUser->getUserId(), \Constants::TYPE_COMPANY, $companyIds);

        // 关闭共享客户邮件
        \DistributeCustomerMailRule::disableCompanyId($opUser->getClientId(), $opUser->getUserId(), $companyIds);

        //同步leads智能获客openSearch
        if(!empty($companyHashIds)){
            Helper::syncLeadsSearchCompany($opUser->getClientId(), [$opUser->getUserId()], $companyHashIds, 'del',$companyIds, $customerEmailList);
        }

        // 删除客户 工作量统计删除
        $statisticsMap = [];
        $fieldMap = ['customer_add_count'];
        foreach ($companyIdsData as $info) {
            $createUserId = $info['create_user'];
            $date = date("Y-m-d", strtotime($info['create_time']));
            $statisticsMap[] = [
                'user_id' => $createUserId,
                'data' => [
                    'customer_add_count' => 1,
                ],
                'date' =>$date
            ];
        }
        $createUserIds = array_unique(array_column($statisticsMap,'user_id'));
        \StatisticsService::statisticRecordReduce($opUser->getClientId(),$createUserIds,$companyIds, 'customer_add_count');
        \StatisticsService::batchUserStatistics($opUser->getClientId(),$statisticsMap,$fieldMap,'-');
        \StatisticsService::batchUserStatisticsDay($opUser->getClientId(),$statisticsMap,$fieldMap,'-');

        // 移除客户监测池
        \common\library\ai_agent\company_quality_check\Helper::batchRemoveQualityCheckCompany($companyIds);

        return $count;
    }

    /**
     * @param $userIds
     * @param $groupId
     * @param $quotaNum
     * @param $extraWhere
     * @param $feedType
     * @param $batchFeedType
     * @param $historyType
     * @param string $extraSelect
     * @param array $extraData
     * @param bool $checkMovePrivateSea 检查是否允许移入公海,默认true 开启检查
     * @param bool $checkCompanyPoolReceiveLimits 检查是否进行公海额度校验
     * @param bool $checkCompanyPrivateLimits 检查是否进行私海客户额度校验
     * @return int
     */
    private function addOwner($userIds, $groupId, $quotaNum, $extraWhere, $feedType, $batchFeedType, $historyType, &$extraData = [], $checkMovePrivateSea = true, $checkCompanyPoolReceiveLimits = false, $checkCompanyPrivateLimits = true, $isWorkflowRollback = false)
    {
        if ($groupId !== null && !is_numeric($groupId))
            return 0;

        if (count($userIds) > self::MAX_BATCH_USER_COUNT)
            throw new \RuntimeException("不能一次增加超过".self::MAX_BATCH_USER_COUNT.'个用户');

        $opUser = \User::getUserObject($this->userId);
        $clientId = $opUser->getClientId();

        $this->paramsMapping();

        [$where, $params] = $this->listObject->buildParams();

        $where .= $extraWhere;

        $alias = empty($this->listObject->getAlias()) ? '' :$this->listObject->getAlias() . '.';

        $from = $this->listObject->getFrom();

        $sql = "select {$alias}company_id, {$alias}user_id,{$alias}name,{$alias}group_id,{$alias}main_customer_email,{$alias}homepage,{$alias}country,"
        ."{$alias}scale_id,{$alias}company_hash_id,{$alias}archive_type,{$alias}origin $from $where";

        $companyData = $this->db->createCommand($sql)->queryAll(true, $params);
        $extraData = $companyData;
        $companyIds = array_column($companyData, 'company_id');
        $this->companyIds = $companyIds;

        if (empty($companyIds))
            return 0;

        $count = count($companyIds);
        if ($count > self::MAX_BATCH_OPERATOR_COUNT)
            throw new \RuntimeException(\Yii::t('customer', 'Selected customer') . $count . \Yii::t('customer', 'Do not support batch operations'));

        // process customer limit
        $countMap = Helper::batchGetCompanyCountData($clientId, $userIds);

        if ($checkCompanyPrivateLimits)
        {
            $list = new UserList();
            $list->setClientId($clientId);
            $list->setUserIds($userIds);
            $list->setFields(['customer_limit', 'nickname', 'user_id']);
            $userInfos = $list->find();

            $limitMap = array_column($userInfos, 'customer_limit', 'user_id');

            foreach ($userIds as $userId)
            {
                $total = $countMap[$userId]['total_quota'] + $count * $quotaNum;
                $limit = $limitMap[$userId] ?? 0;
                if ($limit && $total > $limit)
                    throw new \RuntimeException(\Yii::t('customer', 'Customer amount exceeds the upper limit'));
            }
        }

        if ($checkCompanyPoolReceiveLimits) {
            $this->privateReceiveRules = \common\library\customer\public_record\Helper::checkCompanyPoolReceiveLimits($opUser->getClientId(), $userIds, $companyIds);
        }

        //检查是否允许从公海捞取用户
        if ($checkMovePrivateSea)
        {
            if (PrivilegeService::getInstance($clientId)->hasFunctional(PrivilegeConstants::FUNCTIONAL_COMPANY_POOL))
                \common\library\customer\public_record\Helper::checkMovePrivateSea($clientId,$this->userId,$companyIds);
        }

        // process set group id
        $set = '';
        $now = date('Y-m-d H:i:s');
        if ($groupId !== null && $groupId >= 0)
        {
            Helper::acceptUseGroup($clientId, $groupId, $this->userId, true);

            $set .= ',group_id=' . $groupId;
        }

        // process user_data & order_time
        if (\CustomerOptionService::checkReference($clientId, 'refer_follow_company'))
        {
            $buildSet = Helper::buildUserData('user_data', $userIds, ['order_time' => $now], 'refer_follow_company');

            $set .= ",order_time='$now', user_data = $buildSet";
        }

        //检查是否允许从公海捞取用户
        if ($checkMovePrivateSea)
        {
            if (PrivilegeService::getInstance($clientId)->hasFunctional(PrivilegeConstants::FUNCTIONAL_COMPANY_POOL))
                \common\library\customer\public_record\Helper::checkMovePrivateSea($clientId,$this->userId,$companyIds);
        }

        $companySet = ",edit_time='{$now}',update_time='{$now}'";
        if ($opUser->getUserId()) {
            $companySet .= ",last_edit_user={$opUser->getUserId()}";
        }

        // 公海移入私海更新移入私海时间字段
        if (in_array($historyType, [CompanyHistoryPg::TYPE_FOLLOW_FROM_PUBLIC, CompanyHistoryPg::TYPE_ASSIGN])) {
            $companySet .= ",private_time='{$now}'";
        }

        $companyIdsSql = '(' . implode(',', $companyIds) . ')';

        //update tbl_company set user_id=(select ARRAY(select distinct unnest(array_cat(user_id, ARRAY[6,7,8]::bigint[])))) where company_id in (39091665, 33781405, 39091649);
        $userIdsSql = implode(',', $userIds);
        $sql = "update tbl_company set private_user_time='$now', user_id=(select ARRAY(select distinct unnest(array_cat(user_id, ARRAY[$userIdsSql]::bigint[])))) $companySet $set where client_id=$clientId and company_id in $companyIdsSql and is_archive=1";
        $this->db->createCommand($sql)->execute();

        $sql = "update tbl_customer set user_id=(select ARRAY(select distinct unnest(array_cat(user_id, ARRAY[$userIdsSql]::bigint[])))) $set where client_id=$clientId and company_id in $companyIdsSql and is_archive=1";
        $this->db->createCommand($sql)->execute();

        // 更新scope_user_ids，这里行数过多时会死锁，与上面$sql不好合并成一条，靠pg异步兜底
        /*
        $scopeUserService = new ScopeUserFieldService($clientId, new CompanyMetadata($clientId));
        $scopeUserService->refreshScopeUserIdsByPids($companyIds);
        */


        //获取邮箱用于更新tbl_email_identity表
        (new CustomerSync($clientId))->setFindCompanyId($companyIds)->sync();

        \common\library\server\es_search\SearchQueueService::pushCompanyQueue($this->userId, $clientId,$companyIds,\Constants::SEARCH_INDEX_TYPE_UPDATE);

        // feed
        $type = $batchFeedType;

        Speed::log('BatchCompanyBuilder');
        // history
        $history = new BatchCompanyBuilder();
        $historyOpUserId = $opUser->getUserId();
        if ($this->editReferId) {
            $history->setEditInfo($this->editReferType, null, $this->editReferId);
            $historyOpUserId = 0;
        }
        $history->build($clientId, $historyOpUserId, $historyType, $companyIds,
            [['id'=>'user_id', 'base' => 1, 'old' => [], 'new' => $userIds]]);

        if ($groupId !== null && $groupId >= 0) {
            $map = [];
            foreach ($companyData as $elem)
            {
                $map[$elem['company_id']] = [
                    [
                        'id' => 'group_id',
                        'base' => 1,
                        'new' => $groupId,
                        'old' => $elem['group_id']
                    ]
                ];
            }
            $history = new BatchCompanyBuilder();
            $history->buildByMap($clientId, $this->userId, CompanyHistoryPg::TYPE_EDIT_COMPANY, $map);
        }
        Speed::log('CompanyVersion');
        $companyVersion = new CompanyVersion($opUser->getClientId(), $companyIds);
        $companyVersion->setType(Constant::COMPANY_MODULE_EDIT);
        $companyVersion->add();
        Speed::log('END!!!');



        //记录行为
        $behavior = new BehaviorService($this->userId);
        $behavior->setType(\common\library\behavior\Helper::convertCustomerTrailType($feedType));
        $behavior->setReferId($companyIds);

        $this->runWorkflow($opUser->getClientId(), $companyIds, ['user_id', 'order_time', 'private_time', 'edit_time', 'group_id']);
        $this->runPerformance($opUser->getClientId(), $opUser->getUserId(), \Constants::TYPE_COMPANY, $companyIds,false,[],['order_time','edit_time','update_time','private_time']);

        //ai推荐上报
        foreach ($userIds as $userId)
            Helper::aiEventReport($companyData, EventsReport::EVENT_CREATE, $userId, $clientId, true);

        $this->reportCompanyMailChange($userIds);

        // 更新聊天服务归属人
        $newOwnerInfo = [];
        foreach ($companyData as $item) {
            $owner = PgsqlUtil::trimArray($item['user_id']) ?? [];
            $owner = array_merge($owner, $userIds);
            $newOwnerInfo[$item['company_id']] = array_values(array_unique($owner));
        }
        ChatService::getInstance($opUser->getClientId(), $opUser->getUserId())->transferContactsAsync($newOwnerInfo);

        if (!$isWorkflowRollback)
        {
            $feedData = [
                'to_user_id' => $userIds[array_key_first($userIds)],
                'to_user_name' => implode(',', array_column($userInfos, 'nickname')),
                'company_count' => $count,
                'company_ids' => $companyIds
            ];

            Speed::log('getCompanyName');
            if ($count == 1)
            {
                $type = $feedType;
                $feedData['company_name'] = $companyData[array_key_first($companyData)]['name'];
            }

            Speed::log('Feed');
            $feed = new \Feed();
            $feed->setNodeType($type);
            $feed->setClientId($clientId);
            $feed->setUserId($opUser->getUserId());
            $feed->setCreateUser($opUser->getUserId());
            $feed->setData($feedData);
            $feed->setReferId(0);
            $feed->save();
        }


        return $count;
    }

	/**
	 * 从公海移入私海（包括公海的重新分配）
	 * @param $groupId
	 * @throws CException
	 * @throws ProcessException
	 */
    public function hold($groupId, $userId = null, $isWorkflowRollback = false)
    {
        $opUser = \User::getUserObject($this->userId);
        $clientId = $opUser->getClientId();

		//处理没有建档时不可选择【公共公海分组】权限
		if (!\common\library\privilege_v3\Helper::hasPermission($clientId, $this->userId, PrivilegeConstants::PRIVILEGE_CRM_COMMON_COMPANY_POOL) && !empty($this->params['company_ids'] ?? [])) {
			$this->failCount = \common\library\customer\pool\Helper::getCompanyIdsByCommonPool($clientId, $this->params['company_ids']);
			$this->params['company_ids'] = array_values(array_diff($this->params['company_ids'], $this->failCount));
			if (empty($this->params['company_ids'])) {
				return 0;
			}
		}

		$assignFlag = false;
		if(!empty($userId)){
			$assignFlag = true;
		}

        // 自己操作从公海移入私海
        if ($userId === null)
        {
            $userId = $this->userId;

            $feedType = TrailConstants::TYPE_CUSTOMER_IN_FROM_PUBLIC;
            $batchFeedType = TrailConstants::TYPE_CUSTOMER_BATCH_IN_FROM_PUBLIC;
            $historyType = CompanyHistoryPg::TYPE_FOLLOW_FROM_PUBLIC;

            $checkMovePrivateSea = true;
            $checkCompanyPoolReceiveLimits = true;
            $checkCompanyPrivateLimits = true;
        } elseif ($isWorkflowRollback) {
            $feedType = null;
            $batchFeedType = null;
            $historyType = CompanyHistoryPg::TYPE_ASSIGN;
            $checkMovePrivateSea = false;
            $checkCompanyPoolReceiveLimits = false;
            $checkCompanyPrivateLimits = false;
        } else // 别人操作重新分配
        {
            $feedType = TrailConstants::TYPE_CUSTOMER_ASSIGN;
            $batchFeedType = TrailConstants::TYPE_CUSTOMER_ASSIGN;
            $historyType = CompanyHistoryPg::TYPE_ASSIGN;

            $checkMovePrivateSea = false;
            $checkCompanyPoolReceiveLimits = true;
            $checkCompanyPrivateLimits = true;
        }

	    if ($groupId)
            Helper::checkSubGroupSelect($clientId, $groupId);

	    $userIds = is_array($userId) ? $userId : [$userId];
        $extraData = [];

        $alias = empty($this->listObject->getAlias()) ? '' :$this->listObject->getAlias() . '.';
        $count = $this->addOwner($userIds, $groupId, 1, (' and ' . $alias . 'user_id=\'{}\''), $feedType, $batchFeedType, $historyType, $extraData, $checkMovePrivateSea, true, $checkCompanyPrivateLimits, $isWorkflowRollback);

        //更新到公海领取表中
        if ($count > 0 && !$isWorkflowRollback)
        {
            //公海领取客户支持设置查看商机
            $client = \common\library\account\Client::getClient($clientId);
            $setting = $client->getSettingAttributes([\common\library\account\Client::SETTING_KEY_CUSTOMER_POOL_MOVE_PRIVATE_OPPORTUNITY]);
            if($setting[\common\library\account\Client::SETTING_KEY_CUSTOMER_POOL_MOVE_PRIVATE_OPPORTUNITY]){
                $operator = new OpportunityBatchOperator($this->userId);
                $operator->setParams([
                    'user_type' => [1, 2],
                    'show_all' => true,
                    'company_id' => $this->companyIds,
                ]);
                $operator->getList()->setShowAll(true, PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_MEMBER_MANAGE);
                $operator->getList()->setSkipPermissionCheck(true);
                $operator->share($userIds);
            }

            //通过返回的规则，校验有那些user_id可以移入到私海
            $hasRuleUserId = array_keys($this->privateReceiveRules);
			//（公海）分配（一般管理员才有权限），不更新公海领取客户上限额度
			if (!$assignFlag) {
				\common\library\customer\public_record\Helper::batchUpdateDataByPK($clientId, $hasRuleUserId, $this->companyIds);
			}

            //公海领取统计记录
            CompanyHelper::IncCompanyKeyCount($clientId, $userIds, 'public_company_receive_count', $this->companyIds);
            //主动领取与被动领取区分统计述职
            if ($checkMovePrivateSea) {
                CompanyHelper::IncCompanyKeyCount($clientId, $userIds, 'public_company_allocate_receive_count', $this->companyIds);
            } else {
                CompanyHelper::IncCompanyKeyCount($clientId, $userIds, 'public_company_assigned_receive_count', $this->companyIds);
            }


            //上报 507
            QueueService::dispatch(new TipsPushTodoJob([
                'feed_type_id' => '*******',
                'action' => 'push',
                'feed_type' => 'contacts_invalid',
                'company_id' => $this->companyIds ?? [],
                'client_id' => $opUser->getClientId(),
                'user_id' => $userIds,
                'operator_user_id' => $this->userId
            ]));

            if (count($this->companyIds)) {
                (new SwarmService($clientId))->setSkipUpdateSearch()->refreshByRefer($this->companyIds, [], true);
            }

            //公海客户重新分配消息通知
            $companyInfo = [];
            foreach ($extraData as $company){
                $info['id'] = $company['company_id'];
                $info['name'] = $company['name'];
                $companyInfo[] = $info;
            }
            foreach ($userIds as $userId) {
                $notification = new \common\library\notification\Notification($clientId, \common\library\notification\Constant::NOTIFICATION_TYPE_PUBLIC_CUSTOMER_ASSIGN);
                $notification->user_id = $userId;
                $notification->create_user_id = $this->userId;
                $data = [
                    'operator_info' => [
                        'user_id' => $this->userId,
                        'nickname' => $opUser->getNickname(),
                    ],
                    'company_info' => $companyInfo,

                ];
                $notification->setSourceData($data);
                \common\library\notification\PushHelper::pushNotification($clientId, $userId, $notification);
                
                // 钉钉通知
                \common\library\dingding\Helper::pushNotification($notification);
            }

        }

        return $count;
    }

    public function share($userId)
    {
        $operatorTime = date("Y-m-d H:i:s",time());
        $clientId = \User::getUserObject($this->userId)->getClientId();

        if (!is_array($userId))
            $userId = [$userId];

        $count = $this->addOwner($userId, null, 0.5, '', TrailConstants::TYPE_CUSTOMER_SHARE,
            TrailConstants::TYPE_CUSTOMER_BATCH_SHARE, CompanyHistoryPg::TYPE_SHARE, $companyDataList,false);

        if (!$count)
            return 0;

        \CompanyFollowRecord::updateInsertData($clientId, $userId, \CompanyFollowRecord::TYPE_ACQUIRED_FROM_SHARE, $this->companyIds);

        //向影响到的跟进人发送消息
        $diffUserIds = array_diff($userId, [$this->userId]);

        if (!empty($diffUserIds))
        {
            foreach ($diffUserIds as $singleUserId)
            {
                $notification = new Notification($clientId, \common\library\notification\Constant::NOTIFICATION_TYPE_CUSTOMER_SHARE);
                $notification->user_id =  $singleUserId;
                $notification->create_user_id = $this->userId;
                $notification->setSourceData($companyDataList);
                PushHelper::pushNotification($clientId, $singleUserId, $notification);
            }

            //给被分享的人建待办
            $feeds = [
                'user_ids' => $diffUserIds,
                'operator_time' => $operatorTime,
                'object_ids' => $this->companyIds,
                'operator_user_id' => $this->userId
            ];
            $batchPushJob = new BatchPushTodoFeedJob($clientId,TodoConstant::OBJECT_TYPE_COMPANY,TodoConstant::TODO_TYPE_COMPANY_TRANSFER_TO_ME,$feeds);
            QueueService::dispatch($batchPushJob);
        }

        //上报 507
        QueueService::dispatch(new TipsPushTodoJob([
            'feed_type_id' => '*******',
            'action' => 'push',
            'feed_type' => 'contacts_invalid',
            'company_id' => $this->companyIds ?? [],
            'client_id' => $clientId,
            'user_id' => $userId,
            'operator_user_id' => $this->userId
        ]));

        return $count;
    }

    /**
     * 注意 此函数不应该承担从公海移到私海的操作，这类操作统一都调用hold
     * @param $toUserId
     * @param false $removeAll
     * @return int
     * @throws CDbException
     * @throws CException
     */
    public function transfer($toUserId, $removeAll = false, $checkCompanyPrivateLimits = true, $isWorkflowRollback = false, $copyToOthers = false)
    {
        if (!is_array($toUserId))
            $toUserIds = [$toUserId];
        else
            $toUserIds = $toUserId;

        if (count($toUserIds) > self::MAX_BATCH_USER_COUNT)
            throw new \RuntimeException("不能一次转移超过".self::MAX_BATCH_USER_COUNT.'个用户');

        $isSingleUserId = count($toUserIds) == 1;

        if (!$removeAll && $isSingleUserId && $toUserIds[array_key_first($toUserIds)] == $this->userId)
            return 0;


        $opUser = \User::getUserObject($this->userId);
        $clientId = $opUser->getClientId();

        $this->paramsMapping();
        
        // 根据权限过滤
        if (!$this->listObject->getSkipPrivilege()) {
            [$companyIds, $this->noPermissionCompanyIds] = $this->listObject->fetchEditableObjIds([
                'private_company_privilege' => $removeAll ? PrivilegeConstants::PRIVILEGE_CRM_COMPANY_ASSIGN : PrivilegeConstants::PRIVILEGE_CRM_COMPANY_TRANSFER,
                'public_company_privilege' => PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_ASSIGN,
            ]);
            if (!is_null($companyIds)) {
                if (!$companyIds) {
                    return 0;
                }
                $this->listObject->setCompanyIds($companyIds);
            }
        }
        
        [$where,$params] = $this->listObject->buildParams();

        $alias = empty($this->listObject->getAlias()) ? '' :$this->listObject->getAlias() . '.';

        $from = $this->listObject->getFrom();

        $sql = "select {$alias}company_id ,{$alias}user_id,{$alias}name, {$alias}group_id,{$alias}main_customer_email,{$alias}homepage,{$alias}country,{$alias}scale_id,{$alias}company_hash_id,".
            "{$alias}archive_type,{$alias}origin $from $where";
        $companyData = $this->db->createCommand($sql)->queryAll(true, $params);

        if (empty($companyData))
            return 0;

        // TODO 临时代码 因为现在web app desktop 都没有区分开公私海的重新分配 所以遇到这类数据，重新由hold处理 这里会浪费一次sql请求
        // 这里兼容的是app desktop
        if ($removeAll && count($toUserIds) == 1)
        {
            $company = reset($companyData);
            $userId = PgsqlUtil::trimArray($company['user_id']);
            if (empty($userId))
                return $this->hold(null, $toUserIds[0]);
        }

        $companyIds = array_column($companyData, 'company_id');
        $this->companyIds = $companyIds;

        $count = count($companyIds);

        $map = [];
        $diff = ['id'=>'user_id', 'base' => 1];
        foreach ($companyData as $data) {
            $oldOwner = PgsqlUtil::trimArray($data['user_id']);
            $diff['old'] = $oldOwner;
            $diff['new'] = $removeAll ? $toUserIds : array_values(array_unique(array_diff(array_merge($oldOwner, $toUserIds), [$this->userId])));
            $map[$data['company_id']] = [$diff];
        }

        $companyIdsSql = '(' . implode(',', $companyIds) . ')';

        if ($count > self::MAX_BATCH_OPERATOR_COUNT)
            throw new \RuntimeException(\Yii::t('customer', 'Selected customer') . $count . \Yii::t('customer', 'Do not support batch operations'));

        if ($checkCompanyPrivateLimits)
        {
            // process customer limit
            $countMap = Helper::batchGetCompanyCountData($clientId, $toUserIds);

            $list = new UserList();
            $list->setClientId($clientId);
            $list->setUserIds($toUserIds);
            $list->setFields(['customer_limit', 'nickname', 'user_id']);
            $userInfos = $list->find();

            $limitMap = array_column($userInfos, 'customer_limit', 'user_id');

            foreach ($toUserIds as $userId)
            {
                $total = $countMap[$userId]['total_quota'] + $count * 1;
                $limit = $limitMap[$userId] ?? 0;
                if ($limit && $total > $limit)
                    throw new \RuntimeException(\Yii::t('customer', 'Customer amount exceeds the upper limit'));
            }
        }

        $set = '';
        $now = date('Y-m-d H:i:s');
        if (\CustomerOptionService::checkReference($clientId, 'refer_follow_company'))
        {
            $buildSet = Helper::buildUserData('user_data', $toUserIds, ['order_time' => $now], 'refer_follow_company');
            $set .= ",order_time='$now', user_data = $buildSet";
        }

        $set .= ",edit_time='{$now}',private_user_time='{$now}',update_time='{$now}'";
        if ($opUser->getUserId()) {
            $set .= ",last_edit_user={$opUser->getUserId()}";
        }

        $toUserIdsSql = implode(',', $toUserIds);
        $operatorTime = date("Y-m-d H:i:s",time());

        if ($removeAll)
        {
            $companySql = "update tbl_company set user_id='{ $toUserIdsSql }' $set WHERE client_id=$clientId and company_id in $companyIdsSql and is_archive=1";
            $customerSql = "update tbl_customer set user_id='{ $toUserIdsSql }' where client_id=$clientId and company_id in $companyIdsSql and is_archive=1";
        }
        else
        {
            $opUserIdCast = "$this->userId::bigint";

            $companySql = "update tbl_company set user_id=(select ARRAY(select distinct unnest(array_cat(array_remove(user_id, $opUserIdCast), ARRAY[{$toUserIdsSql}]::bigint[])))) $set WHERE client_id=$clientId and company_id in $companyIdsSql and is_archive=1";
            $customerSql = "update tbl_customer set user_id=(select ARRAY(select distinct unnest(array_cat(array_remove(user_id, $opUserIdCast), ARRAY[{$toUserIdsSql}]::bigint[])))) where client_id=$clientId and company_id in $companyIdsSql and is_archive=1";
        }

        $this->db->createCommand($companySql)->execute();
        $this->db->createCommand($customerSql)->execute();

//        // 更新scope_user_ids
//        if ($companyIds) {
//            $scopeUserService = new ScopeUserFieldService($opUser->getClientId(), new CompanyMetadata($opUser->getClientId()));
//            $scopeUserService->refreshScopeUserIdsByPids($companyIds);
//        }

        //获取邮箱用于更新tbl_email_identity表
        (new CustomerSync($opUser->getClientId()))->setFindCompanyId($companyIds)->sync();

        SearchQueueService::pushCompanyQueue($this->userId,$opUser->getClientId(),$companyIds,\Constants::SEARCH_INDEX_TYPE_UPDATE);

        // history
        $historyType = $removeAll ? CompanyHistoryPg::TYPE_ASSIGN : CompanyHistoryPg::TYPE_TRANSFER;
        $historyType = $removeAll && isset($oldOwner) && count($oldOwner) == 0 ? CompanyHistoryPg::TYPE_ALLOCATION : $historyType;
        $this->buildBatchHistory($clientId, $this->userId, $historyType, $map);

        if ($removeAll) {
            $sendMsgData = [];
            foreach ($companyData as $item)
            {
                $userIds = PgsqlUtil::trimArray($item['user_id']);
                foreach ($userIds as $uid)
                {
                    //当客户原有跟进人跟重新分配有相同 该company_id不计入user_id
                    if (in_array($uid, $toUserIds))
                        continue;

                    $sendMsgData[$uid][] = ['company_id' => $item['company_id'], 'name' => $item['name'], 'group_id' => $item['group_id']];
                }
            }
        }

        if (!$isWorkflowRollback)
        {
            // feed
            $type = TrailConstants::TYPE_CUSTOMER_BATCH_TRANSFER;

            $feedData = [
                'to_user_id' => $toUserIds[array_key_first($toUserIds)],
                'to_user_name' => implode(',', array_column($userInfos, 'nickname')),
                'company_count' => $count,
                'company_ids' => $companyIds
            ];

            if ($count == 1)
            {
                $type = $removeAll ? TrailConstants::TYPE_CUSTOMER_ASSIGN : TrailConstants::TYPE_CUSTOMER_TRANSFER;
                $feedData['company_name'] = $companyData[array_key_first($companyData)]['name'];
            }

            $feed = new \Feed();
            $feed->setNodeType($type);
            $feed->setClientId($clientId);
            $feed->setUserId($opUser->getUserId());
            $feed->setCreateUser($opUser->getUserId());
            $feed->setData($feedData);
            $feed->setReferId(0);
            $feed->save();

            //记录操作的companyId
            \CompanyFollowRecord::updateInsertData($clientId, $toUserIds, \CompanyFollowRecord::TYPE_ACQUIRED_FROM_TRANSFER, $companyIds);

            // 向影响到的跟进人发送消息
            $diffUserIds = array_diff($toUserIds, [$this->userId]);
            if (!empty($diffUserIds))
                foreach ($diffUserIds as $singleUserId)
                    NotificationHelper::sendCustomerTransfer($clientId, $singleUserId, $this->userId, $companyData);

            //给原跟进人发送消息通知
            if ($removeAll) {
                foreach ($sendMsgData as $uid => $msgData)
                {
                    if ($uid == $this->userId)
                        continue;

                    $notification = new Notification($opUser->getClientId(), \common\library\notification\Constant::NOTIFICATION_TYPE_CUSTOMER_CANCEL);
                    $notification->create_user_id = $this->userId;
                    $notification->user_id = $uid;
                    $notification->setSourceData([
                        'company_obj_list' => $msgData,
                    ]);
                    PushHelper::pushNotification($opUser->getClientId(), $uid, $notification);
                }
            }

            //上报 507
            QueueService::dispatch(new TipsPushTodoJob([
                'feed_type_id' => '*******',
                'action' => 'push',
                'feed_type' => 'contacts_invalid',
                'company_id' => $this->companyIds ?? [],
                'client_id' => $clientId,
                'user_id' => $toUserIds,
                'operator_user_id' => $this->userId
            ]));

            //上报

            $companyVersion = new CompanyVersion($opUser->getClientId(), $companyIds);
            $companyVersion->setType(Constant::COMPANY_MODULE_EDIT);
            $companyVersion->add();

            //记录行为
            $behavior = new BehaviorService($this->userId);
            $behavior->setType(BehaviorConstant::TYPE_CUSTOMER_TRANSFER);
            $behavior->setReferId($companyIds);

            //转移客户 给被转移的人建待办
            if (!empty($diffUserIds)) {
                $feeds = [
                    'user_ids' => $diffUserIds,
                    'operator_time' => $operatorTime,
                    'object_ids' => $this->companyIds,
                    'operator_user_id' => $this->userId
                ];
                $batchPushJob = new BatchPushTodoFeedJob($clientId,TodoConstant::OBJECT_TYPE_COMPANY,TodoConstant::TODO_TYPE_COMPANY_TRANSFER_TO_ME,$feeds);
                QueueService::dispatch($batchPushJob);
            }
        }
        
        ($removeAll && $copyToOthers && (count($companyIds) == 1)) && (new ConflictService($clientId, \Constants::TYPE_COMPANY))->detectByRefer($companyIds, ConflictConstants::RESOLVE_TYPE_COMPANY_CONFLICT_TRANSFER_ALL);

        $this->runWorkflow($clientId, $companyIds, ['user_id', 'order_time', 'edit_time', 'private_time']);
        $this->runPerformance($clientId, $opUser->getUserId(), \Constants::TYPE_COMPANY, $companyIds,false,[],[],PerformanceV2Constant::PERFORMANCE_RECORD_JOB_SCENE_TRANSFER);

        //ai推荐上报数据
        // TODO aiEventReport循环了一次user id 跟上面类似 可以合并
        // TODO 历史bug 这里没有针对remove all做正确处理
        Helper::aiEventReport($companyData, EventsReport::EVENT_DELETE, $opUser->getUserId(), $clientId,true);
        foreach ($toUserIds as $userId)
            Helper::aiEventReport($companyData, EventsReport::EVENT_CREATE, $userId, $clientId);

        if ($removeAll)
        {
            $returnUserCompanyIdsMap = [];
            foreach ($sendMsgData as $userId => $companyDetails)
            {
                $returnUserCompanyIdsMap[$userId] = array_column($companyDetails, 'company_id');
            }
        }
        else
        {
            $returnUserCompanyIdsMap[$opUser->getUserId()] = $this->companyIds ?? [];
        }

		//(私海）重新分配，不更新公海领取客户上限额度
		//（私海）转移，不更新公海领取客户上限额度
        //\common\library\customer\public_record\Helper::returnHoldPublicCompanyLimits($clientId, $returnUserCompanyIdsMap);

        if ($removeAll)
            $this->reportCompanyMailChange($this->buildReportUserId($companyData));
        else
            $this->reportCompanyMailChange($toUserIds);

        // 删除相关[跟进客户]任务
        $deleteTaskUserIds = $removeAll ? [] : [$this->userId];
        \common\library\task\Helper::deleteTask($clientId, $companyIds, $deleteTaskUserIds);

        //消除原跟进人的待办
        //重新分配
        if ($removeAll) {
            $feeds = $sendMsgData;
        } else {
            $feeds =[$this->userId => $companyData];
        }


        //消除原跟进人501待办
        $batchDeleteJob = new BatchDeleteTodoFeedJob($clientId, TodoConstant::OBJECT_TYPE_COMPANY, TodoConstant::TODO_TYPE_COMPANY_TRANSFER_TO_ME, $feeds);
        QueueService::dispatch($batchDeleteJob);

        //消除原跟进人503待办
        $batchDeleteJob = new BatchDeleteTodoFeedJob($clientId, TodoConstant::OBJECT_TYPE_COMPANY, TodoConstant::TODO_TYPE_COMPANY_DATA_UPDATE, $feeds);
        QueueService::dispatch($batchDeleteJob);

        //消除原跟进人504待办s
        $batchDeleteJob = new BatchDeleteTodoFeedJob($clientId, TodoConstant::OBJECT_TYPE_COMPANY, TodoConstant::TODO_TYPE_COMPANY_TRAIL_UPDATE, $feeds);
        QueueService::dispatch($batchDeleteJob);

        //消除原跟进人508待办
        $batchDeleteJob = new BatchDeleteTodoFeedJob($clientId, TodoConstant::OBJECT_TYPE_COMPANY, TodoConstant::TODO_TYPE_COMPANY_CUSTOMS_UPDATE, $feeds);
        QueueService::dispatch($batchDeleteJob);

        // 关闭共享客户邮件
        if ($removeAll) {
            \DistributeCustomerMailRule::disableCompanyId($opUser->getClientId(), $opUser->getUserId(), $companyIds);
        } else {
            \DistributeCustomerMailRule::disableCompanyId($opUser->getClientId(), $opUser->getUserId(), $companyIds, [$this->userId]);
        }

        // 聊天服务归属人变更
        $newOwnerInfo = [];
        foreach ($companyData as $company) {
            $owner = PgsqlUtil::trimArray($company['user_id']) ?? [];
            $owner = $removeAll ? $toUserIds : array_values(array_unique(array_diff(array_merge($owner, $toUserIds), [$this->userId])));
            $newOwnerInfo[$company['company_id']] = $owner;
        }
        ChatService::getInstance($opUser->getClientId(), $opUser->getUserId())->transferContactsAsync($newOwnerInfo);
    
        
        if ($removeAll && $copyToOthers && (count($companyIds) == 1)) {
            
            $this->copyToOthers($companyIds[0], array_diff($map[$companyIds[0]][0]['old'] ?? [], $map[$companyIds[0]][0]['new'] ?? []));
        }
    
        return $count;
    }

    public function setGroupId($groupId, $skipLimitCheck = false)
    {
        if (!is_numeric($groupId) && empty($groupId)) {
            return 0;
        }

        $user = \User::getUserObject($this->userId);
        $clientId = $user->getClientId();

        Helper::acceptUseGroup($clientId, $groupId, $this->userId, true);
        Helper::checkSubGroupSelect($clientId, $groupId);

        $this->paramsMapping();

        // 根据权限过滤
        if (!$this->listObject->getSkipPrivilege()) {
            [$companyIds, $this->noPermissionCompanyIds] = $this->listObject->fetchEditableObjIds([
                'private_company_privilege' => PrivilegeConstants::PRIVILEGE_CRM_COMPANY_EDIT,
                'public_company_privilege' => PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_EDIT,
            ], 'group_id');
            if (!is_null($companyIds)) {
                if (!$companyIds) {
                    return 0;
                }
                $this->listObject->setCompanyIds($companyIds);
            }
        }

        [$where,$params] = $this->listObject->buildParams();

        $alias = empty($this->listObject->getAlias()) ? '' :$this->listObject->getAlias() . '.';

        $from = $this->listObject->getFrom();

        $sql = "select {$alias}company_id,{$alias}user_id, {$alias}group_id,{$alias}main_customer_email,{$alias}homepage,{$alias}country,{$alias}scale_id,{$alias}company_hash_id,{$alias}archive_type,".
        "{$alias}origin $from $where";

        $companyData = $this->db->createCommand($sql)->queryAll(true, $params);

        if (empty($companyData))
            return 0;

        $companyIds = [];
        $keepCompanyIds = [];
        foreach ($companyData as $companyDatum) {
            if ($companyDatum['group_id'] != $groupId) {
                $companyIds[] = $companyDatum['company_id'];
            } else {
                $keepCompanyIds[] = $companyDatum['company_id'];
            }
        }
        if (empty($companyIds)) {
            return count($companyData);
        }

//        $companyIds = array_column($companyData, 'company_id');
        $companyIdsSql = implode(',', $companyIds);

        $count = count($companyIds);
        if( !$skipLimitCheck && $count > self::MAX_BATCH_OPERATOR_COUNT ){
            throw  new \RuntimeException(\Yii::t('customer', 'Selected customer') . $count . \Yii::t('customer', 'Do not support batch operations'));
        }

        $set = '';

        if (\CustomerOptionService::checkReference($clientId, 'refer_modified_data'))
        {
            $now = date('Y-m-d H:i:s');

            $buildSet = Helper::buildUserData('user_data', $this->userId, ['order_time' => $now], 'refer_modified_data');
            $set .= "order_time='$now', user_data = $buildSet";

            $sql = "update tbl_customer set $set where client_id=$clientId and company_id in ($companyIdsSql)  and is_archive=1;";

            $this->db->createCommand($sql)->execute();

            $set = ','.$set;
        }


        $now = $createFeedTime = date("Y-m-d H:i:s", time());

        $companySetSql = "group_id={$groupId},update_time='{$now}'";
        if ($this->userId) {
            $companySetSql .= ",last_edit_user={$this->userId}";
        }
        $sql = "update tbl_company set {$companySetSql} $set where client_id=$clientId and company_id in ($companyIdsSql)";

        $count = $this->db->createCommand($sql)->execute();

        // HISTORY
        $map = [];
        $notChangedIds = [];
        foreach ($companyData as $elem)
        {
            if ($groupId == $elem['group_id']) {
                $notChangedIds[] = $elem['company_id'];
            }
            $map[$elem['company_id']] = [
                [
                    'id' => 'group_id',
                    'base' => 1,
                    'new' => $groupId,
                    'old' => $elem['group_id']
                ]
            ];
        }

        $operateScene = $this->getOperateScene();

        $history = new BatchCompanyBuilder();
        if ($operateScene->scene && isset($operateScene->referData['refer_type']) && isset($operateScene->referData['refer_id']))
        {
            $history->setEditInfo($operateScene->scene,null,null,$operateScene->referData['refer_type'], $operateScene->referData['refer_id']);
        }

        $history->buildByMap($clientId, $this->userId, CompanyHistoryPg::TYPE_EDIT_COMPANY, $map);

        SearchQueueService::pushCompanyQueue($this->userId, $clientId, $companyIds,\Constants::SEARCH_INDEX_TYPE_UPDATE);

        //ai推荐上报 todo 优化这边上报
        $ret = Helper::aiEventReport($companyData, EventsReport::EVENT_DELETE, $this->userId, $user->getClientId(),true);
        if ($ret) {
            //查询新的数据
            $sql = "select {$alias}company_id,{$alias}user_id, {$alias}group_id,{$alias}main_customer_email,{$alias}homepage,{$alias}country,{$alias}scale_id,{$alias}company_hash_id,{$alias}archive_type,".
                "{$alias}origin $from $where";

            $newCompanyDatas = $this->db->createCommand($sql)->queryAll(true, $params);

            if (!empty($newCompanyDatas)) {
                Helper::aiEventReport($newCompanyDatas, EventsReport::EVENT_CREATE, $this->userId, $user->getClientId(),true);
            }
        }

        //记录行为
        $behavior = new BehaviorService($this->userId);
        $behavior->setType(BehaviorConstant::TYPE_CUSTOMER_EDIT);
        $behavior->setReferId($companyIds);

        $updateCompanyIds = array_diff($companyIds, $notChangedIds);
        $this->runWorkflow($clientId, $companyIds, ['group_id', 'order_time', 'group_id']);
        $this->runPerformance($clientId, $this->userId, \Constants::TYPE_COMPANY, $companyIds);

        $companyFollowUserIs = array_column($companyData,'user_id','company_id');
        //待办上报
        Helper::batchSetnxUniqueFeeds($clientId, $this->userId, $companyFollowUserIs, ['create_time' => $createFeedTime], TodoConstant::TODO_TYPE_COMPANY_DATA_UPDATE);

        return $count + count($keepCompanyIds);
    }

    //批量重置标签
    public function resetTags($tagIds) {
        $user = \User::getUserObject($this->userId);
        $userId = $user->getUserId();

        $tagIds = is_array($tagIds) ? $tagIds : [$tagIds];

        $api = new TagApi($user->getClientId(), \Constants::TYPE_COMPANY, $userId);
	    [$userTagIds, $clientTagIds] = $api->getTagsByTagId($tagIds);

        //对tag数据重新组装
        if (empty($userTagIds) && empty($clientTagIds)) return 0;

        //处理参数
        $this->paramsMapping();

        // 根据权限过滤
        if (!$this->listObject->getSkipPrivilege()) {
            [$companyIds, $this->noPermissionCompanyIds] = $this->listObject->fetchEditableObjIds([
                'private_company_privilege' => PrivilegeConstants::PRIVILEGE_CRM_COMPANY_EDIT,
                'public_company_privilege' => PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_EDIT,
            ], 'cus_tag');
            if (!is_null($companyIds)) {
                if (!$companyIds) {
                    return 0;
                }
                $this->listObject->setCompanyIds($companyIds);
            }
        }

        [$where, $params] = $this->listObject->buildParams();

        $alias = empty($this->listObject->getAlias()) ? '' :$this->listObject->getAlias() . '.';

        $from = $this->listObject->getFrom();

        $sql = "select {$alias}company_id $from $where";

        $companyIds = $this->db->createCommand($sql)->queryColumn($params);
        $count = count($companyIds);
        if ($count == 0) return 0;
        if ($count > self::MAX_BATCH_OPERATOR_COUNT) {
            throw  new \RuntimeException(\Yii::t('customer', 'Selected customer') . $count . \Yii::t('customer', 'Do not support batch operations'));
        }

        //需要按传入顺序打标签
        $tagJsonb = PgsqlUtil::formatBson($userTagIds);
        $setTagString = "tag = jsonb_set(tag, '{\"$this->userId\"}', $tagJsonb)";

	    $setTagString .= ', client_tag_list = ARRAY[ '.implode(',', $clientTagIds).']::BIGINT[]';

        if (!empty($where)) {
            $sql = "update tbl_company set $setTagString $where";
            $count = $this->db->createCommand($sql)->execute($params);
        } else {
            $count = 0;
        }

        $this->runWorkflow($user->getClientId(), $companyIds, ['tag', 'client_tag_list', 'cus_tag']);
        $this->runPerformance($user->getClientId(), $user->getUserId(), \Constants::TYPE_COMPANY, $companyIds);

        //记录行为
        $behavior = new BehaviorService($userId);
        $behavior->setType(BehaviorConstant::TYPE_CUSTOMER_SET_TAG);
        $behavior->setReferId($companyIds);
        $behavior->add();

        return $count;
    }

    public function addTag(array $tagIds, &$tagListData = [], $clientTag = true, $userId = 0){

	    \LogUtil::info('[TAG][addTag]LoginUser:' . \User::getLoginUser()->getUserId()
		    . ', LoginUserClient:' . \User::getLoginUser()->getClientId()
		    . ', tagIDs:' . json_encode($tagIds)
		    . ', tagListData:' . json_encode($tagListData)
		    . ', clientTag:' . $clientTag
		    . ', userId:' . $userId);

        $userId = !empty($userId) ? $userId : $this->userId;
        $user = \User::getUserObject($userId);
        $clientId = $user->getClientId();

        $tagIds = array_filter($tagIds);
        if (empty($tagIds))
            return 0;

        $api = new \common\library\setting\library\tag\TagApi($user->getClientId(), \Constants::TYPE_COMPANY, $this->userId);
        $api->getParams()->setOwnerUser($userId, boolval($clientTag));
        $tagList = $api->list($tagIds, true);
//        $tagListObj = new GeneralTagList($user->getClientId(), $this->userId, \Constants::TYPE_COMPANY);
//        $tagListObj->setTagId($tagIds);
//        $tagListObj->setClientTag($clientTag);
//        $tagList = $tagListObj->find();
        $tagIdList = $newTagList = array_column($tagList, 'tag_id');
        $tagIdList = array_intersect($tagIds, $tagIdList);
        if(empty($tagIdList)){
            return 0;
        }

        $tagNameList = array_unique(array_column($tagList, 'tag_name'));

        $tagListData = array_map(function($elem){
            return [
                'tag_id' => $elem['tag_id'],
                'tag_name' => $elem['tag_name'],
                'color' => $elem['tag_color'],
            ];
        },$tagList);

        $this->paramsMapping();

        // 根据权限过滤
        if (!$this->listObject->getSkipPrivilege()) {
            [$companyIds, $this->noPermissionCompanyIds] = $this->listObject->fetchEditableObjIds([
                'private_company_privilege' => PrivilegeConstants::PRIVILEGE_CRM_COMPANY_EDIT,
                'public_company_privilege' => PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_EDIT,
            ], 'cus_tag');
            if (!is_null($companyIds)) {
                if (!$companyIds) {
                    return 0;
                }
                $this->listObject->setCompanyIds($companyIds);
            }
        }

        [$where,$params] = $this->listObject->buildParams();

        $alias = empty($this->listObject->getAlias()) ? '' :$this->listObject->getAlias() . '.';

        $from = $this->listObject->getFrom();

        $sql = "select {$alias}company_id, {$alias}client_tag_list $from $where";

        $companyList = $this->db->createCommand($sql)->queryAll(true, $params);

		$companyIds = array_column($companyList, 'company_id');

        $count = count($companyIds);
        if ($count == 0){
            return 0;
        }
        if ($count > self::MAX_BATCH_OPERATOR_COUNT) {
            throw  new \RuntimeException(\Yii::t('customer', 'Selected customer') . $count . \Yii::t('customer', 'Do not support batch operations'));
        }

        $companyIdsSql = implode(',', $companyIds);

	    [$userTagIds, $clientTagIds] = (new TagApi($user->getClientId(), \Constants::TYPE_COMPANY, $userId))->getTagsByTagId($tagIds, null);

	    $sql = "update tbl_company  {$this->listObject->getAlias()}
							set tag = tag || JSONB_BUILD_OBJECT(
							               '{$userId}',
							               (ARRAY(SELECT UNNEST(TRANSLATE((NULLIF(tag ->> '{$userId}', '')) :: JSONB::TEXT, '[]', '{}')::BIGINT[]))
							                   ||
							               ARRAY(
							                       SELECT *
                                          				FROM (SELECT UNNEST(ARRAY [" . implode(', ', $userTagIds) . "] :: BIGINT[])
							                                  EXCEPT
							                                  SELECT UNNEST(TRANSLATE((NULLIF(tag ->> '{$userId}', '')) :: JSONB::TEXT, '[]', '{}')::BIGINT[])
							                                  ) t1(tag)
		                                           ORDER BY array_position(ARRAY [" . implode(', ', $userTagIds) . "] :: BIGINT[], tag)
							                   ))
							           ),
							    client_tag_list = (client_tag_list::BIGINT[])
										           || ARRAY (
													           SELECT *
			                                                        FROM (
															               SELECT UNNEST(ARRAY [" . implode(', ', $clientTagIds) . "] :: BIGINT[])
															               EXCEPT
															               SELECT UNNEST(client_tag_list::BIGINT[])
															               ) t1(client_tag_list)
                                                               ORDER BY array_position(ARRAY [" . implode(', ', $clientTagIds) . "] :: BIGINT[], client_tag_list)
															)
                            where  client_id=$clientId and company_id in ($companyIdsSql)";

        $count = $this->db->createCommand($sql)->execute();

	    \LogUtil::info('[TAG][addTag]LoginUser:' . \User::getLoginUser()->getUserId()
		    . ', LoginUserClient:' . \User::getLoginUser()->getClientId()
		    . ', sql:' . $sql
		    . ', count:' . $count);

        // 更新云Tag
        $cloudTagIdList = \CustomerTagService::getTagIdList($tagNameList);

        $sql = "select email from tbl_customer where client_id=$clientId and is_archive=1 and company_id in ($companyIdsSql)";
        $emailList = $this->db->createCommand($sql)->queryColumn();
        \CustomerTagService::update($emailList, $cloudTagIdList);

        $this->runWorkflow($user->getClientId(), $companyIds, ['tag', 'client_tag_list', 'cus_tag']);
        $this->runPerformance($user->getClientId(), $user->getUserId(), \Constants::TYPE_COMPANY, $companyIds);


	    $map = [];

	    foreach ($companyList as $item) {

		    $item['client_tag_list'] = \common\library\util\PgsqlUtil::trimArray($item['client_tag_list']) ?? [];

		    $new = array_values(array_unique(array_merge($item['client_tag_list'], $clientTagIds)));

		    if (count(array_diff($new, $item['client_tag_list'],)) == 0) {

			    continue;
		    }

		    $map[$item['company_id']] = [[
			    'id'   => 'client_tag_list',
			    'base' => 1,
			    'old'  => $item['client_tag_list'],
			    'new'  => $new
		    ]];

	    }

	    $history = new BatchCompanyBuilder();

	    $history->buildByMap($clientId, $this->userId, CompanyHistoryPg::TYPE_EDIT_COMPANY, $map);

        //记录行为
        $behavior = new BehaviorService($userId);
        $behavior->setType(BehaviorConstant::TYPE_CUSTOMER_SET_TAG);
        $behavior->setReferId($companyIds);
        $behavior->add();

        return $count;
    }

    public function removeTag($tagId, $userId = 0, $skipLimitCheck = false)
    {

	    \LogUtil::info('[TAG][removeTag]LoginUser:' . \User::getLoginUser()->getUserId()
		    . ', LoginUserClient:' . \User::getLoginUser()->getClientId()
		    . ', tagId:' . json_encode($tagId)
		    . ', userId:' . $userId);

	    $userId = !empty($userId) ? $userId : $this->userId;

	    $tagId = (array)$tagId;

        $user = \User::getUserObject($this->userId);

        $this->paramsMapping();

        // 根据权限过滤
        if (!$this->listObject->getSkipPrivilege()) {
            [$companyIds, $this->noPermissionCompanyIds] = $this->listObject->fetchEditableObjIds([
                'private_company_privilege' => PrivilegeConstants::PRIVILEGE_CRM_COMPANY_EDIT,
                'public_company_privilege' => PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_EDIT,
            ], 'cus_tag');
            if (!is_null($companyIds)) {
                if (!$companyIds) {
                    return 0;
                }
                $this->listObject->setCompanyIds($companyIds);
            }
        }

        [$where,$params] = $this->listObject->buildParams();

        $alias = empty($this->listObject->getAlias()) ? '' :$this->listObject->getAlias() . '.';

        $from = $this->listObject->getFrom();

        $sql = "select {$alias}company_id, {$alias}client_tag_list $from $where";

        $companyList = $this->db->createCommand($sql)->queryAll(true, $params);
	    $companyIds = array_column($companyList, 'company_id');
        $count = count($companyIds);

	    [$userTagIds, $clientTagIds] = (new TagApi($user->getClientId(), \Constants::TYPE_COMPANY, $userId))->getTagsByTagId($tagId, null);
        
        $count = count($companyIds);
        
        if (!$skipLimitCheck && $count > self::MAX_BATCH_OPERATOR_COUNT) {
            
            throw new \RuntimeException($count . \Yii::t('customer', 'Do not support batch operations'));
        }
        
	    if (count($companyIds) > 0) {

		    $companyIdsSql = implode(',', $companyIds);
		    // 更新
		    $sql = "update tbl_company
					SET tag             = tag || JSONB_BUILD_OBJECT(
					        '{$userId}',
					        ARRAY(
					                SELECT UNNEST(TRANSLATE((NULLIF(tag ->> '{$userId}', '')) :: JSONB::TEXT, '[]', '{}')::BIGINT[])
					                EXCEPT
					                SELECT UNNEST(ARRAY [" . implode(', ', $tagId) . "] :: BIGINT[])
					            )),
					    client_tag_list = ARRAY(
					            SELECT UNNEST(client_tag_list)
					            EXCEPT
					            SELECT UNNEST(ARRAY [" . implode(', ', $tagId) . "] :: BIGINT[])
					        )
                   where  client_id=:client_id and company_id in ($companyIdsSql)";

		    $count = $this->db->createCommand($sql)->execute([':client_id' => $user->getClientId()]);

		    \LogUtil::info('[TAG][removeTag]LoginUser:' . \User::getLoginUser()->getUserId()
			    . ', LoginUserClient:' . \User::getLoginUser()->getClientId()
			    . ', sql:' . $sql
			    . ', count:' . $count
		    );

	    }

        $this->runWorkflow($user->getClientId(), $companyIds, ['tag', 'client_tag_list', 'cus_tag']);
        $this->runPerformance($user->getClientId(), $user->getUserId(), \Constants::TYPE_COMPANY, $companyIds);



	    $map = [];

	    foreach ($companyList as $item) {

		    $item['client_tag_list'] = \common\library\util\PgsqlUtil::trimArray($item['client_tag_list']) ?? [];

		    $new = array_values(array_unique(array_diff($item['client_tag_list'], $clientTagIds)));

		    if (count(array_diff($item['client_tag_list'], $new)) == 0) {

			    continue;
		    }

		    $map[$item['company_id']] = [[
			                                 'id'   => 'client_tag_list',
			                                 'base' => 1,
			                                 'old'  => $item['client_tag_list'],
			                                 'new'  => $new
		                                 ]];

	    }

	    $history = new BatchCompanyBuilder();

	    $history->buildByMap($user->getClientId(), $this->userId, CompanyHistoryPg::TYPE_EDIT_COMPANY, $map);



	    //记录行为
        $behavior = new BehaviorService($userId);
        $behavior->setType(BehaviorConstant::TYPE_CUSTOMER_DISABLE_TAG);
        $behavior->setReferId($companyIds);
        $behavior->add();

        return $count;
    }


    /**
     * @depracated
     * @param $tagIds
     * @return void
     */
    public function deleteTag($tagIds = []) {

		$sql = 'SELECT company_id
					FROM tbl_company
					WHERE client_id = 1
					  AND is_archive = 1
					  AND client_tag_list && array ['.implode(',', $tagIds).']::BIGINT[]';

		$this->getList()->showAll(true, PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW);

		$this->setParams(['tags' => $tagIds]);

		$this->removeTag($tagIds, 0, true);
	}


    public function addProductGroupIds($company_ids = [], $product_group_ids = [], $userId = 0, $clientId = 0)
    {
        if (empty($company_ids) || empty($product_group_ids) || empty($clientId)) {
            return 0;
        }

        $product_group_ids = array_filter($product_group_ids);
        if (empty($product_group_ids))
            return 0;

        $userId = !empty($userId) ? $userId : $this->userId;

        $product_group_list = \common\library\group\Helper::getGroupNameMap($clientId, \Constants::TYPE_PRODUCT, $product_group_ids, true);
        $product_group_ids = array_intersect($product_group_ids, array_keys($product_group_list));

        if(empty($product_group_ids)){
            return 0;
        }

        $this->setParams(['company_ids' => $company_ids]);
        $this->listObject->setSkipPrivilege(true);

        $this->paramsMapping();
        [$where,$params] = $this->listObject->buildParams();

        $alias = empty($this->listObject->getAlias()) ? '' :$this->listObject->getAlias() . '.';

        $from = $this->listObject->getFrom();

        $sql = "select {$alias}company_id $from $where";

        $companyIds = $this->db->createCommand($sql)->queryColumn($params);

        $count = count($companyIds);

        if ($count > self::MAX_BATCH_OPERATOR_COUNT) {
            throw new \RuntimeException($count . \Yii::t('customer', 'Do not support batch operations'));
        }

        if($count > 0){
            $companyIdsSql = implode(',', $companyIds);
            $producdGroupIdsSql = implode(',', $product_group_ids);
            $sql = "UPDATE tbl_company SET product_group_ids = (SELECT ARRAY (SELECT UNNEST (product_group_ids) UNION SELECT UNNEST (ARRAY [{$producdGroupIdsSql}]:: BIGINT [])))
WHERE client_id=:client_id AND company_id IN ($companyIdsSql);";
            $this->db->createCommand($sql)->execute([':client_id'=> $clientId]);

            $history = new BatchCompanyBuilder();
            $history->build($clientId, $userId, CompanyHistoryPg::TYPE_ADD_PRODUCT_GROUP_ID,
                $companyIds, [['id'=>'product_group_ids', 'base' => 1, 'old' => [], 'new' => [$product_group_ids]]]);

            $this->runWorkflow($clientId, $companyIds, ['product_group_ids']);
            $this->runPerformance($clientId, $userId, \Constants::TYPE_COMPANY, $companyIds);
        }
        return $count;
    }

    public function removeProductGroupIds($product_group_id, $userId = 0, $skipLimitCheck = false) {

        if (empty($product_group_id)) {
            return 0;
        }

        $userId = !empty($userId) ? $userId : $this->userId;
        
		$user = \User::getUserObject($userId);

		$clientId = $user->getClientId();
    
        $product_group_id = (array)$product_group_id;
        
        $product_group_children_ids = \common\library\group\Helper::getChildrenIds($clientId, \Constants::TYPE_PRODUCT, $product_group_id);
        $product_group_ids = array_merge($product_group_id,$product_group_children_ids ?? []);

        $this->listObject->setSkipPrivilege(true);

        $this->paramsMapping();
    
        $this->listObject->setProductGroupIds($product_group_ids);
    
        [$where,$params] = $this->listObject->buildParams();

        $alias = empty($this->listObject->getAlias()) ? '' :$this->listObject->getAlias() . '.';

        $from = $this->listObject->getFrom();

        $sql = "select {$alias}company_id $from $where";

        $companyIds = $this->db->createCommand($sql)->queryColumn($params);
        $count = count($companyIds);

        if (!$skipLimitCheck && $count > self::MAX_BATCH_OPERATOR_COUNT) {
            throw new \RuntimeException($count . \Yii::t('customer', 'Do not support batch operations'));
        }

        if($count > 0){
            $companyIdsSql = implode(',', $companyIds);
            $producdGroupIdsSql = implode(',', $product_group_ids);
            $sql = "UPDATE tbl_company SET product_group_ids = (SELECT ARRAY (SELECT UNNEST (product_group_ids) EXCEPT SELECT UNNEST (ARRAY [{$producdGroupIdsSql}]:: BIGINT [])))
WHERE client_id=:client_id AND company_id IN ($companyIdsSql);";
            $this->db->createCommand($sql)->execute([':client_id'=> $clientId]);

            $history = new BatchCompanyBuilder();
            $history->build($clientId, $userId, CompanyHistoryPg::TYPE_REMOVE_PRODUCT_GROUP_ID,
                $companyIds, [['id'=>'product_group_ids', 'base' => 1, 'old' => $product_group_id, 'new' => []]]);

            $this->runWorkflow($clientId, $companyIds, ['product_group_ids']);
            $this->runPerformance($clientId, $userId, \Constants::TYPE_COMPANY, $companyIds);
        }
        return $count;
    }

	/**
	 * 设置客户公海分组
	 * @param $poolId
	 * @param bool $skipLimitCheck
	 * @param string $scene
	 * @return int
	 * @throws CDbException
	 * @throws CException
	 * @throws ProcessException
	 */
    public function setPoolId($poolId, $skipLimitCheck = false, $scene = 'list')
    {
        $user = \User::getUserObject($this->userId);
        $clientId = $user->getClientId();

        if (!is_numeric($poolId) || !(\common\library\customer\pool\Helper::userInPool($clientId, $this->userId, [$poolId]))) {
            throw new \RuntimeException(\Yii::t('customer', 'No such high seas group permissions'));
        }

        $this->paramsMapping();

        // 根据权限过滤
        if (!$this->listObject->getSkipPrivilege()) {
            [$companyIds, $this->noPermissionCompanyIds] = $this->listObject->fetchEditableObjIds([
                'private_company_privilege' => PrivilegeConstants::PRIVILEGE_CRM_COMPANY_EDIT,
                'public_company_privilege' => PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_EDIT,
            ], 'pool_id');
            if (!is_null($companyIds)) {
                if (!$companyIds) {
                    return 0;
                }
                $this->listObject->setCompanyIds($companyIds);
            }
        }

        [$where,$params] = $this->listObject->buildParams();

        $alias = empty($this->listObject->getAlias()) ? '' :$this->listObject->getAlias() . '.';

        $from = $this->listObject->getFrom();

        $sql = "select {$alias}company_id,{$alias}pool_id,{$alias}user_id $from $where";

        $companyData = $this->db->createCommand($sql)->queryAll(true, $params);

        if (empty($companyData))
            return 0;

        $companyIds = [];
        $keepCompanyIds = [];
        foreach ($companyData as $companyDatum) {
            if ($companyDatum['pool_id'] != $poolId) {
                $companyIds[] = $companyDatum['company_id'];
            } else {
                $keepCompanyIds[] = $companyDatum['company_id'];
            }
        }
        if (empty($companyIds)) {
            return count($keepCompanyIds);
        }

//        $companyIds = array_column($companyData, 'company_id');

		$client = Client::getClient($clientId);
		$detectKey = DuplicateConstants::MODULE_MAP[\Constants::TYPE_COMPANY]['detecting'];
		$ruleTypeKey = DuplicateConstants::MODULE_MAP[\Constants::TYPE_COMPANY]['rule_type'];
		$flag = (($client->getSettingAttributes()[$detectKey] ?? 0) == \common\library\duplicate\DuplicateConstants::DUPLICATE_CONFLICT_DETECTING) ? DuplicateFlagBuilder::DUPLICATE_ALL_RULE_TYPE : ($client->getSettingAttributes()[$ruleTypeKey] ?? 0);
		$maxConst = (($flag != DuplicateFlagBuilder::DUPLICATE_POOL_ID_RULE_TYPE) ? self::MAX_BATCH_OPERATOR_COUNT : self::MAX_POOL_RULE_TYPE_BATCH_OPERATOR_COUNT);

        $count = count($companyIds);
        if( !$skipLimitCheck && ($count > $maxConst) && ($scene != self::POOL_SETTING_OPERATE) ){
            throw  new \RuntimeException(\Yii::t('customer', 'Selected customer') . $count . \Yii::t('customer', 'Do not support batch operations more than {num}', ['num' => $maxConst]));
        }

		if($scene == self::POOL_SETTING_OPERATE && $count > $maxConst){
			$companyIds = array_chunk($companyIds, $maxConst)[0];
		}

		//todo 判重校验
		if ($flag == DuplicateFlagBuilder::DUPLICATE_POOL_ID_RULE_TYPE) {
			$failCompanyIds = Helper::getDuplicateFailCount($clientId, $user->getUserId(), $companyIds, $poolId);
			$this->failCount = count($failCompanyIds);
			$companyIds = array_diff($companyIds, $failCompanyIds);
		}

		if (empty($companyIds)) {
			return count($keepCompanyIds);
		}

		$companyIdsSql = implode(',', $companyIds);

        $set = '';

        if (\CustomerOptionService::checkReference($clientId, 'refer_modified_data'))
        {
            $now = date('Y-m-d H:i:s');

            $buildSet = Helper::buildUserData('user_data', $this->userId, ['order_time' => $now], 'refer_modified_data');
            $set .= "order_time='$now', user_data = $buildSet";

            $sql = "update tbl_customer set $set where company_id in ($companyIdsSql) and client_id=$clientId and is_archive=1;";
            $this->db->createCommand($sql)->execute();

            $set = ','.$set;
        }

        $now = $createFeedTime = date("Y-m-d H:i:s", time());

        $companySetSql = "pool_id={$poolId},update_time='{$now}'";
        if ($this->userId) {
            $companySetSql .= ",last_edit_user={$this->userId}";
        }
        $sql = "update tbl_company set $companySetSql $set where client_id=$clientId AND company_id in ($companyIdsSql)";
        $count = $this->db->createCommand($sql)->execute([]);

        // HISTORY
        $map = [];
        $notChangedIds = [];
        foreach ($companyData as $elem)
        {
            if ($elem['pool_id'] == $poolId) {
                $notChangedIds[] = $elem['company_id'];
            }
            $map[$elem['company_id']] = [
                [
                    'id' => 'pool_id',
                    'base' => 1,
                    'new' => $poolId,
                    'old' => $elem['pool_id']
                ]
            ];
        }
        $operateScene = $this->getOperateScene();

        $history = new BatchCompanyBuilder();
        if ($operateScene->scene && isset($operateScene->referData['refer_type']) && isset($operateScene->referData['refer_id']))
        {
            $history->setEditInfo($operateScene->scene,null,null,$operateScene->referData['refer_type'], $operateScene->referData['refer_id']);
        }

        $history->buildByMap($clientId, $this->userId, CompanyHistoryPg::TYPE_EDIT_COMPANY, $map);

//        Helper::updateSearchIndex($this->userId, $clientId, $companyIds,\Constants::SEARCH_INDEX_TYPE_UPDATE);

        //记录行为
        $behavior = new BehaviorService($this->userId);
        $behavior->setType(BehaviorConstant::TYPE_CUSTOMER_EDIT);
        $behavior->setReferId($companyIds);

        (new CustomerSync($clientId))->setFindCompanyId($companyIds)->sync();
        $updateCompanyIds = array_diff($companyIds, $notChangedIds);
        $this->runWorkflow($clientId, $updateCompanyIds, ['pool_id', 'order_time']);
        $this->runPerformance($clientId, $this->userId, \Constants::TYPE_COMPANY, $updateCompanyIds);

        $companyFollowUserIs = array_column($companyData,'user_id','company_id');
        //待办上报
        Helper::batchSetnxUniqueFeeds($clientId, $this->userId, $companyFollowUserIs, ['create_time' => $createFeedTime], TodoConstant::TODO_TYPE_COMPANY_DATA_UPDATE);

        \common\library\server\es_search\SearchQueueService::pushCompanyQueue($this->userId,$clientId,$companyIds,\Constants::SEARCH_INDEX_TYPE_UPDATE_NEW_COMPANY, true);

        return $count + count($keepCompanyIds);;
    }

    public function getWorkflowReferType()
    {
        return \Constants::TYPE_COMPANY;
    }

    public function buildBatchHistory($clientId, $userId, $historyType, $map)
    {
        $history = new BatchCompanyBuilder();
        if ($this->editReferId) {
            $history->setEditInfo($this->editReferType, null, $this->editReferId);
            $userId = 0;
        }
        $history->buildByMap($clientId, $userId, $historyType, $map);
    }

    private function buildReportUserId(array $userIdField, $exclude = null)
    {
        $userIds = [];

        foreach ($userIdField as $data)
            $userIds = array_merge($userIds, is_array($data['user_id']) ? $data['user_id'] : PgsqlUtil::trimArray($data['user_id']));
        $userIds = array_unique($userIds);

        if ($exclude)
            $userIds = array_diff($userIds, [$exclude]);

        return $userIds;
    }

    protected function reportCompanyMailChange($userId)
    {
        $userIds = is_array($userId) ? $userId : [$userId];

        foreach ($userIds as $userId)
        {
            $user = \User::getUserObject($userId);
            $clientId = $user->getClientId();

            $report = new Report($clientId, $this->userId);
            $report->rebuildCompany();
        }
    }

    public function refreshStatistics()
    {
        \LogUtil::info('refresh statistics  begin');
        $this->paramsMapping();

        $listObj = $this->listObject;
        $listObj->setFields(['company_id']);
        $list = $this->listObject->find();
        \LogUtil::info('refresh status  count:'.count($list));
        foreach ($list as $item)
        {
            $companyId  = $item['company_id'];
            try {
                $company = new Company($this->userId, $companyId);
                $company->statistics();
            } catch ( \Exception $e) {
                \LogUtil::info('refresh statistics error company_id:'.$companyId);
                throw $e;
            }
        }
        \LogUtil::info('refresh statistics  end');
    }

	/**
	 * @throws CDbException
	 * @throws ProcessException
	 * @throws CException
	 */
	public function setFieldData($field, $data, $skipLimitCheck = false) {

        if (empty($field))
            return 0;

        $opUser = \User::getUserObject($this->userId);
        $clientId = $opUser->getClientId();

        (new \common\library\validation\Validator(
            [$field => $data],
            ["group_field:".implode(',', [$clientId, \Constants::TYPE_COMPANY, false, '', $field])]
        ))->validate();

        $this->paramsMapping();

        // 根据权限过滤
        if (!$this->listObject->getSkipPrivilege()) {
            [$companyIds, $this->noPermissionCompanyIds] = $this->listObject->fetchEditableObjIds([
                'private_company_privilege' => PrivilegeConstants::PRIVILEGE_CRM_COMPANY_EDIT,
                'public_company_privilege' => PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_EDIT,
            ], $field);
            if (!is_null($companyIds)) {
                if (!$companyIds) {
                    return 0;
                }
                $this->listObject->setCompanyIds($companyIds);
            }
        }

        [$where, $params] = $this->listObject->buildParams();

        $alias = empty($this->listObject->getAlias()) ? '' :$this->listObject->getAlias() . '.';

        $from = $this->listObject->getFrom();

        $fields = is_numeric($field) ? $alias . 'external_field_data' : $field;

        $sql = "SELECT {$alias}company_id,{$fields} $from $where";
        $companyData = $this->db->createCommand($sql)->queryAll(true, $params);

        if (empty($companyData))
            return 0;

        $companyIds = array_column($companyData, 'company_id');

		if($field == 'pool_id'){

			$client = Client::getClient($clientId);
			$detectKey = DuplicateConstants::MODULE_MAP[\Constants::TYPE_COMPANY]['detecting'];
			$ruleTypeKey = DuplicateConstants::MODULE_MAP[\Constants::TYPE_COMPANY]['rule_type'];
			$flag = (($client->getSettingAttributes()[$detectKey] ?? 0) == \common\library\duplicate\DuplicateConstants::DUPLICATE_CONFLICT_DETECTING) ? DuplicateFlagBuilder::DUPLICATE_ALL_RULE_TYPE : ($client->getSettingAttributes()[$ruleTypeKey] ?? 0);
			if ($flag == DuplicateFlagBuilder::DUPLICATE_POOL_ID_RULE_TYPE) {
				$failCompanyIds = Helper::getDuplicateFailCount($clientId, $opUser->getUserId(), $companyIds, $data);
				$this->failCount = count($failCompanyIds);
				$companyIds = array_diff($companyIds, $failCompanyIds);
			}
		}

		if (empty($companyIds)) {
			return 0;
		}

        $companyIdsSql = implode(',', $companyIds);

        $count = count($companyIds);
		if (!$skipLimitCheck && $count > self::MAX_UPDATE_FIELD_BATCH_OPERATOR_COUNT) {
            throw  new \RuntimeException(\Yii::t('customer', 'Selected customer') . $count . \Yii::t('customer', 'Do not support batch operations more than {num}', ['num' => self::MAX_UPDATE_FIELD_BATCH_OPERATOR_COUNT]));
        }

        $fieldListData = Helper::getBatchOperatorFieldList($clientId, $this->userId);
        $fieldIds = array_column($fieldListData, 'id');

        if (empty($fieldIds) || !in_array($field, $fieldIds)) {
            return 0;
        }

        $now = date('Y-m-d H:i:s');
        $set = '';
        $params = [];

        is_array($data) && array_walk($data, function (&$item) {
            
            $item = str_replace('\'', '\'\'', $item);
        });
        
        if (is_numeric($field)) {
            $key = array_search($field ,$fieldIds);
            $externalData = is_array($data) ? json_encode($data) : '"' . $data . '"';
            if ($fieldListData[$key]["field_type"] == CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT) {
                if (!is_array($data)) {
                    throw new \RuntimeException('Not an array type. The data format is incorrect !!!');
                }
                $set = "external_field_data = external_field_data || JSONB_BUILD_OBJECT(
                           '{$field}',
                           (ARRAY(SELECT UNNEST(TRANSLATE((NULLIF(external_field_data ->> '{$field}', '')) :: JSONB::TEXT, '[]', '{}')::TEXT[]))
                               ||
                               ARRAY(
                                   SELECT UNNEST(ARRAY [" . "'" . implode("','", $data) . "'" . "] :: TEXT[])
                                       EXCEPT
                                   SELECT UNNEST(TRANSLATE((NULLIF(external_field_data ->> '{$field}', '')) :: JSONB::TEXT, '[]', '{}')::TEXT[])
                           ))
                       )";
            } else {
                $set = "external_field_data = jsonb_set((case when (external_field_data = '[]') then '{}' else external_field_data end),'{{$field}}','$externalData',true)";
            }
        } else {
            switch ($field) {
                case 'pool_id':
                    $poolId = $data;
                    if (!(\common\library\customer\pool\Helper::userInPool($clientId, $this->userId, [$poolId]))) {
                        throw new \RuntimeException(\Yii::t('customer', 'No such high seas group permissions'));
                    }

                    $set = "pool_id=$poolId";
                    if ($this->userId) {
                        $set .= ",last_edit_user={$this->userId}";
                    }
                    break;

                case 'group_id':
                    $groupId = $data;
                    Helper::acceptUseGroup($clientId, $groupId, $this->userId, true);
                    Helper::checkSubGroupSelect($clientId, $groupId);

                    $set = "group_id={$groupId}";
                    if ($this->userId) {
                        $set .= ",last_edit_user={$this->userId}";
                    }
                    break;

                case 'country':
                    [$country, $province, $city, $timezone] = $data;
                    (new \common\library\validation\Validator(
                        compact('country', 'province', 'city', 'timezone'),
                        [
                            'country' => 'required|not_empty|string',
                            'province' =>'string',
                            'city' => 'string',
                            'timezone' => 'numeric'
                        ]
                    ))->validate();

                    $countryRegion = PgsqlUtil::formatBson([
                        'country' => $country,
                        'province' => $province,
                        'city' => $city,
                    ]);
                    $set = "country=:country, province=:province, city=:city, timezone=:timezone, country_region={$countryRegion}";
                    $params = [
                        ':country' => $country,
                        ':province' => $province,
                        ':city' => $city,
                        ':timezone' => is_numeric($timezone) ? $timezone : '',
                    ];
                    $data = $country;
                    break;
                case 'origin':
                    
                    throw new \RuntimeException('Unexpected field.');
                    
                    break;
                    
                case 'origin_list':
                    
                    $data = array_values(array_filter($data, 'is_numeric'));
        
                    if (empty($data)) {
            
                        throw new \RuntimeException('No valid value.');
                    }
    
                    $originIds = array_column((new OriginApi($clientId))->listAll(), 'item_id');
    
                    if (array_diff($data, $originIds)) {
                        
                        throw new \RuntimeException('No valid value.');
                    }
                    
                    $set = " origin_list = (origin_list::BIGINT[])
                               || ARRAY (
                                           SELECT *
                                                FROM (
                                                       SELECT UNNEST(ARRAY [" . implode(', ', $data) . "] :: BIGINT[])
                                                       EXCEPT
                                                       SELECT UNNEST(origin_list::BIGINT[])
                                                       ) t1(origin_list)
                                           ORDER BY array_position(ARRAY [" . implode(', ', $data) . "] :: BIGINT[], origin_list)
                                        ) ";
                    break;
                case 'product_group_ids':
                    //新传来的就判断是否存在父级或者子级
                    $api = new GroupApi($clientId,\Constants::TYPE_PRODUCT);
                    $subSet = array_diff(array_column($api->listRecursive($data) , 'id') , $data) ?: [];
                    $parentData = $api->listWithParent($data);
                    $superSet = [];
                    foreach ($parentData as $value){
                        foreach ($value as $v) {
                            $superSet[] = $v;
                        }
                    }
                    $superSet = array_diff(array_column($superSet , 'id') , $data) ?: [];

                    $productGroupIds = $data;
                    $set = "product_group_ids =
                            ARRAY(
                                     (SELECT UNNEST (
                                         (product_group_ids::BIGINT[]) ||
                                         ARRAY (
                                         SELECT UNNEST(ARRAY [" . implode(',', $productGroupIds) . "] :: BIGINT[])
                                         EXCEPT
                                         SELECT UNNEST(product_group_ids::BIGINT[])
                                         )
                                     )
                                     EXCEPT SELECT UNNEST(ARRAY [" . implode(',', $subSet) . "] :: BIGINT[]))
                                     EXCEPT (SELECT UNNEST(product_group_ids::BIGINT[])
                                     INTERSECT SELECT UNNEST(ARRAY [" . implode(',', $superSet) . "] :: BIGINT[]))
                                 )";
                    break;

                default:
                    $set = "{$field}=:data";
                    $params[':data'] = $data;
                    break;
            }

        }

        $history = new CompanyEditCompare($clientId);
        $diffFieldIds = $history->getCompareFields();

        // HISTORY
        $notChangedIds = [];
        if (in_array($field, $diffFieldIds) || is_numeric($field)) {
            $map = [];
            foreach ($companyData as $elem) {
                if (isset($elem[$field])) {
                    if ($data == $elem[$field]) {
                        $notChangedIds[] = $elem['company_id'];
                    } else {
                        $map[$elem['company_id']] = [
                            [
                                'id' => $field,
                                'base' => 1,
                                'new' => $data,
                                'old' => $elem[$field]
                            ]
                        ];
                    }
                } elseif (is_numeric($field)) {
                    $external_field_data = json_decode($elem['external_field_data'], true);
                    if (isset($external_field_data[$field])) {
                        if ($data == $external_field_data[$field]) {
                            $notChangedIds[] = $elem['company_id'];
                        } else {
                            $map[$elem['company_id']] = [
                                [
                                    'id' => $field,
                                    'base' => 0,
                                    'new' => $data,
                                    'old' => $external_field_data[$field]
                                ]
                            ];
                        }
                    } else {
                        $map[$elem['company_id']] = [
                            [
                                'id' => $field,
                                'base' => 0,
                                'new' => $data,
                                'old' => ''
                            ]
                        ];
                    }
                }
            }

            $history = new BatchCompanyBuilder();
            $history->buildByMap($clientId, $this->userId, CompanyHistoryPg::TYPE_EDIT_COMPANY, $map);
        }

        // 更新时间
        $updateCompanyIds = array_diff($companyIds, $notChangedIds);
        $updateCompanyIdsSql = implode(',', $updateCompanyIds);

        if (empty($updateCompanyIds)) {
            return $count;
        }

        $orderTimeSet = '';
        if (\CustomerOptionService::checkReference($clientId, 'refer_modified_data')) {
            $buildSet = Helper::buildUserData('user_data', $this->userId, ['order_time' => $now], 'refer_modified_data');
            $orderTimeSet = "order_time='$now', user_data = $buildSet";

            $sql = "update tbl_customer set $orderTimeSet where client_id=$clientId and company_id in ($updateCompanyIdsSql)  and is_archive=1;";
            $this->db->createCommand($sql)->execute();
        }

        if (!empty($orderTimeSet)) {
            $set .= ",{$orderTimeSet}";
        }

        $set .= ",update_time='{$now}'";
        $sql = "UPDATE tbl_company SET {$set} WHERE client_id={$clientId} AND company_id IN ($updateCompanyIdsSql) AND is_archive = 1 ";
        $count = $this->db->createCommand($sql)->execute($params);


        //记录行为
        $behavior = new BehaviorService($this->userId);
        $behavior->setType(BehaviorConstant::TYPE_CUSTOMER_EDIT);
        $behavior->setReferId($updateCompanyIds);

        SearchQueueService::pushCompanyQueue($this->userId, $clientId, $updateCompanyIds,\Constants::SEARCH_INDEX_TYPE_UPDATE);
        $this->runWorkflow($clientId, $updateCompanyIds, [$field, 'order_time']);
        $this->runPerformance($clientId, $this->userId, \Constants::TYPE_COMPANY, $updateCompanyIds);

        switch ($field) {
            case 'pool_id':
            case 'group_id':
                //待办上报
                $companyFollowUserIs = array_column($companyData,'user_id','company_id');
                $createFeedTime = date("Y-m-d H:i:s", time());
                Helper::batchSetnxUniqueFeeds($clientId, $this->userId, $companyFollowUserIs, ['create_time' => $createFeedTime], TodoConstant::TODO_TYPE_COMPANY_DATA_UPDATE);
                break;
        }

        // 公海分组字段更新，需要同步邮箱身份更新
        if ($field == 'pool_id' && !empty($updateCompanyIds)) {
            (new CustomerSync($clientId))->setFindCompanyId($updateCompanyIds)->sync();
        }

        return $count;
    }

    public function pin($type){

        $userId = !empty($userId) ? $userId : $this->userId;
        $user = \User::getUserObject($userId);
        $clientId = $user->getClientId();

        $this->paramsMapping();
        [$where,$params] = $this->listObject->buildParams();

        $alias = empty($this->listObject->getAlias()) ? '' :$this->listObject->getAlias() . '.';

        $from = $this->listObject->getFrom();

        $sql = "select {$alias}company_id $from $where";

        $companyIds = $this->db->createCommand($sql)->queryColumn($params);

        $count = count($companyIds);
        if ($count == 0){
            return 0;
        }
        if ($count > self::MAX_BATCH_OPERATOR_COUNT) {
            throw  new \RuntimeException(\Yii::t('customer', 'Selected customer') . $count . \Yii::t('customer', 'Do not support batch operations'));
        }


        \Pin::it($type,$companyIds);

        return $count;

    }

    public function unPin($type){

        $userId = !empty($userId) ? $userId : $this->userId;
        $user = \User::getUserObject($userId);
        $clientId = $user->getClientId();

        $this->paramsMapping();
        [$where,$params] = $this->listObject->buildParams();

        $alias = empty($this->listObject->getAlias()) ? '' :$this->listObject->getAlias() . '.';

        $from = $this->listObject->getFrom();

        $sql = "select {$alias}company_id $from $where";

        $companyIds = $this->db->createCommand($sql)->queryColumn($params);

        $count = count($companyIds);
        if ($count == 0){
            return 0;
        }
        if ($count > self::MAX_BATCH_OPERATOR_COUNT) {
            throw  new \RuntimeException(\Yii::t('customer', 'Selected customer') . $count . \Yii::t('customer', 'Do not support batch operations'));
        }


        \Pin::unpin($type,$companyIds);

        return $count;

    }

    public function batchCreate($set_params){

        $userId = !empty($userId) ? $userId : $this->userId;
        $user = \User::getUserObject($userId);
        $clientId = $user->getClientId();

        $this->paramsMapping();
        [$where,$params] = $this->listObject->buildParams();

        $alias = empty($this->listObject->getAlias()) ? '' :$this->listObject->getAlias() . '.';

        $from = $this->listObject->getFrom();

        $sql = "select {$alias}company_id $from $where";

        $companyIds = $this->db->createCommand($sql)->queryColumn($params);

        $count = count($companyIds);
        if ($count == 0){
            return 0;
        }
        if ($count > self::MAX_BATCH_OPERATOR_COUNT) {
            throw  new \RuntimeException(\Yii::t('customer', 'Selected customer') . $count . \Yii::t('customer', 'Do not support batch operations'));
        }


        if (empty($repeat_rule)) {
            $count = \common\library\schedule\Helper::batchCreateSchedule(
                $clientId,
                $userId,
                $set_params['title'],
                $set_params['color'],
                $set_params['participant_user_id'] ?? [],
                $set_params['start_time'],
                $set_params['end_time'],
                $set_params['full_day_flag'] ?? 0,
                $set_params['remind_time'] ?? [],
                $set_params['relation_mail_id'] ?? 0,
                $set_params['refer_type'] ?? 0,
                $companyIds ?? [],
                $set_params['remark'] ?? '',
                $set_params['attach_list'] ?? [],
                $set_params['image_list'] ?? []
            );
        } else {
            $count = \common\library\schedule\Helper::batchCreateRepeatSchedule(
                $clientId,
                $userId,
                $set_params['title'],
                $set_params['color'],
                $set_params['participant_user_id'] ?? [],
                $set_params['start_time'],
                $set_params['end_time'],
                $set_params['full_day_flag'] ?? 0,
                $set_params['remind_time'] ?? [],
                $set_params['relation_mail_id'] ?? 0,
                $set_params['refer_type'] ?? 0,
                $companyIds ?? [],
                $set_params['remark'] ?? '',
                $set_params['attach_list'] ?? [],
                $set_params['image_list'] ?? [],
                $set_params['repeat_rule'] ?? []
            );
        }

        return $count;

    }
    
    public function assignPoolByUser(int $pool_id = null) {
        
        $user = \User::getLoginUser();
        
        $this->paramsMapping();
        
        $this->listObject->setUserNum([1]);
        
        $this->listObject->setShowAll(1);
        
        $this->listObject->setSkipPrivilege(1);
        
        [$where, $params] = $this->listObject->buildParams();
        
        $alias = empty($this->listObject->getAlias()) ? '' : $this->listObject->getAlias() . '.';

        $from = $this->listObject->getFrom();
        
        $sql = "select {$alias}company_id, {$alias}pool_id, {$alias}user_id $from $where";
        
        $companyList = $this->db->createCommand($sql)->queryAll(true, $params);
        
        $count = count($companyList);
        
        if ($count == 0) {
            
            return 0;
        }
        
        if ($count > ConflictConstants::RESOLVE_MAX_COUNT) {
            
            throw  new \RuntimeException(\Yii::t('customer', 'Selected customer') . $count . \Yii::t('customer', 'Do not support batch operations'));
        }
        
        array_walk($companyList, function (&$item) {
            
            $item['user_id'] = (array)PgsqlUtil::trimArray($item['user_id']);
        });
        
        $userLastPoolMap = \common\library\customer\pool\Helper::getUserLastPoolMap($user->getClientId(), array_merge(...array_values(array_column($companyList, 'user_id'))));
        
        $poolCompanyMap = [];
        
        foreach ($companyList as $item) {
            
            if (count((array)$item['user_id']) != 1) {
                
                continue;
            }
    
            $lastPool = $pool_id ?? $userLastPoolMap[$item['user_id'][0]] ?? 0;
            
            $poolCompanyMap[$lastPool][] = $item['company_id'];
        }
        
        foreach ($poolCompanyMap as $poolId => $companyIds) {
            
            $this->setParams(['company_ids' => $companyIds, 'show_all' => 1, 'user_num' => [1]]);
            
            $this->setPoolId($poolId);
        }
        
        $successCompanyIds = array_merge(...array_values($poolCompanyMap));
    
        $successCompanyIds && $result = (new ConflictService($user->getClientId()))->detectByRefer($successCompanyIds, ConflictConstants::RESOLVE_TYPE_COMPANY_CONFLICT_ASSIGN_POOL_BY_USER);
    
        \LogUtil::info('[CONFLICT_ASSIGN_POOL_BY_USER]', [
            'poolCompanyMap'    => $poolCompanyMap,
            'successCompanyIds' => $successCompanyIds,
            'result'            => $result ?? [],
        ]);
    
        return $result['resolveReferId'] ?? [];
    }
    
    
    public function copyToOthers(int $companyId, array $userId) {
        
        if (empty($companyId) || empty($userId)) {
            
            return;
        }
        
        $operator = \User::getLoginUser();
        
        $clientId = $operator->getClientId();
    
    
        $list = new UserList();
    
        $list->setClientId($clientId);
    
        $list->setEnableFlag(UserInfo::ENABLE_TRUE);
    
        $list->setFields(['user_id', 'nickname']);
        
        $userList = $list->find();
    
        $userId = array_values(array_intersect($userId, array_column($userList, 'user_id')));
        
    
        try {
            
            $fieldList = new FieldList($clientId);
     
            $fieldList->setBase(1);
            
            $fieldList->setType([\Constants::TYPE_COMPANY, \Constants::TYPE_CUSTOMER]);
        
            $fieldList->setExcludeId(['next_follow_up_time', 'serial_id', 'cus_tag']);
            
            $fieldList->setIgnoreFormat(1);
            
            $fieldMap = [];
        
            foreach ($fieldList->find() as $item) {
            
                $fieldMap[$item['type']][] = $item['id'];
            }
        
            if (count($fieldMap) != 2) {
            
                return;
            }
        
            $company = new \common\library\customer_v3\company\orm\Company($clientId, $companyId);
        
            if (!$company->isExist()) {
            
                \LogUtil::info('origin company not exist', ['companyId' => $companyId, 'clientId' => $clientId]);
            }
        
        
            $companyInfo = $company->getAttributes(array_merge(['external_field_data', 'tag', 'client_tag_list'], $fieldMap[\Constants::TYPE_COMPANY]));
            
        
            $customerList = new CustomerList($clientId);
        
            $customerList->setCompanyId($companyId);
        
            $customerList = $customerList->find();
        
            array_walk($customerList, function (&$item) use ($fieldMap) {
            
                $item = array_intersect_key($item, array_fill_keys(array_merge(['external_field_data'], $fieldMap[\Constants::TYPE_CUSTOMER]), ''));
        
                $item['external_field_data'] = array_column($item['external_field_data'], 'value', 'id');
            });
            

//        客户池交集有且仅有一个，直接新建；否则，以最近选择聚合
            $poolUserMap = [];


			$userLastPoolMap = array_replace(array_fill_keys($userId, 0), \common\library\customer\pool\Helper::getUserLastPoolMap($clientId, $userId));

			foreach ($userLastPoolMap as $user => $poolId) {

				$poolUserMap[$poolId][] = $user;
			}
        
            $toCompany = [];
        
            foreach ($poolUserMap as $poolId => $owner) {
            
                $company = new Company($clientId);
            
                $company->setSkipDuplicateCheck(1);
                
                $company->setSkipAllGroupCheck(1);
                
                $company->setSkipPrivilegeField(1);
                
                $company->setAllowDuplicateName(1);
                
                $company->setAllowDuplicateTel(1);
                
                $company->setSkipAllNameCheck(1);
            
                $company->setOperatorUserId($operator->getUserId());
            
                $company->setAttributes($companyInfo);
            
                $company->addUser($owner);
            
                $company->pool_id = $poolId;
            
                $customerObjList = array_map(function ($item) use ($clientId) {
                
                    $customer = new Customer($clientId);
                
                    $customer->setAttributes($item);
                
                    $customer->setSkipDuplicateCheck(1);
                
                    $customer->setSkipEmailValidate(1);
                    
                    $customer->setSkipPrivilegeField(1);
                    
                    $customer->setAllowDuplicateTel(1);
                
                    return $customer;
                
                }, $customerList);
            
                try {
                
                    $company->setCustomerList($customerObjList);
                
                    $company->save();
                
                    $toCompany[] = $company->company_id;
                
                } catch (\Throwable $throwable) {
    
                    \LogUtil::error('CONFLICT_COPY_COMPANY_SAVE_FAIL', [
                        'fromCompany' => $companyId,
                        'fromUserId'  => $userId,
                        'poolUserMap' => $poolUserMap,
                        'company'     => $company->getAttributes(),
                        'customer'    => $customerList,
                        'msg'         => $throwable->getMessage(),
                        'trace'       => $throwable->getTraceAsString(),
                    ]);
    
                    continue;
                }
            }
        } catch (\Throwable $throwable) {
    
    
            \LogUtil::error('CONFLICT_COPY_FAIL', [
                'fromCompany' => $companyId,
                'fromUserId'  => $userId,
                'toCompany'   => $toCompany ?? [],
                'poolUserMap' => $poolUserMap ?? [],
                'msg'         => $throwable->getMessage(),
                'trace'       => $throwable->getTraceAsString(),
            ]);
    
            return;
        }
    
        \LogUtil::info('CONFLICT_COPY_DONE', [
            'fromCompany' => $companyId,
            'fromUserId'  => $userId,
            'toCompany'   => $toCompany,
            'poolUserMap' => $poolUserMap,
        ]);
    }
}
