<?php
/**
 * Created by PhpS<PERSON><PERSON>.
 * User: andy
 * Date: 2017/4/10
 * Time: 11:12
 */

namespace common\library\customer;

use common\library\account\Client;
use common\library\account\external\UserInfoExternal;
use common\library\ai\service\EventsReport;
use common\library\ai\service\RecommendService;
use common\library\alibaba\AlibabaService;
use common\library\alibaba\customer\AlibabaCustomerRelationList;
use common\library\APIConstant;
use common\library\cache\ClassCacheRepository;
use common\library\CompanyQuotaManager;
use common\library\custom_field\company_field\CompanyField;
use common\library\custom_field\company_field\CustomerField;
use common\library\custom_field\company_field\duplicate\FieldUniqueValidator;
use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldList;
use common\library\customer\field_unique\DuplicateFlagBuilder;
use common\library\customer\field_unique\EmailUnique;
use common\library\customer\rule_config\RuleConfigConstants;
use common\library\customer\service\AccessService;
use common\library\customer\service\LeadAccessService;
use common\library\customer_v3\common\BaseList;
use common\library\customer_v3\company\CompanyConstants;
use common\library\customer_v3\company\list\CompanyList;
use common\library\customer_v3\company\orm\Company;
use common\library\customer_v3\company\orm\CompanyFilter;
use common\library\customer_v3\customer\CustomerList;
use common\library\customer_v3\customer\orm\Customer;
use common\library\discovery\api\ContactsList;
use common\library\discovery\api\Employee;
use common\library\duplicate\DuplicateConstants;
use common\library\edm\EdmTask;
use common\library\email_identity\EmailIdentity;
use common\library\field\Constant;
use common\library\lead\Lead;
use common\library\lead\LeadCustomerList;
use common\library\lead\LeadList;
use common\library\object\field\field_setting\CompanyFieldSetting;
use common\library\object\field\field_setting\CustomerFieldSetting;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeFieldV2;
use common\library\privilege_v3\PrivilegeService;
use common\library\product_v2\ProductAPI;
use common\library\product_v2\ProductConstant;
use common\library\product_v2\sku\SkuAPI;
use common\library\push\Browser;
use common\library\queue_v2\job\LeadsCompanySearchSyncJob;
use common\library\queue_v2\job\PerformanceV2RecordJob;
use common\library\queue_v2\job\TipsPushTodoJob;
use common\library\queue_v2\QueueService;
use common\library\search\SearchApi;
use common\library\search\SearchConstant;
use common\library\setting\library\cost_invoice\CostInvoiceFilter;
use common\library\setting\library\fund\FundFilter;
use common\library\setting\library\group\GroupApi;
use common\library\setting\library\origin\OriginApi;
use common\library\setting\library\status\Status;
use common\library\setting\library\swarm\SwarmApi;
use common\library\setting\user\UserSetting;
use common\library\sns\customer\CustomerContactHelper;
use common\library\sns\customer\UserCustomerContactMessageList;
use common\library\statistics\CustomerHelper;
use common\library\statistics\util\DateUtil;
use common\library\swarm\SwarmService;
use common\library\todo\TodoConstant;
use common\library\trail\CompanyDynamicList;
use common\library\trail\events\RemarkEvents;
use common\library\trail\TrailConstants;
use common\library\util\PgsqlUtil;
use common\library\util\SqlBuilder;
use common\library\util\TelUtil;
use common\library\util\XlsUtil;
use common\library\workflow\WorkflowConstant;
use common\modules\app\components\AppInfoService;
use CompanyFileService;
use Constants;
use CustomerOptionService;
use EmailUtil;
use LogUtil;
use protobuf\Common\DeviceType;
use protobuf\Common\PBHeader;
use function foo\func;


class Helper
{

    const CUSTOMER_EXPORT = 'customer_export';
    const COMPANY_POOL_FIELDS = ['public_type', 'public_reason'];
    public static $scene = '';



	public static $fieldSettings = [

		Constants::TYPE_COMPANY  => [
			'tag'               => '客户标签',
			'origin_name'       => '客户来源',
			'group_name'        => '客户分组名称',
			'pool_name'         => '公海分组名称',
			'trail_status_name' => '客户状态',
		],

		Constants::TYPE_CUSTOMER => [
			'name' => '主要联系人',
		],
	];


    public static $transferCompanyField = [
        'cus_tag'         => 'tag',
        'origin'          => 'origin_name',
        'group_id'        => 'group_name',
        'pool_id'         => 'pool_name',
        'trail_status'    => 'trail_status_name',
    ];

    const TRANS_FIELD_MAP = [
        Constants::TYPE_COMPANY => [
            'cus_tag'         => 'tag',
            'origin'          => 'origin_name',
            'group_id'        => 'group_name',
            'pool_id'         => 'pool_name',
            'trail_status'    => 'trail_status_name',
        ],
        Constants::TYPE_COMPANY_POOL => [
            'cus_tag'         => 'tag',
            'origin'          => 'origin_name',
            'group_id'        => 'group_name',
            'pool_id'         => 'pool_name',
            'trail_status'    => 'trail_status_name',
        ],
        \Constants::TYPE_CUSTOMER => [

        ],
        Constants::TYPE_LEAD => [
            'cus_tag'         => 'tag',
            'origin'          => 'origin_name',
            'group_id'        => 'group_name',
            'pool_id'         => 'pool_name',
            'trail_status'    => 'trail_status_name',
        ],
        Constants::TYPE_LEAD_POOL => [
            'cus_tag'         => 'tag',
            'origin'          => 'origin_name',
            'group_id'        => 'group_name',
            'pool_id'         => 'pool_name',
            'trail_status'    => 'trail_status_name',
        ],
        \Constants::TYPE_LEAD_CUSTOMER => [

        ],
        Constants::TYPE_OPPORTUNITY => [
            'origin'      => 'origin_name',
            'type'        => 'type_name',
            'main_user'   => 'main_user_info.nickname',
            'handler'     => 'handler_info.nickname',
            'fail_type'   => 'fail_type_name',
            'create_user' => 'create_user_info.nickname',
            'company_id'  => 'company.name',
            'customer_id' => 'customer.name',
            'flow_id'     => 'sale_flow_name',
            'stage'       => 'stage_info.name',
        ],
        Constants::TYPE_CASH_COLLECTION => [
            'company_id'     => 'company_info.name',
            'order_id'       => 'order_info.name',
            'opportunity_id' => 'opportunity_info.name',
        ],
    ];

    const COMPANY_EMAIL_DYNAMIC_ADJUST_ENABLE = 1; //显示调整动态按钮
    const COMPANY_EMAIL_DYNAMIC_ADJUST_DISABLE = 0;//隐藏调整动态按钮


    const VERSION_V3 = 'v3';

	/**
     * 有静态缓存
     * @param $clientId
     * @param $customerId
     * @return Customer
     */
    public static function getCustomer($clientId, $customerId)
    {
        static $map = [];

        $key = $clientId . '_' . $customerId;

        if (!array_key_exists($key, $map))
        {
            $map[$key] = new Customer($clientId, $customerId);
        }

        return $map[$key];
    }

    public static function iconInfo($clientId, $userId, $email)
    {
        $external = [];
        $external['sender_company'] = null;
        $external['sender_company_type'] = 0;
        $external['sender_contact'] = null;

        $customer = new \common\library\customer_v3\customer\orm\Customer($clientId);
        $targetCustomerId = Helper::loadCustomerIdByEmail($clientId, $userId, $email);
        if ($targetCustomerId) {
            $customer->loadById($targetCustomerId);
        }

        $isCustomer = false;

        if ($customer->isExist() && $customer->company_id)
        {
            $isCustomer = true;

            $external['sender_company'] = 1;

            $userIds = $customer->user_id;

            if (in_array($userId, $userIds))
                $external['sender_company_type'] = Customer::TYPE_MINE;
            elseif (empty($userIds))
                $external['sender_company_type'] = Customer::TYPE_PUBLIC;
            else
                $external['sender_company_type'] = Customer::TYPE_COLLEAGUE;
        }

        if (!$isCustomer)
        {
            $contact = \common\library\contact\Helper::findByEmail($clientId, $userId, $email);
            if(!empty($contact))
                $external['sender_contact'] = $contact->getAttributes();
        }

        return $external;
    }

    public static function getCompanyName($companyId, $userId)
    {
        $db = \PgActiveRecord::getDbByUserId($userId);
        $name = '';
        if($db) {
            $name = $db->createCommand("select name from tbl_company where company_id=:company_id")->queryScalar([':company_id'=>$companyId]);
        }
        return $name ? $name : '';
    }

    public static function getOtherCustomer($clientId, $userId, $emailList)
    {
        $emailList = \EmailUtil::findAllMailAddress($emailList);

        if (!$emailList) return [];

        $db = \PgActiveRecord::getDbByClientId($clientId);

        $emailList = array_map(function ($item){
            return \Util::escapeDoubleQuoteSql($item);
        },$emailList);

        $emailStr = '\'' . implode("','",$emailList) . '\'';

        $query = "select email,user_id,company_id from tbl_customer where client_id=:client_id and is_archive=1 and lower(email) in ($emailStr) and user_id!='{}';";
        $data = $db->createCommand($query)->queryAll(true, [':client_id' => $clientId]);

        $companyIdStr = implode(',', array_column($data, 'company_id'));
        if($companyIdStr){
            $query = "select company_id, pool_id from tbl_company where client_id=:client_id and is_archive=1 and company_id in ($companyIdStr) and user_id!='{}';";
            $companies = $db->createCommand($query)->queryAll(true, [':client_id' => $clientId]);
            $companyId2poolIdMap = array_column($companies,'pool_id','company_id');
        }

        $allEmails = [];
        $privateEmails = [];

        $client = Client::getClient($clientId);
        $detectKey = DuplicateConstants::MODULE_MAP[\Constants::TYPE_COMPANY]['detecting'];
        $ruleTypeKey = DuplicateConstants::MODULE_MAP[\Constants::TYPE_COMPANY]['rule_type'];
        // 是否开启客户池判重
        $duplicateSwitchFlag = (($client->getSettingAttributes()[$detectKey] ?? 0) == DuplicateConstants::DUPLICATE_CONFLICT_DETECTING) ? DuplicateFlagBuilder::DUPLICATE_ALL_RULE_TYPE : ($client->getSettingAttributes()[$ruleTypeKey] ?? 0);
        if ($duplicateSwitchFlag) {
            $poolList = \common\library\customer\pool\Helper::getCustomerPoolList();
            $poolList = array_column($poolList, 'pool_id');
            foreach ($data as $item) {
                $email = $item['email'];
                $companyId = $item['company_id'] ?? null;
                $allEmails[] = $email;
                // 获取：本人所属分组下跟进的客户邮箱 || 不同分组内的，同事跟进的客户邮箱
                if (isset($companyId2poolIdMap[$companyId]) && (
                    !in_array($companyId2poolIdMap[$companyId], $poolList) || (
                        in_array($companyId2poolIdMap[$companyId], $poolList) && in_array($userId, PgsqlUtil::trimArray($item['user_id']))
                    )))
                    $privateEmails[] = $email;
            }
            // 获得，相同分组内，非本人跟进的客户邮箱（不包含与我跟进的客户邮箱重复的）
            $conflictEmails = array_diff($allEmails, $privateEmails);
            return array_unique($conflictEmails);
        }

        // 对于同一个邮箱在私海和同事私海都存在的情况,不算做同事邮箱
        foreach ($data as $item)
        {
            $email = $item['email'];
            $userIds = PgsqlUtil::trimArray($item['user_id']);
            $allEmails[] = $email;

            if (in_array($userId, $userIds)) $privateEmails[] = $email;
        }
        return array_diff($allEmails, array_unique($privateEmails));
    }

    public static function stripFormat($data)
    {
        $result = [];
        foreach ($data as $groupData)
        {
            $fields = $groupData['fields'];
            foreach ($fields as $field)
            {
                if (empty($field) || !isset($field['id'])) continue;
                if ($field['base'] == 1 )
                {
                    if ($field['id'] == 'tel')
                    {
                        $result['tel'] = $field['value']['tel'] ?? '';
                        $result['tel_area_code'] = $field['value']['tel_area_code'] ?? '';
                    }
                    elseif ($field['id'] == 'image_list')
                    {
                        $result['image_list'] = [];
                        if (!empty($field['value']) && is_array($field['value']))
                        {
                            foreach ($field['value'] as $image)
                            {
	                            if (is_array($image)) {
		                            $result['image_list'][] = $image['file_id'] ?? 0;
	                            } else {
		                            $result['image_list'][] = intval($image);
	                            }
                            }
                        }
                        $result['image_list'] = array_filter($result['image_list']);
                    }
                    elseif ('cus_tag' == $field['id'])
                    {
                        $result['cus_tag'] = [];
                        if (!empty($field['value']) && is_array($field['value']))
                        {
                            foreach ($field['value'] as $tag)
                            {
                                if (is_array($tag)) {
                                    $result['cus_tag'][] = $tag['tag_id'] ?? 0;
                                } else {
                                    $result['cus_tag'][] = intval($tag);
                                }
                            }
                        }
                        $result['cus_tag'] = array_filter($result['cus_tag']);
                    }
                    elseif ('email' == $field['id'])
                    {
                        $result[$field['id']] = strtolower(trim($field['value']));
                    }
                    elseif ('lonlat' == $field['id'])
                    {
                        $result['longitude'] = isset($field['value']['longitude']) ? $field['value']['longitude'] : 0;
                        $result['latitude'] = isset($field['value']['latitude']) ? $field['value']['latitude'] : 0;
                    }
                    else
                    {
                        $value = $field['value'] ?? '';
                        if (is_string($value)) $value = trim($value);
                        $result[$field['id']] = $value;
                    }
                }
                else
                {
                    $value = $field['value'] ?? '';
                    if (is_string($value)) $value = trim($value);
                    $result['external_field_data'][$field['id']] = $value;
                }
            }
        }

        return $result;
    }

    public static function edmCheck($clientId, $userId, $filter, array $emailList, &$nicknameMap, &$customerData, &$leadCustomerData, &$detailMsg)
    {
        $escapeEmailList = array_map(function ($item) {return \Util::escapeDoubleQuoteSql($item);}, $emailList);

        if ($filter == EdmTask::CUSTOMER_FILTER_TYPE_NOT_ARCHIVE)
        {
            $sql = "select email from tbl_customer where client_id=:client_id and is_archive=1 and lower(email) in ";
            $sql .= '(\'' . implode('\',\'', $escapeEmailList) . '\')';

            $db = \PgActiveRecord::getDbByClientId($clientId);

            $customerList = $db->createCommand($sql)->queryColumn([':client_id' => $clientId]);

            $afterFilter = count($customerList);

            if ($afterFilter)
            {
                $detailMsg .= (" 筛选未建档返还：" . $afterFilter);

                $emailList = array_values(array_diff($emailList, $customerList));
            }
        }

        // 线索邮件动态前置条件：检查是否有线索记录匹配
        $customerList = new \common\library\lead\LeadCustomerList($clientId);
        $customerList->setEmail($emailList);
        $customerList->setFields(['is_archive', 'customer_id', 'company_id', 'lead_id', 'name', 'email', 'user_id']);
        //$customerList->setUserId($userId);
        $customerList->setIsArchive(\common\library\lead\Lead::ARCHIVE_OK);
        $data = $customerList->find();
        foreach ($data as $item) {
            if (empty($item['company_id'])) {
                $leadCustomerData[$item['lead_id']][] = $item['customer_id'];
            }
        }

        if ($filter == EdmTask::CUSTOMER_FILTER_TYPE_NOT_ARCHIVE) {
            return $emailList;
        }

        $leadCustomerNameMap = array_column($data, 'name', 'email');
        foreach ($escapeEmailList as $email)
        {
            if (isset($leadCustomerNameMap[$email]))
            {
                $nicknameMap[$email] = $leadCustomerNameMap[$email];
            }
        }

        // 判断client是否设置了不允许发给同事客户，如果设置了，需要在这一步过滤
        $client = Client::getClient($clientId);
        $setting = $client->getSettingAttributes([Client::SETTING_KEY_EDM_PREVENT_CUSTOMER_CONFLICT]);
        $filterConflict = false;
        $data = [];
        if ($setting[Client::SETTING_KEY_EDM_PREVENT_CUSTOMER_CONFLICT])
        {
            $filterConflict = true;
            if ($filter == EdmTask::CUSTOMER_FILTER_TYPE_ALL)
                $data = array_combine($emailList, array_fill(0, count($emailList), 1));
        }

        $sql = "select A.customer_id,A.name,A.email,A.company_id,B.group_id,B.country,B.star,B.origin,B.score->'total' as score, B.user_id from tbl_customer as A left join tbl_company as B on A.company_id=B.company_id where A.client_id=$clientId and A.is_archive=1 and lower(A.email) in ";
        $sql .= '(\'' . implode('\',\'', $escapeEmailList) . '\') order by B.order_time desc';

        $db = \PgActiveRecord::getDbByClientId($clientId);

        $customerList = $db->createCommand($sql)->queryAll();

        $ignoreConflictCount = 0;

        foreach ($customerList as $elem)
        {
            $email = $elem['email'];
            $userIds = PgsqlUtil::trimArray($elem['user_id']);

            if ($filterConflict)
            {
                if (!empty($userIds) && !in_array($userId, $userIds))
                {
                    ++$ignoreConflictCount;
                    if ($filter == EdmTask::CUSTOMER_FILTER_TYPE_ALL)
                    {
                        unset($data[$email]);
                    }
                    continue;
                }
            }


            if (!empty($userIds) && in_array($userId, $userIds) && !isset($customerData[$email])) {
                $customerData[$email] = $elem;
            }
            $nicknameMap[$email] = empty($elem['name']) ? '' : $elem['name'];
        }


        if ($ignoreConflictCount)
        {
            $detailMsg .= (' 防冲突返还：' . $ignoreConflictCount);
            \LogUtil::info("remove customer conflict count " . $ignoreConflictCount);
        }

        if ($filter == EdmTask::CUSTOMER_FILTER_TYPE_ALL)
        {
            return $filterConflict ? array_keys($data) : $emailList;
        }
        else
        {
            $total = count($emailList);
            $afterFilter = count($customerData);

            if ($total > $afterFilter)
                $detailMsg .= (" 筛选已建档返还：" . ($total - $afterFilter));

            return array_keys($customerData);
        }
    }

    public static function getCompanyCountData($clientId, $userId)
    {

        $companyQuotaManager = new CompanyQuotaManager($clientId);
        return $companyQuotaManager->getCompanyCountData($userId);

//        $pg = \PgActiveRecord::getDbByClientId($clientId);
//
//        // 去除client_id提升性能
//        $total = $pg
//            ->createCommand("select count(1) from tbl_company where is_archive=1 and user_id @> ARRAY[:user_id]::bigint[];")
//            ->queryScalar([':user_id'=>$userId]);
//        //todo 等于会回表，性能待优化
//        $exclusive = $pg
//            ->createCommand("select count(1) from tbl_company where client_id=:client_id and is_archive=1 and user_id='{{$userId}}';")
//            ->queryScalar([':client_id'=>$clientId]);
//
//        $share = $total - $exclusive;
//        $shareQuota = ceil($share * 0.5);
//
//        return [
//            'total_count' => $total,
//            'share_count' => $share,
//            'no_share_count' => $exclusive,
//            'total_quota' => $shareQuota + $exclusive,
//            'share_quota' => $shareQuota,
//            'no_share_quota' => $exclusive
//        ];
    }

    /**
     * boss端删除状态时，将被删除的状态设置为0
     * @param $userId
     * @param array $oldStatus
     * @param $newStatus
     * @return int
     */
    public static function updateAllStatus($clientId, $userId, array $oldStatus, $newStatus) {

        if (empty($oldStatus) || empty($userId)) {

            return;
        }

        $operator = new CompanyBatchOperator($userId);

        $operator->getList()->showAll(true, PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW);

        $operator->getList()->setSkipPrivilege(true);

        $operator->setParams([

            'status_id' => $oldStatus,
            'show_all' => 1,
        ]);

        return $operator->setFieldData('trail_status', $newStatus, true);
    }

    //更新来源
    public static function updateAllOrigin($clientId, $userId, array $oldOrigin, $newOrigin) {

        if (empty($oldOrigin) || empty($userId)) {

            return;
        }
        
        $db = \PgActiveRecord::getDbByClientId($clientId);
        
        //更新客户来源字段
        $db->createCommand("update tbl_company set origin_list = ARRAY(
                                SELECT UNNEST(origin_list)
                                EXCEPT
                                SELECT UNNEST(ARRAY [" . (implode(',', $oldOrigin).($newOrigin > 0 ? (','.$newOrigin) : '')) . "] :: BIGINT[])
                                ) ".($newOrigin > 0 ? " || ARRAY[{$newOrigin}] :: BIGINT[] " :'')."
                                WHERE  is_archive = 1 AND client_id = $clientId and origin_list && ARRAY[" . implode(',', $oldOrigin) . "] :: BIGINT[] ")->execute();
        //更新线索来源字段
        $db->createCommand("update tbl_lead set origin_list = ARRAY(
                                SELECT UNNEST(origin_list)
                                EXCEPT
                                SELECT UNNEST(ARRAY [" . (implode(',', $oldOrigin).($newOrigin > 0 ? (','.$newOrigin) : '')) . "] :: BIGINT[])
                                ) ".($newOrigin > 0 ? " || ARRAY[{$newOrigin}] :: BIGINT[] " :'')."
                                WHERE  is_archive > 0 AND client_id = $clientId and origin_list && ARRAY[" . implode(',', $oldOrigin) . "] :: BIGINT[] ")->execute();
        //更新商机来源字段
        $db->createCommand("update tbl_opportunity set origin_list = ARRAY(
                                SELECT UNNEST(origin_list)
                                EXCEPT
                                SELECT UNNEST(ARRAY [" . (implode(',', $oldOrigin).($newOrigin > 0 ? (','.$newOrigin) : '')) . "] :: BIGINT[])
                                ) ".($newOrigin > 0 ? " || ARRAY[{$newOrigin}] :: BIGINT[] " :'')."
                                WHERE  enable_flag = 1 AND client_id = $clientId and origin_list && ARRAY[" . implode(',', $oldOrigin) . "] :: BIGINT[] ")->execute();

        try{

            $newOrigin = strval($newOrigin);

            //更新客户,线索,商机 筛选条件
            self::modifyUserSettingFilterOrigin($clientId,$oldOrigin, $newOrigin);

            //更新工作流筛选，动作的来源
            self::modifyWorkflowOrigin($clientId,$oldOrigin, $newOrigin);

            //更新自定义报表筛选来源
            self::modifyReportOrigin($clientId,$oldOrigin, $newOrigin);

            //更新审批流配置来源
            self::modifyApprovalOrigin($clientId,$oldOrigin, $newOrigin);

        }catch (\Exception $exception ){
            \LogUtil::error("[updateOriginError:[client_id:{$clientId}] msg:{$exception->getMessage()}  code:{$exception->getCode()} trace:{$exception->getTraceAsString()} newOrigin:{$newOrigin} oldOrigin:".json_encode($oldOrigin));
        }

    }

    //更新自定义报表筛选来源
    public static function modifyReportOrigin($clientId,$oldOrigin, $newOrigin){

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $sql = "select report_key,query_config from tbl_report where client_id = {$clientId} and enable_flag = 1";
        $list = $db->createCommand($sql)->queryAll();
        if(!$list){
            return false;
        }
        $queryFieldKey = [
            'company.origin_list',
            'lead.origin_list',
            'opportunity.origin_list',
        ];
        foreach ($list as $item){
            $updateFlag = false;
            $queryConfig = json_decode($item['query_config'],true);
            foreach ($queryConfig as  &$queryValue){
                if(isset($queryValue['field']) &&  in_array($queryValue['field'],$queryFieldKey) && isset($queryValue['value']) && $queryValue['value']){
                    if(is_array($queryValue['value'])){
                        foreach ($queryValue['value'] as $originIndex => $originValue){
                            if(in_array($originValue,$oldOrigin)){
                                $updateFlag =true;
                                if ($newOrigin > 0) {
    
                                    $queryValue['value'][$originIndex] = $newOrigin;
                                }else{
        
                                    unset($queryValue['value'][$originIndex]);
                                }
                                $item['query_config'] = json_encode($queryConfig);
                            }
                        }
                    }else if(in_array($queryValue['value'],$oldOrigin)){
                        $updateFlag = true;
                        $queryValue['value'] = $newOrigin;
                        $item['query_config'] = json_encode($queryConfig);
                    }
                }
            }
            if($updateFlag && $item['query_config'] ){
                $sql = "update tbl_report  set query_config='{$item['query_config']}'  where client_id = {$clientId} and report_key = '{$item['report_key']}'";
                $res = $db->createCommand($sql)->execute();
                \LogUtil::info("[modifyReportOrigin:[client_id:{$clientId}] report_key:{$item['report_key']} res:{$res} newOrigin:{$newOrigin} oldOrigin:".json_encode($oldOrigin));
            }
        }

    }

    //更新客户,线索,商机 筛选条件
    public static function modifyUserSettingFilterOrigin($clientId, array $oldOrigin, $newOrigin){

        $db = \ProjectActiveRecord::getDbByClientId($clientId);
        $keyList = [
            \common\library\setting\user\UserSetting::CUSTOMER_PRIVATE_FILTER,
            \common\library\setting\user\UserSetting::CUSTOMER_PUBLIC_FILTER,
            \common\library\setting\user\UserSetting::OPPORTUNITY_FILTER,
            \common\library\setting\user\UserSetting::LEAD_PUBLIC_FILTER,
            \common\library\setting\user\UserSetting::LEAD_PUBLIC_FILTER,
            \common\library\setting\user\UserSetting::LEAD_PRIVATE_FILTER,
            \common\library\setting\user\UserSetting::LEAD_PUBLIC_FILTER,
        ];

        $sql = "select * from tbl_user_setting where client_id = {$clientId} and `key`  in('" . join("','", array_values($keyList) ) . "')";
        $list = $db->createCommand($sql)->queryAll();
        if(!$list){
            return false;
        }
        foreach ($list as $item){
            $updateFlag = false;
            $value = json_decode($item['value'],true)??[];
            foreach ($value as $sonIndex => &$sonValue){
                if(isset($sonValue['value']['origin_list']) && $sonValue['value']['origin_list']){
                    //修改来源
                    if(is_array($sonValue['value']['origin_list'])){
                        foreach ($sonValue['value']['origin_list'] as $originIndex => $originValue){
                            if(in_array($originValue,$oldOrigin)){
                                $updateFlag =true;
                                if ($newOrigin > 0) {
    
                                    $sonValue['value']['origin_list'][$originIndex] = $newOrigin;
                                }else{
        
                                    unset($sonValue['value']['origin_list'][$originIndex]);
                                }
                                
                                $item['value'] = json_encode($value);

                            }
                        }
                    }else if(in_array($sonValue['value']['origin_list'],$oldOrigin)){
                        $updateFlag = true;
                        $sonValue['value']['origin_list'] = $newOrigin;
                        $item['value'] = json_encode($value);
                    }
                }
            }
            if($updateFlag && $item && $item['value']){
                $data = json_encode($item['value']);
                $sql = "update tbl_user_setting  set value={$data}  where client_id = {$clientId} and `key` = '{$item['key']}'";
                $res = $db->createCommand($sql)->execute();
                \LogUtil::info("[modifyFilterOrigin:[client_id:{$clientId}] key:{$item['key']} res:{$res} newOrigin:{$newOrigin}  oldOrigin:".json_encode($oldOrigin));
            }
        }
    }

    //更新工作流筛选，动作的来源
    public static function modifyWorkflowOrigin($clientId, array $oldOrigin, $newOrigin){

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $sql = "select rule_id,filters,handlers from tbl_workflow_rule where client_id = {$clientId} and enable_flag = 1";
        $list = $db->createCommand($sql)->queryAll();
        if(!$list){
            return false;
        }
        foreach ($list as $item){
            $updateFlag = false;
            $filters = json_decode($item['filters'],true)??[];
            $handlers = json_decode($item['handlers'],true)??[];

            //工作流筛选
            foreach ($filters as &$filterValue){
                if(isset($filterValue['field']) && $filterValue['field'] == 'origin_list'){
                    if(is_array($filterValue['value'] ?? '')){
                        foreach ($filterValue['value'] as $originIndex => $originValue){
                            if(in_array($originValue,$oldOrigin)){
                                $updateFlag =true;
                                if ($newOrigin > 0) {
    
                                    $filterValue['value'][$originIndex] = $newOrigin;
                                }else{
        
                                    unset($filterValue['value'][$originIndex]);
                                }
                                $item['filters'] = json_encode($filters);
                            }
                        }
                    }else if(in_array($filterValue['value'] ?? '',$oldOrigin)){
                        $updateFlag = true;
                        $filterValue['value'] = $newOrigin;
                        $item['filters'] = json_encode($filters);
                    }
                }
            }
            unset($filterValue);

            //工作流动作
            foreach ($handlers as &$handlerValue){
                if(isset($handlerValue['type']) && $handlerValue['type'] == 'field' && isset($handlerValue['config']) && is_array($handlerValue['config'])){
                    foreach ($handlerValue['config'] as &$configItem){
                        if(isset($configItem['field']) && $configItem['field'] == 'origin_list'){
                            if(is_array($configItem['value'])){
                                foreach ($configItem['value'] as $originIndex => $originValue){
                                    if(in_array($originValue,$oldOrigin)){
                                        $updateFlag =true;
    
                                        if ($newOrigin > 0) {
    
                                            $configItem['value'][$originIndex] = $newOrigin;
                                        }else{
    
                                            unset($configItem['value'][$originIndex]);
                                        }

                                        $item['handlers'] = json_encode($handlers);
                                    }
                                }
                            }else if(in_array($configItem['value'],$oldOrigin)){
                                $updateFlag = true;
                                $configItem['value'] = $newOrigin;
                                $item['handlers'] = json_encode($handlers);
                            }
                        }
                    }
                }
            }
            unset($handlerValue);
            if($updateFlag && ($item['filters'] || $item['handlers'])){
                $sql = "update tbl_workflow_rule  set filters='{$item['filters']}',handlers = '{$item['handlers']}' where client_id = {$clientId} and rule_id = '{$item['rule_id']}'";
                $res = $db->createCommand($sql)->execute();
                \LogUtil::info("[modifyWorkflowOrigin:[client_id:{$clientId}] rule_id:{$item['rule_id']} res:{$res} newOrigin:{$newOrigin}  oldOrigin".json_encode($oldOrigin));
            }
        }

    }


    //更新审批流配置来源
    public  static  function modifyApprovalOrigin($clientId, array $oldOrigin, $newOrigin){

        $db = \ProjectActiveRecord::getDbByClientId($clientId);
        //查找审批单基础配置列表
        $sql = "select approval_flow_id,filters,handlers from tbl_approval_flow_base_config where client_id = {$clientId} and refer_type = ".Constants::TYPE_OPPORTUNITY;
        $approvalBaseList = $db->createCommand($sql)->queryAll();
        if(!$approvalBaseList){
            return false;
        }
        $approvalFlowIds = array_column($approvalBaseList,'approval_flow_id');
        $approvalFlowIdStr = join(',',$approvalFlowIds);
        //查找审批步骤列表
        $sql = "select approval_step_id,approval_flow_id,filters from tbl_approval_flow_step_config where client_id = {$clientId} and approval_flow_id IN($approvalFlowIdStr)";
        $approvalStepList = $db->createCommand($sql)->queryAll()??[];

        //修改审批配置信息
        foreach ($approvalBaseList as $item){
            $updateFlag = false;
            $filters = json_decode($item['filters'],true)??[];
            $handlers = json_decode($item['handlers'],true)??[];
            foreach ($filters as  &$filterValue){
                if(!isset($filterValue['field']) || $filterValue['field'] != 'origin_list'){
                    continue;
                }
                if(is_array($filterValue['value'])){
                    foreach ($filterValue['value'] as $originIndex => $originValue){
                        if(in_array($originValue,$oldOrigin)){
                            $updateFlag =true;
                            if ($newOrigin > 0) {
    
                                $filterValue['value'][$originIndex] = $newOrigin;
                            }else{
        
                                unset($filterValue['value'][$originIndex]);
                            }
                            $item['filters'] = json_encode($filters);
                        }
                    }
                }else if(in_array($filterValue['value'],$oldOrigin)){
                    $updateFlag = true;
                    $filterValue['value'] = $newOrigin;
                    $item['filters'] = json_encode($filters);
                }
            }
            unset($filterValue);
            foreach ($handlers as &$handlerValue){
                if(!isset($handlerValue['handlers']) || !$handlerValue['handlers']){
                    continue;
                }
                foreach ($handlerValue['handlers'] as &$configItem){
                    if(!isset($configItem['type']) || !$configItem['type'] == 'field' || !isset($configItem['config']) || !$configItem['config']){
                        continue;
                    }
                    foreach ($configItem['config'] as &$sonConfigItem){
                        if(!isset($sonConfigItem['field']) || !$sonConfigItem['field'] || $sonConfigItem['field'] != 'origin_list'){
                            continue;
                        }
                        if(is_array($sonConfigItem['value'])){
                            foreach ($sonConfigItem['value'] as $originIndex => $originValue){
                                if(in_array($originValue,$oldOrigin)){
                                    $updateFlag =true;
                                    if ($newOrigin > 0) {
    
                                        $sonConfigItem['value'][$originIndex] = $newOrigin;
                                    }else{
        
                                        unset($sonConfigItem['value'][$originIndex]);
                                    }
                                    $item['handlers'] = json_encode($handlers);
                                }
                            }
                        }else if(in_array($sonConfigItem['value'],$oldOrigin)){
                            $updateFlag = true;
                            $sonConfigItem['value'] = $newOrigin;
                            $item['handlers'] = json_encode($handlers);

                        }

                    }
                }
            }
            unset($handlerValue);
            //涉及字段变更，商机来源，才修改
            if($updateFlag && ($item['filters'] || $item['handlers'])){
                $sql = "update tbl_approval_flow_base_config  set filters='{$item['filters']}',handlers = '{$item['handlers']}' where client_id = {$clientId} and approval_flow_id = '{$item['approval_flow_id']}'";
                $res = $db->createCommand($sql)->execute();
                \LogUtil::info("[modifyApprovalOrigin:[client_id:{$clientId}] approval_flow_id:{$item['approval_flow_id']} newOrigin:{$newOrigin} res:{$res}  oldOrigin".json_encode($oldOrigin));
            }
        }

        //修改审批步骤信息
        foreach ($approvalStepList as $stepItem) {
            $updateFlag = false;
            $stepFilters = json_decode($stepItem['filters'], true) ?? [];
            foreach ($stepFilters as &$stepFilterValue) {
                if (!isset($stepFilterValue['field']) || $stepFilterValue['field'] != 'origin_id' || empty($stepFilterValue['value'])) {
                    continue;
                }
                if (is_array($stepFilterValue['value'])) {
                    foreach ($stepFilterValue['value'] as $originIndex => $originValue) {
                        if (in_array($originValue, $oldOrigin)) {
                            $updateFlag = true;
                            if ($newOrigin > 0) {
        
                                $stepFilterValue['value'][$originIndex] = $newOrigin;
                            }else{
        
                                unset($stepFilterValue['value'][$originIndex]);
                            }
                            $stepItem['filters'] = json_encode($stepFilters);
                        }
                    }
                } else if (in_array($stepFilters['value'], $oldOrigin)) {
                    $updateFlag = true;
                    $filterValue['value'] = $newOrigin;
                    $stepItem['filters'] = json_encode($stepFilters);
                }
            }
            //涉及字段变更，商机来源，才修改
            if($updateFlag && $stepItem['filters'] ){
                $sql = "update tbl_approval_flow_step_config  set filters='{$stepItem['filters']}'  where client_id = {$clientId} and approval_step_id = '{$stepItem['approval_step_id']}'";
                $res = $db->createCommand($sql)->execute();
                \LogUtil::info("[modifyStepOrigin:[client_id:{$clientId}] step_id:{$stepItem['approval_step_id']}  flow_id:{$stepItem['approval_flow_id']}  newOrigin:{$newOrigin} res:{$res}  oldOrigin".json_encode($oldOrigin));
            }
        }
    }


    /**
     * boss端删除分组时，将被删除的分组设置为0
     * @param array $oldGroupIds
     * @param $newGroupId
     * @return int
     */
    public static function updateAllGroup($clientId, $userId, array $oldGroupIds, $newGroupId) {

        $operator = new CompanyBatchOperator($userId);

        $operator->getList()->showAll(true, PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW);

        $operator->getList()->setSkipPrivilege(true);

        $operator->setParams([

            'group_id' => $oldGroupIds,
            'show_all' => 1,
        ]);

        $rows =  $operator->setGroupId($newGroupId, true);

        return $rows;
    }


    public static function query($clientId, $userId, $queryWord,$search_field, $page, $limit)
    {

        $result = [
            'list' => [],
            'totalItem' => 0,
        ];

        if( empty($queryWord) )
            return $result;

        $highlight = "<b>{$queryWord}</b>";
        $queryWordLength = strlen($queryWord);

        //获取字段显示列表
        $customerShowFields = CustomerOptionService::getCustomerShowFields($clientId);
        $customerNotShowFields = array_filter($customerShowFields,function ($item){
                if ($item['display_flag']==0) return true;
        });

		$client = Client::getClient($clientId);
		$detectKey = DuplicateConstants::MODULE_MAP[\Constants::TYPE_COMPANY]['detecting'];
		$ruleTypeKey = DuplicateConstants::MODULE_MAP[\Constants::TYPE_COMPANY]['rule_type'];
		$duplicateRuleType = (($client->getSettingAttributes()[$detectKey] ?? 0) == \common\library\duplicate\DuplicateConstants::DUPLICATE_CONFLICT_DETECTING) ? DuplicateFlagBuilder::DUPLICATE_ALL_RULE_TYPE : ($client->getSettingAttributes()[$ruleTypeKey] ?? 0);

        $listObj = new CompanyList($userId);
        $listObj->setSearchDetail(true);
		if ($duplicateRuleType == DuplicateFlagBuilder::DUPLICATE_POOL_ID_RULE_TYPE) {
			$userPoolMap = \common\library\customer\pool\Helper::getUserPoolMap($clientId);
			$listObj->setPoolId($userPoolMap[$userId]);
		}
        $listObj->setKeyword($queryWord);
        $listObj->setSortBySearchScore(true);

		$search_field = array_filter((array)$search_field);

		if(empty($search_field)) {
			$search_field = [
		        'name',
		        'homepage',
		        'customer_list.name',
		        'customer_list.email',
		        'customer_list.email.domain',
		        'customer_list.tel',
		        'serial_id',
		        'tel',
		        'customer_list.contact.value',
	        ];
        }

	    if (in_array('name', $search_field)) {

		    $search_field[] = 'short_name';
	    }

        $listObj->setSearchFields($search_field);
        $listObj->setSkipPrivilege(true);
        $listObj->setFields([
            'company_id',
            'serial_id',
            'name',
            'short_name',
            'user_id',
            'archive_time as create_time',
            'trail_status',
            'tel',
            'tel_area_code',
            'pool_id',
            'order_time',
            'homepage',
            'country',
            'last_owner',
            'origin_list',
        ]);
        $listObj->setLimit($limit, $page);

        $companyList = $listObj->find();

        if (empty($companyList)) {
            return $result;
        }


        $poolNameMap = \common\library\customer\pool\Helper::getPoolNameMap($clientId, true);

        $searchResult = $listObj->getLastSearchResult();

        if (isset($searchResult['total'])) {
            //0315迭代放开分页
            $count = $searchResult['total'];
            $searchResult = $searchResult['list'] ?? [];
        } else {
            $count = count($companyList);
        }

        $resultMap =  empty($searchResult)?[]:array_combine(array_column($searchResult, '_id'), $searchResult);

        $list = array();

        $queryWord = htmlentities($queryWord);
        $name = '';
        $email = '';
        foreach ($companyList as $elem) {
            $companyId = $elem['company_id'];
            $elem['user_id'] = PgsqlUtil::trimArray($elem['user_id']);
	        $appCompanyName = $elem['name'] . (!empty($elem['short_name']) ? '(' . $elem['short_name'] . ')' : '');
            $companyName = str_ireplace($queryWord, "<b>" . substr(stristr(htmlentities($appCompanyName),$queryWord),0, strlen($queryWord)) . "</b>", htmlentities($appCompanyName));

            $homepage = str_ireplace($queryWord, "<b>" . substr(stristr($elem['homepage'],$queryWord),0,strlen($queryWord)) . "</b>", htmlentities($elem['homepage']));
            $trailStatus = \CustomerOptionService::getOneCustomerStatus($clientId, $elem['trail_status']);
            $country = $elem['country'];
    
            $origin = PgsqlUtil::trimArray($elem['origin_list']);
            $origin_name = CustomerOptionService::getOriginName($clientId, $origin);

            $last_owner = $elem['last_owner'];
            $last_owner_name = CustomerOptionService::getUserInfo($clientId, $elem['last_owner'])['nickname'] ?? '';

            $tel = [];
            if (!empty($elem['tel'])) {
                $tel[] = "{$elem['tel_area_code']} {$elem['tel']}";
            }
            $contact = [];
            $customerList = $resultMap[$companyId]['customer_list']??[];

            foreach ($customerList as $customer) {

                $email = '';
                $qwPos = false;
                if (!empty($customer['email']))
                {
                    $qwPos = strpos($customer['email'], $queryWord);
                    $atPos = strpos($customer['email'], '@');
                    $suffixPos = strrpos($customer['email'], '.');
                    $email = str_pad('', $suffixPos, '*') . substr($customer['email'], $suffixPos);
                    is_numeric($atPos) && $email[$atPos] = '@';
                }

                if (!empty($customer['tel'])) {
                    $tel = array_merge($tel,(array)$customer['tel']);
                }

                if (!empty($customer['contact'])) {
                    $cc = $customer['contact'];
                    //兼容错误数据格式
                    if (isset($customer['contact']['type'])) {
                        $cc = [$cc];
                    }

	                foreach ($cc as $k => $value) {

		                if (!isset($value['type']) || !isset($value['value'])) {

			                unset($cc[$k]);
		                }
	                }

                    $contact = array_merge($contact,array_values($cc));
                }

                if ($qwPos !== false) {
                    $email = substr($email, 0, $qwPos) . $queryWord . substr($email, $qwPos + $queryWordLength);

                    $email = str_ireplace($queryWord, "<b>" . substr(stristr($email,$queryWord),0,strlen($queryWord)) . "</b>", $email);
                    $name = $customer['name'];
                    break;
                } else {
                    //邮箱为空时返回空
                    $email = $email ? '***@***.***' : '';
                    // 连续空格合并为单个空格
                    $trimCustomerName = htmlentities(preg_replace('/\s(?=\S)/',' ',$customer['name']));
                    if (stripos($trimCustomerName, $queryWord) !== false) {
                        $name = str_ireplace($queryWord, "<b>" . substr(stristr($trimCustomerName,$queryWord),0,strlen($queryWord)) . "</b>", $trimCustomerName);
                        break;
                    } else {
                        if ($customer['main_customer_flag'] ?? 0) {
                            $name = $customer['name'];
                        }
                    }
                }
            }
            $is_owner = 1;
            $is_public = 0;
            if (empty($elem['user_id'])) {
                $is_public = 1;
                $owner = ['公海客户'];
                if ($elem['pool_id'] && \common\library\customer\pool\Helper::enabled($clientId,$userId)) {
                    if (!\common\library\customer\pool\Helper::userInPool($clientId, $userId, $elem['pool_id'])) {
                        $is_owner = 0;
                        $companyId = 0;
                        $poolName = array_key_exists($elem['pool_id'], $poolNameMap) ? $poolNameMap[$elem['pool_id']] : '未知分组';
                        $owner = ['公海客户-' . $poolName];
                    }
                }
            } else {

                $scopeUserIds = \common\library\privilege_v3\Helper::getPermissionScopeUser(
                    $clientId,
                    $userId,
                    PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW,
                    true,
                    false);

                //不是私海客户并且不是有权限查看的同事私海客户
                if (
                    !in_array($userId, $elem['user_id'])
                    && ($scopeUserIds != 0 && count(array_intersect($scopeUserIds, $elem['user_id'])) == 0)
                ) {
                    $companyId = 0;
                    $is_owner = 0;
                }

                $owner = array();
                foreach ($elem['user_id'] as $item) {
                    $item = intval($item);
                    $user = \User::getUserObject($item);
                    if (!$user->hasInfo()) {
                        continue;
                    }
                    $owner[] = $user->getNickname();
                }
            }

            $telHighlightResult = array_reduce($tel,function ($carry,$v)use($queryWord,$highlight){
                if( strpos($v,$queryWord) !== false){
                    $carry[] = str_ireplace($queryWord, "<b>" . substr(stristr($v,$queryWord),0,strlen($queryWord)) . "</b>", htmlentities($v));
                }
                return $carry;
            },[]);

            if(empty($telHighlightResult))
                $telHighlightResult[] = $tel[0]??'';

            $telStr = implode(';',$telHighlightResult);
            $telStr = $is_owner ? $telStr : str_pad('', strlen($telStr), '*');

            $contactHighlightResult = array_reduce($contact,function ($carry,$v)use($queryWord,$highlight){

                if(stripos(($v['value'] ?? ''),$queryWord) !==false && !empty(($v['type'] ?? ''))){
                    $carry[] = $v['type'].': '.str_ireplace($queryWord,"<b>" . substr(stristr($v['value'],$queryWord),0,strlen($queryWord)) . "</b>",$v['value']);
                }

                return $carry;

            },[]);

            if( empty($contactHighlightResult) && count($contact)>0 )
                $contactHighlightResult[] =$contact[0]['type'].': '.$contact[0]['value'];

            $contactStr = implode(';',$contactHighlightResult);
            $contactStr = $is_owner ? $contactStr : str_pad('', strlen($contactStr), '*');


            $res = array(
                'company_id' => $companyId,
                'company' => $companyName,
                'app_company' => $appCompanyName,
                'name' => $name,
                'email' => $email,
                'owner' => $owner,
                'trail_status' => $trailStatus,
                'tel' => $telStr,
                'contact' => $contactStr,
                'create_time' => $elem['create_time'],
                'serial_id' => $elem['serial_id'],
                'is_owner' => $is_owner, //app 端使用
                'is_public' => $is_public, //app 端使用
                'order_time' => $elem['order_time'] ?? "",
                'homepage' => $homepage,
                'country' => $country,
                'origin' => $origin[0] ?? 0,
                'origin_list' => $origin,
                'origin_name' => $origin_name,
                'last_owner' => $last_owner,
                'last_owner_name' => $last_owner_name,

            );

            //过滤隐藏的字段
            foreach ($customerNotShowFields as $notShowFieldItem) {
                if ($notShowFieldItem['key'] == 'company') {
                    $res['app_company'] = '***';
                }

                if (isset($res[$notShowFieldItem['key']])) {
                    $res[$notShowFieldItem['key']] = '***';
                }

                if ($notShowFieldItem['key'] == 'owner') {
                    $res['owner'] = ['***'];
                }

                if ($notShowFieldItem['key'] == 'status'
                    && isset($res['trail_status']['item_name'])) {
                    $res['trail_status']['item_name'] = '***';
                }
            }

            $list[] = $res;
        }

        $result['list'] = $list;
        $result['totalItem'] = $count;

        return $result;
    }
    
    /**
     * 获取查重显示字段
     * @param int $clientId
     * @param bool $withNotDisplay
     * @return array
     */
    public static function getDuplicateDisplayFields(int $clientId, bool $withNotDisplay = false): array
    {
        /**
         * @var array $fieldSettings
         * @example [
         *   \Constants::TYPE_COMPANY => [
         *     'name' => [
         *       'display_flag' => 1
         *     ]
         *   ]
         * ]
         */
        $fieldSettings = [
            Constants::TYPE_COMPANY => [],
            Constants::TYPE_CUSTOMER => [],
        ];
        
        $customerShowFields = CustomerOptionService::getCustomerShowFields($clientId);
        $map = [
            \Constants::TYPE_COMPANY => CompanyFieldSetting::duplicateSearchHeaderFields(),
            \Constants::TYPE_CUSTOMER => CustomerFieldSetting::duplicateSearchHeaderFields(),
        ];
        foreach ($customerShowFields as $field) {
            if (isset($map[\Constants::TYPE_COMPANY][$field['key']]) && $field['key'] != 'name') {
                if (!$withNotDisplay && empty($field['display_flag'])) {
                    continue;
                }
                $newFields = array_keys($map[\Constants::TYPE_COMPANY][$field['key']]);
                $fieldSettings[\Constants::TYPE_COMPANY] = array_reduce($newFields, function($carry, $it) use ($field) {
                    $carry[$it] = ['display_flag' => $field['display_flag']];
                    return $carry;
                }, $fieldSettings[\Constants::TYPE_COMPANY]);
                continue;
            }
            if (isset($map[\Constants::TYPE_CUSTOMER][$field['key']])) {
                if (!$withNotDisplay && empty($field['display_flag'])) {
                    continue;
                }
                $newFields = array_keys($map[\Constants::TYPE_CUSTOMER][$field['key']]);
                $fieldSettings[\Constants::TYPE_CUSTOMER] = array_reduce($newFields, function($carry, $it) use ($field) {
                    $carry[$it] = ['display_flag' => $field['display_flag']];
                    return $carry;
                }, $fieldSettings[\Constants::TYPE_CUSTOMER]);
            }
        }
        
        return $fieldSettings;
    }

    public static function queryV2($clientId, $userId, $queryWord, $searchField, $page, $limit)
    {
        $result = [
            'list' => [],
            'totalItem' => 0,
        ];
        
        if (empty($queryWord))
            return $result;
        
        $queryWordLength = mb_strlen($queryWord);
        
        //获取字段显示列表
        $showFields = Helper::getDuplicateDisplayFields($clientId);
        
        $client = Client::getClient($clientId);
        $detectKey = DuplicateConstants::MODULE_MAP[\Constants::TYPE_COMPANY]['detecting'];
        $ruleTypeKey = DuplicateConstants::MODULE_MAP[\Constants::TYPE_COMPANY]['rule_type'];
        $detecting = $client->getSettingAttributes()[$detectKey] ?? 0;
        $ruleType = $client->getSettingAttributes()[$ruleTypeKey] ?? 0;
        $duplicateRuleType = $detecting == DuplicateConstants::DUPLICATE_CONFLICT_DETECTING ?
            DuplicateFlagBuilder::DUPLICATE_ALL_RULE_TYPE : $ruleType;
        
        $listObj = new CompanyList($userId);
        $listObj->setSearchDetail(true);
        if ($duplicateRuleType == DuplicateFlagBuilder::DUPLICATE_POOL_ID_RULE_TYPE) {
            $userPoolMap = \common\library\customer\pool\Helper::getUserPoolMap($clientId);
            $listObj->setPoolId($userPoolMap[$userId]);
        }
        $listObj->setKeyword($queryWord);
        //$listObj->setHighLightFlag(1);
        $listObj->setSortBySearchScore(true);
        
        $searchField = array_filter((array)$searchField);
        
        if (empty($searchField)) {
            $searchField = [
                'name',
                'homepage',
                'customer_list.name',
                'customer_list.email',
                'customer_list.email.domain',
                'customer_list.tel',
                'serial_id',
                'tel',
                'customer_list.contact.value',
            ];
        }
        
        if (in_array('name', $searchField)) {
            $searchField[] = 'short_name';
        }
        
        $listObj->setSearchFields($searchField);
        $listObj->setSkipPrivilege(true);
        $listObj->setLimit($limit, $page);
        $listObj->getFormatter()->duplicateSearchSetting();
        $companyList = $listObj->find();
        
        if (empty($companyList)) {
            return $result;
        }
        
        $searchResult = $listObj->getLastSearchResult();
        
        if (isset($searchResult['total'])) {
            //0315迭代放开分页
            $count = $searchResult['total'];
            $searchResult = $searchResult['list'] ?? [];
        } else {
            $count = count($companyList);
        }

        $resultMap =  empty($searchResult) ? [] : array_column($searchResult, null, '_id');
        
        $queryWord = htmlentities($queryWord);
        $hlLeft = '#{';
        $hlRight = '}#';
        foreach ($companyList as $i => $elem) {
            $companyId = $elem['company_id'];
            $serialId = $elem['serial_id'];
            $companyName = str_ireplace($queryWord, $hlLeft . substr(stristr(htmlentities($elem['name']), $queryWord), 0, strlen($queryWord)) . $hlRight, htmlentities($elem['name']));
            $shortName = str_ireplace($queryWord, $hlLeft . substr(stristr(htmlentities($elem['short_name']), $queryWord), 0, strlen($queryWord)) . $hlRight, htmlentities($elem['short_name']));

            $tel = [];
            if (!empty($elem['tel'])) {
                $tel[] = "{$elem['tel_area_code']} {$elem['tel']}";
            }
            $contact = [];
            $customerList = $resultMap[$companyId]['customer_list'] ?? [];
            
            $customerName = '';
            $email = '';
            foreach ($customerList as $customer) {
                $email = '';
                $qwPos = false;
                if (!empty($customer['email'])) {
                    $qwPos = strpos($customer['email'], $queryWord);
                    $atPos = strpos($customer['email'], '@');
                    $suffixPos = strrpos($customer['email'], '.');
                    $email = str_pad('', $suffixPos, '*') . substr($customer['email'], $suffixPos);
                    is_numeric($atPos) && $email[$atPos] = '@';
                }

                if (!empty($customer['tel'])) {
                    $tel = array_merge($tel, (array)$customer['tel']);
                }

                if (!empty($customer['contact'])) {
                    $cc = $customer['contact'];
                    //兼容错误数据格式
                    if (isset($customer['contact']['type'])) {
                        $cc = [$cc];
                    }

                    foreach ($cc as $k => $value) {
                        if (!isset($value['type']) || !isset($value['value'])) {
                            unset($cc[$k]);
                        }
                    }

                    $contact = array_merge($contact, array_values($cc));
                }

                if ($qwPos !== false) {
                    $email = substr($email, 0, $qwPos) . $queryWord . substr($email, $qwPos + $queryWordLength);

                    $email = str_ireplace($queryWord, $hlLeft . substr(stristr($email, $queryWord), 0, strlen($queryWord)) . $hlRight, $email);
                    $customerName = $customer['name'];
                    break;
                } else {
                    //邮箱为空时返回空
                    $email = $email ? '***@***.***' : '';
                    // 连续空格合并为单个空格
                    $trimCustomerName = htmlentities(preg_replace('/\s(?=\S)/', ' ', $customer['name']));
                    if (str_contains($trimCustomerName, $queryWord)) {
                        $customerName = str_ireplace($queryWord, $hlLeft . substr(stristr($trimCustomerName, $queryWord), 0, strlen($queryWord)) . $hlRight, $trimCustomerName);
                        break;
                    } else {
                        if ($customer['main_customer_flag'] ?? 0) {
                            $customerName = $customer['name'];
                        }
                    }
                }
            }

            $is_owner = 1;
            $is_public = 0;
            $owner = '';
            if (empty($elem['user_id'])) {
                $is_public = 1;
                $owner = '公海客户';
                if ($elem['pool_id'] && \common\library\customer\pool\Helper::enabled($clientId, $userId)) {
                    if (!\common\library\customer\pool\Helper::userInPool($clientId, $userId, $elem['pool_id'])) {
                        $is_owner = 0;
                        $companyId = 0;
                        $poolName = $elem['pool_name'] ?: '未知分组';
                        $owner = '公海客户-' . $poolName;
                    }
                }
            } else {
                $scopeUserIds = \common\library\privilege_v3\Helper::getPermissionScopeUser(
                    $clientId,
                    $userId,
                    PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW,
                    true,
                    false);
                
                //不是私海客户并且不是有权限查看的同事私海客户
                if (
                    !in_array($userId, $elem['user_id'])
                    && ($scopeUserIds != 0 && count(array_intersect($scopeUserIds, $elem['user_id'])) == 0)
                ) {
                    $companyId = 0;
                    $is_owner = 0;
                }
            }

            $telHighlightResult = array_reduce($tel, function ($carry, $v) use ($hlLeft, $hlRight, $queryWord) {
                if (str_contains($v, $queryWord)) {
                    $carry[] = str_ireplace($queryWord, $hlLeft . substr(stristr($v, $queryWord), 0, strlen($queryWord)) . $hlRight, htmlentities($v));
                }
                return $carry;
            }, []);

            if (empty($telHighlightResult))
                $telHighlightResult[] = $tel[0] ?? '';

            $telStr = implode(';', $telHighlightResult);
            $telStr = $is_owner ? $telStr : str_pad('', strlen($telStr), '*');
            $contactHighlightResult = array_reduce($contact, function ($carry, $v) use ($hlRight, $hlLeft, $queryWord) {
                if (stripos(($v['value'] ?? ''), $queryWord) !== false && !empty(($v['type'] ?? ''))) {
                    $carry[] = $v['type'] . ': ' . str_ireplace($queryWord, $hlLeft . substr(stristr($v['value'], $queryWord), 0, strlen($queryWord)) . $hlRight, $v['value']);
                }

                return $carry;
            }, []);

            if (empty($contactHighlightResult) && count($contact) > 0)
                $contactHighlightResult[] = $contact[0]['type'] . ': ' . $contact[0]['value'];

            $contactStr = implode(';', $contactHighlightResult);
            $contactStr = $is_owner ? $contactStr : str_pad('', strlen($contactStr), '*');

            $data = [
                'company_id' => $companyId,
                'name' => $companyName,
                'name_info' => [
                    'info_label' => $companyName,
                    'info_value' => $companyName,
                ],
                'short_name' => $shortName,
                'short_name_info' => [
                    'info_label' => $shortName,
                    'info_value' => $shortName,
                ],
            ];
            if ($is_public) {
                $data['user_id_info'] = [
                    [
                        'info_label' => $owner,
                        'info_value' => 0,
                    ]
                ];
            }
            $elem = array_replace_recursive($elem, $data);

            $companyList[$i] = [
                'serial_id' => $serialId,  // 前端需要用来作唯一校验
                'company_id' => $companyId,
                'is_public' => $is_public,
                'is_owner' => $is_owner,
            ];
            foreach ($showFields[\Constants::TYPE_COMPANY] as $field => $_) {
                if (isset($elem[$field])) {
                    $companyList[$i][$field] = $elem[$field];
                }
                if (isset($elem["{$field}_info"])) {
                    $companyList[$i]["{$field}_info"] = $elem["{$field}_info"];
                }
            }

            $companyList[$i]['customer'] = array_intersect_key([
                'name' => $customerName,
                'email' => $email,
                'contact' => $contactStr,
                'tel_list' => $telStr,
            ], $showFields[\Constants::TYPE_CUSTOMER]);
        }

        $result['list'] = $companyList;
        $result['totalItem'] = $count;

        return $result;
    }

    public static function updateOrderTime($clientId, $userId, $companyId, $customerId, $checkRefer = null, $time = null, $preventPrevious = false, $lazySwarm = false, bool $runPerformance = true)
    {


        if ($checkRefer && !\CustomerOptionService::checkReference($clientId, $checkRefer))
            return;

        if (empty($companyId))
            return;

        if($time == null)
            $time = date('Y-m-d H:i:s');

        $db = \PgActiveRecord::getDbByClientId($clientId);

        $set = '';
        if ($userId)
        {
            $buildSet = Helper::buildUserData('user_data', $userId, ['order_time' => $time], $checkRefer);
            $set = ",user_data = $buildSet";
        }

        $companyId = is_array($companyId) ? $companyId : [$companyId];
        $companyIdsSql = '(' . implode(',', $companyId) . ')';
        $sql = "update tbl_company set order_time='$time', update_time='{$time}' $set where company_id in $companyIdsSql and client_id=$clientId";
        if ($preventPrevious) {
            $sql .= " and order_time < '{$time}'";
        }
        $res = $db->createCommand($sql)->execute();
        LogUtil::info("try_update_order_time", ['company' => $companyIdsSql,'order_time' => $time, 'affected_rows' => $res, 'refer' => $checkRefer]);

        if ($customerId)
        {
            $customerId = is_array($customerId) ? $customerId : [$customerId];
            $customerIdsSql = '(' . implode(',', $customerId) . ')';
            $sql = "update tbl_customer set order_time='$time' $set where customer_id in $customerIdsSql and client_id=$clientId";
            if ($preventPrevious) {
                $sql .= " and order_time < '{$time}'";
            }
            $db->createCommand($sql)->execute();
        }

        if ($res) {
            if (!$lazySwarm) {
                (new SwarmService($clientId))->refreshByRefer($companyId, [
                    'order_time',
                ]);
            }
            //跟进时间更新，消除3天要移入公海object_id
            \common\library\todo\Helper::updateFeedStatusByObjectId($clientId, $userId, TodoConstant::OBJECT_TYPE_COMPANY, TodoConstant::TODO_TYPE_COMPANY_MOVE_TO_PUBLIC_IN_THREE_DAYS, $companyId);
            if ($runPerformance) {
                // 时间更新 计入绩效
                \common\library\performance_v2\Helper::runPerformance($clientId, \Constants::TYPE_COMPANY, $companyId, [], ['order_time','update_time']);
            }
            return $time;
        }
    }

    /**
     * 更新最近跟进时间
     * @param $clientId
     * @param $companyId
     * @param null $checkRefer
     * @param null $time
     * @param false $preventPrevious
     * @return false|int
     * @throws \CDbException
     * @throws \ProcessException
     */
    public static function updateRecentFollowUpTime($clientId, $userId,$companyId, $checkRefer = null, $time = null, bool $runPerformance = true)
    {
        if (($checkRefer && !\CustomerOptionService::checkRecentFollowUpReference($clientId, $checkRefer)) || empty($companyId)) {
            return false;
        }
        if ($time == null) {
            $time = date('Y-m-d H:i:s');
        }
        $db = \PgActiveRecord::getDbByClientId($clientId);

        $companyId = is_array($companyId) ? $companyId : [$companyId];
        $companyIdsSql = '(' . implode(',', $companyId) . ')';
        $sql = "update tbl_company set recent_follow_up_time= '$time' where company_id in $companyIdsSql and client_id=$clientId and recent_follow_up_time < '{$time}'";
        $res = $db->createCommand($sql)->execute();

        if ($res) {
            if ($runPerformance) {
                $companyBatchOperator = new \common\library\customer\CompanyBatchOperator($userId);
                $companyBatchOperator->runPerformance($clientId,$userId,\Constants::TYPE_COMPANY,$companyId);
            }

            (new SwarmService($clientId))->refreshByRefer($companyId, [
                'recent_follow_up_time',
            ]);
        }

        return $res;
    }

    public static function updateRemarkTime($clientId, $userId, $companyId, $customerId, $type = 0, $create_time = '')
    {
	    //$company = (new Company($clientId, $companyId));

		if(empty($companyId)){
			return;
		}

	    $now = $create_time ? date('Y-m-d H:i:s', strtotime($create_time)) : date('Y-m-d H:i:s');

        $db = \PgActiveRecord::getDbByClientId($clientId);

        $companyId = is_array($companyId) ? $companyId : [$companyId];
        $companyIdSql = '(' . implode(',', $companyId) . ')';
        $sql = "select company_id, follow_up_time, recent_follow_up_time from tbl_company where client_id = {$clientId} and company_id in {$companyIdSql}";
        $companyList = $db->createCommand($sql)->queryAll();

        $needUpdateRecentFollowUpTime = CustomerOptionService::checkRecentFollowUpReference($clientId, 'refer_follow_remark');
        $updateRecentFollowUpTimeStr = '';
        if ($needUpdateRecentFollowUpTime) {
            $updateRecentFollowUpTimeStr = ",recent_follow_up_time       = CASE WHEN recent_follow_up_time < '$now' THEN '$now' ELSE recent_follow_up_time END";
        }

        //判断是否写跟进的type
        $latestWriteFollowUpTimeStr = '';
        if (in_array($type,[
            TrailConstants::TYPE_REMARK_ADD,
            TrailConstants::TYPE_REMARK_RELATE_MAIL,
            TrailConstants::TYPE_REMARK_TEL,
            TrailConstants::TYPE_REMARK_MEETING,
            TrailConstants::TYPE_REMARK_CONTACT,
            TrailConstants::TYPE_REMARK_VISIT_HEADQUARTERS,
            TrailConstants::TYPE_REMARK_VISIT_OFFICE,
            TrailConstants::TYPE_REMARK_COME_TO_VISIT_HEADQUARTERS,
            TrailConstants::TYPE_REMARK_COME_TO_VISIT_OFFICE,
            TrailConstants::TYPE_REMARK_MAIL,
            TrailConstants::TYPE_REMARK_VISIT,
            TrailConstants::TYPE_REMARK_SCHEDULE,
        ])) {
            $latestWriteFollowUpTimeStr = ",latest_write_follow_up_time = CASE WHEN latest_write_follow_up_time < '$now' THEN '$now' ELSE latest_write_follow_up_time END";
        }

        if (\CustomerOptionService::checkReference($clientId, 'refer_remark'))
        {


            $buildSet = Helper::buildUserData('user_data', $userId, ['order_time' => $now], 'refer_remark');
            $buildSet = is_array($userId) ? "CASE WHEN order_time > '$now' THEN user_data ELSE " . $buildSet . " end " : str_replace("CASE", " CASE WHEN order_time > '$now' THEN user_data ", $buildSet);

            $userData = " , user_data = {$buildSet} ";

            $db->createCommand("update tbl_company
								SET order_time                  =CASE WHEN order_time < '$now' THEN '$now' ELSE order_time END,
   	 								follow_up_time              = CASE WHEN follow_up_time < '$now' THEN '$now' ELSE follow_up_time END
                        			{$userData}
                        			{$updateRecentFollowUpTimeStr}{$latestWriteFollowUpTimeStr} where company_id in {$companyIdSql} and client_id = {$clientId}")
                ->execute();


            if ($customerId)
            {
                $db->createCommand("update tbl_customer
											SET order_time            =CASE WHEN order_time < '$now' THEN '$now' ELSE order_time END
								    			{$userData} where customer_id=$customerId and client_id=$clientId")
                    ->execute();
            }
        }
        else
        {
            $db->createCommand("update tbl_company
										set follow_up_time              = CASE WHEN follow_up_time < '$now' THEN '$now' ELSE follow_up_time END
     										{$updateRecentFollowUpTimeStr}{$latestWriteFollowUpTimeStr} where company_id in {$companyIdSql} and client_id = {$clientId}")
                ->execute();
        }

        //$now > $company->follow_up_time
        $followCompanyIds = array_column(array_filter($companyList, function ($company) use ($now) {
                return $now > $company['follow_up_time'];
            }), 'company_id') ?? [];

        //$now > $company->recent_follow_up_time
        $recentCompanyIds = array_column(array_filter($companyList, function ($company) use ($now) {
            return $now > $company['recent_follow_up_time'];
        }), 'company_id') ?? [];

        if (!empty($followCompanyIds)) {

		    //跟进时间更新，消除3天要移入公海object_id
		    \common\library\todo\Helper::updateFeedStatusByObjectId($clientId, $userId, TodoConstant::OBJECT_TYPE_COMPANY, TodoConstant::TODO_TYPE_COMPANY_MOVE_TO_PUBLIC_IN_THREE_DAYS, $followCompanyIds);
            \LogUtil::info("batch update user_id = " . is_array($userId) ? json_encode($userId) : $userId . " client_id = " . $clientId . " follow_company_ids = " . json_encode($followCompanyIds));

        }
        //跟进时间更新 重新计算客群
        (new SwarmService($clientId))->refreshByRefer($companyId, [
            'latest_write_follow_up_time',
            'follow_up_time',
            'order_time',
            'recent_follow_up_time',
        ]);

	    // 标记未完成的任务为完成状态
	    if ($needUpdateRecentFollowUpTime && !empty($recentCompanyIds)) {

		    \common\library\task\Helper::finishTask($clientId, $recentCompanyIds);
            \LogUtil::info("batch update user_id = " . is_array($userId) ? json_encode($userId) : $userId . " client_id = " . $clientId . " recent_company_ids = " . json_encode($recentCompanyIds));

        }

        // todo 绩效埋点是否在这里更新最近跟进时间

        \common\library\performance_v2\Helper::runPerformance($clientId, \Constants::TYPE_COMPANY, $companyId);


        \LogUtil::info("batch update user_id = " . is_array($userId) ? json_encode($userId) : $userId . " client_id = " . $clientId . " company_ids = " . json_encode($companyId));
    }

    public static function updateMeetingTime($clientId, $userId, $companyId)
    {
        $now = date('Y-m-d H:i:s');

        $db = \PgActiveRecord::getDbByClientId($clientId);

        $buildSet = Helper::buildUserData('user_data', $userId, ['order_time' => $now], 'updateMeetingTime');

        $res = $db->createCommand("update tbl_company set order_time='$now', meeting_time='$now', user_data = $buildSet where company_id=$companyId and client_id=$clientId")
            ->execute();

        if ($res) {
            (new SwarmService($clientId))->refreshByRefer([$companyId], [
                'meeting_time',
                'order_time',
            ]);
        }

        return $res;
    }

    public static function updateSendMailTime($clientId, $userId, $companyId, $customerId, $mailTime = null)
    {
        if($mailTime == null)
            $mailTime = date('Y-m-d H:i:s');

        $db = \PgActiveRecord::getDbByClientId($clientId);

        $sendMailTimeUpdateSql = "send_mail_time=case when send_mail_time < '$mailTime' then '$mailTime' else send_mail_time end";
        $mailTimeUpdateSql = "mail_time=case when mail_time < '$mailTime' then '$mailTime' else mail_time end";
        $orderTimeUpdateSql = "order_time=case when order_time < '$mailTime' then '$mailTime' else order_time end";
        $needUpdateRecentFollowUpTime = CustomerOptionService::checkRecentFollowUpReference($clientId, 'refer_follow_send_mail');
        $recentFollowUpTime = '';
        if ($needUpdateRecentFollowUpTime)
        {
            $recentFollowUpTime = ",recent_follow_up_time = case when recent_follow_up_time < '$mailTime' then '$mailTime' else recent_follow_up_time END";
        }
        if (\CustomerOptionService::checkReference($clientId, 'refer_send_mail'))
        {
            $set = '';
            $subWhere = " client_id=$clientId";
            if ($userId)
            {
                //将非客户跟进人与客户联系人的往来邮件生成客户动态的配置开关-关闭后要检查跟进人才更新order_time
                $dynamicTrailCheck = \common\library\customer\Helper::DynamicTrailCheck($clientId);
                if( !$dynamicTrailCheck )
                {
                    $subWhere .= " and (user_id='{}' or user_id && ARRAY[$userId]::bigint[])";
                }

                $buildSet = Helper::buildUserData('user_data', $userId, ['mail_time' => $mailTime, 'order_time' => $mailTime], 'refer_send_mail');
                $set = ",user_data = $buildSet";
            }

            $sql = "update tbl_company set {$sendMailTimeUpdateSql},{$mailTimeUpdateSql},{$orderTimeUpdateSql} $set $recentFollowUpTime
where company_id=$companyId and  $subWhere";
            $db->createCommand($sql)->execute();

            $sql = "update tbl_customer set {$sendMailTimeUpdateSql},{$mailTimeUpdateSql},{$orderTimeUpdateSql} $set
where customer_id=$customerId and $subWhere";
            $db->createCommand($sql)->execute();

        }else{
            $set = '';
            if ($userId)
            {
                $buildSet = Helper::buildUserData('user_data', $userId, ['mail_time' => $mailTime]);
                $set = ",user_data = $buildSet";
            }

            $sql = "update tbl_company set send_mail_time='$mailTime',mail_time='$mailTime' $set $recentFollowUpTime
where company_id=$companyId and client_id=$clientId and send_mail_time < '$mailTime'";
            $db->createCommand($sql)->execute();

            $sql = "update tbl_customer set send_mail_time='$mailTime',mail_time='$mailTime' $set
where customer_id=$customerId and client_id=$clientId and send_mail_time < '$mailTime'";

            $db->createCommand($sql)->execute();
        }

        //跟进时间更新，消除3天要移入公海object_id
        \common\library\todo\Helper::updateFeedStatusByObjectId($clientId, $userId, TodoConstant::OBJECT_TYPE_COMPANY, TodoConstant::TODO_TYPE_COMPANY_MOVE_TO_PUBLIC_IN_THREE_DAYS, [$companyId]);

        if (CustomerOptionService::checkRecentFollowUpReference($clientId, 'refer_follow_send_mail'))
        {
            \common\library\task\Helper::finishTask($clientId, [$companyId]);
        }

        \common\library\performance_v2\Helper::runPerformance($clientId, \Constants::TYPE_COMPANY, $companyId);

        (new SwarmService($clientId))->refreshByRefer($companyId);
    }

    public static function updateReceiveMailTime($clientId, $userId, $companyId, $customerId,$receiveMailTime = null)
    {

        if(empty($receiveMailTime)) {
            $receiveMailTime = date('Y-m-d H:i:s');
        }

        $db = \PgActiveRecord::getDbByClientId($clientId);

        if (\CustomerOptionService::checkReference($clientId, 'refer_receive_mail'))
        {
            $set = '';
            $subWhere = " client_id=$clientId ";
            if ($userId)
            {
                //将非客户跟进人与客户联系人的往来邮件生成客户动态的配置开关-关闭后要检查跟进人才更新order_time
                $dynamicTrailCheck = \common\library\customer\Helper::DynamicTrailCheck($clientId);
                if( !$dynamicTrailCheck )
                {
                    $subWhere .= " and (user_id='{}' or user_id && ARRAY[$userId]::bigint[])";
                }
                $buildSet = Helper::buildUserData('user_data', $userId, ['order_time' => $receiveMailTime, 'mail_time' => $receiveMailTime], 'refer_receive_mail');
                $set = ",user_data = $buildSet";
            }

            $sql = "UPDATE tbl_company
					SET receive_mail_time = CASE WHEN receive_mail_time < '$receiveMailTime' THEN '$receiveMailTime' ELSE receive_mail_time END,
					    mail_time         = CASE WHEN mail_time < '$receiveMailTime' THEN '$receiveMailTime' ELSE mail_time END,
					    order_time        = CASE WHEN order_time < '$receiveMailTime' THEN '$receiveMailTime' ELSE order_time END
					     $set where company_id=$companyId and  $subWhere";

            $result = $db->createCommand($sql)->execute();

	        LogUtil::info(('user_id:'.$userId.'$result:'.$result.'$sql:'.$sql));

            $sql = "UPDATE tbl_customer
					SET receive_mail_time = CASE WHEN receive_mail_time < '$receiveMailTime' THEN '$receiveMailTime' ELSE receive_mail_time END,
					    mail_time         = CASE WHEN mail_time < '$receiveMailTime' THEN '$receiveMailTime' ELSE mail_time END,
					    order_time        = CASE WHEN order_time < '$receiveMailTime' THEN '$receiveMailTime' ELSE order_time END
					     $set where customer_id=$customerId and  $subWhere";
            $db->createCommand($sql)->execute();

            //跟进时间更新，消除3天要移入公海object_id
            \common\library\todo\Helper::updateFeedStatusByObjectId($clientId, $userId, TodoConstant::OBJECT_TYPE_COMPANY, TodoConstant::TODO_TYPE_COMPANY_MOVE_TO_PUBLIC_IN_THREE_DAYS, [$companyId]);

        }
        else
        {
            $set = '';
            if ($userId)
            {
                $buildSet = Helper::buildUserData('user_data', $userId, ['mail_time' => $receiveMailTime]);
                $set = ",user_data = $buildSet";
            }

            $sql = "update tbl_company set receive_mail_time='$receiveMailTime',mail_time='$receiveMailTime' $set
where company_id=$companyId and client_id=$clientId and receive_mail_time < '$receiveMailTime'";

	        $result = $db->createCommand($sql)->execute();

	        LogUtil::info(('user_id:'.$userId.'$result2:'.$result.'$sql2:'.$sql));


	        $sql = "update tbl_customer set receive_mail_time='$receiveMailTime',mail_time='$receiveMailTime' $set
where customer_id=$customerId and client_id=$clientId and receive_mail_time < '$receiveMailTime'";
            $db->createCommand($sql)->execute();
        }

        \common\library\performance_v2\Helper::runPerformance($clientId, \Constants::TYPE_COMPANY, $companyId);

        (new SwarmService($clientId))->refreshByRefer($companyId, ['receive_mail_time','mail_time', 'order_time']);
    }

    /**
     * 更新询盘消息时间、最近发送/接收tm消息时间
     * @param $clientId
     * @param $companyIds
     * @param $messageTime
     * @param int $tradeType
     * @param int $messageType
     * @return void
     * @throws \CDbException
     * @throws \ProcessException
     */
    public static function updateTradeAndTmTime($clientId, $companyIds, $messageTime = null, int $tradeType = 0, int $messageType = 0)
    {
        if (empty($messageTime)) {
            $messageTime = date('Y-m-d H:i:s');
        }
        //区分询盘
        $updateTradeTimeFlag = 0;
        if($tradeType == 1 && in_array($messageType,[\common\library\alibaba\Constant::MESSAGE_TYPE_IM, \common\library\alibaba\Constant::MESSAGE_TYPE_ASSIGN_IM_IMQUIRY,\common\library\alibaba\Constant::MESSAGE_TYPE_TRANSFER_IM_IMQUIRY])){
            $updateTradeTimeFlag = 1;
        }

        //消费队列有时消费顺序会有问题，当前消息时间比数据库消息时间更大时才更新
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $companyIdStr = implode(',',$companyIds);
        $updateSql = "UPDATE tbl_company
					SET latest_receive_ali_trade_time = CASE WHEN latest_receive_ali_trade_time < '$messageTime' AND {$updateTradeTimeFlag} = 1 THEN '$messageTime' ELSE latest_receive_ali_trade_time END,
					    latest_receive_ali_tm_time         = CASE WHEN latest_receive_ali_tm_time < '$messageTime' AND {$tradeType} = 1 THEN '$messageTime' ELSE latest_receive_ali_tm_time END,
					    latest_send_ali_tm_time        = CASE WHEN latest_send_ali_tm_time < '$messageTime' AND {$tradeType} = 2 THEN '$messageTime' ELSE latest_send_ali_tm_time END
					     where company_id in($companyIdStr)";

        $db->createCommand($updateSql)->execute();

        \common\library\performance_v2\Helper::runPerformance($clientId, \Constants::TYPE_COMPANY, $companyIds);

        (new SwarmService($clientId))->refreshByRefer($companyIds, [
            'latest_receive_ali_trade_time',
            'latest_send_ali_tm_time',
            'latest_receive_ali_tm_time'
        ]);

        \LogUtil::info("[ updateTradeAndTmTime ] client_id: {$clientId}, companyIds:".json_encode($companyIds));

    }

    /**
     * 更新 客户联系人 && 线索联系人 触达状态（状态、时间、次数、成功次数）
     * @param $clientId *mail/edm 的client_id
     * @param $userId *mail/edm 的user_id
     * @param $relateId *** mail 的mail_id 或者 edm的 task_id
     * @param $emails
     * @param $reachStatus
     * @param $updateReachStatusTime
     * @param $addReachCount
     * @param $addSuccessCount
     * @param $reachType *mail/edm
     * @return void
     */
    public static function updateReachStatus($clientId, $userId, $relateId, $emails, $reachStatus = null, $updateReachStatusTime = false, $addReachCount = false, $addSuccessCount = false, $reachType = 'mail', $reachStatusTime = null)
    {
        $emails = \common\library\email\Util::findAllEmails($emails);
        $customerMap = \common\library\customer\Helper::getCustomersByEmails($clientId,$emails);
        $customerIds = array_column($customerMap, 'customer_id');
        $companyIds = array_column($customerMap, 'company_id');
        //更新客户联系人触达状态
        if (!empty($customerMap)) {
            \common\library\customer\Helper::updateCustomerReachStatus($clientId, $userId, $companyIds, $customerIds,
                $relateId,$reachStatus,$updateReachStatusTime,$addReachCount,$addSuccessCount, $reachType, $reachStatusTime);
        }


        $leadCustomerMap = \common\library\lead\Helper::getLeadCustomersByEmails($clientId,$emails);
        //已转化的线索不处理
        $leadCustomerMap = array_filter($leadCustomerMap, function($item) {
            return ($item['company_id'] ?? 0) == 0;
        });
        $leadCustomerIds = array_column($leadCustomerMap, 'customer_id');
        $leadIds = array_column($leadCustomerMap, 'lead_id');

        //更新线索联系人触达状态
        if (!empty($leadCustomerMap)) {
            \common\library\lead\Helper::updateLeadCustomerReachStatus($clientId, $userId, $leadIds, $leadCustomerIds,
                $relateId,$reachStatus,$updateReachStatusTime,$addReachCount,$addSuccessCount, $reachType);
        }
    }

    /**
     * 更新联系人触达状态（状态、时间、次数、成功次数）
     * @param $clientId
     * @param $userId
     * @param $companyId
     * @param $customerId
     * @param $relateId
     * @param $reachStatus
     * @param $reachStatusTime
     * @param $addReachCount
     * @param $addSuccessCount
     * @param $reachType *mail/edm
     * @return void
     */
    public static function updateCustomerReachStatus(
        $clientId,
        $userId,
        $companyIds,
        $customerIds,
        $relateId,
        $reachStatus = null,
        $updateReachStatusTime = false,
        $addReachCount = false,
        $addSuccessCount = false,
        $reachType = 'mail',
        $reachStatusTime = null,
    )
    {
        if (empty($customerIds)) return;
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $set = '';
        $reachStatusSet = '';

        if ($reachStatus && $updateReachStatusTime) {
            $reachStatusTime = $reachStatusTime ?: date('Y-m-d H:i:s');
            $updateReachStatusCustomerIds = $db->createCommand("select customer_id from tbl_customer where client_id = $clientId and customer_id in (" . implode(',', $customerIds) . ") and reach_status_time < '$reachStatusTime'")->queryColumn();
            if (!empty($updateReachStatusCustomerIds)) {
                $reachStatusSet .= ",reach_status = $reachStatus, reach_status_time = '$reachStatusTime'";
            }
        }

        if ($addReachCount) {
            $set .= ",reach_count = reach_count + 1";
        }

        if ($addSuccessCount) {
            $set .= ",reach_success_count = reach_success_count + 1";
        }

        $subWhere = '';
        if ($userId)
        {
            if ($reachType == 'mail') {
                //将非客户跟进人与客户联系人的往来邮件生成客户动态的配置开关-关闭后要检查跟进人
                $dynamicTrailCheck = \common\library\customer\Helper::DynamicTrailCheck($clientId);
                if (!$dynamicTrailCheck) {
                    $subWhere .= " and (user_id='{}' or user_id && ARRAY[$userId]::bigint[])";
                }
            } elseif ($reachType == 'edm') {
                //edm只匹配个人私海里面的客户联系人
                $subWhere .= " and user_id && ARRAY[$userId]::bigint[]";
            }

            $timeKey = match($reachStatus) {
                \common\library\customer_v3\customer\orm\Customer::MAIL_REACH_STATUS_SENDED => 'reach_delivery_time',
                \common\library\customer_v3\customer\orm\Customer::MAIL_REACH_STATUS_OPEN => 'reach_open_time',
                default => 'reach_sending_time'
            };
            $buildSet = \common\library\customer\Helper::buildUserData('user_data', $userId, [$timeKey => date('Y-m-d H:i:s')]);
            $set .= ",user_data = $buildSet";
        }

        if ($reachStatusSet) {
            $updateReachStatusSql = "update tbl_customer set reach_relate_id=$relateId $reachStatusSet where  customer_id in (" . implode(',', $updateReachStatusCustomerIds) . ") and client_id=$clientId $subWhere";
            $db->createCommand($updateReachStatusSql)->execute();
            LogUtil::info('【updateCustomerReachStatusTime】', ['reachStatus' => $reachStatus, 'updateReachStatusTime' => $updateReachStatusTime, 'set' => $reachStatusSet, 'customerIds' => $updateReachStatusCustomerIds]);
        }

        //跟进人需要加入Where条件
        $sql = "update tbl_customer set reach_relate_id=$relateId $set where  customer_id in (" . implode(',', $customerIds) . ") and client_id=$clientId $subWhere";
        LogUtil::info('【updateCustomerReachStatus】 addReachCount='.$addReachCount.'addSuccessCount='.$addSuccessCount.' set='.$set.'relate_id='.$relateId.'subWhere='.$subWhere.'customerIds='.json_encode($customerIds).'companyIds='.json_encode($companyIds));
        $db->createCommand($sql)->execute();
        (new SwarmService($clientId))->refreshByRefer($companyIds, ['reach_status','reach_status_time']);
        //更新ES
        \common\library\server\es_search\SearchQueueService::pushCompanyQueue(PrivilegeService::getInstance($clientId)->getAdminUserId(), $clientId, $companyIds, \Constants::SEARCH_INDEX_TYPE_UPDATE);
    }

    public static function buildUserData($column, $userId, array $data, $refer = null)
    {
        if ($refer) {
            $data['refer'] = $refer;
        }
        $json = json_encode($data);

        if (is_array($userId))
        {
            $sql = '#placeholder#';
            // jsonb_set(user_data, '{"456"}', (CASE WHEN user_data->'456' is null then '{}' else user_data->'456' end) || '{"order_time": 123}')
            foreach ($userId as $singleUserId)
            {
                $sql = str_replace('#placeholder#', "jsonb_set(#placeholder#, '{\"{$singleUserId}\"}', (CASE WHEN $column->'{$singleUserId}' is null then '{}' else $column->'{$singleUserId}' end) || '$json')", $sql);
            }
            return str_replace('#placeholder#', $column, $sql);

        }
        else
        {
            // CASE WHEN user_data->'765' is null then user_data || '{"765": {"order_time": 123}}'::jsonb else jsonb_set(user_data, '{"765"}', user_data->'765' || '{"order_time": 123}')

            $set = "jsonb_set($column, '{\"$userId\"}', $column->'$userId' || '$json')";

            return "CASE WHEN {$column}->'{$userId}' is null then {$column} || '{\"$userId\": $json}'::jsonb else $set end";
        }
    }


    public static function getExternalFieldSetting($clientId, $type, $userId = 0, array $fields = [], $excludeDeleted = 0)
    {
        static $map = [];

        if (isset($map[$clientId][$type])) {
            return $map[$clientId][$type];
        }
        if (!$fields) {
            $fields = ["id", "base", "name", "require", "hint", "field_type", "disable_flag", 'type','group_id', 'ext_info'];
        }
        $custom_field_list = new FieldList($clientId);

        $custom_field_list->setFields($fields);
        $custom_field_list->setType($type);
        $custom_field_list->setBase(0);
        if ($userId) {
            $custom_field_list->setPrivilegeInfo($userId);
            $custom_field_list->setHideValueForDisable(true);
        }
        if ($excludeDeleted)
            $custom_field_list->setEnableFlag(1);

        $fields = $custom_field_list->find();

//        $custom_field_service = new CustomFieldService($clientId, $type);
//        $fields = $custom_field_service->fieldList(0);

        $result = [];
        foreach ($fields as $field)
            $result[] = $field + ['value' => ''];

        $map[$clientId][$type] = $result;

        return $result;
    }

    //已废弃 使用CustomerField\LeadCustomerField
    public static function getCustomerField($clientId, $data = null, $type = Constants::TYPE_CUSTOMER, $formatValue = false)
    {
        return [
            'customer_id' => 0,
            'company_id' => 0,
            'email' => '',
            'name' => '',
            'main_customer_flag' => 0,
            'fields' => self::getCustomerFormatField($clientId, $data, $type, $formatValue),
        ];
    }
    //已废弃 使用CustomerField\LeadCustomerField
    public static function getCustomerFormatField($clientId, $data = null, $type = Constants::TYPE_CUSTOMER, $formatValue = false)
    {
        static $map = [];

        if (!isset($map[$clientId]))
        {
            $custom_field_list = new FieldList($clientId);
            $custom_field_list->setType($type);
            $custom_field_list->setFields(['id', 'name', 'base', 'require', 'hint', 'field_type', 'ext_info', 'disable_flag', 'is_editable','default']);
            $custom_field_list->setEnableFlag(1);
            $fields = $custom_field_list->find();
//            $custom_field_service = new CustomFieldService($clientId, Constants::TYPE_CUSTOMER);
//            $fields = $custom_field_service->fieldList();

            $result = [];
            foreach ($fields as $field)
            {
                if($field['base'] ==1 && $field['id'] == 'tel') {
                    $value = ['tel_area_code' => '', 'tel' => ''];
                }elseif($field['base'] ==1 && $field['id'] == 'tel_list'){
                    $value = [];
                }elseif($field['base'] ==1 && $field['id'] == 'image_list') {
                    $value = [];
                } elseif ($field['base'] == 1 && $field['id'] == 'main_customer_flag' && Constants::TYPE_LEAD_CUSTOMER == $type && $formatValue) {
                    $field['disable_flag'] = (string) 1;
                    $value = '';
                }else{
                    $value = '';
                }
                if ($formatValue) {
                    $field['format'] = is_array($value) ? json_encode($value) : $value;
                }

                $result[] = $field + ['group_id' => 0, 'value' => $value];
            }

            $map[$clientId] = $result;
        }

        if ($data === null)
            return $map[$clientId];

        $result = $map[$clientId];

        foreach ($result as &$field)
        {
            if ($field['base'] == 1)
            {
                if ($field['id'] == 'tel')
                {
                    $tel_area_code = $data['tel_area_code'] ?? '';
                    $tel = $data['tel'] ?? '';
                    $field['value'] = ['tel_area_code' => $tel_area_code, 'tel' => $tel];
                }
                else if ($field['id'] == 'tel_list') // 没跑数据前的特殊处理
                {
                    if (!empty($data['tel']) || !empty($data['tel_area_code']))
                        $data['tel_list'][] = [$data['tel_area_code'], $data['tel']];
                    $field['value'] = $data['tel_list'] ?? '';
                }
                else if ($field['id'] == 'full_tel_list')
                {
                    if (!empty($data['tel_full']))
                        $data['full_tel_list'][] = $data['tel_full'];
                    $field['value'] = $data['full_tel_list'] ?? '';
                }
                else if($field['id'] == 'image_list')
                {
                    $field['value'] = self::formatImageList($data['image_list'] ?? []);
                }
                else
                {
                    $field['value'] = $data[$field['id']] ?? '';
                }
            }
            else
            {
                $field['value'] = $data['external_field_data'][$field['id']] ?? '';
            }

            if ($formatValue) {
                $field['format'] = is_array($field['value']) ? json_encode($field['value']) : $field['value'];
            }
        }

        return $result;
    }

    public static function getGroupCount($clientId, $userId, $userNum=[0,1,2])
    {
        if(!$userId)
            $userId = \User::getLoginUser()->getUserId();

        $list = new CompanyList($userId);

        $list->setUserNum($userNum);

        [$where, $params] = $list->buildParams();

        $db = \PgActiveRecord::getDbByClientId($clientId);
    
        $from = $list->getFrom();

        $result = $db->createCommand("select group_id, count(1) $from $where GROUP BY group_id")->queryAll(false, $params);

        return array_combine(array_column($result, 0), array_column($result, 1));
    }
    
    
    /**
     * @depracated
     */
    public static function getCustomerCountByGroup($clientId, $userId, $groupType)
    {
        if (empty($clientId) || empty($userId)) return [];
        if (!in_array($groupType, ['pool_id', 'group_id', 'origin', 'trail_status', 'star', 'biz_type'])) return [];

        $list = new CompanyList($userId);
        switch ($groupType) {
            case 'pool_id':
                $list->setUserNum([0]);
                break;
            case 'group_id':
                $list->setUserNum([1,2]);
                break;
            default:
                break;
        }
        [$where, $params] = $list->buildParams();
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $table = \CompanyModel::getModelTableName();
        $result = $db->createCommand("select $groupType, count(1) from $table where $where GROUP BY $groupType")->queryAll(false, $params);
        return array_combine(array_column($result, 0), array_column($result, 1));
    }

    /**
     * 格式化图片列表
     * @param array $list
     * @return array
     */
    public static function formatImageList(array $list){
        $imageList = [];
        $objArray = \UploadFile::findByIds($list);
        //需要保证图片的顺序和db里面的顺序保持一致
        $fileObjArray = array_column($objArray, null, 'file_id');
        if (!empty($objArray)) {
            foreach ($list as $file_id) {
                if(isset($fileObjArray[$file_id])) {
                    $file = new \AliyunUpload();
                    $file->loadByObject($fileObjArray[$file_id]);
                    $elem['file_id'] = $file->getFileId();
                    $elem['file_name'] = $file->getFileName();
                    $elem['file_size'] = $file->getFileSize();
                    $elem['file_path'] = $file->getFileUrl();
                    $elem['file_preview_url'] = $file->getPreview();

                    $imageList[] = $elem;
                }
            }
        }
        return $imageList;
    }

    public static function getDiscoveryCompanyFiled($clientId, $userId, $discoveryCompanyId, CompanyField $companyFieldFormatter = null, CustomerField $customerFieldFormatter = null, $includePosition = [], $page = 1, $page_size = 40, $qualityFlag = false)
    {
        $companyService = new \common\library\discovery\CompanyService($clientId, $userId, $discoveryCompanyId);
        $result['data']['info'] = $companyService->getCompanyProfile();
        $company = $result['data']['info'];

        $contactList = new ContactsList($clientId, $userId);
        $contactList->setCompanyHashId($discoveryCompanyId);
        $contactList->setIncludeNotEmail(false);
        $contactList->setPage($page);
        $contactList->setSize($page_size);
        $contactList->setIncludePositions($includePosition);
        $companyUnrelated = new \common\library\user_company_unrelated\UserCompanyUnrelated($clientId, $userId, $discoveryCompanyId);
        $relatedCompanyHashIds = $companyUnrelated->getRelatedCompanyHashIds();
        $contactList->setChildCompanyHashIds($relatedCompanyHashIds);

        $okkiLeadsFlag = \common\library\privilege_v3\PrivilegeService::getInstance($clientId, $userId)
            ->hasFunctional(\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_OKKI_LEADS_BASE);
        if($okkiLeadsFlag){   // leads版本和非leads查看数据范围不一致
            $contactList->setDefaultSortMode("v2");
            $contactList->setIsNewVersion(true);
        }

        $contactList = $contactList->find();
        $result['data']['emailList'] = $contactList;

        //规模//TODO需要统一到常量
        $scaleMap = CustomerOptionService::SCALE_MAP;
        $scaleId = 0;
        $scale = $company['employees'] ?? 0;
        if($scale){
            foreach ($scaleMap as $key => $item) {
                if($scale >= $item['min'] && $scale <= $item['max']){
                    $scaleId = $key;
                    break;
                }
            }
        }

        //来源
        $originId = \common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_DISCOVERY;
        $okkiLeadsFlag = \common\library\privilege_v3\PrivilegeService::getInstance($clientId, $userId)
            ->hasFunctional(\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_OKKI_LEADS_BASE);
        if($okkiLeadsFlag){
            $originId = \common\library\setting\library\origin\Origin::SYS_ORIGIN_ID_OKKI_LEADS;
        }

        //国家
        $country = $company['country'] ?? '';
        if (!empty($country)){
            $country = strtolower($country);
            if ($country === 'the netherlands'){//dx和crm中country不一致，后续切换数据源之后改用alpha2(2018-10-31)
                $country = 'netherlands';
            }
        }
        $country = $country ? (\CountryService::checkNameInTable($country)['alpha2'] ?? '') : '';
        $address = '';
        if ($company) {
            $address = $company['address'];
            $city = $company['city'] ? ',' . $company['city'] : '';
            $province = $company['province'] ? ',' . $company['province'] : '';
            $postcode = $company['postcode'] ? ',' . $company['postcode'] : '';
            $countryName = $company['countryName'] ? ','. $company['countryName']  : '';
            $address .= $city. $province. $postcode. $countryName;
        }

        $companySocial = new \common\library\discovery\api\Company($clientId, $userId);
        $socialResult = $companySocial->getSocialProfiles($discoveryCompanyId);
        $socialLinks = [];
        foreach ($socialResult ?: [] as $item) {
            $socialLinks[$item['typeId']] = [
                'type' => $item['typeId'],
                'value' => $item['url']
            ];
        }
        $socialLinks = array_values($socialLinks);

        $phone = $company['primaryPhone'] ?? '';
        $telData = TelUtil::formatTelData($phone);
        $companyData = [
            'name' => $company['companyName'] ?? '',
            'origin_list' => array_filter((array)$originId),
            'country' => $country,
            'scale_id' => $scaleId,
            'tel' => $telData['tel'],
            'tel_area_code' => $telData['tel_area_code'],
            'address' => $address,
            'homepage' => $company['homepage'] ?? '',
            'pool_id'  => 0
        ];
        if ($companyFieldFormatter) {
            $companyFieldFormatter->setFieldUserId($userId);
            $companyField = $companyFieldFormatter->format($companyData);
        } else {
            $companyFormatter = new CompanyField($clientId);
            $companyFormatter->setFieldUserId($userId);
            $companyField = $companyFormatter->format($companyData);
        }

        $customerFields = [];
        $emailList = $result['data']['emailList']['emails'] ?? [];
        $customerFieldFormat = $customerFieldFormatter ?? new CustomerField($clientId);
        $customerFieldFormat->setFieldUserId($userId);

		//邮箱质量打标签
		$qualityMap = [];
		if ($qualityFlag) {
			$allEmails = array_filter(array_column($emailList, 'value'));
			if (!empty($allEmails)) {
				$employeeApi = new Employee($clientId, $userId);
				$qualityMap = $employeeApi->getMailQualityRating($allEmails);
			}
		}

        foreach ($emailList as $contact) {
            $contactName = $contact['first_name'] . ' ' . $contact['last_name'];
            $contactEmail =  $contact['value'] ?? '';

            if (!$contactEmail)
                continue;

            if (!\EmailUtil::isEmail($contactEmail))
                continue;

            [$preName, $contactEmail] = \EmailUtil::getPreFixAndEmail($contactEmail);

            $contactEmail = strtolower($contactEmail);

            // 区号
            $telList = [];
            $customerPhoneNums = $contact['phone_numbers'] ?? (!empty($contact['phone_number']) ? [$contact['phone_number']] : []);
            foreach ($customerPhoneNums as $phoneNum) {
                if (empty($phoneNum)) {
                    continue;
                }

                $phoneNumber = \Util::parsePhoneNumber((string) $phoneNum);
                $telList[] = [
                    $phoneNumber['country_code'] ?? '',
                    $phoneNumber['national_number'] ?? ltrim($phoneNumber['raw_input'], '+'),
                ];
            }

            if (isset($contact['position'])) {
                $post = $contact['position'];
            } elseif (isset($contact['type']) && $contact['type'] == 'personal') {
                $post = 'Staff';
            } else {
                $post = 'Department';
            }

            $customerData = [
                'name' => $contactName != ' ' ? $contactName : $preName,
                'email' => $contactEmail,
                'tel_list' => $telList,
                'post' => $post,
            ];

			$tmpCustomerFields = $customerFieldFormat->format($customerData);
			if ($qualityFlag) {
				$tmpCustomerFields['quality_flag'] = in_array($qualityMap[$contactEmail] ?? 0, [\Mail::MAIL_LEVEL_GOOD, \Mail::MAIL_LEVEL_GENERAL]);
			}
            $customerFields[] = $tmpCustomerFields;

        }
        if(empty($customerFields)) {
            $customerFields[] = $customerFieldFormat->format();
        }

        return [$companyField, $customerFields, $socialLinks];
    }

    public static function checkFieldArchiveInfo($clientId,$field, $value,$company_id = 0)
    {
        $value = trim($value);
        $company = new Company($clientId);
        $filterValue = [];
        $count = 0;
        $result = false;
        $data = [
            'result' => false,
            'count' => 0,
            'code' => 0
        ];

        if ($field == 'email') {

            $field = 'customer_list.email';

            $atPos = strpos($value, '@');
            if ($atPos === false)
                throw  new \RuntimeException(\Yii::t('mail', 'Not an email address'));

            $value = substr($value, $atPos + 1);

            //过滤公共邮箱
            if (\common\library\email\CommonDomain::check($value))
                return $data;

            if ($company_id) {
                $company->loadById($company_id);
                $company->getFormatter()->setShowCustomer(true);
                $companyInfo = $company->getAttributes();
                foreach ($companyInfo['customers'] ?? [] as $customer) {

                    $atPos = strpos($customer['email'], '@');
                    if ($atPos === false)
                        continue;

                    $filterValue[] = substr($customer['email'], $atPos + 1);
                }
            }

        } elseif ($field == 'homepage') {

            preg_match('/(?:https?:\/\/)?([^\/?]+)/i', $value, $matchs);
            $value = $matchs[1] ?? '';
            if (empty($value))
                throw new \RuntimeException(\Yii::t('customer', 'Address is incorrect'));

            if ($company_id) {

                $company->loadById($company_id);
                $companyInfo = $company->getAttributes();
                preg_match('/(?:https?:\/\/)?([^\/?]+)/i', trim($companyInfo['homepage']), $companyMatchs);
                $homepage = $companyMatchs[1] ?? '';

                if (!empty($homepage))
                    $filterValue[] = $homepage;
            }

        } else {
            throw  new \RuntimeException(\Yii::t('customer', 'Unsupported fields'));
        }

        $match = [
            'query' => $value,
            'analyzer' => SearchConstant::BUILD_IN_ANALYZER_KEYWORD,
        ];

        if (SearchConstant::enableCompanySearch($clientId ,SearchConstant::COMPANY_SEARCH_SCENE_FIELD_UNIQUE_CHECK)) {
            $count = SearchApi::company($clientId)->countFieldArchiveInfo($field, $value, array_filter((array)$company_id));
        } else {
            $companySearcher = new CompanySearchList();
            $companySearcher->setClientId($clientId);
            $companySearcher->setMatchType(CompanySearchList::MATCH_TYPE_PHRASE);
            $companySearcher->setQueryFields([$field => $match]);
            $count = $companySearcher->count();
        }

        $result = $count > 0 ? true : false;

        $data['code'] = $result ? ($field == 'customer_list.email' ? \ErrorCode::CODE_APPLY_ARCHIVE_DOMAIN_EXIST : \ErrorCode::CODE_APPLY_ARCHIVE_HOMEPAGE_EXIST) : 0;

        //如果是自己的资料，被别人建档也提示
        if (in_array($value, $filterValue)) {
            // 如果是同一家客户有相同后缀的联系人邮箱则不提示
            $count = 1;
            $result = true;
            if ($count == 1) {
                if ($field == 'customer_list.email') {
                    $data['code'] = 0;
                    $result = false;
                    $count = 0;
                }
            } elseif ($count > 1) {
                if ($field == 'customer_list.email') {
                    $data['code'] = \ErrorCode::CODE_APPLY_ARCHIVE_DOMAIN_EXIST;
                } elseif ($field == 'homepage') {
                    $data['code'] = \ErrorCode::CODE_APPLY_ARCHIVE_HOMEPAGE_EXIST;
                }
            }
        }

        $data['result'] = $result;
        $data['count'] = $count;

        return $data;

    }

    public static function getUserSearchSettingBYModule($userId, $module)
    {
        $searchFieldSettingMap = [
            Constants::TYPE_COMPANY => [
                [
                    'id' => UserInfoExternal::EXTERNAL_KEY_COMPANY_SEARCH_FILED,
                    'type' => Constants::TYPE_COMPANY,
                ],
                [
                    'id' => UserInfoExternal::EXTERNAL_KEY_CUSTOMER_SEARCH_FILED,
                    'type' => Constants::TYPE_CUSTOMER,
                ]
            ],
            Constants::TYPE_LEAD => [
                [
                    'id' => UserInfoExternal::EXTERNAL_KEY_LEAD_SEARCH_FILED,
                    'type' => Constants::TYPE_LEAD,
                ],
                [
                    'id' => UserInfoExternal::EXTERNAL_KEY_LEAD_CUSTOMER_SEARCH_FILED,
                    'type' => Constants::TYPE_LEAD_CUSTOMER,
                ]
            ],
            Constants::TYPE_OPPORTUNITY => [
                [
                    'id' => UserInfoExternal::EXTERNAL_KEY_OPPORTUNITY_EXTERNAL_SEARCH_FIELD,
                    'type' => Constants::TYPE_OPPORTUNITY,
                ]
            ]
        ];
        $ret = [];
        $searchFieldSetting = $searchFieldSettingMap[$module] ?? [];
        $user = \User::getUserObject($userId);
        $clientId = $user->getClientId();
        $attrKeys = array_column($searchFieldSetting, 'id');
        $attrs = $user->getInfoObject()->getExtentAttributes($attrKeys);
        foreach ($searchFieldSetting as $item) {
            $itemId = $item['id'];
            $itemType = $item['type'];
            $searchFields = $attrs[$itemId];
            $searchFields = array_filter(is_array($searchFields) ? $searchFields : [$searchFields], 'is_numeric');
            if (!empty($searchFields)) {
                $fieldListObj = new FieldList($clientId);
                $fieldListObj->setType($itemType);
                $fieldListObj->setId($searchFields);
                $fieldListObj->setDisableFlag(0);
                $fieldListObj->setBase(0);
                $searchSetting = $fieldListObj->find();
                foreach ($searchSetting as &$settingItem) {
                    $settingItem['match_type'] = 'term';
                    $settingItem['field_id'] = $settingItem['id'];
                }
                $ret = array_merge($ret, $searchSetting);
            }
        }
        return $ret;
    }

    public static function getUserSearchSetting($userId, $module = Constants::TYPE_COMPANY, $isExport = false,$fields = []){
        [$attrKeys, $companyType, $customerType] = static::getUserSearchSettingKeys($module, $isExport);
        $user = \User::getUserObject($userId);
        $clientId = $user->getClientId();
        $companySearchSetting =[];
        $customerSearchSetting = [];
        $attrs = $user->getInfoObject()->getExtentAttributes($attrKeys);
        $companySearchFiled = $attrs[$attrKeys[0]];
        $companySearchFiled = array_filter(is_array($companySearchFiled) ? $companySearchFiled : [$companySearchFiled], 'is_numeric');

        if(!empty($companySearchFiled)) {
            $fieldListObj = new FieldList($clientId);
            $fieldListObj->setType($companyType);
            $fieldListObj->setId($companySearchFiled);
            $fieldListObj->setDisableFlag(0);
            $fieldListObj->setBase(0);
            if($fields){
                $fieldListObj->setFields($fields);
            }
            $companySearchSetting = $fieldListObj->find();
            foreach ( $companySearchSetting as &$companyItem ){
                $companyItem['match_type'] = 'term';
                $companyItem['field_id'] = $companyItem['id'];
            }
        }
        $customerSearchFiled = $attrs[$attrKeys[1]];
        $customerSearchFiled = array_filter(is_array($customerSearchFiled) ? $customerSearchFiled : [$customerSearchFiled], 'is_numeric');
        if(!empty($customerSearchFiled)) {
            $fieldListObj = new FieldList($clientId);
            $fieldListObj->setType($customerType);
            $fieldListObj->setId($customerSearchFiled);
            $fieldListObj->setDisableFlag(0);
            $fieldListObj->setBase(0);
            if($fields){
                $fieldListObj->setFields($fields);
            }
            $customerSearchSetting = $fieldListObj->find();
            foreach ( $customerSearchSetting as &$customerItem ){
                $customerItem['match_type'] = 'term';
                $customerItem['field_id'] = $customerItem['id'];
            }
        }

        return ['company'=>$companySearchSetting,'customer'=>$customerSearchSetting];

    }

    protected static function getUserSearchSettingKeys($module, $export = false)
    {
        $exportPrefix = 'export_';
        $attrKeysSetting = [
            Constants::TYPE_COMPANY => [
                UserInfoExternal::EXTERNAL_KEY_COMPANY_SEARCH_FILED,
                UserInfoExternal::EXTERNAL_KEY_CUSTOMER_SEARCH_FILED,
            ],
            Constants::TYPE_LEAD => [
                UserInfoExternal::EXTERNAL_KEY_LEAD_SEARCH_FILED,
                UserInfoExternal::EXTERNAL_KEY_LEAD_CUSTOMER_SEARCH_FILED,
            ],
            $exportPrefix . Constants::TYPE_LEAD => [
                UserInfoExternal::EXTERNAL_KEY_LEAD_EXPORT_FILED,
                UserInfoExternal::EXTERNAL_KEY_LEAD_CUSTOMER_EXPORT_FILED,
            ],
        ];

        $companyTypeSetting = [
            Constants::TYPE_COMPANY => Constants::TYPE_COMPANY,
            Constants::TYPE_LEAD    => Constants::TYPE_LEAD,
        ];
        $customerTypeSetting = [
            Constants::TYPE_COMPANY => Constants::TYPE_CUSTOMER,
            Constants::TYPE_LEAD    => Constants::TYPE_LEAD_CUSTOMER,
        ];

        return [$attrKeysSetting[$export ? $exportPrefix . $module : $module], $companyTypeSetting[$module], $customerTypeSetting[$module]];
    }

    public static function getUserSearchSettingByLighthouse($userId, $module = Constants::TYPE_COMPANY, $isExport = false,$fields = [])
    {
        [$attrKeys, $companyType, $customerType] = static::getUserSearchSettingKeys($module, $isExport);
        $user = \User::getUserObject($userId);
        $clientId = $user->getClientId();
        $companySearchSetting =[];
        $customerSearchSetting = [];
        $attrs = $user->getInfoObject()->getExtentAttributes($attrKeys);
        $companySearchFiled = $attrs[$attrKeys[0]];

        if(!empty($companySearchFiled)) {
            $fieldListObj = new FieldList($clientId);
            $fieldListObj->setType($companyType);
            $fieldListObj->setId($companySearchFiled);
            $fieldListObj->setDisableFlag(0);
            if($fields){
                $fieldListObj->setFields($fields);
            }
            $companySearchSetting = $fieldListObj->find();
            foreach ( $companySearchSetting as &$companyItem ){
                $companyItem['match_type'] = 'term';
                $companyItem['field_id'] = $companyItem['id'];
            }
        }
        $customerSearchFiled = $attrs[$attrKeys[1]];
        $customerSearchFiled = array_filter(is_array($customerSearchFiled) ? $customerSearchFiled : [$customerSearchFiled], 'is_numeric');
        if(!empty($customerSearchFiled)) {
            $fieldListObj = new FieldList($clientId);
            $fieldListObj->setType($customerType);
            $fieldListObj->setId($customerSearchFiled);
            $fieldListObj->setDisableFlag(0);
            if($fields){
                $fieldListObj->setFields($fields);
            }
            $customerSearchSetting = $fieldListObj->find();
            foreach ( $customerSearchSetting as &$customerItem ){
                $customerItem['match_type'] = 'term';
                $customerItem['field_id'] = $customerItem['id'];
            }
        }

        return ['company'=>$companySearchSetting,'customer'=>$customerSearchSetting];

    }

    public static function getCompanyStatusNameMap()
    {
        return [
            0 => \Yii::t('customer', '未建档'),
            EmailIdentity::CARD_TYPE_COMPANY => \Yii::t('customer', '我的客户'),
            EmailIdentity::CARD_TYPE_CAN_NOT_MANAGE_COMPANY => \Yii::t('customer', '同事客户'),
            EmailIdentity::CARD_TYPE_CAN_MANAGE_COLLEAGUES_COMPANY => \Yii::t('customer', '同事客户'),
            EmailIdentity::CARD_TYPE_PUBLIC_COMPANY => \Yii::t('customer', '公海客户'),
            EmailIdentity::CARD_TYPE_CAN_NOT_MANAGE_PUBLIC_COMPANY => \Yii::t('customer', '公海客户'),
            EmailIdentity::CARD_TYPE_LEAD => \Yii::t('customer', '我的线索'),
            EmailIdentity::CARD_TYPE_COLLEAGUE_LEAD => \Yii::t('customer', '同事线索'),
            EmailIdentity::CARD_TYPE_PUBLIC_LEAD => \Yii::t('customer', '公海线索'),
        ];
    }

    public static function getIsContactArchiveCompany($clientId, $companyId, $emails): bool
    {
        $customerList = new CustomerList($clientId);
        $customerList->setCompanyId($companyId);
        $customerList->setEmail($emails);
        $customerList->setFields(['email']);
        $customerList->setIsArchive(\common\library\customer_v3\customer\orm\Customer::IS_ARCHIVE_YES);
        $count = $customerList->count();

        return $count > 0;
    }

    public static function getIsContactArchiveLead($clientId, $leadId, $emails): bool
    {
        $leadCustomerList = new \common\library\lead\LeadCustomerList($clientId);
        $leadCustomerList->setLeadId($leadId);
        $leadCustomerList->setEmail($emails);
        $leadCustomerList->setFields(['email']);
        $leadCustomerList->setIsArchive(\common\library\lead\Lead::ARCHIVE_OK);
        $count = $leadCustomerList->count();

        return $count > 0;
    }

    public static function getHashIdAssocStatus($userId, $hashIds)
    {
        $user = \User::getUserObject($userId);
        $clientId = $user->getClientId();
        $hashIds = array_filter($hashIds);

        $companyList = [];
        if (! empty($hashIds)) {
            //查询company列表
            $companyList = new CompanyList($userId);
            $companyList->setCompanyHashId($hashIds);
            $companyList->setSkipPrivilege(true);
            $companyList->setFields(['user_id', 'company_id', 'pool_id', 'company_hash_id', 'scope_user_ids']);
            $companyList = $companyList->find();
        }

        $CompanyHashIds = array_column($companyList, 'company_hash_id');
        $leadHashIds = array_diff($hashIds, $CompanyHashIds);

        $leadList = [];
        if (! empty($leadHashIds)) {
            $leadList = new \common\library\lead\LeadList($userId);
            $leadList->setCompanyHashId($leadHashIds);
            $leadList->setSkipPrivilege(true);
            $leadList->setStatusId([    //过滤ai sdr线索
                Status::SYS_STATUS_NONE,
                Status::SYS_STATUS_NEW,
                Status::SYS_STATUS_INVALID,
                Status::SYS_STATUS_COMPLETE_INFO,
                Status::SYS_STATUS_INITIATIVE_TO_CONTACT,
                Status::SYS_STATUS_INTERKNIT,
                Status::SYS_STATUS_CONVERSION,
                Status::SYS_STATUS_AI_STATUS,
                Status::SYS_STATUS_DUPLICATE
            ]);
            $leadList->setFields(['user_id', 'lead_id', 'company_hash_id']);
            $leadList = $leadList->find();
        }

        $weight = [
            EmailIdentity::CARD_TYPE_STRANGER => 0, //无权限客户
            EmailIdentity::CARD_TYPE_PUBLIC_LEAD => 1, //公海线索
            EmailIdentity::CARD_TYPE_COLLEAGUE_LEAD => 2,//同事线索
            EmailIdentity::CARD_TYPE_LEAD => 3, //我的线索
            EmailIdentity::CARD_TYPE_CAN_NOT_MANAGE_PUBLIC_COMPANY => 4, //无权限公海
            EmailIdentity::CARD_TYPE_PUBLIC_COMPANY => 5, //公海客户
            EmailIdentity::CARD_TYPE_CAN_NOT_MANAGE_COMPANY => 6,//同事客户
            EmailIdentity::CARD_TYPE_CAN_MANAGE_COLLEAGUES_COMPANY => 7, //同事客户 - 可查看
            EmailIdentity::CARD_TYPE_COMPANY => 8, //我的客户
        ];


        $map = [];
        $accessService = (new AccessService($clientId, $userId));
        //处理多条company_hash_id的情况
        foreach ($companyList as &$company) {
            $userIds = PgsqlUtil::trimArray($company['user_id']);
            $scopeUserIds = PgsqlUtil::trimArray($company['scope_user_ids']);
            $status = $accessService->setCompanyInfoData($clientId, $userIds, $company['pool_id'], $company, $scopeUserIds)->cardType(true);

            $company['status'] = $status;
            $company['weight'] = $weight[$status];
            if (! isset($map[$company['company_hash_id']]) || $map[$company['company_hash_id']]['weight'] < $company['weight']) {
                $map[$company['company_hash_id']] = $company;
            }
        }

        foreach ($leadList as &$lead) {
            $status = EmailIdentity::CARD_TYPE_PUBLIC_LEAD;
            $userIds = PgsqlUtil::trimArray($lead['user_id']);
            if (!empty($userIds)) {
                $status = EmailIdentity::CARD_TYPE_COLLEAGUE_LEAD;
                if (in_array($userId, $userIds)) {
                    $status = EmailIdentity::CARD_TYPE_LEAD;
                }
            }
            $lead['status'] = $status;
            $lead['weight'] = $weight[$status];
            if (! isset($map[$lead['company_hash_id']]) || $map[$lead['company_hash_id']]['weight'] < $lead['weight']) {
                $map[$lead['company_hash_id']] = $lead;
            }
        }

        $result = [];
        foreach ($hashIds as $hashId) {
            if (isset($map[$hashId])) {
                $item = $map[$hashId];
            }else {
                $item = [
                    'company_hash_id' => $hashId,
                    'status' => 0, //默认没有建档
                ];
            }
            $result[] = $item;
        }

        return $result;
    }

    public static function updateLatestReceiveAliTradeTime(int $clientId, $companyId, $buyerAccountIds)
    {
        $buyerAccountIds = array_filter(array_unique((array)$buyerAccountIds));

        if (empty($clientId) || empty($companyId) || empty($buyerAccountIds)) {
            return false;
        }

        $buyerAccountIdStr = implode(',', $buyerAccountIds);

        $mysqlDb = \ProjectActiveRecord::getDbByClientId($clientId);
        $summarySql = "select max(latest_trade_time) as max_latest_trade_time,max(last_receive_time) as max_last_receive_time ,max(last_send_time) as max_last_send_time from  tbl_alibaba_chat_summary where client_id = $clientId and buyer_account_id in ($buyerAccountIdStr)";
        $data = $mysqlDb->createCommand($summarySql)->queryRow();

        if (empty($data)) {
            return false;
        }

        $latestReceiveAliTradeTime = $data['max_latest_trade_time'] ?? '';
        $lastReceiveTime = $data['max_last_receive_time'] ?? '';
        $lastSendTime = $data['max_last_send_time'] ?? '';

        $setItem = '';
        //更新最新的时间 支持交易 阿里tm
        // 兼容时间处理'0000-00-00 00:00:00'
        if (!empty($latestReceiveAliTradeTime) && $latestReceiveAliTradeTime != "1970-01-01 00:00:00"
            && strtotime($latestReceiveAliTradeTime) > 0) {
            $setItem = " latest_receive_ali_trade_time = '$latestReceiveAliTradeTime' ";
        }

        if (!empty($lastReceiveTime) && $lastReceiveTime != "1970-01-01 00:00:00"
            && strtotime($lastReceiveTime) > 0) {
            if (empty($setItem)) {
                $setItem .= " latest_receive_ali_tm_time = '$lastReceiveTime' ";
            } else {
                $setItem .= ",latest_receive_ali_tm_time = '$lastReceiveTime' ";
            }
        }

        if (!empty($lastSendTime) && $lastSendTime != "1970-01-01 00:00:00"
            && strtotime($lastSendTime) > 0) {
            if (empty($setItem)) {
                $setItem .= " latest_send_ali_tm_time = '$lastSendTime' ";
            } else {
                $setItem .= ",latest_send_ali_tm_time = '$lastSendTime' ";
            }
        }

        if (!empty($setItem)) {
            //异常捕捉
            try {
                $updateSql = "update tbl_company set $setItem where client_id = $clientId and company_id=$companyId";
                $db = \PgActiveRecord::getDbByClientId($clientId);
                $ret = $db->createCommand($updateSql)->execute();
                if ($ret) {

                    \common\library\performance_v2\Helper::runPerformance($clientId, \Constants::TYPE_COMPANY, [$companyId]);


                    (new SwarmService($clientId))->refreshByRefer([$companyId], [
                        'latest_receive_ali_trade_time',
                        'latest_send_ali_tm_time',
                        'latest_receive_ali_tm_time'
                    ]);
                }
                return $ret;
            }catch (Throwable $e) {
                LogUtil::error("update latest_receive_ali_trade_time fail,sql:".$updateSql);
            }
        }

        return false;
    }

    protected static function buildOriginSettingSql($clientId, $userId, $originId, $key, $value, $createTime)
    {
        $originId = \Util::escapeDoubleQuoteSql($originId);
        return "insert into tbl_customer_origin_setting(`client_id`,`origin_id`,`user_id`,`key`,`value`,`create_time`,`update_time`)" .
            "VALUES ($clientId,{$originId},{$userId},'{$key}','{$value}','{$createTime}','{$createTime}') on duplicate key update value='{$value}',update_time='{$createTime}' ";
    }


    public static function transferSystemFullField($type,$data){

        if(!isset($data['company']) || !isset($data['customer'])){
           return $data;
        }
        //需要转换的字段
        $fieldSettingMap = [
            Constants::TYPE_COMPANY => self::$transferCompanyField,
            Constants::TYPE_CUSTOMER => [
                'name' => 'name'
            ]
        ];

        //对应的字段
        $fieldSettings = self::$fieldSettings;
        if(isset($fieldSettingMap[$type]) && isset($fieldSettings[$type])){
            foreach ($data['company'] as $key =>$value){
                if(isset($fieldSettingMap[$type][$value['id']])){
                    $newFieldName = $fieldSettingMap[$type][$value['id']];
                    $data['company'][$key]['id'] = $newFieldName;
                    if(isset($fieldSettings[$type][$newFieldName])){
                        $data['company'][$key]['name'] = \Yii::t('field', $fieldSettings[$type][$newFieldName]);
                    }
                }
            }

            foreach ($data['customer'] as $key =>$value){
                if(isset($fieldSettingMap[Constants::TYPE_CUSTOMER][$value['id']])){
                    $newFieldName = $fieldSettingMap[Constants::TYPE_CUSTOMER][$value['id']];
                    $data['customer'][$key]['id'] = $newFieldName;
                    if(isset($fieldSettings[Constants::TYPE_CUSTOMER][$newFieldName])){
                        $data['customer'][$key]['name'] = \Yii::t('field', $fieldSettings[Constants::TYPE_CUSTOMER][$newFieldName]);
                    }
                }
            }
        }
        return $data;
    }

    public static function getFields($type,$client_id,$userId, $functionalId = null, $scene = ''){
        /**
         * 暂时粗暴处理成这样子
         * <AUTHOR>
         * todo
         */
        if ($type == Constants::TYPE_PAYPAL) {
            return self::makePaypalFieldSettings();
        } elseif ($type == Constants::TYPE_CUSTOMER_ADVICE) {
            return self::makeCustomerAdviceFieldSettings();
        }

        $customFieldService = new CustomFieldService($client_id, $type);
        $needExcludeIds = $customFieldService->getExcludeIds();

        $customFieldList = new FieldList($client_id);
        $customFieldList->setFields(['id', 'type', 'name', 'field_type','base','require','ext_info', 'disable_flag', 'is_editable', 'relation_field', 'relation_type', 'relation_field_type','group_id','is_list','unique_check','unique_prevent','unique_message','relation_origin_type','relation_origin_field','relation_origin_field_type']);
        $customFieldList->setShowSearchable();
        $customFieldList->setType($type);
        if ($type == Constants::TYPE_OPPORTUNITY) {
            $customFieldList->setGroupId(CustomFieldService::OPPORTUNITY_GROUP_BASIC);
        }
        $customFieldList->setBase(null);
        $customFieldList->setEnableFlag(1);
        $customFieldList->setDisableFlag(0);
        $customFieldList->setPrivilegeInfo($userId, $functionalId);
        $customFieldList->setHideValueForDisable(true);
        if (!empty($needExcludeIds)) {
            $customFieldList->setExcludeId($needExcludeIds);
        }
        $customFieldList->getFormatter()->setScene($scene);
        $customFieldList->setScene($scene);
        $result =  $customFieldList->find();

        return $result;

    }

    public static function filterFieldByLimitSetting($type, $result, $clientId)
    {
        $fieldLimitSettingKeyList = [Client::EXTERNAL_KEY_AMES_FIELDS_LIMIT];
        $client = Client::getClient($clientId);
        $setting = $client->getExtentAttributes($fieldLimitSettingKeyList);

        $disableFields = [];
        if ($setting['ames_fields_limit'] ?? 0) {
            $amesLimitFields = CustomFieldService::getAmesLimitFields();
            $disableFields = array_merge($disableFields, $amesLimitFields[$type]['field'] ?? []);
        }

        switch ($type)
        {
            case Constants::TYPE_COMPANY:
                foreach ($result['company'] ?? [] as $index => $value)
                {
                    if (in_array($value['id'], $disableFields)) {
                        unset($result['company'][$index]);
                    }
                }
                isset($result['company']) && $result['company'] = array_values($result['company']);
                break;
            default:
                break;
        }
        foreach ($result['extra'] ?? [] as $index => $value)
        {
            if (in_array($value['id'], $disableFields)) {
                unset($result['extra'][$index]);
            }

            $privilegeService = new PrivilegeService($clientId);
            $mainSystemId = $privilegeService->getMainSystemId();
            $fieldMap = CustomFieldService::SPECIFIC_FIELD_MAP[$mainSystemId] ?? [];
            if (in_array($value['id'],CustomFieldService::SPECIFIC_FIELD_LIST) && !in_array($value['id'],$fieldMap)){
                unset($result['extra'][$index]);
            }
        }
        isset($result['extra']) && $result['extra'] = array_values($result['extra']);
        return $result;
    }


    /** ，过滤字段
     * @param $type
     * @param $result
     * @return mixed
     */
	public static function filterFieldByPrivilege($type, $result, $clientId, $userId, $originType = null) {

        switch ($type) {
            case Constants::TYPE_COMPANY:
                //隐藏公海分组
                $client = Client::getClient($clientId);
                //如果公海分组开关关闭
                if (!\common\library\privilege_v3\Helper::hasFunctional($clientId, PrivilegeConstants::FUNCTIONAL_COMPANY_POOL_SETTING) || !$client->getExtentAttribute(Client::EXTERNAL_KEY_CUSTOMER_POOL_SWITCH)) {
                    foreach ($result['company'] as $key => $value){
                        if($value['id'] == 'pool_name'){
                            unset($result['company'][$key]);
                        }
                    }
                    $result['company'] = array_values($result['company']);
                }
                break;
            case Constants::TYPE_PRODUCT:
            case Constants::TYPE_ORDER:
                if( !AlibabaService::getInstance($clientId)->hasStore())
                {
                    foreach ($result['extra'] as $key => $value) {
                        if ($value['id'] == 'ali_store_id')
                        {
                            unset($result['extra'][$key]);
                            break;
                        }
                    }
                }
                break;
            case Constants::TYPE_CASH_COLLECTION:
                // 列表字段进行字段权限处理
                $functional = array_flip(PrivilegeConstants::FIELD_PRIVILEGE_FUNCTIONAL_MAP)[$type];
                $fieldPrivileges = \common\library\privilege_v3\privilege_field\Helper::getObjectFieldPrivileges($clientId, $userId, $functional, PrivilegeFieldV2::SCENE_OF_VIEW, $type);
                $disableFields = $fieldPrivileges['disable'] ?? [];
                if (!empty($disableFields)) {
                    foreach ($result['fields'] as $index => $item) {
                        if (in_array($item['id'], $fieldPrivileges)) {
                            unset($result['fields'][$index]);
                        }
                    }
                    foreach ($result['extra'] as $index => $item) {
                        if (in_array($item['id'], $disableFields)) {
                            unset($result['extra'][$index]);
                        }
                        if (isset($item['field']) &&  in_array($item['field'], $disableFields)) {
                            unset($result['extra'][$index]);
                        }
                    }
                }
                break;
            default:
                break;
        }

        if(!array_key_exists('extra', $result)){

			return $result;
		}

		$privilegeService = PrivilegeService::getInstance($clientId, $userId);

		$needCheckPrivilegeFields = [

			Constants::TYPE_COMPANY => [

				'public_time'               => [

					PrivilegeConstants::FUNCTIONAL_COMPANY_POOL,
				],
				'private_time'              => [

					PrivilegeConstants::FUNCTIONAL_COMPANY_POOL,
				],
				'release_count'             => [

					PrivilegeConstants::FUNCTIONAL_COMPANY_POOL,
				],
				'latest_success_opportunity_time' => [

					PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
				],
				'latest_transaction_order_time' => [

					PrivilegeConstants::FUNCTIONAL_PERFORMANCE,
				],
				'success_opportunity_amount_avg_usd' => [

					PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
				],
				'success_opportunity_amount_avg_cny' => [

					PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
				],
				'ongoing_opportunity_count' => [

					PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
				],
				'success_opportunity_count' => [

					PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
				],
				'success_opportunity_amount_usd' => [

					PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
				],
				'success_opportunity_amount_cny' => [

					PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
				],
				'transaction_order_amount_avg' => [

					PrivilegeConstants::FUNCTIONAL_PERFORMANCE,
				],
				'transaction_order_amount' => [

					PrivilegeConstants::FUNCTIONAL_PERFORMANCE,
				],
				'performance_order_count' => [

					PrivilegeConstants::FUNCTIONAL_PERFORMANCE,
				],
				'deal_time'               => [

					PrivilegeConstants::FUNCTIONAL_PERFORMANCE,
				],
				'success_opportunity_first_time' => [

					PrivilegeConstants::FUNCTIONAL_OPPORTUNITY,
				],
				'transaction_order_first_time' => [

					PrivilegeConstants::FUNCTIONAL_PERFORMANCE,
				],

			],

			Constants::TYPE_LEAD => [

				'public_time'   => [

					PrivilegeConstants::FUNCTIONAL_LEAD_POOL,
				],
				'private_time'  => [

					PrivilegeConstants::FUNCTIONAL_LEAD_POOL,
				],
				'release_count' => [

					PrivilegeConstants::FUNCTIONAL_LEAD_POOL,
				],
			],

			Constants::TYPE_OPPORTUNITY => [

				'create_type' => [

					PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_OPPORTUNITY,
				],
                'next_follow_up_time' => [
                    PrivilegeConstants::FUNCTIONAL_OPPORTUNITY_NEW_DYNAMIC,
                ]
			],

			'default' => [

				'performance_order_count' => [

					PrivilegeConstants::FUNCTIONAL_PERFORMANCE,
				],
				'deal_time'               => [

					PrivilegeConstants::FUNCTIONAL_PERFORMANCE,
				],
				'cash_collection'         => [

					PrivilegeConstants::FUNCTIONAL_CASH_COLLECTION,
				],
				'approval_status'         => [

					PrivilegeConstants::FUNCTION_REVIEW_BASE,
				],
			]
		];

		//查询成交订单金额的币种
		$orderPerformanceCurrency = \common\library\performance_v2\Helper::getCurrencyFromDefaultOrderPerformanceRule($clientId);

		array_walk($result['extra'], function (&$item) use ($orderPerformanceCurrency) {

			if (in_array($item['id'], [
				'transaction_order_amount',
				'transaction_order_amount_avg',
                'transaction_order_first_amount'
			])) {

				$item['name'] .= "($orderPerformanceCurrency)";
			}
		});

		$result['extra'] = array_filter($result['extra'], function ($item) use ($privilegeService, $needCheckPrivilegeFields, $type, $originType){

			if ($item['id'] == 'swarm_list' && $originType == Constants::TYPE_COMPANY_POOL) {

				return false;
			}

			if (array_key_exists($type, $needCheckPrivilegeFields)) {

				if (!array_key_exists($item['id'], $needCheckPrivilegeFields[$type])) {

					return true;
				}

				return $privilegeService->checkFunctional($needCheckPrivilegeFields[$type][$item['id']]);
			}

			if (!array_key_exists($item['id'], $needCheckPrivilegeFields['default'])) {

				return true;
			}

			return $privilegeService->checkFunctional($needCheckPrivilegeFields['default'][$item['id']]);
		});

		$result['extra'] = array_values($result['extra']);

		return $result;
    }

    public static function getFullFieldList($client_id, $userId, int $type, $sort = false, $scene = '', $assessFlag = false)
    {

        self::$scene = $scene;

//    	原始type
	    $originType = $type;

        [$type, $functionalId] = \common\library\privilege_v3\Helper::getRealReferAndFunctionalId($type);
        $fields = self::getFields($type,$client_id,$userId, $functionalId, $scene);
        $excludeSettings = [
            Constants::TYPE_COMPANY => [
                'company' => [
                    'province',
                    'city',
                ],
                'customer' => [
                    'main_customer_flag'
                ],
            ],
            Constants::TYPE_LEAD => [
                'company' => [
                    'image_list',
                    'tag',
                    'cus_tag',
                    'group_id',
                    'biz_type',
                    'timezone',
                    'annual_procurement',
                    'intention_level',
                ],
                'customer' => [
                    'image_list',
                    'tag',
                    'main_customer_flag',
                    'remark',
                ],
            ],
            Constants::TYPE_OPPORTUNITY => [
                'company_id',
                //'customer_id',
                'stage',
                //'origin',
                //'type',
                'fail_type',
                'flow_id',
                'main_user',
                'handler',
            ],
            Constants::TYPE_CASH_COLLECTION => [
                'company_id',
                'order_id',
                'opportunity_id',
                'department_id',
                'finance_verify_name',
                'finance_verify_date',
                'refer_type',
                'user_id',
                'create_type'
            ],
            Constants::TYPE_PRODUCT => [
                'create_type',
            ],
        ];
        $excludeSettings = self::refreshExcludeSettingsByScene($excludeSettings, $scene);

        if (in_array($type, [Constants::TYPE_LEAD, Constants::TYPE_COMPANY])) {
            $customerType = $type == Constants::TYPE_LEAD ? Constants::TYPE_LEAD_CUSTOMER : Constants::TYPE_CUSTOMER;
            $customerFields = self::getFields($customerType,$client_id, $userId, $functionalId);

            if (isset($excludeSettings[$type])) {
                $fields = array_filter($fields, function($item) use ($excludeSettings, $type) {
                    return !in_array($item['id'], $excludeSettings[$type]['company']);
                });
                $customerFields = array_filter($customerFields, function($item) use ($excludeSettings, $type) {
                    return !in_array($item['id'], $excludeSettings[$type]['customer']);
                });
            }
            $result['company'] = array_values($fields);
            $result['customer'] = array_values($customerFields);
        } else {
            $fields = array_filter($fields, function($item) use ($excludeSettings, $type) {
                return !in_array($item['id'], $excludeSettings[$type]??[]);
            });
            $result['fields'] = array_values($fields);
        }


        if ($assessFlag) {

            $client = Client::getClient($client_id);
            $clientExtentAttrs = $client->getExtentAttributes();
            $assessFlag = intval($clientExtentAttrs[Client::EXTERNAL_KEY_ASSESS_DIMENSION_SWITCH] ?? 0);
        }

        $result = self::mergeExtra($type, $result, $originType, $assessFlag);

        if($type == Constants::TYPE_COMPANY){
            $result = self::transferSystemFullField($type,$result);
        }
        if ($type == Constants::TYPE_ORDER_PROFIT) {
            $fundFieldsGroup = self::addFundFieldsGroupByItemSettings($client_id);
            $costInvoiceFieldsGroup = self::addCostInvoiceFieldsGroupByItemSettings($client_id);
            $result['fields'] = array_merge($result['fields'], $fundFieldsGroup['fields'], $costInvoiceFieldsGroup['fields']);
        }
        $result = self::setDefaultFieldWidth($type, $result);
		$result = self::filterFieldByPrivilege($type, $result, $client_id, $userId, $originType);
        $result = self::filterFieldByLimitSetting($type, $result, $client_id);
        $result = self::reformFieldsWithPrivilege($type, $result, $client_id, $userId);
        $result = self::sortFields($type, $result);

        return $result;
    }

    /**
     * 为字段补充权限信息
     *
     * @param $type
     * @param $fields
     * @param $clientId
     * @param $userId
     * @return array
     */
    protected static function reformFieldsWithPrivilege($type, $fields, $clientId, $userId): array
    {
        if (!in_array($type, [Constants::TYPE_COMPANY, Constants::TYPE_COMPANY_POOL, Constants::TYPE_LEAD, Constants::TYPE_LEAD_POOL, Constants::TYPE_OPPORTUNITY])) {
            return $fields;
        }
        
        // 增加权限检查
        $functional = PrivilegeConstants::POOL_FIELD_PRIVILEGE_FUNCTIONAL_MAP[$type] ?? array_flip(PrivilegeConstants::FIELD_PRIVILEGE_FUNCTIONAL_MAP)[$type];
        $fieldPrivileges = \common\library\privilege_v3\privilege_field\Helper::getObjectFieldPrivileges($clientId, $userId, $functional, PrivilegeFieldV2::SCENE_OF_VIEW);
        $fieldPrivileges = array_column($fieldPrivileges, null, 'refer_type');

        $transferFlipMap = array_flip(self::TRANS_FIELD_MAP[$type] ?? []);

        $res = [];
        foreach ($fields as $k => $item) {
            $group = [];
            foreach ($item as $kk => $value) {
                $targetId = null;
                if ($value['type'] == \Constants::TYPE_COMPANY && str_starts_with($value['id'], 'company.')) {
                    $targetId = substr($value['id'], strlen('company.')) ?? null;
                    $targetId = CompanyConstants::SPECIAL_FIELD_SETTING_MAP[$targetId] ?? $targetId;
                }

                if (is_null($targetId)) {
                    $targetId = $transferFlipMap[$value['id']] ?? $value['id'];
                }

                if (isset($targetId, $fieldPrivileges[$value['type']]) && in_array($targetId, $fieldPrivileges[$value['type']]['disable'] ?? [])) {
                    continue;
                }

                if (!isset($value['is_editable']) && isset($targetId, $fieldPrivileges[$value['type']]) && in_array($targetId, $fieldPrivileges[$value['type']]['readonly'] ?? [])) {
                    $value['is_editable'] = 0;
                }

                $group[$kk] = $value;
            }
            $res[$k] = $group;
        }

        return $res;
    }

    protected static function sortFields($type, $fields)
    {
        $sortedFieldMap = [
            \Constants::TYPE_PRODUCT => [
                'name',
                'cn_name',
                'images',
                'product_no',
                'model',
                'sku_attributes',
                'group_id',
                'fob',
                'cost_with_tax',
                'minimum_order_quantity',
                'unit',
                'description',
                'package_size',
                'package_volume',
                'package_gross_weight',
                'count_per_package',
                'package_remark',
                'category_ids',
                'hs_code',
                'info_json',
                'product_remark',
//                'sku_remark',
//                'sku_description',
                'from_url',
                'source_type',
                'create_user',
                'create_time',
                'update_user',
                'update_time',
            ],
        ];

        $sortedFieldKeys = $sortedFieldMap[$type] ?? [];

        if (empty($sortedFieldKeys)) {
            return $fields;
        }

        $allFields = array_merge($fields['fields'], $fields['extra']);
        $allFieldKeys = array_column($allFields, 'id');
        $allFieldsIndexed = array_column($allFields, null, 'id');
        $baseFieldKeys = array_column($fields['fields'], 'id');

        $baseFields = $extraFields = [];
        $sortedFields = array_reduce($sortedFieldKeys, function ($carry, $key) use ($allFieldsIndexed) {
            if (array_key_exists($key, $allFieldsIndexed)) {
                $carry[] = $allFieldsIndexed[$key];
            }
            return $carry;
        });

        foreach ($sortedFields as $sortedField) {
            if (in_array($sortedField['id'], $baseFieldKeys, true)) {
                $baseFields[] = $sortedField;
            } else {
                $extraFields[] = $sortedField;
            }
        }

        foreach (array_diff($baseFieldKeys, array_column($baseFields, 'id')) as $key) {
            $baseFields[] = $allFieldsIndexed[$key];
        }
        foreach (array_diff(array_column($fields['extra'], 'id'), array_column($extraFields, 'id')) as $key) {
            $extraFields[] = $allFieldsIndexed[$key];
        }

        return [
            'fields' => $baseFields,
            'extra' => $extraFields,
        ];
    }

	public static function mergeExtra($type, $result, $originType = null, $assessFlag = false) {

		//其他字段
		$extraSettings = [
			Constants::TYPE_COMPANY         => [
				[
					'id'         => 'owner',
					'name'       => \Yii::t('field', '跟进人'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_COMPANY,
					'base'       => 1,
				],
				[
					'id'         => 'last_owner_name',
					'name'       => \Yii::t('field', '原跟进人'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_COMPANY,
					'base'       => 1,

				],
				[
					'id'         => 'score',
					'name'       => \Yii::t('field', '客户评分'),
					'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
					'type'       => Constants::TYPE_COMPANY,
					'base'       => 1,

				],
				[
					'id'         => 'order_time',
					'name'       => \Yii::t('field', '最近联系时间'),
					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
					'type'       => Constants::TYPE_COMPANY,
					'base'       => 1,

				],
				[
					'id'         => 'archive_time',
					'name'       => \Yii::t('field', '创建时间'),
					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
					'type'       => Constants::TYPE_COMPANY,
					'base'       => 1,
				],
				[
					'id'         => 'last_remark_trail',
					'name'       => \Yii::t('field', '最近跟进'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_COMPANY,
					'base'       => 1,
				],
				[
					'id'         => 'last_trail',
					'name'       => \Yii::t('field', '最近动态'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_COMPANY,
					'base'       => 1,
				],
				[
					'id'         => 'archive_type',
					'name'       => \Yii::t('field', '创建方式'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_COMPANY,
					'base'       => 1,
				],
				[
					'id'         => 'success_opportunity_count',
					'name'       => \Yii::t('field', '赢单商机数'),
					'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
					'type'       => Constants::TYPE_COMPANY,
					'base'       => 1,
				],
				[
					'id'         => 'performance_order_count',
					'name'       => \Yii::t('field', '成交订单数'),
					'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
					'type'       => Constants::TYPE_COMPANY,
					'base'       => 1,
				],
				[
					'id'         => 'deal_time',
					'name'       => \Yii::t('field', '最近成交日期'),
					'field_type' => CustomFieldService::FIELD_TYPE_DATE,
					'type'       => Constants::TYPE_COMPANY,
					'base'       => 1,
				],
				[
					'id'         => 'create_user',
					'name'       => \Yii::t('field', '创建人'),
					'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
					'type'       => Constants::TYPE_COMPANY,
					'base'       => 1,
				],
				[
					'id'         => 'last_edit_user',
					'name'       => \Yii::t('field', '最近修改人'),
					'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
					'type'       => Constants::TYPE_COMPANY,
					'base'       => 1,
				],
				[
					'id'         => 'edit_time',
					'name'       => \Yii::t('field', '资料更新时间'),
					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
					'type'       => Constants::TYPE_COMPANY,
					'base'       => 1,
				],
				[
					'id'         => 'private_time',
					'name'       => \Yii::t('field', '最近进入私海时间'),
					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
					'type'       => Constants::TYPE_COMPANY,
					'base'       => 1,
				],
				[
					'id'         => 'public_time',
					'name'       => \Yii::t('field', '最近进入公海时间'),
					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
					'type'       => Constants::TYPE_COMPANY,
					'base'       => 1,
				],
				[
					'id'         => 'release_count',
					'name'       => \Yii::t('field', '进入公海次数'),
					'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
					'type'       => Constants::TYPE_COMPANY,
					'base'       => 1,
				],
                [
                    'id'         => 'public_type',
                    'name'       => \Yii::t('field', '进入公海方式'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'type'       => Constants::TYPE_COMPANY,
                    'base'       => 1,
                ],
				[
					'id'         => 'ali_store_id',
					'name'       => \Yii::t('field', '来源详情'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_COMPANY,
					'base'       => 1,
				],
//                [
//                    'id'         => 'source_detail',
//                    'name'       => \Yii::t('field', '来源详情'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
//                    'type'       => Constants::TYPE_COMPANY,
//                    'base'       => 1,
//                ],
				[
					'id'         => 'recent_follow_up_time',
					'name'       => \Yii::t('field', '最近跟进时间'),
					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
					'type'       => Constants::TYPE_COMPANY,
					'base'       => 1,

				],
				[
					'id'         => 'main_lead_id',
					'name'       => \Yii::t('field', '来源线索'),
					'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
					'type'       => Constants::TYPE_COMPANY,
					'base'       => 1,
				],
			],
            Constants::TYPE_COMPANY_POOL    => [
                [
                    'id'         => 'public_type',
                    'name'       => \Yii::t('field', '进入公海方式'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'type'       => Constants::TYPE_COMPANY,
                    'base'       => 1,
                ],
                [
                    'id'         => 'public_reason',
                    'name'       => \Yii::t('field', '移入公海原因'),
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'type'       => Constants::TYPE_COMPANY,
                    'base'       => 1,
                ],
                [
                    'id'         => 'assess',
                    'name'       => \Yii::t('field', '评分'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
            ],
            Constants::TYPE_LEAD            => [
				[
					'id'         => 'owner',
					'name'       => \Yii::t('field', '跟进人'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_LEAD,
					'base'       => 1,
				],
				[
					'id'         => 'last_owner_name',
					'name'       => \Yii::t('field', '原跟进人'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_LEAD,
					'base'       => 1,
				],
				[
					'id'         => 'follow_count',
					'name'       => \Yii::t('field', '跟进数'),
					'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
					'type'       => Constants::TYPE_LEAD,
					'base'       => 1,
				],
				[
					'id'         => 'order_time',
					'name'       => \Yii::t('field', '最近联系时间'),
					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
					'type'       => Constants::TYPE_LEAD,
					'base'       => 1,
				],
				[
					'id'         => 'lost_day_count',
					'name'       => \Yii::t('field', '未联系天数'),
					'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
					'type'       => Constants::TYPE_LEAD,
					'base'       => 1,
				],
				[
					'id'         => 'transfer_count',
					'name'       => \Yii::t('field', '转移次数'),
					'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
					'type'       => Constants::TYPE_LEAD,
					'base'       => 1,
				],
				[
					'id'         => 'release_count',
					'name'       => \Yii::t('field', '进公海次数'),
					'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
					'type'       => Constants::TYPE_LEAD,
					'base'       => 1,
				],
				[
					'id'         => 'follow_up_time',
					'name'       => \Yii::t('field', '首次跟进时间'),
					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
					'type'       => Constants::TYPE_LEAD,
					'base'       => 1,
				],
				[
					'id'         => 'archive_time',
					'name'       => \Yii::t('field', '创建时间'),
					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
					'type'       => Constants::TYPE_LEAD,
					'base'       => 1,
				],
				[
					'id'         => 'renew_time',
					'name'       => \Yii::t('field', '更新时间'),
					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
					'type'       => Constants::TYPE_LEAD,
					'base'       => 1,
				],
				[
					'id'         => 'archive_type',
					'name'       => \Yii::t('field', '创建方式'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_LEAD,
					'base'       => 1,
				],
				[
					'id'         => 'create_user',
					'name'       => \Yii::t('field', '创建人'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_LEAD,
					'base'       => 1,
				],
				[
					'id'         => 'last_edit_user',
					'name'       => \Yii::t('field', '最近修改人'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_LEAD,
					'base'       => 1,
				],
				[
					'id'         => 'edit_time',
					'name'       => \Yii::t('field', '资料更新时间'),
					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
					'type'       => Constants::TYPE_LEAD,
					'base'       => 1,
				],
				[
					'id'         => 'private_time',
					'name'       => \Yii::t('field', '最近进入私海时间'),
					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
					'type'       => Constants::TYPE_LEAD,
					'base'       => 1,
				],
				[
					'id'         => 'public_time',
					'name'       => \Yii::t('field', '最近进入公海时间'),
					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
					'type'       => Constants::TYPE_LEAD,
					'base'       => 1,
				],
				[
					'id'         => 'store_id',
					'name'       => \Yii::t('field', '来源详情'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_LEAD,
					'base'       => 1,
				],
                [
                    'id'         => 'fail_reason',
                    'name'       => \Yii::t('field', '线索无效原因'),
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'type'       => Constants::TYPE_LEAD,
                    'base'       => 1,
                ],
                [
                    'id'         => 'assess',
                    'name'       => \Yii::t('field', '评分'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'type'       => Constants::TYPE_LEAD,
                    'base'       => 1,
                    'ext_info'   => '{}',
                ],
                [
                    'id'         => 'latest_edm_time',
                    'name'       => \Yii::t('field', '最近发EDM时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'type'       => Constants::TYPE_LEAD,
                    'base'       => 1,
                ],
                [
                    'id'         => 'pin_user_list',
                    'name'       => \Yii::t('field', '关注人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'refer_type' => \Constants::TYPE_OTHER,
                    'type'       => Constants::TYPE_LEAD,
                    'base'       => 1,
                ],
			],
			Constants::TYPE_OPPORTUNITY     => [
				[
					'id'         => 'serial_id',
					'name'       => \Yii::t('field', '商机编号'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_OPPORTUNITY,
					'base'       => 1,
				],
				[
					'id'         => 'company.name',
					'name'       => \Yii::t('field', '客户名称'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_COMPANY,
					'base'       => 1,
                    'is_editable'=> 0,
				],
				/*[
					'id'         => 'customer.name',
					'name'       => \Yii::t('field', '关联联系人'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_OPPORTUNITY,
					'base'       => 1,
				],*/
				[
					'id'         => 'stage_info.success_rate',
					'name'       => \Yii::t('field', '赢率'),
					'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
					'type'       => Constants::TYPE_OPPORTUNITY,
					'base'       => 1,
                    'is_editable'=> 0,
				],
				[
					'id'         => 'sale_flow_name',
					'name'       => \Yii::t('field', '销售流程'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_OPPORTUNITY,
					'base'       => 1,
                    'is_editable'=> 0,
				],
				[
					'id'         => 'stage_info.name',
					'name'       => \Yii::t('field', '销售阶段'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_OPPORTUNITY,
					'base'       => 1,
                    'field'       => 'stage',
                ],
				/*[
					'id'         => 'origin_name',
					'name'       => \Yii::t('field', '商机来源'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_OPPORTUNITY,
					'base'       => 1,
				],*/
				[
					'id'         => 'fail_type_name',
					'name'       => \Yii::t('field', '输单原因'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_OPPORTUNITY,
					'base'       => 1,
				],
			/*	[
					'id'         => 'type_name',
					'name'       => \Yii::t('field', '商机类型'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_OPPORTUNITY,
					'base'       => 1,
				],*/
				[
					'id'         => 'stage_type_name',
					'name'       => \Yii::t('field', '商机状态'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_OPPORTUNITY,
					'base'       => 1,
                    'is_editable'=> 0,
				],
				[
					'id'         => 'fail_stage_info.name',
					'name'       => \Yii::t('field', '输单阶段'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_OPPORTUNITY,
					'base'       => 1,
                    'is_editable'=> 0,
				],
				[
					'id'         => 'main_user_info.nickname',
					'name'       => \Yii::t('field', '负责人'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_OPPORTUNITY,
					'base'       => 1,
				],
				[
					'id'         => 'handler_info.nickname',
					'name'       => \Yii::t('field', '协同跟进人'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_OPPORTUNITY,
					'base'       => 1,
				],
				[
					'id'         => 'create_user_info.nickname',
					'name'       => \Yii::t('field', '创建人'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_OPPORTUNITY,
					'base'       => 1,
                    'is_editable'=> 0,
				],
				[
					'id'         => 'create_time',
					'name'       => \Yii::t('field', '创建时间'),
					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
					'type'       => Constants::TYPE_OPPORTUNITY,
					'base'       => 1,
                    'is_editable'=> 0,
				],
				[
					'id'         => 'update_time',
					'name'       => \Yii::t('field', '更新时间'),
					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
					'type'       => Constants::TYPE_OPPORTUNITY,
					'base'       => 1,
                    'is_editable'=> 0,
				],
				[
					'id'         => 'edit_time',
					'name'       => \Yii::t('field', '资料更新时间'),
					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
					'type'       => Constants::TYPE_OPPORTUNITY,
					'base'       => 1,
                    'is_editable'=> 0,
				],
				[
					'id'         => 'trail_time',
					'name'       => \Yii::t('field', '动态更新时间'),
					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
					'type'       => Constants::TYPE_OPPORTUNITY,
					'base'       => 1,
                    'is_editable'=> 0,
				],
				[
					'id'         => 'product_edit_time',
					'name'       => \Yii::t('field', '商机产品更新时间'),
					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
					'type'       => Constants::TYPE_OPPORTUNITY,
					'base'       => 1,
                    'is_editable'=> 0,
				],
				[
					'id'         => 'create_type',
					'name'       => \Yii::t('field', '创建方式'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_OPPORTUNITY,
					'base'       => 1,
                    'is_editable'=> 0,
				],
				[
					'id'         => 'stage_stay_time',
					'name'       => \Yii::t('field', '当前阶段滞留时间'),
					'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
					'type'       => Constants::TYPE_OPPORTUNITY,
					'base'       => 1,
                    'is_editable'=> 0,
				],
                [
                    'id'         => 'opportunity_trail',
                    'name'       => \Yii::t('field','商机动态'),
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'type'       => Constants::TYPE_OPPORTUNITY,
                    'base'       => 1,
                    'is_editable'=> 0,
                ],
                [
                    'id'         => 'company.short_name',
                    'name'       => \Yii::t('field','简称'),
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'type'       => Constants::TYPE_COMPANY,
                    'base'       => 1,
                    'is_editable'=> 0,
                ],
                [
                    'id'         => 'company.country',
                    'name'       => \Yii::t('field','国家地区'),
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'type'       => Constants::TYPE_COMPANY,
                    'base'       => 1,
                    'is_editable'=> 0,
                ],
                [
                    'id'         => 'company.timezone',
                    'name'       => \Yii::t('field','时区'),
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'type'       => Constants::TYPE_COMPANY,
                    'base'       => 1,
                    'is_editable'=> 0,
                ],
                [
                    'id'         => 'company.biz_type',
                    'name'       => \Yii::t('field','客户类型'),
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'type'       => Constants::TYPE_COMPANY,
                    'base'       => 1,
                    'is_editable'=> 0,
                ],
                [
                    'id'         => 'company.origin_list',
                    'name'       => \Yii::t('field','客户来源'),
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'type'       => Constants::TYPE_COMPANY,
                    'base'       => 1,
                    'is_editable'=> 0,
                ],
                [
                    'id'         => 'company.product_group_ids',
                    'name'       => \Yii::t('field','产品分组'),
                    'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                    'type'       => Constants::TYPE_COMPANY,
                    'base'       => 1,
                    'is_editable'=> 0,
                ],
                [
                    'id'         => 'next_follow_up_time',
                    'name'       => \Yii::t('field','下次日程时间'),
                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
                    'type'       => Constants::TYPE_OPPORTUNITY,
                    'base'       => 1,
                    'is_editable'=> 0,
                ],
                [
                    'id'         => 'pin_user_list',
                    'name'       => \Yii::t('field', '关注人'),
                    'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                    'refer_type' => \Constants::TYPE_OTHER,
                    'type'       => Constants::TYPE_OPPORTUNITY,
                    'base'       => 1,
                    'is_editable'=> 0,
                ],
			],
			Constants::TYPE_CASH_COLLECTION => [
				[
					'id'         => 'user_info.nickname',
					'name'       => \Yii::t('field', '回款单-负责人'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_CASH_COLLECTION,
					'base'       => 1,
                    'field'       => 'user_id',
				],
				[
					'id'         => 'company_info.name',
					'name'       => \Yii::t('field', '回款单-关联客户'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_CASH_COLLECTION,
					'base'       => 1,
                    'field'       => 'company_id',
				],
				[
					'id'         => 'order_info.name',
					'name'       => \Yii::t('field', '回款单-关联订单'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_CASH_COLLECTION,
					'base'       => 1,
                    'field'       => 'order_id',
				],
				[
					'id'         => 'opportunity_info.name',
					'name'       => \Yii::t('field', '回款单-关联商机'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_CASH_COLLECTION,
					'base'       => 1,
                    'field'       => 'opportunity_id',
				],
				[
					'id'         => 'department_info.name',
					'name'       => \Yii::t('field', '回款单-归属部门'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_CASH_COLLECTION,
					'base'       => 1,
                    'field'       => 'department_id',
				],
				[
					'id'         => 'cash_collection_no',
					'name'       => \Yii::t('field', '回款单-回款单号'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_CASH_COLLECTION,
					'base'       => 1,
				],
				[
					'id'         => 'create_time',
					'name'       => \Yii::t('field', '回款单-创建时间'),
					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
					'type'       => Constants::TYPE_CASH_COLLECTION,
					'base'       => 1,
				],
                [
                    'id'         => 'create_type',
                    'name'       => \Yii::t('field', '回款单-创建方式'),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'type'       => Constants::TYPE_CASH_COLLECTION,
                    'base'       => 1,

                ],
				[
					'id'         => 'update_time',
					'name'       => \Yii::t('field', '回款单-最后修改时间'),
					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
					'type'       => Constants::TYPE_CASH_COLLECTION,
					'base'       => 1,
				],
				[
					'id'         => 'cash_collection_invoice_info.cash_collection_invoice_no',
					'name'       => \Yii::t('field', '回款登记编号'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_CASH_COLLECTION,
					'base'       => 1,
				],
				[
					'id'         => 'cash_collection_invoice_info.amount',
					'name'       => \Yii::t('field', '回款金额'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_CASH_COLLECTION,
					'base'       => 1,
				],
				[
					'id'         => 'cash_collection_invoice_info.bank_charge',
					'name'       => \Yii::t('field', '回款手续费'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_CASH_COLLECTION,
					'base'       => 1,
				],
				[
					'id'         => 'cash_collection_invoice_info.current_amount',
					'name'       => \Yii::t('field', '待核销金额'),
					'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
					'type'       => Constants::TYPE_CASH_COLLECTION,
					'base'       => 1,
				],
			],
			Constants::TYPE_PRODUCT         => [
//				[
//					'id'         => 'ali_store_id',
//					'name'       => \Yii::t('field', '国际站店铺'),
//					'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//					'type'       => Constants::TYPE_PRODUCT,
//					'base'       => 1,
//				],
//				[
//					'id'         => 'source_type',
//					'name'       => \Yii::t('field', '创建方式'),
//					'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//					'type'       => Constants::TYPE_PRODUCT,
//					'base'       => 1,
//				],
//				[
//					'id'         => 'product_type',
//					'name'       => \Yii::t('field', '产品类型'),
//					'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//					'type'       => Constants::TYPE_PRODUCT,
//					'base'       => 1,
//				],
//				[
//					'id'         => 'create_user',
//					'name'       => \Yii::t('field', '创建人'),
//					'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//					'type'       => Constants::TYPE_PRODUCT,
//					'base'       => 1,
//				],
//				[
//					'id'         => 'create_time',
//					'name'       => \Yii::t('field', '创建时间'),
//					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
//					'type'       => Constants::TYPE_PRODUCT,
//					'base'       => 1,
//				],
//				[
//					'id'         => 'update_user',
//					'name'       => \Yii::t('field', '修改人'),
//					'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//					'type'       => Constants::TYPE_PRODUCT,
//					'base'       => 1,
//				],
//				[
//					'id'         => 'update_time',
//					'name'       => \Yii::t('field', '更新时间'),
//					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
//					'type'       => Constants::TYPE_PRODUCT,
//					'base'       => 1,
//				],
			],
			Constants::TYPE_ORDER           => [
                [
                    'id'         => 'extra_cost_item',
                    'name'       => \Yii::t('field', '附加费用项'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'type'       => Constants::TYPE_ORDER,
                    'base'       => 1,
                ],
//                [
//                    'id'         => 'approval_status',
//                    'name'       => \Yii::t('field', '审批状态'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//                    'type'       => Constants::TYPE_ORDER,
//                    'base'       => 1,
//                ],
//                [
//                    'id'         => 'update_user',
//                    'name'       => \Yii::t('field', '修改人'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//                    'type'       => Constants::TYPE_ORDER,
//                    'base'       => 1,
//                ],
//                [
//                    'id'         => 'cash_collection',
//                    'name'       => \Yii::t('field', '回款状态'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//                    'type'       => Constants::TYPE_ORDER,
//                    'base'       => 1,
//                ],
//                [
//                    'id'         => 'stock_up_status',
//                    'name'       => \Yii::t('field', '备货状态'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//                    'type'       => Constants::TYPE_ORDER,
//                    'base'       => 1
//                ],
//                [
//                    'id'         => 'shipping_status',
//                    'name'       => \Yii::t('field', '出库状态'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//                    'type'       => Constants::TYPE_ORDER,
//                    'base'       => 1
//                ],
//                [
//                    'id'         => 'end_status',
//                    'name'       => \Yii::t('field', '完成状态'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//                    'type'       => Constants::TYPE_ORDER,
//                    'base'       => 1
//                ],
//                [
//                    'id'         => 'ali_store_id',
//                    'name'       => \Yii::t('field', '来源店铺'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//                    'type'       => Constants::TYPE_ORDER,
//                    'base'       => 1,
//                ],
//                [
//                    'id'         => 'archive_type',
//                    'name'       => \Yii::t('field', '创建方式'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//                    'type'       => Constants::TYPE_ORDER,
//                    'base'       => 1,
//                ],
//                [
//                    'id'         => 'ali_order_id',
//                    'name'       => \Yii::t('field', '阿里订单ID'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//                    'type'       => Constants::TYPE_ORDER,
//                    'base'       => 1,
//                ],
//                [
//                    'id'         => 'seller_account_id',
//                    'name'       => \Yii::t('field', '阿里业务员'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//                    'type'       => Constants::TYPE_ORDER,
//                    'base'       => 1,
//                ],
//                [
//                    'id'         => 'fulfillment_channel',
//                    'name'       => \Yii::t('field', '出口方式'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//                    'type'       => Constants::TYPE_ORDER,
//                    'base'       => 1,
//                ],
//                [
//                    'id'         => 'source_type',
//                    'name'       => \Yii::t('field', '订单类型'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//                    'type'       => Constants::TYPE_ORDER,
//                    'base'       => 1,
//                ],
//                [
//                    'id'         => 'last_sync_time',
//                    'name'       => \Yii::t('field', '上次同步时间'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
//                    'type'       => Constants::TYPE_ORDER,
//                    'base'       => 1,
//                ],
//                [
//                    'id'         => 'ali_status_id',
//                    'name'       => \Yii::t('field', '阿里订单状态'),
//                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
//                    'type'       => Constants::TYPE_ORDER,
//                    'base'       => 1,
//                ],
                [
                    'id'         => 'erp_status',
                    'name'       => \Yii::t('field', 'ERP状态'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'type'       => Constants::TYPE_ORDER,
                    'base'       => 1,
                ],
			],
			Constants::TYPE_PURCHASE_ORDER  => [
                [
                    'id'         => 'extra_cost_item',
                    'name'       => \Yii::t('field', '附加费用项'),
                    'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                    'type'       => Constants::TYPE_ORDER,
                    'base'       => 1,
                ],
				[
					'id'         => 'approval_status',
					'name'       => \Yii::t('field', '审批状态'),
					'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
					'type'       => Constants::TYPE_ORDER,
					'base'       => 1,
				],
				[
					'id'         => 'creator',
					'name'       => \Yii::t('field', '创建人'),
					'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
					'type'       => Constants::TYPE_PRODUCT,
					'base'       => 1,
				],
				[
					'id'         => 'create_time',
					'name'       => \Yii::t('field', '创建时间'),
					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
					'type'       => Constants::TYPE_PRODUCT,
					'base'       => 1,
				],
				[
					'id'         => 'modifier',
					'name'       => \Yii::t('field', '修改人'),
					'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
					'type'       => Constants::TYPE_PRODUCT,
					'base'       => 1,
				],
				[
					'id'         => 'update_time',
					'name'       => \Yii::t('field', '更新时间'),
					'field_type' => CustomFieldService::FIELD_TYPE_DATETIME,
					'type'       => Constants::TYPE_PRODUCT,
					'base'       => 1,
				],
			]
        ];
        //这里接入ExtraFieldHandler 但是缩小影响范围 特殊处理 仅选品入库展示
        $excludeOriginFields = [
            Constants::TYPE_LEAD_POOL => [
                'pin_user_list',
            ],
        ];
        if(self::$scene == self::CUSTOMER_EXPORT && $type == Constants::TYPE_COMPANY ){
            $extraSettings[$type] = array_merge($extraSettings[$type], $extraSettings[Constants::TYPE_COMPANY_POOL]);
        }
        $swarmExtraSettings = $originType == \Constants::TYPE_COMPANY ?  SwarmApi::getExtraField() : [];
        $originTypeExtraSettings = [];
        if ($originType && $originType != $type) {
            $originTypeExtraSettings = $extraSettings[$originType] ?? [];
        }

       if(isset($extraSettings[$type])) {

           $setting = $extraSettings[$type];

           //订单模块权限限制
            if($type == Constants::TYPE_ORDER) {

                //是否有回款单模块
                $hasCashCollectionFunctional = PrivilegeService::getInstance(\User::getLoginUser()->getClientId())->hasFunctional(PrivilegeConstants::FUNCTIONAL_CASH_COLLECTION);
                //是否有oms模块
                $hasPurchaseOrderFunctional = PrivilegeService::getInstance(\User::getLoginUser()->getClientId())->hasFunctional(PrivilegeConstants::FUNCTIONAL_PURCHASE_ORDER);

                if(!$hasCashCollectionFunctional) {
                    $setting = array_filter($setting,function ($value){
                        return $value['id'] != 'cash_collection';
                    });
                }

                if(!$hasPurchaseOrderFunctional) {
                    $setting = array_filter($setting,function ($value){
                        return !in_array($value['id'],['stock_up_status','shipping_status','end_status']);
                    });
                }

                //是否有erp授权
                $hasLesseeRecord = \common\library\erp_service\QCloudLesseeService::hasLesseeRecord(\User::getLoginUser()->getClientId());
                if (!$hasLesseeRecord) {
                    $setting = array_filter($setting,function ($value){
                        return !in_array($value['id'],['erp_status']);
                    });
                }
            }

           $extraSettingConfig = array_column($setting, null, 'id');
           $originTypeExtraSettings && $extraSettingConfig = array_merge(array_column($originTypeExtraSettings, null, 'id'), $extraSettingConfig);
           $swarmExtraSettings = array_column($swarmExtraSettings[$type] ?? [], null, 'id');

           $result['extra'] = array_merge($swarmExtraSettings, $extraSettingConfig);
           //排除源type字段
           foreach ($excludeOriginFields[$originType] ?? [] as $field) {
               if (isset($result['extra'][$field])) {
                   unset($result['extra'][$field]);
               }
           }

           $ignoreField = [];


           if (in_array($type, [Constants::TYPE_COMPANY, Constants::TYPE_LEAD])) {

               $type == \Constants::TYPE_COMPANY && $ignoreField = array_merge(...RuleConfigConstants::FILTER_FIELDS_IGNORE[RuleConfigConstants::RULE_CONFIG_TYPE_COMPANY_FULL_FIELD]);

               $ignoreField[] = $assessFlag ? 'score' : 'assess';

               foreach ($ignoreField as $field) {

                   unset($result['extra'][$field]);
               }
           }


            if ($type != Constants::TYPE_OPPORTUNITY) {
                array_walk($result['extra'], function (&$item) use ($type){
                    $item['type'] = $type;
                });
            }


	       $result['extra'] = array_values($result['extra']);
       }
        return $result;
    }

    public static function setDefaultFieldWidth($type, $result)
    {
        $setting = [
            Constants::TYPE_COMPANY => [
                'company' => [
                ],
                'customer' => [
                    'email' => 240,
                    'remark' => 240,
                ],
                'extra' => [
                    'archive_type' => 120,
                ],
            ],
            Constants::TYPE_OPPORTUNITY => [
                'fields' => [
                    'currency' => 80,
                    'stage' => 200,
                    'amount' => 120,
                    'origin_list' => 200,
                    'type' => 120,
                    'fail_type' => 200,
                ],
                'extra' => [
                    'stage_info.success_rate' => 64,
                ],
            ],
            Constants::TYPE_CASH_COLLECTION => [
                'fields' => [
                    'amount' => 150,
                    'currency' => 80,
                    'date' => 200,
                    'type' => 120,
                    'payee' => 200,
                    'comment' => 200,
                ],
                'extra' => [
                    'cash_collection_no' => 200,
                    'user_info.nickname' => 200,
                    'company_info.name' => 200,
                    'order_info.name' => 200,
                    'opportunity_info.name' => 200,
                    'create_time' => 180,
                    'update_time' => 180,
                ],
            ],
            Constants::TYPE_PRODUCT => [
                'fields' => [
                    'name' => 200,
                    'images' => 100,
                    'product_no' => 160,
                    'model' => 160,
                    'group_id' => 160,
                    'fob' => 200,
                    'cost_with_tax' => 160,
                    'minimum_order_quantity' => 100,
                    'description' => 300,
                    'update_user' => 100,
                    'update_time' => 160,
                ],
                'extra' => [

                ],
            ],
            Constants::TYPE_SCHEDULE => [
                'fields' => [
                ],
            ],
            Constants::TYPE_PLATFORM_PRODUCT_SKU => [
                'fields' => [
                    'product_image' => 88,
                    'third_sku_code' => 180,
                    'local_sku' => 180,
                    'is_match' => 88,
                ],
            ],
            Constants::TYPE_TRADE_DOCUMENT => [
                'fields' => [
                    'name' => 200,
                    'category' => 100,
                    'owner' => 100,
                    'status' => 88,
                    'is_accessible' => 88,
                    'latest_track' => 300,
                ],
            ]
        ];

        $defaultSetting = [
            'name'     => 260,
            'star'     => 160,
            'score'    => 90,
            'tag'      => 300,
            'homepage' => 240,
            'address'  => 300,
            'email'    => 240,
            'remark'   => 240,
        ];

        $fieldTypeSetting = [
            CustomFieldService::FIELD_TYPE_TEXT            => 200,
            CustomFieldService::FIELD_TYPE_TEXTAREA        => 200,
            CustomFieldService::FIELD_TYPE_SELECT          => 200,
            CustomFieldService::FIELD_TYPE_DATE            => 120,
            CustomFieldService::FIELD_TYPE_NUMBER          => 200,
            CustomFieldService::FIELD_TYPE_IMAGE           => 200,
            CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT => 200,
            CustomFieldService::FIELD_TYPE_ATTACH          => 200,
            CustomFieldService::FIELD_TYPE_FIELDS          => 200,
            CustomFieldService::FIELD_TYPE_DATETIME        => 180,
        ];

        if (array_key_exists($type, $setting)) {
            foreach ($result as $fieldListKey => &$fieldList) {
                // 额外字段合并
                if (!array_key_exists($fieldListKey, $setting[$type])) {
                    continue;
                }
                $fieldSettings = $setting[$type][$fieldListKey];
                foreach ($fieldList as &$field) {
                    $field['width'] = $fieldSettings[$field['id']] ?? ($defaultSetting[$field['id']] ?? (isset($field['field_type']) ? ($fieldTypeSetting[$field['field_type']] ?? 200) : 200));
                }
            }
        }

        return $result;
    }

    /**
     * @param $type
     * @return array|mixed
     * @deprecated
     */
    public static function getDefaultSortField($type)
    {

        $sortSetting = [
            Constants::TYPE_LEAD => [
                'name',
                'customer.email',
                'status',
                'ai_tags',
                'create_time',
            ],
            /**
             * @deprecated
             */
            Constants::TYPE_OPPORTUNITY => [
                'name',
                'company.name',
                'customer.name',
                'amount',
                'currency',
                'account_date',
                'stage_name',
                'success_rate',
                'origin_name',
                'type_name',
                'main_user_info.nickname',
                'handler_info.nickname',
                'fail_type_name',
                'create_user_info.nickname',
                'create_time',
                'update_time',
                'edit_time',
                'trail_time',
                'stage_stay_time',
//                'create_type',    //产品要求先不显示
            ],
            Constants::TYPE_COMPANY => [
                'name',
            ],
            UserSetting::PRIVATE_COMPANY_LIST_FIELD => [
                'name',
                'customer.name',
                'customer.email',
                'group_name',
                'owner',
                'star',
                'trail_status_name',
                'tag',
                'score',
                'order_time',
                'create_time',
            ],
            UserSetting::PUBLIC_COMPANY_LIST_FIELD => [
                'name',
                'pool_name',
                'last_owner_name',
                'country',
                'biz_type',
                'origin_name',
                'score',
                'order_time',
                'create_time',
            ],
        ];

        return $sortSetting[$type] ?? [];
    }

    public static function trailCompanyStatisticByUser($userId, $clientId, $begin, $end)
    {
        $list = new \common\library\trail\DynamicListAbstract($clientId);
        $list->setCreateUser($userId);
        $list->setOperatorUserId($userId);
        $list->setBeginTime($begin);
        $list->setEndTime($end);
        $data = $list->getAllStatistics('company');
        return $data;
    }

    /**
     * 提交跟进类型动态
     *
     * @param $userId
     * @param $companyId
     * @param $content
     * @param $remarkType
     * @param $customerId
     * @param $fileIds
     * @param $remarkTime
     * @param $directOwner
     * @param $address,
     * @param $longitude
     * @param $latitude
     * @param $refer_id
     * @return RemarkEvents
     * @depracated see RemarkService@remark
     */
    public static function remark(
        $userId,
        $companyId,
        $content,
        $remarkType,
        $customerId,
        $fileIds,
        $remarkTime,
        $directOwner = false,
        $address = '',
        $longitude = 0,
        $latitude = 0,
        $refer_id = 0,
        $checkOwner = true
    )
    {
        $user = \User::getUserObject($userId);
        $clientId = $user->getClientId();
        $remarkTime = date('Y-m-d H:i:s', empty($remarkTime) ? time() : strtotime($remarkTime));

        if (empty($customerId)) {
            $customerId = 0;
        }

        $company = new Company($clientId, $companyId);
        if ($checkOwner && !$company->checkOwner($user, $directOwner)) {
            throw new \RuntimeException(\Yii::t('customer', 'Non-own data'));
        }

        $customer = null;
        if ($customerId) {
            try {
                $customer = new Customer($clientId, $customerId);
                if ($customer->company_id != $companyId) {
                    throw new \RuntimeException(\Yii::t('trail', 'Contact does not belong to the company'));
                }
            } catch (\ProcessException $e) {
                throw new \RuntimeException($e->getMessage());
            }
        }

        $data = [
            'content' => $content,
            'file_ids' => $fileIds,
            'address' => $address,
            'longitude' => round(floatval($longitude), 6),
            'latitude' => round(floatval($latitude), 6)
        ];

        $event = new RemarkEvents();
        $event->setType($remarkType);
        $event->setCompanyId($companyId);
        $event->setCustomerId($customerId);
        $event->setClientId($clientId);
        $event->setCreateUser($userId);
        $event->setCreateTime($remarkTime);
        $event->setUserId($userId);
        $event->setReferId($refer_id);
        $event->setData($data);
        $event->run();

	    if (!empty($fileIds)) {

		    $fileIDArr = [];

		    foreach ($fileIds as $fileId) {

			    $fileIDArr[] = ['file_id' => $fileId];
		    }

		    CompanyFileService::batchSave($userId, $companyId, $fileIDArr);
	    }

        return $event;
    }

    /**
     * 客户动态设置
     *
     * @param $clientId
     * @return bool
     */
    public static function DynamicTrailCheck($clientId)
    {
        $client = Client::getClient($clientId);
        $setting = $client->getSettingAttributes([Client::SETTING_KEY_CUSTOMER_DYNAMIC_TRAIL]);

        if (isset($setting[Client::SETTING_KEY_CUSTOMER_DYNAMIC_TRAIL]) &&
            $setting[Client::SETTING_KEY_CUSTOMER_DYNAMIC_TRAIL] == 0)
        {
            return false;
        } elseif (isset($setting[Client::SETTING_KEY_CUSTOMER_DYNAMIC_TRAIL]) &&
            $setting[Client::SETTING_KEY_CUSTOMER_DYNAMIC_TRAIL] == 1)
        {
            return true;
        } else {
            return true;
        }
    }

    /**
     * 客户动态设置开关
     *
     * @param $clientId
     * @param int $enable
     */
    public static function DynamicTrailSwitch($clientId, $enable = 1)
    {
        $client = Client::getClient($clientId);
        $client->setSettingAttributes([Client::SETTING_KEY_CUSTOMER_DYNAMIC_TRAIL => $enable]);
        $client->saveSettingAttributes();
    }

    /**
     * 检查传递的field 是否存在
     * @param $key
     * @param string $checkField
     * @return bool
     */
    public static function checkIsExistField($key, $checkField ='customer'){

        $flag = false;
        $user = \User::getLoginUser();
        $setting = UserSetting::getSetting($user->getClientId(), $user->getUserId(), $key);
        $fieldList = $setting->getValue();

        if (empty($fieldList) || !is_array($fieldList))
            return $flag;

        foreach ($fieldList as $fieldSetting)
        {
            if($fieldSetting['id'] == $checkField)
                return true;

            if(strpos($fieldSetting['id'], $checkField) !== false)
                return true;
        }

        return $flag;
    }


    public static function buildPbSearchField($searchField)
    {
        switch ($searchField) {
            case \protobuf\services\customer\PBCompanySearchField::GLOBAL_SEARCH:
                //全局搜索
                $result = null;
                break;
            case \protobuf\services\customer\PBCompanySearchField::COMPANY_NAME:
                //公司名称
                $result = 'name';
                break;
            case \protobuf\services\customer\PBCompanySearchField::SERIAL_ID:
                //客户编号
                $result = 'serial_id';
                break;
            case \protobuf\services\customer\PBCompanySearchField::HOME_PAGE:
                //主页地址
                $result = 'homepage';
                break;
            case \protobuf\services\customer\PBCompanySearchField::CUSTOMER_NAME:
                //联系人电话
                $result = 'customer_list.name';
                break;
            case \protobuf\services\customer\PBCompanySearchField::CUSTOMER_TEL:
                //邮箱地址
                $result = 'customer_list.tel';
                break;
            case \protobuf\services\customer\PBCompanySearchField::EMAIL_ADDRESS:
                //社交账号
                $result = 'customer_list.email';
                break;
            case \protobuf\services\customer\PBCompanySearchField::CUSTOMER_CONTACT:
                //社交账号
                $result = 'customer_list.contact.value';
                break;
            default:
                throw new \RuntimeException('params error! undefined or unsupport search field: '.$searchField);
        }

        return $result;
    }

    /**
     * 检查客群列表中 field 是否存在
     * @param $swarm_id
     * @param $checkField
     * @return bool
     */
    public static function checkIsExistFieldBySwarm($swarm_id, $checkField = 'customer', $scene = 'swarm') {
        
        return self::checkIsExistFieldByScene($swarm_id, $checkField, $scene);
    }

    public static function getCompanyListFieldList($clientId, $userId, $key, $scene = '', $keyOnly = false)
    {
    
        if (empty($key)) {
    
            return [];
        }
        
        if (in_array($scene, ['swarm', 'publicSwarm']) && is_numeric($key)) {
            $swarm_id = $key;
            $cache = ClassCacheRepository::instance($clientId, self::class);
            $fieldList = $cache->remember("checkIsExistFieldBySwarm:{$swarm_id}.{$scene}", function () use ($clientId, $userId, $swarm_id, $scene) {
    
                /** @var \common\library\setting\library\swarm\SwarmApi|\common\library\setting\library\publicSwarm\PublicSwarmApi $api */
                $api = \common\library\setting\item\Api::$scene($clientId, $userId);
                return $api->getFieldList($swarm_id);
            });
        } else {
            $setting = UserSetting::getSetting($clientId, $userId, $key);
            $fieldList = $setting->getValue() ?: [];
        }

        if ($keyOnly) {
            $idKey = in_array($scene, ['swarm', 'publicSwarm']) ? 'field' : 'id';
            return array_column($fieldList, $idKey);
        }

        return $fieldList;
    }

    public static function checkIsExistFieldByScene($key, $checkField = 'customer', $scene = '')
    {
        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $fieldList = self::getCompanyListFieldList($clientId, $userId, $key, $scene);
        $idKey = in_array($scene, ['swarm', 'publicSwarm']) ? 'field' : 'id';

        if (empty($fieldList) || !is_array($fieldList))
            return false;

        foreach ($fieldList as $field) {
            if (empty($field[$idKey])) {
                continue;
            }
            if($field[$idKey] == $checkField)
                return true;
            if(strpos($field[$idKey], $checkField) === 0)
                return true;
        }

        return false;
    }


    /**
     * 获取用户的自定义拓展信息
     * @param $key
     * @return array|mixed
     */
    public static function getExternalValueByKey($key){


        $keys = [
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_DEFAULT_CURRENCY,
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_DEFAULT_EXCHANGE,
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_PERFORMANCE_SHOW_TYPE,
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_PRODUCT_LIST_SHOW_FILED,
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_COMPANY_SEARCH_FILED,
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_CUSTOMER_SEARCH_FILED,
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_DASHBOARD_SALES_WORK_REPORT,
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_LEAD_LIST_SORT,
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_LEAD_SEARCH_FILED,
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_LEAD_CUSTOMER_SEARCH_FILED,
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_LEAD_EXPORT_FILED,
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_LEAD_CUSTOMER_EXPORT_FILED,
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_OPPORTUNITY_LIST_SORT,
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_OPPORTUNITY_EXTERNAL_SEARCH_FIELD,
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_PRODUCT_EXTERNAL_SEARCH_FIELD,
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_PRODUCT_INVENTORY_EXTERNAL_SEARCH_FIELD,
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_ORDER_SEARCH_FILED,
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_ORDER_PROFIT_SEARCH_FILED,
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_SUPPLIER_EXTERNAL_SEARCH_FIELD,
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_CASH_COLLECTION_EXTERNAL_SEARCH_FIELD,
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_PURCHASE_ORDER_EXTERNAL_SEARCH_FIELD,
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_CASH_COLLECTION_INVOICE_EXTERNAL_SEARCH_FIELD,
            \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_COST_INVOICE_EXTERNAL_SEARCH_FIELD,
        ];

        if( !in_array($key,$keys) )
            throw  new \RuntimeException('user external key 不存在！');
        $user = \User::getLoginUser();
        $attrs =  $user->getInfoObject()->getExtentAttributes([$key]);
        $value = $attrs[$key];

        if(is_null($value)) {
            $defaultSetting = [
                UserInfoExternal::EXTERNAL_KEY_LEAD_LIST_SORT             => Constants::TYPE_LEAD,
//                UserInfo::EXTERNAL_KEY_OPPORTUNITY_LIST_SORT      => Constants::TYPE_OPPORTUNITY,
//                UserInfo::EXTERNAL_KEY_ALL_COMPANY_LIST_FIELD     => UserInfo::EXTERNAL_KEY_ALL_COMPANY_LIST_FIELD,
//                UserInfo::EXTERNAL_KEY_PUBLIC_COMPANY_LIST_FIELD  => UserInfo::EXTERNAL_KEY_PUBLIC_COMPANY_LIST_FIELD,
//                UserInfo::EXTERNAL_KEY_PRIVATE_COMPANY_LIST_FIELD => UserInfo::EXTERNAL_KEY_PRIVATE_COMPANY_LIST_FIELD,
            ];
            if (array_key_exists($key, $defaultSetting)) {

                $value = self::getDefaultSortField($defaultSetting[$key]);
            }
        }

        return $value;
    }

    /**
     * @param $clientId
     * @param $userId
     * @param $originName
     * @return int
     * 用于富通迁移
     */
    public static function findOrCrateOrigin($clientId, $userId, $originName)
    {
        static $originMap;

        $originName = trim($originName);
        if ($originName == '') {
            return 0;
        }

        if (!isset($originMap[$originName])) {

            $api = new OriginApi($clientId);
            $originId = $api->getIdByName($originName);
            if(!$originId) {
                try {
                    $api->setOpUser($userId);
                    $originId = $api->create([
                        'item_name' => $originName
                    ]);
                } catch (\Exception $e) {
                    \LogUtil::error($e->getMessage());
                    return 0;
                }
                if (!$originId) {
                    return 0;
                }
            }
            $originMap[$originName] = $originId;

//            $origin = new FieldItemSetting($clientId, \common\library\setting\item\ItemSettingConstant::MIGRATION_ITEM_TYPE_OF_ORIGIN);
//            $origin->loadByItemName($originName);
//
//            if ($origin->isNew()){
//                $origin->client_id = $clientId;
//                $origin->item_type = \common\library\setting\item\ItemSettingConstant::MIGRATION_ITEM_TYPE_OF_ORIGIN;
//                $origin->item_name = $originName;
//                $origin->field_type = FieldItemSetting::FIELD_TYPE_NORMAL;
//                $origin->color = '';
//                $origin->expand_field = '';
//                $origin->update_user = $userId;
//                $origin->update_time = date("Y-m-d H:i:s");
//
//                try{
//                    $result = $origin->save();
//                }catch (\Exception $e){
//                    \LogUtil::info("findOrCrateOrigin save fail:{$e->getMessage()}");
//                    return 0;
//                }
//
//                if (!$result) {
//                    \LogUtil::info("findOrCrateOrigin save fail! 1");
//                    return 0;
//                }
//            }else{
//
//                if ($origin->enable_flag == 0) {
//
//                    $origin->enable_flag = 1;
//                    $result = $origin->save();
//
//                    if (!$result) {
//                        \LogUtil::info("findOrCrateOrigin save fail! 2");
//                        return 0;
//                    }
//
//                }
//            }
//
//            $originMap[$originName] = $origin->item_id;
        }

        return $originMap[$originName];
    }

    public static function getAvailableUserNum($clientId, $userId, $privatePrivilege, $publicPrivilege)
    {
        $userNum = [];
        if (\common\library\privilege_v3\Helper::hasPermission($clientId, $userId, $privatePrivilege)) {
            $userNum = array_merge([1, 2], $userNum);
        }

        if (\common\library\privilege_v3\Helper::hasPermission($clientId, $userId, $publicPrivilege)) {
            $userNum = array_merge([0], $userNum);
        }

        if (empty($userNum)) {
            throw new \RuntimeException(\ErrorCode::CODE_MODULE_PRIVILEGE, implode('|', [$privatePrivilege, $publicPrivilege]));
        }

        return $userNum;
    }

    public static function handleStatisticScene($scenario, $sceneData, CompanyList $list, $clientId, $userId)
    {
        $sceneData = json_decode($sceneData, true);
        $workReportDate = $sceneData['work_report_date'];
        switch ($scenario) {
            case CompanyList::SCENARIO_STATISTIC_CUSTOMER_ADD :
                [$startDay, $endDay] = DateUtil::between($workReportDate);
                $companyIds = CustomerHelper::getAddCompanyIds($clientId, $userId, $startDay, $endDay);
                $list->setCompanyIds(empty($companyIds) ? 0 : $companyIds);
                break;
            case CompanyList::SCENARIO_STATISTIC_FOLLOW_CUSTOMER :
                if (in_array($workReportDate, ['today','week','month','yesterday', 'lastMonth', 'lastWeek'])) {

                    if (in_array($workReportDate, ['yesterday', 'lastMonth', 'lastWeek'])) {
                        $companyIds = CustomerHelper::getFollowCompanyIds($clientId, $userId, $workReportDate);
                        $list->setCompanyIds(empty($companyIds) ? 0 : $companyIds);
                    }
                    //本日
                    if (in_array($workReportDate, ['today'])) {

                        $list->setCompareDayOp(1);
                        $list->setCompareDay(1);
                    }
                    //本周
                    if (in_array($workReportDate, ['week'])) {
                        $week = date("w");
                        $list->setCompareDayOp(1);
                        $list->setCompareDay($week);
                    }
                    //本月
                    if (in_array($workReportDate, ['month'])) {
                        $day = date("d");
                        $day = intval($day);
                        $list->setCompareDayOp(1);
                        $list->setCompareDay($day);
                    }

                } else {
                    throw new \RuntimeException(\Yii::t('common', 'Parameter error'));
                }
                break;
            default:
                throw new \RuntimeException(\Yii::t('common', 'Parameter error'));
                break;
        }
        return $list;
    }

    public static function isArchiveByCompanyHashId($clientId,array $companyHashIds = [])
    {
        if (empty($companyHashIds)) {
            return [];
        }
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $table = \CompanyModel::model()->tableName();
        $companyHashIds = array_map(function ($companyHashId) {
            return "'" . $companyHashId . "'";
        }, $companyHashIds);
        $companyHashIdStr = implode(',', $companyHashIds);

        $sql = "select * from {$table} where client_id=:client_id and is_archive=1 and company_hash_id::text <> ''::text and company_hash_id in ({$companyHashIdStr})";
        return array_column($db->createCommand($sql)->queryAll(true, ['client_id' => $clientId]), 'company_hash_id');
    }

    /**
     * @param $clientId
     * @param $userId
     * @param $toUserId
     * @param $operatorType 1:转移 2:分享客户 3:重新分配客户 其余不做操作
     * @param $companyIds
     */
    public static function opportunityOperatorTypeHandle($clientId, $userId, $toUserId, $operatorType, $companyIds)
    {
        switch ($operatorType)
        {
            case 1:
                $userNum = \common\library\privilege_v3\Helper::getUserNumByPrivilege($clientId, $userId, [PrivilegeConstants::PRIVILEGE_CRM_COMPANY_SHARE]);
                if (!empty($userNum)) {
                    $operator = new CompanyBatchOperator($userId);
                    $operator->setParams(['company_ids' => $companyIds, 'user_num' => $userNum]);
                    $operator->getList()->showAll(true, PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW);
                    $operator->share($toUserId);
                }
                break;
            case 2:
                $userNum = \common\library\privilege_v3\Helper::getUserNumByPrivilege($clientId, $userId, [PrivilegeConstants::PRIVILEGE_CRM_COMPANY_TRANSFER]);
                if (!empty($userNum)) {
                    $operator = new CompanyBatchOperator($userId);
                    $operator->setParams(['company_ids' => $companyIds, 'user_num' => $userNum]);
                    $operator->getList()->showAll(true, PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW);
                    $operator->transfer($toUserId, false);
                }
                break;
            case 3:
                if (\common\library\privilege_v3\Helper::hasPermission($clientId, $userId, PrivilegeConstants::PRIVILEGE_CRM_COMPANY_ASSIGN)) {
                    $operator = new CompanyBatchOperator($userId);
                    $operator->setParams(['company_ids' => $companyIds, 'user_num' => [1, 2]]);
                    $operator->getList()->showAll(true, PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW);
                    $operator->transfer($toUserId, true);
                }
                break;
            default:
                break;
        }

    }

    private static function makePaypalFieldSettings()
    {
        return  [
            [
                'id' => 'transaction_no',
                'type' => Constants::TYPE_PAYPAL,
                'name' => \Yii::t('field', 'PayPal交易号'),
                'field_type' => 2,
                'base' => 1,
                'width' => 200,
            ],
            [
                'id' => 'invoice_id',
                'type' => Constants::TYPE_PAYPAL,
                'name' => \Yii::t('field', '账单号'),
                'field_type' => 2,
                'base' => 1,
                'width' => 285,
            ],
            [
                'id' => 'amount',
                'type' => Constants::TYPE_PAYPAL,
                'name' => \Yii::t('field', '金额'),
                'field_type' => 5,
                'base' => 1,
                'width' => 152,
            ],
            [
                'id' => 'currency',
                'type' => Constants::TYPE_PAYPAL,
                'name' => \Yii::t('field', '货币'),
                'field_type' => 1,
                'base' => 1,
                'width' => 112,
            ],
            [
                'id' => 'create_time',
                'type' => Constants::TYPE_PAYPAL,
                'name' => \Yii::t('field', '日期'),
                'field_type' => 4,
                'base' => 1,
                'width' => 213,
            ],
            [
                'id' => 'receive_name',
                'type' => Constants::TYPE_PAYPAL,
                'name' => \Yii::t('field', '付款人'),
                'field_type' => 1,
                'base' => 1,
                'width' => 164,
            ],
            [
                'id' => 'receive_email',
                'type' => Constants::TYPE_PAYPAL,
                'name' => \Yii::t('field', '付款人邮箱'),
                'field_type' => 1,
                'base' => 1,
                'width' => 256,
            ],
            [
                'id' => 'status',
                'type' => Constants::TYPE_PAYPAL,
                'name' => \Yii::t('field', '状态'),
                'field_type' => 5,
                'base' => 1,
                'width' => 421,
            ],
            [
                'id' => 'note',
                'type' => Constants::TYPE_PAYPAL,
                'name' => \Yii::t('field', '收款说明'),
                'field_type' => 5,
                'base' => 1,
                'width' => 421,
            ]
        ];
    }


    /**
     * 客户筛选条件
     * @param $clientId
     * @param $userId
     * @param $publicFlag
     * @return array
     */
    public static function getCompanyFilterList($clientId,$userId,$publicFlag, $isApp = false){

        //客户星级
        $starData = [];
        for ($i = 0; $i <= 5; $i++) {
            $starData[] = ['key' => \Yii::t('customer', 'star_'.$i), 'value' => strval($i)];
        }

        $userNumLData = [       //私海
            ['key'=>\Yii::t('customer', 'Follow up by one people'),'value'=>'1'],
            ['key'=>\Yii::t('customer', 'Follow up by many people'),'value'=>'2'],
        ];

        $searchModelData = [
            ['key'=>\Yii::t('customer', 'Word search'),'value'=>'0'],
            ['key'=>\Yii::t('customer', 'Prefix matching'),'value'=>'2'],
        ];

        $tagMatchModeData = [
            ['key'=>\Yii::t('customer', 'A customer that contains any label'),'value'=>'1'],
            ['key'=>\Yii::t('customer', 'Contains all labels of the customer'),'value'=>'0'],
        ];

        $pinData = [
            ['key'=>\Yii::t('customer', 'Add the attention'),'value'=>'1'],
//            ['key'=>\Yii::t('customer', 'Remove attention'),'value'=>'0'],
        ];
        $compareDayOpData = [
            ['key'=>\Yii::t('customer', 'Have a contact'),'value'=>'1'],
            ['key'=>\Yii::t('customer', 'Did not contact'),'value'=>'-1'],
        ];

        $stageTypeData = [
            ['key'=>\Yii::t('customer', 'Opportunities in progress'),'value'=>'1'],
            ['key'=>\Yii::t('customer', 'Win opportunities'),'value'=>'2'],
            ['key'=>\Yii::t('customer', 'Lose opportunities'),'value'=>'3'],
            ['key'=>\Yii::t('customer', 'No opportunities'),'value'=>'0'],

        ];

        $archiveData = [
            ['key'=>\Yii::t('customer', 'Unlimited'),'value'=>'0'],
            ['key'=>\Yii::t('customer', 'Auto create'),'value'=>'2'],
            ['key'=>\Yii::t('customer', 'Manually create'),'value'=>'1'],
            ['key'=>\Yii::t('customer', 'Auto file Suggestions'),'value'=>'3'],
            ['key'=>\Yii::t('customer', 'Sync from Alibaba'),'value'=>'4'],
            ['key'=>\Yii::t('customer', 'Create From OKKI Card'),'value'=>'7'],
         ];

        $annualProcurementData = [];
        foreach ( CustomerOptionService::annualProcurementMap() as $k => $item )
        {
            $annualProcurementData[] = ['key' => $item, 'value' => $k];
        }

        $intentionLevelData = [];
        foreach ( CustomerOptionService::intentionLevelMap() as $k => $item )
        {
            $intentionLevelData[] = ['key' => $item, 'value' => $k];
        }

        $willMoveToPublicMap = [];
        foreach ( CustomerOptionService:: willMoveToPublicMap() as $k => $item )
        {
            $willMoveToPublicMap[] = ['key' => $item, 'value' => $k];
        }

        $filterMap = [
            [   'title' => \Yii::t('customer', 'Search Model'),
                'key' =>'search_model',
                'group_id' =>'search_model',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                'data' => $searchModelData,
                'split' => null
            ],
            [   'title' => \Yii::t('customer', 'Keyword'),
                'key' =>'keyword',
                'group_id' =>'keyword',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_TEXT,
                'data' => null,
                'split' => null
            ],
            [   'title' => \Yii::t('customer', 'Follow up people'),
                'key' =>'user_id',
                'group_id' =>'user_id',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => null,
                'split' => null
            ],
            [   'title' => \Yii::t('customer', 'Customer star'),
                'key' => 'star',
                'group_id' => 'star',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => $starData,
                'split' => null
            ],
            [   'title' => \Yii::t('customer', 'Customer Tag match model'),
                'key' =>'tag_match_mode',
                'group_id' =>'tag_match_mode',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                'data' => $tagMatchModeData,
                'split' => null
            ],
            [   'title' => \Yii::t('customer', 'Customer tag'),
                'key' =>'tags',
                'group_id' =>'tags',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => null,
                'split' => null
            ],
            [   'title' => \Yii::t('customer', 'Is pin'),
                'key' =>'pin',
                'group_id' =>'pin',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                'data' => $pinData,
                'split' => null
            ],
            [
                'title' => \Yii::t('customer', 'Customer status'),
                'key' => 'status_id',
                'group_id' => 'status_id',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => null,
                'split' => null
            ],
            [
                'title' => \Yii::t('customer', 'Compare day flag'),
                'key' => 'compare_day_op',
                'group_id' => 'compare_day',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                'data' => $compareDayOpData,
                'split' => null
            ],
            [
                'title' => \Yii::t('customer', 'Compare day'),
                'key' => 'compare_day',
                'group_id' => 'compare_day',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'data' => null,
                'split' => \Yii::t('customer', 'day'),
            ],
            [
                'title' => \Yii::t('customer', 'Create date'),
                'key' => 'create_date',
                'group_id' => 'create_date',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                'data' => [
                    ['key' => 'start_date'],
                    ['key' => 'end_date']],
                'split' => null
            ],
            [
                'title' => \Yii::t('customer', 'Public time'),
                'key' => 'public_time',
                'group_id' => 'public_time',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_DATE,
                'data' => [
                    ['key' => 'public_begin_time'],
                    ['key' => 'public_end_time']],
                'split' => null
            ],

			[
                'title' => \Yii::t('customer', 'Next Follow-up Time'),
                'key' => 'next_follow_up_time',
                'group_id' => 'next_follow_up_time',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_DATE_RANGE,
                'data' => [
                    ['key' => 'next_follow_up_begin_time'],
                    ['key' => 'next_follow_up_end_time']],
                'split' => null
            ],

            [
                'title' => \Yii::t('customer', 'Stage type'),
                'key' => 'stage_type',
                'group_id' => 'stage_type',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => $stageTypeData,
                'split' => null
            ],
            [
                'title' => \Yii::t('customer', 'Country/Area'),
                'key' => 'country',
                'group_id' => 'country',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_OTHER,
                'data' => null,
                'split' => null
            ],
            [
                'title' => \Yii::t('customer', 'Customer group'),
                'key' =>'group_id',
                'group_id' =>'group_id',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => null,
                'split' => null
            ],
            [
                'title' => \Yii::t('customer', 'Customer biz type'),
                'key' =>'biz_type',
                'group_id' =>'biz_type',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => null,
                'split' => null
            ],
            [
                'title' => \Yii::t('customer', 'Customer pool'),
                'key' =>'pool_id',
                'group_id' =>'pool_id',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => null,
                'split' => null
            ],

            [
                'title' => \Yii::t('customer', 'Customer source'),
                'key' => 'origin_list',
                'group_id' => 'origin_list',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => null,
                'split' => null
            ],

            [
                'title' => \Yii::t('customer', 'Source detail'),
                'key' => 'ali_store_id',
                'group_id' => 'ali_store_id',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => null,
                'split' => null
            ],

            [
                'title' => \Yii::t('customer', 'Customer category'),
                'key' => 'category_ids',
                'group_id' => 'category_ids',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => null,
                'split' => null
            ],
            //由于安卓不支持 , 先屏蔽
            [
                'title' => \Yii::t('customer', 'Annual Procurement'),
                'key' => 'annual_procurement',
                'group_id' => 'annual_procurement',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => $annualProcurementData,
                'split' => null
            ],
            [
                'title' => \Yii::t('customer', 'Intention Level'),
                'key' => 'intention_level',
                'group_id' => 'intention_level',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => $intentionLevelData,
                'split' => null
            ],
            [
	            'title'      => \Yii::t('customer', 'Will Public'),
	            'key'        => 'will_public',
	            'group_id'   => 'will_public',
	            'base'       => CustomFieldService::FIELD_SYS_FLAG,
	            'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
	            'data'       => $willMoveToPublicMap,
	            'split'      => null,
            ],
            [
                'title' => \Yii::t('customer', 'Success opportunity count'),
                'key' => 'min_success_opportunity_count',
                'group_id' => 'min_success_opportunity_count',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'data' => null,
                'split' => '~'
            ],
            [
                'title' => \Yii::t('customer', 'Success opportunity count'),
                'key' => 'max_success_opportunity_count',
                'group_id' => 'min_success_opportunity_count',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'data' => null,
                'split' => null
            ],
            [
                'title' => \Yii::t('customer', 'Performance order count'),
                'key' => 'min_performance_order_count',
                'group_id' => 'min_performance_order_count',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'data' => null,
                'split' => '~'
            ],
            [
                'title' => \Yii::t('customer', 'Performance order count'),
                'key' => 'max_performance_order_count',
                'group_id' => 'min_performance_order_count',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_NUMBER,
                'data' => null,
                'split' => null
            ],
            [
                'title' => \Yii::t('customer', 'Deal time'),
                'key' => 'deal_time',
                'group_id' => 'deal_time',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' =>  CustomFieldService::FIELD_TYPE_DATE,
                'data' => [
                    ['key' => 'deal_time_start_date'],
                    ['key' => 'deal_time_end_date']
                ],
                'split' => null
            ],
            [
                'title' => \Yii::t('customer', 'Create Mode'),
                'key' => 'archive_type',
                'group_id' => 'archive_type',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                'data' => $archiveData,
                'split' => null
            ],
            [
                'title' => \Yii::t('customer', 'Customer follow up'),
                'key' => 'user_num',
                'group_id' => 'user_num',
                'base'=>CustomFieldService::FIELD_SYS_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                'data' => $userNumLData,
                'split' => null
            ],
        ];


        $disableFields = \common\library\privilege_v3\Helper::getFieldIdByScope($clientId, $userId, $publicFlag ? PrivilegeConstants::FUNCTIONAL_COMPANY_POOL : PrivilegeConstants::FUNCTIONAL_CUSTOMER , Constants::TYPE_COMPANY, PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_DISABLE);


		$deviceType = (new PBHeader())->getDeviceType();

        //过滤字段
        foreach ($filterMap as $key =>$value){
            // 权限隐藏字段
            if (in_array($value['key'], $disableFields)) {
                unset($filterMap[$key]);
            }
            //公海客户不需要返回跟进情况
            if($publicFlag == 1 && $value['key'] == 'user_num'){
                unset($filterMap[$key]);
            }
            //没有公海权限
            if ($value['key'] == 'pool_id' &&
                !\common\library\privilege_v3\PrivilegeService::getInstance($clientId)->hasFunctional([PrivilegeConstants::FUNCTIONAL_COMPANY_POOL_SETTING])) {
                unset($filterMap[$key]);
            }

            //没有订单模块，过滤业绩
            if (($value['key'] == 'min_performance_order_count' || $value['key'] == 'max_performance_order_count') &&
                !\common\library\privilege_v3\PrivilegeService::getInstance($clientId)->hasFunctional([PrivilegeConstants::FUNCTIONAL_ORDER])) {
                unset($filterMap[$key]);
            }


            //公海客户，筛选条件
            if($publicFlag == 1 && $value['key'] == 'user_id'){
                $publicData =
                    [   'title' => \Yii::t('customer', 'Last Owner'),
                        'key' =>'last_owner_ids',
                        'group_id' =>'last_owner_ids',
                        'base'=>CustomFieldService::FIELD_SYS_FLAG,
                        'field_type' => CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
                        'data' => null,
                        'split' => null
                    ];
                $filterMap[$key] = $publicData;
            }

//            桌面端要
			if ($value['key'] === 'ali_store_id'
				&& (!in_array($deviceType, [DeviceType::MAC, DeviceType::WINDOWS])
					|| !AlibabaService::getInstance($clientId)->hasStore())) {

				unset($filterMap[$key]);
			}

			if ($value['key'] === 'next_follow_up_time'
				&& !in_array($deviceType, [DeviceType::MAC, DeviceType::WINDOWS])) {

				unset($filterMap[$key]);
			}

//			公海不需要这个字段
	        if ($value['key'] === 'will_public' && (!$isApp || $publicFlag)) {

		        unset($filterMap[$key]);
	        }

            // app端 暂不支持来源详情字段的筛选
            if ($value['key'] === 'ali_store_id' && $isApp && !(AppInfoService::versionGreater('4.4.5'))){
                unset($filterMap[$key]);
            }
        }

        $filterMap = array_values($filterMap);
        //自定义字段
        $fields = ['id','field_id','name','ext_info','field_type'];
        $CompanyCustomFieldList = \common\library\customer\Helper::getUserSearchSetting($userId,Constants::TYPE_COMPANY,false,$fields);

        $companyField =
            [
                'title' => \Yii::t('customer', 'Company Field'),
                'key' =>'company_field',
                'group_id' =>'company_field',
                'base'=>CustomFieldService::FIELD_CUSTOM_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_OTHER,
                'data' => $CompanyCustomFieldList['company']??[],
                'split' => null
            ];

        $customerField =
            [
                'title' => \Yii::t('customer', 'Customer Field'),
                'key' =>'customer_field',
                'group_id' =>'customer_field',
                'base'=>CustomFieldService::FIELD_CUSTOM_FLAG,
                'field_type' => CustomFieldService::FIELD_TYPE_OTHER,
                'data' => $CompanyCustomFieldList['customer']??[],
                'split' => null
            ];
        $filterMap[] = $companyField;
        $filterMap[] = $customerField;
        return $filterMap;
    }

    private static function makeCustomerAdviceFieldSettings()
    {
        return [
            [
                'id' => 'email',
                'type' => Constants::TYPE_CUSTOMER_ADVICE,
                'name' => \Yii::t('field', '联系人邮箱'),
                'field_type' => 1,
                'base' => 1,
                'width' => 200,
            ],
            [
                'id' => 'name',
                'type' => Constants::TYPE_CUSTOMER_ADVICE,
                'name' => \Yii::t('field', '联系人名称'),
                'field_type' => 1,
                'base' => 1,
                'width' => 200,
            ],
            [
                'id' => 'company_name',
                'type' => Constants::TYPE_CUSTOMER_ADVICE,
                'name' => \Yii::t('field', '建议关联公司'),
                'field_type' => 1,
                'base' => 1,
                'width' => 200,
            ],
            [
                'id' => 'user_name',
                'type' => Constants::TYPE_CUSTOMER_ADVICE,
                'name' => \Yii::t('field', '建议跟进人'),
                'field_type' => 1,
                'base' => 1,
                'width' => 112,
            ],
            [
                'id' => 'last_mail_subject',
                'type' => Constants::TYPE_CUSTOMER_ADVICE,
                'name' => \Yii::t('field', '最近邮件'),
                'field_type' => 1,
                'base' => 1,
                'width' => 200,
            ],
            [
                'id' => 'order_time',
                'type' => Constants::TYPE_CUSTOMER_ADVICE,
                'name' => \Yii::t('field', '最近联系时间'),
                'field_type' => 4,
                'base' => 1,
                'width' => 200,
            ],
            [
                'id' => 'not_accept_remark',
                'type' => Constants::TYPE_CUSTOMER_ADVICE,
                'name' => \Yii::t('field', '不采纳标记'),
                'field_type' => 1,
                'base' => 1,
                'width' => 200,
            ],
            [
                'id' => 'trust_level',
                'type' => Constants::TYPE_CUSTOMER_ADVICE,
                'name' => \Yii::t('field', '置信度'),
                'field_type' => 1,
                'base' => 1,
                'width' => 200
            ]
        ];
    }

    /**
     * @depracated
     */
    public static function paramsMapping($setting, CompanyList $companyList)
    {
        if (isset($setting['keyword']) && !empty($setting['keyword'])) {
            $companyList->setKeyword($setting['keyword']);
            $companyList->setSortBySearchScore(true);
            isset($setting['search_field']) && !empty($setting['search_field']) && $companyList->setSearchFields($setting['search_field']);
            if (isset($setting['search_model']) && $setting['search_model'] == Constants::SEARCH_MODEL_LIKE_RIGHT) {
                $companyList->setSearchFields(['name']);
                $companyList->setSearchModel($setting['search_model']);
            }
        }

        if (isset($setting['pin'])) $companyList->setIsPin($setting['pin']);
        if (isset($setting['status_id'])) $companyList->setTrailStatus($setting['status_id']);
        if (isset($setting['country'])) $companyList->setCountry($setting['country']);
        if (isset($setting['province'])) $companyList->setProvince($setting['province']);
        if (isset($setting['city'])) $companyList->setCity($setting['city']);
        if (isset($setting['biz_type'])) $companyList->setBizType($setting['biz_type']);
        if (isset($setting['origin'])) $companyList->setOrigin($setting['origin']);
        if (isset($setting['group_id'])) $companyList->setGroupId($setting['group_id']);
        if (isset($setting['pool_id'])) $companyList->setPoolId($setting['pool_id']);
        if (isset($setting['start_date']) || isset($setting['end_date'])) {
            $companyList->setArchiveTime($setting['start_date'], $setting['end_date']);
        }
        if (isset($setting['tags'])) $companyList->setTags($setting['tags']);
        if (isset($setting['pool_id'])) $companyList->setPoolId($setting['pool_id']);
        if(isset($setting['user_id'])) {
            $userId = is_array($setting['user_id']) && empty($setting['user_id']) ? null : $setting['user_id'];
            $companyList->setUserId($userId);

            //在boss端全部客户筛选条件包含user_id 需要将user_num设置为非全部客户;
            if(!empty($userId)){
                $userNum = $setting['user_num'] ?? [];
                $setting['user_num'] = empty($userNum) ? [1,2] : $userNum;
            }
        }
        if (isset($setting['user_num'])) $companyList->setUserNum($setting['user_num']);
        if (isset($setting['star'])) $companyList->setStar($setting['star']);
        if (isset($setting['category_ids'])) $companyList->setCategoryIds($setting['category_ids']);
        if (isset($setting['recent_select_flag'])) $companyList->setIsRecentSelect($setting['recent_select_flag']);
        if (isset($setting['will_public'])) $companyList->setPublicRemindDay($setting['will_public']);
        if (isset($setting['last_owner'])) $companyList->setLastOwner($setting['last_owner']);
        if (isset($setting['company_field'])) {
            $companyField = array_filter(json_decode($setting['company_field'], true) ?? []);
            if (!empty($companyField)) $companyList->setExternalFields(Constants::TYPE_COMPANY, $companyField);
        }
        if (isset($setting['customer_field'])) {
            $customerField = array_filter(json_decode($setting['customer_field'], true) ?? []);
            if (!empty($customerField)) $companyList->setExternalFields(Constants::TYPE_CUSTOMER, $customerField);
        }
        if (isset($setting['tag_match_mode'])) $companyList->setTagMatchMode($setting['tag_match_mode']);
        if (isset($setting['compare_day']) && $setting['compare_day'] != 0) $companyList->setCompareDay($setting['tag_match_mode']);
        if (isset($setting['compare_day_op'])) $companyList->setCompareDayOp($setting['compare_day_op']);
        if (isset($setting['acquired_company_day'])) $companyList->setAcquiredCompanyDay($setting['acquired_company_day']);
        if (isset($setting['show_all']) && $setting['show_all']) $companyList->showAll(true);
        if (isset($setting['stage_type'])) $companyList->setOpportunityStageType($setting['stage_type']);

        //other
        if (isset($setting['company_ids'])) $companyList->setIds($setting['company_ids']);
        if (isset($setting['no_contact_time'])) $companyList->setCompareDay($setting['no_contact_time']);

        return $companyList;
    }

    //todo 优化批量场景
    public static function aiEventReport(array $companys, $eventType, $userId, $clientId,$setUserId = false)
    {
        //查询出所有的originName
        /*
        $originList = new OriginList($clientId);
        $originList->setUserId($userId);
        $originList->getFormatter()->setSpecifyFields(['id', 'name']);
        $result = $originList->find();
        $originMap = array_column($result, 'name', 'id');
        */

        $originApi = new OriginApi($clientId);
        $originApi->setApiFields(['id','name']);
        $result = $originApi->listAll(true);
        $originMap = array_column($result, 'name', 'id');

        $eventReport = new EventsReport($clientId, $userId, \common\library\ai\service\EventsReport::REFER_TYPE_ARCHIVE);

        foreach ($companys as $company) {

            if ($setUserId) {
                $company['user_id'] = is_array($company['user_id'])?json_encode($company['user_id']):$company['user_id'];
                $user_ids = \common\library\util\PgsqlUtil::trimArray($company['user_id']);
                //兼容为空的情况
                if (empty($user_ids)) {
                    $user_ids = [0];
                }

                foreach ($user_ids as $user_id) {

                    $eventReport->setUserId($user_id);
                    $eventReport->setEvent($eventType);
                    $eventReport->setReferId($company['company_id'] ?? 0);
                    $originId = isset($company['origin_list']) ? ($company['origin_list'][0] ?? 0) : ($company['origin'] ?? 0);
                    $originName = $originMap[$originId] ?? '';

                    $extraData = [
                        'create_type' => EventsReport::CREATE_TYPE_MANUAL,
                        'email' => $company['main_customer_email'] ?? '',
                        'origin' => $originId,
                        'origin_name' => $originName,
                        'group_id' => $company['group_id'] ?? 0,
                        'company_name' => $company['name'] ?? '',
                        'homepage' => $company['homepage'] ?? '',
                        'country' => $company['country'] ?? '',
                        'scale_id' => $company['scale_id'] ?? 0,
                        'company_hash_id' => $company['company_hash_id'] ?? ''
                    ];

                    if ($company['archive_type'] == \common\library\customer_v3\company\orm\Company::ARCHIVE_TYPE_AI) {
                        $extraData['create_type'] = EventsReport::CREATE_TYPE_AUTO;
                    }

                    $eventReport->setExtraInfo($extraData);
                    $eventReport->addBodyItem();
                }
            } else {

                $eventReport->setReferId($company['company_id'] ?? 0);
                $eventReport->setEvent($eventType);
                $originId = $company['origin'] ?? 0;

                //来源名字
                $originName = $originMap[$originId] ?? '';

                $extraData = [
                    'create_type' => EventsReport::CREATE_TYPE_MANUAL,
                    'email' => $company['main_customer_email'] ?? '',
                    'origin' => $originId,
                    'origin_name' => $originName,
                    'group_id' => $company['group_id'] ?? 0,
                    'company_name' => $company['name'] ?? '',
                    'homepage' => $company['homepage'] ?? '',
                    'country' => $company['country'] ?? '',
                    'scale_id' => $company['scale_id'] ?? 0,
                    'company_hash_id' => $company['company_hash_id'] ?? ''
                ];

                if ($company['archive_type'] == \common\library\customer_v3\company\orm\Company::ARCHIVE_TYPE_AI) {
                    $extraData['create_type'] = EventsReport::CREATE_TYPE_AUTO;
                }

                if ($eventType == EventsReport::EVENT_DELETE) {
                    unset($extraData['create_type']);
                }

                $eventReport->setExtraInfo($extraData);
                $eventReport->addBodyItem();
            }
        }
        return $eventReport->report();
    }

    /**
     * @param $clientId
     * @param $companyId
     * @param null $type
     * @param null $time
     * @return bool|int
     */
    public static function updateDealTime($clientId, $companyId, $type = null, $time = null)
    {
        if (empty($companyId))
            return false;

        if ($type && $type != \CustomerOptionService::getDealSetting($clientId))
            return false;

        if($time == null)
            $time = date('Y-m-d H:i:s');
        else
            $time = date('Y-m-d H:i:s', strtotime($time));

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $companyId = is_array($companyId) ? $companyId : [$companyId];
        $companyIdsSql = '(' . implode(',', $companyId) . ')';
        $sql = "update tbl_company set deal_time='$time' where company_id in $companyIdsSql and client_id=$clientId and deal_time < '{$time}'";

        $res = $db->createCommand($sql)->execute();

        if ($res) {
            (new SwarmService($clientId))->refreshByRefer($companyId, [
                'deal_time',
            ]);
        }

        return $res;
    }

    public static function batchUpdateDealTime($clientId, $companyIdToTimeMap, $type = null)
    {
        if (isset($companyIdToTimeMap[0])) unset($companyIdToTimeMap[0]);
        if (empty($companyIdToTimeMap)) return false;

        if ($type && $type != CustomerOptionService::getDealSetting($clientId)) return false;

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $updateSql = "update tbl_company set deal_time = (CASE company_id ";
        foreach ($companyIdToTimeMap as $companyId => $dealTime)
        {
            $updateSql .= " WHEN {$companyId} THEN '{$dealTime}' ";
        }
        $companyIdsSql = implode(',', array_keys($companyIdToTimeMap));
        $updateSql .= "ELSE deal_time END) where company_id in ($companyIdsSql) and client_id = $clientId";

        $res = $db->createCommand($updateSql)->execute();

        if ($res) {
            (new SwarmService($clientId))->refreshByRefer(array_keys($companyIdToTimeMap), [
                'deal_time',
            ]);
        }

        return $res;
    }

    public static function refreshStaticCount($clientId, $companyIds, array $types)
    {
        $updateFields = [];
        $updateDealTimeFlag = false;
        $companyIds = is_array($companyIds) ? $companyIds : [$companyIds];

        if (empty($companyIds))
            return false;

        $companyIdsString = implode(',', $companyIds);
        $db = \PgActiveRecord::getDbByClientId($clientId);

        $companyMap = [];
        foreach ($companyIds as $companyId)
        {
            if (empty($companyId))
                continue;

            if (in_array(Constants::TYPE_OPPORTUNITY, $types))
                $companyMap[$companyId]['success_opportunity_count'] = 0;

            if (in_array(Constants::TYPE_ORDER, $types))
                $companyMap[$companyId]['performance_order_count'] = 0;
        }

        if (empty($companyMap))
            return false;

        //opportunity
        if (in_array(Constants::TYPE_OPPORTUNITY, $types))
        {
            $sql = "select stage_type,company_id,count(1), sum(amount_rmb) as amount_cny,sum(amount_usd) as amount_usd,max(account_date) as success_opportunity_time,min(account_date) as success_opportunity_first_time from tbl_opportunity where client_id=$clientId and company_id in ($companyIdsString) and enable_flag=1 and stage_type in (1,2) group by company_id,stage_type ";
            $list = $db->createCommand($sql)->queryAll(true);

            if ( \CustomerOptionService::getDealSetting($clientId) == \Constants::TYPE_OPPORTUNITY) {
                $updateDealTimeFlag = true;
            }

            foreach ($list as $item) {
                if ($item['stage_type'] == 2) {
                    $companyMap[$item['company_id']]['success_opportunity_count'] = $item['count'];
                    $companyMap[$item['company_id']]['success_amount_cny'] = $item['amount_cny'];
                    $companyMap[$item['company_id']]['success_amount_usd'] = $item['amount_usd'];
                    $companyMap[$item['company_id']]['latest_success_opportunity_time'] = $item['success_opportunity_time'];
                    $companyMap[$item['company_id']]['success_opportunity_first_time'] = $item['success_opportunity_first_time'];

                } else if($item['stage_type'] == 1) {
                    $companyMap[$item['company_id']]['ongoing_opportunity_count'] = $item['count'];
                }
            }

            $updateFields = array_merge($updateFields,[
                'success_opportunity_count',
                'success_opportunity_amount_cny',
                'success_opportunity_amount_avg_cny',
                'success_opportunity_amount_usd',
                'success_opportunity_amount_avg_usd',
                'latest_success_opportunity_time',
                'ongoing_opportunity_count',
                'deal_time',
            ]);
        }

        //order
        if (in_array(Constants::TYPE_ORDER, $types))
        {
            $sql = "select company_id, count(1) from tbl_order where client_id=$clientId and enable_flag=1 and account_flag=1 and company_id IN ($companyIdsString) group by company_id ";
            $list = $db->createCommand($sql)->queryAll(true);
            foreach ($list as $item) {
                $companyMap[$item['company_id']]['performance_order_count'] = $item['count'];
            }

            $updateFields = array_merge($updateFields,[
                'performance_order_count'
            ]);
        }

        $updateSqlArray = [];
        foreach ($companyMap as $companyId => $item) {
            $set = [];

            //商机
            if (in_array(Constants::TYPE_OPPORTUNITY, $types))            {
                if (isset($item['success_opportunity_count'])) {
                    $set[] = " success_opportunity_count = {$item['success_opportunity_count']}";
                } else {
                    $set[] = " success_opportunity_count = 0";
                }

                if (isset($item['success_amount_cny'])) {
                    $set[] = " success_opportunity_amount_cny = {$item['success_amount_cny']}";
                    //平均值
                    if ($item['success_opportunity_count'] > 0) {
                        $avgCny = $item['success_amount_cny'] / $item['success_opportunity_count'];
                        $set[] = " success_opportunity_amount_avg_cny = {$avgCny}";
                    }

                } else {
                    $set[] = " success_opportunity_amount_cny = 0";
                    $set[] = " success_opportunity_amount_avg_cny = 0";
                }

                if (isset($item['success_amount_usd'])) {
                    $set[] = " success_opportunity_amount_usd = {$item['success_amount_usd']}";
                    //平均值
                    if ($item['success_opportunity_count'] > 0) {
                        $avgUsd = $item['success_amount_usd'] / $item['success_opportunity_count'];
                        $set[] = " success_opportunity_amount_avg_usd = {$avgUsd}";
                    }
                } else {
                    $set[] = " success_opportunity_amount_usd = 0";
                    $set[] = " success_opportunity_amount_avg_usd = 0";
                }

                if (isset($item['latest_success_opportunity_time'])) {
                    $set[] = " latest_success_opportunity_time = '{$item['latest_success_opportunity_time']}'";
                } else {
                    $set[] = " latest_success_opportunity_time = '1970-01-01 00:00:00'";
                }

                //参考跟进规则
                if ($updateDealTimeFlag) {
                    if (isset($item['latest_success_opportunity_time'])) {
                        $set[] = " deal_time = '{$item['latest_success_opportunity_time']}'";
                    } else {
                        $set[] = " deal_time = '1970-01-01 00:00:00'";
                    }
                }

                if (isset($item['success_opportunity_first_time'])) {
                    $set[] = " success_opportunity_first_time ='{$item['success_opportunity_first_time']}'";
                } else {
                    $set[] = " success_opportunity_first_time = '1970-01-01 00:00:00'";
                }

                if (isset($item['ongoing_opportunity_count'])) {
                    $set[] = " ongoing_opportunity_count = {$item['ongoing_opportunity_count']}";
                } else {
                    $set[] = " ongoing_opportunity_count = 0";
                }
            }

            //订单
            if (in_array(Constants::TYPE_ORDER, $types))
            {
                if (isset($item['performance_order_count'])) {
                    $set[] = " performance_order_count = {$item['performance_order_count']}";
                } else {
                    $set[] = " performance_order_count = 0";
                }
            }

            if (!empty($set))
            {
                $set = implode(',', $set);
                $updateSqlArray[] = "update tbl_company set $set where company_id=$companyId";
            }
        }

        if (empty($updateSqlArray))
            return false;

        $updateSql = implode('; ', $updateSqlArray);

        $db->createCommand($updateSql)->execute();

        if(!empty($updateFields)) {
            (new SwarmService($clientId))->refreshByRefer($companyIds, $updateFields);
        }

        //opportunity 触发更新了 新建客户数配置了最近赢单日期以及首次赢单商机日期以及最近更新时间
        if (in_array(Constants::TYPE_OPPORTUNITY, $types)) {
            if (php_sapi_name() == "cli" || \Yii::app()->params['env'] == 'exp') {
                (new PerformanceV2RecordJob($clientId, \Constants::TYPE_COMPANY, $companyIds))->handle();
            } else {
                QueueService::dispatch(new PerformanceV2RecordJob($clientId, \Constants::TYPE_COMPANY, $companyIds));
            }
        }

        return true;
    }

    public static function getBizTypeList()
    {
        $bizTypeList = CustomerOptionService::$bizType;
        $result = [];
        foreach ($bizTypeList as $item) {
            $result[] = [
                'id' => ($item=='未分组') ? '' : $item,
                'name' => \Yii::t('customer', $item),
            ];
        }
            return $result;
    }

    public static function getStarList()
    {
        $stars = [1,2,3,4,5,0];
        $map = [];
        foreach ($stars as $item) {
            $map[] = [
                'id' => $item,
                'name' => \Yii::t('customer', 'star_' . $item),
            ];
        }
        return $map;
    }

    public static function getActivityList()
    {
        return [
            [
                'id' => 1,
                'name' => \Yii::t('report', '3天内有联系'),
            ],
            [
                'id' => 2,
                'name' => \Yii::t('report', '3-7天内有联系'),
            ],
            [
                'id' => 3,
                'name' => \Yii::t('report', '7-30天内有联系'),
            ],
            [
                'id' => 4,
                'name' => \Yii::t('report', '30-90天内有联系'),
            ],
            [
                'id' => 5,
                'name' => \Yii::t('report', '超过90天未联系'),
            ],
        ];
    }

    public static function getActivityType($num)
    {
        if ($num < 3) return Company::ACTIVITY_TYPE_THREE;
        if ($num >= 3 && $num < 7) return Company::ACTIVITY_TYPE_SEVEN;
        if ($num >= 7 && $num < 30) return Company::ACTIVITY_TYPE_THIRTY;
        if ($num >= 30 && $num < 90) return Company::ACTIVITY_TYPE_NINETY;
        else return Company::ACTIVITY_TYPE_MORE_THAN_NINETY;
    }

    public static function getCompanyActivityMap($companyActivityInfo)
    {
        $result = [];
        if (empty($companyActivityInfo)) return $result;
        foreach ($companyActivityInfo as $companyId => $item)
        {
            $diffDay = round((time() - strtotime($item))/3600/24);
            $result[$companyId] = \common\library\customer\Helper::getActivityType($diffDay);
        }
        return $result;
    }

    public static function hasFieldEditPermission($field, $clientId, $userId, $isPublic)
    {
        return in_array($field, \common\library\privilege_v3\Helper::getFieldIdByScope(
            $clientId,
            $userId,
            $isPublic ? \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_COMPANY_POOL : \common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_CUSTOMER,
            Constants::TYPE_COMPANY,
            \common\library\privilege_v3\PrivilegeConstants::FIELD_PRIVILEGE_SCOPE_READ_WRITE
            ));
    }


    /** 有关联商机的且有邮件动态的客户，返回邮件动态调整功能按钮
     * @param $companyId
     * @param $clientId
     * @param $loginUserId
     * @return array
     * @throws \Exception
     */
    public static function getEmailDynamicAdjustSwitch($companyId,$clientId,$loginUserId){

        $data = [
            'switch' => self::COMPANY_EMAIL_DYNAMIC_ADJUST_DISABLE,
            'permissions' => false
        ];
        //商机编辑权限
        if(!\common\library\privilege_v3\Helper::hasPermission($clientId,$loginUserId,PrivilegeConstants::PRIVILEGE_CRM_OPPORTUNITY_EDIT)
        ){
           return $data;
        }

        //判断客户是公海，还是私海
        $company = new Company($clientId,$companyId);
        if(!$company->isExist()){
            throw new \RuntimeException(\Yii::t('customer', 'Customer does not exist'));
        }
        $companyUserId = $company->getAttributes()['user_id'];
        if($companyUserId){
            //私海客户
            $permission = PrivilegeConstants::PRIVILEGE_CRM_COMPANY_EDIT;
        }else{
            //公海客户
            $permission = PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_EDIT;
        }
        //客户编辑权限
        if(!\common\library\privilege_v3\Helper::hasPermission($clientId,$loginUserId,$permission)){
            return $data;
        }

        //客户是否关联商机
        $list = new \common\library\opportunity\OpportunityList($clientId);
        $list->setUserType([1,2]);
        $list->setViewingUserId($loginUserId);
        $list->setShowAll(1);
        $list->setCompanyIds([$companyId]);
        $list->getFormatter()->listInfoSetting();
        $opportunityList = $list->find();

        if(!count($opportunityList)){
           return $data;
        }
        //判断是否有邮件动态
        $companyDynamicList = new CompanyDynamicList($clientId, $companyId);
        $companyDynamicList->setOperatorUserId($loginUserId);
        $companyDynamicList->setModuleId(TrailConstants::MODULE_MAIL);
        $companyDynamicCount = $companyDynamicList->count();
        if(!$companyDynamicCount){
           return $data;
        }

        //调整动态按钮允许展示
        $data['switch'] = self::COMPANY_EMAIL_DYNAMIC_ADJUST_ENABLE;

        $mainUserIds = [];
        $handlerInfo = [];
        $opportunityUserIds = [];

        foreach ($opportunityList as $item){
            $mainUserIds[] = $item['main_user'];
            $handlerInfo = array_merge($item['handler_info'],$handlerInfo);//商机团队成员
            $opportunityUserIds = array_merge($item['user_id'],$opportunityUserIds); //商机的所有跟进人
        }
        $opportunityUserIds = array_unique($opportunityUserIds);
        $mainUserIds = array_unique($mainUserIds);

        //判断用户是否商机主跟进人或者拥有商机编辑权限
        if(in_array($loginUserId,$mainUserIds) || \common\library\opportunity\Helper::checkOpportunityEditPrivilege($clientId,$loginUserId,$handlerInfo,$opportunityUserIds)){
            $data['permissions'] = true;
        }
        return $data;
    }

    /**
     * @param $clientId
     * @param $groupId
     * @param $userId
     * @param bool $throwException
     * @return bool
     * 检测user是否有权限使用group
     */
    public static function acceptUseGroup($clientId, $groupId, $userId, $throwException = true)
    {
        //未分组不处理
        if (!$groupId)
            return true;

        $api = new GroupApi($clientId, Constants::TYPE_COMPANY);
        $ownerIds = $api->getExternalValue(0, $groupId, \common\library\setting\library\group\Group::External_KEY_OWNER_ID);

        if (!$ownerIds || in_array($userId, $ownerIds)) {
            return true;
        }

        if ($throwException) {
            throw new \RuntimeException(\Yii::t('customer', 'Save failed! Administrator Settings you are not entitled to use the {groupName} group', ['{groupName}' => $api->getOneNameById($groupId)]));
        }

        return false;

//        $settingList = new GroupSettingList($clientId);
//        $settingList->setType(Constants::TYPE_COMPANY);
//        $settingList->setUserIds([0]);
//        $settingList->setKeys([Group::External_KEY_OWNER_ID]);
//        $settingList->setGroupIds([$groupId]);
//        $list = $settingList->find();

//        if (empty($list))
//            return true;
//
//        $value = reset($list);
//        $boll = (empty($value['value']) || in_array($userId, $value['value']));
//
//        if ($boll === true || !$throwException)
//            return $boll;
//
//        $group = new Group($clientId, Constants::TYPE_COMPANY, $groupId);
//        throw new \RuntimeException(\Yii::t('customer', 'Save failed! Administrator Settings you are not entitled to use the {groupName} group', ['{groupName}' => $group->name]));
    }

    //设置了子分组必选，则该分组必须为叶子分组
    public static function checkSubGroupSelect($clientId, $groupId, $throwException = true)
    {
        if ($groupId == 0) return true;
        $switchValue = \common\library\account\Helper::getClientSettingValue($clientId, Client::SETTING_KEY_COMPANY_SUBGROUP_MUST_SELECT_SWITCH);

        //判断是否勾选了子分组必选
        if (!$switchValue || strtolower(trim($switchValue)) == 'false') {
            return true;
        }

//        $hasChild = (new \common\library\group\Group($clientId, \Constants::TYPE_COMPANY, $groupId))->checkHasChild();
        $hasChild = (new GroupApi($clientId, \Constants::TYPE_COMPANY))->checkHasChild($groupId);
        //判断当前分组id是否还有子分组，有则抛出异常
        if ($hasChild) {
            if ($throwException) {
                throw new \RuntimeException(\Yii::t('customer', "当前所选客户分组仍有可选子分组，请完整选择"));
            }
            return false;
        }

        return true;
    }

    // 获取客户最近的关联线索
    // 走索引: index_lead_company_id[client_id, company_id]
    public static function getLatestRelateLead($clientId, $company_id)
    {
        $pgDb = \PgActiveRecord::getDbByClientId($clientId);
        $archiveType = Lead::LEAD_TYPE_ARCHIVE;
        $sql = "select lead_id from tbl_lead where client_id = {$clientId} and company_id = {$company_id} and is_archive = {$archiveType} order by archive_time desc limit 1";
        $ret = $pgDb->createCommand($sql)->queryScalar();
        return $ret ? $ret : 0;
    }

    public static function getOriginLeads($clientId,$company_id){
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $archiveType = \common\library\lead\Lead::LEAD_TYPE_ARCHIVE;
        $sql = "SELECT lead_id,name FROM tbl_lead WHERE client_id = {$clientId} AND company_id = {$company_id} AND  is_archive ={$archiveType}";
        $list = $db->createCommand($sql)->queryAll($sql);

        if($list){
            $company = new Company($clientId,$company_id);
            $mainLeadId = $company->getAttributes(['main_lead_id'])['main_lead_id']??0;
            foreach ($list as $key => $value){
                if($mainLeadId && $value['lead_id'] == $mainLeadId){
                    $list[$key]['main_lead_flag'] = 1;
                }else{
                    $list[$key]['main_lead_flag'] = 0;
                }
            }
        }

        return $list;
    }

    /**
     * @param $clientId
     * @param $companyId
     * @param $sortField
     * @param $sortType
     * @param $page
     * @param $pageSize
     * @param $product_type
     * @return array
     * @throws \ProcessException
     */
    public static function getRelatedProductList($clientId, $companyId, $sortField, $sortType, $page, $pageSize, $product_type){

        $companyId = intval($companyId);
        $clientId = intval($clientId);
        $list = [];
        $count = 0;
        $group_field = intval($product_type) === ProductConstant::PRODUCT_TYPE_SPU ? 'product_id' : 'sku_id';

        $db = \PgActiveRecord::getDbByClientId($clientId);

        //查询客户关联的订单产品，报价单产品
        $sql = "SELECT {$group_field} FROM tbl_invoice_product_record
                  WHERE client_id =:client_id
                  AND type IN(2,3)
                  AND company_id=:company_id
                  AND enable_flag=1
                  UNION
                      SELECT {$group_field}
                      FROM tbl_opportunity_product
                      WHERE client_id = :client_id
                      AND company_id = :company_id";

        $relatedProductList = $db->createCommand($sql)->queryAll(true, [':client_id'=> $clientId,':company_id'=>$companyId]);
        if(!$relatedProductList){
            return [$list,$count];
        }
        $relatedProducts = array_column($relatedProductList, $group_field);
        $count = count($relatedProducts);

        //查询产品的订单数
        $sql = "select {$group_field},COUNT( DISTINCT refer_id) AS total from tbl_invoice_product_record   where client_id = {$clientId} and type=2 AND company_id={$companyId} and enable_flag=1 group by {$group_field}  order by  total desc";
        $productList = $db->createCommand($sql)->queryAll();
        $invoiceMaps = array_column($productList, 'total',$group_field);

        //查询产品报价单数
        $sql = "select {$group_field},COUNT( DISTINCT refer_id) AS total from tbl_invoice_product_record   where client_id = {$clientId}  and type=3 AND company_id={$companyId} and enable_flag=1 group by {$group_field}  order by  total desc";
        $quotationList = $db->createCommand($sql)->queryAll();
        $quotationMas = array_column($quotationList, 'total',$group_field);

        //查询商机产品数
        $sql = "select {$group_field},COUNT(DISTINCT opportunity_id) AS total from tbl_opportunity_product   where client_id = {$clientId}  and company_id={$companyId}  group by {$group_field}  order by  total desc";
        $opportunityList = $db->createCommand($sql)->queryAll();
        $opportunityMaps = array_column($opportunityList, 'total', $group_field);

        $relatedProductMaps = [];
        foreach ($relatedProducts as $id) {
            $productItem = [
                'product_id' => '',
                'sku_id' => '',
                'invoice_count' => $invoiceMaps[$id] ?? 0,
                'quotation_count' => $quotationMas[$id] ?? 0,
                'opportunity_count' => $opportunityMaps[$id] ?? 0,
            ];
            $productItem[$group_field] = $id;
            $relatedProductMaps[] = $productItem;
        }

        //排序
        $type = strtolower($sortType) == 'desc' ? SORT_DESC : SORT_ASC;
        $relatedProductMaps = \Util::array_sort($relatedProductMaps,$sortField,$type);
        $productItemIds = array_column($relatedProductMaps, $group_field);
        $relatedProductMaps = array_combine($productItemIds,$relatedProductMaps);

        $offset = ($page-1)*$pageSize;

        //分页，需要返回的产品id列表
        $productItemIds = array_slice($productItemIds, $offset, $pageSize);
        if ($group_field == 'product_id')
        {
            $productApi = new ProductAPI($clientId);
            $productList = $productApi->items($productItemIds, APIConstant::SCENE_LIST, ['product_no','name','images','product_id','enable_flag','disable_flag']);
        } else {
            $skuApi = new SkuAPI($clientId);
            $productList = $skuApi->items($productItemIds);
        }

        $productIdMaps = array_column($productList, null, $group_field);

        $list = [];
        //返回客户关联成交的产品列表
        foreach ($productItemIds as $itemId) {
            if (!$itemId)
                continue;

            if (!isset($productIdMaps[$itemId])) {
                $productIdMaps[$itemId] = [
                    'product_id' => 0,
                    'sku_id' => 0,
                    'name' => '',
                    'product_no' => '',
                    'sku_code' => '',
                    'images' => [],
                    'product_disable_flag' => 0,
                    'disable_flag' => 0,
                    'enable_flag' => 0,
                ];
            }

            $invoiceCount = $relatedProductMaps[$itemId]['invoice_count'] ?? 0;
            $quotationCount = $relatedProductMaps[$itemId]['quotation_count'] ?? 0;
            $opportunityCount = $relatedProductMaps[$itemId]['opportunity_count'] ?? 0;
            $name = $productIdMaps[$itemId]['name'] ?? '';
            $productIdMaps[$itemId]['enable_flag'] === 0 && $name .= '(已删除)';
            $productNo = $group_field == 'product_id' ? $productIdMaps[$itemId]['product_no'] : $productIdMaps[$itemId]['sku_code'] ?? '';

            if ($group_field == 'product_id') {
                $images = isset($productIdMaps[$itemId]['images']) ? (is_array($productIdMaps[$itemId]['images']) ? $productIdMaps[$itemId]['images']:json_decode($productIdMaps[$itemId]['images'], true)) : [];
                $images = $images[0]['src'] ?? '';
            } else {
                $skuAttrInfo = $productIdMaps[$itemId]['attributes_info'] ?? [];
                $images = isset($productIdMaps[$itemId]['image_info']) ? $productIdMaps[$itemId]['image_info']['file_path'] : '';
            }

            $data = [
                'product_id' => $productIdMaps[$itemId]['product_id'] ?? '',
                'sku_id' => $productIdMaps[$itemId]['sku_id'] ?? '',
                'attributes_info' => $skuAttrInfo ?? [],
                'product_enable_flag' => $productIdMaps[$itemId]['enable_flag'],
                'name' => $name,
                'product_no' => $productNo,
                'images' => $images,
                'invoice_count' => $invoiceCount,
                'quotation_count' => $quotationCount,
                'opportunity_count' => $opportunityCount,
                'product_disable_flag' => $productIdMaps[$itemId]['product_disable_flag'] ?? $productIdMaps[$itemId]['disable_flag'] ?? 0,
            ];
            $list[] = $data;
        }

        return [$list,$count];

    }

    public static function getCustomerName($clientId,$companyId,$customerId){
        $customerList = new CustomerList($clientId);
        $customerList->setCompanyId($companyId);
        $customerList->setCustomerId($customerId);
        $customerList->setFields(['name']);
        $customer = $customerList->find();
        $customerName = "";
        if($customer){
            $customerName = $customer[0]['name']??"";
        }
        return $customerName;
    }

    //根据客户邮箱获取私海客户联系人
    public static function getPrivateCustomer($clientId, $userId, $email,$aliStoreId = 0)
    {
        if(empty($email))
        {
            return [];
        }
        $userId = intval($userId);
        $clientId = intval($clientId);
        $aliStoreId = intval($aliStoreId);
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $email = \Util::escapeDoubleQuoteSql($email);
        $sql = "SELECT c.company_id,c.customer_id,cy.ali_store_id,cy.update_time FROM tbl_customer  c
                left join  tbl_company cy
                on cy.company_id  = c.company_id
                WHERE c.client_id= {$clientId}
                AND c.user_id @> ARRAY[{$userId}]::bigint[]
                AND lower(c.email)='{$email}'
                AND c.is_archive =1";
        if($aliStoreId){
            $sql .= " AND cy.ali_store_id  @> ARRAY[{$aliStoreId}]::bigint[]";
        }
        $sql .= " ORDER  BY cy.update_time DESC";

        $customers = $db->createCommand($sql)->queryAll();

        return $customers;
    }

    //根据buyer_account_id获取私海客户联系人
    public static function getPrivateCustomerByBuyer($clientId, $userId, $buyer_account_id,$aliStoreId = 0)
    {
        if(empty($buyer_account_id))
        {
            return [];
        }
        $userId = intval($userId);
        $clientId = intval($clientId);
        $aliStoreId = intval($aliStoreId);
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $sql = "SELECT c.company_id,c.customer_id,cy.ali_store_id,cy.update_time FROM tbl_alibaba_customer_relation  r
                left join tbl_customer c on r.customer_id=c.customer_id
                left join  tbl_company cy on cy.company_id  = c.company_id
                WHERE r.client_id= {$clientId}
                AND r.buyer_account_id = {$buyer_account_id}
                AND r.customer_id > 0
                AND c.user_id @> ARRAY[{$userId}]::bigint[]
                AND c.is_archive =1";

        if($aliStoreId){
            $sql .= " AND r.store_id = {$aliStoreId}";
        }
        $sql .= " ORDER  BY cy.update_time DESC";

        $customers = $db->createCommand($sql)->queryAll();

        return $customers;
    }


    public static function getPrivateCustomerByIds($clientId, $userId,$customerIds){
        if(empty($customerIds))
        {
            return [];
        }

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $customerIdStr = join(',',$customerIds);
        $sql = "SELECT c.company_id,c.customer_id,cy.ali_store_id,lower(c.email) as email,cy.ali_store_id FROM tbl_customer c
                    LEFT JOIN tbl_company cy
                    on c.company_id = cy.company_id
                    WHERE c.client_id={$clientId}
                    AND c.user_id @> ARRAY[{$userId}]::bigint[]
                    AND c.customer_id in({$customerIdStr})
                    AND c.is_archive =1
                    ORDER  BY cy.update_time DESC ";

        $customerList = $db->createCommand($sql)->queryAll();
        return $customerList;
    }


	/**
	 * @throws \CDbException
	 * @throws \ProcessException
	 * @throws \CException
	 */
	public static function getNotOneSelfCustomerByIds($clientId, $userId, $customerIds){
		if(empty($customerIds))
		{
			return [];
		}

		$db = \PgActiveRecord::getDbByClientId($clientId);
		$customerIdStr = join(',',$customerIds);
		$sql = "SELECT c.company_id,c.customer_id,cy.ali_store_id,lower(c.email) as email,cy.ali_store_id FROM tbl_customer c
                    LEFT JOIN tbl_company cy
                    on c.company_id = cy.company_id
                    WHERE c.client_id={$clientId}
                    AND not c.user_id @> ARRAY[{$userId}]::bigint[]
                    AND c.customer_id in({$customerIdStr})
                    AND c.is_archive =1
                    ORDER  BY cy.update_time DESC ";

		return $db->createCommand($sql)->queryAll();
	}


    /**
     * 转化线索建为客户
     *
     * @param Lead $lead
     * @param Company $company
     * @param integer $userId
     * @return Customer[]|bool
     */
    public static function leadTransformCompany(Lead $lead, Company &$company, int $userId, bool $strict=false, bool $allowRepeatCustomer = false)
    {
        $customerList = [];
        $transform = false;
        $companyIsNewFlag = false;
        $mainCustomerFlag = false;

        $fields = [
            'company' => [
                'name',
                'short_name',
                'origin_list',
                'category_ids',
                'country',
                'scale_id',
                'address',
                'homepage',
                'fax',
                'tel',
                'remark'
            ],
            'customer' => [
                'name',
                'email',
                'post_grade',
                'post',
                'tel_list',
                'contact',
                'gender',
                'birth',
                'image_list'
            ]
        ];

        if (!$lead->isExist())
            return false;

        foreach ($fields['company'] as $field) {
            if (!empty($lead->$field) && empty($company->$field)) {
                $company->$field = $lead->$field;
                $transform = true;
            }
        }

        if ($company->isNew()) {
            $company->main_lead_id = $lead->lead_id;
            $companyIsNewFlag = true;
        }

        if (!empty($company->main_customer)) {
            $mainCustomerFlag = true;
        }

        $list = new LeadCustomerList($lead->client_id);
        $list->setLeadId($lead->lead_id);
        $list->setIsArchive(Lead::ARCHIVE_OK);
        $list->setFilterEmptyEmail(true);
        $leadCustomers = $list->find();
        if( $strict && empty($leadCustomers) )
        {
            \LogUtil::error("转客户失败, 没有联系人: {$lead->lead_id}");
            throw new \RuntimeException(\Yii::t('common','{param} cannot be empty',['{param}'=>"LeadTransformCompanyEmail"]));
        }

        $customerFieldClass = new CustomerField($lead->client_id);
        $customerFieldData = Helper::stripFormat([$customerFieldClass->format()]);

        $allLeadCustomerEmails = array_column($leadCustomers, 'email');
        $archiveCustomerList = new CustomerList($lead->client_id);
        $archiveCustomerList->setFields(['email']);
        $archiveCustomerList->setEmail($allLeadCustomerEmails);
        $archiveCustomerList->setIsArchive(true);
        $archiveCustomerList->setCheckPoolDuplicateSwitchFlag(true);
		$archiveCustomerList->setPoolId($company->pool_id ?? current(\common\library\customer\pool\Helper::getUserLastPoolMap($lead->client_id, [$userId], $company->isPublic())));
        $archiveCustomerEmails = array_column($archiveCustomerList->find(), 'email');

        foreach ($leadCustomers as $leadCustomer) {
            if (!empty($archiveCustomerEmails) && in_array($leadCustomer['email'], $archiveCustomerEmails) && !$allowRepeatCustomer) {
                continue;
            }
            $customer = new Customer($lead->client_id);
            //联系人需要初始化清空所有数据
            $customer->name = '';
            $customer->post_grade = 0;
            $customer->post = '';
            $customer->tel_list = [];
            $customer->birth = '';
            $customer->gender = 0;
            $customer->remark = '';
            $customer->contact = [];
            $customer->image_list = [];
            $customer->external_field_data = $customerFieldData['external_field_data'] ?? [];
            $customer->main_customer_flag = 0;

            if ($allowRepeatCustomer) {
                $customer->setSkipDuplicateCheck(true);
            }
            $flag = false;
            foreach ($fields['customer'] as $field) {
                if (empty($customer->$field) && !empty($leadCustomer[$field])) {
                    $customer->$field = $leadCustomer[$field];
                    $flag = true;
                }
            }

            if (!$flag && !$customer->isNew() && !$customer->isExist()) {
                $flag = true;
            }

            if ($flag) {
                if (empty($company->main_customer) && empty($customer->main_customer_flag) && !empty($leadCustomer['main_customer_flag'])) {
                    $customer->main_customer_flag = 1;
                    $mainCustomerFlag = true;
                }

                $customer->setSourceCustomerId($leadCustomer['customer_id']);
                $customerList[] = $customer;
            }
        }

        if (!empty($customerList)) {
            if ($mainCustomerFlag === false) {
                $customer = reset($customerList);
                $customer->main_customer_flag = 1;
            }

            if ($company->isNew()) {
                $company->setCustomerList($customerList);
            } else {
                $company->addCustomers($customerList);
            }
            $transform = true;
        }

        // 如果联系人数组为空，阻止线索转化客户
        if ($company->isNew() && empty($customerList)) {
            $transform = false;
        }

        if ($transform) {
            $lead->transDataForCompany($company);
        }

        $transform = $transform ? $company->save() : $transform;
        if ($transform) {

            $lead->setOperatorUserId($userId);
            $lead->archive($company, $companyIsNewFlag);
        }

        return $customerList;
    }

    public static function emailExists($clientId, $email, $companyId = 0, $userId = 0)
    {
        $emailUnique = EmailUnique::getInstance($clientId);
        return $emailUnique->isExists($email, $companyId, $userId);
    }

    public static function emailCanArchive($clientId, $email, $companyId = 0)
    {
        $emailUnique = EmailUnique::getInstance($clientId);
        return $emailUnique->canArchive($email, $companyId);
    }

    public static function loadCustomerIdByEmail($clientId, $userId, $email)
    {
        if (empty($clientId) || empty($userId) || empty($email)) return 0;

        $customerList = new CustomerList($clientId);
        $customerList->setAlias('a');
        $findFieldsArray = ['a.email', 'a.customer_id', 'b.user_id', 'b.pool_id', 'b.scope_user_ids'];
        $customerList->setJoin((new CompanyFilter($clientId, 'b')), BaseList::JOIN_TYPE_LEFT, ['company_id', 'company_id']);
        $findFields = implode(',', $findFieldsArray);
        $customerList->setFields($findFields);
        $customerList->setEmail($email);
        $customerList->setOrderBy('order_time');
        $customerList->setOrder('desc');
        $customerList = $customerList->find();

        $allCompanyUserIds = [];
        $allCustomerPoolIds = [];
        $allScopeUserIds = [];
        foreach ($customerList as $item) {
            $itemUserIds = is_array($item['user_id']) ? $item['user_id'] : PgsqlUtil::trimArray($item['user_id']);
            $itemScopeUsers = is_array($item['scope_user_ids']) ? $item['scope_user_ids'] : PgsqlUtil::trimArray($item['scope_user_ids']);
            $allCustomerPoolIds[] = $item['pool_id'];
            $allCompanyUserIds = array_merge($allCompanyUserIds, $itemUserIds);
            $allScopeUserIds = array_merge($allScopeUserIds, $itemScopeUsers);
        }
        $allCompanyUserIds = array_unique($allCompanyUserIds);
        $allCustomerPoolIds = array_unique($allCustomerPoolIds);
        $emailType =  (new AccessService($clientId, $userId))
            ->setCompanyInfoData($clientId, $allCompanyUserIds, $allCustomerPoolIds, $allScopeUserIds)
            ->cardType();
        if (!in_array($emailType, [
            EmailIdentity::CARD_TYPE_COMPANY,
            EmailIdentity::CARD_TYPE_CAN_NOT_MANAGE_COMPANY,
            EmailIdentity::CARD_TYPE_PUBLIC_COMPANY,
            EmailIdentity::CARD_TYPE_CAN_NOT_MANAGE_PUBLIC_COMPANY,
            EmailIdentity::CARD_TYPE_CAN_MANAGE_COLLEAGUES_COMPANY,
        ])) return 0;

        $targetCustomerId = 0;
        foreach ($customerList as $item)
        {
            $itemUserIds = is_array($item['user_id']) ? $item['user_id'] : PgsqlUtil::trimArray($item['user_id']);
            $itemScopeUsers = is_array($item['scope_user_ids']) ? $item['scope_user_ids'] : PgsqlUtil::trimArray($item['scope_user_ids']);
            $itemPoolId = $item['pool_id'];

            $itemCardType = (new AccessService($clientId, $userId))
                ->setCompanyInfoData($clientId, $itemUserIds, $itemPoolId, $itemScopeUsers)
                ->cardType();
            if ($emailType != $itemCardType) continue;
            $targetCustomerId = $item['customer_id'];
            break;
        }
        if (empty($targetCustomerId)) return 0;
        return $targetCustomerId;
    }

    public static function companyFieldConflictList(
        $userId,
        array $matchedRules,
        $skipPrivilege = true,
        $userNum = [0,1,2],
        $limit = null
    ){
        if (empty($matchedRules))
            return [];

        $companyIds = [];
        $duplicateFieldMap = [];
        foreach ($matchedRules as $rule)
        {
            $companyIds[] = $rule['refer_id'];
            $duplicateFieldMap[$rule['refer_id']][] = $rule['name'];
        }

        $user = \User::getUserObject($userId);

        $companyList = new CompanyList($userId);
        $companyList->setIds($companyIds);
        $companyList->setSkipPrivilege($skipPrivilege);
        $companyList->showAll(true);
        $companyList->setIncludeDeleteUserFlag(true);
        $companyList->setFields([
            'company_id',
            'serial_id',
            'name',
            'user_id',
            'scope_user_ids',
            'pool_id',
            'create_time',
            'last_owner',
        ]);
        if ($limit)
            $companyList->setLimit($limit);
        if ($userNum)
            $companyList->setUserNum($userNum);

        $companyList = $companyList->find();
        if (empty($companyList))
            return [];

        $dataList = [];
		$poolNameMap = \common\library\customer\pool\Helper::getFilterPoolNameMap($user->getClientId(), array_unique(array_column($companyList, 'pool_id')), true);
        foreach ($companyList as $company)
        {
            $company['user_id'] = PgsqlUtil::trimArray($company['user_id']);
            $company['scope_user_ids'] = PgsqlUtil::trimArray($company['scope_user_ids']);

            $accessService = (new AccessService($user->getClientId(), $userId));
            $accessService->setCompanyInfoData($user->getClientId(), $company['user_id'], $company['pool_id'], $company['scope_user_ids']);

            $owner = [];
            foreach ($company['user_id'] as $followUserId)
            {
                $item = intval($followUserId);
                $user = \User::getUserObject($item);
                if (!$user->hasInfo()) {
                    continue;
                }
                $owner[] = $user->getNickname();
            }

            $last_owner = '';
            if (isset($company['last_owner']) && $company['last_owner'] > 0) {
                $last_owner_user = \User::getUserObject($company['last_owner']);
                if ($last_owner_user->hasInfo()) {
                    $last_owner = $last_owner_user->getNickname();
                }
            }

            $duplicateFieldNames = array_values(array_unique($duplicateFieldMap[$company['company_id']] ?? []));

            $dataList[] = [
                'company_id' => $company['company_id'],
                'name' => $company['name'],
                'owner' => $owner,
                'last_owner' => $last_owner,
                'is_viewable' => $accessService->isViewable() ? 1 : 0,
                'is_editable' => $accessService->isEditable(),
                'is_transform' => $accessService->isTransform() ? 1 : 0,
                'check_owner' => $accessService->checkOwner() ? 1 : 0,
                'is_public' => empty($company['user_id']) ? 1 : 0,
                'has_pool_privilege' => $accessService->hasPoolPrivilege() ? 1 : 0,
                'user_ids' => $company['user_id'],
                'create_time' => $company['create_time'],
                'serial_id' => $company['serial_id'],
                'duplicate_fields' => $duplicateFieldNames,
				'pool_name' => $poolNameMap[$company['pool_id']] ?? '',
            ];
        }
        return  $dataList;
    }

    public static function leadFieldConflictList(
        $userId,
        array $matchedRules,
        $skipPrivilege = true,
        $userNum = [0,1,2],
        $limit = null
    ){
        if (empty($matchedRules))
            return [];

        $leadIds = [];
        $duplicateFieldMap = [];
        foreach ($matchedRules as $rule)
        {
            $leadIds[] = $rule['refer_id'];
            $duplicateFieldMap[$rule['refer_id']][] = $rule['name'];
        }

        $user = \User::getUserObject($userId);

        $leadList = new LeadList($userId);
        $leadList->setIds($leadIds);
        $leadList->setSkipPrivilege($skipPrivilege);
        $leadList->showAll(true);
        $leadList->setIncludeDeleteUserFlag(true);
        $leadList->setFields([
            'lead_id',
            'serial_id',
            'name',
            'user_id',
            'create_time',
            'scope_user_ids',
        ]);
        if ($limit)
            $leadList->setLimit($limit);
        if ($userNum)
            $leadList->setUserNum($userNum);

        $leadList = $leadList->find();
        if (empty($leadList))
            return [];

        $dataList = [];
        foreach ($leadList as $lead)
        {
            $lead['user_id'] = PgsqlUtil::trimArray($lead['user_id']);
            $lead['scope_user_ids'] = PgsqlUtil::arrayOrTrimArray($lead['scope_user_ids']);

            $accessService = (new LeadAccessService($user->getClientId(), $userId));
            $accessService->setLeadInfoData($user->getClientId(), $lead['user_id'], $lead['scope_user_ids']);

            $owner = [];
            foreach ($lead['user_id'] as $followUserId)
            {
                $item = intval($followUserId);
                $user = \User::getUserObject($item);
                if (!$user->hasInfo()) {
                    continue;
                }
                $owner[] = $user->getNickname();
            }

            $duplicateFieldNames = array_values(array_unique($duplicateFieldMap[$lead['lead_id']] ?? []));

            $dataList[] = [
                'lead_id' => $lead['lead_id'],
                'name' => $lead['name'],
                'owner' => $owner,
                'is_viewable' => $accessService->isViewable() ? 1 : 0,
                'is_editable' => $accessService->isEditable(false),
                'is_transform' => $accessService->isTransform() ? 1 : 0,
                'check_owner' => $accessService->checkOwner() ? 1 : 0,
                'is_public' => empty($lead['user_id']) ? 1 : 0,
                'has_pool_privilege' => $accessService->hasPoolPrivilege() ? 1 : 0,
                'user_ids' => $lead['user_id'],
                'create_time' => $lead['create_time'],
                'serial_id' => $lead['serial_id'],
                'duplicate_fields' => $duplicateFieldNames
            ];
        }

        return  $dataList;
    }

    /**
     * 获取可同步邮件动态的客户联系人
     *
     * 客户邮件动态同步逻辑
     *  个人私海客户邮件往来：
     *      开启状态：私海客户生成邮件动态
     *      关闭状态：私海客户生成邮件动态
     *  同事私海客户邮件往来：
     *      开启状态：同事私海生成邮件动态
     *      关闭状态：不生成邮件动态
     *  公海客户邮件往来：
     *      开启状态：公海客户生成邮件动态
     *      关闭状态：不生成邮件动态
     *
     * @param $clientId
     * @param $userId
     * @param $emails
     * @return array
     */
    public static function getAllowTrailCustomers($clientId, $userId, $emails)
    {
        $poolIds = null;
		$duplicateSwitchFlag = \common\library\customer\pool\Helper::getCheckPoolDuplicateSwitch($clientId);

		if ($duplicateSwitchFlag) {
            //如果开启了客户池判重
            $userId && \User::setLoginUserById($userId);
            $poolList = \common\library\customer\pool\Helper::getCustomerPoolList();
            $poolIds = array_column($poolList,'pool_id');
            //没有可查看的公海分组
            if (empty($poolIds)) {
                return [];
            }
        }


        $customerList = new CustomerList($clientId);
        $customerList->setAlias('a');
        $customerList->setEmail($emails);
        $customerList->setIsArchive(\common\library\customer_v3\customer\orm\Customer::IS_ARCHIVE_YES);
        $customerList->setFields(['a.is_archive', 'a.customer_id', 'a.company_id', 'a.name', 'a.email', 'a.user_id']);
        if ($duplicateSwitchFlag) {
            $customerList->setFields(['a.is_archive', 'a.customer_id', 'a.company_id', 'a.name', 'a.email', 'a.user_id','b.pool_id']);
            $customerList->setJoin((new CompanyFilter($clientId, 'b')), BaseList::JOIN_TYPE_LEFT, ['company_id', 'company_id']);
        }
        $result = $customerList->find();

        //开启了客户池判重,过滤有权限查看的公海分组
        if ($duplicateSwitchFlag) {
            $result = array_filter($result, function ($item) use ($poolIds) {
                return isset($item['pool_id']) && in_array($item['pool_id'],$poolIds);
            });
        }

        $dynamicTrailSwitch = \common\library\customer\Helper::DynamicTrailCheck($clientId);
        $customers = [
            'pool' => [],
            'self' => [],
            'other' => []
        ];

        foreach ($result as $item) {
            $userIds = \common\library\util\PgsqlUtil::trimArray($item['user_id']) ?? [];
            if (!empty($userIds) && in_array($userId, $userIds)) {
                $customers['self'][$item['company_id']] = $item;
            } elseif (!empty($userIds)) {
                $customers['other'][$item['company_id']] = $item;
            } elseif (empty($userIds)) {
                $customers['pool'][$item['company_id']] = $item;
            }
        }

        $trailCustomers = [];
        if (!empty($customers['self'])) {
            $trailCustomers = array_column($customers['self'], null, 'company_id');
        } elseif ($dynamicTrailSwitch && !empty($customers['other'])) {
            $trailCustomers = array_column($customers['other'], null, 'company_id');
        }

        if ($dynamicTrailSwitch && empty($customers['self']) && !empty($customers['pool'])) {
            $trailCustomers = array_column(array_merge($customers['pool'], $trailCustomers), null, 'company_id');
        }

        if ($dynamicTrailSwitch && !empty($customers['self']) && !empty($customers['other'])) {
            $trailCustomers = array_column(array_merge($customers['other'], $trailCustomers), null, 'company_id');
        }

        return $trailCustomers;
    }

    public static function getCompanyInfoMap($clientId, array $companyIds, bool $onlyArchived = false)
    {
        $map = [];
        if (empty($companyIds))
            return $map;

        $db = \PgActiveRecord::getDbByClientId($clientId);

        $fields = ['company_id', 'name', 'serial_id', 'pool_id', 'user_id', 'is_archive', 'create_time', 'trail_status', 'main_customer' , 'ongoing_opportunity_count' , 'performance_order_count' , 'order_time','latest_transaction_order_time', 'scope_user_ids'];

        $sql = 'select ' . implode(',', $fields) . ' from tbl_company where ';
        $where = ' client_id=:client_id';
        $params = [':client_id' => $clientId];
        if ($onlyArchived) {
            $where .= ' and is_archive=1';
        }
        SqlBuilder::buildIntWhere('', 'company_id', $companyIds, $where, $params);
        $sql .= $where;
        $list = $db->createCommand($sql)->queryAll(true, $params) ?: [];
        $map = array_column($list, null, 'company_id');

        foreach ($map as $k => $v) {
            $map[$k]['user_id'] = $v['user_id'] ? PgsqlUtil::trimArray($v['user_id']) : [];
            $map[$k]['scope_user_ids'] = $v['scope_user_ids'] ? PgsqlUtil::trimArray($v['scope_user_ids']) : [];
        }

        return $map;
    }

    public static function getDistinctPublicCustomerEmail($clientId, $emails)
    {
        $result = [];
        if (empty($emails)) return $result;

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $emailList = array_map(function ($item) {
            return \Util::escapeDoubleQuoteSql($item);
        }, $emails);
        $emailListStr = '\'' . implode("','", $emailList) . '\'';
        $sql = "select distinct email from tbl_customer where client_id = {$clientId} and is_archive = 1 and lower(email) in ($emailListStr) and user_id = '{}';";
        return $db->createCommand($sql)->queryColumn();
    }

    public static function getCompanyCustomerCount($clientId, $companyId)
    {
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $sql = "select count(1) from tbl_customer where client_id =:client_id and is_archive = 1 and company_id=:company_id;";
        return (int)$db->createCommand($sql)->queryScalar([':client_id' => $clientId, ':company_id'=> $companyId]);
    }

    /**
     * 判断groupId是否被company引用
     * @param $clientId
     * @param $groupId
     * @return mixed
     */
    public static function isCompanyReferGroupId($clientId, $groupId)
    {
        $sql = "select company_id from tbl_company where client_id=$clientId and group_id={$groupId} and is_archive =1 limit 1";
        $result = \PgActiveRecord::getDbByClientId($clientId)->createCommand($sql)->queryRow();
        if($result)
        {
            return true;
        }
        return false;
    }

    /**
     * 获取客服运营平台client=17383下添加的客户自定义字段okki_client_id不规范的设置的不可用companyIds
     * 客户ID自定义字段必须为数字
     * @param int $clientId
     * @return array|\CDbDataReader
     */
    public static function getOkkiInvalidateCompanyIds($clientId = \common\library\alibaba\Constant::OKKI_OPERATION_DEPARTMENT_CLIENT)
    {
        // os测试环境clientId环境测试
        if(\Yii::app()->params['env'] == 'test')
        {
            // 测试环境客服运营的clientId
            if( $clientId != 14350)
            {
                return [];
            }
            // 测试环境的客户Id的field
            $fieldId = 1118457633;
        }else
        {
            return [];
            // 非客服运营的clientId
            if( $clientId != \common\library\alibaba\Constant::OKKI_OPERATION_DEPARTMENT_CLIENT)
            {
                return [];
            }

            $fieldId = \common\library\alibaba\Constant::CUSTOMER_CLIENT_FIELD_ID;
        }

        $sql = "select company_id from tbl_company where client_id={$clientId} and
        is_archive > 0 and (external_field_data::jsonb->>'{$fieldId}'::text) !~ '^\\d+$' ";

        $companyIds = \PgActiveRecord::getDbByClientId($clientId)->createCommand($sql)->queryColumn();

        return $companyIds ?? [];
    }

    //同步leads智能获客openSearch
    public static function syncLeadsSearchCompany($clientId, $userIds, $companyHashIds, $opt, $company_ids, $emails=[]): void
    {
        $userId = $userIds[0] ?? 0;
        \LogUtil::info('syncLeadsSearchCompany for ' . $clientId, ['userId' => $userId, 'companyHashIds' => $companyHashIds, 'opt' => $opt, 'company_ids' => $company_ids, 'emails' => $emails]);
        if(empty($clientId) || empty($userId) || empty($companyHashIds) || empty($opt)){
            return;
        }
        if (!is_array($companyHashIds)){
            $companyHashIds = [$companyHashIds];
        }

        QueueService::dispatch(new LeadsCompanySearchSyncJob([
            'clientId' => $clientId,
            'userId' => $userId,
            'companyHashIds' => $companyHashIds,
            'type' => 'company',
            'action' => $opt, // add | del
            'ids' => $company_ids,
            'emails' => $emails
        ]));
    }

    //setNxFeed
    public static function batchSetnxUniqueFeeds($clientId, $opUserId, $companyUserIds, $data, $feedType = TodoConstant::TODO_TYPE_COMPANY_TRAIL_UPDATE)
    {
        //剪枝
        if (empty($opUserId) || empty($feedType) || empty($companyUserIds)) {
            return ;
        }
//        $nodeQueryStart = microtime(true);
        $companyIds = array_keys($companyUserIds);

//        $nodeQueryEnd = microtime(true);
//        $nodeQueryCost = round(($nodeQueryEnd-$nodeQueryStart) * 1000,2);
//        \LogUtil::info('查询关注耗时'.$nodeQueryCost.'ms');
//
//        $nodeQueryStart = microtime(true);

        $feeds = [];
        //构造不同user_id 对应的companyIds
        foreach ($companyUserIds as $companyId => $followUserIds) {

            $feedUserIds = is_array($followUserIds)?$followUserIds:PgsqlUtil::trimArray($followUserIds);
            if (empty($feedUserIds)) {
                continue;
            }

            foreach ($feedUserIds as $userId) {

                //操作人自己不需要生成待办
                if ($userId == $opUserId) {
                    continue;
                }

                $feeds[] = [
                    'client_id' => $clientId,
                    'user_id' => $userId,
                    'object_type' => TodoConstant::OBJECT_TYPE_COMPANY,
                    'feed_type' => $feedType,
                    'object_id' => $companyId,
                    'data' => json_encode($data)
                ];
            }
        }


        $feedsArr = array_chunk($feeds, 5000);
        foreach ($feedsArr as $feed) {
            QueueService::dispatch(new TipsPushTodoJob([
                'feed_type_id' => '*******',
                'action' => 'push',
                'feed_type' => 'new_unique_feed',
                'client_id' => $clientId,
                'operator_user_id' => $opUserId,
                'feeds' => $feed,
                'push_feed_type' => $feedType,
                'object_type' => TodoConstant::OBJECT_TYPE_COMPANY,
            ]));
        }


//        $nodeQueryEnd = microtime(true);
//        $nodeQueryCost = round(($nodeQueryEnd-$nodeQueryStart) * 1000,2);
//        \LogUtil::info('上报队列：'.$nodeQueryCost.'ms');
    }


	/**
	 * fileId的数字转换
	 *
	 * @param $list
	 * @return mixed
	 */
	public static function convertFileIds($list) {

		foreach ($list as $key => &$image) {

			if (false !== stripos($image, "E")) {

				$num = explode("e", strtolower($image));

				$value = bcmul($num[0] ?? 0, bcpow(10, $num[1] ?? 0));

				if ($value == 0) {

					unset($list[$key]);

					continue;
				}

				$image = $value;
			}
		}

		return $list;
	}

    public static function batchGetCompanyCountData($clientId, array $userIds)
    {
        $result = [];
       $companyQuotaManager = new CompanyQuotaManager($clientId);
       foreach ($userIds as $userId) {
           $result[$userId] = $companyQuotaManager->getCompanyCountData($userId);
       }
       return $result;
    }

    public static function getFtAccountList($fileId, $userId){

	    $file = new \AliyunUpload();

	    $file->loadByFileId($fileId, $userId);

	    $data = XlsUtil::getCsvData($file->getFileUrl());

	    $result = [];

	    foreach ($data as $datum) {

//			兼容
		    if (!isset($datum[3]) || !isset($datum[23])) {

			    continue;
		    }

		    $email = EmailUtil::findEmail(trim(strtolower($datum[3])));

		    if (!$email) {

			    continue;
		    }

//		    X
		    $op_id = ltrim(trim(strtolower($datum[23]), " \t\n\r\0\x0B\""), '0');

		    $result[] = $op_id;
	    }

	    return array_unique($result);
    }

    /**
     * @param $clientId
     * @param $companyId
     * @param array $userIds
     * 更新customer的user_id 用于把company->user_id 同步到customer
     */
    public static function setCustomerUserId($clientId, $companyId, array $userIds)
    {
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $userIds = PgsqlUtil::formatArray($userIds);
        $updateSql = "update tbl_customer set user_id='{$userIds}' where client_id=:clientId and company_id=:companyId
         and is_archive=:archiveFlag";
        return $db->createCommand($updateSql)->execute([
            ':clientId' => $clientId,
            ':companyId' => $companyId,
            ':archiveFlag' => 1,
        ]);
    }

    public static function getFieldOperatorConfig() {
        $result = [
            'field_type_operator_map' => [],
            'operators'               => [],
        ];

        foreach (self::getSearchFieldTypeOperatorMap() as $fieldType => $operators) {
            $result['field_type_operator_map'][] = [
                'field_type' => $fieldType,
                'operators'  => $operators,
            ];
        }

        $result['operators'] = self::getOperatorsList();

        foreach ($result as &$list) {
            $list = array_values($list);
        }

        return $result;
    }

    public static function getSearchFieldTypeOperatorMap()
    {
        return [
            CustomFieldService::FIELD_TYPE_TEXT => [
	            WorkflowConstant::FILTER_OPERATOR_MATCH,
	            WorkflowConstant::FILTER_OPERATOR_PREFIX,
                WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
            ],

            CustomFieldService::FIELD_TYPE_TEXTAREA => [
	            WorkflowConstant::FILTER_OPERATOR_MATCH,
	            WorkflowConstant::FILTER_OPERATOR_PREFIX,
                WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
            ],

            CustomFieldService::FIELD_TYPE_SELECT => [
                WorkflowConstant::FILTER_OPERATOR_IN,
                WorkflowConstant::FILTER_OPERATOR_NOT_IN,
                WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
            ],

            CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT => [
                WorkflowConstant::FILTER_OPERATOR_IN,
                WorkflowConstant::FILTER_OPERATOR_NOT_IN,
                WorkflowConstant::FILTER_OPERATOR_EQUAL,
                WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL,
                WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
            ],

            CustomFieldService::FIELD_TYPE_DATE => [
                WorkflowConstant::FILTER_OPERATOR_EQUAL,
                WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL,
                WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
                WorkflowConstant::FILTER_OPERATOR_EARLIER,
                WorkflowConstant::FILTER_OPERATOR_LATER,
            ],

            CustomFieldService::FIELD_TYPE_DATETIME => [
                WorkflowConstant::FILTER_OPERATOR_EQUAL,
                WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL,
                WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
                WorkflowConstant::FILTER_OPERATOR_EARLIER,
                WorkflowConstant::FILTER_OPERATOR_LATER,
            ],

            CustomFieldService::FIELD_TYPE_NUMBER => [
	            WorkflowConstant::FILTER_OPERATOR_RANGE,
	            WorkflowConstant::FILTER_OPERATOR_EQUAL,
                WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL,
                WorkflowConstant::FILTER_OPERATOR_GREATER,
                WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL,
                WorkflowConstant::FILTER_OPERATOR_LESS,
                WorkflowConstant::FILTER_OPERATOR_LESS_OR_EQUAL,
                WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
            ],

            CustomFieldService::FIELD_TYPE_BOOLEAN => [
                WorkflowConstant::FILTER_OPERATOR_EQUAL,
            ],

            CustomFieldService::FIELD_TYPE_FORMULA => [
                WorkflowConstant::FILTER_OPERATOR_EQUAL,
                WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL,
                WorkflowConstant::FILTER_OPERATOR_GREATER,
                WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL,
                WorkflowConstant::FILTER_OPERATOR_LESS,
                WorkflowConstant::FILTER_OPERATOR_LESS_OR_EQUAL,
                WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
                WorkflowConstant::FILTER_OPERATOR_RANGE,
            ],

            CustomFieldService::FIELD_TYPE_CALCULATE => [
                WorkflowConstant::FILTER_OPERATOR_EQUAL,
                WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL,
                WorkflowConstant::FILTER_OPERATOR_GREATER,
                WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL,
                WorkflowConstant::FILTER_OPERATOR_LESS,
                WorkflowConstant::FILTER_OPERATOR_LESS_OR_EQUAL,
                WorkflowConstant::FILTER_OPERATOR_IS_NULL,
                WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
                WorkflowConstant::FILTER_OPERATOR_RANGE,
            ],
        ];
    }

    public static function getOperatorsList()
    {
        $operatorList = [
            WorkflowConstant::FILTER_OPERATOR_EQUAL,
            WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL,
            WorkflowConstant::FILTER_OPERATOR_GREATER,
            WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL,
            WorkflowConstant::FILTER_OPERATOR_LESS,
            WorkflowConstant::FILTER_OPERATOR_LESS_OR_EQUAL,
            WorkflowConstant::FILTER_OPERATOR_CONTAINS,
            WorkflowConstant::FILTER_OPERATOR_NOT_CONTAINS,
            WorkflowConstant::FILTER_OPERATOR_IN,
            WorkflowConstant::FILTER_OPERATOR_NOT_IN,
            WorkflowConstant::FILTER_OPERATOR_IS_NULL,
            WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
            WorkflowConstant::FILTER_OPERATOR_EARLIER,
            WorkflowConstant::FILTER_OPERATOR_LATER,
            WorkflowConstant::FILTER_OPERATOR_UPDATE,
            WorkflowConstant::FILTER_OPERATOR_MATCH,
            WorkflowConstant::FILTER_OPERATOR_PREFIX,
            WorkflowConstant::FILTER_OPERATOR_RANGE,
        ];

        $operators = [];
        foreach ($operatorList as $item) {
            $operators[] = [
                'key'  => $item,
                'name' => \Yii::t('workflow', 'filter_operator.' . $item),
            ];
        }

        return $operators;
    }

    public static function getExportMapList($userId, $clientId)
    {
        //如果已经存在映射关系，先加载目前的映射
        $setting = new UserSetting($clientId, $userId, UserSetting::CUSTOMER_EXPORT_MAP);
        $value = $setting->getValue();
        if (!empty($value)) {
            $customerExportMap = json_decode($value,true);
        } else {
            //返回默认客户导出字段
            $customerExportMap = \CustomerOptionService::customerDefaultExportMap();
        }
        return $customerExportMap;
    }

    public static function getImportSheetHead($fileId, $userId)
    {
        $file = new \AliyunUpload();

        $res = $file->loadByFileId($fileId, $userId);
        if (!$res) {
            LogUtil::info('loadByFileId error, user_id:'.$userId.' file_id:'.$fileId);
            throw new \ProcessException('找不到文件,user_id:' . $userId . ' file_id:' . $fileId);
        }

        $path = \UploadService::UPLOAD_FILE . $file->getFileKey();

        if (file_exists($path)) {
            $data = XlsUtil::getExcelData($path);
        } else {
            LogUtil::info('file not exists, need to download  user_id:'.$userId.' file_id:'.$fileId.'importSteetHead');
            $file_url = $file->getFileUrl();
            $http = new \HTTPClient();
            $data = $http->get($file_url);

            if (!$data) {
                LogUtil::info('getImportSheetHead 找不到文件 user_id:'.$userId.' file_id:'.$fileId);
                throw new \ProcessException('找不到文件,user_id:' . $userId . ' file_id:' . $fileId);
            }

            $fileName = basename($file_url);
            $tempFile = sys_get_temp_dir() . DIRECTORY_SEPARATOR . $fileName;
            file_put_contents($tempFile, $data);

            $data = XlsUtil::getExcelData($tempFile);
        }
        $first_row = array_shift($data);
        $second_row = !empty($data) ? array_shift($data) : [];
        return self::getImportSheetHeadByRow($first_row, $second_row);
    }

    public static function getImportSheetHeadByRow($first_row, $second_row = [])
    {
        //判断是否需要处理
        $need_handle_flag = false;
        //需要处理的相关key
        $need_replace_keys = [];
        foreach ($first_row as $key => $item) {
            if (empty(($item)) && !empty($second_row[$key])) {
                $need_handle_flag = true;
                array_push($need_replace_keys, $key, $key - 1);
            }
        }

        //判断是否是多行表头
        $second_row_flag = false;
        if ($need_handle_flag) {
            foreach ($second_row as $key => $item) {
                if (in_array($key, $need_replace_keys) && !empty(($item))) {
                    $first_row[$key] = $item;
                    $second_row_flag = true;
                }
            }
        }
        //处理表头 处理空列和浮点数表头
        $sheetHead = [];
        foreach ($first_row as $k => $v) {
            if (empty($v)) {
                continue;
            }
            if (is_numeric($v)) {
                $v = intval($v);
            }
            $sheetHead[$k] = $v;
        }
        $sheetNum = $second_row_flag ? 2 : 1;
        return [$sheetHead, $sheetNum];
        //return ['sheetHead' => $sheetHead,'sheetNum' => $second_row_flag ? 2 : 1];
    }

    public static function getBatchOperatorFieldList($clientId, $userId)
    {
        $fieldQuery = new FieldList($clientId);
        $fieldQuery->setType(Constants::TYPE_COMPANY);
        $fieldQuery->setIsList(0);
        $fieldQuery->setFields(['id', 'group_id', 'type', 'field_type', 'name', 'ext_info', 'base', 'order', 'disable_flag']);
        $fieldQuery->setDisableFlag(0);
        $fieldQuery->setPrivilegeInfo($userId, PrivilegeConstants::FUNCTIONAL_CUSTOMER);
        $fieldQuery->setScene(PrivilegeFieldV2::SCENE_OF_UPDATE);
        $fieldQuery->setExcludeId([
            'name',
            'serial_id',
            'cus_tag',
            'next_follow_up_time',
        ]);
        $fieldQuery->setFieldType([
            CustomFieldService::FIELD_TYPE_SELECT,
            CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT,
            CustomFieldService::FIELD_TYPE_DATE,
            CustomFieldService::FIELD_TYPE_DATETIME,
            CustomFieldService::FIELD_TYPE_NUMBER,
        ]);
        $fieldQuery->setOrderBy(FieldList::ORDER_PC);
        $list = array_values(array_filter($fieldQuery->find(), function($item) {
            if ($item['field_type'] == CustomFieldService::FIELD_TYPE_NUMBER) {
                return $item['id'] == 'star';
            } else {
                return true;
            }
        }));

        usort($list, function($item1, $item2) {
            if ($item1['group_id'] == $item2['group_id']) {
                return $item1['order'] == $item2['order'] ? 0 : ($item1['order'] > $item2['order'] ? 0 : 1);
            } else {
                return $item1['group_id'] <=> $item2['group_id'];
            }
        });

        return $list;
    }

    /**
     * 通过邮箱获取客户联系人信息
     *
     * @param $clientId
     * @param $email
     * @return array|\CDbDataReader|mixed
     */
    public static function getCustomersByEmails($clientId, $email)
    {
        if (empty($email)) {
            return [];
        }

        $customerList = new CustomerList($clientId);
        $customerList->setAlias('a');
        $findFieldsArray = ['a.email', 'a.customer_id', 'a.company_id', 'b.user_id', 'b.pool_id', 'b.scope_user_ids'];
        $customerList->setJoin((new CompanyFilter($clientId, 'b')), BaseList::JOIN_TYPE_LEFT, ['company_id', 'company_id']);
        $findFields = implode(',', $findFieldsArray);
        $customerList->setFields($findFields);
        $customerList->setEmail($email);
        $customerList->setCheckPoolDuplicateSwitchFlag(true);
        $customerList->setOrderBy(['b.order_time','b.company_id']);
        $customerList->setOrder('desc');
        return $customerList->find();

    }


	/**
	 *
	 * @param $clientId
	 */
	public function getMaxCustomerOrderRank($clientId, $companyId) {

		if (!$companyId) {

			return 1;
		}

		$customerList = new CustomerList($clientId);

		$customerList->setCompanyId($companyId);

		$customerList->setFields('max(order_rank) as order_rank');

		$customerList->setOrder('');

		$customerList->setOrderBy('');

		return ($customerList->find())[0]['order_rank'] ?? 1;
	}

    public static function getProductGroupCompanyCount($userId, $clientId, $product_group_id)
    {
        if (empty($userId) || empty($clientId) || empty($product_group_id)) {
            return 0;
        }
        $product_group_children_ids = \common\library\group\Helper::getChildrenIds($clientId, \Constants::TYPE_PRODUCT, $product_group_id);
        $product_group_ids = array_merge([$product_group_id],$product_group_children_ids ?? []);

        $list = new CompanyList($userId);
        $list->setProductGroupIds($product_group_ids);
        $list->setSkipPrivilege(true);
        return $list->count();
    }

    /**
     * 更新latest_whatsapp_time
     * @param $clientId
     * @param $companyId
     * @param null $checkRefer
     * @param null $time
     * @param false $preventPrevious
     * @return false|int
     * @throws \CDbException
     * @throws \ProcessException
     */
    public static function updateLatestWhatsappTime($clientId, $companyId, $sendTime, $receiveTime)
    {
        $now = date('Y-m-d H:i:s');
        $latestWhatsappTime = max($sendTime, $receiveTime);
        $set = [];
        $params = [];
        if ($latestWhatsappTime && $latestWhatsappTime <= $now) {
            $set['latest_whatsapp_time'] = "latest_whatsapp_time=(CASE
                     WHEN latest_whatsapp_time > :latest_whatsapp_time THEN latest_whatsapp_time
                     ELSE :latest_whatsapp_time
                END)";
            $params[':latest_whatsapp_time'] = $latestWhatsappTime;
        }
        if ($receiveTime && $receiveTime <= $now) {
            $set['latest_whatsapp_receive_time'] = "latest_whatsapp_receive_time=(CASE
                     WHEN latest_whatsapp_receive_time > :latest_whatsapp_receive_time THEN latest_whatsapp_receive_time
                     ELSE :latest_whatsapp_receive_time
                END)";
            $params[':latest_whatsapp_receive_time'] = $receiveTime;
        }

        if ($sendTime && $sendTime <= $now) {
            $set['latest_whatsapp_send_time'] = "latest_whatsapp_send_time=(CASE
                     WHEN latest_whatsapp_send_time > :latest_whatsapp_send_time THEN latest_whatsapp_send_time
                     ELSE :latest_whatsapp_send_time
                END)";
            $params[':latest_whatsapp_send_time'] = $sendTime;
        }

        if (empty($set)) {
            return 0;
        }

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $setSql = implode(',', $set);

        $sql = "update tbl_company set {$setSql} where company_id = :company_id and client_id=:client_id";
        $params[':company_id'] = $companyId;
        $params[':client_id'] = $clientId;

        $res = $db->createCommand($sql)->execute($params);


        if ($res) {
            (new SwarmService($clientId))->refreshByRefer($companyId, [
                'latest_whatsapp_time',
                'latest_whatsapp_receive_time',
                'latest_whatsapp_send_time',
            ]);
        }

        return $res;
    }

    /**
     * 调用这个方法需要运行绩效 但是由于syncMessage接口调用这个方法太频繁 导致db死锁,因此在这个方法中不会运行绩效
     * 在调用这个方法时 请在外层调用一次 runPerformance
     * @param $clientId
     * @param $companyId
     * @param $sync
     * @return int
     */
    public static function resetLatestWhatsappTime($clientId, $companyId, $sync = false)
    {
        $res = 0;
        $companyIds = array_unique(array_filter((array)$companyId));
        if (empty($companyIds)) {
            return $res;
        }
        $messageListObj = new UserCustomerContactMessageList($clientId);
        $messageListObj->renderCompanyLastMessage($companyIds, \common\library\sns\Constants::SNS_CLIENT_WHATSAPP, CustomerContactHelper::SEND_TYPE_BY_ALL);
        $messageList = $messageListObj->find();

        $messageMap = [];
        foreach ($messageList as $message) {
            $messageMap[$message['company_id']][$message['send_type']] = $message['send_time'];
        }
        $updateSql = [];
        foreach ($companyIds as $companyId) {
            $sendWhatsappTime = $messageMap[$companyId][CustomerContactHelper::SEND_TYPE_BY_USER] ?? '1970-01-01 00:00:00';
            $receiveWhatsappTime = $messageMap[$companyId][CustomerContactHelper::SEND_TYPE_BY_CUSTOMER] ?? '1970-01-01 00:00:00';
            $whatsappTime = max($sendWhatsappTime, $receiveWhatsappTime);
            $updateSql[] = "update tbl_company set latest_whatsapp_time= '{$whatsappTime}', latest_whatsapp_receive_time='{$receiveWhatsappTime}', latest_whatsapp_send_time = '{$sendWhatsappTime}'  where company_id = {$companyId} and client_id={$clientId}";
        }

        if ($updateSql) {
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $sql = implode(';', $updateSql);
            $res = $db->createCommand($sql)->execute();

            (new SwarmService($clientId))->refreshByRefer($companyIds, [
                'latest_whatsapp_time',
                'latest_whatsapp_receive_time',
                'latest_whatsapp_send_time',
            ], !$sync);
        }

        return $res;
    }


	public static function refreshCompanyTmTime($clientId, $companyId, $storeId) {
        if (empty($companyId)) {
            return;
        }

		$customerList = new CustomerList($clientId);
		$customerList->setCompanyId($companyId);
		$customerList->setFields(['customer_id']);
		$customerList = $customerList->find() ?? [];

		if (empty($customerList)) {

			return;
		}

		$customerRelationList = new AlibabaCustomerRelationList($clientId);
		$customerRelationList->setStoreId($storeId);
		$customerRelationList->setCustomerId(array_column($customerList, 'customer_id'));
		$customerRelationList->setFields(['buyer_account_id']);
		$customerRelationList = $customerRelationList->find();

		if (empty($customerRelationList)) {

			return;
		}

		self::updateLatestReceiveAliTradeTime($clientId, $companyId, array_column($customerRelationList, 'buyer_account_id'));
	}

    public static function distributeMailTask($clientId, $opUserId, $type, $params, $async = true)
    {
        $task = new \DistributeMailTask();
        $task->client_id = $clientId;
        $task->user_id = $opUserId;
        $task->type = $type;
        $task->status = \DistributeMailTask::UNDO;
        $task->variable = json_encode($params);

        if ($type == \DistributeMailTask::TYPE_PUBLIC_MAIL) {
            $task->refer_id = $params['source_user_mail_id']??0;
        }

        $task->save();
        $taskId = $task->task_id;

        $job = new \common\library\queue_v2\job\DistributeMailJob($clientId, $opUserId, $taskId, $type);
        if ($async && php_sapi_name() != "cli" && \Yii::app()->params['env'] != 'exp') {
            QueueService::dispatch($job);
        } else {
            try {
                $job->handle();
            } catch (\Throwable $e) {
                \LogUtil::exception($e, compact('params'));
                \common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());
            }
        }
    }


	/**
	 * @param $clientId
	 * @param $companyId
	 * @return array
	 * @throws \CDbException
	 * @throws \CException
	 * @throws \ProcessException
	 */
	public static function getGrowthLevel($clientId, $companyId) {

		$db = \PgActiveRecord::getDbByClientId($clientId);

		$sql = 'SELECT client_id, company_id, customer_id, growth_level
				FROM tbl_customer
				WHERE client_id = '.$clientId.'
				  AND is_archive = 1
				  AND company_id = '.$companyId.'
				  AND growth_level > 0';

		$result = $db->createCommand($sql)->queryAll();

		$growthLevel = [];

		array_walk($result, function ($item) use(&$growthLevel) {

			!in_array($item['growth_level'], $growthLevel) && $growthLevel[] = $item['growth_level'];
		});

		return $growthLevel;
	}

	/**
	 * 更新客户表阿里买家身份
	 *
	 * @param      $clientId
	 * @param      $companyId
	 * @param bool $swarmRefresh
	 * @param bool $async
	 * @return void
	 * @throws \CDbException
	 * @throws \CException
	 * @throws \ProcessException
	 */
	public static function updateCompanyGrowthLevel($clientId, $companyId, $swarmRefresh = true, $async = true) {

		$db = \PgActiveRecord::getDbByClientId($clientId);

		$growthLevel = self::getGrowthLevel($clientId, $companyId);

		$sql = 'UPDATE tbl_company
				SET growth_level = \'' . PgsqlUtil::formatArray($growthLevel) . '\'
				WHERE client_id = ' . $clientId . '
				  AND is_archive = 1
				  AND company_id = ' . $companyId;

        $result = $db->createCommand($sql)->execute();

        $swarmRefresh && $result && (new SwarmService($clientId))->refreshByRefer($companyId, ['growth_level'], $async);
    }

    public static function addFundFieldsGroupByItemSettings($clientId)
    {
        $filter = new FundFilter($clientId);
        $filter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $batchFund = $filter->find();
        $batchFund->getFormatter()->displayFields(['item_id', 'item_name']);
        $batchFund->getFormatter()->displayTypeInfo(true);
        $fundList = $batchFund->getAttributes();
        $fundFieldList=[];
        $suffix = \Yii::t('field','订单附加费用');
        foreach ($fundList as $key => $fundField) {
            if (!in_array(\Constants::TYPE_ORDER, $fundField['types_arr'] ?? [])) {
                continue;
            }
            $fundField['id'] = $fundField['fund_id'];
            $fundField['name'] = $fundField['name'] . '(CNY)';
            $fundField['type'] = \Constants::TYPE_ORDER_PROFIT;
            $fundField['field_type'] = Constant::FIELD_TYPE_TEXT;
            $fundField['base'] = 1;
            $fundField['require'] = 1;
            $fundField['ext_info'] = [];
            $fundField['disable_flag'] = 0;
            $fundField['is_editable'] = 0;
            $fundField['relation_field'] = 0;
            $fundField['relation_field_type'] = '';
            $fundField['group_id'] = CustomFieldService::ORDER_PROFIT_GROUP_ORDER_FUND;
            $fundField['is_list'] = 0;
            $fundField['suffix'] = $fundField['group_name'] = $suffix;
//            $fundField['full_name'] = $fundField['name'] . ' ['.$fundField['group_name'].']';
            unset($fundField['fund_id']);
            unset($fundField['types_arr']);
            unset($fundField['types_name']);
            $fundFieldList[] = $fundField;
        }
        unset($fundField);
        $group = [
            'can_add_custom_field' => true,
            'id' => CustomFieldService::ORDER_PROFIT_GROUP_ORDER_FUND,
            'is_hide' => false,
            'list' => [],
            'name' => $suffix,
            'fields' => $fundFieldList
        ];
        return $group;
    }

    public static function addCostInvoiceFieldsGroupByItemSettings($clientId)
    {
        $filter = new CostInvoiceFilter($clientId);
        $filter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $filter->disable_flag = 0;
        $batchCostInvoice = $filter->find();
        $batchCostInvoice->getFormatter()->displayFields(['item_id', 'item_name']);
        $costInvoiceFieldsList = $batchCostInvoice->getAttributes();
        $suffix = \Yii::t('field','成本费用');
        foreach ($costInvoiceFieldsList as $key => &$costInvoiceField) {
            $costInvoiceField['id'] = $costInvoiceField['cost_invoice_item_id'];
            $costInvoiceField['type'] = \Constants::TYPE_ORDER_PROFIT;
            $costInvoiceField['name'] = $costInvoiceField['name']."(CNY)";
            $costInvoiceField['field_type'] = Constant::FIELD_TYPE_TEXT;
            $costInvoiceField['base'] = 1;
            $costInvoiceField['require'] = 1;
            $costInvoiceField['ext_info'] = [];
            $costInvoiceField['disable_flag'] = 0;
            $costInvoiceField['is_editable'] = 0;
            $costInvoiceField['relation_field'] = '';
            $costInvoiceField['relation_field_type'] = 0;
            $costInvoiceField['group_id'] = CustomFieldService::ORDER_PROFIT_GROUP_ORDER_COST_INVOICE_FUND;
            $costInvoiceField['is_list'] = 0;
            $costInvoiceField['suffix'] = $costInvoiceField['group_name'] = $suffix;
//            $costInvoiceField['full_name'] = $costInvoiceField['name'] . ' ['.$costInvoiceField['group_name'].']';
            unset($costInvoiceField['cost_invoice_item_id']);
        }
        unset($costInvoiceField);
        $group = [
            'can_add_custom_field' => true,
            'id' => CustomFieldService::ORDER_PROFIT_GROUP_ORDER_COST_INVOICE_FUND,
            'is_hide' => false,
            'list' => [],
            'name' => $suffix,
            'fields' => $costInvoiceFieldsList
        ];
        return $group;
    }

    public static function updateLatestWhatsappBusinessTime($clientId, $companyId, $sendTime, $receiveTime)
    {
        $now = date('Y-m-d H:i:s');
        $set = [];
        $params = [];
        if ($receiveTime && $receiveTime <= $now) {
            $set['latest_whatsapp_business_receive_time'] = "latest_whatsapp_business_receive_time=(CASE
                     WHEN latest_whatsapp_business_receive_time > :latest_whatsapp_business_receive_time THEN latest_whatsapp_business_receive_time
                     ELSE :latest_whatsapp_business_receive_time
                END)";
            $params[':latest_whatsapp_business_receive_time'] = $receiveTime;
        }

        if ($sendTime && $sendTime <= $now) {
            $set['latest_whatsapp_business_send_time'] = "latest_whatsapp_business_send_time=(CASE
                     WHEN latest_whatsapp_business_send_time > :latest_whatsapp_business_send_time THEN latest_whatsapp_business_send_time
                     ELSE :latest_whatsapp_business_send_time
                END)";
            $params[':latest_whatsapp_business_send_time'] = $sendTime;
        }

        if (empty($set)) {
            return 0;
        }

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $setSql = implode(',', $set);

        $sql = "update tbl_company set {$setSql} where company_id = :company_id and client_id=:client_id";
        $params[':company_id'] = $companyId;
        $params[':client_id'] = $clientId;

        $res = $db->createCommand($sql)->execute($params);

        if ($res) {
            (new SwarmService($clientId))->refreshByRefer($companyId, [
                'latest_whatsapp_business_send_time',
                'latest_whatsapp_business_receive_time',
            ]);
        }

        return $res;
    }

    /**
     * @param $clientId
     * @param $companyId
     * @param $sync
     * @param $burialPoint
     * @return int
     *
     */
    public static function resetLatestWhatsappBusinessTime($clientId, $companyId, $sync = false, $burialPoint = true)
    {
        $res = 0;
        $companyIds = array_unique(array_filter((array)$companyId));
        if (empty($companyIds)) {
            return $res;
        }
        $messageListObj = new UserCustomerContactMessageList($clientId);
        $messageListObj->renderCompanyLastMessage($companyIds, \common\library\sns\Constants::SNS_CLIENT_WHATSAPP_BUSINESS, CustomerContactHelper::SEND_TYPE_BY_ALL);
        $messageList = $messageListObj->find();

        $messageMap = [];
        foreach ($messageList as $message) {
            $messageMap[$message['company_id']][$message['send_type']] = $message['send_time'];
        }
        $updateSql = [];
        foreach ($companyIds as $companyId) {
            $sendWhatsappBusinessTime = $messageMap[$companyId][CustomerContactHelper::SEND_TYPE_BY_USER] ?? '1970-01-01 00:00:00';
            $receiveWhatsappBusinessTime = $messageMap[$companyId][CustomerContactHelper::SEND_TYPE_BY_CUSTOMER] ?? '1970-01-01 00:00:00';
            $updateSql[] = "update tbl_company set latest_whatsapp_business_receive_time = '{$receiveWhatsappBusinessTime}', latest_whatsapp_business_send_time = '{$sendWhatsappBusinessTime}'  where company_id = {$companyId} and client_id={$clientId}";
        }

        if ($updateSql) {
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $sql = implode(';', $updateSql);
            $res = $db->createCommand($sql)->execute();

            if ($burialPoint) {

                (new SwarmService($clientId))->refreshByRefer($companyIds, [
                    'latest_whatsapp_business_receive_time',
                    'latest_whatsapp_business_send_time',
                ], !$sync);
            }

        }

        return $res;
    }


    public static function updateLatestFacebookTime($clientId, $companyId, $sendTime, $receiveTime)
    {
        $now = date('Y-m-d H:i:s');
        $set = [];
        $params = [];
        if ($receiveTime && $receiveTime <= $now) {
            $set['latest_facebook_receive_time'] = "latest_facebook_receive_time=(CASE
                     WHEN latest_facebook_receive_time > :latest_facebook_receive_time THEN latest_facebook_receive_time
                     ELSE :latest_facebook_receive_time
                END)";
            $params[':latest_facebook_receive_time'] = $receiveTime;
        }

        if ($sendTime && $sendTime <= $now) {
            $set['latest_facebook_send_time'] = "latest_facebook_send_time=(CASE
                     WHEN latest_facebook_send_time > :latest_facebook_send_time THEN latest_facebook_send_time
                     ELSE :latest_facebook_send_time
                END)";
            $params[':latest_facebook_send_time'] = $sendTime;
        }

        if (empty($set)) {
            return 0;
        }

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $setSql = implode(',', $set);

        $sql = "update tbl_company set {$setSql} where company_id = :company_id and client_id=:client_id";
        $params[':company_id'] = $companyId;
        $params[':client_id'] = $clientId;

        $res = $db->createCommand($sql)->execute($params);

        if ($res) {
            (new SwarmService($clientId))->refreshByRefer($companyId, [
                'latest_facebook_send_time',
                'latest_facebook_receive_time',
            ]);
        }

        return $res;
    }

    /**
     * @param $clientId
     * @param $companyId
     * @param $sync
     * @param $burialPoint
     * @return int
     */
    public static function resetLatestFaceBookTime($clientId, $companyId, $sync = false, $burialPoint = true)
    {
        $res = 0;
        $companyIds = array_unique(array_filter((array)$companyId));
        if (empty($companyIds)) {
            return $res;
        }
        $messageListObj = new UserCustomerContactMessageList($clientId);
        $messageListObj->renderCompanyLastMessage($companyIds, \common\library\sns\Constants::SNS_CLIENT_FACEBOOK_PAGE, CustomerContactHelper::SEND_TYPE_BY_ALL);
        $messageList = $messageListObj->find();

        $messageMap = [];
        foreach ($messageList as $message) {
            $messageMap[$message['company_id']][$message['send_type']] = $message['send_time'];
        }
        $updateSql = [];
        foreach ($companyIds as $companyId) {
            $sendFacebookTime = $messageMap[$companyId][CustomerContactHelper::SEND_TYPE_BY_USER] ?? '1970-01-01 00:00:00';
            $receiveFacebookTime = $messageMap[$companyId][CustomerContactHelper::SEND_TYPE_BY_CUSTOMER] ?? '1970-01-01 00:00:00';
            $updateSql[] = "update tbl_company set latest_facebook_receive_time = '{$receiveFacebookTime}', latest_facebook_send_time = '{$sendFacebookTime}'  where company_id = {$companyId} and client_id={$clientId}";
        }

        if ($updateSql) {
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $sql = implode(';', $updateSql);
            $res = $db->createCommand($sql)->execute();

            if($burialPoint) {

                (new SwarmService($clientId))->refreshByRefer($companyIds, [
                    'latest_facebook_receive_time',
                    'latest_facebook_send_time',
                ], !$sync);
            }

        }

        return $res;
    }


    //刷新tbl_company表的首次成交订单金额
    public static function RefreshTransactionOrderFirstAmount($clientId, array $list = [], $recount = false, $burialPoint = true){

        $db = \PgActiveRecord::getDbByClientId($clientId);

        $companyIds = array_column($list, 'company_id');

        if(empty($companyIds)){
            return 0;
        }

        $companyIdsSql = implode(',', $companyIds);

        $companyData = [];
        if(!$recount){
            $companyDataSql = "select company_id, transaction_order_first_amount from tbl_company where client_id = {$clientId} and company_id in ({$companyIdsSql}) and transaction_order_first_amount != 0";
            $companyData = $db->createCommand($companyDataSql)->queryAll();
            $companyData = array_column($companyData, 'transaction_order_first_amount', 'company_id');
        }



        $count = 0;
        foreach (array_chunk($list, 100) as $items) {

            $ids = [];
            $updateSql = "update tbl_company set transaction_order_first_amount = case ";

            foreach ($items as &$item) {


                if (!empty($companyData[$item['company_id']]) && $companyData[$item['company_id']] == $item['first_amount']) {
                    unset($item);
                    continue;
                }

                array_push($ids, $item['company_id']);


                $sql = " when company_id = {$item['company_id']} then {$item['first_amount']} ";
                $updateSql .= $sql;

            }

            if(empty($ids)){
                continue;
            }

            $ids = implode(',', $ids);

            $updateSql .= " end where company_id in ({$ids}) and client_id = {$clientId}";


            $count = $db->createCommand($updateSql)->execute() + $count;

        }


        \LogUtil::info("process for client:{$clientId}, count: $count");

        if($burialPoint) {
            (new SwarmService($clientId))->refreshByRefer($companyIds, [
                'transaction_order_first_amount'
            ]);
        }
        return $count;

    }

    //格式化座机信息
    public static function formatterByTel($value)
    {
        $str = '';
        if (empty($value)) return $str;

        $tel_area_code = $value['tel_area_code'] ?? '';
        if ($tel_area_code) {
            $str .= "+{$tel_area_code} ";
        }
        $tel = $value['tel'] ?? '';
        if ($tel) {
            $str .= "- {$tel}";
        }
        return $str;
    }

    // 客户关联商机、取消关联
    public static function batchUpdateOpportunityFlag($clientId, $companyIds, $flag = 1)
    {
        $companyIds = array_filter($companyIds);
        if (empty($companyIds) || !in_array($flag, [0,1])) {
            return false;
        }
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $updateSql = "update tbl_company set opportunity_flag = {$flag} where company_id in (". implode(',', $companyIds) .") and client_id = {$clientId}";
        $res =  $db->createCommand($updateSql)->execute();
    
        try {
            \common\library\server\es_search\SearchQueueService::pushCompanyQueue(\User::getLoginUser() ? \User::getLoginUser()->getUserId() : 0, $clientId, (array)$companyIds, \Constants::SEARCH_INDEX_TYPE_UPDATE_NEW_COMPANY, true);
        } catch (\Throwable $e) {
            \LogUtil::exception($e, ['error_hint' => 'company update push index']);
        }
        
        return $res;
    }

    public static function batchCreateCompanyByEmployeeIds($clientId, $userId, $employeeIds): int
    {
        try {
            \common\library\CommandRunner::run('company', 'batchCreateCompanyByEmployeeIds', [
                'clientId' => $clientId,
                'userId' => $userId,
                'employeeIds' => implode(",", $employeeIds),
            ]);
        } catch (\Exception $exception) {
            \LogUtil::info('batchCreateCompanyByEmployeeIds error:'.$exception->getMessage());
            \LogUtil::info('batchCreateCompanyByEmployeeIds error:'.$exception->getTraceAsString());
            throw new \RuntimeException("批量转化客户失败");
        }
        return count($employeeIds);
    }

    // 根据邮箱去重
    public static function getUpdateCustomers($clientId, $userId, $company, $quickUpdate = 1, $addEmails = []) {
        if ($quickUpdate) {
            if (empty($company->company_hash_id)) {
                throw new \RuntimeException(\Yii::t("ai", "Did not get a list of company contacts"));
            }
            $okkiLeadsFlag = \common\library\privilege_v3\PrivilegeService::getInstance($clientId, $userId)
                ->hasFunctional(\common\library\privilege_v3\PrivilegeConstants::FUNCTIONAL_OKKI_LEADS_BASE);
            $contacts = new \common\library\discovery\api\ContactsList($clientId, $userId);
            $contacts->setCompanyHashId($company->company_hash_id);
            $contacts->setPage(1);
			//一键填充最多支持100
			$contacts->setSize(100);
            $contacts->setSort('desc');
            $contacts->setIncludeNotEmail(true);
            $contacts->setDefaultSortMode('v2');
            $contacts->setIsNewVersion($okkiLeadsFlag);
            $contactList = $contacts->find();
            $contactList = $contactList['emails'] ?? [];
            $addEmails = array_unique(array_column($contactList, 'value'));
        } else {
            $contactList = RecommendService::getContactListByEmails($addEmails);
        }

        if (empty($contactList)) {
            // 无新联系人
            throw new \RuntimeException(\Yii::t("customer", "Selected contacts have no email addresses or no valid contact"));
        }

        $clientId = $company->getClientId();
        // 获取建档客户联系人信息
        $customerList = new CustomerList($clientId);
        $customerList->setCompanyId($company->company_id);
        $customerList->setFilterEmptyEmail(true); // 有邮箱联系人
        $customerList->setFields("email");
        $customerList = array_column($customerList->find(), 'email');

        // 根据邮箱去重
        $contactList = array_filter($contactList, function ($contact) use ($customerList) {
            return !in_array($contact['value'], $customerList);
        });
        if (empty($contactList)) {
            throw new \RuntimeException(\Yii::t('customer', "Selected contacts have duplicate emails with current customer"));
        }

        /**
         * @var Customer[]
        */
        $updateCustomers = [];
        foreach ($contactList as $contact) {
            $contactName = $contact['first_name'] . ' ' . $contact['last_name'];
            $contactEmail =  $contact['value'] ?? '';

            if (!$contactEmail)
                continue;

            if (!\EmailUtil::isEmail($contactEmail))
                continue;

            [$preName, $contactEmail] = \EmailUtil::getPreFixAndEmail($contactEmail);
            $contactEmail = strtolower($contactEmail);

            if (isset($contact['position'])) {
                $post = $contact['position'];
            } elseif (isset($contact['type']) && $contact['type'] == 'personal') {
                $post = 'Staff';
            } else {
                $post = 'Department';
            }

            // 区号
            $telList = [];
            $customerPhoneNum = $contact['phone_number'] ?? '';
            if ($customerPhoneNum) {
                $customerPhoneData = TelUtil::formatTelData($customerPhoneNum);
                $telList = [[$customerPhoneData['tel_area_code'], $customerPhoneData['tel']]];
            }

            $customer = new Customer($clientId);
            $customer->name = $contactName != ' ' ? $contactName : $preName;
            $customer->email = $contactEmail;
            $customer->post = $post;
            $customer->tel_list = $telList;

            $updateCustomers[] = $customer;
        }
        if (empty($updateCustomers)) {
            throw new \RuntimeException(\Yii::t("customer", "Selected contacts have no email addresses or no valid contact"));
        }

        if (empty($customerList)) {
            $updateCustomers[0]->main_customer_flag = 1;
        }

        return $updateCustomers;
    }

    public static function refreshExcludeSettingsByScene($excludeSettings, $scene = '')
    {
        if (empty($scene)) {
            return $excludeSettings;
        }

        $customExclusions = [
            self::CUSTOMER_EXPORT => [
                Constants::TYPE_COMPANY => [
                    'company' => [],
                    'customer' => [
                        'main_customer_flag'
                    ],
                ]
            ],
            // 添加其他场景的自定义排除设置
        ];

        if (isset($customExclusions[$scene])) {
            $excludeSettings = array_replace($excludeSettings, $customExclusions[$scene]);
        }

        return $excludeSettings;
    }

    public static function fillinContactByTel($clientId, \User $user, $snsId)
    {
        $customerList = new CustomerList($clientId);
        $customerList->setTel($snsId);
        $customerList->getFormatter()->snsListSetting();
        $customers = $customerList->find();
        if (empty($customers)) {
            return;
        }
        if ($user->isEmpty()) {
            \LogUtil::info("cannot find sender");
            return;
        }

        $updateList = [];
        foreach ($customers as $customerInfo) {
            if (!isset($customerInfo['contact']) || !isset($customerInfo['customer_id'])) {
                continue;
            }
            $contacts = $customerInfo['contact'];
            $whatsappContacts = array_filter($contacts, function ($v) {
                return $v['type'] == 'whatsapp';
            });
            if (!empty($whatsappContacts)) {
                continue;
            }

            $company = new Company($clientId, $customerInfo['company_id'] ?? 0);
            try {
                if (!$company->isExist()) {
                    continue;
                }

                if ($company->isPublic()) {
                    \common\library\privilege_v3\Helper::checkPermission($clientId, $user->getUserId(), \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_EDIT);
                } else {
                    \common\library\privilege_v3\Helper::checkPermission($clientId, $user->getUserId(), \common\library\privilege_v3\PrivilegeConstants::PRIVILEGE_CRM_COMPANY_EDIT);
                }


                if (!$company->canEdit($user)) {
                    continue;
                }

                //if (!$company->isEditable($user)) {
                //    continue;
                //}
            } catch (\Exception $exception) {
                \LogUtil::info("user_id {$user->getUserId()} has no privilege, customer_id: " . $customerInfo['customer_id'] ?? 0);
                \LogUtil::info($exception->getMessage());
            }

            $customer = new Customer($clientId, $customerInfo['customer_id'] ?? 0);
            if ($customer->isNew()) {
                \LogUtil::info("cannot edit new customer");
                continue;
            }
            $contact = is_array($customer->contact) ? $customer->contact : json_decode($customer->contact, true);
            $contact = array_merge($contact, [['type' => 'whatsapp', 'value' => $snsId]]);
            $customer->contact = $contact;
            $customer->save();
        }
    }


    public static function getUneditableEmailByCompany(int $clientId, int $companyId, array $customerIds = []) {

        $customerIds = array_unique(array_unique(array_filter($customerIds)));

        if (empty($customerIds)) {

            $list = new CustomerList($clientId);

            $list->setCompanyId($companyId);

            $list->setFilterEmptyEmail(true);

            $list->setFields(['customer_id']);

            $list = $list->find();

            if (empty($list)) {

                return [];
            }

            $customerIds = array_column($list, 'customer_id');
        }



        $pg = \PgActiveRecord::getDbByClientId($clientId);

        $sql = 'SELECT DISTINCT UNNEST(customer_id)
                FROM tbl_dynamic_trail
                WHERE enable_flag = 1
                  AND client_id = ' . $clientId . '
                  AND company_id = ' . $companyId . '
                  AND customer_id && ARRAY [' . implode(',', $customerIds) . ']::BIGINT[]
                  AND type BETWEEN 200 AND 400
                LIMIT ' . count($customerIds);

        return $pg->createCommand($sql)->queryColumn();
    }

    /**
     * 获取最近动态的显示值
     * @param $trailInfo
     * @return mixed|string
     */
    public static function getLastTrailValue($trailInfo)
    {
        if (empty($trailInfo)) return '';

        $CALL_PAY_TYPES_LIST = [
            1101 => \Yii::t("trail", "internetCall"), // 打了一个网络电话给   Made an internet call to
            1102 => \Yii::t("trail", "businessCall"), // 打了一个商务电话给   Made a business call to
            1103 => \Yii::t("trail", "viaBusiness"), // 通过商务电话到      By business phone
        ];

        $field_map = [
            [
                'module' => 1,
                'value' => function ($params) {
                    $node_type = $params['node_type'] ?? 0;
                    $data = $params['data'] ?? [];
                    $subject = $data['subject'] ?? '';
                    $plain_content = $data['plain_content'] ?? '';
                    return $node_type === 102 ? self::formatLastTrailSubject($subject) : $plain_content;
                },
            ],
            [
                'module' => 2,
                'value' => function ($params) {
                    $data = $params['data'] ?? [];
                    $subject = $data['subject'] ?? '';
                    return self::formatLastTrailSubject($subject);
                },
            ],
            [
                'module' => 3,
                'value' => function ($params) {
                    $node_type = $params['node_type'] ?? 0;
                    $data = $params['data'] ?? [];
                    $subject = $data['subject'] ?? '';
                    $status = $data['status'] ?? '';
                    if ($node_type === 301) {
                        return $status >= 3 ? self::formatLastTrailSubject($subject) : \Yii::t("trail", "正在发送...");
                    }
                    return self::formatLastTrailSubject($subject);
                },
            ],
            [
                'module' => 4,
                'value' => function ($params) {
                    $data = $params['data'] ?? [];
                    return $data['quotation_name'] ?? '';
                },
            ],
            [
                'module' => 5,
                'value' => function ($params) {
                    $data = $params['data'] ?? [];
                    return $data['pi_name'] ?? '';
                },
            ],
            [
                'module' => 8,
                'value' => function ($params) {
                    $data = $params['data'] ?? [];
                    return $data['order_name'] ?? '';
                },
            ],
            [
                'module' => 9,
                'value' => function ($params) {
                    $data = $params['data'] ?? [];
                    return $data['phone'] ?? '';
                },
            ],
            [
                'module' => 10,
                'value' => function ($params) {
                    $data = $params['data'] ?? [];
                    if (!$data) {
                        return '';
                    }
                    return str_replace('<br />', '\n', $data['content']);
                },
            ],
            [
                'module' => 11,
                'value' => function ($params) use ($CALL_PAY_TYPES_LIST) {
                    $create_user_info = $params['create_user_info'] ?? [];
                    $node_type = $params['node_type'] ?? 0;
                    $customer_info = $params['customer_info'] ?? [];
                    $data = $params['data'] ?? [];

                    $create_user_name = $create_user_info['name'] ?? '';
                    $CALL_PAY_TYPE = $CALL_PAY_TYPES_LIST[$node_type] ?? '';
                    $client_contact = \Yii::t("trail", "clientContact");//客户的联系人
                    $customer_name = $customer_info['name'] ?? '';
                    $target_tel = $data['target_tel'] ?? '';
                    return trim("{$create_user_name}{$CALL_PAY_TYPE}{$client_contact}{$customer_name}{$target_tel}");
                },
            ],
            [
                'module' => 12,
                'value' => function ($params) {
                    //这是js的源代码 用下面的PHP代码替换
                    /*
                     * const content = (data || {}).content || ''

                    return xss(content, { whiteList: {} })
                    .split('#')
                    .reduce((str, item, index) => {
                        if (index === 1 || index === 3) {
                            item = ` <b>${item}</b> `
                        }
                        return `${str}${item}`
                    }, '')*/
                    $data = $params['data'] ?? [];
                    $content = preg_replace( '/<(.*)s(.*)c(.*)r(.*)i(.*)p(.*)t/i', '', $data['content'] ?? '' );
                    $arr = explode('#', $content);
                    $result = '';
                    $index = 0; // 初始化索引

                    foreach ($arr as $item) {
                        if ($index === 1 || $index === 3) {
                            $item = " <b>{$item}</b> ";
                        }
                        $result .= $item;
                        $index++; // 更新索引
                    }
                    return $result;
                },
            ],
            [
                'module' => 13,
                'value' => function ($params) {
                    $node_type_name = $params['node_type_name'] ?? '';
                    $text = \Yii::t("trail", "yu");//于
                    $re = '/' . $text . '/';
                    return preg_replace('/ \d{4}-\d{1,2}-\d{1,2}( \d{1,2}:\d{1,2}:\d{1,2})?/', '', preg_replace($re, '', $node_type_name));
                },
            ],
            [
                'module' => 15,
                'value' => function ($params) {
                    $create_user_info = $params['create_user_info'] ?? [];
                    $node_type = $params['node_type'] ?? 0;
                    $customer_info = $params['customer_info'] ?? [];
                    $data = $params['data'] ?? [];
                    $lead_customer_info = $params['lead_customer_info'] ?? [];

                    $customerInfo = $customer_info[0] ?? null;
                    $leadCustomerInfo = $lead_customer_info[0] ?? null;
                    $payload = [];
                    switch ($node_type) {
                        case 1504:
                            if (!empty($customerInfo) && (isset($customerInfo['name']) || isset($customerInfo['email']) || (isset($customerInfo['tel_list'][0]) && is_array($customerInfo['tel_list'][0])))) {
                                $customerName = $customerInfo['name'] ?? $customerInfo['email'] ?? implode('-', $customerInfo['tel_list'][0] ?? []);
                            }

                            if (!empty($leadCustomerInfo) && (isset($leadCustomerInfo['name']) || isset($leadCustomerInfo['email']) || (isset($leadCustomerInfo['tel_list'][0]) && is_array($leadCustomerInfo['tel_list'][0])))) {
                                $leadCustomerName = $leadCustomerInfo['name'] ?? $leadCustomerInfo['email'] ?? implode('-', $leadCustomerInfo['tel_list'][0] ?? []);
                            } else {
                                $leadCustomerName = '未知联系人';
                            }

                            $payload = [
                                'createUser' => $create_user_info['nickname'] ?? $create_user_info['name'],
                                'snsUser' => $customerName ?? $leadCustomerName,
                            ];
                            break;
                        case 1505:
                            $payload = [
                                'createUser' => $create_user_info['nickname'] ?? $create_user_info['name'] ?? '',
                                'snsUser' => '客户的WhatsApp群组',
                            ];
                            break;
                        default:
                            $payload = [
                                'createUser' => $create_user_info['nickname'] ?? $create_user_info['name'] ?? '',
                                'snsUser'=> rawurldecode($data['sns_nickname'] ?? ''),
                            ];
                            break;
                    }

                    return '【'.$payload['createUser'].'】和 【' .$payload['snsUser'].'】的聊天';
                },
            ],
        ];

        $result = '';

        $node_type = $trailInfo['node_type'] ?? 0;
        $module = intval($trailInfo['module'] ?? 0);
        foreach ($field_map as $item) {
            if ($item['module'] === $module) {
                $params = [
                    'data' => $trailInfo['data'] ?? [],
                    'create_user_info' => $trailInfo['create_user_info'] ?? [],
                    'node_type' => $node_type,
                    'customer_info' => $trailInfo['customer_info'] ?? [],
                    'lead_customer_info' => $trailInfo['lead_customer_info'] ?? [],
                    'node_type_name' => $trailInfo['node_type_name'] ?? '',
                ];
                $result = $item['value']($params);
                if (empty($result)) {
                    $result = $params['node_type_name'];
                }
                break;
            }
        }

        return $result;
    }

    public static function formatLastTrailSubject($subject)
    {
        return trim($subject) ?: \Yii::t("customer", "noSubject"); // 无主题
    }


    /**
     * @param array $list
     * @param $scene
     * @return array
     */
    public static function formatFormFieldValue(array $list, $scene): array
    {
        if (!empty($list["company"])) {
            $companyFields = array();
            foreach ($list["company"] as $data) {
                foreach ($data["fields"] as $field) {
                    $companyFields[$field['id']] = $field["value"];
                }
            }
            $list["company"] = $companyFields;
        }

        if (!empty($list["opportunity"])) {
            $opportunityFields = array();
            foreach ($list["opportunity"] as $data) {
                foreach ($data["fields"] as $field) {
                    $opportunityFields[$field['id']] = $field["value"];
                }
            }
            $list["opportunity"] = $opportunityFields;
        }

        if ($scene == "ignoreCustomer") {
            $list["customers"] = null;
        } elseif (!empty($list["customers"])) {
            $customersFields = array();
            if (in_array($scene, ['info', 'app_form'])) {
                foreach ($list["customers"] as &$data) {
                    foreach ($data["fields"] as $fields) {
                        $customersFields[$fields['id']] = $fields["value"];
                    }
                    $data = $data + $customersFields;
                    unset($data["fields"]);
                    $data = array_replace($data, $customersFields);
                }

            } else {
                foreach ($list["customers"] as &$data) {
                    foreach ($data["fields"] as $fields) {
                        foreach ($fields as $field) {
                            $customersFields[$field['id']] = $field["value"];
                        }
                    }
                    $data = $data + $customersFields;
                    unset($data["fields"]);
                    $data = array_replace($data, $customersFields);
                }
            }
        }
        unset($list['system']);

        return $list;
    }


    public static function formatFormFieldConfig(array $list): array
    {
        if (isset($list["company"])){
            $companyFields = array();
            foreach ($list["company"] as $data){
                foreach ($data["fields"] as $field){
                    $companyFields[] = $field;
                }
            }
            $list["company"] = $companyFields;
        }

        if (isset($list["opportunity"])){
            $opportunityFields = array();
            foreach ($list["opportunity"] as $data){
                foreach ($data["fields"] as $field){
                    $opportunityFields[] = $field;
                }
            }
            $list["opportunity"] = $opportunityFields;
        }

        if (isset($list["customers"])){
            $customersFields = array();
            foreach ($list["customers"] as $data){
                foreach ($data["fields"] as $fields){
                    foreach ($fields as $field){
                        $customersFields[] = $field;
                    }
                }
            }
            $list["customers"] = $customersFields;
        }


        return $list;
    }

    /**
     * @throws \CException
     */
    public static function getUserSettingByUserIds($clientId, $targetUserIds, $key){

        $db = \ProjectActiveRecord::getDbByClientId($clientId);

        $where = '';
        if (is_array($targetUserIds)) {
            $where .= " and user_id in (" . implode(',', $targetUserIds) . ") ";
        } else {
            $where .= " and user_id = {$targetUserIds} ";
        }

        $sql = "select user_id, value from tbl_user_setting where client_id = {$clientId} and `key` = '{$key}' {$where}";
        $data = $db->createCommand($sql)->queryAll();
        $targetUserIds = is_array($targetUserIds) ? $targetUserIds : [$targetUserIds];
        $result = array_fill_keys($targetUserIds, null);
        foreach ($data as $item) {
            $result[$item['user_id']] = $item['value'];
        }

        return $result;
    }

    /**
     * @throws \ProcessException
     */
    public static function getDuplicateFailCount($clientId, $userId, $companyIds, $poolId)
    {
        $companyIds = is_array($companyIds) ? $companyIds : [$companyIds];
        $failCompanyIds = [];

        $list = new CompanyList($userId);
        $list->showAll(true);
        $companyIdsArr = array_chunk($companyIds, 200);
        foreach ($companyIdsArr as $ids) {
            $list->setCompanyIds($ids);
            $companyListData = $list->find();
            foreach ($companyListData as $item) {

                try {

                    (new FieldUniqueValidator($clientId, Constants::TYPE_COMPANY, $item['company_id'] ?? 0))
                        ->setPreventOnly(true)
                        ->setAttributes($item)
                        ->setPoolId($poolId)
                        ->validate();

                    $customerList = new CustomerList($clientId);
                    $customerList->setCompanyId([$item['company_id']]);
                    $customerListData = $customerList->find();

					foreach ($customerListData as $customerItem) {

						(new FieldUniqueValidator($clientId, \Constants::TYPE_CUSTOMER, $item['company_id'] ?? 0))
							->setPreventOnly(true)
							->setAttributes($customerItem)
							->setPoolId($poolId)
							->validate();
					}

                } catch (\Exception $e) {
                    $failCompanyIds[] = $item['company_id'];
                }
            }
        }
        return $failCompanyIds;
    }


    /**
     * 更新INS相关消息时间字段
     * @param $clientId
     * @param $companyId
     * @param $sendTime
     * @param $receiveTime
     * @return int
     * @throws \ProcessException
     */
    public static function updateLatestInsTime($clientId, $companyId, $sendTime, $receiveTime)
    {
        $now = date('Y-m-d H:i:s');
        $latestInsTime = max($sendTime, $receiveTime);
        $set = [];
        $params = [];
        if ($latestInsTime && $latestInsTime <= $now) {
            $set['latest_ins_time'] = "latest_ins_time=(CASE
                     WHEN latest_ins_time > :latest_ins_time THEN latest_ins_time
                     ELSE :latest_ins_time
                END)";
            $params[':latest_ins_time'] = $latestInsTime;
        }
        if ($receiveTime && $receiveTime <= $now) {
            $set['latest_ins_receive_time'] = "latest_ins_receive_time=(CASE
                     WHEN latest_ins_receive_time > :latest_ins_receive_time THEN latest_ins_receive_time
                     ELSE :latest_ins_receive_time
                END)";
            $params[':latest_ins_receive_time'] = $receiveTime;
        }

        if ($sendTime && $sendTime <= $now) {
            $set['latest_ins_send_time'] = "latest_ins_send_time=(CASE
                     WHEN latest_ins_send_time > :latest_ins_send_time THEN latest_ins_send_time
                     ELSE :latest_ins_send_time
                END)";
            $params[':latest_ins_send_time'] = $sendTime;
        }

        if (empty($set)) {
            return 0;
        }

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $setSql = implode(',', $set);

        $sql = "update tbl_company set {$setSql} where company_id = :company_id and client_id=:client_id";
        $params[':company_id'] = $companyId;
        $params[':client_id'] = $clientId;

        $res = $db->createCommand($sql)->execute($params);


        if ($res) {
            (new SwarmService($clientId))->refreshByRefer($companyId, [
                'latest_ins_time',
                'latest_ins_receive_time',
                'latest_ins_send_time',
            ]);
        }

        return $res;
    }

    /**
     * 重置INS消息相关时间
     * @param $clientId
     * @param $companyId
     * @param $sync
     * @param $burialPoint
     * @return int
     * @throws \ProcessException
     */
    public static function resetLatestInsTime($clientId, $companyId, $sync = false, $burialPoint = true)
    {
        $res = 0;
        $companyIds = array_unique(array_filter((array)$companyId));
        if (empty($companyIds)) {
            return $res;
        }
        $messageListObj = new UserCustomerContactMessageList($clientId);
        $messageListObj->renderCompanyLastMessage($companyIds, \common\library\sns\Constants::SNS_CLIENT_INSTAGRAM, CustomerContactHelper::SEND_TYPE_BY_ALL);
        $messageList = $messageListObj->find();

        $messageMap = [];
        foreach ($messageList as $message) {
            $messageMap[$message['company_id']][$message['send_type']] = $message['send_time'];
        }
        $updateSql = [];
        foreach ($companyIds as $companyId) {
            $sendInsTime = $messageMap[$companyId][CustomerContactHelper::SEND_TYPE_BY_USER] ?? '1970-01-01 00:00:00';
            $receiveInsTime = $messageMap[$companyId][CustomerContactHelper::SEND_TYPE_BY_CUSTOMER] ?? '1970-01-01 00:00:00';
            $updateSql[] = "update tbl_company set latest_ins_receive_time = '{$receiveInsTime}', latest_ins_send_time = '{$sendInsTime}'  where company_id = {$companyId} and client_id={$clientId}";
        }

        if ($updateSql) {
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $sql = implode(';', $updateSql);
            $res = $db->createCommand($sql)->execute();

            if($burialPoint) {

                (new SwarmService($clientId))->refreshByRefer($companyIds, [
                    'latest_ins_receive_time',
                    'latest_ins_send_time',
                ], !$sync);
            }

        }

        return $res;
    }

    /**
     * 更新企微相关消息时间字段
     * @param $clientId
     * @param $companyId
     * @param $sendTime
     * @param $receiveTime
     * @return int
     * @throws \ProcessException
     */
    public static function updateLatestWechatTime($clientId, $companyId, $sendTime, $receiveTime)
    {
        $now = date('Y-m-d H:i:s');
        $latestWechatTime = max($sendTime, $receiveTime);
        $set = [];
        $params = [];
        if ($latestWechatTime && $latestWechatTime <= $now) {
            $set['latest_wechat_time'] = "latest_wechat_time=(CASE
                 WHEN latest_wechat_time > :latest_wechat_time THEN latest_wechat_time
                 ELSE :latest_wechat_time
            END)";
            $params[':latest_wechat_time'] = $latestWechatTime;
        }
        if ($receiveTime && $receiveTime <= $now) {
            $set['latest_wechat_receive_time'] = "latest_wechat_receive_time=(CASE
                 WHEN latest_wechat_receive_time > :latest_wechat_receive_time THEN latest_wechat_receive_time
                 ELSE :latest_wechat_receive_time
            END)";
            $params[':latest_wechat_receive_time'] = $receiveTime;
        }

        if ($sendTime && $sendTime <= $now) {
            $set['latest_wechat_send_time'] = "latest_wechat_send_time=(CASE
                 WHEN latest_wechat_send_time > :latest_wechat_send_time THEN latest_wechat_send_time
                 ELSE :latest_wechat_send_time
            END)";
            $params[':latest_wechat_send_time'] = $sendTime;
        }

        if (empty($set)) {
            return 0;
        }

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $setSql = implode(',', $set);

        $sql = "update tbl_company set {$setSql} where company_id = :company_id and client_id=:client_id";
        $params[':company_id'] = $companyId;
        $params[':client_id'] = $clientId;

        $res = $db->createCommand($sql)->execute($params);


        if ($res) {
            (new SwarmService($clientId))->refreshByRefer($companyId, [
                'latest_wechat_time',
                'latest_wechat_receive_time',
                'latest_wechat_send_time',
            ]);
        }

        return $res;
    }

    /**
     * 重置企微消息相关时间
     * @param $clientId
     * @param $companyId
     * @param $sync
     * @param $burialPoint
     * @return int
     * @throws \ProcessException
     */
    public static function resetLatestWechatTime($clientId, $companyId, $sync = false, $burialPoint = true)
    {
        $res = 0;
        $companyIds = array_unique(array_filter((array)$companyId));
        if (empty($companyIds)) {
            return $res;
        }
        $messageListObj = new UserCustomerContactMessageList($clientId);
        $messageListObj->renderCompanyLastMessage($companyIds, \common\library\sns\Constants::SNS_CLIENT_WECOM, CustomerContactHelper::SEND_TYPE_BY_ALL);
        $messageList = $messageListObj->find();

        $messageMap = [];
        foreach ($messageList as $message) {
            $messageMap[$message['company_id']][$message['send_type']] = $message['send_time'];
        }
        $updateSql = [];
        foreach ($companyIds as $companyId) {
            $sendInsTime = $messageMap[$companyId][CustomerContactHelper::SEND_TYPE_BY_USER] ?? '1970-01-01 00:00:00';
            $receiveInsTime = $messageMap[$companyId][CustomerContactHelper::SEND_TYPE_BY_CUSTOMER] ?? '1970-01-01 00:00:00';
            $updateSql[] = "update tbl_company set latest_wechat_receive_time = '{$receiveInsTime}', latest_wechat_send_time = '{$sendInsTime}'  where company_id = {$companyId} and client_id={$clientId}";
        }

        if ($updateSql) {
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $sql = implode(';', $updateSql);
            $res = $db->createCommand($sql)->execute();

            if($burialPoint) {

                (new SwarmService($clientId))->refreshByRefer($companyIds, [
                    'latest_wechat_receive_time',
                    'latest_wechat_send_time',
                ], !$sync);
            }

        }

        return $res;
    }


	public static function duplicateRuleEffect($clientId, $userId, $type){
		$detectingKey = DuplicateConstants::MODULE_MAP[$type]['detecting'];

		$client = Client::getClient($clientId);

		$detectingType = $client->getSettingAttributes()[$detectingKey] ?? 0;
		$customerPoolSwitch = $client->getExtentAttribute(Client::EXTERNAL_KEY_CUSTOMER_POOL_SWITCH) ?? 0;

		\LogUtil::info(" client_id: {$clientId}, user_id: {$userId}, switch rule type detecting_type: {$detectingType}, customer_pool_switch: {$detectingType}");

		if (!empty($detectingType) && !empty($customerPoolSwitch)) {
			$client->setSettingAttributes([
				$detectingKey => DuplicateConstants::DUPLICATE_CONFLICT_UNDETECTED,
				Client::SETTING_KEY_CUSTOMER_DUPLICATE_CONFLICT_DETECTING_ONE_SUCCESS_FLAG => true
			]);

			$client->saveSettingAttributes();

			\common\library\account\Helper::setClientExternalValue($clientId, DuplicateFlagBuilder::DUPLICATE_EXTERNAL_MAP[$type], 1);
			Browser::push($clientId, DuplicateFlagBuilder::DUPLICATE_BROWSER_MAP[$type], 1);

			\common\library\CommandRunner::run(
				'fieldDuplicate',
				'refreshDuplicateFlagRuleChange',
				[
					'client_id' => $clientId,
					'user_id' => $userId,
					'type' => $type,
				],
			);
		}
	}

	public static function getPoolCustomerByIds($clientId, $userId,$customerIds){
		if(empty($customerIds))
		{
			return [];
		}

		$db = \PgActiveRecord::getDbByClientId($clientId);
		$customerIdStr = join(',',$customerIds);
		$sql = "SELECT c.company_id,c.customer_id,cy.ali_store_id,lower(c.email) as email,cy.ali_store_id FROM tbl_customer c
                    LEFT JOIN tbl_company cy
                    on c.company_id = cy.company_id
                    WHERE c.client_id={$clientId}
                    AND c.user_id = '{}'
                    AND c.customer_id in({$customerIdStr})
                    AND c.is_archive =1
                    ORDER  BY cy.update_time DESC ";

		$customerList = $db->createCommand($sql)->queryAll();
		return $customerList;
	}

    public static function getMaxSerialId($clientId)
    {
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $sql = "select max(cast(serial_id as decimal)) as max_number from tbl_company where client_id =:client_id and is_archive = 1 and serial_id ~ '^[0-9]+$';";
        $maxSerialId = $db->createCommand($sql)->queryScalar([':client_id' => $clientId]);
        return $maxSerialId ?: 0;
    }

    public static function hasRelatedAliStore($clientId, $companyId)
    {
            $companyRelationList = new \common\library\alibaba\customer\AlibabaCompanyRelationList($clientId);
            $companyRelationList->setCompanyId($companyId);
            $companyRelationList->setAlibabaCompanyIdNotEmpty();
            $companyRelationList->getFormatter()->setShowLastOwnerAccountInfo(true);
            $companyRelationList->getFormatter()->companyAssocStoreList();
            $alibabaRelationList = $companyRelationList->find();
            return !empty($alibabaRelationList);
    }

    public static function refreshCompanyPortrait($clientId, $companyId, $storeId, $alibabaCompanyId)
    {
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $sql = "update tbl_alibaba_customer_portrait set company_id = {$companyId} where client_id = {$clientId} and store_id = {$storeId} and alibaba_company_id = {$alibabaCompanyId} ";
        $rows = $db->createCommand($sql)->execute();

        \LogUtil::info("refreshCompanyPortrait",[
            'alibaba_company_id' => $alibabaCompanyId,
            'storeId' => $storeId,
            'companyId' => $companyId,
            'rows' => $rows
        ]);
        return true;
    }
}
