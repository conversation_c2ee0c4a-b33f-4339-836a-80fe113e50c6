<?php
/**
 * Created by PhpStorm.
 * User: beenzhang
 * Date: 2021/2/7
 * Time: 4:46 PM
 */

namespace common\library\customer;

use common\library\account\Client;
use common\library\ai\classify\ai_field_data\CompanyAIFieldData;
use common\library\alibaba\customer\CustomerSyncHelper;
use common\library\cash_collection\CashCollectionBatchOperator;
use common\library\CommandRunner;
use common\library\customer\field_unique\DuplicateFlagBuilder;
use common\library\customer_v3\company\orm\Company;
use common\library\customer_v3\customer\CustomerList;
use common\library\customer_v3\customer\orm\Customer;
use common\library\duplicate\DuplicateConstants;
use common\library\email_identity\sync\CustomerSync;
use common\library\google_ads\lead\SessionLeadHelper;
use common\library\history\customer\CustomerCompare;
use common\library\invoice\InvoiceService;
use common\library\notification\NotificationHelper;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\sns\customer\CustomerContactHelper;
use common\library\todo\TodoConstant;
use common\library\version\CompanyVersion;
use common\library\version\Constant;
use common\models\client\CompanyHistoryPg;

class CompanyMerge
{
    protected $userId;
    protected $clientId;
    protected $opUser;

    protected $mainCompanyId;
    /**
     * @var Company
     */
    protected $mainCompany;

    protected $mergedCompanyIds = [];
    /**
     * @var Company[]
     */
    protected $mergedCompanyMap = [];

    protected $companyData = [];
    protected $customerDataList = [];

    protected $saveUserIds = [];

    protected $debugLog = false;
	protected $changeUserPoolFlag = false;


	private $performanceMap = [];

	public function __construct($userId)
    {
        $this->userId = $userId;
        $this->opUser = \User::getUserObject($userId);
        $this->clientId = $this->opUser->getClientId();

        if (\Yii::app()->params['env'] == 'test')
        {
            $this->debugLog = true;
        }
    }

    public function setCompanyIds($mainCompanyId, array $mergedCompanyIds)
    {
        $this->mainCompanyId = $mainCompanyId;
        $this->mergedCompanyIds = $mergedCompanyIds;
    }

    public function setCompanyData($companyData)
    {
        $this->companyData = $companyData;
    }

    public function setCustomerDataList(array $customerList)
    {
        $this->customerDataList = $customerList;
    }

	public function setChangeUserPoolFlag($changeUserPoolFlag)
	{
		$this->changeUserPoolFlag = $changeUserPoolFlag;
	}

    protected function init($mainCompanyId, array $mergedCompanyIds)
    {
        if (empty($mainCompanyId) || empty($mergedCompanyIds))
            throw new \RuntimeException(\Yii::t('customer', 'The merged customer does not exist'));

        $this->mainCompany = new Company($this->clientId, $mainCompanyId);
        $this->mainCompany->setOperatorUserId($this->userId);

        if (!$this->mainCompany->isExist())
            throw new \RuntimeException(\Yii::t('customer', 'Merging customer does not exist'));

        if (!$this->mainCompany->canEdit($this->opUser))
            throw new \RuntimeException(\Yii::t('customer', 'Non-own data'));

        //if ($this->mainCompany->isPublic()) {
        //    \common\library\privilege_v3\Helper::checkPermission($this->clientId, $this->userId, PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_EDIT);
        //} else {
        //    //私海
        //    \common\library\privilege_v3\Helper::checkPermission($this->clientId, $this->userId, PrivilegeConstants::PRIVILEGE_CRM_COMPANY_EDIT);
        //}

        $this->saveUserIds = $this->mainCompany->user_id;
        foreach ($mergedCompanyIds as $mergedCompanyId)
        {
            $mergedCompany = new Company($this->clientId, $mergedCompanyId);
            $mergedCompany->setOperatorUserId($this->userId);

            if (!$mergedCompany->isExist())
                throw new \RuntimeException(\Yii::t('customer', 'The merged customer does not exist'));

            if (!$mergedCompany->canEdit($this->opUser))
                throw new \RuntimeException(\Yii::t('customer', 'Merged customers are Non-own customers'));

            $this->mergedCompanyMap[$mergedCompany->company_id] = $mergedCompany;
            $this->saveUserIds = array_merge($this->saveUserIds, $mergedCompany->user_id);
        }

        $this->saveUserIds = array_values(array_unique(array_filter($this->saveUserIds)));
    }

    protected function setCompanyValue($companyData)
    {
        if (empty($this->mergedCompanyIds) || empty($this->mainCompanyId))
            throw new \RuntimeException('参数错误');

        $company = $this->mainCompany;
        $company->name = $companyData['name'] ?? $company->name;
        $company->short_name = $companyData['short_name'] ?? $company->short_name;
        $company->group_id = $companyData['group_id'] ?? $company->group_id;
        $company->pool_id = $companyData['pool_id'] ?? $company->pool_id;
        $company->country = $companyData['country'] ?? $company->country;
        $company->category_ids = $companyData['category_ids'] ?? $company->category_ids;
        $company->address = $companyData['address'] ?? $company->address;
        $company->remark = $companyData['remark'] ?? $company->remark;
        $company->tel = $companyData['tel'] ?? $company->tel;
        $company->tel_area_code = $companyData['tel_area_code'] ?? $company->tel_area_code;
        $company->fax = $companyData['fax'] ?? $company->fax;
        $company->homepage = $companyData['homepage'] ?? $company->homepage;
        $company->biz_type = $companyData['biz_type'] ?? $company->biz_type;
        $company->star = $companyData['star'] ?? $company->star;
        $company->trail_status = $companyData['trail_status'] ?? $company->trail_status;
        $company->external_field_data = $companyData['external_field_data'] ?? $company->external_field_data;
        $company->province = $companyData['province'] ?? $company->province;
        $company->city = $companyData['city'] ?? $company->city;
        $company->scale_id = $companyData['scale_id'] ?? $company->scale_id;
        $company->image_list = $companyData['image_list'] ?? $company->image_list;
        $company->timezone = $companyData['timezone'] ?? $company->timezone;
        $company->annual_procurement = $companyData['annual_procurement'] ?? $company->annual_procurement;
        $company->intention_level = $companyData['intention_level'] ?? $company->intention_level;
        $company->next_follow_up_time = $companyData['next_follow_up_time'] ?? $company->next_follow_up_time;
        $company->product_group_ids = $companyData['product_group_ids'] ?? $company->product_group_ids;

		$mergedAlibabaUserIds = [];
		$mergedAlibabaLastOwners = [];
        //$mergedCompanyUserIds = [];
        $userDataMap = $company->user_data;
        foreach ($this->mergedCompanyMap as $mergedCompany)
        {
            //$mergedCompanyUserIds = array_merge($mergedCompanyUserIds, $mergedCompany->user_id);

			$mergedAlibabaUserIds = array_merge($mergedAlibabaUserIds, $mergedCompany->alibaba_user_id);
			$mergedAlibabaLastOwners = array_merge($mergedAlibabaLastOwners, $mergedCompany->alibaba_last_owner);

			$company->alibaba_recent_sync_time = max($company->alibaba_recent_sync_time, $mergedCompany->alibaba_recent_sync_time);
			$company->alibaba_first_sync_time = min($company->alibaba_first_sync_time, $mergedCompany->alibaba_first_sync_time);


			//设置绑定的company_hash_id, 优先留下$company的company_hash_id，如果$company没有则使用$mergedCompany
            //两者都无将会在beforeSave时match
            $company->company_hash_id = $company->company_hash_id ? $company->company_hash_id : $mergedCompany->company_hash_id;

            //比较一下主company 和合并的company 最近联系时间，取最新时间为合并后客户的最近联系时间
            if(strtotime($mergedCompany->order_time) > strtotime($company->order_time)){
                $company->order_time = $mergedCompany->order_time;
            }

	        $company->recent_follow_up_time = max($company->recent_follow_up_time, $mergedCompany->recent_follow_up_time);
	        $company->private_time = max($company->private_time, $mergedCompany->private_time);
            $company->private_user_time = max($company->private_user_time, $mergedCompany->private_user_time);
	        // $company->public_time = max($company->public_time, $mergedCompany->public_time); // NOTE: 最近移入公海时间直接使用主客户的，避免产生歧义 --bug=1108733 --user=anhoderai 【工单#20241205000019】进入公海次数显示异常 https://www.tapd.cn/21404721/s/2936949
	        $company->latest_write_follow_up_time = max($company->latest_write_follow_up_time, $mergedCompany->latest_write_follow_up_time);
	        $company->mail_time = max($company->mail_time, $mergedCompany->mail_time);
	        $company->send_mail_time = max($company->send_mail_time, $mergedCompany->send_mail_time);
	        $company->receive_mail_time = max($company->receive_mail_time, $mergedCompany->receive_mail_time);
	        $company->latest_receive_ali_tm_time = max($company->latest_receive_ali_tm_time, $mergedCompany->latest_receive_ali_tm_time);
	        $company->latest_receive_ali_trade_time = max($company->latest_receive_ali_trade_time, $mergedCompany->latest_receive_ali_trade_time);
	        $company->latest_send_ali_tm_time = max($company->latest_send_ali_tm_time, $mergedCompany->latest_send_ali_tm_time);
	        $company->deal_time = max($company->deal_time, $mergedCompany->deal_time);
	        $company->latest_transaction_order_time = max($company->latest_transaction_order_time, $mergedCompany->latest_transaction_order_time);
	        $company->latest_success_opportunity_time = max($company->latest_success_opportunity_time, $mergedCompany->latest_success_opportunity_time);
	        $company->edm_time = max($company->edm_time, $mergedCompany->edm_time);
	        $company->tips_latest_update_time = max($company->tips_latest_update_time, $mergedCompany->tips_latest_update_time);
	        $company->latest_whatsapp_time = max($company->latest_whatsapp_time, $mergedCompany->latest_whatsapp_time);


	        $company->next_follow_up_time = min(($company->next_follow_up_time > '1970-01-01 00:00:00') ? $company->next_follow_up_time : $mergedCompany->next_follow_up_time, ($mergedCompany->next_follow_up_time > '1970-01-01 00:00:00') ? $mergedCompany->next_follow_up_time : $company->next_follow_up_time);
	        $company->transaction_order_first_time = min(($company->transaction_order_first_time > '1970-01-01 00:00:00') ? $company->transaction_order_first_time : $mergedCompany->transaction_order_first_time, ($mergedCompany->transaction_order_first_time > '1970-01-01 00:00:00') ? $mergedCompany->transaction_order_first_time : $company->transaction_order_first_time);
	        $company->success_opportunity_first_time = min(($company->success_opportunity_first_time > '1970-01-01 00:00:00') ? $company->success_opportunity_first_time : $mergedCompany->success_opportunity_first_time, ($mergedCompany->success_opportunity_first_time > '1970-01-01 00:00:00') ? $mergedCompany->success_opportunity_first_time : $company->success_opportunity_first_time);


	        $company->performance_order_count = bcadd($company->performance_order_count, $mergedCompany->performance_order_count);
	        $company->transaction_order_amount = bcadd($company->transaction_order_amount, $mergedCompany->transaction_order_amount);
	        $company->success_opportunity_count = bcadd($company->success_opportunity_count, $mergedCompany->success_opportunity_count);
	        $company->ongoing_opportunity_count = bcadd($company->ongoing_opportunity_count, $mergedCompany->ongoing_opportunity_count);
	        $company->success_opportunity_amount_cny = bcadd($company->success_opportunity_amount_cny, $mergedCompany->success_opportunity_amount_cny);
	        $company->success_opportunity_amount_usd = bcadd($company->success_opportunity_amount_usd, $mergedCompany->success_opportunity_amount_usd);

            $company->origin_list = array_merge($company->origin_list ?? [], $mergedCompany->origin_list ?? []);

	        $company->client_tag_list = array_values(array_unique(array_merge(($company->client_tag_list ?? []), ($mergedCompany->client_tag_list ?? []))));

            foreach ($mergedCompany->tag as $userId => $tags)
            {
                if (array_key_exists($userId, $company->tag))
                {
                    $companyTag = array_values(array_unique(array_merge($tags, $company->tag[$userId])));
                    $company->setTags($userId, $companyTag);
                } else {
                    $company->setTags($userId, $tags);
                }
            }

            foreach ($mergedCompany->user_data as $userId => $userData)
            {
                if (!array_key_exists($userId, $userDataMap))
                {
                    $userDataMap[$userId] = $userData;
                }
            }

            $company->follow_product_list = array_values(array_unique(array_merge($company->follow_product_list, $mergedCompany->follow_product_list)));
            $company->follow_product_group_list = array_values(array_unique(array_merge($company->follow_product_group_list, $mergedCompany->follow_product_group_list)));
            $company->follow_product_category_list = array_values(array_unique(array_merge($company->follow_product_category_list, $mergedCompany->follow_product_category_list)));
            $company->ali_store_id = array_values(array_unique(array_merge($company->ali_store_id, $mergedCompany->ali_store_id)));

            // 有坑，备注一下：由"来源详情=网站"合并到"来源详情=店铺"会丢掉"来源详情=网站"的字段信息，因为没有维护main_lead_id字段，且新的需求为合并后的并集来展示，这个留到下期来实现
            $company->main_lead_id = $company->main_lead_id ?: $mergedCompany->main_lead_id;
        }

		$company->alibaba_last_owner = (array_values(array_unique(array_merge($company->alibaba_last_owner, $mergedAlibabaLastOwners))));
		$company->alibaba_user_id = (array_values(array_unique(array_merge($company->alibaba_user_id, $mergedAlibabaUserIds))));


		$company->addTag($this->userId, $company->client_tag_list);


        //当前client限制的单个客户最大跟进人数，合并后的客户跟进人按顺序取
        $perCompanyMaxUserNum = \common\library\account\Client::getClient($this->clientId)->getExtentAttribute(\common\library\account\Client::EXTERNAL_KEY_PER_COMPANY_MAX_USER_NUM);
        //$mergedCompanyUserIds = array_values(array_unique(array_filter(array_merge($this->mainCompany->user_id, $mergedCompanyUserIds))));
		$mergedCompanyUserIds = $this->saveUserIds ?? [];
        $mergedCompanyUserIds = (!empty($perCompanyMaxUserNum) && $perCompanyMaxUserNum > 0) ? array_slice($mergedCompanyUserIds, 0, $perCompanyMaxUserNum) : $mergedCompanyUserIds;

        $company->user_data = $userDataMap;
        $company->addUser($mergedCompanyUserIds);
        $company->setMergeCompanyIds($this->mergedCompanyIds);
        $company->setHistoryType(CompanyHistoryPg::TYPE_COMPANY_MERGE);
        $company->setSkipDuplicateCheck(true);
        $company->setCheckPool(false);
        $company->setAllowMainCustomerEmpty(true);

        $this->mainCompany = $company;
    }

    protected function setCustomerValue(array $customerList)
    {
        $saveConflictCustomerList = [];
        foreach ($customerList as $customerData)
        {
            $customer = new Customer($this->clientId, $customerData['customer_id']??0);
            $customer->setOperatorUserId($this->userId);
            if ($customer->isNew()){
                throw new \RuntimeException(\Yii::t('customer','Contact does not exist'));
            }
            $customer->name = $customerData['name'] ?? $customer->name;
            $customer->post_grade = $customerData['post_grade'] ?? $customer->post_grade;
            $customer->post = $customerData['post'] ?? $customer->post;
            $customer->tel_list = $customerData['tel_list'] ?? $customer->tel_list;
            $customer->birth = $customerData['birth'] ?? $customer->birth;
            $customer->gender = $customerData['gender'] ?? $customer->gender;
            $customer->remark = $customerData['remark'] ?? $customer->remark;
            $customer->contact = $customerData['contact'] ?? $customer->contact;
            $customer->external_field_data = $customerData['external_field_data'] ?? $customer->external_field_data;
            $customer->group_id = $customerData['group_id'] ?? $customer->group_id;
            $customer->image_list = $customerData['image_list'] ?? $customer->image_list;
			$customer->user_id = $this->saveUserIds;
            $customer->setSkipDuplicateCheck(true);
            $customer->setHistoryTypeMerge(true);

            $saveConflictCustomerList[$customer->email] = $customer;
        }
        return $saveConflictCustomerList;
    }

    public function merge()
    {
        $this->init($this->mainCompanyId,$this->mergedCompanyIds);

		if ($this->changeUserPoolFlag) {
			\common\library\customer\pool\Helper::assignByMainCompany($this->clientId, $this->opUser->getUserId(), $this->mainCompanyId, $this->saveUserIds);
		}

		$client = Client::getClient($this->clientId);
		$detectKey = \common\library\duplicate\DuplicateConstants::MODULE_MAP[\Constants::TYPE_COMPANY]['detecting'];
		$ruleTypeKey = DuplicateConstants::MODULE_MAP[\Constants::TYPE_COMPANY]['rule_type'];
		$duplicateRuleType = (($client->getSettingAttributes()[$detectKey] ?? 0) == \common\library\duplicate\DuplicateConstants::DUPLICATE_CONFLICT_DETECTING) ? \common\library\customer\field_unique\DuplicateFlagBuilder::DUPLICATE_ALL_RULE_TYPE : ($client->getSettingAttributes()[$ruleTypeKey] ?? 0);
		if (!$this->changeUserPoolFlag && $duplicateRuleType == DuplicateFlagBuilder::DUPLICATE_POOL_ID_RULE_TYPE) {
			$this->saveUserIds = \common\library\customer\pool\Helper::eliminateDifferentPoolUserId($this->clientId, $this->opUser->getUserId(), $this->mainCompanyId, $this->saveUserIds);
		}

	    \LogUtil::info("mainCompanyId: {$this->mainCompanyId}, mergedCompanyIds: ".json_encode($this->mergedCompanyIds));


	    $db = \PgActiveRecord::getDbByClientId($this->clientId);

//		客户联系人上限
	    $customerTotalSql = 'SELECT count(*)
				FROM tbl_customer
				WHERE client_id = ' . $this->clientId . '
				  AND company_id IN (' . implode(',', array_merge([$this->mainCompanyId], $this->mergedCompanyIds)) . ')
				  AND is_archive = 1';

	    $customerTotal = $db->createCommand($customerTotalSql)->queryScalar() - count($this->customerDataList);

	    if ($customerTotal > Company::CUSTOMER_COUNT_OVER_LIMIT) {

		    throw new \RuntimeException(\Yii::t('customer', 'The number of contact exceeds the limit of {count}', ['{count}' => Company::CUSTOMER_COUNT_OVER_LIMIT]), \ErrorCode::CODE_COMPANY_CONTACT_LIMIT);
	    }



        $this->setCompanyValue($this->companyData);
        $this->log('setCompanyValue:'.var_export($this->mainCompany->getAttributes(),true));

//        客户信息预先保存
        $this->mainCompany->save();
    
    
        //主客户合并前所有联系人
        $list = new CustomerList($this->clientId);
        $list->setFields('email,customer_id');
        $list->setCompanyId($this->mainCompanyId);
        $customerList = $list->find();
        $beforeMergeCustomerIds = array_column($customerList, 'customer_id');
        $this->log('$beforeMergeCustomerIds:'.var_export($beforeMergeCustomerIds,true));

        // 507 新邮箱 - 联系人邮箱失效检查
        $list = new CustomerList($this->clientId);
        $list->setFields('user_id, company_id, email, customer_id');
        $list->setCompanyId($this->mergedCompanyIds);
        $customerList = $list->find();
        $feedCustomers = [];
        foreach ($customerList as $customer) {
            if (empty($customer['email'])) {
                continue;
            }
            $customer['user_id'] = \common\library\util\PgsqlUtil::trimArray($customer['user_id']);
            $feedCustomers['user_id'] = array_merge($feedCustomers['user_id'] ?? [], $customer['user_id']);
            $feedCustomers['company_id'][] = $customer['company_id'];
            $feedCustomers['customer_id'][] = $customer['customer_id'];
            $feedCustomers['email'][] = $customer['email'];
        }
        if (!empty($feedCustomers['customer_id'] ?? [])) {
            (new \common\library\queue_v2\job\TipsPushTodoJob([
                'feed_type_id' => '*******',
                'action' => 'remove',
                'feed_type' => 'contacts_invalid',
                'company_id' => array_values(array_unique($feedCustomers['company_id'])),
                'customer_id' => $feedCustomers['customer_id'],
                'emails' => $feedCustomers['email'],
                'client_id' => $this->clientId,
                'user_id' => array_values(array_unique($feedCustomers['user_id'])),
                'operator_user_id' => $this->opUser->getUserId()
            ]))->handle();
        }

        //批量修改customer的companyId
        $fromCompanyIdString = implode(',',$this->mergedCompanyIds);
        $changeCustomerSql = "update tbl_customer set main_customer_flag=0, company_id=:new_company_id
where client_id=:client_id and company_id IN ($fromCompanyIdString) and is_archive=1";
        $changeCustomerParams = [':client_id' => $this->clientId, ':new_company_id' => $this->mainCompanyId];
        $db->createCommand($changeCustomerSql)->execute($changeCustomerParams);
        $this->log('$changeCustomerSql:'.$changeCustomerSql.' '.var_export($changeCustomerParams,true));

        $conflictEmailCustomerMap = $this->setCustomerValue($this->customerDataList);
        $this->log('$conflictEmailCustomerMap:'.var_export($conflictEmailCustomerMap, true));

        $list = new CustomerList($this->clientId);
        $list->setFields('*');
        $list->setCompanyId($this->mainCompanyId);
        $allCustomerList = $list->find();
        $this->log('$allCustomerList:'.var_export($allCustomerList, true));

        $historyCustomerList = [];
        foreach ($allCustomerList as $item)
        {
            //原本就在主要客户里面，忽略
            if (in_array($item['customer_id'], $beforeMergeCustomerIds))
                continue;

            //在合并联系人详情里面忽略（后续会有单独的操作历史逻辑）
            if (isset($conflictEmailCustomerMap[$item['email']]))
                continue;

            $historyCustomerList[] = $item;
        }
        $this->log('$historyCustomerList:'.var_export($historyCustomerList, true));

        //构建联系人关联数据迁移map
        $transferMap = [];
        $delCustomerIds = [];
        foreach ($allCustomerList as $item)
        {
            if (isset($conflictEmailCustomerMap[$item['email']])
                && ($conflictEmailCustomerMap[$item['email']]->customer_id !== $item['customer_id'])
            ) {
                $transferMap[$item['email']]['to_id'] = $conflictEmailCustomerMap[$item['email']]->customer_id;
                $transferMap[$item['email']]['from_id'][] = $item['customer_id'];
                $delCustomerIds[] = $item['customer_id'];
                //合并是取联系人最大的买家身份
                if($conflictEmailCustomerMap[$item['email']]->growth_level < $item['growth_level']){
                    $conflictEmailCustomerMap[$item['email']]->growth_level = $item['growth_level'];
                }
            }
        }
        $this->log('$transferMap:'.var_export($transferMap, true));
        $this->log('$delCustomerIds:'.var_export($delCustomerIds, true));

//        // 移除重复网站动态
//        \common\library\trail\Helper::deleteRepeatSiteDynamicOfCompany($this->clientId, $this->mainCompanyId, $this->mergedCompanyIds);
//
//        //迁移客户关联
//        foreach ($this->mergedCompanyIds as $mergedCompanyId) {
//            $this->log("transferCompanyRelation: from:{$mergedCompanyId}; to:{$this->mainCompanyId}");
//            $this->transferCompanyRelation($mergedCompanyId, $this->mainCompanyId);
//        }
//
//        //迁移联系人关联
//        foreach ($transferMap as $email => $item)
//        {
//            $toId = $item['to_id'];
//            foreach ($item['from_id'] as $fromId)
//            {
//                $this->log("transferCustomerRelation: companyId:{$this->mainCompanyId} from:{$fromId}; to:{$toId}");
//                $this->transferCustomerRelation($this->mainCompanyId, $fromId, $toId);
//            }
//        }


        if (!empty($conflictEmailCustomerMap))
        {
            $this->mainCompany->addCustomers($conflictEmailCustomerMap);
        }
        $this->mainCompany->save();
        // 这里加version是保证一定能加到.Company类加version的条件有些苛刻,统一加会导致大量version.相比之下这里加不会导致太多version
        $companyVersion = new CompanyVersion($this->clientId, $this->mainCompanyId);
        $companyVersion->setType(Constant::COMPANY_MODULE_EDIT);
        $companyVersion->add();

        //联系人操作历史
        //mergeCustomerHistory
        $customerFormatter = new CustomerFormatter($this->clientId);
        foreach ($historyCustomerList as $mergedCustomer)
        {
            $history = new CustomerCompare($this->clientId);
            $history->setType(CompanyHistoryPg::TYPE_MERGE_CUSTOMER);
            $history->setEditInfo(CompanyAIFieldData::FIELD_EDIT_TYPE_BY_USER);
            $history->setExtraData([
                'company_id' => $mergedCustomer['company_id'],
                'customer_id' => $mergedCustomer['customer_id'],
            ]);
            $mergedCustomerAttributes = $customerFormatter->strip($mergedCustomer);
            $history->setData($mergedCustomerAttributes, []);
            $history->build($this->userId);
        }

        //必须在删除mergedCompany之前构造，否则userId不正确
        $msgData = $this->buildMessage();
        $this->log("buildMessage: ".var_export($msgData,true));

        //删除重复邮箱联系人
        if (!empty($delCustomerIds)) {
            $customerIdsString = implode(',',$delCustomerIds);
            $db->createCommand("update tbl_customer set user_id='{}',is_archive=0,company_id=0 where client_id=:client_id
and company_id=:company_id and customer_id IN ($customerIdsString)")
                ->execute([':client_id' => $this->clientId, ':company_id' => $this->mainCompanyId]);

            // TODO tbl_email_relation.customer_id 的关系需要维护
            // TODO 但目前 customer_id 字段只支持单人, 实际业务支持多人, 待额外处理
        }
        //删除被合并公司
        $this->deleteMergeCompany();

        //上报待办
        $feed = new \common\library\todo\Feed(TodoConstant::OBJECT_TYPE_COMPANY);
        $feed->deleteFeedByObjectId($this->mergedCompanyIds);

        // 507 新合并的联系人邮箱检查是否失效
        foreach ($feedCustomers['customer_id'] ?? [] as $key => $item) {
            if (in_array($item, $delCustomerIds)) {
                unset($feedCustomers['customer_id'][$key]);
                unset($feedCustomers['email'][$key]);
            }
        }
        if (!empty($feedCustomers['customer_id'] ?? [])) {
            (new \common\library\queue_v2\job\TipsPushTodoJob([
                'feed_type_id' => '*******',
                'action' => 'push',
                'feed_type' => 'contacts_invalid',
                'company_id' => $this->mainCompanyId,
                'customer_id' => array_values($feedCustomers['customer_id']),
                'emails' => array_values($feedCustomers['email']),
                'client_id' => $this->clientId,
                'user_id' => $this->mainCompany->user_id,
                'operator_user_id' => $this->opUser->getUserId()
            ]))->handle();
        }

		$this->mainCompany->customer_count = $this->mainCompany->getCustomerCount();

        $this->mainCompany->save();

        Helper::setCustomerUserId($this->clientId, $this->mainCompanyId, $this->mainCompany->user_id);

        //更新邮箱身份
        (new CustomerSync($this->clientId))->setFindCompanyId($this->mainCompanyId)->sync();

        // AMES 推送客户合并消息到底座  进行订单合并
        \common\library\ames\Helper::pushCompanyMergeMessage($this->clientId, $this->mainCompany, $this->mergedCompanyMap);

        foreach ($msgData as $msgDatum) {
            NotificationHelper::customerMergeMsg($msgDatum['notify_user_list'], $this->userId, $msgDatum);
        }


//		最近动态重算
        \common\library\trail\Helper::resetCompanyLastTrailId($this->clientId, $this->mainCompanyId);

//		动态评论刷新
        \common\library\trail\comment\Helper::refreshCommentByTrail($this->clientId, [], [$this->mainCompanyId]);


        // 上面那段迁移数据的执行时间很长，扔到 crm-queue-customer 队列里面执行
        \common\library\CommandRunner::run(
            'customer',
            'companymergeaftertransferdata',
            [
                'clientId' => $this->clientId,
                'userId' => $this->userId,
                'mainCompanyId' => $this->mainCompanyId,
                'mergedCompanyIds' => '\''.json_encode($this->mergedCompanyIds).'\'',
                'customerTransferMap' => '\''.json_encode($transferMap).'\'',
                'groupId' => $this->mainCompany->group_id,
            ],
        );

        //TODO 异步执行邮件动态合并
        $log = '/tmp/mergeSameEmailCustomerTrail.log';
        CommandRunner::run(
            'customer',
            'mergeSameEmailCustomerTrail',
            [
                'clientId' => $this->clientId,
                'companyId' => $this->mainCompanyId,
            ],
            $log
        );
    }

    protected function transferCompanyRelation($fromId, $toId)
    {
        // time line quotation order user schedule company file statistics
        //time line
        \common\library\trail\Helper::batchChangeCompanyId($fromId, $toId, $this->userId);

        SessionLeadHelper::changeCompanyId($this->clientId, $fromId, $toId);

        // sns customer contact
        CustomerContactHelper::changeCompanyId($this->clientId, $fromId, $toId);

        // CIQ
        \CompanyBindCiq::deleteByCompanyId($fromId, $this->clientId);

        //quotation 报价单
        (new InvoiceService($this->clientId, \Constants::TYPE_QUOTATION))->batchChangeCompanyId($fromId, $toId, $this->userId, $this->mainCompany->group_id);

        //order 订单
        (new InvoiceService($this->clientId, \Constants::TYPE_ORDER))->batchChangeCompanyId($fromId, $toId, $this->userId, $this->mainCompany->group_id);

        //user schedule
        \common\library\schedule\Helper::batchChangeCompanyId($fromId, $toId, $this->userId);

        //companyFile
        \CompanyFileService::batchChangeCompanyId($fromId, $toId, $this->userId);

        //Opportunity
        \common\library\opportunity\Helper::batchChangeCompanyId($fromId, $toId, $this->userId);

//		cashCollection
	    (new CashCollectionBatchOperator($this->userId))->batchChangeCompanyId($fromId, $toId);

        //迁移阿里客户ID,买家ID、询盘ID、来源店铺
        \common\library\alibaba\trade\Helper::batchChangeCompanyId($this->clientId, $fromId, $toId);
        CustomerSyncHelper::batchChangeCompanyId($this->clientId, $fromId, $toId);
    }

    protected function transferCustomerRelation($companyId, $fromCustomerId, $toCustomerId)
    {
        //time line
        \common\library\trail\Helper::batchChangeCustomerId($companyId, $fromCustomerId, $toCustomerId, $this->userId);

        // sns customer contact
        CustomerContactHelper::changeCustomerId($this->clientId, $companyId, $fromCustomerId, $toCustomerId);

        //quotation 报价单
        (new InvoiceService($this->clientId, \Constants::TYPE_QUOTATION))->batchChangeCustomerId($companyId, $fromCustomerId, $toCustomerId, $this->userId);

        //order 订单
        (new InvoiceService($this->clientId, \Constants::TYPE_ORDER))->batchChangeCustomerId($companyId, $fromCustomerId, $toCustomerId, $this->userId);

        //companyFile
        \CompanyFileService::batchChangeCustomerId($companyId, $fromCustomerId, $toCustomerId, $this->userId);

        //Opportunity
        \common\library\opportunity\Helper::batchChangeCustomerId($companyId, $fromCustomerId, $toCustomerId, $this->userId);


        //迁移阿里客户联系人ID, 买家ID、询盘ID、来源店铺
        \common\library\alibaba\trade\Helper::batchChangeCustomerId($this->clientId, $companyId, $fromCustomerId, $toCustomerId);
        CustomerSyncHelper::batchChangeCustomerId($this->clientId, $fromCustomerId, $toCustomerId);
    }

    protected function buildMessage()
    {
        $msgData = [];
        foreach ($this->mergedCompanyMap as $mergedCompany)
        {
            $notifyUserIds = array_merge($this->mainCompany->user_id, $mergedCompany->user_id);
            $msgData[] = [
                'operator_id' => $this->opUser->getUserId(),
                'operator_name' => $this->opUser->getNickname(),
                'operator_email' => $this->opUser->getEmail(),

                'main_company_id' => $this->mainCompany->company_id,
                'main_company_name' => $this->mainCompany->name,
                'main_company_serial' => 'C' . $this->mainCompany->serial_id,

                'another_company_id' => $mergedCompany->company_id,
                'another_company_name' => $mergedCompany->name,
                'another_company_serial' => 'C' . $mergedCompany->serial_id,

                'notify_user_list' => array_values(array_unique($notifyUserIds))
            ];
        }

        return $msgData;
    }

    protected function deleteMergeCompany()
    {
        foreach ($this->mergedCompanyMap as $companyId => $mergedCompany)
        {
            $mergedCompany->is_archive = 0;
            $mergedCompany->last_owner = $mergedCompany->user_id[0] ?? 0;
            $mergedCompany->user_id = [];
            $mergedCompany->setOperatorUserId($this->userId);
            $mergedCompany->setSkipDuplicateCheck(true);
	        $mergedCompany->setCheckSubGroupSelectFlag(false);
            $mergedCompany->save();
        }

        // 删除被合并的客户的相关task
        $mergedCompanyIds = array_keys($this->mergedCompanyMap);
        \common\library\task\Helper::deleteTask($this->clientId, $mergedCompanyIds);
    }

    protected function log($msg)
    {
        if ($this->debugLog)
            \LogUtil::info($msg);
    }
}
