<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 17/8/29
 * Time: 下午5:31
 */

namespace common\library\customer\pool\migration;


use common\library\account\UserInfo;
use common\library\account\UserList;
use common\library\department\DepartmentCacheableRepo;
use common\library\department\DepartmentRedis;

class PoolFormatter extends \ListItemFormatter
{
    protected   $clientId;
    protected   $showFieldsInfo = true;
    protected   $specifyFields;
    protected   $showMemberInfo = false;
    protected   $showAllUserPool = false; //BOSS端进来，展示默认公海分组

    protected   $onlyShowMemberUser = false;
    protected   $showMemberDetail  = false;
    protected   $pageSize = null;
    protected   $page = null;
    protected   $defaultPoolName = "公共公海分组";

    protected   $departmentRedis;
    public function __construct($clientId)
    {
        $this->clientId = $clientId;

    }

    /**
     * @param bool $showFieldsInfo
     */
    public function set(bool $showFieldsInfo)
    {
        $this->showFieldsInfo = $showFieldsInfo;
    }

    /**
     * @param bool $showFieldsInfo
     */
    public function setShowFieldsInfo(bool $showFieldsInfo)
    {
        $this->showFieldsInfo = $showFieldsInfo;
    }
    /**
     * @param int $page
     */
    public function setPage($page)
    {
        $this->page = $page;
    }
    /**
     * @param int $page
     */
    public function setPageSize($pageSize)
    {
        $this->pageSize = $pageSize;
    }

    /**
     * @param mixed $specifyFields
     */
    public function setSpecifyFields($specifyFields)
    {
        $this->specifyFields = $specifyFields;
    }

    /**
     * @param bool $showMemberInfo
     */
    public function setShowMemberInfo(bool $showMemberInfo)
    {
        $this->showMemberInfo = $showMemberInfo;
    }

    /**
     * @param bool $showMemberInfo
     */
    public function setShowAllUserPool(bool $showAllUserPool)
    {
        $this->showAllUserPool = $showAllUserPool;
    }

    /**
     * @param bool $onlyShowMemberUser
     */
    public function setOnlyShowMemberUser(bool $onlyShowMemberUser)
    {
        $this->onlyShowMemberUser = $onlyShowMemberUser;
    }

    /**
     * @param bool $showMemberDetail
     */
    public function setshowMemberDetail(bool $showMemberDetail)
    {
        $this->showMemberDetail = $showMemberDetail;
    }


    /**
     * @return mixed
     */
    public function getDepartmentRedis()
    {
        if( $this->departmentRedis === null )
            $this->departmentRedis = new DepartmentRedis();

        return $this->departmentRedis;
    }

    public function buildMapData()
    {
        $list = $this->batchFlag ? $this->listData : [$this->data];
        $poolIds = array_column($list,'pool_id');

        $memberInfoMap = [];
        $userMap = [];
        $departmentNameMap = [];
        $userDepartmentList = [];
        if( $this->showMemberInfo ){
            $db = \PgActiveRecord::getDbByClientId($this->clientId);
            $sql = 'select pool_id,type,refer_id from tbl_company_pool_member where client_id=:client_id and pool_id in ('.implode(',',$poolIds).') and enable_flag=:enable_flag';
            $params =   [
                ':client_id' => $this->clientId,
                ':enable_flag' => 1,
            ];

            $memberList = $db->createCommand($sql)->queryAll(true,$params);
            $userIds = [];
            $departmentIds = [];
            foreach ( $memberList as $member ){
                if( $member['type'] == Pool::MEMBER_TYPE_USER )
                    $userIds[] = $member['refer_id'];
                else{
                    $departmentIds[] = $member['refer_id'];
                }

                $memberInfoMap[$member['pool_id']][$member['type']][] = $member;
            }

            $userInfoList =  \common\library\account\Helper::getBatchUserInfo($this->clientId,$userIds);

            $userInfoList = array_map(function ($item){
                $realName = $item->family_name . $item->second_name;
                return [
                    'user_id' => $item->user_id,
                    'avatar' => $item->avatar,
                    'name' => $item->nickname,
                    'nickname' => $item->nickname,
                    'realname' => $realName,
                ];

            }, $userInfoList??[]);


            $userMap = array_combine(array_column($userInfoList,'user_id'),$userInfoList);

            $departmentList = DepartmentCacheableRepo::instance($this->clientId)->findByIds($departmentIds, 'id,name');
//            $departmentList = \Department::findByIds($this->clientId,$departmentIds);
            $departmentNameMap = array_column($departmentList??[],'name','id');
            $departmentNameMap['0'] = \Yii::t('common', 'My enterprise');

            if($this->showMemberDetail && $userIds){
                $userDepartmentList = Helper::getUserDepartmentList($userIds,$this->clientId);

            }

        }

        $map = [
            'user' => $userMap,
            'department_name' => $departmentNameMap,
            'member_info' =>$memberInfoMap,
        ];
        if($this->showMemberDetail){
            if($userDepartmentList){
                $map['department_list'] = array($member['pool_id'] => $userDepartmentList);
            }
        }

        $this->setMapData($map);

        parent::buildMapData();
    }

    protected function buildFieldsInfo($data)
    {
        $result = [];

        if( !$this->showFieldsInfo ) return $result;

        $specifyFields = $this->specifyFields;
        if ($this->specifyFields === null) {
            $specifyFields = array_keys($data);
        }

        $result = \ArrayUtil::columns($specifyFields, $data, '');

        return $result;
    }


    public function listInfoSetting(){

        $this->setSpecifyFields([
            'name',
        ]);

        $this->setShowMemberInfo(true);
    }



    protected function formatMember($poolId){

        $redis = $this->getDepartmentRedis();
        $result = [];

        //公共公海分组
        if($poolId == 0){
            $allUserList = Helper::getAllUserMemberInfo($this->clientId);
            if($allUserList && isset($allUserList) && $allUserList){
                return $allUserList['list'];
            }
        }

        $memberMap = $this->getMapData('member_info', $poolId)??[];
        $departmentMap = array();
        if($this->showMemberDetail){
            $departmentMap = $this->getMapData('department_list', $poolId)??[];
        }
        if( empty($memberMap))
            return $result;

        $allUserIds = array_column($memberMap[Pool::MEMBER_TYPE_USER]??[], 'refer_id');
        $atDepartmentUserIds = [];

        if( !$this->onlyShowMemberUser ){

            foreach ( $memberMap[Pool::MEMBER_TYPE_DEPARTMENT]??[] as $member ){

                $name = $this->getMapData('department_name',$member['refer_id']);
                if( is_null($name) )
                    continue;

                $departmentUserIds = $redis->getUserList($this->clientId, $member['refer_id']);
                $userList  = [];
                foreach ( array_intersect($departmentUserIds, $allUserIds) as $userId ){
                    $userInfo = $this->getMapData('user',$userId);
                    $userData =  [
                        'pool_id' => $poolId,
                        'type' => Pool::MEMBER_TYPE_USER,
                        'refer_id' => strval($userId),
                        'name' => $userInfo['nickname']??'',
                        'avatar' => $userInfo['avatar']??''
                    ];
                    if($this->showMemberDetail) {
                        $userData['department'] = $departmentMap[$userId]['department_name'] ?? "";
                        $userData['realname'] = $departmentMap[$userId]['real_name'] ?? "";
                    }
                    $userList[] = $userData;

                }

                $member['member'] = $userList;
                $member['member_count'] = count($userList);
                $member['name'] = $name;
                $member['refer_id'] = strval($member['refer_id']);
                $atDepartmentUserIds = array_merge($member['member']);
                $result[] = $member;

            }

        }
        foreach ( $memberMap[Pool::MEMBER_TYPE_USER]??[] as $member ){
            if( in_array( $member['refer_id'], $atDepartmentUserIds))
                continue;

            $userInfo = $this->getMapData('user',$member['refer_id']);

            if( is_null($userInfo) )
                continue;

            $member['name'] = $userInfo['nickname']??'';
            $member['avatar'] = $userInfo['avatar']??'';
            $member['refer_id'] = strval($member['refer_id']);

            if($this->showMemberDetail) {
                $member['department'] = $departmentMap[$member['refer_id']]['department_name'] ?? "";
                $member['realname'] = $departmentMap[$member['refer_id']]['real_name'] ?? "";
            }
            $result[] = $member;

        }


        return $result;

    }


    public function format($data)
    {

        $poolId = $data['pool_id'];

        $result = [
            'pool_id' => $poolId,
        ];

        if (!empty($this->specifyFields) && in_array('id', $this->specifyFields)) {
            $data['id'] = $data['pool_id'] ?? 0;
        }

        if($poolId == Helper::PUBLIC_USER_DEFAULT_POOL_ID) {
            $data['name'] = \Yii::t('customer', 'Public high seas');     //公共公海分组--要翻译
        }

        $result = array_merge($result,$this->buildFieldsInfo($data));

        if( $this->showMemberInfo ){

            $member = $this->formatMember($poolId);
            $result['member_count'] = count($member);
            //如果需要分页
            if($this->page !== null && $this->pageSize !== null){
                $splitMember= array_slice($member,($this->page - 1)*$this->pageSize,$this->pageSize);
                $result['member']  = $splitMember;
            }else{
                $result['member'] = $member;
            }
        }

        return $result;
    }


}