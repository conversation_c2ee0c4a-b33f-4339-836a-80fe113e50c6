<?php

namespace common\library\customer\service;

use common\library\behavior\BehaviorService;
use common\library\object\object_define\Constant;
use common\library\opportunity\Opportunity;
use common\library\trail\events\RemarkEvents;
use common\library\trail\TrailConstants;
use Constants;
use LogUtil;
use PgActiveRecord;
use RuntimeException;

class OpportunityRemarkService extends RemarkService {


	private $opportunityId;

	private $opportunity;

    private $remarkEvents;


	public function __construct($clientId, $userId, $opportunityId = 0) {

		parent::__construct($clientId, $userId);

		if (!$opportunityId) {

			throw new RuntimeException(\Yii::t('opportunity', 'Opportunity does not exist'));
		}

		$this->setOpportunityById($opportunityId);
	}

	private function setOpportunityById($opportunityId) {

		$this->opportunityId = $opportunityId;

		$opportunity = new Opportunity($this->clientId, $this->opportunityId);

		$opportunity->setUserId($this->userId);

		if (!$opportunity->isExist()) {

			throw new \RuntimeException('商机:' . $this->opportunityId . '不存在');
		}

		if (!$opportunity->canRemark()) {

			throw new \RuntimeException(\Yii::t('account', 'No permission'));
		}

		$this->opportunity = $opportunity;
	}

	public function remark() {

		parent::remark(); // TODO: Change the autogenerated stub
	}

    protected function getRemarkEventsData()
    {
        return array_merge(parent::getRemarkEventsData(), ['obj_name' => Constant::OBJ_OPPORTUNITY, 'obj_id' => $this->opportunityId]); // TODO: Change the autogenerated stub
    }
    protected function runRemarkEvents($data) {

        if (!empty($this->mailIds))
        {
            $emailCount = count($this->mailIds);
            $createTime = $this->remarkTime ?: xm_function_now();
            $trail = new \common\library\trail\events\BatchTrailEvents();
            $trail->setType(TrailConstants::TYPE_REMARK_RELATE_MAIL);
            $trail->setClientId($this->clientId);
            $trail->setCreateUser($this->userId);
            $trail->setUserId($this->userId);
            $trail->setOpportunityId(array_fill(0, $emailCount, $this->opportunityId));
            $trail->setCompanyId(array_fill(0, $emailCount, $this->opportunity->company_id));
            $trail->setCustomerId(array_fill(0, $emailCount, [$this->customerId]));
            $trail->setReferId($this->mailIds);
            $trail->setData(array_fill(0, $emailCount, [
                'content' => $this->content,
                'file_ids' => $this->fileIds,
                'address' => '',
                'longitude' => 0,
                'latitude' => 0
            ]));
            $trail->setCreateTime($createTime);
            $trail->setSkipValidatorMap([TrailConstants::TYPE_REMARK_RELATE_MAIL]);
            $trail->run();


            \common\library\trail\Helper::resetCompanyLastTrailId($this->clientId, $this->opportunity->company_id);
            //更新最联系时间
            \common\library\customer\Helper::updateRemarkTime($this->clientId, $this->userId, $this->opportunity->company_id, $this->customerId,TrailConstants::TYPE_REMARK_RELATE_MAIL, '');

            //行为记录
            $behavior = new BehaviorService();
            $behavior->setReferId($this->opportunityId);
            $behavior->setType(\common\library\behavior\Helper::convertOpportunityTrailType(TrailConstants::TYPE_REMARK_RELATE_MAIL));
            $behavior->add();
            \StatisticsService::dynamicRemarkAdd($this->clientId, $this->userId, $emailCount);
        } else
        {
            $this->remarkEvents = new RemarkEvents();

            $this->remarkEvents->setType($this->remarkType);

            $this->remarkEvents->setOpportunityId($this->opportunityId);

            $this->remarkEvents->setCompanyId($this->opportunity->company_id);

            $this->remarkEvents->setCustomerId($this->customerId);

            $this->remarkEvents->setClientId($this->clientId);

            $this->remarkEvents->setCreateUser($this->userId);

            $this->remarkEvents->setCreateTime($this->remarkTime);

            $this->remarkEvents->setUserId($this->userId);

            $this->remarkEvents->setData($data);

            $this->remarkEvents->run();
        }
	}

	protected function saveNextFollowUpTime() {


		$sql = 'UPDATE tbl_opportunity 
						SET next_follow_up_time = \'' . $this->nextFollowUpTime . '\''
			. ' WHERE client_id = ' . $this->clientId
			. ' AND opportunity_id = ' . $this->opportunityId;

		$result = PgActiveRecord::getDbByClientId($this->clientId)->createCommand($sql)->execute();

		LogUtil::info('update opportunity next_follow_up_time result: ' . $result
			. ' user_id: ' . $this->userId
			. ' client_id: ' . $this->clientId
			. ' opportunity_id: ' . $this->opportunityId
			. ' next_follow_up_time: ' . $this->nextFollowUpTime
		);

		if ($this->setUserSchedule) {

			$this->saveUserSchedule();
		}
	}

	protected function getUserScheduleData(): array {

		$data = parent::getUserScheduleData();

		$data['title'] = '跟进商机：' . $this->opportunity->name;

		$data['refer_type'] = Constants::TYPE_OPPORTUNITY;

		$data['refer_id'] = $this->opportunity->opportunity_id;

		return $data;
	}

    /**
     * @return mixed
     */
    public function getRemarkEvents() {

        return $this->remarkEvents;
    }
}