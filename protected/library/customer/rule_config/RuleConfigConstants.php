<?php

namespace common\library\customer\rule_config;

use common\library\account\Client;
use common\library\custom_field\CustomFieldService;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\workflow\WorkflowConstant;
use Constants;

class RuleConfigConstants {
    
    
    const SUPPORTED_REFER_TYPE_LIST = [
        
        Constants::TYPE_COMPANY,
        Constants::TYPE_CUSTOMER,
        Constants::TYPE_OPPORTUNITY,
        Constants::TYPE_LEAD,
        Constants::TYPE_LEAD_CUSTOMER,
        Constants::TYPE_OTHER,
    ];


//    swarm
    const RULE_CONFIG_TYPE_SWARM                        = 101;
    const RULE_CONFIG_TYPE_SWARM_PUBLIC                 = 102;

//    company
    const RULE_CONFIG_TYPE_COMPANY_LIST_NORMAL          = 201;
    const RULE_CONFIG_TYPE_COMPANY_LIST_ADVANCED        = 202;
    const RULE_CONFIG_TYPE_COMPANY_FULL_FIELD           = 203;
    const RULE_CONFIG_TYPE_COMPANY_FULL_FIELD_EXPORT    = 204;

//    company public
    const RULE_CONFIG_TYPE_COMPANY_LIST_NORMAL_PUBLIC   = 301;
    const RULE_CONFIG_TYPE_COMPANY_LIST_ADVANCED_PUBLIC = 302;
    const RULE_CONFIG_TYPE_COMPANY_FULL_FIELD_PUBLIC    = 303;

//    lead
    const RULE_CONFIG_TYPE_LEAD_LIST_NORMAL             = 401;
    const RULE_CONFIG_TYPE_LEAD_LIST_ADVANCED           = 402;
    const RULE_CONFIG_TYPE_LEAD_FULL_FIELD              = 403;
    const RULE_CONFIG_TYPE_LEAD_EXPORT_FIELD            = 404;

//    lead public
    const RULE_CONFIG_TYPE_LEAD_LIST_NORMAL_PUBLIC      = 501;
    const RULE_CONFIG_TYPE_LEAD_LIST_ADVANCED_PUBLIC    = 502;
    const RULE_CONFIG_TYPE_LEAD_FULL_FIELD_PUBLIC       = 503;

//    opportunity
    const RULE_CONFIG_TYPE_OPPORTUNITY_LIST_NORMAL      = 601;
    const RULE_CONFIG_TYPE_OPPORTUNITY_LIST_ADVANCED    = 602;
    const RULE_CONFIG_TYPE_OPPORTUNITY_FULL_FIELD       = 603;
    
    
    
    const   RULE_TYPE_COMPANY       = 11;
    const   RULE_TYPE_CUSTOMER      = 12;
    const   RULE_TYPE_DATE          = 7;
    const   RULE_TYPE_OPPORTUNITY   = 13;
    const   RULE_TYPE_LEAD          = 14;
    const   RULE_TYPE_LEAD_CUSTOMER = 15;
    
    
    const RULE_TYPE_FULL_FIELD_LIST = [
        
        self::RULE_CONFIG_TYPE_COMPANY_FULL_FIELD,
        self::RULE_CONFIG_TYPE_COMPANY_FULL_FIELD_PUBLIC,
        self::RULE_CONFIG_TYPE_COMPANY_FULL_FIELD_EXPORT,
        self::RULE_CONFIG_TYPE_LEAD_FULL_FIELD,
        self::RULE_CONFIG_TYPE_LEAD_FULL_FIELD_PUBLIC,
        self::RULE_CONFIG_TYPE_LEAD_EXPORT_FIELD,
    ];
    
    const RULE_TYPE_NAME = [
        
        self::RULE_TYPE_COMPANY       => 'Company',
        self::RULE_TYPE_CUSTOMER      => 'Customer',
        self::RULE_TYPE_DATE          => 'Date',
        self::RULE_TYPE_OPPORTUNITY   => 'Opportunity',
        self::RULE_TYPE_LEAD          => 'Lead',
        self::RULE_TYPE_LEAD_CUSTOMER => 'Lead Customer',
    ];
    
    const REFER_RULE_TYPE_MAP = [
        Constants::TYPE_COMPANY       => self::RULE_TYPE_COMPANY,
        Constants::TYPE_CUSTOMER      => self::RULE_TYPE_CUSTOMER,
        Constants::TYPE_OPPORTUNITY   => self::RULE_TYPE_OPPORTUNITY,
        Constants::TYPE_LEAD          => self::RULE_TYPE_LEAD,
        Constants::TYPE_LEAD_CUSTOMER => self::RULE_TYPE_LEAD_CUSTOMER,
    ];
    
    const FILTER_FIELDS_IGNORE = [
        
        self::RULE_CONFIG_TYPE_SWARM => [
            
            Constants::TYPE_COMPANY  => [
                
                'province',
                'city',
                //				'cus_tag',
                'client_tag_list',
                'pin',
                'category_ids',
                'score',
                'will_public',
                'post',
                'last_trail',
                'last_remark_trail',
                
                'tag',
                'swarm_list',
                'image_list',
                'main_lead_id',

                'public_type',
                'public_reason_id',
                'stage_type',
            ],
            Constants::TYPE_CUSTOMER => [
                'post',
                'image_list',
                'main_customer_flag',
                'birth', // 客群不支持联系人生日筛选
            ],

        ],


        self::RULE_CONFIG_TYPE_SWARM_PUBLIC => [

            Constants::TYPE_COMPANY  => [

                'province',
                'city',
                //				'cus_tag',
                'client_tag_list',
                'pin',
                'category_ids',
                'score',
                'will_public',
                'post',
                'last_trail',
                'last_remark_trail',

                'tag',
                'swarm_list',
                'image_list',
                'main_lead_id',

                'users',
                'user_id',
                'next_move_to_public_date',
                'pin_user_list',
                'stage_type',
            ],
            Constants::TYPE_CUSTOMER => [
                'post',
                'image_list',
                'main_customer_flag',
                'birth', // 客群不支持联系人生日筛选
            ],
        
        ],
        
        self::RULE_CONFIG_TYPE_COMPANY_LIST_NORMAL => [
            
            Constants::TYPE_COMPANY  => [
                
                'pin',
                //				'score',
                'will_public',
                
                'tag',
                'client_tag_list',
                'image_list',
                'main_lead_id',
                'last_trail',
                'last_remark_trail',
                'public_type',
                'public_reason_id',
                'stage_type',
            ],
            Constants::TYPE_CUSTOMER => [

                'post',
                'image_list',
                'main_customer_flag',

            ],

            Constants::TYPE_OPPORTUNITY => [
                'company_id',
                'customer_id',
                'product_total_count',
                'product_other_amount',
                'product_total_amount',
                'exchange_rate',
                'main_lead_id',
                'remark',
                'department',
                'cash_collection_collect_amount',
                'cash_collection_not_collect_amount',
                'success_rate',
                'stage_stay_time',
                'customer_id',
                'fail_remark',
                'handler',
            ],

        ],

        self::RULE_CONFIG_TYPE_COMPANY_LIST_NORMAL_PUBLIC => [

            Constants::TYPE_COMPANY  => [

                'pin',
                //				'score',
                'will_public',

                'tag',
                'client_tag_list',
                'image_list',
                'main_lead_id',
                'last_trail',
                'last_remark_trail',

                'users',
                'user_id',
                'next_move_to_public_date',
                'pin_user_list',

            ],
            Constants::TYPE_CUSTOMER => [

                'post',
                'image_list',
                'main_customer_flag',

            ],

            Constants::TYPE_OPPORTUNITY => [
                'company_id',
                'customer_id',
                'product_total_count',
                'product_other_amount',
                'product_total_amount',
                'exchange_rate',
                'main_lead_id',
                'remark',
                'department',
                'cash_collection_collect_amount',
                'cash_collection_not_collect_amount',
                'success_rate',
                'stage_stay_time',
                'customer_id',
                'fail_remark',
                'handler',
            ],
        
        ],
        
        self::RULE_CONFIG_TYPE_COMPANY_LIST_ADVANCED => [
            
            Constants::TYPE_COMPANY => [
                
                'pin',
                //'category_ids',新版客户支持主营产品的
                //				'score',
                'will_public',
                
                'tag',
                'client_tag_list',
                'image_list',
                'main_lead_id',
                'last_trail',
                'last_remark_trail',
                'public_type',
                'public_reason_id',
                'stage_type',
            ],

            Constants::TYPE_CUSTOMER => [

                'post',
                'image_list',
                'main_customer_flag',
            ],

            Constants::TYPE_OPPORTUNITY => [
                'company_id',
                'customer_id',
                'product_total_count',
                'product_other_amount',
                'product_total_amount',
                'exchange_rate',
                'main_lead_id',
                'remark',
                'department',
                'fail_remark',
                'cash_collection_collect_amount',
                'cash_collection_not_collect_amount',
                'success_rate',
                'stage_stay_time',
            ],
        
        ],
        
        self::RULE_CONFIG_TYPE_COMPANY_LIST_ADVANCED_PUBLIC => [
            
            Constants::TYPE_COMPANY => [

                'pin',
                //'category_ids',新版客户支持主营产品的
                //				'score',
                'will_public',
                
                'tag',
                'client_tag_list',
                'image_list',
                'main_lead_id',
                'last_trail',
                'last_remark_trail',

                'users',
                'user_id',
                'next_move_to_public_date',
                'pin_user_list',
            ],

            Constants::TYPE_CUSTOMER => [

                'post',
                'image_list',
                'main_customer_flag',
            ],

            Constants::TYPE_OPPORTUNITY => [
                'company_id',
                'customer_id',
                'product_total_count',
                'product_other_amount',
                'product_total_amount',
                'exchange_rate',
                'main_lead_id',
                'remark',
                'department',
                'fail_remark',
                'cash_collection_collect_amount',
                'cash_collection_not_collect_amount',
                'success_rate',
                'stage_stay_time',
            ],
        
        ],

        self::RULE_CONFIG_TYPE_COMPANY_FULL_FIELD => [

            Constants::TYPE_COMPANY  => [

                'province',
                'city',
                'client_tag_list',
                'pin',
                'will_public',
                'main_lead_id',

                'pin_flag',
                'duplicate_flag',
                'users',
                'growth_level',
                'public_type',
                'public_reason_id',
                'stage_type',
            ],
            Constants::TYPE_CUSTOMER => [

                'main_customer_flag',
                'suspected_invalid_email_flag',
                'forbidden_flag',
                'reach_status',
                'reach_status_time',
            ],
        ],

        self::RULE_CONFIG_TYPE_COMPANY_FULL_FIELD_PUBLIC => [

            Constants::TYPE_COMPANY  => [

                'province',
                'city',
                'client_tag_list',
                'pin',
                'will_public',
                'main_lead_id',

                'pin_flag',
                'duplicate_flag',
                'users',
                'growth_level',


                'users',
                'user_id',
                'next_move_to_public_date',
                'pin_user_list',
                'stage_type',
            ],
            Constants::TYPE_CUSTOMER => [

                'main_customer_flag',
                'suspected_invalid_email_flag',
                'forbidden_flag',
                'reach_status',
                'reach_status_time',
            ],
        ],

        self::RULE_CONFIG_TYPE_COMPANY_FULL_FIELD_EXPORT => [

            Constants::TYPE_COMPANY  => [

                'image_list',
                'client_tag_list',
                'pin',
                'will_public',
                'main_lead_id',

                'pin_flag',
                'duplicate_flag',
                'users',
                'growth_level',
                'stage_type',
            ],
            Constants::TYPE_CUSTOMER => [

                'image_list',
                'main_customer_flag',
                'suspected_invalid_email_flag',
                'forbidden_flag',
                'reach_status',
                'reach_status_time',
            ],
        ],


        self::RULE_CONFIG_TYPE_LEAD_LIST_NORMAL => [
            
            Constants::TYPE_LEAD          => [
                'company_id',
                'pin',
                'will_public',
                'tag',
                'client_tag_list',
                'image_list',
                'main_lead_id',
                'group_id',
                'last_trail',
                'last_remark_trail',
                'company_id',
            ],
            Constants::TYPE_LEAD_CUSTOMER => [
                
                'post',
                'image_list',
                'main_customer_flag',
                'tag',
                'remark',
            ],
        ],
        
        self::RULE_CONFIG_TYPE_LEAD_LIST_ADVANCED => [
            
            Constants::TYPE_LEAD => [
                'pin',
                'category_ids',
                //				'score',
                'will_public',
                
                'tag',
                'client_tag_list',
                'image_list',
                'main_lead_id',
                'last_trail',
                'last_remark_trail',
                'company_id',
                'customer_list.email',
                'customer_list.name',
                'customer_list.contact',
            ],
            
            Constants::TYPE_LEAD_CUSTOMER => [

                'tag',
                'post',
                'image_list',
                'main_customer_flag',
                'remark',
            ],
        ],

        self::RULE_CONFIG_TYPE_LEAD_FULL_FIELD => [

            Constants::TYPE_LEAD          => [

                'province',
                'city',
                'client_tag_list',
                'pin',
                'category_ids',
                'will_public',
                'pin_flag',
                'duplicate_flag',
                'users',
                'growth_level',
                'customer_list.email',
                'customer_list.name',
                'customer_list.contact',
            ],
            Constants::TYPE_LEAD_CUSTOMER => [

                'tag',
                'main_customer_flag',
                'suspected_invalid_email_flag',
                'remark',
                'reach_status',
                'reach_status_time',
            ],
        ],

        self::RULE_CONFIG_TYPE_LEAD_FULL_FIELD_PUBLIC => [

            Constants::TYPE_LEAD          => [

                'province',
                'city',
                'client_tag_list',
                'pin',
                'category_ids',
                'will_public',
                'pin_flag',
                'duplicate_flag',
                'users',
                'growth_level',
                'pin_user_list',
                'tag',
                //                等前端新版
                'cus_tag',
                'group_id',
                'biz_type',
                'timezone',
                'annual_procurement',
                'intention_level',
                'customer_list.email',
                'customer_list.name',
                'customer_list.contact',
            ],
            Constants::TYPE_LEAD_CUSTOMER => [

                'tag',
                'main_customer_flag',
                'suspected_invalid_email_flag',
                'remark',
                'reach_status',
                'reach_status_time',
            ],
        ],

        self::RULE_CONFIG_TYPE_LEAD_EXPORT_FIELD => [

            Constants::TYPE_LEAD          => [
                'image_list',
                'group_id',
                'biz_type',
//                'assess',
                'change_status_time',
                'company_id',
                'conversion_time',
                'create_opportunity_time',
                'duplicate_flag',
                'growth_level',
                'latest_receive_ali_tm_time',
                'latest_receive_ali_trade_time',
                'latest_send_ali_tm_time',
                'latest_write_follow_up_time',
                'next_follow_up_time',
                'pin_flag',
                'private_count',
                'private_time',
                'public_time',
                'read_flag',
                'receive_mail_time',
                'recent_lead_invalid_time',
                'release_count',
                'send_mail_time',
                'timezone'
            ],
            Constants::TYPE_LEAD_CUSTOMER => [
                'image_list',
                'tag',
                'main_customer_flag',
                'suspected_invalid_email_flag',
                'remark',
                'reach_status',
                'reach_status_time',
            ],

        ],


        self::RULE_CONFIG_TYPE_OPPORTUNITY_LIST_NORMAL => [
            Constants::TYPE_COMPANY  => [

                'pin',
                //				'score',
                'will_public',

                'tag',
                'client_tag_list',
                'image_list',
                'main_lead_id',
                'last_trail',
                'last_remark_trail',
                'stage_type',
            ],
            Constants::TYPE_CUSTOMER => [

                'post',
                'image_list',
                'main_customer_flag',

            ],

            Constants::TYPE_OPPORTUNITY => [
                'company_id',
                'customer_id',
                'product_total_count',
                'product_other_amount',
                'product_total_amount',
                'exchange_rate',
                'main_lead_id',
                'remark',
                'department',
                'cash_collection_collect_amount',
                'cash_collection_not_collect_amount',
                'success_rate',
                'stage_stay_time',
                'customer_id',
                'fail_remark',
                'handler',
                'succeed_time',
            ],
        ],

        self::RULE_CONFIG_TYPE_OPPORTUNITY_LIST_ADVANCED => [
            Constants::TYPE_COMPANY  => [

                'pin',
                //				'score',
                'will_public',

                'tag',
                'client_tag_list',
                'image_list',
                'main_lead_id',
                'last_trail',
                'last_remark_trail',
                'stage_type',
            ],
            Constants::TYPE_CUSTOMER => [

                'post',
                'image_list',
                'main_customer_flag',

            ],

            Constants::TYPE_OPPORTUNITY => [
                'company_id',
                'customer_id',
                'product_total_count',
                'product_other_amount',
                'product_total_amount',
                'exchange_rate',
                'main_lead_id',
                'remark',
                'department',
                'cash_collection_collect_amount',
                'cash_collection_not_collect_amount',
                'success_rate',
                'stage_stay_time',
                'customer_id',
                'fail_remark',
                'handler',
                'succeed_time',
            ],
        ],

        self::RULE_CONFIG_TYPE_OPPORTUNITY_FULL_FIELD => [

            Constants::TYPE_COMPANY => [

            ],

            Constants::TYPE_CUSTOMER => [

            ],

            Constants::TYPE_OPPORTUNITY => [
                'succeed_time',
            ],

        ],

    ];
    
    
    const NEED_CHECK_PRIVILEGE_FIELDS_MAP = [];

    const  NEED_CHECK_FUNCTIONAL_FIELDS_MAP = [

        Constants::TYPE_COMPANY     => [

            PrivilegeConstants::FUNCTIONAL_COMPANY_POOL         => [
                'public_time',
                'private_time',
                'release_count',
            ],
            PrivilegeConstants::FUNCTIONAL_COMPANY_POOL_SETTING => [
                'pool_id',
            ],
            PrivilegeConstants::FUNCTIONAL_OKKI_AI => [
                'latest_inquiry_status'
            ],
        ],
        Constants::TYPE_LEAD        => [

            PrivilegeConstants::FUNCTIONAL_LEAD_POOL => [
                'public_time',
                'private_time',
                'release_count',
            ],
            PrivilegeConstants::FUNCTIONAL_OKKI_AI => [
                'latest_inquiry_status'
            ],
        ],
        Constants::TYPE_OPPORTUNITY => [

            PrivilegeConstants::FUNCTIONAL_AI_CLASSIFY_OPPORTUNITY => [
                'create_type',
            ],
            PrivilegeConstants::FUNCTIONAL_OPPORTUNITY_NEW_DYNAMIC => [
                'next_follow_up_time',
            ],
        ],
        'common'                    => [

            PrivilegeConstants::FUNCTIONAL_PERFORMANCE     => [
                'transaction_order_amount',
                'performance_order_count',
                'transaction_order_amount_avg',
                'latest_transaction_order_time',
                'transaction_order_first_time',
                'transaction_order_first_amount',
                'deal_time',
            ],
            PrivilegeConstants::FUNCTIONAL_OPPORTUNITY     => [
                'success_opportunity_count',
                'ongoing_opportunity_count',
                'success_opportunity_amount_cny',
                'success_opportunity_amount_usd',
                'success_opportunity_amount_avg_cny',
                'success_opportunity_amount_avg_usd',
                'success_opportunity_first_time',
                'latest_success_opportunity_time',
                'create_opportunity_time',
            ],
            PrivilegeConstants::FUNCTIONAL_CASH_COLLECTION => [
                'cash_collection',
            ],
            PrivilegeConstants::FUNCTION_REVIEW_BASE       => [
                'approval_status',
            ],
        ],
    ];


    const NEED_CHECK_SETTING_FIELDS_MAP = [

        Constants::TYPE_COMPANY => [

            Client::EXTERNAL_KEY_ASSESS_DIMENSION_SWITCH => [
                'assess',
            ],
            Client::EXTERNAL_KEY_CUSTOMER_POOL_SWITCH    => [
                'pool_id',
            ],
        ],
        Constants::TYPE_LEAD => [

            Client::EXTERNAL_KEY_ASSESS_DIMENSION_SWITCH => [
                'assess',
            ],
        ],
    ];
    
    const NEED_CHECK_HAS_ANY_SYSTEM = [
        Constants::TYPE_COMPANY => [
            'latest_inquiry_status' => [
                PrivilegeConstants::CRM_PRO_SYSTEM_ID,
                PrivilegeConstants::OKKI_PRO_SYSTEM_ID,
            ],
        ],
        Constants::TYPE_LEAD => [
            'latest_inquiry_status' => [
                PrivilegeConstants::CRM_PRO_SYSTEM_ID,
                PrivilegeConstants::OKKI_PRO_SYSTEM_ID,
            ],
        ],
    ];


    const APP_FILTER_FIELDS_IGNORE = ['growth_level'];
    
    

    //	预设字段排序
    const COMPANY_FIELDS = ["name", "short_name", "serial_id", "province", "city", "cus_tag", "client_tag_list", "country", "timezone", "last_trail", "last_remark_trail", "trail_status", "stage_type", "origin_list", "group_id", "pool_id", "star", "score", "assess", "biz_type", "intention_level", "annual_procurement", "scale_id", "homepage", "fax", "tel", "address", "category_ids", "remark", "growth_level", "pin_flag", "customer_count", "image_list", "performance_order_count", "transaction_order_amount", "transaction_order_first_amount", "transaction_order_amount_avg", "success_opportunity_count", "ongoing_opportunity_count", "success_opportunity_amount_cny", "success_opportunity_amount_usd", "success_opportunity_amount_avg_cny", "success_opportunity_amount_avg_usd", "user_id", "last_owner", "alibaba_user_id", "alibaba_last_owner", "create_user", "last_edit_user", "users", "swarm_list", "release_count", "duplicate_flag", "ali_store_id", "main_lead_id", "archive_type", "product_group_ids", "pin_user_list", 'public_type', 'public_reason_id', 'latest_inquiry_status', 'total_opportunity', 'total_fail_opportunity',];

    const CUSTOMER_FIELDS = ["name","email","post_grade","post","tel_list","contact","gender","remark","image_list", 'suspected_invalid_email_flag', 'forbidden_flag', "reach_status"];
    
    const DATE_FIELDS  = ["archive_time","edit_time","order_time","recent_follow_up_time","next_follow_up_time","tips_latest_update_time","private_time","public_time","next_move_to_public_date","latest_write_follow_up_time","send_mail_time","receive_mail_time","latest_receive_ali_tm_time","latest_send_ali_tm_time","latest_receive_ali_trade_time","latest_whatsapp_time","deal_time","transaction_order_first_time","latest_transaction_order_time","success_opportunity_first_time","latest_success_opportunity_time","edm_time","birth","lead_archive_time", "latest_whatsapp_receive_time", "latest_whatsapp_send_time", 'latest_whatsapp_business_receive_time', 'latest_whatsapp_business_send_time', 'latest_facebook_receive_time', 'latest_facebook_send_time', 'latest_wechat_time', 'latest_wechat_receive_time', 'latest_wechat_send_time', 'latest_ins_time', 'latest_ins_receive_time', 'latest_ins_send_time', 'alibaba_first_sync_time', 'alibaba_recent_sync_time', 'reach_status_time', 'private_user_time', 'account_date', 'create_time', 'stage_stay_time'];

    const COMPANY_SORT_FIELDS = ["timezone", "country", "star", "serial_id", "name", "short_name", "trail_status_name", "trail_status", "origin_list", "score", "assess", "biz_type", "intention_level", "annual_procurement", "scale_id", "ali_store_id", "archive_type", "performance_order_count", "success_opportunity_count", "ongoing_opportunity_count", "release_count", "transaction_order_amount", "transaction_order_amount_avg", "success_opportunity_amount_cny", "success_opportunity_amount_usd", "success_opportunity_amount_avg_cny", "success_opportunity_amount_avg_usd", "next_move_to_public_date", "transaction_order_first_time", "success_opportunity_first_time", "latest_success_opportunity_time", "archive_time", "public_time", "private_time", "edit_time", "latest_edm_time", "order_time", "deal_time", "recent_follow_up_time", "next_follow_up_time", "tips_latest_update_time", "latest_write_follow_up_time", "send_mail_time", "receive_mail_time", "latest_receive_ali_tm_time", "latest_send_ali_tm_time", "latest_receive_ali_trade_time", "latest_transaction_order_time", "latest_whatsapp_time", "transaction_order_first_amount", "latest_whatsapp_receive_time", "latest_whatsapp_send_time", "latest_whatsapp_business_receive_time", "latest_whatsapp_business_send_time", "latest_facebook_receive_time", "latest_facebook_send_time", 'private_user_time', 'country_region'];

    const LEAD_FIELDS = ["name", "short_name", "serial_id", "company_name", "province", "city", "tag", "cus_tag", "client_tag_list", "country", "timezone", "last_trail", "last_remark_trail", "origin_list", "assess", "biz_type", "intention_level", "annual_procurement", "scale_id", "homepage", "fax", "tel", "address", "category_ids", "remark", "growth_level", "pin_flag", "image_list", "user_id", "last_owner", "create_user", "last_edit_user", "release_count", "duplicate_flag", "store_id", "archive_type", "pin_user_list", "transfer_count", "company_id", "follow_count", "status", "fail_type", "lost_day_count", "inquiry_origin", "submit_entry", "inquiry_country", "ad_keyword", "private_count", "ai_tags", "read_flag", "customer_list.email", "customer_list.name", "customer_list.contact", 'latest_inquiry_status'];

    const LEAD_CUSTOMER_FIELDS = ["name","email","post_grade","post","tel_list","contact","gender","remark","image_list","reach_status", "suspected_invalid_email_flag"];
    
    const LEAD_DATE_FIELDS  = ["archive_time","edit_time","order_time","next_follow_up_time","private_time","public_time","latest_write_follow_up_time","send_mail_time","receive_mail_time","latest_receive_ali_tm_time","latest_send_ali_tm_time","latest_receive_ali_trade_time","latest_whatsapp_time","latest_whatsapp_receive_time","latest_whatsapp_send_time","edm_time","birth", "conversion_time", "renew_time", "create_opportunity_time", "change_status_time", "recent_lead_invalid_time", "latest_edm_time", "follow_up_time", "reach_status_time"];

    const LEAD_SORT_FIELDS = ["follow_count","order_time","lost_day_count","transfer_count","release_count","renew_time","update_time","edit_time","private_time","public_time","create_time","follow_up_time","next_follow_up_time","assess","archive_type","country","latest_write_follow_up_time","receive_mail_time","latest_send_ali_tm_time","latest_receive_ali_tm_time","latest_receive_ali_trade_time","recent_lead_invalid_time","create_opportunity_time","archive_time","order_time","change_status_time","send_mail_time","latest_edm_time"];

    const OPPORTUNITY_FIELDS = [ 'flow_id', 'stage', 'fail_type', 'opportunity_type', 'stage_edit_time'];
    public static $fieldTypeSortSupport = [
        CustomFieldService::FIELD_TYPE_NUMBER,
        CustomFieldService::FIELD_TYPE_DATE,
        CustomFieldService::FIELD_TYPE_DATETIME,
        CustomFieldService::FIELD_TYPE_SELECT,
    ];

    public static $fieldTypeOperatorMap = [
        
        CustomFieldService::FIELD_TYPE_TEXT => [
            WorkflowConstant::FILTER_OPERATOR_IS_NULL,
            WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
        
        ],
        
        CustomFieldService::FIELD_TYPE_TEXTAREA => [
            WorkflowConstant::FILTER_OPERATOR_IS_NULL,
            WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
        ],
        
        CustomFieldService::FIELD_TYPE_SELECT => [
            WorkflowConstant::FILTER_OPERATOR_IN,
            WorkflowConstant::FILTER_OPERATOR_NOT_IN,
            WorkflowConstant::FILTER_OPERATOR_IS_NULL,
            WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
        ],
        
        CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT => [
            WorkflowConstant::FILTER_OPERATOR_EQUAL,
            WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL,
            WorkflowConstant::FILTER_OPERATOR_IN,
            WorkflowConstant::FILTER_OPERATOR_NOT_IN,
            WorkflowConstant::FILTER_OPERATOR_IS_NULL,
            WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
        ],
        
        CustomFieldService::FIELD_TYPE_DATE => [
            WorkflowConstant::FILTER_OPERATOR_EQUAL,
            WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL,
            WorkflowConstant::FILTER_OPERATOR_IS_NULL,
            WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
            WorkflowConstant::FILTER_OPERATOR_EARLIER,
            WorkflowConstant::FILTER_OPERATOR_LATER,
        ],
        
        CustomFieldService::FIELD_TYPE_DATETIME => [
            WorkflowConstant::FILTER_OPERATOR_EQUAL,
            WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL,
            WorkflowConstant::FILTER_OPERATOR_IS_NULL,
            WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
            WorkflowConstant::FILTER_OPERATOR_EARLIER,
            WorkflowConstant::FILTER_OPERATOR_LATER,
        ],
        
        CustomFieldService::FIELD_TYPE_NUMBER => [
            WorkflowConstant::FILTER_OPERATOR_EQUAL,
            WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL,
            WorkflowConstant::FILTER_OPERATOR_GREATER,
            WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL,
            WorkflowConstant::FILTER_OPERATOR_LESS,
            WorkflowConstant::FILTER_OPERATOR_LESS_OR_EQUAL,
            WorkflowConstant::FILTER_OPERATOR_IS_NULL,
            WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
            WorkflowConstant::FILTER_OPERATOR_RANGE,
        ],
        
        CustomFieldService::FIELD_TYPE_BOOLEAN => [
            WorkflowConstant::FILTER_OPERATOR_EQUAL,
        ],
        
        CustomFieldService::FIELD_TYPE_FORMULA => [
            WorkflowConstant::FILTER_OPERATOR_EQUAL,
            WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL,
            WorkflowConstant::FILTER_OPERATOR_GREATER,
            WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL,
            WorkflowConstant::FILTER_OPERATOR_LESS,
            WorkflowConstant::FILTER_OPERATOR_LESS_OR_EQUAL,
            WorkflowConstant::FILTER_OPERATOR_IS_NULL,
            WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
            WorkflowConstant::FILTER_OPERATOR_RANGE,
        ],
        
        CustomFieldService::FIELD_TYPE_CALCULATE => [
            WorkflowConstant::FILTER_OPERATOR_EQUAL,
            WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL,
            WorkflowConstant::FILTER_OPERATOR_GREATER,
            WorkflowConstant::FILTER_OPERATOR_GREATER_OR_EQUAL,
            WorkflowConstant::FILTER_OPERATOR_LESS,
            WorkflowConstant::FILTER_OPERATOR_LESS_OR_EQUAL,
            WorkflowConstant::FILTER_OPERATOR_IS_NULL,
            WorkflowConstant::FILTER_OPERATOR_NOT_NULL,
            WorkflowConstant::FILTER_OPERATOR_RANGE,
        ],
    ];

    public static function getFieldReplaceMap() {

        return [

            Constants::TYPE_COMPANY  => [
                'cus_tag'          => [
                    'id' => 'tag',
                ],
                'group_id'         => [
                    'id' => 'group_name',
                ],
                'pool_id'          => [
                    'id' => 'pool_name',
                ],
                'trail_status'     => [
                    'id' => 'trail_status_name',
                ],
                'user_id'          => [
                    'id' => 'owner',
                ],
                'last_owner'       => [
                    'id' => 'last_owner_name',
                ],
                'public_reason_id' => [
                    'id' => 'public_reason',
                ],
            ],
            Constants::TYPE_CUSTOMER => [
                'name' => [
                    'name' => \Yii::t('field', '主要联系人'),
                ],
            ],
            Constants::TYPE_LEAD     => [
                'user_id'    => [
                    'id' => 'owner',
                ],
                'last_owner' => [
                    'id' => 'last_owner_name',
                ],
                'fail_type'  => [
                    'id' => 'fail_reason',
                ],
            ],
        ];
    }
}
