<?php
/**
 * The file is part of the php-crm.
 *
 * (c) anhoder <<EMAIL>>.
 *
 * 2023/9/18 09:50
 */

namespace common\library\util\sse;

class SseFrame
{
    private string $data;
    private ?string $id;
    private ?string $event;
    private ?int $retry;

    public function __construct(string $data, ?string $event = null, ?string $id = null, ?int $retry = null)
    {
        $this->id = $id;
        $this->event = $event;
        $this->data = $data;
        $this->retry = $retry;
    }

    /**
     * @return array
     */
    public function getData(): array
    {
        return json_decode($this->data, true) ?: [];
    }

    public function __toString(): string
    {
        $resp = '';
        if ($this->id) {
            $resp .= "id: {$this->id}\n";
        }
        if ($this->event) {
            $resp .= "event: {$this->event}\n";
        }
        if ($this->retry) {
            $resp .= "retry: {$this->retry}\n";
        }
        $resp .= "data: {$this->data}\n\n";
        return $resp;
    }
}
