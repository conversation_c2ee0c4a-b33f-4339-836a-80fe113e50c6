<?php
/**
 * The file is part of the php-crm.
 *
 * (c) anhoder <<EMAIL>>.
 *
 * 2023/9/18 09:50
 */

namespace common\library\util\sse;

use common\library\ai_agent\AiAgentConstants;

class SseResponse
{
    public function __construct()
    {
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no');
    }

    public function write(SseFrame $frame)
    {
        echo $frame;
        ob_flush();
        flush();
    }

    public function writeJson(array $data)
    {
        $this->write(new SseFrame(json_encode($data, JSON_UNESCAPED_UNICODE)));
    }

    public function complete()
    {

    }

}
