<?php

namespace common\library\util\orm;

use xiaoman\orm\database\data\AbstractData;
use xiaoman\orm\database\DBConstants;
use xiaoman\orm\database\document\chapter\TableChapter;
use xiaoman\orm\database\document\chapter\WhereChapter;
use xiaoman\orm\database\document\clause\ParagraphClause;
use xiaoman\orm\database\document\clause\WhereClause;
use xiaoman\orm\database\document\Query as OrmQuery;
use xiaoman\orm\repository\Repository;

/**
 * @method Query select($columns = [])
 * @method Query group($column, $perfix=null)
 * @method Query groupClosure($column,$closure)
 * @method Query order($column, $direction = 'asc',$prefix = null)
 * @method Query orderClosuer($column,callable $closuer)
 * @method Query limit($limit,$offset)
 * @method Query table(string $table,string $alias = null)
 * @method Query where($column, AbstractData $data, $boolean = DBConstants::LOGIC_AND)
 * @method Query paragraph(ParagraphClause $paragraphClause)
 * @method Query whereClosure($column,callable $closure)
 * @method Query removeWhere(array $columns)
 * @method Query clean()
 * @method Query bindTabel(TableChapter $tableChapter)
 * @method Query bindWhere(WhereChapter $whereChapter)
 */
class Query
{
    public function __construct(
        /**
         * @var Repository
         */
        protected $repository,
        protected OrmQuery $document,
    ) {
    }

    public function __call(string $name, array $arguments)
    {
        $this->document->$name(...$arguments);
        return $this;
    }

    public function selectRaw(string $raw): static
    {
        $this->document->getColumn()->addClosure($raw, fn () => $raw);
        return $this;
    }

    public function rawWhere(string $raw, array $bindings = [], $boolean = DBConstants::LOGIC_AND): static
    {
        $this->document->getWhere()
            ->addWhereClause(
                new WhereClause($raw, new RawWhere($raw, $bindings), $boolean),
            );
        return $this;
    }

    public function find(): array
    {
        return $this->repository->findBy($this->document);
    }

    public function delete(): int
    {
        return $this->repository->deleteBy($this->document->convertToDelete());
    }

    public function updates(array $data): int
    {
        $update = $this->document->convertToUpdate();
        $update->set($data);

        return $this->repository->updateBy($update);
    }

    public function count(): int
    {
        return $this->repository->count($this->document);
    }

    public function upsert(array $values, $uniqueBy, $update = null): int
    {
        if (empty($values)) {
            return 0;
        }

        $repository = $this->repository;
        if ($update === []) {
            return $repository->create($values, DBConstants::INSERT_MODE_GENERAL);
        }

        if (is_null($update)) {
            $update = array_keys(reset($values));
        }

        if (! is_array(reset($values))) {
            $values = [$values];
        } else {
            foreach ($values as $key => $value) {
                ksort($value);
                $values[$key] = $value;
            }
        }

        $onConflicts = [];
        foreach ($update as $key => $value) {
            if (is_numeric($key)) {
                $onConflicts[$value] = 'EXCLUDED.'.$value;
            } else {
                $onConflicts[$key] = $value;
            }
        }
        return $repository->create($values, DBConstants::INSERT_MODE_CONFLICT, $onConflicts, $uniqueBy);
    }
}
