<?php
/**
 * Created by PhpStorm.
 * User: amu
 * Date: 2021/4/25
 * Time: 3:01 下午
 */

namespace common\library\util;


class RMBUtil
{
    /**
     * 阿拉伯数字转中文大写金额
     * @param $number
     * @return string|string[]|null
     */
    public static function NumToCNMoney($number)
    {
        $number = trim($number);
        $negative = floatval($number) < 0;
        $number = ltrim($number, '-');
        $capnum = array("零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖");
        $capdigit = array('', "拾","佰","仟","万","拾","佰","仟","亿");
        $subdata = explode(".", $number);
        $yuan = $subdata[0];
        $j = 0;
        $nonzero = 0;
        for ($i = 0; $i < strlen($subdata[0]); $i++) {
            if ($i == 0) { //确定个位
                if (!empty($subdata[1])) {
                    $cncap = (substr($subdata[0], -1, 1) != 0) ? "元" : "元零";
                } else {
                    $cncap = "元";
                }
            }
            if ($i == 4) { //确定万位
                $j = 0;
                $nonzero = 0;
                $cncap = "万" . $cncap;
            }
            if ($i == 8) { //确定亿位
                $j = 0;
                $nonzero = 0;
                $cncap = "亿" . $cncap;
            }
            $numb = substr($yuan, -1, 1); //截取尾数
            $cncap = ($numb)
                ? $capnum[$numb]
                . $capdigit[$j]
                . $cncap
                : (($nonzero) ? "零" . $cncap : $cncap);
            $nonzero = ($numb) ? 1 : $nonzero;
            $yuan = substr($yuan, 0, strlen($yuan) - 1); //截去尾数
            $j++;
        }
        if (!empty($subdata[1])) {
            $chiao = (substr($subdata[1], 0, 1)) ? $capnum[substr($subdata[1], 0, 1)] . "角" : "零角";
            $cent = (substr($subdata[1], 1, 1)) ? $capnum[substr($subdata[1], 1, 1)] . "分" : "零分";
        } else {
            $chiao = "零角";
            $cent = "零分";
        }
        $cncap .= $chiao . $cent;
        $cncap = preg_replace("/(零)+/", "\\1", $cncap); //合并连续“零”

        return ($negative ? '负' : '') .$cncap;
    }


}