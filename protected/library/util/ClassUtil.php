<?php


namespace common\library\util;


class ClassUtil
{

    public static function getAllConstants($class)
    {
        $content = file_get_contents((new \ReflectionClass($class))->getFileName());
        $tokens = token_get_all($content);
        $constant = [
            'line' => 0,
        ];
        $constants = [];
        foreach ($tokens as $tokenIndex => $token) {
            if (is_array($token)) {
                list($type, $v, $line)  = $token;
                if ($v == 'const') {
                    $constant = [
                        'line' => $line,
                        'name' => '',
                        'key' => '',
                        'desc' => '',
                    ];
                    continue;
                }
                if ($constant['line'] == $line) {
                    switch ($type) {
                        case 319:
                            $constant['name'] = $v;
                            break;
                        case 323:
                            $constant['key'] = trim($v, "\'\"");
                            break;
                        case 377:
                            $constant['desc'] = trim($v, " \n\r\t\v\0\/");
                            break;
                        default:
                            break;
                    }
                    continue;
                } else {
                    if (!empty($constant['line']) && $constant['line'] != $line) {
                        $constants[] = $constant;
                        $constant = [
                            'line' => 0,
                            'name' => '',
                            'key' => '',
                            'desc' => '',
                        ];
                    }
                }
            }
        }

        return $constants;
    }

}