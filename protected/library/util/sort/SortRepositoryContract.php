<?php
/**
 * Created by PhpStorm.
 * User: troyli
 * Date: 2019-04-01
 * Time: 12:27
 */

namespace common\library\util\sort;


interface SortRepositoryContract
{

    /**
     * @param array $list [ id => sortNum ]
     *
     * @return mixed
     */
    public function updateSortNum(array $list);

    /**
     * @param $id
     *
     * @return [ id, num]
     */
    public function getMostGreater($num);

    /**
     * @param $id
     *
     * @return array [ id, num]
     */
    public function getMostLesser($num);

    /**
     * @param $id
     *
     * @return int
     */
    public function getSortNumById($id);

    /**
     * @return array [ id ]
     */
    public function getList();

    public function getOrder();

    public function setSortField($sortField);
}