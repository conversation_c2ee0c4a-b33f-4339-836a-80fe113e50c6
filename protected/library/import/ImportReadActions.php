<?php


namespace common\library\import;


use AliyunUpload;
use common\library\export_v2\Export;
use common\library\export_v2\ExportConstants;
use common\library\export_v2\ExportFilter;
use common\library\invoice\Helper;
use common\library\oms\common\ExcelParseExecutor;
use common\library\product_v2\import\ProductImportApi;
use common\library\queue_v2\job\ExportJob;
use common\library\queue_v2\QueueService;
use common\library\setting\item\ItemSettingConstant;
use common\library\setting\library\import_template\OrderProductImportTemplateApi;
use ErrorCode;
use RuntimeException;
use User;
use xiaoman\orm\database\data\Equal;

trait ImportReadActions
{
    private $importType;
    private $formatSetting;
    private $importScene;

    abstract public function initImportRead();

    public function actionImportList($page_size = 20,$page_no = 1, $scene = null)
    {
        $this->initImportRead();
        $importFilter = new  ImportFilter($this->getLoginUserClientId());
        if ($this->importType == \Constants::TYPE_ORDER) {
            $importFilter->order('create_time','desc');
        } else {
            $importFilter->order('update_time','desc');
        }
        $importFilter->limit($page_size,$page_no);
        $importFilter->user_id = new Equal($this->getLoginUserId());
        $importFilter->type = new Equal($this->importType);
	    $importFilter->scene = new Equal($scene ?? $this->importScene);

        $count = $importFilter->count();
        $list=[];
        if( $count )
        {
            $setting = $this->formatSetting ?? 'webListSetting';
            $batchImport = $importFilter->find();
            $batchImport->getFormatter()->$setting();
            $list = $batchImport->getAttributes();
        }

        return $this->success([
            'list' => $list,
            'count' => $count
        ]);
    }

    public function actionImportTaskInfo($import_id)
    {
        $user = User::getLoginUser();
        try {
            $import = new Import($user->getClientId(), $import_id);
        } catch (RuntimeException $e) {
            return $this->fail(-1, '导出任务不存在');
        }
        $fileId = $import->result_file_id;
        if ($fileId) {
            $file = new AliyunUpload();
            $file->loadByFileId($fileId);
            $url = $file->getFileUrl();
        }

        return $this->success([
            'info' => $import->getRawData(),
            'result_file_url' => $url ?? ''
        ]);
    }

    // 解析导入excel的头部
    public function actionPrepareExcelHeader($fileId, $scene = ExcelParseExecutor::EXPORT_SCENE_HEADER){
        $this->initImportRead();
        $userId = $this->getLoginUserId();
        $clientId = $this->getLoginUserClientId();
        $returnAttrs = ['export_id','template_file_id','scene','status','export_time'];

        // 检查是否已存在任务
        $task = new Export($clientId);
        $task->load([
            'template_file_id' => $fileId,
            'enable_flag' => \Constants::ENABLE_FLAG_TRUE,
            'type' => $this->importType,
            'scene' => $scene
        ]);

        if(!$task->isNew()){     // 检查任务是否已成功或者仍在进行中，是则无需再次推入队列
            if($task->status == ExportConstants::EXPORT_STATUS_SUCCESS){
                return $this->success($task->getAttributes($returnAttrs));
            }

            $executor = new ExcelParseExecutor($task);
            if($executor->isDownloading()){
                return $this->success($task->getAttributes($returnAttrs));
            }
        }else{
            $task->template_file_id = $fileId;
            $task->user_id = $userId;
            $task->type = $this->importType;
            $task->params = [];
            $task->scene = $scene;
            $task->create();
        }

        // 根据文件大小判断是同步还是异步执行
        $fileInfo = \UploadFile::findByFileId($fileId);
        $executorParams = ['save_result' => true,];

        $job = new ExportJob($userId, $task->export_id, Export::class, ExcelParseExecutor::class);
        if($fileInfo->file_size > ExcelParseExecutor::ASYNC_FILE_SIZE){
            $executorParams['download_way'] = ExcelParseExecutor::DOWNLOAD_WAY_OSSUTIL;
            $job->setExecutorParams($executorParams);
            QueueService::dispatch($job);
        }else{
            $executorParams['download_way'] = ExcelParseExecutor::DOWNLOAD_WAY_COMMON;
            $job->setExecutorParams($executorParams);
            $job->handle();
        }
        return $this->success($task->getAttributes($returnAttrs));
    }

    public function actionImportFieldMapping($taskId, $scene = 0, $params=[]){
        $this->initImportRead();
        $clientId = $this->getLoginUserClientId();
        $task = new Export($clientId, $taskId);

        if($task->isNew()){
            throw new \RuntimeException("不存在excel字段解析任务");
        }

        $result = [
            'prepared' => false,
            'data' => [],
            'success' => false,
            'msg' => '',
            'error' => '',
        ];

        if($task->status == ExportConstants::EXPORT_STATUS_ORIGIN || $task->status == ExportConstants::EXPORT_STATUS_RUNNING){
            return $this->success($result);
        }

        $result['prepared'] = true;
        if(
            $task->status == ExportConstants::EXPORT_STATUS_FAIL
        ){
            $result['error'] = $task->exception;
            $result['msg'] = \Yii::t('field',"Field from excel mapping failed");
            return $this->success($result);
        }

        // 任务成功的情况下
        $taskResult = json_decode($task->result, true);
        if(empty($taskResult) || !is_array($taskResult) || empty($taskResult['excel_data'])){
            $result['error'] = $task->exception;
            $result['msg'] = \Yii::t('field',"Header Field from excel empty");
            return $this->success($result);
        }

        $data = $taskResult['excel_data'];

        switch($this->importType){
            case \Constants::TYPE_PRODUCT:
                if($data['line'] <= 0){
                    $result['msg'] = $result['error'] = '产品数据为空';
                    return $this->success($result);
                }else{
                    $productImportApi = new ProductImportApi($clientId, $this->getLoginUserId());
                    $result['data'] = $productImportApi->defaultFieldMappingByData([$data['header']], $scene);
                }
            break;
            case \Constants::TYPE_ORDER:
                $result['data'] = Helper::importCheckFileByExcelData($data);
            break;
            case \Constants::TYPE_SUPPLIER:
                $result['data'] = \common\library\supplier\Helper::importCheckFileByExcelData($data);
            break;
            case \Constants::TYPE_SUPPLIER_PRODUCT:
                $result['data'] = \common\library\supplier\Helper::importProductCheckFileByExcelData($data
                );
            break;
        }

        $result['success'] = true;
        return $this->success($result);
    }

    public function actionImportTemplateList($scene = '')
    {
        //todo 后期接入 prometheus 管理导入模板
        $templateMap = [
            \Constants::TYPE_PRODUCT => [
                [
                    'key'       => 'import_spu',
                    'file_name'  => '产品导入标准模板.xlsx',
                    'language'  => \Constants::LANGUAGE_ZHCN,
                    'url'       => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/other/doc/55941995/ea5cea3141f6237c0293bd9e234bc3d28af5f6ac31a09fa194f1491a5eef3b4c.xlsx',
                ],
                [
                    'key'       => 'import_sku',
                    'file_name'  => '多规格产品导入标准模板.xlsx',
                    'language'  => \Constants::LANGUAGE_ZHCN,
                    'url'       => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/other/doc/55941995/345e4e6318f15ffd4de41bd6f0a9b181ba8c3d02335e8556137ab60d998c3e3b.xlsx',
                ],
                [
                    'key'       => 'import_combine',
                    'file_name'  => '组合产品导入标准模板.xlsx',
                    'language'  => \Constants::LANGUAGE_ZHCN,
                    'url'       => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/other/doc/55941995/3a0427c68a9d02ed611dfc1db22b51804d37e1fe0a80cc0597338dcc8d8b9686.xlsx',
                ],
                [
                    'key'       => 'import_spu',
                    'file_name'  => '產品導入標準模板.xlsx',
                    'language'  => \Constants::LANGUAGE_ZHTW,
                    'url'       => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/other/doc/55941995/657f1feaca1687120e049b26098353ef86e763ddbd86638593b1f8065d0537c9.xlsx',
                ],
                [
                    'key'       => 'import_sku',
                    'file_name'  => '多規格產品導入標準模板.xlsx',
                    'language'  => \Constants::LANGUAGE_ZHTW,
                    'url'       => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/other/doc/55941995/2ebff0ff2427921be6b1fb700fe8de88c218204389c6b1c253228a85ab9d5c19.xlsx',
                ],
                [
                    'key'       => 'import_combine',
                    'file_name'  => '組合產品導入標準模板.xlsx',
                    'language'  => \Constants::LANGUAGE_ZHTW,
                    'url'       => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/other/doc/55941995/45c5d21346f14de7d39fd23e1c9e9382644e3a23ea541d3ebdb3c87ad6e47bc2.xlsx',
                ],
                [
                    'key'       => 'import_spu',
                    'file_name'  => 'Product import template standard.xlsx',
                    'language'  => \Constants::LANGUAGE_EN,
                    'url'       => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/other/doc/55941995/71833ba0cc72763fbc2f27bd705d6ac1fa9e29782ddf5b95caef8ffa495a1989.xlsx',
                ],
                [
                    'key'       => 'import_sku',
                    'file_name'  => 'Multi-specification product import standard template.xlsx',
                    'language'  => \Constants::LANGUAGE_EN,
                    'url'       => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/other/doc/55941995/1914cac5a19fda7852954833d5a322b9b6a189b8a9bc55647f2056c0fe43e49d.xlsx',
                ],
                [
                    'key'       => 'import_combine',
                    'file_name'  => 'Combined Product Import Standard Template.xlsx',
                    'language'  => \Constants::LANGUAGE_EN,
                    'url'       => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/other/doc/55941995/188313057048704f8df1a9b2079a365acbe5ba3fa3f64d0a309851877ee35749.xlsx',
                ],
                [
                    'key'       => 'import_parts',
                    'file_name'  => '配件清单导入标准模板.xlsx',
                    'language'  => \Constants::LANGUAGE_ZHCN,
                    'url'       => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/others/9e22690f294df989f56cc698cf8b1e10cfcfb429139a2307aeae41792c6e2f11.xlsx',
                ],
                [
                    'key'       => 'import_parts',
                    'file_name'  => '配件清單導入標準範本.xlsx',
                    'language'  => \Constants::LANGUAGE_ZHTW,
                    'url'       => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/others/7d1778a8d48af0187b42a5787352d370699857f8a091c95b9e310e65dbeb09f2.xlsx',
                ],
                [
                    'key'       => 'import_parts',
                    'file_name'  => 'Accessories list import standard template.xlsx',
                    'language'  => \Constants::LANGUAGE_EN,
                    'url'       => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/others/b8f938feb1887678eb2f8a3723881fab45742adec4095ab26029d3534b395865.xlsx',
                ],
            ],
            \Constants::TYPE_SUPPLIER => [
                [
                    'key'       => 'import_supplier',
                    'file_name'  => '供应商导入标准模板.xlsx',
                    'language'  => \Constants::LANGUAGE_ZHCN,
                    'url'       => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/other/doc/55760752/c3b12e58298f3cba42ac27b299ad5accde857bd954cb11e71eaa53077669f19c.xlsx',
                ],
                [
                    'key'       => 'import_supplier',
                    'file_name'  => '供应商导入标准模板.xlsx',
                    'language'  => \Constants::LANGUAGE_ZHTW,
                    'url'       => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/other/doc/55760752/c3b12e58298f3cba42ac27b299ad5accde857bd954cb11e71eaa53077669f19c.xlsx',
                ],
                [
                    'key'       => 'import_supplier',
                    'file_name'  => '供应商导入标准模板.xlsx',
                    'language'  => \Constants::LANGUAGE_EN,
                    'url'       => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/other/doc/55760752/c3b12e58298f3cba42ac27b299ad5accde857bd954cb11e71eaa53077669f19c.xlsx',
                ],
            ],
            \Constants::TYPE_SUPPLIER_PRODUCT => [
                [
                    'key'       => 'import_supplier_product',
                    'file_name'  => '供应商产品导入标准模板.xlsx',
                    'language'  => \Constants::LANGUAGE_ZHCN,
                    'url'       => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/others/fdaa8eb501f543269eaef6e311931bd72ad9a99ce83bbc314ca03c6a60113c83.xlsx',
                ],
                [
                    'key'       => 'import_supplier_product',
                    'file_name'  => '供应商产品导入标准模板.xlsx',
                    'language'  => \Constants::LANGUAGE_ZHTW,
                    'url'       => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/others/fdaa8eb501f543269eaef6e311931bd72ad9a99ce83bbc314ca03c6a60113c83.xlsx',
                ],
                [
                    'key'       => 'import_supplier_product',
                    'file_name'  => '供应商产品导入标准模板.xlsx',
                    'language'  => \Constants::LANGUAGE_EN,
                    'url'       => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/others/fdaa8eb501f543269eaef6e311931bd72ad9a99ce83bbc314ca03c6a60113c83.xlsx',
                ],
            ],
            \Constants::TYPE_PRODUCT_INVENTORY_WARNING =>
                [
                    [
                        'key' => 'import_product_inventory_warning',
                        'file_name' => '库存预警批量导入模板.xlsx',
                        'language' => \Constants::LANGUAGE_ZHCN,
                        'url' => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/other/doc/55941995/03f5e63063f2265bf8da7d1261448261d12000345bff096736ec2b5238342d17.xls',
                    ],
                    [
                        'key' => 'import_product_inventory_warning',
                        'file_name' => '庫存預警批量導入模板.xlsx',
                        'language' => \Constants::LANGUAGE_ZHTW,
                        'url' => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/other/doc/55941995/56763b8e3d6328a0efd99a1d44d8d52dd781f914ae3bcd23a7a4688d89a64754.xls',
                    ],
                    [
                        'key' => 'import_product_inventory_warning',
                        'file_name' => 'Inventory alert template standard.xlsx',
                        'language' => \Constants::LANGUAGE_EN,
                        'url' => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/other/doc/55941995/af74ca7435a4e957ef442456269ad3ef5fe20d2732c164fdeb5b6825b36e9aa8.xls',
                    ],
                ]
        ];

        //存在对象不需要定义 且 需要导入 如分组 直接用import_type定义
        $subTemplate = [
            \Constants::TYPE_PRODUCT => [ImportConstants::PRODUCT_GROUP_IMPORT =>
                [
                    [
                        'key' => 'import_product_group',
                        'file_name' => '产品分组导入模板.xlsx',
                        'language' => \Constants::LANGUAGE_ZHCN,
                        'url' => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/other/doc/55941995/6183491a0b0ee4ded7f96e3c89eff1a5b89c35998321a92da1c1c37c6e5b9e88.xlsx',
                    ],
                    [
                        'key' => 'import_product_group',
                        'file_name' => '產品分組匯入模板.xlsx',
                        'language' => \Constants::LANGUAGE_ZHTW,
                        'url' => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/other/doc/55941995/f2d2dd3f7c4f2f2349f65734339cc75493a746f1349cf2d22a1522348bbaef60.xlsx',
                    ],
                    [
                        'key' => 'import_product_group',
                        'file_name' => 'Product group import template.xlsx',
                        'language' => \Constants::LANGUAGE_EN,
                        'url' => 'https://jinyuncrmdevelop.oss-cn-hangzhou.aliyuncs.com/other/doc/55941995/84147bb6246c6c798e54027fa06a0946f067d78f110a1d882cfddd55351531d2.xlsx',
                    ],
                ]
            ]
        ];
        $this->initImportRead();
        $result = $templateMap[$this->importType] ?? [];

        if (!empty($scene)) {
            $result = $subTemplate[$this->importType][$scene] ?? [];
        }

        return $this->success($result);
    }

    public function actionImportCheckFile($file_id)
    {
        $this->validate([
            'file_id' => 'required|integer',
        ]);

        //通过file_id获取文件
        $file = new AliyunUpload();
        $file->loadByFileId($file_id, $this->getLoginUserId());
        $data = $file->getFileData();
        if (empty($data)) {
            throw new RuntimeException(\Yii::t('file', 'File no data'));
        }

        $excelData = [
            'header' => $data[0] ?? [],
            'first_line' => $data[1] ?? [],
            'line' => max(count($data) - 1, 0),
        ];
        $returnData = Helper::importCheckFileByExcelData($excelData);
        $this->success($returnData);
    }

    public function actionImportCheckFileNew($file_id)
    {
        $this->validate([
            'file_id' => 'required|integer',
        ]);

        //通过file_id获取文件
        $file = new AliyunUpload();
        $file->loadByFileId($file_id, $this->getLoginUserId());
        list($data,$existEmbedImage) = $file->getFileDataAndImg(uniqid(),$file_id,true,2);
        if (empty($data)) {
            throw new RuntimeException(\Yii::t('file', 'File no data'));
        }
        $tableHead = $data[0] ?? [];
        if (empty($tableHead)) {
            return $this->fail(ErrorCode::CODE_TEMPLATE_HEADER_EMPTY, "表头不能为空", []);
        }
        $api = new OrderProductImportTemplateApi( $this->getLoginUserInfo()->getClientId(), $this->productImportModule,
            ItemSettingConstant::ITEM_TYPE_ORDER_PRODUCT_TEMPLATE, $this->getLoginUserId());
        $res = $api->checkExtraDataIsSave( $this->getLoginUserId(), $this->productImportTplKey, $tableHead);
        $excelData = [
            'header' => $data[0] ?? [],
            'first_line' => $data[1] ?? [],
            'line' => max(count($data) - 1, 0),
            'existEmbedImage' =>$existEmbedImage,
        ];
        $returnData = Helper::importCheckFileByExcelData($excelData);
        return $this->success(array_merge($returnData,$res));
    }
}
