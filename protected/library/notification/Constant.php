<?php

/**
 * This file is part of xiaoman-crm.
 *
 * Copyright © 2012 - 2020 Xiaoman. All Rights Reserved.
 *
 * Created by <PERSON><PERSON><PERSON><PERSON> <she<PERSON><PERSON><PERSON>@xiaoman.cn>, on 2020/03/08.
 */

namespace common\library\notification;

use common\library\ames\AmesNotification;
use common\library\approval\ApprovalService;
use common\library\approval_flow\Constants;
use common\library\dingding\Helper;
use common\library\push\Browser;

class Constant
{
    const MAIL_UNDISTURBED_END_TIME = '08:20:00';
    const MAIL_UNDISTURBED_START_TIME = '00:00:00';

    const APPROVAL_OPERATION_APPLY = Constants::STATUS_PENDING;
    const APPROVAL_OPERATION_PASS = Constants::STATUS_PASSED;
    const APPROVAL_OPERATION_REJECT = Constants::STATUS_REJECTED;
    const APPROVAL_OPERATION_CANCEL = Constants::STATUS_CANCEL;
    const APPROVAL_OPERATION_TRANSFER = 0;

    const APP_TYPE_MAIL = 'mail';
    const APP_TYPE_SCHEDULE = 'schedule';
    const APP_TYPE_APPROVAL = 'approval';
    const APP_TYPE_MAIL_TODO = 'mail_todo';
    const APP_TYPE_ORDER = 'order';
    const APP_TYPE_INQUIRY= 'inquiry';
    const APP_TYPE_NOTIFICATION = 'notification';     // 系统消息（小喇叭）
    const APP_TYPE_WEEKLY_STATISTIC = 'weekly_statistic';
    const APP_TYPE_REPORT_STATISTIC = 'report';
    const APP_TYPE_WORK_JOURNAL_COMMIT = 'work_journal_commit';
    const APP_TYPE_WORK_JOURNAL_REMIND = 'work_journal_remind';
    const APP_TYPE_WORK_JOURNAL_COMMENT = 'work_journal_comment';
    const BADGE_COUNT_PRE_KEY = 'app_push_badge_count_';
    const APP_TYPE_AI_DECISION_MANAGER = 'ai_decision_manager'; // ai决策管家
    const APP_TYPE_TASK_CENTER = 'task_center'; //任务中心

    const MESSAGE_MAIL_TYPE_NOTIFY = 'notify';
    const MESSAGE_MAIL_TYPE_DEV = 'dev';

    //日程相关消息通知
    const NOTIFICATION_TYPE_SCHEDULE_NEW = 101; //日程邀请通知
    const NOTIFICATION_TYPE_SCHEDULE_DEL = 102; //日程移除通知
    const NOTIFICATION_TYPE_SCHEDULE_EDIT = 103; //日程修改通知
    const NOTIFICATION_TYPE_SCHEDULE_COMPLETE = 104; //日程完成通知
    const NOTIFICATION_TYPE_SCHEDULE_COMMENT = 105; //日程评论通知
    const NOTIFICATION_TYPE_SCHEDULE_EDIT_COMMENT = 106; //日程修改评论通知

    // 线索相关消息通知
    const NOTIFICATION_TYPE_LEAD_FOLLOW_EXPORT = 201; //线索报表导出
    const NOTIFICATION_TYPE_LEAD_IMPORT = 202;//线索导入
    const NOTIFICATION_TYPE_LEAD_BATCH_TRANSFORM = 203; // 批量转化线索
    const NOTIFICATION_TYPE_LEAD_TRANSFER = 204;// 转移线索通知
    const NOTIFICATION_TYPE_LEAD_CANCEL = 205;// 取消跟进线索通知
    const NOTIFICATION_TYPE_LEAD_MERGE = 206; //线索合并
    const NOTIFICATION_TYPE_FAVORITE_LIST_TRANSFER_LEAD = 207; //收藏列表转化线索

    // 客户相关消息通知
    const NOTIFICATION_TYPE_CUSTOMER_EXPORT = 301; //客户资料导出完成通知
    const NOTIFICATION_TYPE_CUSTOMER_TRAIL_EXPORT = 302; // 客户动态跟进情况导出完成通知
    const NOTIFICATION_TYPE_CUSTOMER_IMPORT = 303;//客户导入
    const NOTIFICATION_TYPE_CUSTOMER_MOVE_PUBLIC = 304;// 客户三天移入公海
    const NOTIFICATION_TYPE_CUSTOMER_SHARE =305; //客户分享
    const NOTIFICATION_TYPE_CUSTOMER_MAIL_CONFLICT = 306; //邮件撞单
    const NOTIFICATION_TYPE_CUSTOMER_TRANSFER = 307;//转移客户通知
    const NOTIFICATION_TYPE_CUSTOMER_CANCEL = 308;//客户取消跟进通知
    const NOTIFICATION_TYPE_CUSTOMER_MERGE = 309;// 客户合并通知
    const NOTIFICATION_TYPE_CUSTOMER_ALIBABA_SYNC = 312;// 阿里客户通同步通知
	const NOTIFICATION_TYPE_CUSTOMER_IMPORT_BY_FU_TONG = 313;//富通客户导入
    const NOTIFICATION_TYPE_BUSINESS_TARGET_EXPORT = 314;//业务指标导出
    const NOTIFICATION_TYPE_CUSTOMER_TRAIL_COMMENT = 315;//动态评论通知
    const NOTIFICATION_TYPE_PUBLIC_CUSTOMER_ASSIGN = 316;//公海客户重新分配通知
    const NOTIFICATION_TYPE_FAVORITE_LIST_TRANSFER_CUSTOMER = 317; // 收藏列表转换客户通知
    const NOTIFICATION_TYP_CUSTOMER_DETECT_CONFLICT = 318;//客户判重异常检测结果通知
    const NOTIFICATION_TYPE_CUSTOMER_LIMIT_APPROACHING_MAXIMUM_REMINDER = 319;//即将达到保有客户上限提醒
    const NOTIFICATION_TYPE_CUSTOMER_LIMIT_REACHED_NOTIFY = 320;//已达保有客户上限通知
    const NOTIFICATION_TYPE_AI_CLASSIFY_NEW_LEAD_MERGE = 321;
    const NOTIFICATION_TYPE_AI_CLASSIFY_NEW_COMPANY_MERGE = 322;

    // 商机相关消息通知
    const NOTIFICATION_TYPE_OPPORTUNITY_EXPORT = 401; // 商机导出完成通知
    const NOTIFICATION_TYPE_OPPORTUNITY_TRAIL_COMMENT = 402; // 商机动态评论通知
    const NOTIFICATION_TYPE_OPPORTUNITY_IMPORT = 403; // 商机导入完成通知

    // 交易相关通知
    const NOTIFICATION_TYPE_ORDER_HANDLER_ADD = 501;
    const NOTIFICATION_TYPE_ORDER_HANDLER_REMOVE = 502;
    const NOTIFICATION_TYPE_ORDER_DELETE = 503;
    const NOTIFICATION_TYPE_QUOTATION_DELETE = 504;
    const NOTIFICATION_TYPE_PAYPAL_RECEIPT = 505;//paypal收款通知
    const NOTIFICATION_TYPE_PRODUCT_IMPORT = 506;// 产品导入

    const NOTIFICATION_TYPE_ORDER_ALIBABA_SYNC = 511;// 阿里订单同步通知
    const NOTIFICATION_TYPE_ORDER_IMPORT = 512;// 销售订单导入
    const NOTIFICATION_TYPE_ORDER_IMPORT_ROLLBACK = 513;// 销售订单导入撤销
    const NOTIFICATION_TYPE_SUPPLIER_IMPORT = 514;// 供应商导入
    const NOTIFICATION_TYPE_SUPPLIER_PRODUCT_IMPORT = 515;// 供应商产品导入

    // ai
    const NOTIFICATION_TYPE_CUSTOMER_ADVICE_ARCHIVE = 601;
    const NOTIFICATION_TYPE_AI_PRODUCT = 602;
    const NOTIFICATION_TYPE_AI_ADVICE = 603; //AI建档建议-每周新增建档建议. 这里只是兼容性处理，这种类型并没有对应消息盒子中的类型

    // 审批流相关消息通知
    const NOTIFICATION_TYPE_APPROVALFLOW_NOTIFY = 701;
    const NOTIFICATION_TYPE_APPROVALFLOW_EMAIL_NOTIFY = 702;//这里只是兼容性处理，这种类型并没有对应消息盒子中的类型
    const NOTIFICATION_TYPE_APPROVALFLOWCONFIG_CLOSE_NOTIFY = 703;//审批流停用
    const NOTIFICATION_TYPE_APPROVALFLOW_RESULE_NOTITY = 704;//审批流结果通知
    const NOTIFICATION_TYPE_APPROVALFLOW_TODO = 705;//待处理审批流通知

    // 工作流相关消息通知
    const NOTIFICATION_TYPE_WORKFLOW_EMAIL_NOTIFY = 801;//工作流提醒邮件，这里只是兼容性处理，这种类型并没有对应消息盒子中的类型
    const NOTIFICATION_TYPE_WORKFLOW_NOTIFY = 802;//工作流提醒
    const NOTIFICATION_TYPE_WORKFLOW_ASSIGN_NOTIFY = 803;//人员分配通知
    const NOTIFICATION_TYPE_WORKFLOW_BE_ASSIGNED_NOTIFY = 804;//自动分配通知

    // 系统消息
    const NOTIFICATION_TYPE_DISK_SPACE_WILL_OVER_LIMIT = 901; //云空间超量通知
    const NOTIFICATION_TYPE_SYSTEM_NOTICE = 902;//系统（升级，维护）通知
    const NOTIFICATION_TYPE_ALIBABA_STORE_AUTH = 903;//阿里店铺授权完成通知
    const NOTIFICATION_TYPE_WORKFLOW_SYSTEM_NOTIFY = 904;//工作流系统通知
    const NOTIFICATION_TYPE_ALIBABA_STORE_EXPIRED_PROCESS = 905;//阿里店铺授权即将过期通知
    const NOTIFICATION_TYPE_ALIBABA_STORE_EXPIRED = 906;//阿里店铺授权已失效通知
    const NOTIFICATION_TYPE_THIRD_PRODUCT_MATCH_OVER = 907;//平台产品匹配结束
    const NOTIFICATION_TYPE_OSS_SYSTEM_NOTICE = 908;//OS、灯塔相关系统通知
    const NOTIFICATION_TYPE_OSS_DEFAULT_NOTICE = 909;//OS、灯塔常用系统通知
    const NOTIFICATION_TYPE_CUSTOMER_MILESTONE_NOTICE = 910; // 客户成长里程碑通知

    const NOTIFICATION_TYPE_THIRD_PRODUCT_GEN_OVER = 911; // 平台产品生成结束

    const NOTIFICATION_TYPE_OKKI_AI_QUALITY_TASK_OVER = 912; // OKKI AI沟通质检任务完成

    // 工作报告
    const NOTIFICATION_TYPE_WORK_REPORT_DAILY = 1001; //每日工作报告
    const NOTIFICATION_TYPE_WORK_REPORT_WEEK = 1002; //每周工作报告
    const NOTIFICATION_TYPE_WORK_REPORT_MONTH = 1003; //工作月报


    //任务中心
    const NOTIFICATION_TYPE_PURCHASE_PRODUCT_TRANSFER = 1101; //采购任务通知
    const NOTIFICATION_TYPE_INBOUND_PRODUCT_TRANSFER = 1102; //入库任务通知
    const NOTIFICATION_TYPE_OUTBOUND_PRODUCT_TRANSFER = 1103; //出库任务通知
    const NOTIFICATION_TYPE_OTHER_TRANSFER = 1104; //单据任务通知
    const NOTIFICATION_TYPE_CREATE_PRODUCT_TRANSFER = 1110; //新任务
    const NOTIFICATION_TYPE_CHANGE_HANDLER_PRODUCT_TRANSFER = 1111; //变更处理人
    const NOTIFICATION_TYPE_REMOVE_PRODUCT_TRANSFER = 1112; //任务已删除
    const NOTIFICATION_TYPE_CHANGE_STATUS_PRODUCT_TRANSFER = 1113; //任务状态变更
    const NOTIFICATION_TYPE_CHANGE_CONTENT_PRODUCT_TRANSFER = 1114; //任务内容更新
    const NOTIFICATION_TYPE_CHANGE_PROGRESS_PRODUCT_TRANSFER = 1115; //任务进度变更
    const NOTIFICATION_TYPE_UPSTREAM_INVOICE_PRODUCT_TRANSFER = 1116; //上游单据变更
    const NOTIFICATION_TYPE_WAIT_HANDLER_PRODUCT_TRANSFER = 1117; //任务待处理
    const NOTIFICATION_TYPE_COMMENT_PRODUCT_TRANSFER = 1118; //任务评论
    const NOTIFICATION_TYPE_FEEDBACK_PRODUCT_TRANSFER = 1119; //询价协同反馈 目前询价协同其他通知都是用任务的，所以这里常量命名也是类似
    const NOTIFICATION_TYPE_PAYMENT_INVOICE = 1130; //付款单通知
    const NOTIFICATION_TYPE_COST_INVOICE = 1140; //费用单通知
    const NOTIFICATION_TYPE_PRODUCT_INVENTORY_WARNING = 1150; //库存消息通知
    // 任务延时通知类型
    const NOTICE_ACTION_TYPE_PRODUCT_TRANSFER_UNFINISH = 1;  //任务未完成通知

    const NOTIFICATION_TYPE_TMS_TRADE_DOCUMENT_FEEDBACK = 1201; // tms外贸文档快速反馈
    const NOTIFICATION_TYPE_TMS_TRADE_DOCUMENT_MESSAGE = 1202; // tms外贸文档留言

    const NOTIFICATION_TYPE_FACEBOOK_PAGE_EXPIRE = 1301; // facebook页面失效通知
    const NOTIFICATION_TYPE_FACEBOOK_PAGE_ASSIGN = 1302; // facebook页面分配通知
    const NOTIFICATION_TYPE_FACEBOOK_PAGE_REMOVE_ASSIGN = 1303; // facebook页面取消分配通知
    const NOTIFICATION_TYPE_FACEBOOK_CREATE_LEAD_FORM = 1304; // facebook创建线索表单通知

    const NOTIFICATION_TYPE_INSTAGRAM_ACCOUNT_EXPIRE = 1305; // instagram账号失效通知
    const NOTIFICATION_TYPE_INSTAGRAM_ACCOUNT_ASSIGN = 1306; // instagram账号分配通知
    const NOTIFICATION_TYPE_INSTAGRAM_ACCOUNT_REMOVE_ASSIGN = 1307; // instagram账号取消分配通知

    // ERP服务
    const NOTIFICATION_TYPE_ERP_SERVICE_AUTH_SUCCESS = 1401;
    const NOTIFICATION_TYPE_ERP_SERVICE_AUTH_FAILED = 1402;
    const NOTIFICATION_TYPE_ERP_SERVICE_SYNC_FINISH = 1403;
    const NOTIFICATION_TYPE_ERP_SERVICE_SYNC_ORDER = 1404;

    const NOTIFICATION_TYPE_WABA_TEMPLATE_APPROVED = 1501; // waba模板审核通过
    const NOTIFICATION_TYPE_WABA_TEMPLATE_REJECTED = 1502; // waba模板审核中
    const NOTIFICATION_TYPE_WABA_ACCOUNT_APPROVED = 1503; // waba账号审核通过
    const NOTIFICATION_TYPE_WABA_ACCOUNT_REJECTED = 1504; // waba账号审核通过

    // 工作报告
    const NOTIFICATION_TYPE_WORK_JOURNAL_COMMIT = 1601; //工作报告提交
    const NOTIFICATION_TYPE_WORK_JOURNAL_REMIND = 1602; // 工作报告提醒
    const NOTIFICATION_TYPE_WORK_JOURNAL_COMMENT = 1603; // 工作报告评论提醒

    const NOTIFICATION_TYPE_ALI_MAIL_ORGANIZATION_CHECK = 1701; // 组织检测状态提醒

    // 钉钉通知
    const NOTIFICATION_TYPE_DINGTALK_APPROVAL_FORM_TODO = 1801; // 钉钉待处理审批通知
    const NOTIFICATION_TYPE_DINGTALK_APPROVAL_FROM_RESULT = 1802; // 钉钉审批结果通知
    const NOTIFICATION_TYPE_DINGTALK_APPROVAL_FLOW_STOP = 1803; // 钉钉审批流停用通知

    // @人协同沟通
    const NOTIFICATION_TYPE_AT_REMARK = 1901; // 跟进动态及评论
    const NOTIFICATION_TYPE_AT_PRODUCT_TRANSFER = 1902; // 任务动态及评论
    const NOTIFICATION_TYPE_AT_WORK_JOURNAL = 1903; // 工作报告评论
    const NOTIFICATION_TYPE_AT_SCHEDULE = 1904; // 日程评论

    // 消息列表左侧tab常量
    const MODULE_ALL = 'all';
    // 关注和未关注的module是根据userSetting算出
    const MODULE_PIN = 'pin';
    const MODULE_NOT_PIN = 'not_pin';
    const MODULE_AT = 'at'; // @我的消息，与关注、未关注并列
    const MODULE_CUSTOMER = 'customer';
    const MODULE_LEAD = 'lead';
    const MODULE_OPPORTUNITY = 'opportunity';
    const MODULE_INVOICE = 'invoice';
    const MODULE_AI = 'ai';
    const MODULE_WORKFLOW = 'workflow';
    const MODULE_APPROVAL = 'approval';
    const MODULE_SCHEDULE = 'schedule';
    const MODULE_SYSTEM = 'system';
    const MODULE_REPORT = 'report';
    const MODULE_PRODUCT_TRANSFER = 'product_transfer';
    const MODULE_PAYMENT_INVOICE = 'payment_invoice';
    const MODULE_COST_INVOICE = 'cost_invoice';
    const MODULE_TMS_TRADE_DOCUMENT = 'tms_trade_document';
    const MODULE_SOCIAL_MEDIA = 'social-media';
    const MODULE_WHATSAPP_BUSINESS = 'whatsapp_business';
    const MODULE_WHATSAPP_CLOUD = 'whatsapp_cloud';
    const MODULE_WORK_JOURNAL = 'work_journal'; //WEB端使用
    const MODULE_WORK_JOURNAL_COMMIT = 'work_journal_commit';
    const MODULE_WORK_JOURNAL_COMMENT = 'work_journal_comment';
    const MODULE_WORK_JOURNAL_REMIND = 'work_journal_remind';
    const MODULE_ORDER_PROFIT = 'order_profit';
    const MODULE_PRODUCT_INVENTORY_WARNING = 'product_inventory_warning';


    const MODULE_LIST = [
        self::MODULE_ALL,
        self::MODULE_PIN,
        self::MODULE_NOT_PIN,
        self::MODULE_AT,
        self::MODULE_CUSTOMER,
        self::MODULE_LEAD,
        self::MODULE_OPPORTUNITY,
        self::MODULE_INVOICE,
        self::MODULE_PRODUCT_TRANSFER,
        self::MODULE_AI,
        self::MODULE_WORKFLOW,
        self::MODULE_APPROVAL,
        self::MODULE_SCHEDULE,
        self::MODULE_SYSTEM,
        self::MODULE_TMS_TRADE_DOCUMENT,
        self::MODULE_SOCIAL_MEDIA,
    ];

    const  BROWSER_TYPE_MAP = [
        self::NOTIFICATION_TYPE_FACEBOOK_PAGE_EXPIRE => Browser::TYPE_FACEBOOK_PAGE_EXPIRE,
        self::NOTIFICATION_TYPE_FACEBOOK_PAGE_ASSIGN => Browser::TYPE_FACEBOOK_PAGE_ASSIGN,
        self::NOTIFICATION_TYPE_FACEBOOK_PAGE_REMOVE_ASSIGN => Browser::TYPE_FACEBOOK_PAGE_REMOVE_ASSIGN,
        self::NOTIFICATION_TYPE_FACEBOOK_CREATE_LEAD_FORM => Browser::TYPE_FACEBOOK_CREATE_LEAD_FORM,
        self::NOTIFICATION_TYPE_INSTAGRAM_ACCOUNT_EXPIRE => Browser::TYPE_INSTAGRAM_ACCOUNT_EXPIRE,
        self::NOTIFICATION_TYPE_INSTAGRAM_ACCOUNT_ASSIGN => Browser::TYPE_INSTAGRAM_ACCOUNT_ASSIGN,
        self::NOTIFICATION_TYPE_INSTAGRAM_ACCOUNT_REMOVE_ASSIGN => Browser::TYPE_INSTAGRAM_ACCOUNT_REMOVE_ASSIGN,
        self::NOTIFICATION_TYPE_ALIBABA_STORE_EXPIRED_PROCESS => Browser::TYPE_ALIBABA_STORE_EXPIRED_PROCESS,
        self::NOTIFICATION_TYPE_ALIBABA_STORE_EXPIRED => Browser::TYPE_ALIBABA_STORE_EXPIRED,
        self::NOTIFICATION_TYPE_BUSINESS_TARGET_EXPORT => Browser::TYPE_BUSINESS_TARGET_EXPORT,
        self::NOTIFICATION_TYPE_WABA_TEMPLATE_APPROVED => Browser::TYPE_WABA_TEMPLATE_APPROVED,
        self::NOTIFICATION_TYPE_WABA_TEMPLATE_REJECTED => Browser::TYPE_WABA_TEMPLATE_REJECTED,
        self::NOTIFICATION_TYPE_WABA_ACCOUNT_APPROVED => Browser::TYPE_WABA_ACCOUNT_APPROVED,
        self::NOTIFICATION_TYPE_WABA_ACCOUNT_REJECTED => Browser::TYPE_WABA_ACCOUNT_REJECTED,
        self::NOTIFICATION_TYPE_AT_REMARK => Browser::TYPE_AT_REMARK,
        self::NOTIFICATION_TYPE_AT_PRODUCT_TRANSFER => Browser::TYPE_AT_PRODUCT_TRANSFER,
        self::NOTIFICATION_TYPE_AT_WORK_JOURNAL => Browser::TYPE_AT_WORK_JOURNAL,
        self::NOTIFICATION_TYPE_AT_SCHEDULE => Browser::TYPE_AT_SCHEDULE,
    ];

    const APP_EXCLUDE_TYPE = [
        self::NOTIFICATION_TYPE_LEAD_FOLLOW_EXPORT,
        self::NOTIFICATION_TYPE_LEAD_IMPORT,
        self::NOTIFICATION_TYPE_LEAD_BATCH_TRANSFORM,
        self::NOTIFICATION_TYPE_LEAD_CANCEL,
        self::NOTIFICATION_TYPE_LEAD_MERGE,
        self::NOTIFICATION_TYPE_CUSTOMER_EXPORT,
        self::NOTIFICATION_TYPE_CUSTOMER_TRAIL_EXPORT,
        self::NOTIFICATION_TYPE_CUSTOMER_IMPORT,
        self::NOTIFICATION_TYPE_OPPORTUNITY_EXPORT,
        self::NOTIFICATION_TYPE_CUSTOMER_ADVICE_ARCHIVE,
        self::NOTIFICATION_TYPE_WORK_REPORT_DAILY,
        self::NOTIFICATION_TYPE_WORK_REPORT_WEEK,
        self::NOTIFICATION_TYPE_WORK_REPORT_MONTH,
        self::NOTIFICATION_TYPE_CUSTOMER_IMPORT_BY_FU_TONG,
//        self::NOTIFICATION_TYPE_CUSTOMER_TRAIL_COMMENT,
        self::NOTIFICATION_TYPE_OSS_SYSTEM_NOTICE,
        self::NOTIFICATION_TYPE_CUSTOMER_MILESTONE_NOTICE,
        self::NOTIFICATION_TYPE_BUSINESS_TARGET_EXPORT,
        self::NOTIFICATION_TYPE_PUBLIC_CUSTOMER_ASSIGN,
        self::NOTIFICATION_TYP_CUSTOMER_DETECT_CONFLICT,
//        self::NOTIFICATION_TYPE_OPPORTUNITY_TRAIL_COMMENT,
        self::NOTIFICATION_TYPE_FAVORITE_LIST_TRANSFER_LEAD,
        self::NOTIFICATION_TYPE_FAVORITE_LIST_TRANSFER_CUSTOMER,
        self::NOTIFICATION_TYPE_AT_WORK_JOURNAL,
    ];

    // 申请单
    const APP_REFER_TYPE_APPLY = 'apply';
    const APP_REFER_TYPE_APPROVAL = 'approval';
    const APP_REFER_TYPE_CUSTOMER = 'customer';
    const APP_REFER_TYPE_LEAD = 'lead';
    const APP_REFER_TYPE_OPPORTUNITY = 'opportunity';
    const APP_REFER_TYPE_SCHEDULE = 'schedule';

    const APP_MODULE_DAILY_STATISTIC = 'daily_statistic';
    const APP_MODULE_WEEKLY_STATISTIC = 'weekly_statistic';
    const APP_MODULE_MONTHLY_STATISTIC = 'monthly_statistic';
    const APP_MODULE_PRODUCT_UPDATE = 'product_update';
    const APP_MODULE_WORK_JOURNAL_COMMIT = 'work_journal_commit';
    const APP_MODULE_WORK_JOURNAL_REMIND = 'work_journal_remind';
    const APP_MODULE_WORK_JOURNAL_COMMENT = 'work_journal_comment';
    const APP_MODULE_AI_DECISION_MANAGER = 'ai_decision_manager'; // AI决策管家
    const APP_MODEULE_AI_TEAM_ANALYSIS = 'ai_team_analysis';
    const APP_MODULE_TASK_CENTER_INQUIRY_COLLABORATION = 'task_center_inquiry_collaboration'; //询价任务

    // app那边会注册下面这些channel进行特殊处理，否则都归纳到other的channel里面，
    // channel类似模块里面的总通道，里面有各种类型的消息
    const APP_PUSH_CHANNEL_ID_MAIL = 'mail';
    const APP_PUSH_CHANNEL_ID_CUSTOMER = 'customer';
    const APP_PUSH_CHANNEL_ID_APPROVAL = 'approval';
    const APP_PUSH_CHANNEL_ID_SCHEDULE = 'schedule';
    const APP_PUSH_CHANNEL_ID_OTHER = 'other';
    const APP_PUSH_CHANNEL_ID_REPORT = 'report';
    const APP_PUSH_CHANNEL_ID_WORK_JOURNAL = 'work_journal';
    const APP_PUSH_CHANNEL_ID_AI_DECISION_MANAGER = 'decision_manager';

    const APP_PUSH_CHANNEL_ID_AI_TEAM_ANALYSIS = 'ai_team_analysis';

    // 下面 work_journal_commit 、work_journal_remind 需要回归到同一个channel：work_journal中
    const APP_PUSH_CHANNEL_ID_WORK_JOURNAL_COMMIT = 'work_journal_commit';
    const APP_PUSH_CHANNEL_ID_WORK_JOURNAL_REMIND = 'work_journal_remind';

    const APP_PUSH_CHANNEL_ID_TASK_CENTER = 'task_center'; // 任务中心

    const APP_PUSH_CHANNEL_ID_MAP = [
        self::MODULE_CUSTOMER => self::APP_PUSH_CHANNEL_ID_CUSTOMER,
        self::MODULE_LEAD => self::APP_PUSH_CHANNEL_ID_OTHER,
        self::MODULE_OPPORTUNITY => self::APP_PUSH_CHANNEL_ID_OTHER,
        self::MODULE_INVOICE => self::APP_PUSH_CHANNEL_ID_OTHER,
        self::MODULE_AI => self::APP_PUSH_CHANNEL_ID_OTHER,
        self::MODULE_WORKFLOW => self::APP_PUSH_CHANNEL_ID_OTHER,
        self::MODULE_APPROVAL => self::APP_PUSH_CHANNEL_ID_APPROVAL,
        self::MODULE_SCHEDULE => self::APP_PUSH_CHANNEL_ID_SCHEDULE,
        self::MODULE_SYSTEM => self::APP_PUSH_CHANNEL_ID_OTHER,
        self::MODULE_REPORT => self::APP_PUSH_CHANNEL_ID_REPORT,
        self::MODULE_WORK_JOURNAL_COMMIT => self::APP_PUSH_CHANNEL_ID_WORK_JOURNAL,
        self::MODULE_WORK_JOURNAL_REMIND => self::APP_PUSH_CHANNEL_ID_WORK_JOURNAL,
        self::MODULE_WORK_JOURNAL_COMMENT => self::APP_PUSH_CHANNEL_ID_WORK_JOURNAL,
        self::APP_MODULE_AI_DECISION_MANAGER => self::APP_PUSH_CHANNEL_ID_AI_DECISION_MANAGER
    ];

    const NOTIFICATION_TYPE_CONFIG = [
        self::NOTIFICATION_TYPE_WORKFLOW_EMAIL_NOTIFY => [
            'title' => '工作流提醒邮件',
            'template_name' => 'workflowEmailNotify',
            'init_data_function' => 'initWorkflowEmailNotifyData',
        ],
        self::NOTIFICATION_TYPE_APPROVALFLOW_EMAIL_NOTIFY => [
            'title' => '审批流提醒邮件',
            'template_name' => 'approvalflowEmailNotify',
            'init_data_function' => 'initWorkflowEmailNotifyData',
        ],
        self::NOTIFICATION_TYPE_LEAD_FOLLOW_EXPORT => [
            'title' => '线索报表生成通知',
            'init_data_function' => 'initExportTemplateData',
            'module' => self::MODULE_LEAD,
        ],
        self::NOTIFICATION_TYPE_LEAD_IMPORT => [
            'title' => '线索导入结果通知',
            'init_data_function' => 'initCustomerImportData',
            'module' => self::MODULE_LEAD,
        ],
        self::NOTIFICATION_TYPE_LEAD_BATCH_TRANSFORM => [
            'title' => '批量转化线索完成通知',
            'init_data_function' => 'initBatchTransformData',
            'module' => self::MODULE_LEAD,
        ],
        self::NOTIFICATION_TYPE_FAVORITE_LIST_TRANSFER_LEAD => [
            'title' => '批量新建线索完成通知',
            'init_data_function' => 'initCustomerImportData',
            'module' => self::MODULE_LEAD,
        ],
        self::NOTIFICATION_TYPE_LEAD_CANCEL => [
            'title' => '取消跟进线索通知',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_LEAD,
        ],
        self::NOTIFICATION_TYPE_LEAD_TRANSFER => [
            'title' => '转移线索通知',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_LEAD,
        ],
        self::NOTIFICATION_TYPE_LEAD_MERGE => [
            'title' => '合并线索通知',
            'init_data_function' => 'initLeadMergeData',
            'module' => self::MODULE_LEAD,
        ],
        self::NOTIFICATION_TYPE_CUSTOMER_MAIL_CONFLICT => [
            'title' => '邮件撞单提醒',
            'init_data_function' => 'initMailConflict',
            'render_dingtalk_function' => [Helper::class, 'renderMailConflict'],
            'module' => self::MODULE_CUSTOMER,
        ],
        self::NOTIFICATION_TYPE_CUSTOMER_MOVE_PUBLIC => [
            'title' => '即将移入公海提醒',
            'init_data_function' => 'initCustomerMovePublicData',
            'render_dingtalk_function' => [Helper::class, 'renderCustomerMovePublic'],
            'module' => self::MODULE_CUSTOMER,
        ],
        self::NOTIFICATION_TYPE_CUSTOMER_SHARE => [
            'title' => '共享客户通知',
            'init_data_function' => 'initCustomerShareData',
            'module' => self::MODULE_CUSTOMER,
        ],
        self::NOTIFICATION_TYPE_CUSTOMER_TRANSFER => [
            'title' => '转移客户通知',
            'init_data_function' => 'initCustomerTransferData',
            'module' => self::MODULE_CUSTOMER,
        ],
        self::NOTIFICATION_TYPE_CUSTOMER_MERGE => [
            'title' => '合并客户通知',
            'init_data_function' => 'initCustomerMergeData',
            'module' => self::MODULE_CUSTOMER,
        ],
        self::NOTIFICATION_TYPE_CUSTOMER_ALIBABA_SYNC => [
            'title' => '阿里客户通客户同步完成通知',
            'init_data_function' => 'initAlibabaCustomerSyncTaskData',
            'module' => self::MODULE_CUSTOMER,
        ],
        self::NOTIFICATION_TYPE_CUSTOMER_CANCEL => [
            'title' => '取消跟进客户通知',
            'init_data_function' => 'initCustomerCancelData',
            'module' => self::MODULE_CUSTOMER,
        ],
        self::NOTIFICATION_TYPE_CUSTOMER_EXPORT => [
            'title' => '客户资料导出完成',
            'init_data_function' => 'initCompanyTrailExportData',
            'module' => self::MODULE_CUSTOMER,
        ],
        self::NOTIFICATION_TYPE_CUSTOMER_TRAIL_EXPORT => [
            'title' => '客户动态跟进情况导出完成通知',
            'init_data_function' => 'initCompanyTrailExportData',
            'module' => self::MODULE_CUSTOMER,
        ],
        self::NOTIFICATION_TYPE_CUSTOMER_IMPORT => [
            'title' => '客户导入结果通知',
            'init_data_function' => 'initCustomerImportData',
            'module' => self::MODULE_CUSTOMER,
        ],
        self::NOTIFICATION_TYPE_CUSTOMER_IMPORT_BY_FU_TONG => [
	        'title' => '其他系统客户导入完成',
	        'init_data_function' => 'initFTCustomerImportData',
	        'module' => self::MODULE_CUSTOMER,
        ],
        self::NOTIFICATION_TYPE_FAVORITE_LIST_TRANSFER_CUSTOMER => [
            'title' => '批量新建客户完成通知',
            'init_data_function' => 'initCustomerImportData',
            'module' => self::MODULE_CUSTOMER,
        ],
//        TODO
        self::NOTIFICATION_TYPE_CUSTOMER_TRAIL_COMMENT => [
	        'title' => '评论提醒',
	        'init_data_function' => 'initCustomerTrailComment',
	        'module' => self::MODULE_CUSTOMER,
        ],
        self::NOTIFICATION_TYPE_PUBLIC_CUSTOMER_ASSIGN => [
            'title' => '公海客户分配提醒',
            'init_data_function' => 'initCustomerAssignPublicData',
            'render_dingtalk_function' => [Helper::class, 'renderPublicCustomerAssign'],
            'module' => self::MODULE_CUSTOMER,
        ],
        self::NOTIFICATION_TYP_CUSTOMER_DETECT_CONFLICT => [
            'title'              => '客户归属重建',
            'init_data_function' => 'initCustomerTrailComment',
            'module'             => self::MODULE_CUSTOMER,
            'user_defined_title' => true,
        ],
        self::NOTIFICATION_TYPE_CUSTOMER_LIMIT_APPROACHING_MAXIMUM_REMINDER => [
            'title'              => '即将达到客户上限提醒',
            'init_data_function' => 'initCustomerLimitApproachingMaximumReminder',
            'module'             => self::MODULE_CUSTOMER,
        ],
        self::NOTIFICATION_TYPE_CUSTOMER_LIMIT_REACHED_NOTIFY => [
            'title'              => '已达客户上限通知',
            'init_data_function' => 'initCustomerLimitReached',
            'module'             => self::MODULE_CUSTOMER,
        ],
        self::NOTIFICATION_TYPE_OPPORTUNITY_EXPORT => [
            'title' => '商机报表生成通知',
            'init_data_function' => 'initExportTemplateData',
            'module' => self::MODULE_OPPORTUNITY,
        ],
        self::NOTIFICATION_TYPE_OPPORTUNITY_TRAIL_COMMENT => [
            'title' => '商机评论提醒',
            'init_data_function' => 'initOpportunityTrailComment',
            'module' => self::MODULE_OPPORTUNITY,
        ],
        self::NOTIFICATION_TYPE_OPPORTUNITY_IMPORT => [
            'title' => '商机导入结果通知',
            'init_data_function' => 'initCustomerImportData',
            'module' => self::MODULE_OPPORTUNITY,
        ],
        self::NOTIFICATION_TYPE_ORDER_DELETE => [
            'title' => '订单删除提醒',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_INVOICE,
        ],
        self::NOTIFICATION_TYPE_QUOTATION_DELETE => [
            'title' => '报价单删除提醒',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_INVOICE,
        ],
        self::NOTIFICATION_TYPE_PAYPAL_RECEIPT => [
            'title' => 'PayPal收款提醒',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_INVOICE,
        ],
        self::NOTIFICATION_TYPE_ORDER_HANDLER_ADD => [
            'title' => '成为订单处理人提醒',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_INVOICE,
        ],
        self::NOTIFICATION_TYPE_ORDER_HANDLER_REMOVE => [
            'title' => '移出订单处理人提醒',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_INVOICE,
        ],
        self::NOTIFICATION_TYPE_PRODUCT_IMPORT => [
            'title' => '产品导入完成',
            'init_data_function' => 'initCustomerImportData', //可以用客户导入通知信息模版
            'module' => self::MODULE_INVOICE,
        ],
        self::NOTIFICATION_TYPE_ORDER_IMPORT => [
            'title' => '订单导入完成',
            'init_data_function' => 'initCustomerImportData', //可以用客户导入通知信息模版
            'module' => self::MODULE_INVOICE,
        ],
        self::NOTIFICATION_TYPE_SUPPLIER_IMPORT => [
            'title' => '供应商导入完成',
            'init_data_function' => 'initCustomerImportData', //可以用客户导入通知信息模版
            'module' => self::MODULE_INVOICE,
        ],
        self::NOTIFICATION_TYPE_SUPPLIER_PRODUCT_IMPORT => [
            'title' => '供应商产品导入完成',
            'init_data_function' => 'initCustomerImportData', //可以用客户导入通知信息模版
            'module' => self::MODULE_INVOICE,
        ],
        self::NOTIFICATION_TYPE_ORDER_IMPORT_ROLLBACK => [
            'title' => '订单导入撤销失败',
            'init_data_function' => 'initCustomerImportData',
            'module' => self::MODULE_INVOICE,
        ],
        self::NOTIFICATION_TYPE_AI_PRODUCT => [
            'title' => '小满自动创建/更新产品通知',
            'init_data_function' => 'initAiProductNotifyData',
            'module' => self::MODULE_AI,
        ],
        self::NOTIFICATION_TYPE_CUSTOMER_ADVICE_ARCHIVE => [
            'title' => '建档建议执行完成',
            'init_data_function' => 'initCustomerAdviceArchiveData',
            'module' => self::MODULE_AI,
        ],
        self::NOTIFICATION_TYPE_WORKFLOW_NOTIFY => [
            'title' => '工作流提醒',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_WORKFLOW,
            'user_defined_title' => true,
        ],
        self::NOTIFICATION_TYPE_WORKFLOW_ASSIGN_NOTIFY => [
            'title' => '人员分配提醒',
            'init_data_function' => 'initWorkflowAssignNotifyData',
            'module' => self::MODULE_WORKFLOW,
        ],
        self::NOTIFICATION_TYPE_WORKFLOW_BE_ASSIGNED_NOTIFY => [
            'title' => '自动分配通知',
            'init_data_function' => 'initWorkflowBeAssignedNotify',
            'module' => self::MODULE_WORKFLOW
        ],
        self::NOTIFICATION_TYPE_APPROVALFLOWCONFIG_CLOSE_NOTIFY => [
            'title' => '审批流停用',
            'init_data_function' => 'initApprovalFlowConfigCloseNotify',
            'module' => self::MODULE_APPROVAL
        ],
        self::NOTIFICATION_TYPE_APPROVALFLOW_NOTIFY => [
            'title' => '审批流提醒',
            'template_name' => 'approvalflowNotify',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_APPROVAL,
            'user_defined_title' => true,
            'refer_type' => self::APP_REFER_TYPE_APPROVAL,
        ],
        self::NOTIFICATION_TYPE_APPROVALFLOW_TODO => [
            'title' => '待处理审批通知',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_APPROVAL,
            'refer_type' => self::APP_REFER_TYPE_APPROVAL,
        ],
        self::NOTIFICATION_TYPE_APPROVALFLOW_RESULE_NOTITY => [
            'title' => '审批结果通知',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_APPROVAL,
            'refer_type' => self::APP_REFER_TYPE_APPLY,
        ],
        self::NOTIFICATION_TYPE_SCHEDULE_COMPLETE => [
            'title' => '日程完成通知',
            'init_data_function' => 'initScheduleData',
            'module' => self::MODULE_SCHEDULE,
            'refer_type' => self::APP_REFER_TYPE_SCHEDULE,
        ],
        self::NOTIFICATION_TYPE_SCHEDULE_COMMENT => [
            'title' => '日程评论通知',
            'init_data_function' => 'initScheduleData',
            'module' => self::MODULE_SCHEDULE,
            'refer_type' => self::APP_REFER_TYPE_SCHEDULE,
        ],
        self::NOTIFICATION_TYPE_SCHEDULE_EDIT_COMMENT => [
            'title' => '日程修改评论通知',
            'init_data_function' => 'initScheduleData',
            'module' => self::MODULE_SCHEDULE,
            'refer_type' => self::APP_REFER_TYPE_SCHEDULE,
        ],
        self::NOTIFICATION_TYPE_SCHEDULE_NEW => [
            'title' => '日程邀请通知',
            'init_data_function' => 'initScheduleData',
            'module' => self::MODULE_SCHEDULE,
            'refer_type' => self::APP_REFER_TYPE_SCHEDULE,
        ],
        self::NOTIFICATION_TYPE_SCHEDULE_DEL => [
            'title' => '日程移除通知',
            'init_data_function' => 'initScheduleData',
            'module' => self::MODULE_SCHEDULE,
        ],
        self::NOTIFICATION_TYPE_SCHEDULE_EDIT => [
            'title' => '日程修改通知',
            'init_data_function' => 'initScheduleData',
            'module' => self::MODULE_SCHEDULE,
            'refer_type' => self::APP_REFER_TYPE_SCHEDULE,
        ],
        self::NOTIFICATION_TYPE_DISK_SPACE_WILL_OVER_LIMIT => [
            'title' => '云空间超量通知',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_SYSTEM,
        ],
        self::NOTIFICATION_TYPE_SYSTEM_NOTICE => [
            'title' => '系统提醒',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_SYSTEM,
            'user_defined_title' => true,
        ],
        self::NOTIFICATION_TYPE_ALIBABA_STORE_AUTH => [
            'title' => '店铺授权完成通知',
            'init_data_function' => 'initAlibabaStoreAuthData',
            'module' => self::MODULE_SYSTEM,
        ],
        self::NOTIFICATION_TYPE_ALIBABA_STORE_EXPIRED_PROCESS => [
            'title' => '店铺授权即将过期通知',
            'init_data_function' => 'initAlibabaStoreTipsData',
            'module' => self::MODULE_SYSTEM,
        ],
        self::NOTIFICATION_TYPE_ALIBABA_STORE_EXPIRED => [
            'title' => '店铺授权已失效通知',
            'init_data_function' => 'initAlibabaStoreExpireData',
            'module' => self::MODULE_SYSTEM,
        ],
        self::NOTIFICATION_TYPE_WORKFLOW_SYSTEM_NOTIFY => [
            'title' => '工作流执行异常通知',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_SYSTEM,
//            'user_defined_title' => true,
        ],
        self::NOTIFICATION_TYPE_WORK_REPORT_DAILY => [
            'title' => '工作日报',
            'init_data_function' => 'initWorkReportData',
            'module' => self::MODULE_SYSTEM,
        ],
        self::NOTIFICATION_TYPE_WORK_REPORT_WEEK => [
            'title' => '工作周报',
            'init_data_function' => 'initWorkReportData',
            'module' => self::MODULE_SYSTEM,
        ],
        self::NOTIFICATION_TYPE_WORK_REPORT_MONTH => [
            'title' => '工作月报',
            'init_data_function' => 'initWorkReportData',
            'module' => self::MODULE_SYSTEM,
        ],
        self::NOTIFICATION_TYPE_THIRD_PRODUCT_MATCH_OVER => [
            'title' => '平台产品批量匹配完成通知',
            'init_data_function' => 'initThirdProductMatchData',
            'module' => self::MODULE_SYSTEM,
        ],
        self::NOTIFICATION_TYPE_THIRD_PRODUCT_GEN_OVER => [
            'title' => '本地产品生成完成',
            'init_data_function' => 'initThirdProductGenData',
            'module' => self::MODULE_INVOICE,
        ],
        self::NOTIFICATION_TYPE_PRODUCT_INVENTORY_WARNING => [
            'title' => '库存预警通知',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_SYSTEM,
        ],
        self::NOTIFICATION_TYPE_PURCHASE_PRODUCT_TRANSFER => [
            'title' => '采购任务通知',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_PRODUCT_TRANSFER,
        ],
        self::NOTIFICATION_TYPE_INBOUND_PRODUCT_TRANSFER => [
            'title' => '入库任务通知',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_PRODUCT_TRANSFER,
        ],
        self::NOTIFICATION_TYPE_OUTBOUND_PRODUCT_TRANSFER => [
            'title' => '出库任务通知',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_PRODUCT_TRANSFER,
        ],
        self::NOTIFICATION_TYPE_CREATE_PRODUCT_TRANSFER => [
            'title' => '新任务',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_PRODUCT_TRANSFER,
        ],
        self::NOTIFICATION_TYPE_CHANGE_HANDLER_PRODUCT_TRANSFER => [
            'title' => '变更处理人',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_PRODUCT_TRANSFER,
        ],
        self::NOTIFICATION_TYPE_REMOVE_PRODUCT_TRANSFER => [
            'title' => '任务已删除',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_PRODUCT_TRANSFER,
        ],
        self::NOTIFICATION_TYPE_CHANGE_STATUS_PRODUCT_TRANSFER => [
            'title' => '任务状态变更',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_PRODUCT_TRANSFER,
        ],
        self::NOTIFICATION_TYPE_CHANGE_CONTENT_PRODUCT_TRANSFER => [
            'title' => '任务内容更新',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_PRODUCT_TRANSFER,
        ],
        self::NOTIFICATION_TYPE_CHANGE_PROGRESS_PRODUCT_TRANSFER => [
            'title' => '任务进度变更',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_PRODUCT_TRANSFER,
        ],
        self::NOTIFICATION_TYPE_UPSTREAM_INVOICE_PRODUCT_TRANSFER => [
            'title' => '上游单据变更',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_PRODUCT_TRANSFER,
        ],
        self::NOTIFICATION_TYPE_WAIT_HANDLER_PRODUCT_TRANSFER => [
            'title' => '任务待处理',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_PRODUCT_TRANSFER,
        ],
        self::NOTIFICATION_TYPE_COMMENT_PRODUCT_TRANSFER => [
            'title' => '任务跟进动态',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_PRODUCT_TRANSFER,
        ],
        self::NOTIFICATION_TYPE_FEEDBACK_PRODUCT_TRANSFER => [
            'title' => '询价反馈',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_PRODUCT_TRANSFER,
        ],
        self::NOTIFICATION_TYPE_OTHER_TRANSFER => [
            'title' => '单据任务通知',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_PRODUCT_TRANSFER,
        ],
        self::NOTIFICATION_TYPE_PAYMENT_INVOICE => [
            'title' => '付款单通知',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_PAYMENT_INVOICE,
        ],
        self::NOTIFICATION_TYPE_COST_INVOICE => [
            'title' => '费用单通知',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_COST_INVOICE,
        ],
        self::NOTIFICATION_TYPE_OSS_SYSTEM_NOTICE => [
            'title' => '系统提醒',
            'init_data_function' => 'initOssSystemNoticeData',
            'module' => self::MODULE_SYSTEM,
            'user_defined_title' => true,
        ],
        self::NOTIFICATION_TYPE_OSS_DEFAULT_NOTICE => [
            'title' => '系统提醒',
            'init_data_function' => 'initOssSystemNoticeData',
            'module' => self::MODULE_SYSTEM,
            'user_defined_title' => true,
        ],
        self::NOTIFICATION_TYPE_TMS_TRADE_DOCUMENT_FEEDBACK => [
            'title' => '快速反馈',
            'init_data_function' => 'initTmsFeedbackData',
            'module' => self::MODULE_TMS_TRADE_DOCUMENT,
        ],
        self::NOTIFICATION_TYPE_TMS_TRADE_DOCUMENT_MESSAGE => [
            'title' => '客户留言',
            'init_data_function' => 'initTmsMessageData',
            'module' => self::MODULE_TMS_TRADE_DOCUMENT,
        ],
        self::NOTIFICATION_TYPE_CUSTOMER_MILESTONE_NOTICE => [
            'title' => '系统消息',
            'init_data_function' => 'initCustomerMileStoneData',
            'module' => self::MODULE_SYSTEM,
        ],
        self::NOTIFICATION_TYPE_BUSINESS_TARGET_EXPORT => [
            'title' => '业务指标导出完成',
            'init_data_function' => 'initBusinessTargetExportData',
            'module' => self::MODULE_CUSTOMER,
        ],
        self::NOTIFICATION_TYPE_FACEBOOK_PAGE_EXPIRE => [
            'title' => 'FB公共主页授权过期',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_SOCIAL_MEDIA,
        ],
        self::NOTIFICATION_TYPE_FACEBOOK_PAGE_ASSIGN => [
            'title' => 'FB公共主页新增授权',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_SOCIAL_MEDIA,
        ],
        self::NOTIFICATION_TYPE_FACEBOOK_PAGE_REMOVE_ASSIGN => [
            'title' => 'FB公共主页移除授权',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_SOCIAL_MEDIA,
        ],
        self::NOTIFICATION_TYPE_FACEBOOK_CREATE_LEAD_FORM => [
            'title' => 'Facebook Lead表单新消息通知',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_SOCIAL_MEDIA,
        ],
        self::NOTIFICATION_TYPE_INSTAGRAM_ACCOUNT_EXPIRE => [
            'title' => 'Instagram账号授权过期',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_SOCIAL_MEDIA,
        ],
        self::NOTIFICATION_TYPE_INSTAGRAM_ACCOUNT_ASSIGN => [
            'title' => 'Instagram账号新增授权',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_SOCIAL_MEDIA,
        ],
        self::NOTIFICATION_TYPE_INSTAGRAM_ACCOUNT_REMOVE_ASSIGN => [
            'title' => 'Instagram账号移除授权',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_SOCIAL_MEDIA,
        ],
        self::NOTIFICATION_TYPE_ERP_SERVICE_AUTH_SUCCESS => [
            'title' => 'ERP授权成功',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_SYSTEM,
        ],
        self::NOTIFICATION_TYPE_ERP_SERVICE_AUTH_FAILED => [
            'title' => 'ERP授权失败',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_SYSTEM,
        ],
        self::NOTIFICATION_TYPE_ERP_SERVICE_SYNC_FINISH => [
            'title' => '数据同步完成',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_SYSTEM,
        ],
        self::NOTIFICATION_TYPE_ERP_SERVICE_SYNC_ORDER => [
            'title' => '订单状态同步成功',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_SYSTEM,
        ],
        self::NOTIFICATION_TYPE_WABA_ACCOUNT_APPROVED => [
            'title' => 'waba账号审核通过',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_SYSTEM,
        ],
        self::NOTIFICATION_TYPE_WABA_ACCOUNT_REJECTED => [
            'title' => 'waba账号审核不通过',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_SYSTEM,
        ],
//        self::NOTIFICATION_TYPE_WABA_TEMPLATE_APPROVED => [
//            'title' => 'waba模板审核通过',
//            'init_data_function' => 'initDefault',
//            'module' => self::MODULE_SOCIAL_MEDIA,
//        ],
//        self::NOTIFICATION_TYPE_WABA_TEMPLATE_REJECTED => [
//            'title' => 'waba模板审核不通过',
//            'init_data_function' => 'initDefault',
//            'module' => self::MODULE_SOCIAL_MEDIA,
//        ],
        self::NOTIFICATION_TYPE_WORK_JOURNAL_COMMIT => [
            'title' => '提交的工作报告',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_WORK_JOURNAL_COMMIT,
        ],
        self::NOTIFICATION_TYPE_WORK_JOURNAL_COMMENT => [
            'title' => '工作报告评论通知',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_WORK_JOURNAL_COMMENT,
        ],
        self::NOTIFICATION_TYPE_WORK_JOURNAL_REMIND => [
            'title' => '工作报告提交通知',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_WORK_JOURNAL_REMIND,
        ],
        self::NOTIFICATION_TYPE_OKKI_AI_QUALITY_TASK_OVER => [
            'title' => 'AI质检报告已生成',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_SYSTEM,
        ],
        self::NOTIFICATION_TYPE_ALI_MAIL_ORGANIZATION_CHECK => [
            'title' => '邮箱开通',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_SYSTEM,
        ],
        self::NOTIFICATION_TYPE_DINGTALK_APPROVAL_FORM_TODO => [
            'title' => '审批待处理',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_APPROVAL,
        ],
        self::NOTIFICATION_TYPE_DINGTALK_APPROVAL_FROM_RESULT => [
            'title' => '审批结果',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_APPROVAL,
        ],
        self::NOTIFICATION_TYPE_DINGTALK_APPROVAL_FLOW_STOP => [
            'title' => '审批流停用',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_APPROVAL,
        ],
        self::NOTIFICATION_TYPE_AT_REMARK => [
            'title' => '跟进动态及评论@我',
            'init_data_function' => 'initAtPersonData',
            'module' => self::MODULE_AT,
        ],
        self::NOTIFICATION_TYPE_AT_PRODUCT_TRANSFER => [
            'title' => '任务动态及评论@我',
            'init_data_function' => 'initAtPersonData',
            'module' => self::MODULE_AT,
        ],
        self::NOTIFICATION_TYPE_AT_WORK_JOURNAL => [
            'title' => '工作报告评论@我',
            'init_data_function' => 'initAtPersonData',
            'module' => self::MODULE_AT,
        ],
        self::NOTIFICATION_TYPE_AT_SCHEDULE => [
            'title' => '日程评论@我',
            'init_data_function' => 'initAtPersonData',
            'module' => self::MODULE_AT,
        ],
        self::NOTIFICATION_TYPE_AI_CLASSIFY_NEW_LEAD_MERGE => [
            'title' => '新线索自动化合并通知',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_LEAD,
        ],
        self::NOTIFICATION_TYPE_AI_CLASSIFY_NEW_COMPANY_MERGE => [
            'title' => '新客户自动化合并通知',
            'init_data_function' => 'initDefault',
            'module' => self::MODULE_CUSTOMER,
        ],
    ];


    //crm 与 ames 消息通知类型映射关系
    const CRM_AMES_TEMPLATE_MAP = [
        //日程
        self::NOTIFICATION_TYPE_SCHEDULE_NEW => AmesNotification::TEMPLATE_CODE_SCHEDULE_NEW,
        self::NOTIFICATION_TYPE_SCHEDULE_DEL => AmesNotification::TEMPLATE_CODE_SCHEDULE_DEL,
        self::NOTIFICATION_TYPE_SCHEDULE_EDIT => AmesNotification::TEMPLATE_CODE_SCHEDULE_EDIT,
        self::NOTIFICATION_TYPE_SCHEDULE_COMMENT => AmesNotification::TEMPLATE_CODE_SCHEDULE_COMMENT,
        self::NOTIFICATION_TYPE_SCHEDULE_EDIT_COMMENT => AmesNotification::TEMPLATE_CODE_SCHEDULE_EDIT_COMMENT,
        self::NOTIFICATION_TYPE_SCHEDULE_COMPLETE => AmesNotification::TEMPLATE_CODE_SCHEDULE_COMPLETE,

        //线索
        self::NOTIFICATION_TYPE_LEAD_FOLLOW_EXPORT => AmesNotification::TEMPLATE_CODE_LEAD_FOLLOW_EXPORT,
        self::NOTIFICATION_TYPE_LEAD_IMPORT => AmesNotification::TEMPLATE_CODE_LEAD_IMPORT,
        self::NOTIFICATION_TYPE_LEAD_BATCH_TRANSFORM => AmesNotification::TEMPLATE_CODE_LEAD_BATCH_TRANSFORM,
        self::NOTIFICATION_TYPE_LEAD_TRANSFER => AmesNotification::TEMPLATE_CODE_LEAD_TRANSFER,
        self::NOTIFICATION_TYPE_LEAD_CANCEL => AmesNotification::TEMPLATE_CODE_LEAD_CANCEL ,
        self::NOTIFICATION_TYPE_LEAD_MERGE => AmesNotification::TEMPLATE_CODE_LEAD_MERGE,

        //客户
        self::NOTIFICATION_TYPE_CUSTOMER_EXPORT => AmesNotification::TEMPLATE_CODE_CUSTOMER_EXPORT,//客户资料导出完成
        self::NOTIFICATION_TYPE_CUSTOMER_TRAIL_EXPORT => AmesNotification::TEMPLATE_CODE_CUSTOMER_TRAIL_EXPORT,//客户动态导出完成
        self::NOTIFICATION_TYPE_CUSTOMER_MOVE_PUBLIC => AmesNotification::TEMPLATE_CODE_CUSTOMER_WILL_MOVE_TO_PUBLIC,//即将移入公海提醒
        self::NOTIFICATION_TYPE_CUSTOMER_SHARE => AmesNotification::TEMPLATE_CODE_CUSTOMER_SHARE,//共享客户通知
        self::NOTIFICATION_TYPE_CUSTOMER_ALIBABA_SYNC => AmesNotification::TEMPLATE_CODE_CUSTOMER_ALIBABA_SYNC,//阿里客户通客户同步完成通知
        self::NOTIFICATION_TYPE_CUSTOMER_IMPORT_BY_FU_TONG => AmesNotification::TEMPLATE_CODE_CUSTOMER_IMPORT_BY_FU_TONG,//其他系统客户导入完成
        self::NOTIFICATION_TYPE_CUSTOMER_TRAIL_COMMENT => AmesNotification::TEMPLATE_CODE_CUSTOMER_TRAIL_COMMENT,//动态评论
        self::NOTIFICATION_TYPE_CUSTOMER_MAIL_CONFLICT => AmesNotification::TEMPLATE_CODE_CUSTOMER_MAIL_CONFLICT,//邮件撞单提醒
        self::NOTIFICATION_TYPE_CUSTOMER_ADVICE_ARCHIVE => AmesNotification::TEMPLATE_CODE_CUSTOMER_ADVICE_ARCHIVE,//建档建议执行完成
        self::NOTIFICATION_TYPE_CUSTOMER_IMPORT => AmesNotification::TEMPLATE_CODE_CUSTOMER_IMPORT_COMPLETE,
        self::NOTIFICATION_TYPE_CUSTOMER_TRANSFER => AmesNotification::TEMPLATE_CODE_TRANSFER_CUSTOMER,
        self::NOTIFICATION_TYPE_CUSTOMER_MERGE => AmesNotification::TEMPLATE_CODE_COMBINE_CUSTOMER,
        self::NOTIFICATION_TYPE_CUSTOMER_CANCEL => AmesNotification::TEMPLATE_CODE_CANCEL_FOLLOWUP_CUSTOMER,


        //商机
        self::NOTIFICATION_TYPE_OPPORTUNITY_EXPORT => AmesNotification::TEMPLATE_CODE_OPPORTUNITY_EXPORT,//商机资料导出完成

        //订单
        self::NOTIFICATION_TYPE_ORDER_HANDLER_ADD => AmesNotification::TEMPLATE_CODE_ORDER_HANDLER_ADDED,//成为订单处理人提醒
        self::NOTIFICATION_TYPE_ORDER_HANDLER_REMOVE => AmesNotification::TEMPLATE_CODE_ORDER_HANDLER_REMOVED,//移出订单处理人提醒
        self::NOTIFICATION_TYPE_ORDER_DELETE => AmesNotification::TEMPLATE_CODE_ORDER_DELETED,//订单删除提醒
        self::NOTIFICATION_TYPE_QUOTATION_DELETE => AmesNotification::TEMPLATE_CODE_QUOTATION_DELETED,//报价单删除提醒
        self::NOTIFICATION_TYPE_PAYPAL_RECEIPT => AmesNotification::TEMPLATE_CODE_PAYPAL_RECEIPT,//PayPal收款提醒
        self::NOTIFICATION_TYPE_PURCHASE_PRODUCT_TRANSFER => AmesNotification::TEMPLATE_CODE_PRODUCT_TASK_PURCHASE,//采购任务通知
        self::NOTIFICATION_TYPE_INBOUND_PRODUCT_TRANSFER => AmesNotification::TEMPLATE_CODE_PRODUCT_TASK_INBOUND,//入库任务通知
        self::NOTIFICATION_TYPE_OUTBOUND_PRODUCT_TRANSFER => AmesNotification::TEMPLATE_CODE_PRODUCT_TASK_OUTBOUND,//出库任务通知

        self::NOTIFICATION_TYPE_AI_PRODUCT => AmesNotification::TEMPLATE_CODE_AI_PRODUCT,//小满自动创建/更新产品通知
        self::NOTIFICATION_TYPE_THIRD_PRODUCT_MATCH_OVER => AmesNotification::TEMPLATE_CODE_THIRD_PRODUCT_MATCH_OVER,//小满自动创建/更新产品通知

        //审批相关
        self::NOTIFICATION_TYPE_APPROVALFLOW_TODO => AmesNotification::TEMPLATE_CODE_APPROVAL_FLOW_TODO,//待处理审批通知
        self::NOTIFICATION_TYPE_APPROVALFLOW_RESULE_NOTITY => AmesNotification::TEMPLATE_CODE_APPROVAL_FLOW_RESULT,//审批结果通知
        self::NOTIFICATION_TYPE_APPROVALFLOWCONFIG_CLOSE_NOTIFY => AmesNotification::TEMPLATE_CODE_APPROVAL_FLOW_CONFIG_CLOSE,//审批流停用
        self::NOTIFICATION_TYPE_WORKFLOW_ASSIGN_NOTIFY => AmesNotification::TEMPLATE_CODE_WORKFLOW_STAFF_ASSIGNMENT,//人员分配提醒
        self::NOTIFICATION_TYPE_WORKFLOW_BE_ASSIGNED_NOTIFY => AmesNotification::TEMPLATE_CODE_WORKFLOW_STAFF_ASSIGNMENT_AUTO,//自动分配通知
        self::NOTIFICATION_TYPE_WORKFLOW_SYSTEM_NOTIFY => AmesNotification::TEMPLATE_CODE_WORKFLOW_EXECUTE_EXCEPTION,//工作流执行异常通知


        self::NOTIFICATION_TYPE_DISK_SPACE_WILL_OVER_LIMIT => AmesNotification::TEMPLATE_CODE_DISK_SPACE_WILL_EXCEEDED,//云空间超量通知
        self::NOTIFICATION_TYPE_SYSTEM_NOTICE => AmesNotification::TEMPLATE_CODE_SYSTEM_REMINDER,
        self::NOTIFICATION_TYPE_OSS_DEFAULT_NOTICE => AmesNotification::TEMPLATE_CODE_SYSTEM_REMINDER,//系统提醒
        self::NOTIFICATION_TYPE_OSS_SYSTEM_NOTICE => AmesNotification::TEMPLATE_CODE_SYSTEM_REMINDER,//系统提醒
        self::NOTIFICATION_TYPE_ALIBABA_STORE_EXPIRED_PROCESS => AmesNotification::TEMPLATE_CODE_ALIBABA_STORE_EXPIRED_PROCESS,//店铺授权即将过期通知
        self::NOTIFICATION_TYPE_ALIBABA_STORE_AUTH => AmesNotification::TEMPLATE_CODE_ALIBABA_STORE_AUTH,//店铺授权完成通知
        self::NOTIFICATION_TYPE_ALIBABA_STORE_EXPIRED => AmesNotification::TEMPLATE_CODE_ALIBABA_STORE_EXPIRED,//店铺授权已失效通知
        self::NOTIFICATION_TYPE_WORK_REPORT_DAILY => AmesNotification::TEMPLATE_CODE_WORK_REPORT_DAILY,//工作日报
        self::NOTIFICATION_TYPE_WORK_REPORT_WEEK => AmesNotification::TEMPLATE_CODE_WORK_REPORT_WEEKLY,//工作周报
        self::NOTIFICATION_TYPE_TMS_TRADE_DOCUMENT_FEEDBACK => AmesNotification::TEMPLATE_CODE_FEEDBACK,//快速反馈
        self::NOTIFICATION_TYPE_TMS_TRADE_DOCUMENT_MESSAGE => AmesNotification::TEMPLATE_CODE_MESSAGE,//快速留言
        self::NOTIFICATION_TYPE_CUSTOMER_MILESTONE_NOTICE => AmesNotification::TEMPLATE_CODE_SYSTEM_REMINDER,//客户里程碑
    ];

}
