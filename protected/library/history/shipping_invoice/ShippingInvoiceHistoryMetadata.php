<?php

namespace common\library\history\shipping_invoice;

use common\library\history\base\BatchHistory;
use common\library\history\base\History;
use common\library\history\base\HistoryFilter;
use common\library\history\base\HistoryOperator;
use xiaoman\orm\metadata\Metadata;

class ShippingInvoiceHistoryMetadata extends Metadata
{
    protected $columns = [
        'client_id' => [
            'type' => 'bigint',
            'name' => 'client_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => ['enable' => true, 'batch' => true,]
        ],
        'type' => [
            'type' => 'smallint',
            'name' => 'type',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => ['enable' => true, 'batch' => true,]
        ],
        'shipping_invoice_id' => [
            'type' => 'bigint',
            'name' => 'shipping_invoice_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => ['enable' => true, 'batch' => true,]
        ],
        'shipping_record_id' => [
            'type' => 'bigint',
            'name' => 'shipping_record_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => ['enable' => true, 'batch' => true,]
        ],
        'shipping_packing_record_id' => [
            'type' => 'bigint',
            'name' => 'shipping_packing_record_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => ['enable' => true, 'batch' => true,]
        ],
        'shipping_custom_declaration_record_id' => [
            'type' => 'bigint',
            'name' => 'shipping_custom_declaration_record_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => ['enable' => true, 'batch' => true,]
        ],
        'update_user' => [
            'type' => 'bigint',
            'name' => 'update_user',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => ['enable' => true, 'batch' => true,]
        ],
        'diff' => [
            'type' => 'jsonb',
            'name' => 'diff',
            'nullable' => 0,
            'php_type' => 'array',
            'filter' => ['enable' => true, 'json' => true,]
        ],
        'create_time' => [
            'type' => 'timestamp',
            'name' => 'create_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => ['enable' => true, 'range' => true,]
        ],
        'update_time' => [
            'type' => 'timestamp',
            'name' => 'update_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => ['enable' => true, 'range' => true,]
        ],
    ];

    public static function table()
    {
        return 'tbl_shipping_invoice_history';
    }

    public static function dataSource()
    {
        return Metadata::DATA_SOURCE_POSTGRESQL_CLIENT;
    }

    public static function singeObject()
    {
        return History::class;
    }

    public static function batchObject()
    {
        return BatchHistory::class;
    }

    public static function operator()
    {
        return HistoryOperator::class;
    }

    public static function formatter()
    {
        return ShippingInvoiceHistoryFormatter::class;
    }

    public static function filter()
    {
        return HistoryFilter::class;
    }

    public static function objectIdKey()
    {
        return null;
    }
}