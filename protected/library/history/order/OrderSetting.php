<?php
namespace common\library\history\order;

use common\library\history\base\Setting;
use common\library\history\invoice\CostListCompare;
use common\library\history\invoice\DepartmentsCompare;
use common\library\history\invoice\HandlerCompare;
use common\library\history\invoice\StatusCompare;
use common\library\history\invoice\SyncAliOrderConfigCompare;
use common\library\history\invoice\UsersCompare;
use common\library\history\traits\BizObjectFieldSetting;
use common\library\object\object_define\Constant;

class OrderSetting extends Setting
{
    use BizObjectFieldSetting;
    const TYPE_EDIT_ORDER =1;
    const TYPE_NEW_PRODUCT =2;
    const TYPE_EDIT_PRODUCT =3;
    const TYPE_DELETE_PRODUCT =4;
    const TYPE_NEW_COST =5;
    const TYPE_EDIT_COST =6;
    const TYPE_DELETE_COST =7;
    const TYPE_EDIT_USER =8;
    const TYPE_STATUS = 9;
    const TYPE_HANDLER = 10;
    const TYPE_EDIT_DEPARTMENTS = 11;
    const TYPE_ALIBABA_SYNC = 12;
    const TYPE_OPEN_SYNC_ALI_ORDER_SWITCH = 13;  //开启了订单同步
    const TYPE_CLOSE_SYNC_ALI_ORDER_SWITCH = 14;  //关闭了订单同步
    const TYPE_REMOVE = 15;
    const TYPE_RECOVER = 16;
    const TYPE_IMPORT_ORDER = 17;   //批量导入记录
    const TYPE_SYNC_ALI_ORDER_CONFIG_UPDATE = 18;  //更新了订单同步
    const TYPE_SYNC_ERP_ORDER_STATUS_UPDATE = 19;  // erp订单状态变更
    const TYPE_CREATE_ORDER = 20; // 创建 之前没有
    const TYPE_TRANSFER_ORDER = 21; // 转信保

    const RECORD_FIELD = [
        'name',
        'company_id',
        'opportunity_id',
        'customer_id',
        'order_date',
        'remark',
        'account_date',
        'price_contract',
        'currency',
        'order_contract',
        'bank_info',
        'receive_remittance_way',
        'receive_remittance_remark',
        'insurance_remark',
        'customer_phone',
        'customer_email',
        'customer_address',
        'customer_name',
        'company_phone',
        'company_name',
        'company_fax',
        'company_address',
        'transport_mode',
        'package_remark',
        'shipment_port',
        'shipment_deadline_remark',
        'shipment_deadline',
        'marked',
        'more_or_less',
        'target_port',
        'order_no',
        'country',
        'ali_status_id',
        'ali_status_name',
        'fulfillment_channel',
        'tax_refund_type',
        'capital_account_id',
        'exchange_rate',
        'exchange_rate_usd',
        //这部份字段的操作历史是单独成行，放在\common\library\oms\order\Order::historyField处理
//        'status',
//        'handler',
//        'users',
//        'departments',
    ];


    public function metadataClass()
    {
        return OrderHistoryMetadata::class;
    }

    public function bizId()
    {
        return 'order_id';
    }


    public static function objectName()
    {
        return Constant::OBJ_ORDER;
    }

    public function subBizId()
    {
        return null;
    }

    public function typeTitleMap()
    {
        return [
            self::TYPE_CREATE_ORDER => '创建订单',
            self::TYPE_EDIT_ORDER => 'Modified order information',
            self::TYPE_NEW_COST => 'Added cost',
            self::TYPE_EDIT_COST => 'Edited cost',
            self::TYPE_DELETE_COST => 'Delete cost',
            self::TYPE_EDIT_USER => 'Edited the order owner',
            self::TYPE_EDIT_DEPARTMENTS => 'Edited the order owner departments',
            self::TYPE_STATUS => 'Changed order status',
            self::TYPE_HANDLER => 'Edited current handler',
            self::TYPE_ALIBABA_SYNC => 'Modified order information',
            self::TYPE_OPEN_SYNC_ALI_ORDER_SWITCH => 'Enable order synchronization',
            self::TYPE_CLOSE_SYNC_ALI_ORDER_SWITCH => 'Turn off order synchronization',
            self::TYPE_REMOVE => 'Delete order',
            self::TYPE_RECOVER => 'Recover order',
            self::TYPE_SYNC_ALI_ORDER_CONFIG_UPDATE => 'Update order synchronization',
            self::TYPE_IMPORT_ORDER => 'Import order',
            self::TYPE_SYNC_ERP_ORDER_STATUS_UPDATE => 'Erp edit order',
        ];
    }

    public function compareMap()
    {
        return [
            'users' => \common\library\history\base\compare\UsersCompare::class,
            'departments' => \common\library\history\base\compare\DepartmentsCompare::class,
            'handler' => \common\library\history\base\compare\HandlerCompare::class,
            'account_date' => \common\library\history\base\compare\DateCompare::class,
            'opportunity_id' => \common\library\history\base\compare\IdCompare::class,
            'customer_id' => \common\library\history\base\compare\IdCompare::class,
//            'sync_ali_order_config' => SyncAliOrderConfigCompare::class,
        ];
    }

    public function allowEmpty($type, $extraField)
    {
        switch ($type)
        {
            case OrderSetting::TYPE_REMOVE:
            case OrderSetting::TYPE_ALIBABA_SYNC:
                return true;
        }
        return false;
    }

    public function getDefaultEditType(): int
    {
        return self::TYPE_EDIT_ORDER;
    }

    public function externalFields()
    {
        return ['update_refer', 'update_type'];
    }
}