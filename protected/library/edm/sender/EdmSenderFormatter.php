<?php

/**
 * This file is part of xiaoman-crm.
 *
 * Copyright © 2012 - 2019 Xiaoman. All Rights Reserved.
 *
 * Created by <PERSON><PERSON><PERSON><PERSON> <she<PERSON><PERSON><PERSON>@xiaoman.cn>, on 2019/08/05.
 */

namespace common\library\edm\sender;

use common\library\edm\Constants;

class EdmSenderFormatter extends \ListItemFormatter
{

    protected $showStatisticsFlag = false;

    protected $showSenderConfigFlag = false;

    public function __construct()
    {

    }

    public function showStatistics(bool $flag)
    {
        $this->showStatisticsFlag = $flag;
        return $this;
    }

    public function showSenderConfig(bool $flag)
    {
        $this->showSenderConfigFlag = $flag;
        return $this;
    }

    public function buildMapData()
    {
        $map = [];
        $list = $this->listData ?: [$this->data];
        $clientIds = array_column($list, 'client_id');
        if (!empty($clientIds)) {
            $str = implode(', ', $clientIds);
            $clients = \Client::model()->findAll("client_id in ({$str})");
            $map['client'] = array_column($clients, null, 'client_id');
        }

        if ($this->showStatisticsFlag) {
            $senderIds = array_column($list, 'sender_id');
            $str = implode(', ', $senderIds);
            $senders = \EdmSender::model()->findAll("sender_id in ({$str})");
            foreach ($senders as $sender) {
                $edmSender = new EdmSender($sender->client_id);
                $edmSender->setModel($sender);
                $map['sender_statistics'][$sender->sender_id] = $edmSender->getSenderStatistics(true);
            }
        }


        if (isset($list[0]['update_user'])) {
            $userIds = array_column($list, 'update_user');
            $users = \UserInfo::findAllByIds($userIds);
            $map['users'] = \array_column($users, null, 'user_id');
        }

        $this->setMapData($map);

        parent::buildMapData();
    }

    protected function format($data)
    {
        if (!$this->showSenderConfigFlag) {
            unset($data['sender_config']);
        } else {
            $data['sender_config'] = \json_decode($data['sender_config'], true);
        }

        if ($this->hasMapData('client', $data['client_id'] ?? -1)) {
            $data['client_full_name'] = $this->getMapData('client', $data['client_id'])->full_name;
        }

        if ($this->hasMapData('sender_statistics', $data['sender_id'] ?? -1)) {
            $data['sender_statistics'] = $this->getMapData('sender_statistics', $data['sender_id']);
        }

        $updateUserId =$data['update_user'] ?? -1;
        if ($updateUserId == 0) {
            $data['update_user_info'] = null;
        } else if ($updateUserId != -1) {
            if ($this->hasMapData('users', $updateUserId)) {
                $user = $this->getMapData('users', $data['update_user']);
                $data['update_user_info'] = [
                    'user_id' => $user['user_id'],
                    'nickname' => $user['nickname'],
                    'avatar' => $user['avatar'],
                    'position' => $user['position'],
                ];
            }
        }

        switch ($data['status']) {
            case Constants::SENDER_STATUS_DNS_ERROR:
                $data['purchase_status'] = 1;
                $data['dns_status'] = 0;
                break;
            case Constants::SENDER_STATUS_DNS_OK:
                $data['purchase_status'] = 1;
                $data['dns_status'] = 1;
                break;
            case Constants::SENDER_STATUS_AUTH_ERROR:
                $data['purchase_status'] = 1;
                $data['dns_status'] = -1;
                break;
            case Constants::SENDER_STATUS_ACCOUNT_ERROR:
                $data['purchase_status'] = 1;
                $data['dns_status'] = -1;
                break;
            case Constants::SENDER_STATUS_NOT_APPLY:
            case Constants::SENDER_STATUS_NOT_BUY:
                default:
                $data['purchase_status'] = $data['dns_status'] = 0;
                break;
        }

        return $data;
    }
}