<?php

namespace common\library\privilege_v3\scope;

// 为 FilterV2 根据权限人范围组装条件, 仅为orm FilterV2服务
use common\library\object\field\FieldConstant;
use common\library\object\object_define\Constant as ObjConstant;
use common\library\object\object_relation\ObjectRelationConstant;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use xiaoman\orm\common\Filter;
use xiaoman\orm\common\FilterV2;
use xiaoman\orm\database\data\InArray;
use xiaoman\orm\database\data\JsonOverlaps;
use xiaoman\orm\database\join\Group;

/**
 * @property FilterV2 $filter
 */
class FilterPrivilegeHelper{
    protected $clientId;
    protected $objName;
    protected $mainObjName;
    protected $filter;
    protected $scopeUserFilter;
    protected $operatorUser;        // 当前操作人，用于鉴权
    protected $authFilterType = self::AUTH_FILTER_BY_VIEWER;            // 鉴权方式
    protected $viewEmptyScopeUser = false;      // 当对象没有归属人的时候，非超级管理员能否查看该对象
    protected Group $joinGroup;           // 如果当前对象处于join状态，则该属性就会保存 Group 对象
    protected $dataSource;
    protected $skipJoin;        // 跳过join

    const AUTH_FILTER_BY_NONE = 'none';          // 不对权限人范围过滤
    const AUTH_FILTER_BY_VIEWER = 'filter_by_viewer';        // 根据权限可见人过滤
    const AUTH_FILTER_BY_HANDLER = 'filter_by_handler';       // 根据权限负责人过滤

//    const AUTH_FILTER_PARAM = 'auth_filter_type';       // paas化的列表接口前端需要传递是根据权限可见人过滤还是负责人过滤的参数统一使用这个参数

    public function __construct($client_id, Filter $filter){
        $this->clientId = $client_id;
        $this->filter = $filter;

        // 如果需要根据权限人范围过滤，需要判断当前查询对象的Filter是主对象还是从对象
        $this->objName = $this->filter->getMetadata()::objectName();
        $this->dataSource = $this->filter->getMetadata()::dataSource();
        $this->mainObjName = \common\library\object\object_relation\Helper::getMainObjectName($this->objName);
        if(empty($this->mainObjName)){
            $this->mainObjName = $this->objName;
        }
    }

    // 业务侧可通过 $filter->auth_filter_type = xxx 的方式触发 __set() 调用到该方法
    public function setAuthFilterType($authFilterType){
        if(!in_array($authFilterType, [self::AUTH_FILTER_BY_NONE, self::AUTH_FILTER_BY_VIEWER, self::AUTH_FILTER_BY_HANDLER])){
            return;         // 如果业务侧往 auth_filter_type 属性set的是一个不规范的值，不抛出异常，但忽略业务侧的set操作
        }
        $this->authFilterType = $authFilterType;
    }

    public function canViewEmptyScopeUser($flag){
        $this->viewEmptyScopeUser = $flag;
    }

    public function skipJoin($flag){
        $this->skipJoin = $flag;
    }

    // 判断使用$filter查询时是否需要根据权限人范围过滤
    public function isNeedAuth(){
        // 如果业务模块未在tbl_field定义scope_user_ids字段，也不需要鉴权
        $service = new \common\library\object\field\service\ScopeUserFieldService($this->clientId, $this->filter->getMetadata());
        if(empty($service->getScopeUserFieldSettings())){
            return false;
        }

        if(!$this->getOperatorUser()){      // 没有登录态用户则无需过滤
            return false;
        }

        if($this->skipJoin){
            return false;
        }

        // 如果是超级管理员，也不需要过滤
        $this->getScopeUserFilter();
        if(PrivilegeService::getInstance($this->clientId, $this->getOperatorUser()->getUserId())->isAdmin()){
            return false;
        }

        if(empty($this->authFilterType) || !in_array($this->authFilterType, [self::AUTH_FILTER_BY_VIEWER, self::AUTH_FILTER_BY_HANDLER])){      // 没有指定过滤方式，也不需要过滤
            return false;
        }

        /**
         * 多做一个优化，双重保险：如果是对子对象查询，而且子对象的filter有父对象id作为筛选条件，也不需要做权限校验，避免指定父对象id的查询被权限屏蔽误伤
         */
        if($this->mainObjName != $this->objName){
            $mainObjMetadataClass = ObjConstant::OBJ_METADATA_MAP[$this->mainObjName];
            $mainObjPkey = $mainObjMetadataClass::objectIdKey();
            $relation = current(\common\library\object\object_relation\Helper::getUpstreamObjRelations($this->objName, [ObjectRelationConstant::MASTER_DETAIL]));
            if(in_array($mainObjPkey, $this->filter->getWhereColumns()) || in_array($relation['relation_field'], $this->filter->getWhereColumns())){
                return false;
            }
        }

        return true;
    }

    public function getOperatorUser(){
        if(empty($this->operatorUser)){
            $this->operatorUser = \User::getLoginUser();
        }

        return $this->operatorUser;
    }

    public function setJoinGroup($group){
        $this->joinGroup = $group;
    }

    // 权限人范围条件组装
    public function authQuery(){
        if(!$this->isNeedAuth()){
            return;
        }

        if($this->authFilterType == self::AUTH_FILTER_BY_VIEWER){
            $this->authByViewer();
        }
    }

    protected function authByViewer(){
        /**
         * 获取查看的权限名称,这里需要分类讨论
         * 1.如果本对象$objName是父对象，此时 $mainObjName 等于 $objName，根据$objName获取的 $viewPrivilegeName 一定非空;
         * 2.如果本对象$objName是子对象，此时 $mainObjName 不等于 $objName，根据$objName获取的 $viewPrivilegeName 可能为空或不为空，这里需要分类讨论；
         * 2-1.例如订单明细没有自己的权限控制，但订单有，此时根据$objName获取的 $viewPrivilegeName 可能为空，应该要再根据 $mainObjName 获取 $viewPrivilegeName;
         * 2-2.又例如供应商产品是一个从对象，但是供应商产品和供应商的操作权限是分开的，因此此时根据$objName获取的 $viewPrivilegeName 不为空，无需再根据 $mainObjName 获取 $viewPrivilegeName;
         *
         */
        $viewPrivilegeName = \common\library\privilege_v3\Helper::getViewPrivilegeByObjectName($this->objName);
        if(is_null($viewPrivilegeName)){
            $viewPrivilegeName = \common\library\privilege_v3\Helper::getViewPrivilegeByObjectName($this->mainObjName);
        }

        // 获取"查看"权限下的可见范围人
        $condUserIds = \common\library\privilege_v3\Helper::getPermissionScopeUser($this->getOperatorUser()->getClientId(), $this->getOperatorUser()->getUserId(), $viewPrivilegeName, true);

        // 对于客户模块，Filter底层无法判断业务侧是查询 公海数据 还是 私海数据 还是 公海+私海数据，因此这里会根据 公海 + 私海
        if($this->objName == ObjConstant::OBJ_COMPANY || $this->objName == ObjConstant::OBJ_CUSTOMER){
            $mergeCondUserIds = \common\library\privilege_v3\Helper::getPermissionScopeUser($this->getOperatorUser()->getClientId(), $this->getOperatorUser()->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_VIEW, true);
            if($condUserIds === \common\library\privilege_v3\Helper::CAN_MANAGE_ALL_USER || $mergeCondUserIds === \common\library\privilege_v3\Helper::CAN_MANAGE_ALL_USER ){
                return;
            }

            $condUserIds = array_unique(array_merge($condUserIds, $mergeCondUserIds));
        }

        // 如果可见范围为全部，则不用继续筛选
        if($condUserIds === \common\library\privilege_v3\Helper::CAN_MANAGE_ALL_USER){
            return;
        }

        if($this->objName == ObjConstant::OBJ_COMPANY ||
            $this->objName == ObjConstant::OBJ_LEAD ||
            $this->objName == ObjConstant::OBJ_OPPORTUNITY ||
            $this->objName == ObjConstant::OBJ_CUSTOMER){
            $filterFieldName = 'user_id';
        }else{
            $filterFieldName = FieldConstant::SCOPE_USER_IDS_FIELD_NAME;
        }

        $emptyValue = ($this->dataSource == \xiaoman\orm\metadata\Metadata::DATA_SOURCE_MYSQL_CLIENT) ? "JSON_ARRAY()" : "'{}'";
        if($condUserIds === []){
            if($this->viewEmptyScopeUser){
                $scopeFilter = $this->getScopeUserFilter();
                $alias = $scopeFilter->getQuery()->getTable()->getAlias();
                $this->filter->getQuery()->rawWhere(" and {$alias}.{$filterFieldName} = {$emptyValue}");
            }else{
//                $this->filter->breakOff();
                $this->filter->getQuery()->rawWhere(' and 1=0');
            }
            return;
        }

        $scopeFilter = $this->getScopeUserFilter();
        if($this->viewEmptyScopeUser){
            $condUserIdsStr = implode(',', $condUserIds);
            $filterFieldName = $scopeFilter->getQuery()->getTable()->getAlias().'.'.$filterFieldName;
            if ($this->dataSource == \xiaoman\orm\metadata\Metadata::DATA_SOURCE_MYSQL_CLIENT) {
                $strUserIds = implode('","', $condUserIds);
                $scopeFilter->getQuery()->rawWhere(" and (
                    JSON_OVERLAPS({$filterFieldName}, JSON_ARRAY($condUserIdsStr)) 
                    OR JSON_OVERLAPS({$filterFieldName}, JSON_ARRAY(\"$strUserIds\"))
                    OR {$filterFieldName} = {$emptyValue}
                )");
            } else {
                // scope_user_ids 中不可能出现 0 的元素
                $scopeFilter->getQuery()->rawWhere(" and ({$filterFieldName} && Array[{$condUserIdsStr}]::bigint[] or {$filterFieldName} = {$emptyValue})");
            }

        }else{
            if ($this->dataSource == \xiaoman\orm\metadata\Metadata::DATA_SOURCE_MYSQL_CLIENT) {
                $condUserIdsStr = implode(',', $condUserIds);
                $strUserIds = implode('","', $condUserIds);
                $scopeFilter->getQuery()->rawWhere(" and (
                    JSON_OVERLAPS({$filterFieldName}, JSON_ARRAY($condUserIdsStr)) 
                    OR JSON_OVERLAPS({$filterFieldName}, JSON_ARRAY(\"$strUserIds\"))
                )");
            } else {
                $scopeFilter->{$filterFieldName} = new InArray($condUserIds);     // scope_user_ids 中不可能出现 0 的元素
            }
        }
    }

    // 该方法一般用不到，不过可以先留着
//    protected function authByHandler(){
//        $filterFieldName = FieldConstant::SCOPE_HANDLER_FIELD_NAME;
//        $scopeFilter = $this->getScopeUserFilter();
//
//        // 对于 "权限处理人" 字段，还需要判断该字段在业务表的db类型是 bigint 还是 bigint[]
//        $scopeFieldInfo = $scopeFilter->getMetadata()->getFieldTransfer()->getFieldMap($scopeFilter->getMetadata()::objectName())[$filterFieldName];
//
//        // 特殊情况：某些数据行的处理人为0，表示该数据没有处理人，此时默认只有管理员才能看到这些数据，而非任何人都能看到这些数据
//        $realFilterFieldName = $scopeFieldInfo['columns']['data_key'];
//        $scopeFilter->$realFilterFieldName = $scopeFieldInfo['array_flag'] == FieldConstant::IS_ARRAY ? new InArray([$this->getOperatorUser()->getUserId()]) : $this->getOperatorUser()->getUserId();
//    }

    // 获取权限人条件字段所在的Filter，该方法应对从对象做权限人过滤时，主从对象联查的场景
    protected function getScopeUserFilter(){
        if(!empty($this->scopeUserFilter)){
            return $this->scopeUserFilter;
        }

        if($this->objName == $this->mainObjName){       // 说明本对象是主对象
            $this->scopeUserFilter = $this->filter;
            return $this->scopeUserFilter;
        }

        $metadata = ObjConstant::getMetadataByObjectName($this->mainObjName);
        $mainObjFilter = $this->filter->getJoinedFilter($metadata::table());
        $joinScene = $this->filter->isJoinSence() || (!empty($mainObjFilter) && $mainObjFilter->isJoinSence());
        $notEverJoin = false;
        if(!$mainObjFilter){        // 为空说明之前没join过，需要在这里执行join的逻辑
            $notEverJoin = true;
            $filterClass = $metadata::filter();
            $relation = current(\common\library\object\object_relation\Helper::getUpstreamObjRelations($this->objName, [ObjectRelationConstant::MASTER_DETAIL]));
            $mainObjFilter = new $filterClass($this->clientId);
            $this->filter->initJoin();
            $this->filter->innerJoin($mainObjFilter)->on($this->filter->getTableName(), $relation['relation_field'], $mainObjFilter->getTableName(), $relation['relation_upstream_field']);

            // 由于联查的排序、分组和limit 与 单表查询的排序、分组和limit采用不同的方法调用，因此这里还需要将单表的排序、分组和limit用联查的方法再调一次
            // 这里有概率兜不住，兜不住的情况是用户设置了 order闭包 且出现表字段重名冲突
            if(!$joinScene){
                $orderClauses = $this->filter->getQuery()->getOrder()->getOrderClause();
                foreach($orderClauses as $orderClause){
                    $this->filter->joinOrder($orderClause->getColumn(),$orderClause->getDirection(), $this->filter->getMetadata()::table(), $orderClause->getNullOrder());
                }

                $orderClosures = $this->filter->getQuery()->getOrder()->getClosure();
                foreach($orderClosures as $closureColumn => $orderClosure){
                    $this->filter->joinOrderClosuer($closureColumn, $orderClosure);
                }

                $groupClauses = $this->filter->getQuery()->getGroup()->getGroup();
                foreach($groupClauses as $groupClause){
                    $this->filter->joinGroupBy($groupClause->getColumn(), $this->filter->getMetadata()::table());
                }

                $limitClause = $this->filter->getQuery()->getLimit();
                if($limitClause){
                    $this->filter->joinlimit($limitClause->getLimit(), $limitClause->getOffset());
                }
            }
        }

        $this->scopeUserFilter = $mainObjFilter;
        $groupClauses = $this->filter->getQuery()->getGroup()->getGroup();
        $groupClosure = $this->filter->getQuery()->getGroup()->getClosure();
        $columnClosure = $this->filter->getQuery()->getColumn()->getClosure();
        if($this->filter->isJoinSence() && !empty($this->joinGroup)){
            $groupClauses = array_merge($groupClauses, $this->joinGroup->getJoinQuery()->getGroup()->getGroup());
            $groupClosure = $groupClosure || $this->joinGroup->getJoinQuery()->getGroup()->getClosure();
        }
        if(empty($groupClauses) && empty($groupClosure) && empty($columnClosure)){
            $this->scopeUserFilter->select([FieldConstant::SCOPE_USER_IDS_FIELD_NAME]);
            $columnItems = $this->filter->getQuery()->getColumn()->getColumn();
            if($notEverJoin && empty($columnItems)){
                if($this->filter instanceof FilterV2){
                    $this->filter->selectAll();
                }else{
                    $this->filter->select($this->filter->getMetadata()->columnFields());
                }
            }
        }
        return $this->scopeUserFilter;
    }

}
