<?php

use common\modules\app\components\AppInfoService;
use libphonenumber\PhoneNumberUtil;
use xiaoman\orm\database\PgsqlUtil;

/**
 * Created by PhpStorm.
 * User: andy
 * Date: 2014/11/5
 * Time: 15:46
 */

class Util
{
    public static function devurandom()
    {
        $fp = @fopen('/dev/urandom','rb');
        if ($fp !== FALSE)
        {
            $pr_bits = @fread($fp,16);
            @fclose($fp);
            return base64_encode($pr_bits);
        }
        return false;
    }

    public static function getRealIpAddress()
    {
        if (isset($_SERVER['HTTP_X_FORWARDED_FOR']))
        {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
            $pos = strpos($ip, ',');
            if ($pos)
                $ip = substr($ip, 0, $pos);
            $ip = trim($ip);

            if (!empty($ip))
                return $ip;
        }

        if (isset($_SERVER['HTTP_WL_PROXY_CLIENT_IP']))
            return $_SERVER['HTTP_WL_PROXY_CLIENT_IP'];
        else if (isset($_SERVER['HTTP_X_REAL_IP']))
            return $_SERVER['HTTP_X_REAL_IP'];
        else
            return $_SERVER['REMOTE_ADDR']??'';
    }

    public static function getAllHeader()
    {
        $headers = [];
        foreach ($_SERVER as $name => $value)
        {
            if (substr($name, 0, 5) == 'HTTP_')
            {
                $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
            }
        }
        return $headers;
    }

    /**
     * 获取UserAgent
     * @return array
     */
    public static function getUserAgent()
    {
        $headers = self::getAllHeader();
        $userAgent = isset($headers['User-Agent']) ? $headers['User-Agent'] : (isset($headers['user-agent']) ? $headers['user-agent'] : '');

        if (empty($userAgent))
        {
            return null;
        }

        $ua = array();
        $ua['browser'] = $userAgent;
        $type = "";
        if (strpos($userAgent, 'MSIE') != false)
        {
            $ua['ua'] = 'IE';
            //$ua['version'] = self::findUserAgentByKey($userAgent, );
            $type = "MSIE";
        }
        else if (strpos($userAgent, 'Gecko/') != false)
        {
            $ua['ua'] = 'firefox';
            $type = 'Firefox/';
        }
        else if (strpos($userAgent, 'AppleWebKit/') != false)
        {
            if (strpos($userAgent, 'Chrome') != false)
            {
                $ua['ua'] = 'chrome';
                $type = 'Chrome/';
            }
            else
            {
                $ua['ua'] = 'safari';
                $type = 'Version/';
            }
        }
        else if (strpos($userAgent, 'Presto/') != false)
        {
            $ua['ua'] = 'opera';
            $type = 'Opera/';
        }
        else if (strpos($userAgent, 'Trident') != false)
        {
            $ua['ua'] = 'IE';
            $type = 'rv:';
        }
        else if (strpos($userAgent, 'MobileSafari') != false)
        {
            $ua['ua'] = 'MobileSafari';
            $type = 'MobileSafari/';
        }
        else
        {
            $ua['ua'] = 'unknown';
        }
        if ($type != '')
        {
            $ua['version'] = self::findUserAgentByKey($userAgent, $type);
        }
        return $ua;
    }

    /**
     * 根据ua取version
     * @param $ua
     * @param $key
     * @return string
     */
    public static  function findUserAgentByKey($ua, $key)
    {
        $ua .= " ";
        $len = strlen($key);
        $start = strpos($ua, $key);
        $pos = strpos($ua, ' ', $start + $len + 1);
        $version = substr($ua, $start + $len, $pos - $len - $start);
        return str_replace(array(';', ')'), '', $version);
    }

    /**
     * @param array CAtiveRecord $list
     * @return array
     */
    public static function arListToArray(array $list)
    {
        return array_map(function(CActiveRecord $r){return $r->getAttributes();}, $list);
    }

    //获取上传附件的url
    public static function getFileUrl($fileId)
    {
        $file = new AliyunUpload();
        $file->loadByFileId($fileId);
        return $file->generatePresignedUrl();
    }

    public static function plainText(string $content)
    {
        if(empty($content)) {
            return $content;
        }

        $content = str_replace('&nbsp;', ' ', $content);
        //去掉注释<!-- xxx -->
        $content = preg_replace("/<!--(.+?)-->/is", "", $content);
        //过滤:<style>css tag</style>
        $content = preg_replace('/<style[^>]*>(.+?)<\/style>/is', "", $content);
        //过滤:<script>javascript tag</script>
        $content = preg_replace('/<script[^>]*>(.+?)<\s*\/script>/is', "", $content);
        //过滤<p> <html>等尖括号标签
        $content = preg_replace('/<[^>]*>/is', "", $content);

        return trim($content);
    }

    public static function filterScript($str)
    {
        $str = preg_replace('/<(script.*?)>(.*?)<(\/script.*?)>/si', "", $str);//过滤script标签
        $str = preg_replace('/<(\/?script.*?)>/si', "", $str);//过滤script标签
        $str = preg_replace('/javascript/si', "Javascript", $str);//过滤script标签
        $str = preg_replace('/vbscript/si', "Vbscript", $str);//过滤script标签
        $str = preg_replace('/([\s\.])on([a-z]+)\s*=/si', "\\1no\\2=", $str);//过滤script标签
        return $str;
    }


    /**
     * 过滤非utf-8字符串
     * @param string $string
     * @return string
     */
    public static function filterUtf8String(string $string): string
    {
        if (!mb_check_encoding($string, 'UTF-8')) {
            $string = mb_convert_encoding($string, 'UTF-8');
        }
        return (string)$string;
    }

    public static function containChinese($str)
    {
        return preg_match("/[\x{4e00}-\x{9eff}]/u", $str);
    }

    public static function chineseLength($str)
    {
        $count = 0;
        for ($i = 0; $i < strlen($str); $i++)
        {
            $value = ord($str[$i]);
            if ($value > 127)
            {
                $count++;
                if ($value >= 192 && $value <= 223) $i++;
                elseif ($value >= 224 && $value <= 239) $i = $i + 2;
                elseif ($value >= 240 && $value <= 247) $i = $i + 3;
                else die('Not a UTF-8 compatible string');
            }
        }
        return $count;
    }


    public static function removeEmptyArray($arr){
        $data = array();
        if (is_array($arr)) {
            foreach ($arr as $val) {
                if (!empty($val)) {
                    $data[] = $val;
                }
            }
        } else {
            $data = trim($arr);
        }
        return $data;
    }


    /**
     * 格式化字节
     * @param int $bytes
     * @param int $precision
     * @return string
     */
    public static function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= (1 << (10 * $pow));

        return round($bytes, $precision) . ' ' . $units[$pow];
    }

    public static function removeNonAnsiChar($string)
    {
        return preg_replace('/[^\x20-\x7E]/', '', $string);
    }

    public static function escapeSql($value)
    {
        $value = str_replace("'", "''", $value);
        $value = str_replace("\\", "\\\\", $value);
        return $value;
    }

    public static function escapeDoubleQuoteSql($value)
    {
//        $value = str_replace('"', '""', $value);
//        $value = str_replace("\\", "\\\\", $value);
        return addcslashes(str_replace("'", "''", $value), "\000\n\r\\\032");
    }

    /**
     *
     * @param string $path
     * @param int $loopCount
     */
    public static function mkdir($path, $loopCount = 0)
    {
        $parent = dirname($path);
        if (!is_dir($parent)) {
            if ($parent == $path) {
                mkdir($parent);
            } elseif ($loopCount < 100) {
                $loopCount++;
                self::mkdir($parent, $loopCount);
            }

        }
        if (!is_dir($path)) {
            mkdir($path);
        }

    }

    /**
     * @param $ip
     * @return bool
     * 是否内网IP
     */
    public static function isInternalIp($ip)
    {
        $ip = ip2long($ip);
        if (!$ip) {
            return false;
        }
        $net_local = ip2long('***************') >> 24; //127.x.x.x
        $net_a = ip2long('**************') >> 24; //A类网预留ip的网络地址
        $net_b = ip2long('**************') >> 20; //B类网预留ip的网络地址
        $net_c = ip2long('***************') >> 16; //C类网预留ip的网络地址
        return $ip >> 24 === $net_local || $ip >> 24 === $net_a || $ip >> 20 === $net_b || $ip >> 16 === $net_c;
    }

    /**
     * @param $color
     * @return string
     * 将 RGB转化成16进制
     */
    public static function colorRgbTo16($color, $errorDefault = '#666666'){
        $regexp = "/^rgb\(([0-9]+)\,\s*([0-9]+)\,\s*([0-9]+)\)/";
        preg_match($regexp, $color, $match);
        if (empty($match)) return $errorDefault;
        array_shift($match);
        $hexColor = "#";
        $hex = array('0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f');
        for ($i = 0; $i < 3; $i++) {
            $r = null;
            $c = $match[$i];
            if ($c > 255) $c = 255;
            $hexAr = array();
            while ($c >= 16) {
                $r = $c % 16;
                $c = ($c / 16) >> 0;
                array_push($hexAr, $hex[$r]);
            }
            array_push($hexAr, $hex[$c]);
            $ret = array_reverse($hexAr);
            $item = implode('', $ret);
            $item = str_pad($item, 2, '0', STR_PAD_LEFT);
            $hexColor .= $item;
        }
        return $hexColor;
    }
    /**
     * 返回前端设置好的设备id
     * @return string
     */
    public static function getDeviceId(){
        if( isset($_SERVER['HTTP_XIAOMAN_DEVICE_ID']))
            return $_SERVER['HTTP_XIAOMAN_DEVICE_ID'];

        return  $_COOKIE['fingerprint']??'';
    }

    /**
     * 获取设备类型
     */
    public static function getDeviceType(){
        $deviceType = 'other';
        if(isset($_SERVER['HTTP_USER_AGENT'])){
            $agent = $_SERVER['HTTP_USER_AGENT'];
            if(stripos($agent, 'Windows')){
                $deviceType = 'windows';
            }elseif(stripos($agent,'iPhone') || stripos($agent,'iPod') || stripos($agent, 'iPad')){
                $deviceType = 'ios';
            }elseif(stripos($agent, 'mac')){
                $deviceType = 'mac';
            }elseif(stripos($agent, 'Android')){
                $deviceType = 'android';
            }
        }
        return $deviceType;
    }

    public static function numericString($string)
    {
        return preg_replace("/[^0-9]/","",$string);
    }

    //过滤中文和除Xx之外的字符
    public static function filterPhoneNumber($string)
    {
        return preg_replace("/[\x{4e00}-\x{9fa5}A-WY-Za-wy-z]/u","",$string);
    }

    /**
     * (032) 123456
     * 032 123456
     * 032-123456
     * @param $tel
     * @return array
     */
    public static function formatTel($tel)
    {
        $tel = trim($tel);

        if (strpos($tel, '(') === 0 && ($pos = strpos($tel, ')')) !== false)
        {
            $areaCode = substr($tel, 1, $pos - 1);
            $tel = substr($tel, $pos + 1);
        }
        else
        {
            $pos1 = strpos($tel, '-');
            $pos2 = strpos($tel, ' ');

            if ($pos1 !== false && $pos2 !== false)
            {
                $pos = $pos1 < $pos2 ? $pos1 : $pos2;

                $areaCode = substr($tel, 0, $pos);
                $tel = substr($tel, $pos + 1);
            }
            else if ($pos1 !== false || $pos2 !== false)
            {
                $pos = $pos1 !== false ? $pos1 : $pos2;

                $areaCode = substr($tel, 0, $pos);
                $tel = substr($tel, $pos + 1);
            }
            else
            {
                $areaCode = '';
            }
        }

        return [Util::numericString($areaCode), Util::numericString($tel)];
    }

    public static function formatTelList($telListStr)
    {
        $telListStr = trim(strtolower($telListStr));
        $length = strlen($telListStr);
        $pos = 0;
        $result = [];
        for ($i = 0; $i < $length; ++$i)
        {
            if ($telListStr[$i] === ';' || $telListStr[$i] === '/')
            {
                $str = substr($telListStr, $pos, $i - $pos);
                if (!empty(trim($str)) && !empty(Util::numericString($str)))
                    $result[] = Util::formatTel($str);
                $pos = $i + 1;
            }
            else if ($telListStr[$i] === 'o' && $i + 1 < $length && $telListStr[$i+1] === 'r')
            {
                $str = substr($telListStr, $pos, $i - $pos);
                if (!empty(trim($str))  && !empty(Util::numericString($str)))
                    $result[] = Util::formatTel($str);
                $pos = $i + 2;
            }
        }

        if ($pos < $length && !empty($str = trim(substr($telListStr, $pos))))
            $result[] = Util::formatTel($str);

        return $result;
    }

    public static function formatChineseDate($dateString)
    {
        $dateLang = array('Fri' => '星期五', 'Thu' => '星期四', 'Wed' => '星期三', 'Tue' => '星期二', 'Mon' => '星期一', 'Sun' => '星期日', 'Sat' => '星期六', 'AM' => '上午', 'am' => '上午', 'PM' => '下午', 'pm' => '下午',);
        foreach ($dateLang as $en => $cn) {
            $dateString = str_replace($en, $cn, $dateString);
        }
        return $dateString;
    }

    public static function checkAppVersion($curlVersion, $minVersion = '', $maxVersion = ''){
        //没发现版本号，当成网页端范围处理
        if(is_null($curlVersion) )
            return true;
        if(empty($minVersion) && empty($maxVersion)){
            return true;
        }

        $minVersionArray = !empty($minVersion) ? explode('.', $minVersion) : [];
        $maxVersionArray = !empty($maxVersion) ? explode('.', $maxVersion) : [];
        $curlVersionArray = explode('.', $curlVersion);

        //version 格式1.10.0;
        //第一位
        if(($maxVersionArray[0]??9999) < $curlVersionArray[0]){
            return false;
        }
        if(($minVersionArray[0]??0) > $curlVersionArray[0]){
            return false;
        }

        if(($maxVersionArray[0]??9999) == $curlVersionArray[0]){
            if($maxVersionArray[1] > $curlVersionArray[1]){
                return true;
            }
            if($maxVersionArray[2] > $curlVersionArray[2]){
                return true;
            }
            return false;
        }elseif(($minVersionArray[0]??0) == $curlVersionArray[0]){
            if($minVersionArray[1] < $curlVersionArray[1]){
                return true;
            }
            if($minVersionArray[2] < $curlVersionArray[2]){
                return true;
            }
            return false;
        }
        return true;
    }

    public static function redisHashToArray(array  $cache)
    {
        $arr= [];
        for ( $i=0 ; $i < count($cache); $i+=2)
        {
            $arr[$cache[$i]] =  $cache[$i+1];
        }

        return $arr;
    }


    /**
     * 判断是否日期格式
     * @param string $date
     * @return boolean
     */
    public static function isDate($date,$checkYearMonth = false)
    {
        if(!$date)
            return false;

        if (!is_string($date))
            return false;

        if($checkYearMonth){
            if( date('Y-m', strtotime($date)) == $date){
                return true;
            }else{
                return false;
            }
        }else{
            if( date('Y-m-d', strtotime($date)) == $date){
                return true;
            }else{
                return false;
            }
        }

    }


    /**
     * 二维数组排序
     * @param array $data 要处理的数组
     * @param string $key 排序的字段
     * @param string $type 排序的类型 SORT_DESC|SORT_ASC|SORT_STRING
     * @return boolean
     */
    public static function array_sort(array $data, $key, $type = SORT_DESC) {

        array_multisort(array_column($data,$key),$type,$data);
        return $data;

    }

    /**
     * 折算时间戳为【日】,四舍五入
     * @param $time
     * @return string
     */
    public static function formatTimeToDay($time){
        $time = intval($time);
        //不到一天，折算为1天
        if(!$time){
            $day = 0;
        }else if($time <= 86400){
            $day = 1;
        } else{
            $day = round($time/86400);
        }
        return $day;
    }

    /**
     * 折算时间戳为【日-时】
     * @param $time
     * @return string
     */
    public static function formatTimeToDayHours($time){
        $time = intval($time);
        if($time<1){
            return '0小时';
        }
        $dateStr = "";
        $preDay = "";
        $preHours = "";
        $day = $time / 86400;
        if($day>=1){
            $preDay = intval($day)."天";
        }
        $hours = $time % 86400 ;
        if($hours > 0){
            $hours  = $hours/ 3600;
            $preHours = round($hours)."小时";
        }
        $dateStr = $preDay . $preHours;
        return $dateStr;
    }

    /**
     * 折算时间戳为【小时】
     * @param $time
     * @return string
     */
    public static function formatTimeToHours($time){

        $hoursStr = 0;
        $hour = $time / 3600;

        if($hour == 0 || is_int($hour)){
            $hoursStr  = $hour;
        }else if($hour < 1){
            if(intval($hour * 100) >= 50){
                $hoursStr =1;
            }else{
                $hoursStr = 0;
            }

        }else if($hour >1 ){
            $hour = intval($hour);
            $preHour = $time % 3600 / 60;
            if($preHour >= 30){
                $hour += 1;
            }
            $hoursStr = $hour;
        }
        return $hoursStr;
    }
    /**
     * 折算时间戳为【日-时-分】
     * @param $time
     * @return string
     */
    public static function formatTimeToDate($time){

        $dateStr = "";
        $preDay = "";
        $preHours = "";
        $preMinutes = "";

        if($time >= 86400){
            $day = $time / 86400;
            if(intval($day)> 0){
                $preDay = intval($day)."天";
            }
        }

        $hours = $time % 86400 ;
        if($hours >= 1) {
            $hours = $hours / 3600;
            if ($hours > 1) {
                $preHours = round($hours)."小时";
            }
        }

        if($time >= 86400){
            $minutes  = $time /86400/3600;
        }else if ($time < 86400 && $time >= 3600){
            $minutes  = $time /3600;
        }else{
            $minutes  = $time /60;
        }
        if(intval($minutes) > 1){
            $preMinutes = intval($minutes) . "分";
        }
        $dateStr = $preDay . $preHours . $preMinutes;
        return $dateStr;
    }


    /**
     * 提取url 的domain
     * @param $url
     * @return null|string
     */
    public static function  getUrlBaseDomain(string $url){

        $stateDomain = [
            'al','dz','af','ar','ae','aw','om','az','eg','et','ie','ee','ad','ao','ai','ag','at','au','mo','bb','pg','bs','pk','py','ps','bh','pa','br','by','bm','bg','mp','bj','be','is','pr','ba','pl','bo','bz','bw','bt','bf','bi','bv','kp','gq','dk','de','tl','tp','tg','dm','do','ru','ec','er','fr','fo','pf','gf','tf','va','ph','fj','fi','cv','fk','gm','cg','cd','co','cr','gg','gd','gl','ge','cu','gp','gu','gy','kz','ht','kr','nl','an','hm','hn','ki','dj','kg','gn','gw','ca','gh','ga','kh','cz','zw','cm','qa','ky','km','ci','kw','cc','hr','ke','ck','lv','ls','la','lb','lt','lr','ly','li','re','lu','rw','ro','mg','im','mv','mt','mw','my','ml','mk','mh','mq','yt','mu','mr','us','um','as','vi','mn','ms','bd','pe','fm','mm','md','ma','mc','mz','mx','nr','np','ni','ne','ng','nu','no','nf','na','za','aq','gs','eu','pw','pn','pt','jp','se','ch','sv','ws','yu','sl','sn','cy','sc','sa','cx','st','sh','kn','lc','sm','pm','vc','lk','sk','si','sj','sz','sd','sr','sb','so','tj','tw','th','tz','to','tc','tt','tn','tv','tr','tm','tk','wf','vu','gt','ve','bn','ug','ua','uy','uz','es','eh','gr','hk','sg','nc','nz','hu','sy','jm','am','ac','ye','iq','ir','il','it','in','id','uk','vg','io','jo','vn','zm','je','td','gi','cl','cf','cn','yr','com','arpa','edu','gov','int','mil','net','org','biz','info','pro','name','museum','coop','aero','xxx','idv','me','mobi','asia','ax','bl','bq','cat','cw','gb','jobs','mf','rs','su','sx','tel','travel'
        ];

        if (!preg_match("/^http\:\/\//is", $url)){
            $url="http://".$url;
        }

        $urlParse = parse_url(strtolower($url));
        if (!isset($urlParse['host'])) {
            return $urlParse;
        }

        $urlArr = explode(".", $urlParse['host']);
        $count = count($urlArr);

        if($count<=1) {
            return '';
        }

        $domain = '';
        if($count == 2){
            #当域名直接根形式不存在host部分直接输出
            $domain = $urlParse['host'];
        }elseif($count > 2){
            $last = array_pop($urlArr);
            $last_1 = array_pop($urlArr);
            $last_2 = array_pop($urlArr);

            if(in_array($last_1, $stateDomain)){
                $domain = $last_2.'.'.$last_1.'.'.$last;
            }elseif(in_array($last, $stateDomain)){
                $domain=$last_1.'.'.$last;
            }else{
                $domain = $last_1.'.'.$last;
            }
        }
        return $domain;
    }

    /**
     * @param $haystack
     * @param $needle
     * @return bool|int
     */
    public static function striposArray($haystack, array $needle) {
        foreach($needle as $what) {
            if(($pos = stripos($haystack, $what))!==false) return $pos;
        }
        return false;
    }

    /**
     * @return string
     */
    public static function getServerIpAddress()
    {
        return $_SERVER['SERVER_ADDR']??gethostbyname(gethostname());
    }

    public static function logInfo($key, $info)
    {
        echo "[    $key    ] ---- ";
        print_r($info);
        echo "\n";
    }

    public static function batchLogInfo(array $paramsArr, $needPre = true)
    {
        if ($needPre) echo "<pre>";
//        self::logInfo('currentTime', date('Y-m-d H:i:s'));
        foreach ($paramsArr as $key => $value) {
            self::logInfo($key, $value);
        }
    }

    public static function convertSizeUnit(int $size, $unit = "G")
    {
        return match ($unit) {
            "MB" => $size / 1024 / 1024,
            "G" => $size / 1024 / 1024 / 1024,
            default => $size
        };
    }

    /**
     * 根据多项key对多维数组进行排序.类似order by 多项
     * @param array $arr
     * @param array $sortOption
     * @return array|mixed
     * $sortOption = [
     *      'unread_count' => SORT_DESC,
     *      'receive_time' => SORT_DESC,
     * ];
     * $arr = [
     *      [
     *          'receive_time' => 0,
     *          'unread_count' => 200,
     *      ],
     * ];
     */
    public static function sortByManyFields(array $arr, array $sortOption)
    {
        if (empty($arr) || empty($sortOption)) return [];
        $data = [];
        $args = [];
        foreach ($arr as $index => $row) {
            foreach ($sortOption as $key => $sortOrder) {
                $data[$key][$index] = $row[$key];
            }
        }
        foreach ($sortOption as $key => $sortOrder) {
            $args[] = $data[$key];
            $args[] = $sortOrder;
        }
        $args[] = &$arr;
        array_multisort(...$args);
        return array_pop($args);
    }

    public static function getTmpFilePath()
    {
        if (is_dir(Constants::TMP_FILE_PATH)) {
            return Constants::TMP_FILE_PATH;
        }


        try {
            throw new \RuntimeException(\Yii::t('common', '{filePath}Folder not found', ['{filePath}' => '/data/tmpfile/']));
        } catch (Exception $e) {
            \common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());
            return sys_get_temp_dir();
        }

    }

    /**
     * 过滤emoji表情
     * @param $str
     * @return mixed
     */
    public static function unEmoji($str)
    {
        if (!is_string($str))
            return $str;

        $str = preg_replace_callback('/./u', function (array $match) {
            return strlen($match[0]) >= 4 ? '' : $match[0];
        }, $str);
        return $str;
    }

    /**
     * 替换不可打印字符
     * @param string $subject 原字符串
     * @param string $replacement 替换目的字符，默认替换为空格
     * @return string 替换后的字符串
     */
    public static function replaceNonPrintableTo(string $subject, string $replacement = ' '): string
    {
            $regex = '/[\x00-\x1F]/';   // https://zh.wikipedia.org/wiki/控制字符
            return (string) preg_replace($regex, $replacement, $subject);
    }

    /**
     * @param string $subject
     * @return string
     */
    public static function replaceBrToLinebreak(string $subject)
    {
        return preg_replace('/<br\s*\/?>/i', "\n", $subject);
    }

    /**
     * Hashids encode
     * @param $ids
     * @param string $alphabetName
     * @return string
     */
    public static function encodeId($ids, string $alphabetName = Constants::HASHIDS_DEFAULT_ALPHABET): string
    {
        $ids = (array)$ids;
        $hashids = self::newHashIdsFromConfig($alphabetName);
        return $hashids->encode(...$ids);
    }

    /**
     * Hashids decode
     * @param string $hash
     * @param string $alphabetName
     * @return mixed
     */
    public static function decodeId(string $hash, string $alphabetName = Constants::HASHIDS_DEFAULT_ALPHABET)
    {
        $hashids = self::newHashIdsFromConfig($alphabetName);
        return $hashids->decode($hash);
    }

    /**
     * @param string $alphabetName
     * @return \Hashids\Hashids
     */
    public static function newHashIdsFromConfig(string $alphabetName = Constants::HASHIDS_DEFAULT_ALPHABET): \Hashids\Hashids
    {
        $config = Yii::app()->params['hashids'] ?? [];
        if (!isset($config['salt'], $config['minLength'], $config['alphabet'])) {
            throw new \RuntimeException(\Yii::t('commpn', 'Missing hashids config'));
        }
        if (empty($config['alphabet'][$alphabetName])) {
            throw new \RuntimeException(\Yii::t('common', 'Alphabet name error'));
        }

        return new \Hashids\Hashids($config['salt'], $config['minLength'], $config['alphabet'][$alphabetName]);
    }

    /**
     * 处理文件名
     */
    public static function filenameSanitizer(string $unsafeFilename): string
    {
        $dangerousCharacters = ['"', "'", "&", "/", "\\", "?", "#"];
        return str_replace($dangerousCharacters, '_', $unsafeFilename);
    }

    /**
     * 向URL增加query参数
     * @param string $url
     * @param array $params
     * @return string
     */
    public static function appendQueryParamsToUrl(string $url, array $params): string
    {
        if (!$params) {
            return $url;
        }

        $urlInfo = parse_url($url);
        $queryInfo = [];

        parse_str($urlInfo['query'] ?? '', $queryInfo);

        $queryInfo = array_replace($queryInfo, $params);

        $urlInfo['query'] = http_build_query($queryInfo);

        $scheme = '';
        if (!empty($urlInfo['scheme'])) {
            $scheme = "{$urlInfo['scheme']}://";
        }

        return sprintf('%s%s%s?%s', $scheme, $urlInfo['host'] ?? '', $urlInfo['path'] ?? '', $urlInfo['query']);
    }
    /**
     * 解析uri链接对应的文件名
     *
     * @param string $uri
     * @return string
     */
    public static function getFilenameFromUri(string $uri): string
    {
        return basename(parse_url($uri, PHP_URL_PATH));
    }

    /***
     * 解析数据库连接dns
     * @param string $connectString 'mysql:dbname=mydatabase;host=127.0.0.1;charset=GBK;'
     * @return array{host: string, port: string, driver: string}
     */
    public static function parseDSN(string $connectString): array
    {
        $result = ['driver' => 'mysql', 'host' => 'localhost', 'port' => '3306'];
        if (($pos = strpos($connectString, ':')) !== false) {
            $result['driver'] = strtolower(substr($connectString, 0, $pos));
            $connectString = substr($connectString, $pos + 1);
        }

        $kvs = explode(';', $connectString);
        foreach ($kvs as $kv) {
            $kv = explode('=', $kv);
            if (count($kv) !== 2) {
                continue;
            }

            $result[$kv[0]] = $kv[1];
            if ($kv[0] === 'host') {
                $hostPort = explode(':', $kv[1]);
                $result['host'] = $hostPort[0];
                isset($hostPort[1]) && $result['port'] = $hostPort[1];
            }
        }
        return $result;
    }

    /***
     * 判断有向图(邻接表形式)是否有环
     * @param array $graph  图
     * @return bool
     */
    public static function graphHasCycle($graph){
        $hasCycleMap = [];      // 记录从某个节点出发的子图是否成环（用这个map记录走过的路径可以大大减小算法复杂度）
        foreach($graph as $nodeId => $_){
            $visited = [];  // 记录以 $nodeId 为出发点经过的路径
            if($hasCycle = self::iterGraph($graph, $nodeId,$visited, $hasCycleMap)){
                return $hasCycle;
            }
        }

        return false;
    }

    /***
     * 拓扑排序
     *
     * @param array $graph  图
     * @return bool
     */
    public static function topoSort($graph){
        $hasCycleMap = [];      // 记录从某个节点出发的子图是否成环（用这个map记录走过的路径可以大大减小算法复杂度）
        $sortResult = [];
        foreach($graph as $nodeId => $_){
            $visited = [];  // 记录以 $nodeId 为出发点经过的路径
            if($hasCycle = self::iterGraph($graph,$nodeId, $visited, $hasCycleMap,  $sortResult)){
                return [true, []];
            }
        }

        // 第一个元素表示是否有循环依赖
        return [false, $sortResult];
    }

    // 遍历图
    public static function iterGraph(&$graph, $cur, &$visited=[], &$hasCycleMap=[], &$sortResult = []){
        if (!empty($visited[$cur])){
            \LogUtil::info("iterGraph存在依赖,cur={$cur},visited=" . var_export($visited[$cur], true));
            return $cur;
        }

        if (isset($hasCycleMap[$cur])){
            return $hasCycleMap[$cur];
        }

        // 经过 $cur 节点
        $hasCycle = false;
        $visited[$cur] = true;
        foreach ($graph[$cur] ?? [] as $neighbor){  // 遍历相邻节点
            $hasCycle = self::iterGraph($graph, $neighbor, $visited,  $hasCycleMap, $sortResult);
            if($hasCycle){
                break;
            }
        }

        // 离开 $cur 节点
        $sortResult[] = $cur;
        $visited[$cur] = false;
        $hasCycleMap[$cur] = $hasCycle;
        return $hasCycle;
    }
    /**
     * 文件名超过限制保留后缀
     *
     * @param string $fileName
     * @param int $len
     * @return string
     */
    public static function getFilenameRetainExt(string $fileName, int $len): string
    {
        if(mb_strlen($fileName) <= $len){
            return $fileName;
        }

        $ext = '';
        $fileExt = pathinfo($fileName,PATHINFO_EXTENSION);
        if(!empty($fileExt)){
            $len = $len-(mb_strlen($fileExt)+1);
            $ext = '.'.$fileExt;
        }
        return mb_substr($fileName,0, $len).$ext;
    }

    /**
     * 清除富文本格式
     * @param  string $string 字符串
     * @return string
     */
    public static function cleanHtml(string $string): string
    {
        $string = htmlspecialchars_decode($string);
        $string = strip_tags($string,"<br>");
        $string = preg_replace('/<br\\s*?\/??>/i',"\n",$string);
        $string = str_replace("&nbsp;", " ", $string);
        $string = str_replace("&ldquo;", "\"", $string);
        $string = str_replace("&rdquo;", "\"", $string);
        return trim($string);
    }

    /**
     * 检查sns号码数据是否正常
     * @param $contactList
     * @return array
     */
    public static function checkSnsContacts($contactList, ?array $filterCountryCodes = null): array
    {
        $snsIds = array_filter(array_column($contactList, 'sns_id'));
        if (empty($snsIds)) {
            return [
                'valid_count' => 0,
                'invalid_count' => 0,
                'repeat_count' => 0,
                'contact_list' => $contactList,
            ];
        }

        $repeatMap = [];
        $validCount = $invalidCount = 0;

        foreach ($contactList as $key => $contact) {
            $validResult = Util::parsePhoneNumber($contact['sns_id']);
            $contact['sns_id'] = $validResult['raw_input'];
            $contact['wa_id'] = '';
            $isValid = $validResult['is_valid']??false;
            if ($isValid) {
                $contact['wa_id'] = ($validResult['country_code']??'0').($validResult['national_number']??'0');

                $contact['country_code'] = $validResult['country_code'] ?? '';
                $contact['national_number'] = $validResult['national_number'] ?? '';
                $contact['region_code'] = $validResult['region_code'] ?? '';
                $contact['international_number'] = $validResult['international_number'] ?? '';

                if ($filterCountryCodes && in_array($contact['region_code'], $filterCountryCodes)) {
                    $invalidCount++;
                    $contact['status'] = 'invalid';
                } else {
                    $validCount++;
                    $contact['status'] = 'valid';
                }
                $repeatMap[$contact['wa_id']] = ($repeatMap[$contact['wa_id']] ?? 0) + 1;
            } else {
                $invalidCount++;
                $contact['status'] = 'invalid';
            }

            $contactList[$key] = $contact;
        }
        $repeatMap = array_filter($repeatMap, function ($item) {
            return $item > 1;
        });

        $repeatCount = 0;
        foreach ($repeatMap as $repeat) {
            $repeatCount += $repeat - 1;
        }

        return [
            'valid_count' => $validCount,
            'invalid_count' => $invalidCount,
            'repeat_count' => $repeatCount,
            'contact_list' => $contactList,
        ];
    }

    public static function flattenArray($arr)
    {
        $result = [];
        foreach ($arr as $k => $v) {
            if (is_array($v)) {
                $result = array_merge($result, self::flattenArray($v));
            } else {
                $result[] = $v;
            }
        }
        return $result;
    }

    public static function conversionMemory($size)
    {
        $unit=array('b','kb','mb','gb','tb','pb');
        return @round($size/pow(1024,($i=floor(log($size,1024)))),2).' '.$unit[$i];
    }
    /**
     * 截断HTML，根据显示长度截断
     * @param string $html
     * @param int $maxPlainTextLen
     * @param bool $withoutHTMLAndBody
     * @return string
     */
    public static function truncateHTMLByPlainText(string $html, int $maxPlainTextLen, bool $withoutHTMLAndBody = false): string
    {
        $dom = new DOMDocument();

        $options = LIBXML_NOCDATA | LIBXML_COMPACT | LIBXML_NOENT;
        if ($withoutHTMLAndBody) {
            $options |= LIBXML_HTML_NOIMPLIED;
            $options |= LIBXML_HTML_NODEFDTD;
        }

        // 编码声明
        $utf8 = '<?xml encoding="UTF-8">';

        try {
            @$dom->loadHTML($utf8 . $html, $options);
        } catch (\Throwable $exception) {
            \LogUtil::info("[TruncateHTML] DOMDocument::loadHTML {$exception->getMessage()}");
        }

        $hasTruncate = false;
        $truncateNode = static function (?DOMNode $node) use (&$truncateNode, &$maxPlainTextLen, &$hasTruncate) {
            if (null === $node) {
                return;
            }

            switch ($node->nodeType) {
                case XML_ELEMENT_NODE:
                case XML_DOCUMENT_TYPE_NODE:
                case XML_DOCUMENT_NODE:
                case XML_HTML_DOCUMENT_NODE:
                    // DOM是动态变化的，先全部取出来
                    $childNodes = [];
                    foreach ($node->childNodes as $childNode) {
                        $childNodes[] = $childNode;
                    }
                    foreach ($childNodes as $childNode) {
                        if ($maxPlainTextLen <= 0) {
                            $node->removeChild($childNode);
                            continue;
                        }
                        $truncateNode($childNode);
                    }
                    break;
                case XML_TEXT_NODE:
                case XML_CDATA_SECTION_NODE:
                    /** @var \DOMText|\DOMCharacterData $node */
                    $len = mb_strlen($node->wholeText);
                    if ($len >= $maxPlainTextLen && $node->parentNode) {
                        $newTextNode = $node->parentNode->ownerDocument->createTextNode(mb_substr($node->wholeText, 0, $maxPlainTextLen));
                        $node->parentNode->replaceChild($newTextNode, $node);
                        $hasTruncate = true;
                    }
                    $maxPlainTextLen -= $len;
                    break;
            }
        };
        $truncateNode($dom);

        if ($hasTruncate) {
            $html = trim(substr($dom->saveHTML(), strlen($utf8)));
        }

        return $html;
    }


    public static function printNowMemory($key)
    {
        \Util::batchLogInfo([
            $key => self::conversionMemory(memory_get_usage()),
        ], true);
    }

    public static function removeCountryFlagEmoji($text)
    {
        return preg_replace('/[\x{1F1E6}-\x{1F1FF}]/u', '', $text);
    }

    public static function tryJsonDecode(string $json)
    {
        $data = [];
        $err = null;
        try {
            $data = json_decode($json, true, 512, JSON_THROW_ON_ERROR);
        } catch (JsonException $e) {
            $err = $e->getMessage();
        }
        return [is_array($data) ? $data : [], $err];
    }

    public static function parsePhoneNumber(string $rawInput): array
    {
        $phoneUtil = PhoneNumberUtil::getInstance();

        $result = ['raw_input' => $rawInput];
        try {
            $input = '+' . str_replace([' ', '-'], ['', ''], ltrim($rawInput, '+'));
            $phoneNumber = $phoneUtil->parse($input);
            if (empty($phoneNumber)) {
                return $result;
            }

            //时区
            $result['timezone'] = 8;
            $regionCode = $phoneUtil->getRegionCodeForNumber($phoneNumber);
            if ($regionCode){
                $timezones = DateTimeZone::listIdentifiers(DateTimeZone::PER_COUNTRY, $regionCode);
                $timezone = $timezones[0] ?? 'Asia/Shanghai';

                //获取时区的偏移量
                $dateTimeZone = new DateTimeZone($timezone);
                $datetime = new DateTime('now', $dateTimeZone);
                $offsetInSeconds = $dateTimeZone->getOffset($datetime);
                $result['timezone'] = $offsetInSeconds / 3600;
            }

            $result['country_code'] = (string) $phoneNumber->getCountryCode();
            $result['national_number'] = (string) $phoneNumber->getNationalNumber();
            $result['is_valid'] = $phoneUtil->isValidNumber($phoneNumber);
            $result['region_code'] = $phoneUtil->getRegionCodeForNumber($phoneNumber);
            $result['international_number'] = $phoneUtil->format($phoneNumber, \libphonenumber\PhoneNumberFormat::INTERNATIONAL);

            // 巴西号码特殊处理
            // 手机号码在区号后面有可能没加9，导致校验失败，做下容错处理
            if ($result['country_code'] == '55' && !$result['is_valid']) {
                // 手机号码由12数字和加号组成
                if (strlen($input) == 12 + 1) {
                    $input = substr_replace($input, '9', 5, 0);
                    $phoneNumber = $phoneUtil->parse($input);
                    if (empty($phoneNumber)) {
                        return $result;
                    }
                    $result['country_code'] = (string) $phoneNumber->getCountryCode();
                    $result['national_number'] = (string) $phoneNumber->getNationalNumber();
                    $result['is_valid'] = $phoneUtil->isValidNumber($phoneNumber);
                    $result['international_number'] = $phoneUtil->format($phoneNumber, \libphonenumber\PhoneNumberFormat::INTERNATIONAL);
                }
            }
        } catch (\Throwable $e) {
            $result['is_valid'] = false;
        }
        return $result;
    }
    public static function fieldPack($fieldVal, $dataFieldType, $allowNull=false){
        if(is_null($fieldVal) || $fieldVal == \xiaoman\orm\common\DomainObject::XM_NULL){
            return null;
        }

        switch ($dataFieldType)
        {
            case \xiaoman\orm\common\DataMapping::PG_ARRAY:
            case \xiaoman\orm\common\DataMapping::PG_STRING_ARRAY:
                $fieldVal = $fieldVal === '' ? [] : $fieldVal;
                $fieldVal = is_string($fieldVal) ? $fieldVal : PgsqlUtil::packArray($fieldVal);
                break;
            case \xiaoman\orm\common\DataMapping::PG_BSON:
                $fieldVal = $fieldVal === '' ? [] : $fieldVal;
                $fieldVal = is_string($fieldVal) ? $fieldVal : json_encode(empty($fieldVal) ? (new \stdClass()) : $fieldVal);
                break;
            case \xiaoman\orm\common\DataMapping::MYSQL_JSON_STRING:
                $fieldVal = empty($fieldVal) ? '' : json_encode($fieldVal);
                break;
            case \xiaoman\orm\common\DataMapping::INT:
                $fieldVal = (!is_numeric($fieldVal) || empty($fieldVal)) ? intval($fieldVal) : $fieldVal;
                break;
            case \xiaoman\orm\common\DataMapping::FLOAT:
                $fieldVal = (!is_numeric($fieldVal) || empty($fieldVal)) ? floatval($fieldVal) : $fieldVal;
                break;
            case \xiaoman\orm\common\DataMapping::DATETIME:
                $fieldVal = $fieldVal === '' ? ($allowNull ? null : '1970-01-01 00:00:00') : $fieldVal;
                break;
            case \xiaoman\orm\common\DataMapping::DATE:
                $fieldVal = $fieldVal === '' ? ($allowNull ? null : '1970-01-01') : $fieldVal;
                break;
        }

        return $fieldVal;
    }

    public static function fieldUnpack($fieldVal, $dataFieldType, $dbFieldType='')
    {
        if(is_null($fieldVal) || $fieldVal == \xiaoman\orm\common\DomainObject::XM_NULL){
            return null;
        }

        if(in_array($dbFieldType, ['timestamp','dateTime','date']) && $fieldVal === ''){
            return null;
        }

        switch ($dataFieldType)
        {
            case \xiaoman\orm\common\DataMapping::PG_ARRAY:
                $fieldVal = $fieldVal === '' ? [] : $fieldVal;
                $fieldVal = !is_string($fieldVal) ? $fieldVal : PgsqlUtil::unpackArray($fieldVal);
                break;
            case \xiaoman\orm\common\DataMapping::PG_STRING_ARRAY:
                $fieldVal = !is_string($fieldVal) ? $fieldVal : PgsqlUtil::unpackStringArray($fieldVal);
                break;
            case \xiaoman\orm\common\DataMapping::PG_BSON:
                $fieldVal = $fieldVal === '' ? [] : $fieldVal;
                $fieldVal = !is_string($fieldVal) ? $fieldVal : json_decode($fieldVal, true);
                break;
            case \xiaoman\orm\common\DataMapping::MYSQL_JSON_STRING:
                $fieldVal = empty($fieldVal) ? [] : json_decode($fieldVal, true);
                break;
            case \xiaoman\orm\common\DataMapping::INT:
                $fieldVal = (is_numeric($fieldVal)|| empty($fieldVal)) ? intval($fieldVal) : $fieldVal;
                break;
            case \xiaoman\orm\common\DataMapping::FLOAT:
                $fieldVal = (is_numeric($fieldVal) || empty($fieldVal)) ? floatval($fieldVal) : $fieldVal;
                break;
        }

        return $fieldVal;
    }

    public static function isPersonalVersion()
    {
        $host = $_SERVER['HTTP_HOST'] ?? '';
        $personalHost = \Yii::app()->params['host']['okki_personal_domain'] ?? '';
        if ($host == $personalHost) {
            return true;
        }

        $okkiFreeHost = \Yii::app()->params['host']['okki_free_domain'] ?? '';
        $okkiFreeGlobalHost = \Yii::app()->params['host']['okki_free_global_domain'] ?? '';
        return in_array($host, [$okkiFreeHost, $okkiFreeGlobalHost]);
    }

    public static function isApp($version = 0, $equal = true)
    {
        if (empty(AppInfoService::appRequestInfo()->appVersion) && empty(AppInfoService::appRequestInfo()->newAppVersion))
            return false;

        if (empty($version))
            return true;

        return AppInfoService::versionGreater($version, $equal);
    }

    public static function getLanguage()
    {
        static $cachedLang = null;

        if ($cachedLang === null) {
            $cachedLang = 'zh_CN';
            if (\User::isLogin()) {
                $cachedLang = (\User::getLoginUser())->getLanguage();
            }
        }

        return $cachedLang;
    }

    // 在做比如字段重命名需要裸写update或者delete sql的时候，为了预防因为where 条件写少导致没有必要的变更导致数据丢失（主要是测试环境）
    // 在update之前先查一下看影响条数是否符合预期
    // 使用示例
    /*
     * 更新：
        $mysqlDb = \ProjectActiveRecord::getDbByClientId($this->clientId);
        \Util::safeUpdate(
            $mysqlDb,
            " id = 'refer_inquiry_collaboration_no' and client_id=:client_id AND type=:type" ,
            $updateCustomFieldParams,
            "select * from tbl_custom_field ",
            "UPDATE tbl_custom_field SET name ='关联询价任务编号' ",
            1
        );

        删除：
        $mysqlDb = \ProjectActiveRecord::getDbByClientId($this->clientId);
        \Util::safeUpdate(
            $mysqlDb,
            "client_id=$this->clientId and  type=$type and id = '$id'",
            [],
            "select * from tbl_custom_field",
            "delete from tbl_custom_field",
            1
        );

    * */
    public static function safeUpdate($db, $where, $params, $select, $update, $expectedAffectedRowNum)
    {
        $select = $select . " WHERE " . $where;
        $update = $update . " WHERE " . $where;
        $fetchedData = $db->createCommand($select)->queryAll(true, $params);
        $affectedRow = count($fetchedData);


        if ($affectedRow > $expectedAffectedRowNum) {
            throw new Exception("实际影响大于预期影响, 实际影响 $affectedRow, 预计影响 $expectedAffectedRowNum, sql: $select");
        }

//        echo $update . PHP_EOL;

        $db->createCommand($update)->execute($params);

    }

}
