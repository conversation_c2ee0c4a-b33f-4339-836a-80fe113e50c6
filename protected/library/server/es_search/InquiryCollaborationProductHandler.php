<?php
namespace common\library\server\es_search;

use common\library\custom_field\CustomFieldService;
use common\library\inquiry_collaboration\InquiryCollaborationFilter;
use common\library\inquiry_collaboration_product\InquiryCollaborationProduct;
use common\library\inquiry_collaboration_product\InquiryCollaborationProductFilter;
use common\models\es_search\InquiryCollaborationProductSearch;
use common\models\es_search\InquiryCollaborationSearch;
use common\models\es_search\PurchaseOrderSearch;

class InquiryCollaborationProductHandler extends Handler
{
    use ExternalFieldIndexTrait;

    protected function getModelClass()
    {
        return InquiryCollaborationProductSearch::class;
    }

    public function initData($data)
    {
        if(!isset($data['inquiry_product_id'])){
            return;
        }
        $this->id = $data['inquiry_product_id'];
        $this->clientId = $data['client_id'];
        $this->userId = $data['user_id'];
        $this->opType = $data['type'];
        $this->objName = $data['object_name'] ?? '';
        $this->setModuleType(\Constants::TYPE_INQUIRY_COLLABORATION_PRODUCT);
    }

    protected function buildParams() {
        $indexName = $this->getModelClass()::model()->index();
        $bulk =[];
        switch ($this->opType)
        {
            case \Constants::INQUIRY_COLLABORATION_PRODUCT_INDEX_TYPE_CREATE:
            case \Constants::INQUIRY_COLLABORATION_PRODUCT_INDEX_TYPE_UPDATE:
                $body = $this->buildBody();
                $bulk[] = [
                    'index' => [
                        '_index' => $indexName,
                        '_id' => $this->id,
                        'routing' => $this->clientId,
                    ]
                ];
                $bulk[] = $body;
                break;
            case \Constants::INQUIRY_COLLABORATION_PRODUCT_INDEX_TYPE_DELETE:
                $bulk[] = [
                    'delete' => [
                        '_index' => $indexName,
                        '_id' => $this->id,
                        'routing' => $this->clientId,
                    ]
                ];
                break;
        }

        $params = [
            'index' => $indexName,
            'body' => $bulk,
            'refresh' => false      // 不要求强实时性
        ];

        $this->params = $params;

        return $params;
    }

    public function handle() {
        $params = $this->buildParams();
        $connection = $this->getModelClass()::model()->getDbConnection();
        $ret = $connection->bulk($params);
        $results = [];
        $errorCount = 0;

        foreach ($ret['items']??[] as $item ) {
            $results[] = $item['index']['result']??'';
            if(empty($item['index']['error']))
                continue;

            $errorCount++;
            \LogUtil::error("error {$item['index']['_index']}, {$item['index']['_id']}} {$item['index']['error']['reason']}}");
        }

        return ['id'=> $this->id, 'result' => implode(',',$results), 'errorCount' => $errorCount];
    }

    protected function buildBody()
    {
        $filter = new InquiryCollaborationProductFilter($this->clientId);
        $filter->select(['inquiry_product_id',
                        'product_no',
                        'product_model',
                        'product_name',
                        'product_cn_name',
                        'external_field_data',]);
        $filter->inquiry_product_id = $this->id;
        $dataList = $filter->rawData();
        if(empty($dataList)){
            return [];
        }
        $data = $dataList[0];
        $this->setNotIndexExternalFieldType([CustomFieldService::FIELD_TYPE_IMAGE, CustomFieldService::FIELD_TYPE_ATTACH]);
        $formatExternalFields = $this->formatExternalFields($data['external_field_data']);
        $res = [
            'client_id' => $this->clientId,
            'update_time' => xm_function_now(),
            'external_field' => $formatExternalFields,
            'product_no' => $data['product_no'],
            'product_model' => $data['product_model'],
            'product_name' => $data['product_name'],
            'product_cn_name' => $data['product_cn_name'],
        ];
        if($this->opType == \Constants::INQUIRY_COLLABORATION_PRODUCT_INDEX_TYPE_CREATE){
            $res['create_time'] = xm_function_now();
        }
        return $res;
    }
}
