<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2019/7/8
 * Time: 9:42 AM
 */

namespace common\library\server\crontab\task;


use CDbException;
use ClientStatisticsExternalDay;
use common\library\account\Client;
use common\library\account\Helper;
use common\library\customer_v3\company\list\CompanyList;
use common\library\customer_v3\company\orm\CompanyMetadata;
use common\library\email_identity\sync\CustomerSync;
use common\library\notification\Notification;
use common\library\notification\PushHelper;
use common\library\object\field\service\ScopeUserFieldService;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\queue_v2\job\PerformanceV2RecordJob;
use common\library\queue_v2\QueueService;
use common\library\server\es_search\SearchQueueService;
use common\library\setting\library\common\PublicTypeMetadata;
use common\library\setting\library\group\Group;
use common\library\statistics\CompanyHelper;
use common\library\todo\Feed;
use common\library\todo\TodoConstant;
use common\library\version\CompanyVersion;
use common\library\version\Constant;
use common\models\client\CompanyHistoryPg;
use Constants;
use CustomerPublicRemind;
use LogUtil;
use MsgBoxService;
use PgActiveRecord;
use ProjectActiveRecord;
use User;
use UserStatisticsExternal;
use Util;

class CustomerTask extends Task
{

    /**
     * 每天执行
     * 处理自动移入公海逻辑
     * @param $clientId
     */
    protected static function runMoveToPublic($clientId)
    {
        ini_set("memory_limit", "1024M");
        $client = new \common\library\account\Client($clientId);
        if ($client->isNew() || $client->mysql_set_id == 0) {
            LogUtil::info("clientId:{$clientId},client not exist or mysql_set_id = 0");
            return;
        }
        if (!\common\library\privilege_v3\Helper::hasFunctional($clientId,
            PrivilegeConstants::FUNCTIONAL_COMPANY_POOL_AUTO_RULES)) {
            LogUtil::info("clientId:{$clientId},client missing auto rule privilege");
            return;
        }

        if ($client->getExtentAttribute(Client::EXTERNAL_KEY_USE_NEW_PUBLIC_RULE)) {
            LogUtil::info("clientId:{$clientId}, use new public rule");
            return;
        }



        ProjectActiveRecord::setConnection(ProjectActiveRecord::getDbByClientId($clientId));
        $pg = PgActiveRecord::getDbByClientId($clientId);
        $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        if ( !$adminUserId )
        {
            LogUtil::info("not set admin user, ignore");
            return;
        }

        $user = User::getUserObject($adminUserId);
        User::setLoginUserById($user->getUserId());

        //执行过的就跳过
        $nowDate = date('Y-m-d');
        $exist = ProjectActiveRecord::getDbByClientId($clientId)->createCommand("select * from  tbl_customer_public_remind  where client_id={$clientId} and date='{$nowDate}' limit 1")->queryRow();
        if( $exist)
            return;

        try
        {
            $groupData = \common\library\group\Helper::getCustomerGroupMoveToPublicSetting($clientId);
        }catch (CDbException $e)
        {
            LogUtil::info('db: '.$e->errorInfo);
            throw $e;
        }

        $groupMap = array();
        $startTimeMap = array();
        $moveGroup = array();
        $ignoreFrozenUserMap = [];
        $frozenUserIds = Helper::getFreezeUserIds($clientId);

        foreach ($groupData as $elem)
        {
            $groupMap[$elem['id']] = intval($elem['public_time']) * 86400;
            $ignoreFrozenUserMap[$elem['id']] = $elem[Group::External_KEY_PUBLIC_IGNORE_FORZEN_USER] ? true : false;
            $startTimeMap[$elem['id']] = strtotime(date('Y-m-d',strtotime($elem['start_public_time'])));//确保是00:00:00

            if($elem['public_time']){
                $moveGroup[] = $elem['id'];
            }

        }

        if(empty($moveGroup)){
            LogUtil::info("client_id:$clientId not has need to moveGroup");
            return;
        }

        $now = strtotime(date('Y-m-d H:i:s'));

        $noticeArray = array();
        $willPublicArray = array();
        $removeArray = [];
        $removeUserIdList = [];
        $willMoveToPublicCompanyIds = [];

        $list = new CompanyList($user->getUserId());
        $list->setSkipPrivilege(true);
        $list->setUserNum([1,2]);
        $list->setGroupId($moveGroup);
        $list->setFields(['company_id', 'array_to_json(user_id) as user_id', 'order_time', 'group_id','company_hash_id']);
        $result = $list->find();

        $dayX3 = 3;
        $dayX5 = 5;
        $dayX7 = 7;
        $moveToPublicCompanyIds = [];
        $removeUserIds = [];
        $ignoreCompanyIds = [];
        $moveToPublicUserCompanyIdsMap = [];
        foreach ($result as $elem)
        {
            $elem['user_id'] = json_decode($elem['user_id'], true);
            $noticeUserList = $elem['user_id'];
            $userList = [];
            // 排除已冻结账号且跟进人全部为冻结账号则跳过
            $ignoreFrozenUser = $ignoreFrozenUserMap[$elem['group_id']];
            if ($ignoreFrozenUser) {
                if (empty($noticeUserList = array_diff($elem['user_id'], $frozenUserIds))) {
                    $ignoreCompanyIds[] = $elem['company_id'];
                    continue;
                }
            }
            $last_owner = 0;
            $oldUserIds = $elem['user_id'];


            $startTime = $startTimeMap[$elem['group_id']];
            $publicTime = $groupMap[$elem['group_id']];
            $sec = strtotime($elem['order_time']);
            $gap = $now - $sec;//未联系时间

            if($startTime > $now)
            {//未到脚本开始时间，仅做检查记录操作

                $the_rest = $gap > $publicTime ? 0 :$publicTime - $gap;//剩余时间

	            /**
	             * $now + (86400 * $dayX3) > $startTime
	             * (86400 * $dayX3) > $startTime - $now
	             * ($dayX3) > ($startTime - $now)/86400
	             */

                if ($now + $the_rest < $startTime) {//如果在开始脚本之前就已经超过期限
                    foreach ($noticeUserList as $userId) {
                        if ($now + (86400 * $dayX3) > $startTime) {//在3天内到脚本执行时间
//                            if (!isset($noticeArray[$userId]['customerCount']))
//                                $noticeArray[$userId]['customerCount'] = 0;
//
//                            $noticeArray[$userId]['customerCount']++;
                            $willPublicArray[$dayX3][$userId]['customers'][] = $elem['company_id'];
                        }

                        if ($now + (86400 * $dayX5) >= $startTime) {//在5天内到脚本执行时间
                            $willPublicArray[$dayX5][$userId]['customers'][] = $elem['company_id'];
                        }

                        if ($now + (86400 * $dayX7) >= $startTime) {//在7天内到脚本执行时间
                            $willPublicArray[$dayX7][$userId]['customers'][] = $elem['company_id'];
//                            $willMoveToPublicCompanyIds[$userId][] = [
//                                'company_id' => $elem['company_id'],
//                                'company_hash_id' => $elem['company_hash_id']
//                            ];
                        }

//
	                    $leftDay = ceil(($startTime - $now) / 86400);

	                    $result = self::setData($leftDay, $userId, $elem, $willPublicArray, $noticeArray, $willMoveToPublicCompanyIds);

	                    $willPublicArray = $result['willPublicArray'];

	                    $noticeArray = $result['noticeArray'];

	                    $willMoveToPublicCompanyIds = $result['willMoveToPublicCompanyIds'];
//
                    }
                }
                $userList = $elem['user_id'];
            }
            else
            {
                if ($gap < $groupMap[$elem['group_id']])
                {
                    $userList = $elem['user_id'];

                    $dayX3Time = 86400 * $dayX3;
                    $dayX5Time = 86400 * $dayX5;
                    $dayX7Time = 86400 * $dayX7;
                    foreach ($noticeUserList as $userId) {
                        if ($gap > $groupMap[$elem['group_id']] - $dayX3Time) {
//                            if (!isset($noticeArray[$userId]['customerCount']))
//                                $noticeArray[$userId]['customerCount'] = 0;
//
//                            $noticeArray[$userId]['customerCount']++;
                            $willPublicArray[$dayX3][$userId]['customers'][] = $elem['company_id'];
                        }

                        if ($gap > $groupMap[$elem['group_id']] - $dayX5Time) {
                            $willPublicArray[$dayX5][$userId]['customers'][] = $elem['company_id'];
                        }

                        if ($gap > $groupMap[$elem['group_id']] - $dayX7Time) {
                            $willPublicArray[$dayX7][$userId]['customers'][] = $elem['company_id'];
//                            $willMoveToPublicCompanyIds[$userId][] = [
//                                'company_id' => $elem['company_id'],
//                                'company_hash_id' => $elem['company_hash_id']
//                            ];
                        }
//
	                    $leftDay = ceil(($publicTime - $gap) / 86400);

	                    $result = self::setData($leftDay, $userId, $elem, $willPublicArray, $noticeArray, $willMoveToPublicCompanyIds);

	                    $willPublicArray = $result['willPublicArray'];

	                    $noticeArray = $result['noticeArray'];

	                    $willMoveToPublicCompanyIds = $result['willMoveToPublicCompanyIds'];
//


                    }
                } else {
                    $removeArray[] = $elem['company_id'];
                    $removeUserIdList = $elem['user_id'];
                    $last_owner = array_pop($elem['user_id']);
                    LogUtil::info('clientId '.$clientId.' moveCompany:'.$elem['company_id'].' orderTime:'.$elem['order_time'].' publicDay:'.($groupMap[$elem['group_id']]/86400).' groupId:'.$elem['group_id'].' gap:'.$gap.' groupTime:'.$groupMap[$elem['group_id']]);
                }
            }

            if (empty($userList))
            {

                $nowTime = date('Y-m-d H:i:s');
                $publicType = PublicTypeMetadata::PUBLIC_TYPE_RULE;
                $pg->createCommand("update tbl_company set user_id='{}', last_owner=$last_owner,public_time= '{$nowTime}',release_count=release_count+1,public_type={$publicType} where company_id=".$elem['company_id'])->execute();
                $pg->createCommand("update tbl_customer set user_id='{}' where client_id={$clientId}  and is_archive=1 and company_id=".$elem['company_id'])->execute();

                \common\library\customer\public_record\Helper::batchSavePublicRecord($clientId,$removeUserIdList,$elem['company_id']);

                foreach ($removeUserIdList as $userId) {
                    $moveToPublicUserCompanyIdsMap[$userId][] = $elem['company_id'];
                }

                LogUtil::info("clientId {$clientId} finish move company {$elem['company_id']}");

                if ($last_owner)
                {
                    $companyHistory = new CompanyHistoryPg();
                    $companyHistory->client_id = $clientId;
                    $companyHistory->type = CompanyHistoryPg::TYPE_SYS_MOVE_TO_PUBLIC;
                    $companyHistory->company_id = $elem['company_id'];
                    $companyHistory->customer_id = 0;
                    $companyHistory->create_time = date('Y-m-d H:i:s');
                    $companyHistory->update_time = date('Y-m-d H:i:s');
                    $companyHistory->update_user = 0;
                    $diffData = $companyHistory::diffData(['user_id' => ['old' => $oldUserIds, 'new' => []]], 'modify');
                    $companyHistory->diff = json_encode($diffData);
                    $companyHistory->save();
                }

                $moveToPublicCompanyIds[] = $elem['company_id'];
                $removeUserIds = array_merge($oldUserIds, $removeUserIds);
            }
        }

        // 更新scope_user_ids
//        if ($moveToPublicCompanyIds) {
//            $scopeUserService = new ScopeUserFieldService($clientId, new CompanyMetadata($clientId));
//            $scopeUserService->refreshScopeUserIdsByPids($moveToPublicCompanyIds);
//        }

	    foreach ($willPublicArray[0]??[] as $userId => $item) {

		    $keys = array_keys($item['customers']);

		    sort($keys);

		    CustomerPublicRemind::updateDataByPK($userId, $clientId, date('Y-m-d'), $item['customers'], 0);

		    $feedArr = [];

		    foreach ($keys as $key) {

			    if ($key > 3) {

				    break;
			    }

			    $feedArr = array_merge($feedArr, $item['customers'][$key]);
		    }

		    if (!empty($feedArr)) {

			    $feed = new Feed(TodoConstant::OBJECT_TYPE_COMPANY, TodoConstant::TODO_TYPE_COMPANY_MOVE_TO_PUBLIC_IN_THREE_DAYS);

			    $feed->setClientId($clientId);

			    $feed->setUserId($userId);

			    $feed->pushFeed($feedArr);
		    }
	    }


        foreach($willPublicArray as $dayNumber =>$value)
        {
	        if ($dayNumber == 0) {

		        continue;
	        }

            foreach ($value as $userId => $item) {
                CustomerPublicRemind::updateDataByPK($userId, $clientId, date('Y-m-d'), $item['customers'], $dayNumber);

                if ($dayNumber == $dayX3) {
                    $feed = new Feed(TodoConstant::OBJECT_TYPE_COMPANY,TodoConstant::TODO_TYPE_COMPANY_MOVE_TO_PUBLIC_IN_THREE_DAYS);
                    $feed->setClientId($clientId);
                    $feed->setUserId($userId);
                    $feed->pushFeed($item['customers']);
                }

                LogUtil::info("clientId {$clientId} updateDataByPK {$userId}");

            }
        }

        foreach ($noticeArray as $userId => $value) {
            $notification = new Notification($clientId, \common\library\notification\Constant::NOTIFICATION_TYPE_CUSTOMER_MOVE_PUBLIC);
            $notification->user_id = $userId;
            $notification->setIsSystemTrigger(true);
            $notification->setSourceData($value['customerCount']);
            PushHelper::pushNotification($clientId, $userId, $notification);
            
            \common\library\dingding\Helper::pushNotification($notification);
        }

        if(!empty($willMoveToPublicCompanyIds)) {

            foreach ($willMoveToPublicCompanyIds as $user_id => $companyData) {
                self::willMoveToPublicForAIMarketing($clientId,$user_id,$companyData);
            }
        }

        if (!empty($removeArray))
            LogUtil::info('clientId'.$clientId .' remove customer:'.implode(',',$removeArray));

        if (!empty($moveToPublicCompanyIds))
        {
            (new CustomerSync($clientId))->setFindCompanyId($moveToPublicCompanyIds)->sync();

            SearchQueueService::pushCompanyQueue($adminUserId,$clientId,$moveToPublicCompanyIds,Constants::SEARCH_INDEX_TYPE_UPDATE);

            //补上自动移入公海的版本号
            $companyVersion = new CompanyVersion($clientId, $moveToPublicCompanyIds);
            $companyVersion->setType(Constant::COMPANY_MODULE_EDIT);
            $companyVersion->add();
        }

        if (!empty($ignoreCompanyIds)) {
            LogUtil::info('clientId'.$clientId .' ignore company:'.implode(',',$ignoreCompanyIds));
        }

        CompanyHelper::batchIncCompanyKeyCount($clientId, ['move_to_public_company_count', 'auto_move_to_public_company_count'], $moveToPublicUserCompanyIdsMap);

        // 删除相关[跟进客户]任务
        if ($moveToPublicCompanyIds) {
            \common\library\task\Helper::deleteTask($clientId, $moveToPublicCompanyIds, $removeUserIds);
        }

        if (php_sapi_name() == "cli" || \Yii::app()->params['env'] == 'exp') {
            (new PerformanceV2RecordJob($clientId, \Constants::TYPE_COMPANY, $moveToPublicCompanyIds))->handle();
        } else {
            QueueService::dispatch(new PerformanceV2RecordJob($clientId, \Constants::TYPE_COMPANY, $moveToPublicCompanyIds));
        }
    }

	/**
	 * @param $leftDay
	 * @param $userId
	 * @param $elem
	 * @param $willPublicArray
	 * @param $noticeArray
	 * @param $willMoveToPublicCompanyIds
	 * @return array
	 */
	private static function setData($leftDay, $userId, $elem, $willPublicArray, $noticeArray, $willMoveToPublicCompanyIds) {

		if ($leftDay <= 7) {

			$willPublicArray[0][$userId]['customers'][$leftDay][] = $elem['company_id'];

			if ($leftDay == 3) {

				if (!isset($noticeArray[$userId]['customerCount']))
					$noticeArray[$userId]['customerCount'] = 0;

				$noticeArray[$userId]['customerCount']++;
			}

			if ($leftDay == 7) {

				$willMoveToPublicCompanyIds[$userId][] = [
					'company_id'      => $elem['company_id'],
					'company_hash_id' => $elem['company_hash_id'],
				];
			}
		}

		return [
			'willPublicArray'            => $willPublicArray,
			'noticeArray'                => $noticeArray,
			'willMoveToPublicCompanyIds' => $willMoveToPublicCompanyIds,
		];
	}


    public static function willMoveToPublicForAIMarketing(int $client_id,int $user_id,array $company_data)
    {
        //批量操作
        $db = PgActiveRecord::getDbByClientId($client_id);

        if (empty($company_data)) {
            return false;
        }

        $company_data = array_chunk($company_data, 1000);
        $type = \common\library\ai\Constant::WILL_MOVE_TO_PUBLIC;
        $createTime = date('Y-m-d H:i:s');
        try {
            foreach ($company_data as $chunk) {

                $insertValues = [];
                foreach ($chunk as $item) {
                    $companyId = $item['company_id'];
                    $companyHashId = $item['company_hash_id'];
                    $insertValues[] = "({$client_id},{$user_id},{$companyId},'{$companyHashId}','{$createTime}',0,{$type})";

                }
                if (empty($insertValues)) {
                    continue;
                }
                //每一个chunk 结束 尝试入库
                $values = implode(',', $insertValues);

                $sql = "INSERT INTO tbl_ai_activate_company (client_id, user_id, company_id, company_hash_id, create_time,read_flag,type) values {$values} ON CONFLICT (user_id,company_id,type) DO UPDATE SET read_flag=excluded.read_flag, create_time=excluded.create_time";

                $db->createCommand($sql)->execute();
            }

        } catch (\Exception $exception) {
            \LogUtil::error($exception->getMessage());
        }
    }



    /**
     * 每天执行
     * 旧的客户统计脚本, 后续可能会被新统计分析取代
     * @param $clientId
     */
    protected static  function runStatistics($clientId)
    {
        ini_set("memory_limit", "1024M");
        LogUtil::info('client_id ' . $clientId . ' begin');

        $client = \Client::findByClientId($clientId);
        if($client->mysql_set_id == 0){
            return;
        }

        $db = ProjectActiveRecord::getDbByClientId($clientId);
        $pg = PgActiveRecord::getDbByClientId($clientId);


        $data = array();
        $createCustomerData = array();
        $followCustomerData = array();
        $client_data = array();
        $now = time();
        $date = date('Y-m-d', $now - 86400);
        $now = strtotime($date);

        $dataReader = $pg->createCommand("select * from tbl_company where client_id=$clientId and is_archive=1")->query();
        $total_count = $pg->createCommand("select count(1) from tbl_company where client_id=$clientId and is_archive=1")->queryScalar();

        $client_data = array(
            ClientStatisticsExternalDay::TYPE_CUSTOMER_TOTAL_COUNT => array($total_count),
            ClientStatisticsExternalDay::TYPE_CUSTOMER_GROUP => array(),
            ClientStatisticsExternalDay::TYPE_CUSTOMER_STAR => array(),
            ClientStatisticsExternalDay::TYPE_CUSTOMER_STATUS => array(),
            ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME => array(),
            ClientStatisticsExternalDay::TYPE_CUSTOMER_COUNTRY => array(),
            ClientStatisticsExternalDay::TYPE_CUSTOMER_ORIGIN => array(),
        );

        $formatter = new \common\library\customer\CompanyFormatter($clientId);


        foreach ($dataReader as $row) {
            $elem = $formatter->strip($row);

            $star = empty($elem['star']) ? 0 : $elem['star'];
            if (!array_key_exists($star, $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_STAR]))
                $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_STAR][$star] = 0;
            $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_STAR][$star]++;

            $groupId = empty($elem['group_id']) ? 0 : $elem['group_id'];
            if (!array_key_exists($groupId, $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_GROUP]))
                $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_GROUP][$groupId] = 0;
            $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_GROUP][$groupId]++;

            $status = empty($elem['trail_status']) ? 0 : $elem['trail_status'];
            if (!array_key_exists($status, $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_STATUS]))
                $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_STATUS][$status] = 0;
            $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_STATUS][$status]++;

            $recent_time = strtotime($elem['order_time']);

            $recent_time = ($now - $recent_time) / 86400;

            if ($recent_time <= 7) {
                $recent_time = 7;
                if (!array_key_exists($recent_time, $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME]))
                    $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME][$recent_time] = 0;
                $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME][$recent_time]++;
            }
            if ($recent_time <= 30) {
                $recent_time = 30;
                if (!array_key_exists($recent_time, $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME]))
                    $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME][$recent_time] = 0;
                $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME][$recent_time]++;
            }
            if ($recent_time <= 90) {
                $recent_time = 90;
                if (!array_key_exists($recent_time, $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME]))
                    $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME][$recent_time] = 0;
                $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME][$recent_time]++;
            }
            if ($recent_time <= 180) {
                $recent_time = 180;
                if (!array_key_exists($recent_time, $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME]))
                    $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME][$recent_time] = 0;
                $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME][$recent_time]++;
            }
            if ($recent_time > 180) {
                $recent_time = 666666;
                if (!array_key_exists($recent_time, $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME]))
                    $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME][$recent_time] = 0;
                $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_RECENT_TIME][$recent_time]++;
            }

            $country = empty($elem['country']) ? 0 : $elem['country'];
            if ((is_string($country) || is_integer($country)) && !array_key_exists($country, $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_COUNTRY]))
                $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_COUNTRY][$country] = 0;
            $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_COUNTRY][$country]++;

            $origin = empty($elem['origin_list'][0]) ? 0 : $elem['origin_list'][0];
            if (!array_key_exists($origin, $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_ORIGIN]))
                $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_ORIGIN][$origin] = 0;
            $client_data[ClientStatisticsExternalDay::TYPE_CUSTOMER_ORIGIN][$origin]++;

            $userList = $elem['user_id'];

            if (!is_array($userList)) {
                $userList = array($userList);
            }
            if (empty($userList)) {
                $userList = array(0);
            }

            foreach ($userList as $userId) {
                if (!array_key_exists($userId, $data))
                    $data[$userId] = array(
                        UserStatisticsExternal::TYPE_CUSTOMER_GROUP => array(),
                        UserStatisticsExternal::TYPE_CUSTOMER_STAR => array(),
                        UserStatisticsExternal::TYPE_CUSTOMER_STATUS => array(),
                        UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME => array(),
                        UserStatisticsExternal::TYPE_CUSTOMER_COUNTRY => array(),
                        UserStatisticsExternal::TYPE_CUSTOMER_ORIGIN => array(),
                        UserStatisticsExternal::TYPE_CUSTOMER_SCORE => array(),
                        'total_count' => 0
                    );

                $data[$userId]['total_count']++;

                $star = empty($elem['star']) ? 0 : $elem['star'];
                if (!array_key_exists($star, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_STAR]))
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_STAR][$star] = 0;
                $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_STAR][$star]++;

                $groupId = empty($elem['group_id']) ? 0 : $elem['group_id'];
                if (!array_key_exists($groupId, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_GROUP]))
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_GROUP][$groupId] = 0;
                $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_GROUP][$groupId]++;

                $status = empty($elem['trail_status']) ? 0 : $elem['trail_status'];
                if (!array_key_exists($status, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_STATUS]))
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_STATUS][$status] = 0;
                $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_STATUS][$status]++;

                if ($userId && isset($elem['user_data'][$userId]['order_time']))
                {
                    $time = strtotime($elem['user_data'][$userId]['order_time']);

                    $time = ($now - $time) / 86400;

                    if ($time <= 7) {
                        $time = 7;
                        if (!array_key_exists($time, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME]))
                            $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME][$time] = 0;
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME][$time]++;
                    }
                    if ($time <= 15) {
                        $time = 15;
                        if (!array_key_exists($time, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME]))
                            $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME][$time] = 0;
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME][$time]++;
                    }
                    if ($time <= 30) {
                        $time = 30;
                        if (!array_key_exists($time, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME]))
                            $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME][$time] = 0;
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME][$time]++;
                    }
                    if ($time <= 60) {
                        $time = 60;
                        if (!array_key_exists($time, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME]))
                            $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME][$time] = 0;
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME][$time]++;
                    }
                    if ($time <= 90) {
                        $time = 90;
                        if (!array_key_exists($time, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME]))
                            $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME][$time] = 0;
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_RECENT_TIME][$time]++;
                    }
                }

                $country = empty($elem['country']) ? 0 : $elem['country'];
                if ((is_string($country) || is_integer($country)) && !array_key_exists($country, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_COUNTRY]))
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_COUNTRY][$country] = 0;
                $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_COUNTRY][$country]++;

                $origin = empty($elem['origin_list'][0]) ? 0 : $elem['origin_list'][0];
                if (!array_key_exists($origin, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_ORIGIN]))
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_ORIGIN][$origin] = 0;
                $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_ORIGIN][$origin]++;

                //客户评分
                $score = isset($elem['score']['user_total'][$userId]) ? $elem['score']['user_total'][$userId] : (isset($elem['score']['total']) ? $elem['score']['total'] : 0);

                if ($score <= 10) {
                    if (!array_key_exists(1, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE]))
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][1] = 0;
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][1]++;
                }

                if ($score > 10 && $score <= 20) {
                    if (!array_key_exists(2, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE]))
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][2] = 0;
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][2]++;
                }

                if ($score > 20 && $score <= 30) {
                    if (!array_key_exists(3, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE]))
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][3] = 0;
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][3]++;
                }

                if ($score > 30 && $score <= 40) {
                    if (!array_key_exists(4, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE]))
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][4] = 0;
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][4]++;
                }

                if ($score > 40 && $score <= 50) {
                    if (!array_key_exists(5, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE]))
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][5] = 0;
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][5]++;
                }

                if ($score > 50 && $score <= 60) {
                    if (!array_key_exists(6, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE]))
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][6] = 0;
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][6]++;
                }

                if ($score > 60 && $score <= 70) {
                    if (!array_key_exists(7, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE]))
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][7] = 0;
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][7]++;
                }

                if ($score > 70 && $score <= 80) {
                    if (!array_key_exists(8, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE]))
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][8] = 0;
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][8]++;
                }

                if ($score > 80 && $score <= 90) {
                    if (!array_key_exists(9, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE]))
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][9] = 0;
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][9]++;
                }

                if ($score > 90 && $score <= 100) {
                    if (!array_key_exists(10, $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE]))
                        $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][10] = 0;
                    $data[$userId][UserStatisticsExternal::TYPE_CUSTOMER_SCORE][10]++;
                }

            }


            $archiveDate = date('Y-m-d', strtotime($elem['archive_time'] ?? ''));

            $yesterDay = date('Y-m-d', strtotime('yesterday'));

            $followDay = date('Y-m-d', strtotime($elem['order_time'] ?? ''));

            foreach ($userList as $userId) {
                if (!array_key_exists($userId, $createCustomerData))
                    $createCustomerData[$userId] = array(
                        UserStatisticsExternal::TYPE_CREATE_CUSTOMER_GROUP => array(),
                        UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STAR => array(),
                        UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STATUS => array(),
                        UserStatisticsExternal::TYPE_CREATE_CUSTOMER_COUNTRY => array(),
                        UserStatisticsExternal::TYPE_CREATE_CUSTOMER_ORIGIN => array(),
                        UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE => array(),
                    );

                if (!isset($createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_GROUP][$archiveDate])) $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_GROUP][$archiveDate] = array();
                if (!isset($createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STAR][$archiveDate])) $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STAR][$archiveDate] = array();
                if (!isset($createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STATUS][$archiveDate])) $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STATUS][$archiveDate] = array();
                if (!isset($createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_COUNTRY][$archiveDate])) $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_COUNTRY][$archiveDate] = array();
                if (!isset($createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_ORIGIN][$archiveDate])) $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_ORIGIN][$archiveDate] = array();
                if (!isset($createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate])) $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate] = array();

                $star = empty($elem['star']) ? 0 : $elem['star'];
                if (!array_key_exists($star, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STAR][$archiveDate]))
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STAR][$archiveDate][$star] = 0;
                $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STAR][$archiveDate][$star]++;

                $groupId = empty($elem['group_id']) ? 0 : $elem['group_id'];
                if (!array_key_exists($groupId, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_GROUP][$archiveDate]))
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_GROUP][$archiveDate][$groupId] = 0;
                $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_GROUP][$archiveDate][$groupId]++;

                $status = empty($elem['trail_status']) ? 0 : $elem['trail_status'];
                if (!array_key_exists($status, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STATUS][$archiveDate]))
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STATUS][$archiveDate][$status] = 0;
                $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_STATUS][$archiveDate][$status]++;


                $country = empty($elem['country']) ? 0 : $elem['country'];
                if ((is_string($country) || is_integer($country)) && !array_key_exists($country, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_COUNTRY][$archiveDate]))
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_COUNTRY][$archiveDate][$country] = 0;
                $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_COUNTRY][$archiveDate][$country]++;

                $origin = empty($elem['origin_list'][0]) ? 0 : $elem['origin_list'][0];
                if (!array_key_exists($origin, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_ORIGIN][$archiveDate]))
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_ORIGIN][$archiveDate][$origin] = 0;
                $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_ORIGIN][$archiveDate][$origin]++;

                $score = isset($elem['score']['user_total'][$userId]) ? $elem['score']['user_total'][$userId] : (isset($elem['score']['total']) ? $elem['score']['total'] : 0);

                if ($score <= 10) {
                    if (!array_key_exists(1, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate]))
                        $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][1] = 0;
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][1]++;
                }

                if ($score > 10 && $score <= 20) {
                    if (!array_key_exists(2, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate]))
                        $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][2] = 0;
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][2]++;
                }

                if ($score > 20 && $score <= 30) {
                    if (!array_key_exists(3, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate]))
                        $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][3] = 0;
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][3]++;
                }

                if ($score > 30 && $score <= 40) {
                    if (!array_key_exists(4, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate]))
                        $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][4] = 0;
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][4]++;
                }

                if ($score > 40 && $score <= 50) {
                    if (!array_key_exists(5, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate]))
                        $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][5] = 0;
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][5]++;
                }

                if ($score > 50 && $score <= 60) {
                    if (!array_key_exists(6, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate]))
                        $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][6] = 0;
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][6]++;
                }

                if ($score > 60 && $score <= 70) {
                    if (!array_key_exists(7, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate]))
                        $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][7] = 0;
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][7]++;
                }

                if ($score > 70 && $score <= 80) {
                    if (!array_key_exists(8, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate]))
                        $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][8] = 0;
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][8]++;
                }

                if ($score > 80 && $score <= 90) {
                    if (!array_key_exists(9, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate]))
                        $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][9] = 0;
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][9]++;
                }

                if ($score > 90 && $score <= 100) {
                    if (!array_key_exists(10, $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate]))
                        $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][10] = 0;
                    $createCustomerData[$userId][UserStatisticsExternal::TYPE_CREATE_CUSTOMER_SCORE][$archiveDate][10]++;
                }


                //目前只更新昨天的跟进客户统计
                if ($followDay == $yesterDay) {
                    if (!isset($followCustomerData[$userId][$yesterDay])) {
                        $followCustomerData[$userId][$yesterDay] = 0;
                    }
                    $followCustomerData[$userId][$yesterDay]++;
                }


            }


        }
        foreach ($client_data AS $type => $typeData) {
            foreach ($typeData AS $value => $count) {
                $value = Util::escapeDoubleQuoteSql($value);
                $sql = "INSERT INTO tbl_client_statistics_external_day (client_id,date,type,value,count)
VALUES ({$clientId},'$date',$type,'{$value}',{$count})
ON DUPLICATE KEY UPDATE count=count+{$count};";
                try {
                    $db->createCommand($sql)->execute();
                } catch (\Exception $e) {
                    LogUtil::info($sql);
                    LogUtil::info("1mysql执行出错::" . $e->getMessage());
                }
            }
        }
        foreach ($data as $userId => $userData) {
            $sql = "INSERT INTO tbl_user_statistics_day (user_id,client_id,date,create_time,customer_total_count)
VALUES ({$userId},{$clientId},'$date','$date', {$userData['total_count']})
ON DUPLICATE KEY UPDATE customer_total_count=customer_total_count+{$userData['total_count']};";

            try {
                $db->createCommand($sql)->execute();
            } catch (\Exception $e) {
                LogUtil::info($sql);
                LogUtil::info("2mysql执行出错::" . $e->getMessage());
            }

            unset($userData['total_count']);

            foreach ($userData as $type => $typeData) {


                foreach ($typeData as $value => $count) {
                    $value = Util::escapeDoubleQuoteSql($value);
                    $sql = "INSERT INTO tbl_user_statistics_external_day (user_id,client_id,date,type,value,count)
VALUES ({$userId},{$clientId},'$date',$type,'{$value}',{$count})
ON DUPLICATE KEY UPDATE count=count+{$count};";
                    try {
                        $db->createCommand($sql)->execute();
                    } catch (\Exception $e) {
                        LogUtil::info($sql);
                        LogUtil::info("3mysql执行出错::" . $e->getMessage());
                    }
                }

            }
        }


        //更新昨天跟进客户数
        $sqlArr = array();
        foreach ($followCustomerData as $userId => $userData) {
            foreach ($userData as $day => $dataCount) {

                $sqlArr[] = "INSERT INTO tbl_user_statistics_day (user_id,client_id,date,create_time,follow_customer_count)
VALUES ({$userId},{$clientId},'$day','$day', {$dataCount})
ON DUPLICATE KEY UPDATE follow_customer_count={$dataCount}";

            }
        }

        if (!empty($sqlArr)) {

            $sqlArr = array_chunk($sqlArr, 1000);
            foreach ($sqlArr as $sqlItem) {
                try {
                    $sql = implode(';', $sqlItem);
                    $db->createCommand($sql)->execute();
                } catch (\Exception $e) {
                    LogUtil::info($sql);
                    LogUtil::info("5mysql执行出错::" . $e->getMessage());
                }
            }
        }


        foreach ($createCustomerData as $userId => $userData) {
            foreach ($userData as $type => $typeData) {

                foreach ($typeData as $day => $dataCount) {
                    $sqlArr = array();
                    foreach ($dataCount as $value => $count) {
                        $value = Util::escapeDoubleQuoteSql($value);
                        $sqlArr[] = "INSERT INTO tbl_user_statistics_external_day (user_id,client_id,date,type,value,count)
VALUES ({$userId},{$clientId},'$day',$type,'{$value}',{$count})
ON DUPLICATE KEY UPDATE count={$count}";

                    }

                    $sqlArr = array_chunk($sqlArr, 1000);
                    foreach ($sqlArr as $sqlItem) {
                        try {
                            $sql = implode(';', $sqlItem);
                            $db->createCommand($sql)->execute();
                        } catch (\Exception $e) {
                            LogUtil::info($sql);
                            LogUtil::info("4mysql执行出错::" . $e->getMessage());
                        }
                    }

                }

            }
        }


        LogUtil::info('client_id' . $clientId . ' end');
    }

}
