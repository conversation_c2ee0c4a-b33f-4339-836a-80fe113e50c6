RELEASE 1.0.5
-------------
2017-02-16  <PERSON>  <j<PERSON><PERSON><PERSON>@gmail.com>
	* PHPLIB-260: Remove duplicate server selection in Collection::findOne()


RELEASE 1.0.4
-------------
2016-11-14  <PERSON>  <j<PERSON><PERSON><PERSON>@gmail.com>
	* PHPLIB-228: ReadConcern default should only be set when supported by the server


RELEASE 1.0.3
-------------
2016-09-22  <PERSON>  <<EMAIL>>
	* Allow <PERSON> to fast-finish

2016-09-22  <PERSON>  <<EMAIL>>
	* Revise <PERSON> build matrix

2016-09-22  <PERSON>  <<EMAIL>>
	* PHPLIB-193: Do not pass typeMap Client option to Manager

2016-09-22  <PERSON>  <jmi<PERSON><EMAIL>>
	* PHPLIB-219: Fix BSON serialization of findAndModify write concern

2016-06-20  <PERSON>  <j<PERSON><PERSON><PERSON>@gmail.com>
	* Fix indentation

2016-06-20  <PERSON>  <j<PERSON><PERSON><PERSON>@gmail.com>
	* Fix DatabaseCommand::execute() return type hint

2016-06-20  <PERSON>  <<EMAIL>>
	* Fix doc block spacing

2016-06-20  <PERSON> <PERSON>kola  <<EMAIL>>
	* Remove unnecessary use statements

2016-06-03  Jeremy Mikola  <<EMAIL>>
	* CreateIndexes::executeCommand() need not return anything

2016-05-23  corpsee  <<EMAIL>>
	* Fixed return annotation in doc blocks

2016-05-23  Jesper Wallin  <<EMAIL>>
	* Fix typo in query projection example

2016-06-02  Derick Rethans  <<EMAIL>>
	* Fixed documentation to point to the right BSON namespace for ObjectID

2016-06-03  Jeremy Mikola  <<EMAIL>>
	* Note that non-arrays may also be used as documents

2016-06-02  Jeremy Mikola  <<EMAIL>>
	* Add write method examples to CRUD tutorial

2016-03-30  Jeremy Mikola  <<EMAIL>>
	* Add 1.0.2 release notes


RELEASE 1.0.2
-------------
2016-03-30  Jeremy Mikola  <<EMAIL>>
	* Discuss how inserted IDs may be accessed in Upgrade Guide

2016-03-30  Jeremy Mikola  <<EMAIL>>
	* Use reference-style links in Upgrade Guide

2016-03-29  Nick Dresselhaus  <<EMAIL>>
	* PHPLIB-189: Fix merging of comment and maxTimeMS options

2016-03-29  Jeremy Mikola  <<EMAIL>>
	* Use listCollections for command cursor example, move aggregate to CRUD

2016-03-28  Jeremy Mikola  <<EMAIL>>
	* Add query examples and create BSON tutorial page

2016-03-28  Jeremy Mikola  <<EMAIL>>
	* Clarify that Persistable is supported in methods without "typeMap" option

2016-03-24  Jeremy Mikola  <<EMAIL>>
	* Add Client API documentation

2016-03-24  Jeremy Mikola  <<EMAIL>>
	* Fix typo in Client::__get() documentation

2016-03-24  Jeremy Mikola  <<EMAIL>>
	* Split Database docs into API and tutorial

2016-03-24  Jeremy Mikola  <<EMAIL>>
	* CreateCollection::execute() may return an array

2016-03-23  Jeremy Mikola  <<EMAIL>>
	* Split Collection docs into API, tutorial, and upgrade guide

2016-03-23  Jeremy Mikola  <<EMAIL>>
	* Specify when findAndModify operations may return null

2016-03-23  Jeremy Mikola  <<EMAIL>>
	* Misc doc block updates for Collection operations

2016-03-23  Jeremy Mikola  <<EMAIL>>
	* Document typeMap option for DropDatabase and DropIndexes

2016-03-23  Jeremy Mikola  <<EMAIL>>
	* Specify classes for returnDocument option constants

2016-03-23  Jeremy Mikola  <<EMAIL>>
	* FindOne::execute() may return an array

2016-03-14  Jeremy Mikola  <<EMAIL>>
	* Remove branch alias for 1.0.x

2016-03-04  Jeremy Mikola  <<EMAIL>>
	* Add 1.0.1 release notes

RELEASE 1.0.1
-------------
2016-02-16  minenok  <<EMAIL>>
	* PHPLIB-180: Fix option merging for Find operation

2016-01-30  Andreas Braun  <***************>
	* PHPLIB-179: Don't apply typeMap if useCursor is false

2016-01-21  Jeremy Mikola  <<EMAIL>>
	* Update README install instructions for 1.0.0

2016-01-21  Jeremy Mikola  <<EMAIL>>
	* Allow ext-mongodb 1.1.0

RELEASE 1.0.0
-------------
2016-01-21  Jeremy Mikola  <<EMAIL>>
	* Rewrite documentation for 1.0.0

2016-01-21  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-164: CreateCollection should use type map

2016-01-20  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-165: Take explicit db/coll name args in Collection ctor

2016-01-20  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-74: Use Client's default type map for Database and Collection

2016-01-20  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-164: Drop operations should use type map

2016-01-19  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-164: Database::command() should use type map

2016-01-18  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-162: Support database and collection selection via __get()

2016-01-18  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-163: __set_state() for BSON array and document models

2016-01-14  Jeremy Mikola  <<EMAIL>>
	* BSONDocument should allow property access by default

2016-01-14  Jeremy Mikola  <<EMAIL>>
	* Tests for BSON array and document serialize methods

2016-01-12  Jeremy Mikola  <<EMAIL>>
	* Executable interface is internal

2016-01-11  Jeremy Mikola  <<EMAIL>>
	* Unit tests for ListDatabases and ListIndexes options

2016-01-11  Jeremy Mikola  <<EMAIL>>
	* Test for invalid typeMap options

2016-01-11  Jeremy Mikola  <<EMAIL>>
	* Test for invalid ReadConcern options

2016-01-10  Jeremy Mikola  <<EMAIL>>
	* Clean up exception docs and use statements

2016-01-10  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-141: Replace InvalidArgumentTypeException with a factory method

2016-01-07  Jeremy Mikola  <<EMAIL>>
	* Remove unused exception classes

2016-01-07  Jeremy Mikola  <<EMAIL>>
	* Unit test for IndexInfo's ArrayAccess implementation

2016-01-07  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-141: Static method for immutable BadMethodCallException

2016-01-07  Jeremy Mikola  <<EMAIL>>
	* Add missing doc block info

2016-01-07  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-157: Improve _id extraction for Serializable objects

2016-01-07  Jeremy Mikola  <<EMAIL>>
	* Re-order functions.php alphabetically

2016-01-07  Jeremy Mikola  <<EMAIL>>
	* Test with mongodb-1.1.2 on Travis CI

2016-01-05  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-74: Use array and document classes in default type map

2015-12-28  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-74: Model classes for BSON array and document

2015-12-28  Jeremy Mikola  <<EMAIL>>
	* Update IndexInput::bsonSerialize() doc block

2015-12-28  Jeremy Mikola  <<EMAIL>>
	* Add 1.0.0-beta2 release notes

RELEASE 1.0.0-beta2
-------------------
2015-12-25  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-135: Support validator options for CreateCollection

2015-12-25  Jeremy Mikola  <<EMAIL>>
	* Don't use empty() to check for empty documents

2015-12-24  Jeremy Mikola  <<EMAIL>>
	* Clean up some doc block typos

2015-12-23  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-138: Support typeMap option for aggregate and find operations

2015-12-23  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-138: Support typeMap option for Database::command()

2015-12-23  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-138: Support typeMap option for core classes

2015-12-23  Jeremy Mikola  <<EMAIL>>
	* Refactor option handling for Client, Database, and Collection

2015-12-23  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-132: Add MongoDB 3.2 to Travis CI build matrix

2015-12-23  Jeremy Mikola  <<EMAIL>>
	* Add PHP 7 to Travis CI build matrix

2015-12-23  Jeremy Mikola  <<EMAIL>>
	* CreateIndexes can require that $indexes be a list

2015-12-23  Jeremy Mikola  <<EMAIL>>
	* Use InvalidArgumentTypeException for index option validation

2015-12-23  Jeremy Mikola  <<EMAIL>>
	* Remove unnecessary use statement

2015-12-23  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-121: Support indexOptionDefaults option for CreateCollection

2015-12-22  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-130: Support readConcern option on read operations

2015-12-17  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-130: Support readConcern option for Database and Collection

2015-12-22  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-129: Support writeConcern option for findAndModify

2015-12-17  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-132: Bump ext-mongodb dependency to >=1.1.0

2015-12-13  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-125: Support bypassDocumentValidation option on write commands

2015-12-16  Jeremy Mikola  <<EMAIL>>
	* Manually register mongodb.so for PHP 5.4 builds on Travis

2015-12-16  Jeremy Mikola  <<EMAIL>>
	* Use explicit version when installing driver extension

2015-12-16  Jeremy Mikola  <<EMAIL>>
	* Use private constant in lieu of hard-coded string

2015-12-16  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-122: Checking "ok" field in command results is redundant

2015-11-14  Anton Tuyakhov  <<EMAIL>>
	* PHPLIB-153: Add Database::command() helper

2015-12-16  Jeremy Mikola  <<EMAIL>>
	* Move invalid data providers to base TestCase class

2015-12-13  Anton Tuyakhov  <<EMAIL>>
	* Fix distinct maxTimeMS option

2015-12-14  Jeremy Mikola  <<EMAIL>>
	* Revert "PHPLIB-151: Use IPv4 localhost address for default URI"

2015-12-13  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-152: Use 1 instead of -1 for FindOne limit

2015-12-13  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-151: Use IPv4 localhost address for default URI

2015-12-11  Jeremy Mikola  <<EMAIL>>
	* Remove unused property

2015-11-02  Remi Collet  <<EMAIL>>
	* PedantryTest can ignore autoload.php added by downstream

2015-12-02  Jeremy Mikola  <<EMAIL>>
	* Update package install instructions in readme

2015-12-02  Jeremy Mikola  <<EMAIL>>
	* Update dev branch alias

2015-11-25  Hannes Magnusson  <<EMAIL>>
	* Its a bit creepy seeing my family in bug reports

2015-11-24  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-49: withOptions() clone method for Database and Collection

2015-11-24  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-145: __debugInfo for Client, Database, and Collection

2015-11-24  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-144: Use arrays to take Database and Collection options

2015-11-24  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-143: Default URI for Client constructor

2015-11-24  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-90: Client::__toString() should return connection URI

2015-11-18  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-85: Legacy index creation should use {w:1}

2015-11-18  Jeremy Mikola  <<EMAIL>>
	* Split IndexManagementFunctionalTest into Operation tests

2015-11-13  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-68: Throw when accessing fields in unacknowledged write result

2015-11-13  Jeremy Mikola  <<EMAIL>>
	* Functional tests for Delete, Insert, and Update operations

2015-11-13  Jeremy Mikola  <<EMAIL>>
	* Convert bulk write Collection functional test to Operation

2015-11-02  Jeremy Mikola  <<EMAIL>>
	* Add 1.0.0-beta1 release notes

RELEASE 1.0.0-beta1
-------------------
2015-11-02  Jeremy Mikola  <<EMAIL>>
	* Update install docs for PHPC stable and PHPLIB beta

2015-11-01  Jeremy Mikola  <<EMAIL>>
	* Shorthand array syntax

2015-11-01  Jeremy Mikola  <<EMAIL>>
	* Test with mongodb-1.0 on Travis CI

2015-11-01  Jeremy Mikola  <<EMAIL>>
	* MongoDB\Manager no longer has single write methods

2015-11-01  Jeremy Mikola  <<EMAIL>>
	* MongoDB\Driver\BulkWrite now takes an options array

2015-11-01  Jeremy Mikola  <<EMAIL>>
	* Remove helper functions now that Manager RP/WC getters exist

2015-10-23  Jeremy Mikola  <<EMAIL>>
	* Update more GitHub URLs

2015-10-23  Jeremy Mikola  <<EMAIL>>
	* Update GitHub URLs in docs

2015-10-09  Jeremy Mikola  <<EMAIL>>
	* Ensure database is dropped before asserting it doesn't exist

2015-10-09  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-133: Support typeMap option in FindOne

2015-10-09  Jeremy Mikola  <<EMAIL>>
	* Functional test for aggregate command failure

2015-10-09  Jeremy Mikola  <<EMAIL>>
	* Remove redundant option validation from FindAndModify children

2015-10-09  Jeremy Mikola  <<EMAIL>>
	* Remove unnecessary use statement

2015-10-09  Jeremy Mikola  <<EMAIL>>
	* Refactor functional tests to use DropCollection

2015-10-07  Jeremy Mikola  <<EMAIL>>
	* Update extension installation docs for PHP and HHVM

2015-10-07  Jeremy Mikola  <<EMAIL>>
	* Update installation instructions in docs

2015-10-07  Jeremy Mikola  <<EMAIL>>
	* Consolidate release notes by minor version

2015-10-07  Jeremy Mikola  <<EMAIL>>
	* Add 1.0.0-alpha1 release notes

RELEASE 1.0.0-alpha1
--------------------
2015-10-06  Jeremy Mikola  <<EMAIL>>
	* Use "autoload-dev" for loading test classes

2015-09-28  Jeremy Mikola  <<EMAIL>>
	* Replace magic string with a private constant and comments

2015-09-28  Jeremy Mikola  <<EMAIL>>
	* ListCollections functional tests

2015-09-28  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-123: Do not throw when listing indexes on nonexistent collection

2015-09-28  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-107: Fix return statement when dropping nonexistent collection

2015-09-27  Jeremy Mikola  <<EMAIL>>
	* DropDatabase functional tests

2015-09-27  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-107: Do not throw when dropping nonexistent collection

2015-09-24  Jeremy Mikola  <<EMAIL>>
	* Revise docs and exception message for assertDatabaseExists()

2015-09-14  Jeremy Mikola  <<EMAIL>>
	* Trust that Collection's writeConcern is always set

2015-09-14  Jeremy Mikola  <<EMAIL>>
	* Relax writeConcern option checks in operation classes

2015-09-14  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-111: Ensure read ops use appropriate read preference

2015-09-14  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-73: Database and Collection can inherit RP/WC from Manager

2015-09-12  Jeremy Mikola  <<EMAIL>>
	* Bump extension version to beta

2015-09-24  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-120: Require PHP 5.4+

2015-09-18  Daniel Kozak  <<EMAIL>>
	* fix doc url

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Validate Database $databaseName and test getters

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Validate Collection $namespace and test getters

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Rename Collection class properties

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Fix type documentation for Count and Distinct $filter arg

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Disable xdebug extension on Travis

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Unit tests for Find operation

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Unit tests for Aggregate operation

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Unit tests for Distinct operation and allow array/object $filter

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Unit tests for Count operation and allow array/object $filter

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Validate Aggregation $pipeline before $options

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Allow array/object for Collection::find() and findOne() $filter

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Test type checking for BulkWrite constructor options

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Refactor unit tests for write operations

2015-09-02  Jeremy Mikola  <<EMAIL>>
	* Rely on default type map conversion

2015-09-02  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-73: Database and Collection should inherit Manager's WC and RP

2015-08-30  Jeremy Mikola  <<EMAIL>>
	* Print core dumps for segfaults on Travis

2015-08-31  Jeremy Mikola  <<EMAIL>>
	* Remove copypasta in CreateCollection

2015-08-31  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-109: Extract BulkWrite operation class

2015-08-27  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-109: Extract ReplaceOne, UpdateOne, and UpdateMany operation classes

2015-08-27  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-109: Extract DeleteOne and DeleteMany operation classes

2015-08-27  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-109: Extract InsertOne and InsertMany operation classes

2015-08-27  Jeremy Mikola  <<EMAIL>>
	* Refer to findAndModify docs in related Collection methods

2015-07-27  Derick Rethans  <<EMAIL>>
	* PHPC-118: Expect documents as objects (new default type)

2015-07-27  Derick Rethans  <<EMAIL>>
	* PHPLIB-118: Specify "root" option in typemap, as it's separate from "document"

2015-08-26  Jeremy Mikola  <<EMAIL>>
	* Require ext-mongodb ^1.0.0

2015-08-26  Jeremy Mikola  <<EMAIL>>
	* Bump dev-master to 0.3.x-dev

2015-06-18  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-110: Extract Find and FindOne operation classes

2015-06-30  Derick Rethans  <<EMAIL>>
	* PHPLIB-108: Use MongoDB\BSON namespace prefix

2015-06-19  Jeremy Mikola  <<EMAIL>>
	* Make expected document assertions more flexible

2015-06-19  Jeremy Mikola  <<EMAIL>>
	* Ensure operations return documents as objects by default

2015-06-18  Derick Rethans  <<EMAIL>>
	* Because the typemap says 'document as array', we now need to change the return value with a cast to 'object'

2015-06-18  Derick Rethans  <<EMAIL>>
	* Compare all arrays of documents by setting the typemap for documents to 'array'.

2015-06-18  Derick Rethans  <<EMAIL>>
	* Use type map to force arrays instead of objects.

2015-06-17  Jeremy Mikola  <<EMAIL>>
	* Aggregate should check server support before returning a cursor

2015-06-17  Jeremy Mikola  <<EMAIL>>
	* Relax assertion in AggregateFunctionalTest

2015-06-17  Jeremy Mikola  <<EMAIL>>
	* Older servers may return count "n" as a float

2015-06-17  Jeremy Mikola  <<EMAIL>>
	* Don't assume document PHP type mapping in FunctionalTestCase

2015-06-16  Jeremy Mikola  <<EMAIL>>
	* Remove unused Collection constants and methods

2015-06-16  Jeremy Mikola  <<EMAIL>>
	* FeatureDetection utility class is obsolete

2015-06-16  Jeremy Mikola  <<EMAIL>>
	* assertCommandSucceeded() now accepts a result document

2015-06-16  Jeremy Mikola  <<EMAIL>>
	* Extract DropIndexes operation class

2015-06-16  Jeremy Mikola  <<EMAIL>>
	* Extract Database::createCollection() to an operation class

2015-06-16  Jeremy Mikola  <<EMAIL>>
	* Extra DropCollection operation class

2015-06-16  Jeremy Mikola  <<EMAIL>>
	* Extract DropDatabase operation class

2015-06-16  Jeremy Mikola  <<EMAIL>>
	* Extract Collection::listIndexes() to an operation class

2015-06-16  Jeremy Mikola  <<EMAIL>>
	* Extract Database::listCollections() to an operation class

2015-06-16  Jeremy Mikola  <<EMAIL>>
	* Extract Client::listDatabases() to an operation class

2015-06-14  Jeremy Mikola  <<EMAIL>>
	* Replace private methods with generate_index_name() function

2015-06-14  Jeremy Mikola  <<EMAIL>>
	* Return documents as objects from Collection findAndModify methods

2015-06-14  Jeremy Mikola  <<EMAIL>>
	* Extract Collection findAndModify methods to operation classes

2015-06-11  Jeremy Mikola  <<EMAIL>>
	* Extract Collection::count() to an operation class

2015-06-10  Jeremy Mikola  <<EMAIL>>
	* Extract Collection::createIndexes() to an operation class

2015-06-10  Jeremy Mikola  <<EMAIL>>
	* Extract Collection::distinct() to an operation class

2015-06-10  Jeremy Mikola  <<EMAIL>>
	* Extract Collection::aggregate() to an operation class

2015-06-10  Jeremy Mikola  <<EMAIL>>
	* Executable interface for operations

2015-06-14  Jeremy Mikola  <<EMAIL>>
	* Create functions.php file for utility functions

2015-05-06  Jeremy Mikola  <<EMAIL>>
	* Split UnexpectedTypeException for logic and runtime errors

2015-05-14  Sergey  <<EMAIL>>
	* Update data.md

2015-05-12  Jeremy Mikola  <<EMAIL>>
	* Reminder to push current branch (not just tags) for release

2015-05-12  Jeremy Mikola  <<EMAIL>>
	* Add 0.2.0 release notes
