

[[Elasticsearch_Namespaces_IndicesNamespace]]
=== Elasticsearch\Namespaces\IndicesNamespace



Class IndicesNamespace

*Description*


NOTE: this file is autogenerated using util/GenerateEndpoints.php
and Elasticsearch 7.12.1 (3186837139b9c6b6d23c3200870651f10d3343b7)


*Methods*

The class defines the following methods:

* <<Elasticsearch_Namespaces_IndicesNamespaceaddBlock_addBlock,`addBlock()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceanalyze_analyze,`analyze()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceclearCache_clearCache,`clearCache()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceclone_clone,`clone()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceclose_close,`close()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacecreate_create,`create()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacecreateDataStream_createDataStream,`createDataStream()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacedataStreamsStats_dataStreamsStats,`dataStreamsStats()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacedelete_delete,`delete()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacedeleteAlias_deleteAlias,`deleteAlias()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacedeleteDataStream_deleteDataStream,`deleteDataStream()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacedeleteIndexTemplate_deleteIndexTemplate,`deleteIndexTemplate()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacedeleteTemplate_deleteTemplate,`deleteTemplate()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceexists_exists,`exists()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceexistsAlias_existsAlias,`existsAlias()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceexistsIndexTemplate_existsIndexTemplate,`existsIndexTemplate()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceexistsTemplate_existsTemplate,`existsTemplate()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceexistsType_existsType,`existsType()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceflush_flush,`flush()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceflushSynced_flushSynced,`flushSynced()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceforcemerge_forcemerge,`forcemerge()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacefreeze_freeze,`freeze()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceget_get,`get()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacegetAlias_getAlias,`getAlias()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacegetDataStream_getDataStream,`getDataStream()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacegetFieldMapping_getFieldMapping,`getFieldMapping()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacegetIndexTemplate_getIndexTemplate,`getIndexTemplate()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacegetMapping_getMapping,`getMapping()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacegetSettings_getSettings,`getSettings()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacegetTemplate_getTemplate,`getTemplate()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacegetUpgrade_getUpgrade,`getUpgrade()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacemigrateToDataStream_migrateToDataStream,`migrateToDataStream()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceopen_open,`open()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacepromoteDataStream_promoteDataStream,`promoteDataStream()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceputAlias_putAlias,`putAlias()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceputIndexTemplate_putIndexTemplate,`putIndexTemplate()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceputMapping_putMapping,`putMapping()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceputSettings_putSettings,`putSettings()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceputTemplate_putTemplate,`putTemplate()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacerecovery_recovery,`recovery()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacerefresh_refresh,`refresh()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacereloadSearchAnalyzers_reloadSearchAnalyzers,`reloadSearchAnalyzers()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceresolveIndex_resolveIndex,`resolveIndex()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacerollover_rollover,`rollover()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacesegments_segments,`segments()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceshardStores_shardStores,`shardStores()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceshrink_shrink,`shrink()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacesimulateIndexTemplate_simulateIndexTemplate,`simulateIndexTemplate()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacesimulateTemplate_simulateTemplate,`simulateTemplate()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacesplit_split,`split()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacestats_stats,`stats()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceunfreeze_unfreeze,`unfreeze()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceupdateAliases_updateAliases,`updateAliases()`>>
* <<Elasticsearch_Namespaces_IndicesNamespaceupgrade_upgrade,`upgrade()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacevalidateQuery_validateQuery,`validateQuery()`>>
* <<Elasticsearch_Namespaces_IndicesNamespacegetAliases_getAliases,`getAliases()`>>



[[Elasticsearch_Namespaces_IndicesNamespaceaddBlock_addBlock]]
.`addBlock(array $params = [])`
****
[source,php]
----
/*
$params['index']              = (list) A comma separated list of indices to add a block to
$params['block']              = (string) The block to add (one of read, write, read_only or metadata)
$params['timeout']            = (time) Explicit operation timeout
$params['master_timeout']     = (time) Specify timeout for connection to master
$params['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']   = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceanalyze_analyze]]
.`analyze(array $params = [])`
****
[source,php]
----
/*
$params['index'] = (string) The name of the index to scope the operation
$params['body']  = (array) Define analyzer/tokenizer parameters and the text on which the analysis should be performed
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceclearCache_clearCache]]
.`clearCache(array $params = [])`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index name to limit the operation
$params['fielddata']          = (boolean) Clear field data
$params['fields']             = (list) A comma-separated list of fields to clear when using the `fielddata` parameter (default: all)
$params['query']              = (boolean) Clear query caches
$params['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']   = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
$params['request']            = (boolean) Clear request cache
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceclone_clone]]
.`clone(array $params = [])`
****
[source,php]
----
/*
$params['index']                  = (string) The name of the source index to clone
$params['target']                 = (string) The name of the target index to clone into
$params['timeout']                = (time) Explicit operation timeout
$params['master_timeout']         = (time) Specify timeout for connection to master
$params['wait_for_active_shards'] = (string) Set the number of active shards to wait for on the cloned index before the operation returns.
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceclose_close]]
.`close(array $params = [])`
****
[source,php]
----
/*
$params['index']                  = (list) A comma separated list of indices to close
$params['timeout']                = (time) Explicit operation timeout
$params['master_timeout']         = (time) Specify timeout for connection to master
$params['ignore_unavailable']     = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']       = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']       = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
$params['wait_for_active_shards'] = (string) Sets the number of active shards to wait for before the operation returns. Set to `index-setting` to wait according to the index setting `index.write.wait_for_active_shards`, or `all` to wait for all shards, or an integer. Defaults to `0`.
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacecreate_create]]
.`create(array $params = [])`
****
[source,php]
----
/*
$params['index']                  = (string) The name of the index
$params['include_type_name']      = (boolean) Whether a type should be expected in the body of the mappings.
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacecreateDataStream_createDataStream]]
.`createDataStream(array $params = [])`
****
[source,php]
----
/*
$params['name'] = (string) The name of the data stream
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacedataStreamsStats_dataStreamsStats]]
.`dataStreamsStats(array $params = [])`
****
[source,php]
----
/*
$params['name'] = (list) A comma-separated list of data stream names; use `_all` or empty string to perform the operation on all data streams
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacedelete_delete]]
.`delete(array $params = [])`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of indices to delete; use `_all` or `*` string to delete all indices
$params['timeout']            = (time) Explicit operation timeout
$params['master_timeout']     = (time) Specify timeout for connection to master
$params['ignore_unavailable'] = (boolean) Ignore unavailable indexes (default: false)
$params['allow_no_indices']   = (boolean) Ignore if a wildcard expression resolves to no concrete indices (default: false)
$params['expand_wildcards']   = (enum) Whether wildcard expressions should get expanded to open or closed indices (default: open) (Options = open,closed,hidden,none,all) (Default = open)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacedeleteAlias_deleteAlias]]
.`deleteAlias(array $params = [])`
****
[source,php]
----
/*
$params['index']          = (list) A comma-separated list of index names (supports wildcards); use `_all` for all indices (Required)
$params['name']           = (list) A comma-separated list of aliases to delete (supports wildcards); use `_all` to delete all aliases for the specified indices. (Required)
$params['timeout']        = (time) Explicit timestamp for the document
$params['master_timeout'] = (time) Specify timeout for connection to master
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacedeleteDataStream_deleteDataStream]]
.`deleteDataStream(array $params = [])`
****
[source,php]
----
/*
$params['name']             = (list) A comma-separated list of data streams to delete; use `*` to delete all data streams
$params['expand_wildcards'] = (enum) Whether wildcard expressions should get expanded to open or closed indices (default: open) (Options = open,closed,hidden,none,all) (Default = open)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacedeleteIndexTemplate_deleteIndexTemplate]]
.`deleteIndexTemplate(array $params = [])`
****
[source,php]
----
/*
$params['name']           = (string) The name of the template
$params['timeout']        = (time) Explicit operation timeout
$params['master_timeout'] = (time) Specify timeout for connection to master
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacedeleteTemplate_deleteTemplate]]
.`deleteTemplate(array $params = [])`
****
[source,php]
----
/*
$params['name']           = (string) The name of the template
$params['timeout']        = (time) Explicit operation timeout
$params['master_timeout'] = (time) Specify timeout for connection to master
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceexists_exists]]
.`exists(array $params = [])`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names
$params['local']              = (boolean) Return local information, do not retrieve the state from master node (default: false)
$params['ignore_unavailable'] = (boolean) Ignore unavailable indexes (default: false)
$params['allow_no_indices']   = (boolean) Ignore if a wildcard expression resolves to no concrete indices (default: false)
$params['expand_wildcards']   = (enum) Whether wildcard expressions should get expanded to open or closed indices (default: open) (Options = open,closed,hidden,none,all) (Default = open)
$params['flat_settings']      = (boolean) Return settings in flat format (default: false)
$params['include_defaults']   = (boolean) Whether to return all default setting for each of the indices. (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceexistsAlias_existsAlias]]
.`existsAlias(array $params = [])`
****
[source,php]
----
/*
$params['name']               = (list) A comma-separated list of alias names to return (Required)
$params['index']              = (list) A comma-separated list of index names to filter aliases
$params['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']   = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = all)
$params['local']              = (boolean) Return local information, do not retrieve the state from master node (default: false)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceexistsIndexTemplate_existsIndexTemplate]]
.`existsIndexTemplate(array $params = [])`
****
[source,php]
----
/*
$params['name']           = (string) The name of the template
$params['flat_settings']  = (boolean) Return settings in flat format (default: false)
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
$params['local']          = (boolean) Return local information, do not retrieve the state from master node (default: false)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceexistsTemplate_existsTemplate]]
.`existsTemplate(array $params = [])`
****
[source,php]
----
/*
$params['name']           = (list) The comma separated names of the index templates
$params['flat_settings']  = (boolean) Return settings in flat format (default: false)
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
$params['local']          = (boolean) Return local information, do not retrieve the state from master node (default: false)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceexistsType_existsType]]
.`existsType(array $params = [])`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names; use `_all` to check the types across all indices
$params['type']               = DEPRECATED (list) A comma-separated list of document types to check
$params['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']   = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
$params['local']              = (boolean) Return local information, do not retrieve the state from master node (default: false)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceflush_flush]]
.`flush(array $params = [])`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names; use `_all` or empty string for all indices
$params['force']              = (boolean) Whether a flush should be forced even if it is not necessarily needed ie. if no changes will be committed to the index. This is useful if transaction log IDs should be incremented even if no uncommitted changes are present. (This setting can be considered as internal)
$params['wait_if_ongoing']    = (boolean) If set to true the flush operation will block until the flush can be executed if another flush operation is already executing. The default is true. If set to false the flush will be skipped iff if another flush operation is already running.
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceflushSynced_flushSynced]]
.`flushSynced(array $params = [])`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names; use `_all` or empty string for all indices
$params['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']   = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,none,all) (Default = open)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceforcemerge_forcemerge]]
.`forcemerge(array $params = [])`
****
[source,php]
----
/*
$params['index']                = (list) A comma-separated list of index names; use `_all` or empty string to perform the operation on all indices
$params['flush']                = (boolean) Specify whether the index should be flushed after performing the operation (default: true)
$params['ignore_unavailable']   = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']     = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']     = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
$params['max_num_segments']     = (number) The number of segments the index should be merged into (default: dynamic)
$params['only_expunge_deletes'] = (boolean) Specify whether the operation should only expunge deleted documents
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacefreeze_freeze]]
.`freeze(array $params = [])`
****
[source,php]
----
/*
$params['index']                  = (string) The name of the index to freeze
$params['timeout']                = (time) Explicit operation timeout
$params['master_timeout']         = (time) Specify timeout for connection to master
$params['ignore_unavailable']     = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']       = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']       = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = closed)
$params['wait_for_active_shards'] = (string) Sets the number of active shards to wait for before the operation returns.
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceget_get]]
.`get(array $params = [])`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names
$params['include_type_name']  = (boolean) Whether to add the type name to the response (default: false)
$params['local']              = (boolean) Return local information, do not retrieve the state from master node (default: false)
$params['ignore_unavailable'] = (boolean) Ignore unavailable indexes (default: false)
$params['allow_no_indices']   = (boolean) Ignore if a wildcard expression resolves to no concrete indices (default: false)
$params['expand_wildcards']   = (enum) Whether wildcard expressions should get expanded to open or closed indices (default: open) (Options = open,closed,hidden,none,all) (Default = open)
$params['flat_settings']      = (boolean) Return settings in flat format (default: false)
$params['include_defaults']   = (boolean) Whether to return all default setting for each of the indices. (Default = false)
$params['master_timeout']     = (time) Specify timeout for connection to master
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacegetAlias_getAlias]]
.`getAlias(array $params = [])`
****
[source,php]
----
/*
$params['name']               = (list) A comma-separated list of alias names to return
$params['index']              = (list) A comma-separated list of index names to filter aliases
$params['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']   = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = all)
$params['local']              = (boolean) Return local information, do not retrieve the state from master node (default: false)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacegetDataStream_getDataStream]]
.`getDataStream(array $params = [])`
****
[source,php]
----
/*
$params['name']             = (list) A comma-separated list of data streams to get; use `*` to get all data streams
$params['expand_wildcards'] = (enum) Whether wildcard expressions should get expanded to open or closed indices (default: open) (Options = open,closed,hidden,none,all) (Default = open)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacegetFieldMapping_getFieldMapping]]
.`getFieldMapping(array $params = [])`
****
[source,php]
----
/*
$params['fields']             = (list) A comma-separated list of fields (Required)
$params['index']              = (list) A comma-separated list of index names
$params['type']               = DEPRECATED (list) A comma-separated list of document types
$params['include_type_name']  = (boolean) Whether a type should be returned in the body of the mappings.
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacegetIndexTemplate_getIndexTemplate]]
.`getIndexTemplate(array $params = [])`
****
[source,php]
----
/*
$params['name']           = (list) The comma separated names of the index templates
$params['flat_settings']  = (boolean) Return settings in flat format (default: false)
$params['master_timeout'] = (time) Explicit operation timeout for connection to master node
$params['local']          = (boolean) Return local information, do not retrieve the state from master node (default: false)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacegetMapping_getMapping]]
.`getMapping(array $params = [])`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names
$params['type']               = DEPRECATED (list) A comma-separated list of document types
$params['include_type_name']  = (boolean) Whether to add the type name to the response (default: false)
$params['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']   = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
$params['master_timeout']     = (time) Specify timeout for connection to master
$params['local']              = (boolean) Return local information, do not retrieve the state from master node (default: false)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacegetSettings_getSettings]]
.`getSettings(array $params = [])`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names; use `_all` or empty string to perform the operation on all indices
$params['name']               = (list) The name of the settings that should be included
$params['master_timeout']     = (time) Specify timeout for connection to master
$params['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']   = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = all)
$params['flat_settings']      = (boolean) Return settings in flat format (default: false)
$params['local']              = (boolean) Return local information, do not retrieve the state from master node (default: false)
$params['include_defaults']   = (boolean) Whether to return all default setting for each of the indices. (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacegetTemplate_getTemplate]]
.`getTemplate(array $params = [])`
****
[source,php]
----
/*
$params['name']              = (list) The comma separated names of the index templates
$params['include_type_name'] = (boolean) Whether a type should be returned in the body of the mappings.
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacegetUpgrade_getUpgrade]]
.`getUpgrade(array $params = [])`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names; use `_all` or empty string to perform the operation on all indices
$params['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']   = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacemigrateToDataStream_migrateToDataStream]]
.`migrateToDataStream(array $params = [])`
****
[source,php]
----
/*
$params['name'] = (string) The name of the alias to migrate
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceopen_open]]
.`open(array $params = [])`
****
[source,php]
----
/*
$params['index']                  = (list) A comma separated list of indices to open
$params['timeout']                = (time) Explicit operation timeout
$params['master_timeout']         = (time) Specify timeout for connection to master
$params['ignore_unavailable']     = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']       = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']       = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = closed)
$params['wait_for_active_shards'] = (string) Sets the number of active shards to wait for before the operation returns.
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacepromoteDataStream_promoteDataStream]]
.`promoteDataStream(array $params = [])`
****
[source,php]
----
/*
$params['name'] = (string) The name of the data stream
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceputAlias_putAlias]]
.`putAlias(array $params = [])`
****
[source,php]
----
/*
$params['index']          = (list) A comma-separated list of index names the alias should point to (supports wildcards); use `_all` to perform the operation on all indices. (Required)
$params['name']           = (string) The name of the alias to be created or updated (Required)
$params['timeout']        = (time) Explicit timestamp for the document
$params['master_timeout'] = (time) Specify timeout for connection to master
$params['body']           = (array) The settings for the alias, such as `routing` or `filter`
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceputIndexTemplate_putIndexTemplate]]
.`putIndexTemplate(array $params = [])`
****
[source,php]
----
/*
$params['name']           = (string) The name of the template
$params['create']         = (boolean) Whether the index template should only be added if new or can also replace an existing one (Default = false)
$params['cause']          = (string) User defined reason for creating/updating the index template (Default = )
$params['master_timeout'] = (time) Specify timeout for connection to master
$params['body']           = (array) The template definition (Required)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceputMapping_putMapping]]
.`putMapping(array $params = [])`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names the mapping should be added to (supports wildcards); use `_all` or omit to add the mapping on all indices.
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceputSettings_putSettings]]
.`putSettings(array $params = [])`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names; use `_all` or empty string to perform the operation on all indices
$params['master_timeout']     = (time) Specify timeout for connection to master
$params['timeout']            = (time) Explicit operation timeout
$params['preserve_existing']  = (boolean) Whether to update existing settings. If set to `true` existing settings on an index remain unchanged, the default is `false`
$params['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']   = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
$params['flat_settings']      = (boolean) Return settings in flat format (default: false)
$params['body']               = (array) The index settings to be updated (Required)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceputTemplate_putTemplate]]
.`putTemplate(array $params = [])`
****
[source,php]
----
/*
$params['name']              = (string) The name of the template
$params['include_type_name'] = (boolean) Whether a type should be returned in the body of the mappings.
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacerecovery_recovery]]
.`recovery(array $params = [])`
****
[source,php]
----
/*
$params['index']       = (list) A comma-separated list of index names; use `_all` or empty string to perform the operation on all indices
$params['detailed']    = (boolean) Whether to display detailed information about shard recovery (Default = false)
$params['active_only'] = (boolean) Display only those recoveries that are currently on-going (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacerefresh_refresh]]
.`refresh(array $params = [])`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names; use `_all` or empty string to perform the operation on all indices
$params['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']   = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacereloadSearchAnalyzers_reloadSearchAnalyzers]]
.`reloadSearchAnalyzers(array $params = [])`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names to reload analyzers for
$params['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']   = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceresolveIndex_resolveIndex]]
.`resolveIndex(array $params = [])`
*NOTE:* This API is EXPERIMENTAL and may be changed or removed completely in a future release
****
[source,php]
----
/*
$params['name']             = (list) A comma-separated list of names or wildcard expressions
$params['expand_wildcards'] = (enum) Whether wildcard expressions should get expanded to open or closed indices (default: open) (Options = open,closed,hidden,none,all) (Default = open)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacerollover_rollover]]
.`rollover(array $params = [])`
****
[source,php]
----
/*
$params['alias']                  = (string) The name of the alias to rollover (Required)
$params['new_index']              = (string) The name of the rollover index
$params['include_type_name']      = (boolean) Whether a type should be included in the body of the mappings.
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacesegments_segments]]
.`segments(array $params = [])`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names; use `_all` or empty string to perform the operation on all indices
$params['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']   = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
$params['verbose']            = (boolean) Includes detailed memory usage by Lucene. (Default = false)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceshardStores_shardStores]]
.`shardStores(array $params = [])`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names; use `_all` or empty string to perform the operation on all indices
$params['status']             = (list) A comma-separated list of statuses used to filter on shards to get store information for (Options = green,yellow,red,all)
$params['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']   = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceshrink_shrink]]
.`shrink(array $params = [])`
****
[source,php]
----
/*
$params['index']                  = (string) The name of the source index to shrink
$params['target']                 = (string) The name of the target index to shrink into
$params['copy_settings']          = (boolean) whether or not to copy settings from the source index (defaults to false)
$params['timeout']                = (time) Explicit operation timeout
$params['master_timeout']         = (time) Specify timeout for connection to master
$params['wait_for_active_shards'] = (string) Set the number of active shards to wait for on the shrunken index before the operation returns.
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacesimulateIndexTemplate_simulateIndexTemplate]]
.`simulateIndexTemplate(array $params = [])`
****
[source,php]
----
/*
$params['name']           = (string) The name of the index (it must be a concrete index name)
$params['create']         = (boolean) Whether the index template we optionally defined in the body should only be dry-run added if new or can also replace an existing one (Default = false)
$params['cause']          = (string) User defined reason for dry-run creating the new template for simulation purposes (Default = )
$params['master_timeout'] = (time) Specify timeout for connection to master
$params['body']           = (array) New index template definition, which will be included in the simulation, as if it already exists in the system
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacesimulateTemplate_simulateTemplate]]
.`simulateTemplate(array $params = [])`
****
[source,php]
----
/*
$params['name']           = (string) The name of the index template
$params['create']         = (boolean) Whether the index template we optionally defined in the body should only be dry-run added if new or can also replace an existing one (Default = false)
$params['cause']          = (string) User defined reason for dry-run creating the new template for simulation purposes (Default = )
$params['master_timeout'] = (time) Specify timeout for connection to master
$params['body']           = (array) New index template definition to be simulated, if no index template name is specified
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacesplit_split]]
.`split(array $params = [])`
****
[source,php]
----
/*
$params['index']                  = (string) The name of the source index to split
$params['target']                 = (string) The name of the target index to split into
$params['copy_settings']          = (boolean) whether or not to copy settings from the source index (defaults to false)
$params['timeout']                = (time) Explicit operation timeout
$params['master_timeout']         = (time) Specify timeout for connection to master
$params['wait_for_active_shards'] = (string) Set the number of active shards to wait for on the shrunken index before the operation returns.
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacestats_stats]]
.`stats(array $params = [])`
****
[source,php]
----
/*
$params['metric']                     = (list) Limit the information returned the specific metrics.
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceunfreeze_unfreeze]]
.`unfreeze(array $params = [])`
****
[source,php]
----
/*
$params['index']                  = (string) The name of the index to unfreeze
$params['timeout']                = (time) Explicit operation timeout
$params['master_timeout']         = (time) Specify timeout for connection to master
$params['ignore_unavailable']     = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']       = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']       = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = closed)
$params['wait_for_active_shards'] = (string) Sets the number of active shards to wait for before the operation returns.
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceupdateAliases_updateAliases]]
.`updateAliases(array $params = [])`
****
[source,php]
----
/*
$params['timeout']        = (time) Request timeout
$params['master_timeout'] = (time) Specify timeout for connection to master
$params['body']           = (array) The definition of `actions` to perform (Required)
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespaceupgrade_upgrade]]
.`upgrade(array $params = [])`
****
[source,php]
----
/*
$params['index']                 = (list) A comma-separated list of index names; use `_all` or empty string to perform the operation on all indices
$params['allow_no_indices']      = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']      = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
$params['ignore_unavailable']    = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['wait_for_completion']   = (boolean) Specify whether the request should block until the all segments are upgraded (default: false)
$params['only_ancient_segments'] = (boolean) If true, only ancient (an older Lucene major release) segments will be upgraded
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacevalidateQuery_validateQuery]]
.`validateQuery(array $params = [])`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names to restrict the operation; use `_all` or empty string to perform the operation on all indices
$params['type']               = DEPRECATED (list) A comma-separated list of document types to restrict the operation; leave empty to perform the operation on all types
$params['explain']            = (boolean) Return detailed information about the error
$params['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
$params['allow_no_indices']   = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
$params['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both. (Options = open,closed,hidden,none,all) (Default = open)
$params['q']                  = (string) Query in the Lucene query string syntax
$params['analyzer']           = (string) The analyzer to use for the query string
$params['analyze_wildcard']   = (boolean) Specify whether wildcard and prefix queries should be analyzed (default: false)
$params['default_operator']   = (enum) The default operator for query string query (AND or OR) (Options = AND,OR) (Default = OR)
$params['df']                 = (string) The field to use as default where no field prefix is given in the query string
$params['lenient']            = (boolean) Specify whether format-based query failures (such as providing text to a numeric field) should be ignored
$params['rewrite']            = (boolean) Provide a more detailed explanation showing the actual Lucene query that will be executed.
*/
----
****



[[Elasticsearch_Namespaces_IndicesNamespacegetAliases_getAliases]]
.`getAliases(array $params = [])`
****
[source,php]
----
/*
Alias function to getAlias()
*/
----
****


