# CHANGELOG

## 1.8.939 - 2021-4-16
- Add API GetDomain.
- Add API ListSystemAnalyzers.
- Add parameter domain to API ListAppGroups, ModifyAppGroup, CreateAppGroup, DescribeAppGroup, DescribeApp.


## 1.8.938 - 2021-4-14
- Support latest openAPIs.


## 1.8.937 - 2021-4-14
- Delete MultiMedia Post Scan API.


## 1.8.936 - 2021-4-13
- Supported ConfigureDtsJob SynchronizationDirection.
- Fixed DescribePreCheckStatus JobProgress bootTime.


## 1.8.935 - 2021-4-13
- Release AddFaceVideoTemplate DeleteFaceVideoTemplate QueryFaceVideoTemplate MergeVideoModelFace.


## 1.8.934 - 2021-4-12
- Export config api.


## 1.8.933 - 2021-4-12
- Support GetDBTopology API.


## 1.8.932 - 2021-4-9
- Bank Card Ocr and Verify.


## 1.8.931 - 2021-4-9
- Update API param.


## 1.8.930 - 2021-4-8
- Release DetectVideoIPCObject.


## 1.8.929 - 2021-4-6
- Release MonitorExamination.


## 1.8.928 - 2021-4-6
- Update ScreenChestCT DetectRibFracture.


## 1.8.927 - 2021-4-1
- Add data service api, including ListAnalyticsData.
- Update file uploading api, including GenerateFileUploadURL.


## 1.8.926 - 2021-3-31
- Ecs support hibernation.
- DescribeSnapshotGroups add ProgressStatus.


## 1.8.925 - 2021-3-30
- Generated 2020-11-26 for `Airec`.


## 1.8.924 - 2021-3-30
- Supported MultiMedia Post scan.


## 1.8.923 - 2021-3-30
- Supported Open Api.


## 1.8.922 - 2021-3-30
- Supported Open Api.


## 1.8.921 - 2021-3-30
- Monitor.


## 1.8.920 - 2021-3-30
- Support ListDDLPublishRecords API.
- Fixed GetMetaTableColumn, GetMetaTableDetailInfo API response param DataLength lack of precision.


## 1.8.919 - 2021-3-26
- Update api.


## 1.8.918 - 2021-3-26
- Add RenewAdditionalBandwidth API.


## 1.8.917 - 2021-3-26
- Upgrade mongodb sdk.


## 1.8.916 - 2021-3-25
- Add DescribeInstanceBill.
- Update QuerySettleBill to support RecordID filter.


## 1.8.915 - 2021-3-25
- Release autoscaling apis for timer features.


## 1.8.914 - 2021-3-25
- Change indicator date type of ListHistoriticalAgentReport.


## 1.8.913 - 2021-3-24
- Export new API.


## 1.8.912 - 2021-3-23
- Update Open API.


## 1.8.911 - 2021-3-22
- CreateEai api adds SecurityGroupId, VSwitchId parameters.


## 1.8.910 - 2021-3-22
- Add OneConsole support.
- Add ACK support.
- Edit ListSearchLog etc.


## 1.8.909 - 2021-3-21
- Support watermark.
- Support pdf preview.


## 1.8.908 - 2021-3-19
- Mod OpenVCluster add parameters, support cloud product assess.
- Mod ListDashboards add parameters, support cloud product query.


## 1.8.907 - 2021-3-19
- StateConfiguration in CreateStateConfigurationResponse changes from list to single object.


## 1.8.906 - 2021-3-19
- Modify the helpUrl parameter of OnsTopicList and OnsGroupList API to be invisible.


## 1.8.905 - 2021-3-19
- Export new API.


## 1.8.904 - 2021-3-18
- Edit GetTrace api.
- Edit GetMultipleTrace api.


## 1.8.903 - 2021-3-18
- Generated 2018-07-13 for `Ft`.


## 1.8.902 - 2021-3-18
- Generated 2018-07-13 for `Ft`.


## 1.8.901 - 2021-3-18
- Generated 2018-07-13 for `Ft`.


## 1.8.900 - 2021-3-17
- Add organizaition security center API.


## 1.8.899 - 2021-3-17
- Update MakeSuperResolutionImage.


## 1.8.898 - 2021-3-13
- Supportd DescribeTableStatisticsRequest.


## 1.8.897 - 2021-3-12
- Supported batch delete for contact template.
- Supported save for contact template.
- Supported set default for contact template.


## 1.8.896 - 2021-3-11
- Update DetectIPCPedestrian.


## 1.8.895 - 2021-3-9
- Export new API for predictive dialer.


## 1.8.894 - 2021-3-4
- Release RecognizeHandGesture.


## 1.8.893 - 2021-3-4
- Update Ocr.


## 1.8.892 - 2021-3-3
- Add GetRepositoryTagV2 and DeleteRepositoryTagV2 API.


## 1.8.891 - 2021-3-3
- Update Compareface.


## 1.8.890 - 2021-3-3
- Update Open API.


## 1.8.889 - 2021-3-3
- Add Api.


## 1.8.888 - 2021-3-3
- Delete API.


## 1.8.887 - 2021-3-3
- Update ScreenChestCT.


## 1.8.886 - 2021-3-2
- Update default endpoints.


## 1.8.885 - 2021-3-2
- Supported API for dnsCache.
- Supported API for GTM.


## 1.8.884 - 2021-3-2
- Cloud Storage Gateway openapi sdk is released.


## 1.8.883 - 2021-3-2
- Support GroupCoverFace ExternalId for ListFaceGroups.
- Support ResetItems for UpdateFaceGroup.


## 1.8.882 - 2021-3-1
- AMP Version Change.


## 1.8.881 - 2021-3-1
- Mod ListServerlessTopNApps.


## 1.8.880 - 2021-2-28
- AMP Version Change.


## 1.8.879 - 2021-2-26
- Add ListServerlessTopNApps.


## 1.8.878 - 2021-2-26
- Support SLR by InitializeDbsServiceLinkedRole action.
- Change DescribeIncrementBackupList and DescribeFullBackupList to Support Range filter by endTimestamp.
- Change DescribeRestoreTaskList to Support Range filter by createTime.


## 1.8.877 - 2021-2-25
- Support SLR by InitializeDbsServiceLinkedRole action.
- Change DescribeIncrementBackupList and DescribeFullBackupList to Support Range filter.


## 1.8.876 - 2021-2-24
- ALL API.


## 1.8.875 - 2021-2-23
- Support version in ListAppInstance response.
- Support clusterId in CreateEnvironment request.


## 1.8.874 - 2021-2-23
- Supported List Namespaces.
- Supported List Groups.
- Supported List Jobs.


## 1.8.873 - 2021-2-20
- Add CheckServiceStatus.
- Add OpenXtraceDefaultSLR.
- Add OpenArmsDefaultSLR.


## 1.8.872 - 2021-2-19
- Update Imageaudit.


## 1.8.871 - 2021-2-19
- Supported all language sdk.
- Fixed some bugs for polardbx.


## 1.8.870 - 2021-2-19
- Release UnderstandVideoContent.


## 1.8.869 - 2021-2-19
- ReportInstancesStatus OpenAPI supports IssueCategory parameter.


## 1.8.868 - 2021-2-19
- Generated 2018-07-13 for `Ft`.


## 1.8.867 - 2021-2-19
- Add username for RunCommand and InvokeCommand.


## 1.8.866 - 2021-2-19
- Update videorecog.


## 1.8.865 - 2021-2-19
- Create Lindorm Open api.


## 1.8.864 - 2021-2-19
- Supported SystemDiskPerformanceLevel, SystemDiskDeleteWithInstance, DataDiskPerformanceLevel, NetworkInterfaceSecurityGroupIds, SecurityGroupIds, PrivateIpAddress for CreateLaunchTemplate, CreateLaunchTemplateVersion, DescribeLaunchTemplateVersion.


## 1.8.863 - 2021-2-19
- Minor chagnes.


## 1.8.862 - 2021-2-19
- Add API OpenOnsService which can active ons service.


## 1.8.861 - 2021-2-19
- Supported International-26888 Sites Region Endpoints.


## 1.8.860 - 2021-2-19
- Add GetImageQuality API.
- Add more info as parameters to Images related APIs.


## 1.8.859 - 2021-2-19
- Update ModifyDcdnService.


## 1.8.858 - 2021-2-19
- Supported console.


## 1.8.857 - 2021-2-19
- Add update task api.


## 1.8.856 - 2021-2-19
- Generated 2018-01-11 for `rtc`.


## 1.8.855 - 2021-2-19
- Support Open Service API.


## 1.8.854 - 2021-2-19
- Update Function.


## 1.8.853 - 2021-2-19
- Generated 2018-01-11 for `rtc`.


## 1.8.852 - 2021-2-8
- Fix IoT Studio BatchBindDevicesIntoProject API return datatype.
- Fix IoT Studio BatchBindProductsIntoProject API return datatype.
- Fix IoT Studio BatchUnbindProjectDevices API return datatype.
- Fix IoT Studio BatchUnbindProjectProducts API return datatype.


## 1.8.851 - 2021-2-8
- Add username for RunCommand and InvokeCommand.


## 1.8.850 - 2021-2-8
- Update PedestrianDetectAttribute.


## 1.8.849 - 2021-2-7
- Add IoT Studio BatchBindDevicesIntoProject API.
- Add IoT Studio BatchBindProductsIntoProject API.
- Add IoT Studio BatchUnbindProjectDevices API.
- Add IoT Studio BatchUnbindProjectProducts API.


## 1.8.848 - 2021-2-7
- Support patch manager apis.


## 1.8.847 - 2021-2-7
- Support some MergeRequest API.


## 1.8.846 - 2021-2-5
- Generated 2020-05-18 for `dataworks-public`.


## 1.8.845 - 2021-2-5
- Test.


## 1.8.844 - 2021-2-5
- Update Open API.


## 1.8.843 - 2021-2-5
- Generated 2020-05-18 for `dataworks-public`.


## 1.8.842 - 2021-2-4
- Update DetectVehicleIllegalParking DetectVehicleICongestion.


## 1.8.841 - 2021-2-4
- Supported scheduler for outbound call.


## 1.8.840 - 2021-2-4
- Supported console.


## 1.8.839 - 2021-2-4
- Supported console.


## 1.8.838 - 2021-2-4
- Update TaggingImage.


## 1.8.837 - 2021-2-3
- After normalizing the API group name, regenerate the SDK.


## 1.8.836 - 2021-2-3
- Support connection drain.


## 1.8.835 - 2021-2-2
- Add.


## 1.8.834 - 2021-2-2
- CreateShardingDBInstance add ReadOnlyReplicas in ReplicaSet.


## 1.8.833 - 2021-2-1
- Support device credential for mqtt.


## 1.8.832 - 2021-2-1
- Release GenerateHumanSketchStyle MergeImageFace AddFaceImageTemplate QueryFaceImageTemplate DeleteFaceImageTemplate.


## 1.8.831 - 2021-2-1
- Add.


## 1.8.830 - 2021-2-1
- Release GenerateHumanSketchStyle MergeImageFace AddFaceImageTemplate QueryFaceImageTemplate DeleteFaceImageTemplate.


## 1.8.829 - 2021-2-1
- Release RecognizeFood.


## 1.8.828 - 2021-2-1
- Release UnderstandVideoContent.


## 1.8.827 - 2021-2-1
- Release SegmentGreenScreenVideo.


## 1.8.826 - 2021-2-1
- Release DetectVehicleIllegalParking DetectVehicleICongestion.


## 1.8.825 - 2021-1-29
- Update Monitor Api.


## 1.8.824 - 2021-1-28
- Update QueryMetric api support customFilters parameter.


## 1.8.823 - 2021-1-28
- Generated 2018-03-13 for `retailcloud`.


## 1.8.822 - 2021-1-27
- Add New BatchTranslate API.


## 1.8.821 - 2021-1-27
- DetectFaceAttributes Add Score.


## 1.8.820 - 2021-1-25
- Regenerate SDK to override list implement.


## 1.8.819 - 2021-1-25
- Update Open API.


## 1.8.818 - 2021-1-22
- Update Function.


## 1.8.817 - 2021-1-21
- Modify some field types.


## 1.8.816 - 2021-1-21
- Generated 2019-09-10 for `waf-openapi`.


## 1.8.815 - 2021-1-21
- Add.


## 1.8.814 - 2021-1-20
- Add metrics data API.


## 1.8.813 - 2021-1-19
- Update Function.


## 1.8.812 - 2021-1-18
- Generated 2019-03-27 for `dg`.


## 1.8.811 - 2021-1-18
- Add a set of API to support device distribution management, including CreateProductDistributeJob, CreateDeviceDistributeJob, QueryDeviceDistributeJob, ListDeviceDistributeJob, QueryDeviceDistributeDetail, DeleteDeviceDistributeJob, ListDistributedDevice, ListDistributedProduct etc.


## 1.8.810 - 2021-1-18
- Add a set of api to support device distribution management, including CreateProductDistributeJob, CreateDeviceDistributeJob, QueryDeviceDistributeJob, ListDeviceDistributeJob, QueryDeviceDistributeDetail, DeleteDeviceDistributeJob, ListDistributedDevice, ListDistributedProduct etc.


## 1.8.809 - 2021-1-18
- Add ListMergeRequests api.
- Add protected branch api, CreateRepositoryProtectedBranch and DeleteRepositoryProtectedBranch.
- Add repository deploy key api, EnableRepositoryDeployKey and CreateRepositoryDeployKey.
- Add UpdateRepository api.
- Update GetUserInfo, support use OrganizationId param to get user organization name.



## 1.8.808 - 2021-1-15
- Add result value spanId for GetTrace api.


## 1.8.807 - 2021-1-15
- Update Function.


## 1.8.806 - 2021-1-15
- Update Function.


## 1.8.805 - 2021-1-15
- Add API CreateSortScriptValidation.
- Add parameter resourceGroupId to API ListAppGroups.


## 1.8.804 - 2021-1-14
- Add function.


## 1.8.803 - 2021-1-14
- Add BillingDate support in QueryAccontBill.
- Add SplitAccountId, SplitAccountName, SplitBillingCycle, SplitProductDetail, SplitCommodityCode, ServicePeriodUnit support in QuerySplitItemBill.


## 1.8.802 - 2021-1-14
- Update Open API.


## 1.8.801 - 2021-1-14
- Update function.


## 1.8.800 - 2021-1-14
- Create Lindorm Open api.


## 1.8.799 - 2021-1-13
- Add SwitchInstanceHA API.
- ModifyInstanceSpec add SourceBiz param.


## 1.8.798 - 2021-1-13
- Generated 2016-11-01 for `live`.


## 1.8.797 - 2021-1-12
- Add support for IoT jobs, including job management and query APIs like CreateJob, UpdateJob, QueryJob, CancelJob, ListTask, QueryTask, QueryJobStatistics etc.


## 1.8.796 - 2021-1-12
- Update ExtractPedestrianFeatureAttr.


## 1.8.795 - 2021-1-11
- Add result value spanId for GetTrace api.


## 1.8.794 - 2021-1-11
- Add DescribeConnectionStatus and DescribeDTSIP, remove UpgradeTwoWay.


## 1.8.793 - 2021-1-8
- Add API related to resource quota of source monitoring.
- Add a new version of alert log API.


## 1.8.792 - 2021-1-7
- Add new API for configuration and report.


## 1.8.791 - 2021-1-7
- Add CreateAndStartBackupPlan.
- Add DescribeDLAService.
- Add CreateDLAService.
- Add CloseDLAService.


## 1.8.790 - 2021-1-7
- Generated 2019-09-10 for `waf-openapi`.


## 1.8.789 - 2021-1-7
- Generated 2020-11-26 for `Airec`.


## 1.8.788 - 2021-1-6
- Update DetectLungNodule.


## 1.8.787 - 2021-1-6
- Support input parameter ClientToken for CreateAutoProvisioningGroup.


## 1.8.786 - 2021-1-6
- Add API.


## 1.8.785 - 2021-1-5
- Remove legacy APIs.


## 1.8.784 - 2021-1-5
- Add DescribeBackupTasks API.


## 1.8.783 - 2021-1-4
- Generated 2014-05-26 for `Ecs`.


## 1.8.782 - 2021-1-4
- Supported API for GTM.


## 1.8.781 - 2020-12-31
- Add support for thing model function block features, including thing model APIs like CreateThingModel,UpdateThingModel,SetDeviceProperty,InvokeThingService etc.


## 1.8.780 - 2020-12-31
- Update Open API.


## 1.8.779 - 2020-12-30
- Add DescribeActiveOperationTask API.
- Add ModifyActiveOperationTask API.


## 1.8.778 - 2020-12-30
- Support directly specify image version deploy.


## 1.8.777 - 2020-12-29
- Supported SystemDiskPerformanceLevel, SystemDiskDeleteWithInstance, DataDiskPerformanceLevel, NetworkInterfaceSecurityGroupIds, SecurityGroupIds, PrivateIpAddress for CreateLaunchTemplate, CreateLaunchTemplateVersion, DescribeLaunchTemplateVersion.


## 1.8.776 - 2020-12-29
- ListNodesNoPaging return with instance type.


## 1.8.775 - 2020-12-28
- Generated 2020-05-18 for `dataworks-public`.


## 1.8.774 - 2020-12-28
- Release InterpolateVideoFrame ToneSdrVideo ConvertHdrVideo.


## 1.8.773 - 2020-12-28
- Release GenRealPersonVerificationToken GetRealPersonVerificationResult.


## 1.8.772 - 2020-12-28
- Add CommodityCode For QueryOrderAPI.


## 1.8.771 - 2020-12-28
- Ecs confirm.


## 1.8.770 - 2020-12-28
- Add some console API operations.


## 1.8.769 - 2020-12-25
- Generated 2016-04-08 for `Emr`.


## 1.8.768 - 2020-12-25
- Supported for weboffice edit.


## 1.8.767 - 2020-12-24
- Modify ConfigureBackupPlan.


## 1.8.766 - 2020-12-24
- Release CreateBodyDb ListBodyDbs DeleteBodyDb CreateBodyPerson GetBodyPerson ListBodyPerson DeleteBodyPerson AddBodyTrace SearchBodyTrace.


## 1.8.765 - 2020-12-23
- CDRS First edition.


## 1.8.764 - 2020-12-23
- Release DetectPedestrianIntrusion.


## 1.8.763 - 2020-12-23
- Create Lindorm Open api.


## 1.8.762 - 2020-12-23
- Create TSDB Open api.


## 1.8.761 - 2020-12-22
- Minor chagnes.


## 1.8.760 - 2020-12-22
- Update SegmentAnimal SegmentCommonImage.


## 1.8.759 - 2020-12-22
- Support SLB latest OpenAPIs.


## 1.8.758 - 2020-12-22
- Release InterpolateVideoFrame ToneSdrVideo ConvertHdrVideo.


## 1.8.757 - 2020-12-22
- Release InterpolateVideoFrame.


## 1.8.756 - 2020-12-22
- Release DetectRibFracture.


## 1.8.755 - 2020-12-22
- Minor chagnes.


## 1.8.754 - 2020-12-22
- Generated 2020-05-18 for `dataworks-public`.


## 1.8.753 - 2020-12-21
- Support Device of LaunchConfiguration_DataDisk.


## 1.8.752 - 2020-12-18
- Support ListRepositories, ListOrganizations, GetUserInfo, ListRepositoryWebhook, DeleteRepositoryWebhook api.
- AddWebhook api support SecretToken.


## 1.8.751 - 2020-12-18
- Release Full Managed Credentials for Aliyun RDS.


## 1.8.750 - 2020-12-18
- Add GetAgentDownloadUrl api.


## 1.8.749 - 2020-12-18
- Release EvaluateCertificateQuality.


## 1.8.748 - 2020-12-18
- Add ListScenario Api.
- Add ApplyScenario Api.
- Add DeleteScenario Api.


## 1.8.747 - 2020-12-18
- Generated 2020-05-18 for `dataworks-public`.


## 1.8.746 - 2020-12-17
- Add onex PHP sdk.
- Add onex python sdk.


## 1.8.745 - 2020-12-17
- Update SegmentFood.


## 1.8.744 - 2020-12-17
- Update DeleteScdnDomain.


## 1.8.743 - 2020-12-17
- Generated 2014-05-26 for `Ecs`.


## 1.8.742 - 2020-12-17
- Update DeleteDcdnDomain.


## 1.8.741 - 2020-12-17
- Update DeleteCdnDomain.


## 1.8.740 - 2020-12-17
- Generated 2020-01-11 for `servicemesh`.


## 1.8.739 - 2020-12-17
- Update api.


## 1.8.738 - 2020-12-16
- Generated 2019-09-10 for `waf-openapi`.


## 1.8.737 - 2020-12-16
- Support show pipeline history.
- Support show log details.


## 1.8.736 - 2020-12-16
- Add Face Attributes.


## 1.8.735 - 2020-12-15
- Update Open API.


## 1.8.734 - 2020-12-15
- CDRS First edition.


## 1.8.733 - 2020-12-15
- CDRS First edition.


## 1.8.732 - 2020-12-14
- Add update task api.


## 1.8.731 - 2020-12-14
- Add update task api.


## 1.8.730 - 2020-12-14
- CDRS First edition.


## 1.8.729 - 2020-12-14
- Add update task api.


## 1.8.728 - 2020-12-11
- Add Emon route-API support.


## 1.8.727 - 2020-12-9
- Update SegmentCommodity.


## 1.8.726 - 2020-12-8
- Update new version.


## 1.8.725 - 2020-12-8
- Generated 2018-12-12 for `vs`.


## 1.8.724 - 2020-12-8
- Support filtering keys and secrets.
- Support opening KMS service and describing service status by API.
- Add a prefix limitation for SecretName.


## 1.8.723 - 2020-12-8
- Update DescribeScdnDomainCertificateInfo.


## 1.8.722 - 2020-12-8
- Generated 2018-01-11 for `rtc`.


## 1.8.721 - 2020-12-8
- Support Open Service API.


## 1.8.720 - 2020-12-8
- Release DetectCardScreenshot RecognizePoiName.


## 1.8.719 - 2020-12-8
- Update data API.


## 1.8.718 - 2020-12-8
- Supported AndroidNotificationNotifyId for Push and MassPush.
- Supported iOSNotificationCollapseId for Push and MassPush.


## 1.8.717 - 2020-12-8
- Some new parameters are supported.


## 1.8.716 - 2020-12-8
- Update goodstech.


## 1.8.715 - 2020-12-7
- Add Listdashboard Api.


## 1.8.714 - 2020-12-7
- Remove DescribeSubscriptionObjectModifyStatus.


## 1.8.713 - 2020-12-4
- Supported broker demand.


## 1.8.712 - 2020-12-4
- Support manual scan interface.


## 1.8.711 - 2020-12-4
- ReportInstancesStatus OpenAPI supports IssueCategory parameter.


## 1.8.710 - 2020-12-3
- Update DescribeScdnDomainCertificateInfo.


## 1.8.709 - 2020-12-3
- Update ModifyDcdnService.


## 1.8.708 - 2020-12-3
- Update DescribeDomainMax95BpsData.


## 1.8.707 - 2020-12-3
- Update DescribeScdnDomainCertificateInfo.


## 1.8.706 - 2020-12-3
- Update DescribeScdnDomainCertificateInfo.


## 1.8.705 - 2020-12-3
- Update DescribeDomainMax95BpsData.


## 1.8.704 - 2020-12-3
- Update ModifyDcdnService.


## 1.8.703 - 2020-12-3
- Update DescribeDomainMax95BpsData.


## 1.8.702 - 2020-12-3
- Update DescribeScdnDomainCertificateInfo.


## 1.8.701 - 2020-12-3
- Generated 2020-06-29 for `alinlp`.


## 1.8.700 - 2020-12-3
- Generated 2020-05-18 for `dataworks-public`.


## 1.8.699 - 2020-12-3
- Add new api.


## 1.8.698 - 2020-12-3
- Supported all language sdk.
- Fixed some bugs for polardbx.


## 1.8.697 - 2020-12-3
- Support data retrieval of Ota module.
- Added script management API,including CreateThingScript,UpdateThingScript,GetThingScript.
- Update OTA API, including DeleteOTAModule,UpdateOTAModule,ListOTATaskByJob.
- Add OTA API, ListOTAModuleVersionsByDevice.


## 1.8.696 - 2020-12-3
- Update DetectLungNodule.


## 1.8.695 - 2020-12-2
- Update version.


## 1.8.694 - 2020-12-2
- Update version.


## 1.8.693 - 2020-12-2
- Support TemplateContent.


## 1.8.692 - 2020-12-2
- Supported OpenAPI for SDK.


## 1.8.691 - 2020-12-2
- Release ColorizeImage.


## 1.8.690 - 2020-12-2
- Release MergeVideoFace EnhanceVideoQuality.


## 1.8.689 - 2020-12-2
- Update SegmentHead.


## 1.8.688 - 2020-12-1
- Add task api.


## 1.8.687 - 2020-12-1
- Add task api.


## 1.8.686 - 2020-12-1
- Fixed bugs for CCC.


## 1.8.685 - 2020-12-1
- Supported Open Api.


## 1.8.684 - 2020-11-30
- Add OpenARMS api.


## 1.8.683 - 2020-11-30
- Add OpenARMS api.


## 1.8.682 - 2020-11-30
- Update SetVideoSeekConfig.


## 1.8.681 - 2020-11-30
- Update DescribeScdnDomainCertificateInfo.


## 1.8.680 - 2020-11-30
- Update SetVideoSeekConfig.


## 1.8.679 - 2020-11-30
- Add OpenARMS api.


## 1.8.678 - 2020-11-30
- Support autoPtovisioning whth DiskConfigs.


## 1.8.677 - 2020-11-30
- Update OpenDcdnService.


## 1.8.676 - 2020-11-30
- Update OpenDcdnService.


## 1.8.675 - 2020-11-30
- Supported all language sdk.
- Fixed some bugs for polardbx.


## 1.8.674 - 2020-11-29
- Support CPP and Go and PHP.


## 1.8.673 - 2020-11-27
- Supported all language sdk.
- Fixed some bugs for polardbx.


## 1.8.672 - 2020-11-27
- Supported all language sdk.
- Fixed some bugs for polardbx.


## 1.8.671 - 2020-11-27
- Generated 2018-01-11 for `rtc`.


## 1.8.670 - 2020-11-27
- Add Certificate Manager Api.


## 1.8.669 - 2020-11-27
- Support taskList.


## 1.8.668 - 2020-11-26
- ContrastFaceVerify Support Video.


## 1.8.667 - 2020-11-26
- Generated 2020-07-06, 2017-12-04 for `Actiontrail`.


## 1.8.666 - 2020-11-26
- Generated 2020-07-06 for `Actiontrail`.


## 1.8.665 - 2020-11-26
- Add tags api, include GetRepositoryTag, ListRepositoryTags, DeleteRepositoryTag.


## 1.8.664 - 2020-11-25
- Update DescribeScdnDomainCertificateInfo.


## 1.8.663 - 2020-11-25
- Update SetVideoSeekConfig.


## 1.8.662 - 2020-11-25
- Add extension field for image translation.


## 1.8.661 - 2020-11-24
- Create TSDB Open api.


## 1.8.660 - 2020-11-24
- Create TSDB Open api.


## 1.8.659 - 2020-11-24
- Create lindorm Open api.


## 1.8.658 - 2020-11-24
- Create lindorm Open api.


## 1.8.657 - 2020-11-24
- Update SearchFace.


## 1.8.656 - 2020-11-24
- Generated 2020-01-01 for `ddoscoo`.


## 1.8.655 - 2020-11-23
- Supported multiple Language.


## 1.8.654 - 2020-11-23
- Add LivenessDetect API.


## 1.8.653 - 2020-11-20
- Support ListExecutions with filter functionality.


## 1.8.652 - 2020-11-20
- Public beta version.
- Add Api Overseas.


## 1.8.651 - 2020-11-20
- Add a new interface for asynchronous image translation.


## 1.8.650 - 2020-11-20
- Update DetectFace.
- Update RecognizeFace.


## 1.8.649 - 2020-11-19
- Generated 2020-07-02 for `scsp`.


## 1.8.648 - 2020-11-19
- Update DescribeDedicatedClusterInstanceList return data.


## 1.8.647 - 2020-11-19
- Update data API.


## 1.8.646 - 2020-11-19
- Update data API.


## 1.8.645 - 2020-11-19
- Update GenerateHumanAnimeStyle.


## 1.8.644 - 2020-11-19
- Update SegmentBody.


## 1.8.643 - 2020-11-19
- Supported backend interface for ICBU.


## 1.8.642 - 2020-11-18
- Update DescribeDedicatedClusterInstanceList InstanceStatus type.


## 1.8.641 - 2020-11-18
- Generated 2018-01-11 for `rtc`.


## 1.8.640 - 2020-11-18
- Supported webpage scan.


## 1.8.639 - 2020-11-17
- Support event type in timeline of resource properties.
- Support resourceOwnerId in evaluation result.
- Support resource directory in config rule scene.
- Fix terraform related bugs.


## 1.8.638 - 2020-11-17
- Supported International-26888 Sites Region Endpoints.


## 1.8.637 - 2020-11-17
- Supported International-26888 Sites Region Endpoints.


## 1.8.636 - 2020-11-16
- Nat public IP supports the ISP attribute.


## 1.8.635 - 2020-11-16
- Add a new interface for asynchronous image translation.


## 1.8.634 - 2020-11-16
- Update videoenhan.


## 1.8.633 - 2020-11-16
- Update videoseg.


## 1.8.632 - 2020-11-16
- Update videorecog.


## 1.8.631 - 2020-11-16
- Update objectdet.


## 1.8.630 - 2020-11-16
- Update imgsearch.


## 1.8.629 - 2020-11-16
- Update imagerecog.


## 1.8.628 - 2020-11-16
- Update goodstech.


## 1.8.627 - 2020-11-13
- Update Imageaudit.


## 1.8.626 - 2020-11-13
- Add DeccribeEngineVersion DescribeTasks DescribeInstanceConfig and more APIs.


## 1.8.625 - 2020-11-13
- Update DetectLungNodule.


## 1.8.624 - 2020-11-13
- Fix GetBranchInfo and DeleteBranch API 404 error.
- Add ListRepositoryBranches and GetGroupDetail API.


## 1.8.623 - 2020-11-13
- Release ErasePerson.


## 1.8.622 - 2020-11-13
- Release DetectCardScreenshot RecognizePoiName.


## 1.8.621 - 2020-11-13
- Release GenerateHumanAnimeStyle CountCrowd.


## 1.8.620 - 2020-11-13
- Release SegmentHDSky SegmentHDCommonImage.


## 1.8.619 - 2020-11-12
- Support drm.


## 1.8.618 - 2020-11-12
- Fixed the display problem of the repo page.


## 1.8.617 - 2020-11-12
- Generated 2019-06-01 for `smc`.


## 1.8.616 - 2020-11-12
- Update new version.


## 1.8.615 - 2020-11-12
- Add API OpenOnsService which can active ons service.


## 1.8.614 - 2020-11-12
- Update Scdn API.


## 1.8.613 - 2020-11-12
- Support execute database script via open api.


## 1.8.612 - 2020-11-12
- Generated 2014-05-26 for `Ecs`.


## 1.8.611 - 2020-11-12
- Update cert API.


## 1.8.610 - 2020-11-12
- Update cert API.


## 1.8.609 - 2020-11-12
- Release latest features.


## 1.8.608 - 2020-11-12
- Update data API.


## 1.8.607 - 2020-11-12
- Add API OpenOnsService which can active ons service.


## 1.8.606 - 2020-11-12
- Add SaveTraceAppConfig api.


## 1.8.605 - 2020-11-12
- Update HasRenewChangeOrder type to Boolean in DescribeInstances.


## 1.8.604 - 2020-11-12
- Generated 2018-12-12 for `vs`.


## 1.8.603 - 2020-11-12
- Add Pipeline Member api.


## 1.8.602 - 2020-11-12
- Generated 2020-05-18 for `dataworks-public`.


## 1.8.601 - 2020-11-12
- Generated 2016-11-01 for `live`.


## 1.8.600 - 2020-11-12
- Sync cdn APIs.


## 1.8.599 - 2020-11-12
- Add Content.


## 1.8.598 - 2020-11-12
- Generated 2014-05-26 for `Ecs`.


## 1.8.597 - 2020-11-12
- Minor update for r-kvstore.


## 1.8.596 - 2020-11-12
- Generated 2018-06-01 for `dataworks-public`.


## 1.8.595 - 2020-11-12
- Generated 2016-11-01 for `live`.


## 1.8.594 - 2020-11-12
- GetTitleIntelligence add interface.


## 1.8.593 - 2020-11-12
- Fix bug.


## 1.8.592 - 2020-11-12
- Support Sampling for jaeger.


## 1.8.591 - 2020-11-12
- Add alarm notification language settings.


## 1.8.590 - 2020-11-12
- Generated 2018-06-01 for `dataworks-public`.


## 1.8.589 - 2020-11-12
- Generated 2018-07-13 for `Ft`.


## 1.8.588 - 2020-11-12
- Supported Open Api.


## 1.8.587 - 2020-11-12
- Support OpenAPI.


## 1.8.586 - 2020-11-12
- Generated 2018-07-13 for `Ft`.


## 1.8.585 - 2020-11-12
- Update DescribeTrails Response.


## 1.8.584 - 2020-11-5
- Support modifyInstnaceType.


## 1.8.583 - 2020-11-4
- Generated 2019-01-01 for `Cassandra`.


## 1.8.582 - 2020-11-4
- Support modifyInstnaceType.


## 1.8.581 - 2020-11-4
- Generated 2019-01-01 for `Cassandra`.


## 1.8.580 - 2020-11-4
- Support modifyInstnaceType.


## 1.8.579 - 2020-11-4
- Generated 2018-07-13 for `Ft`.


## 1.8.578 - 2020-11-4
- Generated 2018-07-13 for `Ft`.


## 1.8.577 - 2020-11-3
- Minor update for r-kvstore.


## 1.8.576 - 2020-11-3
- Update data API.


## 1.8.575 - 2020-11-2
- Support create serverless by day price type.


## 1.8.574 - 2020-11-1
- Supported All Console APIs.


## 1.8.573 - 2020-10-28
- Support GetParameters, GetParametersByPath, GetSecretParameters, GetSecretParametersByPath.


## 1.8.572 - 2020-10-28
- Supported all language sdk.
- Fixed some bugs for polardbx.


## 1.8.571 - 2020-10-28
- Supported golang sdk.
- Fixed some bugs for drds.


## 1.8.570 - 2020-10-27
- Generated 2020-05-18 for `dataworks-public`.


## 1.8.569 - 2020-10-27
- Support Sampling for jaeger.


## 1.8.568 - 2020-10-27
- Fix bug.


## 1.8.567 - 2020-10-27
- Add role authority management.


## 1.8.566 - 2020-10-26
- Generated 2014-05-26 for `Ecs`.


## 1.8.565 - 2020-10-26
- Generated 2016-11-01 for `live`.


## 1.8.564 - 2020-10-26
- Supported PaymentType for DescribeDnsProductInstance.


## 1.8.563 - 2020-10-26
- Update ScalingConfig parameters.


## 1.8.562 - 2020-10-23
- Change Configuration.


## 1.8.561 - 2020-10-23
- Support feedback.


## 1.8.560 - 2020-10-22
- Add Pipeline Member api.


## 1.8.559 - 2020-10-22
- Update data API.


## 1.8.558 - 2020-10-22
- Update data API.


## 1.8.557 - 2020-10-22
- Update cert API.


## 1.8.556 - 2020-10-21
- DescribeAvailableResource support AcceptLanguage.


## 1.8.555 - 2020-10-21
- Change Documentation configuration.


## 1.8.554 - 2020-10-21
- Cloud Storage Gateway openapi sdk is released.


## 1.8.553 - 2020-10-21
- Release latest features.


## 1.8.552 - 2020-10-21
- Change Documentation configuration.


## 1.8.551 - 2020-10-21
- Change Documentation configuration.


## 1.8.550 - 2020-10-21
- Add custom person interfaces, support registration, update etc.
- Optimize updateSmarttagTemplate interface, set some parameters as optional.


## 1.8.549 - 2020-10-21
- Update cert API.


## 1.8.548 - 2020-10-21
- Add PHP sdk.


## 1.8.547 - 2020-10-20
- Add LivenessFaceVerify API.


## 1.8.546 - 2020-10-20
- Update Configuration.


## 1.8.545 - 2020-10-20
- Add isv interface.


## 1.8.544 - 2020-10-20
- AddDeleteSortScriptFileApi.


## 1.8.543 - 2020-10-20
- Generated 2016-11-01 for `live`.


## 1.8.542 - 2020-10-19
- Add a new interface for asynchronous image translation.


## 1.8.541 - 2020-10-16
- Update OTA API, including ListOTAModuleByProduct, DeleteOTAModule.


## 1.8.540 - 2020-10-16
- Supported AndroidNotificationNotifyId for Push and MassPush.
- Supported iOSNotificationCollapseId for Push and MassPush.


## 1.8.539 - 2020-10-16
- Update cert API.


## 1.8.538 - 2020-10-16
- Update cert API.


## 1.8.537 - 2020-10-15
- Supported TAG APIs.


## 1.8.536 - 2020-10-15
- Add SaveTraceAppConfig api.


## 1.8.535 - 2020-10-15
- Generated 2018-06-01 for `dataworks-public`.


## 1.8.534 - 2020-10-15
- Generated 2018-06-01 for `dataworks-public`.


## 1.8.533 - 2020-10-14
- Generated 2014-05-26 for `Ecs`.


## 1.8.532 - 2020-10-13
- Add Content.


## 1.8.531 - 2020-10-13
- Add describe devices to return device status.


## 1.8.530 - 2020-10-13
- Update Scdn API.


## 1.8.529 - 2020-10-13
- Sync cdn APIs.


## 1.8.528 - 2020-10-13
- Update HasRenewChangeOrder type to Boolean in DescribeInstances.


## 1.8.527 - 2020-10-13
- Add describe devices to return device status.


## 1.8.526 - 2020-10-13
- Add spotDuration support.


## 1.8.525 - 2020-10-13
- GetTitleIntelligence add interface.


## 1.8.524 - 2020-10-13
- Generated 2018-01-11 for `rtc`.


## 1.8.523 - 2020-10-13
- Add DeviceCenter API, including QueryDeviceBySQL.
- Add OTA API, including ListOTAModuleByProduct, DeleteOTAModule.
- Update error message in response with InvokeThingService, SetDeviceProperty.
- Update OTA API, including CreateOTAModule, QueryDeviceVersionCountByPkFromOTA, QueryFirmwareByUid.
- Update thing model API, ImportThingModelTsl.


## 1.8.522 - 2020-10-13
- Generated 2016-11-01 for `live`.


## 1.8.521 - 2020-10-13
- Generated 2018-01-11 for `rtc`.


## 1.8.520 - 2020-10-13
- Generated 2018-12-12 for `vs`.


## 1.8.519 - 2020-10-12
- Support add webhook, get repository info via id or path.


## 1.8.518 - 2020-10-12
- Add list Organizations.


## 1.8.517 - 2020-10-12
- ChatApp third version.
- Add contack check api.


## 1.8.516 - 2020-10-9
- Update Scdn API.


## 1.8.515 - 2020-10-9
- Update DescribeCdnUserBillHistory API.


## 1.8.514 - 2020-10-9
- Update SetVideoSeekConfig API.


## 1.8.513 - 2020-9-29
- Add API OpenOnsService which can active ons service.


## 1.8.512 - 2020-9-29
- Add API OpenOnsService which can active ons service.


## 1.8.511 - 2020-9-29
- Add API OpenOnsService which can active ons service.


## 1.8.510 - 2020-9-29
- Add API OpenOnsService which can active ons service.


## 1.8.509 - 2020-9-28
- Update Scdn API.


## 1.8.508 - 2020-9-28
- Support filtering keys and secrets.
- Support opening KMS service and describing service status by API.
- Add a prefix limitation for SecretName.


## 1.8.507 - 2020-9-27
- Update domain API.


## 1.8.506 - 2020-9-27
- Support Open Service API.


## 1.8.505 - 2020-9-25
- Support DescribeSuspEventQuaraFiles API.
- Support RefreshContainerAsserts API.


## 1.8.504 - 2020-9-24
- ContrastFaceVerify API Add Model Parameter.


## 1.8.503 - 2020-9-24
- Add a new api named SubmitAIImageAuditJob to submit image ai detection job.
- Add a new api named CreateDNADB to create a DNA analysis library.
- Add a new api named ListDNADB to query the list of DNA analysis libraries.
- Add a new api named GetDNADB to query specified DNA analysis library.
- Add a new field DNADBId to the request parameter Config structure for SubmitAIJob api.


## 1.8.502 - 2020-9-23
- Add spotDuration support.


## 1.8.501 - 2020-9-23
- Some new parameters are supported.


## 1.8.500 - 2020-9-23
- Update domain API.


## 1.8.499 - 2020-9-23
- Update Scdn API.


## 1.8.498 - 2020-9-23
- Sync cdn APIs.


## 1.8.497 - 2020-9-22
- Generated 2019-03-08 for `ivision`.


## 1.8.496 - 2020-9-22
- Generated 2018-01-11 for `rtc`.


## 1.8.495 - 2020-9-22
- Generated 2018-12-12 for `vs`.


## 1.8.494 - 2020-9-22
- Generated 2016-11-01 for `live`.


## 1.8.493 - 2020-9-21
- Interface add column wordCount.


## 1.8.492 - 2020-9-21
- Add search object.


## 1.8.491 - 2020-9-20
- Add describe devices to return device status.


## 1.8.490 - 2020-9-18
- Add alarm notification language settings.


## 1.8.489 - 2020-9-17
- GetTitleIntelligence add interface.


## 1.8.488 - 2020-9-16
- Add Content.


## 1.8.487 - 2020-9-15
- Add describe devices to return device status.


## 1.8.486 - 2020-9-11
- Support execute database script via open api.


## 1.8.485 - 2020-9-10
- Add DisableBackupLog.
- Add EnableBackupLog.
- Add ReleaseBackupPlan.
- Add ModifyStorageStrategy.
- Modify ModifyBackupStrategy.
- Modify ModifyBackupSetDownloadRules.


## 1.8.484 - 2020-9-10
- Update HasRenewChangeOrder type to Boolean in DescribeInstances.


## 1.8.483 - 2020-9-9
- Add support cn-beijing service region.
- Add ai video cover interface,include submit and query.
- Add dynamic image query interface.
- Update SubmitMediaAuditJob support MediaAuditConfiguration to set ResourceType.


## 1.8.482 - 2020-9-8
- Supported backend interface for ICBU.


## 1.8.481 - 2020-9-7
- Support parameter APIs.


## 1.8.480 - 2020-9-4
- Sync cdn APIs.


## 1.8.479 - 2020-9-4
- Update Scdn API.


## 1.8.478 - 2020-9-4
- Update domain API.


## 1.8.477 - 2020-9-4
- Add GetImageQuality API.
- Add more info as parameters to Images related APIs.


## 1.8.476 - 2020-9-3
- Generated 2020-07-02 for `scsp`.


## 1.8.475 - 2020-9-3
- Generated 2020-07-02 for `scsp`.


## 1.8.474 - 2020-9-2
- Support sg for ml region.


## 1.8.473 - 2020-9-2
- ChatApp second version.
- Support new message fomat.


## 1.8.472 - 2020-9-2
- Meshstress.


## 1.8.471 - 2020-9-2
- Supported AndroidNotificationHuaweiChannel for Push and MassPush.


## 1.8.470 - 2020-9-2
- Supported AndroidNotificationHuaweiChannel for Push and MassPush.


## 1.8.469 - 2020-9-1
- Add Smart Verify API.


## 1.8.468 - 2020-9-1
- Generated 2020-07-02 for `scsp`.


## 1.8.467 - 2020-9-1
- Update Parameter.


## 1.8.466 - 2020-8-31
- Support PersonList.


## 1.8.465 - 2020-8-31
- Generated 2020-07-02 for `scsp`.


## 1.8.464 - 2020-8-31
- GetPersonList Support LastShotTime.


## 1.8.463 - 2020-8-31
- Supported AAA for BBB.
- Fixed bugs for CCC.


## 1.8.462 - 2020-8-31
- Supported AAA for BBB.
- Fixed bugs for CCC.


## 1.8.461 - 2020-8-31
- Supported AAA for BBB.
- Fixed bugs for CCC.


## 1.8.460 - 2020-8-31
- Add list person visit count API.


## 1.8.459 - 2020-8-28
- Add name.


## 1.8.458 - 2020-8-28
- Support multiple security group for one ehpc cluster.
- Update GWS apis.


## 1.8.457 - 2020-8-28
- Supported Add GetAlgorithmHistoryResult.


## 1.8.456 - 2020-8-27
- Update parameter.


## 1.8.455 - 2020-8-26
- Generated 2020-06-29 for `alinlp`.


## 1.8.454 - 2020-8-26
- Generated 2020-06-29 for `alinlp`.


## 1.8.453 - 2020-8-25
- Generated 2020-08-01 for `SmartHosting`.


## 1.8.452 - 2020-8-25
- Add list person visit count API.


## 1.8.451 - 2020-8-25
- Video Content analytics template introduced to this SDK.


## 1.8.450 - 2020-8-24
- Add project api.


## 1.8.449 - 2020-8-24
- Edit QueryMetricByPage api.


## 1.8.448 - 2020-8-21
- Add QueryMetricByPage api.
- Add GetAppApiByPage api.


## 1.8.447 - 2020-8-19
- Add DescribeLiveDomainCertificateInfo.


## 1.8.446 - 2020-8-19
- Generated 2020-08-01 for `SmartHosting`.


## 1.8.445 - 2020-8-18
- Add API RenewAppGroup.


## 1.8.444 - 2020-8-17
- Add GetTrace api.
- Edit Aler api.


## 1.8.443 - 2020-8-17
- Add pipeline status api.


## 1.8.442 - 2020-8-17
- Add Api HighlightGameVideo.


## 1.8.441 - 2020-8-17
- Add Api HighlightGameVideo.


## 1.8.440 - 2020-8-17
- Fix create project task.


## 1.8.439 - 2020-8-14
- Add POP-Model-related APIs.


## 1.8.438 - 2020-8-14
- Add sdk.


## 1.8.437 - 2020-8-14
- Supported Api DetectIPCPedestrian.


## 1.8.436 - 2020-8-14
- Supported CheckCloudResourceAuthorized API.


## 1.8.435 - 2020-8-14
- Add project Api.


## 1.8.434 - 2020-8-13
- Add PipCode and CommodityCode for QueryInstanceBill, QueryBillOverview, QueryBill, QuerySettleBill, QueryAccountBill, QuerySplitItemBill.
- Support BillOwnerId filter for QueryInstanceBill, QueryBillOverview, QueryBill, QuerySettleBill, QueryAccountBill, QuerySplitItemBill.


## 1.8.433 - 2020-8-13
- Release DetectKneeXRay DetectSpineMRI TranslateMed.


## 1.8.432 - 2020-8-13
- Release DetectKneeXRay DetectSpineMRI TranslateMed.


## 1.8.431 - 2020-8-13
- DescribePrice update amount parameters type, float to string.
- Add ResourceGroupId parameter to support resource group.


## 1.8.430 - 2020-8-13
- Release SegmentHalfBody.


## 1.8.429 - 2020-8-12
- Add pipeline status api.


## 1.8.428 - 2020-8-11
- Add ResourceGroup in resource snapshot.


## 1.8.427 - 2020-8-10
- Generated 2020-05-18 for `dataworks-public`.


## 1.8.426 - 2020-8-8
- Update SearchImage.


## 1.8.425 - 2020-8-7
- GetTitleDiagnose GetTitleGenerate GetImageTranslate supported.


## 1.8.424 - 2020-8-7
- Generated 2020-05-18 for `dataworks-public`.


## 1.8.423 - 2020-8-7
- GetTitleDiagnose GetTitleGenerate GetImageTranslate supported.


## 1.8.422 - 2020-8-7
- Add FindServiceList and GetService qps.


## 1.8.421 - 2020-8-6
- Update OTA API, including CreateOTAStaticUpgradeJob,CreateOTADynamicUpgradeJob and QueryOTAJob.


## 1.8.420 - 2020-8-6
- Add Smart Cloudauth API.


## 1.8.419 - 2020-8-4
- Update RecognizeTable.


## 1.8.418 - 2020-8-4
- Supported for deploy updateStrategy.


## 1.8.417 - 2020-8-3
- Fix Some API Response Define.


## 1.8.416 - 2020-8-3
- Add source picture url for Monitor.


## 1.8.415 - 2020-8-3
- Add Voice Synchronous Scan API.


## 1.8.414 - 2020-8-3
- Add ExtractPedestrianFeatureAttribute.


## 1.8.413 - 2020-7-31
- Edit GetStack api.


## 1.8.412 - 2020-7-30
- Update DetectCelebrity.


## 1.8.411 - 2020-7-30
- Add Smart Cloudauth API.


## 1.8.410 - 2020-7-30
- Add ABTest-related APIs.
- Add UserAnalyzer-related APIs.
- Add API ListSortExpressions.
- Fix property tag_label of response of ListInterventionDictionaryEntries.


## 1.8.409 - 2020-7-30
- Add ChangeVideoSize.


## 1.8.408 - 2020-7-30
- Add SegmentLogo SegmentScene.


## 1.8.407 - 2020-7-30
- Add DetectCelebrity.


## 1.8.406 - 2020-7-30
- Supported ChatApp for SDK.


## 1.8.405 - 2020-7-29
- Add CreateGetDBListFromAgentTask.
- Add GetDBListFromAgent.


## 1.8.404 - 2020-7-29
- API GetTaskStatus return task detail.


## 1.8.403 - 2020-7-29
- Generated 2020-05-18 for `dataworks-public`.


## 1.8.402 - 2020-7-28
- Generated 2018-10-12 for `Airec`.


## 1.8.401 - 2020-7-27
- Supported CheckCloudResourceAuthorized API.


## 1.8.400 - 2020-7-27
- Add HSF apis.


## 1.8.399 - 2020-7-24
- Edit ListTraceApp api with add tags.
- Edit SearchTraceAppByName api with add tags.
- Edut SearchTraceAppByPage api with add tags.


## 1.8.398 - 2020-7-24
- Support GetImageCroppingSuggestions API.
- Add Remarks to face group related APIs.
- Add Model parameter to blind watermark related APIs.


## 1.8.397 - 2020-7-24
- First build.


## 1.8.396 - 2020-7-23
- Supported CloudGame.


## 1.8.395 - 2020-7-23
- Supported AAA for BBB.
- Fixed bugs for CCC.


## 1.8.394 - 2020-7-23
- Update OTA API, including GenerateOTAUploadURL.


## 1.8.393 - 2020-7-23
- Add AddSecretBlacklist and DeleteSecretBlacklist.
- Suppoort ASR.


## 1.8.392 - 2020-7-23
- Add GenerateAndExportDataKey, ExportDataKey, ReEncrypt api.


## 1.8.391 - 2020-7-23
- Add GenerateAndExportDataKey, ExportDataKey, ReEncrypt api.


## 1.8.390 - 2020-7-22
- Generated 2015-01-01 for `R-kvstore`.
- Add SyncDtsStatus API.


## 1.8.389 - 2020-7-22
- Add source id for file uploader.


## 1.8.388 - 2020-7-22
- Add source id for file uploader.


## 1.8.387 - 2020-7-21
- Support Tag API.


## 1.8.386 - 2020-7-21
- Add algorithm type for ListPerson.


## 1.8.385 - 2020-7-20
- Add actions.


## 1.8.384 - 2020-7-17
- Add DescribeRegions.
- Modify DescribeFullBackupList.
- Modify DescribeBackupPlanList.
- Modify DescribeRestoreRangeInfo.


## 1.8.383 - 2020-7-17
- Add actions.


## 1.8.382 - 2020-7-16
- Add more OpenAPIs related to QueryProcessor, FirstRank, SecondRank, etc.


## 1.8.381 - 2020-7-15
- Edit ListTraceApp api.
- Edit SearchTraceAppByName api.
- Edut SearchTraceAppByPage api.


## 1.8.380 - 2020-7-15
- Support ModifyImageAttribute by LaunchPermission.
- Support ImageFamily.


## 1.8.379 - 2020-7-14
- Add Dynamic Registration APIs, including TransformClientId, DeleteClientIds, QueryClientIds, etc.
- Update OTA API, including CreateOTAStaticUpgradeJob and QueryOTAJob.
- Update Device Shadow API UpdateDeviceShadow.


## 1.8.378 - 2020-7-14
- Support Web SDK.


## 1.8.377 - 2020-7-14
- Fixed bug for service code.


## 1.8.376 - 2020-7-14
- Supported API for DescribeDohUserInfo.
- Supported API for DescribeDohAccountStatistics.
- Supported API for DescribeDohDomainStatistics.
- Supported API for DescribeDohDomainStatisticsSummary.
- Supported API for DescribeDohSubDomainStatistics.
- Supported API for DescribeDohSubDomainStatisticsSummary.


## 1.8.375 - 2020-7-10
- Sdk for 109.


## 1.8.374 - 2020-7-9
- Add accountType for customer service.


## 1.8.373 - 2020-7-9
- Add accountType for renderOrder.


## 1.8.372 - 2020-7-9
- Add Log Monitoring API.
- Add and modify non Alibaba Cloud host name API.


## 1.8.371 - 2020-7-8
- Remove GetFootwearPosition PullTakeShoesEvent PullTryOnShoesEvent.
- Add GetFootwearEvent.
- Please ignore the pd s pot.


## 1.8.370 - 2020-7-8
- Add video compose api.


## 1.8.369 - 2020-7-8
- Generated 2020-05-18 for `dataworks-public`.


## 1.8.368 - 2020-7-8
- Support hotword and hint.


## 1.8.367 - 2020-7-7
- Generated 2020-05-18 for `dataworks-public`.


## 1.8.366 - 2020-7-6
- Sdk for 101.


## 1.8.365 - 2020-7-3
- Supported UpdateUserBucketConfig.


## 1.8.364 - 2020-7-3
- Add ListResourceExecutionStatus API.
- ListExecutions API supports filtering by resource id.
- TriggerExecution API supports TimerTrigger.


## 1.8.363 - 2020-7-3
- Supports tag management APIs.


## 1.8.362 - 2020-7-2
- Build sdk.


## 1.8.361 - 2020-7-2
- Generated 2020-05-18 for `dataworks-public`.


## 1.8.360 - 2020-7-2
- SDK version 102.


## 1.8.359 - 2020-7-2
- Add GetFootwearPosition.
- Add PullTakeShoesEvent.
- Add PullTryOnShoesEvent.


## 1.8.358 - 2020-7-2
- Update endpoint.


## 1.8.357 - 2020-7-2
- CreateShardingDBInstance add ProtocolType, to support DynamoDB.
- DescribeDBInstances add return ResourceGroupId.


## 1.8.356 - 2020-7-1
- Added API for modifying non Alibaba Cloud host names.
- Fix the required type adjustment of several parameters.


## 1.8.355 - 2020-7-1
- SDK version 109.


## 1.8.354 - 2020-7-1
- SDK version 109.


## 1.8.353 - 2020-7-1
- ImageProcess First Version.


## 1.8.352 - 2020-7-1
- Sdk version 111.


## 1.8.351 - 2020-7-1
- SDK version 108.


## 1.8.350 - 2020-7-1
- Add API for GetMetadataAmount.


## 1.8.349 - 2020-7-1
- Support Saf For ExecuteExtendService.


## 1.8.348 - 2020-6-30
- Generated 2018-10-12 for `Airec`.


## 1.8.347 - 2020-6-29
- Supported Rotate.


## 1.8.346 - 2020-6-29
- Generated 2019-12-26 for `OutboundBot`.


## 1.8.345 - 2020-6-29
- Generated 2019-12-26 for `OutboundBot`.


## 1.8.344 - 2020-6-29
- Supported Rotate.


## 1.8.343 - 2020-6-29
- Supported gb28181 parent platform.


## 1.8.342 - 2020-6-29
- Add Model Parameter For InitFaceVerify API.


## 1.8.341 - 2020-6-26
- Supported Api EraseLogoInVideo.


## 1.8.340 - 2020-6-24
- Add DescribeBackupPlanBilling.
- Modify ConfigureBackupPlan.
- Modify CreateBackupPlan.
- Modify CreateRestoreTask.
- Modify DescribeRestoreTaskList.
- Modify DescribeBackupPlanList.
- Modify ModifyBackupSourceEndpoint.
- Modify ModifyBackupStrategy.


## 1.8.339 - 2020-6-23
- Generated 2015-12-15 for `CS`.


## 1.8.338 - 2020-6-23
- Support SubmitOperationAuditInfo for Support self-service submission of approval information.
- Support SubmitOperationCredentials for self-service submission of certificate information.
- Support CancelOperationAudit for cancel audit.
- Support GetOperationOssUploadPolic for get oss policy.
- Support QueryOperationAuditInfoLis for query audit list.
- Support QueryOperationAuditInfoDetail for query audit details.


## 1.8.337 - 2020-6-23
- Generated 2018-08-28 for `Tag`.


## 1.8.336 - 2020-6-22
- Add support cn-shenzhen region.


## 1.8.335 - 2020-6-22
- Add GetMultipleTrace api.
- Add SearchTracesByPage api.
- Add GetStack api.


## 1.8.334 - 2020-6-21
- Add persons API.


## 1.8.333 - 2020-6-18
- Supported TransformDBInstancePayType API.


## 1.8.332 - 2020-6-18
- Add GetAuthToken OpenAPI.


## 1.8.331 - 2020-6-17
- Supported sync create eni and assign private ip.


## 1.8.330 - 2020-6-16
- Support TargetImageId in ApplyNodes Interface.


## 1.8.329 - 2020-6-15
- Generated 2019-09-10 for `waf-openapi`.


## 1.8.328 - 2020-6-15
- Generated 2019-09-10 for `waf-openapi`.


## 1.8.327 - 2020-6-12
- Add CreateProduct API ProductSecret in Response.
- Add AMQP APIs, including CreateSubscribeRelation, CreateConsumerGroup, etc.
- Add LinkIoTEdge CreateSceneRule API.
- Add LinkIoTEdge DeleteSceneRule API.
- Add LinkIoTEdge UpdateSceneRule API.
- Add LinkIoTEdge GetSceneRule API.
- Add LinkIoTEdge QuerySceneRule API.
- Add LinkIoTEdge EnableSceneRule API.
- Add LinkIoTEdge DisableSceneRule API.
- Add LinkIoTEdge TriggerSceneRule API.
- Add LinkIoTEdge QuerySummarySceneRuleLog API.
- Add LinkIoTEdge QueryDetailSceneRuleLog API.
- Add LinkIoTEdge BindSceneRuleToEdgeInstance API.
- Add LinkIoTEdge UnbindSceneRuleFromEdgeInstance API.
- Add LinkIoTEdge QueryEdgeInstanceSceneRule API.


## 1.8.326 - 2020-6-12
- Update monitor related API.


## 1.8.325 - 2020-6-12
- Generated 2018-10-12 for `Airec`.


## 1.8.324 - 2020-6-11
- Support ServicePeriodUnit in QueryInstanceBill.


## 1.8.323 - 2020-6-11
- Supported AndroidNotificationVivoChannel for Push and MassPush.


## 1.8.322 - 2020-6-9
- Changed ServiceCode from `cr` to `acr`.


## 1.8.321 - 2020-6-8
- Support offline instance.


## 1.8.320 - 2020-6-4
- Generated 2019-12-26 for `OutboundBot`.


## 1.8.319 - 2020-6-4
- Update monitor related API.


## 1.8.318 - 2020-6-4
- Add SubmitMediaDNADeleteJob and ListMediaDNADeleteJob.
- Modify GetMediaAuditResult interface, support ad logo and live result.
- Modify GetMediaAuditResultTimeline interface, support ad logo and live result.


## 1.8.317 - 2020-6-4
- Update monitor related API.


## 1.8.316 - 2020-6-3
- DescribeCameraStatistics add filed.


## 1.8.315 - 2020-6-2
- Edit SearchTraces api.


## 1.8.314 - 2020-5-29
- Sdk version 111.


## 1.8.313 - 2020-5-29
- SDK version 108.


## 1.8.312 - 2020-5-28
- Supported AndroidNotificationXiaomiChannel for Push and MassPush.


## 1.8.311 - 2020-5-27
- Add app-group-management and quota-updating APIs.
- Add app management APIs.


## 1.8.310 - 2020-5-26
- Support DescribeAllEntity API.


## 1.8.309 - 2020-5-26
- Add smartcall service api.


## 1.8.308 - 2020-5-26
- Add smartcall service api.


## 1.8.307 - 2020-5-25
- Supported auvsp protocol changes.
- Supported start vod streams.
- Supported stop vod streams.
- Supported gb28281 alarm on device.


## 1.8.306 - 2020-5-23
- Generated 2015-01-01 for `R-kvstore`.
- Add ModifyResourceGroup API.
- DescribeInstanceAttribute return ResourceGroupId.


## 1.8.305 - 2020-5-22
- Add schedule crud api.


## 1.8.304 - 2020-5-21
- Support more error Codes.


## 1.8.303 - 2020-5-20
- Generated 2019-09-10 for `waf-openapi`.


## 1.8.302 - 2020-5-20
- Edit GetTrace and SearchTraces api.


## 1.8.301 - 2020-5-20
- Public beta version.
- Add Api Overseas.


## 1.8.300 - 2020-5-19
- Public beta version.
- Add Api Overseas.


## 1.8.299 - 2020-5-19
- Initial version.


## 1.8.298 - 2020-5-18
- Support sequence for listNodes.
- Support StrictSatisfiedTargetCapacity for applyNodes.



## 1.8.297 - 2020-5-18
- Fix DescribeIpcLiveAddress , add in params.


## 1.8.296 - 2020-5-17
- Open API publish.


## 1.8.295 - 2020-5-17
- Open API publish.


## 1.8.294 - 2020-5-17
- Open API publish.


## 1.8.293 - 2020-5-17
- Open API publish.


## 1.8.292 - 2020-5-15
- Add LinkIoTEdge CreateEdgeInstanceChannel, BatchDeleteEdgeInstanceChannel, UpdateEdgeInstanceChannel, BatchGetEdgeInstanceChannel, QueryEdgeInstanceChannel, QueryEdgeInstanceDeviceByDriver, BatchGetEdgeInstanceDeviceDriver, BatchSetEdgeInstanceDeviceChannel, BatchGetEdgeInstanceDeviceChannel API.
- Add LinkIoTEdge BindApplicationToEdgeInstance, UnbindApplicationFromEdgeInstance API.
- Add LinkIoTEdge ReplaceEdgeInstanceGateway, ReleaseEdgeDriverVersion, BatchGetDeviceBindStatus API.


## 1.8.291 - 2020-5-14
- Generated 2015-01-01 for `R-kvstore`.
- Fix DescribeCacheAnalysisReport return empty BigKeys because of wrong type defination.


## 1.8.290 - 2020-5-14
- Supported Grant Permission.
- Supported Revoke Permission.


## 1.8.289 - 2020-5-14
- Add demo edition auth.


## 1.8.288 - 2020-5-13
- Add ListDevicesImages DescribeIpcLiveAddress.
- Modify DescribeDevices.


## 1.8.287 - 2020-5-13
- Add ExportTas apis.


## 1.8.286 - 2020-5-13
- Generated 2019-09-10 for `waf-openapi`.


## 1.8.285 - 2020-5-13
- Supported AAA for BBB.
- Fixed bugs for CCC.


## 1.8.284 - 2020-5-13
- Add ExportTas apis.


## 1.8.283 - 2020-5-13
- About 2019-03-07 and 2018-09-16 Version.


## 1.8.282 - 2020-5-13
- Generated 2018-12-12, 2018-12-07 for `UniMkt`.


## 1.8.281 - 2020-5-12
- Sdk version 109.


## 1.8.280 - 2020-5-12
- BugFix for DescribeProjectMessages not returned the totalCount property.


## 1.8.279 - 2020-5-12
- BugFix for DescribeProjectMessages not returned the totalCount property.


## 1.8.278 - 2020-5-12
- Supported DRM.


## 1.8.277 - 2020-5-11
- Add new openapi ApplyNodes.


## 1.8.276 - 2020-5-11
- Bugfix for DescribeProjectAttachments Integer AttachmentType updated to String AttachmentType.


## 1.8.275 - 2020-5-11
- Support CreationCategory for CreateDBCluster.


## 1.8.274 - 2020-5-9
- DescribeCamerasStatistics add pvType.


## 1.8.273 - 2020-5-8
- Support sg for region.


## 1.8.272 - 2020-5-6
- Describe describeAvailableResource.


## 1.8.271 - 2020-4-30
- Add prometheus ListDashboards api.


## 1.8.270 - 2020-4-29
- Add prometheus ListDashboards api.


## 1.8.269 - 2020-4-29
- Add prometheus ListDashboards api.


## 1.8.268 - 2020-4-29
- Supported VideoProduce.


## 1.8.267 - 2020-4-29
- Supported VideoProduce.


## 1.8.266 - 2020-4-29
- Supported VideoProduce.


## 1.8.265 - 2020-4-29
- Add ListTemplateVersions API.
- GenerateExecutionPolicy supports TemplateVersion.
- CreateTemplate and UpdateTemplate support VersionName.



## 1.8.264 - 2020-4-29
- Support groupId operations.


## 1.8.263 - 2020-4-29
- Add ExportTas apis.


## 1.8.262 - 2020-4-29
- Support Document Translation.
- Support Lanuage Detection.


## 1.8.261 - 2020-4-29
- Sync cdn APIs.


## 1.8.260 - 2020-4-29
- Supported the Marketplace Delivery Center Project Api.
- About Project info.


## 1.8.259 - 2020-4-29
- Add Scdn APIS.
- Sync cdn APIS.


## 1.8.258 - 2020-4-28
- Generated 2018-12-12, 2018-12-07 for `UniMkt`.


## 1.8.257 - 2020-4-28
- Add CompareFaceVerify API.


## 1.8.256 - 2020-4-28
- Generated 2019-03-15 for `fnf`.


## 1.8.255 - 2020-4-27
- Support watermark.


## 1.8.254 - 2020-4-27
- Describe describeAvailableResource.


## 1.8.253 - 2020-4-27
- ContrastFaceVerify Return SubCode.


## 1.8.252 - 2020-4-26
- SDK versioin 101.


## 1.8.251 - 2020-4-26
- SDK version 108.


## 1.8.250 - 2020-4-26
- New api publish.


## 1.8.249 - 2020-4-25
- Generated 2015-01-01 for `R-kvstore`.
- Add RestoreTime for CreateInstance API.


## 1.8.248 - 2020-4-24
- Template update.


## 1.8.247 - 2020-4-24
- Publish apis for ledger instances.
- Publish apis for members.
- Publish apis for time anchors.
- Publish apis for endpoints.


## 1.8.246 - 2020-4-24
- Publish apis for ledger instances.
- Publish apis for members.
- Publish apis for time anchors.
- Publish apis for endpoints.


## 1.8.245 - 2020-4-23
- Fixed bugs for MassPush API.


## 1.8.244 - 2020-4-23
- Add location info.


## 1.8.243 - 2020-4-23
- Support groupId operations.


## 1.8.242 - 2020-4-22
- Generated 2019-01-01 for `Cassandra`.


## 1.8.241 - 2020-4-21
- VideoSearch deploy SDK.


## 1.8.240 - 2020-4-21
- Supported API for DescribeTags.
- Supported API for ListTagResources.
- Supported API for TagResources.
- Supported API for UntagResources.


## 1.8.239 - 2020-4-21
- Supported PackageDesign Apis.
- Supported Api ListPackageDesignModelTypes.
- Supported Api PreviewModelForPackageDesign.
- Supported Api RenderImageForPackageDesign.
- Supported Api GetRenderResult.


## 1.8.238 - 2020-4-21
- Add Statistics API.


## 1.8.237 - 2020-4-20
- Add TemplateId.


## 1.8.236 - 2020-4-20
- Fix FaceContrastPicture.


## 1.8.235 - 2020-4-19
- Multimedia poc modified.


## 1.8.234 - 2020-4-16
- Modify `SubmitSmarttagJob`.


## 1.8.233 - 2020-4-16
- Add ModifyWhiteIps.


## 1.8.232 - 2020-4-16
- Add BatchOptimization param in Stop and Start Instances.
- Add RemoveSymbols in GetInstanceConsoleOutput.
- Add ImageFamily in ModifyImageAttribute.
- Customize InstanceType on DedicatedHosts is supported.
- Add StorageCapacityUnit interfaces.
- Add param Tag in CreateAutoSnapshotPolicy.


## 1.8.231 - 2020-4-16
- Add features for open api.


## 1.8.230 - 2020-4-15
- Add pre train service api.


## 1.8.229 - 2020-4-15
- CreateNode API add return NodeId.


## 1.8.228 - 2020-4-15
- Vision-poc response modified.


## 1.8.227 - 2020-4-14
- Support ListSensitiveColumns, ListSensitiveColumnsDetail API.
- ListUsers API return user execute query count information.
- Fix ListWorkFlowTemplates to return create user information.
- UpdateUser API support to update user max execute query count.


## 1.8.226 - 2020-4-14
- Supported CreateSegmentBodyJob.


## 1.8.225 - 2020-4-13
- ImageProcess First Version.


## 1.8.224 - 2020-4-13
- Fix ListTaskExecutions SDK error.


## 1.8.223 - 2020-4-10
- Support GetOfficeEditURL.
- Support RefreshOfficeEditToken.


## 1.8.222 - 2020-4-10
- Support DescribeIpv4Location.


## 1.8.221 - 2020-4-9
- Add prometheus api AddGrafana and AddIntegration.


## 1.8.220 - 2020-4-9
- Generated 2018-03-13 for `retailcloud`.


## 1.8.219 - 2020-4-9
- Generated 2015-01-01 for `R-kvstore`.
- Add DescribePrice API.


## 1.8.218 - 2020-4-9
- Add DescribePrice API.


## 1.8.217 - 2020-4-9
- WAF OpenApi SDK Release.


## 1.8.216 - 2020-4-9
- Add DescribeDBClusterAvailableResources.


## 1.8.215 - 2020-4-8
- Add SubCode for DescribeFaceVerify.


## 1.8.214 - 2020-4-7
- Generated 2020-03-20 for `imgsearch`.


## 1.8.213 - 2020-4-7
- Generated 2019-12-30 for `facebody`.


## 1.8.212 - 2020-4-7
- Support namespaces.


## 1.8.211 - 2020-4-3
- Add trace api.
- Add prometheus api.


## 1.8.210 - 2020-4-3
- Support shelf type for planogram position.


## 1.8.209 - 2020-4-3
- Support sort script.


## 1.8.208 - 2020-4-3
- Support lmItemId.
- Support STSToken.


## 1.8.207 - 2020-4-2
- Add slo metrics.


## 1.8.206 - 2020-4-2
- Add API ContrastFaceVerify.


## 1.8.205 - 2020-4-2
- Support light up led lamp for ESL.


## 1.8.204 - 2020-4-2
- Support for WaitTimeSeconds for DescribeExecution.


## 1.8.203 - 2020-4-1
- DescribeVerifyToken API Add Parameters-UserIp and UserPhoneNumber and UserRegistTime.


## 1.8.202 - 2020-4-1
- Parameters is string in StartExecution Response.


## 1.8.201 - 2020-4-1
- Counters and Parameters are changed to Map in ListExections and StartExecution.


## 1.8.200 - 2020-3-25
- Supported describe appkey for rtc application.
- Supported set property for rtc channel.
- Fixed the missing error code description for rtc removeterminal.


## 1.8.199 - 2020-3-25
- SDK versioin 101.


## 1.8.198 - 2020-3-25
- Supported describe appkey for rtc application.
- Supported set property for rtc channel.
- Fixed the missing error code description for rtc removeterminal.


## 1.8.197 - 2020-3-24
- DescribeInstanceTypes Supports EniIpv6AddressQuantity.


## 1.8.196 - 2020-3-24
- DescribeInstanceTypes Supports EniIpv6AddressQuantity.


## 1.8.195 - 2020-3-23
- SDK version 107.


## 1.8.194 - 2020-3-23
- SDK version 100.


## 1.8.193 - 2020-3-23
- SDK version 107.


## 1.8.192 - 2020-3-23
- SDK version 100.


## 1.8.191 - 2020-3-23
- SDK versioin 100.


## 1.8.190 - 2020-3-23
- SDK version 107.


## 1.8.189 - 2020-3-23
- SDK version 100.


## 1.8.188 - 2020-3-23
- SDK version 107.


## 1.8.187 - 2020-3-23
- Generated 2015-01-01 for `R-kvstore`.
- Add DescribeSecurityGroupConfiguration API.
- Add ModifySecurityGroupConfiguration API.


## 1.8.186 - 2020-3-23
- Sdk version 107.


## 1.8.185 - 2020-3-23
- SDK version 107.


## 1.8.184 - 2020-3-23
- SDK version 101.


## 1.8.183 - 2020-3-23
- Generated 2014-08-15 for `Rds`.


## 1.8.182 - 2020-3-19
- UploadImageToLib add Urls.


## 1.8.181 - 2020-3-19
- UploadImageToLib add Urls.


## 1.8.180 - 2020-3-18
- Supported API for DescribeInstanceDomains.


## 1.8.179 - 2020-3-18
- ACM POP SDK.


## 1.8.178 - 2020-3-18
- Generated 2018-08-28 for `Tag`.


## 1.8.177 - 2020-3-16
- DescribeFaceVerify API Return DeviceToken Field.


## 1.8.176 - 2020-3-16
- Add exporter related API.


## 1.8.175 - 2020-3-16
- Release.
- Add virtual host modification API.
- Add exchange modification API.
- Add queue modification API.
- Add binding modification API.


## 1.8.174 - 2020-3-16
- Add ImageFamily paramters and apis, in Instance creation apis and Image query apis, and add DescribeImageFromFamily.
- Add Instance batch operation apis RebootInstances StartInstances and StopInstances.
- Add EncryptAlgorithm paramter in instance creati


## 1.8.173 - 2020-3-13
- ACM POP SDK.


## 1.8.172 - 2020-3-12
- Add new Api SegmentBody.


## 1.8.171 - 2020-3-12
- Add new Api SegmentBody.


## 1.8.170 - 2020-3-11
- Update structure for DescribeAvailableResource without compatible.


## 1.8.169 - 2020-3-11
- Support certificate translate.


## 1.8.168 - 2020-3-11
- Support certificate translate.


## 1.8.167 - 2020-3-11
- Support certificate translate.


## 1.8.166 - 2020-3-11
- Support certificate translate.


## 1.8.165 - 2020-3-11
- Add new Api SegmentBody.


## 1.8.164 - 2020-3-11
- Support certificate translate.


## 1.8.163 - 2020-3-11
- Public beta version.
- Add Api GetMeetingMebers.


## 1.8.162 - 2020-3-11
- Supported ondemand record.
- Supported ivision template binding.


## 1.8.161 - 2020-3-10
- ACM POP SDK.


## 1.8.160 - 2020-3-10
- Add ExportTas apis.


## 1.8.159 - 2020-3-9
- Add BackupStorageType.


## 1.8.158 - 2020-3-9
- Add EmotionConfidence to GetImage.


## 1.8.157 - 2020-3-9
- NlpAutoml update contract api.


## 1.8.156 - 2020-3-6
- Add LinkIoTEdge CreateEdgeOssPreSignedAddress API.


## 1.8.155 - 2020-3-6
- Add LinkIoTEdge CreateEdgeOssPreSignedAddress API.


## 1.8.154 - 2020-3-6
- Supported Saf for cn.


## 1.8.153 - 2020-3-6
- Supported Saf for cn.


## 1.8.152 - 2020-3-6
- Supported Saf for oversea.


## 1.8.151 - 2020-3-6
- Supported Saf for oversea.


## 1.8.150 - 2020-3-6
- Add request parameter groupType for OnsGroupCreate.
- Add request parameter groupType for OnsGroupList.


## 1.8.149 - 2020-3-5
- Add param InstanceType for ListImages and ListCustomImages.
- Fix error codes for SubmitJobs and so on.


## 1.8.148 - 2020-3-5
- Add CreateEdgeDriver, DeleteEdgeDriver, BatchGetEdgeDriver, QueryEdgeDriver, CreateEdgeDriverVersion, DeleteEdgeDriverVersion, UpdateEdgeDriverVersion, GetEdgeDriverVersion, QueryEdgeDriverVersion API.
- Add ResetThing API, support reset thing topo and 


## 1.8.147 - 2020-3-5
- Add CreateEdgeDriver, DeleteEdgeDriver, BatchGetEdgeDriver, QueryEdgeDriver, CreateEdgeDriverVersion, DeleteEdgeDriverVersion, UpdateEdgeDriverVersion, GetEdgeDriverVersion, QueryEdgeDriverVersion API.
- Add ResetThing API, support reset thing topo and 


## 1.8.146 - 2020-3-5
- Update send message noPresistFlag param.


## 1.8.145 - 2020-3-4
- Update.


## 1.8.144 - 2020-3-4
- Public beta version.
- Add Api GetMeetingMebers.


## 1.8.143 - 2020-3-3
- Supported secretmanager stable version for kms.


## 1.8.142 - 2020-3-3
- Supported secretmanager stable version for kms.


## 1.8.141 - 2020-3-3
- GeoIP Databases SDK initial release.


## 1.8.140 - 2020-3-3
- Supported secretmanager for kms.


## 1.8.139 - 2020-2-28
- Add QueryPhoneNoAByTrackNo  and AddAxnTrackNo.


## 1.8.138 - 2020-2-28
- Supported for eip operation idempotence.


## 1.8.137 - 2020-2-28
- Supported for eip operation idempotence.


## 1.8.136 - 2020-2-27
- Add translate api.


## 1.8.135 - 2020-2-27
- Sixth version.


## 1.8.134 - 2020-2-27
- Sixth sdk version.


## 1.8.133 - 2020-2-27
- Sixth sdk version.


## 1.8.132 - 2020-2-27
- Sixth version.


## 1.8.131 - 2020-2-27
- Sixth version.


## 1.8.130 - 2020-2-27
- Sixth version.


## 1.8.129 - 2020-2-27
- Release Ft SDK.


## 1.8.128 - 2020-2-26
- Fix tag upper case parameters.
- Fix patch.


## 1.8.127 - 2020-2-26
- Upgrade sdk.


## 1.8.126 - 2020-2-26
- Generated 2015-01-01 for `R-kvstore`.
- Add VpcCloudInstanceId for DescribeInstanceAttribute.


## 1.8.125 - 2020-2-26
- Add InitFaceVerify and DescribeFaceVerify API.


## 1.8.124 - 2020-2-25
- Fix ListWorkFlowTemplates, ListWorkFlowNodes API Go SDK build error.


## 1.8.123 - 2020-2-25
- JAVA fifth sdk version.
- PYTHON second sdk version.


## 1.8.122 - 2020-2-25
- Update backupsetDownloadset fun.


## 1.8.121 - 2020-2-24
- ImmediateDelete.
- DescribeInstance CreateTimeUTC ExpireTimeUTC.
- DescribeInstances CreateTimeUTC ExpireTimeUTC.
- DescribeIpWhitelist Groups GroupName.
- CreateCluster.


## 1.8.120 - 2020-2-24
- Release Ft SDK.


## 1.8.119 - 2020-2-24
- Release Ft SDK.


## 1.8.118 - 2020-2-24
- Add tag of list instance parameters.


## 1.8.117 - 2020-2-24
- Add tag of list instance parameters.


## 1.8.116 - 2020-2-24
- Generated 2018-06-12 for `VoiceNavigator`.


## 1.8.115 - 2020-2-24
- Generated 2019-12-26 for `OutboundBot`.


## 1.8.114 - 2020-2-21
- Support TAG API.


## 1.8.113 - 2020-2-20
- Support set accountType.


## 1.8.112 - 2020-2-20
- Generated 2019-09-28 for `reid`.


## 1.8.111 - 2020-2-20
- Fix the problem that the three APIs, disablesitemonitors, enablesitemonitors and modifysitemonitor, return result fields are not fully define.


## 1.8.110 - 2020-2-20
- Add new interface ListMaskDetectionResults to support to pull mask detection results.


## 1.8.109 - 2020-2-20
- Add new api ListUsers.
- Supported userRole setting for api, CreateApp, UpdateApp, DescribeAppDetail.


## 1.8.108 - 2020-2-18
- Supported customized params in device apis.


## 1.8.107 - 2020-2-15
- Supported ListWorkFlowTemplates, ListWorkFlowNodes API.


## 1.8.106 - 2020-2-15
- Add DescribeDBClusterSSL.
- Add ModifyDBClusterSSL.


## 1.8.105 - 2020-2-14
- Update default endpoints.


## 1.8.104 - 2020-2-14
- Add callback params to StartExecution API.
- Support ListExecutions with Status API.


## 1.8.103 - 2020-2-14
- Update default endpoints.


## 1.8.102 - 2020-2-14
- Fix the field definition for the site monitoring option OptionJson.


## 1.8.101 - 2020-2-13
- ModifyNetworkInterfaceAttribute support modify QueueNumber.
- DescribeNetworkInterfaces support QueueNumber.


## 1.8.100 - 2020-2-13
- Add Staging apis.


## 1.8.99 - 2020-2-13
- ModifyNetworkInterfaceAttribute support modify QueueNumber.
- DescribeNetworkInterfaces support QueueNumber.


## 1.8.98 - 2020-2-13
- ModifyNetworkInterfaceAttribute support modify QueueNumber.
- DescribeNetworkInterfaces support QueueNumber.


## 1.8.97 - 2020-2-13
- ModifyNetworkInterfaceAttribute support modify QueueNumber.
- DescribeNetworkInterfaces support QueueNumber.


## 1.8.96 - 2020-2-13
- ModifyNetworkInterfaceAttribute support modify QueueNumber.
- DescribeNetworkInterfaces support QueueNumber.


## 1.8.95 - 2020-2-13
- ModifyNetworkInterfaceAttribute support modify QueueNumber.
- DescribeNetworkInterfaces support QueueNumber.


## 1.8.94 - 2020-2-13
- Add Staging apis.


## 1.8.93 - 2020-2-13
- Add Staging apis.


## 1.8.92 - 2020-2-13
- Add Staging apis.


## 1.8.91 - 2020-2-13
- Add Staging apis.


## 1.8.90 - 2020-2-13
- Add Staging apis.


## 1.8.89 - 2020-2-13
- Fix GetDataCorrectOrderDetail API return empty database information.


## 1.8.88 - 2020-2-13
- Init MQTT SDK .


## 1.8.87 - 2020-2-12
- Support to create, describe and expire demo access token.


## 1.8.86 - 2020-2-12
- Generated 2018-12-12, 2018-12-07 for `UniMkt`.


## 1.8.85 - 2020-2-12
- Update Full showStorageTyp.


## 1.8.84 - 2020-2-12
- Add Full showStorageTyp.


## 1.8.83 - 2020-2-12
- Support mau.


## 1.8.82 - 2020-2-11
- Return `VpcInstanceId ` for DescribeDBClusterEndpoints.
- Add DescribeBackupLogs.


## 1.8.81 - 2020-2-11
- Support ModifyInstanceAttributes for SecurityGroupIds.


## 1.8.80 - 2020-2-11
- Add Thing Model APIs, including CreateThingModel, UpdateThingModel, etc.
- Add OTA API ListOTATaskByJob.
- Update OTA API ListOTAJobByFirmware.
- Update Thing Core Model API InvokeThingsService.


## 1.8.79 - 2020-2-10
- Update default endpoints.


## 1.8.78 - 2020-2-10
- Supported address for GetMediaMeta.


## 1.8.77 - 2020-2-9
- Sdk version 104.


## 1.8.76 - 2020-2-8
- Add DetectMask Api.


## 1.8.75 - 2020-2-7
- Fix bug for DescribeZoneInfo, delete return result of reionId.
- Fix bug for DeleteZone, parameter zoneId change to compulsory.
- Fix bug for SetProxyPattern, parameter zoneId change to compulsory.


## 1.8.74 - 2020-2-5
- Supported GetOfficePreviewURL.
- Supported RefreshOfficePreviewToken.


## 1.8.73 - 2020-2-4
- Update default endpoints.


## 1.8.72 - 2020-1-20
- Add Thing Model APIs, including QueryThingModel, PublishThingModel, etc.


## 1.8.71 - 2020-1-20
- Onezeroone sdk version.


## 1.8.70 - 2020-1-20
- Onezeroone sdk version.


## 1.8.69 - 2020-1-20
- Release on full language support.


## 1.8.68 - 2020-1-17
- Supported Add the history event signature function.


## 1.8.67 - 2020-1-17
- Supported AAA for BBB.
- Fixed bugs for CCC.


## 1.8.66 - 2020-1-16
- Fix the result value of DeleteSiteMonitors.


## 1.8.65 - 2020-1-16
- Generated 2015-01-01 for `R-kvstore`.
- Update DescribeAvailableResource.


## 1.8.64 - 2020-1-16
- Add fields faceComparisonScore for DescribeVerifyResult and VerifyMaterials interface.


## 1.8.63 - 2020-1-15
- Fix SignatureDoesNotMatch issue of GetOrderBaseInfo API.


## 1.8.62 - 2020-1-15
- Fix parameter issues for label synchronization group.


## 1.8.61 - 2020-1-15
- Fix parameter issues for label synchronization group.


## 1.8.60 - 2020-1-14
- Publish SDK For Java python, PHP.
- Base on 2018-07-13.


## 1.8.59 - 2020-1-14
- Visionai online.


## 1.8.58 - 2020-1-14
- Visionai online.


## 1.8.57 - 2020-1-13
- New API DescribeAvailableResource.


## 1.8.56 - 2020-1-13
- Supported ondemand start transfer stream.


## 1.8.55 - 2020-1-13
- Add Dynamic Tag API.


## 1.8.54 - 2020-1-13
- Supported InstanceMaintenanceAtrributes for maintening the attributes of instances.
- Deleted MaintenanceProperty.
- Supported RedeployDedicatedHost for redeploying dedicated host in under-assessment status.


## 1.8.53 - 2020-1-10
- Return `ConsistentTime` for DescribeBackups.
- Return `DBNodeIds` for CreateDBNodes.


## 1.8.52 - 2020-1-9
- Supported ondemand transfer stream.
- Supported device position.


## 1.8.51 - 2020-1-9
- Support DAILY report in QueryInstanBill.


## 1.8.50 - 2020-1-8
- Add TagLevel to ListSetTags.


## 1.8.49 - 2020-1-7
- Modify desribeInstance return dict item.


## 1.8.48 - 2020-1-7
- Supported AAA for BBB.
- Fixed bugs for CCC.


## 1.8.47 - 2020-1-3
- Add GWS serie apis.
- Add QueryReport.
- Fix ListJobs.
- Etc.


## 1.8.46 - 2020-1-3
- New API DescribeAvailableResource.


## 1.8.45 - 2020-1-3
- ImmediateDelete.
- DescribeInstance CreateTimeUTC ExpireTimeUTC.
- DescribeInstances CreateTimeUTC ExpireTimeUTC.
- DescribeIpWhitelist Groups GroupName.


## 1.8.44 - 2019-12-31
- Add OTA APIs, including GenerateOTAUploadURL, CreateOTAFirmware, etc.
- Add UpdateProductFilterConfig interface, support setting product filter config.


## 1.8.43 - 2019-12-31
- Supported SQL SERVER support delete backup set according to time range.


## 1.8.42 - 2019-12-31
- Add CreateMarketCellSpace method.


## 1.8.41 - 2019-12-31
- Support asymmetric keys.


## 1.8.40 - 2019-12-31
- Add request parameter groupType for OnsGroupCreate.


## 1.8.39 - 2019-12-31
- Supported image for process.


## 1.8.38 - 2019-12-31
- Generated 2019-09-28 for `reid`.


## 1.8.37 - 2019-12-31
- Support DeleteDBCluster.
- Support ModifyDBCluster.
- Support DescribeAvailableResource.


## 1.8.36 - 2019-12-30
- Disable Base64 string data support.


## 1.8.35 - 2019-12-30
- Disable Base64 string data support.


## 1.8.34 - 2019-12-30
- Disable Base64 string data support.


## 1.8.33 - 2019-12-30
- Disable Base64 Image string data support.


## 1.8.32 - 2019-12-30
- Visionai online.


## 1.8.31 - 2019-12-29
- Add new api.


## 1.8.30 - 2019-12-29
- Add new api.


## 1.8.29 - 2019-12-29
- Add new api.


## 1.8.28 - 2019-12-29
- Add new api.


## 1.8.27 - 2019-12-29
- Add new api.


## 1.8.26 - 2019-12-27
- Support set accountType.


## 1.8.25 - 2019-12-27
- Support set accountType.


## 1.8.24 - 2019-12-26
- NlpAutoml add async api.


## 1.8.23 - 2019-12-26
- Modify CreateRestoreTask.


## 1.8.22 - 2019-12-25
- Support merchant sync item info.


## 1.8.21 - 2019-12-25
- Support merchant sync item info.


## 1.8.20 - 2019-12-25
- Support merchant sync item info.


## 1.8.19 - 2019-12-24
- Generated 2018-12-12, 2018-12-07 for `UniMkt`.


## 1.8.18 - 2019-12-20
- Support miniapp.
- Support account type.


## 1.8.17 - 2019-12-20
- Supported Authorization API, includes GrantUserPermission, RevokeUserPermission, ListDatabaseUserPermssions, ListUserPermissions.
- Supported Database or Table Metadata synchronization API, includes SyncInstanceMeta, SyncDatabaseMeta.
- Supported Database or Table Owner set up API.
- Supported GetUser API to get information of one DMS user.
- Supported set Dingding mobile number with RegisterUser API.


## 1.8.16 - 2019-12-20
- Supported Search stream predict task list use modelId for IVISION.


## 1.8.15 - 2019-12-20
- Add result value for OnsConsumerStatus.


## 1.8.14 - 2019-12-19
- Add DescribeSlowLogRecords,DescribeSlowLogs for SlowLog.


## 1.8.13 - 2019-12-19
- Generated 2019-09-28 for `reid`.


## 1.8.12 - 2019-12-19
- DescribeNetworkInterfaces Supports NextToken.


## 1.8.11 - 2019-12-18
- Third sdk version.


## 1.8.10 - 2019-12-18
- Second sdk version.


## 1.8.9 - 2019-12-18
- Second sdk version.


## 1.8.8 - 2019-12-18
- Second sdk version.


## 1.8.7 - 2019-12-18
- First sdk version.


## 1.8.6 - 2019-12-18
- First sdk version.


## 1.8.5 - 2019-12-18
- First sdk version.


## 1.8.4 - 2019-12-18
- First sdk version.


## 1.8.3 - 2019-12-18
- First sdk version.


## 1.8.2 - 2019-12-18
- First sdk version.


## 1.8.1 - 2019-12-18
- First sdk version.


## 1.7.197 - 2019-12-16
- DescribeTransferDomains add query param, TargetUserId, FromUserId, DomainName.


## 1.7.196 - 2019-12-14
- Generated 2015-01-01 for `R-kvstore`.


## 1.7.195 - 2019-12-14
- Update DescribeAvailableResource.


## 1.7.194 - 2019-12-14
- Update DescribeAvailableResource.


## 1.7.193 - 2019-12-11
- Add api `BatchAddThingTopo`, support add multiple sub-devices for a gateway.
- Add api `QueryDeviceByStatus`, support query device list by device status.
- Support grouping products and rules.
- Support using RAM to authorize resouces, including products, devices, rules, groups and instances, to RAM users.


## 1.7.192 - 2019-12-11
- Add tag API.
- Add group process monitoring API.
- Add API for batch setting alarm rules.


## 1.7.191 - 2019-12-5
- Add api QueryMetric.


## 1.7.190 - 2019-12-4
- Delete invalid Api.


## 1.7.189 - 2019-12-4
- Generated 2019-01-01 for `HBase`.


## 1.7.188 - 2019-12-4
- Supported ModifyDBInstanceSpec for Direction param.


## 1.7.187 - 2019-12-4
- Supported ModifyDBInstanceSpec for Direction param.


## 1.7.186 - 2019-12-3
- Supported face library operation.
- Supported task template and words.


## 1.7.185 - 2019-12-2
- Remove QuerySmsProdStatus OpenAPI.


## 1.7.184 - 2019-11-29
- Add RenewInstance.
- Add RenewLogstash.
- Add UpdateInstanceChargeType.
- Add UpdateLogstashChargeType.


## 1.7.183 - 2019-11-28
- Supported API GetTxtRecordForVerify.
- Supported API RetrieveDomain.


## 1.7.182 - 2019-11-28
- Supported dash for compress.


## 1.7.181 - 2019-11-28
- Eni Supports Multi-SecurityGroup.


## 1.7.180 - 2019-11-28
- Support Order Basis API, include CreateOrder, CloseOrder, ListOrders, GetOrderBaseInfo.
- Support Order Approval API, include SubmitOrderApproval, ApproveOrder, GetApprovalDetail.
- Support DataCorrect Order Operation API, include ExecuteDataCorrect, GetDataCorrectOrderDetail, GetDataCorrectBackupFiles.
- Support DDL Order Operation API, include CreatePublishGroupTask.
- Support DataExport Operation API, include ExecuteDataExport, GetDataExportOrderDetail, GetDataExportDownloadURL.


## 1.7.179 - 2019-11-27
- CreateReplicationJob API supports the specified instance type.


## 1.7.178 - 2019-11-26
- Generated 2018-12-12, 2018-12-07 for `UniMkt`.


## 1.7.177 - 2019-11-26
- Modify QueryBill, add SubOrderId in item of response.


## 1.7.176 - 2019-11-26
- Supported API BindInstanceDomains.
- Supported API UnbindInstanceDomains.
- Supported API AddCustomLine.
- Supported API UpdateCustomLine.
- Supported API DeleteCustomLines.
- Supported API DescribeCustomLine.
- Supported API DescribeCustomLines.


## 1.7.175 - 2019-11-25
- One or more people can be subscribed when creating tasks.


## 1.7.174 - 2019-11-22
- Supported ondemand snapshot.


## 1.7.173 - 2019-11-22
- First version.


## 1.7.172 - 2019-11-22
- Update Api Request And Response structure.


## 1.7.171 - 2019-11-21
- Add ConvertPayType.
- Add ConvertLogstashPayType.


## 1.7.170 - 2019-11-20
- Update Nlp Automl SDK.


## 1.7.169 - 2019-11-20
- Add new OpenAPI.


## 1.7.168 - 2019-11-20
- Add new pop api.


## 1.7.167 - 2019-11-20
- Supported ecsDemand CreateDemand, ModifyDemand, DeleteDemand API.
- Modify ecsDemand DescribesDemands API.


## 1.7.166 - 2019-11-20
- Generated 2019-01-01 for `HBase`.


## 1.7.165 - 2019-11-20
- Support anonymous account for enableOrder.


## 1.7.164 - 2019-11-20
- Support anonymous account for enableOrder.


## 1.7.163 - 2019-11-20
- Add API ReportVoipProblems.
- Add API QueryVoipNumberBindINfos.


## 1.7.162 - 2019-11-19
- Supported transcode template.


## 1.7.161 - 2019-11-18
- Add seller refuse reason for queryRefundDetail.


## 1.7.160 - 2019-11-15
- Nlp Automl SDK.


## 1.7.159 - 2019-11-15
- Init php sdk.


## 1.7.158 - 2019-11-15
- Sync cdn APIs.


## 1.7.157 - 2019-11-15
- Fixed bugs.


## 1.7.156 - 2019-11-15
- Sync cdn APIs.


## 1.7.155 - 2019-11-15
- Add Scdn APIS.
- Sync cdn APIS.


## 1.7.154 - 2019-11-15
- Modify QueryAccountBill, support group by product.


## 1.7.153 - 2019-11-15
- Modify DescribeRestoreRangeInfo.


## 1.7.152 - 2019-11-15
- Add DescribeRestoreRangeInfo.


## 1.7.151 - 2019-11-14
- Released dysms OpenAPI.


## 1.7.150 - 2019-11-14
- Support Tag OpenAPI.
- Fix endpoint problem.


## 1.7.149 - 2019-11-13
- Supported Stream analyse.


## 1.7.148 - 2019-11-13
- Fix ResourceId type from Long to String on QueryCostUnitResource.


## 1.7.147 - 2019-11-13
- Fix ResourceId type from Long to String on QueryCostUnitResource.


## 1.7.146 - 2019-11-13
- Add new API QueryBillToOSSSubscription.


## 1.7.145 - 2019-11-13
- Supported API UpdateDomainRemark for Update Domain Remark.
- Supported API UpdateDomainRecordRemark for Update Record Remark.
- Unsupported API CheckDomainRecord.


## 1.7.144 - 2019-11-13
- FindServiceList add RouteConfJson result.


## 1.7.143 - 2019-11-13
- Support Go SDK.
- Support C SDK.
- Support PHP SDK.


## 1.7.142 - 2019-11-8
- Add DescribeJobErrorCode.


## 1.7.141 - 2019-11-5
- Add OperateBlackNo.


## 1.7.140 - 2019-11-2
- Generated 2017-05-25 for `Dyvmsapi`.


## 1.7.139 - 2019-11-1
- Supported directory APIs.


## 1.7.138 - 2019-10-31
- Initial construction.
- Public beta version.
- Supported AliyunController for Interface.
- Supported ConsoleController for Interface.
- Supported AccountController for Interface.


## 1.7.137 - 2019-10-31
- Initial construction.
- Public beta version.
- Supported AliyunController for Interface.
- Supported ConsoleController for Interface.
- Supported AccountController for Interface.


## 1.7.136 - 2019-10-30
- Update interface params of the cloudauth.


## 1.7.135 - 2019-10-30
- Add features for open api.


## 1.7.134 - 2019-10-29
- Add a new Alibaba Cloud Bill API named QueryAccountBill.


## 1.7.133 - 2019-10-25
- Initial construction.
- Public beta version.
- Supported AliyunController for Interface.
- Supported ConsoleController for Interface.
- Supported AccountController for Interface.


## 1.7.132 - 2019-10-25
- Supported query push records.


## 1.7.131 - 2019-10-25
- Add DescribeDBClusterPerformance, DescribeDBNodePerformance for performance.


## 1.7.130 - 2019-10-24
- Generated 2017-12-14 for `BssOpenApi`.


## 1.7.129 - 2019-10-23
- Add a new api named SubmitDynamicImageJob capture a certain part of the video as a dynamic image.
- Add a new api named SubmitWorkflowJob to initiate the VoD workflow processing for audio and video.
- Add a new field AuditStatus in the structure ImageInfo to GetImageInfo api response, which is used to identify the audit status of the image.
- Modify the data type of return field StorageLocation to String.
- Add a new field named MediaType to SubmitAIMediaAuditJob api request.


## 1.7.128 - 2019-10-23
- Add a new api named SubmitDynamicImageJob capture a certain part of the video as a dynamic image.
- Add a new api named SubmitWorkflowJob to initiate the VoD workflow processing for audio and video.
- Add a new field AuditStatus in the structure ImageInfo to GetImageInfo api response, which is used to identify the audit status of the image.
- Modify the data type of return field StorageLocation to String.
- Add a new field named MediaType to SubmitAIMediaAuditJob api request.


## 1.7.127 - 2019-10-23
- Add a new api named SubmitDynamicImageJob capture a certain part of the video as a dynamic image.
- Add a new api named SubmitWorkflowJob to initiate the VoD workflow processing for audio and video.
- Add a new field AuditStatus in the structure ImageInfo to GetImageInfo api response, which is used to identify the audit status of the image.
- Modify the data type of return field StorageLocation to String.
- Add a new field named MediaType to SubmitAIMediaAuditJob api request.


## 1.7.126 - 2019-10-22
- Supported Sync predict for Image.
- Supported Set Frame Frequency for Stream Predict.
- Fixed Errore Code For APIs.


## 1.7.125 - 2019-10-22
- RunInstances support AutoSnapshotPolicyId.


## 1.7.124 - 2019-10-22
- Fix ListDatabases CatalogName dataType integer to string.
- Remove GetLogicDatabase invalid output param named TotalCount.
- Remove ListIndexes invalid output param named ColumnList.
- Support SearchTable API to filter searchTarget.


## 1.7.123 - 2019-10-22
- Supported ExternalId IMM.


## 1.7.122 - 2019-10-21
- Release first version.


## 1.7.121 - 2019-10-18
- Generated 2019-09-10 for `DnsKnocker`.


## 1.7.120 - 2019-10-18
- Add X509 API, including queryDeviceCert, queryCertUrlByApplyId, createDeviceCert.
- Add authType parameter of APIs, including createProduct, queryProduct, queryProductList, CreateProductPop, QueryProductAllInfo, QueryProductListPop, ListProductConsole, QueryAllProductListPop, QueryDeviceBatchList.


## 1.7.119 - 2019-10-17
- Support instance manage API, includes UpdateInstance and DeleteInstance.
- Support user manage API, includes ListUsers and UpdateUser.
- Support meta data information API, includes ListInstances, ListDatabases, ListLogicDatabases, ListTables, ListLogicTables, ListColumns, ListIndexes.
- Support meta data search API, inclues SearchDatabase, SearcTable.
- Support RegisterInstance API to set datalinkName and useDsql option.


## 1.7.118 - 2019-10-16
- Add ListTagResources OpenApi.
- Add TagResources OpenApi.
- Add UntagResources OpenApi.
- Add ModifyDBInstanceAutoUpgradeMinorVersion OpenApi.


## 1.7.117 - 2019-10-14
- Supported callback for groups APIs.
- Supported NVR historical stream for DescribeStreamURL.


## 1.7.116 - 2019-10-14
- Support continuous pushing.


## 1.7.115 - 2019-10-12
- Add new api.


## 1.7.114 - 2019-10-9
- Add VideoCancelScan Api.


## 1.7.113 - 2019-10-9
- Support API RecognizeImageColor.
- Support API DetectImageElements.
- Support API RecolorImage.
- Support API SegmentImage.
- Support API ChangeImageSize.
- Support API ExtendImageStyle.
- Support API RecognizeImageStyle.
- Support API MakeSuperResolution.


## 1.7.112 - 2019-10-8
- Supported Grab Frame IMM.


## 1.7.111 - 2019-10-8
- Supported API MassPush for Push Message or Notice.


## 1.7.110 - 2019-10-8
- Generated 2016-01-20 for `Kms`.


## 1.7.109 - 2019-9-26
- Supported RingConfig for BindAxb,BindAxn,BindAxg,BindAxnExtension.
- Add QuerySubsId.


## 1.7.108 - 2019-9-25
- Support anonymous account.


## 1.7.107 - 2019-9-24
- Generated 2019-08-10 for `multimediaai`.


## 1.7.106 - 2019-9-24
- Generated 2019-04-30 for `schedulerx2`.


## 1.7.105 - 2019-9-23
- Add param, DescribePrice supprot ReservedInstance.


## 1.7.104 - 2019-9-19
- Supported Video Abstract for IMM.


## 1.7.103 - 2019-9-19
- Supported Video Abstract for IMM.


## 1.7.102 - 2019-9-19
- Add bizSubCode and so on.


## 1.7.101 - 2019-9-18
- Supported Video Abstract for IMM.


## 1.7.100 - 2019-9-18
- Generated 2016-11-01 for `live`.


## 1.7.99 - 2019-9-9
- CreateInstance add toen.
- UpdateInstance add toen.


## 1.7.98 - 2019-9-6
- Update endpoint data.


## 1.7.97 - 2019-9-6
- Generated 2016-04-28 for `Vpc`.


## 1.7.96 - 2019-9-5
- Supported for setEndpoint method.


## 1.7.95 - 2019-9-5
- Generated 2014-05-15 for `Slb`.


## 1.7.94 - 2019-9-5
- Generated 2018-11-11 for `foas`.


## 1.7.93 - 2019-9-5
- Generated 2019-05-24 for `cusanalytic_sc_online`.


## 1.7.92 - 2019-9-5
- Generated 2015-11-01 for `Market`.


## 1.7.91 - 2019-9-5
- Generated 2016-07-14 for `CloudAPI`.


## 1.7.90 - 2019-9-5
- Generated 2017-08-01 for `polardb`.


## 1.7.89 - 2019-9-5
- Generated 2017-12-14 for `BssOpenApi`.


## 1.7.88 - 2019-9-3
- Generated 2014-08-15 for `Rds`.


## 1.7.87 - 2019-8-30
- Revert to 2015-09-01.


## 1.7.86 - 2019-8-30
- Release Apis of Version 2019-09-10.


## 1.7.85 - 2019-8-30
- Supported Meida complex for IMM.


## 1.7.84 - 2019-8-30
- Add api for getTrace and searchTracelist.


## 1.7.83 - 2019-8-29
- Add PTZ APIs.


## 1.7.82 - 2019-8-29
- Move StopExecution params to body.


## 1.7.81 - 2019-8-29
- For publish.


## 1.7.80 - 2019-8-28
- Add DBS API UpgradeBackupPlan.
- Add DBS API DescribePreCheckProgressList.


## 1.7.79 - 2019-8-26
- QueryInstanceBillResponse change ownerId type from Long to String.
- QueryInstanceBillResponse change usage type from float to String.
- QueryInstanceBillResponse change listPrice type from float to String.
- QueryInstanceBillResponse change deductedByResourcePackage type from float to String.


## 1.7.78 - 2019-8-22
- Support API RecognizeImageColor.
- Support API DetectImageElements.
- Support API RecolorImage.
- Support API SegmentImage.
- Support API ChangeImageSize.
- Support API ExtendImageStyle.
- Support API RecognizeImageStyle.
- Support API MakeSuperResolutionImage.


## 1.7.77 - 2019-8-22
- Api release 2019-08-08 public sdk 2-5-2 sdk release 20190822 shichun-fsc.


## 1.7.76 - 2019-8-21
- Move StartExecution params to body.


## 1.7.75 - 2019-8-21
- Return backup job id when create backup.
- Return backup set size when describe backups.


## 1.7.74 - 2019-8-20
- API TaobaoFilmGetSchedules retrun col add hallId.


## 1.7.73 - 2019-8-16
- Add LinkIoTEdge API.


## 1.7.72 - 2019-8-15
- FnF public version.
- Add Report task api.


## 1.7.71 - 2019-8-15
- Add API BatchStartCdnDomain, BatchStopCdnDomain, DescribeTagResources, DescribeUserTags, TagResources, UntagResources.


## 1.7.70 - 2019-8-15
- QueryInstanceBillResponse add ServicePeriod.


## 1.7.69 - 2019-8-14
- Expose the interface to the yundun-console.
- Update interface definitions of the cloudauth.


## 1.7.68 - 2019-8-13
- Optimize return code.
- Edit QueryDataset.


## 1.7.67 - 2019-8-13
- Supported for openapi new version.


## 1.7.66 - 2019-8-12
- Support Defect Face API.


## 1.7.65 - 2019-8-9
- Add a lot of new API.


## 1.7.64 - 2019-8-8
- Add CreateStorageSet api to support storageSet.
- Add DeleteStorageSet api to support storageSet.
- Add ModifyStorageSetAttribute api to support storageSet.
- Add DescribeStorageSets api to support storageSet.
- Add DescribeStorageSetDetails api to support storageSet.
- Add parameter StorageSetId,StorageSetPartitionNumber to api CreateDisk,RunInstances,CreateInstance support storageSet.
- Add StorageSetId,StorageSetPartitionNumber with response of api DescribeDisks.
- Add DescribeNetworkInterfaces to support filter by PrivateIpAddress.


## 1.7.63 - 2019-8-8
- Group, Plugin support tag authentication.


## 1.7.62 - 2019-8-7
- Generated 2018-03-13 for `retailcloud`.


## 1.7.61 - 2019-8-6
- Supported GetMediaMeta for IMM.


## 1.7.60 - 2019-8-6
- Supported GetMediaMeta for IMM.


## 1.7.59 - 2019-8-6
- Supported GetMediaMeta for IMM.


## 1.7.58 - 2019-8-6
- Supported GetMediaMeta for IMM.


## 1.7.57 - 2019-8-6
- Supported GetMediaMeta for IMM.


## 1.7.56 - 2019-8-5
- GetOrderDetail add originalConfig param.


## 1.7.55 - 2019-8-5
- GetOrderDetail add originalConfig param.


## 1.7.54 - 2019-8-5
- Modify DBS API DescribeFullBackupList.


## 1.7.53 - 2019-8-2
- SubscribeBillToOSSRequest add multAccountRelSubscribe, bucketOwnerId.
- UnsubscribeBillToOSSRequest add multAccountRelSubscribe.


## 1.7.52 - 2019-7-31
- Endpoint auto route.


## 1.7.51 - 2019-7-30
- Suport ImportCredentials api.


## 1.7.50 - 2019-7-30
- Suport ImportCredentials api.


## 1.7.49 - 2019-7-29
- Supported group API.
- Supported device APIs.
- Supported stream APIs.
- Supported template APIs.
- Supported record APIs.
- Supported domain APIs.


## 1.7.48 - 2019-7-26
- Generated 2019-05-24 for `cusanalytic_sc_online`.


## 1.7.47 - 2019-7-26
- Generated 2019-05-24 for `cusanalytic_sc_online`.


## 1.7.46 - 2019-7-25
- Api createKey add optional parameter `ProtectionLevel`.
- Api describeKey add a field `ProtectionLevel` in the response.
- Add Api `DescribeService`.


## 1.7.45 - 2019-7-25
- App-related actions support tag authentication.


## 1.7.44 - 2019-7-23
- Supported CreationOption of CreateDBCluster with `CloneFromPolarDB `,`CloneFromRDS`,`MigrationFromRDS`.


## 1.7.43 - 2019-7-19
- QueryMonthlyBillResponse add roundDownDiscount.
- QueryBillResponse add roundDownDiscount.
- QueryInstanceBillResponse add item.


## 1.7.42 - 2019-7-18
- Generated 2016-06-07 for `cr`.


## 1.7.41 - 2019-7-18
- Add a new field named Input to SubmitAIJob api request to set the input file of AI job.
- Change the field MediaId of SubmitAIJob api to non-mandatory.


## 1.7.40 - 2019-7-17
- Add a lot of new API.


## 1.7.39 - 2019-7-14
- Modify DBS API DescribeBackupPlanList.


## 1.7.38 - 2019-7-12
- Public api AddLivePullStreamInfoConfig.


## 1.7.37 - 2019-7-11
- Modify CreateBackupPlan.
- Modify ConfigureBackupPlan.
- Modify DescribeFullBackupList.
- Modify DescribeRestoreTaskList.
- Add ModifyBackupSourceEndpoint.
- Add ModifyBackupStrategy.
- Add ModifyBackupPlanName.


## 1.7.36 - 2019-7-5
- Supported library managment for simillarity scene.
- Remove the local file uploader code which can be downloaded from yundun content security document.


## 1.7.35 - 2019-7-5
- Add TaskCancelStatus for QueryTaskList api.


## 1.7.34 - 2019-7-4
- Supported API DescribeRecordStatisticsy for Query Volume.
- Supported API DescribeDomainStatistics for Query Volume.


## 1.7.33 - 2019-7-4
- Supported batch querying for device detail.


## 1.7.32 - 2019-7-3
- Supported API DescribeRecordStatisticsSummary for Query Volume.
- Supported API DescribeDomainStatisticsSummary for Query Volume.
- Supported API DescribeRecordStatisticsHistory for Query Volume.
- Supported API DescribeDomainDnsStatistics for Query Volume.


## 1.7.31 - 2019-7-2
- FnF public version.


## 1.7.30 - 2019-7-1
- Support cloud_essd disk category for API CreateDisk, CreateInstance and RunInstances, and support configurating PerformanceLevel when choose cloud_essd.
- Add ModifyDiskSpec API to support cloud_essd PerformanceLevel modification.
- Add AutoProvisioningGroup interfaces, provide AutoProvisioningGroup function.
- Add RetentionDays to snapshot creating.


## 1.7.29 - 2019-6-27
- Added setting of crop_mode parameter.


## 1.7.28 - 2019-6-24
- Add some new apis to manage VoD domain, such as AddVodDomain, UpdateVodDomain, DeleteVodDomain, BatchStartVodDomain, BatchStopVodDomain, DescribeVodUserDomains, DescribeVodDomainDetail.
- Add some new apis to manage VoD domain config, such as BatchSetVodDomainConfigs, DescribeVodDomainConfigs, DeleteVodSpecificConfig, SetVodDomainCertificate, DescribeVodCertificateList, DescribeVodDomainCertificateInfo.
- Add a new field named AppId to some apis supporting the VoD App feature, such as AddWorkFlow, GetWorkFlow, ListWorkFlow, AddVodTemplate, GetVodTemplate, ListVodTemplate, AddTranscodeTemplateGroup, GetTranscodeTemplateGroup, ListTranscodeTemplateGroup, AddWatermark, GetWatermark, ListWatermark, UploadMediaByURL.
- Add a new field named UserData to SubmitTranscodeJobs api request to support user-defined extension fields, which can be used for transparent return when callbacks.


## 1.7.27 - 2019-6-24
- Add some new apis to manage VoD domain, such as AddVodDomain, UpdateVodDomain, DeleteVodDomain, BatchStartVodDomain, BatchStopVodDomain, DescribeVodUserDomains, DescribeVodDomainDetail.
- Add some new apis to manage VoD domain config, such as BatchSetVodDomainConfigs, DescribeVodDomainConfigs, DeleteVodSpecificConfig, SetVodDomainCertificate, DescribeVodCertificateList, DescribeVodDomainCertificateInfo.
- Add a new field named AppId to some apis supporting the VoD App feature, such as AddWorkFlow, GetWorkFlow, ListWorkFlow, AddVodTemplate, GetVodTemplate, ListVodTemplate, AddTranscodeTemplateGroup, GetTranscodeTemplateGroup, ListTranscodeTemplateGroup, AddWatermark, GetWatermark, ListWatermark, UploadMediaByURL.
- Add a new field named UserData to SubmitTranscodeJobs api request to support user-defined extension fields, which can be used for transparent return when callbacks.


## 1.7.26 - 2019-6-19
- Removed 2018-12-01 for `cr`.


## 1.7.25 - 2019-6-19
- Generated 2018-12-01 for `cr`.


## 1.7.24 - 2019-6-19
1, Add DefaultPolicyVersion as return field to GetPolicy interface, Facilitating to get policy document from this interface.
2, Add RotateStrategy as input field to CreatePolicyVersion interface for rotating policy version when reaching policy version limit.


## 1.7.23 - 2019-6-18
- Supported the related recommend.
- Supported exposure time controll and exposure filter by scene.


## 1.7.22 - 2019-6-17
- Companyreg release.


## 1.7.21 - 2019-6-13
- Fixed DescribeAvailableResource OpenApi AvailableZones value problem.


## 1.7.20 - 2019-6-13
- Generated 2015-01-01 for `R-kvstore`.


## 1.7.19 - 2019-6-13
- Added Network Assistant openapi SDK.


## 1.7.18 - 2019-6-13
- Added DescribeAvailableResource OpenApi.
- Upgrade version to 2.3.8


## 1.7.17 - 2019-6-12
- Added RenewBackupPlan DBS interface.


## 1.7.16 - 2019-6-12
- Fixed bug.


## 1.7.15 - 2019-6-12
- Generated 2018-12-01 for `cr`.


## 1.7.14 - 2019-6-12
- Added InvokeDataAPIService interface, support invoke service of data api to get sql query result.
- Added GetDataAPIServiceDetail interface, support get data api's detail information.
- Added CreateDataAPIService interface, support create data api with sql statement.


## 1.7.13 - 2019-6-12
- Removed `2015-05-06`,`2018-12-01` for `Cr`.


## 1.7.12 - 2019-6-12
- Generated 2019-03-06 for `Dbs`.


## 1.7.11 - 2019-6-11
- Generated 2015-05-06, 2016-06-07, 2018-12-01 for `cr`.


## 1.7.10 - 2019-6-10
- Generated 2015-05-06, 2016-06-07, 2018-12-01 for `cr`.


## 1.7.9 - 2019-6-3
- Generated 2018-01-12 for `afs`.


## 1.7.8 - 2019-6-3
- Generated 2018-05-24 for `welfare-inner`.


## 1.7.7 - 2019-6-2
- Generated 2015-05-06, 2016-06-07, 2018-12-01 for `cr`.


## 1.7.6 - 2019-5-31
- Generated 2016-11-11, 2015-06-30 for `BatchCompute`.


## 1.7.5 - 2019-5-30
- Generated 2013-01-11, 2016-11-11 for `BatchCompute`.


## 1.7.4 - 2019-5-30
- Generated 2019-05-21 for `saf`.


## 1.7.3 - 2019-5-30
- Generated 2015-12-15, 2018-04-18 for `CS`.


## 1.7.2 - 2019-5-30
- Generated 2014-02-14, 2015-05-01, 2018-03-02 for `Ram`.


## 1.7.1 - 2019-5-30
- Generated 2015-05-06, 2016-06-07, 2018-12-01 for `cr`.


## 1.7.0 - 2019-5-29
- Supported `replace`. 


## 1.6.8 - 2019-5-29
- Update Smartag.


## 1.6.7 - 2019-5-29
- Update product.


## 1.6.6 - 2019-5-29
- Generated 2015-05-06, 2016-06-07, 2018-12-01 for `cr`.


## 1.6.5 - 2019-5-29
- Generated 2015-05-06 for `cr`.


## 1.6.4 - 2019-05-27
- Improved Docs.
- Updated APIs.


## 1.6.3 - 2019-05-20
- Updated APIs.


## 1.6.2 - 2019-05-16
- Updated APIs.


## 1.6.1 - 2019-05-09
- Regenerate products.
- Generate `composer.json` for each product.


## 1.6.0 - 2019-05-07
- Changed `Resolver` file name.


## 1.5.1 - 2019-04-19
- Supported `Sas`, `Ivision`.
- Added tests for `Sas`, `Ivision`.


## 1.5.0 - 2019-04-18
- Improved parameters methods.
- Optimized the logic for body encode.


## 1.4.0 - 2019-04-11
- Added `2019-03-25` for `ImageSearch`.


## 1.3.5 - 2019-04-09
- Support `Kms`.


## 1.3.4 - 2019-04-09
- Fixed `MNS`.


## 1.3.3 - 2019-04-08
- Added Apis for `Dbs`.


## 1.3.2 - 2019-04-08
- Support `Dypnsapi`.


## 1.3.1 - 2019-04-02
- Remove `finmall`.


## 1.3.0 - 2019-04-01
- Updated `composer.json`.


## 1.2.10 - 2019-03-27
- Improve `Resolver`.


## 1.2.9 - 2019-03-27
- Support `Dbs`.
- Support `AliProbe`.
- Fixed `BatchReceiveMessage`.


## 1.2.8 - 2019-03-25
- Updated README.md.
- Updated Apis.


## 1.2.7 - 2019-03-24
- Append `SDK` for User-Agent.


## 1.2.6 - 2019-03-24
- Update APIs.


## 1.2.5 - 2019-03-23
- Remove SVG.


## 1.2.4 - 2019-03-19
- Support `alikafka`.
- Support `bss`.
- Support `cds`.
- Support `cf`.
- Support `Commondriver`.
- Support `dataworks-public`.
- Support `drcloud`.
- Support `Edas`.
- Support `Foas`.
- Support `HPC`.
- Support `ITaaS`.
- Support `jarvis-public`.
- Support `LinkWAN`.
- Support `Lubanruler`.
- Support `Oms`.
- Support `PTS`.
- Support `Qualitycheck`.
- Support `waf-openapi`.


## 1.2.3 - 2019-03-19
- Support `cloudwf`.
- Update APIs for `Aegis`.
- Update APIs for `cdn`.
- Update APIs for `dcdn`.
- Update APIs for `imm`.
- Update APIs for `live`.
- Update APIs for `NAS`.


## 1.2.2 - 2019-03-19
- Update docs.


## 1.2.1 - 2019-03-17
- Add `Supported.md`.
- Support `ProductCatalog`.
- Support `polardb`.
- Support `cloudmarketing`.
- Support `Aas`.
- Support `Ft`.
- Support `gpdb`.
- Support `OssAdmin`.
- Support `PetaData`.


## 1.2.0 - 2019-03-16
- Redesign the request class to reduce the code size.
- Support `Yundun`.
- Support `Actiontrail`.
- Support `industry-brain`.
- Support `welfare-inner`.
- Support `xspace`.
- Support `ROS`.
- Support `openanalytics`.
- Support `Cbn`.
- Support `cr`.
- Support `MoPen`.
- Support `Snsuapi`.
- Support `finmall`.
- Support `Emr`.

## 1.1.2 - 2019-03-15
- Add `Iot` Tests.
- Add `Aegis` Apis.


## 1.1.1 - 2019-03-14
- Add `DescribeWhiteListStrategyList` for `Aegis`.
- Add `DescribeAvailableCrossRegion` for `Rds`.
- Add `DescribeAvailableRecoveryTime` for `Rds`.
- Add `DescribeCrossRegionBackupDBInstance` for `Rds`.
- Update Apis for `Aegis`.
- Update Apis for `BssOpenApi`.
- Update Apis for `Green`.


## 1.1.0 - 2019-03-14
- IDE auto-prompt for unlabeling discarded methods.
- Reduce size.
- Added support for 127 Api.
- Functional testing increased from 28 to 35.


## 1.0.10 - 2019-03-13
- Update Docs.


## 1.0.9 - 2019-03-07
- Optimize api analysis.


## 1.0.8 - 2019-02-22
- 238 interfaces added to support 29 products.


## 1.0.7 - 2019-02-21
- Add APIs for `VOD`.


## 1.0.6 - 2019-02-12
- Support Image Search.


## 1.0.5 - 2019-01-23
- `AlibabaCloud\Dybaseapi\MNS` - Support MNS with Feature test.
- Update readme.
- Update bootstrap for test.


## 1.0.4 - 2019-01-15
- Improve Test.
- Improve Resolver.


## 1.0.3 - 2019-01-11
- `AlibabaCloud\CloudAPI` - Support CloudAPI.


## 1.0.2 - 2019-01-11
- Support test on the Windows.


## 1.0.1 - 2019-01-09
- `AlibabaCloud\NlsFiletrans` - Support NLS Filetrans.
- `AlibabaCloud\NlsCloudMeta` - Support NLS Cloud Meta.

## 1.0.0 - 2019-01-07
- Initial release of the Alibaba Cloud SDK for PHP Version 1.0.0 on Packagist See <https://github.com/aliyun/openapi-sdk-php> for more information.
