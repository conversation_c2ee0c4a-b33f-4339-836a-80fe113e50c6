<?php

namespace xiaoman\AlibabaSdk\taobao\top\request;
use xiaoman\AlibabaSdk\taobao\top\RequestCheckUtil;

/**
 * TOP API: alibaba.icbu.ames.enterprise.checkexist
 *
 * <AUTHOR> create
 * @since 1.0, 2018.07.26
 */
class AlibabaIcbuAmesEnterpriseCheckexistRequest
{
    private $usc;

    private $apiParas = array();

    public function setUsc($usc)
    {
        $this->usc = $usc;
        $this->apiParas['usc'] = $usc;
    }

    public function getUsc()
    {
        return $this->usc;
    }


    public function getApiMethodName()
    {
        return 'alibaba.icbu.ames.enterprise.checkexist';
    }

    public function getApiParas()
    {
        return $this->apiParas;
    }

    public function check()
    {
        RequestCheckUtil::checkNotNull($this->usc, 'usc');
    }

    public function putOtherTextParam($key, $value)
    {
        $this->apiParas[$key] = $value;
        $this->$key = $value;
    }
}
