<?php
/**
 * The file is part of the alibaba-sdk.
 *
 * (c) anhoder <<EMAIL>>.
 *
 * 2022/4/20 12:56
 */

namespace xiaoman\AlibabaSdk\taobao\dtc\request;

use http\Exception\RuntimeException;
use xiaoman\AlibabaSdk\taobao\dtc\LazopRequest;
use xiaoman\AlibabaSdk\taobao\dtc\UrlConstants;

class CustomerInfoRequest extends LazopRequest
{
    private $customerId;
    private $sellerId;

    public function __construct()
    {
        parent::__construct('', 'GET');
    }

    public function setCustomerId($customerId)
    {
        $this->customerId = $customerId;
        $this->apiName = $this->formatApiName(sprintf(UrlConstants::URI_CUSTOMER_INFO, $customerId));
    }

    public function setSellerId($sellerId)
    {
        $this->sellerId = $sellerId;
        $this->addApiParam('sellerId', $sellerId);
    }

    public function checkParams()
    {
        $this->checkNotNull('customerId', $this->customerId);
        $this->checkNotNull('sellerId', $this->sellerId);
    }
}
