# Xiaoman ORM 框架实战教程

## 为什么需要这个ORM框架？

在传统的PHP开发中，我们经常遇到这些问题：
- **数据访问代码重复**：每个表都要写相似的CRUD操作
- **SQL拼接容易出错**：手写SQL容易产生注入漏洞和语法错误
- **业务逻辑混乱**：数据访问、业务处理、格式化混在一起
- **扩展性差**：添加新功能需要修改多处代码

Xiaoman ORM通过**元数据驱动**的设计理念，解决了这些问题。它不是简单的数据库抽象层，而是一套完整的**领域驱动设计(DDD)**解决方案。

## 核心设计理念

### 1. 元数据驱动 - 配置即代码

传统方式需要为每个表写大量重复代码，而Xiaoman ORM只需要定义一次元数据，就能自动生成所有功能：

```php
// 传统方式：需要写很多重复代码
class UserDAO {
    public function findById($id) { /* SQL代码 */ }
    public function findByName($name) { /* SQL代码 */ }
    public function create($data) { /* SQL代码 */ }
    // ... 更多重复代码
}

// Xiaoman ORM：只需定义元数据
class UserMetadata extends Metadata {
    protected $columns = [
        'id' => ['type' => 'int', 'filter' => ['enable' => true]],
        'name' => ['type' => 'string', 'filter' => ['enable' => true]],
        // 定义一次，自动获得所有功能
    ];
}
```

### 2. 职责分离 - 每个组件专注一件事

框架将数据操作分解为6个专门的组件，每个组件只负责一个职责：

```
数据定义 → Metadata     (定义表结构和规则)
单条操作 → SingleObject (处理单条记录的CRUD)
批量操作 → BatchObject  (处理多条记录)
查询构建 → Filter       (构建复杂查询条件)
数据展示 → Formatter    (格式化输出数据)
业务逻辑 → Operator     (执行复杂业务操作)
```

这种设计让代码更清晰、更容易维护和扩展。

## 从实际案例理解框架设计

让我们通过一个真实的业务场景来理解框架的设计思路。

### 业务场景：用户设置管理

假设我们要开发一个用户设置功能，需要：
- 存储用户的各种配置（主题、语言、通知等）
- 支持批量查询和更新
- 提供格式化的API输出
- 记录操作日志

### 传统开发方式的问题

```php
// 传统方式：所有逻辑混在一起
class UserSettingController {
    public function getUserSettings($userId) {
        // 1. 数据访问逻辑
        $sql = "SELECT * FROM user_settings WHERE user_id = ? AND enable_flag = 1";
        $settings = $db->query($sql, [$userId]);

        // 2. 业务逻辑
        foreach ($settings as &$setting) {
            if ($setting['key'] == 'theme') {
                $setting['display_name'] = ucfirst($setting['value']);
            }
        }

        // 3. 格式化逻辑
        $result = [];
        foreach ($settings as $setting) {
            $result[$setting['key']] = $setting['value'];
        }

        return $result;
    }
}
```

**问题**：
- 数据访问、业务逻辑、格式化混在一起
- 难以测试和维护
- 代码重复，每个功能都要写类似逻辑

### Xiaoman ORM的解决方案

框架通过职责分离，将上述逻辑分解到不同组件：

```
用户请求 → Controller → Filter(查询) → BatchObject(数据) → Formatter(格式化) → 返回结果
                           ↓
                      Operator(业务逻辑)
                           ↓
                      Metadata(数据定义)
```

每个组件专注自己的职责，代码更清晰、更容易维护。

## 第一个完整示例：从零开始

让我们通过创建用户设置功能，学习如何使用框架。

### 步骤1：理解数据结构

首先分析我们的数据表：

```sql
CREATE TABLE tbl_user_setting (
    id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    key VARCHAR(255) NOT NULL,
    value TEXT,
    enable_flag INTEGER DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 步骤2：创建Metadata - 数据的"身份证"

Metadata是框架的核心，它告诉框架如何处理这张表：

```php
<?php
namespace common\library\setting\user;

use xiaoman\orm\metadata\Metadata;

class UserSettingMetadata extends Metadata
{
    // 字段定义 - 这是框架的"配置文件"
    protected $columns = [
        'id' => [
            'type' => 'int',           // 数据库类型
            'php_type' => 'int',       // PHP类型
            'nullable' => '0',         // 是否可空
            'filter' => [              // 查询配置
                'enable' => true,      // 允许作为查询条件
                'batch' => true        // 允许批量查询
            ]
        ],
        'user_id' => [
            'type' => 'int',
            'php_type' => 'int',
            'nullable' => '0',
            'filter' => ['enable' => true, 'batch' => true]
        ],
        'key' => [
            'type' => 'string',
            'php_type' => 'string',
            'nullable' => '0',
            'filter' => ['enable' => true, 'batch' => true]
        ],
        'value' => [
            'type' => 'text',
            'php_type' => 'string',
            'nullable' => '1',
            'filter' => ['enable' => false, 'batch' => false] // 大字段不允许直接查询
        ],
    ];

    // 告诉框架这些基本信息
    public static function table() { return 'tbl_user_setting'; }
    public static function dataSource() { return Metadata::DATA_SOURCE_POSTGRESQL_CLIENT; }
    public static function singeObject() { return UserSetting::class; }
    public static function batchObject() { return BatchUserSetting::class; }
    public static function filter() { return UserSettingFilter::class; }
    public static function formatter() { return UserSettingFormatter::class; }
    public static function operator() { return UserSettingOperator::class; }
}
```

**设计要点**：
- `filter.enable` 控制字段是否可以作为查询条件
- 大字段（如text）通常设置为不可查询，避免性能问题
- 每个字段都要明确定义类型，确保数据安全

### 步骤3：创建SingleObject - 单条记录操作

SingleObject负责处理单条记录的CRUD操作：

```php
<?php
namespace common\library\setting\user;

use xiaoman\orm\common\SingleObject;

/**
 * 用户设置单个对象
 * @property int $id
 * @property int $client_id
 * @property int $user_id
 * @property string $key
 * @property string $value
 * @property int $enable_flag
 */
class UserSetting extends SingleObject
{
    use InitUserSettingMetadata; // 引入元数据

    // 业务常量
    const ENABLE_FLAG_ENABLED = 1;
    const ENABLE_FLAG_DISABLED = 0;

    const KEY_THEME = 'theme';
    const KEY_LANGUAGE = 'language';

    public function __construct(int $clientId, int $userId = null, string $key = null)
    {
        parent::__construct($clientId);

        // 如果提供了用户ID和键，直接加载数据
        if ($userId && $key) {
            $this->load(['client_id' => $clientId, 'user_id' => $userId, 'key' => $key]);
        }
    }

    /**
     * 业务方法：设置值（自动处理JSON）
     */
    public function setValue($value)
    {
        if (is_array($value) || is_object($value)) {
            $this->value = json_encode($value, JSON_UNESCAPED_UNICODE);
        } else {
            $this->value = (string)$value;
        }
    }

    /**
     * 业务方法：获取格式化的值
     */
    public function getFormattedValue()
    {
        if (empty($this->value)) return null;

        // 尝试解析JSON
        $decoded = json_decode($this->value, true);
        return json_last_error() === JSON_ERROR_NONE ? $decoded : $this->value;
    }

    /**
     * 生命周期钩子：创建前
     */
    protected function beforeCreate()
    {
        $this->create_time = date('Y-m-d H:i:s');
        $this->update_time = date('Y-m-d H:i:s');

        if (empty($this->enable_flag)) {
            $this->enable_flag = self::ENABLE_FLAG_ENABLED;
        }

        return parent::beforeCreate();
    }

    /**
     * 生命周期钩子：更新前
     */
    protected function beforeUpdate()
    {
        $this->update_time = date('Y-m-d H:i:s');
        return parent::beforeUpdate();
    }
}
```

**设计要点**：
- 通过构造函数提供便捷的数据加载方式
- 封装业务逻辑（如JSON处理）到专门的方法
- 使用生命周期钩子自动处理时间戳
- 定义业务常量，避免魔法数字

### 步骤4：创建Filter - 查询构建器

Filter负责构建查询条件：

```php
<?php
namespace common\library\setting\user;

use xiaoman\orm\common\Filter;

/**
 * 用户设置查询过滤器
 * @property int $user_id
 * @property string $key
 * @property int $enable_flag
 * @method BatchUserSetting find()
 */
class UserSettingFilter extends Filter
{
    use InitUserSettingMetadata;

    public function defaultSetting()
    {
        // 设置默认查询条件
        $this->client_id = $this->clientId;
        $this->enable_flag = UserSetting::ENABLE_FLAG_ENABLED;
        parent::defaultSetting();
    }

    /**
     * 业务查询方法：按用户查询
     */
    public function byUser($userId)
    {
        $this->user_id = $userId;
        return $this; // 支持链式调用
    }

    /**
     * 业务查询方法：按配置键查询
     */
    public function byKey($key)
    {
        $this->key = $key;
        return $this;
    }

    /**
     * 业务查询方法：按多个键查询
     */
    public function byKeys(array $keys)
    {
        $this->key = new \xiaoman\orm\database\data\In($keys);
        return $this;
    }

    /**
     * 业务查询方法：只查询启用的设置
     */
    public function onlyEnabled()
    {
        $this->enable_flag = UserSetting::ENABLE_FLAG_ENABLED;
        return $this;
    }
}
```

**设计要点**：
- `defaultSetting()` 设置默认查询条件，避免每次都要设置
- 提供业务相关的查询方法，让代码更语义化
- 支持链式调用，提高代码可读性

## 实战演练：完整的CRUD操作

现在我们已经创建了基础组件，让我们看看如何在实际业务中使用它们。

### 场景1：创建用户设置

```php
// 创建用户的主题设置
$setting = new UserSetting($clientId);
$setting->user_id = 123;
$setting->key = UserSetting::KEY_THEME;
$setting->setValue(['theme' => 'dark', 'sidebar' => 'collapsed']); // 自动JSON编码
$setting->enable_flag = UserSetting::ENABLE_FLAG_ENABLED;

$result = $setting->create();
if ($result) {
    echo "设置创建成功，ID: " . $setting->id;
}
```

**关键点**：
- 使用业务常量而不是魔法字符串
- `setValue()` 方法自动处理JSON编码
- `beforeCreate()` 钩子自动设置时间戳

### 场景2：查询用户设置

```php
// 查询单个设置
$themeSetting = new UserSetting($clientId, 123, UserSetting::KEY_THEME);
if (!$themeSetting->isNew()) {
    $themeConfig = $themeSetting->getFormattedValue(); // 自动JSON解码
    echo "用户主题: " . $themeConfig['theme'];
}

// 批量查询用户的所有UI设置
$filter = new UserSettingFilter($clientId);
$filter->byUser(123)
       ->byKeys([UserSetting::KEY_THEME, UserSetting::KEY_LANGUAGE])
       ->onlyEnabled()
       ->select(['key', 'value', 'update_time'])
       ->orderBy('update_time', 'desc');

$batchSettings = $filter->find();
$settingsData = $batchSettings->getAttributes();

foreach ($settingsData as $setting) {
    echo "{$setting['key']}: {$setting['value']}\n";
}
```

**关键点**：
- 构造函数支持直接加载数据
- 链式调用让查询条件更清晰
- `getFormattedValue()` 自动处理JSON解码

### 场景3：更新设置

```php
// 更新单个设置
$setting = new UserSetting($clientId, 123, UserSetting::KEY_THEME);
if (!$setting->isNew()) {
    $setting->setValue(['theme' => 'light', 'sidebar' => 'expanded']);
    $setting->update(['value']); // 只更新value字段
}

// 批量更新多个用户的语言设置
$filter = new UserSettingFilter($clientId);
$filter->byKey(UserSetting::KEY_LANGUAGE)
       ->user_id = new \xiaoman\orm\database\data\In([123, 456, 789]);

$batchSettings = $filter->find();
$operator = $batchSettings->getOperator();
$operator->update(['value' => 'zh']); // 批量更新为中文
```

**关键点**：
- `update()` 可以指定只更新特定字段
- `beforeUpdate()` 钩子自动更新时间戳
- Operator 提供批量操作能力

## 深入理解：为什么这样设计？

### 设计原理1：元数据驱动的威力

传统ORM通常使用注解或配置文件，而Xiaoman ORM使用PHP数组定义元数据。这样做的好处：

```php
// 传统注解方式（其他框架）
/**
 * @Entity
 * @Table(name="user_settings")
 */
class UserSetting {
    /**
     * @Id
     * @Column(type="integer")
     * @GeneratedValue
     */
    private $id;

    /**
     * @Column(type="string", length=255, nullable=false)
     */
    private $key;
}

// Xiaoman ORM方式：更灵活，更强大
protected $columns = [
    'key' => [
        'type' => 'string',
        'php_type' => 'string',
        'nullable' => '0',
        'filter' => ['enable' => true, 'batch' => true], // 查询控制
        'validation' => ['required', 'max:255'],         // 验证规则
        'business_rules' => ['unique_per_user']          // 业务规则
    ]
];
```

**优势**：
- **运行时可修改**：可以根据业务需要动态调整字段配置
- **更丰富的配置**：不仅定义结构，还能控制查询、验证、业务规则
- **更好的性能**：PHP数组比解析注解更快

### 设计原理2：职责分离的价值

看看传统方式和框架方式的对比：

```php
// 传统方式：所有逻辑混在一起
class UserSettingService {
    public function getUserTheme($userId) {
        // 数据访问
        $sql = "SELECT value FROM user_settings WHERE user_id = ? AND key = 'theme'";
        $result = $this->db->query($sql, [$userId]);

        // 业务逻辑
        if (empty($result)) {
            $theme = 'light'; // 默认主题
        } else {
            $theme = json_decode($result[0]['value'], true);
        }

        // 格式化
        return [
            'theme' => $theme['theme'],
            'display_name' => ucfirst($theme['theme']),
            'is_dark' => $theme['theme'] === 'dark'
        ];
    }
}

// Xiaoman ORM方式：职责清晰分离
class UserSettingController {
    public function getUserTheme($userId) {
        // 1. 查询（Filter负责）
        $filter = new UserSettingFilter($clientId);
        $settings = $filter->byUser($userId)->byKey('theme')->find();

        // 2. 格式化（Formatter负责）
        $formatter = $settings->getFormatter();
        $formatter->themeDisplaySetting();

        // 3. 返回结果
        return $settings->getAttributes();
    }
}
```

**优势**：
- **易于测试**：每个组件可以独立测试
- **易于维护**：修改格式化逻辑只需要改Formatter
- **易于扩展**：添加新功能不影响现有代码

## 进阶技巧：Formatter和Operator

### Formatter：让数据展示更专业

Formatter负责数据的格式化和关联数据的构建。让我们看看如何创建一个专业的Formatter：

```php
<?php
namespace common\library\setting\user;

use xiaoman\orm\common\Formatter;

class UserSettingFormatter extends Formatter
{
    // 定义关联数据的构建规则
    const MAPPING_SETTING = [
        'user_info' => [
            'build_function' => 'buildUserInfo',
            'mapping_function' => 'mappingUserInfo',
        ],
    ];

    /**
     * 基础信息设置 - 只显示核心字段
     */
    public function baseInfoSetting()
    {
        $this->displayFields([
            'id', 'key', 'value', 'update_time'
        ]);
    }

    /**
     * 详细信息设置 - 显示完整信息并加载关联数据
     */
    public function detailInfoSetting()
    {
        $this->baseInfoSetting();
        $this->displayFields(['user_id', 'create_time']);
        $this->displayUserInfo(true); // 启用用户信息关联
    }

    /**
     * 构建用户信息 - 批量加载避免N+1查询
     */
    public function buildUserInfo($key, $data)
    {
        $userIds = array_unique(array_column($data, 'user_id'));

        // 这里可以调用用户服务批量获取用户信息
        $userInfo = [];
        foreach ($userIds as $userId) {
            $userInfo[$userId] = [
                'username' => "user_{$userId}",
                'email' => "user{$userId}@example.com",
            ];
        }

        return $userInfo;
    }

    /**
     * 映射用户信息到每条记录
     */
    public function mappingUserInfo(&$rowResult, $packData)
    {
        $userId = $rowResult['user_id'];
        if (isset($packData['user_info'][$userId])) {
            $userInfo = $packData['user_info'][$userId];
            $rowResult['username'] = $userInfo['username'];
            $rowResult['user_email'] = $userInfo['email'];
        }
    }

    /**
     * 格式化单行数据 - 在每条记录上执行
     */
    protected function formatRow(array $data)
    {
        $result = parent::formatRow($data);

        // 值格式化
        if (isset($result['value'])) {
            $result['formatted_value'] = $this->formatValue($result['value']);
        }

        // 添加计算字段
        $result['is_enabled'] = ($result['enable_flag'] ?? 0) == 1;
        $result['days_since_updated'] = $this->calculateDaysSince($result['update_time'] ?? '');

        return $result;
    }

    private function formatValue($value)
    {
        if (empty($value)) return null;

        // 尝试解析JSON
        $decoded = json_decode($value, true);
        return json_last_error() === JSON_ERROR_NONE ? $decoded : $value;
    }

    private function calculateDaysSince($updateTime)
    {
        if (empty($updateTime)) return 0;
        return floor((time() - strtotime($updateTime)) / 86400);
    }
}
```

**Formatter的设计思路**：
1. **分层设置**：`baseInfoSetting()` 和 `detailInfoSetting()` 提供不同详细程度的输出
2. **关联数据**：通过 `build` 和 `mapping` 函数批量加载关联数据，避免N+1查询
3. **数据增强**：在 `formatRow()` 中添加计算字段和格式化逻辑

### Operator：复杂业务逻辑的家

Operator负责执行复杂的业务操作：

```php
<?php
namespace common\library\setting\user;

use xiaoman\orm\common\Operator;

class UserSettingOperator extends Operator
{
    /**
     * 批量创建用户设置
     */
    public function create($fields = [])
    {
        $data = $this->get($fields ?: ['client_id', 'user_id', 'key', 'value']);
        $time = date('Y-m-d H:i:s');

        // 为每条记录添加时间戳和默认值
        array_walk($data, function (&$item) use ($time) {
            $item['create_time'] = $time;
            $item['update_time'] = $time;
            $item['enable_flag'] = $item['enable_flag'] ?? 1;
        });

        return $this->batchInsert($data);
    }

    /**
     * 用户设置初始化 - 复杂业务逻辑
     */
    public function initializeUserSettings($userId, $template = 'default')
    {
        $defaultSettings = $this->getDefaultTemplate($template);
        $settingsToCreate = [];

        foreach ($defaultSettings as $key => $value) {
            $settingsToCreate[] = [
                'client_id' => $this->clientId,
                'user_id' => $userId,
                'key' => $key,
                'value' => is_array($value) ? json_encode($value) : $value,
                'enable_flag' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
            ];
        }

        return $this->batchInsert($settingsToCreate);
    }

    /**
     * 设置迁移 - 将一个用户的设置复制给另一个用户
     */
    public function migrateSettings($targetUserId)
    {
        $data = $this->get(['key', 'value', 'enable_flag']);
        $migratedSettings = [];

        foreach ($data as $setting) {
            $migratedSettings[] = [
                'client_id' => $this->clientId,
                'user_id' => $targetUserId,
                'key' => $setting['key'],
                'value' => $setting['value'],
                'enable_flag' => $setting['enable_flag'],
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
            ];
        }

        return $this->batchInsert($migratedSettings);
    }

    private function getDefaultTemplate($template)
    {
        $templates = [
            'default' => [
                'theme' => 'light',
                'language' => 'en',
                'timezone' => 'UTC',
            ],
            'admin' => [
                'theme' => 'dark',
                'language' => 'en',
                'admin_panel' => true,
            ],
        ];

        return $templates[$template] ?? $templates['default'];
    }
}
```

**Operator的设计思路**：
1. **业务封装**：将复杂的业务逻辑封装成方法
2. **批量处理**：充分利用批量操作提高性能
3. **数据一致性**：确保相关数据的一致性

## 性能优化实战

### 常见性能问题及解决方案

#### 问题1：N+1查询

**错误做法**：
```php
$filter = new UserSettingFilter($clientId);
$settings = $filter->find()->getAttributes();

foreach ($settings as $setting) {
    // 每次循环都查询数据库 - N+1问题
    $user = new User($clientId, $setting['user_id']);
    echo $user->username;
}
```

**正确做法**：
```php
$filter = new UserSettingFilter($clientId);
$settings = $filter->find();

// 使用Formatter批量加载关联数据
$formatter = $settings->getFormatter();
$formatter->detailInfoSetting(); // 自动批量加载用户信息

$formattedData = $settings->getAttributes();
foreach ($formattedData as $setting) {
    echo $setting['username']; // 已经预加载，无需额外查询
}
```

#### 问题2：查询大字段

**错误做法**：
```php
$filter = new UserSettingFilter($clientId);
// 查询所有字段，包括大的value字段
$settings = $filter->find()->getAttributes();
```

**正确做法**：
```php
$filter = new UserSettingFilter($clientId);
$filter->select(['id', 'key', 'update_time']); // 只选择需要的字段
$settings = $filter->find()->getAttributes();
```

#### 问题3：不合理的查询条件

**错误做法**：
```php
$filter = new UserSettingFilter($clientId);
$filter->rawWhere("value LIKE '%theme%'"); // 在大字段上模糊查询
```

**正确做法**：
```php
$filter = new UserSettingFilter($clientId);
$filter->byKey('theme'); // 在索引字段上精确查询
```

### 缓存策略

```php
class UserSettingFilter extends Filter
{
    /**
     * 带缓存的查询
     */
    public function findWithCache($cacheKey = null, $expire = 300)
    {
        $cacheKey = $cacheKey ?: $this->generateCacheKey();

        $cached = \Yii::app()->cache->get($cacheKey);
        if ($cached !== false) {
            return $this->createBatchObjectFromCache($cached);
        }

        $result = $this->find();
        \Yii::app()->cache->set($cacheKey, $result->getAttributes(), $expire);

        return $result;
    }

    private function generateCacheKey()
    {
        $conditions = [
            'client_id' => $this->clientId,
            'user_id' => $this->user_id,
            'key' => $this->key,
        ];

        return 'user_settings_' . md5(serialize($conditions));
    }
}
```

## 基本CRUD操作

### 1. SingleObject 定义

参考setting模块的实际应用模式：

```php
<?php
namespace common\library\setting\user;

use xiaoman\orm\common\SingleObject;

/**
 * @property int $id
 * @property int $client_id
 * @property int $user_id
 * @property string $key
 * @property string $value
 * @property int $enable_flag
 * @property string $create_time
 * @property string $update_time
 * @method UserSettingOperator getOperator()
 * @method UserSettingFormatter getFormatter()
 */
class UserSetting extends SingleObject
{
    use InitUserSettingMetadata;

    // 常量定义
    const ENABLE_FLAG_ENABLED = 1;
    const ENABLE_FLAG_DISABLED = 0;

    // 配置键常量
    const KEY_THEME = 'theme';
    const KEY_LANGUAGE = 'language';
    const KEY_TIMEZONE = 'timezone';

    public function __construct(int $clientId, int $userId = null, string $key = null)
    {
        parent::__construct($clientId);

        if (!is_null($userId) && !is_null($key)) {
            $this->load([
                'client_id' => $clientId,
                'user_id' => $userId,
                'key' => $key
            ]);
        }
    }

    /**
     * 创建前的钩子
     */
    protected function beforeCreate()
    {
        $this->create_time = date('Y-m-d H:i:s');
        $this->update_time = date('Y-m-d H:i:s');

        if (empty($this->enable_flag)) {
            $this->enable_flag = self::ENABLE_FLAG_ENABLED;
        }

        return parent::beforeCreate();
    }

    /**
     * 更新前的钩子
     */
    protected function beforeUpdate()
    {
        $this->update_time = date('Y-m-d H:i:s');
        return parent::beforeUpdate();
    }

    /**
     * 获取格式化的值
     */
    public function getFormattedValue()
    {
        if (empty($this->value)) {
            return null;
        }

        // 尝试解析JSON
        $decoded = json_decode($this->value, true);
        return json_last_error() === JSON_ERROR_NONE ? $decoded : $this->value;
    }

    /**
     * 设置值（自动JSON编码）
     */
    public function setValue($value)
    {
        if (is_array($value) || is_object($value)) {
            $this->value = json_encode($value, JSON_UNESCAPED_UNICODE);
        } else {
            $this->value = (string)$value;
        }
    }
}
```

### 2. Filter 定义

```php
<?php
namespace common\library\setting\user;

use xiaoman\orm\common\Filter;

/**
 * @property int $id
 * @property int $client_id
 * @property int $user_id
 * @property string $key
 * @property string $value
 * @property int $enable_flag
 * @method BatchUserSetting find()
 */
class UserSettingFilter extends Filter
{
    use InitUserSettingMetadata;

    public function defaultSetting()
    {
        $this->client_id = $this->clientId;
        $this->enable_flag = UserSetting::ENABLE_FLAG_ENABLED;
        parent::defaultSetting();
    }

    /**
     * 按用户查询
     */
    public function byUser($userId)
    {
        $this->user_id = $userId;
        return $this;
    }

    /**
     * 按配置键查询
     */
    public function byKey($key)
    {
        $this->key = $key;
        return $this;
    }

    /**
     * 按多个配置键查询
     */
    public function byKeys(array $keys)
    {
        $this->key = new \xiaoman\orm\database\data\In($keys);
        return $this;
    }

    /**
     * 只查询启用的配置
     */
    public function onlyEnabled()
    {
        $this->enable_flag = UserSetting::ENABLE_FLAG_ENABLED;
        return $this;
    }

    /**
     * 按时间范围查询
     */
    public function byDateRange($startDate, $endDate)
    {
        if ($startDate && $endDate) {
            $this->create_time = new \xiaoman\orm\database\data\Range($startDate, $endDate);
        }
        return $this;
    }
}
```

### 3. BatchObject 定义

```php
<?php
namespace common\library\setting\user;

use xiaoman\orm\common\BatchObject;

class BatchUserSetting extends BatchObject
{
    use InitUserSettingMetadata;

    /**
     * 获取配置键值对映射
     */
    public function getKeyValueMap()
    {
        $data = $this->getAttributes();
        $map = [];

        foreach ($data as $item) {
            $map[$item['key']] = $item['value'];
        }

        return $map;
    }

    /**
     * 获取格式化的配置数据
     */
    public function getFormattedSettings()
    {
        $data = $this->getAttributes();
        $settings = [];

        foreach ($data as $item) {
            $value = $item['value'];

            // 尝试解析JSON
            $decoded = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $value = $decoded;
            }

            $settings[$item['key']] = [
                'value' => $value,
                'raw_value' => $item['value'],
                'user_id' => $item['user_id'],
                'create_time' => $item['create_time'],
                'update_time' => $item['update_time'],
            ];
        }

        return $settings;
    }
}
```

### 4. 基本CRUD操作示例

#### 创建记录
```php
// 创建用户设置
$setting = new UserSetting($clientId);
$setting->user_id = 123;
$setting->key = UserSetting::KEY_THEME;
$setting->setValue(['theme' => 'dark', 'sidebar' => 'collapsed']);
$setting->enable_flag = UserSetting::ENABLE_FLAG_ENABLED;

$result = $setting->create();
if ($result) {
    echo "设置创建成功，ID: " . $setting->id;
}

// 使用构造函数直接加载
$existingSetting = new UserSetting($clientId, 123, UserSetting::KEY_THEME);
if (!$existingSetting->isNew()) {
    echo "当前主题设置: " . $existingSetting->value;
}
```

#### 查询记录
```php
// 单个记录查询
$setting = new UserSetting($clientId, $userId, 'theme');
if (!$setting->isNew()) {
    $themeConfig = $setting->getFormattedValue();
    echo "用户主题: " . $themeConfig['theme'];
}

// 批量查询用户的所有设置
$filter = new UserSettingFilter($clientId);
$filter->byUser($userId)
       ->onlyEnabled()
       ->select(['key', 'value', 'update_time'])
       ->orderBy('update_time', 'desc');

$batchSettings = $filter->find();
$settingsMap = $batchSettings->getKeyValueMap();

// 查询多个用户的特定设置
$filter = new UserSettingFilter($clientId);
$filter->user_id = new \xiaoman\orm\database\data\In([123, 456, 789]);
$filter->byKeys([UserSetting::KEY_THEME, UserSetting::KEY_LANGUAGE]);

$results = $filter->find()->getFormattedSettings();
```

#### 更新记录
```php
// 单个记录更新
$setting = new UserSetting($clientId, $userId, 'theme');
if (!$setting->isNew()) {
    $setting->setValue(['theme' => 'light', 'sidebar' => 'expanded']);
    $setting->update(['value']);
}

// 批量更新 - 禁用某些设置
$filter = new UserSettingFilter($clientId);
$filter->byUser($userId)
       ->byKeys(['old_feature_1', 'old_feature_2']);

$batchSettings = $filter->find();
$operator = $batchSettings->getOperator();
$operator->update(['enable_flag' => UserSetting::ENABLE_FLAG_DISABLED]);

// 条件更新
$filter = new UserSettingFilter($clientId);
$filter->key = 'notification_email';
$filter->value = '<EMAIL>';

$batchSettings = $filter->find();
$operator = $batchSettings->getOperator();
$operator->update(['value' => '<EMAIL>']);
```

#### 删除记录
```php
// 软删除（设置enable_flag为0）
$setting = new UserSetting($clientId, $userId, 'deprecated_setting');
if (!$setting->isNew()) {
    $setting->enable_flag = UserSetting::ENABLE_FLAG_DISABLED;
    $setting->update(['enable_flag']);
}

// 物理删除
$setting = new UserSetting($clientId, $userId, 'temp_setting');
if (!$setting->isNew()) {
    $setting->delete();
}

// 批量删除用户的临时设置
$filter = new UserSettingFilter($clientId);
$filter->byUser($userId);
$filter->rawWhere("key LIKE 'temp_%'");

$batchSettings = $filter->find();
$operator = $batchSettings->getOperator();
$operator->delete();
```

#### 复杂业务操作
```php
// 用户设置迁移示例
class UserSettingMigration
{
    public static function migrateUserSettings($clientId, $fromUserId, $toUserId)
    {
        // 查询源用户的所有设置
        $filter = new UserSettingFilter($clientId);
        $filter->byUser($fromUserId)->onlyEnabled();

        $sourceSettings = $filter->find();
        $settingsData = $sourceSettings->getAttributes();

        // 为目标用户创建设置
        foreach ($settingsData as $settingData) {
            $newSetting = new UserSetting($clientId);
            $newSetting->user_id = $toUserId;
            $newSetting->key = $settingData['key'];
            $newSetting->value = $settingData['value'];
            $newSetting->enable_flag = UserSetting::ENABLE_FLAG_ENABLED;

            try {
                $newSetting->create();
            } catch (\Exception $e) {
                // 处理重复键等异常
                \LogUtil::error("Setting migration failed", [
                    'from_user' => $fromUserId,
                    'to_user' => $toUserId,
                    'key' => $settingData['key'],
                    'error' => $e->getMessage()
                ]);
            }
        }
    }
}
```

## 高级查询

### 1. 复杂条件查询

参考setting模块的查询模式：

```php
use xiaoman\orm\database\data\Range;
use xiaoman\orm\database\data\In;
use xiaoman\orm\database\data\Like;
use xiaoman\orm\database\data\Equal;

$filter = new UserSettingFilter($clientId);

// 范围查询 - 查询ID范围
$filter->id = new Range(100, 200);

// IN查询 - 查询多个用户的设置
$filter->user_id = new In([123, 456, 789]);

// 精确匹配
$filter->key = new Equal('theme');

// 模糊查询 - 查询所有通知相关设置
$filter->rawWhere("key LIKE 'notification_%'");

// 时间范围查询
$filter->create_time = new Range('2024-01-01', '2024-12-31');

// JSON字段查询（PostgreSQL）
$filter->rawWhere("value::jsonb @> ?", ['{"theme": "dark"}']);

// 多条件组合
$filter->wheres([
    'enable_flag' => UserSetting::ENABLE_FLAG_ENABLED,
    'user_id' => new In([123, 456]),
    'key' => new Like('feature_%')
]);

$results = $filter->find();
```

### 2. 条件分组和复杂逻辑

```php
$filter = new UserSettingFilter($clientId);

// WHERE (key = 'theme' OR key = 'language') AND enable_flag = 1
$filter->where(function($query) {
    $query->where('key', new Equal('theme'))
          ->orWhere('key', new Equal('language'));
})
->where('enable_flag', new Equal(UserSetting::ENABLE_FLAG_ENABLED));

// 复杂的业务逻辑查询
$filter->where(function($query) {
    // 查询主题设置或语言设置
    $query->where('key', new Equal('theme'))
          ->orWhere('key', new Equal('language'));
})
->where(function($query) {
    // 并且是最近30天更新的
    $thirtyDaysAgo = date('Y-m-d H:i:s', strtotime('-30 days'));
    $query->where('update_time', '>', $thirtyDaysAgo);
});

$recentSettings = $filter->find();
```

### 3. 排序和分页

```php
$filter = new UserSettingFilter($clientId);
$filter->byUser($userId);

// 多字段排序
$filter->orderBy('key', 'asc')
       ->orderBy('update_time', 'desc');

// 分页查询
$filter->limit(20)->offset(40); // 第3页，每页20条

// 或使用page方法
$filter->page(3, 20); // 第3页，每页20条

$results = $filter->find();
$totalCount = $filter->count(); // 获取总数
```

### 4. 字段选择和性能优化

```php
$filter = new UserSettingFilter($clientId);
$filter->byUser($userId);

// 只选择需要的字段
$filter->select(['key', 'value', 'update_time']);

// 排除大字段
$filter->selectExcept(['create_time', 'enable_flag']);

// 聚合查询
$filter->selectRaw('COUNT(*) as setting_count, MAX(update_time) as last_update');
$filter->groupBy('user_id');

$stats = $filter->find();
```

### 5. 子查询和关联查询

```php
// 子查询示例 - 查询有特定设置的用户
$subQuery = new UserSettingFilter($clientId);
$subQuery->key = 'premium_feature';
$subQuery->value = '1';
$subQuery->select(['user_id']);

$mainFilter = new UserSettingFilter($clientId);
$mainFilter->user_id = new In($subQuery->getQuery());

$premiumUserSettings = $mainFilter->find();

// 使用EXISTS子查询
$filter = new UserSettingFilter($clientId);
$filter->rawWhere("EXISTS (
    SELECT 1 FROM tbl_user_setting us2
    WHERE us2.user_id = tbl_user_setting.user_id
    AND us2.key = 'vip_status'
    AND us2.value = '1'
)");

$vipUserSettings = $filter->find();
```

### 6. 原生SQL和复杂查询

```php
$filter = new UserSettingFilter($clientId);

// PostgreSQL数组操作
$filter->rawWhere("value::jsonb ? 'notifications'"); // JSON包含键
$filter->rawWhere("value::jsonb @> ?", ['{"enabled": true}']); // JSON包含值

// 全文搜索
$filter->rawWhere("to_tsvector('english', value) @@ plainto_tsquery('english', ?)", ['search term']);

// 窗口函数
$filter->selectRaw("
    key, value,
    ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY update_time DESC) as rn
");
$filter->rawWhere("rn = 1"); // 每个用户最新的设置

// CTE（公共表表达式）
$filter->rawWhere("
    WITH recent_settings AS (
        SELECT user_id, key, value,
               ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY update_time DESC) as rn
        FROM tbl_user_setting
        WHERE client_id = ?
    )
    SELECT * FROM recent_settings WHERE rn <= 5
", [$clientId]);
```

### 7. 动态查询构建

```php
class UserSettingFilter extends Filter
{
    /**
     * 按配置类型查询
     */
    public function byConfigType($type)
    {
        switch ($type) {
            case 'ui':
                $this->byKeys(['theme', 'language', 'timezone']);
                break;
            case 'notification':
                $this->rawWhere("key LIKE 'notification_%'");
                break;
            case 'feature':
                $this->rawWhere("key LIKE 'feature_%'");
                break;
        }
        return $this;
    }

    /**
     * 按值类型查询
     */
    public function byValueType($valueType)
    {
        switch ($valueType) {
            case 'json':
                $this->rawWhere("value::text ~ '^[\\[\\{]'"); // 以[或{开头
                break;
            case 'boolean':
                $this->value = new In(['0', '1', 'true', 'false']);
                break;
            case 'numeric':
                $this->rawWhere("value ~ '^[0-9]+$'");
                break;
        }
        return $this;
    }

    /**
     * 活跃用户设置查询
     */
    public function activeUserSettings($days = 30)
    {
        $since = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        $this->update_time = new Range($since, date('Y-m-d H:i:s'));
        $this->onlyEnabled();
        return $this;
    }
}

// 使用示例
$filter = new UserSettingFilter($clientId);
$filter->byConfigType('ui')
       ->activeUserSettings(7) // 最近7天活跃
       ->orderBy('update_time', 'desc');

$recentUiSettings = $filter->find();
```

## 数据格式化

### 1. Formatter 基础概念

Formatter负责数据的展示格式化和字段映射，参考setting模块的实际应用：

```php
<?php
namespace common\library\setting\user;

use xiaoman\orm\common\Formatter;

class UserSettingFormatter extends Formatter
{
    // 映射设置定义
    const MAPPING_SETTING = [
        'user_info' => [
            'build_function' => 'buildUserInfo',
            'mapping_function' => 'mappingUserInfo',
        ],
        'config_details' => [
            'build_function' => 'buildConfigDetails',
            'mapping_function' => 'mappingConfigDetails',
        ],
    ];

    // API输出字段
    protected $apiFields = [
        'id',
        'user_id',
        'key',
        'value',
        'enable_flag',
        'create_time',
        'update_time',
    ];

    /**
     * 基础信息设置
     */
    public function baseInfoSetting()
    {
        $this->displayFields([
            'id',
            'key',
            'value',
            'enable_flag',
            'update_time',
        ]);
    }

    /**
     * 详细信息设置
     */
    public function detailInfoSetting()
    {
        $this->baseInfoSetting();
        $this->displayFields([
            'user_id',
            'create_time',
        ]);

        // 启用关联数据
        $this->displayUserInfo(true);
        $this->displayConfigDetails(true);
    }

    /**
     * 构建用户信息
     */
    public function buildUserInfo($key, $data)
    {
        $userIds = array_unique(array_column($data, 'user_id'));

        // 这里可以调用用户服务获取用户信息
        $userInfo = [];
        foreach ($userIds as $userId) {
            $userInfo[$userId] = [
                'user_id' => $userId,
                'username' => "user_{$userId}",
                'email' => "user{$userId}@example.com",
            ];
        }

        return $userInfo;
    }

    /**
     * 映射用户信息
     */
    public function mappingUserInfo(&$rowResult, $packData)
    {
        $userId = $rowResult['user_id'];
        if (isset($packData['user_info'][$userId])) {
            $userInfo = $packData['user_info'][$userId];
            $rowResult['username'] = $userInfo['username'];
            $rowResult['user_email'] = $userInfo['email'];
        }
    }

    /**
     * 构建配置详情
     */
    public function buildConfigDetails($key, $data)
    {
        $configKeys = array_unique(array_column($data, 'key'));

        // 获取配置的详细描述
        $configDetails = [];
        foreach ($configKeys as $configKey) {
            $configDetails[$configKey] = [
                'key' => $configKey,
                'description' => $this->getConfigDescription($configKey),
                'category' => $this->getConfigCategory($configKey),
                'data_type' => $this->getConfigDataType($configKey),
            ];
        }

        return $configDetails;
    }

    /**
     * 映射配置详情
     */
    public function mappingConfigDetails(&$rowResult, $packData)
    {
        $key = $rowResult['key'];
        if (isset($packData['config_details'][$key])) {
            $details = $packData['config_details'][$key];
            $rowResult['config_description'] = $details['description'];
            $rowResult['config_category'] = $details['category'];
            $rowResult['config_data_type'] = $details['data_type'];
        }
    }

    /**
     * 格式化单行数据
     */
    protected function formatRow(array $data)
    {
        $result = parent::formatRow($data);

        // 值格式化
        if (isset($result['value'])) {
            $result['formatted_value'] = $this->formatValue($result['value'], $result['key'] ?? '');
        }

        // 时间格式化
        if (isset($result['create_time'])) {
            $result['create_time_formatted'] = date('Y-m-d H:i:s', strtotime($result['create_time']));
        }

        // 状态标识
        $result['is_enabled'] = ($result['enable_flag'] ?? 0) == UserSetting::ENABLE_FLAG_ENABLED;

        return $result;
    }

    /**
     * 格式化配置值
     */
    private function formatValue($value, $key)
    {
        if (empty($value)) {
            return null;
        }

        // 尝试解析JSON
        $decoded = json_decode($value, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            return $decoded;
        }

        // 根据配置键进行特殊格式化
        switch ($key) {
            case 'theme':
                return ['theme' => $value, 'display_name' => ucfirst($value)];
            case 'language':
                return ['code' => $value, 'name' => $this->getLanguageName($value)];
            default:
                return $value;
        }
    }

    private function getConfigDescription($key)
    {
        $descriptions = [
            'theme' => '用户界面主题设置',
            'language' => '用户语言偏好',
            'timezone' => '用户时区设置',
            'notification_email' => '邮件通知设置',
        ];

        return $descriptions[$key] ?? '未知配置';
    }

    private function getConfigCategory($key)
    {
        if (strpos($key, 'notification_') === 0) {
            return 'notification';
        }

        $uiKeys = ['theme', 'language', 'timezone'];
        if (in_array($key, $uiKeys)) {
            return 'ui';
        }

        return 'general';
    }

    private function getConfigDataType($key)
    {
        $jsonKeys = ['theme', 'notification_settings'];
        if (in_array($key, $jsonKeys)) {
            return 'json';
        }

        return 'string';
    }

    private function getLanguageName($code)
    {
        $languages = [
            'en' => 'English',
            'zh' => '中文',
            'ja' => '日本語',
        ];

        return $languages[$code] ?? $code;
    }
}
```

### 2. 数据格式化的高级技巧

#### 条件格式化
```php
class UserSettingFormatter extends Formatter
{
    /**
     * 格式化单行数据 - 支持条件格式化
     */
    protected function formatRow(array $data)
    {
        $result = parent::formatRow($data);

        // 根据配置类型进行不同的格式化
        $key = $result['key'] ?? '';

        switch (true) {
            case strpos($key, 'theme') !== false:
                $result = $this->formatThemeConfig($result);
                break;
            case strpos($key, 'notification_') === 0:
                $result = $this->formatNotificationConfig($result);
                break;
            case strpos($key, 'feature_') === 0:
                $result = $this->formatFeatureConfig($result);
                break;
        }

        // 添加通用计算字段
        $result['days_since_updated'] = $this->calculateDaysSince($result['update_time'] ?? '');
        $result['is_system_config'] = $this->isSystemConfig($key);
        $result['config_category'] = $this->getConfigCategory($key);

        return $result;
    }

    private function formatThemeConfig(array $data)
    {
        $value = $data['value'] ?? '';
        $themeData = json_decode($value, true) ?: ['theme' => $value];

        $data['theme_info'] = [
            'theme' => $themeData['theme'] ?? 'light',
            'display_name' => ucfirst($themeData['theme'] ?? 'light'),
            'is_dark_mode' => ($themeData['theme'] ?? 'light') === 'dark'
        ];

        return $data;
    }

    private function formatNotificationConfig(array $data)
    {
        $value = $data['value'] ?? '';
        $notificationData = json_decode($value, true) ?: [];

        $data['notification_info'] = [
            'enabled' => $notificationData['enabled'] ?? true,
            'channels' => $notificationData['channels'] ?? ['email'],
            'frequency' => $notificationData['frequency'] ?? 'immediate'
        ];

        return $data;
    }

    private function formatFeatureConfig(array $data)
    {
        $value = $data['value'] ?? '';
        $enabled = in_array(strtolower($value), ['1', 'true', 'enabled', 'on']);

        $data['feature_info'] = [
            'enabled' => $enabled,
            'status_text' => $enabled ? '启用' : '禁用',
            'css_class' => $enabled ? 'feature-enabled' : 'feature-disabled'
        ];

        return $data;
    }
}
```

#### 批量数据后处理
```php
class UserSettingFormatter extends Formatter
{
    /**
     * 批量数据处理完成后的统计和排序
     */
    public function getAttributes()
    {
        $data = parent::getAttributes();

        // 添加统计信息
        $stats = $this->calculateStatistics($data);

        // 按业务逻辑排序
        $sortedData = $this->sortByBusinessLogic($data);

        return [
            'data' => $sortedData,
            'statistics' => $stats,
            'meta' => [
                'total_count' => count($sortedData),
                'generated_at' => date('Y-m-d H:i:s'),
            ]
        ];
    }

    private function calculateStatistics(array $data)
    {
        $stats = [
            'by_category' => [],
            'by_status' => ['enabled' => 0, 'disabled' => 0],
            'recent_updates' => 0
        ];

        $recentThreshold = strtotime('-7 days');

        foreach ($data as $item) {
            // 按类别统计
            $category = $item['config_category'] ?? 'unknown';
            $stats['by_category'][$category] = ($stats['by_category'][$category] ?? 0) + 1;

            // 按状态统计
            $enabled = ($item['enable_flag'] ?? 0) == 1;
            $stats['by_status'][$enabled ? 'enabled' : 'disabled']++;

            // 最近更新统计
            $updateTime = strtotime($item['update_time'] ?? '');
            if ($updateTime > $recentThreshold) {
                $stats['recent_updates']++;
            }
        }

        return $stats;
    }

    private function sortByBusinessLogic(array $data)
    {
        // 按优先级排序：系统配置 > 用户配置，然后按更新时间倒序
        usort($data, function($a, $b) {
            $aIsSystem = $this->isSystemConfig($a['key'] ?? '');
            $bIsSystem = $this->isSystemConfig($b['key'] ?? '');

            if ($aIsSystem !== $bIsSystem) {
                return $bIsSystem <=> $aIsSystem; // 系统配置优先
            }

            return ($b['update_time'] ?? '') <=> ($a['update_time'] ?? '');
        });

        return $data;
    }
}
```

### 3. Formatter中的管道处理

Formatter支持管道式数据处理，通过`MAPPING_SETTING`配置和管道机制实现：

```php
<?php
namespace common\library\setting\user;

use xiaoman\orm\common\Formatter;

class UserSettingFormatter extends Formatter
{
    // 定义映射设置 - 这是Formatter管道处理的核心
    const MAPPING_SETTING = [
        'user_info' => [
            'build_function' => 'buildUserInfo',        // 数据构建函数
            'mapping_function' => 'mappingUserInfo',     // 数据映射函数
        ],
        'config_details' => [
            'build_function' => 'buildConfigDetails',
            'mapping_function' => 'mappingConfigDetails',
        ],
        'validation_status' => [
            'build_function' => 'buildValidationStatus',
            'mapping_function' => 'mappingValidationStatus',
        ],
    ];

    /**
     * 详细信息设置 - 启用管道处理
     */
    public function detailInfoSetting()
    {
        $this->baseInfoSetting();
        $this->displayFields(['user_id', 'create_time']);

        // 启用关联数据处理
        $this->displayUserInfo(true);
        $this->displayConfigDetails(true);
        $this->displayValidationStatus(true);
    }

    /**
     * 构建用户信息 - build_function示例
     */
    public function buildUserInfo($key, $data)
    {
        // $key = 'user_info'
        // $data = 完整的数据数组

        $userIds = array_unique(array_column($data, 'user_id'));

        if (empty($userIds)) {
            return [];
        }

        // 批量查询用户信息，避免N+1问题
        $userInfo = [];
        foreach ($userIds as $userId) {
            // 这里可以调用用户服务获取用户信息
            $userInfo[$userId] = [
                'user_id' => $userId,
                'username' => "user_{$userId}",
                'email' => "user{$userId}@example.com",
                'avatar' => "/avatars/default.png",
                'status' => 'active'
            ];
        }

        return $userInfo;
    }

    /**
     * 映射用户信息 - mapping_function示例
     */
    public function mappingUserInfo(&$rowResult, $packData)
    {
        // $rowResult = 当前行的结果数组（引用传递）
        // $packData = buildUserInfo返回的数据

        $userId = $rowResult['user_id'];
        if (isset($packData['user_info'][$userId])) {
            $userInfo = $packData['user_info'][$userId];
            $rowResult['username'] = $userInfo['username'];
            $rowResult['user_email'] = $userInfo['email'];
            $rowResult['user_avatar'] = $userInfo['avatar'];
            $rowResult['user_status'] = $userInfo['status'];
            $rowResult['user_display_name'] = $userInfo['username'] ?: "用户{$userId}";
        } else {
            $rowResult['username'] = "未知用户";
            $rowResult['user_email'] = '';
            $rowResult['user_avatar'] = '/avatars/default.png';
            $rowResult['user_status'] = 'unknown';
            $rowResult['user_display_name'] = "用户{$userId}";
        }
    }

    /**
     * 构建配置详情
     */
    public function buildConfigDetails($key, $data)
    {
        $configKeys = array_unique(array_column($data, 'key'));

        // 获取配置的元信息
        $configMeta = [
            'theme' => [
                'category' => 'ui',
                'description' => '界面主题设置',
                'data_type' => 'json',
                'default_value' => 'light',
                'validation_rules' => ['theme' => 'required|in:light,dark,auto']
            ],
            'language' => [
                'category' => 'ui',
                'description' => '语言偏好设置',
                'data_type' => 'string',
                'default_value' => 'en',
                'validation_rules' => ['language' => 'required|in:en,zh,ja']
            ],
            'notification_email' => [
                'category' => 'notification',
                'description' => '邮件通知设置',
                'data_type' => 'boolean',
                'default_value' => true,
                'validation_rules' => ['enabled' => 'boolean']
            ],
        ];

        $result = [];
        foreach ($configKeys as $configKey) {
            $result[$configKey] = $configMeta[$configKey] ?? [
                'category' => 'general',
                'description' => '未知配置项',
                'data_type' => 'string',
                'default_value' => '',
                'validation_rules' => []
            ];
        }

        return $result;
    }

    /**
     * 映射配置详情
     */
    public function mappingConfigDetails(&$rowResult, $packData)
    {
        $configKey = $rowResult['key'];
        $configMeta = $packData['config_details'][$configKey] ?? [];

        $rowResult['config_category'] = $configMeta['category'] ?? 'general';
        $rowResult['config_description'] = $configMeta['description'] ?? '未知配置';
        $rowResult['config_data_type'] = $configMeta['data_type'] ?? 'string';
        $rowResult['config_default_value'] = $configMeta['default_value'] ?? '';
        $rowResult['validation_rules'] = $configMeta['validation_rules'] ?? [];

        // 添加业务逻辑
        $rowResult['is_system_config'] = in_array($configKey, ['system_version', 'maintenance_mode']);
        $rowResult['is_user_customizable'] = !$rowResult['is_system_config'];
        $rowResult['has_default_value'] = !empty($configMeta['default_value']);
    }

    /**
     * 构建验证状态
     */
    public function buildValidationStatus($key, $data)
    {
        $validationResults = [];

        foreach ($data as $setting) {
            $settingKey = $setting['key'];
            $value = $setting['value'];

            $isValid = $this->validateSettingValue($settingKey, $value);
            $validationResults[$setting['id']] = [
                'is_valid' => $isValid,
                'validation_message' => $isValid ? '验证通过' : '验证失败',
                'validation_time' => date('Y-m-d H:i:s')
            ];
        }

        return $validationResults;
    }

    /**
     * 映射验证状态
     */
    public function mappingValidationStatus(&$rowResult, $packData)
    {
        $settingId = $rowResult['id'];
        $validationData = $packData['validation_status'][$settingId] ?? [];

        $rowResult['is_valid'] = $validationData['is_valid'] ?? true;
        $rowResult['validation_message'] = $validationData['validation_message'] ?? '';
        $rowResult['validation_time'] = $validationData['validation_time'] ?? '';
        $rowResult['validation_status_class'] = $rowResult['is_valid'] ? 'valid' : 'invalid';
    }

    private function validateSettingValue($key, $value)
    {
        switch ($key) {
            case 'theme':
                return in_array($value, ['light', 'dark', 'auto']);
            case 'language':
                return in_array($value, ['en', 'zh', 'ja']);
            case 'notification_email':
                return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
            default:
                return true;
        }
    }
}
```

### 4. preparePipelineTask的高级用法

```php
class UserSettingFormatter extends Formatter
{
    /**
     * 自定义管道数据准备
     */
    public function customFormatting(array $data)
    {
        // 1. 手动设置管道任务
        $customTasks = [
            'performance_metrics' => [
                'build_function' => 'buildPerformanceMetrics',
                'mapping_function' => 'mappingPerformanceMetrics',
            ],
            'usage_statistics' => [
                'build_function' => 'buildUsageStatistics',
                'mapping_function' => 'mappingUsageStatistics',
            ]
        ];

        // 2. 使用preparePipelineTask准备数据
        $this->preparePipelineTask($data, $customTasks);

        // 3. 执行格式化
        return $this->result($data);
    }

    /**
     * 构建性能指标
     */
    public function buildPerformanceMetrics($key, $data)
    {
        $userIds = array_unique(array_column($data, 'user_id'));
        $metrics = [];

        foreach ($userIds as $userId) {
            $userSettings = array_filter($data, function($item) use ($userId) {
                return $item['user_id'] == $userId;
            });

            $totalSettings = count($userSettings);
            $activeSettings = count(array_filter($userSettings, function($item) {
                return $item['enable_flag'] == 1;
            }));

            $lastUpdateTimes = array_column($userSettings, 'update_time');
            $lastUpdate = $lastUpdateTimes ? max($lastUpdateTimes) : '';

            $metrics[$userId] = [
                'total_settings' => $totalSettings,
                'active_settings' => $activeSettings,
                'last_update' => $lastUpdate,
                'activity_score' => $this->calculateActivityScore($totalSettings, $activeSettings, $lastUpdate),
                'settings_categories' => $this->getSettingsCategories($userSettings)
            ];
        }

        return $metrics;
    }

    /**
     * 映射性能指标
     */
    public function mappingPerformanceMetrics(&$rowResult, $packData)
    {
        $userId = $rowResult['user_id'];
        $metrics = $packData['performance_metrics'][$userId] ?? [];

        $rowResult['user_total_settings'] = $metrics['total_settings'] ?? 0;
        $rowResult['user_active_settings'] = $metrics['active_settings'] ?? 0;
        $rowResult['user_last_update'] = $metrics['last_update'] ?? '';
        $rowResult['user_activity_score'] = $metrics['activity_score'] ?? 0;
        $rowResult['user_settings_categories'] = $metrics['settings_categories'] ?? [];

        // 添加计算字段
        $rowResult['user_activity_level'] = $this->getActivityLevel($metrics['activity_score'] ?? 0);
        $rowResult['days_since_last_update'] = $this->calculateDaysSince($metrics['last_update'] ?? '');
    }

    /**
     * 构建使用统计
     */
    public function buildUsageStatistics($key, $data)
    {
        $statistics = [
            'total_users' => count(array_unique(array_column($data, 'user_id'))),
            'total_settings' => count($data),
            'settings_by_category' => [],
            'most_popular_settings' => [],
            'recent_activity' => []
        ];

        // 按类别统计
        foreach ($data as $setting) {
            $category = $this->getSettingCategory($setting['key']);
            $statistics['settings_by_category'][$category] =
                ($statistics['settings_by_category'][$category] ?? 0) + 1;
        }

        // 最受欢迎的设置
        $keyCount = array_count_values(array_column($data, 'key'));
        arsort($keyCount);
        $statistics['most_popular_settings'] = array_slice($keyCount, 0, 10, true);

        // 最近活动
        $recentThreshold = strtotime('-7 days');
        $statistics['recent_activity'] = array_filter($data, function($setting) use ($recentThreshold) {
            return strtotime($setting['update_time']) > $recentThreshold;
        });

        return $statistics;
    }

    /**
     * 映射使用统计
     */
    public function mappingUsageStatistics(&$rowResult, $packData)
    {
        $stats = $packData['usage_statistics'];

        // 添加全局统计信息
        $rowResult['global_total_users'] = $stats['total_users'];
        $rowResult['global_total_settings'] = $stats['total_settings'];
        $rowResult['global_recent_activity_count'] = count($stats['recent_activity']);

        // 当前设置的受欢迎程度
        $currentKey = $rowResult['key'];
        $rowResult['setting_popularity_rank'] = array_search($currentKey, array_keys($stats['most_popular_settings'])) + 1;
        $rowResult['setting_usage_count'] = $stats['most_popular_settings'][$currentKey] ?? 0;

        // 类别统计
        $category = $this->getSettingCategory($currentKey);
        $rowResult['category_total_count'] = $stats['settings_by_category'][$category] ?? 0;
    }

    private function calculateActivityScore($totalSettings, $activeSettings, $lastUpdate)
    {
        $score = 0;

        // 基于设置数量的分数 (0-40分)
        $score += min($totalSettings * 4, 40);

        // 基于活跃设置比例的分数 (0-30分)
        if ($totalSettings > 0) {
            $score += ($activeSettings / $totalSettings) * 30;
        }

        // 基于最近更新时间的分数 (0-30分)
        if ($lastUpdate) {
            $daysSinceUpdate = (time() - strtotime($lastUpdate)) / 86400;
            $score += max(30 - $daysSinceUpdate, 0);
        }

        return round($score, 2);
    }

    private function getActivityLevel($score)
    {
        if ($score >= 80) return 'high';
        if ($score >= 50) return 'medium';
        if ($score >= 20) return 'low';
        return 'inactive';
    }

    private function getSettingsCategories($userSettings)
    {
        $categories = [];
        foreach ($userSettings as $setting) {
            $category = $this->getSettingCategory($setting['key']);
            $categories[$category] = ($categories[$category] ?? 0) + 1;
        }
        return $categories;
    }

    private function getSettingCategory($key)
    {
        if (strpos($key, 'notification_') === 0) return 'notification';
        if (in_array($key, ['theme', 'language', 'timezone'])) return 'ui';
        if (strpos($key, 'feature_') === 0) return 'feature';
        if (strpos($key, 'system_') === 0) return 'system';
        return 'general';
    }

    private function calculateDaysSince($dateTime)
    {
        if (empty($dateTime)) return 0;
        return floor((time() - strtotime($dateTime)) / 86400);
    }
}
```

**Formatter管道处理的关键要点**：

1. **MAPPING_SETTING**: 定义数据处理规则的核心配置
2. **build_function**: 批量构建关联数据，在所有数据处理前执行一次
3. **mapping_function**: 将构建的数据映射到每行结果，对每行数据执行
4. **preparePipelineTask**: 手动准备管道数据，支持自定义处理流程
5. **数据流向**: 原始数据 → build阶段 → mapping阶段 → 最终结果
6. **性能优化**: 通过批量构建避免N+1查询问题

这种设计让Formatter既能高效处理大量数据，又能保持代码的清晰和可维护性。

## 批量操作

### 1. Operator 基础概念

Operator负责执行复杂的业务操作，参考setting模块的实际应用：

```php
<?php
namespace common\library\setting\user;

use xiaoman\orm\common\Operator;

class UserSettingOperator extends Operator
{
    const TASK_LIST = [
        'validate_settings' => [
            'method' => 'validateSettings',
        ],
        'sync_cache' => [
            'method' => 'syncToCache',
        ],
    ];

    /**
     * 批量创建设置
     */
    public function create($fields = [])
    {
        if (empty($fields)) {
            $fields = [
                'client_id', 'user_id', 'key', 'value', 'enable_flag'
            ];
        }

        $data = $this->get($fields);
        $time = date('Y-m-d H:i:s');

        // 为每条记录添加时间戳
        array_walk($data, function (&$item) use ($time) {
            $item['create_time'] = $time;
            $item['update_time'] = $time;

            if (!isset($item['enable_flag'])) {
                $item['enable_flag'] = UserSetting::ENABLE_FLAG_ENABLED;
            }
        });

        return $this->batchInsert($data);
    }

    /**
     * 批量更新设置
     */
    public function update(array $fields)
    {
        $fields['update_time'] = date('Y-m-d H:i:s');
        return $this->execute($fields);
    }

    /**
     * 批量设置值更新
     */
    public function updateValues(array $keyValuePairs)
    {
        $data = $this->get(['id', 'key']);
        $updateData = [];

        foreach ($data as $item) {
            $key = $item['key'];
            if (isset($keyValuePairs[$key])) {
                $value = $keyValuePairs[$key];

                // 自动JSON编码
                if (is_array($value) || is_object($value)) {
                    $value = json_encode($value, JSON_UNESCAPED_UNICODE);
                }

                $updateData[$item['id']] = [
                    'value' => $value,
                    'update_time' => date('Y-m-d H:i:s')
                ];
            }
        }

        return $this->batchUpdate($updateData);
    }

    /**
     * 自定义批量更新 - 支持不同记录更新不同字段
     */
    public function batchUpdate(array $data)
    {
        $updateSqlArr = [];
        $table = $this->object->getMetadata()::table();
        $columns = $this->object->getMetadata()->getColumnsKeys();
        $params = [];

        foreach ($data as $settingId => $setting) {
            $validColumns = array_intersect(array_keys($setting), $columns);
            $updateSql = "UPDATE {$table} SET ";
            $updateFields = [];

            foreach ($validColumns as $column) {
                if ($column == 'id') continue;

                $updateFields[] = "{$column} = :{$column}_{$settingId}";
                $params[":{$column}_{$settingId}"] = $setting[$column];
            }

            $updateSql .= implode(',', $updateFields) . " WHERE id = :id_{$settingId}";
            $params[":id_{$settingId}"] = $settingId;
            $updateSqlArr[] = $updateSql;
        }

        $repository = $this->getRepository();
        return $repository->execute(implode(";", $updateSqlArr), $params);
    }

    /**
     * 批量启用/禁用设置
     */
    public function toggleSettings($enable = true)
    {
        $enableFlag = $enable ? UserSetting::ENABLE_FLAG_ENABLED : UserSetting::ENABLE_FLAG_DISABLED;

        return $this->update([
            'enable_flag' => $enableFlag,
            'update_time' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 用户设置迁移
     */
    public function migrateToUser($targetUserId)
    {
        $data = $this->get(['key', 'value', 'enable_flag']);
        $migratedSettings = [];

        foreach ($data as $setting) {
            $migratedSettings[] = [
                'client_id' => $this->clientId,
                'user_id' => $targetUserId,
                'key' => $setting['key'],
                'value' => $setting['value'],
                'enable_flag' => $setting['enable_flag'],
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
            ];
        }

        return $this->batchInsert($migratedSettings);
    }

    /**
     * 设置验证
     */
    public function validateSettings()
    {
        $data = $this->get(['id', 'key', 'value']);
        $validationErrors = [];

        foreach ($data as $setting) {
            $errors = $this->validateSingleSetting($setting);
            if (!empty($errors)) {
                $validationErrors[$setting['id']] = $errors;
            }
        }

        return $validationErrors;
    }

    /**
     * 同步到缓存
     */
    public function syncToCache()
    {
        $data = $this->get(['user_id', 'key', 'value']);
        $userSettings = [];

        // 按用户分组
        foreach ($data as $setting) {
            $userId = $setting['user_id'];
            $userSettings[$userId][$setting['key']] = $setting['value'];
        }

        // 批量写入缓存
        foreach ($userSettings as $userId => $settings) {
            $cacheKey = "user_settings:{$this->clientId}:{$userId}";
            \Yii::app()->cache->set($cacheKey, $settings, 3600); // 1小时过期
        }

        return count($userSettings);
    }

    private function validateSingleSetting($setting)
    {
        $errors = [];
        $key = $setting['key'];
        $value = $setting['value'];

        // 基础验证
        if (empty($key)) {
            $errors[] = 'Key cannot be empty';
        }

        // 特定键的验证
        switch ($key) {
            case 'theme':
                $allowedThemes = ['light', 'dark', 'auto'];
                if (!in_array($value, $allowedThemes)) {
                    $errors[] = 'Invalid theme value';
                }
                break;

            case 'language':
                $allowedLanguages = ['en', 'zh', 'ja'];
                if (!in_array($value, $allowedLanguages)) {
                    $errors[] = 'Invalid language value';
                }
                break;

            case 'notification_email':
                if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    $errors[] = 'Invalid email format';
                }
                break;
        }

        // JSON格式验证
        if (strpos($value, '{') === 0 || strpos($value, '[') === 0) {
            json_decode($value);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $errors[] = 'Invalid JSON format';
            }
        }

        return $errors;
    }
}
```

### 2. 复杂业务操作

```php
class UserSettingOperator extends Operator
{
    /**
     * 用户设置初始化
     */
    public function initializeUserSettings($userId, $template = 'default')
    {
        $defaultSettings = $this->getDefaultSettingsTemplate($template);
        $settingsToCreate = [];

        foreach ($defaultSettings as $key => $value) {
            $settingsToCreate[] = [
                'client_id' => $this->clientId,
                'user_id' => $userId,
                'key' => $key,
                'value' => is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value,
                'enable_flag' => UserSetting::ENABLE_FLAG_ENABLED,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
            ];
        }

        return $this->batchInsert($settingsToCreate);
    }

    /**
     * 设置清理 - 删除过期或无效的设置
     */
    public function cleanupSettings($daysOld = 90)
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysOld} days"));

        // 查找需要清理的设置
        $filter = new UserSettingFilter($this->clientId);
        $filter->enable_flag = UserSetting::ENABLE_FLAG_DISABLED;
        $filter->rawWhere("update_time < ?", [$cutoffDate]);

        $settingsToClean = $filter->find();
        $operator = $settingsToClean->getOperator();

        return $operator->delete();
    }

    /**
     * 设置统计分析
     */
    public function analyzeSettings()
    {
        $data = $this->get(['key', 'value', 'enable_flag', 'user_id']);

        $stats = [
            'total_settings' => count($data),
            'enabled_settings' => 0,
            'disabled_settings' => 0,
            'key_distribution' => [],
            'user_distribution' => [],
            'value_types' => [
                'json' => 0,
                'string' => 0,
                'numeric' => 0,
                'boolean' => 0,
            ],
        ];

        foreach ($data as $setting) {
            // 启用状态统计
            if ($setting['enable_flag'] == UserSetting::ENABLE_FLAG_ENABLED) {
                $stats['enabled_settings']++;
            } else {
                $stats['disabled_settings']++;
            }

            // 键分布统计
            $key = $setting['key'];
            $stats['key_distribution'][$key] = ($stats['key_distribution'][$key] ?? 0) + 1;

            // 用户分布统计
            $userId = $setting['user_id'];
            $stats['user_distribution'][$userId] = ($stats['user_distribution'][$userId] ?? 0) + 1;

            // 值类型统计
            $value = $setting['value'];
            if (is_numeric($value)) {
                $stats['value_types']['numeric']++;
            } elseif (in_array(strtolower($value), ['true', 'false', '0', '1'])) {
                $stats['value_types']['boolean']++;
            } elseif (strpos($value, '{') === 0 || strpos($value, '[') === 0) {
                $stats['value_types']['json']++;
            } else {
                $stats['value_types']['string']++;
            }
        }

        return $stats;
    }

    private function getDefaultSettingsTemplate($template)
    {
        $templates = [
            'default' => [
                'theme' => 'light',
                'language' => 'en',
                'timezone' => 'UTC',
                'notification_email' => true,
                'notification_sms' => false,
            ],
            'admin' => [
                'theme' => 'dark',
                'language' => 'en',
                'timezone' => 'UTC',
                'notification_email' => true,
                'notification_sms' => true,
                'admin_panel_access' => true,
                'debug_mode' => false,
            ],
            'minimal' => [
                'theme' => 'light',
                'language' => 'en',
            ],
        ];

        return $templates[$template] ?? $templates['default'];
    }
}
```

### 3. 管道式数据处理

Operator支持管道式数据处理，这是框架的一个重要特性，允许在数据操作前后执行一系列任务：

```php
class UserSettingOperator extends Operator
{
    // 定义管道任务列表
    const TASK_LIST = [
        'validate_settings' => [
            'method' => 'validateSettings',
            'require_fields' => ['key', 'value'],
        ],
        'sync_cache' => [
            'method' => 'syncToCache',
            'require_fields' => ['user_id', 'key', 'value'],
        ],
        'send_notification' => [
            'method' => 'sendChangeNotification',
            'require_fields' => ['user_id', 'key'],
        ],
    ];

    /**
     * 使用管道处理的更新操作
     */
    public function updateWithPipeline(array $fields)
    {
        // 选择要执行的管道任务
        $selectTasks = ['validate_settings', 'sync_cache'];

        // 执行标准管道处理
        return $this->standardProcess($fields, $selectTasks);
    }

    /**
     * 验证设置 - 管道任务
     */
    protected function validateSettings(array $data)
    {
        foreach ($data['data'] as $setting) {
            $this->validateSingleSetting($setting);
        }

        \LogUtil::info('Settings validation completed', [
            'count' => count($data['data'])
        ]);
    }

    /**
     * 同步缓存 - 管道任务
     */
    protected function syncToCache(array $data)
    {
        $userSettings = [];

        // 按用户分组
        foreach ($data['data'] as $setting) {
            $userId = $setting['user_id'];
            $userSettings[$userId][$setting['key']] = $setting['value'];
        }

        // 批量更新缓存
        foreach ($userSettings as $userId => $settings) {
            $cacheKey = "user_settings:{$this->clientId}:{$userId}";
            \Yii::app()->cache->set($cacheKey, $settings, 3600);
        }

        \LogUtil::info('Cache sync completed', [
            'users_count' => count($userSettings)
        ]);
    }

    /**
     * 发送变更通知 - 管道任务
     */
    protected function sendChangeNotification(array $data)
    {
        $notificationData = [];

        foreach ($data['data'] as $setting) {
            $userId = $setting['user_id'];
            $notificationData[$userId][] = $setting['key'];
        }

        foreach ($notificationData as $userId => $changedKeys) {
            // 发送通知逻辑
            $this->sendUserNotification($userId, $changedKeys);
        }

        \LogUtil::info('Notifications sent', [
            'users_count' => count($notificationData)
        ]);
    }

    /**
     * 自定义管道任务执行
     */
    public function executeCustomPipeline(array $fields, array $customTasks)
    {
        // 准备管道任务数据
        $data = $this->preparePipelineTask($fields, $customTasks);

        // 执行更新
        $result = $this->execute($fields);

        // 运行管道任务
        $this->runPipleline(['data' => $data]);

        return $result;
    }

    private function validateSingleSetting($setting)
    {
        $key = $setting['key'];
        $value = $setting['value'];

        // 基础验证
        if (empty($key)) {
            throw new \InvalidArgumentException('Setting key cannot be empty');
        }

        // 特定键的验证
        switch ($key) {
            case 'theme':
                $allowedThemes = ['light', 'dark', 'auto'];
                if (!in_array($value, $allowedThemes)) {
                    throw new \InvalidArgumentException('Invalid theme value');
                }
                break;

            case 'language':
                $allowedLanguages = ['en', 'zh', 'ja'];
                if (!in_array($value, $allowedLanguages)) {
                    throw new \InvalidArgumentException('Invalid language value');
                }
                break;
        }
    }

    private function sendUserNotification($userId, $changedKeys)
    {
        // 实际的通知发送逻辑
        \LogUtil::info('User setting changed', [
            'user_id' => $userId,
            'changed_keys' => $changedKeys
        ]);
    }
}
```

**管道处理的优势**：
1. **解耦业务逻辑**：将验证、缓存、通知等逻辑分离
2. **可配置执行**：可以选择性执行某些任务
3. **统一错误处理**：框架提供统一的错误处理机制
4. **性能优化**：批量处理相关数据，减少重复操作

## 事务处理

### 1. 基础事务操作

```php
class UserSettingOperator extends Operator
{
    /**
     * 事务中的复杂操作
     */
    public function updateUserSettingsWithTransaction($userId, $settingsData)
    {
        $repository = $this->getRepository();
        $connection = $repository->getConnection();

        $transaction = $connection->beginTransaction();

        try {
            // 1. 备份现有设置
            $backupData = $this->backupUserSettings($userId);

            // 2. 更新设置
            foreach ($settingsData as $key => $value) {
                $setting = new UserSetting($this->clientId, $userId, $key);
                if ($setting->isNew()) {
                    // 创建新设置
                    $setting->user_id = $userId;
                    $setting->key = $key;
                    $setting->setValue($value);
                    $setting->enable_flag = UserSetting::ENABLE_FLAG_ENABLED;
                    $setting->create();
                } else {
                    // 更新现有设置
                    $setting->setValue($value);
                    $setting->update(['value']);
                }
            }

            // 3. 记录操作日志
            $this->logSettingsChange($userId, $settingsData);

            // 4. 清理缓存
            $this->clearUserSettingsCache($userId);

            $transaction->commit();
            return true;

        } catch (\Exception $e) {
            $transaction->rollback();
            \LogUtil::error('User settings update failed', [
                'user_id' => $userId,
                'settings_data' => $settingsData,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 批量操作的事务处理
     */
    public function batchProcessWithTransaction(array $batchData)
    {
        $repository = $this->getRepository();
        $connection = $repository->getConnection();

        $transaction = $connection->beginTransaction();
        $processedIds = [];

        try {
            foreach ($batchData as $data) {
                $id = $this->processSingleRecord($data);
                $processedIds[] = $id;
            }

            $transaction->commit();
            return $processedIds;

        } catch (\Exception $e) {
            $transaction->rollback();

            // 清理已处理的数据
            $this->cleanupFailedBatch($processedIds);

            throw new \Exception(
                'Batch process failed: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }

    private function backupUserSettings($userId)
    {
        $filter = new UserSettingFilter($this->clientId);
        $filter->byUser($userId)->onlyEnabled();

        $settings = $filter->find();
        $backupData = $settings->getAttributes();

        // 存储备份数据到临时表或缓存
        $backupKey = "user_settings_backup:{$this->clientId}:{$userId}:" . time();
        \Yii::app()->cache->set($backupKey, $backupData, 86400); // 24小时

        return $backupKey;
    }

    private function logSettingsChange($userId, $settingsData)
    {
        // 记录设置变更日志
        \LogUtil::info('User settings updated', [
            'user_id' => $userId,
            'client_id' => $this->clientId,
            'changed_keys' => array_keys($settingsData),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    private function clearUserSettingsCache($userId)
    {
        $cacheKey = "user_settings:{$this->clientId}:{$userId}";
        \Yii::app()->cache->delete($cacheKey);
    }
}
```

### 2. 错误处理和重试机制

```php
class UserSettingOperator extends Operator
{
    const MAX_RETRY_ATTEMPTS = 3;
    const RETRY_DELAY = 1; // seconds

    /**
     * 带重试的操作
     */
    public function executeWithRetry(callable $operation, $maxAttempts = self::MAX_RETRY_ATTEMPTS)
    {
        $attempt = 0;
        $lastException = null;

        while ($attempt < $maxAttempts) {
            try {
                return $operation();
            } catch (\Exception $e) {
                $attempt++;
                $lastException = $e;

                \LogUtil::warning("Operation failed, attempt {$attempt}/{$maxAttempts}", [
                    'exception' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                if ($attempt < $maxAttempts) {
                    sleep(self::RETRY_DELAY * $attempt); // 递增延迟
                }
            }
        }

        throw new \Exception(
            "Operation failed after {$maxAttempts} attempts: " . $lastException->getMessage(),
            0,
            $lastException
        );
    }

    /**
     * 安全的批量更新
     */
    public function safeBatchUpdate(array $updateData)
    {
        return $this->executeWithRetry(function() use ($updateData) {
            // 验证数据
            $this->validateBatchUpdateData($updateData);

            // 执行更新
            return $this->batchUpdate($updateData);
        });
    }

    private function validateBatchUpdateData(array $data)
    {
        foreach ($data as $id => $fields) {
            if (!is_numeric($id) || $id <= 0) {
                throw new \InvalidArgumentException("Invalid ID: {$id}");
            }

            if (empty($fields) || !is_array($fields)) {
                throw new \InvalidArgumentException("Invalid update fields for ID: {$id}");
            }
        }
    }
}
```

## 性能优化

### 1. 查询优化

```php
// 1. 字段选择优化
$filter = new UserSettingFilter($clientId);
$filter->select(['id', 'key', 'value', 'update_time']); // 只选择需要的字段
$filter->limit(100); // 限制结果数量

// 2. 索引优化查询
$filter->user_id = 123; // 使用索引字段
$filter->orderBy('update_time', 'desc'); // 在有索引的字段上排序

// 3. 避免N+1查询问题
$formatter = $batchSettings->getFormatter();
$formatter->baseInfoSetting(); // 批量加载关联数据
```

### 2. 缓存策略

```php
class UserSettingFilter extends Filter
{
    private $cacheEnabled = true;
    private $cacheExpire = 300; // 5分钟

    public function findWithCache($cacheKey = null)
    {
        if (!$this->cacheEnabled) {
            return $this->find();
        }

        $cacheKey = $cacheKey ?: $this->generateCacheKey();

        $cached = \Yii::app()->cache->get($cacheKey);
        if ($cached !== false) {
            return $this->createBatchObjectFromCache($cached);
        }

        $result = $this->find();
        \Yii::app()->cache->set($cacheKey, $result->getAttributes(), $this->cacheExpire);

        return $result;
    }

    private function generateCacheKey()
    {
        $conditions = $this->getQuery()->getWhere()->toArray();
        return 'user_settings_' . md5(serialize($conditions));
    }
}
```

### 3. 批量操作优化

```php
class UserSettingOperator extends Operator
{
    const BATCH_SIZE = 1000;

    /**
     * 大批量数据处理
     */
    public function processBigBatch(array $data)
    {
        $chunks = array_chunk($data, self::BATCH_SIZE);
        $totalProcessed = 0;

        foreach ($chunks as $chunk) {
            $processed = $this->processBatch($chunk);
            $totalProcessed += $processed;

            // 释放内存
            unset($chunk);
            gc_collect_cycles();
        }

        return $totalProcessed;
    }

    /**
     * 使用COPY命令进行大批量插入(PostgreSQL)
     */
    public function bulkInsertWithCopy(array $data)
    {
        $repository = $this->getRepository();
        $connection = $repository->getConnection();

        $table = $this->object->getMetadata()::table();
        $columns = array_keys($data[0]);

        $copyCommand = "COPY {$table} (" . implode(',', $columns) . ") FROM STDIN WITH CSV";

        $stmt = $connection->prepare($copyCommand);

        foreach ($data as $row) {
            $csvRow = implode(',', array_map(function($value) {
                return '"' . str_replace('"', '""', $value) . '"';
            }, $row));

            $stmt->execute([$csvRow]);
        }

        return count($data);
    }
}
```

## 最佳实践

### 1. 代码组织和命名规范

参考setting模块的组织方式：

```php
// 目录结构
protected/library/setting/user/
├── UserSetting.php              # SingleObject - 主实体类
├── BatchUserSetting.php         # BatchObject - 批量对象
├── UserSettingFilter.php        # Filter - 查询过滤器
├── UserSettingFormatter.php     # Formatter - 数据格式化
├── UserSettingOperator.php      # Operator - 业务操作
├── UserSettingMetadata.php      # Metadata - 元数据定义
├── InitUserSettingMetadata.php  # Trait - 元数据初始化
└── process/                     # 业务处理类
    ├── ThemeSetting.php
    ├── NotificationSetting.php
    └── LanguageSetting.php
```

### 2. 元数据设计原则

```php
class UserSettingMetadata extends Metadata
{
    // 1. 清晰的字段定义
    protected $columns = [
        'id' => [
            'type' => 'int',
            'name' => 'id',
            'nullable' => '0',
            'php_type' => 'int',
            'comment' => '主键ID',
            'filter' => [
                'enable' => true,
                'batch' => true
            ]
        ],
        // 2. 合理的Filter配置
        'user_id' => [
            'type' => 'int',
            'name' => 'user_id',
            'nullable' => '0',
            'php_type' => 'int',
            'comment' => '用户ID',
            'filter' => [
                'enable' => true,    // 允许查询
                'batch' => true      // 允许批量查询
            ]
        ],
        // 3. 大字段禁用Filter
        'value' => [
            'type' => 'text',
            'name' => 'value',
            'nullable' => '1',
            'php_type' => 'string',
            'comment' => '配置值',
            'filter' => [
                'enable' => false,   // 大字段不允许直接查询
                'batch' => false
            ]
        ],
    ];

    // 4. 常量定义
    const ENABLE_FLAG_ENABLED = 1;
    const ENABLE_FLAG_DISABLED = 0;

    // 5. 验证规则
    const VALIDATION_RULES = [
        'key' => 'required|string|max:255',
        'value' => 'string',
        'enable_flag' => 'required|in:0,1',
    ];
}
```

### 3. 业务逻辑封装

```php
class UserSetting extends SingleObject
{
    // 1. 业务常量
    const KEY_THEME = 'theme';
    const KEY_LANGUAGE = 'language';
    const KEY_TIMEZONE = 'timezone';

    // 2. 业务方法
    public function isThemeSetting()
    {
        return $this->key === self::KEY_THEME;
    }

    public function getFormattedValue()
    {
        // 业务逻辑封装
        if ($this->isThemeSetting()) {
            return $this->parseThemeValue();
        }

        return $this->parseGenericValue();
    }

    // 3. 生命周期钩子
    protected function beforeCreate()
    {
        $this->validateBusinessRules();
        return parent::beforeCreate();
    }

    private function validateBusinessRules()
    {
        if ($this->key === self::KEY_THEME) {
            $allowedThemes = ['light', 'dark', 'auto'];
            if (!in_array($this->value, $allowedThemes)) {
                throw new \InvalidArgumentException('Invalid theme value');
            }
        }
    }
}
```

### 4. 查询优化策略

```php
class UserSettingFilter extends Filter
{
    // 1. 合理的默认设置
    public function defaultSetting()
    {
        $this->client_id = $this->clientId;
        $this->enable_flag = UserSetting::ENABLE_FLAG_ENABLED;
        parent::defaultSetting();
    }

    // 2. 业务查询方法
    public function activeSettings()
    {
        $this->enable_flag = UserSetting::ENABLE_FLAG_ENABLED;
        return $this;
    }

    public function recentlyUpdated($days = 7)
    {
        $since = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        $this->update_time = new Range($since, date('Y-m-d H:i:s'));
        return $this;
    }

    // 3. 性能优化
    public function withMinimalFields()
    {
        $this->select(['id', 'key', 'value']);
        return $this;
    }
}
```

### 5. 错误处理和日志

```php
class UserSettingOperator extends Operator
{
    public function updateWithLogging(array $data)
    {
        try {
            // 记录操作开始
            \LogUtil::info('User setting update started', [
                'client_id' => $this->clientId,
                'data_count' => count($data)
            ]);

            $result = $this->update($data);

            // 记录成功
            \LogUtil::info('User setting update completed', [
                'affected_rows' => $result
            ]);

            return $result;

        } catch (\Exception $e) {
            // 记录错误
            \LogUtil::error('User setting update failed', [
                'exception' => $e->getMessage(),
                'data' => $data,
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }
}
```

## 常见问题

### 1. 性能问题

**问题**: 查询速度慢
```php
// 错误做法
$filter = new UserSettingFilter($clientId);
$filter->rawWhere("value LIKE '%search%'"); // 大字段模糊查询

// 正确做法
$filter = new UserSettingFilter($clientId);
$filter->key = new Like('notification_%'); // 在索引字段上查询
$filter->select(['id', 'key', 'value']); // 只选择需要的字段
```

**问题**: N+1查询
```php
// 错误做法
$settings = $filter->find()->getAttributes();
foreach ($settings as $setting) {
    $user = new User($clientId, $setting['user_id']); // N+1查询
}

// 正确做法
$settings = $filter->find();
$formatter = $settings->getFormatter();
$formatter->displayUserInfo(true); // 批量加载用户信息
$formattedData = $settings->getAttributes();
```

### 2. 数据一致性问题

**问题**: 并发更新冲突
```php
// 解决方案：使用事务和乐观锁
public function safeUpdate($settingId, $newValue, $expectedVersion)
{
    $transaction = $this->getRepository()->getConnection()->beginTransaction();

    try {
        $setting = new UserSetting($this->clientId, null, null);
        $setting->load(['id' => $settingId]);

        if ($setting->version != $expectedVersion) {
            throw new \Exception('Data has been modified by another user');
        }

        $setting->value = $newValue;
        $setting->version = $expectedVersion + 1;
        $setting->update(['value', 'version']);

        $transaction->commit();
        return true;

    } catch (\Exception $e) {
        $transaction->rollback();
        throw $e;
    }
}
```

### 3. 内存使用问题

**问题**: 大批量数据处理内存溢出
```php
// 错误做法
$filter = new UserSettingFilter($clientId);
$allSettings = $filter->find()->getAttributes(); // 一次性加载所有数据

// 正确做法
$filter = new UserSettingFilter($clientId);
$pageSize = 1000;
$offset = 0;

do {
    $filter->limit($pageSize)->offset($offset);
    $settings = $filter->find()->getAttributes();

    foreach ($settings as $setting) {
        // 处理单条记录
        $this->processSetting($setting);
    }

    $offset += $pageSize;
    unset($settings); // 释放内存
    gc_collect_cycles();

} while (count($settings) == $pageSize);
```

## API参考

### 核心类方法

#### Metadata
- `table()` - 返回表名
- `dataSource()` - 返回数据源
- `singeObject()` - 返回SingleObject类名
- `batchObject()` - 返回BatchObject类名
- `filter()` - 返回Filter类名
- `formatter()` - 返回Formatter类名
- `operator()` - 返回Operator类名

#### SingleObject
- `create()` - 创建记录
- `update($fields)` - 更新指定字段
- `delete()` - 删除记录
- `load($conditions)` - 加载数据
- `isNew()` - 是否为新记录
- `getAttributes()` - 获取所有属性
- `bindAttributes($data)` - 绑定属性

#### Filter
- `find()` - 执行查询
- `count()` - 获取记录数
- `select($fields)` - 选择字段
- `where($field, $operator, $value)` - 添加条件
- `orderBy($field, $direction)` - 排序
- `limit($limit)` - 限制数量
- `offset($offset)` - 偏移量

#### BatchObject
- `getAttributes()` - 获取所有数据
- `getFormatter()` - 获取格式化器
- `getOperator()` - 获取操作器

#### Operator
- `create($fields)` - 批量创建
- `update($data)` - 批量更新
- `delete()` - 批量删除
- `batchInsert($data)` - 批量插入
- `execute($fields)` - 执行更新

#### Formatter
- `displayFields($fields)` - 设置显示字段
- `baseInfoSetting()` - 基础信息设置
- `detailInfoSetting()` - 详细信息设置

---

## 总结

Xiaoman ORM框架通过元数据驱动的方式，提供了一套完整的对象关系映射解决方案。参考setting模块的实际应用，框架的核心优势包括：

### 核心特性
1. **清晰的架构**: 通过六大核心组件实现职责分离
2. **灵活的扩展**: 支持自定义Operator和Formatter
3. **类型安全**: 强类型定义和自动验证
4. **跨数据库**: 支持MySQL和PostgreSQL
5. **高性能**: 支持批量操作和查询优化

### 实际应用优势
6. **业务封装**: 参考setting模块的业务逻辑封装模式
7. **配置管理**: 完善的配置项管理和验证机制
8. **缓存集成**: 内置缓存支持，提升查询性能
9. **事务支持**: 完整的事务处理机制，确保数据一致性
10. **错误处理**: 完善的错误处理和重试机制

### 开发效率
11. **自动生成**: 命令行工具自动生成所有必要文件
12. **链式调用**: 直观的API设计，提高代码可读性
13. **批量操作**: 高效的批量数据处理能力
14. **调试支持**: 丰富的日志和调试功能

这种设计使得开发者可以专注于业务逻辑的实现，而不需要关心底层的数据访问细节。Xiaoman ORM框架真正实现了"配置优于编码"的理念，为企业级应用开发提供了强有力的支撑。

## 快速上手指南

### 1. 创建你的第一个ORM模块

1. **分析数据表结构**
2. **创建Metadata** - 定义字段和查询规则
3. **创建SingleObject** - 处理单条记录操作
4. **创建Filter** - 构建查询条件
5. **创建Formatter** - 格式化输出数据
6. **创建Operator** - 处理复杂业务逻辑

### 2. 核心原则

- **元数据驱动**：一次定义，处处使用
- **职责分离**：每个组件专注一个职责
- **性能优先**：合理使用索引和缓存
- **业务封装**：将业务逻辑封装到合适的组件中

### 3. 常用模式

```php
// 查询模式
$filter = new YourFilter($clientId);
$filter->byUser($userId)->onlyEnabled()->select(['id', 'name']);
$data = $filter->find()->getAttributes();

// 格式化模式
$formatter = $batchObject->getFormatter();
$formatter->baseInfoSetting();
$formattedData = $batchObject->getAttributes();

// 业务操作模式
$operator = $batchObject->getOperator();
$operator->batchUpdate(['status' => 1]);
```

通过这个教程，你应该能够理解Xiaoman ORM的设计思路，并能够在实际项目中应用它。记住，好的架构不是一蹴而就的，需要在实践中不断完善和优化。
