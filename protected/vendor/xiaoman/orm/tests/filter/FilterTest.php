<?php


namespace tests\filter;

use common\library\oms\common\OmsConstant;
use common\library\oms\inbound_invoice\InboundInvoiceFilter;
use common\library\oms\inbound_invoice\purchase\PurchaseInboundInvoiceFilter;
use common\library\oms\inbound_invoice\record\InboundRecordFilter;
use common\library\oms\inventory\product_inventory\ProductInventoryFilter;
use common\library\oms\outbound_invoice\other\OtherOutboundInvoiceFilter;
use common\library\oms\outbound_invoice\other\record\OtherOutboundRecordFilter;
use common\library\oms\outbound_invoice\Outbound;
use common\library\oms\outbound_invoice\OutboundInvoice;
use common\library\oms\outbound_invoice\OutboundInvoiceFilter;
use common\library\oms\outbound_invoice\sale\record\SaleOutboundProductFilter;
use common\library\oms\warehouse_return_invoice\ReturnInvoiceFilter;
use common\library\purchase\purchase_order_product\PurchaseOrderProductFilter;
use xiaoman\orm\common\JoinFilterDispatch;
use PHPUnit\Framework\TestCase;
use xiaoman\orm\application\Mail;
use xiaoman\orm\application\MailFilter;
use xiaoman\orm\application\MailTagFilter;
use xiaoman\orm\common\Filter;
use xiaoman\orm\database\data\DateRange;
use xiaoman\orm\database\data\Equal;
use xiaoman\orm\database\DBConstants;
use xiaoman\orm\database\document\clause\ParagraphClause;
use xiaoman\orm\database\grammar\MysqlGrammar;
use xiaoman\orm\database\data\In;
use xiaoman\orm\database\join\JoinDrive;
use xiaoman\orm\database\query\clause\WhereClause;


class FilterTest extends TestCase
{
    public function testDisplayComment()
    {

          $result = ProductInventoryFilter::__buildDisplayComment(1);
          print_r($result);
          //$this->assertNotEmpty($result);
    }

    public function testList()
    {
        try {
            $filter = new MailFilter(1);
            $filter->mail_id = 1118292453;
            //$filter->select(['mail_id', 'client_id', 'mail_type']);
            $data = $filter->find();

            $data->getOperator()->changeMailType(2);

            //print_r($data->getAttributes(['mail_id', 'client_id', 'mail_type']));

            //$data->getFormatter()->displayFields(['mail_id','mail_type']);

            print_r($data->getAttributes());

            //$this->assertNotEmpty($data->getAttributes());
        }catch (\Throwable $exception) {
            echo  $exception->getMessage();
            print_r($exception->getTraceAsString());
        }

    }

    public function testJoin()
    {



        try{
            $mailFilter = new MailFilter(1);
            $mailFilter->mail_id = 1115901315;
            $mailFilter->select(['mail_id','client_id']);

            $mailTagFilter  = new MailTagFilter(1);
            $mailTagFilter->tag_id = 123;


            $esFilter = new MailFilter(1);
            $esFilter->mail_id = 2;

            $join = new JoinFilterDispatch();
            $join->group('mail_id')->first($mailFilter)->left($mailTagFilter)->on('mail_id','id')->order('mail_id','desc',$mailFilter->getQuery()->getTable()->getName())->order('id','desc',$mailTagFilter->getQuery()->getTable()->getName());
            $join->groupLeft('mail_id')->first($esFilter);
            $result = $join->find();
            print_r($result);
        }catch (\Throwable $exception){
            echo  $exception->getMessage();
            print_r($exception->getTraceAsString());
        }





        //$mailFilter->join($mailTagFilter,['mail_id','mail_id']);

//        $grammar = new  MysqlGrammar($mailFilter->getQuery());
//        $result = $grammar->buildSelect();

        //print_r($result);

        $this->assertNotEmpty($result);
    }

    public function testJoinRawData()
    {
        $filter = new PurchaseOrderProductFilter(14367);
        $filter->order_id = new \xiaoman\orm\database\data\In([1118213998]);
        $filter->select(['order_id']);

        $purchaseFilter = new PurchaseOrderFilter(14367);
        $purchaseFilter->enable_flag = 1;
        $purchaseFilter->select(['purchase_order_id','purchase_order_no']);
        $filter->join($purchaseFilter,['purchase_order_id','purchase_order_id']);
        $filter->getCompileSql();
        $data = $filter->rawData(false);
        print_r($data);
    }

    public function testMix()
    {
        try {
            $mailFilter = new MailFilter(1);
            $mailFilter->mail_id = 1115901315;
            $mailFilter->mix();
            $mailFilter->select(['mail_id', 'client_id']);

            $grammar = new  MysqlGrammar($mailFilter->getQuery());
            $result = $grammar->buildSelect();
            print_r($result);

            //$this->assertNotEmpty($result);
        }catch (\Throwable $throwable) {
            print_r($throwable->getMessage());
            print_r($throwable->getTraceAsString());
        }
    }


    public function testReset()
    {
        $filter = new MailFilter(1);
        $filter->mail_type = 1;
        $filter->mail_id = 1234;
        $filter->user_mail_id = new In([123,345]);

        $filter->getCompileSql();
        print_r($filter->getQuery());

        $filter->removeWhere(['mail_type','']);
        $filter->getCompileSql();
        print_r($filter->getQuery());
    }

    public function testObjectHash()
    {
        $a = new  WhereClause('mail_id',new Equal(123),'AND');
        $b = new  WhereClause('mail_id',new Equal(123),'AND');

        echo  spl_object_hash($a),PHP_EOL;
        echo  spl_object_hash($b),PHP_EOL;
        echo  spl_object_id($a),PHP_EOL;
        echo  spl_object_id($b),PHP_EOL;

    }

    public function testRawWhere()
    {
        $filter = new MailFilter(1);
        $filter->mail_type = 1;
        $filter->mail_id = 1234;
        $filter->user_mail_id = new In([123,345]);
        $filter->setRawWhere('and 1=0');

        $filter->getCompileSql();

    }

    public function testJoin1()
    {
        $mailFilter = new MailFilter(1);
        $mailFilter->mail_id = 1115901315;
        $mailFilter->select(['mail_id'=>'mailId','client_id']);

        $mailTagFilter  = new MailTagFilter(1);
       // $mailTagFilter->select(['mail_id']);
        $mailTagFilter->tag_id = 123;


        $mailFilter->initJoin()
            ->leftJoin($mailTagFilter)
            ->on('mail_id','mail_id')
            //->getCompileSql()
            ->joinOrder('mail_id',null)
            ->joinOrder('id','desc',$mailTagFilter->getTableName())
            ->joinGroupBy('mail_id',$mailTagFilter->getTableName())
            ->joinlimit(10,20)
            ->getCompileSql();
            //->find();

    }


    public function testDateTime()
    {
        $filter = new MailFilter(1);
        $filter->mail_type = 1;
        $filter->mail_id = 1234;
        $filter->create_time = new DateRange('122','');

        $filter->getCompileSql();

    }
    public function testGroup()
    {
        //sum(p.count) as purchase_count,sum(i.inbound_count) inbound_count
        //ep
        $purchaseFilter = new PurchaseOrderProductFilter(1);
        $purchaseFilter->order_id = [12234,23344];
        $purchaseFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $purchaseFilter->select(['order_id','invoice_product_id','count'=>function($colunm,$tableName,$systemQuto){
            return "sum({$tableName}.{$colunm}) as purchase_count";
        }]);

        $inboundFilter = new InboundInvoiceFilter(1);
        $inboundFilter->refer_type = \Constants::TYPE_PURCHASE_ORDER;
        $inboundFilter->status = OmsConstant::INBOUND_INVOICE_STATUS_FINISH;
        $inboundFilter->delete_flag = \Constants::DELETE_FLAG_FALSE;
        $inboundFilter->select(['inbound_count'=>function($colunm,$tableName,$systemQuto){
            return "sum({$tableName}.{$colunm}) as inbound_count";
        }]);

        $purchaseFilter->initJoin()
            ->leftJoin($inboundFilter)
            ->on('purchase_order_product_id','sub_refer_id')
            ->joinGroupBy('order_id',$purchaseFilter->getTableName())
            ->joinGroupBy('invoice_product_id',$purchaseFilter->getTableName());

        $purchaseFilter->getCompileSql();



    }


    public function testTime()
    {
        $filter = new MailFilter(1);
        $filter->mail_type = 1;
        $filter->mail_id = 1234;
        $filter->create_time = new DateRange(null,'2018');

        $filter->getCompileSql();

    }

    public function testAlisa()
    {
        $filter = new MailFilter(1);
        $filter->select([
            'mail_id',
            'count' => function(){return 'sum(count) as count';}
        ]);
        $filter->mail_type = 1;
        $filter->mail_id = 1234;

        $filter->getCompileSql();
    }

    //测试获取已经设置的参数
    public function testGetWhere()
    {
        $filter = new MailFilter(1);
        $filter->select([
            'mail_id',
            'mail_type' => 'alisa_type'
        ]);
        $filter->mail_type = 1;
        $filter->mail_id = new In([1,2,3]);
        $filter->create_time = new DateRange(null,'2018');
        $wheres = $filter->getWheres();
        $filter->getCompileSql();
        print_r($wheres);
    }

    public function testGetWhereColumn()
    {
        $filter = new MailFilter(1);
        $filter->select([
            'mail_id',
            'mail_type' => 'alisa_type'
        ]);
        $filter->mail_type = 1;
        $filter->mail_id = 1234;
        $filter->create_time = new DateRange(null,'2018');
        $filter->or('mail_id',456);
        $wheres = $filter->mail_type;
        print_r($wheres);
    }

    //测试重置参数
    public function testResetWhere()
    {
        $filter = new MailFilter(1);
        $filter->select([
            'mail_id',
            'mail_type' => 'alisa_type'
        ]);
        $filter->mail_type = 1;
        $filter->mail_id = 1234;
        $filter->create_time = new DateRange(null,'2018');
        echo  $filter->getCompileSql();
        //print_r($filter->getWheres());
        $filter->removeWhere(['create_time']);

        //print_r($filter->getWheres());
        echo $filter->getCompileSql();
    }

    //测试置空全部参数
    public function testRefershWhere()
    {
        $filter = new MailFilter(1);
        $filter->mail_type = 1;
        $filter->mail_id = 1234;
        $filter->create_time = new DateRange('2017','2018');

        print_r($filter->getWheres());
        $filter->refreshWhere();
        print_r($filter->getWheres());
    }


    public function testFix()
    {
        \User::setLoginUserById(11859132);
        $inboundRecordFilter = new  InboundRecordFilter(14367);
        $inboundRecordFilter->delete_flag = \Constants::DELETE_FLAG_FALSE;
        $inboundRecordFilter->inbound_invoice_id = 1531449582;
        $inboundRecordFilter->status = OmsConstant::INBOUND_INVOICE_STATUS_WAIT;
        //$inboundRecordFilter->keyword();
        $inboundRecordFilter->paragraph(
            ParagraphClause::make()
                ->addWhereClause(new \xiaoman\orm\database\document\clause\WhereClause('mail_id',new Equal(123),DBConstants::LOGIC_AND))
            ->addWhereClause(new \xiaoman\orm\database\document\clause\WhereClause('mail_id',new Equal(123),DBConstants::LOGIC_OR))
        );
        $inboundRecordFilter->getCompileSql();
    }

    public function testClosure()
    {
        $filter = new MailFilter(1);
        $filter->mail_type = 1;
        $filter->mail_id = 1234;
        $filter->create_time = date('Y-m-d 0:0:0');
    }


}
