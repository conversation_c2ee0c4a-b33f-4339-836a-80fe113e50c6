<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

/**
 * The "orderreturns" collection of methods.
 * Typical usage is:
 *  <code>
 *   $contentService = new Google_Service_ShoppingContent(...);
 *   $orderreturns = $contentService->orderreturns;
 *  </code>
 */
class Google_Service_ShoppingContent_Resource_Orderreturns extends Google_Service_Resource
{
  /**
   * Retrieves an order return from your Merchant Center account.
   * (orderreturns.get)
   *
   * @param string $merchantId The ID of the account that manages the order. This
   * cannot be a multi-client account.
   * @param string $returnId Merchant order return ID generated by Google.
   * @param array $optParams Optional parameters.
   * @return Google_Service_ShoppingContent_MerchantOrderReturn
   */
  public function get($merchantId, $returnId, $optParams = array())
  {
    $params = array('merchantId' => $merchantId, 'returnId' => $returnId);
    $params = array_merge($params, $optParams);
    return $this->call('get', array($params), "Google_Service_ShoppingContent_MerchantOrderReturn");
  }
  /**
   * Lists order returns in your Merchant Center account.
   * (orderreturns.listOrderreturns)
   *
   * @param string $merchantId The ID of the account that manages the order. This
   * cannot be a multi-client account.
   * @param array $optParams Optional parameters.
   *
   * @opt_param string createdEndDate Obtains order returns created before this
   * date (inclusively), in ISO 8601 format.
   * @opt_param string createdStartDate Obtains order returns created after this
   * date (inclusively), in ISO 8601 format.
   * @opt_param string maxResults The maximum number of order returns to return in
   * the response, used for paging. The default value is 25 returns per page, and
   * the maximum allowed value is 250 returns per page.
   * @opt_param string orderBy Return the results in the specified order.
   * @opt_param string pageToken The token returned by the previous request.
   * @return Google_Service_ShoppingContent_OrderreturnsListResponse
   */
  public function listOrderreturns($merchantId, $optParams = array())
  {
    $params = array('merchantId' => $merchantId);
    $params = array_merge($params, $optParams);
    return $this->call('list', array($params), "Google_Service_ShoppingContent_OrderreturnsListResponse");
  }
}
