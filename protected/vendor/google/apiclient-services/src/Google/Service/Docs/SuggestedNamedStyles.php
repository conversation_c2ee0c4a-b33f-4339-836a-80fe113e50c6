<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Docs_SuggestedNamedStyles extends Google_Model
{
  protected $namedStylesType = 'Google_Service_Docs_NamedStyles';
  protected $namedStylesDataType = '';
  protected $namedStylesSuggestionStateType = 'Google_Service_Docs_NamedStylesSuggestionState';
  protected $namedStylesSuggestionStateDataType = '';

  /**
   * @param Google_Service_Docs_NamedStyles
   */
  public function setNamedStyles(Google_Service_Docs_NamedStyles $namedStyles)
  {
    $this->namedStyles = $namedStyles;
  }
  /**
   * @return Google_Service_Docs_NamedStyles
   */
  public function getNamedStyles()
  {
    return $this->namedStyles;
  }
  /**
   * @param Google_Service_Docs_NamedStylesSuggestionState
   */
  public function setNamedStylesSuggestionState(Google_Service_Docs_NamedStylesSuggestionState $namedStylesSuggestionState)
  {
    $this->namedStylesSuggestionState = $namedStylesSuggestionState;
  }
  /**
   * @return Google_Service_Docs_NamedStylesSuggestionState
   */
  public function getNamedStylesSuggestionState()
  {
    return $this->namedStylesSuggestionState;
  }
}
