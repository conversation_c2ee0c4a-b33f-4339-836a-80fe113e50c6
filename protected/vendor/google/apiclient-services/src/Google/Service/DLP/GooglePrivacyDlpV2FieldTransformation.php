<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_DLP_GooglePrivacyDlpV2FieldTransformation extends Google_Collection
{
  protected $collection_key = 'fields';
  protected $conditionType = 'Google_Service_DLP_GooglePrivacyDlpV2RecordCondition';
  protected $conditionDataType = '';
  protected $fieldsType = 'Google_Service_DLP_GooglePrivacyDlpV2FieldId';
  protected $fieldsDataType = 'array';
  protected $infoTypeTransformationsType = 'Google_Service_DLP_GooglePrivacyDlpV2InfoTypeTransformations';
  protected $infoTypeTransformationsDataType = '';
  protected $primitiveTransformationType = 'Google_Service_DLP_GooglePrivacyDlpV2PrimitiveTransformation';
  protected $primitiveTransformationDataType = '';

  /**
   * @param Google_Service_DLP_GooglePrivacyDlpV2RecordCondition
   */
  public function setCondition(Google_Service_DLP_GooglePrivacyDlpV2RecordCondition $condition)
  {
    $this->condition = $condition;
  }
  /**
   * @return Google_Service_DLP_GooglePrivacyDlpV2RecordCondition
   */
  public function getCondition()
  {
    return $this->condition;
  }
  /**
   * @param Google_Service_DLP_GooglePrivacyDlpV2FieldId
   */
  public function setFields($fields)
  {
    $this->fields = $fields;
  }
  /**
   * @return Google_Service_DLP_GooglePrivacyDlpV2FieldId
   */
  public function getFields()
  {
    return $this->fields;
  }
  /**
   * @param Google_Service_DLP_GooglePrivacyDlpV2InfoTypeTransformations
   */
  public function setInfoTypeTransformations(Google_Service_DLP_GooglePrivacyDlpV2InfoTypeTransformations $infoTypeTransformations)
  {
    $this->infoTypeTransformations = $infoTypeTransformations;
  }
  /**
   * @return Google_Service_DLP_GooglePrivacyDlpV2InfoTypeTransformations
   */
  public function getInfoTypeTransformations()
  {
    return $this->infoTypeTransformations;
  }
  /**
   * @param Google_Service_DLP_GooglePrivacyDlpV2PrimitiveTransformation
   */
  public function setPrimitiveTransformation(Google_Service_DLP_GooglePrivacyDlpV2PrimitiveTransformation $primitiveTransformation)
  {
    $this->primitiveTransformation = $primitiveTransformation;
  }
  /**
   * @return Google_Service_DLP_GooglePrivacyDlpV2PrimitiveTransformation
   */
  public function getPrimitiveTransformation()
  {
    return $this->primitiveTransformation;
  }
}
