############################################################
##
## PhpSpreadsheet - function name translations
##
## <PERSON><PERSON><PERSON> pols<PERSON> (Polish)
##
############################################################


##
## Funkcje baz danych (Cube Functions)
##
CUBEKPIMEMBER = ELEMENT.KPI.MODUŁU
CUBEMEMBER = ELEMENT.MODUŁU
CUBEMEMBERPROPERTY = WŁAŚCIWOŚĆ.ELEMENTU.MODUŁU
CUBERANKEDMEMBER = USZEREGOWANY.ELEMENT.MODUŁU
CUBESET = ZESTAW.MODUŁÓW
CUBESETCOUNT = LICZNIK.MODUŁÓW.ZESTAWU
CUBEVALUE = WARTOŚĆ.MODUŁU

##
## Funk<PERSON><PERSON> baz danych (Database Functions)
##
DAVERAGE = BD.ŚREDNIA
DCOUNT = BD.ILE.REKORDÓW
DCOUNTA = BD.ILE.REKORDÓW.A
DGET = BD.POLE
DMAX = BD.MAX
DMIN = BD.MIN
DPRODUCT = BD.ILOCZYN
DSTDEV = BD.ODCH.STANDARD
DSTDEVP = BD.ODCH.STANDARD.POPUL
DSUM = BD.SUMA
DVAR = BD.WARIANCJA
DVARP = BD.WARIANCJA.POPUL

##
## Funkcje daty i godziny (Date & Time Functions)
##
DATE = DATA
DATEDIF = DATA.RÓŻNICA
DATESTRING = DATA.CIĄG.ZNAK
DATEVALUE = DATA.WARTOŚĆ
DAY = DZIEŃ
DAYS = DNI
DAYS360 = DNI.360
EDATE = NR.SER.DATY
EOMONTH = NR.SER.OST.DN.MIES
HOUR = GODZINA
ISOWEEKNUM = ISO.NUM.TYG
MINUTE = MINUTA
MONTH = MIESIĄC
NETWORKDAYS = DNI.ROBOCZE
NETWORKDAYS.INTL = DNI.ROBOCZE.NIESTAND
NOW = TERAZ
SECOND = SEKUNDA
THAIDAYOFWEEK = TAJ.DZIEŃ.TYGODNIA
THAIMONTHOFYEAR = TAJ.MIESIĄC.ROKU
THAIYEAR = TAJ.ROK
TIME = CZAS
TIMEVALUE = CZAS.WARTOŚĆ
TODAY = DZIŚ
WEEKDAY = DZIEŃ.TYG
WEEKNUM = NUM.TYG
WORKDAY = DZIEŃ.ROBOCZY
WORKDAY.INTL = DZIEŃ.ROBOCZY.NIESTAND
YEAR = ROK
YEARFRAC = CZĘŚĆ.ROKU

##
## Funkcje inżynierskie (Engineering Functions)
##
BESSELI = BESSEL.I
BESSELJ = BESSEL.J
BESSELK = BESSEL.K
BESSELY = BESSEL.Y
BIN2DEC = DWÓJK.NA.DZIES
BIN2HEX = DWÓJK.NA.SZESN
BIN2OCT = DWÓJK.NA.ÓSM
BITAND = BITAND
BITLSHIFT = BIT.PRZESUNIĘCIE.W.LEWO
BITOR = BITOR
BITRSHIFT = BIT.PRZESUNIĘCIE.W.PRAWO
BITXOR = BITXOR
COMPLEX = LICZBA.ZESP
CONVERT = KONWERTUJ
DEC2BIN = DZIES.NA.DWÓJK
DEC2HEX = DZIES.NA.SZESN
DEC2OCT = DZIES.NA.ÓSM
DELTA = CZY.RÓWNE
ERF = FUNKCJA.BŁ
ERF.PRECISE = FUNKCJA.BŁ.DOKŁ
ERFC = KOMP.FUNKCJA.BŁ
ERFC.PRECISE = KOMP.FUNKCJA.BŁ.DOKŁ
GESTEP = SPRAWDŹ.PRÓG
HEX2BIN = SZESN.NA.DWÓJK
HEX2DEC = SZESN.NA.DZIES
HEX2OCT = SZESN.NA.ÓSM
IMABS = MODUŁ.LICZBY.ZESP
IMAGINARY = CZ.UROJ.LICZBY.ZESP
IMARGUMENT = ARG.LICZBY.ZESP
IMCONJUGATE = SPRZĘŻ.LICZBY.ZESP
IMCOS = COS.LICZBY.ZESP
IMCOSH = COSH.LICZBY.ZESP
IMCOT = COT.LICZBY.ZESP
IMCSC = CSC.LICZBY.ZESP
IMCSCH = CSCH.LICZBY.ZESP
IMDIV = ILORAZ.LICZB.ZESP
IMEXP = EXP.LICZBY.ZESP
IMLN = LN.LICZBY.ZESP
IMLOG10 = LOG10.LICZBY.ZESP
IMLOG2 = LOG2.LICZBY.ZESP
IMPOWER = POTĘGA.LICZBY.ZESP
IMPRODUCT = ILOCZYN.LICZB.ZESP
IMREAL = CZ.RZECZ.LICZBY.ZESP
IMSEC = SEC.LICZBY.ZESP
IMSECH = SECH.LICZBY.ZESP
IMSIN = SIN.LICZBY.ZESP
IMSINH = SINH.LICZBY.ZESP
IMSQRT = PIERWIASTEK.LICZBY.ZESP
IMSUB = RÓŻN.LICZB.ZESP
IMSUM = SUMA.LICZB.ZESP
IMTAN = TAN.LICZBY.ZESP
OCT2BIN = ÓSM.NA.DWÓJK
OCT2DEC = ÓSM.NA.DZIES
OCT2HEX = ÓSM.NA.SZESN

##
## Funkcje finansowe (Financial Functions)
##
ACCRINT = NAL.ODS
ACCRINTM = NAL.ODS.WYKUP
AMORDEGRC = AMORT.NIELIN
AMORLINC = AMORT.LIN
COUPDAYBS = WYPŁ.DNI.OD.POCZ
COUPDAYS = WYPŁ.DNI
COUPDAYSNC = WYPŁ.DNI.NAST
COUPNCD = WYPŁ.DATA.NAST
COUPNUM = WYPŁ.LICZBA
COUPPCD = WYPŁ.DATA.POPRZ
CUMIPMT = SPŁAC.ODS
CUMPRINC = SPŁAC.KAPIT
DB = DB
DDB = DDB
DISC = STOPA.DYSK
DOLLARDE = CENA.DZIES
DOLLARFR = CENA.UŁAM
DURATION = ROCZ.PRZYCH
EFFECT = EFEKTYWNA
FV = FV
FVSCHEDULE = WART.PRZYSZŁ.KAP
INTRATE = STOPA.PROC
IPMT = IPMT
IRR = IRR
ISPMT = ISPMT
MDURATION = ROCZ.PRZYCH.M
MIRR = MIRR
NOMINAL = NOMINALNA
NPER = NPER
NPV = NPV
ODDFPRICE = CENA.PIERW.OKR
ODDFYIELD = RENT.PIERW.OKR
ODDLPRICE = CENA.OST.OKR
ODDLYIELD = RENT.OST.OKR
PDURATION = O.CZAS.TRWANIA
PMT = PMT
PPMT = PPMT
PRICE = CENA
PRICEDISC = CENA.DYSK
PRICEMAT = CENA.WYKUP
PV = PV
RATE = RATE
RECEIVED = KWOTA.WYKUP
RRI = RÓWNOW.STOPA.PROC
SLN = SLN
SYD = SYD
TBILLEQ = RENT.EKW.BS
TBILLPRICE = CENA.BS
TBILLYIELD = RENT.BS
VDB = VDB
XIRR = XIRR
XNPV = XNPV
YIELD = RENTOWNOŚĆ
YIELDDISC = RENT.DYSK
YIELDMAT = RENT.WYKUP

##
## Funkcje informacyjne (Information Functions)
##
CELL = KOMÓRKA
ERROR.TYPE = NR.BŁĘDU
INFO = INFO
ISBLANK = CZY.PUSTA
ISERR = CZY.BŁ
ISERROR = CZY.BŁĄD
ISEVEN = CZY.PARZYSTE
ISFORMULA = CZY.FORMUŁA
ISLOGICAL = CZY.LOGICZNA
ISNA = CZY.BRAK
ISNONTEXT = CZY.NIE.TEKST
ISNUMBER = CZY.LICZBA
ISODD = CZY.NIEPARZYSTE
ISREF = CZY.ADR
ISTEXT = CZY.TEKST
N = N
NA = BRAK
SHEET = ARKUSZ
SHEETS = ARKUSZE
TYPE = TYP

##
## Funkcje logiczne (Logical Functions)
##
AND = ORAZ
FALSE = FAŁSZ
IF = JEŻELI
IFERROR = JEŻELI.BŁĄD
IFNA = JEŻELI.ND
IFS = WARUNKI
NOT = NIE
OR = LUB
SWITCH = PRZEŁĄCZ
TRUE = PRAWDA
XOR = XOR

##
## Funkcje wyszukiwania i odwołań (Lookup & Reference Functions)
##
ADDRESS = ADRES
AREAS = OBSZARY
CHOOSE = WYBIERZ
COLUMN = NR.KOLUMNY
COLUMNS = LICZBA.KOLUMN
FORMULATEXT = FORMUŁA.TEKST
GETPIVOTDATA = WEŹDANETABELI
HLOOKUP = WYSZUKAJ.POZIOMO
HYPERLINK = HIPERŁĄCZE
INDEX = INDEKS
INDIRECT = ADR.POŚR
LOOKUP = WYSZUKAJ
MATCH = PODAJ.POZYCJĘ
OFFSET = PRZESUNIĘCIE
ROW = WIERSZ
ROWS = ILE.WIERSZY
RTD = DANE.CZASU.RZECZ
TRANSPOSE = TRANSPONUJ
VLOOKUP = WYSZUKAJ.PIONOWO

##
## Funkcje matematyczne i trygonometryczne (Math & Trig Functions)
##
ABS = MODUŁ.LICZBY
ACOS = ACOS
ACOSH = ACOSH
ACOT = ACOT
ACOTH = ACOTH
AGGREGATE = AGREGUJ
ARABIC = ARABSKIE
ASIN = ASIN
ASINH = ASINH
ATAN = ATAN
ATAN2 = ATAN2
ATANH = ATANH
BASE = PODSTAWA
CEILING.MATH = ZAOKR.W.GÓRĘ.MATEMATYCZNE
CEILING.PRECISE = ZAOKR.W.GÓRĘ.DOKŁ
COMBIN = KOMBINACJE
COMBINA = KOMBINACJE.A
COS = COS
COSH = COSH
COT = COT
COTH = COTH
CSC = CSC
CSCH = CSCH
DECIMAL = DZIESIĘTNA
DEGREES = STOPNIE
ECMA.CEILING = ECMA.ZAOKR.W.GÓRĘ
EVEN = ZAOKR.DO.PARZ
EXP = EXP
FACT = SILNIA
FACTDOUBLE = SILNIA.DWUKR
FLOOR.MATH = ZAOKR.W.DÓŁ.MATEMATYCZNE
FLOOR.PRECISE = ZAOKR.W.DÓŁ.DOKŁ
GCD = NAJW.WSP.DZIEL
INT = ZAOKR.DO.CAŁK
ISO.CEILING = ISO.ZAOKR.W.GÓRĘ
LCM = NAJMN.WSP.WIEL
LN = LN
LOG = LOG
LOG10 = LOG10
MDETERM = WYZNACZNIK.MACIERZY
MINVERSE = MACIERZ.ODW
MMULT = MACIERZ.ILOCZYN
MOD = MOD
MROUND = ZAOKR.DO.WIELOKR
MULTINOMIAL = WIELOMIAN
MUNIT = MACIERZ.JEDNOSTKOWA
ODD = ZAOKR.DO.NPARZ
PI = PI
POWER = POTĘGA
PRODUCT = ILOCZYN
QUOTIENT = CZ.CAŁK.DZIELENIA
RADIANS = RADIANY
RAND = LOS
RANDBETWEEN = LOS.ZAKR
ROMAN = RZYMSKIE
ROUND = ZAOKR
ROUNDBAHTDOWN = ZAOKR.DÓŁ.BAT
ROUNDBAHTUP = ZAOKR.GÓRA.BAT
ROUNDDOWN = ZAOKR.DÓŁ
ROUNDUP = ZAOKR.GÓRA
SEC = SEC
SECH = SECH
SERIESSUM = SUMA.SZER.POT
SIGN = ZNAK.LICZBY
SIN = SIN
SINH = SINH
SQRT = PIERWIASTEK
SQRTPI = PIERW.PI
SUBTOTAL = SUMY.CZĘŚCIOWE
SUM = SUMA
SUMIF = SUMA.JEŻELI
SUMIFS = SUMA.WARUNKÓW
SUMPRODUCT = SUMA.ILOCZYNÓW
SUMSQ = SUMA.KWADRATÓW
SUMX2MY2 = SUMA.X2.M.Y2
SUMX2PY2 = SUMA.X2.P.Y2
SUMXMY2 = SUMA.XMY.2
TAN = TAN
TANH = TANH
TRUNC = LICZBA.CAŁK

##
## Funkcje statystyczne (Statistical Functions)
##
AVEDEV = ODCH.ŚREDNIE
AVERAGE = ŚREDNIA
AVERAGEA = ŚREDNIA.A
AVERAGEIF = ŚREDNIA.JEŻELI
AVERAGEIFS = ŚREDNIA.WARUNKÓW
BETA.DIST = ROZKŁ.BETA
BETA.INV = ROZKŁ.BETA.ODWR
BINOM.DIST = ROZKŁ.DWUM
BINOM.DIST.RANGE = ROZKŁ.DWUM.ZAKRES
BINOM.INV = ROZKŁ.DWUM.ODWR
CHISQ.DIST = ROZKŁ.CHI
CHISQ.DIST.RT = ROZKŁ.CHI.PS
CHISQ.INV = ROZKŁ.CHI.ODWR
CHISQ.INV.RT = ROZKŁ.CHI.ODWR.PS
CHISQ.TEST = CHI.TEST
CONFIDENCE.NORM = UFNOŚĆ.NORM
CONFIDENCE.T = UFNOŚĆ.T
CORREL = WSP.KORELACJI
COUNT = ILE.LICZB
COUNTA = ILE.NIEPUSTYCH
COUNTBLANK = LICZ.PUSTE
COUNTIF = LICZ.JEŻELI
COUNTIFS = LICZ.WARUNKI
COVARIANCE.P = KOWARIANCJA.POPUL
COVARIANCE.S = KOWARIANCJA.PRÓBKI
DEVSQ = ODCH.KWADRATOWE
EXPON.DIST = ROZKŁ.EXP
F.DIST = ROZKŁ.F
F.DIST.RT = ROZKŁ.F.PS
F.INV = ROZKŁ.F.ODWR
F.INV.RT = ROZKŁ.F.ODWR.PS
F.TEST = F.TEST
FISHER = ROZKŁAD.FISHER
FISHERINV = ROZKŁAD.FISHER.ODW
FORECAST.ETS = REGLINX.ETS
FORECAST.ETS.CONFINT = REGLINX.ETS.CONFINT
FORECAST.ETS.SEASONALITY = REGLINX.ETS.SEZONOWOŚĆ
FORECAST.ETS.STAT = REGLINX.ETS.STATYSTYKA
FORECAST.LINEAR = REGLINX.LINIOWA
FREQUENCY = CZĘSTOŚĆ
GAMMA = GAMMA
GAMMA.DIST = ROZKŁ.GAMMA
GAMMA.INV = ROZKŁ.GAMMA.ODWR
GAMMALN = ROZKŁAD.LIN.GAMMA
GAMMALN.PRECISE = ROZKŁAD.LIN.GAMMA.DOKŁ
GAUSS = GAUSS
GEOMEAN = ŚREDNIA.GEOMETRYCZNA
GROWTH = REGEXPW
HARMEAN = ŚREDNIA.HARMONICZNA
HYPGEOM.DIST = ROZKŁ.HIPERGEOM
INTERCEPT = ODCIĘTA
KURT = KURTOZA
LARGE = MAX.K
LINEST = REGLINP
LOGEST = REGEXPP
LOGNORM.DIST = ROZKŁ.LOG
LOGNORM.INV = ROZKŁ.LOG.ODWR
MAX = MAX
MAXA = MAX.A
MAXIFS = MAKS.WARUNKÓW
MEDIAN = MEDIANA
MIN = MIN
MINA = MIN.A
MINIFS = MIN.WARUNKÓW
MODE.MULT = WYST.NAJCZĘŚCIEJ.TABL
MODE.SNGL = WYST.NAJCZĘŚCIEJ.WART
NEGBINOM.DIST = ROZKŁ.DWUM.PRZEC
NORM.DIST = ROZKŁ.NORMALNY
NORM.INV = ROZKŁ.NORMALNY.ODWR
NORM.S.DIST = ROZKŁ.NORMALNY.S
NORM.S.INV = ROZKŁ.NORMALNY.S.ODWR
PEARSON = PEARSON
PERCENTILE.EXC = PERCENTYL.PRZEDZ.OTW
PERCENTILE.INC = PERCENTYL.PRZEDZ.ZAMK
PERCENTRANK.EXC = PROC.POZ.PRZEDZ.OTW
PERCENTRANK.INC = PROC.POZ.PRZEDZ.ZAMK
PERMUT = PERMUTACJE
PERMUTATIONA = PERMUTACJE.A
PHI = PHI
POISSON.DIST = ROZKŁ.POISSON
PROB = PRAWDPD
QUARTILE.EXC = KWARTYL.PRZEDZ.OTW
QUARTILE.INC = KWARTYL.PRZEDZ.ZAMK
RANK.AVG = POZYCJA.ŚR
RANK.EQ = POZYCJA.NAJW
RSQ = R.KWADRAT
SKEW = SKOŚNOŚĆ
SKEW.P = SKOŚNOŚĆ.P
SLOPE = NACHYLENIE
SMALL = MIN.K
STANDARDIZE = NORMALIZUJ
STDEV.P = ODCH.STAND.POPUL
STDEV.S = ODCH.STANDARD.PRÓBKI
STDEVA = ODCH.STANDARDOWE.A
STDEVPA = ODCH.STANDARD.POPUL.A
STEYX = REGBŁSTD
T.DIST = ROZKŁ.T
T.DIST.2T = ROZKŁ.T.DS
T.DIST.RT = ROZKŁ.T.PS
T.INV = ROZKŁ.T.ODWR
T.INV.2T = ROZKŁ.T.ODWR.DS
T.TEST = T.TEST
TREND = REGLINW
TRIMMEAN = ŚREDNIA.WEWN
VAR.P = WARIANCJA.POP
VAR.S = WARIANCJA.PRÓBKI
VARA = WARIANCJA.A
VARPA = WARIANCJA.POPUL.A
WEIBULL.DIST = ROZKŁ.WEIBULL
Z.TEST = Z.TEST

##
## Funkcje tekstowe (Text Functions)
##
BAHTTEXT = BAT.TEKST
CHAR = ZNAK
CLEAN = OCZYŚĆ
CODE = KOD
CONCAT = ZŁĄCZ.TEKST
DOLLAR = KWOTA
EXACT = PORÓWNAJ
FIND = ZNAJDŹ
FIXED = ZAOKR.DO.TEKST
ISTHAIDIGIT = CZY.CYFRA.TAJ
LEFT = LEWY
LEN = DŁ
LOWER = LITERY.MAŁE
MID = FRAGMENT.TEKSTU
NUMBERSTRING = LICZBA.CIĄG.ZNAK
NUMBERVALUE = WARTOŚĆ.LICZBOWA
PROPER = Z.WIELKIEJ.LITERY
REPLACE = ZASTĄP
REPT = POWT
RIGHT = PRAWY
SEARCH = SZUKAJ.TEKST
SUBSTITUTE = PODSTAW
T = T
TEXT = TEKST
TEXTJOIN = POŁĄCZ.TEKSTY
THAIDIGIT = TAJ.CYFRA
THAINUMSOUND = TAJ.DŹWIĘK.NUM
THAINUMSTRING = TAJ.CIĄG.NUM
THAISTRINGLENGTH = TAJ.DŁUGOŚĆ.CIĄGU
TRIM = USUŃ.ZBĘDNE.ODSTĘPY
UNICHAR = ZNAK.UNICODE
UNICODE = UNICODE
UPPER = LITERY.WIELKIE
VALUE = WARTOŚĆ

##
## Funkcje sieci Web (Web Functions)
##
ENCODEURL = ENCODEURL
FILTERXML = FILTERXML
WEBSERVICE = WEBSERVICE

##
## Funkcje zgodności (Compatibility Functions)
##
BETADIST = ROZKŁAD.BETA
BETAINV = ROZKŁAD.BETA.ODW
BINOMDIST = ROZKŁAD.DWUM
CEILING = ZAOKR.W.GÓRĘ
CHIDIST = ROZKŁAD.CHI
CHIINV = ROZKŁAD.CHI.ODW
CHITEST = TEST.CHI
CONCATENATE = ZŁĄCZ.TEKSTY
CONFIDENCE = UFNOŚĆ
COVAR = KOWARIANCJA
CRITBINOM = PRÓG.ROZKŁAD.DWUM
EXPONDIST = ROZKŁAD.EXP
FDIST = ROZKŁAD.F
FINV = ROZKŁAD.F.ODW
FLOOR = ZAOKR.W.DÓŁ
FORECAST = REGLINX
FTEST = TEST.F
GAMMADIST = ROZKŁAD.GAMMA
GAMMAINV = ROZKŁAD.GAMMA.ODW
HYPGEOMDIST = ROZKŁAD.HIPERGEOM
LOGINV = ROZKŁAD.LOG.ODW
LOGNORMDIST = ROZKŁAD.LOG
MODE = WYST.NAJCZĘŚCIEJ
NEGBINOMDIST = ROZKŁAD.DWUM.PRZEC
NORMDIST = ROZKŁAD.NORMALNY
NORMINV = ROZKŁAD.NORMALNY.ODW
NORMSDIST = ROZKŁAD.NORMALNY.S
NORMSINV = ROZKŁAD.NORMALNY.S.ODW
PERCENTILE = PERCENTYL
PERCENTRANK = PROCENT.POZYCJA
POISSON = ROZKŁAD.POISSON
QUARTILE = KWARTYL
RANK = POZYCJA
STDEV = ODCH.STANDARDOWE
STDEVP = ODCH.STANDARD.POPUL
TDIST = ROZKŁAD.T
TINV = ROZKŁAD.T.ODW
TTEST = TEST.T
VAR = WARIANCJA
VARP = WARIANCJA.POPUL
WEIBULL = ROZKŁAD.WEIBULL
ZTEST = TEST.Z
