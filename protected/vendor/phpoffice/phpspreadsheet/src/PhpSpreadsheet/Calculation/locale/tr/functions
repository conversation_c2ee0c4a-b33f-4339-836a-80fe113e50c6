############################################################
##
## PhpSpreadsheet - function name translations
##
## Türkçe (Turkish)
##
############################################################


##
## Küp işlevleri (Cube Functions)
##
CUBEKPIMEMBER = KÜPKPIÜYESİ
CUBEMEMBER = KÜPÜYESİ
CUBEMEMBERPROPERTY = KÜPÜYEÖZELLİĞİ
CUBERANKEDMEMBER = DERECELİKÜPÜYESİ
CUBESET = KÜPKÜMESİ
CUBESETCOUNT = KÜPKÜMESAYISI
CUBEVALUE = KÜPDEĞERİ

##
## Veritabanı işlevleri (Database Functions)
##
DAVERAGE = VSEÇORT
DCOUNT = VSEÇSAY
DCOUNTA = VSEÇSAYDOLU
DGET = VAL
DMAX = VSEÇMAK
DMIN = VSEÇMİN
DPRODUCT = VSEÇÇARP
DSTDEV = VSEÇSTDSAPMA
DSTDEVP = VSEÇSTDSAPMAS
DSUM = VSEÇTOPLA
DVAR = VSEÇVAR
DVARP = VSEÇVARS

##
## Tarih ve saat işlevleri (Date & Time Functions)
##
DATE = TARİH
DATEDIF = ETARİHLİ
DATESTRING = TARİHDİZİ
DATEVALUE = TARİHSAYISI
DAY = GÜN
DAYS = GÜNSAY
DAYS360 = GÜN360
EDATE = SERİTARİH
EOMONTH = SERİAY
HOUR = SAAT
ISOWEEKNUM = ISOHAFTASAY
MINUTE = DAKİKA
MONTH = AY
NETWORKDAYS = TAMİŞGÜNÜ
NETWORKDAYS.INTL = TAMİŞGÜNÜ.ULUSL
NOW = ŞİMDİ
SECOND = SANİYE
THAIDAYOFWEEK = TAYHAFTANINGÜNÜ
THAIMONTHOFYEAR = TAYYILINAYI
THAIYEAR = TAYYILI
TIME = ZAMAN
TIMEVALUE = ZAMANSAYISI
TODAY = BUGÜN
WEEKDAY = HAFTANINGÜNÜ
WEEKNUM = HAFTASAY
WORKDAY = İŞGÜNÜ
WORKDAY.INTL = İŞGÜNÜ.ULUSL
YEAR = YIL
YEARFRAC = YILORAN

##
## Mühendislik işlevleri (Engineering Functions)
##
BESSELI = BESSELI
BESSELJ = BESSELJ
BESSELK = BESSELK
BESSELY = BESSELY
BIN2DEC = BIN2DEC
BIN2HEX = BIN2HEX
BIN2OCT = BIN2OCT
BITAND = BİTVE
BITLSHIFT = BİTSOLAKAYDIR
BITOR = BİTVEYA
BITRSHIFT = BİTSAĞAKAYDIR
BITXOR = BİTÖZELVEYA
COMPLEX = KARMAŞIK
CONVERT = ÇEVİR
DEC2BIN = DEC2BIN
DEC2HEX = DEC2HEX
DEC2OCT = DEC2OCT
DELTA = DELTA
ERF = HATAİŞLEV
ERF.PRECISE = HATAİŞLEV.DUYARLI
ERFC = TÜMHATAİŞLEV
ERFC.PRECISE = TÜMHATAİŞLEV.DUYARLI
GESTEP = BESINIR
HEX2BIN = HEX2BIN
HEX2DEC = HEX2DEC
HEX2OCT = HEX2OCT
IMABS = SANMUTLAK
IMAGINARY = SANAL
IMARGUMENT = SANBAĞ_DEĞİŞKEN
IMCONJUGATE = SANEŞLENEK
IMCOS = SANCOS
IMCOSH = SANCOSH
IMCOT = SANCOT
IMCSC = SANCSC
IMCSCH = SANCSCH
IMDIV = SANBÖL
IMEXP = SANÜS
IMLN = SANLN
IMLOG10 = SANLOG10
IMLOG2 = SANLOG2
IMPOWER = SANKUVVET
IMPRODUCT = SANÇARP
IMREAL = SANGERÇEK
IMSEC = SANSEC
IMSECH = SANSECH
IMSIN = SANSIN
IMSINH = SANSINH
IMSQRT = SANKAREKÖK
IMSUB = SANTOPLA
IMSUM = SANÇIKAR
IMTAN = SANTAN
OCT2BIN = OCT2BIN
OCT2DEC = OCT2DEC
OCT2HEX = OCT2HEX

##
## Finansal işlevler (Financial Functions)
##
ACCRINT = GERÇEKFAİZ
ACCRINTM = GERÇEKFAİZV
AMORDEGRC = AMORDEGRC
AMORLINC = AMORLINC
COUPDAYBS = KUPONGÜNBD
COUPDAYS = KUPONGÜN
COUPDAYSNC = KUPONGÜNDSK
COUPNCD = KUPONGÜNSKT
COUPNUM = KUPONSAYI
COUPPCD = KUPONGÜNÖKT
CUMIPMT = TOPÖDENENFAİZ
CUMPRINC = TOPANAPARA
DB = AZALANBAKİYE
DDB = ÇİFTAZALANBAKİYE
DISC = İNDİRİM
DOLLARDE = LİRAON
DOLLARFR = LİRAKES
DURATION = SÜRE
EFFECT = ETKİN
FV = GD
FVSCHEDULE = GDPROGRAM
INTRATE = FAİZORANI
IPMT = FAİZTUTARI
IRR = İÇ_VERİM_ORANI
ISPMT = ISPMT
MDURATION = MSÜRE
MIRR = D_İÇ_VERİM_ORANI
NOMINAL = NOMİNAL
NPER = TAKSİT_SAYISI
NPV = NBD
ODDFPRICE = TEKYDEĞER
ODDFYIELD = TEKYÖDEME
ODDLPRICE = TEKSDEĞER
ODDLYIELD = TEKSÖDEME
PDURATION = PSÜRE
PMT = DEVRESEL_ÖDEME
PPMT = ANA_PARA_ÖDEMESİ
PRICE = DEĞER
PRICEDISC = DEĞERİND
PRICEMAT = DEĞERVADE
PV = BD
RATE = FAİZ_ORANI
RECEIVED = GETİRİ
RRI = GERÇEKLEŞENYATIRIMGETİRİSİ
SLN = DA
SYD = YAT
TBILLEQ = HTAHEŞ
TBILLPRICE = HTAHDEĞER
TBILLYIELD = HTAHÖDEME
VDB = DAB
XIRR = AİÇVERİMORANI
XNPV = ANBD
YIELD = ÖDEME
YIELDDISC = ÖDEMEİND
YIELDMAT = ÖDEMEVADE

##
## Bilgi işlevleri (Information Functions)
##
CELL = HÜCRE
ERROR.TYPE = HATA.TİPİ
INFO = BİLGİ
ISBLANK = EBOŞSA
ISERR = EHATA
ISERROR = EHATALIYSA
ISEVEN = ÇİFTMİ
ISFORMULA = EFORMÜLSE
ISLOGICAL = EMANTIKSALSA
ISNA = EYOKSA
ISNONTEXT = EMETİNDEĞİLSE
ISNUMBER = ESAYIYSA
ISODD = TEKMİ
ISREF = EREFSE
ISTEXT = EMETİNSE
N = S
NA = YOKSAY
SHEET = SAYFA
SHEETS = SAYFALAR
TYPE = TÜR

##
## Mantıksal işlevler (Logical Functions)
##
AND = VE
FALSE = YANLIŞ
IF = EĞER
IFERROR = EĞERHATA
IFNA = EĞERYOKSA
IFS = ÇOKEĞER
NOT = DEĞİL
OR = YADA
SWITCH = İLKEŞLEŞEN
TRUE = DOĞRU
XOR = ÖZELVEYA

##
## Arama ve başvuru işlevleri (Lookup & Reference Functions)
##
ADDRESS = ADRES
AREAS = ALANSAY
CHOOSE = ELEMAN
COLUMN = SÜTUN
COLUMNS = SÜTUNSAY
FORMULATEXT = FORMÜLMETNİ
GETPIVOTDATA = ÖZETVERİAL
HLOOKUP = YATAYARA
HYPERLINK = KÖPRÜ
INDEX = İNDİS
INDIRECT = DOLAYLI
LOOKUP = ARA
MATCH = KAÇINCI
OFFSET = KAYDIR
ROW = SATIR
ROWS = SATIRSAY
RTD = GZV
TRANSPOSE = DEVRİK_DÖNÜŞÜM
VLOOKUP = DÜŞEYARA

##
## Matematik ve trigonometri işlevleri (Math & Trig Functions)
##
ABS = MUTLAK
ACOS = ACOS
ACOSH = ACOSH
ACOT = ACOT
ACOTH = ACOTH
AGGREGATE = TOPLAMA
ARABIC = ARAP
ASIN = ASİN
ASINH = ASİNH
ATAN = ATAN
ATAN2 = ATAN2
ATANH = ATANH
BASE = TABAN
CEILING.MATH = TAVANAYUVARLA.MATEMATİK
CEILING.PRECISE = TAVANAYUVARLA.DUYARLI
COMBIN = KOMBİNASYON
COMBINA = KOMBİNASYONA
COS = COS
COSH = COSH
COT = COT
COTH = COTH
CSC = CSC
CSCH = CSCH
DECIMAL = ONDALIK
DEGREES = DERECE
ECMA.CEILING = ECMA.TAVAN
EVEN = ÇİFT
EXP = ÜS
FACT = ÇARPINIM
FACTDOUBLE = ÇİFTFAKTÖR
FLOOR.MATH = TABANAYUVARLA.MATEMATİK
FLOOR.PRECISE = TABANAYUVARLA.DUYARLI
GCD = OBEB
INT = TAMSAYI
ISO.CEILING = ISO.TAVAN
LCM = OKEK
LN = LN
LOG = LOG
LOG10 = LOG10
MDETERM = DETERMİNANT
MINVERSE = DİZEY_TERS
MMULT = DÇARP
MOD = MOD
MROUND = KYUVARLA
MULTINOMIAL = ÇOKTERİMLİ
MUNIT = BİRİMMATRİS
ODD = TEK
PI = Pİ
POWER = KUVVET
PRODUCT = ÇARPIM
QUOTIENT = BÖLÜM
RADIANS = RADYAN
RAND = S_SAYI_ÜRET
RANDBETWEEN = RASTGELEARADA
ROMAN = ROMEN
ROUND = YUVARLA
ROUNDBAHTDOWN = BAHTAŞAĞIYUVARLA
ROUNDBAHTUP = BAHTYUKARIYUVARLA
ROUNDDOWN = AŞAĞIYUVARLA
ROUNDUP = YUKARIYUVARLA
SEC = SEC
SECH = SECH
SERIESSUM = SERİTOPLA
SIGN = İŞARET
SIN = SİN
SINH = SİNH
SQRT = KAREKÖK
SQRTPI = KAREKÖKPİ
SUBTOTAL = ALTTOPLAM
SUM = TOPLA
SUMIF = ETOPLA
SUMIFS = ÇOKETOPLA
SUMPRODUCT = TOPLA.ÇARPIM
SUMSQ = TOPKARE
SUMX2MY2 = TOPX2EY2
SUMX2PY2 = TOPX2AY2
SUMXMY2 = TOPXEY2
TAN = TAN
TANH = TANH
TRUNC = NSAT

##
## İstatistik işlevleri (Statistical Functions)
##
AVEDEV = ORTSAP
AVERAGE = ORTALAMA
AVERAGEA = ORTALAMAA
AVERAGEIF = EĞERORTALAMA
AVERAGEIFS = ÇOKEĞERORTALAMA
BETA.DIST = BETA.DAĞ
BETA.INV = BETA.TERS
BINOM.DIST = BİNOM.DAĞ
BINOM.DIST.RANGE = BİNOM.DAĞ.ARALIK
BINOM.INV = BİNOM.TERS
CHISQ.DIST = KİKARE.DAĞ
CHISQ.DIST.RT = KİKARE.DAĞ.SAĞK
CHISQ.INV = KİKARE.TERS
CHISQ.INV.RT = KİKARE.TERS.SAĞK
CHISQ.TEST = KİKARE.TEST
CONFIDENCE.NORM = GÜVENİLİRLİK.NORM
CONFIDENCE.T = GÜVENİLİRLİK.T
CORREL = KORELASYON
COUNT = BAĞ_DEĞ_SAY
COUNTA = BAĞ_DEĞ_DOLU_SAY
COUNTBLANK = BOŞLUKSAY
COUNTIF = EĞERSAY
COUNTIFS = ÇOKEĞERSAY
COVARIANCE.P = KOVARYANS.P
COVARIANCE.S = KOVARYANS.S
DEVSQ = SAPKARE
EXPON.DIST = ÜSTEL.DAĞ
F.DIST = F.DAĞ
F.DIST.RT = F.DAĞ.SAĞK
F.INV = F.TERS
F.INV.RT = F.TERS.SAĞK
F.TEST = F.TEST
FISHER = FISHER
FISHERINV = FISHERTERS
FORECAST.ETS = TAHMİN.ETS
FORECAST.ETS.CONFINT = TAHMİN.ETS.GVNARAL
FORECAST.ETS.SEASONALITY = TAHMİN.ETS.MEVSİMSELLİK
FORECAST.ETS.STAT = TAHMİN.ETS.İSTAT
FORECAST.LINEAR = TAHMİN.DOĞRUSAL
FREQUENCY = SIKLIK
GAMMA = GAMA
GAMMA.DIST = GAMA.DAĞ
GAMMA.INV = GAMA.TERS
GAMMALN = GAMALN
GAMMALN.PRECISE = GAMALN.DUYARLI
GAUSS = GAUSS
GEOMEAN = GEOORT
GROWTH = BÜYÜME
HARMEAN = HARORT
HYPGEOM.DIST = HİPERGEOM.DAĞ
INTERCEPT = KESMENOKTASI
KURT = BASIKLIK
LARGE = BÜYÜK
LINEST = DOT
LOGEST = LOT
LOGNORM.DIST = LOGNORM.DAĞ
LOGNORM.INV = LOGNORM.TERS
MAX = MAK
MAXA = MAKA
MAXIFS = ÇOKEĞERMAK
MEDIAN = ORTANCA
MIN = MİN
MINA = MİNA
MINIFS = ÇOKEĞERMİN
MODE.MULT = ENÇOK_OLAN.ÇOK
MODE.SNGL = ENÇOK_OLAN.TEK
NEGBINOM.DIST = NEGBİNOM.DAĞ
NORM.DIST = NORM.DAĞ
NORM.INV = NORM.TERS
NORM.S.DIST = NORM.S.DAĞ
NORM.S.INV = NORM.S.TERS
PEARSON = PEARSON
PERCENTILE.EXC = YÜZDEBİRLİK.HRC
PERCENTILE.INC = YÜZDEBİRLİK.DHL
PERCENTRANK.EXC = YÜZDERANK.HRC
PERCENTRANK.INC = YÜZDERANK.DHL
PERMUT = PERMÜTASYON
PERMUTATIONA = PERMÜTASYONA
PHI = PHI
POISSON.DIST = POISSON.DAĞ
PROB = OLASILIK
QUARTILE.EXC = DÖRTTEBİRLİK.HRC
QUARTILE.INC = DÖRTTEBİRLİK.DHL
RANK.AVG = RANK.ORT
RANK.EQ = RANK.EŞİT
RSQ = RKARE
SKEW = ÇARPIKLIK
SKEW.P = ÇARPIKLIK.P
SLOPE = EĞİM
SMALL = KÜÇÜK
STANDARDIZE = STANDARTLAŞTIRMA
STDEV.P = STDSAPMA.P
STDEV.S = STDSAPMA.S
STDEVA = STDSAPMAA
STDEVPA = STDSAPMASA
STEYX = STHYX
T.DIST = T.DAĞ
T.DIST.2T = T.DAĞ.2K
T.DIST.RT = T.DAĞ.SAĞK
T.INV = T.TERS
T.INV.2T = T.TERS.2K
T.TEST = T.TEST
TREND = EĞİLİM
TRIMMEAN = KIRPORTALAMA
VAR.P = VAR.P
VAR.S = VAR.S
VARA = VARA
VARPA = VARSA
WEIBULL.DIST = WEIBULL.DAĞ
Z.TEST = Z.TEST

##
## Metin işlevleri (Text Functions)
##
BAHTTEXT = BAHTMETİN
CHAR = DAMGA
CLEAN = TEMİZ
CODE = KOD
CONCAT = ARALIKBİRLEŞTİR
DOLLAR = LİRA
EXACT = ÖZDEŞ
FIND = BUL
FIXED = SAYIDÜZENLE
ISTHAIDIGIT = TAYRAKAMIYSA
LEFT = SOLDAN
LEN = UZUNLUK
LOWER = KÜÇÜKHARF
MID = PARÇAAL
NUMBERSTRING = SAYIDİZİ
NUMBERVALUE = SAYIDEĞERİ
PHONETIC = SES
PROPER = YAZIM.DÜZENİ
REPLACE = DEĞİŞTİR
REPT = YİNELE
RIGHT = SAĞDAN
SEARCH = MBUL
SUBSTITUTE = YERİNEKOY
T = M
TEXT = METNEÇEVİR
TEXTJOIN = METİNBİRLEŞTİR
THAIDIGIT = TAYRAKAM
THAINUMSOUND = TAYSAYISES
THAINUMSTRING = TAYSAYIDİZE
THAISTRINGLENGTH = TAYDİZEUZUNLUĞU
TRIM = KIRP
UNICHAR = UNICODEKARAKTERİ
UNICODE = UNICODE
UPPER = BÜYÜKHARF
VALUE = SAYIYAÇEVİR

##
## Metin işlevleri (Web Functions)
##
ENCODEURL = URLKODLA
FILTERXML = XMLFİLTRELE
WEBSERVICE = WEBHİZMETİ

##
## Uyumluluk işlevleri (Compatibility Functions)
##
BETADIST = BETADAĞ
BETAINV = BETATERS
BINOMDIST = BİNOMDAĞ
CEILING = TAVANAYUVARLA
CHIDIST = KİKAREDAĞ
CHIINV = KİKARETERS
CHITEST = KİKARETEST
CONCATENATE = BİRLEŞTİR
CONFIDENCE = GÜVENİRLİK
COVAR = KOVARYANS
CRITBINOM = KRİTİKBİNOM
EXPONDIST = ÜSTELDAĞ
FDIST = FDAĞ
FINV = FTERS
FLOOR = TABANAYUVARLA
FORECAST = TAHMİN
FTEST = FTEST
GAMMADIST = GAMADAĞ
GAMMAINV = GAMATERS
HYPGEOMDIST = HİPERGEOMDAĞ
LOGINV = LOGTERS
LOGNORMDIST = LOGNORMDAĞ
MODE = ENÇOK_OLAN
NEGBINOMDIST = NEGBİNOMDAĞ
NORMDIST = NORMDAĞ
NORMINV = NORMTERS
NORMSDIST = NORMSDAĞ
NORMSINV = NORMSTERS
PERCENTILE = YÜZDEBİRLİK
PERCENTRANK = YÜZDERANK
POISSON = POISSON
QUARTILE = DÖRTTEBİRLİK
RANK = RANK
STDEV = STDSAPMA
STDEVP = STDSAPMAS
TDIST = TDAĞ
TINV = TTERS
TTEST = TTEST
VAR = VAR
VARP = VARS
WEIBULL = WEIBULL
ZTEST = ZTEST
