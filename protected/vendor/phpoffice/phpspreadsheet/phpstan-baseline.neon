parameters:
	ignoreErrors:
		-
			message: "#^Argument of an invalid type array\\<int, string\\>\\|false supplied for foreach, only iterables are supported\\.$#"
			count: 3
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Cannot call method attach\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Cell\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Cannot call method cellExists\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 4
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Cannot call method getCell\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 6
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Cannot call method getHighestColumn\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Cannot call method getHighestRow\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:_translateFormulaToEnglish\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:_translateFormulaToEnglish\\(\\) has parameter \\$formula with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:_translateFormulaToLocale\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:_translateFormulaToLocale\\(\\) has parameter \\$formula with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:dataTestReference\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:dataTestReference\\(\\) has parameter \\$operandData with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:getTokensAsString\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:getTokensAsString\\(\\) has parameter \\$tokens with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:localeFunc\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:localeFunc\\(\\) has parameter \\$function with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:validateBinaryOperand\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:validateBinaryOperand\\(\\) has parameter \\$operand with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:validateBinaryOperand\\(\\) has parameter \\$stack with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Offset 'value' does not exist on array\\|null\\.$#"
			count: 5
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Parameter \\#1 \\$haystack of function stripos expects string, float\\|int\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Parameter \\#1 \\$haystack of function strpos expects string, float\\|int\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Parameter \\#1 \\$str of function preg_quote expects string, int\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Parameter \\#2 \\$worksheet of static method PhpOffice\\\\PhpSpreadsheet\\\\DefinedName\\:\\:resolveName\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet, PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$cellStack has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$comparisonOperators has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$controlFunctions has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$cyclicFormulaCell has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$functionReplaceFromExcel has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$functionReplaceFromLocale has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$functionReplaceToExcel has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$functionReplaceToLocale has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$localeFunctions has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$operatorAssociativity has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$operatorPrecedence has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$phpSpreadsheetFunctions has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$returnArrayAsType has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$spreadsheet \\(PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Static property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\:\\:\\$instance \\(PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Calculation\\) in isset\\(\\) is not nullable\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Strict comparison using \\=\\=\\= between array and '\\(' will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Strict comparison using \\=\\=\\= between non\\-empty\\-array and null will always evaluate to false\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Calculation/Calculation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\:\\:DMAX\\(\\) should return float but returns float\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\:\\:DMIN\\(\\) should return float but returns float\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\:\\:DPRODUCT\\(\\) should return float\\|string but returns float\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\:\\:DSTDEV\\(\\) should return float\\|string but returns float\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\:\\:DSTDEVP\\(\\) should return float\\|string but returns float\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\:\\:DSUM\\(\\) should return float\\|string but returns float\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\:\\:DVAR\\(\\) should return float\\|string but returns float\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\:\\:DVARP\\(\\) should return float\\|string but returns float\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database.php

		-
			message: "#^Parameter \\#2 \\$field of static method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\\\DCountA\\:\\:evaluate\\(\\) expects int\\|string, int\\|string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\\\DatabaseAbstract\\:\\:buildCondition\\(\\) has parameter \\$criterion with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database/DatabaseAbstract.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\\\DatabaseAbstract\\:\\:evaluate\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database/DatabaseAbstract.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\\\DatabaseAbstract\\:\\:evaluate\\(\\) has parameter \\$criteria with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database/DatabaseAbstract.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\\\DatabaseAbstract\\:\\:evaluate\\(\\) has parameter \\$database with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database/DatabaseAbstract.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\\\DatabaseAbstract\\:\\:evaluate\\(\\) has parameter \\$field with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database/DatabaseAbstract.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Database\\\\DatabaseAbstract\\:\\:processCondition\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Database/DatabaseAbstract.php

		-
			message: "#^Variable \\$dateValue on left side of \\?\\? always exists and is not nullable\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/DateTimeExcel/DateValue.php

		-
			message: "#^Variable \\$timeValue on left side of \\?\\? always exists and is not nullable\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/DateTimeExcel/TimeValue.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\BesselJ\\:\\:besselj2a\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/BesselJ.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\BesselJ\\:\\:besselj2b\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/BesselJ.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\ConvertBase\\:\\:validatePlaces\\(\\) has parameter \\$places with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/ConvertBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\ConvertBase\\:\\:validateValue\\(\\) has parameter \\$value with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/ConvertBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\ConvertUOM\\:\\:getUOMDetails\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/ConvertUOM.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\ConvertUOM\\:\\:resolveTemperatureSynonyms\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/ConvertUOM.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\Erf\\:\\:erfValue\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/Erf.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\Erf\\:\\:erfValue\\(\\) has parameter \\$value with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/Erf.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\Erf\\:\\:\\$twoSqrtPi has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/Erf.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\ErfC\\:\\:erfcValue\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/ErfC.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\ErfC\\:\\:erfcValue\\(\\) has parameter \\$value with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/ErfC.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Engineering\\\\ErfC\\:\\:\\$oneSqrtPi has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Engineering/ErfC.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\:\\:ISPMT\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\:\\:ISPMT\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\:\\:NPV\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial.php

		-
			message: "#^Parameter \\#1 \\$year of static method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\DateTimeExcel\\\\Helpers\\:\\:isLeapYear\\(\\) expects int\\|string, array\\|int\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/Amortization.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\CashFlow\\\\Constant\\\\Periodic\\\\Interest\\:\\:rateNextGuess\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/Interest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\CashFlow\\\\Constant\\\\Periodic\\\\Interest\\:\\:rateNextGuess\\(\\) has parameter \\$futureValue with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/Interest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\CashFlow\\\\Constant\\\\Periodic\\\\Interest\\:\\:rateNextGuess\\(\\) has parameter \\$numberOfPeriods with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/Interest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\CashFlow\\\\Constant\\\\Periodic\\\\Interest\\:\\:rateNextGuess\\(\\) has parameter \\$payment with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/Interest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\CashFlow\\\\Constant\\\\Periodic\\\\Interest\\:\\:rateNextGuess\\(\\) has parameter \\$presentValue with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/Interest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\CashFlow\\\\Constant\\\\Periodic\\\\Interest\\:\\:rateNextGuess\\(\\) has parameter \\$rate with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/Interest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\CashFlow\\\\Constant\\\\Periodic\\\\Interest\\:\\:rateNextGuess\\(\\) has parameter \\$type with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/Interest.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\CashFlow\\\\Constant\\\\Periodic\\\\Interest\\:\\:schedulePayment\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/Interest.php

		-
			message: "#^Binary operation \"\\-\" between float\\|string and 0\\|float results in an error\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/InterestAndPrincipal.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\CashFlow\\\\Constant\\\\Periodic\\\\InterestAndPrincipal\\:\\:\\$interest has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/InterestAndPrincipal.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\CashFlow\\\\Constant\\\\Periodic\\\\InterestAndPrincipal\\:\\:\\$principal has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/InterestAndPrincipal.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\CashFlow\\\\Variable\\\\NonPeriodic\\:\\:xnpvOrdered\\(\\) should return float\\|string but returns array\\|string\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Variable/NonPeriodic.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\CashFlow\\\\Variable\\\\Periodic\\:\\:presentValue\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/CashFlow/Variable/Periodic.php

		-
			message: "#^Parameter \\#1 \\$year of static method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\Helpers\\:\\:daysPerYear\\(\\) expects int\\|string, array\\|int\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/Coupons.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\Depreciation\\:\\:validateCost\\(\\) has parameter \\$cost with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/Depreciation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\Depreciation\\:\\:validateFactor\\(\\) has parameter \\$factor with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/Depreciation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\Depreciation\\:\\:validateLife\\(\\) has parameter \\$life with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/Depreciation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\Depreciation\\:\\:validateMonth\\(\\) has parameter \\$month with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/Depreciation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\Depreciation\\:\\:validatePeriod\\(\\) has parameter \\$period with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/Depreciation.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\Depreciation\\:\\:validateSalvage\\(\\) has parameter \\$salvage with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/Depreciation.php

		-
			message: "#^Binary operation \"/\" between float\\|string and float\\|string results in an error\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Calculation/Financial/Securities/Price.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\Securities\\\\Price\\:\\:received\\(\\) should return float\\|string but returns array\\|string\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/Securities/Price.php

		-
			message: "#^Parameter \\#1 \\$year of static method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\Helpers\\:\\:daysPerYear\\(\\) expects int\\|string, array\\|int\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Financial/Securities/Price.php

		-
			message: "#^Parameter \\#1 \\$year of static method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Financial\\\\Helpers\\:\\:daysPerYear\\(\\) expects int\\|string, array\\|int\\|string given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Calculation/Financial/Securities/Yields.php

		-
			message: "#^Cannot call method getTokenSubType\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\FormulaToken\\|null\\.$#"
			count: 4
			path: src/PhpSpreadsheet/Calculation/FormulaParser.php

		-
			message: "#^Cannot call method getTokenType\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\FormulaToken\\|null\\.$#"
			count: 9
			path: src/PhpSpreadsheet/Calculation/FormulaParser.php

		-
			message: "#^Cannot call method setTokenSubType\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\FormulaToken\\|null\\.$#"
			count: 5
			path: src/PhpSpreadsheet/Calculation/FormulaParser.php

		-
			message: "#^Cannot call method setValue\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\FormulaToken\\|null\\.$#"
			count: 5
			path: src/PhpSpreadsheet/Calculation/FormulaParser.php

		-
			message: "#^Strict comparison using \\=\\=\\= between PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\FormulaToken and null will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/FormulaParser.php

		-
			message: "#^Strict comparison using \\=\\=\\= between string and null will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/FormulaParser.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Functions\\:\\:ifCondition\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Functions.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Functions\\:\\:ifCondition\\(\\) has parameter \\$condition with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Functions.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Functions\\:\\:isCellValue\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Functions.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Functions\\:\\:isCellValue\\(\\) has parameter \\$idx with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Functions.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Functions\\:\\:isMatrixValue\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Functions.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Functions\\:\\:isMatrixValue\\(\\) has parameter \\$idx with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Functions.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Functions\\:\\:isValue\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Functions.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Functions\\:\\:isValue\\(\\) has parameter \\$idx with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Functions.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Functions\\:\\:operandSpecialHandling\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Functions.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Functions\\:\\:operandSpecialHandling\\(\\) has parameter \\$operand with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Functions.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Internal\\\\MakeMatrix\\:\\:make\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Internal/MakeMatrix.php

		-
			message: "#^Call to function is_string\\(\\) with null will always evaluate to false\\.$#"
			count: 3
			path: src/PhpSpreadsheet/Calculation/Logical/Operations.php

		-
			message: "#^Result of && is always false\\.$#"
			count: 3
			path: src/PhpSpreadsheet/Calculation/Logical/Operations.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\:\\:CHOOSE\\(\\) has parameter \\$chooseArgs with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\:\\:OFFSET\\(\\) should return array\\|string but returns array\\|int\\|string\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Address\\:\\:sheetName\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Address.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:matchFirstValue\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:matchFirstValue\\(\\) has parameter \\$lookupArray with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:matchFirstValue\\(\\) has parameter \\$lookupValue with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:matchLargestValue\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:matchLargestValue\\(\\) has parameter \\$keySet with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:matchLargestValue\\(\\) has parameter \\$lookupArray with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:matchLargestValue\\(\\) has parameter \\$lookupValue with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:matchSmallestValue\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:matchSmallestValue\\(\\) has parameter \\$lookupArray with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:matchSmallestValue\\(\\) has parameter \\$lookupValue with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:prepareLookupArray\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:prepareLookupArray\\(\\) has parameter \\$lookupArray with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:prepareLookupArray\\(\\) has parameter \\$matchType with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:validateLookupArray\\(\\) has parameter \\$lookupArray with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:validateLookupValue\\(\\) has parameter \\$lookupValue with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\ExcelMatch\\:\\:validateMatchType\\(\\) has parameter \\$matchType with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Lookup\\:\\:verifyResultVector\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Lookup.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Lookup\\:\\:verifyResultVector\\(\\) has parameter \\$resultVector with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Lookup.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\LookupBase\\:\\:validateIndexLookup\\(\\) has parameter \\$index_number with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/LookupBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Matrix\\:\\:extractRowValue\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Offset\\:\\:adjustEndCellColumnForWidth\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Offset.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Offset\\:\\:adjustEndCellColumnForWidth\\(\\) has parameter \\$columns with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Offset.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Offset\\:\\:adjustEndCellColumnForWidth\\(\\) has parameter \\$width with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Offset.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Offset\\:\\:adustEndCellRowForHeight\\(\\) has parameter \\$endCellRow with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Offset.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Offset\\:\\:adustEndCellRowForHeight\\(\\) has parameter \\$height with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Offset.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Offset\\:\\:adustEndCellRowForHeight\\(\\) has parameter \\$rows with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Offset.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Offset\\:\\:extractRequiredCells\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Offset.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\LookupRef\\\\Offset\\:\\:extractWorksheet\\(\\) has parameter \\$cellAddress with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/LookupRef/Offset.php

		-
			message: "#^Binary operation \"/\" between array\\|float\\|int\\|string and array\\|float\\|int\\|string results in an error\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Calculation/MathTrig/Combinations.php

		-
			message: "#^Parameter \\#1 \\$factVal of static method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\MathTrig\\\\Factorial\\:\\:fact\\(\\) expects array\\|float, int\\<min, \\-2\\>\\|int\\<0, max\\> given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/MathTrig/Combinations.php

		-
			message: "#^Binary operation \"/\" between array\\|float\\|int\\|string and float\\|int results in an error\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/MathTrig/Factorial.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\MathTrig\\\\IntClass\\:\\:evaluate\\(\\) should return array\\|string but returns int\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/MathTrig/IntClass.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\:\\:MAXIFS\\(\\) should return float but returns float\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\:\\:MINIFS\\(\\) should return float but returns float\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Averages\\:\\:filterArguments\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Averages.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Averages\\:\\:filterArguments\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Averages.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Averages\\:\\:modeCalc\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Averages.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Averages\\:\\:modeCalc\\(\\) has parameter \\$data with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Averages.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Conditional\\:\\:SUMIF\\(\\) should return float\\|string but returns float\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Conditional.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Conditional\\:\\:buildConditionSet\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Conditional.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Conditional\\:\\:buildConditionSetForValueRange\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Conditional.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Conditional\\:\\:buildConditions\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Conditional.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Conditional\\:\\:buildDataSet\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Conditional.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Conditional\\:\\:buildDatabase\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Conditional.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Conditional\\:\\:buildDatabaseWithValueRange\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Conditional.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\Beta\\:\\:\\$logBetaCacheP has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/Beta.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\Beta\\:\\:\\$logBetaCacheQ has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/Beta.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\Beta\\:\\:\\$logBetaCacheResult has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/Beta.php

		-
			message: "#^Constant PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:MAX_ITERATIONS is unused\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:gammp\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:gammp\\(\\) has parameter \\$n with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:gammp\\(\\) has parameter \\$x with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:gcf\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:gcf\\(\\) has parameter \\$n with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:gcf\\(\\) has parameter \\$x with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:gser\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:gser\\(\\) has parameter \\$n with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:gser\\(\\) has parameter \\$x with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:pchisq\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:pchisq\\(\\) has parameter \\$chi2 with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:pchisq\\(\\) has parameter \\$degrees with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Parameter \\#2 \\$columns of static method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\ChiSquared\\:\\:degrees\\(\\) expects int, float\\|int\\<0, max\\> given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\GammaBase\\:\\:calculateDistribution\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/GammaBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\GammaBase\\:\\:calculateInverse\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/GammaBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\GammaBase\\:\\:logGamma1\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/GammaBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\GammaBase\\:\\:logGamma2\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/GammaBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\GammaBase\\:\\:logGamma3\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/GammaBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\GammaBase\\:\\:logGamma4\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/GammaBase.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\GammaBase\\:\\:\\$logGammaCacheResult has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/GammaBase.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\GammaBase\\:\\:\\$logGammaCacheX has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/GammaBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\NewtonRaphson\\:\\:execute\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/NewtonRaphson.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\NewtonRaphson\\:\\:\\$callback has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/NewtonRaphson.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\Normal\\:\\:inverseNcdf\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/Normal.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\Normal\\:\\:inverseNcdf\\(\\) has parameter \\$p with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/Normal.php

		-
			message: "#^Parameter \\#1 \\$factVal of static method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\MathTrig\\\\Factorial\\:\\:fact\\(\\) expects array\\|float, int\\<0, max\\> given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/Poisson.php

		-
			message: "#^Binary operation \"\\-\" between float\\|string and float\\|int\\|numeric\\-string results in an error\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/StandardNormal.php

		-
			message: "#^Constant PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Distributions\\\\StudentT\\:\\:MAX_ITERATIONS is unused\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Distributions/StudentT.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\MaxMinBase\\:\\:datatypeAdjustmentAllowStrings\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/MaxMinBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\MaxMinBase\\:\\:datatypeAdjustmentAllowStrings\\(\\) has parameter \\$value with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/MaxMinBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Percentiles\\:\\:percentileFilterValues\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Percentiles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Percentiles\\:\\:rankFilterValues\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Percentiles.php

		-
			message: "#^Binary operation \"/\" between array\\|float\\|int\\|string and array\\|float\\|int\\|string results in an error\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Permutations.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Trends\\:\\:GROWTH\\(\\) should return array\\<float\\> but returns array\\<int, array\\<int, array\\<int, mixed\\>\\>\\>\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Trends.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Trends\\:\\:TREND\\(\\) should return array\\<float\\> but returns array\\<int, array\\<int, array\\<int, mixed\\>\\>\\>\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Trends.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Trends\\:\\:checkTrendArrays\\(\\) has parameter \\$array1 with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Trends.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\Trends\\:\\:checkTrendArrays\\(\\) has parameter \\$array2 with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/Trends.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\VarianceBase\\:\\:datatypeAdjustmentAllowStrings\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/VarianceBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\VarianceBase\\:\\:datatypeAdjustmentAllowStrings\\(\\) has parameter \\$value with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/VarianceBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\VarianceBase\\:\\:datatypeAdjustmentBooleans\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/VarianceBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\Statistical\\\\VarianceBase\\:\\:datatypeAdjustmentBooleans\\(\\) has parameter \\$value with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/Statistical/VarianceBase.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Calculation\\\\TextData\\:\\:CONCATENATE\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/TextData.php

		-
			message: "#^Variable \\$value on left side of \\?\\? always exists and is not nullable\\.$#"
			count: 4
			path: src/PhpSpreadsheet/Calculation/TextData/Extract.php

		-
			message: "#^Variable \\$value on left side of \\?\\? always exists and is not nullable\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Calculation/TextData/Text.php

		-
			message: "#^Elseif branch is unreachable because previous condition is always true\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Cell/Cell.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Cell\\:\\:getFormulaAttributes\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Cell/Cell.php

		-
			message: "#^Parameter \\#2 \\$format of static method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\:\\:toFormattedString\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Cell/Cell.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Cell\\:\\:\\$formulaAttributes has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Cell/Cell.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Cell\\:\\:\\$parent \\(PhpOffice\\\\PhpSpreadsheet\\\\Collection\\\\Cells\\) in isset\\(\\) is not nullable\\.$#"
			count: 6
			path: src/PhpSpreadsheet/Cell/Cell.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Cell/Cell.php

		-
			message: "#^Call to an undefined method object\\:\\:getHashCode\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Cell/Coordinate.php

		-
			message: "#^Parameter \\#4 \\$currentRow of static method PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Coordinate\\:\\:validateRange\\(\\) expects int, string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Cell/Coordinate.php

		-
			message: "#^Parameter \\#5 \\$endRow of static method PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Coordinate\\:\\:validateRange\\(\\) expects int, string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Cell/Coordinate.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Collection\\\\Memory\\:\\:\\$cache has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Collection/Memory.php

		-
			message: "#^Parameter \\#1 \\$namedRange of method PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\:\\:addNamedRange\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\NamedRange, \\$this\\(PhpOffice\\\\PhpSpreadsheet\\\\DefinedName\\) given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/DefinedName.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\DefinedName\\:\\:\\$scope \\(PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 3
			path: src/PhpSpreadsheet/DefinedName.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\DefinedName\\:\\:\\$worksheet \\(PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/DefinedName.php

		-
			message: "#^Cannot call method setBold\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Cannot call method setColor\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Cannot call method setItalic\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Cannot call method setName\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Cannot call method setSize\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Cannot call method setStrikethrough\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Cannot call method setSubscript\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Cannot call method setSuperscript\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Cannot call method setUnderline\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:startFontTag\\(\\) has parameter \\$tag with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$bold has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$color has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$colourMap has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$endTagCallbacks has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$face has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$italic has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$size has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$stack has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$startTagCallbacks has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$strikethrough has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$stringData has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$subscript has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$superscript has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Html\\:\\:\\$underline has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Sample\\:\\:getSamples\\(\\) should return array\\<array\\<string\\>\\> but returns array\\<string, array\\<string, array\\|string\\>\\>\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Sample.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Helper\\\\Sample\\:\\:log\\(\\) has parameter \\$message with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Sample.php

		-
			message: "#^Parameter \\#1 \\$directory of class RecursiveDirectoryIterator constructor expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Sample.php

		-
			message: "#^Parameter \\#1 \\$filename of function unlink expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Sample.php

		-
			message: "#^Parameter \\#1 \\$path of function pathinfo expects string, array\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Sample.php

		-
			message: "#^Parameter \\#3 \\$subject of function str_replace expects array\\|string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Helper/Sample.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\IOFactory\\:\\:createReader\\(\\) should return PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\IReader but returns object\\.$#"
			count: 1
			path: src/PhpSpreadsheet/IOFactory.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\IOFactory\\:\\:createWriter\\(\\) should return PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\IWriter but returns object\\.$#"
			count: 1
			path: src/PhpSpreadsheet/IOFactory.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\IOFactory\\:\\:\\$readers has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/IOFactory.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\IOFactory\\:\\:\\$writers has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/IOFactory.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\BaseReader\\:\\:getSecurityScanner\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/BaseReader.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\BaseReader\\:\\:\\$fileHandle has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/BaseReader.php

		-
			message: "#^Comparison operation \"\\<\" between int and SimpleXMLElement\\|null results in an error\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Reader/Gnumeric.php

		-
			message: "#^Offset 'percentage' does not exist on SimpleXMLElement\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Gnumeric/PageSetup.php

		-
			message: "#^Offset 'value' does not exist on SimpleXMLElement\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Reader/Gnumeric/PageSetup.php

		-
			message: "#^Variable \\$orientation on left side of \\?\\? always exists and is not nullable\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Gnumeric/PageSetup.php

		-
			message: "#^Comparison operation \"\\<\\=\" between SimpleXMLElement\\|null and int results in an error\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Reader/Gnumeric/Styles.php

		-
			message: "#^Comparison operation \"\\>\" between SimpleXMLElement\\|null and int results in an error\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Reader/Gnumeric/Styles.php

		-
			message: "#^Variable \\$value on left side of \\?\\? always exists and is not nullable\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Html.php

		-
			message: "#^Cannot call method children\\(\\) on SimpleXMLElement\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods.php

		-
			message: "#^Cannot call method getAttributeNS\\(\\) on DOMElement\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods.php

		-
			message: "#^Cannot call method getElementsByTagNameNS\\(\\) on DOMElement\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Reader/Ods.php

		-
			message: "#^Cannot call method getNamedItem\\(\\) on DOMNamedNodeMap\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods.php

		-
			message: "#^Cannot call method getNamespaces\\(\\) on SimpleXMLElement\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods.php

		-
			message: "#^Cannot call method setSelectedCellByColumnAndRow\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\:\\:listWorksheetNames\\(\\) should return array\\<string\\> but returns array\\<int, string\\|null\\>\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods.php

		-
			message: "#^Parameter \\#1 \\$element of method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\:\\:scanElementForText\\(\\) expects DOMNode, DOMElement\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods.php

		-
			message: "#^Parameter \\#1 \\$settings of method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\:\\:lookForActiveSheet\\(\\) expects DOMElement, DOMElement\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods.php

		-
			message: "#^Parameter \\#1 \\$settings of method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\:\\:lookForSelectedCells\\(\\) expects DOMElement, DOMElement\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods.php

		-
			message: "#^Cannot call method getElementsByTagNameNS\\(\\) on DOMElement\\|null\\.$#"
			count: 3
			path: src/PhpSpreadsheet/Reader/Ods/PageSettings.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 6
			path: src/PhpSpreadsheet/Reader/Ods/PageSettings.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\\\PageSettings\\:\\:\\$pageLayoutStyles has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods/PageSettings.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\\\Properties\\:\\:load\\(\\) has parameter \\$namespacesMeta with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods/Properties.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\\\Properties\\:\\:setMetaProperties\\(\\) has parameter \\$namespacesMeta with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods/Properties.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\\\Properties\\:\\:setMetaProperties\\(\\) has parameter \\$propertyName with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods/Properties.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\\\Properties\\:\\:setUserDefinedProperty\\(\\) has parameter \\$propertyValue with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods/Properties.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\\\Properties\\:\\:setUserDefinedProperty\\(\\) has parameter \\$propertyValueAttributes with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods/Properties.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Ods\\\\Properties\\:\\:\\$spreadsheet has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Ods/Properties.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Security\\\\XmlScanner\\:\\:__construct\\(\\) has parameter \\$pattern with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Security/XmlScanner.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Security\\\\XmlScanner\\:\\:getInstance\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Security/XmlScanner.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Security\\\\XmlScanner\\:\\:threadSafeLibxmlDisableEntityLoaderAvailability\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Security/XmlScanner.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Security\\\\XmlScanner\\:\\:toUtf8\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Security/XmlScanner.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Security\\\\XmlScanner\\:\\:toUtf8\\(\\) has parameter \\$xml with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Security/XmlScanner.php

		-
			message: "#^Parameter \\#2 \\$subject of function preg_match expects string, array\\<int, string\\>\\|string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Security/XmlScanner.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Security\\\\XmlScanner\\:\\:\\$callback has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Security/XmlScanner.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Security\\\\XmlScanner\\:\\:\\$libxmlDisableEntityLoaderValue has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Security/XmlScanner.php

		-
			message: "#^Call to an undefined method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\\\SpgrContainer\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\\\SpgrContainer\\\\SpContainer\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DggContainer\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DggContainer\\\\BstoreContainer\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DggContainer\\\\BstoreContainer\\\\BSE\\:\\:getDgContainer\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Call to an undefined method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\\\SpgrContainer\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\\\SpgrContainer\\\\SpContainer\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DggContainer\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DggContainer\\\\BstoreContainer\\|PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DggContainer\\\\BstoreContainer\\\\BSE\\:\\:getDggContainer\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Call to an undefined method object\\:\\:getEndCoordinates\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Call to an undefined method object\\:\\:getEndOffsetX\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Call to an undefined method object\\:\\:getEndOffsetY\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Call to an undefined method object\\:\\:getNestingLevel\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Call to an undefined method object\\:\\:getOPT\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Call to an undefined method object\\:\\:getStartCoordinates\\(\\)\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Call to an undefined method object\\:\\:getStartOffsetX\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Call to an undefined method object\\:\\:getStartOffsetY\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Cannot access offset 1 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^If condition is always true\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\:\\:includeCellRangeFiltered\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\:\\:includeCellRangeFiltered\\(\\) has parameter \\$cellRangeAddress with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\:\\:parseRichText\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\:\\:parseRichText\\(\\) has parameter \\$is with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Negated boolean expression is always false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Parameter \\#1 \\$block of method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\:\\:makeKey\\(\\) expects int, float given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Parameter \\#1 \\$showSummaryBelow of method PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\:\\:setShowSummaryBelow\\(\\) expects bool, int given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Parameter \\#1 \\$showSummaryRight of method PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\:\\:setShowSummaryRight\\(\\) expects bool, int given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Parameter \\#2 \\$row of method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\IReadFilter\\:\\:readCell\\(\\) expects int, string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Parameter \\#2 \\$row of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Xls\\:\\:sizeRow\\(\\) expects int, string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Parameter \\#2 \\$startRow of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Xls\\:\\:getDistanceY\\(\\) expects int, string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Parameter \\#3 \\$subject of function str_replace expects array\\|string, int\\|string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Parameter \\#4 \\$endRow of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Xls\\:\\:getDistanceY\\(\\) expects int, string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\:\\:\\$data \\(string\\) does not accept string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\:\\:\\$documentSummaryInformation \\(string\\) does not accept string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\:\\:\\$documentSummaryInformation \\(string\\) in isset\\(\\) is not nullable\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\:\\:\\$md5Ctxt is never written, only read\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\:\\:\\$summaryInformation \\(string\\) does not accept string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\:\\:\\$summaryInformation \\(string\\) in isset\\(\\) is not nullable\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 8
			path: src/PhpSpreadsheet/Reader/Xls.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\\\Color\\\\BIFF5\\:\\:\\$map has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls/Color/BIFF5.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\\\Color\\\\BIFF8\\:\\:\\$map has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls/Color/BIFF8.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\\\Color\\\\BuiltIn\\:\\:\\$map has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls/Color/BuiltIn.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\\\ErrorCode\\:\\:\\$map has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls/ErrorCode.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\\\RC4\\:\\:\\$i has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls/RC4.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\\\RC4\\:\\:\\$j has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls/RC4.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xls\\\\RC4\\:\\:\\$s has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xls/RC4.php

		-
			message: "#^Argument of an invalid type array\\<int, string\\>\\|false supplied for foreach, only iterables are supported\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot access offset 0 on array\\<int, string\\>\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot access property \\$r on SimpleXMLElement\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot call method addChart\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot call method setBold\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot call method setColor\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot call method setItalic\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot call method setName\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot call method setSize\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot call method setStrikethrough\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot call method setSubscript\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot call method setSuperscript\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Cannot call method setUnderline\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Comparison operation \"\\>\" between SimpleXMLElement\\|null and 0 results in an error\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:boolean\\(\\) has parameter \\$value with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToBoolean\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToBoolean\\(\\) has parameter \\$c with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToError\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToError\\(\\) has parameter \\$c with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToFormula\\(\\) has parameter \\$c with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToFormula\\(\\) has parameter \\$calculatedValue with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToFormula\\(\\) has parameter \\$castBaseType with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToFormula\\(\\) has parameter \\$cellDataType with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToFormula\\(\\) has parameter \\$r with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToFormula\\(\\) has parameter \\$sharedFormulas with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToFormula\\(\\) has parameter \\$value with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToString\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:castToString\\(\\) has parameter \\$c with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:dirAdd\\(\\) has parameter \\$add with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:dirAdd\\(\\) has parameter \\$base with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:getArrayItem\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:getArrayItem\\(\\) has parameter \\$array with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:getArrayItem\\(\\) has parameter \\$key with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:getFromZipArchive\\(\\) should return string but returns string\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:readFormControlProperties\\(\\) has parameter \\$dir with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:readFormControlProperties\\(\\) has parameter \\$docSheet with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:readFormControlProperties\\(\\) has parameter \\$fileWorksheet with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:readPrinterSettings\\(\\) has parameter \\$dir with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:readPrinterSettings\\(\\) has parameter \\$docSheet with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:readPrinterSettings\\(\\) has parameter \\$fileWorksheet with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:stripWhiteSpaceFromStyleString\\(\\) has parameter \\$string with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\:\\:toCSSArray\\(\\) has parameter \\$style with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Negated boolean expression is always true\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Parameter \\#1 \\$fontSizeInPoints of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Font\\:\\:fontSizeToPixels\\(\\) expects int, string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Parameter \\#1 \\$haystack of function strpos expects string, int\\|string given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Parameter \\#1 \\$sizeInCm of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Font\\:\\:centimeterSizeToPixels\\(\\) expects int, string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Parameter \\#1 \\$sizeInInch of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Font\\:\\:inchSizeToPixels\\(\\) expects int, string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Parameter \\#1 \\$worksheetName of method PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\:\\:getSheetByName\\(\\) expects string, array\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Parameter \\#3 \\$subject of function str_replace expects array\\|string, int\\|string given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Reader/Xlsx.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\AutoFilter\\:\\:readAutoFilter\\(\\) has parameter \\$autoFilterRange with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/AutoFilter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\AutoFilter\\:\\:readAutoFilter\\(\\) has parameter \\$xmlSheet with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/AutoFilter.php

		-
			message: "#^Parameter \\#1 \\$operator of method PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\AutoFilter\\\\Column\\\\Rule\\:\\:setRule\\(\\) expects string, null given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Reader/Xlsx/AutoFilter.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\AutoFilter\\:\\:\\$worksheet has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/AutoFilter.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\AutoFilter\\:\\:\\$worksheetXml has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/AutoFilter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\BaseParserClass\\:\\:boolean\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/BaseParserClass.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\BaseParserClass\\:\\:boolean\\(\\) has parameter \\$value with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/BaseParserClass.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:isFilteredColumn\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:isFilteredColumn\\(\\) has parameter \\$columnCoordinate with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:isFilteredRow\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:isFilteredRow\\(\\) has parameter \\$rowCoordinate with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:readColumnAttributes\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:readColumnAttributes\\(\\) has parameter \\$readDataOnly with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:readColumnRangeAttributes\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:readColumnRangeAttributes\\(\\) has parameter \\$readDataOnly with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:readRowAttributes\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:readRowAttributes\\(\\) has parameter \\$readDataOnly with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:\\$worksheet has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ColumnAndRowAttributes\\:\\:\\$worksheetXml has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:readConditionalStyles\\(\\) has parameter \\$xmlSheet with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:readDataBarExtLstOfConditionalRule\\(\\) has parameter \\$cfRule with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:readDataBarExtLstOfConditionalRule\\(\\) has parameter \\$conditionalFormattingRuleExtensions with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:readDataBarOfConditionalRule\\(\\) has parameter \\$cfRule with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:readDataBarOfConditionalRule\\(\\) has parameter \\$conditionalFormattingRuleExtensions with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:readStyleRules\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:readStyleRules\\(\\) has parameter \\$cfRules with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:readStyleRules\\(\\) has parameter \\$extLst with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:setConditionalStyles\\(\\) has parameter \\$xmlExtLst with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:\\$dxfs has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:\\$worksheet has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\ConditionalStyles\\:\\:\\$worksheetXml has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\DataValidations\\:\\:\\$worksheet has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/DataValidations.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\DataValidations\\:\\:\\$worksheetXml has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/DataValidations.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Hyperlinks\\:\\:\\$hyperlinks has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Hyperlinks.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\Hyperlinks\\:\\:\\$worksheet has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/Hyperlinks.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\PageSetup\\:\\:load\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/PageSetup.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\PageSetup\\:\\:pageSetup\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/PageSetup.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\PageSetup\\:\\:\\$worksheet has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/PageSetup.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\PageSetup\\:\\:\\$worksheetXml has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/PageSetup.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\SheetViewOptions\\:\\:\\$worksheet has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/SheetViewOptions.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xlsx\\\\SheetViewOptions\\:\\:\\$worksheetXml has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xlsx/SheetViewOptions.php

		-
			message: "#^Parameter \\#1 \\$haystack of function strpos expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xml.php

		-
			message: "#^Parameter \\#1 \\$textValue of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\StringHelper\\:\\:convertEncoding\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xml.php

		-
			message: "#^Parameter \\#2 \\$subject of function preg_match expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xml.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Reader\\\\Xml\\:\\:\\$fileContents has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xml.php

		-
			message: "#^Argument of an invalid type SimpleXMLElement\\|null supplied for foreach, only iterables are supported\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xml/Properties.php

		-
			message: "#^Argument of an invalid type SimpleXMLElement\\|null supplied for foreach, only iterables are supported\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Reader/Xml/Style.php

		-
			message: "#^Elseif condition is always true\\.$#"
			count: 1
			path: src/PhpSpreadsheet/ReferenceHelper.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: src/PhpSpreadsheet/ReferenceHelper.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\RichText\\\\Run\\:\\:\\$font \\(PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/RichText/Run.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Settings\\:\\:getHttpClient\\(\\) should return Psr\\\\Http\\\\Client\\\\ClientInterface but returns Psr\\\\Http\\\\Client\\\\ClientInterface\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Settings.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Settings\\:\\:getRequestFactory\\(\\) should return Psr\\\\Http\\\\Message\\\\RequestFactoryInterface but returns Psr\\\\Http\\\\Message\\\\RequestFactoryInterface\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Settings.php

		-
			message: "#^Negated boolean expression is always false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Settings.php

		-
			message: "#^Result of && is always false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Settings.php

		-
			message: "#^Strict comparison using \\=\\=\\= between int and null will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Settings.php

		-
			message: "#^Parameter \\#1 \\$excelFormatCode of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Date\\:\\:isDateTimeFormatCode\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Date.php

		-
			message: "#^Parameter \\#1 \\$string of function substr expects string, int given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Shared/Date.php

		-
			message: "#^Parameter \\#1 \\$unixTimestamp of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Date\\:\\:timestampToExcel\\(\\) expects int, float\\|int\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Date.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Date\\:\\:\\$possibleDateFormatCharacters has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Date.php

		-
			message: "#^Cannot access offset 1 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Drawing.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\:\\:getDgId\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Escher/DgContainer.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\:\\:getLastSpId\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Escher/DgContainer.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\:\\:getSpgrContainer\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Escher/DgContainer.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\:\\:setDgId\\(\\) has parameter \\$value with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Escher/DgContainer.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\:\\:setLastSpId\\(\\) has parameter \\$value with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Escher/DgContainer.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\:\\:setSpgrContainer\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Escher/DgContainer.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\:\\:setSpgrContainer\\(\\) has parameter \\$spgrContainer with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Escher/DgContainer.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\:\\:\\$spgrContainer has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Escher/DgContainer.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DgContainer\\\\SpgrContainer\\:\\:getChildren\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Escher/DgContainer/SpgrContainer.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DggContainer\\\\BstoreContainer\\\\BSE\\:\\:\\$parent is never read, only written\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Escher/DggContainer/BstoreContainer/BSE.php

		-
			message: "#^Cannot access offset 0 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Font.php

		-
			message: "#^Cannot access offset 2 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Font.php

		-
			message: "#^Cannot access offset 4 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Font.php

		-
			message: "#^Cannot access offset 6 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Font.php

		-
			message: "#^Parameter \\#1 \\$size of function imagettfbbox expects float, float\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Font.php

		-
			message: "#^Parameter \\#2 \\$defaultFont of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Drawing\\:\\:pixelsToCellDimension\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font, PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Font.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Font\\:\\:\\$autoSizeMethods has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Font.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Font.php

		-
			message: "#^Variable \\$cellText on left side of \\?\\? always exists and is not nullable\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Font.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\EigenvalueDecomposition\\:\\:\\$cdivi has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/EigenvalueDecomposition.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\EigenvalueDecomposition\\:\\:\\$e has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/EigenvalueDecomposition.php

		-
			message: "#^Else branch is unreachable because previous condition is always true\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/LUDecomposition.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\LUDecomposition\\:\\:getDoublePivot\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/LUDecomposition.php

		-
			message: "#^Call to function is_string\\(\\) with float\\|int will always evaluate to false\\.$#"
			count: 5
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:__construct\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:arrayLeftDivide\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:arrayLeftDivideEquals\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:arrayRightDivide\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:arrayRightDivideEquals\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:arrayTimes\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:arrayTimesEquals\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:concat\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:getMatrix\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:minus\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:minusEquals\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:plus\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:plusEquals\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:power\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:times\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Parameter \\#3 \\$c of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\JAMA\\\\Matrix\\:\\:set\\(\\) expects float\\|int\\|null, string given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Result of && is always false\\.$#"
			count: 10
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 19
			path: src/PhpSpreadsheet/Shared/JAMA/Matrix.php

		-
			message: "#^If condition is always true\\.$#"
			count: 7
			path: src/PhpSpreadsheet/Shared/JAMA/SingularValueDecomposition.php

		-
			message: "#^Left side of && is always true\\.$#"
			count: 4
			path: src/PhpSpreadsheet/Shared/JAMA/SingularValueDecomposition.php

		-
			message: "#^Cannot access offset 1 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Cannot access offset 2 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Cannot access offset 3 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Cannot access offset 4 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\:\\:getData\\(\\) should return string but returns string\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\:\\:getStream\\(\\) should return resource but returns resource\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Parameter \\#1 \\$No of class PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS constructor expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Parameter \\#1 \\$oleTimestamp of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\:\\:OLE2LocalDate\\(\\) expects string, string\\|false given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Parameter \\#1 \\$string of function substr expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Parameter \\#2 \\$name of class PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS constructor expects string, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Parameter \\#3 \\$type of class PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS constructor expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Parameter \\#4 \\$prev of class PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS constructor expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Parameter \\#5 \\$next of class PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS constructor expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Parameter \\#6 \\$dir of class PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS constructor expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Parameter \\#9 \\$data of class PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS constructor expects string, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\:\\:\\$root \\(PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\\\Root\\) in isset\\(\\) is not nullable\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE.php

		-
			message: "#^Parameter \\#2 \\$offset of function array_slice expects int, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS.php

		-
			message: "#^Parameter \\#3 \\$length of function array_slice expects int\\|null, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\:\\:\\$_data \\(string\\) in isset\\(\\) is not nullable\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\:\\:\\$startBlock \\(int\\) on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS.php

		-
			message: "#^Parameter \\#1 \\$No of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\:\\:__construct\\(\\) expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/File.php

		-
			message: "#^Parameter \\#4 \\$prev of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\:\\:__construct\\(\\) expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/File.php

		-
			message: "#^Parameter \\#5 \\$next of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\:\\:__construct\\(\\) expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/File.php

		-
			message: "#^Parameter \\#6 \\$dir of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\:\\:__construct\\(\\) expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/File.php

		-
			message: "#^Parameter \\#1 \\$No of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\:\\:__construct\\(\\) expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Parameter \\#1 \\$iSBDcnt of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\\\Root\\:\\:saveHeader\\(\\) expects int, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Parameter \\#1 \\$iSbdSize of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\\\Root\\:\\:saveBbd\\(\\) expects int, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Parameter \\#1 \\$iStBlk of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\\\Root\\:\\:saveBigData\\(\\) expects int, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Parameter \\#2 \\$iBBcnt of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\\\Root\\:\\:saveHeader\\(\\) expects int, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Parameter \\#2 \\$iBsize of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\\\Root\\:\\:saveBbd\\(\\) expects int, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Parameter \\#3 \\$iPPScnt of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\\\Root\\:\\:saveHeader\\(\\) expects int, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Parameter \\#3 \\$iPpsCnt of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\\\Root\\:\\:saveBbd\\(\\) expects int, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Parameter \\#4 \\$prev of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\:\\:__construct\\(\\) expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Parameter \\#5 \\$next of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\:\\:__construct\\(\\) expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Parameter \\#6 \\$dir of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\:\\:__construct\\(\\) expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Parameter \\#9 \\$data of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\:\\:__construct\\(\\) expects string, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\\\Root\\:\\:\\$bigBlockSize \\(int\\) in isset\\(\\) is not nullable\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLE\\\\PPS\\\\Root\\:\\:\\$smallBlockSize \\(int\\) in isset\\(\\) is not nullable\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLE/PPS/Root.php

		-
			message: "#^Parameter \\#1 \\$data of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLERead\\:\\:getInt4d\\(\\) expects string, string\\|false given\\.$#"
			count: 8
			path: src/PhpSpreadsheet/Shared/OLERead.php

		-
			message: "#^Parameter \\#1 \\$string of function substr expects string, string\\|false given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Shared/OLERead.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLERead\\:\\:\\$data has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLERead.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLERead\\:\\:\\$documentSummaryInformation has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLERead.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLERead\\:\\:\\$summaryInformation has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLERead.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\OLERead\\:\\:\\$wrkbook has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLERead.php

		-
			message: "#^Strict comparison using \\=\\=\\= between int and null will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/OLERead.php

		-
			message: "#^Static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\TimeZone\\:\\:validateTimeZone\\(\\) is unused\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/TimeZone.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:calculateGoodnessOfFit\\(\\) has parameter \\$const with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:calculateGoodnessOfFit\\(\\) has parameter \\$meanX with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:calculateGoodnessOfFit\\(\\) has parameter \\$meanY with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:calculateGoodnessOfFit\\(\\) has parameter \\$sumX with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:calculateGoodnessOfFit\\(\\) has parameter \\$sumX2 with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:calculateGoodnessOfFit\\(\\) has parameter \\$sumXY with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:calculateGoodnessOfFit\\(\\) has parameter \\$sumY with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:calculateGoodnessOfFit\\(\\) has parameter \\$sumY2 with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:getBestFitType\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:getError\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:sumSquares\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$DFResiduals has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$SSRegression has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$SSResiduals has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$correlation has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$covariance has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$f has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$goodnessOfFit has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$intersect has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$intersectSE has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$slope has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$slopeSE has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$stdevOfResiduals has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$xOffset has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\BestFit\\:\\:\\$yOffset has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/BestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\PolynomialBestFit\\:\\:getCoefficients\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/PolynomialBestFit.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\PolynomialBestFit\\:\\:getCoefficients\\(\\) has parameter \\$dp with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/PolynomialBestFit.php

		-
			message: "#^Call to an undefined method object\\:\\:getGoodnessOfFit\\(\\)\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/Trend.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\Trend\\:\\:calculate\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/Trend.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\Trend\\:\\:calculate\\(\\) has parameter \\$const with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/Trend.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\Trend\\:\\:calculate\\(\\) has parameter \\$trendType with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/Trend.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\Trend\\:\\:calculate\\(\\) has parameter \\$xValues with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/Trend.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Trend\\\\Trend\\:\\:calculate\\(\\) has parameter \\$yValues with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/Trend/Trend.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\XMLWriter\\:\\:getData\\(\\) should return string but returns string\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/XMLWriter.php

		-
			message: "#^Parameter \\#1 \\$uri of method XMLWriter\\:\\:openUri\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/XMLWriter.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\XMLWriter\\:\\:\\$debugEnabled has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/XMLWriter.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\XMLWriter\\:\\:\\$tempFileName \\(string\\) does not accept string\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Shared/XMLWriter.php

		-
			message: "#^Call to function is_array\\(\\) with string will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Spreadsheet.php

		-
			message: "#^Comparison operation \"\\<\\=\" between int\\<min, \\-1\\> and 1000 is always true\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Spreadsheet.php

		-
			message: "#^Parameter \\#1 \\$worksheet of method PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\:\\:getIndex\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet, PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Spreadsheet.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\:\\:\\$workbookViewVisibilityValues has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Spreadsheet.php

		-
			message: "#^Result of \\|\\| is always true\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Spreadsheet.php

		-
			message: "#^Strict comparison using \\=\\=\\= between PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet and null will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Spreadsheet.php

		-
			message: "#^Strict comparison using \\=\\=\\= between string and null will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Spreadsheet.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Spreadsheet.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalDataBar\\:\\:setConditionalFormattingRuleExt\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalDataBar.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalDataBar\\:\\:setMaximumConditionalFormatValueObject\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalDataBar.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalDataBar\\:\\:setMinimumConditionalFormatValueObject\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalDataBar.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalDataBar\\:\\:setShowValue\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalDataBar.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalDataBarExtension\\:\\:getXmlAttributes\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalDataBarExtension.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalDataBarExtension\\:\\:getXmlElements\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalDataBarExtension.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalDataBarExtension\\:\\:setMaximumConditionalFormatValueObject\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalDataBarExtension.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalDataBarExtension\\:\\:setMinimumConditionalFormatValueObject\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalDataBarExtension.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormatValueObject\\:\\:__construct\\(\\) has parameter \\$type with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormatValueObject.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormatValueObject\\:\\:__construct\\(\\) has parameter \\$value with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormatValueObject.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormatValueObject\\:\\:setCellFormula\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormatValueObject.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormatValueObject\\:\\:setType\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormatValueObject.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormatValueObject\\:\\:setValue\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormatValueObject.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormatValueObject\\:\\:\\$cellFormula has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormatValueObject.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormatValueObject\\:\\:\\$type has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormatValueObject.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormatValueObject\\:\\:\\$value has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormatValueObject.php

		-
			message: "#^Cannot access property \\$axisPosition on SimpleXMLElement\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Cannot access property \\$border on SimpleXMLElement\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Cannot access property \\$direction on SimpleXMLElement\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Cannot access property \\$gradient on SimpleXMLElement\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Cannot access property \\$maxLength on SimpleXMLElement\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Cannot access property \\$minLength on SimpleXMLElement\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Cannot access property \\$negativeBarBorderColorSameAsPositive on SimpleXMLElement\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormattingRuleExtension\\:\\:__construct\\(\\) has parameter \\$id with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormattingRuleExtension\\:\\:generateUuid\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormattingRuleExtension\\:\\:parseExtDataBarElementChildrenFromXml\\(\\) has parameter \\$ns with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormattingRuleExtension\\:\\:parseExtLstXml\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormattingRuleExtension\\:\\:parseExtLstXml\\(\\) has parameter \\$extLstXml with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Offset 'rgb' does not exist on SimpleXMLElement\\|null\\.$#"
			count: 4
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Offset 'theme' does not exist on SimpleXMLElement\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Offset 'tint' does not exist on SimpleXMLElement\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\ConditionalFormatting\\\\ConditionalFormattingRuleExtension\\:\\:\\$id has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\Formatter\\:\\:splitFormat\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/Formatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\Formatter\\:\\:splitFormat\\(\\) has parameter \\$sections with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/Formatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\Formatter\\:\\:splitFormat\\(\\) has parameter \\$value with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/Formatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\Formatter\\:\\:splitFormatCompare\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/Formatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\Formatter\\:\\:splitFormatCompare\\(\\) has parameter \\$cond with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/Formatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\Formatter\\:\\:splitFormatCompare\\(\\) has parameter \\$dfcond with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/Formatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\Formatter\\:\\:splitFormatCompare\\(\\) has parameter \\$dfval with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/Formatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\Formatter\\:\\:splitFormatCompare\\(\\) has parameter \\$val with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/Formatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\Formatter\\:\\:splitFormatCompare\\(\\) has parameter \\$value with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/Formatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\Formatter\\:\\:toFormattedString\\(\\) should return string but returns float\\|int\\|string\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/Formatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\\\PercentageFormatter\\:\\:format\\(\\) has parameter \\$value with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Style/NumberFormat/PercentageFormatter.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\CellIterator\\:\\:adjustForExistingOnlyRange\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/CellIterator.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Drawing\\\\Shadow\\:\\:\\$color \\(PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Color\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Color\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Drawing/Shadow.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\PageSetup\\:\\:getPrintArea\\(\\) should return string but returns string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/PageSetup.php

		-
			message: "#^Parameter \\#1 \\$value of method PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\PageSetup\\:\\:setFirstPageNumber\\(\\) expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/PageSetup.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\PageSetup\\:\\:\\$pageOrder has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/PageSetup.php

		-
			message: "#^Strict comparison using \\=\\=\\= between int\\<min, \\-1\\> and null will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/PageSetup.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\SheetView\\:\\:\\$sheetViewTypes has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/SheetView.php

		-
			message: "#^Strict comparison using \\=\\=\\= between int\\<min, 0\\> and null will always evaluate to false\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Worksheet/SheetView.php

		-
			message: "#^Strict comparison using \\=\\=\\= between string and null will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/SheetView.php

		-
			message: "#^Cannot call method getCalculatedValue\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Cell\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Cannot call method getHashCode\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Cannot call method getValue\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Cell\\|null\\.$#"
			count: 4
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Cannot call method getWorksheet\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\DefinedName\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Cannot call method getXfIndex\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Cell\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Cannot call method rangeToArray\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^If condition is always true\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Left side of && is always true\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\:\\:getChartByName\\(\\) should return PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\|false but returns PhpOffice\\\\PhpSpreadsheet\\\\Chart\\\\Chart\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Parameter \\#1 \\$index of class PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\ColumnDimension constructor expects string, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Parameter \\#1 \\$index of class PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\RowDimension constructor expects int, null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Parameter \\#1 \\$range of static method PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Coordinate\\:\\:rangeDimension\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Parameter \\#2 \\$format of static method PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\:\\:toFormattedString\\(\\) expects string, string\\|null given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Parameter \\#3 \\$rotation of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Font\\:\\:calculateColumnWidth\\(\\) expects int, int\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Worksheet\\\\Worksheet\\:\\:\\$parent \\(PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\) does not accept PhpOffice\\\\PhpSpreadsheet\\\\Spreadsheet\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Right side of && is always true\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Strict comparison using \\=\\=\\= between string and null will always evaluate to false\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Worksheet/Worksheet.php

		-
			message: "#^Call to function array_key_exists\\(\\) with int and array\\{none\\: 'none', dashDot\\: '1px dashed', dashDotDot\\: '1px dotted', dashed\\: '1px dashed', dotted\\: '1px dotted', double\\: '3px double', hair\\: '1px solid', medium\\: '2px solid', \\.\\.\\.\\} will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Cannot access offset 'mime' on array\\|false\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Cannot access offset 0 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Cannot access offset 1 on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Cannot call method getSubscript\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Cannot call method getSuperscript\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:calculateSpansOmitRows\\(\\) has parameter \\$candidateSpannedRow with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:calculateSpansOmitRows\\(\\) has parameter \\$sheet with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:calculateSpansOmitRows\\(\\) has parameter \\$sheetIndex with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateHTMLFooter\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateMeta\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateMeta\\(\\) has parameter \\$desc with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateMeta\\(\\) has parameter \\$val with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellCss\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellCss\\(\\) has parameter \\$cellAddress with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellCss\\(\\) has parameter \\$columnNumber with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellCss\\(\\) has parameter \\$row with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellData\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellData\\(\\) has parameter \\$cell with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellData\\(\\) has parameter \\$cellType with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellData\\(\\) has parameter \\$cssClass with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellDataValue\\(\\) has parameter \\$cell with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellDataValue\\(\\) has parameter \\$cellData with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellDataValueRich\\(\\) has parameter \\$cell with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowCellDataValueRich\\(\\) has parameter \\$cellData with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowIncludeCharts\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowIncludeCharts\\(\\) has parameter \\$coordinate with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowSpans\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowSpans\\(\\) has parameter \\$colSpan with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowSpans\\(\\) has parameter \\$html with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowSpans\\(\\) has parameter \\$rowSpan with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowWriteCell\\(\\) has parameter \\$cellData with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowWriteCell\\(\\) has parameter \\$cellType with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowWriteCell\\(\\) has parameter \\$colNum with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowWriteCell\\(\\) has parameter \\$colSpan with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowWriteCell\\(\\) has parameter \\$coordinate with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowWriteCell\\(\\) has parameter \\$cssClass with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowWriteCell\\(\\) has parameter \\$html with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowWriteCell\\(\\) has parameter \\$row with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowWriteCell\\(\\) has parameter \\$rowSpan with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateRowWriteCell\\(\\) has parameter \\$sheetIndex with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateSheetPrep\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateSheetStarts\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateSheetStarts\\(\\) has parameter \\$rowMin with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateSheetStarts\\(\\) has parameter \\$sheet with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateSheetTags\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateSheetTags\\(\\) has parameter \\$row with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateSheetTags\\(\\) has parameter \\$tbodyStart with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateSheetTags\\(\\) has parameter \\$theadEnd with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateSheetTags\\(\\) has parameter \\$theadStart with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateTableFooter\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateTableTag\\(\\) has parameter \\$html with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateTableTag\\(\\) has parameter \\$id with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateTableTag\\(\\) has parameter \\$sheetIndex with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateTableTagInline\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:generateTableTagInline\\(\\) has parameter \\$id with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Parameter \\#1 \\$borderStyle of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:mapBorderStyle\\(\\) expects int, string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Parameter \\#1 \\$font of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:createCSSStyleFont\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font, PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Parameter \\#1 \\$hAlign of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:mapHAlign\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Parameter \\#1 \\$vAlign of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Html\\:\\:mapVAlign\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Parameter \\#2 \\$length of function fread expects int\\<0, max\\>, int\\<0, max\\>\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Parameter \\#3 \\$use_include_path of function fopen expects bool, int given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Html.php

		-
			message: "#^Negated boolean expression is always false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Ods.php

		-
			message: "#^If condition is always true\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Ods/Cell/Style.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Ods\\\\Cell\\\\Style\\:\\:\\$writer has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Ods/Cell/Style.php

		-
			message: "#^Parameter \\#1 \\$range of static method PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Coordinate\\:\\:splitRange\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Ods/Content.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int\\<2, max\\> given\\.$#"
			count: 3
			path: src/PhpSpreadsheet/Writer/Ods/Content.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Ods\\\\Content\\:\\:\\$formulaConvertor has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Ods/Content.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Ods\\\\Formula\\:\\:\\$definedNames has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Ods/Formula.php

		-
			message: "#^Cannot call method getHashCode\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Cannot use array destructuring on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Offset 'endCoordinates' does not exist on array\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Offset 'endOffsetX' does not exist on array\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Offset 'endOffsetY' does not exist on array\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Offset 'startCoordinates' does not exist on array\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Offset 'startOffsetX' does not exist on array\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Offset 'startOffsetY' does not exist on array\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Parameter \\#1 \\$blipType of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DggContainer\\\\BstoreContainer\\\\BSE\\:\\:setBlipType\\(\\) expects int, int\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Parameter \\#1 \\$data of method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Escher\\\\DggContainer\\\\BstoreContainer\\\\BSE\\\\Blip\\:\\:setData\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Parameter \\#1 \\$font of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Workbook\\:\\:addFont\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font, PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\:\\:\\$documentSummaryInformation \\(string\\) in isset\\(\\) is not nullable\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\:\\:\\$summaryInformation \\(string\\) in isset\\(\\) is not nullable\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\BIFFwriter\\:\\:writeEof\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/BIFFwriter.php

		-
			message: "#^Static property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\BIFFwriter\\:\\:\\$byteOrder \\(int\\) in isset\\(\\) is not nullable\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/BIFFwriter.php

		-
			message: "#^Elseif condition is always true\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Escher.php

		-
			message: "#^If condition is always true\\.$#"
			count: 3
			path: src/PhpSpreadsheet/Writer/Xls/Escher.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Escher\\:\\:\\$data has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Escher.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Escher\\:\\:\\$object has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Escher.php

		-
			message: "#^If condition is always false\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xls/Font.php

		-
			message: "#^Parameter \\#1 \\$bold of static method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Font\\:\\:mapBold\\(\\) expects bool, bool\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Font.php

		-
			message: "#^Parameter \\#1 \\$fontName of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\Font\\:\\:getCharsetFromFontName\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Font.php

		-
			message: "#^Parameter \\#1 \\$textValue of static method PhpOffice\\\\PhpSpreadsheet\\\\Shared\\\\StringHelper\\:\\:UTF8toBIFF8UnicodeShort\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Font.php

		-
			message: "#^Parameter \\#1 \\$underline of static method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Font\\:\\:mapUnderline\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Font.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Parser\\:\\:advance\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Parser.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Parser\\:\\:\\$spreadsheet has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Parser.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 5
			path: src/PhpSpreadsheet/Writer/Xls/Parser.php

		-
			message: "#^Cannot access offset 'encoding' on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Workbook.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Workbook\\:\\:writeAllDefinedNamesBiff8\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Workbook.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Workbook\\:\\:writeExternalsheetBiff8\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Workbook.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Workbook\\:\\:writeMsoDrawingGroup\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Workbook.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Workbook\\:\\:writeSupbookInternal\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Workbook.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Workbook\\:\\:\\$biffSize is never read, only written\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Workbook.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Workbook\\:\\:\\$colors has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Workbook.php

		-
			message: "#^Cannot access offset 'comp' on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Cannot access offset 'ident' on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Cannot access offset 'sa' on array\\|false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Cannot access offset 1 on array\\|false\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Cannot access offset 2 on array\\|false\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Cannot call method getHashCode\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#1 \\$coordinates of static method PhpOffice\\\\PhpSpreadsheet\\\\Cell\\\\Coordinate\\:\\:indexesFromString\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#1 \\$string of function strlen expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#1 \\$string of function substr expects string, string\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#2 \\$length of function fread expects int\\<0, max\\>, int\\<0, max\\>\\|false given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#4 \\$isError of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Worksheet\\:\\:writeBoolErr\\(\\) expects bool, int given\\.$#"
			count: 3
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#5 \\$width of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Worksheet\\:\\:positionImage\\(\\) expects int, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#6 \\$height of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Worksheet\\:\\:positionImage\\(\\) expects int, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Worksheet\\:\\:\\$activePane \\(int\\) does not accept int\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Worksheet\\:\\:\\$colors has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Worksheet\\:\\:\\$countCellStyleXfs is never read, only written\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Worksheet\\:\\:\\$outlineBelow is never read, only written\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Worksheet\\:\\:\\$outlineRight is never read, only written\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Worksheet\\:\\:\\$selection is never read, only written\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Worksheet\\:\\:\\$xlsStringMaxLength is never read, only written\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Worksheet.php

		-
			message: "#^Parameter \\#1 \\$textRotation of static method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Xf\\:\\:mapTextRotation\\(\\) expects int, int\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Xf.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xls\\\\Xf\\:\\:\\$diag is never read, only written\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xls/Xf.php

		-
			message: "#^Argument of an invalid type array\\|null supplied for foreach, only iterables are supported\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx.php

		-
			message: "#^Parameter \\#1 \\$path of function basename expects string, array\\|string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx.php

		-
			message: "#^Parameter \\#1 \\$path of function dirname expects string, array\\|string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx.php

		-
			message: "#^Possibly invalid array key type array\\|string\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx.php

		-
			message: "#^Property PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\:\\:\\$pathNames has no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx.php

		-
			message: "#^Parameter \\#1 \\$string of function substr expects string, int given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Comments.php

		-
			message: "#^Parameter \\#2 \\$content of method XMLWriter\\:\\:writeElement\\(\\) expects string\\|null, int given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/Comments.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/DefinedNames.php

		-
			message: "#^Parameter \\#2 \\$content of method XMLWriter\\:\\:writeElement\\(\\) expects string\\|null, int given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/DocProps.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/DocProps.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Rels\\:\\:writeUnparsedRelationship\\(\\) has parameter \\$relationship with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Rels.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Rels\\:\\:writeUnparsedRelationship\\(\\) has parameter \\$type with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Rels.php

		-
			message: "#^Parameter \\#2 \\$id of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Rels\\:\\:writeRelationship\\(\\) expects int, string given\\.$#"
			count: 5
			path: src/PhpSpreadsheet/Writer/Xlsx/Rels.php

		-
			message: "#^Parameter \\#4 \\$target of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Rels\\:\\:writeRelationship\\(\\) expects string, array\\|string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Rels.php

		-
			message: "#^Cannot call method getBold\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Cannot call method getColor\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Cannot call method getItalic\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Cannot call method getName\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Cannot call method getSize\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Cannot call method getStrikethrough\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Cannot call method getSubscript\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Cannot call method getSuperscript\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Cannot call method getUnderline\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Instanceof between \\*NEVER\\* and PhpOffice\\\\PhpSpreadsheet\\\\RichText\\\\RichText will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Instanceof between string and PhpOffice\\\\PhpSpreadsheet\\\\RichText\\\\RichText will always evaluate to false\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Parameter \\#1 \\$text of method PhpOffice\\\\PhpSpreadsheet\\\\RichText\\\\RichText\\:\\:createTextRun\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, float\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int\\<0, max\\> given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, string\\|null given\\.$#"
			count: 4
			path: src/PhpSpreadsheet/Writer/Xlsx/StringTable.php

		-
			message: "#^Cannot call method getStyle\\(\\) on PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Conditional\\|null\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Comparison operation \"\\<\" between int\\<min, \\-1\\> and 0 is always true\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Parameter \\#2 \\$borders of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Style\\:\\:writeBorder\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Borders, PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Borders\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Parameter \\#2 \\$fill of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Style\\:\\:writeFill\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Fill, PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Fill\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Parameter \\#2 \\$font of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Style\\:\\:writeFont\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font, PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\Font\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Parameter \\#2 \\$numberFormat of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Style\\:\\:writeNumFmt\\(\\) expects PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat, PhpOffice\\\\PhpSpreadsheet\\\\Style\\\\NumberFormat\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, float given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int given\\.$#"
			count: 22
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int\\<0, max\\> given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int\\<1, max\\> given\\.$#"
			count: 2
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, string\\|null given\\.$#"
			count: 7
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Result of \\|\\| is always true\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Style.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int given\\.$#"
			count: 7
			path: src/PhpSpreadsheet/Writer/Xlsx/Workbook.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^If condition is always true\\.$#"
			count: 6
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Worksheet\\:\\:writeAttributeIf\\(\\) has parameter \\$condition with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Worksheet\\:\\:writeDataBarElements\\(\\) has parameter \\$dataBar with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^Method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Worksheet\\:\\:writeElementIf\\(\\) has parameter \\$condition with no type specified\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^Parameter \\#2 \\$content of method XMLWriter\\:\\:writeElement\\(\\) expects string\\|null, int\\|string given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int given\\.$#"
			count: 15
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int\\<0, max\\> given\\.$#"
			count: 3
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int\\<1, max\\> given\\.$#"
			count: 9
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, int\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^Parameter \\#2 \\$value of method XMLWriter\\:\\:writeAttribute\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^Parameter \\#3 \\$stringTable of method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Worksheet\\:\\:writeSheetData\\(\\) expects array\\<string\\>, array\\<string\\>\\|null given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

		-
			message: "#^Parameter \\#4 \\$val of static method PhpOffice\\\\PhpSpreadsheet\\\\Writer\\\\Xlsx\\\\Worksheet\\:\\:writeAttributeIf\\(\\) expects string, int given\\.$#"
			count: 1
			path: src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php

