<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="PHP_CodeSniffer"
         xsi:noNamespaceSchemaLocation="vendor/squizlabs/php_codesniffer/phpcs.xsd">

    <file>samples</file>
    <file>src</file>
    <file>tests</file>

    <exclude-pattern>samples/Header.php</exclude-pattern>
    <exclude-pattern>*/tests/Core/*/*Test\.(inc|css|js)$</exclude-pattern>

    <arg name="report-width" value="200"/>
    <arg name="parallel" value="80"/>
    <arg name="cache" value="/tmp/.phpspreadsheet.phpcs-cache"/>
    <arg name="colors"/>
    <arg value="np"/>

    <!-- Include the whole PSR12 standard -->
    <rule ref="PSR12">
        <exclude name="PSR2.Methods.MethodDeclaration.Underscore"/>
    </rule>
</ruleset>
