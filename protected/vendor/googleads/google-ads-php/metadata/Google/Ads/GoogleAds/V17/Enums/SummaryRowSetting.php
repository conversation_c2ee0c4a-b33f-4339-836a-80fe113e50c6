<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/summary_row_setting.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class SummaryRowSetting
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
8google/ads/googleads/v17/enums/summary_row_setting.protogoogle.ads.googleads.v17.enums"�
SummaryRowSettingEnum"y
SummaryRowSetting
UNSPECIFIED 
UNKNOWN
NO_SUMMARY_ROW
SUMMARY_ROW_WITH_RESULTS
SUMMARY_ROW_ONLYB�
"com.google.ads.googleads.v17.enumsBSummaryRowSettingProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

