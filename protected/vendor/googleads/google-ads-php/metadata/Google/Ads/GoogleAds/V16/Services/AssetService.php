<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v16/services/asset_service.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V16\Services;

class AssetService
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Protobuf\Duration::initOnce();
        \GPBMetadata\Google\Api\FieldBehavior::initOnce();
        \GPBMetadata\Google\Api\LaunchStage::initOnce();
        \GPBMetadata\Google\Api\Client::initOnce();
        \GPBMetadata\Google\Api\Resource::initOnce();
        \GPBMetadata\Google\Protobuf\FieldMask::initOnce();
        \GPBMetadata\Google\Protobuf\Any::initOnce();
        \GPBMetadata\Google\Rpc\Status::initOnce();
        \GPBMetadata\Google\Api\Http::initOnce();
        \GPBMetadata\Google\Api\Annotations::initOnce();
        $pool->internalAddGeneratedFile(
            '
�
0google/ads/googleads/v16/enums/brand_state.protogoogle.ads.googleads.v16.enums"�
BrandStateEnum"�

BrandState
UNSPECIFIED 
UNKNOWN
ENABLED

DEPRECATED

UNVERIFIED
APPROVED
	CANCELLED
REJECTEDB�
"com.google.ads.googleads.v16.enumsBBrandStateProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
6google/ads/googleads/v16/enums/product_condition.protogoogle.ads.googleads.v16.enums"l
ProductConditionEnum"T
ProductCondition
UNSPECIFIED 
UNKNOWN
NEW
REFURBISHED
USEDB�
"com.google.ads.googleads.v16.enumsBProductConditionProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
;google/ads/googleads/v16/enums/proximity_radius_units.protogoogle.ads.googleads.v16.enums"k
ProximityRadiusUnitsEnum"O
ProximityRadiusUnits
UNSPECIFIED 
UNKNOWN	
MILES

KILOMETERSB�
"com.google.ads.googleads.v16.enumsBProximityRadiusUnitsProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
=google/ads/googleads/v16/enums/lead_form_desired_intent.protogoogle.ads.googleads.v16.enums"s
LeadFormDesiredIntentEnum"V
LeadFormDesiredIntent
UNSPECIFIED 
UNKNOWN

LOW_INTENT
HIGH_INTENTB�
"com.google.ads.googleads.v16.enumsBLeadFormDesiredIntentProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Dgoogle/ads/googleads/v16/enums/call_conversion_reporting_state.protogoogle.ads.googleads.v16.enums"�
 CallConversionReportingStateEnum"�
CallConversionReportingState
UNSPECIFIED 
UNKNOWN
DISABLED,
(USE_ACCOUNT_LEVEL_CALL_CONVERSION_ACTION-
)USE_RESOURCE_LEVEL_CALL_CONVERSION_ACTIONB�
"com.google.ads.googleads.v16.enumsB!CallConversionReportingStateProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
9google/ads/googleads/v16/enums/price_extension_type.protogoogle.ads.googleads.v16.enums"�
PriceExtensionTypeEnum"�
PriceExtensionType
UNSPECIFIED 
UNKNOWN

BRANDS

EVENTS
	LOCATIONS

NEIGHBORHOODS
PRODUCT_CATEGORIES

PRODUCT_TIERS
SERVICES
SERVICE_CATEGORIES	

SERVICE_TIERS
B�
"com.google.ads.googleads.v16.enumsBPriceExtensionTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Cgoogle/ads/googleads/v16/enums/brand_request_rejection_reason.protogoogle.ads.googleads.v16.enums"�
BrandRequestRejectionReasonEnum"�
BrandRequestRejectionReason
UNSPECIFIED 
UNKNOWN
EXISTING_BRAND
EXISTING_BRAND_VARIANT
INCORRECT_INFORMATION
NOT_A_BRANDB�
"com.google.ads.googleads.v16.enumsB BrandRequestRejectionReasonProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
4google/ads/googleads/v16/enums/product_channel.protogoogle.ads.googleads.v16.enums"[
ProductChannelEnum"E
ProductChannel
UNSPECIFIED 
UNKNOWN

ONLINE	
LOCALB�
"com.google.ads.googleads.v16.enumsBProductChannelProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
+google/ads/googleads/v16/enums/device.protogoogle.ads.googleads.v16.enums"v

DeviceEnum"h
Device
UNSPECIFIED 
UNKNOWN

MOBILE

TABLET
DESKTOP
CONNECTED_TV	
OTHERB�
"com.google.ads.googleads.v16.enumsBDeviceProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Bgoogle/ads/googleads/v16/enums/lead_form_call_to_action_type.protogoogle.ads.googleads.v16.enums"�
LeadFormCallToActionTypeEnum"�
LeadFormCallToActionType
UNSPECIFIED 
UNKNOWN

LEARN_MORE
	GET_QUOTE
	APPLY_NOW
SIGN_UP

CONTACT_US
	SUBSCRIBE
DOWNLOAD
BOOK_NOW	
	GET_OFFER

REGISTER
GET_INFO
REQUEST_DEMO

JOIN_NOW
GET_STARTEDB�
"com.google.ads.googleads.v16.enumsBLeadFormCallToActionTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
<google/ads/googleads/v16/enums/policy_topic_entry_type.protogoogle.ads.googleads.v16.enums"�
PolicyTopicEntryTypeEnum"�
PolicyTopicEntryType
UNSPECIFIED 
UNKNOWN

PROHIBITED
LIMITED

FULLY_LIMITED
DESCRIPTIVE

BROADENING
AREA_OF_INTEREST_ONLYB�
"com.google.ads.googleads.v16.enumsBPolicyTopicEntryTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Cgoogle/ads/googleads/v16/enums/product_custom_attribute_index.protogoogle.ads.googleads.v16.enums"�
ProductCustomAttributeIndexEnum"w
ProductCustomAttributeIndex
UNSPECIFIED 
UNKNOWN

INDEX0

INDEX1

INDEX2	

INDEX3


INDEX4B�
"com.google.ads.googleads.v16.enumsB ProductCustomAttributeIndexProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
;google/ads/googleads/v16/enums/app_payment_model_type.protogoogle.ads.googleads.v16.enums"X
AppPaymentModelTypeEnum"=
AppPaymentModelType
UNSPECIFIED 
UNKNOWN
PAIDB�
"com.google.ads.googleads.v16.enumsBAppPaymentModelTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
;google/ads/googleads/v16/enums/product_category_level.protogoogle.ads.googleads.v16.enums"�
ProductCategoryLevelEnum"p
ProductCategoryLevel
UNSPECIFIED 
UNKNOWN

LEVEL1

LEVEL2

LEVEL3

LEVEL4

LEVEL5B�
"com.google.ads.googleads.v16.enumsBProductCategoryLevelProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
9google/ads/googleads/v16/enums/parental_status_type.protogoogle.ads.googleads.v16.enums"
ParentalStatusTypeEnum"e
ParentalStatusType
UNSPECIFIED 
UNKNOWN
PARENT�
NOT_A_PARENT�
UNDETERMINED�B�
"com.google.ads.googleads.v16.enumsBParentalStatusTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
<google/ads/googleads/v16/enums/location_ownership_type.protogoogle.ads.googleads.v16.enums"u
LocationOwnershipTypeEnum"X
LocationOwnershipType
UNSPECIFIED 
UNKNOWN
BUSINESS_OWNER
	AFFILIATEB�
"com.google.ads.googleads.v16.enumsBLocationOwnershipTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
?google/ads/googleads/v16/enums/price_extension_price_unit.protogoogle.ads.googleads.v16.enums"�
PriceExtensionPriceUnitEnum"�
PriceExtensionPriceUnit
UNSPECIFIED 
UNKNOWN
PER_HOUR
PER_DAY
PER_WEEK
	PER_MONTH
PER_YEAR
	PER_NIGHTB�
"com.google.ads.googleads.v16.enumsBPriceExtensionPriceUnitProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
>google/ads/googleads/v16/enums/webpage_condition_operand.protogoogle.ads.googleads.v16.enums"�
WebpageConditionOperandEnum"�
WebpageConditionOperand
UNSPECIFIED 
UNKNOWN
URL
CATEGORY

PAGE_TITLE
PAGE_CONTENT
CUSTOM_LABELB�
"com.google.ads.googleads.v16.enumsBWebpageConditionOperandProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
3google/ads/googleads/v16/enums/age_range_type.protogoogle.ads.googleads.v16.enums"�
AgeRangeTypeEnum"�
AgeRangeType
UNSPECIFIED 
UNKNOWN
AGE_RANGE_18_24��
AGE_RANGE_25_34��
AGE_RANGE_35_44��
AGE_RANGE_45_54��
AGE_RANGE_55_64��
AGE_RANGE_65_UP��
AGE_RANGE_UNDETERMINED��B�
"com.google.ads.googleads.v16.enumsBAgeRangeTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
/google/ads/googleads/v16/enums/asset_type.protogoogle.ads.googleads.v16.enums"�

AssetTypeEnum"�
	AssetType
UNSPECIFIED 
UNKNOWN

YOUTUBE_VIDEO
MEDIA_BUNDLE	
IMAGE
TEXT
	LEAD_FORM
BOOK_ON_GOOGLE
	PROMOTION
CALLOUT	
STRUCTURED_SNIPPET

SITELINK
	PAGE_FEED
DYNAMIC_EDUCATION


MOBILE_APP

HOTEL_CALLOUT
CALL	
PRICE
CALL_TO_ACTION
DYNAMIC_REAL_ESTATE
DYNAMIC_CUSTOM
DYNAMIC_HOTELS_AND_RENTALS
DYNAMIC_FLIGHTS
DISCOVERY_CAROUSEL_CARD
DYNAMIC_TRAVEL

DYNAMIC_LOCAL
DYNAMIC_JOBS
LOCATION
HOTEL_PROPERTYB�
"com.google.ads.googleads.v16.enumsBAssetTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
?google/ads/googleads/v16/enums/webpage_condition_operator.protogoogle.ads.googleads.v16.enums"r
WebpageConditionOperatorEnum"R
WebpageConditionOperator
UNSPECIFIED 
UNKNOWN

EQUALS
CONTAINSB�
"com.google.ads.googleads.v16.enumsBWebpageConditionOperatorProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
9google/ads/googleads/v16/enums/policy_review_status.protogoogle.ads.googleads.v16.enums"�
PolicyReviewStatusEnum"�
PolicyReviewStatus
UNSPECIFIED 
UNKNOWN
REVIEW_IN_PROGRESS
REVIEWED
UNDER_APPEAL
ELIGIBLE_MAY_SERVEB�
"com.google.ads.googleads.v16.enumsBPolicyReviewStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
;google/ads/googleads/v16/enums/policy_approval_status.protogoogle.ads.googleads.v16.enums"�
PolicyApprovalStatusEnum"�
PolicyApprovalStatus
UNSPECIFIED 
UNKNOWN
DISAPPROVED
APPROVED_LIMITED
APPROVED
AREA_OF_INTEREST_ONLYB�
"com.google.ads.googleads.v16.enumsBPolicyApprovalStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
1google/ads/googleads/v16/enums/asset_source.protogoogle.ads.googleads.v16.enums"i
AssetSourceEnum"V
AssetSource
UNSPECIFIED 
UNKNOWN

ADVERTISER
AUTOMATICALLY_CREATEDB�
"com.google.ads.googleads.v16.enumsBAssetSourceProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Agoogle/ads/googleads/v16/enums/promotion_extension_occasion.protogoogle.ads.googleads.v16.enums"�
PromotionExtensionOccasionEnum"�
PromotionExtensionOccasion
UNSPECIFIED 
UNKNOWN
	NEW_YEARS
CHINESE_NEW_YEAR
VALENTINES_DAY

EASTER
MOTHERS_DAY
FATHERS_DAY
	LABOR_DAY
BACK_TO_SCHOOL	
	HALLOWEEN

BLACK_FRIDAY
CYBER_MONDAY
	CHRISTMAS


BOXING_DAY
INDEPENDENCE_DAY
NATIONAL_DAY

END_OF_SEASON
WINTER_SALE
SUMMER_SALE
	FALL_SALE
SPRING_SALE
RAMADAN
EID_AL_FITR
EID_AL_ADHA
SINGLES_DAY

WOMENS_DAY
HOLI
PARENTS_DAY
ST_NICHOLAS_DAY
CARNIVAL
EPIPHANY

ROSH_HASHANAH 
PASSOVER!
HANUKKAH"

DIWALI#
NAVRATRI$
SONGKRAN%

YEAR_END_GIFT&B�
"com.google.ads.googleads.v16.enumsBPromotionExtensionOccasionProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Dgoogle/ads/googleads/v16/enums/price_extension_price_qualifier.protogoogle.ads.googleads.v16.enums"�
 PriceExtensionPriceQualifierEnum"^
PriceExtensionPriceQualifier
UNSPECIFIED 
UNKNOWN
FROM	
UP_TO
AVERAGEB�
"com.google.ads.googleads.v16.enumsB!PriceExtensionPriceQualifierProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
0google/ads/googleads/v16/enums/gender_type.protogoogle.ads.googleads.v16.enums"d
GenderTypeEnum"R

GenderType
UNSPECIFIED 
UNKNOWN
MALE


FEMALE
UNDETERMINEDB�
"com.google.ads.googleads.v16.enumsBGenderTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
1google/ads/googleads/v16/common/feed_common.protogoogle.ads.googleads.v16.common"c
Money

currency_code (	H �

amount_micros (H�B
_currency_codeB
_amount_microsB�
#com.google.ads.googleads.v16.commonBFeedCommonProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v16/common;common�GAA�Google.Ads.GoogleAds.V16.Common�Google\\Ads\\GoogleAds\\V16\\Common�#Google::Ads::GoogleAds::V16::Commonbproto3
�
6google/ads/googleads/v16/enums/income_range_type.protogoogle.ads.googleads.v16.enums"�
IncomeRangeTypeEnum"�
IncomeRangeType
UNSPECIFIED 
UNKNOWN
INCOME_RANGE_0_50��
INCOME_RANGE_50_60��
INCOME_RANGE_60_70��
INCOME_RANGE_70_80��
INCOME_RANGE_80_90��
INCOME_RANGE_90_UP��
INCOME_RANGE_UNDETERMINED��B�
"com.google.ads.googleads.v16.enumsBIncomeRangeTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
6google/ads/googleads/v16/common/custom_parameter.protogoogle.ads.googleads.v16.common"I
CustomParameter
key (	H �
value (	H�B
_keyB
_valueB�
#com.google.ads.googleads.v16.commonBCustomParameterProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v16/common;common�GAA�Google.Ads.GoogleAds.V16.Common�Google\\Ads\\GoogleAds\\V16\\Common�#Google::Ads::GoogleAds::V16::Commonbproto3
�
7google/ads/googleads/v16/enums/listing_group_type.protogoogle.ads.googleads.v16.enums"c
ListingGroupTypeEnum"K
ListingGroupType
UNSPECIFIED 
UNKNOWN
SUBDIVISION
UNITB�
"com.google.ads.googleads.v16.enumsBListingGroupTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
:google/ads/googleads/v16/enums/response_content_type.protogoogle.ads.googleads.v16.enums"o
ResponseContentTypeEnum"T
ResponseContentType
UNSPECIFIED 
RESOURCE_NAME_ONLY
MUTABLE_RESOURCEB�
"com.google.ads.googleads.v16.enumsBResponseContentTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
3google/ads/googleads/v16/enums/minute_of_hour.protogoogle.ads.googleads.v16.enums"s
MinuteOfHourEnum"_
MinuteOfHour
UNSPECIFIED 
UNKNOWN
ZERO
FIFTEEN

THIRTY

FORTY_FIVEB�
"com.google.ads.googleads.v16.enumsBMinuteOfHourProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
7google/ads/googleads/v16/enums/product_type_level.protogoogle.ads.googleads.v16.enums"�
ProductTypeLevelEnum"l
ProductTypeLevel
UNSPECIFIED 
UNKNOWN

LEVEL1

LEVEL2

LEVEL3	

LEVEL4


LEVEL5B�
"com.google.ads.googleads.v16.enumsBProductTypeLevelProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
>google/ads/googleads/v16/enums/hotel_date_selection_type.protogoogle.ads.googleads.v16.enums"~
HotelDateSelectionTypeEnum"`
HotelDateSelectionType
UNSPECIFIED 
UNKNOWN
DEFAULT_SELECTION2

USER_SELECTED3B�
"com.google.ads.googleads.v16.enumsBHotelDateSelectionTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
0google/ads/googleads/v16/enums/day_of_week.protogoogle.ads.googleads.v16.enums"�

DayOfWeekEnum"�
	DayOfWeek
UNSPECIFIED 
UNKNOWN

MONDAY
TUESDAY
	WEDNESDAY
THURSDAY

FRIDAY
SATURDAY

SUNDAYB�
"com.google.ads.googleads.v16.enumsBDayOfWeekProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
7google/ads/googleads/v16/enums/content_label_type.protogoogle.ads.googleads.v16.enums"�
ContentLabelTypeEnum"�
ContentLabelType
UNSPECIFIED 
UNKNOWN
SEXUALLY_SUGGESTIVE
BELOW_THE_FOLD

PARKED_DOMAIN
JUVENILE
	PROFANITY
TRAGEDY	
VIDEO	
VIDEO_RATING_DV_G

VIDEO_RATING_DV_PG
VIDEO_RATING_DV_T
VIDEO_RATING_DV_MA

VIDEO_NOT_YET_RATED
EMBEDDED_VIDEO
LIVE_STREAMING_VIDEO

SOCIAL_ISSUESB�
"com.google.ads.googleads.v16.enumsBContentLabelTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Ngoogle/ads/googleads/v16/enums/lead_form_post_submit_call_to_action_type.protogoogle.ads.googleads.v16.enums"�
&LeadFormPostSubmitCallToActionTypeEnum"~
"LeadFormPostSubmitCallToActionType
UNSPECIFIED 
UNKNOWN

VISIT_SITE
DOWNLOAD

LEARN_MORE
SHOP_NOWB�
"com.google.ads.googleads.v16.enumsB\'LeadFormPostSubmitCallToActionTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Xgoogle/ads/googleads/v16/enums/policy_topic_evidence_destination_mismatch_url_type.protogoogle.ads.googleads.v16.enums"�
1PolicyTopicEvidenceDestinationMismatchUrlTypeEnum"�
-PolicyTopicEvidenceDestinationMismatchUrlType
UNSPECIFIED 
UNKNOWN
DISPLAY_URL
	FINAL_URL
FINAL_MOBILE_URL
TRACKING_URL
MOBILE_TRACKING_URLB�
"com.google.ads.googleads.v16.enumsB2PolicyTopicEvidenceDestinationMismatchUrlTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
@google/ads/googleads/v16/enums/product_channel_exclusivity.protogoogle.ads.googleads.v16.enums"�
ProductChannelExclusivityEnum"`
ProductChannelExclusivity
UNSPECIFIED 
UNKNOWN
SINGLE_CHANNEL

MULTI_CHANNELB�
"com.google.ads.googleads.v16.enumsBProductChannelExclusivityProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Jgoogle/ads/googleads/v16/enums/promotion_extension_discount_modifier.protogoogle.ads.googleads.v16.enums"w
&PromotionExtensionDiscountModifierEnum"M
"PromotionExtensionDiscountModifier
UNSPECIFIED 
UNKNOWN	
UP_TOB�
"com.google.ads.googleads.v16.enumsB\'PromotionExtensionDiscountModifierProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
7google/ads/googleads/v16/enums/keyword_match_type.protogoogle.ads.googleads.v16.enums"j
KeywordMatchTypeEnum"R
KeywordMatchType
UNSPECIFIED 
UNKNOWN	
EXACT

PHRASE	
BROADB�
"com.google.ads.googleads.v16.enumsBKeywordMatchTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
5google/ads/googleads/v16/enums/asset_field_type.protogoogle.ads.googleads.v16.enums"�
AssetFieldTypeEnum"�
AssetFieldType
UNSPECIFIED 
UNKNOWN
HEADLINE
DESCRIPTION
MANDATORY_AD_TEXT
MARKETING_IMAGE
MEDIA_BUNDLE

YOUTUBE_VIDEO
BOOK_ON_GOOGLE
	LEAD_FORM	
	PROMOTION

CALLOUT
STRUCTURED_SNIPPET
SITELINK


MOBILE_APP

HOTEL_CALLOUT
CALL	
PRICE

LONG_HEADLINE

BUSINESS_NAME
SQUARE_MARKETING_IMAGE
PORTRAIT_MARKETING_IMAGE
LOGO
LANDSCAPE_LOGO	
VIDEO
CALL_TO_ACTION_SELECTION
AD_IMAGE

BUSINESS_LOGO
HOTEL_PROPERTY
DISCOVERY_CAROUSEL_CARDB�
"com.google.ads.googleads.v16.enumsBAssetFieldTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
agoogle/ads/googleads/v16/enums/policy_topic_evidence_destination_not_working_dns_error_type.protogoogle.ads.googleads.v16.enums"�
8PolicyTopicEvidenceDestinationNotWorkingDnsErrorTypeEnum"�
4PolicyTopicEvidenceDestinationNotWorkingDnsErrorType
UNSPECIFIED 
UNKNOWN
HOSTNAME_NOT_FOUND
GOOGLE_CRAWLER_DNS_ISSUEB�
"com.google.ads.googleads.v16.enumsB9PolicyTopicEvidenceDestinationNotWorkingDnsErrorTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
8google/ads/googleads/v16/enums/call_to_action_type.protogoogle.ads.googleads.v16.enums"�
CallToActionTypeEnum"�
CallToActionType
UNSPECIFIED 
UNKNOWN

LEARN_MORE
	GET_QUOTE
	APPLY_NOW
SIGN_UP

CONTACT_US
	SUBSCRIBE
DOWNLOAD
BOOK_NOW	
SHOP_NOW

BUY_NOW

DONATE_NOW
	ORDER_NOW

PLAY_NOW
SEE_MORE
	START_NOW

VISIT_SITE
	WATCH_NOWB�
"com.google.ads.googleads.v16.enumsBCallToActionTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
.google/ads/googleads/v16/enums/mime_type.protogoogle.ads.googleads.v16.enums"�
MimeTypeEnum"�
MimeType
UNSPECIFIED 
UNKNOWN

IMAGE_JPEG
	IMAGE_GIF
	IMAGE_PNG	
FLASH
	TEXT_HTML
PDF

MSWORD
MSEXCEL	
RTF

	AUDIO_WAV
	AUDIO_MP3
HTML5_AD_ZIP
B�
"com.google.ads.googleads.v16.enumsB
MimeTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Dgoogle/ads/googleads/v16/enums/lead_form_field_user_input_type.protogoogle.ads.googleads.v16.enums"�
LeadFormFieldUserInputTypeEnum"�
LeadFormFieldUserInputType
UNSPECIFIED 
UNKNOWN
	FULL_NAME	
EMAIL
PHONE_NUMBER
POSTAL_CODE
STREET_ADDRESS
CITY	

REGION

COUNTRY

WORK_EMAIL
COMPANY_NAME


WORK_PHONE
	JOB_TITLE
GOVERNMENT_ISSUED_ID_CPF_BR
GOVERNMENT_ISSUED_ID_DNI_AR
GOVERNMENT_ISSUED_ID_DNI_PE
GOVERNMENT_ISSUED_ID_RUT_CL
GOVERNMENT_ISSUED_ID_CC_CO
GOVERNMENT_ISSUED_ID_CI_EC
GOVERNMENT_ISSUED_ID_RFC_MX

FIRST_NAME
	LAST_NAME

VEHICLE_MODEL�
VEHICLE_TYPE�
PREFERRED_DEALERSHIP�
VEHICLE_PURCHASE_TIMELINE�
VEHICLE_OWNERSHIP�
VEHICLE_PAYMENT_TYPE�
VEHICLE_CONDITION�
COMPANY_SIZE�
ANNUAL_SALES�
YEARS_IN_BUSINESS�
JOB_DEPARTMENT�
JOB_ROLE�
OVER_18_AGE�
OVER_19_AGE�
OVER_20_AGE�
OVER_21_AGE�
OVER_22_AGE�
OVER_23_AGE�
OVER_24_AGE�
OVER_25_AGE�
OVER_26_AGE�
OVER_27_AGE�
OVER_28_AGE�
OVER_29_AGE�
OVER_30_AGE�
OVER_31_AGE�
OVER_32_AGE�
OVER_33_AGE�
OVER_34_AGE�
OVER_35_AGE�
OVER_36_AGE�
OVER_37_AGE�
OVER_38_AGE�
OVER_39_AGE�
OVER_40_AGE�
OVER_41_AGE�
OVER_42_AGE�
OVER_43_AGE�
OVER_44_AGE�
OVER_45_AGE�
OVER_46_AGE�
OVER_47_AGE�
OVER_48_AGE�
OVER_49_AGE�
OVER_50_AGE�
OVER_51_AGE�
OVER_52_AGE�
OVER_53_AGE�
OVER_54_AGE�
OVER_55_AGE�
OVER_56_AGE�
OVER_57_AGE�
OVER_58_AGE�
OVER_59_AGE�
OVER_60_AGE�
OVER_61_AGE�
OVER_62_AGE�
OVER_63_AGE�
OVER_64_AGE�
OVER_65_AGE�
EDUCATION_PROGRAM�
EDUCATION_COURSE�
PRODUCT�
SERVICE�

OFFER�
CATEGORY�
PREFERRED_CONTACT_METHOD�
PREFERRED_LOCATION�
PREFERRED_CONTACT_TIME�
PURCHASE_TIMELINE�
YEARS_OF_EXPERIENCE�
JOB_INDUSTRY�
LEVEL_OF_EDUCATION�

PROPERTY_TYPE�
REALTOR_HELP_GOAL�
PROPERTY_COMMUNITY�
PRICE_RANGE�
NUMBER_OF_BEDROOMS�
FURNISHED_PROPERTY�
PETS_ALLOWED_PROPERTY�
NEXT_PLANNED_PURCHASE�
EVENT_SIGNUP_INTEREST�
PREFERRED_SHOPPING_PLACES�
FAVORITE_BRAND�+
&TRANSPORTATION_COMMERCIAL_LICENSE_TYPE�
EVENT_BOOKING_INTEREST�
DESTINATION_COUNTRY�
DESTINATION_CITY�
DEPARTURE_COUNTRY�
DEPARTURE_CITY�
DEPARTURE_DATE�
RETURN_DATE�
NUMBER_OF_TRAVELERS�

TRAVEL_BUDGET�
TRAVEL_ACCOMMODATION�B�
"com.google.ads.googleads.v16.enumsBLeadFormFieldUserInputTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Ygoogle/ads/googleads/v16/enums/policy_topic_evidence_destination_not_working_device.protogoogle.ads.googleads.v16.enums"�
2PolicyTopicEvidenceDestinationNotWorkingDeviceEnum"q
.PolicyTopicEvidenceDestinationNotWorkingDevice
UNSPECIFIED 
UNKNOWN
DESKTOP
ANDROID
IOSB�
"com.google.ads.googleads.v16.enumsB3PolicyTopicEvidenceDestinationNotWorkingDeviceProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
,google/ads/googleads/v16/common/policy.protogoogle.ads.googleads.v16.commonXgoogle/ads/googleads/v16/enums/policy_topic_evidence_destination_mismatch_url_type.protoYgoogle/ads/googleads/v16/enums/policy_topic_evidence_destination_not_working_device.protoagoogle/ads/googleads/v16/enums/policy_topic_evidence_destination_not_working_dns_error_type.proto"n
PolicyViolationKey
policy_name (	H �
violating_text (	H�B
_policy_nameB
_violating_text"�
PolicyValidationParameter
ignorable_policy_topics (	Y
exempt_policy_violation_keys (23.google.ads.googleads.v16.common.PolicyViolationKey"�
PolicyTopicEntry
topic (	H �[
type (2M.google.ads.googleads.v16.enums.PolicyTopicEntryTypeEnum.PolicyTopicEntryTypeG
	evidences (24.google.ads.googleads.v16.common.PolicyTopicEvidenceK
constraints (26.google.ads.googleads.v16.common.PolicyTopicConstraintB
_topic"�

PolicyTopicEvidenceX
website_list (<EMAIL> R
	text_list (2=.google.ads.googleads.v16.common.PolicyTopicEvidence.TextListH 

language_code	 (	H i
destination_text_list (2H.google.ads.googleads.v16.common.PolicyTopicEvidence.DestinationTextListH h
destination_mismatch (2H.google.ads.googleads.v16.common.PolicyTopicEvidence.DestinationMismatchH m
destination_not_working (2J.google.ads.googleads.v16.common.PolicyTopicEvidence.DestinationNotWorkingH 
TextList
texts (	
WebsiteList
websites (	0
DestinationTextList
destination_texts (	�
DestinationMismatch�
	url_types (2.google.ads.googleads.v16.enums.PolicyTopicEvidenceDestinationMismatchUrlTypeEnum.PolicyTopicEvidenceDestinationMismatchUrlType�
DestinationNotWorking
expanded_url (	H��
device (2�.google.ads.googleads.v16.enums.PolicyTopicEvidenceDestinationNotWorkingDeviceEnum.PolicyTopicEvidenceDestinationNotWorkingDevice#
last_checked_date_time (	H��
dns_error_type (2�.google.ads.googleads.v16.enums.PolicyTopicEvidenceDestinationNotWorkingDnsErrorTypeEnum.PolicyTopicEvidenceDestinationNotWorkingDnsErrorTypeH 
http_error_code (H B
reasonB

_expanded_urlB
_last_checked_date_timeB
value"�
PolicyTopicConstrainto
country_constraint_list (2L.google.ads.googleads.v16.common.PolicyTopicConstraint.CountryConstraintListH h
reseller_constraint (2I.google.ads.googleads.v16.common.PolicyTopicConstraint.ResellerConstraintH {
#certificate_missing_in_country_list (2L.google.ads.googleads.v16.common.PolicyTopicConstraint.CountryConstraintListH �
+certificate_domain_mismatch_in_country_list (2L.google.ads.googleads.v16.common.PolicyTopicConstraint.CountryConstraintListH �
CountryConstraintList%
total_targeted_countries (H �[
	countries (2H.google.ads.googleads.v16.common.PolicyTopicConstraint.CountryConstraintB
_total_targeted_countries
ResellerConstraintI
CountryConstraint
country_criterion (	H �B
_country_criterionB
valueB�
#com.google.ads.googleads.v16.commonBPolicyProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v16/common;common�GAA�Google.Ads.GoogleAds.V16.Common�Google\\Ads\\GoogleAds\\V16\\Common�#Google::Ads::GoogleAds::V16::Commonbproto3
�
@google/ads/googleads/v16/enums/location_group_radius_units.protogoogle.ads.googleads.v16.enums"�
LocationGroupRadiusUnitsEnum"`
LocationGroupRadiusUnits
UNSPECIFIED 
UNKNOWN

METERS	
MILES
MILLI_MILESB�
"com.google.ads.googleads.v16.enumsBLocationGroupRadiusUnitsProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
6google/ads/googleads/v16/enums/mobile_app_vendor.protogoogle.ads.googleads.v16.enums"q
MobileAppVendorEnum"Z
MobileAppVendor
UNSPECIFIED 
UNKNOWN
APPLE_APP_STORE
GOOGLE_APP_STOREB�
"com.google.ads.googleads.v16.enumsBMobileAppVendorProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
5google/ads/googleads/v16/enums/interaction_type.protogoogle.ads.googleads.v16.enums"R
InteractionTypeEnum";
InteractionType
UNSPECIFIED 
UNKNOWN

CALLS�>B�
"com.google.ads.googleads.v16.enumsBInteractionTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�\\
.google/ads/googleads/v16/common/criteria.protogoogle.ads.googleads.v16.common;google/ads/googleads/v16/enums/app_payment_model_type.protoCgoogle/ads/googleads/v16/enums/brand_request_rejection_reason.proto0google/ads/googleads/v16/enums/brand_state.proto7google/ads/googleads/v16/enums/content_label_type.proto0google/ads/googleads/v16/enums/day_of_week.proto+google/ads/googleads/v16/enums/device.proto0google/ads/googleads/v16/enums/gender_type.proto>google/ads/googleads/v16/enums/hotel_date_selection_type.proto6google/ads/googleads/v16/enums/income_range_type.proto5google/ads/googleads/v16/enums/interaction_type.proto7google/ads/googleads/v16/enums/keyword_match_type.proto7google/ads/googleads/v16/enums/listing_group_type.proto@google/ads/googleads/v16/enums/location_group_radius_units.proto3google/ads/googleads/v16/enums/minute_of_hour.proto9google/ads/googleads/v16/enums/parental_status_type.proto;google/ads/googleads/v16/enums/product_category_level.proto4google/ads/googleads/v16/enums/product_channel.proto@google/ads/googleads/v16/enums/product_channel_exclusivity.proto6google/ads/googleads/v16/enums/product_condition.protoCgoogle/ads/googleads/v16/enums/product_custom_attribute_index.proto7google/ads/googleads/v16/enums/product_type_level.proto;google/ads/googleads/v16/enums/proximity_radius_units.proto>google/ads/googleads/v16/enums/webpage_condition_operand.proto?google/ads/googleads/v16/enums/webpage_condition_operator.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�
KeywordInfo
text (	H �Y

match_type (2E.google.ads.googleads.v16.enums.KeywordMatchTypeEnum.KeywordMatchTypeB
_text")

PlacementInfo
url (	H �B
_url"A
NegativeKeywordListInfo

shared_set (	H �B
_shared_set"�
MobileAppCategoryInfob
mobile_app_category_constant (	B7�A4
2googleads.googleapis.com/MobileAppCategoryConstantH �B
_mobile_app_category_constant"S
MobileApplicationInfo
app_id (	H �
name (	H�B	
_app_idB
_name"H
LocationInfo 
geo_target_constant (	H �B
_geo_target_constant"M

DeviceInfo?
type (21.google.ads.googleads.v16.enums.DeviceEnum.Device"�
ListingGroupInfoS
type (2E.google.ads.googleads.v16.enums.ListingGroupTypeEnum.ListingGroupTypeI

case_value (25.google.ads.googleads.v16.common.ListingDimensionInfo&
parent_ad_group_criterion (	H �H
path (25.google.ads.googleads.v16.common.ListingDimensionPathH�B
_parent_ad_group_criterionB
_path"a
ListingDimensionPathI

dimensions (25.google.ads.googleads.v16.common.ListingDimensionInfo"]
ListingScopeInfoI

dimensions (25.google.ads.googleads.v16.common.ListingDimensionInfo"�
ListingDimensionInfo@
hotel_id (2,.google.ads.googleads.v16.common.HotelIdInfoH F
hotel_class (2/.google.ads.googleads.v16.common.HotelClassInfoH W
hotel_country_region (27.google.ads.googleads.v16.common.HotelCountryRegionInfoH F
hotel_state (2/.google.ads.googleads.v16.common.HotelStateInfoH D

hotel_city (2..google.ads.googleads.v16.common.HotelCityInfoH P
product_category (24.google.ads.googleads.v16.common.ProductCategoryInfoH J

product_brand (21.google.ads.googleads.v16.common.ProductBrandInfoH N
product_channel (23.google.ads.googleads.v16.common.ProductChannelInfoH e
product_channel_exclusivity	 (2>.google.ads.googleads.v16.common.ProductChannelExclusivityInfoH R
product_condition
 (25.google.ads.googleads.v16.common.ProductConditionInfoH _
product_custom_attribute (2;.google.ads.googleads.v16.common.ProductCustomAttributeInfoH M
product_item_id (22.google.ads.googleads.v16.common.ProductItemIdInfoH H
product_type (20.google.ads.googleads.v16.common.ProductTypeInfoH P
product_grouping (24.google.ads.googleads.v16.common.ProductGroupingInfoH L
product_labels (22.google.ads.googleads.v16.common.ProductLabelsInfoH _
product_legacy_condition (2;.google.ads.googleads.v16.common.ProductLegacyConditionInfoH Q
product_type_full (24.google.ads.googleads.v16.common.ProductTypeFullInfoH F
activity_id (2/.google.ads.googleads.v16.common.ActivityIdInfoH N
activity_rating (23.google.ads.googleads.v16.common.ActivityRatingInfoH P
activity_country (24.google.ads.googleads.v16.common.ActivityCountryInfoH L
activity_state (22.google.ads.googleads.v16.common.ActivityStateInfoH J

activity_city (21.google.ads.googleads.v16.common.ActivityCityInfoH a
unknown_listing_dimension (2<.google.ads.googleads.v16.common.UnknownListingDimensionInfoH B
	dimension"+
HotelIdInfo
value (	H �B
_value".
HotelClassInfo
value (H �B
_value"\\
HotelCountryRegionInfo%
country_region_criterion (	H �B
_country_region_criterion"B
HotelStateInfo
state_criterion (	H �B
_state_criterion"?

HotelCityInfo
city_criterion (	H �B
_city_criterion"�
ProductCategoryInfo
category_id (H �\\
level (2M.google.ads.googleads.v16.enums.ProductCategoryLevelEnum.ProductCategoryLevelB
_category_id"0
ProductBrandInfo
value (	H �B
_value"h
ProductChannelInfoR
channel (2A.google.ads.googleads.v16.enums.ProductChannelEnum.ProductChannel"�
ProductChannelExclusivityInfot
channel_exclusivity (2W.google.ads.googleads.v16.enums.ProductChannelExclusivityEnum.ProductChannelExclusivity"p
ProductConditionInfoX
	condition (2E.google.ads.googleads.v16.enums.ProductConditionEnum.ProductCondition"�
ProductCustomAttributeInfo
value (	H �j
index (2[.google.ads.googleads.v16.enums.ProductCustomAttributeIndexEnum.ProductCustomAttributeIndexB
_value"1
ProductItemIdInfo
value (	H �B
_value"�
ProductTypeInfo
value (	H �T
level (2E.google.ads.googleads.v16.enums.ProductTypeLevelEnum.ProductTypeLevelB
_value"3
ProductGroupingInfo
value (	H �B
_value"1
ProductLabelsInfo
value (	H �B
_value":
ProductLegacyConditionInfo
value (	H �B
_value"3
ProductTypeFullInfo
value (	H �B
_value"
UnknownListingDimensionInfo"}
HotelDateSelectionTypeInfo_
type (2Q.google.ads.googleads.v16.enums.HotelDateSelectionTypeEnum.HotelDateSelectionType"g
HotelAdvanceBookingWindowInfo
min_days (H �
max_days (H�B
	_min_daysB
	_max_days"g
HotelLengthOfStayInfo

min_nights (H �

max_nights (H�B
_min_nightsB
_max_nights"A
HotelCheckInDateRangeInfo

start_date (	
end_date (	"c
HotelCheckInDayInfoL
day_of_week (27.google.ads.googleads.v16.enums.DayOfWeekEnum.DayOfWeek".
ActivityIdInfo
value (	H �B
_value"2
ActivityRatingInfo
value (H �B
_value"3
ActivityCountryInfo
value (	H �B
_value"1
ActivityStateInfo
value (	H �B
_value"0
ActivityCityInfo
value (	H �B
_value"h
InteractionTypeInfoQ
type (2C.google.ads.googleads.v16.enums.InteractionTypeEnum.InteractionType"�
AdScheduleInfoS
start_minute (2=.google.ads.googleads.v16.enums.MinuteOfHourEnum.MinuteOfHourQ

end_minute (2=.google.ads.googleads.v16.enums.MinuteOfHourEnum.MinuteOfHour

start_hour (H �
end_hour (H�L
day_of_week (27.google.ads.googleads.v16.enums.DayOfWeekEnum.DayOfWeekB
_start_hourB
	_end_hour"[
AgeRangeInfoK
type (2=.google.ads.googleads.v16.enums.AgeRangeTypeEnum.AgeRangeType"U

GenderInfoG
type (29.google.ads.googleads.v16.enums.GenderTypeEnum.GenderType"d
IncomeRangeInfoQ
type (2C.google.ads.googleads.v16.enums.IncomeRangeTypeEnum.IncomeRangeType"m
ParentalStatusInfoW
type (2I.google.ads.googleads.v16.enums.ParentalStatusTypeEnum.ParentalStatusType"6
YouTubeVideoInfo
video_id (	H �B
	_video_id"<
YouTubeChannelInfo

channel_id (	H �B
_channel_id"4
UserListInfo
	user_list (	H �B

_user_list"�

ProximityInfo@
	geo_point (2-.google.ads.googleads.v16.common.GeoPointInfo
radius (H �c
radius_units (2M.google.ads.googleads.v16.enums.ProximityRadiusUnitsEnum.ProximityRadiusUnits=
address (2,.google.ads.googleads.v16.common.AddressInfoB	
_radius"�
GeoPointInfo\'
longitude_in_micro_degrees (H �&
latitude_in_micro_degrees (H�B
_longitude_in_micro_degreesB
_latitude_in_micro_degrees"�
AddressInfo
postal_code (	H �

province_code	 (	H�
country_code
 (	H�

province_name (	H�
street_address (	H�
street_address2
 (	H�
	city_name (	H�B
_postal_codeB
_province_codeB

_country_codeB
_province_nameB
_street_addressB
_street_address2B

_city_name"v
	TopicInfoH
topic_constant (	B+�A(
&googleads.googleapis.com/TopicConstantH �
path (	B
_topic_constant"D
LanguageInfo
language_constant (	H �B
_language_constant"5
IpBlockInfo

ip_address (	H �B
_ip_address"g
ContentLabelInfoS
type (2E.google.ads.googleads.v16.enums.ContentLabelTypeEnum.ContentLabelType"p
CarrierInfoL
carrier_constant (	B-�A*
(googleads.googleapis.com/CarrierConstantH �B
_carrier_constant"R
UserInterestInfo#
user_interest_category (	H �B
_user_interest_category"�
WebpageInfo
criterion_name (	H �I

conditions (25.google.ads.googleads.v16.common.WebpageConditionInfo
coverage_percentage (B
sample (22.google.ads.googleads.v16.common.WebpageSampleInfoB
_criterion_name"�
WebpageConditionInfod
operand (2S.google.ads.googleads.v16.enums.WebpageConditionOperandEnum.WebpageConditionOperandg
operator (2U.google.ads.googleads.v16.enums.WebpageConditionOperatorEnum.WebpageConditionOperator
argument (	H �B
	_argument"(
WebpageSampleInfo
sample_urls (	"�
OperatingSystemVersionInfol
!operating_system_version_constant (	B<�A9
7googleads.googleapis.com/OperatingSystemVersionConstantH �B$
"_operating_system_version_constant"p
AppPaymentModelInfoY
type (2K.google.ads.googleads.v16.enums.AppPaymentModelTypeEnum.AppPaymentModelType"�
MobileDeviceInfoW
mobile_device_constant (	B2�A/
-googleads.googleapis.com/MobileDeviceConstantH �B
_mobile_device_constant"F
CustomAffinityInfo
custom_affinity (	H �B
_custom_affinity"@
CustomIntentInfo

custom_intent (	H �B
_custom_intent"�
LocationGroupInfo
feed (	H �
geo_target_constants (	
radius (H�k
radius_units (2U.google.ads.googleads.v16.enums.LocationGroupRadiusUnitsEnum.LocationGroupRadiusUnits
feed_item_sets (	5
(enable_customer_level_location_asset_set	 (H�!
location_group_asset_sets
 (	B
_feedB	
_radiusB+
)_enable_customer_level_location_asset_set"-
CustomAudienceInfo
custom_audience (	"a
CombinedAudienceInfoI
combined_audience (	B.�A+
)googleads.googleapis.com/CombinedAudience" 
AudienceInfo
audience (	"�
KeywordThemeInfoT
keyword_theme_constant (	B2�A/
-googleads.googleapis.com/KeywordThemeConstantH !
free_form_keyword_theme (	H B

keyword_theme"(
LocalServiceIdInfo

service_id (	"
SearchThemeInfo
text (	"�
	BrandInfo
display_name (	B�AH �
	entity_id (	H�
primary_url (	B�AH�
rejection_reason (2[.google.ads.googleads.v16.enums.BrandRequestRejectionReasonEnum.BrandRequestRejectionReasonB�AH�S
status (29.google.ads.googleads.v16.enums.BrandStateEnum.BrandStateB�AH�B

_display_nameB

_entity_idB
_primary_urlB
_rejection_reasonB	
_status"7

BrandListInfo

shared_set (	H �B
_shared_setB�
#com.google.ads.googleads.v16.commonB
CriteriaProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v16/common;common�GAA�Google.Ads.GoogleAds.V16.Common�Google\\Ads\\GoogleAds\\V16\\Common�#Google::Ads::GoogleAds::V16::Commonbproto3
�N
1google/ads/googleads/v16/common/asset_types.protogoogle.ads.googleads.v16.common1google/ads/googleads/v16/common/feed_common.protoDgoogle/ads/googleads/v16/enums/call_conversion_reporting_state.proto8google/ads/googleads/v16/enums/call_to_action_type.protoBgoogle/ads/googleads/v16/enums/lead_form_call_to_action_type.proto=google/ads/googleads/v16/enums/lead_form_desired_intent.protoDgoogle/ads/googleads/v16/enums/lead_form_field_user_input_type.protoNgoogle/ads/googleads/v16/enums/lead_form_post_submit_call_to_action_type.proto<google/ads/googleads/v16/enums/location_ownership_type.proto.google/ads/googleads/v16/enums/mime_type.proto6google/ads/googleads/v16/enums/mobile_app_vendor.protoDgoogle/ads/googleads/v16/enums/price_extension_price_qualifier.proto?google/ads/googleads/v16/enums/price_extension_price_unit.proto9google/ads/googleads/v16/enums/price_extension_type.protoJgoogle/ads/googleads/v16/enums/promotion_extension_discount_modifier.protoAgoogle/ads/googleads/v16/enums/promotion_extension_occasion.protogoogle/api/field_behavior.protogoogle/api/resource.proto"d
YoutubeVideoAsset
youtube_video_id (	H �
youtube_video_title (	B
_youtube_video_id".
MediaBundleAsset
data (H �B
_data"�

ImageAsset
data (H �
	file_size (H�H
	mime_type (25.google.ads.googleads.v16.enums.MimeTypeEnum.MimeTypeB
	full_size (2/.google.ads.googleads.v16.common.ImageDimensionB
_dataB

_file_size"�
ImageDimension

height_pixels (H �
width_pixels (H�
url (	H�B
_height_pixelsB

_width_pixelsB
_url"\'
	TextAsset
text (	H �B
_text"�

LeadFormAsset

business_name
 (	B�Aw
call_to_action_type (2U.google.ads.googleads.v16.enums.LeadFormCallToActionTypeEnum.LeadFormCallToActionTypeB�A\'
call_to_action_description (	B�A
headline (	B�A
description
 (	B�A
privacy_policy_url (	B�A!
post_submit_headline (	H �$
post_submit_description (	H�>
fields (2..google.ads.googleads.v16.common.LeadFormField\\
custom_question_fields (2<.google.ads.googleads.v16.common.LeadFormCustomQuestionFieldQ
delivery_methods	 (27.google.ads.googleads.v16.common.LeadFormDeliveryMethod�
post_submit_call_to_action_type (2i.google.ads.googleads.v16.enums.LeadFormPostSubmitCallToActionTypeEnum.LeadFormPostSubmitCallToActionType#
background_image_asset (	H�g
desired_intent (2O.google.ads.googleads.v16.enums.LeadFormDesiredIntentEnum.LeadFormDesiredIntent
custom_disclosure (	H�B
_post_submit_headlineB
_post_submit_descriptionB
_background_image_assetB
_custom_disclosure"�

LeadFormFieldm

input_type (2Y.google.ads.googleads.v16.enums.LeadFormFieldUserInputTypeEnum.LeadFormFieldUserInputType]
single_choice_answers (2<.google.ads.googleads.v16.common.LeadFormSingleChoiceAnswersH 
has_location_answer (H B	
answers"�
LeadFormCustomQuestionField
custom_question_text (	]
single_choice_answers (2<.google.ads.googleads.v16.common.LeadFormSingleChoiceAnswersH 
has_location_answer (H B	
answers".
LeadFormSingleChoiceAnswers
answers (	"q
LeadFormDeliveryMethodC
webhook (20.google.ads.googleads.v16.common.WebhookDeliveryH B
delivery_details"�
WebhookDelivery#
advertiser_webhook_url (	H �

google_secret (	H�#
payload_schema_version (H�B
_advertiser_webhook_urlB
_google_secretB
_payload_schema_version"
BookOnGoogleAsset"�
PromotionAsset
promotion_target (	B�A�
discount_modifier (2i.google.ads.googleads.v16.enums.PromotionExtensionDiscountModifierEnum.PromotionExtensionDiscountModifier
redemption_start_date (	
redemption_end_date (	k
occasion	 (2Y.google.ads.googleads.v16.enums.PromotionExtensionOccasionEnum.PromotionExtensionOccasion

language_code
 (	

start_date (	
end_date (	L
ad_schedule_targets
 (2/.google.ads.googleads.v16.common.AdScheduleInfo
percent_off (H B
money_amount_off (2&.google.ads.googleads.v16.common.MoneyH 
promotion_code (	HD
orders_over_amount (2&.google.ads.googleads.v16.common.MoneyHB

discount_typeB
promotion_trigger"�
CalloutAsset
callout_text (	B�A

start_date (	
end_date (	L
ad_schedule_targets (2/.google.ads.googleads.v16.common.AdScheduleInfo"B
StructuredSnippetAsset
header (	B�A
values (	B�A"�

SitelinkAsset
	link_text (	B�A
description1 (	
description2 (	

start_date (	
end_date (	L
ad_schedule_targets (2/.google.ads.googleads.v16.common.AdScheduleInfo"6

PageFeedAsset
page_url (	B�A
labels (	"�
DynamicEducationAsset

program_id (	B�A
location_id (	
program_name (	B�A
subject (	
program_description (	
school_name (	
address (	
contextual_keywords (	
android_app_link	 (	
similar_program_ids
 (	
ios_app_link (	
ios_app_store_id (
thumbnail_image_url
 (	
	image_url (	"�
MobileAppAsset
app_id (	B�A[
	app_store (2C.google.ads.googleads.v16.enums.MobileAppVendorEnum.MobileAppVendorB�A
	link_text (	B�A

start_date (	
end_date (	"B
HotelCalloutAsset
text (	B�A

language_code (	B�A"�
	CallAsset
country_code (	B�A
phone_number (	B�A�
call_conversion_reporting_state (2].google.ads.googleads.v16.enums.CallConversionReportingStateEnum.CallConversionReportingStateN
call_conversion_action (	B.�A+
)googleads.googleapis.com/ConversionActionL
ad_schedule_targets (2/.google.ads.googleads.v16.common.AdScheduleInfo"�

PriceAsset\\
type (2I.google.ads.googleads.v16.enums.PriceExtensionTypeEnum.PriceExtensionTypeB�Av
price_qualifier (2].google.ads.googleads.v16.enums.PriceExtensionPriceQualifierEnum.PriceExtensionPriceQualifier

language_code (	B�AG
price_offerings (2..google.ads.googleads.v16.common.PriceOffering"�

PriceOffering
header (	B�A
description (	B�A:
price (2&.google.ads.googleads.v16.common.MoneyB�Aa
unit (2S.google.ads.googleads.v16.enums.PriceExtensionPriceUnitEnum.PriceExtensionPriceUnit
	final_url (	B�A
final_mobile_url (	"r
CallToActionAsset]
call_to_action (2E.google.ads.googleads.v16.enums.CallToActionTypeEnum.CallToActionType"�
DynamicRealEstateAsset

listing_id (	B�A
listing_name (	B�A
	city_name (	
description (	
address (	
price (	
	image_url (	

property_type (	
listing_type	 (	
contextual_keywords
 (	
formatted_price (	
android_app_link (	
ios_app_link
 (	
ios_app_store_id (
similar_listing_ids (	"�
DynamicCustomAsset
id (	B�A
id2 (	

item_title (	B�A

item_subtitle (	
item_description (	
item_address (	

item_category (	
price (	

sale_price	 (	
formatted_price
 (	
formatted_sale_price (	
	image_url (	
contextual_keywords
 (	
android_app_link (	
ios_app_link (	
ios_app_store_id (
similar_ids (	"�
DynamicHotelsAndRentalsAsset
property_id (	B�A

property_name (	B�A
	image_url (	
destination_name (	
description (	
price (	

sale_price (	
star_rating (
category	 (	
contextual_keywords
 (	
address (	
android_app_link (	
ios_app_link
 (	
ios_app_store_id (
formatted_price (	
formatted_sale_price (	
similar_property_ids (	"�
DynamicFlightsAsset
destination_id (	B�A
	origin_id (	
flight_description (	B�A
	image_url (	
destination_name (	
origin_name (	
flight_price (	
flight_sale_price (	
formatted_price	 (	
formatted_sale_price
 (	
android_app_link (	
ios_app_link (	
ios_app_store_id
 (
similar_destination_ids (	
custom_mapping (	"�
DiscoveryCarouselCardAsset
marketing_image_asset (	$
square_marketing_image_asset (	&
portrait_marketing_image_asset (	
headline (	B�A
call_to_action_text (	"�
DynamicTravelAsset
destination_id (	B�A
	origin_id (	
title (	B�A
destination_name (	
destination_address (	
origin_name (	
price (	

sale_price (	
formatted_price	 (	
formatted_sale_price
 (	
category (	
contextual_keywords (	
similar_destination_ids
 (	
	image_url (	
android_app_link (	
ios_app_link (	
ios_app_store_id ("�
DynamicLocalAsset
deal_id (	B�A
	deal_name (	B�A
subtitle (	
description (	
price (	

sale_price (	
	image_url (	
address (	
category	 (	
contextual_keywords
 (	
formatted_price (	
formatted_sale_price (	
android_app_link
 (	
similar_deal_ids (	
ios_app_link (	
ios_app_store_id ("�
DynamicJobsAsset
job_id (	B�A
location_id (	
	job_title (	B�A
job_subtitle (	
description (	
	image_url (	
job_category (	
contextual_keywords (	
address	 (	
salary
 (	
android_app_link (	
similar_job_ids (	
ios_app_link
 (	
ios_app_store_id ("�

LocationAsset
place_id (	\\
business_profile_locations (28.google.ads.googleads.v16.common.BusinessProfileLocationp
location_ownership_type (2O.google.ads.googleads.v16.enums.LocationOwnershipTypeEnum.LocationOwnershipType"Q
BusinessProfileLocation
labels (	

store_code (	

listing_id ("Q
HotelPropertyAsset
place_id (	

hotel_address (	

hotel_name (	B�
#com.google.ads.googleads.v16.commonBAssetTypesProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v16/common;common�GAA�Google.Ads.GoogleAds.V16.Common�Google\\Ads\\GoogleAds\\V16\\Common�#Google::Ads::GoogleAds::V16::Commonbproto3
�#
.google/ads/googleads/v16/resources/asset.proto"google.ads.googleads.v16.resources6google/ads/googleads/v16/common/custom_parameter.proto,google/ads/googleads/v16/common/policy.proto5google/ads/googleads/v16/enums/asset_field_type.proto1google/ads/googleads/v16/enums/asset_source.proto/google/ads/googleads/v16/enums/asset_type.proto;google/ads/googleads/v16/enums/policy_approval_status.proto9google/ads/googleads/v16/enums/policy_review_status.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�
Asset=

resource_name (	B&�A�A 
googleads.googleapis.com/Asset
id (B�AH�
name (	H�J
type (27.google.ads.googleads.v16.enums.AssetTypeEnum.AssetTypeB�A

final_urls (	
final_mobile_urls (	"
tracking_url_template (	H�O
url_custom_parameters (20.google.ads.googleads.v16.common.CustomParameter
final_url_suffix (	H�P
source& (2;.google.ads.googleads.v16.enums.AssetSourceEnum.AssetSourceB�AS
policy_summary
 (26.google.ads.googleads.v16.resources.AssetPolicySummaryB�Ai
field_type_policy_summaries( (2?.google.ads.googleads.v16.resources.AssetFieldTypePolicySummaryB�AV
youtube_video_asset (22.google.ads.googleads.v16.common.YoutubeVideoAssetB�AH T
media_bundle_asset (21.google.ads.googleads.v16.common.MediaBundleAssetB�AH G
image_asset (2+.google.ads.googleads.v16.common.ImageAssetB�AH E

text_asset (2*.google.ads.googleads.v16.common.TextAssetB�AH I
lead_form_asset	 (2..google.ads.googleads.v16.common.LeadFormAssetH R
book_on_google_asset
 (22.google.ads.googleads.v16.common.BookOnGoogleAssetH J
promotion_asset (2/.google.ads.googleads.v16.common.PromotionAssetH F

callout_asset (2-.google.ads.googleads.v16.common.CalloutAssetH [
structured_snippet_asset (27.google.ads.googleads.v16.common.StructuredSnippetAssetH H
sitelink_asset (2..google.ads.googleads.v16.common.SitelinkAssetH I
page_feed_asset (2..google.ads.googleads.v16.common.PageFeedAssetH Y
dynamic_education_asset (26.google.ads.googleads.v16.common.DynamicEducationAssetH K
mobile_app_asset (2/.google.ads.googleads.v16.common.MobileAppAssetH Q
hotel_callout_asset (22.google.ads.googleads.v16.common.HotelCalloutAssetH @

call_asset (2*.google.ads.googleads.v16.common.CallAssetH B
price_asset (2+.google.ads.googleads.v16.common.PriceAssetH W
call_to_action_asset (22.google.ads.googleads.v16.common.CallToActionAssetB�AH \\
dynamic_real_estate_asset (27.google.ads.googleads.v16.common.DynamicRealEstateAssetH S
dynamic_custom_asset (23.google.ads.googleads.v16.common.DynamicCustomAssetH i
 dynamic_hotels_and_rentals_asset  (2=.google.ads.googleads.v16.common.DynamicHotelsAndRentalsAssetH U
dynamic_flights_asset! (24.google.ads.googleads.v16.common.DynamicFlightsAssetH i
discovery_carousel_card_asset" (2;.google.ads.googleads.v16.common.DiscoveryCarouselCardAssetB�AH S
dynamic_travel_asset# (23.google.ads.googleads.v16.common.DynamicTravelAssetH Q
dynamic_local_asset$ (22.google.ads.googleads.v16.common.DynamicLocalAssetH O
dynamic_jobs_asset% (21.google.ads.googleads.v16.common.DynamicJobsAssetH M
location_asset\' (2..google.ads.googleads.v16.common.LocationAssetB�AH X
hotel_property_asset) (23.google.ads.googleads.v16.common.HotelPropertyAssetB�AH :N�AK
googleads.googleapis.com/Asset)customers/{customer_id}/assets/{asset_id}B

asset_dataB
_idB
_nameB
_tracking_url_templateB
_final_url_suffix"�
AssetFieldTypePolicySummarye
asset_field_type (2A.google.ads.googleads.v16.enums.AssetFieldTypeEnum.AssetFieldTypeB�AH �[
asset_source (2;.google.ads.googleads.v16.enums.AssetSourceEnum.AssetSourceB�AH�]
policy_summary_info (26.google.ads.googleads.v16.resources.AssetPolicySummaryB�AH�B
_asset_field_typeB

_asset_sourceB
_policy_summary_info"�
AssetPolicySummaryT
policy_topic_entries (21.google.ads.googleads.v16.common.PolicyTopicEntryB�Ae

review_status (2I.google.ads.googleads.v16.enums.PolicyReviewStatusEnum.PolicyReviewStatusB�Ak
approval_status (2M.google.ads.googleads.v16.enums.PolicyApprovalStatusEnum.PolicyApprovalStatusB�AB�
&com.google.ads.googleads.v16.resourcesB
AssetProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources�GAA�"Google.Ads.GoogleAds.V16.Resources�"Google\\Ads\\GoogleAds\\V16\\Resources�&Google::Ads::GoogleAds::V16::Resourcesbproto3
�
5google/ads/googleads/v16/services/asset_service.proto!google.ads.googleads.v16.services.google/ads/googleads/v16/resources/asset.protogoogle/api/annotations.protogoogle/api/client.protogoogle/api/field_behavior.protogoogle/api/resource.proto google/protobuf/field_mask.protogoogle/rpc/status.proto"�
MutateAssetsRequest
customer_id (	B�AJ

operations (21.google.ads.googleads.v16.services.AssetOperationB�A
partial_failure (j
response_content_type (2K.google.ads.googleads.v16.enums.ResponseContentTypeEnum.ResponseContentType

validate_only ("�
AssetOperation/
update_mask (2.google.protobuf.FieldMask;
create (2).google.ads.googleads.v16.resources.AssetH ;
update (2).google.ads.googleads.v16.resources.AssetH B
	operation"�
MutateAssetsResponse1
partial_failure_error (2.google.rpc.StatusE
results (24.google.ads.googleads.v16.services.MutateAssetResult"�
MutateAssetResult:

resource_name (	B#�A 
googleads.googleapis.com/Asset8
asset (2).google.ads.googleads.v16.resources.Asset2�
AssetService�
MutateAssets6.google.ads.googleads.v16.services.MutateAssetsRequest7.google.ads.googleads.v16.services.MutateAssetsResponse"P�Acustomer_id,operations���1",/v16/customers/{customer_id=*}/assets:mutate:*E�Agoogleads.googleapis.com�A\'https://www.googleapis.com/auth/adwordsB�
%com.google.ads.googleads.v16.servicesBAssetServiceProtoPZIgoogle.golang.org/genproto/googleapis/ads/googleads/v16/services;services�GAA�!Google.Ads.GoogleAds.V16.Services�!Google\\Ads\\GoogleAds\\V16\\Services�%Google::Ads::GoogleAds::V16::Servicesbproto3'
        , true);
        static::$is_initialized = true;
    }
}

