<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/shopping_add_products_to_campaign_recommendation_enum.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class ShoppingAddProductsToCampaignRecommendationEnum
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
Zgoogle/ads/googleads/v17/enums/shopping_add_products_to_campaign_recommendation_enum.protogoogle.ads.googleads.v17.enums"�
/ShoppingAddProductsToCampaignRecommendationEnum"�
Reason
UNSPECIFIED 
UNKNOWN5
1MERCHANT_CENTER_ACCOUNT_HAS_NO_SUBMITTED_PRODUCTS=
9MERCHANT_CENTER_ACCOUNT_HAS_NO_SUBMITTED_PRODUCTS_IN_FEED-
)ADS_ACCOUNT_EXCLUDES_OFFERS_FROM_CAMPAIGN+
\'ALL_PRODUCTS_ARE_EXCLUDED_FROM_CAMPAIGNB�
"com.google.ads.googleads.v17.enumsB4ShoppingAddProductsToCampaignRecommendationEnumProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

