<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/resources/local_services_verification_artifact.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Resources;

class LocalServicesVerificationArtifact
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\FieldBehavior::initOnce();
        \GPBMetadata\Google\Api\Resource::initOnce();
        $pool->internalAddGeneratedFile(
            '
�
`google/ads/googleads/v17/enums/local_services_business_registration_check_rejection_reason.protogoogle.ads.googleads.v17.enums"�
9LocalServicesBusinessRegistrationCheckRejectionReasonEnum"�
5LocalServicesBusinessRegistrationCheckRejectionReason
UNSPECIFIED 
UNKNOWN
BUSINESS_NAME_MISMATCH
BUSINESS_DETAILS_MISMATCH
ID_NOT_FOUND
POOR_DOCUMENT_IMAGE_QUALITY
DOCUMENT_EXPIRED
DOCUMENT_INVALID
DOCUMENT_TYPE_MISMATCH
DOCUMENT_UNVERIFIABLE		
OTHER
B�
"com.google.ads.googleads.v17.enumsB:LocalServicesBusinessRegistrationCheckRejectionReasonProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
4google/ads/googleads/v17/common/local_services.protogoogle.ads.googleads.v17.common"K
LocalServicesDocumentReadOnly
document_url (	H �B

_document_urlB�
#com.google.ads.googleads.v17.commonBLocalServicesProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/common;common�GAA�Google.Ads.GoogleAds.V17.Common�Google\\Ads\\GoogleAds\\V17\\Common�#Google::Ads::GoogleAds::V17::Commonbproto3
�
Ngoogle/ads/googleads/v17/enums/local_services_business_registration_type.protogoogle.ads.googleads.v17.enums"�
)LocalServicesBusinessRegistrationTypeEnum"_
%LocalServicesBusinessRegistrationType
UNSPECIFIED 
UNKNOWN

NUMBER
DOCUMENTB�
"com.google.ads.googleads.v17.enumsB*LocalServicesBusinessRegistrationTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Pgoogle/ads/googleads/v17/enums/local_services_verification_artifact_status.protogoogle.ads.googleads.v17.enums"�
+LocalServicesVerificationArtifactStatusEnum"�
\'LocalServicesVerificationArtifactStatus
UNSPECIFIED 
UNKNOWN

PASSED

FAILED
PENDING

NO_SUBMISSION
	CANCELLEDB�
"com.google.ads.googleads.v17.enumsB,LocalServicesVerificationArtifactStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Ngoogle/ads/googleads/v17/enums/local_services_insurance_rejection_reason.protogoogle.ads.googleads.v17.enums"�
)LocalServicesInsuranceRejectionReasonEnum"�
%LocalServicesInsuranceRejectionReason
UNSPECIFIED 
UNKNOWN
BUSINESS_NAME_MISMATCH!
INSURANCE_AMOUNT_INSUFFICIENT
EXPIRED
NO_SIGNATURE
NO_POLICY_NUMBER#
NO_COMMERCIAL_GENERAL_LIABILITY
EDITABLE_FORMAT
CATEGORY_MISMATCH	
MISSING_EXPIRATION_DATE

POOR_QUALITY
POTENTIALLY_EDITED
WRONG_DOCUMENT_TYPE

	NON_FINAL	
OTHERB�
"com.google.ads.googleads.v17.enumsB*LocalServicesInsuranceRejectionReasonProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Lgoogle/ads/googleads/v17/enums/local_services_license_rejection_reason.protogoogle.ads.googleads.v17.enums"�
\'LocalServicesLicenseRejectionReasonEnum"�
#LocalServicesLicenseRejectionReason
UNSPECIFIED 
UNKNOWN
BUSINESS_NAME_MISMATCH
UNAUTHORIZED
EXPIRED
POOR_QUALITY
UNVERIFIABLE
WRONG_DOCUMENT_OR_ID	
OTHERB�
"com.google.ads.googleads.v17.enumsB(LocalServicesLicenseRejectionReasonProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Ngoogle/ads/googleads/v17/enums/local_services_verification_artifact_type.protogoogle.ads.googleads.v17.enums"�
)LocalServicesVerificationArtifactTypeEnum"�
%LocalServicesVerificationArtifactType
UNSPECIFIED 
UNKNOWN
BACKGROUND_CHECK
	INSURANCE
LICENSE
BUSINESS_REGISTRATION_CHECKB�
"com.google.ads.googleads.v17.enumsB*LocalServicesVerificationArtifactTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�!
Mgoogle/ads/googleads/v17/resources/local_services_verification_artifact.proto"google.ads.googleads.v17.resources`google/ads/googleads/v17/enums/local_services_business_registration_check_rejection_reason.protoNgoogle/ads/googleads/v17/enums/local_services_business_registration_type.protoNgoogle/ads/googleads/v17/enums/local_services_insurance_rejection_reason.protoLgoogle/ads/googleads/v17/enums/local_services_license_rejection_reason.protoPgoogle/ads/googleads/v17/enums/local_services_verification_artifact_status.protoNgoogle/ads/googleads/v17/enums/local_services_verification_artifact_type.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�
!LocalServicesVerificationArtifactY

resource_name (	BB�A�A<
:googleads.googleapis.com/LocalServicesVerificationArtifact
id (B�AH�
creation_date_time (	B�A�
status (2s.google.ads.googleads.v17.enums.LocalServicesVerificationArtifactStatusEnum.LocalServicesVerificationArtifactStatusB�A�

artifact_type (2o.google.ads.googleads.v17.enums.LocalServicesVerificationArtifactTypeEnum.LocalServicesVerificationArtifactTypeB�A~
&background_check_verification_artifact (2G.google.ads.googleads.v17.resources.BackgroundCheckVerificationArtifactB�AH q
insurance_verification_artifact (2A.google.ads.googleads.v17.resources.InsuranceVerificationArtifactB�AH m
license_verification_artifact (2?.google.ads.googleads.v17.resources.LicenseVerificationArtifactB�AH �
1business_registration_check_verification_artifact	 (2Q.google.ads.googleads.v17.resources.BusinessRegistrationCheckVerificationArtifactB�AH :��A�
:googleads.googleapis.com/LocalServicesVerificationArtifactYcustomers/{customer_id}/localServicesVerificationArtifacts/{gls_verification_artifact_id}B

artifact_dataB
_id"�
#BackgroundCheckVerificationArtifact
case_url (	B�AH �.
final_adjudication_date_time (	B�AH�B
	_case_urlB
_final_adjudication_date_time"�
InsuranceVerificationArtifact

amount_micros (B�AH ��
rejection_reason (2o.google.ads.googleads.v17.enums.LocalServicesInsuranceRejectionReasonEnum.LocalServicesInsuranceRejectionReasonB�AH�m
insurance_document_readonly (2>.google.ads.googleads.v17.common.LocalServicesDocumentReadOnlyB�AH�&
expiration_date_time (	B�AH�B
_amount_microsB
_rejection_reasonB
_insurance_document_readonlyB
_expiration_date_time"�
LicenseVerificationArtifact
license_type (	B�AH � 
license_number (	B�AH�%
licensee_first_name (	B�AH�$
licensee_last_name (	B�AH��
rejection_reason (2k.google.ads.googleads.v17.enums.LocalServicesLicenseRejectionReasonEnum.LocalServicesLicenseRejectionReasonB�AH�k
license_document_readonly (2>.google.ads.googleads.v17.common.LocalServicesDocumentReadOnlyB�AH�&
expiration_date_time (	B�AH�B

_license_typeB
_license_numberB
_licensee_first_nameB
_licensee_last_nameB
_rejection_reasonB
_license_document_readonlyB
_expiration_date_time"�
-BusinessRegistrationCheckVerificationArtifact�
registration_type (2o.google.ads.googleads.v17.enums.LocalServicesBusinessRegistrationTypeEnum.LocalServicesBusinessRegistrationTypeB�AH�
check_id (	B�AH��
rejection_reason (2�.google.ads.googleads.v17.enums.LocalServicesBusinessRegistrationCheckRejectionReasonEnum.LocalServicesBusinessRegistrationCheckRejectionReasonB�AH�b
registration_number (2>.google.ads.googleads.v17.resources.BusinessRegistrationNumberB�AH f
registration_document (<EMAIL>�AH B
business_registrationB
_registration_typeB
	_check_idB
_rejection_reason"A
BusinessRegistrationNumber
number (	B�AH �B	
_number"�
BusinessRegistrationDocumentc
document_readonly (2>.google.ads.googleads.v17.common.LocalServicesDocumentReadOnlyB�AH �B
_document_readonlyB�
&com.google.ads.googleads.v17.resourcesB&LocalServicesVerificationArtifactProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3'
        , true);
        static::$is_initialized = true;
    }
}

