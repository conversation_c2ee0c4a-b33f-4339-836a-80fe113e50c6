<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/media_type.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class MediaType
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
/google/ads/googleads/v17/enums/media_type.protogoogle.ads.googleads.v17.enums"�

MediaTypeEnum"y
	MediaType
UNSPECIFIED 
UNKNOWN	
IMAGE
ICON
MEDIA_BUNDLE	
AUDIO	
VIDEO

DYNAMIC_IMAGEB�
"com.google.ads.googleads.v17.enumsBMediaTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

