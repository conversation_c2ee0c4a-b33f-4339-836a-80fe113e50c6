<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v16/errors/partial_failure_error.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V16\Errors;

class PartialFailureError
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
;google/ads/googleads/v16/errors/partial_failure_error.protogoogle.ads.googleads.v16.errors"q
PartialFailureErrorEnum"V
PartialFailureError
UNSPECIFIED 
UNKNOWN!
PARTIAL_FAILURE_MODE_REQUIREDB�
#com.google.ads.googleads.v16.errorsBPartialFailureErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors�GAA�Google.Ads.GoogleAds.V16.Errors�Google\\Ads\\GoogleAds\\V16\\Errors�#Google::Ads::GoogleAds::V16::Errorsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

