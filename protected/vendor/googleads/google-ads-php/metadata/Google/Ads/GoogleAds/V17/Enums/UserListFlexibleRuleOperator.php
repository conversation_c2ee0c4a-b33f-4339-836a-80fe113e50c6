<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/user_list_flexible_rule_operator.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class UserListFlexibleRuleOperator
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
Egoogle/ads/googleads/v17/enums/user_list_flexible_rule_operator.protogoogle.ads.googleads.v17.enums"q
 UserListFlexibleRuleOperatorEnum"M
UserListFlexibleRuleOperator
UNSPECIFIED 
UNKNOWN
AND
ORB�
"com.google.ads.googleads.v17.enumsB!UserListFlexibleRuleOperatorProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

