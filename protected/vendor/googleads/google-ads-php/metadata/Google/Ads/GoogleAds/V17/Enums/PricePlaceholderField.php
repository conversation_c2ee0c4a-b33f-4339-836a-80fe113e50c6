<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/price_placeholder_field.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class PricePlaceholderField
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
<google/ads/googleads/v17/enums/price_placeholder_field.protogoogle.ads.googleads.v17.enums"�	
PricePlaceholderFieldEnum"�	
PricePlaceholderField
UNSPECIFIED 
UNKNOWN
TYPE
PRICE_QUALIFIER
TRACKING_TEMPLATE
LANGUAGE
FINAL_URL_SUFFIX

ITEM_1_HEADERd
ITEM_1_DESCRIPTIONe
ITEM_1_PRICEf
ITEM_1_UNITg
ITEM_1_FINAL_URLSh
ITEM_1_FINAL_MOBILE_URLSi

ITEM_2_HEADER�
ITEM_2_DESCRIPTION�
ITEM_2_PRICE�
ITEM_2_UNIT�
ITEM_2_FINAL_URLS�
ITEM_2_FINAL_MOBILE_URLS�

ITEM_3_HEADER�
ITEM_3_DESCRIPTION�
ITEM_3_PRICE�
ITEM_3_UNIT�
ITEM_3_FINAL_URLS�
ITEM_3_FINAL_MOBILE_URLS�

ITEM_4_HEADER�
ITEM_4_DESCRIPTION�
ITEM_4_PRICE�
ITEM_4_UNIT�
ITEM_4_FINAL_URLS�
ITEM_4_FINAL_MOBILE_URLS�

ITEM_5_HEADER�
ITEM_5_DESCRIPTION�
ITEM_5_PRICE�
ITEM_5_UNIT�
ITEM_5_FINAL_URLS�
ITEM_5_FINAL_MOBILE_URLS�

ITEM_6_HEADER�
ITEM_6_DESCRIPTION�
ITEM_6_PRICE�
ITEM_6_UNIT�
ITEM_6_FINAL_URLS�
ITEM_6_FINAL_MOBILE_URLS�

ITEM_7_HEADER�
ITEM_7_DESCRIPTION�
ITEM_7_PRICE�
ITEM_7_UNIT�
ITEM_7_FINAL_URLS�
ITEM_7_FINAL_MOBILE_URLS�

ITEM_8_HEADER�
ITEM_8_DESCRIPTION�
ITEM_8_PRICE�
ITEM_8_UNIT�
ITEM_8_FINAL_URLS�
ITEM_8_FINAL_MOBILE_URLS�B�
"com.google.ads.googleads.v17.enumsBPricePlaceholderFieldProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

