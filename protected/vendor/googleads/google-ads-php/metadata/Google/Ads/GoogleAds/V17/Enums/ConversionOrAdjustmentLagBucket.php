<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/conversion_or_adjustment_lag_bucket.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class ConversionOrAdjustmentLagBucket
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
Hgoogle/ads/googleads/v17/enums/conversion_or_adjustment_lag_bucket.protogoogle.ads.googleads.v17.enums"�
#ConversionOrAdjustmentLagBucketEnum"�
ConversionOrAdjustmentLagBucket
UNSPECIFIED 
UNKNOWN 
CONVERSION_LESS_THAN_ONE_DAY
CONVERSION_ONE_TO_TWO_DAYS 
CONVERSION_TWO_TO_THREE_DAYS!
CONVERSION_THREE_TO_FOUR_DAYS 
CONVERSION_FOUR_TO_FIVE_DAYS
CONVERSION_FIVE_TO_SIX_DAYS 
CONVERSION_SIX_TO_SEVEN_DAYS"
CONVERSION_SEVEN_TO_EIGHT_DAYS	!
CONVERSION_EIGHT_TO_NINE_DAYS

CONVERSION_NINE_TO_TEN_DAYS!
CONVERSION_TEN_TO_ELEVEN_DAYS$
 CONVERSION_ELEVEN_TO_TWELVE_DAYS
&
"CONVERSION_TWELVE_TO_THIRTEEN_DAYS(
$CONVERSION_THIRTEEN_TO_FOURTEEN_DAYS*
&CONVERSION_FOURTEEN_TO_TWENTY_ONE_DAYS(
$CONVERSION_TWENTY_ONE_TO_THIRTY_DAYS(
$CONVERSION_THIRTY_TO_FORTY_FIVE_DAYS\'
#CONVERSION_FORTY_FIVE_TO_SIXTY_DAYS#
CONVERSION_SIXTY_TO_NINETY_DAYS 
ADJUSTMENT_LESS_THAN_ONE_DAY
ADJUSTMENT_ONE_TO_TWO_DAYS 
ADJUSTMENT_TWO_TO_THREE_DAYS!
ADJUSTMENT_THREE_TO_FOUR_DAYS 
ADJUSTMENT_FOUR_TO_FIVE_DAYS
ADJUSTMENT_FIVE_TO_SIX_DAYS 
ADJUSTMENT_SIX_TO_SEVEN_DAYS"
ADJUSTMENT_SEVEN_TO_EIGHT_DAYS!
ADJUSTMENT_EIGHT_TO_NINE_DAYS
ADJUSTMENT_NINE_TO_TEN_DAYS!
ADJUSTMENT_TEN_TO_ELEVEN_DAYS$
 ADJUSTMENT_ELEVEN_TO_TWELVE_DAYS &
"ADJUSTMENT_TWELVE_TO_THIRTEEN_DAYS!(
$ADJUSTMENT_THIRTEEN_TO_FOURTEEN_DAYS"*
&ADJUSTMENT_FOURTEEN_TO_TWENTY_ONE_DAYS#(
$ADJUSTMENT_TWENTY_ONE_TO_THIRTY_DAYS$(
$ADJUSTMENT_THIRTY_TO_FORTY_FIVE_DAYS%\'
#ADJUSTMENT_FORTY_FIVE_TO_SIXTY_DAYS&#
ADJUSTMENT_SIXTY_TO_NINETY_DAYS\'8
4ADJUSTMENT_NINETY_TO_ONE_HUNDRED_AND_FORTY_FIVE_DAYS(
CONVERSION_UNKNOWN)
ADJUSTMENT_UNKNOWN*B�
"com.google.ads.googleads.v17.enumsB$ConversionOrAdjustmentLagBucketProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

