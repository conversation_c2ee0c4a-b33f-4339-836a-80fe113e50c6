<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/services/custom_conversion_goal_service.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Services;

class CustomConversionGoalService
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\LaunchStage::initOnce();
        \GPBMetadata\Google\Protobuf\Duration::initOnce();
        \GPBMetadata\Google\Api\Client::initOnce();
        \GPBMetadata\Google\Api\Http::initOnce();
        \GPBMetadata\Google\Protobuf\FieldMask::initOnce();
        \GPBMetadata\Google\Api\FieldBehavior::initOnce();
        \GPBMetadata\Google\Api\Annotations::initOnce();
        \GPBMetadata\Google\Api\Resource::initOnce();
        $pool->internalAddGeneratedFile(
            '
�
Bgoogle/ads/googleads/v17/enums/custom_conversion_goal_status.protogoogle.ads.googleads.v17.enums"v
CustomConversionGoalStatusEnum"T
CustomConversionGoalStatus
UNSPECIFIED 
UNKNOWN
ENABLED
REMOVEDB�
"com.google.ads.googleads.v17.enumsBCustomConversionGoalStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
?google/ads/googleads/v17/resources/custom_conversion_goal.proto"google.ads.googleads.v17.resourcesgoogle/api/field_behavior.protogoogle/api/resource.proto"�
CustomConversionGoalL

resource_name (	B5�A�A/
-googleads.googleapis.com/CustomConversionGoal
id (B�A
name (	J
conversion_actions (	B.�A+
)googleads.googleapis.com/ConversionActioni
status (2Y.google.ads.googleads.v17.enums.CustomConversionGoalStatusEnum.CustomConversionGoalStatus:k�Ah
-googleads.googleapis.com/CustomConversionGoal7customers/{customer_id}/customConversionGoals/{goal_id}B�
&com.google.ads.googleads.v17.resourcesBCustomConversionGoalProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3
�
:google/ads/googleads/v17/enums/response_content_type.protogoogle.ads.googleads.v17.enums"o
ResponseContentTypeEnum"T
ResponseContentType
UNSPECIFIED 
RESOURCE_NAME_ONLY
MUTABLE_RESOURCEB�
"com.google.ads.googleads.v17.enumsBResponseContentTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Fgoogle/ads/googleads/v17/services/custom_conversion_goal_service.proto!google.ads.googleads.v17.services?google/ads/googleads/v17/resources/custom_conversion_goal.protogoogle/api/annotations.protogoogle/api/client.protogoogle/api/field_behavior.protogoogle/api/resource.proto google/protobuf/field_mask.proto"�
"MutateCustomConversionGoalsRequest
customer_id (	B�AY

operations (<EMAIL>�A

validate_only (j
response_content_type (2K.google.ads.googleads.v17.enums.ResponseContentTypeEnum.ResponseContentType"�
CustomConversionGoalOperation/
update_mask (2.google.protobuf.FieldMaskJ
create (28.google.ads.googleads.v17.resources.CustomConversionGoalH J
update (28.google.ads.googleads.v17.resources.CustomConversionGoalH D
remove (	B2�A/
-googleads.googleapis.com/CustomConversionGoalH B
	operation"{
#MutateCustomConversionGoalsResponseT
results (2C.google.ads.googleads.v17.services.MutateCustomConversionGoalResult"�
 MutateCustomConversionGoalResultI

resource_name (	B2�A/
-googleads.googleapis.com/CustomConversionGoalX
custom_conversion_goal (28.google.ads.googleads.v17.resources.CustomConversionGoal2�
CustomConversionGoalService�
MutateCustomConversionGoalsE.google.ads.googleads.v17.services.MutateCustomConversionGoalsRequestF.google.ads.googleads.v17.services.MutateCustomConversionGoalsResponse"_�Acustomer_id,operations���@";/v17/customers/{customer_id=*}/customConversionGoals:mutate:*E�Agoogleads.googleapis.com�A\'https://www.googleapis.com/auth/adwordsB�
%com.google.ads.googleads.v17.servicesB CustomConversionGoalServiceProtoPZIgoogle.golang.org/genproto/googleapis/ads/googleads/v17/services;services�GAA�!Google.Ads.GoogleAds.V17.Services�!Google\\Ads\\GoogleAds\\V17\\Services�%Google::Ads::GoogleAds::V17::Servicesbproto3'
        , true);
        static::$is_initialized = true;
    }
}

