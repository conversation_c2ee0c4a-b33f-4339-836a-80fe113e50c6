<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/resources/product_link_invitation.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Resources;

class ProductLinkInvitation
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\FieldBehavior::initOnce();
        \GPBMetadata\Google\Api\Resource::initOnce();
        $pool->internalAddGeneratedFile(
            '
�
Cgoogle/ads/googleads/v17/enums/product_link_invitation_status.protogoogle.ads.googleads.v17.enums"�
ProductLinkInvitationStatusEnum"�
ProductLinkInvitationStatus
UNSPECIFIED 
UNKNOWN
ACCEPTED
	REQUESTED
PENDING_APPROVAL
REVOKED
REJECTED
EXPIREDB�
"com.google.ads.googleads.v17.enumsB ProductLinkInvitationStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
8google/ads/googleads/v17/enums/linked_product_type.protogoogle.ads.googleads.v17.enums"�
LinkedProductTypeEnum"�
LinkedProductType
UNSPECIFIED 
UNKNOWN
DATA_PARTNER

GOOGLE_ADS
HOTEL_CENTER
MERCHANT_CENTER
ADVERTISING_PARTNER	B�
"com.google.ads.googleads.v17.enumsBLinkedProductTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
@google/ads/googleads/v17/resources/product_link_invitation.proto"google.ads.googleads.v17.resourcesCgoogle/ads/googleads/v17/enums/product_link_invitation_status.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�
ProductLinkInvitationM

resource_name (	B6�A�A0
.googleads.googleapis.com/ProductLinkInvitation\'
product_link_invitation_id (B�Ap
status (2[.google.ads.googleads.v17.enums.ProductLinkInvitationStatusEnum.ProductLinkInvitationStatusB�AZ
type (2G.google.ads.googleads.v17.enums.LinkedProductTypeEnum.LinkedProductTypeB�Ad
hotel_center (2G.google.ads.googleads.v17.resources.HotelCenterLinkInvitationIdentifierB�AH j
merchant_center (2J.google.ads.googleads.v17.resources.MerchantCenterLinkInvitationIdentifierB�AH r
advertising_partner (2N.google.ads.googleads.v17.resources.AdvertisingPartnerLinkInvitationIdentifierB�AH :|�Ay
.googleads.googleapis.com/ProductLinkInvitationGcustomers/{customer_id}/productLinkInvitations/{customer_invitation_id}B
invited_account"C
#HotelCenterLinkInvitationIdentifier
hotel_center_id (B�A"I
&MerchantCenterLinkInvitationIdentifier
merchant_center_id (B�A"{
*AdvertisingPartnerLinkInvitationIdentifier@
customer (	B)�A�A#
!googleads.googleapis.com/CustomerH �B
	_customerB�
&com.google.ads.googleads.v17.resourcesBProductLinkInvitationProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3'
        , true);
        static::$is_initialized = true;
    }
}

