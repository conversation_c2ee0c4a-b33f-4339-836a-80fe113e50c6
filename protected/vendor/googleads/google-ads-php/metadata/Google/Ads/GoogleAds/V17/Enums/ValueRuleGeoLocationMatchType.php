<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/value_rule_geo_location_match_type.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class ValueRuleGeoLocationMatchType
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
Ggoogle/ads/googleads/v17/enums/value_rule_geo_location_match_type.protogoogle.ads.googleads.v17.enums"�
!ValueRuleGeoLocationMatchTypeEnum"`
ValueRuleGeoLocationMatchType
UNSPECIFIED 
UNKNOWN
ANY
LOCATION_OF_PRESENCEB�
"com.google.ads.googleads.v17.enumsB"ValueRuleGeoLocationMatchTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

