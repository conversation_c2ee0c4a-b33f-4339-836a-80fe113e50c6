<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/reach_plan_age_range.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class ReachPlanAgeRange
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
9google/ads/googleads/v17/enums/reach_plan_age_range.protogoogle.ads.googleads.v17.enums"�
ReachPlanAgeRangeEnum"�
ReachPlanAgeRange
UNSPECIFIED 
UNKNOWN
AGE_RANGE_18_24��
AGE_RANGE_18_34
AGE_RANGE_18_44
AGE_RANGE_18_49
AGE_RANGE_18_54
AGE_RANGE_18_64
AGE_RANGE_18_65_UP
AGE_RANGE_21_34
AGE_RANGE_25_34��
AGE_RANGE_25_44	
AGE_RANGE_25_49

AGE_RANGE_25_54
AGE_RANGE_25_64
AGE_RANGE_25_65_UP

AGE_RANGE_35_44��
AGE_RANGE_35_49
AGE_RANGE_35_54
AGE_RANGE_35_64
AGE_RANGE_35_65_UP
AGE_RANGE_45_54��
AGE_RANGE_45_64
AGE_RANGE_45_65_UP
AGE_RANGE_50_65_UP
AGE_RANGE_55_64��
AGE_RANGE_55_65_UP
AGE_RANGE_65_UP��B�
"com.google.ads.googleads.v17.enumsBReachPlanAgeRangeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

