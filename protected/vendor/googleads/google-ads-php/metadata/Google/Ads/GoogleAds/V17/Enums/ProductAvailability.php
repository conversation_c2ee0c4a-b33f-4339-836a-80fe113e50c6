<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/product_availability.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class ProductAvailability
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
9google/ads/googleads/v17/enums/product_availability.protogoogle.ads.googleads.v17.enums"|
ProductAvailabilityEnum"a
ProductAvailability
UNSPECIFIED 
UNKNOWN
IN_STOCK
OUT_OF_STOCK
PREORDERB�
"com.google.ads.googleads.v17.enumsBProductAvailabilityProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

