<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/local_services_lead_type.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class LocalServicesLeadType
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
=google/ads/googleads/v17/enums/local_services_lead_type.protogoogle.ads.googleads.v17.enums"o
LocalServicesLeadTypeEnum"R
LeadType
UNSPECIFIED 
UNKNOWN
MESSAGE

PHONE_CALL
BOOKINGB�
"com.google.ads.googleads.v17.enumsBLocalServicesLeadTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

