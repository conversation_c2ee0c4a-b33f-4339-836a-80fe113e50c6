<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/user_list_customer_type_category.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class UserListCustomerTypeCategory
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
Egoogle/ads/googleads/v17/enums/user_list_customer_type_category.protogoogle.ads.googleads.v17.enums"�
 UserListCustomerTypeCategoryEnum"�
UserListCustomerTypeCategory
UNSPECIFIED 
UNKNOWN

ALL_CUSTOMERS

PURCHASERS
HIGH_VALUE_CUSTOMERS
DISENGAGED_CUSTOMERS
QUALIFIED_LEADS
CONVERTED_LEADS
PAID_SUBSCRIBERS
LOYALTY_SIGN_UPS	
CART_ABANDONERS
B�
"com.google.ads.googleads.v17.enumsB!UserListCustomerTypeCategoryProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

