<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/keyword_plan_keyword_annotation.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class KeywordPlanKeywordAnnotation
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
Dgoogle/ads/googleads/v17/enums/keyword_plan_keyword_annotation.protogoogle.ads.googleads.v17.enums"u
 KeywordPlanKeywordAnnotationEnum"Q
KeywordPlanKeywordAnnotation
UNSPECIFIED 
UNKNOWN
KEYWORD_CONCEPTB�
"com.google.ads.googleads.v17.enumsB!KeywordPlanKeywordAnnotationProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

