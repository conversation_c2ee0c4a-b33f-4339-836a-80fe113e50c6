<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/product_issue_severity.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class ProductIssueSeverity
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
;google/ads/googleads/v17/enums/product_issue_severity.protogoogle.ads.googleads.v17.enums"h
ProductIssueSeverityEnum"L
ProductIssueSeverity
UNSPECIFIED 
UNKNOWN
WARNING	
ERRORB�
"com.google.ads.googleads.v17.enumsBProductIssueSeverityProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

