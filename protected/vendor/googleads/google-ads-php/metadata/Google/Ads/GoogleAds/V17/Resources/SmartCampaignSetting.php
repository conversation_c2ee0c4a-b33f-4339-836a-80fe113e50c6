<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/resources/smart_campaign_setting.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Resources;

class SmartCampaignSetting
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\FieldBehavior::initOnce();
        \GPBMetadata\Google\Api\Resource::initOnce();
        $pool->internalAddGeneratedFile(
            '
�	
?google/ads/googleads/v17/resources/smart_campaign_setting.proto"google.ads.googleads.v17.resourcesgoogle/api/resource.proto"�
SmartCampaignSettingL

resource_name (	B5�A�A/
-googleads.googleapis.com/SmartCampaignSetting;
campaign (	B)�A�A#
!googleads.googleapis.com/CampaignZ
phone_number (2D.google.ads.googleads.v17.resources.SmartCampaignSetting.PhoneNumber!
advertising_language_code (	
	final_url (	H �
%ad_optimized_business_profile_setting	 (2Z.google.ads.googleads.v17.resources.SmartCampaignSetting.AdOptimizedBusinessProfileSettingH 

business_name (	H#
business_profile_location
 (	He
PhoneNumber
phone_number (	H �
country_code (	H�B

_phone_numberB

_country_codeY
!AdOptimizedBusinessProfileSetting
include_lead_form (H �B
_include_lead_form:o�Al
-googleads.googleapis.com/SmartCampaignSetting;customers/{customer_id}/smartCampaignSettings/{campaign_id}B
landing_pageB
business_settingB�
&com.google.ads.googleads.v17.resourcesBSmartCampaignSettingProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3'
        , true);
        static::$is_initialized = true;
    }
}

