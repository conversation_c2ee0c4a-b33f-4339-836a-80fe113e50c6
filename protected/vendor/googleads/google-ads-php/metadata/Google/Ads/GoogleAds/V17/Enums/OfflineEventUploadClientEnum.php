<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/offline_event_upload_client_enum.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class OfflineEventUploadClientEnum
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
Egoogle/ads/googleads/v17/enums/offline_event_upload_client_enum.protogoogle.ads.googleads.v17.enums"�
OfflineEventUploadClientEnum"
OfflineEventUploadClient
UNSPECIFIED 
UNKNOWN
GOOGLE_ADS_API
GOOGLE_ADS_WEB_CLIENT
ADS_DATA_CONNECTORB�
"com.google.ads.googleads.v17.enumsB!OfflineEventUploadClientEnumProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

