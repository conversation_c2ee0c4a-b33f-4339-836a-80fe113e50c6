<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/resources/change_event.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Resources;

class ChangeEvent
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Protobuf\FieldMask::initOnce();
        \GPBMetadata\Google\Api\Resource::initOnce();
        \GPBMetadata\Google\Api\FieldBehavior::initOnce();
        $pool->internalAddGeneratedFile(
            '
�
1google/ads/googleads/v17/enums/asset_source.protogoogle.ads.googleads.v17.enums"i
AssetSourceEnum"V
AssetSource
UNSPECIFIED 
UNKNOWN

ADVERTISER
AUTOMATICALLY_CREATEDB�
"com.google.ads.googleads.v17.enumsBAssetSourceProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
0google/ads/googleads/v17/enums/budget_type.protogoogle.ads.googleads.v17.enums"�
BudgetTypeEnum"o

BudgetType
UNSPECIFIED 
UNKNOWN
STANDARD
	FIXED_CPA
SMART_CAMPAIGN
LOCAL_SERVICESB�
"com.google.ads.googleads.v17.enumsBBudgetTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
:google/ads/googleads/v17/enums/asset_set_link_status.protogoogle.ads.googleads.v17.enums"f
AssetSetLinkStatusEnum"L
AssetSetLinkStatus
UNSPECIFIED 
UNKNOWN
ENABLED
REMOVEDB�
"com.google.ads.googleads.v17.enumsBAssetSetLinkStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
?google/ads/googleads/v17/enums/change_event_resource_type.protogoogle.ads.googleads.v17.enums"�
ChangeEventResourceTypeEnum"�
ChangeEventResourceType
UNSPECIFIED 
UNKNOWN
AD
AD_GROUP
AD_GROUP_CRITERION
CAMPAIGN
CAMPAIGN_BUDGET
AD_GROUP_BID_MODIFIER
CAMPAIGN_CRITERION
FEED	
	FEED_ITEM


CAMPAIGN_FEED

AD_GROUP_FEED
AD_GROUP_AD
	
ASSET
CUSTOMER_ASSET
CAMPAIGN_ASSET
AD_GROUP_ASSET
	ASSET_SET
ASSET_SET_ASSET
CAMPAIGN_ASSET_SETB�
"com.google.ads.googleads.v17.enumsBChangeEventResourceTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
4google/ads/googleads/v17/common/url_collection.protogoogle.ads.googleads.v17.common"�

UrlCollection
url_collection_id (	H �

final_urls (	
final_mobile_urls (	"
tracking_url_template (	H�B
_url_collection_idB
_tracking_url_templateB�
#com.google.ads.googleads.v17.commonBUrlCollectionProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/common;common�GAA�Google.Ads.GoogleAds.V17.Common�Google\\Ads\\GoogleAds\\V17\\Common�#Google::Ads::GoogleAds::V17::Commonbproto3
�
agoogle/ads/googleads/v17/enums/policy_topic_evidence_destination_not_working_dns_error_type.protogoogle.ads.googleads.v17.enums"�
8PolicyTopicEvidenceDestinationNotWorkingDnsErrorTypeEnum"�
4PolicyTopicEvidenceDestinationNotWorkingDnsErrorType
UNSPECIFIED 
UNKNOWN
HOSTNAME_NOT_FOUND
GOOGLE_CRAWLER_DNS_ISSUEB�
"com.google.ads.googleads.v17.enumsB9PolicyTopicEvidenceDestinationNotWorkingDnsErrorTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
>google/ads/googleads/v17/enums/campaign_criterion_status.protogoogle.ads.googleads.v17.enums"|
CampaignCriterionStatusEnum"]
CampaignCriterionStatus
UNSPECIFIED 
UNKNOWN
ENABLED

PAUSED
REMOVEDB�
"com.google.ads.googleads.v17.enumsBCampaignCriterionStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
5google/ads/googleads/v17/enums/interaction_type.protogoogle.ads.googleads.v17.enums"R
InteractionTypeEnum";
InteractionType
UNSPECIFIED 
UNKNOWN

CALLS�>B�
"com.google.ads.googleads.v17.enumsBInteractionTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
2google/ads/googleads/v17/enums/ad_group_type.protogoogle.ads.googleads.v17.enums"�
AdGroupTypeEnum"�
AdGroupType
UNSPECIFIED 
UNKNOWN
SEARCH_STANDARD
DISPLAY_STANDARD
SHOPPING_PRODUCT_ADS
	HOTEL_ADS
SHOPPING_SMART_ADS
VIDEO_BUMPER
VIDEO_TRUE_VIEW_IN_STREAM	
VIDEO_TRUE_VIEW_IN_DISPLAY
!
VIDEO_NON_SKIPPABLE_IN_STREAM
VIDEO_OUTSTREAM
SEARCH_DYNAMIC_ADS
#
SHOPPING_COMPARISON_LISTING_ADS
PROMOTED_HOTEL_ADS
VIDEO_RESPONSIVE
VIDEO_EFFICIENT_REACH
SMART_CAMPAIGN_ADS

TRAVEL_ADSB�
"com.google.ads.googleads.v17.enumsBAdGroupTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
0google/ads/googleads/v17/enums/ad_strength.protogoogle.ads.googleads.v17.enums"�
AdStrengthEnum"s

AdStrength
UNSPECIFIED 
UNKNOWN
PENDING

NO_ADS
POOR
AVERAGE
GOOD
	EXCELLENTB�
"com.google.ads.googleads.v17.enumsBAdStrengthProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
0google/ads/googleads/v17/enums/day_of_week.protogoogle.ads.googleads.v17.enums"�

DayOfWeekEnum"�
	DayOfWeek
UNSPECIFIED 
UNKNOWN

MONDAY
TUESDAY
	WEDNESDAY
THURSDAY

FRIDAY
SATURDAY

SUNDAYB�
"com.google.ads.googleads.v17.enumsBDayOfWeekProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
;google/ads/googleads/v17/enums/app_campaign_app_store.protogoogle.ads.googleads.v17.enums"y
AppCampaignAppStoreEnum"^
AppCampaignAppStore
UNSPECIFIED 
UNKNOWN
APPLE_APP_STORE
GOOGLE_APP_STOREB�
"com.google.ads.googleads.v17.enumsBAppCampaignAppStoreProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
>google/ads/googleads/v17/enums/geo_targeting_restriction.protogoogle.ads.googleads.v17.enums"p
GeoTargetingRestrictionEnum"Q
GeoTargetingRestriction
UNSPECIFIED 
UNKNOWN
LOCATION_OF_PRESENCEB�
"com.google.ads.googleads.v17.enumsBGeoTargetingRestrictionProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
=google/ads/googleads/v17/enums/lead_form_desired_intent.protogoogle.ads.googleads.v17.enums"s
LeadFormDesiredIntentEnum"V
LeadFormDesiredIntent
UNSPECIFIED 
UNKNOWN

LOW_INTENT
HIGH_INTENTB�
"com.google.ads.googleads.v17.enumsBLeadFormDesiredIntentProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Cgoogle/ads/googleads/v17/enums/ad_group_primary_status_reason.protogoogle.ads.googleads.v17.enums"�
AdGroupPrimaryStatusReasonEnum"�
AdGroupPrimaryStatusReason
UNSPECIFIED 
UNKNOWN
CAMPAIGN_REMOVED
CAMPAIGN_PAUSED
CAMPAIGN_PENDING
CAMPAIGN_ENDED
AD_GROUP_PAUSED
AD_GROUP_REMOVED
AD_GROUP_INCOMPLETE
KEYWORDS_PAUSED	
NO_KEYWORDS

AD_GROUP_ADS_PAUSED
NO_AD_GROUP_ADS
HAS_ADS_DISAPPROVED

HAS_ADS_LIMITED_BY_POLICY
MOST_ADS_UNDER_REVIEW
CAMPAIGN_DRAFT\'
#AD_GROUP_PAUSED_DUE_TO_LOW_ACTIVITYB�
"com.google.ads.googleads.v17.enumsBAdGroupPrimaryStatusReasonProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
4google/ads/googleads/v17/enums/product_channel.protogoogle.ads.googleads.v17.enums"[
ProductChannelEnum"E
ProductChannel
UNSPECIFIED 
UNKNOWN

ONLINE	
LOCALB�
"com.google.ads.googleads.v17.enumsBProductChannelProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
<google/ads/googleads/v17/enums/location_ownership_type.protogoogle.ads.googleads.v17.enums"u
LocationOwnershipTypeEnum"X
LocationOwnershipType
UNSPECIFIED 
UNKNOWN
BUSINESS_OWNER
	AFFILIATEB�
"com.google.ads.googleads.v17.enumsBLocationOwnershipTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Ygoogle/ads/googleads/v17/enums/policy_topic_evidence_destination_not_working_device.protogoogle.ads.googleads.v17.enums"�
2PolicyTopicEvidenceDestinationNotWorkingDeviceEnum"q
.PolicyTopicEvidenceDestinationNotWorkingDevice
UNSPECIFIED 
UNKNOWN
DESKTOP
ANDROID
IOSB�
"com.google.ads.googleads.v17.enumsB3PolicyTopicEvidenceDestinationNotWorkingDeviceProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Kgoogle/ads/googleads/v17/enums/asset_offline_evaluation_error_reasons.protogoogle.ads.googleads.v17.enums"�
&AssetOfflineEvaluationErrorReasonsEnum"�
"AssetOfflineEvaluationErrorReasons
UNSPECIFIED 
UNKNOWN.
*PRICE_ASSET_DESCRIPTION_REPEATS_ROW_HEADER"
PRICE_ASSET_REPETITIVE_HEADERS3
/PRICE_ASSET_HEADER_INCOMPATIBLE_WITH_PRICE_TYPE9
5PRICE_ASSET_DESCRIPTION_INCOMPATIBLE_WITH_ITEM_HEADER/
+PRICE_ASSET_DESCRIPTION_HAS_PRICE_QUALIFIER$
 PRICE_ASSET_UNSUPPORTED_LANGUAGE
PRICE_ASSET_OTHER_ERRORB�
"com.google.ads.googleads.v17.enumsB\'AssetOfflineEvaluationErrorReasonsProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
7google/ads/googleads/v17/enums/keyword_match_type.protogoogle.ads.googleads.v17.enums"j
KeywordMatchTypeEnum"R
KeywordMatchType
UNSPECIFIED 
UNKNOWN	
EXACT

PHRASE	
BROADB�
"com.google.ads.googleads.v17.enumsBKeywordMatchTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
,google/ads/googleads/v17/enums/ad_type.protogoogle.ads.googleads.v17.enums"�

AdTypeEnum"�
AdType
UNSPECIFIED 
UNKNOWN
TEXT_AD
EXPANDED_TEXT_AD
EXPANDED_DYNAMIC_SEARCH_AD
HOTEL_AD
SHOPPING_SMART_AD	
SHOPPING_PRODUCT_AD

VIDEO_AD
IMAGE_AD
RESPONSIVE_SEARCH_AD 
LEGACY_RESPONSIVE_DISPLAY_AD

APP_AD
LEGACY_APP_INSTALL_AD
RESPONSIVE_DISPLAY_AD
LOCAL_AD
HTML5_UPLOAD_AD
DYNAMIC_HTML5_AD
APP_ENGAGEMENT_AD"
SHOPPING_COMPARISON_LISTING_AD
VIDEO_BUMPER_AD$
 VIDEO_NON_SKIPPABLE_IN_STREAM_AD
VIDEO_OUTSTREAM_AD
VIDEO_TRUEVIEW_IN_STREAM_AD
VIDEO_RESPONSIVE_AD
SMART_CAMPAIGN_AD
CALL_AD 
APP_PRE_REGISTRATION_AD!
IN_FEED_VIDEO_AD"
DEMAND_GEN_MULTI_ASSET_AD(
DEMAND_GEN_CAROUSEL_AD)
	TRAVEL_AD%"
DEMAND_GEN_VIDEO_RESPONSIVE_AD*
DEMAND_GEN_PRODUCT_AD\'B�
"com.google.ads.googleads.v17.enumsBAdTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
3google/ads/googleads/v17/enums/asset_set_type.protogoogle.ads.googleads.v17.enums"�
AssetSetTypeEnum"�
AssetSetType
UNSPECIFIED 
UNKNOWN
	PAGE_FEED
DYNAMIC_EDUCATION
MERCHANT_CENTER_FEED
DYNAMIC_REAL_ESTATE
DYNAMIC_CUSTOM
DYNAMIC_HOTELS_AND_RENTALS
DYNAMIC_FLIGHTS
DYNAMIC_TRAVEL	

DYNAMIC_LOCAL

DYNAMIC_JOBS

LOCATION_SYNC+
\'BUSINESS_PROFILE_DYNAMIC_LOCATION_GROUP
 
CHAIN_DYNAMIC_LOCATION_GROUP
STATIC_LOCATION_GROUP
HOTEL_PROPERTYB�
"com.google.ads.googleads.v17.enumsBAssetSetTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
;google/ads/googleads/v17/enums/budget_delivery_method.protogoogle.ads.googleads.v17.enums"o
BudgetDeliveryMethodEnum"S
BudgetDeliveryMethod
UNSPECIFIED 
UNKNOWN
STANDARD
ACCELERATEDB�
"com.google.ads.googleads.v17.enumsBBudgetDeliveryMethodProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
1google/ads/googleads/v17/enums/payment_mode.protogoogle.ads.googleads.v17.enums"�
PaymentModeEnum"n
PaymentMode
UNSPECIFIED 
UNKNOWN

CLICKS
CONVERSION_VALUE
CONVERSIONS

GUEST_STAYB�
"com.google.ads.googleads.v17.enumsBPaymentModeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
5google/ads/googleads/v17/enums/asset_set_status.protogoogle.ads.googleads.v17.enums"^
AssetSetStatusEnum"H
AssetSetStatus
UNSPECIFIED 
UNKNOWN
ENABLED
REMOVEDB�
"com.google.ads.googleads.v17.enumsBAssetSetStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Lgoogle/ads/googleads/v17/enums/app_campaign_bidding_strategy_goal_type.protogoogle.ads.googleads.v17.enums"�
&AppCampaignBiddingStrategyGoalTypeEnum"�
"AppCampaignBiddingStrategyGoalType
UNSPECIFIED 
UNKNOWN)
%OPTIMIZE_INSTALLS_TARGET_INSTALL_COST3
/OPTIMIZE_IN_APP_CONVERSIONS_TARGET_INSTALL_COST6
2OPTIMIZE_IN_APP_CONVERSIONS_TARGET_CONVERSION_COST(
$OPTIMIZE_RETURN_ON_ADVERTISING_SPEND/
+OPTIMIZE_PRE_REGISTRATION_CONVERSION_VOLUME1
-OPTIMIZE_INSTALLS_WITHOUT_TARGET_INSTALL_COSTB�
"com.google.ads.googleads.v17.enumsB\'AppCampaignBiddingStrategyGoalTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
>google/ads/googleads/v17/enums/ad_group_criterion_status.protogoogle.ads.googleads.v17.enums"z
AdGroupCriterionStatusEnum"\\
AdGroupCriterionStatus
UNSPECIFIED 
UNKNOWN
ENABLED

PAUSED
REMOVEDB�
"com.google.ads.googleads.v17.enumsBAdGroupCriterionStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Ngoogle/ads/googleads/v17/enums/lead_form_post_submit_call_to_action_type.protogoogle.ads.googleads.v17.enums"�
&LeadFormPostSubmitCallToActionTypeEnum"~
"LeadFormPostSubmitCallToActionType
UNSPECIFIED 
UNKNOWN

VISIT_SITE
DOWNLOAD

LEARN_MORE
SHOP_NOWB�
"com.google.ads.googleads.v17.enumsB\'LeadFormPostSubmitCallToActionTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
1google/ads/googleads/v17/enums/listing_type.protogoogle.ads.googleads.v17.enums"L
ListingTypeEnum"9
ListingType
UNSPECIFIED 
UNKNOWN
VEHICLESB�
"com.google.ads.googleads.v17.enumsBListingTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Fgoogle/ads/googleads/v17/enums/feed_item_quality_approval_status.protogoogle.ads.googleads.v17.enums"�
!FeedItemQualityApprovalStatusEnum"\\
FeedItemQualityApprovalStatus
UNSPECIFIED 
UNKNOWN
APPROVED
DISAPPROVEDB�
"com.google.ads.googleads.v17.enumsB"FeedItemQualityApprovalStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
7google/ads/googleads/v17/enums/listing_group_type.protogoogle.ads.googleads.v17.enums"c
ListingGroupTypeEnum"K
ListingGroupType
UNSPECIFIED 
UNKNOWN
SUBDIVISION
UNITB�
"com.google.ads.googleads.v17.enumsBListingGroupTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
8google/ads/googleads/v17/enums/feed_attribute_type.protogoogle.ads.googleads.v17.enums"�
FeedAttributeTypeEnum"�
FeedAttributeType
UNSPECIFIED 
UNKNOWN	
INT64

DOUBLE

STRING
BOOLEAN
URL
	DATE_TIME

INT64_LIST
DOUBLE_LIST	
STRING_LIST

BOOLEAN_LIST
URL_LIST
DATE_TIME_LIST
	
PRICEB�
"com.google.ads.googleads.v17.enumsBFeedAttributeTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
;google/ads/googleads/v17/enums/proximity_radius_units.protogoogle.ads.googleads.v17.enums"k
ProximityRadiusUnitsEnum"O
ProximityRadiusUnits
UNSPECIFIED 
UNKNOWN	
MILES

KILOMETERSB�
"com.google.ads.googleads.v17.enumsBProximityRadiusUnitsProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
?google/ads/googleads/v17/enums/ad_group_ad_primary_status.protogoogle.ads.googleads.v17.enums"�
AdGroupAdPrimaryStatusEnum"�
AdGroupAdPrimaryStatus
UNSPECIFIED 
UNKNOWN
ELIGIBLE

PAUSED
REMOVED
PENDING
LIMITED
NOT_ELIGIBLEB�
"com.google.ads.googleads.v17.enumsBAdGroupAdPrimaryStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
>google/ads/googleads/v17/enums/hotel_date_selection_type.protogoogle.ads.googleads.v17.enums"~
HotelDateSelectionTypeEnum"`
HotelDateSelectionType
UNSPECIFIED 
UNKNOWN
DEFAULT_SELECTION2

USER_SELECTED3B�
"com.google.ads.googleads.v17.enumsBHotelDateSelectionTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
=google/ads/googleads/v17/enums/campaign_experiment_type.protogoogle.ads.googleads.v17.enums"y
CampaignExperimentTypeEnum"[
CampaignExperimentType
UNSPECIFIED 
UNKNOWN
BASE	
DRAFT

EXPERIMENTB�
"com.google.ads.googleads.v17.enumsBCampaignExperimentTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
<google/ads/googleads/v17/enums/asset_automation_status.protogoogle.ads.googleads.v17.enums"o
AssetAutomationStatusEnum"R
AssetAutomationStatus
UNSPECIFIED 
UNKNOWN
OPTED_IN
	OPTED_OUTB�
"com.google.ads.googleads.v17.enumsBAssetAutomationStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Cgoogle/ads/googleads/v17/enums/performance_max_upgrade_status.protogoogle.ads.googleads.v17.enums"�
PerformanceMaxUpgradeStatusEnum"�
PerformanceMaxUpgradeStatus
UNSPECIFIED 
UNKNOWN
UPGRADE_IN_PROGRESS
UPGRADE_COMPLETE
UPGRADE_FAILED
UPGRADE_ELIGIBLEB�
"com.google.ads.googleads.v17.enumsB PerformanceMaxUpgradeStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
9google/ads/googleads/v17/enums/price_extension_type.protogoogle.ads.googleads.v17.enums"�
PriceExtensionTypeEnum"�
PriceExtensionType
UNSPECIFIED 
UNKNOWN

BRANDS

EVENTS
	LOCATIONS

NEIGHBORHOODS
PRODUCT_CATEGORIES

PRODUCT_TIERS
SERVICES
SERVICE_CATEGORIES	

SERVICE_TIERS
B�
"com.google.ads.googleads.v17.enumsBPriceExtensionTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
=google/ads/googleads/v17/enums/positive_geo_target_type.protogoogle.ads.googleads.v17.enums"�
PositiveGeoTargetTypeEnum"r
PositiveGeoTargetType
UNSPECIFIED 
UNKNOWN
PRESENCE_OR_INTEREST
SEARCH_INTEREST
PRESENCEB�
"com.google.ads.googleads.v17.enumsBPositiveGeoTargetTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
7google/ads/googleads/v17/enums/vanity_pharma_text.protogoogle.ads.googleads.v17.enums"�
VanityPharmaTextEnum"�
VanityPharmaText
UNSPECIFIED 
UNKNOWN%
!PRESCRIPTION_TREATMENT_WEBSITE_EN%
!PRESCRIPTION_TREATMENT_WEBSITE_ES"
PRESCRIPTION_DEVICE_WEBSITE_EN"
PRESCRIPTION_DEVICE_WEBSITE_ES
MEDICAL_DEVICE_WEBSITE_EN
MEDICAL_DEVICE_WEBSITE_ES%
!PREVENTATIVE_TREATMENT_WEBSITE_EN%
!PREVENTATIVE_TREATMENT_WEBSITE_ES	)
%PRESCRIPTION_CONTRACEPTION_WEBSITE_EN
)
%PRESCRIPTION_CONTRACEPTION_WEBSITE_ES#
PRESCRIPTION_VACCINE_WEBSITE_EN#
PRESCRIPTION_VACCINE_WEBSITE_ES
B�
"com.google.ads.googleads.v17.enumsBVanityPharmaTextProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
3google/ads/googleads/v17/enums/bidding_source.protogoogle.ads.googleads.v17.enums"�
BiddingSourceEnum"r

BiddingSource
UNSPECIFIED 
UNKNOWN
CAMPAIGN_BIDDING_STRATEGY
AD_GROUP
AD_GROUP_CRITERIONB�
"com.google.ads.googleads.v17.enumsBBiddingSourceProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
:google/ads/googleads/v17/enums/asset_automation_type.protogoogle.ads.googleads.v17.enums"i
AssetAutomationTypeEnum"N
AssetAutomationType
UNSPECIFIED 
UNKNOWN
TEXT_ASSET_AUTOMATIONB�
"com.google.ads.googleads.v17.enumsBAssetAutomationTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
0google/ads/googleads/v17/enums/feed_origin.protogoogle.ads.googleads.v17.enums"R
FeedOriginEnum"@

FeedOrigin
UNSPECIFIED 
UNKNOWN
USER

GOOGLEB�
"com.google.ads.googleads.v17.enumsBFeedOriginProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
5google/ads/googleads/v17/enums/feed_item_status.protogoogle.ads.googleads.v17.enums"^
FeedItemStatusEnum"H
FeedItemStatus
UNSPECIFIED 
UNKNOWN
ENABLED
REMOVEDB�
"com.google.ads.googleads.v17.enumsBFeedItemStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
=google/ads/googleads/v17/enums/advertising_channel_type.protogoogle.ads.googleads.v17.enums"�
AdvertisingChannelTypeEnum"�
AdvertisingChannelType
UNSPECIFIED 
UNKNOWN

SEARCH
DISPLAY
SHOPPING	
HOTEL	
VIDEO

MULTI_CHANNEL	
LOCAL	
SMART	
PERFORMANCE_MAX

LOCAL_SERVICES

TRAVEL


DEMAND_GENB�
"com.google.ads.googleads.v17.enumsBAdvertisingChannelTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
<google/ads/googleads/v17/enums/ad_group_primary_status.protogoogle.ads.googleads.v17.enums"�
AdGroupPrimaryStatusEnum"�
AdGroupPrimaryStatus
UNSPECIFIED 
UNKNOWN
ELIGIBLE

PAUSED
REMOVED
PENDING
NOT_ELIGIBLE
LIMITEDB�
"com.google.ads.googleads.v17.enumsBAdGroupPrimaryStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
5google/ads/googleads/v17/enums/asset_field_type.protogoogle.ads.googleads.v17.enums"�
AssetFieldTypeEnum"�
AssetFieldType
UNSPECIFIED 
UNKNOWN
HEADLINE
DESCRIPTION
MANDATORY_AD_TEXT
MARKETING_IMAGE
MEDIA_BUNDLE

YOUTUBE_VIDEO
BOOK_ON_GOOGLE
	LEAD_FORM	
	PROMOTION

CALLOUT
STRUCTURED_SNIPPET
SITELINK


MOBILE_APP

HOTEL_CALLOUT
CALL	
PRICE

LONG_HEADLINE

BUSINESS_NAME
SQUARE_MARKETING_IMAGE
PORTRAIT_MARKETING_IMAGE
LOGO
LANDSCAPE_LOGO	
VIDEO
CALL_TO_ACTION_SELECTION
AD_IMAGE

BUSINESS_LOGO
HOTEL_PROPERTY
DEMAND_GEN_CAROUSEL_CARDB�
"com.google.ads.googleads.v17.enumsBAssetFieldTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
;google/ads/googleads/v17/enums/product_category_level.protogoogle.ads.googleads.v17.enums"�
ProductCategoryLevelEnum"p
ProductCategoryLevel
UNSPECIFIED 
UNKNOWN

LEVEL1

LEVEL2

LEVEL3

LEVEL4

LEVEL5B�
"com.google.ads.googleads.v17.enumsBProductCategoryLevelProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Ggoogle/ads/googleads/v17/enums/ad_group_criterion_approval_status.protogoogle.ads.googleads.v17.enums"�
"AdGroupCriterionApprovalStatusEnum"�
AdGroupCriterionApprovalStatus
UNSPECIFIED 
UNKNOWN
APPROVED
DISAPPROVED
PENDING_REVIEW
UNDER_REVIEWB�
"com.google.ads.googleads.v17.enumsB#AdGroupCriterionApprovalStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
@google/ads/googleads/v17/enums/product_channel_exclusivity.protogoogle.ads.googleads.v17.enums"�
ProductChannelExclusivityEnum"`
ProductChannelExclusivity
UNSPECIFIED 
UNKNOWN
SINGLE_CHANNEL

MULTI_CHANNELB�
"com.google.ads.googleads.v17.enumsBProductChannelExclusivityProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
7google/ads/googleads/v17/enums/content_label_type.protogoogle.ads.googleads.v17.enums"�
ContentLabelTypeEnum"�
ContentLabelType
UNSPECIFIED 
UNKNOWN
SEXUALLY_SUGGESTIVE
BELOW_THE_FOLD

PARKED_DOMAIN
JUVENILE
	PROFANITY
TRAGEDY	
VIDEO	
VIDEO_RATING_DV_G

VIDEO_RATING_DV_PG
VIDEO_RATING_DV_T
VIDEO_RATING_DV_MA

VIDEO_NOT_YET_RATED
EMBEDDED_VIDEO
LIVE_STREAMING_VIDEO

SOCIAL_ISSUES*
&BRAND_SUITABILITY_CONTENT_FOR_FAMILIES$
 BRAND_SUITABILITY_GAMES_FIGHTING"
BRAND_SUITABILITY_GAMES_MATURE&
"BRAND_SUITABILITY_HEALTH_SENSITIVE0
,BRAND_SUITABILITY_HEALTH_SOURCE_UNDETERMINED!
BRAND_SUITABILITY_NEWS_RECENT$
 BRAND_SUITABILITY_NEWS_SENSITIVE.
*BRAND_SUITABILITY_NEWS_SOURCE_NOT_FEATURED
BRAND_SUITABILITY_POLITICS
BRAND_SUITABILITY_RELIGIONB�
"com.google.ads.googleads.v17.enumsBContentLabelTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
<google/ads/googleads/v17/enums/campaign_serving_status.protogoogle.ads.googleads.v17.enums"�
CampaignServingStatusEnum"s
CampaignServingStatus
UNSPECIFIED 
UNKNOWN
SERVING
NONE	
ENDED
PENDING
	SUSPENDEDB�
"com.google.ads.googleads.v17.enumsBCampaignServingStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
=google/ads/googleads/v17/enums/negative_geo_target_type.protogoogle.ads.googleads.v17.enums"z
NegativeGeoTargetTypeEnum"]
NegativeGeoTargetType
UNSPECIFIED 
UNKNOWN
PRESENCE_OR_INTEREST
PRESENCEB�
"com.google.ads.googleads.v17.enumsBNegativeGeoTargetTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
@google/ads/googleads/v17/enums/campaign_keyword_match_type.protogoogle.ads.googleads.v17.enums"c
CampaignKeywordMatchTypeEnum"C
CampaignKeywordMatchType
UNSPECIFIED 
UNKNOWN	
BROADB�
"com.google.ads.googleads.v17.enumsBCampaignKeywordMatchTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Cgoogle/ads/googleads/v17/enums/bidding_strategy_system_status.protogoogle.ads.googleads.v17.enums"�
BiddingStrategySystemStatusEnum"�
BiddingStrategySystemStatus
UNSPECIFIED 
UNKNOWN
ENABLED
LEARNING_NEW
LEARNING_SETTING_CHANGE
LEARNING_BUDGET_CHANGE
LEARNING_COMPOSITION_CHANGE#
LEARNING_CONVERSION_TYPE_CHANGE&
"LEARNING_CONVERSION_SETTING_CHANGE
LIMITED_BY_CPC_BID_CEILING	
LIMITED_BY_CPC_BID_FLOOR

LIMITED_BY_DATA
LIMITED_BY_BUDGET!
LIMITED_BY_LOW_PRIORITY_SPEND

LIMITED_BY_LOW_QUALITY
LIMITED_BY_INVENTORY"
MISCONFIGURED_ZERO_ELIGIBILITY"
MISCONFIGURED_CONVERSION_TYPES%
!MISCONFIGURED_CONVERSION_SETTINGS
MISCONFIGURED_SHARED_BUDGET
MISCONFIGURED_STRATEGY_TYPE

PAUSED
UNAVAILABLE
MULTIPLE_LEARNING
MULTIPLE_LIMITED
MULTIPLE_MISCONFIGURED
MULTIPLEB�
"com.google.ads.googleads.v17.enumsB BiddingStrategySystemStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Fgoogle/ads/googleads/v17/enums/ad_group_ad_primary_status_reason.protogoogle.ads.googleads.v17.enums"�
 AdGroupAdPrimaryStatusReasonEnum"�
AdGroupAdPrimaryStatusReason
UNSPECIFIED 
UNKNOWN
CAMPAIGN_REMOVED
CAMPAIGN_PAUSED
CAMPAIGN_PENDING
CAMPAIGN_ENDED
AD_GROUP_PAUSED
AD_GROUP_REMOVED
AD_GROUP_AD_PAUSED
AD_GROUP_AD_REMOVED	
AD_GROUP_AD_DISAPPROVED

AD_GROUP_AD_UNDER_REVIEW
AD_GROUP_AD_POOR_QUALITY
AD_GROUP_AD_NO_ADS
 
AD_GROUP_AD_APPROVED_LABELED%
!AD_GROUP_AD_AREA_OF_INTEREST_ONLY
AD_GROUP_AD_UNDER_APPEALB�
"com.google.ads.googleads.v17.enumsB!AdGroupAdPrimaryStatusReasonProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Cgoogle/ads/googleads/v17/enums/vanity_pharma_display_url_mode.protogoogle.ads.googleads.v17.enums"�
VanityPharmaDisplayUrlModeEnum"q
VanityPharmaDisplayUrlMode
UNSPECIFIED 
UNKNOWN
MANUFACTURER_WEBSITE_URL
WEBSITE_DESCRIPTIONB�
"com.google.ads.googleads.v17.enumsBVanityPharmaDisplayUrlModeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Agoogle/ads/googleads/v17/enums/promotion_extension_occasion.protogoogle.ads.googleads.v17.enums"�
PromotionExtensionOccasionEnum"�
PromotionExtensionOccasion
UNSPECIFIED 
UNKNOWN
	NEW_YEARS
CHINESE_NEW_YEAR
VALENTINES_DAY

EASTER
MOTHERS_DAY
FATHERS_DAY
	LABOR_DAY
BACK_TO_SCHOOL	
	HALLOWEEN

BLACK_FRIDAY
CYBER_MONDAY
	CHRISTMAS


BOXING_DAY
INDEPENDENCE_DAY
NATIONAL_DAY

END_OF_SEASON
WINTER_SALE
SUMMER_SALE
	FALL_SALE
SPRING_SALE
RAMADAN
EID_AL_FITR
EID_AL_ADHA
SINGLES_DAY

WOMENS_DAY
HOLI
PARENTS_DAY
ST_NICHOLAS_DAY
CARNIVAL
EPIPHANY

ROSH_HASHANAH 
PASSOVER!
HANUKKAH"

DIWALI#
NAVRATRI$
SONGKRAN%

YEAR_END_GIFT&B�
"com.google.ads.googleads.v17.enumsBPromotionExtensionOccasionProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Fgoogle/ads/googleads/v17/enums/ad_group_criterion_primary_status.protogoogle.ads.googleads.v17.enums"�
!AdGroupCriterionPrimaryStatusEnum"�
AdGroupCriterionPrimaryStatus
UNSPECIFIED 
UNKNOWN
ELIGIBLE

PAUSED
REMOVED
PENDING
NOT_ELIGIBLEB�
"com.google.ads.googleads.v17.enumsB"AdGroupCriterionPrimaryStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
6google/ads/googleads/v17/enums/asset_link_status.protogoogle.ads.googleads.v17.enums"l
AssetLinkStatusEnum"U
AssetLinkStatus
UNSPECIFIED 
UNKNOWN
ENABLED
REMOVED

PAUSEDB�
"com.google.ads.googleads.v17.enumsBAssetLinkStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
+google/ads/googleads/v17/enums/device.protogoogle.ads.googleads.v17.enums"v

DeviceEnum"h
Device
UNSPECIFIED 
UNKNOWN

MOBILE

TABLET
DESKTOP
CONNECTED_TV	
OTHERB�
"com.google.ads.googleads.v17.enumsBDeviceProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
3google/ads/googleads/v17/enums/age_range_type.protogoogle.ads.googleads.v17.enums"�
AgeRangeTypeEnum"�
AgeRangeType
UNSPECIFIED 
UNKNOWN
AGE_RANGE_18_24��
AGE_RANGE_25_34��
AGE_RANGE_35_44��
AGE_RANGE_45_54��
AGE_RANGE_55_64��
AGE_RANGE_65_UP��
AGE_RANGE_UNDETERMINED��B�
"com.google.ads.googleads.v17.enumsBAgeRangeTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
?google/ads/googleads/v17/enums/webpage_condition_operator.protogoogle.ads.googleads.v17.enums"r
WebpageConditionOperatorEnum"R
WebpageConditionOperator
UNSPECIFIED 
UNKNOWN

EQUALS
CONTAINSB�
"com.google.ads.googleads.v17.enumsBWebpageConditionOperatorProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Mgoogle/ads/googleads/v17/enums/ad_group_criterion_primary_status_reason.protogoogle.ads.googleads.v17.enums"�
\'AdGroupCriterionPrimaryStatusReasonEnum"�
#AdGroupCriterionPrimaryStatusReason
UNSPECIFIED 
UNKNOWN
CAMPAIGN_PENDING
CAMPAIGN_CRITERION_NEGATIVE
CAMPAIGN_PAUSED
CAMPAIGN_REMOVED
CAMPAIGN_ENDED
AD_GROUP_PAUSED
AD_GROUP_REMOVED"
AD_GROUP_CRITERION_DISAPPROVED	$
 AD_GROUP_CRITERION_RARELY_SERVED
"
AD_GROUP_CRITERION_LOW_QUALITY#
AD_GROUP_CRITERION_UNDER_REVIEW%
!AD_GROUP_CRITERION_PENDING_REVIEW
+
\'AD_GROUP_CRITERION_BELOW_FIRST_PAGE_BID
AD_GROUP_CRITERION_NEGATIVE!
AD_GROUP_CRITERION_RESTRICTED
AD_GROUP_CRITERION_PAUSED1
-AD_GROUP_CRITERION_PAUSED_DUE_TO_LOW_ACTIVITY
AD_GROUP_CRITERION_REMOVEDB�
"com.google.ads.googleads.v17.enumsB(AdGroupCriterionPrimaryStatusReasonProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
@google/ads/googleads/v17/enums/display_upload_product_type.protogoogle.ads.googleads.v17.enums"�
DisplayUploadProductTypeEnum"�
DisplayUploadProductType
UNSPECIFIED 
UNKNOWN
HTML5_UPLOAD_AD
DYNAMIC_HTML5_EDUCATION_AD
DYNAMIC_HTML5_FLIGHT_AD!
DYNAMIC_HTML5_HOTEL_RENTAL_AD
DYNAMIC_HTML5_JOB_AD
DYNAMIC_HTML5_LOCAL_AD 
DYNAMIC_HTML5_REAL_ESTATE_AD
DYNAMIC_HTML5_CUSTOM_AD	
DYNAMIC_HTML5_TRAVEL_AD

DYNAMIC_HTML5_HOTEL_ADB�
"com.google.ads.googleads.v17.enumsBDisplayUploadProductTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
9google/ads/googleads/v17/enums/quality_score_bucket.protogoogle.ads.googleads.v17.enums"
QualityScoreBucketEnum"e
QualityScoreBucket
UNSPECIFIED 
UNKNOWN

BELOW_AVERAGE
AVERAGE

ABOVE_AVERAGEB�
"com.google.ads.googleads.v17.enumsBQualityScoreBucketProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Dgoogle/ads/googleads/v17/enums/call_conversion_reporting_state.protogoogle.ads.googleads.v17.enums"�
 CallConversionReportingStateEnum"�
CallConversionReportingState
UNSPECIFIED 
UNKNOWN
DISABLED,
(USE_ACCOUNT_LEVEL_CALL_CONVERSION_ACTION-
)USE_RESOURCE_LEVEL_CALL_CONVERSION_ACTIONB�
"com.google.ads.googleads.v17.enumsB!CallConversionReportingStateProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
<google/ads/googleads/v17/enums/policy_topic_entry_type.protogoogle.ads.googleads.v17.enums"�
PolicyTopicEntryTypeEnum"�
PolicyTopicEntryType
UNSPECIFIED 
UNKNOWN

PROHIBITED
LIMITED

FULLY_LIMITED
DESCRIPTIVE

BROADENING
AREA_OF_INTEREST_ONLYB�
"com.google.ads.googleads.v17.enumsBPolicyTopicEntryTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Dgoogle/ads/googleads/v17/enums/legacy_app_install_ad_app_store.protogoogle.ads.googleads.v17.enums"�
LegacyAppInstallAdAppStoreEnum"�
LegacyAppInstallAdAppStore
UNSPECIFIED 
UNKNOWN
APPLE_APP_STORE
GOOGLE_PLAY

WINDOWS_STORE
WINDOWS_PHONE_STORE
CN_APP_STOREB�
"com.google.ads.googleads.v17.enumsBLegacyAppInstallAdAppStoreProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Dgoogle/ads/googleads/v17/enums/lead_form_field_user_input_type.protogoogle.ads.googleads.v17.enums"�
LeadFormFieldUserInputTypeEnum"�
LeadFormFieldUserInputType
UNSPECIFIED 
UNKNOWN
	FULL_NAME	
EMAIL
PHONE_NUMBER
POSTAL_CODE
STREET_ADDRESS
CITY	

REGION

COUNTRY

WORK_EMAIL
COMPANY_NAME


WORK_PHONE
	JOB_TITLE
GOVERNMENT_ISSUED_ID_CPF_BR
GOVERNMENT_ISSUED_ID_DNI_AR
GOVERNMENT_ISSUED_ID_DNI_PE
GOVERNMENT_ISSUED_ID_RUT_CL
GOVERNMENT_ISSUED_ID_CC_CO
GOVERNMENT_ISSUED_ID_CI_EC
GOVERNMENT_ISSUED_ID_RFC_MX

FIRST_NAME
	LAST_NAME

VEHICLE_MODEL�
VEHICLE_TYPE�
PREFERRED_DEALERSHIP�
VEHICLE_PURCHASE_TIMELINE�
VEHICLE_OWNERSHIP�
VEHICLE_PAYMENT_TYPE�
VEHICLE_CONDITION�
COMPANY_SIZE�
ANNUAL_SALES�
YEARS_IN_BUSINESS�
JOB_DEPARTMENT�
JOB_ROLE�
OVER_18_AGE�
OVER_19_AGE�
OVER_20_AGE�
OVER_21_AGE�
OVER_22_AGE�
OVER_23_AGE�
OVER_24_AGE�
OVER_25_AGE�
OVER_26_AGE�
OVER_27_AGE�
OVER_28_AGE�
OVER_29_AGE�
OVER_30_AGE�
OVER_31_AGE�
OVER_32_AGE�
OVER_33_AGE�
OVER_34_AGE�
OVER_35_AGE�
OVER_36_AGE�
OVER_37_AGE�
OVER_38_AGE�
OVER_39_AGE�
OVER_40_AGE�
OVER_41_AGE�
OVER_42_AGE�
OVER_43_AGE�
OVER_44_AGE�
OVER_45_AGE�
OVER_46_AGE�
OVER_47_AGE�
OVER_48_AGE�
OVER_49_AGE�
OVER_50_AGE�
OVER_51_AGE�
OVER_52_AGE�
OVER_53_AGE�
OVER_54_AGE�
OVER_55_AGE�
OVER_56_AGE�
OVER_57_AGE�
OVER_58_AGE�
OVER_59_AGE�
OVER_60_AGE�
OVER_61_AGE�
OVER_62_AGE�
OVER_63_AGE�
OVER_64_AGE�
OVER_65_AGE�
EDUCATION_PROGRAM�
EDUCATION_COURSE�
PRODUCT�
SERVICE�

OFFER�
CATEGORY�
PREFERRED_CONTACT_METHOD�
PREFERRED_LOCATION�
PREFERRED_CONTACT_TIME�
PURCHASE_TIMELINE�
YEARS_OF_EXPERIENCE�
JOB_INDUSTRY�
LEVEL_OF_EDUCATION�

PROPERTY_TYPE�
REALTOR_HELP_GOAL�
PROPERTY_COMMUNITY�
PRICE_RANGE�
NUMBER_OF_BEDROOMS�
FURNISHED_PROPERTY�
PETS_ALLOWED_PROPERTY�
NEXT_PLANNED_PURCHASE�
EVENT_SIGNUP_INTEREST�
PREFERRED_SHOPPING_PLACES�
FAVORITE_BRAND�+
&TRANSPORTATION_COMMERCIAL_LICENSE_TYPE�
EVENT_BOOKING_INTEREST�
DESTINATION_COUNTRY�
DESTINATION_CITY�
DEPARTURE_COUNTRY�
DEPARTURE_CITY�
DEPARTURE_DATE�
RETURN_DATE�
NUMBER_OF_TRAVELERS�

TRAVEL_BUDGET�
TRAVEL_ACCOMMODATION�B�
"com.google.ads.googleads.v17.enumsBLeadFormFieldUserInputTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
;google/ads/googleads/v17/enums/optimization_goal_type.protogoogle.ads.googleads.v17.enums"�
OptimizationGoalTypeEnum"w
OptimizationGoalType
UNSPECIFIED 
UNKNOWN
CALL_CLICKS
DRIVING_DIRECTIONS
APP_PRE_REGISTRATIONB�
"com.google.ads.googleads.v17.enumsBOptimizationGoalTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
2google/ads/googleads/v17/enums/budget_status.protogoogle.ads.googleads.v17.enums"Z
BudgetStatusEnum"F
BudgetStatus
UNSPECIFIED 
UNKNOWN
ENABLED
REMOVEDB�
"com.google.ads.googleads.v17.enumsBBudgetStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
0google/ads/googleads/v17/enums/brand_state.protogoogle.ads.googleads.v17.enums"�
BrandStateEnum"�

BrandState
UNSPECIFIED 
UNKNOWN
ENABLED

DEPRECATED

UNVERIFIED
APPROVED
	CANCELLED
REJECTEDB�
"com.google.ads.googleads.v17.enumsBBrandStateProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
@google/ads/googleads/v17/enums/location_group_radius_units.protogoogle.ads.googleads.v17.enums"�
LocationGroupRadiusUnitsEnum"`
LocationGroupRadiusUnits
UNSPECIFIED 
UNKNOWN

METERS	
MILES
MILLI_MILESB�
"com.google.ads.googleads.v17.enumsBLocationGroupRadiusUnitsProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
6google/ads/googleads/v17/enums/product_condition.protogoogle.ads.googleads.v17.enums"l
ProductConditionEnum"T
ProductCondition
UNSPECIFIED 
UNKNOWN
NEW
REFURBISHED
USEDB�
"com.google.ads.googleads.v17.enumsBProductConditionProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
?google/ads/googleads/v17/enums/price_extension_price_unit.protogoogle.ads.googleads.v17.enums"�
PriceExtensionPriceUnitEnum"�
PriceExtensionPriceUnit
UNSPECIFIED 
UNKNOWN
PER_HOUR
PER_DAY
PER_WEEK
	PER_MONTH
PER_YEAR
	PER_NIGHTB�
"com.google.ads.googleads.v17.enumsBPriceExtensionPriceUnitProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
3google/ads/googleads/v17/enums/minute_of_hour.protogoogle.ads.googleads.v17.enums"s
MinuteOfHourEnum"_
MinuteOfHour
UNSPECIFIED 
UNKNOWN
ZERO
FIFTEEN

THIRTY

FORTY_FIVEB�
"com.google.ads.googleads.v17.enumsBMinuteOfHourProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Dgoogle/ads/googleads/v17/enums/criterion_system_serving_status.protogoogle.ads.googleads.v17.enums"�
 CriterionSystemServingStatusEnum"]
CriterionSystemServingStatus
UNSPECIFIED 
UNKNOWN
ELIGIBLE

RARELY_SERVEDB�
"com.google.ads.googleads.v17.enumsB!CriterionSystemServingStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
9google/ads/googleads/v17/enums/policy_review_status.protogoogle.ads.googleads.v17.enums"�
PolicyReviewStatusEnum"�
PolicyReviewStatus
UNSPECIFIED 
UNKNOWN
REVIEW_IN_PROGRESS
REVIEWED
UNDER_APPEAL
ELIGIBLE_MAY_SERVEB�
"com.google.ads.googleads.v17.enumsBPolicyReviewStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Cgoogle/ads/googleads/v17/enums/brand_request_rejection_reason.protogoogle.ads.googleads.v17.enums"�
BrandRequestRejectionReasonEnum"�
BrandRequestRejectionReason
UNSPECIFIED 
UNKNOWN
EXISTING_BRAND
EXISTING_BRAND_VARIANT
INCORRECT_INFORMATION
NOT_A_BRANDB�
"com.google.ads.googleads.v17.enumsB BrandRequestRejectionReasonProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
@google/ads/googleads/v17/enums/feed_item_validation_status.protogoogle.ads.googleads.v17.enums"}
FeedItemValidationStatusEnum"]
FeedItemValidationStatus
UNSPECIFIED 
UNKNOWN
PENDING
INVALID	
VALIDB�
"com.google.ads.googleads.v17.enumsBFeedItemValidationStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Agoogle/ads/googleads/v17/enums/system_managed_entity_source.protogoogle.ads.googleads.v17.enums"q
SystemManagedResourceSourceEnum"N
SystemManagedResourceSource
UNSPECIFIED 
UNKNOWN

AD_VARIATIONSB�
"com.google.ads.googleads.v17.enumsBSystemManagedEntitySourceProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Cgoogle/ads/googleads/v17/enums/product_custom_attribute_index.protogoogle.ads.googleads.v17.enums"�
ProductCustomAttributeIndexEnum"w
ProductCustomAttributeIndex
UNSPECIFIED 
UNKNOWN

INDEX0

INDEX1

INDEX2	

INDEX3


INDEX4B�
"com.google.ads.googleads.v17.enumsB ProductCustomAttributeIndexProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Cgoogle/ads/googleads/v17/enums/matching_function_context_type.protogoogle.ads.googleads.v17.enums"�
MatchingFunctionContextTypeEnum"t
MatchingFunctionContextType
UNSPECIFIED 
UNKNOWN
FEED_ITEM_ID
DEVICE_NAME
FEED_ITEM_SET_IDB�
"com.google.ads.googleads.v17.enumsB MatchingFunctionContextTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
8google/ads/googleads/v17/enums/call_to_action_type.protogoogle.ads.googleads.v17.enums"�
CallToActionTypeEnum"�
CallToActionType
UNSPECIFIED 
UNKNOWN

LEARN_MORE
	GET_QUOTE
	APPLY_NOW
SIGN_UP

CONTACT_US
	SUBSCRIBE
DOWNLOAD
BOOK_NOW	
SHOP_NOW

BUY_NOW

DONATE_NOW
	ORDER_NOW

PLAY_NOW
SEE_MORE
	START_NOW

VISIT_SITE
	WATCH_NOWB�
"com.google.ads.googleads.v17.enumsBCallToActionTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
<google/ads/googleads/v17/enums/campaign_primary_status.protogoogle.ads.googleads.v17.enums"�
CampaignPrimaryStatusEnum"�
CampaignPrimaryStatus
UNSPECIFIED 
UNKNOWN
ELIGIBLE

PAUSED
REMOVED	
ENDED
PENDING

MISCONFIGURED
LIMITED
LEARNING	
NOT_ELIGIBLE
B�
"com.google.ads.googleads.v17.enumsBCampaignPrimaryStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
0google/ads/googleads/v17/enums/feed_status.protogoogle.ads.googleads.v17.enums"V
FeedStatusEnum"D

FeedStatus
UNSPECIFIED 
UNKNOWN
ENABLED
REMOVEDB�
"com.google.ads.googleads.v17.enumsBFeedStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
7google/ads/googleads/v17/enums/ad_group_ad_status.protogoogle.ads.googleads.v17.enums"l
AdGroupAdStatusEnum"U
AdGroupAdStatus
UNSPECIFIED 
UNKNOWN
ENABLED

PAUSED
REMOVEDB�
"com.google.ads.googleads.v17.enumsBAdGroupAdStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
2google/ads/googleads/v17/enums/budget_period.protogoogle.ads.googleads.v17.enums"^
BudgetPeriodEnum"J
BudgetPeriod
UNSPECIFIED 
UNKNOWN	
DAILY

CUSTOM_PERIODB�
"com.google.ads.googleads.v17.enumsBBudgetPeriodProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
;google/ads/googleads/v17/enums/app_payment_model_type.protogoogle.ads.googleads.v17.enums"X
AppPaymentModelTypeEnum"=
AppPaymentModelType
UNSPECIFIED 
UNKNOWN
PAIDB�
"com.google.ads.googleads.v17.enumsBAppPaymentModelTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
>google/ads/googleads/v17/enums/ad_group_ad_rotation_mode.protogoogle.ads.googleads.v17.enums"t
AdGroupAdRotationModeEnum"W
AdGroupAdRotationMode
UNSPECIFIED 
UNKNOWN
OPTIMIZE
ROTATE_FOREVERB�
"com.google.ads.googleads.v17.enumsBAdGroupAdRotationModeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
>google/ads/googleads/v17/enums/webpage_condition_operand.protogoogle.ads.googleads.v17.enums"�
WebpageConditionOperandEnum"�
WebpageConditionOperand
UNSPECIFIED 
UNKNOWN
URL
CATEGORY

PAGE_TITLE
PAGE_CONTENT
CUSTOM_LABELB�
"com.google.ads.googleads.v17.enumsBWebpageConditionOperandProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Ngoogle/ads/googleads/v17/enums/affiliate_location_feed_relationship_type.protogoogle.ads.googleads.v17.enums"�
)AffiliateLocationFeedRelationshipTypeEnum"[
%AffiliateLocationFeedRelationshipType
UNSPECIFIED 
UNKNOWN
GENERAL_RETAILERB�
"com.google.ads.googleads.v17.enumsB*AffiliateLocationFeedRelationshipTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
0google/ads/googleads/v17/enums/gender_type.protogoogle.ads.googleads.v17.enums"d
GenderTypeEnum"R

GenderType
UNSPECIFIED 
UNKNOWN
MALE


FEMALE
UNDETERMINEDB�
"com.google.ads.googleads.v17.enumsBGenderTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
=google/ads/googleads/v17/enums/frequency_cap_event_type.protogoogle.ads.googleads.v17.enums"r
FrequencyCapEventTypeEnum"U
FrequencyCapEventType
UNSPECIFIED 
UNKNOWN

IMPRESSION

VIDEO_VIEWB�
"com.google.ads.googleads.v17.enumsBFrequencyCapEventTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
<google/ads/googleads/v17/enums/asset_performance_label.protogoogle.ads.googleads.v17.enums"�
AssetPerformanceLabelEnum"m
AssetPerformanceLabel
UNSPECIFIED 
UNKNOWN
PENDING
LEARNING
LOW
GOOD
BESTB�
"com.google.ads.googleads.v17.enumsBAssetPerformanceLabelProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Bgoogle/ads/googleads/v17/enums/app_url_operating_system_type.protogoogle.ads.googleads.v17.enums"p
AppUrlOperatingSystemTypeEnum"O
AppUrlOperatingSystemType
UNSPECIFIED 
UNKNOWN
IOS
ANDROIDB�
"com.google.ads.googleads.v17.enumsBAppUrlOperatingSystemTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
3google/ads/googleads/v17/common/final_app_url.protogoogle.ads.googleads.v17.common"�
FinalAppUrlh
os_type (2W.google.ads.googleads.v17.enums.AppUrlOperatingSystemTypeEnum.AppUrlOperatingSystemType
url (	H �B
_urlB�
#com.google.ads.googleads.v17.commonBFinalAppUrlProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/common;common�GAA�Google.Ads.GoogleAds.V17.Common�Google\\Ads\\GoogleAds\\V17\\Common�#Google::Ads::GoogleAds::V17::Commonbproto3
�
1google/ads/googleads/v17/common/feed_common.protogoogle.ads.googleads.v17.common"c
Money

currency_code (	H �

amount_micros (H�B
_currency_codeB
_amount_microsB�
#com.google.ads.googleads.v17.commonBFeedCommonProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/common;common�GAA�Google.Ads.GoogleAds.V17.Common�Google\\Ads\\GoogleAds\\V17\\Common�#Google::Ads::GoogleAds::V17::Commonbproto3
�
6google/ads/googleads/v17/common/custom_parameter.protogoogle.ads.googleads.v17.common"I
CustomParameter
key (	H �
value (	H�B
_keyB
_valueB�
#com.google.ads.googleads.v17.commonBCustomParameterProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/common;common�GAA�Google.Ads.GoogleAds.V17.Common�Google\\Ads\\GoogleAds\\V17\\Common�#Google::Ads::GoogleAds::V17::Commonbproto3
�
Cgoogle/ads/googleads/v17/enums/ad_serving_optimization_status.protogoogle.ads.googleads.v17.enums"�
AdServingOptimizationStatusEnum"�
AdServingOptimizationStatus
UNSPECIFIED 
UNKNOWN
OPTIMIZE
CONVERSION_OPTIMIZE

ROTATE
ROTATE_INDEFINITELY
UNAVAILABLEB�
"com.google.ads.googleads.v17.enumsB AdServingOptimizationStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
:google/ads/googleads/v17/enums/bidding_strategy_type.protogoogle.ads.googleads.v17.enums"�
BiddingStrategyTypeEnum"�
BiddingStrategyType
UNSPECIFIED 
UNKNOWN

COMMISSION
ENHANCED_CPC
INVALID

MANUAL_CPA

MANUAL_CPC

MANUAL_CPM

MANUAL_CPV

MAXIMIZE_CONVERSIONS

MAXIMIZE_CONVERSION_VALUE
PAGE_ONE_PROMOTED
PERCENT_CPC

TARGET_CPA

TARGET_CPM
TARGET_IMPRESSION_SHARE
TARGET_OUTRANK_SHARE
TARGET_ROAS
TARGET_SPEND	B�
"com.google.ads.googleads.v17.enumsBBiddingStrategyTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Bgoogle/ads/googleads/v17/enums/lead_form_call_to_action_type.protogoogle.ads.googleads.v17.enums"�
LeadFormCallToActionTypeEnum"�
LeadFormCallToActionType
UNSPECIFIED 
UNKNOWN

LEARN_MORE
	GET_QUOTE
	APPLY_NOW
SIGN_UP

CONTACT_US
	SUBSCRIBE
DOWNLOAD
BOOK_NOW	
	GET_OFFER

REGISTER
GET_INFO
REQUEST_DEMO

JOIN_NOW
GET_STARTEDB�
"com.google.ads.googleads.v17.enumsBLeadFormCallToActionTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
@google/ads/googleads/v17/errors/feed_item_validation_error.protogoogle.ads.googleads.v17.errors"�
FeedItemValidationErrorEnum"�
FeedItemValidationError
UNSPECIFIED 
UNKNOWN
STRING_TOO_SHORT
STRING_TOO_LONG
VALUE_NOT_SPECIFIED(
$INVALID_DOMESTIC_PHONE_NUMBER_FORMAT
INVALID_PHONE_NUMBER*
&PHONE_NUMBER_NOT_SUPPORTED_FOR_COUNTRY#
PREMIUM_RATE_NUMBER_NOT_ALLOWED
DISALLOWED_NUMBER_TYPE	
VALUE_OUT_OF_RANGE
*
&CALLTRACKING_NOT_SUPPORTED_FOR_COUNTRY.
*CUSTOMER_NOT_IN_ALLOWLIST_FOR_CALLTRACKINGc
INVALID_COUNTRY_CODE

INVALID_APP_ID!
MISSING_ATTRIBUTES_FOR_FIELDS
INVALID_TYPE_ID
INVALID_EMAIL_ADDRESS
INVALID_HTTPS_URL
MISSING_DELIVERY_ADDRESS
START_DATE_AFTER_END_DATE 
MISSING_FEED_ITEM_START_TIME
MISSING_FEED_ITEM_END_TIME
MISSING_FEED_ITEM_ID#
VANITY_PHONE_NUMBER_NOT_ALLOWED$
 INVALID_REVIEW_EXTENSION_SNIPPET
INVALID_NUMBER_FORMAT
INVALID_DATE_FORMAT
INVALID_PRICE_FORMAT
UNKNOWN_PLACEHOLDER_FIELD.
*MISSING_ENHANCED_SITELINK_DESCRIPTION_LINE&
"REVIEW_EXTENSION_SOURCE_INELIGIBLE\'
#HYPHENS_IN_REVIEW_EXTENSION_SNIPPET -
)DOUBLE_QUOTES_IN_REVIEW_EXTENSION_SNIPPET!&
"QUOTES_IN_REVIEW_EXTENSION_SNIPPET"
INVALID_FORM_ENCODED_PARAMS#
INVALID_URL_PARAMETER_NAME$
NO_GEOCODING_RESULT%(
$SOURCE_NAME_IN_REVIEW_EXTENSION_TEXT&-
)CARRIER_SPECIFIC_SHORT_NUMBER_NOT_ALLOWED\' 
INVALID_PLACEHOLDER_FIELD_ID(
INVALID_URL_TAG)

LIST_TOO_LONG*"
INVALID_ATTRIBUTES_COMBINATION+
DUPLICATE_VALUES,%
!INVALID_CALL_CONVERSION_ACTION_ID-!
CANNOT_SET_WITHOUT_FINAL_URLS.$
 APP_ID_DOESNT_EXIST_IN_APP_STORE/
INVALID_FINAL_URL0
INVALID_TRACKING_URL1*
&INVALID_FINAL_URL_FOR_APP_DOWNLOAD_URL2
LIST_TOO_SHORT3
INVALID_USER_ACTION4
INVALID_TYPE_NAME5
INVALID_EVENT_CHANGE_STATUS6
INVALID_SNIPPETS_HEADER7
INVALID_ANDROID_APP_LINK8;
7NUMBER_TYPE_WITH_CALLTRACKING_NOT_SUPPORTED_FOR_COUNTRY9
RESERVED_KEYWORD_OTHER:
DUPLICATE_OPTION_LABELS;
DUPLICATE_OPTION_PREFILLS<
UNEQUAL_LIST_LENGTHS=
INCONSISTENT_CURRENCY_CODES>*
&PRICE_EXTENSION_HAS_DUPLICATED_HEADERS?.
*ITEM_HAS_DUPLICATED_HEADER_AND_DESCRIPTION@%
!PRICE_EXTENSION_HAS_TOO_FEW_ITEMSA
UNSUPPORTED_VALUEB
INVALID_FINAL_MOBILE_URLC%
!INVALID_KEYWORDLESS_AD_RULE_LABELD\'
#VALUE_TRACK_PARAMETER_NOT_SUPPORTEDE*
&UNSUPPORTED_VALUE_IN_SELECTED_LANGUAGEF
INVALID_IOS_APP_LINKG,
(MISSING_IOS_APP_LINK_OR_IOS_APP_STORE_IDH
PROMOTION_INVALID_TIMEI9
5PROMOTION_CANNOT_SET_PERCENT_OFF_AND_MONEY_AMOUNT_OFFJ>
:PROMOTION_CANNOT_SET_PROMOTION_CODE_AND_ORDERS_OVER_AMOUNTK%
!TOO_MANY_DECIMAL_PLACES_SPECIFIEDL
AD_CUSTOMIZERS_NOT_ALLOWEDM
INVALID_LANGUAGE_CODEN
UNSUPPORTED_LANGUAGEO
IF_FUNCTION_NOT_ALLOWEDP
INVALID_FINAL_URL_SUFFIXQ#
INVALID_TAG_IN_FINAL_URL_SUFFIXR#
INVALID_FINAL_URL_SUFFIX_FORMATS0
,CUSTOMER_CONSENT_FOR_CALL_RECORDING_REQUIREDT\'
#ONLY_ONE_DELIVERY_OPTION_IS_ALLOWEDU
NO_DELIVERY_OPTION_IS_SETV&
"INVALID_CONVERSION_REPORTING_STATEW
IMAGE_SIZE_WRONGX+
\'EMAIL_DELIVERY_NOT_AVAILABLE_IN_COUNTRYY\'
#AUTO_REPLY_NOT_AVAILABLE_IN_COUNTRYZ
INVALID_LATITUDE_VALUE[
INVALID_LONGITUDE_VALUE\\
TOO_MANY_LABELS]
INVALID_IMAGE_URL^
MISSING_LATITUDE_VALUE_
MISSING_LONGITUDE_VALUE`
ADDRESS_NOT_FOUNDa
ADDRESS_NOT_TARGETABLEb
INVALID_ASSET_IDd
INCOMPATIBLE_ASSET_TYPEe
IMAGE_ERROR_UNEXPECTED_SIZEf(
$IMAGE_ERROR_ASPECT_RATIO_NOT_ALLOWEDg
IMAGE_ERROR_FILE_TOO_LARGEh"
IMAGE_ERROR_FORMAT_NOT_ALLOWEDi$
 IMAGE_ERROR_CONSTRAINTS_VIOLATEDj
IMAGE_ERROR_SERVER_ERRORkB�
#com.google.ads.googleads.v17.errorsBFeedItemValidationErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
9google/ads/googleads/v17/enums/location_source_type.protogoogle.ads.googleads.v17.enums"s
LocationSourceTypeEnum"Y
LocationSourceType
UNSPECIFIED 
UNKNOWN
GOOGLE_MY_BUSINESS
	AFFILIATEB�
"com.google.ads.googleads.v17.enumsBLocationSourceTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
8google/ads/googleads/v17/enums/bid_modifier_source.protogoogle.ads.googleads.v17.enums"f
BidModifierSourceEnum"M
BidModifierSource
UNSPECIFIED 
UNKNOWN
CAMPAIGN
AD_GROUPB�
"com.google.ads.googleads.v17.enumsBBidModifierSourceProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
?google/ads/googleads/v17/enums/target_frequency_time_unit.protogoogle.ads.googleads.v17.enums"b
TargetFrequencyTimeUnitEnum"C
TargetFrequencyTimeUnit
UNSPECIFIED 
UNKNOWN

WEEKLYB�
"com.google.ads.googleads.v17.enumsBTargetFrequencyTimeUnitProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
8google/ads/googleads/v17/enums/targeting_dimension.protogoogle.ads.googleads.v17.enums"�
TargetingDimensionEnum"�
TargetingDimension
UNSPECIFIED 
UNKNOWN
KEYWORD
AUDIENCE	
TOPIC

GENDER
	AGE_RANGE
	PLACEMENT
PARENTAL_STATUS
INCOME_RANGE	B�
"com.google.ads.googleads.v17.enumsBTargetingDimensionProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
@google/ads/googleads/v17/enums/location_string_filter_type.protogoogle.ads.googleads.v17.enums"c
LocationStringFilterTypeEnum"C
LocationStringFilterType
UNSPECIFIED 
UNKNOWN	
EXACTB�
"com.google.ads.googleads.v17.enumsBLocationStringFilterTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
4google/ads/googleads/v17/enums/video_thumbnail.protogoogle.ads.googleads.v17.enums"�
VideoThumbnailEnum"x
VideoThumbnail
UNSPECIFIED 
UNKNOWN
DEFAULT_THUMBNAIL
THUMBNAIL_1
THUMBNAIL_2
THUMBNAIL_3B�
"com.google.ads.googleads.v17.enumsBVideoThumbnailProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
4google/ads/googleads/v17/enums/campaign_status.protogoogle.ads.googleads.v17.enums"j
CampaignStatusEnum"T
CampaignStatus
UNSPECIFIED 
UNKNOWN
ENABLED

PAUSED
REMOVEDB�
"com.google.ads.googleads.v17.enumsBCampaignStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Dgoogle/ads/googleads/v17/enums/price_extension_price_qualifier.protogoogle.ads.googleads.v17.enums"�
 PriceExtensionPriceQualifierEnum"^
PriceExtensionPriceQualifier
UNSPECIFIED 
UNKNOWN
FROM	
UP_TO
AVERAGEB�
"com.google.ads.googleads.v17.enumsB!PriceExtensionPriceQualifierProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Egoogle/ads/googleads/v17/enums/asset_link_primary_status_reason.protogoogle.ads.googleads.v17.enums"�
 AssetLinkPrimaryStatusReasonEnum"�
AssetLinkPrimaryStatusReason
UNSPECIFIED 
UNKNOWN
ASSET_LINK_PAUSED
ASSET_LINK_REMOVED
ASSET_DISAPPROVED
ASSET_UNDER_REVIEW
ASSET_APPROVED_LABELEDB�
"com.google.ads.googleads.v17.enumsB!AssetLinkPrimaryStatusReasonProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Xgoogle/ads/googleads/v17/enums/policy_topic_evidence_destination_mismatch_url_type.protogoogle.ads.googleads.v17.enums"�
1PolicyTopicEvidenceDestinationMismatchUrlTypeEnum"�
-PolicyTopicEvidenceDestinationMismatchUrlType
UNSPECIFIED 
UNKNOWN
DISPLAY_URL
	FINAL_URL
FINAL_MOBILE_URL
TRACKING_URL
MOBILE_TRACKING_URLB�
"com.google.ads.googleads.v17.enumsB2PolicyTopicEvidenceDestinationMismatchUrlTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
>google/ads/googleads/v17/enums/resource_change_operation.protogoogle.ads.googleads.v17.enums"z
ResourceChangeOperationEnum"[
ResourceChangeOperation
UNSPECIFIED 
UNKNOWN

CREATE

UPDATE

REMOVEB�
"com.google.ads.googleads.v17.enumsBResourceChangeOperationProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
;google/ads/googleads/v17/enums/policy_approval_status.protogoogle.ads.googleads.v17.enums"�
PolicyApprovalStatusEnum"�
PolicyApprovalStatus
UNSPECIFIED 
UNKNOWN
DISAPPROVED
APPROVED_LIMITED
APPROVED
AREA_OF_INTEREST_ONLYB�
"com.google.ads.googleads.v17.enumsBPolicyApprovalStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
?google/ads/googleads/v17/common/real_time_bidding_setting.protogoogle.ads.googleads.v17.common"8
RealTimeBiddingSetting
opt_in (H �B	
_opt_inB�
#com.google.ads.googleads.v17.commonBRealTimeBiddingSettingProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/common;common�GAA�Google.Ads.GoogleAds.V17.Common�Google\\Ads\\GoogleAds\\V17\\Common�#Google::Ads::GoogleAds::V17::Commonbproto3
�
Egoogle/ads/googleads/v17/enums/target_impression_share_location.protogoogle.ads.googleads.v17.enums"�
!TargetImpressionShareLocationEnum"~
TargetImpressionShareLocation
UNSPECIFIED 
UNKNOWN
ANYWHERE_ON_PAGE
TOP_OF_PAGE
ABSOLUTE_TOP_OF_PAGEB�
"com.google.ads.googleads.v17.enumsB"TargetImpressionShareLocationProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
-google/ads/googleads/v17/common/bidding.protogoogle.ads.googleads.v17.commonEgoogle/ads/googleads/v17/enums/target_impression_share_location.proto"L

Commission#
commission_rate_micros (H �B
_commission_rate_micros"
EnhancedCpc"
	ManualCpa"G
	ManualCpc!
enhanced_cpc_enabled (H �B
_enhanced_cpc_enabled"
	ManualCpm"
	ManualCpv"n
MaximizeConversions
cpc_bid_ceiling_micros (
cpc_bid_floor_micros (
target_cpa_micros ("l
MaximizeConversionValue
target_roas (
cpc_bid_ceiling_micros (
cpc_bid_floor_micros ("�
	TargetCpa
target_cpa_micros (H �#
cpc_bid_ceiling_micros (H�!
cpc_bid_floor_micros (H�B
_target_cpa_microsB
_cpc_bid_ceiling_microsB
_cpc_bid_floor_micros"s
	TargetCpm^
target_frequency_goal (2=.google.ads.googleads.v17.common.TargetCpmTargetFrequencyGoalH B
goal"�
TargetCpmTargetFrequencyGoal
target_count (f
	time_unit (2S.google.ads.googleads.v17.enums.TargetFrequencyTimeUnitEnum.TargetFrequencyTimeUnit"�
TargetImpressionShareq
location (2_.google.ads.googleads.v17.enums.TargetImpressionShareLocationEnum.TargetImpressionShareLocation%
location_fraction_micros (H �#
cpc_bid_ceiling_micros (H�B
_location_fraction_microsB
_cpc_bid_ceiling_micros"�

TargetRoas
target_roas (H �#
cpc_bid_ceiling_micros (H�!
cpc_bid_floor_micros (H�B
_target_roasB
_cpc_bid_ceiling_microsB
_cpc_bid_floor_micros"�
TargetSpend$
target_spend_micros (BH �#
cpc_bid_ceiling_micros (H�B
_target_spend_microsB
_cpc_bid_ceiling_micros"�

PercentCpc#
cpc_bid_ceiling_micros (H �!
enhanced_cpc_enabled (H�B
_cpc_bid_ceiling_microsB
_enhanced_cpc_enabledB�
#com.google.ads.googleads.v17.commonBBiddingProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/common;common�GAA�Google.Ads.GoogleAds.V17.Common�Google\\Ads\\GoogleAds\\V17\\Common�#Google::Ads::GoogleAds::V17::Commonbproto3
�
Agoogle/ads/googleads/v17/enums/advertising_channel_sub_type.protogoogle.ads.googleads.v17.enums"�
AdvertisingChannelSubTypeEnum"�
AdvertisingChannelSubType
UNSPECIFIED 
UNKNOWN
SEARCH_MOBILE_APP
DISPLAY_MOBILE_APP
SEARCH_EXPRESS
DISPLAY_EXPRESS
SHOPPING_SMART_ADS
DISPLAY_GMAIL_AD
DISPLAY_SMART_CAMPAIGN
VIDEO_OUTSTREAM	
VIDEO_ACTION

VIDEO_NON_SKIPPABLE
APP_CAMPAIGN
APP_CAMPAIGN_FOR_ENGAGEMENT

LOCAL_CAMPAIGN#
SHOPPING_COMPARISON_LISTING_ADS
SMART_CAMPAIGN
VIDEO_SEQUENCE%
!APP_CAMPAIGN_FOR_PRE_REGISTRATION 
VIDEO_REACH_TARGET_FREQUENCY
TRAVEL_ACTIVITIESB�
"com.google.ads.googleads.v17.enumsBAdvertisingChannelSubTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
7google/ads/googleads/v17/enums/change_client_type.protogoogle.ads.googleads.v17.enums"�
ChangeClientTypeEnum"�
ChangeClientType
UNSPECIFIED 
UNKNOWN
GOOGLE_ADS_WEB_CLIENT
GOOGLE_ADS_AUTOMATED_RULE
GOOGLE_ADS_SCRIPTS
GOOGLE_ADS_BULK_UPLOAD
GOOGLE_ADS_API
GOOGLE_ADS_EDITOR
GOOGLE_ADS_MOBILE_APP
GOOGLE_ADS_RECOMMENDATIONS	
SEARCH_ADS_360_SYNC

SEARCH_ADS_360_POST

INTERNAL_TOOL	
OTHER
+
\'GOOGLE_ADS_RECOMMENDATIONS_SUBSCRIPTIONB�
"com.google.ads.googleads.v17.enumsBChangeClientTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
5google/ads/googleads/v17/enums/feed_link_status.protogoogle.ads.googleads.v17.enums"^
FeedLinkStatusEnum"H
FeedLinkStatus
UNSPECIFIED 
UNKNOWN
ENABLED
REMOVEDB�
"com.google.ads.googleads.v17.enumsBFeedLinkStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
7google/ads/googleads/v17/enums/product_type_level.protogoogle.ads.googleads.v17.enums"�
ProductTypeLevelEnum"l
ProductTypeLevel
UNSPECIFIED 
UNKNOWN

LEVEL1

LEVEL2

LEVEL3	

LEVEL4


LEVEL5B�
"com.google.ads.googleads.v17.enumsBProductTypeLevelProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
<google/ads/googleads/v17/enums/chain_relationship_type.protogoogle.ads.googleads.v17.enums"{
ChainRelationshipTypeEnum"^
ChainRelationshipType
UNSPECIFIED 
UNKNOWN
AUTO_DEALERS
GENERAL_RETAILERSB�
"com.google.ads.googleads.v17.enumsBChainRelationshipTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
>google/ads/googleads/v17/enums/asset_link_primary_status.protogoogle.ads.googleads.v17.enums"�
AssetLinkPrimaryStatusEnum"�
AssetLinkPrimaryStatus
UNSPECIFIED 
UNKNOWN
ELIGIBLE

PAUSED
REMOVED
PENDING
LIMITED
NOT_ELIGIBLEB�
"com.google.ads.googleads.v17.enumsBAssetLinkPrimaryStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
8google/ads/googleads/v17/enums/frequency_cap_level.protogoogle.ads.googleads.v17.enums"w
FrequencyCapLevelEnum"^
FrequencyCapLevel
UNSPECIFIED 
UNKNOWN
AD_GROUP_AD
AD_GROUP
CAMPAIGNB�
"com.google.ads.googleads.v17.enumsBFrequencyCapLevelProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Cgoogle/ads/googleads/v17/enums/campaign_primary_status_reason.protogoogle.ads.googleads.v17.enums"�
CampaignPrimaryStatusReasonEnum"�
CampaignPrimaryStatusReason
UNSPECIFIED 
UNKNOWN
CAMPAIGN_REMOVED
CAMPAIGN_PAUSED
CAMPAIGN_PENDING
CAMPAIGN_ENDED
CAMPAIGN_DRAFT"
BIDDING_STRATEGY_MISCONFIGURED
BIDDING_STRATEGY_LIMITED
BIDDING_STRATEGY_LEARNING	 
BIDDING_STRATEGY_CONSTRAINED

BUDGET_CONSTRAINED
BUDGET_MISCONFIGURED
SEARCH_VOLUME_LIMITED

AD_GROUPS_PAUSED
NO_AD_GROUPS
KEYWORDS_PAUSED
NO_KEYWORDS
AD_GROUP_ADS_PAUSED
NO_AD_GROUP_ADS
HAS_ADS_LIMITED_BY_POLICY
HAS_ADS_DISAPPROVED
MOST_ADS_UNDER_REVIEW
MISSING_LEAD_FORM_EXTENSION
MISSING_CALL_EXTENSION$
 LEAD_FORM_EXTENSION_UNDER_REVIEW#
LEAD_FORM_EXTENSION_DISAPPROVED
CALL_EXTENSION_UNDER_REVIEW
CALL_EXTENSION_DISAPPROVED+
\'NO_MOBILE_APPLICATION_AD_GROUP_CRITERIA
CAMPAIGN_GROUP_PAUSED*
&CAMPAIGN_GROUP_ALL_GROUP_BUDGETS_ENDED
APP_NOT_RELEASED 
APP_PARTIALLY_RELEASED! 
HAS_ASSET_GROUPS_DISAPPROVED"&
"HAS_ASSET_GROUPS_LIMITED_BY_POLICY#"
MOST_ASSET_GROUPS_UNDER_REVIEW$
NO_ASSET_GROUPS%
ASSET_GROUPS_PAUSED&B�
"com.google.ads.googleads.v17.enumsB CampaignPrimaryStatusReasonProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
5google/ads/googleads/v17/enums/placeholder_type.protogoogle.ads.googleads.v17.enums"�
PlaceholderTypeEnum"�
PlaceholderType
UNSPECIFIED 
UNKNOWN
SITELINK
CALL
APP
LOCATION
AFFILIATE_LOCATION
CALLOUT
STRUCTURED_SNIPPET
MESSAGE		
PRICE

	PROMOTION

AD_CUSTOMIZER
DYNAMIC_EDUCATION

DYNAMIC_FLIGHT
DYNAMIC_CUSTOM

DYNAMIC_HOTEL
DYNAMIC_REAL_ESTATE
DYNAMIC_TRAVEL

DYNAMIC_LOCAL
DYNAMIC_JOB	
IMAGEB�
"com.google.ads.googleads.v17.enumsBPlaceholderTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
5google/ads/googleads/v17/common/asset_set_types.protogoogle.ads.googleads.v17.common<google/ads/googleads/v17/enums/location_ownership_type.proto@google/ads/googleads/v17/enums/location_string_filter_type.protogoogle/api/field_behavior.proto"�
LocationSetx
location_ownership_type (2O.google.ads.googleads.v17.enums.LocationOwnershipTypeEnum.LocationOwnershipTypeB�A�Ad
business_profile_location_set (2;.google.ads.googleads.v17.common.BusinessProfileLocationSetH G
chain_location_set (2).google.ads.googleads.v17.common.ChainSetH M
maps_location_set (20.google.ads.googleads.v17.common.MapsLocationSetH B
source"�
BusinessProfileLocationSet(
http_authorization_token (	B�A�A

email_address (	B�A�A
business_name_filter (	

label_filters (	
listing_id_filters ( 
business_account_id (	B�A"�
ChainSetr
relationship_type (2O.google.ads.googleads.v17.enums.ChainRelationshipTypeEnum.ChainRelationshipTypeB�A�AA
chains (2,.google.ads.googleads.v17.common.ChainFilterB�A"A
ChainFilter
chain_id (B�A
location_attributes (	"a
MapsLocationSetN
maps_locations (21.google.ads.googleads.v17.common.MapsLocationInfoB�A"$
MapsLocationInfo
place_id (	"�
BusinessProfileLocationGroup�
.dynamic_business_profile_location_group_filter (2J.google.ads.googleads.v17.common.DynamicBusinessProfileLocationGroupFilter"�
)DynamicBusinessProfileLocationGroupFilter

label_filters (	e
business_name_filter (2B.google.ads.googleads.v17.common.BusinessProfileBusinessNameFilterH �
listing_id_filters (B
_business_name_filter"�
!BusinessProfileBusinessNameFilter

business_name (	j
filter_type (2U.google.ads.googleads.v17.enums.LocationStringFilterTypeEnum.LocationStringFilterType"p
ChainLocationGroupZ
$dynamic_chain_location_group_filters (2,.google.ads.googleads.v17.common.ChainFilterB�
#com.google.ads.googleads.v17.commonBAssetSetTypesProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/common;common�GAA�Google.Ads.GoogleAds.V17.Common�Google\\Ads\\GoogleAds\\V17\\Common�#Google::Ads::GoogleAds::V17::Commonbproto3
�
2google/ads/googleads/v17/resources/asset_set.proto"google.ads.googleads.v17.resources5google/ads/googleads/v17/enums/asset_set_status.proto3google/ads/googleads/v17/enums/asset_set_type.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�
AssetSet
id (B�A@

resource_name (	B)�A�A#
!googleads.googleapis.com/AssetSet
name (	B�AS
type (2=.google.ads.googleads.v17.enums.AssetSetTypeEnum.AssetSetTypeB�A�AV
status (2A.google.ads.googleads.v17.enums.AssetSetStatusEnum.AssetSetStatusB�A]
merchant_center_feed (2?.google.ads.googleads.v17.resources.AssetSet.MerchantCenterFeed/
"location_group_parent_asset_set_id
 (B�A`
hotel_property_data (2>.google.ads.googleads.v17.resources.AssetSet.HotelPropertyDataB�AD
location_set (2,.google.ads.googleads.v17.common.LocationSetH h
business_profile_location_group (2=.google.ads.googleads.v17.common.BusinessProfileLocationGroupH S
chain_location_group	 (23.google.ads.googleads.v17.common.ChainLocationGroupH [
MerchantCenterFeed
merchant_id (B�A

feed_label (	B�AH �B
_feed_label{
HotelPropertyData!
hotel_center_id (B�AH �
partner_name (	B�AH�B
_hotel_center_idB

_partner_name:X�AU
!googleads.googleapis.com/AssetSet0customers/{customer_id}/assetSets/{asset_set_id}B
asset_set_sourceB�
&com.google.ads.googleads.v17.resourcesB
AssetSetProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3
�
-google/ads/googleads/v17/resources/feed.proto"google.ads.googleads.v17.resources8google/ads/googleads/v17/enums/feed_attribute_type.proto0google/ads/googleads/v17/enums/feed_origin.proto0google/ads/googleads/v17/enums/feed_status.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�
Feed<

resource_name (	B%�A�A
googleads.googleapis.com/Feed
id (B�AH�
name (	B�AH�E

attributes (21.google.ads.googleads.v17.resources.FeedAttributeX
attribute_operations	 (2:.google.ads.googleads.v17.resources.FeedAttributeOperationN
origin (29.google.ads.googleads.v17.enums.FeedOriginEnum.FeedOriginB�AN
status (29.google.ads.googleads.v17.enums.FeedStatusEnum.FeedStatusB�Ad
places_location_feed_data (2?.google.ads.googleads.v17.resources.Feed.PlacesLocationFeedDataH j
affiliate_location_feed_data (2B.google.ads.googleads.v17.resources.Feed.AffiliateLocationFeedDataH �
PlacesLocationFeedDatab

oauth_info (2I.google.ads.googleads.v17.resources.Feed.PlacesLocationFeedData.OAuthInfoB�A

email_address (	H �
business_account_id (	!
business_name_filter	 (	H�
category_filters (	

label_filters (	�
	OAuthInfo
http_method (	H �
http_request_url (	H�&
http_authorization_header (	H�B
_http_methodB
_http_request_urlB
_http_authorization_headerB
_email_addressB
_business_name_filter�
AffiliateLocationFeedData
	chain_ids (�
relationship_type (2o.google.ads.googleads.v17.enums.AffiliateLocationFeedRelationshipTypeEnum.AffiliateLocationFeedRelationshipType:K�AH
googleads.googleapis.com/Feed\'customers/{customer_id}/feeds/{feed_id}B
system_feed_generation_dataB
_idB
_name"�

FeedAttribute
id (H �
name (	H�U
type (2G.google.ads.googleads.v17.enums.FeedAttributeTypeEnum.FeedAttributeType
is_part_of_key (H�B
_idB
_nameB
_is_part_of_key"�
FeedAttributeOperationZ
operator (2C.google.ads.googleads.v17.resources.FeedAttributeOperation.OperatorB�AE
value (21.google.ads.googleads.v17.resources.FeedAttributeB�A"1
Operator
UNSPECIFIED 
UNKNOWN
ADDB�
&com.google.ads.googleads.v17.resourcesB	FeedProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3
�
8google/ads/googleads/v17/resources/campaign_budget.proto"google.ads.googleads.v17.resources2google/ads/googleads/v17/enums/budget_period.proto2google/ads/googleads/v17/enums/budget_status.proto0google/ads/googleads/v17/enums/budget_type.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�
CampaignBudgetF

resource_name (	B/�A�A)
\'googleads.googleapis.com/CampaignBudget
id (B�AH �
name (	H�

amount_micros (H� 
total_amount_micros (H�R
status (2=.google.ads.googleads.v17.enums.BudgetStatusEnum.BudgetStatusB�Af
delivery_method (2M.google.ads.googleads.v17.enums.BudgetDeliveryMethodEnum.BudgetDeliveryMethod
explicitly_shared (H�!
reference_count (B�AH�(
has_recommended_budget (B�AH�2
 recommended_budget_amount_micros (B�AH�R
period
 (2=.google.ads.googleads.v17.enums.BudgetPeriodEnum.BudgetPeriodB�AC
1recommended_budget_estimated_change_weekly_clicks (B�AH�H
6recommended_budget_estimated_change_weekly_cost_micros (B�AH	�I
7recommended_budget_estimated_change_weekly_interactions (B�AH
�B
0recommended_budget_estimated_change_weekly_views (B�AH�L
type (29.google.ads.googleads.v17.enums.BudgetTypeEnum.BudgetTypeB�A#
aligned_bidding_strategy_id (:j�Ag
\'googleads.googleapis.com/CampaignBudget<customers/{customer_id}/campaignBudgets/{campaign_budget_id}B
_idB
_nameB
_amount_microsB
_total_amount_microsB
_explicitly_sharedB
_reference_countB
_has_recommended_budgetB#
!_recommended_budget_amount_microsB4
2_recommended_budget_estimated_change_weekly_clicksB9
7_recommended_budget_estimated_change_weekly_cost_microsB:
8_recommended_budget_estimated_change_weekly_interactionsB3
1_recommended_budget_estimated_change_weekly_viewsB�
&com.google.ads.googleads.v17.resourcesBCampaignBudgetProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3
�
;google/ads/googleads/v17/resources/campaign_asset_set.proto"google.ads.googleads.v17.resourcesgoogle/api/field_behavior.protogoogle/api/resource.proto"�
CampaignAssetSetH

resource_name (	B1�A�A+
)googleads.googleapis.com/CampaignAssetSet;
campaign (	B)�A�A#
!googleads.googleapis.com/Campaign<
	asset_set (	B)�A�A#
!googleads.googleapis.com/AssetSet^
status (2I.google.ads.googleads.v17.enums.AssetSetLinkStatusEnum.AssetSetLinkStatusB�A:v�As
)googleads.googleapis.com/CampaignAssetSetFcustomers/{customer_id}/campaignAssetSets/{campaign_id}~{asset_set_id}B�
&com.google.ads.googleads.v17.resourcesBCampaignAssetSetProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3
�
4google/ads/googleads/v17/enums/ad_group_status.protogoogle.ads.googleads.v17.enums"h
AdGroupStatusEnum"S

AdGroupStatus
UNSPECIFIED 
UNKNOWN
ENABLED

PAUSED
REMOVEDB�
"com.google.ads.googleads.v17.enumsBAdGroupStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
=google/ads/googleads/v17/enums/brand_safety_suitability.protogoogle.ads.googleads.v17.enums"�
BrandSafetySuitabilityEnum"}
BrandSafetySuitability
UNSPECIFIED 
UNKNOWN
EXPANDED_INVENTORY
STANDARD_INVENTORY
LIMITED_INVENTORYB�
"com.google.ads.googleads.v17.enumsBBrandSafetySuitabilityProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
<google/ads/googleads/v17/enums/frequency_cap_time_unit.protogoogle.ads.googleads.v17.enums"n
FrequencyCapTimeUnitEnum"R
FrequencyCapTimeUnit
UNSPECIFIED 
UNKNOWN
DAY
WEEK	
MONTHB�
"com.google.ads.googleads.v17.enumsBFrequencyCapTimeUnitProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
3google/ads/googleads/v17/common/frequency_cap.protogoogle.ads.googleads.v17.common8google/ads/googleads/v17/enums/frequency_cap_level.proto<google/ads/googleads/v17/enums/frequency_cap_time_unit.proto"l
FrequencyCapEntry=
key (20.google.ads.googleads.v17.common.FrequencyCapKey
cap (H �B
_cap"�
FrequencyCapKeyV
level (2G.google.ads.googleads.v17.enums.FrequencyCapLevelEnum.FrequencyCapLevelc

event_type (2O.google.ads.googleads.v17.enums.FrequencyCapEventTypeEnum.FrequencyCapEventType`
	time_unit (2M.google.ads.googleads.v17.enums.FrequencyCapTimeUnitEnum.FrequencyCapTimeUnit
time_length (H �B
_time_lengthB�
#com.google.ads.googleads.v17.commonBFrequencyCapProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/common;common�GAA�Google.Ads.GoogleAds.V17.Common�Google\\Ads\\GoogleAds\\V17\\Common�#Google::Ads::GoogleAds::V17::Commonbproto3
�
7google/ads/googleads/v17/common/targeting_setting.protogoogle.ads.googleads.v17.common"�
TargetingSettingO
target_restrictions (22.google.ads.googleads.v17.common.TargetRestrictionb
target_restriction_operations (2;.google.ads.googleads.v17.common.TargetRestrictionOperation"�
TargetRestrictionf
targeting_dimension (2I.google.ads.googleads.v17.enums.TargetingDimensionEnum.TargetingDimension
bid_only (H �B
	_bid_only"�
TargetRestrictionOperationV
operator (2D.google.ads.googleads.v17.common.TargetRestrictionOperation.OperatorA
value (22.google.ads.googleads.v17.common.TargetRestriction"=
Operator
UNSPECIFIED 
UNKNOWN
ADD

REMOVEB�
#com.google.ads.googleads.v17.commonBTargetingSettingProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/common;common�GAA�Google.Ads.GoogleAds.V17.Common�Google\\Ads\\GoogleAds\\V17\\Common�#Google::Ads::GoogleAds::V17::Commonbproto3
�[
1google/ads/googleads/v17/resources/campaign.proto"google.ads.googleads.v17.resources6google/ads/googleads/v17/common/custom_parameter.proto3google/ads/googleads/v17/common/frequency_cap.proto?google/ads/googleads/v17/common/real_time_bidding_setting.proto7google/ads/googleads/v17/common/targeting_setting.protoCgoogle/ads/googleads/v17/enums/ad_serving_optimization_status.protoAgoogle/ads/googleads/v17/enums/advertising_channel_sub_type.proto=google/ads/googleads/v17/enums/advertising_channel_type.proto;google/ads/googleads/v17/enums/app_campaign_app_store.protoLgoogle/ads/googleads/v17/enums/app_campaign_bidding_strategy_goal_type.proto<google/ads/googleads/v17/enums/asset_automation_status.proto:google/ads/googleads/v17/enums/asset_automation_type.proto5google/ads/googleads/v17/enums/asset_field_type.proto3google/ads/googleads/v17/enums/asset_set_type.protoCgoogle/ads/googleads/v17/enums/bidding_strategy_system_status.proto:google/ads/googleads/v17/enums/bidding_strategy_type.proto=google/ads/googleads/v17/enums/brand_safety_suitability.proto=google/ads/googleads/v17/enums/campaign_experiment_type.proto@google/ads/googleads/v17/enums/campaign_keyword_match_type.proto<google/ads/googleads/v17/enums/campaign_primary_status.protoCgoogle/ads/googleads/v17/enums/campaign_primary_status_reason.proto<google/ads/googleads/v17/enums/campaign_serving_status.proto4google/ads/googleads/v17/enums/campaign_status.proto1google/ads/googleads/v17/enums/listing_type.proto9google/ads/googleads/v17/enums/location_source_type.proto=google/ads/googleads/v17/enums/negative_geo_target_type.proto;google/ads/googleads/v17/enums/optimization_goal_type.proto1google/ads/googleads/v17/enums/payment_mode.protoCgoogle/ads/googleads/v17/enums/performance_max_upgrade_status.proto=google/ads/googleads/v17/enums/positive_geo_target_type.protoCgoogle/ads/googleads/v17/enums/vanity_pharma_display_url_mode.proto7google/ads/googleads/v17/enums/vanity_pharma_text.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�H
Campaign@

resource_name (	B)�A�A#
!googleads.googleapis.com/Campaign
id; (B�AH�
name: (	H�l
primary_statusQ (2O.google.ads.googleads.v17.enums.CampaignPrimaryStatusEnum.CampaignPrimaryStatusB�A�
primary_status_reasonsR (2[.google.ads.googleads.v17.enums.CampaignPrimaryStatusReasonEnum.CampaignPrimaryStatusReasonB�AQ
status (2A.google.ads.googleads.v17.enums.CampaignStatusEnum.CampaignStatusl
serving_status (2O.google.ads.googleads.v17.enums.CampaignServingStatusEnum.CampaignServingStatusB�A�
bidding_strategy_system_statusN (2[.google.ads.googleads.v17.enums.BiddingStrategySystemStatusEnum.BiddingStrategySystemStatusB�A�
ad_serving_optimization_status (2[.google.ads.googleads.v17.enums.AdServingOptimizationStatusEnum.AdServingOptimizationStatusx
advertising_channel_type	 (2Q.google.ads.googleads.v17.enums.AdvertisingChannelTypeEnum.AdvertisingChannelTypeB�A�
advertising_channel_sub_type
 (2W.google.ads.googleads.v17.enums.AdvertisingChannelSubTypeEnum.AdvertisingChannelSubTypeB�A"
tracking_url_template< (	H�O
url_custom_parameters (20.google.ads.googleads.v17.common.CustomParametert
 local_services_campaign_settingsK (2J.google.ads.googleads.v17.resources.Campaign.LocalServicesCampaignSettingse
travel_campaign_settingsU (2C.google.ads.googleads.v17.resources.Campaign.TravelCampaignSettingsl
demand_gen_campaign_settings[ (2F.google.ads.googleads.v17.resources.Campaign.DemandGenCampaignSettingsZ
real_time_bidding_setting\' (27.google.ads.googleads.v17.common.RealTimeBiddingSettingV
network_settings (2<.google.ads.googleads.v17.resources.Campaign.NetworkSettingsY

hotel_setting  (2=.google.ads.googleads.v17.resources.Campaign.HotelSettingInfoB�Ah
dynamic_search_ads_setting! (2D.google.ads.googleads.v17.resources.Campaign.DynamicSearchAdsSettingV
shopping_setting$ (2<.google.ads.googleads.v17.resources.Campaign.ShoppingSettingL
targeting_setting+ (21.google.ads.googleads.v17.common.TargetingSetting`
audience_settingI (2<.google.ads.googleads.v17.resources.Campaign.AudienceSettingB�AH�b
geo_target_type_setting/ (2A.google.ads.googleads.v17.resources.Campaign.GeoTargetTypeSettinga
local_campaign_setting2 (2A.google.ads.googleads.v17.resources.Campaign.LocalCampaignSetting]
app_campaign_setting3 (2?.google.ads.googleads.v17.resources.Campaign.AppCampaignSetting>
labels= (	B.�A�A(
&googleads.googleapis.com/CampaignLabelo
experiment_type (2Q.google.ads.googleads.v17.enums.CampaignExperimentTypeEnum.CampaignExperimentTypeB�AE

base_campaign8 (	B)�A�A#
!googleads.googleapis.com/CampaignH�J
campaign_budget> (	B,�A)
\'googleads.googleapis.com/CampaignBudgetH�o
bidding_strategy_type (2K.google.ads.googleads.v17.enums.BiddingStrategyTypeEnum.BiddingStrategyTypeB�A_
accessible_bidding_strategyG (	B:�A�A4
2googleads.googleapis.com/AccessibleBiddingStrategy

start_date? (	H�H
campaign_groupL (	B+�A(
&googleads.googleapis.com/CampaignGroupH�
end_date@ (	H	�
final_url_suffixA (	H
�J
frequency_caps( (22.google.ads.googleads.v17.common.FrequencyCapEntryy
video_brand_safety_suitability* (2Q.google.ads.googleads.v17.enums.BrandSafetySuitabilityEnum.BrandSafetySuitabilityP

vanity_pharma, (29.google.ads.googleads.v17.resources.Campaign.VanityPharmab
selective_optimization- (2B.google.ads.googleads.v17.resources.Campaign.SelectiveOptimizationg
optimization_goal_setting6 (2D.google.ads.googleads.v17.resources.Campaign.OptimizationGoalSetting[
tracking_setting. (2<.google.ads.googleads.v17.resources.Campaign.TrackingSettingB�AQ
payment_mode4 (2;.google.ads.googleads.v17.enums.PaymentModeEnum.PaymentMode$
optimization_scoreB (B�AH�l
!excluded_parent_asset_field_typesE (2A.google.ads.googleads.v17.enums.AssetFieldTypeEnum.AssetFieldTypef
excluded_parent_asset_set_typesP (2=.google.ads.googleads.v17.enums.AssetSetTypeEnum.AssetSetType"
url_expansion_opt_outH (H�h
performance_max_upgradeM (2B.google.ads.googleads.v17.resources.Campaign.PerformanceMaxUpgradeB�AP
hotel_property_asset_setS (	B)�A�A#
!googleads.googleapis.com/AssetSetH
�[
listing_typeV (2;.google.ads.googleads.v17.enums.ListingTypeEnum.ListingTypeB�AH�f
asset_automation_settingsX (2C.google.ads.googleads.v17.resources.Campaign.AssetAutomationSettingq
keyword_match_typeZ (2U.google.ads.googleads.v17.enums.CampaignKeywordMatchTypeEnum.CampaignKeywordMatchTypeI
bidding_strategyC (	B-�A*
(googleads.googleapis.com/BiddingStrategyH A

commission1 (2+.google.ads.googleads.v17.common.CommissionH @

manual_cpaJ (2*.google.ads.googleads.v17.common.ManualCpaH @

manual_cpc (2*.google.ads.googleads.v17.common.ManualCpcH @

manual_cpm (2*.google.ads.googleads.v17.common.ManualCpmH @

manual_cpv% (2*.google.ads.googleads.v17.common.ManualCpvH T
maximize_conversions (24.google.ads.googleads.v17.common.MaximizeConversionsH ]
maximize_conversion_value (28.google.ads.googleads.v17.common.MaximizeConversionValueH @

target_cpa (2*.google.ads.googleads.v17.common.TargetCpaH Y
target_impression_share0 (26.google.ads.googleads.v17.common.TargetImpressionShareH B
target_roas (2+.google.ads.googleads.v17.common.TargetRoasH D
target_spend (2,.google.ads.googleads.v17.common.TargetSpendH B
percent_cpc" (2+.google.ads.googleads.v17.common.PercentCpcH @

target_cpm) (2*.google.ads.googleads.v17.common.TargetCpmH �
PerformanceMaxUpgradeK
performance_max_campaign (	B)�A�A#
!googleads.googleapis.com/CampaignG
pre_upgrade_campaign (	B)�A�A#
!googleads.googleapis.com/Campaignp
status (2[.google.ads.googleads.v17.enums.PerformanceMaxUpgradeStatusEnum.PerformanceMaxUpgradeStatusB�A�
NetworkSettings!
target_google_search (H �"
target_search_network (H�#
target_content_network (H�*
target_partner_search_network (H�
target_youtube	 (H�%
target_google_tv_network
 (H�B
_target_google_searchB
_target_search_networkB
_target_content_networkB 
_target_partner_search_networkB
_target_youtubeB
_target_google_tv_networkI
HotelSettingInfo!
hotel_center_id (B�AH �B
_hotel_center_id�
DynamicSearchAdsSetting
domain_name (	B�A

language_code (	B�A#
use_supplied_urls_only (H �1
feeds	 (	B"�A
googleads.googleapis.com/FeedB
_use_supplied_urls_only�
ShoppingSetting
merchant_id (H �

feed_label
 (	
campaign_priority (H�
enable_local (H�"
use_vehicle_inventory	 (B�A$
advertising_partner_ids (B�A!
disable_product_feed (H�B
_merchant_idB
_campaign_priorityB

_enable_localB
_disable_product_feedB
TrackingSetting
tracking_url (	B�AH �B

_tracking_url�
GeoTargetTypeSettingq
positive_geo_target_type (2O.google.ads.googleads.v17.enums.PositiveGeoTargetTypeEnum.PositiveGeoTargetTypeq
negative_geo_target_type (2O.google.ads.googleads.v17.enums.NegativeGeoTargetTypeEnum.NegativeGeoTargetType
LocalCampaignSettingg
location_source_type (2I.google.ads.googleads.v17.enums.LocationSourceTypeEnum.LocationSourceType�
AppCampaignSetting�
bidding_strategy_goal_type (2i.google.ads.googleads.v17.enums.AppCampaignBiddingStrategyGoalTypeEnum.AppCampaignBiddingStrategyGoalType
app_id (	B�AH �c
	app_store (2K.google.ads.googleads.v17.enums.AppCampaignAppStoreEnum.AppCampaignAppStoreB�AB	
_app_id�
VanityPharma�
vanity_pharma_display_url_mode (2Y.google.ads.googleads.v17.enums.VanityPharmaDisplayUrlModeEnum.VanityPharmaDisplayUrlModea
vanity_pharma_text (2E.google.ads.googleads.v17.enums.VanityPharmaTextEnum.VanityPharmaTextc
SelectiveOptimizationJ
conversion_actions (	B.�A+
)googleads.googleapis.com/ConversionAction�
OptimizationGoalSettingn
optimization_goal_types (2M.google.ads.googleads.v17.enums.OptimizationGoalTypeEnum.OptimizationGoalTypeR
AudienceSetting&
use_audience_grouped (B�AH �B
_use_audience_groupedp
LocalServicesCampaignSettingsO

category_bids (28.google.ads.googleads.v17.resources.Campaign.CategoryBidu
CategoryBid
category_id (	H �"
manual_cpa_bid_micros (H�B
_category_idB
_manual_cpa_bid_microsS
TravelCampaignSettings#
travel_account_id (B�AH �B
_travel_account_idX
DemandGenCampaignSettings$
upgraded_targeting (B�AH �B
_upgraded_targeting�
AssetAutomationSettingo
asset_automation_type (2K.google.ads.googleads.v17.enums.AssetAutomationTypeEnum.AssetAutomationTypeH �u
asset_automation_status (2O.google.ads.googleads.v17.enums.AssetAutomationStatusEnum.AssetAutomationStatusH�B
_asset_automation_typeB
_asset_automation_status:W�AT
!googleads.googleapis.com/Campaign/customers/{customer_id}/campaigns/{campaign_id}B
campaign_bidding_strategyB
_idB
_nameB
_tracking_url_templateB
_audience_settingB
_base_campaignB
_campaign_budgetB
_start_dateB
_campaign_groupB
	_end_dateB
_final_url_suffixB
_optimization_scoreB
_url_expansion_opt_outB
_hotel_property_asset_setB

_listing_typeB�
&com.google.ads.googleads.v17.resourcesB
CampaignProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3
�
9google/ads/googleads/v17/enums/parental_status_type.protogoogle.ads.googleads.v17.enums"
ParentalStatusTypeEnum"e
ParentalStatusType
UNSPECIFIED 
UNKNOWN
PARENT�
NOT_A_PARENT�
UNDETERMINED�B�
"com.google.ads.googleads.v17.enumsBParentalStatusTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
>google/ads/googleads/v17/enums/display_ad_format_setting.protogoogle.ads.googleads.v17.enums"�
DisplayAdFormatSettingEnum"c
DisplayAdFormatSetting
UNSPECIFIED 
UNKNOWN
ALL_FORMATS

NON_NATIVE

NATIVEB�
"com.google.ads.googleads.v17.enumsBDisplayAdFormatSettingProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
<google/ads/googleads/v17/enums/served_asset_field_type.protogoogle.ads.googleads.v17.enums"�
ServedAssetFieldTypeEnum"�
ServedAssetFieldType
UNSPECIFIED 
UNKNOWN

HEADLINE_1

HEADLINE_2

HEADLINE_3

DESCRIPTION_1

DESCRIPTION_2
HEADLINE
HEADLINE_IN_PORTRAIT

LONG_HEADLINE	
DESCRIPTION

DESCRIPTION_IN_PORTRAIT
BUSINESS_NAME_IN_PORTRAIT

BUSINESS_NAME

MARKETING_IMAGE
MARKETING_IMAGE_IN_PORTRAIT
SQUARE_MARKETING_IMAGE
PORTRAIT_MARKETING_IMAGE
LOGO
LANDSCAPE_LOGO
CALL_TO_ACTION
YOU_TUBE_VIDEO
SITELINK
CALL

MOBILE_APP
CALLOUT
STRUCTURED_SNIPPET	
PRICE
	PROMOTION
AD_IMAGE
	LEAD_FORM

BUSINESS_LOGOB�
"com.google.ads.googleads.v17.enumsBServedAssetFieldTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
6google/ads/googleads/v17/enums/mobile_app_vendor.protogoogle.ads.googleads.v17.enums"q
MobileAppVendorEnum"Z
MobileAppVendor
UNSPECIFIED 
UNKNOWN
APPLE_APP_STORE
GOOGLE_APP_STOREB�
"com.google.ads.googleads.v17.enumsBMobileAppVendorProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
6google/ads/googleads/v17/enums/income_range_type.protogoogle.ads.googleads.v17.enums"�
IncomeRangeTypeEnum"�
IncomeRangeType
UNSPECIFIED 
UNKNOWN
INCOME_RANGE_0_50��
INCOME_RANGE_50_60��
INCOME_RANGE_60_70��
INCOME_RANGE_70_80��
INCOME_RANGE_80_90��
INCOME_RANGE_90_UP��
INCOME_RANGE_UNDETERMINED��B�
"com.google.ads.googleads.v17.enumsBIncomeRangeTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�\\
.google/ads/googleads/v17/common/criteria.protogoogle.ads.googleads.v17.common;google/ads/googleads/v17/enums/app_payment_model_type.protoCgoogle/ads/googleads/v17/enums/brand_request_rejection_reason.proto0google/ads/googleads/v17/enums/brand_state.proto7google/ads/googleads/v17/enums/content_label_type.proto0google/ads/googleads/v17/enums/day_of_week.proto+google/ads/googleads/v17/enums/device.proto0google/ads/googleads/v17/enums/gender_type.proto>google/ads/googleads/v17/enums/hotel_date_selection_type.proto6google/ads/googleads/v17/enums/income_range_type.proto5google/ads/googleads/v17/enums/interaction_type.proto7google/ads/googleads/v17/enums/keyword_match_type.proto7google/ads/googleads/v17/enums/listing_group_type.proto@google/ads/googleads/v17/enums/location_group_radius_units.proto3google/ads/googleads/v17/enums/minute_of_hour.proto9google/ads/googleads/v17/enums/parental_status_type.proto;google/ads/googleads/v17/enums/product_category_level.proto4google/ads/googleads/v17/enums/product_channel.proto@google/ads/googleads/v17/enums/product_channel_exclusivity.proto6google/ads/googleads/v17/enums/product_condition.protoCgoogle/ads/googleads/v17/enums/product_custom_attribute_index.proto7google/ads/googleads/v17/enums/product_type_level.proto;google/ads/googleads/v17/enums/proximity_radius_units.proto>google/ads/googleads/v17/enums/webpage_condition_operand.proto?google/ads/googleads/v17/enums/webpage_condition_operator.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�
KeywordInfo
text (	H �Y

match_type (2E.google.ads.googleads.v17.enums.KeywordMatchTypeEnum.KeywordMatchTypeB
_text")

PlacementInfo
url (	H �B
_url"A
NegativeKeywordListInfo

shared_set (	H �B
_shared_set"�
MobileAppCategoryInfob
mobile_app_category_constant (	B7�A4
2googleads.googleapis.com/MobileAppCategoryConstantH �B
_mobile_app_category_constant"S
MobileApplicationInfo
app_id (	H �
name (	H�B	
_app_idB
_name"H
LocationInfo 
geo_target_constant (	H �B
_geo_target_constant"M

DeviceInfo?
type (21.google.ads.googleads.v17.enums.DeviceEnum.Device"�
ListingGroupInfoS
type (2E.google.ads.googleads.v17.enums.ListingGroupTypeEnum.ListingGroupTypeI

case_value (25.google.ads.googleads.v17.common.ListingDimensionInfo&
parent_ad_group_criterion (	H �H
path (25.google.ads.googleads.v17.common.ListingDimensionPathH�B
_parent_ad_group_criterionB
_path"a
ListingDimensionPathI

dimensions (25.google.ads.googleads.v17.common.ListingDimensionInfo"]
ListingScopeInfoI

dimensions (25.google.ads.googleads.v17.common.ListingDimensionInfo"�
ListingDimensionInfo@
hotel_id (2,.google.ads.googleads.v17.common.HotelIdInfoH F
hotel_class (2/.google.ads.googleads.v17.common.HotelClassInfoH W
hotel_country_region (27.google.ads.googleads.v17.common.HotelCountryRegionInfoH F
hotel_state (2/.google.ads.googleads.v17.common.HotelStateInfoH D

hotel_city (2..google.ads.googleads.v17.common.HotelCityInfoH P
product_category (24.google.ads.googleads.v17.common.ProductCategoryInfoH J

product_brand (21.google.ads.googleads.v17.common.ProductBrandInfoH N
product_channel (23.google.ads.googleads.v17.common.ProductChannelInfoH e
product_channel_exclusivity	 (2>.google.ads.googleads.v17.common.ProductChannelExclusivityInfoH R
product_condition
 (25.google.ads.googleads.v17.common.ProductConditionInfoH _
product_custom_attribute (2;.google.ads.googleads.v17.common.ProductCustomAttributeInfoH M
product_item_id (22.google.ads.googleads.v17.common.ProductItemIdInfoH H
product_type (20.google.ads.googleads.v17.common.ProductTypeInfoH P
product_grouping (24.google.ads.googleads.v17.common.ProductGroupingInfoH L
product_labels (22.google.ads.googleads.v17.common.ProductLabelsInfoH _
product_legacy_condition (2;.google.ads.googleads.v17.common.ProductLegacyConditionInfoH Q
product_type_full (24.google.ads.googleads.v17.common.ProductTypeFullInfoH F
activity_id (2/.google.ads.googleads.v17.common.ActivityIdInfoH N
activity_rating (23.google.ads.googleads.v17.common.ActivityRatingInfoH P
activity_country (24.google.ads.googleads.v17.common.ActivityCountryInfoH L
activity_state (22.google.ads.googleads.v17.common.ActivityStateInfoH J

activity_city (21.google.ads.googleads.v17.common.ActivityCityInfoH a
unknown_listing_dimension (2<.google.ads.googleads.v17.common.UnknownListingDimensionInfoH B
	dimension"+
HotelIdInfo
value (	H �B
_value".
HotelClassInfo
value (H �B
_value"\\
HotelCountryRegionInfo%
country_region_criterion (	H �B
_country_region_criterion"B
HotelStateInfo
state_criterion (	H �B
_state_criterion"?

HotelCityInfo
city_criterion (	H �B
_city_criterion"�
ProductCategoryInfo
category_id (H �\\
level (2M.google.ads.googleads.v17.enums.ProductCategoryLevelEnum.ProductCategoryLevelB
_category_id"0
ProductBrandInfo
value (	H �B
_value"h
ProductChannelInfoR
channel (2A.google.ads.googleads.v17.enums.ProductChannelEnum.ProductChannel"�
ProductChannelExclusivityInfot
channel_exclusivity (2W.google.ads.googleads.v17.enums.ProductChannelExclusivityEnum.ProductChannelExclusivity"p
ProductConditionInfoX
	condition (2E.google.ads.googleads.v17.enums.ProductConditionEnum.ProductCondition"�
ProductCustomAttributeInfo
value (	H �j
index (2[.google.ads.googleads.v17.enums.ProductCustomAttributeIndexEnum.ProductCustomAttributeIndexB
_value"1
ProductItemIdInfo
value (	H �B
_value"�
ProductTypeInfo
value (	H �T
level (2E.google.ads.googleads.v17.enums.ProductTypeLevelEnum.ProductTypeLevelB
_value"3
ProductGroupingInfo
value (	H �B
_value"1
ProductLabelsInfo
value (	H �B
_value":
ProductLegacyConditionInfo
value (	H �B
_value"3
ProductTypeFullInfo
value (	H �B
_value"
UnknownListingDimensionInfo"}
HotelDateSelectionTypeInfo_
type (2Q.google.ads.googleads.v17.enums.HotelDateSelectionTypeEnum.HotelDateSelectionType"g
HotelAdvanceBookingWindowInfo
min_days (H �
max_days (H�B
	_min_daysB
	_max_days"g
HotelLengthOfStayInfo

min_nights (H �

max_nights (H�B
_min_nightsB
_max_nights"A
HotelCheckInDateRangeInfo

start_date (	
end_date (	"c
HotelCheckInDayInfoL
day_of_week (27.google.ads.googleads.v17.enums.DayOfWeekEnum.DayOfWeek".
ActivityIdInfo
value (	H �B
_value"2
ActivityRatingInfo
value (H �B
_value"3
ActivityCountryInfo
value (	H �B
_value"1
ActivityStateInfo
value (	H �B
_value"0
ActivityCityInfo
value (	H �B
_value"h
InteractionTypeInfoQ
type (2C.google.ads.googleads.v17.enums.InteractionTypeEnum.InteractionType"�
AdScheduleInfoS
start_minute (2=.google.ads.googleads.v17.enums.MinuteOfHourEnum.MinuteOfHourQ

end_minute (2=.google.ads.googleads.v17.enums.MinuteOfHourEnum.MinuteOfHour

start_hour (H �
end_hour (H�L
day_of_week (27.google.ads.googleads.v17.enums.DayOfWeekEnum.DayOfWeekB
_start_hourB
	_end_hour"[
AgeRangeInfoK
type (2=.google.ads.googleads.v17.enums.AgeRangeTypeEnum.AgeRangeType"U

GenderInfoG
type (29.google.ads.googleads.v17.enums.GenderTypeEnum.GenderType"d
IncomeRangeInfoQ
type (2C.google.ads.googleads.v17.enums.IncomeRangeTypeEnum.IncomeRangeType"m
ParentalStatusInfoW
type (2I.google.ads.googleads.v17.enums.ParentalStatusTypeEnum.ParentalStatusType"6
YouTubeVideoInfo
video_id (	H �B
	_video_id"<
YouTubeChannelInfo

channel_id (	H �B
_channel_id"4
UserListInfo
	user_list (	H �B

_user_list"�

ProximityInfo@
	geo_point (2-.google.ads.googleads.v17.common.GeoPointInfo
radius (H �c
radius_units (2M.google.ads.googleads.v17.enums.ProximityRadiusUnitsEnum.ProximityRadiusUnits=
address (2,.google.ads.googleads.v17.common.AddressInfoB	
_radius"�
GeoPointInfo\'
longitude_in_micro_degrees (H �&
latitude_in_micro_degrees (H�B
_longitude_in_micro_degreesB
_latitude_in_micro_degrees"�
AddressInfo
postal_code (	H �

province_code	 (	H�
country_code
 (	H�

province_name (	H�
street_address (	H�
street_address2
 (	H�
	city_name (	H�B
_postal_codeB
_province_codeB

_country_codeB
_province_nameB
_street_addressB
_street_address2B

_city_name"v
	TopicInfoH
topic_constant (	B+�A(
&googleads.googleapis.com/TopicConstantH �
path (	B
_topic_constant"D
LanguageInfo
language_constant (	H �B
_language_constant"5
IpBlockInfo

ip_address (	H �B
_ip_address"g
ContentLabelInfoS
type (2E.google.ads.googleads.v17.enums.ContentLabelTypeEnum.ContentLabelType"p
CarrierInfoL
carrier_constant (	B-�A*
(googleads.googleapis.com/CarrierConstantH �B
_carrier_constant"R
UserInterestInfo#
user_interest_category (	H �B
_user_interest_category"�
WebpageInfo
criterion_name (	H �I

conditions (25.google.ads.googleads.v17.common.WebpageConditionInfo
coverage_percentage (B
sample (22.google.ads.googleads.v17.common.WebpageSampleInfoB
_criterion_name"�
WebpageConditionInfod
operand (2S.google.ads.googleads.v17.enums.WebpageConditionOperandEnum.WebpageConditionOperandg
operator (2U.google.ads.googleads.v17.enums.WebpageConditionOperatorEnum.WebpageConditionOperator
argument (	H �B
	_argument"(
WebpageSampleInfo
sample_urls (	"�
OperatingSystemVersionInfol
!operating_system_version_constant (	B<�A9
7googleads.googleapis.com/OperatingSystemVersionConstantH �B$
"_operating_system_version_constant"p
AppPaymentModelInfoY
type (2K.google.ads.googleads.v17.enums.AppPaymentModelTypeEnum.AppPaymentModelType"�
MobileDeviceInfoW
mobile_device_constant (	B2�A/
-googleads.googleapis.com/MobileDeviceConstantH �B
_mobile_device_constant"F
CustomAffinityInfo
custom_affinity (	H �B
_custom_affinity"@
CustomIntentInfo

custom_intent (	H �B
_custom_intent"�
LocationGroupInfo
feed (	H �
geo_target_constants (	
radius (H�k
radius_units (2U.google.ads.googleads.v17.enums.LocationGroupRadiusUnitsEnum.LocationGroupRadiusUnits
feed_item_sets (	5
(enable_customer_level_location_asset_set	 (H�!
location_group_asset_sets
 (	B
_feedB	
_radiusB+
)_enable_customer_level_location_asset_set"-
CustomAudienceInfo
custom_audience (	"a
CombinedAudienceInfoI
combined_audience (	B.�A+
)googleads.googleapis.com/CombinedAudience" 
AudienceInfo
audience (	"�
KeywordThemeInfoT
keyword_theme_constant (	B2�A/
-googleads.googleapis.com/KeywordThemeConstantH !
free_form_keyword_theme (	H B

keyword_theme"(
LocalServiceIdInfo

service_id (	"
SearchThemeInfo
text (	"�
	BrandInfo
display_name (	B�AH �
	entity_id (	H�
primary_url (	B�AH�
rejection_reason (2[.google.ads.googleads.v17.enums.BrandRequestRejectionReasonEnum.BrandRequestRejectionReasonB�AH�S
status (29.google.ads.googleads.v17.enums.BrandStateEnum.BrandStateB�AH�B

_display_nameB

_entity_idB
_primary_urlB
_rejection_reasonB	
_status"7

BrandListInfo

shared_set (	H �B
_shared_setB�
#com.google.ads.googleads.v17.commonB
CriteriaProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/common;common�GAA�Google.Ads.GoogleAds.V17.Common�Google\\Ads\\GoogleAds\\V17\\Common�#Google::Ads::GoogleAds::V17::Commonbproto3
�
>google/ads/googleads/v17/resources/ad_group_bid_modifier.proto"google.ads.googleads.v17.resources8google/ads/googleads/v17/enums/bid_modifier_source.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�	
AdGroupBidModifierJ

resource_name (	B3�A�A-
+googleads.googleapis.com/AdGroupBidModifier?
ad_group
 (	B(�A�A"
 googleads.googleapis.com/AdGroupH�
criterion_id (B�AH�
bid_modifier (H�D

base_ad_group (	B(�A�A"
 googleads.googleapis.com/AdGroupH�i
bid_modifier_source
 (2G.google.ads.googleads.v17.enums.BidModifierSourceEnum.BidModifierSourceB�Ae
hotel_date_selection_type (2;.google.ads.googleads.v17.common.HotelDateSelectionTypeInfoB�AH k
hotel_advance_booking_window (2>.google.ads.googleads.v17.common.HotelAdvanceBookingWindowInfoB�AH [
hotel_length_of_stay (26.google.ads.googleads.v17.common.HotelLengthOfStayInfoB�AH W
hotel_check_in_day (24.google.ads.googleads.v17.common.HotelCheckInDayInfoB�AH B
device (2+.google.ads.googleads.v17.common.DeviceInfoB�AH d
hotel_check_in_date_range (2:.google.ads.googleads.v17.common.HotelCheckInDateRangeInfoB�AH :z�Aw
+googleads.googleapis.com/AdGroupBidModifierHcustomers/{customer_id}/adGroupBidModifiers/{ad_group_id}~{criterion_id}B
	criterionB
	_ad_groupB

_criterion_idB

_bid_modifierB
_base_ad_groupB�
&com.google.ads.googleads.v17.resourcesBAdGroupBidModifierProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3
�
?google/ads/googleads/v17/enums/matching_function_operator.protogoogle.ads.googleads.v17.enums"�
MatchingFunctionOperatorEnum"u
MatchingFunctionOperator
UNSPECIFIED 
UNKNOWN
IN
IDENTITY

EQUALS
AND
CONTAINS_ANYB�
"com.google.ads.googleads.v17.enumsBMatchingFunctionOperatorProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
7google/ads/googleads/v17/common/matching_function.protogoogle.ads.googleads.v17.common?google/ads/googleads/v17/enums/matching_function_operator.proto"�
MatchingFunction
function_string (	H �g
operator (2U.google.ads.googleads.v17.enums.MatchingFunctionOperatorEnum.MatchingFunctionOperator?

left_operands (2(.google.ads.googleads.v17.common.Operand@
right_operands (2(.google.ads.googleads.v17.common.OperandB
_function_string"�
OperandT
constant_operand (28.google.ads.googleads.v17.common.Operand.ConstantOperandH _
feed_attribute_operand (2=.google.ads.googleads.v17.common.Operand.FeedAttributeOperandH T
function_operand (28.google.ads.googleads.v17.common.Operand.FunctionOperandH a
request_context_operand (2>.google.ads.googleads.v17.common.Operand.RequestContextOperandH �
ConstantOperand
string_value (	H 

long_value (H 

boolean_value (H 
double_value (H B
constant_operand_valuen
FeedAttributeOperand
feed_id (H �
feed_attribute_id (H�B

_feed_idB
_feed_attribute_id_
FunctionOperandL
matching_function (21.google.ads.googleads.v17.common.MatchingFunction�
RequestContextOperandq
context_type (2[.google.ads.googleads.v17.enums.MatchingFunctionContextTypeEnum.MatchingFunctionContextTypeB
function_argument_operandB�
#com.google.ads.googleads.v17.commonBMatchingFunctionProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/common;common�GAA�Google.Ads.GoogleAds.V17.Common�Google\\Ads\\GoogleAds\\V17\\Common�#Google::Ads::GoogleAds::V17::Commonbproto3
�
6google/ads/googleads/v17/resources/campaign_feed.proto"google.ads.googleads.v17.resources5google/ads/googleads/v17/enums/feed_link_status.proto5google/ads/googleads/v17/enums/placeholder_type.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�
CampaignFeedD

resource_name (	B-�A�A\'
%googleads.googleapis.com/CampaignFeed8
feed (	B%�A�A
googleads.googleapis.com/FeedH �@
campaign (	B)�A�A#
!googleads.googleapis.com/CampaignH�^
placeholder_types (2C.google.ads.googleads.v17.enums.PlaceholderTypeEnum.PlaceholderTypeL
matching_function (21.google.ads.googleads.v17.common.MatchingFunctionV
status (2A.google.ads.googleads.v17.enums.FeedLinkStatusEnum.FeedLinkStatusB�A:i�Af
%googleads.googleapis.com/CampaignFeed=customers/{customer_id}/campaignFeeds/{campaign_id}~{feed_id}B
_feedB
	_campaignB�
&com.google.ads.googleads.v17.resourcesBCampaignFeedProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3
�
3google/ads/googleads/v17/enums/criterion_type.protogoogle.ads.googleads.v17.enums"�
CriterionTypeEnum"�

CriterionType
UNSPECIFIED 
UNKNOWN
KEYWORD
	PLACEMENT
MOBILE_APP_CATEGORY
MOBILE_APPLICATION

DEVICE
LOCATION

LISTING_GROUP
AD_SCHEDULE	
	AGE_RANGE


GENDER
INCOME_RANGE
PARENTAL_STATUS


YOUTUBE_VIDEO
YOUTUBE_CHANNEL
	USER_LIST
	PROXIMITY	
TOPIC

LISTING_SCOPE
LANGUAGE
IP_BLOCK

CONTENT_LABEL
CARRIER

USER_INTEREST
WEBPAGE
OPERATING_SYSTEM_VERSION
APP_PAYMENT_MODEL

MOBILE_DEVICE
CUSTOM_AFFINITY

CUSTOM_INTENT
LOCATION_GROUP
CUSTOM_AUDIENCE 
COMBINED_AUDIENCE!

KEYWORD_THEME"
AUDIENCE#
NEGATIVE_KEYWORD_LIST$
LOCAL_SERVICE_ID%
SEARCH_THEME&	
BRAND\'

BRAND_LIST(

LIFE_EVENT)B�
"com.google.ads.googleads.v17.enumsBCriterionTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
;google/ads/googleads/v17/resources/campaign_criterion.proto"google.ads.googleads.v17.resources>google/ads/googleads/v17/enums/campaign_criterion_status.proto3google/ads/googleads/v17/enums/criterion_type.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�
CampaignCriterionI

resource_name (	B2�A�A,
*googleads.googleapis.com/CampaignCriterion@
campaign% (	B)�A�A#
!googleads.googleapis.com/CampaignH�
criterion_id& (B�AH�
display_name+ (	B�A
bid_modifier\' (H�
negative( (B�AH�R
type (2?.google.ads.googleads.v17.enums.CriterionTypeEnum.CriterionTypeB�Ac
status# (2S.google.ads.googleads.v17.enums.CampaignCriterionStatusEnum.CampaignCriterionStatusD
keyword (2,.google.ads.googleads.v17.common.KeywordInfoB�AH H
	placement	 (2..google.ads.googleads.v17.common.PlacementInfoB�AH Z
mobile_app_category
 (26.google.ads.googleads.v17.common.MobileAppCategoryInfoB�AH Y
mobile_application (26.google.ads.googleads.v17.common.MobileApplicationInfoB�AH F
location (2-.google.ads.googleads.v17.common.LocationInfoB�AH B
device
 (2+.google.ads.googleads.v17.common.DeviceInfoB�AH K
ad_schedule (2/.google.ads.googleads.v17.common.AdScheduleInfoB�AH G
	age_range (2-.google.ads.googleads.v17.common.AgeRangeInfoB�AH B
gender (2+.google.ads.googleads.v17.common.GenderInfoB�AH M
income_range (20.google.ads.googleads.v17.common.IncomeRangeInfoB�AH S
parental_status (23.google.ads.googleads.v17.common.ParentalStatusInfoB�AH G
	user_list (2-.google.ads.googleads.v17.common.UserListInfoB�AH O

youtube_video (21.google.ads.googleads.v17.common.YouTubeVideoInfoB�AH S
youtube_channel (23.google.ads.googleads.v17.common.YouTubeChannelInfoB�AH H
	proximity (2..google.ads.googleads.v17.common.ProximityInfoB�AH @
topic (2*.google.ads.googleads.v17.common.TopicInfoB�AH O

listing_scope (21.google.ads.googleads.v17.common.ListingScopeInfoB�AH F
language (2-.google.ads.googleads.v17.common.LanguageInfoB�AH E
ip_block (2,.google.ads.googleads.v17.common.IpBlockInfoB�AH O

content_label (21.google.ads.googleads.v17.common.ContentLabelInfoB�AH D
carrier (2,.google.ads.googleads.v17.common.CarrierInfoB�AH O

user_interest (21.google.ads.googleads.v17.common.UserInterestInfoB�AH D
webpage (2,.google.ads.googleads.v17.common.WebpageInfoB�AH d
operating_system_version  (2;.google.ads.googleads.v17.common.OperatingSystemVersionInfoB�AH O

mobile_device! (21.google.ads.googleads.v17.common.MobileDeviceInfoB�AH Q
location_group" (22.google.ads.googleads.v17.common.LocationGroupInfoB�AH S
custom_affinity$ (23.google.ads.googleads.v17.common.CustomAffinityInfoB�AH S
custom_audience) (23.google.ads.googleads.v17.common.CustomAudienceInfoB�AH W
combined_audience* (25.google.ads.googleads.v17.common.CombinedAudienceInfoB�AH O

keyword_theme- (21.google.ads.googleads.v17.common.KeywordThemeInfoB�AH T
local_service_id. (23.google.ads.googleads.v17.common.LocalServiceIdInfoB�AH I

brand_list/ (2..google.ads.googleads.v17.common.BrandListInfoB�AH :v�As
*googleads.googleapis.com/CampaignCriterionEcustomers/{customer_id}/campaignCriteria/{campaign_id}~{criterion_id}B
	criterionB
	_campaignB

_criterion_idB

_bid_modifierB
	_negativeB�
&com.google.ads.googleads.v17.resourcesBCampaignCriterionProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3
�1
;google/ads/googleads/v17/resources/ad_group_criterion.proto"google.ads.googleads.v17.resources6google/ads/googleads/v17/common/custom_parameter.protoGgoogle/ads/googleads/v17/enums/ad_group_criterion_approval_status.protoFgoogle/ads/googleads/v17/enums/ad_group_criterion_primary_status.protoMgoogle/ads/googleads/v17/enums/ad_group_criterion_primary_status_reason.proto>google/ads/googleads/v17/enums/ad_group_criterion_status.proto3google/ads/googleads/v17/enums/bidding_source.protoDgoogle/ads/googleads/v17/enums/criterion_system_serving_status.proto3google/ads/googleads/v17/enums/criterion_type.proto9google/ads/googleads/v17/enums/quality_score_bucket.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�)
AdGroupCriterionH

resource_name (	B1�A�A+
)googleads.googleapis.com/AdGroupCriterion
criterion_id8 (B�AH�
display_nameM (	B�Aa
status (2Q.google.ads.googleads.v17.enums.AdGroupCriterionStatusEnum.AdGroupCriterionStatus[
quality_info (<EMAIL>�A?
ad_group9 (	B(�A�A"
 googleads.googleapis.com/AdGroupH�R
type (2?.google.ads.googleads.v17.enums.CriterionTypeEnum.CriterionTypeB�A
negative: (B�AH��
system_serving_status4 (2].google.ads.googleads.v17.enums.CriterionSystemServingStatusEnum.CriterionSystemServingStatusB�A
approval_status5 (2a.google.ads.googleads.v17.enums.AdGroupCriterionApprovalStatusEnum.AdGroupCriterionApprovalStatusB�A 
disapproval_reasons; (	B�AF
labels< (	B6�A�A0
.googleads.googleapis.com/AdGroupCriterionLabel
bid_modifier= (H�
cpc_bid_micros> (H�
cpm_bid_micros? (H�
cpv_bid_micros@ (H�#
percent_cpc_bid_microsA (H�*
effective_cpc_bid_microsB (B�AH	�*
effective_cpm_bid_microsC (B�AH
�*
effective_cpv_bid_microsD (B�AH�2
 effective_percent_cpc_bid_microsE (B�AH�f
effective_cpc_bid_source (2?.google.ads.googleads.v17.enums.BiddingSourceEnum.BiddingSourceB�Af
effective_cpm_bid_source (2?.google.ads.googleads.v17.enums.BiddingSourceEnum.BiddingSourceB�Af
effective_cpv_bid_source (2?.google.ads.googleads.v17.enums.BiddingSourceEnum.BiddingSourceB�An
 effective_percent_cpc_bid_source# (2?.google.ads.googleads.v17.enums.BiddingSourceEnum.BiddingSourceB�Ag
position_estimates
 (2F.google.ads.googleads.v17.resources.AdGroupCriterion.PositionEstimatesB�A

final_urlsF (	
final_mobile_urlsG (	
final_url_suffixH (	H
�"
tracking_url_templateI (	H�O
url_custom_parameters (20.google.ads.googleads.v17.common.CustomParameter�
primary_statusU (2_.google.ads.googleads.v17.enums.AdGroupCriterionPrimaryStatusEnum.AdGroupCriterionPrimaryStatusB�AH��
primary_status_reasonsV (2k.google.ads.googleads.v17.enums.AdGroupCriterionPrimaryStatusReasonEnum.AdGroupCriterionPrimaryStatusReasonB�AD
keyword (2,.google.ads.googleads.v17.common.KeywordInfoB�AH H
	placement (2..google.ads.googleads.v17.common.PlacementInfoB�AH Z
mobile_app_category (26.google.ads.googleads.v17.common.MobileAppCategoryInfoB�AH Y
mobile_application (26.google.ads.googleads.v17.common.MobileApplicationInfoB�AH O

listing_group  (21.google.ads.googleads.v17.common.ListingGroupInfoB�AH G
	age_range$ (2-.google.ads.googleads.v17.common.AgeRangeInfoB�AH B
gender% (2+.google.ads.googleads.v17.common.GenderInfoB�AH M
income_range& (20.google.ads.googleads.v17.common.IncomeRangeInfoB�AH S
parental_status\' (23.google.ads.googleads.v17.common.ParentalStatusInfoB�AH G
	user_list* (2-.google.ads.googleads.v17.common.UserListInfoB�AH O

youtube_video( (21.google.ads.googleads.v17.common.YouTubeVideoInfoB�AH S
youtube_channel) (23.google.ads.googleads.v17.common.YouTubeChannelInfoB�AH @
topic+ (2*.google.ads.googleads.v17.common.TopicInfoB�AH O

user_interest- (21.google.ads.googleads.v17.common.UserInterestInfoB�AH D
webpage. (2,.google.ads.googleads.v17.common.WebpageInfoB�AH V
app_payment_model/ (24.google.ads.googleads.v17.common.AppPaymentModelInfoB�AH S
custom_affinity0 (23.google.ads.googleads.v17.common.CustomAffinityInfoB�AH O

custom_intent1 (21.google.ads.googleads.v17.common.CustomIntentInfoB�AH S
custom_audienceJ (23.google.ads.googleads.v17.common.CustomAudienceInfoB�AH W
combined_audienceK (25.google.ads.googleads.v17.common.CombinedAudienceInfoB�AH F
audienceO (2-.google.ads.googleads.v17.common.AudienceInfoB�AH F
locationR (2-.google.ads.googleads.v17.common.LocationInfoB�AH F
languageS (2-.google.ads.googleads.v17.common.LanguageInfoB�AH �
QualityInfo

quality_score (B�AH �n
creative_quality_score (2I.google.ads.googleads.v17.enums.QualityScoreBucketEnum.QualityScoreBucketB�Ap
post_click_quality_score (2I.google.ads.googleads.v17.enums.QualityScoreBucketEnum.QualityScoreBucketB�Al
search_predicted_ctr (2I.google.ads.googleads.v17.enums.QualityScoreBucketEnum.QualityScoreBucketB�AB
_quality_score�
PositionEstimates\'
first_page_cpc_micros (B�AH �+
first_position_cpc_micros (B�AH�(
top_of_page_cpc_micros (B�AH�<
*estimated_add_clicks_at_first_position_cpc	 (B�AH�:
(estimated_add_cost_at_first_position_cpc
 (B�AH�B
_first_page_cpc_microsB
_first_position_cpc_microsB
_top_of_page_cpc_microsB-
+_estimated_add_clicks_at_first_position_cpcB+
)_estimated_add_cost_at_first_position_cpc:t�Aq
)googleads.googleapis.com/AdGroupCriterionDcustomers/{customer_id}/adGroupCriteria/{ad_group_id}~{criterion_id}B
	criterionB

_criterion_idB
	_ad_groupB
	_negativeB

_bid_modifierB
_cpc_bid_microsB
_cpm_bid_microsB
_cpv_bid_microsB
_percent_cpc_bid_microsB
_effective_cpc_bid_microsB
_effective_cpm_bid_microsB
_effective_cpv_bid_microsB#
!_effective_percent_cpc_bid_microsB
_final_url_suffixB
_tracking_url_templateB
_primary_statusB�
&com.google.ads.googleads.v17.resourcesBAdGroupCriterionProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3
�
6google/ads/googleads/v17/resources/ad_group_feed.proto"google.ads.googleads.v17.resources5google/ads/googleads/v17/enums/feed_link_status.proto5google/ads/googleads/v17/enums/placeholder_type.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�
AdGroupFeedC

resource_name (	B,�A�A&
$googleads.googleapis.com/AdGroupFeed8
feed (	B%�A�A
googleads.googleapis.com/FeedH �?
ad_group (	B(�A�A"
 googleads.googleapis.com/AdGroupH�^
placeholder_types (2C.google.ads.googleads.v17.enums.PlaceholderTypeEnum.PlaceholderTypeL
matching_function (21.google.ads.googleads.v17.common.MatchingFunctionV
status (2A.google.ads.googleads.v17.enums.FeedLinkStatusEnum.FeedLinkStatusB�A:g�Ad
$googleads.googleapis.com/AdGroupFeed<customers/{customer_id}/adGroupFeeds/{ad_group_id}~{feed_id}B
_feedB
	_ad_groupB�
&com.google.ads.googleads.v17.resourcesBAdGroupFeedProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3
�
;google/ads/googleads/v17/enums/asset_set_asset_status.protogoogle.ads.googleads.v17.enums"h
AssetSetAssetStatusEnum"M
AssetSetAssetStatus
UNSPECIFIED 
UNKNOWN
ENABLED
REMOVEDB�
"com.google.ads.googleads.v17.enumsBAssetSetAssetStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
8google/ads/googleads/v17/resources/asset_set_asset.proto"google.ads.googleads.v17.resourcesgoogle/api/field_behavior.protogoogle/api/resource.proto"�

AssetSetAssetE

resource_name (	B.�A�A(
&googleads.googleapis.com/AssetSetAsset<
	asset_set (	B)�A�A#
!googleads.googleapis.com/AssetSet5
asset (	B&�A�A 
googleads.googleapis.com/Asset`
status (2K.google.ads.googleads.v17.enums.AssetSetAssetStatusEnum.AssetSetAssetStatusB�A:m�Aj
&googleads.googleapis.com/AssetSetAsset@customers/{customer_id}/assetSetAssets/{asset_set_id}~{asset_id}B�
&com.google.ads.googleads.v17.resourcesBAssetSetAssetProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3
�	
Igoogle/ads/googleads/v17/enums/feed_item_quality_disapproval_reason.protogoogle.ads.googleads.v17.enums"�
$FeedItemQualityDisapprovalReasonEnum"�
 FeedItemQualityDisapprovalReason
UNSPECIFIED 
UNKNOWN"
PRICE_TABLE_REPETITIVE_HEADERS&
"PRICE_TABLE_REPETITIVE_DESCRIPTION!
PRICE_TABLE_INCONSISTENT_ROWS*
&PRICE_DESCRIPTION_HAS_PRICE_QUALIFIERS
PRICE_UNSUPPORTED_LANGUAGE.
*PRICE_TABLE_ROW_HEADER_TABLE_TYPE_MISMATCH/
+PRICE_TABLE_ROW_HEADER_HAS_PROMOTIONAL_TEXT,
(PRICE_TABLE_ROW_DESCRIPTION_NOT_RELEVANT	4
0PRICE_TABLE_ROW_DESCRIPTION_HAS_PROMOTIONAL_TEXT
1
-PRICE_TABLE_ROW_HEADER_DESCRIPTION_REPETITIVE
PRICE_TABLE_ROW_UNRATEABLE!
PRICE_TABLE_ROW_PRICE_INVALID

PRICE_TABLE_ROW_URL_INVALID)
%PRICE_HEADER_OR_DESCRIPTION_HAS_PRICE.
*STRUCTURED_SNIPPETS_HEADER_POLICY_VIOLATED\'
#STRUCTURED_SNIPPETS_REPEATED_VALUES,
(STRUCTURED_SNIPPETS_EDITORIAL_GUIDELINES,
(STRUCTURED_SNIPPETS_HAS_PROMOTIONAL_TEXTB�
"com.google.ads.googleads.v17.enumsB%FeedItemQualityDisapprovalReasonProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
,google/ads/googleads/v17/common/policy.protogoogle.ads.googleads.v17.commonXgoogle/ads/googleads/v17/enums/policy_topic_evidence_destination_mismatch_url_type.protoYgoogle/ads/googleads/v17/enums/policy_topic_evidence_destination_not_working_device.protoagoogle/ads/googleads/v17/enums/policy_topic_evidence_destination_not_working_dns_error_type.proto"n
PolicyViolationKey
policy_name (	H �
violating_text (	H�B
_policy_nameB
_violating_text"�
PolicyValidationParameter
ignorable_policy_topics (	Y
exempt_policy_violation_keys (23.google.ads.googleads.v17.common.PolicyViolationKey"�
PolicyTopicEntry
topic (	H �[
type (2M.google.ads.googleads.v17.enums.PolicyTopicEntryTypeEnum.PolicyTopicEntryTypeG
	evidences (24.google.ads.googleads.v17.common.PolicyTopicEvidenceK
constraints (26.google.ads.googleads.v17.common.PolicyTopicConstraintB
_topic"�

PolicyTopicEvidenceX
website_list (<EMAIL> R
	text_list (2=.google.ads.googleads.v17.common.PolicyTopicEvidence.TextListH 

language_code	 (	H i
destination_text_list (2H.google.ads.googleads.v17.common.PolicyTopicEvidence.DestinationTextListH h
destination_mismatch (2H.google.ads.googleads.v17.common.PolicyTopicEvidence.DestinationMismatchH m
destination_not_working (2J.google.ads.googleads.v17.common.PolicyTopicEvidence.DestinationNotWorkingH 
TextList
texts (	
WebsiteList
websites (	0
DestinationTextList
destination_texts (	�
DestinationMismatch�
	url_types (2.google.ads.googleads.v17.enums.PolicyTopicEvidenceDestinationMismatchUrlTypeEnum.PolicyTopicEvidenceDestinationMismatchUrlType�
DestinationNotWorking
expanded_url (	H��
device (2�.google.ads.googleads.v17.enums.PolicyTopicEvidenceDestinationNotWorkingDeviceEnum.PolicyTopicEvidenceDestinationNotWorkingDevice#
last_checked_date_time (	H��
dns_error_type (2�.google.ads.googleads.v17.enums.PolicyTopicEvidenceDestinationNotWorkingDnsErrorTypeEnum.PolicyTopicEvidenceDestinationNotWorkingDnsErrorTypeH 
http_error_code (H B
reasonB

_expanded_urlB
_last_checked_date_timeB
value"�
PolicyTopicConstrainto
country_constraint_list (2L.google.ads.googleads.v17.common.PolicyTopicConstraint.CountryConstraintListH h
reseller_constraint (2I.google.ads.googleads.v17.common.PolicyTopicConstraint.ResellerConstraintH {
#certificate_missing_in_country_list (2L.google.ads.googleads.v17.common.PolicyTopicConstraint.CountryConstraintListH �
+certificate_domain_mismatch_in_country_list (2L.google.ads.googleads.v17.common.PolicyTopicConstraint.CountryConstraintListH �
CountryConstraintList%
total_targeted_countries (H �[
	countries (2H.google.ads.googleads.v17.common.PolicyTopicConstraint.CountryConstraintB
_total_targeted_countries
ResellerConstraintI
CountryConstraint
country_criterion (	H �B
_country_criterionB
valueB�
#com.google.ads.googleads.v17.commonBPolicyProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/common;common�GAA�Google.Ads.GoogleAds.V17.Common�Google\\Ads\\GoogleAds\\V17\\Common�#Google::Ads::GoogleAds::V17::Commonbproto3
�
2google/ads/googleads/v17/common/asset_policy.protogoogle.ads.googleads.v17.common>google/ads/googleads/v17/enums/asset_link_primary_status.protoEgoogle/ads/googleads/v17/enums/asset_link_primary_status_reason.protoKgoogle/ads/googleads/v17/enums/asset_offline_evaluation_error_reasons.proto;google/ads/googleads/v17/enums/policy_approval_status.proto9google/ads/googleads/v17/enums/policy_review_status.proto"�
AdAssetPolicySummaryO
policy_topic_entries (21.google.ads.googleads.v17.common.PolicyTopicEntry`

review_status (2I.google.ads.googleads.v17.enums.PolicyReviewStatusEnum.PolicyReviewStatusf
approval_status (2M.google.ads.googleads.v17.enums.PolicyApprovalStatusEnum.PolicyApprovalStatus"�
AssetLinkPrimaryStatusDetailsr
reason (2].google.ads.googleads.v17.enums.AssetLinkPrimaryStatusReasonEnum.AssetLinkPrimaryStatusReasonH�f
status (2Q.google.ads.googleads.v17.enums.AssetLinkPrimaryStatusEnum.AssetLinkPrimaryStatusH�N
asset_disapproved (21.google.ads.googleads.v17.common.AssetDisapprovedH B	
detailsB	
_reasonB	
_status"�
AssetDisapproved�
 offline_evaluation_error_reasons (2i.google.ads.googleads.v17.enums.AssetOfflineEvaluationErrorReasonsEnum.AssetOfflineEvaluationErrorReasonsB�
#com.google.ads.googleads.v17.commonBAssetPolicyProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/common;common�GAA�Google.Ads.GoogleAds.V17.Common�Google\\Ads\\GoogleAds\\V17\\Common�#Google::Ads::GoogleAds::V17::Commonbproto3
�
7google/ads/googleads/v17/resources/customer_asset.proto"google.ads.googleads.v17.resources5google/ads/googleads/v17/enums/asset_field_type.proto>google/ads/googleads/v17/enums/asset_link_primary_status.protoEgoogle/ads/googleads/v17/enums/asset_link_primary_status_reason.proto6google/ads/googleads/v17/enums/asset_link_status.proto1google/ads/googleads/v17/enums/asset_source.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�

CustomerAssetE

resource_name (	B.�A�A(
&googleads.googleapis.com/CustomerAsset8
asset (	B)�A�A�A 
googleads.googleapis.com/Asset]

field_type (2A.google.ads.googleads.v17.enums.AssetFieldTypeEnum.AssetFieldTypeB�A�AP
source (2;.google.ads.googleads.v17.enums.AssetSourceEnum.AssetSourceB�AS
status (2C.google.ads.googleads.v17.enums.AssetLinkStatusEnum.AssetLinkStatusn
primary_status (2Q.google.ads.googleads.v17.enums.AssetLinkPrimaryStatusEnum.AssetLinkPrimaryStatusB�Ac
primary_status_details (2>.google.ads.googleads.v17.common.AssetLinkPrimaryStatusDetailsB�A�
primary_status_reasons (2].google.ads.googleads.v17.enums.AssetLinkPrimaryStatusReasonEnum.AssetLinkPrimaryStatusReasonB�A:k�Ah
&googleads.googleapis.com/CustomerAsset>customers/{customer_id}/customerAssets/{asset_id}~{field_type}B�
&com.google.ads.googleads.v17.resourcesBCustomerAssetProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3
�
7google/ads/googleads/v17/resources/ad_group_asset.proto"google.ads.googleads.v17.resources5google/ads/googleads/v17/enums/asset_field_type.proto>google/ads/googleads/v17/enums/asset_link_primary_status.protoEgoogle/ads/googleads/v17/enums/asset_link_primary_status_reason.proto6google/ads/googleads/v17/enums/asset_link_status.proto1google/ads/googleads/v17/enums/asset_source.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�
AdGroupAssetD

resource_name (	B-�A�A\'
%googleads.googleapis.com/AdGroupAsset=
ad_group (	B+�A�A�A"
 googleads.googleapis.com/AdGroup8
asset (	B)�A�A�A 
googleads.googleapis.com/Asset]

field_type (2A.google.ads.googleads.v17.enums.AssetFieldTypeEnum.AssetFieldTypeB�A�AP
source (2;.google.ads.googleads.v17.enums.AssetSourceEnum.AssetSourceB�AS
status (2C.google.ads.googleads.v17.enums.AssetLinkStatusEnum.AssetLinkStatusn
primary_status (2Q.google.ads.googleads.v17.enums.AssetLinkPrimaryStatusEnum.AssetLinkPrimaryStatusB�Ac
primary_status_details (2>.google.ads.googleads.v17.common.AssetLinkPrimaryStatusDetailsB�A�
primary_status_reasons	 (2].google.ads.googleads.v17.enums.AssetLinkPrimaryStatusReasonEnum.AssetLinkPrimaryStatusReasonB�A:w�At
%googleads.googleapis.com/AdGroupAssetKcustomers/{customer_id}/adGroupAssets/{ad_group_id}~{asset_id}~{field_type}B�
&com.google.ads.googleads.v17.resourcesBAdGroupAssetProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3
�
.google/ads/googleads/v17/common/ad_asset.protogoogle.ads.googleads.v17.common<google/ads/googleads/v17/enums/asset_performance_label.proto<google/ads/googleads/v17/enums/served_asset_field_type.proto"�
AdTextAsset
text (	H �c
pinned_field (2M.google.ads.googleads.v17.enums.ServedAssetFieldTypeEnum.ServedAssetFieldTypep
asset_performance_label (2O.google.ads.googleads.v17.enums.AssetPerformanceLabelEnum.AssetPerformanceLabelR
policy_summary_info (25.google.ads.googleads.v17.common.AdAssetPolicySummaryB
_text",
AdImageAsset
asset (	H �B
_asset"�
AdVideoAsset
asset (	H �S
ad_video_asset_info (21.google.ads.googleads.v17.common.AdVideoAssetInfoH�B
_assetB
_ad_video_asset_info"�
AdVideoAssetInfot
$ad_video_asset_inventory_preferences (2A.google.ads.googleads.v17.common.AdVideoAssetInventoryPreferencesH �B\'
%_ad_video_asset_inventory_preferences"�
 AdVideoAssetInventoryPreferences
in_feed_preference (H �!
in_stream_preference (H�
shorts_preference (H�B
_in_feed_preferenceB
_in_stream_preferenceB
_shorts_preference"2
AdMediaBundleAsset
asset (	H �B
_asset"<
AdDemandGenCarouselCardAsset
asset (	H �B
_asset"3
AdCallToActionAsset
asset (	H �B
_assetB�
#com.google.ads.googleads.v17.commonBAdAssetProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/common;common�GAA�Google.Ads.GoogleAds.V17.Common�Google\\Ads\\GoogleAds\\V17\\Common�#Google::Ads::GoogleAds::V17::Commonbproto3
�
7google/ads/googleads/v17/resources/campaign_asset.proto"google.ads.googleads.v17.resources5google/ads/googleads/v17/enums/asset_field_type.proto>google/ads/googleads/v17/enums/asset_link_primary_status.protoEgoogle/ads/googleads/v17/enums/asset_link_primary_status_reason.proto6google/ads/googleads/v17/enums/asset_link_status.proto1google/ads/googleads/v17/enums/asset_source.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�

CampaignAssetE

resource_name (	B.�A�A(
&googleads.googleapis.com/CampaignAsset@
campaign (	B)�A�A#
!googleads.googleapis.com/CampaignH �:
asset (	B&�A�A 
googleads.googleapis.com/AssetH�Z

field_type (2A.google.ads.googleads.v17.enums.AssetFieldTypeEnum.AssetFieldTypeB�AP
source (2;.google.ads.googleads.v17.enums.AssetSourceEnum.AssetSourceB�AS
status (2C.google.ads.googleads.v17.enums.AssetLinkStatusEnum.AssetLinkStatusn
primary_status	 (2Q.google.ads.googleads.v17.enums.AssetLinkPrimaryStatusEnum.AssetLinkPrimaryStatusB�Ac
primary_status_details
 (2>.google.ads.googleads.v17.common.AssetLinkPrimaryStatusDetailsB�A�
primary_status_reasons (2].google.ads.googleads.v17.enums.AssetLinkPrimaryStatusReasonEnum.AssetLinkPrimaryStatusReasonB�A:y�Av
&googleads.googleapis.com/CampaignAssetLcustomers/{customer_id}/campaignAssets/{campaign_id}~{asset_id}~{field_type}B
	_campaignB
_assetB�
&com.google.ads.googleads.v17.resourcesBCampaignAssetProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3
�
2google/ads/googleads/v17/resources/feed_item.proto"google.ads.googleads.v17.resources1google/ads/googleads/v17/common/feed_common.proto,google/ads/googleads/v17/common/policy.protoFgoogle/ads/googleads/v17/enums/feed_item_quality_approval_status.protoIgoogle/ads/googleads/v17/enums/feed_item_quality_disapproval_reason.proto5google/ads/googleads/v17/enums/feed_item_status.proto@google/ads/googleads/v17/enums/feed_item_validation_status.proto>google/ads/googleads/v17/enums/geo_targeting_restriction.proto5google/ads/googleads/v17/enums/placeholder_type.proto;google/ads/googleads/v17/enums/policy_approval_status.proto9google/ads/googleads/v17/enums/policy_review_status.proto@google/ads/googleads/v17/errors/feed_item_validation_error.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�
FeedItem@

resource_name (	B)�A�A#
!googleads.googleapis.com/FeedItem8
feed (	B%�A�A
googleads.googleapis.com/FeedH �
id (B�AH�
start_date_time
 (	H�

end_date_time (	H�T
attribute_values (2:.google.ads.googleads.v17.resources.FeedItemAttributeValuev
geo_targeting_restriction (2S.google.ads.googleads.v17.enums.GeoTargetingRestrictionEnum.GeoTargetingRestrictionO
url_custom_parameters (20.google.ads.googleads.v17.common.CustomParameterV
status	 (2A.google.ads.googleads.v17.enums.FeedItemStatusEnum.FeedItemStatusB�A\\
policy_infos
 (2A.google.ads.googleads.v17.resources.FeedItemPlaceholderPolicyInfoB�A:b�A_
!googleads.googleapis.com/FeedItem:customers/{customer_id}/feedItems/{feed_id}~{feed_item_id}B
_feedB
_idB
_start_date_timeB
_end_date_time"�
FeedItemAttributeValue
feed_attribute_id (H �

integer_value (H�

boolean_value
 (H�
string_value (	H�
double_value (H�;
price_value (2&.google.ads.googleads.v17.common.Money
integer_values (
boolean_values (

string_values (	

double_values (B
_feed_attribute_idB
_integer_valueB
_boolean_valueB

_string_valueB

_double_value"�
FeedItemPlaceholderPolicyInfog
placeholder_type_enum
 (2C.google.ads.googleads.v17.enums.PlaceholderTypeEnum.PlaceholderTypeB�A,
feed_mapping_resource_name (	B�AH �e

review_status (2I.google.ads.googleads.v17.enums.PolicyReviewStatusEnum.PolicyReviewStatusB�Ak
approval_status (2M.google.ads.googleads.v17.enums.PolicyApprovalStatusEnum.PolicyApprovalStatusB�AT
policy_topic_entries (21.google.ads.googleads.v17.common.PolicyTopicEntryB�Au
validation_status (2U.google.ads.googleads.v17.enums.FeedItemValidationStatusEnum.FeedItemValidationStatusB�A[
validation_errors (2;.google.ads.googleads.v17.resources.FeedItemValidationErrorB�A�
quality_approval_status (2_.google.ads.googleads.v17.enums.FeedItemQualityApprovalStatusEnum.FeedItemQualityApprovalStatusB�A�
quality_disapproval_reasons	 (2e.google.ads.googleads.v17.enums.FeedItemQualityDisapprovalReasonEnum.FeedItemQualityDisapprovalReasonB�AB
_feed_mapping_resource_name"�
FeedItemValidationErrors
validation_error (2T.google.ads.googleads.v17.errors.FeedItemValidationErrorEnum.FeedItemValidationErrorB�A
description (	B�AH �
feed_attribute_ids (B�A

extra_info (	B�AH�B
_descriptionB
_extra_infoB�
&com.google.ads.googleads.v17.resourcesB
FeedItemProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3
�
1google/ads/googleads/v17/resources/ad_group.proto"google.ads.googleads.v17.resources7google/ads/googleads/v17/common/targeting_setting.proto>google/ads/googleads/v17/enums/ad_group_ad_rotation_mode.proto<google/ads/googleads/v17/enums/ad_group_primary_status.protoCgoogle/ads/googleads/v17/enums/ad_group_primary_status_reason.proto4google/ads/googleads/v17/enums/ad_group_status.proto2google/ads/googleads/v17/enums/ad_group_type.proto5google/ads/googleads/v17/enums/asset_field_type.proto3google/ads/googleads/v17/enums/asset_set_type.proto3google/ads/googleads/v17/enums/bidding_source.proto8google/ads/googleads/v17/enums/targeting_dimension.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�
AdGroup?

resource_name (	B(�A�A"
 googleads.googleapis.com/AdGroup
id" (B�AH �
name# (	H�O
status (2?.google.ads.googleads.v17.enums.AdGroupStatusEnum.AdGroupStatusN
type (2;.google.ads.googleads.v17.enums.AdGroupTypeEnum.AdGroupTypeB�Ai
ad_rotation_mode (2O.google.ads.googleads.v17.enums.AdGroupAdRotationModeEnum.AdGroupAdRotationModeD

base_ad_group$ (	B(�A�A"
 googleads.googleapis.com/AdGroupH�"
tracking_url_template% (	H�O
url_custom_parameters (20.google.ads.googleads.v17.common.CustomParameter@
campaign& (	B)�A�A#
!googleads.googleapis.com/CampaignH�
cpc_bid_micros\' (H�*
effective_cpc_bid_micros9 (B�AH�
cpm_bid_micros( (H�
target_cpa_micros) (H�
cpv_bid_micros* (H	�
target_cpm_micros+ (H
�
target_roas, (H�#
percent_cpc_bid_micros- (H�#
optimized_targeting_enabled; (o
display_custom_bid_dimension (2I.google.ads.googleads.v17.enums.TargetingDimensionEnum.TargetingDimension
final_url_suffix. (	H
�L
targeting_setting (21.google.ads.googleads.v17.common.TargetingSettingZ
audience_setting8 (2;.google.ads.googleads.v17.resources.AdGroup.AudienceSettingB�A-
effective_target_cpa_micros/ (B�AH�i
effective_target_cpa_source (2?.google.ads.googleads.v17.enums.BiddingSourceEnum.BiddingSourceB�A\'
effective_target_roas0 (B�AH�j
effective_target_roas_source  (2?.google.ads.googleads.v17.enums.BiddingSourceEnum.BiddingSourceB�A=
labels1 (	B-�A�A\'
%googleads.googleapis.com/AdGroupLabell
!excluded_parent_asset_field_types6 (2A.google.ads.googleads.v17.enums.AssetFieldTypeEnum.AssetFieldTypef
excluded_parent_asset_set_types: (2=.google.ads.googleads.v17.enums.AssetSetTypeEnum.AssetSetTypej
primary_status> (2M.google.ads.googleads.v17.enums.AdGroupPrimaryStatusEnum.AdGroupPrimaryStatusB�A~
primary_status_reasons? (2Y.google.ads.googleads.v17.enums.AdGroupPrimaryStatusReasonEnum.AdGroupPrimaryStatusReasonB�A4
AudienceSetting!
use_audience_grouped (B�A:U�AR
 googleads.googleapis.com/AdGroup.customers/{customer_id}/adGroups/{ad_group_id}B
_idB
_nameB
_base_ad_groupB
_tracking_url_templateB
	_campaignB
_cpc_bid_microsB
_effective_cpc_bid_microsB
_cpm_bid_microsB
_target_cpa_microsB
_cpv_bid_microsB
_target_cpm_microsB
_target_roasB
_percent_cpc_bid_microsB
_final_url_suffixB
_effective_target_cpa_microsB
_effective_target_roasB�
&com.google.ads.googleads.v17.resourcesBAdGroupProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3
�
.google/ads/googleads/v17/enums/mime_type.protogoogle.ads.googleads.v17.enums"�
MimeTypeEnum"�
MimeType
UNSPECIFIED 
UNKNOWN

IMAGE_JPEG
	IMAGE_GIF
	IMAGE_PNG	
FLASH
	TEXT_HTML
PDF

MSWORD
MSEXCEL	
RTF

	AUDIO_WAV
	AUDIO_MP3
HTML5_AD_ZIP
B�
"com.google.ads.googleads.v17.enumsB
MimeTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�S
3google/ads/googleads/v17/common/ad_type_infos.protogoogle.ads.googleads.v17.commonDgoogle/ads/googleads/v17/enums/call_conversion_reporting_state.proto>google/ads/googleads/v17/enums/display_ad_format_setting.proto@google/ads/googleads/v17/enums/display_upload_product_type.protoDgoogle/ads/googleads/v17/enums/legacy_app_install_ad_app_store.proto.google/ads/googleads/v17/enums/mime_type.proto4google/ads/googleads/v17/enums/video_thumbnail.protogoogle/api/field_behavior.proto"�

TextAdInfo
headline (	H �
description1 (	H�
description2 (	H�B
	_headlineB

_description1B

_description2"�
ExpandedTextAdInfo
headline_part1 (	H �
headline_part2	 (	H�
headline_part3
 (	H�
description (	H�
description2 (	H�
path1
 (	H�
path2 (	H�B
_headline_part1B
_headline_part2B
_headline_part3B
_descriptionB

_description2B
_path1B
_path2"s
ExpandedDynamicSearchAdInfo
description (	H �
description2 (	H�B
_descriptionB

_description2"
HotelAdInfo"
TravelAdInfo"
ShoppingSmartAdInfo"
ShoppingProductAdInfo"E
ShoppingComparisonListingAdInfo
headline (	H �B
	_headline"�
ImageAdInfo
pixel_width (H�
pixel_height (H�
	image_url (	H� 
preview_pixel_width (H�!
preview_pixel_height (H�
preview_image_url (	H�H
	mime_type
 (25.google.ads.googleads.v17.enums.MimeTypeEnum.MimeType
name (	H�D
image_asset (2-.google.ads.googleads.v17.common.AdImageAssetH 
data
 (H "
ad_id_to_copy_image_from (H B
imageB
_pixel_widthB

_pixel_heightB

_image_urlB
_preview_pixel_widthB
_preview_pixel_heightB
_preview_image_urlB
_name"�
VideoBumperInStreamAdInfoG
companion_banner (2-.google.ads.googleads.v17.common.AdImageAsset
action_button_label (	
action_headline (	"�
VideoNonSkippableInStreamAdInfoG
companion_banner (2-.google.ads.googleads.v17.common.AdImageAsset
action_button_label (	
action_headline (	"�
VideoTrueViewInStreamAdInfo
action_button_label (	
action_headline (	G
companion_banner (2-.google.ads.googleads.v17.common.AdImageAsset"=
VideoOutstreamAdInfo
headline (	
description (	"�
InFeedVideoAdInfo
headline (	
description1 (	
description2 (	T
	thumbnail (2A.google.ads.googleads.v17.enums.VideoThumbnailEnum.VideoThumbnail"�
VideoAdInfo<
video (2-.google.ads.googleads.v17.common.AdVideoAssetQ
	in_stream (2<.google.ads.googleads.v17.common.VideoTrueViewInStreamAdInfoH L
bumper (2:.google.ads.googleads.v17.common.VideoBumperInStreamAdInfoH K

out_stream (25.google.ads.googleads.v17.common.VideoOutstreamAdInfoH Y

non_skippable (<EMAIL> E
in_feed	 (22.google.ads.googleads.v17.common.InFeedVideoAdInfoH B
format"�
VideoResponsiveAdInfo?
	headlines (2,.google.ads.googleads.v17.common.AdTextAssetD
long_headlines (2,.google.ads.googleads.v17.common.AdTextAssetB
descriptions (2,.google.ads.googleads.v17.common.AdTextAssetE
call_to_actions (2,.google.ads.googleads.v17.common.AdTextAsset=
videos (2-.google.ads.googleads.v17.common.AdVideoAssetH
companion_banners (2-.google.ads.googleads.v17.common.AdImageAsset
breadcrumb1 (	
breadcrumb2 (	"�
ResponsiveSearchAdInfo?
	headlines (2,.google.ads.googleads.v17.common.AdTextAssetB
descriptions (2,.google.ads.googleads.v17.common.AdTextAsset
path1 (	H �
path2 (	H�B
_path1B
_path2"�
LegacyResponsiveDisplayAdInfo
short_headline (	H �

long_headline (	H�
description (	H�

business_name (	H�!
allow_flexible_color (H�
accent_color (	H�

main_color (	H� 
call_to_action_text (	H�

logo_image (	H�
square_logo_image (	H	�
marketing_image (	H
�#
square_marketing_image (	H�i
format_setting
 (2Q.google.ads.googleads.v17.enums.DisplayAdFormatSettingEnum.DisplayAdFormatSetting
price_prefix (	H�

promo_text (	H
�B
_short_headlineB
_long_headlineB
_descriptionB
_business_nameB
_allow_flexible_colorB

_accent_colorB
_main_colorB
_call_to_action_textB
_logo_imageB
_square_logo_imageB
_marketing_imageB
_square_marketing_imageB

_price_prefixB
_promo_text"�
	AppAdInfoG
mandatory_ad_text (2,.google.ads.googleads.v17.common.AdTextAsset?
	headlines (2,.google.ads.googleads.v17.common.AdTextAssetB
descriptions (2,.google.ads.googleads.v17.common.AdTextAsset=
images (2-.google.ads.googleads.v17.common.AdImageAssetE
youtube_videos (2-.google.ads.googleads.v17.common.AdVideoAssetP
html5_media_bundles (23.google.ads.googleads.v17.common.AdMediaBundleAsset"�
AppEngagementAdInfo?
	headlines (2,.google.ads.googleads.v17.common.AdTextAssetB
descriptions (2,.google.ads.googleads.v17.common.AdTextAsset=
images (2-.google.ads.googleads.v17.common.AdImageAsset=
videos (2-.google.ads.googleads.v17.common.AdVideoAsset"�
AppPreRegistrationAdInfo?
	headlines (2,.google.ads.googleads.v17.common.AdTextAssetB
descriptions (2,.google.ads.googleads.v17.common.AdTextAsset=
images (2-.google.ads.googleads.v17.common.AdImageAssetE
youtube_videos (2-.google.ads.googleads.v17.common.AdVideoAsset"�
LegacyAppInstallAdInfo
app_id (	H �l
	app_store (2Y.google.ads.googleads.v17.enums.LegacyAppInstallAdAppStoreEnum.LegacyAppInstallAdAppStore
headline (	H�
description1 (	H�
description2	 (	H�B	
_app_idB
	_headlineB

_description1B

_description2"�
ResponsiveDisplayAdInfoG
marketing_images (2-.google.ads.googleads.v17.common.AdImageAssetN
square_marketing_images (2-.google.ads.googleads.v17.common.AdImageAssetB
logo_images (2-.google.ads.googleads.v17.common.AdImageAssetI
square_logo_images (2-.google.ads.googleads.v17.common.AdImageAsset?
	headlines (2,.google.ads.googleads.v17.common.AdTextAssetC

long_headline (2,.google.ads.googleads.v17.common.AdTextAssetB
descriptions (2,.google.ads.googleads.v17.common.AdTextAssetE
youtube_videos (2-.google.ads.googleads.v17.common.AdVideoAsset

business_name (	H �

main_color (	H�
accent_color (	H�!
allow_flexible_color (H� 
call_to_action_text (	H�
price_prefix (	H�

promo_text (	H�i
format_setting (2Q.google.ads.googleads.v17.enums.DisplayAdFormatSettingEnum.DisplayAdFormatSettingU
control_spec (2?.google.ads.googleads.v17.common.ResponsiveDisplayAdControlSpecB
_business_nameB
_main_colorB

_accent_colorB
_allow_flexible_colorB
_call_to_action_textB

_price_prefixB
_promo_text"�
LocalAdInfo?
	headlines (2,.google.ads.googleads.v17.common.AdTextAssetB
descriptions (2,.google.ads.googleads.v17.common.AdTextAssetE
call_to_actions (2,.google.ads.googleads.v17.common.AdTextAssetG
marketing_images (2-.google.ads.googleads.v17.common.AdImageAssetB
logo_images (2-.google.ads.googleads.v17.common.AdImageAsset=
videos (2-.google.ads.googleads.v17.common.AdVideoAsset
path1	 (	H �
path2
 (	H�B
_path1B
_path2"�
DisplayUploadAdInfoz
display_upload_product_type (2U.google.ads.googleads.v17.enums.DisplayUploadProductTypeEnum.DisplayUploadProductTypeK
media_bundle (23.google.ads.googleads.v17.common.AdMediaBundleAssetH B
media_asset"a
ResponsiveDisplayAdControlSpec!
enable_asset_enhancements (
enable_autogen_video ("�
SmartCampaignAdInfo?
	headlines (2,.google.ads.googleads.v17.common.AdTextAssetB
descriptions (2,.google.ads.googleads.v17.common.AdTextAsset"�

CallAdInfo
country_code (	
phone_number (	

business_name (	
	headline1 (	
	headline2 (	
description1 (	
description2 (	
call_tracked (
disable_call_conversion (%
phone_number_verification_url (	
conversion_action	 (	�
conversion_reporting_state
 (2].google.ads.googleads.v17.enums.CallConversionReportingStateEnum.CallConversionReportingState
path1
 (	
path2 (	"�
DemandGenMultiAssetAdInfoG
marketing_images (2-.google.ads.googleads.v17.common.AdImageAssetN
square_marketing_images (2-.google.ads.googleads.v17.common.AdImageAssetP
portrait_marketing_images (2-.google.ads.googleads.v17.common.AdImageAssetB
logo_images (2-.google.ads.googleads.v17.common.AdImageAsset?
	headlines (2,.google.ads.googleads.v17.common.AdTextAssetB
descriptions (2,.google.ads.googleads.v17.common.AdTextAsset

business_name (	H � 
call_to_action_text (	H�
lead_form_only	 (H�B
_business_nameB
_call_to_action_textB
_lead_form_only"�
DemandGenCarouselAdInfo

business_name (	B�AF

logo_image (2-.google.ads.googleads.v17.common.AdImageAssetB�AC
headline (2,.google.ads.googleads.v17.common.AdTextAssetB�AF
description (2,.google.ads.googleads.v17.common.AdTextAssetB�A
call_to_action_text (	Z
carousel_cards (2=.google.ads.googleads.v17.common.AdDemandGenCarouselCardAssetB�A"�
DemandGenVideoResponsiveAdInfo?
	headlines (2,.google.ads.googleads.v17.common.AdTextAssetD
long_headlines (2,.google.ads.googleads.v17.common.AdTextAssetB
descriptions (2,.google.ads.googleads.v17.common.AdTextAsset=
videos (2-.google.ads.googleads.v17.common.AdVideoAssetB
logo_images (2-.google.ads.googleads.v17.common.AdImageAsset
breadcrumb1 (	
breadcrumb2 (	H

business_name (2,.google.ads.googleads.v17.common.AdTextAssetB�AM
call_to_actions	 (24.google.ads.googleads.v17.common.AdCallToActionAsset"�
DemandGenProductAdInfoH
headline (2,.google.ads.googleads.v17.common.AdTextAssetB�AH �K
description (2,.google.ads.googleads.v17.common.AdTextAssetB�AH�K

logo_image (2-.google.ads.googleads.v17.common.AdImageAssetB�AH�
breadcrumb1 (	
breadcrumb2 (	H

business_name (2,.google.ads.googleads.v17.common.AdTextAssetB�AQ
call_to_action (24.google.ads.googleads.v17.common.AdCallToActionAssetH�B
	_headlineB
_descriptionB
_logo_imageB
_call_to_actionB�
#com.google.ads.googleads.v17.commonBAdTypeInfosProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/common;common�GAA�Google.Ads.GoogleAds.V17.Common�Google\\Ads\\GoogleAds\\V17\\Common�#Google::Ads::GoogleAds::V17::Commonbproto3
�
/google/ads/googleads/v17/enums/asset_type.protogoogle.ads.googleads.v17.enums"�

AssetTypeEnum"�
	AssetType
UNSPECIFIED 
UNKNOWN

YOUTUBE_VIDEO
MEDIA_BUNDLE	
IMAGE
TEXT
	LEAD_FORM
BOOK_ON_GOOGLE
	PROMOTION
CALLOUT	
STRUCTURED_SNIPPET

SITELINK
	PAGE_FEED
DYNAMIC_EDUCATION


MOBILE_APP

HOTEL_CALLOUT
CALL	
PRICE
CALL_TO_ACTION
DYNAMIC_REAL_ESTATE
DYNAMIC_CUSTOM
DYNAMIC_HOTELS_AND_RENTALS
DYNAMIC_FLIGHTS
DISCOVERY_CAROUSEL_CARD
DYNAMIC_TRAVEL

DYNAMIC_LOCAL
DYNAMIC_JOBS
LOCATION
HOTEL_PROPERTYB�
"com.google.ads.googleads.v17.enumsBAssetTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
+google/ads/googleads/v17/resources/ad.proto"google.ads.googleads.v17.resources6google/ads/googleads/v17/common/custom_parameter.proto3google/ads/googleads/v17/common/final_app_url.proto4google/ads/googleads/v17/common/url_collection.proto,google/ads/googleads/v17/enums/ad_type.proto+google/ads/googleads/v17/enums/device.protoAgoogle/ads/googleads/v17/enums/system_managed_entity_source.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�
Ad:

resource_name% (	B#�A�A
googleads.googleapis.com/Ad
id( (B�AH�

final_urls) (	D
final_app_urls# (2,.google.ads.googleads.v17.common.FinalAppUrl
final_mobile_urls* (	"
tracking_url_template+ (	H�
final_url_suffix, (	H�O
url_custom_parameters
 (20.google.ads.googleads.v17.common.CustomParameter
display_url- (	H�D
type (21.google.ads.googleads.v17.enums.AdTypeEnum.AdTypeB�A%
added_by_google_ads. (B�AH�L
device_preference (21.google.ads.googleads.v17.enums.DeviceEnum.DeviceG
url_collections (2..google.ads.googleads.v17.common.UrlCollection
name/ (	B�AH��
system_managed_resource_source (2[.google.ads.googleads.v17.enums.SystemManagedResourceSourceEnum.SystemManagedResourceSourceB�AC
text_ad (2+.google.ads.googleads.v17.common.TextAdInfoB�AH O
expanded_text_ad (23.google.ads.googleads.v17.common.ExpandedTextAdInfoH >
call_ad1 (2+.google.ads.googleads.v17.common.CallAdInfoH g
expanded_dynamic_search_ad (2<.google.ads.googleads.v17.common.ExpandedDynamicSearchAdInfoB�AH @
hotel_ad (2,.google.ads.googleads.v17.common.HotelAdInfoH Q
shopping_smart_ad (24.google.ads.googleads.v17.common.ShoppingSmartAdInfoH U
shopping_product_ad (26.google.ads.googleads.v17.common.ShoppingProductAdInfoH E
image_ad (2,.google.ads.googleads.v17.common.ImageAdInfoB�AH @
video_ad (2,.google.ads.googleads.v17.common.VideoAdInfoH U
video_responsive_ad\' (26.google.ads.googleads.v17.common.VideoResponsiveAdInfoH W
responsive_search_ad (27.google.ads.googleads.v17.common.ResponsiveSearchAdInfoH f
legacy_responsive_display_ad (2>.google.ads.googleads.v17.common.LegacyResponsiveDisplayAdInfoH <
app_ad (2*.google.ads.googleads.v17.common.AppAdInfoH ]
legacy_app_install_ad (27.google.ads.googleads.v17.common.LegacyAppInstallAdInfoB�AH Y
responsive_display_ad (28.google.ads.googleads.v17.common.ResponsiveDisplayAdInfoH @
local_ad  (2,.google.ads.googleads.v17.common.LocalAdInfoH Q
display_upload_ad! (24.google.ads.googleads.v17.common.DisplayUploadAdInfoH Q
app_engagement_ad" (24.google.ads.googleads.v17.common.AppEngagementAdInfoH j
shopping_comparison_listing_ad$ (<EMAIL> Q
smart_campaign_ad0 (24.google.ads.googleads.v17.common.SmartCampaignAdInfoH \\
app_pre_registration_ad2 (29.google.ads.googleads.v17.common.AppPreRegistrationAdInfoH _
demand_gen_multi_asset_ad> (2:.google.ads.googleads.v17.common.DemandGenMultiAssetAdInfoH Z
demand_gen_carousel_ad? (28.google.ads.googleads.v17.common.DemandGenCarouselAdInfoH i
demand_gen_video_responsive_ad@ (2?.google.ads.googleads.v17.common.DemandGenVideoResponsiveAdInfoH X
demand_gen_product_ad= (27.google.ads.googleads.v17.common.DemandGenProductAdInfoH B
	travel_ad6 (2-.google.ads.googleads.v17.common.TravelAdInfoH :E�AB
googleads.googleapis.com/Ad#customers/{customer_id}/ads/{ad_id}B	
ad_dataB
_idB
_tracking_url_templateB
_final_url_suffixB
_display_urlB
_added_by_google_adsB
_nameB�
&com.google.ads.googleads.v17.resourcesBAdProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3
�
4google/ads/googleads/v17/resources/ad_group_ad.proto"google.ads.googleads.v17.resources?google/ads/googleads/v17/enums/ad_group_ad_primary_status.protoFgoogle/ads/googleads/v17/enums/ad_group_ad_primary_status_reason.proto7google/ads/googleads/v17/enums/ad_group_ad_status.proto0google/ads/googleads/v17/enums/ad_strength.proto;google/ads/googleads/v17/enums/policy_approval_status.proto9google/ads/googleads/v17/enums/policy_review_status.proto+google/ads/googleads/v17/resources/ad.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�
	AdGroupAdA

resource_name (	B*�A�A$
"googleads.googleapis.com/AdGroupAdS
status (2C.google.ads.googleads.v17.enums.AdGroupAdStatusEnum.AdGroupAdStatus?
ad_group	 (	B(�A�A"
 googleads.googleapis.com/AdGroupH �7
ad (2&.google.ads.googleads.v17.resources.AdB�AW
policy_summary (2:.google.ads.googleads.v17.resources.AdGroupAdPolicySummaryB�AS
ad_strength (29.google.ads.googleads.v17.enums.AdStrengthEnum.AdStrengthB�A
action_items
 (	B�A?
labels
 (	B/�A�A)
\'googleads.googleapis.com/AdGroupAdLabeln
primary_status (2Q.google.ads.googleads.v17.enums.AdGroupAdPrimaryStatusEnum.AdGroupAdPrimaryStatusB�A�
primary_status_reasons (2].google.ads.googleads.v17.enums.AdGroupAdPrimaryStatusReasonEnum.AdGroupAdPrimaryStatusReasonB�A:a�A^
"googleads.googleapis.com/AdGroupAd8customers/{customer_id}/adGroupAds/{ad_group_id}~{ad_id}B
	_ad_group"�
AdGroupAdPolicySummaryT
policy_topic_entries (21.google.ads.googleads.v17.common.PolicyTopicEntryB�Ae

review_status (2I.google.ads.googleads.v17.enums.PolicyReviewStatusEnum.PolicyReviewStatusB�Ak
approval_status (2M.google.ads.googleads.v17.enums.PolicyApprovalStatusEnum.PolicyApprovalStatusB�AB�
&com.google.ads.googleads.v17.resourcesBAdGroupAdProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3
�
Jgoogle/ads/googleads/v17/enums/promotion_extension_discount_modifier.protogoogle.ads.googleads.v17.enums"w
&PromotionExtensionDiscountModifierEnum"M
"PromotionExtensionDiscountModifier
UNSPECIFIED 
UNKNOWN	
UP_TOB�
"com.google.ads.googleads.v17.enumsB\'PromotionExtensionDiscountModifierProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�N
1google/ads/googleads/v17/common/asset_types.protogoogle.ads.googleads.v17.common1google/ads/googleads/v17/common/feed_common.protoDgoogle/ads/googleads/v17/enums/call_conversion_reporting_state.proto8google/ads/googleads/v17/enums/call_to_action_type.protoBgoogle/ads/googleads/v17/enums/lead_form_call_to_action_type.proto=google/ads/googleads/v17/enums/lead_form_desired_intent.protoDgoogle/ads/googleads/v17/enums/lead_form_field_user_input_type.protoNgoogle/ads/googleads/v17/enums/lead_form_post_submit_call_to_action_type.proto<google/ads/googleads/v17/enums/location_ownership_type.proto.google/ads/googleads/v17/enums/mime_type.proto6google/ads/googleads/v17/enums/mobile_app_vendor.protoDgoogle/ads/googleads/v17/enums/price_extension_price_qualifier.proto?google/ads/googleads/v17/enums/price_extension_price_unit.proto9google/ads/googleads/v17/enums/price_extension_type.protoJgoogle/ads/googleads/v17/enums/promotion_extension_discount_modifier.protoAgoogle/ads/googleads/v17/enums/promotion_extension_occasion.protogoogle/api/field_behavior.protogoogle/api/resource.proto"d
YoutubeVideoAsset
youtube_video_id (	H �
youtube_video_title (	B
_youtube_video_id".
MediaBundleAsset
data (H �B
_data"�

ImageAsset
data (H �
	file_size (H�H
	mime_type (25.google.ads.googleads.v17.enums.MimeTypeEnum.MimeTypeB
	full_size (2/.google.ads.googleads.v17.common.ImageDimensionB
_dataB

_file_size"�
ImageDimension

height_pixels (H �
width_pixels (H�
url (	H�B
_height_pixelsB

_width_pixelsB
_url"\'
	TextAsset
text (	H �B
_text"�

LeadFormAsset

business_name
 (	B�Aw
call_to_action_type (2U.google.ads.googleads.v17.enums.LeadFormCallToActionTypeEnum.LeadFormCallToActionTypeB�A\'
call_to_action_description (	B�A
headline (	B�A
description
 (	B�A
privacy_policy_url (	B�A!
post_submit_headline (	H �$
post_submit_description (	H�>
fields (2..google.ads.googleads.v17.common.LeadFormField\\
custom_question_fields (2<.google.ads.googleads.v17.common.LeadFormCustomQuestionFieldQ
delivery_methods	 (27.google.ads.googleads.v17.common.LeadFormDeliveryMethod�
post_submit_call_to_action_type (2i.google.ads.googleads.v17.enums.LeadFormPostSubmitCallToActionTypeEnum.LeadFormPostSubmitCallToActionType#
background_image_asset (	H�g
desired_intent (2O.google.ads.googleads.v17.enums.LeadFormDesiredIntentEnum.LeadFormDesiredIntent
custom_disclosure (	H�B
_post_submit_headlineB
_post_submit_descriptionB
_background_image_assetB
_custom_disclosure"�

LeadFormFieldm

input_type (2Y.google.ads.googleads.v17.enums.LeadFormFieldUserInputTypeEnum.LeadFormFieldUserInputType]
single_choice_answers (2<.google.ads.googleads.v17.common.LeadFormSingleChoiceAnswersH 
has_location_answer (H B	
answers"�
LeadFormCustomQuestionField
custom_question_text (	]
single_choice_answers (2<.google.ads.googleads.v17.common.LeadFormSingleChoiceAnswersH 
has_location_answer (H B	
answers".
LeadFormSingleChoiceAnswers
answers (	"q
LeadFormDeliveryMethodC
webhook (20.google.ads.googleads.v17.common.WebhookDeliveryH B
delivery_details"�
WebhookDelivery#
advertiser_webhook_url (	H �

google_secret (	H�#
payload_schema_version (H�B
_advertiser_webhook_urlB
_google_secretB
_payload_schema_version"
BookOnGoogleAsset"�
PromotionAsset
promotion_target (	B�A�
discount_modifier (2i.google.ads.googleads.v17.enums.PromotionExtensionDiscountModifierEnum.PromotionExtensionDiscountModifier
redemption_start_date (	
redemption_end_date (	k
occasion	 (2Y.google.ads.googleads.v17.enums.PromotionExtensionOccasionEnum.PromotionExtensionOccasion

language_code
 (	

start_date (	
end_date (	L
ad_schedule_targets
 (2/.google.ads.googleads.v17.common.AdScheduleInfo
percent_off (H B
money_amount_off (2&.google.ads.googleads.v17.common.MoneyH 
promotion_code (	HD
orders_over_amount (2&.google.ads.googleads.v17.common.MoneyHB

discount_typeB
promotion_trigger"�
CalloutAsset
callout_text (	B�A

start_date (	
end_date (	L
ad_schedule_targets (2/.google.ads.googleads.v17.common.AdScheduleInfo"B
StructuredSnippetAsset
header (	B�A
values (	B�A"�

SitelinkAsset
	link_text (	B�A
description1 (	
description2 (	

start_date (	
end_date (	L
ad_schedule_targets (2/.google.ads.googleads.v17.common.AdScheduleInfo"6

PageFeedAsset
page_url (	B�A
labels (	"�
DynamicEducationAsset

program_id (	B�A
location_id (	
program_name (	B�A
subject (	
program_description (	
school_name (	
address (	
contextual_keywords (	
android_app_link	 (	
similar_program_ids
 (	
ios_app_link (	
ios_app_store_id (
thumbnail_image_url
 (	
	image_url (	"�
MobileAppAsset
app_id (	B�A[
	app_store (2C.google.ads.googleads.v17.enums.MobileAppVendorEnum.MobileAppVendorB�A
	link_text (	B�A

start_date (	
end_date (	"B
HotelCalloutAsset
text (	B�A

language_code (	B�A"�
	CallAsset
country_code (	B�A
phone_number (	B�A�
call_conversion_reporting_state (2].google.ads.googleads.v17.enums.CallConversionReportingStateEnum.CallConversionReportingStateN
call_conversion_action (	B.�A+
)googleads.googleapis.com/ConversionActionL
ad_schedule_targets (2/.google.ads.googleads.v17.common.AdScheduleInfo"�

PriceAsset\\
type (2I.google.ads.googleads.v17.enums.PriceExtensionTypeEnum.PriceExtensionTypeB�Av
price_qualifier (2].google.ads.googleads.v17.enums.PriceExtensionPriceQualifierEnum.PriceExtensionPriceQualifier

language_code (	B�AG
price_offerings (2..google.ads.googleads.v17.common.PriceOffering"�

PriceOffering
header (	B�A
description (	B�A:
price (2&.google.ads.googleads.v17.common.MoneyB�Aa
unit (2S.google.ads.googleads.v17.enums.PriceExtensionPriceUnitEnum.PriceExtensionPriceUnit
	final_url (	B�A
final_mobile_url (	"r
CallToActionAsset]
call_to_action (2E.google.ads.googleads.v17.enums.CallToActionTypeEnum.CallToActionType"�
DynamicRealEstateAsset

listing_id (	B�A
listing_name (	B�A
	city_name (	
description (	
address (	
price (	
	image_url (	

property_type (	
listing_type	 (	
contextual_keywords
 (	
formatted_price (	
android_app_link (	
ios_app_link
 (	
ios_app_store_id (
similar_listing_ids (	"�
DynamicCustomAsset
id (	B�A
id2 (	

item_title (	B�A

item_subtitle (	
item_description (	
item_address (	

item_category (	
price (	

sale_price	 (	
formatted_price
 (	
formatted_sale_price (	
	image_url (	
contextual_keywords
 (	
android_app_link (	
ios_app_link (	
ios_app_store_id (
similar_ids (	"�
DynamicHotelsAndRentalsAsset
property_id (	B�A

property_name (	B�A
	image_url (	
destination_name (	
description (	
price (	

sale_price (	
star_rating (
category	 (	
contextual_keywords
 (	
address (	
android_app_link (	
ios_app_link
 (	
ios_app_store_id (
formatted_price (	
formatted_sale_price (	
similar_property_ids (	"�
DynamicFlightsAsset
destination_id (	B�A
	origin_id (	
flight_description (	B�A
	image_url (	
destination_name (	
origin_name (	
flight_price (	
flight_sale_price (	
formatted_price	 (	
formatted_sale_price
 (	
android_app_link (	
ios_app_link (	
ios_app_store_id
 (
similar_destination_ids (	
custom_mapping (	"�
DemandGenCarouselCardAsset
marketing_image_asset (	$
square_marketing_image_asset (	&
portrait_marketing_image_asset (	
headline (	B�A
call_to_action_text (	"�
DynamicTravelAsset
destination_id (	B�A
	origin_id (	
title (	B�A
destination_name (	
destination_address (	
origin_name (	
price (	

sale_price (	
formatted_price	 (	
formatted_sale_price
 (	
category (	
contextual_keywords (	
similar_destination_ids
 (	
	image_url (	
android_app_link (	
ios_app_link (	
ios_app_store_id ("�
DynamicLocalAsset
deal_id (	B�A
	deal_name (	B�A
subtitle (	
description (	
price (	

sale_price (	
	image_url (	
address (	
category	 (	
contextual_keywords
 (	
formatted_price (	
formatted_sale_price (	
android_app_link
 (	
similar_deal_ids (	
ios_app_link (	
ios_app_store_id ("�
DynamicJobsAsset
job_id (	B�A
location_id (	
	job_title (	B�A
job_subtitle (	
description (	
	image_url (	
job_category (	
contextual_keywords (	
address	 (	
salary
 (	
android_app_link (	
similar_job_ids (	
ios_app_link
 (	
ios_app_store_id ("�

LocationAsset
place_id (	\\
business_profile_locations (28.google.ads.googleads.v17.common.BusinessProfileLocationp
location_ownership_type (2O.google.ads.googleads.v17.enums.LocationOwnershipTypeEnum.LocationOwnershipType"Q
BusinessProfileLocation
labels (	

store_code (	

listing_id ("Q
HotelPropertyAsset
place_id (	

hotel_address (	

hotel_name (	B�
#com.google.ads.googleads.v17.commonBAssetTypesProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/common;common�GAA�Google.Ads.GoogleAds.V17.Common�Google\\Ads\\GoogleAds\\V17\\Common�#Google::Ads::GoogleAds::V17::Commonbproto3
�#
.google/ads/googleads/v17/resources/asset.proto"google.ads.googleads.v17.resources6google/ads/googleads/v17/common/custom_parameter.proto,google/ads/googleads/v17/common/policy.proto5google/ads/googleads/v17/enums/asset_field_type.proto1google/ads/googleads/v17/enums/asset_source.proto/google/ads/googleads/v17/enums/asset_type.proto;google/ads/googleads/v17/enums/policy_approval_status.proto9google/ads/googleads/v17/enums/policy_review_status.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�
Asset=

resource_name (	B&�A�A 
googleads.googleapis.com/Asset
id (B�AH�
name (	H�J
type (27.google.ads.googleads.v17.enums.AssetTypeEnum.AssetTypeB�A

final_urls (	
final_mobile_urls (	"
tracking_url_template (	H�O
url_custom_parameters (20.google.ads.googleads.v17.common.CustomParameter
final_url_suffix (	H�P
source& (2;.google.ads.googleads.v17.enums.AssetSourceEnum.AssetSourceB�AS
policy_summary
 (26.google.ads.googleads.v17.resources.AssetPolicySummaryB�Ai
field_type_policy_summaries( (2?.google.ads.googleads.v17.resources.AssetFieldTypePolicySummaryB�AV
youtube_video_asset (22.google.ads.googleads.v17.common.YoutubeVideoAssetB�AH T
media_bundle_asset (21.google.ads.googleads.v17.common.MediaBundleAssetB�AH G
image_asset (2+.google.ads.googleads.v17.common.ImageAssetB�AH E

text_asset (2*.google.ads.googleads.v17.common.TextAssetB�AH I
lead_form_asset	 (2..google.ads.googleads.v17.common.LeadFormAssetH R
book_on_google_asset
 (22.google.ads.googleads.v17.common.BookOnGoogleAssetH J
promotion_asset (2/.google.ads.googleads.v17.common.PromotionAssetH F

callout_asset (2-.google.ads.googleads.v17.common.CalloutAssetH [
structured_snippet_asset (27.google.ads.googleads.v17.common.StructuredSnippetAssetH H
sitelink_asset (2..google.ads.googleads.v17.common.SitelinkAssetH I
page_feed_asset (2..google.ads.googleads.v17.common.PageFeedAssetH Y
dynamic_education_asset (26.google.ads.googleads.v17.common.DynamicEducationAssetH K
mobile_app_asset (2/.google.ads.googleads.v17.common.MobileAppAssetH Q
hotel_callout_asset (22.google.ads.googleads.v17.common.HotelCalloutAssetH @

call_asset (2*.google.ads.googleads.v17.common.CallAssetH B
price_asset (2+.google.ads.googleads.v17.common.PriceAssetH W
call_to_action_asset (22.google.ads.googleads.v17.common.CallToActionAssetB�AH \\
dynamic_real_estate_asset (27.google.ads.googleads.v17.common.DynamicRealEstateAssetH S
dynamic_custom_asset (23.google.ads.googleads.v17.common.DynamicCustomAssetH i
 dynamic_hotels_and_rentals_asset  (2=.google.ads.googleads.v17.common.DynamicHotelsAndRentalsAssetH U
dynamic_flights_asset! (24.google.ads.googleads.v17.common.DynamicFlightsAssetH j
demand_gen_carousel_card_asset2 (2;.google.ads.googleads.v17.common.DemandGenCarouselCardAssetB�AH S
dynamic_travel_asset# (23.google.ads.googleads.v17.common.DynamicTravelAssetH Q
dynamic_local_asset$ (22.google.ads.googleads.v17.common.DynamicLocalAssetH O
dynamic_jobs_asset% (21.google.ads.googleads.v17.common.DynamicJobsAssetH M
location_asset\' (2..google.ads.googleads.v17.common.LocationAssetB�AH X
hotel_property_asset) (23.google.ads.googleads.v17.common.HotelPropertyAssetB�AH :N�AK
googleads.googleapis.com/Asset)customers/{customer_id}/assets/{asset_id}B

asset_dataB
_idB
_nameB
_tracking_url_templateB
_final_url_suffix"�
AssetFieldTypePolicySummarye
asset_field_type (2A.google.ads.googleads.v17.enums.AssetFieldTypeEnum.AssetFieldTypeB�AH �[
asset_source (2;.google.ads.googleads.v17.enums.AssetSourceEnum.AssetSourceB�AH�]
policy_summary_info (26.google.ads.googleads.v17.resources.AssetPolicySummaryB�AH�B
_asset_field_typeB

_asset_sourceB
_policy_summary_info"�
AssetPolicySummaryT
policy_topic_entries (21.google.ads.googleads.v17.common.PolicyTopicEntryB�Ae

review_status (2I.google.ads.googleads.v17.enums.PolicyReviewStatusEnum.PolicyReviewStatusB�Ak
approval_status (2M.google.ads.googleads.v17.enums.PolicyApprovalStatusEnum.PolicyApprovalStatusB�AB�
&com.google.ads.googleads.v17.resourcesB
AssetProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3
�!
5google/ads/googleads/v17/resources/change_event.proto"google.ads.googleads.v17.resources?google/ads/googleads/v17/enums/change_event_resource_type.proto>google/ads/googleads/v17/enums/resource_change_operation.proto+google/ads/googleads/v17/resources/ad.proto1google/ads/googleads/v17/resources/ad_group.proto4google/ads/googleads/v17/resources/ad_group_ad.proto7google/ads/googleads/v17/resources/ad_group_asset.proto>google/ads/googleads/v17/resources/ad_group_bid_modifier.proto;google/ads/googleads/v17/resources/ad_group_criterion.proto6google/ads/googleads/v17/resources/ad_group_feed.proto.google/ads/googleads/v17/resources/asset.proto2google/ads/googleads/v17/resources/asset_set.proto8google/ads/googleads/v17/resources/asset_set_asset.proto1google/ads/googleads/v17/resources/campaign.proto7google/ads/googleads/v17/resources/campaign_asset.proto;google/ads/googleads/v17/resources/campaign_asset_set.proto8google/ads/googleads/v17/resources/campaign_budget.proto;google/ads/googleads/v17/resources/campaign_criterion.proto6google/ads/googleads/v17/resources/campaign_feed.proto7google/ads/googleads/v17/resources/customer_asset.proto-google/ads/googleads/v17/resources/feed.proto2google/ads/googleads/v17/resources/feed_item.protogoogle/api/field_behavior.protogoogle/api/resource.proto google/protobuf/field_mask.proto"�
ChangeEventC

resource_name (	B,�A�A&
$googleads.googleapis.com/ChangeEvent
change_date_time (	B�Av
change_resource_type (2S.google.ads.googleads.v17.enums.ChangeEventResourceTypeEnum.ChangeEventResourceTypeB�A!
change_resource_name (	B�A_
client_type (2E.google.ads.googleads.v17.enums.ChangeClientTypeEnum.ChangeClientTypeB�A

user_email (	B�AZ
old_resource (2?.google.ads.googleads.v17.resources.ChangeEvent.ChangedResourceB�AZ
new_resource (2?.google.ads.googleads.v17.resources.ChangeEvent.ChangedResourceB�A{
resource_change_operation	 (2S.google.ads.googleads.v17.enums.ResourceChangeOperationEnum.ResourceChangeOperationB�A7
changed_fields
 (2.google.protobuf.FieldMaskB�A;
campaign (	B)�A�A#
!googleads.googleapis.com/Campaign:
ad_group (	B(�A�A"
 googleads.googleapis.com/AdGroup3
feed
 (	B%�A�A
googleads.googleapis.com/Feed<
	feed_item (	B)�A�A#
!googleads.googleapis.com/FeedItem5
asset (	B&�A�A 
googleads.googleapis.com/Asset�
ChangedResource7
ad (2&.google.ads.googleads.v17.resources.AdB�AB
ad_group (2+.google.ads.googleads.v17.resources.AdGroupB�AU
ad_group_criterion (24.google.ads.googleads.v17.resources.AdGroupCriterionB�AC
campaign (2,.google.ads.googleads.v17.resources.CampaignB�AP
campaign_budget (22.google.ads.googleads.v17.resources.CampaignBudgetB�AZ
ad_group_bid_modifier (26.google.ads.googleads.v17.resources.AdGroupBidModifierB�AV
campaign_criterion (25.google.ads.googleads.v17.resources.CampaignCriterionB�A;
feed (2(.google.ads.googleads.v17.resources.FeedB�AD
	feed_item	 (2,.google.ads.googleads.v17.resources.FeedItemB�AL

campaign_feed
 (20.google.ads.googleads.v17.resources.CampaignFeedB�AK

ad_group_feed (2/.google.ads.googleads.v17.resources.AdGroupFeedB�AG
ad_group_ad (2-.google.ads.googleads.v17.resources.AdGroupAdB�A=
asset
 (2).google.ads.googleads.v17.resources.AssetB�AN
customer_asset (21.google.ads.googleads.v17.resources.CustomerAssetB�AN
campaign_asset (21.google.ads.googleads.v17.resources.CampaignAssetB�AM
ad_group_asset (20.google.ads.googleads.v17.resources.AdGroupAssetB�AD
	asset_set (2,.google.ads.googleads.v17.resources.AssetSetB�AO
asset_set_asset (21.google.ads.googleads.v17.resources.AssetSetAssetB�AU
campaign_asset_set (24.google.ads.googleads.v17.resources.CampaignAssetSetB�A:��A~
$googleads.googleapis.com/ChangeEventVcustomers/{customer_id}/changeEvents/{timestamp_micros}~{command_index}~{mutate_index}B�
&com.google.ads.googleads.v17.resourcesBChangeEventProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3'
        , true);
        static::$is_initialized = true;
    }
}

