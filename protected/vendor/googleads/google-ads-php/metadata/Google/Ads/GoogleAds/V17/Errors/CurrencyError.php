<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/errors/currency_error.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Errors;

class CurrencyError
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
4google/ads/googleads/v17/errors/currency_error.protogoogle.ads.googleads.v17.errors"k
CurrencyErrorEnum"V

CurrencyError
UNSPECIFIED 
UNKNOWN\'
#VALUE_NOT_MULTIPLE_OF_BILLABLE_UNITB�
#com.google.ads.googleads.v17.errorsBCurrencyErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

