<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/message_placeholder_field.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class MessagePlaceholderField
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
>google/ads/googleads/v17/enums/message_placeholder_field.protogoogle.ads.googleads.v17.enums"�
MessagePlaceholderFieldEnum"�
MessagePlaceholderField
UNSPECIFIED 
UNKNOWN

BUSINESS_NAME
COUNTRY_CODE
PHONE_NUMBER
MESSAGE_EXTENSION_TEXT
MESSAGE_TEXTB�
"com.google.ads.googleads.v17.enumsBMessagePlaceholderFieldProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

