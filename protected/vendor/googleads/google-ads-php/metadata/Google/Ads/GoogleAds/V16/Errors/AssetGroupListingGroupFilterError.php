<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v16/errors/asset_group_listing_group_filter_error.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V16\Errors;

class AssetGroupListingGroupFilterError
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�

Lgoogle/ads/googleads/v16/errors/asset_group_listing_group_filter_error.protogoogle.ads.googleads.v16.errors"�
%AssetGroupListingGroupFilterErrorEnum"�
!AssetGroupListingGroupFilterError
UNSPECIFIED 
UNKNOWN

TREE_TOO_DEEP
UNIT_CANNOT_HAVE_CHILDREN/
+SUBDIVISION_MUST_HAVE_EVERYTHING_ELSE_CHILD-
)DIFFERENT_DIMENSION_TYPE_BETWEEN_SIBLINGS)
%SAME_DIMENSION_VALUE_BETWEEN_SIBLINGS)
%SAME_DIMENSION_TYPE_BETWEEN_ANCESTORS
MULTIPLE_ROOTS
INVALID_DIMENSION_VALUE	(
$MUST_REFINE_HIERARCHICAL_PARENT_TYPE
$
 INVALID_PRODUCT_BIDDING_CATEGORY%
!CHANGING_CASE_VALUE_WITH_CHILDREN
SUBDIVISION_HAS_CHILDREN
.
*CANNOT_REFINE_HIERARCHICAL_EVERYTHING_ELSE
DIMENSION_TYPE_NOT_ALLOWED.
*DUPLICATE_WEBPAGE_FILTER_UNDER_ASSET_GROUP
LISTING_SOURCE_NOT_ALLOWED 
FILTER_EXCLUSION_NOT_ALLOWED
MULTIPLE_LISTING_SOURCES0
,MULTIPLE_WEBPAGE_CONDITION_TYPES_NOT_ALLOWED*
&MULTIPLE_WEBPAGE_TYPES_PER_ASSET_GROUP
PAGE_FEED_FILTER_HAS_PARENT#
MULTIPLE_OPERATIONS_ON_ONE_NODEB�
#com.google.ads.googleads.v16.errorsB&AssetGroupListingGroupFilterErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors�GAA�Google.Ads.GoogleAds.V16.Errors�Google\\Ads\\GoogleAds\\V16\\Errors�#Google::Ads::GoogleAds::V16::Errorsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

