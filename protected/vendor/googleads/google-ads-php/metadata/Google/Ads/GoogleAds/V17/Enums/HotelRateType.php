<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/hotel_rate_type.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class HotelRateType
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
4google/ads/googleads/v17/enums/hotel_rate_type.protogoogle.ads.googleads.v17.enums"�
HotelRateTypeEnum"u

HotelRateType
UNSPECIFIED 
UNKNOWN
UNAVAILABLE
PUBLIC_RATE
QUALIFIED_RATE
PRIVATE_RATEB�
"com.google.ads.googleads.v17.enumsBHotelRateTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

