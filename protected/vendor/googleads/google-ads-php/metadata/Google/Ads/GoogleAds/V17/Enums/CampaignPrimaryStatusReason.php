<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/campaign_primary_status_reason.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class CampaignPrimaryStatusReason
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
Cgoogle/ads/googleads/v17/enums/campaign_primary_status_reason.protogoogle.ads.googleads.v17.enums"�
CampaignPrimaryStatusReasonEnum"�
CampaignPrimaryStatusReason
UNSPECIFIED 
UNKNOWN
CAMPAIGN_REMOVED
CAMPAIGN_PAUSED
CAMPAIGN_PENDING
CAMPAIGN_ENDED
CAMPAIGN_DRAFT"
BIDDING_STRATEGY_MISCONFIGURED
BIDDING_STRATEGY_LIMITED
BIDDING_STRATEGY_LEARNING	 
BIDDING_STRATEGY_CONSTRAINED

BUDGET_CONSTRAINED
BUDGET_MISCONFIGURED
SEARCH_VOLUME_LIMITED

AD_GROUPS_PAUSED
NO_AD_GROUPS
KEYWORDS_PAUSED
NO_KEYWORDS
AD_GROUP_ADS_PAUSED
NO_AD_GROUP_ADS
HAS_ADS_LIMITED_BY_POLICY
HAS_ADS_DISAPPROVED
MOST_ADS_UNDER_REVIEW
MISSING_LEAD_FORM_EXTENSION
MISSING_CALL_EXTENSION$
 LEAD_FORM_EXTENSION_UNDER_REVIEW#
LEAD_FORM_EXTENSION_DISAPPROVED
CALL_EXTENSION_UNDER_REVIEW
CALL_EXTENSION_DISAPPROVED+
\'NO_MOBILE_APPLICATION_AD_GROUP_CRITERIA
CAMPAIGN_GROUP_PAUSED*
&CAMPAIGN_GROUP_ALL_GROUP_BUDGETS_ENDED
APP_NOT_RELEASED 
APP_PARTIALLY_RELEASED! 
HAS_ASSET_GROUPS_DISAPPROVED"&
"HAS_ASSET_GROUPS_LIMITED_BY_POLICY#"
MOST_ASSET_GROUPS_UNDER_REVIEW$
NO_ASSET_GROUPS%
ASSET_GROUPS_PAUSED&B�
"com.google.ads.googleads.v17.enumsB CampaignPrimaryStatusReasonProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

