<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/reach_plan_surface.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class ReachPlanSurface
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
7google/ads/googleads/v17/enums/reach_plan_surface.protogoogle.ads.googleads.v17.enums"�
ReachPlanSurfaceEnum"�
ReachPlanSurface
UNSPECIFIED 
UNKNOWN
IN_FEED
IN_STREAM_BUMPER
IN_STREAM_NON_SKIPPABLE
IN_STREAM_SKIPPABLE

SHORTSB�
"com.google.ads.googleads.v17.enumsBReachPlanSurfaceProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

