<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v16/resources/recommendation.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V16\Resources;

class Recommendation
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\FieldBehavior::initOnce();
        \GPBMetadata\Google\Api\Resource::initOnce();
        $pool->internalAddGeneratedFile(
            '
�
0google/ads/googleads/v16/enums/brand_state.protogoogle.ads.googleads.v16.enums"�
BrandStateEnum"�

BrandState
UNSPECIFIED 
UNKNOWN
ENABLED

DEPRECATED

UNVERIFIED
APPROVED
	CANCELLED
REJECTEDB�
"com.google.ads.googleads.v16.enumsBBrandStateProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
6google/ads/googleads/v16/enums/product_condition.protogoogle.ads.googleads.v16.enums"l
ProductConditionEnum"T
ProductCondition
UNSPECIFIED 
UNKNOWN
NEW
REFURBISHED
USEDB�
"com.google.ads.googleads.v16.enumsBProductConditionProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
,google/ads/googleads/v16/enums/ad_type.protogoogle.ads.googleads.v16.enums"�

AdTypeEnum"�
AdType
UNSPECIFIED 
UNKNOWN
TEXT_AD
EXPANDED_TEXT_AD
EXPANDED_DYNAMIC_SEARCH_AD
HOTEL_AD
SHOPPING_SMART_AD	
SHOPPING_PRODUCT_AD

VIDEO_AD
IMAGE_AD
RESPONSIVE_SEARCH_AD 
LEGACY_RESPONSIVE_DISPLAY_AD

APP_AD
LEGACY_APP_INSTALL_AD
RESPONSIVE_DISPLAY_AD
LOCAL_AD
HTML5_UPLOAD_AD
DYNAMIC_HTML5_AD
APP_ENGAGEMENT_AD"
SHOPPING_COMPARISON_LISTING_AD
VIDEO_BUMPER_AD$
 VIDEO_NON_SKIPPABLE_IN_STREAM_AD
VIDEO_OUTSTREAM_AD
VIDEO_TRUEVIEW_IN_STREAM_AD
VIDEO_RESPONSIVE_AD
SMART_CAMPAIGN_AD
CALL_AD 
APP_PRE_REGISTRATION_AD!
IN_FEED_VIDEO_AD"
DISCOVERY_MULTI_ASSET_AD#
DISCOVERY_CAROUSEL_AD$
	TRAVEL_AD%!
DISCOVERY_VIDEO_RESPONSIVE_AD&
DEMAND_GEN_PRODUCT_AD\'B�
"com.google.ads.googleads.v16.enumsBAdTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
;google/ads/googleads/v16/enums/proximity_radius_units.protogoogle.ads.googleads.v16.enums"k
ProximityRadiusUnitsEnum"O
ProximityRadiusUnits
UNSPECIFIED 
UNKNOWN	
MILES

KILOMETERSB�
"com.google.ads.googleads.v16.enumsBProximityRadiusUnitsProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
=google/ads/googleads/v16/enums/lead_form_desired_intent.protogoogle.ads.googleads.v16.enums"s
LeadFormDesiredIntentEnum"V
LeadFormDesiredIntent
UNSPECIFIED 
UNKNOWN

LOW_INTENT
HIGH_INTENTB�
"com.google.ads.googleads.v16.enumsBLeadFormDesiredIntentProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Dgoogle/ads/googleads/v16/enums/call_conversion_reporting_state.protogoogle.ads.googleads.v16.enums"�
 CallConversionReportingStateEnum"�
CallConversionReportingState
UNSPECIFIED 
UNKNOWN
DISABLED,
(USE_ACCOUNT_LEVEL_CALL_CONVERSION_ACTION-
)USE_RESOURCE_LEVEL_CALL_CONVERSION_ACTIONB�
"com.google.ads.googleads.v16.enumsB!CallConversionReportingStateProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
9google/ads/googleads/v16/enums/price_extension_type.protogoogle.ads.googleads.v16.enums"�
PriceExtensionTypeEnum"�
PriceExtensionType
UNSPECIFIED 
UNKNOWN

BRANDS

EVENTS
	LOCATIONS

NEIGHBORHOODS
PRODUCT_CATEGORIES

PRODUCT_TIERS
SERVICES
SERVICE_CATEGORIES	

SERVICE_TIERS
B�
"com.google.ads.googleads.v16.enumsBPriceExtensionTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Cgoogle/ads/googleads/v16/enums/brand_request_rejection_reason.protogoogle.ads.googleads.v16.enums"�
BrandRequestRejectionReasonEnum"�
BrandRequestRejectionReason
UNSPECIFIED 
UNKNOWN
EXISTING_BRAND
EXISTING_BRAND_VARIANT
INCORRECT_INFORMATION
NOT_A_BRANDB�
"com.google.ads.googleads.v16.enumsB BrandRequestRejectionReasonProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Bgoogle/ads/googleads/v16/enums/app_url_operating_system_type.protogoogle.ads.googleads.v16.enums"p
AppUrlOperatingSystemTypeEnum"O
AppUrlOperatingSystemType
UNSPECIFIED 
UNKNOWN
IOS
ANDROIDB�
"com.google.ads.googleads.v16.enumsBAppUrlOperatingSystemTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
4google/ads/googleads/v16/enums/product_channel.protogoogle.ads.googleads.v16.enums"[
ProductChannelEnum"E
ProductChannel
UNSPECIFIED 
UNKNOWN

ONLINE	
LOCALB�
"com.google.ads.googleads.v16.enumsBProductChannelProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
+google/ads/googleads/v16/enums/device.protogoogle.ads.googleads.v16.enums"v

DeviceEnum"h
Device
UNSPECIFIED 
UNKNOWN

MOBILE

TABLET
DESKTOP
CONNECTED_TV	
OTHERB�
"com.google.ads.googleads.v16.enumsBDeviceProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Bgoogle/ads/googleads/v16/enums/lead_form_call_to_action_type.protogoogle.ads.googleads.v16.enums"�
LeadFormCallToActionTypeEnum"�
LeadFormCallToActionType
UNSPECIFIED 
UNKNOWN

LEARN_MORE
	GET_QUOTE
	APPLY_NOW
SIGN_UP

CONTACT_US
	SUBSCRIBE
DOWNLOAD
BOOK_NOW	
	GET_OFFER

REGISTER
GET_INFO
REQUEST_DEMO

JOIN_NOW
GET_STARTEDB�
"com.google.ads.googleads.v16.enumsBLeadFormCallToActionTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
<google/ads/googleads/v16/enums/policy_topic_entry_type.protogoogle.ads.googleads.v16.enums"�
PolicyTopicEntryTypeEnum"�
PolicyTopicEntryType
UNSPECIFIED 
UNKNOWN

PROHIBITED
LIMITED

FULLY_LIMITED
DESCRIPTIVE

BROADENING
AREA_OF_INTEREST_ONLYB�
"com.google.ads.googleads.v16.enumsBPolicyTopicEntryTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
>google/ads/googleads/v16/enums/display_ad_format_setting.protogoogle.ads.googleads.v16.enums"�
DisplayAdFormatSettingEnum"c
DisplayAdFormatSetting
UNSPECIFIED 
UNKNOWN
ALL_FORMATS

NON_NATIVE

NATIVEB�
"com.google.ads.googleads.v16.enumsBDisplayAdFormatSettingProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Cgoogle/ads/googleads/v16/enums/product_custom_attribute_index.protogoogle.ads.googleads.v16.enums"�
ProductCustomAttributeIndexEnum"w
ProductCustomAttributeIndex
UNSPECIFIED 
UNKNOWN

INDEX0

INDEX1

INDEX2	

INDEX3


INDEX4B�
"com.google.ads.googleads.v16.enumsB ProductCustomAttributeIndexProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
;google/ads/googleads/v16/enums/app_payment_model_type.protogoogle.ads.googleads.v16.enums"X
AppPaymentModelTypeEnum"=
AppPaymentModelType
UNSPECIFIED 
UNKNOWN
PAIDB�
"com.google.ads.googleads.v16.enumsBAppPaymentModelTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Egoogle/ads/googleads/v16/enums/asset_link_primary_status_reason.protogoogle.ads.googleads.v16.enums"�
 AssetLinkPrimaryStatusReasonEnum"�
AssetLinkPrimaryStatusReason
UNSPECIFIED 
UNKNOWN
ASSET_LINK_PAUSED
ASSET_LINK_REMOVED
ASSET_DISAPPROVED
ASSET_UNDER_REVIEW
ASSET_APPROVED_LABELEDB�
"com.google.ads.googleads.v16.enumsB!AssetLinkPrimaryStatusReasonProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
4google/ads/googleads/v16/common/url_collection.protogoogle.ads.googleads.v16.common"�

UrlCollection
url_collection_id (	H �

final_urls (	
final_mobile_urls (	"
tracking_url_template (	H�B
_url_collection_idB
_tracking_url_templateB�
#com.google.ads.googleads.v16.commonBUrlCollectionProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v16/common;common�GAA�Google.Ads.GoogleAds.V16.Common�Google\\Ads\\GoogleAds\\V16\\Common�#Google::Ads::GoogleAds::V16::Commonbproto3
�
;google/ads/googleads/v16/enums/product_category_level.protogoogle.ads.googleads.v16.enums"�
ProductCategoryLevelEnum"p
ProductCategoryLevel
UNSPECIFIED 
UNKNOWN

LEVEL1

LEVEL2

LEVEL3

LEVEL4

LEVEL5B�
"com.google.ads.googleads.v16.enumsBProductCategoryLevelProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
4google/ads/googleads/v16/enums/video_thumbnail.protogoogle.ads.googleads.v16.enums"�
VideoThumbnailEnum"x
VideoThumbnail
UNSPECIFIED 
UNKNOWN
DEFAULT_THUMBNAIL
THUMBNAIL_1
THUMBNAIL_2
THUMBNAIL_3B�
"com.google.ads.googleads.v16.enumsBVideoThumbnailProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
9google/ads/googleads/v16/enums/parental_status_type.protogoogle.ads.googleads.v16.enums"
ParentalStatusTypeEnum"e
ParentalStatusType
UNSPECIFIED 
UNKNOWN
PARENT�
NOT_A_PARENT�
UNDETERMINED�B�
"com.google.ads.googleads.v16.enumsBParentalStatusTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
<google/ads/googleads/v16/enums/location_ownership_type.protogoogle.ads.googleads.v16.enums"u
LocationOwnershipTypeEnum"X
LocationOwnershipType
UNSPECIFIED 
UNKNOWN
BUSINESS_OWNER
	AFFILIATEB�
"com.google.ads.googleads.v16.enumsBLocationOwnershipTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Agoogle/ads/googleads/v16/enums/system_managed_entity_source.protogoogle.ads.googleads.v16.enums"q
SystemManagedResourceSourceEnum"N
SystemManagedResourceSource
UNSPECIFIED 
UNKNOWN

AD_VARIATIONSB�
"com.google.ads.googleads.v16.enumsBSystemManagedEntitySourceProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
>google/ads/googleads/v16/enums/webpage_condition_operand.protogoogle.ads.googleads.v16.enums"�
WebpageConditionOperandEnum"�
WebpageConditionOperand
UNSPECIFIED 
UNKNOWN
URL
CATEGORY

PAGE_TITLE
PAGE_CONTENT
CUSTOM_LABELB�
"com.google.ads.googleads.v16.enumsBWebpageConditionOperandProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
3google/ads/googleads/v16/enums/age_range_type.protogoogle.ads.googleads.v16.enums"�
AgeRangeTypeEnum"�
AgeRangeType
UNSPECIFIED 
UNKNOWN
AGE_RANGE_18_24��
AGE_RANGE_25_34��
AGE_RANGE_35_44��
AGE_RANGE_45_54��
AGE_RANGE_55_64��
AGE_RANGE_65_UP��
AGE_RANGE_UNDETERMINED��B�
"com.google.ads.googleads.v16.enumsBAgeRangeTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
;google/ads/googleads/v16/enums/policy_approval_status.protogoogle.ads.googleads.v16.enums"�
PolicyApprovalStatusEnum"�
PolicyApprovalStatus
UNSPECIFIED 
UNKNOWN
DISAPPROVED
APPROVED_LIMITED
APPROVED
AREA_OF_INTEREST_ONLYB�
"com.google.ads.googleads.v16.enumsBPolicyApprovalStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
?google/ads/googleads/v16/enums/webpage_condition_operator.protogoogle.ads.googleads.v16.enums"r
WebpageConditionOperatorEnum"R
WebpageConditionOperator
UNSPECIFIED 
UNKNOWN

EQUALS
CONTAINSB�
"com.google.ads.googleads.v16.enumsBWebpageConditionOperatorProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
>google/ads/googleads/v16/enums/asset_link_primary_status.protogoogle.ads.googleads.v16.enums"�
AssetLinkPrimaryStatusEnum"�
AssetLinkPrimaryStatus
UNSPECIFIED 
UNKNOWN
ELIGIBLE

PAUSED
REMOVED
PENDING
LIMITED
NOT_ELIGIBLEB�
"com.google.ads.googleads.v16.enumsBAssetLinkPrimaryStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
9google/ads/googleads/v16/enums/policy_review_status.protogoogle.ads.googleads.v16.enums"�
PolicyReviewStatusEnum"�
PolicyReviewStatus
UNSPECIFIED 
UNKNOWN
REVIEW_IN_PROGRESS
REVIEWED
UNDER_APPEAL
ELIGIBLE_MAY_SERVEB�
"com.google.ads.googleads.v16.enumsBPolicyReviewStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
?google/ads/googleads/v16/enums/price_extension_price_unit.protogoogle.ads.googleads.v16.enums"�
PriceExtensionPriceUnitEnum"�
PriceExtensionPriceUnit
UNSPECIFIED 
UNKNOWN
PER_HOUR
PER_DAY
PER_WEEK
	PER_MONTH
PER_YEAR
	PER_NIGHTB�
"com.google.ads.googleads.v16.enumsBPriceExtensionPriceUnitProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
1google/ads/googleads/v16/enums/asset_source.protogoogle.ads.googleads.v16.enums"i
AssetSourceEnum"V
AssetSource
UNSPECIFIED 
UNKNOWN

ADVERTISER
AUTOMATICALLY_CREATEDB�
"com.google.ads.googleads.v16.enumsBAssetSourceProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
/google/ads/googleads/v16/enums/asset_type.protogoogle.ads.googleads.v16.enums"�

AssetTypeEnum"�
	AssetType
UNSPECIFIED 
UNKNOWN

YOUTUBE_VIDEO
MEDIA_BUNDLE	
IMAGE
TEXT
	LEAD_FORM
BOOK_ON_GOOGLE
	PROMOTION
CALLOUT	
STRUCTURED_SNIPPET

SITELINK
	PAGE_FEED
DYNAMIC_EDUCATION


MOBILE_APP

HOTEL_CALLOUT
CALL	
PRICE
CALL_TO_ACTION
DYNAMIC_REAL_ESTATE
DYNAMIC_CUSTOM
DYNAMIC_HOTELS_AND_RENTALS
DYNAMIC_FLIGHTS
DISCOVERY_CAROUSEL_CARD
DYNAMIC_TRAVEL

DYNAMIC_LOCAL
DYNAMIC_JOBS
LOCATION
HOTEL_PROPERTYB�
"com.google.ads.googleads.v16.enumsBAssetTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
0google/ads/googleads/v16/enums/ad_strength.protogoogle.ads.googleads.v16.enums"�
AdStrengthEnum"s

AdStrength
UNSPECIFIED 
UNKNOWN
PENDING

NO_ADS
POOR
AVERAGE
GOOD
	EXCELLENTB�
"com.google.ads.googleads.v16.enumsBAdStrengthProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
3google/ads/googleads/v16/common/final_app_url.protogoogle.ads.googleads.v16.common"�
FinalAppUrlh
os_type (2W.google.ads.googleads.v16.enums.AppUrlOperatingSystemTypeEnum.AppUrlOperatingSystemType
url (	H �B
_urlB�
#com.google.ads.googleads.v16.commonBFinalAppUrlProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v16/common;common�GAA�Google.Ads.GoogleAds.V16.Common�Google\\Ads\\GoogleAds\\V16\\Common�#Google::Ads::GoogleAds::V16::Commonbproto3
�
Agoogle/ads/googleads/v16/enums/promotion_extension_occasion.protogoogle.ads.googleads.v16.enums"�
PromotionExtensionOccasionEnum"�
PromotionExtensionOccasion
UNSPECIFIED 
UNKNOWN
	NEW_YEARS
CHINESE_NEW_YEAR
VALENTINES_DAY

EASTER
MOTHERS_DAY
FATHERS_DAY
	LABOR_DAY
BACK_TO_SCHOOL	
	HALLOWEEN

BLACK_FRIDAY
CYBER_MONDAY
	CHRISTMAS


BOXING_DAY
INDEPENDENCE_DAY
NATIONAL_DAY

END_OF_SEASON
WINTER_SALE
SUMMER_SALE
	FALL_SALE
SPRING_SALE
RAMADAN
EID_AL_FITR
EID_AL_ADHA
SINGLES_DAY

WOMENS_DAY
HOLI
PARENTS_DAY
ST_NICHOLAS_DAY
CARNIVAL
EPIPHANY

ROSH_HASHANAH 
PASSOVER!
HANUKKAH"

DIWALI#
NAVRATRI$
SONGKRAN%

YEAR_END_GIFT&B�
"com.google.ads.googleads.v16.enumsBPromotionExtensionOccasionProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Dgoogle/ads/googleads/v16/enums/price_extension_price_qualifier.protogoogle.ads.googleads.v16.enums"�
 PriceExtensionPriceQualifierEnum"^
PriceExtensionPriceQualifier
UNSPECIFIED 
UNKNOWN
FROM	
UP_TO
AVERAGEB�
"com.google.ads.googleads.v16.enumsB!PriceExtensionPriceQualifierProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
0google/ads/googleads/v16/enums/gender_type.protogoogle.ads.googleads.v16.enums"d
GenderTypeEnum"R

GenderType
UNSPECIFIED 
UNKNOWN
MALE


FEMALE
UNDETERMINEDB�
"com.google.ads.googleads.v16.enumsBGenderTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
<google/ads/googleads/v16/enums/asset_performance_label.protogoogle.ads.googleads.v16.enums"�
AssetPerformanceLabelEnum"m
AssetPerformanceLabel
UNSPECIFIED 
UNKNOWN
PENDING
LEARNING
LOW
GOOD
BESTB�
"com.google.ads.googleads.v16.enumsBAssetPerformanceLabelProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
1google/ads/googleads/v16/common/feed_common.protogoogle.ads.googleads.v16.common"c
Money

currency_code (	H �

amount_micros (H�B
_currency_codeB
_amount_microsB�
#com.google.ads.googleads.v16.commonBFeedCommonProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v16/common;common�GAA�Google.Ads.GoogleAds.V16.Common�Google\\Ads\\GoogleAds\\V16\\Common�#Google::Ads::GoogleAds::V16::Commonbproto3
�
6google/ads/googleads/v16/enums/income_range_type.protogoogle.ads.googleads.v16.enums"�
IncomeRangeTypeEnum"�
IncomeRangeType
UNSPECIFIED 
UNKNOWN
INCOME_RANGE_0_50��
INCOME_RANGE_50_60��
INCOME_RANGE_60_70��
INCOME_RANGE_70_80��
INCOME_RANGE_80_90��
INCOME_RANGE_90_UP��
INCOME_RANGE_UNDETERMINED��B�
"com.google.ads.googleads.v16.enumsBIncomeRangeTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
6google/ads/googleads/v16/common/custom_parameter.protogoogle.ads.googleads.v16.common"I
CustomParameter
key (	H �
value (	H�B
_keyB
_valueB�
#com.google.ads.googleads.v16.commonBCustomParameterProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v16/common;common�GAA�Google.Ads.GoogleAds.V16.Common�Google\\Ads\\GoogleAds\\V16\\Common�#Google::Ads::GoogleAds::V16::Commonbproto3
�
Kgoogle/ads/googleads/v16/enums/asset_offline_evaluation_error_reasons.protogoogle.ads.googleads.v16.enums"�
&AssetOfflineEvaluationErrorReasonsEnum"�
"AssetOfflineEvaluationErrorReasons
UNSPECIFIED 
UNKNOWN.
*PRICE_ASSET_DESCRIPTION_REPEATS_ROW_HEADER"
PRICE_ASSET_REPETITIVE_HEADERS3
/PRICE_ASSET_HEADER_INCOMPATIBLE_WITH_PRICE_TYPE9
5PRICE_ASSET_DESCRIPTION_INCOMPATIBLE_WITH_ITEM_HEADER/
+PRICE_ASSET_DESCRIPTION_HAS_PRICE_QUALIFIER$
 PRICE_ASSET_UNSUPPORTED_LANGUAGE
PRICE_ASSET_OTHER_ERRORB�
"com.google.ads.googleads.v16.enumsB\'AssetOfflineEvaluationErrorReasonsProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
7google/ads/googleads/v16/enums/listing_group_type.protogoogle.ads.googleads.v16.enums"c
ListingGroupTypeEnum"K
ListingGroupType
UNSPECIFIED 
UNKNOWN
SUBDIVISION
UNITB�
"com.google.ads.googleads.v16.enumsBListingGroupTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Dgoogle/ads/googleads/v16/enums/legacy_app_install_ad_app_store.protogoogle.ads.googleads.v16.enums"�
LegacyAppInstallAdAppStoreEnum"�
LegacyAppInstallAdAppStore
UNSPECIFIED 
UNKNOWN
APPLE_APP_STORE
GOOGLE_PLAY

WINDOWS_STORE
WINDOWS_PHONE_STORE
CN_APP_STOREB�
"com.google.ads.googleads.v16.enumsBLegacyAppInstallAdAppStoreProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
3google/ads/googleads/v16/enums/minute_of_hour.protogoogle.ads.googleads.v16.enums"s
MinuteOfHourEnum"_
MinuteOfHour
UNSPECIFIED 
UNKNOWN
ZERO
FIFTEEN

THIRTY

FORTY_FIVEB�
"com.google.ads.googleads.v16.enumsBMinuteOfHourProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
7google/ads/googleads/v16/enums/product_type_level.protogoogle.ads.googleads.v16.enums"�
ProductTypeLevelEnum"l
ProductTypeLevel
UNSPECIFIED 
UNKNOWN

LEVEL1

LEVEL2

LEVEL3	

LEVEL4


LEVEL5B�
"com.google.ads.googleads.v16.enumsBProductTypeLevelProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
>google/ads/googleads/v16/enums/hotel_date_selection_type.protogoogle.ads.googleads.v16.enums"~
HotelDateSelectionTypeEnum"`
HotelDateSelectionType
UNSPECIFIED 
UNKNOWN
DEFAULT_SELECTION2

USER_SELECTED3B�
"com.google.ads.googleads.v16.enumsBHotelDateSelectionTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
5google/ads/googleads/v16/enums/app_bidding_goal.protogoogle.ads.googleads.v16.enums"�
AppBiddingGoalEnum"�
AppBiddingGoal
UNSPECIFIED 
UNKNOWN*
&OPTIMIZE_FOR_INSTALL_CONVERSION_VOLUME)
%OPTIMIZE_FOR_IN_APP_CONVERSION_VOLUME\'
#OPTIMIZE_FOR_TOTAL_CONVERSION_VALUE)
%OPTIMIZE_FOR_TARGET_IN_APP_CONVERSION,
(OPTIMIZE_FOR_RETURN_ON_ADVERTISING_SPEND=
9OPTIMIZE_FOR_INSTALL_CONVERSION_VOLUME_WITHOUT_TARGET_CPI3
/OPTIMIZE_FOR_PRE_REGISTRATION_CONVERSION_VOLUMEB�
"com.google.ads.googleads.v16.enumsBAppBiddingGoalProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
0google/ads/googleads/v16/enums/day_of_week.protogoogle.ads.googleads.v16.enums"�

DayOfWeekEnum"�
	DayOfWeek
UNSPECIFIED 
UNKNOWN

MONDAY
TUESDAY
	WEDNESDAY
THURSDAY

FRIDAY
SATURDAY

SUNDAYB�
"com.google.ads.googleads.v16.enumsBDayOfWeekProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
7google/ads/googleads/v16/enums/content_label_type.protogoogle.ads.googleads.v16.enums"�
ContentLabelTypeEnum"�
ContentLabelType
UNSPECIFIED 
UNKNOWN
SEXUALLY_SUGGESTIVE
BELOW_THE_FOLD

PARKED_DOMAIN
JUVENILE
	PROFANITY
TRAGEDY	
VIDEO	
VIDEO_RATING_DV_G

VIDEO_RATING_DV_PG
VIDEO_RATING_DV_T
VIDEO_RATING_DV_MA

VIDEO_NOT_YET_RATED
EMBEDDED_VIDEO
LIVE_STREAMING_VIDEO

SOCIAL_ISSUESB�
"com.google.ads.googleads.v16.enumsBContentLabelTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Ngoogle/ads/googleads/v16/enums/lead_form_post_submit_call_to_action_type.protogoogle.ads.googleads.v16.enums"�
&LeadFormPostSubmitCallToActionTypeEnum"~
"LeadFormPostSubmitCallToActionType
UNSPECIFIED 
UNKNOWN

VISIT_SITE
DOWNLOAD

LEARN_MORE
SHOP_NOWB�
"com.google.ads.googleads.v16.enumsB\'LeadFormPostSubmitCallToActionTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Zgoogle/ads/googleads/v16/enums/shopping_add_products_to_campaign_recommendation_enum.protogoogle.ads.googleads.v16.enums"�
/ShoppingAddProductsToCampaignRecommendationEnum"�
Reason
UNSPECIFIED 
UNKNOWN5
1MERCHANT_CENTER_ACCOUNT_HAS_NO_SUBMITTED_PRODUCTS=
9MERCHANT_CENTER_ACCOUNT_HAS_NO_SUBMITTED_PRODUCTS_IN_FEED-
)ADS_ACCOUNT_EXCLUDES_OFFERS_FROM_CAMPAIGN+
\'ALL_PRODUCTS_ARE_EXCLUDED_FROM_CAMPAIGNB�
"com.google.ads.googleads.v16.enumsB4ShoppingAddProductsToCampaignRecommendationEnumProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Xgoogle/ads/googleads/v16/enums/policy_topic_evidence_destination_mismatch_url_type.protogoogle.ads.googleads.v16.enums"�
1PolicyTopicEvidenceDestinationMismatchUrlTypeEnum"�
-PolicyTopicEvidenceDestinationMismatchUrlType
UNSPECIFIED 
UNKNOWN
DISPLAY_URL
	FINAL_URL
FINAL_MOBILE_URL
TRACKING_URL
MOBILE_TRACKING_URLB�
"com.google.ads.googleads.v16.enumsB2PolicyTopicEvidenceDestinationMismatchUrlTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
@google/ads/googleads/v16/enums/product_channel_exclusivity.protogoogle.ads.googleads.v16.enums"�
ProductChannelExclusivityEnum"`
ProductChannelExclusivity
UNSPECIFIED 
UNKNOWN
SINGLE_CHANNEL

MULTI_CHANNELB�
"com.google.ads.googleads.v16.enumsBProductChannelExclusivityProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
8google/ads/googleads/v16/enums/recommendation_type.protogoogle.ads.googleads.v16.enums"�
RecommendationTypeEnum"�
RecommendationType
UNSPECIFIED 
UNKNOWN
CAMPAIGN_BUDGET
KEYWORD
TEXT_AD
TARGET_CPA_OPT_IN
MAXIMIZE_CONVERSIONS_OPT_IN
ENHANCED_CPC_OPT_IN
SEARCH_PARTNERS_OPT_IN
MAXIMIZE_CLICKS_OPT_IN	
OPTIMIZE_AD_ROTATION

KEYWORD_MATCH_TYPE
MOVE_UNUSED_BUDGET
FORECASTING_CAMPAIGN_BUDGET
TARGET_ROAS_OPT_IN
RESPONSIVE_SEARCH_AD 
MARGINAL_ROI_CAMPAIGN_BUDGET
USE_BROAD_MATCH_KEYWORD
RESPONSIVE_SEARCH_AD_ASSET6
2UPGRADE_SMART_SHOPPING_CAMPAIGN_TO_PERFORMANCE_MAX,
(RESPONSIVE_SEARCH_AD_IMPROVE_AD_STRENGTH
DISPLAY_EXPANSION_OPT_IN-
)UPGRADE_LOCAL_CAMPAIGN_TO_PERFORMANCE_MAX 
RAISE_TARGET_CPA_BID_TOO_LOW
FORECASTING_SET_TARGET_ROAS

CALLOUT_ASSET
SITELINK_ASSET

CALL_ASSET
SHOPPING_ADD_AGE_GROUP
SHOPPING_ADD_COLOR 
SHOPPING_ADD_GENDER!
SHOPPING_ADD_GTIN"!
SHOPPING_ADD_MORE_IDENTIFIERS#
SHOPPING_ADD_SIZE$%
!SHOPPING_ADD_PRODUCTS_TO_CAMPAIGN%%
!SHOPPING_FIX_DISAPPROVED_PRODUCTS&
SHOPPING_TARGET_ALL_OFFERS\'2
.SHOPPING_FIX_SUSPENDED_MERCHANT_CENTER_ACCOUNT(;
7SHOPPING_FIX_MERCHANT_CENTER_ACCOUNT_SUSPENSION_WARNING)H
DSHOPPING_MIGRATE_REGULAR_SHOPPING_CAMPAIGN_OFFERS_TO_PERFORMANCE_MAX*"
DYNAMIC_IMAGE_EXTENSION_OPT_IN+
RAISE_TARGET_CPA,
LOWER_TARGET_ROAS-
PERFORMANCE_MAX_OPT_IN.\'
#IMPROVE_PERFORMANCE_MAX_AD_STRENGTH/:
6MIGRATE_DYNAMIC_SEARCH_ADS_CAMPAIGN_TO_PERFORMANCE_MAX0
FORECASTING_SET_TARGET_CPA1
SET_TARGET_CPA2
SET_TARGET_ROAS3$
 MAXIMIZE_CONVERSION_VALUE_OPT_IN4
IMPROVE_GOOGLE_TAG_COVERAGE5$
 PERFORMANCE_MAX_FINAL_URL_OPT_IN6
REFRESH_CUSTOMER_MATCH_LIST7
CUSTOM_AUDIENCE_OPT_IN8
LEAD_FORM_ASSET9"
IMPROVE_DEMAND_GEN_AD_STRENGTH:B�
"com.google.ads.googleads.v16.enumsBRecommendationTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
<google/ads/googleads/v16/enums/served_asset_field_type.protogoogle.ads.googleads.v16.enums"�
ServedAssetFieldTypeEnum"�
ServedAssetFieldType
UNSPECIFIED 
UNKNOWN

HEADLINE_1

HEADLINE_2

HEADLINE_3

DESCRIPTION_1

DESCRIPTION_2
HEADLINE
HEADLINE_IN_PORTRAIT

LONG_HEADLINE	
DESCRIPTION

DESCRIPTION_IN_PORTRAIT
BUSINESS_NAME_IN_PORTRAIT

BUSINESS_NAME

MARKETING_IMAGE
MARKETING_IMAGE_IN_PORTRAIT
SQUARE_MARKETING_IMAGE
PORTRAIT_MARKETING_IMAGE
LOGO
LANDSCAPE_LOGO
CALL_TO_ACTION
YOU_TUBE_VIDEO
SITELINK
CALL

MOBILE_APP
CALLOUT
STRUCTURED_SNIPPET	
PRICE
	PROMOTION
AD_IMAGE
	LEAD_FORM

BUSINESS_LOGOB�
"com.google.ads.googleads.v16.enumsBServedAssetFieldTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Jgoogle/ads/googleads/v16/enums/promotion_extension_discount_modifier.protogoogle.ads.googleads.v16.enums"w
&PromotionExtensionDiscountModifierEnum"M
"PromotionExtensionDiscountModifier
UNSPECIFIED 
UNKNOWN	
UP_TOB�
"com.google.ads.googleads.v16.enumsB\'PromotionExtensionDiscountModifierProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
7google/ads/googleads/v16/enums/keyword_match_type.protogoogle.ads.googleads.v16.enums"j
KeywordMatchTypeEnum"R
KeywordMatchType
UNSPECIFIED 
UNKNOWN	
EXACT

PHRASE	
BROADB�
"com.google.ads.googleads.v16.enumsBKeywordMatchTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Jgoogle/ads/googleads/v16/enums/target_cpa_opt_in_recommendation_goal.protogoogle.ads.googleads.v16.enums"�
$TargetCpaOptInRecommendationGoalEnum"�
 TargetCpaOptInRecommendationGoal
UNSPECIFIED 
UNKNOWN
	SAME_COST
SAME_CONVERSIONS
SAME_CPA
CLOSEST_CPAB�
"com.google.ads.googleads.v16.enumsB%TargetCpaOptInRecommendationGoalProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
5google/ads/googleads/v16/enums/asset_field_type.protogoogle.ads.googleads.v16.enums"�
AssetFieldTypeEnum"�
AssetFieldType
UNSPECIFIED 
UNKNOWN
HEADLINE
DESCRIPTION
MANDATORY_AD_TEXT
MARKETING_IMAGE
MEDIA_BUNDLE

YOUTUBE_VIDEO
BOOK_ON_GOOGLE
	LEAD_FORM	
	PROMOTION

CALLOUT
STRUCTURED_SNIPPET
SITELINK


MOBILE_APP

HOTEL_CALLOUT
CALL	
PRICE

LONG_HEADLINE

BUSINESS_NAME
SQUARE_MARKETING_IMAGE
PORTRAIT_MARKETING_IMAGE
LOGO
LANDSCAPE_LOGO	
VIDEO
CALL_TO_ACTION_SELECTION
AD_IMAGE

BUSINESS_LOGO
HOTEL_PROPERTY
DISCOVERY_CAROUSEL_CARDB�
"com.google.ads.googleads.v16.enumsBAssetFieldTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
agoogle/ads/googleads/v16/enums/policy_topic_evidence_destination_not_working_dns_error_type.protogoogle.ads.googleads.v16.enums"�
8PolicyTopicEvidenceDestinationNotWorkingDnsErrorTypeEnum"�
4PolicyTopicEvidenceDestinationNotWorkingDnsErrorType
UNSPECIFIED 
UNKNOWN
HOSTNAME_NOT_FOUND
GOOGLE_CRAWLER_DNS_ISSUEB�
"com.google.ads.googleads.v16.enumsB9PolicyTopicEvidenceDestinationNotWorkingDnsErrorTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
8google/ads/googleads/v16/enums/call_to_action_type.protogoogle.ads.googleads.v16.enums"�
CallToActionTypeEnum"�
CallToActionType
UNSPECIFIED 
UNKNOWN

LEARN_MORE
	GET_QUOTE
	APPLY_NOW
SIGN_UP

CONTACT_US
	SUBSCRIBE
DOWNLOAD
BOOK_NOW	
SHOP_NOW

BUY_NOW

DONATE_NOW
	ORDER_NOW

PLAY_NOW
SEE_MORE
	START_NOW

VISIT_SITE
	WATCH_NOWB�
"com.google.ads.googleads.v16.enumsBCallToActionTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
.google/ads/googleads/v16/enums/mime_type.protogoogle.ads.googleads.v16.enums"�
MimeTypeEnum"�
MimeType
UNSPECIFIED 
UNKNOWN

IMAGE_JPEG
	IMAGE_GIF
	IMAGE_PNG	
FLASH
	TEXT_HTML
PDF

MSWORD
MSEXCEL	
RTF

	AUDIO_WAV
	AUDIO_MP3
HTML5_AD_ZIP
B�
"com.google.ads.googleads.v16.enumsB
MimeTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Dgoogle/ads/googleads/v16/enums/lead_form_field_user_input_type.protogoogle.ads.googleads.v16.enums"�
LeadFormFieldUserInputTypeEnum"�
LeadFormFieldUserInputType
UNSPECIFIED 
UNKNOWN
	FULL_NAME	
EMAIL
PHONE_NUMBER
POSTAL_CODE
STREET_ADDRESS
CITY	

REGION

COUNTRY

WORK_EMAIL
COMPANY_NAME


WORK_PHONE
	JOB_TITLE
GOVERNMENT_ISSUED_ID_CPF_BR
GOVERNMENT_ISSUED_ID_DNI_AR
GOVERNMENT_ISSUED_ID_DNI_PE
GOVERNMENT_ISSUED_ID_RUT_CL
GOVERNMENT_ISSUED_ID_CC_CO
GOVERNMENT_ISSUED_ID_CI_EC
GOVERNMENT_ISSUED_ID_RFC_MX

FIRST_NAME
	LAST_NAME

VEHICLE_MODEL�
VEHICLE_TYPE�
PREFERRED_DEALERSHIP�
VEHICLE_PURCHASE_TIMELINE�
VEHICLE_OWNERSHIP�
VEHICLE_PAYMENT_TYPE�
VEHICLE_CONDITION�
COMPANY_SIZE�
ANNUAL_SALES�
YEARS_IN_BUSINESS�
JOB_DEPARTMENT�
JOB_ROLE�
OVER_18_AGE�
OVER_19_AGE�
OVER_20_AGE�
OVER_21_AGE�
OVER_22_AGE�
OVER_23_AGE�
OVER_24_AGE�
OVER_25_AGE�
OVER_26_AGE�
OVER_27_AGE�
OVER_28_AGE�
OVER_29_AGE�
OVER_30_AGE�
OVER_31_AGE�
OVER_32_AGE�
OVER_33_AGE�
OVER_34_AGE�
OVER_35_AGE�
OVER_36_AGE�
OVER_37_AGE�
OVER_38_AGE�
OVER_39_AGE�
OVER_40_AGE�
OVER_41_AGE�
OVER_42_AGE�
OVER_43_AGE�
OVER_44_AGE�
OVER_45_AGE�
OVER_46_AGE�
OVER_47_AGE�
OVER_48_AGE�
OVER_49_AGE�
OVER_50_AGE�
OVER_51_AGE�
OVER_52_AGE�
OVER_53_AGE�
OVER_54_AGE�
OVER_55_AGE�
OVER_56_AGE�
OVER_57_AGE�
OVER_58_AGE�
OVER_59_AGE�
OVER_60_AGE�
OVER_61_AGE�
OVER_62_AGE�
OVER_63_AGE�
OVER_64_AGE�
OVER_65_AGE�
EDUCATION_PROGRAM�
EDUCATION_COURSE�
PRODUCT�
SERVICE�

OFFER�
CATEGORY�
PREFERRED_CONTACT_METHOD�
PREFERRED_LOCATION�
PREFERRED_CONTACT_TIME�
PURCHASE_TIMELINE�
YEARS_OF_EXPERIENCE�
JOB_INDUSTRY�
LEVEL_OF_EDUCATION�

PROPERTY_TYPE�
REALTOR_HELP_GOAL�
PROPERTY_COMMUNITY�
PRICE_RANGE�
NUMBER_OF_BEDROOMS�
FURNISHED_PROPERTY�
PETS_ALLOWED_PROPERTY�
NEXT_PLANNED_PURCHASE�
EVENT_SIGNUP_INTEREST�
PREFERRED_SHOPPING_PLACES�
FAVORITE_BRAND�+
&TRANSPORTATION_COMMERCIAL_LICENSE_TYPE�
EVENT_BOOKING_INTEREST�
DESTINATION_COUNTRY�
DESTINATION_CITY�
DEPARTURE_COUNTRY�
DEPARTURE_CITY�
DEPARTURE_DATE�
RETURN_DATE�
NUMBER_OF_TRAVELERS�

TRAVEL_BUDGET�
TRAVEL_ACCOMMODATION�B�
"com.google.ads.googleads.v16.enumsBLeadFormFieldUserInputTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
Ygoogle/ads/googleads/v16/enums/policy_topic_evidence_destination_not_working_device.protogoogle.ads.googleads.v16.enums"�
2PolicyTopicEvidenceDestinationNotWorkingDeviceEnum"q
.PolicyTopicEvidenceDestinationNotWorkingDevice
UNSPECIFIED 
UNKNOWN
DESKTOP
ANDROID
IOSB�
"com.google.ads.googleads.v16.enumsB3PolicyTopicEvidenceDestinationNotWorkingDeviceProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
,google/ads/googleads/v16/common/policy.protogoogle.ads.googleads.v16.commonXgoogle/ads/googleads/v16/enums/policy_topic_evidence_destination_mismatch_url_type.protoYgoogle/ads/googleads/v16/enums/policy_topic_evidence_destination_not_working_device.protoagoogle/ads/googleads/v16/enums/policy_topic_evidence_destination_not_working_dns_error_type.proto"n
PolicyViolationKey
policy_name (	H �
violating_text (	H�B
_policy_nameB
_violating_text"�
PolicyValidationParameter
ignorable_policy_topics (	Y
exempt_policy_violation_keys (23.google.ads.googleads.v16.common.PolicyViolationKey"�
PolicyTopicEntry
topic (	H �[
type (2M.google.ads.googleads.v16.enums.PolicyTopicEntryTypeEnum.PolicyTopicEntryTypeG
	evidences (24.google.ads.googleads.v16.common.PolicyTopicEvidenceK
constraints (26.google.ads.googleads.v16.common.PolicyTopicConstraintB
_topic"�

PolicyTopicEvidenceX
website_list (<EMAIL> R
	text_list (2=.google.ads.googleads.v16.common.PolicyTopicEvidence.TextListH 

language_code	 (	H i
destination_text_list (2H.google.ads.googleads.v16.common.PolicyTopicEvidence.DestinationTextListH h
destination_mismatch (2H.google.ads.googleads.v16.common.PolicyTopicEvidence.DestinationMismatchH m
destination_not_working (2J.google.ads.googleads.v16.common.PolicyTopicEvidence.DestinationNotWorkingH 
TextList
texts (	
WebsiteList
websites (	0
DestinationTextList
destination_texts (	�
DestinationMismatch�
	url_types (2.google.ads.googleads.v16.enums.PolicyTopicEvidenceDestinationMismatchUrlTypeEnum.PolicyTopicEvidenceDestinationMismatchUrlType�
DestinationNotWorking
expanded_url (	H��
device (2�.google.ads.googleads.v16.enums.PolicyTopicEvidenceDestinationNotWorkingDeviceEnum.PolicyTopicEvidenceDestinationNotWorkingDevice#
last_checked_date_time (	H��
dns_error_type (2�.google.ads.googleads.v16.enums.PolicyTopicEvidenceDestinationNotWorkingDnsErrorTypeEnum.PolicyTopicEvidenceDestinationNotWorkingDnsErrorTypeH 
http_error_code (H B
reasonB

_expanded_urlB
_last_checked_date_timeB
value"�
PolicyTopicConstrainto
country_constraint_list (2L.google.ads.googleads.v16.common.PolicyTopicConstraint.CountryConstraintListH h
reseller_constraint (2I.google.ads.googleads.v16.common.PolicyTopicConstraint.ResellerConstraintH {
#certificate_missing_in_country_list (2L.google.ads.googleads.v16.common.PolicyTopicConstraint.CountryConstraintListH �
+certificate_domain_mismatch_in_country_list (2L.google.ads.googleads.v16.common.PolicyTopicConstraint.CountryConstraintListH �
CountryConstraintList%
total_targeted_countries (H �[
	countries (2H.google.ads.googleads.v16.common.PolicyTopicConstraint.CountryConstraintB
_total_targeted_countries
ResellerConstraintI
CountryConstraint
country_criterion (	H �B
_country_criterionB
valueB�
#com.google.ads.googleads.v16.commonBPolicyProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v16/common;common�GAA�Google.Ads.GoogleAds.V16.Common�Google\\Ads\\GoogleAds\\V16\\Common�#Google::Ads::GoogleAds::V16::Commonbproto3
�
2google/ads/googleads/v16/common/asset_policy.protogoogle.ads.googleads.v16.common>google/ads/googleads/v16/enums/asset_link_primary_status.protoEgoogle/ads/googleads/v16/enums/asset_link_primary_status_reason.protoKgoogle/ads/googleads/v16/enums/asset_offline_evaluation_error_reasons.proto;google/ads/googleads/v16/enums/policy_approval_status.proto9google/ads/googleads/v16/enums/policy_review_status.proto"�
AdAssetPolicySummaryO
policy_topic_entries (21.google.ads.googleads.v16.common.PolicyTopicEntry`

review_status (2I.google.ads.googleads.v16.enums.PolicyReviewStatusEnum.PolicyReviewStatusf
approval_status (2M.google.ads.googleads.v16.enums.PolicyApprovalStatusEnum.PolicyApprovalStatus"�
AssetLinkPrimaryStatusDetailsr
reason (2].google.ads.googleads.v16.enums.AssetLinkPrimaryStatusReasonEnum.AssetLinkPrimaryStatusReasonH�f
status (2Q.google.ads.googleads.v16.enums.AssetLinkPrimaryStatusEnum.AssetLinkPrimaryStatusH�N
asset_disapproved (21.google.ads.googleads.v16.common.AssetDisapprovedH B	
detailsB	
_reasonB	
_status"�
AssetDisapproved�
 offline_evaluation_error_reasons (2i.google.ads.googleads.v16.enums.AssetOfflineEvaluationErrorReasonsEnum.AssetOfflineEvaluationErrorReasonsB�
#com.google.ads.googleads.v16.commonBAssetPolicyProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v16/common;common�GAA�Google.Ads.GoogleAds.V16.Common�Google\\Ads\\GoogleAds\\V16\\Common�#Google::Ads::GoogleAds::V16::Commonbproto3
�
.google/ads/googleads/v16/common/ad_asset.protogoogle.ads.googleads.v16.common<google/ads/googleads/v16/enums/asset_performance_label.proto<google/ads/googleads/v16/enums/served_asset_field_type.proto"�
AdTextAsset
text (	H �c
pinned_field (2M.google.ads.googleads.v16.enums.ServedAssetFieldTypeEnum.ServedAssetFieldTypep
asset_performance_label (2O.google.ads.googleads.v16.enums.AssetPerformanceLabelEnum.AssetPerformanceLabelR
policy_summary_info (25.google.ads.googleads.v16.common.AdAssetPolicySummaryB
_text",
AdImageAsset
asset (	H �B
_asset",
AdVideoAsset
asset (	H �B
_asset"2
AdMediaBundleAsset
asset (	H �B
_asset"<
AdDiscoveryCarouselCardAsset
asset (	H �B
_asset"3
AdCallToActionAsset
asset (	H �B
_assetB�
#com.google.ads.googleads.v16.commonBAdAssetProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v16/common;common�GAA�Google.Ads.GoogleAds.V16.Common�Google\\Ads\\GoogleAds\\V16\\Common�#Google::Ads::GoogleAds::V16::Commonbproto3
�
@google/ads/googleads/v16/enums/location_group_radius_units.protogoogle.ads.googleads.v16.enums"�
LocationGroupRadiusUnitsEnum"`
LocationGroupRadiusUnits
UNSPECIFIED 
UNKNOWN

METERS	
MILES
MILLI_MILESB�
"com.google.ads.googleads.v16.enumsBLocationGroupRadiusUnitsProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
6google/ads/googleads/v16/enums/mobile_app_vendor.protogoogle.ads.googleads.v16.enums"q
MobileAppVendorEnum"Z
MobileAppVendor
UNSPECIFIED 
UNKNOWN
APPLE_APP_STORE
GOOGLE_APP_STOREB�
"com.google.ads.googleads.v16.enumsBMobileAppVendorProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
5google/ads/googleads/v16/enums/interaction_type.protogoogle.ads.googleads.v16.enums"R
InteractionTypeEnum";
InteractionType
UNSPECIFIED 
UNKNOWN

CALLS�>B�
"com.google.ads.googleads.v16.enumsBInteractionTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�\\
.google/ads/googleads/v16/common/criteria.protogoogle.ads.googleads.v16.common;google/ads/googleads/v16/enums/app_payment_model_type.protoCgoogle/ads/googleads/v16/enums/brand_request_rejection_reason.proto0google/ads/googleads/v16/enums/brand_state.proto7google/ads/googleads/v16/enums/content_label_type.proto0google/ads/googleads/v16/enums/day_of_week.proto+google/ads/googleads/v16/enums/device.proto0google/ads/googleads/v16/enums/gender_type.proto>google/ads/googleads/v16/enums/hotel_date_selection_type.proto6google/ads/googleads/v16/enums/income_range_type.proto5google/ads/googleads/v16/enums/interaction_type.proto7google/ads/googleads/v16/enums/keyword_match_type.proto7google/ads/googleads/v16/enums/listing_group_type.proto@google/ads/googleads/v16/enums/location_group_radius_units.proto3google/ads/googleads/v16/enums/minute_of_hour.proto9google/ads/googleads/v16/enums/parental_status_type.proto;google/ads/googleads/v16/enums/product_category_level.proto4google/ads/googleads/v16/enums/product_channel.proto@google/ads/googleads/v16/enums/product_channel_exclusivity.proto6google/ads/googleads/v16/enums/product_condition.protoCgoogle/ads/googleads/v16/enums/product_custom_attribute_index.proto7google/ads/googleads/v16/enums/product_type_level.proto;google/ads/googleads/v16/enums/proximity_radius_units.proto>google/ads/googleads/v16/enums/webpage_condition_operand.proto?google/ads/googleads/v16/enums/webpage_condition_operator.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�
KeywordInfo
text (	H �Y

match_type (2E.google.ads.googleads.v16.enums.KeywordMatchTypeEnum.KeywordMatchTypeB
_text")

PlacementInfo
url (	H �B
_url"A
NegativeKeywordListInfo

shared_set (	H �B
_shared_set"�
MobileAppCategoryInfob
mobile_app_category_constant (	B7�A4
2googleads.googleapis.com/MobileAppCategoryConstantH �B
_mobile_app_category_constant"S
MobileApplicationInfo
app_id (	H �
name (	H�B	
_app_idB
_name"H
LocationInfo 
geo_target_constant (	H �B
_geo_target_constant"M

DeviceInfo?
type (21.google.ads.googleads.v16.enums.DeviceEnum.Device"�
ListingGroupInfoS
type (2E.google.ads.googleads.v16.enums.ListingGroupTypeEnum.ListingGroupTypeI

case_value (25.google.ads.googleads.v16.common.ListingDimensionInfo&
parent_ad_group_criterion (	H �H
path (25.google.ads.googleads.v16.common.ListingDimensionPathH�B
_parent_ad_group_criterionB
_path"a
ListingDimensionPathI

dimensions (25.google.ads.googleads.v16.common.ListingDimensionInfo"]
ListingScopeInfoI

dimensions (25.google.ads.googleads.v16.common.ListingDimensionInfo"�
ListingDimensionInfo@
hotel_id (2,.google.ads.googleads.v16.common.HotelIdInfoH F
hotel_class (2/.google.ads.googleads.v16.common.HotelClassInfoH W
hotel_country_region (27.google.ads.googleads.v16.common.HotelCountryRegionInfoH F
hotel_state (2/.google.ads.googleads.v16.common.HotelStateInfoH D

hotel_city (2..google.ads.googleads.v16.common.HotelCityInfoH P
product_category (24.google.ads.googleads.v16.common.ProductCategoryInfoH J

product_brand (21.google.ads.googleads.v16.common.ProductBrandInfoH N
product_channel (23.google.ads.googleads.v16.common.ProductChannelInfoH e
product_channel_exclusivity	 (2>.google.ads.googleads.v16.common.ProductChannelExclusivityInfoH R
product_condition
 (25.google.ads.googleads.v16.common.ProductConditionInfoH _
product_custom_attribute (2;.google.ads.googleads.v16.common.ProductCustomAttributeInfoH M
product_item_id (22.google.ads.googleads.v16.common.ProductItemIdInfoH H
product_type (20.google.ads.googleads.v16.common.ProductTypeInfoH P
product_grouping (24.google.ads.googleads.v16.common.ProductGroupingInfoH L
product_labels (22.google.ads.googleads.v16.common.ProductLabelsInfoH _
product_legacy_condition (2;.google.ads.googleads.v16.common.ProductLegacyConditionInfoH Q
product_type_full (24.google.ads.googleads.v16.common.ProductTypeFullInfoH F
activity_id (2/.google.ads.googleads.v16.common.ActivityIdInfoH N
activity_rating (23.google.ads.googleads.v16.common.ActivityRatingInfoH P
activity_country (24.google.ads.googleads.v16.common.ActivityCountryInfoH L
activity_state (22.google.ads.googleads.v16.common.ActivityStateInfoH J

activity_city (21.google.ads.googleads.v16.common.ActivityCityInfoH a
unknown_listing_dimension (2<.google.ads.googleads.v16.common.UnknownListingDimensionInfoH B
	dimension"+
HotelIdInfo
value (	H �B
_value".
HotelClassInfo
value (H �B
_value"\\
HotelCountryRegionInfo%
country_region_criterion (	H �B
_country_region_criterion"B
HotelStateInfo
state_criterion (	H �B
_state_criterion"?

HotelCityInfo
city_criterion (	H �B
_city_criterion"�
ProductCategoryInfo
category_id (H �\\
level (2M.google.ads.googleads.v16.enums.ProductCategoryLevelEnum.ProductCategoryLevelB
_category_id"0
ProductBrandInfo
value (	H �B
_value"h
ProductChannelInfoR
channel (2A.google.ads.googleads.v16.enums.ProductChannelEnum.ProductChannel"�
ProductChannelExclusivityInfot
channel_exclusivity (2W.google.ads.googleads.v16.enums.ProductChannelExclusivityEnum.ProductChannelExclusivity"p
ProductConditionInfoX
	condition (2E.google.ads.googleads.v16.enums.ProductConditionEnum.ProductCondition"�
ProductCustomAttributeInfo
value (	H �j
index (2[.google.ads.googleads.v16.enums.ProductCustomAttributeIndexEnum.ProductCustomAttributeIndexB
_value"1
ProductItemIdInfo
value (	H �B
_value"�
ProductTypeInfo
value (	H �T
level (2E.google.ads.googleads.v16.enums.ProductTypeLevelEnum.ProductTypeLevelB
_value"3
ProductGroupingInfo
value (	H �B
_value"1
ProductLabelsInfo
value (	H �B
_value":
ProductLegacyConditionInfo
value (	H �B
_value"3
ProductTypeFullInfo
value (	H �B
_value"
UnknownListingDimensionInfo"}
HotelDateSelectionTypeInfo_
type (2Q.google.ads.googleads.v16.enums.HotelDateSelectionTypeEnum.HotelDateSelectionType"g
HotelAdvanceBookingWindowInfo
min_days (H �
max_days (H�B
	_min_daysB
	_max_days"g
HotelLengthOfStayInfo

min_nights (H �

max_nights (H�B
_min_nightsB
_max_nights"A
HotelCheckInDateRangeInfo

start_date (	
end_date (	"c
HotelCheckInDayInfoL
day_of_week (27.google.ads.googleads.v16.enums.DayOfWeekEnum.DayOfWeek".
ActivityIdInfo
value (	H �B
_value"2
ActivityRatingInfo
value (H �B
_value"3
ActivityCountryInfo
value (	H �B
_value"1
ActivityStateInfo
value (	H �B
_value"0
ActivityCityInfo
value (	H �B
_value"h
InteractionTypeInfoQ
type (2C.google.ads.googleads.v16.enums.InteractionTypeEnum.InteractionType"�
AdScheduleInfoS
start_minute (2=.google.ads.googleads.v16.enums.MinuteOfHourEnum.MinuteOfHourQ

end_minute (2=.google.ads.googleads.v16.enums.MinuteOfHourEnum.MinuteOfHour

start_hour (H �
end_hour (H�L
day_of_week (27.google.ads.googleads.v16.enums.DayOfWeekEnum.DayOfWeekB
_start_hourB
	_end_hour"[
AgeRangeInfoK
type (2=.google.ads.googleads.v16.enums.AgeRangeTypeEnum.AgeRangeType"U

GenderInfoG
type (29.google.ads.googleads.v16.enums.GenderTypeEnum.GenderType"d
IncomeRangeInfoQ
type (2C.google.ads.googleads.v16.enums.IncomeRangeTypeEnum.IncomeRangeType"m
ParentalStatusInfoW
type (2I.google.ads.googleads.v16.enums.ParentalStatusTypeEnum.ParentalStatusType"6
YouTubeVideoInfo
video_id (	H �B
	_video_id"<
YouTubeChannelInfo

channel_id (	H �B
_channel_id"4
UserListInfo
	user_list (	H �B

_user_list"�

ProximityInfo@
	geo_point (2-.google.ads.googleads.v16.common.GeoPointInfo
radius (H �c
radius_units (2M.google.ads.googleads.v16.enums.ProximityRadiusUnitsEnum.ProximityRadiusUnits=
address (2,.google.ads.googleads.v16.common.AddressInfoB	
_radius"�
GeoPointInfo\'
longitude_in_micro_degrees (H �&
latitude_in_micro_degrees (H�B
_longitude_in_micro_degreesB
_latitude_in_micro_degrees"�
AddressInfo
postal_code (	H �

province_code	 (	H�
country_code
 (	H�

province_name (	H�
street_address (	H�
street_address2
 (	H�
	city_name (	H�B
_postal_codeB
_province_codeB

_country_codeB
_province_nameB
_street_addressB
_street_address2B

_city_name"v
	TopicInfoH
topic_constant (	B+�A(
&googleads.googleapis.com/TopicConstantH �
path (	B
_topic_constant"D
LanguageInfo
language_constant (	H �B
_language_constant"5
IpBlockInfo

ip_address (	H �B
_ip_address"g
ContentLabelInfoS
type (2E.google.ads.googleads.v16.enums.ContentLabelTypeEnum.ContentLabelType"p
CarrierInfoL
carrier_constant (	B-�A*
(googleads.googleapis.com/CarrierConstantH �B
_carrier_constant"R
UserInterestInfo#
user_interest_category (	H �B
_user_interest_category"�
WebpageInfo
criterion_name (	H �I

conditions (25.google.ads.googleads.v16.common.WebpageConditionInfo
coverage_percentage (B
sample (22.google.ads.googleads.v16.common.WebpageSampleInfoB
_criterion_name"�
WebpageConditionInfod
operand (2S.google.ads.googleads.v16.enums.WebpageConditionOperandEnum.WebpageConditionOperandg
operator (2U.google.ads.googleads.v16.enums.WebpageConditionOperatorEnum.WebpageConditionOperator
argument (	H �B
	_argument"(
WebpageSampleInfo
sample_urls (	"�
OperatingSystemVersionInfol
!operating_system_version_constant (	B<�A9
7googleads.googleapis.com/OperatingSystemVersionConstantH �B$
"_operating_system_version_constant"p
AppPaymentModelInfoY
type (2K.google.ads.googleads.v16.enums.AppPaymentModelTypeEnum.AppPaymentModelType"�
MobileDeviceInfoW
mobile_device_constant (	B2�A/
-googleads.googleapis.com/MobileDeviceConstantH �B
_mobile_device_constant"F
CustomAffinityInfo
custom_affinity (	H �B
_custom_affinity"@
CustomIntentInfo

custom_intent (	H �B
_custom_intent"�
LocationGroupInfo
feed (	H �
geo_target_constants (	
radius (H�k
radius_units (2U.google.ads.googleads.v16.enums.LocationGroupRadiusUnitsEnum.LocationGroupRadiusUnits
feed_item_sets (	5
(enable_customer_level_location_asset_set	 (H�!
location_group_asset_sets
 (	B
_feedB	
_radiusB+
)_enable_customer_level_location_asset_set"-
CustomAudienceInfo
custom_audience (	"a
CombinedAudienceInfoI
combined_audience (	B.�A+
)googleads.googleapis.com/CombinedAudience" 
AudienceInfo
audience (	"�
KeywordThemeInfoT
keyword_theme_constant (	B2�A/
-googleads.googleapis.com/KeywordThemeConstantH !
free_form_keyword_theme (	H B

keyword_theme"(
LocalServiceIdInfo

service_id (	"
SearchThemeInfo
text (	"�
	BrandInfo
display_name (	B�AH �
	entity_id (	H�
primary_url (	B�AH�
rejection_reason (2[.google.ads.googleads.v16.enums.BrandRequestRejectionReasonEnum.BrandRequestRejectionReasonB�AH�S
status (29.google.ads.googleads.v16.enums.BrandStateEnum.BrandStateB�AH�B

_display_nameB

_entity_idB
_primary_urlB
_rejection_reasonB	
_status"7

BrandListInfo

shared_set (	H �B
_shared_setB�
#com.google.ads.googleads.v16.commonB
CriteriaProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v16/common;common�GAA�Google.Ads.GoogleAds.V16.Common�Google\\Ads\\GoogleAds\\V16\\Common�#Google::Ads::GoogleAds::V16::Commonbproto3
�N
1google/ads/googleads/v16/common/asset_types.protogoogle.ads.googleads.v16.common1google/ads/googleads/v16/common/feed_common.protoDgoogle/ads/googleads/v16/enums/call_conversion_reporting_state.proto8google/ads/googleads/v16/enums/call_to_action_type.protoBgoogle/ads/googleads/v16/enums/lead_form_call_to_action_type.proto=google/ads/googleads/v16/enums/lead_form_desired_intent.protoDgoogle/ads/googleads/v16/enums/lead_form_field_user_input_type.protoNgoogle/ads/googleads/v16/enums/lead_form_post_submit_call_to_action_type.proto<google/ads/googleads/v16/enums/location_ownership_type.proto.google/ads/googleads/v16/enums/mime_type.proto6google/ads/googleads/v16/enums/mobile_app_vendor.protoDgoogle/ads/googleads/v16/enums/price_extension_price_qualifier.proto?google/ads/googleads/v16/enums/price_extension_price_unit.proto9google/ads/googleads/v16/enums/price_extension_type.protoJgoogle/ads/googleads/v16/enums/promotion_extension_discount_modifier.protoAgoogle/ads/googleads/v16/enums/promotion_extension_occasion.protogoogle/api/field_behavior.protogoogle/api/resource.proto"d
YoutubeVideoAsset
youtube_video_id (	H �
youtube_video_title (	B
_youtube_video_id".
MediaBundleAsset
data (H �B
_data"�

ImageAsset
data (H �
	file_size (H�H
	mime_type (25.google.ads.googleads.v16.enums.MimeTypeEnum.MimeTypeB
	full_size (2/.google.ads.googleads.v16.common.ImageDimensionB
_dataB

_file_size"�
ImageDimension

height_pixels (H �
width_pixels (H�
url (	H�B
_height_pixelsB

_width_pixelsB
_url"\'
	TextAsset
text (	H �B
_text"�

LeadFormAsset

business_name
 (	B�Aw
call_to_action_type (2U.google.ads.googleads.v16.enums.LeadFormCallToActionTypeEnum.LeadFormCallToActionTypeB�A\'
call_to_action_description (	B�A
headline (	B�A
description
 (	B�A
privacy_policy_url (	B�A!
post_submit_headline (	H �$
post_submit_description (	H�>
fields (2..google.ads.googleads.v16.common.LeadFormField\\
custom_question_fields (2<.google.ads.googleads.v16.common.LeadFormCustomQuestionFieldQ
delivery_methods	 (27.google.ads.googleads.v16.common.LeadFormDeliveryMethod�
post_submit_call_to_action_type (2i.google.ads.googleads.v16.enums.LeadFormPostSubmitCallToActionTypeEnum.LeadFormPostSubmitCallToActionType#
background_image_asset (	H�g
desired_intent (2O.google.ads.googleads.v16.enums.LeadFormDesiredIntentEnum.LeadFormDesiredIntent
custom_disclosure (	H�B
_post_submit_headlineB
_post_submit_descriptionB
_background_image_assetB
_custom_disclosure"�

LeadFormFieldm

input_type (2Y.google.ads.googleads.v16.enums.LeadFormFieldUserInputTypeEnum.LeadFormFieldUserInputType]
single_choice_answers (2<.google.ads.googleads.v16.common.LeadFormSingleChoiceAnswersH 
has_location_answer (H B	
answers"�
LeadFormCustomQuestionField
custom_question_text (	]
single_choice_answers (2<.google.ads.googleads.v16.common.LeadFormSingleChoiceAnswersH 
has_location_answer (H B	
answers".
LeadFormSingleChoiceAnswers
answers (	"q
LeadFormDeliveryMethodC
webhook (20.google.ads.googleads.v16.common.WebhookDeliveryH B
delivery_details"�
WebhookDelivery#
advertiser_webhook_url (	H �

google_secret (	H�#
payload_schema_version (H�B
_advertiser_webhook_urlB
_google_secretB
_payload_schema_version"
BookOnGoogleAsset"�
PromotionAsset
promotion_target (	B�A�
discount_modifier (2i.google.ads.googleads.v16.enums.PromotionExtensionDiscountModifierEnum.PromotionExtensionDiscountModifier
redemption_start_date (	
redemption_end_date (	k
occasion	 (2Y.google.ads.googleads.v16.enums.PromotionExtensionOccasionEnum.PromotionExtensionOccasion

language_code
 (	

start_date (	
end_date (	L
ad_schedule_targets
 (2/.google.ads.googleads.v16.common.AdScheduleInfo
percent_off (H B
money_amount_off (2&.google.ads.googleads.v16.common.MoneyH 
promotion_code (	HD
orders_over_amount (2&.google.ads.googleads.v16.common.MoneyHB

discount_typeB
promotion_trigger"�
CalloutAsset
callout_text (	B�A

start_date (	
end_date (	L
ad_schedule_targets (2/.google.ads.googleads.v16.common.AdScheduleInfo"B
StructuredSnippetAsset
header (	B�A
values (	B�A"�

SitelinkAsset
	link_text (	B�A
description1 (	
description2 (	

start_date (	
end_date (	L
ad_schedule_targets (2/.google.ads.googleads.v16.common.AdScheduleInfo"6

PageFeedAsset
page_url (	B�A
labels (	"�
DynamicEducationAsset

program_id (	B�A
location_id (	
program_name (	B�A
subject (	
program_description (	
school_name (	
address (	
contextual_keywords (	
android_app_link	 (	
similar_program_ids
 (	
ios_app_link (	
ios_app_store_id (
thumbnail_image_url
 (	
	image_url (	"�
MobileAppAsset
app_id (	B�A[
	app_store (2C.google.ads.googleads.v16.enums.MobileAppVendorEnum.MobileAppVendorB�A
	link_text (	B�A

start_date (	
end_date (	"B
HotelCalloutAsset
text (	B�A

language_code (	B�A"�
	CallAsset
country_code (	B�A
phone_number (	B�A�
call_conversion_reporting_state (2].google.ads.googleads.v16.enums.CallConversionReportingStateEnum.CallConversionReportingStateN
call_conversion_action (	B.�A+
)googleads.googleapis.com/ConversionActionL
ad_schedule_targets (2/.google.ads.googleads.v16.common.AdScheduleInfo"�

PriceAsset\\
type (2I.google.ads.googleads.v16.enums.PriceExtensionTypeEnum.PriceExtensionTypeB�Av
price_qualifier (2].google.ads.googleads.v16.enums.PriceExtensionPriceQualifierEnum.PriceExtensionPriceQualifier

language_code (	B�AG
price_offerings (2..google.ads.googleads.v16.common.PriceOffering"�

PriceOffering
header (	B�A
description (	B�A:
price (2&.google.ads.googleads.v16.common.MoneyB�Aa
unit (2S.google.ads.googleads.v16.enums.PriceExtensionPriceUnitEnum.PriceExtensionPriceUnit
	final_url (	B�A
final_mobile_url (	"r
CallToActionAsset]
call_to_action (2E.google.ads.googleads.v16.enums.CallToActionTypeEnum.CallToActionType"�
DynamicRealEstateAsset

listing_id (	B�A
listing_name (	B�A
	city_name (	
description (	
address (	
price (	
	image_url (	

property_type (	
listing_type	 (	
contextual_keywords
 (	
formatted_price (	
android_app_link (	
ios_app_link
 (	
ios_app_store_id (
similar_listing_ids (	"�
DynamicCustomAsset
id (	B�A
id2 (	

item_title (	B�A

item_subtitle (	
item_description (	
item_address (	

item_category (	
price (	

sale_price	 (	
formatted_price
 (	
formatted_sale_price (	
	image_url (	
contextual_keywords
 (	
android_app_link (	
ios_app_link (	
ios_app_store_id (
similar_ids (	"�
DynamicHotelsAndRentalsAsset
property_id (	B�A

property_name (	B�A
	image_url (	
destination_name (	
description (	
price (	

sale_price (	
star_rating (
category	 (	
contextual_keywords
 (	
address (	
android_app_link (	
ios_app_link
 (	
ios_app_store_id (
formatted_price (	
formatted_sale_price (	
similar_property_ids (	"�
DynamicFlightsAsset
destination_id (	B�A
	origin_id (	
flight_description (	B�A
	image_url (	
destination_name (	
origin_name (	
flight_price (	
flight_sale_price (	
formatted_price	 (	
formatted_sale_price
 (	
android_app_link (	
ios_app_link (	
ios_app_store_id
 (
similar_destination_ids (	
custom_mapping (	"�
DiscoveryCarouselCardAsset
marketing_image_asset (	$
square_marketing_image_asset (	&
portrait_marketing_image_asset (	
headline (	B�A
call_to_action_text (	"�
DynamicTravelAsset
destination_id (	B�A
	origin_id (	
title (	B�A
destination_name (	
destination_address (	
origin_name (	
price (	

sale_price (	
formatted_price	 (	
formatted_sale_price
 (	
category (	
contextual_keywords (	
similar_destination_ids
 (	
	image_url (	
android_app_link (	
ios_app_link (	
ios_app_store_id ("�
DynamicLocalAsset
deal_id (	B�A
	deal_name (	B�A
subtitle (	
description (	
price (	

sale_price (	
	image_url (	
address (	
category	 (	
contextual_keywords
 (	
formatted_price (	
formatted_sale_price (	
android_app_link
 (	
similar_deal_ids (	
ios_app_link (	
ios_app_store_id ("�
DynamicJobsAsset
job_id (	B�A
location_id (	
	job_title (	B�A
job_subtitle (	
description (	
	image_url (	
job_category (	
contextual_keywords (	
address	 (	
salary
 (	
android_app_link (	
similar_job_ids (	
ios_app_link
 (	
ios_app_store_id ("�

LocationAsset
place_id (	\\
business_profile_locations (28.google.ads.googleads.v16.common.BusinessProfileLocationp
location_ownership_type (2O.google.ads.googleads.v16.enums.LocationOwnershipTypeEnum.LocationOwnershipType"Q
BusinessProfileLocation
labels (	

store_code (	

listing_id ("Q
HotelPropertyAsset
place_id (	

hotel_address (	

hotel_name (	B�
#com.google.ads.googleads.v16.commonBAssetTypesProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v16/common;common�GAA�Google.Ads.GoogleAds.V16.Common�Google\\Ads\\GoogleAds\\V16\\Common�#Google::Ads::GoogleAds::V16::Commonbproto3
�#
.google/ads/googleads/v16/resources/asset.proto"google.ads.googleads.v16.resources6google/ads/googleads/v16/common/custom_parameter.proto,google/ads/googleads/v16/common/policy.proto5google/ads/googleads/v16/enums/asset_field_type.proto1google/ads/googleads/v16/enums/asset_source.proto/google/ads/googleads/v16/enums/asset_type.proto;google/ads/googleads/v16/enums/policy_approval_status.proto9google/ads/googleads/v16/enums/policy_review_status.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�
Asset=

resource_name (	B&�A�A 
googleads.googleapis.com/Asset
id (B�AH�
name (	H�J
type (27.google.ads.googleads.v16.enums.AssetTypeEnum.AssetTypeB�A

final_urls (	
final_mobile_urls (	"
tracking_url_template (	H�O
url_custom_parameters (20.google.ads.googleads.v16.common.CustomParameter
final_url_suffix (	H�P
source& (2;.google.ads.googleads.v16.enums.AssetSourceEnum.AssetSourceB�AS
policy_summary
 (26.google.ads.googleads.v16.resources.AssetPolicySummaryB�Ai
field_type_policy_summaries( (2?.google.ads.googleads.v16.resources.AssetFieldTypePolicySummaryB�AV
youtube_video_asset (22.google.ads.googleads.v16.common.YoutubeVideoAssetB�AH T
media_bundle_asset (21.google.ads.googleads.v16.common.MediaBundleAssetB�AH G
image_asset (2+.google.ads.googleads.v16.common.ImageAssetB�AH E

text_asset (2*.google.ads.googleads.v16.common.TextAssetB�AH I
lead_form_asset	 (2..google.ads.googleads.v16.common.LeadFormAssetH R
book_on_google_asset
 (22.google.ads.googleads.v16.common.BookOnGoogleAssetH J
promotion_asset (2/.google.ads.googleads.v16.common.PromotionAssetH F

callout_asset (2-.google.ads.googleads.v16.common.CalloutAssetH [
structured_snippet_asset (27.google.ads.googleads.v16.common.StructuredSnippetAssetH H
sitelink_asset (2..google.ads.googleads.v16.common.SitelinkAssetH I
page_feed_asset (2..google.ads.googleads.v16.common.PageFeedAssetH Y
dynamic_education_asset (26.google.ads.googleads.v16.common.DynamicEducationAssetH K
mobile_app_asset (2/.google.ads.googleads.v16.common.MobileAppAssetH Q
hotel_callout_asset (22.google.ads.googleads.v16.common.HotelCalloutAssetH @

call_asset (2*.google.ads.googleads.v16.common.CallAssetH B
price_asset (2+.google.ads.googleads.v16.common.PriceAssetH W
call_to_action_asset (22.google.ads.googleads.v16.common.CallToActionAssetB�AH \\
dynamic_real_estate_asset (27.google.ads.googleads.v16.common.DynamicRealEstateAssetH S
dynamic_custom_asset (23.google.ads.googleads.v16.common.DynamicCustomAssetH i
 dynamic_hotels_and_rentals_asset  (2=.google.ads.googleads.v16.common.DynamicHotelsAndRentalsAssetH U
dynamic_flights_asset! (24.google.ads.googleads.v16.common.DynamicFlightsAssetH i
discovery_carousel_card_asset" (2;.google.ads.googleads.v16.common.DiscoveryCarouselCardAssetB�AH S
dynamic_travel_asset# (23.google.ads.googleads.v16.common.DynamicTravelAssetH Q
dynamic_local_asset$ (22.google.ads.googleads.v16.common.DynamicLocalAssetH O
dynamic_jobs_asset% (21.google.ads.googleads.v16.common.DynamicJobsAssetH M
location_asset\' (2..google.ads.googleads.v16.common.LocationAssetB�AH X
hotel_property_asset) (23.google.ads.googleads.v16.common.HotelPropertyAssetB�AH :N�AK
googleads.googleapis.com/Asset)customers/{customer_id}/assets/{asset_id}B

asset_dataB
_idB
_nameB
_tracking_url_templateB
_final_url_suffix"�
AssetFieldTypePolicySummarye
asset_field_type (2A.google.ads.googleads.v16.enums.AssetFieldTypeEnum.AssetFieldTypeB�AH �[
asset_source (2;.google.ads.googleads.v16.enums.AssetSourceEnum.AssetSourceB�AH�]
policy_summary_info (26.google.ads.googleads.v16.resources.AssetPolicySummaryB�AH�B
_asset_field_typeB

_asset_sourceB
_policy_summary_info"�
AssetPolicySummaryT
policy_topic_entries (21.google.ads.googleads.v16.common.PolicyTopicEntryB�Ae

review_status (2I.google.ads.googleads.v16.enums.PolicyReviewStatusEnum.PolicyReviewStatusB�Ak
approval_status (2M.google.ads.googleads.v16.enums.PolicyApprovalStatusEnum.PolicyApprovalStatusB�AB�
&com.google.ads.googleads.v16.resourcesB
AssetProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources�GAA�"Google.Ads.GoogleAds.V16.Resources�"Google\\Ads\\GoogleAds\\V16\\Resources�&Google::Ads::GoogleAds::V16::Resourcesbproto3
�
@google/ads/googleads/v16/enums/display_upload_product_type.protogoogle.ads.googleads.v16.enums"�
DisplayUploadProductTypeEnum"�
DisplayUploadProductType
UNSPECIFIED 
UNKNOWN
HTML5_UPLOAD_AD
DYNAMIC_HTML5_EDUCATION_AD
DYNAMIC_HTML5_FLIGHT_AD!
DYNAMIC_HTML5_HOTEL_RENTAL_AD
DYNAMIC_HTML5_JOB_AD
DYNAMIC_HTML5_LOCAL_AD 
DYNAMIC_HTML5_REAL_ESTATE_AD
DYNAMIC_HTML5_CUSTOM_AD	
DYNAMIC_HTML5_TRAVEL_AD

DYNAMIC_HTML5_HOTEL_ADB�
"com.google.ads.googleads.v16.enumsBDisplayUploadProductTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�S
3google/ads/googleads/v16/common/ad_type_infos.protogoogle.ads.googleads.v16.commonDgoogle/ads/googleads/v16/enums/call_conversion_reporting_state.proto>google/ads/googleads/v16/enums/display_ad_format_setting.proto@google/ads/googleads/v16/enums/display_upload_product_type.protoDgoogle/ads/googleads/v16/enums/legacy_app_install_ad_app_store.proto.google/ads/googleads/v16/enums/mime_type.proto4google/ads/googleads/v16/enums/video_thumbnail.protogoogle/api/field_behavior.proto"�

TextAdInfo
headline (	H �
description1 (	H�
description2 (	H�B
	_headlineB

_description1B

_description2"�
ExpandedTextAdInfo
headline_part1 (	H �
headline_part2	 (	H�
headline_part3
 (	H�
description (	H�
description2 (	H�
path1
 (	H�
path2 (	H�B
_headline_part1B
_headline_part2B
_headline_part3B
_descriptionB

_description2B
_path1B
_path2"s
ExpandedDynamicSearchAdInfo
description (	H �
description2 (	H�B
_descriptionB

_description2"
HotelAdInfo"
TravelAdInfo"
ShoppingSmartAdInfo"
ShoppingProductAdInfo"E
ShoppingComparisonListingAdInfo
headline (	H �B
	_headline"�
ImageAdInfo
pixel_width (H�
pixel_height (H�
	image_url (	H� 
preview_pixel_width (H�!
preview_pixel_height (H�
preview_image_url (	H�H
	mime_type
 (25.google.ads.googleads.v16.enums.MimeTypeEnum.MimeType
name (	H�D
image_asset (2-.google.ads.googleads.v16.common.AdImageAssetH 
data
 (H "
ad_id_to_copy_image_from (H B
imageB
_pixel_widthB

_pixel_heightB

_image_urlB
_preview_pixel_widthB
_preview_pixel_heightB
_preview_image_urlB
_name"�
VideoBumperInStreamAdInfoG
companion_banner (2-.google.ads.googleads.v16.common.AdImageAsset
action_button_label (	
action_headline (	"�
VideoNonSkippableInStreamAdInfoG
companion_banner (2-.google.ads.googleads.v16.common.AdImageAsset
action_button_label (	
action_headline (	"�
VideoTrueViewInStreamAdInfo
action_button_label (	
action_headline (	G
companion_banner (2-.google.ads.googleads.v16.common.AdImageAsset"=
VideoOutstreamAdInfo
headline (	
description (	"�
InFeedVideoAdInfo
headline (	
description1 (	
description2 (	T
	thumbnail (2A.google.ads.googleads.v16.enums.VideoThumbnailEnum.VideoThumbnail"�
VideoAdInfo<
video (2-.google.ads.googleads.v16.common.AdVideoAssetQ
	in_stream (2<.google.ads.googleads.v16.common.VideoTrueViewInStreamAdInfoH L
bumper (2:.google.ads.googleads.v16.common.VideoBumperInStreamAdInfoH K

out_stream (25.google.ads.googleads.v16.common.VideoOutstreamAdInfoH Y

non_skippable (<EMAIL> E
in_feed	 (22.google.ads.googleads.v16.common.InFeedVideoAdInfoH B
format"�
VideoResponsiveAdInfo?
	headlines (2,.google.ads.googleads.v16.common.AdTextAssetD
long_headlines (2,.google.ads.googleads.v16.common.AdTextAssetB
descriptions (2,.google.ads.googleads.v16.common.AdTextAssetE
call_to_actions (2,.google.ads.googleads.v16.common.AdTextAsset=
videos (2-.google.ads.googleads.v16.common.AdVideoAssetH
companion_banners (2-.google.ads.googleads.v16.common.AdImageAsset
breadcrumb1 (	
breadcrumb2 (	"�
ResponsiveSearchAdInfo?
	headlines (2,.google.ads.googleads.v16.common.AdTextAssetB
descriptions (2,.google.ads.googleads.v16.common.AdTextAsset
path1 (	H �
path2 (	H�B
_path1B
_path2"�
LegacyResponsiveDisplayAdInfo
short_headline (	H �

long_headline (	H�
description (	H�

business_name (	H�!
allow_flexible_color (H�
accent_color (	H�

main_color (	H� 
call_to_action_text (	H�

logo_image (	H�
square_logo_image (	H	�
marketing_image (	H
�#
square_marketing_image (	H�i
format_setting
 (2Q.google.ads.googleads.v16.enums.DisplayAdFormatSettingEnum.DisplayAdFormatSetting
price_prefix (	H�

promo_text (	H
�B
_short_headlineB
_long_headlineB
_descriptionB
_business_nameB
_allow_flexible_colorB

_accent_colorB
_main_colorB
_call_to_action_textB
_logo_imageB
_square_logo_imageB
_marketing_imageB
_square_marketing_imageB

_price_prefixB
_promo_text"�
	AppAdInfoG
mandatory_ad_text (2,.google.ads.googleads.v16.common.AdTextAsset?
	headlines (2,.google.ads.googleads.v16.common.AdTextAssetB
descriptions (2,.google.ads.googleads.v16.common.AdTextAsset=
images (2-.google.ads.googleads.v16.common.AdImageAssetE
youtube_videos (2-.google.ads.googleads.v16.common.AdVideoAssetP
html5_media_bundles (23.google.ads.googleads.v16.common.AdMediaBundleAsset"�
AppEngagementAdInfo?
	headlines (2,.google.ads.googleads.v16.common.AdTextAssetB
descriptions (2,.google.ads.googleads.v16.common.AdTextAsset=
images (2-.google.ads.googleads.v16.common.AdImageAsset=
videos (2-.google.ads.googleads.v16.common.AdVideoAsset"�
AppPreRegistrationAdInfo?
	headlines (2,.google.ads.googleads.v16.common.AdTextAssetB
descriptions (2,.google.ads.googleads.v16.common.AdTextAsset=
images (2-.google.ads.googleads.v16.common.AdImageAssetE
youtube_videos (2-.google.ads.googleads.v16.common.AdVideoAsset"�
LegacyAppInstallAdInfo
app_id (	H �l
	app_store (2Y.google.ads.googleads.v16.enums.LegacyAppInstallAdAppStoreEnum.LegacyAppInstallAdAppStore
headline (	H�
description1 (	H�
description2	 (	H�B	
_app_idB
	_headlineB

_description1B

_description2"�
ResponsiveDisplayAdInfoG
marketing_images (2-.google.ads.googleads.v16.common.AdImageAssetN
square_marketing_images (2-.google.ads.googleads.v16.common.AdImageAssetB
logo_images (2-.google.ads.googleads.v16.common.AdImageAssetI
square_logo_images (2-.google.ads.googleads.v16.common.AdImageAsset?
	headlines (2,.google.ads.googleads.v16.common.AdTextAssetC

long_headline (2,.google.ads.googleads.v16.common.AdTextAssetB
descriptions (2,.google.ads.googleads.v16.common.AdTextAssetE
youtube_videos (2-.google.ads.googleads.v16.common.AdVideoAsset

business_name (	H �

main_color (	H�
accent_color (	H�!
allow_flexible_color (H� 
call_to_action_text (	H�
price_prefix (	H�

promo_text (	H�i
format_setting (2Q.google.ads.googleads.v16.enums.DisplayAdFormatSettingEnum.DisplayAdFormatSettingU
control_spec (2?.google.ads.googleads.v16.common.ResponsiveDisplayAdControlSpecB
_business_nameB
_main_colorB

_accent_colorB
_allow_flexible_colorB
_call_to_action_textB

_price_prefixB
_promo_text"�
LocalAdInfo?
	headlines (2,.google.ads.googleads.v16.common.AdTextAssetB
descriptions (2,.google.ads.googleads.v16.common.AdTextAssetE
call_to_actions (2,.google.ads.googleads.v16.common.AdTextAssetG
marketing_images (2-.google.ads.googleads.v16.common.AdImageAssetB
logo_images (2-.google.ads.googleads.v16.common.AdImageAsset=
videos (2-.google.ads.googleads.v16.common.AdVideoAsset
path1	 (	H �
path2
 (	H�B
_path1B
_path2"�
DisplayUploadAdInfoz
display_upload_product_type (2U.google.ads.googleads.v16.enums.DisplayUploadProductTypeEnum.DisplayUploadProductTypeK
media_bundle (23.google.ads.googleads.v16.common.AdMediaBundleAssetH B
media_asset"a
ResponsiveDisplayAdControlSpec!
enable_asset_enhancements (
enable_autogen_video ("�
SmartCampaignAdInfo?
	headlines (2,.google.ads.googleads.v16.common.AdTextAssetB
descriptions (2,.google.ads.googleads.v16.common.AdTextAsset"�

CallAdInfo
country_code (	
phone_number (	

business_name (	
	headline1 (	
	headline2 (	
description1 (	
description2 (	
call_tracked (
disable_call_conversion (%
phone_number_verification_url (	
conversion_action	 (	�
conversion_reporting_state
 (2].google.ads.googleads.v16.enums.CallConversionReportingStateEnum.CallConversionReportingState
path1
 (	
path2 (	"�
DiscoveryMultiAssetAdInfoG
marketing_images (2-.google.ads.googleads.v16.common.AdImageAssetN
square_marketing_images (2-.google.ads.googleads.v16.common.AdImageAssetP
portrait_marketing_images (2-.google.ads.googleads.v16.common.AdImageAssetB
logo_images (2-.google.ads.googleads.v16.common.AdImageAsset?
	headlines (2,.google.ads.googleads.v16.common.AdTextAssetB
descriptions (2,.google.ads.googleads.v16.common.AdTextAsset

business_name (	H � 
call_to_action_text (	H�
lead_form_only	 (H�B
_business_nameB
_call_to_action_textB
_lead_form_only"�
DiscoveryCarouselAdInfo

business_name (	B�AF

logo_image (2-.google.ads.googleads.v16.common.AdImageAssetB�AC
headline (2,.google.ads.googleads.v16.common.AdTextAssetB�AF
description (2,.google.ads.googleads.v16.common.AdTextAssetB�A
call_to_action_text (	Z
carousel_cards (2=.google.ads.googleads.v16.common.AdDiscoveryCarouselCardAssetB�A"�
DiscoveryVideoResponsiveAdInfo?
	headlines (2,.google.ads.googleads.v16.common.AdTextAssetD
long_headlines (2,.google.ads.googleads.v16.common.AdTextAssetB
descriptions (2,.google.ads.googleads.v16.common.AdTextAsset=
videos (2-.google.ads.googleads.v16.common.AdVideoAssetB
logo_images (2-.google.ads.googleads.v16.common.AdImageAsset
breadcrumb1 (	
breadcrumb2 (	H

business_name (2,.google.ads.googleads.v16.common.AdTextAssetB�AM
call_to_actions	 (24.google.ads.googleads.v16.common.AdCallToActionAsset"�
DemandGenProductAdInfoH
headline (2,.google.ads.googleads.v16.common.AdTextAssetB�AH �K
description (2,.google.ads.googleads.v16.common.AdTextAssetB�AH�K

logo_image (2-.google.ads.googleads.v16.common.AdImageAssetB�AH�
breadcrumb1 (	
breadcrumb2 (	H

business_name (2,.google.ads.googleads.v16.common.AdTextAssetB�AQ
call_to_action (24.google.ads.googleads.v16.common.AdCallToActionAssetH�B
	_headlineB
_descriptionB
_logo_imageB
_call_to_actionB�
#com.google.ads.googleads.v16.commonBAdTypeInfosProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v16/common;common�GAA�Google.Ads.GoogleAds.V16.Common�Google\\Ads\\GoogleAds\\V16\\Common�#Google::Ads::GoogleAds::V16::Commonbproto3
�
+google/ads/googleads/v16/resources/ad.proto"google.ads.googleads.v16.resources6google/ads/googleads/v16/common/custom_parameter.proto3google/ads/googleads/v16/common/final_app_url.proto4google/ads/googleads/v16/common/url_collection.proto,google/ads/googleads/v16/enums/ad_type.proto+google/ads/googleads/v16/enums/device.protoAgoogle/ads/googleads/v16/enums/system_managed_entity_source.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�
Ad:

resource_name% (	B#�A�A
googleads.googleapis.com/Ad
id( (B�AH�

final_urls) (	D
final_app_urls# (2,.google.ads.googleads.v16.common.FinalAppUrl
final_mobile_urls* (	"
tracking_url_template+ (	H�
final_url_suffix, (	H�O
url_custom_parameters
 (20.google.ads.googleads.v16.common.CustomParameter
display_url- (	H�D
type (21.google.ads.googleads.v16.enums.AdTypeEnum.AdTypeB�A%
added_by_google_ads. (B�AH�L
device_preference (21.google.ads.googleads.v16.enums.DeviceEnum.DeviceG
url_collections (2..google.ads.googleads.v16.common.UrlCollection
name/ (	B�AH��
system_managed_resource_source (2[.google.ads.googleads.v16.enums.SystemManagedResourceSourceEnum.SystemManagedResourceSourceB�AC
text_ad (2+.google.ads.googleads.v16.common.TextAdInfoB�AH O
expanded_text_ad (23.google.ads.googleads.v16.common.ExpandedTextAdInfoH >
call_ad1 (2+.google.ads.googleads.v16.common.CallAdInfoH g
expanded_dynamic_search_ad (2<.google.ads.googleads.v16.common.ExpandedDynamicSearchAdInfoB�AH @
hotel_ad (2,.google.ads.googleads.v16.common.HotelAdInfoH Q
shopping_smart_ad (24.google.ads.googleads.v16.common.ShoppingSmartAdInfoH U
shopping_product_ad (26.google.ads.googleads.v16.common.ShoppingProductAdInfoH E
image_ad (2,.google.ads.googleads.v16.common.ImageAdInfoB�AH @
video_ad (2,.google.ads.googleads.v16.common.VideoAdInfoH U
video_responsive_ad\' (26.google.ads.googleads.v16.common.VideoResponsiveAdInfoH W
responsive_search_ad (27.google.ads.googleads.v16.common.ResponsiveSearchAdInfoH f
legacy_responsive_display_ad (2>.google.ads.googleads.v16.common.LegacyResponsiveDisplayAdInfoH <
app_ad (2*.google.ads.googleads.v16.common.AppAdInfoH ]
legacy_app_install_ad (27.google.ads.googleads.v16.common.LegacyAppInstallAdInfoB�AH Y
responsive_display_ad (28.google.ads.googleads.v16.common.ResponsiveDisplayAdInfoH @
local_ad  (2,.google.ads.googleads.v16.common.LocalAdInfoH Q
display_upload_ad! (24.google.ads.googleads.v16.common.DisplayUploadAdInfoH Q
app_engagement_ad" (24.google.ads.googleads.v16.common.AppEngagementAdInfoH j
shopping_comparison_listing_ad$ (<EMAIL> Q
smart_campaign_ad0 (24.google.ads.googleads.v16.common.SmartCampaignAdInfoH \\
app_pre_registration_ad2 (29.google.ads.googleads.v16.common.AppPreRegistrationAdInfoH ^
discovery_multi_asset_ad3 (2:.google.ads.googleads.v16.common.DiscoveryMultiAssetAdInfoH Y
discovery_carousel_ad4 (28.google.ads.googleads.v16.common.DiscoveryCarouselAdInfoH h
discovery_video_responsive_ad< (2?.google.ads.googleads.v16.common.DiscoveryVideoResponsiveAdInfoH X
demand_gen_product_ad= (27.google.ads.googleads.v16.common.DemandGenProductAdInfoH B
	travel_ad6 (2-.google.ads.googleads.v16.common.TravelAdInfoH :E�AB
googleads.googleapis.com/Ad#customers/{customer_id}/ads/{ad_id}B	
ad_dataB
_idB
_tracking_url_templateB
_final_url_suffixB
_display_urlB
_added_by_google_adsB
_nameB�
&com.google.ads.googleads.v16.resourcesBAdProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources�GAA�"Google.Ads.GoogleAds.V16.Resources�"Google\\Ads\\GoogleAds\\V16\\Resources�&Google::Ads::GoogleAds::V16::Resourcesbproto3
ݍ
7google/ads/googleads/v16/resources/recommendation.proto"google.ads.googleads.v16.resources0google/ads/googleads/v16/enums/ad_strength.proto5google/ads/googleads/v16/enums/app_bidding_goal.proto7google/ads/googleads/v16/enums/keyword_match_type.proto8google/ads/googleads/v16/enums/recommendation_type.protoZgoogle/ads/googleads/v16/enums/shopping_add_products_to_campaign_recommendation_enum.protoJgoogle/ads/googleads/v16/enums/target_cpa_opt_in_recommendation_goal.proto+google/ads/googleads/v16/resources/ad.proto.google/ads/googleads/v16/resources/asset.protogoogle/api/field_behavior.protogoogle/api/resource.proto"φ
RecommendationF

resource_name (	B/�A�A)
\'googleads.googleapis.com/Recommendation\\
type (2I.google.ads.googleads.v16.enums.RecommendationTypeEnum.RecommendationTypeB�A\\
impact (2G.google.ads.googleads.v16.resources.Recommendation.RecommendationImpactB�AM
campaign_budget (	B/�A�A)
\'googleads.googleapis.com/CampaignBudgetH�@
campaign (	B)�A�A#
!googleads.googleapis.com/CampaignH�?
ad_group (	B(�A�A"
 googleads.googleapis.com/AdGroupH�
	dismissed (B�AH�<
	campaigns& (	B)�A�A#
!googleads.googleapis.com/Campaign~
campaign_budget_recommendation (2O.google.ads.googleads.v16.resources.Recommendation.CampaignBudgetRecommendationB�AH �
*forecasting_campaign_budget_recommendation (2O.google.ads.googleads.v16.resources.Recommendation.CampaignBudgetRecommendationB�AH o
keyword_recommendation (2H.google.ads.googleads.v16.resources.Recommendation.KeywordRecommendationB�AH n
text_ad_recommendation	 (2G.google.ads.googleads.v16.resources.Recommendation.TextAdRecommendationB�AH �
 target_cpa_opt_in_recommendation
 (2O.google.ads.googleads.v16.resources.Recommendation.TargetCpaOptInRecommendationB�AH �
*maximize_conversions_opt_in_recommendation (2Y.google.ads.googleads.v16.resources.Recommendation.MaximizeConversionsOptInRecommendationB�AH �
"enhanced_cpc_opt_in_recommendation (2Q.google.ads.googleads.v16.resources.Recommendation.EnhancedCpcOptInRecommendationB�AH �
%search_partners_opt_in_recommendation (2T.google.ads.googleads.v16.resources.Recommendation.SearchPartnersOptInRecommendationB�AH �
%maximize_clicks_opt_in_recommendation (2T.google.ads.googleads.v16.resources.Recommendation.MaximizeClicksOptInRecommendationB�AH �
#optimize_ad_rotation_recommendation (2S.google.ads.googleads.v16.resources.Recommendation.OptimizeAdRotationRecommendationB�AH �
!keyword_match_type_recommendation (2Q.google.ads.googleads.v16.resources.Recommendation.KeywordMatchTypeRecommendationB�AH �
!move_unused_budget_recommendation (2Q.google.ads.googleads.v16.resources.Recommendation.MoveUnusedBudgetRecommendationB�AH �
!target_roas_opt_in_recommendation (2P.google.ads.googleads.v16.resources.Recommendation.TargetRoasOptInRecommendationB�AH �
#responsive_search_ad_recommendation (2S.google.ads.googleads.v16.resources.Recommendation.ResponsiveSearchAdRecommendationB�AH �
+marginal_roi_campaign_budget_recommendation (2O.google.ads.googleads.v16.resources.Recommendation.CampaignBudgetRecommendationB�AH �
&use_broad_match_keyword_recommendation (2U.google.ads.googleads.v16.resources.Recommendation.UseBroadMatchKeywordRecommendationB�AH �
)responsive_search_ad_asset_recommendation (2X.google.ads.googleads.v16.resources.Recommendation.ResponsiveSearchAdAssetRecommendationB�AH �
Aupgrade_smart_shopping_campaign_to_performance_max_recommendation  (2m.google.ads.googleads.v16.resources.Recommendation.UpgradeSmartShoppingCampaignToPerformanceMaxRecommendationB�AH �
7responsive_search_ad_improve_ad_strength_recommendation! (2d.google.ads.googleads.v16.resources.Recommendation.ResponsiveSearchAdImproveAdStrengthRecommendationB�AH �
\'display_expansion_opt_in_recommendation" (2V.google.ads.googleads.v16.resources.Recommendation.DisplayExpansionOptInRecommendationB�AH �
8upgrade_local_campaign_to_performance_max_recommendation# (2e.google.ads.googleads.v16.resources.Recommendation.UpgradeLocalCampaignToPerformanceMaxRecommendationB�AH �
+raise_target_cpa_bid_too_low_recommendation$ (2X.google.ads.googleads.v16.resources.Recommendation.RaiseTargetCpaBidTooLowRecommendationB�AH �
*forecasting_set_target_roas_recommendation% (2Y.google.ads.googleads.v16.resources.Recommendation.ForecastingSetTargetRoasRecommendationB�AH z
callout_asset_recommendation\' (2M.google.ads.googleads.v16.resources.Recommendation.CalloutAssetRecommendationB�AH |
sitelink_asset_recommendation( (2N.google.ads.googleads.v16.resources.Recommendation.SitelinkAssetRecommendationB�AH t
call_asset_recommendation) (2J.google.ads.googleads.v16.resources.Recommendation.CallAssetRecommendationB�AH �
%shopping_add_age_group_recommendation* (2W.google.ads.googleads.v16.resources.Recommendation.ShoppingOfferAttributeRecommendationB�AH �
!shopping_add_color_recommendation+ (2W.google.ads.googleads.v16.resources.Recommendation.ShoppingOfferAttributeRecommendationB�AH �
"shopping_add_gender_recommendation, (2W.google.ads.googleads.v16.resources.Recommendation.ShoppingOfferAttributeRecommendationB�AH �
 shopping_add_gtin_recommendation- (2W.google.ads.googleads.v16.resources.Recommendation.ShoppingOfferAttributeRecommendationB�AH �
,shopping_add_more_identifiers_recommendation. (2W.google.ads.googleads.v16.resources.Recommendation.ShoppingOfferAttributeRecommendationB�AH �
 shopping_add_size_recommendation/ (2W.google.ads.googleads.v16.resources.Recommendation.ShoppingOfferAttributeRecommendationB�AH �
0shopping_add_products_to_campaign_recommendation0 (2^.google.ads.googleads.v16.resources.Recommendation.ShoppingAddProductsToCampaignRecommendationB�AH �
0shopping_fix_disapproved_products_recommendation1 (2_.google.ads.googleads.v16.resources.Recommendation.ShoppingFixDisapprovedProductsRecommendationB�AH �
)shopping_target_all_offers_recommendation2 (2X.google.ads.googleads.v16.resources.Recommendation.ShoppingTargetAllOffersRecommendationB�AH �
=shopping_fix_suspended_merchant_center_account_recommendation3 (2h.google.ads.googleads.v16.resources.Recommendation.ShoppingMerchantCenterAccountSuspensionRecommendationB�AH �
Fshopping_fix_merchant_center_account_suspension_warning_recommendation4 (2h.google.ads.googleads.v16.resources.Recommendation.ShoppingMerchantCenterAccountSuspensionRecommendationB�AH �
Sshopping_migrate_regular_shopping_campaign_offers_to_performance_max_recommendation5 (2}.google.ads.googleads.v16.resources.Recommendation.ShoppingMigrateRegularShoppingCampaignOffersToPerformanceMaxRecommendationB�AH �
-dynamic_image_extension_opt_in_recommendation6 (2[.google.ads.googleads.v16.resources.Recommendation.DynamicImageExtensionOptInRecommendationB�AH 
raise_target_cpa_recommendation7 (2O.google.ads.googleads.v16.resources.Recommendation.RaiseTargetCpaRecommendationB�AH �
 lower_target_roas_recommendation8 (2P.google.ads.googleads.v16.resources.Recommendation.LowerTargetRoasRecommendationB�AH �
%performance_max_opt_in_recommendation9 (2T.google.ads.googleads.v16.resources.Recommendation.PerformanceMaxOptInRecommendationB�AH �
2improve_performance_max_ad_strength_recommendation: (2`.google.ads.googleads.v16.resources.Recommendation.ImprovePerformanceMaxAdStrengthRecommendationB�AH �
Emigrate_dynamic_search_ads_campaign_to_performance_max_recommendation; (2p.google.ads.googleads.v16.resources.Recommendation.MigrateDynamicSearchAdsCampaignToPerformanceMaxRecommendationB�AH �
)forecasting_set_target_cpa_recommendation< (2X.google.ads.googleads.v16.resources.Recommendation.ForecastingSetTargetCpaRecommendationB�AH �
set_target_cpa_recommendation= (2X.google.ads.googleads.v16.resources.Recommendation.ForecastingSetTargetCpaRecommendationB�AH �
set_target_roas_recommendation> (2Y.google.ads.googleads.v16.resources.Recommendation.ForecastingSetTargetRoasRecommendationB�AH �
/maximize_conversion_value_opt_in_recommendation? (2].google.ads.googleads.v16.resources.Recommendation.MaximizeConversionValueOptInRecommendationB�AH �
*improve_google_tag_coverage_recommendation@ (2Y.google.ads.googleads.v16.resources.Recommendation.ImproveGoogleTagCoverageRecommendationB�AH �
/performance_max_final_url_opt_in_recommendationA (2\\.google.ads.googleads.v16.resources.Recommendation.PerformanceMaxFinalUrlOptInRecommendationB�AH �
*refresh_customer_match_list_recommendationB (2Y.google.ads.googleads.v16.resources.Recommendation.RefreshCustomerMatchListRecommendationB�AH �
%custom_audience_opt_in_recommendationC (2T.google.ads.googleads.v16.resources.Recommendation.CustomAudienceOptInRecommendationB�AH }
lead_form_asset_recommendationD (2N.google.ads.googleads.v16.resources.Recommendation.LeadFormAssetRecommendationB�AH �
-improve_demand_gen_ad_strength_recommendationE (2[.google.ads.googleads.v16.resources.Recommendation.ImproveDemandGenAdStrengthRecommendationB�AH M
MerchantInfo
id (B�A
name (	B�A
multi_client (B�A�
RecommendationImpactc
base_metrics (2H.google.ads.googleads.v16.resources.Recommendation.RecommendationMetricsB�Ah
potential_metrics (2H.google.ads.googleads.v16.resources.Recommendation.RecommendationMetricsB�A�
RecommendationMetrics
impressions (B�AH �
clicks (B�AH�
cost_micros (B�AH�
conversions	 (B�AH�#
conversions_value (B�AH�
video_views
 (B�AH�B
_impressionsB	
_clicksB
_cost_microsB
_conversionsB
_conversions_valueB
_video_views�
CampaignBudgetRecommendation.
current_budget_amount_micros (B�AH �2
 recommended_budget_amount_micros (B�AH��
budget_options (2r.google.ads.googleads.v16.resources.Recommendation.CampaignBudgetRecommendation.CampaignBudgetRecommendationOptionB�A�
"CampaignBudgetRecommendationOption&
budget_amount_micros (B�AH �\\
impact (2G.google.ads.googleads.v16.resources.Recommendation.RecommendationImpactB�AB
_budget_amount_microsB
_current_budget_amount_microsB#
!_recommended_budget_amount_micros�
KeywordRecommendationB
keyword (2,.google.ads.googleads.v16.common.KeywordInfoB�An
search_terms (2S.google.ads.googleads.v16.resources.Recommendation.KeywordRecommendation.SearchTermB�A,
recommended_cpc_bid_micros (B�AH �K

SearchTerm
text (	B�A*
estimated_weekly_search_count (B�AB
_recommended_cpc_bid_micros�
TextAdRecommendation7
ad (2&.google.ads.googleads.v16.resources.AdB�A

creation_date (	B�AH �!
auto_apply_date (	B�AH�B
_creation_dateB
_auto_apply_date�
TargetCpaOptInRecommendation�
options (2r.google.ads.googleads.v16.resources.Recommendation.TargetCpaOptInRecommendation.TargetCpaOptInRecommendationOptionB�A/
recommended_target_cpa_micros (B�AH ��
"TargetCpaOptInRecommendationOptionx
goal (2e.google.ads.googleads.v16.enums.TargetCpaOptInRecommendationGoalEnum.TargetCpaOptInRecommendationGoalB�A#
target_cpa_micros (B�AH �8
&required_campaign_budget_amount_micros (B�AH�\\
impact (2G.google.ads.googleads.v16.resources.Recommendation.RecommendationImpactB�AB
_target_cpa_microsB)
\'_required_campaign_budget_amount_microsB 
_recommended_target_cpa_micros�
&MaximizeConversionsOptInRecommendation2
 recommended_budget_amount_micros (B�AH �B#
!_recommended_budget_amount_micros 
EnhancedCpcOptInRecommendation#
!SearchPartnersOptInRecommendation|
!MaximizeClicksOptInRecommendation2
 recommended_budget_amount_micros (B�AH �B#
!_recommended_budget_amount_micros"
 OptimizeAdRotationRecommendation�
CalloutAssetRecommendation[
#recommended_campaign_callout_assets (2).google.ads.googleads.v16.resources.AssetB�A[
#recommended_customer_callout_assets (2).google.ads.googleads.v16.resources.AssetB�A�
SitelinkAssetRecommendation\\
$recommended_campaign_sitelink_assets (2).google.ads.googleads.v16.resources.AssetB�A\\
$recommended_customer_sitelink_assets (2).google.ads.googleads.v16.resources.AssetB�A
CallAssetRecommendation�
KeywordMatchTypeRecommendationB
keyword (2,.google.ads.googleads.v16.common.KeywordInfoB�Aj
recommended_match_type (2E.google.ads.googleads.v16.enums.KeywordMatchTypeEnum.KeywordMatchTypeB�A�
MoveUnusedBudgetRecommendation(
excess_campaign_budget (	B�AH �s
budget_recommendation (2O.google.ads.googleads.v16.resources.Recommendation.CampaignBudgetRecommendationB�AB
_excess_campaign_budget�
TargetRoasOptInRecommendation)
recommended_target_roas (B�AH �8
&required_campaign_budget_amount_micros (B�AH�B
_recommended_target_roasB)
\'_required_campaign_budget_amount_micros�
%ResponsiveSearchAdAssetRecommendation?

current_ad (2&.google.ads.googleads.v16.resources.AdB�AG
recommended_assets (2&.google.ads.googleads.v16.resources.AdB�A�
1ResponsiveSearchAdImproveAdStrengthRecommendation?

current_ad (2&.google.ads.googleads.v16.resources.AdB�AC
recommended_ad (2&.google.ads.googleads.v16.resources.AdB�A[
 ResponsiveSearchAdRecommendation7
ad (2&.google.ads.googleads.v16.resources.AdB�A�
"UseBroadMatchKeywordRecommendationB
keyword (2,.google.ads.googleads.v16.common.KeywordInfoB�A%
suggested_keywords_count (B�A$
campaign_keywords_count (B�A(
campaign_uses_shared_budget (B�A3
&required_campaign_budget_amount_micros (B�Aw
:UpgradeSmartShoppingCampaignToPerformanceMaxRecommendation
merchant_id (B�A
sales_country_code (	B�A�
%RaiseTargetCpaBidTooLowRecommendation/
recommended_target_multiplier (B�AH �+
average_target_cpa_micros (B�AH�B 
_recommended_target_multiplierB
_average_target_cpa_micros%
#DisplayExpansionOptInRecommendation4
2UpgradeLocalCampaignToPerformanceMaxRecommendation�
&ForecastingSetTargetRoasRecommendation$
recommended_target_roas (B�A_
campaign_budget (2A.google.ads.googleads.v16.resources.Recommendation.CampaignBudgetB�A�
$ShoppingOfferAttributeRecommendationV
merchant (2?.google.ads.googleads.v16.resources.Recommendation.MerchantInfoB�A

feed_label (	B�A
offers_count (B�A!
demoted_offers_count (B�A�
,ShoppingFixDisapprovedProductsRecommendationV
merchant (2?.google.ads.googleads.v16.resources.Recommendation.MerchantInfoB�A

feed_label (	B�A
products_count (B�A\'
disapproved_products_count (B�A�
%ShoppingTargetAllOffersRecommendationV
merchant (2?.google.ads.googleads.v16.resources.Recommendation.MerchantInfoB�A$
untargeted_offers_count (B�A

feed_label (	B�A�
+ShoppingAddProductsToCampaignRecommendationV
merchant (2?.google.ads.googleads.v16.resources.Recommendation.MerchantInfoB�A

feed_label (	B�Ak
reason (2V.google.ads.googleads.v16.enums.ShoppingAddProductsToCampaignRecommendationEnum.ReasonB�A�
5ShoppingMerchantCenterAccountSuspensionRecommendationV
merchant (2?.google.ads.googleads.v16.resources.Recommendation.MerchantInfoB�A

feed_label (	B�A�
JShoppingMigrateRegularShoppingCampaignOffersToPerformanceMaxRecommendationV
merchant (2?.google.ads.googleads.v16.resources.Recommendation.MerchantInfoB�A

feed_label (	B�A�
TargetAdjustmentInfo

shared_set (	B�AH �*
recommended_target_multiplier (B�A*
current_average_target_micros (B�AB
_shared_set�
RaiseTargetCpaRecommendationg
target_adjustment (2G.google.ads.googleads.v16.resources.Recommendation.TargetAdjustmentInfoB�Ae
app_bidding_goal (2A.google.ads.googleads.v16.enums.AppBiddingGoalEnum.AppBiddingGoalB�AH �B
_app_bidding_goal�
LowerTargetRoasRecommendationg
target_adjustment (2G.google.ads.googleads.v16.resources.Recommendation.TargetAdjustmentInfoB�A*
(DynamicImageExtensionOptInRecommendation}
CampaignBudget"
current_amount_micros (B�A*
recommended_new_amount_micros (B�A
new_start_date (	B�A#
!PerformanceMaxOptInRecommendationI
-ImprovePerformanceMaxAdStrengthRecommendation
asset_group (	B�AX
=MigrateDynamicSearchAdsCampaignToPerformanceMaxRecommendation

apply_link (	B�A�
%ForecastingSetTargetCpaRecommendation*
recommended_target_cpa_micros (B�A_
campaign_budget (2A.google.ads.googleads.v16.resources.Recommendation.CampaignBudgetB�A,
*MaximizeConversionValueOptInRecommendation(
&ImproveGoogleTagCoverageRecommendation+
)PerformanceMaxFinalUrlOptInRecommendation�
&RefreshCustomerMatchListRecommendation
user_list_id (B�A
user_list_name (	B�A$
days_since_last_refresh (B�Aa
top_spending_account (2>.google.ads.googleads.v16.resources.Recommendation.AccountInfoB�A%
targeting_accounts_count (B�AZ

owner_account (2>.google.ads.googleads.v16.resources.Recommendation.AccountInfoB�AF
AccountInfo
customer_id (B�A
descriptive_name (	B�Ah
!CustomAudienceOptInRecommendationC
keywords (2,.google.ads.googleads.v16.common.KeywordInfoB�A
LeadFormAssetRecommendation�
(ImproveDemandGenAdStrengthRecommendation
ad (	B�AS
ad_strength (29.google.ads.googleads.v16.enums.AdStrengthEnum.AdStrengthB�A*
demand_gen_asset_action_items (	B�A:i�Af
\'googleads.googleapis.com/Recommendation;customers/{customer_id}/recommendations/{recommendation_id}B
recommendationB
_campaign_budgetB
	_campaignB
	_ad_groupB

_dismissedB�
&com.google.ads.googleads.v16.resourcesBRecommendationProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources�GAA�"Google.Ads.GoogleAds.V16.Resources�"Google\\Ads\\GoogleAds\\V16\\Resources�&Google::Ads::GoogleAds::V16::Resourcesbproto3'
        , true);
        static::$is_initialized = true;
    }
}

