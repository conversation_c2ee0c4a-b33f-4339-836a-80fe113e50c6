<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/errors/errors.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Errors;

class Errors
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Protobuf\Duration::initOnce();
        $pool->internalAddGeneratedFile(
            '
�	
;google/ads/googleads/v17/errors/campaign_budget_error.protogoogle.ads.googleads.v17.errors"�
CampaignBudgetErrorEnum"�
CampaignBudgetError
UNSPECIFIED 
UNKNOWN$
 CAMPAIGN_BUDGET_CANNOT_BE_SHARED
CAMPAIGN_BUDGET_REMOVED
CAMPAIGN_BUDGET_IN_USE(
$CAMPAIGN_BUDGET_PERIOD_NOT_AVAILABLE<
8CANNOT_MODIFY_FIELD_OF_IMPLICITLY_SHARED_CAMPAIGN_BUDGET6
2CANNOT_UPDATE_CAMPAIGN_BUDGET_TO_IMPLICITLY_SHAREDC
?CANNOT_UPDATE_CAMPAIGN_BUDGET_TO_EXPLICITLY_SHARED_WITHOUT_NAME6
2CANNOT_UPDATE_CAMPAIGN_BUDGET_TO_EXPLICITLY_SHARED	H
DCANNOT_USE_IMPLICITLY_SHARED_CAMPAIGN_BUDGET_WITH_MULTIPLE_CAMPAIGNS

DUPLICATE_NAME"
MONEY_AMOUNT_IN_WRONG_CURRENCY/
+MONEY_AMOUNT_LESS_THAN_CURRENCY_MINIMUM_CPC

MONEY_AMOUNT_TOO_LARGE
NEGATIVE_MONEY_AMOUNT)
%NON_MULTIPLE_OF_MINIMUM_CURRENCY_UNIT=
9TOTAL_BUDGET_AMOUNT_MUST_BE_UNSET_FOR_BUDGET_PERIOD_DAILY
INVALID_PERIOD(
$CANNOT_USE_ACCELERATED_DELIVERY_MODE8
4BUDGET_AMOUNT_MUST_BE_UNSET_FOR_CUSTOM_BUDGET_PERIODB�
#com.google.ads.googleads.v17.errorsBCampaignBudgetErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
3google/ads/googleads/v17/errors/invoice_error.protogoogle.ads.googleads.v17.errors"�
InvoiceErrorEnum"�
InvoiceError
UNSPECIFIED 
UNKNOWN
YEAR_MONTH_TOO_OLD
NOT_INVOICED_CUSTOMER
BILLING_SETUP_NOT_APPROVED*
&BILLING_SETUP_NOT_ON_MONTHLY_INVOICING
NON_SERVING_CUSTOMERB�
#com.google.ads.googleads.v17.errorsBInvoiceErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Fgoogle/ads/googleads/v17/errors/youtube_video_registration_error.protogoogle.ads.googleads.v17.errors"�
!YoutubeVideoRegistrationErrorEnum"�
YoutubeVideoRegistrationError
UNSPECIFIED 
UNKNOWN
VIDEO_NOT_FOUND
VIDEO_NOT_ACCESSIBLE
VIDEO_NOT_ELIGIBLEB�
#com.google.ads.googleads.v17.errorsB"YoutubeVideoRegistrationErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
=google/ads/googleads/v17/errors/asset_group_asset_error.protogoogle.ads.googleads.v17.errors"�
AssetGroupAssetErrorEnum"�
AssetGroupAssetError
UNSPECIFIED 
UNKNOWN
DUPLICATE_RESOURCE.
*EXPANDABLE_TAGS_NOT_ALLOWED_IN_DESCRIPTION
AD_CUSTOMIZER_NOT_SUPPORTED/
+HOTEL_PROPERTY_ASSET_NOT_LINKED_TO_CAMPAIGNB�
#com.google.ads.googleads.v17.errorsBAssetGroupAssetErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
8google/ads/googleads/v17/errors/country_code_error.protogoogle.ads.googleads.v17.errors"b
CountryCodeErrorEnum"J
CountryCodeError
UNSPECIFIED 
UNKNOWN
INVALID_COUNTRY_CODEB�
#com.google.ads.googleads.v17.errorsBCountryCodeErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
0google/ads/googleads/v17/errors/date_error.protogoogle.ads.googleads.v17.errors"�

DateErrorEnum"�
	DateError
UNSPECIFIED 
UNKNOWN 
INVALID_FIELD_VALUES_IN_DATE%
!INVALID_FIELD_VALUES_IN_DATE_TIME
INVALID_STRING_DATE#
INVALID_STRING_DATE_TIME_MICROS$
 INVALID_STRING_DATE_TIME_SECONDS0
,INVALID_STRING_DATE_TIME_SECONDS_WITH_OFFSET
EARLIER_THAN_MINIMUM_DATE
LATER_THAN_MAXIMUM_DATE3
/DATE_RANGE_MINIMUM_DATE_LATER_THAN_MAXIMUM_DATE	2
.DATE_RANGE_MINIMUM_AND_MAXIMUM_DATES_BOTH_NULL
B�
#com.google.ads.googleads.v17.errorsBDateErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
=google/ads/googleads/v17/errors/conversion_action_error.protogoogle.ads.googleads.v17.errors"�
ConversionActionErrorEnum"�
ConversionActionError
UNSPECIFIED 
UNKNOWN
DUPLICATE_NAME
DUPLICATE_APP_ID7
3TWO_CONVERSION_ACTIONS_BIDDING_ON_SAME_APP_DOWNLOAD1
-BIDDING_ON_SAME_APP_DOWNLOAD_AS_GLOBAL_ACTION)
%DATA_DRIVEN_MODEL_WAS_NEVER_GENERATED
DATA_DRIVEN_MODEL_EXPIRED
DATA_DRIVEN_MODEL_STALE
DATA_DRIVEN_MODEL_UNKNOWN	
CREATION_NOT_SUPPORTED

UPDATE_NOT_SUPPORTED,
(CANNOT_SET_RULE_BASED_ATTRIBUTION_MODELSB�
#com.google.ads.googleads.v17.errorsBConversionActionErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
2google/ads/googleads/v17/errors/mutate_error.protogoogle.ads.googleads.v17.errors"�
MutateErrorEnum"�
MutateError
UNSPECIFIED 
UNKNOWN
RESOURCE_NOT_FOUND!
ID_EXISTS_IN_MULTIPLE_MUTATES
INCONSISTENT_FIELD_VALUES
MUTATE_NOT_ALLOWED	
RESOURCE_NOT_IN_GOOGLE_ADS

RESOURCE_ALREADY_EXISTS+
\'RESOURCE_DOES_NOT_SUPPORT_VALIDATE_ONLY.
*OPERATION_DOES_NOT_SUPPORT_PARTIAL_FAILURE
RESOURCE_READ_ONLY
B�
#com.google.ads.googleads.v17.errorsBMutateErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
4google/ads/googleads/v17/errors/customer_error.protogoogle.ads.googleads.v17.errors"x
CustomerErrorEnum"c

CustomerError
UNSPECIFIED 
UNKNOWN
STATUS_CHANGE_DISALLOWED
ACCOUNT_NOT_SET_UPB�
#com.google.ads.googleads.v17.errorsBCustomerErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
9google/ads/googleads/v17/errors/string_format_error.protogoogle.ads.googleads.v17.errors"q
StringFormatErrorEnum"X
StringFormatError
UNSPECIFIED 
UNKNOWN

ILLEGAL_CHARS
INVALID_FORMATB�
#com.google.ads.googleads.v17.errorsBStringFormatErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Agoogle/ads/googleads/v17/errors/new_resource_creation_error.protogoogle.ads.googleads.v17.errors"�
NewResourceCreationErrorEnum"�
NewResourceCreationError
UNSPECIFIED 
UNKNOWN
CANNOT_SET_ID_FOR_CREATE
DUPLICATE_TEMP_IDS
TEMP_ID_RESOURCE_HAD_ERRORSB�
#com.google.ads.googleads.v17.errorsBNewResourceCreationErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
9google/ads/googleads/v17/errors/customer_feed_error.protogoogle.ads.googleads.v17.errors"�
CustomerFeedErrorEnum"�
CustomerFeedError
UNSPECIFIED 
UNKNOWN,
(FEED_ALREADY_EXISTS_FOR_PLACEHOLDER_TYPE"
CANNOT_CREATE_FOR_REMOVED_FEED0
,CANNOT_CREATE_ALREADY_EXISTING_CUSTOMER_FEED\'
#CANNOT_MODIFY_REMOVED_CUSTOMER_FEED
INVALID_PLACEHOLDER_TYPE,
(MISSING_FEEDMAPPING_FOR_PLACEHOLDER_TYPE1
-PLACEHOLDER_TYPE_NOT_ALLOWED_ON_CUSTOMER_FEEDB�
#com.google.ads.googleads.v17.errorsBCustomerFeedErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
5google/ads/googleads/v17/errors/asset_set_error.protogoogle.ads.googleads.v17.errors"�
AssetSetErrorEnum"�

AssetSetError
UNSPECIFIED 
UNKNOWN
DUPLICATE_ASSET_SET_NAME!
INVALID_PARENT_ASSET_SET_TYPE7
3ASSET_SET_SOURCE_INCOMPATIBLE_WITH_PARENT_ASSET_SET/
+ASSET_SET_TYPE_CANNOT_BE_LINKED_TO_CUSTOMER
INVALID_CHAIN_IDS>
:LOCATION_SYNC_ASSET_SET_DOES_NOT_SUPPORT_RELATIONSHIP_TYPE4
0NOT_UNIQUE_ENABLED_LOCATION_SYNC_TYPED_ASSET_SET
INVALID_PLACE_IDS	
OAUTH_INFO_INVALID
OAUTH_INFO_MISSING+
\'CANNOT_DELETE_AS_ENABLED_LINKAGES_EXIST
B�
#com.google.ads.googleads.v17.errorsBAssetSetErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Agoogle/ads/googleads/v17/errors/keyword_plan_campaign_error.protogoogle.ads.googleads.v17.errors"�
KeywordPlanCampaignErrorEnum"�
KeywordPlanCampaignError
UNSPECIFIED 
UNKNOWN
INVALID_NAME
INVALID_LANGUAGES
INVALID_GEOS
DUPLICATE_NAME
MAX_GEOS_EXCEEDED
MAX_LANGUAGES_EXCEEDEDB�
#com.google.ads.googleads.v17.errorsBKeywordPlanCampaignErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Jgoogle/ads/googleads/v17/errors/third_party_app_analytics_link_error.protogoogle.ads.googleads.v17.errors"�
#ThirdPartyAppAnalyticsLinkErrorEnum"�
ThirdPartyAppAnalyticsLinkError
UNSPECIFIED 
UNKNOWN!
INVALID_ANALYTICS_PROVIDER_ID
INVALID_MOBILE_APP_ID
MOBILE_APP_IS_NOT_ENABLED8
4CANNOT_REGENERATE_SHAREABLE_LINK_ID_FOR_REMOVED_LINKB�
#com.google.ads.googleads.v17.errorsB$ThirdPartyAppAnalyticsLinkErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
:google/ads/googleads/v17/errors/video_campaign_error.protogoogle.ads.googleads.v17.errors"m
VideoCampaignErrorEnum"S
VideoCampaignError
UNSPECIFIED 
UNKNOWN
MUTATE_REQUIRES_RESERVATIONB�
#com.google.ads.googleads.v17.errorsBVideoCampaignErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
1google/ads/googleads/v17/errors/asset_error.protogoogle.ads.googleads.v17.errors"�
AssetErrorEnum"�

AssetError
UNSPECIFIED 
UNKNOWN,
(CUSTOMER_NOT_ON_ALLOWLIST_FOR_ASSET_TYPE

DUPLICATE_ASSET
DUPLICATE_ASSET_NAME
ASSET_DATA_IS_MISSING
CANNOT_MODIFY_ASSET_NAME&
"FIELD_INCOMPATIBLE_WITH_ASSET_TYPE
INVALID_CALL_TO_ACTION_TEXT(
$LEAD_FORM_INVALID_FIELDS_COMBINATION	
LEAD_FORM_MISSING_AGREEMENT

INVALID_ASSET_STATUS+
\'FIELD_CANNOT_BE_MODIFIED_FOR_ASSET_TYPE
SCHEDULES_CANNOT_OVERLAP9
5PROMOTION_CANNOT_SET_PERCENT_OFF_AND_MONEY_AMOUNT_OFF>
:PROMOTION_CANNOT_SET_PROMOTION_CODE_AND_ORDERS_OVER_AMOUNT%
!TOO_MANY_DECIMAL_PLACES_SPECIFIED/
+DUPLICATE_ASSETS_WITH_DIFFERENT_FIELD_VALUE2
.CALL_CARRIER_SPECIFIC_SHORT_NUMBER_NOT_ALLOWED5
1CALL_CUSTOMER_CONSENT_FOR_CALL_RECORDING_REQUIRED
CALL_DISALLOWED_NUMBER_TYPE"
CALL_INVALID_CONVERSION_ACTION
CALL_INVALID_COUNTRY_CODE-
)CALL_INVALID_DOMESTIC_PHONE_NUMBER_FORMAT
CALL_INVALID_PHONE_NUMBER/
+CALL_PHONE_NUMBER_NOT_SUPPORTED_FOR_COUNTRY(
$CALL_PREMIUM_RATE_NUMBER_NOT_ALLOWED(
$CALL_VANITY_PHONE_NUMBER_NOT_ALLOWED$
 PRICE_HEADER_SAME_AS_DESCRIPTION
MOBILE_APP_INVALID_APP_ID5
1MOBILE_APP_INVALID_FINAL_URL_FOR_APP_DOWNLOAD_URL 
NAME_REQUIRED_FOR_ASSET_TYPE 4
0LEAD_FORM_LEGACY_QUALIFYING_QUESTIONS_DISALLOWED! 
NAME_CONFLICT_FOR_ASSET_TYPE"
CANNOT_MODIFY_ASSET_SOURCE#-
)CANNOT_MODIFY_AUTOMATICALLY_CREATED_ASSET$-
)LEAD_FORM_LOCATION_ANSWER_TYPE_DISALLOWED% 
PAGE_FEED_INVALID_LABEL_TEXT&B�
#com.google.ads.googleads.v17.errorsBAssetErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
9google/ads/googleads/v17/errors/authorization_error.protogoogle.ads.googleads.v17.errors"�
AuthorizationErrorEnum"�
AuthorizationError
UNSPECIFIED 
UNKNOWN
USER_PERMISSION_DENIED$
 DEVELOPER_TOKEN_NOT_ON_ALLOWLIST

DEVELOPER_TOKEN_PROHIBITED
PROJECT_DISABLED
AUTHORIZATION_ERROR
ACTION_NOT_PERMITTED
INCOMPLETE_SIGNUP
CUSTOMER_NOT_ENABLED
MISSING_TOS	 
DEVELOPER_TOKEN_NOT_APPROVED
=
9INVALID_LOGIN_CUSTOMER_ID_SERVING_CUSTOMER_ID_COMBINATION
SERVICE_ACCESS_DENIED"
ACCESS_DENIED_FOR_ACCOUNT_TYPE
METRIC_ACCESS_DENIED(
$CLOUD_PROJECT_NOT_UNDER_ORGANIZATION.
*ACTION_NOT_PERMITTED_FOR_SUSPENDED_ACCOUNTB�
#com.google.ads.googleads.v17.errorsBAuthorizationErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�

Cgoogle/ads/googleads/v17/errors/account_budget_proposal_error.protogoogle.ads.googleads.v17.errors"�
AccountBudgetProposalErrorEnum"�
AccountBudgetProposalError
UNSPECIFIED 
UNKNOWN
FIELD_MASK_NOT_ALLOWED
IMMUTABLE_FIELD
REQUIRED_FIELD_MISSING#
CANNOT_CANCEL_APPROVED_PROPOSAL#
CANNOT_REMOVE_UNAPPROVED_BUDGET 
CANNOT_REMOVE_RUNNING_BUDGET 
CANNOT_END_UNAPPROVED_BUDGET
CANNOT_END_INACTIVE_BUDGET	
BUDGET_NAME_REQUIRED

CANNOT_UPDATE_OLD_BUDGET
CANNOT_END_IN_PAST
CANNOT_EXTEND_END_TIME
"
PURCHASE_ORDER_NUMBER_REQUIRED"
PENDING_UPDATE_PROPOSAL_EXISTS=
9MULTIPLE_BUDGETS_NOT_ALLOWED_FOR_UNAPPROVED_BILLING_SETUP/
+CANNOT_UPDATE_START_TIME_FOR_STARTED_BUDGET6
2SPENDING_LIMIT_LOWER_THAN_ACCRUED_COST_NOT_ALLOWED
UPDATE_IS_NO_OP#
END_TIME_MUST_FOLLOW_START_TIME5
1BUDGET_DATE_RANGE_INCOMPATIBLE_WITH_BILLING_SETUP
NOT_AUTHORIZED
INVALID_BILLING_SETUP
OVERLAPS_EXISTING_BUDGET$
 CANNOT_CREATE_BUDGET_THROUGH_API$
 INVALID_MASTER_SERVICE_AGREEMENT
CANCELED_BILLING_SETUPB�
#com.google.ads.googleads.v17.errorsBAccountBudgetProposalErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
5google/ads/googleads/v17/errors/batch_job_error.protogoogle.ads.googleads.v17.errors"�
BatchJobErrorEnum"�

BatchJobError
UNSPECIFIED 
UNKNOWN.
*CANNOT_MODIFY_JOB_AFTER_JOB_STARTS_RUNNING
EMPTY_OPERATIONS
INVALID_SEQUENCE_TOKEN
RESULTS_NOT_READY
INVALID_PAGE_SIZE
CAN_ONLY_REMOVE_PENDING_JOB
CANNOT_LIST_RESULTS9
5ASSET_GROUP_AND_ASSET_GROUP_ASSET_TRANSACTION_FAILURE	8
4ASSET_GROUP_LISTING_GROUP_FILTER_TRANSACTION_FAILURE

REQUEST_TOO_LARGEB�
#com.google.ads.googleads.v17.errorsBBatchJobErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
5google/ads/googleads/v17/errors/url_field_error.protogoogle.ads.googleads.v17.errors"�
UrlFieldErrorEnum"�

UrlFieldError
UNSPECIFIED 
UNKNOWN!
INVALID_TRACKING_URL_TEMPLATE(
$INVALID_TAG_IN_TRACKING_URL_TEMPLATE%
!MISSING_TRACKING_URL_TEMPLATE_TAG-
)MISSING_PROTOCOL_IN_TRACKING_URL_TEMPLATE-
)INVALID_PROTOCOL_IN_TRACKING_URL_TEMPLATE#
MALFORMED_TRACKING_URL_TEMPLATE)
%MISSING_HOST_IN_TRACKING_URL_TEMPLATE(
$INVALID_TLD_IN_TRACKING_URL_TEMPLATE	.
*REDUNDANT_NESTED_TRACKING_URL_TEMPLATE_TAG

INVALID_FINAL_URL
INVALID_TAG_IN_FINAL_URL"
REDUNDANT_NESTED_FINAL_URL_TAG
!
MISSING_PROTOCOL_IN_FINAL_URL!
INVALID_PROTOCOL_IN_FINAL_URL
MALFORMED_FINAL_URL
MISSING_HOST_IN_FINAL_URL
INVALID_TLD_IN_FINAL_URL
INVALID_FINAL_MOBILE_URL#
INVALID_TAG_IN_FINAL_MOBILE_URL)
%REDUNDANT_NESTED_FINAL_MOBILE_URL_TAG(
$MISSING_PROTOCOL_IN_FINAL_MOBILE_URL(
$INVALID_PROTOCOL_IN_FINAL_MOBILE_URL
MALFORMED_FINAL_MOBILE_URL$
 MISSING_HOST_IN_FINAL_MOBILE_URL#
INVALID_TLD_IN_FINAL_MOBILE_URL
INVALID_FINAL_APP_URL 
INVALID_TAG_IN_FINAL_APP_URL&
"REDUNDANT_NESTED_FINAL_APP_URL_TAG 
MULTIPLE_APP_URLS_FOR_OSTYPE
INVALID_OSTYPE 
INVALID_PROTOCOL_FOR_APP_URL "
INVALID_PACKAGE_ID_FOR_APP_URL!-
)URL_CUSTOM_PARAMETERS_COUNT_EXCEEDS_LIMIT"2
.INVALID_CHARACTERS_IN_URL_CUSTOM_PARAMETER_KEY\'4
0INVALID_CHARACTERS_IN_URL_CUSTOM_PARAMETER_VALUE(-
)INVALID_TAG_IN_URL_CUSTOM_PARAMETER_VALUE)-
)REDUNDANT_NESTED_URL_CUSTOM_PARAMETER_TAG*
MISSING_PROTOCOL+
INVALID_PROTOCOL4
INVALID_URL,
DESTINATION_URL_DEPRECATED-
INVALID_TAG_IN_URL.
MISSING_URL_TAG/
DUPLICATE_URL_ID0
INVALID_URL_ID1
FINAL_URL_SUFFIX_MALFORMED2#
INVALID_TAG_IN_FINAL_URL_SUFFIX3
INVALID_TOP_LEVEL_DOMAIN5
MALFORMED_TOP_LEVEL_DOMAIN6

MALFORMED_URL7
MISSING_HOST8
NULL_CUSTOM_PARAMETER_VALUE9\'
#VALUE_TRACK_PARAMETER_NOT_SUPPORTED:B�
#com.google.ads.googleads.v17.errorsBUrlFieldErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
8google/ads/googleads/v17/errors/manager_link_error.protogoogle.ads.googleads.v17.errors"�
ManagerLinkErrorEnum"�
ManagerLinkError
UNSPECIFIED 
UNKNOWN\'
#ACCOUNTS_NOT_COMPATIBLE_FOR_LINKING
TOO_MANY_MANAGERS
TOO_MANY_INVITES#
ALREADY_INVITED_BY_THIS_MANAGER#
ALREADY_MANAGED_BY_THIS_MANAGER 
ALREADY_MANAGED_IN_HIERARCHY
DUPLICATE_CHILD_FOUND
CLIENT_HAS_NO_ADMIN_USER	
MAX_DEPTH_EXCEEDED

CYCLE_NOT_ALLOWED
TOO_MANY_ACCOUNTS 
TOO_MANY_ACCOUNTS_AT_MANAGER
%
!NON_OWNER_USER_CANNOT_MODIFY_LINK(
$SUSPENDED_ACCOUNT_CANNOT_ADD_CLIENTS
CLIENT_OUTSIDE_TREE
INVALID_STATUS_CHANGE
INVALID_CHANGE
CUSTOMER_CANNOT_MANAGE_SELF%
!CREATING_ENABLED_LINK_NOT_ALLOWEDB�
#com.google.ads.googleads.v17.errorsBManagerLinkErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
3google/ads/googleads/v17/errors/bidding_error.protogoogle.ads.googleads.v17.errors"�	
BiddingErrorEnum"�	
BiddingError
UNSPECIFIED 
UNKNOWN+
\'BIDDING_STRATEGY_TRANSITION_NOT_ALLOWED.
*CANNOT_ATTACH_BIDDING_STRATEGY_TO_CAMPAIGN+
\'INVALID_ANONYMOUS_BIDDING_STRATEGY_TYPE
!
INVALID_BIDDING_STRATEGY_TYPE
INVALID_BID3
/BIDDING_STRATEGY_NOT_AVAILABLE_FOR_ACCOUNT_TYPE0
,CANNOT_CREATE_CAMPAIGN_WITH_BIDDING_STRATEGYO
KCANNOT_TARGET_CONTENT_NETWORK_ONLY_WITH_CAMPAIGN_LEVEL_POP_BIDDING_STRATEGY3
/BIDDING_STRATEGY_NOT_SUPPORTED_WITH_AD_SCHEDULE1
-PAY_PER_CONVERSION_NOT_AVAILABLE_FOR_CUSTOMER2
.PAY_PER_CONVERSION_NOT_ALLOWED_WITH_TARGET_CPA:
6BIDDING_STRATEGY_NOT_ALLOWED_FOR_SEARCH_ONLY_CAMPAIGNS;
7BIDDING_STRATEGY_NOT_SUPPORTED_IN_DRAFTS_OR_EXPERIMENTSI
EBIDDING_STRATEGY_TYPE_DOES_NOT_SUPPORT_PRODUCT_TYPE_ADGROUP_CRITERION

BID_TOO_SMALL
BID_TOO_BIG"
BID_TOO_MANY_FRACTIONAL_DIGITS 
INVALID_DOMAIN_NAME!$
 NOT_COMPATIBLE_WITH_PAYMENT_MODE"9
5BIDDING_STRATEGY_TYPE_INCOMPATIBLE_WITH_SHARED_BUDGET%/
+BIDDING_STRATEGY_AND_BUDGET_MUST_BE_ALIGNED&O
KBIDDING_STRATEGY_AND_BUDGET_MUST_BE_ATTACHED_TO_THE_SAME_CAMPAIGNS_TO_ALIGN\'8
4BIDDING_STRATEGY_AND_BUDGET_MUST_BE_REMOVED_TOGETHER(<
8CPC_BID_FLOOR_MICROS_GREATER_THAN_CPC_BID_CEILING_MICROS)B�
#com.google.ads.googleads.v17.errorsBBiddingErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
9google/ads/googleads/v17/errors/campaign_feed_error.protogoogle.ads.googleads.v17.errors"�
CampaignFeedErrorEnum"�
CampaignFeedError
UNSPECIFIED 
UNKNOWN,
(FEED_ALREADY_EXISTS_FOR_PLACEHOLDER_TYPE"
CANNOT_CREATE_FOR_REMOVED_FEED0
,CANNOT_CREATE_ALREADY_EXISTING_CAMPAIGN_FEED\'
#CANNOT_MODIFY_REMOVED_CAMPAIGN_FEED
INVALID_PLACEHOLDER_TYPE,
(MISSING_FEEDMAPPING_FOR_PLACEHOLDER_TYPE&
"NO_EXISTING_LOCATION_CUSTOMER_FEED	
LEGACY_FEED_TYPE_READ_ONLY
B�
#com.google.ads.googleads.v17.errorsBCampaignFeedErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
6google/ads/googleads/v17/errors/date_range_error.protogoogle.ads.googleads.v17.errors"�
DateRangeErrorEnum"�
DateRangeError
UNSPECIFIED 
UNKNOWN
INVALID_DATE
START_DATE_AFTER_END_DATE
CANNOT_SET_DATE_TO_PAST 
AFTER_MAXIMUM_ALLOWABLE_DATE/
+CANNOT_MODIFY_START_DATE_IF_ALREADY_STARTEDB�
#com.google.ads.googleads.v17.errorsBDateRangeErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
0google/ads/googleads/v17/errors/null_error.protogoogle.ads.googleads.v17.errors"L

NullErrorEnum";
	NullError
UNSPECIFIED 
UNKNOWN
NULL_CONTENTB�
#com.google.ads.googleads.v17.errorsBNullErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
@google/ads/googleads/v17/errors/customizer_attribute_error.protogoogle.ads.googleads.v17.errors"�
CustomizerAttributeErrorEnum"a
CustomizerAttributeError
UNSPECIFIED 
UNKNOWN\'
#DUPLICATE_CUSTOMIZER_ATTRIBUTE_NAMEB�
#com.google.ads.googleads.v17.errorsBCustomizerAttributeErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
@google/ads/googleads/v17/errors/feed_item_validation_error.protogoogle.ads.googleads.v17.errors"�
FeedItemValidationErrorEnum"�
FeedItemValidationError
UNSPECIFIED 
UNKNOWN
STRING_TOO_SHORT
STRING_TOO_LONG
VALUE_NOT_SPECIFIED(
$INVALID_DOMESTIC_PHONE_NUMBER_FORMAT
INVALID_PHONE_NUMBER*
&PHONE_NUMBER_NOT_SUPPORTED_FOR_COUNTRY#
PREMIUM_RATE_NUMBER_NOT_ALLOWED
DISALLOWED_NUMBER_TYPE	
VALUE_OUT_OF_RANGE
*
&CALLTRACKING_NOT_SUPPORTED_FOR_COUNTRY.
*CUSTOMER_NOT_IN_ALLOWLIST_FOR_CALLTRACKINGc
INVALID_COUNTRY_CODE

INVALID_APP_ID!
MISSING_ATTRIBUTES_FOR_FIELDS
INVALID_TYPE_ID
INVALID_EMAIL_ADDRESS
INVALID_HTTPS_URL
MISSING_DELIVERY_ADDRESS
START_DATE_AFTER_END_DATE 
MISSING_FEED_ITEM_START_TIME
MISSING_FEED_ITEM_END_TIME
MISSING_FEED_ITEM_ID#
VANITY_PHONE_NUMBER_NOT_ALLOWED$
 INVALID_REVIEW_EXTENSION_SNIPPET
INVALID_NUMBER_FORMAT
INVALID_DATE_FORMAT
INVALID_PRICE_FORMAT
UNKNOWN_PLACEHOLDER_FIELD.
*MISSING_ENHANCED_SITELINK_DESCRIPTION_LINE&
"REVIEW_EXTENSION_SOURCE_INELIGIBLE\'
#HYPHENS_IN_REVIEW_EXTENSION_SNIPPET -
)DOUBLE_QUOTES_IN_REVIEW_EXTENSION_SNIPPET!&
"QUOTES_IN_REVIEW_EXTENSION_SNIPPET"
INVALID_FORM_ENCODED_PARAMS#
INVALID_URL_PARAMETER_NAME$
NO_GEOCODING_RESULT%(
$SOURCE_NAME_IN_REVIEW_EXTENSION_TEXT&-
)CARRIER_SPECIFIC_SHORT_NUMBER_NOT_ALLOWED\' 
INVALID_PLACEHOLDER_FIELD_ID(
INVALID_URL_TAG)

LIST_TOO_LONG*"
INVALID_ATTRIBUTES_COMBINATION+
DUPLICATE_VALUES,%
!INVALID_CALL_CONVERSION_ACTION_ID-!
CANNOT_SET_WITHOUT_FINAL_URLS.$
 APP_ID_DOESNT_EXIST_IN_APP_STORE/
INVALID_FINAL_URL0
INVALID_TRACKING_URL1*
&INVALID_FINAL_URL_FOR_APP_DOWNLOAD_URL2
LIST_TOO_SHORT3
INVALID_USER_ACTION4
INVALID_TYPE_NAME5
INVALID_EVENT_CHANGE_STATUS6
INVALID_SNIPPETS_HEADER7
INVALID_ANDROID_APP_LINK8;
7NUMBER_TYPE_WITH_CALLTRACKING_NOT_SUPPORTED_FOR_COUNTRY9
RESERVED_KEYWORD_OTHER:
DUPLICATE_OPTION_LABELS;
DUPLICATE_OPTION_PREFILLS<
UNEQUAL_LIST_LENGTHS=
INCONSISTENT_CURRENCY_CODES>*
&PRICE_EXTENSION_HAS_DUPLICATED_HEADERS?.
*ITEM_HAS_DUPLICATED_HEADER_AND_DESCRIPTION@%
!PRICE_EXTENSION_HAS_TOO_FEW_ITEMSA
UNSUPPORTED_VALUEB
INVALID_FINAL_MOBILE_URLC%
!INVALID_KEYWORDLESS_AD_RULE_LABELD\'
#VALUE_TRACK_PARAMETER_NOT_SUPPORTEDE*
&UNSUPPORTED_VALUE_IN_SELECTED_LANGUAGEF
INVALID_IOS_APP_LINKG,
(MISSING_IOS_APP_LINK_OR_IOS_APP_STORE_IDH
PROMOTION_INVALID_TIMEI9
5PROMOTION_CANNOT_SET_PERCENT_OFF_AND_MONEY_AMOUNT_OFFJ>
:PROMOTION_CANNOT_SET_PROMOTION_CODE_AND_ORDERS_OVER_AMOUNTK%
!TOO_MANY_DECIMAL_PLACES_SPECIFIEDL
AD_CUSTOMIZERS_NOT_ALLOWEDM
INVALID_LANGUAGE_CODEN
UNSUPPORTED_LANGUAGEO
IF_FUNCTION_NOT_ALLOWEDP
INVALID_FINAL_URL_SUFFIXQ#
INVALID_TAG_IN_FINAL_URL_SUFFIXR#
INVALID_FINAL_URL_SUFFIX_FORMATS0
,CUSTOMER_CONSENT_FOR_CALL_RECORDING_REQUIREDT\'
#ONLY_ONE_DELIVERY_OPTION_IS_ALLOWEDU
NO_DELIVERY_OPTION_IS_SETV&
"INVALID_CONVERSION_REPORTING_STATEW
IMAGE_SIZE_WRONGX+
\'EMAIL_DELIVERY_NOT_AVAILABLE_IN_COUNTRYY\'
#AUTO_REPLY_NOT_AVAILABLE_IN_COUNTRYZ
INVALID_LATITUDE_VALUE[
INVALID_LONGITUDE_VALUE\\
TOO_MANY_LABELS]
INVALID_IMAGE_URL^
MISSING_LATITUDE_VALUE_
MISSING_LONGITUDE_VALUE`
ADDRESS_NOT_FOUNDa
ADDRESS_NOT_TARGETABLEb
INVALID_ASSET_IDd
INCOMPATIBLE_ASSET_TYPEe
IMAGE_ERROR_UNEXPECTED_SIZEf(
$IMAGE_ERROR_ASPECT_RATIO_NOT_ALLOWEDg
IMAGE_ERROR_FILE_TOO_LARGEh"
IMAGE_ERROR_FORMAT_NOT_ALLOWEDi$
 IMAGE_ERROR_CONSTRAINTS_VIOLATEDj
IMAGE_ERROR_SERVER_ERRORkB�
#com.google.ads.googleads.v17.errorsBFeedItemValidationErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Agoogle/ads/googleads/v17/errors/conversion_value_rule_error.protogoogle.ads.googleads.v17.errors"�
ConversionValueRuleErrorEnum"�
ConversionValueRuleError
UNSPECIFIED 
UNKNOWN
INVALID_GEO_TARGET_CONSTANT0
,CONFLICTING_INCLUDED_AND_EXCLUDED_GEO_TARGET
CONFLICTING_CONDITIONS/
+CANNOT_REMOVE_IF_INCLUDED_IN_VALUE_RULE_SET
CONDITION_NOT_ALLOWED
FIELD_MUST_BE_UNSET0
,CANNOT_PAUSE_UNLESS_VALUE_RULE_SET_IS_PAUSED
UNTARGETABLE_GEO_TARGET	
INVALID_AUDIENCE_USER_LIST

INACCESSIBLE_USER_LIST"
INVALID_AUDIENCE_USER_INTEREST\'
#CANNOT_ADD_RULE_WITH_STATUS_REMOVED
B�
#com.google.ads.googleads.v17.errorsBConversionValueRuleErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
1google/ads/googleads/v17/errors/range_error.protogoogle.ads.googleads.v17.errors"W
RangeErrorEnum"E

RangeError
UNSPECIFIED 
UNKNOWN
TOO_LOW
TOO_HIGHB�
#com.google.ads.googleads.v17.errorsBRangeErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Ggoogle/ads/googleads/v17/errors/recommendation_subscription_error.protogoogle.ads.googleads.v17.errors"f
#RecommendationSubscriptionErrorEnum"?
RecommendationSubscriptionError
UNSPECIFIED 
UNKNOWNB�
#com.google.ads.googleads.v17.errorsB$RecommendationSubscriptionErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
4google/ads/googleads/v17/errors/distinct_error.protogoogle.ads.googleads.v17.errors"m
DistinctErrorEnum"X

DistinctError
UNSPECIFIED 
UNKNOWN
DUPLICATE_ELEMENT
DUPLICATE_TYPEB�
#com.google.ads.googleads.v17.errorsBDistinctErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
6google/ads/googleads/v17/errors/ad_sharing_error.protogoogle.ads.googleads.v17.errors"�
AdSharingErrorEnum"�
AdSharingError
UNSPECIFIED 
UNKNOWN 
AD_GROUP_ALREADY_CONTAINS_AD"
INCOMPATIBLE_AD_UNDER_AD_GROUP
CANNOT_SHARE_INACTIVE_ADB�
#com.google.ads.googleads.v17.errorsBAdSharingErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
4google/ads/googleads/v17/errors/currency_error.protogoogle.ads.googleads.v17.errors"k
CurrencyErrorEnum"V

CurrencyError
UNSPECIFIED 
UNKNOWN\'
#VALUE_NOT_MULTIPLE_OF_BILLABLE_UNITB�
#com.google.ads.googleads.v17.errorsBCurrencyErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
;google/ads/googleads/v17/errors/custom_audience_error.protogoogle.ads.googleads.v17.errors"�
CustomAudienceErrorEnum"�
CustomAudienceError
UNSPECIFIED 
UNKNOWN
NAME_ALREADY_USED
CANNOT_REMOVE_WHILE_IN_USE
RESOURCE_ALREADY_REMOVED-
)MEMBER_TYPE_AND_PARAMETER_ALREADY_EXISTED
INVALID_MEMBER_TYPE(
$MEMBER_TYPE_AND_VALUE_DOES_NOT_MATCH
POLICY_VIOLATION
INVALID_TYPE_CHANGE	B�
#com.google.ads.googleads.v17.errorsBCustomAudienceErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Xgoogle/ads/googleads/v17/enums/policy_topic_evidence_destination_mismatch_url_type.protogoogle.ads.googleads.v17.enums"�
1PolicyTopicEvidenceDestinationMismatchUrlTypeEnum"�
-PolicyTopicEvidenceDestinationMismatchUrlType
UNSPECIFIED 
UNKNOWN
DISPLAY_URL
	FINAL_URL
FINAL_MOBILE_URL
TRACKING_URL
MOBILE_TRACKING_URLB�
"com.google.ads.googleads.v17.enumsB2PolicyTopicEvidenceDestinationMismatchUrlTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
9google/ads/googleads/v17/errors/ad_group_feed_error.protogoogle.ads.googleads.v17.errors"�
AdGroupFeedErrorEnum"�
AdGroupFeedError
UNSPECIFIED 
UNKNOWN,
(FEED_ALREADY_EXISTS_FOR_PLACEHOLDER_TYPE"
CANNOT_CREATE_FOR_REMOVED_FEED
ADGROUP_FEED_ALREADY_EXISTS*
&CANNOT_OPERATE_ON_REMOVED_ADGROUP_FEED
INVALID_PLACEHOLDER_TYPE,
(MISSING_FEEDMAPPING_FOR_PLACEHOLDER_TYPE&
"NO_EXISTING_LOCATION_CUSTOMER_FEEDB�
#com.google.ads.googleads.v17.errorsBAdGroupFeedErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�

Lgoogle/ads/googleads/v17/errors/asset_group_listing_group_filter_error.protogoogle.ads.googleads.v17.errors"�
%AssetGroupListingGroupFilterErrorEnum"�
!AssetGroupListingGroupFilterError
UNSPECIFIED 
UNKNOWN

TREE_TOO_DEEP
UNIT_CANNOT_HAVE_CHILDREN/
+SUBDIVISION_MUST_HAVE_EVERYTHING_ELSE_CHILD-
)DIFFERENT_DIMENSION_TYPE_BETWEEN_SIBLINGS)
%SAME_DIMENSION_VALUE_BETWEEN_SIBLINGS)
%SAME_DIMENSION_TYPE_BETWEEN_ANCESTORS
MULTIPLE_ROOTS
INVALID_DIMENSION_VALUE	(
$MUST_REFINE_HIERARCHICAL_PARENT_TYPE
$
 INVALID_PRODUCT_BIDDING_CATEGORY%
!CHANGING_CASE_VALUE_WITH_CHILDREN
SUBDIVISION_HAS_CHILDREN
.
*CANNOT_REFINE_HIERARCHICAL_EVERYTHING_ELSE
DIMENSION_TYPE_NOT_ALLOWED.
*DUPLICATE_WEBPAGE_FILTER_UNDER_ASSET_GROUP
LISTING_SOURCE_NOT_ALLOWED 
FILTER_EXCLUSION_NOT_ALLOWED
MULTIPLE_LISTING_SOURCES0
,MULTIPLE_WEBPAGE_CONDITION_TYPES_NOT_ALLOWED*
&MULTIPLE_WEBPAGE_TYPES_PER_ASSET_GROUP
PAGE_FEED_FILTER_HAS_PARENT#
MULTIPLE_OPERATIONS_ON_ONE_NODEB�
#com.google.ads.googleads.v17.errorsB&AssetGroupListingGroupFilterErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
6google/ads/googleads/v17/errors/experiment_error.protogoogle.ads.googleads.v17.errors"�	
ExperimentErrorEnum"�	
ExperimentError
UNSPECIFIED 
UNKNOWN!
CANNOT_SET_START_DATE_IN_PAST
END_DATE_BEFORE_START_DATE 
START_DATE_TOO_FAR_IN_FUTURE
DUPLICATE_EXPERIMENT_NAME$
 CANNOT_MODIFY_REMOVED_EXPERIMENT
START_DATE_ALREADY_PASSED
CANNOT_SET_END_DATE_IN_PAST 
CANNOT_SET_STATUS_TO_REMOVED	
CANNOT_MODIFY_PAST_END_DATE

INVALID_STATUS!
INVALID_CAMPAIGN_CHANNEL_TYPE&
"OVERLAPPING_MEMBERS_AND_DATE_RANGE
#
INVALID_TRIAL_ARM_TRAFFIC_SPLIT
TRAFFIC_SPLIT_OVERLAPPINGE
ASUM_TRIAL_ARM_TRAFFIC_UNEQUALS_TO_TRIAL_TRAFFIC_SPLIT_DENOMINATOR+
\'CANNOT_MODIFY_TRAFFIC_SPLIT_AFTER_START
EXPERIMENT_NOT_FOUND
EXPERIMENT_NOT_YET_STARTED%
!CANNOT_HAVE_MULTIPLE_CONTROL_ARMS
IN_DESIGN_CAMPAIGNS_NOT_SET"
CANNOT_SET_STATUS_TO_GRADUATED8
4CANNOT_CREATE_EXPERIMENT_CAMPAIGN_WITH_SHARED_BUDGET8
4CANNOT_CREATE_EXPERIMENT_CAMPAIGN_WITH_CUSTOM_BUDGET
STATUS_TRANSITION_INVALID&
"DUPLICATE_EXPERIMENT_CAMPAIGN_NAME(
$CANNOT_REMOVE_IN_CREATION_EXPERIMENT0
,CANNOT_ADD_CAMPAIGN_WITH_DEPRECATED_AD_TYPES6
2CANNOT_ENABLE_SYNC_FOR_UNSUPPORTED_EXPERIMENT_TYPE&
"INVALID_DURATION_FOR_AN_EXPERIMENTB�
#com.google.ads.googleads.v17.errorsBExperimentErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Cgoogle/ads/googleads/v17/errors/customer_lifecycle_goal_error.protogoogle.ads.googleads.v17.errors"�
CustomerLifecycleGoalErrorEnum"�
CustomerLifecycleGoalError
UNSPECIFIED 
UNKNOWN&
"CUSTOMER_ACQUISITION_VALUE_MISSING&
"CUSTOMER_ACQUISITION_INVALID_VALUE4
0CUSTOMER_ACQUISITION_INVALID_HIGH_LIFETIME_VALUE0
,CUSTOMER_ACQUISITION_VALUE_CANNOT_BE_CLEARED>
:CUSTOMER_ACQUISITION_HIGH_LIFETIME_VALUE_CANNOT_BE_CLEARED
INVALID_EXISTING_USER_LIST)
%INVALID_HIGH_LIFETIME_VALUE_USER_LISTB�
#com.google.ads.googleads.v17.errorsBCustomerLifecycleGoalErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
1google/ads/googleads/v17/errors/image_error.protogoogle.ads.googleads.v17.errors"�
ImageErrorEnum"�

ImageError
UNSPECIFIED 
UNKNOWN

INVALID_IMAGE

STORAGE_ERROR
BAD_REQUEST
UNEXPECTED_SIZE
ANIMATED_NOT_ALLOWED
ANIMATION_TOO_LONG
SERVER_ERROR
CMYK_JPEG_NOT_ALLOWED	
FLASH_NOT_ALLOWED

FLASH_WITHOUT_CLICKTAG&
"FLASH_ERROR_AFTER_FIXING_CLICK_TAG
ANIMATED_VISUAL_EFFECT

FLASH_ERROR
LAYOUT_PROBLEM
PROBLEM_READING_IMAGE_FILE
ERROR_STORING_IMAGE
ASPECT_RATIO_NOT_ALLOWED
FLASH_HAS_NETWORK_OBJECTS
FLASH_HAS_NETWORK_METHODS

FLASH_HAS_URL
FLASH_HAS_MOUSE_TRACKING
FLASH_HAS_RANDOM_NUM
FLASH_SELF_TARGETS
FLASH_BAD_GETURL_TARGET
FLASH_VERSION_NOT_SUPPORTED&
"FLASH_WITHOUT_HARD_CODED_CLICK_URL
INVALID_FLASH_FILE$
 FAILED_TO_FIX_CLICK_TAG_IN_FLASH$
 FLASH_ACCESSES_NETWORK_RESOURCES
FLASH_EXTERNAL_JS_CALL
FLASH_EXTERNAL_FS_CALL 
FILE_TOO_LARGE!
IMAGE_DATA_TOO_LARGE"
IMAGE_PROCESSING_ERROR#
IMAGE_TOO_SMALL$

INVALID_INPUT%
PROBLEM_READING_FILE&
IMAGE_CONSTRAINTS_VIOLATED\'
FORMAT_NOT_ALLOWED(B�
#com.google.ads.googleads.v17.errorsBImageErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Agoogle/ads/googleads/v17/errors/keyword_plan_ad_group_error.protogoogle.ads.googleads.v17.errors"|
KeywordPlanAdGroupErrorEnum"]
KeywordPlanAdGroupError
UNSPECIFIED 
UNKNOWN
INVALID_NAME
DUPLICATE_NAMEB�
#com.google.ads.googleads.v17.errorsBKeywordPlanAdGroupErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
9google/ads/googleads/v17/errors/feed_item_set_error.protogoogle.ads.googleads.v17.errors"�
FeedItemSetErrorEnum"�
FeedItemSetError
UNSPECIFIED 
UNKNOWN
FEED_ITEM_SET_REMOVED
CANNOT_CLEAR_DYNAMIC_FILTER 
CANNOT_CREATE_DYNAMIC_FILTER
INVALID_FEED_TYPE
DUPLICATE_NAME&
"WRONG_DYNAMIC_FILTER_FOR_FEED_TYPE$
 DYNAMIC_FILTER_INVALID_CHAIN_IDSB�
#com.google.ads.googleads.v17.errorsBFeedItemSetErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
1google/ads/googleads/v17/errors/label_error.protogoogle.ads.googleads.v17.errors"�
LabelErrorEnum"�

LabelError
UNSPECIFIED 
UNKNOWN
CANNOT_APPLY_INACTIVE_LABEL5
1CANNOT_APPLY_LABEL_TO_DISABLED_AD_GROUP_CRITERION5
1CANNOT_APPLY_LABEL_TO_NEGATIVE_AD_GROUP_CRITERION!
EXCEEDED_LABEL_LIMIT_PER_TYPE&
"INVALID_RESOURCE_FOR_MANAGER_LABEL
DUPLICATE_NAME
INVALID_LABEL_NAME 
CANNOT_ATTACH_LABEL_TO_DRAFT	/
+CANNOT_ATTACH_NON_MANAGER_LABEL_TO_CUSTOMER
B�
#com.google.ads.googleads.v17.errorsBLabelErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
6google/ads/googleads/v17/errors/multiplier_error.protogoogle.ads.googleads.v17.errors"�
MultiplierErrorEnum"�
MultiplierError
UNSPECIFIED 
UNKNOWN
MULTIPLIER_TOO_HIGH
MULTIPLIER_TOO_LOW
TOO_MANY_FRACTIONAL_DIGITS/
+MULTIPLIER_NOT_ALLOWED_FOR_BIDDING_STRATEGY3
/MULTIPLIER_NOT_ALLOWED_WHEN_BASE_BID_IS_MISSING
NO_MULTIPLIER_SPECIFIED0
,MULTIPLIER_CAUSES_BID_TO_EXCEED_DAILY_BUDGET2
.MULTIPLIER_CAUSES_BID_TO_EXCEED_MONTHLY_BUDGET	1
-MULTIPLIER_CAUSES_BID_TO_EXCEED_CUSTOM_BUDGET
3
/MULTIPLIER_CAUSES_BID_TO_EXCEED_MAX_ALLOWED_BID1
-BID_LESS_THAN_MIN_ALLOWED_BID_WITH_MULTIPLIER1
-MULTIPLIER_AND_BIDDING_STRATEGY_TYPE_MISMATCH
B�
#com.google.ads.googleads.v17.errorsBMultiplierErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
7google/ads/googleads/v17/errors/asset_group_error.protogoogle.ads.googleads.v17.errors"�
AssetGroupErrorEnum"�
AssetGroupError
UNSPECIFIED 
UNKNOWN
DUPLICATE_NAME,
(CANNOT_ADD_ASSET_GROUP_FOR_CAMPAIGN_TYPE
NOT_ENOUGH_HEADLINE_ASSET"
NOT_ENOUGH_LONG_HEADLINE_ASSET 
NOT_ENOUGH_DESCRIPTION_ASSET"
NOT_ENOUGH_BUSINESS_NAME_ASSET$
 NOT_ENOUGH_MARKETING_IMAGE_ASSET+
\'NOT_ENOUGH_SQUARE_MARKETING_IMAGE_ASSET	
NOT_ENOUGH_LOGO_ASSET
<
8FINAL_URL_SHOPPING_MERCHANT_HOME_PAGE_URL_DOMAINS_DIFFER$
 PATH1_REQUIRED_WHEN_PATH2_IS_SET
SHORT_DESCRIPTION_REQUIRED

FINAL_URL_REQUIRED*
&FINAL_URL_CONTAINS_INVALID_DOMAIN_NAME
AD_CUSTOMIZER_NOT_SUPPORTED2
.CANNOT_MUTATE_ASSET_GROUP_FOR_REMOVED_CAMPAIGNB�
#com.google.ads.googleads.v17.errorsBAssetGroupErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
0google/ads/googleads/v17/errors/enum_error.protogoogle.ads.googleads.v17.errors"X

EnumErrorEnum"G
	EnumError
UNSPECIFIED 
UNKNOWN
ENUM_VALUE_NOT_PERMITTEDB�
#com.google.ads.googleads.v17.errorsBEnumErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
?google/ads/googleads/v17/errors/search_term_insight_error.protogoogle.ads.googleads.v17.errors"�
SearchTermInsightErrorEnum"�
SearchTermInsightError
UNSPECIFIED 
UNKNOWN\'
#FILTERING_NOT_ALLOWED_WITH_SEGMENTS#
LIMIT_NOT_ALLOWED_WITH_SEGMENTS"
MISSING_FIELD_IN_SELECT_CLAUSE&
"REQUIRES_FILTER_BY_SINGLE_RESOURCE%
!SORTING_NOT_ALLOWED_WITH_SEGMENTS)
%SUMMARY_ROW_NOT_ALLOWED_WITH_SEGMENTSB�
#com.google.ads.googleads.v17.errorsBSearchTermInsightErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
?google/ads/googleads/v17/errors/ad_group_customizer_error.protogoogle.ads.googleads.v17.errors"T
AdGroupCustomizerErrorEnum"6
AdGroupCustomizerError
UNSPECIFIED 
UNKNOWNB�
#com.google.ads.googleads.v17.errorsBAdGroupCustomizerErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
4google/ads/googleads/v17/errors/audience_error.protogoogle.ads.googleads.v17.errors"�
AudienceErrorEnum"�

AudienceError
UNSPECIFIED 
UNKNOWN
NAME_ALREADY_IN_USE
DIMENSION_INVALID
AUDIENCE_SEGMENT_NOT_FOUND\'
#AUDIENCE_SEGMENT_TYPE_NOT_SUPPORTED
DUPLICATE_AUDIENCE_SEGMENT
TOO_MANY_SEGMENTS$
 TOO_MANY_DIMENSIONS_OF_SAME_TYPE

IN_USE	
MISSING_ASSET_GROUP_ID
4
0CANNOT_CHANGE_FROM_CUSTOMER_TO_ASSET_GROUP_SCOPEB�
#com.google.ads.googleads.v17.errorsBAudienceErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�

6google/ads/googleads/v17/errors/asset_link_error.protogoogle.ads.googleads.v17.errors"�
AssetLinkErrorEnum"�
AssetLinkError
UNSPECIFIED 
UNKNOWN
PINNING_UNSUPPORTED
UNSUPPORTED_FIELD_TYPE+
\'FIELD_TYPE_INCOMPATIBLE_WITH_ASSET_TYPE.
*FIELD_TYPE_INCOMPATIBLE_WITH_CAMPAIGN_TYPE)
%INCOMPATIBLE_ADVERTISING_CHANNEL_TYPE.
*IMAGE_NOT_WITHIN_SPECIFIED_DIMENSION_RANGE
INVALID_PINNED_FIELD*
&MEDIA_BUNDLE_ASSET_FILE_SIZE_TOO_LARGE	:
6NOT_ENOUGH_AVAILABLE_ASSET_LINKS_FOR_VALID_COMBINATION
2
.NOT_ENOUGH_AVAILABLE_ASSET_LINKS_WITH_FALLBACKH
DNOT_ENOUGH_AVAILABLE_ASSET_LINKS_WITH_FALLBACK_FOR_VALID_COMBINATION
YOUTUBE_VIDEO_REMOVED

YOUTUBE_VIDEO_TOO_LONG
YOUTUBE_VIDEO_TOO_SHORT
EXCLUDED_PARENT_FIELD_TYPE
INVALID_STATUS&
"YOUTUBE_VIDEO_DURATION_NOT_DEFINED-
)CANNOT_CREATE_AUTOMATICALLY_CREATED_LINKS.
*CANNOT_LINK_TO_AUTOMATICALLY_CREATED_ASSET#
CANNOT_MODIFY_ASSET_LINK_SOURCE9
5CANNOT_LINK_LOCATION_LEAD_FORM_WITHOUT_LOCATION_ASSET
CUSTOMER_NOT_VERIFIED
UNSUPPORTED_CALL_TO_ACTIONB�
#com.google.ads.googleads.v17.errorsBAssetLinkErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
:google/ads/googleads/v17/errors/campaign_draft_error.protogoogle.ads.googleads.v17.errors"�
CampaignDraftErrorEnum"�
CampaignDraftError
UNSPECIFIED 
UNKNOWN
DUPLICATE_DRAFT_NAME*
&INVALID_STATUS_TRANSITION_FROM_REMOVED+
\'INVALID_STATUS_TRANSITION_FROM_PROMOTED1
-INVALID_STATUS_TRANSITION_FROM_PROMOTE_FAILED 
CUSTOMER_CANNOT_CREATE_DRAFT 
CAMPAIGN_CANNOT_CREATE_DRAFT
INVALID_DRAFT_CHANGE
INVALID_STATUS_TRANSITION	-
)MAX_NUMBER_OF_DRAFTS_PER_CAMPAIGN_REACHED
\'
#LIST_ERRORS_FOR_PROMOTED_DRAFT_ONLYB�
#com.google.ads.googleads.v17.errorsBCampaignDraftErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Zgoogle/ads/googleads/v17/errors/customer_sk_ad_network_conversion_value_schema_error.protogoogle.ads.googleads.v17.errors"�
1CustomerSkAdNetworkConversionValueSchemaErrorEnum"�
-CustomerSkAdNetworkConversionValueSchemaError
UNSPECIFIED 
UNKNOWN
INVALID_LINK_ID
INVALID_APP_ID
INVALID_SCHEMA
LINK_CODE_NOT_FOUND
INVALID_EVENT_COUNTER
INVALID_EVENT_NAMEB�
#com.google.ads.googleads.v17.errorsB2CustomerSkAdNetworkConversionValueSchemaErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
@google/ads/googleads/v17/errors/customer_user_access_error.protogoogle.ads.googleads.v17.errors"�
CustomerUserAccessErrorEnum"�
CustomerUserAccessError
UNSPECIFIED 
UNKNOWN
INVALID_USER_ID
REMOVAL_DISALLOWED
DISALLOWED_ACCESS_ROLE\'
#LAST_ADMIN_USER_OF_SERVING_CUSTOMER
LAST_ADMIN_USER_OF_MANAGERB�
#com.google.ads.googleads.v17.errorsBCustomerUserAccessErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
8google/ads/googleads/v17/errors/keyword_plan_error.protogoogle.ads.googleads.v17.errors"�
KeywordPlanErrorEnum"�
KeywordPlanError
UNSPECIFIED 
UNKNOWN
BID_MULTIPLIER_OUT_OF_RANGE
BID_TOO_HIGH
BID_TOO_LOW"
BID_TOO_MANY_FRACTIONAL_DIGITS
DAILY_BUDGET_TOO_LOW+
\'DAILY_BUDGET_TOO_MANY_FRACTIONAL_DIGITS

INVALID_VALUE 
KEYWORD_PLAN_HAS_NO_KEYWORDS	
KEYWORD_PLAN_NOT_ENABLED

KEYWORD_PLAN_NOT_FOUND
MISSING_BID

MISSING_FORECAST_PERIOD
INVALID_FORECAST_DATE_RANGE
INVALID_NAMEB�
#com.google.ads.googleads.v17.errorsBKeywordPlanErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
?google/ads/googleads/v17/errors/campaign_customizer_error.protogoogle.ads.googleads.v17.errors"V
CampaignCustomizerErrorEnum"7
CampaignCustomizerError
UNSPECIFIED 
UNKNOWNB�
#com.google.ads.googleads.v17.errorsBCampaignCustomizerErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Egoogle/ads/googleads/v17/errors/conversion_value_rule_set_error.protogoogle.ads.googleads.v17.errors"�
ConversionValueRuleSetErrorEnum"�
ConversionValueRuleSetError
UNSPECIFIED 
UNKNOWN%
!CONFLICTING_VALUE_RULE_CONDITIONS
INVALID_VALUE_RULE\'
#DIMENSIONS_UPDATE_ONLY_ALLOW_APPEND
CONDITION_TYPE_NOT_ALLOWED
DUPLICATE_DIMENSIONS
INVALID_CAMPAIGN_ID2
.CANNOT_PAUSE_UNLESS_ALL_VALUE_RULES_ARE_PAUSED0
,SHOULD_PAUSE_WHEN_ALL_VALUE_RULES_ARE_PAUSED	/
+VALUE_RULES_NOT_SUPPORTED_FOR_CAMPAIGN_TYPE
+
\'INELIGIBLE_CONVERSION_ACTION_CATEGORIES5
1DIMENSION_NO_CONDITION_USED_WITH_OTHER_DIMENSIONS&
"DIMENSION_NO_CONDITION_NOT_ALLOWED
,
(UNSUPPORTED_CONVERSION_ACTION_CATEGORIESB�
#com.google.ads.googleads.v17.errorsB ConversionValueRuleSetErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
;google/ads/googleads/v17/errors/not_allowlisted_error.protogoogle.ads.googleads.v17.errors"}
NotAllowlistedErrorEnum"b
NotAllowlistedError
UNSPECIFIED 
UNKNOWN-
)CUSTOMER_NOT_ALLOWLISTED_FOR_THIS_FEATUREB�
#com.google.ads.googleads.v17.errorsBNotAllowlistedErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
:google/ads/googleads/v17/errors/policy_finding_error.protogoogle.ads.googleads.v17.errors"|
PolicyFindingErrorEnum"b
PolicyFindingError
UNSPECIFIED 
UNKNOWN
POLICY_FINDING
POLICY_TOPIC_NOT_FOUNDB�
#com.google.ads.googleads.v17.errorsBPolicyFindingErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
1google/ads/googleads/v17/errors/query_error.protogoogle.ads.googleads.v17.errors"�
QueryErrorEnum"�

QueryError
UNSPECIFIED 
UNKNOWN
QUERY_ERROR2
BAD_ENUM_CONSTANT
BAD_ESCAPE_SEQUENCE
BAD_FIELD_NAME
BAD_LIMIT_VALUE

BAD_NUMBER
BAD_OPERATOR
BAD_PARAMETER_NAME=
BAD_PARAMETER_VALUE>$
 BAD_RESOURCE_TYPE_IN_FROM_CLAUSE-

BAD_SYMBOL
	BAD_VALUE
DATE_RANGE_TOO_WIDE$
DATE_RANGE_TOO_NARROW<
EXPECTED_AND
EXPECTED_BY-
)EXPECTED_DIMENSION_FIELD_IN_SELECT_CLAUSE%"
EXPECTED_FILTERS_ON_DATE_RANGE7

EXPECTED_FROM,

EXPECTED_LIST).
*EXPECTED_REFERENCED_FIELD_IN_SELECT_CLAUSE
EXPECTED_SELECT

EXPECTED_SINGLE_VALUE*(
$EXPECTED_VALUE_WITH_BETWEEN_OPERATOR
INVALID_DATE_FORMAT&
MISALIGNED_DATE_FOR_FILTER@
INVALID_STRING_VALUE9\'
#INVALID_VALUE_WITH_BETWEEN_OPERATOR&
"INVALID_VALUE_WITH_DURING_OPERATOR$
 INVALID_VALUE_WITH_LIKE_OPERATOR8
OPERATOR_FIELD_MISMATCH#&
"PROHIBITED_EMPTY_LIST_IN_CONDITION
PROHIBITED_ENUM_CONSTANT61
-PROHIBITED_FIELD_COMBINATION_IN_SELECT_CLAUSE\'
#PROHIBITED_FIELD_IN_ORDER_BY_CLAUSE(%
!PROHIBITED_FIELD_IN_SELECT_CLAUSE$
 PROHIBITED_FIELD_IN_WHERE_CLAUSE+
\'PROHIBITED_RESOURCE_TYPE_IN_FROM_CLAUSE+-
)PROHIBITED_RESOURCE_TYPE_IN_SELECT_CLAUSE0,
(PROHIBITED_RESOURCE_TYPE_IN_WHERE_CLAUSE:/
+PROHIBITED_METRIC_IN_SELECT_OR_WHERE_CLAUSE10
,PROHIBITED_SEGMENT_IN_SELECT_OR_WHERE_CLAUSE3<
8PROHIBITED_SEGMENT_WITH_METRIC_IN_SELECT_OR_WHERE_CLAUSE5
LIMIT_VALUE_TOO_LOW 
PROHIBITED_NEWLINE_IN_STRING(
$PROHIBITED_VALUE_COMBINATION_IN_LIST
6
2PROHIBITED_VALUE_COMBINATION_WITH_BETWEEN_OPERATOR
STRING_NOT_TERMINATED
TOO_MANY_SEGMENTS"
UNEXPECTED_END_OF_QUERY	
UNEXPECTED_FROM_CLAUSE/
UNRECOGNIZED_FIELD 
UNEXPECTED_INPUT!
REQUESTED_METRICS_FOR_MANAGER;
FILTER_HAS_TOO_MANY_VALUES?B�
#com.google.ads.googleads.v17.errorsBQueryErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
6google/ads/googleads/v17/errors/reach_plan_error.protogoogle.ads.googleads.v17.errors"�
ReachPlanErrorEnum"�
ReachPlanError
UNSPECIFIED 
UNKNOWN!
NOT_FORECASTABLE_MISSING_RATE)
%NOT_FORECASTABLE_NOT_ENOUGH_INVENTORY(
$NOT_FORECASTABLE_ACCOUNT_NOT_ENABLEDB�
#com.google.ads.googleads.v17.errorsBReachPlanErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
8google/ads/googleads/v17/errors/ad_parameter_error.protogoogle.ads.googleads.v17.errors"�
AdParameterErrorEnum"{
AdParameterError
UNSPECIFIED 
UNKNOWN&
"AD_GROUP_CRITERION_MUST_BE_KEYWORD!
INVALID_INSERTION_TEXT_FORMATB�
#com.google.ads.googleads.v17.errorsBAdParameterErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
7google/ads/googleads/v17/errors/ad_group_ad_error.protogoogle.ads.googleads.v17.errors"�
AdGroupAdErrorEnum"�
AdGroupAdError
UNSPECIFIED 
UNKNOWN$
 AD_GROUP_AD_LABEL_DOES_NOT_EXIST$
 AD_GROUP_AD_LABEL_ALREADY_EXISTS
AD_NOT_UNDER_ADGROUP\'
#CANNOT_OPERATE_ON_REMOVED_ADGROUPAD 
CANNOT_CREATE_DEPRECATED_ADS
CANNOT_CREATE_TEXT_ADS
EMPTY_FIELD\'
#RESOURCE_REFERENCED_IN_MULTIPLE_OPS	
AD_TYPE_CANNOT_BE_PAUSED

AD_TYPE_CANNOT_BE_REMOVED 
CANNOT_UPDATE_DEPRECATED_ADSB�
#com.google.ads.googleads.v17.errorsBAdGroupAdErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�+
.google/ads/googleads/v17/errors/ad_error.protogoogle.ads.googleads.v17.errors"�(
AdErrorEnum"�(
AdError
UNSPECIFIED 
UNKNOWN,
(AD_CUSTOMIZERS_NOT_SUPPORTED_FOR_AD_TYPE
APPROXIMATELY_TOO_LONG
APPROXIMATELY_TOO_SHORT
BAD_SNIPPET
CANNOT_MODIFY_AD\'
#CANNOT_SET_BUSINESS_NAME_IF_URL_SET
CANNOT_SET_FIELD*
&CANNOT_SET_FIELD_WITH_ORIGIN_AD_ID_SET	/
+CANNOT_SET_FIELD_WITH_AD_ID_SET_FOR_SHARING
)
%CANNOT_SET_ALLOW_FLEXIBLE_COLOR_FALSE7
3CANNOT_SET_COLOR_CONTROL_WHEN_NATIVE_FORMAT_SETTING
CANNOT_SET_URL
!
CANNOT_SET_WITHOUT_FINAL_URLS
CANNOT_SET_WITH_FINAL_URLS
CANNOT_SET_WITH_URL_DATA\'
#CANNOT_USE_AD_SUBCLASS_FOR_OPERATOR#
CUSTOMER_NOT_APPROVED_MOBILEADS(
$CUSTOMER_NOT_APPROVED_THIRDPARTY_ADS1
-CUSTOMER_NOT_APPROVED_THIRDPARTY_REDIRECT_ADS
CUSTOMER_NOT_ELIGIBLE1
-CUSTOMER_NOT_ELIGIBLE_FOR_UPDATING_BEACON_URL
DIMENSION_ALREADY_IN_UNION
DIMENSION_MUST_BE_SET
DIMENSION_NOT_IN_UNION#
DISPLAY_URL_CANNOT_BE_SPECIFIED 
DOMESTIC_PHONE_NUMBER_FORMAT
EMERGENCY_PHONE_NUMBER
EMPTY_FIELD0
,FEED_ATTRIBUTE_MUST_HAVE_MAPPING_FOR_TYPE_ID(
$FEED_ATTRIBUTE_MAPPING_TYPE_MISMATCH !
ILLEGAL_AD_CUSTOMIZER_TAG_USE!
ILLEGAL_TAG_USE"
INCONSISTENT_DIMENSIONS#)
%INCONSISTENT_STATUS_IN_TEMPLATE_UNION$
INCORRECT_LENGTH%
INELIGIBLE_FOR_UPGRADE&&
"INVALID_AD_ADDRESS_CAMPAIGN_TARGET\'
INVALID_AD_TYPE(\'
#INVALID_ATTRIBUTES_FOR_MOBILE_IMAGE)&
"INVALID_ATTRIBUTES_FOR_MOBILE_TEXT*
INVALID_CALL_TO_ACTION_TEXT+
INVALID_CHARACTER_FOR_URL,
INVALID_COUNTRY_CODE-*
&INVALID_EXPANDED_DYNAMIC_SEARCH_AD_TAG/

INVALID_INPUT0
INVALID_MARKUP_LANGUAGE1
INVALID_MOBILE_CARRIER2!
INVALID_MOBILE_CARRIER_TARGET3
INVALID_NUMBER_OF_ELEMENTS4
INVALID_PHONE_NUMBER_FORMAT51
-INVALID_RICH_MEDIA_CERTIFIED_VENDOR_FORMAT_ID6
INVALID_TEMPLATE_DATA7\'
#INVALID_TEMPLATE_ELEMENT_FIELD_TYPE8
INVALID_TEMPLATE_ID9

LINE_TOO_WIDE:!
MISSING_AD_CUSTOMIZER_MAPPING;
MISSING_ADDRESS_COMPONENT<
MISSING_ADVERTISEMENT_NAME=
MISSING_BUSINESS_NAME>
MISSING_DESCRIPTION1?
MISSING_DESCRIPTION2@
MISSING_DESTINATION_URL_TAGA 
MISSING_LANDING_PAGE_URL_TAGB
MISSING_DIMENSIONC
MISSING_DISPLAY_URLD
MISSING_HEADLINEE
MISSING_HEIGHTF

MISSING_IMAGEG-
)MISSING_MARKETING_IMAGE_OR_PRODUCT_VIDEOSH
MISSING_MARKUP_LANGUAGESI
MISSING_MOBILE_CARRIERJ

MISSING_PHONEK$
 MISSING_REQUIRED_TEMPLATE_FIELDSL 
MISSING_TEMPLATE_FIELD_VALUEM
MISSING_TEXTN
MISSING_VISIBLE_URLO

MISSING_WIDTHP\'
#MULTIPLE_DISTINCT_FEEDS_UNSUPPORTEDQ$
 MUST_USE_TEMP_AD_UNION_ID_ON_ADDR
TOO_LONGS
	TOO_SHORTT"
UNION_DIMENSIONS_CANNOT_CHANGEU
UNKNOWN_ADDRESS_COMPONENTV
UNKNOWN_FIELD_NAMEW
UNKNOWN_UNIQUE_NAMEX
UNSUPPORTED_DIMENSIONSY
URL_INVALID_SCHEMEZ 
URL_INVALID_TOP_LEVEL_DOMAIN[

URL_MALFORMED\\
URL_NO_HOST]
URL_NOT_EQUIVALENT^
URL_HOST_NAME_TOO_LONG_

URL_NO_SCHEME`
URL_NO_TOP_LEVEL_DOMAINa
URL_PATH_NOT_ALLOWEDb
URL_PORT_NOT_ALLOWEDc
URL_QUERY_NOT_ALLOWEDd4
0URL_SCHEME_BEFORE_EXPANDED_DYNAMIC_SEARCH_AD_TAGf)
%USER_DOES_NOT_HAVE_ACCESS_TO_TEMPLATEg$
 INCONSISTENT_EXPANDABLE_SETTINGSh
INVALID_FORMATi
INVALID_FIELD_TEXTj
ELEMENT_NOT_PRESENTk
IMAGE_ERRORl
VALUE_NOT_IN_RANGEm
FIELD_NOT_PRESENTn
ADDRESS_NOT_COMPLETEo
ADDRESS_INVALIDp
VIDEO_RETRIEVAL_ERRORq
AUDIO_ERRORr
INVALID_YOUTUBE_DISPLAY_URLs
TOO_MANY_PRODUCT_IMAGESt
TOO_MANY_PRODUCT_VIDEOSu.
*INCOMPATIBLE_AD_TYPE_AND_DEVICE_PREFERENCEv*
&CALLTRACKING_NOT_SUPPORTED_FOR_COUNTRYw-
)CARRIER_SPECIFIC_SHORT_NUMBER_NOT_ALLOWEDx
DISALLOWED_NUMBER_TYPEy*
&PHONE_NUMBER_NOT_SUPPORTED_FOR_COUNTRYz<
8PHONE_NUMBER_NOT_SUPPORTED_WITH_CALLTRACKING_FOR_COUNTRY{#
PREMIUM_RATE_NUMBER_NOT_ALLOWED|#
VANITY_PHONE_NUMBER_NOT_ALLOWED}#
INVALID_CALL_CONVERSION_TYPE_ID~=
9CANNOT_DISABLE_CALL_CONVERSION_AND_SET_CONVERSION_TYPE_ID#
CANNOT_SET_PATH2_WITHOUT_PATH1�3
.MISSING_DYNAMIC_SEARCH_ADS_SETTING_DOMAIN_NAME�\'
"INCOMPATIBLE_WITH_RESTRICTION_TYPE�1
,CUSTOMER_CONSENT_FOR_CALL_RECORDING_REQUIRED�"
MISSING_IMAGE_OR_MEDIA_BUNDLE�0
+PRODUCT_TYPE_NOT_SUPPORTED_IN_THIS_CAMPAIGN�0
+PLACEHOLDER_CANNOT_HAVE_EMPTY_DEFAULT_VALUE�=
8PLACEHOLDER_COUNTDOWN_FUNCTION_CANNOT_HAVE_DEFAULT_VALUE�&
!PLACEHOLDER_DEFAULT_VALUE_MISSING�)
$UNEXPECTED_PLACEHOLDER_DEFAULT_VALUE�\'
"AD_CUSTOMIZERS_MAY_NOT_BE_ADJACENT�,
\'UPDATING_AD_WITH_NO_ENABLED_ASSOCIATION�A
<CALL_AD_VERIFICATION_URL_FINAL_URL_DOES_NOT_HAVE_SAME_DOMAIN�@
;CALL_AD_FINAL_URL_AND_VERIFICATION_URL_CANNOT_BOTH_BE_EMPTY�
TOO_MANY_AD_CUSTOMIZERS�!
INVALID_AD_CUSTOMIZER_FORMAT� 
NESTED_AD_CUSTOMIZER_SYNTAX�%
 UNSUPPORTED_AD_CUSTOMIZER_SYNTAX�(
#UNPAIRED_BRACE_IN_AD_CUSTOMIZER_TAG�,
\'MORE_THAN_ONE_COUNTDOWN_TAG_TYPE_EXISTS�*
%DATE_TIME_IN_COUNTDOWN_TAG_IS_INVALID�\'
"DATE_TIME_IN_COUNTDOWN_TAG_IS_PAST�)
$UNRECOGNIZED_AD_CUSTOMIZER_TAG_FOUND�(
#CUSTOMIZER_TYPE_FORBIDDEN_FOR_FIELD�&
!INVALID_CUSTOMIZER_ATTRIBUTE_NAME�
STORE_MISMATCH�(
#MISSING_REQUIRED_IMAGE_ASPECT_RATIO�
MISMATCHED_ASPECT_RATIOS�*
%DUPLICATE_IMAGE_ACROSS_CAROUSEL_CARDS�B�
#com.google.ads.googleads.v17.errorsBAdErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Bgoogle/ads/googleads/v17/errors/custom_conversion_goal_error.protogoogle.ads.googleads.v17.errors"�
CustomConversionGoalErrorEnum"�
CustomConversionGoalError
UNSPECIFIED 
UNKNOWN
INVALID_CONVERSION_ACTION!
CONVERSION_ACTION_NOT_ENABLED/
+CANNOT_REMOVE_LINKED_CUSTOM_CONVERSION_GOAL
CUSTOM_GOAL_DUPLICATE_NAME$
 DUPLICATE_CONVERSION_ACTION_LIST?
;NON_BIDDABLE_CONVERSION_ACTION_NOT_ELIGIBLE_FOR_CUSTOM_GOALB�
#com.google.ads.googleads.v17.errorsBCustomConversionGoalErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Igoogle/ads/googleads/v17/errors/keyword_plan_campaign_keyword_error.protogoogle.ads.googleads.v17.errors"�
#KeywordPlanCampaignKeywordErrorEnum"a
KeywordPlanCampaignKeywordError
UNSPECIFIED 
UNKNOWN 
CAMPAIGN_KEYWORD_IS_POSITIVEB�
#com.google.ads.googleads.v17.errorsB$KeywordPlanCampaignKeywordErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
<google/ads/googleads/v17/errors/shopping_product_error.protogoogle.ads.googleads.v17.errors"�
ShoppingProductErrorEnum"�
ShoppingProductError
UNSPECIFIED 
UNKNOWN
MISSING_CAMPAIGN_FILTER
MISSING_AD_GROUP_FILTER!
UNSUPPORTED_DATE_SEGMENTATIONB�
#com.google.ads.googleads.v17.errorsBShoppingProductErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
4google/ads/googleads/v17/errors/function_error.protogoogle.ads.googleads.v17.errors"�
FunctionErrorEnum"�

FunctionError
UNSPECIFIED 
UNKNOWN
INVALID_FUNCTION_FORMAT
DATA_TYPE_MISMATCH 
INVALID_CONJUNCTION_OPERANDS
INVALID_NUMBER_OF_OPERANDS
INVALID_OPERAND_TYPE
INVALID_OPERATOR 
INVALID_REQUEST_CONTEXT_TYPE)
%INVALID_FUNCTION_FOR_CALL_PLACEHOLDER	$
 INVALID_FUNCTION_FOR_PLACEHOLDER

INVALID_OPERAND"
MISSING_CONSTANT_OPERAND_VALUE"
INVALID_CONSTANT_OPERAND_VALUE

INVALID_NESTING#
MULTIPLE_FEED_IDS_NOT_SUPPORTED/
+INVALID_FUNCTION_FOR_FEED_WITH_FIXED_SCHEMA
INVALID_ATTRIBUTE_NAMEB�
#com.google.ads.googleads.v17.errorsBFunctionErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
1google/ads/googleads/v17/errors/quota_error.protogoogle.ads.googleads.v17.errors"�
QuotaErrorEnum"}

QuotaError
UNSPECIFIED 
UNKNOWN
RESOURCE_EXHAUSTED
ACCESS_PROHIBITED"
RESOURCE_TEMPORARILY_EXHAUSTEDB�
#com.google.ads.googleads.v17.errorsBQuotaErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
6google/ads/googleads/v17/errors/size_limit_error.protogoogle.ads.googleads.v17.errors"�
SizeLimitErrorEnum"q
SizeLimitError
UNSPECIFIED 
UNKNOWN
REQUEST_SIZE_LIMIT_EXCEEDED 
RESPONSE_SIZE_LIMIT_EXCEEDEDB�
#com.google.ads.googleads.v17.errorsBSizeLimitErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Fgoogle/ads/googleads/v17/errors/conversion_custom_variable_error.protogoogle.ads.googleads.v17.errors"�
!ConversionCustomVariableErrorEnum"v
ConversionCustomVariableError
UNSPECIFIED 
UNKNOWN
DUPLICATE_NAME

DUPLICATE_TAG
RESERVED_TAGB�
#com.google.ads.googleads.v17.errorsB"ConversionCustomVariableErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�	
0google/ads/googleads/v17/errors/feed_error.protogoogle.ads.googleads.v17.errors"�

FeedErrorEnum"�
	FeedError
UNSPECIFIED 
UNKNOWN
ATTRIBUTE_NAMES_NOT_UNIQUE/
+ATTRIBUTES_DO_NOT_MATCH_EXISTING_ATTRIBUTES.
*CANNOT_SPECIFY_USER_ORIGIN_FOR_SYSTEM_FEED4
0CANNOT_SPECIFY_GOOGLE_ORIGIN_FOR_NON_SYSTEM_FEED2
.CANNOT_SPECIFY_FEED_ATTRIBUTES_FOR_SYSTEM_FEED4
0CANNOT_UPDATE_FEED_ATTRIBUTES_WITH_ORIGIN_GOOGLE
FEED_REMOVED
INVALID_ORIGIN_VALUE	
FEED_ORIGIN_IS_NOT_USER
 
INVALID_AUTH_TOKEN_FOR_EMAIL

INVALID_EMAIL
DUPLICATE_FEED_NAME

INVALID_FEED_NAME
MISSING_OAUTH_INFO.
*NEW_ATTRIBUTE_CANNOT_BE_PART_OF_UNIQUE_KEY
TOO_MANY_ATTRIBUTES
INVALID_BUSINESS_ACCOUNT3
/BUSINESS_ACCOUNT_CANNOT_ACCESS_LOCATION_ACCOUNT
INVALID_AFFILIATE_CHAIN_ID
DUPLICATE_SYSTEM_FEED
GMB_ACCESS_ERROR5
1CANNOT_HAVE_LOCATION_AND_AFFILIATE_LOCATION_FEEDS#
LEGACY_EXTENSION_TYPE_READ_ONLYB�
#com.google.ads.googleads.v17.errorsBFeedErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
6google/ads/googleads/v17/errors/field_mask_error.protogoogle.ads.googleads.v17.errors"�
FieldMaskErrorEnum"�
FieldMaskError
UNSPECIFIED 
UNKNOWN
FIELD_MASK_MISSING
FIELD_MASK_NOT_ALLOWED
FIELD_NOT_FOUND
FIELD_HAS_SUBFIELDSB�
#com.google.ads.googleads.v17.errorsBFieldMaskErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
4google/ads/googleads/v17/errors/operator_error.protogoogle.ads.googleads.v17.errors"^
OperatorErrorEnum"I

OperatorError
UNSPECIFIED 
UNKNOWN
OPERATOR_NOT_SUPPORTEDB�
#com.google.ads.googleads.v17.errorsBOperatorErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Agoogle/ads/googleads/v17/errors/customer_manager_link_error.protogoogle.ads.googleads.v17.errors"�
CustomerManagerLinkErrorEnum"�
CustomerManagerLinkError
UNSPECIFIED 
UNKNOWN
NO_PENDING_INVITE\'
#SAME_CLIENT_MORE_THAN_ONCE_PER_CALL-
)MANAGER_HAS_MAX_NUMBER_OF_LINKED_ACCOUNTS-
)CANNOT_UNLINK_ACCOUNT_WITHOUT_ACTIVE_USER+
\'CANNOT_REMOVE_LAST_CLIENT_ACCOUNT_OWNER+
\'CANNOT_CHANGE_ROLE_BY_NON_ACCOUNT_OWNER2
.CANNOT_CHANGE_ROLE_FOR_NON_ACTIVE_LINK_ACCOUNT
DUPLICATE_CHILD_FOUND	.
*TEST_ACCOUNT_LINKS_TOO_MANY_CHILD_ACCOUNTS
B�
#com.google.ads.googleads.v17.errorsBCustomerManagerLinkErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
9google/ads/googleads/v17/errors/ad_customizer_error.protogoogle.ads.googleads.v17.errors"�
AdCustomizerErrorEnum"�
AdCustomizerError
UNSPECIFIED 
UNKNOWN!
COUNTDOWN_INVALID_DATE_FORMAT
COUNTDOWN_DATE_IN_PAST
COUNTDOWN_INVALID_LOCALE\'
#COUNTDOWN_INVALID_START_DAYS_BEFORE
UNKNOWN_USER_LISTB�
#com.google.ads.googleads.v17.errorsBAdCustomizerErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Ggoogle/ads/googleads/v17/errors/policy_validation_parameter_error.protogoogle.ads.googleads.v17.errors"�
"PolicyValidationParameterErrorEnum"�
PolicyValidationParameterError
UNSPECIFIED 
UNKNOWN3
/UNSUPPORTED_AD_TYPE_FOR_IGNORABLE_POLICY_TOPICS8
4UNSUPPORTED_AD_TYPE_FOR_EXEMPT_POLICY_VIOLATION_KEYSL
HCANNOT_SET_BOTH_IGNORABLE_POLICY_TOPICS_AND_EXEMPT_POLICY_VIOLATION_KEYSB�
#com.google.ads.googleads.v17.errorsB#PolicyValidationParameterErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
<google/ads/googleads/v17/errors/payments_account_error.protogoogle.ads.googleads.v17.errors"x
PaymentsAccountErrorEnum"\\
PaymentsAccountError
UNSPECIFIED 
UNKNOWN&
"NOT_SUPPORTED_FOR_MANAGER_CUSTOMERB�
#com.google.ads.googleads.v17.errorsBPaymentsAccountErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
:google/ads/googleads/v17/errors/recommendation_error.protogoogle.ads.googleads.v17.errors"�
RecommendationErrorEnum"�
RecommendationError
UNSPECIFIED 
UNKNOWN
BUDGET_AMOUNT_TOO_SMALL
BUDGET_AMOUNT_TOO_LARGE
INVALID_BUDGET_AMOUNT
POLICY_ERROR
INVALID_BID_AMOUNT
ADGROUP_KEYWORD_LIMIT"
RECOMMENDATION_ALREADY_APPLIED
RECOMMENDATION_INVALIDATED	
TOO_MANY_OPERATIONS


NO_OPERATIONS!
DIFFERENT_TYPES_NOT_SUPPORTED
DUPLICATE_RESOURCE_NAME
$
 RECOMMENDATION_ALREADY_DISMISSED
INVALID_APPLY_REQUEST+
\'RECOMMENDATION_TYPE_APPLY_NOT_SUPPORTED
INVALID_MULTIPLIER3
/ADVERTISING_CHANNEL_TYPE_GENERATE_NOT_SUPPORTED.
*RECOMMENDATION_TYPE_GENERATE_NOT_SUPPORTED(
$RECOMMENDATION_TYPES_CANNOT_BE_EMPTYB�
#com.google.ads.googleads.v17.errorsBRecommendationErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
7google/ads/googleads/v17/errors/region_code_error.protogoogle.ads.googleads.v17.errors"_
RegionCodeErrorEnum"H
RegionCodeError
UNSPECIFIED 
UNKNOWN
INVALID_REGION_CODEB�
#com.google.ads.googleads.v17.errorsBRegionCodeErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
@google/ads/googleads/v17/errors/customer_client_link_error.protogoogle.ads.googleads.v17.errors"�
CustomerClientLinkErrorEnum"�
CustomerClientLinkError
UNSPECIFIED 
UNKNOWN*
&CLIENT_ALREADY_INVITED_BY_THIS_MANAGER\'
#CLIENT_ALREADY_MANAGED_IN_HIERARCHY
CYCLIC_LINK_NOT_ALLOWED"
CUSTOMER_HAS_TOO_MANY_ACCOUNTS#
CLIENT_HAS_TOO_MANY_INVITATIONS*
&CANNOT_HIDE_OR_UNHIDE_MANAGER_ACCOUNTS-
)CUSTOMER_HAS_TOO_MANY_ACCOUNTS_AT_MANAGER 
CLIENT_HAS_TOO_MANY_MANAGERS	B�
#com.google.ads.googleads.v17.errorsBCustomerClientLinkErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
1google/ads/googleads/v17/errors/field_error.protogoogle.ads.googleads.v17.errors"�
FieldErrorEnum"�

FieldError
UNSPECIFIED 
UNKNOWN
REQUIRED
IMMUTABLE_FIELD

INVALID_VALUE
VALUE_MUST_BE_UNSET
REQUIRED_NONEMPTY_LIST
FIELD_CANNOT_BE_CLEARED

BLOCKED_VALUE	
FIELD_CAN_ONLY_BE_CLEARED
B�
#com.google.ads.googleads.v17.errorsBFieldErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�

Hgoogle/ads/googleads/v17/errors/conversion_adjustment_upload_error.protogoogle.ads.googleads.v17.errors"�
#ConversionAdjustmentUploadErrorEnum"�
ConversionAdjustmentUploadError
UNSPECIFIED 
UNKNOWN 
TOO_RECENT_CONVERSION_ACTION 
CONVERSION_ALREADY_RETRACTED
CONVERSION_NOT_FOUND
CONVERSION_EXPIRED"
ADJUSTMENT_PRECEDES_CONVERSION!
MORE_RECENT_RESTATEMENT_FOUND
TOO_RECENT_CONVERSION	N
JCANNOT_RESTATE_CONVERSION_ACTION_THAT_ALWAYS_USES_DEFAULT_CONVERSION_VALUE
#
TOO_MANY_ADJUSTMENTS_IN_REQUEST
TOO_MANY_ADJUSTMENTS
RESTATEMENT_ALREADY_EXISTS
#
DUPLICATE_ADJUSTMENT_IN_REQUEST-
)CUSTOMER_NOT_ACCEPTED_CUSTOMER_DATA_TERMS2
.CONVERSION_ACTION_NOT_ELIGIBLE_FOR_ENHANCEMENT
INVALID_USER_IDENTIFIER
UNSUPPORTED_USER_IDENTIFIER.
*GCLID_DATE_TIME_PAIR_AND_ORDER_ID_BOTH_SET
CONVERSION_ALREADY_ENHANCED$
 DUPLICATE_ENHANCEMENT_IN_REQUEST.
*CUSTOMER_DATA_POLICY_PROHIBITS_ENHANCEMENT 
MISSING_ORDER_ID_FOR_WEBPAGE
ORDER_ID_CONTAINS_PII
INVALID_JOB_ID
NO_CONVERSION_ACTION_FOUND"
INVALID_CONVERSION_ACTION_TYPEB�
#com.google.ads.googleads.v17.errorsB$ConversionAdjustmentUploadErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
8google/ads/googleads/v17/errors/change_event_error.protogoogle.ads.googleads.v17.errors"�
ChangeEventErrorEnum"�
ChangeEventError
UNSPECIFIED 
UNKNOWN
START_DATE_TOO_OLD
CHANGE_DATE_RANGE_INFINITE
CHANGE_DATE_RANGE_NEGATIVE
LIMIT_NOT_SPECIFIED
INVALID_LIMIT_CLAUSEB�
#com.google.ads.googleads.v17.errorsBChangeEventErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
9google/ads/googleads/v17/errors/currency_code_error.protogoogle.ads.googleads.v17.errors"[
CurrencyCodeErrorEnum"B
CurrencyCodeError
UNSPECIFIED 
UNKNOWN
UNSUPPORTEDB�
#com.google.ads.googleads.v17.errorsBCurrencyCodeErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
5google/ads/googleads/v17/errors/feed_item_error.protogoogle.ads.googleads.v17.errors"�
FeedItemErrorEnum"�

FeedItemError
UNSPECIFIED 
UNKNOWN.
*CANNOT_CONVERT_ATTRIBUTE_VALUE_FROM_STRING\'
#CANNOT_OPERATE_ON_REMOVED_FEED_ITEM*
&DATE_TIME_MUST_BE_IN_ACCOUNT_TIME_ZONE
KEY_ATTRIBUTES_NOT_FOUND
INVALID_URL
MISSING_KEY_ATTRIBUTES
KEY_ATTRIBUTES_NOT_UNIQUE%
!CANNOT_MODIFY_KEY_ATTRIBUTE_VALUE	,
(SIZE_TOO_LARGE_FOR_MULTI_VALUE_ATTRIBUTE

LEGACY_FEED_TYPE_READ_ONLYB�
#com.google.ads.googleads.v17.errorsBFeedItemErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Dgoogle/ads/googleads/v17/errors/feed_attribute_reference_error.protogoogle.ads.googleads.v17.errors"�
FeedAttributeReferenceErrorEnum"�
FeedAttributeReferenceError
UNSPECIFIED 
UNKNOWN!
CANNOT_REFERENCE_REMOVED_FEED
INVALID_FEED_NAME
INVALID_FEED_ATTRIBUTE_NAMEB�
#com.google.ads.googleads.v17.errorsB FeedAttributeReferenceErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Igoogle/ads/googleads/v17/errors/ad_group_criterion_customizer_error.protogoogle.ads.googleads.v17.errors"�
#AdGroupCriterionCustomizerErrorEnum"]
AdGroupCriterionCustomizerError
UNSPECIFIED 
UNKNOWN
CRITERION_IS_NOT_KEYWORDB�
#com.google.ads.googleads.v17.errorsB$AdGroupCriterionCustomizerErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
?google/ads/googleads/v17/errors/extension_feed_item_error.protogoogle.ads.googleads.v17.errors"�
ExtensionFeedItemErrorEnum"�
ExtensionFeedItemError
UNSPECIFIED 
UNKNOWN
VALUE_OUT_OF_RANGE
URL_LIST_TOO_LONG2
.CANNOT_HAVE_RESTRICTION_ON_EMPTY_GEO_TARGETING
CANNOT_SET_WITH_FINAL_URLS!
CANNOT_SET_WITHOUT_FINAL_URLS
INVALID_PHONE_NUMBER*
&PHONE_NUMBER_NOT_SUPPORTED_FOR_COUNTRY-
)CARRIER_SPECIFIC_SHORT_NUMBER_NOT_ALLOWED	#
PREMIUM_RATE_NUMBER_NOT_ALLOWED

DISALLOWED_NUMBER_TYPE(
$INVALID_DOMESTIC_PHONE_NUMBER_FORMAT#
VANITY_PHONE_NUMBER_NOT_ALLOWED
"
INVALID_CALL_CONVERSION_ACTION.
*CUSTOMER_NOT_ON_ALLOWLIST_FOR_CALLTRACKING/*
&CALLTRACKING_NOT_SUPPORTED_FOR_COUNTRY0
,CUSTOMER_CONSENT_FOR_CALL_RECORDING_REQUIRED
INVALID_APP_ID&
"QUOTES_IN_REVIEW_EXTENSION_SNIPPET\'
#HYPHENS_IN_REVIEW_EXTENSION_SNIPPET&
"REVIEW_EXTENSION_SOURCE_INELIGIBLE(
$SOURCE_NAME_IN_REVIEW_EXTENSION_TEXT
INCONSISTENT_CURRENCY_CODES*
&PRICE_EXTENSION_HAS_DUPLICATED_HEADERS4
0PRICE_ITEM_HAS_DUPLICATED_HEADER_AND_DESCRIPTION%
!PRICE_EXTENSION_HAS_TOO_FEW_ITEMS&
"PRICE_EXTENSION_HAS_TOO_MANY_ITEMS
UNSUPPORTED_VALUE*
&UNSUPPORTED_VALUE_IN_SELECTED_LANGUAGE
INVALID_DEVICE_PREFERENCE
INVALID_SCHEDULE_END*
&DATE_TIME_MUST_BE_IN_ACCOUNT_TIME_ZONE 
INVALID_SNIPPETS_HEADER!\'
#CANNOT_OPERATE_ON_REMOVED_FEED_ITEM"<
8PHONE_NUMBER_NOT_SUPPORTED_WITH_CALLTRACKING_FOR_COUNTRY#(
$CONFLICTING_CALL_CONVERSION_SETTINGS$
EXTENSION_TYPE_MISMATCH%
EXTENSION_SUBTYPE_REQUIRED&
EXTENSION_TYPE_UNSUPPORTED\'1
-CANNOT_OPERATE_ON_FEED_WITH_MULTIPLE_MAPPINGS(.
*CANNOT_OPERATE_ON_FEED_WITH_KEY_ATTRIBUTES)
INVALID_PRICE_FORMAT*
PROMOTION_INVALID_TIME+%
!TOO_MANY_DECIMAL_PLACES_SPECIFIED,$
 CONCRETE_EXTENSION_TYPE_REQUIRED- 
SCHEDULE_END_NOT_AFTER_START.B�
#com.google.ads.googleads.v17.errorsBExtensionFeedItemErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Cgoogle/ads/googleads/v17/errors/operation_access_denied_error.protogoogle.ads.googleads.v17.errors"�
OperationAccessDeniedErrorEnum"�
OperationAccessDeniedError
UNSPECIFIED 
UNKNOWN
ACTION_NOT_PERMITTED"
CREATE_OPERATION_NOT_PERMITTED"
REMOVE_OPERATION_NOT_PERMITTED"
UPDATE_OPERATION_NOT_PERMITTED*
&MUTATE_ACTION_NOT_PERMITTED_FOR_CLIENT-
)OPERATION_NOT_PERMITTED_FOR_CAMPAIGN_TYPE#
CREATE_AS_REMOVED_NOT_PERMITTED0
,OPERATION_NOT_PERMITTED_FOR_REMOVED_RESOURCE	-
)OPERATION_NOT_PERMITTED_FOR_AD_GROUP_TYPE
%
!MUTATE_NOT_PERMITTED_FOR_CUSTOMERB�
#com.google.ads.googleads.v17.errorsBOperationAccessDeniedErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
=google/ads/googleads/v17/errors/keyword_plan_idea_error.protogoogle.ads.googleads.v17.errors"x
KeywordPlanIdeaErrorEnum"\\
KeywordPlanIdeaError
UNSPECIFIED 
UNKNOWN
URL_CRAWL_ERROR

INVALID_VALUEB�
#com.google.ads.googleads.v17.errorsBKeywordPlanIdeaErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
;google/ads/googleads/v17/errors/custom_interest_error.protogoogle.ads.googleads.v17.errors"�
CustomInterestErrorEnum"�
CustomInterestError
UNSPECIFIED 
UNKNOWN
NAME_ALREADY_USEDF
BCUSTOM_INTEREST_MEMBER_ID_AND_TYPE_PARAMETER_NOT_PRESENT_IN_REMOVE 
TYPE_AND_PARAMETER_NOT_FOUND&
"TYPE_AND_PARAMETER_ALREADY_EXISTED\'
#INVALID_CUSTOM_INTEREST_MEMBER_TYPE
CANNOT_REMOVE_WHILE_IN_USE
CANNOT_CHANGE_TYPEB�
#com.google.ads.googleads.v17.errorsBCustomInterestErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�	
3google/ads/googleads/v17/errors/request_error.protogoogle.ads.googleads.v17.errors"�
RequestErrorEnum"�
RequestError
UNSPECIFIED 
UNKNOWN
RESOURCE_NAME_MISSING
RESOURCE_NAME_MALFORMED
BAD_RESOURCE_ID
INVALID_CUSTOMER_ID
OPERATION_REQUIRED
RESOURCE_NOT_FOUND
INVALID_PAGE_TOKEN
EXPIRED_PAGE_TOKEN
INVALID_PAGE_SIZE
PAGE_SIZE_NOT_SUPPORTED(
REQUIRED_FIELD_MISSING	
IMMUTABLE_FIELD
TOO_MANY_MUTATE_OPERATIONS
)
%CANNOT_BE_EXECUTED_BY_MANAGER_ACCOUNT
CANNOT_MODIFY_FOREIGN_FIELD
INVALID_ENUM_VALUE%
!DEVELOPER_TOKEN_PARAMETER_MISSING\'
#LOGIN_CUSTOMER_ID_PARAMETER_MISSING(
$VALIDATE_ONLY_REQUEST_HAS_PAGE_TOKEN9
5CANNOT_RETURN_SUMMARY_ROW_FOR_REQUEST_WITHOUT_METRICS8
4CANNOT_RETURN_SUMMARY_ROW_FOR_VALIDATE_ONLY_REQUESTS)
%INCONSISTENT_RETURN_SUMMARY_ROW_VALUE0
,TOTAL_RESULTS_COUNT_NOT_ORIGINALLY_REQUESTED 
RPC_DEADLINE_TOO_SHORT!
UNSUPPORTED_VERSION&
CLOUD_PROJECT_NOT_FOUND\'B�
#com.google.ads.googleads.v17.errorsBRequestErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
=google/ads/googleads/v17/errors/audience_insights_error.protogoogle.ads.googleads.v17.errors"�
AudienceInsightsErrorEnum"r
AudienceInsightsError
UNSPECIFIED 
UNKNOWN;
7DIMENSION_INCOMPATIBLE_WITH_TOPIC_AUDIENCE_COMBINATIONSB�
#com.google.ads.googleads.v17.errorsBAudienceInsightsErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
5google/ads/googleads/v17/errors/not_empty_error.protogoogle.ads.googleads.v17.errors"R
NotEmptyErrorEnum"=

NotEmptyError
UNSPECIFIED 
UNKNOWN

EMPTY_LISTB�
#com.google.ads.googleads.v17.errorsBNotEmptyErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
agoogle/ads/googleads/v17/enums/policy_topic_evidence_destination_not_working_dns_error_type.protogoogle.ads.googleads.v17.enums"�
8PolicyTopicEvidenceDestinationNotWorkingDnsErrorTypeEnum"�
4PolicyTopicEvidenceDestinationNotWorkingDnsErrorType
UNSPECIFIED 
UNKNOWN
HOSTNAME_NOT_FOUND
GOOGLE_CRAWLER_DNS_ISSUEB�
"com.google.ads.googleads.v17.enumsB9PolicyTopicEvidenceDestinationNotWorkingDnsErrorTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
:google/ads/googleads/v17/errors/asset_set_link_error.protogoogle.ads.googleads.v17.errors"�
AssetSetLinkErrorEnum"�
AssetSetLinkError
UNSPECIFIED 
UNKNOWN)
%INCOMPATIBLE_ADVERTISING_CHANNEL_TYPE
DUPLICATE_FEED_LINK2
.INCOMPATIBLE_ASSET_SET_TYPE_WITH_CAMPAIGN_TYPE
DUPLICATE_ASSET_SET_LINK$
 ASSET_SET_LINK_CANNOT_BE_REMOVEDB�
#com.google.ads.googleads.v17.errorsBAssetSetLinkErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
4google/ads/googleads/v17/errors/campaign_error.protogoogle.ads.googleads.v17.errors"�
CampaignErrorEnum"�

CampaignError
UNSPECIFIED 
UNKNOWN!
CANNOT_TARGET_CONTENT_NETWORK 
CANNOT_TARGET_SEARCH_NETWORK6
2CANNOT_TARGET_SEARCH_NETWORK_WITHOUT_GOOGLE_SEARCH0
,CANNOT_TARGET_GOOGLE_SEARCH_FOR_CPM_CAMPAIGN-
)CAMPAIGN_MUST_TARGET_AT_LEAST_ONE_NETWORK(
$CANNOT_TARGET_PARTNER_SEARCH_NETWORKK
GCANNOT_TARGET_CONTENT_NETWORK_ONLY_WITH_CRITERIA_LEVEL_BIDDING_STRATEGY	6
2CAMPAIGN_DURATION_MUST_CONTAIN_ALL_RUNNABLE_TRIALS
$
 CANNOT_MODIFY_FOR_TRIAL_CAMPAIGN
DUPLICATE_CAMPAIGN_NAME
INCOMPATIBLE_CAMPAIGN_FIELD

INVALID_CAMPAIGN_NAME*
&INVALID_AD_SERVING_OPTIMIZATION_STATUS
INVALID_TRACKING_URL>
:CANNOT_SET_BOTH_TRACKING_URL_TEMPLATE_AND_TRACKING_SETTING 
MAX_IMPRESSIONS_NOT_IN_RANGE
TIME_UNIT_NOT_SUPPORTED1
-INVALID_OPERATION_IF_SERVING_STATUS_HAS_ENDED
BUDGET_CANNOT_BE_SHARED%
!CAMPAIGN_CANNOT_USE_SHARED_BUDGET0
,CANNOT_CHANGE_BUDGET_ON_CAMPAIGN_WITH_TRIALS!
CAMPAIGN_LABEL_DOES_NOT_EXIST!
CAMPAIGN_LABEL_ALREADY_EXISTS
MISSING_SHOPPING_SETTING"
INVALID_SHOPPING_SALES_COUNTRY;
7ADVERTISING_CHANNEL_TYPE_NOT_AVAILABLE_FOR_ACCOUNT_TYPE(
$INVALID_ADVERTISING_CHANNEL_SUB_TYPE ,
(AT_LEAST_ONE_CONVERSION_MUST_BE_SELECTED!
CANNOT_SET_AD_ROTATION_MODE"/
+CANNOT_MODIFY_START_DATE_IF_ALREADY_STARTED#
CANNOT_SET_DATE_TO_PAST$
MISSING_HOTEL_CUSTOMER_LINK%
INVALID_HOTEL_CUSTOMER_LINK&
MISSING_HOTEL_SETTING\'B
>CANNOT_USE_SHARED_CAMPAIGN_BUDGET_WHILE_PART_OF_CAMPAIGN_GROUP(

APP_NOT_FOUND)9
5SHOPPING_ENABLE_LOCAL_NOT_SUPPORTED_FOR_CAMPAIGN_TYPE*3
/MERCHANT_NOT_ALLOWED_FOR_COMPARISON_LISTING_ADS+#
INSUFFICIENT_APP_INSTALLS_COUNT,
SENSITIVE_CATEGORY_APP-
HEC_AGREEMENT_REQUIRED.<
8NOT_COMPATIBLE_WITH_VIEW_THROUGH_CONVERSION_OPTIMIZATION1,
(INVALID_EXCLUDED_PARENT_ASSET_FIELD_TYPE0:
6CANNOT_CREATE_APP_PRE_REGISTRATION_FOR_NON_ANDROID_APP2=
9APP_NOT_AVAILABLE_TO_CREATE_APP_PRE_REGISTRATION_CAMPAIGN3
INCOMPATIBLE_BUDGET_TYPE4)
%LOCAL_SERVICES_DUPLICATE_CATEGORY_BID5\'
#LOCAL_SERVICES_INVALID_CATEGORY_BID6\'
#LOCAL_SERVICES_MISSING_CATEGORY_BID7
INVALID_STATUS_CHANGE9 
MISSING_TRAVEL_CUSTOMER_LINK: 
INVALID_TRAVEL_CUSTOMER_LINK;*
&INVALID_EXCLUDED_PARENT_ASSET_SET_TYPE>,
(ASSET_SET_NOT_A_HOTEL_PROPERTY_ASSET_SET?F
BHOTEL_PROPERTY_ASSET_SET_ONLY_FOR_PERFORMANCE_MAX_FOR_TRAVEL_GOALS@ 
AVERAGE_DAILY_SPEND_TOO_HIGHA+
\'CANNOT_ATTACH_TO_REMOVED_CAMPAIGN_GROUPB%
!CANNOT_ATTACH_TO_BIDDING_STRATEGYC
CANNOT_CHANGE_BUDGET_PERIODD
NOT_ENOUGH_CONVERSIONSG.
*CANNOT_SET_MORE_THAN_ONE_CONVERSION_ACTIONH#
NOT_COMPATIBLE_WITH_BUDGET_TYPEI0
,NOT_COMPATIBLE_WITH_UPLOAD_CLICKS_CONVERSIONJ.
*APP_ID_MUST_MATCH_CONVERSION_ACTION_APP_IDL8
4CONVERSION_ACTION_WITH_DOWNLOAD_CATEGORY_NOT_ALLOWEDM5
1CONVERSION_ACTION_WITH_DOWNLOAD_CATEGORY_REQUIREDN#
CONVERSION_TRACKING_NOT_ENABLEDO-
)NOT_COMPATIBLE_WITH_BIDDING_STRATEGY_TYPEP6
2NOT_COMPATIBLE_WITH_GOOGLE_ATTRIBUTION_CONVERSIONSQ
CONVERSION_LAG_TOO_HIGHR"
NOT_LINKED_ADVERTISING_PARTNERS-
)INVALID_NUMBER_OF_ADVERTISING_PARTNER_IDST1
-CANNOT_TARGET_DISPLAY_NETWORK_WITHOUT_YOUTUBEU6
2CANNOT_LINK_TO_COMPARISON_SHOPPING_SERVICE_ACCOUNTVI
ECANNOT_TARGET_NETWORK_FOR_COMPARISON_SHOPPING_SERVICE_LINKED_ACCOUNTSW:
6CANNOT_MODIFY_TEXT_ASSET_AUTOMATION_WITH_ENABLED_TRIALXE
ADYNAMIC_TEXT_ASSET_CANNOT_OPT_OUT_WITH_FINAL_URL_EXPANSION_OPT_INY*
&CANNOT_SET_CAMPAIGN_KEYWORD_MATCH_TYPEZA
=CANNOT_DISABLE_BROAD_MATCH_WHEN_KEYWORD_CONVERSION_IN_PROCESS[4
0CANNOT_DISABLE_BROAD_MATCH_WHEN_TARGETING_BRANDS\\D
@CANNOT_ENABLE_BROAD_MATCH_FOR_BASE_CAMPAIGN_WITH_PROMOTING_TRIAL]:
6CANNOT_ENABLE_BROAD_MATCH_FOR_PROMOTING_TRIAL_CAMPAIGN^B�
#com.google.ads.googleads.v17.errorsBCampaignErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
6google/ads/googleads/v17/errors/shared_set_error.protogoogle.ads.googleads.v17.errors"�
SharedSetErrorEnum"�
SharedSetError
UNSPECIFIED 
UNKNOWN2
.CUSTOMER_CANNOT_CREATE_SHARED_SET_OF_THIS_TYPE
DUPLICATE_NAME
SHARED_SET_REMOVED
SHARED_SET_IN_USEB�
#com.google.ads.googleads.v17.errorsBSharedSetErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
;google/ads/googleads/v17/errors/merchant_center_error.protogoogle.ads.googleads.v17.errors"�
MerchantCenterErrorEnum"�
MerchantCenterError
UNSPECIFIED 
UNKNOWN"
MERCHANT_ID_CANNOT_BE_ACCESSED5
1CUSTOMER_NOT_ALLOWED_FOR_SHOPPING_PERFORMANCE_MAXB�
#com.google.ads.googleads.v17.errorsBMerchantCenterErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�	
8google/ads/googleads/v17/errors/feed_mapping_error.protogoogle.ads.googleads.v17.errors"�
FeedMappingErrorEnum"�
FeedMappingError
UNSPECIFIED 
UNKNOWN
INVALID_PLACEHOLDER_FIELD
INVALID_CRITERION_FIELD
INVALID_PLACEHOLDER_TYPE
INVALID_CRITERION_TYPE
NO_ATTRIBUTE_FIELD_MAPPINGS 
FEED_ATTRIBUTE_TYPE_MISMATCH8
4CANNOT_OPERATE_ON_MAPPINGS_FOR_SYSTEM_GENERATED_FEED	*
&MULTIPLE_MAPPINGS_FOR_PLACEHOLDER_TYPE
(
$MULTIPLE_MAPPINGS_FOR_CRITERION_TYPE+
\'MULTIPLE_MAPPINGS_FOR_PLACEHOLDER_FIELD)
%MULTIPLE_MAPPINGS_FOR_CRITERION_FIELD
\'
#UNEXPECTED_ATTRIBUTE_FIELD_MAPPINGS.
*LOCATION_PLACEHOLDER_ONLY_FOR_PLACES_FEEDS)
%CANNOT_MODIFY_MAPPINGS_FOR_TYPED_FEED:
6INVALID_PLACEHOLDER_TYPE_FOR_NON_SYSTEM_GENERATED_FEED;
7INVALID_PLACEHOLDER_TYPE_FOR_SYSTEM_GENERATED_FEED_TYPE)
%ATTRIBUTE_FIELD_MAPPING_MISSING_FIELD
LEGACY_FEED_TYPE_READ_ONLYB�
#com.google.ads.googleads.v17.errorsBFeedMappingErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Agoogle/ads/googleads/v17/errors/offline_user_data_job_error.protogoogle.ads.googleads.v17.errors"�

OfflineUserDataJobErrorEnum"�

OfflineUserDataJobError
UNSPECIFIED 
UNKNOWN
INVALID_USER_LIST_ID
INVALID_USER_LIST_TYPE 
NOT_ON_ALLOWLIST_FOR_USER_ID! 
INCOMPATIBLE_UPLOAD_KEY_TYPE
MISSING_USER_IDENTIFIER
INVALID_MOBILE_ID_FORMAT
TOO_MANY_USER_IDENTIFIERS	+
\'NOT_ON_ALLOWLIST_FOR_STORE_SALES_DIRECT,
(NOT_ON_ALLOWLIST_FOR_UNIFIED_STORE_SALES 
INVALID_PARTNER_ID
INVALID_ENCODING
INVALID_COUNTRY_CODE
 
INCOMPATIBLE_USER_IDENTIFIER
FUTURE_TRANSACTION_TIME
INVALID_CONVERSION_ACTION
MOBILE_ID_NOT_SUPPORTED
INVALID_OPERATION_ORDER
CONFLICTING_OPERATION%
!EXTERNAL_UPDATE_ID_ALREADY_EXISTS
JOB_ALREADY_STARTED
REMOVE_NOT_SUPPORTED
REMOVE_ALL_NOT_SUPPORTED
INVALID_SHA256_FORMAT
CUSTOM_KEY_DISABLED
CUSTOM_KEY_NOT_PREDEFINED
CUSTOM_KEY_NOT_SET-
)CUSTOMER_NOT_ACCEPTED_CUSTOMER_DATA_TERMS:
6ATTRIBUTES_NOT_APPLICABLE_FOR_CUSTOMER_MATCH_USER_LIST"&
"LIFETIME_VALUE_BUCKET_NOT_IN_RANGE#/
+INCOMPATIBLE_USER_IDENTIFIER_FOR_ATTRIBUTES$
FUTURE_TIME_NOT_ALLOWED%1
-LAST_PURCHASE_TIME_LESS_THAN_ACQUISITION_TIME&#
CUSTOMER_IDENTIFIER_NOT_ALLOWED\'
INVALID_ITEM_ID(7
3FIRST_PURCHASE_TIME_GREATER_THAN_LAST_PURCHASE_TIME*
INVALID_LIFECYCLE_STAGE+
INVALID_EVENT_VALUE,+
\'EVENT_ATTRIBUTE_ALL_FIELDS_ARE_REQUIRED-$
 OPERATION_LEVEL_CONSENT_PROVIDED0B�
#com.google.ads.googleads.v17.errorsBOfflineUserDataJobErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Bgoogle/ads/googleads/v17/errors/resource_access_denied_error.protogoogle.ads.googleads.v17.errors"s
ResourceAccessDeniedErrorEnum"R
ResourceAccessDeniedError
UNSPECIFIED 
UNKNOWN
WRITE_ACCESS_DENIEDB�
#com.google.ads.googleads.v17.errorsBResourceAccessDeniedErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�0
8google/ads/googleads/v17/enums/resource_limit_type.protogoogle.ads.googleads.v17.enums"�.
ResourceLimitTypeEnum"�-
ResourceLimitType
UNSPECIFIED 
UNKNOWN
CAMPAIGNS_PER_CUSTOMER
BASE_CAMPAIGNS_PER_CUSTOMER%
!EXPERIMENT_CAMPAIGNS_PER_CUSTOMERi 
HOTEL_CAMPAIGNS_PER_CUSTOMER)
%SMART_SHOPPING_CAMPAIGNS_PER_CUSTOMER
AD_GROUPS_PER_CAMPAIGN#
AD_GROUPS_PER_SHOPPING_CAMPAIGN 
AD_GROUPS_PER_HOTEL_CAMPAIGN	*
&REPORTING_AD_GROUPS_PER_LOCAL_CAMPAIGN
(
$REPORTING_AD_GROUPS_PER_APP_CAMPAIGN(
$MANAGED_AD_GROUPS_PER_SMART_CAMPAIGN4"
AD_GROUP_CRITERIA_PER_CUSTOMER\'
#BASE_AD_GROUP_CRITERIA_PER_CUSTOMER
-
)EXPERIMENT_AD_GROUP_CRITERIA_PER_CUSTOMERk"
AD_GROUP_CRITERIA_PER_CAMPAIGN"
CAMPAIGN_CRITERIA_PER_CUSTOMER\'
#BASE_CAMPAIGN_CRITERIA_PER_CUSTOMER-
)EXPERIMENT_CAMPAIGN_CRITERIA_PER_CUSTOMERl!
WEBPAGE_CRITERIA_PER_CUSTOMER&
"BASE_WEBPAGE_CRITERIA_PER_CUSTOMER,
(EXPERIMENT_WEBPAGE_CRITERIA_PER_CUSTOMER+
\'COMBINED_AUDIENCE_CRITERIA_PER_AD_GROUP5
1CUSTOMER_NEGATIVE_PLACEMENT_CRITERIA_PER_CUSTOMER;
7CUSTOMER_NEGATIVE_YOUTUBE_CHANNEL_CRITERIA_PER_CUSTOMER
CRITERIA_PER_AD_GROUP
LISTING_GROUPS_PER_AD_GROUP*
&EXPLICITLY_SHARED_BUDGETS_PER_CUSTOMER*
&IMPLICITLY_SHARED_BUDGETS_PER_CUSTOMER+
\'COMBINED_AUDIENCE_CRITERIA_PER_CAMPAIGN"
NEGATIVE_KEYWORDS_PER_CAMPAIGN$
 NEGATIVE_PLACEMENTS_PER_CAMPAIGN
GEO_TARGETS_PER_CAMPAIGN#
NEGATIVE_IP_BLOCKS_PER_CAMPAIGN 
PROXIMITIES_PER_CAMPAIGN!(
$LISTING_SCOPES_PER_SHOPPING_CAMPAIGN",
(LISTING_SCOPES_PER_NON_SHOPPING_CAMPAIGN#$
 NEGATIVE_KEYWORDS_PER_SHARED_SET$&
"NEGATIVE_PLACEMENTS_PER_SHARED_SET%-
)SHARED_SETS_PER_CUSTOMER_FOR_TYPE_DEFAULT(>
:SHARED_SETS_PER_CUSTOMER_FOR_NEGATIVE_PLACEMENT_LIST_LOWER);
7HOTEL_ADVANCE_BOOKING_WINDOW_BID_MODIFIERS_PER_AD_GROUP,#
BIDDING_STRATEGIES_PER_CUSTOMER-!
BASIC_USER_LISTS_PER_CUSTOMER/#
LOGICAL_USER_LISTS_PER_CUSTOMER0\'
"RULE_BASED_USER_LISTS_PER_CUSTOMER�"
BASE_AD_GROUP_ADS_PER_CUSTOMER5(
$EXPERIMENT_AD_GROUP_ADS_PER_CUSTOMER6
AD_GROUP_ADS_PER_CAMPAIGN7#
TEXT_AND_OTHER_ADS_PER_AD_GROUP8
IMAGE_ADS_PER_AD_GROUP9#
SHOPPING_SMART_ADS_PER_AD_GROUP:&
"RESPONSIVE_SEARCH_ADS_PER_AD_GROUP;
APP_ADS_PER_AD_GROUP<#
APP_ENGAGEMENT_ADS_PER_AD_GROUP=
LOCAL_ADS_PER_AD_GROUP>
VIDEO_ADS_PER_AD_GROUP?+
&LEAD_FORM_CAMPAIGN_ASSETS_PER_CAMPAIGN�*
&PROMOTION_CUSTOMER_ASSETS_PER_CUSTOMERO*
&PROMOTION_CAMPAIGN_ASSETS_PER_CAMPAIGNP*
&PROMOTION_AD_GROUP_ASSETS_PER_AD_GROUPQ)
$CALLOUT_CUSTOMER_ASSETS_PER_CUSTOMER�)
$CALLOUT_CAMPAIGN_ASSETS_PER_CAMPAIGN�)
$CALLOUT_AD_GROUP_ASSETS_PER_AD_GROUP�*
%SITELINK_CUSTOMER_ASSETS_PER_CUSTOMER�*
%SITELINK_CAMPAIGN_ASSETS_PER_CAMPAIGN�*
%SITELINK_AD_GROUP_ASSETS_PER_AD_GROUP�4
/STRUCTURED_SNIPPET_CUSTOMER_ASSETS_PER_CUSTOMER�4
/STRUCTURED_SNIPPET_CAMPAIGN_ASSETS_PER_CAMPAIGN�4
/STRUCTURED_SNIPPET_AD_GROUP_ASSETS_PER_AD_GROUP�,
\'MOBILE_APP_CUSTOMER_ASSETS_PER_CUSTOMER�,
\'MOBILE_APP_CAMPAIGN_ASSETS_PER_CAMPAIGN�,
\'MOBILE_APP_AD_GROUP_ASSETS_PER_AD_GROUP�/
*HOTEL_CALLOUT_CUSTOMER_ASSETS_PER_CUSTOMER�/
*HOTEL_CALLOUT_CAMPAIGN_ASSETS_PER_CAMPAIGN�/
*HOTEL_CALLOUT_AD_GROUP_ASSETS_PER_AD_GROUP�&
!CALL_CUSTOMER_ASSETS_PER_CUSTOMER�&
!CALL_CAMPAIGN_ASSETS_PER_CAMPAIGN�&
!CALL_AD_GROUP_ASSETS_PER_AD_GROUP�\'
"PRICE_CUSTOMER_ASSETS_PER_CUSTOMER�\'
"PRICE_CAMPAIGN_ASSETS_PER_CAMPAIGN�\'
"PRICE_AD_GROUP_ASSETS_PER_AD_GROUP�*
%AD_IMAGE_CAMPAIGN_ASSETS_PER_CAMPAIGN�*
%AD_IMAGE_AD_GROUP_ASSETS_PER_AD_GROUP�&
!PAGE_FEED_ASSET_SETS_PER_CUSTOMER�3
.DYNAMIC_EDUCATION_FEED_ASSET_SETS_PER_CUSTOMER�#
ASSETS_PER_PAGE_FEED_ASSET_SET�0
+ASSETS_PER_DYNAMIC_EDUCATION_FEED_ASSET_SET�0
+DYNAMIC_REAL_ESTATE_ASSET_SETS_PER_CUSTOMER�-
(ASSETS_PER_DYNAMIC_REAL_ESTATE_ASSET_SET�+
&DYNAMIC_CUSTOM_ASSET_SETS_PER_CUSTOMER�(
#ASSETS_PER_DYNAMIC_CUSTOM_ASSET_SET�7
2DYNAMIC_HOTELS_AND_RENTALS_ASSET_SETS_PER_CUSTOMER�4
/ASSETS_PER_DYNAMIC_HOTELS_AND_RENTALS_ASSET_SET�*
%DYNAMIC_LOCAL_ASSET_SETS_PER_CUSTOMER�\'
"ASSETS_PER_DYNAMIC_LOCAL_ASSET_SET�,
\'DYNAMIC_FLIGHTS_ASSET_SETS_PER_CUSTOMER�)
$ASSETS_PER_DYNAMIC_FLIGHTS_ASSET_SET�+
&DYNAMIC_TRAVEL_ASSET_SETS_PER_CUSTOMER�(
#ASSETS_PER_DYNAMIC_TRAVEL_ASSET_SET�)
$DYNAMIC_JOBS_ASSET_SETS_PER_CUSTOMER�&
!ASSETS_PER_DYNAMIC_JOBS_ASSET_SET�/
*BUSINESS_NAME_CAMPAIGN_ASSETS_PER_CAMPAIGN�/
*BUSINESS_LOGO_CAMPAIGN_ASSETS_PER_CAMPAIGN�
VERSIONS_PER_ADR
USER_FEEDS_PER_CUSTOMERZ
SYSTEM_FEEDS_PER_CUSTOMER[
FEED_ATTRIBUTES_PER_FEED\\
FEED_ITEMS_PER_CUSTOMER^
CAMPAIGN_FEEDS_PER_CUSTOMER_$
 BASE_CAMPAIGN_FEEDS_PER_CUSTOMER`*
&EXPERIMENT_CAMPAIGN_FEEDS_PER_CUSTOMERm
AD_GROUP_FEEDS_PER_CUSTOMERa$
 BASE_AD_GROUP_FEEDS_PER_CUSTOMERb*
&EXPERIMENT_AD_GROUP_FEEDS_PER_CUSTOMERn
AD_GROUP_FEEDS_PER_CAMPAIGNc
FEED_ITEM_SETS_PER_CUSTOMERd 
FEED_ITEMS_PER_FEED_ITEM_SETe%
!CAMPAIGN_EXPERIMENTS_PER_CUSTOMERp(
$EXPERIMENT_ARMS_PER_VIDEO_EXPERIMENTq
OWNED_LABELS_PER_CUSTOMERs
LABELS_PER_CAMPAIGNu
LABELS_PER_AD_GROUPv
LABELS_PER_AD_GROUP_ADw!
LABELS_PER_AD_GROUP_CRITERIONx
TARGET_CUSTOMERS_PER_LABELy\'
#KEYWORD_PLANS_PER_USER_PER_CUSTOMERz3
/KEYWORD_PLAN_AD_GROUP_KEYWORDS_PER_KEYWORD_PLAN{+
\'KEYWORD_PLAN_AD_GROUPS_PER_KEYWORD_PLAN|3
/KEYWORD_PLAN_NEGATIVE_KEYWORDS_PER_KEYWORD_PLAN}+
\'KEYWORD_PLAN_CAMPAIGNS_PER_KEYWORD_PLAN~$
CONVERSION_ACTIONS_PER_CUSTOMER�!
BATCH_JOB_OPERATIONS_PER_JOB�
BATCH_JOBS_PER_CUSTOMER�9
4HOTEL_CHECK_IN_DATE_RANGE_BID_MODIFIERS_PER_AD_GROUP�@
;SHARED_SETS_PER_ACCOUNT_FOR_ACCOUNT_LEVEL_NEGATIVE_KEYWORDS�3
.ACCOUNT_LEVEL_NEGATIVE_KEYWORDS_PER_SHARED_SET�/
*ENABLED_ASSET_PER_HOTEL_PROPERTY_ASSET_SET�7
2ENABLED_HOTEL_PROPERTY_ASSET_LINKS_PER_ASSET_GROUP�
BRANDS_PER_SHARED_SET�-
(ENABLED_BRAND_LIST_CRITERIA_PER_CAMPAIGN�&
!SHARED_SETS_PER_ACCOUNT_FOR_BRAND�&
!LOOKALIKE_USER_LISTS_PER_CUSTOMER�B�
"com.google.ads.googleads.v17.enumsBResourceLimitTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
9google/ads/googleads/v17/errors/string_length_error.protogoogle.ads.googleads.v17.errors"r
StringLengthErrorEnum"Y
StringLengthError
UNSPECIFIED 
UNKNOWN	
EMPTY
	TOO_SHORT
TOO_LONGB�
#com.google.ads.googleads.v17.errorsBStringLengthErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
;google/ads/googleads/v17/errors/collection_size_error.protogoogle.ads.googleads.v17.errors"i
CollectionSizeErrorEnum"N
CollectionSizeError
UNSPECIFIED 
UNKNOWN
TOO_FEW
TOO_MANYB�
#com.google.ads.googleads.v17.errorsBCollectionSizeErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Igoogle/ads/googleads/v17/errors/resource_count_limit_exceeded_error.protogoogle.ads.googleads.v17.errors"�
#ResourceCountLimitExceededErrorEnum"�
ResourceCountLimitExceededError
UNSPECIFIED 
UNKNOWN

ACCOUNT_LIMIT
CAMPAIGN_LIMIT

ADGROUP_LIMIT
AD_GROUP_AD_LIMIT
AD_GROUP_CRITERION_LIMIT
SHARED_SET_LIMIT
MATCHING_FUNCTION_LIMIT
RESPONSE_ROW_LIMIT_EXCEEDED	
RESOURCE_LIMIT
B�
#com.google.ads.googleads.v17.errorsB$ResourceCountLimitExceededErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�2
5google/ads/googleads/v17/errors/criterion_error.protogoogle.ads.googleads.v17.errors"�/
CriterionErrorEnum"�/
CriterionError
UNSPECIFIED 
UNKNOWN
CONCRETE_TYPE_REQUIRED
INVALID_EXCLUDED_CATEGORY
INVALID_KEYWORD_TEXT
KEYWORD_TEXT_TOO_LONG
KEYWORD_HAS_TOO_MANY_WORDS
KEYWORD_HAS_INVALID_CHARS
INVALID_PLACEMENT_URL
INVALID_USER_LIST	
INVALID_USER_INTEREST
$
 INVALID_FORMAT_FOR_PLACEMENT_URL
PLACEMENT_URL_IS_TOO_LONG"
PLACEMENT_URL_HAS_ILLEGAL_CHAR
,
(PLACEMENT_URL_HAS_MULTIPLE_SITES_IN_LINE9
5PLACEMENT_IS_NOT_AVAILABLE_FOR_TARGETING_OR_EXCLUSION
INVALID_TOPIC_PATH
INVALID_YOUTUBE_CHANNEL_ID
INVALID_YOUTUBE_VIDEO_ID\'
#YOUTUBE_VERTICAL_CHANNEL_DEPRECATED*
&YOUTUBE_DEMOGRAPHIC_CHANNEL_DEPRECATED
YOUTUBE_URL_UNSUPPORTED 
CANNOT_EXCLUDE_CRITERIA_TYPE
CANNOT_ADD_CRITERIA_TYPE$
 CANNOT_EXCLUDE_SIMILAR_USER_LIST
CANNOT_ADD_CLOSED_USER_LIST:
6CANNOT_ADD_DISPLAY_ONLY_LISTS_TO_SEARCH_ONLY_CAMPAIGNS5
1CANNOT_ADD_DISPLAY_ONLY_LISTS_TO_SEARCH_CAMPAIGNS7
3CANNOT_ADD_DISPLAY_ONLY_LISTS_TO_SHOPPING_CAMPAIGNS1
-CANNOT_ADD_USER_INTERESTS_TO_SEARCH_CAMPAIGNS9
5CANNOT_SET_BIDS_ON_CRITERION_TYPE_IN_SEARCH_CAMPAIGNS 7
3CANNOT_ADD_URLS_TO_CRITERION_TYPE_FOR_CAMPAIGN_TYPE!
INVALID_COMBINED_AUDIENCEz
INVALID_CUSTOM_AFFINITY`
INVALID_CUSTOM_INTENTa
INVALID_CUSTOM_AUDIENCEy
INVALID_IP_ADDRESS"
INVALID_IP_FORMAT#
INVALID_MOBILE_APP$
INVALID_MOBILE_APP_CATEGORY%
INVALID_CRITERION_ID&
CANNOT_TARGET_CRITERION\'$
 CANNOT_TARGET_OBSOLETE_CRITERION("
CRITERION_ID_AND_TYPE_MISMATCH)
INVALID_PROXIMITY_RADIUS*"
INVALID_PROXIMITY_RADIUS_UNITS+ 
INVALID_STREETADDRESS_LENGTH,
INVALID_CITYNAME_LENGTH-
INVALID_REGIONCODE_LENGTH.
INVALID_REGIONNAME_LENGTH/
INVALID_POSTALCODE_LENGTH0
INVALID_COUNTRY_CODE1
INVALID_LATITUDE2
INVALID_LONGITUDE36
2PROXIMITY_GEOPOINT_AND_ADDRESS_BOTH_CANNOT_BE_NULL4
INVALID_PROXIMITY_ADDRESS5
INVALID_USER_DOMAIN_NAME6 
CRITERION_PARAMETER_TOO_LONG7&
"AD_SCHEDULE_TIME_INTERVALS_OVERLAP82
.AD_SCHEDULE_INTERVAL_CANNOT_SPAN_MULTIPLE_DAYS9%
!AD_SCHEDULE_INVALID_TIME_INTERVAL:0
,AD_SCHEDULE_EXCEEDED_INTERVALS_PER_DAY_LIMIT;/
+AD_SCHEDULE_CRITERION_ID_MISMATCHING_FIELDS<$
 CANNOT_BID_MODIFY_CRITERION_TYPE=2
.CANNOT_BID_MODIFY_CRITERION_CAMPAIGN_OPTED_OUT>(
$CANNOT_BID_MODIFY_NEGATIVE_CRITERION?
BID_MODIFIER_ALREADY_EXISTS@
FEED_ID_NOT_ALLOWEDA(
$ACCOUNT_INELIGIBLE_FOR_CRITERIA_TYPEB.
*CRITERIA_TYPE_INVALID_FOR_BIDDING_STRATEGYC
CANNOT_EXCLUDE_CRITERIOND
CANNOT_REMOVE_CRITERIONE$
 INVALID_PRODUCT_BIDDING_CATEGORYL
MISSING_SHOPPING_SETTINGM
INVALID_MATCHING_FUNCTIONN
LOCATION_FILTER_NOT_ALLOWEDO$
 INVALID_FEED_FOR_LOCATION_FILTERb
LOCATION_FILTER_INVALIDP7
3CANNOT_SET_GEO_TARGET_CONSTANTS_WITH_FEED_ITEM_SETS{\'
"CANNOT_SET_BOTH_ASSET_SET_AND_FEED�3
.CANNOT_SET_FEED_OR_FEED_ITEM_SETS_FOR_CUSTOMER�,
\'CANNOT_SET_ASSET_SET_FIELD_FOR_CUSTOMER�4
/CANNOT_SET_GEO_TARGET_CONSTANTS_WITH_ASSET_SETS�.
)CANNOT_SET_ASSET_SETS_WITH_FEED_ITEM_SETS�%
 INVALID_LOCATION_GROUP_ASSET_SET�!
INVALID_LOCATION_GROUP_RADIUS|&
"INVALID_LOCATION_GROUP_RADIUS_UNIT}2
.CANNOT_ATTACH_CRITERIA_AT_CAMPAIGN_AND_ADGROUPQ9
5HOTEL_LENGTH_OF_STAY_OVERLAPS_WITH_EXISTING_CRITERIONRA
=HOTEL_ADVANCE_BOOKING_WINDOW_OVERLAPS_WITH_EXISTING_CRITERIONS.
*FIELD_INCOMPATIBLE_WITH_NEGATIVE_TARGETINGT
INVALID_WEBPAGE_CONDITIONU!
INVALID_WEBPAGE_CONDITION_URLV)
%WEBPAGE_CONDITION_URL_CANNOT_BE_EMPTYW.
*WEBPAGE_CONDITION_URL_UNSUPPORTED_PROTOCOLX.
*WEBPAGE_CONDITION_URL_CANNOT_BE_IP_ADDRESSYE
AWEBPAGE_CONDITION_URL_DOMAIN_NOT_CONSISTENT_WITH_CAMPAIGN_SETTINGZ1
-WEBPAGE_CONDITION_URL_CANNOT_BE_PUBLIC_SUFFIX[/
+WEBPAGE_CONDITION_URL_INVALID_PUBLIC_SUFFIX\\9
5WEBPAGE_CONDITION_URL_VALUE_TRACK_VALUE_NOT_SUPPORTED]<
8WEBPAGE_CRITERION_URL_EQUALS_CAN_HAVE_ONLY_ONE_CONDITION^7
3WEBPAGE_CRITERION_NOT_SUPPORTED_ON_NON_DSA_AD_GROUP_7
3CANNOT_TARGET_USER_LIST_FOR_SMART_DISPLAY_CAMPAIGNSc1
-CANNOT_TARGET_PLACEMENTS_FOR_SEARCH_CAMPAIGNS~*
&LISTING_SCOPE_TOO_MANY_DIMENSION_TYPESd\'
#LISTING_SCOPE_TOO_MANY_IN_OPERATORSe+
\'LISTING_SCOPE_IN_OPERATOR_NOT_SUPPORTEDf$
 DUPLICATE_LISTING_DIMENSION_TYPEg%
!DUPLICATE_LISTING_DIMENSION_VALUEh0
,CANNOT_SET_BIDS_ON_LISTING_GROUP_SUBDIVISIONi#
INVALID_LISTING_GROUP_HIERARCHYj+
\'LISTING_GROUP_UNIT_CANNOT_HAVE_CHILDRENk2
.LISTING_GROUP_SUBDIVISION_REQUIRES_OTHERS_CASEl:
6LISTING_GROUP_REQUIRES_SAME_DIMENSION_TYPE_AS_SIBLINGSm 
LISTING_GROUP_ALREADY_EXISTSn 
LISTING_GROUP_DOES_NOT_EXISTo#
LISTING_GROUP_CANNOT_BE_REMOVEDp
INVALID_LISTING_GROUP_TYPEq*
&LISTING_GROUP_ADD_MAY_ONLY_USE_TEMP_IDr
LISTING_SCOPE_TOO_LONGs%
!LISTING_SCOPE_TOO_MANY_DIMENSIONSt
LISTING_GROUP_TOO_LONGu
LISTING_GROUP_TREE_TOO_DEEPv
INVALID_LISTING_DIMENSIONw"
INVALID_LISTING_DIMENSION_TYPEx@
<ADVERTISER_NOT_ON_ALLOWLIST_FOR_COMBINED_AUDIENCE_ON_DISPLAY,
\'CANNOT_TARGET_REMOVED_COMBINED_AUDIENCE�!
INVALID_COMBINED_AUDIENCE_ID�*
%CANNOT_TARGET_REMOVED_CUSTOM_AUDIENCE�?
:HOTEL_CHECK_IN_DATE_RANGE_OVERLAPS_WITH_EXISTING_CRITERION�3
.HOTEL_CHECK_IN_DATE_RANGE_START_DATE_TOO_EARLY�0
+HOTEL_CHECK_IN_DATE_RANGE_END_DATE_TOO_LATE�\'
"HOTEL_CHECK_IN_DATE_RANGE_REVERSED�-
(BROAD_MATCH_MODIFIER_KEYWORD_NOT_ALLOWED�)
$ONE_AUDIENCE_ALLOWED_PER_ASSET_GROUP�,
\'AUDIENCE_NOT_ELIGIBLE_FOR_CAMPAIGN_TYPE�F
AAUDIENCE_NOT_ALLOWED_TO_ATTACH_WHEN_AUDIENCE_GROUPED_SET_TO_FALSE�+
&CANNOT_TARGET_CUSTOMER_MATCH_USER_LIST�/
*NEGATIVE_KEYWORD_SHARED_SET_DOES_NOT_EXIST�3
.CANNOT_ADD_REMOVED_NEGATIVE_KEYWORD_SHARED_SET�;
6CANNOT_HAVE_MULTIPLE_NEGATIVE_KEYWORD_LIST_PER_ACCOUNT�/
*CUSTOMER_CANNOT_ADD_CRITERION_OF_THIS_TYPE�$
CANNOT_TARGET_SIMILAR_USER_LIST�G
BCANNOT_ADD_AUDIENCE_SEGMENT_CRITERION_WHEN_AUDIENCE_GROUPED_IS_SET�&
!ONE_AUDIENCE_ALLOWED_PER_AD_GROUP�!
INVALID_DETAILED_DEMOGRAPHIC�
CANNOT_RECOGNIZE_BRAND�$
BRAND_SHARED_SET_DOES_NOT_EXIST�(
#CANNOT_ADD_REMOVED_BRAND_SHARED_SET�8
3ONLY_EXCLUSION_BRAND_LIST_ALLOWED_FOR_CAMPAIGN_TYPE�B�
#com.google.ads.googleads.v17.errorsBCriterionErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
8google/ads/googleads/v17/errors/media_upload_error.protogoogle.ads.googleads.v17.errors"�
MediaUploadErrorEnum"�
MediaUploadError
UNSPECIFIED 
UNKNOWN
FILE_TOO_BIG
UNPARSEABLE_IMAGE
ANIMATED_IMAGE_NOT_ALLOWED
FORMAT_NOT_ALLOWED
EXTERNAL_URL_NOT_ALLOWED
INVALID_URL_REFERENCE&
"MISSING_PRIMARY_MEDIA_BUNDLE_ENTRY
ANIMATED_VISUAL_EFFECT	
ANIMATION_TOO_LONG

ASPECT_RATIO_NOT_ALLOWED%
!AUDIO_NOT_ALLOWED_IN_MEDIA_BUNDLE
CMYK_JPEG_NOT_ALLOWED

FLASH_NOT_ALLOWED
FRAME_RATE_TOO_HIGH.
*GOOGLE_WEB_DESIGNER_ZIP_FILE_NOT_PUBLISHED
IMAGE_CONSTRAINTS_VIOLATED
INVALID_MEDIA_BUNDLE
INVALID_MEDIA_BUNDLE_ENTRY
INVALID_MIME_TYPE
INVALID_PATH
LAYOUT_PROBLEM

MALFORMED_URL
MEDIA_BUNDLE_NOT_ALLOWED/
+MEDIA_BUNDLE_NOT_COMPATIBLE_TO_PRODUCT_TYPE1
-MEDIA_BUNDLE_REJECTED_BY_MULTIPLE_ASSET_SPECS"
TOO_MANY_FILES_IN_MEDIA_BUNDLE/
+UNSUPPORTED_GOOGLE_WEB_DESIGNER_ENVIRONMENT
UNSUPPORTED_HTML5_FEATURE)
%URL_IN_MEDIA_BUNDLE_NOT_SSL_COMPLIANT
VIDEO_FILE_NAME_TOO_LONG\'
#VIDEO_MULTIPLE_FILES_WITH_SAME_NAME %
!VIDEO_NOT_ALLOWED_IN_MEDIA_BUNDLE!(
$CANNOT_UPLOAD_MEDIA_TYPE_THROUGH_API"
DIMENSIONS_NOT_ALLOWED#B�
#com.google.ads.googleads.v17.errorsBMediaUploadErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
<google/ads/googleads/v17/errors/bidding_strategy_error.protogoogle.ads.googleads.v17.errors"�
BiddingStrategyErrorEnum"�
BiddingStrategyError
UNSPECIFIED 
UNKNOWN
DUPLICATE_NAME\'
#CANNOT_CHANGE_BIDDING_STRATEGY_TYPE%
!CANNOT_REMOVE_ASSOCIATED_STRATEGY"
BIDDING_STRATEGY_NOT_SUPPORTED@
<INCOMPATIBLE_BIDDING_STRATEGY_AND_BIDDING_STRATEGY_GOAL_TYPEB�
#com.google.ads.googleads.v17.errorsBBiddingStrategyErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
/google/ads/googleads/v17/errors/adx_error.protogoogle.ads.googleads.v17.errors"Q
AdxErrorEnum"A
AdxError
UNSPECIFIED 
UNKNOWN
UNSUPPORTED_FEATUREB�
#com.google.ads.googleads.v17.errorsB
AdxErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
>google/ads/googleads/v17/errors/feed_item_set_link_error.protogoogle.ads.googleads.v17.errors"�
FeedItemSetLinkErrorEnum"q
FeedItemSetLinkError
UNSPECIFIED 
UNKNOWN
FEED_ID_MISMATCH%
!NO_MUTATE_ALLOWED_FOR_DYNAMIC_SETB�
#com.google.ads.googleads.v17.errorsBFeedItemSetLinkErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Ygoogle/ads/googleads/v17/enums/policy_topic_evidence_destination_not_working_device.protogoogle.ads.googleads.v17.enums"�
2PolicyTopicEvidenceDestinationNotWorkingDeviceEnum"q
.PolicyTopicEvidenceDestinationNotWorkingDevice
UNSPECIFIED 
UNKNOWN
DESKTOP
ANDROID
IOSB�
"com.google.ads.googleads.v17.enumsB3PolicyTopicEvidenceDestinationNotWorkingDeviceProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
;google/ads/googleads/v17/errors/partial_failure_error.protogoogle.ads.googleads.v17.errors"q
PartialFailureErrorEnum"V
PartialFailureError
UNSPECIFIED 
UNKNOWN!
PARTIAL_FAILURE_MODE_REQUIREDB�
#com.google.ads.googleads.v17.errorsBPartialFailureErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
:google/ads/googleads/v17/errors/list_operation_error.protogoogle.ads.googleads.v17.errors"~
ListOperationErrorEnum"d
ListOperationError
UNSPECIFIED 
UNKNOWN
REQUIRED_FIELD_MISSING
DUPLICATE_VALUESB�
#com.google.ads.googleads.v17.errorsBListOperationErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�	
9google/ads/googleads/v17/errors/billing_setup_error.protogoogle.ads.googleads.v17.errors"�
BillingSetupErrorEnum"�
BillingSetupError
UNSPECIFIED 
UNKNOWN\'
#CANNOT_USE_EXISTING_AND_NEW_ACCOUNT\'
#CANNOT_REMOVE_STARTED_BILLING_SETUP2
.CANNOT_CHANGE_BILLING_TO_SAME_PAYMENTS_ACCOUNT3
/BILLING_SETUP_NOT_PERMITTED_FOR_CUSTOMER_STATUS
INVALID_PAYMENTS_ACCOUNT5
1BILLING_SETUP_NOT_PERMITTED_FOR_CUSTOMER_CATEGORY
INVALID_START_TIME_TYPE#
THIRD_PARTY_ALREADY_HAS_BILLING	
BILLING_SETUP_IN_PROGRESS

NO_SIGNUP_PERMISSION!
CHANGE_OF_BILL_TO_IN_PROGRESS
PAYMENTS_PROFILE_NOT_FOUND

PAYMENTS_ACCOUNT_NOT_FOUND
PAYMENTS_PROFILE_INELIGIBLE
PAYMENTS_ACCOUNT_INELIGIBLE$
 CUSTOMER_NEEDS_INTERNAL_APPROVAL7
3PAYMENTS_PROFILE_NEEDS_SERVICE_AGREEMENT_ACCEPTANCE6
2PAYMENTS_ACCOUNT_INELIGIBLE_CURRENCY_CODE_MISMATCH 
FUTURE_START_TIME_PROHIBITED0
,TOO_MANY_BILLING_SETUPS_FOR_PAYMENTS_ACCOUNTB�
#com.google.ads.googleads.v17.errorsBBillingSetupErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
?google/ads/googleads/v17/errors/campaign_shared_set_error.protogoogle.ads.googleads.v17.errors"r
CampaignSharedSetErrorEnum"T
CampaignSharedSetError
UNSPECIFIED 
UNKNOWN
SHARED_SET_ACCESS_DENIEDB�
#com.google.ads.googleads.v17.errorsBCampaignSharedSetErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
6google/ads/googleads/v17/errors/media_file_error.protogoogle.ads.googleads.v17.errors"�
MediaFileErrorEnum"�
MediaFileError
UNSPECIFIED 
UNKNOWN
CANNOT_CREATE_STANDARD_ICON0
,CANNOT_SELECT_STANDARD_ICON_WITH_OTHER_TYPES)
%CANNOT_SPECIFY_MEDIA_FILE_ID_AND_DATA
DUPLICATE_MEDIA
EMPTY_FIELD\'
#RESOURCE_REFERENCED_IN_MULTIPLE_OPS*
&FIELD_NOT_SUPPORTED_FOR_MEDIA_SUB_TYPE
INVALID_MEDIA_FILE_ID	
INVALID_MEDIA_SUB_TYPE

INVALID_MEDIA_FILE_TYPE
INVALID_MIME_TYPE
INVALID_REFERENCE_ID

INVALID_YOU_TUBE_ID!
MEDIA_FILE_FAILED_TRANSCODING
MEDIA_NOT_TRANSCODED-
)MEDIA_TYPE_DOES_NOT_MATCH_MEDIA_FILE_TYPE
NO_FIELDS_SPECIFIED"
NULL_REFERENCE_ID_AND_MEDIA_ID
TOO_LONG
UNSUPPORTED_TYPE 
YOU_TUBE_SERVICE_UNAVAILABLE,
(YOU_TUBE_VIDEO_HAS_NON_POSITIVE_DURATION
YOU_TUBE_VIDEO_NOT_FOUNDB�
#com.google.ads.googleads.v17.errorsBMediaFileErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
4google/ads/googleads/v17/errors/database_error.protogoogle.ads.googleads.v17.errors"�
DatabaseErrorEnum"�

DatabaseError
UNSPECIFIED 
UNKNOWN
CONCURRENT_MODIFICATION
DATA_CONSTRAINT_VIOLATION
REQUEST_TOO_LARGEB�
#com.google.ads.googleads.v17.errorsBDatabaseErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Igoogle/ads/googleads/v17/errors/keyword_plan_ad_group_keyword_error.protogoogle.ads.googleads.v17.errors"�
"KeywordPlanAdGroupKeywordErrorEnum"�
KeywordPlanAdGroupKeywordError
UNSPECIFIED 
UNKNOWN
INVALID_KEYWORD_MATCH_TYPE
DUPLICATE_KEYWORD
KEYWORD_TEXT_TOO_LONG
KEYWORD_HAS_INVALID_CHARS
KEYWORD_HAS_TOO_MANY_WORDS
INVALID_KEYWORD_TEXT 
NEGATIVE_KEYWORD_HAS_CPC_BID 
NEW_BMM_KEYWORDS_NOT_ALLOWED	B�
#com.google.ads.googleads.v17.errorsB#KeywordPlanAdGroupKeywordErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
>google/ads/googleads/v17/errors/ad_group_criterion_error.protogoogle.ads.googleads.v17.errors"�	
AdGroupCriterionErrorEnum"�	
AdGroupCriterionError
UNSPECIFIED 
UNKNOWN+
\'AD_GROUP_CRITERION_LABEL_DOES_NOT_EXIST+
\'AD_GROUP_CRITERION_LABEL_ALREADY_EXISTS*
&CANNOT_ADD_LABEL_TO_NEGATIVE_CRITERION
TOO_MANY_OPERATIONS
CANT_UPDATE_NEGATIVE
CONCRETE_TYPE_REQUIRED!
BID_INCOMPATIBLE_WITH_ADGROUP
CANNOT_TARGET_AND_EXCLUDE	
ILLEGAL_URL

INVALID_KEYWORD_TEXT
INVALID_DESTINATION_URL
MISSING_DESTINATION_URL_TAG
1
-KEYWORD_LEVEL_BID_NOT_SUPPORTED_FOR_MANUALCPM
INVALID_USER_STATUS
CANNOT_ADD_CRITERIA_TYPE 
CANNOT_EXCLUDE_CRITERIA_TYPE5
1CAMPAIGN_TYPE_NOT_COMPATIBLE_WITH_PARTIAL_FAILURE-
)OPERATIONS_FOR_TOO_MANY_SHOPPING_ADGROUPS4
0CANNOT_MODIFY_URL_FIELDS_WITH_DUPLICATE_ELEMENTS!
CANNOT_SET_WITHOUT_FINAL_URLS6
2CANNOT_CLEAR_FINAL_URLS_IF_FINAL_MOBILE_URLS_EXIST3
/CANNOT_CLEAR_FINAL_URLS_IF_FINAL_APP_URLS_EXIST ;
7CANNOT_CLEAR_FINAL_URLS_IF_TRACKING_URL_TEMPLATE_EXISTS!:
6CANNOT_CLEAR_FINAL_URLS_IF_URL_CUSTOM_PARAMETERS_EXIST"2
.CANNOT_SET_BOTH_DESTINATION_URL_AND_FINAL_URLS#=
9CANNOT_SET_BOTH_DESTINATION_URL_AND_TRACKING_URL_TEMPLATE$/
+FINAL_URLS_NOT_SUPPORTED_FOR_CRITERION_TYPE%6
2FINAL_MOBILE_URLS_NOT_SUPPORTED_FOR_CRITERION_TYPE&B�
#com.google.ads.googleads.v17.errorsBAdGroupCriterionErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Cgoogle/ads/googleads/v17/errors/user_list_customer_type_error.protogoogle.ads.googleads.v17.errors"�
UserListCustomerTypeErrorEnum"�
UserListCustomerTypeError
UNSPECIFIED 
UNKNOWN
CONFLICTING_CUSTOMER_TYPES
NO_ACCESS_TO_USER_LIST
USERLIST_NOT_ELIGIBLE>
:CONVERSION_TRACKING_NOT_ENABLED_OR_NOT_MCC_MANAGER_ACCOUNT-
)TOO_MANY_USER_LISTS_FOR_THE_CUSTOMER_TYPEB�
#com.google.ads.googleads.v17.errorsBUserListCustomerTypeErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
?google/ads/googleads/v17/errors/campaign_experiment_error.protogoogle.ads.googleads.v17.errors"�
CampaignExperimentErrorEnum"�
CampaignExperimentError
UNSPECIFIED 
UNKNOWN
DUPLICATE_NAME
INVALID_TRANSITION/
+CANNOT_CREATE_EXPERIMENT_WITH_SHARED_BUDGET6
2CANNOT_CREATE_EXPERIMENT_FOR_REMOVED_BASE_CAMPAIGN3
/CANNOT_CREATE_EXPERIMENT_FOR_NON_PROPOSED_DRAFT%
!CUSTOMER_CANNOT_CREATE_EXPERIMENT%
!CAMPAIGN_CANNOT_CREATE_EXPERIMENT)
%EXPERIMENT_DURATIONS_MUST_NOT_OVERLAP	8
4EXPERIMENT_DURATION_MUST_BE_WITHIN_CAMPAIGN_DURATION
*
&CANNOT_MUTATE_EXPERIMENT_DUE_TO_STATUSB�
#com.google.ads.googleads.v17.errorsBCampaignExperimentErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
9google/ads/googleads/v17/errors/language_code_error.protogoogle.ads.googleads.v17.errors"�
LanguageCodeErrorEnum"i
LanguageCodeError
UNSPECIFIED 
UNKNOWN
LANGUAGE_CODE_NOT_FOUND
INVALID_LANGUAGE_CODEB�
#com.google.ads.googleads.v17.errorsBLanguageCodeErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Dgoogle/ads/googleads/v17/errors/campaign_conversion_goal_error.protogoogle.ads.googleads.v17.errors"�
CampaignConversionGoalErrorEnum"�
CampaignConversionGoalError
UNSPECIFIED 
UNKNOWN@
<CANNOT_USE_CAMPAIGN_GOAL_FOR_SEARCH_ADS_360_MANAGED_CAMPAIGN;
7CANNOT_USE_STORE_SALE_GOAL_FOR_PERFORMANCE_MAX_CAMPAIGNB�
#com.google.ads.googleads.v17.errorsB CampaignConversionGoalErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
8google/ads/googleads/v17/errors/media_bundle_error.protogoogle.ads.googleads.v17.errors"�
MediaBundleErrorEnum"�
MediaBundleError
UNSPECIFIED 
UNKNOWN
BAD_REQUEST"
DOUBLECLICK_BUNDLE_NOT_ALLOWED
EXTERNAL_URL_NOT_ALLOWED
FILE_TOO_LARGE.
*GOOGLE_WEB_DESIGNER_ZIP_FILE_NOT_PUBLISHED

INVALID_INPUT
INVALID_MEDIA_BUNDLE	
INVALID_MEDIA_BUNDLE_ENTRY

INVALID_MIME_TYPE
INVALID_PATH
INVALID_URL_REFERENCE

MEDIA_DATA_TOO_LARGE&
"MISSING_PRIMARY_MEDIA_BUNDLE_ENTRY
SERVER_ERROR

STORAGE_ERROR
SWIFFY_BUNDLE_NOT_ALLOWED
TOO_MANY_FILES
UNEXPECTED_SIZE/
+UNSUPPORTED_GOOGLE_WEB_DESIGNER_ENVIRONMENT
UNSUPPORTED_HTML5_FEATURE)
%URL_IN_MEDIA_BUNDLE_NOT_SSL_COMPLIANT
CUSTOM_EXIT_NOT_ALLOWEDB�
#com.google.ads.googleads.v17.errorsBMediaBundleErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
3google/ads/googleads/v17/errors/context_error.protogoogle.ads.googleads.v17.errors"�
ContextErrorEnum"�
ContextError
UNSPECIFIED 
UNKNOWN\'
#OPERATION_NOT_PERMITTED_FOR_CONTEXT0
,OPERATION_NOT_PERMITTED_FOR_REMOVED_RESOURCEB�
#com.google.ads.googleads.v17.errorsBContextErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Kgoogle/ads/googleads/v17/errors/conversion_goal_campaign_config_error.protogoogle.ads.googleads.v17.errors"�
%ConversionGoalCampaignConfigErrorEnum"�
!ConversionGoalCampaignConfigError
UNSPECIFIED 
UNKNOWN@
<CANNOT_USE_CAMPAIGN_GOAL_FOR_SEARCH_ADS_360_MANAGED_CAMPAIGNA
=CUSTOM_GOAL_DOES_NOT_BELONG_TO_GOOGLE_ADS_CONVERSION_CUSTOMER%
!CAMPAIGN_CANNOT_USE_UNIFIED_GOALS
EMPTY_CONVERSION_GOALS2
.STORE_SALE_STORE_VISIT_CANNOT_BE_BOTH_INCLUDEDD
@PERFORMANCE_MAX_CAMPAIGN_CANNOT_USE_CUSTOM_GOAL_WITH_STORE_SALESB�
#com.google.ads.googleads.v17.errorsB&ConversionGoalCampaignConfigErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
.google/ads/googleads/v17/errors/id_error.protogoogle.ads.googleads.v17.errors"E
IdErrorEnum"6
IdError
UNSPECIFIED 
UNKNOWN
	NOT_FOUNDB�
#com.google.ads.googleads.v17.errorsBIdErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
<google/ads/googleads/v17/errors/shared_criterion_error.protogoogle.ads.googleads.v17.errors"�
SharedCriterionErrorEnum"h
SharedCriterionError
UNSPECIFIED 
UNKNOWN2
.CRITERION_TYPE_NOT_ALLOWED_FOR_SHARED_SET_TYPEB�
#com.google.ads.googleads.v17.errorsBSharedCriterionErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
4google/ads/googleads/v17/errors/ad_group_error.protogoogle.ads.googleads.v17.errors"�
AdGroupErrorEnum"�
AdGroupError
UNSPECIFIED 
UNKNOWN
DUPLICATE_ADGROUP_NAME
INVALID_ADGROUP_NAME%
!ADVERTISER_NOT_ON_CONTENT_NETWORK
BID_TOO_BIG*
&BID_TYPE_AND_BIDDING_STRATEGY_MISMATCH
MISSING_ADGROUP_NAME 
ADGROUP_LABEL_DOES_NOT_EXIST	 
ADGROUP_LABEL_ALREADY_EXISTS
,
(INVALID_CONTENT_BID_CRITERION_TYPE_GROUP8
4AD_GROUP_TYPE_NOT_VALID_FOR_ADVERTISING_CHANNEL_TYPE9
5ADGROUP_TYPE_NOT_SUPPORTED_FOR_CAMPAIGN_SALES_COUNTRY
B
>CANNOT_ADD_ADGROUP_OF_TYPE_DSA_TO_CAMPAIGN_WITHOUT_DSA_SETTING7
3PROMOTED_HOTEL_AD_GROUPS_NOT_AVAILABLE_FOR_CUSTOMER,
(INVALID_EXCLUDED_PARENT_ASSET_FIELD_TYPE*
&INVALID_EXCLUDED_PARENT_ASSET_SET_TYPE)
%CANNOT_ADD_AD_GROUP_FOR_CAMPAIGN_TYPE
INVALID_STATUSB�
#com.google.ads.googleads.v17.errorsBAdGroupErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
:google/ads/googleads/v17/errors/authentication_error.protogoogle.ads.googleads.v17.errors"�
AuthenticationErrorEnum"�
AuthenticationError
UNSPECIFIED 
UNKNOWN
AUTHENTICATION_ERROR
CLIENT_CUSTOMER_ID_INVALID
CUSTOMER_NOT_FOUND
GOOGLE_ACCOUNT_DELETED	!
GOOGLE_ACCOUNT_COOKIE_INVALID
(
$GOOGLE_ACCOUNT_AUTHENTICATION_FAILED-
)GOOGLE_ACCOUNT_USER_AND_ADS_USER_MISMATCH
LOGIN_COOKIE_REQUIRED

NOT_ADS_USER
OAUTH_TOKEN_INVALID
OAUTH_TOKEN_EXPIRED
OAUTH_TOKEN_DISABLED
OAUTH_TOKEN_REVOKED
OAUTH_TOKEN_HEADER_INVALID
LOGIN_COOKIE_INVALID
USER_ID_INVALID&
"TWO_STEP_VERIFICATION_NOT_ENROLLED$
 ADVANCED_PROTECTION_NOT_ENROLLED
ORGANIZATION_NOT_RECOGNIZED
ORGANIZATION_NOT_APPROVED4
0ORGANIZATION_NOT_ASSOCIATED_WITH_DEVELOPER_TOKENB�
#com.google.ads.googleads.v17.errorsBAuthenticationErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
+google/ads/googleads/v17/common/value.protogoogle.ads.googleads.v17.common"�
Value

boolean_value (H 
int64_value (H 
float_value (H 
double_value (H 
string_value (	H B
valueB�
#com.google.ads.googleads.v17.commonB
ValueProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/common;common�GAA�Google.Ads.GoogleAds.V17.Common�Google\\Ads\\GoogleAds\\V17\\Common�#Google::Ads::GoogleAds::V17::Commonbproto3
�
Cgoogle/ads/googleads/v17/errors/product_link_invitation_error.protogoogle.ads.googleads.v17.errors"�
ProductLinkInvitationErrorEnum"�
ProductLinkInvitationError
UNSPECIFIED 
UNKNOWN
INVALID_STATUS
PERMISSION_DENIED
NO_INVITATION_REQUIRED/
+CUSTOMER_NOT_PERMITTED_TO_CREATE_INVITATIONB�
#com.google.ads.googleads.v17.errorsBProductLinkInvitationErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Agoogle/ads/googleads/v17/errors/ad_group_bid_modifier_error.protogoogle.ads.googleads.v17.errors"�
AdGroupBidModifierErrorEnum"�
AdGroupBidModifierError
UNSPECIFIED 
UNKNOWN
CRITERION_ID_NOT_SUPPORTED=
9CANNOT_OVERRIDE_OPTED_OUT_CAMPAIGN_CRITERION_BID_MODIFIERB�
#com.google.ads.googleads.v17.errorsBAdGroupBidModifierErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
=google/ads/googleads/v17/errors/extension_setting_error.protogoogle.ads.googleads.v17.errors"�
ExtensionSettingErrorEnum"�
ExtensionSettingError
UNSPECIFIED 
UNKNOWN
EXTENSIONS_REQUIRED%
!FEED_TYPE_EXTENSION_TYPE_MISMATCH
INVALID_FEED_TYPE4
0INVALID_FEED_TYPE_FOR_CUSTOMER_EXTENSION_SETTING%
!CANNOT_CHANGE_FEED_ITEM_ON_CREATE)
%CANNOT_UPDATE_NEWLY_CREATED_EXTENSION3
/NO_EXISTING_AD_GROUP_EXTENSION_SETTING_FOR_TYPE3
/NO_EXISTING_CAMPAIGN_EXTENSION_SETTING_FOR_TYPE	3
/NO_EXISTING_CUSTOMER_EXTENSION_SETTING_FOR_TYPE
-
)AD_GROUP_EXTENSION_SETTING_ALREADY_EXISTS-
)CAMPAIGN_EXTENSION_SETTING_ALREADY_EXISTS-
)CUSTOMER_EXTENSION_SETTING_ALREADY_EXISTS
5
1AD_GROUP_FEED_ALREADY_EXISTS_FOR_PLACEHOLDER_TYPE5
1CAMPAIGN_FEED_ALREADY_EXISTS_FOR_PLACEHOLDER_TYPE5
1CUSTOMER_FEED_ALREADY_EXISTS_FOR_PLACEHOLDER_TYPE
VALUE_OUT_OF_RANGE$
 CANNOT_SET_FIELD_WITH_FINAL_URLS
FINAL_URLS_NOT_SET
INVALID_PHONE_NUMBER*
&PHONE_NUMBER_NOT_SUPPORTED_FOR_COUNTRY-
)CARRIER_SPECIFIC_SHORT_NUMBER_NOT_ALLOWED#
PREMIUM_RATE_NUMBER_NOT_ALLOWED
DISALLOWED_NUMBER_TYPE(
$INVALID_DOMESTIC_PHONE_NUMBER_FORMAT#
VANITY_PHONE_NUMBER_NOT_ALLOWED
INVALID_COUNTRY_CODE#
INVALID_CALL_CONVERSION_TYPE_ID.
*CUSTOMER_NOT_IN_ALLOWLIST_FOR_CALLTRACKINGE*
&CALLTRACKING_NOT_SUPPORTED_FOR_COUNTRY
INVALID_APP_ID&
"QUOTES_IN_REVIEW_EXTENSION_SNIPPET \'
#HYPHENS_IN_REVIEW_EXTENSION_SNIPPET!(
$REVIEW_EXTENSION_SOURCE_NOT_ELIGIBLE"(
$SOURCE_NAME_IN_REVIEW_EXTENSION_TEXT#

MISSING_FIELD$
INCONSISTENT_CURRENCY_CODES%*
&PRICE_EXTENSION_HAS_DUPLICATED_HEADERS&4
0PRICE_ITEM_HAS_DUPLICATED_HEADER_AND_DESCRIPTION\'%
!PRICE_EXTENSION_HAS_TOO_FEW_ITEMS(&
"PRICE_EXTENSION_HAS_TOO_MANY_ITEMS)
UNSUPPORTED_VALUE*
INVALID_DEVICE_PREFERENCE+
INVALID_SCHEDULE_END-*
&DATE_TIME_MUST_BE_IN_ACCOUNT_TIME_ZONE/%
!OVERLAPPING_SCHEDULES_NOT_ALLOWED0 
SCHEDULE_END_NOT_AFTER_START1
TOO_MANY_SCHEDULES_PER_DAY2&
"DUPLICATE_EXTENSION_FEED_ITEM_EDIT3
INVALID_SNIPPETS_HEADER4<
8PHONE_NUMBER_NOT_SUPPORTED_WITH_CALLTRACKING_FOR_COUNTRY5
CAMPAIGN_TARGETING_MISMATCH6"
CANNOT_OPERATE_ON_REMOVED_FEED7
EXTENSION_TYPE_REQUIRED8-
)INCOMPATIBLE_UNDERLYING_MATCHING_FUNCTION9
START_DATE_AFTER_END_DATE:
INVALID_PRICE_FORMAT;
PROMOTION_INVALID_TIME<<
8PROMOTION_CANNOT_SET_PERCENT_DISCOUNT_AND_MONEY_DISCOUNT=>
:PROMOTION_CANNOT_SET_PROMOTION_CODE_AND_ORDERS_OVER_AMOUNT>%
!TOO_MANY_DECIMAL_PLACES_SPECIFIED?
INVALID_LANGUAGE_CODE@
UNSUPPORTED_LANGUAGEA0
,CUSTOMER_CONSENT_FOR_CALL_RECORDING_REQUIREDB&
"EXTENSION_SETTING_UPDATE_IS_A_NOOPC
DISALLOWED_TEXTDB�
#com.google.ads.googleads.v17.errorsBExtensionSettingErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
=google/ads/googleads/v17/errors/access_invitation_error.protogoogle.ads.googleads.v17.errors"�
AccessInvitationErrorEnum"�
AccessInvitationError
UNSPECIFIED 
UNKNOWN
INVALID_EMAIL_ADDRESS$
 EMAIL_ADDRESS_ALREADY_HAS_ACCESS
INVALID_INVITATION_STATUS\'
#GOOGLE_CONSUMER_ACCOUNT_NOT_ALLOWED
INVALID_INVITATION_ID0
,EMAIL_ADDRESS_ALREADY_HAS_PENDING_INVITATION&
"PENDING_INVITATIONS_LIMIT_EXCEEDED 
EMAIL_DOMAIN_POLICY_VIOLATED	B�
#com.google.ads.googleads.v17.errorsBAccessInvitationErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
<google/ads/googleads/v17/errors/feed_item_target_error.protogoogle.ads.googleads.v17.errors"�
FeedItemTargetErrorEnum"�
FeedItemTargetError
UNSPECIFIED 
UNKNOWN#
MUST_SET_TARGET_ONEOF_ON_CREATE#
FEED_ITEM_TARGET_ALREADY_EXISTS&
"FEED_ITEM_SCHEDULES_CANNOT_OVERLAP(
$TARGET_LIMIT_EXCEEDED_FOR_GIVEN_TYPE
TOO_MANY_SCHEDULES_PER_DAY=
9CANNOT_HAVE_ENABLED_CAMPAIGN_AND_ENABLED_AD_GROUP_TARGETS
DUPLICATE_AD_SCHEDULE
DUPLICATE_KEYWORD	B�
#com.google.ads.googleads.v17.errorsBFeedItemTargetErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
<google/ads/googleads/v17/errors/function_parsing_error.protogoogle.ads.googleads.v17.errors"�
FunctionParsingErrorEnum"�
FunctionParsingError
UNSPECIFIED 
UNKNOWN

NO_MORE_INPUT
EXPECTED_CHARACTER
UNEXPECTED_SEPARATOR
UNMATCHED_LEFT_BRACKET
UNMATCHED_RIGHT_BRACKET
TOO_MANY_NESTED_FUNCTIONS
MISSING_RIGHT_HAND_OPERAND
INVALID_OPERATOR_NAME	/
+FEED_ATTRIBUTE_OPERAND_ARGUMENT_NOT_INTEGER

NO_OPERANDS
TOO_MANY_OPERANDSB�
#com.google.ads.googleads.v17.errorsBFunctionParsingErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
2google/ads/googleads/v17/errors/header_error.protogoogle.ads.googleads.v17.errors"}
HeaderErrorEnum"j
HeaderError
UNSPECIFIED 
UNKNOWN
INVALID_LOGIN_CUSTOMER_ID
INVALID_LINKED_CUSTOMER_IDB�
#com.google.ads.googleads.v17.errorsBHeaderErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Cgoogle/ads/googleads/v17/errors/campaign_lifecycle_goal_error.protogoogle.ads.googleads.v17.errors"�
CampaignLifecycleGoalErrorEnum"�
CampaignLifecycleGoalError
UNSPECIFIED 
UNKNOWN
CAMPAIGN_MISSING
INVALID_CAMPAIGN2
.CUSTOMER_ACQUISITION_INVALID_OPTIMIZATION_MODE!
INCOMPATIBLE_BIDDING_STRATEGY
MISSING_PURCHASE_GOAL4
0CUSTOMER_ACQUISITION_INVALID_HIGH_LIFETIME_VALUE2
.CUSTOMER_ACQUISITION_UNSUPPORTED_CAMPAIGN_TYPE&
"CUSTOMER_ACQUISITION_INVALID_VALUE	&
"CUSTOMER_ACQUISITION_VALUE_MISSING
=
9CUSTOMER_ACQUISITION_MISSING_EXISTING_CUSTOMER_DEFINITION?
;CUSTOMER_ACQUISITION_MISSING_HIGH_VALUE_CUSTOMER_DEFINITIONB�
#com.google.ads.googleads.v17.errorsBCampaignLifecycleGoalErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
8google/ads/googleads/v17/errors/product_link_error.protogoogle.ads.googleads.v17.errors"�
ProductLinkErrorEnum"�
ProductLinkError
UNSPECIFIED 
UNKNOWN
INVALID_OPERATION
CREATION_NOT_PERMITTED
INVITATION_EXISTS
LINK_EXISTSB�
#com.google.ads.googleads.v17.errorsBProductLinkErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
?google/ads/googleads/v17/errors/customer_customizer_error.protogoogle.ads.googleads.v17.errors"V
CustomerCustomizerErrorEnum"7
CustomerCustomizerError
UNSPECIFIED 
UNKNOWNB�
#com.google.ads.googleads.v17.errorsBCustomerCustomizerErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Agoogle/ads/googleads/v17/errors/identity_verification_error.protogoogle.ads.googleads.v17.errors"�
IdentityVerificationErrorEnum"�
IdentityVerificationError
UNSPECIFIED 
UNKNOWN
NO_EFFECTIVE_BILLING$
 BILLING_NOT_ON_MONTHLY_INVOICING 
VERIFICATION_ALREADY_STARTEDB�
#com.google.ads.googleads.v17.errorsBIdentityVerificationErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
9google/ads/googleads/v17/errors/change_status_error.protogoogle.ads.googleads.v17.errors"�
ChangeStatusErrorEnum"�
ChangeStatusError
UNSPECIFIED 
UNKNOWN
START_DATE_TOO_OLD
CHANGE_DATE_RANGE_INFINITE
CHANGE_DATE_RANGE_NEGATIVE
LIMIT_NOT_SPECIFIED
INVALID_LIMIT_CLAUSEB�
#com.google.ads.googleads.v17.errorsBChangeStatusErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
8google/ads/googleads/v17/errors/account_link_error.protogoogle.ads.googleads.v17.errors"s
AccountLinkErrorEnum"[
AccountLinkError
UNSPECIFIED 
UNKNOWN
INVALID_STATUS
PERMISSION_DENIEDB�
#com.google.ads.googleads.v17.errorsBAccountLinkErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�	
3google/ads/googleads/v17/errors/setting_error.protogoogle.ads.googleads.v17.errors"�
SettingErrorEnum"�
SettingError
UNSPECIFIED 
UNKNOWN!
SETTING_TYPE_IS_NOT_AVAILABLE0
,SETTING_TYPE_IS_NOT_COMPATIBLE_WITH_CAMPAIGN;
7TARGETING_SETTING_CONTAINS_INVALID_CRITERION_TYPE_GROUPQ
MTARGETING_SETTING_DEMOGRAPHIC_CRITERION_TYPE_GROUPS_MUST_BE_SET_TO_TARGET_ALL\\
XTARGETING_SETTING_CANNOT_CHANGE_TARGET_ALL_TO_FALSE_FOR_DEMOGRAPHIC_CRITERION_TYPE_GROUPC
?DYNAMIC_SEARCH_ADS_SETTING_AT_LEAST_ONE_FEED_ID_MUST_BE_PRESENT;
7DYNAMIC_SEARCH_ADS_SETTING_CONTAINS_INVALID_DOMAIN_NAME	6
2DYNAMIC_SEARCH_ADS_SETTING_CONTAINS_SUBDOMAIN_NAME
=
9DYNAMIC_SEARCH_ADS_SETTING_CONTAINS_INVALID_LANGUAGE_CODE>
:TARGET_ALL_IS_NOT_ALLOWED_FOR_PLACEMENT_IN_SEARCH_CAMPAIGN.
*SETTING_VALUE_NOT_COMPATIBLE_WITH_CAMPAIGNH
DBID_ONLY_IS_NOT_ALLOWED_TO_BE_MODIFIED_WITH_CUSTOMER_MATCH_TARGETINGB�
#com.google.ads.googleads.v17.errorsBSettingErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
=google/ads/googleads/v17/errors/conversion_upload_error.protogoogle.ads.googleads.v17.errors"�
ConversionUploadErrorEnum"�
ConversionUploadError
UNSPECIFIED 
UNKNOWN#
TOO_MANY_CONVERSIONS_IN_REQUEST
UNPARSEABLE_GCLID
CONVERSION_PRECEDES_EVENT*

EXPIRED_EVENT+
TOO_RECENT_EVENT,
EVENT_NOT_FOUND-
UNAUTHORIZED_CUSTOMER 
TOO_RECENT_CONVERSION_ACTION
6
2CONVERSION_TRACKING_NOT_ENABLED_AT_IMPRESSION_TIMEQ
MEXTERNAL_ATTRIBUTION_DATA_SET_FOR_NON_EXTERNALLY_ATTRIBUTED_CONVERSION_ACTIONQ
MEXTERNAL_ATTRIBUTION_DATA_NOT_SET_FOR_EXTERNALLY_ATTRIBUTED_CONVERSION_ACTION
F
BORDER_ID_NOT_PERMITTED_FOR_EXTERNALLY_ATTRIBUTED_CONVERSION_ACTION
ORDER_ID_ALREADY_IN_USE
DUPLICATE_ORDER_ID
TOO_RECENT_CALL
EXPIRED_CALL
CALL_NOT_FOUND
CONVERSION_PRECEDES_CALL0
,CONVERSION_TRACKING_NOT_ENABLED_AT_CALL_TIME$
 UNPARSEABLE_CALLERS_PHONE_NUMBER#
CLICK_CONVERSION_ALREADY_EXISTS"
CALL_CONVERSION_ALREADY_EXISTS)
%DUPLICATE_CLICK_CONVERSION_IN_REQUEST(
$DUPLICATE_CALL_CONVERSION_IN_REQUEST
CUSTOM_VARIABLE_NOT_ENABLED&
"CUSTOM_VARIABLE_VALUE_CONTAINS_PII
INVALID_CUSTOMER_FOR_CLICK
INVALID_CUSTOMER_FOR_CALL,
(CONVERSION_NOT_COMPLIANT_WITH_ATT_POLICY 
CLICK_NOT_FOUND!
INVALID_USER_IDENTIFIER"N
JEXTERNALLY_ATTRIBUTED_CONVERSION_ACTION_NOT_PERMITTED_WITH_USER_IDENTIFIER#
UNSUPPORTED_USER_IDENTIFIER$
GBRAID_WBRAID_BOTH_SET&
UNPARSEABLE_WBRAID\'
UNPARSEABLE_GBRAID(<
8ONE_PER_CLICK_CONVERSION_ACTION_NOT_PERMITTED_WITH_BRAID.7
3CUSTOMER_DATA_POLICY_PROHIBITS_ENHANCED_CONVERSIONS/-
)CUSTOMER_NOT_ACCEPTED_CUSTOMER_DATA_TERMS0
ORDER_ID_CONTAINS_PII17
3CUSTOMER_NOT_ENABLED_ENHANCED_CONVERSIONS_FOR_LEADS2
INVALID_JOB_ID4
NO_CONVERSION_ACTION_FOUND5"
INVALID_CONVERSION_ACTION_TYPE6B�
#com.google.ads.googleads.v17.errorsBConversionUploadErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
4google/ads/googleads/v17/errors/internal_error.protogoogle.ads.googleads.v17.errors"�
InternalErrorEnum"�

InternalError
UNSPECIFIED 
UNKNOWN
INTERNAL_ERROR
ERROR_CODE_NOT_PUBLISHED
TRANSIENT_ERROR
DEADLINE_EXCEEDEDB�
#com.google.ads.googleads.v17.errorsBInternalErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
5google/ads/googleads/v17/errors/user_data_error.protogoogle.ads.googleads.v17.errors"�
UserDataErrorEnum"�

UserDataError
UNSPECIFIED 
UNKNOWN-
)OPERATIONS_FOR_CUSTOMER_MATCH_NOT_ALLOWED
TOO_MANY_USER_IDENTIFIERS
USER_LIST_NOT_APPLICABLEB�
#com.google.ads.googleads.v17.errorsBUserDataErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
<google/ads/googleads/v17/errors/policy_violation_error.protogoogle.ads.googleads.v17.errors"b
PolicyViolationErrorEnum"F
PolicyViolationError
UNSPECIFIED 
UNKNOWN
POLICY_ERRORB�
#com.google.ads.googleads.v17.errorsBPolicyViolationErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
>google/ads/googleads/v17/errors/campaign_criterion_error.protogoogle.ads.googleads.v17.errors"�
CampaignCriterionErrorEnum"�

CampaignCriterionError
UNSPECIFIED 
UNKNOWN
CONCRETE_TYPE_REQUIRED
INVALID_PLACEMENT_URL 
CANNOT_EXCLUDE_CRITERIA_TYPE\'
#CANNOT_SET_STATUS_FOR_CRITERIA_TYPE+
\'CANNOT_SET_STATUS_FOR_EXCLUDED_CRITERIA
CANNOT_TARGET_AND_EXCLUDE
TOO_MANY_OPERATIONS-
)OPERATOR_NOT_SUPPORTED_FOR_CRITERION_TYPE	C
?SHOPPING_CAMPAIGN_SALES_COUNTRY_NOT_SUPPORTED_FOR_SALES_CHANNEL

CANNOT_ADD_EXISTING_FIELD$
 CANNOT_UPDATE_NEGATIVE_CRITERION8
4CANNOT_SET_NEGATIVE_KEYWORD_THEME_CONSTANT_CRITERION
"
INVALID_KEYWORD_THEME_CONSTANT=
9MISSING_KEYWORD_THEME_CONSTANT_OR_FREE_FORM_KEYWORD_THEMEI
ECANNOT_TARGET_BOTH_PROXIMITY_AND_LOCATION_CRITERIA_FOR_SMART_CAMPAIGN@
<CANNOT_TARGET_MULTIPLE_PROXIMITY_CRITERIA_FOR_SMART_CAMPAIGN5
1LOCATION_NOT_LAUNCHED_FOR_LOCAL_SERVICES_CAMPAIGN0
,LOCATION_INVALID_FOR_LOCAL_SERVICES_CAMPAIGN5
1CANNOT_TARGET_COUNTRY_FOR_LOCAL_SERVICES_CAMPAIGN<
8LOCATION_NOT_IN_HOME_COUNTRY_FOR_LOCAL_SERVICES_CAMPAIGN=
9CANNOT_ADD_OR_REMOVE_LOCATION_FOR_LOCAL_SERVICES_CAMPAIGNG
CAT_LEAST_ONE_POSITIVE_LOCATION_REQUIRED_FOR_LOCAL_SERVICES_CAMPAIGNP
LAT_LEAST_ONE_LOCAL_SERVICE_ID_CRITERION_REQUIRED_FOR_LOCAL_SERVICES_CAMPAIGN+
\'LOCAL_SERVICE_ID_NOT_FOUND_FOR_CATEGORY=
9CANNOT_ATTACH_BRAND_LIST_TO_NON_QUALIFIED_SEARCH_CAMPAIGNB
>CANNOT_REMOVE_ALL_LOCATIONS_DUE_TO_TOO_MANY_COUNTRY_EXCLUSIONSB�
#com.google.ads.googleads.v17.errorsBCampaignCriterionErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
:google/ads/googleads/v17/errors/smart_campaign_error.protogoogle.ads.googleads.v17.errors"�
SmartCampaignErrorEnum"�
SmartCampaignError
UNSPECIFIED 
UNKNOWN 
INVALID_BUSINESS_LOCATION_ID
INVALID_CAMPAIGN1
-BUSINESS_NAME_OR_BUSINESS_LOCATION_ID_MISSING%
!REQUIRED_SUGGESTION_FIELD_MISSING
GEO_TARGETS_REQUIRED&
"CANNOT_DETERMINE_SUGGESTION_LOCALE
FINAL_URL_NOT_CRAWLABLEB�
#com.google.ads.googleads.v17.errorsBSmartCampaignErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
<google/ads/googleads/v17/enums/policy_topic_entry_type.protogoogle.ads.googleads.v17.enums"�
PolicyTopicEntryTypeEnum"�
PolicyTopicEntryType
UNSPECIFIED 
UNKNOWN

PROHIBITED
LIMITED

FULLY_LIMITED
DESCRIPTIVE

BROADENING
AREA_OF_INTEREST_ONLYB�
"com.google.ads.googleads.v17.enumsBPolicyTopicEntryTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
,google/ads/googleads/v17/common/policy.protogoogle.ads.googleads.v17.commonXgoogle/ads/googleads/v17/enums/policy_topic_evidence_destination_mismatch_url_type.protoYgoogle/ads/googleads/v17/enums/policy_topic_evidence_destination_not_working_device.protoagoogle/ads/googleads/v17/enums/policy_topic_evidence_destination_not_working_dns_error_type.proto"n
PolicyViolationKey
policy_name (	H �
violating_text (	H�B
_policy_nameB
_violating_text"�
PolicyValidationParameter
ignorable_policy_topics (	Y
exempt_policy_violation_keys (23.google.ads.googleads.v17.common.PolicyViolationKey"�
PolicyTopicEntry
topic (	H �[
type (2M.google.ads.googleads.v17.enums.PolicyTopicEntryTypeEnum.PolicyTopicEntryTypeG
	evidences (24.google.ads.googleads.v17.common.PolicyTopicEvidenceK
constraints (26.google.ads.googleads.v17.common.PolicyTopicConstraintB
_topic"�

PolicyTopicEvidenceX
website_list (<EMAIL> R
	text_list (2=.google.ads.googleads.v17.common.PolicyTopicEvidence.TextListH 

language_code	 (	H i
destination_text_list (2H.google.ads.googleads.v17.common.PolicyTopicEvidence.DestinationTextListH h
destination_mismatch (2H.google.ads.googleads.v17.common.PolicyTopicEvidence.DestinationMismatchH m
destination_not_working (2J.google.ads.googleads.v17.common.PolicyTopicEvidence.DestinationNotWorkingH 
TextList
texts (	
WebsiteList
websites (	0
DestinationTextList
destination_texts (	�
DestinationMismatch�
	url_types (2.google.ads.googleads.v17.enums.PolicyTopicEvidenceDestinationMismatchUrlTypeEnum.PolicyTopicEvidenceDestinationMismatchUrlType�
DestinationNotWorking
expanded_url (	H��
device (2�.google.ads.googleads.v17.enums.PolicyTopicEvidenceDestinationNotWorkingDeviceEnum.PolicyTopicEvidenceDestinationNotWorkingDevice#
last_checked_date_time (	H��
dns_error_type (2�.google.ads.googleads.v17.enums.PolicyTopicEvidenceDestinationNotWorkingDnsErrorTypeEnum.PolicyTopicEvidenceDestinationNotWorkingDnsErrorTypeH 
http_error_code (H B
reasonB

_expanded_urlB
_last_checked_date_timeB
value"�
PolicyTopicConstrainto
country_constraint_list (2L.google.ads.googleads.v17.common.PolicyTopicConstraint.CountryConstraintListH h
reseller_constraint (2I.google.ads.googleads.v17.common.PolicyTopicConstraint.ResellerConstraintH {
#certificate_missing_in_country_list (2L.google.ads.googleads.v17.common.PolicyTopicConstraint.CountryConstraintListH �
+certificate_domain_mismatch_in_country_list (2L.google.ads.googleads.v17.common.PolicyTopicConstraint.CountryConstraintListH �
CountryConstraintList%
total_targeted_countries (H �[
	countries (2H.google.ads.googleads.v17.common.PolicyTopicConstraint.CountryConstraintB
_total_targeted_countries
ResellerConstraintI
CountryConstraint
country_criterion (	H �B
_country_criterionB
valueB�
#com.google.ads.googleads.v17.commonBPolicyProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/common;common�GAA�Google.Ads.GoogleAds.V17.Common�Google\\Ads\\GoogleAds\\V17\\Common�#Google::Ads::GoogleAds::V17::Commonbproto3
�
5google/ads/googleads/v17/errors/user_list_error.protogoogle.ads.googleads.v17.errors"�	
UserListErrorEnum"�	

UserListError
UNSPECIFIED 
UNKNOWN7
3EXTERNAL_REMARKETING_USER_LIST_MUTATE_NOT_SUPPORTED
CONCRETE_TYPE_REQUIRED
CONVERSION_TYPE_ID_REQUIRED
DUPLICATE_CONVERSION_TYPES
INVALID_CONVERSION_TYPE
INVALID_DESCRIPTION
INVALID_NAME
INVALID_TYPE	4
0CAN_NOT_ADD_LOGICAL_LIST_AS_LOGICAL_LIST_OPERAND
*
&INVALID_USER_LIST_LOGICAL_RULE_OPERAND
NAME_ALREADY_USED%
!NEW_CONVERSION_TYPE_NAME_REQUIRED
%
!CONVERSION_TYPE_NAME_ALREADY_USED
OWNERSHIP_REQUIRED_FOR_SET"
USER_LIST_MUTATE_NOT_SUPPORTED
INVALID_RULE
INVALID_DATE_RANGE%
!CAN_NOT_MUTATE_SENSITIVE_USERLIST
MAX_NUM_RULEBASED_USERLISTS\'
#CANNOT_MODIFY_BILLABLE_RECORD_COUNT
APP_ID_NOT_SET-
)USERLIST_NAME_IS_RESERVED_FOR_SYSTEM_LIST 7
3ADVERTISER_NOT_ON_ALLOWLIST_FOR_USING_UPLOADED_DATA%
RULE_TYPE_IS_NOT_SUPPORTED":
6CAN_NOT_ADD_A_SIMILAR_USERLIST_AS_LOGICAL_LIST_OPERAND#:
6CAN_NOT_MIX_CRM_BASED_IN_LOGICAL_LIST_WITH_OTHER_LISTS$
APP_ID_NOT_ALLOWED\'
CANNOT_MUTATE_SYSTEM_LIST(
MOBILE_APP_IS_SENSITIVE)
SEED_LIST_DOES_NOT_EXIST*#
INVALID_SEED_LIST_ACCESS_REASON+
INVALID_SEED_LIST_TYPE,
INVALID_COUNTRY_CODES-B�
#com.google.ads.googleads.v17.errorsBUserListErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
Jgoogle/ads/googleads/v17/errors/geo_target_constant_suggestion_error.protogoogle.ads.googleads.v17.errors"�
$GeoTargetConstantSuggestionErrorEnum"�
 GeoTargetConstantSuggestionError
UNSPECIFIED 
UNKNOWN
LOCATION_NAME_SIZE_LIMIT
LOCATION_NAME_LIMIT
INVALID_COUNTRY_CODE
REQUEST_PARAMETERS_UNSETB�
#com.google.ads.googleads.v17.errorsB%GeoTargetConstantSuggestionErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
>google/ads/googleads/v17/errors/asset_group_signal_error.protogoogle.ads.googleads.v17.errors"�
AssetGroupSignalErrorEnum"�
AssetGroupSignalError
UNSPECIFIED 
UNKNOWN
TOO_MANY_WORDS!
SEARCH_THEME_POLICY_VIOLATION&
"AUDIENCE_WITH_WRONG_ASSET_GROUP_IDB�
#com.google.ads.googleads.v17.errorsBAssetGroupSignalErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
;google/ads/googleads/v17/errors/asset_set_asset_error.protogoogle.ads.googleads.v17.errors"�
AssetSetAssetErrorEnum"�
AssetSetAssetError
UNSPECIFIED 
UNKNOWN
INVALID_ASSET_TYPE
INVALID_ASSET_SET_TYPE
DUPLICATE_EXTERNAL_KEY!
PARENT_LINKAGE_DOES_NOT_EXISTB�
#com.google.ads.googleads.v17.errorsBAssetSetAssetErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
:google/ads/googleads/v17/errors/experiment_arm_error.protogoogle.ads.googleads.v17.errors"�
ExperimentArmErrorEnum"�
ExperimentArmError
UNSPECIFIED 
UNKNOWN\'
#EXPERIMENT_ARM_COUNT_LIMIT_EXCEEDED
INVALID_CAMPAIGN_STATUS!
DUPLICATE_EXPERIMENT_ARM_NAME%
!CANNOT_SET_TREATMENT_ARM_CAMPAIGN
CANNOT_MODIFY_CAMPAIGN_IDS-
)CANNOT_MODIFY_CAMPAIGN_WITHOUT_SUFFIX_SET+
\'CANNOT_MUTATE_TRAFFIC_SPLIT_AFTER_START*
&CANNOT_ADD_CAMPAIGN_WITH_SHARED_BUDGET	*
&CANNOT_ADD_CAMPAIGN_WITH_CUSTOM_BUDGET
4
0CANNOT_ADD_CAMPAIGNS_WITH_DYNAMIC_ASSETS_ENABLED5
1UNSUPPORTED_CAMPAIGN_ADVERTISING_CHANNEL_SUB_TYPE,
(CANNOT_ADD_BASE_CAMPAIGN_WITH_DATE_RANGE
1
-BIDDING_STRATEGY_NOT_SUPPORTED_IN_EXPERIMENTS0
,TRAFFIC_SPLIT_NOT_SUPPORTED_FOR_CHANNEL_TYPEB�
#com.google.ads.googleads.v17.errorsBExperimentArmErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
�
5google/ads/googleads/v17/errors/time_zone_error.protogoogle.ads.googleads.v17.errors"Y
TimeZoneErrorEnum"D

TimeZoneError
UNSPECIFIED 
UNKNOWN
INVALID_TIME_ZONEB�
#com.google.ads.googleads.v17.errorsBTimeZoneErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3
��
,google/ads/googleads/v17/errors/errors.protogoogle.ads.googleads.v17.errors+google/ads/googleads/v17/common/value.proto8google/ads/googleads/v17/enums/resource_limit_type.proto=google/ads/googleads/v17/errors/access_invitation_error.protoCgoogle/ads/googleads/v17/errors/account_budget_proposal_error.proto8google/ads/googleads/v17/errors/account_link_error.proto9google/ads/googleads/v17/errors/ad_customizer_error.proto.google/ads/googleads/v17/errors/ad_error.proto7google/ads/googleads/v17/errors/ad_group_ad_error.protoAgoogle/ads/googleads/v17/errors/ad_group_bid_modifier_error.protoIgoogle/ads/googleads/v17/errors/ad_group_criterion_customizer_error.proto>google/ads/googleads/v17/errors/ad_group_criterion_error.proto?google/ads/googleads/v17/errors/ad_group_customizer_error.proto4google/ads/googleads/v17/errors/ad_group_error.proto9google/ads/googleads/v17/errors/ad_group_feed_error.proto8google/ads/googleads/v17/errors/ad_parameter_error.proto6google/ads/googleads/v17/errors/ad_sharing_error.proto/google/ads/googleads/v17/errors/adx_error.proto1google/ads/googleads/v17/errors/asset_error.proto=google/ads/googleads/v17/errors/asset_group_asset_error.proto7google/ads/googleads/v17/errors/asset_group_error.protoLgoogle/ads/googleads/v17/errors/asset_group_listing_group_filter_error.proto>google/ads/googleads/v17/errors/asset_group_signal_error.proto6google/ads/googleads/v17/errors/asset_link_error.proto;google/ads/googleads/v17/errors/asset_set_asset_error.proto5google/ads/googleads/v17/errors/asset_set_error.proto:google/ads/googleads/v17/errors/asset_set_link_error.proto4google/ads/googleads/v17/errors/audience_error.proto=google/ads/googleads/v17/errors/audience_insights_error.proto:google/ads/googleads/v17/errors/authentication_error.proto9google/ads/googleads/v17/errors/authorization_error.proto5google/ads/googleads/v17/errors/batch_job_error.proto3google/ads/googleads/v17/errors/bidding_error.proto<google/ads/googleads/v17/errors/bidding_strategy_error.proto9google/ads/googleads/v17/errors/billing_setup_error.proto;google/ads/googleads/v17/errors/campaign_budget_error.protoDgoogle/ads/googleads/v17/errors/campaign_conversion_goal_error.proto>google/ads/googleads/v17/errors/campaign_criterion_error.proto?google/ads/googleads/v17/errors/campaign_customizer_error.proto:google/ads/googleads/v17/errors/campaign_draft_error.proto4google/ads/googleads/v17/errors/campaign_error.proto?google/ads/googleads/v17/errors/campaign_experiment_error.proto9google/ads/googleads/v17/errors/campaign_feed_error.protoCgoogle/ads/googleads/v17/errors/campaign_lifecycle_goal_error.proto?google/ads/googleads/v17/errors/campaign_shared_set_error.proto8google/ads/googleads/v17/errors/change_event_error.proto9google/ads/googleads/v17/errors/change_status_error.proto;google/ads/googleads/v17/errors/collection_size_error.proto3google/ads/googleads/v17/errors/context_error.proto=google/ads/googleads/v17/errors/conversion_action_error.protoHgoogle/ads/googleads/v17/errors/conversion_adjustment_upload_error.protoFgoogle/ads/googleads/v17/errors/conversion_custom_variable_error.protoKgoogle/ads/googleads/v17/errors/conversion_goal_campaign_config_error.proto=google/ads/googleads/v17/errors/conversion_upload_error.protoAgoogle/ads/googleads/v17/errors/conversion_value_rule_error.protoEgoogle/ads/googleads/v17/errors/conversion_value_rule_set_error.proto8google/ads/googleads/v17/errors/country_code_error.proto5google/ads/googleads/v17/errors/criterion_error.proto9google/ads/googleads/v17/errors/currency_code_error.proto4google/ads/googleads/v17/errors/currency_error.proto;google/ads/googleads/v17/errors/custom_audience_error.protoBgoogle/ads/googleads/v17/errors/custom_conversion_goal_error.proto;google/ads/googleads/v17/errors/custom_interest_error.proto@google/ads/googleads/v17/errors/customer_client_link_error.proto?google/ads/googleads/v17/errors/customer_customizer_error.proto4google/ads/googleads/v17/errors/customer_error.proto9google/ads/googleads/v17/errors/customer_feed_error.protoCgoogle/ads/googleads/v17/errors/customer_lifecycle_goal_error.protoAgoogle/ads/googleads/v17/errors/customer_manager_link_error.protoZgoogle/ads/googleads/v17/errors/customer_sk_ad_network_conversion_value_schema_error.proto@google/ads/googleads/v17/errors/customer_user_access_error.proto@google/ads/googleads/v17/errors/customizer_attribute_error.proto4google/ads/googleads/v17/errors/database_error.proto0google/ads/googleads/v17/errors/date_error.proto6google/ads/googleads/v17/errors/date_range_error.proto4google/ads/googleads/v17/errors/distinct_error.proto0google/ads/googleads/v17/errors/enum_error.proto:google/ads/googleads/v17/errors/experiment_arm_error.proto6google/ads/googleads/v17/errors/experiment_error.proto?google/ads/googleads/v17/errors/extension_feed_item_error.proto=google/ads/googleads/v17/errors/extension_setting_error.protoDgoogle/ads/googleads/v17/errors/feed_attribute_reference_error.proto0google/ads/googleads/v17/errors/feed_error.proto5google/ads/googleads/v17/errors/feed_item_error.proto9google/ads/googleads/v17/errors/feed_item_set_error.proto>google/ads/googleads/v17/errors/feed_item_set_link_error.proto<google/ads/googleads/v17/errors/feed_item_target_error.proto@google/ads/googleads/v17/errors/feed_item_validation_error.proto8google/ads/googleads/v17/errors/feed_mapping_error.proto1google/ads/googleads/v17/errors/field_error.proto6google/ads/googleads/v17/errors/field_mask_error.proto4google/ads/googleads/v17/errors/function_error.proto<google/ads/googleads/v17/errors/function_parsing_error.protoJgoogle/ads/googleads/v17/errors/geo_target_constant_suggestion_error.proto2google/ads/googleads/v17/errors/header_error.proto.google/ads/googleads/v17/errors/id_error.protoAgoogle/ads/googleads/v17/errors/identity_verification_error.proto1google/ads/googleads/v17/errors/image_error.proto4google/ads/googleads/v17/errors/internal_error.proto3google/ads/googleads/v17/errors/invoice_error.protoAgoogle/ads/googleads/v17/errors/keyword_plan_ad_group_error.protoIgoogle/ads/googleads/v17/errors/keyword_plan_ad_group_keyword_error.protoAgoogle/ads/googleads/v17/errors/keyword_plan_campaign_error.protoIgoogle/ads/googleads/v17/errors/keyword_plan_campaign_keyword_error.proto8google/ads/googleads/v17/errors/keyword_plan_error.proto=google/ads/googleads/v17/errors/keyword_plan_idea_error.proto1google/ads/googleads/v17/errors/label_error.proto9google/ads/googleads/v17/errors/language_code_error.proto:google/ads/googleads/v17/errors/list_operation_error.proto8google/ads/googleads/v17/errors/manager_link_error.proto8google/ads/googleads/v17/errors/media_bundle_error.proto6google/ads/googleads/v17/errors/media_file_error.proto8google/ads/googleads/v17/errors/media_upload_error.proto;google/ads/googleads/v17/errors/merchant_center_error.proto6google/ads/googleads/v17/errors/multiplier_error.proto2google/ads/googleads/v17/errors/mutate_error.protoAgoogle/ads/googleads/v17/errors/new_resource_creation_error.proto;google/ads/googleads/v17/errors/not_allowlisted_error.proto5google/ads/googleads/v17/errors/not_empty_error.proto0google/ads/googleads/v17/errors/null_error.protoAgoogle/ads/googleads/v17/errors/offline_user_data_job_error.protoCgoogle/ads/googleads/v17/errors/operation_access_denied_error.proto4google/ads/googleads/v17/errors/operator_error.proto;google/ads/googleads/v17/errors/partial_failure_error.proto<google/ads/googleads/v17/errors/payments_account_error.proto:google/ads/googleads/v17/errors/policy_finding_error.protoGgoogle/ads/googleads/v17/errors/policy_validation_parameter_error.proto<google/ads/googleads/v17/errors/policy_violation_error.proto8google/ads/googleads/v17/errors/product_link_error.protoCgoogle/ads/googleads/v17/errors/product_link_invitation_error.proto1google/ads/googleads/v17/errors/query_error.proto1google/ads/googleads/v17/errors/quota_error.proto1google/ads/googleads/v17/errors/range_error.proto6google/ads/googleads/v17/errors/reach_plan_error.proto:google/ads/googleads/v17/errors/recommendation_error.protoGgoogle/ads/googleads/v17/errors/recommendation_subscription_error.proto7google/ads/googleads/v17/errors/region_code_error.proto3google/ads/googleads/v17/errors/request_error.protoBgoogle/ads/googleads/v17/errors/resource_access_denied_error.protoIgoogle/ads/googleads/v17/errors/resource_count_limit_exceeded_error.proto?google/ads/googleads/v17/errors/search_term_insight_error.proto3google/ads/googleads/v17/errors/setting_error.proto<google/ads/googleads/v17/errors/shared_criterion_error.proto6google/ads/googleads/v17/errors/shared_set_error.proto<google/ads/googleads/v17/errors/shopping_product_error.proto6google/ads/googleads/v17/errors/size_limit_error.proto:google/ads/googleads/v17/errors/smart_campaign_error.proto9google/ads/googleads/v17/errors/string_format_error.proto9google/ads/googleads/v17/errors/string_length_error.protoJgoogle/ads/googleads/v17/errors/third_party_app_analytics_link_error.proto5google/ads/googleads/v17/errors/time_zone_error.proto5google/ads/googleads/v17/errors/url_field_error.proto5google/ads/googleads/v17/errors/user_data_error.protoCgoogle/ads/googleads/v17/errors/user_list_customer_type_error.proto5google/ads/googleads/v17/errors/user_list_error.proto:google/ads/googleads/v17/errors/video_campaign_error.protoFgoogle/ads/googleads/v17/errors/youtube_video_registration_error.protogoogle/protobuf/duration.proto"g
GoogleAdsFailure?
errors (2/.google.ads.googleads.v17.errors.GoogleAdsError

request_id (	"�
GoogleAdsError>

error_code (2*.google.ads.googleads.v17.errors.ErrorCode
message (	7
trigger (2&.google.ads.googleads.v17.common.Value@
location (2..google.ads.googleads.v17.errors.ErrorLocation>
details (2-.google.ads.googleads.v17.errors.ErrorDetails"��
	ErrorCodeW

request_error (2>.google.ads.googleads.v17.errors.RequestErrorEnum.RequestErrorH p
bidding_strategy_error (2N.google.ads.googleads.v17.errors.BiddingStrategyErrorEnum.BiddingStrategyErrorH [
url_field_error (<EMAIL> j
list_operation_error (2J.google.ads.googleads.v17.errors.ListOperationErrorEnum.ListOperationErrorH Q
query_error (2:.google.ads.googleads.v17.errors.QueryErrorEnum.QueryErrorH T
mutate_error (2<.google.ads.googleads.v17.errors.MutateErrorEnum.MutateErrorH ^
field_mask_error (2B.google.ads.googleads.v17.errors.FieldMaskErrorEnum.FieldMaskErrorH i
authorization_error	 (2J.google.ads.googleads.v17.errors.AuthorizationErrorEnum.AuthorizationErrorH Z
internal_error
 (<EMAIL> Q
quota_error (2:.google.ads.googleads.v17.errors.QuotaErrorEnum.QuotaErrorH H
ad_error (24.google.ads.googleads.v17.errors.AdErrorEnum.AdErrorH X
ad_group_error
 (2>.google.ads.googleads.v17.errors.AdGroupErrorEnum.AdGroupErrorH m
campaign_budget_error (2L.google.ads.googleads.v17.errors.CampaignBudgetErrorEnum.CampaignBudgetErrorH Z
campaign_error (<EMAIL> k
video_campaign_error� (2J.google.ads.googleads.v17.errors.VideoCampaignErrorEnum.VideoCampaignErrorH l
authentication_error (2L.google.ads.googleads.v17.errors.AuthenticationErrorEnum.AuthenticationErrorH �
#ad_group_criterion_customizer_error� (2d.google.ads.googleads.v17.errors.AdGroupCriterionCustomizerErrorEnum.AdGroupCriterionCustomizerErrorH t
ad_group_criterion_error (2P.google.ads.googleads.v17.errors.AdGroupCriterionErrorEnum.AdGroupCriterionErrorH x
ad_group_customizer_error� (2R.google.ads.googleads.v17.errors.AdGroupCustomizerErrorEnum.AdGroupCustomizerErrorH g
ad_customizer_error (2H.google.ads.googleads.v17.errors.AdCustomizerErrorEnum.AdCustomizerErrorH _
ad_group_ad_error (2B.google.ads.googleads.v17.errors.AdGroupAdErrorEnum.AdGroupAdErrorH ^
ad_sharing_error (2B.google.ads.googleads.v17.errors.AdSharingErrorEnum.AdSharingErrorH K
	adx_error (26.google.ads.googleads.v17.errors.AdxErrorEnum.AdxErrorH Q
asset_errork (2:.google.ads.googleads.v17.errors.AssetErrorEnum.AssetErrorH r
asset_group_asset_error� (2N.google.ads.googleads.v17.errors.AssetGroupAssetErrorEnum.AssetGroupAssetErrorH �
&asset_group_listing_group_filter_error� (2h.google.ads.googleads.v17.errors.AssetGroupListingGroupFilterErrorEnum.AssetGroupListingGroupFilterErrorH b
asset_group_error� (2D.google.ads.googleads.v17.errors.AssetGroupErrorEnum.AssetGroupErrorH l
asset_set_asset_error� (2J.google.ads.googleads.v17.errors.AssetSetAssetErrorEnum.AssetSetAssetErrorH i
asset_set_link_error� (2H.google.ads.googleads.v17.errors.AssetSetLinkErrorEnum.AssetSetLinkErrorH \\
asset_set_error� (<EMAIL> W

bidding_error (2>.google.ads.googleads.v17.errors.BiddingErrorEnum.BiddingErrorH v
campaign_criterion_error (2R.google.ads.googleads.v17.errors.CampaignCriterionErrorEnum.CampaignCriterionErrorH �
campaign_conversion_goal_error� (2\\.google.ads.googleads.v17.errors.CampaignConversionGoalErrorEnum.CampaignConversionGoalErrorH z
campaign_customizer_error� (2T.google.ads.googleads.v17.errors.CampaignCustomizerErrorEnum.CampaignCustomizerErrorH m
collection_size_error (2L.google.ads.googleads.v17.errors.CollectionSizeErrorEnum.CollectionSizeErrorH �
%conversion_goal_campaign_config_error� (2h.google.ads.googleads.v17.errors.ConversionGoalCampaignConfigErrorEnum.ConversionGoalCampaignConfigErrorH d
country_code_errorm (2F.google.ads.googleads.v17.errors.CountryCodeErrorEnum.CountryCodeErrorH ]
criterion_error  (2B.google.ads.googleads.v17.errors.CriterionErrorEnum.CriterionErrorH �
custom_conversion_goal_error� (2X.google.ads.googleads.v17.errors.CustomConversionGoalErrorEnum.CustomConversionGoalErrorH z
customer_customizer_error� (2T.google.ads.googleads.v17.errors.CustomerCustomizerErrorEnum.CustomerCustomizerErrorH Z
customer_errorZ (<EMAIL> }
customizer_attribute_error� (2V.google.ads.googleads.v17.errors.CustomizerAttributeErrorEnum.CustomizerAttributeErrorH N

date_error! (28.google.ads.googleads.v17.errors.DateErrorEnum.DateErrorH ^
date_range_error" (2B.google.ads.googleads.v17.errors.DateRangeErrorEnum.DateRangeErrorH Z
distinct_error# (<EMAIL> �
feed_attribute_reference_error$ (2\\.google.ads.googleads.v17.errors.FeedAttributeReferenceErrorEnum.FeedAttributeReferenceErrorH Z
function_error% (<EMAIL> p
function_parsing_error& (2N.google.ads.googleads.v17.errors.FunctionParsingErrorEnum.FunctionParsingErrorH H
id_error\' (24.google.ads.googleads.v17.errors.IdErrorEnum.IdErrorH Q
image_error( (2:.google.ads.googleads.v17.errors.ImageErrorEnum.ImageErrorH g
language_code_errorn (2H.google.ads.googleads.v17.errors.LanguageCodeErrorEnum.LanguageCodeErrorH d
media_bundle_error* (2F.google.ads.googleads.v17.errors.MediaBundleErrorEnum.MediaBundleErrorH d
media_upload_errort (2F.google.ads.googleads.v17.errors.MediaUploadErrorEnum.MediaUploadErrorH ^
media_file_errorV (2B.google.ads.googleads.v17.errors.MediaFileErrorEnum.MediaFileErrorH n
merchant_center_error� (2L.google.ads.googleads.v17.errors.MerchantCenterErrorEnum.MerchantCenterErrorH `
multiplier_error, (2D.google.ads.googleads.v17.errors.MultiplierErrorEnum.MultiplierErrorH }
new_resource_creation_error- (2V.google.ads.googleads.v17.errors.NewResourceCreationErrorEnum.NewResourceCreationErrorH [
not_empty_error. (<EMAIL> N

null_error/ (28.google.ads.googleads.v17.errors.NullErrorEnum.NullErrorH Z
operator_error0 (<EMAIL> Q
range_error1 (2:.google.ads.googleads.v17.errors.RangeErrorEnum.RangeErrorH l
recommendation_error: (2L.google.ads.googleads.v17.errors.RecommendationErrorEnum.RecommendationErrorH �
!recommendation_subscription_error� (2d.google.ads.googleads.v17.errors.RecommendationSubscriptionErrorEnum.RecommendationSubscriptionErrorH a
region_code_error3 (2D.google.ads.googleads.v17.errors.RegionCodeErrorEnum.RegionCodeErrorH W

setting_error4 (2>.google.ads.googleads.v17.errors.SettingErrorEnum.SettingErrorH g
string_format_error5 (2H.google.ads.googleads.v17.errors.StringFormatErrorEnum.StringFormatErrorH g
string_length_error6 (2H.google.ads.googleads.v17.errors.StringLengthErrorEnum.StringLengthErrorH �
operation_access_denied_error7 (2Z.google.ads.googleads.v17.errors.OperationAccessDeniedErrorEnum.OperationAccessDeniedErrorH �
resource_access_denied_error8 (2X.google.ads.googleads.v17.errors.ResourceAccessDeniedErrorEnum.ResourceAccessDeniedErrorH �
#resource_count_limit_exceeded_error9 (2d.google.ads.googleads.v17.errors.ResourceCountLimitExceededErrorEnum.ResourceCountLimitExceededErrorH �
 youtube_video_registration_erroru (2`.google.ads.googleads.v17.errors.YoutubeVideoRegistrationErrorEnum.YoutubeVideoRegistrationErrorH {
ad_group_bid_modifier_error; (2T.google.ads.googleads.v17.errors.AdGroupBidModifierErrorEnum.AdGroupBidModifierErrorH W

context_error< (2>.google.ads.googleads.v17.errors.ContextErrorEnum.ContextErrorH Q
field_error= (2:.google.ads.googleads.v17.errors.FieldErrorEnum.FieldErrorH ^
shared_set_error> (2B.google.ads.googleads.v17.errors.SharedSetErrorEnum.SharedSetErrorH p
shared_criterion_error? (2N.google.ads.googleads.v17.errors.SharedCriterionErrorEnum.SharedCriterionErrorH w
campaign_shared_set_error@ (2R.google.ads.googleads.v17.errors.CampaignSharedSetErrorEnum.CampaignSharedSetErrorH s
conversion_action_errorA (2P.google.ads.googleads.v17.errors.ConversionActionErrorEnum.ConversionActionErrorH �
"conversion_adjustment_upload_errors (2d.google.ads.googleads.v17.errors.ConversionAdjustmentUploadErrorEnum.ConversionAdjustmentUploadErrorH �
 conversion_custom_variable_error� (2`.google.ads.googleads.v17.errors.ConversionCustomVariableErrorEnum.ConversionCustomVariableErrorH s
conversion_upload_erroro (2P.google.ads.googleads.v17.errors.ConversionUploadErrorEnum.ConversionUploadErrorH ~
conversion_value_rule_error� (2V.google.ads.googleads.v17.errors.ConversionValueRuleErrorEnum.ConversionValueRuleErrorH �
conversion_value_rule_set_error� (2\\.google.ads.googleads.v17.errors.ConversionValueRuleSetErrorEnum.ConversionValueRuleSetErrorH T
header_errorB (2<.google.ads.googleads.v17.errors.HeaderErrorEnum.HeaderErrorH Z
database_errorC (<EMAIL> j
policy_finding_errorD (2J.google.ads.googleads.v17.errors.PolicyFindingErrorEnum.PolicyFindingErrorH N

enum_errorF (28.google.ads.googleads.v17.errors.EnumErrorEnum.EnumErrorH d
keyword_plan_errorG (2F.google.ads.googleads.v17.errors.KeywordPlanErrorEnum.KeywordPlanErrorH }
keyword_plan_campaign_errorH (2V.google.ads.googleads.v17.errors.KeywordPlanCampaignErrorEnum.KeywordPlanCampaignErrorH �
#keyword_plan_campaign_keyword_error� (2d.google.ads.googleads.v17.errors.KeywordPlanCampaignKeywordErrorEnum.KeywordPlanCampaignKeywordErrorH {
keyword_plan_ad_group_errorJ (2T.google.ads.googleads.v17.errors.KeywordPlanAdGroupErrorEnum.KeywordPlanAdGroupErrorH �
#keyword_plan_ad_group_keyword_error� (2b.google.ads.googleads.v17.errors.KeywordPlanAdGroupKeywordErrorEnum.KeywordPlanAdGroupKeywordErrorH q
keyword_plan_idea_errorL (2N.google.ads.googleads.v17.errors.KeywordPlanIdeaErrorEnum.KeywordPlanIdeaErrorH �
account_budget_proposal_errorM (2Z.google.ads.googleads.v17.errors.AccountBudgetProposalErrorEnum.AccountBudgetProposalErrorH [
user_list_errorN (<EMAIL> e
change_event_error� (2F.google.ads.googleads.v17.errors.ChangeEventErrorEnum.ChangeEventErrorH g
change_status_errorO (2H.google.ads.googleads.v17.errors.ChangeStatusErrorEnum.ChangeStatusErrorH N

feed_errorP (28.google.ads.googleads.v17.errors.FeedErrorEnum.FeedErrorH �
$geo_target_constant_suggestion_errorQ (2f.google.ads.googleads.v17.errors.GeoTargetConstantSuggestionErrorEnum.GeoTargetConstantSuggestionErrorH j
campaign_draft_errorR (2J.google.ads.googleads.v17.errors.CampaignDraftErrorEnum.CampaignDraftErrorH [
feed_item_errorS (<EMAIL> Q
label_errorT (2:.google.ads.googleads.v17.errors.LabelErrorEnum.LabelErrorH g
billing_setup_errorW (2H.google.ads.googleads.v17.errors.BillingSetupErrorEnum.BillingSetupErrorH z
customer_client_link_errorX (2T.google.ads.googleads.v17.errors.CustomerClientLinkErrorEnum.CustomerClientLinkErrorH }
customer_manager_link_error[ (2V.google.ads.googleads.v17.errors.CustomerManagerLinkErrorEnum.CustomerManagerLinkErrorH d
feed_mapping_error\\ (2F.google.ads.googleads.v17.errors.FeedMappingErrorEnum.FeedMappingErrorH g
customer_feed_error] (2H.google.ads.googleads.v17.errors.CustomerFeedErrorEnum.CustomerFeedErrorH e
ad_group_feed_error^ (2F.google.ads.googleads.v17.errors.AdGroupFeedErrorEnum.AdGroupFeedErrorH g
campaign_feed_error` (2H.google.ads.googleads.v17.errors.CampaignFeedErrorEnum.CampaignFeedErrorH m
custom_interest_errora (2L.google.ads.googleads.v17.errors.CustomInterestErrorEnum.CustomInterestErrorH y
campaign_experiment_errorb (2T.google.ads.googleads.v17.errors.CampaignExperimentErrorEnum.CampaignExperimentErrorH w
extension_feed_item_errord (2R.google.ads.googleads.v17.errors.ExtensionFeedItemErrorEnum.ExtensionFeedItemErrorH d
ad_parameter_errore (2F.google.ads.googleads.v17.errors.AdParameterErrorEnum.AdParameterErrorH z
feed_item_validation_errorf (2T.google.ads.googleads.v17.errors.FeedItemValidationErrorEnum.FeedItemValidationErrorH s
extension_setting_errorg (2P.google.ads.googleads.v17.errors.ExtensionSettingErrorEnum.ExtensionSettingErrorH f
feed_item_set_error� (2F.google.ads.googleads.v17.errors.FeedItemSetErrorEnum.FeedItemSetErrorH s
feed_item_set_link_error� (2N.google.ads.googleads.v17.errors.FeedItemSetLinkErrorEnum.FeedItemSetLinkErrorH n
feed_item_target_errorh (2L.google.ads.googleads.v17.errors.FeedItemTargetErrorEnum.FeedItemTargetErrorH p
policy_violation_errori (2N.google.ads.googleads.v17.errors.PolicyViolationErrorEnum.PolicyViolationErrorH m
partial_failure_errorp (2L.google.ads.googleads.v17.errors.PartialFailureErrorEnum.PartialFailureErrorH �
!policy_validation_parameter_errorr (2b.google.ads.googleads.v17.errors.PolicyValidationParameterErrorEnum.PolicyValidationParameterErrorH ^
size_limit_errorv (2B.google.ads.googleads.v17.errors.SizeLimitErrorEnum.SizeLimitErrorH {
offline_user_data_job_errorw (2T.google.ads.googleads.v17.errors.OfflineUserDataJobErrorEnum.OfflineUserDataJobErrorH n
not_allowlisted_error� (2L.google.ads.googleads.v17.errors.NotAllowlistedErrorEnum.NotAllowlistedErrorH d
manager_link_errory (2F.google.ads.googleads.v17.errors.ManagerLinkErrorEnum.ManagerLinkErrorH g
currency_code_errorz (2H.google.ads.googleads.v17.errors.CurrencyCodeErrorEnum.CurrencyCodeErrorH `
experiment_error{ (2D.google.ads.googleads.v17.errors.ExperimentErrorEnum.ExperimentErrorH s
access_invitation_error| (2P.google.ads.googleads.v17.errors.AccessInvitationErrorEnum.AccessInvitationErrorH ^
reach_plan_error} (2B.google.ads.googleads.v17.errors.ReachPlanErrorEnum.ReachPlanErrorH W

invoice_error~ (2>.google.ads.googleads.v17.errors.InvoiceErrorEnum.InvoiceErrorH p
payments_account_error (2N.google.ads.googleads.v17.errors.PaymentsAccountErrorEnum.PaymentsAccountErrorH \\
time_zone_error� (<EMAIL> _
asset_link_error� (2B.google.ads.googleads.v17.errors.AssetLinkErrorEnum.AssetLinkErrorH \\
user_data_error� (<EMAIL> \\
batch_job_error� (<EMAIL> e
account_link_error� (2F.google.ads.googleads.v17.errors.AccountLinkErrorEnum.AccountLinkErrorH �
$third_party_app_analytics_link_error� (2d.google.ads.googleads.v17.errors.ThirdPartyAppAnalyticsLinkErrorEnum.ThirdPartyAppAnalyticsLinkErrorH {
customer_user_access_error� (2T.google.ads.googleads.v17.errors.CustomerUserAccessErrorEnum.CustomerUserAccessErrorH n
custom_audience_error� (2L.google.ads.googleads.v17.errors.CustomAudienceErrorEnum.CustomAudienceErrorH [
audience_error� (<EMAIL> x
search_term_insight_error� (2R.google.ads.googleads.v17.errors.SearchTermInsightErrorEnum.SearchTermInsightErrorH k
smart_campaign_error� (2J.google.ads.googleads.v17.errors.SmartCampaignErrorEnum.SmartCampaignErrorH k
experiment_arm_error� (2J.google.ads.googleads.v17.errors.ExperimentArmErrorEnum.ExperimentArmErrorH t
audience_insights_error� (2P.google.ads.googleads.v17.errors.AudienceInsightsErrorEnum.AudienceInsightsErrorH e
product_link_error� (2F.google.ads.googleads.v17.errors.ProductLinkErrorEnum.ProductLinkErrorH �
4customer_sk_ad_network_conversion_value_schema_error� (2�.google.ads.googleads.v17.errors.CustomerSkAdNetworkConversionValueSchemaErrorEnum.CustomerSkAdNetworkConversionValueSchemaErrorH [
currency_error� (<EMAIL> u
asset_group_signal_error� (2P.google.ads.googleads.v17.errors.AssetGroupSignalErrorEnum.AssetGroupSignalErrorH �
product_link_invitation_error� (2Z.google.ads.googleads.v17.errors.ProductLinkInvitationErrorEnum.ProductLinkInvitationErrorH �
customer_lifecycle_goal_error� (2Z.google.ads.googleads.v17.errors.CustomerLifecycleGoalErrorEnum.CustomerLifecycleGoalErrorH �
campaign_lifecycle_goal_error� (2Z.google.ads.googleads.v17.errors.CampaignLifecycleGoalErrorEnum.CampaignLifecycleGoalErrorH �
identity_verification_error� (2X.google.ads.googleads.v17.errors.IdentityVerificationErrorEnum.IdentityVerificationErrorH �
user_list_customer_type_error� (2X.google.ads.googleads.v17.errors.UserListCustomerTypeErrorEnum.UserListCustomerTypeErrorH q
shopping_product_error� (2N.google.ads.googleads.v17.errors.ShoppingProductErrorEnum.ShoppingProductErrorH B

error_code"�

ErrorLocation\\
field_path_elements (2?.google.ads.googleads.v17.errors.ErrorLocation.FieldPathElementD
FieldPathElement

field_name (	
index (H �B
_index"�
ErrorDetails
unpublished_error_code (	Y
policy_violation_details (27.google.ads.googleads.v17.errors.PolicyViolationDetailsU
policy_finding_details (25.google.ads.googleads.v17.errors.PolicyFindingDetailsO
quota_error_details (22.google.ads.googleads.v17.errors.QuotaErrorDetailsU
resource_count_details (25.google.ads.googleads.v17.errors.ResourceCountDetails"�
PolicyViolationDetails#
external_policy_description (	@
key (23.google.ads.googleads.v17.common.PolicyViolationKey
external_policy_name (	

is_exemptible ("g
PolicyFindingDetailsO
policy_topic_entries (21.google.ads.googleads.v17.common.PolicyTopicEntry"�
QuotaErrorDetailsU

rate_scope (2A.google.ads.googleads.v17.errors.QuotaErrorDetails.QuotaRateScope
	rate_name (	.
retry_delay (2.google.protobuf.Duration"J
QuotaRateScope
UNSPECIFIED 
UNKNOWN
ACCOUNT
	DEVELOPER"�
ResourceCountDetails
enclosing_id (	
enclosing_resource (	
limit ([

limit_type (2G.google.ads.googleads.v17.enums.ResourceLimitTypeEnum.ResourceLimitType
existing_count (B�
#com.google.ads.googleads.v17.errorsBErrorsProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

