<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v16/errors/keyword_plan_idea_error.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V16\Errors;

class KeywordPlanIdeaError
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
=google/ads/googleads/v16/errors/keyword_plan_idea_error.protogoogle.ads.googleads.v16.errors"x
KeywordPlanIdeaErrorEnum"\\
KeywordPlanIdeaError
UNSPECIFIED 
UNKNOWN
URL_CRAWL_ERROR

INVALID_VALUEB�
#com.google.ads.googleads.v16.errorsBKeywordPlanIdeaErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors�GAA�Google.Ads.GoogleAds.V16.Errors�Google\\Ads\\GoogleAds\\V16\\Errors�#Google::Ads::GoogleAds::V16::Errorsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

