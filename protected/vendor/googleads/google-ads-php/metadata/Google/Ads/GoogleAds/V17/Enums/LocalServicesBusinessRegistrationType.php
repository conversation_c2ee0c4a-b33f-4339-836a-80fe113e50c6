<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/local_services_business_registration_type.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class LocalServicesBusinessRegistrationType
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
Ngoogle/ads/googleads/v17/enums/local_services_business_registration_type.protogoogle.ads.googleads.v17.enums"�
)LocalServicesBusinessRegistrationTypeEnum"_
%LocalServicesBusinessRegistrationType
UNSPECIFIED 
UNKNOWN

NUMBER
DOCUMENTB�
"com.google.ads.googleads.v17.enumsB*LocalServicesBusinessRegistrationTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

