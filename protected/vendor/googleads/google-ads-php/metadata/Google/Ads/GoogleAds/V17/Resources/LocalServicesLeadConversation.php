<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/resources/local_services_lead_conversation.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Resources;

class LocalServicesLeadConversation
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\FieldBehavior::initOnce();
        \GPBMetadata\Google\Api\Resource::initOnce();
        $pool->internalAddGeneratedFile(
            '
�
Egoogle/ads/googleads/v17/enums/local_services_conversation_type.protogoogle.ads.googleads.v17.enums"�
%LocalServicesLeadConversationTypeEnum"�
ConversationType
UNSPECIFIED 
UNKNOWN	
EMAIL
MESSAGE

PHONE_CALL
SMS
BOOKING
WHATSAPP
ADS_APIB�
"com.google.ads.googleads.v17.enumsB"LocalServicesConversationTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Dgoogle/ads/googleads/v17/enums/local_services_participant_type.protogoogle.ads.googleads.v17.enums"q
 LocalServicesParticipantTypeEnum"M
ParticipantType
UNSPECIFIED 
UNKNOWN

ADVERTISER
CONSUMERB�
"com.google.ads.googleads.v17.enumsB!LocalServicesParticipantTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Igoogle/ads/googleads/v17/resources/local_services_lead_conversation.proto"google.ads.googleads.v17.resourcesDgoogle/ads/googleads/v17/enums/local_services_participant_type.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�
LocalServicesLeadConversationU

resource_name (	B>�A�A8
6googleads.googleapis.com/LocalServicesLeadConversation
id (B�Ay
conversation_channel (2V.google.ads.googleads.v17.enums.LocalServicesLeadConversationTypeEnum.ConversationTypeB�Ao
participant_type (2P.google.ads.googleads.v17.enums.LocalServicesParticipantTypeEnum.ParticipantTypeB�A@
lead (	B2�A�A,
*googleads.googleapis.com/LocalServicesLead
event_date_time (	B�AZ
phone_call_details (24.google.ads.googleads.v17.resources.PhoneCallDetailsB�AH �U
message_details (22.google.ads.googleads.v17.resources.MessageDetailsB�AH�:��A�
6googleads.googleapis.com/LocalServicesLeadConversation\\customers/{customer_id}/localServicesLeadConversations/{local_services_lead_conversation_id}B
_phone_call_detailsB
_message_details"V
PhoneCallDetails!
call_duration_millis (B�A
call_recording_url (	B�A"A
MessageDetails
text (	B�A
attachment_urls (	B�AB�
&com.google.ads.googleads.v17.resourcesB"LocalServicesLeadConversationProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3'
        , true);
        static::$is_initialized = true;
    }
}

