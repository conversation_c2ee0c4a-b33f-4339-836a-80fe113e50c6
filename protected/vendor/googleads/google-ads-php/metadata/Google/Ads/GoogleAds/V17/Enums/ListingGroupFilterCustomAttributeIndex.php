<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/listing_group_filter_custom_attribute_index.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class ListingGroupFilterCustomAttributeIndex
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
Pgoogle/ads/googleads/v17/enums/listing_group_filter_custom_attribute_index.protogoogle.ads.googleads.v17.enums"�
*ListingGroupFilterCustomAttributeIndexEnum"�
&ListingGroupFilterCustomAttributeIndex
UNSPECIFIED 
UNKNOWN

INDEX0

INDEX1

INDEX2

INDEX3

INDEX4B�
"com.google.ads.googleads.v17.enumsB+ListingGroupFilterCustomAttributeIndexProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

