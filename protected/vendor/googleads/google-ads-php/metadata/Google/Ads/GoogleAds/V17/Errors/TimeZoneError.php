<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/errors/time_zone_error.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Errors;

class TimeZoneError
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
5google/ads/googleads/v17/errors/time_zone_error.protogoogle.ads.googleads.v17.errors"Y
TimeZoneErrorEnum"D

TimeZoneError
UNSPECIFIED 
UNKNOWN
INVALID_TIME_ZONEB�
#com.google.ads.googleads.v17.errorsBTimeZoneErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

