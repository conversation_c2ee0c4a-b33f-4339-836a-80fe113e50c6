<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v16/errors/conversion_goal_campaign_config_error.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V16\Errors;

class ConversionGoalCampaignConfigError
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
Kgoogle/ads/googleads/v16/errors/conversion_goal_campaign_config_error.protogoogle.ads.googleads.v16.errors"�
%ConversionGoalCampaignConfigErrorEnum"�
!ConversionGoalCampaignConfigError
UNSPECIFIED 
UNKNOWN@
<CANNOT_USE_CAMPAIGN_GOAL_FOR_SEARCH_ADS_360_MANAGED_CAMPAIGNA
=CUSTOM_GOAL_DOES_NOT_BELONG_TO_GOOGLE_ADS_CONVERSION_CUSTOMER%
!CAMPAIGN_CANNOT_USE_UNIFIED_GOALS
EMPTY_CONVERSION_GOALS2
.STORE_SALE_STORE_VISIT_CANNOT_BE_BOTH_INCLUDEDD
@PERFORMANCE_MAX_CAMPAIGN_CANNOT_USE_CUSTOM_GOAL_WITH_STORE_SALESB�
#com.google.ads.googleads.v16.errorsB&ConversionGoalCampaignConfigErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors�GAA�Google.Ads.GoogleAds.V16.Errors�Google\\Ads\\GoogleAds\\V16\\Errors�#Google::Ads::GoogleAds::V16::Errorsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

