<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v16/resources/age_range_view.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V16\Resources;

class AgeRangeView
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\FieldBehavior::initOnce();
        \GPBMetadata\Google\Api\Resource::initOnce();
        $pool->internalAddGeneratedFile(
            '
�
7google/ads/googleads/v16/resources/age_range_view.proto"google.ads.googleads.v16.resourcesgoogle/api/resource.proto"�
AgeRangeViewD

resource_name (	B-�A�A\'
%googleads.googleapis.com/AgeRangeView:n�Ak
%googleads.googleapis.com/AgeRangeViewBcustomers/{customer_id}/ageRangeViews/{ad_group_id}~{criterion_id}B�
&com.google.ads.googleads.v16.resourcesBAgeRangeViewProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources�GAA�"Google.Ads.GoogleAds.V16.Resources�"Google\\Ads\\GoogleAds\\V16\\Resources�&Google::Ads::GoogleAds::V16::Resourcesbproto3'
        , true);
        static::$is_initialized = true;
    }
}

