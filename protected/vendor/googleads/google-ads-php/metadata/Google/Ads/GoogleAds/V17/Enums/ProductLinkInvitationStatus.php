<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/product_link_invitation_status.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class ProductLinkInvitationStatus
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
Cgoogle/ads/googleads/v17/enums/product_link_invitation_status.protogoogle.ads.googleads.v17.enums"�
ProductLinkInvitationStatusEnum"�
ProductLinkInvitationStatus
UNSPECIFIED 
UNKNOWN
ACCEPTED
	REQUESTED
PENDING_APPROVAL
REVOKED
REJECTED
EXPIREDB�
"com.google.ads.googleads.v17.enumsB ProductLinkInvitationStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

