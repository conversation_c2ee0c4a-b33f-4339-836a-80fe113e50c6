<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/distance_bucket.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class DistanceBucket
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
4google/ads/googleads/v17/enums/distance_bucket.protogoogle.ads.googleads.v17.enums"�
DistanceBucketEnum"�
DistanceBucket
UNSPECIFIED 
UNKNOWN
WITHIN_700M

WITHIN_1KM

WITHIN_5KM
WITHIN_10KM
WITHIN_15KM
WITHIN_20KM
WITHIN_25KM
WITHIN_30KM	
WITHIN_35KM

WITHIN_40KM
WITHIN_45KM
WITHIN_50KM

WITHIN_55KM
WITHIN_60KM
WITHIN_65KM
BEYOND_65KM
WITHIN_0_7MILES
WITHIN_1MILE

WITHIN_5MILES
WITHIN_10MILES
WITHIN_15MILES
WITHIN_20MILES
WITHIN_25MILES
WITHIN_30MILES
WITHIN_35MILES
WITHIN_40MILES
BEYOND_40MILESB�
"com.google.ads.googleads.v17.enumsBDistanceBucketProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

