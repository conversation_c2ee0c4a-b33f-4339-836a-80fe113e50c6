<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/errors/field_mask_error.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Errors;

class FieldMaskError
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
6google/ads/googleads/v17/errors/field_mask_error.protogoogle.ads.googleads.v17.errors"�
FieldMaskErrorEnum"�
FieldMaskError
UNSPECIFIED 
UNKNOWN
FIELD_MASK_MISSING
FIELD_MASK_NOT_ALLOWED
FIELD_NOT_FOUND
FIELD_HAS_SUBFIELDSB�
#com.google.ads.googleads.v17.errorsBFieldMaskErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

