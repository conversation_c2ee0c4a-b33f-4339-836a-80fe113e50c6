<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/local_services_lead_credit_state.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class LocalServicesLeadCreditState
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
Egoogle/ads/googleads/v17/enums/local_services_lead_credit_state.protogoogle.ads.googleads.v17.enums"f
LocalServicesCreditStateEnum"F
CreditState
UNSPECIFIED 
UNKNOWN
PENDING
CREDITEDB�
"com.google.ads.googleads.v17.enumsB!LocalServicesLeadCreditStateProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

