<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/resources/travel_activity_performance_view.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Resources;

class TravelActivityPerformanceView
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\FieldBehavior::initOnce();
        \GPBMetadata\Google\Api\Resource::initOnce();
        $pool->internalAddGeneratedFile(
            '
�
Igoogle/ads/googleads/v17/resources/travel_activity_performance_view.proto"google.ads.googleads.v17.resourcesgoogle/api/resource.proto"�
TravelActivityPerformanceViewU

resource_name (	B>�A�A8
6googleads.googleapis.com/TravelActivityPerformanceView:s�Ap
6googleads.googleapis.com/TravelActivityPerformanceView6customers/{customer_id}/travelActivityPerformanceViewsB�
&com.google.ads.googleads.v17.resourcesB"TravelActivityPerformanceViewProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3'
        , true);
        static::$is_initialized = true;
    }
}

