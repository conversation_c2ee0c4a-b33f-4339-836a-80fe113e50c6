<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/conversion_environment_enum.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class ConversionEnvironmentEnum
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
@google/ads/googleads/v17/enums/conversion_environment_enum.protogoogle.ads.googleads.v17.enums"d
ConversionEnvironmentEnum"G
ConversionEnvironment
UNSPECIFIED 
UNKNOWN
APP
WEBB�
"com.google.ads.googleads.v17.enumsBConversionEnvironmentEnumProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

