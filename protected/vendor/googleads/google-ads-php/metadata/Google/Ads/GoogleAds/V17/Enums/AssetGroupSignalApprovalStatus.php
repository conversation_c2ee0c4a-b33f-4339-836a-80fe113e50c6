<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/asset_group_signal_approval_status.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class AssetGroupSignalApprovalStatus
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
Ggoogle/ads/googleads/v17/enums/asset_group_signal_approval_status.protogoogle.ads.googleads.v17.enums"�
"AssetGroupSignalApprovalStatusEnum"|
AssetGroupSignalApprovalStatus
UNSPECIFIED 
UNKNOWN
APPROVED
LIMITED
DISAPPROVED
UNDER_REVIEWB�
"com.google.ads.googleads.v17.enumsB#AssetGroupSignalApprovalStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

