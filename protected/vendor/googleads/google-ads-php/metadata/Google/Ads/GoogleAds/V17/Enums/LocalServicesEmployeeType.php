<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/local_services_employee_type.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class LocalServicesEmployeeType
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
Agoogle/ads/googleads/v17/enums/local_services_employee_type.protogoogle.ads.googleads.v17.enums"|
LocalServicesEmployeeTypeEnum"[
LocalServicesEmployeeType
UNSPECIFIED 
UNKNOWN
BUSINESS_OWNER
EMPLOYEEB�
"com.google.ads.googleads.v17.enumsBLocalServicesEmployeeTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

