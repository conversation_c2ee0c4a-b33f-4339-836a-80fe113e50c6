<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/resources/local_services_employee.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Resources;

class LocalServicesEmployee
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\FieldBehavior::initOnce();
        \GPBMetadata\Google\Api\Resource::initOnce();
        $pool->internalAddGeneratedFile(
            '
�
Agoogle/ads/googleads/v17/enums/local_services_employee_type.protogoogle.ads.googleads.v17.enums"|
LocalServicesEmployeeTypeEnum"[
LocalServicesEmployeeType
UNSPECIFIED 
UNKNOWN
BUSINESS_OWNER
EMPLOYEEB�
"com.google.ads.googleads.v17.enumsBLocalServicesEmployeeTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Cgoogle/ads/googleads/v17/enums/local_services_employee_status.protogoogle.ads.googleads.v17.enums"x
LocalServicesEmployeeStatusEnum"U
LocalServicesEmployeeStatus
UNSPECIFIED 
UNKNOWN
ENABLED
REMOVEDB�
"com.google.ads.googleads.v17.enumsB LocalServicesEmployeeStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
@google/ads/googleads/v17/resources/local_services_employee.proto"google.ads.googleads.v17.resourcesAgoogle/ads/googleads/v17/enums/local_services_employee_type.protogoogle/api/field_behavior.protogoogle/api/resource.proto"�	
LocalServicesEmployeeM

resource_name (	B6�A�A0
.googleads.googleapis.com/LocalServicesEmployee
id (B�AH �
creation_date_time (	B�Ap
status (2[.google.ads.googleads.v17.enums.LocalServicesEmployeeStatusEnum.LocalServicesEmployeeStatusB�Aj
type (2W.google.ads.googleads.v17.enums.LocalServicesEmployeeTypeEnum.LocalServicesEmployeeTypeB�AU
university_degrees (24.google.ads.googleads.v17.resources.UniversityDegreeB�AG
residencies (2-.google.ads.googleads.v17.resources.ResidencyB�AH
fellowships (2..google.ads.googleads.v17.resources.FellowshipB�A
	job_title	 (	B�AH�)
year_started_practicing
 (B�AH�
languages_spoken (	B�A
category_ids (	B�A-
national_provider_id_number
 (	B�AH�

email_address (	B�AH�

first_name (	B�AH�
middle_name (	B�AH�
	last_name (	B�AH�:u�Ar
.googleads.googleapis.com/LocalServicesEmployee@customers/{customer_id}/localServicesEmployees/{gls_employee_id}B
_idB

_job_titleB
_year_started_practicingB
_national_provider_id_numberB
_email_addressB
_first_nameB
_middle_nameB

_last_name"�
UniversityDegree"
institution_name (	B�AH �
degree (	B�AH�!
graduation_year (B�AH�B
_institution_nameB	
_degreeB
_graduation_year"{
	Residency"
institution_name (	B�AH �!
completion_year (B�AH�B
_institution_nameB
_completion_year"|

Fellowship"
institution_name (	B�AH �!
completion_year (B�AH�B
_institution_nameB
_completion_yearB�
&com.google.ads.googleads.v17.resourcesBLocalServicesEmployeeProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3'
        , true);
        static::$is_initialized = true;
    }
}

