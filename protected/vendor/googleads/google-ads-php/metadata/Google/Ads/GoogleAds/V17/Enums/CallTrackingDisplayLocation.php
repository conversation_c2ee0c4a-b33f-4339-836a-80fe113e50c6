<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/call_tracking_display_location.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class CallTrackingDisplayLocation
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
Cgoogle/ads/googleads/v17/enums/call_tracking_display_location.protogoogle.ads.googleads.v17.enums"x
CallTrackingDisplayLocationEnum"U
CallTrackingDisplayLocation
UNSPECIFIED 
UNKNOWN
AD
LANDING_PAGEB�
"com.google.ads.googleads.v17.enumsB CallTrackingDisplayLocationProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

