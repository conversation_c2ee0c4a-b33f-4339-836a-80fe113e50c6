<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/google_ads_field_category.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class GoogleAdsFieldCategory
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
>google/ads/googleads/v17/enums/google_ads_field_category.protogoogle.ads.googleads.v17.enums"�
GoogleAdsFieldCategoryEnum"l
GoogleAdsFieldCategory
UNSPECIFIED 
UNKNOWN
RESOURCE
	ATTRIBUTE
SEGMENT

METRICB�
"com.google.ads.googleads.v17.enumsBGoogleAdsFieldCategoryProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

