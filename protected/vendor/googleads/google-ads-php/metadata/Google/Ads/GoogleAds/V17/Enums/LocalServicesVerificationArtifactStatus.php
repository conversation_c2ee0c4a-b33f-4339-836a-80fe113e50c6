<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/local_services_verification_artifact_status.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class LocalServicesVerificationArtifactStatus
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
Pgoogle/ads/googleads/v17/enums/local_services_verification_artifact_status.protogoogle.ads.googleads.v17.enums"�
+LocalServicesVerificationArtifactStatusEnum"�
\'LocalServicesVerificationArtifactStatus
UNSPECIFIED 
UNKNOWN

PASSED

FAILED
PENDING

NO_SUBMISSION
	CANCELLEDB�
"com.google.ads.googleads.v17.enumsB,LocalServicesVerificationArtifactStatusProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

