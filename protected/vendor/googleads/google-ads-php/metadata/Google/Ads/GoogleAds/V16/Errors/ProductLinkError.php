<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v16/errors/product_link_error.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V16\Errors;

class ProductLinkError
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
8google/ads/googleads/v16/errors/product_link_error.protogoogle.ads.googleads.v16.errors"�
ProductLinkErrorEnum"�
ProductLinkError
UNSPECIFIED 
UNKNOWN
INVALID_OPERATION
CREATION_NOT_PERMITTED
INVITATION_EXISTS
LINK_EXISTSB�
#com.google.ads.googleads.v16.errorsBProductLinkErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors�GAA�Google.Ads.GoogleAds.V16.Errors�Google\\Ads\\GoogleAds\\V16\\Errors�#Google::Ads::GoogleAds::V16::Errorsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

