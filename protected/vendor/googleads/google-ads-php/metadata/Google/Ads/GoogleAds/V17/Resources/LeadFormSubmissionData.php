<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/resources/lead_form_submission_data.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Resources;

class LeadFormSubmissionData
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\FieldBehavior::initOnce();
        \GPBMetadata\Google\Api\Resource::initOnce();
        $pool->internalAddGeneratedFile(
            '
�
Dgoogle/ads/googleads/v17/enums/lead_form_field_user_input_type.protogoogle.ads.googleads.v17.enums"�
LeadFormFieldUserInputTypeEnum"�
LeadFormFieldUserInputType
UNSPECIFIED 
UNKNOWN
	FULL_NAME	
EMAIL
PHONE_NUMBER
POSTAL_CODE
STREET_ADDRESS
CITY	

REGION

COUNTRY

WORK_EMAIL
COMPANY_NAME


WORK_PHONE
	JOB_TITLE
GOVERNMENT_ISSUED_ID_CPF_BR
GOVERNMENT_ISSUED_ID_DNI_AR
GOVERNMENT_ISSUED_ID_DNI_PE
GOVERNMENT_ISSUED_ID_RUT_CL
GOVERNMENT_ISSUED_ID_CC_CO
GOVERNMENT_ISSUED_ID_CI_EC
GOVERNMENT_ISSUED_ID_RFC_MX

FIRST_NAME
	LAST_NAME

VEHICLE_MODEL�
VEHICLE_TYPE�
PREFERRED_DEALERSHIP�
VEHICLE_PURCHASE_TIMELINE�
VEHICLE_OWNERSHIP�
VEHICLE_PAYMENT_TYPE�
VEHICLE_CONDITION�
COMPANY_SIZE�
ANNUAL_SALES�
YEARS_IN_BUSINESS�
JOB_DEPARTMENT�
JOB_ROLE�
OVER_18_AGE�
OVER_19_AGE�
OVER_20_AGE�
OVER_21_AGE�
OVER_22_AGE�
OVER_23_AGE�
OVER_24_AGE�
OVER_25_AGE�
OVER_26_AGE�
OVER_27_AGE�
OVER_28_AGE�
OVER_29_AGE�
OVER_30_AGE�
OVER_31_AGE�
OVER_32_AGE�
OVER_33_AGE�
OVER_34_AGE�
OVER_35_AGE�
OVER_36_AGE�
OVER_37_AGE�
OVER_38_AGE�
OVER_39_AGE�
OVER_40_AGE�
OVER_41_AGE�
OVER_42_AGE�
OVER_43_AGE�
OVER_44_AGE�
OVER_45_AGE�
OVER_46_AGE�
OVER_47_AGE�
OVER_48_AGE�
OVER_49_AGE�
OVER_50_AGE�
OVER_51_AGE�
OVER_52_AGE�
OVER_53_AGE�
OVER_54_AGE�
OVER_55_AGE�
OVER_56_AGE�
OVER_57_AGE�
OVER_58_AGE�
OVER_59_AGE�
OVER_60_AGE�
OVER_61_AGE�
OVER_62_AGE�
OVER_63_AGE�
OVER_64_AGE�
OVER_65_AGE�
EDUCATION_PROGRAM�
EDUCATION_COURSE�
PRODUCT�
SERVICE�

OFFER�
CATEGORY�
PREFERRED_CONTACT_METHOD�
PREFERRED_LOCATION�
PREFERRED_CONTACT_TIME�
PURCHASE_TIMELINE�
YEARS_OF_EXPERIENCE�
JOB_INDUSTRY�
LEVEL_OF_EDUCATION�

PROPERTY_TYPE�
REALTOR_HELP_GOAL�
PROPERTY_COMMUNITY�
PRICE_RANGE�
NUMBER_OF_BEDROOMS�
FURNISHED_PROPERTY�
PETS_ALLOWED_PROPERTY�
NEXT_PLANNED_PURCHASE�
EVENT_SIGNUP_INTEREST�
PREFERRED_SHOPPING_PLACES�
FAVORITE_BRAND�+
&TRANSPORTATION_COMMERCIAL_LICENSE_TYPE�
EVENT_BOOKING_INTEREST�
DESTINATION_COUNTRY�
DESTINATION_CITY�
DEPARTURE_COUNTRY�
DEPARTURE_CITY�
DEPARTURE_DATE�
RETURN_DATE�
NUMBER_OF_TRAVELERS�

TRAVEL_BUDGET�
TRAVEL_ACCOMMODATION�B�
"com.google.ads.googleads.v17.enumsBLeadFormFieldUserInputTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
Bgoogle/ads/googleads/v17/resources/lead_form_submission_data.proto"google.ads.googleads.v17.resourcesgoogle/api/field_behavior.protogoogle/api/resource.proto"�
LeadFormSubmissionDataN

resource_name (	B7�A�A1
/googleads.googleapis.com/LeadFormSubmissionData
id (	B�A5
asset (	B&�A�A 
googleads.googleapis.com/Asset;
campaign (	B)�A�A#
!googleads.googleapis.com/Campaigne
lead_form_submission_fields (2;.google.ads.googleads.v17.resources.LeadFormSubmissionFieldB�Ar
"custom_lead_form_submission_fields
 (2A.google.ads.googleads.v17.resources.CustomLeadFormSubmissionFieldB�A:
ad_group (	B(�A�A"
 googleads.googleapis.com/AdGroup?
ad_group_ad (	B*�A�A$
"googleads.googleapis.com/AdGroupAd
gclid (	B�A!
submission_date_time	 (	B�A:��A�
/googleads.googleapis.com/LeadFormSubmissionDataMcustomers/{customer_id}/leadFormSubmissionData/{lead_form_user_submission_id}"�
LeadFormSubmissionFieldr

field_type (2Y.google.ads.googleads.v17.enums.LeadFormFieldUserInputTypeEnum.LeadFormFieldUserInputTypeB�A
field_value (	B�A"U
CustomLeadFormSubmissionField

question_text (	B�A
field_value (	B�AB�
&com.google.ads.googleads.v17.resourcesBLeadFormSubmissionDataProtoPZKgoogle.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources�GAA�"Google.Ads.GoogleAds.V17.Resources�"Google\\Ads\\GoogleAds\\V17\\Resources�&Google::Ads::GoogleAds::V17::Resourcesbproto3'
        , true);
        static::$is_initialized = true;
    }
}

