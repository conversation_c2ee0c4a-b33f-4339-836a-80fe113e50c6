<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/errors/region_code_error.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Errors;

class RegionCodeError
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
7google/ads/googleads/v17/errors/region_code_error.protogoogle.ads.googleads.v17.errors"_
RegionCodeErrorEnum"H
RegionCodeError
UNSPECIFIED 
UNKNOWN
INVALID_REGION_CODEB�
#com.google.ads.googleads.v17.errorsBRegionCodeErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

