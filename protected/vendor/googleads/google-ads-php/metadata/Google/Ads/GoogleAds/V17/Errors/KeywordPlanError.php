<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/errors/keyword_plan_error.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Errors;

class KeywordPlanError
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
8google/ads/googleads/v17/errors/keyword_plan_error.protogoogle.ads.googleads.v17.errors"�
KeywordPlanErrorEnum"�
KeywordPlanError
UNSPECIFIED 
UNKNOWN
BID_MULTIPLIER_OUT_OF_RANGE
BID_TOO_HIGH
BID_TOO_LOW"
BID_TOO_MANY_FRACTIONAL_DIGITS
DAILY_BUDGET_TOO_LOW+
\'DAILY_BUDGET_TOO_MANY_FRACTIONAL_DIGITS

INVALID_VALUE 
KEYWORD_PLAN_HAS_NO_KEYWORDS	
KEYWORD_PLAN_NOT_ENABLED

KEYWORD_PLAN_NOT_FOUND
MISSING_BID

MISSING_FORECAST_PERIOD
INVALID_FORECAST_DATE_RANGE
INVALID_NAMEB�
#com.google.ads.googleads.v17.errorsBKeywordPlanErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

