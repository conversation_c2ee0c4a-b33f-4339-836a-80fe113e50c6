<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/user_list_type.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class UserListType
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
3google/ads/googleads/v17/enums/user_list_type.protogoogle.ads.googleads.v17.enums"�
UserListTypeEnum"�
UserListType
UNSPECIFIED 
UNKNOWN
REMARKETING
LOGICAL
EXTERNAL_REMARKETING

RULE_BASED
SIMILAR
	CRM_BASED
	LOOKALIKE	B�
"com.google.ads.googleads.v17.enumsBUserListTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

