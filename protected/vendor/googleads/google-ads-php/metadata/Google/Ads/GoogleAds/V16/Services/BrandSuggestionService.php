<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v16/services/brand_suggestion_service.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V16\Services;

class BrandSuggestionService
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\LaunchStage::initOnce();
        \GPBMetadata\Google\Api\FieldBehavior::initOnce();
        \GPBMetadata\Google\Api\Http::initOnce();
        \GPBMetadata\Google\Api\Annotations::initOnce();
        \GPBMetadata\Google\Protobuf\Duration::initOnce();
        \GPBMetadata\Google\Api\Client::initOnce();
        $pool->internalAddGeneratedFile(
            '
�
0google/ads/googleads/v16/enums/brand_state.protogoogle.ads.googleads.v16.enums"�
BrandStateEnum"�

BrandState
UNSPECIFIED 
UNKNOWN
ENABLED

DEPRECATED

UNVERIFIED
APPROVED
	CANCELLED
REJECTEDB�
"com.google.ads.googleads.v16.enumsBBrandStateProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums�GAA�Google.Ads.GoogleAds.V16.Enums�Google\\Ads\\GoogleAds\\V16\\Enums�"Google::Ads::GoogleAds::V16::Enumsbproto3
�
@google/ads/googleads/v16/services/brand_suggestion_service.proto!google.ads.googleads.v16.servicesgoogle/api/annotations.protogoogle/api/client.protogoogle/api/field_behavior.proto"
SuggestBrandsRequest
customer_id (	B�A
brand_prefix (	B�AH �
selected_brands (	B�AB

_brand_prefix"[
SuggestBrandsResponseB
brands (22.google.ads.googleads.v16.services.BrandSuggestion"�
BrandSuggestion

id (	
name (	
urls (	H
state (29.google.ads.googleads.v16.enums.BrandStateEnum.BrandState2�
BrandSuggestionService�

SuggestBrands7.google.ads.googleads.v16.services.SuggestBrandsRequest8.google.ads.googleads.v16.services.SuggestBrandsResponse"R�Acustomer_id,brand_prefix���1",/v16/customers/{customer_id=*}:suggestBrands:*E�Agoogleads.googleapis.com�A\'https://www.googleapis.com/auth/adwordsB�
%com.google.ads.googleads.v16.servicesBBrandSuggestionServiceProtoPZIgoogle.golang.org/genproto/googleapis/ads/googleads/v16/services;services�GAA�!Google.Ads.GoogleAds.V16.Services�!Google\\Ads\\GoogleAds\\V16\\Services�%Google::Ads::GoogleAds::V16::Servicesbproto3'
        , true);
        static::$is_initialized = true;
    }
}

