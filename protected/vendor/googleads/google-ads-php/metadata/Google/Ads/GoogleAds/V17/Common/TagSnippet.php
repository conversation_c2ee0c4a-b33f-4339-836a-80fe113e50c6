<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/common/tag_snippet.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Common;

class TagSnippet
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
>google/ads/googleads/v17/enums/tracking_code_page_format.protogoogle.ads.googleads.v17.enums"g
TrackingCodePageFormatEnum"I
TrackingCodePageFormat
UNSPECIFIED 
UNKNOWN
HTML
AMPB�
"com.google.ads.googleads.v17.enumsBTrackingCodePageFormatProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
7google/ads/googleads/v17/enums/tracking_code_type.protogoogle.ads.googleads.v17.enums"�
TrackingCodeTypeEnum"w
TrackingCodeType
UNSPECIFIED 
UNKNOWN
WEBPAGE
WEBPAGE_ONCLICK

CLICK_TO_CALL
WEBSITE_CALLB�
"com.google.ads.googleads.v17.enumsBTrackingCodeTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3
�
1google/ads/googleads/v17/common/tag_snippet.protogoogle.ads.googleads.v17.common7google/ads/googleads/v17/enums/tracking_code_type.proto"�

TagSnippetS
type (2E.google.ads.googleads.v17.enums.TrackingCodeTypeEnum.TrackingCodeTypef
page_format (2Q.google.ads.googleads.v17.enums.TrackingCodePageFormatEnum.TrackingCodePageFormat
global_site_tag (	H �

event_snippet (	H�B
_global_site_tagB
_event_snippetB�
#com.google.ads.googleads.v17.commonBTagSnippetProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/common;common�GAA�Google.Ads.GoogleAds.V17.Common�Google\\Ads\\GoogleAds\\V17\\Common�#Google::Ads::GoogleAds::V17::Commonbproto3'
        , true);
        static::$is_initialized = true;
    }
}

