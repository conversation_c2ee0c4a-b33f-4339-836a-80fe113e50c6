<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/listing_group_filter_type_enum.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class ListingGroupFilterTypeEnum
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
Cgoogle/ads/googleads/v17/enums/listing_group_filter_type_enum.protogoogle.ads.googleads.v17.enums"�
ListingGroupFilterTypeEnum"m
ListingGroupFilterType
UNSPECIFIED 
UNKNOWN
SUBDIVISION

UNIT_INCLUDED

UNIT_EXCLUDEDB�
"com.google.ads.googleads.v17.enumsBListingGroupFilterTypeEnumProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

