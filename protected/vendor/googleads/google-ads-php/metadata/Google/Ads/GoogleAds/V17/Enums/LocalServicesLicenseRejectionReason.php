<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/local_services_license_rejection_reason.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class LocalServicesLicenseRejectionReason
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
Lgoogle/ads/googleads/v17/enums/local_services_license_rejection_reason.protogoogle.ads.googleads.v17.enums"�
\'LocalServicesLicenseRejectionReasonEnum"�
#LocalServicesLicenseRejectionReason
UNSPECIFIED 
UNKNOWN
BUSINESS_NAME_MISMATCH
UNAUTHORIZED
EXPIRED
POOR_QUALITY
UNVERIFIABLE
WRONG_DOCUMENT_OR_ID	
OTHERB�
"com.google.ads.googleads.v17.enumsB(LocalServicesLicenseRejectionReasonProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

