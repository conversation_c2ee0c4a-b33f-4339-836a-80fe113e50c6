<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/local_services_verification_artifact_type.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class LocalServicesVerificationArtifactType
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
Ngoogle/ads/googleads/v17/enums/local_services_verification_artifact_type.protogoogle.ads.googleads.v17.enums"�
)LocalServicesVerificationArtifactTypeEnum"�
%LocalServicesVerificationArtifactType
UNSPECIFIED 
UNKNOWN
BACKGROUND_CHECK
	INSURANCE
LICENSE
BUSINESS_REGISTRATION_CHECKB�
"com.google.ads.googleads.v17.enumsB*LocalServicesVerificationArtifactTypeProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

