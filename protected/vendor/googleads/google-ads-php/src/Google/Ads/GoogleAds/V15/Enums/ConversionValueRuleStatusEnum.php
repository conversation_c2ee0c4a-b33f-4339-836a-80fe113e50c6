<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v15/enums/conversion_value_rule_status.proto

namespace Google\Ads\GoogleAds\V15\Enums;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Container for enum describing possible statuses of a conversion value rule.
 *
 * Generated from protobuf message <code>google.ads.googleads.v15.enums.ConversionValueRuleStatusEnum</code>
 */
class ConversionValueRuleStatusEnum extends \Google\Protobuf\Internal\Message
{

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V15\Enums\ConversionValueRuleStatus::initOnce();
        parent::__construct($data);
    }

}

