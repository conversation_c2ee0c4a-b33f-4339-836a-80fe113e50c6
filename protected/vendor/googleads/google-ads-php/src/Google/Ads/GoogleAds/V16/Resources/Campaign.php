<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v16/resources/campaign.proto

namespace Google\Ads\GoogleAds\V16\Resources;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A campaign.
 *
 * Generated from protobuf message <code>google.ads.googleads.v16.resources.Campaign</code>
 */
class Campaign extends \Google\Protobuf\Internal\Message
{
    /**
     * Immutable. The resource name of the campaign.
     * Campaign resource names have the form:
     * `customers/{customer_id}/campaigns/{campaign_id}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     */
    protected $resource_name = '';
    /**
     * Output only. The ID of the campaign.
     *
     * Generated from protobuf field <code>optional int64 id = 59 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $id = null;
    /**
     * The name of the campaign.
     * This field is required and should not be empty when creating new
     * campaigns.
     * It must not contain any null (code point 0x0), NL line feed
     * (code point 0xA) or carriage return (code point 0xD) characters.
     *
     * Generated from protobuf field <code>optional string name = 58;</code>
     */
    protected $name = null;
    /**
     * Output only. The primary status of the campaign.
     * Provides insight into why a campaign is not serving or not serving
     * optimally. Modification to the campaign and its related entities might take
     * a while to be reflected in this status.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.CampaignPrimaryStatusEnum.CampaignPrimaryStatus primary_status = 81 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $primary_status = 0;
    /**
     * Output only. The primary status reasons of the campaign.
     * Provides insight into why a campaign is not serving or not serving
     * optimally. These reasons are aggregated to determine an overall
     * CampaignPrimaryStatus.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v16.enums.CampaignPrimaryStatusReasonEnum.CampaignPrimaryStatusReason primary_status_reasons = 82 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    private $primary_status_reasons;
    /**
     * The status of the campaign.
     * When a new campaign is added, the status defaults to ENABLED.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.CampaignStatusEnum.CampaignStatus status = 5;</code>
     */
    protected $status = 0;
    /**
     * Output only. The ad serving status of the campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.CampaignServingStatusEnum.CampaignServingStatus serving_status = 21 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $serving_status = 0;
    /**
     * Output only. The system status of the campaign's bidding strategy.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.BiddingStrategySystemStatusEnum.BiddingStrategySystemStatus bidding_strategy_system_status = 78 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $bidding_strategy_system_status = 0;
    /**
     * The ad serving optimization status of the campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.AdServingOptimizationStatusEnum.AdServingOptimizationStatus ad_serving_optimization_status = 8;</code>
     */
    protected $ad_serving_optimization_status = 0;
    /**
     * Immutable. The primary serving target for ads within the campaign.
     * The targeting options can be refined in `network_settings`.
     * This field is required and should not be empty when creating new
     * campaigns.
     * Can be set only when creating campaigns.
     * After the campaign is created, the field can not be changed.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.AdvertisingChannelTypeEnum.AdvertisingChannelType advertising_channel_type = 9 [(.google.api.field_behavior) = IMMUTABLE];</code>
     */
    protected $advertising_channel_type = 0;
    /**
     * Immutable. Optional refinement to `advertising_channel_type`.
     * Must be a valid sub-type of the parent channel type.
     * Can be set only when creating campaigns.
     * After campaign is created, the field can not be changed.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.AdvertisingChannelSubTypeEnum.AdvertisingChannelSubType advertising_channel_sub_type = 10 [(.google.api.field_behavior) = IMMUTABLE];</code>
     */
    protected $advertising_channel_sub_type = 0;
    /**
     * The URL template for constructing a tracking URL.
     *
     * Generated from protobuf field <code>optional string tracking_url_template = 60;</code>
     */
    protected $tracking_url_template = null;
    /**
     * The list of mappings used to substitute custom parameter tags in a
     * `tracking_url_template`, `final_urls`, or `mobile_final_urls`.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v16.common.CustomParameter url_custom_parameters = 12;</code>
     */
    private $url_custom_parameters;
    /**
     * The Local Services Campaign related settings.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.LocalServicesCampaignSettings local_services_campaign_settings = 75;</code>
     */
    protected $local_services_campaign_settings = null;
    /**
     * Settings for Travel campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.TravelCampaignSettings travel_campaign_settings = 85;</code>
     */
    protected $travel_campaign_settings = null;
    /**
     * Settings for Discovery campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.DiscoveryCampaignSettings discovery_campaign_settings = 87;</code>
     */
    protected $discovery_campaign_settings = null;
    /**
     * Settings for Real-Time Bidding, a feature only available for campaigns
     * targeting the Ad Exchange network.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.RealTimeBiddingSetting real_time_bidding_setting = 39;</code>
     */
    protected $real_time_bidding_setting = null;
    /**
     * The network settings for the campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.NetworkSettings network_settings = 14;</code>
     */
    protected $network_settings = null;
    /**
     * Immutable. The hotel setting for the campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.HotelSettingInfo hotel_setting = 32 [(.google.api.field_behavior) = IMMUTABLE];</code>
     */
    protected $hotel_setting = null;
    /**
     * The setting for controlling Dynamic Search Ads (DSA).
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.DynamicSearchAdsSetting dynamic_search_ads_setting = 33;</code>
     */
    protected $dynamic_search_ads_setting = null;
    /**
     * The setting for controlling Shopping campaigns.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.ShoppingSetting shopping_setting = 36;</code>
     */
    protected $shopping_setting = null;
    /**
     * Setting for targeting related features.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.TargetingSetting targeting_setting = 43;</code>
     */
    protected $targeting_setting = null;
    /**
     * Immutable. Setting for audience related features.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v16.resources.Campaign.AudienceSetting audience_setting = 73 [(.google.api.field_behavior) = IMMUTABLE];</code>
     */
    protected $audience_setting = null;
    /**
     * The setting for ads geotargeting.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.GeoTargetTypeSetting geo_target_type_setting = 47;</code>
     */
    protected $geo_target_type_setting = null;
    /**
     * The setting for local campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.LocalCampaignSetting local_campaign_setting = 50;</code>
     */
    protected $local_campaign_setting = null;
    /**
     * The setting related to App Campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.AppCampaignSetting app_campaign_setting = 51;</code>
     */
    protected $app_campaign_setting = null;
    /**
     * Output only. The resource names of labels attached to this campaign.
     *
     * Generated from protobuf field <code>repeated string labels = 61 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     */
    private $labels;
    /**
     * Output only. The type of campaign: normal, draft, or experiment.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.CampaignExperimentTypeEnum.CampaignExperimentType experiment_type = 17 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $experiment_type = 0;
    /**
     * Output only. The resource name of the base campaign of a draft or
     * experiment campaign. For base campaigns, this is equal to `resource_name`.
     * This field is read-only.
     *
     * Generated from protobuf field <code>optional string base_campaign = 56 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     */
    protected $base_campaign = null;
    /**
     * The budget of the campaign.
     *
     * Generated from protobuf field <code>optional string campaign_budget = 62 [(.google.api.resource_reference) = {</code>
     */
    protected $campaign_budget = null;
    /**
     * Output only. The type of bidding strategy.
     * A bidding strategy can be created by setting either the bidding scheme to
     * create a standard bidding strategy or the `bidding_strategy` field to
     * create a portfolio bidding strategy.
     * This field is read-only.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.BiddingStrategyTypeEnum.BiddingStrategyType bidding_strategy_type = 22 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $bidding_strategy_type = 0;
    /**
     * Output only. Resource name of AccessibleBiddingStrategy, a read-only view
     * of the unrestricted attributes of the attached portfolio bidding strategy
     * identified by 'bidding_strategy'. Empty, if the campaign does not use a
     * portfolio strategy. Unrestricted strategy attributes are available to all
     * customers with whom the strategy is shared and are read from the
     * AccessibleBiddingStrategy resource. In contrast, restricted attributes are
     * only available to the owner customer of the strategy and their managers.
     * Restricted attributes can only be read from the BiddingStrategy resource.
     *
     * Generated from protobuf field <code>string accessible_bidding_strategy = 71 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     */
    protected $accessible_bidding_strategy = '';
    /**
     * The date when campaign started in serving customer's timezone in YYYY-MM-DD
     * format.
     *
     * Generated from protobuf field <code>optional string start_date = 63;</code>
     */
    protected $start_date = null;
    /**
     * The campaign group this campaign belongs to.
     *
     * Generated from protobuf field <code>optional string campaign_group = 76 [(.google.api.resource_reference) = {</code>
     */
    protected $campaign_group = null;
    /**
     * The last day of the campaign in serving customer's timezone in YYYY-MM-DD
     * format. On create, defaults to 2037-12-30, which means the campaign will
     * run indefinitely. To set an existing campaign to run indefinitely, set this
     * field to 2037-12-30.
     *
     * Generated from protobuf field <code>optional string end_date = 64;</code>
     */
    protected $end_date = null;
    /**
     * Suffix used to append query parameters to landing pages that are served
     * with parallel tracking.
     *
     * Generated from protobuf field <code>optional string final_url_suffix = 65;</code>
     */
    protected $final_url_suffix = null;
    /**
     * A list that limits how often each user will see this campaign's ads.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v16.common.FrequencyCapEntry frequency_caps = 40;</code>
     */
    private $frequency_caps;
    /**
     * Output only. Brand Safety setting at the individual campaign level. Allows
     * for selecting an inventory type to show your ads on content that is the
     * right fit for your brand. See
     * https://support.google.com/google-ads/answer/7515513.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.BrandSafetySuitabilityEnum.BrandSafetySuitability video_brand_safety_suitability = 42 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $video_brand_safety_suitability = 0;
    /**
     * Describes how unbranded pharma ads will be displayed.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.VanityPharma vanity_pharma = 44;</code>
     */
    protected $vanity_pharma = null;
    /**
     * Selective optimization setting for this campaign, which includes a set of
     * conversion actions to optimize this campaign towards.
     * This feature only applies to app campaigns that use MULTI_CHANNEL as
     * AdvertisingChannelType and APP_CAMPAIGN or APP_CAMPAIGN_FOR_ENGAGEMENT as
     * AdvertisingChannelSubType.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.SelectiveOptimization selective_optimization = 45;</code>
     */
    protected $selective_optimization = null;
    /**
     * Optimization goal setting for this campaign, which includes a set of
     * optimization goal types.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.OptimizationGoalSetting optimization_goal_setting = 54;</code>
     */
    protected $optimization_goal_setting = null;
    /**
     * Output only. Campaign-level settings for tracking information.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.TrackingSetting tracking_setting = 46 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $tracking_setting = null;
    /**
     * Payment mode for the campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.PaymentModeEnum.PaymentMode payment_mode = 52;</code>
     */
    protected $payment_mode = 0;
    /**
     * Output only. Optimization score of the campaign.
     * Optimization score is an estimate of how well a campaign is set to perform.
     * It ranges from 0% (0.0) to 100% (1.0), with 100% indicating that the
     * campaign is performing at full potential. This field is null for unscored
     * campaigns.
     * See "About optimization score" at
     * https://support.google.com/google-ads/answer/9061546.
     * This field is read-only.
     *
     * Generated from protobuf field <code>optional double optimization_score = 66 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $optimization_score = null;
    /**
     * The asset field types that should be excluded from this campaign. Asset
     * links with these field types will not be inherited by this campaign from
     * the upper level.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v16.enums.AssetFieldTypeEnum.AssetFieldType excluded_parent_asset_field_types = 69;</code>
     */
    private $excluded_parent_asset_field_types;
    /**
     * The asset set types that should be excluded from this campaign. Asset set
     * links with these types will not be inherited by this campaign from
     * the upper level.
     * Location group types (GMB_DYNAMIC_LOCATION_GROUP,
     * CHAIN_DYNAMIC_LOCATION_GROUP, and STATIC_LOCATION_GROUP) are child types of
     * LOCATION_SYNC. Therefore, if LOCATION_SYNC is set for this field, all
     * location group asset sets are not allowed to be linked to this campaign,
     * and all Location Extension (LE) and Affiliate Location Extensions (ALE)
     * will not be served under this campaign.
     * Only LOCATION_SYNC is currently supported.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v16.enums.AssetSetTypeEnum.AssetSetType excluded_parent_asset_set_types = 80;</code>
     */
    private $excluded_parent_asset_set_types;
    /**
     * Represents opting out of URL expansion to more targeted URLs. If opted out
     * (true), only the final URLs in the asset group or URLs specified in the
     * advertiser's Google Merchant Center or business data feeds are targeted.
     * If opted in (false), the entire domain will be targeted. This field can
     * only be set for Performance Max campaigns, where the default value is
     * false.
     *
     * Generated from protobuf field <code>optional bool url_expansion_opt_out = 72;</code>
     */
    protected $url_expansion_opt_out = null;
    /**
     * Output only. Information about campaigns being upgraded to Performance Max.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.PerformanceMaxUpgrade performance_max_upgrade = 77 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $performance_max_upgrade = null;
    /**
     * Immutable. The set of hotel properties for Performance Max for travel goals
     * campaigns.
     *
     * Generated from protobuf field <code>optional string hotel_property_asset_set = 83 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     */
    protected $hotel_property_asset_set = null;
    /**
     * Immutable. Listing type of ads served for this campaign.
     * Field is restricted for usage with Performance Max campaigns.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v16.enums.ListingTypeEnum.ListingType listing_type = 86 [(.google.api.field_behavior) = IMMUTABLE];</code>
     */
    protected $listing_type = null;
    /**
     * Contains the opt-in/out status of each AssetAutomationType.
     * See documentation of each asset automation type enum for default
     * opt in/out behavior.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v16.resources.Campaign.AssetAutomationSetting asset_automation_settings = 88;</code>
     */
    private $asset_automation_settings;
    protected $campaign_bidding_strategy;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $resource_name
     *           Immutable. The resource name of the campaign.
     *           Campaign resource names have the form:
     *           `customers/{customer_id}/campaigns/{campaign_id}`
     *     @type int|string $id
     *           Output only. The ID of the campaign.
     *     @type string $name
     *           The name of the campaign.
     *           This field is required and should not be empty when creating new
     *           campaigns.
     *           It must not contain any null (code point 0x0), NL line feed
     *           (code point 0xA) or carriage return (code point 0xD) characters.
     *     @type int $primary_status
     *           Output only. The primary status of the campaign.
     *           Provides insight into why a campaign is not serving or not serving
     *           optimally. Modification to the campaign and its related entities might take
     *           a while to be reflected in this status.
     *     @type array<int>|\Google\Protobuf\Internal\RepeatedField $primary_status_reasons
     *           Output only. The primary status reasons of the campaign.
     *           Provides insight into why a campaign is not serving or not serving
     *           optimally. These reasons are aggregated to determine an overall
     *           CampaignPrimaryStatus.
     *     @type int $status
     *           The status of the campaign.
     *           When a new campaign is added, the status defaults to ENABLED.
     *     @type int $serving_status
     *           Output only. The ad serving status of the campaign.
     *     @type int $bidding_strategy_system_status
     *           Output only. The system status of the campaign's bidding strategy.
     *     @type int $ad_serving_optimization_status
     *           The ad serving optimization status of the campaign.
     *     @type int $advertising_channel_type
     *           Immutable. The primary serving target for ads within the campaign.
     *           The targeting options can be refined in `network_settings`.
     *           This field is required and should not be empty when creating new
     *           campaigns.
     *           Can be set only when creating campaigns.
     *           After the campaign is created, the field can not be changed.
     *     @type int $advertising_channel_sub_type
     *           Immutable. Optional refinement to `advertising_channel_type`.
     *           Must be a valid sub-type of the parent channel type.
     *           Can be set only when creating campaigns.
     *           After campaign is created, the field can not be changed.
     *     @type string $tracking_url_template
     *           The URL template for constructing a tracking URL.
     *     @type array<\Google\Ads\GoogleAds\V16\Common\CustomParameter>|\Google\Protobuf\Internal\RepeatedField $url_custom_parameters
     *           The list of mappings used to substitute custom parameter tags in a
     *           `tracking_url_template`, `final_urls`, or `mobile_final_urls`.
     *     @type \Google\Ads\GoogleAds\V16\Resources\Campaign\LocalServicesCampaignSettings $local_services_campaign_settings
     *           The Local Services Campaign related settings.
     *     @type \Google\Ads\GoogleAds\V16\Resources\Campaign\TravelCampaignSettings $travel_campaign_settings
     *           Settings for Travel campaign.
     *     @type \Google\Ads\GoogleAds\V16\Resources\Campaign\DiscoveryCampaignSettings $discovery_campaign_settings
     *           Settings for Discovery campaign.
     *     @type \Google\Ads\GoogleAds\V16\Common\RealTimeBiddingSetting $real_time_bidding_setting
     *           Settings for Real-Time Bidding, a feature only available for campaigns
     *           targeting the Ad Exchange network.
     *     @type \Google\Ads\GoogleAds\V16\Resources\Campaign\NetworkSettings $network_settings
     *           The network settings for the campaign.
     *     @type \Google\Ads\GoogleAds\V16\Resources\Campaign\HotelSettingInfo $hotel_setting
     *           Immutable. The hotel setting for the campaign.
     *     @type \Google\Ads\GoogleAds\V16\Resources\Campaign\DynamicSearchAdsSetting $dynamic_search_ads_setting
     *           The setting for controlling Dynamic Search Ads (DSA).
     *     @type \Google\Ads\GoogleAds\V16\Resources\Campaign\ShoppingSetting $shopping_setting
     *           The setting for controlling Shopping campaigns.
     *     @type \Google\Ads\GoogleAds\V16\Common\TargetingSetting $targeting_setting
     *           Setting for targeting related features.
     *     @type \Google\Ads\GoogleAds\V16\Resources\Campaign\AudienceSetting $audience_setting
     *           Immutable. Setting for audience related features.
     *     @type \Google\Ads\GoogleAds\V16\Resources\Campaign\GeoTargetTypeSetting $geo_target_type_setting
     *           The setting for ads geotargeting.
     *     @type \Google\Ads\GoogleAds\V16\Resources\Campaign\LocalCampaignSetting $local_campaign_setting
     *           The setting for local campaign.
     *     @type \Google\Ads\GoogleAds\V16\Resources\Campaign\AppCampaignSetting $app_campaign_setting
     *           The setting related to App Campaign.
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $labels
     *           Output only. The resource names of labels attached to this campaign.
     *     @type int $experiment_type
     *           Output only. The type of campaign: normal, draft, or experiment.
     *     @type string $base_campaign
     *           Output only. The resource name of the base campaign of a draft or
     *           experiment campaign. For base campaigns, this is equal to `resource_name`.
     *           This field is read-only.
     *     @type string $campaign_budget
     *           The budget of the campaign.
     *     @type int $bidding_strategy_type
     *           Output only. The type of bidding strategy.
     *           A bidding strategy can be created by setting either the bidding scheme to
     *           create a standard bidding strategy or the `bidding_strategy` field to
     *           create a portfolio bidding strategy.
     *           This field is read-only.
     *     @type string $accessible_bidding_strategy
     *           Output only. Resource name of AccessibleBiddingStrategy, a read-only view
     *           of the unrestricted attributes of the attached portfolio bidding strategy
     *           identified by 'bidding_strategy'. Empty, if the campaign does not use a
     *           portfolio strategy. Unrestricted strategy attributes are available to all
     *           customers with whom the strategy is shared and are read from the
     *           AccessibleBiddingStrategy resource. In contrast, restricted attributes are
     *           only available to the owner customer of the strategy and their managers.
     *           Restricted attributes can only be read from the BiddingStrategy resource.
     *     @type string $start_date
     *           The date when campaign started in serving customer's timezone in YYYY-MM-DD
     *           format.
     *     @type string $campaign_group
     *           The campaign group this campaign belongs to.
     *     @type string $end_date
     *           The last day of the campaign in serving customer's timezone in YYYY-MM-DD
     *           format. On create, defaults to 2037-12-30, which means the campaign will
     *           run indefinitely. To set an existing campaign to run indefinitely, set this
     *           field to 2037-12-30.
     *     @type string $final_url_suffix
     *           Suffix used to append query parameters to landing pages that are served
     *           with parallel tracking.
     *     @type array<\Google\Ads\GoogleAds\V16\Common\FrequencyCapEntry>|\Google\Protobuf\Internal\RepeatedField $frequency_caps
     *           A list that limits how often each user will see this campaign's ads.
     *     @type int $video_brand_safety_suitability
     *           Output only. Brand Safety setting at the individual campaign level. Allows
     *           for selecting an inventory type to show your ads on content that is the
     *           right fit for your brand. See
     *           https://support.google.com/google-ads/answer/7515513.
     *     @type \Google\Ads\GoogleAds\V16\Resources\Campaign\VanityPharma $vanity_pharma
     *           Describes how unbranded pharma ads will be displayed.
     *     @type \Google\Ads\GoogleAds\V16\Resources\Campaign\SelectiveOptimization $selective_optimization
     *           Selective optimization setting for this campaign, which includes a set of
     *           conversion actions to optimize this campaign towards.
     *           This feature only applies to app campaigns that use MULTI_CHANNEL as
     *           AdvertisingChannelType and APP_CAMPAIGN or APP_CAMPAIGN_FOR_ENGAGEMENT as
     *           AdvertisingChannelSubType.
     *     @type \Google\Ads\GoogleAds\V16\Resources\Campaign\OptimizationGoalSetting $optimization_goal_setting
     *           Optimization goal setting for this campaign, which includes a set of
     *           optimization goal types.
     *     @type \Google\Ads\GoogleAds\V16\Resources\Campaign\TrackingSetting $tracking_setting
     *           Output only. Campaign-level settings for tracking information.
     *     @type int $payment_mode
     *           Payment mode for the campaign.
     *     @type float $optimization_score
     *           Output only. Optimization score of the campaign.
     *           Optimization score is an estimate of how well a campaign is set to perform.
     *           It ranges from 0% (0.0) to 100% (1.0), with 100% indicating that the
     *           campaign is performing at full potential. This field is null for unscored
     *           campaigns.
     *           See "About optimization score" at
     *           https://support.google.com/google-ads/answer/9061546.
     *           This field is read-only.
     *     @type array<int>|\Google\Protobuf\Internal\RepeatedField $excluded_parent_asset_field_types
     *           The asset field types that should be excluded from this campaign. Asset
     *           links with these field types will not be inherited by this campaign from
     *           the upper level.
     *     @type array<int>|\Google\Protobuf\Internal\RepeatedField $excluded_parent_asset_set_types
     *           The asset set types that should be excluded from this campaign. Asset set
     *           links with these types will not be inherited by this campaign from
     *           the upper level.
     *           Location group types (GMB_DYNAMIC_LOCATION_GROUP,
     *           CHAIN_DYNAMIC_LOCATION_GROUP, and STATIC_LOCATION_GROUP) are child types of
     *           LOCATION_SYNC. Therefore, if LOCATION_SYNC is set for this field, all
     *           location group asset sets are not allowed to be linked to this campaign,
     *           and all Location Extension (LE) and Affiliate Location Extensions (ALE)
     *           will not be served under this campaign.
     *           Only LOCATION_SYNC is currently supported.
     *     @type bool $url_expansion_opt_out
     *           Represents opting out of URL expansion to more targeted URLs. If opted out
     *           (true), only the final URLs in the asset group or URLs specified in the
     *           advertiser's Google Merchant Center or business data feeds are targeted.
     *           If opted in (false), the entire domain will be targeted. This field can
     *           only be set for Performance Max campaigns, where the default value is
     *           false.
     *     @type \Google\Ads\GoogleAds\V16\Resources\Campaign\PerformanceMaxUpgrade $performance_max_upgrade
     *           Output only. Information about campaigns being upgraded to Performance Max.
     *     @type string $hotel_property_asset_set
     *           Immutable. The set of hotel properties for Performance Max for travel goals
     *           campaigns.
     *     @type int $listing_type
     *           Immutable. Listing type of ads served for this campaign.
     *           Field is restricted for usage with Performance Max campaigns.
     *     @type array<\Google\Ads\GoogleAds\V16\Resources\Campaign\AssetAutomationSetting>|\Google\Protobuf\Internal\RepeatedField $asset_automation_settings
     *           Contains the opt-in/out status of each AssetAutomationType.
     *           See documentation of each asset automation type enum for default
     *           opt in/out behavior.
     *     @type string $bidding_strategy
     *           Portfolio bidding strategy used by campaign.
     *     @type \Google\Ads\GoogleAds\V16\Common\Commission $commission
     *           Commission is an automatic bidding strategy in which the advertiser pays
     *           a certain portion of the conversion value.
     *     @type \Google\Ads\GoogleAds\V16\Common\ManualCpa $manual_cpa
     *           Standard Manual CPA bidding strategy.
     *           Manual bidding strategy that allows advertiser to set the bid per
     *           advertiser-specified action. Supported only for Local Services campaigns.
     *     @type \Google\Ads\GoogleAds\V16\Common\ManualCpc $manual_cpc
     *           Standard Manual CPC bidding strategy.
     *           Manual click-based bidding where user pays per click.
     *     @type \Google\Ads\GoogleAds\V16\Common\ManualCpm $manual_cpm
     *           Standard Manual CPM bidding strategy.
     *           Manual impression-based bidding where user pays per thousand
     *           impressions.
     *     @type \Google\Ads\GoogleAds\V16\Common\ManualCpv $manual_cpv
     *           A bidding strategy that pays a configurable amount per video view.
     *     @type \Google\Ads\GoogleAds\V16\Common\MaximizeConversions $maximize_conversions
     *           Standard Maximize Conversions bidding strategy that automatically
     *           maximizes number of conversions while spending your budget.
     *     @type \Google\Ads\GoogleAds\V16\Common\MaximizeConversionValue $maximize_conversion_value
     *           Standard Maximize Conversion Value bidding strategy that automatically
     *           sets bids to maximize revenue while spending your budget.
     *     @type \Google\Ads\GoogleAds\V16\Common\TargetCpa $target_cpa
     *           Standard Target CPA bidding strategy that automatically sets bids to
     *           help get as many conversions as possible at the target
     *           cost-per-acquisition (CPA) you set.
     *     @type \Google\Ads\GoogleAds\V16\Common\TargetImpressionShare $target_impression_share
     *           Target Impression Share bidding strategy. An automated bidding strategy
     *           that sets bids to achieve a chosen percentage of impressions.
     *     @type \Google\Ads\GoogleAds\V16\Common\TargetRoas $target_roas
     *           Standard Target ROAS bidding strategy that automatically maximizes
     *           revenue while averaging a specific target return on ad spend (ROAS).
     *     @type \Google\Ads\GoogleAds\V16\Common\TargetSpend $target_spend
     *           Standard Target Spend bidding strategy that automatically sets your bids
     *           to help get as many clicks as possible within your budget.
     *     @type \Google\Ads\GoogleAds\V16\Common\PercentCpc $percent_cpc
     *           Standard Percent Cpc bidding strategy where bids are a fraction of the
     *           advertised price for some good or service.
     *     @type \Google\Ads\GoogleAds\V16\Common\TargetCpm $target_cpm
     *           A bidding strategy that automatically optimizes cost per thousand
     *           impressions.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V16\Resources\Campaign::initOnce();
        parent::__construct($data);
    }

    /**
     * Immutable. The resource name of the campaign.
     * Campaign resource names have the form:
     * `customers/{customer_id}/campaigns/{campaign_id}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getResourceName()
    {
        return $this->resource_name;
    }

    /**
     * Immutable. The resource name of the campaign.
     * Campaign resource names have the form:
     * `customers/{customer_id}/campaigns/{campaign_id}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setResourceName($var)
    {
        GPBUtil::checkString($var, True);
        $this->resource_name = $var;

        return $this;
    }

    /**
     * Output only. The ID of the campaign.
     *
     * Generated from protobuf field <code>optional int64 id = 59 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int|string
     */
    public function getId()
    {
        return isset($this->id) ? $this->id : 0;
    }

    public function hasId()
    {
        return isset($this->id);
    }

    public function clearId()
    {
        unset($this->id);
    }

    /**
     * Output only. The ID of the campaign.
     *
     * Generated from protobuf field <code>optional int64 id = 59 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int|string $var
     * @return $this
     */
    public function setId($var)
    {
        GPBUtil::checkInt64($var);
        $this->id = $var;

        return $this;
    }

    /**
     * The name of the campaign.
     * This field is required and should not be empty when creating new
     * campaigns.
     * It must not contain any null (code point 0x0), NL line feed
     * (code point 0xA) or carriage return (code point 0xD) characters.
     *
     * Generated from protobuf field <code>optional string name = 58;</code>
     * @return string
     */
    public function getName()
    {
        return isset($this->name) ? $this->name : '';
    }

    public function hasName()
    {
        return isset($this->name);
    }

    public function clearName()
    {
        unset($this->name);
    }

    /**
     * The name of the campaign.
     * This field is required and should not be empty when creating new
     * campaigns.
     * It must not contain any null (code point 0x0), NL line feed
     * (code point 0xA) or carriage return (code point 0xD) characters.
     *
     * Generated from protobuf field <code>optional string name = 58;</code>
     * @param string $var
     * @return $this
     */
    public function setName($var)
    {
        GPBUtil::checkString($var, True);
        $this->name = $var;

        return $this;
    }

    /**
     * Output only. The primary status of the campaign.
     * Provides insight into why a campaign is not serving or not serving
     * optimally. Modification to the campaign and its related entities might take
     * a while to be reflected in this status.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.CampaignPrimaryStatusEnum.CampaignPrimaryStatus primary_status = 81 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getPrimaryStatus()
    {
        return $this->primary_status;
    }

    /**
     * Output only. The primary status of the campaign.
     * Provides insight into why a campaign is not serving or not serving
     * optimally. Modification to the campaign and its related entities might take
     * a while to be reflected in this status.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.CampaignPrimaryStatusEnum.CampaignPrimaryStatus primary_status = 81 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setPrimaryStatus($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\CampaignPrimaryStatusEnum\CampaignPrimaryStatus::class);
        $this->primary_status = $var;

        return $this;
    }

    /**
     * Output only. The primary status reasons of the campaign.
     * Provides insight into why a campaign is not serving or not serving
     * optimally. These reasons are aggregated to determine an overall
     * CampaignPrimaryStatus.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v16.enums.CampaignPrimaryStatusReasonEnum.CampaignPrimaryStatusReason primary_status_reasons = 82 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getPrimaryStatusReasons()
    {
        return $this->primary_status_reasons;
    }

    /**
     * Output only. The primary status reasons of the campaign.
     * Provides insight into why a campaign is not serving or not serving
     * optimally. These reasons are aggregated to determine an overall
     * CampaignPrimaryStatus.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v16.enums.CampaignPrimaryStatusReasonEnum.CampaignPrimaryStatusReason primary_status_reasons = 82 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param array<int>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setPrimaryStatusReasons($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::ENUM, \Google\Ads\GoogleAds\V16\Enums\CampaignPrimaryStatusReasonEnum\CampaignPrimaryStatusReason::class);
        $this->primary_status_reasons = $arr;

        return $this;
    }

    /**
     * The status of the campaign.
     * When a new campaign is added, the status defaults to ENABLED.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.CampaignStatusEnum.CampaignStatus status = 5;</code>
     * @return int
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * The status of the campaign.
     * When a new campaign is added, the status defaults to ENABLED.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.CampaignStatusEnum.CampaignStatus status = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setStatus($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\CampaignStatusEnum\CampaignStatus::class);
        $this->status = $var;

        return $this;
    }

    /**
     * Output only. The ad serving status of the campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.CampaignServingStatusEnum.CampaignServingStatus serving_status = 21 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getServingStatus()
    {
        return $this->serving_status;
    }

    /**
     * Output only. The ad serving status of the campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.CampaignServingStatusEnum.CampaignServingStatus serving_status = 21 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setServingStatus($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\CampaignServingStatusEnum\CampaignServingStatus::class);
        $this->serving_status = $var;

        return $this;
    }

    /**
     * Output only. The system status of the campaign's bidding strategy.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.BiddingStrategySystemStatusEnum.BiddingStrategySystemStatus bidding_strategy_system_status = 78 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getBiddingStrategySystemStatus()
    {
        return $this->bidding_strategy_system_status;
    }

    /**
     * Output only. The system status of the campaign's bidding strategy.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.BiddingStrategySystemStatusEnum.BiddingStrategySystemStatus bidding_strategy_system_status = 78 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setBiddingStrategySystemStatus($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\BiddingStrategySystemStatusEnum\BiddingStrategySystemStatus::class);
        $this->bidding_strategy_system_status = $var;

        return $this;
    }

    /**
     * The ad serving optimization status of the campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.AdServingOptimizationStatusEnum.AdServingOptimizationStatus ad_serving_optimization_status = 8;</code>
     * @return int
     */
    public function getAdServingOptimizationStatus()
    {
        return $this->ad_serving_optimization_status;
    }

    /**
     * The ad serving optimization status of the campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.AdServingOptimizationStatusEnum.AdServingOptimizationStatus ad_serving_optimization_status = 8;</code>
     * @param int $var
     * @return $this
     */
    public function setAdServingOptimizationStatus($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\AdServingOptimizationStatusEnum\AdServingOptimizationStatus::class);
        $this->ad_serving_optimization_status = $var;

        return $this;
    }

    /**
     * Immutable. The primary serving target for ads within the campaign.
     * The targeting options can be refined in `network_settings`.
     * This field is required and should not be empty when creating new
     * campaigns.
     * Can be set only when creating campaigns.
     * After the campaign is created, the field can not be changed.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.AdvertisingChannelTypeEnum.AdvertisingChannelType advertising_channel_type = 9 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return int
     */
    public function getAdvertisingChannelType()
    {
        return $this->advertising_channel_type;
    }

    /**
     * Immutable. The primary serving target for ads within the campaign.
     * The targeting options can be refined in `network_settings`.
     * This field is required and should not be empty when creating new
     * campaigns.
     * Can be set only when creating campaigns.
     * After the campaign is created, the field can not be changed.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.AdvertisingChannelTypeEnum.AdvertisingChannelType advertising_channel_type = 9 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param int $var
     * @return $this
     */
    public function setAdvertisingChannelType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\AdvertisingChannelTypeEnum\AdvertisingChannelType::class);
        $this->advertising_channel_type = $var;

        return $this;
    }

    /**
     * Immutable. Optional refinement to `advertising_channel_type`.
     * Must be a valid sub-type of the parent channel type.
     * Can be set only when creating campaigns.
     * After campaign is created, the field can not be changed.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.AdvertisingChannelSubTypeEnum.AdvertisingChannelSubType advertising_channel_sub_type = 10 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return int
     */
    public function getAdvertisingChannelSubType()
    {
        return $this->advertising_channel_sub_type;
    }

    /**
     * Immutable. Optional refinement to `advertising_channel_type`.
     * Must be a valid sub-type of the parent channel type.
     * Can be set only when creating campaigns.
     * After campaign is created, the field can not be changed.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.AdvertisingChannelSubTypeEnum.AdvertisingChannelSubType advertising_channel_sub_type = 10 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param int $var
     * @return $this
     */
    public function setAdvertisingChannelSubType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\AdvertisingChannelSubTypeEnum\AdvertisingChannelSubType::class);
        $this->advertising_channel_sub_type = $var;

        return $this;
    }

    /**
     * The URL template for constructing a tracking URL.
     *
     * Generated from protobuf field <code>optional string tracking_url_template = 60;</code>
     * @return string
     */
    public function getTrackingUrlTemplate()
    {
        return isset($this->tracking_url_template) ? $this->tracking_url_template : '';
    }

    public function hasTrackingUrlTemplate()
    {
        return isset($this->tracking_url_template);
    }

    public function clearTrackingUrlTemplate()
    {
        unset($this->tracking_url_template);
    }

    /**
     * The URL template for constructing a tracking URL.
     *
     * Generated from protobuf field <code>optional string tracking_url_template = 60;</code>
     * @param string $var
     * @return $this
     */
    public function setTrackingUrlTemplate($var)
    {
        GPBUtil::checkString($var, True);
        $this->tracking_url_template = $var;

        return $this;
    }

    /**
     * The list of mappings used to substitute custom parameter tags in a
     * `tracking_url_template`, `final_urls`, or `mobile_final_urls`.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v16.common.CustomParameter url_custom_parameters = 12;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getUrlCustomParameters()
    {
        return $this->url_custom_parameters;
    }

    /**
     * The list of mappings used to substitute custom parameter tags in a
     * `tracking_url_template`, `final_urls`, or `mobile_final_urls`.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v16.common.CustomParameter url_custom_parameters = 12;</code>
     * @param array<\Google\Ads\GoogleAds\V16\Common\CustomParameter>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setUrlCustomParameters($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Ads\GoogleAds\V16\Common\CustomParameter::class);
        $this->url_custom_parameters = $arr;

        return $this;
    }

    /**
     * The Local Services Campaign related settings.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.LocalServicesCampaignSettings local_services_campaign_settings = 75;</code>
     * @return \Google\Ads\GoogleAds\V16\Resources\Campaign\LocalServicesCampaignSettings|null
     */
    public function getLocalServicesCampaignSettings()
    {
        return $this->local_services_campaign_settings;
    }

    public function hasLocalServicesCampaignSettings()
    {
        return isset($this->local_services_campaign_settings);
    }

    public function clearLocalServicesCampaignSettings()
    {
        unset($this->local_services_campaign_settings);
    }

    /**
     * The Local Services Campaign related settings.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.LocalServicesCampaignSettings local_services_campaign_settings = 75;</code>
     * @param \Google\Ads\GoogleAds\V16\Resources\Campaign\LocalServicesCampaignSettings $var
     * @return $this
     */
    public function setLocalServicesCampaignSettings($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Resources\Campaign\LocalServicesCampaignSettings::class);
        $this->local_services_campaign_settings = $var;

        return $this;
    }

    /**
     * Settings for Travel campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.TravelCampaignSettings travel_campaign_settings = 85;</code>
     * @return \Google\Ads\GoogleAds\V16\Resources\Campaign\TravelCampaignSettings|null
     */
    public function getTravelCampaignSettings()
    {
        return $this->travel_campaign_settings;
    }

    public function hasTravelCampaignSettings()
    {
        return isset($this->travel_campaign_settings);
    }

    public function clearTravelCampaignSettings()
    {
        unset($this->travel_campaign_settings);
    }

    /**
     * Settings for Travel campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.TravelCampaignSettings travel_campaign_settings = 85;</code>
     * @param \Google\Ads\GoogleAds\V16\Resources\Campaign\TravelCampaignSettings $var
     * @return $this
     */
    public function setTravelCampaignSettings($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Resources\Campaign\TravelCampaignSettings::class);
        $this->travel_campaign_settings = $var;

        return $this;
    }

    /**
     * Settings for Discovery campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.DiscoveryCampaignSettings discovery_campaign_settings = 87;</code>
     * @return \Google\Ads\GoogleAds\V16\Resources\Campaign\DiscoveryCampaignSettings|null
     */
    public function getDiscoveryCampaignSettings()
    {
        return $this->discovery_campaign_settings;
    }

    public function hasDiscoveryCampaignSettings()
    {
        return isset($this->discovery_campaign_settings);
    }

    public function clearDiscoveryCampaignSettings()
    {
        unset($this->discovery_campaign_settings);
    }

    /**
     * Settings for Discovery campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.DiscoveryCampaignSettings discovery_campaign_settings = 87;</code>
     * @param \Google\Ads\GoogleAds\V16\Resources\Campaign\DiscoveryCampaignSettings $var
     * @return $this
     */
    public function setDiscoveryCampaignSettings($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Resources\Campaign\DiscoveryCampaignSettings::class);
        $this->discovery_campaign_settings = $var;

        return $this;
    }

    /**
     * Settings for Real-Time Bidding, a feature only available for campaigns
     * targeting the Ad Exchange network.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.RealTimeBiddingSetting real_time_bidding_setting = 39;</code>
     * @return \Google\Ads\GoogleAds\V16\Common\RealTimeBiddingSetting|null
     */
    public function getRealTimeBiddingSetting()
    {
        return $this->real_time_bidding_setting;
    }

    public function hasRealTimeBiddingSetting()
    {
        return isset($this->real_time_bidding_setting);
    }

    public function clearRealTimeBiddingSetting()
    {
        unset($this->real_time_bidding_setting);
    }

    /**
     * Settings for Real-Time Bidding, a feature only available for campaigns
     * targeting the Ad Exchange network.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.RealTimeBiddingSetting real_time_bidding_setting = 39;</code>
     * @param \Google\Ads\GoogleAds\V16\Common\RealTimeBiddingSetting $var
     * @return $this
     */
    public function setRealTimeBiddingSetting($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Common\RealTimeBiddingSetting::class);
        $this->real_time_bidding_setting = $var;

        return $this;
    }

    /**
     * The network settings for the campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.NetworkSettings network_settings = 14;</code>
     * @return \Google\Ads\GoogleAds\V16\Resources\Campaign\NetworkSettings|null
     */
    public function getNetworkSettings()
    {
        return $this->network_settings;
    }

    public function hasNetworkSettings()
    {
        return isset($this->network_settings);
    }

    public function clearNetworkSettings()
    {
        unset($this->network_settings);
    }

    /**
     * The network settings for the campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.NetworkSettings network_settings = 14;</code>
     * @param \Google\Ads\GoogleAds\V16\Resources\Campaign\NetworkSettings $var
     * @return $this
     */
    public function setNetworkSettings($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Resources\Campaign\NetworkSettings::class);
        $this->network_settings = $var;

        return $this;
    }

    /**
     * Immutable. The hotel setting for the campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.HotelSettingInfo hotel_setting = 32 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V16\Resources\Campaign\HotelSettingInfo|null
     */
    public function getHotelSetting()
    {
        return $this->hotel_setting;
    }

    public function hasHotelSetting()
    {
        return isset($this->hotel_setting);
    }

    public function clearHotelSetting()
    {
        unset($this->hotel_setting);
    }

    /**
     * Immutable. The hotel setting for the campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.HotelSettingInfo hotel_setting = 32 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V16\Resources\Campaign\HotelSettingInfo $var
     * @return $this
     */
    public function setHotelSetting($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Resources\Campaign\HotelSettingInfo::class);
        $this->hotel_setting = $var;

        return $this;
    }

    /**
     * The setting for controlling Dynamic Search Ads (DSA).
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.DynamicSearchAdsSetting dynamic_search_ads_setting = 33;</code>
     * @return \Google\Ads\GoogleAds\V16\Resources\Campaign\DynamicSearchAdsSetting|null
     */
    public function getDynamicSearchAdsSetting()
    {
        return $this->dynamic_search_ads_setting;
    }

    public function hasDynamicSearchAdsSetting()
    {
        return isset($this->dynamic_search_ads_setting);
    }

    public function clearDynamicSearchAdsSetting()
    {
        unset($this->dynamic_search_ads_setting);
    }

    /**
     * The setting for controlling Dynamic Search Ads (DSA).
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.DynamicSearchAdsSetting dynamic_search_ads_setting = 33;</code>
     * @param \Google\Ads\GoogleAds\V16\Resources\Campaign\DynamicSearchAdsSetting $var
     * @return $this
     */
    public function setDynamicSearchAdsSetting($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Resources\Campaign\DynamicSearchAdsSetting::class);
        $this->dynamic_search_ads_setting = $var;

        return $this;
    }

    /**
     * The setting for controlling Shopping campaigns.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.ShoppingSetting shopping_setting = 36;</code>
     * @return \Google\Ads\GoogleAds\V16\Resources\Campaign\ShoppingSetting|null
     */
    public function getShoppingSetting()
    {
        return $this->shopping_setting;
    }

    public function hasShoppingSetting()
    {
        return isset($this->shopping_setting);
    }

    public function clearShoppingSetting()
    {
        unset($this->shopping_setting);
    }

    /**
     * The setting for controlling Shopping campaigns.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.ShoppingSetting shopping_setting = 36;</code>
     * @param \Google\Ads\GoogleAds\V16\Resources\Campaign\ShoppingSetting $var
     * @return $this
     */
    public function setShoppingSetting($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Resources\Campaign\ShoppingSetting::class);
        $this->shopping_setting = $var;

        return $this;
    }

    /**
     * Setting for targeting related features.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.TargetingSetting targeting_setting = 43;</code>
     * @return \Google\Ads\GoogleAds\V16\Common\TargetingSetting|null
     */
    public function getTargetingSetting()
    {
        return $this->targeting_setting;
    }

    public function hasTargetingSetting()
    {
        return isset($this->targeting_setting);
    }

    public function clearTargetingSetting()
    {
        unset($this->targeting_setting);
    }

    /**
     * Setting for targeting related features.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.TargetingSetting targeting_setting = 43;</code>
     * @param \Google\Ads\GoogleAds\V16\Common\TargetingSetting $var
     * @return $this
     */
    public function setTargetingSetting($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Common\TargetingSetting::class);
        $this->targeting_setting = $var;

        return $this;
    }

    /**
     * Immutable. Setting for audience related features.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v16.resources.Campaign.AudienceSetting audience_setting = 73 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return \Google\Ads\GoogleAds\V16\Resources\Campaign\AudienceSetting|null
     */
    public function getAudienceSetting()
    {
        return $this->audience_setting;
    }

    public function hasAudienceSetting()
    {
        return isset($this->audience_setting);
    }

    public function clearAudienceSetting()
    {
        unset($this->audience_setting);
    }

    /**
     * Immutable. Setting for audience related features.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v16.resources.Campaign.AudienceSetting audience_setting = 73 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param \Google\Ads\GoogleAds\V16\Resources\Campaign\AudienceSetting $var
     * @return $this
     */
    public function setAudienceSetting($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Resources\Campaign\AudienceSetting::class);
        $this->audience_setting = $var;

        return $this;
    }

    /**
     * The setting for ads geotargeting.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.GeoTargetTypeSetting geo_target_type_setting = 47;</code>
     * @return \Google\Ads\GoogleAds\V16\Resources\Campaign\GeoTargetTypeSetting|null
     */
    public function getGeoTargetTypeSetting()
    {
        return $this->geo_target_type_setting;
    }

    public function hasGeoTargetTypeSetting()
    {
        return isset($this->geo_target_type_setting);
    }

    public function clearGeoTargetTypeSetting()
    {
        unset($this->geo_target_type_setting);
    }

    /**
     * The setting for ads geotargeting.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.GeoTargetTypeSetting geo_target_type_setting = 47;</code>
     * @param \Google\Ads\GoogleAds\V16\Resources\Campaign\GeoTargetTypeSetting $var
     * @return $this
     */
    public function setGeoTargetTypeSetting($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Resources\Campaign\GeoTargetTypeSetting::class);
        $this->geo_target_type_setting = $var;

        return $this;
    }

    /**
     * The setting for local campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.LocalCampaignSetting local_campaign_setting = 50;</code>
     * @return \Google\Ads\GoogleAds\V16\Resources\Campaign\LocalCampaignSetting|null
     */
    public function getLocalCampaignSetting()
    {
        return $this->local_campaign_setting;
    }

    public function hasLocalCampaignSetting()
    {
        return isset($this->local_campaign_setting);
    }

    public function clearLocalCampaignSetting()
    {
        unset($this->local_campaign_setting);
    }

    /**
     * The setting for local campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.LocalCampaignSetting local_campaign_setting = 50;</code>
     * @param \Google\Ads\GoogleAds\V16\Resources\Campaign\LocalCampaignSetting $var
     * @return $this
     */
    public function setLocalCampaignSetting($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Resources\Campaign\LocalCampaignSetting::class);
        $this->local_campaign_setting = $var;

        return $this;
    }

    /**
     * The setting related to App Campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.AppCampaignSetting app_campaign_setting = 51;</code>
     * @return \Google\Ads\GoogleAds\V16\Resources\Campaign\AppCampaignSetting|null
     */
    public function getAppCampaignSetting()
    {
        return $this->app_campaign_setting;
    }

    public function hasAppCampaignSetting()
    {
        return isset($this->app_campaign_setting);
    }

    public function clearAppCampaignSetting()
    {
        unset($this->app_campaign_setting);
    }

    /**
     * The setting related to App Campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.AppCampaignSetting app_campaign_setting = 51;</code>
     * @param \Google\Ads\GoogleAds\V16\Resources\Campaign\AppCampaignSetting $var
     * @return $this
     */
    public function setAppCampaignSetting($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Resources\Campaign\AppCampaignSetting::class);
        $this->app_campaign_setting = $var;

        return $this;
    }

    /**
     * Output only. The resource names of labels attached to this campaign.
     *
     * Generated from protobuf field <code>repeated string labels = 61 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getLabels()
    {
        return $this->labels;
    }

    /**
     * Output only. The resource names of labels attached to this campaign.
     *
     * Generated from protobuf field <code>repeated string labels = 61 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setLabels($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->labels = $arr;

        return $this;
    }

    /**
     * Output only. The type of campaign: normal, draft, or experiment.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.CampaignExperimentTypeEnum.CampaignExperimentType experiment_type = 17 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getExperimentType()
    {
        return $this->experiment_type;
    }

    /**
     * Output only. The type of campaign: normal, draft, or experiment.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.CampaignExperimentTypeEnum.CampaignExperimentType experiment_type = 17 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setExperimentType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\CampaignExperimentTypeEnum\CampaignExperimentType::class);
        $this->experiment_type = $var;

        return $this;
    }

    /**
     * Output only. The resource name of the base campaign of a draft or
     * experiment campaign. For base campaigns, this is equal to `resource_name`.
     * This field is read-only.
     *
     * Generated from protobuf field <code>optional string base_campaign = 56 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getBaseCampaign()
    {
        return isset($this->base_campaign) ? $this->base_campaign : '';
    }

    public function hasBaseCampaign()
    {
        return isset($this->base_campaign);
    }

    public function clearBaseCampaign()
    {
        unset($this->base_campaign);
    }

    /**
     * Output only. The resource name of the base campaign of a draft or
     * experiment campaign. For base campaigns, this is equal to `resource_name`.
     * This field is read-only.
     *
     * Generated from protobuf field <code>optional string base_campaign = 56 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setBaseCampaign($var)
    {
        GPBUtil::checkString($var, True);
        $this->base_campaign = $var;

        return $this;
    }

    /**
     * The budget of the campaign.
     *
     * Generated from protobuf field <code>optional string campaign_budget = 62 [(.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getCampaignBudget()
    {
        return isset($this->campaign_budget) ? $this->campaign_budget : '';
    }

    public function hasCampaignBudget()
    {
        return isset($this->campaign_budget);
    }

    public function clearCampaignBudget()
    {
        unset($this->campaign_budget);
    }

    /**
     * The budget of the campaign.
     *
     * Generated from protobuf field <code>optional string campaign_budget = 62 [(.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setCampaignBudget($var)
    {
        GPBUtil::checkString($var, True);
        $this->campaign_budget = $var;

        return $this;
    }

    /**
     * Output only. The type of bidding strategy.
     * A bidding strategy can be created by setting either the bidding scheme to
     * create a standard bidding strategy or the `bidding_strategy` field to
     * create a portfolio bidding strategy.
     * This field is read-only.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.BiddingStrategyTypeEnum.BiddingStrategyType bidding_strategy_type = 22 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getBiddingStrategyType()
    {
        return $this->bidding_strategy_type;
    }

    /**
     * Output only. The type of bidding strategy.
     * A bidding strategy can be created by setting either the bidding scheme to
     * create a standard bidding strategy or the `bidding_strategy` field to
     * create a portfolio bidding strategy.
     * This field is read-only.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.BiddingStrategyTypeEnum.BiddingStrategyType bidding_strategy_type = 22 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setBiddingStrategyType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\BiddingStrategyTypeEnum\BiddingStrategyType::class);
        $this->bidding_strategy_type = $var;

        return $this;
    }

    /**
     * Output only. Resource name of AccessibleBiddingStrategy, a read-only view
     * of the unrestricted attributes of the attached portfolio bidding strategy
     * identified by 'bidding_strategy'. Empty, if the campaign does not use a
     * portfolio strategy. Unrestricted strategy attributes are available to all
     * customers with whom the strategy is shared and are read from the
     * AccessibleBiddingStrategy resource. In contrast, restricted attributes are
     * only available to the owner customer of the strategy and their managers.
     * Restricted attributes can only be read from the BiddingStrategy resource.
     *
     * Generated from protobuf field <code>string accessible_bidding_strategy = 71 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getAccessibleBiddingStrategy()
    {
        return $this->accessible_bidding_strategy;
    }

    /**
     * Output only. Resource name of AccessibleBiddingStrategy, a read-only view
     * of the unrestricted attributes of the attached portfolio bidding strategy
     * identified by 'bidding_strategy'. Empty, if the campaign does not use a
     * portfolio strategy. Unrestricted strategy attributes are available to all
     * customers with whom the strategy is shared and are read from the
     * AccessibleBiddingStrategy resource. In contrast, restricted attributes are
     * only available to the owner customer of the strategy and their managers.
     * Restricted attributes can only be read from the BiddingStrategy resource.
     *
     * Generated from protobuf field <code>string accessible_bidding_strategy = 71 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setAccessibleBiddingStrategy($var)
    {
        GPBUtil::checkString($var, True);
        $this->accessible_bidding_strategy = $var;

        return $this;
    }

    /**
     * The date when campaign started in serving customer's timezone in YYYY-MM-DD
     * format.
     *
     * Generated from protobuf field <code>optional string start_date = 63;</code>
     * @return string
     */
    public function getStartDate()
    {
        return isset($this->start_date) ? $this->start_date : '';
    }

    public function hasStartDate()
    {
        return isset($this->start_date);
    }

    public function clearStartDate()
    {
        unset($this->start_date);
    }

    /**
     * The date when campaign started in serving customer's timezone in YYYY-MM-DD
     * format.
     *
     * Generated from protobuf field <code>optional string start_date = 63;</code>
     * @param string $var
     * @return $this
     */
    public function setStartDate($var)
    {
        GPBUtil::checkString($var, True);
        $this->start_date = $var;

        return $this;
    }

    /**
     * The campaign group this campaign belongs to.
     *
     * Generated from protobuf field <code>optional string campaign_group = 76 [(.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getCampaignGroup()
    {
        return isset($this->campaign_group) ? $this->campaign_group : '';
    }

    public function hasCampaignGroup()
    {
        return isset($this->campaign_group);
    }

    public function clearCampaignGroup()
    {
        unset($this->campaign_group);
    }

    /**
     * The campaign group this campaign belongs to.
     *
     * Generated from protobuf field <code>optional string campaign_group = 76 [(.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setCampaignGroup($var)
    {
        GPBUtil::checkString($var, True);
        $this->campaign_group = $var;

        return $this;
    }

    /**
     * The last day of the campaign in serving customer's timezone in YYYY-MM-DD
     * format. On create, defaults to 2037-12-30, which means the campaign will
     * run indefinitely. To set an existing campaign to run indefinitely, set this
     * field to 2037-12-30.
     *
     * Generated from protobuf field <code>optional string end_date = 64;</code>
     * @return string
     */
    public function getEndDate()
    {
        return isset($this->end_date) ? $this->end_date : '';
    }

    public function hasEndDate()
    {
        return isset($this->end_date);
    }

    public function clearEndDate()
    {
        unset($this->end_date);
    }

    /**
     * The last day of the campaign in serving customer's timezone in YYYY-MM-DD
     * format. On create, defaults to 2037-12-30, which means the campaign will
     * run indefinitely. To set an existing campaign to run indefinitely, set this
     * field to 2037-12-30.
     *
     * Generated from protobuf field <code>optional string end_date = 64;</code>
     * @param string $var
     * @return $this
     */
    public function setEndDate($var)
    {
        GPBUtil::checkString($var, True);
        $this->end_date = $var;

        return $this;
    }

    /**
     * Suffix used to append query parameters to landing pages that are served
     * with parallel tracking.
     *
     * Generated from protobuf field <code>optional string final_url_suffix = 65;</code>
     * @return string
     */
    public function getFinalUrlSuffix()
    {
        return isset($this->final_url_suffix) ? $this->final_url_suffix : '';
    }

    public function hasFinalUrlSuffix()
    {
        return isset($this->final_url_suffix);
    }

    public function clearFinalUrlSuffix()
    {
        unset($this->final_url_suffix);
    }

    /**
     * Suffix used to append query parameters to landing pages that are served
     * with parallel tracking.
     *
     * Generated from protobuf field <code>optional string final_url_suffix = 65;</code>
     * @param string $var
     * @return $this
     */
    public function setFinalUrlSuffix($var)
    {
        GPBUtil::checkString($var, True);
        $this->final_url_suffix = $var;

        return $this;
    }

    /**
     * A list that limits how often each user will see this campaign's ads.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v16.common.FrequencyCapEntry frequency_caps = 40;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getFrequencyCaps()
    {
        return $this->frequency_caps;
    }

    /**
     * A list that limits how often each user will see this campaign's ads.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v16.common.FrequencyCapEntry frequency_caps = 40;</code>
     * @param array<\Google\Ads\GoogleAds\V16\Common\FrequencyCapEntry>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFrequencyCaps($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Ads\GoogleAds\V16\Common\FrequencyCapEntry::class);
        $this->frequency_caps = $arr;

        return $this;
    }

    /**
     * Output only. Brand Safety setting at the individual campaign level. Allows
     * for selecting an inventory type to show your ads on content that is the
     * right fit for your brand. See
     * https://support.google.com/google-ads/answer/7515513.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.BrandSafetySuitabilityEnum.BrandSafetySuitability video_brand_safety_suitability = 42 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getVideoBrandSafetySuitability()
    {
        return $this->video_brand_safety_suitability;
    }

    /**
     * Output only. Brand Safety setting at the individual campaign level. Allows
     * for selecting an inventory type to show your ads on content that is the
     * right fit for your brand. See
     * https://support.google.com/google-ads/answer/7515513.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.BrandSafetySuitabilityEnum.BrandSafetySuitability video_brand_safety_suitability = 42 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setVideoBrandSafetySuitability($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\BrandSafetySuitabilityEnum\BrandSafetySuitability::class);
        $this->video_brand_safety_suitability = $var;

        return $this;
    }

    /**
     * Describes how unbranded pharma ads will be displayed.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.VanityPharma vanity_pharma = 44;</code>
     * @return \Google\Ads\GoogleAds\V16\Resources\Campaign\VanityPharma|null
     */
    public function getVanityPharma()
    {
        return $this->vanity_pharma;
    }

    public function hasVanityPharma()
    {
        return isset($this->vanity_pharma);
    }

    public function clearVanityPharma()
    {
        unset($this->vanity_pharma);
    }

    /**
     * Describes how unbranded pharma ads will be displayed.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.VanityPharma vanity_pharma = 44;</code>
     * @param \Google\Ads\GoogleAds\V16\Resources\Campaign\VanityPharma $var
     * @return $this
     */
    public function setVanityPharma($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Resources\Campaign\VanityPharma::class);
        $this->vanity_pharma = $var;

        return $this;
    }

    /**
     * Selective optimization setting for this campaign, which includes a set of
     * conversion actions to optimize this campaign towards.
     * This feature only applies to app campaigns that use MULTI_CHANNEL as
     * AdvertisingChannelType and APP_CAMPAIGN or APP_CAMPAIGN_FOR_ENGAGEMENT as
     * AdvertisingChannelSubType.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.SelectiveOptimization selective_optimization = 45;</code>
     * @return \Google\Ads\GoogleAds\V16\Resources\Campaign\SelectiveOptimization|null
     */
    public function getSelectiveOptimization()
    {
        return $this->selective_optimization;
    }

    public function hasSelectiveOptimization()
    {
        return isset($this->selective_optimization);
    }

    public function clearSelectiveOptimization()
    {
        unset($this->selective_optimization);
    }

    /**
     * Selective optimization setting for this campaign, which includes a set of
     * conversion actions to optimize this campaign towards.
     * This feature only applies to app campaigns that use MULTI_CHANNEL as
     * AdvertisingChannelType and APP_CAMPAIGN or APP_CAMPAIGN_FOR_ENGAGEMENT as
     * AdvertisingChannelSubType.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.SelectiveOptimization selective_optimization = 45;</code>
     * @param \Google\Ads\GoogleAds\V16\Resources\Campaign\SelectiveOptimization $var
     * @return $this
     */
    public function setSelectiveOptimization($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Resources\Campaign\SelectiveOptimization::class);
        $this->selective_optimization = $var;

        return $this;
    }

    /**
     * Optimization goal setting for this campaign, which includes a set of
     * optimization goal types.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.OptimizationGoalSetting optimization_goal_setting = 54;</code>
     * @return \Google\Ads\GoogleAds\V16\Resources\Campaign\OptimizationGoalSetting|null
     */
    public function getOptimizationGoalSetting()
    {
        return $this->optimization_goal_setting;
    }

    public function hasOptimizationGoalSetting()
    {
        return isset($this->optimization_goal_setting);
    }

    public function clearOptimizationGoalSetting()
    {
        unset($this->optimization_goal_setting);
    }

    /**
     * Optimization goal setting for this campaign, which includes a set of
     * optimization goal types.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.OptimizationGoalSetting optimization_goal_setting = 54;</code>
     * @param \Google\Ads\GoogleAds\V16\Resources\Campaign\OptimizationGoalSetting $var
     * @return $this
     */
    public function setOptimizationGoalSetting($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Resources\Campaign\OptimizationGoalSetting::class);
        $this->optimization_goal_setting = $var;

        return $this;
    }

    /**
     * Output only. Campaign-level settings for tracking information.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.TrackingSetting tracking_setting = 46 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V16\Resources\Campaign\TrackingSetting|null
     */
    public function getTrackingSetting()
    {
        return $this->tracking_setting;
    }

    public function hasTrackingSetting()
    {
        return isset($this->tracking_setting);
    }

    public function clearTrackingSetting()
    {
        unset($this->tracking_setting);
    }

    /**
     * Output only. Campaign-level settings for tracking information.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.TrackingSetting tracking_setting = 46 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V16\Resources\Campaign\TrackingSetting $var
     * @return $this
     */
    public function setTrackingSetting($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Resources\Campaign\TrackingSetting::class);
        $this->tracking_setting = $var;

        return $this;
    }

    /**
     * Payment mode for the campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.PaymentModeEnum.PaymentMode payment_mode = 52;</code>
     * @return int
     */
    public function getPaymentMode()
    {
        return $this->payment_mode;
    }

    /**
     * Payment mode for the campaign.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.PaymentModeEnum.PaymentMode payment_mode = 52;</code>
     * @param int $var
     * @return $this
     */
    public function setPaymentMode($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\PaymentModeEnum\PaymentMode::class);
        $this->payment_mode = $var;

        return $this;
    }

    /**
     * Output only. Optimization score of the campaign.
     * Optimization score is an estimate of how well a campaign is set to perform.
     * It ranges from 0% (0.0) to 100% (1.0), with 100% indicating that the
     * campaign is performing at full potential. This field is null for unscored
     * campaigns.
     * See "About optimization score" at
     * https://support.google.com/google-ads/answer/9061546.
     * This field is read-only.
     *
     * Generated from protobuf field <code>optional double optimization_score = 66 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return float
     */
    public function getOptimizationScore()
    {
        return isset($this->optimization_score) ? $this->optimization_score : 0.0;
    }

    public function hasOptimizationScore()
    {
        return isset($this->optimization_score);
    }

    public function clearOptimizationScore()
    {
        unset($this->optimization_score);
    }

    /**
     * Output only. Optimization score of the campaign.
     * Optimization score is an estimate of how well a campaign is set to perform.
     * It ranges from 0% (0.0) to 100% (1.0), with 100% indicating that the
     * campaign is performing at full potential. This field is null for unscored
     * campaigns.
     * See "About optimization score" at
     * https://support.google.com/google-ads/answer/9061546.
     * This field is read-only.
     *
     * Generated from protobuf field <code>optional double optimization_score = 66 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param float $var
     * @return $this
     */
    public function setOptimizationScore($var)
    {
        GPBUtil::checkDouble($var);
        $this->optimization_score = $var;

        return $this;
    }

    /**
     * The asset field types that should be excluded from this campaign. Asset
     * links with these field types will not be inherited by this campaign from
     * the upper level.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v16.enums.AssetFieldTypeEnum.AssetFieldType excluded_parent_asset_field_types = 69;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getExcludedParentAssetFieldTypes()
    {
        return $this->excluded_parent_asset_field_types;
    }

    /**
     * The asset field types that should be excluded from this campaign. Asset
     * links with these field types will not be inherited by this campaign from
     * the upper level.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v16.enums.AssetFieldTypeEnum.AssetFieldType excluded_parent_asset_field_types = 69;</code>
     * @param array<int>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setExcludedParentAssetFieldTypes($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::ENUM, \Google\Ads\GoogleAds\V16\Enums\AssetFieldTypeEnum\AssetFieldType::class);
        $this->excluded_parent_asset_field_types = $arr;

        return $this;
    }

    /**
     * The asset set types that should be excluded from this campaign. Asset set
     * links with these types will not be inherited by this campaign from
     * the upper level.
     * Location group types (GMB_DYNAMIC_LOCATION_GROUP,
     * CHAIN_DYNAMIC_LOCATION_GROUP, and STATIC_LOCATION_GROUP) are child types of
     * LOCATION_SYNC. Therefore, if LOCATION_SYNC is set for this field, all
     * location group asset sets are not allowed to be linked to this campaign,
     * and all Location Extension (LE) and Affiliate Location Extensions (ALE)
     * will not be served under this campaign.
     * Only LOCATION_SYNC is currently supported.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v16.enums.AssetSetTypeEnum.AssetSetType excluded_parent_asset_set_types = 80;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getExcludedParentAssetSetTypes()
    {
        return $this->excluded_parent_asset_set_types;
    }

    /**
     * The asset set types that should be excluded from this campaign. Asset set
     * links with these types will not be inherited by this campaign from
     * the upper level.
     * Location group types (GMB_DYNAMIC_LOCATION_GROUP,
     * CHAIN_DYNAMIC_LOCATION_GROUP, and STATIC_LOCATION_GROUP) are child types of
     * LOCATION_SYNC. Therefore, if LOCATION_SYNC is set for this field, all
     * location group asset sets are not allowed to be linked to this campaign,
     * and all Location Extension (LE) and Affiliate Location Extensions (ALE)
     * will not be served under this campaign.
     * Only LOCATION_SYNC is currently supported.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v16.enums.AssetSetTypeEnum.AssetSetType excluded_parent_asset_set_types = 80;</code>
     * @param array<int>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setExcludedParentAssetSetTypes($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::ENUM, \Google\Ads\GoogleAds\V16\Enums\AssetSetTypeEnum\AssetSetType::class);
        $this->excluded_parent_asset_set_types = $arr;

        return $this;
    }

    /**
     * Represents opting out of URL expansion to more targeted URLs. If opted out
     * (true), only the final URLs in the asset group or URLs specified in the
     * advertiser's Google Merchant Center or business data feeds are targeted.
     * If opted in (false), the entire domain will be targeted. This field can
     * only be set for Performance Max campaigns, where the default value is
     * false.
     *
     * Generated from protobuf field <code>optional bool url_expansion_opt_out = 72;</code>
     * @return bool
     */
    public function getUrlExpansionOptOut()
    {
        return isset($this->url_expansion_opt_out) ? $this->url_expansion_opt_out : false;
    }

    public function hasUrlExpansionOptOut()
    {
        return isset($this->url_expansion_opt_out);
    }

    public function clearUrlExpansionOptOut()
    {
        unset($this->url_expansion_opt_out);
    }

    /**
     * Represents opting out of URL expansion to more targeted URLs. If opted out
     * (true), only the final URLs in the asset group or URLs specified in the
     * advertiser's Google Merchant Center or business data feeds are targeted.
     * If opted in (false), the entire domain will be targeted. This field can
     * only be set for Performance Max campaigns, where the default value is
     * false.
     *
     * Generated from protobuf field <code>optional bool url_expansion_opt_out = 72;</code>
     * @param bool $var
     * @return $this
     */
    public function setUrlExpansionOptOut($var)
    {
        GPBUtil::checkBool($var);
        $this->url_expansion_opt_out = $var;

        return $this;
    }

    /**
     * Output only. Information about campaigns being upgraded to Performance Max.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.PerformanceMaxUpgrade performance_max_upgrade = 77 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V16\Resources\Campaign\PerformanceMaxUpgrade|null
     */
    public function getPerformanceMaxUpgrade()
    {
        return $this->performance_max_upgrade;
    }

    public function hasPerformanceMaxUpgrade()
    {
        return isset($this->performance_max_upgrade);
    }

    public function clearPerformanceMaxUpgrade()
    {
        unset($this->performance_max_upgrade);
    }

    /**
     * Output only. Information about campaigns being upgraded to Performance Max.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.resources.Campaign.PerformanceMaxUpgrade performance_max_upgrade = 77 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V16\Resources\Campaign\PerformanceMaxUpgrade $var
     * @return $this
     */
    public function setPerformanceMaxUpgrade($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Resources\Campaign\PerformanceMaxUpgrade::class);
        $this->performance_max_upgrade = $var;

        return $this;
    }

    /**
     * Immutable. The set of hotel properties for Performance Max for travel goals
     * campaigns.
     *
     * Generated from protobuf field <code>optional string hotel_property_asset_set = 83 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getHotelPropertyAssetSet()
    {
        return isset($this->hotel_property_asset_set) ? $this->hotel_property_asset_set : '';
    }

    public function hasHotelPropertyAssetSet()
    {
        return isset($this->hotel_property_asset_set);
    }

    public function clearHotelPropertyAssetSet()
    {
        unset($this->hotel_property_asset_set);
    }

    /**
     * Immutable. The set of hotel properties for Performance Max for travel goals
     * campaigns.
     *
     * Generated from protobuf field <code>optional string hotel_property_asset_set = 83 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setHotelPropertyAssetSet($var)
    {
        GPBUtil::checkString($var, True);
        $this->hotel_property_asset_set = $var;

        return $this;
    }

    /**
     * Immutable. Listing type of ads served for this campaign.
     * Field is restricted for usage with Performance Max campaigns.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v16.enums.ListingTypeEnum.ListingType listing_type = 86 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @return int
     */
    public function getListingType()
    {
        return isset($this->listing_type) ? $this->listing_type : 0;
    }

    public function hasListingType()
    {
        return isset($this->listing_type);
    }

    public function clearListingType()
    {
        unset($this->listing_type);
    }

    /**
     * Immutable. Listing type of ads served for this campaign.
     * Field is restricted for usage with Performance Max campaigns.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v16.enums.ListingTypeEnum.ListingType listing_type = 86 [(.google.api.field_behavior) = IMMUTABLE];</code>
     * @param int $var
     * @return $this
     */
    public function setListingType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\ListingTypeEnum\ListingType::class);
        $this->listing_type = $var;

        return $this;
    }

    /**
     * Contains the opt-in/out status of each AssetAutomationType.
     * See documentation of each asset automation type enum for default
     * opt in/out behavior.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v16.resources.Campaign.AssetAutomationSetting asset_automation_settings = 88;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getAssetAutomationSettings()
    {
        return $this->asset_automation_settings;
    }

    /**
     * Contains the opt-in/out status of each AssetAutomationType.
     * See documentation of each asset automation type enum for default
     * opt in/out behavior.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v16.resources.Campaign.AssetAutomationSetting asset_automation_settings = 88;</code>
     * @param array<\Google\Ads\GoogleAds\V16\Resources\Campaign\AssetAutomationSetting>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setAssetAutomationSettings($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Ads\GoogleAds\V16\Resources\Campaign\AssetAutomationSetting::class);
        $this->asset_automation_settings = $arr;

        return $this;
    }

    /**
     * Portfolio bidding strategy used by campaign.
     *
     * Generated from protobuf field <code>string bidding_strategy = 67 [(.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getBiddingStrategy()
    {
        return $this->readOneof(67);
    }

    public function hasBiddingStrategy()
    {
        return $this->hasOneof(67);
    }

    /**
     * Portfolio bidding strategy used by campaign.
     *
     * Generated from protobuf field <code>string bidding_strategy = 67 [(.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setBiddingStrategy($var)
    {
        GPBUtil::checkString($var, True);
        $this->writeOneof(67, $var);

        return $this;
    }

    /**
     * Commission is an automatic bidding strategy in which the advertiser pays
     * a certain portion of the conversion value.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.Commission commission = 49;</code>
     * @return \Google\Ads\GoogleAds\V16\Common\Commission|null
     */
    public function getCommission()
    {
        return $this->readOneof(49);
    }

    public function hasCommission()
    {
        return $this->hasOneof(49);
    }

    /**
     * Commission is an automatic bidding strategy in which the advertiser pays
     * a certain portion of the conversion value.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.Commission commission = 49;</code>
     * @param \Google\Ads\GoogleAds\V16\Common\Commission $var
     * @return $this
     */
    public function setCommission($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Common\Commission::class);
        $this->writeOneof(49, $var);

        return $this;
    }

    /**
     * Standard Manual CPA bidding strategy.
     * Manual bidding strategy that allows advertiser to set the bid per
     * advertiser-specified action. Supported only for Local Services campaigns.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.ManualCpa manual_cpa = 74;</code>
     * @return \Google\Ads\GoogleAds\V16\Common\ManualCpa|null
     */
    public function getManualCpa()
    {
        return $this->readOneof(74);
    }

    public function hasManualCpa()
    {
        return $this->hasOneof(74);
    }

    /**
     * Standard Manual CPA bidding strategy.
     * Manual bidding strategy that allows advertiser to set the bid per
     * advertiser-specified action. Supported only for Local Services campaigns.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.ManualCpa manual_cpa = 74;</code>
     * @param \Google\Ads\GoogleAds\V16\Common\ManualCpa $var
     * @return $this
     */
    public function setManualCpa($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Common\ManualCpa::class);
        $this->writeOneof(74, $var);

        return $this;
    }

    /**
     * Standard Manual CPC bidding strategy.
     * Manual click-based bidding where user pays per click.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.ManualCpc manual_cpc = 24;</code>
     * @return \Google\Ads\GoogleAds\V16\Common\ManualCpc|null
     */
    public function getManualCpc()
    {
        return $this->readOneof(24);
    }

    public function hasManualCpc()
    {
        return $this->hasOneof(24);
    }

    /**
     * Standard Manual CPC bidding strategy.
     * Manual click-based bidding where user pays per click.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.ManualCpc manual_cpc = 24;</code>
     * @param \Google\Ads\GoogleAds\V16\Common\ManualCpc $var
     * @return $this
     */
    public function setManualCpc($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Common\ManualCpc::class);
        $this->writeOneof(24, $var);

        return $this;
    }

    /**
     * Standard Manual CPM bidding strategy.
     * Manual impression-based bidding where user pays per thousand
     * impressions.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.ManualCpm manual_cpm = 25;</code>
     * @return \Google\Ads\GoogleAds\V16\Common\ManualCpm|null
     */
    public function getManualCpm()
    {
        return $this->readOneof(25);
    }

    public function hasManualCpm()
    {
        return $this->hasOneof(25);
    }

    /**
     * Standard Manual CPM bidding strategy.
     * Manual impression-based bidding where user pays per thousand
     * impressions.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.ManualCpm manual_cpm = 25;</code>
     * @param \Google\Ads\GoogleAds\V16\Common\ManualCpm $var
     * @return $this
     */
    public function setManualCpm($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Common\ManualCpm::class);
        $this->writeOneof(25, $var);

        return $this;
    }

    /**
     * A bidding strategy that pays a configurable amount per video view.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.ManualCpv manual_cpv = 37;</code>
     * @return \Google\Ads\GoogleAds\V16\Common\ManualCpv|null
     */
    public function getManualCpv()
    {
        return $this->readOneof(37);
    }

    public function hasManualCpv()
    {
        return $this->hasOneof(37);
    }

    /**
     * A bidding strategy that pays a configurable amount per video view.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.ManualCpv manual_cpv = 37;</code>
     * @param \Google\Ads\GoogleAds\V16\Common\ManualCpv $var
     * @return $this
     */
    public function setManualCpv($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Common\ManualCpv::class);
        $this->writeOneof(37, $var);

        return $this;
    }

    /**
     * Standard Maximize Conversions bidding strategy that automatically
     * maximizes number of conversions while spending your budget.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.MaximizeConversions maximize_conversions = 30;</code>
     * @return \Google\Ads\GoogleAds\V16\Common\MaximizeConversions|null
     */
    public function getMaximizeConversions()
    {
        return $this->readOneof(30);
    }

    public function hasMaximizeConversions()
    {
        return $this->hasOneof(30);
    }

    /**
     * Standard Maximize Conversions bidding strategy that automatically
     * maximizes number of conversions while spending your budget.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.MaximizeConversions maximize_conversions = 30;</code>
     * @param \Google\Ads\GoogleAds\V16\Common\MaximizeConversions $var
     * @return $this
     */
    public function setMaximizeConversions($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Common\MaximizeConversions::class);
        $this->writeOneof(30, $var);

        return $this;
    }

    /**
     * Standard Maximize Conversion Value bidding strategy that automatically
     * sets bids to maximize revenue while spending your budget.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.MaximizeConversionValue maximize_conversion_value = 31;</code>
     * @return \Google\Ads\GoogleAds\V16\Common\MaximizeConversionValue|null
     */
    public function getMaximizeConversionValue()
    {
        return $this->readOneof(31);
    }

    public function hasMaximizeConversionValue()
    {
        return $this->hasOneof(31);
    }

    /**
     * Standard Maximize Conversion Value bidding strategy that automatically
     * sets bids to maximize revenue while spending your budget.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.MaximizeConversionValue maximize_conversion_value = 31;</code>
     * @param \Google\Ads\GoogleAds\V16\Common\MaximizeConversionValue $var
     * @return $this
     */
    public function setMaximizeConversionValue($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Common\MaximizeConversionValue::class);
        $this->writeOneof(31, $var);

        return $this;
    }

    /**
     * Standard Target CPA bidding strategy that automatically sets bids to
     * help get as many conversions as possible at the target
     * cost-per-acquisition (CPA) you set.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.TargetCpa target_cpa = 26;</code>
     * @return \Google\Ads\GoogleAds\V16\Common\TargetCpa|null
     */
    public function getTargetCpa()
    {
        return $this->readOneof(26);
    }

    public function hasTargetCpa()
    {
        return $this->hasOneof(26);
    }

    /**
     * Standard Target CPA bidding strategy that automatically sets bids to
     * help get as many conversions as possible at the target
     * cost-per-acquisition (CPA) you set.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.TargetCpa target_cpa = 26;</code>
     * @param \Google\Ads\GoogleAds\V16\Common\TargetCpa $var
     * @return $this
     */
    public function setTargetCpa($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Common\TargetCpa::class);
        $this->writeOneof(26, $var);

        return $this;
    }

    /**
     * Target Impression Share bidding strategy. An automated bidding strategy
     * that sets bids to achieve a chosen percentage of impressions.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.TargetImpressionShare target_impression_share = 48;</code>
     * @return \Google\Ads\GoogleAds\V16\Common\TargetImpressionShare|null
     */
    public function getTargetImpressionShare()
    {
        return $this->readOneof(48);
    }

    public function hasTargetImpressionShare()
    {
        return $this->hasOneof(48);
    }

    /**
     * Target Impression Share bidding strategy. An automated bidding strategy
     * that sets bids to achieve a chosen percentage of impressions.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.TargetImpressionShare target_impression_share = 48;</code>
     * @param \Google\Ads\GoogleAds\V16\Common\TargetImpressionShare $var
     * @return $this
     */
    public function setTargetImpressionShare($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Common\TargetImpressionShare::class);
        $this->writeOneof(48, $var);

        return $this;
    }

    /**
     * Standard Target ROAS bidding strategy that automatically maximizes
     * revenue while averaging a specific target return on ad spend (ROAS).
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.TargetRoas target_roas = 29;</code>
     * @return \Google\Ads\GoogleAds\V16\Common\TargetRoas|null
     */
    public function getTargetRoas()
    {
        return $this->readOneof(29);
    }

    public function hasTargetRoas()
    {
        return $this->hasOneof(29);
    }

    /**
     * Standard Target ROAS bidding strategy that automatically maximizes
     * revenue while averaging a specific target return on ad spend (ROAS).
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.TargetRoas target_roas = 29;</code>
     * @param \Google\Ads\GoogleAds\V16\Common\TargetRoas $var
     * @return $this
     */
    public function setTargetRoas($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Common\TargetRoas::class);
        $this->writeOneof(29, $var);

        return $this;
    }

    /**
     * Standard Target Spend bidding strategy that automatically sets your bids
     * to help get as many clicks as possible within your budget.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.TargetSpend target_spend = 27;</code>
     * @return \Google\Ads\GoogleAds\V16\Common\TargetSpend|null
     */
    public function getTargetSpend()
    {
        return $this->readOneof(27);
    }

    public function hasTargetSpend()
    {
        return $this->hasOneof(27);
    }

    /**
     * Standard Target Spend bidding strategy that automatically sets your bids
     * to help get as many clicks as possible within your budget.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.TargetSpend target_spend = 27;</code>
     * @param \Google\Ads\GoogleAds\V16\Common\TargetSpend $var
     * @return $this
     */
    public function setTargetSpend($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Common\TargetSpend::class);
        $this->writeOneof(27, $var);

        return $this;
    }

    /**
     * Standard Percent Cpc bidding strategy where bids are a fraction of the
     * advertised price for some good or service.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.PercentCpc percent_cpc = 34;</code>
     * @return \Google\Ads\GoogleAds\V16\Common\PercentCpc|null
     */
    public function getPercentCpc()
    {
        return $this->readOneof(34);
    }

    public function hasPercentCpc()
    {
        return $this->hasOneof(34);
    }

    /**
     * Standard Percent Cpc bidding strategy where bids are a fraction of the
     * advertised price for some good or service.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.PercentCpc percent_cpc = 34;</code>
     * @param \Google\Ads\GoogleAds\V16\Common\PercentCpc $var
     * @return $this
     */
    public function setPercentCpc($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Common\PercentCpc::class);
        $this->writeOneof(34, $var);

        return $this;
    }

    /**
     * A bidding strategy that automatically optimizes cost per thousand
     * impressions.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.TargetCpm target_cpm = 41;</code>
     * @return \Google\Ads\GoogleAds\V16\Common\TargetCpm|null
     */
    public function getTargetCpm()
    {
        return $this->readOneof(41);
    }

    public function hasTargetCpm()
    {
        return $this->hasOneof(41);
    }

    /**
     * A bidding strategy that automatically optimizes cost per thousand
     * impressions.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.common.TargetCpm target_cpm = 41;</code>
     * @param \Google\Ads\GoogleAds\V16\Common\TargetCpm $var
     * @return $this
     */
    public function setTargetCpm($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V16\Common\TargetCpm::class);
        $this->writeOneof(41, $var);

        return $this;
    }

    /**
     * @return string
     */
    public function getCampaignBiddingStrategy()
    {
        return $this->whichOneof("campaign_bidding_strategy");
    }

}

