<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/common/ad_type_infos.proto

namespace Google\Ads\GoogleAds\V17\Common;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A Demand Gen product ad.
 *
 * Generated from protobuf message <code>google.ads.googleads.v17.common.DemandGenProductAdInfo</code>
 */
class DemandGenProductAdInfo extends \Google\Protobuf\Internal\Message
{
    /**
     * Required. Text asset used for the short headline.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v17.common.AdTextAsset headline = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     */
    protected $headline = null;
    /**
     * Required. Text asset used for the description.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v17.common.AdTextAsset description = 2 [(.google.api.field_behavior) = REQUIRED];</code>
     */
    protected $description = null;
    /**
     * Required. Logo image to be used in the ad. Valid image types are GIF, JPEG,
     * and PNG. The minimum size is 128x128 and the aspect ratio must be 1:1
     * (+-1%).
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v17.common.AdImageAsset logo_image = 3 [(.google.api.field_behavior) = REQUIRED];</code>
     */
    protected $logo_image = null;
    /**
     * First part of text that appears in the ad with the displayed URL.
     *
     * Generated from protobuf field <code>string breadcrumb1 = 4;</code>
     */
    protected $breadcrumb1 = '';
    /**
     * Second part of text that appears in the ad with the displayed URL.
     *
     * Generated from protobuf field <code>string breadcrumb2 = 5;</code>
     */
    protected $breadcrumb2 = '';
    /**
     * Required. The advertiser/brand name.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.AdTextAsset business_name = 6 [(.google.api.field_behavior) = REQUIRED];</code>
     */
    protected $business_name = null;
    /**
     * Asset of type CallToActionAsset used for the "Call To Action" button.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v17.common.AdCallToActionAsset call_to_action = 7;</code>
     */
    protected $call_to_action = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Ads\GoogleAds\V17\Common\AdTextAsset $headline
     *           Required. Text asset used for the short headline.
     *     @type \Google\Ads\GoogleAds\V17\Common\AdTextAsset $description
     *           Required. Text asset used for the description.
     *     @type \Google\Ads\GoogleAds\V17\Common\AdImageAsset $logo_image
     *           Required. Logo image to be used in the ad. Valid image types are GIF, JPEG,
     *           and PNG. The minimum size is 128x128 and the aspect ratio must be 1:1
     *           (+-1%).
     *     @type string $breadcrumb1
     *           First part of text that appears in the ad with the displayed URL.
     *     @type string $breadcrumb2
     *           Second part of text that appears in the ad with the displayed URL.
     *     @type \Google\Ads\GoogleAds\V17\Common\AdTextAsset $business_name
     *           Required. The advertiser/brand name.
     *     @type \Google\Ads\GoogleAds\V17\Common\AdCallToActionAsset $call_to_action
     *           Asset of type CallToActionAsset used for the "Call To Action" button.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V17\Common\AdTypeInfos::initOnce();
        parent::__construct($data);
    }

    /**
     * Required. Text asset used for the short headline.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v17.common.AdTextAsset headline = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     * @return \Google\Ads\GoogleAds\V17\Common\AdTextAsset|null
     */
    public function getHeadline()
    {
        return $this->headline;
    }

    public function hasHeadline()
    {
        return isset($this->headline);
    }

    public function clearHeadline()
    {
        unset($this->headline);
    }

    /**
     * Required. Text asset used for the short headline.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v17.common.AdTextAsset headline = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     * @param \Google\Ads\GoogleAds\V17\Common\AdTextAsset $var
     * @return $this
     */
    public function setHeadline($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\AdTextAsset::class);
        $this->headline = $var;

        return $this;
    }

    /**
     * Required. Text asset used for the description.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v17.common.AdTextAsset description = 2 [(.google.api.field_behavior) = REQUIRED];</code>
     * @return \Google\Ads\GoogleAds\V17\Common\AdTextAsset|null
     */
    public function getDescription()
    {
        return $this->description;
    }

    public function hasDescription()
    {
        return isset($this->description);
    }

    public function clearDescription()
    {
        unset($this->description);
    }

    /**
     * Required. Text asset used for the description.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v17.common.AdTextAsset description = 2 [(.google.api.field_behavior) = REQUIRED];</code>
     * @param \Google\Ads\GoogleAds\V17\Common\AdTextAsset $var
     * @return $this
     */
    public function setDescription($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\AdTextAsset::class);
        $this->description = $var;

        return $this;
    }

    /**
     * Required. Logo image to be used in the ad. Valid image types are GIF, JPEG,
     * and PNG. The minimum size is 128x128 and the aspect ratio must be 1:1
     * (+-1%).
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v17.common.AdImageAsset logo_image = 3 [(.google.api.field_behavior) = REQUIRED];</code>
     * @return \Google\Ads\GoogleAds\V17\Common\AdImageAsset|null
     */
    public function getLogoImage()
    {
        return $this->logo_image;
    }

    public function hasLogoImage()
    {
        return isset($this->logo_image);
    }

    public function clearLogoImage()
    {
        unset($this->logo_image);
    }

    /**
     * Required. Logo image to be used in the ad. Valid image types are GIF, JPEG,
     * and PNG. The minimum size is 128x128 and the aspect ratio must be 1:1
     * (+-1%).
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v17.common.AdImageAsset logo_image = 3 [(.google.api.field_behavior) = REQUIRED];</code>
     * @param \Google\Ads\GoogleAds\V17\Common\AdImageAsset $var
     * @return $this
     */
    public function setLogoImage($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\AdImageAsset::class);
        $this->logo_image = $var;

        return $this;
    }

    /**
     * First part of text that appears in the ad with the displayed URL.
     *
     * Generated from protobuf field <code>string breadcrumb1 = 4;</code>
     * @return string
     */
    public function getBreadcrumb1()
    {
        return $this->breadcrumb1;
    }

    /**
     * First part of text that appears in the ad with the displayed URL.
     *
     * Generated from protobuf field <code>string breadcrumb1 = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setBreadcrumb1($var)
    {
        GPBUtil::checkString($var, True);
        $this->breadcrumb1 = $var;

        return $this;
    }

    /**
     * Second part of text that appears in the ad with the displayed URL.
     *
     * Generated from protobuf field <code>string breadcrumb2 = 5;</code>
     * @return string
     */
    public function getBreadcrumb2()
    {
        return $this->breadcrumb2;
    }

    /**
     * Second part of text that appears in the ad with the displayed URL.
     *
     * Generated from protobuf field <code>string breadcrumb2 = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setBreadcrumb2($var)
    {
        GPBUtil::checkString($var, True);
        $this->breadcrumb2 = $var;

        return $this;
    }

    /**
     * Required. The advertiser/brand name.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.AdTextAsset business_name = 6 [(.google.api.field_behavior) = REQUIRED];</code>
     * @return \Google\Ads\GoogleAds\V17\Common\AdTextAsset|null
     */
    public function getBusinessName()
    {
        return $this->business_name;
    }

    public function hasBusinessName()
    {
        return isset($this->business_name);
    }

    public function clearBusinessName()
    {
        unset($this->business_name);
    }

    /**
     * Required. The advertiser/brand name.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.common.AdTextAsset business_name = 6 [(.google.api.field_behavior) = REQUIRED];</code>
     * @param \Google\Ads\GoogleAds\V17\Common\AdTextAsset $var
     * @return $this
     */
    public function setBusinessName($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\AdTextAsset::class);
        $this->business_name = $var;

        return $this;
    }

    /**
     * Asset of type CallToActionAsset used for the "Call To Action" button.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v17.common.AdCallToActionAsset call_to_action = 7;</code>
     * @return \Google\Ads\GoogleAds\V17\Common\AdCallToActionAsset|null
     */
    public function getCallToAction()
    {
        return $this->call_to_action;
    }

    public function hasCallToAction()
    {
        return isset($this->call_to_action);
    }

    public function clearCallToAction()
    {
        unset($this->call_to_action);
    }

    /**
     * Asset of type CallToActionAsset used for the "Call To Action" button.
     *
     * Generated from protobuf field <code>optional .google.ads.googleads.v17.common.AdCallToActionAsset call_to_action = 7;</code>
     * @param \Google\Ads\GoogleAds\V17\Common\AdCallToActionAsset $var
     * @return $this
     */
    public function setCallToAction($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Common\AdCallToActionAsset::class);
        $this->call_to_action = $var;

        return $this;
    }

}

