<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v15/resources/recommendation.proto

namespace Google\Ads\GoogleAds\V15\Resources;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A recommendation.
 *
 * Generated from protobuf message <code>google.ads.googleads.v15.resources.Recommendation</code>
 */
class Recommendation extends \Google\Protobuf\Internal\Message
{
    /**
     * Immutable. The resource name of the recommendation.
     * `customers/{customer_id}/recommendations/{recommendation_id}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     */
    protected $resource_name = '';
    /**
     * Output only. The type of recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.enums.RecommendationTypeEnum.RecommendationType type = 2 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $type = 0;
    /**
     * Output only. The impact on account performance as a result of applying the
     * recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.RecommendationImpact impact = 3 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $impact = null;
    /**
     * Output only. The budget targeted by this recommendation. This will be set
     * only when the recommendation affects a single campaign budget.
     * This field will be set for the following recommendation types:
     * CAMPAIGN_BUDGET, FORECASTING_CAMPAIGN_BUDGET, MARGINAL_ROI_CAMPAIGN_BUDGET,
     * MOVE_UNUSED_BUDGET
     *
     * Generated from protobuf field <code>optional string campaign_budget = 24 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     */
    protected $campaign_budget = null;
    /**
     * Output only. The campaign targeted by this recommendation.
     * This field will be set for the following recommendation types:
     * CALL_EXTENSION, CALLOUT_EXTENSION, ENHANCED_CPC_OPT_IN,
     * USE_BROAD_MATCH_KEYWORD, KEYWORD, KEYWORD_MATCH_TYPE,
     * UPGRADE_LOCAL_CAMPAIGN_TO_PERFORMANCE_MAX, MAXIMIZE_CLICKS_OPT_IN,
     * MAXIMIZE_CONVERSIONS_OPT_IN, OPTIMIZE_AD_ROTATION,
     * RESPONSIVE_SEARCH_AD,
     * RESPONSIVE_SEARCH_AD_ASSET,
     * SEARCH_PARTNERS_OPT_IN, DISPLAY_EXPANSION_OPT_IN, SITELINK_EXTENSION,
     * TARGET_CPA_OPT_IN, TARGET_ROAS_OPT_IN, TEXT_AD,
     * UPGRADE_SMART_SHOPPING_CAMPAIGN_TO_PERFORMANCE_MAX,
     * RAISE_TARGET_CPA_BID_TOO_LOW, FORECASTING_SET_TARGET_ROAS,
     * SHOPPING_ADD_AGE_GROUP, SHOPPING_ADD_COLOR, SHOPPING_ADD_GENDER,
     * SHOPPING_ADD_SIZE, SHOPPING_ADD_GTIN, SHOPPING_ADD_MORE_IDENTIFIERS,
     * SHOPPING_ADD_PRODUCTS_TO_CAMPAIGN, SHOPPING_FIX_DISAPPROVED_PRODUCTS,
     * SHOPPING_MIGRATE_REGULAR_SHOPPING_CAMPAIGN_OFFERS_TO_PERFORMANCE_MAX,
     * DYNAMIC_IMAGE_EXTENSION_OPT_IN, RAISE_TARGET_CPA, LOWER_TARGET_ROAS,
     * FORECASTING_SET_TARGET_CPA,
     * SET_TARGET_CPA, SET_TARGET_ROAS
     *
     * Generated from protobuf field <code>optional string campaign = 25 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     */
    protected $campaign = null;
    /**
     * Output only. The ad group targeted by this recommendation. This will be set
     * only when the recommendation affects a single ad group.
     * This field will be set for the following recommendation types:
     * KEYWORD, OPTIMIZE_AD_ROTATION, RESPONSIVE_SEARCH_AD,
     * RESPONSIVE_SEARCH_AD_ASSET, TEXT_AD
     *
     * Generated from protobuf field <code>optional string ad_group = 26 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     */
    protected $ad_group = null;
    /**
     * Output only. Whether the recommendation is dismissed or not.
     *
     * Generated from protobuf field <code>optional bool dismissed = 27 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     */
    protected $dismissed = null;
    /**
     * Output only. The campaigns targeted by this recommendation.
     * This field will be set for the following recommendation types:
     * CAMPAIGN_BUDGET, FORECASTING_CAMPAIGN_BUDGET,
     * MARGINAL_ROI_CAMPAIGN_BUDGET and MOVE_UNUSED_BUDGET
     *
     * Generated from protobuf field <code>repeated string campaigns = 38 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     */
    private $campaigns;
    protected $recommendation;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $resource_name
     *           Immutable. The resource name of the recommendation.
     *           `customers/{customer_id}/recommendations/{recommendation_id}`
     *     @type int $type
     *           Output only. The type of recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\RecommendationImpact $impact
     *           Output only. The impact on account performance as a result of applying the
     *           recommendation.
     *     @type string $campaign_budget
     *           Output only. The budget targeted by this recommendation. This will be set
     *           only when the recommendation affects a single campaign budget.
     *           This field will be set for the following recommendation types:
     *           CAMPAIGN_BUDGET, FORECASTING_CAMPAIGN_BUDGET, MARGINAL_ROI_CAMPAIGN_BUDGET,
     *           MOVE_UNUSED_BUDGET
     *     @type string $campaign
     *           Output only. The campaign targeted by this recommendation.
     *           This field will be set for the following recommendation types:
     *           CALL_EXTENSION, CALLOUT_EXTENSION, ENHANCED_CPC_OPT_IN,
     *           USE_BROAD_MATCH_KEYWORD, KEYWORD, KEYWORD_MATCH_TYPE,
     *           UPGRADE_LOCAL_CAMPAIGN_TO_PERFORMANCE_MAX, MAXIMIZE_CLICKS_OPT_IN,
     *           MAXIMIZE_CONVERSIONS_OPT_IN, OPTIMIZE_AD_ROTATION,
     *           RESPONSIVE_SEARCH_AD,
     *           RESPONSIVE_SEARCH_AD_ASSET,
     *           SEARCH_PARTNERS_OPT_IN, DISPLAY_EXPANSION_OPT_IN, SITELINK_EXTENSION,
     *           TARGET_CPA_OPT_IN, TARGET_ROAS_OPT_IN, TEXT_AD,
     *           UPGRADE_SMART_SHOPPING_CAMPAIGN_TO_PERFORMANCE_MAX,
     *           RAISE_TARGET_CPA_BID_TOO_LOW, FORECASTING_SET_TARGET_ROAS,
     *           SHOPPING_ADD_AGE_GROUP, SHOPPING_ADD_COLOR, SHOPPING_ADD_GENDER,
     *           SHOPPING_ADD_SIZE, SHOPPING_ADD_GTIN, SHOPPING_ADD_MORE_IDENTIFIERS,
     *           SHOPPING_ADD_PRODUCTS_TO_CAMPAIGN, SHOPPING_FIX_DISAPPROVED_PRODUCTS,
     *           SHOPPING_MIGRATE_REGULAR_SHOPPING_CAMPAIGN_OFFERS_TO_PERFORMANCE_MAX,
     *           DYNAMIC_IMAGE_EXTENSION_OPT_IN, RAISE_TARGET_CPA, LOWER_TARGET_ROAS,
     *           FORECASTING_SET_TARGET_CPA,
     *           SET_TARGET_CPA, SET_TARGET_ROAS
     *     @type string $ad_group
     *           Output only. The ad group targeted by this recommendation. This will be set
     *           only when the recommendation affects a single ad group.
     *           This field will be set for the following recommendation types:
     *           KEYWORD, OPTIMIZE_AD_ROTATION, RESPONSIVE_SEARCH_AD,
     *           RESPONSIVE_SEARCH_AD_ASSET, TEXT_AD
     *     @type bool $dismissed
     *           Output only. Whether the recommendation is dismissed or not.
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $campaigns
     *           Output only. The campaigns targeted by this recommendation.
     *           This field will be set for the following recommendation types:
     *           CAMPAIGN_BUDGET, FORECASTING_CAMPAIGN_BUDGET,
     *           MARGINAL_ROI_CAMPAIGN_BUDGET and MOVE_UNUSED_BUDGET
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\CampaignBudgetRecommendation $campaign_budget_recommendation
     *           Output only. The campaign budget recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\CampaignBudgetRecommendation $forecasting_campaign_budget_recommendation
     *           Output only. The forecasting campaign budget recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\KeywordRecommendation $keyword_recommendation
     *           Output only. The keyword recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\TextAdRecommendation $text_ad_recommendation
     *           Output only. Add expanded text ad recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\TargetCpaOptInRecommendation $target_cpa_opt_in_recommendation
     *           Output only. The TargetCPA opt-in recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\MaximizeConversionsOptInRecommendation $maximize_conversions_opt_in_recommendation
     *           Output only. The MaximizeConversions Opt-In recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\EnhancedCpcOptInRecommendation $enhanced_cpc_opt_in_recommendation
     *           Output only. The Enhanced Cost-Per-Click Opt-In recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\SearchPartnersOptInRecommendation $search_partners_opt_in_recommendation
     *           Output only. The Search Partners Opt-In recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\MaximizeClicksOptInRecommendation $maximize_clicks_opt_in_recommendation
     *           Output only. The MaximizeClicks Opt-In recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\OptimizeAdRotationRecommendation $optimize_ad_rotation_recommendation
     *           Output only. The Optimize Ad Rotation recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\KeywordMatchTypeRecommendation $keyword_match_type_recommendation
     *           Output only. The keyword match type recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\MoveUnusedBudgetRecommendation $move_unused_budget_recommendation
     *           Output only. The move unused budget recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\TargetRoasOptInRecommendation $target_roas_opt_in_recommendation
     *           Output only. The Target ROAS opt-in recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\ResponsiveSearchAdRecommendation $responsive_search_ad_recommendation
     *           Output only. The add responsive search ad recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\CampaignBudgetRecommendation $marginal_roi_campaign_budget_recommendation
     *           Output only. The marginal ROI campaign budget recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\UseBroadMatchKeywordRecommendation $use_broad_match_keyword_recommendation
     *           Output only. The use broad match keyword recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\ResponsiveSearchAdAssetRecommendation $responsive_search_ad_asset_recommendation
     *           Output only. The responsive search ad asset recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\UpgradeSmartShoppingCampaignToPerformanceMaxRecommendation $upgrade_smart_shopping_campaign_to_performance_max_recommendation
     *           Output only. The upgrade a Smart Shopping campaign to a Performance Max
     *           campaign recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\ResponsiveSearchAdImproveAdStrengthRecommendation $responsive_search_ad_improve_ad_strength_recommendation
     *           Output only. The responsive search ad improve ad strength recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\DisplayExpansionOptInRecommendation $display_expansion_opt_in_recommendation
     *           Output only. The Display Expansion opt-in recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\UpgradeLocalCampaignToPerformanceMaxRecommendation $upgrade_local_campaign_to_performance_max_recommendation
     *           Output only. The upgrade a Local campaign to a Performance Max campaign
     *           recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\RaiseTargetCpaBidTooLowRecommendation $raise_target_cpa_bid_too_low_recommendation
     *           Output only. The raise target CPA bid too low recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\ForecastingSetTargetRoasRecommendation $forecasting_set_target_roas_recommendation
     *           Output only. The forecasting set target ROAS recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\CalloutAssetRecommendation $callout_asset_recommendation
     *           Output only. The callout asset recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\SitelinkAssetRecommendation $sitelink_asset_recommendation
     *           Output only. The sitelink asset recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\CallAssetRecommendation $call_asset_recommendation
     *           Output only. The call asset recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation $shopping_add_age_group_recommendation
     *           Output only. The shopping add age group recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation $shopping_add_color_recommendation
     *           Output only. The shopping add color recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation $shopping_add_gender_recommendation
     *           Output only. The shopping add gender recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation $shopping_add_gtin_recommendation
     *           Output only. The shopping add GTIN recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation $shopping_add_more_identifiers_recommendation
     *           Output only. The shopping add more identifiers recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation $shopping_add_size_recommendation
     *           Output only. The shopping add size recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingAddProductsToCampaignRecommendation $shopping_add_products_to_campaign_recommendation
     *           Output only. The shopping add products to campaign recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingFixDisapprovedProductsRecommendation $shopping_fix_disapproved_products_recommendation
     *           Output only. The shopping fix disapproved products recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingTargetAllOffersRecommendation $shopping_target_all_offers_recommendation
     *           Output only. The shopping target all offers recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingMerchantCenterAccountSuspensionRecommendation $shopping_fix_suspended_merchant_center_account_recommendation
     *           Output only. The shopping fix suspended Merchant Center account
     *           recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingMerchantCenterAccountSuspensionRecommendation $shopping_fix_merchant_center_account_suspension_warning_recommendation
     *           Output only. The shopping fix Merchant Center account suspension warning
     *           recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingMigrateRegularShoppingCampaignOffersToPerformanceMaxRecommendation $shopping_migrate_regular_shopping_campaign_offers_to_performance_max_recommendation
     *           Output only. The shopping migrate Regular Shopping Campaign offers to
     *           Performance Max recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\DynamicImageExtensionOptInRecommendation $dynamic_image_extension_opt_in_recommendation
     *           Output only. Recommendation to enable dynamic image extensions on the
     *           account, allowing Google to find the best images from ad landing pages
     *           and complement text ads.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\RaiseTargetCpaRecommendation $raise_target_cpa_recommendation
     *           Output only. Recommendation to raise Target CPA.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\LowerTargetRoasRecommendation $lower_target_roas_recommendation
     *           Output only. Recommendation to lower Target ROAS.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\PerformanceMaxOptInRecommendation $performance_max_opt_in_recommendation
     *           Output only. The Performance Max Opt In recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\ImprovePerformanceMaxAdStrengthRecommendation $improve_performance_max_ad_strength_recommendation
     *           Output only. The improve Performance Max ad strength recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\MigrateDynamicSearchAdsCampaignToPerformanceMaxRecommendation $migrate_dynamic_search_ads_campaign_to_performance_max_recommendation
     *           Output only. The Dynamic Search Ads to Performance Max migration
     *           recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\ForecastingSetTargetCpaRecommendation $forecasting_set_target_cpa_recommendation
     *           Output only. The forecasting set target CPA recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\ForecastingSetTargetCpaRecommendation $set_target_cpa_recommendation
     *           Output only. The set target CPA recommendation.
     *     @type \Google\Ads\GoogleAds\V15\Resources\Recommendation\ForecastingSetTargetRoasRecommendation $set_target_roas_recommendation
     *           Output only. The set target ROAS recommendation.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V15\Resources\Recommendation::initOnce();
        parent::__construct($data);
    }

    /**
     * Immutable. The resource name of the recommendation.
     * `customers/{customer_id}/recommendations/{recommendation_id}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getResourceName()
    {
        return $this->resource_name;
    }

    /**
     * Immutable. The resource name of the recommendation.
     * `customers/{customer_id}/recommendations/{recommendation_id}`
     *
     * Generated from protobuf field <code>string resource_name = 1 [(.google.api.field_behavior) = IMMUTABLE, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setResourceName($var)
    {
        GPBUtil::checkString($var, True);
        $this->resource_name = $var;

        return $this;
    }

    /**
     * Output only. The type of recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.enums.RecommendationTypeEnum.RecommendationType type = 2 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return int
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Output only. The type of recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.enums.RecommendationTypeEnum.RecommendationType type = 2 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param int $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V15\Enums\RecommendationTypeEnum\RecommendationType::class);
        $this->type = $var;

        return $this;
    }

    /**
     * Output only. The impact on account performance as a result of applying the
     * recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.RecommendationImpact impact = 3 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\RecommendationImpact|null
     */
    public function getImpact()
    {
        return $this->impact;
    }

    public function hasImpact()
    {
        return isset($this->impact);
    }

    public function clearImpact()
    {
        unset($this->impact);
    }

    /**
     * Output only. The impact on account performance as a result of applying the
     * recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.RecommendationImpact impact = 3 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\RecommendationImpact $var
     * @return $this
     */
    public function setImpact($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\RecommendationImpact::class);
        $this->impact = $var;

        return $this;
    }

    /**
     * Output only. The budget targeted by this recommendation. This will be set
     * only when the recommendation affects a single campaign budget.
     * This field will be set for the following recommendation types:
     * CAMPAIGN_BUDGET, FORECASTING_CAMPAIGN_BUDGET, MARGINAL_ROI_CAMPAIGN_BUDGET,
     * MOVE_UNUSED_BUDGET
     *
     * Generated from protobuf field <code>optional string campaign_budget = 24 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getCampaignBudget()
    {
        return isset($this->campaign_budget) ? $this->campaign_budget : '';
    }

    public function hasCampaignBudget()
    {
        return isset($this->campaign_budget);
    }

    public function clearCampaignBudget()
    {
        unset($this->campaign_budget);
    }

    /**
     * Output only. The budget targeted by this recommendation. This will be set
     * only when the recommendation affects a single campaign budget.
     * This field will be set for the following recommendation types:
     * CAMPAIGN_BUDGET, FORECASTING_CAMPAIGN_BUDGET, MARGINAL_ROI_CAMPAIGN_BUDGET,
     * MOVE_UNUSED_BUDGET
     *
     * Generated from protobuf field <code>optional string campaign_budget = 24 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setCampaignBudget($var)
    {
        GPBUtil::checkString($var, True);
        $this->campaign_budget = $var;

        return $this;
    }

    /**
     * Output only. The campaign targeted by this recommendation.
     * This field will be set for the following recommendation types:
     * CALL_EXTENSION, CALLOUT_EXTENSION, ENHANCED_CPC_OPT_IN,
     * USE_BROAD_MATCH_KEYWORD, KEYWORD, KEYWORD_MATCH_TYPE,
     * UPGRADE_LOCAL_CAMPAIGN_TO_PERFORMANCE_MAX, MAXIMIZE_CLICKS_OPT_IN,
     * MAXIMIZE_CONVERSIONS_OPT_IN, OPTIMIZE_AD_ROTATION,
     * RESPONSIVE_SEARCH_AD,
     * RESPONSIVE_SEARCH_AD_ASSET,
     * SEARCH_PARTNERS_OPT_IN, DISPLAY_EXPANSION_OPT_IN, SITELINK_EXTENSION,
     * TARGET_CPA_OPT_IN, TARGET_ROAS_OPT_IN, TEXT_AD,
     * UPGRADE_SMART_SHOPPING_CAMPAIGN_TO_PERFORMANCE_MAX,
     * RAISE_TARGET_CPA_BID_TOO_LOW, FORECASTING_SET_TARGET_ROAS,
     * SHOPPING_ADD_AGE_GROUP, SHOPPING_ADD_COLOR, SHOPPING_ADD_GENDER,
     * SHOPPING_ADD_SIZE, SHOPPING_ADD_GTIN, SHOPPING_ADD_MORE_IDENTIFIERS,
     * SHOPPING_ADD_PRODUCTS_TO_CAMPAIGN, SHOPPING_FIX_DISAPPROVED_PRODUCTS,
     * SHOPPING_MIGRATE_REGULAR_SHOPPING_CAMPAIGN_OFFERS_TO_PERFORMANCE_MAX,
     * DYNAMIC_IMAGE_EXTENSION_OPT_IN, RAISE_TARGET_CPA, LOWER_TARGET_ROAS,
     * FORECASTING_SET_TARGET_CPA,
     * SET_TARGET_CPA, SET_TARGET_ROAS
     *
     * Generated from protobuf field <code>optional string campaign = 25 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getCampaign()
    {
        return isset($this->campaign) ? $this->campaign : '';
    }

    public function hasCampaign()
    {
        return isset($this->campaign);
    }

    public function clearCampaign()
    {
        unset($this->campaign);
    }

    /**
     * Output only. The campaign targeted by this recommendation.
     * This field will be set for the following recommendation types:
     * CALL_EXTENSION, CALLOUT_EXTENSION, ENHANCED_CPC_OPT_IN,
     * USE_BROAD_MATCH_KEYWORD, KEYWORD, KEYWORD_MATCH_TYPE,
     * UPGRADE_LOCAL_CAMPAIGN_TO_PERFORMANCE_MAX, MAXIMIZE_CLICKS_OPT_IN,
     * MAXIMIZE_CONVERSIONS_OPT_IN, OPTIMIZE_AD_ROTATION,
     * RESPONSIVE_SEARCH_AD,
     * RESPONSIVE_SEARCH_AD_ASSET,
     * SEARCH_PARTNERS_OPT_IN, DISPLAY_EXPANSION_OPT_IN, SITELINK_EXTENSION,
     * TARGET_CPA_OPT_IN, TARGET_ROAS_OPT_IN, TEXT_AD,
     * UPGRADE_SMART_SHOPPING_CAMPAIGN_TO_PERFORMANCE_MAX,
     * RAISE_TARGET_CPA_BID_TOO_LOW, FORECASTING_SET_TARGET_ROAS,
     * SHOPPING_ADD_AGE_GROUP, SHOPPING_ADD_COLOR, SHOPPING_ADD_GENDER,
     * SHOPPING_ADD_SIZE, SHOPPING_ADD_GTIN, SHOPPING_ADD_MORE_IDENTIFIERS,
     * SHOPPING_ADD_PRODUCTS_TO_CAMPAIGN, SHOPPING_FIX_DISAPPROVED_PRODUCTS,
     * SHOPPING_MIGRATE_REGULAR_SHOPPING_CAMPAIGN_OFFERS_TO_PERFORMANCE_MAX,
     * DYNAMIC_IMAGE_EXTENSION_OPT_IN, RAISE_TARGET_CPA, LOWER_TARGET_ROAS,
     * FORECASTING_SET_TARGET_CPA,
     * SET_TARGET_CPA, SET_TARGET_ROAS
     *
     * Generated from protobuf field <code>optional string campaign = 25 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setCampaign($var)
    {
        GPBUtil::checkString($var, True);
        $this->campaign = $var;

        return $this;
    }

    /**
     * Output only. The ad group targeted by this recommendation. This will be set
     * only when the recommendation affects a single ad group.
     * This field will be set for the following recommendation types:
     * KEYWORD, OPTIMIZE_AD_ROTATION, RESPONSIVE_SEARCH_AD,
     * RESPONSIVE_SEARCH_AD_ASSET, TEXT_AD
     *
     * Generated from protobuf field <code>optional string ad_group = 26 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getAdGroup()
    {
        return isset($this->ad_group) ? $this->ad_group : '';
    }

    public function hasAdGroup()
    {
        return isset($this->ad_group);
    }

    public function clearAdGroup()
    {
        unset($this->ad_group);
    }

    /**
     * Output only. The ad group targeted by this recommendation. This will be set
     * only when the recommendation affects a single ad group.
     * This field will be set for the following recommendation types:
     * KEYWORD, OPTIMIZE_AD_ROTATION, RESPONSIVE_SEARCH_AD,
     * RESPONSIVE_SEARCH_AD_ASSET, TEXT_AD
     *
     * Generated from protobuf field <code>optional string ad_group = 26 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setAdGroup($var)
    {
        GPBUtil::checkString($var, True);
        $this->ad_group = $var;

        return $this;
    }

    /**
     * Output only. Whether the recommendation is dismissed or not.
     *
     * Generated from protobuf field <code>optional bool dismissed = 27 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return bool
     */
    public function getDismissed()
    {
        return isset($this->dismissed) ? $this->dismissed : false;
    }

    public function hasDismissed()
    {
        return isset($this->dismissed);
    }

    public function clearDismissed()
    {
        unset($this->dismissed);
    }

    /**
     * Output only. Whether the recommendation is dismissed or not.
     *
     * Generated from protobuf field <code>optional bool dismissed = 27 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param bool $var
     * @return $this
     */
    public function setDismissed($var)
    {
        GPBUtil::checkBool($var);
        $this->dismissed = $var;

        return $this;
    }

    /**
     * Output only. The campaigns targeted by this recommendation.
     * This field will be set for the following recommendation types:
     * CAMPAIGN_BUDGET, FORECASTING_CAMPAIGN_BUDGET,
     * MARGINAL_ROI_CAMPAIGN_BUDGET and MOVE_UNUSED_BUDGET
     *
     * Generated from protobuf field <code>repeated string campaigns = 38 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getCampaigns()
    {
        return $this->campaigns;
    }

    /**
     * Output only. The campaigns targeted by this recommendation.
     * This field will be set for the following recommendation types:
     * CAMPAIGN_BUDGET, FORECASTING_CAMPAIGN_BUDGET,
     * MARGINAL_ROI_CAMPAIGN_BUDGET and MOVE_UNUSED_BUDGET
     *
     * Generated from protobuf field <code>repeated string campaigns = 38 [(.google.api.field_behavior) = OUTPUT_ONLY, (.google.api.resource_reference) = {</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setCampaigns($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->campaigns = $arr;

        return $this;
    }

    /**
     * Output only. The campaign budget recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.CampaignBudgetRecommendation campaign_budget_recommendation = 4 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\CampaignBudgetRecommendation|null
     */
    public function getCampaignBudgetRecommendation()
    {
        return $this->readOneof(4);
    }

    public function hasCampaignBudgetRecommendation()
    {
        return $this->hasOneof(4);
    }

    /**
     * Output only. The campaign budget recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.CampaignBudgetRecommendation campaign_budget_recommendation = 4 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\CampaignBudgetRecommendation $var
     * @return $this
     */
    public function setCampaignBudgetRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\CampaignBudgetRecommendation::class);
        $this->writeOneof(4, $var);

        return $this;
    }

    /**
     * Output only. The forecasting campaign budget recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.CampaignBudgetRecommendation forecasting_campaign_budget_recommendation = 22 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\CampaignBudgetRecommendation|null
     */
    public function getForecastingCampaignBudgetRecommendation()
    {
        return $this->readOneof(22);
    }

    public function hasForecastingCampaignBudgetRecommendation()
    {
        return $this->hasOneof(22);
    }

    /**
     * Output only. The forecasting campaign budget recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.CampaignBudgetRecommendation forecasting_campaign_budget_recommendation = 22 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\CampaignBudgetRecommendation $var
     * @return $this
     */
    public function setForecastingCampaignBudgetRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\CampaignBudgetRecommendation::class);
        $this->writeOneof(22, $var);

        return $this;
    }

    /**
     * Output only. The keyword recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.KeywordRecommendation keyword_recommendation = 8 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\KeywordRecommendation|null
     */
    public function getKeywordRecommendation()
    {
        return $this->readOneof(8);
    }

    public function hasKeywordRecommendation()
    {
        return $this->hasOneof(8);
    }

    /**
     * Output only. The keyword recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.KeywordRecommendation keyword_recommendation = 8 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\KeywordRecommendation $var
     * @return $this
     */
    public function setKeywordRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\KeywordRecommendation::class);
        $this->writeOneof(8, $var);

        return $this;
    }

    /**
     * Output only. Add expanded text ad recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.TextAdRecommendation text_ad_recommendation = 9 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\TextAdRecommendation|null
     */
    public function getTextAdRecommendation()
    {
        return $this->readOneof(9);
    }

    public function hasTextAdRecommendation()
    {
        return $this->hasOneof(9);
    }

    /**
     * Output only. Add expanded text ad recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.TextAdRecommendation text_ad_recommendation = 9 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\TextAdRecommendation $var
     * @return $this
     */
    public function setTextAdRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\TextAdRecommendation::class);
        $this->writeOneof(9, $var);

        return $this;
    }

    /**
     * Output only. The TargetCPA opt-in recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.TargetCpaOptInRecommendation target_cpa_opt_in_recommendation = 10 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\TargetCpaOptInRecommendation|null
     */
    public function getTargetCpaOptInRecommendation()
    {
        return $this->readOneof(10);
    }

    public function hasTargetCpaOptInRecommendation()
    {
        return $this->hasOneof(10);
    }

    /**
     * Output only. The TargetCPA opt-in recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.TargetCpaOptInRecommendation target_cpa_opt_in_recommendation = 10 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\TargetCpaOptInRecommendation $var
     * @return $this
     */
    public function setTargetCpaOptInRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\TargetCpaOptInRecommendation::class);
        $this->writeOneof(10, $var);

        return $this;
    }

    /**
     * Output only. The MaximizeConversions Opt-In recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.MaximizeConversionsOptInRecommendation maximize_conversions_opt_in_recommendation = 11 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\MaximizeConversionsOptInRecommendation|null
     */
    public function getMaximizeConversionsOptInRecommendation()
    {
        return $this->readOneof(11);
    }

    public function hasMaximizeConversionsOptInRecommendation()
    {
        return $this->hasOneof(11);
    }

    /**
     * Output only. The MaximizeConversions Opt-In recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.MaximizeConversionsOptInRecommendation maximize_conversions_opt_in_recommendation = 11 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\MaximizeConversionsOptInRecommendation $var
     * @return $this
     */
    public function setMaximizeConversionsOptInRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\MaximizeConversionsOptInRecommendation::class);
        $this->writeOneof(11, $var);

        return $this;
    }

    /**
     * Output only. The Enhanced Cost-Per-Click Opt-In recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.EnhancedCpcOptInRecommendation enhanced_cpc_opt_in_recommendation = 12 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\EnhancedCpcOptInRecommendation|null
     */
    public function getEnhancedCpcOptInRecommendation()
    {
        return $this->readOneof(12);
    }

    public function hasEnhancedCpcOptInRecommendation()
    {
        return $this->hasOneof(12);
    }

    /**
     * Output only. The Enhanced Cost-Per-Click Opt-In recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.EnhancedCpcOptInRecommendation enhanced_cpc_opt_in_recommendation = 12 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\EnhancedCpcOptInRecommendation $var
     * @return $this
     */
    public function setEnhancedCpcOptInRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\EnhancedCpcOptInRecommendation::class);
        $this->writeOneof(12, $var);

        return $this;
    }

    /**
     * Output only. The Search Partners Opt-In recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.SearchPartnersOptInRecommendation search_partners_opt_in_recommendation = 14 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\SearchPartnersOptInRecommendation|null
     */
    public function getSearchPartnersOptInRecommendation()
    {
        return $this->readOneof(14);
    }

    public function hasSearchPartnersOptInRecommendation()
    {
        return $this->hasOneof(14);
    }

    /**
     * Output only. The Search Partners Opt-In recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.SearchPartnersOptInRecommendation search_partners_opt_in_recommendation = 14 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\SearchPartnersOptInRecommendation $var
     * @return $this
     */
    public function setSearchPartnersOptInRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\SearchPartnersOptInRecommendation::class);
        $this->writeOneof(14, $var);

        return $this;
    }

    /**
     * Output only. The MaximizeClicks Opt-In recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.MaximizeClicksOptInRecommendation maximize_clicks_opt_in_recommendation = 15 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\MaximizeClicksOptInRecommendation|null
     */
    public function getMaximizeClicksOptInRecommendation()
    {
        return $this->readOneof(15);
    }

    public function hasMaximizeClicksOptInRecommendation()
    {
        return $this->hasOneof(15);
    }

    /**
     * Output only. The MaximizeClicks Opt-In recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.MaximizeClicksOptInRecommendation maximize_clicks_opt_in_recommendation = 15 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\MaximizeClicksOptInRecommendation $var
     * @return $this
     */
    public function setMaximizeClicksOptInRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\MaximizeClicksOptInRecommendation::class);
        $this->writeOneof(15, $var);

        return $this;
    }

    /**
     * Output only. The Optimize Ad Rotation recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.OptimizeAdRotationRecommendation optimize_ad_rotation_recommendation = 16 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\OptimizeAdRotationRecommendation|null
     */
    public function getOptimizeAdRotationRecommendation()
    {
        return $this->readOneof(16);
    }

    public function hasOptimizeAdRotationRecommendation()
    {
        return $this->hasOneof(16);
    }

    /**
     * Output only. The Optimize Ad Rotation recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.OptimizeAdRotationRecommendation optimize_ad_rotation_recommendation = 16 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\OptimizeAdRotationRecommendation $var
     * @return $this
     */
    public function setOptimizeAdRotationRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\OptimizeAdRotationRecommendation::class);
        $this->writeOneof(16, $var);

        return $this;
    }

    /**
     * Output only. The keyword match type recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.KeywordMatchTypeRecommendation keyword_match_type_recommendation = 20 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\KeywordMatchTypeRecommendation|null
     */
    public function getKeywordMatchTypeRecommendation()
    {
        return $this->readOneof(20);
    }

    public function hasKeywordMatchTypeRecommendation()
    {
        return $this->hasOneof(20);
    }

    /**
     * Output only. The keyword match type recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.KeywordMatchTypeRecommendation keyword_match_type_recommendation = 20 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\KeywordMatchTypeRecommendation $var
     * @return $this
     */
    public function setKeywordMatchTypeRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\KeywordMatchTypeRecommendation::class);
        $this->writeOneof(20, $var);

        return $this;
    }

    /**
     * Output only. The move unused budget recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.MoveUnusedBudgetRecommendation move_unused_budget_recommendation = 21 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\MoveUnusedBudgetRecommendation|null
     */
    public function getMoveUnusedBudgetRecommendation()
    {
        return $this->readOneof(21);
    }

    public function hasMoveUnusedBudgetRecommendation()
    {
        return $this->hasOneof(21);
    }

    /**
     * Output only. The move unused budget recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.MoveUnusedBudgetRecommendation move_unused_budget_recommendation = 21 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\MoveUnusedBudgetRecommendation $var
     * @return $this
     */
    public function setMoveUnusedBudgetRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\MoveUnusedBudgetRecommendation::class);
        $this->writeOneof(21, $var);

        return $this;
    }

    /**
     * Output only. The Target ROAS opt-in recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.TargetRoasOptInRecommendation target_roas_opt_in_recommendation = 23 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\TargetRoasOptInRecommendation|null
     */
    public function getTargetRoasOptInRecommendation()
    {
        return $this->readOneof(23);
    }

    public function hasTargetRoasOptInRecommendation()
    {
        return $this->hasOneof(23);
    }

    /**
     * Output only. The Target ROAS opt-in recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.TargetRoasOptInRecommendation target_roas_opt_in_recommendation = 23 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\TargetRoasOptInRecommendation $var
     * @return $this
     */
    public function setTargetRoasOptInRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\TargetRoasOptInRecommendation::class);
        $this->writeOneof(23, $var);

        return $this;
    }

    /**
     * Output only. The add responsive search ad recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ResponsiveSearchAdRecommendation responsive_search_ad_recommendation = 28 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\ResponsiveSearchAdRecommendation|null
     */
    public function getResponsiveSearchAdRecommendation()
    {
        return $this->readOneof(28);
    }

    public function hasResponsiveSearchAdRecommendation()
    {
        return $this->hasOneof(28);
    }

    /**
     * Output only. The add responsive search ad recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ResponsiveSearchAdRecommendation responsive_search_ad_recommendation = 28 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\ResponsiveSearchAdRecommendation $var
     * @return $this
     */
    public function setResponsiveSearchAdRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\ResponsiveSearchAdRecommendation::class);
        $this->writeOneof(28, $var);

        return $this;
    }

    /**
     * Output only. The marginal ROI campaign budget recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.CampaignBudgetRecommendation marginal_roi_campaign_budget_recommendation = 29 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\CampaignBudgetRecommendation|null
     */
    public function getMarginalRoiCampaignBudgetRecommendation()
    {
        return $this->readOneof(29);
    }

    public function hasMarginalRoiCampaignBudgetRecommendation()
    {
        return $this->hasOneof(29);
    }

    /**
     * Output only. The marginal ROI campaign budget recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.CampaignBudgetRecommendation marginal_roi_campaign_budget_recommendation = 29 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\CampaignBudgetRecommendation $var
     * @return $this
     */
    public function setMarginalRoiCampaignBudgetRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\CampaignBudgetRecommendation::class);
        $this->writeOneof(29, $var);

        return $this;
    }

    /**
     * Output only. The use broad match keyword recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.UseBroadMatchKeywordRecommendation use_broad_match_keyword_recommendation = 30 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\UseBroadMatchKeywordRecommendation|null
     */
    public function getUseBroadMatchKeywordRecommendation()
    {
        return $this->readOneof(30);
    }

    public function hasUseBroadMatchKeywordRecommendation()
    {
        return $this->hasOneof(30);
    }

    /**
     * Output only. The use broad match keyword recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.UseBroadMatchKeywordRecommendation use_broad_match_keyword_recommendation = 30 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\UseBroadMatchKeywordRecommendation $var
     * @return $this
     */
    public function setUseBroadMatchKeywordRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\UseBroadMatchKeywordRecommendation::class);
        $this->writeOneof(30, $var);

        return $this;
    }

    /**
     * Output only. The responsive search ad asset recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ResponsiveSearchAdAssetRecommendation responsive_search_ad_asset_recommendation = 31 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\ResponsiveSearchAdAssetRecommendation|null
     */
    public function getResponsiveSearchAdAssetRecommendation()
    {
        return $this->readOneof(31);
    }

    public function hasResponsiveSearchAdAssetRecommendation()
    {
        return $this->hasOneof(31);
    }

    /**
     * Output only. The responsive search ad asset recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ResponsiveSearchAdAssetRecommendation responsive_search_ad_asset_recommendation = 31 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\ResponsiveSearchAdAssetRecommendation $var
     * @return $this
     */
    public function setResponsiveSearchAdAssetRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\ResponsiveSearchAdAssetRecommendation::class);
        $this->writeOneof(31, $var);

        return $this;
    }

    /**
     * Output only. The upgrade a Smart Shopping campaign to a Performance Max
     * campaign recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.UpgradeSmartShoppingCampaignToPerformanceMaxRecommendation upgrade_smart_shopping_campaign_to_performance_max_recommendation = 32 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\UpgradeSmartShoppingCampaignToPerformanceMaxRecommendation|null
     */
    public function getUpgradeSmartShoppingCampaignToPerformanceMaxRecommendation()
    {
        return $this->readOneof(32);
    }

    public function hasUpgradeSmartShoppingCampaignToPerformanceMaxRecommendation()
    {
        return $this->hasOneof(32);
    }

    /**
     * Output only. The upgrade a Smart Shopping campaign to a Performance Max
     * campaign recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.UpgradeSmartShoppingCampaignToPerformanceMaxRecommendation upgrade_smart_shopping_campaign_to_performance_max_recommendation = 32 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\UpgradeSmartShoppingCampaignToPerformanceMaxRecommendation $var
     * @return $this
     */
    public function setUpgradeSmartShoppingCampaignToPerformanceMaxRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\UpgradeSmartShoppingCampaignToPerformanceMaxRecommendation::class);
        $this->writeOneof(32, $var);

        return $this;
    }

    /**
     * Output only. The responsive search ad improve ad strength recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ResponsiveSearchAdImproveAdStrengthRecommendation responsive_search_ad_improve_ad_strength_recommendation = 33 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\ResponsiveSearchAdImproveAdStrengthRecommendation|null
     */
    public function getResponsiveSearchAdImproveAdStrengthRecommendation()
    {
        return $this->readOneof(33);
    }

    public function hasResponsiveSearchAdImproveAdStrengthRecommendation()
    {
        return $this->hasOneof(33);
    }

    /**
     * Output only. The responsive search ad improve ad strength recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ResponsiveSearchAdImproveAdStrengthRecommendation responsive_search_ad_improve_ad_strength_recommendation = 33 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\ResponsiveSearchAdImproveAdStrengthRecommendation $var
     * @return $this
     */
    public function setResponsiveSearchAdImproveAdStrengthRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\ResponsiveSearchAdImproveAdStrengthRecommendation::class);
        $this->writeOneof(33, $var);

        return $this;
    }

    /**
     * Output only. The Display Expansion opt-in recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.DisplayExpansionOptInRecommendation display_expansion_opt_in_recommendation = 34 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\DisplayExpansionOptInRecommendation|null
     */
    public function getDisplayExpansionOptInRecommendation()
    {
        return $this->readOneof(34);
    }

    public function hasDisplayExpansionOptInRecommendation()
    {
        return $this->hasOneof(34);
    }

    /**
     * Output only. The Display Expansion opt-in recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.DisplayExpansionOptInRecommendation display_expansion_opt_in_recommendation = 34 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\DisplayExpansionOptInRecommendation $var
     * @return $this
     */
    public function setDisplayExpansionOptInRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\DisplayExpansionOptInRecommendation::class);
        $this->writeOneof(34, $var);

        return $this;
    }

    /**
     * Output only. The upgrade a Local campaign to a Performance Max campaign
     * recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.UpgradeLocalCampaignToPerformanceMaxRecommendation upgrade_local_campaign_to_performance_max_recommendation = 35 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\UpgradeLocalCampaignToPerformanceMaxRecommendation|null
     */
    public function getUpgradeLocalCampaignToPerformanceMaxRecommendation()
    {
        return $this->readOneof(35);
    }

    public function hasUpgradeLocalCampaignToPerformanceMaxRecommendation()
    {
        return $this->hasOneof(35);
    }

    /**
     * Output only. The upgrade a Local campaign to a Performance Max campaign
     * recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.UpgradeLocalCampaignToPerformanceMaxRecommendation upgrade_local_campaign_to_performance_max_recommendation = 35 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\UpgradeLocalCampaignToPerformanceMaxRecommendation $var
     * @return $this
     */
    public function setUpgradeLocalCampaignToPerformanceMaxRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\UpgradeLocalCampaignToPerformanceMaxRecommendation::class);
        $this->writeOneof(35, $var);

        return $this;
    }

    /**
     * Output only. The raise target CPA bid too low recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.RaiseTargetCpaBidTooLowRecommendation raise_target_cpa_bid_too_low_recommendation = 36 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\RaiseTargetCpaBidTooLowRecommendation|null
     */
    public function getRaiseTargetCpaBidTooLowRecommendation()
    {
        return $this->readOneof(36);
    }

    public function hasRaiseTargetCpaBidTooLowRecommendation()
    {
        return $this->hasOneof(36);
    }

    /**
     * Output only. The raise target CPA bid too low recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.RaiseTargetCpaBidTooLowRecommendation raise_target_cpa_bid_too_low_recommendation = 36 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\RaiseTargetCpaBidTooLowRecommendation $var
     * @return $this
     */
    public function setRaiseTargetCpaBidTooLowRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\RaiseTargetCpaBidTooLowRecommendation::class);
        $this->writeOneof(36, $var);

        return $this;
    }

    /**
     * Output only. The forecasting set target ROAS recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ForecastingSetTargetRoasRecommendation forecasting_set_target_roas_recommendation = 37 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\ForecastingSetTargetRoasRecommendation|null
     */
    public function getForecastingSetTargetRoasRecommendation()
    {
        return $this->readOneof(37);
    }

    public function hasForecastingSetTargetRoasRecommendation()
    {
        return $this->hasOneof(37);
    }

    /**
     * Output only. The forecasting set target ROAS recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ForecastingSetTargetRoasRecommendation forecasting_set_target_roas_recommendation = 37 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\ForecastingSetTargetRoasRecommendation $var
     * @return $this
     */
    public function setForecastingSetTargetRoasRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\ForecastingSetTargetRoasRecommendation::class);
        $this->writeOneof(37, $var);

        return $this;
    }

    /**
     * Output only. The callout asset recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.CalloutAssetRecommendation callout_asset_recommendation = 39 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\CalloutAssetRecommendation|null
     */
    public function getCalloutAssetRecommendation()
    {
        return $this->readOneof(39);
    }

    public function hasCalloutAssetRecommendation()
    {
        return $this->hasOneof(39);
    }

    /**
     * Output only. The callout asset recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.CalloutAssetRecommendation callout_asset_recommendation = 39 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\CalloutAssetRecommendation $var
     * @return $this
     */
    public function setCalloutAssetRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\CalloutAssetRecommendation::class);
        $this->writeOneof(39, $var);

        return $this;
    }

    /**
     * Output only. The sitelink asset recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.SitelinkAssetRecommendation sitelink_asset_recommendation = 40 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\SitelinkAssetRecommendation|null
     */
    public function getSitelinkAssetRecommendation()
    {
        return $this->readOneof(40);
    }

    public function hasSitelinkAssetRecommendation()
    {
        return $this->hasOneof(40);
    }

    /**
     * Output only. The sitelink asset recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.SitelinkAssetRecommendation sitelink_asset_recommendation = 40 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\SitelinkAssetRecommendation $var
     * @return $this
     */
    public function setSitelinkAssetRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\SitelinkAssetRecommendation::class);
        $this->writeOneof(40, $var);

        return $this;
    }

    /**
     * Output only. The call asset recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.CallAssetRecommendation call_asset_recommendation = 41 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\CallAssetRecommendation|null
     */
    public function getCallAssetRecommendation()
    {
        return $this->readOneof(41);
    }

    public function hasCallAssetRecommendation()
    {
        return $this->hasOneof(41);
    }

    /**
     * Output only. The call asset recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.CallAssetRecommendation call_asset_recommendation = 41 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\CallAssetRecommendation $var
     * @return $this
     */
    public function setCallAssetRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\CallAssetRecommendation::class);
        $this->writeOneof(41, $var);

        return $this;
    }

    /**
     * Output only. The shopping add age group recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingOfferAttributeRecommendation shopping_add_age_group_recommendation = 42 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation|null
     */
    public function getShoppingAddAgeGroupRecommendation()
    {
        return $this->readOneof(42);
    }

    public function hasShoppingAddAgeGroupRecommendation()
    {
        return $this->hasOneof(42);
    }

    /**
     * Output only. The shopping add age group recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingOfferAttributeRecommendation shopping_add_age_group_recommendation = 42 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation $var
     * @return $this
     */
    public function setShoppingAddAgeGroupRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation::class);
        $this->writeOneof(42, $var);

        return $this;
    }

    /**
     * Output only. The shopping add color recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingOfferAttributeRecommendation shopping_add_color_recommendation = 43 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation|null
     */
    public function getShoppingAddColorRecommendation()
    {
        return $this->readOneof(43);
    }

    public function hasShoppingAddColorRecommendation()
    {
        return $this->hasOneof(43);
    }

    /**
     * Output only. The shopping add color recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingOfferAttributeRecommendation shopping_add_color_recommendation = 43 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation $var
     * @return $this
     */
    public function setShoppingAddColorRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation::class);
        $this->writeOneof(43, $var);

        return $this;
    }

    /**
     * Output only. The shopping add gender recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingOfferAttributeRecommendation shopping_add_gender_recommendation = 44 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation|null
     */
    public function getShoppingAddGenderRecommendation()
    {
        return $this->readOneof(44);
    }

    public function hasShoppingAddGenderRecommendation()
    {
        return $this->hasOneof(44);
    }

    /**
     * Output only. The shopping add gender recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingOfferAttributeRecommendation shopping_add_gender_recommendation = 44 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation $var
     * @return $this
     */
    public function setShoppingAddGenderRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation::class);
        $this->writeOneof(44, $var);

        return $this;
    }

    /**
     * Output only. The shopping add GTIN recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingOfferAttributeRecommendation shopping_add_gtin_recommendation = 45 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation|null
     */
    public function getShoppingAddGtinRecommendation()
    {
        return $this->readOneof(45);
    }

    public function hasShoppingAddGtinRecommendation()
    {
        return $this->hasOneof(45);
    }

    /**
     * Output only. The shopping add GTIN recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingOfferAttributeRecommendation shopping_add_gtin_recommendation = 45 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation $var
     * @return $this
     */
    public function setShoppingAddGtinRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation::class);
        $this->writeOneof(45, $var);

        return $this;
    }

    /**
     * Output only. The shopping add more identifiers recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingOfferAttributeRecommendation shopping_add_more_identifiers_recommendation = 46 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation|null
     */
    public function getShoppingAddMoreIdentifiersRecommendation()
    {
        return $this->readOneof(46);
    }

    public function hasShoppingAddMoreIdentifiersRecommendation()
    {
        return $this->hasOneof(46);
    }

    /**
     * Output only. The shopping add more identifiers recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingOfferAttributeRecommendation shopping_add_more_identifiers_recommendation = 46 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation $var
     * @return $this
     */
    public function setShoppingAddMoreIdentifiersRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation::class);
        $this->writeOneof(46, $var);

        return $this;
    }

    /**
     * Output only. The shopping add size recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingOfferAttributeRecommendation shopping_add_size_recommendation = 47 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation|null
     */
    public function getShoppingAddSizeRecommendation()
    {
        return $this->readOneof(47);
    }

    public function hasShoppingAddSizeRecommendation()
    {
        return $this->hasOneof(47);
    }

    /**
     * Output only. The shopping add size recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingOfferAttributeRecommendation shopping_add_size_recommendation = 47 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation $var
     * @return $this
     */
    public function setShoppingAddSizeRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingOfferAttributeRecommendation::class);
        $this->writeOneof(47, $var);

        return $this;
    }

    /**
     * Output only. The shopping add products to campaign recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingAddProductsToCampaignRecommendation shopping_add_products_to_campaign_recommendation = 48 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingAddProductsToCampaignRecommendation|null
     */
    public function getShoppingAddProductsToCampaignRecommendation()
    {
        return $this->readOneof(48);
    }

    public function hasShoppingAddProductsToCampaignRecommendation()
    {
        return $this->hasOneof(48);
    }

    /**
     * Output only. The shopping add products to campaign recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingAddProductsToCampaignRecommendation shopping_add_products_to_campaign_recommendation = 48 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingAddProductsToCampaignRecommendation $var
     * @return $this
     */
    public function setShoppingAddProductsToCampaignRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingAddProductsToCampaignRecommendation::class);
        $this->writeOneof(48, $var);

        return $this;
    }

    /**
     * Output only. The shopping fix disapproved products recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingFixDisapprovedProductsRecommendation shopping_fix_disapproved_products_recommendation = 49 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingFixDisapprovedProductsRecommendation|null
     */
    public function getShoppingFixDisapprovedProductsRecommendation()
    {
        return $this->readOneof(49);
    }

    public function hasShoppingFixDisapprovedProductsRecommendation()
    {
        return $this->hasOneof(49);
    }

    /**
     * Output only. The shopping fix disapproved products recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingFixDisapprovedProductsRecommendation shopping_fix_disapproved_products_recommendation = 49 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingFixDisapprovedProductsRecommendation $var
     * @return $this
     */
    public function setShoppingFixDisapprovedProductsRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingFixDisapprovedProductsRecommendation::class);
        $this->writeOneof(49, $var);

        return $this;
    }

    /**
     * Output only. The shopping target all offers recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingTargetAllOffersRecommendation shopping_target_all_offers_recommendation = 50 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingTargetAllOffersRecommendation|null
     */
    public function getShoppingTargetAllOffersRecommendation()
    {
        return $this->readOneof(50);
    }

    public function hasShoppingTargetAllOffersRecommendation()
    {
        return $this->hasOneof(50);
    }

    /**
     * Output only. The shopping target all offers recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingTargetAllOffersRecommendation shopping_target_all_offers_recommendation = 50 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingTargetAllOffersRecommendation $var
     * @return $this
     */
    public function setShoppingTargetAllOffersRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingTargetAllOffersRecommendation::class);
        $this->writeOneof(50, $var);

        return $this;
    }

    /**
     * Output only. The shopping fix suspended Merchant Center account
     * recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingMerchantCenterAccountSuspensionRecommendation shopping_fix_suspended_merchant_center_account_recommendation = 51 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingMerchantCenterAccountSuspensionRecommendation|null
     */
    public function getShoppingFixSuspendedMerchantCenterAccountRecommendation()
    {
        return $this->readOneof(51);
    }

    public function hasShoppingFixSuspendedMerchantCenterAccountRecommendation()
    {
        return $this->hasOneof(51);
    }

    /**
     * Output only. The shopping fix suspended Merchant Center account
     * recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingMerchantCenterAccountSuspensionRecommendation shopping_fix_suspended_merchant_center_account_recommendation = 51 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingMerchantCenterAccountSuspensionRecommendation $var
     * @return $this
     */
    public function setShoppingFixSuspendedMerchantCenterAccountRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingMerchantCenterAccountSuspensionRecommendation::class);
        $this->writeOneof(51, $var);

        return $this;
    }

    /**
     * Output only. The shopping fix Merchant Center account suspension warning
     * recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingMerchantCenterAccountSuspensionRecommendation shopping_fix_merchant_center_account_suspension_warning_recommendation = 52 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingMerchantCenterAccountSuspensionRecommendation|null
     */
    public function getShoppingFixMerchantCenterAccountSuspensionWarningRecommendation()
    {
        return $this->readOneof(52);
    }

    public function hasShoppingFixMerchantCenterAccountSuspensionWarningRecommendation()
    {
        return $this->hasOneof(52);
    }

    /**
     * Output only. The shopping fix Merchant Center account suspension warning
     * recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingMerchantCenterAccountSuspensionRecommendation shopping_fix_merchant_center_account_suspension_warning_recommendation = 52 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingMerchantCenterAccountSuspensionRecommendation $var
     * @return $this
     */
    public function setShoppingFixMerchantCenterAccountSuspensionWarningRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingMerchantCenterAccountSuspensionRecommendation::class);
        $this->writeOneof(52, $var);

        return $this;
    }

    /**
     * Output only. The shopping migrate Regular Shopping Campaign offers to
     * Performance Max recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingMigrateRegularShoppingCampaignOffersToPerformanceMaxRecommendation shopping_migrate_regular_shopping_campaign_offers_to_performance_max_recommendation = 53 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingMigrateRegularShoppingCampaignOffersToPerformanceMaxRecommendation|null
     */
    public function getShoppingMigrateRegularShoppingCampaignOffersToPerformanceMaxRecommendation()
    {
        return $this->readOneof(53);
    }

    public function hasShoppingMigrateRegularShoppingCampaignOffersToPerformanceMaxRecommendation()
    {
        return $this->hasOneof(53);
    }

    /**
     * Output only. The shopping migrate Regular Shopping Campaign offers to
     * Performance Max recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ShoppingMigrateRegularShoppingCampaignOffersToPerformanceMaxRecommendation shopping_migrate_regular_shopping_campaign_offers_to_performance_max_recommendation = 53 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingMigrateRegularShoppingCampaignOffersToPerformanceMaxRecommendation $var
     * @return $this
     */
    public function setShoppingMigrateRegularShoppingCampaignOffersToPerformanceMaxRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\ShoppingMigrateRegularShoppingCampaignOffersToPerformanceMaxRecommendation::class);
        $this->writeOneof(53, $var);

        return $this;
    }

    /**
     * Output only. Recommendation to enable dynamic image extensions on the
     * account, allowing Google to find the best images from ad landing pages
     * and complement text ads.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.DynamicImageExtensionOptInRecommendation dynamic_image_extension_opt_in_recommendation = 54 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\DynamicImageExtensionOptInRecommendation|null
     */
    public function getDynamicImageExtensionOptInRecommendation()
    {
        return $this->readOneof(54);
    }

    public function hasDynamicImageExtensionOptInRecommendation()
    {
        return $this->hasOneof(54);
    }

    /**
     * Output only. Recommendation to enable dynamic image extensions on the
     * account, allowing Google to find the best images from ad landing pages
     * and complement text ads.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.DynamicImageExtensionOptInRecommendation dynamic_image_extension_opt_in_recommendation = 54 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\DynamicImageExtensionOptInRecommendation $var
     * @return $this
     */
    public function setDynamicImageExtensionOptInRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\DynamicImageExtensionOptInRecommendation::class);
        $this->writeOneof(54, $var);

        return $this;
    }

    /**
     * Output only. Recommendation to raise Target CPA.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.RaiseTargetCpaRecommendation raise_target_cpa_recommendation = 55 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\RaiseTargetCpaRecommendation|null
     */
    public function getRaiseTargetCpaRecommendation()
    {
        return $this->readOneof(55);
    }

    public function hasRaiseTargetCpaRecommendation()
    {
        return $this->hasOneof(55);
    }

    /**
     * Output only. Recommendation to raise Target CPA.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.RaiseTargetCpaRecommendation raise_target_cpa_recommendation = 55 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\RaiseTargetCpaRecommendation $var
     * @return $this
     */
    public function setRaiseTargetCpaRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\RaiseTargetCpaRecommendation::class);
        $this->writeOneof(55, $var);

        return $this;
    }

    /**
     * Output only. Recommendation to lower Target ROAS.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.LowerTargetRoasRecommendation lower_target_roas_recommendation = 56 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\LowerTargetRoasRecommendation|null
     */
    public function getLowerTargetRoasRecommendation()
    {
        return $this->readOneof(56);
    }

    public function hasLowerTargetRoasRecommendation()
    {
        return $this->hasOneof(56);
    }

    /**
     * Output only. Recommendation to lower Target ROAS.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.LowerTargetRoasRecommendation lower_target_roas_recommendation = 56 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\LowerTargetRoasRecommendation $var
     * @return $this
     */
    public function setLowerTargetRoasRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\LowerTargetRoasRecommendation::class);
        $this->writeOneof(56, $var);

        return $this;
    }

    /**
     * Output only. The Performance Max Opt In recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.PerformanceMaxOptInRecommendation performance_max_opt_in_recommendation = 57 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\PerformanceMaxOptInRecommendation|null
     */
    public function getPerformanceMaxOptInRecommendation()
    {
        return $this->readOneof(57);
    }

    public function hasPerformanceMaxOptInRecommendation()
    {
        return $this->hasOneof(57);
    }

    /**
     * Output only. The Performance Max Opt In recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.PerformanceMaxOptInRecommendation performance_max_opt_in_recommendation = 57 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\PerformanceMaxOptInRecommendation $var
     * @return $this
     */
    public function setPerformanceMaxOptInRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\PerformanceMaxOptInRecommendation::class);
        $this->writeOneof(57, $var);

        return $this;
    }

    /**
     * Output only. The improve Performance Max ad strength recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ImprovePerformanceMaxAdStrengthRecommendation improve_performance_max_ad_strength_recommendation = 58 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\ImprovePerformanceMaxAdStrengthRecommendation|null
     */
    public function getImprovePerformanceMaxAdStrengthRecommendation()
    {
        return $this->readOneof(58);
    }

    public function hasImprovePerformanceMaxAdStrengthRecommendation()
    {
        return $this->hasOneof(58);
    }

    /**
     * Output only. The improve Performance Max ad strength recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ImprovePerformanceMaxAdStrengthRecommendation improve_performance_max_ad_strength_recommendation = 58 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\ImprovePerformanceMaxAdStrengthRecommendation $var
     * @return $this
     */
    public function setImprovePerformanceMaxAdStrengthRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\ImprovePerformanceMaxAdStrengthRecommendation::class);
        $this->writeOneof(58, $var);

        return $this;
    }

    /**
     * Output only. The Dynamic Search Ads to Performance Max migration
     * recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.MigrateDynamicSearchAdsCampaignToPerformanceMaxRecommendation migrate_dynamic_search_ads_campaign_to_performance_max_recommendation = 59 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\MigrateDynamicSearchAdsCampaignToPerformanceMaxRecommendation|null
     */
    public function getMigrateDynamicSearchAdsCampaignToPerformanceMaxRecommendation()
    {
        return $this->readOneof(59);
    }

    public function hasMigrateDynamicSearchAdsCampaignToPerformanceMaxRecommendation()
    {
        return $this->hasOneof(59);
    }

    /**
     * Output only. The Dynamic Search Ads to Performance Max migration
     * recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.MigrateDynamicSearchAdsCampaignToPerformanceMaxRecommendation migrate_dynamic_search_ads_campaign_to_performance_max_recommendation = 59 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\MigrateDynamicSearchAdsCampaignToPerformanceMaxRecommendation $var
     * @return $this
     */
    public function setMigrateDynamicSearchAdsCampaignToPerformanceMaxRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\MigrateDynamicSearchAdsCampaignToPerformanceMaxRecommendation::class);
        $this->writeOneof(59, $var);

        return $this;
    }

    /**
     * Output only. The forecasting set target CPA recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ForecastingSetTargetCpaRecommendation forecasting_set_target_cpa_recommendation = 60 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\ForecastingSetTargetCpaRecommendation|null
     */
    public function getForecastingSetTargetCpaRecommendation()
    {
        return $this->readOneof(60);
    }

    public function hasForecastingSetTargetCpaRecommendation()
    {
        return $this->hasOneof(60);
    }

    /**
     * Output only. The forecasting set target CPA recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ForecastingSetTargetCpaRecommendation forecasting_set_target_cpa_recommendation = 60 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\ForecastingSetTargetCpaRecommendation $var
     * @return $this
     */
    public function setForecastingSetTargetCpaRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\ForecastingSetTargetCpaRecommendation::class);
        $this->writeOneof(60, $var);

        return $this;
    }

    /**
     * Output only. The set target CPA recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ForecastingSetTargetCpaRecommendation set_target_cpa_recommendation = 61 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\ForecastingSetTargetCpaRecommendation|null
     */
    public function getSetTargetCpaRecommendation()
    {
        return $this->readOneof(61);
    }

    public function hasSetTargetCpaRecommendation()
    {
        return $this->hasOneof(61);
    }

    /**
     * Output only. The set target CPA recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ForecastingSetTargetCpaRecommendation set_target_cpa_recommendation = 61 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\ForecastingSetTargetCpaRecommendation $var
     * @return $this
     */
    public function setSetTargetCpaRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\ForecastingSetTargetCpaRecommendation::class);
        $this->writeOneof(61, $var);

        return $this;
    }

    /**
     * Output only. The set target ROAS recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ForecastingSetTargetRoasRecommendation set_target_roas_recommendation = 62 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @return \Google\Ads\GoogleAds\V15\Resources\Recommendation\ForecastingSetTargetRoasRecommendation|null
     */
    public function getSetTargetRoasRecommendation()
    {
        return $this->readOneof(62);
    }

    public function hasSetTargetRoasRecommendation()
    {
        return $this->hasOneof(62);
    }

    /**
     * Output only. The set target ROAS recommendation.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v15.resources.Recommendation.ForecastingSetTargetRoasRecommendation set_target_roas_recommendation = 62 [(.google.api.field_behavior) = OUTPUT_ONLY];</code>
     * @param \Google\Ads\GoogleAds\V15\Resources\Recommendation\ForecastingSetTargetRoasRecommendation $var
     * @return $this
     */
    public function setSetTargetRoasRecommendation($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V15\Resources\Recommendation\ForecastingSetTargetRoasRecommendation::class);
        $this->writeOneof(62, $var);

        return $this;
    }

    /**
     * @return string
     */
    public function getRecommendation()
    {
        return $this->whichOneof("recommendation");
    }

}

